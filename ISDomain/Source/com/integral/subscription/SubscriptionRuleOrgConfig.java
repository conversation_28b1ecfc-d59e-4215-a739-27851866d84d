package com.integral.subscription;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.integral.admin.utils.StringUtils;
import com.integral.user.Organization;

public class SubscriptionRuleOrgConfig {

	private List<Organization> filteredOrgs;

	private List<String> preferredProviders;	

	public SubscriptionRuleOrgConfig(List<Organization> preferredProviders,
			List<Organization> filteredOrgs){		
		String csvPreferredProviders = StringUtils.getCSV(preferredProviders);
		this.preferredProviders = new ArrayList<String>(Arrays.asList(csvPreferredProviders.split(",")));	
		
		this.filteredOrgs = new ArrayList<Organization>(filteredOrgs);		
	}

	public List<Organization> getFilteredOrgs() {
		return filteredOrgs;
	}

	public void setFilteredOrgs(List<Organization> filteredOrgs) {
		this.filteredOrgs =  new ArrayList<Organization>(filteredOrgs);
	}
	
	public List<String> getPreferredProviders(){
		return this.preferredProviders;
	}

	public void setPreferredProviders(List<Organization> preferredProviders){
		String csvPreferredProviders = StringUtils.getCSV(preferredProviders);
		this.preferredProviders = new ArrayList<String>(Arrays.asList(csvPreferredProviders.split(",")));
	}

}
