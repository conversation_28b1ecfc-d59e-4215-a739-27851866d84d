package com.integral.subscription;

import com.integral.finance.counterparty.TradingParty;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.provider.ProviderOrgFunction;
import com.integral.subscription.rules.SubscriptionRuleAttributeMBeanc;
import com.integral.util.CompositeKeys;
import com.integral.user.Organization;
import com.integral.admin.utils.StringUtils;
import com.integral.finance.counterparty.LegalEntity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;


/**
 * Class that caches the filtered list of orgs after applying subscription rules
 * for a fi-le. 
 * 
 * 
 */
public class SubscriptionRuleFilteredOrgCache {

	private static Log log = LogFactory
			.getLog(SubscriptionRuleFilteredOrgCache.class);

	private static SubscriptionRuleFilteredOrgCache instance = new SubscriptionRuleFilteredOrgCache();


	// Key: FI org short name
	// Value : Hashmap : containing the OrgConfig object grouped by FI-LE + ccy + i/p prov list
	// 			key: customer le name + currency pair + Hashcode of the
	// 				 previous preferred org list for the customer LE
	// 			value : Composite object consisting of the filtered orgs and short names of the preferred orgs
	private ConcurrentHashMap<String, ConcurrentHashMap<CompositeKeys, SubscriptionRuleOrgConfig>> fiToFilteredOrgsMap;

	private SubscriptionRuleFilteredOrgCache() {
		fiToFilteredOrgsMap = new ConcurrentHashMap<String, ConcurrentHashMap<CompositeKeys, SubscriptionRuleOrgConfig>>();	
	}

	public static SubscriptionRuleFilteredOrgCache getInstance() {
		return instance;
	}
	
	/**
	 * Checks if there is a entry in the cache for the given input set
	 * @param customerLE LE of the customer org
	 * @param ccyPair currency pair
	 * @param preferredProviders List of the preferred org list
	 * @return filtered list of orgs if cache hit; null otherwise
	 */
	public List<Organization> checkAndGetFilteredOrgsForCustomer(
			LegalEntity customerLE, String ccyPair, List<Organization> preferredProviders) {
		
		if (!SubscriptionRuleAttributeMBeanc.getInstance()
				.isSubscriptionRulesCacheEnabled()) {
			return null;
		}
		
		String fiOrg = customerLE.getOrganization().getShortName();

		if (fiToFilteredOrgsMap.containsKey(fiOrg)) {
			ConcurrentHashMap<CompositeKeys, SubscriptionRuleOrgConfig> fiLEToOrgConfigMap = fiToFilteredOrgsMap
					.get(fiOrg);

			String fiLE = customerLE.getShortName();			
			String hashCode = String.valueOf(generateHashCode(preferredProviders));
			CompositeKeys fiLEToOrgConfigMapKey = CompositeKeys.getCompositeKeys(fiLE,
					ccyPair, hashCode);

			if (fiLEToOrgConfigMap.containsKey(fiLEToOrgConfigMapKey)) {
				SubscriptionRuleOrgConfig orgConfig = fiLEToOrgConfigMap
						.get(fiLEToOrgConfigMapKey);
				List<Organization> filteredOrgs = new ArrayList<Organization>(
						orgConfig.getFilteredOrgs());
				return filteredOrgs;
			}
			return null;
		}
		return null;
	}
	
	public void clearSubscriptionRuleFilteredOrgCache(){
		if(log.isDebugEnabled()){
			logMessage("Clearing SubscriptionRuleFilteredOrgCache");	
		}
		fiToFilteredOrgsMap.clear();
	}
	
	public void invalidateCacheForFI(String fiOrgShortName, String logMessage) {
		if (log.isDebugEnabled()) {
			logMessage("Invalidating SubscriptionRuleFilteredOrgCache for FI:",
					fiOrgShortName, ":Reason:", logMessage);
		}
		fiToFilteredOrgsMap.remove(fiOrgShortName);
	}

	public void updateFIToFilteredOrgsCache(LegalEntity customerLE,
			String ccyPair, List<Organization> preferredProviders,
			List<Organization> filteredOrgs) {
		String fiOrg = customerLE.getOrganization().getShortName();
		ConcurrentHashMap<CompositeKeys, SubscriptionRuleOrgConfig> fiLEToOrgConfigMap = fiToFilteredOrgsMap.get(fiOrg);
		
		if(fiLEToOrgConfigMap == null){
			fiLEToOrgConfigMap = new ConcurrentHashMap<CompositeKeys, SubscriptionRuleOrgConfig>();
		}		
		
		String fiLE = customerLE.getShortName();		
		String hashCode = String.valueOf(generateHashCode(preferredProviders));
		CompositeKeys key = CompositeKeys.getCompositeKeys(fiLE, ccyPair, hashCode);
		
		SubscriptionRuleOrgConfig orgConfig = fiLEToOrgConfigMap.get(key);
		if(orgConfig == null){
			orgConfig = new SubscriptionRuleOrgConfig(preferredProviders, filteredOrgs);
		}
		orgConfig.setFilteredOrgs(filteredOrgs);
		orgConfig.setPreferredProviders(preferredProviders);		
		
		// update the map with the new orgConfig
		fiLEToOrgConfigMap.put(key, orgConfig);
		
		if(log.isDebugEnabled()){
			logMessage("Updating subscription rule filtered org cache:", fiLEToOrgConfigMap);
		}		
		
		// update the fiToFilteredOrgsMap with the new map		
		fiToFilteredOrgsMap.put(fiOrg, fiLEToOrgConfigMap);		
	}
	
	private void logMessage(
			String mesg,
			ConcurrentHashMap<CompositeKeys, SubscriptionRuleOrgConfig> fiLEToOrgConfigMap) {
		StringBuilder debugMessage = new StringBuilder(mesg);
		debugMessage.append("\n");
		for (Entry<CompositeKeys, SubscriptionRuleOrgConfig> itr : fiLEToOrgConfigMap
				.entrySet()) {
			debugMessage.append("Key:");
			debugMessage.append(itr.getKey().toString());
			debugMessage.append("\n");
			debugMessage.append("Filtered Orgs:");
			SubscriptionRuleOrgConfig orgConfig = itr.getValue();
			Iterator<Organization> filteredOrgItr = orgConfig.getFilteredOrgs().iterator();
			while(filteredOrgItr.hasNext()){
				debugMessage.append(filteredOrgItr.next().getShortName()).append(":");
			}
			debugMessage.append("\n");
			debugMessage.append("Preferred Providers:");
			Iterator<String> orgItr = orgConfig.getPreferredProviders().iterator();
			while(orgItr.hasNext()){
				debugMessage.append(orgItr.next()).append(":");
			}
			debugMessage.append("\n");
		}
		log.debug(debugMessage.toString());
		
	}

	private int generateHashCode(List<Organization> orgs) {
        if(orgs == null){
            return 0;
        }
        String csvPreferredProviders = StringUtils.getCSV(orgs);        
        String[] sortedNames = csvPreferredProviders.split(",");
        Arrays.sort(sortedNames);
		int orgsHashCode = Arrays.hashCode(sortedNames);
		return orgsHashCode;
	}

    public void handleUpdateOfOrganization(Organization organization){
        //TODO make it more granular
        List<String> invalidateEntries = new LinkedList<String>( );
        Set<Map.Entry<String,ConcurrentHashMap<CompositeKeys,SubscriptionRuleOrgConfig>>> entries = fiToFilteredOrgsMap.entrySet();
        for(Map.Entry<String,ConcurrentHashMap<CompositeKeys,SubscriptionRuleOrgConfig>> eachEnty : entries){
            Collection<SubscriptionRuleOrgConfig> values = eachEnty.getValue().values();
            for(SubscriptionRuleOrgConfig subscriptionRuleOrgConfig : values){
                if(subscriptionRuleOrgConfig.getPreferredProviders().contains( organization.getShortName() )){
                    invalidateEntries.add( eachEnty.getKey() );
                    break;
                }
            }
        }
        for(String invalidEntry : invalidateEntries){
        	invalidateCacheForFI( invalidEntry, "Change in Org info of:" + organization.getShortName() );
        }
    }   
    
	public void handleUpdateOfExecutionProviders(Organization customerOrg) {
		if (fiToFilteredOrgsMap.containsKey(customerOrg.getShortName())) {
			invalidateCacheForFI(
					customerOrg.getShortName(),
					"Change in Liquidity Rule for FI:"
							+ customerOrg.getShortName());
		}
	}

	public void handleUpdateOfPrimeBroker(TradingParty tradingParty) {		
		if (fiToFilteredOrgsMap.containsKey(tradingParty.getOrganization()
				.getShortName())) {
			invalidateCacheForFI(tradingParty.getOrganization().getShortName(),
					"Change in Trading Party Organization:"
							+ tradingParty.getOrganization().getShortName());
		}
	}

    public void handleUpdateOfSubscriptionRule( ProviderOrgFunction providerOrgFunction )
    {
        //TODO make it more granular
        List<String> invalidateEntries = new LinkedList(  );
        Set<Map.Entry<String,ConcurrentHashMap<CompositeKeys,SubscriptionRuleOrgConfig>>> entries = fiToFilteredOrgsMap.entrySet();
        for(Map.Entry<String,ConcurrentHashMap<CompositeKeys,SubscriptionRuleOrgConfig>> eachEnty : entries){
            Collection<SubscriptionRuleOrgConfig> values = eachEnty.getValue().values();
            for(SubscriptionRuleOrgConfig subscriptionRuleOrgConfig : values){
                if(subscriptionRuleOrgConfig.getPreferredProviders().contains( providerOrgFunction.getOrganization().getShortName() )){
                    invalidateEntries.add( eachEnty.getKey() );
                    break;
                }
            }
        }
		for (String invalidEntry : invalidateEntries) {
			invalidateCacheForFI(invalidEntry,
					"Change in Provider Org function of:"
							+ providerOrgFunction.getOrganization()
									.getShortName());
		}
    }
    
	private void logMessage(String... strings) {
		StringBuilder debugMessage = new StringBuilder(100);
		for (int i = 0; i < strings.length; i++) {
			debugMessage.append(strings[i]);
		}
		log.debug(debugMessage.toString());
	}
}
