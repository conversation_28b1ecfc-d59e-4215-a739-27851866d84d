package com.integral.subscription;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.integral.broker.model.Stream;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.dealing.liquidityProvision.LiquidityProvision;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.primebroker.PrimeBrokerPathUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.subscription.rules.SubscriptionRuleAttributeMBeanc;
import com.integral.subscription.rules.SubscriptionRules;
import com.integral.user.Organization;
import com.integral.util.CompositeKeys;
import com.integral.finance.currency.CurrencyPair;


public class SubscriptionRuleEngine {

	private static Log log = LogFactory.getLog(SubscriptionRuleEngine.class);

	private static final int REAL_LP_INDEX = -1;

	public static final String KEY_SEPARATOR = "_";

	private static final String NO_PB = "NO_PB";

	private static final Comparator<Organization> orgAlphabaeticalComparator;

	private static final String INACTIVE_STREAM = "SRE_INACTIVE_STREAM";

	static {
		orgAlphabaeticalComparator = new Comparator<Organization>() {
			@Override
			public int compare(Organization org1, Organization org2) {
				return org1.getShortName().compareTo(org2.getShortName());
			}
		};
	}

	/**
	 * Returns the user preferred execution list for the given FI org and currency pair
	 * @param placedByOrg FI Org	
	 * @param ccyPair currency pair	
	 * @return user preferred execution list of organizations
	 */
	private static List<Organization> getPriorityList(
			Organization placedByOrg, CurrencyPair ccyPair) throws Exception{
		LiquidityProvision liquidityRule = ISUtilImpl.getLiquidityProvision(
				placedByOrg, ccyPair);		
		if (liquidityRule != null) {			
			List<Organization> executionProviderList = new ArrayList<Organization>(liquidityRule.getExecutionProviders());			
			return executionProviderList;
		} else {
			StringBuilder sb = new StringBuilder(200);
			sb.append("Liquidity rules is null for Organization:").append(placedByOrg.getShortName());
			sb.append(":for currency pair:").append(ccyPair);
			log.error(sb.toString());
			throw new Exception(sb.toString());
		}		
	}

	/**
	 * Retrieve the subscription rule set on the real LP and filter masked lps
	 * based on subscription rules
	 * 
	 * @param customerLegalEntity
	 *            Legal entity of the customer organization
	 * @param orgs
	 *            List of orgs to be filtered based on subscription rules
	 */
	public static List<Organization> filterBasedOnSubscriptionRules(
			LegalEntity customerLegalEntity, CurrencyPair ccyPair, List<Organization> orgs){
		if (orgs == null)
			return null;
		if(orgs.size() == 0)
			return orgs;
		// if subscription rules is not enabled, do not filter.
		if (!SubscriptionRuleAttributeMBeanc.getInstance()
				.isSubscriptionRulesEnabled()) {
			return orgs;
		}
        long startTime = System.nanoTime();
		Organization placedByOrg = customerLegalEntity.getOrganization();
		
		if (log.isDebugEnabled()) {
			logMessage(
					orgs,
					"filterBasedOnSubscriptionRules:orgs list for customer org:",
					placedByOrg.getShortName(),
					":Before applying subscription rules:");
		}
		List<Organization> filteredOrgs;
		try {

			// get the priority list for the current FI;
			List<Organization> priorityLPList = getPriorityList(placedByOrg,
					ccyPair);

			// Check if this request can be fulfilled from the cache
			filteredOrgs = SubscriptionRuleFilteredOrgCache.getInstance()
					.checkAndGetFilteredOrgsForCustomer(customerLegalEntity, ccyPair.getName(), orgs); 
			if (filteredOrgs != null) {
				// cache hit; return from the cache				
				if (log.isDebugEnabled()) {
					logMessage(
							filteredOrgs,
							"filterBasedOnSubscriptionRules:cache hit:returning from cache for FI:",
							placedByOrg.getShortName(), ":filtered orgs:");
				}
				long endTime = System.nanoTime();
				if (log.isDebugEnabled()) {
					logMessage(
							"filterBasedOnSubscriptionRules:Time taken to filter:",
							Long.toString((endTime - startTime)));
				}
				return filteredOrgs;
			}
			filteredOrgs = new ArrayList<Organization>(
						orgs.size());

			// key:<real_lp_shortname>_<all pbs in this path>
			// value:{List of LPs related via this PB}
			HashMap<CompositeKeys, ArrayList<Organization>> primeBrokerToLPMap = new HashMap<CompositeKeys, ArrayList<Organization>>(
					orgs.size());

			// key : <real lp/stream short name>
			// value : look up the priorityLPList for the organization and get
			// its array index
			HashMap<CompositeKeys, Integer> realLPToChosenLP = new HashMap<CompositeKeys, Integer>(
					orgs.size());
			Iterator<Organization> itr = orgs.iterator();
			while (itr.hasNext()) {
				Organization currentOrg = itr.next();
				// check if this org is the customer org, if yes, add it to the
				// filtered list
				if (currentOrg == placedByOrg) {
					filteredOrgs.add(currentOrg);
					continue;
				}

                if(ISUtilImpl.getInstance().isVenueProvider( currentOrg )){
                    filteredOrgs.add( currentOrg );
                    continue;
                }
				Organization realLpForOrg = currentOrg.getRealLP();
				// realLpForOrg for the real lp will be null for both prime
				// broker and real lp
				if (realLpForOrg == null) {
					realLpForOrg = currentOrg;
				}
				if (realLpForOrg != null) {
					SubscriptionRules rule = realLpForOrg
							.getProviderOrgFunction().getSubscriptionRule();

					switch (rule) {
					case SINGLE_STREAM:
						filterBasedOnStreamPriority(customerLegalEntity,
								currentOrg, realLpForOrg, realLPToChosenLP,
								priorityLPList, filteredOrgs, true);
						break;
					case MULTIPLE_STREAM_MULTIPLE_PATH:
						filterBasedOnStreamPriority(customerLegalEntity,
								currentOrg, realLpForOrg, realLPToChosenLP,
								priorityLPList, filteredOrgs, false);
						break;
					case MULTIPLE_STREAM_SINGLE_PATH:

						String primeBroker = getPrimeBrokerChain(
								customerLegalEntity, currentOrg);

						// update the prime broker map
						CompositeKeys primeBrokerToLPMapKey;

						// if no pbs, add to the primeBrokerToLPMap with key
						// :<real_lp>_<NO_PB>
						if (primeBroker == null) {
							primeBrokerToLPMapKey = CompositeKeys.getCompositeKeys(
									realLpForOrg.getShortName(), NO_PB);
						} else {
							primeBrokerToLPMapKey = CompositeKeys.getCompositeKeys(
									realLpForOrg.getShortName(), primeBroker);
						}
						ArrayList<Organization> lpList = primeBrokerToLPMap
								.get(primeBrokerToLPMapKey);
						if (lpList == null) {
							// this is the first lp to be added to this prime
							// broker
							lpList = new ArrayList<Organization>();
						}
						lpList.add(currentOrg);
						primeBrokerToLPMap.put(primeBrokerToLPMapKey, lpList);
						break;
					case NONE:
						if (log.isDebugEnabled()) {
							logMessage("NONE RULE:Adding ", currentOrg.getShortName());							
						}
						filteredOrgs.add(currentOrg);
						break;
						
					}
				}
			}
			List<Organization> singlePathFilteredOrgs = processPrimeBrokerToLPMap(primeBrokerToLPMap,
					customerLegalEntity, priorityLPList);
			if(!singlePathFilteredOrgs.isEmpty()){
				filteredOrgs.addAll(singlePathFilteredOrgs);
			}
            // update the customer to Filtered org map
            SubscriptionRuleFilteredOrgCache.getInstance()
					.updateFIToFilteredOrgsCache(customerLegalEntity,
							ccyPair.getName(), orgs, filteredOrgs);

		} catch (Exception e) {
			log.error("Exception: ", e);
			filteredOrgs = (ArrayList<Organization>) orgs;
		}
		long endTime = System.nanoTime();
		logMessage("filterBasedOnSubscriptionRules:Time taken to filter:",
					Long.toString((endTime - startTime)));
		logMessage(
				filteredOrgs,
				"filterBasedOnSubscriptionRules:orgs list for customer org:",
				placedByOrg.getShortName(),
				":After applying subscription rules:");

		return filteredOrgs;
	}

	/**
	 * Updates the filteredOrgs by applying the stream priority rule on the
	 * current selection of LPs.
	 * 
	 * @param customerLegalEntity
	 *            Legal entity of the customer organization
	 * @param currentOrg
	 *            organization to be included or excluded based on the stream
	 *            priority rule
	 * @param realLpForOrg
	 *            real lp of the currentOrg
	 * @param realLPToChosenLP
	 *            Map containing the current selection of LPs/Streams
	 * @param priorityLPList
	 *            Priority list of LPs set by the customer org
	 * @param filteredOrgs
	 *            filtered list of organizations
	 * @param isSingleStream
	 *            true for single stream rule; false for multi stream rule
	 */
	private static void filterBasedOnStreamPriority(
			LegalEntity customerLegalEntity, Organization currentOrg,
			Organization realLpForOrg,
			HashMap<CompositeKeys, Integer> realLPToChosenLP,
			List<Organization> priorityLPList,
			List<Organization> filteredOrgs, boolean isSingleStream) {
		CompositeKeys realLPToChosenLPKey;
		
		String streamId = getStreamForCustomerOrgByProvider(customerLegalEntity,
				currentOrg);
		
		if(streamId.equals(INACTIVE_STREAM)){
			// do not process this current org since its
			// giving an inactive stream.D o not add to the filtered orgs nor use 
			// it for determining stream priority
			return;
		}
		// if its a single stream, we use <real_lp_shortname> as the key
		// if its multiple streams, we use <real_lp_shortname>_<stream_id> as
		// the key
		if (isSingleStream) {
			realLPToChosenLPKey = CompositeKeys.getCompositeKeys(realLpForOrg.getShortName());
		} else {
			realLPToChosenLPKey = CompositeKeys.getCompositeKeys(realLpForOrg.getShortName(), streamId);
		}
		if (realLPToChosenLP.containsKey(realLPToChosenLPKey)) {
			// If present, compare the current org index in the priorityLPList
			// with the existing index
			// and replace if current org is of higher priority

			// determine chosenLPIndex and chosenOrg
			int chosenLPIndex = realLPToChosenLP.get(realLPToChosenLPKey);
			Organization chosenOrg;
			if (chosenLPIndex == REAL_LP_INDEX) {
				chosenOrg = realLpForOrg;
			} else {
				chosenOrg = priorityLPList.get(chosenLPIndex);
			}
			// determine currentLPIndex and currentOrg
			int currentLPIndex;
			if (currentOrg.getShortName().equals(realLpForOrg.getShortName())) {
				// current lp is the real lp;make its index -1
				currentLPIndex = REAL_LP_INDEX;
			} else {
				currentLPIndex = priorityLPList.indexOf(currentOrg);
			}

			// determine if pb set b/w customer org and chosen org
			boolean isPBSetWithChosenOrg = getPrimeBrokerChain(customerLegalEntity,
					chosenOrg) != null ? true : false;

			// determine if pb set b/w customer org and current org
			boolean isPBSetWithCurrentOrg = getPrimeBrokerChain(customerLegalEntity,
					currentOrg) != null ? true : false;

			boolean replaceWithCurrentOrg = false;

			// compare if both are in the same state; else winner is one who
			// does not have a pb
			if (isPBSetWithChosenOrg == isPBSetWithCurrentOrg) {
				// both chosen lp and current lp have a pb or do not have a pb;
				// compare their priorities in this case
				if (currentLPIndex < chosenLPIndex) {
					// current lp has higher priority than the chosen lp;hence
					// swap
					replaceWithCurrentOrg = true;
				}
			} else if (!isPBSetWithCurrentOrg) {
				// there is no pb with current org;chosen lp has a pb,
				// hence swap it with current org.
				replaceWithCurrentOrg = true;
			}

			if (replaceWithCurrentOrg) {
				// remove from the filteredOrgs list and add the new
				// lp
				filteredOrgs.remove(chosenOrg);
				filteredOrgs.add(currentOrg);

				// update the map with the new priority
				realLPToChosenLP.put(realLPToChosenLPKey, currentLPIndex);

				if (log.isDebugEnabled()) {
					if (isSingleStream) {

						logMessage("SINGLE_STREAM RULE:replacing ",
								chosenOrg.getShortName(), " with ",
								currentOrg.getShortName());
					} else {
						logMessage(
								"MULTIPLE_STREAM_MULTIPLE_PATH RULE:Provider for stream:",
								streamId, ":replacing ",
								chosenOrg.getShortName(), " with ",
								currentOrg.getShortName());
					}
				}
			}
		} else {
			// no entry for this LP/stream yet; create an entry in the map
			// and add it to the filteredOrgs list
			int chosenLPIndex;
			if (currentOrg.getShortName().equals(realLpForOrg.getShortName())) {
				// current lp is the real lp, add index -1
				chosenLPIndex = REAL_LP_INDEX;
			} else {
				// look up the priority list to figure out the
				// current org's index
				chosenLPIndex = priorityLPList.indexOf(currentOrg);
			}
			realLPToChosenLP.put(realLPToChosenLPKey, chosenLPIndex);
			filteredOrgs.add(currentOrg);
			if (log.isDebugEnabled()) {
				if (isSingleStream) {
					logMessage("SINGLE_STREAM RULE:No chosen LP for real LP:",
							realLPToChosenLPKey.toString(), " yet; adding: ",
							currentOrg.getShortName(), " with index value of:",
							String.valueOf(chosenLPIndex));

				} else {
					logMessage(
							"MULTIPLE_STREAM_MULTIPLE_PATH RULE:No chosen LP for stream:",
							streamId, " yet; adding: ",
							currentOrg.getShortName(), " with index value of:",
							String.valueOf(chosenLPIndex));
				}
			}
		}
	}

	/**
	 * Iterate over the multiple paths for each real lp and chose one of the
	 * path
	 * 
	 * @param primeBrokerToLPMap
	 *            :key:<real_lp>_<list of pbs in the path>, value:<list of
	 *            indirect lps in the path>
	 * @param placedByOrgLE
	 *            : Legal entity of the customer org
	 * @param priorityLPList
	 *            : <priority list of LPs at the customer org>
	 * @return filtered list of lps after applying the
	 *         MULITPLE_STREAM_SINGLE_PATH rule
	 */
	private static ArrayList<Organization> processPrimeBrokerToLPMap(
			HashMap<CompositeKeys, ArrayList<Organization>> primeBrokerToLPMap,
			LegalEntity placedByOrgLE,
			final List<Organization> priorityLPList) {
		ArrayList<Organization> filteredOrgs = new ArrayList<Organization>();
		if (primeBrokerToLPMap.isEmpty())
			return filteredOrgs;		
		if (log.isDebugEnabled()) {
			logMessage(
					"processPrimeBrokerToLPMap:PB path :<List of pbs via this pb path>:",
					primeBrokerToLPMap);
		}

		// Phase 1:Choose the path; prepare the chosen lps from the chosen path;
		// prepare the exclusion list
		// Phase 2:Choose distinct streams from the selected path; For each
		// excluded LP, check if "OVERRIDE" is set;
		// if set, include its stream as long as there is no
		// conflict with the current stream selection

		// key:<real_lp_short_name>
		// value:<List of lps from the same path>
		HashMap<String, List<Organization>> realLpToIncludedLpMap = new HashMap<String, List<Organization>>();

		// key: <real_lp_short_name>
		// value:<List of excluded lps>
		HashMap<String, ArrayList<Organization>> realLpToExcludedLpMap = new HashMap<String, ArrayList<Organization>>();

		// Phase 1 : Choose the path; prepare the chosen lps from the chosen
		// path; prepare the exclusion list
		for (Map.Entry<CompositeKeys, ArrayList<Organization>> primeBrokerLPMapItr : primeBrokerToLPMap
				.entrySet()) {
			String realLp = primeBrokerLPMapItr.getKey().getKeysAtIndex(0); // {realLP, primeBrokerId}
			String currentPB = primeBrokerLPMapItr.getKey().getKeysAtIndex(1);
			if (realLpToIncludedLpMap.containsKey(realLp)) {
				List<Organization> chosenLPList = realLpToIncludedLpMap
						.get(realLp);
				List<Organization> currentLPList = primeBrokerLPMapItr
						.getValue();

				List<Organization> includedLps, excludedLps;

				// determine if pb set b/w customer org and current org;
				// comparing the first entry suffices since LPs are grouped by PBs, 
				// If no pb set in the current org list, we will have they key as "NO_PB"
				boolean isPBSetWithCurrentOrg = currentPB.equals(NO_PB) ? false
						: true;

				// determine if pb set b/w customer org and chosen org
				boolean isPBSetWithChosenOrg = getPrimeBrokerChain(placedByOrgLE,
						chosenLPList.get(0)) != null ? true : false;

				// do we have a path without any pbs? if yes, then this path
				// wins over any other indirect paths
				if (!isPBSetWithChosenOrg) {
					// there is no pb for the chosen orgs, so add the current
					// lps to excluded path
					includedLps = chosenLPList;
					excludedLps = currentLPList;
				} else if (!isPBSetWithCurrentOrg) {
					// there is no pb for the current orgs, so add the current
					// lps to included path
					includedLps = currentLPList;
					excludedLps = chosenLPList;
				} else {
					// Compare only if the chosen lps and current lps have prime
					// broker set

					// sort alphabaetically, compare the priority between the
					// first lp in the chosen list with
					// the first lp in the current lp list

					Collections.sort(chosenLPList, orgAlphabaeticalComparator);
					Collections.sort(currentLPList, orgAlphabaeticalComparator);
					if (priorityLPList.indexOf(currentLPList.get(0)) < priorityLPList
							.indexOf(chosenLPList.get(0))) {
						includedLps = currentLPList;
						excludedLps = chosenLPList;
					} else {
						includedLps = chosenLPList;
						excludedLps = currentLPList;
					}
				}
				realLpToIncludedLpMap.put(realLp, includedLps);
				// update the excluded list
				ArrayList<Organization> excludedLpList = realLpToExcludedLpMap
						.get(realLp);
				if (excludedLpList == null) {
					excludedLpList = new ArrayList<Organization>();
				}
				excludedLpList.addAll(excludedLps);
				realLpToExcludedLpMap.put(realLp, excludedLpList);
			} else {
				// first entry for this real lp
				ArrayList<Organization> lpList = new ArrayList<Organization>(
						primeBrokerLPMapItr.getValue());
				realLpToIncludedLpMap.put(realLp, lpList);
			}
		}
		// phase 2: process the realLpToIncludedLpMap and realLpToExcludedLpMap
		// to
		// create the filteredOrgs list
		// add the streams from the realLpToIncludedLpMap based on stream
		// priority rule.
		for (Map.Entry<String, List<Organization>> itr : realLpToIncludedLpMap
				.entrySet()) {
			String realLp = itr.getKey();
			Organization realLpForOrg = ISUtilImpl.getInstance().getOrg(realLp);
			ArrayList<Organization> chosenLps = (ArrayList<Organization>) itr
					.getValue();
			if (log.isDebugEnabled()) {
				logMessage(chosenLps, "Chosen LPs for real LP:", realLp + ":");
			}
							
			HashMap<String, Integer> streamToChosenLp = new HashMap<String, Integer>();
			for (int i = 0; i < chosenLps.size(); i++) {
				Organization currentOrg = chosenLps.get(i);
				String streamId = getStreamForCustomerOrgByProvider(
						placedByOrgLE, currentOrg);
				if(streamId.equals(INACTIVE_STREAM)){
					// do not process this current org since its
					// giving an inactive stream.Do not add to the filtered orgs nor use 
					// it for determining stream priority
					continue;
				}
				if (streamToChosenLp.containsKey(streamId)) {
					Integer chosenIndex = streamToChosenLp.get(streamId);
					Organization chosenOrg;
					if (chosenIndex == REAL_LP_INDEX) {
						chosenOrg = realLpForOrg;
					} else {
						chosenOrg = priorityLPList.get(chosenIndex);
					}

					int currentIndex;
					if (currentOrg.getShortName().equals(realLp)) {
						// current lp is the real lp;make its index -1
						currentIndex = REAL_LP_INDEX;
					} else {
						currentIndex = priorityLPList.indexOf(currentOrg);
					}

					if (currentIndex < chosenIndex) {
						filteredOrgs.remove(chosenOrg);
						filteredOrgs.add(currentOrg);

						// update the map with the new priority
						streamToChosenLp.put(streamId, currentIndex);
						
						if (log.isDebugEnabled()) {
							logMessage(
									"MULTIPLE_STREAM_SINGLE_PATH RULE:Provider for stream:",
									streamId, ":replacing ",
									chosenOrg.getShortName(), " with ",
									currentOrg.getShortName());
						}
					}

				} else {
					int chosenLPIndex;
					if (currentOrg.getShortName().equals(realLp)) {
						// current lp is the real lp, add index -1
						chosenLPIndex = REAL_LP_INDEX;
					} else {
						// look up the priority list to figure out the
						// current org's index
						chosenLPIndex = priorityLPList.indexOf(currentOrg);
					}
					streamToChosenLp.put(streamId, chosenLPIndex);
					filteredOrgs.add(currentOrg);
					if (log.isDebugEnabled()) {
						logMessage(
								"MULTIPLE_STREAM_SINGLE_PATH RULE:No chosen LP for stream:",
								streamId, " yet; adding: ",
								currentOrg.getShortName(),
								" with index value of:",
								String.valueOf(chosenLPIndex));
					}
				}
			}
			// iterate over the exclusion list to include their streams if
			// overrride is set and there is no conflict with the current stream 
			// selection
			ArrayList<Organization> excludedLps = realLpToExcludedLpMap
					.get(realLp);
			if (excludedLps != null) {
				// sort the excludedLps based on their priority and start
				// including so that stream from a higher priority excluded 
				// lp is included in case of stream conflicts within excluded lps
				Collections.sort(excludedLps, new Comparator<Organization>() {
					@Override
					public int compare(Organization o1, Organization o2) {
						return priorityLPList.indexOf(o1) < priorityLPList
								.indexOf(o2) ? -1 : 1;
					}
				});

				if (log.isDebugEnabled()) {
					logMessage(excludedLps, "Excluded LPs for real LP:",
							realLp, ":");
				}
				for (int i = 0; i < excludedLps.size(); i++) {
					Organization excludedOrg = excludedLps.get(i);
					// check if override is set for this excluded org					
					if(excludedOrg.getProviderOrgFunction().isOverrideSetAtMaskedLP()){
						String streamId = getStreamForCustomerOrgByProvider(
								placedByOrgLE, excludedOrg);
						if(streamId.equals(INACTIVE_STREAM)){
							// do not process this current org since its
							// giving an inactive stream.D o not add to the filtered orgs nor use 
							// it for determining stream priority
							continue;
						}
						if (!streamToChosenLp.containsKey(streamId)) {
							// this stream has not been included yet; include
							// this excludedOrg
							streamToChosenLp.put(streamId,
									priorityLPList.indexOf(excludedLps.get(i)));
							filteredOrgs.add(excludedOrg);
							if (log.isDebugEnabled()) {
								logMessage(
										"MULTIPLE_STREAM_SINGLE_PATH RULE:Override set. Adding stream:",
										streamId, " with provider:",
										excludedOrg.getShortName());
							}
						}
					}
				}
			}
		}
		return filteredOrgs;
	}

	// takerOrg -> customerOrg
	// providerOrg -> masked lp
	private static String getPrimeBrokerChain(LegalEntity takerOrgLE,
			Organization providerOrganization) {
		return PrimeBrokerPathUtil.getInstance().getPrimeBrokerChainStr(
				takerOrgLE, providerOrganization);
	}

	// TODO: Change this method in 5.2 when we have the new util method
	// which honors this special case.
	private static String getStreamForCustomerOrgByProvider(
			LegalEntity customerLegalEntity, Organization providerOrg) {


		LegalEntity subscriptionLE = customerLegalEntity;
		Organization streamManagementOrg = providerOrg;
		Organization realLP = providerOrg.getRealLP();
		Organization adaptorOrg;
		if(realLP != null){
			adaptorOrg = realLP;
		}else{
			adaptorOrg = providerOrg;
		}
		// realLP is not null, its a masked lp
		// IDC_IS_USE_PB_PRICE_STREAM_PREFIX is set for this masked lp
		if ((realLP != null)
				&& ISFactory.getInstance().getISMBean()
						.isPBPriceStreamEnabled(providerOrg.getShortName())) {
			// check if there is a multi pb path between the customer le and
			// providerOrg [aka masked lp]
			List<TradingParty> pbTPs = PrimeBrokerPathUtil.getInstance()
					.getCoverTradePrimeBrokerTPs(customerLegalEntity,
							providerOrg);
			if (pbTPs != null && !pbTPs.isEmpty()) {
				// if the provider org is a masked lp and
				// IDC_IS_USE_PB_PRICE_STREAM_PREFIX is set
				// for this masked lp, then use the stream assigned from the
				// real lp to the
				// last pb in the multi pb chain between customerLE and masked
				// lp

				subscriptionLE = pbTPs.get(pbTPs.size() - 1).getLegalEntity();
				streamManagementOrg = realLP;
			}
		}		
		String streamId = CounterpartyUtilC.getStreamId(subscriptionLE,
				streamManagementOrg);		
		Stream stream = ISUtilImpl.getInstance()
				.getStream(streamId, adaptorOrg);
		if (stream != null) {
			if (!stream.isActive()) {
				streamId = INACTIVE_STREAM;
			}
		}
		if(streamId == null){
			streamId = "";
		}
		return streamId;
	}

	private static void logMessage(String mesg,
			HashMap<CompositeKeys, ArrayList<Organization>> map) {
		StringBuilder debugMessage = new StringBuilder(mesg);
		debugMessage.append("\n");
		for (Map.Entry<CompositeKeys, ArrayList<Organization>> itr : map
				.entrySet()) {
			debugMessage.append(itr.getKey());
			debugMessage.append(":Orgs:");
			Iterator<Organization> orgItr = itr.getValue().iterator();
			while(orgItr.hasNext()){
				debugMessage.append(orgItr.next().getShortName()).append(":");
			}
			debugMessage.append("\n");
		}
		log.debug(debugMessage.toString());
	}

	private static void logMessage(String... strings) {
		StringBuilder debugMessage = new StringBuilder(100);
		for (int i = 0; i < strings.length; i++) {
			debugMessage.append(strings[i]);
		}
		log.debug(debugMessage.toString());
	}

	private static void logMessage(List<Organization> orgs, String... strings) {
		StringBuilder debugMessage = new StringBuilder(100);
		for (int i = 0; i < strings.length; i++) {
			debugMessage.append(strings[i]);
		}
		for (int i = 0; i < orgs.size(); i++) {
			debugMessage.append(orgs.get(i).getShortName()).append(":");
		}
		log.debug(debugMessage.toString());
	}
}

