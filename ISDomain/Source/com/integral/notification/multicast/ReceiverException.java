package com.integral.notification.multicast;

import com.integral.spaces.SpacesException;

/**
 * Created by <PERSON><PERSON><PERSON> on 11/12/14.
 */
public class ReceiverException extends SpacesException {

    public ReceiverException() {
        super();
    }

    public ReceiverException(String message) {
        super(message);
    }

    public ReceiverException(String message, Throwable cause) {
        super(message, cause);
    }

    public ReceiverException(Throwable cause) {
        super(cause);
    }
}
