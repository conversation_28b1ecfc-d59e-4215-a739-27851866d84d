package com.integral.notification.multicast;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.model.Position;
import com.integral.spaces.SpaceEntity;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 7/1/16
 * Time: 4:21 PM
 * To change this template use File | Settings | File Templates.
 */
public class PositionUnSafeSerializer implements UnSafeSerializer {

    @Override
    public byte[] serialize(SpaceEntity position, UnSafeBuffer buffer) {
        return PositionSerializer.serialize((Position) position,buffer);
    }

    @Override
    public SpaceEntity deSerialize(UnSafeBuffer buffer) {
        return PositionSerializer.deSerialize(buffer);
    }
}
