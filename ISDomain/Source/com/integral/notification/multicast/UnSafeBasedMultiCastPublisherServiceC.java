package com.integral.notification.multicast;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.message.ErrorMessageC;
import com.integral.notification.PublisherService;
import com.integral.spaces.SpaceEntity;

import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.net.UnknownHostException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 7/1/16
 * Time: 3:47 PM
 * To change this template use File | Settings | File Templates.
 */
public class UnSafeBasedMultiCastPublisherServiceC  implements PublisherService{

    private static Log log = LogFactory.getLog(UnSafeBasedMultiCastPublisherServiceC.class);
    MulticastNotificationConfig config;
    private boolean initialized = false;
    MulticastSocket multicastSocket;
    InetAddress inetAddress;
    int port;
    private UnSafeSerializer unSafeSerializer;

    private Map<String,InetAddress> inetAddressMap = new ConcurrentHashMap<String,InetAddress>();

    private static ThreadLocal<UnSafeBuffer> localUnSafeBuffer = new ThreadLocal<UnSafeBuffer>() {
    };

    public UnSafeBasedMultiCastPublisherServiceC(MulticastNotificationConfig config,UnSafeSerializer unSafeSerializer) {
        this.config = config;
        this.unSafeSerializer =unSafeSerializer;
        initialize();
    }

    private void initialize() {
        try {
            String address = config.getMulticastAddress();
            inetAddress = getInetAddress(address);
            port = config.getMulticastPort();
            multicastSocket = new MulticastSocket();
            multicastSocket.joinGroup(inetAddress);
            initialized = true;
        } catch (Exception e) {
            String errorMessage = "Failed to initialize publisher";
            log.error(errorMessage, e);
            throw new PublisherException(errorMessage, e.getCause());
        }
    }

    private InetAddress getInetAddress(String address) throws UnknownHostException {
        return InetAddress.getByName(address);
    }

    public ErrorMessage publishEntity(SpaceEntity entity) {
        return publishEntity(entity, null);
    }

    public ErrorMessage publishEntity(SpaceEntity entity, String address) {
        try {
            if (initialized) {

                UnSafeBuffer buffer = localUnSafeBuffer.get();
                if (buffer == null) {
                    buffer = new UnSafeBuffer();
                    buffer.init(new byte[config.getMessageSize()]);
                    localUnSafeBuffer.set(buffer);
                }
                buffer.resetPosition();

                byte[] bytes = unSafeSerializer.serialize(entity, buffer);

                if (null != bytes) {
                    InetAddress groupAddress = getMultiCastGroupAddress(entity, address);
                    if (log.isDebugEnabled()) {
                        log.debug("Broadcasting position : " + entity.get_id() + " on multicast : "
                                + groupAddress.getHostAddress() + " port : " + port);
                    }
                    DatagramPacket packet = new DatagramPacket(bytes, bytes.length, groupAddress, port);
                    multicastSocket.send(packet);
                }
            } else {
                String errorMessage = "MulticastPublisherServiceC not initialized, hence not publishing entity : " + entity;
                log.error(errorMessage);
                ErrorMessage message = new ErrorMessageC();
                message.setErrorCode(errorMessage);
                return message;
            }
        } catch (Exception e) {
            String errorMessage = "Exception trying to publish position : " + entity;
            log.error(errorMessage, e);
            ErrorMessage message = new ErrorMessageC();
            message.setErrorCode(errorMessage);
            message.setException(e);
            return message;
        }
        return null;

    }

    private InetAddress getMultiCastGroupAddress(SpaceEntity entity, String address) {
        InetAddress groupAddress = inetAddress;
        if(null!=address){
            InetAddress inetAddressCached = inetAddressMap.get(address);
            if(null!=inetAddressCached){
                groupAddress = inetAddressCached;
            }else{
                try{
                    groupAddress = getInetAddress(address);
                    inetAddressMap.put(address, groupAddress);
                }catch(Exception e){
                    if(log.isDebugEnabled()){
                        log.debug("Unable to get inetaddress:"+address+",org:"+entity.getNamespaceName()+"using default inet address"+inetAddress.getHostAddress(),e);
                    }
                }
            }
        }
        return groupAddress;
    }


}