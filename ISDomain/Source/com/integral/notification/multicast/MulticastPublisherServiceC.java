package com.integral.notification.multicast;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.message.ErrorMessageC;
import com.integral.notification.PublisherService;
import com.integral.spaces.SpaceEntity;
import com.integral.spaces.serialize.DefaultSerializationView;
import com.integral.spaces.serialize.SerializationHandler;
import com.integral.spaces.serialize.SerializerFactory;
import com.integral.system.configuration.ConfigurationFactory;

import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;

/**
 * Created by rushabh on 11/12/14.
 * This class is going to be perform a SpacesEntity publishing over multicast.
 */
public class MulticastPublisherServiceC implements PublisherService {

    private static Log log = LogFactory.getLog(MulticastPublisherServiceC.class);
    MulticastNotificationConfig config;
    private boolean initialized = false;
    SerializationHandler handler;
    MulticastSocket multicastSocket;
    InetAddress inetAddress;
    int port;

    public MulticastPublisherServiceC(MulticastNotificationConfig config) {
        this.config = config;
        initialize();
    }

    private void initialize() {
        try {
            String address = config.getMulticastAddress();
            inetAddress = getInetAddress(address);
            port = config.getMulticastPort();
            multicastSocket = new MulticastSocket();
            multicastSocket.joinGroup(inetAddress);
            int ttl = ConfigurationFactory.getServerMBean().getMulticastTTL();
            multicastSocket.setTimeToLive(ttl);
            handler = SerializerFactory.getHandlerForType(SerializerFactory.Type.BSON);
            initialized = true;
            log.info("MPSC.initialize default address=" + address + ", port=" + port + ", ttl=" + ttl );
        } catch (Exception e) {
            String errorMessage = "Failed to initialize publisher";
            log.error(errorMessage, e);
            throw new PublisherException(errorMessage, e.getCause());
        }
    }

    private InetAddress getInetAddress(String address) throws UnknownHostException {
        return InetAddress.getByName(address);
    }

    private static ThreadLocal<ByteBuffer> localByteBuffer =
            new ThreadLocal<ByteBuffer>() {
            };

    public ErrorMessage publishEntity(SpaceEntity object) {
        return publishEntity(object,null);
    }

    public ErrorMessage publishEntity(SpaceEntity object,String address){
        try {
            if (initialized) {
                ByteBuffer buffer = localByteBuffer.get();
                if (buffer == null) {
                    buffer = ByteBuffer.allocate(config.getMessageSize());
                    localByteBuffer.set(buffer);

                }
                buffer.clear();
                handler.serializeObject(object, DefaultSerializationView.class, SerializationHandler.Operation.INSERT, buffer);
                byte[] bytes = ByteBuffer.wrap(buffer.array(), 0, buffer.position()).array();

                InetAddress groupAddress =  inetAddress;
                if(null!=address){
                    try{
                        groupAddress = getInetAddress(address);
                    }catch(Exception e){
                        if (log.isDebugEnabled()) {
                            log.debug("Using default inetaddress:"+inetAddress.getHostAddress()+",problem resolving inetaddress : " + object.get_id() + " on multicast : "
                                    + address + " port : " + port,e);
                        }
                    }
                }

                if (log.isDebugEnabled()) {
                    log.debug("Broadcasting object : " + object.get_id() + " on multicast : "
                            + groupAddress.getHostAddress() + " port : " + port);
                }
                DatagramPacket packet = new DatagramPacket(bytes, bytes.length, groupAddress, port);
                multicastSocket.send(packet);
            } else {
                String errorMessage = "MulticastPublisherServiceC not initialized, hence not publishing object : " + object;
                log.error(errorMessage);
                ErrorMessage message = new ErrorMessageC();
                message.setErrorCode(errorMessage);
                return message;
            }
        } catch (Exception e) {
            String errorMessage = "Exception trying to publish object : " + object;
            log.error(errorMessage, e);
            ErrorMessage message = new ErrorMessageC();
            message.setErrorCode(errorMessage);
            message.setException(e);
            return message;
        }
        return null;

    }
}
