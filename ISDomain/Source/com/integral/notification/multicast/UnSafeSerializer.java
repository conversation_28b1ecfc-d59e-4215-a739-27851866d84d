package com.integral.notification.multicast;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.model.Position;
import com.integral.spaces.SpaceEntity;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 7/1/16
 * Time: 4:18 PM
 * To change this template use File | Settings | File Templates.
 */
public interface UnSafeSerializer {

    public byte[] serialize(SpaceEntity position, UnSafeBuffer buffer);

    public SpaceEntity deSerialize(UnSafeBuffer buffer) ;
}
