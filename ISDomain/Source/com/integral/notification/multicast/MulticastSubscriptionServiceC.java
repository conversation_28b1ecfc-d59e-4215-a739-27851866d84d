package com.integral.notification.multicast;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.message.ErrorMessageC;
import com.integral.notification.SubscriptionHandler;
import com.integral.notification.SubscriptionService;
import com.integral.util.collections.ConcurrentHashSet;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by rushabh on 11/12/14.
 */
public class MulticastSubscriptionServiceC implements SubscriptionService{

    private static final Log log = LogFactory.getLog(MulticastSubscriptionServiceC.class);
    private SubscriptionHandler subscriptionHandler;
    private AtomicBoolean shutdown;
    private MulticastNotificationConfig config;
    private MulticastSocket multicastSocket;
    private static AtomicInteger threadCounter = new AtomicInteger();
    private SubscriptionType subscriptionType = SubscriptionType.BUFFER;
    private Set<String> joinedGroups = new ConcurrentHashSet<String>();

    public MulticastSubscriptionServiceC(MulticastNotificationConfig config){
        this.config = config;
        shutdown = new AtomicBoolean(false);
    }

    public MulticastSubscriptionServiceC(MulticastNotificationConfig config,SubscriptionType subscriptionType){
        this.config = config;
        shutdown = new AtomicBoolean(false);
        this.subscriptionType = subscriptionType;
    }

    private ErrorMessage initialize(boolean joinDefaultGroup) {
        try {
            int multicastPort = config.getMulticastPort();
            log.info("Initializing Multicast address :" + config.getMulticastAddress() + ", port:" + multicastPort);
            multicastSocket = new MulticastSocket(multicastPort);
            if(joinDefaultGroup){
                log.info("Initializing Multicast subscription, joining the default Multicast group  address :" + config.getMulticastAddress() + ", port:" + multicastPort);
                joinDefaultGroup();
            }else{
                log.info("Initializing Multicast subscription, Not joining the default Multicast group  address :" + config.getMulticastAddress() + ", port:" + multicastPort);
            }
            switch(subscriptionType){
                case BUFFER:
                    new Thread(new ListenerThread(multicastSocket),"MulticastSubscriptionService-"+ subscriptionType +"-"+threadCounter.getAndIncrement()+String.valueOf(multicastPort)).start();
                    break;
                case UNSAFE_BUFFER:
                    new Thread(new UnSafeBufferListenerThread(multicastSocket),"MulticastSubscriptionService-"+ subscriptionType +"-"+threadCounter.getAndIncrement()+String.valueOf(multicastPort)).start();
                    break;
                default :
                    throw new IllegalArgumentException("Unsupported subscription type:"+ subscriptionType);
            }
        } catch (Exception e) {
            String message = "Failed to initialize receiver:";
            ErrorMessageC errorMessageC = new ErrorMessageC();
            errorMessageC.setErrorCode(message);
            errorMessageC.setException(e);
            log.error(message, e);
            return errorMessageC;
        }
        return null;
    }

    public  ErrorMessage joinDefaultGroup(){

        String multicastAddress = config.getMulticastAddress();
        try {
            if(joinedGroups.contains(multicastAddress)){
                log.info("MulticastSubscriptionServiceC.joinDefaultGroup:Already joined the default Multicast Group  address :"+multicastAddress );
                return null;
            }
            InetAddress address = getInetAddress();
            multicastSocket.joinGroup(address);
            joinedGroups.add(multicastAddress);
            log.info("MulticastSubscriptionServiceC:Joined the default Multicast Group  address :" + multicastAddress + ", port:" + config.getMulticastPort());
        } catch (IOException e) {
            String message = "joinGroup():Failed to Join default Multicast Group [" + multicastAddress + "]";
            ErrorMessageC errorMessageC = new ErrorMessageC();
            errorMessageC.setErrorCode(message);
            errorMessageC.setException(e);
            log.info(message);
            if(log.isDebugEnabled()){
                log.debug(message, e);
            }
            return errorMessageC;
        }
        return null;
    }

    private InetAddress getInetAddress() throws UnknownHostException {
        return InetAddress.getByName(config.getMulticastAddress());
    }

    public ErrorMessage joinGroup(String addressStr) {
        try {
            InetAddress address = InetAddress.getByName(addressStr);
            multicastSocket.joinGroup(address);
            joinedGroups.add(addressStr);
            if(log.isInfoEnabled()){
                log.info("MulticastSubscriptionServiceC.joinGroup():Joining the multi cast group:"+addressStr+",port:"+multicastSocket.getPort());
            }
        } catch (IOException e) {
            String message = "joinGroup():Failed to Join Group [" + addressStr + "]";
            ErrorMessageC errorMessageC = new ErrorMessageC();
            errorMessageC.setErrorCode(message);
            errorMessageC.setException(e);
            log.info(message);
            if(log.isDebugEnabled()){
                log.debug(message,e);
            }
            return errorMessageC;
        }
        return null;
    }

    public ErrorMessage leaveGroup(String addressStr){
        try {
            multicastSocket.leaveGroup(InetAddress.getByName(addressStr));
            joinedGroups.remove(addressStr);
            if(log.isInfoEnabled()){
                log.info("MulticastSubscriptionServiceC.leaveGroup():Leaving the multi cast group:"+addressStr+",port:"+multicastSocket.getPort());
            }
        } catch (IOException e) {
            String message = "leaveGroup():Failed to Leave Group [" + addressStr + "]";
            ErrorMessageC errorMessageC = new ErrorMessageC();
            errorMessageC.setErrorCode(message);
            errorMessageC.setException(e);
            log.info(message);
            if(log.isDebugEnabled()){
                log.debug(message,e);
            }
            return errorMessageC;
        }
        return null;
    }


    public ErrorMessage start(boolean joinDefaultGroup) {
        return initialize(joinDefaultGroup);
    }

    @Override
    public ErrorMessage start() {
        return start(true);
    }

    @Override
    public ErrorMessage stop() {
        try {
            log.info("Stopping MulticastSubscriptionServiceC");
            if (shutdown.compareAndSet(false, true)) {
                if (null != this.multicastSocket) {
                    if (!this.multicastSocket.isClosed()) {
                        leaveJoinedGroups();
                        this.multicastSocket.close();
                    }
                }
            }
            return null;
        } catch (Exception e) {
            String message = "Failed to close MulticastSubscriptionServiceC";
            ErrorMessageC errorMessageC = new ErrorMessageC();
            errorMessageC.setErrorCode(message);
            errorMessageC.setException(e);
            log.error(message, e);
            return errorMessageC;
        }
    }

    private void leaveJoinedGroups() {
        for (String joinedGroup:joinedGroups){
            try{
                this.multicastSocket.leaveGroup(InetAddress.getByName(joinedGroup));
            }catch (Exception e){
                log.info("Error occurred while leaving the multicast group:"+joinedGroup);
            }
        }
    }

    @Override
    public ErrorMessage registerSubscriptionHandler(SubscriptionHandler subscriptionHandler) {
        this.subscriptionHandler = subscriptionHandler;
        log.info("Successfully registered subscriptionHandler " + subscriptionHandler);
        return null;
    }

    private class ListenerThread implements Runnable {
        MulticastSocket socket;
        ByteBuffer buffer;

        public ListenerThread(MulticastSocket socket) {
            this.socket = socket;
            buffer = ByteBuffer.allocate(config.getMessageSize());
        }

        @Override
        public void run() {
            while (!shutdown.get()) {
                try {
                    DatagramPacket dp = new DatagramPacket(buffer.array(), buffer.capacity());
                    multicastSocket.receive(dp);
                    subscriptionHandler.onReceiveObject(buffer);
                } catch (Exception e) {
                    if(!shutdown.get()){
                        log.error("Error while receiving entity:", e);
                    }
                    log.info("run():Not able to receive the entity,shutdown is already triggered");
                } finally {
                    buffer.clear();
                }
            }
        }
    }

    private class UnSafeBufferListenerThread implements Runnable {
        MulticastSocket socket;
        byte[] buffer;
        UnSafeBuffer safeBuf;

        public UnSafeBufferListenerThread(MulticastSocket socket) {
            this.socket = socket;
            buffer = new byte[config.getMessageSize()];
            safeBuf = new UnSafeBuffer();
        }

        @Override
        public void run() {
            while (!shutdown.get()) {
                try {
                    DatagramPacket dp = new DatagramPacket(buffer, buffer.length);
                    multicastSocket.receive(dp);
                    safeBuf.init(buffer);
                    subscriptionHandler.onReceiveObject(safeBuf);
                } catch (Exception e) {
                    if(!shutdown.get()){
                        log.error("UnSafeBufferListenerThread : Error while receiving entity:", e);
                    }
                    else {
                        log.info("UnSafeBufferListenerThread : Not able to receive the entity,shutdown is already triggered");
                    }
                } finally {
                    safeBuf.resetMemoryBuffer();
                }
            }
        }
    }

    public enum SubscriptionType {
        BUFFER,UNSAFE_BUFFER;
    }

}

