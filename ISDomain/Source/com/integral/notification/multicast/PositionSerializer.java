package com.integral.notification.multicast;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.AccountOwner;
import com.integral.model.Position;
import com.integral.model.PositionState;
import com.integral.persistence.Entity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserC;
import com.integral.user.UserFactory;

import java.util.Date;


/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 12/9/15
 * Time: 3:49 PM
 * To change this template use File | Settings | File Templates.
 */
public class PositionSerializer {

    private static final Log log = LogFactory.getLog(PositionSerializer.class);

    private static final byte VERSION = 1;

    public static byte[] serialize(Position position, UnSafeBuffer buffer) {
        User user=null;
        if (position.getAccountOwner()!=null && position.getAccountOwner().getAccountOwnerLevel() == AccountOwner.AccountOwnerLevel.USER) {
            user = UserFactory.getUser(position.getAccountOwner().getEntityName() + '@' + position.getNamespaceName());
            if (null == user || ! user.isActive()) {
                if (log.isDebugEnabled()) {
                    log.debug("User not found or user is inactive.Skipping position publishing:" + position);
                }
                return null;
            }
        }

        Organization org = ReferenceDataCacheC.getInstance().getOrganization(position.getNamespaceName());
        if(null == org || !org.isActive()){
            if (log.isDebugEnabled()) {
                log.debug("Organization not found or organization is inactive:"+position.getNamespaceName()+".Skipping position publishing:" + position);
            }
            return null;
        }

       return serialize(position, user,org,buffer);
    }

    public static byte[] serialize(Position position, User user,Organization org,UnSafeBuffer buffer) {

        try {
            buffer.put(VERSION);
            buffer.putInt(getOrganizationIndex(org));
            buffer.putString(position.getCurrencyPair());
//            buffer.putString(position.getReportingCurrency());
            buffer.putDouble(position.getBaseCurrAmt());
            buffer.putDouble(position.getTermCurrAmt());
            buffer.putDouble(position.getRealizedPNL());
            buffer.putDouble(position.getRealizedPNLInRepCcy());
            buffer.putDouble(position.getRolledBaseCcyAmt());
            buffer.putDouble(position.getRolledTermCcyAmt());

            buffer.putLong(position.getValueDate().getTime());
          //  buffer.putLong(position.getTimeToLive().getTime());

            buffer.putDouble(position.getVolume());
            buffer.putDouble(position.getVolumeInRepCcy());
            buffer.putInt(position.getState().getStateId());

//            buffer.putString(position.getBookName());
//            buffer.putLong(position.getLastModifiedTime());
//            buffer.putLong(position.getCalculationVersionId());

            buffer.putDouble(position.getBaseBuyAmount());
            buffer.putDouble(position.getTermBuyAmount());
            buffer.putDouble(position.getBaseSellAmount());
            buffer.putDouble(position.getTermSellAmount());
            buffer.putDouble(position.getAverageBuyRate());
            buffer.putDouble(position.getAverageSellRate());

            serializeAccountOwner(position, user, org, buffer);
            buffer.putLong(position.getVersionId());
            buffer.putString(position.getId());
            buffer.flip();
            return buffer.array();
        } catch (Exception e) {
            log.info("Failed to serialize the position object:" + position);
            if(log.isDebugEnabled()){
                log.debug("Failed to serialize the position object:" + position,e);
            }
            buffer.resetPosition();
            return null;
        }
    }


    private static void serializeAccountOwner(Position position, User user,Organization org , UnSafeBuffer buffer) {
        AccountOwner accountOwner = position.getAccountOwner();
        AccountOwner.AccountOwnerLevel accountOwnerLevel = accountOwner.getAccountOwnerLevel();
        buffer.putInt(accountOwnerLevel.getLevelIndex());
        switch (accountOwnerLevel) {
            case ORG:
            case MAKER_ORG:
            case TAKER_ORG:
                buffer.putInt(getOrganizationIndex(org));
                break;
            case USER:
                buffer.putLong(user.getObjectId());
                break;
            case LE:
                LegalEntity le = (LegalEntity) ReferenceDataCacheC.getInstance().getEntityByShortName(accountOwner.getEntityName(), LegalEntity.class, org.getNamespace(), Entity.ACTIVE_STATUS);
                buffer.putLong(le.getObjectId());
                break;
            case CPTYORG:
                Organization cptyOrg= ReferenceDataCacheC.getInstance().getOrganization(accountOwner.getEntityName());
                buffer.putInt(cptyOrg.getIndex());
                break;
            default: {
                throw new UnsupportedOperationException("Unsupported account owner level:" + accountOwnerLevel);
            }
        }
    }

    private static Integer getOrganizationIndex(Position position) {
        Organization org = ReferenceDataCacheC.getInstance().getOrganization(position.getNamespaceName());
        return org.getIndex();
    }

    private static Integer getOrganizationIndex(Organization org) {
        return org.getIndex();
    }

    public static int getNameSpace(UnSafeBuffer buffer) {
        //skip to second position first byte is version
        buffer.skip(1);
        int nameSpace = buffer.getInt();
        buffer.resetPosition();
        return nameSpace;
    }

    private static String readNameSpace(UnSafeBuffer buffer) {
        Organization organization = ReferenceDataCacheC.getInstance().getOrganization(buffer.getInt());
        return organization.getShortName();
    }

    public static Position deSerialize(UnSafeBuffer buffer) {
        try {
            Position position = new Position();
            //skip to second position first byte is version
            //byte version = buffer.get();
            buffer.skip(1);
            position.setNamespaceName(readNameSpace(buffer));
            position.setCurrencyPair(buffer.getString());
//            position.setReportingCurrency(buffer.getString());
            position.setBaseCurrAmt(buffer.getDouble());
            position.setTermCurrAmt(buffer.getDouble());
            position.setRealizedPNL(buffer.getDouble());
            position.setRealizedPNLInRepCcy(buffer.getDouble());
            position.setRolledBaseCcyAmt(buffer.getDouble());
            position.setRolledTermCcyAmt(buffer.getDouble());

            position.setValueDate(new Date(buffer.getLong()));
//            position.setTimeToLive(new Date(buffer.getLong()));

            position.setVolume(buffer.getDouble());
            position.setVolumeInRepCcy(buffer.getDouble());
            position.setState(PositionState.getPositionState(buffer.getInt()));

//            position.setBookName(buffer.getString());
//            position.setLastModifiedTime(buffer.getLong());
//            position.setCalculationVersionId(buffer.getLong());

            position.setBaseBuyAmount(buffer.getDouble());
            position.setTermBuyAmount(buffer.getDouble());
            position.setBaseSellAmount(buffer.getDouble());
            position.setTermSellAmount(buffer.getDouble());
            position.setAverageBuyRate(buffer.getDouble());
            position.setAverageSellRate(buffer.getDouble());

            AccountOwner accountOwner = getAccountOwner(buffer);
            position.setAccountOwner(accountOwner);

            position.setVersionId(buffer.getLong());
            position.set_id(buffer.getString());
            return position;
        } catch (Exception e) {
            log.info("Failed to de-serialize the position object");
            if(log.isDebugEnabled()){
                log.debug("Failed to de-serialize the position object",e);
            }
        }
        return null;
    }

    private static AccountOwner getAccountOwner(UnSafeBuffer buffer) {
        AccountOwner.AccountOwnerLevel accountOwnerLevel = AccountOwner.AccountOwnerLevel.getAccountOwnerLevel(buffer.getInt());
        String entityName = null;
        switch (accountOwnerLevel) {
            case ORG:
            case MAKER_ORG:
            case TAKER_ORG:
                entityName = readNameSpace(buffer);
                break;
            case USER:
                User user = (User) ReferenceDataCacheC.getInstance().getEntityByObjectId(buffer.getLong(), UserC.class);
                entityName = user.getShortName();
                break;
            case LE:
                LegalEntity le = (LegalEntity) ReferenceDataCacheC.getInstance().getEntityByObjectId(buffer.getLong(), LegalEntity.class);
                entityName = le.getShortName();
                break;
            case CPTYORG:
                Organization organization = ReferenceDataCacheC.getInstance().getOrganization(buffer.getInt());
                entityName = organization.getShortName();
                break;
            default: {
                throw new UnsupportedOperationException("Unsupported account owner level:" + accountOwnerLevel);
            }
        }
        return new AccountOwner(accountOwnerLevel, entityName);
    }
}