package com.integral.notification;

import com.integral.message.ErrorMessage;

/**
 * Created by rush<PERSON><PERSON> on 11/12/14.
 */
public interface SubscriptionService {

    public ErrorMessage registerSubscriptionHandler(SubscriptionHandler subscriptionHandler);

    public ErrorMessage start(boolean joinDefaultGroup);

    public ErrorMessage start();

    public ErrorMessage stop();

    public ErrorMessage joinGroup(String address);

    public ErrorMessage leaveGroup(String address);

    public  ErrorMessage joinDefaultGroup();
}
