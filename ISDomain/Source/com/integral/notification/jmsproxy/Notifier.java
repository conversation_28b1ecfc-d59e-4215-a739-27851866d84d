package com.integral.notification.jmsproxy;

import com.integral.is.alerttest.AlertCheckC;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.jmsproxy.server.jmsproxy.Connection;
import com.integral.jmsproxy.server.jmsproxy.ConnectionCache;
import com.integral.jmsproxy.server.jmsproxy.ConnectionCacheC;
import com.integral.jmsproxy.server.stream.Stream;
import com.integral.jmsproxy.server.stream.StreamElement;
import com.integral.jmsproxy.server.stream.StreamElementC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.User;
import org.codehaus.jackson.map.ObjectMapper;

public class Notifier
{
    private ObjectMapper jsonObjectMapper  = new ObjectMapper();
    private ConnectionCache connectionCache = ConnectionCacheC.getInstance();
    protected Log log = LogFactory.getLog( this.getClass() );
    protected Log gridAPILogger = LogFactory.getLog( "com.integral.fxiapi.logger" );
    protected final ISMBean ismb;
    private static Notifier notifier = new Notifier();

    public static Notifier getInstance()
    {
        return notifier;
    }
    private Notifier()
    {
    	ismb = ISFactory.getInstance().getISMBean();
    }

    /**
     *
     * @param key
     * @param priority
     * @param handlerId
     * @param asyncResponse
     * @param user
     */
    public void addToJmsProxy( String key, int priority, String handlerId, Object asyncResponse, User user )
    {
        addToJmsProxy( key, priority, handlerId, asyncResponse, user, true );
    }

    /**
     *
     * @param key
     * @param priority
     * @param handlerId
     * @param asyncResponse
     * @param user
     * @param wrapAsJSON
     */
    public void addToJmsProxy( String key, int priority, String handlerId, Object asyncResponse, User user, boolean wrapAsJSON )
    {
        addToJmsProxy(key, priority, handlerId, asyncResponse, user, wrapAsJSON, false);
    }

    /**
     *
     * @param key
     * @param priority
     * @param handlerId
     * @param asyncResponse
     * @param user
     * @param wrapAsJSON
     * @param ackRequired
     */
    public void addToJmsProxy( String  key,
                               int     priority,
                               String  handlerId,
                               Object  asyncResponse,
                               User    user,
                               boolean wrapAsJSON,
                               boolean ackRequired )
    {
        try
        {
            // Alert check for QA testing
            if ("OM".equalsIgnoreCase(handlerId)) {
                AlertCheckC.callback(ISAlertMBean.IDC_IS_ORDER_MESSAGE_JMSPROXY);
            }
            else if ("PM".equalsIgnoreCase(handlerId)) {
                AlertCheckC.callback(ISAlertMBean.IDC_IS_POSITION_MESSAGE_JMSPROXY);
            }

            String response;

            if ( wrapAsJSON )
            {
                response = jsonObjectMapper.writeValueAsString( asyncResponse );
            }
            else
            {
                response = ( String ) asyncResponse;
            }

            StreamElement streamElement = new StreamElementC( key, response, handlerId, ackRequired, priority );
            sendToUser(user,response,streamElement,handlerId, ackRequired);

        }
        catch ( Exception e )
        {
            log.error( "Notifier.addToJmsProxy. Error while converting/sending JSON : object = " + asyncResponse
                    + " hId=" + handlerId, e );
        }
    }

    /**
     *
     * @param key
     * @param priority
     * @param handlerId
     * @param asyncResponse
     * @param users
     * @param wrapAsJSON
     * @param ackRequired
     */
    public void addToJmsProxy( String  key,
                               int     priority,
                               String  handlerId,
                               Object  asyncResponse,
                               User[]    users,
                               boolean wrapAsJSON,
                               boolean ackRequired )
    {
        try
        {
            // Alert check for QA testing
            if ("OM".equalsIgnoreCase(handlerId)) {
                AlertCheckC.callback(ISAlertMBean.IDC_IS_ORDER_MESSAGE_JMSPROXY);
            }
            else if ("PM".equalsIgnoreCase(handlerId)) {
                AlertCheckC.callback(ISAlertMBean.IDC_IS_POSITION_MESSAGE_JMSPROXY);
            }

            String response;

            if ( wrapAsJSON )
            {
                response = jsonObjectMapper.writeValueAsString( asyncResponse );
            }
            else
            {
                response = ( String ) asyncResponse;
            }

            StreamElement streamElement = new StreamElementC( key, response, handlerId, ackRequired, priority );
            for(User user:users)
            {
                sendToUser(user,response,streamElement,handlerId,ackRequired);
            }
        }
        catch ( Exception e )
        {
            log.error( "Notifier.addToJmsProxy. Error while converting/sending JSON : object = " + asyncResponse
                    + " hId=" + handlerId, e );
        }
    }

    private void  sendToUser(User user,String response,StreamElement streamElement,
                             String  handlerId,boolean ackRequired)
    {
        try
        {
            Connection connection = connectionCache.getConnection(user);
            if (connection != null)
            {
                Stream stream = connection.getStream();
                if (stream != null)
                {
                    if(ismb.isLogFullGridAPIMessage(handlerId) || response.length() < 100 ) {
                    	 StringBuilder sb = new StringBuilder(response.length() + 180);
                         sb.append("Notifier.addToJmsProxy Message sent to user: ")
                                 .append(user.getShortName())
                                 .append(", AckId: ").append(streamElement.getKey())
                                 .append(" and Ack ").append(ackRequired ? "enabled" : "disabled")
                                 .append(", MSG: ").append(response);
                         gridAPILogger.info(sb.toString());
                    }else {
                    	StringBuilder sb = new StringBuilder(100 + 180);
                    	sb.append("Notifier.addToJmsProxy Message sent to user: ")
                    	.append(user.getShortName())
                    	.append(", AckId: ").append(streamElement.getKey())
                    	.append(" and Ack ").append(ackRequired ? "enabled" : "disabled")
                    	.append(", MSG: ").append(response,0,99);
                    	gridAPILogger.info(sb.toString());
                    }
                    
                    stream.add(streamElement);
                }
            }
            else
            {
                if (log.isDebugEnabled())
                {
                    log.debug("Notifier.addToJmsProxy: No connection for the user: " + user);
                }
            }
        }
        catch(Exception e)
        {
            log.error( "Notifier.addToJmsProxy. Error while converting/sending JSON : object = " + response
                    + " hId=" + handlerId, e );
        }
    }
}
