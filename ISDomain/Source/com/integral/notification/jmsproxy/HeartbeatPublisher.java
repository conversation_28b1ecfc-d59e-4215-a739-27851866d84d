package com.integral.notification.jmsproxy;

import com.integral.jmsproxy.server.jmsproxy.Connection;
import com.integral.jmsproxy.server.jmsproxy.ConnectionCacheC;
import com.integral.persistence.cache.ReferenceDataCacheC;

public class HeartbeatPublisher {

    private void sendHeartbeat() {

        for(String user : ConnectionCacheC.getInstance().getAllUserIds()) {
       //     ReferenceDataCacheC.getInstance().getEntityByGuid(user, User.class, null, 'A');
       //     Notifier.getInstance().addToJmsProxy("heartbeat", 0, "heartbeat", new HeartbeatMessage(), null);
        }
    }



}
