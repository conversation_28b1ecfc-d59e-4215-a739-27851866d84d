package com.integral.notification;

import com.google.common.collect.Sets;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.message.ErrorMessageC;
import com.integral.notification.multicast.ReceiverException;
import com.integral.spaces.SpaceEntity;
import com.integral.spaces.serialize.ByteBufferInputStream;
import com.integral.spaces.serialize.DefaultSerializationView;
import com.integral.spaces.serialize.SerializationHandler;
import com.integral.spaces.serialize.SerializerFactory;

import java.nio.ByteBuffer;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 2/6/15
 * Time: 2:51 PM
 * To change this template use File | Settings | File Templates.
 */
public abstract class SpaceEntitySubscriptionHandler<T extends SpaceEntity> implements SubscriptionHandler<ByteBuffer> {

    private SerializationHandler handler;

    private Class deSerializationClass;

    Set<String> namespaceSet;

    private static final Log log = LogFactory.getLog(SpaceEntitySubscriptionHandler.class);

    public SpaceEntitySubscriptionHandler(Class deSerializationClass) {
        this.handler = SerializerFactory.getHandlerForType(SerializerFactory.Type.BSON);
        this.namespaceSet = Sets.newSetFromMap(new ConcurrentHashMap<String, Boolean>());
        this.deSerializationClass = deSerializationClass;
    }

    @Override
    public void onReceiveObject(ByteBuffer buffer) {
        try {
            //TODO : optimize this piece of code to deserialize only the message partially and checking it if any subscribers exist
            T entity = (T) handler.deserializeObject(deSerializationClass, new ByteBufferInputStream(buffer), DefaultSerializationView.class);
            if (log.isDebugEnabled()) {
                log.debug("SpaceEntity RECD " + entity.get_id());
            }

            onReceiveEntity(entity);

        } catch (Exception e) {
            log.error("Error while receiving entity:" + (buffer != null ? buffer.array() : ""), e);
        }
    }

    public abstract void onReceiveEntity(T entity);

    /**
     * @param namespace
     * @return
     */
    public ErrorMessage subscribeForNameSpace(String namespace) {
        try {
            if (namespace == null) {
                throw new ReceiverException("Cannot register null namespace");
            }
            namespaceSet.add(namespace);
            log.info("Successfully subscribed for namespace :" + namespace);

        } catch (Exception e) {
            String error = "Failed to subscribed for namespace: " + namespace;
            log.error(error, e);
            ErrorMessage errorMessage = new ErrorMessageC();
            errorMessage.setErrorCode(error);
            errorMessage.setException(e);
            return errorMessage;
        }
        return null;
    }

    public ErrorMessage unSubscribeForNameSpace(String namespace) {
        try {
            if (namespace == null) {
                throw new ReceiverException("Cannot register null namespace");
            }
            namespaceSet.remove(namespace);
            log.info("Successfully unSubscribed for namespace :" + namespace);

        } catch (Exception e) {
            String error = "Failed to unSubscribed for namespace: " + namespace;
            log.error(error, e);
            ErrorMessage errorMessage = new ErrorMessageC();
            errorMessage.setErrorCode(error);
            errorMessage.setException(e);
            return errorMessage;
        }
        return null;
    }

}
