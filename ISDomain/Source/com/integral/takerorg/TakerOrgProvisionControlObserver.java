package com.integral.takerorg;



import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.regex.Pattern;



import com.integral.adaptor.order.OrderBroadcaster;
import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.exception.IdcException;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.message.MessageFactory;
import com.integral.liquidityrules.TakerOrgProvisionControl;
import com.integral.log.Log;
import com.integral.log.LogFactory;

import com.integral.message.WorkflowMessage;
import com.integral.model.ReferenceEntity;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;
import com.integral.system.notification.NotificationEvent;
import com.integral.system.notification.NotificationEventFilter;
import com.integral.system.notification.NotificationFactory;
import com.integral.system.notification.NotificationSenderC;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.takerorg.service.TakerOrgProvisionControlService;
import com.integral.takerorg.service.TakerOrgfactory;
import com.integral.user.Organization;
import com.integral.user.User;


public  class TakerOrgProvisionControlObserver implements NotificationObserver {

	
	private TakerOrgProvisionControlService takerOrgProvisionService = TakerOrgfactory.getInstance().getTakerProvisionService();

	private ConcurrentMap<String, TakerOrgProvisionControl> orgVsTakerOrgControlMap = new ConcurrentHashMap<String, TakerOrgProvisionControl>();
        
    

   
    
    @Override
    public void notifyAdd(Notification notification) {
    	
        try {
			handle(notification.getEntityNameSpace(), notification.getEntityId());
		} catch (IdcException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
    }

    @Override
    public void notifyUpdate(Notification notification) {
        try {
			handle(notification.getEntityNameSpace(), notification.getEntityId());
		} catch (IdcException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
    }
    
    protected ReferenceEntity getReferenceEntity(String nameSpace, String entityId) {
    	return takerOrgProvisionService.getTakerProvisionDataService().getTakerProvisionById(nameSpace, entityId);        
    }
    
    
    private Log log = LogFactory.getLog(this.getClass());

	/* (non-Javadoc)
	 * @see com.integral.message.MessageHandler#handle(com.integral.message.Message)
	 */
	public void  handle( String nameSpace, String entityId ) throws IdcException
	{
	
			TakerOrgProvisionControl latestTakerProvisionControl = (TakerOrgProvisionControl) getReferenceEntity(nameSpace, entityId);
			if(latestTakerProvisionControl == null){
				return;
			}
			Organization org = ISUtilImpl.getInstance().getOrg(latestTakerProvisionControl.getShortName());	
			org.setTakerOrgProvisionControl(latestTakerProvisionControl);			
			TakerOrgProvisionControl tempTakerProvisionControl = new TakerOrgProvisionControl(latestTakerProvisionControl.getShortName(), latestTakerProvisionControl.isStreamingPriceEnabled(), latestTakerProvisionControl.isOrderExecutionEnabled(), latestTakerProvisionControl.isFmaPricingEnabled());
			
			TakerOrgProvisionControl takerProvisionControl = orgVsTakerOrgControlMap.put(nameSpace,tempTakerProvisionControl);			
			if(RuntimeFactory.getServerRuntimeMBean().isTakerOrgControlQueryFromMongoEnabled(nameSpace)){
				if(takerProvisionControl == null || takerProvisionControl.isOrderExecutionEnabled() != latestTakerProvisionControl.isOrderExecutionEnabled()){	
					
					sendPushNotification(org,latestTakerProvisionControl);
				}
			}
					
			return;
		
	}
	
	
	public void sendPushNotification(Organization org,TakerOrgProvisionControl latestTakerProvisionControl ){	
			
			String orgName = org.getShortName();
			
			if ( org != null )
			{
				sendAlert(org,latestTakerProvisionControl);
				Collection<Organization> orderProviderList = OrderConfiguration.getInstance().getExtendedOrderProviderOrgsList();
				if ( orderProviderList.contains(org) )
				{				
					log.info("TOPCO.sendPushNotification - Order execution updated for org=" + org + ",value=" + latestTakerProvisionControl.isOrderExecutionEnabled());
					OrderBroadcaster.republishAll(orgName, latestTakerProvisionControl.isOrderExecutionEnabled());
				}
				else
				{
					log.info("TOPCO.sendPushNotification : Org skipped. Not in order provider's list (extended). Org=" + orgName + ",List=" + orderProviderList);
				}
			}
			else
			{
				log.info("TOPCO.sendPushNotification : Org is null. Skipped. OrgName=" + orgName + ",Org=" + org);
			}
		
	}

	/**
	 * @param org
	 */
	private void sendAlert( Organization org,TakerOrgProvisionControl takerProvisionControl )
	{
		String message = org_pattern.matcher(takerProvisionControl.isOrderExecutionEnabled() ? ORG_ENABLED_NOTIFICATION_PATTERN : ORG_DISABLED_NOTIFICATION_PATTERN).replaceFirst(org.getShortName());
		ArrayList<User> users = ISUtilImpl.getInstance().getLoggedInUserObjects(ISCommonConstants.CLIENT_NAME_FXINSIDE);
		Iterator<User> us = users.listIterator();
		HashSet<Organization> orgs = new HashSet<Organization>();
		while ( us.hasNext() )
		{
			User user = us.next();
			if ( user.getOrganization().getObjectId() == org.getObjectId() )
			{
				orgs.add(user.getOrganization());
				continue;
			}
			Collection<Organization> o =  new ArrayList(user.getOrganization().getRelatedOrganizations(ISCommonConstants.LP_ORG_RELATIONSHIP));
			if ( o.contains(org) )
			{
				orgs.add(user.getOrganization());
				continue;
			}
			us.remove();
		}
		for ( Organization organization : orgs )
		{
			WorkflowMessage msg = MessageFactory.newWorkflowMessage();
			msg.setParameterValue("MessageContent", message);
			msg.setParameterValue("MessageProps", null);

			NotificationEvent ne = NotificationFactory.newNotificationEvent();
			ne.setMessage(msg);
			NotificationEventFilter nf = NotificationFactory.newOrganizationUsersNotificationEventFilter(organization);
			NotificationSenderC.getInstance().sendMessage(nf, ne, 1);

		}
	}

	static String ORG_DISABLED_NOTIFICATION_PATTERN = "<workflowMessage><event>EXECUTION_DISABLED</event><topic>ALERT</topic><parameter key=\"org\" class=\"java.lang.String\">_ORGNAME_</parameter></workflowMessage>";
	static String ORG_ENABLED_NOTIFICATION_PATTERN = "<workflowMessage><event>EXECUTION_ENABLED</event><topic>ALERT</topic><parameter key=\"org\" class=\"java.lang.String\">_ORGNAME_</parameter></workflowMessage>";
	static Pattern org_pattern = Pattern.compile("_ORGNAME_");
}
