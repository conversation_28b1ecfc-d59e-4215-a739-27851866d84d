package com.integral.tradingvenue;

import com.integral.exception.IdcException;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.common.TradingVenueFactory;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.functor.OrganizationModificationRemoteTransactionFunctor;
import com.integral.is.rex.provision.RexProvisionCache;
import com.integral.is.rex.provision.V4EmsRexPovisionCache;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.server.VirtualServer;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.Map;

/**
 * Created by pranabdas on 11/19/14.
 */
public class TradingVenueOrganizationChangeHandlerC implements MessageHandler{
    private static final Log log = LogFactory.getLog( TradingVenueOrganizationChangeHandlerC.class );
    public static final VirtualServer virtualServer = ISFactory.getInstance().getISMBean().getVirtualServer();

    public Message handle(Message message) throws IdcException
    {
        if( ISFactory.getInstance().getISMBean().isServicesEnabled()){
            Map props = message.getMap();

            String notificationType = (String) props.get( OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_KEY_NOTIFICATIONTYPE);

            if (!OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_VALUE_VIRTUALSERVER_UPDATE.equals(notificationType)) {
                return message;
            }

            String organizationName = (String) props.get(OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_KEY_SHORTNAME);
            Organization organization = ReferenceDataCacheC.getInstance().getOrganization( organizationName );
            Collection<Organization> provisionedOrgs = ISUtilImpl.getInstance().getProvisionedOrgs();

            if( provisionedOrgs.contains( organization ) ){
                Collection<TradingVenueRelationShip> tradingVenueRelations = ISUtilImpl.getInstance().getTradingVenueRelationShips( organization );
                for( TradingVenueRelationShip tradingVenueRelationship : tradingVenueRelations ){
                    Organization tvOrg = tradingVenueRelationship.getTvOrg();
                    TradingVenueEntity tradingVenue = tvOrg.getTradingVenueOrgFunction().getTradingVenue();
                    Collection<CurrencyPair> currencyPairs = tradingVenue.getSupportedCurrencyPairs().getCurrencyPairs();
                    for( CurrencyPair currencyPair : currencyPairs ){
                        TradingVenueFactory.getInstance().loadTradingVenue( tvOrg, currencyPair, tradingVenue.getVenueType() );
                    }
                }
                RexProvisionCache.updateForOrgChange( organization );
                V4EmsRexPovisionCache.updateForOrgChange( organization );
            }
        }
        return message;
    }
}
