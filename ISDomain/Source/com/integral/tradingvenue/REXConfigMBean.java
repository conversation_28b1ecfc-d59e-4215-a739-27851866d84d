package com.integral.tradingvenue;

import com.integral.adaptor.config.AdaptorConfigurationMBean;

/**
 * Created by prana<PERSON><PERSON> on 10/2/14.
 */
public interface REXConfigMBean extends AdaptorConfigurationMBean
{

    public static final String IDC_IS_DIRECTED_ORDER_TRADING_VENUE_NAME = "IDC.IS.Directed.Order.TradingVenue.Name";
    public static final String IDC_IS_DIRECTED_ORDER_TRADING_VENUE_NAME_PREFIX = IDC_IS_DIRECTED_ORDER_TRADING_VENUE_NAME + ".";
    public static final String IDC_IS_DIRECTED_ORDER_TIME_IN_REX = "IDC.IS.Directed.Order.Time.In.REX";
    public static final String IDC_IS_DIRECTED_ORDER_TIME_IN_REX_PREFIX = IDC_IS_DIRECTED_ORDER_TIME_IN_REX + ".";
    public static final String IDC_IS_DIRECTED_ORDER_OVERRIDE_TIF = "IDC.IS.Directed.Order.Override.Tif";
    public static final String IDC_IS_DIRECTED_ORDER_OVERRIDE_TIF_PREFIX = IDC_IS_DIRECTED_ORDER_OVERRIDE_TIF + ".";
    public static final String IDC_IS_DIRECTED_ORDER_VENUE_ONLY = "IDC.IS.Directed.Order.Venue.Only";
    public static final String IDC_IS_DIRECTED_ORDER_VENUE_ONLY_PREFIX = IDC_IS_DIRECTED_ORDER_VENUE_ONLY + ".";
    public static final String PRICE_SOURCES_STRING = "Idc.Rex.Supported.Price.Source";
    public static final String DEFAULT_SRC = "FXB";

    int getTimeInREX();

    int getTimeInREX( String custOrg );

    String getTradingVenue();

    String getTradingVenue( String custOrg );

    boolean isOverrideTIF();

    boolean isOverrideTIF( String custOrg );

    boolean isVenueOnly();

    boolean isVenueOnly( String custOrg );

    String getPriceSource(String priceSrcName);

}
