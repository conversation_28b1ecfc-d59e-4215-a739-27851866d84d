package com.integral.tradingvenue;

import java.util.Map;
import java.util.HashMap;
import java.util.Properties;

import com.integral.adaptor.config.AdaptorConfiguration;

/**
 * Created by prana<PERSON>das on 10/2/14.
 */
public class REXConfig extends AdaptorConfiguration implements REXConfigMBean
{

    @SuppressWarnings({ "StaticNonFinalField" })
    private static REXConfigMBean instance = createInstanceAndLoadProperties();
    private String tradingVenue;
    private Map<String, String> tradingVenueMap = new HashMap<String, String>();
    private int timeInREX;
    private Map<String, Integer> timeInREXMap = new HashMap<String, Integer>();
    private boolean overrideTIF;
    private Map<String, Boolean> overrideTIFMap = new HashMap<String, Boolean>();
    private boolean venueOnly;
    private Map<String, Boolean> venueOnlyMap = new HashMap<String, Boolean>();



    private REXConfig(){
        super("com.integral.tradingvenue.REXConfigMBean");
    }

    /**
     * Returns a singleton instance.
     *
     * @return a singleton instance of <code>OrderConfigurationMBean</code>
     */
    public static REXConfigMBean getInstance()
    {
        return instance;
    }

    @Override
    public void initialize()
    {
        super.initialize();
        loadProperties();
    }

    /**
     * Creates an instance of OrderServiceMBeanC and loads all properties.
     *
     * @return an instance of OrderServiceMBeanC with all properties loaded.
     */
    private static REXConfig createInstanceAndLoadProperties()
    {
        final REXConfig config = new REXConfig();
        config.loadProperties();
        return config;
    }

    public void loadProperties(){
        try{
            tradingVenue = getStringProperty( IDC_IS_DIRECTED_ORDER_TRADING_VENUE_NAME, null );
            tradingVenueMap = initSingleSuffixStringPropertyMap( IDC_IS_DIRECTED_ORDER_TRADING_VENUE_NAME_PREFIX, null );
            timeInREX = getIntProperty( IDC_IS_DIRECTED_ORDER_TIME_IN_REX, 60 );
            timeInREXMap = initSingleSuffixIntegerPropertyMap( IDC_IS_DIRECTED_ORDER_TIME_IN_REX_PREFIX, null );
            overrideTIF = getBooleanProperty( IDC_IS_DIRECTED_ORDER_OVERRIDE_TIF, false );
            overrideTIFMap = initSingleSuffixBooleanPropertyMap( IDC_IS_DIRECTED_ORDER_OVERRIDE_TIF_PREFIX, null );
            venueOnly = getBooleanProperty( IDC_IS_DIRECTED_ORDER_VENUE_ONLY, true );
            venueOnlyMap = initSingleSuffixBooleanPropertyMap( IDC_IS_DIRECTED_ORDER_VENUE_ONLY_PREFIX, null );
        }
        catch( Exception ex ){
            log.error( "REXConfig.initialize.ERROR : Exception while initializing the config properties for TradingVenue="
                    + tradingVenue, ex );
        }
    }

    public String getTradingVenue(){
        return this.tradingVenue;
    }

    public String getTradingVenue( String customerOrg ){
        String tv = tradingVenueMap.get( customerOrg );
        return ( tv == null ) ? this.tradingVenue : tv;
    }

    public int getTimeInREX(){
        return this.timeInREX;
    }

    public int getTimeInREX( String customerOrg ){
        Integer tir = timeInREXMap.get( customerOrg );
        return ( tir == null || tir == 0 ) ? this.timeInREX : tir;
    }

    public boolean isOverrideTIF(){ return this.overrideTIF; }

    public boolean isOverrideTIF( String customerOrg ){
        Boolean override = overrideTIFMap.get( customerOrg );
        return ( override == null ) ? this.overrideTIF : override;
    }

    public boolean isVenueOnly(){ return this.venueOnly; }

    public boolean isVenueOnly( String custOrg ){
        Boolean isVenueOnly = venueOnlyMap.get( custOrg );
        return ( isVenueOnly == null ) ? this.venueOnly : isVenueOnly;
    }

    public String getPriceSource(String priceSrcName){
        Map<String,String> priceSources = new HashMap<String, String>(  );
        Properties priceSrcProperties = getPropertiesWithPrefix( PRICE_SOURCES_STRING );
        for(Map.Entry entry : priceSrcProperties.entrySet()){
                String key = (String)entry.getKey();
                String value = (String)entry.getValue();
                priceSources.put( key.substring( PRICE_SOURCES_STRING.length() + 1 ),value );
            }
        Object src = priceSources.get( priceSrcName );
        if(src == null){
                src = priceSources.get( DEFAULT_SRC );
            }
        if(src != null){
                return (String)src;
            }
        return null;
    }
}
