package com.integral.staging.oms;

import java.util.Map;

import com.integral.is.ISCommonConstants;
import com.integral.model.dealing.OrderRequest;
import com.integral.oms.OMSEnumUtil;
import com.integral.oms.OrderAction;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.staging.Order;
import com.integral.staging.OrderBatch;
import com.integral.staging.oms.config.OMSConfig;
import com.integral.staging.oms.model.OMSOrderListParams;
import com.integral.staging.oms.model.OMSOrderParam;
import com.integral.user.Organization;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 6/19/15
 */
public class OrderValidator {
	
	
	public static String validateMassOrders( OMSOrderListParams omsOrderListParams )
	{

		StringBuilder errorMsg = new StringBuilder();
		for ( OMSOrderParam omsOrderParam : omsOrderListParams.getOrderListParams() )
		{
			String errors = validateDraft(omsOrderParam);
			if ( errors != null )
			{
				if (omsOrderParam.getClientOrderId() != null) {
					errorMsg.append(omsOrderParam.getClientOrderId()).append(" ");
				}
				errorMsg.append(errors).append(" ");
			}
		}

		if ( errorMsg.length() > 0 ) 
		{
			return errorMsg.toString();
		}

		return null;

	}
	
	public static String valiidateBatchOrders( OrderBatch orderBatch, OrderAction orderAction )
	{
		String errors = null;
		if ( orderAction != null )
		{

			for ( Order order : orderBatch.getOrderList() )
			{
				switch ( orderAction )
				{
				case ACTIVATE :
					errors = validateActive(order);
					break;
				case AUTOEXECUTE :
					errors = validateAutoOrder(order);
					break;
				default : //Do nothing	
				}
			}

		}
		return errors;
	}
	
	
    public static String validateDraft(OMSOrderParam param){
        StringBuilder sb = new StringBuilder();
        if(param.getCcyPair() == null) sb.append("Currency Pair should be specified. ");
        else if(param.getDealtCcy() == null) sb.append("Dealt Currency should be specified. ");
        else if(!param.getCcyPair().contains(param.getDealtCcy())) sb.append("Dealt Currency should be component of Currency Pair. ");
        if(param.getSide() == null) sb.append("Buy/Sell side should be specified. ");
        if(param.getOrderType() == null) sb.append("Order Type should be specified. ");
        else if(!OMSEnumUtil.contains(OrderRequest.Type.class, param.getOrderType())) sb.append("Invalid Order Type. ");
		validateOrderPrice(param.getOrderType(), param.getOrderPrice(), sb);
        if(sb.length() > 0) return sb.toString();
        else return null;
    }

	private static void validateOrderPrice(String orderType, Double orderPrice, StringBuilder sb) {
		if(!(orderType.equals(OrderRequest.Type.MARKET.toString()) || orderType.equals(OrderRequest.Type.FIXING.toString())) && orderPrice == null){
			sb.append("Order Price should be specified. ");
		}
	}

    public static String validateActive(Order order){
        StringBuilder sb = new StringBuilder();
        if(order.getDealtAmt() == null) sb.append("Dealt Amount should be specified. ");
		validateOrderPrice(order.getType().toString(), order.getPrice(), sb);
        //if(order.getCounterPartyA() == null) sb.append("Customer Account should be specified. ");
        //if(order.getCounterPartyAOrg() == null) sb.append("Customer Name should be specified. ");
        if(order.getCounterPartyAOrg() != null)  {
        	String error = validateOrganization(order.getCounterPartyAOrg());
        	if(error!=null) {
        		sb.append(error);
        	}
        }
        if(order.getTif() == null) sb.append("TIF should be specified. ");
        else if(order.getTif().equals(Order.TIF.GTD) && order.getExpiryTime() == null) sb.append("Expiry Time should be specified for GTD Orders. ");
        if(sb.length() > 0) return sb.toString();
        else return null;
    }
    

    
    public static String validateExecuteOrder(Order order){
        StringBuilder sb = new StringBuilder();
        if(order.getDealtAmt() == null) sb.append("Dealt Amount should be specified. ");
        if(order.getFilledPrice() == null) sb.append("Fill Price should be specified. ");
		validateOrderPrice(order.getType().toString(), order.getPrice(), sb);
        if(order.getCounterPartyAOrg() == null)  {
        	sb.append("Customer Name should be specified. ");
        }
    	else {
			String error = validateOrganization(order.getCounterPartyAOrg());
			if ( error != null ) {
				sb.append(error);
			}
		}
        if(order.getCounterPartyA() == null) sb.append("Customer Account should be specified. ");
        if(order.getTif() == null) sb.append("TIF should be specified. ");
        else if(order.getTif().equals(Order.TIF.GTD) && order.getExpiryTime() == null) sb.append("Expiry Time should be specified for GTD Orders. ");
        if(sb.length() > 0) return sb.toString();
        else return null;
    }
    
    public static String validateAutoOrder(Order order){
        StringBuilder sb = new StringBuilder();
        if(order.getDealtAmt() == null) sb.append("Dealt Amount should be specified. ");
		validateOrderPrice(order.getType().toString(), order.getPrice(), sb);
        if(order.getCounterPartyAOrg() == null) {
        	sb.append("Customer Name should be specified. ");
		}
		else {
			String error = validateOrganization(order.getCounterPartyAOrg());
			if ( error != null ) {
				sb.append(error);
			}else{
				error = validateSDOrgAndFIOrgRelationship(order.getCounterPartyAOrg(), order.getCounterPartyBOrg());
				if ( error != null ) {
					sb.append(error);
				}
			}
		}
        if(order.getCounterPartyA() == null) sb.append("Customer Account should be specified. ");
        if(order.getTif() == null) sb.append("TIF should be specified. ");
        else if(order.getTif().equals(Order.TIF.GTD) && order.getExpiryTime() == null) sb.append("Expiry Time should be specified for GTD Orders. ");
        if(sb.length() > 0) return sb.toString();
        else return null;
    }
    
	public static String validateOrganization( String orgStr )
	{
		Organization org = null;
		try {
			org = ReferenceDataCacheC.getInstance().getOrganization(orgStr);
		}
		catch ( Exception e ) {

		}
		if ( org == null ) {
			return OMSConstants.ORG_NOT_VALID;
		}
		return null;
	}
	
	public static String validateSDOrgAndFIOrgRelationship(String fiOrgStr, String omsOrgStr){
		
		String salesDealerOrg = OMSConfig.getInstance().getSalesDealerOrg(omsOrgStr);
		if ( salesDealerOrg == null )
		{
			return " Sales Dealer org not defined for OMS org: "+ omsOrgStr;
		}
		Organization sdOrg = ReferenceDataCacheC.getInstance().getOrganization(salesDealerOrg);
		Organization fiOrg = ReferenceDataCacheC.getInstance().getOrganization(fiOrgStr);
		if (!sdOrg.getRelatedOrganizations(ISCommonConstants.LP_ORG_RELATIONSHIP).contains(fiOrg)) {
			return " Failed Auto submit. "+fiOrg.getShortName() +" relationship missing with system "+sdOrg.getShortName();
		}		
		return null;
	}
    
    public static String validateTrigger(Order order ,Map<String, Object> context){
        StringBuilder sb = new StringBuilder();
		if ( context != null && context.get("fillRate") != null ) {
			try {
				Double fillRate = (Double)context.get("fillRate");
			}
			catch (Exception e) {
				sb.append("Invalid Fill Rate . ");
			}
		}
		else
		{
			sb.append("Fill Rate missing. ");
		}
		if(sb.length() > 0) return sb.toString();
        else return null;
    }
}
