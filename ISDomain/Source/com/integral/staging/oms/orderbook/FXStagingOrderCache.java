package com.integral.staging.oms.orderbook;


import java.util.concurrent.ConcurrentHashMap;

import com.integral.oms.spaces.fx.esp.FXStagingOrder;

/**
 * Cache for holding {@link com.integral.oms.spaces.fx.esp.FXStagingOrder} objects
 */
public class FXStagingOrderCache {
    private static final ConcurrentHashMap<String,FXStagingOrder> fxOrderMap = new ConcurrentHashMap<String, FXStagingOrder>( 100 );

    public static void add(FXStagingOrder order){
        fxOrderMap.putIfAbsent( order.getOrderId(),order );
    }

    public static FXStagingOrder remove( String orderId){
        return fxOrderMap.remove( orderId );
    }

    public static FXStagingOrder get(String orderId){
        return fxOrderMap.get( orderId );
    }

    public static String getInfo(){
        StringBuilder sb = new StringBuilder( 50 );
        sb.append( '{' );
        sb.append( "\"fxOrderMap.size\" : \"" ).append( fxOrderMap.size() ).append( "\"" );
        sb.append( '}' );
        return sb.toString();
    }
}
