package com.integral.staging.oms.orderbook;

import com.integral.is.oms.OrderBook;
import com.integral.is.oms.OrderBookCache;
import com.integral.is.oms.OrderBookCacheC;
import com.integral.is.oms.OrderBuilder;
import com.integral.is.oms.OrderExecutionDetails;
import com.integral.is.oms.OrderExecutionReport;
import com.integral.is.oms.OrderFactory;
import com.integral.is.oms.OrderSnapshot;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.oms.spaces.fx.esp.FXStagingOrder;
import com.integral.persistence.Namespace;
import com.integral.staging.Order;
import com.integral.staging.OrderStatusType;
import com.integral.staging.oms.OMSConstants;
import com.integral.staging.oms.OMSOrderService;
import com.integral.staging.oms.model.OMSOrderParam;
import com.integral.util.IdcUtilC;
import com.integral.util.MathUtilC;

public class FXStagingOrderBookManager
{
	private static Log log = LogFactory.getLog(FXStagingOrderBookManager.class);

    private static OrderBuilder orderBuilder = new OrderBuilder();
	
	public static boolean submitOrderToOrderBook( Order order )
	{
		try
		{
			com.integral.is.oms.Order emsOrder = orderBuilder.newOrder(order);
			OrderBookCache cache = OrderBookCacheC.getInstance();
			cache.submitOrder(emsOrder, false);
			log.info(OMSConstants.OMS_LOG_PREFIX + ".submitOrder : Staging order submitted to TriggerOrderBook. ID:" + order.get_id());
		}
		catch ( Exception e )
		{
			log.error(OMSConstants.OMS_LOG_PREFIX + ".submitOrder : Error while submitting order to TriggerOrderBook", e);
			return false;
		}
		return true;

	}
	
	public static boolean triggerStagingOrder( String orderId, Namespace namespace, double fillQty, double fillPrice , long triggerTime)
	{
		
		OMSOrderParam param = new OMSOrderParam();
		param.setFillPrice(fillPrice);
		param.setMatchedPrice(fillPrice);
		param.setOrderState(OrderStatusType.TRIGGERED.name());
		param.setTriggerTime(triggerTime);
		//param.setFilledAmount(fillQty);
		//passing action user value null as trigger is automatic
		ErrorMessage errorMsg = OMSOrderService.updateStagingOrder(orderId, namespace, param, null);
		if(errorMsg != null) {
		    log.warn(OMSConstants.OMS_LOG_PREFIX + " .triggerStagingOrder : Failed to update the order to Triggered state");
			return false;
		
		}
		
		return true;
	}
	
	
	public static boolean amendOrder(String orderId , double amendRate) {
		
		log.info(OMSConstants.OMS_LOG_PREFIX + " .amendOrder : Amending price to : " + amendRate + " in TriggerOrderBook for orderId=" + orderId);
		try {
			FXStagingOrder stagingOrder = FXStagingOrderCache.get(orderId);
			if ( stagingOrder == null )
			{
				log.warn(OMSConstants.OMS_LOG_PREFIX + " .updateStagingOrder : Order not found in TriggerOrderBook for orderId=" + orderId);
				return false;
			}
			
			OrderBook book = OrderBookCacheC.getInstance().getOrderBook(stagingOrder);
			stagingOrder.amendRate(amendRate);
			stagingOrder.amendTriggerRate(amendRate);
			
			if(book !=null) {
		         if (stagingOrder.getEntityDescriptor().isBid()) {
		             book.resortBidOrders();
		         }
		         else {
		             book.resortOfferOrders();
		         }
			}
			return true;
		} catch (Exception e) {
			log.error(OMSConstants.OMS_LOG_PREFIX + " .amendOrder : Amending in TriggerOrderBook failed for  for orderId=" + orderId);
			return false;
		}
		
	}
	
	public static boolean withdrawOrder(String orderId)
	{
		try
		{
			FXStagingOrder stagingOrder = FXStagingOrderCache.remove(orderId);
			if ( stagingOrder == null )
			{
				log.info(OMSConstants.OMS_LOG_PREFIX + ".withdrawOrder : Staging order not found in cache " + orderId);
			}
			else
			{
				OrderBookCache cache = OrderBookCacheC.getInstance();
				OrderSnapshot snapshot = cache.cancelOrder(stagingOrder, true);
				if(snapshot != null) {
					log.info(OMSConstants.OMS_LOG_PREFIX + ".withdrawOrder : Staging order withdrawn from TriggerOrderBook. ID :" + orderId);
				}
			}

		}
		catch ( Exception e )
		{
			log.error(OMSConstants.OMS_LOG_PREFIX + ".withdrawOrder : Error while withdrawing order from TriggerOrderBook", e);
			return false;
		}

		return true;
	}
	
	/**
	 * Send dummy Execution to complete the order
	 * @param order
	 * @param orderExecutionDetails
	 */
	public static OrderSnapshot executeOrderWithDummyFill( com.integral.is.oms.Order order, OrderExecutionDetails orderExecutionDetails )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + ".eOWDF :Send Dummy Execution to Complete the Order " + order.getOrderId());
		String orderId = order.getOrderId();
		double executedAmount = order.getOrderAmount();
		OrderExecutionReport oer = OrderFactory.newOrderExecutionReport();
		oer.setVerified();
		oer.setOrderId(orderId);
		oer.setOrder(order);
		oer.setCounterOrderId(orderExecutionDetails.getMatchedOrderID());
		oer.setExecutedAmount(executedAmount);
		oer.setExecutedPrice(orderExecutionDetails.getExecutionPrice());
		if ( order.getEntityDescriptor().isDealingInTerm() )
		{
			oer.setExecutedSettledAmount(MathUtilC.correctFloatingPointsCalculationPrecision(executedAmount / orderExecutionDetails.getExecutionPrice()));
		}
		else
		{
			oer.setExecutedSettledAmount(MathUtilC.multiply(executedAmount, orderExecutionDetails.getExecutionPrice()));
		}
		OrderSnapshot orderSnapshot = OrderBookCacheC.getInstance().updateOrder(oer, false);

		return orderSnapshot;
	}
	
	
}
