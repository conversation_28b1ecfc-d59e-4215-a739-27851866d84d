package com.integral.staging.oms;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import com.integral.finance.currency.CurrencyPair;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.map.annotate.JsonSerialize.Inclusion;

import com.integral.commons.Tuple;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.ContingencyParameter;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.marketData.fx.FXMarketDataSetC;
import com.integral.finance.price.fx.FXPrice;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.message.MessageFactory;
import com.integral.is.oms.OMSBrokerRateSubscriptionManagerC;
import com.integral.is.oms.OMSBrokerRateSubscriptionManagerC.OMSBrokerSubscriptionRateHandler;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.math.MathUtil;
import com.integral.message.ErrorMessage;
import com.integral.message.MessageStatus;
import com.integral.model.dealing.OrderRequest.Type;
import com.integral.persistence.Namespace;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.staging.Order;
import com.integral.staging.OrderStagingServiceFactory;
import com.integral.staging.StageOrderIdProvider;
import com.integral.staging.StagingAreaPersistenceService;
import com.integral.staging.oms.audit.OMSAuditEvent;
import com.integral.staging.oms.client.socketio.OMSResponse;
import com.integral.staging.oms.config.OMSConfig;
import com.integral.staging.oms.model.OMSContigencyParameter;
import com.integral.staging.oms.model.OMSOrderParam;
import com.integral.staging.oms.model.OMSState;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.server.VirtualServerTypeC;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.util.StringUtilC;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 4/15/15
 */
public class OMSUtil

{
	private static final Log log = LogFactory.getLog(OMSUtil.class);
	private static ObjectMapper objMapper = new ObjectMapper();

	static {
		objMapper.setSerializationInclusion(Inclusion.NON_EMPTY);
	}
	public static boolean in( String input, String... collection )
	{
		if ( input == null )
			return false;
		for ( String str : collection )
		{
			if ( input.equals(str) )
				return true;
		}
		return false;
	}
	
    public static boolean isOMSServerDeployment() {

        String s = ConfigurationFactory.getServerMBean().getVirtualServerType();
        return s != null && s.equals( VirtualServerTypeC.OMSServer );
    }
    
	public static Collection<Organization> getDeployedOMSOrgs()
	{
		Collection<Organization> omsOrgs = new HashSet<Organization>();
		for ( String omsOrgStr : OMSConfig.getInstance().getOmsOrgs() ) {
			Organization omsOrg = ISUtilImpl.getInstance().getOrg(omsOrgStr);
			if ( omsOrg != null ) {
				omsOrgs.add(omsOrg);
			}
		}
		return omsOrgs;
	}
	
	public static Tuple<Boolean, Organization> getOMSOrg( User loggedInUser )
	{
		Organization userOrg = loggedInUser.getOrganization();
		Organization brokerOrg = userOrg.getBrokerOrganization();
		if ( !userOrg.isBroker() && brokerOrg != null && OMSConfig.getInstance().getOmsOrgs().contains(brokerOrg.getShortName()) )
		{
			log.info((new StringBuilder(OMSConstants.OMS_LOG_PREFIX).append(".getOMSOrg ").append(" Customer login")).toString());
			return new Tuple(Boolean.TRUE, brokerOrg);
		}

		return new Tuple(Boolean.FALSE, loggedInUser.getOrganization());
	}
	
	public static OMSAuditEvent getOMSEvent( OMSResponse response, String org, String actionUser, String actionOrg )
	{
		OMSAuditEvent omsEvent = new OMSAuditEvent();
		omsEvent.setOrderId(response.getOrderId());
		omsEvent.setEventMsg(response.getResponseString());
		omsEvent.setEvent(response.getEvent().getCode());
		omsEvent.setNamespaceName(org);
		omsEvent.setTimeStamp(System.currentTimeMillis());
		omsEvent.setActionUser(actionUser);
		omsEvent.setActionOrg(actionOrg);
		if ( response.getProperties() != null )
		{
			if ( response.getProperties().get(OMSResponse.OLD_STATE) != null )
			{
				omsEvent.setOldState(response.getProperties().get(OMSResponse.OLD_STATE));
			}
			if ( response.getProperties().get(OMSResponse.NEW_STATE) != null )
			{
				omsEvent.setNewState(response.getProperties().get(OMSResponse.NEW_STATE));
			}
		}
		if ( response.getOrderParams() != null )
		{
			try
			{
				String orderStr = objMapper.writeValueAsString(response.getOrderParams());
				omsEvent.setOrder(orderStr);
			}
			catch ( IOException e )
			{
				log.error(new StringBuilder(OMSConstants.OMS_LOG_PREFIX).append(".getOMSEvent order: ").append("Error while parsing OMS order param").append(response.getOrderParams()).toString());
			}

		}
		return omsEvent;
	}
	
	public static List<Order> getLinkedOrders( Order primaryOrder )
	{
		List<Order> linkedOrders = new ArrayList<Order>();
		if ( primaryOrder.getOrderContingencies() != null && !primaryOrder.getOrderContingencies().isEmpty() )
		{
			for ( ContingencyParameter cp : primaryOrder.getOrderContingencies() )
			{
				for ( String linkedOrderId : cp.getLinkedOrderIds() )
				{
					if ( linkedOrderId.equals(primaryOrder.get_id()) )
					{
						continue;
					}
					Order linkedOrder = OrderStagingServiceFactory.getOrderStagingService().getOrder(linkedOrderId, primaryOrder.getNamespace());
					if ( linkedOrder == null )
					{
						log.warn(new StringBuilder(OMSConstants.OMS_LOG_PREFIX).append(".getLinkedOrders order: ").append("Linked order not found linkedOrderId : ").append(linkedOrderId).toString());
						continue;
					}					
					linkedOrders.add(linkedOrder);
				}

			}
		}
		return linkedOrders;
	}
	

	
	
	public static void updateCustomerDetails(OMSOrderParam params) throws IllegalArgumentException
	{
		if ( params.getCptyAOrg() != null )
		{
			Organization cptyAOrg = ReferenceDataCacheC.getInstance().getOrganization(params.getCptyAOrg());
			if ( cptyAOrg == null )
			{
				log.warn(OMSConstants.OMS_LOG_PREFIX + " .getCustomerDetails : Org is invalid for order: " + params.getOrderId());
				throw new IllegalArgumentException("Org " + params.getCptyAOrg() + "  is invalid for order: " + params.getOrderId());
			}
			else
			{
				log.info(OMSConstants.OMS_LOG_PREFIX + " .getCustomerDetails : Setting Default LE for orderId :" + params.getOrderId());
				params.setCptyA(cptyAOrg.getDefaultDealingEntity().getShortName());
			}
		}

	}
	
	
	public static Order updateOrderParams(Order order , Map<String, Object> updatedParams ) throws IllegalArgumentException{
		try
		{
			if ( updatedParams != null )
			{
				if ( updatedParams.get("cptyA") != null )
				{
					order.setCounterPartyA((String) updatedParams.get("CptyA"));
				}
				if ( updatedParams.get("cptyAOrg") != null )
				{
					String cptyAOrgStr = (String) updatedParams.get("CptyAOrg");
					order.setCounterPartyAOrg(cptyAOrgStr);

					Organization cptyAOrg = ReferenceDataCacheC.getInstance().getOrganization(cptyAOrgStr);
					if ( cptyAOrg == null )
					{
						log.warn(OMSConstants.OMS_LOG_PREFIX + " .getCustomerDetails : Org is invalid for order: " + order.getId());
						throw new IllegalArgumentException("Org " + cptyAOrgStr + "  is invalid for order: " + order.getId());
					}
					else
					{
						log.info(OMSConstants.OMS_LOG_PREFIX + " .getCustomerDetails : Setting Default LE for orderId :" + order.getId());
						order.setCounterPartyA(cptyAOrg.getDefaultDealingEntity().getShortName());
					}
				}
				if ( updatedParams.get("expiryTime") != null )
				{
					order.setExpiryTime(Long.valueOf((String) updatedParams.get("expiryTime")));
				}
				if ( updatedParams.get("notes") != null )
				{
					order.setNotes((String) updatedParams.get("notes"));
				}
				if ( updatedParams.get("custNotes") != null )
				{
					order.setCustNotes((String) updatedParams.get("custNotes"));
				}
				if ( updatedParams.get("dealtAmt") != null )
				{
					order.setDealtAmt(Double.valueOf((String) updatedParams.get("dealtAmt")));
				}
				if ( updatedParams.get("fillPrice") != null )
				{
					String fillPriceStr = (String)updatedParams.get("fillPrice");
					if(!fillPriceStr.equals("")) {
						order.setFilledPrice(Double.valueOf(fillPriceStr));
					}else {
						order.setFilledPrice(null);
					}
				}
				if ( updatedParams.get("filledAmount") != null )
				{
					order.setFilledAmount(Double.valueOf((String) updatedParams.get("filledAmount")));
				}
				if ( updatedParams.get("orderPrice") != null )
				{
					order.setPrice(Double.valueOf((String) updatedParams.get("orderPrice")));
				}
				if ( updatedParams.get("matchedPrice") != null )
				{
					order.setMatchedPrice(Double.valueOf((String) updatedParams.get("matchedPrice")));
				}
				if ( updatedParams.get("triggerTime") != null )
				{
					order.setTriggerTime(Long.valueOf((String)updatedParams.get("triggerTime")));
				}
				if ( updatedParams.get("orderState") != null )
				{
					int state = OMSState.fromOMSState((String) updatedParams.get("orderState")).code;
					if ( state != order.getState() )
					{
						order.setState(state);
					}
				}
				if ( updatedParams.get("tradeId") != null )
				{
					order.setTradeId((String) updatedParams.get("tradeId"));
				}
				String correlationId = order.getOrderBatchId();
				if ( correlationId == null )
					correlationId = order.get_id();
			}
		}catch (Exception e) {
			throw new IllegalArgumentException("Invalid updateParams order: " + order.getId());
		}
        
        return order;
	}
	
	
	public static Order updateOrderParams(Order order , OMSOrderParam param ) {
		if(param != null) {
			String orderId = param.getOrderId();
			
			if(param.getCptyA() != null) order.setCounterPartyA(param.getCptyA());
	        if(param.getCptyAOrg() != null) order.setCounterPartyAOrg(param.getCptyAOrg());
	        if(param.getOrderPrice() != null) order.setPrice(param.getOrderPrice());
			if ( param.getDealtAmt() != null ) {
	        	order.setDealtAmt(param.getDealtAmt());
			}
			else if ( param.getBaseAmt() != null && order.getPrice() != null ) {
				if ( order.isDealtInBase() ) {
					order.setDealtAmt(param.getBaseAmt());
	        	} else {
					order.setDealtAmt(param.getBaseAmt() * order.getPrice());
	        	}
	        }
	        if(param.getExpiryTime() != null) order.setExpiryTime(param.getExpiryTime());
	        if( param.getFillPrice()!=null) {
				if ( Double.compare(param.getFillPrice(), -1.0) == 0 ) {
	        		order.setFilledPrice(null);
	        	} else {
	        		order.setFilledPrice(param.getFillPrice());
	        		
	        	}
	        }
	        if(param.getFilledAmount() != null) order.setFilledAmount(param.getFilledAmount());
	        if(param.getLockedByUser() != null) order.setLockedByUser(param.getLockedByUser());
	        if(param.getNotes() != null) order.setNotes(param.getNotes());
	        if(param.getCustNotes() != null) order.setCustNotes(param.getCustNotes());
	        if(param.getMatchedPrice() != null) order.setMatchedPrice(param.getMatchedPrice());
	        if(param.getOrderType() != null) order.setType(Type.valueOf(param.getOrderType()));
	        //if(param.getTif()!=null) order.setTif(Order.TIF.valueOf(param.getTif()));
	        if(param.getTriggerTime() != null) order.setTriggerTime(param.getTriggerTime());
			/*if ((Order.TIF.GTD == order.getTif()) && param.getExpiryTime() != null ) {
				order.setExpiryTime(param.getExpiryTime());
				OrderExpiryManager.scheduleExpiry(order);
			}*/
			if ( param.getOrderState() != null ) {
				int state = OMSState.fromOMSState(param.getOrderState()).code;
				if(state != order.getState()) {
					order.setState(state);
				}
			}
			if ( param.getTradeId() != null )
			{
				order.setTradeId(param.getTradeId());
			}
			order.setVenueBMR(param.getVenueBMR());
	        String correlationId = order.getOrderBatchId();
	        if(correlationId == null) correlationId = orderId;
		}
        
        return order;
	}

	public static Order constructOrder( OMSOrderParam orderParam ,User user)
	{
		if ( orderParam == null )
			return null;
		Order order = new Order();
		order.setOmsOrder(true);
		order.setSource("GridAPI");
		order.setUser(user);
		order.setUserName(user.getShortName());
		order.setNamespace(user.getNamespace());
		order.setNamespaceName(user.getNamespace().getShortName());
		long time = System.currentTimeMillis();
		order.setCreatedBusinessDate(time);
		order.setCreatedTime(time);
		order.setModifiedTime(time);
		order.set_id(StageOrderIdProvider.getInstance().nextId());

		order.setClientOrderId(orderParam.getClientOrderId());
		order.setCcyPair(orderParam.getCcyPair());
		order.setDealtCcy(orderParam.getDealtCcy());
		if ( "BUY".equalsIgnoreCase(orderParam.getSide()) )
			order.setBuy(true);
		order.setDealtAmt(orderParam.getDealtAmt());
		Type orderType = Type.valueOf(orderParam.getOrderType());
		order.setType(orderType);
		order.setCustOrder(orderParam.isCustOrder());
		order.setPrice(orderParam.getOrderPrice());
		order.setFilledPrice(orderParam.getFillPrice());
		order.setCounterPartyA(orderParam.getCptyA());
		order.setCounterPartyAOrg(orderParam.getCptyAOrg());
		Organization cptyBOrg = user.getOrganization();
		order.setCounterPartyBOrg(cptyBOrg.getShortName());
		order.setTif(Order.TIF.valueOf(orderParam.getTif()));
		order.setExpiryTime(orderParam.getExpiryTime());
		order.setNotes(orderParam.getNotes());
		order.setCustNotes(orderParam.getCustNotes());
		order.setState(OMSState.fromOMSState(orderParam.getOrderState()).code);
		order.setLockedByUser(orderParam.getLockedByUser());
		order.setOrderBatchId(orderParam.getPortfolioId());
		order.setMatchedPrice(orderParam.getMatchedPrice());
		order.setOrderContingencies(getContingencyParams(orderParam.getContigencyParameters()));
		order.setPrimaryOrder(orderParam.isPrimaryOrder());
		order.setToOrg(orderParam.getToOrg());
		order.setFromOrg(orderParam.getFromOrg());
		order.setAcceptedForPassing(orderParam.isAcceptedForPass());
		order.setSentForPassing(orderParam.isSentForPass());
		order.setEmsOrderId(orderParam.getEmsOrderId());
		if(orderParam.isEmsOrder()){
			order.setEMSOrder(true);
		}
		order.setFixingReference(orderParam.getFixingReference());
		order.setFixingTime(orderParam.getFixingTime());
		return order;
	}
	
	private static List<com.integral.finance.dealing.ContingencyParameter> getContingencyParams(List<OMSContigencyParameter> cParams)
    {
		List<com.integral.finance.dealing.ContingencyParameter> cps = new ArrayList<com.integral.finance.dealing.ContingencyParameter>();
    	if( cParams != null && !cParams.isEmpty() )
    	{
    		for( OMSContigencyParameter cp : cParams )
    		{
    			com.integral.finance.dealing.ContingencyParameter contParam = new com.integral.finance.dealing.ContingencyParameter();
    			contParam.setType( cp.getType() );
    			contParam.setLinkedOrderIds( cp.getLinkedOrderIds() );
    			contParam.setGroupId( cp.getGroupId() );
    			cps.add(contParam);
    		}
    	}
    	return cps;
    }
	
	public static Double getHomeCcyAmt(User user, Double baseAmt, String baseSide, String currencyPair,
			String homeCurrency) {
		Double homeCcyAmt = null;
		String baseCcy = CurrencyFactory.getBaseCurrency(currencyPair);
		if (baseCcy.equals(homeCurrency)) {
			return baseAmt;
		}
		String baseAndHomeCcy = baseCcy + "/" + homeCurrency;
		Map<String, Double> marketRateMap = getMarketRates(user, new String[] { baseAndHomeCcy }, "MAINStaticFXMDS");
		Double conversionRate = null;
		if (baseSide == "BUY") {
			if ((conversionRate = marketRateMap.get(baseAndHomeCcy + "/OFFER")) != null) {
				homeCcyAmt = baseAmt * conversionRate;
			}
		} else if ((conversionRate = marketRateMap.get(baseAndHomeCcy + "/BID")) != null) {
			homeCcyAmt = baseAmt * conversionRate;
		}
		return getFormattedAmount(homeCcyAmt);
	}

	public static double getFormattedAmount(double amount)
	{		
		return Double.parseDouble(String.format( "%.2f", amount ));
	}
	
	public static OMSOrderParam convertToParam(User user, Order order )
	{
		OMSOrderParam param = new OMSOrderParam();
		param.setOrderId(order.get_id());
		param.setClientOrderId(order.getClientOrderId());
		param.setCcyPair(order.getCcyPair());
		param.setDealtCcy(order.getDealtCcy());
		param.setDealtAmt(order.getDealtAmt());
		if ( order.getFilledPrice() != null && order.getDealtAmt() != null ) {
			param.setBaseAmt(getFormattedAmount(order.isDealtInBase()? order.getDealtAmt() : (order.getDealtAmt()/order.getFilledPrice())));
			param.setTermAmt(getFormattedAmount(order.isDealtInBase() ? (order.getDealtAmt() * order.getFilledPrice()) : order.getDealtAmt()));
		}else if(order.getPrice() != null && order.getDealtAmt() != null){
			param.setBaseAmt(getFormattedAmount(order.isDealtInBase()? order.getDealtAmt() : (order.getDealtAmt()/order.getPrice())));
			param.setTermAmt(getFormattedAmount(order.isDealtInBase() ? (order.getDealtAmt() * order.getPrice()) : order.getDealtAmt()));
		}
		if(order.isBuy() ) {
			param.setSide("BUY");
			param.setBaseSide(order.isDealtInBase()?"BUY" :"SELL");
		} else {
			param.setSide("SELL");
			param.setBaseSide(order.isDealtInBase()?"SELL" :"BUY");
		}
		
		if (param.getBaseAmt() != null) {
			Namespace namespace = order.getNamespace();
			if (namespace != null) {
				Organization org = ReferenceDataCacheC.getInstance().getOrganization(namespace);
				LegalEntity lt = org.getDefaultDealingEntity();
				if (lt != null) {
					if (lt.getBusinessCenter() != null && lt.getBusinessCenter().getReportingCurrency()!=null) {
						Currency homeCurrency = lt.getBusinessCenter().getReportingCurrency();
						param.setHomeCcyAmt(getHomeCcyAmt(user, param.getBaseAmt(), param.getBaseSide(),
								param.getCcyPair(), homeCurrency.getShortName()));
					}
				}
			}
		}	
		param.setCustOrder(order.isCustOrder());		
		param.setOrderType(order.getType().name());
		param.setOrderPrice(order.getPrice());
		param.setCptyA(order.getCounterPartyA());
		param.setCptyAOrg(order.getCounterPartyAOrg());
		param.setTif(order.getTif().name());
		param.setExpiryTime(order.getExpiryTime());
		param.setOrderState(OMSState.fromStatusCode(order.getState()).toString());
		param.setFillPrice(order.getFilledPrice());
		param.setMatchedPrice(order.getMatchedPrice());
		param.setFilledAmount(order.getFilledAmount());
		param.setLockedByUser(order.getLockedByUser());
		param.setNotes(order.getNotes());
		param.setCustNotes(order.getCustNotes());
		param.setPortfolioId(order.getOrderBatchId());
		param.setTriggerTime(order.getTriggerTime());
		param.setTradeId(order.getTradeId());
		param.setContigencyParameters(toOMSContingencyParams(order.getOrderContingencies()));
		param.setPrimaryOrder(order.isPrimaryOrder());
		param.setToOrg(order.getToOrg());
		param.setFromOrg(order.getFromOrg());
		param.setAcceptedForPass(order.isAcceptedForPassing());
		param.setSentForPass(order.isSentForPassing());
		param.setEmsOrderId(order.getEmsOrderId());
		param.setCptyBOrg(order.getCounterPartyBOrg());
		param.setEmsOrder(order.isEMSOrder());
		param.setFixingReference(order.getFixingReference());
		param.setFixingTime(order.getFixingTime());
		param.setFixingDate(order.getFixingTime());
		param.setTransactionId(order.getTransactionId());
		param.setVenueBMR(order.getVenueBMR());
		return param;
	}

	
	public static List<OMSContigencyParameter> toOMSContingencyParams(Collection<ContingencyParameter> cParams)
    {
		List<OMSContigencyParameter> cps = new ArrayList<OMSContigencyParameter>();
    	if( cParams != null && !cParams.isEmpty() )
    	{
    		for( ContingencyParameter cp : cParams )
    		{
    			OMSContigencyParameter contParam = new OMSContigencyParameter();
    			contParam.setType( cp.getType() );
    			contParam.setLinkedOrderIds( cp.getLinkedOrderIds() );
    			contParam.setGroupId( cp.getGroupId() );
    			cps.add(contParam);
    		}
    	}
    	return cps;
    }


	public static Map<String, Double> getMarketRates(User user, String[] currencyPairs, String mdsName )
	{
   		if(user != null && !user.hasPermission(OMSConstants.OMS_PERMISSION_TRADER_USER) && !user.hasPermission(OMSConstants.OMS_PERMISSION_CHIEF_DEALER_USER)) {
			return null;
		}
   		String brokerShortName = null;
   		Organization brokerOrg = null;
		if (user != null) {
			brokerOrg = user.getOrganization().getBrokerOrganization();
			if (brokerOrg == null && OMSConfig.getInstance().getOmsOrgs().contains(user.getOrganization().getShortName())) {
				brokerShortName = user.getOrganization().getShortName();
			}
			else if((brokerOrg != null && OMSConfig.getInstance().getOmsOrgs().contains(brokerOrg.getShortName()))){
				brokerShortName = brokerOrg.getShortName();
			}
		}
		Map<String, Double> marketRateMap = null;
   		if(brokerShortName != null && OMSConfig.getInstance().isMarketRateFromBrokersOrderBookEnabled(brokerShortName)){
   			marketRateMap =  ((OMSBrokerSubscriptionRateHandler)OMSBrokerRateSubscriptionManagerC.getInstance().getRateHandler()).getBrokerBestBidAndOfferRate(brokerShortName, currencyPairs);
   		}
   		if(marketRateMap == null){
   			marketRateMap = new HashMap<String, Double>();
   		}
		if(mdsName == null){
			mdsName = "LiveFXMDS";
		}
		FXMarketDataSet fxMarketDataSet = getMarketDataSet(mdsName);
		if ( fxMarketDataSet == null )
			return marketRateMap;
		for ( String currencyPair : currencyPairs )
		{
			if ( currencyPair.length() != 7 )
				continue;
			if (marketRateMap.get(currencyPair + "/BID") == null || marketRateMap.get(currencyPair + "/OFFER") > 100000
					|| marketRateMap.get(currencyPair + "/BID") > 100000) {
				CurrencyPair ccyPair = CurrencyFactory.getCurrencyPairFromString(currencyPair);
				Currency currency1 = ccyPair != null ? ccyPair.getBaseCurrency() : null;
				Currency currency2 = ccyPair != null ? ccyPair.getVariableCurrency() : null;
				FXMarketDataElement fxMarketDataElement = fxMarketDataSet.findSpotConversionMarketDataElement(currency1,
						currency2, false);
				if (fxMarketDataElement == null)
					fxMarketDataElement = fxMarketDataSet.findSpotConversionMarketDataElement(currency1, currency2,
							true);
				if (fxMarketDataElement == null)
					continue;
				FXPrice fxPrice = fxMarketDataElement.getFXPrice();
				if (!fxPrice.getBaseCurrency().isSameAs(currency1)) {
					fxPrice = fxPrice.getInverted();
				}
				double bidRate = fxPrice.getBidFXRate().getRate();
				double offerRate = fxPrice.getOfferFXRate().getRate();
				if (user != null) {
					FXRateConvention rateConvention = QuoteConventionUtilC.getInstance()
							.getFXRateConvention(user.getOrganization());
					FXRateBasis rateBasis = rateConvention.getFXRateBasis(currency1, currency2);
					if (rateBasis != null) {
						int precision = rateBasis.getSpotPrecision();
						offerRate = MathUtil.round(offerRate, precision, BigDecimal.ROUND_CEILING);
						bidRate = MathUtil.round(bidRate, precision, BigDecimal.ROUND_FLOOR);
					}
				}
				marketRateMap.put(currencyPair + "/BID", bidRate);
				marketRateMap.put(currencyPair + "/OFFER", offerRate);
			}
		}
		return marketRateMap;
	}

	public static FXMarketDataSet getMarketDataSet( String mdsName )
	{
		return (FXMarketDataSet) ReferenceDataCacheC.getInstance().getEntityByShortName(mdsName, FXMarketDataSetC.class, UserFactory.getNamespace("MAIN"), 'A');
	}
	
	public static boolean isSuccess( ErrorMessage errorMessage )
	{
		return errorMessage == null || errorMessage.getStatus() == MessageStatus.SUCCESS;
	}

	public static ErrorMessage createError( String msg )
	{
		log.warn(msg);
		ErrorMessage message = MessageFactory.newErrorMessage();
		message.setStatus(MessageStatus.FAILURE);
		message.setCode(msg);
		return message;
	}

	public static ErrorMessage createSuccess()
	{
		ErrorMessage message = MessageFactory.newErrorMessage();
		message.setStatus(MessageStatus.SUCCESS);
		return message;
	}

	public static String checkOMSOrgConfiguration( String omsOrgStr, String omsUserStr )
	{
		StringBuilder strBuilder = new StringBuilder(500);
		if ( !OMSConfig.getInstance().isSocketServerEnabled() ) {
			strBuilder.append("Socket Server is not enabled on this virtual Server.Property ").append(OMSConfig.OMS_SOCKET_SERVER_ENABLED);
			return strBuilder.toString();
		}
		if ( StringUtilC.isNullOrEmpty(omsOrgStr) ) {
			strBuilder.append(" OMS Org is null or empty ").append(omsOrgStr);
			return strBuilder.toString();
		}
		Organization omsOrg = checkOrgValid(omsOrgStr);
		if ( omsOrg == null ) {
			strBuilder.append("OMS org is not valid or active:").append(omsOrgStr);
			return strBuilder.toString();
		}
		if ( omsOrg.getDefaultDealingUser() == null ) {
			strBuilder.append("OMS org default dealing user is null").append(omsOrgStr);
			return strBuilder.toString();
		}
		if ( !omsOrg.getDefaultDealingUser().hasPermission(OMSConstants.OMS_PERMISSION_TRADER_USER) ) {
			return strBuilder.append("OMS Org Default dealing user ").append(omsOrg.getDefaultDealingUser().getShortName()).append(" does not have permission ").append(OMSConstants.OMS_PERMISSION_TRADER_USER).toString();
		}
    
		if ( omsUserStr != null ) {
			User omsUser = omsOrg.getUser(omsUserStr);
			if ( omsUser == null ) {
				strBuilder.append("OMS user ").append(omsUserStr).append(" is not valid or active in org ").append(omsOrgStr);
				return strBuilder.toString();
			}

			if ( !omsUser.hasPermission("FXIProBookTrade") ) {
				return strBuilder.append("OMS user ").append(omsUserStr).append(" does not have permission ").append("FXIProBookTrade").toString();
			}
			if ( !omsUser.hasPermission(OMSConstants.OMS_PERMISSION_TRADER_USER) ) {
				return strBuilder.append("OMS user ").append(omsUserStr).append(" does not have permission ").append(OMSConstants.OMS_PERMISSION_TRADER_USER).toString();
			}
			if ( !omsUser.hasPermission(OMSConstants.OMS_PERMISSION_CHIEF_DEALER_USER) ) {
				return strBuilder.append("OMS user ").append(omsUserStr).append(" does not have permission ").append(OMSConstants.OMS_PERMISSION_TRADER_USER).toString();
			}
			if ( !omsUser.hasPermission(StagingAreaPersistenceService.stagingAreaCDView) ) {
				return strBuilder.append("OMS user ").append(omsUserStr).append(" does not have permission ").append(StagingAreaPersistenceService.stagingAreaCDView).toString();
			}
		}
		
		List<String> omsOrgs = OMSConfig.getInstance().getOmsOrgs();
		if ( omsOrgs == null || omsOrgs.size() == 0 ) {
			strBuilder.append(OMSConfig.OMS_ORGS).append(" Property is not set or empty");
			return strBuilder.toString();
		}
		if ( !omsOrgs.contains(omsOrgStr) ) {
			strBuilder.append(OMSConfig.OMS_ORGS).append(" Property does not contain org ").append(omsOrgStr);
			return strBuilder.toString();
		}

		String marketMonitorOrgStr = OMSConfig.getInstance().getMarketMonitorOrderBookOrg(omsOrgStr);
		if ( StringUtilC.isNullOrEmpty(marketMonitorOrgStr) ) {
			strBuilder.append(OMSConfig.OMS_MARKET_MONITOR_ORDERBOOK_ORG).append(" Property is not defined or empty for OMS Org:").append(omsOrgStr);
			return strBuilder.toString();
		}
		else {
			Organization marketMonitorOrg = checkOrgValid(marketMonitorOrgStr);
			if ( marketMonitorOrg == null ) {
				strBuilder.append("Market Monitor org is not valid:").append(marketMonitorOrgStr);
				return strBuilder.toString();
			}
			else if ( marketMonitorOrg.getVirtualServer() == null || !ConfigurationFactory.getServerMBean().getVirtualServerName().equals(marketMonitorOrg.getVirtualServer().getShortName()) ) {
				strBuilder.append(marketMonitorOrgStr).append("'s virtual server is not set or same as OMS server");
				return strBuilder.toString();
			}
		}
		String autoSDOrgStr = OMSConfig.getInstance().getSalesDealerOrg(omsOrgStr);
		if ( StringUtilC.isNullOrEmpty(autoSDOrgStr) ) {
			strBuilder.append(OMSConfig.SALES_DEALER_ORG).append(" Property is not defined or empty for OMS Org:").append(omsOrgStr);
			return strBuilder.toString();
		}
		else {
			Organization autoSDOrg = checkOrgValid(autoSDOrgStr);
			if ( autoSDOrg == null ) {
				strBuilder.append("Auto SD org is not valid:").append(autoSDOrgStr);
				return strBuilder.toString();
			}
			else if ( autoSDOrg.getVirtualServer() == null || !ConfigurationFactory.getServerMBean().getVirtualServerName().equals(autoSDOrg.getVirtualServer().getShortName()) ) {
				strBuilder.append(autoSDOrgStr + "'s virtual server is not set or same as OMS server");
				return strBuilder.toString();
			}
			
			if(autoSDOrg.getDefaultDealingUser() == null) {
				strBuilder.append("Auto SD Org ").append(autoSDOrgStr).append(" default dealing user is null").append(omsOrgStr);
				return strBuilder.toString();
			
			}
			if ( !autoSDOrg.getDefaultDealingUser().hasPermission(ISConstantsC.OCO_TRADING_PERM) )
			{
				return strBuilder.append("Auto SD Org Deafault user ").append(autoSDOrgStr).append(" does not have permission ").append(ISConstantsC.OCO_TRADING_PERM).toString();
			}
			if ( !autoSDOrg.getDefaultDealingUser().hasPermission(ISConstantsC.OUO_TRADING_PERM) )
			{
				return strBuilder.append("Auto SD Org Deafault user ").append(autoSDOrgStr).append(" does not have permission ").append(ISConstantsC.OUO_TRADING_PERM).toString();
			}
		}
		
		if(!validateOMSTradeChannelForSTP(omsOrg)) {
			strBuilder.append("OMS org ").append(omsOrgStr).append(" does not contain ").append(OMSConstants.OMS_TRADE_CHANNEL_MANUAL).append(" in supported STP channels");
			return strBuilder.toString();
		}
		return null;
	}
	
	private static Organization checkOrgValid( String orgStr )
	{
		try {
			Organization org = ReferenceDataCacheC.getInstance().getOrganization(orgStr);
			return org;
		}
		catch ( Exception e ) {
		}
		return null;
	}
	
	private static boolean validateOMSTradeChannelForSTP(Organization omsOrg) {
        String includedChannels = omsOrg.getIncludedManualTradeChannelsForStp();
        if(includedChannels == null) includedChannels = "";
        String[] tokens = includedChannels.split("[\\s,;]+");
        for(String token : tokens)
        {
            if(!token.isEmpty() && token.equals(OMSConstants.OMS_TRADE_CHANNEL_MANUAL)) {
            	return true;
            }
        }
        return false;
	}

}
