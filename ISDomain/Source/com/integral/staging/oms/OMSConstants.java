package com.integral.staging.oms;


public class OMSConstants
{
	public static String OMS_SOCKET_EVENT_AUTH = "auth";
	public static String OMS_SOCKET_EVENT_ACTION = "action";
	public static String OMS_SOCKET_EVENT_ALL_ORDERS = "allOrders";
	public static String OMS_SOCKET_EVENT_AGGREGATE_ORDERS = "aggregateOrders";
	public static String OMS_SOCKET_EVENT_CREATE_ORDER = "createOrder";
	public static String OMS_SOCKET_EVENT_NOTIFICATION = "notification";
	public static String OMS_SOCKET_EVENT_MARKET_RATES = "marketRatesInc";
	public static String OMS_SOCKET_FORCE_LOGOUT = "forceLogout";
	public static String OMS_SOCKET_EVENT_MARKET_RATES_FULL_REFRESH = "marketRatesFR";
	public static String OMS_LOG_PREFIX = " [StagingOMS] ";
	public static String OMS_TRADE_CHANNEL_AUTO = "OMS/AUTO";
	public static String OMS_TRADE_CHANNEL_MANUAL = "OMS/MANUAL";
	public static String OMS_PERMISSION_TRADER_USER = "OMSTraderUser";
	public static String OMS_PERMISSION_CHIEF_DEALER_USER = "OMSChiefDealerUser";
	public static String OMS_PERMISSION_ADMIN_USER = "OMSAdminUser";
	public static String OMS_SYSTEM_USER = "System";
	public static String OMS_ACTION_USER = "ActionUser";

    public static final String INSUFFICIENT_PERMISSION = " User does not have OMS Trader Permission";
    public static final String INTERNAL_SERVER_ERROR = " Internal Server Error. Contact Support";
    public static final String OMS_ORDER_NOT_FOUND = " Order not found";
    public static final String OMS_ORG_NOT_CONFIGURED = " OMS Org Not Configured";
    public static final String OMS_DUPLICATE_ORDER_ID = " Duplicate Order Id";
    public static final String OMS_ORDER_ID_MISSING = " Order Id Missing";
    public static final String OMS_ACTION_MISSING = " Action Missing";
    public static final String OMS_USER_NOT_LOGGED_IN = "User not logged in";
    public static final String INVALID_TOKEN = " Invalid Authentitcation Token";
    public static final String ORDER_REQUEST_NOT_FOUND = "Order Request not found";
    public static final String FAILED_TO_SEND_ORDER = "Failed to send message to OMS";
    public static final String ORDER_NOT_FOUND = "ORDER NOT FOUND";
    public static final String ORDER_UPDATE_FAILED = "ORDER UPDATE FAILED";
    public static final String ORG_NOT_VALID = " Organization not Valid";
    public static final String OMS_ORDER_AUTO_ALREADY = " Order is already in auto";
	public static final String TAKE_CREDIT_FAILED = "Take Credit Failed";

}
