package com.integral.staging.oms.model;

import java.util.ArrayList;
import java.util.List;

public class OMSContigencyParameter
{
	int type;

	List<String> linkedOrderIds;

	String groupId;

	/**
	 * @return the type eg. 1 for 'OCO', 2 for 'OTO' etc.
	 */
	public int getType()
	{
		return type;
	}

	/**
	 * @param type the type to set
	 */
	public void setType( int type )
	{
		this.type = type;
	}

	/**
	 * @return linkedOrderIds the list of linked order ids
	 */
	public List<String> getLinkedOrderIds()
	{
		return linkedOrderIds;
	}

	/**
	 * @param add linkedOrderId to the list
	 */
	public void addLinkedOrderIds( String linkedOrderId )
	{
		if ( this.linkedOrderIds == null )
			this.linkedOrderIds = new ArrayList<String>();
		this.linkedOrderIds.add(linkedOrderId);
	}

	/**
	 * @param linkedOrderIds the linkedOrderIds to set
	 */
	public void setLinkedOrderIds( List<String> linkedOrderIds )
	{
		this.linkedOrderIds = linkedOrderIds;
	}

	/**
	 * @return the groupId
	 */
	public String getGroupId()
	{
		return groupId;
	}

	/**
	 * @param groupId the groupId to set
	 */
	public void setGroupId( String groupId )
	{
		this.groupId = groupId;
	}

	@Override
	public String toString()
	{
		return "ContingencyParameter [type=" + type + ", linkedOrderIds=" + linkedOrderIds + ", groupId=" + groupId + "]";
	}
}
