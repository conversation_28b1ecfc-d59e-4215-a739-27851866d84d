package com.integral.staging.oms.model;

public class OMSAggregateOrderParam {

    private String aggregateKey;
    private String fixingAsset;
    private String fixingReference;
    private Long fixingTime;
    private String ccyPair;
    private Double balance;
    private String dealtCcy;
    private String side;
    private int orderCount;
    private Long date;
    private String orderIds;
    private Double fixingRate;
    private Boolean fixingRateAvailable;

    public String getAggregateKey() {
        return aggregateKey;
    }

    public void setAggregateKey(String aggregateKey) {
        this.aggregateKey = aggregateKey;
    }

    public String getFixingAsset() {
        return fixingAsset;
    }

    public void setFixingAsset(String fixingAsset) {
        this.fixingAsset = fixingAsset;
    }

    public String getFixingReference() {
        return fixingReference;
    }

    public void setFixingReference(String fixingReference) {
        this.fixingReference = fixingReference;
    }

    public Long getFixingTime() {
        return fixingTime;
    }

    public void setFixingTime(Long fixingTime) {
        this.fixingTime = fixingTime;
    }

    public String getCcyPair() {
        return ccyPair;
    }

    public void setCcyPair(String ccyPair) {
        this.ccyPair = ccyPair;
    }

    public Double getBalance() {
        return balance;
    }

    public void setBalance(Double balance) {
        this.balance = balance;
    }

    public String getDealtCcy() {
        return dealtCcy;
    }

    public void setDealtCcy(String dealtCcy) {
        this.dealtCcy = dealtCcy;
    }

    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side;
    }

    public int getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(int orderCount) {
        this.orderCount = orderCount;
    }

    public Long getDate() {
        return date;
    }

    public void setDate(Long date) {
        this.date = date;
    }

    public String getOrderIds() {
        return orderIds;
    }

    public void setOrderIds(String orderIds) {
        this.orderIds = orderIds;
    }

    public Double getFixingRate() {
        return fixingRate;
    }

    public void setFixingRate(Double fixingRate) {
        this.fixingRate = fixingRate;
    }

    public Boolean getFixingRateAvailable() {
        return fixingRateAvailable;
    }

    public void setFixingRateAvailable(Boolean fixingRateAvailable) {
        this.fixingRateAvailable = fixingRateAvailable;
    }

    @Override
    public String toString() {
        return "OMSAggregateOrderParam{" +
                ", aggregateKey='" + aggregateKey + '\'' +
                ", fixingAsset='" + fixingAsset + '\'' +
                ", fixingReference='" + fixingReference + '\'' +
                ", fixingTime=" + fixingTime +
                ", ccyPair='" + ccyPair + '\'' +
                ", balance=" + balance +
                ", dealtCcy='" + dealtCcy + '\'' +
                ", side='" + side + '\'' +
                ", orderCount=" + orderCount +
                ", date=" + date +
                ", orderIds=" + orderIds +
                ", fixingRate=" + fixingRate +
                ", fixingRateAvailable=" + fixingRateAvailable +
                '}';
    }
}
