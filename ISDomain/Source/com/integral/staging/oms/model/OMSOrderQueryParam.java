package com.integral.staging.oms.model;

import java.util.List;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 5/6/15
 */
public class OMSOrderQueryParam {
    private List<String> states;
    private List<String> portfolioIds;
    private List<String> clientOrderIds;
    private List<String> currencyPairs;
    private String brokerOrg;
    private String customerOrg;

    public List<String> getStates() {
        return states;
    }

    public void setStates(List<String> states) {
        this.states = states;
    }

    public String getBrokerOrg()
	{
		return brokerOrg;
	}

	public void setBrokerOrg( String brokerOrg )
	{
		this.brokerOrg = brokerOrg;
	}

	public String getCustomerOrg()
	{
		return customerOrg;
	}

	public void setCustomerOrg( String customerOrg )
	{
		this.customerOrg = customerOrg;
	}

	public List<String> getPortfolioIds() {
        return portfolioIds;
    }

    public void setPortfolioIds(List<String> portfolioIds) {
        this.portfolioIds = portfolioIds;
    }

    public List<String> getClientOrderIds() {
        return clientOrderIds;
    }

    public void setClientOrderIds(List<String> clientOrderIds) {
        this.clientOrderIds = clientOrderIds;
    }

    public List<String> getCurrencyPairs() {
        return currencyPairs;
    }

    public void setCurrencyPairs(List<String> currencyPairs) {
        this.currencyPairs = currencyPairs;
    }

    @Override
    public String toString() {
        return "OMSOrderQueryParam{" +
                "states=" + states +
                ", portfolioIds=" + portfolioIds +
                ", clientOrderIds=" + clientOrderIds +
                ", currencyPairs=" + currencyPairs +
                '}';
    }
}
