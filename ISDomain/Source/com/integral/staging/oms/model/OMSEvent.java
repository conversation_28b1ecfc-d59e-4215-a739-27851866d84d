package com.integral.staging.oms.model;

import java.util.HashMap;

public enum OMSEvent {
	
	CREATED(1, "CREATED"), UPDATED(2, "UPDATED"), STATE_CHANGED(3, "STATE_CHANGED"),AUTO_FAILURE(4, "AUTO_FAILURE"),
	AUTO_SUCCESS(5, "AUTO_SUCCESS"), PASS_SENT(6, "SENT"), PASS_SENT_FAILED(7, "SENT_FAILED"),PASS_RECEIVED(8, "RECEIVED"),
	PASS_ACCEPTED(9, "ACCEPTED"), PASS_REJECTED(10, "REJECTED"),PASS_FILLED(11, "FILLED"), 	PASS_WITHDRAW_FAILED(12, "WITHDRAW_FAILED"),
	PASS_WITHDRAWN(13, "WITHDRAWN"), PASS_CANCELLED(14, "CANCELLED"), IMPORTED(15, "IMPORTED"),
	PASS_CANCEL_RECEIVED(16, "CA<PERSON>EL_RECEIVED"), PASS_CANCEL_ACCEPTED(17, "CANCEL_ACCEPTED"), PASS_CANCEL_REJECTED(18, "CANCEL_REJECTED"),
	PASS_AMEND_RECEIVED(19, "AMEND_RECEIVED"), PASS_AMEND_ACCEPTED(20, "AMEND_ACCEPTED"), PASS_AMEND_REJECTED(21, "AMEND_REJECTED"),
	CREDIT_FAIL(22, "CREDIT_FAIL");
	
	private int code;
	private String displayName;
	
	public String getDisplayName() {
		return displayName;
	}
	public int getCode() {
		return code;
	}

	public static OMSEvent fromStatusCode( int statusCode )
	{
		return eventToEnum.get(statusCode);
	}
	
	OMSEvent(int code, String displayName) {
		this.code = code;
		this.displayName = displayName;
	}
	private static HashMap<Integer, OMSEvent> eventToEnum = new HashMap<Integer, OMSEvent>();
	static
	{
		for ( OMSEvent e : OMSEvent.values() )
		{
			eventToEnum.put(e.code, e);
		}
	}
	
}
