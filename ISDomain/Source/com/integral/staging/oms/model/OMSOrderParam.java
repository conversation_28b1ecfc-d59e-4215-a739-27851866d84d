package com.integral.staging.oms.model;

import java.util.List;


/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 4/9/15
 */
public class OMSOrderParam {
    private String orderId;
    private String clientOrderId;
    private String emsOrderId;
	private String ccyPair;
    private String dealtCcy;
    private String side;
    private Double dealtAmt;
    private String orderType;
    private Double orderPrice;
    private Double fillPrice;
    private Double filledAmount;
    private Double matchedPrice; // market price at the time of matching
    private String cptyA;
    private String cptyAOrg;
    private String tif;
    private Long expiryTime;
    private String notes;
    private String orderState;
    private String lockedByUser;
    private String portfolioId;
    private Double matchPrice;
    private Double ppSpread;
    private Boolean archived;
    private Long triggerTime;
    private String tradeId;
    private boolean primaryOrder;
    private String fromOrg;
    private String toOrg;
    private boolean acceptedForPass;
	private boolean sentForPass;
	private String cptyBOrg;
	private Double baseAmt;
	private Double termAmt;
	private Double homeCcyAmt;
	private boolean custOrder;	
	private String custNotes;
	private boolean ymBBook;
	private boolean emsOrder;
    private Long fixingTime;
    private String fixingReference;
    //Cover id
    private String transactionId;
    private Double venueBMR;
    private Long fixingDate;

	public boolean isEmsOrder()
	{
		return emsOrder;
	}

	public void setEmsOrder( boolean emsOrder )
	{
		this.emsOrder = emsOrder;
	}

	public boolean isYmBBook()
	{
		return ymBBook;
	}

	public void setYmBBook( boolean ymBBook )
	{
		this.ymBBook = ymBBook;
	}
	
	public String getCustNotes() {
		return custNotes;
	}

	public void setCustNotes(String custNotes) {
		this.custNotes = custNotes;
	}

	public boolean isCustOrder() {
		return custOrder;
	}

	public void setCustOrder(boolean custOrder) {
		this.custOrder = custOrder;
	}

	public Double getHomeCcyAmt() {
		return homeCcyAmt;
	}

	public void setHomeCcyAmt(Double usdAmt) {
		this.homeCcyAmt = usdAmt;
	}

	public Double getTermAmt()
	{
		return termAmt;
	}

	public void setTermAmt( Double termAmt )
	{
		this.termAmt = termAmt;
	}

	private String baseSide;


	public Double getBaseAmt()
	{
		return baseAmt;
	}

	public void setBaseAmt( Double baseAmt )
	{
		this.baseAmt = baseAmt;
	}

	public String getBaseSide()
	{
		return baseSide;
	}

	public void setBaseSide( String baseSide )
	{
		this.baseSide = baseSide;
	}

	public String getCptyBOrg()
	{
		return cptyBOrg;
	}

	public void setCptyBOrg( String cptyBOrg )
	{
		this.cptyBOrg = cptyBOrg;
	}

	private List<OMSContigencyParameter> contigencyParameters;

	public String getClientOrderId() {
        return clientOrderId;
    }

    public void setClientOrderId(String clientOrderId) {
        this.clientOrderId = clientOrderId;
    }

    public String getCcyPair() {
        return ccyPair;
    }

    public void setCcyPair(String ccyPair) {
        this.ccyPair = ccyPair;
    }

    public String getDealtCcy() {
        return dealtCcy;
    }

    public void setDealtCcy(String dealtCcy) {
        this.dealtCcy = dealtCcy;
    }

    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side;
    }

    public Double getDealtAmt() {
        return dealtAmt;
    }

    public void setDealtAmt(Double dealtAmt) {
        this.dealtAmt = dealtAmt;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Double getOrderPrice() {
        return orderPrice;
    }

    public void setOrderPrice(Double orderPrice) {
        this.orderPrice = orderPrice;
    }

    public Double getFillPrice() {
        return fillPrice;
    }

    public void setFillPrice(Double fillPrice) {
        this.fillPrice = fillPrice;
    }

    public String getCptyA() {
        return cptyA;
    }

    public void setCptyA(String cptyA) {
        this.cptyA = cptyA;
    }

    public String getCptyAOrg() {
        return cptyAOrg;
    }

    public void setCptyAOrg(String cptyAOrg) {
        this.cptyAOrg = cptyAOrg;
    }

    public String getTif() {
        return tif;
    }

    public void setTif(String tif) {
        this.tif = tif;
    }

    public Long getExpiryTime() {
        return expiryTime;
    }

    public void setExpiryTime(Long expiryTime) {
        this.expiryTime = expiryTime;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getOrderState() {
        return orderState;
    }

    public void setOrderState(String orderState) {
        this.orderState = orderState;
    }

    public String getLockedByUser() {
        return lockedByUser;
    }

    public void setLockedByUser(String lockedByUser) {
        this.lockedByUser = lockedByUser;
    }

    public String getPortfolioId() {
        return portfolioId;
    }

    public void setPortfolioId(String portfolioId) {
        this.portfolioId = portfolioId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Double getMatchPrice() {
        return matchPrice;
    }

    public void setMatchPrice(Double matchPrice) {
        this.matchPrice = matchPrice;
    }

    public Double getPpSpread() {
        return ppSpread;
    }

    public void setPpSpread(Double ppSpread) {
        this.ppSpread = ppSpread;
    }

    public Double getMatchedPrice() {
        return matchedPrice;
    }

    public void setMatchedPrice(Double matchedPrice) {
        this.matchedPrice = matchedPrice;
    }

    public Boolean isArchived() {
        return archived;
    }

    public void setArchived(Boolean archived) {
        this.archived = archived;
    }

    @Override
    public String toString() {
        return "OMSOrderParam{" +
                "orderId='" + orderId + '\'' +
                ", clientOrderId='" + clientOrderId + '\'' +
                ", ccyPair='" + ccyPair + '\'' +
                ", dealtCcy='" + dealtCcy + '\'' +
                ", side='" + side + '\'' +
                ", dealtAmt=" + dealtAmt +
                ", orderType='" + orderType + '\'' +
                ", orderPrice=" + orderPrice +
                ", fillPrice=" + fillPrice +
                ", filledAmount=" + filledAmount +
                ", matchedPrice=" + matchedPrice +
                ", cptyA='" + cptyA + '\'' +
                ", cptyAOrg='" + cptyAOrg + '\'' +
                ", tif='" + tif + '\'' +
                ", expiryTime=" + expiryTime +
                ", notes='" + notes + '\'' +
                ", orderState='" + orderState + '\'' +
                ", lockedByUser='" + lockedByUser + '\'' +
                ", portfolioId='" + portfolioId + '\'' +
                ", matchPrice=" + matchPrice +
                ", ppSpread=" + ppSpread +
                ", archived=" + archived +
                ", contigencyParameters=" + contigencyParameters +
                ", fixingTime=" + fixingTime +
                ", fixingReference=" + fixingReference +
                ", transactionId=" + transactionId +
                ", venueBMR=" + venueBMR +
                '}';
    }

	public Double getFilledAmount()
	{
		return filledAmount;
	}

	public void setFilledAmount( Double filledAmount )
	{
		this.filledAmount = filledAmount;
	}

	public Long getTriggerTime()
	{
		return triggerTime;
	}

	public void setTriggerTime( Long triggerTime )
	{
		this.triggerTime = triggerTime;
	}

	public String getTradeId()
	{
		return tradeId;
	}

	public void setTradeId( String tradeId )
	{
		this.tradeId = tradeId;
	}
	
    public List<OMSContigencyParameter> getContigencyParameters()
	{
		return contigencyParameters;
	}

	public void setContigencyParameters( List<OMSContigencyParameter> contigencyParameters )
	{
		this.contigencyParameters = contigencyParameters;
	}

	public boolean isPrimaryOrder()
	{
		return primaryOrder;
	}

	public void setPrimaryOrder( boolean primaryOrder )
	{
		this.primaryOrder = primaryOrder;
	}
	
    public boolean isAcceptedForPass()
	{
		return acceptedForPass;
	}

	public void setAcceptedForPass( boolean acceptedForPass )
	{
		this.acceptedForPass = acceptedForPass;
	}


    public boolean isSentForPass()
	{
		return sentForPass;
	}

	public void setSentForPass( boolean sentForPass )
	{
		this.sentForPass = sentForPass;
	}

	public String getToOrg()
	{
		return toOrg;
	}

	public void setToOrg( String toOrg )
	{
		this.toOrg = toOrg;
	}

	public String getFromOrg()
	{
		return fromOrg;
	}

	public void setFromOrg( String fromOrg )
	{
		this.fromOrg = fromOrg;
	}
	
    public String getEmsOrderId()
	{
		return emsOrderId;
	}

	public void setEmsOrderId( String emsOrderId )
	{
		this.emsOrderId = emsOrderId;
	}

    public Long getFixingTime() {
        return fixingTime;
    }

    public void setFixingTime(Long fixingTime) {
        this.fixingTime = fixingTime;
    }

    public String getFixingReference() {
        return fixingReference;
    }

    public void setFixingReference(String fixingReference) {
        this.fixingReference = fixingReference;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public Double getVenueBMR() {
        return venueBMR;
    }

    public void setVenueBMR(Double venueBMR) {
        this.venueBMR = venueBMR;
    }

    public Long getFixingDate() {
        return fixingDate;
    }

    public void setFixingDate(Long fixingDate) {
        this.fixingDate = fixingDate;
    }
}
