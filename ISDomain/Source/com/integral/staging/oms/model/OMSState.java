package com.integral.staging.oms.model;

import java.util.HashMap;
import java.util.HashSet;

import com.integral.staging.OrderStatusType;

public enum OMSState
{

	DRAFT(OrderStatusType.INTIAL), ACTIVE(OrderStatusType.INUSE), TRIGGERED(OrderStatusType.TRIGGERED), REJECTED(OrderStatusType.REJECTED),
	FILLED(OrderStatusType.MATCHED), RECEIVED(OrderStatusType.IMPORTED), PASSED(OrderStatusType.DOWNLOADED ), CANCELLED(OrderStatusType.CANCELLED),
	CANCEL_REQUESTED(OrderStatusType.CANCEL_REQUESTED), AUTO(OrderStatusType.AUTO), NOTIFIED(OrderStatusType.EXECUTED), ARCHIVED(OrderStatusType.ARCHIVED),
	EXPIRED(OrderStatusType.EXPIRED), ACCEPTED(OrderStatusType.ACCEPTED), TAKEN_BACK(OrderStatusType.WITHDRAWN), AMEND_REQUESTED(OrderStatusType.AMEND_REQUESTED), CREDIT_FAIL(OrderStatusType.CREDIT_FAIL),;

	OrderStatusType orderStatusType;

	public OrderStatusType getOrderStatusType()
	{
		return orderStatusType;
	}

	OMSState( OrderStatusType stagingState )
	{
		this.orderStatusType = stagingState;
	}

	public static OMSState fromStatusCode( int statusCode )
	{
		return statusToEnum.get(statusCode);
	}

	public static OrderStatusType fromOMSState( String omsState )
	{
		OMSState omsStateEnum = OMSState.valueOf(omsState);
		return omsStateEnum.orderStatusType;
	}

	public static boolean isValidState( String omsState )
	{
		return omsStateNameSet.contains(omsState);
	}

	private static HashMap<Integer, OMSState> statusToEnum = new HashMap<Integer, OMSState>();
	private static HashSet<String> omsStateNameSet = new HashSet<String>();

	static
	{
		for ( OMSState e : OMSState.values() )
		{
			statusToEnum.put(e.getOrderStatusType().code, e);
			omsStateNameSet.add(e.name());
		}
	}

}
