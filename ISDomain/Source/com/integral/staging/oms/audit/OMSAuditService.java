package com.integral.staging.oms.audit;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.spaces.fx.persistence.ISSpacesPersistenceService;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.spaces.PersistenceConstants;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.spaces.ApplicationSpaceEvent;
import com.integral.spaces.QueryBuilder;
import com.integral.spaces.SpaceIterator;
import com.integral.staging.oms.OMSConstants;
import com.integral.staging.oms.config.OMSConfig;
import com.integral.staging.oms.model.OMSEvent;
import com.integral.util.GUIDFactory;

/***
 * Audit Service to store and retreive OMS Audit events into Mongo
 * <AUTHOR>
 *
 */
public class OMSAuditService 
{

	static Log log = LogFactory.getLog(OMSAuditService.class);
	static AtomicLong incrementalCorrelationId = new AtomicLong();
	
	
	/**
	 *  Method to persist OMS Audit event into Mongo DB
	 */
	public static void audit( OMSAuditEvent omsAuditEvent )
	{
		try
		{
			if ( OMSConfig.getInstance().isAuditEnabled() )
			{
				omsAuditEvent.set_id(GUIDFactory.nextGUID());
				ISSpacesPersistenceService omsAuditPersistenceService;
				omsAuditPersistenceService = PersistenceServiceFactory.getAuditPersistenceService();

				ApplicationSpaceEvent ase = omsAuditPersistenceService.createEvent(omsAuditEvent, ApplicationEventCodes.EVENT_OMS_AUDIT_EVENT_PERSIST);
				long correlationId = incrementalCorrelationId.incrementAndGet();
				omsAuditPersistenceService.persist(ase, Long.toString(correlationId), false, "OMS Audit Event");
			}
		}
		catch ( Exception e )
		{
			log.error(OMSConstants.OMS_LOG_PREFIX + "Error in persisting OMS Audit Event" + omsAuditEvent, e);
		}
	}
	
	
	/**
	 * Query method to retreive OMSAuditEvent records
	 * 
	 * Query params can be orderId , userName , timeStamp (These columns have to be indexed in Mongo Db)
	 * @return
	 */
	/**
	 * Query method to retreive OMSAuditEvent records
	 * 
	 * Query params can be orderId , userName , timeStamp (These columns have to be indexed in Mongo Db)
	 * @return
	 */
	public static List<OMSAuditEvent> retrieve(String orderId ,String namespace) 
	{
		QueryBuilder queryBuilder = PersistenceServiceFactory.getAuditPersistenceService().getMetaspace().createNewQueryBuilder(namespace, PersistenceConstants.OMSAUDIT);
		queryBuilder.add(PersistenceServiceFactory.getAuditPersistenceService().getMetaspace().defaultCriteriaSet().is("ordId", orderId));
		SpacesQueryService.QueryResult<SpaceIterator> result = new SpacesQueryService.QueryResult<SpaceIterator>();
        List<OMSAuditEvent> omsAuditEvents = new ArrayList<OMSAuditEvent>();
		 int count = 0;
			try {
	            result.setResult( queryBuilder.build().getIterator(OMSAuditEvent.class , 0, 100));
	            count = result.getResult().count();
	            if(result.getResult() == null)
	            {
	            
	            }
	            else
	            {
	               result.setStatus( SpacesQueryService.QueryResult.Status.SUCCESS );
	               SpaceIterator<OMSAuditEvent> i = result.getResult();
	               while (i.hasNext())
	               {
	            	  OMSAuditEvent auditEvent =   i.next();
	            	  if(auditEvent.getEvent()!=null) {
	            		  
	            		  auditEvent.setEventDisplayName(OMSEvent.fromStatusCode(auditEvent.getEvent()).getDisplayName());
	            	  }
	            	  omsAuditEvents.add(auditEvent);
	               }
	            }
			}catch (Exception e)
			{
				log.error(OMSConstants.OMS_LOG_PREFIX + "Error in retreiving OMS Audit Event, OrderID: " + orderId + " Org: " + namespace, e);
			
			}
			return omsAuditEvents;
	}
	
}
