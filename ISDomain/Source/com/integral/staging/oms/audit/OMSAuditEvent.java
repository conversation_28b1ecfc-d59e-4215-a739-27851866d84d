package com.integral.staging.oms.audit;

import com.integral.spaces.DefaultSpaceEntity;
import com.integral.spaces.serialize.AllViews;
import com.integral.spaces.serialize.ClassSerializer;
import com.integral.spaces.serialize.FieldSerializer;
import com.integral.spaces.serialize.NumberSerializer;
import com.integral.spaces.serialize.ObjectSerializer;
import com.integral.spaces.serialize.StringSerializer;

@ClassSerializer(name = "omsae")
public class OMSAuditEvent extends DefaultSpaceEntity
{
	/***
	 * OMS Order Id of the event to be persisted
	 */
	@FieldSerializer(shortName = "ordId", views = { AllViews.class }, serializeUsing = StringSerializer.class)
	private String orderId;

	/**
	 * User which caused this Audit event, Empty if System generated Audit event
	 */
	@FieldSerializer(shortName = "u", views = { AllViews.class }, serializeUsing = StringSerializer.class)
	private String userName;

	/**
	 * 
	 */
	@FieldSerializer(shortName = "evt", views = { AllViews.class }, serializeUsing = NumberSerializer.class)
	private Integer event;

	@FieldSerializer(shortName = "tms", serializeUsing = NumberSerializer.class)
	private long timeStamp;

	@FieldSerializer(shortName = "ord", serializeUsing = StringSerializer.class, views = { AllViews.class } )
    private String order;
	
	@FieldSerializer(shortName = "evtMsg", views = { AllViews.class }, serializeUsing = StringSerializer.class)
	private String eventMsg;
	
	@FieldSerializer(shortName = "oS", views = { AllViews.class }, serializeUsing = StringSerializer.class)
	private String oldState;
	
	@FieldSerializer(shortName = "nS", views = { AllViews.class }, serializeUsing = StringSerializer.class)
	private String newState;
	
	@FieldSerializer(shortName = "aUsr", views = { AllViews.class }, serializeUsing = StringSerializer.class)
	private String actionUser;
	
	@FieldSerializer(shortName = "aOrg", views = { AllViews.class }, serializeUsing = StringSerializer.class)
	private String actionOrg;
	
	transient private String eventDisplayName;
	
    public String getEventDisplayName() {
		return eventDisplayName;
	}

	public void setEventDisplayName(String eventDisplayName) {
		this.eventDisplayName = eventDisplayName;
	}

	public String getActionOrg() {
		return actionOrg;
	}

	public void setActionOrg(String actionOrg) {
		this.actionOrg = actionOrg;
	}

	public String getActionUser() {
		return actionUser;
	}

	public void setActionUser(String actionUser) {
		this.actionUser = actionUser;
	}
	
	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Integer getEvent() {
		return event;
	}

	public void setEvent(Integer event) {
		this.event = event;
	}
	
	public String getOrder() {
		return order;
	}

	public void setOrder(String order) {
		this.order = order;
	}
	
	public String getEventMsg() {
		return eventMsg;
	}

	public void setEventMsg(String eventMsg) {
		this.eventMsg = eventMsg;
	}

	public String getOldState()
	{
		return oldState;
	}

	public void setOldState( String oldState )
	{
		this.oldState = oldState;
	}

	public String getNewState()
	{
		return newState;
	}

	public void setNewState( String newState )
	{
		this.newState = newState;
	}

	public long getTimeStamp()
	{
		return timeStamp;
	}

	public void setTimeStamp( long timeStamp )
	{
		this.timeStamp = timeStamp;
	}
	

}
