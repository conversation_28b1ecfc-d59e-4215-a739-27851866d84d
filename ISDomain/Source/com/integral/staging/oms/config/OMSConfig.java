package com.integral.staging.oms.config;

import java.util.*;

import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Entity;
import com.integral.persistence.Namespace;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.configuration.IdcMBeanC;
import com.integral.util.IdcUtilC;
import com.integral.util.StringUtilC;

public class OMSConfig  extends IdcMBeanC {
	
    private static final Log log = LogFactory.getLog( OMSConfig.class );
	public static final String OMS_AUDIT_ENABLED = "Idc.OMS.Audit.Enabled";
    public static final String SALES_DEALER_ORG = "Idc.OMS.Auto.Execute.Sales.Dealer.Org.";
    public static final String OMS_ORGS = "Idc.OMS.Orgs";
    public static final String OMS_CCYPairs = "Idc.OMS.CCYPairs";
	public static final String OMS_CCYPairs_PREFIX = OMS_CCYPairs + ".";
	public static final String OMS_CURRENCY_PAIR_GROUPS_LIST = "Idc.OMS.CcyPairGroups.List";
	public static final String OMS_CURRENCY_PAIR_GROUPS_LIST_PREFIX = OMS_CURRENCY_PAIR_GROUPS_LIST + ".";

    public static final String OMS_MARKET_MONITOR_ORDERBOOK_ORG = "Idc.OMS.Market.Monitor.OrderBook.Org.";
    public static final String OMS_ORDERBOOK_RATE_PROVIDER= "Idc.OMS.Trigger.Rate.Provider.";
    
    
    public static final String OMS_SOCKET_SERVER_ENABLED= "Idc.OMS.Socket.Server.Enabled";
    public static final String OMS_SOCKET_MAX_HTTP_CONTENT_LENGTH = "Idc.OMS.Socket.Server.MaxHttpContentLength";
    public static final String OMS_SOCKET_SERVER_PORT= "Idc.OMS.Socket.Server.Port";
    
    public static final String OMS_AUTO_FIX_TRANSPORT = "Idc.OMS.Auto.Execute.FIX.Transport";
    public static final String OMS_AUTO_ORDER_RELOAD = "Idc.OMS.Auto.Execute.Order.Reload";
    public static final String OMS_ORDER_PASSING_ACCOUNTS = "Idc.OMS.Order.Passing.Accounts";
    
    public static final String OMS_ORDER_ALLOW_LINKED_ORDER_PASS = "Idc.OMS.Passing.Linked.Order.Allowed";
    public static final String OMS_ORG_UI_SETTINGS = "Idc.OMS.Org.UI.Settings.";
    public static final String OMS_ORG_EXTERNAL_OMS_ORGS = "Idc.OMS.External.Venue.Orgs";
    private static final String OMS_MARKET_RATE_SETTINGS = "Idc.OMS.MarketRate.Settings";
    
    private Map<String ,String> salesDealerOrg;
    private List<String> omsOrgs;
    private List<String> externalOMSOrgs;
   
    private List<String> globalCCYPairs;
    private Map<String,String> CCYPairsPerOrg ;
	private Map<String, Collection<String>> ccyPairGroupsListMap;
    private Map<String ,String> marketMonitorOBOrg;
    private Map<String ,String> triggerRateProvider;
	private Map<String ,String> orderPassingAccounts;
    private Map<String, Map<String,String>> orgUISettings;
    
    private Integer socketServerPort;
    private Integer maxHttpContentLength;
    private boolean socketServerEnabled;
    private boolean autoOrderReloadEnabled;
    private boolean auditEnabled;
    private boolean linkedOrderPassingAllowed;
    private boolean isMarketRateFromBrokersOrderBook;
    private Map<String, Boolean> isMarketRateFromBrokersOrderBookMap;


    public boolean isAuditEnabled()
	{
		return auditEnabled;
	}

	private static final String G7_CCY_PAIRS = "EUR/USD,USD/JPY,GBP/USD,USD/CHF,AUD/USD,USD/CAD,NZD/USD";
	private static class OMSConfigHolder
	{
		private static final OMSConfig INSTANCE = new OMSConfig();
	}

	public static OMSConfig getInstance()
	{
		return OMSConfigHolder.INSTANCE;
	}
    
    private OMSConfig() {
        super("com.integral.staging.oms.config.OMSConfig");
        initialize();
    }
    
    @Override
	public synchronized final void initialize()
	{
		try
		{
			externalOMSOrgs = IdcUtilC.arrayAsArrayList( IdcUtilC.getSubstring( getStringProperty( OMS_ORG_EXTERNAL_OMS_ORGS, "" ), "," ) );
			salesDealerOrg = initSingleSuffixStringPropertyMap(SALES_DEALER_ORG, null);
			marketMonitorOBOrg = initSingleSuffixStringPropertyMap(OMS_MARKET_MONITOR_ORDERBOOK_ORG, null);
			triggerRateProvider = initSingleSuffixStringPropertyMap(OMS_ORDERBOOK_RATE_PROVIDER, null);
			socketServerPort = getIntProperty(OMS_SOCKET_SERVER_PORT, 9092);
			socketServerEnabled = getBooleanProperty(OMS_SOCKET_SERVER_ENABLED, false);
			maxHttpContentLength = getIntProperty(OMS_SOCKET_MAX_HTTP_CONTENT_LENGTH, 2097152);
			omsOrgs = IdcUtilC.arrayAsArrayList( IdcUtilC.getSubstring( getStringProperty( OMS_ORGS, "" ), "," ) );
			globalCCYPairs = IdcUtilC.arrayAsArrayList( IdcUtilC.getSubstring( getStringProperty( OMS_CCYPairs, G7_CCY_PAIRS  ), "," ) );
			CCYPairsPerOrg = initSingleSuffixStringPropertyMap(OMS_CCYPairs_PREFIX, null);
			ccyPairGroupsListMap = initSingleSuffixStringCollectionPropertyMap(OMS_CURRENCY_PAIR_GROUPS_LIST_PREFIX, null);
			orderPassingAccounts = initSingleSuffixStringPropertyMap(OMS_ORDER_PASSING_ACCOUNTS + ".", null);
			autoOrderReloadEnabled = getBooleanProperty(OMS_AUTO_ORDER_RELOAD, false);
			auditEnabled = getBooleanProperty(OMS_AUDIT_ENABLED, true);
			linkedOrderPassingAllowed = getBooleanProperty(OMS_ORDER_ALLOW_LINKED_ORDER_PASS, false);
			orgUISettings = initSinglePrefixMultiValuePropertyMap(OMS_ORG_UI_SETTINGS , null);
			isMarketRateFromBrokersOrderBook = getBooleanProperty(OMS_MARKET_RATE_SETTINGS, true);
			isMarketRateFromBrokersOrderBookMap = initSingleSuffixBooleanPropertyMap(OMS_MARKET_RATE_SETTINGS + ".", null, isMarketRateFromBrokersOrderBook);		
		}
		catch ( Exception e )
		{
			log.error("Exception in initializing OMS config", e);
		}
	}

    public boolean isMarketRateFromBrokersOrderBookEnabled(String orgName){
        if(orgName != null) {
            Boolean value = isMarketRateFromBrokersOrderBookMap.get(orgName);
            if(value != null) return value;
        }

        return isMarketRateFromBrokersOrderBook;
    } 
    
	public Map<String, Map<String, String>> getOrgUISettings()
	{
		return orgUISettings;
	}

	public void setOrgUISettings( Map<String, Map<String, String>> orgUISettings )
	{
		this.orgUISettings = orgUISettings;
	}

	public boolean isAutoOrderReloadEnabled()
	{
		return autoOrderReloadEnabled;
	}

	public void setAutoOrderReloadEnabled( boolean autoOrderReloadEnabled )
	{
		this.autoOrderReloadEnabled = autoOrderReloadEnabled;
	}

	public boolean isSocketServerEnabled()
	{
		return socketServerEnabled;
	}
    
	public Integer getSocketServerPort()
	{
		return socketServerPort;
	}
	
	public boolean isLinkedOrderPassingAllowed()
	{
		return linkedOrderPassingAllowed;
	}

	public void setLinkedOrderPassingAllowed( boolean linkedOrderPassingAllowed )
	{
		this.linkedOrderPassingAllowed = linkedOrderPassingAllowed;
	}

	public Integer getMaxHttpContentLength()
	{
		return maxHttpContentLength;
	}
	
	public String getMarketMonitorOrderBookOrg(String omsOrg)
	{
		return marketMonitorOBOrg.get(omsOrg);
	}
	
    /**
     * Property to define Sales Dealer org for OMS org which will be used to place orders on behalf of customer
     * @param omsOrg OMS org
     * @return sales dealer org
     */
    public String getSalesDealerOrg(String omsOrg) {
    	
    	return salesDealerOrg.get(omsOrg);
    	
    }
    /**
     * Property to get the Rate provider for OMS order book
     * @param omsOrg OMS org
     * @return rate provider
     */
    public String getRateProviderForTriggering(String omsOrg) {
    	
    	return triggerRateProvider.get(omsOrg);
    }

	public List<String> getOmsOrgs()
	{
		return omsOrgs;
	}
	
	public List<String> getCCYPairs()
	{
		return globalCCYPairs;
	}
	
	public List<String> getOrderPassingAccounts(String org)
	{
		List<String> orderPassAccountsList = new ArrayList<String>();
		String orderPassingAccountStr = orderPassingAccounts.get(org);
		if ( orderPassingAccountStr != null )
		{
			orderPassAccountsList = IdcUtilC.arrayAsArrayList(IdcUtilC.getSubstring(orderPassingAccountStr, ","));
		}
		return orderPassAccountsList;
	}
	
	public boolean isExternalOMS( String omsOrg )
	{
		return externalOMSOrgs != null && externalOMSOrgs.contains ( omsOrg );
	}

	public Set<String> getCCYPairs(String org)
	{
		Set<String> ccyPairsSet = new LinkedHashSet<String>();
		String orgLevelCCYPairs = CCYPairsPerOrg.get(org);
		boolean orgLevelConfigured = false;

		// check for currency pairs list configured
		if ( orgLevelCCYPairs != null )
		{
			orgLevelConfigured = true;
			ArrayList ccyPairList = IdcUtilC.arrayAsArrayList(IdcUtilC.getSubstring(orgLevelCCYPairs, ","));
			ccyPairsSet.addAll ( ccyPairList );
		}

		// check for any currency pair group list config
		Collection<String> ccyPairGroupList = ccyPairGroupsListMap.get( org );
		if ( ccyPairGroupList != null )
		{
			orgLevelConfigured = true;
			for ( String cpgFullName: ccyPairGroupList )
			{
				int pos = cpgFullName.indexOf( '@' );
				if ( pos > 0 )
				{
					String cpgName = cpgFullName.substring( 0, pos ).trim();
					String cpgNamespace = cpgFullName.substring( pos + 1 ).trim();
					Namespace ns = ReferenceDataCacheC.getInstance ().getNamespace ( cpgNamespace );
					if ( ! StringUtilC.isNullOrEmpty ( cpgName ) && ns != null )
					{
						CurrencyPairGroup cpg = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance ().getEntityByShortName ( cpgName, CurrencyPairGroupC.class, ns, Entity.ACTIVE_STATUS );
						if ( cpg != null )
						{
							for ( CurrencyPair currencyPair: cpg.getCurrencyPairs () )
							{
								ccyPairsSet.add ( currencyPair.getName () );
							}
						}
						else
						{
							log.warn ( "OC.getCCYPairs - no currency pair group found with name=" + cpgFullName );
						}
					}
					else
					{
						log.warn ( "OC.getCCYPairs - invalid cpg name or namespace=" + cpgFullName + ",ns=" + ns );
					}
				}
				else
				{
					log.warn ( "OC.getCCYPairs - invalid property value=" + cpgFullName + ". Value should be currency pair group shortname@namespace format." );
				}
			}
		}
		if ( !orgLevelConfigured )
		{
			ccyPairsSet.addAll ( globalCCYPairs );
		}
		return ccyPairsSet;
	}
}
