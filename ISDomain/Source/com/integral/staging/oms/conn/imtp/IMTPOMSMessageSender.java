package com.integral.staging.oms.conn.imtp;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.imtp.IMTPMessageReceiver;
import com.integral.imtp.config.IMTPConfigMBeanFactory;
import com.integral.imtp.config.IMTPConfigMBeanImpl;
import com.integral.imtp.connection.IMTPConnection;
import com.integral.imtp.connection.IMTPConnectionManager;
import com.integral.imtp.message.IMTPApplicationMessage;
import com.integral.imtp.message.IMTPMessageFactory;
import com.integral.imtp.session.IMTPSessionManager;
import com.integral.imtp.session.Session;
import com.integral.is.common.exception.MessageCommunicationExceptionC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.staging.oms.message.OMSMessage;
import com.integral.staging.oms.message.OMSMessageSerializer;
import com.integral.staging.oms.message.OMSMsgType;

public class IMTPOMSMessageSender
{

	private static Log log = LogFactory.getLog(IMTPOMSMessageSender.class);

	private String host;
	private String acceptorId;
	private int port;
	private byte version;
	private String selector;
	IMTPConfigMBeanImpl config;

	private Session session;

	public IMTPOMSMessageSender( String host, int port, String acceptorId, byte version)
	{
		this.host = host;
		this.port = port;
		this.acceptorId = acceptorId;
		this.version = version;
		this.selector = "OMS.SERVER.REQUEST.0";
	}

	public boolean initialize()
	{
		try
		{
			config = IMTPConfigMBeanImpl.copyIMTPConfig(IMTPConfigMBeanFactory.getInstance().getIMTPConfigMBean());
			config.setAcceptorHost(host);
			config.setAcceptorId(acceptorId);
			config.setAcceptorPort(port);
			String sessionId = IMTPSessionManager.getSessionId(config);
			session = (Session) IMTPSessionManager.getInstance().getSession(sessionId, IMTPConnectionManager.getInstance().getInitiator().getSessionPipelineSetupFunctor());
			
			return true;
		}
		catch ( Exception ex )
		{
			log.warn("initialize : Unable to initialize for " + this, ex);
			return false;
		}
	}

	public synchronized boolean isConnected()
	{
		IMTPConnection imtpConnection = session.getConnection();
		if ( imtpConnection == null || imtpConnection.getConnectionState().equals(IMTPConnection.ConnectionState.CLOSED) )
		{
			return false;
		}
		return true;
	}

	public synchronized boolean connect()
	{
		try
		{
			IMTPConnection imtpConnection = session.getConnection();

			if ( imtpConnection == null || imtpConnection.getConnectionState().equals(IMTPConnection.ConnectionState.CLOSED) )
			{
				if ( !session.getAndSetIsPendingConnection(true) )
				{
					try
					{
						imtpConnection = session.getConnection();
						if ( imtpConnection == null || imtpConnection.getConnectionState().equals(IMTPConnection.ConnectionState.CLOSED) )
						{
							log.info("Setting IMTP Connection for " + this);

							IMTPConnectionManager.getInstance().getInitiator().connect(config);
						}
					}
					finally
					{
						session.getAndSetIsPendingConnection(false);
					}

				}
				else
				{
					log.info("connect : IMTP Connection already pending for " + this);
				}
			}
			return true;
		}
		catch ( Exception ex )
		{
			log.warn("connect : Unable to connect for " + this, ex);
			return false;
		}
	}

	public synchronized boolean shutdown()
	{
		try
		{
			IMTPConnection imtpConnection = session.getConnection();

			if ( imtpConnection == null || imtpConnection.getConnectionState().equals(IMTPConnection.ConnectionState.CLOSED) )
			{
				log.info("shutdown : IMTP Connection already closed for " + this);
			}
			else
			{
				IMTPConnectionManager.getInstance().getInitiator().shutDown();
			}
			return true;
		}
		catch ( Exception ex )
		{
			log.warn("shutdown : Unable to connect for " + this, ex);
			return false;
		}
	}

	public synchronized OMSMessage sendMessage( OMSMsgType omsMsgType, OMSMessage message ) throws MessageCommunicationExceptionC
	{
		try
		{
			UnSafeBuffer unsafeBuf = new UnSafeBuffer();
			unsafeBuf.init(new byte[131072]);
			OMSMessageSerializer.serialize(omsMsgType.getMsgTypeCode(), version, message, unsafeBuf);
			IMTPApplicationMessage appMessage = IMTPMessageFactory.borrowApplicationMessage(unsafeBuf.array());
			appMessage.setAppDataType(IMTPApplicationMessage.APP_DATA_TYPE_OBJECT);
			appMessage.setAppSelector(this.selector);

			try
			{
				try
				{
					session.getMessageHandler().sendMessage(appMessage, false);
				}
				catch ( Exception ex )
				{
					log.error("sendMessage : IMTP error while getting response", ex);
				}
	
			}
			catch ( Exception ex )
			{
				log.error("sendMessage : Error in sending server imtp message", ex);
				throw new MessageCommunicationExceptionC(new StringBuffer().append("Exception in sending ").append(appMessage.getClass().getName()).append(" to host ").append(host).append(" ").append(ex.toString()).append(": message{").append(ex.getMessage()).append("}").toString());
			}

		}
		catch ( Exception ex )
		{
			log.error("sendMessage : Unknown Error in sending imtp message", ex);
			throw new MessageCommunicationExceptionC(ex.getMessage());
		}
		finally
		{

		}
		return null;
	}

	@Override
	public String toString()
	{
		return "IMTPOMSMessageSender{" + "port=" + port + ", acceptorId='" + acceptorId + '\'' + ", host='" + host + '\'' + '}';
	}


}
