package com.integral.staging.oms.conn.imtp;


import com.integral.imtp.IMTPMessageReceiver;
import com.integral.imtp.message.IMTPApplicationMessage;
import com.integral.imtp.message.IMTPMessage;
import com.integral.imtp.message.PoolableMessage;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.staging.oms.conn.OMSServerResponseHandler;


public class IMTPOMSMessageReceiver implements  IMTPMessageReceiver {
	private static final Log log = LogFactory.getLog(IMTPOMSMessageReceiver.class);
	public static String SELECTOR = "OMS.SERVER.REQUEST.0";
    private OMSServerResponseHandler responseHandler;

	public IMTPOMSMessageReceiver()
    {
		this.responseHandler = new OMSServerResponseHandler();
	}

	@Override
	public void messageReceived(IMTPMessage message) {
		IMTPApplicationMessage applicationMessage = (IMTPApplicationMessage) message;
		try {
			if (log.isDebugEnabled()) {
				log.debug("messageReceived: Received IMTP message" + applicationMessage.getApplicationData());
			}

			try {
				responseHandler.handle(applicationMessage);

			} catch (Exception ex) {
				log.error("messageReceived : Exception while handling IMTPMessage: ", ex);
			}

			// Increment the reference.
			((PoolableMessage) applicationMessage).incrementReference();

		} catch (Exception e) {
			log.error("messageReceived : Exception while handling IMTPMessage: ", e);
		}
	}

}
