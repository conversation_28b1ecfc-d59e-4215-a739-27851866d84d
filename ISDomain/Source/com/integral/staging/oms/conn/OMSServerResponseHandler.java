package com.integral.staging.oms.conn;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.imtp.message.IMTPApplicationMessage;
import com.integral.imtp.session.IMTPSession;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.staging.oms.OMSConstants;
import com.integral.staging.oms.conn.imtp.OMSServerException;
import com.integral.staging.oms.message.*;
import com.integral.staging.oms.passorder.OMSNotificationProcessor;

public class OMSServerResponseHandler
{

	protected Log log = LogFactory.getLog(this.getClass());

	public void handle( IMTPApplicationMessage imtpMessage )
	{
		try
		{
			if ( log.isDebugEnabled() )
			{
				log.debug(OMSConstants.OMS_LOG_PREFIX + " Received IMTP OMS Message " + imtpMessage);
			}
			byte[] msg = (byte[]) imtpMessage.getApplicationData();
			UnSafeBuffer unsafeBuf = new UnSafeBuffer();
			unsafeBuf.init(msg);
			OMSMessage omsMessage = deserializeAndHandleMessage(unsafeBuf);
			if ( omsMessage != null )
			{
				OMSNotificationProcessor.getInstance().notify(omsMessage);
			}

		}
		catch ( Exception e )
		{
			log.error(OMSConstants.OMS_LOG_PREFIX + " Exception while handling OMS imtp message", e);
		}
	}

	public static OMSMessage deserializeAndHandleMessage( UnSafeBuffer unsafeBuf ) throws OMSServerException
	{
		OMSMessage omsMessage = null;

		short msgTypeCode = OMSMessageSerializer.getMsgType(unsafeBuf);
		byte version = OMSMessageSerializer.getVersion(unsafeBuf);

		OMSMsgType msgType = OMSMsgType.getMsgType(msgTypeCode);
		switch ( msgType )
		{
		case PASS :
			omsMessage = new OMSPass();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case PASS_RESPONSE :
			omsMessage = new OMSPassResponse();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case ACCEPT :
			omsMessage = new OMSAccept();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case ACCEPT_RESPONSE :
			omsMessage = new OMSAcceptResponse();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case WITHDRAW_ACCEPT :
			omsMessage = new OMSWithdrawAccept();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case WITHDRAW_REJECT :
			omsMessage = new OMSWithdrawReject();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case WITHDRAW_ACCEPT_RESPONSE :
			omsMessage = new OMSWithdrawAcceptResponse();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case WITHDRAW_REJECT_RESPONSE :
			omsMessage = new OMSWithdrawRejectResponse();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case WITHDRAW :
			omsMessage = new OMSWithdraw();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case WITHDRAW_RESPONSE :
			omsMessage = new OMSWithdrawResponse();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case REJECT :
			omsMessage = new OMSReject();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case REJECT_RESPONSE :
			omsMessage = new OMSRejectResponse();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case FILL :
			omsMessage = new OMSFill();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case FILL_RESPONSE :
			omsMessage = new OMSFillResponse();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case AUTO :
			omsMessage = new OMSAuto();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case AUTO_RESPONSE :
			omsMessage = new OMSAutoResponse();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case MANUAL :
			omsMessage = new OMSManual();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case MANUAL_RESPONSE :
			omsMessage = new OMSManualResponse();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case UPDATE :
			omsMessage = new OMSUpdate();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case CONFIRM :
			omsMessage = new OMSOrderConfirmation();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case AMEND:
			omsMessage = new OMSAmend();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case AMEND_RESPONSE:
			omsMessage = new OMSAmendResponse();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case AMEND_ACCEPT:
			omsMessage = new OMSAmendAccept();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case AMEND_ACCEPT_RESPONSE:
			omsMessage = new OMSAmendAcceptResponse();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case AMEND_REJECT:
			omsMessage = new OMSAmendReject();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		case AMEND_REJECT_RESPONSE:
			omsMessage = new OMSAmendRejectResponse();
			OMSMessageSerializer.deserialize(msgTypeCode, version, omsMessage, unsafeBuf);
			break;
		default :
		}

		return omsMessage;
	}
}
