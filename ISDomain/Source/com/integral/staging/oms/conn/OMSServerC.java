package com.integral.staging.oms.conn;

import java.util.Map;

import com.integral.imtp.connection.IMTPConnectionManager;
import com.integral.services.ServiceContainerMBean;
import com.integral.services.ServiceInvocationException;
import com.integral.services.ServiceLevel;
import com.integral.services.ServiceSettings;
import com.integral.services.ServiceSettings.TransportSettings;
import com.integral.services.config.ServicesMBean.ServiceTransport;

public class OMSServerC implements OMSServer
{
	private static final String VERSION = "1.0";
    private OMSServerServiceConfig configuration;
    private Status status;
    private ServiceSettings serviceSettings;

    /**
     * Identifies each service based on its unique id.
     *
     * @return id.
     */
    @Override
    public String getId() {
        return OMSServer.class.getCanonicalName();
    }

    @Override
    public OMSServerServiceConfig getConfiguration() {
        return configuration;
    }

    @Override
    public Class<OMSServerServiceConfig> getConfigurationType() {
        return OMSServerServiceConfig.class;
    }

    @Override
	public void initialize( OMSServerServiceConfig config ) throws ServiceInvocationException
	{
		this.configuration = config;
		status = Status.INITIALIZED;
		ServiceSettings serviceSettings = new ServiceSettings();
		serviceSettings.setServicePort(ServiceContainerMBean.getInstance().getPort());
		serviceSettings.setServiceTransport(ServiceTransport.IMTP.toString());
		TransportSettings transportSettings = new TransportSettings();
		transportSettings.setExtension1(IMTPConnectionManager.getInstance().getAcceptor().getIMTPConfig().getAcceptorId());
		serviceSettings.setTransportSettings(transportSettings);
		this.serviceSettings = serviceSettings;
	}

    @Override
    public void start() throws ServiceInvocationException {
        status = Status.RUNNING;
    }

    @Override
    public void stop() throws ServiceInvocationException {
        status = Status.INITIALIZED;
    }

    @Override
    public void halt() throws ServiceInvocationException, UnsupportedOperationException {
        status = Status.HALTED;
    }


    @Override
    public void resume() throws ServiceInvocationException, UnsupportedOperationException {
        status = Status.RUNNING;
    }

    @Override
    public void tearDown() throws ServiceInvocationException {
        status = Status.DOWN;
    }

    @Override
    public ServiceSettings getServiceSettings() {
        return serviceSettings;
    }

    @Override
    public Status getStatus() {
        return status;
    }

    @Override
    public ServiceLevel getServiceLevel() {
        return configuration.getServiceLevel();
    }

    @Override
    public Map<String, String> reportStats() {
        return null;
    }

	@Override
	public String getVersion()
	{
		return VERSION;
	}

	@Override
	public String getDescription()
	{
		return configuration.toString();
	}
}
