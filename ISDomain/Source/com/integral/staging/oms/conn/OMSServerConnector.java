package com.integral.staging.oms.conn;

import com.integral.is.common.exception.MessageCommunicationExceptionC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.services.ServiceInvocationException;
import com.integral.services.provider.ServiceInstance;
import com.integral.services.provider.ServiceProvider;
import com.integral.services.provider.ServiceProviderChangeListener;
import com.integral.services.registry.RegistryChangeListener;
import com.integral.staging.oms.OMSConstants;
import com.integral.staging.oms.conn.imtp.IMTPOMSMessageSender;
import com.integral.staging.oms.conn.imtp.OMSServerException;
import com.integral.staging.oms.conn.service.OMSServerServiceLocator;
import com.integral.staging.oms.message.OMSMessage;

import java.util.concurrent.ConcurrentHashMap;

public class OMSServerConnector
{
    private static final Object LOCK = new Object ();
    private static final Log log = LogFactory.getLog ( OMSServerServiceLocator.class );
    private static final byte VERSION = 0;

    private final ConcurrentHashMap<String, OMSServiceHolder> serviceStatusHolderMap = new ConcurrentHashMap<String, OMSServiceHolder> ();

    public OMSServerConnector ( )
    {
    }

    private OMSServiceHolder getOrCreateServiceStatusHolder ( String omsOrg )
    {
        OMSServiceHolder serviceStatusHolder = serviceStatusHolderMap.get ( omsOrg );
        if ( serviceStatusHolder == null )
        {
            try
            {
                synchronized ( LOCK )
                {
                    serviceStatusHolder = serviceStatusHolderMap.get ( omsOrg );
                    if ( serviceStatusHolder != null )
                    {
                        log.info ( "OSH.getOrCreateServiceStatusHolder - found OMSServiceHolder for org="
                                + omsOrg + ",serviceStatusHolder=" + serviceStatusHolder  );
                        return serviceStatusHolder;
                    }

                    OMSServerServiceConfig omsServiceConfig = new OMSServerServiceConfig ( omsOrg );
                    ServiceProvider<OMSServer, OMSServerServiceConfig> serviceProvider = OMSServerServiceLocator.INSTANCE.getServiceProvider ( omsServiceConfig );
                    serviceStatusHolder = new OMSServiceHolder ( serviceProvider, omsOrg );
                    boolean success = serviceProvider.registerChangeListener ( serviceStatusHolder );
                    if ( success )
                    {
                        OMSServiceHolder cachedHolder = serviceStatusHolderMap.putIfAbsent ( omsOrg, serviceStatusHolder );
                        if ( cachedHolder != null )
                        {
                            log.info ( "OSH.getOrCreateServiceStatusHolder - already found OMSServiceHolder for org="
                                    + omsOrg + ",cachedHolder=" + cachedHolder + " Unregister/discard new=" + serviceStatusHolder );
                            serviceProvider.unRegisterChangeListener ( serviceStatusHolder );
                            return cachedHolder;
                        }
                        else
                        {
                            log.info ( "OSH.getOrCreateServiceStatusHolder - Created OMSServiceHolder for org="
                                    + omsOrg + ",serviceStatusHolder=" + serviceStatusHolder + ",serviceProvider="
                                    + serviceProvider + ",omsServiceConfig=" + omsServiceConfig );
                            serviceStatusHolder.updateServiceStatus ();
                            return serviceStatusHolder;
                        }
                    }
                    else
                    {
                        log.info ( "OSH.getOrCreateServiceStatusHolder - Failed to create OMSServiceHolder for org="
                                + omsOrg + ",serviceProvider=" + serviceProvider + ",omsServiceConfig=" + omsServiceConfig );
                        return null;
                    }
                }
            }
            catch ( Exception ex )
            {
                log.warn ( "getOrCreateServiceStatusHolder : Failed to create OMSServerConnector for org=" + omsOrg, ex );
            }
        }
        return serviceStatusHolder;
    }

    private IMTPOMSMessageSender startIMTPSender ( ServiceProvider<OMSServer, OMSServerServiceConfig> serviceProvider )
    {
        IMTPOMSMessageSender messageSenderTemp = null;
        try
        {
            if ( serviceProvider.getServiceInstance ().getConfiguration () != null && serviceProvider.getServiceInstance ().getRunningInstanceId () != null )
            {
                String serviceAddress = serviceProvider.getServiceInstance ().getAddress ();
                int port = serviceProvider.getServiceInstance ().getPort ();
                String acceptorId = serviceProvider.getServiceInstance ().getTransportExtension1 ();
                log.info ( OMSConstants.OMS_LOG_PREFIX + ".startIMTPSender : Setting IMTP connection with OMS Sever. AcceptorId-> " + acceptorId + " , port->" + port );

                messageSenderTemp = new IMTPOMSMessageSender ( serviceAddress, port, acceptorId, VERSION );
                if ( messageSenderTemp.initialize () )
                {
                    if ( !messageSenderTemp.isConnected () )
                    {
                        messageSenderTemp.connect ();
                    }
                    log.info ( OMSConstants.OMS_LOG_PREFIX + ".startIMTPSender : Initializing messageSender successful." );
                }
                else
                {
                    log.error ( OMSConstants.OMS_LOG_PREFIX + ".startIMTPSender : Initializing messageSender failed." );
                }
            }
        }
        catch ( Exception e )
        {
            log.error ( OMSConstants.OMS_LOG_PREFIX + ".startIMTPSender : Initializing messageSender failed.", e );
        }
        return messageSenderTemp;
    }

    public OMSMessage sendMessage ( OMSMessage omsMessage ) throws OMSServerException, MessageCommunicationExceptionC
    {
        OMSMessage responseObj;
        String omsOrg = omsMessage.getToOMSOrg ();
        OMSServiceHolder serviceStatusHolder = getOrCreateServiceStatusHolder ( omsOrg );
        if ( !serviceStatusHolder.isServiceActive () )
        {
            log.info ( " sendMessage : OMS Service is not active " + omsMessage.getToOMSOrg () );
            throw new OMSServerException ( " Connection to Org " + omsMessage.getToOMSOrg () + " is inactive" );
        }

        IMTPOMSMessageSender sender = serviceStatusHolder.getIMTPOMSSender ();
        if ( sender != null )
        {
            if ( !sender.isConnected () )
            {
                log.info ( " sendMessage : Reconnecting to " + omsMessage.getToOMSOrg () );
                sender.connect ();
            }
            if ( !sender.isConnected () )
            {
                log.info ( " sendMessage : Organization " + omsMessage.getToOMSOrg () + " is not connected" );
                throw new OMSServerException ( " Connection to Org " + omsMessage.getToOMSOrg () + " is inactive" );
            }
            else
            {
                log.info ( OMSConstants.OMS_LOG_PREFIX + " sendMessage : " + omsMessage );
                responseObj = sender.sendMessage ( omsMessage.getMsgType (), omsMessage );
            }
        }
        else
        {
            log.info ( " sendMessage : OMS " + omsMessage.getToOMSOrg () + " message sender not initialized" );
            throw new OMSServerException ( " Connection to Org " + omsMessage.getToOMSOrg () + " is inactive" );
        }
        return responseObj;
    }

    private class OMSServiceHolder implements ServiceProviderChangeListener
    {

        private final ServiceProvider<OMSServer, OMSServerServiceConfig> serviceProvider;
        private final String description;
        private Boolean serviceActive;
        private IMTPOMSMessageSender imtpOMSSender;
        private final String omsOrg;

        public OMSServiceHolder ( ServiceProvider<OMSServer, OMSServerServiceConfig> serviceProvider, String omsOrg )
        {
            this.serviceProvider = serviceProvider;
            this.description = toString ();
            this.omsOrg = omsOrg;
            initMessageSender ();
        }

        private void initMessageSender ( )
        {
            try
            {
                ServiceInstance<OMSServer> serviceInstance = getServiceInstance ();
                if ( serviceInstance != null && serviceInstance.getRunningInstanceId () != null )
                {
                    imtpOMSSender = startIMTPSender ( serviceProvider );

                    log.info ( OMSConstants.OMS_LOG_PREFIX + ".initMessageSender : Initialized OMS Message sender for OMS Org = " + omsOrg );
                }
                else
                {
                    log.info ( OMSConstants.OMS_LOG_PREFIX + ".initMessageSender : No running instance found for OMS Org" + omsOrg );
                }
            }
            catch ( Exception e )
            {
                log.error ( OMSConstants.OMS_LOG_PREFIX + ".initMessageSender : Error while updating status" );
            }
        }

        private void destroyMessageSender ( )
        {
            if ( imtpOMSSender != null )
            {
                imtpOMSSender.shutdown ();
                imtpOMSSender = null;
            }
        }

        public boolean isServiceActive ( )
        {
            if ( serviceActive == null )
            {
                updateServiceStatus ();
            }
            return serviceActive != null && serviceActive;
        }

        public IMTPOMSMessageSender getIMTPOMSSender ( )
        {
            return imtpOMSSender;
        }

        /**
         * notifies about an event. if {@link ServiceProviderEvent#getRegistryEventType()} returns {@link RegistryChangeListener.RegistryEventType#ZOOKEEPER_CONNECTION_DOWN}
         * Do not call any method on the {@code com.integral.services.registry.ServiceRegistry} unless a following notifyRegistryEvent call with
         * {@link ServiceProviderEvent#getRegistryEventType()} returns {@link RegistryChangeListener.RegistryEventType#ZOOKEEPER_CONNECTION_UP}
         *
         * @param event event
         */
        @Override
        public void notifyServiceProviderEvent ( ServiceProviderEvent event )
        {
            log.info ( "OSC.notifyServiceProviderEvent - notification received. event=" + event );
            if ( event.getRegistryEventType ().equals ( RegistryChangeListener.RegistryEventType.ZOOKEEPER_CONNECTION_DOWN ) )
            {
                log.info ( "OSC.notifyServiceProviderEvent - zookeeper connection down notification received. event=" + event );
                serviceActive = false;
                destroyMessageSender ();
            }
            else
            {
                log.info ( "OSC.notifyServiceProviderEvent - notification received. Updating service status. event=" + event );
                updateServiceStatus ();
                initMessageSender ();
            }
        }

        protected void updateServiceStatus ( )
        {
            ServiceInstance<OMSServer> serviceInstance = getServiceInstance ();
            if ( serviceInstance != null && serviceInstance.getRunningInstanceId () != null )
            {
                if ( log.isDebugEnabled () )
                {
                    log.debug ( OMSConstants.OMS_LOG_PREFIX + ".updateServiceStatus : Service is active for "
                            + serviceProvider + ",serviceInstance=" + serviceInstance );
                }
                serviceActive = true;
            }
            else
            {
                log.info ( OMSConstants.OMS_LOG_PREFIX + ".updateServiceStatus : Status is Inactive for "
                        + serviceProvider + ",serviceInstance=" + serviceInstance );
                serviceActive = null;
            }
        }

        /**
         * Returns the description of the listener. It is necessary to uniquely identify each listener to log stats against it.
         *
         * @return description
         */
        @Override
        public String getDescription ( )
        {
            return description;
        }

        @Override
        public String toString ( )
        {
            final StringBuilder sb = new StringBuilder ( "OMSServiceHolder{" );
            sb.append ( " omsOrg=" ).append ( omsOrg );
            sb.append ( " serviceProvider=" ).append ( serviceProvider );
            sb.append ( '}' );
            return sb.toString ();
        }

        public ServiceInstance<OMSServer> getServiceInstance ( )
        {
            try
            {
                return serviceProvider.getServiceInstance ();
            }
            catch ( ServiceInvocationException e )
            {
                log.warn ( "getServiceInstance : Failed to get ServiceInstance. " + description );
                if ( log.isDebugEnabled () )
                {
                    log.debug ( "getServiceInstance : Failed to get ServiceInstance.  " + description, e );
                }
                return null;
            }
        }
    }
}
