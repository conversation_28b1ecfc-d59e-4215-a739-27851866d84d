package com.integral.staging.oms.conn;

import java.util.Set;

import com.integral.staging.oms.message.OMSMessage;

public class OMSServerFilter
{
	private Set<Integer> omsOrgs;	
	
	public OMSServerFilter(Set<Integer> omsOrgs)
	{
		this.omsOrgs = omsOrgs;
	}
	
	public boolean match(OMSMessage omsMessage)
	{
		if ( omsOrgs != null && !omsOrgs.contains(omsMessage.getFromOMSOrg()) )
		{
			return false;
		}

		return true;
	}

}
