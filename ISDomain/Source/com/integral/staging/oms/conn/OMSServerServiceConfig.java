package com.integral.staging.oms.conn;

import java.util.List;

import com.integral.services.ServiceConfiguration;
import com.integral.services.ServiceLevel;

public class OMSServerServiceConfig extends ServiceConfiguration {

	private String serverName;
	
	private String omsOrgName;
	
	@Deprecated
	private List<String> omsOrgs;

	public OMSServerServiceConfig() {
		super("");
	}

	public OMSServerServiceConfig(String omsOrgName) {
		super(omsOrgName);
		this.omsOrgName = omsOrgName;
		this.setUniqueServiceConfigId("OMS"+omsOrgName);
	}

	public OMSServerServiceConfig(ServiceLevel serviceLevel, String serviceConfigurationId) {
		super(serviceLevel, serviceConfigurationId);
	}

	public String getServerName()
	{
		return serverName;
	}

	public void setServerName( String serverName )
	{
		this.serverName = serverName;
	}
	
	public String getOmsOrgName()
	{
		return omsOrgName;
	}

	public void setOmsOrgName( String omsOrgName )
	{
		this.omsOrgName = omsOrgName;
	}

	public List<String> getOmsOrgs()
	{
		return omsOrgs;
	}

	public void setOmsOrgs( List<String> omsOrgs )
	{
		this.omsOrgs = omsOrgs;
	}

	@Override
	public int hashCode()
	{
		int result = omsOrgName.hashCode();
		return result;
	}

	
	
	@Override
	public boolean equals( Object obj )
	{
		if ( this == obj )
			return true;
		if ( obj == null )
			return false;
		if ( getClass() != obj.getClass() )
			return false;
		
		OMSServerServiceConfig other = (OMSServerServiceConfig) obj;
		if ( !serverName.equals(other.serverName) )
		{
			return false;
		}
		else if ( omsOrgName == null  &&  other.omsOrgName != null  || omsOrgName != null  &&  other.omsOrgName == null)
		{
			return false;
		}
		else if ( !omsOrgName.equals(other.omsOrgName))
		{
			return false;
		}
		return true;
	}


	@Override
	public String toString() {
		return "OMSServerServiceConfig [serverName=" + serverName + ", omsOrgName=" + omsOrgName + "]";
	}

	
}
