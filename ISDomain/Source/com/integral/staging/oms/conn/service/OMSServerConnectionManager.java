package com.integral.staging.oms.conn.service;

import com.integral.is.startup.services.VirtualServerServiceConnector;
import com.integral.staging.oms.conn.OMSServerConnector;

public class OMSServerConnectionManager
{

	private static class OMSServerConnectionManagerHolder
	{
		private static final OMSServerConnectionManager INSTANCE = new OMSServerConnectionManager();
	}

	public static OMSServerConnectionManager getInstance()
	{
		return OMSServerConnectionManagerHolder.INSTANCE;
	}

	private static class OMSServerConnectorHolder
	{
		private static final OMSServerConnector INSTANCE = new OMSServerConnector();
	}

	public OMSServerConnector getConnector()
	{
		return OMSServerConnectorHolder.INSTANCE;
	}
	
	private static class VirtualServerConnectorHolder
	{
		private static final VirtualServerServiceConnector INSTANCE = new VirtualServerServiceConnector();
	}

	public VirtualServerServiceConnector getVirtualServerConnector()
	{
		return VirtualServerConnectorHolder.INSTANCE;
	}
}
