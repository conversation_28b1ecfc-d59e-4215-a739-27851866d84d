package com.integral.staging.oms.conn.service;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.services.ServiceIdentity;
import com.integral.services.ServicesFactory;
import com.integral.services.container.ServiceContainer;
import com.integral.services.filter.ServiceInstanceFilter;
import com.integral.services.provider.ServiceProvider;
import com.integral.services.provider.ServiceProviderImpl;
import com.integral.services.strategy.ServiceInstanceSelectionStrategies;
import com.integral.services.strategy.ServiceInstanceSelectionStrategy;
import com.integral.staging.oms.conn.OMSServer;
import com.integral.staging.oms.conn.OMSServerServiceConfig;

import java.util.Collections;

public enum OMSServerServiceLocator
{
    INSTANCE;
    private final ServiceContainer serviceContainer;
    private static final Log log = LogFactory.getLog ( OMSServerServiceLocator.class );

    private OMSServerServiceLocator ( )
    {
        this.serviceContainer = ServicesFactory.getInstance ().getServiceContainer ();
    }

    public ServiceProvider<OMSServer, OMSServerServiceConfig> getServiceProvider ( OMSServerServiceConfig omsServiceConfig )
    {
        try
        {
            ServiceInstanceFilter<OMSServerServiceConfig> filter = new OMSServerServiceFilter ( omsServiceConfig );

            ServiceIdentity<OMSServerServiceConfig> serviceIdentity = new ServiceIdentity<OMSServerServiceConfig> ( OMSServer.class.getCanonicalName (), null,
                    omsServiceConfig, OMSServerServiceConfig.class, false, OMSServer.class.getName () );
            ServiceInstanceSelectionStrategy<OMSServerServiceConfig> strategy = ServiceInstanceSelectionStrategies.RANDOM;
            return new ServiceProviderImpl<OMSServer, OMSServerServiceConfig> ( serviceIdentity, strategy,
                    Collections.singletonList ( filter ), serviceContainer );
        }
        catch ( Exception ex )
        {
            log.error ( "ServiceLocator.getServiceProvider : Error getting Service Provider.", ex );
        }
        return null;
    }
}




