package com.integral.staging.oms.conn.service;

import com.integral.services.RunningServiceInstanceIdentity;
import com.integral.services.ServiceConfiguration;
import com.integral.services.filter.ServiceInstanceFilter;
import com.integral.staging.oms.conn.OMSServerServiceConfig;

public class OMSServerServiceFilter implements ServiceInstanceFilter<OMSServerServiceConfig>
{
	OMSServerServiceConfig omsConfig;
	public OMSServerServiceFilter(OMSServerServiceConfig omsConfig)
	{
		this.omsConfig = omsConfig;
	}
	
	@Override
	public boolean apply( RunningServiceInstanceIdentity<OMSServerServiceConfig> service )
	{
        ServiceConfiguration configuration = (ServiceConfiguration) service.getConfiguration();
        return configuration.getServiceConfigurationId().equals(omsConfig.getServiceConfigurationId());
    }

}
