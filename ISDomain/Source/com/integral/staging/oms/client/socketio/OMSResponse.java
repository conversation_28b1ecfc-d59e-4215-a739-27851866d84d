package com.integral.staging.oms.client.socketio;

import java.util.HashMap;

import com.integral.finance.creditLimit.CreditLimit;
import com.integral.oms.OrderAction;
import com.integral.staging.AmendParameters;
import com.integral.staging.Order;
import com.integral.staging.oms.OMSUtil;
import com.integral.staging.oms.model.OMSEvent;
import com.integral.staging.oms.model.OMSOrderParam;
import com.integral.staging.oms.model.OMSState;

public class OMSResponse extends SocketResponse
{
	private final static String WHITE_SPACE = " ";
	public final static String OLD_STATE = "OS";
	public final static String NEW_STATE = "NS";
	
	private String orderId;
	private OMSEvent event;
	private OMSOrderParam orderParams;

	public OMSResponse( String orderId ,String responseString, OMSEvent event, OMSOrderParam orderParams )
	{
		super(responseString);
		this.orderId = orderId;
		this.event = event;
		this.orderParams = orderParams;
	}
	
	
	public OMSResponse( Status status, String response)
	{
		super(status , response);
	}

	public OMSOrderParam getOrderParams()
	{
		return orderParams;
	}

	public void setOrderParams( OMSOrderParam orderParams )
	{
		this.orderParams = orderParams;
	}

	public OMSEvent getEvent()
	{
		return event;
	}

	public void setEvent( OMSEvent event )
	{
		this.event = event;
	}

	public String getOrderId()
	{
		return orderId;
	}

	public void setOrderId( String orderId )
	{
		this.orderId = orderId;
	}


	public static StringBuilder getOrderDetails(Order order) {
		
		StringBuilder strBuilder = new StringBuilder();
		String org = order.getCounterPartyAOrg()!=null ? (order.getCounterPartyAOrg()+"'s") : "PlaceHolder";
		boolean isLinkedOrder = order.getOrderContingencies()!=null && !order.getOrderContingencies().isEmpty();
		String orderType = order.getType().name();
		String side = order.isBuy() ? "Buy" : "Sell";
		String baseCcy = order.getCcyPair().substring(0, 3);
		String termCcy = order.getCcyPair().substring(4, 7);
		String variableCcy = order.getDealtCcy().equals(baseCcy) ? termCcy : baseCcy;
		strBuilder.append(org).append(WHITE_SPACE);
		strBuilder.append(isLinkedOrder? " Linked " : "");
		strBuilder.append(orderType).append(" Order (").append(order.get_id()).append(") to ").append(side).append(WHITE_SPACE);
		strBuilder.append(order.getDealtAmt()).append(WHITE_SPACE);
		strBuilder.append(order.getDealtCcy()).append(WHITE_SPACE).append("vs").append(WHITE_SPACE).append(variableCcy).append(WHITE_SPACE);
		
		return strBuilder;

	}

	public static OMSResponse getOMSResponse( Order order,  OMSEvent omsEvent, OMSState oldState, OMSState newState ,OMSOrderParam updatedOrderParams)
	{
		return getOMSResponse( order,  omsEvent, oldState, newState , updatedOrderParams, null);
	}

	public static OMSResponse getOMSResponse( Order order,  OMSEvent omsEvent, OMSState oldState, OMSState newState ,OMSOrderParam updatedOrderParams, String rejectReason)
	{
		return getOMSResponse( order,  omsEvent, oldState, newState , updatedOrderParams, rejectReason,null);
	}

	public static OMSResponse getOMSResponse( Order order,  OMSEvent omsEvent, OMSState oldState, OMSState newState ,OMSOrderParam updatedOrderParams, String rejectReason, OrderAction orderAction)
	{
		StringBuilder strBuilder = getOrderDetails(order);
		
		OMSOrderParam params  = null;
		switch ( omsEvent )
		{
		case IMPORTED :
			params = OMSUtil.convertToParam(null, order);
			strBuilder.append("Imported");
			break;
		case CREATED :
			params = OMSUtil.convertToParam(null, order);
			strBuilder.append("Created");
			break;
		case UPDATED :
			if ( updatedOrderParams != null )
			{
				params = updatedOrderParams;
				String org = order.getCounterPartyAOrg() != null ? (order.getCounterPartyAOrg() + "'s") : "PlaceHolder";
				strBuilder = new StringBuilder();
				strBuilder.append(org).append(" Order (").append(order.get_id()).append(")");
				if ( updatedOrderParams.getCptyAOrg() != null )
				{
					strBuilder.append(" Customer updated to ").append(updatedOrderParams.getCptyAOrg());
				}
				else if ( updatedOrderParams.getBaseAmt() != null )
				{
					strBuilder.append(" Base Amount updated to ").append(updatedOrderParams.getBaseAmt());
				}
				else if ( updatedOrderParams.getFillPrice() != null )
				{
					strBuilder.append(" Fill Price updated to ").append(Double.compare(updatedOrderParams.getFillPrice(),-1.0)==0 ? "" : updatedOrderParams.getFillPrice());
				}
				else if ( updatedOrderParams.getOrderPrice() != null )
				{
					strBuilder.append(" Order Price updated to ").append(updatedOrderParams.getOrderPrice());
				}
				else if ( updatedOrderParams.getDealtAmt() != null )
				{
					strBuilder.append(" Order Amount updated to ").append(updatedOrderParams.getDealtAmt());
				}
				else if ( updatedOrderParams.getNotes() != null )
				{
					strBuilder.append(" Notes updated to ").append(updatedOrderParams.getNotes());
				}
				else if ( updatedOrderParams.getCustNotes() != null )
				{
					strBuilder.append(" Customer Notes updated to ").append(updatedOrderParams.getCustNotes());
				}
				else if ( updatedOrderParams.getVenueBMR() != null )
				{
					strBuilder.append(" Fixing rate updated to ").append(updatedOrderParams.getVenueBMR());
				}
				else
				{
					strBuilder.append(" Updated");
				}
			}
			if(order.getAmendParameters() != null){
				if(order.getAmendParameters().getDealtAmt() != null){
					strBuilder.append(" Order Amount updated to ").append(order.getAmendParameters().getDealtAmt());
				}
				if(order.getAmendParameters().getPrice() != null){
					strBuilder.append(" Rate updated to ").append(order.getAmendParameters().getPrice());
				}
			}
			break;
		case AUTO_FAILURE :
			strBuilder.append(" Rejected from EMS");
			break;
		case CREDIT_FAIL :
			strBuilder.append(" "+CreditLimit.ERROR_INSUFFICIENT_CREDIT);
			break;
		case AUTO_SUCCESS :
			strBuilder.append(" sent to EMS for Auto Execution");
			break;
		case STATE_CHANGED :
			switch ( newState )
			{
			case DRAFT :
				strBuilder.append("Moved to Draft");
				break;
			case ACTIVE :
				if ( oldState == OMSState.AUTO )
				{
					strBuilder.append("cancelled from EMS");
				}
				else
				{
					strBuilder.append("is now Active");
				}
				break;
			case TRIGGERED :
				strBuilder.append("is Triggered. Fill Price : ").append(order.getFilledPrice());
				break;
			case FILLED :
				if(OMSState.CREDIT_FAIL.equals(oldState)) {
					strBuilder.append("Rejected Order");
				}else{
					strBuilder.append("was Filled at ").append(order.getFilledPrice());
				}
				break;
			case NOTIFIED :
				params = OMSUtil.convertToParam(null, order);
				if(OMSState.CREDIT_FAIL.equals(oldState)) {
					if(orderAction.equals(OrderAction.CREDIT_OVERRIDE_ACCEPT)) {
						strBuilder.append("Allowed Credit Overshoot and Notified");
					}else{
						strBuilder.append("Skipped Credit Check and Notified");
					}
				}else{
					strBuilder.append("was Notified");
				}
				break;
			case AUTO :
				strBuilder.append("sent to EMS for Auto Execution");
				break;
			case ARCHIVED :
				strBuilder.append("Archived");
				break;
			case EXPIRED :
				strBuilder.append("Expired");
				break;
			case CANCELLED :
				params = OMSUtil.convertToParam(null, order);
				strBuilder.append("Cancelled");
				break;
			default :
				strBuilder.append(" State changed from ").append(oldState.toString()).append(" ").append(newState.toString());
			}
			break;
		case PASS_SENT :
			strBuilder.append(" Passed ");
			if ( order.getToOrg() != null )
			{
				strBuilder.append(" to ").append(order.getToOrg());
			}
			break;
		case PASS_RECEIVED :
			params = OMSUtil.convertToParam(null, order);
			strBuilder.append(" Received");
			if ( order.getFromOrg() != null )
			{
				strBuilder.append(" from ").append(order.getFromOrg());
			}
			break;
		case PASS_CANCEL_RECEIVED :
			strBuilder.append(" Cancel Received");
			if ( order.getFromOrg() != null )
			{
				strBuilder.append(" from ").append(order.getFromOrg());
			}
			break;
		case PASS_AMEND_RECEIVED :
			strBuilder.append(" Amend Received");
			if ( order.getFromOrg() != null )
			{
				strBuilder.append(" from ").append(order.getFromOrg());
			}
			AmendParameters amendParameters = order.getAmendParameters();
			if(amendParameters.getDealtAmt() != null){
				strBuilder.append(" new amount ").append(amendParameters.getDealtAmt());
			}
			if(amendParameters.getPrice() != null){
				strBuilder.append(" new rate ").append(amendParameters.getPrice());
			}
			break;
		case PASS_ACCEPTED :
			strBuilder.append(" Accepted");
			if ( order.getToOrg() != null )
			{
				strBuilder.append(" by ").append(order.getToOrg());
			}
			break;
		case PASS_REJECTED :
			strBuilder.append(" Rejected");
			if ( order.getToOrg() != null )
			{
				strBuilder.append(" by ").append(order.getToOrg());
			}
			if(rejectReason != null){
				strBuilder.append(", ").append(rejectReason);
			}
			break;
		case PASS_AMEND_ACCEPTED :
			strBuilder.append(" Amend Accepted");
			if ( order.getToOrg() != null )
			{
				strBuilder.append(" by ").append(order.getToOrg());
			}
			break;
		case PASS_AMEND_REJECTED :
			strBuilder.append(" Amend Rejected");
			if ( order.getToOrg() != null )
			{
				strBuilder.append(" by ").append(order.getToOrg());
			}
			if(rejectReason != null){
				strBuilder.append(", ").append(rejectReason);
			}
			break;
		case PASS_CANCEL_ACCEPTED :
			strBuilder.append(" Cancel Accepted");
			if ( order.getToOrg() != null )
			{
				strBuilder.append(" by ").append(order.getToOrg());
			}
			break;
		case PASS_CANCEL_REJECTED :
			strBuilder.append(" Cancel Rejected");
			if ( order.getToOrg() != null )
			{
				strBuilder.append(" by ").append(order.getToOrg());
			}
			if(rejectReason != null){
				strBuilder.append(", ").append(rejectReason);
			}
			break;
		case PASS_CANCELLED :
			strBuilder.append(" Cancelled");
			if ( order.getToOrg() != null )
			{
				strBuilder.append(" by ").append(order.getToOrg());
			}
			break;
		case PASS_FILLED :
			strBuilder.append(" Filled");
			if ( order.getToOrg() != null )
			{
				strBuilder.append(" by ").append(order.getToOrg());
				strBuilder.append(" at rate ").append(order.getFilledPrice());
				strBuilder.append(" with Benchmark rate of ").append(order.getVenueBMR());
			}
			break;
		case PASS_WITHDRAWN :
			strBuilder.append(" Taken back");
			if ( order.getFromOrg() != null )
			{
				strBuilder.append(" by ").append(order.getFromOrg());
			}
			else if ( order.getToOrg() != null )
			{
				strBuilder.append(" from ").append(order.getToOrg());
			}
			break;
		case PASS_WITHDRAW_FAILED :
			strBuilder.append(" Failed to take back");
			if ( order.getToOrg() != null )
			{
				strBuilder.append(" from ").append(order.getToOrg());
			}
			break;
		case PASS_SENT_FAILED :
			strBuilder.append(" Failed to send Pass");
			if ( order.getToOrg() != null )
			{
				strBuilder.append(" to ").append(order.getToOrg());
			}
			break;
		}
			
		OMSResponse response = new OMSResponse(order.get_id(), strBuilder.toString(), omsEvent, params);		
		HashMap<String,String> properties = new HashMap<String, String>();
		if(oldState !=null) {
			properties.put(OLD_STATE, oldState.name());
		}
		if(newState !=null) {
			properties.put(NEW_STATE, newState.name());
		}
		response.setProperties(properties);
		return response;
	}
	
	@Override
	public String toString()
	{
		StringBuilder strBuilder = new StringBuilder();
		strBuilder.append("Event : ").append(event);
		strBuilder.append(", OMSOrderParam : ").append(orderParams);
		strBuilder.append(", Response Msg: ").append(responseString);
		return strBuilder.toString();
	}

}
