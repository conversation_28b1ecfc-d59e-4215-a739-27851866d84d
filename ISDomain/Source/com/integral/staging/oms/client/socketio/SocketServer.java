package com.integral.staging.oms.client.socketio;

import static com.integral.staging.oms.client.socketio.Utils.getClientString;

import java.util.*;

import com.corundumstudio.socketio.AckRequest;
import com.corundumstudio.socketio.Configuration;
import com.corundumstudio.socketio.SocketIOClient;
import com.corundumstudio.socketio.SocketIOServer;
import com.corundumstudio.socketio.VoidAckCallback;
import com.corundumstudio.socketio.listener.ConnectListener;
import com.corundumstudio.socketio.listener.DataListener;
import com.corundumstudio.socketio.listener.DisconnectListener;
import com.integral.commons.Tuple;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.staging.oms.OMSConstants;
import com.integral.staging.oms.OMSOrderService;
import com.integral.staging.oms.OMSUtil;
import com.integral.staging.oms.config.OMSConfig;
import com.integral.staging.oms.model.OMSAggregateOrderParam;
import com.integral.staging.oms.model.OMSOrderParam;
import com.integral.staging.oms.model.OMSOrderQueryParam;
import com.integral.user.User;
import com.integral.util.CompositeKeys;

/***
 * SocketIO server for OMS
 * <AUTHOR>
 *
 */
public class SocketServer
{
	private Log log = LogFactory.getLog(SocketServer.class);
	private SocketIOServer server;
	private static final String CONTEXT = "/OMSSocket";

	public SocketIOServer getServer()
	{
		return server;
	}

	public SocketServer()
	{
		try
		{
			Configuration config = new Configuration();
			config.setPort(OMSConfig.getInstance().getSocketServerPort());
			config.setMaxHttpContentLength(OMSConfig.getInstance().getMaxHttpContentLength());
			config.setHttpCompression(false); // required from v1.7.9 onwards
			config.setWebsocketCompression(false); // required from v1.7.9 onwards
			config.setContext(CONTEXT);
			
			server = new SocketIOServer(config);
			initServer();
		}
		catch ( Exception e )
		{
			log.error(OMSConstants.OMS_LOG_PREFIX + "Failed to start socket server");
		}
	}

	private void initServer() throws InterruptedException
	{

		server.addDisconnectListener(new DisconnectListener() {

			@Override
			public void onDisconnect( SocketIOClient client )
			{
				try
				{
					log.info(OMSConstants.OMS_LOG_PREFIX + "on Disconnection:-" + getClientString(client));
					ClientSession cachedSession = ClientSessionCache.getInstance().getClientSession(client);
					if ( cachedSession != null )
					{
						ClientSessionCache.getInstance().remove(client);
					}
				}
				catch ( Exception e )
				{
					log.error(OMSConstants.OMS_LOG_PREFIX + "Error in processing Event: Disconnect", e);
				}
			}
		});

		server.addConnectListener(new ConnectListener() {

			@Override
			public void onConnect( SocketIOClient client )
			{
				log.info(OMSConstants.OMS_LOG_PREFIX + "on Connection:-" + getClientString(client));
			}
		});

		server.addEventListener(OMSConstants.OMS_SOCKET_EVENT_AUTH, SocketRequest.class, new DataListener<SocketRequest>() {
			@Override
			public void onData( final SocketIOClient client, SocketRequest request, AckRequest ackSender ) throws Exception
			{
				try
				{
					//For now every auth request will close old connection and create a new one
					log.info(OMSConstants.OMS_LOG_PREFIX + "on Authentication:-" + getClientString(client));
					Tuple<User, String> authResult = OMSValidator.validateAuth(request);

					SocketResponse socketResponse = null;
					if ( authResult.first != null )
					{
						//First remove all connections (Should be 1) for the user 
						CompositeKeys keyTuple = ClientSessionCache.getInstance().getClientSessionCacheKey(request);
						Set<ClientSession> sessionsForUser = ClientSessionCache.getInstance().getResources(keyTuple);
						for ( ClientSession session : sessionsForUser )
						{
							log.info(OMSConstants.OMS_LOG_PREFIX + "Removing session and sending force logout:-" + getClientString(session.getClientConnection()));
							session.getClientConnection().sendEvent(OMSConstants.OMS_SOCKET_FORCE_LOGOUT, new DefaultVoidCallBack(), "");
							ClientSessionCache.getInstance().remove(session.getClientConnection());
						}
						//Now add the new connection
						ClientSessionCache.getInstance().addToCache(keyTuple, client, authResult.first);
						socketResponse = new SocketResponse(SocketResponse.Status.OK, null);

					}
					else
					{
						socketResponse = new SocketResponse(SocketResponse.Status.ERROR, authResult.second);
					}
					client.sendEvent(OMSConstants.OMS_SOCKET_EVENT_AUTH, new DefaultVoidCallBack(), socketResponse);
				}
				catch ( Exception e )
				{
					log.error(OMSConstants.OMS_LOG_PREFIX + "Error in processing Event: " + OMSConstants.OMS_SOCKET_EVENT_AUTH, e);
				}
			}
		});

		server.addEventListener(OMSConstants.OMS_SOCKET_EVENT_ALL_ORDERS, OMSRequest.class, new DataListener<OMSRequest>() {
			@Override
			public void onData( final SocketIOClient client, OMSRequest omsRequest, final AckRequest ackRequest )
			{
				try
				{
					ClientSession cachedSession = ClientSessionCache.getInstance().getClientSession(client);
					if ( cachedSession != null )
					{
						OMSOrderQueryParam queryParams = omsRequest.getQueryParam();
						Map<String, List<OMSOrderParam>> orderResponse = OMSOrderService.queryOrders(cachedSession.getUser(), queryParams);
						// send message back to client with ack callback WITH data
						client.sendEvent(OMSConstants.OMS_SOCKET_EVENT_ALL_ORDERS, new DefaultVoidCallBack(), orderResponse);
					}

				}
				catch ( Exception e )
				{
					log.error(OMSConstants.OMS_LOG_PREFIX + "Error in processing Event: " + OMSConstants.OMS_SOCKET_EVENT_ALL_ORDERS, e);
				}

			}
		});

		server.addEventListener(OMSConstants.OMS_SOCKET_EVENT_AGGREGATE_ORDERS, OMSRequest.class, new DataListener<OMSRequest>() {
			@Override
			public void onData( final SocketIOClient client, OMSRequest omsRequest, final AckRequest ackRequest )
			{
				try
				{
					ClientSession cachedSession = ClientSessionCache.getInstance().getClientSession(client);
					if ( cachedSession != null )
					{
						OMSOrderQueryParam queryParams = omsRequest.getQueryParam();
						Collection<OMSAggregateOrderParam> orderResponse = OMSOrderService.queryAggregateOrders(cachedSession.getUser(), queryParams);
						// send message back to client with ack callback WITH data
						client.sendEvent(OMSConstants.OMS_SOCKET_EVENT_AGGREGATE_ORDERS, new DefaultVoidCallBack(), orderResponse);
					}

				}
				catch ( Exception e )
				{
					log.error(OMSConstants.OMS_LOG_PREFIX + "Error in processing Event: " + OMSConstants.OMS_SOCKET_EVENT_AGGREGATE_ORDERS, e);
				}

			}
		});

		server.addEventListener(OMSConstants.OMS_SOCKET_EVENT_MARKET_RATES, OMSRequest.class, new DataListener<OMSRequest>() {
			@Override
			public void onData( final SocketIOClient client, OMSRequest omsRequest, final AckRequest ackRequest )
			{
				try
				{
					ClientSession cachedSession = ClientSessionCache.getInstance().getClientSession(client);
					if ( cachedSession != null )
					{
						OMSOrderQueryParam queryParams = omsRequest.getQueryParam();
						for ( String ccyPair : queryParams.getCurrencyPairs() )
						{
							cachedSession.addProperty("ccyPair", ccyPair);
						}
					}
				}
				catch ( Exception e )
				{
					log.error(OMSConstants.OMS_LOG_PREFIX + "Error in processing Event: " + OMSConstants.OMS_SOCKET_EVENT_MARKET_RATES, e);
				}

			}
		});

		server.addEventListener(OMSConstants.OMS_SOCKET_EVENT_MARKET_RATES_FULL_REFRESH, OMSRequest.class, new DataListener<OMSRequest>() {
			@Override
			public void onData( final SocketIOClient client, OMSRequest omsRequest, final AckRequest ackRequest )
			{
				try
				{
					ClientSession cachedSession = ClientSessionCache.getInstance().getClientSession(client);
					if ( cachedSession != null )
					{
						List<String> ccyPairs = omsRequest.getQueryParam() != null && omsRequest.getQueryParam().getCurrencyPairs() != null ? omsRequest.getQueryParam().getCurrencyPairs() : new ArrayList<String>();
						if ( ccyPairs != null )
						{
							String[] ccyPairsArr = ccyPairs.toArray(new String[ccyPairs.size()]);
							Map<String, Double> marketRateResponse = OMSUtil.getMarketRates(cachedSession.getUser(), ccyPairsArr, null);

							cachedSession.getClientConnection().sendEvent(OMSConstants.OMS_SOCKET_EVENT_MARKET_RATES, new DefaultVoidCallBack(), marketRateResponse);
						}
					}
				}
				catch ( Exception e )
				{
					log.error(OMSConstants.OMS_LOG_PREFIX + "Error in processing Event: " + OMSConstants.OMS_SOCKET_EVENT_MARKET_RATES_FULL_REFRESH, e);
				}
			}
		});

		server.start();
	}

}

class DefaultVoidCallBack extends VoidAckCallback
{

	@Override
	protected void onSuccess()
	{
		// TODO Auto-generated method stub

	}
}