package com.integral.staging.oms.client.socketio;

import static com.integral.staging.oms.client.socketio.Utils.getClientString;


import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

import com.corundumstudio.socketio.SocketIOClient;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.staging.oms.OMSConstants;
import com.integral.user.User;
import com.integral.util.CompositeKeys;

public class ClientSessionCache
{
	private static class ClientSessionCacheHolder
	{
		private static final ClientSessionCache INSTANCE = new ClientSessionCache();
	}

	public  static ClientSessionCache getInstance()
	{
		return ClientSessionCacheHolder.INSTANCE;
	}
	
    private ConcurrentHashMap<CompositeKeys, ClientSession> allBrowserSessions = new ConcurrentHashMap<CompositeKeys, ClientSession>();

    private ConcurrentHashMap<UUID, ClientSession> allIOClients = new ConcurrentHashMap<UUID, ClientSession>();
   
    private ConcurrentHashMap<String, List<UUID>> orgConnectionsMap = new ConcurrentHashMap<String, List<UUID>>();

    private final Log log = LogFactory.getLog(ClientSessionCache.class);

    /**
     * Register the {@link com.corundumstudio.socketio.SocketIOClient} with a
     * {@link com.integral.spaces.util.CompositeKeys} This map is used to lookup
     * resources and transmit messages
     *
     * @param sessionKey
     * @param ioClient
     */
    public  ClientSession addToCache(CompositeKeys sessionKey,
                                           SocketIOClient ioClient , User user) {

        if (log.isDebugEnabled()) {
            log.debug("Attempting to add client for:" + sessionKey + " client: "
                    + getClientString(ioClient));
        }

        ClientSession clientSession = allBrowserSessions.get(sessionKey);
        if (clientSession == null) {
            clientSession = new ClientSession(ioClient, sessionKey ,user);
            ClientSession clientSessionOld = allBrowserSessions.putIfAbsent(
                    sessionKey, clientSession);
            if (clientSessionOld != null) {
                clientSession = clientSessionOld;
            } else {
                allIOClients.put(ioClient.getSessionId(), clientSession);
                addSessionToOrgMap(sessionKey.getKeysAtIndex(0), ioClient.getSessionId());
            }
        }
        return clientSession;
    }

	private  void addSessionToOrgMap( String org, UUID uuid )
	{
		List<UUID> uuidList = orgConnectionsMap.get(org);
		if ( uuidList == null )
		{
			uuidList = new ArrayList<UUID>();
		}
		uuidList.add(uuid);
		orgConnectionsMap.put(org, uuidList);
	}
	

	private  void removeSessionFromOrgMap( String org, UUID uuid )
	{
		List<UUID> uuidList = orgConnectionsMap.get(org);
		if ( uuidList != null )
		{
			uuidList.remove(uuid);
		}
	}
	
    /**
     * Retrieve the list of ClientSession with the given session key.
     *
     * @param sessionKey
     * @return
     */
    public  Set<ClientSession> getResources(CompositeKeys sessionKey) {
        Set<ClientSession> clientSessionList = new HashSet<ClientSession>();

        ClientSession clientSession = allBrowserSessions.get(sessionKey);
        if (clientSession != null) {
            clientSessionList.add(clientSession);
        }

        return clientSessionList;
    }

    /**
     * Remove the specified SocketIOClient from the Browser sessions.
     *
     * @param client
     * @return an instance of removed
     * {@link com.integral.unity.session.ClientSession} corresponding to
     * this client, Null if none is present.
     */
    public  ClientSession remove(SocketIOClient client) {
        ClientSession clientSession = allIOClients.get(client.getSessionId());
        if (clientSession == null) {
            log.warn("Call for remove does not have an associated entry, possible bug or the browser reinitiated a connection");
            return null;
        }
        return remove(clientSession, client);
    }

    public  CompositeKeys getClientSessionCacheKey(SocketRequest authRequest) {
        return CompositeKeys.getCompositeKeys(authRequest.getUserOrg(),
                authRequest.getUserName());
    }

    public  CompositeKeys getClientSessionCacheKey(String... keys) {
        return CompositeKeys.getCompositeKeys(keys);
    }

    private  ClientSession remove(ClientSession clientSession,
                                        SocketIOClient ioClient) {

        if (log.isDebugEnabled()) {
            log.debug("Attempting to remove mapping for:" + clientSession
                    + " with client:" + getClientString(ioClient));
        }

        ClientSession removedSession = allBrowserSessions.remove(clientSession
                .getSessionKey());
        if (removedSession == null) {
            log.warn("No entry found for pair:" + clientSession
                    + " returning back");
        } else {
            allIOClients.remove(ioClient.getSessionId());
            removeSessionFromOrgMap(clientSession.getSessionKey().getKeysAtIndex(0), ioClient.getSessionId());
        }

        log.info("Successfully removed client session for:" + clientSession);
        return removedSession;
    }

    public  ClientSession getClientSession(SocketIOClient client) {
        return allIOClients.get(client.getSessionId());
    }

    public  Collection<ClientSession> getAllConnectedSessions() {
        return allIOClients.values();
    }
    
	public  List<ClientSession> getAllSessionsForOrg( String org )
	{
		List<ClientSession> ioSessions = new ArrayList<ClientSession>();
		if ( org == null )
		{
			log.info(OMSConstants.OMS_LOG_PREFIX + ".getAllSessionsForOrg: Org is null");
			return ioSessions;
		}
		List<UUID> orgSessionList = orgConnectionsMap.get(org);

		if ( orgSessionList != null )
		{
			for ( UUID uuid : orgSessionList )
			{
				ioSessions.add(allIOClients.get(uuid));
			}
		}

		return ioSessions;
	}
}

