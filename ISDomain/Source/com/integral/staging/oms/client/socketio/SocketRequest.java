package com.integral.staging.oms.client.socketio;


public class SocketRequest 
{

	String authToken;
	String userName;
    String userOrg;


    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserOrg() {
        return userOrg;
    }

    public void setUserOrg(String userOrg) {
        this.userOrg = userOrg;
    }

    public String getAuthToken()
	{
		return authToken;
	}

	public void setAuthToken( String authToken )
	{
		this.authToken = authToken;
	}
	
    @Override
    public String toString() {
        return "Request{" +
                "userName='" + userName + '\'' +
                ", userOrg='" + userOrg + '\'' +
                '}';
    }
}
