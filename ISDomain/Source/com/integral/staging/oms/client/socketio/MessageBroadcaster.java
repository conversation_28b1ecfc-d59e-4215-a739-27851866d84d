package com.integral.staging.oms.client.socketio;


import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.staging.oms.OMSConstants;
import com.integral.staging.oms.OMSUtil;


public class MessageBroadcaster {
    private ScheduledExecutorService consumerThreadPool;
    private static final int POOL_SIZE = 2;
    private static final int POLL_DELAY = 500;
    private static final int POLL_TIME = 500;
    private static Log log = LogFactory.getLog(MessageBroadcaster.class);
    
    public MessageBroadcaster() {
        consumerThreadPool = Executors.newScheduledThreadPool(
        		POOL_SIZE, new ThreadFactory() {
                    private volatile int threadNum;

                    public Thread newThread(Runnable r) {
                        threadNum++;
                        
                        return new Thread(r, "MB" + threadNum);
                    }
                });

        for (int i = 0; i < POOL_SIZE; i++) {
            consumerThreadPool.scheduleWithFixedDelay(new BroadcasterThread(), POLL_DELAY, POLL_DELAY, TimeUnit.MILLISECONDS);
        }
    }

	private class BroadcasterThread implements Runnable
	{

		public BroadcasterThread()
		{

		}

		@Override
		public void run()
		{
			long currentTime = System.currentTimeMillis();
			for ( ClientSession clientSession : ClientSessionCache.getInstance().getAllConnectedSessions() )
			{
				try {
				if ( currentTime - clientSession.getLastExecutionTime() > POLL_TIME )
				{
					clientSession.setLastExecutionTime(currentTime);
					Set<String> ccyPairs = clientSession.getProperty("ccyPair");
					if ( ccyPairs != null )
					{
						String[] ccyPairsArr =  ccyPairs.toArray(new String[ccyPairs.size()]);
						Map<String, Double> marketRateResponse = OMSUtil.getMarketRates(clientSession.getUser(),ccyPairsArr, null);

						clientSession.getClientConnection().sendEvent(OMSConstants.OMS_SOCKET_EVENT_MARKET_RATES, new DefaultVoidCallBack(), marketRateResponse);
					}
				}
				}catch(Exception e) {
					log.error(OMSConstants.OMS_LOG_PREFIX + " Exception while running Message broadcaster" , e);
				}

			}
		}
	}
    }
