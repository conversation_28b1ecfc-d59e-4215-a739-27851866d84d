package com.integral.staging.oms.client.socketio;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.atomic.AtomicLong;

import com.corundumstudio.socketio.SocketIOClient;
import com.integral.user.User;
import com.integral.util.CompositeKeys;


public class ClientSession
{
    private CompositeKeys sessionKey;
    
    private SocketIOClient clientConnection;
    
    private User user;
    
    private AtomicLong lastExecutionTime;
    
    private ConcurrentHashMap<String, Set<String>> properties;

	public ClientSession(SocketIOClient clientConnection, CompositeKeys keyTuple , User user) {
        this.clientConnection = clientConnection;
        this.sessionKey = keyTuple;
        this.user = user;
        this.lastExecutionTime = new AtomicLong(-1l);
        this.properties = new ConcurrentHashMap<String, Set<String>>();
    }

    public SocketIOClient getClientConnection() {
        return clientConnection;
    }

    public CompositeKeys getSessionKey() {
        return sessionKey;
    }

    public User getUser()
	{
		return user;
	}

    public void setLastExecutionTime(long time) {
        lastExecutionTime.set(time);
    }

    public long getLastExecutionTime() {
        return lastExecutionTime.get();
    }
    

    public void addProperty(String key, String value) {
        Set<String> values = properties.get(key);
        if (values == null) {
            values = new CopyOnWriteArraySet<String>();
            properties.put(key, values);
        }
        values.add(value);
    }

    public Set<String> getProperty(String key) {
        return properties.get(key);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ClientSession that = (ClientSession) o;

        if (!clientConnection.equals(that.clientConnection)) return false;
        if (!sessionKey.equals(that.sessionKey)) return false;

        return true;
    }
}
