package com.integral.staging.oms.client.socketio;

import java.util.HashMap;


public class SocketResponse 
{

	public enum Status {
		    OK,
		    ERROR
	};
	
	Status status;
	
	String responseString;

    HashMap<String,String> properties;

    public SocketResponse(String responseString) {
        this.responseString = responseString;
    }
    
    public SocketResponse( Status status, String responseString) {

        this.responseString = responseString;
        this.status = status;
    }

    public String getResponseString() {
        return responseString;
    }

    public HashMap<String, String> getProperties() {
        return properties;
    }

    public void setProperties(HashMap<String, String> properties) {
        this.properties = properties;
    }
    
    public void addProperty(String key , String value) {
    	if(this.properties == null ) {
    		this.properties = new HashMap<String, String>();
    	}
    	this.properties.put(key, value);
    }
    
    public Status getStatus()
	{
		return status;
	}

	public void setStatus( Status status )
	{
		this.status = status;
	}
}
