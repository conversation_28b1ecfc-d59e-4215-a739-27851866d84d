package com.integral.staging.oms.client.socketio;

import com.corundumstudio.socketio.SocketIOClient;

public class Utils
{

	public static String getClientString(SocketIOClient client) {
		StringBuilder sbr = new StringBuilder("sId:" + client.getSessionId())
				.append(" ,currentTransport:"
						+ client.getTransport().getValue())
				.append(" ,address:" + client.getHandshakeData().getAddress())
				.append(" ,headers:" + client.getHandshakeData().getHeaders())
				.append(" ,url:" + client.getHandshakeData().getUrl())
				.append(" ,urlParams:"
						+ client.getHandshakeData().getUrlParams())
				.append(" ,Xdomain:" + client.getHandshakeData().isXdomain())
				.append(" ,time:" + client.getHandshakeData().getTime());
		return sbr.toString();
	}
}
