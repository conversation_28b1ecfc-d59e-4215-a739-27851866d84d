package com.integral.staging.oms.client.socketio;

import com.integral.cas.CommonAuthenticationServiceC;
import com.integral.commons.Tuple;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.staging.oms.OMSConstants;
import com.integral.user.Organization;
import com.integral.user.User;

public class OMSValidator
{
	private static Log log = LogFactory.getLog(OMSValidator.class);
	
	public  static Tuple<User, String> validateAuth(SocketRequest request) {
		Tuple<User,String> tuple = new Tuple<User, String>();
		try {
			Organization org ;
			User user;
			if(request.getUserOrg() == null) {
				tuple.second = "Invalid OrgName";
				return tuple;
			} else {
				org = ReferenceDataCacheC.getInstance().getOrganization(request.getUserOrg());
				if(org == null) {
					tuple.second = "Invalid OrgName";
					return tuple;
				}
			}
			
			if(request.getUserName() == null) {
				tuple.second = "Invalid UserName";
				return tuple;
			} else {
				user = org.getUser(request.getUserName());
				if(user == null) {
					tuple.second = "Invalid UserName";
					return tuple;
				}else if(!user.hasPermission(OMSConstants.OMS_PERMISSION_TRADER_USER) && !user.hasPermission(OMSConstants.OMS_PERMISSION_CHIEF_DEALER_USER)) {
					tuple.second = OMSConstants.INSUFFICIENT_PERMISSION;
					return tuple;
				}
			}
		
			
			if(request.getAuthToken() == null || !CommonAuthenticationServiceC.getInstance().isTokenValid(request.getAuthToken())) {
				tuple.second = OMSConstants.INVALID_TOKEN;
				return tuple;
			}
			
			tuple.first = user;
			return tuple;
		}catch(Exception e) {
			log.error("Error While validating Authentication" , e);
			return tuple;
		}
	}
}
