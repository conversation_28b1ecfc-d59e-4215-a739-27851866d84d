package com.integral.staging.oms.client;

import java.util.ArrayList;
import java.util.List;

import com.corundumstudio.socketio.SocketIOClient;
import com.corundumstudio.socketio.VoidAckCallback;
import com.integral.is.management.monitor.AMQPSender;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.management.NotificationEvent;
import com.integral.management.NotificationEventTypes;
import com.integral.staging.Order;
import com.integral.staging.StagingAreaPersistenceService;
import com.integral.staging.oms.OMSConstants;
import com.integral.staging.oms.client.socketio.ClientSession;
import com.integral.staging.oms.client.socketio.ClientSessionCache;
import com.integral.staging.oms.client.socketio.OMSResponse;
import com.integral.staging.oms.client.socketio.SocketResponse;
import com.integral.staging.oms.config.OMSConfig;
import com.integral.staging.oms.model.OMSState;
import com.integral.system.runtime.RuntimeFactory;

public class OMSNotifier
{

	private static ArrayList<String> customerNotificationStates = new ArrayList<String>();
	static {
		customerNotificationStates.add(OMSState.DRAFT.name());
		customerNotificationStates.add(OMSState.ACTIVE.name());
		customerNotificationStates.add(OMSState.NOTIFIED.name());
	}
	private static Log log = LogFactory.getLog(OMSNotifier.class);
	/**
	 * To notify all logged in users on this org
	 * @param org org
	 * @param response response
	 */
	public static void notifyAllClients( String org, OMSResponse response )
	{

		try
		{

			if ( response != null )
			{
				if ( log.isDebugEnabled() )
				{
					log.debug(OMSConstants.OMS_LOG_PREFIX + ".notifyAllClients : Notification for org : " + org + " , Notification : " + response);
				}
				notifyGM(org, response);

				if ( OMSConfig.getInstance().isSocketServerEnabled() )
				{
					List<ClientSession> clientSessions = ClientSessionCache.getInstance().getAllSessionsForOrg(org);
					if( response.getOrderParams () != null){
						clientSessions.addAll(ClientSessionCache.getInstance().getAllSessionsForOrg(response.getOrderParams ().getCptyAOrg()));
					}
					if ( clientSessions != null )
					{
						for ( ClientSession clientSession : clientSessions )
						{
							try
							{
								SocketIOClient client = clientSession.getClientConnection();
								client.sendEvent("notification", new VoidAckCallback() {
									@Override
									public void onSuccess()
									{
									}
								}, response);
							}
							catch ( Exception e )
							{
								log.error(OMSConstants.OMS_LOG_PREFIX + ".notifyAllClients : Error while notifying clients on websocket", e);
							}

						}
					}

				}
			}

		}
		catch ( Exception e )
		{
			log.error(OMSConstants.OMS_LOG_PREFIX + ".notifyAllClients : Error while notifying clients", e);
		}
	}

	private static void notifyGM(String org, OMSResponse response) {
		try {
			Order order = StagingAreaPersistenceService.queryOrderById(response.getOrderId(), org);
			if(order != null) {
				AMQPSender sender = AMQPSender.getInstance();
				NotificationEvent eventMessage = new NotificationEvent(NotificationEventTypes.OMS, System.currentTimeMillis(), RuntimeFactory.getServerRuntimeMBean().getVirtualServer().getName(), "OMS", "OMS");
				eventMessage.addParameter("OrderId", response.getOrderId());
				eventMessage.addParameter("Event", response.getEvent());
				eventMessage.addParameter("BrokerOrg", org);
				eventMessage.addParameter("EventMessage", response.getResponseString());
				eventMessage.addParameter("ClientOrderId", order.getClientOrderId());
				eventMessage.addParameter("BUY", order.isBuy());
				eventMessage.addParameter("CcyPair", order.getCcyPair());
				eventMessage.addParameter("DealtCcy", order.getDealtCcy());
				eventMessage.addParameter("DealtAmt", order.getDealtAmt());
				eventMessage.addParameter("CustOrder", order.isCustOrder());
				eventMessage.addParameter("OrderType", order.getType().name());
				eventMessage.addParameter("OrderPrice", order.getPrice());
				eventMessage.addParameter("CptyA", order.getCounterPartyA());
				eventMessage.addParameter("CptyAOrg", order.getCounterPartyAOrg());
				eventMessage.addParameter("Tif", order.getTif().name());
				eventMessage.addParameter("ExpiryTime", order.getExpiryTime());
				eventMessage.addParameter("OrderState", OMSState.fromStatusCode(order.getState()).toString());
				eventMessage.addParameter("FillPrice", order.getFilledPrice());
				eventMessage.addParameter("MatchedPrice", order.getMatchedPrice());
				eventMessage.addParameter("FilledAmount", order.getFilledAmount());
				eventMessage.addParameter("LockedByUser", order.getLockedByUser());
				eventMessage.addParameter("Notes", order.getNotes());
				eventMessage.addParameter("CustNotes", order.getCustNotes());
				eventMessage.addParameter("PortfolioId", order.getOrderBatchId());
				eventMessage.addParameter("TriggerTime", order.getTriggerTime());
				eventMessage.addParameter("TradeId", order.getTradeId());
				eventMessage.addParameter("PrimaryOrder", order.isPrimaryOrder());
				eventMessage.addParameter("ToOrg", order.getToOrg());
				eventMessage.addParameter("FromOrg", order.getFromOrg());
				eventMessage.addParameter("AcceptedForPass", order.isAcceptedForPassing());
				eventMessage.addParameter("SentForPass", order.isSentForPassing());
				eventMessage.addParameter("EmsOrderId", order.getEmsOrderId());
				eventMessage.addParameter("CptyBOrg", order.getCounterPartyBOrg());
				eventMessage.addParameter("EmsOrder", order.isEMSOrder());
				eventMessage.addParameter("FixingReference", order.getFixingReference());
				eventMessage.addParameter("FixingTime", order.getFixingTime());
				eventMessage.addParameter("TransactionId", order.getTransactionId());
				eventMessage.addParameter("VenueBMR", order.getVenueBMR());
				sender.sendEventMessage(eventMessage);
			}
		} catch (Exception e) {
			log.error("OMSNotifier.notifyAllClients() error while sending to GM", e);
		}
	}

	public static void sendAlert( String org, SocketResponse response )
	{

		try
		{

			if ( response != null )
			{
				if ( log.isDebugEnabled() )
				{
					log.debug(OMSConstants.OMS_LOG_PREFIX + ".sendAlert : Notification for org : " + org + " , Notification : " + response);
				}

				if ( OMSConfig.getInstance().isSocketServerEnabled() )
				{
					List<ClientSession> clientSessions = ClientSessionCache.getInstance().getAllSessionsForOrg(org);					
					if ( clientSessions != null )
					{
						for ( ClientSession clientSession : clientSessions )
						{
							try
							{
								SocketIOClient client = clientSession.getClientConnection();
								client.sendEvent("alert", new VoidAckCallback() {
									@Override
									public void onSuccess()
									{
									}
								}, response);
							}
							catch ( Exception e )
							{
								log.error(OMSConstants.OMS_LOG_PREFIX + ".sendAlert : Error while sending alert on websocket", e);
							}

						}
					}

				}
			}

		}
		catch ( Exception e )
		{
			log.error(OMSConstants.OMS_LOG_PREFIX + ".sendAlert : Error while notifying clients", e);
		}
	}
}
