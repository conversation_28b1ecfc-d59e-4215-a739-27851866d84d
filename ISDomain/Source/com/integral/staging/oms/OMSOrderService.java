package com.integral.staging.oms;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.integral.finance.instrument.InstrumentClassification;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.util.SpreadConstants;
import com.integral.math.MathUtil;
import com.integral.model.dealing.OrderRequest;
import com.integral.staging.oms.model.*;
import com.integral.util.MathUtilC;
import org.apache.commons.lang.StringUtils;

import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.commons.Tuple;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.ContingencyParameter;
import com.integral.finance.dealing.ContingencyType;
import com.integral.is.ISCommonConstants;
import com.integral.is.priceprovision.PriceProvisionRulesService;
import com.integral.is.priceprovision.PriceProvisionServicesFactoryC;
import com.integral.is.priceprovision.rules.SpreadRuleParameter;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.message.ErrorMessageC;
import com.integral.message.MessageEvent;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.message.WorkflowMessageC;
import com.integral.oms.OrderAction;
import com.integral.persistence.Namespace;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.staging.Order;
import com.integral.staging.OrderBatch;
import com.integral.staging.OrderStagingServiceFactory;
import com.integral.staging.OrderStatusType;
import com.integral.staging.StageOrderIdProvider;
import com.integral.staging.StagingAreaPersistenceService;
import com.integral.staging.oms.audit.OMSAuditService;
import com.integral.staging.oms.client.OMSNotifier;
import com.integral.staging.oms.client.socketio.OMSResponse;
import com.integral.staging.oms.client.socketio.SocketResponse.Status;
import com.integral.staging.oms.config.OMSConfig;
import com.integral.staging.oms.orderbook.FXStagingOrderBookManager;
import com.integral.staging.oms.passorder.PassOrderReceiver;
import com.integral.user.Organization;
import com.integral.user.User;

public class OMSOrderService
{
	
	private static final Log log = LogFactory.getLog(OMSOrderService.class);
	private static final long BASISDIVISORFACTOR = 10000;
	
	public static Tuple<String, OrderBatch> createBatchOrders( User user, OMSOrderListParams omsOrderListParams, String action )
	{
		String errorMsgs = OrderValidator.validateMassOrders(omsOrderListParams);
		if ( errorMsgs != null )
		{
			log.info(OMSConstants.OMS_LOG_PREFIX + ".createBatchOrders : Order validation failed " + errorMsgs);
			return new Tuple<String, OrderBatch>(errorMsgs, null);
		}

		OrderBatch orderbatch = OMSOrderService.createMassOrders(user, omsOrderListParams);
		if ( orderbatch == null || orderbatch.getOrderList() == null || orderbatch.getOrderList().isEmpty() )
		{
			log.info(OMSConstants.OMS_LOG_PREFIX + ".createBatchOrders : Order creation failed");
			return new Tuple<String, OrderBatch>("Order creation failed", null);
		}

		OrderAction orderAction = null;
		if ( !StringUtils.isBlank(action) && OrderAction.valueOf(action.toUpperCase()) != null )
		{
			orderAction = OrderAction.valueOf(action.toUpperCase());
		}
		errorMsgs = OrderValidator.valiidateBatchOrders(orderbatch, orderAction);
		if ( errorMsgs != null )
		{
			log.info(OMSConstants.OMS_LOG_PREFIX + ".createBatchOrders : Order validation failed " + errorMsgs);
			return new Tuple<String, OrderBatch>(errorMsgs, null);
		}

		WorkflowMessage workFlowMsg = new WorkflowMessageC();
		workFlowMsg.setObject(orderbatch);
		workFlowMsg.setTopic(ISCommonConstants.MSG_TOPIC_ORDER);
		workFlowMsg.setEvent(MessageEvent.CREATE);
		workFlowMsg.setSender(user);
		WorkflowMessage result = OrderStagingServiceFactory.getOrderStagingService().process(workFlowMsg);
		
		if ( result != null && result.getReplyMessage() != null && result.getReplyMessage().getStatus() == MessageStatus.FAILURE )
		{
			Collection errors = result.getReplyMessage().getErrors();
			String errorMsg = "";
			for ( Object o : errors )
			{
				errorMsg += ((ErrorMessage) o).getCode();
			}
			log.info(OMSConstants.OMS_LOG_PREFIX + ".createBatchOrders : Creation of Batch Orders failed; error msg=" + errorMsg);
			return new Tuple<String, OrderBatch>(errorMsgs, null);
		}
		log.info(OMSConstants.OMS_LOG_PREFIX + ".createBatchOrders: order created");
		return new Tuple<String, OrderBatch>(null, orderbatch);
	}
	
	
	private static OrderBatch createMassOrders(User user , OMSOrderListParams omsOrderListParams )
	{
		try
		{
			ArrayList<String> linkedOrderIds = new ArrayList<String>(3);
			ArrayList<String> orderIdList = new ArrayList<String>(3);
			OrderBatch orderBatch = new OrderBatch();
			List<Order> orders = new ArrayList<Order>(3);
			boolean ifDoneOrder = omsOrderListParams.getOrderListParams().size() == 3;
			String primaryOrderId = null;
			
			for ( OMSOrderParam omsOrderParam : omsOrderListParams.getOrderListParams() )
			{
	    		OMSUtil.updateCustomerDetails(omsOrderParam);
				Order order = OMSUtil.constructOrder(omsOrderParam ,user);
				if(OMSConfig.getInstance().isExternalOMS(user.getOrganization().getShortName())) {
					order.setExternalVenue(true);
				}
				orders.add(order);
				linkedOrderIds.add(order.get_id());

				if ( omsOrderParam.isPrimaryOrder() )
				{
					primaryOrderId = order.get_id();
				}
			}

			String groupId = StageOrderIdProvider.getInstance().nextId();
			for ( Order order : orders )
			{
				orderIdList.add(order.get_id());
				for ( ContingencyParameter contParameter : order.getOrderContingencies() )
				{
					contParameter.setGroupId(groupId);

					List<String> linkedOrderIdList = new ArrayList<String>(linkedOrderIds);
					linkedOrderIdList.remove(order.getId());

					if ( ifDoneOrder && !order.isPrimaryOrder() && primaryOrderId != null )
					{
						linkedOrderIdList.remove(primaryOrderId);
					}
					contParameter.setLinkedOrderIds(linkedOrderIdList);
				}
			}
			orderBatch.setOrderList(orders);
			orderBatch.setOrderIDList(orderIdList);
			orderBatch.setUser(user);
			return orderBatch;
		}
		catch ( Exception e )
		{
			log.error(OMSConstants.OMS_LOG_PREFIX + ".createMassOrders : Mass order creation failed" ,e);
			return null;
		}

	}
	
	
	public static OMSResponse createOrder( User user, OMSOrderParam orderParam ,String action)
	{

		String errorMsgs = OrderValidator.validateDraft(orderParam);
		if ( errorMsgs != null )
			return new OMSResponse(Status.ERROR, errorMsgs);

		Order order = OMSUtil.constructOrder(orderParam ,user);
		WorkflowMessage workFlowMsg = new WorkflowMessageC();
		workFlowMsg.setObject(order);
		workFlowMsg.setTopic(ISCommonConstants.MSG_TOPIC_ORDER);
		workFlowMsg.setEvent(MessageEvent.CREATE);
		workFlowMsg.setSender(order.getUser());
		WorkflowMessage result = OrderStagingServiceFactory.getOrderStagingService().process(workFlowMsg);

		if ( result != null && result.getReplyMessage() != null && result.getReplyMessage().getStatus() == MessageStatus.FAILURE )
		{
			Collection errors = result.getReplyMessage().getErrors();
			String errorMsg = "";
			for ( Object o : errors )
			{
				errorMsg += ((ErrorMessage) o).getCode();
			}
			return new OMSResponse(Status.ERROR, errorMsg);
		}

		if ( order.getExpiryTime() != null )
			OrderExpiryManager.scheduleExpiry(order);
		log.info(OMSConstants.OMS_LOG_PREFIX + ".createOrder: order created");

		OMSResponse response = null;
		if ( !StringUtils.isBlank(action) && OrderAction.valueOf(action.toUpperCase()) != null )
		{	
		     OrderAction orderAction = OrderAction.valueOf(action.toUpperCase());
		     if(orderAction == OrderAction.ACTIVATE) {
		    	 //action(order.getId(), action, null);
				response = OMSResponse.getOMSResponse(order, OMSEvent.CREATED, null, OMSState.ACTIVE, orderParam);
		     }
		} else {
			response =  OMSResponse.getOMSResponse(order, OMSEvent.CREATED, null, OMSState.DRAFT, orderParam);
		}
		OMSNotifier.notifyAllClients(order.getNamespaceName(), response);
		OMSAuditService.audit(OMSUtil.getOMSEvent(response, order.getNamespaceName(), user.getShortName(), null));
		response.addProperty("orderId", order.getId());
		return response;
	}
	
	

	public static Map<String, List<OMSOrderParam>> queryOrders( User user, OMSOrderQueryParam queryParams, String brokerOrgName, boolean isMainUser ){
		
		boolean isMainOrg = false;
		if(isMainUser){
			isMainOrg = true;
		}		
		else {
			isMainOrg = user.getOrganization().getShortName().equals("MAIN");
		}
		if(log.isDebugEnabled()){
			log.debug(user +" is main user : "+ isMainOrg);
		}
		if ( !isMainOrg && !user.hasPermission(OMSConstants.OMS_PERMISSION_TRADER_USER) && !user.hasPermission(OMSConstants.OMS_PERMISSION_CHIEF_DEALER_USER) )
		{
			return null;
		}
		HashMap<String, List<OMSOrderParam>> ordersResponse = new HashMap<String, List<OMSOrderParam>>();

		List stateCodes = null;
		if ( queryParams != null && queryParams.getStates() != null )
		{
			stateCodes = new ArrayList();
			for ( String state : queryParams.getStates() )
			{
				if ( OMSState.isValidState(state) )
				{
					int code = OMSState.fromOMSState(state).code;
					stateCodes.add(code);
					ordersResponse.put(state, new ArrayList<OMSOrderParam>());
				}
			}
		}
		if ( stateCodes == null || stateCodes.size() == 0 )
		{
			return ordersResponse;
		}
		List<Order> orders = new ArrayList<Order>();
		if ( isMainOrg )
		{
			if(log.isDebugEnabled()){
				log.debug("Inside main org");
			}
			List<String> omsOrgs = null;
			if(brokerOrgName == null){
				omsOrgs = OMSConfig.getInstance().getOmsOrgs();
			}
			else{
				omsOrgs = new ArrayList<String>(1);
				omsOrgs.add(brokerOrgName);
			}
			Map<String, List> filters = new HashMap<String, List>();
			if ( stateCodes != null )
			{
				filters.put("stt", stateCodes);
			}
			for ( String omsOrg : omsOrgs )
			{
				Organization org = ReferenceDataCacheC.getInstance().getOrganization(omsOrg);
				if(org != null){				
					orders.addAll(StagingAreaPersistenceService.getOrders(filters, org));
				}
			}
			user = null;
		}
		else if ( queryParams.getCustomerOrg() != null && queryParams.getBrokerOrg() != null )
		{
			Organization brokerOrg = ReferenceDataCacheC.getInstance().getOrganization(queryParams.getBrokerOrg());
			Map<String, List> filters = new HashMap<String, List>();
			if ( stateCodes != null )
			{
				filters.put("stt", stateCodes);
			}
			List<Order> brokerOrders = StagingAreaPersistenceService.getOrders(filters, brokerOrg);
			for ( Order order : brokerOrders )
			{
				if ( null != order.getCounterPartyAOrg() && order.getCounterPartyAOrg().equals(queryParams.getCustomerOrg()) )
				{
					orders.add(order);
				}
			}
		}
		else
		{
			orders = OrderStagingServiceFactory.getOrderStagingService().queryOrder(user, stateCodes, queryParams.getCurrencyPairs(), queryParams.getClientOrderIds(), queryParams.getPortfolioIds());
		}		
		for ( Order order : orders )
		{
			OMSOrderParam param = OMSUtil.convertToParam(user, order);
			if(log.isDebugEnabled()){
				log.debug("Param : " + param.toString());
			}
			//param = applyPPSpread(param, order);
			if ( param != null )
				if ( ordersResponse.get(param.getOrderState()) != null )
				{
					ordersResponse.get(param.getOrderState()).add(param);
				}
		}
		if(log.isDebugEnabled()){
			log.debug("OrdersResponse : " + ordersResponse.keySet().toString());
		}
		return ordersResponse;
		
	}

	public static Map<String, List<OMSOrderParam>> queryOrders( User user, OMSOrderQueryParam queryParams )
	{
		return queryOrders( user, queryParams, null, false );
	}

	public static Collection<OMSAggregateOrderParam> queryAggregateOrders(User user, OMSOrderQueryParam queryParams) {
		Map<String, List<OMSOrderParam>> orders = queryOrders(user, queryParams, null, false);
		Map<String, OMSAggregateOrderParam> aggregateOrders = new HashMap<String, OMSAggregateOrderParam>();
		Map<String, String> orgUISettings = OMSConfig.getInstance().getOrgUISettings().get(user.getOrganization().getShortName());
		for (Map.Entry<String, List<OMSOrderParam>> entry : orders.entrySet()) {
			List<OMSOrderParam> ordersParam = entry.getValue();
			for (OMSOrderParam order : ordersParam) {
				if (order.getOrderType().equals(OrderRequest.Type.FIXING.toString())) {
					String aggregateKey = getAggregateOrderKey(order);
					OMSAggregateOrderParam aggregateOrderParam = aggregateOrders.get(aggregateKey);
					if (aggregateOrderParam == null) {
						aggregateOrders.put(aggregateKey, getAggregateOrderParam(order, aggregateKey, orgUISettings));
					} else {
						aggregateOrderParam.setOrderCount(aggregateOrderParam.getOrderCount() + 1);
						aggregateOrderParam.setOrderIds(aggregateOrderParam.getOrderIds()+","+order.getOrderId());
						if (!order.getSide().equals(OrderRequest.RequestLeg.BuySellMode.SELL.toString())) {
							aggregateOrderParam.setBalance(aggregateOrderParam.getBalance() + order.getDealtAmt());
						} else {
							aggregateOrderParam.setBalance(aggregateOrderParam.getBalance() - order.getDealtAmt());
						}
					}
				}
			}
		}
		for (OMSAggregateOrderParam order : aggregateOrders.values()) {
			if (order.getBalance() < 0) {
				order.setBalance(Math.abs(order.getBalance()));
				order.setSide(OrderRequest.RequestLeg.BuySellMode.SELL.toString());
			} else {
				order.setSide(OrderRequest.RequestLeg.BuySellMode.BUY.toString());
			}
		}
		return aggregateOrders.values();
	}

	private static String getAggregateOrderKey(OMSOrderParam order) {
		StringBuilder sb = new StringBuilder();
		sb.append(order.getCcyPair()).append(":").append(order.getDealtCcy()).append(":");
		sb.append(order.getFixingReference()).append(":").append(getDefaultDateTimeFormat().format(order.getFixingTime()));
		return sb.toString();
	}

	public static SimpleDateFormat getDefaultDateTimeFormat()
	{
		final String datePatternForAggregate = "dd-MM-yyyy HH:mm";
		return new SimpleDateFormat(datePatternForAggregate);
	}

	private static OMSAggregateOrderParam getAggregateOrderParam(OMSOrderParam order, String aggregateKey, Map<String, String> orgUISettings) {
		OMSAggregateOrderParam param = new OMSAggregateOrderParam();
		param.setFixingAsset(getFixingAsset(order, orgUISettings));
		param.setAggregateKey(aggregateKey);
		param.setCcyPair(order.getCcyPair());
		param.setDealtCcy(order.getDealtCcy());
		param.setOrderCount(1);
		if (order.getSide().equals(OrderRequest.RequestLeg.BuySellMode.SELL.toString())) {
			param.setBalance(0 - order.getDealtAmt());
		} else {
			param.setBalance(order.getDealtAmt());
		}
		param.setFixingReference(order.getFixingReference());
		param.setFixingTime(order.getFixingTime());
		param.setDate(order.getFixingTime());
		param.setOrderIds(order.getOrderId());
		param.setFixingRate(order.getVenueBMR());
		if(order.getVenueBMR() != null){
			param.setFixingRateAvailable(true);
		}
		return param;
	}

	private static String getFixingAsset(OMSOrderParam order, Map<String, String> orgUISettings) {
		String fixingAssest = InstrumentClassification.CURRENCY_CLSF;
		CurrencyPair ccyPair = CurrencyFactory.getCurrencyPairFromString(order.getCcyPair());
		if (ccyPair.getBaseCurrency().getInstrumentClassification().getShortName().equals(InstrumentClassification.METAL_CLSF)
				|| ccyPair.getVariableCurrency().getInstrumentClassification().getShortName().equals(InstrumentClassification.METAL_CLSF)) {
			fixingAssest = InstrumentClassification.METAL_CLSF;
		} else if (ccyPair.getBaseCurrency().getInstrumentClassification().getShortName().equals(InstrumentClassification.INDEX_CLSF)
				|| ccyPair.getVariableCurrency().getInstrumentClassification().getShortName().equals(InstrumentClassification.INDEX_CLSF)) {
			fixingAssest = InstrumentClassification.INDEX_CLSF;
		}

		return orgUISettings.get(fixingAssest) != null ? orgUISettings.get(fixingAssest) : fixingAssest;
	}

	public static OMSOrderParam applyPPSpread( OMSOrderParam orderParam, Order order )
	{
		orderParam.setMatchPrice(orderParam.getOrderPrice());
		try
		{
			String cptyAOrg = order.getCounterPartyAOrg();
			String cptyBOrg = order.getCounterPartyBOrg();
			if ( cptyAOrg == null || cptyBOrg == null )
			{
				return orderParam;
			}
			PriceProvisionRulesService rulesService = PriceProvisionServicesFactoryC.getInstance().createPriceProvisionService().getRulesService();
			Organization cptyA = OrganizationUtil.getOrganization(cptyAOrg);
			Organization cptyB = OrganizationUtil.getOrganization(cptyBOrg);
			String ccyPair = order.getCcyPair();
			CurrencyPair currencyPair = CurrencyFactory.getCurrencyPair(ccyPair.substring(0, 3), ccyPair.substring(4, 7));
			if ( cptyA == null || cptyB == null || currencyPair == null )
			{
				return orderParam;
			}
			SpreadRuleParameter spreadRuleParameter = rulesService.getSpreadRuleParameter(cptyA, cptyB, currencyPair);
			if ( spreadRuleParameter == null )
			{
				//log.info(".applyPPSpread no relation ship between " + cptyAOrg + " and " + counterPartyBOrg + "; no spread applied for the order " + order.getId());
				return orderParam;
			}
			Double baseAmountD = orderParam.getBaseAmt();
			//double bidSpread = spreadRuleParameter.getPipsBidSpread();
			//double offerSpread = spreadRuleParameter.getPipsOfferSpread();
			double bidSpread = spreadRuleParameter.getPipsBidSpread(baseAmountD);
			double offerSpread = spreadRuleParameter.getPipsOfferSpread(baseAmountD);
			if (log.isDebugEnabled()) {
				log.debug("OMSOrderService.applyPPSpread:Bid/Offer Spread:" + bidSpread + ":" 
								+ offerSpread + ":OrderBaseAmount:" + baseAmountD + ":ClientOrderId:" + order.getClientOrderId());
			}
			if ( orderParam.getSide().equals("BUY") )
			{
				orderParam.setPpSpread(offerSpread);
				orderParam.setMatchPrice(orderParam.getOrderPrice() + offerSpread);
			}
			else if ( orderParam.getSide().equals("SELL") )
			{
				orderParam.setPpSpread(bidSpread);
				orderParam.setMatchPrice(orderParam.getOrderPrice() - bidSpread);
			}
			return orderParam;
		}
		catch ( Exception e )
		{
			//log.warn(".applyPPSpread some error during applying spread; Dropping the order " + order.getId());
			return orderParam;
		}
	}
	
	public static void moveLinkedOrderToAuto( Order parentOrder, Order linkedOrder )
	{
		try{
			log.info(OMSConstants.OMS_LOG_PREFIX + ".activateLinkedOrder - Triggering Linked order with orderId: " + linkedOrder.getId());			
	
			if ( linkedOrder.getState() != OrderStatusType.INUSE.code && linkedOrder.getState() != OrderStatusType.INTIAL.code 
					&& linkedOrder.getState() != OrderStatusType.TRIGGERED.code)
			{
				log.info(OMSConstants.OMS_LOG_PREFIX + ".moveLinkedOrderToAuto - Linked order with orderId: " + linkedOrder.getId() + " is not in Active, Draft or Triggered state");
			}
			else {
				if (OMSState.ACTIVE.getOrderStatusType().getCode() == linkedOrder
						.getState()) {
					// If order is in Active state then remove it from order book
					if (!FXStagingOrderBookManager.withdrawOrder(linkedOrder.get_id())) {
						return;
					}
				}
				String errorss = OrderValidator.validateAutoOrder(linkedOrder);
				if (errorss != null) {
					return;
				}
				String errorMsgStr = PassOrderReceiver.auto(linkedOrder);
				if (errorMsgStr != null) {
					log.error(OMSConstants.OMS_LOG_PREFIX
							+ ".moveLinkedOrderToAuto - Linked order with orderId: "
							+ linkedOrder.getId() + " for orderId= "
							+ parentOrder.getId() + " failed to auto withdraw"
							+ errorMsgStr);
					return;
				}

			}
		}catch ( Exception e ) {
			log.error(OMSConstants.OMS_LOG_PREFIX + ".moveLinkedOrderToAuto :", e);
		}

	}
	
	public static void autoAcceptLinkedOrder( Order parentOrder, Order linkedOrder )
	{
		try{
			log.info(OMSConstants.OMS_LOG_PREFIX + ".autoAcceptLinkedOrder -" + linkedOrder.getId());			
	
			if ( linkedOrder.getState() != OrderStatusType.IMPORTED.code )
			{
				log.info(OMSConstants.OMS_LOG_PREFIX + ".autoAcceptLinkedOrder - Linked order with orderId: " + linkedOrder.getId() + " is not in Received state");
			}
			else {				
				String errorMsgStr = PassOrderReceiver.accept(linkedOrder);
				if (errorMsgStr != null) {
					log.error(OMSConstants.OMS_LOG_PREFIX
							+ ".autoAcceptLinkedOrder - Linked order with orderId: "
							+ linkedOrder.getId() + " for orderId= "
							+ parentOrder.getId() + " failed to auto accept"
							+ errorMsgStr);
					return;
				}

			}
		}catch ( Exception e ) {
			log.error(OMSConstants.OMS_LOG_PREFIX + ".autoAcceptLinkedOrder :", e);
		}

	}
	
	public static void activateLinkedOrderAndMoveOCXOrderToManual( Order parentOrder, Order linkedOrder )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + ".activateLinkedOrder - Triggering Linked order with orderId: " + linkedOrder.getId());
		OrderStatusType oldStateLinkedOrder = OrderStatusType.fromCode(linkedOrder.getState());

		if ( linkedOrder.getState() != OrderStatusType.AUTO.code )
		{
			log.info(OMSConstants.OMS_LOG_PREFIX + ".activateLinkedOrder - Linked order with orderId: " + linkedOrder.getId() + " is not in AUTO state");
		}
		else
		{
			FXStagingOrderBookManager.submitOrderToOrderBook(linkedOrder);
			/*linkedOrder.setState(OrderStatusType.INUSE.code);
			ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(linkedOrder, linkedOrder.getOrderBatchId());
			if ( errorMsg != null )
			{
				log.error(OMSConstants.OMS_LOG_PREFIX + ".activateLinkedOrderAndMoveOCXOrderToManual - Linked order with orderId: " + linkedOrder.getId() + " for orderId=" + parentOrder.get_id() + " failed to activate");
				return;
			}*/
			if ( OMSConfig.getInstance().isLinkedOrderPassingAllowed() && linkedOrder.getFromOrg() != null )
			{
				String errorMsgStr = PassOrderReceiver.manual(linkedOrder);
				if ( errorMsgStr != null )
				{
					log.error(OMSConstants.OMS_LOG_PREFIX + ".activateLinkedOrderAndMoveOCXOrderToManual - Linked order with orderId: " + linkedOrder.getId() + " for orderId= " + parentOrder.getId() + " failed to auto withdraw" + errorMsgStr);
					return;
				}
			}				
			
		}

	}
	
	
	public static void activateLinkedOrder( Order parentOrder, Order linkedOrder )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + ".activateLinkedOrder - Triggering Linked order with orderId: " + linkedOrder.getId());
		OrderStatusType oldStateLinkedOrder = OrderStatusType.fromCode(linkedOrder.getState());

		if ( linkedOrder.getState() != OrderStatusType.INTIAL.code )
		{
			log.info(OMSConstants.OMS_LOG_PREFIX + ".activateLinkedOrder - Linked order with orderId: " + linkedOrder.getId() + " is not in DRAFT state");
		}
		else
		{
			if(linkedOrder.isEMSOrder()){
				String errorMsgStr = PassOrderReceiver.accept(linkedOrder);
				if ( errorMsgStr != null )
				{
					log.error(OMSConstants.OMS_LOG_PREFIX + ".activateLinkedOrder - Linked order with orderId: " + linkedOrder.getId() + " for orderId= " + parentOrder.getId() + " failed to auto withdraw" + errorMsgStr);
					return;
				}
			}else{
				FXStagingOrderBookManager.submitOrderToOrderBook(linkedOrder);
				linkedOrder.setState(OrderStatusType.INUSE.code);
				ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(linkedOrder, linkedOrder.getOrderBatchId());
				if ( errorMsg != null )
				{
					log.error(OMSConstants.OMS_LOG_PREFIX + ".activateLinkedOrder - Linked order with orderId: " + linkedOrder.getId() + " for orderId=" + parentOrder.get_id() + " failed to activate");
				}
				else
				{
					OMSResponse omsResponse = OMSResponse.getOMSResponse(linkedOrder, OMSEvent.STATE_CHANGED, OMSState.fromStatusCode(oldStateLinkedOrder.code), OMSState.ACTIVE, null);
					OMSNotifier.notifyAllClients(linkedOrder.getNamespaceName(), omsResponse);
					OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, linkedOrder.getNamespaceName(), OMSConstants.OMS_SYSTEM_USER, null));				
				}
			}
		}

	}
	
	public static void cancelLinkedOrder( Order parentOrder, Order linkedOrder)
	{

		log.info(OMSConstants.OMS_LOG_PREFIX + ".cancelLinkedOrder - Cancelling Linked order with orderId: " + linkedOrder.getId());
		OrderStatusType oldStateLinkedOrder = OrderStatusType.fromCode(linkedOrder.getState());
		FXStagingOrderBookManager.withdrawOrder(linkedOrder.getId());
		if ( linkedOrder.getState() == OrderStatusType.CANCELLED.code )
		{
			log.info(OMSConstants.OMS_LOG_PREFIX + ".cancelLinkedOrder - Linked order with orderId: " + linkedOrder.getId() + " already cancelled");
		}
		else if ( linkedOrder.getState() == OrderStatusType.EXECUTED.code || linkedOrder.getState() == OrderStatusType.ARCHIVED.code )
		{
			log.info(OMSConstants.OMS_LOG_PREFIX + ".cancelLinkedOrder - Linked order with orderId: " + linkedOrder.getId() + " already Executed/Archived");
		}
		else
		{
			linkedOrder.setState(OrderStatusType.CANCELLED.code);
			ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(linkedOrder, linkedOrder.getOrderBatchId());

			if ( errorMsg != null )
			{
				log.error(OMSConstants.OMS_LOG_PREFIX + ".cancelLinkedOrder - Linked order with orderId: " + linkedOrder.getId() + " for orderId= " + parentOrder.getId() + " failed to cancel");
				return;
			}
			if ( OMSConfig.getInstance().isLinkedOrderPassingAllowed() && linkedOrder.getFromOrg() != null )
			{
				String errorMsgStr = PassOrderReceiver.reject(linkedOrder);
				if ( errorMsgStr != null )
				{
					log.error(OMSConstants.OMS_LOG_PREFIX + ".cancelLinkedOrder - Linked order with orderId: " + linkedOrder.getId() + " for orderId= " + parentOrder.getId() + " failed to cancel" + errorMsgStr);
					return;
				}
			}
			OMSResponse omsResponse = OMSResponse.getOMSResponse(linkedOrder, OMSEvent.STATE_CHANGED, OMSState.fromStatusCode(oldStateLinkedOrder.code), OMSState.CANCELLED, null);
			OMSNotifier.notifyAllClients(parentOrder.getNamespaceName(), omsResponse);
			OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, parentOrder.getNamespaceName(), OMSConstants.OMS_SYSTEM_USER, null));			
		}

	}
	
	public static ErrorMessage handlePassOrderWorkflow( Order order ) {
		
		if ( order.getFromOrg() != null )
		{
			if ( order.getState() == OrderStatusType.CANCELLED.code )
			{
				//Send Reject to Pass Sender
				PassOrderReceiver.reject(order);
			}
			else if ( order.getState() == OrderStatusType.EXECUTED.code )
			{
				//Send Filled to Pass Receiver
				PassOrderReceiver.fill(order, null);
			}
		}
		/*else if ( order.getToOrg() != null )
		{
			if ( order.getState() == OrderStatusType.CANCELLED.code )
			{
				//Send Reject to Pass Sender
				PassOrderSender.getInstance().withdraw(order);
			}
		}*/
	
		return null;
	}
	
	public static ErrorMessage handleOrderContigencies( Order order, String actionUser )
	{

		for ( ContingencyParameter contigency : order.getOrderContingencies() )
		{
			for ( String linkedOrderId : contigency.getLinkedOrderIds() )
			{
				if ( linkedOrderId.equals(order.getId()) )
				{
					continue;
				}
				Order linkedOrder = OrderStagingServiceFactory.getOrderStagingService().getOrder(linkedOrderId, order.getNamespace());
				if ( linkedOrder == null )
				{
					log.warn(OMSConstants.OMS_LOG_PREFIX + ".handleOrderContigencies - Linked order with orderId: " + linkedOrderId + " for orderId=" + order.getId() + " not found");
				}
				else
					synchronized ( linkedOrder )
					{
						{
							if ( (order.getState() == OrderStatusType.CANCELLED.code) && (linkedOrder.getState() != OrderStatusType.CANCELLED.code)
									&& (linkedOrder.getState() != OrderStatusType.MATCHED.code) && contigency.getType() == ContingencyType.OTO )
							{
								log.info(OMSConstants.OMS_LOG_PREFIX + ".handleOrderContigencies - Canceling linked order: " + linkedOrderId + " on cancellation of primary order " + order.getId());
								cancelLinkedOrder(order, linkedOrder);
							}
							else if ( linkedOrder.getState() == OrderStatusType.AUTO.code )
							{
								log.info(OMSConstants.OMS_LOG_PREFIX + ".handleOrderContigencies - Linked order orderId: " + linkedOrderId + " is in AUTO state hence not handling contigency action");
							}

							if ( order.getState() == OrderStatusType.MATCHED.code && contigency.getType() == ContingencyType.OCO )
							{
								//OCO : Cancel Linked orders
								cancelLinkedOrder(order, linkedOrder);								
							}
							else if ( contigency.getType() == ContingencyType.OTO )
							{

								//OTO : Triggering Linked orders
								activateLinkedOrder(order, linkedOrder);
							}
						}

					}
			}
		}
		return null;
	}
	
	public static ErrorMessage handleAcceptAutoManualOrderContigencies( Order order, String actionUser )
	{

		for ( ContingencyParameter contigency : order.getOrderContingencies() )
		{
			for ( String linkedOrderId : contigency.getLinkedOrderIds() )
			{
				if ( linkedOrderId.equals(order.getId()) )
				{
					continue;
				}
				Order linkedOrder = OrderStagingServiceFactory.getOrderStagingService().getOrder(linkedOrderId, order.getNamespace());
				if ( linkedOrder == null )
				{
					log.warn(OMSConstants.OMS_LOG_PREFIX + ".handleOrderContigencies - Linked order with orderId: " + linkedOrderId + " for orderId=" + order.getId() + " not found");
				}
				else
					synchronized ( linkedOrder )
					{
						{
							if ( (order.getState() == OrderStatusType.INUSE.code) && (linkedOrder.getState() == OrderStatusType.AUTO.code))
							{
								activateLinkedOrderAndMoveOCXOrderToManual(order, linkedOrder);
							}
							else if ( ((order.getState() == OrderStatusType.AUTO.code) 
										&& (linkedOrder.getState() == OrderStatusType.INUSE.code || linkedOrder.getState() == OrderStatusType.TRIGGERED.code))
									|| ((order.getState() == OrderStatusType.EXECUTED.code) && (linkedOrder.getState() == OrderStatusType.INTIAL.code)))
							{
								moveLinkedOrderToAuto(order, linkedOrder);
							}
							else if ( (order.getState() == OrderStatusType.INUSE.code) && (linkedOrder.getState() == OrderStatusType.IMPORTED.code))
							{
								autoAcceptLinkedOrder(order, linkedOrder);
							}else if ( contigency.getType() == ContingencyType.OCO  && (order.getState() == OrderStatusType.EXECUTED.code) 
									&& (linkedOrder.getState() != OrderStatusType.CANCELLED.code))
							{
								cancelLinkedOrder(order, linkedOrder);
							}
						}

					}
			}
		}
		return null;
	}
	

	public static ErrorMessage updateStagingOrder(String orderId , Namespace namespace , OMSOrderParam param, String actionUser)
	{
		Order order = OrderStagingServiceFactory.getOrderStagingService().getOrder(orderId, namespace);
		if ( order != null )
		{
			synchronized ( order )
			{
				order = OrderStagingServiceFactory.getOrderStagingService().getOrder(orderId, namespace);
				OMSState oldState = OMSState.fromStatusCode(order.getState());
				Double amendedPrice = null;
				if ( param.getOrderPrice() != null && (order.getPrice() == null || Double.compare(param.getOrderPrice(), order.getPrice()) != 0) ) {
					amendedPrice = param.getOrderPrice();
				}
				
				order = OMSUtil.updateOrderParams(order, param);
				ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

				if ( errorMsg != null )
				{
					return errorMsg;
				}
				OMSState newState = OMSState.fromStatusCode(order.getState());
				
				if ( amendedPrice != null && newState == OMSState.ACTIVE )
				{
					FXStagingOrderBookManager.amendOrder(orderId, amendedPrice);
				}
				OMSEvent omsEvent = newState == oldState ? OMSEvent.UPDATED : OMSEvent.STATE_CHANGED;
				OMSResponse omsResponse = OMSResponse.getOMSResponse(order, omsEvent, oldState, newState, param);
				OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
				OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), actionUser, null));				
				
				log.info(OMSConstants.OMS_LOG_PREFIX + ".updateStagingOrder - order updated orderId=" + orderId);

			}
		} else {
			log.info(OMSConstants.OMS_LOG_PREFIX + ".updateStagingOrder - order not found orderId=" + orderId);
			ErrorMessage errorMsg = new ErrorMessageC();
			errorMsg.setCode("Order not exist");
		}
		return null;

	}

    public static ErrorMessage updateStagingOrders(String orderIds , Namespace namespace ,OMSAggregateOrderParam param, String actionUser)
    {
        String orderIdArray[] = orderIds.split(",");
        for(String orderId : orderIdArray) {
            Order order = OrderStagingServiceFactory.getOrderStagingService().getOrder(orderId, namespace);
            if (order != null) {
                synchronized (order) {
                    order = OrderStagingServiceFactory.getOrderStagingService().getOrder(orderId, namespace);
                    OMSState oldState = OMSState.fromStatusCode(order.getState());

					order.setVenueBMR(param.getFixingRate());
                    ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

                    if (errorMsg != null) {
                        return errorMsg;
                    }
					OMSOrderParam updatedOrderParams = new OMSOrderParam();
					updatedOrderParams.setVenueBMR(param.getFixingRate());
                    OMSState newState = OMSState.fromStatusCode(order.getState());
                    OMSEvent omsEvent = newState == oldState ? OMSEvent.UPDATED : OMSEvent.STATE_CHANGED;
                    OMSResponse omsResponse = OMSResponse.getOMSResponse(order, omsEvent, oldState, newState, updatedOrderParams);
                    OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
                    OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), actionUser, null));
                    log.info(OMSConstants.OMS_LOG_PREFIX + ".updateStagingOrder - order updated orderId=" + orderId);
                }
            } else {
                log.info(OMSConstants.OMS_LOG_PREFIX + ".updateStagingOrder - order not found orderId=" + orderId);
                ErrorMessage errorMsg = new ErrorMessageC();
                errorMsg.setCode("Order not exist");
            }
        }
        return null;

    }

	public static ErrorMessage updateFillRateOnStagingOrder(String orderId, Namespace namespace, String actionUser, Organization providerOrganization) {
		Order order = OrderStagingServiceFactory.getOrderStagingService().getOrder(orderId, namespace, true);
		if (order != null) {
			synchronized (order) {
				order = OrderStagingServiceFactory.getOrderStagingService().getOrder(orderId, namespace);
				OMSState oldState = OMSState.fromStatusCode(order.getState());
				double fillRate = getFillRate(providerOrganization, order);

				order.setFilledPrice(fillRate);
				ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

				if (errorMsg != null) {
					return errorMsg;
				}
				OMSOrderParam updatedOrderParams = new OMSOrderParam();
				updatedOrderParams.setFillPrice(order.getFilledPrice());
				OMSState newState = OMSState.fromStatusCode(order.getState());
				OMSEvent omsEvent = newState == oldState ? OMSEvent.UPDATED : OMSEvent.STATE_CHANGED;
				OMSResponse omsResponse = OMSResponse.getOMSResponse(order, omsEvent, oldState, newState, updatedOrderParams);
				OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
				OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), actionUser, null));
				log.info(OMSConstants.OMS_LOG_PREFIX + ".updateFillRateOnStagingOrder - order updated orderId=" + orderId);
			}
		} else {
			log.info(OMSConstants.OMS_LOG_PREFIX + ".updateFillRateOnStagingOrder - order not found orderId=" + orderId);
			ErrorMessage errorMsg = new ErrorMessageC();
			errorMsg.setCode("Order not exist");
		}

		return null;

	}

	private static double getFillRate(Organization providerOrganization, Order order) {
		double fixingRate = order.getVenueBMR();
		Organization custOrganization = OrganizationUtil.getOrganization(order.getCounterPartyAOrg());
		CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(order.getCcyPair());
		SpreadRuleParameter srp = ServiceFactory.getPriceProvisionRulesService().getSpreadRuleParameter(custOrganization, providerOrganization, currencyPair,true);
		StringBuilder sb = new StringBuilder();
		sb.append(OMSConstants.OMS_LOG_PREFIX + ".getFillRate - orderId=" + order.getId());
		sb.append(", fixingRate: " + fixingRate);
		double baseAmount = OMSUtil.getFormattedAmount(order.isDealtInBase()? order.getDealtAmt() : (order.getDealtAmt()/fixingRate));
        sb.append(", baseAmount: " + baseAmount);
		Double ppSpotSpread = null;
		if(srp.isEnabled()){
			if(srp.getSpotSpreadType() == SpreadConstants.PIPS){
				if ((order.isBuy() && order.isDealtInBase()) || (!order.isBuy() && !order.isDealtInBase())) {
					fixingRate = MathUtilC.add(fixingRate, srp.getRawOfferSpread(baseAmount));
					sb.append(", offerSpread: " + srp.getRawOfferSpread(baseAmount));
					ppSpotSpread = srp.getRawOfferSpread(baseAmount);
				}else{
					fixingRate = MathUtilC.subtract(fixingRate, srp.getRawBidSpread(baseAmount));
					sb.append(", bidSpread: " + srp.getRawBidSpread(baseAmount));
					ppSpotSpread = srp.getRawBidSpread(baseAmount);
				}
			}else if(srp.getSpotSpreadType() == SpreadConstants.BPS){
				if ((order.isBuy() && order.isDealtInBase()) || (!order.isBuy() && !order.isDealtInBase())) {
					double spread = MathUtilC.multiply(fixingRate, srp.getRawOfferSpread(baseAmount))/BASISDIVISORFACTOR;
					spread = MathUtil.round(spread, srp.getFXRateBasis().getSpotPrecision(), BigDecimal.ROUND_CEILING);
					fixingRate = MathUtilC.add(fixingRate, spread);
					sb.append(", offerSpread: " + spread);
					ppSpotSpread = spread;
				}else{
					double spread = MathUtilC.multiply(fixingRate, srp.getRawBidSpread(baseAmount))/BASISDIVISORFACTOR;
					spread = MathUtil.round(spread, srp.getFXRateBasis().getSpotPrecision(), BigDecimal.ROUND_CEILING);
					fixingRate = MathUtilC.subtract(fixingRate, spread);
					sb.append(", bidSpread: " + spread);
					ppSpotSpread = spread;
				}
			}
		}
		order.setPpSpotSpread(ppSpotSpread);
		sb.append(", fillRate: " + fixingRate);
		log.info(sb.toString());
		return fixingRate;
	}
}
