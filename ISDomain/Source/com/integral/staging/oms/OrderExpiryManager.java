package com.integral.staging.oms;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.model.dealing.DealingModel;
import com.integral.persistence.Namespace;
import com.integral.persistence.NamespaceC;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.staging.Order;
import com.integral.staging.OrderStagingServiceFactory;
import com.integral.staging.OrderStatusType;
import com.integral.staging.StagingAreaPersistenceService;
import com.integral.staging.oms.audit.OMSAuditService;
import com.integral.staging.oms.client.OMSNotifier;
import com.integral.staging.oms.client.socketio.OMSResponse;
import com.integral.staging.oms.model.OMSEvent;
import com.integral.staging.oms.model.OMSState;
import com.integral.staging.oms.orderbook.FXStagingOrderBookManager;

public class OrderExpiryManager
{

	private static final Log log = LogFactory.getLog(OrderExpiryManager.class);
	private static final ScheduledExecutorService executorService = Executors.newScheduledThreadPool(10, new ThreadFactory());

    public static void scheduleExpiry(Order order){
        long delay = order.getExpiryTime() - System.currentTimeMillis();
        if(delay < 0) delay = 0;
        executorService.schedule(new OMSOrderExpiryWorker(order.getId() , order.getNamespace().getShortName()), delay, TimeUnit.MILLISECONDS);
    }

    private static class ThreadFactory implements java.util.concurrent.ThreadFactory {
        final AtomicInteger threadNumber = new AtomicInteger(1);
        ThreadGroup tg = null;

        private ThreadFactory() {
            this.tg = new ThreadGroup("OMSExpiryThreadGroup");
        }
        public Thread newThread(Runnable runnable) {
            return new Thread(this.tg, runnable, "OMSExpiryThread-"+ threadNumber.getAndIncrement());
        }
    }

    private static class OMSOrderExpiryWorker implements Runnable{
        private String orderId;
        private String namespace;
        public OMSOrderExpiryWorker(String id , String namespace){
            this.orderId = id;
            this.namespace = namespace;
        }

        @Override
        public void run() {
            log.info(OMSConstants.OMS_LOG_PREFIX +"Starting to expire the order " + orderId);
            long expiryStartedTime = System.currentTimeMillis();
            FXStagingOrderBookManager.withdrawOrder(orderId);
            Namespace namespaceEntity = (Namespace) ReferenceDataCacheC.getInstance().getEntityByShortName( namespace, NamespaceC.class, null, 'A' );
            if(namespaceEntity == null) {
            	log.error(OMSConstants.OMS_LOG_PREFIX +" Invalid Namespace " + namespace + " for orderId :" + orderId);
            }
            Order order = OrderStagingServiceFactory.getOrderStagingService().getOrder(orderId, namespaceEntity);
            if(order == null){
                log.warn(OMSConstants.OMS_LOG_PREFIX +" OMS Order expiry failed; order not exist for the id " + orderId);
                return;
            }
			synchronized ( order )
			{
				if ( order.getStatus() == 'P' )
				{
					log.info(OMSConstants.OMS_LOG_PREFIX + " OMS Order is already expired id=" + orderId);
					return;
				}
				if ( order.getExpiryTime() == null || (order.getExpiryTime() - expiryStartedTime) > 1000 )
				{
					log.info(OMSConstants.OMS_LOG_PREFIX + " OMS Order expiry time modified to future date; order Id=" + orderId);
					return;
				}
				int state = order.getState();
				
				int archived = OrderStatusType.ARCHIVED.getCode();
				int expired = OrderStatusType.EXPIRED.getCode();
				int deleted = OrderStatusType.DELETED.getCode();
				int executed = OrderStatusType.EXECUTED.getCode();
				int cancelled = OrderStatusType.CANCELLED.getCode();
				int matched = OrderStatusType.MATCHED.getCode();

				if ( state == archived || state == expired || state == deleted || state == executed || state == cancelled || (state == matched && !order.isEMSOrder()))
				{
					log.info(OMSConstants.OMS_LOG_PREFIX + "OMS Order is not in expirable state : " +  OMSState.fromStatusCode(order.getState()) + " order Id=" + orderId);
					return;
				}
				order.setStatus(DealingModel.Status.PASSIVE);
				order.setState(OrderStatusType.EXPIRED.getCode());

				ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.get_id());

				if ( errorMsg != null )
				{
					log.error(OMSConstants.OMS_LOG_PREFIX + "cancellation of the order " + order.getId() + " failed, error msg=" + errorMsg);
				}
				else
				{
					notifications(order , state);
				}
			}

        }

		private void notifications( Order order, int state )
		{
			OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.STATE_CHANGED, OMSState.fromStatusCode(state), OMSState.EXPIRED, null);
			OMSNotifier.notifyAllClients(namespace, omsResponse);
			OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, namespace, null, null));			
			log.info(OMSConstants.OMS_LOG_PREFIX + "OMS Order " + orderId + " expired.");

		}
    }
    
}
