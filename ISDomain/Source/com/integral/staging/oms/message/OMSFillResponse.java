package com.integral.staging.oms.message;

import com.integral.commons.buffers.UnSafeBuffer;

public class OMSFillResponse extends OMSFill
{
	private boolean initialFill = true;

	public boolean isInitialFill() {
		return initialFill;
	}

	public void setInitialFill(boolean initialFill) {
		this.initialFill = initialFill;
	}

	@Override
	public OMSMsgType getMsgType()
	{
		return OMSMsgType.FILL_RESPONSE;
	}
	
	@Override
	public String toString()
	{
		return super.toString();
	}

	@Override
	public void writeTo( UnSafeBuffer safeBuf )
	{
		super.writeTo(safeBuf);
		safeBuf.putBoolean(initialFill);
	}

	@Override
	public void readFrom( UnSafeBuffer safeBuf )
	{
		super.readFrom(safeBuf);
		this.initialFill = safeBuf.getBoolean();
	}
}
