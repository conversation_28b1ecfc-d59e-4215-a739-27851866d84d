package com.integral.staging.oms.message;


import com.integral.commons.buffers.UnSafeBuffer;

public class OMSAmendAccept extends OMSMessage
{
	public static OMSMsgType msgType = OMSMsgType.AMEND_ACCEPT;

	private Double dealtAmt;
	private Double orderPrice;

	public Double getDealtAmt() {
		return dealtAmt;
	}

	public void setDealtAmt(Double dealtAmt) {
		this.dealtAmt = dealtAmt;
	}

	public Double getOrderPrice() {
		return orderPrice;
	}

	public void setOrderPrice(Double orderPrice) {
		this.orderPrice = orderPrice;
	}


	@Override
	public void writeTo( UnSafeBuffer safeBuf )
	{
		super.writeTo(safeBuf);
		putIfNotNull(safeBuf, dealtAmt);
		putIfNotNull(safeBuf, orderPrice);
	}

	@Override
	public void readFrom( UnSafeBuffer safeBuf )
	{
		super.readFrom(safeBuf);
		this.dealtAmt = getDoubleIfPresent(safeBuf);
		this.orderPrice = getDoubleIfPresent(safeBuf);
	}

	@Override
	public OMSMsgType getMsgType()
	{
		return msgType;
	}
	
	@Override
	public String toString()
	{
		StringBuilder builder = new StringBuilder();
		builder.append(super.toString());
		builder.append("dealtAmt").append(" : ").append(dealtAmt).append(", ");
		builder.append("orderPrice").append(" : ").append(orderPrice).append(", ");
		return builder.toString();
	}

}
