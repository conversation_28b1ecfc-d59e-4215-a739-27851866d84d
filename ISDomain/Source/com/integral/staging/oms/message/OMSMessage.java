package com.integral.staging.oms.message;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.commons.serialize.Storeable;

public abstract class OMSMessage implements Storeable
{

	public final static byte VERSION = 0;
	/**
	 * This is the orderId of Sender OMS
	 */
	protected String clientOrderId;
	protected String orderId;
	protected String fromOMSOrg;
	protected String toOMSOrg;
	protected String failureReason;
	protected boolean failed;
	protected String user;
	protected boolean emsOrder;

	public abstract OMSMsgType getMsgType();
	
	public String getUser() {
		return user;
	}

	public String getClientOrderId()
	{
		return clientOrderId;
	}

	public void setClientOrderId( String clientOrderId )
	{
		this.clientOrderId = clientOrderId;
	}

	public void setUser(String user) {
		this.user = user;
	}	
	
	public String getOrderId()
	{
		return orderId;
	}

	public void setOrderId( String orderId )
	{
		this.orderId = orderId;
	}
	
	public String getFromOMSOrg()
	{
		return fromOMSOrg;
	}

	public void setFromOMSOrg( String omsOrg )
	{
		this.fromOMSOrg = omsOrg;
	}

	public String getToOMSOrg()
	{
		return toOMSOrg;
	}

	public void setToOMSOrg( String toOMSOrg )
	{
		this.toOMSOrg = toOMSOrg;
	}
	
	public boolean isEmsOrder()
	{
		return emsOrder;
	}

	public void setEmsOrder( boolean emsOrder )
	{
		this.emsOrder = emsOrder;
	}
	
	public boolean isFailed()
	{
		return failed;
	}

	public void setFailed( boolean failed )
	{
		this.failed = failed;
	}
	
	public String getFailureReason()
	{
		return failureReason;
	}

	public void setFailureReason( String failureReason )
	{
		this.failureReason = failureReason;
	}
	
	public static void putIfNotNull( UnSafeBuffer safeBuf ,String obj) {
		if(obj !=null) {
			safeBuf.putBoolean(true);
			safeBuf.putString(obj);
		} else {
			safeBuf.putBoolean(false);
		}
	}
	
	public static String getIfPresent( UnSafeBuffer safeBuf ) {
		boolean isSet = safeBuf.getBoolean();
		if ( isSet )
		{
			return safeBuf.getString();
		}
		return null;
	}
	
	public static void putIfNotNull( UnSafeBuffer safeBuf ,Double obj) {
		if(obj !=null) {
			safeBuf.putBoolean(true);
			safeBuf.putDouble(obj);
		} else {
			safeBuf.putBoolean(false);
		}
	}
	
	public static Double getDoubleIfPresent( UnSafeBuffer safeBuf ) {
		boolean isSet = safeBuf.getBoolean();
		if ( isSet )
		{
			return safeBuf.getDouble();
		}
		return null;
	}
	
	public static void putIfNotNull( UnSafeBuffer safeBuf ,Long obj) {
		if(obj !=null) {
			safeBuf.putBoolean(true);
			safeBuf.putLong(obj);
		} else {
			safeBuf.putBoolean(false);
		}
	}
	
	public static Long getLongIfPresent( UnSafeBuffer safeBuf ) {
		boolean isSet = safeBuf.getBoolean();
		if ( isSet )
		{
			return safeBuf.getLong();
		}
		return null;
	}
	
	@Override
	public void writeTo( UnSafeBuffer safeBuf )
	{
		putIfNotNull(safeBuf, fromOMSOrg);
		putIfNotNull(safeBuf, toOMSOrg);
		putIfNotNull(safeBuf, orderId);
		putIfNotNull(safeBuf, clientOrderId);
		putIfNotNull(safeBuf, failureReason);
		putIfNotNull(safeBuf, user);
		safeBuf.putBoolean(emsOrder);
		safeBuf.putBoolean(failed);
	}

	@Override
	public void readFrom( UnSafeBuffer safeBuf )
	{
		this.fromOMSOrg = getIfPresent(safeBuf);
		this.toOMSOrg = getIfPresent(safeBuf);
		this.orderId = getIfPresent(safeBuf);
		this.clientOrderId = getIfPresent(safeBuf);
		this.failureReason = getIfPresent(safeBuf);
		this.user = getIfPresent(safeBuf);
		this.emsOrder = safeBuf.getBoolean();
		this.failed = safeBuf.getBoolean();
	}
	

	@Override
	public int getEstimatedSize()
	{
		return 32;
	}
	
	@Override
	public String toString()
	{
		StringBuilder builder = new StringBuilder();
		builder.append("MsgType").append(" : ").append(getMsgType()).append(", ");
		builder.append("fromOMSOrg").append(" : ").append(fromOMSOrg).append(", ");
		builder.append("toOMSOrg").append(" : ").append(toOMSOrg).append(", ");
		builder.append("clientOrderId").append(" : ").append(clientOrderId).append(", ");
		builder.append("orderId").append(" : ").append(orderId).append(", ");
		builder.append("failureReason").append(" : ").append(failureReason).append(", ");
		builder.append("failed").append(" : ").append(failed).append(", ");
		builder.append("user").append(" : ").append(user).append(", ");
		builder.append("emsOrder").append(" : ").append(emsOrder);

		return builder.toString();
	}
	
}
