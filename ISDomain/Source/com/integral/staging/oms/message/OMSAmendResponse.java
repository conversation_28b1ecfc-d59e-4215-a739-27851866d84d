package com.integral.staging.oms.message;

import com.integral.commons.buffers.UnSafeBuffer;

public class OMSAmendResponse extends OMSAmend
{
	private boolean amendBeforeAccept = false;

	public boolean isAmendBeforeAccept() {
		return amendBeforeAccept;
	}

	public void setAmendBeforeAccept(boolean amendBeforeAccept) {
		this.amendBeforeAccept = amendBeforeAccept;
	}

	@Override
	public OMSMsgType getMsgType()
	{
		return OMSMsgType.AMEND_RESPONSE;
	}

	@Override
	public String toString()
	{
		return super.toString();
	}

	@Override
	public void writeTo( UnSafeBuffer safeBuf )
	{
		super.writeTo(safeBuf);
		safeBuf.putBoolean(amendBeforeAccept);
	}

	@Override
	public void readFrom( UnSafeBuffer safeBuf )
	{
		super.readFrom(safeBuf);
		this.amendBeforeAccept = safeBuf.getBoolean();
	}
}
