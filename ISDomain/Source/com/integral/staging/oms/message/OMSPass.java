package com.integral.staging.oms.message;

import java.util.ArrayList;
import java.util.List;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.staging.oms.model.OMSContigencyParameter;
import com.integral.staging.oms.model.OMSOrderParam;

public class OMSPass extends OMSMessage
{
	public static OMSMsgType msgType = OMSMsgType.PASS;
	
	private List<OMSOrderParam> omsOrders;

	public List<OMSOrderParam> getOmsOrders()
	{
		return omsOrders;
	}

	public void setOmsOrders( List<OMSOrderParam> omsOrders )
	{
		this.omsOrders = omsOrders;
	}

	@Override
	public int getEstimatedSize()
	{
		return 400;
	}

	@Override
	public void writeTo( UnSafeBuffer safeBuf )
	{
		super.writeTo(safeBuf);
		if ( omsOrders != null )
		{
			safeBuf.putBoolean(true);
			safeBuf.putInt(omsOrders.size());
			for(OMSOrderParam orderParam : omsOrders) {
				putIfNotNull(safeBuf, orderParam.getClientOrderId());
				putIfNotNull(safeBuf, orderParam.getCcyPair());
				putIfNotNull(safeBuf, orderParam.getDealtCcy());
				putIfNotNull(safeBuf, orderParam.getSide());
				putIfNotNull(safeBuf, orderParam.getDealtAmt());
				putIfNotNull(safeBuf, orderParam.getOrderType());
				putIfNotNull(safeBuf, orderParam.getOrderPrice());
				putIfNotNull(safeBuf, orderParam.getCptyA());
				putIfNotNull(safeBuf, orderParam.getCptyAOrg());
				putIfNotNull(safeBuf, orderParam.getTif());
				putIfNotNull(safeBuf, orderParam.getExpiryTime());
				safeBuf.putBoolean(orderParam.isPrimaryOrder());
				putIfNotNull(safeBuf, orderParam.getCustNotes());
				putIfNotNull(safeBuf, orderParam.getFixingReference());
				putIfNotNull(safeBuf, orderParam.getFixingTime());
				for(OMSContigencyParameter omsContigencyParameter : orderParam.getContigencyParameters()){
					putIfNotNull(safeBuf, omsContigencyParameter.getType()+"");
				}
			}

		}
		else
		{
			safeBuf.putBoolean(false);
		}

	}

	@Override
	public void readFrom( UnSafeBuffer safeBuf )
	{
		super.readFrom(safeBuf);
		
		boolean isSet = safeBuf.getBoolean();
		if ( isSet )
		{
			int orderCount = safeBuf.getInt();
			this.omsOrders = new ArrayList<OMSOrderParam>(orderCount);
			for ( int i = 0 ; i < orderCount ; i++ )
			{
				OMSOrderParam orderParam = new OMSOrderParam();
				orderParam.setClientOrderId(getIfPresent(safeBuf));
				orderParam.setCcyPair(getIfPresent(safeBuf));
				orderParam.setDealtCcy(getIfPresent(safeBuf));
				orderParam.setSide(getIfPresent(safeBuf));
				orderParam.setDealtAmt(getDoubleIfPresent(safeBuf));
				orderParam.setOrderType(getIfPresent(safeBuf));
				orderParam.setOrderPrice(getDoubleIfPresent(safeBuf));
				orderParam.setCptyA(getIfPresent(safeBuf));
				orderParam.setCptyAOrg(getIfPresent(safeBuf));
				orderParam.setTif(getIfPresent(safeBuf));
				orderParam.setExpiryTime(getLongIfPresent(safeBuf));
				orderParam.setPrimaryOrder(safeBuf.getBoolean());
				orderParam.setCustNotes(getIfPresent(safeBuf));
				orderParam.setFixingReference(getIfPresent(safeBuf));
				orderParam.setFixingTime(getLongIfPresent(safeBuf));
				String type = getIfPresent(safeBuf);
				if(type != null){
					List<OMSContigencyParameter> cps = new ArrayList<OMSContigencyParameter>();	
					OMSContigencyParameter contParam = new OMSContigencyParameter();
					contParam.setType(Integer.parseInt(type));	
					cps.add(contParam);
					orderParam.setContigencyParameters(cps);
				}
				omsOrders.add(orderParam);
			}
		}
	}
	
	@Override
	public OMSMsgType getMsgType()
	{
		return msgType;
	}

}
