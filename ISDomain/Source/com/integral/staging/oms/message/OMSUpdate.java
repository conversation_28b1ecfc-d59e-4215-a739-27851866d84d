package com.integral.staging.oms.message;

import com.integral.commons.buffers.UnSafeBuffer;


public class OMSUpdate extends OMSMessage
{
	public static OMSMsgType msgType = OMSMsgType.UPDATE;
	
	public enum OrderUpdatedStatus {
		FILLED("FILLED"),
		PARTIAL_FILLED("PARTIAL_FILLED"),
		EXPIRED("EXPIRED");
		
		private String value;
		OrderUpdatedStatus(String value){
			this.value = value;
		}
		public String getValue() {
			return value;
		}		
	}

	private double filledAmount;
	private String orderUpdatedStatus;
	private String tradeId;
	
	public String getTradeId() {
		return tradeId;
	}

	public void setTradeId(String tradeId) {
		this.tradeId = tradeId;
	}

	public double getFilledAmount() {
		return filledAmount;
	}

	public void setFilledAmount(double filledAmount) {
		this.filledAmount = filledAmount;
	}

	public String getOrderUpdatedStatus() {
		return orderUpdatedStatus;
	}

	public void setOrderUpdatedStatus(String orderUpdatedStatus) {
		this.orderUpdatedStatus = orderUpdatedStatus;
	}

	@Override
	public OMSMsgType getMsgType()
	{
		return msgType;
	}

	@Override
	public String toString()
	{
		return super.toString();
	}
	
	@Override
	public void writeTo( UnSafeBuffer safeBuf )
	{		
		super.writeTo(safeBuf);
		putIfNotNull(safeBuf, orderUpdatedStatus);
		putIfNotNull(safeBuf, tradeId);
	}

	@Override
	public void readFrom( UnSafeBuffer safeBuf )
	{
		super.readFrom(safeBuf);
		this.orderUpdatedStatus = getIfPresent(safeBuf);
		this.tradeId = getIfPresent(safeBuf);
	}
}
