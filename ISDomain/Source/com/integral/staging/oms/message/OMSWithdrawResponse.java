package com.integral.staging.oms.message;

import com.integral.commons.buffers.UnSafeBuffer;

public class OMSWithdrawResponse extends OMSWithdraw
{
	private boolean cancelBeforeAccept = false;
		
	public boolean isCancelBeforeAccept() {
		return cancelBeforeAccept;
	}

	public void setCancelBeforeAccept(boolean cancelBeforeAccept) {
		this.cancelBeforeAccept = cancelBeforeAccept;
	}

	@Override
	public OMSMsgType getMsgType()
	{
		return OMSMsgType.WITHDRAW_RESPONSE;
	}
	
	@Override
	public String toString()
	{
		return super.toString();
	}
	
	@Override
	public void writeTo( UnSafeBuffer safeBuf )
	{
		super.writeTo(safeBuf);
		safeBuf.putBoolean(cancelBeforeAccept);
	}

	@Override
	public void readFrom( UnSafeBuffer safeBuf )
	{
		super.readFrom(safeBuf);
		this.cancelBeforeAccept = safeBuf.getBoolean();
	}
	
}
