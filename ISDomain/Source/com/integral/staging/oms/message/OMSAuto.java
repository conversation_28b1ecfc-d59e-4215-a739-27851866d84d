package com.integral.staging.oms.message;

import com.integral.commons.buffers.UnSafeBuffer;


public class OMSAuto extends OMSMessage
{
	public static OMSMsgType msgType = OMSMsgType.AUTO;
	

	@Override
	public OMSMsgType getMsgType()
	{
		return msgType;
	}

	@Override
	public String toString()
	{
		return super.toString();
	}
	
	@Override
	public void writeTo( UnSafeBuffer safeBuf )
	{
		super.writeTo(safeBuf);
	}

	@Override
	public void readFrom( UnSafeBuffer safeBuf )
	{
		super.readFrom(safeBuf);
	}
}
