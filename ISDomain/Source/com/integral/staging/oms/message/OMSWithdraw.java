package com.integral.staging.oms.message;

import com.integral.commons.buffers.UnSafeBuffer;


public class OMSWithdraw extends OMSMessage
{
	public static OMSMsgType msgType = OMSMsgType.WITHDRAW;
	
	
	private boolean expired = false;
	private boolean auto =  false;
	
	public boolean isAuto() {
		return auto;
	}

	public void setAuto(boolean auto) {
		this.auto = auto;
	}

	public boolean isExpired() {
		return expired;
	}

	public void setExpired(boolean expired) {
		this.expired = expired;
	}

	
	@Override
	public void writeTo( UnSafeBuffer safeBuf )
	{
		super.writeTo(safeBuf);
		safeBuf.putBoolean(expired);
		safeBuf.putBoolean(auto);
	}

	@Override
	public void readFrom( UnSafeBuffer safeBuf )
	{
		super.readFrom(safeBuf);
		this.expired = safeBuf.getBoolean();
		this.auto = safeBuf.getBoolean();
	}
	

	@Override
	public OMSMsgType getMsgType()
	{
		return msgType;
	}
	
	@Override
	public String toString()
	{
		return super.toString();
	}

}
