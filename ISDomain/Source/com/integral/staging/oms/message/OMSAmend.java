package com.integral.staging.oms.message;

import com.integral.commons.buffers.UnSafeBuffer;


public class OMSAmend extends OMSMessage
{
	public static OMSMsgType msgType = OMSMsgType.AMEND;


	private Double dealtAmt;
	private Double orderPrice;
	private boolean auto =  false;

	public Double getDealtAmt() {
		return dealtAmt;
	}

	public void setDealtAmt(Double dealtAmt) {
		this.dealtAmt = dealtAmt;
	}

	public Double getOrderPrice() {
		return orderPrice;
	}

	public boolean isAuto() {
		return auto;
	}

	public void setAuto(boolean auto) {
		this.auto = auto;
	}

	public void setOrderPrice(Double orderPrice) {
		this.orderPrice = orderPrice;
	}


	@Override
	public void writeTo( UnSafeBuffer safeBuf )
	{
		super.writeTo(safeBuf);
		putIfNotNull(safeBuf, dealtAmt);
		putIfNotNull(safeBuf, orderPrice);
		safeBuf.putBoolean(auto);
	}

	@Override
	public void readFrom( UnSafeBuffer safeBuf )
	{
		super.readFrom(safeBuf);
		this.dealtAmt = getDoubleIfPresent(safeBuf);
		this.orderPrice = getDoubleIfPresent(safeBuf);
		this.auto = safeBuf.getBoolean();
	}


	@Override
	public OMSMsgType getMsgType()
	{
		return msgType;
	}

	@Override
	public String toString()
	{
		StringBuilder builder = new StringBuilder();
		builder.append(super.toString());
		builder.append("dealtAmt").append(" : ").append(dealtAmt).append(", ");
		builder.append("orderPrice").append(" : ").append(orderPrice).append(", ");
		return builder.toString();
	}
}
