package com.integral.staging.oms.message;

import java.util.HashMap;

public enum OMSMsgType
{
	PASS((short) 1), PASS_RESPONSE((short) 2), WITHDRAW((short) 3), WITHDRAW_RESPONSE((short) 4), ACCEPT((short) 5), 
	ACCEPT_RESPONSE((short) 6), REJECT((short) 7), REJECT_RESPONSE((short) 8), FILL((short) 9), FILL_RESPONSE((short) 10),
	CANCEL((short) 11), CANCEL_RESPONSE((short) 12),AUTO((short) 13), AUTO_RESPONSE((short) 14),CONFIRM((short) 15),MANUAL((short) 16),
	MANUAL_RESPONSE((short) 17), UPDATE((short) 18), WITHDRAW_ACCEPT((short) 19), WITHDRAW_ACCEPT_RESPONSE((short) 20),
	WITHDRAW_REJECT((short) 21), WITHDRAW_REJECT_RESPONSE((short) 22), AMEND((short) 23), AMEND_RESPONSE((short) 24),
	AMEND_ACCEPT((short) 25), AMEND_ACCEPT_RESPONSE((short) 26), AMEND_REJECT((short) 27), AMEND_REJECT_RESPONSE((short) 28);

	short msgTypeCode;

	public short getMsgTypeCode()
	{
		return msgTypeCode;
	}

	private OMSMsgType( short msgTypeCode )
	{
		this.msgTypeCode = msgTypeCode;
	}

	public static OMSMsgType getMsgType( short msgTypeCode )
	{
		return msgTypeToEnum.get(msgTypeCode);
	}

	private static HashMap<Short, OMSMsgType> msgTypeToEnum = new HashMap<Short, OMSMsgType>();

	static
	{
		for ( OMSMsgType e : OMSMsgType.values() )
		{
			msgTypeToEnum.put(e.getMsgTypeCode(), e);
		}
	}

}
