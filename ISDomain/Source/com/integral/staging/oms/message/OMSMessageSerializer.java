package com.integral.staging.oms.message;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.commons.serialize.Storeable;

public class OMSMessageSerializer
{
	private final static int VERSION_OFFSET = 0;

	private final static int MSG_TYPE_OFFSET = VERSION_OFFSET + UnSafeBuffer.getSizeOfByte();

	public static short getMsgType(UnSafeBuffer safeBuf) {
		return UnSafeBuffer.getShort(safeBuf.array(), MSG_TYPE_OFFSET);
	}

	public static byte getVersion(UnSafeBuffer safeBuf) {
		return UnSafeBuffer.getByte(safeBuf.array(), VERSION_OFFSET);
	}
	
	public static int getEstimated(Storeable storeable){
		return storeable.getEstimatedSize();
	}

	public static void serialize(short msgType, byte version, Storeable msg, UnSafeBuffer safeBuf) {
		safeBuf.put(version);
		safeBuf.putShort(msgType);
		msg.writeTo(safeBuf);
	}

	public static void deserialize(short msgType, byte version, Storeable msg, UnSafeBuffer safeBuf) {
		safeBuf.skip(3); //2 for msgType and 1 for version
		msg.readFrom(safeBuf);
	}


}
