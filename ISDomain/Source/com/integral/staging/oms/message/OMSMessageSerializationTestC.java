package com.integral.staging.oms.message;

import java.util.ArrayList;
import java.util.List;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.staging.oms.conn.OMSServerResponseHandler;
import com.integral.staging.oms.model.OMSOrderParam;
import com.integral.test.TestCaseC;

public class OMSMessageSerializationTestC extends TestCaseC
{

	
	public OMSMessageSerializationTestC( String aName )
	{
		super(aName);
	}

	public void testOMSPassSerialization() {
		OMSPass passRequest = new OMSPass();
		passRequest.setFromOMSOrg("MSFX");
		passRequest.setToOMSOrg("Broker1");
		passRequest.setOrderId("order1");
		passRequest.setEmsOrder(true);
		passRequest.setClientOrderId("clientOrderId");
		
		List<OMSOrderParam> params = new ArrayList<OMSOrderParam>();
	
		OMSOrderParam orderParam = new OMSOrderParam();
		orderParam.setClientOrderId("clOrderId1");
		orderParam.setCcyPair("EUR/USD");
		orderParam.setDealtCcy("EUR");
		orderParam.setDealtAmt(1000.0);
		orderParam.setSide("BUY");
		orderParam.setOrderType("LIMIT");
		orderParam.setOrderPrice(1.2345);
		orderParam.setCptyA("MSFXle1");
		orderParam.setCptyAOrg("MSFX");
		orderParam.setTif("GTC");
		orderParam.setExpiryTime(null);
		orderParam.setContigencyParameters(null);
		orderParam.setPrimaryOrder(true);
		params.add(orderParam);

		passRequest.setOmsOrders(params);
		
		UnSafeBuffer unsafeBuf = new UnSafeBuffer();
		unsafeBuf.init(new byte[131072]);
		OMSMessageSerializer.serialize(passRequest.getMsgType().getMsgTypeCode(), (byte)1, passRequest, unsafeBuf);
		unsafeBuf.flip();
		OMSPass desPassMessage = null;
		try {
			desPassMessage = (OMSPass)OMSServerResponseHandler.deserializeAndHandleMessage(unsafeBuf);
		} catch(Exception e) {
			
		}
		assertNotNull(desPassMessage);
		assertNotNull(desPassMessage.getOmsOrders());
		assertEquals(desPassMessage.getFromOMSOrg(), passRequest.getFromOMSOrg());
		assertEquals(desPassMessage.getToOMSOrg(), passRequest.getToOMSOrg());
		assertEquals(desPassMessage.getOrderId(), passRequest.getOrderId());
		assertEquals(desPassMessage.isEmsOrder(), passRequest.isEmsOrder());
		
		OMSOrderParam desOrderParam = desPassMessage.getOmsOrders().get(0);
		
		assertEquals(orderParam.getClientOrderId(), desOrderParam.getClientOrderId());
		assertEquals(orderParam.getCcyPair(), desOrderParam.getCcyPair());
		assertEquals(orderParam.getDealtCcy(), desOrderParam.getDealtCcy());
		assertEquals(orderParam.getSide(), desOrderParam.getSide());
		assertEquals(orderParam.getOrderType(), desOrderParam.getOrderType());
		assertEquals(orderParam.getCptyA(), desOrderParam.getCptyA());
		assertEquals(orderParam.getCptyAOrg(), desOrderParam.getCptyAOrg());
		assertEquals(orderParam.getTif(), desOrderParam.getTif());
		assertEquals(orderParam.isPrimaryOrder(), desOrderParam.isPrimaryOrder());
		
	}
	
	public void testOMSMessageSerialization() {
		messagePersistence(new OMSPass());
		messagePersistence(new OMSPassResponse());
		messagePersistence(new OMSAccept());
		messagePersistence(new OMSAcceptResponse());
		messagePersistence(new OMSWithdraw());
		messagePersistence(new OMSWithdrawResponse());
		messagePersistence(new OMSReject());
		messagePersistence(new OMSRejectResponse());
		messagePersistence(new OMSFill());
		messagePersistence(new OMSFillResponse());
		messagePersistence(new OMSAuto());
		messagePersistence(new OMSAutoResponse());
		messagePersistence(new OMSOrderConfirmation());
		
	}
	
	public void messagePersistence(OMSMessage message) {
		message.setFromOMSOrg("MSFX");
		message.setToOMSOrg("Broker1");
		message.setOrderId("order1");
		message.setEmsOrder(true);
		message.setClientOrderId("clientOrderId");
		message.setFailed(true);
		message.setFailureReason("failureReason");
		message.setUser("user");
		
		UnSafeBuffer unsafeBuf = new UnSafeBuffer();
		unsafeBuf.init(new byte[131072]);
		OMSMessageSerializer.serialize(message.getMsgType().getMsgTypeCode(), (byte)1, message, unsafeBuf);
		unsafeBuf.flip();
		OMSMessage deserializedMessage = null;
		try {
			deserializedMessage = (OMSMessage)OMSServerResponseHandler.deserializeAndHandleMessage(unsafeBuf);
		} catch(Exception e) {
			
		}
		assertEquals(message.getMsgType(), deserializedMessage.getMsgType());
		assertNotNull(deserializedMessage);
		
		assertEquals(deserializedMessage.getFromOMSOrg(), message.getFromOMSOrg());
		assertEquals(deserializedMessage.getToOMSOrg(), message.getToOMSOrg());
		assertEquals(deserializedMessage.getOrderId(), message.getOrderId());
		assertEquals(deserializedMessage.isEmsOrder(), message.isEmsOrder());
		assertEquals(deserializedMessage.getClientOrderId(), message.getClientOrderId());
		assertEquals(deserializedMessage.getFailureReason(), message.getFailureReason());
		assertEquals(deserializedMessage.isFailed(), message.isFailed());
		assertEquals(deserializedMessage.getUser(), message.getUser());
		
		
	}
	
}
