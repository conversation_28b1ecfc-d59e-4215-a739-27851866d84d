package com.integral.staging.oms.message;

import com.integral.commons.buffers.UnSafeBuffer;


public class OMSOrderConfirmation extends OMSMessage
{
	public static OMSMsgType msgType = OMSMsgType.CONFIRM;
	
	private String tradeId;
	private double executionPrice;
	
	public double getExecutionPrice()
	{
		return executionPrice;
	}

	public void setExecutionPrice( double executionPrice )
	{
		this.executionPrice = executionPrice;
	}

	public String getTradeId()
	{
		return tradeId;
	}

	public void setTradeId( String tradeId )
	{
		this.tradeId = tradeId;
	}
	@Override
	public OMSMsgType getMsgType()
	{
		return msgType;
	}

	@Override
	public String toString()
	{
		return super.toString();
	}
	
	@Override
	public void writeTo( UnSafeBuffer safeBuf )
	{
		super.writeTo(safeBuf);
		putIfNotNull(safeBuf, tradeId);
		putIfNotNull(safeBuf, executionPrice);
	}

	@Override
	public void readFrom( UnSafeBuffer safeBuf )
	{
		super.readFrom(safeBuf);
		tradeId = getIfPresent(safeBuf);
		executionPrice = getDoubleIfPresent(safeBuf);
	}
}
