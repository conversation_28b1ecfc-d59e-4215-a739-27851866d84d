package com.integral.staging.oms.message;

import com.integral.commons.buffers.UnSafeBuffer;


public class OMSReject extends OMSMessage
{
	public static OMSMsgType msgType = OMSMsgType.REJECT;
	
	private String reason;
	public String getReason()
	{
		return reason;
	}

	public void setReason( String reason )
	{
		this.reason = reason;
	}

	@Override
	public OMSMsgType getMsgType()
	{
		return msgType;
	}

	@Override
	public String toString()
	{
		return super.toString();
	}
	
	@Override
	public void writeTo( UnSafeBuffer safeBuf )
	{
		super.writeTo(safeBuf);
		putIfNotNull(safeBuf, reason);
	}

	@Override
	public void readFrom( UnSafeBuffer safeBuf )
	{
		super.readFrom(safeBuf);
		this.reason = getIfPresent(safeBuf);
	}
}
