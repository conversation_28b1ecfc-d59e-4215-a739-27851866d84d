package com.integral.staging.oms.message;

import com.integral.commons.buffers.UnSafeBuffer;

public class OMSFill extends OMSMessage
{
	OMSMsgType msgType = OMSMsgType.FILL;
	private String tradeId;
	private String fillId;
	private double fillPrice;
	private boolean ymBBook;
	private Double venueBMR;
	private Double dealtAmt;
	private Long creditWorkflowMode;
	private Double ppSpotSpread;
	
	public boolean isYmBBook()
	{
		return ymBBook;
	}

	public void setYmBBook( boolean ymBBook )
	{
		this.ymBBook = ymBBook;
	}

	public String getTradeId()
	{
		return tradeId;
	}

	public void setTradeId( String tradeId )
	{
		this.tradeId = tradeId;
	}

	public String getFillId()
	{
		return fillId;
	}

	public void setFillId( String fillId )
	{
		this.fillId = fillId;
	}

	public double getFillPrice()
	{
		return fillPrice;
	}

	public void setFillPrice( double fillPrice )
	{
		this.fillPrice = fillPrice;
	}

	public Double getVenueBMR() {
		return venueBMR;
	}

	public void setVenueBMR(Double venueBMR) {
		this.venueBMR = venueBMR;
	}

	public Double getDealtAmt() {
		return dealtAmt;
	}

	public void setDealtAmt(Double dealtAmt) {
		this.dealtAmt = dealtAmt;
	}

	public Long getCreditWorkflowMode() {
		return creditWorkflowMode;
	}

	public void setCreditWorkflowMode(Long creditWorkflowMode) {
		this.creditWorkflowMode = creditWorkflowMode;
	}

	public Double getPpSpotSpread() {
		return ppSpotSpread;
	}

	public void setPpSpotSpread(Double ppSpotSpread) {
		this.ppSpotSpread = ppSpotSpread;
	}

	@Override
	public void writeTo( UnSafeBuffer safeBuf )
	{
		super.writeTo(safeBuf);
		putIfNotNull(safeBuf, tradeId);
		putIfNotNull(safeBuf, fillId);
		putIfNotNull(safeBuf, fillPrice);
		safeBuf.putBoolean(ymBBook);
		putIfNotNull(safeBuf, venueBMR);
		putIfNotNull(safeBuf, dealtAmt);
		putIfNotNull(safeBuf, creditWorkflowMode);
		putIfNotNull(safeBuf, ppSpotSpread);
	}

	@Override
	public void readFrom( UnSafeBuffer safeBuf )
	{
		super.readFrom(safeBuf);
		tradeId = getIfPresent(safeBuf);
		fillId = getIfPresent(safeBuf);
		fillPrice = getDoubleIfPresent(safeBuf);
		ymBBook = safeBuf.getBoolean();
		venueBMR = getDoubleIfPresent(safeBuf);
		dealtAmt = getDoubleIfPresent(safeBuf);
		creditWorkflowMode = getLongIfPresent(safeBuf);
		ppSpotSpread = getDoubleIfPresent(safeBuf);
	}


	@Override
	public OMSMsgType getMsgType()
	{
		return msgType;
	}
	
	@Override
	public String toString()
	{
		return super.toString();
	}
}
