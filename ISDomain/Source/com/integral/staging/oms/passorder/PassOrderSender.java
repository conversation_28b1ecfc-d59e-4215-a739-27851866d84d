package com.integral.staging.oms.passorder;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;

import com.integral.is.common.exception.MessageCommunicationExceptionC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.persistence.Namespace;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.staging.Order;
import com.integral.staging.OrderStagingServiceFactory;
import com.integral.staging.StagingAreaPersistenceService;
import com.integral.staging.oms.OMSConstants;
import com.integral.staging.oms.OMSUtil;
import com.integral.staging.oms.audit.OMSAuditService;
import com.integral.staging.oms.client.OMSNotifier;
import com.integral.staging.oms.client.socketio.OMSResponse;
import com.integral.staging.oms.config.OMSConfig;
import com.integral.staging.oms.conn.imtp.OMSServerException;
import com.integral.staging.oms.conn.service.OMSServerConnectionManager;
import com.integral.staging.oms.message.*;
import com.integral.staging.oms.model.OMSEvent;
import com.integral.staging.oms.model.OMSOrderParam;
import com.integral.staging.oms.model.OMSState;
import com.integral.staging.oms.orderbook.FXStagingOrderBookManager;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;

public class PassOrderSender
{
	private static Log log = LogFactory.getLog(PassOrderSender.class);
	
	static BlockingQueue<OMSMessage> pendingOMSMessages = new ArrayBlockingQueue<OMSMessage>(5000000);
	
	static {
		(new PassOrderSender.OMSSenderThread()).start();
	}
		
	static class OMSSenderThread extends Thread{

		@Override
		public void run() {
			
			while(true) {
				OMSMessage omsMessage = null;
				try {
					omsMessage = pendingOMSMessages.take();
				
				while (true) {
					String errorMsgStr = sendMessage(omsMessage);
					if (errorMsgStr != null && errorMsgStr.contains("Connection to Org") && errorMsgStr.contains("is inactive")) {
						try {
							log.warn(OMSConstants.OMS_LOG_PREFIX + " Trying to resend to OMS" + omsMessage);
							Thread.sleep(60000);
						} catch (InterruptedException e) {}
						continue;
					} else if (errorMsgStr == null){
						break;
					} else {
						log.warn(OMSConstants.OMS_LOG_PREFIX + " Failed to send to OMS " + omsMessage);
						break;
					}
				}
				} catch (Exception e1) {
					if(omsMessage != null){
						try {
							pendingOMSMessages.put(omsMessage);
						} catch (InterruptedException e) {}
					}
				}
			}
			
		}
		
		
		
	}
	
	public static void addToPendingMessageQueue(OMSMessage omsMessage){
		try {
			pendingOMSMessages.put(omsMessage);
		} catch (InterruptedException e) {}
	}

	/**
	 * Pass OMS order another OMS org
	 * @param order
	 * @param toOMSOrg
	 * @return
	 */
	public static String pass( Order order, String toOMSOrg )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + " pass : Passing order org " + order.getNamespaceName() + " orderId " + order.getId() + " toOrg " + toOMSOrg);

		synchronized ( order ) {
			/*if(!order.isPrimaryOrder()) {
				//Only primary order is allowed for passing
				return "Linked child orders cannot be passed";
			}*/

			List<Order> orders = new ArrayList<Order>(3);
			List<Order> linkedOrders = OMSUtil.getLinkedOrders(order);
			if ( linkedOrders.size() > 0 && OMSConfig.getInstance().isLinkedOrderPassingAllowed() ) {
				orders.addAll(linkedOrders);
			}

			orders.add(order);

			OMSPass passRequest = new OMSPass();
			passRequest.setFromOMSOrg(order.getNamespaceName());
			passRequest.setToOMSOrg(toOMSOrg);
			passRequest.setUser(IdcUtilC.getSessionContextUser().getShortName());
			passRequest.setClientOrderId(order.get_id());

			List<OMSOrderParam> params = new ArrayList<OMSOrderParam>();
			for ( Order orderToPass : orders ) {
				OMSOrderParam orderParam = new OMSOrderParam();
				orderParam.setClientOrderId(orderToPass.getId());
				orderParam.setCcyPair(orderToPass.getCcyPair());
				orderParam.setDealtCcy(orderToPass.getDealtCcy());
				orderParam.setDealtAmt(orderToPass.getDealtAmt());
				orderParam.setSide(orderToPass.isBuy() ? "BUY" : "SELL");
				orderParam.setOrderType(orderToPass.getType().name());
				orderParam.setOrderPrice(orderToPass.getPrice());
				orderParam.setCptyA(orderToPass.getCounterPartyB());
				orderParam.setCptyAOrg(orderToPass.getCounterPartyBOrg());
				orderParam.setTif(orderToPass.getTif().name());
				orderParam.setExpiryTime(orderToPass.getExpiryTime());
				orderParam.setContigencyParameters(OMSUtil.toOMSContingencyParams(orderToPass.getOrderContingencies()));
				orderParam.setPrimaryOrder(orderToPass.isPrimaryOrder());
				params.add(orderParam);
			}

			passRequest.setOmsOrders(params);

			String errorMsgStr = sendMessage(passRequest);
			if ( errorMsgStr != null ) {
				return errorMsgStr;
			}
			return null;
		}
	}

	/**
	 * Withdraw Passed OMS order
	 * @param order
	 * @return
	 */
	public static String withdraw( Order order )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + " withdraw : Withdrawing passed order  org " + order.getNamespaceName() + " orderId " + order.getId() + " toOrg " + order.getToOrg());
		synchronized ( order ) {
			OMSWithdraw omsWithdraw = new OMSWithdraw();
			omsWithdraw.setFromOMSOrg(order.getNamespaceName());
			omsWithdraw.setToOMSOrg(order.getToOrg());
			omsWithdraw.setUser(IdcUtilC.getSessionContextUser().getShortName());
			omsWithdraw.setClientOrderId(order.getId());
			String errorMsgStr = sendMessage(omsWithdraw);
			if ( errorMsgStr != null ) {
				return errorMsgStr;
			}
			return null;
		}

	}

	public static void onAutoOrder( OMSAuto omsAuto )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + " onAutoOrder : on Auto Order " + omsAuto);
		OMSAutoResponse omsAutoResp = (OMSAutoResponse)populateResponse(omsAuto, new OMSAutoResponse());
		if ( omsAuto.isEmsOrder() ) {
			ErrorMessage errorMessage = EMSOMSOrderResponseHandler.handleAuto(omsAuto);
			if ( !OMSUtil.isSuccess(errorMessage) ) {
				omsAutoResp.setFailureReason(errorMessage.getCode());
				omsAutoResp.setFailed(true);
			}
		}
		else {
			log.warn(OMSConstants.OMS_LOG_PREFIX + " onAutoOrder :  Auto is not supported for OMS orders" + omsAuto);
			omsAutoResp.setFailureReason(" Auto is not supported for OMS orders");
			omsAutoResp.setFailed(true);
		}
		String errorMsgStr = sendMessage(omsAutoResp);
		if ( errorMsgStr != null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + " onAutoOrder : Failed to send Reject response" + omsAuto);
			return;
		}
	}
	
	public static String onUpdateOMSOrder( OMSUpdate omsUpdate )
	{
		final OMSUpdate omsOrderUpdate = omsUpdate;
		log.info(OMSConstants.OMS_LOG_PREFIX + " onUpdateOrder : on Update Order " + omsOrderUpdate);		
		String errorMsgStr = sendMessage(omsOrderUpdate);
		if ( errorMsgStr != null ) {
			if (errorMsgStr.contains("Connection to Org") && errorMsgStr.contains("is inactive")) {
				// This code executes when OMS server is not up
				addToPendingMessageQueue(omsOrderUpdate);
				return null;
			}
			log.warn(OMSConstants.OMS_LOG_PREFIX + " onUpdateOrder : Failed to send update " + omsOrderUpdate);
			return errorMsgStr;
		}
		return null;
	}
	
	public static void onManualOrder( OMSManual omsManual )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + " onManualOrder : on Manual Order " + omsManual);
		OMSManualResponse omsManualResp = (OMSManualResponse)populateResponse(omsManual, new OMSManualResponse());
		if ( omsManual.isEmsOrder() ) {
			ErrorMessage errorMessage = EMSOMSOrderResponseHandler.handleManual(omsManual);
			if ( !OMSUtil.isSuccess(errorMessage) ) {
				omsManualResp.setFailureReason(errorMessage.getCode());
				omsManualResp.setFailed(true);
			}
		}
		else {
			log.warn(OMSConstants.OMS_LOG_PREFIX + " onManualOrder :  Manual is not supported for OMS orders" + omsManual);
			omsManualResp.setFailureReason(" Manual is not supported for OMS orders");
			omsManualResp.setFailed(true);
		}
		String errorMsgStr = sendMessage(omsManualResp);
		if ( errorMsgStr != null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + " onManualOrder : Failed to send Reject response" + omsManual);
			return;
		}
	}

	public static void onPassResponse( OMSPassResponse passResponse )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + " onPassResponse : " + passResponse);

		if ( passResponse.isEmsOrder() ) {
			EMSOMSOrderResponseHandler.handleSubmitResponse(passResponse);
		}
		else {
			Organization org = ReferenceDataCacheC.getInstance().getOrganization(passResponse.getToOMSOrg());
			for ( OMSOrderParam orderParam : passResponse.getOmsOrders() ) {
				Order order = OrderStagingServiceFactory.getOrderStagingService().getOrder(orderParam.getClientOrderId(), org.getNamespace());

				if ( order != null ) {
					if ( passResponse.getFailureReason() != null && passResponse.getFailureReason().trim().length() != 0 ) {
						log.info(OMSConstants.OMS_LOG_PREFIX + " onPassResponse : Pass order rejected for " + passResponse);
						OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_SENT_FAILED, OMSState.ACTIVE, OMSState.ACTIVE, null);
						OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
						OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), passResponse.getUser(), null));
					}
					else {
						synchronized ( order ) {
							order.setSentForPassing(true);
							order.setToOrg(passResponse.getFromOMSOrg());
							order.setState(OMSState.PASSED.getOrderStatusType().code);
							FXStagingOrderBookManager.withdrawOrder(order.get_id());
							ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

							if ( errorMsg != null ) {
								log.info(OMSConstants.OMS_LOG_PREFIX + " onPassResponse : Error while updating order " + passResponse);
								return;
							}
							else {
								OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_SENT, OMSState.ACTIVE, OMSState.PASSED, null);
								OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
								OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), passResponse.getUser(), null));
							}
						}
					}
				}
				else {
					log.warn(OMSConstants.OMS_LOG_PREFIX + " onPassResponse : Order not found " + orderParam.getClientOrderId() + " for Org " + org);
				}

			}

		}

	}

	public static void onAmendResponse(OMSAmendResponse amendResponse) {
		log.info(OMSConstants.OMS_LOG_PREFIX + "onAmendResponse : " + amendResponse);

		if (amendResponse.isAmendBeforeAccept() && amendResponse.isAuto()) {
			log.info(OMSConstants.OMS_LOG_PREFIX + "handleAmendResponse : Auto order amend is done without approval= " + amendResponse.getClientOrderId());
		}
		else if (amendResponse.isEmsOrder()) {
			EMSOMSOrderResponseHandler.handleAmendResponse(amendResponse);
		} else {
			log.info(OMSConstants.OMS_LOG_PREFIX + "onAmendResponse : Not implemented for passing between OMS orgs " + amendResponse);
		}
	}

	public static void onWithdrawResponse( OMSWithdrawResponse withdrawResponse )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onWithdrawResponse : " + withdrawResponse);

		if ( withdrawResponse.isEmsOrder() ) {
			EMSOMSOrderResponseHandler.handleWithdrawResponse(withdrawResponse);
		}
		else {
			Order order = getOrderFromMessage(withdrawResponse);

			if ( order != null ) {
				synchronized ( order ) {
					if ( withdrawResponse.getFailureReason() != null && withdrawResponse.getFailureReason().trim().length() != 0 ) {
						log.info(OMSConstants.OMS_LOG_PREFIX + "onWithdrawResponse : Withdraw Pass order rejected for " + withdrawResponse);
						OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_WITHDRAW_FAILED, OMSState.fromStatusCode(order.getState()), OMSState.fromStatusCode(order.getState()), null);
						OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
						OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), withdrawResponse.getUser(), null));
					}
					else {
						OMSState oldState = OMSState.fromStatusCode(order.getState());
						order.setAcceptedForPassing(false);
						order.setSentForPassing(false);
						order.setToOrg(null);
						order.setState(OMSState.TAKEN_BACK.getOrderStatusType().code);
						ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

						if ( errorMsg != null ) {
							log.info(OMSConstants.OMS_LOG_PREFIX + "onWithdrawResponse : Error while updating order " + withdrawResponse);
							return;
						}
						else {
							OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_WITHDRAWN, oldState, OMSState.TAKEN_BACK, null);
							OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
							OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), withdrawResponse.getUser(), null));
						}
					}
				}
			}
			else {
				log.warn(OMSConstants.OMS_LOG_PREFIX + " onWithdrawResponse Order not found " + withdrawResponse);
			}
		}

	}

	public static void onRejection( OMSReject omsReject )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onRejection  " + omsReject);
		OMSRejectResponse omsRejectResp = (OMSRejectResponse)populateResponse(omsReject, new OMSRejectResponse());
		boolean orderAlreadyExpired = false;
		if ( omsReject.isEmsOrder() ) {
			ErrorMessage errorMessage = EMSOMSOrderResponseHandler.handleReject(omsReject);
			if ( !OMSUtil.isSuccess(errorMessage) ) {
				omsRejectResp.setFailureReason(errorMessage.getCode());
				omsRejectResp.setFailed(true);
			}
		}
		else {
			Order order = getOrderFromMessage(omsReject);

			if ( order != null ) {
				synchronized ( order ) {
					OMSState oldState = OMSState.fromStatusCode(order.getState());
					if (!oldState.equals(OMSState.EXPIRED)) {
						order.setAcceptedForPassing(false);
						order.setSentForPassing(false);
						order.setToOrg(null);
						order.setState(OMSState.REJECTED.getOrderStatusType().code);
						ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

						if (errorMsg != null) {
							log.info(OMSConstants.OMS_LOG_PREFIX + "onRejection : Error while updating order " + omsReject);
							omsRejectResp.setFailureReason(OMSConstants.ORDER_UPDATE_FAILED);
							omsRejectResp.setFailed(true);
						} else {
							OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_REJECTED, oldState, OMSState.REJECTED, null);
							OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
							OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsReject.getUser(), omsReject.getFromOMSOrg()));
						}
					}else{
						orderAlreadyExpired = true;
						log.info(OMSConstants.OMS_LOG_PREFIX + "onRejection : Order already expired, " + omsReject);
					}
				}
			}
			else {
				log.warn(OMSConstants.OMS_LOG_PREFIX + " onRejection Order not found " + omsReject);
				omsRejectResp.setFailureReason(OMSConstants.ORDER_NOT_FOUND);
				omsRejectResp.setFailed(true);
			}
		}
		if(!orderAlreadyExpired) {
			String errorMsgStr = sendMessage(omsRejectResp);
			if (errorMsgStr != null) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + " Failed to send Reject response" + omsReject);
				return;
			}
		}
	}

	public static void onAmendRejection( OMSAmendReject omsAmendReject )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onAmendRejection  " + omsAmendReject);
		OMSAmendRejectResponse omsAmendRejectResp = (OMSAmendRejectResponse)populateResponse(omsAmendReject, new OMSAmendRejectResponse());

		if ( omsAmendReject.isEmsOrder() ) {
			ErrorMessage errorMessage = EMSOMSOrderResponseHandler.handleAmendReject(omsAmendReject);
			if ( !OMSUtil.isSuccess(errorMessage) ) {
				omsAmendRejectResp.setFailureReason(errorMessage.getCode());
				omsAmendRejectResp.setFailed(true);
			}
		}
		String errorMsgStr = sendMessage(omsAmendRejectResp);
		if ( errorMsgStr != null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + " Failed to send Amend Reject response" + omsAmendReject);
			return;
		}
	}

	public static void onAmendAcceptance( OMSAmendAccept omsAmendAccept )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onAmendAcceptance  " + omsAmendAccept);
		OMSAmendAcceptResponse omsAmendAcceptResp = (OMSAmendAcceptResponse)populateResponse(omsAmendAccept, new OMSAmendAcceptResponse());

		if ( omsAmendAccept.isEmsOrder() ) {
			ErrorMessage errorMessage = EMSOMSOrderResponseHandler.handleAmendAccept(omsAmendAccept);
			if ( !OMSUtil.isSuccess(errorMessage) ) {
				omsAmendAcceptResp.setFailureReason(errorMessage.getCode());
				omsAmendAcceptResp.setFailed(true);
			}
		}
		String errorMsgStr = sendMessage(omsAmendAcceptResp);
		if ( errorMsgStr != null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + " Failed to send Amend Accept response" + omsAmendAccept);
			return;
		}
	}

	public static void onCancelRejection( OMSWithdrawReject omsWithdrawReject )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onCancelRejection  " + omsWithdrawReject);
		OMSWithdrawRejectResponse omsWithdrawRejectResp = (OMSWithdrawRejectResponse)populateResponse(omsWithdrawReject, new OMSWithdrawRejectResponse());
		
		if ( omsWithdrawReject.isEmsOrder() ) {
			ErrorMessage errorMessage = EMSOMSOrderResponseHandler.handleCancelReject(omsWithdrawReject);
			if ( !OMSUtil.isSuccess(errorMessage) ) {
				omsWithdrawRejectResp.setFailureReason(errorMessage.getCode());
				omsWithdrawRejectResp.setFailed(true);
			}
		}
		else {
			Order order = getOrderFromMessage(omsWithdrawReject);

			if ( order != null ) {
				synchronized ( order ) {
					OMSState oldState = OMSState.fromStatusCode(order.getState());
					order.setAcceptedForPassing(true);
					order.setSentForPassing(true);
					order.setState(OMSState.ACCEPTED.getOrderStatusType().code);
					ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

					if ( errorMsg != null ) {
						log.info(OMSConstants.OMS_LOG_PREFIX + "onCancelRejection : Error while updating order " + omsWithdrawReject);
						omsWithdrawRejectResp.setFailureReason(OMSConstants.ORDER_UPDATE_FAILED);
						omsWithdrawRejectResp.setFailed(true);
					}
					else {
						OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_CANCEL_REJECTED, oldState, OMSState.ACCEPTED, null);
						OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
						OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsWithdrawReject.getUser(), omsWithdrawReject.getFromOMSOrg()));
					}
				}
			}
			else {
				log.warn(OMSConstants.OMS_LOG_PREFIX + " onCancelRejection Order not found " + omsWithdrawReject);
				omsWithdrawRejectResp.setFailureReason(OMSConstants.ORDER_NOT_FOUND);
				omsWithdrawRejectResp.setFailed(true);
			}
		}
		String errorMsgStr = sendMessage(omsWithdrawRejectResp);
		if ( errorMsgStr != null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + " Failed to send Reject response" + omsWithdrawReject);
			return;
		}
	}

	public static void onCancelation( OMSCancel omsCancel )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + " onCancelation  " + omsCancel);
		OMSCancelResponse omsCancelResponse = (OMSCancelResponse)populateResponse(omsCancel, new OMSCancelResponse());
		
		if ( omsCancel.isEmsOrder() ) {
			ErrorMessage errorMessage = EMSOMSOrderResponseHandler.handleCancel(omsCancel);
			if ( !OMSUtil.isSuccess(errorMessage) ) {
				omsCancelResponse.setFailureReason(errorMessage.getCode());
				omsCancelResponse.setFailed(true);
			}
		}
		else {
			Order order = getOrderFromMessage(omsCancel);
			if ( order != null ) {
				synchronized ( order ) {
					OMSState oldState = OMSState.fromStatusCode(order.getState());
					order.setAcceptedForPassing(false);
					order.setSentForPassing(false);
					order.setToOrg(null);
					order.setState(OMSState.CANCELLED.getOrderStatusType().code);
					ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

					if ( errorMsg != null ) {
						log.info(OMSConstants.OMS_LOG_PREFIX + " onCancelation : Error while updating order " + omsCancel);
						omsCancelResponse.setFailureReason(OMSConstants.ORDER_UPDATE_FAILED);
						omsCancelResponse.setFailed(true);
					}
					else {
						OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_CANCELLED, oldState, OMSState.CANCELLED, null);
						OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
						OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsCancel.getUser(), omsCancel.getFromOMSOrg()));
					}
				}
			}
			else {
				log.warn(OMSConstants.OMS_LOG_PREFIX + " onCancelation Order not found " + omsCancel);
				omsCancelResponse.setFailureReason(OMSConstants.ORDER_NOT_FOUND);
				omsCancelResponse.setFailed(true);
			}
		}

		String errorMsgStr = sendMessage(omsCancelResponse);
		if ( errorMsgStr != null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "onCancelation Failed to send Cancel response" + omsCancel);
			return;
		}
	}

	public static void onAcceptance( OMSAccept omsAccept )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + " onAcceptance : " + omsAccept);
		OMSAcceptResponse omsAcceptResp = (OMSAcceptResponse)populateResponse(omsAccept, new OMSAcceptResponse());


		if ( omsAccept.isEmsOrder() ) {
			ErrorMessage errorMessage = EMSOMSOrderResponseHandler.handleAccept(omsAccept);
			if ( !OMSUtil.isSuccess(errorMessage) ) {
				omsAcceptResp.setFailureReason(errorMessage.getCode());
				omsAcceptResp.setFailed(true);
			}
		}
		else {
			Order order = getOrderFromMessage(omsAccept);

			if ( order != null ) {
				synchronized ( order ) {
					OMSState oldState = OMSState.fromStatusCode(order.getState());
					order.setAcceptedForPassing(true);
					order.setSentForPassing(true);
					order.setState(OMSState.ACCEPTED.getOrderStatusType().code);

					ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

					if ( errorMsg != null ) {
						log.info(OMSConstants.OMS_LOG_PREFIX + " onAcceptance : Error while updating order " + omsAccept);
						omsAcceptResp.setFailureReason(OMSConstants.ORDER_UPDATE_FAILED);
						omsAcceptResp.setFailed(true);
					}
					else {
						OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_ACCEPTED, oldState, OMSState.ACCEPTED, null);
						OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
						OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsAccept.getUser(), omsAccept.getFromOMSOrg()));
					}
				}

			}
			else {
				log.warn(OMSConstants.OMS_LOG_PREFIX + " onAcceptance : Order not found " + omsAccept);
				omsAcceptResp.setFailureReason(OMSConstants.ORDER_NOT_FOUND);
				omsAcceptResp.setFailed(true);
			}
		}

		String errorMsgStr = sendMessage(omsAcceptResp);
		if ( errorMsgStr != null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "onAcceptance : Failed to send Accept response" + omsAccept);
			return;
		}
	}
	
	public static void onCancelAcceptance( OMSWithdrawAccept omsCancelAccept )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + " onCancelAcceptance : " + omsCancelAccept);
		OMSWithdrawAcceptResponse omsCancelAcceptResp = (OMSWithdrawAcceptResponse)populateResponse(omsCancelAccept, new OMSWithdrawAcceptResponse());


		if ( omsCancelAccept.isEmsOrder() ) {
			ErrorMessage errorMessage = EMSOMSOrderResponseHandler.handleCancelAccept(omsCancelAccept);
			if ( !OMSUtil.isSuccess(errorMessage) ) {
				omsCancelAcceptResp.setFailureReason(errorMessage.getCode());
				omsCancelAcceptResp.setFailed(true);
			}
		}
		else {
			Order order = getOrderFromMessage(omsCancelAccept);

			if ( order != null ) {
				synchronized ( order ) {
					OMSState oldState = OMSState.fromStatusCode(order.getState());					
					order.setState(OMSState.CANCELLED.getOrderStatusType().code);

					ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

					if ( errorMsg != null ) {
						log.info(OMSConstants.OMS_LOG_PREFIX + " onCancelAcceptance : Error while updating order " + omsCancelAccept);
						omsCancelAcceptResp.setFailureReason(OMSConstants.ORDER_UPDATE_FAILED);
						omsCancelAcceptResp.setFailed(true);
					}
					else {
						OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_CANCEL_ACCEPTED, oldState, OMSState.CANCELLED, null);
						OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
						OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsCancelAccept.getUser(), omsCancelAccept.getFromOMSOrg()));
					}
				}

			}
			else {
				log.warn(OMSConstants.OMS_LOG_PREFIX + " onCancelAcceptance : Order not found " + omsCancelAccept);
				omsCancelAcceptResp.setFailureReason(OMSConstants.ORDER_NOT_FOUND);
				omsCancelAcceptResp.setFailed(true);
			}
		}

		String errorMsgStr = sendMessage(omsCancelAcceptResp);
		if ( errorMsgStr != null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "onAcceptance : Failed to send Accept response" + omsCancelAccept);
			return;
		}
	}

	public static void onFill( OMSFill omsFill )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + " onFill  " + omsFill);

		String errorMsgStr = null;
		OMSFillResponse omsFillResp = (OMSFillResponse)populateResponse(omsFill, new OMSFillResponse());
		omsFillResp.setInitialFill( omsFill.getCreditWorkflowMode() == null);
		if ( omsFill.isEmsOrder() ) {
			ErrorMessage errorMessage = EMSOMSOrderResponseHandler.handleFill(omsFill);
			if ( !OMSUtil.isSuccess(errorMessage) ) {
				omsFillResp.setFailureReason(errorMessage.getCode());
				omsFillResp.setFailed(true);
			}
			else {
				omsFillResp.setTradeId(omsFill.getTradeId());
			}
		}
		else {
			Order order = getOrderFromMessage(omsFill);

			if ( order != null ) {
				synchronized ( order ) {
					OMSState oldState = OMSState.fromStatusCode(order.getState());
					order.setAcceptedForPassing(true);
					order.setSentForPassing(true);
					order.setFilledPrice(omsFill.getFillPrice());
					order.setMatchedPrice(omsFill.getFillPrice());
					order.setTransactionId(omsFill.getTradeId());
					order.setState(OMSState.FILLED.getOrderStatusType().code);
					order.setVenueBMR(omsFill.getVenueBMR());
					ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

					if ( errorMsg != null ) {
						log.info(OMSConstants.OMS_LOG_PREFIX + " onFill : Error while updating order " + omsFill);
						omsFillResp.setFailureReason(OMSConstants.ORDER_UPDATE_FAILED);
						omsFillResp.setFailed(true);
					}
					else {
						OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_FILLED, oldState, OMSState.FILLED, null);
						OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
						OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsFill.getUser(), omsFill.getFromOMSOrg()));
					}
				}
			}
			else {
				omsFillResp.setFailureReason(OMSConstants.ORDER_NOT_FOUND);
				omsFillResp.setFailed(true);
				log.warn(OMSConstants.OMS_LOG_PREFIX + " onFill : Order not found " + omsFill);
			}
		}

		errorMsgStr = sendMessage(omsFillResp);
		if ( errorMsgStr != null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + " onFill : Failed to send Fill response" + omsFill);
			return;
		}
	}

	public static String sendMessage( OMSMessage message )
	{
		try {
			if ( OMSConfig.getInstance().getOmsOrgs().contains(message.getToOMSOrg()) ) {
				//OMS Received is deployed on same OMS Server
				if ( !OMSNotificationProcessor.getInstance().notify(message) ) {
					return OMSConstants.FAILED_TO_SEND_ORDER;
				}
			}
			else {
				OMSServerConnectionManager.getInstance().getConnector().sendMessage(message);
			}
		}
		catch ( OMSServerException omsE ) {
			log.error(OMSConstants.OMS_LOG_PREFIX + " Error while sending message" + omsE.getMessage());
			return omsE.getMessage();
		}
		catch ( MessageCommunicationExceptionC mce ) {
			log.error(OMSConstants.OMS_LOG_PREFIX + " Message Communication error", mce);
			return OMSConstants.FAILED_TO_SEND_ORDER;
		}
		catch ( Exception e ) {
			log.error(OMSConstants.OMS_LOG_PREFIX + " Error while send message ", e);
			return OMSConstants.INTERNAL_SERVER_ERROR;
		}

		return null;

	}
	
	private static OMSMessage populateResponse( OMSMessage request, OMSMessage response )
	{
		response.setClientOrderId(request.getClientOrderId());
		response.setFromOMSOrg(request.getToOMSOrg());
		response.setToOMSOrg(request.getFromOMSOrg());
		response.setOrderId(request.getOrderId());
		response.setUser(request.getUser());
		response.setEmsOrder(request.isEmsOrder());
		return response;
	}

	private static Order getOrderFromMessage( OMSMessage omsMessage )
	{
		Namespace namespace = ReferenceDataCacheC.getInstance().getOrganization(omsMessage.getToOMSOrg()).getNamespace();
		Order order = OrderStagingServiceFactory.getOrderStagingService().getOrder(omsMessage.getClientOrderId(), namespace);
		return order;
	}
}
