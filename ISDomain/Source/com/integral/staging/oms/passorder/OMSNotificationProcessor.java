package com.integral.staging.oms.passorder;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.staging.oms.OMSConstants;
import com.integral.staging.oms.message.*;

public class OMSNotificationProcessor
{
	private Log log = LogFactory.getLog(OMSNotificationProcessor.class);
	private BlockingQueue<OMSMessage> messageQueue;
	
	private static class Holder
	{
		private static final OMSNotificationProcessor INSTANCE = new OMSNotificationProcessor();
	}

	public static OMSNotificationProcessor getInstance()
	{
		return Holder.INSTANCE;
	}

	private OMSNotificationProcessor()
	{
		messageQueue =  new LinkedBlockingQueue<OMSMessage>(); 
		NotificationExecutor executor = new NotificationExecutor();
	    Thread notificationProcessor = new Thread(executor, "OMSNotificationProcessor");
	    notificationProcessor.start();
	}
	
    public boolean notify(OMSMessage omsMessage) {
        boolean isOffered = messageQueue.offer(omsMessage);
        if (!isOffered) {
            log.error("notify(): message queue rejected event:" + omsMessage);
        }
        return isOffered;
    }
    
	public class NotificationExecutor implements Runnable {

	    public void run() {
	        while (true) {
	            try {
	                OMSMessage message = messageQueue.take();
	                
	            	if ( message != null )
					{
						OMSMsgType msgtype = message.getMsgType();
						log.info(OMSConstants.OMS_LOG_PREFIX + " NotificationExecutor.run() Received Msg " + message.toString());
						switch ( msgtype )
						{
						case PASS_RESPONSE :
							PassOrderSender.onPassResponse((OMSPassResponse) message);
							break;
						case WITHDRAW_RESPONSE :
							PassOrderSender.onWithdrawResponse((OMSWithdrawResponse) message);
							break;
						case ACCEPT :
							PassOrderSender.onAcceptance((OMSAccept) message);
							break;
						case REJECT :
							PassOrderSender.onRejection((OMSReject) message);
							break;
						case WITHDRAW_ACCEPT :
							PassOrderSender.onCancelAcceptance((OMSWithdrawAccept) message);
							break;
						case WITHDRAW_REJECT :
							PassOrderSender.onCancelRejection((OMSWithdrawReject) message);
							break;
						case AMEND_RESPONSE :
							PassOrderSender.onAmendResponse((OMSAmendResponse) message);
							break;
						case AMEND_ACCEPT :
							PassOrderSender.onAmendAcceptance((OMSAmendAccept) message);
							break;
						case AMEND_REJECT :
							PassOrderSender.onAmendRejection((OMSAmendReject) message);
							break;
						case FILL :
							PassOrderSender.onFill((OMSFill) message);
							break;
						case PASS :
							PassOrderReceiver.onPassOrder((OMSPass) message);
							break;
						case CANCEL :
							PassOrderSender.onCancelation((OMSCancel) message);
							break;
						case WITHDRAW :
							PassOrderReceiver.onWithdrawOrder((OMSWithdraw) message);
							break;
						case AUTO :
							PassOrderSender.onAutoOrder((OMSAuto) message);
							break;	
						case UPDATE :
							PassOrderReceiver.onUpdate((OMSUpdate) message);
							break;
						case AUTO_RESPONSE :
							PassOrderReceiver.onAutoResponse((OMSAutoResponse) message);
							break;	
						case MANUAL :
							PassOrderSender.onManualOrder((OMSManual) message);
							break;	
						case MANUAL_RESPONSE :
							PassOrderReceiver.onManualResponse((OMSManualResponse) message);
							break;
						case ACCEPT_RESPONSE :
							PassOrderReceiver.onPassAcceptedResponse((OMSAcceptResponse) message);
							break;
						case REJECT_RESPONSE :
							PassOrderReceiver.onPassRejectedResponse((OMSRejectResponse) message);
							break;
						case WITHDRAW_ACCEPT_RESPONSE :
							PassOrderReceiver.onPassWithdrawAcceptedResponse((OMSWithdrawAcceptResponse) message);
							break;
						case WITHDRAW_REJECT_RESPONSE :
							PassOrderReceiver.onPassWithdrawRejectResponse((OMSWithdrawRejectResponse) message);
							break;
						case AMEND :
							PassOrderReceiver.onAmendOrder((OMSAmend) message);
							break;
						case AMEND_ACCEPT_RESPONSE :
							PassOrderReceiver.onPassAmendAcceptedResponse((OMSAmendAcceptResponse) message);
							break;
						case AMEND_REJECT_RESPONSE :
							PassOrderReceiver.onPassAmendRejectResponse((OMSAmendRejectResponse) message);
							break;
						case FILL_RESPONSE :
							PassOrderReceiver.onPassFillResponse((OMSFillResponse) message);
							break;
						case CONFIRM :
							PassOrderReceiver.onPassConfirmation((OMSOrderConfirmation) message);
							break;
						default :
							log.info(OMSConstants.OMS_LOG_PREFIX + " Received Msg " + message.toString());
							break;
						}
					}
	            } catch (Throwable e) {
	                log.warn("run():Exception while processing OMSMessage ", e);
	                continue;
	            }
	        }
	    }
	}

}
