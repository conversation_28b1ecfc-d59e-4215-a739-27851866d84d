package com.integral.staging.oms.passorder;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.sql.Time;
import java.sql.Timestamp;

import com.integral.is.oms.OrderConstants;
import com.integral.model.dealing.*;
import com.integral.model.dealing.descriptor.algo.AlgoDescriptor;
import com.integral.model.dealing.descriptor.algo.AlgoParametersExternalAlgo;
import com.integral.time.DateTimeFactory;
import com.integral.finance.dealing.ContingencyParameter;
import com.integral.is.ISCommonConstants;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.finance.quote.calculator.CreditServiceFacadeFactory;
import com.integral.is.log.MessageLogger;
import com.integral.is.oms.OMSUtilC;
import com.integral.is.oms.Order;
import com.integral.is.oms.directedOrders.OrderMatchConfiguration;
import com.integral.is.oms.directedOrders.OrderMatchConfigurationFactory;
import com.integral.is.oms.log.OMSLogger;
import com.integral.is.spaces.fx.esp.provision.ProvisionCache;
import com.integral.is.spaces.fx.esp.validation.ErrorCode;
import com.integral.is.spaces.fx.esp.workflowhandler.MatchRequestPersistenceService;
import com.integral.is.spaces.fx.esp.workflowhandler.MatchRequestWorkflowHelper;
import com.integral.is.spaces.fx.esp.workflowhandler.MatchResponseWorkflowHandler;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.message.ErrorMessageC;
import com.integral.message.MessageStatus;
import com.integral.model.dealing.MatchEvent.MatchEventLeg.BuySellMode;
import com.integral.model.dealing.OrderRequest.RequestLeg;
import com.integral.model.dealing.OrderRequest.Type;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.staging.Order.TIF;
import com.integral.staging.oms.OMSConstants;
import com.integral.staging.oms.OMSUtil;
import com.integral.staging.oms.message.*;
import com.integral.staging.oms.model.OMSContigencyParameter;
import com.integral.staging.oms.model.OMSOrderParam;
import com.integral.user.Organization;

public class EMSOMSRouter
{
	private static Log log = LogFactory.getLog(EMSOMSRouter.class);
	private final static MatchRequestWorkflowHelper workflowHelper = MatchRequestWorkflowHelper.getInstance();
	private final static MatchRequestPersistenceService persistenceService = MatchRequestPersistenceService.getInstance();
	private final static Map<String, ManageOMSLinkedOrders> omsLinkedOrders = new HashMap<String, ManageOMSLinkedOrders>();

	/**
	 * Pass OMS order another OMS org
	 * @param order
	 * @return
	 */
	public static ErrorMessage routeToOMS( com.integral.is.oms.Order order)
	{
		return enableMatch(order, true, null);
	}

	public static ErrorMessage enableMatch(com.integral.is.oms.Order order, boolean sendToOMS, String omsOrgStr) {
		OrderMatchConfiguration omConfig = null;
		SingleLegOrder orderRequest = (SingleLegOrder) order.getEntityDescriptor().getEntity();
		try {

			long acceptanceRecvdByIS = System.currentTimeMillis();
			if(omsOrgStr == null){
				omsOrgStr = orderRequest.getExecutionInstructions().getRoutingInstruction().getTradingVenue();
			}
			log.info(OMSConstants.OMS_LOG_PREFIX + "routeToOMS : Sending order for Customer: " + orderRequest.getNamespaceName() +", OMSOrg: "+omsOrgStr +", orderId: " + orderRequest.get_id());
			
			Organization omsOrg = null;
			if(omsOrgStr == null) {
				return OMSUtil.createError(ISCommonConstants.REQUEST_VALIDATION_TRADINGVENUE_INVALID);
			} else {
				omsOrg = ReferenceDataCacheC.getInstance().getOrganization(omsOrgStr);
				if ( omsOrg == null ) {
					return OMSUtil.createError(ISCommonConstants.REQUEST_VALIDATION_TRADINGVENUE_INVALID);
				}
			}

			synchronized ( orderRequest ) {
				OMSUtilC.enableOrderForLPCrossing(order, false);
				long s = System.currentTimeMillis();
				omConfig = OrderMatchConfigurationFactory.borrowOrderMatchConfiguration(orderRequest);
				omConfig.setOrderMatchProvision(ProvisionCache.getRelationshipProvision(orderRequest.getLegalEntity(), omsOrg, orderRequest.getCurrencyPair()));
				omConfig.populateAndValidate();
				long validationTime = System.currentTimeMillis() - s;
				if ( !omConfig.getErrorCode().isEmpty() ) {
					logErrorCodes(omConfig.getErrorCode());
					return omConfig.getErrorCode().get(0).getErrorMessage(orderRequest);
				}
				OrderMatchRequest omsMatchRequest = workflowHelper.createUpdateMatchRequest(omConfig);
				omConfig.getProvider().getAdaptor().getWorkflowFunctor().validateOrderMatchRequest(omsMatchRequest);
				omsMatchRequest.setDirectedOrderVenueMatch(true);
				omsMatchRequest.getMatchEventTimes().setAcceptanceReceivedByIS(acceptanceRecvdByIS);
				omsMatchRequest.getMatchEventTimes().validationTime = validationTime;
				StringBuilder sb = new StringBuilder(100).append(OMSConstants.OMS_LOG_PREFIX +"routeToOMS :");
				sb.append(" COR.id=").append(omsMatchRequest.getOrderRequest().get_id());
				sb.append(", COR_tid=").append(omsMatchRequest.getOrderRequest().getTransactionId());
				sb.append(", CMR_id=").append(omsMatchRequest.get_id());
				sb.append(", Venue=").append(omsOrgStr);
				sb.append(", isDirect=").append(true);
				log.info(sb.toString());

				omsMatchRequest.setWarmUpObject(orderRequest.isWarmUpObject());

				MatchEvent.MatchEventLeg customerMatchEventLeg = omsMatchRequest.getMatchEventLeg();
				boolean success = order.fill(customerMatchEventLeg.getMatchedAmount(), customerMatchEventLeg.getMatchRate(), null, omsMatchRequest.get_id(), omsMatchRequest.getMatchingVenue().getShortName());
				if ( !success ) {
					return OMSUtil.createError(ISCommonConstants.REQUEST_VALIDATION_DIRECTED_ORDER_FILL_FAILED);
				}
				if ( omsMatchRequest.getTradingParty() != null ) {
					long st = System.currentTimeMillis();
					success = CreditServiceFacadeFactory.getInstance().takeCreditMultiLevel(omsMatchRequest, omConfig.getSingleLegOrder().getLegalEntity(), omsOrg.getDefaultDealingEntity(), false );
					long cT = System.currentTimeMillis();
					omsMatchRequest.getMatchEventTimes().creditTime = cT - st;
					if ( !success ) {
						return OMSUtil.createError("Credit Failure");
					}
				}
				omsMatchRequest.getMatchEventLeg().setMatchRate(orderRequest.getRequestLeg().getSpotRate());
				omsMatchRequest.getMatchEventTimes().setReqSentToVenue(System.currentTimeMillis());
				success = persistenceService.persistMatchRequest(omsMatchRequest, ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_SUBMIT);
				if ( !success ) {
					failureWorkflow(omsMatchRequest, true, false, false);
					updateOrderOnError(order, omsMatchRequest);
					String msg = "Problem while persisting match request = " + omsMatchRequest.get_id();
					return OMSUtil.createError(msg);
				}
				if(sendToOMS){
					ErrorMessage errorMessage = dispatchSubmitRequest(omsMatchRequest, omsOrg);
					if ( errorMessage != null && errorMessage.getStatus() != MessageStatus.SUCCESS ) {
						updateOrderOnError(order, omsMatchRequest, orderRequest.getRequestLeg().getSpotRate());
						return errorMessage;
					}
				}
			}
		}
		catch ( Exception e ) {
			String msg = "Exception while submitting match request for order = " + orderRequest.get_id();
			log.error(msg, e);
			MessageLogger.getInstance().log(ISAlertMBean.DO_ALERT_EVENT_REQUEST_SUBMISSION_FAILED, EMSOMSRouter.class.getName(), "Exception while sending message -Error" + e.getMessage(), orderRequest.get_id());
			return OMSUtil.createError(msg);
		}
		finally {
			if ( omConfig != null ) {
				OrderMatchConfigurationFactory.returnOrderMatchConfiguration(omConfig);
			}
		}
		return null;
	}
	
	public static void notifyOMS(Order order, String updatedStatus) {
		Object obj = order.getEntityDescriptor().getEntity();
		String omsOrgStr = ((SingleLegOrder) obj).getExecutionInstructions().getRoutingInstruction()
				.getTradingVenue();
		if (omsOrgStr != null) {
			Organization omsOrg = ReferenceDataCacheC.getInstance().getOrganization(omsOrgStr);
			if (omsOrg != null) {
				OMSUpdate omsUpdate = new OMSUpdate();
				omsUpdate.setClientOrderId(order.getOrderId());
				omsUpdate.setFromOMSOrg(((SingleLegOrder) obj).getNamespaceName());
				omsUpdate.setToOMSOrg(omsOrgStr);
				omsUpdate.setOrderId(((SingleLegOrder) obj).getOmsOrderId());
				omsUpdate.setUser(OMSConstants.OMS_SYSTEM_USER);
				omsUpdate.setEmsOrder(true);
				omsUpdate.setFilledAmount(order.getFilledAmount());
				omsUpdate.setOrderUpdatedStatus(updatedStatus);
				omsUpdate.setTradeId(((SingleLegOrder) obj).getTransactionId());
				PassOrderSender.onUpdateOMSOrder(omsUpdate);
			}
		}
	}
	

	public static ErrorMessage dispatchSubmitRequest( OrderMatchRequest omsMatchRequest, Organization toOMSOrg )
	{
		SingleLegOrder orderRequest = omsMatchRequest.getOrderRequest();
		if(orderRequest.getOrderContingencies().size() ==0){
			OMSPass newOrderRequest = new OMSPass();
			newOrderRequest.setFromOMSOrg(omsMatchRequest.getNamespaceName());
			newOrderRequest.setToOMSOrg(toOMSOrg.getShortName());
			newOrderRequest.setClientOrderId(orderRequest.get_id());
			newOrderRequest.setUser(orderRequest.getUser().getShortName());
			newOrderRequest.setEmsOrder(true);
			List<OMSOrderParam> params = new ArrayList<OMSOrderParam>();
			OMSOrderParam orderParam = new OMSOrderParam();
			orderParam.setClientOrderId(orderRequest.get_id());
			orderParam.setCcyPair(omsMatchRequest.getCurrencyPair().getName());
			orderParam.setDealtCcy(omsMatchRequest.getDealtCurrency().getShortName());
			orderParam.setDealtAmt(omsMatchRequest.getMatchEventLeg().getFinalAcceptanceAmount());
			orderParam.setSide(getBidOfferMode(orderRequest));
			orderParam.setOrderType(orderRequest.getType().name());
			double orderPrice;
			if(orderRequest.getType() == Type.STOP) {
				orderPrice = orderRequest.getOrderTrigger().getTriggerRate();
			} else {
				orderPrice = orderRequest.getOrderSpotRate();
			}
			orderParam.setOrderPrice(orderPrice);
			orderParam.setCptyA(omsMatchRequest.getLegalEntity().getShortName());
			orderParam.setCptyAOrg(omsMatchRequest.getOrgShortName());
			orderParam.setTif(getTimeInForce(omsMatchRequest).name());
			orderParam.setExpiryTime(omsMatchRequest.getExpiryTime());
			orderParam.setContigencyParameters(new ArrayList<OMSContigencyParameter>());
			orderParam.setPrimaryOrder(true);
			orderParam.setCustNotes(orderRequest.getNotes());
			try {
				FixingParameters fixingParameters = orderRequest.getFixingParameters();
				if (fixingParameters != null) {
					orderParam.setFixingReference(fixingParameters.getFixingVenue());
					Timestamp fixingTimeValue = new Timestamp(DateTimeFactory.newDate(fixingParameters.getFixingDate()).asJdkDate().getTime() +
							(Time.valueOf(fixingParameters.getFixingTime() + ":00").getTime()));
					orderParam.setFixingTime(fixingTimeValue.getTime());
					log.info("EMSOMSRouter.dispatchSubmitRequest ()  fixingVenue: " + fixingParameters.getFixingVenue()
							+ ", fixingTime: " + fixingTimeValue.getTime());
				}
			} catch (Exception e) {
				log.error("EMSOMSRouter.dispatchSubmitRequest () Error in fixing order", e);
			}
			params.add(orderParam);
	
			newOrderRequest.setOmsOrders(params);
	
			String errorMsgStr = PassOrderSender.sendMessage(newOrderRequest);
			if ( errorMsgStr != null ) {
				ErrorMessage errorMsg = new ErrorMessageC();
				errorMsg.setCode(errorMsgStr);
				return errorMsg;
			}
			return OMSUtil.createSuccess();
		}
		else{
			int objectIndex = 0;
			if(orderRequest.getOrderContingencies().size() > 1){
				objectIndex = 1;
			}
			ContingencyParameter contingencyParameter = (ContingencyParameter)((ArrayList)orderRequest.getOrderContingencies()).
					get(objectIndex);
			ManageOMSLinkedOrders manageOMSLinkedOrders = omsLinkedOrders.get(contingencyParameter.getGroupId());
			if(manageOMSLinkedOrders != null){
				
				OMSOrderParam orderParam = new OMSOrderParam();
				orderParam.setClientOrderId(orderRequest.get_id());
				orderParam.setCcyPair(omsMatchRequest.getCurrencyPair().getName());
				orderParam.setDealtCcy(omsMatchRequest.getDealtCurrency().getShortName());
				orderParam.setDealtAmt(omsMatchRequest.getMatchEventLeg().getFinalAcceptanceAmount());
				orderParam.setSide(getBidOfferMode(orderRequest));
				orderParam.setOrderType(orderRequest.getType().name());
				double orderPrice;
				if(orderRequest.getType() == Type.STOP) {
					orderPrice = orderRequest.getOrderTrigger().getTriggerRate();
				} else {
					orderPrice = orderRequest.getOrderSpotRate();
				}
				orderParam.setOrderPrice(orderPrice);
				orderParam.setCptyA(omsMatchRequest.getLegalEntity().getShortName());
				orderParam.setCptyAOrg(omsMatchRequest.getOrgShortName());
				orderParam.setTif(getTimeInForce(omsMatchRequest).name());
				orderParam.setExpiryTime(omsMatchRequest.getExpiryTime());
				setOMSContigencyParameter(orderRequest, contingencyParameter,
						orderParam);
				orderParam.setCustNotes(orderRequest.getNotes());
				manageOMSLinkedOrders.addOMSOrderParam(orderParam);
				
			}
			else{
				OMSPass newOrderRequest = new OMSPass();
				int waitingOrderCount = 1;
				if(contingencyParameter.getType() == 2 && contingencyParameter.getLinkedOMSIds().split(",").length ==2){
					waitingOrderCount = 2;
				}
				manageOMSLinkedOrders = new EMSOMSRouter.ManageOMSLinkedOrders(waitingOrderCount,newOrderRequest);
				newOrderRequest.setFromOMSOrg(omsMatchRequest.getNamespaceName());
				newOrderRequest.setToOMSOrg(toOMSOrg.getShortName());
				newOrderRequest.setClientOrderId(contingencyParameter.getGroupId());
				newOrderRequest.setUser(orderRequest.getUser().getShortName());
				newOrderRequest.setEmsOrder(true);
				List<OMSOrderParam> params = new ArrayList<OMSOrderParam>();
				OMSOrderParam orderParam = new OMSOrderParam();
				orderParam.setClientOrderId(orderRequest.get_id());
				orderParam.setCcyPair(omsMatchRequest.getCurrencyPair().getName());
				orderParam.setDealtCcy(omsMatchRequest.getDealtCurrency().getShortName());
				orderParam.setDealtAmt(omsMatchRequest.getMatchEventLeg().getFinalAcceptanceAmount());
				orderParam.setSide(getBidOfferMode(orderRequest));
				orderParam.setOrderType(orderRequest.getType().name());
				double orderPrice;
				if(orderRequest.getType() == Type.STOP) {
					orderPrice = orderRequest.getOrderTrigger().getTriggerRate();
				} else {
					orderPrice = orderRequest.getOrderSpotRate();
				}
				orderParam.setOrderPrice(orderPrice);
				orderParam.setCptyA(omsMatchRequest.getLegalEntity().getShortName());
				orderParam.setCptyAOrg(omsMatchRequest.getOrgShortName());
				orderParam.setTif(getTimeInForce(omsMatchRequest).name());
				orderParam.setExpiryTime(omsMatchRequest.getExpiryTime());
				
				setOMSContigencyParameter(orderRequest, contingencyParameter,
						orderParam);
				orderParam.setCustNotes(orderRequest.getNotes());
				params.add(orderParam);
		
				newOrderRequest.setOmsOrders(params);
				omsLinkedOrders.put(contingencyParameter.getGroupId(), manageOMSLinkedOrders);
				manageOMSLinkedOrders.start();
			}
			
			return OMSUtil.createSuccess();
		}
	}

	private static void setOMSContigencyParameter(SingleLegOrder orderRequest,
			ContingencyParameter contingencyParameter, OMSOrderParam orderParam) {
		List<OMSContigencyParameter> cps = new ArrayList<OMSContigencyParameter>();
		OMSContigencyParameter contParam = new OMSContigencyParameter();
		cps.add(contParam);
		if(contingencyParameter.getType() == 4){
			contParam.setType(1);
			orderParam.setContigencyParameters(cps);
			orderParam.setPrimaryOrder(true);
		}else if(contingencyParameter.getPrimaryOrderId() == null){
			contParam.setType(2);
			orderParam.setContigencyParameters(cps);
			orderParam.setPrimaryOrder(true);
		}else{					
			orderParam.setPrimaryOrder(false);
			if(orderRequest.getOrderContingencies().size() > 1){
				contParam.setType(1);
				orderParam.setContigencyParameters(cps);
			}else{
				orderParam.setContigencyParameters(new ArrayList<OMSContigencyParameter>());
			}
		}
	}
	
	static class ManageOMSLinkedOrders extends Thread{
		private int waitingOrderCount;
		private OMSPass newOrderRequest;
		ManageOMSLinkedOrders(int waitingOrderCount, OMSPass newOrderRequest){
			this.waitingOrderCount = waitingOrderCount;
			this.newOrderRequest = newOrderRequest;
		}
		public void addOMSOrderParam(OMSOrderParam orderParam){
			newOrderRequest.getOmsOrders().add(orderParam);
			waitingOrderCount = waitingOrderCount - 1;
			synchronized(newOrderRequest){
				newOrderRequest.notify();
			}
		}		
		public void run(){
			synchronized(newOrderRequest){
				while(waitingOrderCount != 0){
					try{
					newOrderRequest.wait();
					}catch(InterruptedException e){}
				}
				String errorMsgStr = PassOrderSender.sendMessage(newOrderRequest);
				if ( errorMsgStr != null ) {
					ErrorMessage errorMsg = new ErrorMessageC();
					errorMsg.setCode(errorMsgStr);
					log.error("Error while sending linked orders to OMS: "+errorMsg);
				}
				omsLinkedOrders.remove(newOrderRequest.getClientOrderId());
			}
			
		}
	}

	
	
	public static ErrorMessage withdrawFromOMS( com.integral.is.oms.Order order, boolean expired, boolean wasAuto)
	{
		SingleLegOrder orderRequest = (SingleLegOrder) order.getEntityDescriptor().getEntity();
		log.info(OMSConstants.OMS_LOG_PREFIX + " Withdrawing passed order  org " + orderRequest.getNamespaceName() + " orderId " + orderRequest.get_id());

		try {
			synchronized ( orderRequest ) {
				Collection<OrderMatchRequest> matchRequests = orderRequest.getActiveMatchRequests();
				if ( matchRequests.size() > 0 ) {
					for ( OrderMatchRequest omsMatchRequest : matchRequests ) {
						if ( !omsMatchRequest.isCancellationDone() && !omsMatchRequest.isExpiratationDone() ) {
							omsMatchRequest.setCancellationDone(true);
							Organization omsOrg = omsMatchRequest.getMatchingVenue();
							if ( omsOrg == null ) {
								StringBuilder sb = new StringBuilder(100).append(OMSConstants.OMS_LOG_PREFIX + ".cancelMatchRequest : Trading Venue not found since their relationship has been removed.");
								sb.append(" CustOrg->").append(orderRequest.getOrgShortName());
								sb.append(". Invoking cancellation workflow on server for OrderId->" + orderRequest.get_id());
								log.info(sb.toString());
								return OMSUtil.createError(OMSConstants.OMS_ORG_NOT_CONFIGURED);
							}
							ErrorMessage errorMessage = dispatchWithdawRequest(omsMatchRequest, omsOrg, expired, wasAuto);							
							if ( errorMessage.getStatus() != MessageStatus.SUCCESS ) {
								omsMatchRequest.setCancellationDone(false);
								log.warn(OMSConstants.OMS_LOG_PREFIX + "Cancel Request to venue failed for RequestId = " + omsMatchRequest.get_id());
								return OMSUtil.createError(ISCommonConstants.ORDER_CANCEL_PENDING_CANCEL);
							}
							else {
								OMSLogger.getInstance().logDirectedOrderCancelSuccessMessage(omsMatchRequest);
							}

							log.info(OMSConstants.OMS_LOG_PREFIX + "Submitted Cancel request for RequestId = " + omsMatchRequest.get_id());
						}

					}
					return OMSUtil.createSuccess();
				}
			}
		}
		catch ( Exception e ) {
			String msg = "Exception while cancelling match request for order = " + orderRequest.get_id();
			log.error(OMSConstants.OMS_LOG_PREFIX + msg, e);
			return OMSUtil.createError(msg);
		}
		return OMSUtil.createSuccess();

	}

	
	
	public static ErrorMessage dispatchWithdawRequest( OrderMatchRequest omsMatchRequest, Organization toOMSOrg, boolean expired, boolean wasAuto )
	{
		SingleLegOrder orderRequest = omsMatchRequest.getOrderRequest();
		final OMSWithdraw omsWithdraw = new OMSWithdraw();
		omsWithdraw.setFromOMSOrg(omsMatchRequest.getNamespaceName());
		omsWithdraw.setToOMSOrg(toOMSOrg.getShortName());
		omsWithdraw.setClientOrderId(orderRequest.get_id());
		omsWithdraw.setUser(orderRequest.getUser().getShortName());
		omsWithdraw.setEmsOrder(true);
		omsWithdraw.setExpired(expired);
		omsWithdraw.setAuto(wasAuto);
		String errorMsgStr = PassOrderSender.sendMessage(omsWithdraw);
		if ( errorMsgStr != null ) {
			if (errorMsgStr.contains("Connection to Org") && errorMsgStr.contains("is inactive")) {
				// This code executes when OMS server is not up
				PassOrderSender.addToPendingMessageQueue(omsWithdraw);
				return OMSUtil.createSuccess();
			}
			ErrorMessage errorMsg = new ErrorMessageC();
			errorMsg.setCode(errorMsgStr);
			return errorMsg;
		}
		return OMSUtil.createSuccess();
	}

	public static ErrorMessage amendOMSOrder(SingleLegOrder orderRequest, Double dealtAmt, Double orderPrice, boolean wasAuto) {
		log.info(OMSConstants.OMS_LOG_PREFIX + " Amending passed order  org " + orderRequest.getNamespaceName() + " orderId " + orderRequest.get_id());

		try {
			synchronized (orderRequest) {
				Collection<OrderMatchRequest> matchRequests = orderRequest.getActiveMatchRequests();
				if (matchRequests.size() > 0) {
					for (OrderMatchRequest omsMatchRequest : matchRequests) {
						Organization omsOrg = omsMatchRequest.getMatchingVenue();
						if (omsOrg == null) {
							StringBuilder sb = new StringBuilder(100).append(OMSConstants.OMS_LOG_PREFIX + ".amendOMSOrder : Trading Venue not found since their relationship has been removed.");
							sb.append(" CustOrg->").append(orderRequest.getOrgShortName());
							sb.append(". Invoking Amend workflow on server for OrderId->" + orderRequest.get_id());
							log.info(sb.toString());
							return OMSUtil.createError(OMSConstants.OMS_ORG_NOT_CONFIGURED);
						}
						ErrorMessage errorMessage = dispatchAmendRequest(omsMatchRequest, omsOrg, dealtAmt, orderPrice, wasAuto);
						if (errorMessage.getStatus() != MessageStatus.SUCCESS) {
							omsMatchRequest.setCancellationDone(false);
							log.warn(OMSConstants.OMS_LOG_PREFIX + "Amend Request to venue failed for RequestId = " + omsMatchRequest.get_id());
							return OMSUtil.createError(ISCommonConstants.ORDER_CANCEL_PENDING_CANCEL);
						} else {
							OMSLogger.getInstance().logDirectedOrderAmendSuccessMessage(omsMatchRequest);
						}
						log.info(OMSConstants.OMS_LOG_PREFIX + "Submitted Amend request for RequestId = " + omsMatchRequest.get_id()+ ", orderId " + orderRequest.get_id());
					}
					return OMSUtil.createSuccess();
				}else{
					ErrorMessage errorMessage = dispatchAmendRequestForAutoOrder(orderRequest, dealtAmt, orderPrice, wasAuto);
					if (errorMessage.getStatus() != MessageStatus.SUCCESS) {
						log.warn(OMSConstants.OMS_LOG_PREFIX + "Amend Request to venue failed for orderId " + orderRequest.get_id());
						return OMSUtil.createError(ISCommonConstants.ORDER_CANCEL_PENDING_CANCEL);
					}
					log.info(OMSConstants.OMS_LOG_PREFIX + "Submitted Amend request of auto order for orderId " + orderRequest.get_id());
				}
			}
		} catch (Exception e) {
			String msg = "Exception while Amending match request for order = " + orderRequest.get_id();
			log.error(OMSConstants.OMS_LOG_PREFIX + msg, e);
			return OMSUtil.createError(msg);
		}
		return OMSUtil.createSuccess();
	}

	public static ErrorMessage dispatchAmendRequest(OrderMatchRequest omsMatchRequest, Organization toOMSOrg, Double dealtAmt, Double orderPrice, boolean wasAuto) {
		SingleLegOrder orderRequest = omsMatchRequest.getOrderRequest();
		final OMSAmend omsAmend = new OMSAmend();
		omsAmend.setFromOMSOrg(omsMatchRequest.getNamespaceName());
		omsAmend.setToOMSOrg(toOMSOrg.getShortName());
		omsAmend.setClientOrderId(orderRequest.get_id());
		omsAmend.setUser(orderRequest.getUser().getShortName());
		omsAmend.setEmsOrder(true);
		omsAmend.setAuto(wasAuto);
		if (dealtAmt != null) {
			omsAmend.setDealtAmt(dealtAmt);
		}
		if (orderPrice != null) {
			omsAmend.setOrderPrice(orderPrice);
		}
		String errorMsgStr = PassOrderSender.sendMessage(omsAmend);
		if (errorMsgStr != null) {
			if (errorMsgStr.contains("Connection to Org") && errorMsgStr.contains("is inactive")) {
				// This code executes when OMS server is not up
				PassOrderSender.addToPendingMessageQueue(omsAmend);
				return OMSUtil.createSuccess();
			}
			ErrorMessage errorMsg = new ErrorMessageC();
			errorMsg.setCode(errorMsgStr);
			return errorMsg;
		}
		return OMSUtil.createSuccess();
	}

	public static ErrorMessage dispatchAmendRequestForAutoOrder(SingleLegOrder orderRequest, Double dealtAmt, Double orderPrice, boolean wasAuto) {
		String omsOrgStr = orderRequest.getExecutionInstructions().getRoutingInstruction()
				.getTradingVenue();
		final OMSAmend omsAmend = new OMSAmend();
		omsAmend.setFromOMSOrg(orderRequest.getNamespaceName());
		omsAmend.setToOMSOrg(omsOrgStr);
		omsAmend.setClientOrderId(orderRequest.get_id());
		omsAmend.setUser(orderRequest.getUser().getShortName());
		omsAmend.setEmsOrder(true);
		omsAmend.setAuto(wasAuto);
		if (dealtAmt != null) {
			omsAmend.setDealtAmt(dealtAmt);
		}
		if (orderPrice != null) {
			omsAmend.setOrderPrice(orderPrice);
		}
		String errorMsgStr = PassOrderSender.sendMessage(omsAmend);
		if (errorMsgStr != null) {
			if (errorMsgStr.contains("Connection to Org") && errorMsgStr.contains("is inactive")) {
				// This code executes when OMS server is not up
				PassOrderSender.addToPendingMessageQueue(omsAmend);
				return OMSUtil.createSuccess();
			}
			ErrorMessage errorMsg = new ErrorMessageC();
			errorMsg.setCode(errorMsgStr);
			return errorMsg;
		}
		return OMSUtil.createSuccess();
	}
	
	

	public static ErrorMessage confirmOrder( com.integral.is.oms.Order order, SingleLegTrade trade )
	{
		SingleLegOrder orderRequest = (SingleLegOrder) order.getEntityDescriptor().getEntity();
		log.info(OMSConstants.OMS_LOG_PREFIX + " Confirming auto order order  org " + orderRequest.getNamespaceName() + " orderId " + orderRequest.get_id());
		String omsOrgStr = orderRequest.getExecutionInstructions().getRoutingInstruction().getTradingVenue();
		Organization omsOrg = ReferenceDataCacheC.getInstance().getOrganization(omsOrgStr);
		if ( omsOrg == null ) {
			return OMSUtil.createError(ISCommonConstants.REQUEST_VALIDATION_TRADINGVENUE_INVALID);
		}
		OMSOrderConfirmation omsOrderConfirmation = new OMSOrderConfirmation();
		omsOrderConfirmation.setFromOMSOrg(orderRequest.getNamespaceName());
		omsOrderConfirmation.setTradeId(trade.get_id());
		omsOrderConfirmation.setToOMSOrg(omsOrg.getShortName());
		omsOrderConfirmation.setClientOrderId(orderRequest.get_id());
		omsOrderConfirmation.setUser(orderRequest.getUser().getShortName());
		omsOrderConfirmation.setEmsOrder(true);
		String errorMsgStr = PassOrderSender.sendMessage(omsOrderConfirmation);
		if ( errorMsgStr != null ) {
			ErrorMessage errorMsg = new ErrorMessageC();
			errorMsg.setCode(errorMsgStr);
			return errorMsg;
		}
		return OMSUtil.createSuccess();
	}

	protected static void logErrorCodes( Collection<ErrorCode> errorCodes )
	{
		StringBuilder sb = new StringBuilder(errorCodes.size() * 100);
		for ( ErrorCode ec : errorCodes ) {
			sb.append(' ').append(ec.name());
		}
		sb.append(" ]");
		log.info(sb.toString());
	}

	protected static void failureWorkflow( OrderMatchRequest omsMatchRequest, boolean callInsert, boolean shouldCheckCredit, boolean shouldPersist )
	{
		MatchEvent.MatchEventLeg matchEventLeg = omsMatchRequest.getMatchEventLeg();
		matchEventLeg.setCancelledAmount(matchEventLeg.getFinalAcceptanceAmount());
		matchEventLeg.setPendingAmount(0);
		MatchRequestWorkflowHelper.getInstance().updateResponsePending(omsMatchRequest, false);
		omsMatchRequest.getState().setName(State.Name.RSCANCELLED);
		MatchResponseWorkflowHandler.getInstance().cancelMatchRequest(omsMatchRequest, callInsert, shouldCheckCredit, shouldPersist, ISCommonConstants.ROUTED_ORDER_REJECT_CLASSIFICATION_INTERNAL_REJECT, ISCommonConstants.DEFAULT_EVENT_TIMES_DOWH);
	}

	private static void updateOrderOnError( com.integral.is.oms.Order order, OrderMatchRequest matchRequest )
	{
		updateOrderOnError(order, matchRequest, matchRequest.getMatchEventLeg().getMatchRate());
	}

	private static void updateOrderOnError( com.integral.is.oms.Order order, OrderMatchRequest matchRequest, double rate )
	{
		MatchEvent.MatchEventLeg matchEventLeg = matchRequest.getMatchEventLeg();
		order.fill(-matchEventLeg.getCancelledAmount(), rate, null, matchRequest.get_id(), matchRequest.getMatchingVenue().getShortName());
	}

	private static TIF getTimeInForce( OrderMatchRequest matchRequest )
	{
		switch ( matchRequest.getTimeInForce() ) {
		case GTC :
			return TIF.GTC;
		case GTD :
			return TIF.GTD;
		default :
			return TIF.GTC;
		}
	}

	private static String getBidOfferMode( SingleLegOrder orderRequest )
	{
		boolean isDealtInTerm = orderRequest.getDealtCurrency().equals( orderRequest.getTermCurrency() );
		RequestLeg requestLeg = orderRequest.getRequestLeg();
		switch ( requestLeg.getBuySellMode() ) {
		case BUY :
			return isDealtInTerm ? BuySellMode.SELL.name() : BuySellMode.BUY.name();
		case SELL :
			return isDealtInTerm ? BuySellMode.BUY.name() : BuySellMode.SELL.name();
		}
		return null;
	}
}
