package com.integral.staging.oms.passorder;

import java.util.Collection;

import com.integral.SEF.UTIService;
import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.adaptor.order.handler.UnsolicitedOrderCancelHandlerCS;
import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.CreditEntity;
import com.integral.finance.trade.CreditWorkflowMode;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.GlobalMetrics;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.finance.quote.calculator.CreditServiceFacadeFactory;
import com.integral.is.oms.*;
import com.integral.is.oms.calculator.OrderCalculatorFactory;
import com.integral.is.spaces.fx.esp.cache.FXESPWorkflowCache;
import com.integral.is.spaces.fx.esp.factory.DealingModelFactory;
import com.integral.is.spaces.fx.esp.priceprovision.FXESPPriceProvisionUtil;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.is.spaces.fx.esp.workflowhandler.MatchRequestWorkflowHelper;
import com.integral.is.spaces.fx.esp.workflowhandler.MatchResponseWorkflowHandler;
import com.integral.is.spaces.fx.handler.DirectedOrderVerificationHandler;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.is.spaces.fx.service.ServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.*;
import com.integral.model.dealing.DealingModelRef;
import com.integral.model.dealing.MatchEvent;
import com.integral.model.dealing.OrderMatchRequest;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.model.dealing.State;
import com.integral.model.dealing.TradeLeg;
import com.integral.model.dealing.descriptor.CoveredTradeDescriptor;
import com.integral.oms.spaces.fx.esp.FXOrder;
import com.integral.oms.spaces.fx.esp.cache.FXOrderCache;
import com.integral.oms.spaces.fx.esp.calculator.FXAmendOrderWorkflowCalculator;
import com.integral.staging.oms.OMSConstants;
import com.integral.staging.oms.OMSUtil;
import com.integral.staging.oms.message.*;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.is.finance.businessCenter.EndOfDayService;

public class EMSOMSOrderResponseHandler
{

	private static Log log = LogFactory.getLog(EMSOMSOrderResponseHandler.class);

	public static ErrorMessage handleSubmitResponse( OMSPassResponse response )
	{
		try {

			OrderMatchRequest matchRequest = getMatchRequest(response.getClientOrderId(), response.getFromOMSOrg());
			if ( matchRequest == null ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleSubmitResponse : Couldn't find match request for reject response = " + response.getOrderId());
				return OMSUtil.createError(OMSConstants.ORDER_REQUEST_NOT_FOUND);
			}
			if ( response.isFailed() ) {
				handleOrderSubmitReject(response, matchRequest);
			}
			else {
				log.info(OMSConstants.OMS_LOG_PREFIX + " Order Submitted Succesfully to OMS. MatchRequestID : " + matchRequest.get_id());
				FXOrder order = FXOrderCache.get(matchRequest.getOrderRequestId());
				SingleLegOrder orderRequest = (SingleLegOrder) order.getEntityDescriptor().getEntity();
				orderRequest.setOmsOrderId(response.getOrderId());
				//handleOrderSubmitted(response, matchRequest);
			}
			return OMSUtil.createSuccess();
		}
		catch ( Exception e ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "handleSubmitResponse : Error in handling response " + response , e);
			return OMSUtil.createError(OMSConstants.INTERNAL_SERVER_ERROR);
		}
	}

	public static ErrorMessage handleAmendResponse(OMSAmendResponse response) {
		try {
			OrderMatchRequest matchRequest = getMatchRequest(response.getClientOrderId(), response.getFromOMSOrg());
			if (matchRequest == null) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleAmendResponse : Couldn't find match request for amend response = " + response.getClientOrderId());
				return OMSUtil.createError(OMSConstants.ORDER_REQUEST_NOT_FOUND);
			}
			if (response.isFailed()) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleAmendResponse : OMS order amend failed= " + response.getClientOrderId());
			} else {
				if (response.isAmendBeforeAccept() && !response.isAuto()) {
					handleOrderAmend(response.getOrderId(), matchRequest,  response.getDealtAmt(), response.getOrderPrice(), true, response.getFromOMSOrg());
				} else {
					SingleLegOrder singleLegOrder = matchRequest.getOrderRequest();
					WorkflowMessage msg = ISUtilImpl.getInstance().createWorkflowMessage(singleLegOrder,
							MessageEvent.AMEND_RECEIVED, ISConstantsC.MSG_TOPIC_MATCH);
					OrderCalculatorFactory.getInstance().getOrderWorkflowCalculator().notifyUser(singleLegOrder.getUser(), msg,
							singleLegOrder.getHandler());
					long st = System.currentTimeMillis();
					synchronized (singleLegOrder) {
						PersistenceServiceFactory.getISSpacesPersistenceService().persist(singleLegOrder,
								ApplicationEventCodes.EVENT_ESP_ORDER_AMEND_RECEIVED, "AmendReceived");
					}
					long endTime = System.currentTimeMillis();
					StringBuilder sb = new StringBuilder(100);
					sb.append("oId=").append(singleLegOrder.get_id())
							.append(",uROPrRTV=").append(endTime - st);
					log.info(sb.toString());
				}
			}
			return OMSUtil.createSuccess();
		} catch (Exception e) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "handleAmendResponse : Error in handling response " + response, e);
			return OMSUtil.createError(OMSConstants.INTERNAL_SERVER_ERROR);
		}
	}

	public static ErrorMessage handleWithdrawResponse( OMSWithdrawResponse response )
	{
		try {
			OrderMatchRequest matchRequest = getMatchRequest(response.getClientOrderId(), response.getFromOMSOrg());
			if ( matchRequest == null ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleWithdrawResponse : Couldn't find match request for reject response = " + response.getClientOrderId());
				return OMSUtil.createError(OMSConstants.ORDER_REQUEST_NOT_FOUND);
			}
			if ( response.isFailed() ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleWithdrawResponse : OMS order withdraw failed= " + response.getClientOrderId());
			} else {
				if(response.isCancelBeforeAccept()){
					if(response.isExpired()){
						matchRequest.setExpiratationDone(true);
						SingleLegOrder singleLegOrder = matchRequest.getOrderRequest();
						singleLegOrder.setCancelReceived(false);
						FXOrder order = FXOrderCache.get(singleLegOrder.get_id());  
						if(order != null){
							order.setMarkedForCancel(false);
						}
					}
					handleOrderCancelled(matchRequest);
				}else{
					matchRequest.setCancellationDone(false);

					SingleLegOrder singleLegOrder = matchRequest.getOrderRequest();	
				    WorkflowMessage msg = ISUtilImpl.getInstance().createWorkflowMessage(singleLegOrder, 
				    		MessageEvent.CANCEL_RECEIVED, ISConstantsC.MSG_TOPIC_MATCH);
					OrderCalculatorFactory.getInstance().getOrderWorkflowCalculator().notifyUser(singleLegOrder.getUser(), msg, 
							singleLegOrder.getHandler());
					long st = System.currentTimeMillis();
			        synchronized (singleLegOrder) {
			        	PersistenceServiceFactory.getISSpacesPersistenceService().persist(singleLegOrder, 
			        			ApplicationEventCodes.EVENT_ESP_ORDER_CANCEL_RECEIVED, "CancelReceived");
			        }
			        long endTime = System.currentTimeMillis();
			        StringBuilder sb = new StringBuilder( 100 );
			        sb.append( "oId=" ).append( singleLegOrder.get_id() )
			                .append(",uROPrRTV=").append( endTime-st );
			        log.info( sb.toString() );
				}
			}
			return OMSUtil.createSuccess();
		}
		catch ( Exception e ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "handleWithdrawResponse : Error in handling response " + response , e);
			return OMSUtil.createError(OMSConstants.INTERNAL_SERVER_ERROR);
		}
	}

	public static ErrorMessage handleCancelAccept( OMSWithdrawAccept omsCancelAccept ){
		try {
			OrderMatchRequest matchRequest = getMatchRequest(omsCancelAccept.getClientOrderId(), omsCancelAccept.getFromOMSOrg());
			if ( matchRequest == null ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleCancelAccept : Couldn't find match request for cancel accept = " + omsCancelAccept.getClientOrderId());
				return OMSUtil.createError(OMSConstants.ORDER_REQUEST_NOT_FOUND);
			}
			if ( omsCancelAccept.isFailed() ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleCancelAccept : OMS order withdraw failed= " + omsCancelAccept.getClientOrderId());
			} else {
				handleOrderCancelled(matchRequest);
			}
			return OMSUtil.createSuccess();
		}
		catch ( Exception e ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "handleCancelAccept : Error in handling response " + omsCancelAccept , e);
			return OMSUtil.createError(OMSConstants.INTERNAL_SERVER_ERROR);
		}
	}
	

	
	public static ErrorMessage handleAccept( OMSAccept omsAccept )
	{
		try {
			OrderMatchRequest matchRequest = getMatchRequest(omsAccept.getClientOrderId(), omsAccept.getFromOMSOrg());
			if ( matchRequest == null ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleAccept : Couldn't find match request for reject response = " + omsAccept.getClientOrderId());
				return OMSUtil.createError(OMSConstants.ORDER_REQUEST_NOT_FOUND);
			}
			handleOrderAccepted(omsAccept.getOrderId(), matchRequest);
			return OMSUtil.createSuccess();
		}
		catch ( Exception e ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "handleAccept : Error in handling response " + omsAccept , e);
			return OMSUtil.createError(OMSConstants.INTERNAL_SERVER_ERROR);
		}
	}
	
	public static ErrorMessage handleAuto( OMSAuto omsAuto )
	{
		try {
			OrderMatchRequest matchRequest = getMatchRequest(omsAuto.getClientOrderId(), omsAuto.getFromOMSOrg());
			if ( matchRequest == null ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleAuto : Couldn't find match request as order has beed already moved to auto = " + omsAuto.getClientOrderId());
				return OMSUtil.createError(OMSConstants.OMS_ORDER_AUTO_ALREADY);
			}
			synchronized (matchRequest) {
				handleOrderReactivation(omsAuto, matchRequest);
			}
			return OMSUtil.createSuccess();
		}
		catch ( Exception e ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "handleAuto : Error in handling response " + omsAuto , e);
			return OMSUtil.createError(OMSConstants.INTERNAL_SERVER_ERROR);
		}
	}
	
	public static ErrorMessage handleManual( OMSManual omsManual )
	{
		try {
			OrderMatchRequest matchRequest = getMatchRequest(omsManual.getClientOrderId(), null);
			if ( matchRequest != null ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleManual : Order is filled, cannot be taken to manual" + omsManual);
				return OMSUtil.createError("Order is filled, cannot be taken to manual");
			}
			FXOrder order = FXOrderCache.get(omsManual.getClientOrderId());
			if(order == null){
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleManual : Order is filled, cannot be taken to manual" + omsManual);
				return OMSUtil.createError("Order is filled, cannot be taken to manual");
			}
			EMSOMSRouter.enableMatch(order, false, omsManual.getFromOMSOrg());
			return OMSUtil.createSuccess();
		}
		catch ( Exception e ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "handleManual : Error in handling response " + omsManual , e);
			return OMSUtil.createError(OMSConstants.INTERNAL_SERVER_ERROR);
		}
	}

	public static ErrorMessage handleReject( OMSReject omsReject )
	{
		try {
			OrderMatchRequest matchRequest = getMatchRequest(omsReject.getClientOrderId(), omsReject.getFromOMSOrg());
			if ( matchRequest == null ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleReject : Couldn't find match request for Cancel response = " + omsReject.getClientOrderId());
				return OMSUtil.createError(OMSConstants.ORDER_REQUEST_NOT_FOUND);
			}
			handleUnsolicitedOrderCancelled(matchRequest, OrderCancelledBy.DESK);
			return OMSUtil.createSuccess();
		}
		catch ( Exception e ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "handleReject : Error in handling response " + omsReject , e);
			return OMSUtil.createError(OMSConstants.INTERNAL_SERVER_ERROR);
		}
	}

	public static ErrorMessage handleCancelReject( OMSWithdrawReject omsWithdrawReject )
	{
		try {
			OrderMatchRequest matchRequest = getMatchRequest(omsWithdrawReject.getClientOrderId(), omsWithdrawReject.getFromOMSOrg());
			if ( matchRequest == null ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleReject : Couldn't find match request for Cancel response = " + omsWithdrawReject.getClientOrderId());
				return OMSUtil.createError(OMSConstants.ORDER_REQUEST_NOT_FOUND);
			}
			handleCancelOrderReject(matchRequest);
			return OMSUtil.createSuccess();
		}
		catch ( Exception e ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "handleReject : Error in handling response " + omsWithdrawReject , e);
			return OMSUtil.createError(OMSConstants.INTERNAL_SERVER_ERROR);
		}
	}

	public static ErrorMessage handleAmendReject( OMSAmendReject omsAmendReject )
	{
		try {
			OrderMatchRequest matchRequest = getMatchRequest(omsAmendReject.getClientOrderId(), omsAmendReject.getFromOMSOrg());
			if ( matchRequest == null ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleAmendReject : Couldn't find match request for Amend Reject response = " + omsAmendReject.getClientOrderId());
				return OMSUtil.createError(OMSConstants.ORDER_REQUEST_NOT_FOUND);
			}
			handleAmendOrderReject(matchRequest);
			return OMSUtil.createSuccess();
		}
		catch ( Exception e ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "handleAmendReject : Error in handling response " + omsAmendReject , e);
			return OMSUtil.createError(OMSConstants.INTERNAL_SERVER_ERROR);
		}
	}

	public static ErrorMessage handleAmendAccept( OMSAmendAccept omsAmendAccept )
	{
		try {
			OrderMatchRequest matchRequest = getMatchRequest(omsAmendAccept.getClientOrderId(), omsAmendAccept.getFromOMSOrg());
			if ( matchRequest == null ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleAmendAccept : Couldn't find match request for Amend Accept response = " + omsAmendAccept.getClientOrderId());
				return OMSUtil.createError(OMSConstants.ORDER_REQUEST_NOT_FOUND);
			}
			handleOrderAmend(omsAmendAccept.getOrderId(), matchRequest,  omsAmendAccept.getDealtAmt(), omsAmendAccept.getOrderPrice(), false, omsAmendAccept.getFromOMSOrg());
			return OMSUtil.createSuccess();
		}
		catch ( Exception e ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "handleAmendAccept : Error in handling response " + omsAmendAccept , e);
			return OMSUtil.createError(OMSConstants.INTERNAL_SERVER_ERROR);
		}
	}
	
	public static ErrorMessage handleCancelResponse( OMSCancelResponse omsCancelResponse )
	{
		try {
			OrderMatchRequest matchRequest = getMatchRequest(omsCancelResponse.getClientOrderId(), omsCancelResponse.getFromOMSOrg());
			if ( matchRequest == null ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleCancelResponse : Couldn't find match request for Cancel response = " + omsCancelResponse.getClientOrderId());
				return OMSUtil.createError(OMSConstants.ORDER_REQUEST_NOT_FOUND);
			}
			if ( omsCancelResponse.isFailed() ) {
				handleCancelOrderReject( matchRequest);
			}
			else {
				handleOrderCancelled(matchRequest);
			}
			return OMSUtil.createSuccess();
		}
		catch ( Exception e ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "handleCancelResponse : Error in handling response " + omsCancelResponse , e);
			return OMSUtil.createError(OMSConstants.INTERNAL_SERVER_ERROR);
		}
	}

	public static ErrorMessage handleCancel( OMSCancel omsCancel )
	{
		try {
			OrderMatchRequest matchRequest = getMatchRequest(omsCancel.getClientOrderId(), omsCancel.getFromOMSOrg());
			if ( matchRequest == null ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleCancel : Couldn't find match request for Cancel response = " + omsCancel.getClientOrderId());
				return OMSUtil.createError(OMSConstants.ORDER_REQUEST_NOT_FOUND);
			}
			handleUnsolicitedOrderCancelled(matchRequest, OrderCancelledBy.OTHER);
			return OMSUtil.createSuccess();
		}
		catch ( Exception e ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "handleCancel : Error in handling response " + omsCancel , e);
			return OMSUtil.createError(OMSConstants.INTERNAL_SERVER_ERROR);
		}
	}

	public static ErrorMessage handleFill( OMSFill omsFill )
	{
		try {
			OrderMatchRequest matchRequest = getMatchRequest(omsFill.getClientOrderId(), omsFill.getFromOMSOrg());
			if ( matchRequest == null ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "handleFill : Couldn't find match request for reject response = " + omsFill.getClientOrderId());
				return OMSUtil.createError(OMSConstants.ORDER_REQUEST_NOT_FOUND);
			}
			ErrorMessage msg = handleFillResponse(omsFill, matchRequest);
			if(msg != null){
				return msg;
			}
			return OMSUtil.createSuccess();
		}
		catch ( Exception e ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "handleFill : Error in handling response " + omsFill , e);
			return OMSUtil.createError(OMSConstants.INTERNAL_SERVER_ERROR);
		}
	}

	private static void handleOrderSubmitted( OMSPassResponse response, OrderMatchRequest matchRequest )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + " .handleOrderSubmitted : " + matchRequest.get_id());
		matchRequest.getDirectedOrderInfo().setExternalReferenceId(response.getOrderId());
		matchRequest.getDirectedOrderInfo().setResponseReceiveTime(System.currentTimeMillis());
		long[] eventTimes = new long[2];
		MatchResponseWorkflowHandler.getInstance().handleOrderSubmitted(matchRequest, eventTimes);
	}

	private static void handleOrderSubmitReject( OMSPassResponse response, OrderMatchRequest matchRequest)
	{
		log.warn(OMSConstants.OMS_LOG_PREFIX + ".handleOrderSubmitReject : Submit attempt for order = " + matchRequest.get_id() + " is rejected with reason: " + response.getFailureReason());

		matchRequest.setRejectCode(response.getFailureReason());
		matchRequest.setRejectReason(response.getFailureReason());
		MatchEvent.MatchEventLeg matchEventLeg = matchRequest.getMatchEventLeg();
		matchEventLeg.setCancelledAmount(matchEventLeg.getFinalAcceptanceAmount());
		matchEventLeg.setPendingAmount(0);
		matchRequest.getDirectedOrderInfo().setResponseReceiveTime(System.currentTimeMillis());
		matchRequest.getState().setName(State.Name.RSCANCELLED);
		MatchResponseWorkflowHandler.getInstance().handleOrderSubmitReject(matchRequest, true, true, true, 1, new long[3]);
	}
	private static void handleOrderAccepted( String orderId, OrderMatchRequest matchRequest ) throws Exception
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + " .handleOrderAccepted : " + matchRequest.get_id());
		matchRequest.getDirectedOrderInfo().setExternalReferenceId(orderId);
		matchRequest.getDirectedOrderInfo().setResponseReceiveTime(System.currentTimeMillis());
		long[] eventTimes = new long[2];		
		MatchResponseWorkflowHandler.getInstance().handleOrderSubmitted(matchRequest, eventTimes);
		
		SingleLegOrder singleLegOrder = matchRequest.getOrderRequest();
	    DealingModelUtil.doStateTransition( singleLegOrder, State.Name.RSACTIVE );
	    MessageHandler handler = singleLegOrder.getHandler();
	    WorkflowMessage msg = ISUtilImpl.getInstance().createWorkflowMessage(singleLegOrder, MessageEvent.VENUE_ORDER_SUBMIT, ISConstantsC.MSG_TOPIC_MATCH);
		OrderCalculatorFactory.getInstance().getOrderWorkflowCalculator().notifyUser(singleLegOrder.getUser(), msg, handler);
		long st = System.currentTimeMillis();
        synchronized (singleLegOrder) {
        	PersistenceServiceFactory.getISSpacesPersistenceService().persist(singleLegOrder, ApplicationEventCodes.EVENT_ESP_ORDER_ACCEPT, "OrderAccepted");
        }
        long endTime = System.currentTimeMillis();
        StringBuilder sb = new StringBuilder( 100 );
        sb.append( "oId=" ).append( singleLegOrder.get_id() )
                .append(",uROPrRTV=").append( endTime-st );
        log.info( sb.toString() );
	}

	
	private static void handleOrderReactivation( OMSAuto response, OrderMatchRequest matchRequest ) throws Exception
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + " .handleOrderAuto : " + matchRequest.get_id());
		
		SingleLegOrder singleLegOrder = matchRequest.getOrderRequest();
		if(!singleLegOrder.getState().getName().equals(State.Name.RSACTIVE)){	 
			handleOrderAccepted(response.getOrderId(), matchRequest);	 
		}
		
		FXOrder order = FXOrderCache.get(matchRequest.getOrderRequestId());
		matchRequest.getOrderRequest().getRequestLeg().setMinFillAmount(order.getOrderAmount());
		MatchEvent.MatchEventLeg matchEventLeg = matchRequest.getMatchEventLeg();
		double cancelledAmount = matchEventLeg.getPendingAmount();
		matchEventLeg.setCancelledAmount(cancelledAmount);
		matchEventLeg.setMinFillAmount(order.getOrderAmount());
		if ( MatchRequestWorkflowHelper.getInstance().validateTerminalAmount(matchRequest) ) {
			matchEventLeg.setPendingAmount(0);
			matchRequest.getState().setName(State.Name.RSCANCELLED);
			MatchResponseWorkflowHandler.getInstance().handleUnsolicitedCancel(matchRequest, true, true, true, 1, new long[3]);
		}
         OMSUtilC.enableOrderForLPCrossing(order, true);
         OrderBook book = OrderBookCacheC.getInstance().getOrderBook(order);
         book.matchOrder(order, OrderConstants.MATCH_UPON_OMS_AUTO, false);
	}
	
	private static void handleCancelOrderReject(OrderMatchRequest matchRequest )
	{
		log.error(OMSConstants.OMS_LOG_PREFIX + ".handleCancelOrderReject = " + matchRequest.get_id());
		matchRequest.setRejectCode("Rejected_From_OMS");
		matchRequest.setRejectReason("Rejected_From_OMS");
		long[] eventTimes = new long[2];
		matchRequest.getDirectedOrderInfo().setCancelReceiveTime(System.currentTimeMillis());
		MatchResponseWorkflowHandler.getInstance().handleCancelOrderRejectResponse(matchRequest, eventTimes);
		
		SingleLegOrder singleLegOrder = matchRequest.getOrderRequest();
		singleLegOrder.setCancelReceived(false);
		singleLegOrder.getOrderRequestAttributes().setRouterCancellationDone(false);
		FXOrderCache.get(singleLegOrder.get_id()).setMarkedForCancel(false);
		
		
	    WorkflowMessage msg = ISUtilImpl.getInstance().createWorkflowMessage(singleLegOrder, MessageEvent.CANCEL_REJECT, ISConstantsC.MSG_TOPIC_MATCH);
		OrderCalculatorFactory.getInstance().getOrderWorkflowCalculator().notifyUser(singleLegOrder.getUser(), msg, singleLegOrder.getHandler());
		long st = System.currentTimeMillis();
        synchronized (singleLegOrder) {
        	PersistenceServiceFactory.getISSpacesPersistenceService().persist(singleLegOrder, ApplicationEventCodes.EVENT_ESP_ORDER_CANCEL_REJECT, "CancelRejected");
        }
        long endTime = System.currentTimeMillis();
        StringBuilder sb = new StringBuilder( 100 );
        sb.append( "oId=" ).append( singleLegOrder.get_id() )
                .append(",uROPrRTV=").append( endTime-st );
        log.info( sb.toString() );
	}


	private static void handleAmendOrderReject(OrderMatchRequest matchRequest )
	{
		log.error(OMSConstants.OMS_LOG_PREFIX + ".handleAmendOrderReject = " + matchRequest.get_id());
		SingleLegOrder singleLegOrder = matchRequest.getOrderRequest();
		WorkflowMessage msg = ISUtilImpl.getInstance().createWorkflowMessage(singleLegOrder, MessageEvent.AMEND_REJECT, ISConstantsC.MSG_TOPIC_MATCH);
		OrderCalculatorFactory.getInstance().getOrderWorkflowCalculator().notifyUser(singleLegOrder.getUser(), msg, singleLegOrder.getHandler());
		long st = System.currentTimeMillis();
		synchronized (singleLegOrder) {
			PersistenceServiceFactory.getISSpacesPersistenceService().persist(singleLegOrder, ApplicationEventCodes.EVENT_ESP_ORDER_AMEND_REJECT, "AmendRejected");
		}
		long endTime = System.currentTimeMillis();
		StringBuilder sb = new StringBuilder( 100 );
		sb.append( "oId=" ).append( singleLegOrder.get_id() )
				.append(",uROPrRTV=").append( endTime-st );
		log.info( sb.toString() );
	}

	private static void handleOrderCancelled( OrderMatchRequest matchRequest )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + " .handleOrderCancelled : " + matchRequest.get_id());
		MatchEvent.MatchEventLeg matchEventLeg = matchRequest.getMatchEventLeg();
		double cancelledAmount = matchEventLeg.getPendingAmount();
		matchEventLeg.setCancelledAmount(cancelledAmount);
		if ( MatchRequestWorkflowHelper.getInstance().validateTerminalAmount(matchRequest) ) {
			matchEventLeg.setPendingAmount(0);
			matchRequest.getDirectedOrderInfo().setCancelReceiveTime(System.currentTimeMillis());
			long[] eventTimes = new long[2];
			MatchResponseWorkflowHandler.getInstance().handleRequestExpiry(matchRequest, eventTimes);
		}
	}

	private static void handleOrderAmend(String orderId, OrderMatchRequest matchRequest, Double dealtAmt, Double orderPrice, boolean beforeAccept, String omsOrgStr) {
		log.info(OMSConstants.OMS_LOG_PREFIX + " .handleOrderAmend : " + matchRequest.get_id()+", orderId: "+orderId);
		SingleLegOrder singleLegOrder = matchRequest.getOrderRequest();
		FXOrder order = FXOrderCache.get(matchRequest.getOrderRequestId());

		if (beforeAccept) {
			//Cancel old match request
			matchRequest.getOrderRequest().getRequestLeg().setMinFillAmount(order.getOrderAmount());
			MatchEvent.MatchEventLeg matchEventLeg = matchRequest.getMatchEventLeg();
			double cancelledAmount = matchEventLeg.getPendingAmount();
			matchEventLeg.setCancelledAmount(cancelledAmount);
			matchEventLeg.setMinFillAmount(order.getOrderAmount());
			if (MatchRequestWorkflowHelper.getInstance().validateTerminalAmount(matchRequest)) {
				matchEventLeg.setPendingAmount(0);
				matchRequest.getState().setName(State.Name.RSCANCELLED);
				MatchResponseWorkflowHandler.getInstance().handleUnsolicitedCancel(matchRequest, true, true, true, 1, new long[3]);
			}
		} else if(dealtAmt != null){
			//Update match request
			matchRequest.getDirectedOrderInfo().setExternalReferenceId(orderId);
			matchRequest.getDirectedOrderInfo().setResponseReceiveTime(System.currentTimeMillis());
			matchRequest.incAmendCount();
			singleLegOrder.setRoutedMatchRequest(matchRequest);
			long[] eventTimes = new long[2];
			MatchResponseWorkflowHandler.getInstance().handleAmendSuccess(matchRequest, dealtAmt, orderPrice == null ? 0 : orderPrice, eventTimes);
		}

		//Amend Order
		WorkflowMessage wfm = ISUtilImpl.getInstance().createWorkflowMessage(singleLegOrder, com.integral.message.MessageEvent.AMEND, ISCommonConstants.MSG_TOPIC_REQUEST);
		wfm.setParameterValue(ISCommonConstants.WF_PARAM_NEW_ORDER_AMOUNT, dealtAmt);
		if (order.isStop() || order.isStoplimit() || order.isStopMarketRange()) {
			wfm.setParameterValue(ISCommonConstants.WF_PARAM_NEW_TRIGGER_RATE, orderPrice);
		} else {
			wfm.setParameterValue(ISCommonConstants.WF_PARAM_NEW_ORDER_RATE, orderPrice);
		}
		FXAmendOrderWorkflowCalculator.processAmend(singleLegOrder, wfm, order, false, true);

		if (beforeAccept) {
			//Create new match request
			EMSOMSRouter.enableMatch(order, false, omsOrgStr);
		}
		log.info(OMSConstants.OMS_LOG_PREFIX + " .handleOrderAmend :  orderAmount after amend: " + order.getOrderAmount() + ", number of Active match request: " + singleLegOrder.getActiveMatchRequests().size()+",orderId: "+orderId);
		synchronized (singleLegOrder) {
			PersistenceServiceFactory.getISSpacesPersistenceService().persist(singleLegOrder, ApplicationEventCodes.EVENT_ESP_ORDER_AMEND_ACCEPT, "AmendAccept");
		}
	}

	private static void handleUnsolicitedOrderCancelled( OrderMatchRequest matchRequest, OrderCancelledBy cancelledBy )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + " .handleUnsolicitedOrderCancelled : " + matchRequest.get_id());
		MatchEvent.MatchEventLeg matchEventLeg = matchRequest.getMatchEventLeg();
		double cancelledAmount = matchEventLeg.getPendingAmount();
		matchEventLeg.setCancelledAmount(cancelledAmount);
		FXOrder order = FXOrderCache.get(matchRequest.getOrderRequestId());
		OMSUtilC.enableOrderForLPCrossing(order, false);
		if ( MatchRequestWorkflowHelper.getInstance().validateTerminalAmount(matchRequest) ) {
			matchEventLeg.setPendingAmount(0);
			matchRequest.getState().setName(State.Name.RSCANCELLED);
			MatchResponseWorkflowHandler.getInstance().handleUnsolicitedCancel(matchRequest, true, true, true, 1, new long[3]);
			new UnsolicitedOrderCancelHandlerCS().cancelOrder(matchRequest.getOrderRequestId(), cancelledBy);
		}
	}

	public static ErrorMessage handleFillResponse( OMSFill omsFill, OrderMatchRequest matchRequest )
	{
		log.info(new StringBuilder().append(OMSConstants.OMS_LOG_PREFIX + " .handleFillResponse : received fill ").append(omsFill.getFillId()).append(" for order id ").append(omsFill.getOrderId()).append(", fR=").append(omsFill.getFillPrice()).toString());
		SingleLegOrder orderRequest = matchRequest.getOrderRequest();
		User user = orderRequest.getUser();
		ISUtilImpl.getInstance().setSessionContext(user);
		return processOrderFill(omsFill, matchRequest, orderRequest);
	}

	private static ErrorMessage processOrderFill( OMSFill omsFill, OrderMatchRequest matchRequest, SingleLegOrder orderRequest ) {
		log.info(new StringBuilder(OMSConstants.OMS_LOG_PREFIX + " .processOrderFill : rid = ").append(omsFill.getFillId())
				.append(", Fillprice=").append(omsFill.getFillPrice()).append(", Filled DealtAmt=").append(omsFill.getDealtAmt())
				.append(", VenueBMR=").append(omsFill.getVenueBMR()).append(", PPSpotSpread=").append(omsFill.getPpSpotSpread()).toString());
		SingleLegTrade trade = createUpdateTrade(omsFill, matchRequest);
		OrderMatchRequest matchRequestForTrade = (OrderMatchRequest) trade.getMatchEvent();
		CreditServiceFacadeFactory.getInstance().isRateOrAmountModified(trade);
		boolean omsOrderCreditEnabled = OrderConfiguration.getInstance().getOmsOrderCreditEnabled(omsFill.getFromOMSOrg(), omsFill.getToOMSOrg());
		FXESPPriceProvisionUtil.roundRates(trade, trade.getTradeLeg());
		DealingModelUtil.updateTrade(trade, omsFill.getDealtAmt(), null);
		if (omsOrderCreditEnabled && (omsFill.getCreditWorkflowMode() == null || omsFill.getCreditWorkflowMode() != CreditWorkflowMode.NO_CHECK.getCode())) {
			Organization brokerOrg = OrganizationUtil.getOrganization(omsFill.getFromOMSOrg());
			if(brokerOrg != null) {
				log.info(OMSConstants.OMS_LOG_PREFIX + " .processOrderFill : setting CreditEntities for brokerOrg: "+brokerOrg);
				trade.setCreditEntities(CreditUtilC.getAllEnabledCreditEntitiesBetween(brokerOrg.getDefaultDealingEntity(), orderRequest.getLegalEntity()));
				for(CreditEntity ce :trade.getCreditEntities()){
					log.info(OMSConstants.OMS_LOG_PREFIX + " .processOrderFill : setting CreditEntities for brokerOrg: "+ce.toString());

				}
			}
			//And take credit on trade
			WorkflowMessage wfm;
			if (omsFill.getCreditWorkflowMode() == null) {
				wfm = CreditServiceFacadeFactory.getInstance(CreditServiceFacadeFactory.CreditServiceType.NOTIFICATION).takeCreditSingleLevel(trade);
			} else {
				wfm = CreditServiceFacadeFactory.getInstance(CreditServiceFacadeFactory.CreditServiceType.NOTIFICATION).takeCreditSingleLevel(trade, true, false, false);
			}
			if (wfm.getStatus().equals(MessageStatus.FAILURE)) {
				log.info(OMSConstants.OMS_LOG_PREFIX + " .processOrderFill : Take Credit Failed ");
				return OMSUtil.createError(OMSConstants.TAKE_CREDIT_FAILED);
			}
		}
		MatchResponseWorkflowHandler.getInstance().fillMatchRequest(trade, matchRequestForTrade, false, false, new long[3]);
		trade.getEventTimes().setResponseSentByAppTime(System.currentTimeMillis());
		DirectedOrderVerificationHandler.getInstance().logTradeMetrics(trade, orderRequest);
		MatchEvent.MatchEventLeg matchEventLeg = matchRequest.getMatchEventLeg();
		matchEventLeg.setCancelledAmount(0);
		if ( MatchRequestWorkflowHelper.getInstance().validateTerminalAmount(matchRequest) ) {
			matchEventLeg.setPendingAmount(0);
			matchRequest.getState().setName(State.Name.RSEXPIRED);
			matchRequest.setExpiratationDone(true);
			MatchResponseWorkflowHandler.getInstance().handleRequestExpiry(matchRequest, new long[3]);
		}
		omsFill.setTradeId(trade.get_id());
		return null;
	}

	public static SingleLegTrade createUpdateTrade( OMSFill omsFill, OrderMatchRequest customerRequest )
	{
		SingleLegOrder customerOrderRequest = customerRequest.getOrderRequest();

		String transactionIdWithfillId = DealingModelUtil.generateTransactionId(customerOrderRequest.getChannel());
		String counterPartyFillId = omsFill.getFillId();
		SingleLegTrade customerTrade = createNewCustomerTrade(customerRequest, transactionIdWithfillId);
		DealingModelUtil.updateRateOnTrade(customerTrade, omsFill.getFillPrice(), omsFill.getDealtAmt());
		customerTrade.setClassification(ISUtilImpl.TRD_SPOT_CLSF.getShortName());
		TradeLeg customerTradeLeg = customerTrade.getTradeLeg();
		customerTradeLeg.setDealtAmount(customerTrade.getDealtCurrency().round(omsFill.getDealtAmt()));
		MatchEvent.MatchEventLeg customerMatchLeg = customerRequest.getMatchEventLeg();
		customerTradeLeg.setClassification(ISUtilImpl.TRDLEG_SPOT_CLSF.getShortName());
		customerTradeLeg.setValueDate(EndOfDayService.getValueDate( customerRequest.getFxRateBasis(), EndOfDayService.getCurrentTradeDate() ).asJdkDate().getTime() );
		long valueDate = customerTradeLeg.getValueDate();
     	long fixingDate = DealingModelUtil.getFixingDate(customerTrade, valueDate);
    	if (fixingDate != -1)
    	{
    		customerTradeLeg.setFixingDate(fixingDate);
    	} 

		switch ( customerMatchLeg.getBuySellMode() ) {
		case BUY :
			customerTradeLeg.setBuyingBaseCurrency(true);
			break;
		case SELL :
			customerTradeLeg.setBuyingBaseCurrency(false);
			break;
		}
		if ( customerOrderRequest.getClientReferenceId() == null ) {
			customerTrade.getTakerDownloadAttributes().setTakerReferenceId(customerOrderRequest.get_id());
		}
		else {
			customerTrade.getTakerDownloadAttributes().setTakerReferenceId(customerOrderRequest.getClientReferenceId());
		}
		customerTrade.getMakerDownloadAttributes().setTakerReferenceId(customerOrderRequest.get_id());
		if(omsFill.isYmBBook()) {
			customerTrade.setBbookTrade(omsFill.isYmBBook());
			customerTrade.setCptyBBookName(ISCommonConstants.YM_BOOK_NAME_B);
		}
		customerTrade.setFirstTradeId(customerRequest.get_id());
		customerTrade.setLegalEntity(customerRequest.getLegalEntity());
		customerTrade.setCreditEntities(customerRequest.getCreditEntities());
		DealingModelUtil.doStateTransition(customerTrade, State.Name.TSINIT, customerTrade.getUser().getFullyQualifiedName());
		ServiceFactory.getTradeService().newTrade(customerTrade);
		UTIService.intercept(customerTrade);
		if ( customerOrderRequest.getOriginatingTrade() != null ) {
			DealingModelUtil.populateOriginatingTradeDescriptor(customerOrderRequest.getOriginatingTrade(), customerTrade.getOriginatingTrade());
		}
		else {
			DealingModelUtil.populateOriginatingTradeDescriptor(customerTrade.getOriginatingTrade(), customerTrade);
		}
		if ( customerOrderRequest.getCoveredTrade() != null ) {
			CoveredTradeDescriptor desc = new CoveredTradeDescriptor();
			desc.setTradeRef(new DealingModelRef<SingleLegTrade>());
			DealingModelUtil.populateCoveredTradeDescriptor(customerOrderRequest.getCoveredTrade(), desc);
			customerTrade.setCoveredTrade(desc);
		}

		GlobalMetrics.getInstance().addTrade(customerTrade.getNamespace().getShortName());
		FXESPWorkflowCache.addTrade(customerTrade);
		customerTrade.setMakerTransactionId(transactionIdWithfillId);
		customerTrade.setExternalReferenceId(counterPartyFillId);
		if (omsFill.getPpSpotSpread() != null) {
			TradeLeg.CoverRateDescriptor coverRateDescriptor = customerTrade.getTradeLeg().getCoverRateDescriptor();
			coverRateDescriptor.setPPPreTradeSpread(omsFill.getPpSpotSpread());
			coverRateDescriptor.setPPSpotSpread(omsFill.getPpSpotSpread());
			coverRateDescriptor.setCoverRate(omsFill.getVenueBMR());
            coverRateDescriptor.setCoverRateUpdated(true);
			log.info(new StringBuilder(OMSConstants.OMS_LOG_PREFIX + " .createUpdateTrade : rid = ").append(omsFill.getFillId())
					.append(", PPPreTradeSpread=").append(coverRateDescriptor.getPPPreTradeSpread()).append(", PPSpotSpread=").append(coverRateDescriptor.getPPSpotSpread())
					.append(", CoverRate=").append(coverRateDescriptor.getCoverRate()).toString());
		}
		return customerTrade;
	}

	private static SingleLegTrade createNewCustomerTrade( OrderMatchRequest customerRequest, String transactionIdWithfillId )
	{
		SingleLegTrade customerTrade = DealingModelFactory.getInstance().newTrade(customerRequest);
		customerRequest.setNumberOfFills(customerRequest.getTrades().size());
		customerTrade.setFullAmountTrade(true);
		customerTrade.setTradingParty(customerRequest.getTradingParty());
		customerTrade.set_id(transactionIdWithfillId);

		customerTrade.setMakerUser(customerRequest.getTradingParty().getLegalEntityOrganization().getDefaultDealingUser());
		return customerTrade;
	}

	public static OrderMatchRequest getMatchRequest( String orderRequestId, String omsOrg )
	{
		SingleLegOrder orderRequest = FXESPWorkflowCache.getOrderRequest( orderRequestId );
		if ( orderRequest != null )
		{

			Collection<OrderMatchRequest> matchRequests = orderRequest.getActiveMatchRequests();
			for (OrderMatchRequest omsMatchRequest : matchRequests)
			{
				TradingParty tp = omsMatchRequest.getTradingParty();
				// if omsOrg is null, return any match request
				if( omsOrg == null )

				{
					log.info( new StringBuilder ( 200 ).append ( OMSConstants.OMS_LOG_PREFIX )
							.append ( ".getMatchRequest : returning the first match request. orderRequestId=" )
							.append ( orderRequestId ).append ( ",omsMatchRequest.tp=" )
							.append( omsMatchRequest.getTradingParty() ).append ( ",omsMatchRequest=" )
							.append ( omsMatchRequest ).toString () );
					return omsMatchRequest;
				}
				else if ( tp != null && tp.getLegalEntityOrganization () != null && omsOrg.equals( tp.getLegalEntityOrganization ().getShortName () ) )
				{
					log.info( new StringBuilder ( 200 ).append ( OMSConstants.OMS_LOG_PREFIX )
							.append ( ".getMatchRequest : matched with omsOrg name for orderRequestId=" )
							.append ( orderRequestId ).append ( ",omsOrg=" ).append ( omsOrg )
							.append ( ",omsMatchRequest.tp=" ).append( tp ).append ( ",omsMatchRequest=" )
							.append ( omsMatchRequest ).toString () );
					return omsMatchRequest;
				}
				else
				{
					log.info( new StringBuilder ( 200 ).append ( OMSConstants.OMS_LOG_PREFIX )
							.append ( ".getMatchRequest : skipping match request for orderRequestId=" )
							.append ( orderRequestId ).append ( ",omsOrg=" ).append ( omsOrg )
							.append ( ",omsMatchRequest.tp=" ).append( tp ).append ( ",omsMatchRequest=" )
							.append ( omsMatchRequest ).toString () );
				}
			}
			if ( matchRequests.isEmpty () )
			{
				log.info ( new StringBuilder ( 200 ).append ( OMSConstants.OMS_LOG_PREFIX )
						.append ( ".getMatchRequest : No match request found for orderRequestId=" )
						.append ( orderRequestId ).append ( ",omsOrg=" ).append ( omsOrg ).append ( ",orderRequest=" )
						.append ( orderRequest ).append ( ",matchRequests=" ).append ( matchRequests ).toString () );
			}
		}
		else
		{
			log.info( new StringBuilder ( 200 ).append ( OMSConstants.OMS_LOG_PREFIX )
					.append ( ".getMatchRequest : No order request found in FXESPWorkflowCache for orderRequestId=" )
					.append ( orderRequestId ).append ( ",omsOrg=" ).append ( omsOrg ).toString () );
		}
		return null;
	}
}
