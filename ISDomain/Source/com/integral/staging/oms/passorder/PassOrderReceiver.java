package com.integral.staging.oms.passorder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.commons.Tuple;
import com.integral.finance.creditLimit.CreditLimit;
import com.integral.finance.dealing.ContingencyParameter;
import com.integral.finance.trade.CreditWorkflowMode;
import com.integral.is.common.exception.MessageCommunicationExceptionC;
import com.integral.is.common.mbean.ClientConfMBean;
import com.integral.is.common.mbean.ISFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.model.dealing.OrderRequest;
import com.integral.oms.OrderAction;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.staging.*;
import com.integral.staging.Order.TIF;
import com.integral.staging.oms.*;
import com.integral.staging.oms.audit.OMSAuditService;
import com.integral.staging.oms.client.OMSNotifier;
import com.integral.staging.oms.client.socketio.OMSResponse;
import com.integral.staging.oms.client.socketio.SocketResponse;
import com.integral.staging.oms.config.OMSConfig;
import com.integral.staging.oms.conn.imtp.OMSServerException;
import com.integral.staging.oms.conn.service.OMSServerConnectionManager;
import com.integral.staging.oms.message.*;
import com.integral.staging.oms.model.OMSEvent;
import com.integral.staging.oms.model.OMSOrderListParams;
import com.integral.staging.oms.model.OMSOrderParam;
import com.integral.staging.oms.model.OMSState;
import com.integral.staging.oms.orderbook.FXStagingOrderBookManager;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;

public class PassOrderReceiver
{
	private static final Log log = LogFactory.getLog(PassOrderReceiver.class);
	final static String ORDER_REJECT_REASON = "Reason: current time has passed the fixing cut-off time";
	static final ClientConfMBean clientconfMBean = ISFactory.getInstance().getClientConfMBean();

	/**
	 * Send Acceptence message for Passed order to requesting OMS org
	 * @param order - oms order
	 * @return errorMsg in case of some error else return null
	 */
	public static String accept( Order order )
	{

		synchronized ( order ) {
			log.info(OMSConstants.OMS_LOG_PREFIX + " accept: Pass order Accepted for org: " + order.getNamespaceName() + " orderId: " + order.get_id());

			OMSAccept omsAccept = new OMSAccept();
			omsAccept.setToOMSOrg(order.getFromOrg());
			omsAccept.setFromOMSOrg(order.getNamespaceName());
			omsAccept.setOrderId(order.get_id());
			omsAccept.setEmsOrder(order.isEMSOrder());
			if(IdcUtilC.getSessionContextUser() != null){
				omsAccept.setUser(IdcUtilC.getSessionContextUser().getShortName());
			}else{
				omsAccept.setUser(OMSConstants.OMS_SYSTEM_USER);
			}
			omsAccept.setClientOrderId(order.getClientOrderId());
			return sendMessage(omsAccept);
		}

	}

	/**
	 * Send Rejection message for Passed order to requesting OMS org
	 * @param order - oms order
	 * @return errorMsg in case of some error else return null
	 */
	public static String reject( Order order )
	{
		synchronized ( order ) {
			log.info(OMSConstants.OMS_LOG_PREFIX + " reject : Pass order Rejected for org: " + order.getNamespaceName() + " orderId: " + order.get_id());

			OMSReject omsReject = new OMSReject();
			omsReject.setToOMSOrg(order.getFromOrg());
			omsReject.setFromOMSOrg(order.getNamespaceName());
			omsReject.setOrderId(order.get_id());
			if(IdcUtilC.getSessionContextUser() != null){
				omsReject.setUser(IdcUtilC.getSessionContextUser().getShortName());
			}else{
				omsReject.setUser(OMSConstants.OMS_SYSTEM_USER);
			}
			omsReject.setEmsOrder(order.isEMSOrder());
			
			omsReject.setClientOrderId(order.getClientOrderId());
			return sendMessage(omsReject);
		}
	}
	
	/**
	 * Send Cancel Acceptence message for Passed order to requesting OMS org
	 * @param order - oms order
	 * @return errorMsg in case of some error else return null
	 */
	public static String cancelAccept( Order order )
	{

		synchronized ( order ) {
			log.info(OMSConstants.OMS_LOG_PREFIX + " cancelAccept: Pass Cancel order Accepted for org: " + order.getNamespaceName() + " orderId: " + order.get_id());

			OMSWithdrawAccept omsWithdrawAccept = new OMSWithdrawAccept();
			omsWithdrawAccept.setToOMSOrg(order.getFromOrg());
			omsWithdrawAccept.setFromOMSOrg(order.getNamespaceName());
			omsWithdrawAccept.setOrderId(order.get_id());
			omsWithdrawAccept.setEmsOrder(order.isEMSOrder());
			omsWithdrawAccept.setUser(IdcUtilC.getSessionContextUser().getShortName());
						omsWithdrawAccept.setClientOrderId(order.getClientOrderId());
			return sendMessage(omsWithdrawAccept);
		}
	}

	/**
	 * Send Cancel Rejection message for Passed order to requesting OMS org
	 * @param order - oms order
	 * @return errorMsg in case of some error else return null
	 */
	public static String cancelReject( Order order )
	{
		synchronized ( order ) {
			log.info(OMSConstants.OMS_LOG_PREFIX + " cancelReject : Pass Cancel order Rejected for org: " + order.getNamespaceName() + " orderId: " + order.get_id());

			OMSWithdrawReject omsWithdrawReject = new OMSWithdrawReject();
			omsWithdrawReject.setToOMSOrg(order.getFromOrg());
			omsWithdrawReject.setFromOMSOrg(order.getNamespaceName());
			omsWithdrawReject.setOrderId(order.get_id());
			if(IdcUtilC.getSessionContextUser() != null){
				omsWithdrawReject.setUser(IdcUtilC.getSessionContextUser().getShortName());
			}else{
				omsWithdrawReject.setUser(OMSConstants.OMS_SYSTEM_USER);
			}
			omsWithdrawReject.setEmsOrder(order.isEMSOrder());
			
			omsWithdrawReject.setClientOrderId(order.getClientOrderId());
			return sendMessage(omsWithdrawReject);
		}
	}

	/**
	 * Send Amend Acceptance message for Passed order to requesting OMS org
	 * @param order - oms order
	 * @return errorMsg in case of some error else return null
	 */
	public static String amendAccept( Order order )
	{

		synchronized ( order ) {
			log.info(OMSConstants.OMS_LOG_PREFIX + " amendAccept: Pass Amend order Accepted for org: " + order.getNamespaceName() + " orderId: " + order.get_id()+ ", amendParameters: "+order.getAmendParameters().toString());

			OMSAmendAccept omsAmendAccept = new OMSAmendAccept();
			omsAmendAccept.setToOMSOrg(order.getFromOrg());
			omsAmendAccept.setFromOMSOrg(order.getNamespaceName());
			omsAmendAccept.setOrderId(order.get_id());
			omsAmendAccept.setEmsOrder(order.isEMSOrder());
			omsAmendAccept.setUser(IdcUtilC.getSessionContextUser().getShortName());
			omsAmendAccept.setClientOrderId(order.getClientOrderId());
			if ( order.getAmendParameters().getDealtAmt() != null){
				omsAmendAccept.setDealtAmt(order.getAmendParameters().getDealtAmt());
			}
			if ( order.getAmendParameters().getPrice() != null){
				omsAmendAccept.setOrderPrice(order.getAmendParameters().getPrice());
			}
			return sendMessage(omsAmendAccept);
		}
	}

	/**
	 * Send Amend Rejection message for Passed order to requesting OMS org
	 * @param order - oms order
	 * @return errorMsg in case of some error else return null
	 */
	public static String amendReject( Order order )
	{
		synchronized ( order ) {
			log.info(OMSConstants.OMS_LOG_PREFIX + " amendReject : Pass Amend order Rejected for org: " + order.getNamespaceName() + " orderId: " + order.get_id());

			OMSAmendReject omsAmendReject = new OMSAmendReject();
			omsAmendReject.setToOMSOrg(order.getFromOrg());
			omsAmendReject.setFromOMSOrg(order.getNamespaceName());
			omsAmendReject.setOrderId(order.get_id());
			if(IdcUtilC.getSessionContextUser() != null){
				omsAmendReject.setUser(IdcUtilC.getSessionContextUser().getShortName());
			}else{
				omsAmendReject.setUser(OMSConstants.OMS_SYSTEM_USER);
			}
			omsAmendReject.setEmsOrder(order.isEMSOrder());

			omsAmendReject.setClientOrderId(order.getClientOrderId());
			return sendMessage(omsAmendReject);
		}
	}

	public static void onPassAmendAcceptedResponse( OMSAmendAcceptResponse omsAmendAcceptResp )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onPassAmendAcceptedResponse: Pass Amend Response received " + omsAmendAcceptResp);

		Order order = getOrderFromMessage(omsAmendAcceptResp);
		if ( order == null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "onPassAmendAcceptedResponse: Order not found" + omsAmendAcceptResp);
			return;
		}
		synchronized ( order ) {
			Double amendedPrice = null;
			if (order.getFixingReference() == null) {
				order.setState(OMSState.ACTIVE.getOrderStatusType().code);
				if (order.getAmendParameters().getPrice() != null &&
						(order.getPrice() == null || Double.compare(order.getAmendParameters().getPrice(), order.getPrice()) != 0)) {
					amendedPrice = order.getAmendParameters().getPrice();
				}
				if (amendedPrice != null) {
					order.setPrice(amendedPrice);
				}
			} else {
				order.setState(OMSState.DRAFT.getOrderStatusType().code);
			}
			if (order.getAmendParameters().getDealtAmt() != null) {
				order.setDealtAmt(order.getAmendParameters().getDealtAmt());
			}
			ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

			if ( errorMsg != null ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "onPassAmendAcceptedResponse: Error while updating order " + omsAmendAcceptResp);
			}
			else {
				OMSState newState = OMSState.fromStatusCode(order.getState());

				if ( amendedPrice != null && newState == OMSState.ACTIVE )
				{
					FXStagingOrderBookManager.amendOrder(order.getId(), amendedPrice);
				}
				OMSResponse omsResponse;
				if (order.getFixingReference() == null) {
					omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_AMEND_ACCEPTED, OMSState.AMEND_REQUESTED, OMSState.ACTIVE, null);
				} else {
					omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_AMEND_ACCEPTED, OMSState.AMEND_REQUESTED, OMSState.DRAFT, null);
				}
				OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
				OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsAmendAcceptResp.getUser(), null));
			}
		}
	}

	public static void onPassAmendRejectResponse( OMSAmendRejectResponse omsAmendRejectResp )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onPassAmendRejectResponse: Pass Amend Reject Response received " + omsAmendRejectResp);

		Order order = getOrderFromMessage(omsAmendRejectResp);
		if ( order == null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "onPassAmendRejectResponse: Order not found" + omsAmendRejectResp);
			return;
		}
		synchronized ( order ) {
			if (order.getFixingReference() == null) {
				order.setState(OMSState.ACTIVE.getOrderStatusType().code);
			} else {
				order.setState(OMSState.DRAFT.getOrderStatusType().code);
			}
			ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

			if ( errorMsg != null ) {
				log.error(OMSConstants.OMS_LOG_PREFIX + "onPassAmendRejectResponse: Error while updating order " + omsAmendRejectResp);
			}
			else {
				OMSResponse omsResponse;
				if (order.getFixingReference() == null) {
					omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_AMEND_REJECTED, OMSState.AMEND_REQUESTED, OMSState.ACTIVE, null);
				} else {
					String rejectReason = null;
					long timeInMin = clientconfMBean.getFixingOrderAmendCancelThresholdTime(omsAmendRejectResp.getToOMSOrg());
					if (order.getFixingTime() - System.currentTimeMillis() < (timeInMin * 60 * 1000)) {
						rejectReason = ORDER_REJECT_REASON;
					}
					omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_AMEND_REJECTED, OMSState.AMEND_REQUESTED, OMSState.DRAFT, null, rejectReason);
				}
				OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
				OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsAmendRejectResp.getUser(), null));
			}
		}
	}

	/**
	 * Send Filled message for Passed order to requesting OMS org
	 * @param order - oms order
	 * @return errorMsg in case of some error else return null
	 */
	public static String fill( Order order , OrderAction action)
	{
		synchronized ( order ) {
			log.info(OMSConstants.OMS_LOG_PREFIX + " fill : Pass order Fill for org: " + order.getNamespaceName()
					+ " orderId: " + order.get_id()+", FillPrice: "+order.getFilledPrice()+", VenueBMR: "+order.getVenueBMR()+", PpSpotSpread: "+order.getPpSpotSpread());

			OMSFill omsFill = new OMSFill();
			omsFill.setToOMSOrg(order.getFromOrg());
			omsFill.setFromOMSOrg(order.getNamespaceName());
			omsFill.setOrderId(order.getId());
			omsFill.setFillPrice(order.getFilledPrice());
			omsFill.setFillId("OMS" + order.getId());
			omsFill.setTradeId(order.getTradeId());
			omsFill.setUser(order.getActionUser());
			omsFill.setEmsOrder(order.isEMSOrder());
			omsFill.setVenueBMR(order.getVenueBMR());
			omsFill.setPpSpotSpread(order.getPpSpotSpread());
			omsFill.setClientOrderId(order.getClientOrderId());
			omsFill.setDealtAmt(order.getDealtAmt());
			OrderStatusType currentStatus = OrderStatusType.fromCode(order.getState());
			if (currentStatus.code == OrderStatusType.CREDIT_FAIL.code) {
				if (action.equals(OrderAction.CREDIT_OVERRIDE_ACCEPT)) {
					omsFill.setCreditWorkflowMode((long)CreditWorkflowMode.EXCESS_UTILIZATION_ALLOWED.getCode());
				} else {
					omsFill.setCreditWorkflowMode((long)CreditWorkflowMode.NO_CHECK.getCode());
				}
			}
			return sendMessage(omsFill);
		}
	}

	/**
	 * Send auto message for Passed order to requesting OMS org
	 * @param order - oms order
	 * @return errorMsg in case of some error else return null
	 */
	public static String auto( Order order )
	{
		synchronized ( order ) {
			log.info(OMSConstants.OMS_LOG_PREFIX + " auto :  Pass order Auto Execution for org: " + order.getNamespaceName() + " orderId: " + order.get_id());

			OMSAuto omsAuto = new OMSAuto();
			omsAuto.setToOMSOrg(order.getFromOrg());
			omsAuto.setFromOMSOrg(order.getNamespaceName());
			omsAuto.setOrderId(order.getId());
			if(IdcUtilC.getSessionContextUser() != null){
				omsAuto.setUser(IdcUtilC.getSessionContextUser().getShortName());
			}else{
				omsAuto.setUser(OMSConstants.OMS_SYSTEM_USER);
			}
			omsAuto.setEmsOrder(order.isEMSOrder());
		
			omsAuto.setClientOrderId(order.getClientOrderId());
			return sendMessage(omsAuto);
		}
	}
	
	/**
	 * Send manual message for Passed order to requesting OMS org
	 * @param order - oms order
	 * @return errorMsg in case of some error else return null
	 */
	public static String manual( Order order )
	{
		synchronized ( order ) {
			log.info(OMSConstants.OMS_LOG_PREFIX + " manual :  Pass order manual Execution for org: " + order.getNamespaceName() + " orderId: " + order.get_id());

			OMSManual omsManual = new OMSManual();
			omsManual.setToOMSOrg(order.getFromOrg());
			omsManual.setFromOMSOrg(order.getNamespaceName());
			omsManual.setOrderId(order.getId());
			if(IdcUtilC.getSessionContextUser() != null){
				omsManual.setUser(IdcUtilC.getSessionContextUser().getShortName());
			}else{
				omsManual.setUser(OMSConstants.OMS_SYSTEM_USER);
			}
			omsManual.setEmsOrder(order.isEMSOrder());
		
			omsManual.setClientOrderId(order.getClientOrderId());
			return sendMessage(omsManual);
		}
	}
	
	public static void onPassAcceptedResponse( OMSAcceptResponse omsAcceptResp )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onPassAcceptedResponse: Pass Accepted Response received " + omsAcceptResp);

		Order order = getPrimaryServerOrderFromMessage(omsAcceptResp);
		if ( order == null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "onPassAcceptedResponse: Order not found" + omsAcceptResp);
			return;
		}
		synchronized ( order ) {
			ErrorMessage errorMsg;
			if(order.getFixingReference() == null) {
				order.setState(OMSState.ACTIVE.getOrderStatusType().code);
				errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());
				FXStagingOrderBookManager.submitOrderToOrderBook(order);
			}else{
				order.setState(OMSState.DRAFT.getOrderStatusType().code);
				errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());
			}

			if ( errorMsg != null ) {
				log.error(OMSConstants.OMS_LOG_PREFIX + "onPassAcceptedResponse: Error while updating order " + omsAcceptResp);
				return;
			}
			else {
				OMSResponse omsResponse;
				if(order.getFixingReference() != null){
					omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_ACCEPTED, OMSState.RECEIVED, OMSState.DRAFT, null);
				}else {
					omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_ACCEPTED, OMSState.RECEIVED, OMSState.ACTIVE, null);
				}
				OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
				OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsAcceptResp.getUser(), null));
				if(order.getOrderContingencies().size() > 0 &&
						((ContingencyParameter)((ArrayList)order.getOrderContingencies()).get(0)).getType() ==1){
					OMSOrderService.handleAcceptAutoManualOrderContigencies(order, OMSConstants.OMS_SYSTEM_USER);
				}
			}
			if(order.getFixingReference() == null) {
				handleAuto(omsAcceptResp, order);
			}
		}
	}

	private static void handleAuto(OMSAcceptResponse omsAcceptResp, Order order) {
		boolean routeOrderToOMSWithAuto = OrderConfiguration.getInstance().isOrderRouteToOMSWithAuto(omsAcceptResp.getToOMSOrg(),
				omsAcceptResp.getFromOMSOrg());
		if (routeOrderToOMSWithAuto) {
			log.info(OMSConstants.OMS_LOG_PREFIX + "onPassAcceptedResponse().handleAuto(): Moving Pass Accepted order to auto" + omsAcceptResp);
			boolean validForAuto = true;
			if (OMSState.ACTIVE.getOrderStatusType().getCode() == order.getState()) {
				//If order is in Active state then remove it from order book
				if (!FXStagingOrderBookManager.withdrawOrder(order.get_id())) {
					log.error(OMSConstants.OMS_LOG_PREFIX + "onPassAcceptedResponse.handleAuto(): Order could not be removed from Orderbook: " + omsAcceptResp);
					validForAuto = false;
				}
			}
			if (validForAuto) {
				auto(order);
			}
		}
	}

	public static void onPassWithdrawAcceptedResponse( OMSWithdrawAcceptResponse omsWithdrawAcceptResp )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onPassWithdrawAcceptedResponse: Pass Withdraw Accepted Response received " + omsWithdrawAcceptResp);

		Order order = getOrderFromMessage(omsWithdrawAcceptResp);
		if ( order == null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "onPassWithdrawAcceptedResponse: Order not found" + omsWithdrawAcceptResp);
			return;
		}
		synchronized ( order ) {
			FXStagingOrderBookManager.withdrawOrder(order.getId());
			order.setState(OMSState.CANCELLED.getOrderStatusType().code);
			ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

			if ( errorMsg != null ) {
				log.warn(OMSConstants.OMS_LOG_PREFIX + "onPassWithdrawAcceptedResponse: Failed to Cancel order during withdraw request " + omsWithdrawAcceptResp);
			}
			else {
				OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_CANCEL_ACCEPTED, OMSState.CANCEL_REQUESTED, OMSState.CANCELLED, null);
				OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
				OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsWithdrawAcceptResp.getUser(), null));
				OMSOrderService.handleOrderContigencies(order, OMSConstants.OMS_SYSTEM_USER);
			}
		}
	}
	
	public static void onPassWithdrawRejectResponse( OMSWithdrawRejectResponse omsWithdrawRejectResp )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onPassWithdrawRejectResponse: Pass Withdraw Reject Response received " + omsWithdrawRejectResp);

		Order order = getOrderFromMessage(omsWithdrawRejectResp);
		if ( order == null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "onPassWithdrawRejectResponse: Order not found" + omsWithdrawRejectResp);
			return;
		}
		synchronized ( order ) {
			if (order.getFixingReference() == null) {
				order.setState(OMSState.ACTIVE.getOrderStatusType().code);
			} else {
				order.setState(OMSState.DRAFT.getOrderStatusType().code);
			}
			ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());
			
			if ( errorMsg != null ) {
				log.error(OMSConstants.OMS_LOG_PREFIX + "onPassWithdrawRejectResponse: Error while updating order " + omsWithdrawRejectResp);
			}
			else {
				OMSResponse omsResponse;
				if (order.getFixingReference() == null) {
					omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_CANCEL_REJECTED, OMSState.CANCEL_REQUESTED, OMSState.ACTIVE, null);
				} else {
					String rejectReason = null;
					long timeInMin = clientconfMBean.getFixingOrderAmendCancelThresholdTime(omsWithdrawRejectResp.getToOMSOrg());
					if (order.getFixingTime() - System.currentTimeMillis() < (timeInMin * 60 * 1000)) {
						rejectReason = ORDER_REJECT_REASON;
					}
					omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_CANCEL_REJECTED, OMSState.CANCEL_REQUESTED, OMSState.DRAFT, null, rejectReason);
				}
				OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
				OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsWithdrawRejectResp.getUser(), null));
			}
		}
	}
	
	public static void onAutoResponse( OMSAutoResponse omsAutoResp )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onAutoResponse: Pass Auto Response received " + omsAutoResp);	
		Order order = getPrimaryServerOrderFromMessage(omsAutoResp);
		if ( order == null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "onAutoResponse:  Order not found" + omsAutoResp);
			return;
		}
		synchronized ( order ) {
			if(omsAutoResp.isFailed()){
				log.warn(OMSConstants.OMS_LOG_PREFIX + "onAutoResponse:  Auto failed" + omsAutoResp.getFailureReason());
				OMSResponse omsResponse = new OMSResponse(SocketResponse.Status.ERROR, order.get_id() + " : "+ omsAutoResp.getFailureReason());
				OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
				return;
			}
			order.setState(OMSState.AUTO.getOrderStatusType().code);
			ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

			if ( errorMsg != null ) {
				log.error(OMSConstants.OMS_LOG_PREFIX + "onAutoResponse: Error while updating order " + omsAutoResp);
			}
			else {
				OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.AUTO_SUCCESS, OMSState.ACTIVE, OMSState.AUTO, null);
				OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
				OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsAutoResp.getUser(), null));
				if(order.getOrderContingencies().size() > 0 &&
						((ContingencyParameter)((ArrayList)order.getOrderContingencies()).get(0)).getType() ==1){
					OMSOrderService.handleAcceptAutoManualOrderContigencies(order, OMSConstants.OMS_SYSTEM_USER);
				}
			}
		}
	}
	
	public static void onUpdate( OMSUpdate omsUpdate )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onUpdate: Update received " + omsUpdate);
	

		Order order = getOrderFromMessage(omsUpdate);
		if ( order == null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "onUpdate:  Order not found" + omsUpdate);
			return;
		}
		synchronized ( order ) {
			if(omsUpdate.getOrderUpdatedStatus().equals(OMSUpdate.OrderUpdatedStatus.FILLED.getValue())
					&& order.getState() != OMSState.NOTIFIED.getOrderStatusType().code){
				order.setState(OMSState.NOTIFIED.getOrderStatusType().code);
				ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

				if ( errorMsg != null ) {
					log.error(OMSConstants.OMS_LOG_PREFIX + "onUpdate: Error while updating order " + omsUpdate);
				}
				else {
					OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.STATE_CHANGED, OMSState.AUTO, OMSState.NOTIFIED, null);
					OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
					OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsUpdate.getUser(), null));
					OMSOrderService.handleAcceptAutoManualOrderContigencies(order, OMSConstants.OMS_SYSTEM_USER);
				}
			}			
			
		}
	}
	
	public static void onManualResponse( OMSManualResponse omsManualResp )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onCancelAutoResponse: Pass Manual Response received " + omsManualResp);		

		Order order = getOrderFromMessage(omsManualResp);
		if ( order == null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "onCancelAutoResponse:  Order not found" + omsManualResp);
			return;
		}
		synchronized ( order ) {
			if(omsManualResp.isFailed()){
				log.warn(OMSConstants.OMS_LOG_PREFIX + "onCancelAutoResponse:  Manual failed" + omsManualResp.getFailureReason());
				OMSResponse omsResponse = new OMSResponse(SocketResponse.Status.ERROR, order.get_id() + " : " + omsManualResp.getFailureReason());
				OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
				return;
			}
			order.setState(OMSState.ACTIVE.getOrderStatusType().code);
			ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

			if ( errorMsg != null ) {
				log.error(OMSConstants.OMS_LOG_PREFIX + "onCancelAutoResponse: Error while updating order " + omsManualResp);
			}
			else {
				OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.STATE_CHANGED, OMSState.AUTO, OMSState.ACTIVE, null);
				OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
				OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsManualResp.getUser(), null));
				if(order.getOrderContingencies().size() > 0 &&
						((ContingencyParameter)((ArrayList)order.getOrderContingencies()).get(0)).getType() ==1){
					OMSOrderService.handleAcceptAutoManualOrderContigencies(order, OMSConstants.OMS_SYSTEM_USER);
				}
			}
		}
	}

	public static void onPassRejectedResponse( OMSRejectResponse omsRejectResp )
	{

		log.info(OMSConstants.OMS_LOG_PREFIX + "onPassRejectedResponse:  Pass Rejected Response received " + omsRejectResp);

		Order order = getOrderFromMessage(omsRejectResp);
		if ( order == null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "onPassRejectedResponse: Order not found" + omsRejectResp);
			return;
		}
		FXStagingOrderBookManager.withdrawOrder(order.getId());

		order.setState(OMSState.CANCELLED.getOrderStatusType().code);
		ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

		if ( errorMsg != null ) {
			log.error(OMSConstants.OMS_LOG_PREFIX + "onPassRejectedResponse: Error while updating order " + omsRejectResp);
			return;
		}
		String rejectReason = null;
		if(order.getFixingTime() != null) {
			long timeInMin = clientconfMBean.getNewFixingOrderThresholdTime(omsRejectResp.getToOMSOrg());
			if (order.getFixingTime() - System.currentTimeMillis() < (timeInMin * 60 * 1000)) {
				rejectReason = ORDER_REJECT_REASON;
			}
		}

		OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_REJECTED, OMSState.RECEIVED, OMSState.REJECTED, null, rejectReason);
		OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
		OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsRejectResp.getUser(), null));

		if(order.isPrimaryOrder() && order.getOrderContingencies().size() > 0){
				OMSOrderService.handleOrderContigencies(order, OMSConstants.OMS_SYSTEM_USER);
		}

	}

	public static void onPassFillResponse( OMSFillResponse omsFillResp )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onPassFillResponse: Pass Filled Response received " + omsFillResp);
		Order order = getOrderFromMessage(omsFillResp);
		if ( order == null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "onPassFillResponse: Order not found" + omsFillResp);
		}
		else {
			if ( order.isEMSOrder() ) {
				if(omsFillResp.getFailureReason() != null && omsFillResp.getFailureReason().equals(OMSConstants.TAKE_CREDIT_FAILED)){
					order.setTradeId(omsFillResp.getTradeId());
					order.setState(OrderStatusType.CREDIT_FAIL.code);
					String correlationId = order.getOrderBatchId();
					if (correlationId == null)
						correlationId = order.getId();
					ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, correlationId);
					if (errorMsg == null) {
						OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.CREDIT_FAIL, null,
								OMSState.CREDIT_FAIL, null);
						OMSResponse omsResponseNotify = new OMSResponse(SocketResponse.Status.ERROR, "Order:"+order.get_id() + "  " + CreditLimit.ERROR_INSUFFICIENT_CREDIT);
						OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponseNotify);
						OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(),
								null, null));
					}
				}else {
					order.setTradeId(omsFillResp.getTradeId());
					ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());
					if (errorMsg != null) {
						log.error(OMSConstants.OMS_LOG_PREFIX + " onPassFillResponse: Error while updating order " + omsFillResp);
					}else if(omsFillResp.isInitialFill()){
						OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.STATE_CHANGED, OMSState.FILLED,
								OMSState.NOTIFIED, null);
						OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
						OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(),
								null, null));
					}
				}
			}
		}
	}

	public static void onPassConfirmation( OMSOrderConfirmation omsConfirm )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onPassConfirmation:  Pass Confirmation received " + omsConfirm);
		Order order = getOrderFromMessage(omsConfirm);
		if ( order == null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + "onPassConfirmation:  Order not found" + omsConfirm);
		}
		else {
			if(order.isEMSOrder()) {
				log.info(OMSConstants.OMS_LOG_PREFIX + " onPassConfirmation: Auto Order  received " + omsConfirm);
				order.setTradeId(omsConfirm.getTradeId());
				order.setState(OMSState.NOTIFIED.getOrderStatusType().code);
				
				ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

				if ( errorMsg != null ) {
					log.error(OMSConstants.OMS_LOG_PREFIX + " onPassConfirmation: Error while updating order " + omsConfirm);
					return;
				}
				OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.STATE_CHANGED, null, OMSState.NOTIFIED, null);
				OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
				OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), OMSConstants.OMS_SYSTEM_USER, null));
			}

		}
	}
	
	public static void onPassOrder( OMSPass omsPass )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + " onPassOrder: Pass order received " + omsPass);

		OMSPassResponse passResponse = (OMSPassResponse)populateResponse(omsPass, new OMSPassResponse());
		try {
			Organization org = ReferenceDataCacheC.getInstance().getOrganization(omsPass.getToOMSOrg());
			OMSOrderListParams orderListParams = new OMSOrderListParams();
			List<OMSOrderParam> omsParams = new ArrayList<OMSOrderParam>();
			for ( OMSOrderParam orderParam : omsPass.getOmsOrders() ) {
				if(orderParam.isPrimaryOrder()){
					orderParam.setOrderState(OMSState.RECEIVED.name());
				}else{
					orderParam.setOrderState(OMSState.DRAFT.name());
				}
				if(orderParam.getFixingReference() != null){
					orderParam.setOrderPrice(null);
					orderParam.setOrderType(OrderRequest.Type.FIXING.toString());
				}
				orderParam.setEmsOrder(omsPass.isEmsOrder());
				orderParam.setFromOrg(omsPass.getFromOMSOrg());
				omsParams.add(orderParam);
				Order order = OrderStagingServiceFactory.getOrderStagingService().getOrderByClOrdId(orderParam.getClientOrderId(), org.getDefaultDealingUser(), null);
				if ( order != null && order.getState() != OMSState.CANCELLED.getOrderStatusType().code && order.getState() != OMSState.ARCHIVED.getOrderStatusType().code ) {
					log.info(OMSConstants.OMS_LOG_PREFIX + " onPassOrder: Duplicate order received " + omsPass);
					passResponse.setFailureReason(OMSConstants.OMS_DUPLICATE_ORDER_ID);
					sendMessage(passResponse);
					return;
				}
			}
			
			orderListParams.setOrderListParams(omsParams);
			
			Tuple<String, OrderBatch> response = OMSOrderService.createBatchOrders(org.getDefaultDealingUser(), orderListParams, null);
			if ( response.first != null || response.second == null ) {
				log.info(OMSConstants.OMS_LOG_PREFIX + " onPassOrder: Pass Order creation failed " + omsPass + " Reason " + response.first);
				passResponse.setFailureReason(response.first);
				passResponse.setFailed(true);
				sendMessage(passResponse);
			}
			else {
				OrderBatch orderBatch = response.second;
				List<OMSOrderParam> omsResponseParams = new ArrayList<OMSOrderParam>();
				boolean isFixingOrder = false;
				long fixingTime = 0L;
				for ( Order newOrder : orderBatch.getOrderList() ) {
					if ( newOrder.getTif() == TIF.GTD && newOrder.getExpiryTime() != null ) {
						OrderExpiryManager.scheduleExpiry(newOrder);
					}
					OMSResponse omsResponse = OMSResponse.getOMSResponse(newOrder, OMSEvent.PASS_RECEIVED, null, OMSState.RECEIVED, null);
					OMSNotifier.notifyAllClients(newOrder.getNamespaceName(), omsResponse);
					OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, newOrder.getNamespaceName(), omsPass.getUser(), omsPass.getFromOMSOrg()));
					omsResponseParams.add(OMSUtil.convertToParam(null, newOrder));
					if(newOrder.getFixingReference() != null) {
						isFixingOrder = true;
						fixingTime = newOrder.getFixingTime();
					}
				}
				passResponse.setOmsOrders(omsResponseParams);
				sendMessage(passResponse);
				if(!isFixingOrder) {
					handleAccept(omsPass, orderBatch);
				}else{
						long timeInMin = clientconfMBean.getNewFixingOrderThresholdTime(omsPass.getToOMSOrg());
						if (fixingTime - System.currentTimeMillis() < (timeInMin * 60 * 1000)) {
							log.info(OMSConstants.OMS_LOG_PREFIX + "onPassOrder: Rejecting order as order placing is not allowed near to fixing time. order: "
									+ omsPass + ", fixingTime: " + fixingTime + ", new order place Threshold time in min: " + timeInMin);
							handleReject(omsPass, orderBatch);
						}else if(OrderConfiguration.getInstance().isRouteFixingOrderToOMSWithAutoAccept(omsPass.getToOMSOrg(),
								omsPass.getFromOMSOrg())){
							handleFixingOrderAccept(omsPass, orderBatch);
						}
				}
			}
		}catch (Exception e) {
			log.error(OMSConstants.OMS_LOG_PREFIX + " onPassOrder: Error while handling pass order " + omsPass , e);
			passResponse.setFailureReason(OMSConstants.INTERNAL_SERVER_ERROR);
			passResponse.setFailed(true);
			sendMessage(passResponse);
		}

	}

	private static void handleAccept(OMSPass omsPass, OrderBatch orderBatch) {
		boolean routeOrderToOMSWithAuto = OrderConfiguration.getInstance().isOrderRouteToOMSWithAuto(omsPass.getToOMSOrg(),
				omsPass.getFromOMSOrg());
		if (routeOrderToOMSWithAuto) {
			log.info(OMSConstants.OMS_LOG_PREFIX + "onPassOrder.handleAccept(): Moving Pass order to auto-accept" + omsPass);
			for (Order newOrder : orderBatch.getOrderList()) {
				if (newOrder.isPrimaryOrder()) {
					String errors = OrderValidator.validateAutoOrder(newOrder);
					if (errors != null) {
						log.error(OMSConstants.OMS_LOG_PREFIX + "onPassOrder.handleAccept(): Some error in validateAutoOrder: " + errors);
						reject(newOrder);
					} else {
						accept(newOrder);
					}
					break;
				}
			}
		}
	}

	private static void handleReject(OMSPass omsPass, OrderBatch orderBatch) {
		log.info(OMSConstants.OMS_LOG_PREFIX + "onPassOrder.handleReject(): Moving Pass order to auto-reject: " + omsPass);
		for (Order newOrder : orderBatch.getOrderList()) {
			if (newOrder.isPrimaryOrder()) {
				reject(newOrder);
				break;
			}
		}
	}

	private static void handleFixingOrderAccept(OMSPass omsPass, OrderBatch orderBatch) {
		log.info(OMSConstants.OMS_LOG_PREFIX + "onPassOrder.handleFixingOrderAccept(): Moving Pass fixing order to auto-accept: " + omsPass);
		for (Order newOrder : orderBatch.getOrderList()) {
			if (newOrder.isPrimaryOrder()) {
				accept(newOrder);
				break;
			}
		}
	}

	/**
	 * Send Cancellation message for Passed order to requesting OMS org
	 * @param order - oms order
	 * @return errorMsg in case of some error else return null
	 */
	public static String cancel( Order order )
	{
		synchronized ( order ) {
			log.info(OMSConstants.OMS_LOG_PREFIX + " cancel: Pass order Cancelled for org: " + order.getNamespaceName() + " orderId: " + order.get_id());

			OMSCancel omsCancel = new OMSCancel();
			omsCancel.setToOMSOrg(order.getFromOrg());
			omsCancel.setFromOMSOrg(order.getNamespaceName());
			omsCancel.setOrderId(order.get_id());
			omsCancel.setUser(IdcUtilC.getSessionContextUser().getShortName());
			omsCancel.setClientOrderId(order.getClientOrderId());
			omsCancel.setEmsOrder(order.isEMSOrder());
			return sendMessage(omsCancel);
		}
	}

	public static void onWithdrawOrder( OMSWithdraw omsWithdraw )
	{

		log.info(OMSConstants.OMS_LOG_PREFIX + "onWithdrawOrder: Withdraw Pass order received " + omsWithdraw);
		OMSWithdrawResponse withdrawResp = (OMSWithdrawResponse)populateResponse(omsWithdraw, new OMSWithdrawResponse());
		boolean cancelRequested = false;
		Order order = getOrderFromMessage(omsWithdraw);
		if ( order == null ) {
			log.info(OMSConstants.OMS_LOG_PREFIX + "onWithdrawOrder: Withdraw Pass Order Not found for " + omsWithdraw);
			withdrawResp.setFailureReason(OMSConstants.OMS_ORDER_NOT_FOUND);
			withdrawResp.setFailed(true);
		}
		else {
			synchronized ( order ) {
				OMSState oldState = OMSState.fromStatusCode(order.getState());
				if (!(oldState.equals(OMSState.RECEIVED) || (oldState.equals(OMSState.DRAFT) && !order.getType().equals(OrderRequest.Type.FIXING)) || oldState.equals(OMSState.CANCELLED) || omsWithdraw.isExpired() || omsWithdraw.isAuto())) {
					order.setState(OMSState.CANCEL_REQUESTED.getOrderStatusType().code);
					ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

					if (errorMsg != null) {
						log.warn(OMSConstants.OMS_LOG_PREFIX
								+ "onWithdrawOrder: Failed to Cancel order during withdraw request "
								+ omsWithdraw);
						withdrawResp
								.setFailureReason(OMSConstants.INTERNAL_SERVER_ERROR);
						withdrawResp.setFailed(true);
					} else {
						OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_CANCEL_RECEIVED, null,
								OMSState.CANCEL_REQUESTED, null);
						OMSNotifier.notifyAllClients(order.getNamespaceName(),omsResponse);
						OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(),
								omsWithdraw.getUser(), omsWithdraw.getFromOMSOrg()));
						cancelRequested = true;
					}
				} else{
					withdrawResp.setCancelBeforeAccept(true);
					if(omsWithdraw.isExpired()){
						withdrawResp.setExpired(true);
					}
					FXStagingOrderBookManager.withdrawOrder(order.getId());
					order.setState(OMSState.CANCELLED.getOrderStatusType().code);
					ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

					if ( errorMsg != null ) {
						log.warn(OMSConstants.OMS_LOG_PREFIX + "onWithdrawOrder: Failed to Cancel order during withdraw request " + omsWithdraw);
						withdrawResp.setFailureReason(OMSConstants.INTERNAL_SERVER_ERROR);
						withdrawResp.setFailed(true);
					}
					else {
						OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_WITHDRAWN, null, OMSState.CANCELLED, null);
						OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
						OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsWithdraw.getUser(), omsWithdraw.getFromOMSOrg()));
						if(!(omsWithdraw.isAuto() && order.getOrderContingencies().size() > 0 &&
								((ContingencyParameter)((ArrayList)order.getOrderContingencies()).get(0)).getType() ==1)){
							OMSOrderService.handleOrderContigencies(order, OMSConstants.OMS_SYSTEM_USER);
						}
					}
				}
			}
		}

		String errorMsgStr = sendMessage(withdrawResp);
		if ( errorMsgStr != null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + " onWithdrawOrder: Failed to send Reject response" + omsWithdraw);
		}
		if (cancelRequested && order.getType().equals(OrderRequest.Type.FIXING)) {
			long timeInMin = clientconfMBean.getFixingOrderAmendCancelThresholdTime(omsWithdraw.getToOMSOrg());
			if (order.getFixingTime() - System.currentTimeMillis() < (timeInMin * 60 * 1000)) {
				log.info(OMSConstants.OMS_LOG_PREFIX + "onWithdrawOrder: Rejecting cancellation as cancel is not allowed near to fixing time. order: "
						+ omsWithdraw + ", fixingTime: " + order.getFixingTime() + ", Cancel Threshold in min: " + timeInMin);
				cancelReject(order);
			}
		}
	}

	public static void onAmendOrder( OMSAmend omsAmend )
	{
		log.info(OMSConstants.OMS_LOG_PREFIX + "onAmendOrder: Amend Pass order received " + omsAmend);
		OMSAmendResponse amendResp = (OMSAmendResponse)populateResponse(omsAmend, new OMSAmendResponse());
		boolean amendRequested = false;
		Order order = getOrderFromMessage(omsAmend);
		if ( order == null ) {
			log.info(OMSConstants.OMS_LOG_PREFIX + "onAmendOrder: Amend Pass Order Not found for " + omsAmend);
			amendResp.setFailureReason(OMSConstants.OMS_ORDER_NOT_FOUND);
			amendResp.setFailed(true);
		}
		else {
			synchronized ( order ) {
				OMSState oldState = OMSState.fromStatusCode(order.getState());
				if (!(oldState.equals(OMSState.RECEIVED) || (oldState.equals(OMSState.DRAFT) && !order.getType().equals(OrderRequest.Type.FIXING)) || oldState.equals(OMSState.AUTO))) {
					AmendParameters amendParameters = new AmendParameters(omsAmend.getDealtAmt(), omsAmend.getOrderPrice());
					order.setState(OMSState.AMEND_REQUESTED.getOrderStatusType().code);
					order.setAmendParameters(amendParameters);
					ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

					if (errorMsg != null) {
						log.warn(OMSConstants.OMS_LOG_PREFIX
								+ "onAmendOrder: Failed to update order during amend request "
								+ omsAmend);
						amendResp
								.setFailureReason(OMSConstants.INTERNAL_SERVER_ERROR);
						amendResp.setFailed(true);
					} else {
						OMSResponse omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.PASS_AMEND_RECEIVED, null,
								OMSState.AMEND_REQUESTED, null);
						OMSNotifier.notifyAllClients(order.getNamespaceName(),omsResponse);
						OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(),
								omsAmend.getUser(), omsAmend.getFromOMSOrg()));
						amendRequested = true;
					}
				} else{
					amendResp.setAmendBeforeAccept(true);
					if(omsAmend.getDealtAmt() != null) {
						order.setDealtAmt(omsAmend.getDealtAmt());
						amendResp.setDealtAmt(omsAmend.getDealtAmt());
					}
					if(omsAmend.getOrderPrice() != null) {
						order.setPrice(omsAmend.getOrderPrice());
						amendResp.setOrderPrice(omsAmend.getOrderPrice());
					}
					AmendParameters amendParameters = new AmendParameters(omsAmend.getDealtAmt(), omsAmend.getOrderPrice());
					order.setAmendParameters(amendParameters);
					ErrorMessage errorMsg = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());

					if ( errorMsg != null ) {
						log.warn(OMSConstants.OMS_LOG_PREFIX + "onAmendOrder: Failed to update order during amend request " + omsAmend);
						amendResp.setFailureReason(OMSConstants.INTERNAL_SERVER_ERROR);
						amendResp.setFailed(true);
					}
					else {
						OMSResponse omsResponse;
						if (order.getFixingReference() != null) {
							omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.UPDATED, null, OMSState.ACTIVE, null);
						}else{
							omsResponse = OMSResponse.getOMSResponse(order, OMSEvent.UPDATED, null, OMSState.RECEIVED, null);
						}
						OMSNotifier.notifyAllClients(order.getNamespaceName(), omsResponse);
						OMSAuditService.audit(OMSUtil.getOMSEvent(omsResponse, order.getNamespaceName(), omsAmend.getUser(), omsAmend.getFromOMSOrg()));
					}
				}
			}
		}

		String errorMsgStr = sendMessage(amendResp);
		if ( errorMsgStr != null ) {
			log.warn(OMSConstants.OMS_LOG_PREFIX + " onAmendOrder: Failed to send amend response" + omsAmend);
		}
		if (amendRequested && order.getType().equals(OrderRequest.Type.FIXING)) {
			long timeInMin = clientconfMBean.getFixingOrderAmendCancelThresholdTime(omsAmend.getToOMSOrg());
			if (order.getFixingTime() - System.currentTimeMillis() < (timeInMin * 60 * 1000)) {
				log.info(OMSConstants.OMS_LOG_PREFIX + "onAmendOrder: Rejecting amend as amend is not allowed near to fixing time. order: "
						+ omsAmend + ", fixingTime: " + order.getFixingTime() + ", Amend Threshold in min: " + timeInMin);
				amendReject(order);
			}
		}
	}

	private static String sendMessage( OMSMessage message )
	{
		try {
			if ( OMSConfig.getInstance().getOmsOrgs().contains(message.getToOMSOrg()) ) {
				if ( !OMSNotificationProcessor.getInstance().notify(message) ) {
					return OMSConstants.FAILED_TO_SEND_ORDER;
				}
			}
			else {
				if ( message.isEmsOrder() ) {
					OMSServerConnectionManager.getInstance().getVirtualServerConnector().sendMessage(message);
				}
				else {
					OMSServerConnectionManager.getInstance().getConnector().sendMessage(message);
				}
			}
		}
		catch ( OMSServerException omsE ) {
			log.error(OMSConstants.OMS_LOG_PREFIX + "sendMessage: Error while sending message " + omsE.getMessage());
			return omsE.getMessage();
		}
		catch ( MessageCommunicationExceptionC mce ) {
			log.error(OMSConstants.OMS_LOG_PREFIX + "sendMessage: Message Communication error", mce);
			return OMSConstants.FAILED_TO_SEND_ORDER;
		}
		catch ( Exception e ) {
			log.error(OMSConstants.OMS_LOG_PREFIX + "sendMessage: Error while send message ", e);
			return OMSConstants.INTERNAL_SERVER_ERROR;
		}
		return null;
	}

	public static OMSMessage populateResponse( OMSMessage request, OMSMessage response )
	{
		response.setClientOrderId(request.getClientOrderId());
		response.setFromOMSOrg(request.getToOMSOrg());
		response.setToOMSOrg(request.getFromOMSOrg());
		response.setOrderId(request.getOrderId());
		response.setUser(request.getUser());
		response.setEmsOrder(request.isEmsOrder());
		return response;
	}
	
	private static Order getOrderFromMessage( OMSMessage omsMessage )
	{
		Organization org = ReferenceDataCacheC.getInstance().getOrganization(omsMessage.getToOMSOrg());
		return OrderStagingServiceFactory.getOrderStagingService().getOrderByClOrdId(omsMessage.getClientOrderId(),
				org.getDefaultDealingUser(), null);
	}

	private static Order getPrimaryServerOrderFromMessage( OMSMessage omsMessage )
	{
		Organization org = ReferenceDataCacheC.getInstance().getOrganization(omsMessage.getToOMSOrg());
		return OrderStagingServiceFactory.getOrderStagingService().getOrderByClOrdIdFromPrimaryServer(omsMessage.getClientOrderId(),
				org.getDefaultDealingUser(), null);
	}
}
