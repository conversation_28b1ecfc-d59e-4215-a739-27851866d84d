package com.integral.staging;

import java.util.HashMap;

public enum ErrorCode
{

	ORDER_NOT_FOUND(1), MULTIPLE_ORDERS_FOUND(2), NULL_ORDER_NOT_ALLOWED(3), USER_REQUIRED_FOR_SAVE(4), CLOID_CANNOT_BE_NULL(5), ORDER_EXISTS_ALREADY(6), 
	INVALID_CURRENCYPAIR(7), INVALID_VALUE_DATE(8) , INVALID_VALUE_DATE_EARLIER_THAN_TRADEDATE(9) , INTERNAL_SERVER_ERROR(10) , NON_SPOT_VALUE_DATE(11) ,
	NO_ACTIVE_USER(12) , INVALID_CC(13), INVALID_ORDER_SUBMISSION_TIME(14) , INVALID_ACCOUNT(15) , INACTIVE_ACCOUNT(16) , INACTIVE_CC(17),
	INVALID_CC_RELATIONSHIP(18) , INVALID_CC_ACCOUNT(19) , NO_CC_PROVIDERS(20) , INVALID_ACCOUNT_FOR_GIVEN_CC(21) , NO_PERM_TO_PLACE_ONBEHALF_OF_TRADING_USER(22),
	INVALID_DEALT_AMOUNT(23) , INVALID_DEALT_CCY(24) , CLOID_CANNOT_BE_BLANK(25) , CLOID_EXCEEDS_MAX_LENGTH_LIMITS(26), INVALID_ERISA_FUND_SETUP(27), 
	NO_EXECUTION_BATCH_SCHEDULED(28), CC_VALIDATION_FAILED(29), INVALID_40ACT_FUND_SETUP(30) ;

	int code;

	ErrorCode( int c )
	{
		this.code = c;
	}

	public int getCode()
	{
		return this.code;
	}

	private static HashMap<Integer, ErrorCode> codeToEnum = new HashMap<Integer, ErrorCode>();
	static
	{
		for ( ErrorCode e : ErrorCode.values() )
			codeToEnum.put(e.getCode(), e);
	}

	public static ErrorCode fromCode( int i )
	{
		return codeToEnum.get(i);
	}

}
