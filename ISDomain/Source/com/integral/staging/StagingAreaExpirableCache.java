package com.integral.staging;


import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;

import static java.util.concurrent.TimeUnit.MILLISECONDS;

/**
 * Created by bhaskars on 5/7/14.
 */
public class StagingAreaExpirableCache
{
	private com.google.common.cache.Cache<String,Order> objectCache;
    private OrderCacheRemovalListener<Order> listener;
    private boolean cacheDisabled = false;
    private Log log = LogFactory.getLog( StagingAreaExpirableCache.class );

    static ArrayList<Integer> ignoreCacheStates =  new ArrayList<Integer>(Arrays.asList(	OrderStatusType.CANCELLED.code,
																				        	OrderStatusType.ARCHIVED.code, 
																				        	OrderStatusType.DELETED.code, 
																				        	OrderStatusType.EXPIRED.code)); 
    
    public StagingAreaExpirableCache(int cacheSize,long expirationTimeInMS,int concurrencyLevel,boolean cacheEnabled) {
        log.info("Configuring Cache for StagingArea Order ");
        CacheBuilder cb = CacheBuilder.newBuilder();
        listener = new OrderCacheRemovalListener<Order>();
        cb.removalListener(listener);
        cb.concurrencyLevel(concurrencyLevel);
        cb.maximumSize(cacheSize);
        cb.expireAfterAccess(expirationTimeInMS, MILLISECONDS);
        cb.expireAfterWrite(expirationTimeInMS, MILLISECONDS);
        objectCache = cb.build();
        this.cacheDisabled = !cacheEnabled;
    }

    public static boolean isCachingDisabled(Order order) {
    	return ignoreCacheStates.contains(order.getState());
    }
    /**
     * Returns the key to the cache based on the given namespace and id.
     * NB. The id is guaranteed to be unique within the namespace only.
     *
     * @param namespace of the Order
     * @param id of the Order
     * @return key to the cache
     */
    private String getKey(final String namespace, final String id) {
        return new StringBuilder().append( namespace ).append( "." ).append(id).toString();
    }

    private String getKey(Order order) {
        return  getKey(order.getNamespaceName(),order.getId())  ;
    }


    /**
     * Query spaces to find the Order object.
     * This does a query on primary
     *
     * @param nameSpaceName of the Order object
     * @param id of the Order
     * @return Order found on success or null upon failure
     */
    private Order queryOrder(String nameSpaceName,String id) {
        Order order = StagingAreaPersistenceService.queryOrderById( id, nameSpaceName );
        if(order != null && !isCachingDisabled(order))
            add(order);
        else{
            log.info("Order not found in database : queryOrder(nameSpaceName=" + nameSpaceName + " ,id=" + id + " )");
        }
        return order;
    }
    
    private Order queryOrder(String nameSpaceName, String id, boolean queryPrimaryServer) {
        Order order = StagingAreaPersistenceService.queryOrderById( id, nameSpaceName, queryPrimaryServer);
        if(order != null && !isCachingDisabled(order))
            add(order);
        else{
            log.info("Order not found in database : queryOrder(nameSpaceName=" + nameSpaceName + " ,id=" + id + " )");
        }
        return order;
    }


    /**
     * Adds the object to the cache by associating the object in the cache with its derived key.
     * If the key already exists in the cache the new object will replace the old one.
     *
     * @param order object to be associated in the cache
     */
    public void add(Order order) {
        if(order == null){
            log.info("add: received null Order");
            return;
        }
        if (cacheDisabled)
            return;

        String key = getKey(order);
        add(key, order);
        log.debug("Added order to cache. order=" + order);
    }

    /**
     *
     * This always queries from Primary
     * @param id
     * @param namespaceName
     * @return
     */
    public Order get(String id,String namespaceName){
        Order order;
        if( cacheDisabled ){
            order = queryOrder( namespaceName, id );
        }
        else{
            String key = getKey(namespaceName,id);
            order = get(key);
            if (order == null) {
                log.info("Cache miss, key=" + key + " - querying database");
                order = queryOrder( namespaceName, id );
            }
            else{
                log.info("Cache hit, key=" + key);
            }
        }
        return order;
    }

    public Order get(String id, String namespaceName, boolean queryPrimaryServer){
        Order order;
        if( cacheDisabled ){
            order = queryOrder( namespaceName, id, queryPrimaryServer );
        }
        else{
            String key = getKey(namespaceName,id);
            order = get(key);
            if (order == null) {
                log.info("Cache miss, key=" + key + " - querying database");
                order = queryOrder( namespaceName, id, queryPrimaryServer );
            }
            else{
                log.info("Cache hit, key=" + key);
            }
        }
        return order;
    }

    public Order compareQueriedOrderWithCache(Order order){
        if( cacheDisabled || null==order || isCachingDisabled(order)){
            return order;
        }else{
        	
            String key = getKey(order);
            Order cachedOrder = get(key);
            if(null == cachedOrder){
                add(order);
                return order;
            }else{
                if(order.getVersionId() > cachedOrder.getVersionId()){
                    add(order);
                    return order;
                }
                return cachedOrder;
            }
        }
    }

    private void add(String key,Order object){
        objectCache.put(key,object);
    }

    private Order get(String key){
        return objectCache.getIfPresent(key);
    }

    public long size(){
        return objectCache.size();
    }

    public void clear(){
        log.info( "GOT A REQUEST to CLEAR THE CACHE. Current Element count: " + objectCache.size() );
        objectCache.invalidateAll();
    }

    public Map<String, Order> getAllCachedEntities()
    {
        return objectCache.asMap();
    }

    private class OrderCacheRemovalListener<Order> implements RemovalListener {
        private Log listenerLogger = LogFactory.getLog(OrderCacheRemovalListener.class);

        public OrderCacheRemovalListener(){

        }

        @Override
        public void onRemoval(RemovalNotification notification) {
            if(listenerLogger.isDebugEnabled()){
                StringBuilder sb = new StringBuilder("Order cache removal of key=").append(notification.getKey())
                        .append(" ,value=").append(notification.getValue().toString());
                if(notification.getCause() != null){
                    sb.append(" , cause=").append(notification.getCause().name());
                }
                listenerLogger.debug(sb.toString());
            }
        }
    }

}
