/**
 * <AUTHOR>
 */
package com.integral.staging.config;

import java.util.HashMap;
import java.util.Map;

import com.integral.system.configuration.IdcMBeanC;;

/**
 * <AUTHOR>
 *
 */
public class StagingServiceConfig extends IdcMBeanC implements StagingServiceConfigMBean
{

	static StagingServiceConfigMBean instance = createInstanceAndLoadProperties();
	boolean isEnabled;
	private Map<String, Boolean> stagingServiceEnabledMap = new HashMap<String, Boolean>();
    private Map<String, String> firstOrderNotificationEmailMap = new HashMap<String, String>();
    private Map<String, String> orderSummaryNotificationEmailMap = new HashMap<String, String>();
    int maxStagingOrderListSizePerOrg;
    boolean isStagingEODOrderExpiryEnabled;
    int purgingKeepPeriod;
    private Map<String, Boolean> senderSubIDAsAccountMap;
    private boolean senderSubIDAsAccount;
    private long timeToLiveInCacheForTerminalStateOrder;
    private String eligibleOrgsForEODOrderExpiry;
    private boolean sendCCWithExecReport;
    private Map<String, Boolean> sendCCWithExecReportMap;
    private int sendBMRateWithExecReport;
    private Map<String, Integer> sendBMRateWithExecReportMap;
    private boolean validateCCYPair;
    private Map<String, Boolean> validateCCYPairMap;
    private boolean sendExpiredExecReport;
    private Map<String, Boolean> sendExpiredExecReportMap;
    private Map<String, Boolean> sendAccountMap;
    private boolean sendAccount;
    private boolean multipleAllocationsSupported;
    private Map<String, Boolean> multipleAllocationsSupportedMap;
    private boolean useEmailAddressForUser;
    private Map<String, Boolean> useEmailAddressForUserMap;
    private boolean unlockNotExecutedOrder;
    private Map<String, Boolean> unlockNotExecutedOrderMap;
    /**
	 * 
	 */
	private StagingServiceConfig()
	{
		super("com.integral.staging.config.StagingServiceConfig");
	}

	/**
	 * Returns a singleton instance.
	 *
	 * @return a singleton instance of <code>OrderConfigurationMBean</code>
	 */
	public static StagingServiceConfigMBean getInstance()
	{
		return instance;
	}

    /**
     * Creates an instance of OrderServiceMBeanC and loads all properties.
     *
     * @return an instance of OrderServiceMBeanC with all properties loaded.
     */
    private static StagingServiceConfig createInstanceAndLoadProperties()
    {
        final StagingServiceConfig result = new StagingServiceConfig();
        result.loadProperties();
        return result;
    }


    private void loadProperties()
    {
        isEnabled = getBooleanProperty(IDC_SS_ENABLED, false);
        isStagingEODOrderExpiryEnabled = getBooleanProperty(IDC_SS_ORDER_MARK_ENDOFDAY_EXPIRY_ENABLED, false);
        stagingServiceEnabledMap = initSingleSuffixBooleanPropertyMap( IDC_SS_ENABLED_PREFIX, null );
        firstOrderNotificationEmailMap = initSingleSuffixStringPropertyMap( IDC_SS_FIRST_ORDER_EMAIL_PREFIX, null );
        orderSummaryNotificationEmailMap = initSingleSuffixStringPropertyMap( IDC_SS_ORDER_SUMMARY_EMAIL_PREFIX, null );
        purgingKeepPeriod = getIntProperty( IDC_SS_PURGE_KEEP_PERIOD , 30 );
        maxStagingOrderListSizePerOrg = getIntProperty( IDC_SS_PURGE_KEEP_PERIOD , 1000 );
        senderSubIDAsAccountMap = initSingleSuffixBooleanPropertyMap( IDC_SS_ORDER_SENDER_SUB_ID_AS_ACCOUNT_PREFIX, null );
        senderSubIDAsAccount = getBooleanProperty(IDC_SS_ORDER_SENDER_SUB_ID_AS_ACCOUNT, false);
        timeToLiveInCacheForTerminalStateOrder = getLongProperty(IDC_SS_TIME_TO_LIVE_IN_CAHCE_FOR_TERMINAL_STATE_ORDER, 1800000);
        eligibleOrgsForEODOrderExpiry = getStringProperty(IDC_SS_EOD_EXPIRE_ORDERS_ORGS, null);
        sendCCWithExecReport = getBooleanProperty(IDC_SS_SEND_CC_WITH_EXECUTION_REPORT, false);
        sendCCWithExecReportMap = initSingleSuffixBooleanPropertyMap( IDC_SS_SEND_CC_WITH_EXECUTION_REPORT_PREFIX, null);
        sendBMRateWithExecReport = getIntProperty(IDC_SS_SEND_BM_RATE_WITH_EXECUTION_REPORT, -1);
        sendBMRateWithExecReportMap = initSingleSuffixIntegerPropertyMap( IDC_SS_SEND_BM_RATE_WITH_EXECUTION_REPORT_PREFIX, null);
        validateCCYPair = getBooleanProperty(IDC_SS_VALIDATE_CCYPAIR, false);
        validateCCYPairMap = initSingleSuffixBooleanPropertyMap( IDC_SS_VALIDATE_CCYPAIR_PREFIX, null);
        sendExpiredExecReport = getBooleanProperty(IDC_SS_SEND_EXPIRED_EXECUTION_REPORT, false);
        sendExpiredExecReportMap = initSingleSuffixBooleanPropertyMap( IDC_SS_SEND_EXPIRED_EXECUTION_REPORT_PREFIX, null);
        sendAccount = getBooleanProperty( IDC_SS_SEND_ACCOUNTTAG_WITH_EXECUTIONREPORT, true );
        sendAccountMap = initSingleSuffixBooleanPropertyMap( IDC_SS_SEND_ACCOUNTTAG_WITH_EXECUTIONREPORT_PREFIX, null );
        multipleAllocationsSupported = getBooleanProperty( IDC_SS_ORDER_MULTIPLE_ALLOCATIONS_SUPPORTED, false );
        multipleAllocationsSupportedMap = initSingleSuffixBooleanPropertyMap( IDC_SS_ORDER_MULTIPLE_ALLOCATIONS_SUPPORTED_PREFIX, null );
        useEmailAddressForUser = getBooleanProperty( IDC_SS_ORDER_USE_EMAIL_ADDRESS_FOR_USER, false );
        useEmailAddressForUserMap = initSingleSuffixBooleanPropertyMap( IDC_SS_ORDER_USE_EMAIL_ADDRESS_FOR_USER_PREFIX, null );
        unlockNotExecutedOrder = getBooleanProperty( IDC_SS_ORDER_UNLOCK_NOT_EXECUTED, false );
        unlockNotExecutedOrderMap = initSingleSuffixBooleanPropertyMap( IDC_SS_ORDER_UNLOCK_NOT_EXECUTED_PREFIX, null );
    }

        @Override
	public void initialize()
	{
		super.initialize();
        loadProperties();
	}
        
    /* (non-Javadoc)
	 * @see com.integral.staging.config.StagingServiceConfigMBean#isStagingEODOrderExpiryEnabled()
	 */
	@Override
	public boolean isStagingEODOrderExpiryEnabled()
	{
		return isStagingEODOrderExpiryEnabled;
	}


	/* (non-Javadoc)
	 * @see com.integral.staging.config.StagingServiceConfigMBean#isEnabled(java.lang.String)
	 */
	@Override
	public boolean isEnabled( String org )
	{
		Boolean val = stagingServiceEnabledMap.get(org);
		return val != null ? val : isEnabled;
	}

    @Override
    public String getFirstOrderNotificationEmailAddress(String orgName)
    {
        if(orgName == null){
            return null;
        }
        else{
            return firstOrderNotificationEmailMap.get( orgName );
        }
    }

    @Override
    public String getOrderSummaryNotificationEmailAddress(String orgName)
    {
        if(orgName == null){
            return null;
        }
        else{
            return orderSummaryNotificationEmailMap.get( orgName );
        }
    }

    @Override
    public int getStagingAreaPurgeKeepPeriod()
    {
        return purgingKeepPeriod;
    }

	@Override
	public boolean isSenderSubIDAsAccount(String orgName) 
	{
		Boolean val = senderSubIDAsAccountMap.get(orgName);
		return val != null ? val : senderSubIDAsAccount;
	}

	@Override
	public long getTimeToLiveInCacheForTerminalStateOrder() 
	{		
		return this.timeToLiveInCacheForTerminalStateOrder;
	}

	@Override
	public String getEligibleOrgsForEODOrderExpiry() 
	{		
		return this.eligibleOrgsForEODOrderExpiry;
	}

	@Override
	public boolean sendCCWithExecutionReport(String orgName) 
	{
		Boolean val = sendCCWithExecReportMap.get(orgName);
		return val != null ? val : sendCCWithExecReport;
	}

	@Override
	public int sendBMRateWithExecutionReport(String orgName) 
	{
		Integer val = sendBMRateWithExecReportMap.get(orgName);
		return val != null ? val : sendBMRateWithExecReport;
	}

    @Override
    public boolean validateCurrencyPair(String orgName)
    {
        Boolean val = validateCCYPairMap.get(orgName);
        return val != null ? val : validateCCYPair;
    }

    @Override
    public boolean sendExpiredExecutionReport(String orgName)
    {
        Boolean val = sendExpiredExecReportMap.get(orgName);
        return val != null ? val : sendExpiredExecReport;
    }

    @Override
    public boolean sendAccount( String orgName )
    {
        Boolean val = sendAccountMap.get(orgName);
        return val != null ? val : sendAccount;
    }

    @Override
    public boolean isMultipleAllocationsSupported( String orgName )
    {
        Boolean val = multipleAllocationsSupportedMap.get(orgName);
        return val != null ? val : multipleAllocationsSupported;
    }

	@Override
	public boolean useEmailAddressForUser(String orgName) 
	{
	       Boolean val = useEmailAddressForUserMap.get(orgName);
	       return val != null ? val : useEmailAddressForUser;
	}

	@Override
	public boolean unlockNotExecutedOrder(String orgName) 
	{
	       Boolean val = unlockNotExecutedOrderMap.get(orgName);
	       return val != null ? val : unlockNotExecutedOrder;
	}
    
    
}
