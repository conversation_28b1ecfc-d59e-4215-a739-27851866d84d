/**
 * <AUTHOR>
 */
package com.integral.staging.config;

import com.integral.system.configuration.IdcMBean;
/**
 * <AUTHOR>
 *
 */
public interface StagingServiceConfigMBean extends IdcMBean
{
	public String IDC_SS_ENABLED = "Idc.Staging.Service.Enabled";
    public String IDC_SS_ENABLED_PREFIX = "Idc.Staging.Service.Enabled.";
    public String IDC_SS_FIRST_ORDER_EMAIL_PREFIX = "Idc.Staging.Service.FirstOrder.EmailAddress.";
    public String IDC_SS_ORDER_SUMMARY_EMAIL_PREFIX = "Idc.Staging.Service.Orders.Summary.EmailAddress.";
    public String IDC_SS_ORDER_MARK_ENDOFDAY_EXPIRY_ENABLED = "Idc.Staging.Service.EOD.ExpireOrders.Enabled";
    public String IDC_SS_PURGE_KEEP_PERIOD = "Idc.Staging.Service.PURGE.KEEP.PERIOD";
    public String IDC_SS_ORDER_SENDER_SUB_ID_AS_ACCOUNT = "Idc.Staging.Service.Order.SenderSubID.As.Account";
    public String IDC_SS_ORDER_SENDER_SUB_ID_AS_ACCOUNT_PREFIX = "Idc.Staging.Service.Order.SenderSubID.As.Account.";
    public String IDC_SS_TIME_TO_LIVE_IN_CAHCE_FOR_TERMINAL_STATE_ORDER = "Idc.Staging.Service.TimeToLive.In.Cache.For.Terminal.State.Order";
    public String IDC_SS_EOD_EXPIRE_ORDERS_ORGS = "Idc.Staging.Service.EOD.Expire.Orders.Orgs";    
    public String IDC_SS_SEND_CC_WITH_EXECUTION_REPORT = "Idc.Staging.Service.Send.CC.With.ExecutionReport";
    public String IDC_SS_SEND_CC_WITH_EXECUTION_REPORT_PREFIX = IDC_SS_SEND_CC_WITH_EXECUTION_REPORT + ".";
    public String IDC_SS_SEND_BM_RATE_WITH_EXECUTION_REPORT = "Idc.Staging.Service.Send.BenchMarkRate.With.ExecutionReport";
    public String IDC_SS_SEND_BM_RATE_WITH_EXECUTION_REPORT_PREFIX = IDC_SS_SEND_BM_RATE_WITH_EXECUTION_REPORT + ".";
    public String IDC_SS_VALIDATE_CCYPAIR = "Idc.Staging.Service.Validate.CurrencyPair";
    public String IDC_SS_VALIDATE_CCYPAIR_PREFIX = IDC_SS_VALIDATE_CCYPAIR + ".";
    public String IDC_SS_SEND_EXPIRED_EXECUTION_REPORT = "Idc.Staging.Service.Send.Expired.ExecutionReport";
    public String IDC_SS_SEND_EXPIRED_EXECUTION_REPORT_PREFIX = IDC_SS_SEND_EXPIRED_EXECUTION_REPORT + ".";
    public String IDC_SS_SEND_ACCOUNTTAG_WITH_EXECUTIONREPORT = "Idc.Staging.Service.Send.AccountTag.With.ExecutionReport";
    public String IDC_SS_SEND_ACCOUNTTAG_WITH_EXECUTIONREPORT_PREFIX = IDC_SS_SEND_ACCOUNTTAG_WITH_EXECUTIONREPORT + ".";
    public String IDC_SS_ORDER_MULTIPLE_ALLOCATIONS_SUPPORTED = "Idc.Staging.Service.Order.Multiple.Allocations.Supported";
    public String IDC_SS_ORDER_MULTIPLE_ALLOCATIONS_SUPPORTED_PREFIX = IDC_SS_ORDER_MULTIPLE_ALLOCATIONS_SUPPORTED + ".";
    public String IDC_SS_ORDER_USE_EMAIL_ADDRESS_FOR_USER = "Idc.Staging.Service.Order.Use.Email.Address.For.User";
    public String IDC_SS_ORDER_USE_EMAIL_ADDRESS_FOR_USER_PREFIX = IDC_SS_ORDER_USE_EMAIL_ADDRESS_FOR_USER + ".";
    public String IDC_SS_ORDER_UNLOCK_NOT_EXECUTED = "Idc.Staging.Service.Order.Unlock.Not.Executed";
    public String IDC_SS_ORDER_UNLOCK_NOT_EXECUTED_PREFIX = IDC_SS_ORDER_UNLOCK_NOT_EXECUTED + ".";
    
    boolean isStagingEODOrderExpiryEnabled();
    
	boolean isEnabled( String org );

    String getFirstOrderNotificationEmailAddress(String orgName);

    String getOrderSummaryNotificationEmailAddress(String orgName);

    int getStagingAreaPurgeKeepPeriod();
    
    boolean isSenderSubIDAsAccount(String orgName);
    
    long getTimeToLiveInCacheForTerminalStateOrder();
    
    String getEligibleOrgsForEODOrderExpiry();
    
    boolean sendCCWithExecutionReport(String orgName);
    
    int sendBMRateWithExecutionReport(String orgName);

    boolean validateCurrencyPair(String orgName);

    boolean sendExpiredExecutionReport(String orgName);

    boolean sendAccount( String orgName);

    boolean isMultipleAllocationsSupported( String name );
    
    boolean useEmailAddressForUser(String orgName);
    
    /**
     * Unlock order and put into staging area for re-import if property is true
     * @param orgName
     * @return
     */
    boolean unlockNotExecutedOrder(String orgName);
}
