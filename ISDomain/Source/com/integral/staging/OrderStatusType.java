package com.integral.staging;

import java.util.HashMap;

public enum OrderStatusType
{
	/**
	 * Initial - Newly submitted order will in this state. (Draft for OMS)
	 * InUse - Order selected for execution will be in this state (Active for OMS)
	 *         Order can move back to Initial state if given up by Execution service (e.g. RMM )
	 * Cancelled - Order cancelled before fill will be in this state.
	 * Executed - completed partial/completely filled will be in this state; in OMS after trade created and customer notified
	 * Rejected - order selected for execution but failed after execution will be in rejected. These orders cant be executed again.
     * Deleted - With spaces we can not delete object directly but a TTL can be set on object which is used by persistence mechanism to delete.
     * Maintaining state as DELETED When the order is marked for delete
	 * MATCHED - Used for OMS, after price match
	 * ARCHIVED - Used in OMS broker marked this order as archived, may be deleted later
	 * TRIGGERED - This state is reached when active resting order reached its trigger rate
	 * AUTO - This state is when order is handed over to EMS
	 * Downloaded - Orders Downloaded out by CSV or sent to order org
	 * Imported - Orders Imported from CSV or order which are received from other 
	 * Accepted - Orders Accepted by receiving og
	 * Withdrawn - Orders Withdrawn by Sender
	 * CANCEL_REQUESTED - Order requested for cancellation by sender 
	 */
	INTIAL(1), INUSE(2), CANCELLED(3), EXECUTED(4), REJECTED(5) ,
	DELETED(6) , EXPIRED(7), DOWNLOADED(8), MATCHED(9), TRIGGERED(10) ,
	AUTO(11) , IMPORTED(12) , ARCHIVED(13) , ACCEPTED(14) , WITHDRAWN(15), CANCEL_REQUESTED(16), AMEND_REQUESTED(17), CREDIT_FAIL(18);

	OrderStatusType( int c )
	{
		this.code = c;
	}

	public int code;

	public int getCode()
	{
		return code;
	}

	public static OrderStatusType fromCode( int code )
	{
		return codeToEnum.get(code);
	}

	private static HashMap<Integer, OrderStatusType> codeToEnum = new HashMap<Integer, OrderStatusType>();
	static
	{
		for ( OrderStatusType e : OrderStatusType.values() )
			codeToEnum.put(e.getCode(), e);
	}
}
