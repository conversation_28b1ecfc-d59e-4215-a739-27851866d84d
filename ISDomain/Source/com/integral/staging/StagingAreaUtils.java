package com.integral.staging;

import java.text.DateFormat;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.log.MessageLogger;
import com.integral.message.ErrorMessage;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import org.joda.time.LocalTime;

import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.trade.Tenor;
import com.integral.fxbenchmark.FXBenchMarkRateProvider;
import com.integral.fxbenchmark.FXBenchMarkService;
import com.integral.is.common.CustomThreadFactory;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.netting.NettingMBeanC;
import com.integral.staging.config.StagingServiceConfig;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.ConvertUtils;

/**
 * Utility class for Staging Area
 * <AUTHOR>
 *
 */
public class StagingAreaUtils {	
	protected static Log log = LogFactory.getLog(StagingAreaUtils.class);
	private static final ScheduledExecutorService resourceManagementService = 
			Executors.newScheduledThreadPool(1, new CustomThreadFactory("ResourceManagementService-"));
	public static final int ORDER_STAGE_SUBMISSION = 1;
	public static final int ORDER_STAGE_IMPORT = 2;	
    private static Date ttlDate = null;
    
    
    public static ScheduledExecutorService getResourceManagementService()
    {
    	return resourceManagementService;
    }
	
	/**
	 * Method to return value date
	 * @param validationErrors 
	 * @param futSetDateStr
	 * @param org
	 * @param ccyPair
	 * @param dateformat
	 * @return valueDate
	 */
	public static IdcDate getValueDateFromDateString(StringBuilder validationErrors, String futSetDateStr, Organization org, String ccyPair, DateFormat dateformat)
	{
		IdcDate valueDate = null;
		
		FXRateConvention fxRateConvention = QuoteConventionUtilC.getInstance().getFXRateConvention(org);
		FXRateBasis rateBasis = null;
		
		if(fxRateConvention != null && ccyPair != null)
		{
			rateBasis = fxRateConvention.getFXRateBasis(ccyPair);
			valueDate = convertDate(futSetDateStr, rateBasis, dateformat);
		}
		
		if(rateBasis != null && validationErrors != null && valueDate != null )
		{
            IdcDate todDate = rateBasis.getValueDate( EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate() , Tenor.TODAY_TENOR );
            if( valueDate.isEarlierThan(todDate) )
            {
    			String valDateStr = null;
    			if ( dateformat == null )
    			{
    				valDateStr = new StringBuilder( 20 ).append( valueDate.getDayOfMonth() ).append( "/" ).append( valueDate.getMonth() ).append( "/" ).append( valueDate.getYear() ).toString();
    			}
    			else{
    				valDateStr = dateformat.format( valueDate.asJdkDate() );
    			}
            	validationErrors.append("Value Date - ").append( valDateStr ).append(" for CCYPair - ").append(ccyPair).append(" is earlier than TOD. ");
            }
			    
		}		
		return valueDate;
	}
	
	/**
	 * Helper class to convert a given date String into IdcDaye using Dateformat and FxRateBasis
	 * @param dateStr
	 * @param rb
	 * @param format
	 * @return valueDate
	 */
	private static IdcDate convertDate( String dateStr, FXRateBasis rb, DateFormat format )
    {
        //do value date validation
        Tenor tenor = null;
        if ( dateStr == null || dateStr.trim().length() == 0 )
        {
            if ( NettingMBeanC.getInstance().isTenorSupported() )
            {

                tenor = Tenor.SPOT_TENOR;
            }
            else
            {
                //since dateString is null and tenors are not supported don't allow blank values as SPOT dates
                return null;
            }
        }
        else
        {
            try
            {
                tenor = new Tenor( dateStr.trim() );
            }
            catch ( Exception e )
            {
                tenor = null;
            }
        }

        if ( tenor == null )
        {
            // it's not a tenor so try date
            try
            {
                return ( IdcDate ) ConvertUtils.convert
                        ( dateStr, IdcDate.class, format );

            }
            catch ( Exception e )
            {
                return null;
            }
        }
        else
        {
            //if it's tenor check if it's supported
            if ( NettingMBeanC.getInstance().isTenorSupported() )
            {
                IdcDate current = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
                if ( rb != null )
                {
                    return rb.getValueDate( current, tenor );
                }
                else
                {
                    log.info( "StagingAreaUtils.convertDate - Rate basis is null, can't convert tenor into date. check currency pair. rb=" + rb );
                    return null;
                }
            }
            else
            {
                log.info( "StagingAreaUtils.convertDate - Tenors not allowed. Please check property - IDC_NETTING_PORTFOLIO_TENOR_SUPPORTED." );
                return null;
            }
        }
    }

    public static Date getTTLDate( Date date )
    {
        if(ttlDate != null && ttlDate.after( date ) )
        {
            return ttlDate;
        }
        else
        {
            Date ttl = null;
            int keepPeriod = StagingServiceConfig.getInstance().getStagingAreaPurgeKeepPeriod();
            if ( keepPeriod != 0 && date != null )
            {
                Calendar expiry_date = Calendar.getInstance( TimeZone.getTimeZone( "UTC" ) );
                expiry_date.setTime( date );
                expiry_date.add( Calendar.DATE, keepPeriod );

                // expiry time is set to - SAT 04:00:00 AM (UTC).
                Calendar estimated_date = ( Calendar ) expiry_date.clone();
                expiry_date.set( Calendar.DAY_OF_WEEK, Calendar.SATURDAY );
                expiry_date.set( Calendar.HOUR_OF_DAY, 4 );
                expiry_date.set( Calendar.MINUTE, 0 );
                expiry_date.set( Calendar.SECOND, 0 );
                expiry_date.set( Calendar.MILLISECOND, 0 );
                if ( estimated_date.after( expiry_date ) )
                {
                    expiry_date.add( Calendar.DATE, 7 );
                }
                ttl = expiry_date.getTime();
            }
            ttlDate = ttl;
            return ttl;
        }
    }


    public static void populateBMRateOnOrderSubmission(final List<Order> orderList)
    {
    	populateBenchMarkRate(orderList, ORDER_STAGE_SUBMISSION);
    }
    
    public static void populateBMRateOnOrderImport(final List<Order> orderList)
    {
    	populateBenchMarkRate(orderList, ORDER_STAGE_IMPORT);
    }    
 
    private static void populateBenchMarkRate(final List<Order> orderList, int stage)
    {
    	if (orderList == null || orderList.size() == 0)
    	{
    		return;
    	}
    	FXBenchMarkRateProvider benchMarkRateProvider = FXBenchMarkService.getInstance().getFXBenchMarkRateProvider();
    	Map<String, Double> priceMap = new HashMap<String, Double>();
    	for (Order order : orderList)
    	{
    		String ccyPair = order.getCcyPair();
    		double mid = 0.0; 
    		// check if rate already queried before for this currency pair
    		Double previousRate = priceMap.get(ccyPair);
    		if (previousRate == null)
    		{
    		    CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString ( ccyPair );
                mid = benchMarkRateProvider.getCurrentMidRate( currencyPair );
    			log.info("StagingAreaUtils.populateBenchMarkRate():BenchMarkRate for Currency Pair:" + ccyPair + ":" + mid);
    			if (mid == 0.0)
    			{
    				log.warn("StagingAreaUtils.populateBenchMarkRate():BenchMarkRate Invalid for Currency Pair :" + ccyPair);    				
    			}
    			priceMap.put(ccyPair, mid); 
    		}
    		else
    		{
    			mid = previousRate.doubleValue();
    		}
    		if (ORDER_STAGE_SUBMISSION == stage)
    		{
    			order.setOrdSubmissionBMRate(mid);
    		}
    		else if (ORDER_STAGE_IMPORT == stage)
    		{
    			order.setOrdImportBMRate(mid);
    		}
      	}
    }

    public static void processAlertConditionOnOrderSubmission(final WorkflowMessage replyMsg, final String orderMsg)
    {
        if (replyMsg == null)
        {
            return;
        }
        Object source = replyMsg.getObject();
        if (MessageStatus.FAILURE.equals(replyMsg.getStatus()) && source instanceof Order)
        {
            Order order = (Order)source;
            String orderSource = order.getSource();
            if (StagingAreaConstants.STAGING_ORDER_SOURCE_FIX.equals(orderSource))
            {
                StringBuilder data = new StringBuilder(150);
                if (orderMsg != null && !"".equals(orderMsg.trim()))
                {
                    data.append(orderMsg);
                }
                else
                {
                    data.append("Organization:")
                            .append(order.getNamespaceName())
                            .append(":ClientOrderID:")
                            .append(order.getClientOrderId())
                            .append(":OrderID:")
                            .append(order.get_id())
                            .append(":CurrencyPair:")
                            .append(order.getCcyPair())
                            .append(":DealtCurrency:")
                            .append(order.getDealtCcy())
                            .append(":Account:")
                            .append(order.getAccount())
                            .append(":Quantity:")
                            .append(order.getDealtAmt())
                            .append(":Side:")
                            .append(order.isBuy() ? "BUY" : "SELL");
                }
                data.append(":Reason:");
                Collection<?> errorCollection = replyMsg.getErrors();
                if (errorCollection != null)
                {
                    Iterator<?> it = errorCollection.iterator();
                    if (it != null)
                    {
                        Object nextObj = it.next();
                        if (nextObj instanceof ErrorMessage)
                        {
                            ErrorMessage errorMessage = (ErrorMessage)nextObj;
                            String errorCode = errorMessage.getErrorCode();
                            data.append(errorCode);
                            data.append(",");
                        }
                    }
                }
                MessageLogger.getInstance().log(ISAlertMBean.ALERT_EVENT_STAGING_AREA_ORDER_SUBMISSION_FAILED,
                        "StagingAreaUtils.processAlertConditionOnOrderSubmission()", "Staging Area Order submission.", data.toString());
            }
        }
    }
 }
