package com.integral.staging;

import com.integral.netting.model.NettingTradeRequest;

public class SaTranslator
{
	public static Order ntrToOrder( NettingTradeRequest ntr )
	{
		Order o = new Order();
		o.setAccount(ntr.getFundName());
		o.setCcyPair(ntr.getCcyPair().getName());
		o.setClientOrderId(ntr.getSequenceNumber());
		o.setCreatedBusinessDate(System.currentTimeMillis());
		o.setCreatedTime(o.getCreatedBusinessDate());
		o.setDealtAmt(ntr.getBaseAmount() == null ? ntr.getTermAmt() : ntr.getBaseAmt());
		o.setDealtCcy(ntr.getDealtCcy().getName());
		//o.setDealtIsBuy(ntr.);
		o.setNamespace(ntr.getNettingPortfolio().getUser().getNamespace());
		o.setNamespaceName(ntr.getNettingPortfolio().getUser().getNamespace().getShortName());
		o.setValueDate(ntr.getValueDate());

		return o;
	}
}
