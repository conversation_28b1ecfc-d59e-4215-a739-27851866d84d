package com.integral.staging;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.netting.model.NettingTradeRequest;

import java.util.Comparator;

// Copyright (c) 2012 Integral Development Corp.  All rights reserved.

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.netting.model.NettingTradeRequest;

import java.util.Comparator;

/**
 * Comparator used for comparing the request pojos based on the sequence id and then with sequence number.
 *
 * <AUTHOR> Development Corp.
 */
public class StagingOrderComparator implements Comparator<Order>
{
    private static Log log = LogFactory.getLog( com.integral.staging.StagingOrderComparator.class );

    public int compare( Order rp1, Order rp2 )
    {
        try
        {

                if(rp1.getCreatedTime() > rp2.getCreatedTime())
                {
                    return -1;
                }
                else if(rp1.getCreatedTime() == rp2.getCreatedTime())
                {
                    if(rp1.get_id().compareTo(rp2.get_id())  > 0)
                    {
                        return -1;
                    }
                    else if(rp1.get_id().compareTo(rp2.get_id()) == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                }
                else return 1;
        }
        catch ( Exception e )
        {
            log.error( "compare : Exception while comparing orders. rp1=" + rp1 + ",rp2=" + rp2, e );
        }
        return -1;
    }
}
