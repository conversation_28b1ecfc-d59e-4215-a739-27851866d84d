package com.integral.staging;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.spaces.fx.persistence.ISSpacesPersistenceService;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.message.ErrorMessageC;
import com.integral.message.MessageEvent;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.netting.NettingMBeanC;
import com.integral.persistence.Namespace;
import com.integral.persistence.spaces.PersistenceConstants;
import com.integral.persistence.spaces.PersistenceServiceConfiguration;
import com.integral.spaces.ApplicationSpaceEvent;
import com.integral.spaces.Criteria;
import com.integral.spaces.Metaspace;
import com.integral.spaces.Metaspaces;
import com.integral.spaces.Query;
import com.integral.spaces.QueryBuilder;
import com.integral.spaces.QueryBuilder.Sort;
import com.integral.spaces.SpaceIterator;
import com.integral.spaces.serialize.DefaultSerializationView;
import com.integral.spaces.spi.CriteriaSet;
import com.integral.staging.model.UpdateOrderRequestList;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
@SuppressWarnings({ "rawtypes", "deprecation", "unchecked", "incomplete-switch" })

public class StagingAreaPersistenceService
{
	public static final String QUERY_CRITERIA_ID = "_id";
	public static final String QUERY_CRITERIA_COID = "coid";
	public static final String QUERY_CRITERIA_STT = "stt";
	public static final String QUERY_CRITERIA_CURRENCYPAIR = "cp";
	public static final String QUERY_CRITERIA_OBID = "obid";
	public static final String QUERY_CRITERIA_USER_NAME = "u"; 
	public static final String QUERY_CRITERIA_CREATION_TIME = "ctm";
	public static final String QUERY_CRITERIA_LOCKEDBY = "lckdBy";
	public static final String QUERY_CRITERIA_SOURCE = "src";
	public static final String QUERY_CRITERIA_OMS = "oms";
	public static final String QUERY_CRITERIA_ST = "st";
	public static final String QUERY_CRITERIA_VALUE_DATE = "vd";
	public static final String QUERY_CRITERIA_VALUE_EXTM = "extm";
	public static final String QUERY_CRITERIA_AE = "ae";
	public static final String QUERY_CRITERIA_ERISA = "erisa";
	public static final String QUERY_CRITERIA_40ACT = "40act";
	public static final String QUERY_CRITERIA_BUY_BASE = "bb";
	public static final String QUERY_CRITERIA_NO_OF_LEGS  = "nl";
	public static final String ORG_SEPARATOR = ",";
	private static String spaceName = PersistenceConstants.STAGINGAREAORDER;
	private static Log log = LogFactory.getLog(StagingAreaPersistenceService.class);
    public static String stagingAreaCDView = "StagingAreaCDView";
    public static StagingOrderComparator stagingOrderComparator = new StagingOrderComparator();

    //private static StagingAreaExpirableCache cache = new StagingAreaExpirableCache( NettingMBeanC.getInstance().getStagingCacheSize(), NettingMBeanC.getInstance().getStagingExpirationTimeInMS(),
                                                                                    //NettingMBeanC.getInstance().getStagingCacheConcurrency(), NettingMBeanC.getInstance().isStagingCacheEnabled());
    private static StagingOrderCache cache = new StagingOrderCacheC();

	private StagingAreaPersistenceService()
	{
	}
	
	/**
	 *
	 * @param ids
	 * @param field
	 * @return
	 */
	private static boolean shouldQueryCache(Collection<String> ids, String field, boolean queryPrimary)
	{
		if (ids == null || ids.size() == 0)
		{
			return false;
		}
		if (!QUERY_CRITERIA_ID.equals(field))
		{
			return false;
		}
		if (queryPrimary)
		{
			return false;
		}
		return true;
	}	

	private static boolean filterOrder(final Order order, final String userName, 
			final boolean userSpecific, final String source, final boolean excludeAutoExecution, final Integer[] states, final Integer[] ignoreStates)
	{
		if (userSpecific && (!userName.equals(order.getLockedByUser())))
		{
			return true;
		}
		if (source != null && (source.equals(order.getSource())))
		{
			return true;
		}
		if (excludeAutoExecution && order.isAutoExecution())
		{
			return true;
		}
		Integer orderState = order.getState();
		if (states != null && states.length > 0 && (!foundElement(states, orderState)))
		{
			return true;
		}
		if (ignoreStates != null && ignoreStates.length > 0 && (foundElement(ignoreStates, orderState)))
		{
			return true;
		}
		return false;
	}
	
	private static boolean foundElement(final Integer[] source, final Integer element)
	{
		for (Integer s : source)
		{
			if (s != null && s.equals(element))
			{
				return true;
			}
		}
		return false;
	}
	
	private static boolean filterOrder(final Order cachedOrder, final Order queriedOrder, Integer[] states, final Integer[] ignoreStates)
	{
		if (cachedOrder == null)
		{
			return true;
		}
		// if reference is same then order is getting added to cache first time
		if (cachedOrder == queriedOrder)
		{
			return false;
		}
		Integer orderState = cachedOrder.getState();
		if (states != null && states.length > 0 && (!foundElement(states, orderState)))
		{
			return true;
		}
		if (ignoreStates != null && ignoreStates.length > 0 && (foundElement(ignoreStates, orderState)))
		{
			return true;
		}
		return false;
	}
	
	private static List<Order> getOrders( Collection<String> ids, User user, String field, boolean userSpecific, String excludedSource,
										  boolean excludeAutoExecution, Integer[] states, Integer[] ignoreStates, boolean queryPrimaryServer , Boolean rst )
	{
		String namespace = user.getNamespace().getShortName();
		String userName = user.getName();
		boolean chiefDealerUser = user.hasPermission(stagingAreaCDView);
		if (shouldQueryCache(ids, field, queryPrimaryServer))
		{
			Map<String, Order> orderMap = null;
			if (!chiefDealerUser)
			{					
				orderMap = cache.getOrders(namespace, userName, ids);
			}
			else
			{
				orderMap = cache.getOrders(namespace, ids);
			}			
			if (orderMap.size() == ids.size())
			{
				if (log.isDebugEnabled())
				{
					log.debug("StagingAreaPersistenceService.getOrders(): Cache returned requested number of orders.");
				}
				// we received expected no of orders
				// now filter out orders 
				List<Order> expectedOrders = new ArrayList<Order>();
				for (Map.Entry<String, Order> entry : orderMap.entrySet())
				{
					Order order = entry.getValue();
					if (!filterOrder(order, userName, userSpecific, excludedSource, excludeAutoExecution, states, ignoreStates))
					{
						expectedOrders.add(order);
					}
					else
					{
						if (log.isDebugEnabled())
						{
							log.debug("StagingAreaPersistenceService.getOrders(): Order filtered:" + order.getId());
						}
					}
				}
				return expectedOrders;
			}
		}
		String metaspaceName = PersistenceServiceConfiguration.getStagingAreaMetaspaceName();
        List<Order> orderList = new ArrayList<Order>();
        SpaceIterator<Order> oIterator = null;
		try
		{
			Metaspace ms = Metaspaces.getInstance().getMetaspace(namespace, metaspaceName);
			if ( ms == null )
				return null;

			QueryBuilder queryBuilder = ms.createNewQueryBuilder(namespace, spaceName);

            if(!chiefDealerUser)
            {
                queryBuilder.add(ms.defaultCriteriaSet().is(QUERY_CRITERIA_USER_NAME, userName));
            }

            if(rst != null && !rst.booleanValue())
			{
				queryBuilder.add(ms.defaultCriteriaSet().is(QUERY_CRITERIA_NO_OF_LEGS, 1));
			}

			if ( userSpecific )
				queryBuilder.add(ms.defaultCriteriaSet().is(QUERY_CRITERIA_LOCKEDBY, userName));
			
			if(excludedSource != null)
			{
				queryBuilder.add(ms.defaultCriteriaSet().notEquals(QUERY_CRITERIA_SOURCE, excludedSource));
			}
			if (excludeAutoExecution)
			{
				queryBuilder.add(ms.defaultCriteriaSet().is(QUERY_CRITERIA_AE, false));
			}
			
			if ( states != null && states.length > 0 )
			{
				if ( states.length == 1 )
				{
					queryBuilder.add(ms.defaultCriteriaSet().is(QUERY_CRITERIA_STT, states[0]));
				}
				else
				{
					queryBuilder.add(ms.defaultCriteriaSet().in(QUERY_CRITERIA_STT, (Object[]) states));
				}
			}

			if ( ignoreStates != null && ignoreStates.length > 0)
			{
				if ( ignoreStates.length == 1 )
				{
					queryBuilder.add(ms.defaultCriteriaSet().notEquals(QUERY_CRITERIA_STT, ignoreStates[0]));
				}
				else
				{
					queryBuilder.add(ms.defaultCriteriaSet().notIn(QUERY_CRITERIA_STT, (Object[]) ignoreStates));
				}
			}

			if ( ids != null && ids.size() > 0 )
				queryBuilder.add(ms.defaultCriteriaSet().in(field, ids.toArray()));

			//latest first
			queryBuilder.orderBy(QUERY_CRITERIA_CREATION_TIME, Sort.DESCENDING);
            queryBuilder.orderBy( QUERY_CRITERIA_ID , Sort.DESCENDING);
			Query.QueryPreference queryPreference = (queryPrimaryServer ? Query.QueryPreference.PRIMARY : null);			
			if (queryPreference != null)
			{
				oIterator = queryBuilder.build().getIterator(Order.class , NettingMBeanC.getInstance().getMaxSizeForStagingArea( user.getOrganization().getShortName() ), queryPreference);
			}
			else 
			{
				oIterator = queryBuilder.build().getIterator(Order.class , NettingMBeanC.getInstance().getMaxSizeForStagingArea( user.getOrganization().getShortName() ));
			}
			if(oIterator != null)
			{
                if (oIterator.hasNext())
                {
                	List<Order> mongoOrderList = oIterator.nextBatch();
					if (log.isDebugEnabled())
					{
						log.debug("StagingAreaPersistenceService.getOrders(): Number of Orders returned:" + mongoOrderList.size());
					}
                    for(Order order: mongoOrderList)
                    {
                    	Order cachedOrder = cache.add(order);
                    	if (!filterOrder(cachedOrder, order, states, ignoreStates))
                    	{
                    		orderList.add(cachedOrder) ;
                    	} 
                    	else
                    	{
    						if (log.isDebugEnabled())
    						{
    							log.debug("StagingAreaPersistenceService.getOrders(): Order filtered:" + order.getId());
    						}
                    	}
                    }
                }
            }

            sortOrders( orderList );
            return orderList;
		}
		catch ( Exception ex )
		{
			log.error(ex.getMessage(), ex);
		}
		finally
		{
			closeIterator(oIterator);
		}
		return null;
	}
	
	private static void closeIterator(final SpaceIterator<Order> spaceIterator)
	{
		try 
		{
			if (spaceIterator != null)
			{
				spaceIterator.close();
			}
		}
		catch(Throwable t)
		{
			log.warn("Problem with closing spaces iterator,", t);
		}
	}

	public static List<Order> getOrders( Collection<String> ids, User user, String field, boolean userSpecific,
										                   String excludeSource, Integer[] states, Integer[] ignoreStates )
	{
		return getOrders( ids, user, field, userSpecific , excludeSource, false, states, ignoreStates , false, null );
	}

	public static List<Order> getOrdersFromPrimaryServer( Collection<String> ids, User user, String field, boolean userSpecific,
										 String excludeSource, Integer[] states, Integer[] ignoreStates )
	{
		return getOrders( ids, user, field, userSpecific , excludeSource, false, states, ignoreStates , true, null );
	}

	public static List<Order> getOrders( Collection<String> ids, User user, String field, boolean userSpecific,
										 String excludeSource, boolean excludeAutoExecution, Integer[] states, Integer[] ignoreStates, Boolean rst )
	{
        return getOrders( ids, user, field, userSpecific , excludeSource, excludeAutoExecution, states, ignoreStates , false, rst );
	}

    private static void sortOrders( List<Order> orderList )
    {
    	Collections.sort( orderList, stagingOrderComparator);
    }
  

	public static List<Order> getOrders(Map<String, List> filters, User user)
    {
        String metaspaceName = PersistenceServiceConfiguration.getStagingAreaMetaspaceName();
        List<Order> orderList = new ArrayList<Order>();
        String namespace = user.getNamespace().getShortName();
        String userName = user.getName();
        SpaceIterator<Order> oIterator = null;
        try
        {
            Metaspace ms = Metaspaces.getInstance().getMetaspace(namespace, metaspaceName);
            if(ms == null) return orderList;
            QueryBuilder queryBuilder = ms.createNewQueryBuilder(namespace, spaceName);
            // if user do not have chief dealer permission show fetch only his records
            if(!user.hasPermission( stagingAreaCDView ))
            {
                queryBuilder.add(ms.defaultCriteriaSet().is(QUERY_CRITERIA_USER_NAME, userName));
            }
            for(Map.Entry<String, List> entry : filters.entrySet())
            {
                queryBuilder.add(ms.defaultCriteriaSet().in(entry.getKey(), entry.getValue().toArray()));
            }
            // sort by create time desc
            queryBuilder.orderBy(QUERY_CRITERIA_CREATION_TIME, Sort.DESCENDING);
            oIterator = queryBuilder.build().getIterator(Order.class , NettingMBeanC.getInstance().getMaxSizeForStagingArea( user.getOrganization().getShortName() ));
            Integer[] states = getStates(filters);
            if(oIterator != null)
            {
                if (oIterator.hasNext())
                {
                    for(Order order: oIterator.nextBatch())
                    {
                    	Order cachedOrder = cache.add(order);
                    	if (!filterOrder(cachedOrder, order, states, null))
                    	{
                    		orderList.add(cachedOrder) ;
                    	}                        
                    }
                }
            }
        }
        catch (Exception e)
        {
            log.error("Some error during order query", e);
        }
        finally
        {
        	closeIterator(oIterator);
        }
        return orderList;
    }
	
	private static Integer[] getStates(final Map<String, List> filters)
	{
		if (filters == null)
		{
			return null;
		}
        List<?> inputStates = filters.get(QUERY_CRITERIA_STT); 
        Integer[] states = null;            
        if (inputStates != null)
        {             
        	List<Integer> intStates = new ArrayList<Integer>(inputStates.size());
        	for (Object obj : inputStates)
        	{
        		if (obj instanceof Integer)
        		{
        			intStates.add((Integer)obj);
        		} 
        		else if(obj instanceof String)
        		{
        			try 
        			{
        				int state = Integer.parseInt((String)obj);
        				intStates.add(state);
        			}
        			catch(Exception e)
        			{
        				// Number format exception
        			}            			
        		}
        	}
        	states = new Integer[intStates.size()];
        	states = intStates.toArray(states);
        }
        return states;
	}
    
	public static List<Order> getOrders( Map<String, List> filters, Organization org )
	{
		if(log.isDebugEnabled()){
			log.debug("Inside getOrders for : "+ org);
		}
		String metaspaceName = PersistenceServiceConfiguration.getStagingAreaMetaspaceName();
		List<Order> orderList = new ArrayList<Order>();
		String namespace = org.getNamespace().getShortName();
		SpaceIterator<Order> oIterator = null;
		try
		{
			Metaspace ms = Metaspaces.getInstance().getMetaspace(namespace, metaspaceName);
			if ( ms == null )
				return orderList;
			QueryBuilder queryBuilder = ms.createNewQueryBuilder(namespace, spaceName);

			for ( Map.Entry<String, List> entry : filters.entrySet() )
			{
				queryBuilder.add(ms.defaultCriteriaSet().in(entry.getKey(), entry.getValue().toArray()));
			}
			// sort by create time desc
			queryBuilder.orderBy(QUERY_CRITERIA_CREATION_TIME, Sort.DESCENDING);
			oIterator = queryBuilder.build().getIterator(Order.class, NettingMBeanC.getInstance().getMaxSizeForStagingArea(org.getShortName()));
			Integer[] states = getStates(filters);
			if ( oIterator != null )
			{
				if ( oIterator.hasNext() )
				{
					for ( Order order : oIterator.nextBatch() )
					{
						Order cachedOrder = cache.add(order);
						if (!filterOrder(cachedOrder, order, states, null))
						{
							orderList.add(cachedOrder);
						}
					}
				}
			}
		}
		catch ( Exception e )
		{
			log.error(".getOrders : Some error during order query", e);
		}
		finally
		{
			closeIterator(oIterator);
		}
		if(log.isDebugEnabled()){
			log.debug("Order List : "+ orderList);
		}
		return orderList;
	}

    public static List<Order> getActiveOMSOrders(Organization org )
    {
        String metaspaceName = PersistenceServiceConfiguration.getStagingAreaMetaspaceName();
        List<Order> orderList = new ArrayList<Order>();
        String namespace = org.getNamespace().getShortName();
        SpaceIterator<Order> oIterator = null;
        try{
            Metaspace ms = Metaspaces.getInstance().getMetaspace(namespace, metaspaceName);
            if(ms == null) return orderList;
            QueryBuilder queryBuilder = ms.createNewQueryBuilder(namespace, PersistenceConstants.STAGINGAREAORDER);
            Integer[] activeStates = {OrderStatusType.INTIAL.getCode(), OrderStatusType.INUSE.getCode(),
            		OrderStatusType.MATCHED.getCode(), OrderStatusType.TRIGGERED.getCode(),
					OrderStatusType.REJECTED.getCode(), OrderStatusType.ACCEPTED.getCode(), OrderStatusType.CREDIT_FAIL.getCode(),
            		OrderStatusType.IMPORTED.getCode(), OrderStatusType.CANCEL_REQUESTED.getCode(), OrderStatusType.AMEND_REQUESTED.getCode()};
            queryBuilder.add(ms.defaultCriteriaSet().in(QUERY_CRITERIA_STT, activeStates));
            queryBuilder.add(ms.defaultCriteriaSet().is(QUERY_CRITERIA_OMS, true));
            queryBuilder.add(ms.defaultCriteriaSet().is(QUERY_CRITERIA_ST, 'A'));
            //queryBuilder.add(ms.defaultCriteriaSet().greaterThan("extm", 0));
            // sort by create time desc
            queryBuilder.orderBy(QUERY_CRITERIA_CREATION_TIME, Sort.DESCENDING);
            oIterator = queryBuilder.build().getIterator(Order.class , NettingMBeanC.getInstance().getMaxSizeForStagingArea( org.getShortName() ));
            if(oIterator != null)
            {
                if (oIterator.hasNext())
                {
                    for(Order order: oIterator.nextBatch())
                    {
                    	Order cachedOrder = cache.add(order);
                    	if (!filterOrder(cachedOrder, order, activeStates, null))
                    	{
                    		orderList.add(cachedOrder);
                    	}
                    }
                }
            }
        }
        catch (Exception e)
        {
            log.error(".getActiveOMSOrders : Some error during order query", e);
        }
        finally
        {
        	closeIterator(oIterator);
        }
        return orderList;
    }
    
    public static List<Order> getAutoOMSOrders(Organization org )
    {
        String metaspaceName = PersistenceServiceConfiguration.getStagingAreaMetaspaceName();
        List<Order> orderList = new ArrayList<Order>();
        String namespace = org.getNamespace().getShortName();
        SpaceIterator<Order> oIterator = null;
        try
        {
            Metaspace ms = Metaspaces.getInstance().getMetaspace(namespace, metaspaceName);
            if(ms == null) return orderList;
            QueryBuilder queryBuilder = ms.createNewQueryBuilder(namespace, PersistenceConstants.STAGINGAREAORDER);
            Integer[] activeStates = {OrderStatusType.AUTO.getCode()};
            queryBuilder.add(ms.defaultCriteriaSet().in(QUERY_CRITERIA_STT, activeStates));
            queryBuilder.add(ms.defaultCriteriaSet().is(QUERY_CRITERIA_OMS, true));
            queryBuilder.add(ms.defaultCriteriaSet().is(QUERY_CRITERIA_ST, 'A'));
            //queryBuilder.add(ms.defaultCriteriaSet().greaterThan("extm", 0));
            // sort by create time desc
            queryBuilder.orderBy(QUERY_CRITERIA_CREATION_TIME, Sort.DESCENDING);
            oIterator = queryBuilder.build().getIterator(Order.class , NettingMBeanC.getInstance().getMaxSizeForStagingArea( org.getShortName() ));
            if(oIterator != null)
            {
                if (oIterator.hasNext())
                {
                    for(Order order: oIterator.nextBatch())
                    {
                    	Order cachedOrder = cache.add(order);
                    	if (!filterOrder(cachedOrder, order, activeStates, null))
                    	{
                    		 orderList.add(cachedOrder) ;
                    	}
                    }
                }
            }
        }
        catch (Exception e)
        {
            log.error(".getAutoOMSOrders : Some error during order query", e);
        }
        finally
        {
        	closeIterator(oIterator);
        }
        return orderList;
    }
    
    public static Order getOrderFromCache(final String id, final String namespace)
    {
    	return cache.getOrder(namespace, id);
    }
	
	public static ErrorMessage updateOrdersAsExported(List<String> ids, User user){
		ErrorMessage em = null;		
		for(String id : ids)
		{
			//Order odr = StagingAreaPersistenceService.getOrder(id, user.getNamespace());
			String namespace = user.getNamespace().getName();			;
			Order odr = queryOrderById(id, namespace);
			if (odr == null)
			{
				// query primary server
				odr = queryOrderById(id, namespace, true);
			}				
			if (odr != null)
			{
				synchronized(odr)
				{
					odr.setState(OrderStatusType.DOWNLOADED.getCode());
					odr.setLockedByUser(user.getName());
					em = StagingAreaPersistenceService.updateOrder(odr, odr.getOrderBatchId());
				}
			}
		}
		return em;
	}

	
	public static List<Order> getOrders(String namespace , OrderStatusType state, Map<String, Object> qCriteriaMap, boolean queryPrimaryServer)
    {
        String metaspaceName = PersistenceServiceConfiguration.getStagingAreaMetaspaceName();
        SpaceIterator<Order> spaceIterator = null;
        try
        {
            Metaspace ms = Metaspaces.getInstance().getMetaspace(namespace, metaspaceName);
            if ( ms == null )
                return null;
            CriteriaSet cs = ms.defaultCriteriaSet();
            QueryBuilder queryBuilder = ms.createNewQueryBuilder( namespace, spaceName );
            Integer[] doneState = { state.getCode()};
            queryBuilder.add(cs.in( QUERY_CRITERIA_STT, ( Object[] ) doneState ));            
            
            queryBuilder.add(cs.notEquals(QUERY_CRITERIA_SOURCE, "PTFLO"));
            if (qCriteriaMap != null)
            {
            	if (qCriteriaMap.containsKey(StagingAreaPersistenceService.QUERY_CRITERIA_ERISA) 
            			&& qCriteriaMap.containsKey(StagingAreaPersistenceService.QUERY_CRITERIA_40ACT)) {
            		// Erisa and 40Act both orders qualify for no netting so filtering out orders if any one is true with or condition
            		Object erisaValue = qCriteriaMap.get(StagingAreaPersistenceService.QUERY_CRITERIA_ERISA);
            		Object fortyAccountValue = qCriteriaMap.get(StagingAreaPersistenceService.QUERY_CRITERIA_40ACT);
            		if (erisaValue instanceof Boolean && fortyAccountValue instanceof Boolean) {
            			boolean erisa = ((Boolean)erisaValue).booleanValue();
            			boolean fortyAccount = ((Boolean)fortyAccountValue).booleanValue();
            			Criteria noNettingCriteria = null;
            			if (erisa) {
            				if (erisa == fortyAccount) {
            					// if both are true then build criteria with or condition
            					noNettingCriteria = cs.or(cs.is(StagingAreaPersistenceService.QUERY_CRITERIA_ERISA, erisaValue), 
                    					cs.is(StagingAreaPersistenceService.QUERY_CRITERIA_40ACT, fortyAccountValue));
            				} else {
            					// and condition
            					noNettingCriteria = cs.and(cs.is(StagingAreaPersistenceService.QUERY_CRITERIA_ERISA, erisaValue), 
                    					cs.is(StagingAreaPersistenceService.QUERY_CRITERIA_40ACT, fortyAccountValue));
            				}
            			} else {
            				// if both are false or one of them is false then build criteria with and condition
        					noNettingCriteria = cs.and(cs.is(StagingAreaPersistenceService.QUERY_CRITERIA_ERISA, erisaValue), 
                					cs.is(StagingAreaPersistenceService.QUERY_CRITERIA_40ACT, fortyAccountValue));
            			}
            			qCriteriaMap.remove(StagingAreaPersistenceService.QUERY_CRITERIA_ERISA);
            			qCriteriaMap.remove(StagingAreaPersistenceService.QUERY_CRITERIA_40ACT);
            			queryBuilder.add(noNettingCriteria);
            		}
            	}
            	
            	for (Map.Entry<String, Object> entry : qCriteriaMap.entrySet())
            	{
            		String key = entry.getKey();
            		Object value = entry.getValue();
            		if (value != null)
            		{
            			queryBuilder.add( cs.is( key, value ) );
            		}
            	}
            }
			Query.QueryPreference queryPreference = (queryPrimaryServer ? Query.QueryPreference.PRIMARY : null);
			
            //List<Order> orders = (List<Order>) queryBuilder.build().getResultList(Order.class, queryPreference);
			int maxOrderCountPerBatch = NettingMBeanC.getInstance().getMaxOrderCountPerAutoExecutionBatch(namespace);
			spaceIterator = queryBuilder.build().getIterator(Order.class, maxOrderCountPerBatch, queryPreference);
			List<Order> returnedOrders = new ArrayList<Order>();
			if (spaceIterator != null)
			{
				if (spaceIterator.hasNext())
				{
					List<Order> orders = spaceIterator.nextBatch();
					for (Order order : orders)
					{
		               	Order cachedOrder = cache.add(order);
	                	if (!filterOrder(cachedOrder, order, doneState, null))
	                	{
	                		returnedOrders.add(cachedOrder);
	                	}           	
					}
				}
			}
            return returnedOrders;
        }
        catch ( Exception ex )
        {
            log.error(ex.getMessage(), ex);
        }
        finally
        {
        	closeIterator(spaceIterator);
        }
        return null;
    }
    
    
    
    public static long getUserInitialStateOrdersCount( String namespace, String userName)
    {
        String metaspaceName = PersistenceServiceConfiguration.getStagingAreaMetaspaceName();
        try
        {
            Metaspace ms = Metaspaces.getInstance().getMetaspace(namespace, metaspaceName);
            if ( ms == null )
                return 0L;

            QueryBuilder queryBuilder = ms.createNewQueryBuilder( namespace, spaceName );
            Criteria crt2 = ms.defaultCriteriaSet().is( QUERY_CRITERIA_STT, OrderStatusType.INTIAL.getCode() );
            queryBuilder.add(crt2);
            if(userName != null)
            {
                Criteria crt3 = ms.defaultCriteriaSet().is( QUERY_CRITERIA_USER_NAME, userName );
                queryBuilder.add( crt3 );
            }
            return queryBuilder.build().getCount();
        }
        catch ( Exception ex )
        {
            log.error(ex.getMessage(), ex);
        }
        return 0L;
    }

    /**
     * If user is null then returns null and no delete
     * else deletes orders orders for that user
     *
     * @param user
     * @return
     */
    public static List<ErrorMessage> deleteOrders( List<String> orderIds, User user )
    {
        if ( user == null )
        {
            log.warn( "SAQS.deleteOrders - Unable to delete expired Staging orders. User passed is null." );
            return null;
        }
        List<ErrorMessage> errors = null;       
        List<Order> ordersToCheck = getOrders(orderIds, user, QUERY_CRITERIA_ID, false, null, null, null);
        if(ordersToCheck.size() == 0 || ordersToCheck.size() != orderIds.size())
        {
            log.error( "SAQS.deleteOrders - Unable to query specified orders. Hence unable to perform DELETE operation." );
            ErrorMessage error = MessageFactory.newErrorMessage();
            error.setErrorCode( "UNABLE_TO_DELETE_SELECTED_ORDERS" );
            errors = new ArrayList<ErrorMessage>();
            errors.add( error );
            return errors;
        }
        for ( Order order : ordersToCheck )
        {
            synchronized ( order )
            {
                order.setState( OrderStatusType.DELETED.getCode() );
                ErrorMessage error = updateOrder( order, order.getOrderBatchId() );
                if ( null != error )
                {
                    log.error( "SAQS.deleteOrders Failed for Order.id=" + order.get_id() + ", cloid=" + order.getClientOrderId() + " Error Message : " + error.getUserMessage() );
                    if ( null == errors )
                    {
                        errors = new ArrayList<ErrorMessage>();
                    }
                    errors.add( error );
                }
            }
        }  
        return errors;
    }

   
    public static List<Order> getModifiableOrders( User user )
	{
		String metaspaceName = PersistenceServiceConfiguration.getStagingAreaMetaspaceName();
		String namespace = user.getNamespace().getShortName();
		String userName = user.getName();
		try
		{
			Metaspace ms = Metaspaces.getInstance().getMetaspace(namespace, metaspaceName);
			if ( ms == null )
				return null;
			QueryBuilder queryBuilder = ms.createNewQueryBuilder(namespace, spaceName);
			Criteria crt1 = ms.defaultCriteriaSet().and( ms.defaultCriteriaSet().is( QUERY_CRITERIA_LOCKEDBY, userName ), ms.defaultCriteriaSet().is( QUERY_CRITERIA_STT, OrderStatusType.INUSE.getCode() ) );
			Criteria crt2;
			Integer[] states = null;
			if(NettingMBeanC.getInstance().isStagingImportDraftOrders(user.getOrganization().getName()))
			{
				states = new Integer[2];
				states[0] = OrderStatusType.INTIAL.getCode();
				states[1] = OrderStatusType.DOWNLOADED.getCode();
				crt2 = ms.defaultCriteriaSet().or(ms.defaultCriteriaSet().is(QUERY_CRITERIA_STT, states[0]), ms.defaultCriteriaSet().is(QUERY_CRITERIA_STT, states[1]));
			}
			else
			{
				states = new Integer[1];
				states[0] = OrderStatusType.DOWNLOADED.getCode();
				crt2 = ms.defaultCriteriaSet().is(QUERY_CRITERIA_STT, states[0]);
			}
			Criteria crt4 = ms.defaultCriteriaSet().or(crt1, crt2);	
			if(user.hasPermission(stagingAreaCDView))
			{
				queryBuilder.add(crt4);
			}
			else
			{
				queryBuilder.add(ms.defaultCriteriaSet().and(ms.defaultCriteriaSet().is(QUERY_CRITERIA_USER_NAME, userName), crt4));
			}			
			//latest first
			queryBuilder.orderBy(QUERY_CRITERIA_CREATION_TIME, Sort.DESCENDING);

			List<Order> orderList = (List<Order>) queryBuilder.build().getResultList(Order.class);
			List<Order> returnedOrders = null;
			if (orderList != null)
			{
				returnedOrders = new ArrayList<Order>(orderList.size());
	            for(Order order : orderList)
	            {
	            	Order cachedOrder = cache.add(order);
	            	if (!filterOrder(cachedOrder, order, states, null))
	            	{
	            		returnedOrders.add(cachedOrder);
	            	}
	            }
			}
			return returnedOrders;
		}
		catch ( Exception ex )
		{
			log.error(ex.getMessage(), ex);
		}
		return null;
	}

 	public static Long getOrdersCount( User user)
    {
        String namespace = user.getNamespace().getShortName();
        String userName = user.getName();
        String metaspaceName = PersistenceServiceConfiguration.getStagingAreaMetaspaceName();
        try
        {
            Metaspace ms = Metaspaces.getInstance().getMetaspace(namespace, metaspaceName);
            if ( ms == null )
                return 0L;
            QueryBuilder queryBuilder = ms.createNewQueryBuilder(namespace, spaceName);
            Criteria crt2 = ms.defaultCriteriaSet().is( QUERY_CRITERIA_USER_NAME, userName );
            Criteria crt3 = ms.defaultCriteriaSet().notEquals( QUERY_CRITERIA_SOURCE, StagingAreaConstants.PORTFOLIO );
            queryBuilder.add(crt2);
            queryBuilder.add(crt3);
            return queryBuilder.build().getCount();
        }
        catch ( Exception ex )
        {
            log.error(ex.getMessage(), ex);
        }
        return 0L;
    }

 	/**
 	 * Only three states are handled with this method
 	 * Lock/Unlock and expired 
 	 * so do not call this method to update the state of order to any other state
 	 * @param updateParams
 	 * @param user
 	 * @param pfId
 	 * @param queryOrderIds
 	 * @param queryPrimaryServer
 	 * @return
 	 */
	public static Map<String, ErrorMessage> updateOrders( UpdateOrderRequestList updateParams, User user, String pfId, boolean queryOrderIds, boolean queryPrimaryServer  )
	{
    	Map<String, ErrorMessage> errorMap = new HashMap<String, ErrorMessage>();
		String namespace = user.getNamespace().getShortName();
		String metaspaceName = PersistenceServiceConfiguration.getStagingAreaMetaspaceName();
		Metaspace ms = Metaspaces.getInstance().getMetaspace( namespace, metaspaceName );
		Map<String, com.integral.staging.model.UpdateOrderRequest> updateOrderRequestMap = new HashMap<String, com.integral.staging.model.UpdateOrderRequest>();
		for ( com.integral.staging.model.UpdateOrderRequest updateOrderRequest : updateParams )
		{
			updateOrderRequestMap.put(updateOrderRequest.getClientOrderId(), updateOrderRequest);
		}
		String field = QUERY_CRITERIA_COID;
		if ( queryOrderIds )
		{
			field = QUERY_CRITERIA_ID;
		}

		try
		{
			if ( ms != null )
			{
                List<Order> odrs = getOrders( updateOrderRequestMap.keySet(), user, field, false, null,false, null, null, queryPrimaryServer, null ); // should we pass ignoreStates ?
                if ( odrs != null )
                {
                    for ( Order odr : odrs )
                    {
                    	synchronized(odr)
                    	{
                            String msg = "Order.id=" + odr.get_id() + ", cloid=" + odr.getClientOrderId();
                            com.integral.staging.model.UpdateOrderRequest uor = queryOrderIds ? updateOrderRequestMap.get(odr.get_id()) : updateOrderRequestMap.get(odr.getClientOrderId());
                            OrderStatusType orderStatusType = uor.getUpdateType();
                            ErrorMessage error = null;
                            //validate order
                            switch ( orderStatusType )
                            {
                            case INUSE :
                            	if (odr.getState() == OrderStatusType.INTIAL.getCode())
                            	{
                                	odr.setState(OrderStatusType.INUSE.getCode());
                                    odr.setLockedByUser( user.getShortName() );
                                    odr.setPrevState( OrderStatusType.INTIAL.getCode() );
                                    odr.setPfId( pfId );
                                }
                                else if(odr.getState() == OrderStatusType.DOWNLOADED.getCode())
                                {
                                	odr.setState(OrderStatusType.INUSE.getCode());
                                    odr.setLockedByUser( user.getShortName() );
                                    odr.setPrevState( OrderStatusType.DOWNLOADED.getCode() );
                                    odr.setPfId( pfId );
                                }
                                else
                                {
                                	error = new ErrorMessageC();
                                    error.setCode("Order is not in initial or downloaded state");
                                    error.setErrorCode("Order is not in initial or downloaded state");
                                }
                                break;
                            case INTIAL :
                            	if ( !(odr.getState() == OrderStatusType.INUSE.getCode()) )
                                {
                                    error = new ErrorMessageC();
                                    error.setCode( "Order is not in INUSE state" );
                                    error.setErrorCode( "Order is not in INUSE state" );
                                }
                                else
                                {
                                	odr.setState(odr.getPrevState());
                                    odr.setLockedByUser("");
                                    odr.setPfId( "" );
                                }
 
                                break;
                                
                            case EXPIRED :
                            	int state = odr.getState();
                            	if (OrderStatusType.EXECUTED.getCode() == state)
                            	{
                                    error = new ErrorMessageC();
                                    error.setCode("Order is already executed");
                                    error.setErrorCode("ORDER_ALREADY_EXECUTED");
                            	}
                            	else if(OrderStatusType.REJECTED.getCode() == state)
                            	{
                                    error = new ErrorMessageC();
                                    error.setCode( "Order is already rejected" );
                                    error.setErrorCode( "ORDER_ALREADY_REJECTED" );
                            	}
                              	else if(OrderStatusType.CANCELLED.getCode() == state)
                            	{
                                    error = new ErrorMessageC();
                                    error.setCode("Order is already cancelled");
                                    error.setErrorCode("ORDER_ALREADY_CANCELLED");
                            	}
                              	else 
                              	{
                              		odr.setState( OrderStatusType.EXPIRED.getCode() );
                              	}
                            	break;                            
                            }

                            //Save order
                            if ( error == null )
                            {
                                error = updateOrder(odr, odr.getOrderBatchId());
                            }
                            //check for error from save action.
                            if ( error == null )
                            {
                                log.info(msg + ",newstate=" + orderStatusType + " updated");
                            }
                            else
                            {                    
                                log.error(msg +  ",newstate=" + orderStatusType + " failed to update.");
                                errorMap.put(odr.get_id(), error);
                            }
                    	}

                    }
                }
                return errorMap;
			}

		}
		catch ( Exception ex )
		{
			log.error(ex.getMessage(), ex);
		}
		return null;
	}


    public static List<ErrorMessage> updateAllOrdersForUser( User user, boolean setInUse )
	{
		String namespace = user.getNamespace().getShortName();
		String metaspaceName = PersistenceServiceConfiguration.getStagingAreaMetaspaceName();
		Metaspace ms = Metaspaces.getInstance().getMetaspace(namespace, metaspaceName);
		try
		{
            if ( ms != null )
            {
                List<Order> odrs = getModifiableOrders( user ); // should we pass ignoreStates ?
                return updateOrdersForUser(odrs , user , null , setInUse);
            }
		}
		catch ( Exception ex )
		{
			log.error(ex.getMessage(), ex);
		}
		return null;
	}
    /**
     * Use this method only for locking and unlocking orders
     * @param orderList
     * @param user
     * @param pfID
     * @param isLock
     * @return
     */
    public static List<ErrorMessage> updateOrdersForUser( List<Order> orderList , User user , String pfID , boolean isLock )
    {
        try
        {
            if(user != null)
            {
                List<ErrorMessage> errors = null;
                if ( orderList != null )
                {
                    for ( Order odr : orderList )
                    {
                    	synchronized (odr)
                    	{
                            String msg = "Order.id=" + odr.get_id() + ", cloid=" + odr.getClientOrderId();
                            ErrorMessage error = null;
                            //validate order
                            if ( isLock )
                            {
                                if ( odr.getState() == OrderStatusType.INTIAL.getCode() )
                                {
                                    odr.setState( OrderStatusType.INUSE.getCode() );
                                    odr.setLockedByUser( user.getShortName() );
                                    odr.setPrevState( OrderStatusType.INTIAL.getCode() );
                                    odr.setPfId( pfID );
                                }
                                else if ( odr.getState() == OrderStatusType.DOWNLOADED.getCode() )
                                {
                                    odr.setState( OrderStatusType.INUSE.getCode() );
                                    odr.setLockedByUser( user.getShortName() );
                                    odr.setPrevState( OrderStatusType.DOWNLOADED.getCode() );
                                    odr.setPfId( pfID );
                                }
                                else 
                                {
                                	error = new ErrorMessageC();
                                    error.setCode("Order is not in initial or downloaded state");
                                    error.setErrorCode("Order is not in initial or downloaded state");                       
                                }
                            }
                            else if ( odr.getLockedByUser() != null && odr.getLockedByUser().equals( user.getShortName() ) )
                            {
                                if ( !( odr.getState() == OrderStatusType.INUSE.getCode() ) )
                                {
                                    error = new ErrorMessageC();
                                    error.setCode( "Order is not in INUSE state" );
                                    error.setErrorCode( "Order is not in INUSE state" );
                                }
                                else
                                {
                                    odr.setState( odr.getPrevState() );
                                    odr.setPfId( "" );
                                    odr.setLockedByUser( "" );
                                }
                            }

                            //Save order
                            if ( error == null )
                            {
                                error = updateOrder( odr, odr.getOrderBatchId() );
                            }

                            //check for error from save action.
                            if ( error == null )
                            {
                                log.info( msg + " updated" );
                            }
                            else
                            {
                                // Error handling
                                if ( errors == null )
                                {
                                    errors = new ArrayList<ErrorMessage>();
                                }

                                log.error( msg + " failed to update." + " Error: " + error.getErrorCode() +
                                                ( null == error.getException() ? "" : ( " Exception: " + error.getException() ) ));
                                errors.add( error );
                            }
                    	}
                    }
                }
                return errors;
            }
        }
        catch ( Exception ex )
        {
            log.error(ex.getMessage(), ex);
        }
        return null;
    }

    /**
     * You don't need to synchronize order submission as there will not be any work-flow that's modifying this order concurrently
     * @param odrBatch
     * @param correlationId
     * @return
     */
    public static ErrorMessage submitOrderBatch( OrderBatch odrBatch, String correlationId )
	{
    	List<Order> orderList = odrBatch.getOrderList();
		// populate Order with BenchMark rate 
		StagingAreaUtils.populateBMRateOnOrderSubmission(orderList);
		String obid = odrBatch.get_id();
		ISSpacesPersistenceService usps = PersistenceServiceFactory.getStagingAreaPersistenceService();
		ApplicationSpaceEvent evtOdrBatch = usps.createEvent(odrBatch, ApplicationEventCodes.EVENT_SA_ORDER_BATCH_SUBMIT);		
		for(Order odr : orderList)
		{

			odr.setOrderBatchId(obid);
			ErrorMessage error = submitOrder(odr, obid);
			if(error != null && error.getErrors() != null)
			{
				return error;
			}
		}
        
    	evtOdrBatch.setSendNotification(false);       
		ErrorMessage error = usps.synchronousPersist( evtOdrBatch, obid, false, "SAOrderBatchSubmit" );
		return error;
	}
    

    

    
	/**
	 * You don't need to synchronize order submission as there will not be any workflow that's modifying this order concurrently
	 * @param odr
	 * @param correlationId
	 * @return
	 */
	public static ErrorMessage submitOrder( Order odr, String correlationId )
	{
		ISSpacesPersistenceService usps = PersistenceServiceFactory.getStagingAreaPersistenceService();
		ApplicationSpaceEvent evt = usps.createEvent(odr, ApplicationEventCodes.EVENT_SA_ORDER_SUBMIT);
		evt.setSendNotification(true);
		cache.add(odr);
		log.info("SOQS.submitOrder - order added in cache, Id= " + odr.getId());
		ErrorMessage error = usps.synchronousPersist(evt, correlationId, false, "SAOrderSubmit");		
		return error;
	}

	
	public static ErrorMessage updateOrder( Order odr, String correlationId )
	{
		int code = odr.getState();
		String id = odr.getId();
        log.info("SOQS.updateOrder - for order Id=" + id + ":state:" + OrderStatusType.fromCode(code) + ":" + code);
        //set ttl        
        if(odr.getState() == OrderStatusType.CANCELLED.getCode()
                || odr.getState() == OrderStatusType.REJECTED.getCode()
                || odr.getState() == OrderStatusType.EXPIRED.getCode()
                || odr.getState() == OrderStatusType.DELETED.getCode()
                || odr.getState() == OrderStatusType.EXECUTED.getCode())
        {
            Date ttlDate = StagingAreaUtils.getTTLDate( new Date() );
            if ( ttlDate != null )
            {
                odr.setTimeToLive( ttlDate );
            }           
        }
        ISSpacesPersistenceService usps = PersistenceServiceFactory.getStagingAreaPersistenceService();
		ApplicationSpaceEvent evt = usps.createEvent(odr, ApplicationEventCodes.EVENT_SA_ORDER_UPDATE);
        evt.setSendNotification(true);
        // add to cache before persisting so that cache always have the latest state
		if (cache.ignoreCaching(odr))
		{
			cache.scheduleOrderRemoval(odr);			
		}	 
		else
		{
			cache.add(odr);			
		}
		ErrorMessage error = usps.synchronousPersist( evt, correlationId, false, "SAOrderUpdate" );
		return error;
	}


	public static ErrorMessage cancelOrder( Order odr, String correlationId )
	{
		String id = odr.getId();
        log.info("SOQS.cancelOrder - for order Id=" + id + " state : " + odr.getState());
		//set ttl
        Date ttlDate = StagingAreaUtils.getTTLDate( new Date() );
        if(ttlDate != null)
        {
            odr.setTimeToLive( ttlDate );
        }
        ISSpacesPersistenceService usps = PersistenceServiceFactory.getStagingAreaPersistenceService();
		ApplicationSpaceEvent evt = usps.createEvent(odr, ApplicationEventCodes.EVENT_SA_ORDER_CANCEL);
        evt.setSendNotification(true);
        // It's a terminal state order so schedule removal from cache.
        if (cache.ignoreCaching(odr))
        {
        	cache.scheduleOrderRemoval(odr);       	
        }       
        
		ErrorMessage error = usps.synchronousPersist( evt, correlationId, false, "SAOrderCancel" );        
		return error;
	}

	public static Order getOrder( String orderId, Namespace ns )
	{
        return queryOrderById(orderId, ns.getShortName(), false);
	}
	
	public static Order getOrder( String orderId, Namespace ns, boolean queryPrimaryServer )
	{
        return queryOrderById(orderId, ns.getShortName(), queryPrimaryServer);
	}
	
	public static Order queryOrderById( String id, String namespace )
    {
		return queryOrderById(id, namespace, false);
    }
	
	public static Order queryOrderById( String id, String namespace, boolean queryPrimaryServer )
    {
		// query order from cache first , if cache does not have it then query from Mongo
		Order order = null;
		if (!queryPrimaryServer)
		{
			order = getOrderFromCache(id, namespace);
			if (order != null)
			{
				return order;
			}
		}
        String metaspaceName = PersistenceServiceConfiguration.getStagingAreaMetaspaceName();
        try
        {
            Metaspace ms = Metaspaces.getInstance().getMetaspace(namespace, metaspaceName);
            if ( ms == null )
			{
				log.info("SOQS.getOrder - metaspace is null.Id=" + id + ",ns=" + namespace + ",ms=" + metaspaceName);
				return null;
			}

            QueryBuilder queryBuilder = ms.createNewQueryBuilder(namespace, spaceName);

            queryBuilder.add(ms.defaultCriteriaSet().is(QUERY_CRITERIA_ID, id));
    		Query.QueryPreference queryPreference = (queryPrimaryServer ? Query.QueryPreference.PRIMARY : null);    		
			if (queryPreference != null)
			{
				order = (Order) queryBuilder.build().getSingleResult(Order.class, DefaultSerializationView.class, queryPreference);
			}
			else
			{
				order = (Order) queryBuilder.build().getSingleResult(Order.class);
			}			
            return cache.add(order);
        }
        catch ( Exception ex )
        {
            log.error("SOQS.getOrder - Failed to query order.Id=" + id + ",ns=" + namespace, ex);
        }
        return null;
    }

	public static Order getOrderUsingCoid( String coid, Namespace ns )
	{
		String metaspaceName = PersistenceServiceConfiguration.getStagingAreaMetaspaceName();
		String namespace = ns.getShortName();
		try
		{
			Metaspace ms = Metaspaces.getInstance().getMetaspace(namespace, metaspaceName);
			if ( ms == null )
			{
				log.info("SOQS.getOrderUsingCoid - metaspace is null.coid=" + coid + ",ns=" + ns + ",ms=" + metaspaceName);
				return null;
			}
			QueryBuilder queryBuilder = ms.createNewQueryBuilder(namespace, spaceName);
			queryBuilder = queryBuilder.add(ms.defaultCriteriaSet().is(QUERY_CRITERIA_COID, coid));
			Order o = (Order) queryBuilder.build().getSingleResult(Order.class);
			return cache.add( o );
		}
		catch ( Exception e )
		{
			log.error("SOQS.getOrderUsingCoid - Failed to query order.coid=" + coid + ",ns=" + ns, e);
		}
		return null;
	}
	
	public static Order getOrderUsingCoid( String coid, User user )
	{
		if (user == null) 
		{
			return null;
		}
		Namespace ns = user.getNamespace();
		String metaspaceName = PersistenceServiceConfiguration.getStagingAreaMetaspaceName();
		String namespace = ns.getShortName();
		try
		{
			Metaspace ms = Metaspaces.getInstance().getMetaspace(namespace, metaspaceName);
			if ( ms == null )
			{
				log.info("SOQS.getOrderUsingCoid - metaspace is null.coid=" + coid + ",ns=" + ns + ",ms=" + metaspaceName);
				return null;
			}		
			Criteria crt = null;
			if(user.hasPermission(stagingAreaCDView))
			{
				crt = ms.defaultCriteriaSet().is(QUERY_CRITERIA_COID, coid);
			}
			else
			{
				String userName = user.getShortName();
				crt = ms.defaultCriteriaSet().and(ms.defaultCriteriaSet().is(QUERY_CRITERIA_USER_NAME, userName), ms.defaultCriteriaSet().is(QUERY_CRITERIA_COID, coid));				
			}			
			QueryBuilder queryBuilder = ms.createNewQueryBuilder(namespace, spaceName);
			queryBuilder = queryBuilder.add(crt);
			Order o = (Order) queryBuilder.build().getSingleResult(Order.class);
			return cache.add( o );
		}
		catch ( Exception e )
		{
			log.error("SOQS.getOrderUsingCoid - Failed to query order.coid=" + coid + ",ns=" + ns, e);
		}
		return null;
	}


    public static StagingOrderCache getCache()
    {
        return cache;
    }
    
    public static List<ErrorMessage> markPastTradeDateOrdersExpired(final String namespace, final boolean isNZDRole)
    { 
    	SpaceIterator<Order> spaceIterator = null;
        try
        {            
            List<Order> returnedOrders = new ArrayList<Order>();
            List<ErrorMessage> errors = null;
            String metaspaceName = PersistenceServiceConfiguration.getStagingAreaMetaspaceName();
           	Metaspace ms = Metaspaces.getInstance().getMetaspace(namespace, metaspaceName);
            if ( ms == null )
            {
            	return null;
            }
            CriteriaSet cs = ms.defaultCriteriaSet();
            QueryBuilder queryBuilder = ms.createNewQueryBuilder(namespace, spaceName);
            Integer[] ignoreStates = { OrderStatusType.CANCELLED.getCode() , OrderStatusType.REJECTED.getCode(), OrderStatusType.EXPIRED.getCode(), OrderStatusType.EXECUTED.getCode(), OrderStatusType.DELETED.getCode()};
            queryBuilder.add(cs.notIn(QUERY_CRITERIA_STT, (Object[]) ignoreStates));
            
            IdcDate current = null;
            if(isNZDRole)
        	{
            	current = EndOfDayServiceFactory.getEndOfDayService().getCurrentNZDTradeDate();
            	queryBuilder.add(cs.regex(QUERY_CRITERIA_CURRENCYPAIR, StagingAreaConstants.NZD_CP));
        	}
            else
            {
            	current = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            }
            queryBuilder.add(cs.lessThan(QUERY_CRITERIA_VALUE_DATE, current.asJdkDate().getTime())); 
            spaceIterator = queryBuilder.build().getIterator(Order.class); 
            if (spaceIterator != null)
            {
            	while(spaceIterator.hasNext())
            	{
            		Order order = spaceIterator.next();
                	Order cachedOrder = cache.add(order);
                	if (!filterOrder(cachedOrder, order, null, ignoreStates))
                	{
                		returnedOrders.add(cachedOrder);
                	}    
            	}
            }  
            for ( Order order : returnedOrders )
            {
           		// if it's then only send Expired Execution report to client
            	WorkflowMessage message = MessageFactory.newWorkflowMessage();
            	message.setTopic(ISCommonConstants.MSG_TOPIC_ORDER);
            	message.setEvent(MessageEvent.EXPIRE);
            	message.setObject(order);
            	WorkflowMessage resp = OrderStagingServiceFactory.getOrderStagingService().process(message);
            	MessageStatus status = resp.getStatus();
            	if (MessageStatus.FAILURE.equals(status))
            	{
            		log.error("StagingAreaPersistenceService.markOrdersExpired():Marking Expired failed for Order :ClientOrderID:" + 
            												order.getClientOrderId() +":OrderID:" + order.get_id());
            		String error =  resp.getErrorMessage();
            		if (error != null)
            		{
            		    ErrorMessage errorMessage = MessageFactory.newErrorMessage();                	       
            	        errorMessage.setErrorCode(error);                	        
            			if (null == errors)
            			{
            				errors = new ArrayList<ErrorMessage>();
            			}
            			errors.add(errorMessage);
            		}
            	}
            	else
            	{
            		log.info("StagingAreaPersistenceService.markOrdersExpired():Marking Expired sucessful for Order :ClientOrderID:" + 
            												order.getClientOrderId() +":OrderID:" + order.get_id());
            	}
            }
            return errors;
        }
        catch ( Exception ex )
        {
            log.error("StagingAreaPersistenceService.markPastTradeDateOrdersExpired():Problem with Expiring Orders at End of Day:Reason:" + ex.getMessage(), ex);
        }
        finally
        {
        	closeIterator(spaceIterator);
        }
        return null;
    }

}
