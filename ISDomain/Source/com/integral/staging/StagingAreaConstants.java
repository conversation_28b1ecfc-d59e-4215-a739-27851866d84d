package com.integral.staging;

/**
 * Created by avinashpandit on 8/15/14.
 */
public class StagingAreaConstants
{
    public static final String EXPIRED = "EXPIRED";
    public static final String DONE = "DONE";
    public static final String CANCELLED = "CANCELLED";
    public static final String REJECTED = "REJECTED";
    public static final String TYPE = "type";
    public static final String PFID = "pfID";
    public static final String QUERY_ORDER_IDS = "queryOrderIds";
    public static final String SOURCE = "Source";
    public static final String FIX = "FIX";
    public static final String PORTFOLIO = "PTFLO";
    public static final String OPERATION = "OPR";
    public static final String COUNT = "COUNT";
    public static final String READ_ORDERS = "READ_ORDERS";
    public static final String IGNORE_STATES = "IGNORE_STATES";
    public static final String STATES = "qAS";
    public static final String POP_IP = "importPopUp";
    public static final String EMPTY_STRING = "";
    public static final String NZD_CP = ".*NZD.*";
    public static final String ONEPLUSCHAR = ".*";
    public static final String SACheifDealerPerm = "StagingAreaCDView";
    public static final String PERSIST_USE_OBJECT = "USE_OBJECT";
    public static final String ERROR_MSG_PORTFOLIO_CLEAR = "Please clear previous Portfolio";
    public static final String EXCLUDE_AUTO_EXECUTION = "ExcludeAutoExec";
    public static final String STAGING_ORDER_SOURCE_FIX = "FIX";
    public static final String RST = "RST";
    public static final int FIX_FIELD_EXECUTION_STRATEGY = 10011;
    public static final int EXECUTION_STRATEGY_AUTO = 1;
    public static final int EXECUTION_STRATEGY_TRADER_MANAGED = 2;
    public static final int FIX_TAG_NO_BROKER_RESTRICTIONS = 10000;
    public static final int FIX_TAG_BROKER_RESTRICTION = 10001;
    public static final int FIX_TAG_ERISA = 6499;
    public static final String ERISA = "ERISA";
    public static final int FIX_TAG_40ACT = 6455;
    public static final String _40ACT = "40ACT";
    public static final int FIX_TAG_REPORTING_INTERMEDIARY_ROLE = 123;
	public static final char FIX_TAG_REPORTING_INTERMEDIARY_SOURCE = 'G';
	public static final int FIX_TAG_REPEATING_GROUP_TRD_REG_PUBLICATION = 2668;
	public static final int FIX_TAG_TRD_REG_PUBLICATION_TYPE = 2669;
	public static final char FIX_TAG_TRD_REG_PUBLICATION_TYPE_PRE_TRADE_VALUE = 0;
	public static final char FIX_TAG_TRD_REG_PUBLICATION_TYPE_POST_TRADE_VALUE = 1;
	public static final int FIX_TAG_TRD_REG_PUBLICATION_REASON = 2670;
	public static final int FIX_TAG_TRADE_PRICE_CONDITION = 1839;
	public static final int FIX_TAG_TRADING_CAPACITY = 29;
	public static final int FIX_TAG_TRADE_REPORTING_STATUS = 2524;
	public static final int FIX_TAG_REG_TRADE_ID_TYPE = 1906;
	public static final int FIX_TAG_REG_TRADE_ID_TYPE_VALUE = 5;
	public static final int FIX_TAG_REG_TRADE_ID = 1903;	
	public static final String MIFID_PARAMS_TRADING_VENUE = "tv";
	public static final String MIFID_PARAMS_LEGAL_ENTITY= "le";
	public static final String MIFID_PARAMS_INVESTMENT_DECISION_MAKER = "idm";
	public static final String MIFID_PARAMS_TRADING_CAPACITY = "tc";
	public static final String MIFID_PARAMS_PRETRADE_DEFERRAL = "prd";
	public static final String MIFID_PARAMS_POSTTRADE_DEFERRAL = "pod";
	public static final String MIFID_PARAMS_POSTTRADE_INDICATOR = "pti";
	public static final String MIFID_PARAMS_TRADE_REPORTING_STATUS = "trs";
	public static final String MIFID_PARAMS_APA_IDENTIFIER = "apa";
	public static final String MIFID_PARAMS_TRANSACTION_ID = "ti";
	public static final String MIFID_PARAMS_ISIN = "in";
	public static final int EXECUTION_REPORT_BENCHMARKRATE_ORDER_SUBMISSION_TIME = 1;
	public static final int EXECUTION_REPORT_BENCHMARKRATE_ORDER_WEIGHTAGE_AVERAGE = 2;
	public static final int EXECUTION_REPORT_BENCHMARKRATE_ORDER_ALLOCATION_TIME = 3;
    public static final String MIFID_PARAMS_RTN = "rtn";

}
