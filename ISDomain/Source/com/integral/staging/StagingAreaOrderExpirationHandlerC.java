package com.integral.staging;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.staging.config.StagingServiceConfig;

public class StagingAreaOrderExpirationHandlerC implements MessageHandler {

	protected Log log = LogFactory.getLog( StagingAreaOrderExpirationHandlerC.class );
	 
	public Message handle(Message message) {
		try
        {
			String orgs = StagingServiceConfig.getInstance().getEligibleOrgsForEODOrderExpiry();
	        if (orgs == null || "".equals(orgs.trim()))
	        {
	        	log.info("StagingAreaOrderExpirationHandlerC.handle():No org is configured for EndOfDay order expiry.");
	            return null;
	        }
	            
	        String[] orgArray = orgs.split(",");            
	        for (String org : orgArray)
	        {
				log.warn( "StagingAreaOrderExpirationHandlerC.handle(): Marking all the expired orders at the end of business day for NameSpace:" + org);
	            StagingAreaPersistenceService.markPastTradeDateOrdersExpired(org.trim(), false);
	        }

        }
        catch ( Exception e )
        {
            log.error( "StagingAreaOrderExpirationHandlerC.handle() : Error marking orders as expired.", e );
        }
        return message;
	}
}
