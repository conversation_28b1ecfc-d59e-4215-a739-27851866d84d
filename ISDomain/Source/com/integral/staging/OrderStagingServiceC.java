/**
 * <AUTHOR>
 */
package com.integral.staging;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.staging.config.StagingServiceConfig;
import com.integral.staging.model.UpdateOrderRequestList;
import com.integral.commons.Tuple;
import com.integral.fix.client.handler.ApplicationHandler;
import com.integral.fix.client.util.FixUtilC;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.message.ErrorMessageC;
import com.integral.message.Message;
import com.integral.message.MessageEvent;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.Namespace;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.user.User;
import com.integral.user.UserFactory;

import static com.integral.fix.client.FixConstants.StagingOrderCancelRejectReason.*;
@SuppressWarnings("unchecked")
public class OrderStagingServiceC implements OrderStagingService
{
	Log log = LogFactory.getLog(this.getClass());
	protected static ISMBean isMBean = ISFactory.getInstance().getISMBean();

	protected OrderStagingServiceC()
	{
	}

	/**
	 * @param message
	 * @return
	 */
	public WorkflowMessage process( WorkflowMessage message )
	{
		String topic = message.getTopic();
		MessageEvent event = message.getEvent();
		if ( !ISCommonConstants.MSG_TOPIC_ORDER_BATCH.equals(topic)  && !ISCommonConstants.MSG_TOPIC_ORDER.equals(topic))
		{
			WorkflowMessage resp = MessageFactory.newWorkflowMessage(null, event, topic);
			resp.setStatus(MessageStatus.FAILURE);
			resp.addError("Unsupported message topic");
			message.setReplyMessage(resp);
			return message;
		}

		if ( MessageEvent.CREATE.equals(event) )
		{
			WorkflowMessage replyMsg = submitOrderBatch(message);
			StagingAreaUtils.processAlertConditionOnOrderSubmission(replyMsg, null);
			message.setReplyMessage(replyMsg);
			return message;
		}
		else if ( MessageEvent.CANCEL.equals(event) )
		{
			message.setReplyMessage(cancelOrder(message));
			return message;
		}
		else if ( MessageEvent.REPLACE.equals(event) )
		{
			message.setReplyMessage(replaceOrder(message));
			return message;
		}
		else if ( MessageEvent.UPDATE.equals(event) ) // changes order's state
		{
			message.setReplyMessage(updateOrders(message));
			return message;
		}
        else if ( MessageEvent.UPDATEALL.equals(event) ) // changes order's state
        {
            message.setReplyMessage(updateAllOrders(message));
            return message;
        }
		else if ( MessageEvent.LOCKORDERS.equals(event) ) // changes order's state
		{
			message.setReplyMessage(lockOrders(message));
			return message;
		}
		else if ( MessageEvent.UNLOCKORDERS.equals(event) ) // changes order's state
		{
			message.setReplyMessage(unlockOrders(message));
			return message;
		}
		//else if( "QUERY".equals(event.getName()) )
		else if ( MessageEvent.READ.equals(event) )
		{
            String operation =  (String) message.getParameterValue( StagingAreaConstants.OPERATION );
            if(operation == null || StagingAreaConstants.READ_ORDERS.equals( operation ))
            {
                message.setReplyMessage(queryOrder(message));
            }
            else if(StagingAreaConstants.COUNT.equals( operation ))
            {
                message.setReplyMessage(countOrders( message ));
            }
            else{
                WorkflowMessage resp = MessageFactory.newWorkflowMessage(null, event, topic);
                resp.setStatus(MessageStatus.FAILURE);
                resp.addError("Unsupported READ operation - " + operation);
                message.setReplyMessage(resp);
            }
			return message;
		}
		
		else if ( MessageEvent.EXECUTE.equals(event))
		{
			message.setReplyMessage(handleTradeNotification(message, event));
			return message;
		}
		else if(MessageEvent.REJECT.equals(event))
		{
			message.setReplyMessage(handleTradeNotification(message, event));
			return message;
		}
		else if(MessageEvent.EXPIRE.equals(event))
		{
			message.setReplyMessage(handleTradeNotification(message, event));
			return message;
		}
        else if ( MessageEvent.DELETE.equals(event) )
        {
            message.setReplyMessage(deleteOrders( message ));
            return message;
        }
		else if ( MessageEvent.REPLAY.equals(event) )
		{
			message.setReplyMessage(replayOrders( message ));
			return message;
		}
		else
		{
			WorkflowMessage resp = MessageFactory.newWorkflowMessage(null, event, topic);
			resp.setStatus(MessageStatus.FAILURE);
			resp.addError("Unsupported message event");
			message.setReplyMessage(resp);
			return message;
		}
	}


	private User getSessionUser(final Order order, final User user)
	{
		User sessionUser = order.getSessionUser();		
		if (sessionUser == null)
		{
			String userName = user.getName();
			String sessionUserName = order.getSessionUserName();
			if (sessionUserName == null ||  
					"".equals(sessionUserName.trim()) || sessionUserName.equals(userName))
			{
				sessionUser = user; 
			}
			else
			{
				String sessionUserfullname = sessionUserName + '@' + order.getNamespaceName();
				sessionUser = UserFactory.getUser(sessionUserfullname);
			}
			order.setSessionUser(sessionUser);
		}
		return sessionUser;
	}

	/**
	 * @param message
	 * @param event
	 * @return
	 */
	private Message handleTradeNotification( WorkflowMessage message, MessageEvent event )
	{
		Object object = message.getObject();
		Order order  = null;
		if (object instanceof Order)
		{
			order  = (Order)object;
		}
		
		if (order == null) 
		{
			log.error("OSS.handleTradeNotification():Order null on Event :" + event);
			message.addError("ORDER_NOT_FOUND");
			return message;
		}
		User sessionUser = null;
		boolean sendExpiredExecReportToClient = true;
		synchronized(order)
		{
			if( MessageEvent.EXECUTE.equals(event) )
			{
				order.setState(OrderStatusType.EXECUTED.getCode());
			}
			else if (MessageEvent.REJECT.equals(event))
			{
				order.setState(OrderStatusType.REJECTED.getCode());
			}
			else if (MessageEvent.EXPIRE.equals(event))
			{
				order.setState(OrderStatusType.EXPIRED.getCode());
				String org = order.getNamespaceName();
				sendExpiredExecReportToClient = StagingServiceConfig.getInstance().sendExpiredExecutionReport(org);
			}
			else
			{
				log.error("OSS.handleTradeNotification():Event type processing not handled :" + event);
				message.addError("EVENT_TYPE_NOT_SUPPORTED");
				return message;
			}
			// use the session user for sending notification to client
			String userName = order.getUserName();
			String userfullname = userName + '@' + order.getNamespaceName();
			User user = UserFactory.getUser(userfullname);
			order.setUser(user);				
			sessionUser = getSessionUser(order, user);	
			// set trade date on order
			FixUtilC.setTradeDate(order);
			ErrorMessage em = StagingAreaPersistenceService.updateOrder(order, order.getOrderBatchId());
			if( em != null  )
			{
				log.warn("OSS.handleTradeNotification:failed to save order.Id=" + order.get_id());
				message.addError(em);
				//return message;
			}
		}
		if( sessionUser == null )
		{
			log.info("OSSC.handleTradeNotification Session User is null.oid=" + order.get_id() + ",u=" + order.getUserName() + ",ns=" + order.getNamespaceName());
		}

		
		//ignore errors
		// notify OMS only if channel through which order submitted is "FIX"
		String source = order.getSource();
		if ("FIX".equalsIgnoreCase(source) && sessionUser != null && sendExpiredExecReportToClient)
		{
			notifyOMS(message, sessionUser);
		}
		else
		{
			StringBuilder sb = new StringBuilder();
			sb.append("OSSC.handleTradeNotification():not notifying OMS for Order:ClientOrderID:");
			sb.append(order.getClientOrderId());
			sb.append(":OrderID:");
			sb.append(order.get_id());
			sb.append(":Source:");
			sb.append(source);
			sb.append(":SendExpiredExecReport:");
			sb.append(sendExpiredExecReportToClient);
			log.info(sb.toString());
		}	
		
		message.setStatus(MessageStatus.SUCCESS);
		return message;	
	}


	/**
	 * Fast and convenient method to access order by clOrdId. This mehtod returns most recent order for 
	 * given clOrdId. ClOrdIds are reusable.
	 */
	public Order getNonTerminalStateOrderByClOrdId( String clOrdId, User user )
	{
		List<String> ids = new ArrayList<String>();
		ids.add(clOrdId);
		Integer ignoreStates[] = { OrderStatusType.EXECUTED.getCode(), OrderStatusType.CANCELLED.getCode(),OrderStatusType.REJECTED.getCode() };
		List<Order> orders = StagingAreaPersistenceService.getOrders(ids, user, "coid", false, null, null, ignoreStates);
		if( orders != null && !orders.isEmpty()) 
		{
			return orders.get(0);
		}
		return null;
	}
	
	/**
	 * Fast and convenient method to access order by clOrdId. This mehtod returns most recent order for 
	 * given clOrdId. ClOrdIds are reusable.
	 */
	public Order getOrderByClOrdId( String clOrdId, User user, Integer ignoreStates[] )
	{
		List<String> ids = new ArrayList<String>();
		ids.add(clOrdId);
		List<Order> orders = StagingAreaPersistenceService.getOrders(ids, user, "coid", false, null, null, null);
		if( orders != null && !orders.isEmpty()) 
		{
			return orders.get(0);
		}
		return null;
	}

	/**
	 * Fast and convenient method to access order by clOrdId. This mehtod returns most recent order for
	 * given clOrdId. ClOrdIds are reusable.
	 */
	public Order getOrderByClOrdIdFromPrimaryServer( String clOrdId, User user, Integer ignoreStates[] )
	{
		List<String> ids = new ArrayList<String>();
		ids.add(clOrdId);
		List<Order> orders = StagingAreaPersistenceService.getOrdersFromPrimaryServer(ids, user, "coid", false, null, null, null);
		if( orders != null && !orders.isEmpty())
		{
			return orders.get(0);
		}
		return null;
	}
	
	public Order getOrder(String orderId , Namespace ns)
	{
		Order order = StagingAreaPersistenceService.queryOrderById(orderId, ns.getName());
		return order;
	}
	
	@Override
	public Order getOrder(String orderId, Namespace ns, boolean queryPrimaryServer) 
	{
		Order order = StagingAreaPersistenceService.queryOrderById(orderId, ns.getName(), queryPrimaryServer);	
		return order;
	}
	
	
	
	public Order getOrderByOrdId( String ordID, User user, Integer ignoreStates[] )
	{
		List<String> ids = new ArrayList<String>();
		ids.add(ordID);
		List<Order> orders = StagingAreaPersistenceService.getOrders(ids, user, StagingAreaPersistenceService.QUERY_CRITERIA_ID, false, null, null, null);
		if( orders != null && !orders.isEmpty()) 
		{
			return orders.get(0);
		}
		return null;
	}

	/**
	 * @param message
	 * @return
	 */
	private WorkflowMessage queryOrder( WorkflowMessage message ) throws IllegalArgumentException
	{
		Object objIds = message.getParameterValue(OrderStagingService.QUERY_PARAM_ID);
		if( objIds != null && !( objIds instanceof List<?>) )
		{
			throw new IllegalArgumentException("unknown type for query param");
		}
		
		List<String> ids = (List<String>)objIds;

        Object aStates = message.getParameterValue(OrderStagingService.QUERY_ACCEPTED_STATES);
        if( aStates != null && !( aStates instanceof Integer[]) )
        {
            throw new IllegalArgumentException("unknown type for accepted state param");
        }

        Boolean queryByUser = (Boolean) message.getParameterValue(OrderStagingService.QUERY_LOCKED_USER);
        
        String excludeSource = (String) message.getParameterValue(OrderStagingService.QUERY_SOURCE);

        Integer[] aOrderStates = (Integer[]) aStates;

        User user = message.getSender();

        Integer[] ignoreStates = (Integer[]) message.getParameterValue( StagingAreaConstants.IGNORE_STATES );
		Boolean excludeAutoExecutionB = (Boolean) message.getParameterValue(StagingAreaConstants.EXCLUDE_AUTO_EXECUTION);
		boolean excludeAutoExecution = (excludeAutoExecutionB == null ? false : excludeAutoExecutionB);
		Object rstB =  message.getParameterValue( StagingAreaConstants.RST );
		Boolean rst = null;
		if (rstB instanceof Boolean)
		{
			rst = (Boolean)rstB;
		}		
        List<Order> orders = StagingAreaPersistenceService.getOrders(ids, user, StagingAreaPersistenceService.QUERY_CRITERIA_ID, queryByUser == null ? false : queryByUser ,
				excludeSource, excludeAutoExecution, aOrderStates, ignoreStates, rst);
        
		WorkflowMessage response = MessageFactory.newWorkflowMessage(orders, message.getEvent(), message.getTopic());
		message.setObject(orders);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public List<Order> queryOrder(User user, List<String> states, List<String> ccyPairs, List<String> clientOrderIds, List<String> portfolioIds){
		Map<String, List> filters = new HashMap<String, List>();
		if(states != null)
		{
			filters.put(StagingAreaPersistenceService.QUERY_CRITERIA_STT, states);
		}
		if(ccyPairs != null)
		{
			filters.put(StagingAreaPersistenceService.QUERY_CRITERIA_CURRENCYPAIR, ccyPairs);
		}
		if(clientOrderIds != null)
		{
			filters.put(StagingAreaPersistenceService.QUERY_CRITERIA_COID, clientOrderIds);
		}
		if(portfolioIds != null)
		{
			filters.put(StagingAreaPersistenceService.QUERY_CRITERIA_OBID, portfolioIds);
		}
		return StagingAreaPersistenceService.getOrders(filters, user);
	}


    /**
     * query order from staging area , by default order will be queried from secondary server
     * If order is not received from secondary server then query primary
     If order received from secondary server is not in locked state then query primary server
     If order received from primary server is also not in locked state then log error and still continue the OMS notification work-flow
     * @param orderId
     * @param nameSpace
     * @return
     */
    public Order queryOrder(final String orderId, final Namespace nameSpace)
    {
        Order order = OrderStagingServiceFactory.getOrderStagingService().getOrder( orderId, nameSpace);
        if (order != null)
        {
            Integer ordStateInt = order.getState();
            if (ordStateInt != null && OrderStatusType.INUSE.getCode() != ordStateInt.intValue())
            {
                log.info("OSSC.queryOrder():Order is not in locked state so querying from primary server:" + orderId);
                Order primaryOrder = OrderStagingServiceFactory.getOrderStagingService().getOrder( orderId, nameSpace, true);
                if (primaryOrder != null)
                {
                    order = primaryOrder;
                }
                else
                {
                    log.warn("OSSC.queryOrder():Order could not be queried from primary server:" + orderId);
                }
            }
        }
        else
        {
            log.info("OSSC.queryOrder():Order is not found so querying from primary server:" + orderId);
            order = OrderStagingServiceFactory.getOrderStagingService().getOrder( orderId, nameSpace, true);
        }
        if (order != null)
        {
            Integer ordStateInt = order.getState();
            if (ordStateInt != null && OrderStatusType.INUSE.getCode() != ordStateInt.intValue())
            {
                log.error("OSSC.queryOrder():Order queried from Staging Area is not in locked state but still processing OMS notification:" + orderId);
            }
        }
        return order;
    }

    /**
     * @param message
     * @return
     */
    private WorkflowMessage countOrders( WorkflowMessage message ) throws IllegalArgumentException
    {
        User user = message.getSender();
        Long count = StagingAreaPersistenceService.getOrdersCount( user );
        WorkflowMessage response = MessageFactory.newWorkflowMessage(count, message.getEvent(), message.getTopic());
        message.setObject(count);
        return response;
    }

	/**
	 * @param message
	 * @return
	 */
	private WorkflowMessage updateOrders( WorkflowMessage message )
	{		
		WorkflowMessage resp = MessageFactory.newWorkflowMessage( message.getObject(), message.getEvent(), message.getTopic() );
		com.integral.staging.model.UpdateOrderRequestList updateParams = ( com.integral.staging.model.UpdateOrderRequestList ) message.getObject();
		User user = message.getSender();
		boolean queryOrderIds = (Boolean) message.getParameterValue("queryOrderIds");
		String pfID = (String) message.getParameterValue("pfID");
		Map<String, ErrorMessage> errorMap = StagingAreaPersistenceService.updateOrders( updateParams, user, pfID, queryOrderIds, false );
		if ( errorMap != null && errorMap.size() > 0)
		{
			resp.setStatus(MessageStatus.FAILURE);
			resp.addErrors(errorMap.values());
			StringBuilder sb = new StringBuilder();			
			for (ErrorMessage errorMessage : errorMap.values())
			{
				if (errorMessage != null)
				{
					sb.append(errorMessage.getErrorMessage());
					sb.append(":");
				}
			}
			if (log.isDebugEnabled()) 
			{
				log.debug("OSS.updateOrders- update failed for orders:" + updateParams +":Errors:" + sb.toString());
			}
			else
			{
				log.info("OSS.updateOrders- update failed for orders for user:" + user +":Errors:" + sb.toString());
			}			
		}
		else
		{
			if (log.isDebugEnabled())
			{
				log.debug("OSS.updateOrders- updated orders :" + updateParams);
			}
			else
			{
				log.info("OSS.updateOrders- updated orders for user:" + user);
			}
			
		}
		return resp;
	}

	/**
	 * @param message
	 * @return
	 */
	private WorkflowMessage lockOrders( WorkflowMessage message )
	{		
		WorkflowMessage resp = MessageFactory.newWorkflowMessage( message.getObject(), message.getEvent(), message.getTopic() );
		List<Order> orderList = (List<Order>) message.getObject();
		User user = message.getSender();
		if (orderList == null || orderList.size() == 0) 
		{
			log.info("OSS.lockOrders - OrderList null/empty, nothing to update");
		}		
		boolean useObject = false;
		Object useObj = message.getParameterValue("USE_OBJECT");
		if (useObj instanceof Boolean) 
		{
			useObject = ((Boolean)useObj).booleanValue();
		}
		Collection<ErrorMessage> errors = null;
		String pfID = (String) message.getParameterValue("pfID");
		if (useObject)
		{
			errors = StagingAreaPersistenceService.updateOrdersForUser( orderList, user, pfID, true );
		}
		else 
		{
			com.integral.staging.model.UpdateOrderRequestList orderUpdates = new com.integral.staging.model.UpdateOrderRequestList();
			for (Order order : orderList) 
			{			
				com.integral.staging.model.UpdateOrderRequest uor = new com.integral.staging.model.UpdateOrderRequest();
				uor.setUpdateType( OrderStatusType.INUSE );
				uor.setClientOrderId(order.get_id());
				orderUpdates.add(uor);
			}
			Map<String, ErrorMessage> errorMap = StagingAreaPersistenceService.updateOrders(orderUpdates, user, pfID, true, false );
			if (errorMap != null && errorMap.size() > 0)
			{
				// locking of orders failed and reason may be that query from secondary mongo server did not give updated record 
				// so trying once with primary server
				StringBuilder sb = new StringBuilder();
				sb.append("OSS.lockOrders - Lock failed for orders:");
				sb.append(errorMap.keySet());
				sb.append(":Errors:");
				sb.append(getErrorMessage(errorMap.values()));
				sb.append(":Initiating lock once after querying primary mongo server");
				log.warn(sb.toString());
				orderUpdates = new com.integral.staging.model.UpdateOrderRequestList();
				for (String orderId : errorMap.keySet())
				{
					com.integral.staging.model.UpdateOrderRequest uor = new com.integral.staging.model.UpdateOrderRequest();
					uor.setUpdateType( OrderStatusType.INUSE );
					uor.setClientOrderId(orderId);
					orderUpdates.add(uor);
				}
				errorMap = StagingAreaPersistenceService.updateOrders(orderUpdates, user, pfID, true, true );
				if (errorMap != null && errorMap.size() > 0)
				{
					errors = errorMap.values();
				}
			}
		}
		if ( errors != null )
		{
			resp.setStatus(MessageStatus.FAILURE);
			resp.addErrors(errors);
			log.warn("OSS.lockOrders - Lock failed for orders:" + orderList + ":Errors:" + getErrorMessage(errors));
		}
		else
		{
			log.info("OSS.lockOrders - Locked orders:" + orderList);
		}
		return resp;

	}
	
	private String getErrorMessage(final Collection<ErrorMessage> errors )
	{
		if (errors == null) return null;
		StringBuilder sb = new StringBuilder();
		for (ErrorMessage errorMessage : errors)
		{
			if (errorMessage != null)
			{
				sb.append(errorMessage.getErrorCode());
				sb.append(":");
			}
		}
		return sb.toString();
	}

	/**
	 * @param message
	 * @return
	 */
	private WorkflowMessage unlockOrders( WorkflowMessage message )
	{
		WorkflowMessage resp = MessageFactory.newWorkflowMessage( message.getObject(), message.getEvent(), message.getTopic() );
		List<Order> orderList = (List<Order>) message.getObject();
		User user = message.getSender();
		if (orderList == null || orderList.size() == 0) 
		{
			log.info("OSS.unlockOrders - OrderList null/empty, nothing to update");
		}
		boolean useObject = false;
		Object useObj = message.getParameterValue("USE_OBJECT");
		if (useObj instanceof Boolean) {
			useObject = ((Boolean)useObj).booleanValue();
		}
		Collection<ErrorMessage> errors = null;
		//passing blank value for portfolioid
		if (useObject)
		{
			errors = StagingAreaPersistenceService.updateOrdersForUser(orderList, user, "" , false );
		}
		else 
		{
			com.integral.staging.model.UpdateOrderRequestList orderUpdates = new com.integral.staging.model.UpdateOrderRequestList();
			for (Order order : orderList) 
			{			
				com.integral.staging.model.UpdateOrderRequest uor = new com.integral.staging.model.UpdateOrderRequest();
				uor.setUpdateType( OrderStatusType.INTIAL );
				uor.setClientOrderId(order.get_id());
				orderUpdates.add(uor);
			}
			Map<String, ErrorMessage> errorMap = StagingAreaPersistenceService.updateOrders(orderUpdates, user,"", true, false);
			if (errorMap != null && errorMap.size() > 0)
			{
				// locking of orders failed and reason may be that query from secondary mongo server did not give updated record 
				// so trying once with primary server
				StringBuilder sb = new StringBuilder();
				sb.append("OSS.unlockOrders - Unlock failed for orders:");
				sb.append(errorMap.keySet());
				sb.append(":Errors:");
				sb.append(getErrorMessage(errorMap.values()));
				sb.append(":Initiating Unlock once after querying primary mongo server");
				log.warn(sb.toString());
				orderUpdates = new UpdateOrderRequestList();
				for (String orderId : errorMap.keySet())
				{
					com.integral.staging.model.UpdateOrderRequest uor = new com.integral.staging.model.UpdateOrderRequest();
					uor.setUpdateType( OrderStatusType.INTIAL );
					uor.setClientOrderId(orderId);
					orderUpdates.add(uor);
				}
				errorMap = StagingAreaPersistenceService.updateOrders(orderUpdates, user, "", true, true );
				if (errorMap != null && errorMap.size() > 0)
				{
					errors = errorMap.values();
				}
			}
		}

		if ( errors != null )
		{
			resp.setStatus(MessageStatus.FAILURE);
			resp.addErrors(errors);
			log.warn("OSS.unlockOrders - Unlock failed for orders:" + orderList + ":Errors:" + getErrorMessage(errors));
		}
		else
		{
			log.info("OSS.unlockOrders - Unlocked orders:" + orderList);
		}
		return resp;
	}

	/**
     * @param message
     * @return
     */
    private WorkflowMessage updateAllOrders( WorkflowMessage message )
    {        
        WorkflowMessage resp = MessageFactory.newWorkflowMessage(message.getObject(), message.getEvent(), message.getTopic());
        Boolean state = (Boolean) message.getObject();
        User user = message.getSender();
        List<ErrorMessage> errors = StagingAreaPersistenceService.updateAllOrdersForUser( user, state );
        if ( errors != null )
        {
            resp.setStatus(MessageStatus.FAILURE);
            resp.addErrors(errors);
            log.info("OSS.updateAllOrders- Update failed for all orders for user :" + user);
        }
        else
        {
            log.info("OSS.updateAllOrders- Updated all orders for user:" + user);
        }
        return resp;
    }

    /**
     * @param message
     * @return
     */
    
	private WorkflowMessage deleteOrders( WorkflowMessage message )
    {
        WorkflowMessage resp = MessageFactory.newWorkflowMessage(message.getObject(), message.getEvent(), message.getTopic());
        User user = message.getSender();

		List<String> orderIds = (List<String>) message.getParameterValue( OrderStagingService.QUERY_PARAM_ID);

		List<ErrorMessage> errors = StagingAreaPersistenceService.deleteOrders(orderIds, user );

        if ( errors != null )
        {
            resp.setStatus(MessageStatus.FAILURE);
            resp.addErrors(errors);
            log.info("OSS.deleteOrders- Delete Order Failed for user :" + user + ":Errors:" + errors);
        }
        else
        {
            log.info("OSS.deleteOrders - Deleted orders for user:" + user);
        }
        return resp;
    }

	/**
	 * @param message
	 * @return
	 */
	private WorkflowMessage replayOrders( WorkflowMessage message )
	{
		WorkflowMessage resp = MessageFactory.newWorkflowMessage(message.getObject(), message.getEvent(), message.getTopic());

		User user = message.getSender();

		List<String> orderIds = (List<String>) message.getParameterValue( OrderStagingService.QUERY_PARAM_ID);

		if ( user == null )
		{
			log.warn( "OSS.replayOrders - Unable to replay Staging orders. User passed is null." );
			return null;
		}

		List<Order> ordersToCheck;
		List<ErrorMessage> errors = null;		
		Integer[] ignoreState = new Integer[] {OrderStatusType.DELETED.getCode(), OrderStatusType.DOWNLOADED.getCode() };
		ordersToCheck = StagingAreaPersistenceService.getOrders(orderIds, user, "_id", false, null, null, ignoreState);
		if(ordersToCheck.size() == 0 || ordersToCheck.size() != orderIds.size())
		{
			log.error( "SAQS.replayOrders - Unable to query specified orders. Hence unable to perform REPLAY operation." );
			ErrorMessage error = MessageFactory.newErrorMessage();
			error.setErrorCode( "UNABLE_TO_REPLAY_SELECTED_ORDERS" );
			errors = new ArrayList<ErrorMessage>();
			errors.add( error );
		}
		else
		{
			for ( Order order : ordersToCheck )
			{
				String spotRateStr = order.getSpotRate();
				String forwardPointStr = order.getFwdPts();
				if ( spotRateStr != null )
				{
					BigDecimal spotRateB = new BigDecimal( spotRateStr );
					double spotRate = spotRateB.doubleValue();
					order.setSpotRateD( spotRate );
				}
				if ( forwardPointStr != null )
				{
					BigDecimal forwardPointB = new BigDecimal( forwardPointStr );
					double forwardPoint = forwardPointB.doubleValue();
					order.setFwdPtsD( forwardPoint );
				}
				WorkflowMessage rspMsg = MessageFactory.newWorkflowMessage();
				rspMsg.setTopic( ISCommonConstants.MSG_TOPIC_ORDER );
				rspMsg.setObject( order );
				ApplicationHandler handler = FixUtilC.getInstance().getFixConfiguration().getApplicationHandler();
				User sessionUser = getSessionUser(order, user);
				boolean isSent = handler.handleStagedOrderNotification( rspMsg, sessionUser );
				if ( !isSent )
				{
					log.error( "SAQS.replayOrders Failed for Order.id=" + order.get_id() + ", cloid=" + order.getClientOrderId() );
					if ( null == errors )
					{
						errors = new ArrayList<ErrorMessage>();
					}
					ErrorMessage errorMessage = MessageFactory.newErrorMessage();
					errorMessage.setErrorCode( "ERROR_REPLAY_ORDER_" + order.getId() );
					errors.add( errorMessage );
				}
			}
		}
		if ( errors != null )
		{
			resp.setStatus(MessageStatus.FAILURE);
			resp.addErrors(errors);
			log.info("OSS.replayOrders- REPLAY Order Failed for user :" + user + ":Errors:" + errors);
		}
		else
		{
			log.info("OSS.replayOrders - REPLAYed orders for user:" + user);
		}
		return resp;
	}


    /**
	 * @param message
	 * @return
	 */
	private WorkflowMessage replaceOrder( WorkflowMessage message )
	{
		long st = System.currentTimeMillis();
		WorkflowMessage resp = MessageFactory.newWorkflowMessage(message.getObject(), message.getEvent(), message.getTopic());
		Order odr = (Order) message.getObject();
		User user = message.getSender();

		if ( odr == null )
		{
			resp.setStatus(MessageStatus.FAILURE);
			resp.addError(ErrorCode.NULL_ORDER_NOT_ALLOWED.name());
			return resp;
		}

		if ( odr.getUserName() == null && user == null )
		{
			resp.setStatus(MessageStatus.FAILURE);
			resp.addError(ErrorCode.USER_REQUIRED_FOR_SAVE.name());
			return resp;
		}
		
		Order replacableOrder = StagingAreaPersistenceService.getOrderUsingCoid( (String)message.getParameterValue("ClientOrderId"), user.getNamespace());
		
		if(replacableOrder == null)
		{
			resp.setStatus(MessageStatus.FAILURE);
			resp.addError(ErrorCode.ORDER_NOT_FOUND.name());
			return resp;
		}
		
		odr.setValueDate(replacableOrder.getValueDate());

		WorkflowMessage cancelledMsg = cancelOrder(message);
		if ( cancelledMsg != null && cancelledMsg.getStatus() != null && cancelledMsg.getStatus() == MessageStatus.FAILURE )
			return cancelledMsg;

		if ( odr.getUserName() == null && user != null )
        {
			odr.setUser(user);
        }
		
		String id = StageOrderIdProvider.getInstance().nextId();
		odr.set_id(id);
		odr.setOrderBatchId(replacableOrder.getOrderBatchId());
		odr.setState(OrderStatusType.INTIAL.getCode());
		String virtualServer = RuntimeFactory.getServerRuntimeMBean().getVirtualServer().getShortName();
		odr.setVirtualServer(virtualServer);
		odr.setSource((String) message.getParameterValue("Source"));
		ErrorMessage error = StagingAreaPersistenceService.submitOrder(odr, odr.getOrderBatchId());
		resp.setObject(cancelledMsg.getObject()); // return the older order
		if ( error != null )
		{
			resp.setStatus(MessageStatus.FAILURE);
			resp.addError(error);
			log.info("OSS.cancelOrder- Order replacement failed. coid=" + odr.getClientOrderId() + ",oid=" + odr.get_id() + ",errorCode=" + error);
		}
		else
		{
			log.info("OSS.cancelOrder- Order replaced. coid=" + odr.getClientOrderId() + ",oid=" + odr.get_id() + ",prt" + (System.currentTimeMillis() - st));
		}
		return resp;
	}

	/**
	 * @param message
	 * @return
	 */
	private WorkflowMessage cancelOrder( WorkflowMessage message )
	{
		long st = System.currentTimeMillis();
		User user = message.getSender();
		boolean isClientOids = false;
		List<String> ids;
		String cloid = (String) message.getParameterValue(ISCommonConstants.CLIENT_ORDER_ID);
		if(cloid == null)
		{
			//try getting List of orderids
			ids = (List<String>) message.getParameterValue( OrderStagingService.QUERY_PARAM_ID);
		}
		else
		{
			isClientOids = true;
			ids = new ArrayList<String>();
			ids.add(cloid);
		}

		//Integer ignoreStates[] = { OrderStatusType.CANCELLED.getCode() };
		List<Order> orders;
		if(isClientOids)
		{
			orders = StagingAreaPersistenceService.getOrders(ids, user, StagingAreaPersistenceService.QUERY_CRITERIA_COID, false, null, null, null);
		}
		else
		{
			orders = StagingAreaPersistenceService.getOrders(ids, user, StagingAreaPersistenceService.QUERY_CRITERIA_ID, false, null, null, null);
		}

		WorkflowMessage resp = MessageFactory.newWorkflowMessage(message.getObject(), message.getEvent(), message.getTopic());

		if(isClientOids)
		{
			if ( orders == null || orders.size() != 1 )
			{
				resp.setStatus( MessageStatus.FAILURE );
				ErrorMessage err = new ErrorMessageC();

				if ( orders == null || orders.size() <= 0 )
				{
					err.setCode( ErrorCode.ORDER_NOT_FOUND.name() );
				}
				else
				{
					err.setCode( ErrorCode.MULTIPLE_ORDERS_FOUND.name() );
				}

				resp.addError( err );
				return resp;
			}

			Order order = orders.get(0);
			resp.setObject(order);
			if ( cancelOrder( message, resp, order ) )
			{
				log.info("OSS.cancelOrder- Order cancelled. coid=" + order.getClientOrderId() + ",oid=" + order.get_id() + ",prt" + (System.currentTimeMillis() - st));
			}
			else
			{
				log.info("OSS.cancelOrder- Unable to cancel Order. coid=" + order.getClientOrderId() + ",oid=" + order.get_id() + ",prt" + (System.currentTimeMillis() - st));
			}

			return resp;

		}
		else
		{
			if(orders == null || orders.size() != ids.size())
			{
				resp.setStatus( MessageStatus.FAILURE );
				ErrorMessage err = new ErrorMessageC();
				err.setCode( ErrorCode.MULTIPLE_ORDERS_FOUND.name() );
				resp.addError( err );
				return resp;
			}

			for(Order order : orders)
			{
				cancelOrder( message, resp, order );
			}

		}
		return resp;
	}

	private boolean cancelOrder( WorkflowMessage message, WorkflowMessage resp, Order order )
	{
		synchronized(order)
		{
			Tuple<Boolean, String> orderTerminalState = getOrderTerminalStateInfo(order);

			if (orderTerminalState != null && orderTerminalState.first)
	        {
	            resp.addError(orderTerminalState.second);
				log.info("OSS.cancelOrder- Order cancellation failed. coid=" + order.getClientOrderId() + ",oid=" + order.get_id() + ",errorCode=" + orderTerminalState.second);
				return false;
	        }

			order.setState( OrderStatusType.CANCELLED.getCode());
			order.setSource((String) message.getParameterValue("Source"));
			ErrorMessage error = StagingAreaPersistenceService.cancelOrder(order, order.getOrderBatchId());

			if ( error != null )
	        {
	            resp.setStatus( MessageStatus.FAILURE);
	            resp.addError(error);
	            log.info("OSS.cancelOrder- Order cancellation failed. coid=" + order.getClientOrderId() + ",oid=" + order.get_id() + ",errorCode=" + error);
				return false;
	        }
			return true;
		}
	}

	private Tuple<Boolean, String> getOrderTerminalStateInfo(final Order order)
	{
		if (order == null) return null;
		if(OrderStatusType.INUSE.getCode() == order.getState())
		{
			return new Tuple<Boolean, String>(true, STAGING_ORDER_CANCEL_REJECT_REASON_ORDER_IS_IN_EXECUTION.getText());
		}
		
		if(OrderStatusType.EXECUTED.getCode() == order.getState())
		{
			return new Tuple<Boolean, String>(true, STAGING_ORDER_CANCEL_REJECT_REASON_ORDER_IS_FILLED.getText());
		}
		
		if(OrderStatusType.REJECTED.getCode() == order.getState())
		{
			return new Tuple<Boolean, String>(true, STAGING_ORDER_CANCEL_REJECT_REASON_ORDER_IS_REJECTED.getText());
		}
		
		if(OrderStatusType.DELETED.getCode() == order.getState())
		{
			return new Tuple<Boolean, String>(true, STAGING_ORDER_CANCEL_REJECT_REASON_ORDER_IS_DELETED.getText());
		}
		
		if(OrderStatusType.EXPIRED.getCode() == order.getState())
		{
			return new Tuple<Boolean, String>(true, STAGING_ORDER_CANCEL_REJECT_REASON_ORDER_IS_EXPIRED.getText());
		}
		
		if(OrderStatusType.CANCELLED.getCode() == order.getState())
		{
			return new Tuple<Boolean, String>(true, STAGING_ORDER_CANCEL_REJECT_REASON_ORDER_IS_ALREADY_CANCELLED.getText());
		}
		return null;
	}

	/**
	 * @param message
	 * @return
	 */
	private WorkflowMessage submitOrderBatch( WorkflowMessage message )
	{
		OrderBatch orderBatch = new OrderBatch();
		WorkflowMessage resp = MessageFactory.newWorkflowMessage(message.getObject(), message.getEvent(), message.getTopic());
		
		if(message.getObject() instanceof Order)
		{
			List<Order> orderList = new ArrayList<Order>(1);
			List<String> orderIDList = new ArrayList<String>(1);
			Order order = (Order) message.getObject();			
			if(order.getState() == null) order.setState(OrderStatusType.INTIAL.getCode());
			if(order.getSource() == null) order.setSource("FIX");
			order.setCreatedBusinessDate(System.currentTimeMillis());
			order.setCreatedTime(order.getCreatedBusinessDate());
			order.setModifiedTime(System.currentTimeMillis());
			
			orderList.add(order);
			orderIDList.add(order.get_id());
			orderBatch.setOrderList(orderList); 
			orderBatch.setOrderIDList(orderIDList);
			
			if(orderBatch.getNamespace() == null)
			{
				orderBatch.setNamespace(order.getNamespace());
				orderBatch.setNamespaceName(order.getNamespaceName());
			}
			
			User user = message.getSender();
			if ( user != null )
	        {
				order.setUser( user );
	        }

			WorkflowMessage response = OrderStagingValidator.validateStagingOrderSubmit( message );

			if(response.getStatus() == MessageStatus.FAILURE)
			{
				//validation error , send rejection
				log.warn( "OSS.submitOrderBatch - OrderStagingValidator validation Failed. " + response.getErrorMessage() );
				return response;
			}

		}
		else
		{
			orderBatch = (OrderBatch) message.getObject();
		}
		
		long st = System.currentTimeMillis();
		
		User user = message.getSender();

		String id = updateOrderBatchWithReferenceData( orderBatch, user );
		ErrorMessage error = StagingAreaPersistenceService.submitOrderBatch( orderBatch, id );

		if ( error != null )
		{
			resp.setStatus( MessageStatus.FAILURE );
			resp.addError( error );
			log.info( "OSS.submitOrder- Order batch submission failed for orders :" + orderBatch.getOrderList() + ":errorCode:" + error );
		}
		else
		{
			log.info( "OSS.submitOrder- Order batch Submitted for orders :" + orderBatch.getOrderList() + ":Time(milis):" + ( System.currentTimeMillis() - st ) );
		}
		return resp;
	}

	private String updateOrderBatchWithReferenceData( OrderBatch orderBatch, User user )
	{
		String id = StageOrderIdProvider.getInstance().nextId();
		orderBatch.set_id( id );

		if ( user != null )
		{
			orderBatch.setUser( user );
			orderBatch.setUserName( user.getName() );

			if ( orderBatch.getNamespace() == null )
			{
				orderBatch.setNamespace( user.getNamespace() );
				orderBatch.setNamespaceName( user.getNamespace().getName() );
			}
		}

		List<Order> orderList = orderBatch.getOrderList();
		if(orderList != null && orderList.size() > 0)
		{
			for(Order order : orderList)
			{
				updateOrderWithReferenceData(order);
			}
		}

		String virtualServer = RuntimeFactory.getServerRuntimeMBean().getVirtualServer().getShortName();
		orderBatch.setVirtualServer( virtualServer );
		return id;
	}

	private void updateOrderWithReferenceData( Order order )
	{
		if(order != null && order.getUser() != null)
		{
			LegalEntity le = FixUtilC.getInstance().getCounterparty( order.getAccount(), order.getUser().getOrganization() );

			if(le != null && le.isErisaFund())
			{
				if (order.isErisa()) {
					order.setErisa(true);
				} else if (order.isFortyAccount()) {
					order.setFortyAccount(true);
				} else {
					// backward compatible
					order.setErisa(true);
				}
				log.info( "OSS.submitOrder- Erisa account " + order.getAccount() + " order - " + order.getId() );
			}
			else
			{
				log.info( "OSS.submitOrder-  No Erisa account " + order.getAccount() + " order - " + order.getId() );
			}
		}
	}

	
	private static boolean notifyOMS( final Message message, User user )
	{
		try
		{
			boolean isSent = true;
			ApplicationHandler handler = FixUtilC.getInstance().getFixConfiguration().getApplicationHandler();
			isSent = handler.handleStagedOrderNotification((WorkflowMessage) message, user);
			return isSent;
		}
		catch ( Exception e )
		{
			e.printStackTrace();
			return false;
		}
	}
	
}
