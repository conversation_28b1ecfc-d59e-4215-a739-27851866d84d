package com.integral.staging;

import java.util.HashMap;

public enum OrderBatchStatusType
{
	/**
	 * Draft - Newly submitted order Batch will in this state.
	 * SEMIDRAFT - Some orders in the order Batch will be in Draft state
	 * NODRAFT - No draft orders in the order batch
	 */
	DRAFT(1), SEMIDRAFT(2);

	OrderBatchStatusType( int c )
	{
		this.code = c;
	}

	public int code;

	public int getCode()
	{
		return code;
	}

	public static OrderBatchStatusType fromCode( int code )
	{
		return codeToEnum.get(code);
	}

	private static HashMap<Integer, OrderBatchStatusType> codeToEnum = new HashMap<Integer, OrderBatchStatusType>();
	static
	{
		for ( OrderBatchStatusType e : OrderBatchStatusType.values() )
			codeToEnum.put(e.getCode(), e);
	}
}