/**
 * <AUTHOR>
 */
package com.integral.staging;

import com.integral.staging.config.StagingServiceConfig;
import com.integral.staging.config.StagingServiceConfigMBean;

/**
 * Factory returns an instance of {@link OrderStagingService}.
 * <AUTHOR>
 *
 */
public class OrderStagingServiceFactory
{
	private OrderStagingServiceFactory()
	{
	}

	private static OrderStagingService orderStagingService = new OrderStagingServiceC();		


	public static OrderStagingService getOrderStagingService()
	{
		return orderStagingService;
	}
	
	public static StagingServiceConfigMBean getConfigMBean()
	{
		return StagingServiceConfig.getInstance();
	}

}
