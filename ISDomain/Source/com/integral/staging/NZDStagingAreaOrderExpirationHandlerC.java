package com.integral.staging;

import com.integral.exception.IdcException;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.staging.config.StagingServiceConfig;

public class NZDStagingAreaOrderExpirationHandlerC implements MessageHandler{
	
	protected Log log = LogFactory.getLog( NZDStagingAreaOrderExpirationHandlerC.class );

	public Message handle(Message message) throws IdcException {
		try
        {
			String orgs = StagingServiceConfig.getInstance().getEligibleOrgsForEODOrderExpiry();
	        if (orgs == null || "".equals(orgs.trim()))
	        {
	        	log.info("NZDStagingAreaOrderExpirationHandlerC.handle():No org is configured for EndOfDay order expiry.");
	            return message;
	        }
	            
	        String[] orgArray = orgs.split(",");
	        for (String org : orgArray)
	        {
	        	log.warn( "NZDStagingAreaOrderExpirationHandlerC.handle(): Marking all the expired orders at the end of business day for NameSpace:" + org );
	        	StagingAreaPersistenceService.markPastTradeDateOrdersExpired(org.trim(), true);
	        }
        }
        catch ( Exception e )
        {
            log.error( "NZDStagingAreaOrderExpirationHandlerC.handle.ERROR : Error marking orders as expired.", e );
        }
        return message;
	}

}
