package com.integral.staging;

import java.util.Collection;
import java.util.Map;

public interface StagingOrderCache {
	
	/**
	 * get all the orders for a namespace
	 * @param namespace
	 * @return
	 */
	Collection<Order> getOrdersForNamespace(String namespace);
	
	
	/**
	 * get all the specified orders
	 * @param namespace
	 * @param ids
	 * @return
	 */
	Map<String, Order> getOrders(String namespace, Collection<String> ids);
	
	/**
	 * 
	 * @param namespace
	 * @param id
	 * @return
	 */
	Order getOrder(String namespace, String id);
	
	/**
	 * 
	 * @param namespace
	 * @param user
	 * @param id
	 * @return
	 */
	Order getOrder(String namespace, String user, String id);
	
	/**
	 * get all the specified orders for a namespace and user
	 * @param namespace
	 * @param user
	 * @param ids
	 * @return
	 */
	Map<String, Order> getOrders(String namespace, String user, Collection<String> ids);
	
	/**
	 * Returns all non terminal state orders for a namespace that satisfies the state criteria
	 * @param namespace
	 * @param states
	 * @return
	 */
	Collection<Order> getOrdersForNamespace(String namespace, Collection<Integer> states);
	
	/**
	 * returns all non terminal state orders for a namespace and user
	 * It's callers responsibility to handle chief dealer usecase
	 * @param namespace
	 * @param user
	 * @return
	 */
	Collection<Order> getOrdersForUser(String namespace, String user);
	
	/**
	 * returns all non terminal state orders for a namespace and user that satisfies the states filter
	 * It's callers responsibility to handle chief dealer usecase
	 * @param namespace
	 * @param user
	 * @param states
	 * @return
	 */
	Collection<Order> getOrdersForUser(String namespace, String user, Collection<Integer> states);
	
	/**
	 * @param order
	 * @return --> return the first added order
	 */
	Order add(Order order);
	
	/**
	 * Add collection of orders to cache
	 * @param orders --> returns the collection of firstly added orders
	 */
	Collection<Order> add(Collection<Order> orders);
	
	/**
	 * Add all orders for a namespace
	 * Use this method only if you are sure that all the orders belong to 
	 * same namespace
	 * @param namespace
	 * @param orders
	 * @return
	 */
	Collection<Order> add(String namespace, Collection<Order> orders);
	
	/**
	 * add all orders for a namespace and user
	 * use this method only if you are sure that all the orders belong to same namespace and user
	 * @param namespace
	 * @param user
	 * @param orders
	 * @return
	 */
	Collection<Order> add(String namespace, String user, Collection<Order> orders);
	
	/**
	 * Remove order from cache
	 * @param order
	 * @return
	 */
	Order remove(Order order);
	
	/**
	 * remove Order for a namespace and user
	 * @param id
	 * @return
	 */
	Order remove(String namespace, String user, String id);
	
	/**
	 * remove collection of orders for a namespace and user
	 * @param namespace
	 * @param user
	 * @param ids
	 * @return
	 */
	Collection<Order> remove(String namespace, String user, Collection<String> ids);
	
	/**
	 * remove all the orders for a namespace
	 * @param namespace
	 * @return
	 */
	Collection<Order> removeOrdersForNamespace(String namespace);
	
	/**
	 * remove all the orders for a namespace that satisfies the state criteria
	 * @param namespace
	 * @param states
	 * @return
	 */
	Collection<Order> removeOrdersForNamespace(String namespace, Collection<Integer> states);
	
	/**
	 * Remove all the orders for a namespace and user
	 * @param namespace
	 * @param user
	 * @return
	 */
	Collection<Order> removeOrdersForUser(String namespace, String user);
	
	/**
	 * remove all the orders for a namespace and user for a specified states
	 * @param namespace
	 * @param user
	 * @param states
	 * @return
	 */
	Collection<Order> removeOrdersForUser(String namespace, String user, Collection<Integer> states);
	
	/**
	 * Igonore Order from caching
	 * @param order
	 * @return
	 */
	boolean ignoreCaching(Order order);
	
	/**
	 * Schedule Order removal from cache
	 * @param order
	 * @return
	 */
	boolean scheduleOrderRemoval(Order order);

}
