package com.integral.staging;

import com.integral.persistence.TransactionIdService;
import com.integral.persistence.TransactionIdServiceFactory;

/**
 * This class is used to generate the staging-area ids. 
 * Currently it uses order id sequence.
 */
public class StageOrderIdProvider
{

	static TransactionIdService tidService = TransactionIdServiceFactory.getTransactionIdService();
	protected static StageOrderIdProvider _instance = new StageOrderIdProvider();

	protected StageOrderIdProvider()
	{
	}

	public static StageOrderIdProvider getInstance()
	{
		return _instance;
	}

	/**
	 * Returns the next ID for the specified schema.
	 * IDs are guaranteed to be unique within a schema.
	 *
	 * @return a unique ID for the specified schema
	 */
	public String nextId()
	{
		return tidService.getOrderID("IS");
	}

}
