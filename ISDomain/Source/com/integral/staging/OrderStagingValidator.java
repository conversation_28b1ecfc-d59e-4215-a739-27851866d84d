package com.integral.staging;

import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.currency.Currency;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.fix.client.util.FixUtilC;
import com.integral.is.common.fi.SupportedCurrencyPairUtil;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.netting.AutoExecutionStrategyType;
import com.integral.netting.NettingCoreUtils;
import com.integral.netting.NettingMBean;
import com.integral.netting.NettingMBeanC;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.services.definition.IFixService;
import com.integral.session.UserServiceManager;
import com.integral.staging.config.StagingServiceConfig;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import org.joda.time.LocalTime;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

//import static com.integral.staging.StagingAreaConstants.ERISA;


/**
 * Created by avinashpandit on 5/19/15.
 */
public class OrderStagingValidator
{
    private static final Log log = LogFactory.getLog( OrderStagingValidator.class );
    public static final int MAX_CLIENTID_LENGTH = 100;

    //todo : Add error message logs
    public static WorkflowMessage validateStagingOrderSubmit( WorkflowMessage requestMsg )
    {
        Order order;

        WorkflowMessage resp = MessageFactory.newWorkflowMessage( requestMsg.getObject(), requestMsg.getEvent(), requestMsg.getTopic() );

        if ( requestMsg.getObject() == null || !( requestMsg.getObject() instanceof Order ) )
        {
            resp.setStatus( MessageStatus.FAILURE );
            resp.addError( ErrorCode.INTERNAL_SERVER_ERROR.name() );
            return resp;
        }
        else
        {
            order = ( Order ) requestMsg.getObject();
        }

        if ( order.isOmsOrder() )
        {
            //For now donot do any validation for OMS orders
            return resp;
        }

        if(!validateClientOrderId( order, resp )){
            return resp;
        }

        User user = requestMsg.getSender();

        if ( user == null )
        {
            resp.setStatus( MessageStatus.FAILURE );
            resp.addError( ErrorCode.USER_REQUIRED_FOR_SAVE.name() );
            return resp;
        }

        //validate Amount
        if ( !validateAmount( order, resp ) )
        {
            return resp;
        }
        //validate CC
        User orderUser = order.getUser();
        String orgname = orderUser.getOrganization().getShortName();

        if ( !validateAccountAndCC( order, orgname, resp ) )
        {
            return resp;
        }

        if ( !validateCcyPairAndValueDate( order, resp, user ) )
        {
            return resp;
        }
        boolean autoExection = order.isAutoExecution();
        //validate active user
        if ( autoExection && !validateActiveUser( order, orgname, resp ) )
        {
            return resp;
        }
        if (autoExection && !validateAutoExecution( resp, orderUser, order ) )
        {
            return resp;
        }
        
        /* Check if this user-cloid combination exists already */
        Order existingOrder = StagingAreaPersistenceService.getOrderUsingCoid( order.getClientOrderId(), order.getNamespace() );
        if ( existingOrder != null )
        {
            resp.setStatus( MessageStatus.FAILURE );
            resp.addError( ErrorCode.ORDER_EXISTS_ALREADY.name() );
            return resp;
        }

        return resp;
    }

    private static boolean validateClientOrderId(Order order,WorkflowMessage resp)
    {
        if ( order.getClientOrderId() == null )
        {
            resp.setStatus( MessageStatus.FAILURE );
            resp.addError( ErrorCode.CLOID_CANNOT_BE_NULL.name() );
            return false;
        }

        int clidLen = order.getClientOrderId().trim().length();
        if( clidLen == 0 )
        {
            resp.setStatus( MessageStatus.FAILURE );
            resp.addError( ErrorCode.CLOID_CANNOT_BE_BLANK.name() );
            return false;
        }

        if( clidLen > MAX_CLIENTID_LENGTH )
        {
            resp.setStatus( MessageStatus.FAILURE );
            resp.addError( ErrorCode.CLOID_EXCEEDS_MAX_LENGTH_LIMITS.name() );
            return false;
        }
        return true;
    }

    private static boolean validateCcyPairAndValueDate( Order order, WorkflowMessage resp, User user )
    {
        FXRateConvention fxRateConvention = QuoteConventionUtilC.getInstance().getFXRateConvention( user.getOrganization() );
        if ( fxRateConvention != null )
        {
            //validate ccy pair
            FXRateBasis rateBasis = fxRateConvention.getFXRateBasis( order.getCcyPair() );
            if ( rateBasis == null )
            {
                resp.setStatus( MessageStatus.FAILURE );
                resp.addError( ErrorCode.INVALID_CURRENCYPAIR.name() );
                return false;
            }
            else
            {
                //validate ccypair
                String orgName = user.getOrganization().getShortName();
                if (StagingServiceConfig.getInstance().validateCurrencyPair(orgName))
                {
                    if ( !validateCcypair( order, user ) )
                    {
                        resp.setStatus( MessageStatus.FAILURE );
                        resp.addError( ErrorCode.INVALID_CURRENCYPAIR.name() );
                        return false;
                    }
                }
                //validate valuedate
                if ( order.getValueDate() != null && order.getValueDate() > 0 )
                {
                    IdcDate vDate = DateTimeFactory.newDate( new Date( order.getValueDate() ) );
                    if ( vDate.isEarlierThan( EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate() ) )
                    {
                        resp.setStatus( MessageStatus.FAILURE );
                        resp.addError( ErrorCode.INVALID_VALUE_DATE_EARLIER_THAN_TRADEDATE.name() );
                        return false;
                    }
                    //check if org enabled for only spot orders
                    if ( NettingMBeanC.getInstance().isOnlySpotTradingEnabled( user.getOrganization().getShortName() ) )
                    {
                        //validate if order is spot
                        IdcDate spotDate = rateBasis.getSpotDate( EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate() );
                        if ( !vDate.isSameAs( spotDate ) )
                        {
                            resp.setStatus( MessageStatus.FAILURE );
                            resp.addError( ErrorCode.NON_SPOT_VALUE_DATE.name() );
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    private static boolean validateCcypair( Order order, User user )
    {
        Collection<String> ccyPairs;
        Set<String> ccs = order.getCcSet();
        String orderCurrencyPair = order.getCcyPair();
        if ( ccs == null || ccs.size() == 0 )
        {
            ccyPairs = SupportedCurrencyPairUtil.getSupportedCurrencyPairsStr( user.getOrganization() );
            return ( ccyPairs != null && ccyPairs.contains( orderCurrencyPair ) );
        }
        for ( String cc : ccs )
        {
            Organization org = ReferenceDataCacheC.getInstance().getOrganization( cc );
            if ( org != null )
            {
                ccyPairs = SupportedCurrencyPairUtil.getSupportedCurrencyPairs( user.getOrganization(), org );
                if ( ccyPairs != null && !ccyPairs.contains( orderCurrencyPair ) )
                {
                    log.warn( "OrderStagingValidator.validateCcypair(): Currency Pair " + orderCurrencyPair + " not supported by PB :" + org );
                    return false;
                }
            }
        }
        return true;
    }

    private static boolean validateAmount( Order order, WorkflowMessage resp )
    {
        double dealtAmt = order.getDealtAmt();
        Currency ccy = order.getDealtCurrency();
        if ( ccy == null )
        {
            log.warn( "OrderStagingValidator.validateAmount : Dealt Amount validation can not be processed. Dealt Ccy is NULL." );
            resp.setStatus( MessageStatus.FAILURE );
            resp.addError( ErrorCode.INVALID_DEALT_CCY.name() );
            return false;
        }
        else
        {
            if ( !( BigDecimal.valueOf( dealtAmt ).remainder( BigDecimal.valueOf( ccy.getTickValue() ) ).doubleValue()
                    == 0.0 ) || dealtAmt < ccy.getTickValue() )
            {
                log.warn( "OrderStagingValidator.validateAmount : Dealt Amount validation failed. DealtAmt : " + dealtAmt );
                resp.setStatus( MessageStatus.FAILURE );
                resp.addError( ErrorCode.INVALID_DEALT_AMOUNT.name() );
                return false;
            }
        }
        return true;
    }

    private static boolean validateAutoExecution( WorkflowMessage resp, User orderUser, Order order )
    {
        Organization org = orderUser.getOrganization();
        String orgName = org.getShortName();
        if ( NettingMBeanC.getInstance().isAutoExecutionEnabled( orgName ) )
        {
            String strategyName = NettingMBeanC.getInstance().getAutoExecutionStrategy( org );
            String startTimeStr = null;
            String endTimeStr = null;
            boolean erisaOrder = order.isErisa();
            boolean buyBase = order.isBuyBase();
            boolean inSession = false;
            if ( AutoExecutionStrategyType.ORG.name().equals( strategyName ) )
            {
            	// get all scheduled batched
        		User executionUser = NettingMBeanC.getInstance().getAutoExecutionOrgStrategyExecutionUser(org);    	
        		List<String> executionBatchs = getExecutionBatchIds(order, true, org, executionUser);
    			log.info("OrderStagingValidator.validateAutoExecution:ExecutionBatchId:" + executionBatchs + ":" + order);
    			if (executionBatchs == null || executionBatchs.size() == 0)
    			{
    				resp.setStatus( MessageStatus.FAILURE );
    	            resp.addError( ErrorCode.NO_EXECUTION_BATCH_SCHEDULED.name() );    	               
    				return false;
    			}
    			
       			// at least one batch should be running currently
    			for (String execBatchId : executionBatchs)
    			{
    				startTimeStr = NettingMBeanC.getInstance().getAutoExecutionStartTime( org, execBatchId );
    	            endTimeStr = NettingMBeanC.getInstance().getAutoExecutionEndTime( org, execBatchId );
       	            LocalTime startTime = NettingCoreUtils.getTime( startTimeStr, "00:00:00" );
    	            LocalTime endTime = NettingCoreUtils.getTime( endTimeStr, "23:59:59" );
     	            if ( NettingCoreUtils.isInSession( startTime, endTime ) )
    	            {
    	            	inSession = true;
    	            	break;
    	            }
    			}
    			
            }
            else if ( AutoExecutionStrategyType.USER.name().equals( strategyName ) )
            {
        		List<String> executionBatchs = getExecutionBatchIds(order, false, org, orderUser);
    			log.info("OrderStagingValidator.validateAutoExecution:ExecutionBatchIds:" + executionBatchs + ":" + order);
    			if (executionBatchs == null || executionBatchs.size() == 0)
    			{
    				resp.setStatus( MessageStatus.FAILURE );
    	            resp.addError( ErrorCode.NO_EXECUTION_BATCH_SCHEDULED.name() );    	               
    				return false;
    			}
    			// at least one batch should be running currently
    			for (String execBatchId : executionBatchs)
    			{
    				startTimeStr = NettingMBeanC.getInstance().getAutoExecutionStartTime( orderUser, execBatchId );
    	            endTimeStr = NettingMBeanC.getInstance().getAutoExecutionEndTime( orderUser, execBatchId );
    	            LocalTime startTime = NettingCoreUtils.getTime( startTimeStr, "00:00:00" );
    	            LocalTime endTime = NettingCoreUtils.getTime( endTimeStr, "23:59:59" );
    	            if ( NettingCoreUtils.isInSession( startTime, endTime ) )
    	            {
    	            	inSession = true;
    	            	break; 
    	            }
    			}
 
            }

            if (!inSession)
            {
            	resp.setStatus( MessageStatus.FAILURE );
	            resp.addError( ErrorCode.INVALID_ORDER_SUBMISSION_TIME.name() );
	            return false;
            }
        }
        return true;
    }
    /**
     * return execution batch in which order will be executed.
     * @param order
     * @param orgStrategy
     * @param org
     * @param user
     * @return
     */
    private static List<String> getExecutionBatchIds(final Order order, final boolean orgStrategy, final Organization org, final User user)
    {
    	boolean erisaOrder = order.isErisa();
    	boolean _40ActOrder = order.isFortyAccount();
    	boolean buyBase = order.isBuyBase();
    	List<String> batchIds = new ArrayList<String>();
    	boolean swapOrder = (order.getNoOfLegs() == 2);
    	//String executionBatch = null;
    	if (orgStrategy)
    	{
    		String executionBatchesStr = NettingMBeanC.getInstance().getAutoExecutionBatches(org);
    		if (executionBatchesStr == null || "".equals(executionBatchesStr.trim()))
    		{
    			log.info("OrderStagingValidator.validateAutoExecution:No Batch sceduled for org=" + org.getShortName() + ", strategy=ORG");
    			return null;
    		}
    		String[] executionBatches = executionBatchesStr.split(",");
    		
    		for (String executionBatchId : executionBatches)
    		{
    			executionBatchId = executionBatchId.trim();
    			String startegyImplClass = NettingMBeanC.getInstance().getAutoExecutionStartegyImplClass(org, executionBatchId);
    			if (startegyImplClass != null)
    			{
    				if (NettingMBean.ORG_STARTEGY_ERISA_BUY_BASE_IMPL_CLASS.equals(startegyImplClass) 
    						   && (erisaOrder || _40ActOrder) && buyBase)
    				{
    					batchIds.add(executionBatchId);
    				}
    				else if(NettingMBean.ORG_STARTEGY_ERISA_SELL_BASE_IMPL_CLASS.equals(startegyImplClass)
    						&& (erisaOrder || _40ActOrder) && !buyBase)
    				{
    					batchIds.add(executionBatchId);
    				}
    				else if (NettingMBean.ORG_STARTEGY_NON_ERISA_IMPL_CLASS.equals(startegyImplClass) 
    						&& !(erisaOrder || _40ActOrder))
    				{
    					batchIds.add(executionBatchId);
    				}
    				else if (NettingMBean.ORG_STARTEGY_SWAP_BATCH_IMPL_CLASS.equals(startegyImplClass) && swapOrder)
    				{
    					batchIds.add(executionBatchId);
    				}
                    else if (NettingMBean.ORG_STARTEGY_BATCH_SSP_IMPL_CLASS.equals(startegyImplClass)
                            && !(erisaOrder || _40ActOrder))
                    {
                        batchIds.add(executionBatchId);
                    }
    			}
    		}
    	}
    	else
    	{
       		String executionBatchesStr = NettingMBeanC.getInstance().getAutoExecutionBatches(user);
    		if (executionBatchesStr == null || "".equals(executionBatchesStr.trim()))
    		{
    			log.info("OrderStagingValidator.validateAutoExecution:No Batch scheduled for user=" + user.getFullName() + ", strategy=USER");
    			return null;
    		}
    		String[] executionBatches = executionBatchesStr.split(",");
    		
    		for (String executionBatchId : executionBatches)
    		{
    			executionBatchId = executionBatchId.trim();
    			String startegyImplClass = NettingMBeanC.getInstance().getAutoExecutionStartegyImplClass(user, executionBatchId);
    			if (startegyImplClass != null)
    			{
    				if (NettingMBean.USER_STARTEGY_ERISA_BUY_BASE_IMPL_CLASS.equals(startegyImplClass) 
    						   && (erisaOrder || _40ActOrder) && buyBase)
    				{
    					batchIds.add(executionBatchId);
    				}
    				else if(NettingMBean.USER_STARTEGY_ERISA_SELL_BASE_IMPL_CLASS.equals(startegyImplClass)
    						&& (erisaOrder || _40ActOrder) && !buyBase)
    				{
    					batchIds.add(executionBatchId);
    				}
    				else if (NettingMBean.USER_STARTEGY_NON_ERISA_IMPL_CLASS.equals(startegyImplClass) 
    						&& !(erisaOrder || _40ActOrder))
    				{
    					batchIds.add(executionBatchId);
    				}
    				else if (NettingMBean.USER_STARTEGY_SWAP_BATCH_IMPL_CLASS.equals(startegyImplClass) && swapOrder)
    				{
    					batchIds.add(executionBatchId);
    				}
                    else if (NettingMBean.USER_STARTEGY_BATCH_SSP_IMPL_CLASS.equals(startegyImplClass)
                            && !(erisaOrder || _40ActOrder))
                    {
                        batchIds.add(executionBatchId);
                    }
    			}
    		}
    	}
    	return batchIds;
    }

    private static boolean validateAccountAndCC( Order order, String orgname, WorkflowMessage resp )
    {
        String account = order.getAccount();
        List<AllocationDetail> allocationDetails = order.getAllocationDetails();

        if(allocationDetails != null && allocationDetails.size() > 0)
        {
            for(AllocationDetail allocationDetail : allocationDetails)
            {
                if(!validAccountAndCCPerAccount( order, orgname, resp, allocationDetail.getAccountName() ))
                {
                    return false;
                }
            }
        }
        else if ( account != null && account.length() > 0 )
        {
            return validAccountAndCCPerAccount( order, orgname, resp, account );
        }
        else
        {
            resp.setStatus( MessageStatus.FAILURE );
            resp.addError( ErrorCode.INVALID_ACCOUNT.name() );
            return false;
        }

        return true;
    }

    private static boolean validAccountAndCCPerAccount( Order order, String orgname, WorkflowMessage resp, String account )
    {
    	boolean autoExecution = order.isAutoExecution();
    	boolean erisaOrderFromClient = order.isErisa();
    	boolean _40ActOrderFromClient = order.isFortyAccount();
        LegalEntity le = FixUtilC.getInstance().getCounterparty( account, order.getUser().getOrganization() );
        if ( le == null )
        {
        	ErrorCode ec = ErrorCode.INVALID_ACCOUNT;
        	if (erisaOrderFromClient) {
        		ec = ErrorCode.INVALID_ERISA_FUND_SETUP;
        	} else if (_40ActOrderFromClient) {
        		ec = ErrorCode.INVALID_40ACT_FUND_SETUP;
        	}
            resp.setStatus( MessageStatus.FAILURE );
            resp.addError( ec.name() );
            return false;
        }
        if ( !le.isActive() )
        {
            resp.setStatus( MessageStatus.FAILURE );
            resp.addError( ErrorCode.INACTIVE_ACCOUNT.name() );
            return false;
        }
        boolean erisaLE = le.isErisaFund();
        if (autoExecution) {
        	if (erisaLE ^ (erisaOrderFromClient || _40ActOrderFromClient)) {
        		resp.setStatus( MessageStatus.FAILURE );
        		if (_40ActOrderFromClient) {
        			// if order is 40 account but fund is not setup as no netting
        			resp.addError( ErrorCode.INVALID_40ACT_FUND_SETUP.name() );
        		} else {
        			resp.addError( ErrorCode.INVALID_ERISA_FUND_SETUP.name() );
        		}
                return false;
        	} 
        }
        /*
        if (autoExecution && (erisaLE ^ erisaOrderFromClient))
        {
            resp.setStatus( MessageStatus.FAILURE );
            resp.addError( ErrorCode.INVALID_ERISA_FUND_SETUP.name() );
            return false;
        }
        */
        if ( NettingMBeanC.getInstance().isValidateStagingOrderCC( orgname ) )
        {
            // If it's auto execution order then only valid CC is required
            // If trader managed work flow then CC is not required but if it's present then
            // should be valid            
            Set<String> ccSet = order.getCcSet();
            if ( autoExecution && (ccSet == null || ccSet.size() == 0) )
            {
                resp.setStatus( MessageStatus.FAILURE );
                resp.addError( ErrorCode.INVALID_CC.name() );
                return false;
            }
            if (ccSet != null && ccSet.size() > 0)
            {
            	Set<String> validCCs = new HashSet<String>();
                for ( String selectedCC : ccSet )
                {
                    final Organization cc = ReferenceDataCacheC.getInstance().getOrganization( selectedCC );
                    if ( cc == null || !cc.isActive() )
                    {
                    	log.info("OrderStagingValidator.validAccountAndCCPerAccount:CC org is invalid:" + cc);                        
                        continue;
                    }

                    final User user = order.getUser();
                    final Organization org = user.getOrganization();

                    if ( !CounterpartyUtilC.isRelationshipExist( org, cc ) )
                    {
                    	log.info("OrderStagingValidator.validAccountAndCCPerAccount:CC org doesn't have relationship with FI:" + cc);                       
                        continue;
                    }

                    final LegalEntity ccDefaultLe = cc.getDefaultDealingEntity();
                    if ( ccDefaultLe == null || !ccDefaultLe.isActive() )
                    {
                    	log.info("OrderStagingValidator.validAccountAndCCPerAccount:CC org doesn't have default legal entity set or inactive:" + cc);                       
                        continue;
                    }

                    final User ccDefaultUser = cc.getDefaultDealingUser();
                    if ( ccDefaultUser == null )
                    {
                    	log.info("OrderStagingValidator.validAccountAndCCPerAccount:CC org doesn't have default dealing user set:" + cc);                       
                        continue;
                    }

                    //validate account and CC
                    //validate CC has relationship with default dealing entity
                    TradingParty tp = CounterpartyUtilC.getTradingParty( org.getDefaultDealingEntity(), cc );
                    if ( tp == null || !tp.isActive() )
                    {
                    	log.info("OrderStagingValidator.validAccountAndCCPerAccount:FI's default dealing entity does not have active trading party with CC:" + cc);                       
                        continue;
                    }

                    tp = CounterpartyUtilC.getTradingParty( le, cc );

                    if ( tp == null || !tp.isActive() )
                    {
                    	log.info("OrderStagingValidator.validAccountAndCCPerAccount:FI's legal entity does not have active trading party with CC:" + cc);                        
                        continue;
                    }
                    validCCs.add(selectedCC);
                }
                if (validCCs.size() == 0)
                {      
                	log.info("OrderStagingValidator.validAccountAndCCPerAccount:CC Validation failed.");
                  	resp.setStatus( MessageStatus.FAILURE );
                    resp.addError( ErrorCode.CC_VALIDATION_FAILED.name() );
                    return false;
                }
                else
                {
                	// there is at least on valid CC
                	// For autox set the CC set as valid CC set
                	log.info("OrderStagingValidator.validAccountAndCCPerAccount:Valid CCs:" + validCCs);
                	if (autoExecution)
                	{
                		order.setCcSet(validCCs);
                	}
                }
            }
        }
        return true;
    }


    private static boolean validateActiveUser( Order order, String orgname, WorkflowMessage resp )
    {
        if ( NettingMBeanC.getInstance().isValidateActiveUser( orgname ) )
        {
            if ( !UserServiceManager.getInstance().isUserCurrentlyLoggedInGridAPI( order.getUser() ) )
            {
                resp.setStatus( MessageStatus.FAILURE );
                resp.addError( ErrorCode.NO_ACTIVE_USER.name() );
                return false;
            }
        }
        return true;
    }

}
