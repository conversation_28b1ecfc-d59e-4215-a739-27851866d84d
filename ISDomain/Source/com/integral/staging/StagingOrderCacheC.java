package com.integral.staging;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.staging.config.StagingServiceConfig;
import com.integral.staging.oms.OMSUtil;


public class StagingOrderCacheC implements StagingOrderCache {
	private static final Log LOG = LogFactory.getLog(StagingOrderCacheC.class);
	private List<Integer> ignoreStatesForCaching = Arrays.asList(OrderStatusType.EXECUTED.code,
            													   OrderStatusType.REJECTED.code,
            													   OrderStatusType.CANCELLED.code, 
            													   OrderStatusType.ARCHIVED.code, 
            													   OrderStatusType.DELETED.code, 
            													   OrderStatusType.EXPIRED.code);
	private ConcurrentMap<String, ConcurrentMap<String, ConcurrentMap<String, OrderEntry>>> orderCache = 
								new ConcurrentHashMap<String, ConcurrentMap<String,ConcurrentMap<String,OrderEntry>>>();

	public StagingOrderCacheC()
	{
		 if(OMSUtil.isOMSServerDeployment()) {
			 ignoreStatesForCaching = Arrays.asList(OrderStatusType.REJECTED.code,
					   OrderStatusType.CANCELLED.code, 
					   OrderStatusType.ARCHIVED.code, 
					   OrderStatusType.DELETED.code, 
					   OrderStatusType.EXPIRED.code);
		 }
	}
	
	@Override
	public Collection<Order> getOrdersForNamespace(final String namespace) 
	{
		if (namespace == null)
		{
			return Collections.emptyList();
		}
		List<Order> returnedOrders = new ArrayList<Order>();
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.get(namespace);
		if (namespaceMap == null || namespaceMap.size() == 0)
		{
			return Collections.emptyList();
		}
		for (Map.Entry<String, ConcurrentMap<String, OrderEntry>> entry : namespaceMap.entrySet())
		{
			ConcurrentMap<String, OrderEntry> map = entry.getValue();
			for (Map.Entry<String, OrderEntry> entr : map.entrySet())
			{
				OrderEntry oe = entr.getValue();
				returnedOrders.add(oe.getOrder());
			}
		}
		return returnedOrders;
	}
		

	@Override
	public Map<String, Order> getOrders(final String namespace, final Collection<String> ids) 
	{
		if (namespace == null || ids == null || ids.size() == 0)
		{
			return Collections.emptyMap();
		}	
		
		Map<String, Order> returnedOrders = new HashMap<String, Order>();
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> nameSpaceMap = orderCache.get(namespace);
		if (nameSpaceMap == null || nameSpaceMap.size() == 0)
		{
			return Collections.emptyMap();
		}
		
		for (Map.Entry<String, ConcurrentMap<String, OrderEntry>> entry : nameSpaceMap.entrySet())
		{
			ConcurrentMap<String, OrderEntry> map = entry.getValue();
			for (Map.Entry<String, OrderEntry> entr : map.entrySet())
			{
				String id = entr.getKey();
				OrderEntry oe = entr.getValue();
				if (ids.contains(id))
				{
					returnedOrders.put(id, oe.getOrder());
				}
			}
		}
		return returnedOrders;
	}



	@Override
	public Map<String, Order> getOrders(String namespace, String user, Collection<String> ids) 
	{
		if (namespace == null || ids == null || ids.size() == 0)
		{
			return Collections.emptyMap();
		}
		if (user == null)
		{
			return getOrders(namespace, ids);
		}
		Map<String, Order> returnedOrders = new HashMap<String, Order>();
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.get(namespace);
		if (namespaceMap == null || namespaceMap.size() == 0)
		{
			return Collections.emptyMap();
		}
		ConcurrentMap<String, OrderEntry> userMap = namespaceMap.get(user);
		if (userMap == null || userMap.size() == 0)
		{
			return Collections.emptyMap();
		}
		for (Map.Entry<String, OrderEntry> entry : userMap.entrySet())
		{
			String id = entry.getKey();
			if (ids.contains(id))
			{
				OrderEntry oe = entry.getValue();
				returnedOrders.put(id, oe.getOrder());
			}
		}
		return returnedOrders;
	}



	@Override
	public Collection<Order> getOrdersForNamespace(String namespace, Collection<Integer> states) 
	{
		if (namespace == null)
		{
			return Collections.emptyList();
		}
		if (states == null || states.size() == 0)
		{
			return getOrdersForNamespace(namespace);
		}
		List<Order> returnedOrders = new ArrayList<Order>();
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.get(namespace);
		if (namespaceMap == null || namespaceMap.size() == 0)
		{
			return Collections.emptyList();
		}		
		for (Map.Entry<String, ConcurrentMap<String, OrderEntry>> entry : namespaceMap.entrySet())
		{
			ConcurrentMap<String, OrderEntry> map = entry.getValue();
			for (Map.Entry<String, OrderEntry> entr : map.entrySet())
			{
				OrderEntry oe = entr.getValue();
				Order order = oe.getOrder();
				Integer state = order.getState();
				if (states.contains(state))
				{
					returnedOrders.add(order);
				}					
			}
		}
		return returnedOrders;
	}

	@Override
	public Collection<Order> getOrdersForUser(final String namespace, final String user) 
	{
		if (namespace == null)
		{
			return Collections.emptyList();
		}
		if (user == null)
		{
			return getOrdersForNamespace(namespace);
		}
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.get(namespace);
		if (namespaceMap == null || namespaceMap.size() == 0)
		{
			return Collections.emptyList();
		}
		ConcurrentMap<String, OrderEntry> userMap = namespaceMap.get(user);
		if (userMap == null || userMap.size() == 0)
		{
			return Collections.emptyList();
		}
		List<Order> returnedOrders = new ArrayList<Order>();
		for (Map.Entry<String, OrderEntry> entry : userMap.entrySet())
		{			
			OrderEntry oe = entry.getValue();
			returnedOrders.add(oe.getOrder());
		}		
		return returnedOrders;
	}

	@Override
	public Collection<Order> getOrdersForUser(String namespace, String user, Collection<Integer> states) 
	{
		if (namespace == null)
		{
			return Collections.emptyList();
		}
		if (user == null)
		{
			return getOrdersForNamespace(namespace, states);
		}
		if (states == null || states.size() == 0)
		{
			return getOrdersForUser(namespace, user);
		}
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.get(namespace);
		if (namespaceMap == null || namespaceMap.size() == 0)
		{
			return Collections.emptyList();
		}
		ConcurrentMap<String, OrderEntry> userMap = namespaceMap.get(user);
		if (userMap == null || userMap.size() == 0)
		{
			return Collections.emptyList();
		}
		List<Order> returnedOrders = new ArrayList<Order>();
		for (Map.Entry<String, OrderEntry> entry : userMap.entrySet())
		{			
			OrderEntry oe = entry.getValue();
			Order o = oe.getOrder();
			Integer state = o.getState();
			if (states.contains(state))
			{
				returnedOrders.add(oe.getOrder());
			}			
		}		
		return returnedOrders;
	}
	
	

	@Override
	public Order getOrder(final String namespace, final String id) 
	{
		if (namespace == null || id == null)
		{
			return null;
		}
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.get(namespace);
		if (namespaceMap == null || namespaceMap.size() == 0)
		{
			return null;
		}		
		for (Map.Entry<String, ConcurrentMap<String, OrderEntry>> entry : namespaceMap.entrySet())
		{
			ConcurrentMap<String, OrderEntry> map = entry.getValue();
			OrderEntry orderEntry = map.get(id);
			if (orderEntry != null)
			{
				return orderEntry.getOrder();
			}
		}
		return null;
	}


	@Override
	public Order getOrder(final String namespace, final String user, final String id) 
	{
		if (namespace == null || id == null)
		{
			return null;
		}
		if (user == null)
		{
			return getOrder(namespace, id);
		}
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.get(namespace);
		if (namespaceMap == null || namespaceMap.size() == 0)
		{
			return null;
		}
		ConcurrentMap<String, OrderEntry> userMap = namespaceMap.get(user);
		if (userMap == null || userMap.size() == 0)
		{
			return null;
		}
		OrderEntry oe = userMap.get(id);
		if (oe == null)
		{
			return null;
		}
		return oe.getOrder();
	}

	public boolean ignoreCaching(final Order order)
	{
		if (order == null)
		{
			return true;
		}
		Integer state = order.getState();
		return ignoreStatesForCaching.contains(state);
	}
	@Override
	public Order add(Order order) 
	{
		if (order == null)
		{
			return null;
		}
		if (ignoreCaching(order))
		{
			return order;
		}
		String namespace = order.getNamespace().getName();
		String user = order.getUserName();
		if (namespace == null || user == null)
		{
			throw new IllegalArgumentException("Namespace or User NULL.");
		}
		ConcurrentMap<String,OrderEntry> userMap = getUserMap(namespace, user);
		String id = order.getId();
		OrderEntry entry = userMap.get(id);
		if (entry == null)
		{
			entry = new OrderEntry(order);
			OrderEntry returnedValue = userMap.putIfAbsent(id, entry);
			// some other thread already updated
			if (returnedValue != null)
			{
				entry = returnedValue;
			}
		}
		return entry.getOrder();
	}

	@Override
	public Collection<Order> add(final Collection<Order> orders) 
	{
		if (orders == null || orders.size() == 0)
		{
			return Collections.emptyList();
		}
		List<Order> returnedOrders = new ArrayList<Order>();
		for (Order order : orders)
		{
			if (order != null)
			{
				returnedOrders.add(add(order));
			}			
		}		
		return returnedOrders;
	}
	
	

	@Override
	public Collection<Order> add(String namespace, final Collection<Order> orders) 
	{
		if (namespace == null)
		{
			return add(orders);
		}
		if (orders == null || orders.size() == 0)
		{
			return Collections.emptyList();
		}
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = getNamespaceMap(namespace);
		List<Order> returnedOrders = new ArrayList<Order>();
		for (Order order : orders)
		{
			if (order != null)
			{
				if (ignoreCaching(order))
				{
					returnedOrders.add(order);
				}
				else
				{
					String user = order.getUserName();
					ConcurrentMap<String,OrderEntry> userMap = namespaceMap.get(user);
					if (userMap == null)
					{
						userMap = new ConcurrentHashMap<String, StagingOrderCacheC.OrderEntry>();
						ConcurrentMap<String,OrderEntry> returnedValue = namespaceMap.putIfAbsent(user, userMap);
						// some other thread already updated
						if (returnedValue != null)
						{
							userMap = returnedValue;
						}
					}
					String id = order.getId();
					OrderEntry entry = userMap.get(id);
					if (entry == null)
					{
						entry = new OrderEntry(order);
						OrderEntry returnedValue = userMap.putIfAbsent(id, entry);
						// some other thread already updated
						if (returnedValue != null)
						{
							entry = returnedValue;
						}
					}
					returnedOrders.add(entry.getOrder());
				}
			}
		}
		return returnedOrders;
	}

	@Override
	public Collection<Order> add(String namespace, String user, final Collection<Order> orders) 
	{
		if (user == null)
		{
			return add(namespace, orders);
		}
		if (namespace == null)
		{
			// then not considering user
			return add(orders);
		}
	
		if (orders == null || orders.size() == 0)
		{
			return Collections.emptyList();
		}

		ConcurrentMap<String,OrderEntry> userMap = getUserMap(namespace, user);
		List<Order> returnedOrders = new ArrayList<Order>();
		for (Order order : orders)
		{
			if (order != null)
			{
				if (ignoreCaching(order))
				{
					returnedOrders.add(order);
				}
				else
				{
					String id = order.getId();
					OrderEntry entry = userMap.get(id);
					if (entry == null)
					{
						entry = new OrderEntry(order);
						OrderEntry returnedValue = userMap.putIfAbsent(id, entry);
						// some other thread already updated
						if (returnedValue != null)
						{
							entry = returnedValue;
						}
					}
					returnedOrders.add(entry.getOrder());
				}
			}
		}
		return returnedOrders;
	}

	private ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> getNamespaceMap(final String namespace)
	{
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.get(namespace);
		if (namespaceMap == null)
		{
			namespaceMap = new ConcurrentHashMap<String, ConcurrentMap<String,OrderEntry>>();
			ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> returnedValue = orderCache.putIfAbsent(namespace, namespaceMap);
			// some other thread already updated
			if (returnedValue != null)
			{
				namespaceMap = returnedValue;
			}
		}
		return namespaceMap;
	}

	private ConcurrentMap<String,OrderEntry> getUserMap(final String namespace, String user)
	{
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.get(namespace);
		if (namespaceMap == null)
		{
			namespaceMap = new ConcurrentHashMap<String, ConcurrentMap<String,OrderEntry>>();
			ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> returnedValue = orderCache.putIfAbsent(namespace, namespaceMap);
			// some other thread already updated
			if (returnedValue != null)
			{
				namespaceMap = returnedValue;
			}
		}
		ConcurrentMap<String,OrderEntry> userMap = namespaceMap.get(user);
		if (userMap == null)
		{
			userMap = new ConcurrentHashMap<String, StagingOrderCacheC.OrderEntry>();
			ConcurrentMap<String,OrderEntry> returnedValue = namespaceMap.putIfAbsent(user, userMap);
			// some other thread already updated
			if (returnedValue != null)
			{
				userMap = returnedValue;
			}
		}
		return userMap;
	}
	
	@Override
	public Order remove(Order order) 
	{
		if (order == null)
		{
			return null;
		}
		String namespace = order.getNamespace().getName();
		String user = order.getUserName();
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.get(namespace);
		if (namespaceMap == null)
		{
			return null;
		}
		ConcurrentMap<String,OrderEntry> userMap = namespaceMap.get(user);
		if (userMap == null)
		{
			return null;
		}
		String id = order.getId();
		OrderEntry entry = userMap.remove(id);
		if (entry != null)
		{
			return entry.getOrder();
		}
		return null;
	}

	@Override
	public Order remove(final String namespace, final String user, final String id) 
	{
		if (namespace == null || user == null || id == null)
		{
			return null;
		}
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.get(namespace);
		if (namespaceMap == null)
		{
			return null;
		}
		ConcurrentMap<String,OrderEntry> userMap = namespaceMap.get(user);	
		if (userMap == null)
		{
			return null;
		}
		OrderEntry entry = userMap.remove(id);
		if (entry != null)
		{
			return entry.getOrder();
		}
		return null;
	}

	@Override
	public Collection<Order> remove(final String namespace, final String user, final Collection<String> ids) 
	{
		if (namespace == null || user == null || ids == null || ids.size() == 0)
		{
			return Collections.emptyList();
		}
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.get(namespace);
		if (namespaceMap == null || namespaceMap.size() == 0)
		{
			return Collections.emptyList();
		}
		ConcurrentMap<String,OrderEntry> userMap = namespaceMap.get(user);	
		if (userMap == null || userMap.size() == 0)
		{
			return Collections.emptyList();
		}
		List<Order> returnedOrders = new ArrayList<Order>();
		for (String id : ids)
		{
			if (id != null)
			{
				OrderEntry returnedOrderEntry = userMap.remove(id);
				if (returnedOrderEntry != null)
				{
					returnedOrders.add(returnedOrderEntry.getOrder());
				}
			}
		}
		return returnedOrders;
	}

	@Override
	public Collection<Order> removeOrdersForNamespace(final String namespace) 
	{
		if (namespace == null)			
		{
			return Collections.emptyList();
		}
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.remove(namespace);
		List<Order> returnedOrders = new ArrayList<Order>();
		if (namespaceMap != null)
		{
			for (Map.Entry<String, ConcurrentMap<String, OrderEntry>> entry : namespaceMap.entrySet())
			{
				ConcurrentMap<String, OrderEntry> map = entry.getValue();
				for (Map.Entry<String, OrderEntry> entr : map.entrySet())
				{					
					OrderEntry oe = entr.getValue();
					returnedOrders.add(oe.getOrder());
				}
			}
		}
		return returnedOrders;
	}

	@Override
	public Collection<Order> removeOrdersForNamespace(String namespace, Collection<Integer> states) 
	{
		if (namespace == null)
		{
			return Collections.emptyList();
		}
		if (states == null || states.size() == 0)
		{
			return removeOrdersForNamespace(namespace);
		}
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.get(namespace);
		if (namespaceMap == null || namespaceMap.size() == 0)
		{
			return Collections.emptyList();
		}
		List<Order> returnedOrders = new ArrayList<Order>();
		for (Map.Entry<String, ConcurrentMap<String, OrderEntry>> entr : namespaceMap.entrySet())
		{
			ConcurrentMap<String, OrderEntry> userMap = entr.getValue();
			List<String> keysTobeRemoved = new ArrayList<String>();
			for (Map.Entry<String, OrderEntry> entry : userMap.entrySet())
			{
				OrderEntry entryValue = entry.getValue();
				Order order = entryValue.getOrder();
				Integer orderState = order.getState();
				if (states.contains(orderState))
				{						
					keysTobeRemoved.add(entry.getKey());
				}					
			}
			for (String key : keysTobeRemoved)
			{
				OrderEntry entry = userMap.remove(key);
				if (entry != null)
				{
					returnedOrders.add(entry.getOrder());
				}
			}
		}
		return returnedOrders;
	}

	@Override
	public Collection<Order> removeOrdersForUser(final String namespace, final String user) {
		if (namespace == null && user == null)
		{
			return Collections.emptyList();
		}
		if (user == null)
		{
			return removeOrdersForNamespace(namespace);
		}
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.get(namespace);
		if (namespaceMap == null || namespaceMap.size() == 0)
		{
			return Collections.emptyList();
		}
		ConcurrentMap<String, OrderEntry> userMap = namespaceMap.remove(user);
		if (userMap == null || userMap.size() == 0)
		{
			return Collections.emptyList();
		}
		List<Order> returnedOrders = new ArrayList<Order>();
		for (Map.Entry<String, OrderEntry> entry : userMap.entrySet())
		{
			OrderEntry oe = entry.getValue();
			returnedOrders.add(oe.getOrder());
		}
		return returnedOrders;
	}

	@Override
	public Collection<Order> removeOrdersForUser(final String namespace, final String user, final Collection<Integer> states) 
	{
		if (states == null || states.size() == 0)
		{
			return removeOrdersForUser(namespace, user);
		}
		
		if (namespace == null)
		{
			return Collections.emptyList();
		}
		if (user == null)
		{
			return removeOrdersForNamespace(namespace, states);
		}
		ConcurrentMap<String, ConcurrentMap<String, OrderEntry>> namespaceMap = orderCache.get(namespace);
		if (namespaceMap == null)
		{
			return Collections.emptyList();
		}
		ConcurrentMap<String, OrderEntry> userMap = namespaceMap.get(user);
		if (userMap == null)
		{
			return Collections.emptyList();
		}
		List<String> keysTobeRemoved = new ArrayList<String>();
		List<Order> returnedOrders = new ArrayList<Order>();
		for (Map.Entry<String, OrderEntry> entry : userMap.entrySet())
		{
			OrderEntry oe = entry.getValue();
			Order order = oe.getOrder();
			Integer orderState = order.getState();
			if (states.contains(orderState))
			{
				keysTobeRemoved.add(entry.getKey());
			}
		}
		for (String key : keysTobeRemoved)
		{
			OrderEntry removedEntry = userMap.remove(key);
			if (removedEntry != null)
			{
				returnedOrders.add(removedEntry.getOrder());
			}
		}
		return returnedOrders;
	}



	@Override
	public boolean scheduleOrderRemoval(final Order order) 
	{
		if (order == null)
		{
			return true;
		}
		String nameSpace = order.getNamespaceName();
		String user = order.getUserName();
		String id = order.getId();
		if (getOrder(nameSpace, user, id) == null)
		{
			LOG.info("StagingOrderCacheC.scheduleOrderRemoval():order is not in cahce so removeal is not scheduled for Order:" + id);
			return true;
		}
		try 
		{
			long delay = StagingServiceConfig.getInstance().getTimeToLiveInCacheForTerminalStateOrder();
			ScheduledExecutorService resourceManagementService = StagingAreaUtils.getResourceManagementService();
			if (resourceManagementService == null)
			{
				LOG.warn("StagingOrderCacheC.scheduleOrderRemoval():ResouceManagementService NULL:" + order.getId());
				return false;
			}
			resourceManagementService.schedule(new EvictTask(order), delay, TimeUnit.MILLISECONDS);
			LOG.info("StagingOrderCacheC.scheduleOrderRemoval():order removal scheduled from cache after mili seconds:" + delay + " for "+ order.getId());
			return true;
		}
		catch(Throwable e)
		{
			LOG.warn("StagingOrderCacheC.scheduleOrderRemoval():problem with scheduling order removal from cache:" + order.getId(), e);
			return false;
		}		
	}


	private class EvictTask implements Runnable{
		private Order order;
		public EvictTask(final Order order)
		{
			this.order = order;
		}
		@Override
		public void run() 
		{
			try 
			{
				Order removedOrder = remove(this.order);
				if (removedOrder != null)
				{
					LOG.info("StagingOrderCacheC.EvictTask.run():removed order from cache:" + removedOrder.getId());
				}
				else
				{
					LOG.info("StagingOrderCacheC.EvictTask.run():removed order from cache :Null+:Request Order for removal:" + this.order.getId());
				}
			}
			catch(Throwable e)
			{
				LOG.warn("StagingOrderCacheC.EvictTask.run():problem with removing order for cache:" + order.getId(), e);
			}
		}		
	}



	private static class OrderEntry {
		private Order order;
		private long entryTime = -1;
		
		public OrderEntry(Order order)
		{
			this.order = order;
			this.entryTime = System.currentTimeMillis();
		}
		
		public OrderEntry(Order order, long entryTime)
		{
			this.order = order;
			this.entryTime = entryTime;
		}
		
		public Order getOrder()
		{
			return this.order;
		}
		
		public long getEntryTime()
		{
			return this.entryTime;
		}
	}
}
