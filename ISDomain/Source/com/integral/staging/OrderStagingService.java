/**
 * <AUTHOR>
 */
package com.integral.staging;

import com.integral.message.WorkflowMessage;
import com.integral.persistence.Namespace;
import com.integral.user.User;

import java.util.List;

/**
 * TODO
 * <AUTHOR>
 *
 */
public interface OrderStagingService
{
	String QUERY_PARAM_ID = "qIds";
    String QUERY_ACCEPTED_STATES = "qAS";
    String QUERY_LOCKED_USER = "qLU";
    String QUERY_SOURCE = "Source";

	/**
	 * TODO
	 * @param message
	 * @return
	 */
	WorkflowMessage process( WorkflowMessage message );
	
	/**
	 * 
	 * @param clOrdId
	 * @param user
	 * @return most recent order associated with clOrdId for give user.
	 */
	Order getNonTerminalStateOrderByClOrdId( String clOrdId, User user );
	
	
	/**
	 * 
	 * @param clOrdId
	 * @param user
	 * @param ignoreStates
	 * @return
	 */
	Order getOrderByClOrdId( String clOrdId, User user , Integer[] ignoreStates);

	/**
	 *
	 * @param clOrdId
	 * @param user
	 * @param ignoreStates
	 * @return
	 */
	Order getOrderByClOrdIdFromPrimaryServer( String clOrdId, User user , Integer[] ignoreStates);
	
	/**
	 * 
	 * @param ordID
	 * @param user
	 * @param ignoreStates
	 * @return
	 */
	Order getOrderByOrdId( String ordID, User user, Integer ignoreStates[] );
	
	
	/**
	 * 
	 * @param orderId
	 * @param ns
	 * @return
	 */
	Order getOrder( String orderId, Namespace ns);
	
	/**
	 * 
	 * @param orderId
	 * @param ns
	 * @param queryPrimaryServer
	 * @return
	 */
	Order getOrder( String orderId, Namespace ns, boolean queryPrimaryServer);

	/**
	 *
	 * @param user
	 * @param states
	 * @param ccyPairs
	 * @param clientOrderIds
	 * @param portfolioIds
	 * @return
	 */
	public List<Order> queryOrder(User user, List<String> states, List<String> ccyPairs, List<String> clientOrderIds, List<String> portfolioIds);

    /**
     * query order from staging area , by default order will be queried from secondary server
     * If order is not received from secondary server then query primary
     If order received from secondary server is not in locked state then query primary server
     If order received from primary server is also not in locked state then log error and still continue the OMS notification work-flow
     * @param orderId
     * @param nameSpace
     * @return
     */
     public Order queryOrder(final String orderId, final Namespace nameSpace);
}
