package com.integral.marketmaker.service;


import com.integral.broker.config.BrokerConfigurationService;
import com.integral.broker.config.BrokerConfigurationServiceFactory;
import com.integral.broker.marketmaker.BrokerMarketMakerUtil;
import com.integral.broker.quote.BrokerQuoteFactory;
import com.integral.broker.quote.QuoteHandlerC;
import com.integral.finance.dealing.Quote;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.maker.service.broker.CorePriceBrokerService;
import com.integral.marketmaker.broker.BrokerMetaData;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.config.MarketMakerConfigMBean;
import com.integral.marketmaker.service.heartbeat.MMBAHeartbeatService;
import com.integral.persistence.cache.ReferenceDataCache;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.server.VirtualServer;
import com.integral.system.server.VirtualServerC;
import com.integral.user.Organization;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

public class MMBAService implements  MMService
{
    private static final Log log = LogFactory.getLog(MMBAHeartbeatService.class.getName());
    private List<MMService> mmServices ;
    private ScheduledExecutorService scheduledExecutorService ;
    private BrokerConfigurationService brokerConfigurationService;
    private CorePriceBrokerService corePriceBrokerService;
    private MarketMakerConfigMBean marketMakerConfigMBean;
    private ReferenceDataCache referenceDataCache ;
    /**
     *
     */
    public MMBAService(){
        mmServices = new ArrayList<MMService>(2);
        mmServices.add(new  MMBAHeartbeatService(this));
        scheduledExecutorService = Executors.newScheduledThreadPool(1);
        marketMakerConfigMBean = MarketMakerConfig.getInstance();
        referenceDataCache = ReferenceDataCacheC.getInstance();
    }

    @Override
    public boolean init() {
        boolean isSuccess = false;
        for (MMService mmService:mmServices) {
            isSuccess = isSuccess & mmService.init();
        }
        brokerConfigurationService = BrokerConfigurationServiceFactory.getBrokerConfigurationService();
        corePriceBrokerService= MarketMakerFactory.getInstance().getCorePriceBrokerService();
        return isSuccess;
    }

    @Override
    public boolean start() {
        boolean isSuccess = false;
        for (MMService mmService:mmServices) {
            isSuccess = isSuccess & mmService.start();
        }
        return isSuccess;
    }

    @Override
    public boolean stop() {
        boolean isSuccess = false;
        for (MMService mmService:mmServices) {
            isSuccess = isSuccess & mmService.stop();
        }
        mmServices.clear();
        mmServices=null;
        return isSuccess;
    }

    public void processEvents(Runnable runnable,long mills){
        scheduledExecutorService.scheduleWithFixedDelay(runnable,mills,mills, TimeUnit.MILLISECONDS);
    }

    /**
     * Returns the deployed Broker Organization on the Virtual Server
     *
     * @return
     */
    public Collection<String> getDeployedMMBrokerOrganization() {

        Collection<String> marketMakerEnabledBrokers = new ArrayList<String>(marketMakerConfigMBean.getMarketMakerEnabledBrokers());
        marketMakerEnabledBrokers.retainAll(brokerConfigurationService.getDeployedBrokerOrganizationNames());
        return marketMakerEnabledBrokers;
    }

    /**
     *
     * @param abroker
     * @return
     */
    public void publishStaleQuote(String abroker){
        Organization broker = referenceDataCache.getOrganization(abroker);
        BrokerMetaData brokerMetaData = corePriceBrokerService.get(broker.getShortName());
        if (null != brokerMetaData) {
            Set<String> currencyPair = brokerMetaData.getCurrencyPairs();
            for (String cp : currencyPair) {
                log.warn("MMVirtual Server is not active, Publishing Stale Rate for  Currency Pair :"
                        + cp + " Broker : " + broker);
                Quote staleQuote = BrokerMarketMakerUtil.createStaleQuote(broker, cp);
                QuoteHandlerC quoteHandler = (QuoteHandlerC) BrokerQuoteFactory.getInstance().getQuoteHandler(broker);
                quoteHandler.sendRate(staleQuote, Collections.emptyMap());
            }
        }
    }


    public VirtualServer getVirtualServer(String vsName) {
        return (VirtualServer) referenceDataCache.getEntityByShortName(vsName, VirtualServerC.class, null, 'A');
    }

    public MarketMakerConfigMBean getMMConfiguration(){
        return MarketMakerConfig.getInstance();
    }
}
