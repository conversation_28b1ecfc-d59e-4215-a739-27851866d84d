package com.integral.marketmaker.service.coreprice;

import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.broker.configuration.ConfigurationFactory;
import com.integral.broker.volatility.VolatilityMetric;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.finance.currency.CurrencyService;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.CorePriceSerializer;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.config.MarketMakerConfigMBean;
import com.integral.organization.MulticastAddress;
import com.integral.transport.multicast.MulticastAddressPoolService;
import com.integral.user.Organization;

import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.net.SocketTimeoutException;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.concurrent.ConcurrentHashMap;

public class MMVolatilityFactorPublisherC implements Runnable {
    private static final Log log = LogFactory.getLog(MMVolatilityFactorPublisherC.class);
    private static final String MULTICAST_ADDRESS_KEY = "MarketSignals";
    private static final MMVolatilityFactorPublisherC instanceForNewService = new MMVolatilityFactorPublisherC(true);
    private static final MMVolatilityFactorPublisherC instanceForOldService = new MMVolatilityFactorPublisherC(false);
    private MulticastSocket multicastSocket;
    private final Map<String,String> joinedMulticastGroups = new ConcurrentHashMap<String,String>();
    private int mtu = 1500;
    private final boolean newService;

    private MMVolatilityFactorPublisherC(boolean newService){
        this.newService = newService;
        try{
            MarketMakerConfigMBean makerConfigMBean = MarketMakerConfig.getInstance();
            int port = newService ? makerConfigMBean.getMarketSignalsMulticastPort() : ConfigurationFactory.getInstance().getVolatilityServiceMBean().getMulticastPort();
            int timeout = makerConfigMBean.getBrokerMulticastSocketTimeout();
            mtu = makerConfigMBean.getMulticastMTU();
            multicastSocket = new MulticastSocket(port);
            multicastSocket.setTimeToLive( com.integral.system.configuration.ConfigurationFactory.getServerMBean().getMulticastTTL());
            multicastSocket.setSoTimeout(timeout);
            log.info("MMVolatilityFactorPublisherC.init:: port=" + port + ",timeout=" + timeout + ",mtu=" + mtu);
            Thread thread = new Thread(this);
            thread.setName("MMVolatilityFactorPublisherThread_" + (newService ? "NEW" : "OLD"));
            thread.setDaemon(true);
            thread.start();
        }catch(Exception e){
            log.error("MMVolatilityFactorPublisherC.init:: Exception: ", e);
        }
    }

    public static MMVolatilityFactorPublisherC getInstance(boolean newService){
        return newService ? instanceForNewService : instanceForOldService;
    }

    public synchronized void joinMulticast(String brokerOrg){

        //Skipping to rejoin the multicast if already joined
        if(joinedMulticastGroups.containsKey(brokerOrg)){
            log.info("MMVolatilityFactorPublisherC.joinMulticast: multicast group already joined for broker "+brokerOrg);
            return;
        }

        try{
            //The method creates a new multicast address if it does not exists
            MulticastAddress multicastAddress = MulticastAddressPoolService.getInstance().createMulticastAddress(brokerOrg, MULTICAST_ADDRESS_KEY);
            String addressStr = multicastAddress.getMulitcastAddress();
            InetAddress inetAddress  = InetAddress.getByName(addressStr);
            log.info( "MMVolatilityFactorPublisherC.run: start listening volatility service updates at address="
                    + inetAddress + ", addressStr=" + addressStr + ", brokerOrg=" + brokerOrg + ", new=" + newService + ", orgs=" + joinedMulticastGroups.keySet());
            multicastSocket.joinGroup(inetAddress);
            joinedMulticastGroups.put(brokerOrg,addressStr);
        }catch(Exception e){
            log.error("MMVolatilityFactorPublisherC.joinMulticast:: Exception: ", e);
        }
    }

    public synchronized void leaveMulticast(String brokerOrg) {

        if(!joinedMulticastGroups.containsKey(brokerOrg)){
            log.info("MMVolatilityFactorPublisherC.leaveMulticast: Skipping to leave multicast. No multicast group joined for broker "+brokerOrg);
            return;
        }

        try{
            String addressStr = joinedMulticastGroups.get(brokerOrg);
            InetAddress inetAddress  = InetAddress.getByName(addressStr);
            log.info( "MMVolatilityFactorPublisherC.leaveMulticast: stop listening volatility service updates at address="
                    + inetAddress + ", addressStr=" + addressStr + ", brokerOrg=" + brokerOrg + ", new=" + newService + ", orgs=" + joinedMulticastGroups.keySet());
            multicastSocket.leaveGroup(inetAddress);
            joinedMulticastGroups.remove(brokerOrg);
        }catch(Exception e){
            log.error("MMVolatilityFactorPublisherC.leaveMulticast:: Failed to leave! Exception: ", e);
            log.info("MMVolatilityFactorPublisherC.leaveMulticast:: MulticastAddresspool for "+brokerOrg+":"+findExistingMulticastAddressesForBroker(brokerOrg));
        }
    }

    @Override
    public void run() {
        log.info("MMVolatilityFactorPublisherC.run: Volatility Factor listener started listening, new=" + newService);
        try {
            byte[] bytes = new byte[mtu];
            while (true) {
                try {
                    DatagramPacket packet = new DatagramPacket(bytes, bytes.length);
                    try {
                        multicastSocket.receive(packet); //blocking call
                    } catch (SocketTimeoutException e) {
                        if (log.isDebugEnabled())
                            log.debug("MMVolatilityFactorPublisher.run:: no volatility factor for " + multicastSocket.getSoTimeout() + " milliseconds. new=" + newService);
                        continue;
                    }
                    List<VolatilityMetric> volatilityMetricList;
                    if(!newService){
                        volatilityMetricList = CorePriceSerializer.deserializeVolatilityMetric(packet.getData());
                    }else {
                        volatilityMetricList = CorePriceSerializer.deserializeVolatilityMetricJson(packet);
                    }
                    if (volatilityMetricList == null || volatilityMetricList.isEmpty()) continue;
                    for(VolatilityMetric volatilityMetric : volatilityMetricList) {
                        Organization org = OrganizationUtil.getOrganization(volatilityMetric.getBroker());
                        String ccyPair = volatilityMetric.getCurrencyPair();
                        CurrencyPair currencyPair = CurrencyService.getCurrencyPair(volatilityMetric.getCurrencyPair());
                        CorePricePublisherC publisher = CorePriceServiceFactory.getInstance().getPublisher(org,currencyPair);
                        if(publisher == null){
                            if(log.isDebugEnabled()) log.debug("MMVolatilityFactorPublisher.run:: Skipping update. CorePricePublisherC not found for Org: " + org.getShortName() +", ccyPair: "+ ccyPair + ", new=" + newService + ", data=" + volatilityMetric);
                            continue;
                        }
                        publisher.updateMarketSignals(volatilityMetric.getValue(), volatilityMetric.getBidOfferMode());

                        if (log.isDebugEnabled())
                            log.debug("MMVolatilityFactorPublisher.run:: volatility factor published new=" + newService + ", data=" + volatilityMetric );
                    }
                } catch (Throwable e) {
                    log.warn("MMVolatilityFactorPublisher.run failed for new=" + newService, e);
                }
            }
        }
        catch (Exception ex){
            log.error("MMVolatilityFactorPublisher.run:: IOException while listening on multicast for new=" + newService, ex);
        }
        finally {
            log.info("MMVolatilityFactorPublisher.run:: completed listening. new=" + newService);
        }
    }

    private List<String> findExistingMulticastAddressesForBroker(String brokerOrg){

        List<String> multicastAddressList = new ArrayList<String>();
        List<MulticastAddress> mcAddressess = null;
        try {
            mcAddressess = MulticastAddressPoolService.getInstance().getAllMulticastAddresses();
        } catch (Exception e) {
            log.info("MMVolatilityFactorPublisherC.findExistingMulticastAddress:: Failed to get all multicast addresses");
            return multicastAddressList;
        }

        for(MulticastAddress mcAddress : mcAddressess){
            String org = mcAddress.getOrganization();
            String type = mcAddress.getType();
            String multicastAddrStr = mcAddress.getMulitcastAddress();

            if(log.isDebugEnabled()){
                log.debug("Org="+org+", Type="+type+", MulticastAddress="+multicastAddrStr);
            }

            if(brokerOrg.equals(org) && MULTICAST_ADDRESS_KEY.equals(type)){
                multicastAddressList.add(multicastAddrStr);
            }
        }

        return multicastAddressList;
    }
}
