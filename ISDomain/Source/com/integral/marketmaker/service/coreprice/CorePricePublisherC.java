package com.integral.marketmaker.service.coreprice;

import com.integral.broker.BrokerAdaptorUtil;
import com.integral.broker.aggregate.BrokerFilterFactoryC;
import com.integral.broker.aggregate.BrokerQuoteAggregatorFactory;
import com.integral.broker.aggregate.MultiTierAverageQuoteAggregatorC;
import com.integral.broker.aggregate.QuoteAggregator;
import com.integral.broker.cache.ProviderQuoteCache;
import com.integral.broker.config.BrokerRDSConfigServiceC;
import com.integral.broker.filter.BrokerFilterFactory;
import com.integral.broker.filter.DisabledProviderFilterC;
import com.integral.broker.filter.MultiProviderQuoteFilter;
import com.integral.broker.filter.cfd.CfdFilterUtil;
import com.integral.broker.log.*;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.Product;
import com.integral.broker.model.RateFilterDefinition;
import com.integral.broker.price.PriceBook;
import com.integral.broker.filter.cfd.CfdLpFailoverFilter;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.finance.fx.FXRateBasis;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.marketmaker.CorePriceSerializer;
import com.integral.marketmaker.MarketMakerAggregationLogger;
import com.integral.marketmaker.MarketMakerThreadPoolFactory;
import com.integral.marketmaker.config.CorePriceConfig;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.corepricing.AggregationStrategy;
import com.integral.marketmaker.corepricing.CorePricingMetaData;
import com.integral.marketmaker.corepricing.Mode;
import com.integral.marketmaker.provider.ProvidersMetaData;
import com.integral.marketmaker.tierpricing.TierPricingMetaData;
import com.integral.model.broker.BrokerConfiguration;
import com.integral.model.broker.CfdLpFailoverConfig;
import com.integral.rw.rule.RiskMode;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.user.Organization;
import com.integral.ym.service.YMServiceHelper;

import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.nio.ByteBuffer;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 7/27/16
 */
public class CorePricePublisherC implements Runnable {
    private final Log log;
    private final Organization brokerOrg;
    private final CurrencyPair currencyPair;
    private final long period; //milliseconds
    private static final ScheduledExecutorService scheduledExecutorService = MarketMakerThreadPoolFactory.getPublisherScheduler();
    private boolean running;
    private final CorePriceProviderQuoteCacheC providerQuoteCache;
    private ScheduledFuture<?> scheduledFuture;
    private final CorePriceAggregatorC aggregator;
    private final CorePriceCalculatorC calculator;
    private final CorePriceConfig config;
    private final int tierPricePort;
    private final int corePricePort;
    private final int volatileFactorPublishPort;
    private MulticastSocket multicastSocket;
    private InetAddress multicastAddress;
    private CorePriceData stalePrice;
    private TierdPriceData staleTierPrice;
    private Log mmAggregationLogger;
    private QuoteAggregator multiTierAggregator;
    private Product product;
    private BrokerQuoteAggregatorFactory mmAggregatorFactory;
    private boolean isMidRateAggregation = true;
    private AtomicBoolean configChange = new AtomicBoolean(false);
    private AtomicBoolean dynamicConfigChange = new AtomicBoolean(false);
    private long lastPublishTime;
    private boolean ymEnabled;
    private boolean noYM;
    AggregationStrategy aggregationStrategy = AggregationStrategy.BestPriceMidRate;
    private AtomicInteger circuitBrRef = new AtomicInteger(-1);
    private AtomicReference<RiskMode> riskModRef = new AtomicReference<RiskMode>();
    private volatile long quoteCounterProcessed = 0L;
    private volatile long firstTierChangeCounterProcessed = 0L;
    private double volatilityFactor =0;
    private int bidOfferMode = 0; //0=NA, 1=MID, 2=BID-OFFER
    private Mode aggStgyMode = Mode.MANUAL;
    private long lastPulseTime = 0L;
    private CfdLpFailoverFilter cfdLpFailoverFilter;

    public CorePricePublisherC(Organization org, CurrencyPair cp, CorePriceConfig config){
        this.brokerOrg = org;
        this.currencyPair = cp;
        String loggerName = "com.integral.mm.Publisher." + org.getShortName() + "." + cp.getBaseCurrency().getName() + "." + cp.getVariableCurrency().getName();
        log = LogFactory.getLog(loggerName);
        ProvidersMetaData providersMetaData = config.getProviderList(cp);
        Set<String> providers;
        if(providersMetaData != null){
            providers = new HashSet<String>(providersMetaData.getProviderStatus().keySet());
        }else {
            providers = new HashSet<String>();
        }
        providerQuoteCache = CorePriceServiceFactory.getInstance().getProviderQuoteCache(org, cp, providers);
        aggregator = new CorePriceAggregatorC(org, cp);
        calculator = new CorePriceCalculatorC(org.getShortName(), cp.getName());
        this.config = config;
        MarketMakerConfig marketMakerConfig = MarketMakerConfig.getInstance();
        String address = marketMakerConfig.getMulticastAddress();
        tierPricePort = marketMakerConfig.getTierPriceMulticastPort();
        volatileFactorPublishPort = marketMakerConfig.getVolatileFactorMulticastPort();
        corePricePort = marketMakerConfig.getCorePriceMulticastPort();
        period = marketMakerConfig.getAggregationInterval(org.getShortName());
        try{
            multicastAddress = InetAddress.getByName(address);
            multicastSocket = new MulticastSocket();
            multicastSocket.setTimeToLive( ConfigurationFactory.getServerMBean().getMulticastTTL());
        } catch (Exception e) {
            log.error("CorePricePublisherC:: failed to instantiate Core Price Publisher", e);
        }
        stalePrice = new CorePriceData();
        stalePrice.setOrganization(brokerOrg.getShortName());
        stalePrice.setCurrencyPair(currencyPair.getName());
        staleTierPrice = new TierdPriceData(brokerOrg.getShortName(), currencyPair.getName());
        staleTierPrice.addTier(0, 0, 0);
        mmAggregationLogger = MarketMakerAggregationLogger.getLog(org.getShortName());
        multiTierAggregator = new MultiTierAverageQuoteAggregatorC();

        CorePricingMetaData corePriceMetaData = config.getCorePriceMetaData(currencyPair);
        TierPricingMetaData tierPriceMetaData = config.getTierPriceMetaData(currencyPair);
        if(corePriceMetaData != null && tierPriceMetaData != null) {
            aggregationStrategy = corePriceMetaData.getAggregationStrategy();
            aggStgyMode = corePriceMetaData.getAggregationStrgyMode();
        }
        ymEnabled = YMServiceHelper.getInstance().getYmWarehouseService().isRiskWarehouseEnabled(getCurrencyPair(), getOrgName());
        noYM = MarketMakerConfig.YMMode.NO_YM.equals(MarketMakerConfig.getInstance().getYMMode(getOrgName()));
        cfdLpFailoverFilter = new CfdLpFailoverFilter ( null, org.getShortName(), "MarketMaker", "CorePrice", cp.getName() );
        log.info("CorePricePublisherC.updateYMStatus:: updated status for " + org.getShortName() + " " + cp.getName() + " ym enabled=" + ymEnabled);
        updateAggMethod();
    }

    public synchronized void start(){
        if(!running){
            if(scheduledFuture != null) scheduledFuture.cancel(true);
            providerQuoteCache.start();
            scheduledFuture = scheduledExecutorService.scheduleWithFixedDelay(this, 0, period, TimeUnit.MILLISECONDS);
            running = true;
            log.info("CorePricePublisherC.publish:: started for " + brokerOrg.getShortName() + ", cp=" + currencyPair.getName());
        }else {
            log.info("CorePricePublisherC.publish:: already started for " + brokerOrg.getShortName() + ", cp=" + currencyPair.getName());
        }
    }

    public synchronized void stop(){
        publishCorePrice(stalePrice);
        publishTierPrices(staleTierPrice);
        providerQuoteCache.stop();
        if(scheduledFuture != null) {
            scheduledFuture.cancel(false);
            log.info("CorePricePublisherC.stop:: Timer cancelled for " + brokerOrg.getShortName() + " " + currencyPair.getName());
        }
        running = false;
        log.info("CorePricePublisherC.stop:: CorePricePublisher stopped for " + brokerOrg.getShortName() + " " + currencyPair.getName());
    }

    @Override
    public void run() {
        try{
            publish();
        }catch (Throwable ex){
            log.warn("CorePricePublisherC.publish:: failed for " + brokerOrg.getShortName() + ", cp=" + currencyPair.getName(), ex);
        }
    }

    public void publish(){
        Collection<Quote> quotes = null;
        try {
            String brokerName = brokerOrg.getShortName();
            String cpName = currencyPair.getName();
            StringBuilder sb = new StringBuilder();
            sb.append(cpName).append(' ');
            long currentTime = System.currentTimeMillis();
            long quoteCounter = providerQuoteCache.getQuoteCounter().get();
            long firstTierChangeCounter = providerQuoteCache.getFirstTierChangeCounter().get();
            int publishInterval = MarketMakerConfig.getInstance().getPublishInterval(brokerName);
            int forcePubInterval = MarketMakerConfig.getInstance().getForcePublishInterval(brokerName);
            boolean handleConfigChange = configChange.get() && configChange.compareAndSet(true, false);
            boolean handleDynamicConfigChange = dynamicConfigChange.get() && dynamicConfigChange.compareAndSet(true, false);
            double volatilityFactorForCalculation = volatilityFactor == 0.0D ? 1.0D : volatilityFactor;
            int bidOfferModeForCalculation = bidOfferMode;
            boolean toPublish = handleConfigChange ||
                    //New quotes
                    ( this.quoteCounterProcessed != quoteCounter && (currentTime-lastPublishTime >= publishInterval)) ||

                    //First Tier changed for any LP
                    ( this.firstTierChangeCounterProcessed != firstTierChangeCounter && (currentTime-lastPublishTime >= period)) ||

                    //Forced publish
                    ( currentTime - lastPublishTime >= forcePubInterval);
            if( log.isDebugEnabled() ){
                StringBuilder sb1 = new StringBuilder(300);
                sb1.append("CorePricePublisherC.publish : ");
                sb1.append("quoteCounterProcessed=").append(quoteCounterProcessed);
                sb1.append(", quoteCounter=").append(quoteCounter);
                sb1.append(", firstTierChangeCounterProcessed=").append(firstTierChangeCounterProcessed);
                sb1.append(", firstTierChangedCounter=").append(firstTierChangeCounter);
                sb1.append(", handleConfigChange=").append(handleConfigChange);
                sb1.append(", publishInterval=").append(publishInterval);
                sb1.append(", forcePublishInterval=").append(forcePubInterval);
                sb1.append(", firstTierChangePublishInterval=").append(period);
                sb1.append(", (currentTime-lastPublishTime)=").append(currentTime-lastPublishTime);
                sb1.append(", toPublish=").append(toPublish);
                log.debug(sb1.toString());
            }
            if(!toPublish){
                return;
            }
            FXRateBasis rateBasis = config.getRateBasis(brokerOrg, currencyPair);
            int precision = rateBasis.getSpotPrecision();
            double pipsFactor = rateBasis.getPipsFactor();
            quotes = providerQuoteCache.getQuotes();
            BrokerAdaptorUtil.getInstance().incrementRefCounter(quotes);
            Collection<Quote> copyOfQuotes = new ArrayList<Quote>(quotes);
            MarketMakerAggregationLogger.logQuoteDetails(quotes, sb);
            BrokerLoggerC.createLocalBuilder(100);
            copyOfQuotes = filterQuotes(copyOfQuotes);
            StringBuilder stringBuilder = BrokerLoggerC.getLocalBuilder().get();
            sb.append(stringBuilder);
            ProvidersMetaData providersMetaData = config.getProviderList(currencyPair);
            CorePriceData corePrice;
            if(providersMetaData != null) {
                //aggregate & create core price
                corePrice = aggregator.aggregate(copyOfQuotes, precision, providersMetaData.getProviderStatus(), sb);
            }else {
                corePrice = stalePrice;
            }
            publishCorePrice(corePrice);
            boolean core = config.isPricingEnabled(currencyPair);
            boolean gm = RuntimeFactory.getServerRuntimeMBean().isRatesEnabled();
            if(!core || !gm){
                sb.append(" prc off ").append(core).append(" ").append(gm);
                publishTierPrices(staleTierPrice);
                mmAggregationLogger.info(sb.toString());
                return;
            }

            //create tiered prices
            CorePricingMetaData coreConfig = config.getCorePriceMetaData(currencyPair);
            if(coreConfig == null) {
                if(log.isDebugEnabled())
                    log.debug("CorePricePublisherC.publish:: Core Price Configuration not exist not publishing core price " + brokerName + " " + cpName);
                publishTierPrices(staleTierPrice);
                sb.append("core conf null ");
                mmAggregationLogger.info(sb.toString());
                return;
            }
            TierPricingMetaData tierConfig = config.getTierPriceMetaData(currencyPair);
            if(tierConfig == null) {
                if(log.isDebugEnabled())
                    log.debug("CorePricePublisherC.publish:: Tier Configuration do not exist. Not publishing core prices " + brokerName + " " + cpName);
                publishTierPrices(staleTierPrice);
                sb.append("tier conf null ");
                mmAggregationLogger.info(sb.toString());
                return;
            }

            //check for pulse service message timeout
            checkMarketPulseTimeout();

            TierdPriceData tierdPrice = null;
            if(isMidRateAggregation) {
                sb.append(" agg=MR ");
                tierdPrice = calculator.calculateTierdPrice(brokerOrg, currencyPair, coreConfig, tierConfig, corePrice, precision, pipsFactor, volatilityFactorForCalculation, sb);
            }else{
                if(handleDynamicConfigChange) {
                    TierPricingMetaData tierPricingMetaData = config.getTierPriceMetaData(product.getCurrencyPair());
                    if (tierPricingMetaData != null && tierPricingMetaData.isVolatilityEnabled() && tierPricingMetaData.isUseMultiplierFromService()) {
                        CorePriceProductUtil.updateProduct(product, tierPricingMetaData,volatilityFactorForCalculation);
                    }
                }
                PriceBook priceBook = multiTierAggregator.aggregate(copyOfQuotes, product, mmAggregatorFactory);
                sb.append(" agg=WA ");
                tierdPrice = calculator.fromPriceBookToTieredPrice(priceBook, tierConfig, brokerName, cpName, sb, pipsFactor, precision, volatilityFactorForCalculation);
            }
            publishTierPrices(tierdPrice);
            publishVolatilityFactor(brokerOrg,currencyPair,volatilityFactor, bidOfferModeForCalculation);

            mmAggregationLogger.info(sb.toString());
            lastPublishTime = System.currentTimeMillis();
            quoteCounterProcessed = quoteCounter;
            firstTierChangeCounterProcessed = firstTierChangeCounter;
        }
        finally {
            if(quotes != null) {
                BrokerAdaptorUtil.getInstance().decrementRefCounter(quotes);
            }
            BrokerLoggerC.resetLocalBuilder();
        }
    }

    private void checkMarketPulseTimeout(){
        if((bidOfferMode != 0 || volatilityFactor != 0.0D) &&
                System.currentTimeMillis() - lastPulseTime >= 1000L * MarketMakerConfig.getInstance().getVolatilityServiceTimeoutSeconds()){
            bidOfferMode = 0; //NA
            volatilityFactor = 0.0D;
            log.warn("CorePricePublisherC.checkMarketPulseTimeout:: MarketSignals Service Timeout for org=" + brokerOrg.getShortName() + ", cp=" + currencyPair.getName() + ", lastPulseTime=" + lastPulseTime);
            updateAggMethod();
        }
    }

    private Collection<Quote> filterQuotes(Collection<Quote> quotes){
        BrokerOrganizationFunction bof = brokerOrg.getBrokerOrganizationFunction();
        BrokerFilterFactoryC filterFactory = BrokerFilterFactoryC.getBrokerFilterFactory();

        MarketMakerConfig marketMakerConfig = MarketMakerConfig.getInstance();
        if(marketMakerConfig.evaluatePriceMakingProviderStatus(brokerOrg.getShortName())) {
            // Filter all the disable providers.
            DisabledProviderFilterC disabledProviderFilter = BrokerFilterFactory.getInstance().getDisabledProviderFilter();
            disabledProviderFilter.filter(quotes, bof, filterFactory);
        }else{
            if(log.isDebugEnabled()){
                log.debug("CorePricePublisherC.filterQuotes: PriceMaking Status ON/OFF of LP ignored for Broker " +
                           brokerOrg.getShortName());
            }
        }
        boolean cfdFailover = marketMakerConfig.isSingleLpFailoverEnabled(brokerOrg.getShortName(), currencyPair.getName());
        CfdLpFailoverConfig failoverConfig = null;
        if(cfdFailover){
            BrokerConfiguration brkRdsConfig = BrokerRDSConfigServiceC.getInstance ().getBrokerConfiguration ( brokerOrg.getShortName() );
            failoverConfig = brkRdsConfig != null ? brkRdsConfig.getCfdLpFailoverConfig () : null;
            cfdFailover = failoverConfig != null && failoverConfig.isEnabled ();
        }
        if ( cfdFailover )
        {
            String[] lpOrder = marketMakerConfig.getSingleLpFailoverPriority(brokerOrg.getShortName(), currencyPair.getName());
            quotes = CfdFilterUtil.filter ( quotes, currencyPair, lpOrder, filterFactory, failoverConfig, cfdLpFailoverFilter );
        }else{
            RateFilterDefinition activeRateFilterDefinition = bof.getActiveRateFilterDefinition();
            List<MultiProviderQuoteFilter<Quote, RateFilterDefinition>> filters = bof.getMultiProviderRateFilters();
            for (MultiProviderQuoteFilter<Quote, RateFilterDefinition> filter : filters) {
                quotes = filter.filter(quotes, activeRateFilterDefinition, filterFactory);
            }
        }
        return quotes;
    }

    private void publishCorePrice(CorePriceData corePrice){
        ByteBuffer byteBuffer = CorePriceSerializer.serializeCorePrice(corePrice, brokerOrg, currencyPair);
        DatagramPacket packet = new DatagramPacket(byteBuffer.array(), 0, byteBuffer.position(), multicastAddress, corePricePort);
        try {
            multicastSocket.send(packet);
        }catch (Exception e){
            log.warn("CorePricePublisherC.publishTierPrices:: failed to publish core price", e);
        }
        if(log.isDebugEnabled()) log.debug("CorePricePublisherC.publishCorePrices:: corePrice=" + corePrice);
    }

    private void publishTierPrices(TierdPriceData tierdPrice){
        ByteBuffer byteBuffer = CorePriceSerializer.serializeTierPrice(tierdPrice, brokerOrg, currencyPair);
        DatagramPacket packet = new DatagramPacket(byteBuffer.array(), 0, byteBuffer.position(), multicastAddress, tierPricePort);
        try {
            multicastSocket.send(packet);
        }catch (Exception e){
            log.warn("CorePricePublisherC.publishTierPrices:: failed to publish tierd prices", e);
        }
        if(log.isDebugEnabled()) log.info("CorePricePublisherC.publishTierPrices:: tiers=" + tierdPrice);
    }

    /* package */ ProviderQuoteCache getProviderQuoteCache(){
        return providerQuoteCache;
    }

    private void publishVolatilityFactor(Organization organization, CurrencyPair ccyPair, double factor, int boMode){
        ByteBuffer byteBuffer =  CorePriceSerializer.serialize(organization, ccyPair, System.currentTimeMillis(),factor, boMode);
        DatagramPacket packet = new DatagramPacket(byteBuffer.array(), 0, byteBuffer.position(), multicastAddress, volatileFactorPublishPort);
        try {
            multicastSocket.send(packet);
        }catch (Exception e){
            log.warn("CorePricePublisherC.publishVolatilityFactorUsed:: failed to publish volatility Factor ", e);
        }
        if(log.isDebugEnabled()) log.debug("CorePricePublisherC.publishVolatilityFactorUsed:: volatility Factor org:" + organization.getShortName() + ", cp:"+ ccyPair.getName() + ", vol:"+ factor );
    }

    public synchronized void updateAggregationChange(boolean newAggMidRate, Mode aggStrygyMode){
        aggregationStrategy = newAggMidRate ? AggregationStrategy.BestPriceMidRate : AggregationStrategy.WeightedAverage;
        this.aggStgyMode = aggStrygyMode;
        log.info("CorePricePublisherC.updateAggregationChange:: agg strategy=" + aggregationStrategy + ", mode=" + aggStrygyMode);
        updateAggMethod();
    }

    public synchronized void updateMarketSignals(double spreadMultiplier, int bidOfferMode){
        lastPulseTime = System.currentTimeMillis();
        if(spreadMultiplier == volatilityFactor && bidOfferMode == this.bidOfferMode){
            if(log.isDebugEnabled()) log.debug("CorePricePublisherC.updateMarketSignals: no change in MarketSignals: volatilityFactor=" + spreadMultiplier + ", bidOfferMode=" + bidOfferMode);
            return;
        }
        volatilityFactor = spreadMultiplier;
        int oldBidOfferMode = this.bidOfferMode;
        this.bidOfferMode = bidOfferMode;
        dynamicConfigChange.set(true);
        if(oldBidOfferMode != bidOfferMode){
            updateAggMethod();
        }
        log.info("CorePricePublisherC.updateMarketSignals: org:"+ brokerOrg.getShortName() + ", ccyPair:"+ getCurrencyPair() + ", volatilityFactor: "+ spreadMultiplier + ", bidOfferMode: "+ bidOfferMode);
    }

    public synchronized void updateTierChange(TierPricingMetaData tierPricingMetaData){
        if(isMidRateAggregation){
            if(log.isDebugEnabled()) log.debug("CorePricePublisherC.updateTierChange:: skipping tier change, org=" + brokerOrg.getShortName() + ", cp=" + currencyPair.getName());
        }else {
            double vf = volatilityFactor == 0.0D ? 1.0 : volatilityFactor;
            CorePriceProductUtil.updateProduct(product, tierPricingMetaData,vf);
            log.info("CorePricePublisherC.updateTierChange:: product updated with new tiers org=" + brokerOrg.getShortName() + ", cp" + currencyPair.getName());
        }
    }

    public synchronized void updateYMStatus(){
        boolean status = YMServiceHelper.getInstance().getYmWarehouseService().isRiskWarehouseEnabled(getCurrencyPair(), getOrgName());
        if(status != ymEnabled) {
            ymEnabled = status;
            log.info("CorePricePublisherC.updateYMStatus:: updated status for " + getOrgName() + " " + getCurrencyPair() + " ym enabled=" + status);
            updateAggMethod();
            //if YM off switch skew to auto to avoid manual skew and cover execution
            if (!status) {
                MarketMakerFactory.getInstance().getCorePricingService().updateSkewingMode(getOrgName(), getCurrencyPair(), Mode.AUTO);
                log.info("CorePricePublisherC.updateYMStatus:: updated skew mode to AUTO for " + getOrgName() + " " + getCurrencyPair());
            }
        }else {
            if(log.isDebugEnabled()) log.debug("CorePricePublisherC.updateYMStatus:: no change in status for " + getOrgName() + " " + getCurrencyPair() + " ym enabled=" + status);
        }
    }

    public void updateWarehouseStatus(int circuitBr, RiskMode riskMode){
        int oldCirBr = circuitBrRef.getAndSet(circuitBr);
        RiskMode oldRisk = riskModRef.getAndSet(riskMode);
        if(oldRisk == null || !riskMode.equals(oldRisk) || circuitBr != oldCirBr){
            updateYMStatus();
        }else {
            if(log.isDebugEnabled()) log.debug("CorePricePublisherC.updateWH:: no change in riskMode for " + getOrgName() + " " + getCurrencyPair() + " " + riskMode.name());
        }
    }

    public synchronized void updateYMMode(){
        noYM = MarketMakerConfig.YMMode.NO_YM.equals(MarketMakerConfig.getInstance().getYMMode(getOrgName()));
        log.info(".updateYMMode: updated to noYM=" + noYM + ", broker=" + brokerOrg.getShortName() + ", cp=" + currencyPair.getName());
        updateAggMethod();
    }

    private void updateAggMethod(){
        boolean currentModeIsMid = isMidRateAggregation;
        boolean configuredIsMid = aggregationStrategy.equals(AggregationStrategy.BestPriceMidRate);
        boolean pulseModeIsMid = bidOfferMode == 1;
        boolean modeFromPulse = aggStgyMode == Mode.AUTO;
        boolean newModeIsMid = modeFromPulse ? pulseModeIsMid : configuredIsMid;
        if(!noYM && !ymEnabled) newModeIsMid = false;

        if(newModeIsMid != currentModeIsMid){
            log.info("CorePricePublisherC.updateAggMethod:: new agg mthd=" + newModeIsMid + ", old agg mthd=" + currentModeIsMid + ", org=" + brokerOrg.getShortName() + ", cp=" + currencyPair.getName() + ", ym enabled=" + !noYM + ", ym status=" + ymEnabled + ", conf mid=" + isMidRateAggregation +  ", volatility mid=" + bidOfferMode);
            if(!newModeIsMid){
                initializeProduct();
            }
        }
        isMidRateAggregation = newModeIsMid;
    }

    private void initializeProduct(){
        CorePricingMetaData corePriceMetaData = config.getCorePriceMetaData(currencyPair);
        TierPricingMetaData tierPriceMetaData = config.getTierPriceMetaData(currencyPair);
        double vf = volatilityFactor == 0.0D ? 1.0 : volatilityFactor;
        product = CorePriceProductUtil.createProduct(corePriceMetaData, tierPriceMetaData, config, multiTierAggregator,vf);
        mmAggregatorFactory = new BrokerQuoteAggregatorFactory(product, null);
    }

    public String getCurrencyPair(){
        return currencyPair.getName();
    }

    public synchronized boolean isRunning(){
        return running;
    }

    public void setConfigChange(boolean configChange) {
        this.configChange.set(configChange);
    }

    private String getOrgName(){
        return brokerOrg.getShortName();
    }

    private double getVolatilityFactor(){return volatilityFactor;}
    public void updateProviderChange(){
        if(isMidRateAggregation){
            log.info("CorePricePublisherC.updateProviderList:: skipping provider change as it is mid rate aggregation for " + getOrgName() + " " + getCurrencyPair());
        }else {
            initializeProduct();
            log.info("CorePricePublisherC.updateProviderList:: updated providers for " + getOrgName() + " " + getCurrencyPair());
        }
    }
}
