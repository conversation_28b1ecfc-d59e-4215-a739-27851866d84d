package com.integral.marketmaker.service.coreprice;

import com.integral.broker.price.Price;
import com.integral.broker.price.PriceBook;
import com.integral.broker.skew.SkewServiceFactory;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.TierData;
import com.integral.marketmaker.corepricing.CorePricingMetaData;
import com.integral.marketmaker.corepricing.Mode;
import com.integral.marketmaker.tierpricing.TierPricingMetaData;
import com.integral.math.MathUtil;
import com.integral.user.Organization;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.SortedSet;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 7/28/16
 */
public class CorePriceCalculatorC {
    private final Log log;
    private static final ConcurrentHashMap<String, Double> midPriceCache = new ConcurrentHashMap<String, Double>();
    public CorePriceCalculatorC(String orgName, String cp){
        String loggerName = "com.integral.mm.PriceCalculator." + orgName + CurrencyFactory.getBaseCurrency(cp) + "." + CurrencyFactory.getTermCurrency(cp);
        log = LogFactory.getLog(loggerName);
    }

    public TierdPriceData calculateTierdPrice(Organization broker, CurrencyPair currencyPair, CorePricingMetaData corePricingMetaData, TierPricingMetaData tierPricingMetaData, CorePriceData corePrice, int precision, double pipsFactor, double volatilityFactor, StringBuilder sb){
        double skew = 0, bidSkew = 0, offerSkew = 0;
        sb.append('[');
        if(corePricingMetaData.getSkewMode().equals(Mode.AUTO)){
            double skewInPips = SkewServiceFactory.getSkewService(broker.getShortName()).getLatestSkew(currencyPair.getName()).getSkewValue();
                if(log.isDebugEnabled()) log.debug("CorePriceCalculator.calculateTierdPrice:: skew=" + skewInPips + ", org=" + broker.getShortName() + ", cp=" + currencyPair.getName());
            skew = skewInPips;
            skew = MathUtil.round(skew, precision, BigDecimal.ROUND_HALF_EVEN);
            bidSkew = skew; offerSkew = skew;
            sb.append("sk A ").append(skew).append(' ');
        }else {
        	boolean isAsymmSkew = corePricingMetaData.isAsymmSkew();
            skew = corePricingMetaData.getSkew() / pipsFactor;
            bidSkew = isAsymmSkew ? corePricingMetaData.getBidSkew()/pipsFactor : skew;
            offerSkew = isAsymmSkew ? corePricingMetaData.getOfferSkew()/pipsFactor : skew;
            bidSkew = MathUtil.round(bidSkew, precision, BigDecimal.ROUND_HALF_EVEN);
            offerSkew = MathUtil.round(offerSkew, precision, BigDecimal.ROUND_HALF_EVEN);
            sb.append("sk M ").append(isAsymmSkew).append(' ').append(bidSkew).append(' ').append(offerSkew).append(' ');
        }
        double midRate;
        boolean useMissingPriceCalculation = false;
        if(corePricingMetaData.getPricingMode().equals(Mode.AUTO)) {
            midRate = corePrice.getMidRate();
            if(midRate > 0.0D) {
                midPriceCache.put(getKey(corePrice), midRate);
                sb.append("Mid A ").append(midRate).append(' ');
            } else if(tierPricingMetaData.isMissingPriceCalculationEnabled() &&
                        tierPricingMetaData.getTiers().size() > 0){
                useMissingPriceCalculation = true;
                double halfSpread = tierPricingMetaData.getTiers().first().getMissingPriceSpread()/pipsFactor/2.0D;
                //apply volatility multiplier
                double spreadMultiplier = tierPricingMetaData.isUseMultiplierFromService() ? volatilityFactor : tierPricingMetaData.getVolatilitySpreadMultiplier();
                if(tierPricingMetaData.isVolatilityEnabled() && spreadMultiplier != 0.0D){
                    halfSpread = halfSpread * spreadMultiplier;
                }
                if(corePrice.getBidRate() > 0.0) midRate = corePrice.getBidRate() + halfSpread;
                else if (corePrice.getOfferRate() > 0.0) midRate = corePrice.getOfferRate() - halfSpread;
                else {
                    Double cachedRate = midPriceCache.get(getKey(corePrice));
                    midRate = cachedRate == null ? 0.0D : cachedRate;
                }
                midRate = MathUtil.round(midRate, precision, BigDecimal.ROUND_HALF_EVEN);
                sb.append("Mid Miss ").append(midRate).append(' ');
            }else {
                sb.append("Mid A ").append(midRate).append(' ');
            }
        }else {
            midRate = corePricingMetaData.getMidRate();
            sb.append("Mid M ").append(midRate).append(' ');
        }
        return calculateTierdPrice(broker, currencyPair, midRate, precision, bidSkew, offerSkew, tierPricingMetaData, pipsFactor, volatilityFactor, sb, useMissingPriceCalculation);
    }
    private String getKey(CorePriceData corePrice){
        return corePrice.getOrganization() + '-' + corePrice.getCurrencyPair();
    }
    public String getKey(String broker, String cp){
        return broker + '-' + cp;
    }

    private TierdPriceData calculateTierdPrice(Organization broker, CurrencyPair currencyPair, double midRate, int precison, double bidSkew, double offerSkew, TierPricingMetaData tierPriceMetaData, double pipsFactor, double volatilityFactor, StringBuilder sb, boolean useMissingPriceCalculation){
       if(volatilityFactor == 0){
           volatilityFactor =1;
       }

        //apply skew
        double bidBase = midRate + bidSkew, offerBase = midRate + offerSkew;
        double mid = (bidBase + offerBase)/2.0;
        TierdPriceData tierdPrice = new TierdPriceData(broker.getShortName(), currencyPair.getName());
        if(tierPriceMetaData == null) return tierdPrice;

        double spreadMultiplier = tierPriceMetaData.isUseMultiplierFromService() ? volatilityFactor : tierPriceMetaData.getVolatilitySpreadMultiplier();
        sb.append(" vf ").append(tierPriceMetaData.isUseMultiplierFromService()).append(' ').append(tierPriceMetaData.isVolatilityEnabled()).append(spreadMultiplier);
        SortedSet<TierData> tiers = tierPriceMetaData.getTiers();
        if(!tiers.isEmpty() && bidBase > offerBase){
			TierData firstTier = tiers.first();
			double bosp = firstTier.getBidOfferSpread()/pipsFactor;
			if(bidBase - offerBase > bosp){ // inversion greater than bid offer spread of first tier
				bidBase = midRate; offerBase = midRate; //not applying skew
				/*double delta = ((bidBase - offerBase) - bosp)/2.0D;
				bidBase = bidBase - delta;
				offerBase = offerBase + delta;*/
				//now bidBase - offerBase should be equal to bosp
			}
		}
        Double lastBid = null, lastOffer = null;
        StringBuilder debugSB = new StringBuilder(200);
        if(log.isDebugEnabled()){
            debugSB.append("CalculateTierdPrice using MidRate voEn:").append(tierPriceMetaData.isVolatilityEnabled()).append(" useVSMFromService:").append(tierPriceMetaData.isUseMultiplierFromService()).append(" vsm:").append(spreadMultiplier);
        }
        for(TierData tier : tiers){
            double amount = tier.getTierAmount();
            //market spread
            double bidOfferSpread= useMissingPriceCalculation ? tier.getMissingPriceSpread() : tier.getBidOfferSpread();
            if(log.isDebugEnabled()) {
                String spName = useMissingPriceCalculation ? " misssp " : " bosp ";
                debugSB.append(" tier:").append(amount).append(spName).append(bidOfferSpread);
            }
            if(tierPriceMetaData.isVolatilityEnabled() && spreadMultiplier != 0.0D){
                bidOfferSpread = bidOfferSpread * spreadMultiplier;
            }
            if(log.isDebugEnabled()) {
                debugSB.append(" newBOSP:").append(bidOfferSpread);
            }
            double spread = bidOfferSpread / pipsFactor / 2.0D;
            double bid = bidBase - spread;
            double offer = offerBase + spread;

            //stale rate
            if(midRate == 0.0){
                bid = 0.0;
                offer = 0.0;
                bidBase = 0.0; offerBase = 0.0;
            }else {
				//negative rate
				if(bid < 0.0D) bid = 0.0D;
				if(offer < 0.0D) offer = 0.0D;
				//inverted rate
				if(bid > mid) bid = mid;
				if(offer < mid) offer = mid;
			}
            //hourglass rate
            if(lastBid == null) lastBid = bid;
            if(lastOffer == null) lastOffer = offer;
            if(bid > lastBid) bid = lastBid;
            if(offer < lastOffer) offer = lastOffer;
            lastBid = bid;
            lastOffer = offer;
            //round
            bid = MathUtil.round(bid, precison, BigDecimal.ROUND_HALF_EVEN);
            offer = MathUtil.round(offer, precison, BigDecimal.ROUND_HALF_EVEN);
            tierdPrice.addTier(amount, bid, offer);
            String spName = useMissingPriceCalculation ? " misssp " : " bosp ";
            sb.append('(').append("amt ").append(amount).append(spName).append(spread).append(" b ").append(bid).append(" o ").append(offer).append(')').append(']');
        }
        if(log.isDebugEnabled()){
            log.debug(debugSB.toString());
        }
        return tierdPrice;
    }

    public TierdPriceData fromPriceBookToTieredPrice(PriceBook priceBook, TierPricingMetaData tierDataSet, String broker, String cp, StringBuilder sb, double pipsFactor, int precision, double volatilityFactor){
        TierdPriceData tierdPriceData = new TierdPriceData(broker, cp);
        Collection<Price> bids = priceBook.getBids();
        Collection<Price> offers = priceBook.getOffers();
        Price bid0 = getPriceForAmt(bids, 0);
        Price offer0 = getPriceForAmt(offers, 0);
        if(bid0 != null && bid0.getRate() > 0 && offer0 != null && offer0.getRate() > 0) {
            double mid = (bid0.getRate() + offer0.getRate())/2.0;
            mid = MathUtil.round(mid, precision, BigDecimal.ROUND_HALF_EVEN);
            midPriceCache.put(getKey(broker, cp), mid);
            sb.append("(mid:").append(mid).append(')');
        }
        sb.append('[');
        double spreadMultiplier = tierDataSet.isUseMultiplierFromService() ? volatilityFactor : tierDataSet.getVolatilitySpreadMultiplier();
        spreadMultiplier = tierDataSet.isVolatilityEnabled() ? spreadMultiplier : 1.0;
        for(TierData tierData : tierDataSet.getTiers()){
            double tierAmount = tierData.getTierAmount();
            Price bid = getPriceForAmt(bids, tierAmount);
            Price offer = getPriceForAmt(offers, tierAmount);
            Double bidRate = null, offerRate = null;
            String missing = null;
            double bidSpread = (tierData.getBidSpread() * spreadMultiplier) / pipsFactor;
            double offerSpread = (tierData.getOfferSpread() * spreadMultiplier) / pipsFactor;
            if(bid != null && offer != null) {
                bidRate = bid.getRate();
                offerRate = offer.getRate();
                //spread applied in the QuoteAggregator
                //bidRate = MathUtil.round(bidRate - bidSpread, precision, BigDecimal.ROUND_HALF_EVEN);
                //offerRate = MathUtil.round(offerRate + offerSpread, precision, BigDecimal.ROUND_HALF_EVEN);
            }else if(tierDataSet.isMissingPriceCalculationEnabled()) {
                double missingSpread = (tierData.getMissingPriceSpread() * spreadMultiplier) / pipsFactor;
                if(bid != null){
                    bidRate = bid.getRate();
                    offerRate = bidRate + missingSpread;
                    offerRate = MathUtil.round(offerRate, precision, BigDecimal.ROUND_HALF_EVEN);
                    missing = "offer(" + bidRate + ':' + missingSpread + ')';
                } else if (offer != null) {
                    offerRate = offer.getRate();
                    bidRate = offerRate - missingSpread;
                    bidRate = MathUtil.round(bidRate, precision, BigDecimal.ROUND_HALF_EVEN);
                    missing = "bid(" + offerRate + ':' + missingSpread + ')';
                }else {
                    Double midRate = midPriceCache.get(getKey(broker, cp));
                    if(midRate != null) {
                        double halfSpread = missingSpread / 2.0;
                        bidRate = midRate - halfSpread;
                        offerRate = midRate + halfSpread;
                        bidRate = MathUtil.round(bidRate, precision, BigDecimal.ROUND_HALF_EVEN);
                        offerRate = MathUtil.round(offerRate, precision, BigDecimal.ROUND_HALF_EVEN);
                        missing = "both(" + midRate + ':' + missingSpread + ')';
                    }
                }
            }
            if(bidRate != null && offerRate != null) {
                tierdPriceData.addTier(tierAmount, bidRate, offerRate);
                sb.append("(amt ").append(tierAmount).append(" b ").append(bidRate);
                sb.append(" o ").append(offerRate);
                if(missing != null) sb.append(" missing=").append(missing);
                sb.append(')');
            }else if(tierDataSet.isMissingPriceCalculationEnabled()){
                sb.append("(amt ").append(tierAmount).append(" missing=ref price NA").append(')');
            }
        }
        sb.append(']');
        return tierdPriceData;
    }

    private Price getPriceForAmt(Collection<Price> prices, double amt){
        for(Price price : prices){
            if(price.getAmount() >= amt) return price;
        }
        return null;
    }
}
