package com.integral.marketmaker.service.coreprice;

import com.integral.broker.BrokerAdaptorUtil;
import com.integral.broker.cache.ProviderQuoteCache;
import com.integral.broker.cache.ProviderQuoteObserver;
import com.integral.broker.model.Product;
import com.integral.broker.price.PriceBook;
import com.integral.broker.publish.Publisher;
import com.integral.broker.quote.BrokerQuoteFactory;
import com.integral.broker.quote.QuoteHandler;
import com.integral.broker.subscribe.Subscriber;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.is.message.MarketRate;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.pool.IllegalPooledObjectStateException;
import com.integral.user.Organization;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 7/27/16
 */
public class CorePriceProviderQuoteCacheC implements ProviderQuoteCache {
    private final Log log;
    private final Organization brokerOrg;
    private final CurrencyPair currencyPair;
    protected transient ConcurrentMap<String, Quote> quoteMap = new ConcurrentHashMap<String, Quote>();
    private Set<String> providers;
    /*
        On addition of a new quote to the cache this counter is incremented by 1
     */
    private AtomicLong quoteCounter = new AtomicLong(0L);

    /*
        On change of first tier of Quote from any provider this counter is incremented by 1
     */
    private AtomicLong firstTierChangeCounter = new AtomicLong(0L);

    public CorePriceProviderQuoteCacheC(Organization org, CurrencyPair cp, Set<String> lps){
        this.brokerOrg = org;
        this.currencyPair = cp;
        this.providers = lps;
        String loggerName = "com.integral.mm.ProviderQuoteCache." + org.getShortName() + "." + cp.getBaseCurrency().getName() + "." + cp.getVariableCurrency().getName();
        log = LogFactory.getLog(loggerName);
    }

    @Override
    public void start() {
        QuoteHandler quoteHandler = BrokerQuoteFactory.getInstance().getQuoteHandler(brokerOrg);
        quoteHandler.addObserver(currencyPair, this);
    }

    @Override
    public void stop() {
        quoteMap.clear();
        QuoteHandler quoteHandler = BrokerQuoteFactory.getInstance().getQuoteHandler(brokerOrg);
        quoteHandler.removeObserver(currencyPair, this);
    }

    @Override
    public Set<Organization> getProviders() {
        throw new UnsupportedOperationException();
    }

    public void setProviders(Set<String> newProviders){
        newProviders = new HashSet<String>(newProviders);
        Set<String> toBeRemoved = new HashSet<String>(providers);
        toBeRemoved.removeAll(newProviders);
        providers = newProviders;
        for(String lp : toBeRemoved){
            Quote oldQuote = quoteMap.remove(lp);
            if(oldQuote != null) decrementRefCounter(oldQuote);
        }
    }

    @Override
    public Collection<Quote> getQuotes() {
        List<Quote> quotes = new ArrayList<Quote>();
        for(Quote quote : quoteMap.values()){
            if(!quote.isActive()) continue;
            if(quote.getCurrencyPair().isMatch(currencyPair)) quotes.add(quote);
        }
        return quotes;
    }

    @Override
    public Map<Organization, List<Quote>> getQuoteMap() {
        return null;
    }

    @Override
    public boolean clearUpdated() {
        return false;
    }

    @Override
    public boolean updated() {
        return false;
    }

    @Override
    public void addQuote(Quote quote) {
        String lpName = quote.getOrganization().getShortName();
        if(!providers.contains(lpName)){
            if(log.isDebugEnabled()) log.debug("CorePriceProviderQuoteCacheC.addQuote:: non mm lp=" + lpName + ", bk=" + brokerOrg.getShortName() + ", cp=" + currencyPair.getName());
            return;
        }
        /*
            MM-618. Checked with Simon. Inactive rate from LP should be processed.
            If INACTIVE rate is received from LP remove the last quote from quote cache so that stale/old quotes are not used for
            core price generation
        */
        if(quote.isActive() && incrementRefCounter(quote)) {
            Quote oldQuote = quoteMap.put(lpName, quote);
            if (oldQuote != null) {
                boolean firstTierChanged = hasFirstTierChanged(oldQuote, quote);
                if (firstTierChanged) {
                    firstTierChangeCounter.incrementAndGet();
                }
                decrementRefCounter(oldQuote);
            } else {
                firstTierChangeCounter.incrementAndGet();
            }
            quoteCounter.incrementAndGet();
        }
        else{
            /*
                Failed to increment the reference count. Remove the old Quote so that stale price is  not used in
                core price generation.
             */
            Quote removedQuote = quoteMap.remove(lpName);
            if( removedQuote != null ){
                decrementRefCounter(removedQuote);
                quoteCounter.incrementAndGet();
                firstTierChangeCounter.incrementAndGet();
            }
            if (!quote.isActive()) {
                if (log.isDebugEnabled()) {
                    log.debug("CorePriceProviderQuoteCacheC.addQuote:: stale lp=" + lpName + ", bk=" + brokerOrg.getShortName() + ", cp=" + currencyPair.getName());
                }
            }
        }
    }

    private boolean hasFirstTierChanged(Quote oldQuote, Quote newQuote){
        double oldBid = oldQuote.getBids().isEmpty() ? 0 : ((FXLegDealingPrice)oldQuote.getBidQuotePriceAtIndex(0)).getRate();
        double newBid = newQuote.getBids().isEmpty() ? 0 : ((FXLegDealingPrice) newQuote.getBidQuotePriceAtIndex(0)).getRate();
        double oldOffer = oldQuote.getOffers().isEmpty() ? 0 : ((FXLegDealingPrice) oldQuote.getOfferQuotePriceAtIndex(0)).getRate();
        double newOffer = newQuote.getOffers().isEmpty() ? 0 : ((FXLegDealingPrice) newQuote.getOfferQuotePriceAtIndex(0)).getRate();
        return oldBid != newBid || oldOffer != newOffer;
    }

    private boolean incrementRefCounter(Quote quote){
        try {
            BrokerAdaptorUtil.getInstance().incrementRefCounter(quote);
            return true;
        }catch (IllegalPooledObjectStateException e){
            String lpName = quote.getOrganization().getShortName();
            log.warn("CorePriceProviderQuoteCacheC.addQuote:: failed to increment ref count. lp=" + lpName + ", bk=" + brokerOrg.getShortName() + ", cp=" + currencyPair.getName() + ", quid=" + quote.getGUID());
        }
        return false;
    }

    private void decrementRefCounter(Quote quote){
        BrokerAdaptorUtil.getInstance().decrementRefCounter(quote);
    }

    @Override
    public void removeQuote(Organization org) {
        if(org != null) {
            Quote oldQuote = quoteMap.remove(org.getShortName());
            if(oldQuote != null) decrementRefCounter(oldQuote);
        }
    }

    @Override
    public boolean removeQuote(Quote quote, boolean isRejected) {
        String lpName = quote.getOrganization().getShortName();
        boolean removed = quoteMap.remove(lpName, quote);
        if(removed) decrementRefCounter(quote);
        return removed;
    }

    @Override
    public void handleEvent(char event, Organization org) {
        log.info("CorePriceProviderQuoteCache.handleEvent:: ev=" + event + ", bk=" + brokerOrg.getShortName() + ", cp=" + currencyPair.getName() + ", lp=" + org.getShortName());
        if ( event == Subscriber.UNSUBSCRIBE_EVENT ) {
            Quote removedQuote = quoteMap.remove(org.getShortName());
            if(removedQuote != null) decrementRefCounter(removedQuote);
        }
    }

    @Override
    public boolean addObserver(ProviderQuoteObserver observer) {
        return false;
    }

    @Override
    public boolean removeObserver(ProviderQuoteObserver observer) {
        return false;
    }

    @Override
    public boolean attachPublisher(Publisher publisher) {
        return false;
    }

    @Override
    public boolean removePublisher(Publisher publisher) {
        return false;
    }

    @Override
    public boolean clearBestPriceUpdated() {
        return false;
    }

    @Override
    public Product getProduct() {
        return null;
    }

    @Override
    public void setRawRate(PriceBook raw) {

    }

    @Override
    public void rateReceived(MarketRate rate) {

    }

    @Override
    public BestProviderPrice getBestProviderPrice() {
        return null;
    }


    public AtomicLong getQuoteCounter() {
        return quoteCounter;
    }

    public AtomicLong getFirstTierChangeCounter() {
        return firstTierChangeCounter;
    }
}
