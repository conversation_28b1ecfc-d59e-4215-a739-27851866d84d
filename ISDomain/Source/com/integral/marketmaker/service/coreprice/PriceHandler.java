package com.integral.marketmaker.service.coreprice;

import com.integral.aggregation.AggregationResponseHandler;
import com.integral.aggregation.Response;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.exception.IdcException;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;

/**
 * Created by verma on 4/20/17.
 */
class PriceHandler implements AggregationResponseHandler<Response, FXPriceBook>, MessageHandler {
    private static Log log = LogFactory.getLog(PriceHandler.class);

    private final TOBBasedCorePriceServiceSubscription subscription;
    private String subscriptionKey;

    public PriceHandler(TOBBasedCorePriceServiceSubscription subscription) {
        this.subscription = subscription;
        this.subscriptionKey = "TOBBasedCorePriceServiceSubscription";
    }

    /**
     * Called when subscription/un-subscription request response is ready. This is a case when request is submitted to
     * aggregation service asynchronously.
     *
     * @param response
     */
    @Override
    public void onCompleted(Response response) {

    }

    /**
     * Called on every aggregated price book update.
     *
     * @param book aggregated view
     */
    @Override
    public void onPriceUpdate(FXPriceBook book) {
        try {
            subscription.onPriceUpdate(book);
        } catch (Exception ex) {
            log.warn("onPriceUpdate : Exception ", ex);
        }
    }

    @Override
    public Message handle(Message message) throws IdcException {
        if( log.isDebugEnabled() ){
            log.debug("handle : Skipped Message="+message);
        }
        return null;
    }
    @Override
    public String getSubscriptionKey(){
        return subscriptionKey;
    }
}
