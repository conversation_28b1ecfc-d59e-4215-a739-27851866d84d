package com.integral.marketmaker.service.coreprice;

import java.util.SortedSet;
import java.util.TreeSet;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 7/28/16
 */
public class TierdPriceData {
    private final String organization;
    private final String currencyPair;
    private final SortedSet<Tier> tiers = new TreeSet<Tier>();

    public TierdPriceData(String broker, String cp){
        this.organization = broker;
        this.currencyPair = cp;
    }

    public String getOrganization() {
        return organization;
    }

    public String getCurrencyPair() {
        return currencyPair;
    }

    public SortedSet<Tier> getTiers() {
        return tiers;
    }

    public void addTier(double amt, double bid, double offer){
        Tier tier = new Tier(amt, bid, offer);
        tiers.add(tier);
    }

    @Override
    public String toString(){
        StringBuilder sb = new StringBuilder("TierdPriceData [");
        sb.append("org=").append(organization).append(", cp=").append(currencyPair).append(", tiers=").append(tiers).append("]");
        return sb.toString();
    }

    public static class Tier implements Comparable<Tier>{
        private double amount;
        private double bidRate;
        private double offerRate;

        public Tier(double amt, double bid, double offer){
            this.amount = amt;
            this.bidRate = bid;
            this.offerRate = offer;
        }

        public double getAmount() {
            return amount;
        }

        public void setAmount(double amount) {
            this.amount = amount;
        }

        public double getBidRate() {
            return bidRate;
        }

        public void setBidRate(double bidRate) {
            this.bidRate = bidRate;
        }

        public double getOfferRate() {
            return offerRate;
        }

        public void setOfferRate(double offerRate) {
            this.offerRate = offerRate;
        }

        @Override
        public boolean equals(Object other){
            return other instanceof Tier && ((Tier)other).amount == this.amount;
        }
        @Override
        public int hashCode(){
            return (int)amount;
        }

        @Override
        public String toString(){
            StringBuilder sb = new StringBuilder("Tier [");
            sb.append("amt=").append(amount).append(", bid=").append(bidRate).append(", offer=").append(offerRate).append("]");
            return sb.toString();
        }

        @Override
        public int compareTo(Tier other) {
            if (amount < other.amount ) {
                return -1;
            }
            return amount == other.amount ? 0 : 1;
        }
    }
}
