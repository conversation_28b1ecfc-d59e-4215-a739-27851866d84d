package com.integral.marketmaker.service.coreprice;

import com.integral.finance.currency.CurrencyPair;
import com.integral.user.Organization;
import com.integral.util.collections.ConcurrentHashSet;

/**
 * Created by verma on 1/13/16.
 */
public abstract class Subscription {
    enum State {
        /*
            Subscription state is CREATED
         */
        CREATED,
        /*
            Subscription state is SUBSCRIBED
         */
        SUBSCRIBED,

        /*
            Subscription state is UNSUBSCRIBED
         */
        UNSUBSCRIBED,

        /*
            Subscription state is DIRTY
         */
        DIRTY
    }

    protected State state;
    protected final Organization organization;
    protected final CurrencyPair currencyPair;
    protected final ConcurrentHashSet<CorePriceService.Handler> handlers = new ConcurrentHashSet<CorePriceService.Handler>();

    public Subscription(Organization organization, CurrencyPair currencyPair) {
        this.organization = organization;
        this.currencyPair = currencyPair;
        this.state = State.CREATED;
    }

    public void addHandler(CorePriceService.Handler handler) {
        if( handler != null ) {
            handlers.add(handler);
        }
    }

    public Organization getOrganization() {
        return organization;
    }

    public CurrencyPair getCurrencyPair() {
        return currencyPair;
    }

    public State getState() {
        return state;
    }

    public void setState(State state) {
        this.state = state;
    }

    public ConcurrentHashSet<CorePriceService.Handler> getHandlers() {
        return handlers;
    }

    public abstract CorePriceData getLatestUpdate();

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("Subscription{");
        sb.append("state=").append(state);
        sb.append(", organization=").append(organization);
        sb.append(", currencyPair=").append(currencyPair);
        sb.append('}');
        return sb.toString();
    }
}
