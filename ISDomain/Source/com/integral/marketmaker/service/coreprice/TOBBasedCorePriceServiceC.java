package com.integral.marketmaker.service.coreprice;

import com.integral.is.common.liquidityProvision.LiquidityProvisionChangeHandler;
import com.integral.is.common.liquidityProvision.functor.LiquidityProvisionRemoteNotificationFunctor;
import com.integral.is.common.maintenance.MaintenanceAgent;
import com.integral.log.Log;
import com.integral.log.LogFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * Created by verma on 1/11/16.
 */
public class TOBBasedCorePriceServiceC extends CorePriceServiceC<TOBBasedCorePriceServiceSubscription>{
    private static final Log log = LogFactory.getLog(TOBBasedCorePriceServiceC.class);
    protected final ProvisionUpdateHandler provisionUpdateHandler;

    public TOBBasedCorePriceServiceC() {
        super();
        this.provisionUpdateHandler = new ProvisionUpdateHandler();
    }

    /**
     * Start the service.
     *
     * @return true if the start is successful.
     */
    @Override
    public boolean start() {
        boolean success = super.start();
        if( success ){
            LiquidityProvisionRemoteNotificationFunctor.addObserver( provisionUpdateHandler  );
        }
        return success;
    }

    public boolean stop(){
        boolean success = super.stop();
        if( success ){
            LiquidityProvisionRemoteNotificationFunctor.removeObserver(provisionUpdateHandler);
        }
        return success;
    }

    @Override
    protected TOBBasedCorePriceServiceSubscription createSubscription(SubscribeRequest request) {
        TOBBasedCorePriceServiceSubscription subscription = new TOBBasedCorePriceServiceSubscription(request.getOrganization(),request.getCurrencyPair());
        return subscription;
    }

    @Override
    protected void subscriptionDone(TOBBasedCorePriceServiceSubscription subscription) {

    }

    @Override
    protected SubscriptionWorker createSubscriptionWorker(TOBBasedCorePriceServiceSubscription subscription) {
        return new SubscriptionWorkerForAggregationService(subscription);
    }

    @Override
    protected void subscriptionWorkerCreated(SubscriptionWorker newWorker) {
        provisionUpdateHandler.subscriptionWorkerCreated(newWorker);
    }

    private class ProvisionUpdateHandler implements LiquidityProvisionChangeHandler {
        private ConcurrentHashMap<String,AtomicInteger> provisionVersionCounterMap = new ConcurrentHashMap<String, AtomicInteger>();

        ProvisionUpdateHandler() {
        }

        /**
         * This notification is called when liquidity rules and price making defined for an org are changed.
         * A liquidity rule may be added, removed or changed in sort order for an FI org.
         * Price making parameters may be changed, added for an FI org.
         *
         * @param orgName FI Org for which the liquidity change happened.
         * @param props   Properties that were notified with the change.
         */
        @Override
        public void handleChange(String orgName, Map props) {
            if( orgName == null || orgName.trim().equals("")){
                return;
            }

            /*
                Schedule all workers for the given organization for execution
             */
            boolean provisionVersionCounterIncremented = false;

            for(Map.Entry<TOBBasedCorePriceServiceSubscription, SubscriptionWorker> entry : getSubscriptionWorkerMap().entrySet()){
                SubscriptionWorker worker = entry.getValue();
                Subscription subscription = worker.getSubscription();
                if( subscription != null && subscription.getOrganization() != null){
                    if( orgName.equals(subscription.getOrganization().getShortName())){
                        /*
                            Increment the provision version counter for an organization when the first worker is found.
                         */
                        if( !provisionVersionCounterIncremented ){
                            AtomicInteger counter = getProvisionVersionCounter(orgName);
                            int count = counter.incrementAndGet();
                            log.info("ProvisionUpdateHandler : Version Counter incremented to "+count+" for "+orgName);
                            provisionVersionCounterIncremented = true;
                        }
                        MaintenanceAgent.getSingleThreadedExecutorService().execute(entry.getValue());
                    }
                }
            }
        }

        AtomicInteger getProvisionVersionCounter(String orgName) {
            AtomicInteger counter = provisionVersionCounterMap.get(orgName);
            if( counter == null ){
                AtomicInteger newCounter = new AtomicInteger();
                AtomicInteger cachedCounter = provisionVersionCounterMap.putIfAbsent(orgName,newCounter);
                if( cachedCounter != null ){
                    counter = cachedCounter;
                }
                else{
                    counter = newCounter;
                }
            }
            return counter;
        }


        void subscriptionWorkerCreated(SubscriptionWorker newWorker) {
            AtomicInteger counter = getProvisionVersionCounter(newWorker.getSubscription().getOrganization().getShortName());
            newWorker.setProvisionCounter(counter);
        }
    }
}