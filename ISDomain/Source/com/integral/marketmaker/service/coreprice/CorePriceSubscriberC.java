package com.integral.marketmaker.service.coreprice;

import com.integral.broker.BrokerAdaptorUtil;
import com.integral.broker.model.Product;
import com.integral.broker.subscribe.Subscriber;
import com.integral.broker.subscription.BrokerSubscriptionManager;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.common.BaseProvider;
import com.integral.is.common.Provider;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.ProviderManagerC;
import com.integral.marketmaker.config.CorePriceConfig;
import com.integral.marketmaker.provider.ProvidersMetaData;
import com.integral.marketmaker.startup.MMProviderStatusObserver;
import com.integral.persistence.cache.ReferenceDataCache;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.collections.ConcurrentHashSet;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 7/28/16
 */
public class CorePriceSubscriberC implements Subscriber {
    private static final Log log = LogFactory.getLog(CorePriceSubscriberC.class);
    private final Organization brokerOrg;
    private final CurrencyPair currencyPair;
    private final CorePriceConfig config;
    private boolean running;

    public CorePriceSubscriberC(Organization org, CurrencyPair cp, CorePriceConfig config){
        this.brokerOrg = org;
        this.currencyPair = cp;
        this.config = config;
    }

    public synchronized void start(){
        if(!running) {
            subscribe();
            running = true;
        }else {
            log.info("CorePriceSubscriberC.start:: already running " + brokerOrg.getShortName() + " " + currencyPair.getName());
        }
    }

    private void subscribe(){
        User user = getUser();
        BrokerSubscriptionManager subscriptionManager = BrokerSubscriptionManager.getInstance();
        ProvidersMetaData providerList = config.getProviderList(currencyPair);
        if(providerList == null) {
            log.info("CorePriceSubscriberC.start:: No providers to subscribe to.");
            return;
        }
        Set<String> providers = providerList.getProviderStatus().keySet();
        ConcurrentHashMap<String, Provider> providerMap = ProviderManagerC.getInstance().getProviderMap();
        for(String providerName : providers){
            Organization organization = ReferenceDataCacheC.getInstance().getOrganization(providerName);
            subscriptionManager.subscribe(user, currencyPair, organization, user);
            log.info("CorePriceSubscriberC.start:: subscribed for bk=" + brokerOrg.getShortName() + ", cp=" + currencyPair.getName() + ", lp=" + providerName);
            Provider provider = providerMap.get(providerName);
            if( null!=provider) {
                provider.addProviderStatusObserver(new MMProviderStatusObserver(providerName));
            }
        }
    }

    public synchronized void reSubscribe(){
        if(!running){
            log.warn("CorePriceSubscriberC.reSubscribe:: not running. " + brokerOrg.getShortName() + " " + currencyPair.getName());
        }
        subscribe();
    }

    public synchronized void stop(){
        running = false;
    }

    public synchronized void removeProviders(List<Organization> providers){
        //providerList.removeAll(providers);
        // un subscribe
    }

    private User getUser(){
        User user = brokerOrg.getDefaultDealingUser();
        if(user == null){
            log.warn("CorePriceSubscriberC:: Default Dealing User is null, no subscriptions done");
            throw new RuntimeException("Default Dealing User is null");
        }
        return user;
    }

    @Override
    public Product getProduct() {
        return null;
    }

    @Override
    public void setProduct(Product product) {

    }

    @Override
    public void stop(Collection<Organization> providers) {

    }

    @Override
    public void stopExcept(Organization provider) {

    }

    @Override
    public void startProvider(BaseProvider bp) {

    }

    @Override
    public void update(boolean isProductActive) {

    }

    public String getCurrencyPair(){
        return currencyPair.getName();
    }

    public synchronized boolean isRunning(){
        return running;
    }
}
