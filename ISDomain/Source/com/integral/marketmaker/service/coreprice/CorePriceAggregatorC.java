package com.integral.marketmaker.service.coreprice;

import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.math.MathUtil;
import com.integral.user.Organization;
import com.integral.util.CollectionUtil;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 7/28/16
 */
public class CorePriceAggregatorC {
    private final Log log;
    private final Organization broker;
    private final CurrencyPair currencyPair;

    public CorePriceAggregatorC(Organization org, CurrencyPair cp){
        this.broker = org;
        this.currencyPair = cp;
        String loggerName = "com.integral.mm.Aggregator." + org.getShortName() + "." + cp.getBaseCurrency().getName() + "." + cp.getVariableCurrency().getName();
        log = LogFactory.getLog(loggerName);
    }

    public CorePriceData aggregate(Collection<Quote> quotes, int precision, Map<String, String> providerStatus, StringBuilder sb){
        CorePriceData corePrice = new CorePriceData();
        corePrice.setOrganization(broker.getShortName());
        corePrice.setCurrencyPair(currencyPair.getName());
        Set<String> providerSet = providerStatus.keySet();
        sb.append("Quotes:[");
        for(Quote quote : quotes){
            String provider = quote.getOrganization().getShortName();
            if(!providerSet.contains(provider)) {
                sb.append("(Non MM Pvdr ").append(provider).append(')');
                continue;
            }
            double bidRate = 0, offerRate = 0;
            sb.append("(Pvdr ").append(provider).append(' ');
            if(!CollectionUtil.isEmptyOrNull(quote.getBids())){
                bidRate = quote.getBids().get(0).getRate();
                sb.append("bid ").append(bidRate).append(' ');
            }
            if(!CollectionUtil.isEmptyOrNull(quote.getOffers())){
                offerRate = quote.getOffers().get(0).getRate();
                sb.append("offer ").append(offerRate).append(' ');
            }
            sb.append(')');
            corePrice.addProviderPrice(provider, bidRate, offerRate);
        }
        sb.append(']');
        calculateBestPrice(corePrice, precision, providerStatus, sb);
        return corePrice;
    }

    private void calculateBestPrice(CorePriceData corePrice, int precision, Map<String, String> providerStatus, StringBuilder sb){
        Double bestBid = null, bestOffer = null, midRate;
        sb.append("[Inact (");
        for(ProviderPriceData providerPrice : corePrice.getProviderPrices()){
            if(isActive(providerPrice.getProvider(), providerStatus)){
                double bidPrice = providerPrice.getBidPrice();
                if(bestBid == null || bidPrice > bestBid){
                    bestBid = bidPrice;
                }
                double offerPrice = providerPrice.getOfferPrice();
                if((bestOffer == null || offerPrice < bestOffer) && offerPrice > 0){
                    bestOffer = offerPrice;
                }
            }else {
                sb.append(providerPrice.getProvider()).append(' ');
            }
        }
        sb.append(')');
        if(bestBid != null) corePrice.setBidRate(bestBid);
        if(bestOffer != null) corePrice.setOfferRate(bestOffer);
        if(bestBid == null || bestOffer == null) return;
        if(bestBid == 0.0D || bestOffer == 0.0D) return;
        midRate = (bestBid + bestOffer) / 2.0D;
        midRate = MathUtil.round(midRate, precision, BigDecimal.ROUND_HALF_EVEN);
        sb.append("bid ").append(bestBid).append(" mid ").append(midRate).append(" off ").append(bestOffer).append(']');
        //inverted rate check is not required
        /*if(bestBid > midRate){
            bestBid = midRate;
        }
        if(bestOffer < midRate){
            bestOffer = midRate;
        }*/
        corePrice.setBidRate(bestBid);
        corePrice.setOfferRate(bestOffer);
        corePrice.setMidRate(midRate);
    }

    private boolean isActive(String provider, Map<String, String> providerStatus){
        return "ON".equalsIgnoreCase(providerStatus.get(provider));
    }
}
