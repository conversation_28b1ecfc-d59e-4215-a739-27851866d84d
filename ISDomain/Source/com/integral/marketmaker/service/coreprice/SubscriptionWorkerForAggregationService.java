package com.integral.marketmaker.service.coreprice;

import com.integral.aggregation.AggregationMethod;
import com.integral.aggregation.AggregationService;
import com.integral.aggregation.Response;
import com.integral.aggregation.subscription.AggregationRequest;
import com.integral.aggregation.subscription.AggregationRequestC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.user.Organization;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by verma on 4/20/17.
 */
public class SubscriptionWorkerForAggregationService extends SubscriptionWorker {
    private AggregationRequest currentAggregationRequest;
    private static AtomicInteger aggregationRequestCounter = new AtomicInteger(0);
    private final TOBBasedCorePriceServiceSubscription subscription;

    public SubscriptionWorkerForAggregationService(TOBBasedCorePriceServiceSubscription subscription) {
        super(subscription);
        this.subscription = subscription;
    }

    protected boolean stopSubscription() {
        if (currentAggregationRequest != null) {
            try {
                ISUtilImpl.getInstance().setSessionContext( currentAggregationRequest.getUser() );
                Response response = AggregationService.getInstance().unSubscribe(currentAggregationRequest);
                switch (response.getStatus()) {
                    case Response.STATUS_OK:
                        currentAggregationRequest = null;
                        return true;
                    case Response.STATUS_FAIL:
                        return false;
                }
            } catch (Exception ex) {
                log.error("stopSubscription : Exception for Request=" + currentAggregationRequest, ex);
            }
            return false;
        }
        return true;
    }

    protected boolean startSubscription() {
        try {
            int currentProvisionCounter = provisionCounter.get();
            currentAggregationRequest = createAggregationRequest();
            ISUtilImpl.getInstance().setSessionContext( currentAggregationRequest.getUser() );
            Response response = AggregationService.getInstance().subscribe(currentAggregationRequest);
            switch (response.getStatus()) {
                case Response.STATUS_OK:
                    lastSubscriptionProvisionCounter = currentProvisionCounter;
                    return true;
                case Response.STATUS_FAIL:
                    log.info("SubscriptionWorker.startSubscription : Failed for " + subscription);
                    return false;
            }
        } catch (Exception ex) {
            log.error("startSubscription : Exception for Request=" + currentAggregationRequest, ex);
        }
        return false;
    }

    @Override
    protected String getRequestUserNamePrefix() {
        return "YM_MM_CP_AggUser_";
    }

    private AggregationRequest createAggregationRequest() {
        AggregationRequestC r = new AggregationRequestC();
        r.setRequestId("YM_MM_CP_REQ_"+ aggregationRequestCounter.incrementAndGet());
        r.setOrganization(subscription.getOrganization());
        r.setCcyPair(subscription.getCurrencyPair());
        r.setDealtCcy(subscription.getCurrencyPair().getBaseCurrency());
        r.setUser(getRequestUserForOrganization(subscription.getOrganization()));
        r.setAggregationMethod(AggregationMethod.FULL_BOOK);
        r.setPriceHandler(createPriceHandler());
        Collection<Organization> providers = ISUtilImpl.getInstance().getAggregationProvidersFromLiquidityRule(subscription.getOrganization(),subscription.getCurrencyPair());
        if( providers != null && !providers.isEmpty() ){
            List<Organization> pList = new ArrayList<Organization>(providers.size());
            for( Organization org : providers){
                if( org.isSameAs(subscription.getOrganization())){
                    continue;
                }
                if( org.isBroker() || org.isExternalProvider() ){
                    pList.add(org);
                }
            }
            if( pList.size() > 1 ){
                r.setDropFXIDirectStreamPrices(true);
            }
            r.setPriceProviders(pList);
        }
        return r;
    }

    private PriceHandler createPriceHandler() {
        return new PriceHandler(subscription);
    }
}
