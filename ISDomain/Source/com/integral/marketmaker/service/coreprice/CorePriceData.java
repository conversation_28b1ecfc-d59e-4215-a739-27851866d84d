package com.integral.marketmaker.service.coreprice;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by verma on 3/7/16.
 */
public class CorePriceData {
    private String organization;
    private String currencyPair;
    private double bidRate;
    private double offerRate;
    private double midRate;
    private List<ProviderPriceData> providerPrices = new ArrayList<ProviderPriceData>();

    public double getBidRate() {
        return bidRate;
    }

    public void setBidRate(double bidRate) {
        this.bidRate = bidRate;
    }

    public double getOfferRate() {
        return offerRate;
    }

    public void setOfferRate(double offerRate) {
        this.offerRate = offerRate;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public String getCurrencyPair() {
        return currencyPair;
    }

    public void setCurrencyPair(String currencyPair) {
        this.currencyPair = currencyPair;
    }

    public double getMidRate() {
        return midRate;
    }

    public void setMidRate(double midRate) {
        this.midRate = midRate;
    }

    public void addProviderPrice(String name, double bid, double offer){
        ProviderPriceData providerPrice = new ProviderPriceData(name, bid, offer);
        providerPrices.add(providerPrice);
    }

    public List<ProviderPriceData> getProviderPrices(){
        return providerPrices;
    }

    public void reset(){
        this.bidRate = 0.0D;
        this.offerRate = 0.0D;
        this.midRate = 0.0D;
        providerPrices.clear();
    }

    @Override
    public String toString(){
        StringBuilder sb = new StringBuilder("CorePriceData [");
        sb.append("org=").append(organization).append(", cp=").append(currencyPair).append(", bid=").append(bidRate).append(", offer=").append(offerRate).append(", mid=").append(midRate);
        sb.append("providerPrices=").append(providerPrices).append("]");
        return sb.toString();
    }
}
