package com.integral.marketmaker.service.coreprice;

import com.integral.aggregation.price.FXPriceBook;
import com.integral.commons.counter.AtomicCounter;
import com.integral.finance.currency.CurrencyPair;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.Organization;

/**
 * Created by verma on 3/7/16.
 */
public class TOBBasedCorePriceServiceSubscription extends Subscription {
    /*
        The Data is typically written by a single thread.
        Read operation is from the last written position.
        The writer can override data that is being read. This is however controlled by CAPACITY.
        It is assumed that a thread reading the Data object completes its read before the given slot gets overridden by the
        writer.
        E.g. If there are 4 write operations per second it will take 16 seconds for a given slot to be reused thus providing
        a buffer between read and write operations.
     */
    public final int baseCcyIdx;
    public final int termCcyIdx;
    public final int orgIdx;
    private CorePriceData[] corePriceDataArr;
    private final AtomicCounter counter = new AtomicCounter();
    private volatile int indexForRead = -1;
    private final int mask;
    private static Log log = LogFactory.getLog(TOBBasedCorePriceServiceSubscription.class);

    public TOBBasedCorePriceServiceSubscription(Organization organization, CurrencyPair currencyPair) {
        super(organization, currencyPair);
        this.orgIdx = organization.getIndex();
        this.baseCcyIdx = currencyPair.getBaseCurrency().getIndex();
        this.termCcyIdx = currencyPair.getVariableCurrency().getIndex();
        int CAPACITY = 64;
        corePriceDataArr = new CorePriceData[CAPACITY];
        for (int i = 0; i < CAPACITY; ++i) {
            corePriceDataArr[i] = new CorePriceData();
        }
        mask = CAPACITY - 1;
    }

    public CorePriceData getLatestUpdate() {
        int idx = indexForRead;
        if (idx != -1) {
            return corePriceDataArr[idx];
        }
        return null;
    }

    public void onPriceUpdate(FXPriceBook book) {
        /*
            Calculate and Cache the Core Price and Skew
        */
        double bidRate = 0.0D;
        double offerRate = 0.0D;

        if (book != null && !book.isStale()) {
            if (book.getBids().size() > 0 && book.getOffers().size() > 0) {
                bidRate = book.getBid(0).getRate();
                offerRate = book.getOffer(0).getRate();
            }
        }
        if( log.isDebugEnabled() ){
            log.debug("onPriceUpdate : subscription="+toString()+", bidRate="+bidRate+", offerRate="+offerRate);
        }

        /*
            Get the slot for writing
         */
        long writeCounter;
        do {
            writeCounter = counter.get();
        }
        while (!counter.compareAndSet(writeCounter, writeCounter + 1));

        int idx = (int) (writeCounter & mask);

        CorePriceData corePriceData = corePriceDataArr[idx];
        corePriceData.reset();
        corePriceData.setBidRate(bidRate);
        corePriceData.setOfferRate(offerRate);
        indexForRead = idx;
        invokeHandlersOnCorePriceUpdate();
    }

    private void invokeHandlersOnCorePriceUpdate() {
        for (CorePriceService.Handler handler : getHandlers()) {
            try {
                handler.corePriceDataUpdated();
            }
            catch (Throwable th){
                if( log.isDebugEnabled() ){
                    log.debug("invokeHandlersOnCorePriceUpdate : Exception for handler "+handler,th);
                }
            }
        }
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder(100).append("TOBBasedCorePriceServiceSubscription{");
        sb.append(" org=").append(organization);
        sb.append(", ccyPair=").append(currencyPair);
        sb.append(", baseCcyIdx=").append(baseCcyIdx);
        sb.append(", termCcyIdx=").append(termCcyIdx);
        sb.append(", orgIdx=").append(orgIdx);
        sb.append('}');
        return sb.toString();
    }
}
