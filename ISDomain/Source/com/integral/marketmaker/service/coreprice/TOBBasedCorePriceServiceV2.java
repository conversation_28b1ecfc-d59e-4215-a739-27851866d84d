package com.integral.marketmaker.service.coreprice;

/**
 * Created by verma on 4/20/17.
 */
public class TOBBasedCorePriceServiceV2 extends TOBBasedCorePriceServiceC {
    public TOBBasedCorePriceServiceV2() {
    }

    @Override
    protected SubscriptionWorker createSubscriptionWorker(TOBBasedCorePriceServiceSubscription subscription) {
        return new SubscriptionWorkerForRequestService(subscription);
    }
}