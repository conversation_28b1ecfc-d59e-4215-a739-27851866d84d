package com.integral.marketmaker.service.coreprice;

import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.broker.aggregate.QuoteAggregator;
import com.integral.broker.model.*;
import com.integral.broker.sort.PriceTimeSorterC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.MarketMakerAggregationLogger;
import com.integral.marketmaker.TierData;
import com.integral.marketmaker.config.CorePriceConfig;
import com.integral.marketmaker.corepricing.CorePricingMetaData;
import com.integral.marketmaker.provider.ProvidersMetaData;
import com.integral.marketmaker.tierpricing.TierPricingMetaData;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;

import java.util.*;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 1/10/17
 */
public class CorePriceProductUtil {
    private static final Log log = LogFactory.getLog(CorePriceProductUtil.class);
    public static Product createProduct(CorePricingMetaData corePriceData, TierPricingMetaData tierPricingMetaData, CorePriceConfig config, QuoteAggregator aggregator, double volatilityFactor){
        try {
            String cp = corePriceData.getCcyPair();
            String org = corePriceData.getBrokerOrgName();
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPairFromString(cp);
            Organization organization = OrganizationUtil.getOrganization(org);

            Product product = new ProductC();
            CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(cp);
            product.setCurrencyPair(currencyPair);
            product.setESP(true);
            product.setObjectID(System.currentTimeMillis());
            product.setActive(true);
            product.setOrderExecutionActive(false);
            product.setNamespace(organization.getNamespace());

            Configuration configuration = new ConfigurationC();
            product.setConfiguration(configuration);
            configuration.setShortName("MM_CFG_" + cp.replace('/', '_') + "_" + org);
            configuration.setObjectID(System.currentTimeMillis());
            configuration.setActive(true);
            ProvidersMetaData providerList = config.getProviderList(ccyPair);
            if(providerList == null){
                log.warn("CorePriceProductUtil.createProduct:: ProvidersMetaData not present. Providers will be empty for " + org + " " + cp);
            }
            Map<String, String> providerStatus = providerList != null ? providerList.getProviderStatus() : new HashMap<String, String>();
            List<Organization> activeProviders = new ArrayList<Organization>();
            for (Map.Entry<String, String> entry : providerStatus.entrySet()) {
                if (entry.getValue().equalsIgnoreCase("ON")) {
                    Organization provider = OrganizationUtil.getOrganization(entry.getKey());
                    if (provider != null) activeProviders.add(provider);
                }
            }
            configuration.setPriceProviders(activeProviders);
            configuration.setOrderProviders(new ArrayList<Organization>());
            configuration.setPriceSorter(new PriceTimeSorterC());
            configuration.setSortOrder(10);
            configuration.setQuoteAggregator(aggregator);

            Spread spread = new SpreadC();
            spread.setActive(true);
            spread.setCalculationBasis(CalculationBasis.PIPS);
            spread.setType(SpreadType.BIDOFFER);
            configuration.setSpreadMinimumEnabled(true);
            configuration.setESPSpread(spread);
            double spreadMultiplier = tierPricingMetaData.isUseMultiplierFromService() ? volatilityFactor : tierPricingMetaData.getVolatilitySpreadMultiplier();
            SortedSet<TierData> tierDataSet = tierPricingMetaData.getTiers();
            addTiers(configuration, tierDataSet, tierPricingMetaData.isMinSpreadEnabled(), tierPricingMetaData.isVolatilityEnabled(), spreadMultiplier);

            CurrencyPairGroup cpg = new CurrencyPairGroupC();
            cpg.setFXRateConvention(ReferenceDataCacheC.getInstance().getStandardFXRateConvention());
            configuration.setCurrencyPairGroup(cpg);
            configuration.setSpreadPriority(SpreadPriority.First);
            configuration.setNamespace(organization.getNamespace());

            Stream stream = new StreamC();
            stream.setShortName("MM_STRM_" + cp.replace('/', '_') + "_" + org);
            stream.setObjectID(System.currentTimeMillis());
            stream.setNamespace(organization.getNamespace());
            stream.setBrokerOrganizationFunction(organization.getBrokerOrganizationFunction());
            stream.setUser(organization.getDefaultDealingUser());
            stream.setQuotePublicationCheckInterval(250);
            stream.setQuotePublicationInterval(1000);
            stream.setSkewEnabled(true);
            stream.setVwapSkew(true);
            configuration.setStream(stream);

            log.info("CorePriceProductUtil.createProduct:: created org=" + org + ", cp=" + cp);
            return product;
        }catch (Exception e){
            log.error("CorePriceProductUtil.createProduct:: Exception while creating product", e);
            return null;
        }
    }

    private static void addTiers(Configuration configuration, Set<TierData> tierDataSet, boolean minSpEnabled, boolean volatilityEnabled, double spreadMultipler){
        List<Tier> tiers = new ArrayList<Tier>();
        int sortOrder = 10;
        StringBuilder sb  = new StringBuilder(200);
        if(log.isDebugEnabled()){
            sb.append("CorePriceProductUtil addTiers ").append("voEn:").append(volatilityEnabled).append(" vsm:").append(spreadMultipler);
        }
        for(TierData tierData : tierDataSet){
            Tier tier = ModelFactory.getInstance().newTier();
            tier.setActive(true);
            tier.setSortOrder(sortOrder);
            sortOrder = sortOrder + 10;
            tier.setBidLimit(tierData.getTierAmount());
            tier.setOfferLimit(tierData.getTierAmount());
            double bidSpread = tierData.getBidSpread();
            double offerSpread = tierData.getOfferSpread();
            if(log.isDebugEnabled()){
                sb.append(" Tier:").append(tierData.getTierAmount()).append(" bsp:").append(bidSpread).append(" osp").append(offerSpread);
            }
            if(volatilityEnabled == true && spreadMultipler != 0){
                bidSpread = bidSpread * spreadMultipler;
                offerSpread = offerSpread * spreadMultipler;
                if(log.isDebugEnabled()) {
                    sb.append(" newBSP:").append(bidSpread).append(" newOSP").append(offerSpread);
                }
            }
            tier.setSpreadFixedBid(bidSpread);
            tier.setSpreadFixedOffer(offerSpread);
            if(minSpEnabled) tier.setSpreadMinimum(tierData.getBidOfferSpread());
            tier.setConfiguration(configuration);
            tier.setSpreadBias(SpreadBias.Bid);
            tiers.add(tier);
        }
        configuration.setTiers(tiers);
        if(log.isDebugEnabled()){
            log.debug(sb.toString());
        }
    }

    public static void updateProduct(Product product, TierPricingMetaData tierPricingMetaData, double volatilityFactor){
        double spreadMultiplier = tierPricingMetaData.isUseMultiplierFromService() ? volatilityFactor : tierPricingMetaData.getVolatilitySpreadMultiplier();

        Configuration configuration = product.getConfiguration();
        addTiers(configuration, tierPricingMetaData.getTiers(), tierPricingMetaData.isMinSpreadEnabled(), tierPricingMetaData.isVolatilityEnabled(), spreadMultiplier);
    }
}