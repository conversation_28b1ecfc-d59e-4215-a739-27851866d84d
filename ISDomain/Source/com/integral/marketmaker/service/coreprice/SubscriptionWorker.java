package com.integral.marketmaker.service.coreprice;

import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.persistence.Entity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.*;
import com.integral.util.GUIDFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by verma on 4/20/17.
 */
abstract class SubscriptionWorker implements Runnable {
    protected static Log log = LogFactory.getLog(SubscriptionWorker.class);
    private final Subscription subscription;
    private ScheduledFuture future;
    private AtomicBoolean shutdown = new AtomicBoolean(false);
    private static ConcurrentHashMap<String,User> dynamicUserMap = new ConcurrentHashMap<String, User>();

    /*
        Counter for provision version associated with this SubscriptionWorker.
        On each change to the relevant reference data this counter is incremented by the reference data notification
        processor. If the worker detects a change in the provision counter since last subscription it
        will re-trigger the subscription again.
     */
    protected AtomicInteger provisionCounter;
    protected int lastSubscriptionProvisionCounter;

    public SubscriptionWorker(Subscription subscription) {
        this.subscription = subscription;
    }

    boolean stop() {
        return shutdown.compareAndSet(false, true);
    }

    @Override
    public void run() {
        try {
            if (shutdown.get()) {
                if (stopSubscription()) {
                    future.cancel(false);
                    log.info("SubscriptionWorker.run : Exit for " + subscription);
                }
                return;
            }
            Subscription.State state = subscription.getState();
            if (subscription.getHandlers().size() != 0) {
                if (lastSubscriptionProvisionCounter != provisionCounter.get()) {
                    /*
                        Provision Has Changed. If the subscription is in SUBSCRIBED state Move it to DIRTY state
                     */
                    switch (state) {
                        case SUBSCRIBED:
                            state = Subscription.State.DIRTY;
                            break;
                    }
                }
            }
            switch (state) {
                case CREATED:
                    if (subscription.getHandlers().size() != 0) {
                        boolean success = startSubscription();
                        if (success) {
                            subscription.setState(Subscription.State.SUBSCRIBED);
                        }
                    }
                    break;

                case SUBSCRIBED:
                    if (subscription.getHandlers().size() == 0) {
                        boolean success = stopSubscription();
                        if (success) {
                            subscription.setState(Subscription.State.UNSUBSCRIBED);
                        }
                    }
                    break;

                case UNSUBSCRIBED:
                    if (subscription.getHandlers().size() != 0) {
                        boolean success = startSubscription();
                        if (success) {
                            subscription.setState(Subscription.State.SUBSCRIBED);
                        }
                    }
                    break;

                case DIRTY:
                    boolean success = stopSubscription();
                    if (success) {
                        subscription.setState(Subscription.State.UNSUBSCRIBED);
                        success = startSubscription();
                        if (success) {
                            subscription.setState(Subscription.State.SUBSCRIBED);
                        }
                    }
                    break;
            }
        } catch (Throwable th) {
            log.warn("SubscriptionWorker.run : Failed ", th);
        }
    }

    protected abstract boolean stopSubscription();

    protected abstract boolean startSubscription();

    public void setFuture(ScheduledFuture future) {
        this.future = future;
    }

    public ScheduledFuture getFuture() {
        return future;
    }

    protected Subscription getSubscription() {
        return subscription;
    }

    void setProvisionCounter(AtomicInteger provisionCounter) {
        this.provisionCounter = provisionCounter;
    }

    User getRequestUserForOrganization(Organization organization) {
        if(MarketMakerConfig.getInstance().isDynamicUserForTOBBasedCorePriceService()){
            return getOrCreateDynamicUserForAggregationRequest(organization);
        }
        else {
            String userName = AggregationServiceFactory.getInstance().getAggregationMBean().getAggregationUserShortName();
            User user = null;
            if (userName != null) {
                user = (User) ReferenceDataCacheC.getInstance().getEntityByShortName(userName, User.class, organization.getNamespace(), null);
            }
            if (user == null) {
                user = organization.getDefaultDealingUser();
            }
            return user;
        }
    }

    private User getOrCreateDynamicUserForAggregationRequest(Organization organization) {
        String userId = getRequestUserNamePrefix() + this.hashCode();
        String userFullName = userId + '@' + organization.getShortName();
        User user = UserFactory.getUser( userFullName );

        //Create a dummy runtime user and add to reference data cache
        if ( user == null )
        {
            user = new UserC();
            user.setUserClassification( UserUtilC.DYNAMIC_USER_CLSF );
            user.setShortName( userId );
            user.setOrganization( organization );
            user.setNamespace( organization.getNamespace() );
            user.setStatus( Entity.ACTIVE_STATUS );
            user.setGUID( GUIDFactory.nextGUID() );
            user.setLongName( userId );    //This is not fully qualified name but the long user name
            user.setDescription( userId );
            long userObjectID = UserUtilC.getInstance().nextDynamicUserObjectID();
            user.setObjectID( userObjectID );
            //Associating User to org's default LE
            user.setDefaultDealingEntity( organization.getDefaultDealingEntity() );
            User cachedUser = dynamicUserMap.putIfAbsent(userFullName,user);
            if( cachedUser != null ){
                /**
                 * Calling load again if there are multiple threads getting into user creation
                 * to ensure user is loaded into reference data cache before returning.
                 */
                ReferenceDataCacheC.getInstance().loadReferenceDataEntity(cachedUser);
                return cachedUser;
            }
            else {
                ReferenceDataCacheC.getInstance().loadReferenceDataEntity(user);
                return user;
            }
        }
        return user;
    }

    protected abstract String getRequestUserNamePrefix();
}
