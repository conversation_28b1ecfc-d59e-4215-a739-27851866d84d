package com.integral.marketmaker.service.coreprice;

import com.integral.finance.currency.CurrencyPair;
import com.integral.marketmaker.config.CorePriceConfig;
import com.integral.marketmaker.config.MarketMakerConfigC;
import com.integral.user.Organization;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 8/4/16
 */
public class CorePriceServiceFactory {
    protected static CorePriceServiceFactory instance = new CorePriceServiceFactory();
    protected final ConcurrentHashMap<String, CorePriceService> corePriceServiceMap = new ConcurrentHashMap<String, CorePriceService>();
    protected final ConcurrentHashMap<String, CorePricePublisherC> publisherMap = new ConcurrentHashMap<String, CorePricePublisherC>();
    protected final ConcurrentHashMap<String, CorePriceSubscriberC> subscriberMap = new ConcurrentHashMap<String, CorePriceSubscriberC>();
    private final ConcurrentHashMap<String, ConcurrentHashMap<String, CorePriceProviderQuoteCacheC>> providerQuoteCacheMapMap = new ConcurrentHashMap<String, ConcurrentHashMap<String, CorePriceProviderQuoteCacheC>>();
    public enum CorePriceServiceType {MM, YM}

    public static CorePriceServiceFactory getInstance(){
        return instance;
    }

    public CorePriceService getCorePriceService(Organization org, CorePriceServiceType type){
        String shortName = org.getShortName();
        CorePriceService corePriceService = corePriceServiceMap.get(shortName);
        if(corePriceService == null){
            CorePriceConfig config = null;
            if(type.equals(CorePriceServiceType.MM)) config = createMarketMakerConfig(org);
            corePriceService = new MMCorePriceServiceC(org, config);
            corePriceServiceMap.putIfAbsent(shortName, corePriceService);
            corePriceService = corePriceServiceMap.get(shortName);
        }
        return corePriceService;
    }

    public CorePricePublisherC getPublisher(Organization org, CurrencyPair currencyPair, CorePriceConfig config){
        String key = getKey(org.getShortName(), currencyPair.getName());
        CorePricePublisherC publisher = publisherMap.get(key);
        if(publisher == null){
            publisher = new CorePricePublisherC(org, currencyPair, config);
            publisherMap.putIfAbsent(key, publisher);
            publisher = publisherMap.get(key);
        }
        return publisher;
    }

    public CorePriceSubscriberC getSubscriber(Organization org, CurrencyPair currencyPair, CorePriceConfig config){
        String key = getKey(org.getShortName(), currencyPair.getName());
        CorePriceSubscriberC subscriber = subscriberMap.get(key);
        if(subscriber == null){
            subscriber = new CorePriceSubscriberC(org, currencyPair, config);
            subscriberMap.putIfAbsent(key, subscriber);
            subscriber = subscriberMap.get(key);
        }
        return subscriber;
    }

    public CorePriceSubscriberC findSubscriber(String orgName, String ccyPair){
        return subscriberMap.get(getKey(orgName, ccyPair));
    }

    public CorePriceProviderQuoteCacheC getProviderQuoteCache(Organization org, CurrencyPair cp, Set<String> providers){
        String orgName = org.getShortName();
        String cpName = cp.getName();
        ConcurrentHashMap<String, CorePriceProviderQuoteCacheC> cacheMap = providerQuoteCacheMapMap.get(orgName);
        if(cacheMap == null){
            providerQuoteCacheMapMap.putIfAbsent(orgName, new ConcurrentHashMap<String, CorePriceProviderQuoteCacheC>());
            cacheMap = providerQuoteCacheMapMap.get(orgName);
        }
        CorePriceProviderQuoteCacheC quoteCache = cacheMap.get(cpName);
        if(quoteCache == null){
            cacheMap.putIfAbsent(cpName, new CorePriceProviderQuoteCacheC(org, cp, providers));
            quoteCache = cacheMap.get(cpName);
        }
        return quoteCache;
    }

    public CorePriceProviderQuoteCacheC findProviderQuoteCache(String orgName, String ccyPair){
        ConcurrentHashMap<String, CorePriceProviderQuoteCacheC> cacheMap = providerQuoteCacheMapMap.get(orgName);
        if(cacheMap == null) return null;
        return cacheMap.get(ccyPair);
    }

    public List<CorePriceProviderQuoteCacheC> getAllProviderQuoteCaches(String orgName){
        List<CorePriceProviderQuoteCacheC> quoteCacheList = new ArrayList<CorePriceProviderQuoteCacheC>();
        ConcurrentHashMap<String, CorePriceProviderQuoteCacheC> quoteCacheMap = providerQuoteCacheMapMap.get(orgName);
        if(quoteCacheMap != null) {
            quoteCacheList.addAll(quoteCacheMap.values());
        }
        return quoteCacheList;
    }

    protected CorePriceConfig createMarketMakerConfig(Organization org){
        return new MarketMakerConfigC(org);
    }

    public CorePricePublisherC findPublisher(String orgName, String ccyPair){
        return publisherMap.get(getKey(orgName, ccyPair));
    }

    public List<CorePricePublisherC> getCorePricePublishers(String orgName){
        List<CorePricePublisherC> publishers = new ArrayList<CorePricePublisherC>();
        String keyPrefix = getKey(orgName, "");
        for(Map.Entry<String, CorePricePublisherC> entry : publisherMap.entrySet()){
            if(entry.getKey().startsWith(keyPrefix)){
                publishers.add(entry.getValue());
            }
        }
        return publishers;
    }

    public List<CorePriceSubscriberC> getSubscribers(String orgName){
        List<CorePriceSubscriberC> subscribers = new ArrayList<CorePriceSubscriberC>();
        String keyPrefix = getKey(orgName, "");
        for(Map.Entry<String, CorePriceSubscriberC> entry : subscriberMap.entrySet()){
            if(entry.getKey().startsWith(keyPrefix)){
                subscribers.add(entry.getValue());
            }
        }
        return subscribers;
    }

    public List<CorePricePublisherC> getPublishers(){
        Collection<CorePricePublisherC> publishers = publisherMap.values();
        return new ArrayList<CorePricePublisherC>(publishers);
    }

    public CorePricePublisherC getPublisher(Organization org, CurrencyPair currencyPair){
        String key = getKey(org.getShortName(), currencyPair.getName());
        CorePricePublisherC publisher = publisherMap.get(key);
        return publisher;
    }


    private String getKey(String orgName, String ccyPair){
        return orgName + '~' + ccyPair;
    }
}
