package com.integral.marketmaker.service.coreprice;

import com.integral.util.MathUtilC;

/**
 * Created by verma on 3/7/16.
 */
public class DefaultSkewCalculatorC implements SkewCalculator {

    public DefaultSkewCalculatorC() {
    }

    @Override
    public double calculateEffectiveSkew(double provisionedSkew, double bidRate, double offerRate, MaxSkewRestriction mode) {
        switch (mode) {
            case DO_NOT_CROSS_MID: {
                double bidOfferSpread = offerRate - bidRate;
                if (bidOfferSpread > 0) {
                    double maxSkew = MathUtilC.correctFloatingPointsCalculationPrecision(bidOfferSpread / 2.0D);
                    return calculateMaxAllowedSkew(provisionedSkew, maxSkew);
                }
            }
            break;

            case DO_NOT_CROSS_OTHER_SIDE: {
                double bidOfferSpread = MathUtilC.correctFloatingPointsCalculationPrecision(offerRate - bidRate);
                if (bidOfferSpread > 0.0D) {
                    return calculateMaxAllowedSkew(provisionedSkew, bidOfferSpread);
                }
            }
            break;

            case NONE:
                return provisionedSkew;
        }
        return 0.0D;
    }

    /**
     * @param provisionedSkew Provisioned Skew. It can be positive, negative or zero.
     * @param maxSkewAbs      Max allowed absolute value of skew.
     * @return
     */
    private double calculateMaxAllowedSkew(double provisionedSkew, double maxSkewAbs) {
        if (Math.abs(provisionedSkew) > maxSkewAbs) {
            /*
                maxSkew is always positive or 0.0D
             */
            if (provisionedSkew < 0.0D) {
                return MathUtilC.correctFloatingPointsCalculationPrecision(maxSkewAbs * -1.0D);
            }
            return maxSkewAbs;
        }
        return provisionedSkew;
    }
}
