package com.integral.marketmaker.service.coreprice;

import com.integral.is.common.maintenance.MaintenanceAgent;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.util.CompositeKeys;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 *
 */
public abstract class CorePriceServiceC<S extends Subscription> implements CorePriceService {
    private static final Log log = LogFactory.getLog(CorePriceServiceC.class);
    private final ConcurrentHashMap<CompositeKeys, S> subscriptionsMap = new ConcurrentHashMap<CompositeKeys, S>();
    private final ConcurrentHashMap<S,SubscriptionWorker> subscriptionWorkerMap = new ConcurrentHashMap<S, SubscriptionWorker>();
    private AtomicBoolean initialized = new AtomicBoolean(false);
    private AtomicBoolean started = new AtomicBoolean(false);

    CorePriceServiceC() {

    }

    /**
     * Initialize the service.
     *
     * @return true if successfully initialized.
     */
    @Override
    public boolean init() {
        return initialized.compareAndSet(false,true);
    }

    /**
     * Start the service.
     *
     * @return true if the start is successful.
     */
    @Override
    public boolean start() {
        return started.compareAndSet(false,true);
    }

    /**
     * Stop the service.
     *
     * @return true if the stop is successful.
     */
    @Override
    public boolean stop() {
        boolean success = false;
        if( started.compareAndSet(true,false)){
            try{
                for( SubscriptionWorker worker : subscriptionWorkerMap.values() ){
                    worker.stop();
                    MaintenanceAgent.getSingleThreadedExecutorService().execute(worker);
                }
                subscriptionWorkerMap.clear();
                subscriptionsMap.clear();
                success = true;
            }
            finally {
                if( !success ){
                    started.set(true);
                }
                else{
                    initialized.set(false);
                }
            }
        }
        else{
            log.warn("stop : Service not started.");
        }
        return success;
    }

    @Override
    public SubscribeResponse<S> subscribe(SubscribeRequest request) {
        SubscribeResponseC<S> subscribeResponse = new SubscribeResponseC<S>(request);
        if( !started.get() ){
            log.warn("subscribe : Service not started. Request not processed. request="+request);
            subscribeResponse.status = SubscribeResponse.Status.ERROR;
            return subscribeResponse;
        }
        try {
            S subscription = getOrCreateSubscription(request);
            subscription.addHandler(request.getHandler());
            SubscriptionWorker subscriptionWorker = subscriptionWorkerMap.get(subscription);
            if( subscriptionWorker == null ) {
                SubscriptionWorker newWorker = createSubscriptionWorker(subscription);
                SubscriptionWorker cachedSubscriptionWorker = subscriptionWorkerMap.putIfAbsent(subscription, newWorker);
                if (cachedSubscriptionWorker == null) {
                    subscriptionDone(subscription);
                    subscriptionWorkerCreated(newWorker);
                    /*
                        Schedule the worker for the subscription
                    */
                    ScheduledFuture future = MaintenanceAgent.getSingleThreadedExecutorService().scheduleAtFixedRate(newWorker, 0L, 60, TimeUnit.SECONDS);
                    newWorker.setFuture(future);
                }
            }
            subscribeResponse.status = SubscribeResponse.Status.OK;
            subscribeResponse.setSubscription(subscription);
            return subscribeResponse;
        } catch (Exception ex) {
            log.error("subscribe : Exception in processing request=" + request, ex);
        }
        subscribeResponse.status = SubscribeResponse.Status.ERROR;
        return subscribeResponse;
    }

    protected abstract SubscriptionWorker createSubscriptionWorker(S subscription);

    protected abstract void subscriptionWorkerCreated(SubscriptionWorker newWorker);

    /**
     * CallBack when a new subscription is done.
     * @param subscription
     */
    protected abstract void subscriptionDone(S subscription);

    protected S getOrCreateSubscription(SubscribeRequest request) {
        S subscription;
        CompositeKeys key = CompositeKeys.getCompositeKeys(request.getOrganization().getShortName(), request.getCurrencyPair().getName());
        subscription = subscriptionsMap.get(key);
        if (subscription == null) {
            subscription = createSubscription(request);
            S cachedSubscription = subscriptionsMap.putIfAbsent(key, subscription);
            if (cachedSubscription != null) {
                subscription = cachedSubscription;
            }
        }
        return subscription;
    }

    protected abstract S createSubscription(SubscribeRequest request);

    private static class SubscribeResponseC<E extends Subscription> implements SubscribeResponse {
        private SubscribeRequest request;
        private Status status;
        private E subscription;

        public SubscribeResponseC(SubscribeRequest request) {
            this.request = request;
        }

        @Override
        public SubscribeRequest getRequest() {
            return request;
        }

        @Override
        public Status getStatus() {
            return status;
        }

        public E getSubscription() {
            return subscription;
        }

        public void setSubscription(E subscription) {
            this.subscription = subscription;
        }
    }

    public Map<CompositeKeys, S> getSubscriptionsMap() {
        return Collections.unmodifiableMap(subscriptionsMap);
    }

    public Map<S, SubscriptionWorker> getSubscriptionWorkerMap() {
        return Collections.unmodifiableMap(subscriptionWorkerMap);
    }
}
