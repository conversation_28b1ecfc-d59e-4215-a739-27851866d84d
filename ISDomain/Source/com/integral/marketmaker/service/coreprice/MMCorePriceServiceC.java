package com.integral.marketmaker.service.coreprice;

import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.config.CorePriceConfig;
import com.integral.user.Organization;

import java.util.Set;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 8/4/16
 */
public class MMCorePriceServiceC implements CorePriceService {
    private static final Log log = LogFactory.getLog(MMCorePriceServiceC.class);
    private final Organization organization;
    private final CorePriceConfig config;
    public MMCorePriceServiceC(Organization org, CorePriceConfig config){
        this.organization = org;
        this.config = config;
    }

    @Override
    public boolean init() {
        return false;
    }

    public synchronized boolean start(){
        Set<String> currencyPairs = config.getCurrencyPairs();
        for(String ccyPair : currencyPairs){
            CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(ccyPair);
            if(currencyPair == null) {
                log.info("MMCorePriceService.start:: currency pair not present " + ccyPair);
                continue;
            }
            startSubscriber(organization, currencyPair);
            startPublisher(organization, currencyPair);
            log.info("MMCorePriceServiceC.start:: started for org=" + organization.getShortName() + ", cp=" + ccyPair);
        }
        return true;
    }

    @Override
    public synchronized boolean stop() {
        Set<String> currencyPairs = config.getCurrencyPairs();
        for(String ccyPair : currencyPairs){
            stopPublisher(organization.getShortName(), ccyPair);
            stopSubscriber(organization.getShortName(), ccyPair);
            log.info("MMCorePriceServiceC.stop:: stopped mm service for org=" + organization.getShortName() + ", cp=" + ccyPair);
        }
        return true;
    }

    private void stopSubscriber(String orgName, String ccyPair){
        CorePriceSubscriberC subscriber = CorePriceServiceFactory.getInstance().findSubscriber(orgName, ccyPair);
        if(subscriber != null){
            subscriber.stop();
            log.info("MMCorePriceServiceC.stopSubscriber:: stopped " + orgName + " " + ccyPair);
        }else {
            log.info("MMCorePriceServiceC.stopSubscriber:: not exist " + orgName + " " + ccyPair);
        }
    }

    private void stopPublisher(String brokerOrg, String currencyPair){
        CorePricePublisherC publisher = CorePriceServiceFactory.getInstance().findPublisher(brokerOrg, currencyPair);
        if(publisher != null) {
            publisher.stop();
            log.info("MMCorePriceServiceC.stopPublisher:: stopped " + brokerOrg + " " + currencyPair);
        }else {
            log.info("MMCorePriceServiceC.stopPublisher:: not exist " + brokerOrg + " " + currencyPair);
        }
    }

    public synchronized void startSubscriber(Organization brokerOrg, CurrencyPair currencyPair){
        CorePriceSubscriberC subscriber = CorePriceServiceFactory.getInstance().getSubscriber(brokerOrg, currencyPair, config);
        subscriber.start();
    }

    public synchronized void startPublisher(Organization brokerOrg, CurrencyPair currencyPair){
        CorePricePublisherC publisher = CorePriceServiceFactory.getInstance().getPublisher(brokerOrg, currencyPair, config);
        publisher.start();
    }

    @Override
    public SubscribeResponse subscribe(SubscribeRequest request) {
        return null;
    }
}
