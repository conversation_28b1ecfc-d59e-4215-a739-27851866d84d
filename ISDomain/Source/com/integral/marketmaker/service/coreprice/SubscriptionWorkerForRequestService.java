package com.integral.marketmaker.service.coreprice;

import com.integral.aggregation.AggregationMethod;
import com.integral.finance.dealing.DealingFactory;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.trade.Tenor;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.spaces.fx.service.ServiceFactory;
import com.integral.message.*;
import com.integral.persistence.ExternalSystemC;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by verma on 4/20/17.
 */
public class SubscriptionWorkerForRequestService extends SubscriptionWorker {
    private Request currentSubscriptionRequest;
    private static AtomicInteger aggregationRequestCounter = new AtomicInteger(0);
    private final TOBBasedCorePriceServiceSubscription subscription;

    public SubscriptionWorkerForRequestService(TOBBasedCorePriceServiceSubscription subscription) {
        super(subscription);
        this.subscription = subscription;
    }

    protected boolean stopSubscription() {
        if (currentSubscriptionRequest != null) {
            try {
                ISUtilImpl.getInstance().setSessionContext( currentSubscriptionRequest.getUser() );
                WorkflowMessage wfMsg = createUnSubscriptionWorkflowMessage(currentSubscriptionRequest);
                WorkflowMessage response = ServiceFactory.getFxisRequestService().process(wfMsg);
                MessageStatus respStatus = null;
                if (response != null) {
                    Message message = response.getReplyMessage();
                    if (message != null) {
                        respStatus = message.getStatus();
                    }
                }
                if (respStatus != null && respStatus.equals(MessageStatus.SUCCESS)) {
                    currentSubscriptionRequest = null;
                    log.info("stopSubscription : Success for " + subscription);
                    return true;
                } else {
                    log.info("stopSubscription : Failed for " + subscription);
                    return false;
                }
            } catch (Exception ex) {
                log.error("stopSubscription : Exception for Request=" + currentSubscriptionRequest, ex);
            }
            return false;
        }
        return true;
    }

    protected boolean startSubscription() {
        try {
            int currentProvisionCounter = provisionCounter.get();
            Request requestMessage = getSubscriptionRequest();
            if (requestMessage == null) {
                return false;
            }
            ISUtilImpl.getInstance().setSessionContext( requestMessage.getUser() );
            WorkflowMessage wfMsg = createSubscriptionWorkflowMessage(requestMessage);
            WorkflowMessage response = ServiceFactory.getFxisRequestService().process(wfMsg);
            MessageStatus respStatus = null;
            if (response != null) {
                Message message = response.getReplyMessage();
                if (message != null) {
                    respStatus = message.getStatus();
                }
            }
            if (respStatus != null && respStatus.equals(MessageStatus.SUCCESS)) {
                currentSubscriptionRequest = requestMessage;
                lastSubscriptionProvisionCounter = currentProvisionCounter;
                log.info("startSubscription : Success for " + subscription);
                return true;
            } else {
                log.info("startSubscription : Failed for " + subscription);
                currentSubscriptionRequest = null;
                return false;
            }
        } catch (Exception ex) {
            log.error("startSubscription : Exception for Request=" + currentSubscriptionRequest, ex);
        }
        return false;
    }

    private WorkflowMessage createSubscriptionWorkflowMessage(Request request) {
        WorkflowMessage wfMsg = MessageFactory.newWorkflowMessage();
        wfMsg.setMessageId(0);
        wfMsg.setSender(request.getUser());
        wfMsg.setEvent(MessageEvent.CREATE);
        wfMsg.setTopic(ISCommonConstants.MSG_TOPIC_REQUEST);
        wfMsg.setParameterValue(ISCommonConstants.MSG_REC_AT_WORKFLOW_TS, String.valueOf(System.currentTimeMillis()));
        wfMsg.setParameterValue(ISCommonConstants.EXECUTION_TYPE, "S");
        PriceHandler handler = createPriceHandler();
        wfMsg.setParameterValue(ISCommonConstants.MESSAGEHANDLER, handler);
        wfMsg.setObject(request);
        return wfMsg;
    }

    private WorkflowMessage createUnSubscriptionWorkflowMessage(Request request) {
        WorkflowMessage wfMsg = MessageFactory.newWorkflowMessage();
        wfMsg.setMessageId(0);
        wfMsg.setSender(request.getUser());
        wfMsg.setEvent(MessageEvent.WITHDRAW);
        wfMsg.setTopic(ISCommonConstants.MSG_TOPIC_REQUEST);
        wfMsg.setParameterValue(ISCommonConstants.MSG_REC_AT_WORKFLOW_TS, String.valueOf(System.currentTimeMillis()));
        wfMsg.setParameterValue(ISCommonConstants.EXECUTION_TYPE, "S");
        wfMsg.setObject(request);
        return wfMsg;
    }

    @Override
    protected String getRequestUserNamePrefix() {
        return "YM_MM_CP_AggUser_";
    }

    public Request getSubscriptionRequest() {
        try {
            List<Organization> providers = getProvidersList();
            if (providers == null || providers.isEmpty()) {
                return null;
            }
            // Create Request
            Request request = DealingFactory.newRequest();
            User user = getRequestUserForOrganization(subscription.getOrganization());
            request.setUser(user);
            request.setRequestClassification(ISUtilImpl.getInstance().getRequestClassification(ISCommonConstants.QTQ_CREATE_TYPE));
            request.setChannel(new ExternalSystemC("YM/MM/CP"));
            request.setCounterparty(subscription.getOrganization().getDefaultDealingEntity());

            // Create FXLegDealingPrice
            FXLegDealingPrice fxLegDealingPrice = FXDealingFactory.newFXLegDealingPrice();
            fxLegDealingPrice.setBidOfferMode(DealingPrice.TWO_WAY);
            fxLegDealingPrice.setTenor(Tenor.SPOT_TENOR);
            fxLegDealingPrice.setDealtCurrency(subscription.getCurrencyPair().getBaseCurrency());
            fxLegDealingPrice.setSettledCurrency(subscription.getCurrencyPair().getVariableCurrency());
            fxLegDealingPrice.setDealtAmount(100000000);//set to any value this is ignored for QT subscription
            fxLegDealingPrice.setDealtCurrencyProperty(FXLegDealingPrice.CCY1);
            request.setRequestPrice(ISCommonConstants.SINGLE_LEG, fxLegDealingPrice);

            String requestId = "YM_MM_CP_REQ_" + aggregationRequestCounter.incrementAndGet();
            request.setExternalRequestId(requestId);
            request.setOrganization(subscription.getOrganization());
            request.setIsAggregationRequest(true);
            request.setToOrganizations(providers);
            request.getRequestAttributes().setIsSingleLPFromClient(false);
            request.setAggregationMethod(AggregationMethod.FULL_BOOK);
            if (providers.size() > 1) {
                request.setDropFXIDirectStreamPrices(true);
            }
            return request;
        } catch (Exception e) {
            log.error("getSubscriptionRequest : Exception while creating subscription request ", e);
        }
        return null;
    }

    private List<Organization> getProvidersList() {
        Collection<Organization> providers = ISUtilImpl.getInstance().getAggregationProvidersFromLiquidityRule(subscription.getOrganization(), subscription.getCurrencyPair());
        if (providers != null && !providers.isEmpty()) {
            List<Organization> pList = new ArrayList<Organization>(providers.size());
            for (Organization org : providers) {
                if (org.isSameAs(subscription.getOrganization())) {
                    continue;
                }
                if (org.isBroker() || org.isExternalProvider()) {
                    pList.add(org);
                }
            }
            return pList;
        }
        return null;
    }

    private PriceHandler createPriceHandler() {
        return new PriceHandler(subscription);
    }
}
