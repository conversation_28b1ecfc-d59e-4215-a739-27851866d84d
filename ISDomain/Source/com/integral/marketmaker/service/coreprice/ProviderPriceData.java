package com.integral.marketmaker.service.coreprice;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 8/1/16
 */
public class ProviderPriceData {
    private String provider;
    private double bidPrice;
    private double offerPrice;

    public ProviderPriceData(){}

    public ProviderPriceData(String name, double bid, double offer){
        this.provider = name;
        this.bidPrice = bid;
        this.offerPrice = offer;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public double getBidPrice() {
        return bidPrice;
    }

    public void setBidPrice(double bidPrice) {
        this.bidPrice = bidPrice;
    }

    public double getOfferPrice() {
        return offerPrice;
    }

    public void setOfferPrice(double offerPrice) {
        this.offerPrice = offerPrice;
    }

    @Override
    public String toString(){
        StringBuilder sb = new StringBuilder("ProviderPriceData [");
        sb.append("name=").append(provider).append(", bid=").append(bidPrice).append(", offer=").append(offerPrice).append("]");
        return sb.toString();
    }
}
