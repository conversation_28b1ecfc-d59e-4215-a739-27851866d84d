package com.integral.marketmaker.service.coreprice;

import com.integral.marketmaker.service.MMService;

/**
 * Service for providing parameters for market making.
 * This service provides :
 *      Core Bid Rate
 *      Core Offer Rate
 *      Skew
 */
public interface CorePriceService<T extends Subscription> extends MMService{
    /*
        Subscribe for market making parameters.
     */
    public SubscribeResponse<T> subscribe(SubscribeRequest request);

    public interface Handler {
        /*
            Callback when core price is updated.
         */
        void corePriceDataUpdated();
    }
}
