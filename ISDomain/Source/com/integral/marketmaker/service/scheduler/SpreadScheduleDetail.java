package com.integral.marketmaker.service.scheduler;

import com.integral.time.IdcDateTime;

public class SpreadScheduleDetail {

    public SpreadScheduleDetail() {
    }

    private double multiplier;
    private boolean  enableMultiplier;
    private String ccyPairName;
    private String brokerOrgName;
    private IdcDateTime currentScheduleTime;
    private  boolean isActive;

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public IdcDateTime getCurrentScheduleTime() {
        return currentScheduleTime;
    }

    public void setCurrentScheduleTime(IdcDateTime currentScheduleTime) {
        this.currentScheduleTime = currentScheduleTime;
    }

    public String getCcyPairName() {
        return ccyPairName;
    }

    public void setCcyPairName(String ccyPairName) {
        this.ccyPairName = ccyPairName;
    }

    public String getBrokerOrgName() {
        return brokerOrgName;
    }


    public void setBrokerOrgName(String brokerOrgName) {
        this.brokerOrgName = brokerOrgName;
    }

    public double getMultiplier() {
        return multiplier;
    }

    public void setMultiplier(double multiplier) {
        this.multiplier = multiplier;
    }

    public boolean isEnableMultiplier() {
        return enableMultiplier;
    }

    public void setEnableMultiplier(boolean enableMultiplier) {
        this.enableMultiplier = enableMultiplier;
    }

    @Override
    public String toString() {
        return "SpreadScheduleDetail{" +
                "multiplier=" + multiplier +
                ", enableMultiplier=" + enableMultiplier +
                ", ccy pair='" + ccyPairName + '\'' +
                ", broker='" + brokerOrgName + '\'' +
                ", currentScheduleTime=" + currentScheduleTime +
                '}';
    }
}
