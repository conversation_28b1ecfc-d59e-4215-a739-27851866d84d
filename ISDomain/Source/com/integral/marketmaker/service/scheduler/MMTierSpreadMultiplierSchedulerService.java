package com.integral.marketmaker.service.scheduler;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.maker.service.broker.CorePriceBrokerDataService;
import com.integral.maker.service.tierpricing.SpreadMultiplierSchedulerDataService;
import com.integral.maker.service.tierpricing.TierPricingService;
import com.integral.marketmaker.broker.BrokerMetaData;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.tierpricing.SchedulerMetaData;
import com.integral.marketmaker.tierpricing.SpreadMultiplierSchedulerMetaData;
import com.integral.marketmaker.tierpricing.TierPricingMetaData;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;
import com.integral.system.timer.Timer;
import com.integral.system.timer.TimerFactory;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateTime;
import com.integral.util.StringUtilC;

import java.sql.Time;
import java.util.HashMap;
import java.util.TimeZone;
import java.util.List;
import java.util.Set;
import java.util.Collection;
import java.util.Observer;
import java.util.Observable;
import java.util.concurrent.ConcurrentHashMap;

public class MMTierSpreadMultiplierSchedulerService {

    private static final Log log = LogFactory.getLog(MMTierSpreadMultiplierSchedulerService.class);
    private static MMTierSpreadMultiplierSchedulerService instance = new MMTierSpreadMultiplierSchedulerService();
    private ConcurrentHashMap<String, SpreadScheduleDetail> scheduledSchedules = new ConcurrentHashMap<String, SpreadScheduleDetail>();
    private Timer timer = TimerFactory.getTimer();
    private final TimeZone gmtTimeZone  = TimeZone.getTimeZone( "GMT" );
    private HashMap<Integer, IdcDate> dayofWeekToDateMap = new HashMap<Integer, IdcDate>();
    private IdcDate cacheCurrentDate ;

    private CorePriceBrokerDataService corePriceBrokerDataService;
    private SpreadMultiplierSchedulerDataService spreadMultiplierSchedulerDataService;
    private TierPricingService tierPricingService;

    public MMTierSpreadMultiplierSchedulerService(){

    }
    public void initialize(String brokerOrgName)
    {
        log.info("MMTSMSS.initialize: "+ brokerOrgName);
        registerSchedulerConfigurationChange(brokerOrgName);
        processPendingSchedule(brokerOrgName);
        loadSchedulerConfiguration(brokerOrgName);
    }

    public SpreadScheduleDetail getSpreadScheduleDetails(String brokerOrgName, String ccyPairName) {
        String key = brokerOrgName + "@" + ccyPairName;
        return scheduledSchedules.get(key);
    }

    public void process(String brokerOrgName, String ccyPairName)
    {
        if( StringUtilC.isNullOrEmpty(brokerOrgName)){
            log.info("MMTSMSS.process broker org name is null or empty");
            return;
        }
        if( StringUtilC.isNullOrEmpty(ccyPairName)){
            log.info("MMTSMSS.process ccy pair is null or empty");
            return;
        }

        log.info("MMTSMSS.process "+ brokerOrgName +"@"+ ccyPairName);
        SpreadMultiplierSchedulerMetaData schedulerMetaData =  this.getSpreadMultiplierSchedulerDataService().getSpreadMultiplierSchedulerMetaData(brokerOrgName, ccyPairName);
        if(schedulerMetaData != null){
            process(schedulerMetaData);
        }
        else
        {
            log.info("MMTSMSS.process: scheduler configuration not found for "+ brokerOrgName +"@"+ ccyPairName);
        }
    }

    private void registerSchedulerConfigurationChange(String brokerOrgName)
    {
        MMTierSpreadMultiplierSchedulerService.SpreadSchedulerNotificationHandler tierSpreadSchedulerMetaDataChangeHandler = new MMTierSpreadMultiplierSchedulerService.SpreadSchedulerNotificationHandler(getSpreadMultiplierSchedulerDataService(), this);
        log.info("MMTSMSS.registerSchedulerConfigurationChange: registering for spread scheduler configuration change for broker "+ brokerOrgName);
        this.getSpreadMultiplierSchedulerDataService().registerForMetaDataChange(brokerOrgName, tierSpreadSchedulerMetaDataChangeHandler);
    }


    private void loadSchedulerConfiguration(String brokerOrgName)
    {
        log.info("MMTSMSS.loadSchedulerConfiguration: Load ccypairs for broker "+ brokerOrgName);
        BrokerMetaData brokerMetaData = this.getCorePriceBrokerDataService().getBrokerMetaData(brokerOrgName);
        if(brokerMetaData != null) {
            Set<String> ccyPairs = brokerMetaData.getCurrencyPairs();
            for (String ccyPairName : ccyPairs) {
                process(brokerOrgName, ccyPairName);
            }
        }
    }

    public SpreadMultiplierSchedulerDataService getSpreadMultiplierSchedulerDataService() {
        if (spreadMultiplierSchedulerDataService == null) {
            spreadMultiplierSchedulerDataService = MarketMakerFactory.getInstance().getSpreadMultiplierSchedulerService().getSpreadMultiplierSchedulerDataService();
        }
        return spreadMultiplierSchedulerDataService;
    }

    public void setSpreadMultiplierSchedulerDataService(SpreadMultiplierSchedulerDataService spreadMultiplierSchedulerDataService){
        this.spreadMultiplierSchedulerDataService = spreadMultiplierSchedulerDataService;
    }

    public CorePriceBrokerDataService getCorePriceBrokerDataService()
    {
       if(this.corePriceBrokerDataService == null) {
           return MarketMakerFactory.getInstance().getCorePriceBrokerService().getBrokerDataService();
       }
       return corePriceBrokerDataService;
    }

    public void setCorePriceBrokerDataService(CorePriceBrokerDataService corePriceBrokerDataService)
    {
        this.corePriceBrokerDataService = corePriceBrokerDataService;
    }

    public TierPricingService getTierPricingService()
    {
        if(tierPricingService == null){
            tierPricingService = MarketMakerFactory.getInstance().getTierPricingService();
        }
        return tierPricingService;
    }

    public void setTierPricingService(TierPricingService tierPricingService)
    {
        this.tierPricingService = tierPricingService;
    }

    private synchronized void process(SpreadMultiplierSchedulerMetaData schedulerMetaData)
    {
        if(schedulerMetaData == null)
            return;
        //clear if anything is planned.
        String key = schedulerMetaData.getBrokerOrgName() + "@"+ schedulerMetaData.getCcyPair();
        log.info("MMTSMSS.process: processing tier spread multiplier scheduler metadata for "+ key);
        
        if(scheduledSchedules.containsKey(key)){
            log.info("MMTSMSS.process: removing existing planned schedule for "+ key);
            SpreadScheduleDetail scheduleDetail = scheduledSchedules.get(key);
            timer.removeRequest(null, scheduleDetail);
            scheduledSchedules.remove(key);
        }
        updateDayofTheWeekToDates();

        if(schedulerMetaData.isSchedulerActive() == false)
        {
            log.info("MMTSMSS.process: Scheduler is turned off for "+ key);
            return;
        }
        List<SchedulerMetaData> activeSchedules = schedulerMetaData.getActiveSchedules();
        if(activeSchedules == null || activeSchedules.isEmpty() )
        {
            log.info("MMTSMSS.process: No schedules configured for "+ key);
            return;
        }

        SpreadScheduleDetail spreadScheduleDetail = getNextActiveScheduleDetails(schedulerMetaData);
        if(!spreadScheduleDetail.isActive()){
            log.info("MMTSMSS.process: No active schedule found for "+ key);
            return;
        }
        log.info("MMTSMSS.process: adding schedule to timer. "+ spreadScheduleDetail.toString());
        timer.addRequest(new TierSpreadMultiplierSchedulerTimerHandler(getTierPricingService(), this), spreadScheduleDetail.getCurrentScheduleTime(), spreadScheduleDetail);
        scheduledSchedules.put(key, spreadScheduleDetail);
    }

    private SpreadScheduleDetail getNextActiveScheduleDetails(SpreadMultiplierSchedulerMetaData schedulerMetaData ){

        IdcDateTime currentDateTime = DateTimeFactory.newDateTime();
        StringBuilder sb = new StringBuilder(500);

        if(log.isDebugEnabled())
        {
            sb.append("MMTSMSS.getNextActiveScheduleDetails: currentDateTime:"+ currentDateTime);
        }
        else
        {
            log.info("MMTSMSS.getNextActiveScheduleDetails: currentDateTime:"+ currentDateTime  );
        }
        SpreadScheduleDetail spreadScheduleDetail =new SpreadScheduleDetail();
        spreadScheduleDetail.setBrokerOrgName(schedulerMetaData.getBrokerOrgName());
        spreadScheduleDetail.setCcyPairName(schedulerMetaData.getCcyPair());
        Collection<SchedulerMetaData> schedulerMetaDataCollection = schedulerMetaData.getActiveSchedules();
        for(SchedulerMetaData schedulerMetaData1 : schedulerMetaDataCollection) {
            if (schedulerMetaData1.isScheduleActive() == true) {
                IdcDate startDate = dayofWeekToDateMap.get(schedulerMetaData1.getStartDayOfTheWeek());
                IdcDateTime startTime = DateTimeFactory.newDateTime(startDate, new Time(schedulerMetaData1.getStartHour(), schedulerMetaData1.getStartMinute(), 0), gmtTimeZone);


                if( startTime.isLaterThan(currentDateTime) && (spreadScheduleDetail.getCurrentScheduleTime() == null ||
                        startTime.isEarlierThanOrEqualTo(spreadScheduleDetail.getCurrentScheduleTime()))){
                    spreadScheduleDetail.setCurrentScheduleTime(startTime);
                    spreadScheduleDetail.setMultiplier(schedulerMetaData1.getSpreadMultiplier());
                    spreadScheduleDetail.setEnableMultiplier(true);
                    spreadScheduleDetail.setActive(true);
                    if(log.isDebugEnabled())
                    {
                        log.debug("MMTSMSS.getNextActiveScheduleDetails: start Time selected: "+ startTime + " | "+ schedulerMetaData1.getSpreadMultiplier() );
                    }
                }

                IdcDate endDate = dayofWeekToDateMap.get(schedulerMetaData1.getEndDayOfTheWeek());
                IdcDateTime endTime = DateTimeFactory.newDateTime(endDate, new Time(schedulerMetaData1.getEndHour(), schedulerMetaData1.getEndMinute(), 0), gmtTimeZone);
                if(log.isDebugEnabled())
                {
                    sb.append(" startTime:").append( startTime ).append(" endTime:").append(endTime).append(" multiplier:").append(schedulerMetaData1.getSpreadMultiplier());
                }

                if( endTime.isLaterThan(currentDateTime) && (spreadScheduleDetail.getCurrentScheduleTime() ==null ||
                        endTime.isEarlierThan(spreadScheduleDetail.getCurrentScheduleTime()))){
                    spreadScheduleDetail.setCurrentScheduleTime(endTime);
                    spreadScheduleDetail.setMultiplier(1);
                    spreadScheduleDetail.setEnableMultiplier(false);
                    spreadScheduleDetail.setActive(true);
                    if(log.isDebugEnabled())
                    {
                        log.debug("MMTSMSS.getNextActiveScheduleDetails: end Time selected: "+ endTime  );
                    }
                }
            }
        }
        if(log.isDebugEnabled()) {
            log.debug(sb.toString());
        }
        return  spreadScheduleDetail;
    }

    private void updateDayofTheWeekToDates (){

        IdcDate currentDate = DateTimeFactory.newDate();

        if(cacheCurrentDate != null && currentDate.isSameAs(cacheCurrentDate)){
           log.info("MMTSMSS.updateDayofWeekToDates: DayofTheWeekToDates cache is still valid. No update needed");

           if(log.isDebugEnabled())
           {
               StringBuilder sb = new StringBuilder(200);
               sb.append("MMTSMSS.updateDayofWeekToDates: (0-Mon) cache ");
               Set<Integer> keys = dayofWeekToDateMap.keySet();
               for(Integer key: keys){
                   sb.append(key + ":"+ dayofWeekToDateMap.get(key) + " ");
               }
               log.debug("MMTSMSS.updateDayofWeekToDates: DayofTheWeekToDates cache "+ sb.toString());
           }
           return;
        }

        StringBuilder sb = new StringBuilder(200);

        //0-> Monday, 1->Tu 2->We 3->Th 4->Fr 5-> sat 6->sun
        int dayofWeek = currentDate.dayOfWeek();
        if(!dayofWeekToDateMap.isEmpty() ){
            dayofWeekToDateMap.clear();
        }
        cacheCurrentDate =currentDate;
        sb.append("MMTSMSS.updateDayofWeekToDates: (0-Mon) current Date:"+ currentDate + "  HashMap:");
        dayofWeekToDateMap.put(dayofWeek, currentDate);
        sb.append(dayofWeek + ":"+ currentDate + " ");
        for(int i=1; i< IdcDate.DAYS_PER_WEEK; i++ ){
            int newDayOfweek =  (dayofWeek+i) % IdcDate.DAYS_PER_WEEK;
            IdcDate newDate = currentDate.addDays(i);
            dayofWeekToDateMap.put(newDayOfweek, newDate);
            sb.append(newDayOfweek + ":"+ newDate + " ");
        }
      log.info(sb.toString());
    }


    private void processPendingSchedule(String brokerOrgName)
    {
        BrokerMetaData brokerMetaData = this.getCorePriceBrokerDataService().getBrokerMetaData(brokerOrgName);
        if(brokerMetaData != null) {
            Set<String> ccyPairs = brokerMetaData.getCurrencyPairs();
            for (String ccyPairName : ccyPairs) {
                processPendingSchedule(brokerOrgName, ccyPairName);
            }
        }
    }

    public synchronized void processPendingSchedule(String brokerOrgName, String ccyPairName) {

        if (StringUtilC.isNullOrEmpty(brokerOrgName)) {
            log.info("MMTSMSS.processPendingSchedule broker org name is null or empty");
            return;
        }
        if (StringUtilC.isNullOrEmpty(ccyPairName)) {
            log.info("MMTSMSS.processPendingSchedule ccy pair is null or empty");
            return;
        }

        String key = brokerOrgName + "@" + ccyPairName;

        SpreadMultiplierSchedulerMetaData schedulersMetaData = this.getSpreadMultiplierSchedulerDataService().getSpreadMultiplierSchedulerMetaData(brokerOrgName, ccyPairName);
        if (schedulersMetaData == null) {
            log.info("MMTSMSS.processPendingSchedule: No schedules found for " + key);
            return;
        }
        if (!schedulersMetaData.isSchedulerActive()) {
            log.info("MMTSMSS.processPendingSchedule: Master Active switch is Off for " + key);
            return;
        }
        List<SchedulerMetaData> activeSchedules = schedulersMetaData.getActiveSchedules();
        if (activeSchedules == null || activeSchedules.isEmpty()) {
            log.info("MMTSMSS.processPendingSchedule: No schedules configured for " + key);
            return;
        }

        if (scheduledSchedules.containsKey(key)) {
            log.info("MMTSMSS.processPendingSchedule: removing existing planned schedule for " + key);
            SpreadScheduleDetail scheduleDetail = scheduledSchedules.get(key);
            timer.removeRequest(null, scheduleDetail);
            scheduledSchedules.remove(key);
        }

        updateDayofTheWeekToDates();

        SpreadScheduleDetail spreadScheduleDetail = getPendingActiveScheduleDetails(schedulersMetaData);
        if (!spreadScheduleDetail.isActive()) {
            log.info("MMTSMSS.processPendingSchedule: No pending active schedule found for " + key);
            return;
        }

        TierPricingMetaData tierPricingMetaData = this.getTierPricingService().get(spreadScheduleDetail.getBrokerOrgName(), spreadScheduleDetail.getCcyPairName());
        if (tierPricingMetaData != null) {
            MarketMakerConfig marketMakerConfig = MarketMakerConfig.getInstance();
            boolean marketPulseEnabled = marketMakerConfig.isMarketPulseEnabled(spreadScheduleDetail.getBrokerOrgName());
            if(!marketPulseEnabled){
                tierPricingMetaData.setVolatilitySpreadMultiplier(spreadScheduleDetail.getMultiplier());
                tierPricingMetaData.setVolatilityEnabled(spreadScheduleDetail.isEnableMultiplier());
                this.getTierPricingService().update(tierPricingMetaData);
                log.info("MMTSMSS.processPendingSchedule: Tier Price Volatility Spread Multiplier updated for " + spreadScheduleDetail.getBrokerOrgName() + "@" + spreadScheduleDetail.getCcyPairName() +". Multiplier: "+ spreadScheduleDetail.getMultiplier() +"|"+ spreadScheduleDetail.isEnableMultiplier());
            }else{
                log.info("MMTSMSS.processPendingSchedule: Skipping update as market pulse is enabled for " + spreadScheduleDetail.getBrokerOrgName() + "@" + spreadScheduleDetail.getCcyPairName());
            }
        }
        else{
            log.info("MMTSMSS.processPendingSchedule: Tier price metadata not found for " + spreadScheduleDetail.getBrokerOrgName() + "@" + spreadScheduleDetail.getCcyPairName());
            return;
        }
    }


    private SpreadScheduleDetail getPendingActiveScheduleDetails(SpreadMultiplierSchedulerMetaData schedulersMetaData){

        IdcDateTime currentDateTime = DateTimeFactory.newDateTime();
        StringBuilder sb = new StringBuilder(500);

        if(log.isDebugEnabled())
        {
            sb.append("MMTSMSS.getPendingActiveScheduleDetails: currentDateTime:"+ currentDateTime);
        }

        SpreadScheduleDetail spreadScheduleDetail =new SpreadScheduleDetail();
        spreadScheduleDetail.setBrokerOrgName(schedulersMetaData.getBrokerOrgName());
        spreadScheduleDetail.setCcyPairName(schedulersMetaData.getCcyPair());
        Collection<SchedulerMetaData> schedulerMetaDataCollection = schedulersMetaData.getActiveSchedules();
        for(SchedulerMetaData schedulerMetaData1 : schedulerMetaDataCollection) {
            if (schedulerMetaData1.isScheduleActive() == true) {
                IdcDate startDate = dayofWeekToDateMap.get(schedulerMetaData1.getStartDayOfTheWeek());
                IdcDateTime startTime = DateTimeFactory.newDateTime(startDate, new Time(schedulerMetaData1.getStartHour(), schedulerMetaData1.getStartMinute(), 0), gmtTimeZone);

                IdcDate endDate = dayofWeekToDateMap.get(schedulerMetaData1.getEndDayOfTheWeek());
                IdcDateTime endTime = DateTimeFactory.newDateTime(endDate, new Time(schedulerMetaData1.getEndHour(), schedulerMetaData1.getEndMinute(), 0), gmtTimeZone);

                if(log.isDebugEnabled())
                {
                    sb.append(", evaluate startTime:").append( startTime ).append(" endTime:").append(endTime);
                }

                if( startTime.isEarlierThanOrEqualTo(currentDateTime) && endTime.isLaterThan (currentDateTime) ){
                    spreadScheduleDetail.setCurrentScheduleTime(startTime);
                    spreadScheduleDetail.setMultiplier(schedulerMetaData1.getSpreadMultiplier());
                    spreadScheduleDetail.setEnableMultiplier(true);
                    spreadScheduleDetail.setActive(true);
                    if(log.isDebugEnabled())
                    {
                        sb.append(", Selected schedule startTime:").append( startTime ).append(" multiplier:").append(schedulerMetaData1.getSpreadMultiplier());
                    }
                    break;
                }
            }
        }
        if(log.isDebugEnabled()) {
            log.debug(sb.toString());
        }
        return  spreadScheduleDetail;
    }

    ///Todo: Need to stop all the timers.
    public void releaseResource() {

    }

    public static MMTierSpreadMultiplierSchedulerService getInstance(){
            return  instance;
    }



    //scheduler metadata change observer
    public class SpreadSchedulerNotificationHandler  implements NotificationObserver
    {
        private SpreadMultiplierSchedulerDataService spreadMultiplierSchedulerDataService;
        private MMTierSpreadMultiplierSchedulerService service;
        SpreadSchedulerNotificationHandler(SpreadMultiplierSchedulerDataService spreadMultiplierSchedulerDataService, MMTierSpreadMultiplierSchedulerService service)
        {
            this.spreadMultiplierSchedulerDataService = spreadMultiplierSchedulerDataService;
            this.service = service;
        }

        @Override
        public void notifyAdd(Notification notification) {
            handleNotification(notification);
        }

        @Override
        public void notifyUpdate(Notification notification) {
            handleNotification(notification);
        }

        protected void handleNotification(Notification notification) {

            try {
                String orgName = notification.getEntityNameSpace();
                String entityId = notification.getEntityId();
                log.info("MMTSMSS.handleNotification: handling tier spread multiplier scheduler metadata changed for  "+ orgName + "@"+ entityId);
                SpreadMultiplierSchedulerMetaData schedulerMetaData =  this.spreadMultiplierSchedulerDataService.getSpreadMultiplierSchedulerMetaDataById(orgName, entityId);
                this.service.process(schedulerMetaData.getBrokerOrgName(), schedulerMetaData.getCcyPair());

            } catch (Exception e) {
                log.error("MMTSMSS.handleNotification:,  Notification:" + notification, e);
            }
        }

    }


    /// Timer callback class
    private class TierSpreadMultiplierSchedulerTimerHandler implements Observer
    {

        private TierPricingService tierPricingService;
        private MMTierSpreadMultiplierSchedulerService service;

        public TierSpreadMultiplierSchedulerTimerHandler(TierPricingService tierPricingService, MMTierSpreadMultiplierSchedulerService service)
        {
            this.tierPricingService = tierPricingService;
            this.service = service;
        }
        @Override
        public void update(Observable obs, Object selectorObj) {
            if (selectorObj == null) {
                return;
            }
            SpreadScheduleDetail spreadScheduleDetail = (SpreadScheduleDetail) selectorObj;
            if (spreadScheduleDetail == null)
                return;
            log.info("MMTSMSS.update: Handling Scheduler callback :"+ spreadScheduleDetail.toString());

            try {
                TierPricingMetaData tierPricingMetaData = this.tierPricingService.get(spreadScheduleDetail.getBrokerOrgName(), spreadScheduleDetail.getCcyPairName());
                if (tierPricingMetaData == null) {
                    log.info("MMTSMSS.update: Tier price metadata not found for " + spreadScheduleDetail.getBrokerOrgName() + "@" + spreadScheduleDetail.getCcyPairName());
                    return;
                }
                tierPricingMetaData.setVolatilitySpreadMultiplier(spreadScheduleDetail.getMultiplier());
                tierPricingMetaData.setVolatilityEnabled(spreadScheduleDetail.isEnableMultiplier());
                this.tierPricingService.update(tierPricingMetaData);
            } catch (Exception ex) {
                log.error("MMTSMSS.update: Error occurred while processing Tier spread scheduler Timer event for "+ spreadScheduleDetail.toString(),ex);
            }
            finally {
                this.service.process(spreadScheduleDetail.getBrokerOrgName(), spreadScheduleDetail.getCcyPairName());
            }

        }
    }
}
