package com.integral.marketmaker.service.heartbeat;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.service.MMBAService;
import com.integral.marketmaker.service.MMService;
import com.integral.messaging.*;
import com.integral.spaces.config.MetaspacesConfigMBeanImpl;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.system.server.VirtualServer;
import com.integral.system.server.VirtualServerType;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class MMBAHeartbeatService implements MMService,Runnable{

    private static final Log log            = LogFactory.getLog(MMBAHeartbeatService.class.getName());
    private enum State {
            PREINIT,INIT,STARTED,STOPPED
    }

    private MessageReceiver receiver;
    private MMBAService mmbaService;
    private ConcurrentHashMap<String, MMVSBrokerMap> virtualServerMap;
    private State state;

    public MMBAHeartbeatService(MMBAService mmbaService){
        this.mmbaService = mmbaService;
        state = State.PREINIT;
    }

    public void run(){
        if(state == State.STARTED && null!=virtualServerMap && virtualServerMap.size()>0) {
            try {
                for (Map.Entry<String, MMVSBrokerMap> entry : virtualServerMap.entrySet()) {
                    MMVSBrokerMap mmvsBrokerMap = entry.getValue();
                    long lastEntry = mmvsBrokerMap.getLastHeartBeat();
                    long heartbeatInterval = MarketMakerConfig.getInstance().getHeartBeatInterval();
                    if (lastEntry != 0L && System.currentTimeMillis() - lastEntry > heartbeatInterval) {
                        Collection<String> deployedBrokers = mmvsBrokerMap.getDeployedBrokersInMM();
                        for (String broker : deployedBrokers) {
                            mmbaService.publishStaleQuote(broker);
                        }
                        mmvsBrokerMap.setLastHeartBeat(0);
                    }
                }
            }catch(Exception e){
                log.info("MMBAHS.run - Exception during heartbeat message processing",e);
            }
        }
    }
    private class AMQPListener extends DefaultMessageListener{
        private AMQPListener(){

        }
        @Override
        public void onMessage(RMQMessage message) {
            if(state==State.STARTED) {
                String msg = null;
                try {
                    msg = new String((byte[]) message.getPayload());
                    String msgStr;
                    msgStr = msg.substring(msg.indexOf("|") + 1, msg.length());
                    String vsName = msgStr.split(",")[1];
                    VirtualServer server = mmbaService.getVirtualServer(vsName);
                    if (server.getVirtualServerType().getName().equals(VirtualServerType.MarketMakerServer)) {
                        MMVSBrokerMap mmvsBrokerMap = virtualServerMap.get(server.getName());
                        if(mmvsBrokerMap==null){
                            mmvsBrokerMap = new MMVSBrokerMap(server.getName());
                            virtualServerMap.put(mmvsBrokerMap.getVSName(), mmvsBrokerMap);
                        }
                        else { mmvsBrokerMap.updateLastHeartBeat();}
                    }
                }catch (Exception e){
                    log.error(".onMessage: Exception during processing heartbeat message msg=" + msg, e);
                }
            }

        }
    }

    private class MMVSBrokerMap{
        String vSName;
        long lastHeartBeat;
        private MMVSBrokerMap(String vSName){
            this.vSName =vSName;
            lastHeartBeat = 0;
        }
        void updateLastHeartBeat(){
            lastHeartBeat = System.currentTimeMillis();
            setLastHeartBeat(lastHeartBeat);
        }

        void setLastHeartBeat(long lasthb){
            lastHeartBeat = lasthb;
        }

        long getLastHeartBeat() {
            return lastHeartBeat;
        }

        String getVSName() {
            return vSName;
        }

        public Collection<String> getDeployedBrokersInMM(){
            // TODO:currently all brokers are deployed in single MM virtual server
            // once support for multiple MM server , this needs to return the correct list
            return  mmbaService.getDeployedMMBrokerOrganization();
        }
    }

    @Override
    public boolean init() {
        try {
            String RMQ_PREFIX       = MetaspacesConfigMBeanImpl.getInstance().getMongoDBPrefix();
            String VS_NAME          = ConfigurationFactory.getServerMBean().getVirtualServerName();
            String queueName = RMQ_PREFIX+"_"+VS_NAME+"_"+ServerRuntimeMBean.HEARTBEAT_COMPONENT+"_DiscardMsg"+"-Q";
            boolean queueDurable        = false;
            boolean queueExclusive      = false;
            boolean queueAutoDelete     = true;
            boolean manualAck           = false;
            receiver = MessageReceiverFactory.newMessageReceiver(ServerRuntimeMBean.HEARTBEAT_EXCHANGE,
                                                                queueName,queueDurable,
                                                                queueExclusive,queueAutoDelete,
                                                                manualAck, new AMQPListener());
            virtualServerMap        = new ConcurrentHashMap<String, MMVSBrokerMap>();
            state                   = State.INIT;
            return true;
        }catch (MessagingException e) {

            log.error("MMBAHS.init - Heartbeat Listener failed , Will not listen to MM Heartbeat message " , e);
        }
        return false;
    }

    @Override
    public boolean start() {
        if(state == State.INIT) {
            String HEARTBEAT_MSG_BINDING_KEY = "*.HEARTBEAT";
            try {
                receiver.addBinding(HEARTBEAT_MSG_BINDING_KEY);
                mmbaService.processEvents(this,mmbaService.getMMConfiguration().getHeartBeatInterval());
                state = State.STARTED;
                return true;
            } catch (MessagingException e) {
                log.error("MMBAHS.start - Heartbeat Listener Binding Failed, Will not listen to MM Heartbeat message " , e);
            }
        }
        return false;
    }

    @Override
    public boolean stop() {
        try {
            receiver.close();
        } catch (MessagingException e) {
          //ignore
        }
        receiver = null;
        virtualServerMap.clear();
        virtualServerMap=null;
        return false;
    }
}
