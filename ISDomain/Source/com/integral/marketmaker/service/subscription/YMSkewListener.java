package com.integral.marketmaker.service.subscription;

import com.integral.broker.configuration.ConfigurationFactory;
import com.integral.broker.configuration.PriceConfigurationMBean;
import com.integral.broker.skew.SkewInfo;
import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.is.ISCommonConstants;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.config.MarketMakerConfigMBean;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.riskmanagement.rw.snapshot.RiskWarehouseSnapshot;
import com.integral.riskmanagement.rw.snapshot.RiskWarehouseSnapshotSerializer;
import com.integral.user.Organization;
import com.integral.ym.message.MessageType;
import com.integral.ym.service.YMServiceHelper;
import com.integral.ym.service.messaging.YMReceiverService;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 8/30/16
 */
public class YMSkewListener implements YMReceiverService.MessageHandler {
    private static final Log log = LogFactory.getLog(YMSkewListener.class);
    private static final YMSkewListener instance = new YMSkewListener();
    private final RiskWarehouseSnapshot riskWarehouseSnapshot = new RiskWarehouseSnapshot();
    private final RiskWarehouseSnapshotSerializer serializer = new RiskWarehouseSnapshotSerializer();
    private final ConcurrentHashMap<String, ConcurrentHashMap<String, SkewInfo>> skewMapMap = new ConcurrentHashMap<String, ConcurrentHashMap<String, SkewInfo>>();
    private final PriceConfigurationMBean mBean = ConfigurationFactory.getInstance().getPriceConfigurationMBean();
    private final Set<Integer> orgIndexSet = new HashSet<Integer>();

    private YMSkewListener(){

    }

    public static YMSkewListener getInstance(){
        return instance;
    }

    public synchronized void start(){
        skewMapMap.clear();
        orgIndexSet.clear();
        MarketMakerConfigMBean marketMakerConfigMBean = MarketMakerConfig.getInstance();
        List<String> enabledBrokers = marketMakerConfigMBean.getMarketMakerEnabledBrokers();
        for(String orgName : enabledBrokers){
            Organization org = ReferenceDataCacheC.getInstance().getOrganization(orgName);
            if(org == null){
                log.warn("start : Org not exist orgName=" + orgName);
                continue;
            }
            orgIndexSet.add(org.getIndex());
        }
        try {
            if (YMServiceHelper.getInstance().getYmReceiverService() == null) {
                YMReceiverService s = (YMReceiverService) Class.forName(YMServiceHelper.getInstance().getYMReceiverServiceClassName()).newInstance();
                if (s.init() && s.start()) {
                    YMServiceHelper.getInstance().setYmReceiverService(s);
                }else {
                    log.error("start : Failed to instantiate YMReceiverService");
                }
            }else {
                log.error("start : YMReceiverService already instantiated");
            }
            YMServiceHelper.getInstance().getYmReceiverService().subscribeForMessages(MessageType.RISK_WAREHOUSE_SNAPSHOT, this);
            log.info("start : started successfully.");
        }catch (Exception e){
            log.error("start : Exception during starting of YMReceiverService", e);
        }

    }

    public synchronized void stop(){
        YMServiceHelper.getInstance().getYmReceiverService().unSubscribeForMessages(MessageType.RISK_WAREHOUSE_SNAPSHOT, this);
        skewMapMap.clear();
        orgIndexSet.clear();
        log.info("stop : stopped successfully.");
    }

    @Override
    public void onMessage(UnSafeBuffer pooledBuffer) {
        try {
            byte messageType = pooledBuffer.get();
            if (messageType != MessageType.RISK_WAREHOUSE_SNAPSHOT) {
                if(log.isDebugEnabled()) log.debug("onMessage : skipping. msgType=" + messageType);
                return;
            }
            //Skip version
            byte version = pooledBuffer.get();
            int orgIndex = pooledBuffer.getInt();
            if(!orgIndexSet.contains(orgIndex)){
                if(log.isDebugEnabled())log.debug("onMessage : message for non market maker org. orgIndex="+orgIndex);
                return;
            }
            riskWarehouseSnapshot.reset();
            serializer.deserialize(riskWarehouseSnapshot, pooledBuffer);
            if(!ISCommonConstants.YM_BOOK_NAME_B.equals(riskWarehouseSnapshot.getBookName())){
                if(log.isDebugEnabled()){
                    log.debug("onMessage : Skip skew update from riskWarehouseSnapshot with id="+riskWarehouseSnapshot.get_id());
                }
                return;
            }
            updateSkew(riskWarehouseSnapshot.getNamespaceName(), riskWarehouseSnapshot.getCurrencyPair(), riskWarehouseSnapshot.getProvisionedSkewInPips());
        } catch (Exception e) {
            if(log.isDebugEnabled()) log.debug("onMessage : Exception during parsing the message.", e);
        }
    }

    public double getSkew(String orgName, String cp){
        ConcurrentHashMap<String, SkewInfo> skewMap = skewMapMap.get(orgName);
        if(skewMap == null) return 0;
        SkewInfo skewInfo = skewMap.get(cp);
        if(skewInfo == null) return 0;
        if(System.currentTimeMillis() - skewInfo.getTimestamp() > mBean.getSkewTTL(orgName, cp)) return 0;
        return skewInfo.getSkewValue();
    }

    private void updateSkew(String orgName, String cp, double skew){
        ConcurrentHashMap<String, SkewInfo> skewMap = skewMapMap.get(orgName);
        if(skewMap == null){
            skewMapMap.putIfAbsent(orgName, new ConcurrentHashMap<String, SkewInfo>());
            skewMap = skewMapMap.get(orgName);
        }
        SkewInfo skewInfo = skewMap.get(cp);
        if(skewInfo == null) {
            skewMap.putIfAbsent(cp, new SkewInfo(skew, 0));
            skewInfo = skewMap.get(cp);
        }
        skewInfo.setSkewValue(skew);
        skewInfo.setTimestamp(System.currentTimeMillis());
        if(log.isDebugEnabled()) log.debug("updateSkew : skew=" + skew + ", org=" + orgName + ", cp=" + cp);
    }
}
