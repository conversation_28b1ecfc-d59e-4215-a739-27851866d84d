package com.integral.marketmaker.service.subscription;

import com.integral.user.User;

import java.util.Collection;

/**
 * Created by raghunathans on 8/12/16.
 */
public interface MMSubscriptionManager {

    void subscribe(String brokerOrgName, String userFullName);

    void unSubscribe(String brokerOrgName, String userFullName);

    void unSubscribe(User user);

    /*
     *   This method returns all the user subscriptions (user full name) for a particular Broker organization short name.
     *   @param  brokerOrgName - Broker organization short name.
     */
    Collection<String> getUserSubscriptions(String brokerName);
}
