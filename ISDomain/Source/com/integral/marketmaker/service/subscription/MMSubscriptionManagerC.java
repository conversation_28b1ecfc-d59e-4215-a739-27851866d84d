package com.integral.marketmaker.service.subscription;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.User;
import com.integral.util.collections.ConcurrentHashSet;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by raghunathans on 8/12/16.
 */
public class MMSubscriptionManagerC implements MMSubscriptionManager {

    private MMSubscriptionManagerC() {
    }

    private static MMSubscriptionManagerC instance = new MMSubscriptionManagerC();

    private static final Log log = LogFactory.getLog(MMSubscriptionManagerC.class);

    private final ConcurrentHashMap<String, ConcurrentHashSet<String>> subscriptionCache = new ConcurrentHashMap<String, ConcurrentHashSet<String>>();

    private final MMBrokerSubscriptionService service = MMBrokerSubscriptionServiceC.getInstance();

    public static MMSubscriptionManagerC getInstance() {
        return instance;
    }

    @Override
    public void subscribe(String brokerOrgName, String userFullName) {

        ConcurrentHashSet<String> userSubscriptions = subscriptionCache.get(brokerOrgName);
        if (null == userSubscriptions) {
            ConcurrentHashSet<String> newUserSubscription = new ConcurrentHashSet<String>();
            userSubscriptions = subscriptionCache.putIfAbsent(brokerOrgName, newUserSubscription);
            if (null == userSubscriptions) {
                userSubscriptions = newUserSubscription;
                //Call handlers so that we can register to RDS on this namespace.
                try {
                    service.initBrokerSubscription(brokerOrgName);
                } catch (Exception e) {
                    log.info("MMSubscriptionManagerC.subscribe:Broker  subscription initialization failed:" + brokerOrgName);
                }
            }
        }

        if (userSubscriptions.contains(userFullName)) {
            log.info("MMSubscriptionManagerC.subscribe:User subscription already exist for broker:" + brokerOrgName + ",user:" + userFullName);
        } else {
            userSubscriptions.add(userFullName);
            log.info("MMSubscriptionManagerC.subscribe:User Subscription successful for broker:" + brokerOrgName + ",user:" + userFullName);
        }
    }

    @Override
    public void unSubscribe(String brokerOrgName, String userFullName) {
        unSubscribeSubscription(brokerOrgName, userFullName);
    }

    private void unSubscribeSubscription(String brokerOrgName, String userFullName) {
        ConcurrentHashSet<String> userSubscriptionInfoMap = subscriptionCache.get(brokerOrgName);
        if (null != userSubscriptionInfoMap && !userSubscriptionInfoMap.isEmpty()) {
            userSubscriptionInfoMap.remove(userFullName);
            log.info("MMSubscriptionManagerC.unSubscribe:User Un Subscription successful for brokerOrgName:" + brokerOrgName + ",for the user");
        } else {
            log.info("MMSubscriptionManagerC.unSubscribe:User subscription does not exist to un subscribe for brokerOrgName:" + brokerOrgName + ",for the user");
        }
    }

    @Override
    public void unSubscribe(User user) {
        if (subscriptionCache.isEmpty()) {
            return;
        }

        unSubscribeSubscription(user.getOrganization().getShortName(), user.getFullName());
    }

    public Collection<String> getUserSubscriptions(String brokerName) {
        ConcurrentHashSet<String> userSubscriptionInfoMap = subscriptionCache.get(brokerName);

        if (null != userSubscriptionInfoMap) {
            return Collections.unmodifiableCollection(userSubscriptionInfoMap);
        }
        return Collections.EMPTY_LIST;
    }

    public Map<String, ConcurrentHashSet<String>> getAllUserSubscriptions() {
        return Collections.unmodifiableMap(subscriptionCache);
    }

    public void clearAll() {
        log.info("MMSubscriptionManagerC.clearAll:Clearing all the user subscriptions:" + subscriptionCache);
        subscriptionCache.clear();
    }
}