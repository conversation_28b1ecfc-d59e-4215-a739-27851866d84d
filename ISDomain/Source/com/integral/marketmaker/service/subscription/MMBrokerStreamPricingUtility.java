package com.integral.marketmaker.service.subscription;

import com.integral.broker.model.Configuration;
import com.integral.broker.model.Stream;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.stream.StreamTieredData;
import com.integral.marketmaker.stream.StreamsMetaData;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.Set;

public class MMBrokerStreamPricingUtility
{
    private static final Log log = LogFactory.getLog( MMBrokerStreamPricingUtility.class );

    final static char STATUS_ACTIVE = 'A';
    final static char STATUS_NOT_ACTIVE = 'P';

    public static void addConfigDetails ( String brokerOrgName, StreamsMetaData streamsMetaData )
    {
        if ( streamsMetaData != null )
        {
            Organization org = ISUtilImpl.getInstance ().getOrg ( brokerOrgName );
            String ccyPair = streamsMetaData.getCcyPair ();
            Collection<Stream> streams = org.getBrokerOrganizationFunction ().getStreams ();
            Set<StreamTieredData> streamTieredDatas = streamsMetaData.getStreamTieredData ();
            for ( StreamTieredData streamTieredData : streamTieredDatas )
            {
                Stream stream = getStream ( streams, streamTieredData.getStreamName () );
                if ( stream == null )
                {
                    log.info( "MSPU.addConfigDetails - no stream found with name=" + streamTieredData.getStreamName () + " in broker=" + brokerOrgName );
                }
                Configuration configuration = getActiveMarketMakerConfigurationForCcyPair ( stream, ccyPair );

                if ( stream == null || configuration == null || !stream.isActive () )
                {
                    streamTieredData.setStatus ( STATUS_NOT_ACTIVE );
                }
                streamTieredData.setHourglassPricingSupported ( isHourglassSupported ( ccyPair, stream, streamTieredData ) );
            }
        }
    }

    public static boolean isHourglassSupported ( String ccyPair, Stream brokerTieredDataStream, StreamTieredData streamTieredData )
    {
        if ( streamTieredData.getStatus () == STATUS_ACTIVE && brokerTieredDataStream != null )
        {
            Configuration configuration = getActiveMarketMakerConfigurationForCcyPair ( brokerTieredDataStream, ccyPair );
            if ( configuration != null )
            {
                return configuration.isHourglassPricingSupported ();
            }
        }
        return false;
    }

    public static Stream getStream ( Collection<Stream> streams, String streamName )
    {
        Stream brokerTieredDataStream = null;
        for ( Stream stream : streams )
        {
            if ( stream.getName ().equals ( streamName ) )
            {
                brokerTieredDataStream = stream;
                break;
            }
        }
        return brokerTieredDataStream;
    }

    private static Configuration getActiveMarketMakerConfigurationForCcyPair ( Stream stream, String ccyPair )
    {
        Configuration ccyPairConfiguration = null;
        if ( stream == null )
        {
            return null;
        }
        for ( Configuration configuration : stream.getConfigurations () )
        {
            if ( configuration.isMarketMaker () && configuration.isActive () )
            {
                Collection<CurrencyPair> currencyPairs = configuration.getCurrencyPairs ();
                for ( CurrencyPair currencyPair : currencyPairs )
                {
                    if ( currencyPair.getDisplayName ().equals ( ccyPair ) )
                    {
                        ccyPairConfiguration = configuration;
                    }
                }
                if ( ccyPairConfiguration != null )
                {
                    break;
                }
            }
        }
        return ccyPairConfiguration;
    }

}
