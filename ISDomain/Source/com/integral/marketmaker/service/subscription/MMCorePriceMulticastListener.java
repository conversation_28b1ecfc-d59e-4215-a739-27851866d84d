package com.integral.marketmaker.service.subscription;

import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.broker.marketmaker.customergroup.MarketMakerCustomerGroupRateListener;
import com.integral.broker.volatility.VolatilityMetric;
import com.integral.is.common.util.JMSProxyUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.CorePriceSerializer;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.config.MarketMakerConfigMBean;
import com.integral.marketmaker.service.coreprice.CorePriceData;
import com.integral.marketmaker.service.coreprice.TierdPriceData;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserFactory;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.net.SocketTimeoutException;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 8/18/16
 */
public class MMCorePriceMulticastListener {
    private static final Log log = LogFactory.getLog(MMCorePriceMulticastListener.class);
    private static final MMCorePriceMulticastListener instance = new MMCorePriceMulticastListener();
    private volatile boolean active;
    private Set<Integer> orgIndexSet = new CopyOnWriteArraySet<Integer>();
    private MMJmsproxyPublisher publisher;
    private MMJmsproxyTierPricePublisher tierPricePublisher;
    private HashMap<String, MMJmsProxyVolatilityFactorPublisher> volatilityPublisherHashMap = new HashMap<String, MMJmsProxyVolatilityFactorPublisher>();
    private HashMap<String, MarketMakerCustomerGroupRateListener> customerGroupRateListenerHashMap = new HashMap<String, MarketMakerCustomerGroupRateListener>();

    private AtomicInteger counter = new AtomicInteger(0);
    private MMCorePriceMulticastListener(){}

    public static MMCorePriceMulticastListener getInstance(){
        return instance;
    }

    public synchronized void start(){
        if(active){
            log.info("MMCorePriceMulticastListener.start:: Already started");
            return;
        }
        active = true;
        if(publisher != null) publisher.setRunning(false); //in case if publisher is still running
        publisher = new MMJmsproxyPublisher();
        Thread thread = new Thread(publisher);
        thread.setName("MMCorePricePublisherThread");
        thread.setDaemon(true);
        thread.start();

        if(tierPricePublisher != null) tierPricePublisher.setRunning(false);
        tierPricePublisher = new MMJmsproxyTierPricePublisher();
        Thread tierThread = new Thread(tierPricePublisher);
        tierThread.setName("MMTierPricePublisherThread");
        tierThread.setDaemon(true);
        tierThread.start();

        log.info("MMCorePriceMulticastListener.start:: Started successfully.");
    }

    public synchronized void startVolatilityPublisher(String name){
        MMJmsProxyVolatilityFactorPublisher volatilityFactorPublisher = volatilityPublisherHashMap.get(name);
        if(volatilityFactorPublisher != null && volatilityFactorPublisher.running ) {
            log.info("MMJmsProxyVolatilityFactorPublisher.startVolatilityPublisher:: Already started for org:"+ name);
            return;
        }
        volatilityFactorPublisher = new MMJmsProxyVolatilityFactorPublisher(name);
        Thread thread = new Thread(volatilityFactorPublisher);
        thread.setName("MMJmsProxyVolatilityFactorPublisher_"+counter.incrementAndGet());
        thread.setDaemon(true);
        thread.start();
        volatilityPublisherHashMap.put(name,volatilityFactorPublisher);
    }

    public synchronized void startCustomerGroupRatePublisher(String name){
        MarketMakerCustomerGroupRateListener customerGroupRateListener = customerGroupRateListenerHashMap.get(name);
        if(customerGroupRateListener != null && customerGroupRateListener.isRunning()){
            log.info("MMCorePriceMulticastListener.startCustomerGroupRatePublisher: MarketMakerCustomerGroupRateListener already started for org:"+ name );
            return;
        }

        Organization org = OrganizationUtil.getOrganization(name);
        MarketMakerCustomerGroupRateListener listener = new MarketMakerCustomerGroupRateListener(org);
        listener.startListener();
        customerGroupRateListenerHashMap.put(name, listener);
    }

    public synchronized void stop(){
        if(publisher != null) publisher.setRunning(false);
        if(tierPricePublisher != null) tierPricePublisher.setRunning(false);
        active = false;
        log.info("MMCorePriceMulticastListener.stop:: Stopped successfully.");
    }

    public void add(String name){
        Organization org = ReferenceDataCacheC.getInstance().getOrganization(name);
        if(org == null){
            log.warn("MMCorePriceMulticastListener.add:: org not exist " + name);
            return;
        }
        orgIndexSet.add(org.getIndex());
    }

    public void remove(String name){
        Organization org = ReferenceDataCacheC.getInstance().getOrganization(name);
        if(org == null){
            log.warn("MMCorePriceMulticastListener.remove:: org not exist " + name);
            return;
        }
        orgIndexSet.remove(org.getIndex());
    }

    public boolean isActive(){
        return active;
    }

    private class MMJmsproxyPublisher implements Runnable {
        private static final String handlerId = "MMCPD";
        private volatile boolean running = true;

        @Override
        public void run() {
            MarketMakerConfigMBean makerConfigMBean = MarketMakerConfig.getInstance();
            String address = makerConfigMBean.getMulticastAddress();
            int port = makerConfigMBean.getCorePriceMulticastPort();
            int timeout = makerConfigMBean.getBrokerMulticastSocketTimeout();
            int mtu = makerConfigMBean.getMulticastMTU();
            try {
                MulticastSocket multicastSocket = new MulticastSocket(port);
                multicastSocket.setTimeToLive( ConfigurationFactory.getServerMBean().getMulticastTTL());
                multicastSocket.setSoTimeout(timeout);
                InetAddress multicastAddress = InetAddress.getByName(address);
                multicastSocket.joinGroup(multicastAddress);
                byte[] bytes = new byte[mtu];
                MMSubscriptionManager subscriptionManager = MMSubscriptionManagerC.getInstance();
                while (running) {
                    try {
                        DatagramPacket packet = new DatagramPacket(bytes, bytes.length);
                        try {
                            multicastSocket.receive(packet); //blocking call
                        } catch (SocketTimeoutException e) {
                            if (log.isDebugEnabled())
                                log.debug("MMJmsproxyPublisher.run:: no rates for " + multicastSocket.getSoTimeout() + " milliseconds");
                            continue;
                        }
                        CorePriceData corePriceData = CorePriceSerializer.deserializeCorePrice(packet.getData(), orgIndexSet);
                        if (corePriceData == null) continue;
                        String orgName = corePriceData.getOrganization();
                        String ccyPair = corePriceData.getCurrencyPair();
                        String id = handlerId + orgName + ccyPair;
                        Collection<String> userSubscriptions = subscriptionManager.getUserSubscriptions(orgName);
                        for (String subscription : userSubscriptions) {
                            User user = UserFactory.getUser(subscription);
                            if (user == null) {
                                log.warn("MMJmsproxyPublisher.run:: User is null. Skipping notification for " + subscription + ", data=" + corePriceData);
                                continue;
                            }
                            JMSProxyUtil.getInstance().addToJmsProxy(
                                    id, 10,
                                    handlerId, corePriceData, user, true);
                            if (log.isDebugEnabled())
                                log.debug("MMJmsproxyPublisher.run:: published price " + corePriceData);
                        }
                        if (log.isDebugEnabled())
                            log.debug("MMJmsproxyPublisher.run:: Core Price pushed to client");
                    } catch (Throwable e) {
                        log.warn("MMJmsproxyPublisher.run failed", e);
                    }
                }
            } catch (IOException e) {
                log.warn("MMJmsproxyPublisher.run:: execption while listening on multicast", e);
            } finally {
                running = false;
                active = false;
                log.info("MMJmsproxyPublisher.run:: completed listening");
            }
        }

        public void setRunning(boolean running) {
            this.running = running;
            log.info("MMJmsproxyPublisher.setRunning:: running=" + running);
        }
    }

    private class MMJmsproxyTierPricePublisher implements Runnable {
        private static final String handlerId = "MMTPD";
        private volatile boolean running = true;

        @Override
        public void run() {
            MarketMakerConfigMBean makerConfigMBean = MarketMakerConfig.getInstance();
            String address = makerConfigMBean.getMulticastAddress();
            int port = makerConfigMBean.getTierPriceMulticastPort();
            int timeout = makerConfigMBean.getBrokerMulticastSocketTimeout();
            int mtu = makerConfigMBean.getMulticastMTU();
            try {
                MulticastSocket multicastSocket = new MulticastSocket(port);
                multicastSocket.setTimeToLive( ConfigurationFactory.getServerMBean().getMulticastTTL());
                multicastSocket.setSoTimeout(timeout);
                InetAddress multicastAddress = InetAddress.getByName(address);
                multicastSocket.joinGroup(multicastAddress);
                byte[] bytes = new byte[mtu];
                MMSubscriptionManager subscriptionManager = MMSubscriptionManagerC.getInstance();
                while (running) {
                    try {
                        DatagramPacket packet = new DatagramPacket(bytes, bytes.length);
                        try {
                            multicastSocket.receive(packet); //blocking call
                        } catch (SocketTimeoutException e) {
                            if (log.isDebugEnabled())
                                log.debug("MMJmsproxyTierPricePublisher.run:: no rates for " + multicastSocket.getSoTimeout() + " milliseconds");
                            continue;
                        }
                        TierdPriceData tierdPriceData = CorePriceSerializer.deserializeTierPrice(packet.getData(), orgIndexSet);
                        if (tierdPriceData == null) continue;
                        String orgName = tierdPriceData.getOrganization();
                        String ccyPair = tierdPriceData.getCurrencyPair();
                        String id = handlerId + orgName + ccyPair;
                        Collection<String> userSubscriptions = subscriptionManager.getUserSubscriptions(orgName);
                        for (String subscription : userSubscriptions) {
                            User user = UserFactory.getUser(subscription);
                            if (user == null) {
                                log.warn("MMJmsproxyTierPricePublisher.run:: User is null. Skipping notification for " + subscription + ", data=" + tierdPriceData);
                                continue;
                            }
                            JMSProxyUtil.getInstance().addToJmsProxy(
                                    id, 10,
                                    handlerId, tierdPriceData, user, true);
                            if (log.isDebugEnabled())
                                log.debug("MMJmsproxyTierPricePublisher.run:: published price " + tierdPriceData);
                        }
                        if (log.isDebugEnabled())
                            log.debug("MMJmsproxyTierPricePublisher.run:: Core Price pushed to client");
                    } catch (Throwable e) {
                        log.warn("MMJmsproxyTierPricePublisher.run failed", e);
                    }
                }
            } catch (IOException e) {
                log.warn("MMJmsproxyTierPricePublisher.run:: execption while listening on multicast", e);
            } finally {
                running = false;
                active = false;
                log.info("MMJmsproxyTierPricePublisher.run:: completed listening");
            }
        }

        public void setRunning(boolean running) {
            this.running = running;
            log.info("MMJmsproxyTierPricePublisher.setRunning:: running=" + running);
        }
    }


    private class MMJmsProxyVolatilityFactorPublisher implements Runnable {
        private static final String handlerId = "MMVFD";
        private volatile boolean running = true;
        private String brokerOrg;
        private static final String MULTICAST_ADDRESS_KEY = "VolatilityService";

        public MMJmsProxyVolatilityFactorPublisher(String brokerOrg){
            this.brokerOrg = brokerOrg;
        }

        @Override
        public void run() {
            MarketMakerConfigMBean makerConfigMBean = MarketMakerConfig.getInstance();
            String address = makerConfigMBean.getMulticastAddress();
            int port = makerConfigMBean.getVolatileFactorMulticastPort();
            int timeout = makerConfigMBean.getBrokerMulticastSocketTimeout();
            int mtu = makerConfigMBean.getMulticastMTU();

            try {
                MulticastSocket multicastSocket = new MulticastSocket(port);
                multicastSocket.setTimeToLive( ConfigurationFactory.getServerMBean().getMulticastTTL());
                multicastSocket.setSoTimeout(timeout);
                InetAddress inetAddress  = InetAddress.getByName(address);
                multicastSocket.joinGroup(inetAddress);
                byte[] bytes = new byte[mtu];
                MMSubscriptionManager subscriptionManager = MMSubscriptionManagerC.getInstance();
                while (running) {
                    try {
                        DatagramPacket packet = new DatagramPacket(bytes, bytes.length);
                        try {
                            multicastSocket.receive(packet); //blocking call
                        } catch (SocketTimeoutException e) {
                            if (log.isDebugEnabled())
                                log.debug("MMJmsProxyVolatilityFactorPublisher.run:: no vol factor for " + multicastSocket.getSoTimeout() + " milliseconds");
                            continue;
                        }

                        List<VolatilityMetric> volatilityMetricList = CorePriceSerializer.deserializeVolatilityMetric(packet.getData());
                        if (volatilityMetricList == null || volatilityMetricList.isEmpty()) continue;
                        for(VolatilityMetric volatilityMetric : volatilityMetricList) {

                            String orgName = volatilityMetric.getBroker();
                            String ccyPair = volatilityMetric.getCurrencyPair();
                            String id = handlerId + orgName + ccyPair;
                            Collection<String> userSubscriptions = subscriptionManager.getUserSubscriptions(orgName);
                            for (String subscription : userSubscriptions) {
                                User user = UserFactory.getUser(subscription);
                                if (user == null) {
                                    log.warn("MMJmsProxyVolatilityFactorPublisher.run:: User is null. Skipping notification for " + subscription + ", data=" + volatilityMetric);
                                    continue;
                                }
                                JMSProxyUtil.getInstance().addToJmsProxy(
                                        id, 10,
                                        handlerId, volatilityMetric, user, true);
                                if (log.isDebugEnabled())
                                    log.debug("MMJmsProxyVolatilityFactorPublisher.run:: published volatility factor " + volatilityMetric);
                            }
                            if (log.isDebugEnabled())
                                log.debug("MMJmsProxyVolatilityFactorPublisher.run:: volatility factor  pushed to client");
                        }
                    } catch (Throwable e) {
                        log.warn("MMJmsProxyVolatilityFactorPublisher.run failed", e);
                    }
                }
            } catch (IOException e) {
                log.warn("MMJmsProxyVolatilityFactorPublisher.run:: exception while listening on multicast", e);
            }
            catch (Exception ex){
                log.error();
            }
            finally {
                running = false;
                active = false;
                log.info("MMJmsProxyVolatilityFactorPublisher.run:: completed listening");
            }
        }

        public void setRunning(boolean running) {
            this.running = running;
            log.info("MMJmsProxyVolatilityFactorPublisher.setRunning:: running=" + running);
        }
    }
}
