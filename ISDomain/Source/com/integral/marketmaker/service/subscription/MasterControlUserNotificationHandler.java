package com.integral.marketmaker.service.subscription;


import com.integral.broker.configuration.ConfigurationMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.maker.service.broker.CorePriceBrokerDataService;
import com.integral.maker.service.mastercontrol.MasterControlSevice;
import com.integral.marketmaker.broker.BrokerMetaData;
import com.integral.marketmaker.rule.MasterControl;
import com.integral.notification.jmsproxy.Notifier;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;
import com.integral.user.User;
import com.integral.user.UserFactory;

import java.util.Collection;

public class MasterControlUserNotificationHandler  implements NotificationObserver
{
    private static final int MM_VIEW_MSG_PRIORITY = 10;
    private static final String MM_MASTER_CONTROL_VIEW_HANDLER = "MMMC";
    public static final String MM_MASTER_CONTROL_VIEW = "MasterControlView:";

    private static final Log log = LogFactory.getLog(MasterControlUserNotificationHandler.class);
    private MasterControlSevice masterControlService = null;
    private CorePriceBrokerDataService corePriceBrokerDataService = null;
    private ConfigurationMBean configurationMBean = null;
    MasterControlUserNotificationHandler()
    {
        masterControlService = MarketMakerFactory.getInstance().getMasterControlService();
        corePriceBrokerDataService = MarketMakerFactory.getInstance().getCorePriceBrokerService().getBrokerDataService();
        configurationMBean=MarketMakerFactory.getInstance().getConfigurationMBean();
    }

    @Override
    public void notifyAdd(Notification notification) {
        sendPushNotification(notification);
    }

    @Override
    public void notifyUpdate(Notification notification) {
        sendPushNotification(notification);
    }

    protected void sendPushNotification(Notification notification) {
        String orgName = notification.getEntityNameSpace();

        try {
            MasterControl masterControl = masterControlService.query(orgName);

            Collection<String> userSubscriptions = MMSubscriptionManagerC.getInstance().getUserSubscriptions(orgName);
            for (String userSubscription : userSubscriptions) {

                User user = UserFactory.getUser(userSubscription);
                if (null == user) {
                    log.info("sendPushNotification:Notifications are not sent since user is null:" + userSubscription + ", Notification:" + notification);
                } else {
                    Notifier.getInstance().addToJmsProxy(MM_MASTER_CONTROL_VIEW + System.currentTimeMillis(), MM_VIEW_MSG_PRIORITY,
                            MM_MASTER_CONTROL_VIEW_HANDLER, masterControl, user, true);
                    if (log.isDebugEnabled()) {
                        log.debug("sendPushNotification:Notification sent for the Master Control Config:" + masterControl +
                                ",viewName:" + MM_MASTER_CONTROL_VIEW + ",viewHandlerName:" + MM_MASTER_CONTROL_VIEW_HANDLER);
                    }

                }

            }
        } catch (Exception e) {
            log.error("sendPushNotification:Failed to push Master Control Config change notification to client,  Notification:" + notification);
        }
    }

}
