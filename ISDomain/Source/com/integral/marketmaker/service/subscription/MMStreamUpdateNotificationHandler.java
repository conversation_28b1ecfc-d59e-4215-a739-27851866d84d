package com.integral.marketmaker.service.subscription;


import com.integral.broker.configuration.ConfigurationMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.maker.service.broker.CorePriceBrokerDataService;
import com.integral.maker.service.mastercontrol.MasterControlSevice;
import com.integral.marketmaker.broker.BrokerMetaData;
import com.integral.marketmaker.rule.MasterControl;
import com.integral.notification.jmsproxy.Notifier;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;
import com.integral.user.User;
import com.integral.user.UserFactory;
import org.json.JSONStringer;

import java.util.Collection;

public class MMStreamUpdateNotificationHandler  implements NotificationObserver
{
    private static final int MM_VIEW_MSG_PRIORITY = 10;
    private static final String STREAM_UPDATE_HANDLER = "PESUH"; //Stream update handler
    public static final String MM_STRAM_UPDATE_VIEW = "StreamUpdateView";

    private static final Log log = LogFactory.getLog(MMStreamUpdateNotificationHandler.class);

    MMStreamUpdateNotificationHandler()
    {

    }

    @Override
    public void notifyAdd(Notification notification) {
        sendPushNotification(notification);
    }

    @Override
    public void notifyUpdate(Notification notification) {
        sendPushNotification(notification);
    }

    protected void sendPushNotification(Notification notification) {
        String orgName = notification.getEntityNameSpace();
        String streamId = notification.getEntityId();
        JSONStringer jsonStringer = new JSONStringer();
        try {
            String notificationMessage = jsonStringer.object().key("org").value(orgName).key("stream").value(streamId).endObject().toString();

            Collection<String> userSubscriptions = MMSubscriptionManagerC.getInstance().getUserSubscriptions(orgName);
            for (String userSubscription : userSubscriptions) {

                User user = UserFactory.getUser(userSubscription);
                if (null == user) {
                    log.info("MMStreamUpdateNotificationHandler.sendPushNotification:Notifications are not sent since user is null:" + userSubscription + ", Notification:" + notification);
                } else {
                    Notifier.getInstance().addToJmsProxy(MM_STRAM_UPDATE_VIEW + System.currentTimeMillis(), MM_VIEW_MSG_PRIORITY,
                            STREAM_UPDATE_HANDLER, notificationMessage, user, false);
                    if (log.isDebugEnabled()) {
                        log.debug("MMStreamUpdateNotificationHandler.sendPushNotification:Notification sent for the Stream change:" + notificationMessage +
                                ",viewName:" + MM_STRAM_UPDATE_VIEW + ",viewHandlerName:" + STREAM_UPDATE_HANDLER);
                    }
                }
            }
        } catch (Exception e) {
            log.error("sendPushNotification:Failed to push Master Control Config change notification to client,  Notification:" + notification, e );
        }
    }

}
