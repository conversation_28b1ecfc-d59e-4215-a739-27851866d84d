package com.integral.marketmaker.service.subscription;


import com.integral.broker.configuration.ConfigurationMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.maker.service.broker.CorePriceBrokerDataService;
import com.integral.maker.service.pricing.MarketMakerPriceControlService;
import com.integral.marketmaker.broker.BrokerMetaData;
import com.integral.marketmaker.rule.pricecontrol.MMChannelConfig;
import com.integral.notification.jmsproxy.Notifier;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;
import com.integral.user.User;
import com.integral.user.UserFactory;

import java.util.Collection;

public class ClientChannelUserNotificationHandler  implements NotificationObserver
{
    private static final int MM_VIEW_MSG_PRIORITY = 10;
    private static final String MM_CHANNELSTATUS_VIEW_HANDLER = "MMCS";
    public static final String MM_CHANNELSTATUS_VIEW = "MMChannelStatusView:";
    public static final String MM_PRICE_CONTROL_VIEW_HANDLER ="MMPC";
    public static final String MM_PRICE_CONTROL_VIEW ="MMPriceControlView:";
    private static final Log log = LogFactory.getLog(ClientChannelUserNotificationHandler.class);
    private MarketMakerPriceControlService priceControlService = null;
    private CorePriceBrokerDataService corePriceBrokerDataService = null;
    private ConfigurationMBean configurationMBean = null;
    ClientChannelUserNotificationHandler()
    {
        priceControlService  = MarketMakerFactory.getInstance().getPriceControlService();
        corePriceBrokerDataService = MarketMakerFactory.getInstance().getCorePriceBrokerService().getBrokerDataService();
        configurationMBean=MarketMakerFactory.getInstance().getConfigurationMBean();
    }

    @Override
    public void notifyAdd(Notification notification) {
        sendPushNotification(notification);
    }

    @Override
    public void notifyUpdate(Notification notification) {
        sendPushNotification(notification);
        sendPriceControlNotification(notification);
    }

    protected void sendPushNotification(Notification notification)
    {
        String orgName = notification.getEntityNameSpace();
        BrokerMetaData brokerMetaData = corePriceBrokerDataService.getBrokerMetaData(orgName);
        if( null!= brokerMetaData )
        {
            Collection<String> currencyPairs = brokerMetaData.getCurrencyPairs();
            if (null != currencyPairs && currencyPairs.size() > 0)
            {
                try
                {
                    Collection<MMChannelConfig> mmChannelConfigs = priceControlService.getClientChannelStatus(orgName, currencyPairs);
                    Collection<String> userSubscriptions = MMSubscriptionManagerC.getInstance().getUserSubscriptions(orgName);
                    for (String userSubscription : userSubscriptions) {

                        User user = UserFactory.getUser(userSubscription);
                        if (null == user)
                        {
                            log.info("sendPushNotification:Notifications are not sent since user is null:" + userSubscription + ", Notification:" + notification);
                        }
                        else
                        {
                            Notifier.getInstance().addToJmsProxy(MM_CHANNELSTATUS_VIEW + System.currentTimeMillis(), MM_VIEW_MSG_PRIORITY,
                                    MM_CHANNELSTATUS_VIEW_HANDLER, mmChannelConfigs, user, true);
                            if (log.isDebugEnabled())
                            {
                                log.debug("sendPushNotification:Notification sent for the Client Channel Config:" + mmChannelConfigs +
                                        ",viewName:"+MM_CHANNELSTATUS_VIEW+",viewHandlerName:"+MM_CHANNELSTATUS_VIEW_HANDLER);
                            }
                        }

                    }
                }
                catch (Exception e)
                {
                    log.error("sendPushNotification:Failed to  push Client Channel Config change notification to client,  Notification:" + notification);
                }

            }
            else
            {
                log.info("sendPushNotification:IGnoring Client Channel Notification no currency pair configured for Market Maker User, Notification:" + notification);
            }
        }
        else
        {
            log.info("sendPushNotification:Ignoring Client Channel Notification no currency pair configured for Market Maker User, Notification:" + notification);
        }

    }

    protected void sendPriceControlNotification(Notification notification) {

        try {
            String orgName = notification.getEntityNameSpace();
            Collection<String> userSubscriptions = MMSubscriptionManagerC.getInstance().getUserSubscriptions(orgName);
            com.integral.marketmaker.rule.pricecontrol.PriceControl priceControl = priceControlService.query(orgName);

            for (String userSubscription : userSubscriptions) {
                User user = UserFactory.getUser(userSubscription);
                if (null == user) {
                    log.info("sendPushNotification:Notifications are not sent since user is null:" + userSubscription + ", Notification:" + notification);
                } else {
                    Notifier.getInstance().addToJmsProxy(MM_PRICE_CONTROL_VIEW + System.currentTimeMillis(), MM_VIEW_MSG_PRIORITY,
                            MM_PRICE_CONTROL_VIEW_HANDLER, priceControl, user, true);
                    if (log.isDebugEnabled()) {
                        log.debug("sendPushNotification:Notification sent for the Price MasterControl Config:" + priceControl +
                                ",viewName:" + MM_PRICE_CONTROL_VIEW + ",viewHandlerName:" + MM_PRICE_CONTROL_VIEW_HANDLER);
                    }
                }
            }
        } catch (Exception e) {
            log.error("sendPushNotification:Failed to  push Client Price MasterControl Config change notification to client,  Notification:" + notification);
        }
    }


}
