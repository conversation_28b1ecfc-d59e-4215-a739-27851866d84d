package com.integral.marketmaker.service.subscription;

import com.integral.broker.configuration.ConfigurationFactory;
import com.integral.is.common.util.JMSProxyUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.marketmaker.tierpricing.SpreadMultiplierSchedulerMetaData;
import com.integral.model.ReferenceEntity;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.marketmaker.stream.StreamsMetaData;
import com.integral.volatilityservice.VolatilityServiceConfiguration;
import com.integral.volatilityservice.MovingAverageAlgoConfiguration;

import java.util.Collection;

/**
 * Created by raghunathans on 8/18/16.
 */
public class MMBrokerSubscriptionServiceC implements MMBrokerSubscriptionService {

    private static final int MM_VIEW_MSG_PRIORITY = 10;
    private static final String MM_BROKER_VIEW_HANDLER = "MMBV";

    private static final String MM_PROVIDER_VIEW_HANDLER = "MMPV";

    private static final String MM_CORE_PRICING_VIEW_HANDLER = "MMCPV";

    private static final String MM_TIER_PRICING_VIEW_HANDLER = "MMTPV";

    private static final String MM_STREAM_PRICING_VIEW_HANDLER = "MMSPV";

    private static final String MM_TIER_SPREAD_SCHEDULER_VIEW_HANDLER = "MMTSSV";

    private static final String VS_MOVING_AVG_ALGO_VIEW_HANDLER ="VSMAAV";


    private static MarketMakerFactory marketMakerFactory = MarketMakerFactory.getInstance();
    private BrokerMetaDataChangeHandler brokerMetaDataChangeHandler = new BrokerMetaDataChangeHandler();
    private ProviderMetaDataChangeHandler providerMetaDataChangeHandler = new ProviderMetaDataChangeHandler();
    private CorePricingMetaDataChangeHandler corePricingMetaDataChangeHandler = new CorePricingMetaDataChangeHandler();
    private TierPricingMetaDataChangeHandler tierPricingMetaDataChangeHandler = new TierPricingMetaDataChangeHandler();
    private StreamPricingMetaDataChangeHandler streamPricingMetaDataChangeHandler = new StreamPricingMetaDataChangeHandler();
    private ClientChannelUserNotificationHandler clientChannelUserNotificationHandler = new ClientChannelUserNotificationHandler();
    private MasterControlUserNotificationHandler masterControlUserNotificationHandler = new MasterControlUserNotificationHandler();
    private TierSpreadSchedulerMetaDataChangeHandler tierSpreadSchedulerMetaDataChangeHandler = new TierSpreadSchedulerMetaDataChangeHandler();
    private MMStreamUpdateNotificationHandler streamUpdateNotificationHandler = new MMStreamUpdateNotificationHandler();
    private VolatilityServiceConfigChangeHandler volatilityServiceConfigChangeHandler = new VolatilityServiceConfigChangeHandler();
    private MovingAvgAlgoMetaDataChangeHandler movingAvgAlgoMetaDataChangeHandler = new MovingAvgAlgoMetaDataChangeHandler();
    private static final Log log = LogFactory.getLog(MMBrokerSubscriptionServiceC.class);

    private static MMBrokerSubscriptionServiceC instance = new MMBrokerSubscriptionServiceC();

    public static MMBrokerSubscriptionServiceC getInstance() {
        return instance;
    }

    private MMBrokerSubscriptionServiceC() {
        ConfigurationFactory.getInstance().getVolatilityServiceMBean().register(volatilityServiceConfigChangeHandler);
    }

    @Override
    public void initBrokerSubscription(String brokerOrgName) {

        //Subscribing to RDS for this broker org name.
        MarketMakerFactory instance = MarketMakerFactory.getInstance();
        instance.getCorePriceBrokerService().getBrokerDataService().registerForMetaDataChange(brokerOrgName, brokerMetaDataChangeHandler);
        instance.getCorePriceProviderService().getProviderDataService().registerForMetaDataChange(brokerOrgName, providerMetaDataChangeHandler);
        instance.getCorePricingService().getCorePricingDataService().registerForMetaDataChange(brokerOrgName, corePricingMetaDataChangeHandler);
        instance.getTierPricingService().getTierPricingDataService().registerForMetaDataChange(brokerOrgName, tierPricingMetaDataChangeHandler);
        instance.getSpreadMultiplierSchedulerService().getSpreadMultiplierSchedulerDataService().registerForMetaDataChange(brokerOrgName, tierSpreadSchedulerMetaDataChangeHandler);
        instance.getStreamPricingService().getStreamPricingDataService().registerForMetaDataChange(brokerOrgName, streamPricingMetaDataChangeHandler);
        instance.getPriceControlService().registerListenerForDataChange(brokerOrgName,clientChannelUserNotificationHandler );
        instance.getMasterControlService().registerListenerForDataChange(brokerOrgName, masterControlUserNotificationHandler);
        instance.getVolatilityServiceConfigService().registerForMetaDataChange(brokerOrgName, volatilityServiceConfigChangeHandler);
        instance.getMovingAvgAlgoConfigurationService().registerForMetaDataChange(brokerOrgName, movingAvgAlgoMetaDataChangeHandler);
        MMCorePriceMulticastListener corePriceMulticastListener = MMCorePriceMulticastListener.getInstance();
        corePriceMulticastListener.start();
        corePriceMulticastListener.add(brokerOrgName);
        corePriceMulticastListener.startVolatilityPublisher(brokerOrgName);
        corePriceMulticastListener.startCustomerGroupRatePublisher(brokerOrgName);
        log.info("Successfully Registered Notification handlers for the broker organization:" + brokerOrgName);
    }

    public void notifyStreamUpdate(Notification notification){
        streamUpdateNotificationHandler.notifyUpdate(notification);
    }

    public class BrokerMetaDataChangeHandler extends MarketMakerMetaDataChangeHandler {

        @Override
        protected ReferenceEntity getReferenceEntity(String orgName, String entityId) {
            return marketMakerFactory.getCorePriceBrokerService().getBrokerDataService().getBrokerMetaDataById(orgName, entityId);
        }

        @Override
        protected String getViewHandlerName() {
            return MM_BROKER_VIEW_HANDLER;
        }

    }

    public class ProviderMetaDataChangeHandler extends MarketMakerMetaDataChangeHandler {

        @Override
        protected ReferenceEntity getReferenceEntity(String orgName, String entityId) {
            return marketMakerFactory.getCorePriceProviderService().getProviderDataService().getProvidersMetaDataById(orgName, entityId);
        }

        @Override
        protected String getViewHandlerName() {
            return MM_PROVIDER_VIEW_HANDLER;
        }
    }

    public class CorePricingMetaDataChangeHandler extends MarketMakerMetaDataChangeHandler {

        @Override
        protected ReferenceEntity getReferenceEntity(String orgName, String entityId) {
            return marketMakerFactory.getCorePricingService().getCorePricingDataService().getCorePricingMetaDataById(orgName, entityId);
        }

        @Override
        protected String getViewHandlerName() {
            return MM_CORE_PRICING_VIEW_HANDLER;
        }
    }

    public class TierPricingMetaDataChangeHandler extends MarketMakerMetaDataChangeHandler {

        @Override
        protected ReferenceEntity getReferenceEntity(String orgName, String entityId) {
            return marketMakerFactory.getTierPricingService().getTierPricingDataService().getTierPricingMetaDataById(orgName, entityId);
        }

        @Override
        protected String getViewHandlerName() {
            return MM_TIER_PRICING_VIEW_HANDLER;
        }
    }



    public class TierSpreadSchedulerMetaDataChangeHandler extends MarketMakerMetaDataChangeHandler{

        @Override
        protected ReferenceEntity getReferenceEntity(String orgName, String entityId) {
            SpreadMultiplierSchedulerMetaData spreadMultiplierSchedulerMetaData =  marketMakerFactory.getSpreadMultiplierSchedulerService().getSpreadMultiplierSchedulerDataService().getSpreadMultiplierSchedulerMetaDataById(orgName, entityId);
            return  getResponseSpreadMultiplierSchedulerMetaData(spreadMultiplierSchedulerMetaData);
        }

        @Override
        protected String getViewHandlerName() {
            return MM_TIER_SPREAD_SCHEDULER_VIEW_HANDLER;
        }

        private SpreadMultiplierSchedulerMetaData getResponseSpreadMultiplierSchedulerMetaData(SpreadMultiplierSchedulerMetaData spreadMultiplierSchedulerMetaData)
        {
            if(spreadMultiplierSchedulerMetaData == null)
                return null;

            SpreadMultiplierSchedulerMetaData responseMetadata = new  SpreadMultiplierSchedulerMetaData(spreadMultiplierSchedulerMetaData.getBrokerOrgName(), spreadMultiplierSchedulerMetaData.getCcyPair());
            responseMetadata.setSchedules(spreadMultiplierSchedulerMetaData.getActiveSchedules());
            responseMetadata.setSchedulerActive(spreadMultiplierSchedulerMetaData.isSchedulerActive());
            responseMetadata.setVersionId(spreadMultiplierSchedulerMetaData.getVersionId());

            return  responseMetadata;
        }
    }

    public class StreamPricingMetaDataChangeHandler extends MarketMakerMetaDataChangeHandler {

        @Override
        protected ReferenceEntity getReferenceEntity(String orgName, String entityId) {
            StreamsMetaData streamsMetaData=  marketMakerFactory.getStreamPricingService().getStreamPricingDataService().getStreamsMetaDataById(orgName, entityId);
            MMBrokerStreamPricingUtility.addConfigDetails(orgName, streamsMetaData);
            return streamsMetaData;
        }

        @Override
        protected String getViewHandlerName() {
            return MM_STREAM_PRICING_VIEW_HANDLER;
        }
    }

    public class MovingAvgAlgoMetaDataChangeHandler extends MarketMakerMetaDataChangeHandler{

        @Override
        protected ReferenceEntity getReferenceEntity(String orgName, String entityId) {
           MovingAverageAlgoConfiguration algoConfiguration = marketMakerFactory.getMovingAvgAlgoConfigurationService().getById(entityId, orgName);
            return algoConfiguration;
        }

        @Override
        protected String getViewHandlerName() {
            return VS_MOVING_AVG_ALGO_VIEW_HANDLER;
        }
    }

    public abstract class MarketMakerMetaDataChangeHandler implements NotificationObserver {

        @Override
        public void notifyAdd(Notification notification) {
            sendPushNotification(notification);
        }

        @Override
        public void notifyUpdate(Notification notification) {
            sendPushNotification(notification);
        }

        protected abstract ReferenceEntity getReferenceEntity(String orgName, String entityId);

        protected abstract String getViewHandlerName();



        protected void sendPushNotification(Notification notification) {
            String orgName = notification.getEntityNameSpace();
            String entityId = notification.getEntityId();
            ReferenceEntity metaDataById = getReferenceEntity(orgName, entityId);

            if (null != metaDataById) {
                Collection<String> userSubscriptions = MMSubscriptionManagerC.getInstance().getUserSubscriptions(orgName);

                for (String userSubscription : userSubscriptions) {

                    User user = UserFactory.getUser(userSubscription);
                    if (null == user) {
                        log.info("sendPushNotification:Notifications are not sent since user is null:" + userSubscription + ", Notification:" + notification);
                    }

                    JMSProxyUtil.getInstance().addToJmsProxy(
                            getViewHandlerName() + metaDataById.get_id(), MM_VIEW_MSG_PRIORITY,
                            getViewHandlerName(), metaDataById, user, true);

                    if (log.isDebugEnabled()) {
                        log.debug("sendPushNotification:Notification sent for the metadata:" + metaDataById + ",viewHandlerName:" + getViewHandlerName());
                    }
                }
            } else {
                log.info("sendPushNotification:Notifications are not sent since metadata is null:" + metaDataById + ", Notification:" + notification);
            }
        }
    }
}
