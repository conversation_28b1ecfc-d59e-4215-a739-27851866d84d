package com.integral.marketmaker.service.subscription;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.notification.jmsproxy.Notifier;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;
import com.integral.system.configuration.ConfigUpdateHandler;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.volatilityservice.VolatilityServiceMBean;
import org.json.JSONStringer;

import java.util.Collection;
import java.util.List;

public class VolatilityServiceConfigChangeHand<PERSON> implements NotificationObserver, ConfigUpdateHandler {
    private static final Log log = LogFactory.getLog(VolatilityServiceConfigChangeHandler.class);
    private static final String MM_VOLATILITY_SERVICE_CONFIG_CHANGE_HANDLER ="MMVSCCH";
    private static final int MM_VIEW_MSG_PRIORITY = 10;
    @Override
    public void notifyAdd(Notification notification) {
        sendPushNotification(notification);
    }

    @Override
    public void notifyUpdate(Notification notification) {
        sendPushNotification(notification);
    }

    protected void sendPushNotification(Notification notification) {
        String orgName = notification.getEntityNameSpace();
        notify(orgName);
    }

    private void notify(String orgName){
        JSONStringer jsonStringer = new JSONStringer();
        try {
            String notificationMessage = jsonStringer.object().key("org").value(orgName).endObject().toString();

            Collection<String> userSubscriptions = MMSubscriptionManagerC.getInstance().getUserSubscriptions(orgName);
            if(userSubscriptions == null)
               return;
            for (String userSubscription : userSubscriptions) {
                User user = UserFactory.getUser(userSubscription);
                if (null == user) {
                    log.info("VolatilityServiceConfigChangeHandler.sendPushNotification:Notifications are not sent since user is null:" + userSubscription);
                } else {
                    Notifier.getInstance().addToJmsProxy(MM_VOLATILITY_SERVICE_CONFIG_CHANGE_HANDLER + System.currentTimeMillis(), MM_VIEW_MSG_PRIORITY,
                            MM_VOLATILITY_SERVICE_CONFIG_CHANGE_HANDLER, notificationMessage, user, false);
                    if (log.isDebugEnabled()) {
                        log.debug("VolatilityServiceConfigChangeHandler.sendPushNotification:Notification sent for Volatility Config change:" + notificationMessage +
                                ",viewName:" + MM_VOLATILITY_SERVICE_CONFIG_CHANGE_HANDLER + ",viewHandlerName:" + MM_VOLATILITY_SERVICE_CONFIG_CHANGE_HANDLER);
                    }
                }
            }
        } catch (Exception e) {
            log.error("sendPushNotification:Failed to push Volatility service Config change notification to client." , e );
        }
    }

    @Override
    public void handleUpdate(String propertyName, String event) {
        if (!VolatilityServiceMBean.ENABLED_ORGS.equals(propertyName)) {
            return;
        }
        log.info("VolatilityServiceConfigChangeHandler.handleUpdate:  handling property update.."+VolatilityServiceMBean.ENABLED_ORGS);
        List<String> marketMakerEnabledOrgs = MarketMakerConfig.getInstance().getMarketMakerEnabledBrokers();
        for(String org: marketMakerEnabledOrgs){
            if(org == null)
                continue;
            notify(org);
        }
    }
}
