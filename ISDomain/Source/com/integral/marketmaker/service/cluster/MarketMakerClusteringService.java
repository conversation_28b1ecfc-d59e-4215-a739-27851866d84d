package com.integral.marketmaker.service.cluster;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.cluster.ClusterMetaData;
import com.integral.rds.client.ClientFactory;
import com.integral.rds.client.ReferenceDataService;
import com.integral.services.ServiceContainerMBean;
import com.integral.services.cluster.ClusterManager;
import com.integral.services.cluster.ClusterManagerFactory;
import com.integral.services.cluster.notification.OnlineOffLineTransitionHandler;

import java.util.Collections;


public class MarketMakerClusteringService {

    private Log log = LogFactory.getLog(MarketMakerClusteringService.class);
    private static final String NAMESPACE_DELIMITTER = "-";

    private ClusterManagerFactory factory = ClusterManagerFactory.getInstance();
    private ClusterManager clusterManager;

    public static final MarketMakerClusteringService service = new MarketMakerClusteringService();

    public static MarketMakerClusteringService getInstance(){
        return service;
    }
    public boolean joinCluster(String clusterName, OnlineOffLineTransitionHandler handler
    ) throws Exception{

        ReferenceDataService rds = ClientFactory.getFactory().getReferenceDataService();

        if (rds == null) {
            throw new Exception("RDS Client not initialized");
        }

        ServiceContainerMBean mmServiceMBean = ServiceContainerMBean.getInstance();
        String nameSpacedClusterName = mmServiceMBean.getServicesNamespace() + NAMESPACE_DELIMITTER + clusterName;
        ClusterMetaData clusterMetaData = (ClusterMetaData) rds.retrieveById(ClusterMetaData.class, nameSpacedClusterName,ClusterMetaData.NAMESPACE);

        if(null == clusterMetaData) {
            throw new RuntimeException("MMClusteringService.joinCluster(): Failed to join cluster.Unable to get cluster metadata:" + nameSpacedClusterName);
        }

        log.info("Found MM Cluster in RDS. Joining...");
        clusterManager = factory.createClusterManager(mmServiceMBean, Collections.singletonList(clusterMetaData));
        boolean result = clusterManager.startInstance(clusterName, handler);
        return result;
    }

    public boolean leaveCluster(String clusterName) {
        if(clusterManager!=null){
            clusterManager.stopInstance(clusterName);
            return true;
        }
        return false;
    }
}
