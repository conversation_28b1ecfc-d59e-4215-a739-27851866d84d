package com.integral.marketmaker.service;

import com.integral.marketmaker.service.coreprice.CorePriceService;
import com.integral.marketmaker.service.coreprice.TOBBasedCorePriceServiceSubscription;

/**
 * Created by verma on 1/14/16.
 */
public class MarketMakerServices {
    private static CorePriceService<TOBBasedCorePriceServiceSubscription> tobBasedCorePriceService;

    public static CorePriceService<TOBBasedCorePriceServiceSubscription> getTOBBasedCorePriceService(){
        return tobBasedCorePriceService;
    }

    public static void setTOBBasedCorePriceService(CorePriceService<TOBBasedCorePriceServiceSubscription> service) {
        tobBasedCorePriceService = service;
    }
}
