package com.integral.marketmaker.service;

/**
 * Created by verma on 4/11/16.
 */
public interface MMService {
    /**
     * Initialize the service.
     * @return true if successfully initialized.
     */
    boolean init();

    /**
     * Start the service.
     * @return true if the start is successful.
     */
    boolean start();

    /**
     * Stop the service.
     * @return true if the stop is successful.
     */
    boolean stop();
}
