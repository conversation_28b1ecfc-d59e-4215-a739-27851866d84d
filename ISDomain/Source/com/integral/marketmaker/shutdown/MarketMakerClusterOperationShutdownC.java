package com.integral.marketmaker.shutdown;

import com.integral.admin.utils.StringUtils;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.service.cluster.MarketMakerClusteringService;
import com.integral.system.runtime.ShutdownTask;

import java.util.Hashtable;

public class MarketMakerClusterOperationShutdownC implements ShutdownTask {

    private static final Log log = LogFactory.getLog(MarketMakerClusterOperationShutdownC.class);
    @Override
    public String shutdown(String aName, Hashtable args) {
        String clusterName = MarketMakerConfig.getInstance().getClusterName();
        if(StringUtils.isNullOrEmptyString(clusterName)){
            log.info("Leaving Cluster:"+clusterName);
            boolean result = MarketMakerClusteringService.getInstance().leaveCluster(clusterName);
            log.info("Successfully Left the cluster:"+clusterName+":"+result);
        }

        return null;
    }
}
