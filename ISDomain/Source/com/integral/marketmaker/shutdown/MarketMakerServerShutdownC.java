package com.integral.marketmaker.shutdown;

import com.integral.broker.filter.BrokerFilterFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.service.coreprice.CorePriceService;
import com.integral.marketmaker.service.coreprice.CorePriceServiceFactory;
import com.integral.marketmaker.service.coreprice.MMCorePriceServiceC;
import com.integral.marketmaker.service.coreprice.MMVolatilityFactorPublisherC;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.runtime.ShutdownTask;
import com.integral.user.Organization;

import java.util.Hashtable;
import java.util.List;

public class MarketMakerServerShutdownC implements ShutdownTask {
    private static Log log = LogFactory.getLog(MarketMakerServerShutdownC.class);

    @Override
    public String shutdown(String aName, Hashtable args) {
        List<String> marketMakerEnabledOrgs = MarketMakerConfig.getInstance().getMarketMakerEnabledBrokers();
        log.info("MarketMakerServerShutdownC.shutdown:: Market Maker Configured Orgs: " + marketMakerEnabledOrgs);

        if(!MarketMakerConfig.getInstance().isJoinMMClusterOnStartup()){
            for(String brokerName : marketMakerEnabledOrgs) {
                stopCorePriceServices(brokerName);
            }
        }

        return null;
    }

    public void stopCorePriceServices(String brokerName){
        {
            try{

                Organization broker = ReferenceDataCacheC.getInstance().getOrganization(brokerName);
                if(broker==null){
                    log.warn("Failed to stop core services! Broker org does not exist:"+brokerName);
                    return;
                }

                //Leaving the multicast group for Volatility Publisher
                MMVolatilityFactorPublisherC.getInstance(true).leaveMulticast(brokerName);
                MMVolatilityFactorPublisherC.getInstance(false).leaveMulticast(brokerName);
                log.info("MarketMakerServerShutdownC.stopCorePriceServices:: Left Multicast for  " + brokerName);

                //Stopping Rate filter
                BrokerFilterFactory brokerFilterFactory = BrokerFilterFactory.getInstance();
                if(null!=brokerFilterFactory.getRateFilterManager(broker)){
                    brokerFilterFactory.getRateFilterManager(broker).stop();
                    log.info("MarketMakerServerShutdownC.stopCorePriceServices:: RateFilter stopped for " + brokerName);
                }

                //Stopping core pricing service
                CorePriceService corePriceService = CorePriceServiceFactory.getInstance().getCorePriceService(broker, CorePriceServiceFactory.CorePriceServiceType.MM);
                if((corePriceService instanceof MMCorePriceServiceC)){
                    corePriceService.stop();
                    log.info("MarketMakerServerShutdownC.stopCorePriceServices:: stopped core price services for " + brokerName);
                }else {
                    log.error("MarketMakerServerShutdownC.stopCorePriceServices:: core price service is not instance of Market Maker " + brokerName);
                }

            }catch (Exception e){
                log.error("MarketMakerServerShutdownC.stopCorePriceServices:: Exception during stopping core price services for " + brokerName, e);
            }
        }
    }

}
