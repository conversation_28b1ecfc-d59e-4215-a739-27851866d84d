package com.integral.marketmaker.shutdown;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.service.MarketMakerServices;
import com.integral.system.runtime.ShutdownTask;

import java.util.Hashtable;

/**
 * Created by verma on 4/11/16.
 */
public class MarketMakerShutdown implements ShutdownTask {
    private static final Log log = LogFactory.getLog(MarketMakerShutdown.class);

    /**
     * The method that is being called at system shutdown
     *
     * @param aName
     * @param args
     */
    @Override
    public String shutdown(String aName, Hashtable args) {
        if(MarketMakerServices.getTOBBasedCorePriceService() != null ){
            if(MarketMakerServices.getTOBBasedCorePriceService().stop()){
                MarketMakerServices.setTOBBasedCorePriceService(null);
            }
        }
        log.info("shutdown : Done");
        return null;
    }
}
