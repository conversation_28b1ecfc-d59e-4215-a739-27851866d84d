package com.integral.marketmaker.shutdown;

import com.integral.broker.marketmaker.BrokerMarketMakerUtil;
import com.integral.broker.quote.BrokerQuoteFactory;
import com.integral.broker.quote.QuoteHandlerC;
import com.integral.finance.dealing.Quote;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.marketmaker.broker.BrokerMetaData;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.service.coreprice.CorePriceService;
import com.integral.marketmaker.service.coreprice.CorePriceServiceFactory;
import com.integral.marketmaker.startup.broker.CorePriceMulticastListener;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.runtime.ShutdownTask;
import com.integral.user.Organization;

import java.util.Collections;
import java.util.Hashtable;
import java.util.List;
import java.util.Set;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 */
public class CorePriceServiceShutdown implements ShutdownTask {
    private static Log log = LogFactory.getLog(CorePriceServiceShutdown.class);

    @Override
    public String shutdown(String aName, Hashtable args) {
        List<String> marketMakerEnabledOrgs = MarketMakerConfig.getInstance().getMarketMakerEnabledBrokers();
        log.info("CPSS.shutdown:: Market Maker Configured Orgs: " + marketMakerEnabledOrgs);
        for(String brokerName : marketMakerEnabledOrgs) {
            Organization broker = ReferenceDataCacheC.getInstance().getOrganization(brokerName);
            CorePriceServiceFactory corePriceServiceFactory = CorePriceServiceFactory.getInstance();
            CorePriceService corePriceService = corePriceServiceFactory.getCorePriceService(broker,
                                                                        CorePriceServiceFactory.CorePriceServiceType.MM);
            corePriceService.stop();
        }
        return null;
    }

    public static void publishStateRate(Organization broker){
        String brokerName = broker.getShortName();
        try {
            List<String> mmBrokers = MarketMakerConfig.getInstance().getMarketMakerEnabledBrokers();
            if(!mmBrokers.contains(brokerName)){
                log.info("CorePriceServiceShutdown.publishStateRate:: not a MM broker " + brokerName);
                return;
            }
            BrokerMetaData brokerMetaData = MarketMakerFactory.getInstance().getCorePriceBrokerService().get(brokerName);
            if(brokerMetaData == null){
                log.info("CorePriceServiceShutdown.publishStateRate:: metadata not present for " + brokerName);
                return;
            }
            Set<String> currencyPairs = brokerMetaData.getCurrencyPairs();
            for(String cp : currencyPairs) {
                Quote staleQuote = BrokerMarketMakerUtil.createStaleQuote(broker, cp);
                QuoteHandlerC quoteHandler = (QuoteHandlerC) BrokerQuoteFactory.getInstance().getQuoteHandler(broker);
                quoteHandler.sendRate(staleQuote, Collections.emptyMap());
                log.info("CorePriceServiceShutdown.publishStateRate:: published stale rate for " + brokerName);
            }
        }catch (Exception e){
            log.warn("CorePriceServiceShutdown.publishStateRate:: failed to publish stale rate for " + brokerName, e);
        }
    }
}
