package com.integral.marketmaker.config;

import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.maker.service.broker.CorePriceBrokerService;
import com.integral.maker.service.corepricing.CorePricingService;
import com.integral.maker.service.provider.CorePriceProviderService;
import com.integral.maker.service.tierpricing.TierPricingService;
import com.integral.marketmaker.broker.BrokerMetaData;
import com.integral.marketmaker.corepricing.CorePricingMetaData;
import com.integral.marketmaker.provider.ProvidersMetaData;
import com.integral.marketmaker.tierpricing.TierPricingMetaData;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;

import java.util.HashSet;
import java.util.Set;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 8/4/16
 */
public class MarketMakerConfigC implements CorePriceConfig {
    private static Log log = LogFactory.getLog(MarketMakerConfigC.class);
    private final Organization organization;
    private final CorePriceBrokerService brokerService;
    private final CorePriceProviderService providerService;
    private final CorePricingService corePricingService;
    private final TierPricingService tierPricingService;

    public MarketMakerConfigC(Organization org){
        this.organization = org;
        MarketMakerFactory factory = MarketMakerFactory.getInstance();
        brokerService = factory.getCorePriceBrokerService();
        providerService = factory.getCorePriceProviderService();
        corePricingService = factory.getCorePricingService();
        tierPricingService = factory.getTierPricingService();
    }

    @Override
    public boolean isProviderPricesIncluded() {
        return true;
    }

    @Override
    public boolean isTierPricesPublished() {
        return true;
    }

    @Override
    public Set<String> getCurrencyPairs() {
        BrokerMetaData brokerMetaData = brokerService.get(organization.getShortName());
        if(brokerMetaData != null) {
            return brokerMetaData.getCurrencyPairs();
        }else {
            return new HashSet<String>();
        }
    }

    @Override
    public ProvidersMetaData getProviderList(CurrencyPair currencyPair) {
        return providerService.get(organization.getShortName(), currencyPair.getName());
    }

    @Override
    public CorePricingMetaData getCorePriceMetaData(CurrencyPair currencyPair) {
        return corePricingService.get(organization.getShortName(), currencyPair.getName());
    }

    @Override
    public TierPricingMetaData getTierPriceMetaData(CurrencyPair currencyPair) {
        return tierPricingService.get(organization.getShortName(), currencyPair.getName());
    }

    @Override
    public FXRateBasis getRateBasis(Organization org, CurrencyPair cp) {
        String quoteConName = MarketMakerConfig.getInstance().getQuoteConvention(org.getShortName());
        FXRateConvention quoteConv = (FXRateConvention) ReferenceDataCacheC.getInstance().getEntityByShortName(quoteConName, FXRateConvention.class, null, 'A');
        FXRateBasis rateBasis = quoteConv.getFXRateBasis(cp);
        return rateBasis;
    }

    @Override
    public int getPrecision(Organization org, CurrencyPair cp){
        FXRateBasis rateBasis = getRateBasis(org, cp);
        return rateBasis.getSpotPrecision();
    }

    @Override
    public boolean isPricingEnabled(CurrencyPair ccyPair){
        CorePricingMetaData corePricingMetaData = corePricingService.get(organization.getShortName(), ccyPair.getName());
        if(corePricingMetaData == null) {
            if(log.isDebugEnabled()) log.debug("MarketMakerConfigC.isPricingEnabled:: Core Price Configuration not exist " + organization.getShortName() + " " + ccyPair.getName());
            return false;
        }
        return corePricingMetaData.isPricingEnabled();
    }
}
