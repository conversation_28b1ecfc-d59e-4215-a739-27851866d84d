package com.integral.marketmaker.config;


import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import com.integral.broker.model.Product;
import com.integral.broker.model.Stream;
import com.integral.broker.notify.RemoteNotificationC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.is.common.ISConstantsC;
import com.integral.is.spaces.fx.esp.provision.ProvisionCache;
import com.integral.is.spaces.fx.esp.provision.RelationshipProvision;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.SubscriptionUtil;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.maker.service.pricing.MarketMakerPriceControlService;
import com.integral.marketmaker.rule.pricecontrol.CurrencyPair;
import com.integral.marketmaker.rule.pricecontrol.PersistentPriceControl;
import com.integral.marketmaker.rule.pricecontrol.PriceControl;
import com.integral.persistence.ExternalSystem;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;
import com.integral.rds.service.marketmaker.MarketMakerPersistenceService;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.user.Organization;

public class ClientChannelConfigCache implements NotificationObserver
{
    MarketMakerPriceControlService priceControlService = MarketMakerFactory.getInstance().getPriceControlService();
    private static ClientChannelConfigCache clientChannelConfigCache = new ClientChannelConfigCache();
    protected static Log log = LogFactory.getLog(ClientChannelConfigCache.class.getName());

    private ClientChannelConfigCache()
    {

    }

    public static ClientChannelConfigCache getInstance()
    {
        return clientChannelConfigCache;
    }

    public void notifyAdd(Notification notification)
    {
        notifyUpdate(notification);
    }

    public void notifyUpdate(Notification notification)
    {
        String namespace = notification.getEntityNameSpace();
        RemoteNotificationC.setBrokerPriceMakingUpdatedTime ( namespace, System.currentTimeMillis () );
        triggerChanges(namespace);
    }

    public void initialize()
    {
        try
        {
            log.info("ClientChannelConfigCache.initialize - Started Initialization of Market Maker Client Channel");
            Collection<Organization> organizations = RuntimeFactory.getServerRuntimeMBean().
                                                                                getVirtualServer().getOrganizations();
            log.info("ClientChannelConfigCache.initialize deployed orgs=" + organizations);
            Set<Organization> brokers = new HashSet<Organization>();
            for(Organization oa : organizations){
                Collection<Organization> relatedOrgs = oa.getRelatedOrganizations(ISConstantsC.FI_ORG_RELATIONSHIP);
                for(Organization lp : relatedOrgs){
                    if(lp.isBroker()) {
                        brokers.add(lp);
                    }
                }
            }
            registerRDSListeners(brokers);
            for(Organization org:brokers)
            {
                triggerChanges(org.getShortName());
            }
            log.info("ClientChannelConfigCache.initialize - Completed Initialization of Market Maker Client Channel");
        }
        catch(Exception e)
        {
            log.error("Failed to initialize MM ClientChannel config " +
                    "Rates may be shown to customer even if turned off", e);
        }

    }
    /**
     *
     * @param orgName
     */
    private void triggerChanges(String orgName)
    {
        try
        {
            PriceControl priceControl = priceControlService.query(orgName);
            log.info("ClientChannelConfigCache.priceControl change:: org=" + orgName + ", pc=" + priceControl);
            if( null!= priceControl){

                if( log.isDebugEnabled())
            {
                    log.debug("ClientChannelConfigCache.triggerChanges:Evaluating Price Control config " +
                            "(Client Channel) for Provider: " + orgName);
                }
                SubscriptionUtil.notifyPriceControlChanges(priceControl);
            }else{
                if(log.isDebugEnabled()) log.debug("ClientChannelConfigCache.triggerChanges:: updating "
                        + orgName + ", price control is null");
            }
        }
        catch(Exception e)
        {
            log.error("Failed to tigger subscription change for Price Control Change " +
                    "(Client Channel) for Org " + orgName, e);
        }

    }
    /**
     *  Check if client Channel is supported
     *
     * @param provider
     * @param customer
     * @param currencyPair
     * @return
     */
    public boolean isSupportedClientChannel(Organization provider,Organization customer,String currencyPair)
    {
        PriceControl priceControl = null;
        ExternalSystem externalSystem = customer.getClientChannel();
        if( null!=externalSystem) {

            try {
                priceControl = priceControlService.query(provider.getShortName());
            } catch (Exception e) {
                log.error("Failed to lookup Price Control config (Client Channel) for Provider: " + provider
                        + " ,Customer: " + customer + ", Currency Pair :" + currencyPair
                        + " , Customer Client Channel: " + customer.getClientChannel(), e);
            }

            if (provider.isBroker() && null != priceControl ) {
                if( log.isDebugEnabled())
        {
                   log.debug(" Evaluating Price Control config (Client Channel) for Provider: " + provider
                           + " ,Customer: " + customer + ", Currency Pair :" + currencyPair
                           + " , Customer Client Channel: " + customer.getClientChannel());
                }
            CurrencyPair currencyPairPC = priceControl.getCurrencyPair(currencyPair);
                if (null != currencyPairPC) {
                boolean clientChActive = currencyPairPC.isClientChannelEnabled(customer.getClientChannel().getShortName());
                if(clientChActive)
                {
                    clientChActive = checkForSyntheticCrossComponents(customer, provider, currencyPair, priceControl);
                }
                return clientChActive;
            }
        }
        }
        return true;
    }

    private boolean checkForSyntheticCrossComponents(Organization fi, Organization broker, String ccyPair, PriceControl priceControl)
    {
        try {
            com.integral.finance.currency.CurrencyPair cp = CurrencyFactory.getCurrencyPairFromString(ccyPair);
            RelationshipProvision relationshipProvision = ProvisionCache.getRelationshipProvision(fi.getDefaultDealingEntity(), broker, cp);
            String streamId = relationshipProvision.getStreamId();
            Stream stream = broker.getBrokerOrganizationFunction().getStream(streamId);
            Product product = stream.getProduct(cp, true);
            if(product != null && product.isSynthetic())
            {
                Product foreignCCYPProduct = product.getForeignCCYPProduct();
                Product localCCYPProduct = product.getLocalCCYPProduct();
                if (foreignCCYPProduct == null || localCCYPProduct == null) {
                    if(log.isDebugEnabled()) log.debug(".checkForSyntheticCrossComponents:: incorrect configuration, local or foreign products are null br=" + broker.getShortName() + ", cp=" + cp.getName() + ", fi=" + fi.getShortName());
                    return true;
                }
                CurrencyPair synthCpChannel = priceControl.getCurrencyPair(product.getCurrencyPair().getName());
                boolean synOn = synthCpChannel == null || synthCpChannel.isClientChannelEnabled(fi.getClientChannel().getShortName());
                CurrencyPair primaryCpChannel = priceControl.getCurrencyPair(foreignCCYPProduct.getCurrencyPair().getName());
                boolean primaryOn = primaryCpChannel == null || primaryCpChannel.isClientChannelEnabled(fi.getClientChannel().getShortName());
                CurrencyPair localCpChannel = priceControl.getCurrencyPair(localCCYPProduct.getCurrencyPair().getName());
                boolean localOn = localCpChannel == null || localCpChannel.isClientChannelEnabled(fi.getClientChannel().getShortName());
                boolean isActive = synOn && primaryOn && localOn;
                return isActive;
            }
        } catch (Exception e) {
            log.warn("Exception during checking client channel", e);
            return true;
        }
        return true;
    }

    /**
     *
     * @param providers
     */
    private void registerRDSListeners(Collection<Organization> providers)
    {
        MarketMakerPersistenceService persistenceService = MarketMakerFactory.getInstance().getPersistenceService();
        for(Organization provider:providers)
        {
            if(provider.isBroker())
            {
                persistenceService.addObserver(this, provider.getNamespace().getShortName(),
                                                PersistentPriceControl.class);
            }
        }
    }
}
