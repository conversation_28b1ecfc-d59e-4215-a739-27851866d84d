package com.integral.marketmaker.config;

import java.util.List;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 8/18/16
 */
public interface MarketMakerConfigMBean {
    String TOBBasedCorePriceServiceClass = "IDC.MM.TOBBasedCorePriceService.Class";
    String DynamicUserForTOBBasedCorePriceService = "IDC.MM.TOBBasedCorePriceService.DynamicUser";
    String MarketMakerServicesStartupEnabled = "IDC.MM.MarketMakerServicesStartupEnabled";
    String marketMakerEnabledBrokers = "Idc.MarketMaker.Enabled.Orgs";
    String multicastAddress = "Idc.MarketMaker.Multicast.Address";
    String tierPriceMulticastPort = "Idc.MarketMaker.Tier.Multicast.Port";
    String volatileFactorMulticastPort = "IDC.MarketMaker.Volatile.Multicast.Port";
    String corePriceMulticastPort = "Idc.MarketMaker.CorePrice.Multicast.Port";
    String brokerMinPublishInterval = "Idc.MarketMaker.Broker.Min.Publish.Interval";
    String brokerMulticastSocketTimeout = "Idc.MarketMaker.Broker.Multicast.SocketTimeout";
    String MARKET_MAKER_AGGREGATION_INTERVAL = "Idc.MarketMaker.Aggregation.Interval";
    String MARKET_MAKER_AGGREGATION_INTERVAL_PREFIX = MARKET_MAKER_AGGREGATION_INTERVAL + ".";
    String publishInterval = "Idc.MarketMaker.Publish.Interval";
    String forcePublishInterval = "Idc.MarketMaker.Force.Publish.Interval";
    String multicastMTU = "Idc.MarketMaker.Multicast.MTU";
    String quoteConvention = "Idc.MarketMaker.QuoteConvention";
    String IMAGELOCATION = "Idc.MarketMaker.Image.Location";
    String PRICEMAKINGSTATUS = "Idc.MarketMaker.VirtualServer.PriceMakingStatus";
    String PRICEMAKINGSTATUS_PREFIX = "Idc.MarketMaker.VirtualServer.PriceMakingStatus.";

    String MMVIRTUALSERVER_HEARTBEAT_INTVL = "Idc.MarketMaker.VirtualServer.HeartBeat.Interval";
    String RWProvisionListenerClass = "Idc.MarketMaker.RWProvision.Listener.Class";
    String YM_MODE = "Idc.MarketMaker.YM.Mode";
    String COVER_STRATEGY_CONTROL_ENABLED = "Idc.MarketMaker.CoverStrategy.Control.Enabled";
    String MARKET_MAKER_SKEW_ALLOWED_RANGE ="Idc.MarketMaker.Skew.Allowed.Range";
    String MARKET_MAKER_SKEW_ALLOWED_RANGE_PREFIX ="Idc.MarketMaker.Skew.Allowed.Range.";
    String MASTER_CONTROL_UI_ENABLED= "IDC.MarketMaker.MasterControl.UI.Enabled";
    String MASTER_CONTROL_UI_ENABLED_PREFIX= "IDC.MarketMaker.MasterControl.UI.Enabled.";
    String VOLATILITY_SERVICE_TIMEOUT = "Idc.MarketMaker.Volatility.Service.Timeout.Seconds";
    String MARKET_PULSE_ENABLED = "Idc.MarketMaker.MarketSignals.Enabled";
    String MARKET_MAKER_DYNAMIC_AGG_STRATEGY_ENABLED = "Idc.MarketMaker.Dynamic.Pricing.Mode.Enabled";
    String MARKET_MAKER_VOLATILITY_SERVICE_ENABLED = "Idc.MarketMaker.Volatility.Service.Enabled";
    String MARKET_SIGNALS_MULTICAST_PORT = "Idc.MarketSignals.Multicast.Port";
    enum YMMode {NO_YM, NO_HEDGING, NO_AUTO_HEDGING, FULL_YM}

    String JOIN_MARKET_MAKER_CLUSTER_ON_STARTUP = "Idc.MarketMaker.Join.Cluster.On.Startup";
    String MARKET_MAKER_CLUSTER_NAME = "Idc.MarketMaker.Cluster.Name";
    String SINGLE_LP_FAILOVER_ENABLED = "Idc.MarketMaker.Single.LP.Failover.Enabled";
    String LP_FAILOVER_PRIORITY = "Idc.MarketMaker.Single.LP.Failover.Priority";

    String getTobBasedCorePriceServiceClass();

    boolean isMarketMakerServicesStartupEnabled();

    List<String> getMarketMakerEnabledBrokers();

    String getMulticastAddress();

    int getTierPriceMulticastPort();

    int getVolatileFactorMulticastPort();

    int getCorePriceMulticastPort();

    int getBrokerMinPublishInterval();

    int getBrokerMulticastSocketTimeout();

    int getAggregationInterval(String brokerName);

    int getMulticastMTU();

    String getQuoteConvention(String brokerName);

    String getImageLocation();

    boolean isDynamicUserForTOBBasedCorePriceService();

    int getPublishInterval(String brokerName);

    int getForcePublishInterval(String brokerName);

    boolean evaluatePriceMakingProviderStatus(String broker);

    long getHeartBeatInterval();

    String getRWProvisionListenerClass();

    YMMode getYMMode(String brokerName);
    boolean isCoverStrategyControlEnabled(String brokerName);

    MarketMakerConfig.MMConfigChangeHandler getPropertyChangeHandler();

    void setPropertyChangeHandler(MarketMakerConfig.MMConfigChangeHandler changeHandler);
    Integer getMarketMakerSkewAllowedRange(String brokerName);

    boolean isMasterControlUIEnabled(String brokerName);
    int getVolatilityServiceTimeoutSeconds();
    boolean isMarketPulseEnabled(String brokerName);
    boolean isDynamicAggStrategyEnabled(String brokerName);
    boolean isVolatilityServiceEnabled(String brokerName);
    int getMarketSignalsMulticastPort();
    boolean isJoinMMClusterOnStartup();
    String getClusterName();
    public boolean isSingleLpFailoverEnabled(String brokerName, String ccyPair);
    public String[] getSingleLpFailoverPriority(String brokerName, String ccyPair);
}
