package com.integral.marketmaker.config;

import com.integral.cluster.ClusterConfigDataService;
import com.integral.is.common.mbean.PropertyChangeHandler;
import com.integral.marketmaker.startup.PropertyChangeListener;
import com.integral.model.cluster.ClusterMetaData;
import com.integral.model.cluster.ResourceInfo;
import com.integral.services.ServiceContainerMBean;
import com.integral.services.cluster.controller.ClusterControllerService;
import com.integral.system.configuration.IdcMBeanC;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by verma on 4/11/16.
 */
public class MarketMakerConfig extends IdcMBeanC implements MarketMakerConfigMBean {
    private static MarketMakerConfig current;

    private boolean marketMakerServicesStartupEnabled;
    private String tobBasedCorePriceServiceClass;
    private List<String> marketMakerEnabledBrokers; //comma separated list of broker names
    private String multicastAddress;
    private int tierPriceMulticastPort;
    private int volatileFactorMulticastPort;
    private int corePriceMulticastPort;
    private int brokerMinPublishInterval; //milliseconds
    private int brokerMulticastSocketTimeout; //milliseconds
    private int aggregationInterval; //milliseconds
    private Map<String, Integer> aggregationIntervalMap;
    private int multicastMTU;
    private String quoteConvention;
    private Map<String, String> quoteConventionMap;
    private String imageLocation;
    private boolean dynamicUserForTOBBasedCorePriceService;
    private int publishInterval;
    private Map<String, Integer> publishIntervalMap;
    private int forcePubInterval;
    private Map<String, Integer> forcePubIntervalMap;
    private boolean evaluatePriceMakingProviderStatus;
    private Map<String, Boolean> evaluatePriceMakingProviderStatusMap;
    private long mmVirtualServerHBInterval;
    private String rwProvisionListenerClass;
    private YMMode defaultYMMode = YMMode.FULL_YM;
    private Map<String, YMMode> ymModeMap;
    private MMConfigChangeHandler changeHandler = new MMConfigChangeHandler();
    private boolean coverStrategyControlEnabled;
    private Map<String, Boolean> coverStrategyControlEnabledMap;
    private Integer marketMakerSkewAllowedRange;
    private Map<String, Integer> marketMakerSkewAllowedRangeMap;
    private Boolean masterControlUIEnabled;
    private Map<String, Boolean> masterControlUIEnabledMap;
    private int volatilityServiceTimeoutSeconds;
    private boolean marketPulseEnabled;
    private Map<String, Boolean> marketPulseEnabledMap;
    private boolean dynamicAggStrategyEnabled;
    private Map<String, Boolean> dynamicAggStrategyEnabledMap;
    private boolean volatilityServiceEnabled;
    private Map<String, Boolean> volatilityServiceEnabledMap;
    private int marketSignalsMulticastPort;
    private boolean isJoinMMClusterOnStartup;
    private String clusterName;
    private boolean singleLpFailoverEnabled;
    private Map<String, Boolean> singleLpFailoverEnabledMap;
    private Map<String, String[]> singleLpFailoverPriorityMap;

    public static final String DEFAULT_MARKET_MAKER_CLUSTER_NAME="MarketMakerCluster";

    static {
        current = new MarketMakerConfig("com.integral.marketmaker.config.MarketMakerConfig");
        current.loadProperties();
    }

    public static MarketMakerConfig getInstance(){
        return current;
    }

    public MarketMakerConfig(String propFile) {
        super(propFile);
    }

    @Override
    public void initialize(){
        super.initialize();
        loadProperties();
    }

    private void loadProperties(){
        this.marketMakerServicesStartupEnabled = getBooleanProperty(MarketMakerServicesStartupEnabled, false);
        this.tobBasedCorePriceServiceClass = getStringProperty(TOBBasedCorePriceServiceClass, "com.integral.marketmaker.service.coreprice.TOBBasedCorePriceServiceC");
        this.dynamicUserForTOBBasedCorePriceService = getBooleanProperty(DynamicUserForTOBBasedCorePriceService,true);
        List<String> temp = initCommaSperatedPropertyList(MarketMakerConfigMBean.marketMakerEnabledBrokers, null);
        marketMakerEnabledBrokers = Collections.unmodifiableList(temp);
        multicastAddress = getStringProperty(MarketMakerConfigMBean.multicastAddress, "*********");

        volatileFactorMulticastPort = getIntProperty(MarketMakerConfigMBean.volatileFactorMulticastPort, 3335);
        tierPriceMulticastPort = getIntProperty(MarketMakerConfigMBean.tierPriceMulticastPort, 3333);
        corePriceMulticastPort = getIntProperty(MarketMakerConfigMBean.corePriceMulticastPort, 3332);
        brokerMinPublishInterval = getIntProperty(MarketMakerConfigMBean.brokerMinPublishInterval, 50);
        brokerMulticastSocketTimeout = getIntProperty(MarketMakerConfigMBean.brokerMulticastSocketTimeout, 5000);
        aggregationInterval = getIntProperty(MARKET_MAKER_AGGREGATION_INTERVAL, 1000);
        aggregationIntervalMap = initSingleSuffixIntegerPropertyMap(MARKET_MAKER_AGGREGATION_INTERVAL_PREFIX, MARKET_MAKER_AGGREGATION_INTERVAL);
        multicastMTU = getIntProperty(MarketMakerConfigMBean.multicastMTU, 1400);
        quoteConvention = getStringProperty(MarketMakerConfigMBean.quoteConvention, "STDQOTCNV");
        quoteConventionMap = initSingleSuffixStringPropertyMap(MarketMakerConfigMBean.quoteConvention + ".", null);
        imageLocation = getStringProperty(IMAGELOCATION,"/fxi/resources/marketmaker/MMLogos.zip");
        publishInterval = getIntProperty(MarketMakerConfigMBean.publishInterval, 500);
        publishIntervalMap = initSingleSuffixIntegerPropertyMap(MarketMakerConfigMBean.publishInterval + ".", null);
        forcePubInterval = getIntProperty(MarketMakerConfigMBean.forcePublishInterval, 10000);
        forcePubIntervalMap = initSingleSuffixIntegerPropertyMap(MarketMakerConfigMBean.forcePublishInterval + ".", null);
        evaluatePriceMakingProviderStatus  = getBooleanProperty(PRICEMAKINGSTATUS,false);
        evaluatePriceMakingProviderStatusMap = initSingleSuffixBooleanPropertyMap(PRICEMAKINGSTATUS_PREFIX,null);
        mmVirtualServerHBInterval = getLongProperty(MMVIRTUALSERVER_HEARTBEAT_INTVL,5000L);
        rwProvisionListenerClass = getStringProperty(RWProvisionListenerClass, "com.integral.marketmaker.RWProvisionListenerForMM");
        loadYMode();
        coverStrategyControlEnabled = getBooleanProperty(COVER_STRATEGY_CONTROL_ENABLED, false);
        coverStrategyControlEnabledMap = initSingleSuffixBooleanPropertyMap(COVER_STRATEGY_CONTROL_ENABLED + '.', null);

        marketMakerSkewAllowedRange = getIntProperty(MARKET_MAKER_SKEW_ALLOWED_RANGE, null);
        marketMakerSkewAllowedRangeMap = initSingleSuffixIntegerPropertyMap(MARKET_MAKER_SKEW_ALLOWED_RANGE_PREFIX, null);

        masterControlUIEnabled = getBooleanProperty(MASTER_CONTROL_UI_ENABLED, false);
        masterControlUIEnabledMap = initSingleSuffixBooleanPropertyMap(MASTER_CONTROL_UI_ENABLED_PREFIX, null);
        volatilityServiceTimeoutSeconds = getIntProperty(VOLATILITY_SERVICE_TIMEOUT, 60);
        marketPulseEnabled = getBooleanProperty(MARKET_PULSE_ENABLED, false);
        marketPulseEnabledMap = initSingleSuffixBooleanPropertyMap(MARKET_PULSE_ENABLED + '.', null);
        dynamicAggStrategyEnabled = getBooleanProperty(MARKET_MAKER_DYNAMIC_AGG_STRATEGY_ENABLED, false);
        dynamicAggStrategyEnabledMap = initSingleSuffixBooleanPropertyMap(MARKET_MAKER_DYNAMIC_AGG_STRATEGY_ENABLED + '.', null);
        volatilityServiceEnabled = getBooleanProperty(MARKET_MAKER_VOLATILITY_SERVICE_ENABLED, false);
        volatilityServiceEnabledMap = initSingleSuffixBooleanPropertyMap(MARKET_MAKER_VOLATILITY_SERVICE_ENABLED + '.', null);
        marketSignalsMulticastPort = getIntProperty(MARKET_SIGNALS_MULTICAST_PORT, 47924);
        isJoinMMClusterOnStartup = getBooleanProperty(JOIN_MARKET_MAKER_CLUSTER_ON_STARTUP, false);
        clusterName = getStringProperty(MARKET_MAKER_CLUSTER_NAME,DEFAULT_MARKET_MAKER_CLUSTER_NAME);
        singleLpFailoverEnabled = getBooleanProperty(SINGLE_LP_FAILOVER_ENABLED, false);
        singleLpFailoverEnabledMap = initMultipleSuffixBooleanPropertyMap(SINGLE_LP_FAILOVER_ENABLED + '.', null, singleLpFailoverEnabled);
        Map<String, String> tempMap = initMultipleSuffixStringPropertyMap(LP_FAILOVER_PRIORITY + '.', null);getStringListProperty(LP_FAILOVER_PRIORITY, null);
        singleLpFailoverPriorityMap = toStringListMap(tempMap);
    }
    private Map<String, String[]> toStringListMap(Map<String, String> values) {
        Map<String, String[]> map = new HashMap<String, String[]>();
        for(Map.Entry<String, String> entry : values.entrySet()){
            if(entry.getValue() == null || entry.getValue().isEmpty()) continue;
            map.put(entry.getKey(), entry.getValue().split(","));
        }
        return map;
    }

    private void loadYMode(){
        Map<String, String> tempMap = initSingleSuffixStringPropertyMap(YM_MODE + '.', null);
        Map<String, YMMode> tempEnumMap = new HashMap<String, YMMode>();
        for(Map.Entry<String, String> entry: tempMap.entrySet()){
            String key = entry.getKey();
            String value = entry.getValue();
            try{
                YMMode mode = YMMode.valueOf(entry.getValue());
                tempEnumMap.put(key, mode);
            }catch (IllegalArgumentException e){
                log.error(".loadProperites: invalid value; prop=" + key + ", value=" + value);
            }
        }
        ymModeMap = tempEnumMap;
    }

    @Override
    public void setProperty( String key, String aValue, int scope, String oldValue ){
        super.setProperty(key, aValue, scope, oldValue);

        //callbacks for specific property changes
        if(key.equals(MarketMakerConfigMBean.marketMakerEnabledBrokers) ||
                key.startsWith(MarketMakerConfigMBean.YM_MODE)) {
            PropertyChangeListener.updateServices(oldValue, aValue, key);
        }
        getPropertyChangeHandler().handle(key, aValue, oldValue);
    }

    public String getTobBasedCorePriceServiceClass()     {
        return tobBasedCorePriceServiceClass;
    }

    public boolean isMarketMakerServicesStartupEnabled() {
        return marketMakerServicesStartupEnabled;
    }

    public List<String> getMarketMakerEnabledBrokers(){
        return marketMakerEnabledBrokers;
    }

    public String getMulticastAddress() {
        return multicastAddress;
    }

    public int getTierPriceMulticastPort() {
        return tierPriceMulticastPort;
    }

    public int getVolatileFactorMulticastPort(){
        return volatileFactorMulticastPort;
    }

    public int getCorePriceMulticastPort() {
        return corePriceMulticastPort;
    }

    public int getBrokerMinPublishInterval() {
        return brokerMinPublishInterval;
    }

    public int getBrokerMulticastSocketTimeout() {
        return brokerMulticastSocketTimeout;
    }

    public int getAggregationInterval(String brokerName) {
        if(null!=brokerName && brokerName.length()>0){
            Integer interval = aggregationIntervalMap.get(brokerName);
            if(null!=interval){
                return interval;
            }
        }
        return aggregationInterval;
    }

    public int getMulticastMTU() {
        return multicastMTU;
    }

    public String getQuoteConvention(String brokerName) {
        if(brokerName != null) {
            String value = quoteConventionMap.get(brokerName);
            if(value != null) return value;
        }
        return quoteConvention;
    }

    public String getImageLocation()
    {
        return imageLocation;
    }

    public boolean isDynamicUserForTOBBasedCorePriceService() {
        return dynamicUserForTOBBasedCorePriceService;
    }

    @Override
    public int getPublishInterval(String brokerName){
        if(brokerName != null){
            Integer value = publishIntervalMap.get(brokerName);
            if(value != null) return value;
        }
        return publishInterval;
    }

    @Override
    public int getForcePublishInterval(String brokerName){
        if(brokerName != null){
            Integer value = forcePubIntervalMap.get(brokerName);
            if(value != null) return value;
        }
        return forcePubInterval;
    }

    public boolean evaluatePriceMakingProviderStatus(String broker){
        Boolean result = evaluatePriceMakingProviderStatusMap.get(broker);
        return null==result ? evaluatePriceMakingProviderStatus:result;
    }

    @Override
    public Integer getMarketMakerSkewAllowedRange(String broker)
    {
        if(broker != null) {
            Integer result = marketMakerSkewAllowedRangeMap.get(broker);
            if(result != null){
                return result;
            }
        }
        return  marketMakerSkewAllowedRange;
    }

    @Override
    public boolean isMasterControlUIEnabled(String broker){
        if(broker != null) {
            Boolean result = masterControlUIEnabledMap.get(broker);
            if(result != null){
                return result;
            }
        }
        return masterControlUIEnabled;
    }

    public long getHeartBeatInterval(){
        return mmVirtualServerHBInterval;
    }

    @Override
    public String getRWProvisionListenerClass(){
        return rwProvisionListenerClass;
    }

    @Override
    public YMMode getYMMode(String brokerName) {
        if(brokerName != null){
            YMMode value = ymModeMap.get(brokerName);
            if(value != null){
                return value;
            }
        }
        return defaultYMMode;
    }

    @Override
    public boolean isCoverStrategyControlEnabled(String brokerName){
        if(brokerName != null){
            Boolean value = coverStrategyControlEnabledMap.get(brokerName);
            if(value != null){
                return value;
            }
        }
        return coverStrategyControlEnabled;
    }

    @Override
    public MMConfigChangeHandler getPropertyChangeHandler(){
        return changeHandler;
    }

    @Override
    public void setPropertyChangeHandler(MMConfigChangeHandler changeHandler) {
        this.changeHandler = changeHandler;
    }

    @Override
    public int getVolatilityServiceTimeoutSeconds(){
        return volatilityServiceTimeoutSeconds;
    }

    @Override
    public boolean isMarketPulseEnabled(String brokerName){
        if(brokerName != null){
            Boolean value = marketPulseEnabledMap.get(brokerName);
            if(value != null){
                return value;
            }
        }
        return marketPulseEnabled;
    }
    @Override
    public boolean isDynamicAggStrategyEnabled(String brokerName){
        if(brokerName != null){
            Boolean value = dynamicAggStrategyEnabledMap.get(brokerName);
            if(value != null){
                return value;
            }
        }
        return dynamicAggStrategyEnabled;
    }

    @Override
    public boolean isVolatilityServiceEnabled(String brokerName){
        if(brokerName != null){
            Boolean value = volatilityServiceEnabledMap.get(brokerName);
            if(value != null){
                return value;
            }
        }
        return volatilityServiceEnabled;
    }

    @Override
    public int getMarketSignalsMulticastPort(){
        return marketSignalsMulticastPort;
    }

    @Override
    public boolean isJoinMMClusterOnStartup() {
        return isJoinMMClusterOnStartup;
    }

    @Override
    public String getClusterName() {
        return clusterName;
    }

    @Override
    public boolean isSingleLpFailoverEnabled(String brokerName, String ccyPair){
        if(brokerName != null && ccyPair != null){
            Boolean value = singleLpFailoverEnabledMap.get(brokerName + '.' + ccyPair);
            if(value != null){
                return value;
            }
        }
        if(brokerName != null){
            Boolean value = singleLpFailoverEnabledMap.get(brokerName);
            if(value != null){
                return value;
            }
        }
        return singleLpFailoverEnabled;
    }
    @Override
    public String[] getSingleLpFailoverPriority(String brokerName, String ccyPair){
        if(brokerName != null && ccyPair != null){
            String[] value = singleLpFailoverPriorityMap.get(brokerName + '.' + ccyPair);
            if(value != null){
                return value;
            }
        }
        if(brokerName != null){
            String[] value = singleLpFailoverPriorityMap.get(brokerName);
            if(value != null){
                return value;
            }
        }
        return null;
    }

    public static class MMConfigChangeHandler implements PropertyChangeHandler{
        private final Set<MMConfigChangeObserver> observers = Collections.newSetFromMap(new ConcurrentHashMap<MMConfigChangeObserver, Boolean>());

        public void registerObserver(MMConfigChangeObserver observer){
            observers.add(observer);
        }

        public void removeObserver(MMConfigChangeObserver observer){
            observers.remove(observer);
        }

        @Override
        public void handle(String key, String newValue, String oldValue) {
            for(MMConfigChangeObserver observer : observers){
                try {
                    observer.handlePropertyChange(key, newValue, oldValue);
                }catch (Exception e){
                    log.warn(".handle: exception during callback prop=" + key + ", hander=" + observer.getClass().getName(), e);
                }
            }
        }
    }

    //The class shall be instanciated by the cluster rebalancer
    public static class MarketMakerEnabledOrgChangeHandler extends MMConfigChangeHandler{

        public static final String NAMESPACE_DELIMITTER = "-";

        @Override
        public void handle(String key, String newValue, String oldValue) {
            super.handle(key,newValue,oldValue);

            //The cluster controller shall rebalance the cluster on change of mm enabled organization list
            if(key.equals(MarketMakerConfigMBean.marketMakerEnabledBrokers)){
                ClusterConfigDataService configDataService = ClusterConfigDataService.getInstance();
                String namespace = ServiceContainerMBean.getInstance().getServicesNamespace();
                String clusterName = MarketMakerConfig.getInstance().getClusterName();
                String nameSpacedClusterName = namespace + NAMESPACE_DELIMITTER + clusterName;

                ClusterMetaData clusterMetaData = configDataService.getClusterMetaData(ClusterMetaData.NAMESPACE, nameSpacedClusterName);

                if(clusterMetaData==null){
                    log.error("MarketMakerEnabledOrgChangeHandler.handle():Failed to rebalance cluster. No cluster metadata present for the id:"+nameSpacedClusterName);
                    return;
                }

                List<ResourceInfo> resources = clusterMetaData.getResources();
                if(!resources.isEmpty()){
                    ResourceInfo resourceInfo = resources.get(0);
                    boolean result = ClusterControllerService.getInstance().reBalanceCluster(clusterMetaData.getNameSpacedClusterName(), resourceInfo.getName(),
                            resourceInfo.getPayloadInfo().getReplicas());
                    log.info("MarketMakerEnabledOrgChangeHandler.handle():Re-balancing cluster :" +clusterMetaData.getNameSpacedClusterName()+",resource:"+resourceInfo.getName()+",result:"+result);
                }else{
                    log.info("MarketMakerEnabledOrgChangeHandler.handle():No resource present in cluster metadata:" + clusterMetaData);
                }
            }
        }
    }
}
