package com.integral.marketmaker.config;

import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.fx.FXRateBasis;
import com.integral.marketmaker.corepricing.CorePricingMetaData;
import com.integral.marketmaker.provider.ProvidersMetaData;
import com.integral.marketmaker.tierpricing.TierPricingMetaData;
import com.integral.user.Organization;

import java.util.Set;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 8/4/16
 */
public interface CorePriceConfig {
    boolean isProviderPricesIncluded();

    boolean isTierPricesPublished();

    Set<String> getCurrencyPairs();

    ProvidersMetaData getProviderList(CurrencyPair currencyPair);

    CorePricingMetaData getCorePriceMetaData(CurrencyPair currencyPair);

    TierPricingMetaData getTierPriceMetaData(CurrencyPair currencyPair);

    int getPrecision(Organization org, CurrencyPair currencyPair);

    FXRateBasis getRateBasis(Organization org, CurrencyPair currencyPair);

    boolean isPricingEnabled(CurrencyPair ccyPair);
}
