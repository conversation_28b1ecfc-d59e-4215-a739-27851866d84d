package com.integral.marketmaker.startup;

import com.integral.marketmaker.MarketMakerThreadPoolFactory;
import com.integral.marketmaker.service.coreprice.CorePricePublisherC;
import com.integral.marketmaker.service.coreprice.CorePriceServiceFactory;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 7/10/17
 */
public class YMStatusMonitor implements Runnable {
    private static ScheduledFuture<?> scheduledFuture;

    private void updateStatus(){
        List<CorePricePublisherC> publishers = CorePriceServiceFactory.getInstance().getPublishers();
        for(CorePricePublisherC publisher : publishers){
            publisher.updateYMStatus();
        }
    }

    public static synchronized void scheduleMonitor(){
        ScheduledExecutorService scheduledExecutor = Executors.newSingleThreadScheduledExecutor(new MarketMakerThreadPoolFactory.MarketMakerThreadFactory("MMMonitorThread"));
        if(scheduledFuture != null){
            scheduledFuture.cancel(false);
        }
        scheduledFuture = scheduledExecutor.scheduleWithFixedDelay(new YMStatusMonitor(), 30, 30, TimeUnit.SECONDS);
    }

    @Override
    public void run() {
        updateStatus();
    }
}
