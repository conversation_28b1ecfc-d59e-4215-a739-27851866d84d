package com.integral.marketmaker.startup;


import com.integral.broker.skew.SkewService;
import com.integral.broker.skew.SkewServiceFactory;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.maker.service.corepricing.CorePricingDataService;
import com.integral.marketmaker.broker.BrokerMetaData;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.corepricing.AggregationStrategy;
import com.integral.marketmaker.corepricing.CorePricingMetaData;
import com.integral.marketmaker.corepricing.Mode;
import com.integral.marketmaker.service.coreprice.CorePricePublisherC;
import com.integral.marketmaker.service.coreprice.CorePriceServiceFactory;
import com.integral.math.MathUtil;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;
import com.integral.user.Organization;

import java.math.BigDecimal;
import java.util.Set;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 2/2/17
 */
public class CorePriceChangeListener implements NotificationObserver {
    private static final Log log = LogFactory.getLog(CorePriceChangeListener.class);
    CorePricingDataService corePricingDataService = MarketMakerFactory.getInstance().getCorePricingService().getCorePricingDataService();
    private static final CorePriceChangeListener instance = new CorePriceChangeListener();

    public static CorePriceChangeListener getInstance(){
        return instance;
    }

    protected CorePriceChangeListener(){

    }

    @Override
    public void notifyAdd(Notification notification) {
        notify(notification);
    }

    @Override
    public void notifyUpdate(Notification notification) {
        notify(notification);
    }

    private void notify(Notification notification){
        String orgName = notification.getEntityNameSpace();
        String entityId = notification.getEntityId();
        CorePricingMetaData metaData = corePricingDataService.getCorePricingMetaDataById(orgName, entityId);
        if(metaData == null){
            log.warn("CorePriceChangeListener.notify:: entity not found org=" + orgName + ", id=" + entityId);
            return;
        }
        Organization organization = ReferenceDataCacheC.getInstance().getOrganization(orgName);
        if(organization == null){
            log.warn(".notify:: org not exist " + orgName);
            return;
        }
        String ccyPair = metaData.getCcyPair();
        notify(organization, ccyPair, metaData);
    }

    protected void notify(Organization organization, String ccyPair, CorePricingMetaData metaData){
        String orgName = organization.getShortName();
        updateCorePricePublisher(orgName, ccyPair, metaData);
        updateSkewService(orgName, ccyPair, metaData);
    }

    private void updateSkewService(String orgName, String ccyPair, CorePricingMetaData metaData){
        Mode skewMode = metaData.getSkewMode();
        boolean newValue = skewMode != null && skewMode.equals(Mode.MANUAL);
        double skew = metaData.getSkew();
        skew = convertToAbsolute(orgName, ccyPair, skew);
        boolean isAsymmSkew = metaData.isAsymmSkew();
        SkewService skewService = SkewServiceFactory.getSkewService(orgName);
        skewService.setManual(ccyPair, newValue);
        if(isAsymmSkew) {
        	double bidSkew = convertToAbsolute(orgName, ccyPair, metaData.getBidSkew());
        	double offerSkew = convertToAbsolute(orgName, ccyPair, metaData.getOfferSkew());
        	skewService.updateAsymmSkewManual(ccyPair, bidSkew, offerSkew);
		}else {
			skewService.updateSkewManual(ccyPair, skew);
		}
    }

    protected void updateCorePricePublisher(String orgName, String ccyPair, CorePricingMetaData corePricingMetaData){
        CorePricePublisherC publisher = CorePriceServiceFactory.getInstance().findPublisher(orgName, ccyPair);
        if(publisher != null) {
            AggregationStrategy strategy = corePricingMetaData.getAggregationStrategy();
            publisher.updateAggregationChange(strategy.equals(AggregationStrategy.BestPriceMidRate), corePricingMetaData.getAggregationStrgyMode());
            publisher.setConfigChange(true);
            log.info("CorePriceChangeListener.updateCorePricePublisher:: org=" + orgName + ", cp=" + ccyPair + ", strgy=" + strategy);
        }else {
            log.info("CorePriceChangeListener.updateCorePricePublisher:: no publisher, org=" + orgName + ", cp=" + ccyPair);
        }
    }

    public static void registerWithRDS(String orgName, CorePriceChangeListener corePriceChangeListener){
        MarketMakerFactory.getInstance().getCorePricingService().getCorePricingDataService().registerForMetaDataChange(orgName, corePriceChangeListener);
    }

    public static void initSkewMode(String orgName){
        BrokerMetaData brokerMetaData = MarketMakerFactory.getInstance().getCorePriceBrokerService().get(orgName);
        if(brokerMetaData != null){
            Set<String> currencyPairs = brokerMetaData.getCurrencyPairs();
            for(String ccyPair : currencyPairs){
                CorePricingMetaData corePricingMetaData = MarketMakerFactory.getInstance().getCorePricingService().get(orgName, ccyPair);
                Mode skewMode = corePricingMetaData.getSkewMode();
                boolean isAsymmSkew = corePricingMetaData.isAsymmSkew();
                double skew = corePricingMetaData.getSkew();
                skew = convertToAbsolute(orgName, ccyPair, skew);
                double bidSkew = corePricingMetaData.getBidSkew();
                bidSkew = convertToAbsolute(orgName, ccyPair, bidSkew);
                double offerSkew = corePricingMetaData.getOfferSkew();
                offerSkew = convertToAbsolute(orgName, ccyPair, offerSkew);
                boolean skewManual = skewMode != null && skewMode.equals(Mode.MANUAL);
				SkewService skewService = SkewServiceFactory.getSkewService(orgName);
				skewService.setManual(ccyPair, skewManual);
                if(isAsymmSkew){
                	skewService.updateAsymmSkewManual(ccyPair, bidSkew, offerSkew);
				}else {
                	skewService.updateSkewManual(ccyPair, skew);
				}
                log.info(".initializeSkew::bk=" + orgName + ",cp=" + ccyPair + " manual=" + skewManual + ",IsAsym=" + isAsymmSkew + 
                " value=" + skew + ",bidSk=" + bidSkew + ",offerSk=" + offerSkew);
            }
        }
    }

    public static double convertToAbsolute(String orgName, String cpName, double skew){
        ReferenceDataCacheC referenceDataCache = ReferenceDataCacheC.getInstance();
        Organization org = referenceDataCache.getOrganization(orgName);
        CurrencyPair cp = CurrencyFactory.getCurrencyPairFromString(cpName);
        if(org == null || cp == null){
            log.warn("CorePriceChangeListener.convertToAbsolute:: Org or ccy pair is null for " + orgName + " " + cpName);
            return 0;
        }
        String quoteConName = MarketMakerConfig.getInstance().getQuoteConvention(org.getShortName());
        FXRateConvention quoteConv = (FXRateConvention) referenceDataCache.getEntityByShortName(quoteConName, FXRateConvention.class, null, 'A');
        FXRateBasis rateBasis = null;
        if(quoteConv != null) rateBasis = quoteConv.getFXRateBasis(cp);
        if(rateBasis == null){
            log.warn("CorePriceChangeListener.convertToAbsolute:: ratebasis is null for " + orgName + " " + cpName);
            return 0;
        }
        int precision = rateBasis.getSpotPrecision();
        double pipsFactor = rateBasis.getPipsFactor();
        skew = skew / pipsFactor;
        skew = MathUtil.round(skew, precision, BigDecimal.ROUND_HALF_EVEN);
        return skew;
    }
}
