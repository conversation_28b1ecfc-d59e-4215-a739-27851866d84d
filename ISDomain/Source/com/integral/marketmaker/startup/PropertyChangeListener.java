package com.integral.marketmaker.startup;

import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.broker.config.BrokerConfigurationServiceFactory;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ServiceProvidersMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.MarketMakerThreadPoolFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.config.MarketMakerConfigMBean;
import com.integral.marketmaker.service.coreprice.CorePricePublisherC;
import com.integral.marketmaker.service.coreprice.CorePriceService;
import com.integral.marketmaker.service.coreprice.CorePriceServiceFactory;
import com.integral.marketmaker.service.coreprice.MMCorePriceServiceC;
import com.integral.marketmaker.shutdown.MarketMakerServerShutdownC;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.server.VirtualServer;
import com.integral.system.server.VirtualServerType;
import com.integral.user.Organization;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 2/23/17
 */
public class PropertyChangeListener implements Runnable {
    private static final Log log = LogFactory.getLog(ProviderListChangeListener.class);
    private final String oldValue;
    private final String newValue;
    private final String key;
    private static ExecutorService executorService = Executors.newSingleThreadExecutor(new MarketMakerThreadPoolFactory.MarketMakerThreadFactory("CorePriceServiceStarter"));

    public PropertyChangeListener(String oldList, String newList, String key){
        this.oldValue = oldList;
        this.newValue = newList;
        this.key = key;
    }

    @Override
    public void run(){
        if(key.startsWith(MarketMakerConfigMBean.marketMakerEnabledBrokers)){
            handleMakerMakerEnabledOrgsChange();
        }else if(key.startsWith(MarketMakerConfigMBean.YM_MODE)){
            handleYMModeChange();
        }else {
            log.warn(".propertyChange: Unknown property chantge " + key);
        }
    }

    private void handleMakerMakerEnabledOrgsChange(){
        String[] oldList = oldValue.split(",");
        String[] newList = newValue.split(",");
        Set<String> oldOrgs = new HashSet<String>(Arrays.asList(oldList));
        Set<String> newOrgs = new HashSet<String>(Arrays.asList(newList));

        //stopping MM service for the removed orgs
        for(String org : oldOrgs){
            if(!newOrgs.contains(org)){
                stopMarketMakerServices(org);
            }
        }

        //starting MM service for added orgs
        for(String org : newOrgs){
            if(!oldOrgs.contains(org)){
                startMarketMakerServices(org);
            }
        }

        log.info("PropertyChangeListener.run:: Completed");
    }

    private void handleYMModeChange(){
        int index = key.lastIndexOf('.');
        String brokerName = key.substring(index+1);
        List<CorePricePublisherC> publishers = CorePriceServiceFactory.getInstance().getCorePricePublishers(brokerName);
        for(CorePricePublisherC publisher : publishers){
            publisher.updateYMMode();
        }
    }

    private void startMarketMakerServices(String orgName){
        Organization org = OrganizationUtil.getOrganization(orgName);
        if(org == null){
            log.info("PropertyChangeListener.startMarketMakerServices:: org not exist " + orgName);
            return;
        }
        ServiceProvidersMBean serviceProvidersMBean = ISFactory.getInstance().getServicProvidersMBean();
        serviceProvidersMBean.onNotification();
        log.info("PropertyChangeListener.startMarketMakerServices:: refreshed provider deployment " + orgName);
        new MarketMakerServerStartupC().startCorePriceServices(orgName);
        log.info("PropertyChangeListener.startMarketMakerServices:: started core price services" + orgName);
    }

    private void stopMarketMakerServices(String orgName){
        Organization org = OrganizationUtil.getOrganization(orgName);
        if(org == null){
            log.info("PropertyChangeListener.stopMarketMakerServices:: org not exist " + orgName);
            return;
        }

        new MarketMakerServerShutdownC().stopCorePriceServices(orgName);
        log.info("PropertyChangeListener.stopMarketMakerServices:: stopped " + orgName);
    }

    public static void updateServices(String oldValue, String newValue, String key){
        if(oldValue == null) oldValue = "";
        if(newValue == null) newValue = "";
        VirtualServer thisVS = RuntimeFactory.getServerRuntimeMBean().getVirtualServer();
        if(thisVS.getVirtualServerType().getShortName().equals(VirtualServerType.MarketMakerServer)){
            if(key.startsWith(MarketMakerConfigMBean.marketMakerEnabledBrokers)
            && MarketMakerConfig.getInstance().isJoinMMClusterOnStartup()){
                log.info("PropertyChangeListener.updateServices:: Skipping services update. MM Server running in cluster mode");
            }else{
                executorService.submit(new PropertyChangeListener(oldValue, newValue, key));
                log.info("PropertyChangeListener.updateServices:: submitted in New Thread");
            }
        }else if(thisVS.getVirtualServerType().getShortName().equals(VirtualServerType.BrokerAdaptor)) {
            Collection<String> deployedBrokers = BrokerConfigurationServiceFactory.getBrokerConfigurationService().getDeployedBrokerOrganizationNames();
            List<String> oldOrgs = Arrays.asList(oldValue.split(","));
            List<String> newOrgs = Arrays.asList(newValue.split(","));
            List<String> temp = new ArrayList<String>(newOrgs);
            temp.addAll(oldOrgs);
            temp.retainAll(deployedBrokers);
            boolean needUpdate = !temp.isEmpty();
            if(needUpdate) {
                executorService.submit(new MarketMakerBrokerServiceStarter(oldOrgs, newOrgs));
                log.info("PropertyChangeListener.updateServices:: submitted in New Thread");
            }
        } else {
            log.info("PropertyChangeListener.updateServices:: Not a MM Virtual Server");
        }
    }
}

class MarketMakerBrokerServiceStarter implements Runnable{
    private List<String> oldOrgs;
    private List<String> newOrgs;

    public MarketMakerBrokerServiceStarter(List<String> oldList, List<String> newList){
        this.oldOrgs = oldList;
        this.newOrgs = newList;
    }

    @Override
    public void run() {
        List<String> toBeStarted = new ArrayList<String>(newOrgs);
        toBeStarted.removeAll(oldOrgs);
        for(String broker : toBeStarted){
            MarketMakerServerStartupC.startMMServicesForBroker(broker);
        }
    }
}
