package com.integral.marketmaker.startup;

import com.integral.broker.admin.AdminFactory;
import com.integral.broker.admin.ConfigurationService;
import com.integral.broker.model.Configuration;
import com.integral.broker.model.Stream;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.MarketMakerServiceProvidersMBeanC;
import com.integral.is.common.mbean.ServiceProvidersMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.provider.ProvidersMetaData;
import com.integral.marketmaker.service.coreprice.*;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;
import com.integral.user.Organization;

import java.util.*;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 2/8/17
 */
public class ProviderListChangeListener implements NotificationObserver {
    private static final Log log = LogFactory.getLog(ProviderListChangeListener.class);
    private static final ProviderListChangeListener instance = new ProviderListChangeListener();

    public static ProviderListChangeListener getInstance(){
        return instance;
    }

    private ProviderListChangeListener(){

    }

    @Override
    public void notifyAdd(Notification notification) {
        notify(notification);
    }

    @Override
    public void notifyUpdate(Notification notification) {
        notify(notification);
    }

    private void notify(Notification notification){
        String brokerName = notification.getEntityNameSpace();
        String entityId = notification.getEntityId();
        ProvidersMetaData providerMetadata = MarketMakerFactory.getInstance().getCorePriceProviderService().getProviderDataService().getProvidersMetaDataById(brokerName, entityId);
        if(providerMetadata == null){
            log.info("ProviderListChangeListener.notify:: entity not found org=" + brokerName + ", id=" + entityId);
            return;
        }
        String ccyPair = providerMetadata.getCcyPair();
        updateLPDeployment();
        //updateConfigurations(brokerName, ccyPair, providerMetadata);
        refreshSubscriptions(brokerName, ccyPair);
        updateProviderQuoteCache(brokerName, ccyPair, providerMetadata);
        updatePublishers(brokerName, ccyPair);
    }

    private void updatePublishers(String brokerName, String ccyPair){
        CorePricePublisherC publisher = CorePriceServiceFactory.getInstance().findPublisher(brokerName, ccyPair);
        if(publisher == null){
            log.info("ProviderListChangeListener.notify:: no publisher for org=" + brokerName + ", cp=" + ccyPair);
            return;
        }else {
            publisher.updateProviderChange();
        }
    }

    private void updateLPDeployment(){
        ServiceProvidersMBean serviceProvidersMBean = ISFactory.getInstance().getServicProvidersMBean();
        if(serviceProvidersMBean instanceof MarketMakerServiceProvidersMBeanC){
            serviceProvidersMBean.onNotification();
        }
    }

    private void updateProviderQuoteCache(String brokerName, String ccyPair, ProvidersMetaData providersMetaData){
        CorePriceProviderQuoteCacheC providerQuoteCache = CorePriceServiceFactory.getInstance().findProviderQuoteCache(brokerName, ccyPair);
        if(providerQuoteCache != null) {
            Set<String> providerSet = providersMetaData.getProviderStatus().keySet();
            providerQuoteCache.setProviders(providerSet);
            log.info("ProviderListChangeListener.updateProviderQuoteCache:: updated lp=" + providerSet + ", bk=" + brokerName + ", cp=" + ccyPair);
        }else {
            log.info("ProviderListChangeListener.updateProviderQuoteCache:: quote cache not present, bk=" + brokerName + ", cp=" + ccyPair);
        }
    }

    private void updateConfigurations(String brokerName, String ccyPair, ProvidersMetaData providerMetadata){
        Set<String> providers = new HashSet<String>(providerMetadata.getProviderStatus().keySet());
        providers.add(brokerName);
        List<Configuration> configurations = findAllMMConfiguration(brokerName, ccyPair);
        ConfigurationService configurationService = AdminFactory.getInstance().getConfigurationService();
        for(Configuration config : configurations){
            String streamName = config.getStream() != null ? config.getStream().getShortName() : "NULL";
            try {
                List<Organization> orderProviders = null;
                if (config.isLiquidityGroupOnly()) {
                    orderProviders = new ArrayList<Organization>(config.getLiquidityGroupOrderProviders(ccyPair));
                } else {
                    orderProviders = config.getOrderProviders();
                }
                boolean hasChanged = false;
                List<Organization> updatedLPs = new ArrayList<Organization>(orderProviders.size());
                for(Organization provider : orderProviders) {
                    if (providers.contains(provider.getShortName())) {
                        updatedLPs.add(provider);
                    }else {
                        hasChanged = true;
                    }
                }
                if (hasChanged) {
                    configurationService.setOrderProviders(config, updatedLPs);
                    log.info("ProviderListChangeListener.updateConfigurations:: updated bk=" + brokerName + ", cp=" + ccyPair + ", strm=" + streamName + ", config=" + config.getShortName());
                }else {
                    log.info("ProviderListChangeListener.updateConfigurations:: no change bk=" + brokerName + ", cp=" + ccyPair + ", strm=" + streamName + ", config=" + config.getShortName());
                }
            }catch (Exception e){
                log.error("ProviderListChangeListener.updateConfigurations:: failed bk=" + brokerName + ", cp=" + ccyPair + ", strm=" + streamName + ", config=" + config.getShortName(), e);
            }
        }
    }


    private List<Configuration> findAllMMConfiguration(String brokerName, String ccyPair){
        List<Configuration> mmConfigs = new ArrayList<Configuration>();
        Organization organization = ReferenceDataCacheC.getInstance().getOrganization(brokerName);
        Collection<Stream> streams = organization.getBrokerOrganizationFunction().getStreams();
        for(Stream stream:streams){
            Collection<Configuration> configurations = stream.getConfigurations();
            for(Configuration configuration:configurations){
                if(configuration.isMarketMaker() ){
                    if(configuration.getCurrencyPairGroup().getCurrencyPairs().iterator().next().getName().equals(ccyPair)) {
                        mmConfigs.add(configuration);
                    }
                }
            }
        }
        return mmConfigs;
    }
    private void refreshSubscriptions(String orgName, String cp){
        if(!MarketMakerConfig.getInstance().getMarketMakerEnabledBrokers().contains(orgName)){
            log.info("ProviderListChangeListener.refreshSubscriptions:: Not a MM Org " + orgName);
            return;
        }
        CorePriceSubscriberC subscriber = CorePriceServiceFactory.getInstance().findSubscriber(orgName, cp);
        if(subscriber == null){
            log.info("ProviderListChangeListener.refreshSubscriptions:: no active subscription. " + orgName + " " + cp);
            createNewSubscription(orgName, cp);
            return;
        }
        subscriber.reSubscribe();
        log.info("ProviderListChangeListener.refreshSubscriptions:: Started for " + orgName + ", and " + cp);
    }

    private void createNewSubscription(String orgName, String cp){
        try{
            Organization broker = ReferenceDataCacheC.getInstance().getOrganization(orgName);
            CorePriceService corePriceService = CorePriceServiceFactory.getInstance().getCorePriceService(broker, CorePriceServiceFactory.CorePriceServiceType.MM);
            MMCorePriceServiceC mmCorePriceService = (MMCorePriceServiceC)corePriceService;
            CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(cp);
            mmCorePriceService.startSubscriber(broker, currencyPair);
            log.info("ProviderListChangeListener.refreshSubscriptions:: created new subscription for " + orgName + " " + cp);
        }catch (Exception e){
            log.error("ProviderListChangeListener.refreshSubscriptions:: failed to create new subscription for " + orgName + " " + cp, e);
        }
    }

    public static void registerWithRDS(String brokerName, ProviderListChangeListener obj){
        MarketMakerFactory.getInstance().getCorePriceProviderService().getProviderDataService().registerForMetaDataChange(brokerName, obj);
    }
}
