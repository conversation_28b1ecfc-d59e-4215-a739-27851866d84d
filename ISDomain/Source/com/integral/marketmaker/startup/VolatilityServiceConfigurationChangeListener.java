package com.integral.marketmaker.startup;

import com.integral.broker.configuration.ConfigurationFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.marketmaker.broker.BrokerMetaData;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.tierpricing.TierPricingMetaData;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;
import com.integral.system.configuration.ConfigUpdateHandler;
import com.integral.volatilityservice.VolatilityServiceConfiguration;
import com.integral.volatilityservice.VolatilityServiceElement;
import com.integral.volatilityservice.VolatilityServiceMBean;

import java.util.HashMap;
import java.util.List;


public class VolatilityServiceConfigurationChangeListener implements NotificationObserver, ConfigUpdateHandler {
    private static final Log log = LogFactory.getLog(VolatilityServiceConfigurationChangeListener.class);

    private static final VolatilityServiceConfigurationChangeListener listener = new VolatilityServiceConfigurationChangeListener();
    private final double DEFAULT_VOL_SPREAD_MULTIPLIER =1;

    public static VolatilityServiceConfigurationChangeListener getInstance(){
        return listener;
    }

    public static void registerWithRDS(String orgName, VolatilityServiceConfigurationChangeListener changeListener){
        MarketMakerFactory.getInstance().getVolatilityServiceConfigService().registerForMetaDataChange(orgName, changeListener);
    }

    @Override
    public void notifyAdd(Notification notification) {
        notify(notification);
    }

    @Override
    public void notifyUpdate(Notification notification) {
        notify(notification);
    }

    private void notify(Notification notification){
        String orgName = notification.getEntityNameSpace();
        log.info("VolatilityServiceConfigurationChangeListener.notify: VolatilityServiceConfiguration changed received for Org:"+ orgName);
        updateTierPriceMetaData(orgName);
    }

    public void updateTierPriceMetaData(String orgName){
        VolatilityConfigHandler handler = new VolatilityConfigHandler();
        handler.setOrgName(orgName);
        Thread thread= new Thread(handler);
        thread.setName("VolatilityServiceConfigChangeHandler_"+ orgName);
        thread.start();
    }

    // Handle Idc.VolatilityService.Enabled.Orgs is modified
    @Override
    public void handleUpdate(String propertyName, String event) {
        if (!VolatilityServiceMBean.ENABLED_ORGS.equals(propertyName)) {
            return;
        }
        log.info("VolatilityServiceConfigurationChangeListener.handling: Idc.VolatilityService.Enabled.Orgs change");
        updateTierPriceMetaData();
    }

    //revert spreadMultiplier and UseMultiplierFromService for MM orgs that are not configured in Volatility service
    public void updateTierPriceMetaData(){
        List<String> volServiceEnabledOrgs = ConfigurationFactory.getInstance().getVolatilityServiceMBean().getEnabledOrgs();
        List<String> marketMakerEnabledOrgs = MarketMakerConfig.getInstance().getMarketMakerEnabledBrokers();

        for (String marketMakerOrg : marketMakerEnabledOrgs) {
            if (volServiceEnabledOrgs != null && volServiceEnabledOrgs.contains(marketMakerOrg)) {
                continue;
            }
            revertToManual(marketMakerOrg);
        }
    }

    private void revertToManual(String orgName) {
        //get all ccy Pair....
        BrokerMetaData brokerMetaData = MarketMakerFactory.getInstance().getCorePriceBrokerService().get(orgName);
        if (brokerMetaData == null)
            return;
        for (String ccyPair : brokerMetaData.getCurrencyPairs()) {
            try {
                TierPricingMetaData tierPricingMetaData = MarketMakerFactory.getInstance().getTierPricingService().get(orgName, ccyPair);
                if (tierPricingMetaData != null && tierPricingMetaData.isUseMultiplierFromService()) {
                    tierPricingMetaData.setUseMultiplierFromService(false);
                    tierPricingMetaData.setVolatilitySpreadMultiplier(DEFAULT_VOL_SPREAD_MULTIPLIER);
                    TierPricingMetaData updatedTierPricingMetaData = MarketMakerFactory.getInstance().getTierPricingService().update(tierPricingMetaData);
                    log.info("VolatilityServiceConfigurationChangeListener.revertToManual: Updating Tier Price metadata config volatility to Manual:" + updatedTierPricingMetaData.toString());
                }
            } catch (Exception ex) {
                log.error("VolatilityServiceConfigurationChangeListener.revertToManual: Error occurred. org:"+ orgName + ", cp:"+ ccyPair, ex);
            }
        }
    }

    private class VolatilityConfigHandler implements Runnable{

        final int MAX_RETRY_COUNT =3;
        private String orgName;
        private int retryCount;
        public String getOrgName() {
            return orgName;
        }
        public void setOrgName(String orgName) {
            this.orgName = orgName;
        }

        @Override
        public void run() {
            VolatilityServiceConfiguration metaData = MarketMakerFactory.getInstance().getVolatilityServiceConfigService().getByOrgName(orgName);
            if (metaData == null) {
                log.warn("VolatilityServiceConfigurationChangeListener.notify:: entity not found org=" + orgName);
                return;
            }
            HashMap<String, Boolean> volCcypairMap = new HashMap<String, Boolean>();
            for (VolatilityServiceElement element : metaData.getElements()) {
                volCcypairMap.put(element.getCcyPair(), element.isEnabled());
            }

            BrokerMetaData brokerMetaData = MarketMakerFactory.getInstance().getCorePriceBrokerService().get(orgName);
            if (brokerMetaData == null)
                return;

            for (String ccyPair : brokerMetaData.getCurrencyPairs()) {

                if (volCcypairMap.containsKey(ccyPair) && volCcypairMap.get(ccyPair) == Boolean.TRUE)
                    continue;
                retryCount = 0;
                boolean updateSucessful = false;
               do{
                    try {
                        //For disabled configuration, revert tierPrice volatility to Manual
                        TierPricingMetaData tierPricingMetaData = MarketMakerFactory.getInstance().getTierPricingService().get(orgName, ccyPair);
                        if(tierPricingMetaData == null ){
                            updateSucessful = true;
                            if(log.isDebugEnabled()){
                                log.debug("VolatilityConfigChangeHandler: TierPricing Meta Data not found for broker:"+orgName + ", cp:"+ ccyPair);
                            }
                        }
                        else if( tierPricingMetaData != null && tierPricingMetaData.isUseMultiplierFromService() == false){
                            updateSucessful = true;
                            if(log.isDebugEnabled()){
                                log.debug("VolatilityConfigChangeHandler: TierPricing Meta Data isUseMultiplierFromService is false for broker:"+orgName + ", cp:"+ ccyPair);
                            }
                        }
                        else if (tierPricingMetaData != null && tierPricingMetaData.isUseMultiplierFromService()) {
                            tierPricingMetaData.setUseMultiplierFromService(false);
                            tierPricingMetaData.setVolatilitySpreadMultiplier(1);
                            TierPricingMetaData updatedTierPricingMetaData = MarketMakerFactory.getInstance().getTierPricingService().update(tierPricingMetaData);
                            log.info("Updating Tier Price metadata config volatility to Manual:" + updatedTierPricingMetaData.toString());
                            updateSucessful = true;
                        }

                        if(updateSucessful == true)
                            break;
                    } catch (Exception ex) {
                        log.error("Error occurred while updating Tier price metadata volatility to Manual for org:" + orgName, ex);
                        updateSucessful = false;
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {

                        }
                    }
                    retryCount++;

                } while (retryCount < MAX_RETRY_COUNT);

                if (!updateSucessful) {
                    log.warn("Updating Tier Price metadata config volatility to Manual Failed for broker:" + metaData.getOrg() + ", ccyPair: " + ccyPair);
                }

            }
        }
    }
}
