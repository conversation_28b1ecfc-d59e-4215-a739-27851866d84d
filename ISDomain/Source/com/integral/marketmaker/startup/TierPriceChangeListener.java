package com.integral.marketmaker.startup;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.maker.service.tierpricing.TierPricingDataService;
import com.integral.marketmaker.service.coreprice.CorePricePublisherC;
import com.integral.marketmaker.service.coreprice.CorePriceServiceFactory;
import com.integral.marketmaker.tierpricing.TierPricingMetaData;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 2/2/17
 */
public class TierPriceChangeListener implements NotificationObserver {
    private static final Log log = LogFactory.getLog(TierPriceChangeListener.class);
    TierPricingDataService tierPricingDataService = MarketMakerFactory.getInstance().getTierPricingService().getTierPricingDataService();
    private static final TierPriceChangeListener instance = new TierPriceChangeListener();

    public static TierPriceChangeListener getInstance(){
        return instance;
    }

    private TierPriceChangeListener(){

    }

    @Override
    public void notifyAdd(Notification notification) {
        updateCorePricePublisher(notification);
    }

    @Override
    public void notifyUpdate(Notification notification) {
        updateCorePricePublisher(notification);
    }

    private void updateCorePricePublisher(Notification notification){
        String orgName = notification.getEntityNameSpace();
        String entityId = notification.getEntityId();
        TierPricingMetaData tierPricingMetaData = tierPricingDataService.getTierPricingMetaDataById(orgName, entityId);
        String ccyPair = tierPricingMetaData.getCcyPair();
        CorePricePublisherC publisher = CorePriceServiceFactory.getInstance().findPublisher(orgName, ccyPair);
        if(publisher != null) {
            publisher.updateTierChange(tierPricingMetaData);
            publisher.setConfigChange(true);
            log.info("TierPriceChangeListener.updateCorePricePublisher:: org=" + orgName + ", cp=" + ccyPair);
        }else {
            log.info("TierPriceChangeListener.updateCorePricePublisher:: no publisher, org=" + orgName + ", cp=" + ccyPair);
        }
    }

    public static void registerWithRDS(String orgName, TierPriceChangeListener tierPriceChangeListener){
        MarketMakerFactory.getInstance().getTierPricingService().getTierPricingDataService().registerForMetaDataChange(orgName, tierPriceChangeListener);
    }
}
