package com.integral.marketmaker.startup;

import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.service.MarketMakerServices;
import com.integral.marketmaker.service.coreprice.CorePriceService;
import com.integral.marketmaker.service.coreprice.TOBBasedCorePriceServiceSubscription;
import com.integral.system.runtime.StartupTask;

import java.util.Hashtable;

/**
 * Created by verma on 4/11/16.
 */
public class MarketMakerStartup implements StartupTask {
    private static final Log log = LogFactory.getLog(MarketMakerStartup.class);

    /**
     * The method that is being called at system startup
     *
     * @param aName
     * @param args
     */
    @Override
    public String startup(String aName, Hashtable args) {
        if( !MarketMakerConfig.getInstance().isMarketMakerServicesStartupEnabled()){
            log.info("startup : MarketMaker services startup is not enabled");
            return null;
        }
        boolean success = false;
        try {
            if (MarketMakerServices.getTOBBasedCorePriceService() == null) {
                CorePriceService<TOBBasedCorePriceServiceSubscription> service = (CorePriceService<TOBBasedCorePriceServiceSubscription>) Class.forName(MarketMakerConfig.getInstance().getTobBasedCorePriceServiceClass()).newInstance();
                if( service.init() && service.start() ){
                    MarketMakerServices.setTOBBasedCorePriceService(service);
                    success = true;
                }
            }
            else{
                success = true;
            }
        }
        catch (Throwable th) {
            success = false;
            log.error("startup ",th);
        }
        finally {
            if( !success ){
                logAlertAndStopServer("MarketMakerStartup Failed");
            }
        }
        return null;
    }

    protected void logAlertAndStopServer(String message) {
        try {
            log.fatal("logAlertAndStopServer : Application startup failed. Message="+message);
            MessageLogger.getInstance().log(ERROR_CODE_APP_STARTUP_FAILED, this.getClass().getName(), message, null);
            /*
                Sleep for 5 seconds for log to get flushed.
             */
            Thread.sleep(5000);
        }
        catch (InterruptedException e) {
            e.printStackTrace();
        }
        finally {
            System.exit(-1);
        }
    }
}
