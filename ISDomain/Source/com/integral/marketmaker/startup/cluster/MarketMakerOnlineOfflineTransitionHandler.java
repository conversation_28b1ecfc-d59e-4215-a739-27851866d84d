package com.integral.marketmaker.startup.cluster;

import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.service.coreprice.CorePriceService;
import com.integral.marketmaker.service.coreprice.CorePriceServiceFactory;
import com.integral.marketmaker.service.coreprice.MMCorePriceServiceC;
import com.integral.marketmaker.shutdown.MarketMakerServerShutdownC;
import com.integral.marketmaker.startup.MarketMakerServerStartupC;
import com.integral.services.ServiceContainerMBean;
import com.integral.services.cluster.mapped.notification.OnlineOffLineMappedTransitionHandler;
import com.integral.user.Organization;

import java.util.List;

public class MarketMakerOnlineOfflineTransitionHandler implements OnlineOffLineMappedTransitionHandler {

    Log log = LogFactory.getLog(MarketMakerOnlineOfflineTransitionHandler.class);

    @Override
    public void onBecomeOnline(String resourceName, String partitionId) {

        if(log.isDebugEnabled()){
            StringBuilder sb = new StringBuilder();
            sb.append("MarketMakerOnlineOfflineTransitionHandler.onBecomeOnline::");
            sb.append(" MM Server : "+ ServiceContainerMBean.getLocalServerName());
            sb.append(" ResourceName : "+resourceName);
            sb.append(" PartitionId :"+partitionId);
            log.info(sb.toString());
        }

        //PartitionId must be the Broker org name it is mapped with
        Organization org = OrganizationUtil.getOrganization(partitionId);
        if(org == null){
            log.warn("MarketMakerOnlineOfflineTransitionHandler.onBecomeOnline:: org not exist " + partitionId);
            return;
        }
        String orgName = org.getShortName();
        List<String> mmBrokersList = MarketMakerConfig.getInstance().getMarketMakerEnabledBrokers();
        if(null!=mmBrokersList && mmBrokersList.contains(orgName)){
            new MarketMakerServerStartupC().startCorePriceServices(orgName);
            log.info("MarketMakerOnlineOfflineTransitionHandler.onBecomeOnline:: started core price services for " + orgName);
        }else{
            log.warn("MarketMakerOnlineOfflineTransitionHandler.onBecomeOnline:: Failed to start MM services. "+orgName+" is not Market Maker enabled org");
        }
    }

    @Override
    public void onBecomeOffline(String resourceName, String partitionId) {

        if(log.isDebugEnabled()) {
            StringBuilder sb = new StringBuilder();
            sb.append("MarketMakerOnlineOfflineTransitionHandler.onBecomeOffline::");
            sb.append(" MM Server : "+ ServiceContainerMBean.getLocalServerName());
            sb.append(" ResourceName : "+resourceName);
            sb.append(" PartitionId :"+partitionId);
            log.info(sb.toString());
        }

        //PartitionId must be the Broker org name it is mapped with
        Organization org = OrganizationUtil.getOrganization(partitionId);
        if(org == null){
            log.info("MarketMakerOnlineOfflineTransitionHandler.onBecomeOffline:: org does not exist " + partitionId);
            return;
        }

        String orgName = org.getShortName();
        new MarketMakerServerShutdownC().stopCorePriceServices(orgName);
        log.info("MarketMakerOnlineOfflineTransitionHandler.onBecomeOffline:: stopped core price services for " + orgName);
    }

    @Override
    public void dropped(String resourceName, String partitionId) {
        log.info("Dropping partition:"+partitionId);
    }
}
