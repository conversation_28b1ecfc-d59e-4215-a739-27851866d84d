package com.integral.marketmaker.startup.broker;

import com.integral.ems.provision.EMSProvisionManager;
import com.integral.ems.provision.EMSProvisionManagerFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.corepricing.CorePricingMetaData;
import com.integral.marketmaker.corepricing.ExecutionStrategy;
import com.integral.marketmaker.startup.CorePriceChangeListener;
import com.integral.user.Organization;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 3/3/17
 */
public class CorePriceChangeListenerForBroker extends CorePriceChangeListener {
    private static final Log log = LogFactory.getLog(CorePriceChangeListenerForBroker.class);

    @Override
    protected void notify(Organization organization, String ccyPair, CorePricingMetaData metaData){
        updateProductProvision(organization, ccyPair, metaData.getExecutionStrategy().equals(ExecutionStrategy.NoCover));
    }

    private void updateProductProvision(Organization organization, String ccyPair, boolean noCoverEnabled){
        EMSProvisionManager emsProvisionManager = EMSProvisionManagerFactory.getEMSProvisionManager(organization);
        emsProvisionManager.destroyProductProvisionsIfCoverStgyChanged(ccyPair, noCoverEnabled);
    }
}
