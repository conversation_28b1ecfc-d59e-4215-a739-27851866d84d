package com.integral.marketmaker.startup.broker;

import com.integral.ems.provision.EMSProvisionManager;
import com.integral.ems.provision.EMSProvisionManagerFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.streampricing.StreamPricingDataService;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;
import com.integral.user.Organization;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 12/6/16
 */
public class MMPriceMakingSpreadChangeListener implements NotificationObserver {
    private static final Log log = LogFactory.getLog(MMPriceMakingSpreadChangeListener.class);
    private StreamPricingDataService streamPricingDataService = new StreamPricingDataService();
    private static final MMPriceMakingSpreadChangeListener instance = new MMPriceMakingSpreadChangeListener();

    public static MMPriceMakingSpreadChangeListener getInstance(){
        return instance;
    }

    private MMPriceMakingSpreadChangeListener(){

    }

    private void refreshProductProvisions(String orgName){
        Organization organization = ReferenceDataCacheC.getInstance().getOrganization(orgName);
        if(organization == null){
            log.warn("MMPriceMakingSpreadChangeListener.refreshProductProvisions:: org not exist " + orgName);
            return;
        }
        EMSProvisionManager emsProvisionManager = EMSProvisionManagerFactory.getEMSProvisionManager(organization);
        emsProvisionManager.destroyMarketMakerProductProvisions();
        log.info("MMPriceMakingSpreadChangeListener.refreshProductProvisions - product provisions refreshed for " + orgName);
    }

    @Override
    public void notifyAdd(Notification notification) {
        refreshProductProvisions(notification.getEntityNameSpace());
    }

    @Override
    public void notifyUpdate(Notification notification) {
        refreshProductProvisions(notification.getEntityNameSpace());
    }

    public void registerWithRDS(String brokerName){
        streamPricingDataService.registerForMetaDataChange(brokerName, this);
        log.info("MMPriceMakingSpreadChangeListener.registerWithRDS:: registered for Stream Metadata changes for " + brokerName);
    }
}
