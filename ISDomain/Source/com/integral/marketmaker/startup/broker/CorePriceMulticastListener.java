package com.integral.marketmaker.startup.broker;

import com.integral.broker.config.BrokerConfigurationServiceFactory;
import com.integral.broker.marketmaker.BrokerMarketMakerUtil;
import com.integral.broker.quote.BrokerQuoteFactory;
import com.integral.broker.quote.QuoteHandlerC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.finance.fx.FXRateConvention;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.CorePriceSerializer;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.service.coreprice.TierdPriceData;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.user.Organization;

import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.net.SocketTimeoutException;
import java.util.*;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 8/9/16
 */
public class CorePriceMulticastListener {
    private static final Log log = LogFactory.getLog(CorePriceMulticastListener.class);
    private static final CorePriceMulticastListener instance = new CorePriceMulticastListener();
    private MMBrokerListener listener;

    private volatile boolean active;

    private CorePriceMulticastListener(){}

    public static CorePriceMulticastListener getInstance(){
        return instance;
    }

    public synchronized void startListening(){
        try {
            //cancel existing listener if any
            if(active){
                log.info("CorePriceMulticastListener.startListening:: already listening");
                return;
            }
            active = true;
            if(listener != null){
                listener.setRunning(false);
            }
            listener = new MMBrokerListener();
            Thread thread = new Thread(listener);
            thread.setName("MMBrokerMulticastListenerThread");
            thread.setDaemon(true);
            thread.start();
            log.info("CorePriceMulticastListener.startListening:: Started");
        } catch (Exception e) {
            log.warn("CorePriceMulticastListener.startListening", e);
        }
    }

    public synchronized void stopListening(){
        if(listener != null){
            listener.setRunning(false);
        }
        active = false;
        log.info("CorePriceMulticastListener.stopListening:: Stopped");
    }

    public boolean isActive(){
        return active;
    }

    private class MMBrokerListener implements Runnable{
        private volatile boolean running = true;
        private final MulticastSocket multicastSocket;
        private final FXRateConvention quoteConv; //std quote convention
        private final byte[] bytes;
        private final Set<Integer> brokerIndexSet = new HashSet<Integer>();
        private final long minPublishInterval;

        public MMBrokerListener() throws Exception {
            MarketMakerConfig marketMakerConfig = MarketMakerConfig.getInstance();
            String address = marketMakerConfig.getMulticastAddress();
            int multicastPort = marketMakerConfig.getTierPriceMulticastPort();
            int socketTimeout = marketMakerConfig.getBrokerMulticastSocketTimeout();
            this.minPublishInterval = marketMakerConfig.getBrokerMinPublishInterval();
            int mtu = marketMakerConfig.getMulticastMTU();
            multicastSocket = new MulticastSocket(multicastPort);
            multicastSocket.setTimeToLive( ConfigurationFactory.getServerMBean().getMulticastTTL());
            multicastSocket.setSoTimeout(socketTimeout);
            InetAddress multicastAddress = InetAddress.getByName(address);
            multicastSocket.joinGroup(multicastAddress);
            Collection<Organization> deployedBrokers = BrokerConfigurationServiceFactory.getBrokerConfigurationService().getDeployedBrokerOrganizations();
            for(Organization broker : deployedBrokers){
                brokerIndexSet.add(broker.getIndex());
            }
            quoteConv = ReferenceDataCacheC.getInstance().getStandardFXRateConvention();
            bytes = new byte[mtu];
        }

        @Override
        public void run() {
            //long lastPublished = 0;
            while (running) {
                try {
                    DatagramPacket packet = new DatagramPacket(bytes, bytes.length);
                    try {
                        multicastSocket.receive(packet); //blocking call
                    } catch (SocketTimeoutException e) {
                        if(log.isDebugEnabled()) log.debug("CorePriceMulticastListener:: no rates for " + multicastSocket.getSoTimeout() + " milliseconds");
                        continue;
                    }
                    //lastPublished = waitTillMinInterval(lastPublished);
                    TierdPriceData tiers = CorePriceSerializer.deserializeTierPrice(packet.getData(), brokerIndexSet);
                    if(tiers == null) continue;
                    Organization organization = ReferenceDataCacheC.getInstance().getOrganization(tiers.getOrganization());
                    CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(tiers.getCurrencyPair());
                    QuoteHandlerC quoteHandler = (QuoteHandlerC)BrokerQuoteFactory.getInstance().getQuoteHandler(organization);
                    Quote quote = BrokerMarketMakerUtil.createQuote(organization, currencyPair, tiers, quoteConv);
                    quoteHandler.sendRate(quote, Collections.emptyMap());
                    if(log.isDebugEnabled())log.debug("CorePriceMulticastListener.run:: Quoted added to quote handler. org=" + tiers.getOrganization() + ", cp=" + tiers.getCurrencyPair());
                } catch (Throwable e) {
                    log.warn("MulticastPoller.run failed", e);
                }
            }
            //do clean up
            multicastSocket.close();
            running = false;
            active = false;
            log.info("MulticastPoller.run:: run completed");
        }

        private long waitTillMinInterval(long lastPublished){
            long current = System.currentTimeMillis();
            long diff = current - lastPublished;
            if(diff < minPublishInterval){
                try {
                    if(log.isDebugEnabled()) log.debug("CorePriceMulticastListener:: sleeping for " + (minPublishInterval - diff));
                    Thread.sleep(minPublishInterval - diff);
                } catch (InterruptedException e) {
                    log.warn("some exception", e);
                }
            }
            return System.currentTimeMillis();
        }

        public void setRunning(boolean running) {
            this.running = running;
        }
    }
}


