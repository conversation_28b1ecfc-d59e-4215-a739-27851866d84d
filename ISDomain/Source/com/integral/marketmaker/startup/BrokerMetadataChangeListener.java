package com.integral.marketmaker.startup;

import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.MarketMakerServiceProvidersMBeanC;
import com.integral.is.common.mbean.ServiceProvidersMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.marketmaker.broker.BrokerMetaData;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.service.coreprice.*;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;
import com.integral.user.Organization;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 2/22/17
 */
public class BrokerMetadataChangeListener implements NotificationObserver {
    private static final Log log = LogFactory.getLog(BrokerMetadataChangeListener.class);
    private static final BrokerMetadataChangeListener instance = new BrokerMetadataChangeListener();

    public static BrokerMetadataChangeListener getInstance(){
        return instance;
    }

    private BrokerMetadataChangeListener(){

    }

    @Override
    public void notifyAdd(Notification notification) {
        updateCorePriceService(notification);
    }

    @Override
    public void notifyUpdate(Notification notification) {
        updateCorePriceService(notification);
    }

    private void updateCorePriceService(Notification notification){
        String orgName = notification.getEntityNameSpace();
        if(!MarketMakerConfig.getInstance().getMarketMakerEnabledBrokers().contains(orgName)){
            log.info("BrokerMetadataChangeListener.updateCorePriceService:: Not a MM Org " + orgName);
            return;
        }
        String entityId = notification.getEntityId();
        BrokerMetaData metaData = MarketMakerFactory.getInstance().getCorePriceBrokerService().getBrokerDataService().getBrokerMetaDataById(orgName, entityId);
        if(metaData == null){
            log.warn("BrokerMetadataChangeListener.updateCorePriceService:: BrokerMetadata is null. name=" + orgName);
            return;
        }
        Set<String> currencyPairs = metaData.getCurrencyPairs();
        updateSubscribers(orgName, currencyPairs);
        updatePublishers(orgName, currencyPairs);
        updateLPDeployment();
    }

    private void updateLPDeployment(){
        ServiceProvidersMBean serviceProvidersMBean = ISFactory.getInstance().getServicProvidersMBean();
        if(serviceProvidersMBean instanceof MarketMakerServiceProvidersMBeanC){
            serviceProvidersMBean.onNotification();
        }
    }

    private void updatePublishers(String orgName, Set<String> currencyPairs){
        currencyPairs = new HashSet<String>(currencyPairs);
        List<CorePricePublisherC> publishers = CorePriceServiceFactory.getInstance().getCorePricePublishers(orgName);
        for(CorePricePublisherC publisher : publishers){
            String currencyPair = publisher.getCurrencyPair();
            if(currencyPairs.contains(currencyPair)){
                if(!publisher.isRunning()) {
                    publisher.start();
                    log.info("BrokerMetadataChangeListener.updatePublishers:: Started for " + orgName + ", and " + publisher.getCurrencyPair());
                }
                currencyPairs.remove(currencyPair);
            }else {
                if(publisher.isRunning()) {
                    publisher.stop();
                    log.info("BrokerMetadataChangeListener.updatePublishers:: Stopped for " + orgName + ", and " + publisher.getCurrencyPair());
                }
            }
        }
        if(currencyPairs.isEmpty()) return;
        Organization org = OrganizationUtil.getOrganization(orgName);
        if(org == null){
            log.warn("BrokerMetadataChangeListener.updatePublishers org is null " + orgName);
            return;
        }
        for(String cp : currencyPairs){
            CorePriceService corePriceService = CorePriceServiceFactory.getInstance().getCorePriceService(org, CorePriceServiceFactory.CorePriceServiceType.MM);
            if(corePriceService instanceof MMCorePriceServiceC){
                CurrencyPair ccyPair = CurrencyFactory.getCurrencyPairFromString(cp);
                if(ccyPair == null) {
                    log.warn("BrokerMetadataChangeListener.updatePublishers cp is null " + cp);
                    continue;
                }
                ((MMCorePriceServiceC) corePriceService).startPublisher(org, ccyPair);
                log.info("BrokerMetadataChangeListener.updatePublishers:: Started for " + orgName + ", and " + cp);
            }else {
                log.error("BrokerMetadataChangeListener.updatePublishers:: CorePriceService is not an instance of Market Maker");
            }
        }
    }

    private void updateSubscribers(String orgName, Set<String> currencyPairs){
        currencyPairs = new HashSet<String>(currencyPairs);
        List<CorePriceSubscriberC> subscribers = CorePriceServiceFactory.getInstance().getSubscribers(orgName);
        for(CorePriceSubscriberC subscriber : subscribers){
            String currencyPair = subscriber.getCurrencyPair();
            if(currencyPairs.contains(currencyPair)){
                if(!subscriber.isRunning()){
                    subscriber.start();
                    log.info("BrokerMetadataChangeListener.updateSubscribers:: started " + orgName + " " + currencyPair);
                }
                currencyPairs.remove(currencyPair);
            }else {
                if(subscriber.isRunning()){
                    subscriber.stop();
                    log.info("BrokerMetadataChangeListener.updateSubscribers:: stopped " + orgName + " " + currencyPair);
                }
            }
            if(currencyPairs.isEmpty()) return;
            Organization org = OrganizationUtil.getOrganization(orgName);
            if(org == null){
                log.warn("BrokerMetadataChangeListener.updateSubscribers org is null " + orgName);
                return;
            }
            for(String cp : currencyPairs){
                CorePriceService corePriceService = CorePriceServiceFactory.getInstance().getCorePriceService(org, CorePriceServiceFactory.CorePriceServiceType.MM);
                if(corePriceService instanceof MMCorePriceServiceC){
                    CurrencyPair ccyPair = CurrencyFactory.getCurrencyPairFromString(cp);
                    if(ccyPair == null) {
                        log.warn("BrokerMetadataChangeListener.updateSubscribers cp is null " + cp);
                        continue;
                    }
                    ((MMCorePriceServiceC) corePriceService).startSubscriber(org, ccyPair);
                    log.info("BrokerMetadataChangeListener.updateSubscribers:: Started for " + orgName + ", and " + cp);
                }else {
                    log.error("BrokerMetadataChangeListener.updateSubscribers:: CorePriceService is not an instance of Market Maker");
                }
            }
        }
    }

    public static void registerWithRDS(String namespace, BrokerMetadataChangeListener observer){
        MarketMakerFactory.getInstance().getCorePriceBrokerService().getBrokerDataService().registerForMetaDataChange(namespace, observer);
    }
}
