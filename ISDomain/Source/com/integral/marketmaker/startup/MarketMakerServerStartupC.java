package com.integral.marketmaker.startup;

import com.integral.broker.LocalBrokerAdaptorFactory;
import com.integral.broker.config.BrokerConfigurationServiceFactory;
import com.integral.broker.configuration.ConfigurationFactory;
import com.integral.broker.filter.BrokerFilterFactory;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ServiceProvidersMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.service.MMBAService;
import com.integral.marketmaker.service.coreprice.CorePriceService;
import com.integral.marketmaker.service.coreprice.CorePriceServiceFactory;
import com.integral.marketmaker.service.coreprice.MMVolatilityFactorPublisherC;
import com.integral.marketmaker.service.scheduler.MMTierSpreadMultiplierSchedulerService;
import com.integral.marketmaker.startup.broker.CorePriceChangeListenerForBroker;
import com.integral.marketmaker.startup.broker.CorePriceMulticastListener;
import com.integral.marketmaker.startup.broker.MMPriceMakingSpreadChangeListener;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.rds.client.ClientFactory;
import com.integral.rds.notification.NotificationObserver;
import com.integral.riskmanagement.rw.provision.RWProvisionImpl;
import com.integral.system.runtime.StartupTask;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.Hashtable;
import java.util.List;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 4/4/17
 */
public class MarketMakerServerStartupC implements StartupTask {
    private static Log log = LogFactory.getLog(MarketMakerServerStartupC.class);

    @Override
    public String startup(String aName, Hashtable args) throws Exception {
        ISFactory.getInstance().setServiceProvidersMBean(ServiceProvidersMBean.ServiceProvidersMBeanType.MarketMaker);
        ServiceProvidersMBean servicProvidersMBean = ISFactory.getInstance().getServicProvidersMBean();
        servicProvidersMBean.startAllAdaptors(true);
        List<String> marketMakerEnabledOrgs = MarketMakerConfig.getInstance().getMarketMakerEnabledBrokers();
        log.info("MarketMakerServerStartupC.startup:: Market Maker Configured Orgs: " + marketMakerEnabledOrgs);
        LocalBrokerAdaptorFactory.init();
        //If the server is running in cluster mode, no need to start the core price services.
        //They shall be started via transition handler on notification from cluster controller.
        if(!MarketMakerConfig.getInstance().isJoinMMClusterOnStartup()){
            for(String brokerName : marketMakerEnabledOrgs) {
                startCorePriceServices(brokerName);
            }
        }

        VolatilityServiceConfigurationChangeListener.getInstance().updateTierPriceMetaData();
        ConfigurationFactory.getInstance().getVolatilityServiceMBean().register(VolatilityServiceConfigurationChangeListener.getInstance());
        log.info("MarketMakerServerStartupC.startup:: Started Market Maker Services.");
        return null;
    }

    public void startCorePriceServices(String brokerName){
        try{
            String listenerClass = MarketMakerConfig.getInstance().getRWProvisionListenerClass();
            NotificationObserver observer = (NotificationObserver) Class.forName(listenerClass).newInstance();
            ClientFactory.getFactory().getNotificationHandler().addObserver(RWProvisionImpl.class, brokerName, observer);
            BrokerFilterFactory brokerFilterFactory = BrokerFilterFactory.getInstance();
            Organization broker = ReferenceDataCacheC.getInstance().getOrganization(brokerName);
            CorePriceService corePriceService = CorePriceServiceFactory.getInstance().getCorePriceService(broker, CorePriceServiceFactory.CorePriceServiceType.MM);
            corePriceService.start();
            ProviderListChangeListener.registerWithRDS(brokerName, ProviderListChangeListener.getInstance());
            BrokerMetadataChangeListener.registerWithRDS(brokerName, BrokerMetadataChangeListener.getInstance());
            //new CorePriceChangeListener().setInitialSkew(brokerName);
            CorePriceChangeListener.registerWithRDS(brokerName, CorePriceChangeListener.getInstance());
            TierPriceChangeListener.registerWithRDS(brokerName, TierPriceChangeListener.getInstance());
            VolatilityServiceConfigurationChangeListener.registerWithRDS(brokerName,VolatilityServiceConfigurationChangeListener.getInstance());
            CorePriceChangeListener.initSkewMode(brokerName);
            YMStatusMonitor.scheduleMonitor();
            if(brokerFilterFactory.getRateFilterManager(broker) == null){
                brokerFilterFactory.addFilterManager(broker).start();
                log.info("MarketMakerServerStartupC.startup:: RateFilterManager started for " + brokerName);
            }

            //The persistentService used within the loadConfiguration is throwing concurrentTimeout exception in clustered mode
            //Hence synchronizing MMTierSpreadMultiplierSchedulerService initialization
            synchronized (MarketMakerServerStartupC.class){
                MMTierSpreadMultiplierSchedulerService.getInstance().initialize(brokerName);
            }

            MMVolatilityFactorPublisherC.getInstance(true).joinMulticast(brokerName);
            MMVolatilityFactorPublisherC.getInstance(false).joinMulticast(brokerName);
            VolatilityServiceConfigurationChangeListener.getInstance().updateTierPriceMetaData(brokerName);
            log.info("MarketMakerServerStartupC.startup:: Core Price Service Started for " + brokerName);
        }catch (Exception e){
            log.error("MarketMakerServerStartupC.startup:: Exception during starting core price services for " + brokerName, e);
        }
    }

    public static void startMMServicesForBroker(String brokerName){
        try {
            Collection<String> deployedBrokers = BrokerConfigurationServiceFactory.getBrokerConfigurationService().getDeployedBrokerOrganizationNames();
            if(deployedBrokers.contains(brokerName)) {
                MMBAService mmbaService = new MMBAService();
                mmbaService.init();
                mmbaService.start();
                CorePriceMulticastListener.getInstance().startListening();
                MMPriceMakingSpreadChangeListener.getInstance().registerWithRDS(brokerName);
                //new CorePriceChangeListenerForBroker().setInitialSkew(brokerName);
                CorePriceChangeListener.registerWithRDS(brokerName, new CorePriceChangeListenerForBroker());
                log.info("MarketMakerServerStartupC.startMMServicesForBroker:: Started Market Maker Services for " + brokerName);
            }else {
                log.info("MarketMakerServerStartupC.startMMServicesForBroker:: Not a Market Maker enabled Broker " + brokerName);
            }

        }catch (Exception e){
            log.error("MarketMakerServerStartupC.startMMServicesForBroker:: Exception during initialization", e);
        }
    }
}
