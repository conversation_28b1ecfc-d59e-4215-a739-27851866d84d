package com.integral.marketmaker.startup;


import com.integral.broker.quote.BrokerQuoteFactory;
import com.integral.broker.quote.QuoteHandler;
import com.integral.broker.quote.QuoteHandlerC;
import com.integral.is.common.Provider;
import com.integral.is.common.ProviderManagerObserver;
import com.integral.is.common.ProviderStatusObserver;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.ProviderManagerC;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;

import java.util.concurrent.ConcurrentHashMap;


public class MMProviderManagerObserver implements ProviderManagerObserver {
    protected Log log = LogFactory.getLog(this.getClass());

    public MMProviderManagerObserver()
    {
    }

    @Override
    public void providerAdded(String providerName) {
        log.info("MMPMO.providerAdded " + providerName);
        Provider provider =ProviderManagerC.getInstance().getProvider(providerName);
        if( null!=provider) {
            provider.addProviderStatusObserver(new MMProviderStatusObserver(providerName));
        }
    }

    @Override
    public void providerRemoved(String providerName) {

    }
}
