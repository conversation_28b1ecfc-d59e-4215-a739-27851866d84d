package com.integral.marketmaker.startup;


import com.integral.broker.quote.BrokerQuoteFactory;
import com.integral.broker.quote.QuoteHandler;
import com.integral.broker.quote.QuoteHandlerC;
import com.integral.is.common.ProviderStatusObserver;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;

import java.util.List;


public class MMProviderStatusObserver implements ProviderStatusObserver {
    protected Log log = LogFactory.getLog(this.getClass());
    private String provider ;
    public MMProviderStatusObserver( String provider )
    {
        this.provider = provider;
    }

    public void providerActive()
    {
        log.warn("MMPSO.provideractive: PROVIDER-UP: Name " + provider);

    }

    @Override
    public void providerInactive() {
        log.warn("MMPSO.providerInactive: PROVIDER-DOWN: Removing quotes for Name " + provider);
        removeQuote();
    }

   @Override
    public void providerStale() {
        log.warn("MMPSO.providerStale: PROVIDER-STALE: Removing quotes for Name " + provider);
       removeQuote();
    }

    private  void removeQuote() {
        List<String> marketMakerEnabledBrokers = MarketMakerConfig.getInstance().getMarketMakerEnabledBrokers();
        for(String broker:marketMakerEnabledBrokers) {
            Organization brokerOrg = ReferenceDataCacheC.getInstance().getOrganization(broker);
            if( null!= brokerOrg) {
                try {
                    QuoteHandler quoteHandler = BrokerQuoteFactory.getInstance().getQuoteHandler(brokerOrg);
        Organization providerOrg = ReferenceDataCacheC.getInstance().getOrganization(provider);
        ((QuoteHandlerC)quoteHandler).removeQuote(providerOrg);
                }catch(Exception e){
                    log.error("MMPSO.removeQuote for failed for Provider " +
                               provider + " , MM Broker " + broker + " combination",e);
                }
            }
        }
    }
}
