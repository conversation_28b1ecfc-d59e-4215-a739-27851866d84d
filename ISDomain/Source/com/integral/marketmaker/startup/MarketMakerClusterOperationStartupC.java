package com.integral.marketmaker.startup;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.marketmaker.service.cluster.MarketMakerClusteringService;
import com.integral.marketmaker.startup.cluster.MarketMakerOnlineOfflineTransitionHandler;
import com.integral.system.runtime.StartupTask;

import java.util.Hashtable;

public class MarketMakerClusterOperationStartupC implements StartupTask {

    private static Log log = LogFactory.getLog(MarketMakerServerStartupC.class);

    @Override
    public String startup(String aName, Hashtable args) throws Exception {
        joinClusterOnStartup();
        return null;
    }

    private void joinClusterOnStartup() {

        boolean joinCluster = MarketMakerConfig.getInstance().isJoinMMClusterOnStartup();
        if(joinCluster){
            String clusterName = MarketMakerConfig.getInstance().getClusterName();
            boolean joinSuccess = false;
            try{
                joinSuccess = MarketMakerClusteringService.getInstance().joinCluster(clusterName, new MarketMakerOnlineOfflineTransitionHandler());
                log.info("MarketMakerClusterOperationStartupC.joinClusterOnStartup : joined :"+joinSuccess);
            }catch (Exception ex){
                throw new RuntimeException("MarketMakerClusterOperationStartupC.joinClusterOnStartup : Exception occurred to join the cluster:"+ex);
            }

            if(!joinSuccess){
                throw new RuntimeException("MarketMakerClusterOperationStartupC.joinClusterOnStartup : Failed to join the cluster");
            }
        }else{
            log.info("Not joining MM cluster : JoinClusterOnStartup is disabled");
        }
    }
}
