package com.integral.marketmaker.startup;

import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ServiceProvidersMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.ProviderManagerC;
import com.integral.system.runtime.StartupTask;

import java.util.Hashtable;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 4/7/17
 */
public class MarketMakerProviderListStartupC implements StartupTask {
    private static Log log = LogFactory.getLog(MarketMakerServerStartupC.class);
    @Override
    public String startup(String aName, Hashtable args) throws Exception {
        ISFactory.getInstance().setServiceProvidersMBean(ServiceProvidersMBean.ServiceProvidersMBeanType.MarketMaker);
        ServiceProvidersMBean servicProvidersMBean = ISFactory.getInstance().getServicProvidersMBean();
        servicProvidersMBean.startAllAdaptors(true);
        MMProviderManagerObserver mmProviderManagerObserver = new MMProviderManagerObserver();
        String providerList = servicProvidersMBean.getProvidersList();
        for (String provider : providerList.split(",")){
            mmProviderManagerObserver.providerAdded(provider);
        }
        ProviderManagerC.getInstance().addObserver(mmProviderManagerObserver);
        log.info("MarketMakerProviderListStartupC.startup:: provider list set");
        return null;
    }
}
