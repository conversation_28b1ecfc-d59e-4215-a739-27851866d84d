package com.integral.marketmaker;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 10/6/16
 */
public class MarketMakerThreadPoolFactory {
    private static ScheduledExecutorService publisherScheduler;

    public static ScheduledExecutorService getPublisherScheduler(){
        if(publisherScheduler == null){
            publisherScheduler = Executors.newScheduledThreadPool(10, new MarketMakerThreadFactory("MMCorePricePublisherThread"));
        }
        return publisherScheduler;
    }

    public static class MarketMakerThreadFactory implements ThreadFactory
    {
        final AtomicInteger threadNumber = new AtomicInteger( 1 );
        ThreadGroup tg = null;
        String name ;
        public MarketMakerThreadFactory( String name )
        {
            tg = new ThreadGroup( name );
            this.name=name;
        }

        public Thread newThread( Runnable runnable )
        {
            return new Thread( tg, runnable, name+"_" + threadNumber.getAndIncrement() );
        }
    }
}

