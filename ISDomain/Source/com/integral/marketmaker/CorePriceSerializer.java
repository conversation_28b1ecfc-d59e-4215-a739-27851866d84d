package com.integral.marketmaker;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.integral.broker.volatility.VolatilityMetric;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyResolver;
import com.integral.is.finance.currency.CurrencyService;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.service.coreprice.CorePriceData;
import com.integral.marketmaker.service.coreprice.ProviderPriceData;
import com.integral.marketmaker.service.coreprice.TierdPriceData;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.transport.multicast.configuration.MulticastConfigurationFactory;
import com.integral.user.Organization;
import com.integral.user.OrganizationIndexResolver;

import javax.xml.bind.DatatypeConverter;
import java.net.DatagramPacket;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 8/15/16
 */
public class CorePriceSerializer {
    private static final Log log = LogFactory.getLog(CorePriceSerializer.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    public static ByteBuffer serializeCorePrice(CorePriceData corePrice, Organization org, CurrencyPair cp){
        int noOfLPs = corePrice.getProviderPrices().size();
        byte[] bytes = new byte[40 + noOfLPs * 20]; //4 + 4 + 4 + 8 + 8 + 8 + 4 + (noOfLPs * (4 + 8 + 8))
        ByteBuffer byteBuffer = ByteBuffer.wrap(bytes);
        byteBuffer.putInt(org.getIndex());
        byteBuffer.putInt(cp.getBaseCurrency().getIndex());
        byteBuffer.putInt(cp.getVariableCurrency().getIndex());
        byteBuffer.putDouble(corePrice.getBidRate());
        byteBuffer.putDouble(corePrice.getMidRate());
        byteBuffer.putDouble(corePrice.getOfferRate());
        byteBuffer.putInt(noOfLPs);
        for(ProviderPriceData providerPrice : corePrice.getProviderPrices()){
            Organization provider = ReferenceDataCacheC.getInstance().getOrganization(providerPrice.getProvider());
            if(provider == null) continue;
            int lpIndex = provider.getIndex();
            double bid = providerPrice.getBidPrice();
            double offer = providerPrice.getOfferPrice();
            byteBuffer.putInt(lpIndex);
            byteBuffer.putDouble(bid);
            byteBuffer.putDouble(offer);
        }
        return byteBuffer;
    }

    public static CorePriceData deserializeCorePrice(byte[] data, Set<Integer> orgIndexFilter){
        ByteBuffer byteBuffer = ByteBuffer.wrap(data);
        int orgIndex = byteBuffer.getInt();
        if(orgIndexFilter != null && !orgIndexFilter.contains(orgIndex)) return null;
        int bcIndex = byteBuffer.getInt();
        int vcIndex = byteBuffer.getInt();
        Organization organization = ReferenceDataCacheC.getInstance().getOrganization(orgIndex);
        Currency bc = CurrencyFactory.getCurrencyByIndex(bcIndex);
        Currency vc = CurrencyFactory.getCurrencyByIndex(vcIndex);
        CurrencyPair currencyPair = CurrencyFactory.getCurrencyPair(bc, vc);
        double bestBid = byteBuffer.getDouble();
        double midPrice = byteBuffer.getDouble();
        double bestOffer = byteBuffer.getDouble();
        CorePriceData corePrice = new CorePriceData();
        corePrice.setOrganization(organization.getShortName());
        corePrice.setCurrencyPair(currencyPair.getName());
        corePrice.setBidRate(bestBid);
        corePrice.setMidRate(midPrice);
        corePrice.setOfferRate(bestOffer);
        int count = byteBuffer.getInt();
        for (int i = 1; i <= count; i++) {
            int lpIndex = byteBuffer.getInt();
            Organization lp = ReferenceDataCacheC.getInstance().getOrganization(lpIndex);
            double bid = byteBuffer.getDouble();
            double offer = byteBuffer.getDouble();
            corePrice.addProviderPrice(lp.getShortName(), bid, offer);
        }
        return corePrice;
    }

    public static ByteBuffer serializeTierPrice(TierdPriceData tierdPrice, Organization org, CurrencyPair cp){
        int noOfTiers = tierdPrice.getTiers().size();
        byte[] bytes = new byte[16 + noOfTiers * 24]; //4 + 4 + 4 + 4 + (noOfTier * (8 + 8 + 8)) =
        ByteBuffer byteBuffer = ByteBuffer.wrap(bytes);
        byteBuffer.putInt(org.getIndex());
        byteBuffer.putInt(cp.getBaseCurrency().getIndex());
        byteBuffer.putInt(cp.getVariableCurrency().getIndex());
        byteBuffer.putInt(noOfTiers);
        for(TierdPriceData.Tier tier : tierdPrice.getTiers()){
            byteBuffer.putDouble(tier.getAmount());
            byteBuffer.putDouble(tier.getBidRate());
            byteBuffer.putDouble(tier.getOfferRate());
        }
        return byteBuffer;
    }

    public static TierdPriceData deserializeTierPrice(byte[] data, Set<Integer> orgIndexFilter){
        ByteBuffer byteBuffer = ByteBuffer.wrap(data);
        int orgIndex = byteBuffer.getInt();
        if(orgIndexFilter != null && !orgIndexFilter.contains(orgIndex)) return null;
        int bcIndex = byteBuffer.getInt();
        int vcIndex = byteBuffer.getInt();
        Organization organization = ReferenceDataCacheC.getInstance().getOrganization(orgIndex);
        Currency bc = CurrencyFactory.getCurrencyByIndex(bcIndex);
        Currency vc = CurrencyFactory.getCurrencyByIndex(vcIndex);
        CurrencyPair currencyPair = CurrencyFactory.getCurrencyPair(bc, vc);
        TierdPriceData tiers = new TierdPriceData(organization.getShortName(), currencyPair.getName());
        int count = byteBuffer.getInt();
        for (int i = 1; i <= count; i++) {
            double amount = byteBuffer.getDouble();
            double bid = byteBuffer.getDouble();
            double offer = byteBuffer.getDouble();
            tiers.addTier(amount, bid, offer);
        }
        return tiers;
    }

    public static List<VolatilityMetric> deserializeVolatilityMetric(byte[]data){
        ByteBuffer byteBuffer = ByteBuffer.wrap(data);
        CurrencyResolver currencyResolver = MulticastConfigurationFactory.getInstance().getCurrencyResolverInstance();
        OrganizationIndexResolver organizationIndexResolver = MulticastConfigurationFactory.getInstance().getOrganizationIndexResolverInstance();
        List<VolatilityMetric> metricList = new ArrayList<VolatilityMetric>();
        String orgShortName = organizationIndexResolver.getOrganizationShortname(byteBuffer.getInt());
        if(orgShortName == null){
            if(log.isDebugEnabled()) log.debug("Failed to deserialize volatility metric, base64 encoded data: " + DatatypeConverter.printBase64Binary(data));
            return metricList;
        }
        long timeStamp = byteBuffer.getLong();
        int metricLength = byteBuffer.getInt();
        for(int index=0; index < metricLength; index++){

            String baseCcy = currencyResolver.getCcyShortname( byteBuffer.getInt());
            String variableCcy = currencyResolver.getCcyShortname( byteBuffer.getInt());
            double metricValue = byteBuffer.getDouble();
            int bidOfferMode = byteBuffer.getInt();

            VolatilityMetric metric =  new VolatilityMetric();
            metric.setBroker(orgShortName);
            metric.setTimestamp(timeStamp);
            metric.setCurrencyPair(CurrencyService.getCurrencyPair(baseCcy, variableCcy).getName());
            metric.setValue(metricValue);
            metric.setBidOfferMode(bidOfferMode);
            metricList.add(metric);
        }
        return metricList;
    }

    public static List<VolatilityMetric> deserializeVolatilityMetricJson(DatagramPacket packet){
        String jsonStr = new String(packet.getData(), packet.getOffset(), packet.getLength());
        try{
            if(log.isDebugEnabled()) log.debug("Deserializing volatility metric: " + jsonStr);
            List<com.integral.marketpulse.VolatilityMetric> list = objectMapper.readValue(jsonStr, new TypeReference<List<com.integral.marketpulse.VolatilityMetric>>(){});
            List<VolatilityMetric> output = new ArrayList<VolatilityMetric>();
            for(com.integral.marketpulse.VolatilityMetric metric : list){
                output.add(new VolatilityMetric(metric));
            }
            return output;
        }catch (Exception e){
            if(log.isDebugEnabled()) log.debug("Error deserializing volatility metric", e);
            return null;
        }
    }

    public static ByteBuffer serialize(Organization broker, CurrencyPair currencyPair, long timestamp, double factor, int bidOfferMode) { //0=NA, 1=MID, 2=BID-OFFER
        int NO_OF_ITEMS = 1;
        //body: orgIndex(4 bytes), timestamp(8 bytes), numberOfItems(4 bytes), n * [baseCcyIndex(4 bytes), varCcyIndex(4 bytes), value(8 bytes)]
        int length = 4 + 8 + 4 + NO_OF_ITEMS * (4 + 4 + 8 + 4);
        byte[] bytes = new byte[length];
        ByteBuffer byteBuffer = ByteBuffer.wrap(bytes);
        byteBuffer.putInt(broker.getIndex());
        byteBuffer.putLong(timestamp);
        byteBuffer.putInt(NO_OF_ITEMS);
        byteBuffer.putInt(currencyPair.getBaseCurrency().getIndex());
        byteBuffer.putInt(currencyPair.getVariableCurrency().getIndex());
        byteBuffer.putDouble(factor);
        byteBuffer.putInt(bidOfferMode);

        return byteBuffer;
    }
}
