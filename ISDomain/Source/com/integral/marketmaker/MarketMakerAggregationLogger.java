package com.integral.marketmaker;

import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.log.LoggerUtilC;

import java.util.Collection;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 11/23/16
 */
public class MarketMakerAggregationLogger {
    private static final Log log = LogFactory.getLog(MarketMakerAggregationLogger.class);

    public static Log getLog(String orgName){
        return LoggerUtilC.getInstance().createSpecializedLog(log, orgName, false);
    }

    public static void logQuoteDetails(Collection<Quote> quotes, StringBuilder sb){
        for(Quote quote : quotes) {
            sb.append(' ').append(quote.getOrganization().getShortName());
            sb.append(' ').append(quote.getGUID());
            String type;
            if(quote.getPriceType() == Quote.PRICE_TYPE_MULTI_TIER) type = "MT";
            else if(quote.getPriceType() == Quote.PRICE_TYPE_QUOTES) type = "MQ";
            else type = "UNKNOWN";
            sb.append(' ').append(type);
            sb.append(" B");
            for(FXLegDealingPrice bid : quote.getBids()){
                sb.append(' ').append(bid.getDealtAmount());
                sb.append(' ').append(bid.getSpotRate());
            }
            sb.append(" O");
            for(FXLegDealingPrice offer : quote.getOffers()){
                sb.append(' ').append(offer.getDealtAmount());
                sb.append(' ').append(offer.getSpotRate());
            }
        }
    }
}
