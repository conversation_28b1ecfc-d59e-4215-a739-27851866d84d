package com.integral.adaptor.response;

/**
 * Factory for {@link ResponseHandler} instances. Should be initialized at
 * startup by the appropriate instance using {@link #__init__at__startup__(ResponseHandler)} method.
 * <AUTHOR>
 */
public class ResponseHandlerFactory
{

	public static ResponseHandlerFactory instance = new ResponseHandlerFactory();

	private ResponseHandler recv = null;

	public void __init__at__startup__(ResponseHandler recv)
	{
		this.recv = recv;
	}

	public ResponseHandler getMessageReceiver()
	{
		return this.recv;
	}
}
