package com.integral.adaptor.response;

import com.integral.adaptor.config.AdaptorConfiguration;
import com.integral.adaptor.config.AdaptorConfigurationFactory;
import com.integral.adaptor.log.ProviderRateMetrics;
import com.integral.broker.model.Stream;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.TradingVenue;
import com.integral.is.message.MarketRate;
import com.integral.is.message.MarketRateSerializer;
import com.integral.is.message.MarketRateSerializerFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.transport.multicast.MulticastAddress;
import com.integral.transport.multicast.MulticastGroup;
import com.integral.user.Organization;
import com.integral.xems.aeron.TradingVenueIntegration;
import com.integral.xems.broker.quote.MVBrokerQuote;
import com.integral.xems.adaptor.response.MDFMulticastQuoteSender;
import com.integral.xems.log.LogUtil;

import java.io.IOException;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.net.UnknownHostException;
import java.nio.BufferOverflowException;
import java.nio.ByteBuffer;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * An specialization of the standard {@link ResponseHandlerC} that can
 * send rate messages when hosted inside an instance of an IS.
 * This class assumes that a valid db connection is available, and therefore, queries all the multicast
 * information required to send a message.  <Br>
 * <p/>
 * {@link #sendRate(com.integral.is.message.MarketRate)} will send rate over a configured multicast address/port,
 * if found for the provider.
 * In addition, if there are JMS subscribers to this provider, a copy of the rate will be sent to them as well.
 * <p/>
 * <p/>
 * Note that Mcast listeners do {@link com.integral.is.message.V2MarketRateSerializer Version 2} serialization, whereas
 * JMS listeners followin {@link com.integral.is.message.V1MarketRateSerializer Version 1} serialization.
 *
 * <AUTHOR>
 */
public class LocalResponseHandlerC extends ResponseHandlerC
{
    protected Log log = LogFactory.getLog( this.getClass() );

    ConcurrentHashMap<Integer, ProviderRateMetrics.RateMetric> rateMetricsMap = new ConcurrentHashMap<Integer, ProviderRateMetrics.RateMetric>();
    static MetricsManager metricsManager = MetricsManager.instance();
    static ServerMBean serverMBean = ConfigurationFactory.getServerMBean();    

    // {providerShortname -> {streamIndex -> InetAddress}}
    ConcurrentHashMap<String, ConcurrentHashMap<String, InetAddress>> streamAddrMap = new ConcurrentHashMap<String, ConcurrentHashMap<String, InetAddress>>();
    private static final String NULL_STREAM = "__NULL_STREAM__";
    private int streamIndexErrorCounter = 0;
    private long timeLastMetric = 0;

    static int serializerVersion = AdaptorConfigurationFactory.getAdaptorConfigurationMBean().getSerializerVersion();

    boolean isVenueIntegrationEnabled = false;
    private AdaptorConfiguration config;
    private int mdfMulticastPort;
    TradingVenueIntegration tradingVenueIntegration;

    private MDFMulticastQuoteSender mdfQuoteSender;

    public LocalResponseHandlerC() {
        try {
            config = AdaptorConfigurationFactory.getAdaptorConfigurationMBean();
            mdfMulticastPort = config.getMdfMulticastPort();
            if(config.isV4BrokerEnabled()){
                try {
                    Class<?> c = Class.forName("com.integral.adaptor.response.MDFMulticastQuoteSenderC");
                    Constructor constructor = null;
                    constructor =  c.getDeclaredConstructor();
                    mdfQuoteSender = (MDFMulticastQuoteSender) constructor.newInstance();
                } catch (Exception e) {
                    log.error("Exception in the constructor LocalResponseHandlerC : ", e);
                }
            }
            isVenueIntegrationEnabled = config.isVenueIntegrationEnabled();
            if(isVenueIntegrationEnabled) {
                Class<?> c = Class.forName("com.integral.is.common.tradingvenue.aeron.TradingVenueIntegrationManager");
                Method method = c.getDeclaredMethod("getInstance", null);
                tradingVenueIntegration = (TradingVenueIntegration) method.invoke(null);

                log.info("Broke Venue Integration Enabled : " + mdfQuoteSender);

            }
        } catch (Exception e) {
            log.error("Exception in the constructor LocalResponseHandlerC ", e);
        }
    }
    public void init() throws IOException
    {

    }

    public void sendRate( MarketRate message )
    {
        sendRate(message, ISCommonConstants.PROVIDER_TYPE_DEF);
    }

    public void sendRate( MarketRate message, String providerType )
    {
    	sendRate(message, providerType, serializerVersion );
    }


    public void sendRate(MarketRate message, String providerType, int serializerVer) {
        sendMcastRate(message, serializerVer);
        if (isVenueIntegrationEnabled) {
            sendRatesToVenue(message, providerType);
        }

    }

    private void sendRatesToVenue(MarketRate message, String providerType) {
        int superLPIndex = message.getSuperLPIndex();
        int ccyPairIndex = message.getCcyPairIndex();
        int providerIndex = message.getProviderIndex();

            if (tradingVenueIntegration != null) {
                ConcurrentHashMap<Integer, List<TradingVenue>> ccyPairCache = tradingVenueIntegration.getTradingVenueList(superLPIndex);
                if (log.isDebugEnabled()) {
                    log.debug("currency pair cache " + ccyPairCache + "  stream index " + superLPIndex + "  cache " + tradingVenueIntegration.getTradingVenueList(superLPIndex));

                }
                if (ccyPairCache != null) {
                    try {
                    List<TradingVenue> tradingVenues = ccyPairCache.get(ccyPairIndex);

                    if (tradingVenues != null) {
                        for (TradingVenue tradingVenue : tradingVenues) {
                            if(tradingVenue!=null){
                                tradingVenue.sendQuotes(message);
                            }else{
                                if (log.isDebugEnabled()) {
                                    log.debug("TradingVenue is null for ccypair :" + ccyPairIndex + " streamIndex: " + superLPIndex);
                                }
                            }

                        }
                    } else {
                        if (log.isDebugEnabled()) {
                            log.debug("Venue Integeration currency cache returned null for ccypair :" + ccyPairIndex + " streamIndex: " + superLPIndex);
                        }
                    }
                    }catch(Exception e){
                        log.error("sendRatesToVenue : Error sending rates to Venue: " +   ccyPairIndex + " streamIndex: " + superLPIndex);
                    }
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug("Venue Integeration cache returned null for superLPINdex:" + superLPIndex);
                    }
                }
            }

    }

    public boolean sendRatesToMDF(MVBrokerQuote quote, String orgName, String ccyPair) {
        String mdfMulticastGroup = config.getMDFMulticastGroup(orgName, ccyPair);
        InetAddress address = null;
        try {
            address = InetAddress.getByName(mdfMulticastGroup);
        } catch (Exception e) {
            log.error("sendRatesToMDF : Not Publishing Quotes to MDF, Exception getting InetAddress for mdfMulticastGroup :" + mdfMulticastGroup, e);
            return false;
        }
        if(mdfQuoteSender==null){
            if(log.isDebugEnabled()){
                log.debug(" mdfQuoteSender is null ");
            }
            return false;
        }
        if(log.isDebugEnabled()){
            log.debug(" Sending quotes to MDF : " + mdfMulticastGroup + " port : " + mdfMulticastPort );
        }
        return  mdfQuoteSender.sendQuotes(quote,address, mdfMulticastPort);

    }

    private void sendMcastRate( MarketRate message, int serializerVer )
    {
        String providerShortName = message.getProviderShortName();
        Organization providerOrg = ReferenceDataCacheC.getInstance().getOrganization( message.getProviderIndex() );
        if ( providerOrg == null )
        {
            providerOrg = ReferenceDataCacheC.getInstance().getOrganization( providerShortName );
        }
        if ( providerOrg == null )
        {
            return;
        }
        MulticastGroup mcastGroup = providerOrg.getMulticastGroup();
        if ( mcastGroup != null && mcastGroup.isEnabled() )
        {
            InetAddress address = getMcastAddressForStream( providerShortName, message.getStreamId() );
            if ( address != null )
            {
                ByteBuffer buff = bbPool.syncBorrowObject();
                MulticastSocket mcastSocket = null;
                try {
                    if (message.getStreamId() == null) {
                        message.setStreamIndex(0);
                    } else {
                        MulticastAddress mcastAddr = mcastGroup.getMulticastAddressForStreamName(message.getStreamId());
                        if (mcastAddr != null) {
                            message.setStreamIndex(mcastAddr.getOffset());
                        } else {
                            message.setStreamIndex(0);
                            if (streamIndexErrorCounter % 1000 == 0) {
                                log.warn("Multicast Address found null so setting stream index to 0, Provider:" + message.getProviderShortName() + " Stream: " + message.getStreamId());
                            }
                            streamIndexErrorCounter++;
                            if (streamIndexErrorCounter == Integer.MAX_VALUE) {
                                streamIndexErrorCounter = 0;
                            }
                        }
                    }
                    // This will generate metric at provider and stream level - since address hash code is unique per stream
                    ProviderRateMetrics.RateMetric rateMetric = rateMetricsMap.get(address.hashCode());
                    if (rateMetric == null) {
                        rateMetric = new ProviderRateMetrics.RateMetric();
                        rateMetric.providerName = message.getProviderShortName();
                        rateMetric.streamName = message.getStreamId();
                        rateMetric.port = mcastGroup.getLogicalPort().getPort();
                        rateMetricsMap.put(address.hashCode(), rateMetric);
                        metricsManager.register(rateMetric);
                    }
                    MarketRateSerializer serializer = MarketRateSerializerFactory.instance().getSerializerForVersion(serializerVer);
                    if (serializer.isMultipleMessagesPerMarketRateEnabled()) {
                        /*
                            Borrowing multicast socket first before serialization for versions supporting multiple messages per market rate
                         */
                        mcastSocket = socketPool.syncBorrowObject();
                        if (mcastSocket == null) {
                            log.error("Not able to send multicast rates: multicast socket is: " + mcastSocket + " - Current avail pool size is " + socketPool.getSize());
                        } else {
                            int numTiers = message.getNumTiers();
                            int maxTiersPerMessage = serializer.getMaxTiersPerMessage(buff.capacity());
                            int numMessages = numTiers / maxTiersPerMessage + (numTiers % maxTiersPerMessage != 0 ? 1 : 0);
                            /*
                                Setting the Rate Sent timestamp just before serialization
                             */
                            message.setRateSentByAdapter(System.currentTimeMillis());
                            for (int i = 0; i < numMessages; ++i) {
                                int startTierIdx = i * maxTiersPerMessage;
                                int numTiersToSerialize = Math.min(maxTiersPerMessage, (numTiers - i * maxTiersPerMessage));
                                buff.clear();
                                serializer.serialize(message, buff, startTierIdx, numTiersToSerialize);
                                buff.flip();
                                DatagramPacket packet = new DatagramPacket(buff.array(), buff.remaining(), address, mcastGroup.getLogicalPort().getPort());
                                if (log.isDebugEnabled()) {
                                    log.debug("Time sent multicast packet:" + System.currentTimeMillis() + " - Time rate sent by adaptor" + message.getRateSentByAdapter());
                                }
                                mcastSocket.send(packet);
                                if (rateMetric.maxPacketSize < packet.getLength()) {
                                    rateMetric.maxPacketSize = packet.getLength();
                                }
                            }
                            rateMetric.numRatesSent++;
                        }
                    } else {
                        serializer.serialize(message, buff);
                        buff.flip();
                        DatagramPacket packet = new DatagramPacket(buff.array(), buff.remaining(), address, mcastGroup.getLogicalPort().getPort());
                        mcastSocket = socketPool.syncBorrowObject();
                        if (mcastSocket != null) {
                            if (log.isDebugEnabled()) {
                                log.debug("Time sent multicast packet:" + System.currentTimeMillis() + " - Time rate sent by adaptor" + message.getRateSentByAdapter());
                            }
                            mcastSocket.send(packet);
                            if (rateMetric.maxPacketSize < packet.getLength()) {
                                rateMetric.maxPacketSize = packet.getLength();
                            }
                            rateMetric.numRatesSent++;
                        } else {
                            log.error("Not able to send multicast rates: multicast socket is: " + mcastSocket + " - Current avail pool size is " + socketPool.getSize());
                        }
                    }
                    rateMetric.addProcessingTime(message.getRateReceivedByAdapter() - message.getRateSentByAdapter());
                    rateMetric.addProviderDelay(message.getRateEffective() - message.getRateReceivedByAdapter());
                    if (System.currentTimeMillis() - timeLastMetric >= serverMBean.getMetricsManagerTimer()) {
                        rateMetric.swapMetrics();
                        timeLastMetric = System.currentTimeMillis();
                    }
                }
                catch (BufferOverflowException bufferOverflowException) {
                    StringBuilder sb = LogUtil.getStringBuilder();
                    sb.append("Buffer Overflow when sending mcast message")
                            .append("BASE_CCY=").append(message.getBaseCcy())
                            .append("VAR_CCY=").append(message.getVariableCcy())
                            .append("NUM_BID_TIERS=").append(message.getNumBidTiers())
                            .append("NUM_VAR_TIERS=").append(message.getNumOfferTiers())
                            .append("BOOK_NAME=").append(message.getBookname())
                            .append("QUOTE_ID=").append(message.getQuoteId())
                            .append("PROVIDER_SHORT_NAME=").append(message.getProviderShortName());
                    log.warn(sb.toString());
                    if( log.isDebugEnabled() ){
                        log.debug(sb.toString(),bufferOverflowException);
                    }
                } catch (IOException e) {
                    log.error("IOException when sending mcast message due to: " + e.getMessage());
                    if (log.isDebugEnabled()) {
                        log.error("Stack Trace ", e);
                    }
                }
                finally
                {
                    bbPool.syncReturnObject( buff );
                    if ( mcastSocket != null )
                    {
                        socketPool.syncReturnObject( mcastSocket );
                    }
                }
            }
        }
    }


    protected InetAddress getMcastAddressForStream( String providerShortname, String streamName )
    {
        ConcurrentHashMap<String, InetAddress> index = streamAddrMap.get( providerShortname );
        if ( index == null )
        {
            updateAddrMapForProvider( providerShortname );
            index = streamAddrMap.get( providerShortname );
        }

        if ( streamName == null )
        {
            streamName = NULL_STREAM;
        }
        return index.get( streamName );
    }

    protected void updateAddrMapForProvider( String providerShortname )
    {
        ConcurrentHashMap<String, InetAddress> index = streamAddrMap.get( providerShortname );
        if ( index == null )
        {
            index = new ConcurrentHashMap<String, InetAddress>();
            ConcurrentHashMap<String, InetAddress> existingIndex = streamAddrMap.putIfAbsent( providerShortname, index );
            if ( existingIndex != null )
            {
                index = existingIndex;
            }
        }
        index.clear();

        Organization providerOrg = ReferenceDataCacheC.getInstance().getOrganization( providerShortname );

        MulticastGroup mcastGroup = providerOrg.getMulticastGroup();

        if ( mcastGroup.isEnabled() )
        {
            List streams = mcastGroup.getAddresses();
            for ( Iterator it = streams.iterator(); it.hasNext(); )
            {
                MulticastAddress addr = ( MulticastAddress ) it.next();
                Stream stream = addr.getStream();
                if ( addr != null )
                {
                    try
                    {
                        InetAddress inetAddr = InetAddress.getByName( addr.getAddress() );
                        // REMOVE the metrices here - they will get added with next rate - this is to accommodate change in port
                        ProviderRateMetrics.RateMetric rateMetric = rateMetricsMap.remove( inetAddr.hashCode() );
                        if(rateMetric!=null)
                        {
                            metricsManager.unregister(rateMetric);
                        }

                        if ( stream != null )
                        {
                            index.put( stream.getShortName(), inetAddr );
                        }
                        else
                        {
                            String addressString = inetAddr.getHostAddress();
                            if(addressString!=null)
                            {
                                String lastOctetZero = addressString.substring ( addressString.lastIndexOf( "." )+1 );
                                if( "0".equals(lastOctetZero) )
                                {
                                    index.put( NULL_STREAM, inetAddr );
                                }
                            }
                        }
                    }
                    catch ( UnknownHostException e )
                    {
                        log.error( "Cannot resolved multicast group: " + addr.getAddress() + " due to: " + e.getMessage(), e );
                    }
                }
            }
        }
    }

    public void clearProviderStreamCache( String providerName )
    {
        updateAddrMapForProvider( providerName );
    }

}
