package com.integral.adaptor.response;

import com.integral.is.message.*;
import com.integral.is.message.rfs.RFSMarketRate;
import com.integral.is.message.rfs.RFSResponseMessage;
import com.integral.is.message.rfs.RFSTradeResponse;
import com.integral.is.message.rfs.RFSTradePending;

import javax.jms.JMSException;

/**
 * Created by IntelliJ IDEA.
 * User: kumark
 * Date: Aug 1, 2005
 * Time: 3:41:59 PM
 * To change this template use File | Settings | File Templates.
 */
public interface MessageReciever
{
	public void heartbeatRecieved(ProviderStatus status);

	public void rateRecieved(MarketRate message);

	public void tradeRecieved(TradeResponse message, boolean removeTradeInfo);

	public void tradeRecieved(TradeResponse message) throws JMSException;

	public void tradeRecieved(TradeResponses message) throws JMSException;

	public void tradePendingRecieved(TradePending message) throws JMSException;

	public void sendMessage(ISMessage message, String providerId);

	public void rfsRateRecieved(RFSMarketRate message);

	public void rfsTradeRecieved(RFSTradeResponse message) throws JMSException;

	public void rfsTradeRecieved(RFSTradeResponse message, boolean removeRFSRequest);

	public void rfsTradePendingRecieved(RFSTradeResponse message) throws JMSException;

	public void sendRFSResponseMessage(RFSResponseMessage response);

	public void sendRFSResponseMessage(RFSResponseMessage response, boolean removeRFSRequest);

	String OBJECT_TYPE = "objType";
}
