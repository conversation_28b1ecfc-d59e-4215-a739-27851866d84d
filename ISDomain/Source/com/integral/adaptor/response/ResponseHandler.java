package com.integral.adaptor.response;

import javax.jms.JMSException;

import com.integral.is.message.ISMessage;
import com.integral.is.message.MarketRate;
import com.integral.is.message.ProviderStatus;
import com.integral.is.message.TradePending;
import com.integral.is.message.TradeResponse;
import com.integral.is.message.TradeResponses;
import com.integral.is.message.rfs.RFSMarketRate;
import com.integral.is.message.rfs.RFSResponseMessage;
import com.integral.is.message.rfs.RFSTradeResponse;
import com.integral.xems.broker.quote.MVBrokerQuote;

/**
 * Created by IntelliJ IDEA.
 * User: kumark
 * Date: Aug 1, 2005
 * Time: 3:41:59 PM
 * To change this template use File | Settings | File Templates.
 */
public interface ResponseHandler
{
	public void heartbeatRecieved(ProviderStatus status);

	public void rateRecieved(MarketRate message);

    public void rateRecieved(MarketRate message, String providerType);
    
    public void rateRecieved(MarketRate message, String providerType, int serializerVer );

	public void tradeRecieved(TradeResponse message, boolean removeTradeInfo) throws JMSException;

	public void tradeRecieved(TradeResponse message, boolean removeTradeInfo, boolean partOfMultipleFills) throws JMSException ;

	public void tradeRecieved(TradeResponse message) throws JMSException;

	public void tradeRecieved(TradeResponses message) throws Exception;

	public void tradePendingRecieved(TradePending message) throws JMSException;

	public void tradePendingRecieved(TradePending message, boolean partOfMultilpleFills) throws JMSException;
	
	public void sendMessage(ISMessage message, String providerId);

	public void sendMessage(ISMessage message, String queueName, String brokerId) throws Exception;

	public void rfsRateRecieved(RFSMarketRate message);

	public void rfsTradeRecieved(RFSTradeResponse message) throws JMSException;

	public void rfsTradeRecieved(RFSTradeResponse message, boolean removeRFSRequest);

	public void rfsTradePendingRecieved(RFSTradeResponse message) throws JMSException;

	public void sendRFSResponseMessage(RFSResponseMessage response);

	public void sendRFSResponseMessage(RFSResponseMessage response, boolean removeRFSRequest);

	public void reloadHeartBeatDestinations(String providerName);

	public boolean sendRatesToMDF(MVBrokerQuote quote, String orgName, String ccyPair);


	String OBJECT_TYPE = "objType";

    String STRING_FORMAT = "stringFormat";

    String FORMAT_JSON = "JSON";
}
