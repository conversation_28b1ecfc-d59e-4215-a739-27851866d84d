package com.integral.adaptor.response;

import com.integral.is.message.MarketRate;

/**
 * A {@link ResponseHandler} implementation that runs on a ProviderAdaptor instance, which does not have
 * access to a DB.
 * <AUTHOR>
 */
public class AdaptorResponseHandlerC extends ResponseHandlerC implements ResponseHandler
{
	public AdaptorResponseHandlerC()
	{
	}

	public void sendRate(MarketRate mr)
	{
		//Empty implementation
	}

     public void sendRate( MarketRate message, String providerType )
     {
          //Empty implementation
     }
     
     public void sendRate( MarketRate message, String providerType, int serializerVer )
     {
          //Empty implementation
     }
    
    @Override
    public void clearProviderStreamCache( String providerName )
    {
        //Do nothing
    }

}
