package com.integral.adaptor.response;

import java.io.IOException;
import java.io.StringWriter;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import javax.jms.JMSException;
import javax.jms.QueueSession;

import com.integral.alert.AlertLoggerFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.xems.broker.quote.MVBrokerQuote;
import org.codehaus.jackson.JsonFactory;
import org.codehaus.jackson.JsonGenerator;

import com.integral.adaptor.AdaptorConstantC;
import com.integral.adaptor.config.AdaptorConfigurationFactory;
import com.integral.adaptor.heartbeat.MulticastHeartbeatSender;
import com.integral.adaptor.login.AdaptorLoginManagerC;
import com.integral.adaptor.manager.AdaptorTradeManager;
import com.integral.adaptor.manager.StalenessCheckHandler;
import com.integral.adaptor.manager.rfs.RFSAdaptorManager;
import com.integral.imtp.config.IMTPConfigMBeanC;
import com.integral.imtp.config.IMTPVersion;
import com.integral.imtp.connection.IMTPConnectionManager;
import com.integral.imtp.message.IMTPApplicationMessage;
import com.integral.imtp.message.IMTPMessageFactory;
import com.integral.imtp.session.IMTPSession;
import com.integral.is.ISCommonConstants;
import com.integral.is.alerttest.AlertCheckC;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.ISCommonUtilC;
import com.integral.is.log.MessageLogger;
import com.integral.is.message.ISMessage;
import com.integral.is.message.MarketRate;
import com.integral.is.message.MarketRateSerializer;
import com.integral.is.message.MarketRateSerializerFactory;
import com.integral.is.message.ProviderStatus;
import com.integral.is.message.TradePending;
import com.integral.is.message.TradeResponse;
import com.integral.is.message.TradeResponses;
import com.integral.is.message.rfs.RFSMarketRate;
import com.integral.is.message.rfs.RFSMarketRateC;
import com.integral.is.message.rfs.RFSResponseMessage;
import com.integral.is.message.rfs.RFSResponseMessageC;
import com.integral.is.message.rfs.RFSTradePendingC;
import com.integral.is.message.rfs.RFSTradeRejectC;
import com.integral.is.message.rfs.RFSTradeResponse;
import com.integral.is.message.rfs.RFSTradeResponseC;
import com.integral.is.message.rfs.RFSTradeVerifyC;
import com.integral.is.message.rfs.serializers.RFSMessageJsonSerializerFactory;
import com.integral.is.message.rfs.serializers.RFSMessageType;
///*
//import com.integral.jmsx.JMSMBeanC;
//import com.integral.jmsx.JMSManager;
//import com.integral.jmsx.JMSXMLMBeanC;
//import com.integral.jmsx.JNDIEntry;
//import com.integral.jmsx.hp.HiPerfJMSManager;
//*/
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.pool.ArrayListObjectPool;
import com.integral.pool.PoolFactory;
import com.integral.serialize.Serializer;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.transport.multicast.configuration.MulticastConfigurationFactory;
import com.integral.transport.multicast.configuration.MulticastMBean;

/**
 * This class is helper class for Adaptor code to send
 * messages to IntegrationServer.
 */
public abstract class ResponseHandlerC implements ResponseHandler
{

    public static final ServerMBean serverMBean = ConfigurationFactory.getServerMBean();
    public static final ISMBean isMBean = ISFactory.getInstance().getISMBean();
    private static final String VIRTUAL_SERVER_NAME = serverMBean.getVirtualServerName();
    private static final String LOCAL_SERVER_NAME = getLocalServerName();
    protected AdaptorLoginManagerC adaLoginMgr;
    static Log log = LogFactory.getLog( ResponseHandler.class );
    protected StalenessCheckHandler stalenessHandler;
    protected Map<String, List<String>> heartBeatDestinations;
    protected int deliveryMode = AdaptorConfigurationFactory.getAdaptorConfigurationMBean().getRatesDeliveryMode();
    //private RFSAdaptorManager rfsTradeMgr = RFSAdaptorManager.getInstance();
    private static MulticastMBean multicastMBean;
    private boolean rfsOverImtp = IMTPConfigMBeanC.getInstance().isRFSEnabledOverIMTP();

    MarketRateSerializer v1Serializer = MarketRateSerializerFactory.instance().getSerializerForVersion( 1 );
    MarketRateSerializer v2Serializer = MarketRateSerializerFactory.instance().getSerializerForVersion( 2 );

    ArrayListObjectPool<ByteBuffer> bbPool = null; 
    ArrayListObjectPool<MulticastSocket> socketPool = new ArrayListObjectPool<MulticastSocket>( new MulticastSocketFactory(), MulticastConfigurationFactory.getInstance().getMulticastMBean().getMulticastSocketPoolSize() );
	MulticastHeartbeatSender heartBeatSender = new MulticastHeartbeatSender();

	protected static ResponseHandlerC instance = null;

    public static ResponseHandlerC getInstance()
    {
        if ( instance == null )
        {
            synchronized (ResponseHandlerC.class)
            {
                if ( instance == null )
                {
                    instance = AdaptorConfigurationFactory.getResponseHandlerInstance();
                }
            }
        }
        return instance;
    }

    /**
     * Initialize the class
     */
    protected ResponseHandlerC()
    { 
    	int packetSize = ISFactory.getInstance().getISMBean().getMcastMaxPacketSize();
    	bbPool = new ArrayListObjectPool<ByteBuffer>( new ByteBufferFactory( packetSize, false ), 4 ); // 1024 * 4
    	
        //To register the AdaptorConfigMBean
        AdaptorConfigurationFactory.getAdaptorConfigurationMBean();
        adaLoginMgr = AdaptorLoginManagerC.getInstance();
        stalenessHandler = StalenessCheckHandler.getInstance();
		heartBeatDestinations = new ConcurrentHashMap<String, List<String>>();

        heartBeatSender.start();
        try
        {
            cacheHeartBeatDestinations();
        }
        catch ( Exception ex )
        {
            log.error( "MessageReceiverC.ResponseHandlerC() : Error in caching heartbeat destinations", ex );
        }
        if(rfsOverImtp){
            log.info("RFS requests enabled over IMTP");
        }
    }

    /**
     * Send heartbeat to integration server
     *
     * @param status
     */
    public void heartbeatRecieved( ProviderStatus status )
    {
        status.setVirtualServer(VIRTUAL_SERVER_NAME);
        status.setHostName(LOCAL_SERVER_NAME);
        status.setIMTPVersionId(IMTPVersion.IMTP_VERSION_ID);
        if(AdaptorConfigurationFactory.getAdaptorConfigurationMBean().isIMTPEnabled()){
            status.setImtpPort(IMTPConnectionManager.getInstance().getAcceptor().getBoundPort());
        }
        StalenessCheckHandler.getInstance().heartbeatReceived(status);
        if (log.isDebugEnabled())
        {
            log.debug( "MessageReceiverC.heartbeatReceived : Sending HeartBeat [" + status + "]" );
        }
        if(AdaptorConfigurationFactory.getAdaptorConfigurationMBean().isMulticastHeartBeatEnabled()){
            heartBeatSender.sendHeartbeat(status);
        }else{
	        int deliveryMode = AdaptorConfigurationFactory.getAdaptorConfigurationMBean().getHeartbeatDeliveryMode();
	        publishHeartbeat(status, deliveryMode, ServerMBean.GLOBAL_HEARTBEAT_JNDI_TOPIC_NAME);
	        if (serverMBean.isLPAutoDeploymentBackwardCompatible()) {
	            List<String> destList = heartBeatDestinations.get(createHeartBeatDestKey(status));
	            if (destList != null) {
	                Iterator<String> iter = destList.iterator();
	                while (iter.hasNext()) {
	                    String destination = iter.next();
	                    publishHeartbeat(status, deliveryMode, destination);
	                }
	            }
	        }
        }   
    }

    private void publishHeartbeat(ProviderStatus status, int deliveryMode, String destination) {
        try
        {
            log.info("This method is no longer supported. Dumping stack trace now");
            Thread.dumpStack();
           /* AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_ADAPTOR_JMS_PROVIDERSTATUS);
            if ( adaLoginMgr.isTextMessageEnabledForAllServers() )
            {
                String msg = status.getToString();
                Map<String,String> props = new HashMap<String,String>();
                props.put( ResponseHandler.OBJECT_TYPE, status.getClass().getName() );
                JMSManager.getInstance().sendMessage( destination, msg, props, deliveryMode, -1, -1 );

            }
            else
                JMSManager.getInstance().sendMessage( destination, status, null, deliveryMode, -1, -1 );*/
        }
        catch ( Exception e )
        {
           /* log.error( "MessageReceiverC.heartbeatRecieved: Error while sending heartbeat to destination -> " + destination );
            MessageLogger.getInstance().log( ISAlertMBean.ALERT_ACTION_ADAPTOR_JMS_PROVIDERSTATUS, "MessageReceiverC.heartbeatRecieved", "Error in sending ProviderStatus message to destination -> " + destination, null );
            if ( log.isDebugEnabled() )
            {
                log.debug( "MessageReceiverC.heartbeatRecieved: Exception", e );
            }*/

        }
    }

    private String createHeartBeatDestKey(ProviderStatus status) {
        String hbDestPrefix = (String) status.getProperty(ISCommonConstants.HB_DEST_PREFIX_KEY);
        hbDestPrefix = hbDestPrefix == null ? AdaptorConstantC.TOIS_MESSAGES : hbDestPrefix;

        return new StringBuilder(hbDestPrefix).append(".").append(status.getProviderShortName()).toString();
    }

    private static String getLocalServerName()
    {
        String hostname = null ;
        try
        {
            hostname = InetAddress.getLocalHost().getHostName() ;
        }
        catch ( UnknownHostException e )
        {
        	log.warn("ResponseHandlerC.getLocalServerName: ServerHostAddress is not found");
        }
        return hostname;
    }

    public void rateRecieved( MarketRate message )
    {
        rateRecieved(message, ISCommonConstants.PROVIDER_TYPE_DEF);
    }
    /**
     * Send market rate to intergration server.
     *
     * @param message
     */
    public void rateRecieved( MarketRate message, String providerType )
    {
    	rateRecieved(message, providerType, 2);
    }
    
    
    public void rateRecieved( MarketRate message, String providerType, int serializerVer )
    {
        stalenessHandler.notifyRateReceived( message );
        sendRate( message, providerType, serializerVer );
    }
    

    static Map<String, Boolean> udpMap = new ConcurrentHashMap();

    /*    protected static boolean isUdpTransport(String providerName) {
             Boolean isUdp = udpMap.get(providerName);
             if (isUdp==null) {
                 isUdp = MessagingUtilsC.isRatesTransportUDP(providerName);
                 udpMap.put(providerName, isUdp);
             }
             return isUdp;
         }*/


    public abstract void sendRate( MarketRate message, String providerType);
    
    public abstract void sendRate( MarketRate message, String providerType, int serializerVer );
    
    /**
     * This method shoud be called directly from StalenessCheckHandler.
     * All others shoud use rateReceived to send rates
     *
     * @param message
     */
    public abstract void sendRate( MarketRate message );

    /**
     * Send deal execution status to integration server.
     * This method is also called byAdaptorManager while sending deal Rejection
     * on verification Timeout when Auto Cancellation is enabled.
     *
     * @param message
     * @throws JMSException
     */
    public void tradeRecieved( TradeResponse message ) throws JMSException
    {
        tradeRecieved( message, true );
    }

    /**
     * Send deal execution status to integration server.
     * This method is also called byAdaptorManager while sending deal Rejection
     * on verification Timeout when Auto Cancellation is enabled.
     *
     * @param message
     * @throws JMSException
     */
    public void tradeRecieved( TradeResponse message, boolean isRemoveTradeInfo ) throws JMSException
    {
        tradeRecieved( message, isRemoveTradeInfo, false );
    }

    /**
     * Process,audit and send the trade response to the queue configured for the trade
     * Remove the trade info for the trade from AdaptorTradeManager cache if isRemoveTradeInfo is set to true
     *
     * @param message
     * @param isRemoveTradeInfo
     * @param isPartOfMultiFill
     */
    public void tradeRecieved( TradeResponse message, boolean isRemoveTradeInfo, boolean isPartOfMultiFill ) throws JMSException
    {
        String tradeId = message.getTradeId();
        log.warn( "MessageReceiverC.tradeRecieved : Received " + message.getClass().getName() + " for tradeId->" + tradeId);

        AdaptorTradeManager.getInstance().processProviderResponse( message );
        //AdaptorAuditC.getInstance().audit( message );
        Map props = new HashMap();
        if( isPartOfMultiFill )
        	props.put( AdaptorConstantC.REQUEST_REFERENCE_ID, tradeId);

        if (isPartOfMultiFill) {
            try {
                IMTPSession imtpSession = AdaptorTradeManager.getInstance().getTradeDestinationSession(tradeId);
                if(imtpSession == null) {
                    //QueueSession session = AdaptorTradeManager.getInstance().acquireSessionForTradeId(tradeId);
                    sendTradeResponseToServer( message, props, null );
                } else {
                    sendTradeResponseToServer( message, props );
                }
            } catch (Exception e) {
                log.error("ResponseHandlerC.tradeRecieved for multifill. not able to get Queue Session from pool for " + tradeId, e);
                sendTradeResponseToServer( message, props );
            }
        }
        else {
            sendTradeResponseToServer( message, props );
        }
        if ( isRemoveTradeInfo )
        {
            AdaptorTradeManager.getInstance().removeTrade(tradeId);
        }
    }

    public void tradePendingRecieved( TradePending message ) throws JMSException
    {
    	tradePendingRecieved( message, false );
    }

    /**
     * Send TradePending message to JMS Queue corresponding to the trade
     *
     * @param message
     */

    public void tradePendingRecieved( TradePending message, boolean isPartOfMultiFill ) throws JMSException
    {
        String tradeId = message.getTradeId();
        if ( log.isDebugEnabled() )
        {
            log.debug( "MessageReceiverC.tradePendingRecieved : Received TradePending for tradeId " + tradeId );
        }
        String tradeDestination = null;
        try
        {
            String serverId = AdaptorTradeManager.getInstance().getServerID( tradeId );
            IMTPSession session = AdaptorTradeManager.getInstance().getTradeDestinationSession(tradeId);
            long t0 = System.nanoTime();
            if(session == null){
            	log.error("MessageReceiverC.sendTradeResponseToServer:TradePendingResponse cannot be sent as IMTP Session is Null " + ":Server:" + serverId + ":Trade:" + tradeId);
            	return;
            	/*
                String brokerId = AdaptorTradeManager.getInstance().getBrokerId( tradeId );
                if ( brokerId == null )
                {
                    log.error( "MessageReceiverC.tradePendingRecieved : TradePending not sent for " + tradeId );
                    return;
                }
                tradeDestination = AdaptorTradeManager.getInstance().getTradeDestination( tradeId );
                if ( tradeDestination == null )
                {
                    log.error( "Trade Response sent before TradePending could be sent. TradePending not sent" );
                    return;
                }
                AlertCheckC.callback( ISAlertMBean.ALERT_ACTION_ADAPTOR_JMS_TRADEPENDING );
                Map props = new HashMap();
                if( isPartOfMultiFill )
                    props.put( AdaptorConstantC.REQUEST_REFERENCE_ID , tradeId );

                if ( adaLoginMgr.isTextMessageEnabled( serverId ) )
                {
                    String msg = message.getToString();
                    props.put( ResponseHandler.OBJECT_TYPE, message.getClass().getName() );
                    JMSManager.getInstance().sendMessageToQueue( brokerId, tradeDestination, msg, props, -1, -1, -1 );
                }
                else
                {
                    JMSManager.getInstance().sendMessageToQueue( brokerId, tradeDestination, message, props, -1, -1, -1 );
                }
                */
            }else{
                AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_TRADERESPONSE);
                IMTPApplicationMessage applicationMessage;
                if(adaLoginMgr.isTextMessageEnabled(serverId)){
                    String msg = message.getToString();
                    applicationMessage = IMTPMessageFactory.borrowApplicationMessage(msg);
                    applicationMessage.addProperty(ResponseHandler.OBJECT_TYPE, message.getClass().getName());
                    applicationMessage.setAppDataType(IMTPApplicationMessage.APP_DATA_TYPE_STRING);
                }else{
                    applicationMessage = IMTPMessageFactory.borrowApplicationMessage(message);
                    applicationMessage.setAppDataType(IMTPApplicationMessage.APP_DATA_TYPE_OBJECT);
                }
                applicationMessage.setAppId(tradeId);
                applicationMessage.setAppSelector("TOIS.TRADES");
                session.getMessageHandler().sendMessage(applicationMessage, isMBean.isTradeResponseImtpMessagePersistenceEnabled());
            }

            log.warn( "MessageReceiverC.tradePendingRecieved:TradePending sent for tradeId->" + tradeId + " took us=" + TimeUnit.MICROSECONDS.convert(System.nanoTime() - t0, TimeUnit.NANOSECONDS)  );
        }
        catch ( Exception e )
        {
            log.error( "MessageReceiverC.tradePendingRecieved: Error while sending trade pending to integration server id " + AdaptorTradeManager.getInstance().getServerID( tradeId ), e );
            if ( log.isDebugEnabled() )
            {
                log.debug( "MessageReceiverC.tradePendingRecieved: Exception", e );
            }
            MessageLogger.getInstance().log( ISAlertMBean.ALERT_ACTION_ADAPTOR_JMS_TRADEPENDING, "MessageReceiverC.tradePendingRecieved", new StringBuffer().append( "Trade Pending message not send to IS. Exception[" ).append( e ).append( "], broker ID[" ).append( AdaptorTradeManager.getInstance().getBrokerId( tradeId ) ).append( "], destination[" ).append( AdaptorTradeManager.getInstance().getTradeDestination( tradeId ) ).append( "], TXID[" ).append( tradeId ).append( "]" ).toString(), null );

        }
    }

    /**
     * Send deal execution status to integration server.
     *
     * @param message
     * @throws JMSException
     */
    public void tradeRecieved( TradeResponses message ) throws Exception
    {
        String serverId = null;
        String tradeDestination = null;
        try
        {
            if (ISCommonUtilC.isWarmUpObject(message)) {
                return;
            }
            Iterator<TradeResponse> iter = message.getTradeResponses().iterator();
            if ( iter.hasNext() )
            {
                TradeResponse response = ( TradeResponse ) iter.next();
                String tradeId = response.getTradeId();
                long t0 = System.currentTimeMillis();
                log.warn(new StringBuilder(100).append("MessageReceiverC.tradeReceived(TradeResponses) : Received for tradeId->").append(tradeId).toString());
                serverId = AdaptorTradeManager.getInstance().getServerID( tradeId );
                AdaptorTradeManager.getInstance().processProviderResponse( response );
                long t1 = System.currentTimeMillis();
                IMTPSession session = AdaptorTradeManager.getInstance().getTradeDestinationSession(tradeId);
                long t2 = System.currentTimeMillis();
                if(session == null){
                	log.error("MessageReceiverC.sendTradeResponseToServer:Trade cannot be sent as IMTP Session is Null " + ":Server:" + serverId + ":Trade:" + tradeId);
                	return;
                	/*
                    AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_ADAPTOR_JMS_TRADERESPONSE);
                    tradeDestination = AdaptorTradeManager.getInstance().getTradeDestination(tradeId);
                    if ( adaLoginMgr.isTextMessageEnabled( serverId ) )
                    {
                        try
                        {
                            String msg = message.getToString();
                            Map<String,String> props = new HashMap<String,String>();
                            props.put( ResponseHandler.OBJECT_TYPE, message.getClass().getName() );
                            JMSManager.getInstance().sendMessageToQueue(
                                    AdaptorTradeManager.getInstance().getBrokerId( tradeId ),
                                    AdaptorTradeManager.getInstance().getTradeDestination( tradeId ), msg, props, -1, -1,
                                    -1 );

                        }
                        catch ( Exception ex )
                        {
                            log.error(
                                    "ResponseHandlerC.tradeReceived: Exception while sending TradeResponses for tradeId "
                                            + tradeId, ex );
                        }

                    }
                    else
                        JMSManager.getInstance()
                                .sendMessageToQueue(AdaptorTradeManager.getInstance().getBrokerId(tradeId),
                                        AdaptorTradeManager.getInstance().getTradeDestination(tradeId), message, null,
                                        -1, -1, -1);
                                        */
                } else{
                    AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_TRADERESPONSE);
                    IMTPApplicationMessage applicationMessage;
                    if(adaLoginMgr.isTextMessageEnabled(serverId)){
                        long st = System.currentTimeMillis();
                        String msg = message.getToString();
                        applicationMessage = IMTPMessageFactory.borrowApplicationMessage(msg);
                        applicationMessage.addProperty( ResponseHandler.OBJECT_TYPE, message.getClass().getName() );
                        applicationMessage.setAppDataType(IMTPApplicationMessage.APP_DATA_TYPE_STRING);
                        long et = System.currentTimeMillis();
                        if ((et - st) > 1) log.info("RHC.tradeReceived: Serialize TimeTaken=" + (et - st) + " ms");
                    }else{
                        applicationMessage = IMTPMessageFactory.borrowApplicationMessage(message);
                        applicationMessage.setAppDataType(IMTPApplicationMessage.APP_DATA_TYPE_OBJECT);
                    }
                    applicationMessage.setAppId(tradeId);
                    applicationMessage.setAppSelector("TOIS.TRADES");
                    tradeDestination = "IMTPSession:" + session.getSessionId();
                    session.getMessageHandler().sendMessage(applicationMessage, isMBean.isTradeResponseImtpMessagePersistenceEnabled());
                }
                log.warn(new StringBuilder(200)
                        .append("MessageReceiverC.tradeReceived: TradeResponses sent for tradeId->").append(tradeId)
                        .append(". TimeTaken (ms) to procRes=").append(t1 - t0)
                        .append(", detSess=").append(t2 - t1)
                        .append(", sendMsg=").append(System.currentTimeMillis() - t2).toString());
            }
        }
        catch ( Exception ex )
        {
            Iterator<TradeResponse> iter = message.getTradeResponses().iterator();
            if ( iter.hasNext() )
            {
                TradeResponse response = ( TradeResponse ) iter.next();
                AlertLoggerFactory.getMessageLogger().log( ISAlertMBean.ALERT_ACTION_ADAPTOR_JMS_TRADERESPONSE, "MessageReceiverC.tradeReceived(TradeResponses)", new StringBuffer().append( "Verification/Rejection message not send to IS. Exception[" ).append( ex ).append( "], broker ID[" ).append( AdaptorTradeManager.getInstance().getBrokerId( response.getTradeId() ) ).append( "], destination[" ).append( tradeDestination ).append( "], TXID[" ).append( response.getTradeId() ).append( "]" ).toString(), null );

            }
            log.error( "MessageReceiverC.tradeRecieved: Error while sending trade verification to integration server id " + serverId, ex );
            throw ex;
        }
        finally
        {
            Iterator<TradeResponse> iter = message.getTradeResponses().iterator();
            if ( iter.hasNext() )
            {
                TradeResponse response = ( TradeResponse ) iter.next();
                AdaptorTradeManager.getInstance().removeTrade( response.getTradeId() );
            }
        }
    }

    /**
     * Send Message to Heartbeat topic to IS.
     *
     * @param message
     * @param providerId
     */
    public void sendMessage( ISMessage message, String providerId )
    {
        try
        {
            log.info("This method is no longer supported. Dumping stack trace now");
            Thread.dumpStack();
			/*if ( adaLoginMgr.isTextMessageEnabledForAllServers() )
			{
				String msg = message.getToString();
				Map<String,String> props = new HashMap<String,String>();
				props.put( ResponseHandler.OBJECT_TYPE, message.getClass().getName() );
				JMSManager.getInstance().sendMessage( getHeartbeatDestination( providerId ), msg, props, -1, -1, -1 );
			}
			else
            JMSManager.getInstance().sendMessage( getHeartbeatDestination( providerId ), message, null, -1, -1, -1 );
*/        }
        catch ( Exception e )
        {
            log.error( "MessageReceiverC.sendMessage: Error while sending message to integration server.", e );
        }
    }

    /**
     * Send the object message to the given queueName on the given brokerId
     *
     * @param message
     * @param queueName
     * @param brokerId
     */
    public void sendMessage( ISMessage message, String queueName, String brokerId ) throws Exception
    {
        try
        {
			// if(textMsg)
			// {
			// String msg = message.getToString();
			// Map props = new HashMap();
			// props.put("ResponseHandler.OBJECT_TYPE", message.getClass().getName());
			// JMSManager.getInstance().sendMessageToQueue(brokerId, queueName,
			// msg, props, -1, -1, -1);
			//
			// }
			// else
            //JMSManager.getInstance().sendMessageToQueue( brokerId, queueName, message, null, -1, -1, -1 );
            log.info("This method is no longer supported. Dumping stack trace now");
            Thread.dumpStack();
        }
        catch ( Exception e )
        {
            log.error( "MessageReceiverC.sendMessage : Error in sending message to Broker=" + brokerId + ", Queue=" + queueName + " Exception : " + e );
            if ( log.isDebugEnabled() )
            {
                log.debug( "MessageReceiverC.sendMessage: Exception", e );
            }
            throw new Exception();
        }
    }

    /**
     * Send StreamUpdate message to IntegrationServer
     *
     * @param message
     * @param providerId
     */
   /* public void streamUpdateReceived( StreamUpdate message, String providerId )
    {
        try
        {
			if ( adaLoginMgr.isTextMessageEnabledForAllServers() )
			{
				String msg = message.getToString();
				Map<String,String> props = new HashMap<String,String>();
				props.put( ResponseHandler.OBJECT_TYPE, message.getClass().getName() );
				JMSManager.getInstance().sendMessage( getHeartbeatDestination( providerId ), msg, props, -1, -1, -1 );
			}
			else
            JMSManager.getInstance().sendMessage( getHeartbeatDestination( providerId ), message, null, -1, -1, -1 );
        }
        catch ( Exception e )
        {
            log.error( "MessageReceiverC.streamUpdateReceived: Error while sending StreamUpdate message to integration server." + e );
            if ( log.isDebugEnabled() )
            {
                log.debug( "MessageReceiverC.streamUpdateReceived: Exception", e );
            }
        }
    }*/

    /**
     * Send RFSMarketRate message to IS.
     *
     * @param message
     */
    public void rfsRateRecieved(RFSMarketRate message) {
        if (log.isDebugEnabled()) {
            try {
                log.debug("ResponseHandlerC.rfsRateRecieved : MarketRateString = " + message.getToString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        String requestId = message.getRequestId();
        String providerShortName = message.getProviderShortName();
        String key = providerShortName + "-" + requestId;
        String brokerId = null;
        String rateDestination = null;
        IMTPSession session = RFSAdaptorManager.getInstance().getTradeDestinationSession(requestId, providerShortName);
        Properties msgProp = new Properties();
        String msg = null;
        try {
            msgProp.setProperty(OBJECT_TYPE, message.getClass().getName());
            msg = message.getToString();
            if (session == null ) {
            	log.warn("ResponseHandlerC.rfsRateRecieved : IMTP Session null : rate can not be sent over IMTP for Request:" + key);          	
            	return;
            } else {
                AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_TRADERESPONSE);
                JSONGeneratorHolder generator = new JSONGeneratorHolder(RFSMessageType.RFSMarketRate);
                generator.getMsgHolder().setRFSMarketRate((RFSMarketRateC)message);
                sendOverImtp(message,requestId,session,generator.getMessageString(), ResponseHandler.FORMAT_JSON);

            }
        } catch (Exception e) {
            log.error("ResponseHandlerC.rfsRateRecieved : Error while creating rfs message ", e);
            MessageLogger.getInstance().log(session == null ? ISAlertMBean.ALERT_ACTION_ADAPTOR_JMS_RFS_MARKETRATE : ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_RFS_MARKETRATE, "ResponseHandlerC.rfsRateRecieved", new StringBuffer().append("RFSMarketRate message not sent to IS. Exception[").append(e).append("], broker ID[").append(brokerId).append("], destination[").append(rateDestination).append("], TXID[").append(key).append("]").toString(), null);
        }
    }

    private void sendOverImtp(ISMessage message, String requestId, IMTPSession session, String data, String messageFormat) throws IOException {
        IMTPApplicationMessage applicationMessage = IMTPMessageFactory.borrowApplicationMessage(data);
        applicationMessage.addProperty(ResponseHandler.OBJECT_TYPE, message.getClass().getName());
        applicationMessage.addProperty(ResponseHandler.STRING_FORMAT, messageFormat);
        applicationMessage.setAppDataType(IMTPApplicationMessage.APP_DATA_TYPE_STRING);
        applicationMessage.setAppId(requestId);
        applicationMessage.setAppSelector("TOIS.TRADES");
        session.getMessageHandler().sendMessage(applicationMessage, false);
    }
    /**
     * Send RFSTradeResponse message to IS.
     *
     * @param message
     * @throws JMSException
     */
    public void rfsTradeRecieved( RFSTradeResponse message ) throws JMSException
    {
        rfsTradeRecieved( message, true );
    }

    /**
     * Send RFSTradeResponse message to IS. Decides
     * whether to remove cahce for this trade or not.
     *
     * @param message
     * @param isRemoveTradeInfo
     */
    public void rfsTradeRecieved( RFSTradeResponse message, boolean isRemoveTradeInfo )
    {
        String provider = message.getProvider();
        String tradeId = message.getTradeId();
        String key = provider + "-" + tradeId;
        log.warn( "MessageReceiverC.rfsTradeRecieved : Received " + message.getClass().getName() + " for " + key );
        RFSAdaptorManager.getInstance().processProviderResponse( message );
        String isTrader = ( String ) message.getProperty( "Trader" );
//        if ( isTrader == null || ( !"true".equalsIgnoreCase( isTrader ) ) )
//        {
//            AdaptorAuditC.getInstance().audit( message );
//        }
        sendRFSTradeResponseToServer( message );
        if ( isRemoveTradeInfo )
        {
            RFSAdaptorManager.getInstance().removeRFSSubscribe( tradeId, provider );
            RFSAdaptorManager.getInstance().removeRFSTradeRequest( tradeId, provider );
            RFSAdaptorManager.getInstance().removeLock( tradeId, provider );
        }
    }

    /**
     * Send RFSTradePending message to server
     *
     * @param message
     */
    public void rfsTradePendingRecieved(RFSTradeResponse message) {

        String provider = message.getProvider();
        String tradeId = message.getTradeId();
        String key = provider + "-" + tradeId;
        if (log.isDebugEnabled()) {
            log.debug("MessageReceiverC.rfsTradePendingRecieved : Received TradePending for " + key);
        }
        String brokerId = null;
        String tradeDestination = null;
        IMTPSession session = RFSAdaptorManager.getInstance().getTradeDestinationSession(tradeId,provider);
        try {
            Properties msgProp = new Properties();
            msgProp.setProperty(OBJECT_TYPE, message.getClass().getName());
            String msg = message.getToString();
            /*
            brokerId = RFSAdaptorManager.getInstance().getBrokerId(tradeId, provider);
            tradeDestination = RFSAdaptorManager.getInstance().getTradeDestination(tradeId, provider);
            
            if (brokerId == null) {
                log.error("MessageReceiverC.rfsTradePendingRecieved : TradePending not sent for " + key);
                return;
            }
            if (tradeDestination == null) {
                log.error("MessageReceiverC.rfsTradePendingRecieved :Trade Response sent before TradePending could be sent. TradePending not sent");
                return;
            }
            */
            if (session == null) {
            	log.error("MessageReceiverC.rfsTradePendingRecieved:Trade Response cannot be sent as IMTP session is Null for Trade:" + key);
            	MessageLogger.getInstance().log(ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_RFS_TRADEPENDING, "MessageReceiverC.rfsTradePendingRecieved", new StringBuffer().append("Trade Pending message not sent to IS. Reason[IMTP Session Null], TXID[").append(key).append("]").toString(), null);
            	return;
            } else {
                AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_RFS_TRADEPENDING);
                JSONGeneratorHolder generator = getJSONGeneratorHolder(message);
                //JSONGeneratorHolder generator = new JSONGeneratorHolder(RFSMessageType.RFSTradePending);
                //generator.getMsgHolder().setRFSTradePending((RFSTradePendingC)message);
                sendOverImtp(message,tradeId,session,generator.getMessageString(), ResponseHandler.FORMAT_JSON);

            }
            log.warn("MessageReceiverC.rfsTradePendingRecieved:RFSTradePending sent for key->" + key);
        } catch (Exception e) {
            log.error("MessageReceiverC.rfsTradePendingRecieved: Error while sending trade pending to integration server id " + RFSAdaptorManager.getInstance().getServerID(tradeId, provider) + "," , e);
            MessageLogger.getInstance().log(session == null ? ISAlertMBean.ALERT_ACTION_ADAPTOR_JMS_RFS_TRADEPENDING : ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_RFS_TRADEPENDING, "MessageReceiverC.rfsTradePendingRecieved", new StringBuffer().append("Trade Pending message not sent to IS. Exception[").append(e).append("], broker ID[").append(brokerId).append("], destination[").append(tradeDestination).append("], TXID[").append(key).append("]").toString(), null);

        }
    }

    /**
     * Send RFSResponseMessage to IS. These are typically non-trade messages.
     *
     * @param message
     */
    public void sendRFSResponseMessage( RFSResponseMessage message )
    {
        sendRFSResponseMessage( message, true );
    }

    /**
     * Send RFSResponseMessage to IS. These are typically non-trade messages.
     *
     * @param message
     */
    public void sendRFSResponseMessage( RFSResponseMessage message, boolean removeRFSRequest )
    {
        String provider = message.getProvider();
        String requestId = message.getRequestReferenceId();
        String key = provider + "-" + requestId;
        if ( log.isDebugEnabled() )
        {
            log.debug( "MessageReceiverC.sendRFSResponseMessage for tradeId " + key );
        }
        String brokerId = null;
        String tradeDestination = null;
        IMTPSession session = RFSAdaptorManager.getInstance().getTradeDestinationSession(requestId,provider);
        try
        {
            Properties msgProp = new Properties();
            msgProp.setProperty( OBJECT_TYPE, message.getClass().getName() );
            String msg = message.getToString();
            /*
            brokerId = RFSAdaptorManager.getInstance().getBrokerId( requestId, provider );
            tradeDestination = RFSAdaptorManager.getInstance().getTradeDestination( requestId, provider );
            if ( brokerId == null )
            {
                log.warn( "MessageReceiverC.sendRFSResponseMessage :brokerId not found for " + key );
                return;
            }
            if ( tradeDestination == null )
            {
                log.warn( "MessageReceiverC.sendRFSResponseMessage :tradeDestination not found for " + key );
                return;
            }
            */
            if (session == null) {
            	log.error("MessageReceiverC.sendRFSResponseMessage:RFSResponseMesage cannot be sent as IMTP session is Null for Request :" + key);
            	MessageLogger.getInstance().log( ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_RFS_RESPONSEMESSAGE, "MessageReceiverC.sendRFSResponseMessage", new StringBuffer().append( "RFSResponse message not sent to IS. Reason[IMTP Session Null], TXID[" ).append( key ).append( "]" ).toString(), null );
            	return;
            } else {
                AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_RFS_RESPONSEMESSAGE);

                JSONGeneratorHolder generator = getJSONGeneratorHolder(message);
                sendOverImtp(message, requestId, session, generator.getMessageString(), ResponseHandler.FORMAT_JSON);

            }
            log.warn( "MessageReceiverC.sendRFSResponseMessage:RFSResponseMessage sent for key->" + key );
        }
        catch ( Exception e )
        {
            log.error( "MessageReceiverC.sendRFSResponseMessage : Error in sending RFS Response message for :" + key + ":Exception : ", e );
            MessageLogger.getInstance().log( session == null ? ISAlertMBean.ALERT_ACTION_ADAPTOR_JMS_RFS_RESPONSEMESSAGE : ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_RFS_RESPONSEMESSAGE, "MessageReceiverC.sendRFSResponseMessage", new StringBuffer().append( "RFSResponse message not sent to IS. Exception[" ).append( e ).append( "], broker ID[" ).append( brokerId ).append( "], destination[" ).append( tradeDestination ).append( "], TXID[" ).append( key ).append( "]" ).toString(), null );
        }
        finally
        {
            if ( removeRFSRequest )
            {
                RFSAdaptorManager.getInstance().removeRFSSubscribe( requestId, provider );
                RFSAdaptorManager.getInstance().removeRFSTradeRequest( requestId, provider );
                RFSAdaptorManager.getInstance().removeLock( requestId, provider );
            }
        }
    }

    /**
     * Get heartbeat topic
     *
     * @param providerShortName
     * @return heart beat destination
     */
    private String getHeartbeatDestination( String providerShortName )
    {
        StringBuffer topic = new StringBuffer( AdaptorConstantC.TOIS_MESSAGES );
        topic.append( "." ).append( providerShortName );
        return topic.toString();
    }

    public void cacheHeartBeatDestinations() throws Exception
    {
        //String destinationPrefix = AdaptorConfigurationFactory.getAdaptorConfigurationMBean().getHeartbeatDestinationJNDIPrefix();
        /*Collection<JNDIEntry> destColl = JMSXMLMBeanC._getInstance().getDestinationList();
        if ( destColl != null )
        {
            Iterator<JNDIEntry> iter = destColl.iterator();
            while ( iter.hasNext() )
            {
                JNDIEntry destination = ( JNDIEntry ) iter.next();
                String jndiName = destination.getJndiName();
                if ( jndiName.contains( AdaptorConstantC.TOIS_MESSAGES ) )
                {
                    String heartBeatDestinationsKey = null;
                    if ( jndiName.contains( "-" ) )
                    {
                        heartBeatDestinationsKey = jndiName.substring( 0, jndiName.indexOf( "-" ) );
                    }
                    else
                    {
                        heartBeatDestinationsKey = jndiName;
                    }
                    synchronized ( heartBeatDestinations )
                    {
                        if ( heartBeatDestinations.get( heartBeatDestinationsKey ) == null )
                        {
                            heartBeatDestinations.put( heartBeatDestinationsKey, new ArrayList<String>() );
                        }
                        List<String> topicList = heartBeatDestinations.get( heartBeatDestinationsKey );
                        if ( !topicList.contains( jndiName ) )
                        {
                            topicList.add( jndiName );
                            if( log.isDebugEnabled() )
                            {
                            	log.debug( "MessageReceiverC.cacheHeartBeatDestinations : Added HeartBeat Destination -> JNDIName=" + jndiName + ",JMSName=" + destination.getJmsName() );
                            }
                        }
                    }
                }
            }
        }*/
    }


    public void reloadHeartBeatDestinations(String providerName)
    {
        //String destinationPrefix = AdaptorConfigurationFactory.getAdaptorConfigurationMBean().getHeartbeatDestinationJNDIPrefix();
        /*Collection<JNDIEntry> destColl = JMSXMLMBeanC._getInstance().getDestinationList();
        if ( destColl != null )
        {
        	String searchKey = AdaptorConstantC.TOIS_MESSAGES + '.' +providerName;
            Iterator<JNDIEntry> iter = destColl.iterator();
            while ( iter.hasNext() )
            {
                JNDIEntry destination = ( JNDIEntry ) iter.next();
                String jndiName = destination.getJndiName();
                if ( jndiName.contains( searchKey ) )
                {
                    String heartBeatDestinationsKey = null;
                    if ( jndiName.contains( "-" ) )
                    {
                        heartBeatDestinationsKey = jndiName.substring( 0, jndiName.indexOf( "-" ) );
                    }
                    else
                    {
                        heartBeatDestinationsKey = jndiName;
                    }
                    synchronized ( heartBeatDestinations )
                    {
                        if ( heartBeatDestinations.get( heartBeatDestinationsKey ) == null )
                        {
                            heartBeatDestinations.put( heartBeatDestinationsKey, new ArrayList<String>() );
                        }
                        List<String> topicList = heartBeatDestinations.get( heartBeatDestinationsKey );
                        if ( !topicList.contains( jndiName ) )
                        {
                            topicList.add( jndiName );
                            log.warn( "ResponseHandlerC.reloadHeartBeatDestinations : Added HeartBeat Destination -> JNDIName=" + jndiName + ",JMSName=" + destination.getJmsName() );
                        }
                        else
                        {
                        	log.warn( "ResponseHandlerC.reloadHeartBeatDestinations : Already in cache - HeartBeat Destination -> JNDIName=" + jndiName + ",JMSName=" + destination.getJmsName() );
                        }
                    }
                }
            }
        }*/
    }

    /**
     * Send the trade response message to the JMS Queue corresponding to the trade
     *
     * @param message
     */
    private void sendTradeResponseToServer( TradeResponse message, Map props )
    {
        String tradeId = null;
        String tradeDestination = null;
        try
        {
            tradeId = message.getTradeId();
			String serverId = AdaptorTradeManager.getInstance().getServerID( tradeId );
            IMTPSession session = AdaptorTradeManager.getInstance().getTradeDestinationSession(tradeId);
            long t0 = System.nanoTime();
            if(session == null){
            	log.error("MessageReceiverC.sendTradeResponseToServer:TradeResponse cannot be sent as IMTP Session is Null " 
            																			+ ":Server:" + serverId + ":Trade:" + tradeId);
            	return;
            	/*
                AlertCheckC.callback( ISAlertMBean.ALERT_ACTION_ADAPTOR_JMS_TRADERESPONSE );
                if ( adaLoginMgr.isTextMessageEnabled( serverId ) )
                {
                    String msg = message.getToString();
                    props.put( ResponseHandler.OBJECT_TYPE, message.getClass().getName() );
                    tradeDestination = AdaptorTradeManager.getInstance().getTradeDestination(tradeId);
                    JMSManager.getInstance().sendMessageToQueue( AdaptorTradeManager.getInstance().getBrokerId( tradeId ),
                            tradeDestination, msg, props, -1, -1, -1 );

                }
                else
                    JMSManager.getInstance().sendMessageToQueue( AdaptorTradeManager.getInstance().getBrokerId( tradeId ),
                            tradeDestination, message, props, -1, -1, -1 );
                            */
            }else{
                AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_TRADERESPONSE);
                IMTPApplicationMessage applicationMessage;
                if(adaLoginMgr.isTextMessageEnabled(serverId)){
                    String msg = message.getToString();
                    applicationMessage = IMTPMessageFactory.borrowApplicationMessage(msg);
                    applicationMessage.addProperty( ResponseHandler.OBJECT_TYPE, message.getClass().getName() );
                    applicationMessage.setAppDataType(IMTPApplicationMessage.APP_DATA_TYPE_STRING);
                }else{
                    applicationMessage = IMTPMessageFactory.borrowApplicationMessage(message);
                    applicationMessage.setAppDataType(IMTPApplicationMessage.APP_DATA_TYPE_OBJECT);
                }
                applicationMessage.setAppId(tradeId);
                applicationMessage.setAppSelector("TOIS.TRADES");
                applicationMessage.addProperties(props);
                tradeDestination = "IMTPSession:" + session.getSessionId();
                session.getMessageHandler().sendMessage(applicationMessage, isMBean.isTradeResponseImtpMessagePersistenceEnabled());
           }

            log.warn( "MessageReceiverC.sendTradeResponseToServer:TradeResponse sent for tradeId->" + tradeId + " took us=" + TimeUnit.MICROSECONDS.convert(System.nanoTime() - t0, TimeUnit.NANOSECONDS));
        }
        catch ( Exception ex )
        {
            log.error( "MessageReceiverC.sendTradeResponseToServer: Error while sending trade response to integration server id " + AdaptorTradeManager.getInstance().getServerID( tradeId ) + "," + ex );
            if ( log.isDebugEnabled() )
            {
                log.debug( "MessageReceiverC.sendTradeResponseToServer: Exception", ex );
            }
            AlertLoggerFactory.getMessageLogger().log( ISAlertMBean.ALERT_ACTION_ADAPTOR_JMS_TRADERESPONSE, "MessageReceiverC.sendTradeResponseToServer", new StringBuffer().append( "Verification/Rejection message not sent to IS. Exception[" ).append( ex ).append( "], broker ID[" ).append( AdaptorTradeManager.getInstance().getBrokerId( tradeId ) ).append( "], destination[" ).append(tradeDestination).append( "], TXID[" ).append( tradeId ).append( "]" ).toString(), null );
        }
    }


    @Deprecated
    private void sendTradeResponseToServer(TradeResponse message, Map props, QueueSession session) {
        String tradeId = message.getTradeId();
        String serverId = AdaptorTradeManager.getInstance().getServerID(tradeId);
        String brokerId = AdaptorTradeManager.getInstance().getBrokerId(tradeId);
        String tradeDestination = AdaptorTradeManager.getInstance().getTradeDestination(tradeId);
        try {
            log.info("This method is no longer supported. Dumping stack trace now");
            Thread.dumpStack();
            /*AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_ADAPTOR_JMS_TRADERESPONSE);
            HiPerfJMSManager jmsManager = (HiPerfJMSManager) JMSManager.getInstance();
            JNDIEntry destinationElement = JMSMBeanC.getInstance().getJmsNameJNDIEntry(brokerId, tradeDestination);
            int deliveryMode = DeliveryMode.NON_PERSISTENT;
            int priority = Message.DEFAULT_PRIORITY;
            long timeToLive = Message.DEFAULT_TIME_TO_LIVE;
            if (destinationElement != null) {
                deliveryMode = destinationElement.getDeliveryMode();
                priority = destinationElement.getPriority();
                timeToLive = destinationElement.getTimeToLive();
            }

            synchronized (session) {
                if (adaLoginMgr.isTextMessageEnabled(serverId)) {
                    props.put(ResponseHandler.OBJECT_TYPE, message.getClass().getName());
                    //check for message parameters
                    String txtMessage = message.getToString();

                    TextMessage msg = session.createTextMessage();
                    msg.setText(txtMessage);

                    jmsManager._applyJMSProperties(props, msg, false);
                    javax.jms.Queue queue = jmsManager.
                            _createQueue(session, tradeDestination);
                    QueueSender sender = session.createSender(queue);
                    sender.send(msg, deliveryMode, priority, timeToLive);
                } else {
                    Message msg = session.createObjectMessage();
                    ((javax.jms.ObjectMessage) msg).setObject(message);
                    jmsManager._applyJMSProperties(props, msg, false);
                    javax.jms.Queue queue = jmsManager.
                            _createQueue(session, tradeDestination);
                    QueueSender sender = session.createSender(queue);
                    sender.send(msg, deliveryMode, priority, timeToLive);
                }
            }
            log.warn("MessageReceiverC.sendTradeResponseToServer:TradeResponse sent for tradeId->" + tradeId);
      */  }
        catch (Exception ex) {
            log.error(
                    "MessageReceiverC.sendTradeResponseToServer: Error while sending trade response to integration server id " +
                    AdaptorTradeManager.getInstance().getServerID(tradeId) + "," + ex);
            if (log.isDebugEnabled()) {
                log.debug("MessageReceiverC.sendTradeResponseToServer: Exception", ex);
            }
            AlertLoggerFactory.getMessageLogger().log(ISAlertMBean.ALERT_ACTION_ADAPTOR_JMS_TRADERESPONSE,
                                            "MessageReceiverC.sendTradeResponseToServer", new StringBuffer()
                            .append("Verification/Rejection message not sent to IS. Exception[").append(ex)
                            .append("], broker ID[").append(brokerId)
                            .append("], destination[")
                            .append(tradeDestination).append("], TXID[")
                            .append(tradeId).append("]").toString(), null);
        }
    }

    /**
     * Send RFSTradeResponse to server
     *
     * @param message
     */
    private void sendRFSTradeResponseToServer(RFSTradeResponse message) {
        String provider = message.getProvider();
        String tradeId = message.getTradeId();
        String key = provider + "-" + tradeId;
        String brokerId = null;
        String tradeDestination = null;
        IMTPSession session = RFSAdaptorManager.getInstance().getTradeDestinationSession(tradeId, provider);
        try {
            Properties msgProp = new Properties();
            msgProp.setProperty(OBJECT_TYPE, message.getClass().getName());
            String msg = message.getToString();
            //brokerId = RFSAdaptorManager.getInstance().getBrokerId(tradeId, provider);
            //tradeDestination = RFSAdaptorManager.getInstance().getTradeDestination(tradeId, provider);
            if (session == null) {
            	log.error("MessageReceiverC.sendRFSTradeResponseToServer: RFSTradeResponse cannot be sent as IMTP Session is Null for Trade :" + tradeId);
            	MessageLogger.getInstance().log(ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_RFS_TRADERESPONSE, "MessageReceiverC.sendRFSTradeResponseToServer", new StringBuffer().append("Verification/Rejection message not sent to IS. Reason[IMTP Session Null], TXID[").append(key).append("]").toString(), null);
            	return;
            } else {
                AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_RFS_TRADERESPONSE);

                JSONGeneratorHolder generator = getJSONGeneratorHolder(message);
                sendOverImtp(message,tradeId,session,generator.getMessageString(), ResponseHandler.FORMAT_JSON);

            }
        } catch (Exception ex) {
            log.error("MessageReceiverC.sendRFSTradeResponseToServer: Error while sending trade response to integration server id " + RFSAdaptorManager.getInstance().getServerID(tradeId, provider) + ",", ex);
            AlertLoggerFactory.getMessageLogger().log(session == null ? ISAlertMBean.ALERT_ACTION_ADAPTOR_JMS_RFS_TRADERESPONSE : ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_RFS_TRADERESPONSE, "MessageReceiverC.sendRFSTradeResponseToServer", new StringBuffer().append("Verification/Rejection message not sent to IS. Exception[").append(ex).append("], broker ID[").append(brokerId).append("], destination[").append(tradeDestination).append("], TXID[").append(key).append("]").toString(), null);
        }
    }

    private static class ByteBufferFactory implements PoolFactory<ByteBuffer>
    {
        int capacity = 2048; //default to 1k buffer.
        boolean isDirect;

        public ByteBufferFactory( int capacity, boolean isDirect )
        {
            this.capacity = capacity;
            this.isDirect = isDirect;
        }

        public ByteBuffer make()
        {
            if ( isDirect )
            {
                return ByteBuffer.allocateDirect( capacity );
            }
            else
            {
                return ByteBuffer.allocate( capacity );
            }
        }

        public void recycle( ByteBuffer pooled )
        {
            pooled.clear();
        }
    }

    protected class MulticastSocketFactory implements PoolFactory<MulticastSocket>
    {

        public MulticastSocketFactory()
        {
        }

        public MulticastSocket make()
        {
            MulticastSocket mcastSocket = null;
            try
            {
                mcastSocket = new MulticastSocket();
                mcastSocket.setTimeToLive(ConfigurationFactory.getServerMBean().getMulticastTTL());
            }
            catch ( Exception e )
            {
                log.error( "Not able to create multicast Socket:", e );
            }
            return mcastSocket;
        }

        public void recycle( MulticastSocket pooled )
        {
        }
    }

    public abstract void clearProviderStreamCache( String providerName);

	public Map<String, List<String>> getHeartBeatDestinations()
	{
		return heartBeatDestinations;
	}

    private JSONGeneratorHolder getJSONGeneratorHolder(RFSResponseMessage message){
        JSONGeneratorHolder holder = null;
        if(message instanceof RFSTradePendingC){
            holder = new JSONGeneratorHolder(RFSMessageType.RFSTradePending);
            holder.getMsgHolder().setRFSTradePending((RFSTradePendingC)message);
        }else if(message instanceof RFSTradeRejectC){
            holder = new JSONGeneratorHolder(RFSMessageType.RFSTradeReject);
            holder.getMsgHolder().setRFSTradeReject((RFSTradeRejectC)message);
        }else if(message instanceof RFSTradeVerifyC){
            holder = new JSONGeneratorHolder(RFSMessageType.RFSTradeVerify);
            holder.getMsgHolder().setRFSTradeVer((RFSTradeVerifyC)message);
        }else if(message instanceof RFSTradeResponseC){
            holder = new JSONGeneratorHolder(RFSMessageType.RFSTradeResponse);
            holder.getMsgHolder().setRFSTradeResponse((RFSTradeResponseC)message);
        }else if(message instanceof RFSResponseMessageC){
            holder = new JSONGeneratorHolder(RFSMessageType.RFSResponseMessage);
            holder.getMsgHolder().setRFSRespMessage((RFSResponseMessageC)message);
        }else{
            log.error("NO JSON Serializer configured for "+message.getClass());
        }
        return holder;
    }


    public static class JSONGeneratorHolder {
        StringWriter writer;
        JsonGenerator jg;
        RFSMessageJsonSerializerFactory.RFSMessageHolder msgHolder;
        private static JsonFactory jsonFactory = new JsonFactory();
        static Log log = LogFactory.getLog( JSONGeneratorHolder.class );
        Serializer sz;

        public JSONGeneratorHolder(RFSMessageType messageType) {
            try {
                writer = new StringWriter();
                sz = RFSMessageJsonSerializerFactory.getInstance().getRFSSerializer(messageType);
                jg = jsonFactory.createJsonGenerator(writer);
                msgHolder = new RFSMessageJsonSerializerFactory.RFSMessageHolder();

            } catch (Exception ex) {
                log.error("Error creating Generator for " + messageType, ex);
            }
        }

        public RFSMessageJsonSerializerFactory.RFSMessageHolder getMsgHolder() {
            return this.msgHolder;
        }

        public String getMessageString() throws Exception {
            if(msgHolder.getMessage() == null){
                throw new Exception("No message has been set in the holder for serialization");
            }
            jg.writeStartObject();
            sz.serialize(msgHolder, jg, 0);
            jg.writeEndObject();
            jg.flush();
            jg.close();
            return writer.getBuffer().toString();
        }
    }
	public MulticastHeartbeatSender getHeartBeatSender() {
		return heartBeatSender;
	}

    public boolean sendRatesToMDF(MVBrokerQuote quote, String orgName, String ccyPair){
        return false;
    }

}
