package com.integral.adaptor;

/**
 * constants used across all the adaptors.
 * <AUTHOR> Development Corp.
 */
public class AdaptorConstantC
{

	//Used for audit
	public static final String TRD_VERIFY = "TrdVerify";
	public static final String TRD_REJECTED = "TrdRejected";
	public static final String COMPONENT = "IS";
	public static final String TRD_REJ_BY_INTEGRAL = "Rejected by Integral";
	public static final String VERIFICATION_TIMED_OUT = "deal verification timeout";

	//rate status active
	public static final String RATE_ACTIVE = "ACTIVE";
	public static final String RATE_INACTIVE = "INACTIVE";

	//Standard log message constituents. Used by log file monitoring scripts.
	public static final String TRADE_SUBMITTED = "TRADE-SUBMITTED";
	public static final String TRADE_VERIFIED = "TRADE-VERIFIED";
	public static final String TRADE_REJECTED = "TRADE-REJECTED";
	public static final String ADAPTER_UP = "ADAPTER-UP";
	public static final String ADAPTER_DOWN = "ADAPTER-DOWN";

	//Misc
	public static final String GUID = "GUID";
	public static final String EVENT_MAP = "EVTMAP";
	public static final String EVENT_MAP_DELIM = "|";
	public static final String EVENT_MAP_KEY_DELIM = "~";
	public static final String EVENT_TIME_PROVIDER_SUBMIT_DEAL = "ProviderSubmitDeal";
	public static final String EVENT_TIME_DISP_ADAPTER_REC_ACC = "AcceptanceReceivedByAdapter";
	public static final String EVENT_TIME_DISP_ACC_SENT_TO_PROVIDER = "AcceptanceSentToProvider";
	public static final String EVENT_TIME_ADAPTOR_TRADE_REJECTED = "AdaptorProviderTradeRejected";
	public static final String EVENT_TIME_ADAPTOR_TRADE_VERIFIED = "AdaptorProviderTradeVerified";
	public static final String EVENT_TIME_DISP_REC_REJ_FROM_PROVIDER = "RejectionReceivedFromProvider";
	public static final String EVENT_TIME_DISP_REC_VERIFY_FROM_PROVIDER = "VerificationReceivedFromProvider";
	public static final String EVENT_TIME_DISP_ADAPTER_REC_RATE = "RateReceivedByAdapter";
	public static final String EVENT_TIME_DISP_ADAPTER_SENT_RATE = "RateSentByAdapter";
	public static final String EVENT_TIME_RATE_EFFECTIVE = "RateEffective";

	public static final String TOIS_MESSAGES = "TOIS.MESSAGES";
	public static final String ENTITY_ACTION = "ACTION";
	public static final String ENTITY_TYPE = "TYPE";
	public static final String ENTITY_ACTION_ADD = "ADD";
	public static final String ENTITY_TYPE_AUDITEVENT = "com.integral.audit.AuditEvent";
	public static final String USER_NAME = "USER";

	public static final String TENOR_SPOT = "SPOT";

	//Simulator rates topic
	public static final String PSEUDO_RATE = "PseudoRate";

	public static final char SPACE = ' ';
	public static final String ACTIVE_STATUS = "Active";
	public static final String INACTIVE_STATUS = "InActive";

	public static final String MULTI_TIER_REQUESTID_SEPARATOR = "_";

	public static final String EVENT_TIME_DISP_TIMEOUT_AT_ADAPTOR = "TimedOutAtAdaptor";

	//Alert Callback Action Constants
	public static final String ALERT_ACTION_ADAPTOR_HTTP_REQUEST_PROCESSING = "Alert.Adaptor.HTTP.Request.Processing";
	public static final String ALERT_ACTION_ADAPTOR_REQUESTSERVICE_LOOKUP = "Alert.Adaptor.RequestService.Lookup";
	public static final String ALERT_ACTION_ADAPTOR_PROCESS_LOGIN = "Alert.Adaptor.Process.Login";
	public static final String ALERT_ACTION_ADAPTOR_PROCESS_SUBSCRIPTION = "Alert.Adaptor.Process.Subscription";
	public static final String ALERT_ACTION_ADAPTOR_PROCESS_UNSUBSCRIPTION = "Alert.Adaptor.Process.UnSubscription";
	public static final String ALERT_ACTION_ADAPTOR_PROCESS_TRADE = "Alert.Adaptor.Process.Trade";
	public static final String ALERT_ACTION_ADAPTOR_JMS_PROVIDERSTATUS = "Alert.Adaptor.JMS.ProviderStatus";
	public static final String ALERT_ACTION_ADAPTOR_JMS_MARKETRATE = "Alert.Adaptor.JMS.MarketRate";
	public static final String ALERT_ACTION_ADAPTOR_JMS_TRADERESPONSE = "Alert.Adaptor.JMS.TradeResponse";
	public static final String ALERT_ACTION_ADAPTOR_JMS_TRADEPENDING = "Alert.Adaptor.JMS.TradePending";
	public static final String ALERT_ACTION_ADAPTOR_JMS_REMOTEAUDIT = "Alert.Adaptor.JMS.RemoteAudit";
	public static final String ALERT_ACTION_ADAPTOR_NO_TRADE_INFO = "Alert.Adaptor.No.Trade.Information";

	public static final String ERROR_NO_LOGIN_INFO_FOR_SERVER = "No Login Info for Server";

	// RFS rejection related constants

	public static final String RFS_NOT_HANDLED_AT_ADAPTOR = "RFS request cannot be handled";
	public static final String RFS_DEFAULT_FAILURE_RESPONSE_MESSAGE = "Internal Server Error.Could not process RFS request";

	public static final String RFS_REQUEST_EXPIRED_AT_ADAPTOR = "Quote Request expired at Adaptor";
	public static final String RFS_REQUEST_EXPIRED_AT_PROVIDER = "Quote Request expired at Provider";
	public static final String RFS_REQUEST_REJECTED_BY_PROVIDER = "Quote Request rejected by Provider";
	public static final String RFS_QUOTE_CANCELLED_BY_PROVIDER = "Quote Cancel sent by Provider";
	public static final String RFS_REQUEST_SUBMIT_FAILURE = "Quote Request submit failed";

	public static final String RFS_REQUEST_EXPIRED = "RFS Request Expired";

	// Default reject reason if Provider does not give any reason
	public static final String DEFAULT_REJECT_REASON = "Trade rejected. No reason specified by provider.";
	public static final String ERROR_STREAMID_NOT_SET = "StreamId is not set";
	public static final String ERROR_UNSUPPORTED_STREAMID = "Unsupported Stream";
	public static final String ERROR_STREAM_INACTIVE = "Stream is INACTIVE";
	public static final String ERROR_ACCOUNTID_NOT_SET = "ACCOUNTID is not set";
	public static final String ERROR_INVALID_BUY_SELL = "Invalid BuySell";
	public static final String ERROR_PROVIDER_INACTIVE = "Provider is INACTIVE";
	public static final String ERROR_TRADE_SUBMIT_FAILED = "Trade Submit Failed";
	public static final String ERROR_TERMCCY_TRADE_NOT_SUPPORTED = "Term currency trading is not supported";

	//Legal Entity
	public static final String LEGAL_ENTITY = "legalEntity";

	//
	public static final String ADAPTOR_INIT_URL_PREFIX = "http://";
	public static final String ADAPTOR_INIT_URL_PORT_PREFIX = ":";
	public static final String ADAPTOR_INIT_URL_SUFFIX = "/adAdmin/ISRequestProcessorServlet";

	public static final String CCYPAIR_SEPERATOR = "/";
	public static final String SUBSCRIPTION_SEPERATOR = "~";

	// custom fields used in sending receivedTime and adaptorGUID 

	public static final int RECEIVED_TIME_FIELD = 9000;
	public static final int GUID_FIELD = 9001;

	public static final String MSG_KEY_CURRENCY_PAIR = "CP";
	public static final String MSG_KEY_STREAM = "STREAM";

	public static final String RFS_SUBSCRIPTION_REQUEST = "RFSRequest";
	
	public static final String OMS_PROVIDER = "OMSPROVIDER";
	
	// Used in processing multiple fills separately on IS side.
	public static final String REQUEST_REFERENCE_ID = "REQUEST_REFERENCE_ID";

	// OCX Disclosed & Undisclosed External System
	public static final String DISCLOSED_PREFIX = "TrueFXDisclosedMaskPrefix";
	public static final String UNDISCLOSED_PREFIX = "TrueFXUndisclosedMaskPrefix";

}
