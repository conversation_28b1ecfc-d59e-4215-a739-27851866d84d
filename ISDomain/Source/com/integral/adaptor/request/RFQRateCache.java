package com.integral.adaptor.request;

import com.integral.is.message.rfs.RFSFXRate;
import com.integral.user.User;
import com.integral.commons.Tuple;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class RFQRateCache {

    protected static RFQRateCache _instance = new RFQRateCache();

    private Map<String, RFSFXRate> txnIdToRate = new ConcurrentHashMap<String, RFSFXRate>();

    private Map<String, Tuple<Double, Double>> txnIdToCoverRate = new ConcurrentHashMap<String, Tuple<Double, Double>>();

    private Map<String, User> txnIdTradeRequestUser = new ConcurrentHashMap<String, User>();

    public static RFQRateCache getInstance() {
        return _instance;
    }

    public Map<String, RFSFXRate> getTxnIdToRate() {
        return txnIdToRate;
    }

    public Map<String, Tuple<Double, Double>> getTxnIdToCoverRate() {
        return txnIdToCoverRate;
    }

    public Map<String, User> getTxnIdTradeRequestUser() {
        return txnIdTradeRequestUser;
    }

    public void removeFromTradeRequestUserCache(String txnId) {
        txnIdTradeRequestUser.remove(txnId);
    }
}
