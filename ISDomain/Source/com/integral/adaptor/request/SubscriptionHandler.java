package com.integral.adaptor.request;

import com.integral.is.message.MarketRateSubscribe;
import com.integral.is.message.MarketRateUnsubscribe;
import com.integral.is.message.ResponseMessage;

/**
 * Handles subscription requests. Subscription requests include starting and stopping
 * requests. For example, you can start and stop subscription to certain currency pairs
 * using the following methods
 * startSubscription(...);
 * stopSubscription(...);
 */
public interface SubscriptionHandler
{
	/**
	 * Starts subscription for a certain currency pair specified in the MarketRateSubscribe
	 * object. This object contains the base/variable ccy pair information.
	 * @param request a MarketRateSubscribe object containing base/variable ccy pair information.
	 * @return Return success/failure of starting subscription along with other information like
	 * stream source, etc
	 */
	public ResponseMessage startSubscription(MarketRateSubscribe request);

	/**
	 * Stops subscription for a certain currency pair specified in the MarketRateUnsubscribe
	 * object. This object contains the base/variable ccy pair information.
	 * @param request a MarketRateUnsubscribe object containing base/variable ccy pair information.
	 * @return Return success/failure of stopping subscription
	 */
	public ResponseMessage stopSubscription(MarketRateUnsubscribe request);

};