// Copyright (c) 2005 Integral Development Corp. All rights reserved.
package com.integral.adaptor.request;

import java.rmi.RemoteException;
import java.util.HashMap;
import java.util.Map;

import javax.naming.Context;
import javax.naming.InitialContext;

import com.integral.adaptor.AdaptorConstantC;
import com.integral.adaptor.handler.WarmupOrderHandler;
import com.integral.adaptor.login.AdaptorLoginManagerC;
import com.integral.adaptor.manager.AdaptorTradeManager;
import com.integral.adaptor.manager.BrokerAdaptorTradeManager;
import com.integral.adaptor.manager.StalenessCheckHandler;
import com.integral.adaptor.manager.rfs.RFSAdaptorManager;
import com.integral.adaptor.request.ejb.RequestService;
import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.broker.BrokerAdaptorFactory;
import com.integral.broker.publish.rfs.RawRate;
import com.integral.broker.publish.rfs.RawRateUtil;
import com.integral.broker.request.SpacesBrokerOrderHandler;
import com.integral.broker.request.SpacesBrokerRequestHandlerFactory;
import com.integral.broker.request.rfs.BrokerRFQTradeHandlerC;
import com.integral.broker.rfs.RFSHandlerC;
import com.integral.broker.rfs.RFSHandlerProxyC;
import com.integral.commons.Tuple;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.is.broker.RFSBrokerWorkflowFunctorC;
import com.integral.is.common.rfs.RFSConstants;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.message.BrokerOrderRequest;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBeanC;
import com.integral.is.common.util.ISCommonUtilC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.message.*;
import com.integral.is.message.rfs.*;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.configuration.ServerMBean;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;
import com.integral.util.StopWatchC;

/**
 * RequestService acts as a service provider for a Provider Adaptor. Any requests to the
 * the Provider Adaptor goes through this object. 
 * For example, IS interacting with Provider Adaptor might want to ask the adaptor to start
 * subscribing to a set of currency pairs. This can be done through startSubscription(...);
 * 
 * <AUTHOR>
 * @since Outrigger release
 */
public class RequestServiceC implements RequestService
{

	static RequestServiceC instance = new RequestServiceC();

	protected Log log = LogFactory.getLog(this.getClass());

	private RequestServiceC()
	{
	}

	public static RequestService getRequestService()
	{
		if ( instance == null )
		{
			synchronized ( RequestService.class )
			{
				instance = new RequestServiceC();
			}
		}
		return instance;
	}

	/**
	 * Start subscription for the currency pair specfied in MarketRateSubscribe
	 *
	 * @param request is a MarketRateSubscribe object that contains base and var ccy
	 * @return ResponseMessage contains the status of the method call
	 */
	public ResponseMessage startSubscription(MarketRateSubscribe request)
	{
		if ( log.isDebugEnabled() )
		{
			log.debug("RequestServiceC.startSubscription called");
		}
		StopWatchC watch = null;
		if ( LogFactory.isTimingEnabled(this.getClass()) )
		{
			watch = LogFactory.getStopWatch(this.getClass(), "RequestServiceC.startSubscription " + request.toString());
		}
		if ( !isServerLoggedIn(request.getServerId()) && isTraderRequest(request) )
		{
			if ( watch != null )
				watch.stopAndLog();
			return getFailureResponse(AdaptorConstantC.ERROR_NO_LOGIN_INFO_FOR_SERVER);
		}
		StalenessCheckHandler.getInstance().onSubscription(request.getBaseCcy() + request.getVariableCcy());
        String providerType = getProviderType(request, request.getStreamId());
        
		ResponseMessage msg = getFactory(request.getProviderShortName(), providerType).getSubscriptionHandler().startSubscription(request);
		if ( watch != null )
		{
			watch.stopAndLog();
		}

		return msg;
	}

	/**
	 * Stop subscription for the currency pair specfied in MarketRateUnsubscribe
	 *
	 * @param request is a MarketRateUnsubscribe object that contains base and var ccy
	 * @return ResponseMessage contains the status of the method call
	 */
	public ResponseMessage stopSubscription(MarketRateUnsubscribe request)
	{
		if ( log.isDebugEnabled() )
		{
			log.debug("RequestServiceC.stopSubscription called");
		}
		StopWatchC watch = null;
		if ( LogFactory.isTimingEnabled(this.getClass()) )
		{
			watch = LogFactory.getStopWatch(this.getClass(), "RequestServiceC.stopSubscription " + request.toString());
		}
		if ( !isServerLoggedIn(request.getServerId()) && isTraderRequest(request) )
		{
			if ( watch != null )
				watch.stopAndLog();
			return getFailureResponse(AdaptorConstantC.ERROR_NO_LOGIN_INFO_FOR_SERVER);
		}
		StalenessCheckHandler.getInstance().onUnsubscription(request.getBaseCcy() + request.getVariableCcy());
        String providerType = getProviderType(request, request.getStreamId());

		ResponseMessage msg = getFactory(request.getProviderShortName(), providerType).getSubscriptionHandler().stopSubscription(request);
		if ( watch != null )
		{
			watch.stopAndLog();
		}

		return msg;
	}

	/**
	 * Client accepts a previously sent quote.
	 *
	 * @param request The TradeRequest object that contains quote acceptance information
	 * @return ResponseMessage contains the status of the method call
	 */
	public ResponseMessage quoteAccepted(TradeRequest request)
	{
		long st = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
		{
			log.debug("RequestServiceC.quoteAccepted");
		}
        long t0 = System.currentTimeMillis();
        String providerType = getProviderType(request, request.getStreamId());
        long t1 = System.currentTimeMillis();
		StopWatchC watch = null;
		if ( LogFactory.isTimingEnabled(this.getClass()) )
		{
			watch = LogFactory.getStopWatch(this.getClass(), "RequestServiceC.quoteAccepted " + request.toString());
		}
		if ( !isServerLoggedIn(request.getServerId()) && !isTraderRequest(request) )
		{
			if ( watch != null )
				watch.stopAndLog();
			return getFailureResponse(AdaptorConstantC.ERROR_NO_LOGIN_INFO_FOR_SERVER);
		}
        long t2 = System.currentTimeMillis();
		AdaptorTradeManager.getInstance().addTradeRequest(request);
        long t3 = System.currentTimeMillis();
		ResponseMessage msg = null;
        long t4 = 0; long t5 = 0; long t6 = 0;
		if ( !ISCommonUtilC.isWarmUpObject(request) )
		{
            t4 = System.currentTimeMillis();
            RequestHandlerFactory factory = getFactory(request.getProviderShortName(), providerType);

            t5 = System.currentTimeMillis();
            OrderHandler orderHandler = factory.getOrderHandler();

            t6 = System.currentTimeMillis();
            msg = orderHandler.quoteAccepted(request);
		}
		else
		{
			getFactory(request.getProviderShortName(), providerType).getOrderHandler(); //to initialize
			msg = WarmupOrderHandler.getInstance().quoteAccepted(request);
		}

		if ( msg == null )
		{
			log.warn("RequestServiceC.quoteAccepted : ResponseMessage is null. Removing request from AdaptorTradeManager ");
			AdaptorTradeManager.getInstance().removeTrade(request.getTradeId());
		}
		else if ( msg.getStatus() != ResponseMessage.SUCCESS )
		{
			log.warn("RequestServiceC.quoteAccepted : Unable to send TradeRequest to Provider for TradeID " + request.getTradeId());
			log.warn("RequestServiceC.quoteAccepted : ResponseMessage is  " + msg.toString());
			log.warn("RequestServiceC.quoteAccepted : Removing request for TradeID " + request.getTradeId() + " from AdaptorTradeManager");
			AdaptorTradeManager.getInstance().removeTrade(request.getTradeId());
		}
		if ( watch != null )
		{
			watch.stopAndLog();
		}
        StringBuilder sb = new StringBuilder(100);
        if ((t0 - st) > 1) sb.append(" log.tt=").append(t0 - st).append(" ms").append(", ");
        if ((t1 - t0) > 1) sb.append(" pType.tt=").append(t1 - t0).append(" ms").append(", ");
        if ((t2 - t1) > 1) sb.append(" loginChk.tt=").append(t2 - t1).append(" ms").append(", ");
        if ((t3 - t2) > 1) sb.append(" addTR.tt=").append(t3 - t2).append(" ms").append(", ");
        if ((t4 - t3) > 1) sb.append(" wrmChk.tt=").append(t4 - t3).append(" ms").append(", ");
        if ((t5 - t4) > 1) sb.append(" gRF.tt=").append(t5 - t4).append(" ms").append(", ");
        if ((t6 - t5) > 1) sb.append(" gOH.tt=").append(t6 - t5).append(" ms").append(". ");
        if(sb.length() > 0) {
            sb.append("st=").append(st).append(", ");
            sb.append("t0=").append(t0).append(", ");
            sb.append("t1=").append(t1).append(", ");
            sb.append("t2=").append(t2).append(", ");
            sb.append("t3=").append(t3).append(", ");
            sb.append("t4=").append(t4).append(", ");
            sb.append("t5=").append(t5).append(", ");
            sb.append("t6=").append(t6).append(", ");
            log.info(sb.toString());
        }
        return msg;
	}

    @Override
    public ResponseMessage submitBrokerOrder(BrokerOrderRequest request) throws RemoteException
    {
        if (log.isDebugEnabled()) {
            log.debug("RequestServiceC.submitBrokerOrder: Submitting the broker order with id: " + request.getRequestId());
        }

        StopWatchC watch = null;
        if (LogFactory.isTimingEnabled(this.getClass())) {
            watch = LogFactory.getStopWatch(this.getClass(), "RequestServiceC.submitBrokerOrder " + request.toString());
        }

        if ( !isServerLoggedIn(request.getServerId())) {
            if (watch != null) {
                watch.stopAndLog();
            }

            return getFailureResponse(AdaptorConstantC.ERROR_NO_LOGIN_INFO_FOR_SERVER);
        }

        OrderHandler handler = getFactory(request.getProviderShortName(), request.getProviderType()).getOrderHandler();

        ResponseMessage msg;
        if (handler instanceof SpacesBrokerOrderHandler) {
            // Add request to the cache.
            BrokerAdaptorTradeManager.getInstance().addTradeRequest(request);

            SpacesBrokerOrderHandler spacesHandler = (SpacesBrokerOrderHandler) handler;
            msg = spacesHandler.submitBrokerOrder(request);
        }
        else {
            msg = getFailureResponse("Cannot handle BrokerOrderRequest since spaces is not enabled.");
        }

        if (watch != null) {
            watch.stopAndLog();
        }

        return msg;
    }

	public ResponseMessage login(LoginMessage message)
	{
		if ( log.isDebugEnabled() )
		{
			log.debug("RequestServiceC.login");
		}
		StopWatchC watch = null;
		if ( LogFactory.isTimingEnabled(this.getClass()) )
		{
			watch = LogFactory.getStopWatch(this.getClass(), "RequestServiceC.login " + message.toString());
		}

		ResponseMessage msg = AdaptorLoginManagerC.getInstance().handleLogin(message);
		if ( watch != null )
		{
			watch.stopAndLog();
		}

		return msg;
	}

	/**
	 * Sends RFS subscription request message to provider.
	 * @param message
	 * @return
	 */
	public ResponseMessage startRFSSubscription(RFSSubscribe message)
	{
		String providerName = message.getProviderShortName();
        String providerType = getProviderType(message, message.getStreamId());
		RequestHandlerFactory factory = getFactory(providerName, providerType);
		String requestId = message.getRequestId();
		if ( log.isDebugEnabled() )
		{
			log.debug("RequestServiceC.startRFSSubscription for Provider->" + providerName + ",RequestId->" + requestId);
		}
		StopWatchC watch = null;
		if ( LogFactory.isTimingEnabled(this.getClass()) )
		{
			watch = LogFactory.getStopWatch(this.getClass(), "RequestServiceC.startRFSSubscription " + message.toString());
		}
		if ( !isServerLoggedIn(message.getServerId()) )
		{
			if ( watch != null )
				watch.stopAndLog();
			return getFailureResponse(AdaptorConstantC.ERROR_NO_LOGIN_INFO_FOR_SERVER);
		}
		if ( factory.getRFSHandler() != null )
		{
			ResponseMessage resp;
			synchronized ( RFSAdaptorManager.getInstance().getLock(requestId, providerName) )
			{
				RFSAdaptorManager.getInstance().addRFSSubscribe(message);
				resp = factory.getRFSHandler().startRFSSubscription(message);
                if ( resp == null )
                {
                    log.info( "RequestServiceC.startRFSSubscription : Handler returned null response for RequestId->"
                            + requestId + " Provider->" + providerName + ",RFSHandler=" + factory.getRFSHandler() );
                    return getFailureResponse( AdaptorConstantC.RFS_NOT_HANDLED_AT_ADAPTOR );
                }
                if ( resp.getStatus() != ResponseMessage.SUCCESS )
				{
					log.warn("RequestServiceC.startRFSSubscription : RFSSubscription FAILED for RequestId->"
							+ requestId + " Provider->" + providerName + ",resp=" + resp.getFailureReasons () );
					RFSAdaptorManager.getInstance().removeRFSSubscribe(requestId, providerName);
					RFSAdaptorManager.getInstance().removeLock(requestId, providerName);
				}
			}
			if ( watch != null )
				watch.stopAndLog();
			return resp;
		}
		if ( watch != null )
			watch.stopAndLog();
		return getFailureResponse(AdaptorConstantC.RFS_NOT_HANDLED_AT_ADAPTOR);
	}

	/**
	 * Sends RFS withdraw request message to provider.
	 * @param message
	 * @return ResponseMessage - indicating success/failure or request
	 */
	public ResponseMessage stopRFSSubscription(RFSUnsubscribe message)
	{
		String providerName = message.getProviderShortName();
		String providerType = getProviderType(message, message.getStreamId());
		RequestHandlerFactory factory = getFactory(providerName, providerType);
		String requestId = message.getRequestId();
		if ( log.isDebugEnabled() )
		{
			log.debug("RequestServiceC.stopRFSSubscription for Provider->" + providerName + ",RequestId->" + requestId);
		}
		StopWatchC watch = null;
		if ( LogFactory.isTimingEnabled(this.getClass()) )
		{
			watch = LogFactory.getStopWatch(this.getClass(), "RequestServiceC.stopRFSSubscription " + message.toString());
		}
		if ( !isServerLoggedIn(message.getServerId()) )
		{
			if ( watch != null )
				watch.stopAndLog();
			return getFailureResponse(AdaptorConstantC.ERROR_NO_LOGIN_INFO_FOR_SERVER);
		}
		if ( factory.getRFSHandler() != null )
		{
			synchronized ( RFSAdaptorManager.getInstance().getLock(requestId, providerName) )
			{
				Object obj = RFSAdaptorManager.getInstance().getRFSSubscribe(requestId, providerName);
				ResponseMessage resp;
				if ( obj == null )
				{
					log.warn("RequestServiceC.stopRFSSubscription : No RFS Subscription found for Provider->" + providerName + ",RequestId->" + requestId);
					resp = new ResponseMessageC();
					resp.setStatus(ResponseMessage.SUCCESS);
					RFSAdaptorManager.getInstance().removeLock(requestId, providerName);
				}
				else
				{
					resp = factory.getRFSHandler().stopRFSSubscription(message);
					if ( resp.getStatus() == ResponseMessage.SUCCESS )
					{
						RFSAdaptorManager.getInstance().removeRFSSubscribe(requestId, providerName);
						RFSAdaptorManager.getInstance().removeLock(requestId, providerName);
					}
				}
				if ( watch != null )
					watch.stopAndLog();
				if ( resp != null )
					return resp;
				else
					return getFailureResponse(AdaptorConstantC.RFS_DEFAULT_FAILURE_RESPONSE_MESSAGE);
			}
		}
		if ( watch != null )
			watch.stopAndLog();
		return getFailureResponse(AdaptorConstantC.RFS_NOT_HANDLED_AT_ADAPTOR);
	}

	/**
	 * sends RFS trade acceptance message to Adaptor
	 * @param message
	 * @return ResponseMessage
	 */
	public ResponseMessage quoteAccepted(RFSTradeRequest message)
	{
		String providerName = message.getProviderShortName();
		String providerType = getProviderType(message, message.getStreamId());
		RequestHandlerFactory factory = getFactory(providerName, providerType);
		String requestId = message.getRequestId();
		if ( log.isDebugEnabled() )
		{
			log.debug("RequestServiceC.quoteAccepted(RFSTradeRequest) for Provider->" + providerName + ",RequestId->" + requestId);
		}
		StopWatchC watch = null;
		if ( LogFactory.isTimingEnabled(this.getClass()) )
		{
			watch = LogFactory.getStopWatch(this.getClass(), "RequestServiceC.quoteAccepted " + message.toString());
		}
		if ( !isServerLoggedIn(message.getServerId()) )
		{
			if ( watch != null )
				watch.stopAndLog();
			return getFailureResponse(AdaptorConstantC.ERROR_NO_LOGIN_INFO_FOR_SERVER);
		}
		if ( factory.getRFSHandler() != null )
		{
			ResponseMessage resp;
			synchronized ( RFSAdaptorManager.getInstance().getLock(requestId, providerName) )
			{
				RFSSubscribe rfsRequest = RFSAdaptorManager.getInstance().getRFSSubscribe(requestId, providerName);
				message.setProperty(AdaptorConstantC.RFS_SUBSCRIPTION_REQUEST, rfsRequest);
				 boolean success = false;
                final Organization brokerOrg = ReferenceDataCacheC.getInstance().getOrganization(providerName);
                RFSHandler brokerRFSHandler = getRfsHandler(message, brokerOrg);
				boolean useRfq = BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(brokerOrg.getShortName()).useRfqForRfqCoverTrade();
                if(brokerRFSHandler == null && message.isRfq() && !useRfq){
					RFSSubscribe rfsSubscribe = getRfsSubscribe(message, message.getRequestId(), brokerOrg, false);
                    RFSAdaptorManager.getInstance().addRFSSubscribe(rfsSubscribe);
                }
				if(message.isRfq() && !useRfq){
					log.info("RequestServiceC.quoteAccepted : not sending new request for RFQ for RequestId->" + requestId + ",Provider->" + providerName);
				}
				if ( ((ISMBeanC) ISFactory.getInstance().getISMBean()).isOutrightDisplayOrderEnabled(providerName) && ISUtilImpl.getInstance().getOrg(providerName).isDisplayOrderProvider() )
				{
						//:TO DO  Add some logic to add trades to Adaptor manager
						success = RFSAdaptorManager.getInstance().addRFSTradeRequest(message ,false);
				}
				else
				{
					success = RFSAdaptorManager.getInstance().addRFSTradeRequest(message);
				}
				
				if ( !success )
				{
					log.warn("RequestServiceC.quoteAccepted(RFSTradeRequest) : " + AdaptorConstantC.RFS_REQUEST_EXPIRED + "for RequestId->" + requestId + ",Provider->" + providerName);
					resp = getFailureResponse(AdaptorConstantC.RFS_REQUEST_EXPIRED);
					resp.setRequestReferenceId(requestId);
				}
				else
				{
					if(brokerRFSHandler == null && message.isRfq() && !useRfq){
						resp = submitRFQTrade(message);
					}else {
						resp = factory.getRFSHandler().quoteAccepted(message);
					}
					if (resp == null) {
						log.warn("RequestServiceC.quoteAccepted(RFSTradeRequest) : ResponseMessage is null for RequestId->" + requestId + ",Provider->" + providerName);
						RFSAdaptorManager.getInstance().removeRFSTradeRequest(requestId, providerName);
						resp = getFailureResponse(AdaptorConstantC.RFS_DEFAULT_FAILURE_RESPONSE_MESSAGE);
					} else if (resp.getStatus() != ResponseMessage.SUCCESS) {
						log.warn("RequestServiceC.quoteAccepted(RFSTradeRequest) : Unable to send RFSTradeRequest to Provider for RequestId->" + requestId + ",Provider->" + providerName);
						RFSAdaptorManager.getInstance().removeRFSTradeRequest(requestId, providerName);
					} else {
						if (watch != null) {
							watch.stopAndLog();
						}
						return resp;
					}
				}
				RFSAdaptorManager.getInstance().removeLock(requestId, providerName);
				if ( watch != null )
					watch.stopAndLog();
				return resp;
			}
		}
		if ( watch != null )
			watch.stopAndLog();
		return getFailureResponse(AdaptorConstantC.RFS_NOT_HANDLED_AT_ADAPTOR);
	}

	private RFSHandler getRfsHandler(RFSTradeRequest message, Organization brokerOrg) {
		RFSHandler rfsHandler = SpacesBrokerRequestHandlerFactory.getInstance(brokerOrg).getRFSHandler();
		RFSHandlerProxyC rfsHandlerProxyC = (RFSHandlerProxyC) rfsHandler;
		return rfsHandlerProxyC.getRfsHandlerCache().get(message.getRequestId());
	}


	private ResponseMessage submitRFQTrade(RFSTradeRequest request) {

		try {
			log.info("RequestServiceC.submitRFQTrade - received RFQ trade. RFSTradeRequest="
					+ request + ",tradeId=" + request.getRequestId());
			String newId = request.getRequestId() + ISCommonConstants.RFQ;
            final Organization brokerOrg = ReferenceDataCacheC.getInstance().getOrganization(request.getProviderShortName());
            RFSSubscribe rfsSubscribe = getRfsSubscribe(request, newId, brokerOrg, true);

            ResponseMessage resp = startRFSSubscription(rfsSubscribe);
			RFSHandler rfsHandler = SpacesBrokerRequestHandlerFactory.getInstance(brokerOrg).getRFSHandler();
			RFSHandlerProxyC rfsHandlerProxyC = (RFSHandlerProxyC) rfsHandler;
			RFSHandler brokerRFSHandler = rfsHandlerProxyC.getRfsHandlerCache().get(rfsSubscribe.getRequestId());
			log.info("RequestServiceC.submitRFQTrade - sending rfs subscribe for RFQ trade. RFSTradeRequest="
					+ request + ",tradeId=" + request.getRequestId() + ",rfsSubscribe.requestId=" + rfsSubscribe.getRequestId());
			if (brokerRFSHandler instanceof RFSHandlerC) {
				BrokerRFQTradeHandlerC atBestOrderHandler = new BrokerRFQTradeHandlerC((RFSHandlerC) brokerRFSHandler, request);
				atBestOrderHandler.init();
			} else {
				log.info("RequestServiceC.submitRFQTrade - RFQ trade execution through RFS failed for requestId="
						+ request.getRequestId() + ",handler=" + brokerRFSHandler + ",responseMsg=" + resp);
			}

			return resp;
		} catch (Exception e) {
			log.error("RequestServiceC.submitRFQTrade - Exception while submitting RFQ trade. message=" + request, e);
		}
		return null;
	}

    private RFSSubscribe getRfsSubscribe(RFSTradeRequest request, String newId, Organization brokerOrg, boolean addRateToCache) {
        RFSSubscribe rfsSubscribe = MessageFactory.newRFSSubscribe();
        final Organization requestingOrg = OrganizationUtil.getOrganization(request.getOrgShortName());

        rfsSubscribe.setRequestId(newId);
        rfsSubscribe.setOriginatingRequestId(newId);
        rfsSubscribe.setBaseCurrency(request.getBaseCurrency());
        rfsSubscribe.setVariableCurrency(request.getVariableCurrency());
        rfsSubscribe.setRequestExpiryTimeInSeconds(BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(brokerOrg.getShortName()).getRFQCoverExpiryTimeInMillis() / 1000);
        rfsSubscribe.setOrgShortName(request.getOrgShortName());
        rfsSubscribe.setUserOrganization(request.getUserOrganization());
        rfsSubscribe.setLeShortName(request.getLeShortName());
		if (request.getOrgShortName().equals(request.getUserOrganization())) {
			rfsSubscribe.setUserShortName(request.getUserShortName());
		} else {
			rfsSubscribe.setUserShortName(requestingOrg.getDefaultDealingUser().getShortName());
		}
        rfsSubscribe.setStreamId(request.getStreamId());
        rfsSubscribe.setProviderKey(request.getProviderKey());
        rfsSubscribe.setProviderShortName(request.getProviderShortName());
        rfsSubscribe.setAsWarmupMessage(request.isWarmupMessage());
        rfsSubscribe.setServerId(request.getServerId());
        rfsSubscribe.setProperty(ISCommonConstants.PROVIDER_TYPE, ISCommonConstants.PROVIDER_TYPE_BA);
        rfsSubscribe.setTradeDate(EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate().asJdkDate());//todo set based on currency pair and current logic in BA
        rfsSubscribe.setProperty(RFSConstants.VALIDATION_FAILURES, new HashMap<String, String>());
        //rfsSubscribe.setMT300Field72 ( null );//todo
        rfsSubscribe.setProperty(ISCommonConstants.BASE_CURRENCY_CLSF_PARAM, CurrencyFactory.getCurrency(request.getBaseCurrency()).getInstrumentClassification().getShortName());
        rfsSubscribe.setProperty(ISCommonConstants.TERM_CCY_CLSF_PARAM, CurrencyFactory.getCurrency(request.getVariableCurrency()).getInstrumentClassification().getShortName());
        rfsSubscribe.setProperty(ISCommonConstants.RFQ_TRADE_PARAM, Boolean.TRUE.toString());
        //rfsSubscribe.setProperty(ISCommonConstants.FXI_TRANSACTIONID, request.getRequestId());
        rfsSubscribe.setProperty(ISCommonConstants.FXI_TRANSACTIONID, newId);
        IdcDate valueDate = DateTimeFactory.newDate(request.getNearLeg().getValueDate());

        FXRateConvention conv = QuoteConventionUtilC.getInstance().getFXRateConvention(requestingOrg);
        FXRateBasis rb = conv.getFXRateBasis(request.getBaseCurrency(), request.getVariableCurrency());

        boolean isRateBasisNonDeliverable = rb.isNonDeliverable();
		int deliverableNDFParam = ( Integer )request.getProperty ( ISCommonConstants.DELIVERABLE_NDF_PARAM);
			if ( ISCommonConstants.DELIVERABLE_NDF_PARAMETER_DEFAULT != deliverableNDFParam )
			{
				if ( IdcUtilC.isDeliverableNDFRequestParameterSupported ( requestingOrg ) )
				{
					log.info ( "SBOH.submitAtBestOrder - Deliverable/NDF parameter based on request. "
							+ ",param=" + deliverableNDFParam + ",ratebasis.isNonDeliverable="
							+ isRateBasisNonDeliverable + ",requestId=" + request.getRequestId () );
				}
				else
				{
					deliverableNDFParam = ISCommonConstants.DELIVERABLE_NDF_PARAMETER_DEFAULT;
				}
			}
        IdcDate spotDate = rb.getSpotDate(EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate());
        boolean isOutright = !valueDate.isSameAs(spotDate);
        boolean ppSpotSpreadDisabled = isOutright && ISFactory.getInstance().getISMBean().isPPSpotSpreadForOutrightDisabledForOrg(requestingOrg, brokerOrg);
        rfsSubscribe.setProperty(RFSBrokerWorkflowFunctorC.SPOT_SPREAD_DISABLED, ppSpotSpreadDisabled);
        rfsSubscribe.setTradeClassification(request.getTradeClassification());
        rfsSubscribe.setSpotDate(spotDate);
        rfsSubscribe.setProperty ( ISCommonConstants.DELIVERABLE_NDF_PARAM, deliverableNDFParam );

        RFSFXLeg rfsfxLeg = new RFSFXLegC();
        rfsfxLeg.setValueDate(request.getNearLeg().getValueDate());
        rfsfxLeg.setDealtCurrency(request.getTradeLegs().get(0).getDealtCurrency());
        rfsfxLeg.setTenor(request.getNearLeg().getTenor());
        rfsfxLeg.setName(ISCommonConstants.SINGLE_LEG);
        if(request.isTwoWayPriceRequest()){
			rfsfxLeg.setBidOfferMode(DealingPrice.TWO_WAY);
		}else {
			rfsfxLeg.setBidOfferMode(request.getNearLeg().getBidOfferMode() == DealingPrice.OFFER ? DealingPrice.OFFER : DealingPrice.BID);
		}
        rfsSubscribe.setNearLeg(rfsfxLeg);
		RawRate rawRate = RawRateUtil.deserializeMiFIDRequestParams(request.getRawRate());
		Map<String, RFSFXRate> txnIdToRate = RFQRateCache.getInstance().getTxnIdToRate();
		Map<String, Tuple<Double,Double>> txnIdToCoverRate = RFQRateCache.getInstance().getTxnIdToCoverRate();
        RFSFXRate rate = new RFSFXRateC();
        double amount;
		if (request.getNearLeg().getOfferRate() != null) {
			amount = request.getNearLeg().getOfferRate().getDealtAmount();
			if (addRateToCache) {
				txnIdToRate.put(request.getRequestId(), request.getNearLeg().getOfferRate());
				txnIdToCoverRate.put(request.getRequestId(), new Tuple<Double, Double>(rawRate.getOfferSpotRate(), rawRate.getOfferFwdPoints()));
			}
		} else {
			amount = request.getNearLeg().getBidRate().getDealtAmount();
			if (addRateToCache) {
				txnIdToRate.put(request.getRequestId(), request.getNearLeg().getBidRate());
				txnIdToCoverRate.put(request.getRequestId(), new Tuple<Double, Double>(rawRate.getBidSpotRate(), rawRate.getBidFwdPoints()));
			}
		}
        rate.setDealtAmount(amount);
		if(request.isTwoWayPriceRequest()){
			rfsfxLeg.setOfferRate(rate);
			rfsfxLeg.setBidRate(rate);
		}else {
			if (DealingPrice.OFFER == rfsfxLeg.getBidOfferMode()) {
				rfsfxLeg.setOfferRate(rate);
			} else {
				rfsfxLeg.setBidRate(rate);
			}
		}

        if (request.getFarLeg() != null) {
            RFSFXLeg farLeg = new RFSFXLegC();
            farLeg.setValueDate(request.getFarLeg().getValueDate());
            farLeg.setDealtCurrency(request.getTradeLegs().get(1).getDealtCurrency());
            farLeg.setTenor(request.getFarLeg().getTenor());
            farLeg.setName(ISCommonConstants.FAR_LEG);
			if(request.isTwoWayPriceRequest()){
				farLeg.setBidOfferMode(DealingPrice.TWO_WAY);
			}else {
				farLeg.setBidOfferMode(rfsSubscribe.getNearLeg().getBidOfferMode() == DealingPrice.BID ? DealingPrice.OFFER : DealingPrice.BID);
			}
            farLeg.setLegClassification(RFSFXLegC.TRD_LEG_OUTRIGHT );
            rfsSubscribe.setFarLeg(farLeg);

            rfsSubscribe.getNearLeg().setName(ISCommonConstants.NEAR_LEG);

            RFSFXRate farRate = new RFSFXRateC();
            double farAmount;
			if (request.getFarLeg().getOfferRate() != null) {
				farAmount = request.getFarLeg().getOfferRate().getDealtAmount();
				if (addRateToCache) {
					txnIdToRate.put(request.getRequestId() + ISCommonConstants.FAR_LEG, request.getFarLeg().getOfferRate());
					txnIdToCoverRate.put(request.getRequestId() + ISCommonConstants.FAR_LEG, new Tuple<Double, Double>(rawRate.getOfferFarSpotRate(), rawRate.getOfferFarFwdPoints()));
				}
			} else {
				farAmount = request.getFarLeg().getBidRate().getDealtAmount();
				if (addRateToCache) {
					txnIdToRate.put(request.getRequestId() + ISCommonConstants.FAR_LEG, request.getFarLeg().getBidRate());
					txnIdToCoverRate.put(request.getRequestId() + ISCommonConstants.FAR_LEG, new Tuple<Double, Double>(rawRate.getBidFarSpotRate(), rawRate.getBidFarFwdPoints()));
				}
			}
            farRate.setDealtAmount(farAmount);
			if(request.isTwoWayPriceRequest()){
				farLeg.setOfferRate(farRate);
				farLeg.setBidRate(farRate);
			}else {
				if (DealingPrice.OFFER == farLeg.getBidOfferMode()) {
					farLeg.setOfferRate(farRate);
				} else {
					farLeg.setBidRate(farRate);
				}
			}
        }
        return rfsSubscribe;
    }

    /**
	 * Return the context/environment variable for the specified name.
	 */
	protected Object getContextVariable(String name)
	{
		try
		{
			Context initial = new InitialContext();
			Context environment = (Context) initial.lookup("java:comp/env");
			return environment.lookup(name);
		}
		catch ( Exception e )
		{
			log.error("Context variable not found: " + name);
		}
		return null;
	}

	/**
	 *
	 * @param serverId
	 * @return true if the server is logged in
	 */
	private boolean isServerLoggedIn(String serverId)
	{
		return AdaptorLoginManagerC.getInstance().isServerLoggedIn(serverId);
	}

	/**
	 *
	 * @param err
	 * @return the ResponseMessage with status as FAILURE and failure reason set to err
	 */
	private ResponseMessage getFailureResponse(String err)
	{
		ResponseMessage resp = new ResponseMessageC();
		resp.setStatus(ResponseMessage.FAILURE);
		resp.setFailureReason(err);
		try
		{
			return resp;
		}
		catch ( Exception e )
		{
			log.error("RequestServiceC.getFailureResponse : Error in creating failure response", e);
		}
		return null;
	}

	/**
	 * To check is incoming request is from trader.
	 */
	private boolean isTraderRequest(ISMessage request)
	{
		boolean traderRequest = false;
		if ( request.getProperty("Trader") != null )
		{
			String isTraderRequest = (String) request.getProperty("Trader");
			if ( isTraderRequest.equalsIgnoreCase("true") )
			{
				traderRequest = true;
			}
		}
		return traderRequest;
	}

    private RequestHandlerFactory getFactory(String provider)
	{
        return getFactory(provider, null);
    }

    private RequestHandlerFactory getFactory(String provider, String providerType)
    {
    	RequestHandlerFactory factory = providerType != null ? RequestHandlerFactoryRegistry.getRequestHandlerFactory(providerType, provider) : RequestHandlerFactoryRegistry.getRequestHandlerFactory(provider);
    	if(factory == null)
    	{
    		//Look for Handler factory registered for OMS Provider. Required for trader.
    		factory = RequestHandlerFactoryRegistry.getRequestHandlerFactory(provider);
    	}
    	if(factory == null)
    	{
    		//temporary fix for trader where OA acts as router to subcription request made for provider
    		factory = RequestHandlerFactoryRegistry.getRequestHandlerFactory(RequestHandlerFactory.DEFAULT_FACTORY_NAME);
    	}
    	if(factory == null)
    	{
    		throw new RuntimeException("RequestHandlerFactory not found for provider " + provider + " , " + providerType);
    	}
    	return factory;
    }

    private String getProviderType(ISMessage message, String streamId) {
        String providerType = (String) message.getProperty(ISCommonConstants.PROVIDER_TYPE);
        if (ServerMBean.DEFAULT_DO_STREAM_NAME.equals(streamId)) {
            //Provider type should be OA for Display Order Stream.
            providerType = ISCommonConstants.PROVIDER_TYPE_OA;
        }
        return providerType;
    }


}