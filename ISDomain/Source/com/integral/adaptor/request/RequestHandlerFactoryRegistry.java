package com.integral.adaptor.request;

import java.util.concurrent.ConcurrentHashMap;

import com.integral.log.Log;
import com.integral.log.LogFactory;

public class RequestHandlerFactoryRegistry
{
	static Log log = LogFactory.getLog(RequestHandlerFactoryRegistry.class);
	static ConcurrentHashMap<String, RequestHandlerFactory> requestHandlerFactories = new ConcurrentHashMap<String, RequestHandlerFactory>();
    private static final String HYPHEN_SEPARATOR = "-";

    public static boolean register(String provider, RequestHandlerFactory factory)
	{
		RequestHandlerFactory old = requestHandlerFactories.putIfAbsent(provider, factory);
		boolean isAdded = (old == null);
		if(isAdded)
		{
			log.warn("RequestHandlerFactoryRegistry.register - RequestHandlerFactory added for - " + provider);
		}
		return isAdded;
	}

    public static boolean register(String providerType, String provider, RequestHandlerFactory factory)
	{
        RequestHandlerFactory old = requestHandlerFactories.putIfAbsent(createKey(providerType, provider), factory);
		boolean isAdded = (old == null);
		if(isAdded)
		{
			log.warn("RequestHandlerFactoryRegistry.register - RequestHandlerFactory added for - " + provider + " , " + providerType);
		}
		return isAdded;
	}

	public static RequestHandlerFactory getRequestHandlerFactory(String provider)
	{
		if ( provider != null )
		{
			return requestHandlerFactories.get(provider);
		}
		return null;
	}

    public static RequestHandlerFactory getRequestHandlerFactory(String providerType, String provider)
	{
		if ( providerType != null && provider != null )
		{
			return requestHandlerFactories.get( createKey( providerType, provider ) );
		}
		return null;
	}

    public static String createKey(String providerType, String provider) {
        return new StringBuilder(providerType).append(HYPHEN_SEPARATOR).append(provider).toString();
    }

	public static ConcurrentHashMap<String, RequestHandlerFactory> getRequestHandlerFactories()
	{
		return requestHandlerFactories;
	}
}
