package com.integral.adaptor.request;

import com.integral.is.message.ResponseMessage;
import com.integral.is.message.rfs.RFSMessage;
import com.integral.is.message.rfs.RFSSubscribe;
import com.integral.is.message.rfs.RFSTradeRequest;
import com.integral.is.message.rfs.RFSUnsubscribe;

//Copyright (c) 2001-2005 Integral Development Corp.  All rights reserved.

/**
 * Description of the interface or class.
 *
 * <AUTHOR> Development Corp.
 */
public interface RFSHandler
{

	public ResponseMessage startRFSSubscription(RFSSubscribe message);

	public ResponseMessage quoteAccepted(RFSTradeRequest message);

	public ResponseMessage stopRFSSubscription(RFSUnsubscribe message);

}
