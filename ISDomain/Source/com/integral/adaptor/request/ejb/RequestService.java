package com.integral.adaptor.request.ejb;

import com.integral.is.message.BrokerOrderRequest;
import com.integral.is.message.LoginMessage;
import com.integral.is.message.MarketRateSubscribe;
import com.integral.is.message.MarketRateUnsubscribe;
import com.integral.is.message.ResponseMessage;
import com.integral.is.message.TradeRequest;
import com.integral.is.message.rfs.RFSSubscribe;
import com.integral.is.message.rfs.RFSTradeRequest;
import com.integral.is.message.rfs.RFSUnsubscribe;

import java.rmi.RemoteException;

/**
 * RequestService acts as a service provider for a Provider Adaptor. Any requests to the
 * the Provider Adaptor goes through this object.
 * For example, IS interacting with Provider Adaptor might want to ask the adaptor to start
 * subscribing to a set of currency pairs. This can be done through
 * RequestService.startSubscription(...);
 */
public interface RequestService {
	
	String REGISTERED_NAME = "AdaptorRequestServiceSingleton";
	
    /**
     * Start subscription for the currency pair specfied in MarketRateSubscribe
     * @param request is a MarketRateSubscribe object that contains base and var ccy
     */
    public ResponseMessage startSubscription(MarketRateSubscribe request) throws RemoteException;

    /**
     * Stop subscription for the currency pair specfied in MarketRateUnsubscribe
     * @param request is a MarketRateUnsubscribe object that contains base and var ccy
     * @return ResponseMessage contains the status of the method call
     */
    public ResponseMessage stopSubscription(MarketRateUnsubscribe request) throws RemoteException;


    /**
     * Client accepts a previously sent quote.
     * @param request The TradeRequest object that contains quote acceptance information
     * @return ResponseMessage contains the status of the method call
     */
    public ResponseMessage quoteAccepted(TradeRequest request) throws RemoteException;

    /**
     * Submit a new broker order request coming from client.
     */
    public ResponseMessage submitBrokerOrder(BrokerOrderRequest request) throws RemoteException;

    /**
     * to send login message to Adaptor
     * @param message
     * @return
     */
    public ResponseMessage login(LoginMessage message) throws RemoteException;

    /**
     * Sends create RFQ message to provider.
     * @param message
     * @return ResponseMessage - indicating success/failure or request
     * @throws RemoteException
     */
    public ResponseMessage startRFSSubscription(RFSSubscribe message) throws RemoteException;

    /**
     * Sends withdraw request message to provider.
     * @param message
     * @return ResponseMessage - indicating success/failure or request
     * @throws RemoteException
     */
    public ResponseMessage stopRFSSubscription(RFSUnsubscribe message) throws RemoteException;

    /**
     * to send trade acceptance message to Adaptor
     * @param message
     * @return
     */
    public ResponseMessage quoteAccepted(RFSTradeRequest message) throws RemoteException;

}
