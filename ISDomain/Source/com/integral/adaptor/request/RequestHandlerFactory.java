// Copyright (c) 2005 Integral Development Corp. All rights reserved.
package com.integral.adaptor.request;

/**
 * Factory class to get the appropriate handler objects
 * Handler objects implement a particular functionality.
 * For example,
 * RequestHandlerFactory.getSubscriptionHandler()
 * returns a SubscriptionHandler class that will take care of
 * subscriptions for various ccy pairs
 *
 * <AUTHOR> May 2, 2005 1:16 PM
 * @version $Revision: *******.********** $ $Date: 2010-03-22 10:37:00 $ $Author: sharma $
 */
public abstract class RequestHandlerFactory
{
	
	public static String DEFAULT_FACTORY_NAME = "DEFAULT";
	
	/**
	 * Return a SubscriptionHandler object. SubscriptionHandler will take care of
	 * subscriptions for various ccy pairs
	 */
	public abstract SubscriptionHandler getSubscriptionHandler();

	/**
	 * Return an OrderHandler object. OrderHandlers will take answer order related
	 * requests like 'Quote<PERSON>ccept', etc.
	 */
	public abstract OrderHandler getOrderHandler();

	/**
	 * Return an RFSHandler object. RFSHandler takes care of RFS related workflows
	 * like withdraw request, expire request, request status. 
	 *
	 */
	public RFSHandler getRFSHandler()
	{
		return null;
	}
}