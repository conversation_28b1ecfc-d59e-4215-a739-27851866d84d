package com.integral.adaptor.request;

import com.integral.is.message.ResponseMessage;
import com.integral.is.message.TradeRequest;

/**
 * Handles trade requests. Trade requests like accepting a quote is supported.
 * For example, If you want to send quote acceptance to a provider, you would
 * call quoteAccepted(TradeRequest request)
 */
public interface OrderHandler
{
	/**
	 * Send quote acceptance to provider.
	 * @param request a TradeRequest object containing quote acceptance information.
	 * @return ResponseMessage contains the status of the method call
	 */
	public ResponseMessage quoteAccepted(TradeRequest request);

}