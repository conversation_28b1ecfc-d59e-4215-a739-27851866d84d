package com.integral.adaptor.service.mbean;

import java.util.Collection;
import java.util.Hashtable;

import com.integral.broker.config.BrokerConfigurationServiceFactory;
import com.integral.broker.request.SpacesBrokerRequestHandlerFactory;
import com.integral.broker.rfs.RFSHandlerProxyC;
import com.integral.ems.EMSServerFactory;
import com.integral.ems.engine.EMSServer;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.shutdown.CorePriceServiceShutdown;
import com.integral.system.runtime.ShutdownTask;
import com.integral.user.Organization;
// Copyright (c) 2001-2004 Integral Development Corp.  All rights reserved.

/**
 * <AUTHOR> Development Corp.
 */

public class BrokerAdaptorShutDown implements BrokerAdaptorShutDownMBean,ShutdownTask {
    Log log = LogFactory.getLog(this.getClass());

    @Override
    public void create() throws Exception {
        log.info(this.getClass() + " CREATE");
    }

    @Override
    public void start() throws Exception {
        log.info(this.getClass() + " START");
    }

    @Override
    public void stop() {
        log.info(this.getClass() + " STOP initiated");
        EMSServer ems = EMSServerFactory.getInstance().getEMSServer();
        if (ems != null) {
            log.info("Stopping the EMS Server for BrokerAdaptor.");
            ems.stop();
        }
        try {
        	Collection<Organization> brokerOrgs = BrokerConfigurationServiceFactory.getBrokerConfigurationService().getDeployedBrokerOrganizations();
        	for(Organization brokerOrg : brokerOrgs) {
        		RFSHandlerProxyC rfsHandler = (RFSHandlerProxyC) SpacesBrokerRequestHandlerFactory.getInstance(brokerOrg).getRFSHandler();
        		rfsHandler.withdrawAllRequests();
                CorePriceServiceShutdown.publishStateRate(brokerOrg);
        	}
        }
        catch (Exception e) {
            log.error("stop : RFSHandler.withdrawRequests() failed", e);
        }
        log.info(this.getClass() + " STOP completed");
    }

    @Override
    public String shutdown(String aName, Hashtable args) {
        stop();
        return  "BrokerAdaptorShutDown completed";
    }
}
