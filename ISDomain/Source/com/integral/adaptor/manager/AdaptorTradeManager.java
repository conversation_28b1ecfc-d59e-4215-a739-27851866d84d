package com.integral.adaptor.manager;

import com.integral.adaptor.AdaptorConstantC;
import com.integral.adaptor.config.AdaptorConfiguration;
import com.integral.adaptor.config.AdaptorConfigurationFactory;
import com.integral.adaptor.login.AdaptorLoginManagerC;
import com.integral.adaptor.response.ResponseHandlerC;
import com.integral.imtp.session.IMTPSession;
import com.integral.is.ISCommonConstants;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.log.MessageLogger;
import com.integral.is.message.*;
import com.integral.log.Log;
import com.integral.log.LogFactory;

import javax.jms.JMSException;
import javax.jms.QueueSession;
import java.util.concurrent.*;

/**
 * This singleton class caches the TradeRequest messages coming to the adaptor.
 * If no response is received from the provider within time specified in auto cancellation time
 * a TradePending message is sent back if auto-cancellation is disabled, otherwise
 * a TradeReject is sent.
 * This class also processes the provider response and sets the response status to ALREADY_REJECTED_BY_ADAPTOR if the response
 * from provider is delayed and the adaptor has sent a TradeReject on its own.
 */
public class AdaptorTradeManager {

    private static final String STATUS_RESPONSE_SENT = "ResponseSent";
    private static final String STATUS_REJECT_SENT = "RejectSent";
    private static final String STATUS_PENDING_SENT = "PendingSent";
    private static ConcurrentMap<String, String> tradeResponseStatusMap = new ConcurrentHashMap<String, String>();
    private static ConcurrentMap<String, TradeRequest> tradeRequestMap = new ConcurrentHashMap<String, TradeRequest>();
    private static ConcurrentMap<String, ScheduledFuture> tradeSchedulerMap = new ConcurrentHashMap<String, ScheduledFuture>();
    private static AdaptorTradeManager instance;
    private static ScheduledExecutorService tradeScheduler = null;
    private AdaptorConfiguration configMBean;
    private Log log = LogFactory.getLog(this.getClass());

    /**
     * Constructor
     */
    private AdaptorTradeManager() {
        configMBean = AdaptorConfigurationFactory.getAdaptorConfigurationMBean();
        tradeScheduler = Executors.newScheduledThreadPool(configMBean.getTradeSchedulerServiceThreadPoolSize());
    }

    /**
     * @return instance of the class
     */
    public static AdaptorTradeManager getInstance() {
        if (instance == null) {
            synchronized (AdaptorTradeManager.class){
                if (instance == null) {
                    instance = new AdaptorTradeManager();
                }
            }
        }
        return instance;
    }

    /**
     * Add trade request object to the cache and start the timer thread.
     *
     * @param req
     */
    public void addTradeRequest(TradeRequest req) {
        String tradeId = req.getTradeId();

        tradeResponseStatusMap.put(tradeId, "");
        tradeRequestMap.put(tradeId, req);

        //new Thread(new TradeTimer(tradeId, req.getAmount())).start();
        ScheduledFuture tradeScheduler = AdaptorTradeManager.tradeScheduler.schedule(new TradeTimer(tradeId, req.getAmount()),
                configMBean.getAutoCancellationTime(), TimeUnit.SECONDS);
        tradeSchedulerMap.put(tradeId, tradeScheduler);
    }

    /**
     * @param tradeId
     * @return TradeRequest object corresponding to the tradeId.
     */
    public TradeRequest getTradeRequest(String tradeId) {
        Object obj = tradeRequestMap.get(tradeId);
        if (obj != null) {
            return (TradeRequest) obj;
        }
        return null;
    }

    /**
     * Remove the trade info from the cache
     *
     * @param tradeId
     */
    public void removeTrade(String tradeId) {
        //releaseSession(tradeId);
        tradeResponseStatusMap.remove(tradeId);
        tradeRequestMap.remove(tradeId);

        try {
            ScheduledFuture tradeScheduler = tradeSchedulerMap.remove(tradeId);
            if (tradeScheduler != null) tradeScheduler.cancel(false);
        } catch (Exception e) {
            log.error("AdaptorTradeManager.removeTrade - " + e.getMessage());
        }
    }

    /**
     * @param tradeId
     * @return true if a response has been sent by the adaptor
     */
    public boolean responseSent(String tradeId) {
        String status = tradeResponseStatusMap.get(tradeId);
        return status != null && (status.equals(STATUS_PENDING_SENT) || status.equals(STATUS_REJECT_SENT));
    }

    /**
     * Process the trade response received from the Provider.
     * If the trade has been rejected by the Adaptor then
     * a status of ALREADY_REJECTED_BY_ADAPTOR will be sent to IS.
     *
     * @param msg
     */
    public void processProviderResponse(TradeResponse msg) {
        if (msg.getStatus() == TradeResponse.REJECTED_BY_ADAPTOR) {
            return;
        }
        String trdId = msg.getTradeId();
        String status = tradeResponseStatusMap.get(trdId);
        if (status == null) {
            log.error("AdaptorTradeManager.processProviderResponse : No trade found in cache for " + trdId);
            MessageLogger.getInstance().log(ISAlertMBean.ALERT_ACTION_ADAPTOR_NO_TRADE_INFO, "AdaptorTradeManager.processProviderResponse", new StringBuffer().append("No trade information found in adaptor cache for ").append(trdId).append(". Unable to update the trade status. It may be a possible duplicate response").toString(), null);
            return;
        }
        if (status.equals(STATUS_REJECT_SENT)) {
            msg.setStatus(TradeResponse.ALREADY_REJECTED_BY_ADAPTOR);
            log.warn("AdaptorTradeManager.processProviderResponse : TradeResponse received after verification timed out for " + trdId);
        } else {
            tradeResponseStatusMap.put(trdId, STATUS_RESPONSE_SENT);
            log.warn("AdaptorTradeManager.processProviderResponse : status ResponseSent updated for tradeId->" + trdId);
        }
    }

    /**
     * Check if the trade is already rejected by Adaptor.
     *
     * @param trdId
     * @return true if already rejected by adaptor
     */
    public boolean alreadyRejectedByAdaptor(String trdId) {
        String status = tradeResponseStatusMap.get(trdId);
        if (status == null) {
            log.error("AdaptorTradeManager.alreadyRejectedByAdaptor : No trade found in cache for " + trdId);
            log.error("AdaptorTradeManager.processProviderResponse : No trade found in cache for " + trdId);
            MessageLogger.getInstance().log(ISAlertMBean.ALERT_ACTION_ADAPTOR_NO_TRADE_INFO, "AdaptorTradeManager.alreadyRejectedByAdaptor", new StringBuffer().append("No trade information found in adaptor cache for ").append(trdId).append(". Unable to find if the trade was already rejected by adaptor. It may be a possible duplicate response").toString(), null);
            return true;
        }
        return status.equals(STATUS_REJECT_SENT);
    }

    /**
     * @param tradeId
     * @return the JMS Broker id corresponding to the tradeId
     */
    public String getBrokerId(String tradeId) {
        TradeRequest tradeRequest = tradeRequestMap.get(tradeId);
        if (tradeRequest != null) {
        return AdaptorLoginManagerC.getInstance().getBroker((tradeRequest).getServerId());
        }
        return null;
    }

    /**
     * @param tradeId
     * @return the trade queue corresponding to the tradeId
     */
    public String getTradeDestination(String tradeId) {
        return AdaptorLoginManagerC.getInstance().getTradeQueue((tradeRequestMap.get(tradeId)).getServerId());
    }

    public IMTPSession getTradeDestinationSession(String tradeId) {
        String serverId = (tradeRequestMap.get(tradeId)).getServerId();
        return AdaptorLoginManagerC.getInstance().getLoggedInIMTPSession(serverId);
    }


    /**
     * @param tradeId
     * @return the serverId of the server which initiated the trade
     */
    public String getServerID(String tradeId) {
        Object obj = tradeRequestMap.get(tradeId);
        if (obj != null) {
            return ((TradeRequest) obj).getServerId();
        }
        return null;
    }

    private ConcurrentHashMap<String, QueueSession> sessionsMap = new ConcurrentHashMap<String, QueueSession>();

//    public QueueSession acquireSessionForTradeId(String tradeId) throws Exception {
//        QueueSession session = sessionsMap.get(tradeId);
//        if (session == null) {
//            HiPerfJMSManager jmsManager = (HiPerfJMSManager) JMSManager.getInstance();
//            QueueSessionPool pool = jmsManager.getQueueSessionPool();
//            session = pool.borrowQueueSession(getBrokerId(tradeId));
//            QueueSession oldSession = sessionsMap.putIfAbsent(tradeId, session);
//            if (oldSession != null) {
//                pool.returnQueueSession(getBrokerId(tradeId), session);
//                session = oldSession;
//            }
//        }
//        return session;
//    }

//    public void releaseSession(String tradeId) {
//        try {
//            QueueSession session = sessionsMap.remove(tradeId);
//            if (session != null) {
//                HiPerfJMSManager jmsManager = (HiPerfJMSManager) JMSManager.getInstance();
//                QueueSessionPool pool = jmsManager.getQueueSessionPool();
//                pool.returnQueueSession(getBrokerId(tradeId), session);
//            }
//        } catch (Exception e) {
//            log.error("AdaptorTradeManager.releaseSession" + tradeId, e);
//        }
//    }

    /**
     * @param tradeId
     * @return the persistence message queue of the server corresponding to the tradeId
     */
    public String getPersistenceMessageQueue(String tradeId) {
        return AdaptorLoginManagerC.getInstance().getPersistenceMessageQueue(tradeRequestMap.get(tradeId).getServerId());
    }

    /**
     * Send a TradePending/TradeReject response if no response has been received from the provider.
     *
     * @param key
     * @param amount
     */
    private void verificationTimedOut(String key, double amount) throws JMSException {
        String currentStatus = tradeResponseStatusMap.get(key);

        if (currentStatus == null || currentStatus.equals(STATUS_RESPONSE_SENT)) {
            return;
        }

        log.warn("AdaptorTradeManager.verificationTimedOut : Verification timed out for TRDID " + key);
        //Send Rejection to IS if AutoCancel is enabled else send a Pending Message
        Timing timing = new TimingC();
        timing.setTime(ISCommonConstants.EVENT_TIME_DISP_TIMEOUT_AT_ADAPTOR, System.currentTimeMillis());
        if (configMBean.isAutoCancellationApplicable()) {
            TradeReject tr = MessageFactory.newTradeReject();
            tr.setTradeId(key);
            tr.setReasonDescription(AdaptorConstantC.VERIFICATION_TIMED_OUT);
            tr.setRejectedAmount(amount);
            tr.setStatus(TradeResponse.REJECTED_BY_ADAPTOR);
            tr.setTiming(timing);
            ResponseHandlerC.getInstance().tradeRecieved(tr, false);
            tradeResponseStatusMap.put(key, STATUS_REJECT_SENT);
        } else {
            TradePending trdPending = MessageFactory.newTradePending();
            trdPending.setTradeId(key);
            trdPending.setTiming(timing);
            try {
                ResponseHandlerC.getInstance().tradePendingRecieved(trdPending);
            } catch (JMSException e) {
                e.printStackTrace();
            }
            tradeResponseStatusMap.put(key, STATUS_PENDING_SENT);
        }
    }

    private class TradeTimer implements Runnable {
        String tradeId;
        double amount;

        public TradeTimer(String tradeId, double amount) {
            this.tradeId = tradeId;
            this.amount = amount;
        }

        public void run() {
            try {
                verificationTimedOut(tradeId, amount);
            } catch (Exception e) {
                log.error("TradeTimer.run : Interrupted ", e);
            }
        }
    }
}