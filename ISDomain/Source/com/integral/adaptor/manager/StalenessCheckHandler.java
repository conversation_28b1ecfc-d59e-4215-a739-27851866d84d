package com.integral.adaptor.manager;

import com.integral.is.message.*;
import com.integral.is.common.ProviderQuoteIdFactory;
import com.integral.is.ISCommonConstants;
import com.integral.adaptor.response.ResponseHandlerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.adaptor.config.AdaptorConfigurationFactory;
import com.integral.adaptor.config.AdaptorConfiguration;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class StalenessCheckHandler
{

	private static StalenessCheckHandler instance = new StalenessCheckHandler();
	private AdaptorConfiguration configMBean = AdaptorConfigurationFactory.getAdaptorConfigurationMBean();
	private ProviderStatus status;
	private int lastStatus = ProviderStatus.INACTIVE;
	private Map<String, Integer> lastStreamStatusMap = new HashMap<String, Integer>();
	private ConcurrentHashMap<String, MarketRate> rates = new ConcurrentHashMap<String, MarketRate>(100);
	private List<String> ccyPairs = new ArrayList<String>();
	private long lastRateReceivedAt = System.currentTimeMillis();
	private boolean isStale = false;
	private boolean override = false;
	private boolean enabled = false;
	private Thread timerThread;
	private Timer timer = new Timer();
	private Log log = LogFactory.getLog(this.getClass());

	/**
	 * Constructor
	 */
	private StalenessCheckHandler()
	{
		override = configMBean.isOverrideStalenessCheck();
		enabled = configMBean.isStalenessCheckApplicable();
		timerThread = new Thread(timer);
		timerThread.start();
		log.warn("StalenessCheckHandler : Staleness check is " + (enabled ? "Enabled" : "Disabled"));
		log.warn("StalenessCheckHandler : Staleness check is " + (override ? "Overridden" : "not Overridden"));
	}

	/**
	 *
	 * @return instance of the class
	 */
	public static StalenessCheckHandler getInstance()
	{
		return instance;
	}

	/**
	 * Send inactive heartbeat message
	 */
	public void sendInactiveHeartBeat()
	{
		try
		{
			if ( null != status )
			{
				log.warn("StalenessCheckHandler.sendInactiveHeartbeat : Sending inactive heartbeat");
				status.setStatus(ProviderStatus.INACTIVE);
				ResponseHandlerC.getInstance().heartbeatRecieved(status);
			}
			else
			{
				log.warn("StalenessCheckHandler.sendInactiveHeartbeat : Provider-Inactive-heartbeat is not sent. Provider status is not updated at all.");
			}
		}
		catch ( Exception ex )
		{
			log.error("StalenessCheckHandler.sendInactiveHeartBeat : Error ", ex);
		}
	}

	/**
	 * Method will be used in case of priceCenter switch
	 * @param stream
	 */
	public void sendUnsupportedRates(String stream)
	{
		try
		{
			log.warn("StalenessCheckHandler.sendUnsupportedRates : Sending Unsupported rates for Stream " + stream);
			Collection ratesColl = rates.values();
			Iterator iter = ratesColl.iterator();
			MarketRate rate;
			while (iter.hasNext())
			{
				rate = (MarketRate) iter.next();
				if ( rate.getStreamId().equalsIgnoreCase(stream) )
				{
					MarketRate mr = MessageFactory.newMarketRate();
					mr.setBaseCcy(rate.getBaseCcy());
					mr.setVarCcy(rate.getVariableCcy());
					mr.setStreamId(rate.getStreamId());
					mr.setProviderShortName(rate.getProviderShortName());
					mr.setStale(true);
					mr.setQuoteId(ProviderQuoteIdFactory.newQuoteId(rate.getQuoteId()));
					log.warn("StalenessCheckHandler.sendUnsupportedRates : Sending Stale rate for " + mr.getBaseCcy() + "/" + mr.getVariableCcy() + " : " + mr);
					ResponseHandlerC.getInstance().sendRate(mr);
				}
			}
		}
		catch ( Exception ex )
		{
			log.error("StalenessCheckHandler.sendUnsupportedRates : Error " + ex);
		}
	}

	/**
	 * Notify receiving a rate
	 * @param message
	 */
	public void notifyRateReceived(MarketRate message)
	{
		try
		{
			lastRateReceivedAt = System.currentTimeMillis();
			if ( message.getStreamId() != null )
			{
				rates.put(message.getBaseCcy() + message.getVariableCcy() + message.getStreamId(), message);
			}
			else
			{
				rates.put(message.getBaseCcy() + message.getVariableCcy(), message);
			}
			if ( getIsStale() )
			{
				//if provider is Stale we have to make the status active as soon as we receive the rate
				isStale = false;
				if ( !override )
				{
					status.setStatus(ProviderStatus.ACTIVE);
					ResponseHandlerC.getInstance().heartbeatRecieved(status);
				}
			}
		}
		catch ( Exception ex )
		{
			log.error("StalenessCheckHandler.notifyRateReceived(MarketRate) : Error ", ex);
		}
	}

	/**
	 * Notify receiving a rate
	 */
	public void notifyRateReceived()
	{
		try
		{
			lastRateReceivedAt = System.currentTimeMillis();
			if ( getIsStale() )
			{
				//if provider is Stale we have to make the status active as soon as we receive the rate
				isStale = false;
				if ( !override )
				{
					status.setStatus(ProviderStatus.ACTIVE);
					ResponseHandlerC.getInstance().heartbeatRecieved(status);
				}
			}
		}
		catch ( Exception ex )
		{
			log.error("StalenessCheckHandler.notifyRateReceived : Error ", ex);
		}
	}

	/**
	 *
	 * @param ccyPair
	 */
	public void onSubscription(String ccyPair)
	{
		try
		{
			synchronized ( ccyPairs )
			{
				if ( ccyPairs.size() == 0 )
				{
					timer.reset();
				}
				if ( !ccyPairs.contains(ccyPair) )
					ccyPairs.add(ccyPair);
			}
		}
		catch ( Exception ex )
		{
			log.error("StalenessCheckHandler.onSubscription : Error ", ex);
		}
	}

	/**
	 *
	 * @param ccyPair
	 */
	public void onUnsubscription(String ccyPair)
	{
		try
		{
			synchronized ( ccyPairs )
			{
				ccyPairs.remove(ccyPair);
				if ( ccyPairs.size() == 0 )
				{
					setIsStale(false);
					status.setStatus(ProviderStatus.ACTIVE);
					ResponseHandlerC.getInstance().heartbeatRecieved(status);
				}
			}
		}
		catch ( Exception e )
		{
			log.error("StalenessCheckHandler.onUnsubscription : Error ", e);
		}
	}

	/**
	 * Called on receiving a heartbeat message by MessageRecieverC
	 * @param status
	 */
	@SuppressWarnings("unchecked")
	public void heartbeatReceived(ProviderStatus status)
	{
		try
		{
			if ( isStalenessCheckEnabled() )
			{
				if ( !isStalenessCheckOverriden() && getIsStale() && status.getStatus() == ProviderStatus.ACTIVE )
				{
					status.setStatus(ProviderStatus.STALE);
				}
			}
			Object obj = status.getProperty(ISCommonConstants.STREAM_STATUS);
			if ( obj != null )
			{
				Map<String, Integer> streamStatusMap = (Map) obj;
				for ( Map.Entry<String, Integer> mapEntry : lastStreamStatusMap.entrySet() )
				{
					if ( !streamStatusMap.containsKey(mapEntry.getKey()) )
					{
						continue;
					}
					if ( streamStatusMap.get(mapEntry.getKey()) == ProviderStatus.INACTIVE && mapEntry.getValue() != ProviderStatus.INACTIVE )
					{
						sendUnsupportedRates(mapEntry.getKey());
					}
				}
				lastStreamStatusMap = streamStatusMap;
			}
			setStatus(status);
		}
		catch ( Exception ex )
		{
			log.error("StalenessCheckHandler.heartbeatReceived : Error ", ex);
		}
	}

	/**
	 * send unsupported rates
	 */
	private void sendUnsupportedRates()
	{
		Collection<MarketRate> ratesColl = rates.values();
		Iterator<MarketRate> iter = ratesColl.iterator();
		MarketRate rate;
		while (iter.hasNext())
		{
			rate = (MarketRate) iter.next();
			MarketRate mr = MessageFactory.newMarketRate();
			mr.setBaseCcy(rate.getBaseCcy());
			mr.setVarCcy(rate.getVariableCcy());
			mr.setStreamId(rate.getStreamId());
			mr.setProviderShortName(rate.getProviderShortName());
			mr.setStale(true);
			mr.setQuoteId(ProviderQuoteIdFactory.newQuoteId(rate.getQuoteId()));
			log.warn("StalenessCheckHandler.sendUnsupportedRates : Sending Stale rate for " + mr.getBaseCcy() + "/" + mr.getVariableCcy() + " : " + mr);
			ResponseHandlerC.getInstance().sendRate(mr);
		}
	}

	MarketRateSerializer v1Serializer = MarketRateSerializerFactory.instance().getSerializerForVersion(1);

	/**
	 * Set override Staleness. If this is called when status is stale an active heartbeat is sent followed by active cached rates
	 * @param override
	 */
	private void setOverrideStaleness(boolean override)
	{
		//Check if the status is STALE, If status is STALE and override is true
		// then send an active heartbeat and the cached rates

		//if the value of override is changed from true to false or false to true then continue else return
		if ( this.override == override )
		{
			return;
		}
		this.override = override;
		log.warn("StalenessCheckHandler.setOverrideStaleness : StalenessCheck is " + (override ? "Overridden" : "not Overridden"));
		if ( override && configMBean.isStalenessCheckApplicable() && configMBean.getStalenessCheckTime() > 0 )
		{
			if ( getIsStale() )
			{
				status.setStatus(ProviderStatus.ACTIVE);
				ResponseHandlerC.getInstance().heartbeatRecieved(status);
				Collection ratesColl = rates.values();
				Iterator iter = ratesColl.iterator();
				MarketRate rate;
				while (iter.hasNext())
				{
					rate = (MarketRate) iter.next();
					MarketRate mr = ((MarketRateC) rate).clone();
					mr.setQuoteId(ProviderQuoteIdFactory.newQuoteId(mr.getQuoteId()));
					log.warn("StalenessCheckHandler.setOverrideStaleness : Sending Rate - " + mr.toString());
					ResponseHandlerC.getInstance().sendRate(mr);
				}
			}
		}
	}

	/**
	 *
	 * @return true if staleness check is overridden
	 */
	private boolean isStalenessCheckOverriden()
	{
		return override;
	}

	/**
	 *
	 * @return true if staleness check is enabled
	 */
	private boolean isStalenessCheckEnabled()
	{
		if ( configMBean.isStalenessCheckApplicable() && configMBean.getStalenessCheckTime() > 0 )
			return true;
		else
			return false;
	}

	/**
	 * notify that the provider is stale
	 * Send a STALE ProviderStatus message.
	 */
	private void notifyIsStale()
	{
		if ( !override )
		{
			if ( status != null )
			{
				if ( lastStatus == ProviderStatus.ACTIVE )
				{
					status.setStatus(ProviderStatus.STALE);
					ResponseHandlerC.getInstance().heartbeatRecieved(status);
				}
			}
		}
	}

	/**
	 *
	 * @return true if stale
	 */
	private boolean getIsStale()
	{
		return isStale;
	}

	/**
	 *
	 * @param value
	 */
	private void setIsStale(boolean value)
	{
		isStale = value;
	}

	/**
	 *
	 * Send unsupported rates when provider status changes from ACTIVE to INACTIVE or STALE. This is done as api client does not have access to ProviderStatus update.
	 * Update the provider status
	 * @param st
	 */
	private synchronized void setStatus(ProviderStatus st)
	{
		this.status = st;
		if ( lastStatus == ProviderStatus.ACTIVE )
		{
			if ( st.getStatus() == ProviderStatus.INACTIVE )
			{
				timer.pause();
				log.warn("PROVIDER DOWN. " + configMBean.getShortName() + " is INACTIVE");
				sendUnsupportedRates();
				rates.clear();
				ccyPairs.clear();
				setIsStale(false);
			}
			else if ( st.getStatus() == ProviderStatus.STALE )
			{
				log.warn("PROVIDER DOWN. " + configMBean.getShortName() + " is STALE");
				sendUnsupportedRates();
			}
		}
		else if ( lastStatus == ProviderStatus.INACTIVE )
		{
			if ( st.getStatus() == ProviderStatus.ACTIVE )
			{
				if ( timer.isPaused() )
					timer.restart();
				log.warn("PROVIDER UP. " + configMBean.getShortName() + " is ACTIVE");
			}
			else if ( st.getStatus() == ProviderStatus.STALE )
			{
				log.error("PROVIDER STATUS CHANGED FROM INACTIVE TO STALE. ERROR");
			}
		}
		else if ( lastStatus == ProviderStatus.STALE )
		{
			if ( st.getStatus() == ProviderStatus.ACTIVE )
			{
				log.warn("PROVIDER UP. " + configMBean.getShortName() + " is ACTIVE");
			}
			else if ( st.getStatus() == ProviderStatus.INACTIVE )
			{
				timer.pause();
				log.warn("PROVIDER DOWN. " + configMBean.getShortName() + " is INACTIVE");
				sendUnsupportedRates();
				rates.clear();
				ccyPairs.clear();
				setIsStale(false);
			}
		}
		lastStatus = st.getStatus();
	}

	/**
	 * Staleness check timer
	 */
	private class Timer implements Runnable
	{
		private boolean running = false;
		private long startTime = 0;

		private void pause()
		{
			if ( running )
			{
				running = false;
			}
		}

		private boolean isPaused()
		{
			return !running;
		}

		private void restart()
		{
			startTime = System.currentTimeMillis();
			running = true;
		}

		private void reset()
		{
			startTime = System.currentTimeMillis();
		}

		public void run()
		{
			while (true)
			{
				if ( configMBean.isOverrideStalenessCheck() )
				{
					setOverrideStaleness(true);
				}
				else
				{
					setOverrideStaleness(false);
				}
				if ( running )
				{
					if ( System.currentTimeMillis() - startTime > configMBean.getStalenessCheckTime() * 1000 )
					{
						if ( System.currentTimeMillis() - lastRateReceivedAt > configMBean.getStalenessCheckTime() * 1000 )
						{
							if ( !getIsStale() )
							{
								if ( configMBean.isStalenessCheckApplicable() && configMBean.getStalenessCheckTime() > 0 )
								{
									if ( ccyPairs.size() != 0 )
									{
										setIsStale(true);
										notifyIsStale();
									}
								}
							}
						}
					}
				}
				try
				{
					Thread.sleep(500);
				}
				catch ( InterruptedException e )
				{
					e.printStackTrace();
				}
			}
		}
	}
}
