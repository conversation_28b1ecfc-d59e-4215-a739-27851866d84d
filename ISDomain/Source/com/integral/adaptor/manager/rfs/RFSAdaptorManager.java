package com.integral.adaptor.manager.rfs;

import com.integral.imtp.session.IMTPSession;
import com.integral.is.message.*;
import com.integral.is.message.rfs.*;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.util.RequestExpireHandler;
import com.integral.adaptor.login.AdaptorLoginManagerC;
import com.integral.adaptor.config.AdaptorConfiguration;
import com.integral.adaptor.config.AdaptorConfigurationFactory;
import com.integral.adaptor.response.ResponseHandlerC;
import com.integral.log.Log;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public class RFSAdaptorManager
{
	private static final RFSAdaptorManager _instance = new RFSAdaptorManager();
	private static final Log log = com.integral.log.LogFactory.getLog(_instance.getClass());

	private static final ConcurrentMap<String, RFSSubscribe> rfsSubscribeRequestMap = new ConcurrentHashMap<String, RFSSubscribe>();
	private static final ConcurrentMap<String, RFSTradeRequest> rfsTradeRequestMap = new ConcurrentHashMap<String, RFSTradeRequest>();
	private static final Map<String, Object> rfsRequestIdLocks = new HashMap<String, Object>();
	private static final Map<String, TimerTask> rfsTradeTimerMap = new HashMap<String, TimerTask>();

	private static final String REJECTED_BY_ADAPTOR = "RejectedByAdaptor";
	private static final String PENDING_SENT_BY_ADAPTOR = "PendingSentByAdaptor";

	private final Timer autoCanTimer = new Timer();

	private final AdaptorConfiguration configMBean;

	private RFSAdaptorManager()
	{
		configMBean = AdaptorConfigurationFactory.getAdaptorConfigurationMBean();
	}

	/**
	 *
	 * @return instance of RFSAdaptorManager
	 */
	public static RFSAdaptorManager getInstance()
	{
		return _instance;
	}

	/**
	 *
	 * @param requestId request id
	 * @param providerName provider name
	 * @return lock object for the requestId corresponding to the providerName
	 *
	 * The workflows involving subscription,un-subscription,trade and those in which the RFSRequestMessage objects are
	 * modified(like setting timing) must first get the lock and synchronize on them.
	 */
	public Object getLock(String requestId, String providerName)
	{
		if ( requestId == null || providerName == null )
		{
			throw new IllegalArgumentException();
		}
		synchronized ( rfsRequestIdLocks )
		{
			if ( !rfsRequestIdLocks.containsKey(providerName + "-" + requestId) )
			{
				rfsRequestIdLocks.put(providerName + "-" + requestId, new Object());
				log.warn("RFSAdaptorManager.getLock : Created lock for " + providerName + "-" + requestId);
			}
			return rfsRequestIdLocks.get(providerName + "-" + requestId);
		}
	}

	/**
	 * Remove the lock for the requestId corresponding to the providerName.
	 * This must be done when the RFS has reached its final stage
	 * @param requestId request id
	 * @param providerName provider name
	 */
	public void removeLock(String requestId, String providerName)
	{
		synchronized ( rfsRequestIdLocks )
		{
			Object obj = rfsRequestIdLocks.remove(providerName + "-" + requestId);
			if ( obj != null )
			{
				log.warn("RFSAdaptorManager.removeLock : Removed lock for " + providerName + "-" + requestId);
			}
		}
	}

	/**
	 *
	 * @param requestId request id
	 * @param providerName provider name
	 * @return RFSSubscribe request object for the requestId corresponding to the Provider
	 */
	public RFSSubscribe getRFSSubscribe(String requestId, String providerName)
	{
		if ( requestId == null || providerName == null )
		{
			throw new IllegalArgumentException();
		}
		return rfsSubscribeRequestMap.get(providerName + "-" + requestId);
	}

	/**
	 *
	 * @param requestId request id
	 * @param providerName provider name
	 * @return RFSTradeRequest object for the requestId corresponding to the Provider
	 */
	public RFSTradeRequest getRFSTradeRequest(String requestId, String providerName)
	{
		if ( requestId == null || providerName == null )
		{
			throw new IllegalArgumentException();
		}
		return rfsTradeRequestMap.get(providerName + "-" + requestId);
	}

	/**
	 * Add the RFSSubscribe request to the rfsSubscribeRequestMap
	 * @param request request
	 */
	public void addRFSSubscribe(RFSSubscribe request)
	{
		String providerShortName = request.getProviderShortName();
		String requestId = request.getRequestId();
		synchronized ( rfsSubscribeRequestMap )
		{
			rfsSubscribeRequestMap.put(providerShortName + "-" + requestId, request);
			log.warn("RFSAdaptorManager.addRFSSubscribe : Added RFSSubscribe for " + providerShortName + "-" + requestId);
		}
	}

	/**
	 * Add the RFSSubscribe request to the rfsSubscribeRequestMap If NotPresent
	 * @param request request
	 */
	public void addRFSSubscribeIfNotPresent(RFSSubscribe request)
	{
		String providerShortName = request.getProviderShortName();
		String requestId = request.getRequestId();
		synchronized ( rfsSubscribeRequestMap )
		{
			if(rfsSubscribeRequestMap.get(providerShortName + "-" + requestId) == null) {
				rfsSubscribeRequestMap.put(providerShortName + "-" + requestId, request);
				log.warn("RFSAdaptorManager.addRFSSubscribeIfNotPresent : Added RFSSubscribe for " + providerShortName + "-" + requestId);
			}
		}
	}

	/**
	 * Remove the RFSSubscribeRequest from map for the requestId corresponding to the Provider
	 * Add the RFSTradeRequest to the rfsTradeRequestMap for the requestId corresponding to the Provider if RFSSubscribeRequest was
	 * found.
	 * @param request request
	 * @return true if RFSTradeRequest was added. false implies RFSSubscribeRequest was not found.
	 * This may be due to expiry of RFS Subscription
	 */
	public boolean addRFSTradeRequest(RFSTradeRequest request , boolean checkSubscriptionCache)
	{
		RFSSubscribe rfsSubscribe;
		String providerShortName = request.getProviderShortName();
		String requestId = request.getRequestId();
		String key = providerShortName + "-" + requestId;
		boolean added = false;
		synchronized ( rfsTradeRequestMap )
		{
			synchronized ( rfsSubscribeRequestMap )
			{
				rfsSubscribe = rfsSubscribeRequestMap.remove(key);
			}
			if ( rfsSubscribe != null || !checkSubscriptionCache)
			{
				log.warn("RFSAdaptorManager.addRFSTradeRequest : Removed RFSSubscribe for " + key);
				rfsTradeRequestMap.put(key, request);
				added = true;
				log.warn("RFSAdaptorManager.addRFSTradeRequest : Added RFSTradeRequest for " + key);
			}
		}
		if ( rfsSubscribe != null )
		{
			String originatingReqId = rfsSubscribe.getOriginatingRequestId();
			String reqId = rfsSubscribe.getRequestId();
			String provider = rfsSubscribe.getProviderShortName();
			RequestExpireHandler.getInstance().removeOriginatingRequestId(provider, originatingReqId, reqId);
		}		
		if ( added )
		{
			String atBestOrderParam = ( String ) request.getProperty ( ISCommonConstants.AT_BEST_ORDER_RFS_PARAM );
			boolean isAtBestOrder = Boolean.TRUE.toString ().equalsIgnoreCase ( atBestOrderParam  );
			String rfqTradeParam = ( String ) request.getProperty ( ISCommonConstants.RFQ_TRADE_PARAM );
			boolean isRFQTrade = Boolean.TRUE.toString ().equalsIgnoreCase ( rfqTradeParam  );
			if ( !(isAtBestOrder || isRFQTrade) )
			{
				TimerTask tradeTimer = new TradeTimer ( providerShortName, requestId );
				synchronized ( rfsTradeTimerMap )
				{
					rfsTradeTimerMap.put ( key, tradeTimer );
				}
				autoCanTimer.schedule ( tradeTimer, ( long ) configMBean.getAutoCancellationTime () * 1000 );
				log.info ( "RFSAdaptorManager.addRFSTradeRequest : Auto Cancel Timer Task scheduled for " + key );
			}
			else
			{
				log.info ( "RFSAdaptorManager.addRFSTradeRequest : skipping auto Cancel Timer Task scheduled for at best order or RFQ trade. key=" + key );
			}
		}
		return added;
	}

	public boolean addRFSTradeRequest(RFSTradeRequest request)
	{
		return addRFSTradeRequest(request, true);
	}
	/**
	 * Remove RFSSubscribe request from the rfsSubscribeRequestMap
	 * @param requestId request id
	 * @param providerName provider name
	 */
	public void removeRFSSubscribe(String requestId, String providerName)
	{
		RFSSubscribe rfsSubscribe;
		synchronized ( rfsSubscribeRequestMap )
		{
			rfsSubscribe = rfsSubscribeRequestMap.remove(providerName + "-" + requestId);
			if ( rfsSubscribe != null )
			{
				log.warn("RFSAdaptorManager.removeRFSSubscribe : Removed RFSSubscribe for " + providerName + "-" + requestId);
			}
		}
		if ( rfsSubscribe != null )
		{
			String originatingReqId = rfsSubscribe.getOriginatingRequestId();
			RequestExpireHandler.getInstance().removeOriginatingRequestId(providerName, originatingReqId, requestId);
		}
	}

	/**
	 * Remove RFSTradeRequest from the rfsTradeRequestMap
	 * @param requestId request id
	 * @param providerName provider name
	 */
	public void removeRFSTradeRequest(String requestId, String providerName)
	{
		Object obj;
		String key = providerName + "-" + requestId;
		synchronized ( rfsTradeRequestMap )
		{
			obj = rfsTradeRequestMap.remove(key);
			if ( obj != null )
			{
				log.warn("RFSAdaptorManager.removeRFSTradeRequest : Removed RFSTradeRequest for " + key);
			}
		}
		if ( obj != null )
		{
			synchronized ( rfsTradeTimerMap )
			{
				TimerTask task = rfsTradeTimerMap.remove(key);
				if ( task != null )
				{
					log.warn("RFSAdaptorManager.removeRFSTradeRequest : Auto Cancel Timer Task removed for " + key);
					task.cancel();
					log.warn("RFSAdaptorManager.removeRFSTradeRequest : Auto Cancel Timer Task cancelled for " + key);
				}
			}
		}
	}

	/**
	 * Clear the rfsSubscribeRequestMap
	 */
	public void removeAllRFSSubscribe()
	{
		synchronized ( rfsSubscribeRequestMap )
		{
			rfsSubscribeRequestMap.clear();
			log.warn("RFSAdaptorManager.removeAllRFSSubscribe : Cleared rfsSubscribeRequestMap");
		}
	}

	/**
	 * Clear the rfsTradeRequestMap
	 */
	public void removeAllRFSTradeRequest()
	{
		synchronized ( rfsTradeRequestMap )
		{
			rfsTradeRequestMap.clear();
			log.warn("RFSAdaptorManager.removeAllRFSTradeRequest : Cleared rfsTradeRequestMap");
		}
	}

	/**
	 *
	 * @param providerShortName provider short name
	 * @return the total number of currently active RFS Subscriptions for the provider name. This method
	 * should be used in adaptors that support more than one provider e.g. DemoBank
	 */
	public int getRFSRequestSize(String providerShortName)
	{
		synchronized ( rfsSubscribeRequestMap )
		{
			Set<String> keys = rfsSubscribeRequestMap.keySet();
			int count = 0;
			Iterator<String> iterator = keys.iterator();
			String key;
			while (iterator.hasNext())
			{
				key = iterator.next();
				if ( key.startsWith(providerShortName + "-") )
					++count;
			}
			return count;
		}
	}

	/**
	 *
	 * @return the total number of currently active RFS Subscriptions
	 */
	public int getRFSRequestSize()
	{
		return rfsSubscribeRequestMap.keySet().size();
	}

	/**
	 *
	 * @param requestId request id
	 * @param providerName provider name
	 * @return The BrokerId corresponding to the requestId for the provider
	 */
	public String getBrokerId(String requestId, String providerName)
	{
		return AdaptorLoginManagerC.getInstance().getBroker(getServerID(requestId, providerName));
	}

	/**
	 *
	 * @param requestId request id
	 * @param providerName provider name
	 * @return the trade destination corresponding to the requestId for the provider
	 */
	public String getTradeDestination(String requestId, String providerName)
	{
		return AdaptorLoginManagerC.getInstance().getTradeQueue(getServerID(requestId, providerName));
	}


    /**
     *
     * @param requestId request id
     * @param providerShortName provider name
     * @return the trade destination session corresponding to the requestId for the provider
     */
    public IMTPSession getTradeDestinationSession(String requestId, String providerShortName) {
        String serverId = getServerID(requestId, providerShortName);
        return AdaptorLoginManagerC.getInstance().getLoggedInIMTPSession(serverId);
    }
	/**
	 *
	 * @param requestId request id
	 * @param providerName provider name
	 * @return the persistence message queue corresponding to the requestId for the provider
	 */
	public String getPersistenceMessageQueue(String requestId, String providerName)
	{
		return AdaptorLoginManagerC.getInstance().getPersistenceMessageQueue(getServerID(requestId, providerName));
	}

	/**
	 *
	 * @param requestId request id
	 * @param providerName provider name
	 * @return the serverId corresponding to the requestId for the provider
	 */
	public String getServerID(String requestId, String providerName)
	{
		RFSRequestMessage obj;
		obj = getRFSSubscribe(requestId, providerName);
		if ( obj == null )
		{
			obj = getRFSTradeRequest(requestId, providerName);
		}
		if ( obj != null )
		{
			return obj.getServerId();
		}
		return null;
	}

	public void processProviderResponse(RFSTradeResponse message)
	{
		String provider = message.getProvider();
		String tradeId = message.getTradeId();
		String key = provider + "-" + tradeId;
		synchronized ( rfsTradeTimerMap )
		{
			TimerTask task = rfsTradeTimerMap.remove(key);
			if ( task != null )
			{
				if ( task.cancel() )
				{
					log.warn("RFSAdaptorManager.processProviderResponse : Auto Cancel Task cancelled for " + key);
				}
			}
			RFSTradeRequest tradeReq = getRFSTradeRequest(tradeId, provider);
			if ( tradeReq == null )
			{
				log.error("RFSAdaptorManager.processProviderResponse : Auto Cancel Status Unknown. RFSTradeRequest not found for " + key);
				return;
			}
			Object rejected = tradeReq.getProperty(REJECTED_BY_ADAPTOR);
			if ( rejected != null )
			{
				if ( (Boolean) rejected )
				{
					log.warn("RFSAdaptorManager.processProviderResponse : RFS Trade for " + key + " has already been rejected by the adaptor");
					message.setStatus(TradeResponse.ALREADY_REJECTED_BY_ADAPTOR);
				}
			}
		}
	}

	private void verificationTimedOut(String providerName, String requestId)
	{
		String key = providerName + "-" + requestId;
		synchronized ( rfsTradeTimerMap )
		{
			TimerTask task = rfsTradeTimerMap.remove(key);
			if ( task == null )
			{
				log.warn("RFSAdaptorManager.verificationTimedOut : Provider's RFS Trade Response has already been sent for->" + key);
				return;
			}
			//Send Rejection to IS if AutoCancel is enabled else send a Pending Message
			Timing timing = new TimingC();
			timing.setTime(ISCommonConstants.EVENT_TIME_DISP_TIMEOUT_AT_ADAPTOR, System.currentTimeMillis());
			RFSTradeRequest tradeReq = getRFSTradeRequest(requestId, providerName);
			if ( tradeReq == null )
			{
				log.error("RFSAdaptorManager.verificationTimedOut : RFSTradeRequest not found for " + key);
				return;
			}
			if ( configMBean.isAutoCancellationApplicable() )
			{
				RFSTradeReject tr = MessageFactory.newRFSTradeReject();
				tr.setTradeId(tradeReq.getRequestId());
				tr.setBaseCurrency(tradeReq.getBaseCurrency());
				tr.setVariableCurrency(tradeReq.getVariableCurrency());
				tr.setLegalEntity(tradeReq.getLeShortName());
				tr.setProvider(tradeReq.getProviderShortName());
				tr.setUser(tradeReq.getUserShortName());
				tr.setOrganization(tradeReq.getOrgShortName());
				tr.setRejectReason(ISCommonConstants.RFS_REJECTED_BY_ADAPTOR);
				tr.setStatus(TradeResponse.REJECTED_BY_ADAPTOR);
				tr.setTiming(timing);
				log.warn("RFSAdaptorManager.verificationTimedOut : Sending RFSTradeReject msg for " + key);
				ResponseHandlerC.getInstance().rfsTradeRecieved(tr, false);
				tradeReq.setProperty(REJECTED_BY_ADAPTOR, true);
			}
			else
			{
				Object pendingSent = tradeReq.getProperty(PENDING_SENT_BY_ADAPTOR);
				if ( pendingSent != null && (Boolean) pendingSent )
				{
					log.warn("RFSAdaptorManager.verificationTimedOut : RFSTradePending has already been sent for " + key);
				}
				RFSTradePending trdPending = MessageFactory.newRFSTradePending();
				trdPending.setTradeId(tradeReq.getRequestId());
				trdPending.setBaseCurrency(tradeReq.getBaseCurrency());
				trdPending.setVariableCurrency(tradeReq.getVariableCurrency());
				trdPending.setLegalEntity(tradeReq.getLeShortName());
				trdPending.setProvider(tradeReq.getProviderShortName());
				trdPending.setUser(tradeReq.getUserShortName());
				trdPending.setOrganization(tradeReq.getOrgShortName());
				trdPending.setTiming(timing);
				log.warn("RFSAdaptorManager.verificationTimedOut : Sending RFSTradePending msg for " + key);
				ResponseHandlerC.getInstance().rfsTradePendingRecieved(trdPending);
				tradeReq.setProperty(PENDING_SENT_BY_ADAPTOR, true);
			}
		}
	}

	public void sendRFSTradePending(String providerName, String requestId)
	{
		String key = providerName + "-" + requestId;
		synchronized ( rfsTradeTimerMap )
		{
			RFSTradeRequest tradeReq = getRFSTradeRequest(requestId, providerName);
			if ( tradeReq == null )
			{
				log.error("RFSAdaptorManager.sendRFSTradePending : RFSTradeRequest not found for " + key);
				return;
			}
			Object pendingSent = tradeReq.getProperty(PENDING_SENT_BY_ADAPTOR);
			if ( pendingSent != null && (Boolean) pendingSent )
			{
				log.warn("RFSAdaptorManager.sendRFSTradePending : RFSTradePending has already been sent for " + key);
				return;
			}
			RFSTradePending trdPending = MessageFactory.newRFSTradePending();
			trdPending.setTradeId(tradeReq.getRequestId());
			trdPending.setBaseCurrency(tradeReq.getBaseCurrency());
			trdPending.setVariableCurrency(tradeReq.getVariableCurrency());
			trdPending.setLegalEntity(tradeReq.getLeShortName());
			trdPending.setProvider(tradeReq.getProviderShortName());
			trdPending.setUser(tradeReq.getUserShortName());
			trdPending.setOrganization(tradeReq.getOrgShortName());
			log.warn("RFSAdaptorManager.sendRFSTradePending : Sending RFSTradePending msg for " + key);
			ResponseHandlerC.getInstance().rfsTradePendingRecieved(trdPending);
			tradeReq.setProperty(PENDING_SENT_BY_ADAPTOR, true);
		}
	}

	/**
	 *
	 * TradeTimer class sends RFSTradePending/RFSTradeReject based on the autoCancellation flag
	 * if no response has been received from the provider within autoCancellation time
	 *
	 */
	private class TradeTimer extends TimerTask
	{
		String providerName;
		String requestId;

		private TradeTimer(String providerName, String requestId)
		{
			this.providerName = providerName;
			this.requestId = requestId;
		}

		public void run()
		{
			try
			{
				log.warn("RFSAdaptorManager.TradeTimer.run : Verification Timed Out for " + providerName + "-" + requestId);
				verificationTimedOut(providerName, requestId);
			}
			catch ( Exception e )
			{
				log.error("TradeTimer.run : exception ", e);
			}
		}
	}
}
