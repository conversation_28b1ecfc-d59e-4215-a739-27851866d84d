package com.integral.adaptor.heartbeat;

import java.util.ArrayList;
import java.util.List;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.commons.serialize.Storeable;
import com.integral.is.message.ProviderStatus;
import com.integral.is.message.ProviderStatusC;

public class ProviderStatusSerializer {

	private final static int VERSION_OFFSET = 0;

	private final static int MSG_TYPE_OFFSET = VERSION_OFFSET + UnSafeBuffer.getSizeOfByte();

	public static byte getVersion(UnSafeBuffer safeBuf) {
		return UnSafeBuffer.getByte(safeBuf.array(), VERSION_OFFSET);
	}

	public static short getMsgType(UnSafeBuffer safeBuf) {
		return UnSafeBuffer.getShort(safeBuf.array(), MSG_TYPE_OFFSET);
	}

	public static int getEstimated(Storeable storeable) {
		return storeable.getEstimatedSize();
	}

	public static void serialize(short msgType, byte version, Storeable msg, UnSafeBuffer safeBuf) {
		safeBuf.put(version);
		safeBuf.putShort(msgType);
		msg.writeTo(safeBuf);
	}

	public static void deserialize(short msgType, byte version, Storeable msg,UnSafeBuffer safeBuf) {		
		msg.readFrom(safeBuf);
	}
	
	
	public static Object deserialize(UnSafeBuffer safeBuf) {
		byte version = safeBuf.get();
		short messageType = safeBuf.getShort();
		
		switch (messageType){
  		case ProviderStatus.MSG_TYPE_PROVIDER_STATUS:
  		  ProviderStatus status = new ProviderStatusC();
  		  status.readFrom(safeBuf);
        return status;
  		case ProviderStatus.MSG_TYPE_BULK_PROVIDER_STATUS:
  		  int size = safeBuf.getInt();
  	     List <ProviderStatus> list = new ArrayList<ProviderStatus>(size);
  	     for(int i=0;i<size;i++){
  	       ProviderStatus providerStatus = new ProviderStatusC();
  	       providerStatus.readFrom(safeBuf);
  	       list.add(providerStatus);
  	     }
  	    return list;
		}
		return null;
	}
	
	public static void serialize(short msgType, byte version, List <ProviderStatus> messages, UnSafeBuffer safeBuf){			
			safeBuf.put(version);
			safeBuf.putShort(msgType);
			safeBuf.putInt(messages.size());
			for(Storeable msg: messages){
				msg.writeTo(safeBuf);
			}
		
	}
	

}
