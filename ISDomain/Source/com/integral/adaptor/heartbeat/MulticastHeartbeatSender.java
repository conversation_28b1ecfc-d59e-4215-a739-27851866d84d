package com.integral.adaptor.heartbeat;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.util.List;

import com.integral.adaptor.config.AdaptorConfiguration;
import com.integral.adaptor.config.AdaptorConfigurationFactory;
import com.integral.adaptor.response.ResponseHandler;
import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.imtp.config.IMTPVersion;
import com.integral.imtp.connection.IMTPConnectionManager;
import com.integral.is.message.ProviderStatus;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.IdcMBeanC;

public class MulticastHeartbeatSender {
	final static Log log = LogFactory.getLog( MulticastHeartbeatSender.class );
	  
	private boolean shouldSendHeartbeats = false;
	private MulticastSocket socket;
	private InetAddress add;
	private int port;

	public void sendHeartbeat(ProviderStatus providerStatusMessage) {
	        try
	        {
                 if ( providerStatusMessage != null ){
	                  	UnSafeBuffer unsafeBuf = new UnSafeBuffer();		                
	                  	unsafeBuf.init(new byte[providerStatusMessage.getEstimatedSize()]);
		                ProviderStatusSerializer.serialize(ProviderStatus.MSG_TYPE_PROVIDER_STATUS, ProviderStatus.VERSION, providerStatusMessage, unsafeBuf);
		                unsafeBuf.flip();
		                send(unsafeBuf);		            	                
                 }	    
	        } catch ( Exception t )
	        {
	            log.warn( "MulticastHeartbeatSenderC.statusSenderTask: An error occurred while sending heartbeat to destination " +providerStatusMessage, t );
	        }

	}
	public void sendHeartbeat(List<ProviderStatus> providerStatusMessages) {
        try
        {
             if ( providerStatusMessages != null ){
                  	UnSafeBuffer unsafeBuf = new UnSafeBuffer();		               
                  	if(providerStatusMessages.size() > 0){
	                  	unsafeBuf.init(new byte[256  * providerStatusMessages.size()]);
		                ProviderStatusSerializer.serialize(ProviderStatus.MSG_TYPE_BULK_PROVIDER_STATUS,ProviderStatus.VERSION, providerStatusMessages, unsafeBuf);
		                unsafeBuf.flip();
		                send(unsafeBuf);		            	
                  	}
             }	    
        } catch ( Exception t )
        {
            log.warn( "MulticastHeartbeatSenderC.statusSenderTask: An error occurred while sending heartbeat to destination " +providerStatusMessages, t );
        }

}
	         
	 private void send(UnSafeBuffer buff) {
	  	    DatagramPacket packet = new DatagramPacket(buff.array(), buff.remaining(), add, port);
	      	    try {
	        	      socket.send(packet);
	        	    } catch (IOException e) {
	        	      log.warn("MulticastHeartbeatSenderC.send unable to send data on multicast for " + add.getHostAddress() + ":" + port);
	        	    }
	        	  }


	public void start() {		
		 AdaptorConfiguration config =  AdaptorConfigurationFactory.getAdaptorConfigurationMBean();	      
	       try
           {	       
		       this.add = InetAddress.getByName(  config.getMulticastHeartBeatGroup() );		        
		       this.port = config.getMulticastHeartBeatPort(); 
		       this.socket = new MulticastSocket();
		       this.socket.setTimeToLive(ConfigurationFactory.getServerMBean().getMulticastTTL());
		       shouldSendHeartbeats = true;		       
	       }catch(Exception e){
        	   log.warn( "MulticastHeartbeatSenderC.start unable to initialize the multicast proxy " ,e );
                 
           }

	}

	public void stop() {
		try{
			this.socket.close(); 
			shouldSendHeartbeats = false;
		}catch(Exception e){
     	   log.warn( "MulticastHeartbeatSenderC.stop unable to stop the multicast proxy " ,e );              
        }
	}

}
