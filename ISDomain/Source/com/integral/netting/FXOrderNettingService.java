package com.integral.netting;

import com.integral.finance.dealing.Request;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.OrderRequest;
import com.integral.model.dealing.Trade;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 5/29/13
 * Time: 3:00 PM
 * A service to implement Order level Trade Netting.
 */
public interface FXOrderNettingService<T,O> {

    public static final char HYPHEN = '-';

    /**
     * Adds a trade to the order bucket for netting.
     *
     * @param trade to be netted.
     * @return true if considered for netting, false if trade doesn't qualify for netting.
     */
    boolean addTrade(T trade);


    /**
     * Add trades to order bucket for netting - Used for Reloaded orders
     */
    void addTrades(List<T> trades);

    /**
     * Checks if user needs individual notification if trade was qualified for netting.
     *
     * @param trade to be netted.
     * @return true if user need to be notified, false otherwise.
     */
    boolean doNotifyUser(T trade);

    /**
     * Nets the trades collected against the given order and returns the netted trade object.
     * Old trades are cancelled and new trades are created after netting.
     *
     * @param orderRequest whose trades need to be netted.
     * @return netted trade.
     */
    List<T> netTrade(O orderRequest);
    List<T> netTradeWithAttributes(O orderRequest, WorkflowMessage msg);

    /**
     * API to shutdown executors etc., if any
     */
    void doCleanup();

    Double getNettedAmount(String orderId);

    void setPBKPIAndComputeOrderAvgRate(T trade, boolean inNettingBucket);
}
