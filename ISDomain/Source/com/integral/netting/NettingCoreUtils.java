package com.integral.netting;

import com.integral.admin.utils.StringUtils;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import org.joda.time.LocalTime;

/**
 * Created by avinashpandit on 12/14/16.
 */
public class NettingCoreUtils
{
    private static NettingCoreUtils instance = new NettingCoreUtils();
    private static Log log = LogFactory.getLog( NettingCoreUtils.class );

    public static NettingCoreUtils getInstance()
    {
        return instance;
    }

    private NettingCoreUtils()
    {
    }


    //time in "hh:mm" format as defined in property
    public static LocalTime getTime( String time, String defaultTime )
    {
        if ( StringUtils.isNullOrEmptyString( time ) )
        {
            log.info( "getTime: failed to parse time defaulting now to " + defaultTime );
            time = defaultTime;
        }
        try
        {
            String[] etarr = time.split( ":" );
            int hh = Integer.valueOf( etarr[0] );
            int mm = Integer.valueOf( etarr[1] );
            return new LocalTime( hh, mm );
        }
        catch ( Exception ex )
        {
            log.error( "getTime: failed to parse time ", ex );
            return new LocalTime( 0, 0 );
        }
    }


    public static boolean isInSession( LocalTime startTime, LocalTime endTime )
    {
        LocalTime currentTime = new LocalTime();
        if ( startTime.compareTo( endTime ) < 0 )
        {
            // StartTime and EndTime are on the same date and startTime < endTime
            // Check if currentTime is between start and end times
            if ( ( startTime.compareTo( currentTime ) < 0 ) && ( endTime.compareTo( currentTime ) > 0 ) )
            {
                return true;
            }
            return false;
        }
        else
        {
            // StartTime is greater than EndTime
            if ( ( endTime.compareTo( currentTime ) < 0 ) && ( startTime.compareTo( currentTime ) > 0 ) )
            {
                return false;
            }
            return true;
        }
    }

    public static long getStartDelay( LocalTime startTime, LocalTime currentTime, long interval )
    {
        long delay;
        if ( startTime.compareTo( currentTime ) < 0 )
        {
            long currentTimeDiff = ( currentTime.getMillisOfDay() - startTime.getMillisOfDay() );
            delay = interval - ( currentTimeDiff % interval );
        }
        else
        {
            long currentTimeDiff = ( startTime.getMillisOfDay() - currentTime.getMillisOfDay() );
            delay = ( currentTimeDiff % interval );
        }
        return delay;
    }

}
