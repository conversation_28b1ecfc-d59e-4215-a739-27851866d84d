package com.integral.priceregeneration;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.dealing.priceRegeneration.*;
import com.integral.finance.dealing.priceRegeneration.fx.PriceRegenerationWrapperCacheC;
import com.integral.is.spaces.fx.esp.factory.DealingModelFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.UserSubscriptionInfo;
import com.integral.message.MessageHandler;
import com.integral.model.dealing.SingleLegOrderMatch;
import com.integral.user.Organization;

/**
 * Created with IntelliJ IDEA.
 * User: anatarajan
 * Date: 11/20/14
 * Time: 11:54 AM
 * To change this template use File | Settings | File Templates.
 */
public class PriceRegenerationFacade {
    private static PriceRegenerationFacade priceRegenerationFacade;
    private static PriceRegenerationService prservice = PriceRegenerationServiceFactory.getPriceRegenerationService();
    private static Log log = LogFactory.getLog(PriceRegenerationFacade.class);

    static {
        priceRegenerationFacade = new PriceRegenerationFacade();
    }

    public static PriceRegenerationFacade getInstance() {
        return priceRegenerationFacade;
    }

    public static void set_instance(PriceRegenerationFacade facade) {
        log.error("TO BE USED ONLY FOR TEST CASES, REPORT A BUG IF SEEN OTHERWISE");
        priceRegenerationFacade = facade;
    }

    public boolean startPriceRegeneration(SingleLegOrderMatch matchEvent, Organization lpOrg) {
        if (matchEvent.isIntraFloor()) {
            if (log.isDebugEnabled()) {
                log.debug("startPriceRegeneration : Price Regeneration not applicable for IntraFloor match = " + matchEvent.get_id());
            }
            return true;
        }
        Organization customerOrg = (Organization) matchEvent.getOrganization();
        LegalEntity customerOrgDefaultLE = customerOrg.getDefaultDealingEntity();
        TradingParty tp = customerOrgDefaultLE.getTradingParty(lpOrg);
        if (tp == null || !prservice.isPriceRegenerationEnabled(lpOrg, tp)) {
            if (tp == null) {
                log.warn("startPriceRegeneration : No Trading Party defined for Customer LE " + customerOrgDefaultLE.getFullyQualifiedName() + " in " + lpOrg.getShortName());
                log.warn("startPriceRegeneration : Price Regeneration will not be done for " + matchEvent.get_id());
            }
            return true;
        }
        String selector = UserSubscriptionInfo.createSelector(lpOrg.getShortName(), matchEvent.getCurrencyPair().getName(), matchEvent.getOrgShortName());
        boolean priceRegenSuccess;
        String acceptedQuoteRateId = matchEvent.getMatchedQuote().getExternalQuoteId();
        MessageHandler priceRegenHandler = DealingModelFactory.getInstance().newPriceRegenerationUpdateHandler(lpOrg.getShortName(), matchEvent.get_id(), selector, acceptedQuoteRateId);
        priceRegenSuccess = prservice.startPriceRegeneration(matchEvent, priceRegenHandler, tp);
        matchEvent.setPriceRegenStarted(priceRegenSuccess);
        if (log.isDebugEnabled()) {
            if (priceRegenSuccess) {
                log.debug("PriceRegeneration started for Match " + matchEvent.get_id());
            }
        }
        return priceRegenSuccess;
    }

    public void stopPriceRegeneration(SingleLegOrderMatch matchEvent) {
        if (matchEvent.isPriceRegenStarted()) {
            MessageHandler priceRegenHandler;
            Organization customerOrg = (Organization) matchEvent.getOrganization();
            LegalEntity customerOrgDefaultLE = customerOrg.getDefaultDealingEntity();
            Organization lpOrg = matchEvent.getMatchedQuote().getMaskedOrganization();
            TradingParty tp = customerOrgDefaultLE.getTradingParty(lpOrg);
            if (tp == null || !prservice.isPriceRegenerationEnabled(lpOrg, tp)) {
                if (tp == null) {
                    log.warn("stopPriceRegeneration : No Trading Party defined for Customer LE " + customerOrgDefaultLE.getFullyQualifiedName() + " in " + lpOrg.getShortName());
                    log.warn("stopPriceRegeneration : Stop Price Regeneration will not be done for " + matchEvent.get_id());
                }
                return;
            }
            String selector = UserSubscriptionInfo.createSelector(lpOrg.getShortName(), matchEvent.getCurrencyPair().getName(), matchEvent.getOrgShortName());
            String acceptedQuoteRateId = matchEvent.getQuoteForTradeRequest().getExternalQuoteId();
            priceRegenHandler = DealingModelFactory.getInstance().newPriceRegenerationUpdateHandler(lpOrg.getShortName(), matchEvent.get_id(), selector, acceptedQuoteRateId);
            prservice.stopPriceRegeneration(matchEvent, priceRegenHandler, tp);
            log.info("PriceRegeneration stopped for Match " + matchEvent.get_id());
        }
    }

    /**
     * This method should be invoked only when isUsePriceRegenerationInOrderMatch is set to true in SingleLegOrder.
     * Otherwise will return incorrect value.
     * @param amount
     * @param tier
     * @param isBid
     * @return
     */
    public boolean hasSufficientLiquidity(double amount, int tier, boolean isBid, String key) {
        boolean result = false;
        if (amount > 0.0 && key != null) {
            PriceRegenerationWrapper wrapper = PriceRegenerationWrapperCacheC.getInstance().getPriceRegenerationWrapper(key);
            result = wrapper.hasSufficientLiquidity(amount, tier, isBid);
        }
        return result;
    }

}
