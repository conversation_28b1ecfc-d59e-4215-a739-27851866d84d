package com.integral.pricing.subscription;

// Copyright (c) 2023 Integral Development Corp.  All rights reserved.

import com.integral.is.message.MarketRate;
import com.integral.is.message.MarketRateSerializerFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.messaging.*;
import com.integral.messaging.config.MessagingConfiguration;
import com.integral.pricing.config.PricingConfigurationFactory;
import com.integral.pricing.config.PricingConfigurationMBean;
import com.integral.pricing.core.PricingRequestConstants;
import com.integral.pricing.core.PricingRequestService;
import com.integral.pricing.util.PricingThreadPoolFactory;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.system.runtime.ServerRuntimeMBean;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Listens on EXCELPRICINGX to excel spot rate updates.
 */
public class ExcelPricingRateListenerC extends DefaultMessageListener
{
    private final PricingRequestService pricingRequestService;
    private static final Log log = LogFactory.getLog ( ExcelPricingRateListenerC.class );
    private static final ServerMBean _serverMBean = ConfigurationFactory.getServerMBean ();
    private final ConcurrentHashMap<String, Long> timestampMap = new ConcurrentHashMap<String, Long> ();
    private final ConcurrentHashMap<String, Long> counterMap = new ConcurrentHashMap<String, Long> ();
    private static final PricingConfigurationMBean pricingConfigMBean = PricingConfigurationFactory.getPricingConfigurationMBean ();

    ExcelPricingRateListenerC ( PricingRequestService pricingRequestService )
    {
        this.pricingRequestService = pricingRequestService;
    }

    public static void enableExcelPricingRateListener ( ExcelPricingRateListenerC listener, String pricingSource, String brokerName )
    {
        // skip the credit update messages if the current virtual server type is not broker adaptor
        if ( !ConfigurationFactory.getServerMBean ().isBrokerAdaptor () )
        {
            log.info ( "EPRL.enableExcelPricingRateListener : listener is not setup as it is not a FXI dealing server or notification server. serverType=" + ConfigurationFactory.getServerMBean ().getVirtualServerType () );
            return;
        }

        try
        {
            String exchangeName = ServerRuntimeMBean.EXCEL_PRICING_EXCHANGE;
            String queueName = MessagingConfiguration.getInstance ().getUserDBPrefix () + "_" + _serverMBean.getVirtualServerName () + "_" + exchangeName;
            log.info ( "EPRL.enableExcelPricingRateListener : set up RMQ excel pricing rate listener=" + listener
                    + ",exchange=" + exchangeName + ",pricingSource=" + pricingSource + ",brokerName=" + brokerName );
            setupMessageListener ( listener, queueName, exchangeName, pricingSource, brokerName );
        }
        catch ( Exception e )
        {
            log.error ( "EPRL.enableExcelPricingRateListener : excel pricing message listener failed to start.", e );
        }
    }

    public static void setupMessageListener ( com.integral.messaging.MessageListener listener, String queueName, String exchangeName, String pricingSource, String brokerName ) throws MessagingException
    {
        MessageReceiver receiver = MessageReceiverFactory.newMessageReceiver ( exchangeName, queueName, false, true, true, false, listener );
        receiver.addBinding ( pricingSource + "@" + brokerName + "." + "#" );
    }

    @Override
    public void onMessage ( final RMQMessage message )
    {
        try
        {
            PricingThreadPoolFactory.getInstance ().getSingleThreadReceiver ().execute ( new Runnable ()
            {
                @Override
                public void run ( )
                {
                    parseMessage ( message );
                }
            } );
        }
        catch ( Exception e )
        {
            log.error ( "EPRL.onMessage - exception while handling the excel rate message. msg=" + message );
        }

        try
        {
            message.ack ();
        }
        catch ( MessagingException e )
        {
            log.error ( "EPRL.onMessage - Exception in sending the ack for message:" + message.getPayload ()
                    + ":routingKey=" + message.getRoutingKey (), e );
        }
    }

    private void parseMessage ( RMQMessage message )
    {
        String msgText = null;
        String pricingSource = null;
        try
        {
            final int serVersion = pricingConfigMBean.getPrcMrktSerializerVer ();
            MessageHolder messageHolder = ( MessageHolder ) message.getPayload ();
            msgText = ( String ) messageHolder.getMessage ();
            Map<String, String> properties = messageHolder.getMessageProperties ();
            pricingSource = properties.get ( PricingRequestConstants.PRICING_SOURCE );
            final MarketRate rate = MarketRateSerializerFactory.instance ().getSerializerForVersion ( serVersion ).deserialize ( msgText );
            if ( log.isDebugEnabled () )
            {
                log.debug ( "EPRL.parseMessage - received excel rate message=" + msgText + ",messageHolder=" + messageHolder + ",pricingSource=" + pricingSource + ",marketRate=" + rate );
            }
            if ( rate.getTenor () != null && rate.getTenor ().isSpot () )
            {
                String quoteId = rate.getQuoteId ();
                String[] tokens = quoteId.split ( "-" );
                String timestampStr = tokens[2];
                long timestamp = Long.parseLong ( timestampStr, 16 );
                String counterStr = tokens[4];
                long counter = Long.parseLong ( counterStr, 16 );
                String cp = rate.getBaseCcy () + '/' + rate.getVariableCcy ();
                Long lastTime = timestampMap.put ( cp, timestamp );
                Long lastCount = counterMap.put ( cp, counter );
                if ( lastTime == null )
                {
                    lastTime = 0L;
                }
                if ( lastCount == null )
                {
                    lastCount = -1L;
                }
                boolean isOld = false;
                if ( lastTime > timestamp )
                {
                    isOld = true;
                }
                else if ( lastTime == timestamp )
                {
                    if ( lastCount >= counter )
                    {
                        isOld = true;
                    }
                }
                if ( isOld )
                {
                    timestampMap.put ( cp, lastTime );
                    counterMap.put ( cp, lastCount );
                    // record and drop the rate
                    log.warn ( "EPRL.parseMessage - rate : " + quoteId + "  dropped for pricing source less than last sequence: " + pricingSource + " " );
                    log.warn ( "EPRL.parseMessage - rate : last time : " + lastTime + " counter: " + lastCount + "  : recent time : " + timestamp + " count=" + counter );
                    return;
                }
                if ( log.isDebugEnabled () )
                {
                    log.debug ( "EPRL.parseMessage - rate : " + quoteId + "  sent for pricing source : " + pricingSource );
                }
                pricingRequestService.onRateReceive ( rate, pricingSource );
            }
            else
            {
                log.debug ( "EPRL.parseMessage - rate : " + rate + "  dropped as rate is not ESP" );
            }
        }
        catch ( Exception ee )
        {
            log.error ( "EPRL.parseMessage - Exception while sending market Rate mds=" + pricingSource + ", msg=" + msgText + ",pricingSource=" + pricingSource, ee );
        }
    }
}