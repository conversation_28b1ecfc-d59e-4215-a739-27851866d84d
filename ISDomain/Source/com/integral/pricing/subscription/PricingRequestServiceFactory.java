package com.integral.pricing.subscription;

import com.integral.pricing.core.PricingRequestService;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Factory class for MDS specific instantiation
 */
public class PricingRequestServiceFactory
{
    protected static final PricingRequestServiceFactory instance = new PricingRequestServiceFactory();
    private static ConcurrentMap<String, PricingRequestService> pricingServiceProviders = new ConcurrentHashMap<String, PricingRequestService>();

    private PricingRequestServiceFactory()
    {

    }

    public static PricingRequestServiceFactory getInstance()
    {
        return instance;
    }

    public PricingRequestService
    getExcelPricingRequestService( String pricingSource, String nameSpace, String orgName )
    {
        String key = getKey( pricingSource, nameSpace, orgName );
        PricingRequestService requestService = pricingServiceProviders.get( key );
        if ( requestService == null )
        {
            ExcelPricingRequestService newRequestService = new ExcelPricingRequestService(pricingSource , orgName);
            requestService = pricingServiceProviders.putIfAbsent( key, newRequestService );
            if ( requestService == null )
            {
                requestService = newRequestService;
            }
        }
        return requestService;
    }

    private String getKey( String pricingSource, String nameSpace, String orgName )
    {
        String key = nameSpace + '-' + orgName + '-' + pricingSource;
        return key;
    }

    /**
     * For JSP utility
     * @return map
     */
    public static Map<String, PricingRequestService> getPricingServiceProvidersMap()
    {
        return Collections.unmodifiableMap( pricingServiceProviders );
    }
}
