package com.integral.pricing.subscription;

import com.integral.broker.quote.QuoteHandler;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.common.cache.bucket.MessageNotifier;
import com.integral.user.Organization;

public class PricingSubscriptionRequestC implements PricingSubscriptionRequest {
	
	private String mdsName;
	private Organization organization;
	private CurrencyPair ccyp;
	private QuoteHandler qH;
	
	public PricingSubscriptionRequestC(String mdsName, Organization organization, CurrencyPair ccyp, QuoteHandler qH) {
		this.mdsName = mdsName;
		this.organization = organization;
		this.ccyp = ccyp;
		this.qH = qH;
	}

	@Override
	public String getPricingSource() {
		return mdsName;
	}

	@Override
	public Organization getOrganization() {
		return organization;
	}

	@Override
	public CurrencyPair getCurrencyPair() {
		return ccyp;
	}

	@Override
	public MessageNotifier getQuoteHandler() {
		return (MessageNotifier) qH;
	}
	
	@Override
	public String toString() {
		return "PricingSubscriptionRequestC [mdsName=" + mdsName
				+ ", organization=" + organization + ", ccyp=" + ccyp + ", qH="
				+ qH + "]";
	}

}
