package com.integral.pricing.subscription;

import com.integral.broker.BrokerAdaptorFactory;
import com.integral.broker.config.BrokerAdaptorMBean;
import com.integral.broker.quote.BrokerQuoteFactory;
import com.integral.broker.quote.ExcelQuoteHandlerC;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.marketData.fx.MDSUtil;
import com.integral.finance.marketData.model.FXMDSUpdateEvent;
import com.integral.finance.marketData.model.MDSUpdateEvent;
import com.integral.finance.marketData.service.MDSService;
import com.integral.finance.marketData.service.MDSUpdateEventListener;
import com.integral.finance.marketData.service.MDSUpdateEventNotifier;
import com.integral.finance.trade.Tenor;
import com.integral.is.common.ProviderQuoteIdFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.message.MarketRate;
import com.integral.is.message.MarketRateC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.quote.ExcelPricingRateEventC;
import com.integral.message.StatusMessage;
import com.integral.message.StatusMessageFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.pricing.config.PricingConfigurationFactory;
import com.integral.pricing.core.PricingRequestService;
import com.integral.time.IdcDate;
import com.integral.user.Organization;

import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Class for publishing ESP prices from External excel pricing.
 */
public class ExcelPricingRequestService implements PricingRequestService, MDSUpdateEventListener<FXMDSUpdateEvent>
{
    /**
     * Logger for this class and its descendants.
     */
    protected Log log = LogFactory.getLog ( this.getClass () );
    AtomicInteger sequenceId = new AtomicInteger ();
    private final BrokerAdaptorMBean mBean;
    private final String brokerName;
    private final String mdsName;
    private final ExcelQuoteHandlerC quoteHandler;

    private final ConcurrentHashMap<String, AtomicLong> subscriptionCountMap = new ConcurrentHashMap<String, AtomicLong> (); //ccyPair : AtomicLong

    /*
     *  This cache will be used to feed SPOT cached rates to quote handler
     *  during new subscription request to avoid dependency on rate update
     *  tick for showing up rates on client.
     */
    private static final ConcurrentHashMap<String, MarketRate> rateCache = new ConcurrentHashMap<String, MarketRate> (); //ccyPair : rate

    /*
     * caching for maintaining listeners  for pricing updates
     */
    private final ConcurrentMap<String, ExcelPricingRateListenerC> excelListenerCache = new ConcurrentHashMap<String, ExcelPricingRateListenerC> ();

    protected ExcelPricingRequestService ( String mdsName, String brokerName )
    {
        this.mdsName = mdsName;
        this.brokerName = brokerName;
        this.mBean = BrokerAdaptorFactory.getInstance ().getBrokerAdaptorMBean ( brokerName );
        this.quoteHandler = ( ExcelQuoteHandlerC ) BrokerQuoteFactory.getInstance ().getExcelQuoteHandler ( ReferenceDataCacheC.getInstance ().getOrganization ( brokerName ) );
    }

    public synchronized StatusMessage subscribe ( com.integral.pricing.subscription.PricingSubscriptionRequest subscriptionRequest )
    {
        Organization fiOrg;
        if ( subscriptionRequest.getOrganization () == null )
        {
            return StatusMessageFactory.getInstance ().getInvalidFI ();
        }
        else
        {
            fiOrg = validateFI ( subscriptionRequest.getOrganization ().getShortName () );
            if ( fiOrg == null )
            {
                return StatusMessageFactory.getInstance ().getInvalidFI ();
            }
        }
        if ( subscriptionRequest.getCurrencyPair () == null )
        {
            return StatusMessageFactory.getInstance ().getInvalidCP ();
        }

        if ( subscriptionRequest.getPricingSource () == null )
        {
            return StatusMessageFactory.getInstance ().getInvalidPricingSource ();
        }

        String mdsName = subscriptionRequest.getPricingSource ();
        String cpName = subscriptionRequest.getCurrencyPair ().getName ();
        incrementSubscriptionCount ( cpName );
        registerWithMDSListener ( mdsName, cpName );
        log.info ( "ExcelPricingRequestService:subscription:: subscription added for Org :" + brokerName
                + " pricing source :  " + mdsName + " for CP : " + cpName );

        // setup RMQ listener as well
        ExcelPricingRateListenerC excelListener = excelListenerCache.get ( mdsName );
        if ( excelListener == null )
        {
            excelListener = new ExcelPricingRateListenerC ( this );
            ExcelPricingRateListenerC.enableExcelPricingRateListener ( excelListener, mdsName, brokerName );
            excelListenerCache.putIfAbsent ( mdsName, excelListener );
        }

        String rateCacheKey = mdsName + '_' + cpName;
        MarketRate rate = getCachedRate ( rateCacheKey );
        if ( rate != null )
        {
            log.info ( "ExcelPricingRequestService.subscribe() : Cached rate is picked up for new subscription request - " + subscriptionRequest );
            onRateReceive ( rate, mdsName );
        }
        return new StatusMessage.SuccessMessage ();
    }

    private void registerWithMDSListener ( String mdsName, String cp )
    {
        //assuming the registration is idempotent
        //MDSChangeListener.getInstance().addPricingService(mdsName, cp, this);
        MDSService<FXMDSUpdateEvent> mdsService = MDSService.getInstance ();
        MDSUpdateEventNotifier<FXMDSUpdateEvent, MDSUpdateEventListener<FXMDSUpdateEvent>> updateEventNotifier = mdsService.getMDSUpdateEventNotifier ();
        String mdsKey = MDSUtil.getMDSKey ( brokerName, mdsName );
        updateEventNotifier.registerMDSUpdateEventListener ( mdsKey, this );
        log.info ( "ExcelPricingRequestService.registerWithMDSListener:: Registered for mds=" + mdsName + ", cp=" + cp + ", br=" + brokerName );
    }

    public synchronized StatusMessage unSubscribe ( com.integral.pricing.subscription.PricingSubscriptionRequest subscriptionRequest )
    {

        Organization fiOrg = validateFI ( subscriptionRequest.getOrganization ().getShortName () );
        if ( fiOrg == null )
        {
            return StatusMessageFactory.getInstance ().getInvalidFI ();
        }
        String mdsName = subscriptionRequest.getPricingSource ();
        String cpName = subscriptionRequest.getCurrencyPair ().getName ();
        decrementSubscriptionCount ( cpName );
        String rateCacheKey = mdsName + '_' + cpName;
        rateCache.remove ( rateCacheKey );
        log.info ( "ExcelPricingRequestService:unSubscribe:: un subscription for Org :" + brokerName
                + " pricing source :  " + mdsName + " for CP : " + cpName );
        return new StatusMessage.SuccessMessage ();
    }

    private Organization validateFI ( String fiName )
    {
        // check if the Org exists, if not return false.
        return ReferenceDataCacheC.getInstance ().getOrganization ( fiName );
    }

    /**
     * call back from market rate listener
     *
     * @param rate          rate
     * @param pricingSource source
     */
    public void onRateReceive ( MarketRate rate, String pricingSource )
    {
        Organization providerOrg = ISUtilImpl.getInstance ().getOrg ( rate.getProviderShortName () );
        String provider = providerOrg.getShortName ();
        String cpName = rate.getBaseCcy () + '/' + rate.getVariableCcy ();
        if ( getSubscriptionCount ( cpName ) > 0 )
        {
            final Quote quote = prepareQuote ( rate, pricingSource );
            quoteHandler.sendRate ( quote, Collections.emptyMap () );
            String rateCacheKey = pricingSource + '_' + cpName;
            updateRateCache ( rateCacheKey, rate );
        }
        else
        {
            log.info ( "ExcelPricingRequestService::Ignoring update, no subscribers : Pricing source Name :" + pricingSource + " provider  :  " + provider + " cp : " + cpName );
        }
    }

    private void spotRateChanged ( String mdsName, String cp, double bidRate, double offerRate )
    {
        if ( checkIfActiveSubscriptions ( mdsName, cp ) )
        {
            MarketRate rate = createMarketRate ( mdsName, cp, bidRate, offerRate, false );
            onRateReceive ( rate, mdsName );
        }
    }

    private boolean checkIfActiveSubscriptions ( String mdsName, String cp )
    {
        long count = getSubscriptionCount ( cp );
        if ( log.isDebugEnabled () )
        {
            log.debug ( "ExcelPricingRequestService:: subscriptionCount=" + count + ",mds=" + mdsName );
        }
        return count > 0;
    }

    private void spotRateStale ( String mdsName, String cp )
    {
        if ( checkIfActiveSubscriptions ( mdsName, cp ) )
        {
            MarketRate rate = createMarketRate ( mdsName, cp, 0, 0, true );
            onRateReceive ( rate, mdsName );
        }
    }

    private MarketRate createMarketRate ( String mdsName, String cp, double bidRate, double offerRate, boolean stale )
    {
        CurrencyPair ccyPair = CurrencyFactory.getCurrencyPairFromString ( cp );
        Organization brokerOrg = ReferenceDataCacheC.getInstance ().getOrganization ( brokerName );
        FXRateBasis rateBasis = QuoteConventionUtilC.getInstance ().getFXRateConvention ( brokerOrg ).getFXRateBasis ( ccyPair );
        //double tierSize = mBean.getMaxTierSizeForExcelPricing(brokerName);

        MarketRateC rate = new MarketRateC ();
        String quoteId = ProviderQuoteIdFactory.newProviderQuoteId ( brokerName, System.currentTimeMillis () );
        rate.setQuoteId ( quoteId );
        //rate.setProviderQuoteId();
        rate.setBaseCcy ( ccyPair.getBaseCurrency ().getName () );
        rate.setVarCcy ( ccyPair.getVariableCurrency ().getName () );
        //rate.setLimitCcy(rate.getBaseCcy());
        rate.setProviderShortName ( brokerName );
        rate.setStreamId ( mdsName );
        rate.setBidRate ( bidRate, 0 );
        //rate.setBidSpotRate(bidRate, 0);
        //rate.setBidLimit((long) tierSize, 0);
        rate.setOfferRate ( offerRate, 0 );
        //rate.setOfferSpotRate(offerRate, 0);
        //rate.setOfferLimit((long) tierSize, 0);
        rate.setStale ( stale );
        //rate.setServerId();
        //rate.setTiming();
        //rate.setPriceType();
        //rate.getBidTier(0).setTimeEffective();
        //rate.getOfferTier(0).setTimeEffective();

        rate.setTenor ( Tenor.SPOT_TENOR );
        IdcDate currentDate = EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ();
        IdcDate valueDate = rateBasis.getValueDate ( currentDate, Tenor.SPOT_TENOR );
        rate.setValueDate ( valueDate );
        return rate;
    }

    private Quote prepareQuote ( MarketRate rate, String cacheKey )
    {
        CurrencyPair rateCcyPair = CurrencyFactory.newCurrencyPair ( rate.getBaseCcy (), rate.getVariableCcy () );
        Organization org = ISUtilImpl.getInstance ().getOrg ( rate.getProviderShortName () );

        FXRateConvention conv = QuoteConventionUtilC.getInstance ().getFXRateConvention ( org );
        FXRateBasis fxRateBasis = conv.getFXRateBasis ( rateCcyPair );

        String guid = getGUID ( rate.getProviderShortName () );
        ExcelPricingRateEventC excelPricingRateEventC = new ExcelPricingRateEventC ( rate, new Date (),
                rate.getValueDate (), Tenor.SPOT_TENOR, rateCcyPair,
                0.0, 0.0, "EXCEL_PRICING", org, guid, getNextSequenceId (), true );

        excelPricingRateEventC.setAdaptorOrganization ( ReferenceDataCacheC.getInstance ().getOrganization ( rate.getProviderShortName () ) );
        excelPricingRateEventC.setTopicName ( PricingConfigurationFactory.getPricingConfigurationMBean ().getExcelPricingJmsTopic () );
        excelPricingRateEventC.setCacheKey ( cacheKey );
        excelPricingRateEventC.setDealtCurrency ( CurrencyFactory.getCurrency ( rate.getBaseCcy () ) );
        populateTicks ( excelPricingRateEventC, rate, guid );
        excelPricingRateEventC.setFxRateBasis ( fxRateBasis );

        if ( rate.isStale () )
        {
            excelPricingRateEventC.setActive ( false );
        }
        return excelPricingRateEventC;
    }

    private int getNextSequenceId ( )
    {
        return sequenceId.incrementAndGet ();
    }

    private void populateTicks ( ExcelPricingRateEventC excelPricingRateEventC, MarketRate rate, String guid )
    {
        double tierSize = mBean.getMaxTierSizeForExcelPricing ( brokerName );
        FXLegDealingPrice tick = excelPricingRateEventC.addOfferDealingPrice ( 0, "FinalRate", guid,
                rate.getProviderShortName (), rate.getOfferRate (),
                rate.getOfferSpotRate (), rate.getOfferForwardPoints (), tierSize, rate.getOfferRate (), true );
        tick.setQuote ( excelPricingRateEventC );
        tick = excelPricingRateEventC.addBidDealingPrice ( 0, "FinalRate", getGUID ( rate.getProviderShortName () ),
                rate.getProviderShortName (), rate.getBidRate (),
                rate.getBidSpotRate (), rate.getBidForwardPoints (), tierSize, rate.getBidRate (), true );
        tick.setQuote ( excelPricingRateEventC );
    }

    private String getGUID ( String provideShortName )
    {
        StringBuilder sb = new StringBuilder ( provideShortName );
        sb.append ( "-" ).append ( provideShortName ).append ( "-" ).append ( System.currentTimeMillis () );
        return sb.toString ();
    }

    public void updateRateCache ( String key, MarketRate rate )
    {
        rateCache.put ( key, rate );
    }

    public MarketRate getCachedRate ( String key )
    {
        return rateCache.get ( key );
    }

    private void incrementSubscriptionCount ( String cp )
    {
        AtomicLong count = subscriptionCountMap.get ( cp );
        if ( count == null )
        {
            subscriptionCountMap.putIfAbsent ( cp, new AtomicLong () );
            count = subscriptionCountMap.get ( cp );
        }
        count.incrementAndGet ();
        log.info ( "ExcelPricingRequestService.incrementSubscriptionCount::count=" + count.get () + ", br=" + brokerName + ", cp=" + cp + ", mds=" + mdsName );
    }

    private void decrementSubscriptionCount ( String cp )
    {
        AtomicLong count = subscriptionCountMap.get ( cp );
        if ( count != null )
        {
            long value = count.decrementAndGet ();
            if ( value < 0 )
            {
                log.error ( "Subscription count become -ive for org=" + brokerName + ", cp=" + cp + ", count=" + value );
                count.addAndGet ( -1 * value );
            }
        }
        else
        {
            log.error ( "Un-subscription called for non existing subscription for org=" + brokerName + ", cp=" + cp );
        }
        long i = count == null ? 0 : count.get ();
        log.info ( "ExcelPricingRequestService.decrementSubscriptionCount::count=" + i + ", br=" + brokerName + ", cp=" + cp + ", mds=" + mdsName );
    }

    private long getSubscriptionCount ( String cp )
    {
        AtomicLong count = subscriptionCountMap.get ( cp );
        return count == null ? 0 : count.get ();
    }

    public void onUpdateEvent ( FXMDSUpdateEvent fxmdsUpdateEvent )
    {
        try
        {
            MDSUpdateEvent.Type event = fxmdsUpdateEvent.getType ();
            String mdsName = fxmdsUpdateEvent.getMDSName ();
            String ccyPair = fxmdsUpdateEvent.getInstrument ();
            if ( log.isDebugEnabled () )
            {
                log.debug ( "ExcelPricingRequestService.onUpdateEvent:: mds=" + mdsName + ", cp=" + ccyPair + ", event=" + event.name () );
            }
            if ( isStale ( event ) )
            {
                spotRateStale ( mdsName, ccyPair );
            }
            else if ( fxmdsUpdateEvent.isSpot () )
            {
                if ( MDSUpdateEvent.Type.DELETE.equals ( event ) )
                {
                    spotRateStale ( mdsName, ccyPair );
                }
                else
                {
                    if ( fxmdsUpdateEvent.getSrc () == FXMDSUpdateEvent.Source.EXCEL.getId () )
                    {
                        spotRateChanged ( mdsName, ccyPair, fxmdsUpdateEvent.getBid (), fxmdsUpdateEvent.getOffer () );
                    }
                    else
                    {
                        if ( log.isDebugEnabled () )
                        {
                            log.debug ( "ExcelPricingRequestService.onUpdateEvent:: Skipping non-Excel source event " + fxmdsUpdateEvent );
                        }
                    }
                }
            }
            else
            {
                if ( log.isDebugEnabled () )
                {
                    log.debug ( "ExcelPricingRequestService.onUpdateEvent:: skipping as MDE is forward point" );
                }
            }
        }
        catch ( Exception e )
        {
            log.error ( "ExcelPricingRequestService.onUpdateEvent:: Exception during execution", e );
        }
    }

    private boolean isStale ( MDSUpdateEvent.Type type )
    {
        switch ( type )
        {
            case TIMEOUT:
            case LOGOUT:
            case STALE:
                return true;
            default:
                return false;
        }
    }

    /**
     * For JSP utility
     *
     * @return map
     */
    public Map<String, AtomicLong> getSubscriptionCountMap ( )
    {
        return Collections.unmodifiableMap ( subscriptionCountMap );
    }
}
