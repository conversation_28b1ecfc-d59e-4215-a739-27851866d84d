package com.integral.pricing.subscription;


import com.integral.finance.currency.CurrencyPair;
import com.integral.is.common.cache.bucket.MessageNotifier;
import com.integral.user.Organization;

public interface PricingSubscriptionRequest
{
    public String getPricingSource();

    /**
     * short name of the LP
     *
     * @return
     */
    public Organization getOrganization();

    public CurrencyPair getCurrencyPair();

    public MessageNotifier getQuoteHandler();
}
