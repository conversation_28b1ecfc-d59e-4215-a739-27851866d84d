package com.integral.pricing.config;


import com.integral.system.configuration.IdcMBean;
import com.integral.user.Organization;

public interface PricingConfigurationMBean extends IdcMBean
{
    public final String PRC_EXCEL_PRICING_JMS_TOPIC = "IDC.PRC.EXCEL.PRICING.TOPIC";

    // need to drive this from the config
    public final String PRC_MESSAGE_FLOWTODISK_ENABLED = "PRC.MESSAGE.FLOWTODISK.ENABLED";

    public final static String PRC_THREAD_POOL_QUEUE_SIZE = "IDC.PRC.THREAD.POOL.QUEUE.SIZE";
    public final static String PRC_THREAD_POOL_MIN_SIZE = "IDC.PRC.THREADPOOL.MIN.SIZE";
    public final static String PRC_THREAD_POOL_MAX_SIZE = "IDC.PRC.THREADPOOL.MAX.SIZE";
    public final static String PRC_THREAD_POOL_KEEP_ALIVE = "IDC.PRC.THREADPOOL.KEEP.ALIVE";
    public final static String PRC_MESSAGE_DELIVERY_MODE = "IDC.PRC.MESSGE.DELIVERY.MODE";
    public final static String PRC_MRKT_SERIALIZER_VERSION = "IDC.PRC.MRKT.SERIALIZER.VERSION";
    
    public final static String PRC_EXCEL_RATE_LOG_CATAGORY = "com.integral.pricing.excel.logger";
    public final static String PRC_EXCEL_RATE_LOG_COMPRESSION = "IDC.BrokerAdaptor.Excel.Log.Compression.Enabled";
    public final static boolean IDC_EXCEL_LOG_COMPRESSION_DEFAULT_ENABLE = true;
    // Excel pricing timeout related properties.
    public String IDC_EXCEL_SESSION_TIMEOUT="Idc.Excel.Session.Timeout";
    // in seconds
    public int IDC_EXCEL_SESSION_DEFAULT_TIMEOUT = 60;

    String PP_FEES_ENABLED = "Idc.PriceProvision.Fees.Enabled";
    String BA_PRICE_FROM_MDS_ENABLED = "Idc.BrokerAdaptor.PriceFromMDS.Enabled";

    public String getExcelPricingJmsTopic();

    int getPrcMinThreadPoolSize();

    int getPrcMaxThreadPoolSize();

    long getKeepAliveTime();

    int getThreadPoolQueueSize();

    int getPrcMessageDeliveryMode();

    int getPrcMrktSerializerVer();

    boolean isPrcFlowToDiskEnabled();

    public int getExcelSessionTimeout();

    public int getExcelSessionTimeout( Organization organization ) ;
    
    public String getExcelRateLogCatagory();
    
    public boolean isExcelRateLogCompressionEnabled();

    boolean isPriceProvisionFeesEnabled(String orgName);
    boolean isPriceFromMDSEnabled(String orgName);
}
