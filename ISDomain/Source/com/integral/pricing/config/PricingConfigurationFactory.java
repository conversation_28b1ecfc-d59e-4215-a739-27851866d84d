package com.integral.pricing.config;


public class PricingConfigurationFactory
{

    protected static PricingConfigurationFactory current;
    protected PricingConfigurationMBean pricingConfigurationMBean;

    static
    {
        current = new PricingConfigurationFactory();
    }

    protected PricingConfigurationFactory()
    {
        pricingConfigurationMBean = null;
    }

    protected PricingConfigurationMBean _newPricingConfigurationMBean()
    {
        final PricingConfigurationC result = new PricingConfigurationC();
        result.loadProperties();
        return result;
    }

    protected static PricingConfigurationMBean newPricingConfigurationMBean()
    {
        return current._newPricingConfigurationMBean();
    }

    protected PricingConfigurationMBean _getPricingConfigurationMBean()
    {
        if ( pricingConfigurationMBean == null )
        {
            pricingConfigurationMBean = PricingConfigurationFactory.newPricingConfigurationMBean();
        }
        return pricingConfigurationMBean;
    }

    public static PricingConfigurationMBean getPricingConfigurationMBean()
    {
        return current._getPricingConfigurationMBean();
    }
}
