package com.integral.pricing.config;

import com.integral.system.configuration.IdcMBeanC;
import com.integral.user.Organization;

import javax.jms.DeliveryMode;
import java.util.Map;


public class PricingConfigurationC extends IdcMBeanC implements PricingConfigurationMBean
{
    int minThreadpoolSize;
    int maxThreadpoolSize;
    int keepAliveTime;
    int poolQueueSize;
    String jmsTopic;
    boolean flowToDiskEnabled;
    int prcMessageDeliveryMode;
    int prcMrktSerializerVersion;
    private int excelSessionTimeout;
    private String excelRateLogCatagory;
    private boolean isExcelRateLogCompressionEnabled;
    private boolean priceProvisionFeesEnabled;
    private Map<String, Boolean> priceProvisionFeesEnabledMap;
    private boolean priceFromMDSEnabled;
    private Map<String, Boolean> priceFromMDSEnabledMap;

    protected PricingConfigurationC()
    {
        super( "com.integral.pricing.config.PricingConfigurationMBean" );
    }

    public void initialize()
    {
        super.initialize();
        loadProperties();
    }

    void loadProperties()
    {

        minThreadpoolSize = getIntProperty( PRC_THREAD_POOL_MIN_SIZE, 10 );
        maxThreadpoolSize = getIntProperty( PRC_THREAD_POOL_MAX_SIZE, 200 );
        keepAliveTime = getIntProperty( PRC_THREAD_POOL_KEEP_ALIVE, 10 );
        poolQueueSize = getIntProperty( PRC_THREAD_POOL_QUEUE_SIZE, 100 );
        jmsTopic = getStringProperty( PRC_EXCEL_PRICING_JMS_TOPIC, "TOPIC.EXCEL.PRICING" );
        flowToDiskEnabled = getBooleanProperty( PRC_MESSAGE_FLOWTODISK_ENABLED, false );
        prcMessageDeliveryMode = getIntProperty( PRC_MESSAGE_DELIVERY_MODE, DeliveryMode.NON_PERSISTENT );
        prcMrktSerializerVersion = getIntProperty( PRC_MRKT_SERIALIZER_VERSION, 5 );
        excelSessionTimeout=getIntProperty(IDC_EXCEL_SESSION_TIMEOUT, IDC_EXCEL_SESSION_DEFAULT_TIMEOUT );
        excelRateLogCatagory = PRC_EXCEL_RATE_LOG_CATAGORY;
        isExcelRateLogCompressionEnabled = getBooleanProperty(PRC_EXCEL_RATE_LOG_COMPRESSION, IDC_EXCEL_LOG_COMPRESSION_DEFAULT_ENABLE);
        priceProvisionFeesEnabled = getBooleanProperty(PP_FEES_ENABLED, false);
        priceProvisionFeesEnabledMap = initSingleSuffixBooleanPropertyMap(PP_FEES_ENABLED + '.', null);
        priceFromMDSEnabled = getBooleanProperty(BA_PRICE_FROM_MDS_ENABLED, false);
        priceFromMDSEnabledMap = initSingleSuffixBooleanPropertyMap(BA_PRICE_FROM_MDS_ENABLED + '.', null);
    }

    @Override
    public String getExcelPricingJmsTopic()
    {
        return jmsTopic;
    }

    @Override
    public int getPrcMinThreadPoolSize()
    {
        return minThreadpoolSize;
    }

    @Override
    public int getPrcMaxThreadPoolSize()
    {
        return maxThreadpoolSize;
    }

    @Override
    public long getKeepAliveTime()
    {
        return keepAliveTime;
    }

    @Override
    public int getThreadPoolQueueSize()
    {
        return poolQueueSize;
    }

    @Override
    public int getPrcMessageDeliveryMode()
    {
        return prcMessageDeliveryMode;
    }

    @Override
    public int getPrcMrktSerializerVer()
    {
        return prcMrktSerializerVersion;
    }

    @Override
    public boolean isPrcFlowToDiskEnabled()
    {
        return flowToDiskEnabled;
    }

    @Override
    public int getExcelSessionTimeout()
    {
        return excelSessionTimeout;
    }

    @Override
    public int getExcelSessionTimeout( Organization organization )
    {
        if(organization == null){
            return getExcelSessionTimeout();
        }
        else{
            return getIntProperty( IDC_EXCEL_SESSION_TIMEOUT + "." + organization.getShortName(), getExcelSessionTimeout() );
        }
    }

	@Override
	public String getExcelRateLogCatagory() 
	{
		return excelRateLogCatagory;
	}

	@Override
	public boolean isExcelRateLogCompressionEnabled()
	{
		return isExcelRateLogCompressionEnabled;
	}

	@Override
	public boolean isPriceProvisionFeesEnabled(String orgName){
        if(orgName != null){
            Boolean value = priceProvisionFeesEnabledMap.get(orgName);
            if(value != null) {
                return value;
            }
        }
        return priceProvisionFeesEnabled;
    }

    @Override
    public boolean isPriceFromMDSEnabled(String orgName){
        if(orgName != null){
            Boolean value = priceFromMDSEnabledMap.get(orgName);
            if(value != null) {
                return value;
            }
        }
        return priceFromMDSEnabled;
    }
}
