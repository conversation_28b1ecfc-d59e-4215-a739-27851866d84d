package com.integral.pricing.util;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Thread pool for pricing related tasks
 */
public class PricingThreadPoolFactory
{
    private static PricingThreadPoolFactory factory = new PricingThreadPoolFactory();

    private  ExecutorService singleThreadedPublisher=Executors.newSingleThreadExecutor( new PricingServiceThreadFactory( "PricingServicePublisherThreadFactory" ) );
    private  ExecutorService singleThreadedReceiver=Executors.newSingleThreadExecutor( new PricingServiceThreadFactory( "PricingServiceReceiverThreadFactory" ) );
    public synchronized static PricingThreadPoolFactory getInstance()
    {
        return factory;
    }

    private PricingThreadPoolFactory()
    {
    }

    public ExecutorService getSingleThreadPublisher()
    {
        return singleThreadedPublisher;
    }

    public ExecutorService getSingleThreadReceiver()
    {
        return singleThreadedReceiver;
    }

    private class PricingServiceThreadFactory implements ThreadFactory
    {
        final AtomicInteger threadNumber = new AtomicInteger( 1 );
        ThreadGroup tg = null;
        String name ;
        public PricingServiceThreadFactory( String name )
        {
            tg = new ThreadGroup( name );
            this.name=name;
        }

        public Thread newThread( Runnable runnable )
        {
            return new Thread( tg, runnable, name+"_" + threadNumber.getAndIncrement() );
        }
    }
}
