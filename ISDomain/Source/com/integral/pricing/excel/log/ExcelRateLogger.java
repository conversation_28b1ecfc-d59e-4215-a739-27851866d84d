package com.integral.pricing.excel.log;

import java.sql.Timestamp;
import java.util.ArrayList;

import com.integral.admin.services.org.marketdataset.ExcelRate;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.log.LoggerUtilC;
import com.integral.pricing.config.PricingConfigurationFactory;
import com.integral.pricing.config.PricingConfigurationMBean;

/**
 * <AUTHOR>
 *
 */
public class ExcelRateLogger 
{
	private static final char comma 		= ',';
	private static final char whitespace 	= ' ';
	
	protected Log rateLog;
	static Log strdLog = LogFactory.getLog(ExcelRateLogger.class);
	
	private final PricingConfigurationMBean config = PricingConfigurationFactory.getPricingConfigurationMBean();
	
	private boolean initiated = false;
	private boolean gzip = true;
	
	private ExcelRateLogger()
	{
		rateLog = LogFactory.getLog(config.getExcelRateLogCatagory());
		gzip = config.isExcelRateLogCompressionEnabled();
	}
	
	public synchronized void init(String specialization)
	{
		if ( !initiated || gzip != config.isExcelRateLogCompressionEnabled() )
		{
			gzip = config.isExcelRateLogCompressionEnabled();
			rateLog = LoggerUtilC.getInstance().createSpecializedLog(rateLog, specialization, gzip);
			initiated = true;
		}
	}
	
	private static class Holder
	{
		public static final ExcelRateLogger INSTANCE = new ExcelRateLogger();
	}
	
	public static ExcelRateLogger getInstance()
	{
		return Holder.INSTANCE;
	}

	public void logInfo(String msg)
	{
		if( rateLog.isInfoEnabled() )
		{
			rateLog.info(msg);
		}
	}
	
	public void logDebug(String msg)
	{
		if( rateLog.isDebugEnabled() )
		{
			rateLog.debug(msg);
		}
	}
	
	/*
	 * Sample log line
	 * 2013-10-07 21:05:25,058  INFO http-0.0.0.0-8080-1 [MM1@MAIN.Broker1] usr=MM1 org=Broker1 mds=FXMDS cnt=28 rts=[
	 * {cp=EUR/USD,t=SPOT,b=1.47849999999977,o=1.47949999999977,bc=EUR,tc=USD,syn=F} {cp=EUR/USD,t=ON,b=8690.55,o=8690.6,bc=EUR,tc=USD,syn=F} 
	 * {cp=EUR/USD,t=TN,b=8690.55,o=8690.58,bc=EUR,tc=USD,syn=F} {cp=EUR/USD,t=SN,b=8690.65,o=8690.69,bc=EUR,tc=USD,syn=F} 
	 * {cp=EUR/USD,t=1W,b=1481.42,o=1481.47,bc=EUR,tc=USD,syn=F} {cp=EUR/USD,t=2W,b=1481.87,o=1481.95,bc=EUR,tc=USD,syn=F} 
	 * {cp=EUR/USD,t=1M,b=1548.0,o=1483.01,bc=EUR,tc=USD,syn=F} {cp=EUR/USD,t=2M,b=1485.5,o=1485.65,bc=EUR,tc=USD,syn=F} 
	 * {cp=EUR/USD,t=3M,b=1488.26,o=1488.53,bc=EUR,tc=USD,syn=F} {cp=EUR/USD,t=3W,b=1482.37,o=1482.47,bc=EUR,tc=USD,syn=F} 
	 * {cp=EUR/USD,t=4M,b=1490.75,o=1491.75,bc=EUR,tc=USD,syn=F} {cp=EUR/USD,t=5M,b=1494.17,o=1494.67,bc=EUR,tc=USD,syn=F} 
	 * {cp=EUR/USD,t=6M,b=1497.47,o=1498.13,bc=EUR,tc=USD,syn=F} {cp=EUR/USD,t=7M,b=1501.11,o=1501.96,bc=EUR,tc=USD,syn=F} 
	 * {cp=EUR/USD,t=8M,b=1505.28,o=1506.28,bc=EUR,tc=USD,syn=F} {cp=EUR/USD,t=9M,b=1508.8,o=1510.05,bc=EUR,tc=USD,syn=F} 
	 * {cp=EUR/USD,t=10M,b=1512.59,o=1513.99,bc=EUR,tc=USD,syn=F} {cp=EUR/USD,t=11M,b=1517.02,o=1518.62,bc=EUR,tc=USD,syn=F} 
	 * {cp=EUR/USD,t=12M,b=1521.18,o=1522.93,bc=EUR,tc=USD,syn=F} {cp=EUR/USD,t=18M,b=1539.75,o=1559.75,bc=EUR,tc=USD,syn=F} 
	 * {cp=EUR/USD,t=2Y,b=1581.7,o=1594.2,bc=EUR,tc=USD,syn=F} {cp=USD/JPY,t=SPOT,b=99.1067550000128,o=99.2167000000128,bc=USD,tc=JPY,syn=F} 
	 * {cp=USD/JPY,t=1M,b=13208.34,o=13209.56,bc=USD,tc=JPY,syn=F} {cp=EUR/AUD,t=SPOT,b=1.54173499999979,o=1.54142499999979,bc=EUR,tc=AUD,syn=F} 
	 * {cp=AUD/USD,t=SPOT,b=1.08985999999988,o=1.10051499999986,bc=AUD,tc=USD,syn=F} {cp=USD/AUD,t=SPOT,b=1.14230541379467,o=1.15342306451594,bc=USD,tc=AUD,syn=F} 
	 * {cp=USD/INR,t=SPOT,b=55.0322850000019,o=58.0322300000019,bc=USD,tc=INR,syn=F} {cp=USD/INR,t=1W,b=294.0,o=294.0,bc=USD,tc=INR,syn=F}
	 * ]
	 */
	
	public void logExcelRates(ArrayList<ExcelRate> xlrates, String user, String org, String mds)
	{
		String msg = null;
		try
		{
			StringBuilder sb = new StringBuilder(4096);
			sb.append("usr=").append(user).append(whitespace);
			sb.append("org=").append(org).append(whitespace);
			sb.append("mds=").append(mds).append(whitespace);
			sb.append("cnt=").append(xlrates.size()).append(whitespace);
			sb.append("rts=[\n");
			String ts = new Timestamp ( System.currentTimeMillis () ).toString ();
			for( ExcelRate eR : xlrates )
			{
				sb.append(getExcelRateMessage(eR, mds, ts)).append("\n");
			}
			sb.setLength(sb.length()-1);
			sb.append("]");
			msg = sb.toString();
		}
		catch ( Exception e )
		{
			strdLog.error("ERL.logExcelRates : Error - ", e);
		}
		finally
		{
			logInfo(msg);
		}
	}
	
	private String getExcelRateMessage(ExcelRate xlRate, String mdsName, String timestamp )
	{
		StringBuilder sb = new StringBuilder(64);
		sb.append("{");
		if( xlRate == null )
		{
			sb.append("null}");
			return sb.toString();
		}
        sb.append("mds=").append(mdsName).append(comma);
		sb.append ( "ts=" ).append ( timestamp ).append ( comma );
		sb.append("cp=").append(xlRate.getCP()).append(comma);
		sb.append("t=").append(xlRate.getTenor()).append(comma);
		sb.append("b=").append(String.valueOf(xlRate.getB())).append(comma);
		sb.append("o=").append(String.valueOf(xlRate.getO())).append(comma);
		sb.append("m=").append(xlRate.getM()!=null ? String.valueOf(xlRate.getM()): "").append(comma);
		sb.append("bc=").append(xlRate.getBase()).append(comma);
		sb.append("tc=").append(xlRate.getTerm()).append(comma);
		sb.append("syn=").append(xlRate.isSynced()?"T":"F");
		sb.append("}");
		return sb.toString();
	}

}
