package com.integral.pricing.core;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.atomic.AtomicLong;

import com.integral.admin.services.org.marketdataset.MarketDataSetCache;

/**
 * Daemon process for checking last update rate from excel and
 * send an inactive rate if the last update time does not match
 *
 */
public class ExcelPricingStaleRateChecker extends TimerTask
{
    private final long delayInterval = 60 * 5 * 1000L;
    private final Timer timer;

    public ExcelPricingStaleRateChecker(Timer timer) {
        this.timer = timer;

    }
    @Override
    public void run()
    {
        // retrieve the timestamps from the timestamp cache
        // and if the latency is more than the heart beat interval then
        // send stale rates for that Org.
          // needed in 4.11
        /*Map<String,AtomicLong> timerMap = MarketDataSetCache.getInstance().getRatesTimeMap();
        Set<String> keys= timerMap.keySet();
        List<String> orgs = new ArrayList<String>();
        for (String key:keys)
        {
            if ((new Date().getTime() - timerMap.get(key).get())>delayInterval)
            {
                // call the excel rate publisher to publish stale rates.
                PricingRatePublisherFactory.getExcelPricingRatePublisher().sendInactiveRates( key );
                timerMap.remove(key);
            }
        }*/
    }
}
