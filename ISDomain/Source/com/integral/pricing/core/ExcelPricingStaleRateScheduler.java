package com.integral.pricing.core;

import com.integral.admin.services.org.marketdataset.ExcelPricingDBSyncTask;

import java.util.Timer;

/**
 * Created with IntelliJ IDEA.
 * User: kasi
 * Date: 6/5/13
 * Time: 11:09 AM
 * To change this template use File | Settings | File Templates.
 */
public class ExcelPricingStaleRateScheduler
{
    public static final int period_in_second = 5;
    private static ExcelPricingStaleRateScheduler excelPricingScheduler = new ExcelPricingStaleRateScheduler();

    private ExcelPricingStaleRateChecker staleTask = null;
    private boolean isStarted = false;
    public static ExcelPricingStaleRateScheduler instance()
    {
        return excelPricingScheduler;
    }
    private ExcelPricingStaleRateScheduler()
    {

    }
    public void start()
    {
        if(!isStarted)
        {
            Timer timer = new Timer(true);
            staleTask = new ExcelPricingStaleRateChecker(timer);
            int period = 1000 * period_in_second;
            timer.schedule(staleTask, 0, period);
            isStarted = true;
        }

    }

    public void stop()
    {
        if(isStarted)
        {
            //TODO
        }
    }

}
