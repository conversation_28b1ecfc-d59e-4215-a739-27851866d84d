package com.integral.pricing.core;

import com.integral.is.message.MarketRate;
import com.integral.message.StatusMessage;

public interface PricingRequestService
{

    StatusMessage subscribe( com.integral.pricing.subscription.PricingSubscriptionRequest subscriptionRequest );

    StatusMessage unSubscribe( com.integral.pricing.subscription.PricingSubscriptionRequest subscriptionRequest );

    public void onRateReceive( MarketRate rate, String pricingSource );
}
