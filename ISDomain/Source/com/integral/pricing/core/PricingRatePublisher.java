package com.integral.pricing.core;

import com.integral.is.message.MarketRate;
import com.integral.message.StatusMessage;
import com.integral.user.Organization;


public interface PricingRatePublisher
{
    public StatusMessage publishRate( MarketRate marketRate, String pricingSource, String namespace );

    /**
     * when we detect that there are no rates from pricing source, (heart beat adaptor) then we
     * need to send stale quote to observers.
     * @param providerShortName
     */
    public void sendInactiveRates(String providerShortName);

    void sendInactiveRates( String providerShortName, String pricingSource, String namespace, String cp );
}
