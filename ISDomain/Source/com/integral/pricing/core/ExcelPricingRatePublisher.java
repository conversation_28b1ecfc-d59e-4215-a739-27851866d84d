package com.integral.pricing.core;

import com.integral.is.message.MarketRate;
import com.integral.is.message.MarketRateSerializerFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.StatusMessage;
import com.integral.messaging.MessageHolder;
import com.integral.messaging.MessageSender;
import com.integral.messaging.MessageSenderFactory;
import com.integral.pricing.config.PricingConfigurationFactory;
import com.integral.pricing.config.PricingConfigurationMBean;
import com.integral.pricing.util.PricingThreadPoolFactory;
import com.integral.system.runtime.ServerRuntimeMBean;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicBoolean;

public class ExcelPricingRatePublisher implements PricingRatePublisher
{
    private static final Log log = LogFactory.getLog ( ExcelPricingRatePublisher.class );
    private static final ConcurrentMap<String, ConcurrentMap<String, MarketRate>> orgRatesMap = new ConcurrentHashMap<String, ConcurrentMap<String, MarketRate>> ();
    private static final ConcurrentMap<String, AtomicBoolean> staleRatesProcess = new ConcurrentHashMap<String, AtomicBoolean> ();
    private static final PricingConfigurationMBean pricingConfigMBean = PricingConfigurationFactory.getPricingConfigurationMBean ();
    private static MessageSender messageSender;

    static
    {
        try
        {
            messageSender = MessageSenderFactory.newMessageSender ( ServerRuntimeMBean.EXCEL_PRICING_EXCHANGE );
        }
        catch ( Exception e )
        {
            log.error ( "EPRP.init - Exception while initializing the message sender for excel pricing.", e );
        }
    }

    @Override
    public StatusMessage publishRate ( final MarketRate marketRate, final String pricingSource, final String namespace )
    {
        try
        {
            // get it from config
            final int mrktSerializerVersion = pricingConfigMBean.getPrcMrktSerializerVer ();
            final Map<String, String> properties = new HashMap<String, String> ( 3 );
            properties.put ( PricingRequestConstants.PRICING_SOURCE, pricingSource );
            properties.put ( PricingRequestConstants.NAME_SPACE, namespace );
            properties.put ( PricingRequestConstants.CP, marketRate.getBaseCcy () + "/" + marketRate.getVariableCcy () );
            final String messageStr = MarketRateSerializerFactory.instance ().getSerializerForVersion ( mrktSerializerVersion ).serialize ( marketRate );
            PricingThreadPoolFactory.getInstance ().getSingleThreadPublisher ().execute ( new Runnable ()
            {
                @Override
                public void run ( )
                {
                try
                {
                    MessageHolder rateMessageHolder = new MessageHolder ();
                    rateMessageHolder.setMessage ( messageStr );
                    rateMessageHolder.setMessageProperties ( properties );

                    messageSender.sendMessage ( pricingSource + "@" + namespace, rateMessageHolder );
                }
                catch ( Exception e )
                {
                    log.error ( "EPRP.publishRate - Exception while publishing excel rate in RMQ. pricingSource=" + pricingSource + ",ns=" + namespace, e );
                }

                if ( (staleRatesProcess.get ( marketRate.getProviderShortName () ) == null) ||
                        (staleRatesProcess.get ( marketRate.getProviderShortName () ) != null &&
                                !staleRatesProcess.get ( marketRate.getProviderShortName () ).get ()) )
                {
                    addRateToCache ( pricingSource, namespace, marketRate.getBaseCcy () + "/" + marketRate.getVariableCcy (), marketRate );
                }
                }
            } );

            if ( log.isDebugEnabled () )
            {
                log.debug ( "EPRP.publishRate - Message for rate " + marketRate.getQuoteId () );
            }
        }
        catch ( Exception ex )
        {
            log.error ( "EPRP.publishRate - Exception while sending excel price message for rateId=" + marketRate.getQuoteId () );
            return new StatusMessage.FailureMessage ( ex.getMessage () );
        }

        return new StatusMessage.SuccessMessage ();
    }

    @Override
    public void sendInactiveRates ( String providerShortName )
    {
        // get the Map associated with the org.
        log.info ( "EPRP.sendInactiveRates - provider name :" + providerShortName );
        if ( staleRatesProcess.get ( providerShortName ) == null )
        {
            staleRatesProcess.putIfAbsent ( providerShortName, new AtomicBoolean ( true ) );
        }
        else
        {
            staleRatesProcess.get ( providerShortName ).set ( true );
        }
        ConcurrentMap<String, MarketRate> ratesMap = orgRatesMap.get ( providerShortName );
        if ( ratesMap == null )
        {
            return;
        }

        // get the keys associated with the ratesMap
        Set<String> keys = ratesMap.keySet ();

        for ( String key : keys )
        {
            // get the value, also parse the params
            MarketRate marketRate = ratesMap.get ( key );
            marketRate.setStale ( true );
            String[] params = key.split ( "_" );
            publishRate ( marketRate, params[0], params[1] );
        }
        staleRatesProcess.get ( providerShortName ).set ( false );
    }

    @Override
    public void sendInactiveRates ( String providerShortName, String pricingSource, String namespace, String cp )
    {
        // get the Map associated with the org.
        if ( staleRatesProcess.get ( providerShortName ) == null )
        {
            staleRatesProcess.putIfAbsent ( providerShortName, new AtomicBoolean ( true ) );
        }
        else
        {
            staleRatesProcess.get ( providerShortName ).set ( true );
        }
        ConcurrentMap<String, MarketRate> ratesMap = orgRatesMap.get ( providerShortName );
        if ( ratesMap == null )
        {
            return;
        }
        MarketRate marketRate = ratesMap.get ( getKey ( pricingSource, namespace, cp ) );
        publishRate ( marketRate, pricingSource, namespace );
        staleRatesProcess.get ( providerShortName ).set ( false );

    }

    private void addRateToCache ( String pricingSource, String namespace, String cp, MarketRate marketRate )
    {
        // check if the org has a map already

        if ( orgRatesMap.get ( marketRate.getProviderShortName () ) == null )
        {
            ConcurrentHashMap<String, MarketRate> ratesMap = new ConcurrentHashMap<String, MarketRate> ();
            ratesMap.put ( getKey ( pricingSource, namespace, cp ), marketRate );
            orgRatesMap.put ( marketRate.getProviderShortName (), ratesMap );
        }
        else
        {
            orgRatesMap.get ( marketRate.getProviderShortName () ).put ( getKey ( pricingSource, namespace, cp ), marketRate );
        }
    }

    private String getKey ( String pricingSource, String namespace, String cp )
    {
        return pricingSource + '_' + namespace + '_' + cp;
    }
}
