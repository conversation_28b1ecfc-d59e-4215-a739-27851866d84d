package com.integral.pricing.core;


public class PricingRatePublisherFactory
{
    private static final PricingRatePublisherFactory ourInstance = new PricingRatePublisherFactory();
    private static final ExcelPricingRatePublisher excelPricingRatePublisher = new ExcelPricingRatePublisher();
    public static PricingRatePublisherFactory getInstance()
    {
        return ourInstance;
    }

    private PricingRatePublisherFactory()
    {
    }

    public static PricingRatePublisher getExcelPricingRatePublisher()
    {
        return excelPricingRatePublisher;
    }
}
