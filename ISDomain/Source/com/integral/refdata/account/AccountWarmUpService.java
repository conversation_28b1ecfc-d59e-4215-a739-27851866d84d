package com.integral.refdata.account;

import com.integral.adaptor.order.configuration.OrderConfigurationFactory;
import com.integral.exception.IdcException;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.functor.OrganizationModificationRemoteTransactionFunctor;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.organization.Account;
import com.integral.organization.AccountGroup;
import com.integral.rds.client.QueryIterator;
import com.integral.rds.exception.ReferenceDataServiceException;
import com.integral.rds.service.AccountsService;
import com.integral.rds.service.AccountsServiceC;
import com.integral.user.Organization;
import com.integral.util.Tuple;
import com.integral.util.collections.ConcurrentHashSet;

import java.util.*;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 11/6/15
 * Time: 3:25 PM
 * To change this template use File | Settings | File Templates.
 */
public class AccountWarmUpService implements MessageHandler {

    private static Log log = LogFactory.getLog(AccountWarmUpService.class);

    private static final String CUSTOMER_ORG = "corg";

    public AccountWarmUpService() {
        OrganizationModificationRemoteTransactionFunctor.addObserver(this);
    }

    public Message handle(Message message) throws IdcException {

        if (!ISFactory.getInstance().getISMBean().isAccountWarmUpEnabled()) {
            return message;
        }

        Map props = message.getMap();
        String notificationType = (String) props.get(OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_KEY_NOTIFICATIONTYPE);

        if (!OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_VALUE_VIRTUALSERVER_UPDATE.equals(notificationType)) {
            return message;
        }

        try {
            String orgName = (String) props.get(OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_KEY_SHORTNAME);
            String newVirtualServer = (String) props.get(OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_KEY_NEW_VIRTUAL_SERVER);

            ISMBean isMBean = ISFactory.getInstance().getISMBean();
            String vsShortName = isMBean.getVirtualServer().getShortName();
            if (null != newVirtualServer && vsShortName.equalsIgnoreCase(newVirtualServer) && null != orgName) {
                log.info("ACWS.handle():Received notification for virtual server change for organization:" + orgName + ",message:" + message);
                Set<Tuple<String, String>> customerOrgs = new HashSet<Tuple<String, String>>();
                Tuple<String, String> tuple = new Tuple<String, String>();
                tuple.first = orgName;
                tuple.second = orgName;
                customerOrgs.add(tuple);
                initWarmUp(customerOrgs);
                log.info("ACWS.handle():Updated Account cache warm up for organization:" + orgName + ",message:" + message);
            }
        } catch (Exception e) {
            log.error("ACWS.handle():Exception occurred during the Account cache warm up on virtual server update notification:" + message, e);
        }

        return message;
    }

    public void initialize() {
        if (ISFactory.getInstance().getISMBean().isAccountWarmUpEnabled()) {
            try {
                Set<Tuple<String, String>> supportedCustomerOrgs = getSupportedCustomerOrgs();
                if (!supportedCustomerOrgs.isEmpty()) {
                    initWarmUp(supportedCustomerOrgs);
                }
                log.info("ACWS:Successfully started account reference data cache warm up.");
            } catch (Exception e) {
                log.error("ACWS:Account cache warm up failed.", e);
            }
        } else {
            log.info("ACWS:Account cache warm up is disabled");
        }
    }

    public void initWarmUp(Set<Tuple<String, String>> customerOrgs) {
        Thread task = new Thread(new AccountCacheWarmUpTask(customerOrgs));
        task.setName("AccountWarmUpCacheLoadingTask");
        task.start();
    }

    private Set<Tuple<String, String>> getSupportedCustomerOrgs() {
        Set<Tuple<String, String>> supportedNamespacesWithCustomerOrgs = new ConcurrentHashSet<Tuple<String, String>>();
        Tuple<String, String> customerOrgTuple;
        Collection<Organization> extendedOrderProviderOrgsList = OrderConfigurationFactory.getOrderConfigurationMBean().getExtendedOrderProviderOrgsList();

        log.info("ACWS.getSupportedOrganizations::" + extendedOrderProviderOrgsList);

        if (null != extendedOrderProviderOrgsList) {
            for (Organization org : extendedOrderProviderOrgsList) {
                customerOrgTuple = new Tuple<String, String>();
                //Namespace
                customerOrgTuple.first = org.getShortName();
                //Customer Org
                customerOrgTuple.second = org.getShortName();
                supportedNamespacesWithCustomerOrgs.add(customerOrgTuple);
            }
        }
        return supportedNamespacesWithCustomerOrgs;
    }

    public class AccountCacheWarmUpTask implements Runnable {
        Set<Tuple<String, String>> supportedCustomerOrgs;

        public AccountCacheWarmUpTask(Set<Tuple<String, String>> supportedCustomerOrgs) {
            this.supportedCustomerOrgs = supportedCustomerOrgs;
        }

        @Override
        public void run() {
            for (Tuple<String, String> supportedCustomerOrg : supportedCustomerOrgs) {
                if (supportedCustomerOrg != null) {
                    loadAccounts(supportedCustomerOrg.first, supportedCustomerOrg.second);
                    loadAccountGroups(supportedCustomerOrg.first);
                }
            }
            log.info("ACWT.run:Completed loading of accounts cache for the supported organizations:");
        }

    }
    private void loadAccountGroups(String accountsNamespace) {
        log.info("ACWT.loadAccountGroups:Loading accounts group cache for the namespace:" + accountsNamespace);
        long startTime = System.currentTimeMillis();
        int count = 0;
        try {
            Map<String, String> queryParameters = getQueryParameters(accountsNamespace);
            QueryIterator<AccountGroup> accountGroupIterator = AccountsServiceC.getInstance().getAccountGroupIteratorByParams(accountsNamespace, queryParameters);
            while (accountGroupIterator.hasNext()) {
                accountGroupIterator.next();
                count++;
            }
        } catch (ReferenceDataServiceException e) {
            log.error("ACWT.loadAccountGroups:Exception occurred while trying to fetch account groups for the namespace:" + accountsNamespace, e);
        }
        log.info("ACWT.loadAccountGroups:Completed loading of account groups cache for the namespace:" + accountsNamespace +
                ",Time taken:" + (System.currentTimeMillis() - startTime) + "(ms)" + ", no of account groups added:" + count);

    }

    private void loadAccounts(String accountsNamespace, String customerOrg) {
        log.info("ACWT.loadAccounts:Loading accounts cache for the namespace:" + accountsNamespace);
        long startTime = System.currentTimeMillis();
        int count = 0;
        try {
            Map<String, String> queryParameters = getQueryParameters(accountsNamespace);
            queryParameters.put(CUSTOMER_ORG, customerOrg);
            QueryIterator<Account> accountsIterator = AccountsServiceC.getInstance().getAccountIteratorByParams(accountsNamespace, queryParameters);
            while (accountsIterator.hasNext()) {
                accountsIterator.next();
                count++;
            }
        } catch (ReferenceDataServiceException e) {
            log.error("ACWT.loadAccounts:Exception occurred while trying to fetch accounts for the namespace:" + accountsNamespace, e);
        }
        log.info("ACWT.loadAccounts:Completed loading of accounts cache for the namespace:" + accountsNamespace + ",customer org:" + customerOrg +
                ",Time taken:" + (System.currentTimeMillis() - startTime) + "(ms)" + ", no of accounts added:" + count);
    }

    private Map<String, String> getQueryParameters(String accountsNamespace) {
        Map<String, String> queryParams = new HashMap<String, String>();
        queryParams.put(AccountsService.NAMESPACE_FIELD, accountsNamespace);
        return queryParams;
    }
}