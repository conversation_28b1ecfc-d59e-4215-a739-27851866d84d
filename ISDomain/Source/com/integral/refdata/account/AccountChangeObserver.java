package com.integral.refdata.account;

import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;

/**
 * <AUTHOR>
 *
 */
public class AccountChangeObserver implements NotificationObserver
{
	AccountRDSUpdateHandler accountHandler;
	
	public AccountChangeObserver(AccountRDSUpdateHandler handler)
	{
		this.accountHandler = handler;
	}
	
	@Override
	public void notifyAdd(Notification notification)
	{
		accountHandler.handleAccountUpdate(notification.getEntityNameSpace(), notification.getEntityId());
	}

	@Override
	public void notifyUpdate(Notification notification)
	{
		accountHandler.handleAccountUpdate(notification.getEntityNameSpace(), notification.getEntityId());
	}

}
