package com.integral.refdata.account;

import com.integral.is.common.mbean.PropertyChangeHandler;
import com.integral.system.configuration.IdcMBeanC;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@SuppressWarnings("serial")
public class RefDataConfig extends IdcMBeanC {

    protected static RefDataConfig instance;

    private int accountIdMaxLength;

    protected static final String AccountIdMaxLengthProperty = "IDC.RD.Account.Id.Max.Length";


    static {
        init();
    }

    protected static void init() {
        instance = new RefDataConfig();
    }

    private RefDataConfig() {
        super("com.integral.refdata.RefDataConfig");
    }

    @Override
    public void initialize() {
        this.accountIdMaxLength = getIntProperty(AccountIdMaxLengthProperty, 100);
    }

    public static RefDataConfig getInstance() {
        return instance;
    }

    public int getAccountIdMaxLength() {
        return accountIdMaxLength;
    }
}
