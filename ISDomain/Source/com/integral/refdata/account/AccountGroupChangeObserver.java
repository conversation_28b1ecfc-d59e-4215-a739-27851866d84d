package com.integral.refdata.account;

import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 6/2/15
 * Time: 2:22 PM
 * To change this template use File | Settings | File Templates.
 */
public class AccountGroupChangeObserver implements NotificationObserver {

    private AccountRDSUpdateHandler accountHandler;

    public AccountGroupChangeObserver(AccountRDSUpdateHandler handler) {
        this.accountHandler = handler;
    }

    @Override
    public void notifyAdd(Notification notification) {
        accountHandler.handleAccountGroupUpdate(notification.getEntityNameSpace(), notification.getEntityId());
    }

    @Override
    public void notifyUpdate(Notification notification) {
        accountHandler.handleAccountGroupUpdate(notification.getEntityNameSpace(), notification.getEntityId());
    }
}
