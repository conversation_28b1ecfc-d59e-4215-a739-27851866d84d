package com.integral.cluster;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.services.ServiceContainerMBean;
import com.integral.services.cluster.ClusterManager;
import com.integral.services.cluster.ClusterManagerFactory;
import com.integral.services.cluster.notification.MasterSlaveTransitionHandler;
import com.integral.services.cluster.notification.OnlineOffLineTransitionHandler;
import com.integral.system.configuration.ConfigurationFactory;

/**
 * Created by raghunathans on 1/25/17.
 */
public class ClusterManagerHelper {

    private static ClusterManagerHelper helper = new ClusterManagerHelper();
    private ServiceContainerMBean instance = ServiceContainerMBean.getInstance();
    private ClusterManagerFactory factory = ClusterManagerFactory.getInstance();
    private ClusterConfigDataService configService = ClusterConfigDataService.getInstance();
    private ClusterManager clusterManager;

    private static Log log = LogFactory.getLog(ClusterManagerHelper.class.getName());

    private ClusterManagerHelper() {
        instance.setInstanceId(ConfigurationFactory.getServerMBean().getVirtualServerName());
        clusterManager = factory.createClusterManager(instance, configService.getAllClusterMetaData());
    }

    public static ClusterManagerHelper getInstance() {
        return helper;
    }


    public boolean startInstance(String clusterName,
                                 OnlineOffLineTransitionHandler handler) {
        return clusterManager.startInstance(clusterName, handler);
    }

    public boolean startInstance(String clusterName,
                                 MasterSlaveTransitionHandler handler) {
        return clusterManager.startInstance(clusterName, handler);
    }

    public boolean stopInstance(String clusterName) {
        return clusterManager.stopInstance(clusterName);
    }

    public void shutdown() {
        try {
            clusterManager.shutdown();
        } catch (Exception e) {
            log.info("Failed to shutdown cluster manager", e);
        }
    }
}
