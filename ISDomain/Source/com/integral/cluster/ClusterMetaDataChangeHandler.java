package com.integral.cluster;

import com.integral.is.ISCommonConstants;
import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.cluster.ClusterMetaData;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;
import com.integral.services.ServiceContainerMBean;
import com.integral.services.cluster.controller.ClusterControllerService;

import java.util.Collections;

/**
 * Created by raghunathans on 7/31/17.
 */
public class ClusterMetaDataChangeHandler implements NotificationObserver {

    private final Log log = LogFactory.getLog(this.getClass());

    protected static MessageLogger messageLogger = MessageLogger.getInstance();

    @Override
    public void notifyAdd(Notification notification) {
        log.info("Notification received for ADD ClusterMetadata:"+notification.getEntityId());
        ClusterMetaData cmd = ClusterConfigDataService.getInstance().getClusterMetaData(notification.getEntityNameSpace(), notification.getEntityId());

        if(null!=cmd && cmd.isControllerEnabled()){
            ServiceContainerMBean instance = ServiceContainerMBean.getInstance();

            boolean isStarted = ClusterControllerService.getInstance().start(instance, Collections.singletonList(cmd));
            if (!isStarted) {
                messageLogger.log(ISCommonConstants.CLUSTER_CONTROLLER_STARTUP_FAILED, ClusterMetaDataChangeHandler.class.getName(), "Failed to start the cluster controller", cmd.toString());
                log.error("Failed to start the cluster controller for:" + cmd.getClusterName());
            }
        }else{
            log.error("No cluster ClusterMetadata available with id:" + notification.getEntityId() + ", in the namespace:" + notification.getEntityNameSpace());
        }
    }

    @Override
    public void notifyUpdate(Notification notification) {
        log.info("Notification received for update ClusterMetadata:"+notification.getEntityId());
    }
}
