package com.integral.cluster.broker;

import com.integral.admin.utils.StringUtils;
import com.integral.broker.model.Stream;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.services.cluster.rebalancer.ConstraintAwareDynamicPartitionRebalancer;
import com.integral.services.cluster.rebalancer.strategy.ConstraintAwareAutoRebalanceStrategy;
import com.integral.system.property.SystemPropertyC;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

public class BAClusterRebalancer extends ConstraintAwareDynamicPartitionRebalancer {
    private static final Log log = LogFactory.getLog(BAClusterRebalancer.class);
    @Override
    public Set<String> getPartitions(String resourceName) {
        String groupName = resourceName.startsWith(BAClusterConstants.CLUSTER_NAME_PREFIX) ?
                resourceName.substring(BAClusterConstants.CLUSTER_NAME_PREFIX.length()) : resourceName;
        SystemPropertyC systemProperty = BAClusterUtil.getSystemProperty(groupName);
        if(systemProperty == null) {
            log.info("BAClusterRebalancer.getPartitions: empty for " + groupName);
            return Collections.emptySet();
        }
        String brkListStr = (String) systemProperty.getValue();
        log.info("BAClusterRebalancer.getPartitions: " + groupName + " " + brkListStr);
        if(StringUtils.isNullOrEmptyString(brkListStr)) {
            log.info("BAClusterRebalancer.getPartitions: empty for " + groupName);
            return Collections.emptySet();
        }
        String[] brkArray = brkListStr.split(",");
        Set<String> streamSet = new HashSet<String>();
        for(String brk : brkArray){
            Organization brkOrg = ReferenceDataCacheC.getInstance().getOrganization(brk);
            if (brkOrg == null || !brkOrg.isActive() || !brkOrg.isBroker()) continue;
            Collection<Stream> streams = brkOrg.getBrokerOrganizationFunction().getStreams();
            for(Stream stream : streams){
                if(!stream.isActive()) continue;
                streamSet.add(brk + "~" + stream.getShortName());
            }
        }
        log.info("BAClusterRebalancer.getPartitions for " + resourceName + ", streams=" + streamSet);
        return streamSet;
    }

    @Override
    protected ConstraintAwareAutoRebalanceStrategy.NamedPartitionReplicaPlacementScheme getNamedPartitionReplicaPlacementScheme() {
        return new BAClusterReplicaPlacementScheme();
    }
}
