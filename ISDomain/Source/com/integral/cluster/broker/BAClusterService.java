package com.integral.cluster.broker;

import com.integral.cluster.ClusterConfigDataService;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.ReferenceEntity;
import com.integral.model.cluster.ClusterMetaData;
import com.integral.model.cluster.PayloadInfo;
import com.integral.model.cluster.RebalancerInfo;
import com.integral.model.cluster.ResourceInfo;
import com.integral.rds.client.ClientFactory;
import com.integral.rds.client.ReferenceDataService;
import com.integral.rds.exception.ReferenceDataServiceException;
import com.integral.services.ServiceContainerMBean;
import com.integral.services.cluster.ClusterManager;
import com.integral.services.cluster.ClusterManagerFactory;
import com.integral.services.cluster.ClusterRegistry;
import com.integral.services.cluster.controller.ClusterControllerService;
import org.codehaus.jackson.map.ObjectMapper;

import java.util.Collections;

public class BAClusterService {
    private static final Log log = LogFactory.getLog(BAClusterService.class);
    private ServiceContainerMBean serviceContainerMBean;
    private ClusterRegistry clusterRegistry;
    private ObjectMapper objectMapper;
    private ReferenceDataService rdsClient;
    private ClusterConfigDataService configDataService;
    public BAClusterService(){
        try{
            objectMapper = new ObjectMapper();
            serviceContainerMBean = ServiceContainerMBean.getInstance();
            log.info("BAClusterService: " +
                    "zookeeper url=" + serviceContainerMBean.getZookeeperConnectString() +
                    ", instanceId=" + serviceContainerMBean.getInstanceId() +
                    ", address=" + serviceContainerMBean.getAddress() +
                    ", namespace=" + serviceContainerMBean.getServicesNamespace() +
                    ", conn timeout=" + serviceContainerMBean.getConnectionTimeoutMilliseconds() +
                    ", session timeout=" + serviceContainerMBean.getZookeeperSessionTimeoutMilliseconds() +
                    ", host name=" + serviceContainerMBean.getHostName() +
                    ", port=" + serviceContainerMBean.getPort() +
                    ", default service port=" + serviceContainerMBean.getDefaultServicePort());
            clusterRegistry = new ClusterRegistry(serviceContainerMBean);
            clusterRegistry.init();
            rdsClient = ClientFactory.getFactory().getReferenceDataService();
            configDataService = ClusterConfigDataService.getInstance();
        }catch (Exception e){
            log.error("Exception while instantiating BAClusterService", e);
        }
    }
    public ClusterMetaData createCluster(String groupName) {
        ClusterMetaData clusterMetaData = createClusterMetaData(groupName);
        boolean inHelix = clusterRegistry.getClusters().contains(clusterMetaData.getNameSpacedClusterName());
        boolean inRds = configDataService.getClusterMetaData(clusterMetaData.getNameSpacedClusterName()) != null;
        if(inHelix && inRds){
            log.info("BAClusterService.createCluster: Cluster Already exists in Helix and RDS " + groupName);
            return configDataService.getClusterMetaData(clusterMetaData.getNameSpacedClusterName());
        }
        if(!inHelix){
            inHelix = clusterRegistry.createCluster(clusterMetaData);
            log.info("BAClusterService.createCluster: Cluster created in Helix " + groupName + ", status=" + inHelix);
        }
        if(inHelix && !inRds) {
            inRds = createInRDS(clusterMetaData);
            log.info("BAClusterService.createCluster: Cluster created in RDS " + groupName + ", status=" + inRds);
        }
        if(inHelix && inRds) {
            log.info("BAClusterService.createCluster: Cluster created in Helix and RDS " + groupName);
            return configDataService.getClusterMetaData(clusterMetaData.getNameSpacedClusterName());
        }else {
            log.error("BAClusterService.createCluster: Cluster creation failed in Helix and RDS " + groupName + ", inHelix=" + inHelix + ", inRDS=" + inRds);
            return null;
        }
    }

    private boolean createInRDS(ClusterMetaData clusterMetaData) {
        try {
            ReferenceEntity referenceEntity = rdsClient.create(clusterMetaData);
            if (null == referenceEntity) {
                log.error("BAClusterService: Failed to create the cluster metadata in RDS: " + clusterMetaData);
                return false;
            }
            log.info("BAClusterService: Cluster Created in RDS " + clusterMetaData);
            return true;
        } catch (ReferenceDataServiceException e) {
            log.error("BAClusterService: Failed to create the cluster metadata in RDS: " + clusterMetaData, e);
            return false;
        }
    }

    public void joinCluster(String groupName) {
        try{
            ClusterMetaData clusterMetaData = createClusterMetaData(groupName);
            log.info("BAClusterService.joinCluster: clusterMetadata=" + objectMapper.writeValueAsString(clusterMetaData));
            ClusterManager clusterManager = ClusterManagerFactory.getInstance().createClusterManager(serviceContainerMBean, Collections.singletonList(clusterMetaData));
            clusterManager.startInstance(clusterMetaData.getClusterName(), new BAClusterStateTransitionHandler());
            log.info("BAClusterService.joinCluster: joined cluster " + clusterMetaData.getNameSpacedClusterName());
        }catch (Exception e){
            log.error("BAClusterService.joinCluster: Exception while joining cluster " + groupName, e);
        }
    }

    public void rebalanceCluster(String groupName) {
        ClusterMetaData clusterMetaData = createClusterMetaData(groupName);
        ResourceInfo resourceInfo = clusterMetaData.getResources().get(0);
        boolean result = ClusterControllerService.getInstance().reBalanceCluster(clusterMetaData
                        .getNameSpacedClusterName(), resourceInfo.getName(),
                resourceInfo.getPayloadInfo().getReplicas());
        log.info("BAClusterService.rebalanceCluster: rebalanced cluster " +
                clusterMetaData.getNameSpacedClusterName() + ", result=" + result);
    }

    private ClusterMetaData createClusterMetaData(String groupName) {
        if(!groupName.startsWith(BAClusterConstants.CLUSTER_NAME_PREFIX)) {
            groupName = BAClusterConstants.CLUSTER_NAME_PREFIX + groupName;
        }
        ClusterMetaData clusterMetaData = new ClusterMetaData();
        clusterMetaData.setClusterName(groupName);
        clusterMetaData.setMappedPartition(true);
        clusterMetaData.setClusterMode(3); //ONLINE_OFFLINE
        clusterMetaData.setControllerEnabled(true);
        clusterMetaData.setNameSpace(serviceContainerMBean.getServicesNamespace());
        clusterMetaData.setShortName(clusterMetaData.getNameSpacedClusterName());
        clusterMetaData.setMetaDataCustomizationClass(BAClusterCustomization.class.getName());
        ResourceInfo resourceInfo = new ResourceInfo();
        resourceInfo.setName(groupName);
        RebalancerInfo rebalancerInfo = new RebalancerInfo();
        rebalancerInfo.setMode(1);
        rebalancerInfo.setClassName(BAClusterRebalancer.class.getName());
        resourceInfo.setRebalancerInfo(rebalancerInfo);
        PayloadInfo payloadInfo = new PayloadInfo();
        payloadInfo.setCount(250);
        payloadInfo.setReplicas(1);
        resourceInfo.setPayloadInfo(payloadInfo);
        clusterMetaData.setResources(Collections.singletonList(resourceInfo));
        log.info("BAClusterService.createClusterMetaData: clusterMetaData=" + clusterMetaData);
        return clusterMetaData;
    }
}
