package com.integral.cluster.broker;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.cluster.ClusterMetaData;
import com.integral.services.cluster.CustomMetaDataProvider;

public class BAClusterCustomization implements CustomMetaDataProvider {
    private static final Log log = LogFactory.getLog(BAClusterCustomization.class);
    @Override
    public void updateMetaData(ClusterMetaData clusterMetaData) {
        log.info("BAClusterCustomization: Updating the cluster metadata placeholder " + clusterMetaData);
    }
}
