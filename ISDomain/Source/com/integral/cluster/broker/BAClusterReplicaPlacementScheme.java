package com.integral.cluster.broker;

import com.integral.broker.model.Stream;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.services.cluster.rebalancer.strategy.ConstraintAwareAutoRebalanceStrategy;
import com.integral.services.cluster.util.PropertyConstants;
import com.integral.user.Organization;
import org.apache.helix.AccessOption;
import org.apache.helix.HelixManager;
import org.apache.helix.ZNRecord;
import org.apache.helix.api.id.PartitionId;
import org.apache.helix.store.zk.ZkHelixPropertyStore;
import org.apache.zookeeper.data.Stat;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BAClusterReplicaPlacementScheme implements ConstraintAwareAutoRebalanceStrategy.NamedPartitionReplicaPlacementScheme {
    private static final Log log = LogFactory.getLog(BAClusterReplicaPlacementScheme.class);
    private String resourceName;
    private Map<String, String> partitionMap = new HashMap<String, String>();

    @Override
    public void init(HelixManager helixManager) {
        log.info("BAClusterReplicaPlacementScheme_: init()");
    }
    @Override
    public void init(HelixManager helixManager, String resourceName) {
        this.resourceName = resourceName;
        ZkHelixPropertyStore<ZNRecord> helixPropertyStore = helixManager.getHelixPropertyStore();
        ZNRecord znRecord = helixPropertyStore.get(PropertyConstants.PARTITION_MAPPING_PATH
                + resourceName, new Stat(), AccessOption.PERSISTENT);
        partitionMap = znRecord.getMapField(PropertyConstants.ACTIVE_PARTITIONS);
        if(partitionMap == null) partitionMap = new HashMap<String, String>();
        log.info("BAClusterReplicaPlacementScheme: resourceName=" + resourceName + " partitionMap=" + partitionMap);
    }

    @Override
    public String getLocation(int partitionId, int replicaId, int numPartitions, int numReplicas, List<String> nodeNames) {
        log.info("BAClusterReplicaPlacementScheme_: " + resourceName + " " + partitionId + " " + replicaId + " " + numPartitions + " " + numReplicas + " " + nodeNames);
        int index = partitionId % nodeNames.size();
        String selectedNode = nodeNames.get(index);
        log.info("BAClusterReplicaPlacementScheme_: selected node=" + selectedNode);
        return selectedNode;
    }
    @Override
    public String getLocation(PartitionId partitionId, int replicaId, int numPartitions, int numReplicas, List<String> nodeNames) {
        String partitionIdString = partitionId.stringify();
        log.info("BAClusterReplicaPlacementScheme: " + resourceName + " " + partitionIdString + " " + replicaId + " " + numPartitions + " " + numReplicas + " " + nodeNames);
        String partitionName = partitionMap.get(partitionIdString);
        String vsName = getVirtualServerForStream(partitionName);
        String selectedNode;
        if(vsName != null && nodeNames.contains(vsName)){
            selectedNode = vsName;
        }else {
            int index = Math.abs(partitionId.hashCode()) % nodeNames.size();
            selectedNode = nodeNames.get(index);
        }
        log.info("BAClusterReplicaPlacementScheme: selected for id=" + partitionId +
                ", name=" + partitionName + ", vs=" + vsName + ", node=" + selectedNode);
        return selectedNode;
    }

    private String getVirtualServerForStream(String partitionName) {
        try{
            String[] split = partitionName.split("~");
            String orgName = split[0];
            String streamName = split[1];
            Organization organization = ReferenceDataCacheC.getInstance().getOrganization(orgName);
            Stream stream = organization.getBrokerOrganizationFunction().getStream(streamName);
            return stream.getVirtualServerName();
        }catch (Exception e){
            log.error("BAClusterReplicaPlacementScheme.getVirtualServerForStream: exception", e);
            return null;
        }
    }
}
