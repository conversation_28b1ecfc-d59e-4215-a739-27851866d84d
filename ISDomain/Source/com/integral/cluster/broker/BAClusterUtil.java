package com.integral.cluster.broker;

import com.integral.exception.IdcNoSuchObjectException;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.query.QueryFactory;
import com.integral.query.QueryService;
import com.integral.system.property.SystemPropertyC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;

import java.util.Vector;

public class BAClusterUtil {
    private static final Log log = LogFactory.getLog(BAClusterUtil.class);
    public static SystemPropertyC getSystemProperty(String group) {
        try {
            QueryService qryService = QueryFactory.getQueryService();
            ExpressionBuilder builder = new ExpressionBuilder();
            Expression criteria = builder.get("shortName").equal(BAClusterConstants.BA_GROUP_MAPPING_PROPERTY)
                    .and(builder.get("scope").equal("GROUP"))
                    .and(builder.get("serverGroup").equal(group));
            ReadAllQuery qry = new ReadAllQuery(SystemPropertyC.class, criteria);
            Vector systemProperties = (Vector)qryService.find(qry, 'A');
            if(systemProperties != null && !systemProperties.isEmpty()) {
                return (SystemPropertyC) systemProperties.get(0);
            }
        } catch (IdcNoSuchObjectException e) {
            log.error("BAClusterRebalancer: Exception while getting system property", e);
        }
        return null;
    }
}
