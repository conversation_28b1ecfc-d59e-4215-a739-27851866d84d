package com.integral.cluster.broker;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.services.cluster.mapped.notification.OnlineOffLineMappedTransitionHandler;

public class BAClusterStateTransitionHandler implements OnlineOffLineMappedTransitionHandler {
    private static final Log log = LogFactory.getLog(BAClusterStateTransitionHandler.class);

    @Override
    public void dropped(String groupName, String partitionName) {
        log.info("BAClusterStateTransitionHandler.dropped(): " + groupName + " : " + partitionName);
        try{
            String brkName = split(partitionName)[0];
            String stream = split(partitionName)[1];
            log.info("BAClusterStateTransitionHandler dropped stream " + stream + " for broker " + brkName);
        }catch (Exception e){
            log.error("BAClusterStateTransitionHandler.dropped(): Exception while handling broker cluster change for " + partitionName, e);
        }
    }

    @Override
    public void onBecomeOnline(String groupName, String partitionName) {
        log.info("BAClusterStateTransitionHandler.onBecomeOnline(): " + groupName + " : " + partitionName);
        try{
            String brkName = split(partitionName)[0];
            String stream = split(partitionName)[1];
            log.info("BAClusterStateTransitionHandler started stream " + stream + " for broker " + brkName);
        }catch (Exception e){
            log.error("BAClusterStateTransitionHandler.onBecomeOnline(): Exception while handling broker cluster change for " + partitionName, e);
        }
    }

    @Override
    public void onBecomeOffline(String groupName, String partitionName) {
        log.info("BAClusterStateTransitionHandler.onBecomeOffline(): " + groupName + " : " + partitionName);
        try{
            String brkName = split(partitionName)[0];
            String stream = split(partitionName)[1];
            log.info("BAClusterStateTransitionHandler stopped stream " + stream + " for broker " + brkName);
        }catch (Exception e){
            log.error("BAClusterStateTransitionHandler.onBecomeOffline(): Exception while handling broker cluster change for " + partitionName, e);
        }
    }

    private String[] split(String partitionName) {
        return partitionName.split("~", 2);
    }
}
