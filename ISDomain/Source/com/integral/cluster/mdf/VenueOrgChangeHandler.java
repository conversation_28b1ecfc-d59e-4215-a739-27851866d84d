package com.integral.cluster.mdf;

import com.integral.cluster.ClusterConfigDataService;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.ConfigChangeHandler;
import com.integral.mdf.MDFConfigMBean;
import com.integral.model.cluster.ClusterMetaData;
import com.integral.model.cluster.ResourceInfo;
import com.integral.services.ServiceContainerMBean;
import com.integral.services.cluster.controller.ClusterControllerService;

import java.util.List;

/**
 * Created by raghunathans on 8/3/17.
 */
public class VenueOrgChangeHandler implements ConfigChangeHandler {

    public static final String MDFCLUSTER_PREFIX = "-MDFCluster-";
    private final Log log = LogFactory.getLog(this.getClass());

    public static final VenueOrgChangeHandler changeHandler = new VenueOrgChangeHandler();


    public static VenueOrgChangeHandler getInstance (){
        return changeHandler;
    }

    @Override
    public void handle(String key, String newValue, String oldValue) {

        if (key.contains(MDFConfigMBean.MDF_ENABLED_FI)) {
            log.info("Config change received for property:"+MDFConfigMBean.MDF_ENABLED_FI + ", oldValue:"+oldValue+ ",newValue:"+newValue);

            String venueName = key.replaceFirst(MDFConfigMBean.MDF_ENABLED_FI, "");

            ClusterConfigDataService configDataService = ClusterConfigDataService.getInstance();

            String namespace = ServiceContainerMBean.getInstance().getServicesNamespace();

            String id = namespace+ MDFCLUSTER_PREFIX +venueName;

            ClusterMetaData clusterMetaData = configDataService.getClusterMetaData(ClusterMetaData.NAMESPACE, id);

            if(clusterMetaData==null){
                log.error("VenueOrgChangeHandler.handle():No cluster metadata present for the id:"+id);
            }

            List<ResourceInfo> resources = clusterMetaData.getResources();
            if(!resources.isEmpty()){
                ResourceInfo resourceInfo = resources.get(0);
                //trigger for the re-balancing of FI's in the Venue
                boolean result = ClusterControllerService.getInstance().reBalanceCluster(clusterMetaData.getNameSpacedClusterName(), resourceInfo.getName(),
                        resourceInfo.getPayloadInfo().getReplicas());
                log.info("VenueOrgChangeHandler.handle():Re-balancing cluster :" +clusterMetaData.getNameSpacedClusterName()+",resource:"+resourceInfo.getName()+",result:"+result);
            }else{
                log.info("VenueOrgChangeHandler.handle():No resource present in cluster metadata:" + clusterMetaData);
            }
        }
    }
}
