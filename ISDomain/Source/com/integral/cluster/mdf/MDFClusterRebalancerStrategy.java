package com.integral.cluster.mdf;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.MDFConfigMBeanC;
import com.integral.services.cluster.rebalancer.strategy.ConstraintAwareAutoRebalanceStrategy;
import org.apache.helix.ZNRecord;
import org.apache.helix.api.State;
import org.apache.helix.api.id.ParticipantId;
import org.apache.helix.api.id.PartitionId;
import org.apache.helix.api.id.ResourceId;

import java.util.*;

public class MDFClusterRebalancerStrategy extends ConstraintAwareAutoRebalanceStrategy {

    Log log = LogFactory.getLog(this.getClass());

    public MDFClusterRebalancerStrategy(String resourceName, List<String> partitions, LinkedHashMap<String, Integer> states, int maximumPerNode, NamedPartitionReplicaPlacementScheme placementScheme) {
        super(resourceName, partitions, states, maximumPerNode, placementScheme);
        log.info("MDFClusterRebalancerStrategy() states = " + states + " maximumPerNode="+maximumPerNode);
    }

    public MDFClusterRebalancerStrategy(ResourceId resourceId, List<PartitionId> partitions, LinkedHashMap<State, Integer> states, int maximumPerNode, NamedPartitionReplicaPlacementScheme placementScheme) {
        super(resourceId, partitions, states, maximumPerNode, placementScheme);
        log.info("MDFClusterRebalancerStrategy() states = " + states + " maximumPerNode="+maximumPerNode);
    }

    protected Map<Replica, Node> computePreferredPlacement(final List<String> nodeNames) {
        log.info(" statesMAp="+this._stateMap+", liveNodes= "+_liveNodesList);
        return super.computePreferredPlacement(nodeNames);
    }

        protected Map<Replica, Node> computeExistingPreferredPlacement(
            final Map<PartitionId, Map<ParticipantId, State>> currentMapping) {
        log.infoAsFormat("%s - _preferredAssignment -> %s" ,"_preferredAssignment", fromMap(_preferredAssignment));
        Map<Replica, Node> res = super.computeExistingPreferredPlacement(currentMapping);
        logMaps("computeExistingPreferredPlacement");
        return res;
    }

    protected Map<Replica, Node> computeExistingNonPreferredPlacement(
            Map<PartitionId, Map<ParticipantId, State>> currentMapping) {
        log.infoAsFormat("%s - _existingPreferredAssignment -> %s"  ,"_existingPreferredAssignment", fromMap(_existingPreferredAssignment));
        Map<Replica, Node> res = super.computeExistingNonPreferredPlacement(currentMapping);
        logMaps("computeExistingNonPreferredPlacement");
        return res;
    }

    protected Set<Replica> computeOrphaned() {
        log.infoAsFormat("%s - _existingNonPreferredAssignment -> %s"  ,"", fromMap(_existingNonPreferredAssignment));
        return super.computeOrphaned();
    }

    protected void moveNonPreferredReplicasToPreferred() {
        super.moveNonPreferredReplicasToPreferred();
        logMaps("moveNonPreferredReplicasToPreferred");
    }

    protected void assignOrphans() {
        super.assignOrphans();
        logMaps("assignOrphans");
    }

    protected void moveExcessReplicas() {
        super.moveExcessReplicas();
        logMaps("moveExcessReplicas");
    }

    protected void prepareResult(ZNRecord znRecord) {
        _prepareResult(znRecord);
        log.info("prepareResult post - znRepord.list" + znRecord.getListFields());
        log.info("prepareResult post - znRepord.list" + znRecord.getMapFields());
    }

    /**
     * Update a ZNRecord with the results of the rebalancing.
     * @param znRecord
     */
    protected void _prepareResult(ZNRecord znRecord) {
        // The map fields are keyed on partition name to a pair of node and state, i.e. it
        // indicates that the partition with given state is served by that node
        //
        // The list fields are also keyed on partition and list all the nodes serving that partition.
        // This is useful to verify that there is no node serving multiple replicas of the same
        // partition.
        Map<String, List<String>> newPreferences = new TreeMap<String, List<String>>();
        for (PartitionId partition : _partitions) {
            String partitionName = partition.stringify();
            znRecord.setMapField(partitionName, new TreeMap<String, String>());
            znRecord.setListField(partitionName, new ArrayList<String>());
            newPreferences.put(partitionName, new ArrayList<String>());
        }

        List<Node> labeledNodes = new ArrayList<Node>(_liveNodesList.size());
        List<Node> genericNodes = new ArrayList<Node>(_liveNodesList.size());

        splitNodes(_liveNodesList,labeledNodes,genericNodes);

        // for preference lists, the rough priority that we want is:
        // [existing preferred, existing non-preferred, non-existing preferred, non-existing
        // non-preferred]

        for (Node node : labeledNodes) {
            for (Replica replica : node.getPreferred()) {
                znRecord.getListField(replica.getPartition().toString()).add(node.getId().toString());
            }
        }

        for (Node node : genericNodes) {
            for (Replica replica : node.getNonPreferred()) {
                if (node.getNewReplicas().contains(replica)) {
                    newPreferences.get(replica.getPartition().toString()).add(node.getId().toString());
                } else {
                    znRecord.getListField(replica.getPartition().toString()).add(node.getId().toString());
                }
            }
        }
        normalizePreferenceLists(znRecord.getListFields(), newPreferences);

        // generate preference maps based on the preference lists
        for (PartitionId partition : _partitions) {
            List<String> preferenceList = znRecord.getListField(partition.toString());
            int i = 0;
            for (String participant : preferenceList) {
                znRecord.getMapField(partition.toString()).put(participant, _stateMap.get(i).toString());
                i++;
            }
        }
    }

    private void splitNodes(List<Node> liveNodesList, List<Node> labeledNodes, List<Node> genericNodes) {
        Map<String,Collection<String>> nodeLevels = MDFConfigMBeanC.
                getInstance().getNodeServiceLevels(this._resourceId.stringify());

    }


    void logMaps(String event){
        log.infoAsFormat("method - %s nodes=%s",event,fromNodeMap(_nodeMap));
    }

    protected void _normalizePreferenceList(List<String> preferenceList, Map<String, Map<String, Integer>> nodeReplicaCounts) {
        log.info("normalizePreferenceList - IGNORED");
    }

    String fromMap(Map<Replica, Node> map){
        if(map==null){
            return "{null}";
        }
        Iterator<Map.Entry<Replica, Node>> i = map.entrySet().iterator();
        if (! i.hasNext())
            return "{empty}";

        StringBuffer sb = new StringBuffer();
        sb.append('{');
        for (;;) {
            Map.Entry<Replica, Node> e = i.next();
            Replica key = e.getKey();
            Node value = e.getValue();
            sb.append(key);
            sb.append('=');
            appendTo(value,sb);
            if (! i.hasNext())
                return sb.append('}').toString();
            sb.append(',').append(' ');
        }
    }

    String fromNodeMap(Map<ParticipantId, Node> map){
        if(map==null){
            return "{null}";
        }
        Iterator<Map.Entry<ParticipantId, Node>> i = map.entrySet().iterator();
        if (! i.hasNext())
            return "{empty}";

        StringBuffer sb = new StringBuffer();
        sb.append('{');
        for (;;) {
            Map.Entry<ParticipantId, Node> e = i.next();
            ParticipantId key = e.getKey();
            Node value = e.getValue();
            sb.append(key);
            sb.append('=');
            appendToDetails(value,sb);
            if (! i.hasNext())
                return sb.append('}').toString();
            sb.append(',').append(' ');
        }
    }

    void appendTo(Node node,StringBuffer sb){
        String s = node.toString().replace('\n',' ');
        sb.append(s);
    }

    void appendToDetails(Node node,StringBuffer sb){
        String s = node.toString().replace('\n',' ');
        sb.append(s);
        sb.append(" preffered replicas =").append(node.getPreferred());
        sb.append(" preffered replicas =").append(node.getNonPreferred());
        sb.append(" preffered replicas =").append(node.getNewReplicas());
    }

}