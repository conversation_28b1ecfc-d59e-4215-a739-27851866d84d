package com.integral.cluster.mdf;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.MDFConfigMBeanC;
import com.integral.model.ReferenceEntity;
import com.integral.rds.client.ClientFactory;
import com.integral.rds.client.QueryIterator;
import com.integral.rds.client.ReferenceDataService;
import com.integral.rds.message.Query;
import com.integral.rds.message.QueryBuilder;
import com.integral.user.MarketDataSubscription;

import java.util.*;
import java.util.concurrent.TimeUnit;

public class MDGSubscritionsCache {

    private static Log log = LogFactory.getLog(MDGSubscritionsCache.class);

    final static long seven_days_mills = TimeUnit.DAYS.toMillis(7);

    private static MDGSubscritionsCache instance = new MDGSubscritionsCache();

    volatile boolean isInitialized = false;

    Map<String,Set<String>> subscriptions = new HashMap<String, Set<String>>();

    private MDGSubscritionsCache(){
    }

    public static MDGSubscritionsCache getInstance(){
        return instance;
    }

    public boolean init(){
        long  st = System.currentTimeMillis();
        List<String> fis = MDFConfigMBeanC.getInstance().getAllMDFEnabledFIs();
        for(String fi:fis){
            Set<String> list = queryLastSubscribedPairs(fi);
            subscriptions.put(fi,list);
        }
        log.info("initialized mdg subscriptions cache. num fis="+fis.size()+" time taken="+(System.currentTimeMillis()-st));
        return isInitialized=true;
    }

    public boolean isSubscribed(String fi,String ccyp){
        if(!isInitialized || fi==null || ccyp==null) return false;
        Set<String> ccyps = subscriptions.get(fi);
        if(ccyps!=null)
            return ccyps.contains(ccyp);
        return false;
    }

    public Set<String> getSubscribedPairs(String fi){
        if(!isInitialized || fi==null) return Collections.EMPTY_SET;
        Set<String> ccyps = subscriptions.get(fi);
        return ccyps != null ? Collections.unmodifiableSet(ccyps) : Collections.EMPTY_SET;
    }

    public Set<String> queryLastSubscribedPairs(String fi){
        QueryBuilder builder = new QueryBuilder(MarketDataSubscription.class);
        builder.addStringParam("org", fi);
        builder.greaterThan("time",String.valueOf(System.currentTimeMillis()-seven_days_mills));

        List<MarketDataSubscription> subs = getObjectByQuery(MarketDataSubscription.NAMESPACE, builder);

        if(subs!=null && !subs.isEmpty()){
            HashSet<String> set = new HashSet<String>();
            for(MarketDataSubscription mds:subs){
                set.add(mds.getCurrencypair());
            }
            return set;
        }

        return Collections.EMPTY_SET;
    }

    public static List<MarketDataSubscription> getObjectByQuery(String namespace, QueryBuilder builder) {
        if(builder==null)return null;

        Class type = MarketDataSubscription.class;
        Query queryParams = builder.build();
        try {
            List<MarketDataSubscription> referenceEntities = new ArrayList();
            ReferenceDataService rds = ClientFactory.getFactory().getReferenceDataService();
            QueryIterator iter = rds.getIterator(MarketDataSubscription.class, namespace,queryParams);
            while(iter.hasNext()){
                referenceEntities.add((MarketDataSubscription)iter.next());
            }
            return referenceEntities;
        } catch (Exception e) {
            log.error("Error in retrieving object of type:" + type + " by query:" + queryParams, e);
        }
        return null;
    }

}
