package com.integral.cluster.mdf;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.MDFConfigMBeanC;
import com.integral.services.cluster.rebalancer.strategy.ConstraintAwareAutoRebalanceStrategy;
import com.integral.services.cluster.util.PropertyConstants;
import com.integral.util.StringUtil;
import org.apache.helix.AccessOption;
import org.apache.helix.HelixManager;
import org.apache.helix.ZNRecord;
import org.apache.helix.api.id.PartitionId;
import org.apache.helix.store.zk.ZkHelixPropertyStore;
import org.apache.zookeeper.data.Stat;

import java.util.*;

public class FixedNodeReplicaPlacementScheme implements
        ConstraintAwareAutoRebalanceStrategy.NamedPartitionReplicaPlacementScheme{

    Log log = LogFactory.getLog(this.getClass());

    HelixManager manager;
    String resourceName;

    //PartitionId - Name map
    Map<String,String> partitionNameMap = new HashMap<String,String>();


    @Override
    public void init(HelixManager manager, String resourceName) {
        this.manager = manager;
        this.resourceName = resourceName;
        ZkHelixPropertyStore<ZNRecord> helixPropertyStore = manager.getHelixPropertyStore();

        ZNRecord znRecord = helixPropertyStore.get(PropertyConstants.PARTITION_MAPPING_PATH + resourceName, new Stat(),
                AccessOption.PERSISTENT);
        if(null!=znRecord) {
            Map<String, String> mapField = znRecord.getMapField(PropertyConstants.ACTIVE_PARTITIONS);
            partitionNameMap.putAll(mapField);
        }
    }

    @Override
    public String getLocation(PartitionId partitionId, int replicaId, int numPartitions, int numReplicas, List<String> nodeNames) {

        String _strPartitionId = partitionId.stringify();
        int startIndex = Math.abs(partitionId.hashCode()) % nodeNames.size();

        if(log.isDebugEnabled()){
            log.debug(StringUtil.fastConcat("getLocation: partitionId=",
                    _strPartitionId,
                    " ,replicaId=",String.valueOf(replicaId),
                    " ,numPartitions=",String.valueOf(numPartitions),
                    " ,numReplicas=",String.valueOf(numReplicas),
                    " ,nodes=",nodeNames.toString()
            ));
        }

        //Only one node
        if(nodeNames.size()==1){
            log.info(StringUtil.fastConcat("getLocation: only active node assigned. partitionId=",
                    _strPartitionId,
                    " ,node=",nodeNames.toString()
            ));
            return nodeNames.get(0);
        }

        String bookName = partitionNameMap.get(_strPartitionId);

        if(bookName==null){
            String node = nodeNames.get(startIndex);
            log.info(StringUtil.fastConcat("getLocation: unmapped partition ." ,
                            "consistent node assignment. partitionId=",_strPartitionId,
                    " ,partitionName=",bookName,
                    " ,node=",node
            ));
            return node;
        }

        String[] components = bookName.split("/");
        if(components.length<3){
            String node = nodeNames.get(startIndex);
            log.info(StringUtil.fastConcat("getLocation: unknown format .consistent node assignment. partitionId=",
                    _strPartitionId,
                    " ,partitionName=",bookName,
                    " ,node=",node
            ));
            return node;
        }

        String org = components[0];
        String ccyp = components[1] + '/' + components[2];
        String orgServiceLevel = MDFConfigMBeanC.getInstance().getOrgServiceLevel(org,this.resourceName);
        String ccypServiceLevel = MDFConfigMBeanC.getInstance().getCurrencyPairFreqLevel(ccyp,this.resourceName);

        Map<String,Collection<String>> nodeLevels = MDFConfigMBeanC.
                getInstance().getNodeServiceLevels(this.resourceName);

        List<String> generalNodes = new ArrayList<String>(nodeNames.size());//max size
        List<String> matchingNodes = new ArrayList<String>(nodeNames.size());//max size

        for(String node:nodeNames){
            Collection<String> levels = nodeLevels.get(node);
            if(levels!=null&&!levels.isEmpty()) {
                if (containsBoth(levels, orgServiceLevel, ccypServiceLevel)) {
                    matchingNodes.add(node);
                }
            }else{
                //general node found.
                generalNodes.add(node);
            }
        }

        if(matchingNodes.size()>0){
            int idx = Math.abs(partitionId.hashCode()) % matchingNodes.size();
            String node = matchingNodes.get(idx);
            log.info(StringUtil.fastConcat("getLocation: preferred node assignment. partitionId=",
                    partitionId.stringify(),
                    " ,partitionName=", bookName,
                    " ,node=", node
            ));
            return node;
        }

        //Since no general nodes are avialiable use whatever is available.
        if(generalNodes.size()==0){
            generalNodes = nodeNames;
        }

        startIndex = Math.abs(partitionId.hashCode()) % generalNodes.size();
        String node = generalNodes.get(startIndex);
        log.info(StringUtil.fastConcat("getLocation: consistent node assignment. partitionId=",
                partitionId.stringify(),
                " ,partitionName=",bookName,
                " ,node=",node
        ));
        return node;
    }

    boolean containsEither(Collection<String> levels,String level1,String level2){
        if( levels==null || levels.isEmpty() )
            return false;
        for(String servicelevel : levels){
            if(servicelevel.matches(level1) || servicelevel.matches(level2))
                return true;
        }
        return false;
    }


    boolean containsBoth(Collection<String> levels,String level1,String level2){
        if( levels==null || levels.isEmpty() )
            return false;
        return !levels.contains(level1)?false:levels.contains(level2);
    }

    //  Below methods shouldn't be called.
    @Override
    public void init(HelixManager manager) {
        Thread.currentThread().dumpStack();
    }

    @Override
    public String getLocation(int partitionId, int replicaId, int numPartitions, int numReplicas, List<String> nodeNames) {
        Thread.currentThread().dumpStack();
        return null;
    }
}
