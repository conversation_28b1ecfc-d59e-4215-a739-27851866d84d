package com.integral.cluster.mdf;

import com.integral.services.cluster.rebalancer.ConstraintAwareDynamicPartitionRebalancer;
import com.integral.services.cluster.rebalancer.strategy.ConstraintAwareAutoRebalanceStrategy;
import org.apache.helix.HelixManager;
import org.apache.helix.api.id.PartitionId;
import org.apache.helix.controller.strategy.AutoRebalanceStrategy;

import java.util.List;

public class OrgReplicaPlacementScheme
        extends AutoRebalanceStrategy.DefaultPlacementScheme
        implements ConstraintAwareAutoRebalanceStrategy.NamedPartitionReplicaPlacementScheme {
    @Override
    public void init(HelixManager manager, String resourceName) {
        super.init(manager);
    }

    @Override
    public String getLocation(PartitionId partitionId, int replicaId, int numPartitions, int numReplicas, List<String> nodeNames) {
        Thread.currentThread().dumpStack();
        return null;
    }
}
