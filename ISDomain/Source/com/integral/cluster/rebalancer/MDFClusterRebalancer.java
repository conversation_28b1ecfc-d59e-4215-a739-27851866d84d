package com.integral.cluster.rebalancer;

import com.google.common.base.Function;
import com.google.common.base.Functions;
import com.google.common.collect.Lists;
import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.cluster.mdf.*;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.fx.FXRateBasis;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.MDFConfigMBeanC;
import com.integral.persistence.Entity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.provision.VenueConfiguration;
import com.integral.services.cluster.rebalancer.DynamicPartitionRebalancer;
import com.integral.services.cluster.rebalancer.strategy.ConstraintAwareAutoRebalanceStrategy;
import com.integral.services.cluster.util.PropertyConstants;
import com.integral.tradingvenue.TradingVenueClassificationEnums;
import com.integral.tradingvenue.TradingVenueEntity;
import com.integral.tradingvenue.TradingVenueRelationShip;
import com.integral.user.MarketDataSubscription;
import com.integral.user.Organization;
import org.apache.helix.*;
import org.apache.helix.api.Cluster;
import org.apache.helix.api.Participant;
import org.apache.helix.api.State;
import org.apache.helix.api.id.ParticipantId;
import org.apache.helix.api.id.PartitionId;
import org.apache.helix.api.id.ResourceId;
import org.apache.helix.controller.context.ControllerContextProvider;
import org.apache.helix.controller.rebalancer.config.RebalancerConfig;
import org.apache.helix.controller.rebalancer.util.ConstraintBasedAssignment;
import org.apache.helix.controller.stages.ResourceCurrentState;
import org.apache.helix.controller.strategy.AutoRebalanceStrategy;
import org.apache.helix.model.IdealState;
import org.apache.helix.model.ResourceAssignment;
import org.apache.helix.model.StateModelDefinition;
import org.apache.helix.store.zk.ZkHelixPropertyStore;
import org.apache.zookeeper.data.Stat;

import java.util.*;

import static com.integral.mdf.MDFConfigMBean.MDF_PARTITION_TYPE_BOOK;
import static com.integral.mdf.MDFConfigMBean.MDF_PARTITION_TYPE_ORG;
import static com.integral.tradingvenue.TradingVenueClassificationEnums.VenueType.CLOB;

/**
 * Created by raghunathans on 6/8/17.
 */
public class MDFClusterRebalancer extends DynamicPartitionRebalancer {

    Log log = LogFactory.getLog(this.getClass());

    public static final String book_delimiter = "/" ;

    private VenueOrgChangeHandler instance = VenueOrgChangeHandler.getInstance();

    public static final String SEPARATOR = "_";

    FixedNodeReplicaPlacementScheme placementScheme = new FixedNodeReplicaPlacementScheme();


    public MDFClusterRebalancer(){
        //Include the trigger for the property change listener
        MDFConfigMBeanC.getInstance().setConfigChangeHandler(instance);
    }

   @Override
    public void init(HelixManager helixManager, ControllerContextProvider contextProvider) {
        super.init(helixManager,contextProvider);
        placementScheme.init(helixManager,this.resourceName);
    }

    @Override
    public Set<String> getPartitions(String venue) {
        String pType = MDFConfigMBeanC.getInstance().getMDFPartitionType(venue);
        if(MDF_PARTITION_TYPE_ORG.equals(pType)) {
            return getMembers(venue);
        }else if(MDF_PARTITION_TYPE_BOOK.equals(pType)){
            Set<String> members = getMembers(venue);
            Collection<String> ccypairs = getCurrencyPairs(venue);
            Set<String> partitions = new LinkedHashSet<String>();
            for(String member : members){
                boolean isFilter = MDFConfigMBeanC.getInstance().isSubscriptionFilterEnabled(venue,member);
                if (!isFilter) {
                	for(String ccyp:ccypairs){
                    	partitions.add(member+book_delimiter+ccyp);
                    }
                } else {
                    Set<String> subscribedPairs = MDGSubscritionsCache.getInstance().queryLastSubscribedPairs(member);
                    for (String ccyp : ccypairs) {
                        if (subscribedPairs.contains(ccyp)) {
                            partitions.add(member + book_delimiter + ccyp);
                        }
                    }
                }
            }
            return partitions;
        }
        return Collections.emptySet();
    }

    /**
     * @return true if organization is not ExternalProvider, ECN, Broker, Masked LP, ClearingHouse, PrimeBroker.
     */
    public static boolean isCustomerOrganization(Organization organization)
    {
        return !( organization.isExternalProvider() || organization.isECN() || organization.isBroker() || organization.isMasked() || organization.isClearingHouse() || organization.isPrimeBroker() );
    }


    Set<String> getMembers(String venueName) {

        Set<String> memberList = new LinkedHashSet<String>();
        Organization tvOrg = OrganizationUtil.getOrganization(venueName);

        if(tvOrg==null){
            throw new IllegalStateException("Invalid Venue name ,no organization found for the venue name:"+venueName);
        }

        TradingVenueEntity tradingVenue = (TradingVenueEntity) ReferenceDataCacheC.getInstance().getEntityByShortName(venueName, TradingVenueEntity.class,
                null, Entity.ACTIVE_STATUS);

        if(tradingVenue == null){
            throw new IllegalStateException("Invalid Venue name ,no trading venue found for the venue name:"+venueName);
        }

        if(tradingVenue.getTvType().equals(TradingVenueClassificationEnums.TvType.PRICE_DISTRIBUTION)){
            List<String> members = MDFConfigMBeanC.getInstance().getMDFEnabledFI(venueName);
            memberList.addAll(members);
            return memberList;
        }

        Collection<TradingVenueRelationShip> tvCustomersRelations = tvOrg.getTvCustomersRelations();

        if(tvCustomersRelations != null){
            for(TradingVenueRelationShip tradingVenueRelationShip : tvCustomersRelations){
                Organization customerOrg = tradingVenueRelationShip.getCustomerOrg();
                if( null != customerOrg
                        && isCustomerOrganization(customerOrg)
                        && MDFConfigMBeanC.getInstance().getMDFEnabledFI(venueName).contains(customerOrg.getShortName()) )
                {
                    memberList.add(customerOrg.getShortName());
                }
            }
        }

        return memberList;
    }


    Collection<String> getCurrencyPairs(String venueOrg){

        TradingVenueEntity tradingVenue = (TradingVenueEntity) ReferenceDataCacheC.getInstance()
                .getEntityByShortName(venueOrg, TradingVenueEntity.class, null, null);

        if (tradingVenue == null || tradingVenue.getVenueType().getId() != CLOB.getId()) {
            Collections.emptyList();
        }

        Map<String, VenueConfiguration> ccyVenueMap = new HashMap<String, VenueConfiguration>();
       // ccyVenueMap.put(VenueConfiguration.DEFAULT_CONFIG, null);
        if(tradingVenue.getSupportedCurrencyPairs() != null){
            Collection<CurrencyPairGroup> currencyPairGroups = tradingVenue.getSupportedCurrencyPairs().getCurrencyPairGroups();
            if(currencyPairGroups != null) {
                for (CurrencyPairGroup currencyPairGroup : currencyPairGroups) {
                    Collection<CurrencyPair> currencyPairs = currencyPairGroup.getCurrencyPairs();
                    if(currencyPairs != null) {
                        for (CurrencyPair currencyPair : currencyPairs) {
                            ccyVenueMap.put(currencyPair.getDisplayName(), null);
                        }
                    }
                }
            }

            Collection<FXRateBasis> includedFXRateBasis = tradingVenue.getSupportedCurrencyPairs().getIncludedFXRateBasis();
            if(includedFXRateBasis != null) {
                for (FXRateBasis fxRateBasis : includedFXRateBasis) {
                    ccyVenueMap.put(fxRateBasis.getCurrencyPairString(),null);
                }
            }
        }

        return ccyVenueMap.keySet();
    }

    @Override
    public ResourceAssignment computeResourceMapping(IdealState idealState,
                                                     RebalancerConfig rebalancerConfig, ResourceAssignment prevAssignment, Cluster cluster,
                                                     ResourceCurrentState currentState) {

        long startTime = System.currentTimeMillis();
        String pType = MDFConfigMBeanC.getInstance().getMDFPartitionType(this.resourceName);
        log.info("Balancing the cluster for resource:" + idealState.getId() + ", partitionType="+pType);

        if(MDF_PARTITION_TYPE_ORG.equals(pType)) {
            return super.computeResourceMapping(idealState,rebalancerConfig,prevAssignment,cluster,currentState);
        }

        if (!idealState.isEnabled()) {
            log.info("Skipping custom re-balancing as the resource:" + idealState.getId() +"  is disabled");
            ResourceAssignment assignment = super.computeResourceMapping(
                    idealState, rebalancerConfig, prevAssignment, cluster,
                    currentState);
            log.info("Updated Assignment:" + assignment);
            return assignment;
        }

        long t1 = System.currentTimeMillis();

        //Custom-logic-begin
        ///Bring in the custom partitions here
        ZkHelixPropertyStore<ZNRecord> helixPropertyStore = helixManager.getHelixPropertyStore();

        ResourceId resourceId = idealState.getResourceId();
        ZNRecord znRecord = helixPropertyStore.get(PropertyConstants.PARTITION_MAPPING_PATH
                + resourceId, new Stat(), AccessOption.PERSISTENT);

        Set<PartitionId> partitions = getCurrentPartitions(znRecord); //only mapped partitions...
        //Custom-logic-end
        long t2 = System.currentTimeMillis();

        List<ParticipantId> liveParticipantList = new ArrayList<ParticipantId>(cluster.getLiveParticipantMap().keySet());
        if(liveParticipantList.isEmpty()){
            log.info("Skipping custom re-balancing as there are no live participants");
            ResourceAssignment assignment = super.computeResourceMapping(
                    idealState, rebalancerConfig, prevAssignment, cluster,
                    currentState);
            log.info("Updated Assignment:" + assignment);
            return assignment;
        }


        List<String> liveNodeNames =
                Lists.newArrayList(Lists.transform(liveParticipantList, Functions.toStringFunction()));


        boolean isEnabled = (idealState != null) ? idealState.isEnabled() : true;
        StateModelDefinition stateModelDef = cluster.getStateModelMap().get(idealState.getStateModelDefId());

        // Compute a preference list based on the current ideal state
        //List<PartitionId> partitions = new ArrayList<PartitionId>(idealState.getPartitionIdSet());
        Map<ParticipantId, Participant> liveParticipants = cluster.getLiveParticipantMap();
        Map<ParticipantId, Participant> allParticipants = cluster.getParticipantMap();
        // get the participant lists

        int replicas = 1;

        // count how many replicas should be in each state
        Map<State, String> upperBounds =
                ConstraintBasedAssignment.stateConstraints(stateModelDef, idealState.getResourceId(),
                        cluster.getConfig());
        LinkedHashMap<State, Integer> stateCountMap =
                ConstraintBasedAssignment.stateCount(upperBounds, stateModelDef, liveParticipants.size(),
                        replicas);
        long t3 = System.currentTimeMillis();


        // compute the current mapping from the current state
        Map<PartitionId, Map<ParticipantId, State>> currentMapping =
                currentMapping(idealState, currentState, stateCountMap);

        //Custom-logic-begin
        Map<PartitionId, Map<ParticipantId, State>> currentMappingOrgnl =
                currentMapping(idealState, currentState, stateCountMap);
        long t4 = System.currentTimeMillis();


        Set<PartitionId> toRemovePartitions = new HashSet<PartitionId>();
        for (Map.Entry<PartitionId, Map<ParticipantId, State>> entry : currentMapping.entrySet()) {
            if(!partitions.contains(entry.getKey())){
                toRemovePartitions.add(entry.getKey());
            }
        }

        for (PartitionId partitionId : toRemovePartitions) {
            currentMapping.remove(partitionId);
        }
        long t5 = System.currentTimeMillis();

        //Custom-logic-end

        // compute a full partition mapping for the resource
        if (log.isDebugEnabled()) {
            log.debug("Processing resource:" + idealState.getResourceId());
        }
        ResourceAssignment partitionMapping = new ResourceAssignment(idealState.getResourceId());
        Map<PartitionId,Map<ParticipantId, State>> mappings = new HashMap<PartitionId, Map<ParticipantId,State>>();
        for (PartitionId partition : partitions) {
            Set<ParticipantId> disabledParticipantsForPartition =
                    ConstraintBasedAssignment.getDisabledParticipants(allParticipants, partition);

            List<String> rawPreferenceList = getPrefferedNodes(partition,liveNodeNames);
            if (rawPreferenceList == null) {
                rawPreferenceList = Collections.emptyList();
            }
            List<ParticipantId> preferenceList =
                    Lists.transform(rawPreferenceList, new Function<String, ParticipantId>() {
                        @Override
                        public ParticipantId apply(String participantName) {
                            return ParticipantId.from(participantName);
                        }
                    });

            //preferenceList = ConstraintBasedAssignment.getPreferenceList(cluster, partition, preferenceList);

            Map<ParticipantId, State> bestStateForPartition =
                    ConstraintBasedAssignment.computeAutoBestStateForPartition(upperBounds,
                            liveParticipants.keySet(), stateModelDef, preferenceList,
                            currentState.getCurrentStateMap(idealState.getResourceId(), partition),
                            disabledParticipantsForPartition, isEnabled);

            partitionMapping.addReplicaMap(partition, bestStateForPartition);
            mappings.put(partition, bestStateForPartition);
        }

        long t6 = System.currentTimeMillis();


        //Remove unmapped partitions from assignment.
        for (PartitionId partition : toRemovePartitions) {
            Map<ParticipantId, State> map = currentMappingOrgnl.get(partition);
            if(null!=map){
                Map<ParticipantId, State> newStates= new HashMap<ParticipantId, State>();
                for (Map.Entry<ParticipantId, State> states : map.entrySet()) {
                    newStates.put(states.getKey(), State.from(HelixDefinedState.DROPPED));
                }
                partitionMapping.addReplicaMap(partition, newStates);
                if(!map.isEmpty()){
                    mappings.put(partition, newStates);
                }
            }
        }

        long t7 = System.currentTimeMillis();


        log.info("computeResourceMapping - Current Partition Assignment: num=" + mappings.size()
                +" t1="+(t1-startTime)
                +" t2="+(t2-t1)
                +" t3="+(t3-t2)
                +" t4="+(t4-t3)
                +" t5="+(t5-t4)
                +" t6="+(t6-t5)
                +" t7="+(t7-t6)
                +" total="+(t7-startTime)
        );

        if(log.isDebugEnabled()){
            log.info("Complete Partition Assignment:" + partitionMapping.toString());
        }

        return partitionMapping;
    }

    private List<String> getPrefferedNodes(PartitionId partitionId,List<String> nodes) {
        String prefferedNode = placementScheme.getLocation(partitionId,1,Integer.MAX_VALUE,1,nodes);
        List<String> pNodes = new ArrayList<String>(1);
        pNodes.add(prefferedNode);
        return pNodes;
    }

    private Map<PartitionId, Map<ParticipantId, State>> currentMapping(IdealState idealState,
                                                                       ResourceCurrentState currentStateOutput, Map<State, Integer> stateCountMap) {
        Map<PartitionId, Map<ParticipantId, State>> map =
                new HashMap<PartitionId, Map<ParticipantId, State>>();

        for (PartitionId partition : idealState.getPartitionIdSet()) {
            Map<ParticipantId, State> curStateMap =
                    currentStateOutput.getCurrentStateMap(idealState.getResourceId(), partition);
            map.put(partition, new HashMap<ParticipantId, State>());
            for (ParticipantId node : curStateMap.keySet()) {
                State state = curStateMap.get(node);
                if (stateCountMap.containsKey(state)) {
                    map.get(partition).put(node, state);
                }
            }

            Map<ParticipantId, State> pendingStateMap =
                    currentStateOutput.getPendingStateMap(idealState.getResourceId(), partition);
            for (ParticipantId node : pendingStateMap.keySet()) {
                State state = pendingStateMap.get(node);
                if (stateCountMap.containsKey(state)) {
                    map.get(partition).put(node, state);
                }
            }
        }
        return map;
    }

    private Set<PartitionId> getCurrentPartitions(ZNRecord znRecord) {
        Set<PartitionId> aPartitions = new HashSet<PartitionId>();
        if (null != znRecord) {
            Map<String, String> mapField = znRecord.getMapField(PropertyConstants.ACTIVE_PARTITIONS);
            if (null != mapField) {
                for (Map.Entry<String, String> partitions : mapField.entrySet()) {
                    if (null != partitions.getValue()) {
                        aPartitions.add(PartitionId.from(partitions.getKey()));
                    }
                }
            }
        }
        return aPartitions;
    }

}
