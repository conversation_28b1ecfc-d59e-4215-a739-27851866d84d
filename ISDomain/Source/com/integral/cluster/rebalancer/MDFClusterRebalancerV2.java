package com.integral.cluster.rebalancer;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.cluster.mdf.FixedNodeReplicaPlacementScheme;
import com.integral.cluster.mdf.MDFClusterRebalancerStrategy;
import com.integral.cluster.mdf.OrgReplicaPlacementScheme;
import com.integral.cluster.mdf.VenueOrgChangeHandler;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.fx.FXRateBasis;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.MDFConfigMBeanC;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.provision.VenueConfiguration;
import com.integral.services.cluster.rebalancer.ConstraintAwareDynamicPartitionRebalancer;
import com.integral.services.cluster.rebalancer.strategy.ConstraintAwareAutoRebalanceStrategy;
import com.integral.services.cluster.util.PropertyConstants;
import com.integral.tradingvenue.TradingVenueEntity;
import com.integral.tradingvenue.TradingVenueRelationShip;
import com.integral.user.Organization;
import org.apache.helix.*;
import org.apache.helix.api.Cluster;
import org.apache.helix.api.Participant;
import org.apache.helix.api.State;
import org.apache.helix.api.id.ParticipantId;
import org.apache.helix.api.id.PartitionId;
import org.apache.helix.api.id.ResourceId;
import org.apache.helix.controller.context.ControllerContextProvider;
import org.apache.helix.controller.rebalancer.config.RebalancerConfig;
import org.apache.helix.controller.rebalancer.util.ConstraintBasedAssignment;
import org.apache.helix.controller.stages.ResourceCurrentState;
import org.apache.helix.controller.strategy.AutoRebalanceStrategy;
import org.apache.helix.model.IdealState;
import org.apache.helix.model.ResourceAssignment;
import org.apache.helix.model.StateModelDefinition;
import org.apache.helix.store.zk.ZkHelixPropertyStore;
import org.apache.zookeeper.data.Stat;

import java.util.*;

import static com.integral.mdf.MDFConfigMBean.MDF_PARTITION_TYPE_BOOK;
import static com.integral.mdf.MDFConfigMBean.MDF_PARTITION_TYPE_ORG;
import static com.integral.tradingvenue.TradingVenueClassificationEnums.VenueType.CLOB;

/**
 * Created by raghunathans on 6/8/17.
 * @deprecated do not use
 */
public class MDFClusterRebalancerV2 extends ConstraintAwareDynamicPartitionRebalancer {

    Log LOG = LogFactory.getLog(this.getClass());

    public static final String book_delimiter = "/" ;

    private VenueOrgChangeHandler instance = VenueOrgChangeHandler.getInstance();

    public static final String SEPARATOR = "_";


    public MDFClusterRebalancerV2(){
        //Include the trigger for the property change listener
        MDFConfigMBeanC.getInstance().setConfigChangeHandler(instance);
    }

   @Override
    public void init(HelixManager helixManager, ControllerContextProvider contextProvider) {
        super.init(helixManager,contextProvider);
    }

    @Override
    public Set<String> getPartitions(String venueName) {
        String pType = MDFConfigMBeanC.getInstance().getMDFPartitionType(venueName);
        if(MDF_PARTITION_TYPE_ORG.equals(pType)) {
            return getMembers(venueName);
        }else if(MDF_PARTITION_TYPE_BOOK.equals(pType)){
            Set<String> members = getMembers(venueName);
            Collection<String> ccypairs = getCurrencyPairs(venueName);
            Set<String> partitions = new LinkedHashSet<String>();
            for(String member : members){
                for(String ccyp:ccypairs){
                    partitions.add(member+book_delimiter+ccyp);
                }
            }
            return partitions;
        }
        return Collections.emptySet();
    }


    protected AutoRebalanceStrategy getAutoRebalanceStrategy(IdealState idealState, List<PartitionId> partitions, LinkedHashMap<State, Integer> stateCountMap, int maxPartition) {
        String pType = MDFConfigMBeanC.getInstance().getMDFPartitionType(this.resourceName);
        if(MDF_PARTITION_TYPE_ORG.equals(pType)) {
            return new AutoRebalanceStrategy(idealState.getResourceId(), partitions, stateCountMap,
                    maxPartition, getReplicaPlacementScheme());
        }
        return new MDFClusterRebalancerStrategy(idealState.getResourceId(), partitions, stateCountMap, maxPartition, this.getReplicaPlacementScheme());
    }

    protected ConstraintAwareAutoRebalanceStrategy.NamedPartitionReplicaPlacementScheme getReplicaPlacementScheme() {
        String pType = MDFConfigMBeanC.getInstance().getMDFPartitionType(this.resourceName);
        if(MDF_PARTITION_TYPE_ORG.equals(pType)) {
            return new OrgReplicaPlacementScheme();
        }

        //TODO - evaluate properties and if no service levels are defined then fall back to OrgReplicaPlacementScheme;

        ConstraintAwareAutoRebalanceStrategy.NamedPartitionReplicaPlacementScheme scheme = this.getNamedPartitionReplicaPlacementScheme();
        scheme.init(this.helixManager, this.resourceName);
        return scheme;
    }

    protected ConstraintAwareAutoRebalanceStrategy.NamedPartitionReplicaPlacementScheme getNamedPartitionReplicaPlacementScheme(){
        return new FixedNodeReplicaPlacementScheme();
    }

    /**
     * @return true if organization is not ExternalProvider, ECN, Broker, Masked LP, ClearingHouse, PrimeBroker.
     */
    public static boolean isCustomerOrganization(Organization organization)
    {
        return !( organization.isExternalProvider() || organization.isECN() || organization.isBroker() || organization.isMasked() || organization.isClearingHouse() || organization.isPrimeBroker() );
    }


    Set<String> getMembers(String venueName) {

        Set<String> memberList = new LinkedHashSet<String>();
        Organization tvOrg = OrganizationUtil.getOrganization(venueName);

        if(tvOrg==null){
            throw new IllegalStateException("Invalid Venue name ,no organization found for the venue name:"+venueName);
        }

        Collection<TradingVenueRelationShip> tvCustomersRelations = tvOrg.getTvCustomersRelations();

        if(tvCustomersRelations != null){
            for(TradingVenueRelationShip tradingVenueRelationShip : tvCustomersRelations){
                Organization customerOrg = tradingVenueRelationShip.getCustomerOrg();
                if( null != customerOrg
                        && isCustomerOrganization(customerOrg)
                        && MDFConfigMBeanC.getInstance().getMDFEnabledFI(venueName).contains(customerOrg.getShortName()) )
                {
                    memberList.add(customerOrg.getShortName());
                }
            }
        }

        return memberList;
    }


    Collection<String> getCurrencyPairs(String venueOrg){

        TradingVenueEntity tradingVenue = (TradingVenueEntity) ReferenceDataCacheC.getInstance()
                .getEntityByShortName(venueOrg, TradingVenueEntity.class, null, null);

        if (tradingVenue == null || tradingVenue.getVenueType().getId() != CLOB.getId()) {
            Collections.emptyList();
        }

        Map<String, VenueConfiguration> ccyVenueMap = new HashMap<String, VenueConfiguration>();
       // ccyVenueMap.put(VenueConfiguration.DEFAULT_CONFIG, null);
        if(tradingVenue.getSupportedCurrencyPairs() != null){
            Collection<CurrencyPairGroup> currencyPairGroups = tradingVenue.getSupportedCurrencyPairs().getCurrencyPairGroups();
            if(currencyPairGroups != null) {
                for (CurrencyPairGroup currencyPairGroup : currencyPairGroups) {
                    Collection<CurrencyPair> currencyPairs = currencyPairGroup.getCurrencyPairs();
                    if(currencyPairs != null) {
                        for (CurrencyPair currencyPair : currencyPairs) {
                            ccyVenueMap.put(currencyPair.getDisplayName(), null);
                        }
                    }
                }
            }

            Collection<FXRateBasis> includedFXRateBasis = tradingVenue.getSupportedCurrencyPairs().getIncludedFXRateBasis();
            if(includedFXRateBasis != null) {
                for (FXRateBasis fxRateBasis : includedFXRateBasis) {
                    ccyVenueMap.put(fxRateBasis.getCurrencyPairString(),null);
                }
            }
        }

        return ccyVenueMap.keySet();
    }

    @Override
    public ResourceAssignment computeResourceMapping(IdealState idealState,
                                                     RebalancerConfig rebalancerConfig, ResourceAssignment prevAssignment, Cluster cluster,
                                                     ResourceCurrentState currentState) {
        boolean isEnabled = (idealState != null) ? idealState.isEnabled() : true;
        StateModelDefinition stateModelDef =
                cluster.getStateModelMap().get(idealState.getStateModelDefId());

        //Custom-logic-begin
        ///Bring in the custom partitions here
        ZkHelixPropertyStore<ZNRecord> helixPropertyStore = helixManager
                .getHelixPropertyStore();

        ResourceId resourceId = idealState.getResourceId();
        ZNRecord znRecord = helixPropertyStore.get(PropertyConstants.PARTITION_MAPPING_PATH
                + resourceId, new Stat(), AccessOption.PERSISTENT);

        List<PartitionId> partitions = getCurrentPartitions(znRecord);
        //Custom-logic-end

        // Compute a preference list based on the current ideal state
        //List<PartitionId> partitions = new ArrayList<PartitionId>(idealState.getPartitionIdSet());
        Map<ParticipantId, Participant> liveParticipants = cluster.getLiveParticipantMap();
        Map<ParticipantId, Participant> allParticipants = cluster.getParticipantMap();
        int replicas = -1;
        String replicaStr = idealState.getReplicas();
        if (replicaStr.equals(HelixConstants.StateModelToken.ANY_LIVEINSTANCE.toString())) {
            replicas = liveParticipants.size();
        } else {
            replicas = Integer.valueOf(replicaStr);
        }

        // count how many replicas should be in each state
        Map<State, String> upperBounds =
                ConstraintBasedAssignment.stateConstraints(stateModelDef, idealState.getResourceId(),
                        cluster.getConfig());
        LinkedHashMap<State, Integer> stateCountMap =
                ConstraintBasedAssignment.stateCount(upperBounds, stateModelDef, liveParticipants.size(),
                        replicas);

        // get the participant lists
        List<ParticipantId> liveParticipantList =
                new ArrayList<ParticipantId>(liveParticipants.keySet());
        List<ParticipantId> allParticipantList =
                new ArrayList<ParticipantId>(cluster.getParticipantMap().keySet());

        // compute the current mapping from the current state
        Map<PartitionId, Map<ParticipantId, State>> currentMapping =
                currentMapping(idealState, currentState, stateCountMap);

        //Custom-logic-begin
        Map<PartitionId, Map<ParticipantId, State>> currentMappingOrgnl =
                currentMapping(idealState, currentState, stateCountMap);

        Set<PartitionId> toRemovePartitions = new HashSet<PartitionId>();
        for (Map.Entry<PartitionId, Map<ParticipantId, State>> entry : currentMapping.entrySet()) {
            if(!partitions.contains(entry.getKey())){
                toRemovePartitions.add(entry.getKey());
            }
        }

        for (PartitionId partitionId : toRemovePartitions) {
            currentMapping.remove(partitionId);
        }
        //Custom-logic-end

        // If there are nodes tagged with resource, use only those nodes
        // If there are nodes tagged with resource name, use only those nodes
        Set<ParticipantId> taggedNodes = new HashSet<ParticipantId>();
        Set<ParticipantId> taggedLiveNodes = new HashSet<ParticipantId>();
        if (idealState.getInstanceGroupTag() != null) {
            for (ParticipantId participantId : allParticipantList) {
                if (cluster.getParticipantMap().get(participantId).hasTag(idealState.getInstanceGroupTag())) {
                    taggedNodes.add(participantId);
                    if (liveParticipants.containsKey(participantId)) {
                        taggedLiveNodes.add(participantId);
                    }
                }
            }
            if (!taggedLiveNodes.isEmpty()) {
                // live nodes exist that have this tag
                if (LOG.isDebugEnabled()) {
                    LOG.debug("found the following participants with tag " + idealState.getInstanceGroupTag()
                            + " for " + idealState.getResourceId() + ": " + taggedLiveNodes);
                }
            } else if (taggedNodes.isEmpty()) {
                // no live nodes and no configured nodes have this tag
                LOG.warn("Resource " + idealState.getResourceId() + " has tag "
                        + idealState.getInstanceGroupTag() + " but no configured participants have this tag");
            } else {
                // configured nodes have this tag, but no live nodes have this tag
                LOG.warn("Resource " + idealState.getResourceId() + " has tag "
                        + idealState.getInstanceGroupTag() + " but no live participants have this tag");
            }
            allParticipantList = new ArrayList<ParticipantId>(taggedNodes);
            liveParticipantList = new ArrayList<ParticipantId>(taggedLiveNodes);
        }

        // determine which nodes the replicas should live on
        int maxPartition = idealState.getMaxPartitionsPerInstance();
        if (LOG.isDebugEnabled()) {
            LOG.debug("currentMapping: " + currentMapping);
            LOG.debug("stateCountMap: " + stateCountMap);
            LOG.debug("liveNodes: " + liveParticipantList);
            LOG.debug("allNodes: " + allParticipantList);
            LOG.debug("maxPartition: " + maxPartition);
        }

        AutoRebalanceStrategy algorithm = getAutoRebalanceStrategy(idealState, partitions, stateCountMap,maxPartition);

        ZNRecord newMapping =
                algorithm.typedComputePartitionAssignment(liveParticipantList, currentMapping,allParticipantList,stateModelDef);




        // compute a full partition mapping for the resource
        if (LOG.isDebugEnabled()) {
            LOG.debug("Processing resource:" + idealState.getResourceId());
        }
        ResourceAssignment partitionMapping = new ResourceAssignment(idealState.getResourceId());
        Map<PartitionId,Map<ParticipantId, State>> mappings = new HashMap<PartitionId, Map<ParticipantId,State>>();
        for (PartitionId partition : partitions) {
            Set<ParticipantId> disabledParticipantsForPartition =
                    ConstraintBasedAssignment.getDisabledParticipants(allParticipants, partition);
            List<String> rawPreferenceList = newMapping.getListField(partition.stringify());
            if (rawPreferenceList == null) {
                rawPreferenceList = Collections.emptyList();
            }
            List<ParticipantId> preferenceList =
                    Lists.transform(rawPreferenceList, new Function<String, ParticipantId>() {
                        @Override
                        public ParticipantId apply(String participantName) {
                            return ParticipantId.from(participantName);
                        }
                    });
            preferenceList =
                    ConstraintBasedAssignment.getPreferenceList(cluster, partition, preferenceList);
            Map<ParticipantId, State> bestStateForPartition =
                    ConstraintBasedAssignment.computeAutoBestStateForPartition(upperBounds,
                            liveParticipants.keySet(), stateModelDef, preferenceList,
                            currentState.getCurrentStateMap(idealState.getResourceId(), partition),
                            disabledParticipantsForPartition, isEnabled);
            partitionMapping.addReplicaMap(partition, bestStateForPartition);
            mappings.put(partition, bestStateForPartition);
        }

        for (PartitionId partition : toRemovePartitions) {
            Map<ParticipantId, State> map = currentMappingOrgnl.get(partition);
            if(null!=map){
                Map<ParticipantId, State> newStates= new HashMap<ParticipantId, State>();
                for (Map.Entry<ParticipantId, State> states : map.entrySet()) {
                    newStates.put(states.getKey(), State.from(HelixDefinedState.DROPPED));
                }
                partitionMapping.addReplicaMap(partition, newStates);
                if(!map.isEmpty()){
                    mappings.put(partition, newStates);
                }
            }
        }

        LOG.info("Current Partition Assignment:" + mappings);

        if(LOG.isDebugEnabled()){
            LOG.info("Complete Partition Assignment:" + partitionMapping.toString());
        }

        return partitionMapping;
    }

    private Map<PartitionId, Map<ParticipantId, State>> currentMapping(IdealState idealState,
                                                                       ResourceCurrentState currentStateOutput, Map<State, Integer> stateCountMap) {
        Map<PartitionId, Map<ParticipantId, State>> map =
                new HashMap<PartitionId, Map<ParticipantId, State>>();

        for (PartitionId partition : idealState.getPartitionIdSet()) {
            Map<ParticipantId, State> curStateMap =
                    currentStateOutput.getCurrentStateMap(idealState.getResourceId(), partition);
            map.put(partition, new HashMap<ParticipantId, State>());
            for (ParticipantId node : curStateMap.keySet()) {
                State state = curStateMap.get(node);
                if (stateCountMap.containsKey(state)) {
                    map.get(partition).put(node, state);
                }
            }

            Map<ParticipantId, State> pendingStateMap =
                    currentStateOutput.getPendingStateMap(idealState.getResourceId(), partition);
            for (ParticipantId node : pendingStateMap.keySet()) {
                State state = pendingStateMap.get(node);
                if (stateCountMap.containsKey(state)) {
                    map.get(partition).put(node, state);
                }
            }
        }
        return map;
    }

    private List<PartitionId> getCurrentPartitions(ZNRecord znRecord) {
        List<PartitionId> aPartitions = new ArrayList<PartitionId>();
        if (null != znRecord) {
            Map<String, String> mapField = znRecord.getMapField(PropertyConstants.ACTIVE_PARTITIONS);
            if (null != mapField) {
                for (Map.Entry<String, String> partitions : mapField.entrySet()) {
                    if (null != partitions.getValue()) {
                        aPartitions.add(PartitionId.from(partitions.getKey()));
                    }
                }
            }
        }
        return aPartitions;
    }

}
