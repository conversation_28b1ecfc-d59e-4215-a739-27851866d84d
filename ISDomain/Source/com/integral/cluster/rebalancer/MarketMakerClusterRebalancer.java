package com.integral.cluster.rebalancer;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.services.cluster.rebalancer.DynamicPartitionRebalancer;
import org.apache.helix.HelixManager;
import org.apache.helix.controller.context.ControllerContextProvider;

import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

public class MarketMakerClusterRebalancer extends DynamicPartitionRebalancer {

    Log log = LogFactory.getLog(MarketMakerClusterRebalancer.class);

    protected MarketMakerConfig.MMConfigChangeHandler mmConfigChangeHandler = new MarketMakerConfig.MarketMakerEnabledOrgChangeHandler();

    public MarketMakerClusterRebalancer(){
        MarketMakerConfig.getInstance().setPropertyChangeHandler(mmConfigChangeHandler);
    }

    @Override
    public void init(HelixManager helixManager, ControllerContextProvider contextProvider) {
        super.init(helixManager,contextProvider);
    }

    @Override
    public Set<String> getPartitions(String resourceName) {
        MarketMakerConfig mmConfig = MarketMakerConfig.getInstance();
        List<String> mmEnabledOrgs = mmConfig.getMarketMakerEnabledBrokers();

        if(mmEnabledOrgs==null || mmEnabledOrgs.size()==0){
            if(log.isDebugEnabled()){
                log.debug("MarketMakerClusterRebalancer.getPartitions : No MM enabled Broker Orgs found!");
            }
            return Collections.emptySet();
        }

        if(log.isDebugEnabled()){
            log.debug("MarketMakerClusterRebalancer.getPartitions : MM Enabled orgs:"+mmEnabledOrgs);
        }
        return new LinkedHashSet<String>(mmEnabledOrgs);
    }

}
