package com.integral.cluster;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.ReferenceEntity;
import com.integral.model.cluster.ClusterMetaData;
import com.integral.rds.client.ClientFactory;
import com.integral.rds.client.QueryIterator;
import com.integral.rds.client.ReferenceDataService;
import com.integral.rds.client.ReferenceDataStoreException;
import com.integral.rds.exception.ReferenceDataServiceException;
import com.integral.rds.message.Query;
import com.integral.rds.message.QueryBuilder;
import com.integral.rds.notification.NotificationHandler;
import com.integral.rds.notification.NotificationObserver;
import com.integral.rds.persistence.QueryService;
import com.integral.services.ServiceContainerMBean;

import java.util.*;

public class ClusterConfigDataService {

    public static final String CLUSTER_NAMESPACES = "cNs";
    protected Log log = LogFactory.getLog(this.getClass());

    private static ClusterConfigDataService dataService = new ClusterConfigDataService();

    private ClusterConfigDataService(){

    }

    public static ClusterConfigDataService getInstance() {
        return dataService;
    }

    public ClusterMetaData getClusterMetaData(String effectiveClusterName) {
        try {
            return (ClusterMetaData) getReferenceDataService().retrieveById(
                    ClusterMetaData.class, effectiveClusterName,
                    ClusterMetaData.NAMESPACE);
        } catch (Exception e) {
            log.error(
                    "Error in retrieving cluster config meta data with effectiveClusterName:"
                            + effectiveClusterName, e);
        }
        return null;
    }

    public List<ClusterMetaData> getAllClusterMetaData() {

        List<ClusterMetaData> clustersMetaData = new ArrayList<ClusterMetaData>();
        Map<String, String> queryMap = new HashMap<String, String>();
        queryMap.put(QueryService.NAMESPACE_FIELD , ClusterMetaData.NAMESPACE);
        queryMap.put(CLUSTER_NAMESPACES , ServiceContainerMBean.getInstance().getServicesNamespace());

        QueryIterator<? extends ReferenceEntity> iterator = getObjectsByQuery(
                ClusterMetaData.NAMESPACE, ClusterMetaData.class,queryMap);

        try {
            while (iterator.hasNext()) {
                clustersMetaData.add((ClusterMetaData) iterator.next());
            }
        } catch (ReferenceDataServiceException e) {
            log.error("Error in retrieving cluster config meta data", e);
        }

        return clustersMetaData;
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    private QueryIterator<? extends ReferenceEntity> getObjectsByQuery(
            String namespace, Class type, Map<String, String> queryMap) {
        QueryBuilder builder = new QueryBuilder(type);
        for (Map.Entry<String, String> entry : queryMap.entrySet()) {
            builder.addStringParam(entry.getKey(), entry.getValue());
        }
        try {
            Query queryParams = builder.build();
            return getReferenceDataService().getIterator(type, namespace,
                    queryParams);
        } catch (Exception e) {
            log.error("Error in retrieving object of type:" + type
                    + " by query:" + queryMap, e);
            throw new ClusterConfigDataException(e);
        }
    }

    public ClusterMetaData getClusterMetaData(String namespace, String id) {
        try {
            return (ClusterMetaData) getReferenceDataService().retrieveById (ClusterMetaData.class, id,namespace, ReferenceDataService.ReadPreference.SERVER);
        } catch (Exception e) {
            log.error("Error in retrieving object of type:" + ClusterMetaData.class
                    + " by id:" + id+ " namespace:"+namespace, e);
            throw new ReferenceDataStoreException(e);
        }
    }

    public void registerListener(NotificationObserver observer){
        try {
            getNotificationHandler().addObserver(ClusterMetaData.class,ClusterMetaData.NAMESPACE,observer);
        } catch (Exception e) {
            log.error("Error in adding observer type:ClusterMetaData", e);
            throw new ReferenceDataStoreException(e);
        }
    }

    protected ReferenceDataService getReferenceDataService() {
        ReferenceDataService referenceDataService = ClientFactory.getFactory()
                .getReferenceDataService();
        if (null == referenceDataService) {
            String message = "getReferenceDataService():Reference data client service is not initialized";
            log.error(message);
            throw new ClusterConfigDataException(message);
        }
        return referenceDataService;
    }

    private NotificationHandler getNotificationHandler() {
        NotificationHandler notificationHandler = ClientFactory.getFactory().getNotificationHandler();
        if (null == notificationHandler) {
            String message = "getNotificationHandler():Reference data client service is not initialized";
            log.error(message);
            throw new ReferenceDataStoreException(message);
        }
        return notificationHandler;
    }
}
