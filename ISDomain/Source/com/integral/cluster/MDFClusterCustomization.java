package com.integral.cluster;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.cluster.ClusterMetaData;
import com.integral.model.cluster.ResourceInfo;
import com.integral.services.cluster.CustomMetaDataProvider;
import com.integral.system.configuration.ConfigurationFactory;

import java.util.List;

/**
 * Created by raghunathans on 7/14/17.
 */
public class MDFClusterCustomization implements CustomMetaDataProvider {

    public static final String IDC_MDF_CLUSTERS_VENUE_NAME_PREFIX = "IDC.MDFClusters.Venue.Name.";
    private static Log log = LogFactory.getLog(MDFClusterCustomization.class);

    public void updateMetaData(ClusterMetaData clusterMetaData) {
        String clusterName = clusterMetaData.getClusterName();
        List<ResourceInfo> templateResources = clusterMetaData.getResources();
        if(templateResources == null || templateResources.isEmpty()){
            log.warn("Failed to update the resource name for the cluster :"+clusterName);
            return;
        }

        String propertyName = IDC_MDF_CLUSTERS_VENUE_NAME_PREFIX + clusterName;
        String mappingVenue = ConfigurationFactory.getServerMBean().getStringProperty(propertyName, null);
        if(null != mappingVenue)  {
            ResourceInfo resourceInfo = templateResources.get(0);
            resourceInfo.setName(mappingVenue);
        }else{
            log.warn("Failed to update the resource name for the cluster :"+clusterName+ ", no mapping venue name found, no property set for :"+propertyName);
        }
    }
}
