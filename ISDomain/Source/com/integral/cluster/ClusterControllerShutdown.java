package com.integral.cluster;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.services.ServiceContainerMBean;
import com.integral.services.cluster.controller.ClusterControllerService;
import com.integral.system.runtime.ShutdownTask;

import java.util.Hashtable;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 1/7/16
 * Time: 2:12 PM
 * To change this template use File | Settings | File Templates.
 */
public class ClusterControllerShutdown implements ShutdownTask {

    private final Log log = LogFactory.getLog(this.getClass());
    @Override
    public String shutdown(String aName, Hashtable args) {
        try {
            ServiceContainerMBean instance = ServiceContainerMBean.getInstance();
            if (instance.isClusterControllerEnabled()) {
                log.info("Stopping Cluster Controllers...");
                boolean stopped = ClusterControllerService.getInstance().stop();
                if (!stopped) {
                    throw new RuntimeException("ClusterController shutdown failed");
                }
            }
        } catch (Exception ex) {
            throw new RuntimeException("ClusterController shutdown failed", ex);
        }
        return null;
    }
}
