package com.integral.cluster;

import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;

import com.integral.is.ISCommonConstants;
import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.cluster.ClusterMetaData;
import com.integral.model.cluster.ResourceInfo;
import com.integral.services.ServiceContainerMBean;
import com.integral.services.cluster.controller.ClusterControllerService;
import com.integral.spaces.config.MetaspacesConfigMBean;
import com.integral.spaces.config.NSCustomRebalancer;
import com.integral.system.runtime.StartupTask;

/**
 * Created with IntelliJ IDEA. User: raghunathans Date: 1/7/16 Time: 2:01 PM To
 * change this template use File | Settings | File Templates.
 */
public class ClusterControllerStartupC implements StartupTask {
	private final Log log = LogFactory.getLog(this.getClass());

	protected static MessageLogger messageLogger = MessageLogger.getInstance();

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.integral.system.runtime.StartupTask#startup(java.lang.String,
	 * java.util.Hashtable)
	 */
	@Override
	public String startup(String aName, Hashtable args) throws Exception {

		try {
			ServiceContainerMBean instance = ServiceContainerMBean
					.getInstance();
			if (instance.isClusterControllerEnabled()) {	

				log.info("Starting Cluster Controllers....");
				ClusterConfigDataService configDataService = ClusterConfigDataService
						.getInstance();
				List<ClusterMetaData> allClusterMetaData = configDataService
						.getAllClusterMetaData();
				
				boolean isStarted = ClusterControllerService.getInstance()
						.start(instance, allClusterMetaData);
				if (!isStarted) {
					messageLogger
							.log(ISCommonConstants.CLUSTER_CONTROLLER_STARTUP_FAILED,
									ClusterControllerStartupC.class.getName(),
									"Failed to start all the cluster controllers",
									allClusterMetaData.toString());
					throw new Exception("Failed to start all the cluster controllers");
				}

                configDataService.registerListener(new ClusterMetaDataChangeHandler());
			}
		} catch (Exception e) {
			log.error("Error starting the cluster controller startup", e);
			messageLogger.log(
					ISCommonConstants.CLUSTER_CONTROLLER_STARTUP_FAILED,
					ClusterControllerStartupC.class.getName(),
					"Cluster Controller server startup failed",
					"Exception in ClusterControllerStartupC startup");
			throw e;
		}
		return null;
	}
}
