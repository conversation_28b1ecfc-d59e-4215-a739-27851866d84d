package com.integral.messaging;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import com.integral.is.common.mbean.ISFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.messaging.config.model.ClusterConfiguration;
import com.integral.model.ReferenceEntity;
import com.integral.rds.client.ClientFactory;
import com.integral.rds.client.ReferenceDataService;
import com.integral.rds.client.ReferenceDataService.ReadPreference;
import com.integral.rds.exception.ReferenceDataServiceException;
import com.integral.rds.message.Query;
import com.integral.rds.message.QueryBuilder;
import com.integral.rds.persistence.PersistenceService.UpdateValidationType;
import com.integral.rds.persistence.QueryService;

public class MessagingService {

	private static Log logger = LogFactory.getLog(MessagingService.class);

	private static final long TIMEOUT = 10;
	private static final TimeUnit UNIT = TimeUnit.SECONDS;
	private static final String NAMESPACE = "main";
	private static final String USERNAME = "integral";
	private static final String PASSWORD = "integral";

	private static ReferenceDataService rds = ClientFactory.getFactory()
			.getReferenceDataService();

	public static ClusterConfiguration updateClusterAddress(String shortName,
			String clusterAddress) {
		logger.info("Updating cluster:" + shortName + ":new address:"
				+ clusterAddress);

		ClusterConfiguration newClusterConfig = null;
		try {
			ClusterConfiguration clusterConfig = (ClusterConfiguration) rds
					.retrieveById(ClusterConfiguration.class, shortName,
							NAMESPACE, ReadPreference.SERVER);
			logger.info("Updating cluster:" + shortName);
			logger.info("Old details:cluster address:"
					+ clusterConfig.getClusterAddress() + ":recoveryInterval:"
					+ clusterConfig.getRecoveryInterval()
					+ ", new details:cluster address:" + clusterAddress);

			clusterConfig.setClusterAddress(clusterAddress);
			//clusterConfig.setRecoveryInterval(recoveryInterval);

			newClusterConfig = (ClusterConfiguration) rds.update(clusterConfig,
					UpdateValidationType.STRICT);
			logger.info("Update successful");

		} catch (ReferenceDataServiceException e) {
			logger.error(e.getMessage(), e);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return newClusterConfig;
	}

	public static List<ClusterConfiguration> getAllClusterConfigurations() {
		if (ISFactory.getInstance().getISMBean().isRDSClientEnabled()) {
			try {

				Query query = new QueryBuilder(ClusterConfiguration.class)
						.addStringParam(QueryService.NAMESPACE_FIELD, NAMESPACE)
						.build();

				Future<List<? extends ReferenceEntity>> result = rds.retrieve(
						ClusterConfiguration.class, NAMESPACE, query, null);

				List<? extends ReferenceEntity> referenceEntities = result.get(
						TIMEOUT, UNIT);
				if (referenceEntities != null) {
					return (List<ClusterConfiguration>) referenceEntities;
				}
				return new ArrayList<ClusterConfiguration>();
			} catch (ReferenceDataServiceException e) {
				logger.error(e.getMessage(), e);
			} catch (InterruptedException e) {
				logger.error(e.getMessage(), e);
			} catch (ExecutionException e) {
				logger.error(e.getMessage(), e);
			} catch (TimeoutException e) {
				logger.error(e.getMessage(), e);
			}
		}
		return new ArrayList<ClusterConfiguration>();
	}

}
