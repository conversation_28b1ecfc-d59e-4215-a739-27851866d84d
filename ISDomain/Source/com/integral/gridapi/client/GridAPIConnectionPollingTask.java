package com.integral.gridapi.client;

import com.integral.gridapi.client.message.*;
import com.integral.gridapi.client.parser.IMessageParser;
import com.integral.gridapi.client.parser.MessageParserC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.configuration.ConfigurationFactory;

import javax.xml.parsers.ParserConfigurationException;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.UnknownHostException;
import java.util.List;

public class GridAPIConnectionPollingTask implements Runnable {

    private final GridAPIConnection connection;
    private Log log;
    IMessageParser messageParser;

    public GridAPIConnectionPollingTask(GridAPIConnection connection) {
        this.connection = connection;
        log = LogFactory.getLog(GridAPIConnectionPollingTask.class.getName() + "-" + connection.getOrgName());

    }

    @Override
    public void run() {
        try {
            if (!ConfigurationFactory.getServerMBean().isApplicationStarted()) {
                return;
            }
            if (messageParser == null) {
                try {
                    messageParser = new MessageParserC();
                } catch (ParserConfigurationException e) {
                    e.printStackTrace();
                    return;
                }
            }

            if (connection.checkConnection()) {
                PolledMessage message = pollMessage();

                if (message != null) {
                    processPolledMessage(message);
                }
            }
        }
        catch (UnknownHostException e){
            log.warn("run : Failed in running task due to unknown host" );
            if(log.isDebugEnabled()){
                log.error("run : Error in running task due to unknown host", e);
            }
        }
        catch (Exception e) {
            log.warn("run : Failed in running task" );
            if(log.isDebugEnabled()){
                log.error("run : Error in running task", e);
            }
        }
    }

    public PolledMessage pollMessage() throws Exception {
        if (connection.getTradeNotificationHandler() != null && !connection.isTradeNotificationSubscribed()) {
            log.info("run : TradeNotificationHandler is set but not yet subscribed, hence subscribing to TradeNotifications");
            boolean tradeSubscription = connection.subscribeForTradeNotifications();
            connection.setTradeNotificationSubscribed(tradeSubscription);
        }
        if (connection.getRateHandler() != null) {
            connection.processPendingSubscriptions();
        }
        PolledMessage message = connection.pollForMessages();

        if (message == null || message.getStatus().equals(PolledMessage.STATUS.UNKNOWN_EXCEPTION)) {
            log.info("Unknown exception from server,returning and trying again");
            return null;
        }
        if (!message.getStatus().equals(PolledMessage.STATUS.SUCCESS)) {
            log.info("run : Poll request failed");
            return null;
            //connection.reset();
        }
        return message;
    }

    private void processPolledMessage(PolledMessage message) {
        try {
            String messageString = message.getMessage();
            if (messageString == null) {
                if (log.isDebugEnabled()) {
                    log.debug("run : Message String is null");
                }
                return;
            }
            if (log.isDebugEnabled()) {
                log.debug(messageString);
            }
            InputStream stream = new ByteArrayInputStream(messageString.getBytes());
            List<ParsedMessage> messages = messageParser.parseMessages(stream);
            if( messages == null || messages.isEmpty() ){
                return;
            }
            for (ParsedMessage m : messages) {
                if (m.getType() != null) {
                    switch (m.getType()) {
                        case RATE:
                            if (connection.getRateHandler() != null) {
                                connection.getRateHandler().handle((MultitierRateMessage) m);
                            }
                            break;
                        case TRADE:
                            if (connection.getTradeNotificationHandler() != null) {
                                TradeNotificationMessage tm = (TradeNotificationMessage) m;
                                String ackId = tm.getAckId();
                                if (ackId != null) {
                                    connection.addPendingAckId(ackId);
                                }
                                connection.getTradeNotificationHandler().handle(tm);
                            } else {
                                if (log.isDebugEnabled()) {
                                    log.debug("run : TradeHandler is NULL, ignoring trade message");
                                }
                            }
                            break;
                        case DO_NO_PROCESS: {
                            DoNotProcessMessage doNotProcessMsg = (DoNotProcessMessage) m;
                            String ackId = doNotProcessMsg.getAckId();
                            if (ackId != null) {
                                connection.addPendingAckId(ackId);
                            }
                            break;
                        }
                        default:
                            log.warn("Unidentified event type");
                            break;
                    }
                }

            }
        }
        catch (Exception ex) {
            log.warn("run : Failed to process message "+message);
            if( log.isDebugEnabled() ){
                log.error("run : Failed to process message "+message,ex);
            }
        }
    }
}
