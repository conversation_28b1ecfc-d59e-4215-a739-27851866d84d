package com.integral.gridapi.client.message;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 2/10/15
 * Time: 10:37 AM
 * To change this template use File | Settings | File Templates.
 */
public class ResponseMessage {

    private String data;
    private String errorMessage;
    private Status status;
    private int responseCode;

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public int getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(int responseCode) {
        this.responseCode = responseCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }


    @Override
    public String toString() {
        return "ResponseMessage{" +
                "responseCode=" + responseCode +
                ", status=" + status +
                ", data='" + data + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }

    public enum Status{
        FAILED,
        SUCCESS,
        REJECTED,
        UNAUTHORIZED,
        UNKNOWN_EXCEPTION;
    }
}
