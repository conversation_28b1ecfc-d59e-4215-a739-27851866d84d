package com.integral.gridapi.client.message;

public class RateData
{
    private double quantity;
    private String provider;
    private double rate;
    private TYPE type;

    public double getQuantity()
    {
        return quantity;
    }

    public void setQuantity(double quantity)
    {
        this.quantity = quantity;
    }

    public String getProvider()
    {
        return provider;
    }

    public void setProvider(String provider)
    {
        this.provider = provider;
    }

    public double getRate()
    {
        return rate;
    }

    public void setRate(double rate)
    {
        this.rate = rate;
    }

    public TYPE getType()
    {
        return type;
    }

    public void setType(TYPE type)
    {
        this.type = type;
    }

    @Override
    public String toString()
    {
        StringBuilder builder = new StringBuilder();
        builder.append("RateData [quantity=");
        builder.append(quantity);
        builder.append(", provider=");
        builder.append(provider);
        builder.append(", rate=");
        builder.append(rate);
        builder.append(", type=");
        builder.append(type);
        builder.append("]");
        return builder.toString();
    }

    public enum TYPE
    {
        BID,
        OFFER
    }
}
