/**
 * 
 */
package com.integral.gridapi.client.message;

public class PolledMessage
{
    private String message;
    private STATUS status;
    private int responseCode;
    
    public String getMessage()
    {
        return message;
    }
    public void setMessage(String message)
    {
        this.message = message;
    }
    public STATUS getStatus()
    {
        return status;
    }
    public void setStatus(STATUS status)
    {
        this.status = status;
    }
    public int getResponseCode()
    {
        return responseCode;
    }
    public void setResponseCode(int responseCode)
    {
        this.responseCode = responseCode;
    }
    
    public enum STATUS
    {
        FAILED,
        SUCCESS,
        REJECTED,
        UNKNOWN_EXCEPTION
    }
}
