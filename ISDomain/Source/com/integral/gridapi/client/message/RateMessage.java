package com.integral.gridapi.client.message;

import com.integral.is.message.MarketRate;

public class RateMessage implements ParsedMessage
{
    private String ackId;
    private String jmsMessageId;
    private String requestId;
    private String currencyPair;
    private RateData bidRateData;
    private RateData offerRateData;
    private MarketRate marketRate;
    private long timestamp;

    private String organization;

    public String getRequestId()
    {
        return requestId;
    }

    public void setRequestId(String requestId)
    {
        this.requestId = requestId;
    }

    public String getCurrencyPair()
    {
        return currencyPair;
    }

    public void setCurrencyPair(String currencyPair)
    {
        this.currencyPair = currencyPair;
    }

    public String getLPOrganization()
    {
        return getBidRateData() != null ? getBidRateData().getProvider() : null;
    }

    public long getTimestamp()
    {
        return timestamp;
    }

    public void setTimestamp(long timestamp)
    {
        this.timestamp = timestamp;
    }

    public RateData getBidRateData()
    {
        return bidRateData;
    }

    public void setBidRateData(RateData bidRateData)
    {
        this.bidRateData = bidRateData;
    }

    public RateData getOfferRateData()
    {
        return offerRateData;
    }

    public void setOfferRateData(RateData offerRateData)
    {
        this.offerRateData = offerRateData;
    }

    public MarketRate getMarketRate()
    {
        return marketRate;
    }

    public void setMarketRate(MarketRate marketRate)
    {
        this.marketRate = marketRate;
    }

    public double getBidRate()
    {
        return (this.getBidRateData() != null) ? this.getBidRateData().getRate() : 0.0;
    }

    public double getOfferRate()
    {
        return (this.getOfferRateData() != null) ? this.getOfferRateData().getRate() : 0.0;
    }

    public String getOrganization()
    {
        return organization;
    }

    public void setOrganization(String organization)
    {
        this.organization = organization;
    }

    public String getJmsMessageId()
    {
        return jmsMessageId;
    }

    public void setJmsMessageId(String jmsMessageId)
    {
        this.jmsMessageId = jmsMessageId;
    }

    @Override
    public ParsedMessage.TYPE getType()
    {
        return ParsedMessage.TYPE.RATE;
    }

    @Override
    public String toString()
    {
        return "RateMessage{" +
                "requestId=" + requestId +
                ", currencyPair='" + currencyPair + '\'' +
                ", bidRate=" + getBidRate() +
                ", offerRate=" + getOfferRate() +
                ", lp=" + getLPOrganization() +
                ", timestamp=" + timestamp +
                ", organization='" + organization + '\'' +
                '}';
    }

    @Override
    public void setAckId(String ackId)
    {
        this.ackId = ackId;
    }

    @Override
    public String getAckId()
    {
        return ackId;
    }
}

