package com.integral.gridapi.client.message;


public class TradeNotificationMessage implements ParsedMessage
{
    public static final String TYPE = "TRADE";
    
    private String ackId;
    private String jmsMessageId;
    private String wfMsgId;
    private String refId;
    private String event;
    private String topic;
    private String status;
    private String requestRefId;
    private boolean isMaker;
    private String ISOrderId;
    private String reqClassification;
    private String orgShortName;
    private String toOrgShortName;
    private String userShortName;
    private String legName;
    private String bidOffer;
    private String tenor;
    private String dealtCcyProperty;
    private String dealtCcy;
    private String settledCcy;
    private double dealtAmount;
    private String tradeRefId;
    private String ISExternalId;
    private String reqExternalId;
    private String tradeClassification;
    private String tradeDate;
    private String cptyA;
    private String cptyB;
    private String valueDate;
    private String currency1;
    private String currency2;
    private String baseCurrency;
    private String varCurrency;
    private double rate;
    private double spotRate;
    private double fwdPoints;
    private double ccyAmount1;
    private double ccyAmount2;
    private String externalUserId;
    private String coveredDealId;
    private boolean isBuyingBaseCCY;

    private String instrument;
    
    public String getWfMsgId()
    {
        return wfMsgId;
    }
    public void setWfMsgId(String wfMsgId)
    {
        this.wfMsgId = wfMsgId;
    }
    public String getRefId()
    {
        return refId;
    }
    public void setRefId(String refId)
    {
        this.refId = refId;
    }
    public String getEvent()
    {
        return event;
    }
    public void setEvent(String event)
    {
        this.event = event;
    }
    public String getTopic()
    {
        return topic;
    }
    public void setTopic(String topic)
    {
        this.topic = topic;
    }
    public String getStatus()
    {
        return status;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }
    public String getRequestRefId()
    {
        return requestRefId;
    }
    public void setRequestRefId(String requestRefId)
    {
        this.requestRefId = requestRefId;
    }
    public boolean isMaker()
    {
        return isMaker;
    }
    public void setMaker(boolean isMaker)
    {
        this.isMaker = isMaker;
    }
    public String getISOrderId()
    {
        return ISOrderId;
    }
    public void setISOrderId(String iSOrderId)
    {
        ISOrderId = iSOrderId;
    }
    public String getReqClassification()
    {
        return reqClassification;
    }
    public void setReqClassification(String reqClassification)
    {
        this.reqClassification = reqClassification;
    }
    public String getOrgShortName()
    {
        return orgShortName;
    }
    public void setOrgShortName(String orgShortName)
    {
        this.orgShortName = orgShortName;
    }
    public String getToOrgShortName()
    {
        return toOrgShortName;
    }
    public void setToOrgShortName(String toOrgShortName)
    {
        this.toOrgShortName = toOrgShortName;
    }
    public String getUserShortName()
    {
        return userShortName;
    }
    public void setUserShortName(String userShortName)
    {
        this.userShortName = userShortName;
    }
    public String getBidOffer()
    {
        return bidOffer;
    }
    public void setBidOffer(String bidOffer)
    {
        this.bidOffer = bidOffer;
    }
    public String getTenor()
    {
        return tenor;
    }
    public void setTenor(String tenor)
    {
        this.tenor = tenor;
    }
    public String getDealtCcyProperty()
    {
        return dealtCcyProperty;
    }
    public void setDealtCcyProperty(String dealtCcyProperty)
    {
        this.dealtCcyProperty = dealtCcyProperty;
    }
    public String getDealtCcy()
    {
        return dealtCcy;
    }
    public void setDealtCcy(String dealtCcy)
    {
        this.dealtCcy = dealtCcy;
    }
    public String getSettledCcy()
    {
        return settledCcy;
    }
    public void setSettledCcy(String settledCcy)
    {
        this.settledCcy = settledCcy;
    }
    public double getDealtAmount()
    {
        return dealtAmount;
    }
    public void setDealtAmount(double dealtAmount)
    {
        this.dealtAmount = dealtAmount;
    }
    public String getTradeRefId()
    {
        return tradeRefId;
    }
    public void setTradeRefId(String tradeRefId)
    {
        this.tradeRefId = tradeRefId;
    }
    public String getISExternalId()
    {
        return ISExternalId;
    }
    public void setISExternalId(String iSExternalId)
    {
        ISExternalId = iSExternalId;
    }
    public String getReqExternalId()
    {
        return reqExternalId;
    }
    public void setReqExternalId(String reqExternalId)
    {
        this.reqExternalId = reqExternalId;
    }
    public String getTradeClassification()
    {
        return tradeClassification;
    }
    public void setTradeClassification(String tradeClassification)
    {
        this.tradeClassification = tradeClassification;
    }
    public String getTradeDate()
    {
        return tradeDate;
    }
    public void setTradeDate(String tradeDate)
    {
        this.tradeDate = tradeDate;
    }
    public String getCptyA()
    {
        return cptyA;
    }
    public void setCptyA(String cptyA)
    {
        this.cptyA = cptyA;
    }
    public String getCptyB()
    {
        return cptyB;
    }
    public void setCptyB(String cptyB)
    {
        this.cptyB = cptyB;
    }
    public String getValueDate()
    {
        return valueDate;
    }
    public void setValueDate(String valueDate)
    {
        this.valueDate = valueDate;
    }
    public String getCurrency1()
    {
        return currency1;
    }
    public void setCurrency1(String currency1)
    {
        this.currency1 = currency1;
    }
    public String getCurrency2()
    {
        return currency2;
    }
    public void setCurrency2(String currency2)
    {
        this.currency2 = currency2;
    }
    public String getBaseCurrency()
    {
        return baseCurrency;
    }
    public void setBaseCurrency(String baseCurrency)
    {
        this.baseCurrency = baseCurrency;
    }
    public String getVarCurrency()
    {
        return varCurrency;
    }
    public void setVarCurrency(String varCurrency)
    {
        this.varCurrency = varCurrency;
    }
    public double getRate()
    {
        return rate;
    }
    public void setRate(double rate)
    {
        this.rate = rate;
    }
    public double getSpotRate()
    {
        return spotRate;
    }
    public void setSpotRate(double spotRate)
    {
        this.spotRate = spotRate;
    }
    public double getFwdPoints()
    {
        return fwdPoints;
    }
    public void setFwdPoints(double fwdPoints)
    {
        this.fwdPoints = fwdPoints;
    }
    public double getCcyAmount1()
    {
        return ccyAmount1;
    }
    public void setCcyAmount1(double ccyAmount1)
    {
        this.ccyAmount1 = ccyAmount1;
    }
    public double getCcyAmount2()
    {
        return ccyAmount2;
    }
    public void setCcyAmount2(double ccyAmount2)
    {
        this.ccyAmount2 = ccyAmount2;
    }
    
    public String getJmsMessageId()
    {
        return jmsMessageId;
    }

    public void setJmsMessageId(String jmsMessageId)
    {
        this.jmsMessageId = jmsMessageId;
    }

    @Override
    public String toString()
    {
        return "TradeNotification[" + getJmsMessageId() +" | event=" + this.event + " | orgShortName=" + this.orgShortName + " | toOrgShortName=" + this.toOrgShortName + 
                " | cptyA=" + this.cptyA + " | cptyB=" + this.cptyB + " | isMaker=" + this.isMaker +  " | reqExternalId=" +  this.reqExternalId + 
                " | bidOffer=" + this.bidOffer + " | baseCurrency=" + this.baseCurrency + " | varCurrency=" + this.varCurrency +
                " | dealtAmount=" + this.dealtAmount + " | ccyAmount1=" +this.ccyAmount1 + " | ccyAmount2=" + this.ccyAmount2 +
                " | rate=" + this.rate + " | ISOrderId=" + this.ISOrderId + " | valueDate=" + this.valueDate + " | tradeDate=" + this.tradeDate +               
                " | ackId=" + this.ackId + " | requestRefId=" + this.requestRefId + " | tradeRefId=" + this.tradeRefId +  " | coveredDealId=" + this.coveredDealId+ "]";
    }
    
    public String getInstrument()
    {
        if( instrument == null )
            instrument = this.baseCurrency + "/" + this.varCurrency;
        
        return instrument;
    }
    
    @Override
    public ParsedMessage.TYPE getType()
    {
        return ParsedMessage.TYPE.TRADE;
    }
    @Override
    public void setAckId(String ackId)
    {
        this.ackId = ackId;
    }
    @Override
    public String getAckId()
    {
        return ackId;
    }
    public String getExternalUserId()
    {
        return externalUserId;
    }
    public void setExternalUserId(String externalUserId)
    {
        this.externalUserId = externalUserId;
    }
	public String getLegName()
	{
		return legName;
	}
	public void setLegName( String legName )
	{
		this.legName = legName;
	}
	public String getCoveredDealId()
	{
		return coveredDealId;
	}
	public void setCoveredDealId( String coveredDealId )
	{
		this.coveredDealId = coveredDealId;
	}
	public boolean isBuyingBaseCCY()
	{
		return isBuyingBaseCCY;
	}
	public void setBuyingBaseCCY( boolean isBuyingBaseCCY )
	{
		this.isBuyingBaseCCY = isBuyingBaseCCY;
	}
    
}
