package com.integral.gridapi.client.message;

public class DoNotProcessMessage implements ParsedMessage
{

	private String jmsMessageId;
	private String ackId;;
	@Override
	public ParsedMessage.TYPE getType()
	{
		return TYPE.DO_NO_PROCESS;
	}

	@Override
	public void setJmsMessageId( String id )
	{
		this.jmsMessageId = id;
	}

	@Override
	public String getJmsMessageId()
	{
		return jmsMessageId;
	}

	@Override
	public void setAckId( String ackId )
	{
		this.ackId = ackId;
	}

	@Override
	public String getAckId()
	{
		return ackId;
	}
}
