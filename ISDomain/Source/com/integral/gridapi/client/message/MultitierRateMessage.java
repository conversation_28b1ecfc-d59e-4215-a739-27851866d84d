package com.integral.gridapi.client.message;

import java.util.ArrayList;
import java.util.List;

public class MultitierRateMessage extends RateMessage {
    private List<RateData> bidRates;
    private List<RateData> offerRates;

    public MultitierRateMessage() {
        bidRates = new ArrayList<RateData>();
        offerRates = new ArrayList<RateData>();
    }

    public List<RateData> getBidRates() {
        return bidRates;
    }

    public void addBidRate(RateData rate) {
        this.bidRates.add(rate);
    }

    public void setBidRates(List<RateData> bidRates) {
        this.bidRates = bidRates;
    }

    public List<RateData> getOfferRates() {
        return offerRates;
    }

    public void addOfferRate(RateData rate) {
        this.offerRates.add(rate);
    }

    public void setOfferRates(List<RateData> offerRates) {
        this.offerRates = offerRates;
    }

    @Override
    public double getBidRate() {
        return (this.getBidRates() != null) ? this.getBidRates().get(0).getRate() : 0.0;
    }

    @Override
    public double getOfferRate() {
        return (this.getOfferRates() != null) ? this.getOfferRates().get(0).getRate() : 0.0;
    }

    @Override
    public String getLPOrganization() {
        return getBidRates().get(0) != null ? getBidRates().get(0).getProvider() : null;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("MultitierRateMessage [bidRates=");
        builder.append(bidRates);
        builder.append(", offerRates=");
        builder.append(offerRates);
        builder.append(", getRequestId()=");
        builder.append(getRequestId());
        builder.append(", getAckId()=");
        builder.append(getAckId());
        builder.append(", getCurrencyPair()=");
        builder.append(getCurrencyPair());
        builder.append(", getTimestamp()=");
        builder.append(getTimestamp());
        builder.append(", getBidRateData()=");
        builder.append(getBidRateData());
        builder.append(", getOfferRateData()=");
        builder.append(getOfferRateData());
        builder.append(", getMarketRate()=");
        builder.append(getMarketRate());
        builder.append(", getOrganization()=");
        builder.append(getOrganization());
        builder.append(", getType()=");
        builder.append(getType());
        builder.append("]");
        return builder.toString();
    }
}

