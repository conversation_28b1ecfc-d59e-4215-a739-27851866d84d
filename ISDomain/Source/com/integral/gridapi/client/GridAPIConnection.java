package com.integral.gridapi.client;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.net.HttpURLConnection;
import java.net.SocketException;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.zip.GZIPInputStream;

import com.integral.executionservice.client.api.APIOrder;
import com.integral.executionservice.client.api.APIOrderRequest;
import com.integral.executionservice.client.api.APIOrderResponse;
import com.integral.executionservice.client.api.APIOrderResponseC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.gridapi.client.config.GridApiConfig;
import com.integral.gridapi.client.handler.MessageHandler;
import com.integral.gridapi.client.message.MultitierRateMessage;
import com.integral.gridapi.client.message.PolledMessage;
import com.integral.gridapi.client.message.TradeNotificationMessage;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.util.CompositeKeys;

public class GridAPIConnection
{
	public static final int RESPONSE_CODE_SUCCESS = 200;
	public static final int RESPONSE_CODE_UNAUTHORIZED = 401;
	
	public static final String CHANNEL_NAME_DOT_NET_CLIENT = "DNET/PD";
	public final String SSO_COOKIE_RENEW = "SSO_TOKEN_RENEW";
	private long lastConnectionAttemptTime;
	private static final long TIME_BEFORE_RECONNECT = 30000;
	private final GridAPICookieManager cookieManager = new GridAPICookieManager();
	private final Log log;
	private int pollCounter = 0;
	private boolean connected = false;
	private boolean tradeNotificationSubscribed = false;
	public AtomicInteger disconnectCounter = new AtomicInteger(0);
	
	private MessageHandler<TradeNotificationMessage> tradeNotificationHandler;

	private Set<String> ackIdSet = new LinkedHashSet<String>(100);
	private GridApiConfig gridAPIConfig;

	private final HashSet<String> subscriptionRequests = new HashSet<String>();
	private final HashSet<String> pendingSubscriptionsRequests = new HashSet<String>();
	private MessageHandler<MultitierRateMessage> rateHandler;


	public GridAPIConnection(GridApiConfig config)
	{
		this.log = LogFactory.getLog(GridAPIConnection.class.getName() + "-" + config.getOrg());
		this.gridAPIConfig = config;
	}
	
	protected int sendGetMessage( String urlString ) throws Exception
	{
		try
		{
			URL obj = new URL(urlString);
			HttpURLConnection con = (HttpURLConnection) obj.openConnection();
			con.setRequestProperty("User-Agent", "Java Client");
			con.setRequestProperty("Accept-Encoding", "gzip, compress");
			con.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
			cookieManager.setCookies(con);
			int responseCode = con.getResponseCode();
			if ( log.isDebugEnabled() )
			{
				log.debug("Sending 'GET' request to URL : " + urlString);
				log.debug("Response Code : " + responseCode);
				log.debug("Get Response : " + getResponse(con));
			}
			if ( responseCode == RESPONSE_CODE_SUCCESS )
			{
				disconnectCounter.set(0);
				cookieManager.storeCookies(con);
			}
			else if ( responseCode == RESPONSE_CODE_UNAUTHORIZED )
			{
				log.warn("Setting connected=false since responseCode=" + responseCode);
				connected = false;
			}
			else
			{
				disconnectCounter.incrementAndGet();
			}
			
			return responseCode;
		}
		catch (SocketException e){
			log.info("sendGetMessage : Failed for url="+urlString+", SocketExp");
			if( log.isDebugEnabled()){
				log.debug("sendGetMessage : Failed for url="+urlString, e);
			}
		}
		catch ( Exception e )
		{
			log.info("sendGetMessage : Failed");
			if(log.isDebugEnabled()){
				log.debug("sendGetMessage : Failed", e);
			}
			disconnectCounter.incrementAndGet();
		}
		return -1;
	}

	protected int sendPostMessage( String postUrl, String postData )
	{
		try
		{
			URL obj = new URL(postUrl);
			HttpURLConnection con = (HttpURLConnection) obj.openConnection();
			String urlParameters = postData;
			log.info("\nSending 'POST' request to URL : " + postUrl);
			log.info("Post parameters : " + urlParameters);
			//add reuqest header
			con.setRequestMethod("POST");
			con.setRequestProperty("User-Agent", "Java Client");
			con.setRequestProperty("Accept-Encoding", "gzip, compress");
			con.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
			con.setRequestProperty("Content-Length", String.valueOf(urlParameters.length()));
			cookieManager.setCookies(con);
			// Send post request
			con.setDoOutput(true);
			DataOutputStream wr = new DataOutputStream(con.getOutputStream());
			wr.writeBytes(urlParameters);
			wr.flush();
			wr.close();
			String response = getResponse(con);
			int responseCode = con.getResponseCode();
			log.info("Response Code : " + responseCode);
			log.info("Get Response : " + response);

			if ( responseCode == RESPONSE_CODE_SUCCESS )
			{
				disconnectCounter.set(0);
				cookieManager.storeCookies(con);
			}
			else if ( responseCode == RESPONSE_CODE_UNAUTHORIZED )
			{
				log.info("Setting connected=false since responseCode=" + responseCode);
				connected = false;
			}
			else
			{
				disconnectCounter.incrementAndGet();
			}
			return responseCode;
		}
		catch (SocketException e){
			log.info("sendPostMessage : Failed for url="+postUrl+", SocketExp");
			if( log.isDebugEnabled()){
				log.debug("sendPostMessage : Failed for url="+postUrl, e);
			}
		}
		catch ( Exception e )
		{
			log.info("sendPostMessage : Failed for url="+postUrl);
			if(log.isDebugEnabled()){
				log.debug("sendPostMessage : Failed for url="+postUrl,e);
			}
			disconnectCounter.incrementAndGet();
		}
		return -1;

	}

	public PolledMessage pollForMessages() 
	{
		PolledMessage responseMsg = new PolledMessage();
		try
		{
			URL obj;
			obj = new URL(getProxyPollUrl());

			HttpURLConnection con = (HttpURLConnection) obj.openConnection();
			if ( con == null )
			{
				responseMsg.setStatus(PolledMessage.STATUS.FAILED);
				responseMsg.setResponseCode(-1);
				return responseMsg;
			}
			con.setRequestProperty("User-Agent", "Java Client");
			con.setRequestProperty("Accept-Encoding", "gzip, compress");
			con.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
			con.setConnectTimeout(getFxiConnectionTimeout());
			con.setReadTimeout(getFxiReadConnectionTimeout());
			cookieManager.setCookies(con);
			responseMsg.setResponseCode(con.getResponseCode());
			if ( log.isDebugEnabled() )
			{
				log.debug("poll_response_Code : " + responseMsg.getResponseCode());
			}
			if ( responseMsg.getResponseCode() == RESPONSE_CODE_SUCCESS )
			{
				String response = getResponse(con);
				disconnectCounter.set(0);
				cookieManager.storeCookies(con);
				if ( log.isDebugEnabled() )
				{
					log.debug("poll_response_msg : " + response);
				}
				responseMsg.setMessage(response);
				responseMsg.setStatus(PolledMessage.STATUS.SUCCESS);
				return responseMsg;
			}
			else if ( responseMsg.getResponseCode() == RESPONSE_CODE_UNAUTHORIZED )
			{
				log.info("pollForMessages : Setting connected=false since responseCode=" + responseMsg.getResponseCode());
				connected = false;
			}
			else if ( responseMsg.getResponseCode() == 500 )
			{
				String response = getResponse(con);
				log.info("pollForMessages : Polled message returned failed response code: " + responseMsg.getResponseCode());
				responseMsg.setMessage(response);
				responseMsg.setStatus(PolledMessage.STATUS.UNKNOWN_EXCEPTION);
				disconnectCounter.incrementAndGet();
				return responseMsg;
			}
			else
			{
				log.info("pollForMessages : Polled message returned failed response code: " + responseMsg.getResponseCode());
				responseMsg.setStatus(PolledMessage.STATUS.REJECTED);
				return responseMsg;
			}
		}
		catch (SocketException e){
			responseMsg.setStatus(PolledMessage.STATUS.FAILED);
			log.info("pollForMessages : Failed SocketExp");
			if( log.isDebugEnabled()){
				log.debug("pollForMessages : Failed ", e);
			}
		}
		catch ( Exception e )
		{
			log.warn("pollForMessages : Failed ");
			if( log.isDebugEnabled()){
				log.warn("pollForMessages : Failed ",e);
			}
			responseMsg.setStatus(PolledMessage.STATUS.FAILED);
			disconnectCounter.incrementAndGet();
		}
		return responseMsg;
	}

	public boolean doLogin() throws Exception
	{
		int statusCode = sendPostMessage(getLoginUrl(), getLoginPostMessage());
		log.info("StatusCode in doLogin(): " + statusCode);
		if ( statusCode == RESPONSE_CODE_SUCCESS )
		{
			statusCode = sendPostMessage(getLoginInitUrl(), loginInitPostMsg());
			log.info("Status code for initialization: " + statusCode);
		}
		else
		{
			log.warn("ConnectionManager: login FAILED...");
			connected = false;
			throw new Exception("Login failed with statusCode: " + statusCode + " , login_url= " + getLoginUrl() + " , login_msg= " + getLoginPostMessage());
		}
		log.info("Login successful for ORG : " + getOrgName());
		connected = (statusCode == RESPONSE_CODE_SUCCESS);
		return connected;
	}
	
	public boolean sendHeartBeat() throws Exception
	{
		int responseCode;
		try
		{
			String url = getHeartBeatURL();
			URL obj = new URL(url);
			HttpURLConnection con = (HttpURLConnection) obj.openConnection();
			con.setRequestProperty("User-Agent", "Java Client");
			con.setRequestProperty("Accept-Encoding", "gzip, compress");
			con.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
			cookieManager.setCookies(con);
			responseCode = con.getResponseCode();
			if ( log.isDebugEnabled() )
			{
				log.debug("sendHeartBeat : Sending 'GET' request to URL : " + url);
				log.debug("sendHeartBeat : Response Code : " + responseCode);
				//log.debug("Get Response : " + getResponse(con));
			}
			if ( responseCode == RESPONSE_CODE_SUCCESS )
			{
				String responseParams = getResponse(con);
				if ( responseParams != null && responseParams.contains(SSO_COOKIE_RENEW) )
				{
					if ( log.isDebugEnabled() )
					{
						log.debug("sendHeartBeat : Token Renewal requested from Server for org->" + getOrgName());
					}

					int response = sendGetMessage(getSSORenewUrl());
					if ( response != RESPONSE_CODE_SUCCESS )
					{
						log.warn("sendHeartBeat : SSO Token Renewal Request failed for org->" + getOrgName());
					}
					else
					{
						log.info("sendHeartBeat : SSO Token Renewal Successful for org->" + getOrgName());
					}
				}
				return true;
			}
			else if ( responseCode == RESPONSE_CODE_UNAUTHORIZED )
			{
				log.warn("sendHeartBeat : Setting connected=false since responseCode=" + responseCode);
				connected = false;
			}
			else
			{
				log.warn("sendHeartBeat : Hearbeat FAILED");
			}
		}
		catch (SocketException se){
			log.info("sendHeartBeat : Could not get message due to SocketExp");
			if(log.isDebugEnabled()){
				log.debug("Could not get message due to socketException",se);
			}
		}
		catch ( Exception e )
		{
			log.warn("sendHeartBeat : Failed");
			if( log.isDebugEnabled()){
				log.warn("sendHeartBeat : Failed",e);
			}
		}
		return false;
	}


	public boolean doLogout() throws Exception
	{
		int statusCode = sendPostMessage(getLogoutUrl(), "");
		log.info("Logout called, return status: " + statusCode);
		return statusCode == RESPONSE_CODE_SUCCESS;
	}
	
	private String getResponse( HttpURLConnection con ) throws Exception
	{
		String inputLine;
		StringBuilder response = new StringBuilder(1000);
		BufferedReader in = null;
		try
		{
			if ( con.getHeaderField("Content-Encoding") != null && con.getHeaderField("Content-Encoding").equalsIgnoreCase("gzip") )
			{
				GZIPInputStream gzis = new GZIPInputStream(con.getInputStream());
				InputStreamReader reader = new InputStreamReader(gzis);
				in = new BufferedReader(reader);
			}
			else
			{
				in = new BufferedReader(new InputStreamReader(con.getInputStream()));
			}

			while ( (inputLine = in.readLine()) != null )
				response.append(inputLine);

		}
		finally
		{
			try
			{
				if ( in != null )
					in.close();
			}
			catch ( IOException e )
			{
				log.info("getResponse : Failed IOExp");
				if( log.isDebugEnabled()){
					log.debug("getResponse : Failed IOExp",e);
				}
			}
		}
		return response.toString();
	}



    
	private String getProxyPollUrl()
	{
		StringBuilder url = new StringBuilder(100).append(getPollUrl());
		StringBuilder ackIds = new StringBuilder();
		synchronized ( ackIdSet )
		{
			Iterator<String> iterator = ackIdSet.iterator();
			int counter = 0;
			while ( iterator.hasNext() && counter <= gridAPIConfig.getPollingThreshold() )
			{
				String ackId = iterator.next();
				counter++;
				ackIds.append(ackId).append(",");
				ackIdSet.remove(ackId);
			}
		}
		if ( ackIds.length() > 1 )
		{
			ackIds.deleteCharAt(ackIds.length() - 1);
		}
		url.append("/jmsproxy?cmd=P&f=1&ackIdList=").append(ackIds.toString()).append("&u=").append(getUserName()).append("&r=0&l=0&p=0&o=").append(getOrgName()).append("&reqID=POLL").append(pollCounter++);
		return url.toString();
	}

	protected boolean subscribeForTradeNotifications()
	{
		boolean result = false;
		try
		{
			int statusCode = sendGetMessage(getNotificationSubscriptionUrl());
			result = (statusCode == RESPONSE_CODE_SUCCESS);
		}
		catch ( Exception e )
		{
			log.warn("subscribeForTradeNotifications : Failed");
			if( log.isDebugEnabled()){
				log.debug("subscribeForTradeNotifications : Exception occured while subscribing for trade Notifications", e);
			}
		}
		return result;
	}

	protected void processPendingSubscriptions() throws Exception {
		if (pendingSubscriptionsRequests.isEmpty()) {
			return;
		}
		List<String> subscriptions = new ArrayList<String>();
		synchronized (pendingSubscriptionsRequests) {
			for (String s : pendingSubscriptionsRequests) {
				subscriptions.add(s);
			}
		}
		for (String subscription : subscriptions) {
			StringTokenizer st = new StringTokenizer(subscription, "~");
			String ccyPair = st.nextToken();
			int view = Integer.parseInt(st.nextToken());
			synchronized (subscriptionRequests){
				if( subscriptionRequests.contains(subscription)){
					/*
						Avoid duplicate subscription requests.
					 */
					pendingSubscriptionsRequests.remove(subscription);
					continue;
				}
			}
			String subscriptionPostMessage = getSubscriptionPostMessage(ccyPair, view);
			int responseCode = sendPostMessage(getSubscriptionUrl(), subscriptionPostMessage);
			if (responseCode == RESPONSE_CODE_SUCCESS) {
				synchronized (pendingSubscriptionsRequests) {
					pendingSubscriptionsRequests.remove(subscription);
				}
				synchronized (subscriptionRequests) {
					subscriptionRequests.add(subscription);
				}
			}
		}
	}

	/**
	 *
	 * @param ccyPair
	 * @param viewType Refer MarketDataViewType
	 *     FULL_BOOK_AGGREGATED = 0
	 *     FULL_BOOK_NON_AGGREGATED = 1
	 */
	public void subscribeRates(String ccyPair, int viewType) {
		StringBuilder sb = new StringBuilder(10);
		sb.append(ccyPair).append('~').append(viewType);
		synchronized (pendingSubscriptionsRequests) {
			pendingSubscriptionsRequests.add(sb.toString());
		}
	}

	private String getNotificationSubscriptionUrl()
	{
		StringBuilder sb = new StringBuilder(200).append(getPollUrl());
		sb.append("/jmsproxy/jmsproxy.do?e=subscribe&u=").append(getUserName()).append("&o=").append(getOrgName()).append("&handlerId=NH&brokerId=FXIDirectBroker");
		return sb.toString();
	}



	private String getEncryptedPassword( String Input )
	{
		String pass = "";
		try
		{
			byte[] msgBytes = Input.getBytes();
			try
			{
				MessageDigest m = MessageDigest.getInstance("MD5");
				m.reset();
				m.update(msgBytes);
				byte[] digest = m.digest();
				BigInteger bigInt = new BigInteger(1, digest);
				pass = bigInt.toString(16);

			}
			catch ( NoSuchAlgorithmException e )
			{
				e.printStackTrace(); 
			}

		}
		catch ( Exception e )
		{
			log.warn("getEncryptedPassword : Failed");
			if(log.isDebugEnabled()){
				log.debug("getEncryptedPassword : Failed",e);
			}
		}
		return pass;
	}

	public boolean isConnected()
	{
		return connected;
	}

	@Override
	public boolean equals( Object o )
	{
		if ( this == o )
			return true;
		if ( o == null || getClass() != o.getClass() )
			return false;

		GridAPIConnection that = (GridAPIConnection) o;

		if ( !getOrgName().equals(that.getOrgName()) )
			return false;
		if ( !getPassword().equals(that.getPassword()) )
			return false;
		if ( !getPollUrl().equals(that.getPollUrl()) )
			return false;
		if ( !getUrl().equals(that.getUrl()) )
			return false;
		if ( !getUserName().equals(that.getUserName()) )
			return false;
		return true;
	}

	@Override
	public int hashCode()
	{
		int result = getUrl().hashCode();
		result = 31 * result + getPollUrl().hashCode();
		result = 31 * result + getUserName().hashCode();
		result = 31 * result + getOrgName().hashCode();
		result = 31 * result + getPassword().hashCode();
		return result;
	}

	public void setConnected( boolean connected )
	{
		this.connected = connected;
	}

	public void addPendingAckId( String ackId )
	{
		synchronized ( ackIdSet )
		{
			ackIdSet.add(ackId);
		}
	}

	public long getLastConnectionAttemptTime()
	{
		return lastConnectionAttemptTime;
	}

	public void setLastConnectionAttemptTime( long lastConnectionAttemptTime )
	{
		this.lastConnectionAttemptTime = lastConnectionAttemptTime;
	}

		
	public APIOrderResponse postOrder(APIOrderRequest orderRequest)
	{
		APIOrderResponseC orderResponse = null;
		orderResponse = new APIOrderResponseC(orderRequest.getOrder());
		try
		{
			APIOrder order = orderRequest.getOrder();
			String orderMsgData = order.json();
			int responseCode = sendPostMessage(getOrderSubmitUrl(), orderMsgData);
			if ( responseCode == RESPONSE_CODE_SUCCESS )
			{
				orderResponse.setStatus(APIOrderResponseC.Status.SUCCESS);
				return orderResponse;
			}

		}
		catch ( Exception e )
		{
			log.warn("postOrder: Failed to submit order ");
			if( log.isDebugEnabled()){
				log.debug("postOrder: Failed to submit order", e);
			}

		}
		orderResponse.setStatus(APIOrderResponseC.Status.FAILED);
		return orderResponse;
	}
	
	public boolean checkConnection()
	{
		if ( !connected  )
		{
			synchronized ( this )
			{
				if ( connected )
				{
					return true;
				}
				if ( (System.currentTimeMillis() - getLastConnectionAttemptTime()) > TIME_BEFORE_RECONNECT )
				{
					reset();
					try
					{
						setLastConnectionAttemptTime(System.currentTimeMillis());
						doLogin();
					}
					catch ( Exception e )
					{
						if ( log.isDebugEnabled() )
						{
							log.debug("Exception while logging into " + getOrgName(), e);
						}
					}
				}
				else
				{
					if ( log.isDebugEnabled() )
					{
						log.debug("run : Not reconnecting since ST-LCT<TBR" + "connected : " + isConnected());
					}
				}
			}
		}
		
		else if ( disconnectCounter.get() >= gridAPIConfig.getDisconnectThreshold() )
		{
			synchronized ( this )
			{
				reset();
				try
				{
					setLastConnectionAttemptTime(System.currentTimeMillis());
					doLogin();
				}
				catch ( Exception e )
				{
					if ( log.isDebugEnabled() )
					{
						log.debug("Exception while logging into " + getOrgName(), e);
					}
				}
			}
		}

		return connected;
	}
	
	
	protected void reset()
	{
		try
		{
			if ( connected )
			{
				doLogout();
			}
		}
		catch ( Exception e )
		{
			log.warn("reset : Failed");
			if(log.isDebugEnabled()){
				log.debug("reset : Failed",e);
			}

		}
		setConnected(false);
		List<String> subscriptions = new ArrayList<String>();
		synchronized (subscriptionRequests) {
			for (String s : subscriptionRequests) {
				subscriptions.add(s);
			}
			subscriptionRequests.clear();
		}

		synchronized (pendingSubscriptionsRequests) {
			for (String s : subscriptions) {
				pendingSubscriptionsRequests.add(s);
			}
		}
		tradeNotificationSubscribed = false;
		cookieManager.reset();
		ackIdSet.clear();
		disconnectCounter.set(0);
	}

	public void subscribeTradeNotfications( MessageHandler<TradeNotificationMessage> tradeNotificationHandler )
	{
		this.tradeNotificationHandler = tradeNotificationHandler;
	}

	public void unsubscribeTradeNotifications()
	{
		this.tradeNotificationHandler = null;
	}

	public MessageHandler<TradeNotificationMessage> getTradeNotificationHandler()
	{
		return tradeNotificationHandler;
	}

	public boolean isTradeNotificationSubscribed()
	{
		return tradeNotificationSubscribed;
	}

	public void setTradeNotificationSubscribed( boolean tradeNotificationSubscribed )
	{
		this.tradeNotificationSubscribed = tradeNotificationSubscribed;
	}

	public String getUrl()
	{
		return gridAPIConfig.getHostUrl();
	}

	public String getPollUrl()
	{
		return gridAPIConfig.getPollUrl();
	}

	public String getUserName()
	{
		return gridAPIConfig.getUsername();
	}

	public String getOrgName()
	{
		return gridAPIConfig.getOrg();
	}

	public String getPassword()
	{
		return gridAPIConfig.getPassword();
	}
	
	public int getFxiConnectionTimeout() 
	{
		return gridAPIConfig.getConnectionTimeOut();
	}
	
	public int getFxiReadConnectionTimeout()
	{
		return gridAPIConfig.getReadConnectionTimeOut();
	}

	private String getLoginUrl()
	{
		return getUrl() + "/fxi/fxiapi/sso/login";
	}
	
	private String getSSORenewUrl()
	{
		return getUrl() + "/fxi/fxiapi/sso/token/renew";
	}

	private String getLogoutUrl()
	{
		return getUrl() + "/fxi/fxiapi/auth/logout";
	}

	private String getLoginPostMessage()
	{
		return "{\"user\": \"" + getUserName() + "\", \"pass\": \"" + getEncryptedPassword(getPassword()) + "\", \"org\": \"" + getOrgName() + "\", \"apiVersion\": \"" + gridAPIConfig.getApiVersion() + "\"}";
	}

	private String getLoginInitUrl()
	{
		return getUrl() + "/fxi/fxiapi/dealingService/initialize";
	}

	private String getOrderSubmitUrl()
	{
		return getUrl() + "/fxi/fxiapi/order/place";
	}

	private String loginInitPostMsg()
	{
		return "{\"user\": \"" + getUserName() + "\", \"pass\": \"" + getEncryptedPassword(getPassword()) + "\", \"org\": \"" + getOrgName() + "\", \"clientVersion\": \"" + gridAPIConfig.getClientVersion() + "\", \"clientType\": \"WEBCLIENT\", \"apiVersion\": \"" + gridAPIConfig.getApiVersion() + "\", \"clientMidMarkEnabled\":true}";
	}

	private String getHeartBeatURL()
	{
		StringBuilder sb = new StringBuilder(200).append(getUrl()).append("/fxi/fxiapi/heartbeat");
		sb.append("?&l=").append(0).append("&a=").append(0).append("&h=").append(0).append("&c=").append(0);
		return sb.toString();
	}

	private String getSubscriptionPostMessage(String currencyPair, int viewType) {
		String baseCcy = CurrencyFactory.getBaseCurrency(currencyPair);
		String termCcy = CurrencyFactory.getTermCurrency(currencyPair);
		String requestID = baseCcy + termCcy + "_"+getUserName() + "@" + getOrgName();
        String postMsgData;
		switch (viewType){
			case 0:
			    //Full Book Aggregated
                postMsgData = "{\"async\": \"true\", \"requestList\" : [{\"reqID\" : \"" +
                        requestID + "\", \"view\" : " + viewType + ",  \"reqType\" : 1, \"depth\" : 10, \"updType\" : 0, \"entryType\" : 2, \"instrument\" : \"" +
                        currencyPair + "\", \"providers\" : [], \"minSize\" : 0, \"dealtInstrument\" : \"" +
                        baseCcy + "\"}]}";
                break;
            case 1:
                //Full Book Non-Aggregated
                postMsgData = "{\"async\": \"true\", \"requestList\" : [{\"reqID\" : \"" +
                        requestID + "\", \"view\" : " + viewType + ",  \"reqType\" : 1, \"depth\" : 200, \"updType\" : 0, \"entryType\" : 2, \"instrument\" : \"" +
                        currencyPair + "\", \"providers\" : [], \"minSize\" : 0, \"dealtInstrument\" : \"" +
                        baseCcy + "\"}]}";
                break;
            case 3:
                //Best Price
                postMsgData = "{\"async\": \"true\", \"requestList\" : [{\"reqID\" : \"" +
                        requestID + "\", \"view\" : " + viewType + ",  \"reqType\" : 1, \"depth\" : 1, \"updType\" : 0, \"entryType\" : 2, \"instrument\" : \"" +
                        currencyPair + "\", \"providers\" : [], \"dealtInstrument\" : \"" +
                        baseCcy + "\"}]}";
                break;

            default:
                postMsgData = "{\"async\": \"true\", \"requestList\" : [{\"reqID\" : \"" +
                        requestID + "\", \"view\" : " + viewType + ",  \"reqType\" : 1, \"depth\" : 1, \"updType\" : 0, \"entryType\" : 2, \"instrument\" : \"" +
                        currencyPair + "\", \"providers\" : [], \"minSize\" : 1000, \"dealtInstrument\" : \"" +
                        baseCcy + "\", \"requestedSize\" : 10000000}]}";
                break;
        }
		return postMsgData;
	}

	private String getSubscriptionUrl() {
		return getUrl() + "/fxi/fxiapi/marketdata/subscribeBatch";
	}
	
	public CompositeKeys getConnectionKey()
	{
		return CompositeKeys.getCompositeKeys(getOrgName() , getUserName());
	}

	public MessageHandler<MultitierRateMessage> getRateHandler() {
		return rateHandler;
	}

	public void setRateHandler(MessageHandler<MultitierRateMessage> rateHandler) {
		this.rateHandler = rateHandler;
	}
}

