package com.integral.gridapi.client;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import com.integral.executionservice.client.api.APIOrderRequest;
import com.integral.gridapi.client.config.GridApiConfig;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.util.CompositeKeys;

public class GridAPIConnectionManager
{
	private static GridAPIConnectionManager _instance = null; 
	private static final Log log = LogFactory.getLog(GridAPIConnectionManager.class);
	private final ConcurrentHashMap<CompositeKeys, GridAPIConnection> connections = new ConcurrentHashMap<CompositeKeys, GridAPIConnection>();
	private final ScheduledExecutorService executorService = Executors.newScheduledThreadPool(10, new GridAPIConnectionThreadFactory());
	
	private ConcurrentHashMap<CompositeKeys, ScheduledFuture> scheduledHeartbeatTasks = new ConcurrentHashMap<CompositeKeys, ScheduledFuture>();
	private ConcurrentHashMap<CompositeKeys, ScheduledFuture> scheduledPollingsTasks = new ConcurrentHashMap<CompositeKeys, ScheduledFuture>();
	static 
	{
		_instance = new GridAPIConnectionManager();
	}
	
	public static GridAPIConnectionManager getInstance()
	{
		return _instance;
	}
    
	private GridAPIConnectionManager()
	{}
	
	public GridAPIConnection getConnection(GridApiConfig config)
	{
		if ( config == null )
		{
			return null;
		}
		
		CompositeKeys connectionKey = CompositeKeys.getCompositeKeys(config.getOrg() , config.getUsername());
		
		GridAPIConnection connection = connections.get(connectionKey);
		if ( connection == null )
		{
			GridAPIConnection newConnection = new GridAPIConnection(config);

			GridAPIConnection cachedConnection = connections.putIfAbsent(newConnection.getConnectionKey(), newConnection);
			if ( cachedConnection != null )
			{
				return cachedConnection;
			}
			else
			{
				return newConnection;
			}
		}
		return connection;
	}
	
	
	public void setConnection(GridApiConfig config, GridAPIConnection connection)
	{
		connections.put(CompositeKeys.getCompositeKeys(config.getOrg() , config.getUsername()), connection);
	}
	
	public boolean startPollingTask(GridApiConfig config)
	{
		GridAPIConnection connection = getConnection(config);
		if ( connection == null )
		{
			log.error("startPollingTask: No connection found for Config=" + config);
			return false;
		}
		//Schedule with fixed delay ensures linear polling without overlaps
		CompositeKeys connectionKey = connection.getConnectionKey();
		if ( config.getPollingInterval() != -1 && !scheduledPollingsTasks.containsKey(connectionKey))
		{
			ScheduledFuture future = executorService.scheduleWithFixedDelay(new GridAPIConnectionPollingTask(connection), 1000, config.getPollingInterval(), TimeUnit.MILLISECONDS);
			scheduledPollingsTasks.putIfAbsent(connectionKey, future);
		}
		return true;
	}
	
	public boolean startHearbeatTask(GridApiConfig config)
	{
		GridAPIConnection connection = getConnection(config);
		if ( connection == null )
		{
			log.error("startHearbeatTask: No connection found for Config=" + config);
			return false;
		}
		CompositeKeys connectionKey = connection.getConnectionKey();
		if ( config.getHearbeatInterval() != -1 && !scheduledHeartbeatTasks.containsKey(connectionKey) )
		{
			ScheduledFuture future = executorService.scheduleWithFixedDelay(new GridAPIHearbeatTask(connection), 1500, config.getHearbeatInterval(), TimeUnit.MILLISECONDS);
			scheduledHeartbeatTasks.putIfAbsent(connectionKey, future);
		}
		return true;
	}
	
	private void stopHeartbeatTask(CompositeKeys connectionKey)
	{
		ScheduledFuture heartbeatTask = scheduledHeartbeatTasks.get(connectionKey);
		if ( heartbeatTask != null )
		{
			heartbeatTask.cancel(true);
			scheduledHeartbeatTasks.remove(connectionKey);
		}
	}
	
	private void stopPollingTask(CompositeKeys connectionKey)
	{
		ScheduledFuture pollingTask = scheduledPollingsTasks.get(connectionKey);
		if ( pollingTask != null )
		{
			pollingTask.cancel(true);
			scheduledPollingsTasks.remove(connectionKey);
		}
		
	}
	
	
	public boolean processOrderTask(GridApiConfig config, APIOrderRequest orderRequest )
	{
		try
		{
			GridAPIConnection connection = getConnection(config);
			if ( connection == null )
			{
				log.error("processOrderTask : No Connection found for Config=" + config);
				return false;
			}
			executorService.submit(new GridAPIConnectionEMSTask(connection, orderRequest));
			return true;
		}
		catch ( Exception e )
		{
			log.error("processOrderTask : Error in exe for Organization=" + config.getOrg() + ". Returning null connection");
		}
		return false;
	}

	
	public void stop(Set<CompositeKeys> connectionKeys) 
	{

		if ( connectionKeys != null )
		{
			for ( CompositeKeys connectionKey : connectionKeys )
			{
				try
				{
					GridAPIConnection connection = connections.get(connectionKey);
					if ( connection != null )
					{

						stopHeartbeatTask(connectionKey);
						stopPollingTask(connectionKey);
						
						connection.unsubscribeTradeNotifications();
						connection.reset();

						connections.remove(connectionKey);
					}
				}
				catch ( Exception e )
				{
					log.error(".stop Error in stopping Connections ->" + connectionKey, e);
				}
			}
		}

	}
	

	private static final class GridAPIConnectionThreadFactory implements ThreadFactory
	{
		private static final AtomicInteger counter = new AtomicInteger(0);
		
		@Override
		public Thread newThread( Runnable r )
		{
			return new Thread(r, "GridAPIConnectionThread-" + counter.incrementAndGet());
		}
	}
}
