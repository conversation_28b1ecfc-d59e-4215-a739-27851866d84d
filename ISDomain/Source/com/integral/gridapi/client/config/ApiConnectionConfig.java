package com.integral.gridapi.client.config;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 2/10/15
 * Time: 3:26 PM
 * To change this template use File | Settings | File Templates.
 */
public class ApiConnectionConfig {

    private int connectionTimeOut;
    private int readConnectionTimeOut;
    private int retryAttempts;


    public ApiConnectionConfig() {
        //Set Default values
        this.connectionTimeOut = 10000;
        this.readConnectionTimeOut = 20000;
        this.retryAttempts = 3;
    }

    public int getConnectionTimeOut() {
        return connectionTimeOut;
    }

    public void setConnectionTimeOut(int connectionTimeOut) {
        this.connectionTimeOut = connectionTimeOut;
    }

    public int getReadConnectionTimeOut() {
        return readConnectionTimeOut;
    }

    public void setReadConnectionTimeOut(int readConnectionTimeOut) {
        this.readConnectionTimeOut = readConnectionTimeOut;
    }

    public int getRetryAttempts() {
        return retryAttempts;
    }

    public void setRetryAttempts(int retryAttempts) {
        this.retryAttempts = retryAttempts;
    }
}
