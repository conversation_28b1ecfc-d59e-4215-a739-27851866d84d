/**
 * 
 */
package com.integral.gridapi.client.config;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * Config will contain all the connection related properties for Grid API http connection
 * Moving ahead this can be reused for other connection types like FIX
 * 
 * <AUTHOR>
 *
 */
public class GridApiConfig
{
	private static Log log = LogFactory.getLog(GridApiConfig.class);
	
    public static final String GRID_API_ORG_NAME = "Org";
    public static final String GRID_API_USERNAME = "Username";
    public static final String GRID_API_PASSWORD = "Password";
    public static final String GRID_API_SERVER_URL = "Server.URL";
    public static final String GRID_API_SERVER_POLL_URL = "Poll.URL";
    
	public static final String GRID_API_VERSION = "Api.Version";
	public static final String GRID_API_CLIENT_VERSION = "Client.Version";
	public static final String GRID_API_CONNECTION_POLLING_INTERVAL = "Polling.Interval";
	public static final String GRID_API_CONNECTION_HEARTBEAT_INTERVAL = "Heartbeat.Interval";
	public static final String GRID_API_READ_CONNECTION_TIMEOUT = "Read.Connection.Timeout";
	public static final String GRID_API_CONNECTION_TIMEOUT = "Connection.Timeout";
	public static final String GRID_API_DiSCONNECT_THRESHOLD = "Disconnect.Threshold";
	public static final String GRID_API_POLLING_THRESHOLD = "Polling.Ack.Limit";
    
    private String hostUrl;
    private String pollUrl;
    private String org;
    private String username;
    private String password;
    private String apiVersion;
    
    private String clientVersion;
    private long pollingInterval;
    private long HearbeatInterval;
    private int connectionTimeOut;
    private int readConnectionTimeOut;
    private int disconnectThreshold;
    private int pollingThreshold;
    
	public GridApiConfig()
	{
		//Set Default values
		this.pollingInterval = 400;
		this.HearbeatInterval = 500;
		this.connectionTimeOut = 10000;
		this.readConnectionTimeOut = 20000;
		this.pollingThreshold = 10;
		this.disconnectThreshold = 3;
		this.apiVersion = "1.41";
		this.clientVersion = "********";
	}
    
    public String getHostUrl()
    {
        return hostUrl;
    }
    
	public String getPollUrl()
    {
        return pollUrl;
    }
    public void setHostUrl(String hostUrl)
    {
        this.hostUrl = hostUrl;
    }
    public void setPollUrl(String pollUrl)
    {
        this.pollUrl = pollUrl;
    }
    public String getOrg()
    {
        return org;
    }
    public void setOrg(String org)
    {
        this.org = org;
    }
    public String getUsername()
    {
        return username;
    }
    public void setUsername(String username)
    {
        this.username = username;
    }
    public String getPassword()
    {
        return password;
    }
    public void setPassword(String password)
    {
        this.password = password;
    }        
    public String getApiVersion()
	{
		return apiVersion;
	}
	public void setApiVersion( String apiVersion )
	{
		this.apiVersion = apiVersion;
	}
	public String getClientVersion()
	{
		return clientVersion;
	}
	public void setClientVersion( String clientVersion )
	{
		this.clientVersion = clientVersion;
	}
	public long getPollingInterval()
	{
		return pollingInterval;
	}
	public void setPollingInterval( long pollingInterval )
	{
		this.pollingInterval = pollingInterval;
	}
	public long getHearbeatInterval()
	{
		return HearbeatInterval;
	}
	public void setHearbeatInterval( long hearbeatInterval )
	{
		HearbeatInterval = hearbeatInterval;
	}		
	public int getConnectionTimeOut()
	{
		return connectionTimeOut;
	}
	public void setConnectionTimeOut( int connectionTimeOut )
	{
		this.connectionTimeOut = connectionTimeOut;
	}
	public int getReadConnectionTimeOut()
	{
		return readConnectionTimeOut;
	}
	public void setReadConnectionTimeOut( int readConnectionTimeOut )
	{
		this.readConnectionTimeOut = readConnectionTimeOut;
	}
	public int getDisconnectThreshold()
	{
		return disconnectThreshold;
	}

	public void setDisconnectThreshold( int disconnectThreshold )
	{
		this.disconnectThreshold = disconnectThreshold;
	}
	public boolean isEmpty()
    {
        return ( this.hostUrl == null || this.pollUrl == null || this.org == null ||
                    this.username == null || this.password == null);
    }
    
	public static void populateGridApiConfigMap(Map<String, String> serverConfigMap , ConcurrentHashMap<String,GridApiConfig> gridApiConfigMap)
	{
		
		for ( String org : serverConfigMap.keySet() )
		{
			String tradeBridgeOrgConfig = serverConfigMap.get(org);
			GridApiConfig gridApiConfig = gridApiConfigMap.get(org);
			if ( gridApiConfig == null )
			{
				gridApiConfig = new GridApiConfig();
			}
			tradeBridgeOrgConfig = cleanupForWhiteSpaces(tradeBridgeOrgConfig);
			String[] tokens = tradeBridgeOrgConfig.split(",");
			for ( String token : tokens )
			{
				String[] keyValue = token.split("=");
				mapGridApiConfig(keyValue, gridApiConfig);
			}
			gridApiConfigMap.putIfAbsent(org, gridApiConfig);
			log.info("Loaded Grid api config mapping for: " + org + " config->" + gridApiConfig);
		}
	}
	
	@Override
	public String toString()
	{
		return  " Org=" + this.org + 
				" User=" + this.username + 
				" HostUrl=" +this.hostUrl +
				" PollUrl="  + this.pollUrl + 				
				" apiVersion" + this.apiVersion +
				" clientVersion" + this.clientVersion +
				" pollingInterval" + this.pollingInterval +
				" HearbeatInterval" + this.HearbeatInterval +
				" readConnectionTimeOut" + this.readConnectionTimeOut +
				" connectionTimeOut" + this.connectionTimeOut ;				
	}
	
	private static void mapGridApiConfig( String[] values, GridApiConfig config )
	{
		if ( GridApiConfig.GRID_API_ORG_NAME.equals(values[0]) )
		{
			config.setOrg(values[1]);
		}
		else if ( GridApiConfig.GRID_API_SERVER_URL.equals(values[0]) )
		{
			config.setHostUrl(values[1]);
		}
		else if ( GridApiConfig.GRID_API_SERVER_POLL_URL.equals(values[0]) )
		{
			config.setPollUrl(values[1]);
		}
		else if ( GridApiConfig.GRID_API_USERNAME.equals(values[0]) )
		{
			config.setUsername(values[1]);
		}
		else if ( GridApiConfig.GRID_API_PASSWORD.equals(values[0]) )
		{
			config.setPassword(values[1]);
		}
		else if ( GridApiConfig.GRID_API_VERSION.equals(values[0]) )
		{
			config.setApiVersion(values[1]);
		}
		else if ( GridApiConfig.GRID_API_CLIENT_VERSION.equals(values[0]) )
		{
			config.setClientVersion(values[1]);
		}
		else if ( GridApiConfig.GRID_API_CONNECTION_POLLING_INTERVAL.equals(values[0]) )
		{
			config.setPollingInterval(Long.parseLong(values[1]));
		}
		else if ( GridApiConfig.GRID_API_CONNECTION_HEARTBEAT_INTERVAL.equals(values[0]) )
		{
			config.setHearbeatInterval(Long.parseLong(values[1]));
		}
		else if ( GridApiConfig.GRID_API_READ_CONNECTION_TIMEOUT.equals(values[0]) )
		{
			config.setReadConnectionTimeOut(Integer.parseInt(values[1]));
		}
		else if ( GridApiConfig.GRID_API_CONNECTION_TIMEOUT.equals(values[0]) )
		{
			config.setConnectionTimeOut(Integer.parseInt(values[1]));
		}
		else if ( GridApiConfig.GRID_API_DiSCONNECT_THRESHOLD.equals(values[0]) )
		{
			config.setDisconnectThreshold(Integer.parseInt(values[1]));
		}
		else if ( GridApiConfig.GRID_API_POLLING_THRESHOLD.equals(values[0]) )
		{
			config.setPollingThreshold(Integer.parseInt(values[1]));
		}
	}
	
	private static String cleanupForWhiteSpaces( String input )
	{
		String toReturn = null;
		if ( input != null )
		{
			toReturn = input.replaceAll("\\s", "");
		}
		return toReturn;
	}

	public int getPollingThreshold()
	{
		return pollingThreshold;
	}

	public void setPollingThreshold( int pollingThreshold )
	{
		this.pollingThreshold = pollingThreshold;
	}

}