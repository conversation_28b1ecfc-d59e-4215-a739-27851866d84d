package com.integral.gridapi.client;

import com.integral.gridapi.client.config.ApiConnectionConfig;
import com.integral.gridapi.client.message.ResponseMessage;
import com.integral.log.Log;
import com.integral.log.LogFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.zip.GZIPInputStream;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 2/10/15
 * Time: 2:58 PM
 * To change this template use File | Settings | File Templates.
 */
public class AdminApiConnector {

    private static final int RESPONSE_CODE_SUCCESS = 200;
    private static final int RESPONSE_CODE_UNAUTHORIZED = 401;
    private static final int RESPONSE_CODE_EXCEPTION = 500;
    private final ApiConnectionConfig config;

    private Log log = LogFactory.getLog(this.getClass());

    public ResponseMessage getData(String getUrl) {
        ResponseMessage responseMsg = new ResponseMessage();
        HttpURLConnection con = null;
        try {
            URL obj = new URL(getUrl);
            con = (HttpURLConnection) obj.openConnection();
            if (con == null) {
                responseMsg.setStatus(ResponseMessage.Status.FAILED);
                responseMsg.setResponseCode(-1);
                return responseMsg;
            }
            con.setRequestProperty("Accept-Encoding", "gzip, compress");
            con.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            con.setConnectTimeout(config.getConnectionTimeOut());
            con.setReadTimeout(config.getReadConnectionTimeOut());
            responseMsg.setResponseCode(con.getResponseCode());
            if (log.isDebugEnabled()) {
                log.debug("getData(): URL:" + getUrl + "get_response_Code: " + responseMsg.getResponseCode());
            }
            switch (responseMsg.getResponseCode()) {
                case RESPONSE_CODE_SUCCESS: {
                    String response = getResponse(con);
                    if (log.isDebugEnabled()) {
                        log.debug("getData(): URL:" + getUrl + "get_response_msg : " + response);
                    }
                    responseMsg.setData(response);
                    responseMsg.setStatus(ResponseMessage.Status.SUCCESS);
                    return responseMsg;
                }
                case RESPONSE_CODE_UNAUTHORIZED: {
                    log.error("getData():Unauthorised response for get_response_msg:" + responseMsg.getResponseCode());
                    responseMsg.setErrorMessage("Unauthorised response for get_response_msg");
                    responseMsg.setStatus(ResponseMessage.Status.UNAUTHORIZED);
                    return responseMsg;
                }
                case RESPONSE_CODE_EXCEPTION: {
                    log.error("getData():get_response_msg returned exception response code: " + responseMsg.getResponseCode());
                    responseMsg.setErrorMessage("getData():get_response_msg returned exception response code: " + responseMsg.getResponseCode());
                    responseMsg.setStatus(ResponseMessage.Status.UNKNOWN_EXCEPTION);
                    return responseMsg;
                }
                default: {
                    log.error("getData():get_response_msg returned rejected response code: " + responseMsg.getResponseCode());
                    responseMsg.setStatus(ResponseMessage.Status.REJECTED);
                    return responseMsg;
                }
            }
        } catch (Exception e) {
            log.warn("Exception occured while retrieving messages", e);
            responseMsg.setStatus(ResponseMessage.Status.FAILED);
            responseMsg.setErrorMessage(e.getMessage());
        } finally {
            if (null != con) {
                con.disconnect();
            }
        }

        return responseMsg;
    }

    public AdminApiConnector(ApiConnectionConfig config) {
        this.config = config;
    }

    private String getResponse(HttpURLConnection con) throws Exception {
        StringBuilder response = new StringBuilder(1000);
        BufferedReader in = null;
        try {
            if (con.getHeaderField("Content-Encoding") != null && con.getHeaderField("Content-Encoding").equalsIgnoreCase("gzip")) {
                GZIPInputStream gzis = new GZIPInputStream(con.getInputStream());
                InputStreamReader reader = new InputStreamReader(gzis);
                in = new BufferedReader(reader);
            } else {
                in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            }

            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }

        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                log.error("Exception while getting response: " + e);
            }
        }
        return response.toString();
    }

}
