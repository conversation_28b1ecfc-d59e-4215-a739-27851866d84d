/**
 * 
 */
package com.integral.gridapi.client.parser;

/**
 * <AUTHOR>
 *
 */
public enum TradeMessageFormat
{
    EMPTY_1,
    EMPTY_2,
    WF_MSG_ID,
    Ref_ID,
    Event,
    Topic,
    Status,
    Request_REF_ID,
    Request_Modified_Datetime,
    Is_Maker,
    IS_Order_ID,
    Request_Classification,
    Org_Short_Name,
    To_Org_Short_Name,
    User_Short_Name,
    Leg_Price_Name,
    Bid_Offer,
    Tenor,
    Dealt_Ccy_Property,
    Dealt_Currency,
    Settled_Currency,
    Dealt_Amount,
    Trade_Ref_ID,
    Trade_Modification_Datetime,
    IS_Esternal_ID,
    Request_External_ID,
    Trade_Classification,
    Trade_Date,
    Cpty_A,
    Cpty_B,
    TradeState,
    Trade_Leg_Classification,
    Value_Date,
    Currency_1,
    Currency_2,
    Base_Currency,
    Var_Currency,
    Rate,
    Spot_Rate,
    Fwd_Points,
    FX_Rate_Conversion,
    Currency_1_Amount,
    Currency_2_Amount,
    Buying_Currency_1,
    Error_Code,
    Error_Severity,
    Error_Param,
    External_Request_Id,
    CoveredDealId,
   
}
