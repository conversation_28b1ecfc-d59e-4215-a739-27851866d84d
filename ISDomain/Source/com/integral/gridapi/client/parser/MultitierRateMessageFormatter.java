package com.integral.gridapi.client.parser;

import com.integral.gridapi.client.message.MultitierRateMessage;
import com.integral.gridapi.client.message.RateData;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * Created by verma on 11/4/15.
 */
public class MultitierRateMessageFormatter extends RateMessageFormatter
{
    protected Log log = LogFactory.getLog(MultitierRateMessageFormatter.class);

    private static final int INDEX_REQUEST_ID = 1;
    private static final int INDEX_TIMESTAMP = 2;
    private static final int INDEX_CCY_PAIR = 3;

    public MultitierRateMessageFormatter(String regex)
    {
        super(regex);
    }

    /**
     * Example :
     * <JMSMessage id="1286963927"><handlerId>RM</handlerId><expiryTime>1407412499859</expiryTime><JMSHeader></JMSHeader><data type="Text"><![CDATA[[1000,1407370145097,USD/JPY,0,A,0,[0,1,100000.0,EMSB,98.57,],[1,0,100000.0,EMSB,98.57,]]]]></data></JMSMessage></RM><ack></ack></JMSMessages></JMSProxy>
     * @param input
     * @return
     */
    @Override
    public MultitierRateMessage format(String input)
    {
        if( log.isDebugEnabled() ) {
            log.debug("MTRMF: format().input= " + input);
        }

        String [] tokens = input.split(getRegex());

        if(tokens.length < 21)
        {
            if( log.isDebugEnabled() ) {
                log.debug("Rate message data not in expected format: " + input);
            }
            return null;
        }

        MultitierRateMessage msg = new MultitierRateMessage();
        int index = 0;
        for(String token : tokens)
        {
            if(index == INDEX_REQUEST_ID)
            {
                msg.setRequestId(token);
            }
            else if ( index == INDEX_TIMESTAMP )
            {
                msg.setTimestamp(Long.parseLong(token));
            }
            else if(index == INDEX_CCY_PAIR)
            {
                msg.setCurrencyPair(token);
            }

            index++;
        }

        if(input.indexOf('[') == 0)
        {
            input = input.substring(1, input.length());

            if(input.charAt(input.length()-1) == ']')
            {
                input = input.substring(0, input.length()-1);
            }
        }

        populateRateTiers(msg, input);
        return msg;
    }

    protected String populateRateTiers(MultitierRateMessage msg, String input)
    {
        char startToken = '[';
        char endToken = ']';
        int startFromIndex = 0;
        int endFromIndex = 0;
        boolean hasMore = true;

        while(hasMore)
        {
            int startIndex = input.indexOf(startToken, startFromIndex);
            int endIndex = input.indexOf(endToken, endFromIndex);
            if(startIndex == -1 || endIndex == -1)
            {
                hasMore = false;
            } else
            {
                String bidOfferText = input.substring(startIndex+1, endIndex);
                startFromIndex = endIndex+1;
                endFromIndex = endIndex+1;
                processForRateMessage(msg, bidOfferText);
            }
        }

        return null;
    }

    private void processForRateMessage(MultitierRateMessage rateMessage, String input)
    {
        String [] tokens = input.split("[,]");

        if(tokens.length < BID_OFFER_RATE_FORMAT.values().length)
        {
            log.error("Invalid rate data");
            return;
        }

        RateData rateData = new RateData();
        for( int i = 0 ; i < tokens.length ; i++)
        {
            if(i == BID_OFFER_RATE_FORMAT.TYPE.ordinal())
            {
                rateData.setType( tokens[i].equals("0") ? RateData.TYPE.BID : RateData.TYPE.OFFER);
            } else if(i == BID_OFFER_RATE_FORMAT.PROVIDER.ordinal())
            {
                rateData.setProvider(tokens[i]);
            } else if(i == BID_OFFER_RATE_FORMAT.VOLUME.ordinal())
            {
                rateData.setQuantity(Double.parseDouble(tokens[i]));
            } else if(i == BID_OFFER_RATE_FORMAT.RATE.ordinal())
            {
                rateData.setRate(Double.parseDouble(tokens[i]));
            }
        }

        if(rateData.getType() == RateData.TYPE.BID)
        {
            rateMessage.addBidRate(rateData);
        }
        else
        {
            rateMessage.addOfferRate(rateData);
        }
    }

    public static void main(String ...strings)
    {
//        String input = "[1000,1406853324089,EUR/JPY,0,A,0,[0,0,250000.0,HSBC,137.679,],[1,0,250000.0,HSBC,137.687,]]";
        String multitierInput = "[1000,1410374852339,EUR/USD,0,A,0,[0,0,7211000.0,SUCD+,1.29074,],[0,0,4000000.0,ALFN+,1.29073,],[0,0,7836000.0,DB6+,1.29072,],[0,0,3250000.0,FXPRO,1.29071,],[0,0,5500000.0,FXPRO,1.2907,],[0,0,2100000.0,MSFXE+,1.29069,],[0,0,1.0E7,HSBC+,1.29068,],[0,0,1.15E7,FXPRO,1.29067,],[0,0,4000000.0,HSBC+,1.29066,],[0,0,500000.0,FXPRO,1.29064,],[0,0,2000000.0,FXPRO,1.29063,],[0,0,5000000.0,FXPRO,1.29062,],[0,0,500000.0,FXPRO,1.29061,],[0,0,500000.0,FXPRO,1.29059,],[0,0,500000.0,FXPRO,1.29057,],[0,0,1000000.0,FXPRO,1.28943,],[1,0,1500000.0,HSBC+,1.29078,],[1,2,1000000.0,ALFN,1.29079,],[1,0,1.6211E7,MSFXE+,1.2908,],[1,0,4350000.0,FXPRO,1.29081,],[1,0,5100000.0,FXPRO,1.29082,],[1,0,9000000.0,FXPRO,1.29084,],[1,0,1.1E7,FXPRO,1.29085,],[1,0,4400000.0,FXPRO,1.29086,],[1,0,3000000.0,FXPRO,1.29087,],[1,0,500000.0,FXPRO,1.29088,],[1,0,5000000.0,FXPRO,1.2909,],[1,0,2500000.0,FXPRO,1.29091,],[1,0,500000.0,FXPRO,1.29093,],[1,0,500000.0,FXPRO,1.29095,],[1,0,500000.0,FXPRO,1.29097,],[1,0,1000000.0,FXPRO,1.29209,]]";

        new MultitierRateMessageFormatter(RateMessageFormatter.GRID_API_XML_MESSAGE_REGEX).format(multitierInput);
    }

    public enum BID_OFFER_RATE_FORMAT
    {
        TYPE,
        INDEX2,
        VOLUME,
        PROVIDER,
        RATE
    }
}

