package com.integral.gridapi.client.parser;

import com.integral.gridapi.client.message.RateData;
import com.integral.gridapi.client.message.RateMessage;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * Created by verma on 11/4/15.
 */
public class RateMessageFormatter implements IMessageFormatter<RateMessage, String> {
    protected Log log = LogFactory.getLog(RateMessageFormatter.class);

    public static final String GRID_API_XML_MESSAGE_REGEX = "[,\\[\\]]";

    private static final int INDEX_REQUEST_ID = 1;
    private static final int INDEX_TIMESTAMP = 2;
    private static final int INDEX_CCY_PAIR = 3;
    private static final int INDEX_OFFER_RATE_QUANTITY = 18;
    private static final int INDEX_OFFER_RATE_PROVIDER = 19;
    private static final int INDEX_OFFER_RATE = 20;
    private static final int INDEX_BID_RATE_QUANTITY = 10;
    private static final int INDEX_BID_RATE_PROVIDER = 11;
    private static final int INDEX_BID_RATE = 12;

    private String regex;

    public RateMessageFormatter(String regex) {
        this.regex = regex;
    }

    /**
     * Example :
     * <JMSMessage id="**********"><handlerId>RM</handlerId><expiryTime>1407412499859</expiryTime><JMSHeader></JMSHeader><data type="Text"><![CDATA[[1000,1407370145097,USD/JPY,0,A,0,[0,1,100000.0,EMSB,98.57,],[1,0,100000.0,EMSB,98.57,]]]]></data></JMSMessage></RM><ack></ack></JMSMessages></JMSProxy>
     *
     * @param input
     * @return
     */
    @Override
    public RateMessage format(String input) {
        RateMessage msg = new RateMessage();
        RateData offerData = new RateData();
        RateData bidData = new RateData();
        msg.setBidRateData(bidData);
        msg.setOfferRateData(offerData);

        String[] tokens = input.split(regex);

        if (tokens.length < 21) {
            if( log.isDebugEnabled() ) {
                log.debug("Rate message data format is not valid: " + input);
            }
            return null;
        }

        int index = 0;
        for (String token : tokens) {
            if (index == INDEX_REQUEST_ID) {
                msg.setRequestId(token);
            } else if (index == INDEX_TIMESTAMP) {
                msg.setTimestamp(Long.parseLong(token));
            } else if (index == INDEX_CCY_PAIR) {
                msg.setCurrencyPair(token);
            } else if (index == INDEX_OFFER_RATE_QUANTITY) {
                offerData.setQuantity(Double.parseDouble(token));
            } else if (index == INDEX_OFFER_RATE_PROVIDER) {
                offerData.setProvider(token);
            } else if (index == INDEX_OFFER_RATE) {
                offerData.setRate(Double.parseDouble(token));
            } else if (index == INDEX_BID_RATE_QUANTITY) {
                bidData.setQuantity(Double.parseDouble(token));
            } else if (index == INDEX_BID_RATE_PROVIDER) {
                bidData.setProvider(token);
            } else if (index == INDEX_BID_RATE) {
                bidData.setRate(Double.parseDouble(token));
            }

            index++;
        }

        return msg;
    }

    public String getRegex() {
        return this.regex;
    }

    public static void main(String... strings) {
        String input = "[1000,1406853324089,EUR/JPY,0,A,0,[0,0,250000.0,HSBC,137.679,],[1,0,250000.0,HSBC,137.687,]]";

        RateMessage msg = new RateMessageFormatter(GRID_API_XML_MESSAGE_REGEX).format(input);
        System.out.println(msg.getCurrencyPair());
        System.out.println(msg.getOfferRate());
        System.out.println(msg.getBidRate());
    }

}

