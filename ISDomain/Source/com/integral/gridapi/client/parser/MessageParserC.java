package com.integral.gridapi.client.parser;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

import com.integral.gridapi.client.message.RateMessage;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import com.integral.gridapi.client.message.DoNotProcessMessage;
import com.integral.gridapi.client.message.ParsedMessage;
import com.integral.gridapi.client.message.TradeNotificationMessage;
import com.integral.log.Log;
import com.integral.log.LogFactory;


public class MessageParserC implements IMessageParser<ParsedMessage, InputStream>
{
	protected Log log = LogFactory.getLog(MessageParserC.class);

	public static final String RATE_MESSAGE_TAG_NAME = "RM";
	public static final String TRADE_MESSAGE_TAG_NAME = "NH";
	public static final String ACK_ID_TAG_NAME = "ack";
	public static final String JMS_MESSAGE_TAG_NAME = "JMSMessage";
	public static final String DATA_TAG_NAME = "data";

	private DocumentBuilderFactory dbFactory;
	private DocumentBuilder dBuilder;
	private IMessageFormatter<TradeNotificationMessage, String> tradeMessageFormatter;
	private IMessageFormatter<RateMessage, String> rateMessageFormatter;

	public MessageParserC() throws ParserConfigurationException
	{
		dbFactory = DocumentBuilderFactory.newInstance();
		dBuilder = dbFactory.newDocumentBuilder();
		rateMessageFormatter = new MultitierRateMessageFormatter(RateMessageFormatter.GRID_API_XML_MESSAGE_REGEX);
		tradeMessageFormatter = new TradeMessageFormatter(TradeMessageFormatter.GRID_API_XML_MESSAGE_REGEX);
	}

	@Override
	public List<ParsedMessage> parseMessages( InputStream inputStream )
	{
		List<ParsedMessage> parsedMessages = new ArrayList<ParsedMessage>();

		Document doc;
		try
		{
			doc = dBuilder.parse(inputStream);
		}
		catch ( SAXException e )
		{
			log.error("SAX exception while reading from input stream: " + e);
			return parsedMessages;
		}
		catch ( IOException e )
		{
			log.error("IOException while reading from input stream: " + e);
			return parsedMessages;
		}

		log.debug("Root element: " + doc.getDocumentElement().getNodeName());

		String ackId = getAckIdFromJmsMessage(doc);
		//parse rate messages
		extractMessages(doc, parsedMessages,RATE_MESSAGE_TAG_NAME, ackId);

		//parse trade messages
		extractMessages(doc, parsedMessages, TRADE_MESSAGE_TAG_NAME, ackId);

		if ( parsedMessages.size() == 0 && ackId != null )
		{
			// This is done so that we send Ack's for ignored/filtered messages
			ParsedMessage msg = new DoNotProcessMessage();
			//msg.setJmsMessageId(jmsMessageId);
			msg.setAckId(ackId);
			parsedMessages.add(msg);
		}
		return parsedMessages;
	}

	private String getAckIdFromJmsMessage( Document doc )
	{
		String ackId = null;
		NodeList ackMsgList = doc.getElementsByTagName(ACK_ID_TAG_NAME);
		if ( ackMsgList.getLength() > 0 )
		{
			Node ackNode = ackMsgList.item(0);

			if ( ackNode.getNodeType() == Node.ELEMENT_NODE )
			{
				Element eEle = (Element) ackNode;
				if ( eEle.getFirstChild() != null )
					ackId = eEle.getFirstChild().getNodeValue();
			}
		}
		return ackId;
	}

	private void extractMessages( Document doc, List<ParsedMessage> parsedMessages, String messageTag, String ackId )
	{
		NodeList nRateMsgList = doc.getElementsByTagName(messageTag);

		for ( int i = 0 ; i < nRateMsgList.getLength() ; i++ )
		{
			Node nNode = nRateMsgList.item(i);
			log.debug("Current elem: " + nNode.getNodeName());

			if ( nNode.getNodeType() == Node.ELEMENT_NODE )
			{
				Element eEle = (Element) nNode;
				NodeList eList = eEle.getElementsByTagName(JMS_MESSAGE_TAG_NAME); //<JMSMessage> 
				log.debug("Total rate messages = " + eList.getLength());

				for ( int j = 0 ; j < eList.getLength() ; j++ )
				{
					Node eNode = eList.item(j);
					log.debug("\tMessage ele: " + eNode.getNodeName() + " --> ");

					String jmsMessageId = "";
					if ( eNode.hasAttributes() )
					{
						Node idNode = eNode.getAttributes().getNamedItem("id");
						if ( idNode != null )
							jmsMessageId = idNode.getNodeValue();
					}

					if ( eNode.getNodeType() == Node.ELEMENT_NODE )
					{
						Element rateNode = (Element) eNode;
						NodeList dataList = rateNode.getElementsByTagName(DATA_TAG_NAME);

						if ( dataList.getLength() > 0 )
						{
							ParsedMessage msg = null;
							if(RATE_MESSAGE_TAG_NAME.equals(messageTag))
							{
								msg = rateMessageFormatter.format(dataList.item(0).getTextContent());
								if(msg != null)
								{
									msg.setJmsMessageId(jmsMessageId);
									msg.setAckId(ackId);
									parsedMessages.add(msg);
								}
							}
							else if ( TRADE_MESSAGE_TAG_NAME.equals(messageTag) )
							{
								for ( int k = 0 ; k < dataList.getLength() ; k++ )
								{
									String textContent = dataList.item(k).getTextContent();
									msg = tradeMessageFormatter.format(textContent);
									if ( msg != null )
									{
										msg.setJmsMessageId(jmsMessageId);
										msg.setAckId(ackId);
										parsedMessages.add(msg);
									}

								}
							}
						}
					}
				}
			}
		}
	}

	public static void main( String... strings ) throws ParserConfigurationException, SAXException, IOException
	{
		//        String msg = "<JMSProxy pt=\"0\"><S>K</S><event name=\"update\" ts=\"1410389725779\"><status>SUCCESS</status></event><JMSMessages><NH><JMSMessage id=\"319438755\"><handlerId>NH</handlerId><expiryTime>1410432874082</expiryTime><JMSHeader><AckRequired val=\"true\" /><MessageType val=\"ResponseESP_1\" /></JMSHeader><data type=\"Text\"><![CDATA[~[1410389725577|FXI8266002|WITHDRAW|REQUEST|SUCCESS|8144001|2014-09-10 22:55:25 GMT|false|8144001|LIMIT|Broker|FXI|BrokerQuoter|singleLeg|BID||currency1|EUR|USD|1000000.0000|||FXI8266004|null||2014-09-11|BrokerLE1|FXI-le1||||EUR|USD|EUR|USD|1.3384|1.3384|0.0000|STDQOTCNV|1000000.0000|1338400.0000|true||||{ExternalRequestId;FXI8266002}|{BroadcastOrder;true}|{DisplayLimit;0.0000}|{TimeInForce;GTD}|{TradeChannel;BA/ESP}|{OrderType;LIMIT}|{LastModifiedTime;2014-09-10 22:55:25 GMT}|{ExpiryTime;9.871}|{CancelledBy;FXI}|{PlacedByOrg;Broker}|{TWAPSTABS;20140910-22:55:25}|{TWAPETABS;19700101-00:00:00}|{TWAPPT;0.0}|{TWAPSIMls;0}|{TWAPMSIMls;0}|{TWAPSIRF;N}|{TWAPSS;0.0}|{TWAPSRS;0.0}|{TWAPSSRF;N}|{TWAPTOBP;0.0}|{TWAPAOE;0}|{TWAPSFOK;N}|{SEF;true}|{OrigOrderAmount;1000000.0}|{OrigOrderRate;1.3384}|{AvgFillPrice;0.0}|{ISOrderId;8144001}]]]></data></JMSMessage><JMSMessage id=\"338303848\"><handlerId>NH</handlerId><expiryTime>1410432874082</expiryTime><JMSHeader><AckRequired val=\"true\" /><MessageType val=\"ResponseESP_1\" /></JMSHeader><data type=\"Text\"><![CDATA[~[1410389725582|835524389|CREATE|REQUEST|SUCCESS|8144000|2014-09-10 22:55:24 GMT|true|8144000|MARKET|Broker|FXI|BrokerQuoter|singleLeg|OFFER|SPOT|currency1|EUR|USD|1000000.0000|||FXI8266000|null||2014-09-11|BrokerLE1|FXI-le1||||EUR|USD|EUR|USD|1.3384|1.3384|0.0000|STDQOTCNV|1000000.0000|1338400.0000|true||||{ExternalRequestId;835524389}|{BroadcastOrder;true}|{DisplayLimit;1000000.0000}|{TimeInForce;GTD}|{TradeChannel;DNET/PD}|{OrderType;MKT}|{LastModifiedTime;2014-09-10 22:55:24 GMT}|{OrderExcnStrategy;VWAP}|{MarketRange;0.5}|{ExpiryTime;1.0}|{PlacedByOrg;Broker}|{TWAPSTABS;20140910-22:55:24}|{TWAPETABS;19700101-00:00:00}|{TWAPPT;0.0}|{TWAPSIMls;0}|{TWAPMSIMls;0}|{TWAPSIRF;N}|{TWAPSS;0.0}|{TWAPSRS;0.0}|{TWAPSSRF;N}|{TWAPTOBP;0.0}|{TWAPAOE;0}|{TWAPSFOK;N}|{SEF;true}|{OrigOrderAmount;1000000.0}|{OrigOrderRate;1.3384}|{AvgFillPrice;0.0}|{ISOrderId;8144000}|{OrderExcnStrategy;VWAP}]]]></data></JMSMessage><JMSMessage id=\"344162962\"><handlerId>NH</handlerId><expiryTime>1410432874082</expiryTime><JMSHeader><mType val=\"dt\" /><fii val=\"76\" /><MessageType val=\"ResponseESP_1\" /><AckRequired val=\"true\" /><lpi val=\"76\" /></JMSHeader><data type=\"Text\"><![CDATA[~[0|0|CREATE|TRADE|SUCCESS|FXI8266000|2014-09-10 22:55:25 GMT|true|8144000|QUOTED|Broker|SHAH|BrokerQuoter|singleLeg|BID|SPOT|currency1|EUR|USD|1000000.0000|FXI8266002|2014-09-10 22:55:25 GMT|FXI8266002|FXI8266002|FXSpot|2014-09-11|BrokerLE1|SHAHLE1||FXSPOTLEG|2014-09-15|EUR|USD|EUR|USD|1.3384|1.3384|0.0000|STDQOTCNV|1000000.0000|1338400.0000|true||||{ExternalRequestId;835524389}|{RequestTransactionId;FXI8266000}|{RequestDealtAmount;1000000.0000}|{RequestFilledAmount;1000000.0000}|{TriggerRate;0.0}|{InitialTriggerRate;0.0}|{MR;0.0}|{MSR;0.0}|{MFP;0.0}|{PlacedByOrg;Broker}|{maskedLP;Broker}|{UPI;EUR_USD_SPOT}|{UTI;MCQRZU5ST1IN8266002}|{SEF;false}|{allocation;false}|{TriggerRate;0.0}]]]></data></JMSMessage></NH><RM><JMSMessage id=\"396737129\"><handlerId>RM</handlerId><expiryTime>1410432874082</expiryTime><JMSHeader></JMSHeader><data type=\"Text\"><![CDATA[[EURUSDUser1-Broker,1410389725651,EUR/USD,0,A,0,[0,0,4000000.0,Broker,1.3383,],[1,0,1000000.0,Broker,1.3384,]]]]></data></JMSMessage></RM><ack>1410389725579-3,1410389725586-4,1410389725598-5</ack></JMSMessages></JMSProxy>";
		String msg = "<JMSProxy pt=\"0\"><S>K</S><event name=\"update\" ts=\"1410389725779\"><status>SUCCESS</status></event><JMSMessages><NH><JMSMessage id=\"319438755\"><handlerId>NH</handlerId><expiryTime>1410432874082</expiryTime><JMSHeader><AckRequired val=\"true\" /><MessageType val=\"ResponseESP_1\" /></JMSHeader><data type=\"Text\"><![CDATA[~[1410389725577|FXI8266002|WITHDRAW|REQUEST|SUCCESS|8144001|2014-09-10 22:55:25 GMT|false|8144001|LIMIT|Broker|FXI|BrokerQuoter|singleLeg|BID||currency1|EUR|USD|1000000.0000|||FXI8266004|null||2014-09-11|BrokerLE1|FXI-le1||||EUR|USD|EUR|USD|1.3384|1.3384|0.0000|STDQOTCNV|1000000.0000|1338400.0000|true||||{ExternalRequestId;FXI8266002}|{BroadcastOrder;true}|{DisplayLimit;0.0000}|{TimeInForce;GTD}|{TradeChannel;BA/ESP}|{OrderType;LIMIT}|{LastModifiedTime;2014-09-10 22:55:25 GMT}|{ExpiryTime;9.871}|{CancelledBy;FXI}|{PlacedByOrg;Broker}|{TWAPSTABS;20140910-22:55:25}|{TWAPETABS;19700101-00:00:00}|{TWAPPT;0.0}|{TWAPSIMls;0}|{TWAPMSIMls;0}|{TWAPSIRF;N}|{TWAPSS;0.0}|{TWAPSRS;0.0}|{TWAPSSRF;N}|{TWAPTOBP;0.0}|{TWAPAOE;0}|{TWAPSFOK;N}|{SEF;true}|{OrigOrderAmount;1000000.0}|{OrigOrderRate;1.3384}|{AvgFillPrice;0.0}|{ISOrderId;8144001}]]]></data></JMSMessage><JMSMessage id=\"338303848\"><handlerId>NH</handlerId><expiryTime>1410432874082</expiryTime><JMSHeader><AckRequired val=\"true\" /><MessageType val=\"ResponseESP_1\" /></JMSHeader><data type=\"Text\"><![CDATA[~[1410389725582|835524389|CREATE|REQUEST|SUCCESS|8144000|2014-09-10 22:55:24 GMT|true|8144000|MARKET|Broker|FXI|BrokerQuoter|singleLeg|OFFER|SPOT|currency1|EUR|USD|1000000.0000|||FXI8266000|null||2014-09-11|BrokerLE1|FXI-le1||||EUR|USD|EUR|USD|1.3384|1.3384|0.0000|STDQOTCNV|1000000.0000|1338400.0000|true||||{ExternalRequestId;835524389}|{BroadcastOrder;true}|{DisplayLimit;1000000.0000}|{TimeInForce;GTD}|{TradeChannel;DNET/PD}|{OrderType;MKT}|{LastModifiedTime;2014-09-10 22:55:24 GMT}|{OrderExcnStrategy;VWAP}|{MarketRange;0.5}|{ExpiryTime;1.0}|{PlacedByOrg;Broker}|{TWAPSTABS;20140910-22:55:24}|{TWAPETABS;19700101-00:00:00}|{TWAPPT;0.0}|{TWAPSIMls;0}|{TWAPMSIMls;0}|{TWAPSIRF;N}|{TWAPSS;0.0}|{TWAPSRS;0.0}|{TWAPSSRF;N}|{TWAPTOBP;0.0}|{TWAPAOE;0}|{TWAPSFOK;N}|{SEF;true}|{OrigOrderAmount;1000000.0}|{OrigOrderRate;1.3384}|{AvgFillPrice;0.0}|{ISOrderId;8144000}|{OrderExcnStrategy;VWAP}]]]></data></JMSMessage><JMSMessage id=\"344162962\"><handlerId>NH</handlerId><expiryTime>1410432874082</expiryTime><JMSHeader><mType val=\"dt\" /><fii val=\"76\" /><MessageType val=\"ResponseESP_1\" /><AckRequired val=\"true\" /><lpi val=\"76\" /></JMSHeader><data type=\"Text\"><![CDATA[~[0|0|CREATE|TRADE|SUCCESS|FXI8266000|2014-09-10 22:55:25 GMT|true|8144000|QUOTED|Broker|SHAH|BrokerQuoter|singleLeg|BID|SPOT|currency1|EUR|USD|1000000.0000|FXI8266002|2014-09-10 22:55:25 GMT|FXI8266002|FXI8266002|FXSpot|2014-09-11|BrokerLE1|SHAHLE1||FXSPOTLEG|2014-09-15|EUR|USD|EUR|USD|1.3384|1.3384|0.0000|STDQOTCNV|1000000.0000|1338400.0000|true||||{ExternalRequestId;835524389}|{RequestTransactionId;FXI8266000}|{RequestDealtAmount;1000000.0000}|{RequestFilledAmount;1000000.0000}|{TriggerRate;0.0}|{InitialTriggerRate;0.0}|{MR;0.0}|{MSR;0.0}|{MFP;0.0}|{PlacedByOrg;Broker}|{maskedLP;Broker}|{UPI;EUR_USD_SPOT}|{UTI;MCQRZU5ST1IN8266002}|{SEF;false}|{allocation;false}|{TriggerRate;0.0}]]]></data></JMSMessage></NH><RM><JMSMessage id=\"396737129\"><handlerId>RM</handlerId><expiryTime>1410432874082</expiryTime><JMSHeader></JMSHeader><data type=\"Text\"><![CDATA[[EURUSDUser1-Broker,1410374852339,EUR/USD,0,A,0,[0,0,7211000.0,SUCD+,1.29074,],[0,0,4000000.0,ALFN+,1.29073,],[0,0,7836000.0,DB6+,1.29072,],[0,0,3250000.0,FXPRO,1.29071,],[0,0,5500000.0,FXPRO,1.2907,],[0,0,2100000.0,MSFXE+,1.29069,],[0,0,1.0E7,HSBC+,1.29068,],[0,0,1.15E7,FXPRO,1.29067,],[0,0,4000000.0,HSBC+,1.29066,],[0,0,500000.0,FXPRO,1.29064,],[0,0,2000000.0,FXPRO,1.29063,],[0,0,5000000.0,FXPRO,1.29062,],[0,0,500000.0,FXPRO,1.29061,],[0,0,500000.0,FXPRO,1.29059,],[0,0,500000.0,FXPRO,1.29057,],[0,0,1000000.0,FXPRO,1.28943,],[1,0,1500000.0,HSBC+,1.29078,],[1,2,1000000.0,ALFN,1.29079,],[1,0,1.6211E7,MSFXE+,1.2908,],[1,0,4350000.0,FXPRO,1.29081,],[1,0,5100000.0,FXPRO,1.29082,],[1,0,9000000.0,FXPRO,1.29084,],[1,0,1.1E7,FXPRO,1.29085,],[1,0,4400000.0,FXPRO,1.29086,],[1,0,3000000.0,FXPRO,1.29087,],[1,0,500000.0,FXPRO,1.29088,],[1,0,5000000.0,FXPRO,1.2909,],[1,0,2500000.0,FXPRO,1.29091,],[1,0,500000.0,FXPRO,1.29093,],[1,0,500000.0,FXPRO,1.29095,],[1,0,500000.0,FXPRO,1.29097,],[1,0,1000000.0,FXPRO,1.29209,]]]]></data></JMSMessage></RM><ack>1410389725579-3,1410389725586-4,1410389725598-5</ack></JMSMessages></JMSProxy>";
		InputStream stream = new ByteArrayInputStream(msg.getBytes());
		List<ParsedMessage> messages = new MessageParserC().parseMessages(stream);
		System.err.println("Total msg: " + messages.size());
		for ( ParsedMessage pMsg : messages )
		{
			System.out.println(pMsg);
		}
	}
}
