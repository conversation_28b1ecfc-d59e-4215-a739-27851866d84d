/**
 * 
 */
package com.integral.gridapi.client.parser;

import com.integral.gridapi.client.message.TradeNotificationMessage;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * <AUTHOR>
 *
 */
public class TradeMessageFormatter implements IMessageFormatter<TradeNotificationMessage, String>
{
    protected Log log = LogFactory.getLog(TradeMessageFormatter.class);
    
    public static final String GRID_API_XML_MESSAGE_REGEX = "[~|,\\[\\]]";

    private String regex;
    
    public TradeMessageFormatter(String regex)
    {
        this.regex = regex;
    }
    
    @Override
    public TradeNotificationMessage format(String input)
    {
		if( log.isDebugEnabled() ){
			log.debug("format : Input="+input);
		}
        String [] tokens = input.split(regex);
        if ( log.isDebugEnabled() )
            log.debug(".format: Token count: " + tokens.length);
        
        if(tokens.length < TradeMessageFormat.values().length)
        {
            if( log.isDebugEnabled() ) {
				log.debug(".format: trade message in invalid format, ignoring: " + input);
			}
            return null;
        }
        
        TradeNotificationMessage msg = new TradeNotificationMessage();
        
        for( int i = 0 ; i < tokens.length ; i++)
		{
			if ( i == TradeMessageFormat.Event.ordinal() )
			{
				msg.setEvent(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Topic.ordinal() )
			{
				if ( !TradeNotificationMessage.TYPE.equalsIgnoreCase(tokens[i]) )
				{
					// not a TRADE notification message, ignore
					if ( log.isDebugEnabled() )
						log.debug(".format: Invalid TRADE notification msg: " + input);
					return null;
				}
				msg.setTopic(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Status.ordinal() )
			{
				msg.setStatus(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Request_REF_ID.ordinal() )
			{
				msg.setRequestRefId(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Is_Maker.ordinal() )
			{
				msg.setMaker(Boolean.parseBoolean(tokens[i]));
			}
			else if ( i == TradeMessageFormat.IS_Order_ID.ordinal() )
			{
				//IS_Order_ID is null for Manual Trades. Skip those.
				if( tokens[i] == null || tokens[i].trim().equals("")){
					return null;
				}
				msg.setISOrderId(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Request_Classification.ordinal() )
			{
				msg.setReqClassification(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Org_Short_Name.ordinal() )
			{
				msg.setOrgShortName(tokens[i]);
			}
			else if ( i == TradeMessageFormat.To_Org_Short_Name.ordinal() )
			{
				msg.setToOrgShortName(tokens[i]);
			}
			else if ( i == TradeMessageFormat.User_Short_Name.ordinal() )
			{
				msg.setUserShortName(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Leg_Price_Name.ordinal() )
			{
				msg.setLegName(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Dealt_Ccy_Property.ordinal() )
			{
				msg.setDealtCcyProperty(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Bid_Offer.ordinal() )
			{
				msg.setBidOffer(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Dealt_Amount.ordinal() )
			{
				msg.setDealtAmount(Double.parseDouble(tokens[i]));
			}
			else if ( i == TradeMessageFormat.Trade_Ref_ID.ordinal() )
			{
				msg.setTradeRefId(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Request_External_ID.ordinal() )
			{
				msg.setReqExternalId(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Cpty_A.ordinal() )
			{
				msg.setCptyA(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Cpty_B.ordinal() )
			{
				msg.setCptyB(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Base_Currency.ordinal() )
			{
				msg.setBaseCurrency(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Var_Currency.ordinal() )
			{
				msg.setVarCurrency(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Dealt_Currency.ordinal() )
			{
				msg.setDealtCcy(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Settled_Currency.ordinal() )
			{
				msg.setSettledCcy(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Value_Date.ordinal() )
			{
				msg.setValueDate(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Trade_Date.ordinal() )
			{
				msg.setTradeDate(tokens[i]);
			}
			else if ( i == TradeMessageFormat.Rate.ordinal() )
			{
				msg.setRate(Double.parseDouble(tokens[i]));
			}
			else if( i == TradeMessageFormat.Spot_Rate.ordinal())
			{
				msg.setSpotRate(Double.parseDouble(tokens[i]));
			}
			else if ( i == TradeMessageFormat.Currency_1_Amount.ordinal() )
			{
				msg.setCcyAmount1(Double.parseDouble(tokens[i]));
			}
			else if ( i == TradeMessageFormat.Currency_2_Amount.ordinal() )
			{
				msg.setCcyAmount2(Double.parseDouble(tokens[i]));
			}
			else if ( i == TradeMessageFormat.Buying_Currency_1.ordinal() )
			{
				msg.setBuyingBaseCCY(Boolean.parseBoolean(tokens[i]));
			}
			else if ( i == TradeMessageFormat.Tenor.ordinal() )
			{
				msg.setTenor(tokens[i]);
			}
			else if ( i == TradeMessageFormat.External_Request_Id.ordinal() )
			{
				String extId = tokens[47];

				if ( extId != null && extId.contains("ExternalRequestId") )
				{
					msg.setReqExternalId(extractKeyValueProperty(extId));
					msg.setExternalUserId(getExternalUserId(extId));
				}
			}
			else if ( i == TradeMessageFormat.CoveredDealId.ordinal() )
			{
				String coveredDealId = tokens[i];

				if ( coveredDealId != null && coveredDealId.contains("CoveredDealId") )
				{
					msg.setCoveredDealId(extractKeyValueProperty(coveredDealId));
				}
			}else if( i == TradeMessageFormat.Trade_Classification.ordinal()){
				String tradeClassification = tokens[i];
				msg.setTradeClassification(tradeClassification);
			}

		}
        
        if( msg.getTopic() != null && TradeNotificationMessage.TYPE.equalsIgnoreCase(msg.getTopic()) )
        {
            return msg;
        }
        else
        {
            if ( log.isDebugEnabled() )
                log.debug(".format: Not a TRADE notification msg, returning null for: " + input);
            return null;
        }
        
    }
    
    private String getExternalUserId(String externalId)
    {
        String userId = null;
        String delim = "[\" \"; ]";
        String [] tokens = externalId.split(delim);
        if(tokens.length >= 9)
        {
            userId = tokens[3];
        }
        if ( log.isDebugEnabled() )
            log.debug("Extracted external user id: " + userId + " , from ext ref id: " + externalId);
        return userId;
    }
    
    private String extractKeyValueProperty(String coveredDealId)
    {
        String dealId = null;
        String delim = "\\{|;|\\}";
        String [] tokens = coveredDealId.split(delim);
        if(tokens.length >= 3)
        {
        	dealId = tokens[2];
        }
        if ( log.isDebugEnabled() )
            log.debug("Extracted covered deal id: " + dealId + " , from  id: " + coveredDealId);
        return dealId;
    }
   
}
