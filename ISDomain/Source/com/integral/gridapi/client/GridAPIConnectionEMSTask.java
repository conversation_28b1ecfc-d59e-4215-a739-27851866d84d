package com.integral.gridapi.client;

import java.util.concurrent.Callable;

import com.integral.executionservice.client.api.APIOrderRequest;
import com.integral.executionservice.client.api.APIOrderResponse;
import com.integral.executionservice.client.api.APIOrderResponseC;
import com.integral.executionservice.client.api.APIOrderResponse.Status;
import com.integral.log.Log;
import com.integral.log.LogFactory;

public class GridAPIConnectionEMSTask implements Callable<APIOrderResponse>
{

	private final GridAPIConnection connection;

	private Log log;
	APIOrderRequest orderRequest;

	public GridAPIConnectionEMSTask( GridAPIConnection connection , APIOrderRequest orderRequest)
	{
		this.connection = connection;
		this.orderRequest = orderRequest;
		log = LogFactory.getLog(GridAPIConnectionEMSTask.class.getName() + "-" + connection.getOrgName());

	}

	@Override
	public APIOrderResponse call() throws Exception
	{
		APIOrderResponse response = null;
		try
		{
			if ( connection.checkConnection() )
			{
				response = connection.postOrder(orderRequest);
			}
		}
		catch ( Exception e )
		{
			log.error("run : Error in running task", e);
			response = new APIOrderResponseC(orderRequest.getOrder(), Status.FAILED);
		}

		if ( orderRequest.getHandler() != null )
		{
			orderRequest.getHandler().handle(response);
		}
		else
		{
			log.warn("call(): No order handler found for order-> " + orderRequest);
		}
		return response;
	}

}
