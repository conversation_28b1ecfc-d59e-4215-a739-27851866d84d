package com.integral.gridapi.client;

import com.integral.log.Log;
import com.integral.log.LogFactory;

public class GridAPIHearbeatTask  implements Runnable
{

	private final GridAPIConnection connection;
	final String heartbeatURI = "/heartbeat";
	private Log log ;
	public GridAPIHearbeatTask( GridAPIConnection connection )
	{
		this.connection = connection;
		this.log = LogFactory.getLog(GridAPIHearbeatTask.class.getName() + "-" + connection.getOrgName());

	}
	
	public void run()
	{
		try
		{
			if ( log.isDebugEnabled() )
			{
				log.debug("run(): Running heartbeat ");
			}

			if ( connection.checkConnection() )
			{
				connection.sendHeartBeat();
			}

		}
		catch ( Exception e )
		{
			log.warn("Problem with Running Heart Beat task.", e);
		}

	}

}
