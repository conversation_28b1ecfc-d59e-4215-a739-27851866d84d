package com.integral.position;

import com.google.common.collect.ImmutableSet;
import com.integral.event.Filter;
import com.integral.model.AccountOwner;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 7/14/16
 * Time: 5:38 PM
 * To change this template use File | Settings | File Templates.
 */
public class LegalEntityFilterOnOrgShortName extends AbstractFilterOnShortName  {

    public LegalEntityFilterOnOrgShortName(ImmutableSet<String> allowedOrganizations_)
    {
        super(allowedOrganizations_);
    }

    @Override
    public PositionFilterType getId()
    {
        return PositionFilterType.LegalEntity;
    }

    @Override
    public AccountOwner.AccountOwnerLevel getAccountOwnerLevel() {
        return AccountOwner.AccountOwnerLevel.LE;
    }

    @Override
    public String getOrgShortName(Position position) {
        return position.getNamespaceName();
    }
}
