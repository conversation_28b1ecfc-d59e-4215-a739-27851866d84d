package com.integral.position;

public class PositionSubscriptionHelper {

    private static final String TRADING_PARTY_COL_NAME = "TPID";
    private static final String TRADING_PARTY_ORG_COL_NAME = "TPORGID";
    private static final String TRADING_PARTY_USER_COL_NAME = "TPUSERID";
    private static final String ORG_COL_NAME = "ORGID";
    private static final String LE_COL_NAME = "LEID";
    private static final String USER_COL_NAME = "USERID";
    private static final String SETTLEMENT_DATE_COL_NAME = "SETTLEMENTDATE";
    private static final String TRADE_DATE_COL_NAME = "TRADEDATE";
    private static final String CURRENCY_PAIR = "CP";
    private static final String CURRENCY= "Currency";

    public static String getTradingPartyColName()
    {
        return TRADING_PARTY_COL_NAME;
    }

    public static String getSettlementDateColName()
    {
        return SETTLEMENT_DATE_COL_NAME;
    }

    public static String getCurrencyPairColName()
    {
        return CURRENCY_PAIR;
    }

    public static String getCurrencyColName()
    {
        return CURRENCY;
    }
    public static String getTradeDateColName()
    {
        return TRADE_DATE_COL_NAME;
    }

    /**
     * Getter for property 'tradingPartyOrgColName'.
     *
     * @return Value for property 'tradingPartyOrgColName'.
     */
    public static String getTradingPartyOrgColName()
    {
        return TRADING_PARTY_ORG_COL_NAME;
    }

    /**
     * Getter for property 'tradingUserOrgColName'.
     *
     * @return Value for property 'tradingUserOrgColName'.
     */
    public static String getTradingUserOrgColName()
    {
        return TRADING_PARTY_USER_COL_NAME;
    }

    /**
     * Getter for property 'orgColName'.
     *
     * @return Value for property 'orgColName'.
     */
    public static String getOrgColName()
    {
        return ORG_COL_NAME;
    }

    public static String getleColName()
    {
        return LE_COL_NAME;
    }

    /**
     * Getter for property 'userColName'.
     *
     * @return Value for property 'userColName'.
     */
    public static String getUserColName()
    {
        return USER_COL_NAME;
    }

}
