package com.integral.position;

import com.google.common.collect.ImmutableSet;
import com.integral.event.Filter;
import com.integral.model.AccountOwner;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 7/18/16
 * Time: 10:52 AM
 * To change this template use File | Settings | File Templates.
 */
public abstract class AbstractFilterOnShortName implements Filter<Position, PositionFilterType> {

    protected final ImmutableSet<String> allowedOrganizations;

    public AbstractFilterOnShortName(ImmutableSet<String> allowedOrganizations_) {
        allowedOrganizations = allowedOrganizations_;
    }

    @Override
    public boolean accept(Position position) {
        AccountOwner accountOwner = position.getAccountOwner();
        if (accountOwner == null)
            return false;

        AccountOwner.AccountOwnerLevel accountOwnerLevel = accountOwner.getAccountOwnerLevel();
        if (getAccountOwnerLevel() != accountOwnerLevel)
            return false;

        String orgShortName = getOrgShortName(position);

        return allowedOrganizations.contains(orgShortName);
    }

    public abstract AccountOwner.AccountOwnerLevel getAccountOwnerLevel();

    public abstract String getOrgShortName(Position position);
}
