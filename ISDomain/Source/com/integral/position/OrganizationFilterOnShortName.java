package com.integral.position;

import com.google.common.collect.ImmutableSet;
import com.integral.event.Filter;
import com.integral.model.AccountOwner;
import com.integral.model.AccountOwner.AccountOwnerLevel;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.
/**
 * Accept only organization with short name provided on creation.
 * <AUTHOR> Development Corporation
 *
 */
public class OrganizationFilterOnShortName extends  AbstractFilterOnShortName
{
	public OrganizationFilterOnShortName(ImmutableSet<String> allowedOrganizations_)
	{
		super(allowedOrganizations_);
	}
	
	@Override
	public PositionFilterType getId()
	{
		return PositionFilterType.Organization;
	}

    @Override
    public AccountOwnerLevel getAccountOwnerLevel() {
        return AccountOwnerLevel.ORG;
    }

    @Override
    public String getOrgShortName(Position position) {
        return position.getAccountOwner().getEntityName();
    }
}
