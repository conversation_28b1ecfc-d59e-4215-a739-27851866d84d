package com.integral.position;

import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

import com.google.common.collect.Sets;
import com.integral.event.Filter;
import com.integral.event.Listener;
import com.integral.event.Source;
import com.integral.finance.counterparty.UserCounterpartyGroup;
import com.integral.model.AccountOwner;
import com.integral.model.AccountOwner.AccountOwnerLevel;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;
import com.integral.user.User;
import com.integral.util.CollectionUtil;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.
/**
 * Accept only customer organization of sales dealer user. Set of customer organization is updated from DB whenever UserCounterpartyGroup for sales dealer user changes.
 * <AUTHOR> Development Corporation
 *
 */
public final class SalesDealerCustomerFilterOnShortName implements Filter<Position,PositionFilterType>
{
	private final Set<String> _salesDealerCustomerOrganizations = Sets.newConcurrentHashSet();
 	private final AtomicReference<Runnable> _dealerCustomerUpdator = new AtomicReference<Runnable>();
	
	public SalesDealerCustomerFilterOnShortName(Source<UserCounterpartyGroup> userCounterpartyGroupChangeEventSource_, final User salesDealerUser_)
	{
		_salesDealerCustomerOrganizations.add(salesDealerUser_.getOrganization().getShortName());
		_salesDealerCustomerOrganizations.addAll(PositionDistributionManager.getSalesDealerCustomerOrganizationsShortName( salesDealerUser_));
		userCounterpartyGroupChangeEventSource_.addListener(new Listener<UserCounterpartyGroup>()
		{
			@Override
			public void onEvent(UserCounterpartyGroup userCounterpartyGroup)
			{
                if(userCounterpartyGroup.getOrganization().getShortName().equals(salesDealerUser_.getOrganization().getShortName()))
//				if(CollectionUtil.contains(t.getUsers(), salesDealerUser_ ))
				{
					_dealerCustomerUpdator.set(new Runnable()
					{
						@Override
						public void run()
						{
							_salesDealerCustomerOrganizations.clear();
                            _salesDealerCustomerOrganizations.add(salesDealerUser_.getOrganization().getShortName());
							_salesDealerCustomerOrganizations.addAll(PositionDistributionManager.getSalesDealerCustomerOrganizationsShortName( salesDealerUser_));
						}
					});
				}
			}
		});
	}
	
	@Override
	public PositionFilterType getId()
	{
		return PositionFilterType.Organization;
	}

	@Override
	public boolean accept(Position position)
	{
		AccountOwner accountOwner = position.getAccountOwner();
		if(accountOwner == null)
			return false;
		
		AccountOwnerLevel accountOwnerLevel = accountOwner.getAccountOwnerLevel();
		if(AccountOwnerLevel.ORG != accountOwnerLevel)
			return false;
		
		String orgShortName = accountOwner.getEntityName();
		Runnable dealerCustomerUpdator = _dealerCustomerUpdator.getAndSet(null);
		if(dealerCustomerUpdator !=null )
		{
			dealerCustomerUpdator.run();
		}
		return _salesDealerCustomerOrganizations.contains(orgShortName);
	}

}
