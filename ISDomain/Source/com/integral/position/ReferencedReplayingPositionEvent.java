package com.integral.position;

import com.integral.event.Factory;
import com.integral.event.Nothing;
import com.integral.event.ReferencedReplayingEventC;
import com.integral.event.Source;
import com.integral.event.SupplyableReplayingEvent;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;
import com.integral.util.CompositeKeysGeneric;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.
public class ReferencedReplayingPositionEvent extends ReferencedReplayingEventC<String, com.integral.model.Position, PositionFilterType>
{
	private final Factory<PositionCache, Nothing> _cachingStrategyFactory;
	
	public ReferencedReplayingPositionEvent(
			String eventDescription,
			Source<Position> source_,
			Factory<CompositeKeysGeneric, Position> dataToReferenceKeyFactory,
			Factory<PositionCache, Nothing> cachingStrategyFactory_,
			boolean supplyCacheUpdatedValueOnNewUpdate,
			boolean createSegmentEvenWhenNoSubscriber)
	{
		super(eventDescription, source_, dataToReferenceKeyFactory,
				cachingStrategyFactory_, supplyCacheUpdatedValueOnNewUpdate,
				createSegmentEvenWhenNoSubscriber);
		_cachingStrategyFactory = cachingStrategyFactory_;
	}
	
	@Override
	protected SupplyableReplayingEvent<String, Position, PositionFilterType> createUnderlyingReplayingEvent(CompositeKeysGeneric referenceKey)
	{
		return new SupplyablePositionEvent(eventDescription, _cachingStrategyFactory.create(Nothing.Instance), supplyCacheUpdatedValueOnNewUpdate);
	}
}
