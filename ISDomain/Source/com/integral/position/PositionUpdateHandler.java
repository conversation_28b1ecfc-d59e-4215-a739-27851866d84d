/**
 * <AUTHOR>
 */
package com.integral.position;

import com.integral.event.Listener;
import com.integral.model.Position;

/**
 * Callback handler to receive position update from position service. 
 * 
 * Client services are expected to implement this interface. Position service will ensure that handler 
 * will not be called until first call is finished. 
 * <AUTHOR>
 *
 */
public interface PositionUpdateHandler extends Listener<Position>
{
}
