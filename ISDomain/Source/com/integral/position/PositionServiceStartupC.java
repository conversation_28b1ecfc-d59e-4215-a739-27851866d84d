/**
 * <AUTHOR>
 */
package com.integral.position;

import java.util.Hashtable;

import com.google.common.collect.Sets;
import com.google.common.collect.Sets.SetView;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.notification.SubscriptionService;
import com.integral.notification.multicast.MulticastSubscriptionServiceC;
import com.integral.position.notification.multicast.PositionSubscriptionHandler;
import com.integral.system.runtime.StartupTask;
import com.integral.trade.notification.TradeNotificationServiceFactory;
import com.integral.trade.notification.config.TradeNotificationConfigMBean;
import com.integral.util.Converters;
import com.integral.util.StringUtil;

/**
 * <AUTHOR>
 *
 */
public class PositionServiceStartupC implements StartupTask
{
	private static final Log log = LogFactory.getLog(PositionServiceStartupC.class);

	/* 
	 * (non-Javadoc)
	 * @see com.integral.system.runtime.StartupTask#startup(java.lang.String, java.util.Hashtable)
	 */
	@Override
	public String startup( String aName, Hashtable args)
	{
		//init config
		PositionServiceConfig positionServiceConfig = ISFactory.getInstance().getPositionServiceConfig();
		
		//init PDM
		PositionDistributionManager.getInstance();
		
		//init  
		PositionSubscriptionHandler subscriptionHandler = PositionDistributionManager.getInstance().getPositionSubscriptionHandler();
		
		//init Position service
		ServiceFactory.getPositionService();
		
		//start multicast listener.
		SubscriptionService multicastSubscriptionService = new MulticastSubscriptionServiceC(positionServiceConfig, MulticastSubscriptionServiceC.SubscriptionType.UNSAFE_BUFFER);
		
		multicastSubscriptionService.registerSubscriptionHandler(subscriptionHandler);
		
		multicastSubscriptionService.start(false);

        subscriptionHandler.setMulticastSubscriptionService(multicastSubscriptionService);
		
		TradeNotificationConfigMBean tradeNotificationConfig = TradeNotificationServiceFactory.getConfig();
		
		if(!tradeNotificationConfig.isTradeNotificationEnabled()) 
		{
			 log.warn("No positions will be computed because trade notification is disabled");
		}
		else if(!positionServiceConfig.isDisableOldPSGlobally() && tradeNotificationConfig.isTradeNotificationCheckEnabledAtOrgLevel())
		{
			SetView<String> orgForWhichPositionIsEnabledButTradeNotificationNotEnabled = Sets.difference(positionServiceConfig.getEnabledNewPositionServiceOrganization(), tradeNotificationConfig.getTradeNotificationEnabledOrgs());
			if(!orgForWhichPositionIsEnabledButTradeNotificationNotEnabled.isEmpty())
			{
				log.warn(String.format("Unable to compute positions for organizations {%s} because trade notification is not enabled.", StringUtil.join(',', Converters.<String>getPassThroughConverter(), orgForWhichPositionIsEnabledButTradeNotificationNotEnabled)));
			}
		}
		return null;
	}

}
