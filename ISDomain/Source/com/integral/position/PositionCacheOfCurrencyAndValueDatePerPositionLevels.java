package com.integral.position;

import com.google.common.base.Optional;
import com.google.common.collect.ImmutableMap;
import com.integral.event.Filter;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;
import com.integral.model.PositionState;
import com.integral.util.*;

import java.lang.reflect.Array;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by raghunathans on 8/14/17.
 */
public class PositionCacheOfCurrencyAndValueDatePerPositionLevels implements PositionCache
{
    /**
     * {Instrument{valueDate(id, Position}}
     */
    private final ConcurrentHashMap<String, ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>>> unaggregatedPositionMap = new ConcurrentHashMap<String,ConcurrentHashMap<Date,ConcurrentHashMap<String,Position>>>();

    private static final Log log = LogFactory.getLog(PositionCacheOfCurrencyAndValueDatePerPositionLevels.class);

    /*
     * Cache is arranged in 'Instrument{valueDate(id,Position}' structure. So provide filters in this order. The missing level will be considered as wild card.
     */
    @Override
    public Iterable<Position> getByCriteria(Iterable<Filter<Position, PositionFilterType>> criterias)
    {
        if(unaggregatedPositionMap.isEmpty())
        {
            log.debug("cache is empty");
            return Collections.emptyList();
        }

        final Map<String, Position> relevantPositionList = getAggregatedPositionMatchingFilter(Filter.Util.<Position, PositionFilterType>combineAsAnd(criterias));

        log.debug(new MessageBuilder()
        {
            @Override
            public String build()
            {
                return StringUtil.fastConcat(" aggregated values for position are : {", StringUtil.join(',', Converters.ToString.<Position>getToStringConverter(), relevantPositionList.values()));
            }
        });

        return relevantPositionList.values();
    }

    @Override
    public Optional<Position> getAggregatedPosition(String currencyPair, Iterable<Filter<Position, PositionFilterType>> filters)
    {
        ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>> currencyDateBucket = unaggregatedPositionMap.get(currencyPair);
        List<Position> childrenWithDifferentValueDate = new ArrayList<Position>();

        List<Position> childrenWithDifferentAccountOwnerVal;
        for (Map.Entry<Date, ConcurrentHashMap<String,Position>> valueDateBucket : currencyDateBucket.entrySet())
        {
            childrenWithDifferentAccountOwnerVal = new ArrayList<Position>();
            ConcurrentHashMap<String,Position> positions = valueDateBucket.getValue();
            for (Map.Entry<String, Position> entry:positions.entrySet()){
                if(Filter.Util.matchesAll(entry.getValue(), filters))
                {
                    childrenWithDifferentAccountOwnerVal.add(entry.getValue());
                }
            }

            if(!childrenWithDifferentAccountOwnerVal.isEmpty()){
                //account owner level aggregated positions
                childrenWithDifferentValueDate.add(PositionAggregator.AccountOwnerLevel.convert(childrenWithDifferentAccountOwnerVal));
            }
        }

        if(childrenWithDifferentValueDate.isEmpty())
        {
            return Optional.<Position>absent();
        }

        return Optional.<Position>of(PositionAggregator.CurrencyLevel.convert(childrenWithDifferentValueDate));
    }


    /*
     * Iterate over all cached positions. Aggregate all positions passing filter criteria on currency pair and add it to  'relevantPositionList'.
     */
    private Map<String, Position> getAggregatedPositionMatchingFilter(Filter<Position, PositionFilterType> filter)
    {
        Map<String, Position> relevantPositionList = new HashMap<String, Position>(); // All positions meeting all criteria.

        // iterating on currency bucket
        for (Map.Entry<String, ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>>> currencyBucket : unaggregatedPositionMap.entrySet())
        {

            List<Position> childrenWithDifferentValueDate = new ArrayList<Position>();

            // lookup value date bucket to identify relevant child and build aggregated position while iterating
            for (Map.Entry<Date, ConcurrentHashMap<String,Position>> valueDateBucket : currencyBucket.getValue().entrySet())
            {
                List<Position> childrenWithDifferentAccountOwnerVal = new ArrayList<Position>();
                for (Position positionForValueDate : valueDateBucket.getValue().values())
                {
                    // filter valid position as per defined filters
                    if(filter.accept(positionForValueDate))
                    {
                        childrenWithDifferentAccountOwnerVal.add(positionForValueDate);
                    }
                }

                if(!childrenWithDifferentAccountOwnerVal.isEmpty()){
                    //account owner level aggregated positions
                    childrenWithDifferentValueDate.add(PositionAggregator.AccountOwnerLevel.convert(childrenWithDifferentAccountOwnerVal));
                }

            }

            // its is possible to have no relevant position in cache
            if(!childrenWithDifferentValueDate.isEmpty())
            {
                // placeholder for aggregated position at currency pair level
                Position positionAggregatedOnCurrencyPair = PositionAggregator.CurrencyLevel.convert(childrenWithDifferentValueDate);
                relevantPositionList.put(currencyBucket.getKey(), positionAggregatedOnCurrencyPair);
            }
        }

        return relevantPositionList;
    }

    /**
     * This method will return <b>unaggregated</b> position with mentioned id. This operation is costly because it has to iterate full cache for this, please consider using <i>getByCriteria(Iterable{Filter{Position, PositionFilterType}})</i>
     */
    @Override
    public Optional<Position> get(final String id)
    {
        for (Map.Entry<String, ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>>> entry : unaggregatedPositionMap.entrySet())
        {
            for(ConcurrentHashMap<String,Position> positions : entry.getValue().values())
            {
                Position position = positions.get(id);
                if(null!=position)
                {
                    return Optional.of(position);
                }
            }
        }
        return Optional.absent();
    }

    @Override
    public Position cache(Position position)
    {
        // get currency pair bucket
        String currencyPair = position.getCurrencyPair();
        ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>> currencyBucket = getOrCreateInstrumentBucket(currencyPair);

        ConcurrentHashMap<String,Position> valueDateBucket = getOrCreateValueDateLevelCache(position.getValueDate(),currencyBucket);

        if(PositionState.ROLLED == position.getState())
        {
            // remove rolled position from cache
            valueDateBucket.remove(position.get_id());
            if(log.isDebugEnabled()){
                log.debug("PositionLevels.cache():Removing following position from the cache:"+position);
            }
        }
        else
        {
            // update position for value date
            valueDateBucket.put(position.get_id(), position);
            if(log.isDebugEnabled()){
                log.debug("PositionLevels.cache():Updated following position into the cache:"+position);
            }
        }

        return position;
    }


    private ConcurrentHashMap<String,Position> getOrCreateValueDateLevelCache(Date valueDate,ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>> currencyLevelCache)
    {
        ConcurrentHashMap<String,Position> valueDateBucket = currencyLevelCache.get(valueDate);
        if(valueDateBucket==null)
        {
            ConcurrentHashMap<String,Position> previousValue = currencyLevelCache.putIfAbsent(valueDate, valueDateBucket = new ConcurrentHashMap<String,Position>());
            if(previousValue != null)
            {
                valueDateBucket = previousValue;
            }
            else
            {
                log.infoAsFormat("PositionCacheOfCurrencyAndValueDatePerPositionLevels:new position bucket created for value date {%s}", valueDate);
            }
        }
        return valueDateBucket;
    }

    /*
     * Defining no filter is equivalent calling clear on cache. </p> <b>Note:</b> remove operation reply on position server for periodic updates to re-populate and aggregate cache.
     */
    @Override
    public Iterable<Position> removeByCriteria(Iterable<Filter<Position, PositionFilterType>> criterias)
    {
        final Iterable<Position> removedPositionList = clearAllPositionsMatchingFilter(Filter.Util.<Position, PositionFilterType>combineAsAnd(criterias));

        log.debug("Removing following positions from cache:");
        log.debug(new MessageBuilder()
        {
            @Override
            public String build()
            {
                return StringUtil.join(',', Converters.ToString.<Position>getToStringConverter(), removedPositionList);
            }
        });

        return removedPositionList;
    }

    /**
     * @param filter
     * @return all position removed from cache
     */
    private List<Position> clearAllPositionsMatchingFilter(Filter<Position, PositionFilterType> filter)
    {
        if(unaggregatedPositionMap.isEmpty())
        {
            return Collections.emptyList();
        }

        List<Position> removedPositionList = new ArrayList<Position>();

        // iterating on currency bucket
        for (Map.Entry<String, ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>>> currencyBucket : unaggregatedPositionMap.entrySet())
        {
            for (Map.Entry<Date, ConcurrentHashMap<String,Position>> valueDateBucket : currencyBucket.getValue().entrySet())
            {
                Iterator<Position> iterator = valueDateBucket.getValue().values().iterator();

                while(iterator.hasNext()){
                // filter valid position as per defined filters
                    Position positionForValueDate = iterator.next();
                    try
                    {
                        if(filter.accept(positionForValueDate))
                        {
                            removedPositionList.add(positionForValueDate);
                            iterator.remove();
                        }
                    }
                    catch (Exception e)
                    {
                        log.error("Exception while matching position{"+positionForValueDate+"} to remove from cache", e);
                    }
                }
            }
        }

        return removedPositionList;
    }

    /*
     * Method will simply remove position with provided 'id'. Cache will return removed position.
     */
    @Override
    public Optional<Position> remove(String id)
    {
        // iterating on currency bucket
        for (Map.Entry<String, ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>>> currencyBucket : unaggregatedPositionMap.entrySet())
        {
            for (Map.Entry<Date, ConcurrentHashMap<String,Position>> valueDateBucket : currencyBucket.getValue().entrySet())
            {
                // lookup value date bucket to identify relevant child and build aggregated position while iterating
                Map<String, Position> positions = valueDateBucket.getValue();

                Position removed = positions.remove(id);
                    if(null != removed)
                    {
                        return Optional.of(removed);
                }
            }
        }

        return Optional.absent();
    }

    /**
     * Recompute internal structure of cache. This can be used in scenario wherein cache is corrupted because of bad entry. You evicted that entry and now you want internal structure to re-organize.
     */
    protected void refresh()
    {
        log.info("refreshing cache");
    }


    public void clear()
    {
        log.info("clearing cache");
        // As its clearing resuse structure don't destroy
        for(ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>> currentBucket : unaggregatedPositionMap.values())
        {
            currentBucket.clear();
        }
    }

    private ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>> getOrCreateInstrumentBucket(String currencyPair)
    {
        ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>> instrumentBucket = unaggregatedPositionMap.get(currencyPair);
        if(instrumentBucket==null)
        {
            ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>> previousValue = unaggregatedPositionMap.putIfAbsent(currencyPair, instrumentBucket = new ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>>());
            if(previousValue != null)
            {
                instrumentBucket = previousValue;
            }
            else
            {
                log.info(String.format("PositionCacheOfCurrencyAndValueDatePerPositionLevels:new position bucket created for currency pair{%s}", currencyPair));
            }
        }
        return instrumentBucket;
    }



    @Override
    public String getCacheDetails()
    {
        return StringUtil.<String, ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>>>join(unaggregatedPositionMap, ",", "=", Converters.<String>getPassThroughConverter(), new Converter<ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>>, String>()
        {
            @Override
            public String convert(ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>> from_)
            {
                return convertInnerMap(from_);
            }

            public String convertInnerMap(Map<Date, ConcurrentHashMap<String,Position>> dateVsPositions)
            {
                return StringUtil.<Date, ConcurrentHashMap<String,Position>>join(dateVsPositions,",", "=",Converters.ToString.<Date>getToStringConverter(),new Converter<ConcurrentHashMap<String, Position>, String>() {
                    public String convert(ConcurrentHashMap<String, Position> positions) {
                        return StringUtil.<String, Position>join(positions,",", "=",Converters.<String>getPassThroughConverter(),Converters.ToString.<Position>getToStringConverter());
                    }
                });
            }
        });
    }

    @Override
    public String getCacheDetails(final Converter<Position, String> positionToStringConverter)
    {
        return StringUtil.<String, ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>>>join(unaggregatedPositionMap, ",", "=", Converters.<String>getPassThroughConverter(), new Converter<ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>>, String>()
        {
            @Override
            public String convert(ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>> from_)
            {
               return convertInnerMap(from_);
            }

            public String convertInnerMap(Map<Date, ConcurrentHashMap<String,Position>> dateVsPositions)
            {
                 return StringUtil.<Date, ConcurrentHashMap<String,Position>>join(dateVsPositions,",", "=",Converters.ToString.<Date>getToStringConverter(),new Converter<ConcurrentHashMap<String, Position>, String>() {
                     public String convert(ConcurrentHashMap<String, Position> positions) {
                         return StringUtil.<String, Position>join(positions,",", "=",Converters.<String>getPassThroughConverter(),positionToStringConverter);
                     }
                 });
            }
        });
    }

    public ImmutableMap<String, ImmutableMap<Date,ImmutableMap<String, Position>>> getImmutableViewOfUnaggregatedPositions()
    {
        ImmutableMap.Builder<String, ImmutableMap<Date, ImmutableMap<String, Position>>> currencyCachebuilder = ImmutableMap.<String, ImmutableMap<Date, ImmutableMap<String, Position>>>builder();
        for (Map.Entry<String, ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>>> currencyLevelCache : unaggregatedPositionMap.entrySet())
        {
            ImmutableMap.Builder<Date, ImmutableMap<String, Position>> vdCachebuilder = ImmutableMap.<Date, ImmutableMap<String, Position>>builder();
            for(Map.Entry<Date, ConcurrentHashMap<String,Position>> valueDateLevelCache : currencyLevelCache.getValue().entrySet())
            {

                vdCachebuilder.put(valueDateLevelCache.getKey(), ImmutableMap.<String, Position>copyOf(valueDateLevelCache.getValue()));
            }

            currencyCachebuilder.put(currencyLevelCache.getKey(), vdCachebuilder.build());
        }
        return currencyCachebuilder.build();
    }

    public ImmutableMap<String, Position> getImmutableViewOfAggregatedPositions()
    {
        return ImmutableMap.<String, Position>copyOf(getAggregatedPositionMatchingFilter(Filter.Util.<Position, PositionFilterType>passAllFilter()));
    }

    @Override
    public Iterator<Position> iterator()
    {
        List<Iterator<Position>> iterators = new ArrayList<Iterator<Position>>();

        for(ConcurrentHashMap<Date, ConcurrentHashMap<String,Position>> cur : unaggregatedPositionMap.values())
        {
            for (ConcurrentHashMap<String, Position> values : cur.values()){
                 iterators.add(values.values().iterator());
            }
        }

        return CollectionUtil.<Position>join(iterators.toArray((Iterator<Position>[]) Array.newInstance(Iterator.class, 0)));
    }
}
