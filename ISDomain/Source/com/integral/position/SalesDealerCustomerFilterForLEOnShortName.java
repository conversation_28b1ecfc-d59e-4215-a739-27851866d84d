package com.integral.position;

import com.google.common.collect.Sets;
import com.integral.event.Filter;
import com.integral.event.Listener;
import com.integral.event.Source;
import com.integral.finance.counterparty.UserCounterpartyGroup;
import com.integral.model.AccountOwner;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;
import com.integral.user.User;
import com.integral.util.CollectionUtil;

import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Created by raghunathans on 8/30/17.
 */
public class SalesDealerCustomerFilterForLEOnShortName implements Filter<Position,PositionFilterType>
{
    public static final String DELIMITER = "_";
    private final Set<String> _salesDealerLegalEntities = Sets.newConcurrentHashSet();
    private final AtomicReference<Runnable> _dealerCustomerUpdator = new AtomicReference<Runnable>();

    public SalesDealerCustomerFilterForLEOnShortName(Source<UserCounterpartyGroup> userCounterpartyGroupChangeEventSource_, final User salesDealerUser_)
    {
        _salesDealerLegalEntities.addAll(PositionDistributionManager.getSalesDealerLegalEntities(salesDealerUser_));
        _salesDealerLegalEntities.addAll(PositionDistributionManager.getLegalEntitiesForUser(salesDealerUser_));
        userCounterpartyGroupChangeEventSource_.addListener(new Listener<UserCounterpartyGroup>()
        {
            @Override
            public void onEvent(UserCounterpartyGroup userCounterpartyGroup)
            {
                if(userCounterpartyGroup.getOrganization().getShortName().equals(salesDealerUser_.getOrganization().getShortName()))
//				if(CollectionUtil.contains(t.getUsers(), salesDealerUser_ ))
                {
                    _dealerCustomerUpdator.set(new Runnable()
                    {
                        @Override
                        public void run()
                        {
                            _salesDealerLegalEntities.clear();
                            _salesDealerLegalEntities.addAll(PositionDistributionManager.getSalesDealerLegalEntities(salesDealerUser_));
                            _salesDealerLegalEntities.addAll(PositionDistributionManager.getLegalEntitiesForUser(salesDealerUser_));
                        }
                    });
                }
            }
        });
    }

    @Override
    public PositionFilterType getId()
    {
        return PositionFilterType.LegalEntity;
    }

    @Override
    public boolean accept(Position position)
    {
        AccountOwner accountOwner = position.getAccountOwner();
        if(accountOwner == null)
            return false;

        AccountOwner.AccountOwnerLevel accountOwnerLevel = accountOwner.getAccountOwnerLevel();
        if(AccountOwner.AccountOwnerLevel.LE != accountOwnerLevel)
            return false;

        String orgShortName = position.getNamespaceName();
        String fullLEName = orgShortName+ DELIMITER + accountOwner.getEntityName();

        Runnable dealerCustomerRefresher = _dealerCustomerUpdator.getAndSet(null);
        if(dealerCustomerRefresher !=null )
        {
            dealerCustomerRefresher.run();
        }
        return _salesDealerLegalEntities.contains(fullLEName);
    }

}
