package com.integral.position;

import com.google.common.base.Optional;
import com.integral.event.CacheOfIdentifiable;
import com.integral.position.persistence.PositionTradeAudit;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.
public class PositionTradeAuditCache extends CacheOfIdentifiable<String, PositionTradeAudit>
{
	public PositionTradeAuditCache()
	{
		super(PositionTradeAuditCache.class.getSimpleName());
	}
	
	@Override
	public PositionTradeAudit cache(PositionTradeAudit t)
	{
		Optional<PositionTradeAudit> current = get(t.getId());
		
		if(current.isPresent())
		{
			return super.cache(current.get().combine(t));
		}
		
		return super.cache(t);
	}

}
