package com.integral.position;

import com.integral.model.Position;

import java.util.Collection;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * For each currency pair this cache will maintain aggregated position across organization. This map holds organization short name  vs organization level cache
 * <AUTHOR> Development Corporation
 *
 */
public class AggregatedCurrencyLevelConcurrentHashMap extends AggregatedConcurrentHashMap<Date, AggregatedValueDateLevelConcurrentHashMap>
{
    private static final long serialVersionUID = 1L;
    @Override
    protected Collection<Position> getValuesToAggregate()
    {
        // for Currency level cache aggregating aggregated positions at value date level
        List<Position> list = new LinkedList<Position>();
        for(AggregatedValueDateLevelConcurrentHashMap valueDateLevel : values())
        {
            Position aggregatedPosition = valueDateLevel.getAggregatedPosition();
            if(aggregatedPosition==null)
            {
                valueDateLevel.updateAggregatedValueAcrossMap();
                aggregatedPosition = valueDateLevel.getAggregatedPosition();
            }
            if(aggregatedPosition!=null)
            {
                list.add(aggregatedPosition);
            }
        }
        return list;
    }

    @Override
    protected PositionAggregator getAggregator()
    {
        return PositionAggregator.CurrencyLevel;
    }
}
