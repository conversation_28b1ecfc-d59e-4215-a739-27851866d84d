package com.integral.position;

import com.integral.model.Position;

import java.util.Collection;

/**
 * For each currency pair and value date combination there will be
 * multiple positions corresponding to various organizations, represented by
 * this map. This map maintains aggregated position across all organizations,
 * which means aggregated position for specific combination of currency pair
 * and value date.
 *
 * <AUTHOR> Development Corporation
 *
 */
public class AggregatedValueDateLevelConcurrentHashMap extends AggregatedConcurrentHashMap<String, Position> {
    private static final long serialVersionUID = 1L;

    @Override
    protected Collection<Position> getValuesToAggregate() {
        return values();
    }

    @Override
    protected PositionAggregator getAggregator() {
        return PositionAggregator.ValueDateLevel;
    }
}
