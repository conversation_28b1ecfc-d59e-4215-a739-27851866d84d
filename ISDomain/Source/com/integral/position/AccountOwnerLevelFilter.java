package com.integral.position;

import com.integral.event.Filter;
import com.integral.model.AccountOwner;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;

/**
 * Created by raghunathans on 8/14/17.
 */
public class AccountOwnerLevelFilter implements Filter<Position,PositionFilterType>
{
    AccountOwner.AccountOwnerLevel level;

    public AccountOwnerLevelFilter(AccountOwner.AccountOwnerLevel level){
        this.level = level;
    }

    @Override
    public PositionFilterType getId()
    {
        return PositionFilterType.CurrencyPair;
    }

    @Override
    public boolean accept(Position position)
    {
        AccountOwner accountOwner = position.getAccountOwner();
        if(accountOwner == null)
            return false;

        AccountOwner.AccountOwnerLevel accountOwnerLevel = accountOwner.getAccountOwnerLevel();
        return this.level == accountOwnerLevel;
    }

}
