package com.integral.position;

import java.util.Date;

import com.integral.time.DateTimeFactory;
import org.apache.commons.lang.time.DateUtils;

import com.integral.event.Filter;
import com.integral.finance.positions.service.PositionSubscriptionInfo;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;
import com.integral.time.IdcDate;

public class ValueDateFilter implements Filter<Position,PositionFilterType>
{
	private final PositionSubscriptionInfo request;
	
	public ValueDateFilter(PositionSubscriptionInfo request) 
	{
		this.request = request;
	}

	@Override
	public boolean accept(Position position) 
	{
		IdcDate fromSettlementDate = request.getFromSettlementDate();
		IdcDate toSettlementDate = request.getToSettlementDate();
		
		return isInValueDateRange(position, fromSettlementDate!=null?fromSettlementDate.asJdkDate():null,
				toSettlementDate!=null?toSettlementDate.asJdkDate():null);
	}

	public static boolean isInValueDateRange(Position position, Date fromSettlementDate, Date toSettlementDate)
	{
        Date valueDate = position.getValueDate();

        //validating the position value date with current trade date this ensure after 2:00 pm old value date positions are not shown

        IdcDate posDate = DateTimeFactory.newDate(valueDate);
        if(!posDate.isLaterThanOrEqualTo(getCurrentTradeDate())){
            return false;
        }

		if(fromSettlementDate == null && toSettlementDate == null)
		{
			return true; // accept data as no range specified
		}

		return isInValueDateRange(valueDate, fromSettlementDate, toSettlementDate);
	}
	
	public static boolean isInValueDateRange(Date valueDate, Date fromSettlementDate, Date toSettlementDate)
	{
		if(fromSettlementDate == null)
		{
			return DateUtils.isSameDay(valueDate, toSettlementDate) || valueDate.before(toSettlementDate);
		}
		
		if(toSettlementDate == null)
		{
			return DateUtils.isSameDay(valueDate, fromSettlementDate) || valueDate.after(fromSettlementDate);
		}
		
		return DateUtils.isSameDay(valueDate, fromSettlementDate) ||
				DateUtils.isSameDay(valueDate, toSettlementDate) ||
				(valueDate.before(toSettlementDate) && valueDate.after(fromSettlementDate));
	}

	@Override
	public PositionFilterType getId() 
	{
		return PositionFilterType.ValueDate;
	}
	
	@Override
	public String toString() 
	{
		return "Filter: ValueDate";
	}

    public static IdcDate getCurrentTradeDate() {
        return com.integral.is.finance.businessCenter.EndOfDayService.getCurrentTradeDate();
    }
}
