/**
 * <AUTHOR>
 */
package com.integral.position;

import com.integral.model.Position;
import com.integral.position.PositionDistributionManager.PositionSubscriptionRequest;
import com.integral.util.collections.ConcurrentHashSet;

/**
 * <AUTHOR>
 *
 */
public class PositionDistributionList
{
	ConcurrentHashSet<PositionSubscriptionRequest> subscribers = new ConcurrentHashSet<PositionSubscriptionRequest>();	
	
	String id;
	
	public PositionDistributionList(String namespace,String positiontype)
	{
		this.id = namespace + '-' + positiontype; 
	}

	/**
	 * @param psr
	 */
	public void add( PositionSubscriptionRequest psr )
	{
		subscribers.add(psr);
	}

	/**
	 * @param wrapper
	 */
	public void remove( PositionSubscriptionRequest wrapper )
	{
		subscribers.remove(wrapper);
	}

	/**
	 * @param position
	 */
	public void distribute( Position position )
	{
		long positionvd = position.getValueDate().getTime();
		for ( PositionSubscriptionRequest request : subscribers )
		{
			if( request.info.getFromSettlementDate().asJdkDate().getTime() >=  positionvd && request.info.getToSettlementDate().asJdkDate().getTime() <= positionvd  )
			{
				request.handler.onEvent(position);
			}
		}
	}
	
	
	public String toString()
	{
		return id; 
	}
	
	
}
