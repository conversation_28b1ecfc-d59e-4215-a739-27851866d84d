package com.integral.position;

import java.util.Collection;

import com.integral.model.Position;
import com.integral.model.PositionState;
import com.integral.util.Converter;

/**
 * Perform aggregation of child position at various value dates. All child must be of same currency pair.  
 * <AUTHOR> Development Corporation
 *
 */
public class PositionAggregatorOnValueDate implements Converter<Collection<Position>, Position>
{

	/* 
	 * Aggregate collection of children and provide aggregated position.
	 */
	@Override
	public Position convert(Collection<Position> from_)
	{
		Position aggregatedPosition = null;
		
		for (Position position : from_) 
		{
			if(aggregatedPosition == null)
			{
				aggregatedPosition = position.clone();
				aggregatedPosition.setAggregatedPositionLevelIndex(position.getAggregatedPositionLevelIndex()+1);
				setCommonFields(aggregatedPosition);
				continue; // I don't want to combine this position to itself :)
			}
			
			// defensive check to ensure we are aggregating child with same currency pair
			if(!aggregatedPosition.getCurrencyPair().equalsIgnoreCase(position.getCurrencyPair()))
			{
				throw new RuntimeException("Children of aggregated position must be of same currency pair");
			}
			
			aggregatedPosition = combine(aggregatedPosition, position);
		}
		
		aggregatedPosition.setChildren(from_);
		
		return aggregatedPosition;
	}

    /*
	 * Aggregate collection of children and provide aggregated position.
	 */
    public Position convertViewable(Collection<Position> from_)
    {
        Position aggregatedPosition = null;

        for (Position position : from_)
        {
            if(aggregatedPosition == null)
            {
                aggregatedPosition = position.clone();
                aggregatedPosition.setAggregatedPositionLevelIndex(position.getAggregatedPositionLevelIndex()+1);
                aggregatedPosition.setAggregatedPosition(true);
                aggregatedPosition.setState(PositionState.OPEN);
                aggregatedPosition.setAggregatedPositionLevel("ValueDate");
                aggregatedPosition.setValueDate(null);
                aggregatedPosition.set_id(position.get_id());
                continue; // I don't want to combine this position to itself :)
            }

            // defensive check to ensure we are aggregating child with same currency pair
            if(!aggregatedPosition.getCurrencyPair().equalsIgnoreCase(position.getCurrencyPair()))
            {
                throw new RuntimeException("Children of aggregated position must be of same currency pair");
            }

            aggregatedPosition = combine(aggregatedPosition, position);
        }

        aggregatedPosition.setChildren(from_);

        return aggregatedPosition;
    }

	/**
	 * Update first argument with second such that it reflect aggregated position. <b>Do not mutate Second argument</b>
	 * @param position1 aggregated position
	 * @param position2 position to be included in aggregated position
	 * @return
	 */
	public Position combine(Position first, Position second)
	{
		// Base buy and sell
		final double aggregatedBaseBuyAmount = first.getBaseBuyAmount() + second.getBaseBuyAmount();
		first.setBaseBuyAmount(aggregatedBaseBuyAmount);
		final double aggregatedBaseSellAmount = first.getBaseSellAmount() + second.getBaseSellAmount();
		first.setBaseSellAmount(aggregatedBaseSellAmount);
		
		// term buy and sell
		final double aggregatedTermBuyAmount = first.getTermBuyAmount() + second.getTermBuyAmount();
		first.setTermBuyAmount(aggregatedTermBuyAmount);
		final double aggregatedTermSellAmount = first.getTermSellAmount() + second.getTermSellAmount();
		first.setTermSellAmount(aggregatedTermSellAmount);
		
		// Aggregated amounts
		final double baseCurrAmtAggregated = first.getBaseCurrAmt() + second.getBaseCurrAmt();
		first.setBaseCurrAmt(baseCurrAmtAggregated);
		final double termCurrAmtAggregated = first.getTermCurrAmt() + second.getTermCurrAmt();
		first.setTermCurrAmt(termCurrAmtAggregated);
		
		// Rate
		first.setRate(termCurrAmtAggregated/baseCurrAmtAggregated);
		first.setAverageBuyRate(aggregatedTermSellAmount/aggregatedBaseBuyAmount);
		first.setAverageSellRate(aggregatedTermBuyAmount/aggregatedBaseSellAmount);
		
		// PnL
		first.setRealizedPNL(first.getRealizedPNL() + second.getRealizedPNL());
		first.setUnrealizedPnl(first.getUnrealizedPnl() + second.getUnrealizedPnl());
		
		return first;
	}
	
	
	/**
	 * setting common fields (independent of children) to aggregated position. Example: Position state (open/close), aggregation level and indicator.  
	 * @param aggregatedPosition
	 */
	public void setCommonFields(Position aggregatedPosition)
	{
		aggregatedPosition.setAggregatedPosition(true);
		aggregatedPosition.setState(PositionState.OPEN);
		aggregatedPosition.setAggregatedPositionLevel("ValueDate");
		aggregatedPosition.setValueDate(null);
		aggregatedPosition.setAccountOwner(null);
	}
	
}
