package com.integral.position;

import com.google.common.collect.ImmutableSet;
import com.integral.event.Filter;
import com.integral.model.AccountOwner;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 7/15/16
 * Time: 4:17 PM
 * To change this template use File | Settings | File Templates.
 */
public class CounterPartyFilterOnOrgShortName extends AbstractFilterOnShortName {

    public CounterPartyFilterOnOrgShortName(ImmutableSet<String> allowedOrganizations_) {
        super(allowedOrganizations_);
    }

    @Override
    public PositionFilterType getId() {
        return PositionFilterType.CPTYORG;
    }

    @Override
    public AccountOwner.AccountOwnerLevel getAccountOwnerLevel() {
        return AccountOwner.AccountOwnerLevel.CPTYORG;
    }

    @Override
    public String getOrgShortName(Position position) {
        return position.getNamespaceName();
    }
}

