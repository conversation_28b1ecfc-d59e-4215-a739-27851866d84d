package com.integral.position;

import com.integral.model.Position;

import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Concurrent Map extension which always maintains an aggregated position across all positions values contained in map.
 * <AUTHOR> Development Corporation
 */
public abstract class AggregatedConcurrentHashMap<K, V> extends ConcurrentHashMap<K, V> {

        private static final long serialVersionUID = 1L;
        private final AtomicReference<Position> aggregatedPosition = new AtomicReference<Position>();
        private final AtomicBoolean isDirty = new AtomicBoolean(true);

        public Position getAggregatedPosition()
        {
            return updateAggregatedValueAcrossMap();
        }

        @Override
        public V put(K key, V value)
        {
            V v = super.put(key, value);
            isDirty.set(true);
            return v;
        }

        @Override
        public V remove(Object key)
        {
            V toReturn = super.remove(key);
            isDirty.set(true);
            return toReturn;
        }

        @Override
        public V putIfAbsent(K key, V value)
        {
            V previousValue = super.putIfAbsent(key, value);
            if(previousValue == null)
            {
                isDirty.set(true);
            }
            return previousValue;
        }

        @Override
        public void clear()
        {
            super.clear();
            isDirty.set(true);
        }

        @Override
        public void putAll(Map<? extends K, ? extends V> m)
        {
            super.putAll(m);
            isDirty.set(true);
        }

        @Override
        public V replace(K key, V value)
        {
            V toReturn = super.replace(key, value);
            isDirty.set(true);
            return toReturn;
        }

        @Override
        public boolean remove(Object key, Object value)
        {
            boolean removed = super.remove(key, value);
            if(removed)
            {
                isDirty.set(true);
            }
            return removed;
        }

        @Override
        public boolean replace(K key, V oldValue, V newValue)
        {
            boolean replaced = super.replace(key, oldValue, newValue);
            if(replaced)
            {
                isDirty.set(true);
            }
            return replaced;
        }

        /**
         * Update and return aggregated value across cache.
         * @return
         */
        Position updateAggregatedValueAcrossMap()
        {
            if(isDirty.get())
            {
                Position previousValue = null;
                Position newValue = null;
                do
                {
                    previousValue = aggregatedPosition.get();
                    newValue = getAggregator().convert(getValuesToAggregate());
                }
                while(!aggregatedPosition.compareAndSet(previousValue, newValue));
                return newValue;
            }
            return aggregatedPosition.get();
        }

        protected abstract PositionAggregator getAggregator();

        protected abstract Collection<Position> getValuesToAggregate();
}
