/**
 * <AUTHOR>
 */
package com.integral.position;


import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.integral.admin.utils.CollectionUtil;
import com.integral.event.*;
import com.integral.finance.counterparty.*;
import com.integral.finance.positions.service.PositionSubscriptionInfo;
import com.integral.is.common.mbean.ISFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.AccountOwner;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.position.notification.multicast.PositionSubscriptionHandler;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.util.CompositeKeysGeneric;
import com.integral.util.StringUtil;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * This class maintains position request subscription and 
 * <AUTHOR>
 *
 */
public class PositionDistributionManager
{
    //Key factory for raw positionevents with key as [positionlevel + enity name + entity namespace]
    private static final PositionReferenceKeyFactory DATA_TO_REFERENCE_KEY_FACTORY = new PositionReferenceKeyFactory();

    //Key factory for grouped (at LE and user levels ) positionevents with key as [positionlevel + * + entity namespace]
    private static final PositionGroupReferenceKeyFactory DATA_TO_GROUP_REFERENCE_KEY_FACTORY = new PositionGroupReferenceKeyFactory();

	private static final Factory<PositionCache, Nothing> currencyAndValueDateLevelCachingStrategyFactory = new Factory<PositionCache, Nothing>()
    {
        @Override
        public PositionCache create(Nothing n)
        {
            return new PositionCacheOfCurrencyAndValueDateLevels();
        }
    };

    private static final Factory<PositionCache, Nothing> currencyAndValueDateLevelGroupedCachingStrategyFactory = new Factory<PositionCache, Nothing>()
    {
        @Override
        public PositionCache create(Nothing n)
        {
            return new PositionCacheOfCurrencyAndValueDatePerPositionLevels();
        }
    };


    private static final Factory<PositionCache, Nothing> positionCachingStrategyFactory = new Factory<PositionCache, Nothing>()
	{
        @Override
        public PositionCache create(Nothing n)
        {
            return new PositionCacheInCurrencyValueDateAndOrgHierarchy();
        }
	};

    public static final String DELIMITER = "_";

    public static enum ReplayingEventTagName{Namespace};

	/**
	 * RequestId Vs UpdateHandler map. Required for un-subcription.
	 */
	private final ConcurrentHashMap<String, ConcurrentHashMap<String,PositionSubscriptionRequest>> handlersCache = new ConcurrentHashMap<String, ConcurrentHashMap<String,PositionSubscriptionRequest>>();
	
	/**
	 * Each sales dealer has different set of customers hence different Aggregation.
     * Hence we are maintaining a Replaying Event per sales dealer user.
     * The key for map is <i>user.getObjectId()</i>
	 */
	private final ConcurrentHashMap<CompositeKeysGeneric, ReplayablePositionEvent> salesDealerPositionEventCache = new ConcurrentHashMap<CompositeKeysGeneric, ReplayablePositionEvent>();

    final PositionSubscriptionHandler subscriptionHandler = new PositionSubscriptionHandler(PositionSource.getInstance());
	
	private static final Log log = LogFactory.getLog(PositionDistributionManager.class);
	
	private static class Holder
	{
		public static final PositionDistributionManager INSTANCE = new PositionDistributionManager();
	}
	
	public static PositionDistributionManager getInstance()
	{
		return Holder.INSTANCE;
	}

    /**
     * Except for sales dealers all user subscriptions are handled by this referenced replaying event.
     * Which maintain segments based on reference key provided by user at the time of subscription, this
     * improves co-location of listener and data.
     */
    private final ReferencedReplayingPositionEvent rawPositionEvent;

    /**
     * Except for sales dealers all user subscriptions are handled by this referenced replaying event.
     * Which maintain segments based on reference key provided by user at the time of subscription, this
     * improves co-location of listener and data.
     */
    private final ReferencedReplayingPositionEvent groupedPositionEvent;

	private final SupplyableDataSource<UserCounterpartyGroup> userCounterpartyGroupChangeEventSource = new SupplyableDataSource<UserCounterpartyGroup>();

	private PositionDistributionManager()
	{
		rawPositionEvent = new ReferencedReplayingPositionEvent ("PositionUpdateEvent",
				PositionSource.getInstance(), 
				DATA_TO_REFERENCE_KEY_FACTORY,
                currencyAndValueDateLevelCachingStrategyFactory,
				true, 
				ISFactory.getInstance().getPositionServiceConfig().prepopulatePositionCache());
		rawPositionEvent.start();

        groupedPositionEvent = new ReferencedReplayingPositionEvent ("PositionUpdateGroupingEvent",
                PositionSource.getInstance(),
                DATA_TO_GROUP_REFERENCE_KEY_FACTORY,
                currencyAndValueDateLevelGroupedCachingStrategyFactory,
                true,
                ISFactory.getInstance().getPositionServiceConfig().prepopulatePositionCache());
        groupedPositionEvent.start();
	}

	
	public PositionSubscriptionHandler getPositionSubscriptionHandler(){
		return subscriptionHandler;
	}
	
	/**
	 * callback to notify PDM that some user's counter-party has changed.
	 * @param counterpartyGroup
	 */
	public void onUserCounterpartyGroupChange(UserCounterpartyGroup counterpartyGroup)
	{
		userCounterpartyGroupChangeEventSource.supply(counterpartyGroup);
	}
		
	/**
	 * 
	 * @param request
	 * @param handler
	 */
	public boolean addHandler(PositionSubscriptionInfo request,  Listener<Position> handler )
	{
		PositionSubscriptionRequest psr = new PositionSubscriptionRequest(request, handler, request.getRequestUser());		
		boolean isDuplicate = addToHandlerCache(psr);
		if( !isDuplicate )
		{
            String positionLevel = request.getPositionLevel();
            boolean addSalesDealerSubscription = false;
            Set<String> orgsSubscribed = new HashSet<String>();

            if(null!= positionLevel){
                //This is extracting the request based on position level . validation done in controller
                AccountOwner.AccountOwnerLevel accountOwnerLevel = AccountOwner.AccountOwnerLevel.valueOf(positionLevel);
                //Customer postiions are  only for org level positions
                if((accountOwnerLevel.LE == accountOwnerLevel || accountOwnerLevel.ORG == accountOwnerLevel  )&& request.isCustomerPositionIncluded()){
                    //include customer positions also
                    orgsSubscribed = getSalesDealerCustomerOrganizationsShortName( request.getRequestUser() );
                    addSalesDealerSubscription = !CollectionUtil.isEmptyOrNull(orgsSubscribed); // sales dealer will always have customer orgs.
                }
            }else{
                //This is extracting the request based on old position service , primarily from groupby columns
                if(!CollectionUtil.isEmptyOrNull(request.groupByColums()) && request.groupByColums().contains(PositionSubscriptionHelper.getOrgColName()))
                {
                    // if not grouped by org then handle as non-sales dealer subscription. Because we cannot groupby its customer orgs which is equivalent to non-sales dealer subscription
                    orgsSubscribed = getSalesDealerCustomerOrganizationsShortName( request.getRequestUser() );
                    addSalesDealerSubscription = !CollectionUtil.isEmptyOrNull(orgsSubscribed); // sales dealer will always have customer orgs.
                }
            }

            //If customer position needs to be included , do a default sales dealer position for the request , making sure its position level based request.
            if(addSalesDealerSubscription || ( request.isCustomerPositionIncluded() && null!= positionLevel))
            {
                if(null!= positionLevel &&  AccountOwner.AccountOwnerLevel.LE == AccountOwner.AccountOwnerLevel.valueOf(positionLevel) ){
                    log.infoAsFormat("New LE level subscription {requestId=%s} added for sales dealer{%s}", request.getClientReferenceId(), request.getRequestUser().getShortName());
                    orgsSubscribed.add(request.getRequestUser().getOrganization().getShortName());
                    getOrCreateReplayingEventForLESalesDealer(request.getRequestUser()).subscribeAndReplay(handler, getApplicableFilters(request));
                }else{
                    orgsSubscribed.add(request.getRequestUser().getOrganization().getShortName());
                    getOrCreateReplayingEventForOrgSalesDealer(request.getRequestUser()).subscribeAndReplay(handler, getApplicableFilters(request));
                    log.infoAsFormat("New subscription {requestId=%s} added for sales dealer{%s}", request.getClientReferenceId(), request.getRequestUser().getShortName());
                }
            }
            else
            {
                if(request.isWildCardSubscription()){
                    groupedPositionEvent.subscribeAndReplay(handler, DATA_TO_GROUP_REFERENCE_KEY_FACTORY.create(request), getApplicableFilters(request));
                    log.infoAsFormat("New wild card subscription {requestId=%s} added for FI{%s}", request.getClientReferenceId(), request.getRequestUser().getShortName());
                }else{
                    rawPositionEvent.subscribeAndReplay(handler, DATA_TO_REFERENCE_KEY_FACTORY.create(request), getApplicableFilters(request));
                    log.infoAsFormat("New subscription {requestId=%s} added for FI{%s}", request.getClientReferenceId(), request.getRequestUser().getShortName());

                    // if its a org level request, create user level dummy event and vice versa
                    CompositeKeysGeneric dummyReferenceKey;
                    if(request.getUser() == null && !request.isWildCardSubscription()){
                        // incoming is a org level request; create a dummy event at the user level
                        dummyReferenceKey = PositionReferenceKeyFactory.getUserLevelCacheKey(request);
                    }else{
                        // incoming is a user level request; create a dummy event at the org level
                        dummyReferenceKey = PositionReferenceKeyFactory.getOrgLevelCacheKey(request);
                    }
                    rawPositionEvent.subscribeAndReplayDummyEvent(dummyReferenceKey);
                }

                orgsSubscribed.add(request.getOrganization().getShortName());
            }

            for (String string : orgsSubscribed) {
                Organization o = ReferenceDataCacheC.getInstance().getOrganization(string);
                if (o != null) {
                    subscriptionHandler.subscribe(o);
                }
            }

        }
		return isDuplicate;
	}

    /**
	 * 
	 * @param user
	 * @param requestId
	 * @return
	 */
	public boolean removeHandler(User user, String requestId)
	{  
		log.infoAsFormat("removeAllHandler for user{%s} requestId{%s}", user.getShortName(), requestId);
		if(user==null || requestId==null) return false;
		
		boolean result = false;
		String keyId = String.valueOf( user.getObjectId() );
		ConcurrentHashMap<String, PositionSubscriptionRequest> handlers = handlersCache.get(keyId);
		if ( handlers != null )
		{
			PositionSubscriptionRequest wrapper = handlers.remove(requestId);
			if( wrapper != null )
			{
                Set<String> orgsSubscribed = new HashSet<String>();
                AccountOwner.AccountOwnerLevel level = getLevel(wrapper);
                CompositeKeysGeneric key = CompositeKeysGeneric.getCompositeKeys(level, keyId);
                ReplayablePositionEvent salesDealerPositionEvent = salesDealerPositionEventCache.get(key);
				if(salesDealerPositionEvent!=null)
				{
					log.infoAsFormat("un-subscription {requestId=%s} for sales dealer{%s}", requestId, user.getShortName());
					orgsSubscribed = getSalesDealerCustomerOrganizationsShortName( user );
					result = salesDealerPositionEvent.unsubscribe(wrapper.handler);
				}
				else
				{
                    if(wrapper.info.isWildCardSubscription()){
                        result = groupedPositionEvent.unsubscribe(wrapper.handler, DATA_TO_GROUP_REFERENCE_KEY_FACTORY.create(wrapper.info));
                        log.infoAsFormat("un-subscription for wild card subscription {requestId=%s} for customer{%s}",requestId, user.getShortName());
                    }else{
                        result = rawPositionEvent.unsubscribe(wrapper.handler, DATA_TO_REFERENCE_KEY_FACTORY.create(wrapper.info));
                        log.infoAsFormat("un-subscription {requestId=%s} for customer{%s}", requestId, user.getShortName());
                    }
					orgsSubscribed.add(user.getOrganization().getShortName());
				}

				if( result ){
					for (String string : orgsSubscribed) {
						Organization o = ReferenceDataCacheC.getInstance().getOrganization(string);
						if (o != null) {
							subscriptionHandler.unsubscribe(o);
						}
					}
				}
			}
		}
		return result;
	}

    protected AccountOwner.AccountOwnerLevel getLevel(PositionSubscriptionRequest wrapper) {
        if(wrapper.info.getPositionLevel() != null){
            try{
                AccountOwner.AccountOwnerLevel accountOwnerLevel = AccountOwner.AccountOwnerLevel.valueOf(wrapper.info.getPositionLevel());
                if(null!=accountOwnerLevel ){
                    return accountOwnerLevel;
                }
            }catch (Exception e){
                return AccountOwner.AccountOwnerLevel.ORG;
            }
        }
        return AccountOwner.AccountOwnerLevel.ORG;
    }


    /**
	 * 
	 * @param user
	 * @return
	 */
	public void removeAllHandler(User user)
	{  
		log.infoAsFormat("removeAllHandler for user{%s}", user.getShortName());
		String key = String.valueOf( user.getObjectId() );
		ConcurrentHashMap<String, PositionSubscriptionRequest> handlers = handlersCache.remove(key);
		if ( handlers != null )
		{
            List<ReplayablePositionEvent> salesDealerPositionEvents = new ArrayList<ReplayablePositionEvent>();
            for (AccountOwner.AccountOwnerLevel level: AccountOwner.AccountOwnerLevel.values())  {
                CompositeKeysGeneric keyId = CompositeKeysGeneric.getCompositeKeys(level,key);
                ReplayablePositionEvent event = salesDealerPositionEventCache.get(keyId);
                if(null!=event){
                    salesDealerPositionEvents.add(event);
                }
            }

			Iterator<PositionSubscriptionRequest> requestIterator = handlers.values().iterator();
			while(requestIterator.hasNext())
			{
				PositionSubscriptionRequest request = requestIterator.next();
				if( request != null )
				{
                    if(request.info.isWildCardSubscription()){
                        groupedPositionEvent.unsubscribe(request.handler, DATA_TO_GROUP_REFERENCE_KEY_FACTORY.create(request.info));
                        log.infoAsFormat("un-subscription for wild card subscription customer{%s} {requestId=%s}", user.getShortName(),request.info.getClientReferenceId());
                    }else{
                        rawPositionEvent.unsubscribe(request.handler, DATA_TO_REFERENCE_KEY_FACTORY.create(request.info));
                        log.infoAsFormat("un-subscription for customer{%s} {requestId=%s}", user.getShortName(),request.info.getClientReferenceId());
                    }
                    //Will look up in sales dealer and remove the same
                    for(ReplayablePositionEvent salesDealerPositionEvent:salesDealerPositionEvents)
                    {
                        log.infoAsFormat("un-subscription for sales dealer{%s} {requestId=%s}", user.getShortName(), request.info.getClientReferenceId());
                        salesDealerPositionEvent.unsubscribe(request.handler);
                    }
				}
				requestIterator.remove();
			}
		}
	}
	
	/**
     * Remove all position handler attached for the specified user
     * 
     * @param userFullName The name is specified in a pattern USER-SHORTNAME@NAMESPACE-SHORTNAME-PATH, <NAME_EMAIL>1. If the namespace is omitted, MAIN is assumed.
     */
	public boolean removeHandler(String userFullName, String requestId)
	{
		log.infoAsFormat("removeHandler for customer{%s} {requestId=%s}", userFullName,requestId);
		return removeHandler(UserFactory.getUser(userFullName), requestId);
	}
	
    /**
     * Remove all position handler attached for the specified user
     * 
     * @param userFullName The name is specified in a pattern USER-SHORTNAME@NAMESPACE-SHORTNAME-PATH, <NAME_EMAIL>1. If the namespace is omitted, MAIN is assumed.
     */
	public void removeAllHandler(String userFullName)
	{
		log.infoAsFormat("removeAllHandler for customer{%s}", userFullName);
		removeAllHandler(UserFactory.getUser(userFullName));
	}
	
	/**
	 * Return true if subscription already exist and not added to cache. Which means method return true if request is duplicate.
	 * @param wrapper
	 * @return
	 */
	protected boolean addToHandlerCache(PositionSubscriptionRequest wrapper)
	{
		PositionSubscriptionInfo request = wrapper.info;
		Listener<Position> handler = wrapper.handler;
		User user = wrapper.user;
		String key = String.valueOf( user.getObjectId() );
		ConcurrentHashMap<String,PositionSubscriptionRequest> handlers = handlersCache.get(key);
		if( handlers == null )
		{
			ConcurrentHashMap<String,PositionSubscriptionRequest> newHandlers = new ConcurrentHashMap<String, PositionSubscriptionRequest>();
			handlers = handlersCache.putIfAbsent(key, newHandlers);
			if(handlers == null)
			{
				handlers = newHandlers;
			}
		}
		return handlers.putIfAbsent(PositionSubscriptionInfo.Util.getRequestParamId(request), new PositionSubscriptionRequest(request,handler,user)) != null;
	}
	
	public ImmutableMap<CompositeKeysGeneric, Cache<String, Position, PositionFilterType>> getNonSalesDealerPositionCaches()
	{
		return rawPositionEvent.getUnderlyingCache();
	}
	
	public Collection<ReplayablePositionEvent> getSalesDealerPositionCaches()
	{
		return salesDealerPositionEventCache.values();
	}

    public ReplayablePositionEvent getSalesDealerPositionCache(CompositeKeysGeneric key)
    {
        return salesDealerPositionEventCache.get(key);
    }


    public ImmutableMap<CompositeKeysGeneric, Cache<String, Position, PositionFilterType>> getGroupedPositionCaches()
    {
        return groupedPositionEvent.getUnderlyingCache();
    }

	
	public void clearAllPositionCache()
	{
		log.info("clearAllPositionCache ");
		for(Cache<String, Position, PositionFilterType> rawCache : rawPositionEvent.getUnderlyingCache().values())
		{
			rawCache.clear();
		}

        for(Cache<String, Position, PositionFilterType> groupCache : groupedPositionEvent.getUnderlyingCache().values())
        {
            groupCache.clear();
        }

        for(ReplayablePositionEvent sdEvent : salesDealerPositionEventCache.values())
		{
            sdEvent.cleanAllEventCache();
        }

	}
	
	public ImmutableMap<CompositeKeysGeneric, ImmutableMap<Listener<Position>, Iterable<Filter<Position, PositionFilterType>>>> getCurrentListenersOfPosition()
	{
		return rawPositionEvent.getCurrentListeners();
	}
	
	class PositionSubscriptionRequest
	{
		PositionSubscriptionInfo info;
		Listener<Position> handler;
		User user;
		
		PositionSubscriptionRequest(PositionSubscriptionInfo info, Listener<Position> handler, User requestingUser)
		{
			this.info = info;
			this.handler = handler;
			this.user = requestingUser;
		}
	}
	
	/**
	 * Adding only ValueDate filter because data is already segmented on composite key of Organization and CurrencyPair, therefore those filters are not required.
	 * @param request
	 * @return
	 */
	private static Iterable<Filter<Position,PositionFilterType>> getApplicableFilters(final PositionSubscriptionInfo request)
	{
		// Value date filter
		Filter<Position,PositionFilterType> valueDateFilter = new ValueDateFilter(request);
        String positionLevel = request.getPositionLevel();
        if(null!= positionLevel){
            //This is extracting the request based on position level . validation done in controller
            if(request.isWildCardSubscription()){
                AccountOwner.AccountOwnerLevel accountOwnerLevel = AccountOwner.AccountOwnerLevel.valueOf(positionLevel);
                AccountOwnerLevelFilter accountOwnerLevelFilter = new AccountOwnerLevelFilter(accountOwnerLevel);
                List<Filter<Position,PositionFilterType>> filters = new ArrayList<Filter<Position, PositionFilterType>>();
                filters.add(accountOwnerLevelFilter);
                filters.add(valueDateFilter);
                return filters;
            }
        }
        return Collections.singletonList(valueDateFilter);
    }
	
	private ReplayablePositionEvent getOrCreateReplayingEventForOrgSalesDealer(final User user)
	{
		log.debugAsFormat("Request:getOrCreateReplayingEventForOrgSalesDealer for sales dealer user{%s}", user.getShortName());

        String keyId = String.valueOf( user.getObjectId() );
        CompositeKeysGeneric key = CompositeKeysGeneric.getCompositeKeys(AccountOwner.AccountOwnerLevel.ORG,keyId);
		ReplayablePositionEvent event = salesDealerPositionEventCache.get(key);
		if(event == null)
		{
			FilteredSource<Position> filteredPositionSourceForSalesDealer = createFilteredPositionSourceForSalesDealer(user);

            PositionCache cachingStrategy_ = positionCachingStrategyFactory.create(Nothing.Instance);
            event = new ReplayablePositionEvent(StringUtil.fastConcat("SalesDealerPositionEvent-",user.getShortName()),
					filteredPositionSourceForSalesDealer,
                    cachingStrategy_, true,
					Collections.singleton(CompositeKeysGeneric.getCompositeKeys(ReplayingEventTagName.Namespace,user.getOrganization().getShortName())));

            UserSalesDealerChangeListener listener = new UserSalesDealerChangeListener(user,cachingStrategy_);
            userCounterpartyGroupChangeEventSource.addListener(listener);

			ReplayablePositionEvent previousValue = salesDealerPositionEventCache.putIfAbsent(key, event);
			
			if(previousValue!=null)
			{
				event = previousValue;
			}
			else
			{
				log.infoAsFormat("Created new event for sales dealer user{%s}", user.getShortName());
				event.start(); // connect this newly created event to its source.
				filteredPositionSourceForSalesDealer.start();// connect filter source to its underlying source.
			}
		}
		return event;
	}

    private ReplayablePositionEvent getOrCreateReplayingEventForLESalesDealer(final User user)
    {
        log.debugAsFormat("Request:getOrCreateReplayingEventForLESalesDealer for sales dealer user{%s}", user.getShortName());

        String keyId = String.valueOf( user.getObjectId() );
        CompositeKeysGeneric key = CompositeKeysGeneric.getCompositeKeys(AccountOwner.AccountOwnerLevel.LE,keyId);
        ReplayablePositionEvent event = salesDealerPositionEventCache.get(key);
        if(event == null)
        {
            FilteredSource<Position> filteredPositionSourceForSalesDealer = new FilteredSource<Position>(PositionSource.getInstance(),
                    new SalesDealerCustomerFilterForLEOnShortName(userCounterpartyGroupChangeEventSource, user));

            PositionCache cachingStrategy_ = currencyAndValueDateLevelGroupedCachingStrategyFactory.create(Nothing.Instance);
            event = new ReplayablePositionEvent(StringUtil.fastConcat("SalesDealerPositionEvent-",AccountOwner.AccountOwnerLevel.LE.toString(),user.getShortName()),
                    filteredPositionSourceForSalesDealer,
                    cachingStrategy_, true,
                    Collections.singleton(CompositeKeysGeneric.getCompositeKeys(ReplayingEventTagName.Namespace,user.getOrganization().getShortName())));

            UserSalesDealerChangeListener listener = new UserSalesDealerChangeListener(user,cachingStrategy_);
            userCounterpartyGroupChangeEventSource.addListener(listener);

            ReplayablePositionEvent previousValue = salesDealerPositionEventCache.putIfAbsent(key, event);

            if(previousValue!=null)
            {
                event = previousValue;
            }
            else
            {
                log.infoAsFormat("Created new LE level event for sales dealer user{%s}", user.getShortName());
                event.start(); // connect this newly created event to its source.
                filteredPositionSourceForSalesDealer.start();// connect filter source to its underlying source.
            }
        }
        return event;
    }
	
	private FilteredSource<Position> createFilteredPositionSourceForSalesDealer(final User user)
	{
		return new FilteredSource<Position>(PositionSource.getInstance(), new SalesDealerCustomerFilterOnShortName(userCounterpartyGroupChangeEventSource, user));
	}
	
	public void startCachePopulationAsSDOrg(Organization org, AccountOwner.AccountOwnerLevel type)
	{
		log.debugAsFormat("Requested startCachePopulationAsSDOrg for organization{%s}", org.getShortName());

        String keyId = String.valueOf( org.getObjectId() );
        CompositeKeysGeneric key = CompositeKeysGeneric.getCompositeKeys(type,keyId);
		ReplayablePositionEvent event = salesDealerPositionEventCache.get(key);
		if(event==null)
		{
			Collection<String> salesDealerCustomerOrganizations = new HashSet<String>();
			salesDealerCustomerOrganizations.add(org.getShortName());			
			getOrCreateReplayingEventForSalesDealer(org, salesDealerCustomerOrganizations,type);

            for (String orgName : salesDealerCustomerOrganizations) {
                Organization o = ReferenceDataCacheC.getInstance().getOrganization(orgName);
                if (o != null) {
                    subscriptionHandler.subscribe(o);
                }
            }
			log.infoAsFormat("started startCachePopulationAsSDOrg for organization{%s}", org.getShortName());
		}
		else
		{
			log.debugAsFormat("Cache already Populating For organization{%s}", org.getShortName());
		}
	}

	public void stopCachePopulationIfSDOrgUnlessOtherUsersListening(String orgShortName)
	{
		Organization org = ReferenceDataCacheC.getInstance().getOrganization(orgShortName);
		stopCachePopulationIfSDOrgUnlessOtherUsersListening(org);
	}


	private void stopCachePopulationIfSDOrgUnlessOtherUsersListening(Organization org)
	{
		log.infoAsFormat("Requested stopCachePopulationIfSDOrgUnlessOtherUsersListening for sales dealer{%s}", org.getShortName());

        String keyId = String.valueOf( org.getObjectId() );
        CompositeKeysGeneric key = CompositeKeysGeneric.getCompositeKeys(AccountOwner.AccountOwnerLevel.ORG,keyId);
		ReplayablePositionEvent event = salesDealerPositionEventCache.get(key);
		if(!event.hasAnyListener())
		{
			log.infoAsFormat("Removing event for sales dealer{%s}", org.getShortName());
			salesDealerPositionEventCache.remove(key);
		}
		else
		{
			log.infoAsFormat("Not removing event for sales dealer{%s} as it has listeners", org.getShortName());
		}
	}
	
	private ReplayablePositionEvent getOrCreateReplayingEventForSalesDealer(Organization org, Collection<String> dealOrgs,AccountOwner.AccountOwnerLevel type)
	{
        String keyId = String.valueOf( org.getObjectId() );
        CompositeKeysGeneric key = CompositeKeysGeneric.getCompositeKeys(type,keyId);
		ReplayablePositionEvent event = salesDealerPositionEventCache.get(key);
		if(event == null)
		{
            ReplayablePositionEvent previousValue=null;
            FilteredSource<Position> filteredPositionSourceForSalesDealer = null;
            PositionSource positionSource = PositionSource.getInstance();
            if(type == AccountOwner.AccountOwnerLevel.ORG){
                filteredPositionSourceForSalesDealer = new FilteredSource<Position>(positionSource, new OrganizationFilterOnShortName(ImmutableSet.<String>copyOf(dealOrgs)));
                event = new ReplayablePositionEvent(StringUtil.fastConcat("SalesDealerPositionEvent-", org.getShortName()), filteredPositionSourceForSalesDealer, positionCachingStrategyFactory.create(Nothing.Instance), true, Collections.singleton(CompositeKeysGeneric.getCompositeKeys(ReplayingEventTagName.Namespace,org.getShortName())));
                previousValue = salesDealerPositionEventCache.putIfAbsent(key, event);
            }else if(type == AccountOwner.AccountOwnerLevel.LE){
                filteredPositionSourceForSalesDealer = new FilteredSource<Position>(positionSource, new LegalEntityFilterOnOrgShortName(ImmutableSet.<String>copyOf(dealOrgs)));
                event = new ReplayablePositionEvent(StringUtil.fastConcat("SalesDealerPositionLEEvent-", org.getShortName()), filteredPositionSourceForSalesDealer, positionCachingStrategyFactory.create(Nothing.Instance), true, Collections.singleton(CompositeKeysGeneric.getCompositeKeys(ReplayingEventTagName.Namespace,org.getShortName())));
                previousValue = salesDealerPositionEventCache.putIfAbsent(key, event);
            }else if(type == AccountOwner.AccountOwnerLevel.CPTYORG){
                filteredPositionSourceForSalesDealer = new FilteredSource<Position>(positionSource, new CounterPartyFilterOnOrgShortName(ImmutableSet.<String>copyOf(dealOrgs)));
                event = new ReplayablePositionEvent(StringUtil.fastConcat("SalesDealerPositionCPTYEvent-", org.getShortName()), filteredPositionSourceForSalesDealer, positionCachingStrategyFactory.create(Nothing.Instance), true, Collections.singleton(CompositeKeysGeneric.getCompositeKeys(ReplayingEventTagName.Namespace,org.getShortName())));
                previousValue = salesDealerPositionEventCache.putIfAbsent(key, event);
            }else if(type == AccountOwner.AccountOwnerLevel.USER){
                filteredPositionSourceForSalesDealer = new FilteredSource<Position>(positionSource, new UserFilterOnOrgShortName(ImmutableSet.<String>copyOf(dealOrgs)));
                event = new ReplayablePositionEvent(StringUtil.fastConcat("SalesDealerPositionUSEREvent-", org.getShortName()), filteredPositionSourceForSalesDealer, positionCachingStrategyFactory.create(Nothing.Instance), true, Collections.singleton(CompositeKeysGeneric.getCompositeKeys(ReplayingEventTagName.Namespace,org.getShortName())));
                previousValue = salesDealerPositionEventCache.putIfAbsent(key, event);
            }else if(type == AccountOwner.AccountOwnerLevel.MAKER_ORG){
                filteredPositionSourceForSalesDealer = new FilteredSource<Position>(positionSource, new MakerOrganizationFilterOnShortName(ImmutableSet.<String>copyOf(dealOrgs)));
                event = new ReplayablePositionEvent(StringUtil.fastConcat("SalesDealerPositionMakerOrgvent-", org.getShortName()), filteredPositionSourceForSalesDealer, positionCachingStrategyFactory.create(Nothing.Instance), true, Collections.singleton(CompositeKeysGeneric.getCompositeKeys(ReplayingEventTagName.Namespace,org.getShortName())));
                previousValue = salesDealerPositionEventCache.putIfAbsent(key, event);
            }else if(type == AccountOwner.AccountOwnerLevel.TAKER_ORG){
                filteredPositionSourceForSalesDealer = new FilteredSource<Position>(positionSource, new TakerOrganizationFilterOnShortName(ImmutableSet.<String>copyOf(dealOrgs)));
                event = new ReplayablePositionEvent(StringUtil.fastConcat("SalesDealerPositionTakerOrgvent-", org.getShortName()), filteredPositionSourceForSalesDealer, positionCachingStrategyFactory.create(Nothing.Instance), true, Collections.singleton(CompositeKeysGeneric.getCompositeKeys(ReplayingEventTagName.Namespace,org.getShortName())));
                previousValue = salesDealerPositionEventCache.putIfAbsent(key, event);
            }
			
			if(previousValue!=null)
			{
				event = previousValue;
			}
			else
			{
				log.infoAsFormat("Created new event for sales dealer{%s}", org.getShortName());
                if(null!=event){
				    event.start(); // connect this newly created event to its source.
                }
                if(null!=filteredPositionSourceForSalesDealer){
				    filteredPositionSourceForSalesDealer.start();// connect filter source to its underlying source.
                }
			}
		}
		return event;
	}

    /**
     * Returns the set of current organizations with legal entity names.
     *
     * @param user user
     * @return returns empty set if no customer orgs configured.
     */
    public static Set<String> getLegalEntitiesForUser(User user){
        Set<String> toBeSubscribed = new HashSet<String>();

        CounterpartyGroup existingCptyGroup = ( CounterpartyGroup ) user.getCustomFieldValue( "DirectFX_AssociatedLegalEntities" );
        if ( existingCptyGroup != null )
        {
            Collection associatedLEs = existingCptyGroup.getCounterparties();
            for ( Object associatedLE : associatedLEs )
            {
                LegalEntity le = ( LegalEntity ) associatedLE;
                if ( le.isActive() )
                {
                    toBeSubscribed.add(le.getOrganization().getShortName()+ DELIMITER +le.getShortName());
                }
            }
        }
        return toBeSubscribed;
    }

    /**
     * Returns the set of customer organizations  (i.e. which are not ExternalProvider, ECN, Broker, Masked LP, ClearingHouse, PrimeBroker) with legal entity names configured for the specified sales dealer.
     *
     * @param user user
     * @return returns empty set if no customer orgs configured.
     */
    public static Set<String> getSalesDealerLegalEntities( User user)
    {
        Set<String> fiOrgs = new HashSet<String>();
        try
        {
            if ( user != null )
            {
                Collection<TradingParty> sdTps = CounterpartyUtilC.getCounterpartyLegalEntitiesForAssociatedSDG( user );
                if (!CollectionUtil.isEmptyOrNull(sdTps))
                {
                    fiOrgs = new HashSet<String>( sdTps.size() );
                    for ( TradingParty tp : sdTps )
                    {
                        if ( tp.getLegalEntityOrganization() != null && isCustomerOrganization(tp.getLegalEntityOrganization()) )
                        {
                            //Using the full name since LE short name can be repeated across namepspaces.
                            fiOrgs.add( tp.getLegalEntityOrganization().getShortName() + DELIMITER +tp.getLegalEntity().getShortName() );
                        }
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.getSalesDealerCustomerOrganizations : Exception while getting customer orgs for user=" + user, e );
        }
        return fiOrgs;
    }

	 /**
     * Returns the set of customer organizations (i.e. which are not ExternalProvider, ECN, Broker, Masked LP, ClearingHouse, PrimeBroker) configured for the specified sales dealer.
     *
     * @param user user
     * @return returns empty set if no customer orgs configured.
     */
    public static Set<String> getSalesDealerCustomerOrganizationsShortName( User user)
    {
    	Set<String> fiOrgs = new HashSet<String>();
        try
        {
            if ( user != null )
            {
                Collection<TradingParty> sdTps = CounterpartyUtilC.getCounterpartyLegalEntitiesForAssociatedSDG( user );
                if (!CollectionUtil.isEmptyOrNull(sdTps))
                {
            		fiOrgs = new HashSet<String>( sdTps.size() );
            		for ( TradingParty tp : sdTps )
            		{
            		    if ( tp.getLegalEntityOrganization() != null && isCustomerOrganization(tp.getLegalEntityOrganization()) )
            		    {
            		        fiOrgs.add( tp.getLegalEntityOrganization().getShortName() );
            		    }
            		}
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.getSalesDealerCustomerOrganizations : Exception while getting customer orgs for user=" + user, e );
        }
        return fiOrgs;
    }
    
    /**
     * @return true if organization is not ExternalProvider, ECN, Broker, Masked LP, ClearingHouse, PrimeBroker.
     */
    public static boolean isCustomerOrganization(Organization organization)
    {
        return !( organization.isExternalProvider() || organization.isECN() || organization.isMasked() || organization.isClearingHouse() || organization.isPrimeBroker() );
    }
    
     public static Collection<String> getCounterpartyLegalEntitiesForAssociatedSDGAsString( Organization org )
    {
    	Collection<String> fiOrgs = new HashSet<String>();
        try
        {
            if ( org != null )
            {
            	Collection<TradingParty> sdTps = CounterpartyUtilC.getCounterpartyLegalEntitiesForAssociatedSDG(org);
                if (!CollectionUtil.isEmptyOrNull(sdTps))
                {            		
            		for ( TradingParty tp : sdTps )
            		{
            		    if ( tp.getLegalEntityOrganization() != null && isCustomerOrganization(tp.getLegalEntityOrganization()) )
            		    {
            		        fiOrgs.add( tp.getLegalEntityOrganization().getShortName() );
            		    }
            		}
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.getSalesDealerCustomerOrganizations : Exception while getting customer orgs for FI=" + org.getShortName(), e );
        }
        return fiOrgs;
    }
}
