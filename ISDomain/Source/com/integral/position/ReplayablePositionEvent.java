package com.integral.position;

import java.util.Collections;
import java.util.Map.Entry;
import java.util.Set;

import com.google.common.base.Optional;
import com.integral.event.Filter;
import com.integral.event.Listener;
import com.integral.event.Listeners;
import com.integral.event.ReplayingEventC;
import com.integral.event.Source;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;
import com.integral.util.CompositeKeysGeneric;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.
public class ReplayablePositionEvent extends ReplayingEventC<String, Position, PositionFilterType>
{
	private final PositionCache eventCache;
	private static final Log log = LogFactory.getLog(ReplayablePositionEvent.class);
	private final Set<CompositeKeysGeneric> _tags;
	
	public ReplayablePositionEvent(
			String eventDescription,
			Source<Position> source_,
			PositionCache cachingStrategy_,
			boolean supplyCacheUpdatedValueOnNewUpdate)
	{
		super(eventDescription, source_, cachingStrategy_,
				supplyCacheUpdatedValueOnNewUpdate);
		this.eventCache = cachingStrategy_;
		_tags = Collections.emptySet();
	}
	
	public ReplayablePositionEvent(
			String eventDescription,
			Source<Position> source_,
			PositionCache cachingStrategy_,
			boolean supplyCacheUpdatedValueOnNewUpdate,
			Set<CompositeKeysGeneric> tags_)
	{
		super(eventDescription, source_, cachingStrategy_,
				supplyCacheUpdatedValueOnNewUpdate);
		this.eventCache = cachingStrategy_;
		_tags = tags_;
	}
	
	public boolean hasTag(CompositeKeysGeneric tag)
	{
		return _tags.contains(tag);
	}
	
	public Set<CompositeKeysGeneric>  getTags()
	{
		return _tags;
	}
	
	@Override
	protected void onUpdate(Position t) 
	{
		lock.lock();
        try
		{
            //1. update cache
			Position updatedEvent = eventCache.cache(t);

            //2. call listeners
			for (Entry<Listener<Position>, Iterable<Filter<Position,PositionFilterType>>> subscription : listenerToFilterMap.entrySet()) 
			{
				if(Filter.Util.matchesAll(t, subscription.getValue()))
				{
					if(supplyCacheUpdatedValueOnNewUpdate)
					{
						Optional<Position> aggregatedPosition = eventCache.getAggregatedPosition(t.getCurrencyPair(), subscription.getValue());
						if(aggregatedPosition.isPresent())
						{
							Listeners.callSafe(subscription.getKey(), aggregatedPosition.get());
							logData(subscription.getKey(), updatedEvent, " supply updated value on event");
						}
						else
						{
							log.warn("Unable to compute aggregated position for: "+ t.get_id());
						}
					}
					else
					{
						Listeners.callSafe(subscription.getKey(), t);
						logData(subscription.getKey(), t, " supply original event");
					}
				}
			}
		}
		finally{lock.unlock();}
	}
}
