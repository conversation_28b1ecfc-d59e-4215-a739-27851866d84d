package com.integral.position;

import com.integral.event.SupplyableDataSource;
import com.integral.model.Position;

/**
 * Singleton position source.
 * 
 * <AUTHOR> Development Corporation
 */
public class PositionSource extends SupplyableDataSource<Position>
{
	private static final PositionSource Instance = new PositionSource();
	private PositionSource(){}
	
	public static PositionSource getInstance() 
	{
		return Instance;
	}
}
