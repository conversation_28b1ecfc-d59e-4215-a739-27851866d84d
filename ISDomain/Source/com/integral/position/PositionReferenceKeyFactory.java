package com.integral.position;

import com.integral.event.Factory;
import com.integral.finance.positions.service.PositionSubscriptionInfo;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.AccountOwner;
import com.integral.model.AccountOwner.AccountOwnerLevel;
import com.integral.model.Position;
import com.integral.util.CompositeKeysGeneric;

public class PositionReferenceKeyFactory implements Factory<CompositeKeysGeneric, Position>
{
    protected Log log = LogFactory.getLog(PositionReferenceKeyFactory.class);

	public CompositeKeysGeneric create(PositionSubscriptionInfo positionSubscriptionInfo) 
	{
		return getCacheKey(positionSubscriptionInfo);
	}
	
	@Override
	public CompositeKeysGeneric create(Position position) 
	{
		switch ( position.getAccountOwner().getAccountOwnerLevel() )
		{
		case ORG :
			//This is org summary . this position requires only one level up of aggregation.
			return getCacheKey(position.getAccountOwner().getAccountOwnerLevel().name(), position.getAccountOwner().getEntityName(), position.getNamespaceName() );
		case USER :
			return getCacheKey(position.getAccountOwner().getAccountOwnerLevel().name(), position.getAccountOwner().getEntityName(), position.getNamespaceName() );
		default :
			break;
		}
		return getCacheKey(position.getAccountOwner().getAccountOwnerLevel().name(), position.getAccountOwner().getEntityName(), position.getNamespaceName() );
	}
	
	protected CompositeKeysGeneric getCacheKey(PositionSubscriptionInfo info)
	{
		return getCompositeKeys(info);
	}
	
	/**
	 * 
	 * @param value
	 * @param positionType
	 * @return
	 */
	public static CompositeKeysGeneric getCacheKey(String positionType, String value ,String namespace)
	{
		return CompositeKeysGeneric.getCompositeKeys(positionType, value, namespace);
	}
	
	/**
	 * This is a bridge between old world and new world of position service. {@link PositionSubscriptionHelper}} defines types/level of positions
	 * as column. In world they are defined by {@link AccountOwnerLevel}. 
	 *   
	 */
	protected CompositeKeysGeneric getCompositeKeys(PositionSubscriptionInfo info)
	{
        String positionLevel = info.getPositionLevel();
        if(positionLevel==null){
            for(String col : info.groupByColums())
            {
                if( PositionSubscriptionHelper.getSettlementDateColName().equals(col) || PositionSubscriptionHelper.getCurrencyPairColName().equals(col) )
                {
                    continue;
                }

                else if( PositionSubscriptionHelper.getleColName().equals(col) )
                {
                    if(null!=info.getLegalEntity() && null!= info.getLegalEntity().getNamespace()){
                        return getCacheKey( AccountOwnerLevel.LE.name() , info.getLegalEntity().getShortName() , info.getLegalEntity().getNamespace().getShortName() );
                    }else{
                        log.info("Position Subscription request for LegalEntity level is missing LegalEntity information.Default level will be considered:"+ info);
                    }
                }
            }

            // requested view - USER - Summary
            if( info.getUser() != null ){
                return getCacheKey( AccountOwnerLevel.USER.name() , info.getUser().getShortName() , info.getOrganization().getNamespace().getShortName() );
            }

            // requested view ORG - Summary
            return getCacheKey( AccountOwnerLevel.ORG.name() , info.getOrganization().getShortName() , info.getOrganization().getNamespace().getShortName() );
        }else{
            //positionLevel is pre-validated for null and valid Levels
            AccountOwnerLevel accountOwnerLevel = AccountOwnerLevel.valueOf(positionLevel);
            switch (accountOwnerLevel ){
                case ORG:
                    return getCacheKey( AccountOwnerLevel.ORG.name() , info.getOrganization().getShortName() , info.getOrganization().getNamespace().getShortName() );
                case USER:
                    return getCacheKey( AccountOwnerLevel.USER.name() , info.getUser().getShortName() , info.getOrganization().getNamespace().getShortName() );
                case LE:
                    if(null!=info.getLegalEntity() && null!= info.getLegalEntity().getNamespace()){
                        return getCacheKey( AccountOwnerLevel.LE.name() , info.getLegalEntity().getShortName() , info.getLegalEntity().getNamespace().getShortName() );
                    }else{
                        log.info("Position Subscription request for LegalEntity level is missing LegalEntity information.Default level will be considered:"+ info);
                    }
            }
            return getCacheKey( AccountOwnerLevel.ORG.name() , info.getOrganization().getShortName() , info.getOrganization().getNamespace().getShortName() );
        }
	}
	
	public static CompositeKeysGeneric getOrgLevelCacheKey(
			PositionSubscriptionInfo info) {
		// requested view ORG - Summary
		return getCacheKey(AccountOwnerLevel.ORG.name(), info.getOrganization()
				.getShortName(), info.getOrganization().getNamespace()
				.getShortName());

	}

	public static CompositeKeysGeneric getUserLevelCacheKey(
			PositionSubscriptionInfo info) {
		// requested view - USER - Summary
		return getCacheKey(AccountOwnerLevel.USER.name(), info.getRequestUser()
				.getShortName(), info.getOrganization().getNamespace()
				.getShortName());
	}

}
