package com.integral.position;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.google.common.base.Optional;
import com.integral.event.Cache;
import com.integral.event.Filter;
import com.integral.event.Identifiable;
import com.integral.model.AccountOwner.AccountOwnerLevel;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;
import com.integral.util.CollectionUtil;
import com.integral.util.Converter;

public class PositionEventCache implements Cache<String, Position, PositionFilterType>
{

	/* 
	 * Cache is arranged in 'Organization{Instrument{valueDate, Position}}' structure. So provide filters in this order. The missing level will be considered as wild card. 
	 */
	@Override
	public Iterable<Position> getByCriteria(Iterable<Filter<Position, PositionFilterType>> criterias) 
	{
		if(positionMap.isEmpty())
		{
			return Collections.emptyList();
		}
		
		int filterIndex = 0;
		Filter<Position, PositionFilterType> lastIteratedFilter = null;
		
		List<Map<String, Map<Date, Position>>> relevantInstBucketList = new ArrayList<Map<String,Map<Date,Position>>>(); // All positions meeting Organization criteria (if defined).
		
		List<Map<Date, Position>> relevantValueDateBucketList = new ArrayList<Map<Date,Position>>(); // All positions meeting Currency pair and Organization criteria (if defined).
		
		List<Position> relevantPositionList = new ArrayList<Position>(); // All positions meeting all criteria.
		
		for (Filter<Position, PositionFilterType> filter : criterias) 
		{
			switch(filter.getId())
			{
				// update 'relevantInstBucketList' with position collection meeting Organization criteria
				case Organization:
				{
					filterIndex = ensureCorrectFilterOrderForOrganization(++filterIndex);
					
					for( Entry<String, Map<String, Map<Date, Position>>> entry : positionMap.entrySet())
					{
						Map<String, Map<Date, Position>> instToVdToPositionMap = entry.getValue();
						
						if(instToVdToPositionMap.isEmpty())
						{
							continue;
						}
						
						for(Map<Date, Position> vdToPositionMap : instToVdToPositionMap.values())
						{
							if(!vdToPositionMap.isEmpty())
							{
								if(filter.accept(vdToPositionMap.values().iterator().next()))
								{
									relevantInstBucketList.add(instToVdToPositionMap);
								}
								break;
							}
						}
					}
						
					break;
				}
				// update 'relevantValueDateBucketList' with all collections meeting Organization (filtered in last section) and CurrencyPair criteria.
				case CurrencyPair: 
				{
					filterIndex = ensureCorrectFilterOrderForCurrencyPair(++filterIndex, lastIteratedFilter);
					
					// Organization is not defined so evaluate all CurrencyPair from all organizations.
					if(filterIndex == 1)
					{
						for( Entry<String, Map<String, Map<Date, Position>>> entry : positionMap.entrySet())
						{
							Map<String, Map<Date, Position>> instToVdToPositionMap = entry.getValue();
							
							if(instToVdToPositionMap.isEmpty())
							{
								continue;
							}
							
							relevantInstBucketList.add(instToVdToPositionMap);
						}
					}
					
					// evaluate for CurrencyPair criteria and update relevantValueDateBucketList
					for(Map<String, Map<Date, Position>> instToVdToPositionMap : relevantInstBucketList)
					{
						if(instToVdToPositionMap.isEmpty())
						{
							continue;
						}
						
						for(Map<Date, Position> vdToPositionMap : instToVdToPositionMap.values())
						{
							if(!vdToPositionMap.isEmpty())
							{
								if(filter.accept(vdToPositionMap.values().iterator().next()))
								{
									relevantValueDateBucketList.add(vdToPositionMap);
								}
								break;
							}
						}
					}
					
					
					break;
				}
				
				// evaluate for value date criteria and update relevantPositionList
				case ValueDate: 
				{
					if(relevantInstBucketList.isEmpty())
					{
						for( Entry<String, Map<String, Map<Date, Position>>> orgBucket : positionMap.entrySet())
						{
							addAllFilteredPositionToRelevantPositionList(relevantPositionList, filter, orgBucket.getValue());
						}
					}
					else
					{
						// get all positions meeting Value date criteria
						for(Map<String, Map<Date, Position>> relevantInstBucket : relevantInstBucketList)
						{
							addAllFilteredPositionToRelevantPositionList(relevantPositionList, filter, relevantInstBucket);
						}
					}
					
					break;
				}
				
				default: throw new RuntimeException("Invalid Position filter Id");
			}
		}
		
		return relevantPositionList;
	}

	
	private void addAllFilteredPositionToRelevantPositionList(
			List<Position> filteredPositionList,
			Filter<Position, PositionFilterType> filter,
			Map<String, Map<Date, Position>> instToVdToPositionMap) 
	{
		
		if(instToVdToPositionMap.isEmpty())
		{
			return;
		}
		
		for(Map<Date, Position> vdToPositionMap : instToVdToPositionMap.values())
		{
			if(!vdToPositionMap.isEmpty())
			{
				for(Position p : vdToPositionMap.values())
				{
					if(filter.accept(p))
					{
						filteredPositionList.add(p);
					}
				}
			}
		}
	}

	private int ensureCorrectFilterOrderForCurrencyPair(int filterIndex,
			Filter<Position, PositionFilterType> lastIteratedFilter) {
		if(filterIndex == 1 || 
				(filterIndex == 2 && lastIteratedFilter.getId() == PositionFilterType.Organization))
		{
			throw new RuntimeException("CurrencyPair filter if define should be either first in the list or second if previous one is Organization");
		}
		return filterIndex;
	}

	private int ensureCorrectFilterOrderForOrganization(int filterIndex) {
		if(filterIndex != 1)
		{
			throw new RuntimeException("Organization filter if define should be the first in the list");
		}
		return filterIndex;
	}

	@Override
	public Optional<Position> get(String id) 
	{
		PositionWrapper wrapper = idToPositionMap.get(id);
		return Optional.fromNullable(wrapper!=null ? wrapper.position : null);
	}

	@Override
	public Position cache(Position position) 
	{
		if(AccountOwnerLevel.ORG != position.getAccountOwner().getAccountOwnerLevel())
		{
			throw new RuntimeException("Account owner level other than 'ORG' not supported");
		}
		
		// get Organization bucket
		Map<String, Map<Date, Position>> instVDToPositionMap = positionMap.get(position.getAccountOwner().getEntityName());
		
		// get currency pair bucket
		Map<Date, Position> vdToPositionMap = instVDToPositionMap.get(position.getCurrencyPair());
		
		// cache value date to position mapping
		updatePositionInCache(position, vdToPositionMap);
		
		return position;
	}

	@Override
	public Iterable<Position> removeByCriteria(Iterable<Filter<Position, PositionFilterType>> criteria) 
	{
		return Collections.emptyList();
	}

	@Override
	public Optional<Position> remove(String id) 
	{
		PositionWrapper p = idToPositionMap.remove(id);
		return Optional.fromNullable(p.valueDateMapRef.remove(p.position.getValueDate()));
	}
	
	private void updatePositionInCache(Position position, Map<Date, Position> vdToPositionMap) 
	{
		idToPositionMap.put(position.getId(), new PositionWrapper(position, vdToPositionMap));
		vdToPositionMap.put(position.getValueDate(), position);
	}
	
	/**
	 * Organization{Instrument{valueDate, Position}}
	 */
	private final Map<String, Map<String, Map<Date, Position>>> positionMap = new HashMap<String, Map<String,Map<Date,Position>>>();
	private final Map<String, PositionWrapper> idToPositionMap = new HashMap<String, PositionWrapper>();
	
	/**
	 * Wrapper class to hold cross reference to 'value date to position map' for faster cleanup.
	 * <AUTHOR>
	 */
	private static class PositionWrapper implements Identifiable<String>
	{
		private final Position position;
		private final Map<Date, Position> valueDateMapRef;
		
		public PositionWrapper(Position p, Map<Date, Position> valueDateMapRef) 
		{
			this.position = p;
			this.valueDateMapRef = valueDateMapRef;
		}
		
		@Override
		public String getId() 
		{
			return position.getId();
		}
	}

	@Override
	public void clear()
	{
		positionMap.clear();
		idToPositionMap.clear();
	}


	@Override
	public String getCacheDetails()
	{
		return PositionEventCache.class.getSimpleName();
	}


	@Override
	public String getCacheDetails(Converter<Position, String> stringTransform)
	{
		return getCacheDetails();
	}


	@SuppressWarnings("unchecked")
	@Override
	public Iterator<Position> iterator()
	{
		List<Iterator<Position>> iterators = new ArrayList<Iterator<Position>>();
		
		for(Map<String, Map<Date, Position>> org : positionMap.values())
		{
			for(Map<Date, Position> cur: org.values())
			{
				iterators.add(cur.values().iterator());
			}
		}
		
		return CollectionUtil.<Position>join((Iterator<Position>[]) iterators.toArray());
	}
}
