package com.integral.position;

import com.integral.event.Listener;
import com.integral.finance.counterparty.UserCounterpartyGroup;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.User;
import com.integral.util.CollectionUtil;

/**
 * Created by raghunathans on 9/22/17.
 */
public class UserSalesDealerChangeListener implements Listener<UserCounterpartyGroup> {

    private PositionCache cachingStrategy;
    private User user;

    private static final Log log = LogFactory.getLog(PositionDistributionManager.class);

    public UserSalesDealerChangeListener(User user, PositionCache cachingStrategy) {
        this.cachingStrategy = cachingStrategy;
        this.user = user;
    }

    @Override
    public void onEvent(UserCounterpartyGroup userCounterpartyGroup) {

        String orgShortName = this.user.getOrganization().getShortName();
        if(userCounterpartyGroup.getOrganization().getShortName().equals(orgShortName))
        {
            log.info("Received change in the user counter party group.Clearing the sales dealer aggregated position cache for the user:"+user.getShortName()+", of the org:"+orgShortName);
            this.cachingStrategy.clear();
        }
    }

}
