/**
 * <AUTHOR>
 */
package com.integral.position;

import com.integral.event.Listener;
import com.integral.finance.positions.service.PositionSubscriptionInfo;
import com.integral.message.WorkflowMessage;
import com.integral.model.Position;
import com.integral.user.User;

/**
 * <AUTHOR>
 *
 */
public interface PositionService
{
	/**
	 * Subscribe to position update for given set of parameters. if position service doesn't support
	 * updates for give parameters then error will be returned.
	 * Every {@link PositionSubscriptionInfo} should carry a unique id which can be used to subscribe.
	 * @param request
	 * @param callbackHandler
	 * @return
	 */
	WorkflowMessage subscribe(PositionSubscriptionInfo request,  Listener<Position> callbackHandler);
	
	/**
	 * Un-subscribe from position update. 
	 * @param requestId
	 * @return
	 */
	WorkflowMessage unsubscribe(User user, String requestId);
	
	/**
	 * Un-subscribes all the existing requests for position update. This can be used at the time
	 * of logout.
	 * @param user
	 * @return
	 */
	WorkflowMessage unsubscribe(User user);

	/**
	 * Returns position for given parameters.
	 * @param query
	 * @return
	 */
	WorkflowMessage queryPositions(PositionSubscriptionInfo query);
}
