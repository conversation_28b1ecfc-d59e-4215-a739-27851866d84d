package com.integral.position;

import com.google.common.base.Objects;
import com.google.common.base.Optional;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableMap.Builder;
import com.integral.event.Filter;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;
import com.integral.model.PositionState;
import com.integral.util.*;

import java.lang.reflect.Array;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Cache maintains aggregated and un-aggregated position for various
 * value dates per currency pair. Mainly written from the prospective of
 * caching positions for a Sales dealer and provide real-time aggregated position per
 * value date per currency pair. However, this cache can be used for any other
 * scenario with same requirement.
 * <p>
 * Unaggregated cache on the top level has map of currency pair vs value date
 * position cache (which is also called CurrencyLevel cache because it holds
 * position for a currency pair). Value date position cache is map of
 * value date vs position cache on organization (which is
 * Map&lt;String,Position&gt; and also referred as value date level cache as it
 * holds position for single value date).
 * 
 * <AUTHOR>
 * 
 */
public class PositionCacheInCurrencyValueDateAndOrgHierarchy implements PositionCache
{
	/**
	 * {Instrument{valueDate{Organization/User, Position}}}
	 */
	private final ConcurrentHashMap<String, AggregatedCurrencyLevelConcurrentHashMap> positionCache = new ConcurrentHashMap<String, AggregatedCurrencyLevelConcurrentHashMap>();
	private static final Log log = LogFactory.getLog(PositionCacheInCurrencyValueDateAndOrgHierarchy.class);

	/* 
	 * Cache is arranged in 'Instrument{valueDate{Organization/User, Position}}' structure. So provide filters in this order. The missing level will be considered as wild card. 
	 */
	@Override
	public Iterable<Position> getByCriteria(Iterable<Filter<Position, PositionFilterType>> criterias) 
	{
		if(positionCache.isEmpty())
		{
			log.debug("cache is empty");
			return Collections.emptyList();
		}
		
		final List<Position> aggregatedPositionList = new ArrayList<Position>();
		
		Filter<Position, PositionFilterType> filter = Filter.Util.combineAsAnd(criterias);
		
		for (Entry<String, AggregatedCurrencyLevelConcurrentHashMap> currencyBucket : positionCache.entrySet()) 
		{
			List<Position> aggregatedPositionsAtValueDateLevel = new ArrayList<Position>();
			for (AggregatedValueDateLevelConcurrentHashMap valueDateLevelCache : currencyBucket.getValue().values())
			{
				List<Position> relevantPositionsAtValueDateLevel = new ArrayList<Position>();
				
				Iterator<Position> iterator = valueDateLevelCache.values().iterator();
				while(iterator.hasNext())
				{
					Position position = iterator.next();
					if(filter.accept(position))
					{
						relevantPositionsAtValueDateLevel.add(position);
					}
				}
				
				if(!relevantPositionsAtValueDateLevel.isEmpty())
				{
					aggregatedPositionsAtValueDateLevel.add(PositionAggregator.ValueDateLevel.convert(relevantPositionsAtValueDateLevel));
				}
			}
			
			if(!aggregatedPositionsAtValueDateLevel.isEmpty())
			{
				aggregatedPositionList.add(PositionAggregator.CurrencyLevel.convert(aggregatedPositionsAtValueDateLevel));
			}
		}
		
		log.debug("Returning following positions from cache:");
		log.debug(new MessageBuilder()
		{
			@Override
			public String build()
			{
				return StringUtil.join(',', Converters.ToString.<Position>getToStringConverter(), aggregatedPositionList);
			}
		});
		
		return aggregatedPositionList;
	}
	
	@Override
	public Optional<Position> getAggregatedPosition(String currencyPair, Iterable<Filter<Position, PositionFilterType>> filters)
	{
		if(CollectionUtil.isEmptyOrNull(filters))
		{
			return Optional.<Position>fromNullable(getOrCreateCurrencyLevelCache(currencyPair).getAggregatedPosition());
		}
		// IVO
		
		AggregatedCurrencyLevelConcurrentHashMap valueDateLevelCache = getOrCreateCurrencyLevelCache(currencyPair);
		List<Position> aggregatedPositionAtDifferentValueDate = new ArrayList<Position>();
		
		for (Entry<Date, AggregatedValueDateLevelConcurrentHashMap> valueDateBucket : valueDateLevelCache.entrySet())
		{
			List<Position> relevantChildAtSameVD = new ArrayList<Position>();
			
			for(Position p : valueDateBucket.getValue().values())
			{
				if(Filter.Util.matchesAll(p, filters))
				{
					relevantChildAtSameVD.add(p); 
				}
			}
			
			if(!relevantChildAtSameVD.isEmpty())
			{
				aggregatedPositionAtDifferentValueDate.add(PositionAggregator.ValueDateLevel.convert(relevantChildAtSameVD));
			}
		}
		
		if(aggregatedPositionAtDifferentValueDate.isEmpty())
		{
			return Optional.<Position>absent();
		}
		
		return Optional.<Position>of(PositionAggregator.CurrencyLevel.convert(aggregatedPositionAtDifferentValueDate));
	}

	/** 
	 * This method will return <b>unaggregated</b> position with mentioned id. This operation is costly because it has to iterate full cache for this, please consider using <i>getByCriteria(Iterable{Filter{Position, PositionFilterType}})</i>
	 */
	@Override
	public Optional<Position> get(final String id) 
	{
		for (Entry<String, AggregatedCurrencyLevelConcurrentHashMap>  currencyLevelCache : positionCache.entrySet())
		{
			for(AggregatedValueDateLevelConcurrentHashMap valueDateLevelCache : currencyLevelCache.getValue().values())
			{
				for(Position p : valueDateLevelCache.values())
				{
					if(Objects.equal(id, p.get_id()))
					{
						return Optional.of(p);
					}
				}
			}
		}
		return Optional.absent();
	}

	@Override
	public Position cache(Position position) 
	{
		String currencyPair = position.getCurrencyPair();
		Date valueDate = position.getValueDate();
		
		// get currency pair bucket
		AggregatedCurrencyLevelConcurrentHashMap currencyLevelCache = getOrCreateCurrencyLevelCache(currencyPair);
		// get value date bucket
		AggregatedValueDateLevelConcurrentHashMap valueDateLevelCache = getOrCreateValueDateLevelCache(valueDate, currencyLevelCache);

		if(PositionState.ROLLED == position.getState())
		{
			// remove rolled position from cache
			valueDateLevelCache.remove(position.getAccountOwner().getEntityName());
            if(log.isDebugEnabled()){
                log.debug("OrgHierarchy.cache():Removing following position from the cache:"+position);
            }
		}
		else
		{
			// update value date position cache for organization 
			valueDateLevelCache.put(position.getAccountOwner().getEntityName(), position);
            if(log.isDebugEnabled()){
                log.debug("OrgHierarchy.cache():Updated following position into the cache:"+position);
            }
		}
		
		return position;
	}

	/* 
	 * Defining no filter is equivalent calling clear on cache. </p> <b>Note:</b> remove operation reply on position server for periodic updates to re-populate and aggregate cache. 
	 */
	@Override
	public Iterable<Position> removeByCriteria(Iterable<Filter<Position, PositionFilterType>> criterias) 
	{
		final List<Position> removedPositionList = new ArrayList<Position>();
		
		Filter<Position, PositionFilterType> filter = Filter.Util.combineAsAnd(criterias);
		
		// identify currencies for which positions to be removed
		for (Entry<String, AggregatedCurrencyLevelConcurrentHashMap> currencyBucket : positionCache.entrySet()) 
		{
			for (AggregatedValueDateLevelConcurrentHashMap valueDateLevelCache : currencyBucket.getValue().values()) // iterating to analyze value date level cache
			{
				Iterator<Position> iterator = valueDateLevelCache.values().iterator();
				while(iterator.hasNext()) // iterating on position per organization
				{
					Position position = iterator.next();
					if(filter.accept(position))
					{
						removedPositionList.add(position);
						iterator.remove();
					}
				}
			}
		}

       if(log.isDebugEnabled()){
            log.debug("Removing following positions from cache:");
            log.debug(new MessageBuilder()
            {
                @Override
                public String build()
                {
                    return StringUtil.join(',', Converters.ToString.<Position>getToStringConverter(), removedPositionList);
                }
            });
        }
		
		return removedPositionList;
	}


	/*
	 * Method will simply remove position with provided 'id'. Cache will return removed value.
	 */
	@Override
	public Optional<Position> remove(String id) 
	{
		for (Entry<String, AggregatedCurrencyLevelConcurrentHashMap>  currencyLevelCache : positionCache.entrySet())
		{
			for(AggregatedValueDateLevelConcurrentHashMap valueDateLevelCache : currencyLevelCache.getValue().values())
			{
				for(Entry<String, Position> entry : valueDateLevelCache.entrySet())
				{
					if(Objects.equal(id, entry.getValue().get_id()))
					{
                        if(log.isDebugEnabled()){
                            log.debug("remove():Removed the following position from the cache:"+entry.getValue());
                        }
						return Optional.of(entry.getValue());
					}
				}
			}
		}
		
		return Optional.absent();
	}
	
	/**
	 * Recompute internal structure of cache. This can be used in scenario wherein cache is corrupted because of bad entry. You evicted that entry and now you want internal structure to re-organize.  
	 */
	protected void refresh()
	{
		log.info("Refreshing cache");
	}
	
	
	public void clear()
	{
		log.info("clearing cache");
		
		// iterating on currency bucket
		for (Entry<String, AggregatedCurrencyLevelConcurrentHashMap> currencyLevelCache : positionCache.entrySet()) 
		{
			for (AggregatedValueDateLevelConcurrentHashMap valueDateLevelCache : currencyLevelCache.getValue().values())
			{
				valueDateLevelCache.clear();
			}
			currencyLevelCache.getValue().clear();
		}
		positionCache.clear();
	}
	
	private AggregatedCurrencyLevelConcurrentHashMap getOrCreateCurrencyLevelCache(String currencyPair)
	{
		AggregatedCurrencyLevelConcurrentHashMap currencyLevelCache = positionCache.get(currencyPair);
		if(currencyLevelCache==null)
		{
			AggregatedCurrencyLevelConcurrentHashMap previousValue = positionCache.putIfAbsent(currencyPair, currencyLevelCache = new AggregatedCurrencyLevelConcurrentHashMap());
			if(previousValue != null)
			{
				currencyLevelCache = previousValue;
			}
			else
			{
				log.info(String.format("new position bucket created for currency pair{%s}", currencyPair));
			}
		}
		return currencyLevelCache;
	}
	
	private AggregatedValueDateLevelConcurrentHashMap getOrCreateValueDateLevelCache(Date valueDate, AggregatedCurrencyLevelConcurrentHashMap currencyLevelCache)
	{
		AggregatedValueDateLevelConcurrentHashMap valueDateBucket = currencyLevelCache.get(valueDate);
		if(valueDateBucket==null)
		{
			AggregatedValueDateLevelConcurrentHashMap previousValue = currencyLevelCache.putIfAbsent(valueDate, valueDateBucket = new AggregatedValueDateLevelConcurrentHashMap());
			if(previousValue != null)
			{
				valueDateBucket = previousValue;
			}
			else
			{
				log.infoAsFormat("new position bucket created for value date {%s}", valueDate);
			}
		}
		return valueDateBucket;
	}
	
	private AggregatedValueDateLevelConcurrentHashMap getOrCreateValueDateLevelCache(Date valueDate, String currencyPair)
	{
		AggregatedCurrencyLevelConcurrentHashMap currencyLevelCache = getOrCreateCurrencyLevelCache(currencyPair);
		return getOrCreateValueDateLevelCache(valueDate, currencyLevelCache);
	}

	@Override
	public String getCacheDetails()
	{
		return StringUtil.<String, AggregatedCurrencyLevelConcurrentHashMap>join(positionCache, ",", "=", Converters.<String>getPassThroughConverter(), new Converter<AggregatedCurrencyLevelConcurrentHashMap, String>()
        {
            @Override
            public String convert(AggregatedCurrencyLevelConcurrentHashMap from_)
            {
                return from_.getAggregatedPosition().toString();
            }
        });
	}

	@Override
	public String getCacheDetails(final Converter<Position, String> positionToStringConverter)
	{
		return StringUtil.<String, AggregatedCurrencyLevelConcurrentHashMap>join(positionCache, ",", "=", Converters.<String>getPassThroughConverter(), new Converter<AggregatedCurrencyLevelConcurrentHashMap, String>()
		{
			@Override
			public String convert(AggregatedCurrencyLevelConcurrentHashMap from_)
			{
				return positionToStringConverter.convert(from_.getAggregatedPosition());
			}
		});
	}
	
	public ImmutableMap<String, ImmutableMap<Date,ImmutableMap<String, Position>>> getImmutableViewOfUnaggregatedPositions()
	{
		Builder<String, ImmutableMap<Date, ImmutableMap<String, Position>>> currencyCachebuilder = ImmutableMap.<String, ImmutableMap<Date, ImmutableMap<String, Position>>>builder();
		for (Entry<String, AggregatedCurrencyLevelConcurrentHashMap>  currencyLevelCache : positionCache.entrySet())
		{
			Builder<Date, ImmutableMap<String, Position>> vdCachebuilder = ImmutableMap.<Date, ImmutableMap<String, Position>>builder();
			for(Entry<Date, AggregatedValueDateLevelConcurrentHashMap> valueDateLevelCache : currencyLevelCache.getValue().entrySet())
			{
			
				vdCachebuilder.put(valueDateLevelCache.getKey(), ImmutableMap.<String, Position>copyOf(valueDateLevelCache.getValue()));
			}
			
			currencyCachebuilder.put(currencyLevelCache.getKey(), vdCachebuilder.build());
		}
		return currencyCachebuilder.build();
	}
	
	
	public ImmutableMap<String, Position> getImmutableViewOfAggregatedPositions()
	{
		Builder<String, Position> builder = ImmutableMap.<String, Position>builder();
		for (Entry<String, AggregatedCurrencyLevelConcurrentHashMap>  entry : positionCache.entrySet())
		{
			builder.put(entry.getKey(), entry.getValue().getAggregatedPosition());
		}
		return builder.build();
	}


	@Override
	public Iterator<Position> iterator()
	{
		List<Iterator<Position>> iterators = new ArrayList<Iterator<Position>>();
		
		for(AggregatedCurrencyLevelConcurrentHashMap cur : positionCache.values())
		{
			for(AggregatedValueDateLevelConcurrentHashMap org: cur.values())
			{
				iterators.add(org.values().iterator());
			}
		}
		return CollectionUtil.<Position>join(iterators.toArray((Iterator<Position>[]) Array.newInstance(Iterator.class, 0)));
	}
}