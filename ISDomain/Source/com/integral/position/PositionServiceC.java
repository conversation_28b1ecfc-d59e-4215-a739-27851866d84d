/**
 * <AUTHOR>
 */
package com.integral.position;

import com.integral.event.Listener;
import com.integral.finance.positions.service.PositionSubscriptionInfo;
import com.integral.message.MessageEvent;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.model.Position;
import com.integral.user.User;

/**
 * <AUTHOR>
 *
 */
public class PositionServiceC implements PositionService
{

    public static final String DUPLICATE_POSITION_SUBSCRIPTION = "Request.Validation.Duplicate.Subscription";
    String TOPIC = "POSITION";

	public PositionServiceC()
	{
		
	}
	
	/* (non-Javadoc)
	 * @see com.integral.position.PositionService#subscribe(com.integral.finance.positions.service.PositionSubscriptionInfo, com.integral.position.PositionUpdateHandler)
	 */
	@Override
	public WorkflowMessage subscribe( PositionSubscriptionInfo request,  Listener<Position> callbackHandler )
	{
		WorkflowMessage response = MessageFactory.newWorkflowMessage(request,MessageEvent.SUBSCRIBE,TOPIC);
		
		//TODO 1. validate position request.
		boolean isDuplicate = PositionDistributionManager.getInstance().addHandler(request, callbackHandler);
		if( isDuplicate )
		{
			response.addError(DUPLICATE_POSITION_SUBSCRIPTION);
		}
		return response;
	}

	/* (non-Javadoc)
	 * @see com.integral.position.PositionService#unsubscribe(java.lang.String)
	 */
	@Override
	public WorkflowMessage unsubscribe(User user, String requestId )
	{
		boolean subscriptionExist = PositionDistributionManager.getInstance().removeHandler(user, requestId);
		WorkflowMessage response = MessageFactory.newWorkflowMessage();
		response.setEventName("Unsubscribe");
		response.setReferenceId(requestId);
		response.setTopic(TOPIC);
		if(subscriptionExist)
		{
			response.setStatus(MessageStatus.SUCCESS);
		}
		else
		{
			response.setStatus(MessageStatus.FAILURE);
			response.setError("No subscription exist", null);
		}
		return response;
	}

	/* (non-Javadoc)
	 * @see com.integral.position.PositionService#unsubscribe(com.integral.user.User)
	 */
	@Override
	public WorkflowMessage unsubscribe( User user )
	{
		WorkflowMessage response = MessageFactory.newWorkflowMessage();
		response.setEventName("Unsubscribe");
		response.setTopic(TOPIC);
		try 
		{
			PositionDistributionManager.getInstance().removeAllHandler(user);
			response.setStatus(MessageStatus.SUCCESS);
		} 
		catch (Exception e) 
		{
			response.addError(e);
			response.setStatus(MessageStatus.FAILURE);
		}
		return response;
	}

	/* (non-Javadoc)
	 * @see com.integral.position.PositionService#queryPositions(com.integral.finance.positions.service.PositionSubscriptionInfo)
	 */
	@Override
	public WorkflowMessage queryPositions( PositionSubscriptionInfo query )
	{
		WorkflowMessage response = MessageFactory.newWorkflowMessage();
		response.setEventName("Unsubscribe");
		response.setTopic(TOPIC);
		try 
		{
			PositionDistributionManager.getInstance();
			
			response.addError("Position query not supported");
			response.setStatus(MessageStatus.FAILURE);
		} 
		catch (Exception e) 
		{
			response.addError(e);
			response.setStatus(MessageStatus.FAILURE);
		}
		return response;
	}

}
