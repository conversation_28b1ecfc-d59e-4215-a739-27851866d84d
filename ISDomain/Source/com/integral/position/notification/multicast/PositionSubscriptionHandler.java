/**
 * <AUTHOR>
 */
package com.integral.position.notification.multicast;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.event.SupplyableDataSource;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.model.Position;
import com.integral.notification.SubscriptionHandler;
import com.integral.notification.SubscriptionService;
import com.integral.notification.multicast.MulticastSubscriptionServiceC;
import com.integral.notification.multicast.PositionSerializer;
import com.integral.organization.MulticastAddress;
import com.integral.pipeline.metrics.Metrics;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.position.PositionDistributionManager;
import com.integral.position.PositionServiceConfig;
import com.integral.position.PositionServiceConfigC;
import com.integral.transport.multicast.MulticastAddressPoolService;
import com.integral.transport.multicast.MulticastGroup;
import com.integral.user.Organization;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Callback handler for {@link MulticastSubscriptionServiceC}. Its bridge between {@link MulticastSubscriptionServiceC}
 * and {@link PositionDistributionManager}. {@link PositionDistributionManager} is responsible for distributing position updates in thread safe manner.
 *
 * <AUTHOR>
 */
public class PositionSubscriptionHandler implements SubscriptionHandler<UnSafeBuffer> {
    private SupplyableDataSource<Position> source;

    final ConcurrentHashMap<Integer, AtomicInteger> subscribedNS = new ConcurrentHashMap<Integer, AtomicInteger>(20);

    private int receivedCount = 0;

    private int droppedCount = 0;
    private SubscriptionService multicastSubscriptionService;

    private static final Log log = LogFactory.getLog(PositionSubscriptionHandler.class);

    public PositionSubscriptionHandler(SupplyableDataSource<Position> supplyableDataSource) {
        this.source = supplyableDataSource;
        MetricsManager.instance().register(new PositionReceiverMetrics());
    }


    public void subscribe(Organization org) {
        AtomicInteger count = subscribedNS.get(org.getIndex());
        if (count == null) {
            AtomicInteger newCount = new AtomicInteger(0);
            count = subscribedNS.putIfAbsent(org.getIndex(), newCount);
            if (count == null) {
                count = newCount;
            }
            joinMultiCastGroup(org);
        }
        count.incrementAndGet();
    }



    public void unsubscribe(Organization org) {
        int nameSpace = org.getIndex();
        AtomicInteger count = subscribedNS.get(nameSpace);
        if (null != count ) {
            if( count.get() > 0){
                count.decrementAndGet();
            }else if( count.get() == 0){
                leaveMultiCastGroup(org);
            }
        }
    }

    /* (non-Javadoc)
     * @see com.integral.notification.SubscriptionHandler#onReceiveObject(com.integral.spaces.SpaceEntity)
     */
    @Override
    public void onReceiveObject(UnSafeBuffer buffer) {
        int ns = PositionSerializer.getNameSpace(buffer);
        AtomicInteger c = subscribedNS.get(ns);
        if (c != null && c.get() > 0) {
            Position newUpdate = PositionSerializer.deSerialize(buffer);
            receivedCount++;
            source.supply(newUpdate);
        } else {
            droppedCount++;
        }
    }

    public boolean isSubscribed(Organization org) {
        int nameSpace = org.getIndex();
        return subscribedNS.containsKey(nameSpace);
    }

    public void setMulticastSubscriptionService(SubscriptionService multicastSubscriptionService) {
        this.multicastSubscriptionService =multicastSubscriptionService;
    }

    private class PositionReceiverMetrics implements Metrics {
        public StringBuilder report() {
            StringBuilder builder = new StringBuilder();
            builder.append("t=PRM, rc=").append(receivedCount).append(", dc=").append(droppedCount);
            //We will loose some metrics due to multi-thread updates on same variables, which is fine.
            receivedCount = 0;
            droppedCount = 0;
            return builder;
        }
    }

    private void joinMultiCastGroup(Organization org) {
        if(multicastSubscriptionService!=null){
            boolean hasJoined = false;
                String baseAddress = getMultiCastAddress(org);
                if(null!=baseAddress){
                    multicastSubscriptionService.joinGroup(baseAddress);
                    hasJoined= true;
                }

            if(!hasJoined){
                log.info("PositionSubscriptionHandler.joinMultiCastGroup():Joining the default Multicast Group  address for the organization :"+org.getShortName());
                multicastSubscriptionService.joinDefaultGroup();
            }
        }
    }

    String getMultiCastAddress(Organization org){
        if(PositionServiceConfigC.Instance.isUseMulticastAddressPool()) {
            try {
                MulticastAddress address = MulticastAddressPoolService.getInstance().createMulticastAddress(org.getShortName(), PositionServiceConfig.MCAST_ADDRESS_POSITON);
                return address.getMulitcastAddress();
            } catch (Exception e) {
                return null;
            }
        }else {
            MulticastGroup multicastGroup = org.getMulticastGroup();
            if (null != multicastGroup && null != multicastGroup.getBaseAddress() && multicastGroup.isEnabled()) {
                return multicastGroup.getBaseAddress();
            }
        }
        return null;
    }




    private void leaveMultiCastGroup(Organization org) {
        if(multicastSubscriptionService!=null){
            MulticastGroup multicastGroup = org.getMulticastGroup();
            if(null!=multicastGroup && null!= multicastGroup.getBaseAddress()&& multicastGroup.isEnabled()){
                String baseAddress = multicastGroup.getBaseAddress();
                if(null!=baseAddress){
                    log.info("PositionSubscriptionHandler.leaveMultiCastGroup():Leaving the Multicast Group address:"+baseAddress+", for the organization :"+org.getShortName());
                    multicastSubscriptionService.leaveGroup(baseAddress);
                }
            }
        }
    }

}