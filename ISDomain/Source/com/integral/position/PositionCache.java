package com.integral.position;

import com.google.common.base.Optional;
import com.integral.event.Cache;
import com.integral.event.Filter;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.
public interface PositionCache extends Cache<String, Position, PositionFilterType>
{
	public abstract Optional<Position> getAggregatedPosition(String currencyPair, Iterable<Filter<Position, PositionFilterType>> filters);

}