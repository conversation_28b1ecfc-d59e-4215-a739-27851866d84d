package com.integral.position;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;

import com.google.common.base.Objects;
import com.google.common.base.Optional;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableMap.Builder;
import com.integral.event.Filter;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;
import com.integral.model.PositionState;
import com.integral.util.CollectionUtil;
import com.integral.util.Converter;
import com.integral.util.Converters;
import com.integral.util.MessageBuilder;
import com.integral.util.StringUtil;

/**
 * Cache is designed to keep data in Map of Map scheme: Instrument{valueDate, Position}. When queried for a currency pair it returns aggregated position.
 * <AUTHOR>
 *
 */
public class PositionCacheOfCurrencyAndValueDateLevels implements PositionCache
{
	/**
	 * {Instrument{valueDate, Position}}
	 */
	private final ConcurrentHashMap<String, ConcurrentHashMap<Date, Position>> unaggregatedPositionMap = new ConcurrentHashMap<String,ConcurrentHashMap<Date,Position>>();
	
	private static final Log log = LogFactory.getLog(PositionCacheOfCurrencyAndValueDateLevels.class);
	
	/* 
	 * Cache is arranged in 'Instrument{valueDate, Position}' structure. So provide filters in this order. The missing level will be considered as wild card. 
	 */
	@Override
	public Iterable<Position> getByCriteria(Iterable<Filter<Position, PositionFilterType>> criterias) 
	{
		if(unaggregatedPositionMap.isEmpty())
		{
			log.debug("cache is empty");
			return Collections.emptyList();
		}
		
		final Map<String, Position> relevantPositionList = getAggregatedPositionMatchingFilter(Filter.Util.<Position, PositionFilterType>combineAsAnd(criterias));
		
		log.debug(new MessageBuilder()
		{
			@Override
			public String build()
			{
				return StringUtil.fastConcat(" aggregated values for position are : {", StringUtil.join(',', Converters.ToString.<Position>getToStringConverter(), relevantPositionList.values()));
			}
		});
		
		return relevantPositionList.values();
	}
	
	@Override
	public Optional<Position> getAggregatedPosition(String currencyPair, Iterable<Filter<Position, PositionFilterType>> filters)
	{
		ConcurrentHashMap<Date, Position> currencyDateBucket = unaggregatedPositionMap.get(currencyPair);
		List<Position> childrenWithDifferentValueDate = new ArrayList<Position>();
		
		for (Entry<Date, Position> valueDateBucket : currencyDateBucket.entrySet())
		{
			Position position = valueDateBucket.getValue();
			if(Filter.Util.matchesAll(position, filters))
			{
				childrenWithDifferentValueDate.add(position);
			}
		}
		
		if(childrenWithDifferentValueDate.isEmpty())
		{
			return Optional.<Position>absent();
		}
		
		return Optional.<Position>of(PositionAggregator.CurrencyLevel.convert(childrenWithDifferentValueDate));
	}


	/*
	 * Iterate over all cached positions. Aggregate all positions passing filter criteria on currency pair and add it to  'relevantPositionList'.
	 */
	private Map<String, Position> getAggregatedPositionMatchingFilter(Filter<Position, PositionFilterType> filter) 
	{
		Map<String, Position> relevantPositionList = new HashMap<String, Position>(); // All positions meeting all criteria.
		
		// iterating on currency bucket
		for (Entry<String, ConcurrentHashMap<Date, Position>> currencyBucket : unaggregatedPositionMap.entrySet()) 
		{
			// placeholder for aggregated position at currency pair level 
			Position positionAggregatedOnCurrencyPair = null;
			List<Position> childrenWithDifferentValueDate = new ArrayList<Position>();
			
			// lookup value date bucket to identify relevant child and build aggregated position while iterating  
			for (Entry<Date, Position> valueDateBucket : currencyBucket.getValue().entrySet())
			{
				Position positionForValueDate = valueDateBucket.getValue();
				
				// filter valid position as per defined filters 
				if(filter.accept(positionForValueDate))
				{
					// clone very first position as aggregated position
					if(positionAggregatedOnCurrencyPair == null)
					{
						positionAggregatedOnCurrencyPair = positionForValueDate.clone();
						positionAggregatedOnCurrencyPair.setAggregatedPositionLevelIndex(positionForValueDate.getAggregatedPositionLevelIndex()+1);
						PositionAggregator.CurrencyLevel.setCommonFields(positionAggregatedOnCurrencyPair);
						
						// don't miss first child 
						childrenWithDifferentValueDate.add(positionForValueDate);
						continue; // I don't want to combine this position to itself :) 
					}
					
					// update aggregated position
					positionAggregatedOnCurrencyPair = PositionAggregator.CurrencyLevel.combine(positionAggregatedOnCurrencyPair, positionForValueDate);
					
					// update child collection to provide with aggregated position 
					childrenWithDifferentValueDate.add(positionForValueDate);
				}
			}
			
			// its is possible to have no relevant position in cache
			if(positionAggregatedOnCurrencyPair!=null)
			{
				positionAggregatedOnCurrencyPair.setChildren(childrenWithDifferentValueDate);
				relevantPositionList.put(currencyBucket.getKey(), positionAggregatedOnCurrencyPair);
			}
		}
		
		return relevantPositionList;
	}

	/** 
	 * This method will return <b>unaggregated</b> position with mentioned id. This operation is costly because it has to iterate full cache for this, please consider using <i>getByCriteria(Iterable{Filter{Position, PositionFilterType}})</i>
	 */
	@Override
	public Optional<Position> get(final String id) 
	{
		for (Entry<String, ConcurrentHashMap<Date, Position>>  entry : unaggregatedPositionMap.entrySet())
		{
			for(Position p : entry.getValue().values())
			{
				if(Objects.equal(id, p.get_id()))
				{
					return Optional.of(p);
				}
			}
		}
		return Optional.absent();
	}

	@Override
	public Position cache(Position position) 
	{
		// get currency pair bucket
		String currencyPair = position.getCurrencyPair();
		ConcurrentHashMap<Date, Position> currencyBucket = getOrCreateInstrumentBucket(currencyPair);
		
		if(PositionState.ROLLED == position.getState())
		{
			// remove rolled position from cache
			currencyBucket.remove(position.getValueDate());
		}
		else
		{
			// update position for value date 
			currencyBucket.put(position.getValueDate(), position);
		}
		
		return position;
	}


	/* 
	 * Defining no filter is equivalent calling clear on cache. </p> <b>Note:</b> remove operation reply on position server for periodic updates to re-populate and aggregate cache. 
	 */
	@Override
	public Iterable<Position> removeByCriteria(Iterable<Filter<Position, PositionFilterType>> criterias) 
	{
		final Iterable<Position> removedPositionList = clearAllPositionsMatchingFilter(Filter.Util.<Position, PositionFilterType>combineAsAnd(criterias));
		
		log.debug("Removing following positions from cache:");
		log.debug(new MessageBuilder()
		{
			@Override
			public String build()
			{
				return StringUtil.join(',', Converters.ToString.<Position>getToStringConverter(), removedPositionList);
			}
		});
		
		return removedPositionList;
	}

	/**
	 * @param filter
	 * @return all position removed from cache
	 */
	private List<Position> clearAllPositionsMatchingFilter(Filter<Position, PositionFilterType> filter)
	{
		if(unaggregatedPositionMap.isEmpty())
		{
			return Collections.emptyList();
		}
		
		List<Position> removedPositionList = new ArrayList<Position>();
		
		// iterating on currency bucket
		for (Entry<String, ConcurrentHashMap<Date, Position>> currencyBucket : unaggregatedPositionMap.entrySet()) 
		{
			// lookup value date bucket to identify relevant child to remove
			Iterator<Entry<Date, Position>> valueDateToPositionMapIterator = currencyBucket.getValue().entrySet().iterator();
			
			while(valueDateToPositionMapIterator.hasNext())
			{
				Entry<Date, Position> valueDateBucket  = valueDateToPositionMapIterator.next();
				Position positionForValueDate = valueDateBucket.getValue();
				
				// filter valid position as per defined filters 
				try
				{
					if(filter.accept(positionForValueDate))
					{
						removedPositionList.add(positionForValueDate);
						valueDateToPositionMapIterator.remove();
					}
				} 
				catch (Exception e)
				{
					log.error("Exception while matching position{"+positionForValueDate+"} to remove from cache", e);
				}
			}
		}
		
		return removedPositionList;
	}

	/*
	 * Method will simply remove position with provided 'id'. Cache will return removed position.
	 */
	@Override
	public Optional<Position> remove(String id) 
	{
		// iterating on currency bucket
		for (Entry<String, ConcurrentHashMap<Date, Position>> currencyBucket : unaggregatedPositionMap.entrySet()) 
		{
			// lookup value date bucket to identify relevant child and build aggregated position while iterating  
			Iterator<Entry<Date, Position>> entrySetIterator = currencyBucket.getValue().entrySet().iterator();
			while(entrySetIterator.hasNext())
			{
				Entry<Date, Position> valueDateBucket = entrySetIterator.next();
				Position position = valueDateBucket.getValue();
				if(Objects.equal(position.get_id(), id))
				{
					entrySetIterator.remove();
					return Optional.of(position);
				}
			}
		}
		
		return Optional.absent();
	}
	
	/**
	 * Recompute internal structure of cache. This can be used in scenario wherein cache is corrupted because of bad entry. You evicted that entry and now you want internal structure to re-organize.  
	 */
	protected void refresh()
	{
		log.info("refreshing cache");
	}
	
	
	public void clear()
	{
		log.info("clearing cache");
		// As its clearing resuse structure don't destroy
		for(ConcurrentHashMap<Date, Position> currenctBucket : unaggregatedPositionMap.values())
		{
			currenctBucket.clear();
		}
	}
	
	private ConcurrentHashMap<Date, Position> getOrCreateInstrumentBucket(String currencyPair)
	{
		ConcurrentHashMap<Date, Position> instrumentBucket = unaggregatedPositionMap.get(currencyPair);
		if(instrumentBucket==null)
		{
			ConcurrentHashMap<Date, Position> previousValue = unaggregatedPositionMap.putIfAbsent(currencyPair, instrumentBucket = new ConcurrentHashMap<Date, Position>());
			if(previousValue != null)
			{
				instrumentBucket = previousValue;
			}
			else
			{
				log.info(String.format("new position bucket created for currency pair{%s}", currencyPair));
			}
		}
		return instrumentBucket;
	}

	@Override
	public String getCacheDetails()
	{
		return StringUtil.<String, ConcurrentHashMap<Date, Position>>join(unaggregatedPositionMap, ",", "=", Converters.<String>getPassThroughConverter(), new Converter<ConcurrentHashMap<Date, Position>, String>()
		{
			@Override
			public String convert(ConcurrentHashMap<Date, Position> from_)
			{
				return StringUtil.join(from_, ",", "=", Converters.ToString.<Date>getToStringConverter(), Converters.ToString.<Position>getToStringConverter());
			}
			
		});
	}

	@Override
	public String getCacheDetails(final Converter<Position, String> positionToStringConverter)
	{
		return StringUtil.<String, ConcurrentHashMap<Date, Position>>join(unaggregatedPositionMap, ",", "=", Converters.<String>getPassThroughConverter(), new Converter<ConcurrentHashMap<Date, Position>, String>()
		{
			@Override
			public String convert(ConcurrentHashMap<Date, Position> from_)
			{
				return StringUtil.join(from_, ",", "=", Converters.ToString.<Date>getToStringConverter(), positionToStringConverter);
			}
			
		});
	}
	
	public ImmutableMap<String, ImmutableMap<Date, Position>> getImmutableViewOfUnaggregatedPositions()
	{
		Builder<String, ImmutableMap<Date, Position>> builder = ImmutableMap.<String, ImmutableMap<Date, Position>>builder();
		for (Entry<String, ConcurrentHashMap<Date, Position>>  entry : unaggregatedPositionMap.entrySet())
		{
			builder.put(entry.getKey(), ImmutableMap.<Date, Position>copyOf(entry.getValue()));
		}
		return builder.build();
	}
	
	public ImmutableMap<String, Position> getImmutableViewOfAggregatedPositions()
	{
		return ImmutableMap.<String, Position>copyOf(getAggregatedPositionMatchingFilter(Filter.Util.<Position, PositionFilterType>passAllFilter()));
	}
	
	@Override
	public Iterator<Position> iterator()
	{
		List<Iterator<Position>> iterators = new ArrayList<Iterator<Position>>();
		
		for(ConcurrentHashMap<Date, Position> cur : unaggregatedPositionMap.values())
		{
			iterators.add(cur.values().iterator());
		}
		
		return CollectionUtil.<Position>join(iterators.toArray((Iterator<Position>[]) Array.newInstance(Iterator.class, 0)));
	}
}
