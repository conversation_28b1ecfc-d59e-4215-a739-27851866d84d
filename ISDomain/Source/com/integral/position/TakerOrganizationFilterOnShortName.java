package com.integral.position;

import com.google.common.collect.ImmutableSet;
import com.integral.model.AccountOwner;

/**
 * Created by raghunathans on 3/17/17.
 */
public class TakerOrganizationFilterOnShortName extends OrganizationFilterOnShortName {

    public TakerOrganizationFilterOnShortName(ImmutableSet<String> allowedOrganizations_) {
        super(allowedOrganizations_);
    }

    @Override
    public AccountOwner.AccountOwnerLevel getAccountOwnerLevel() {
        return AccountOwner.AccountOwnerLevel.TAKER_ORG;
    }
}
