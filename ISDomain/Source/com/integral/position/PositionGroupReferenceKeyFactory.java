package com.integral.position;

import com.integral.event.Factory;
import com.integral.finance.positions.service.PositionSubscriptionInfo;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.AccountOwner;
import com.integral.model.Position;
import com.integral.util.CompositeKeysGeneric;

/**
 * Created by raghunathans on 8/16/17.
 */
public class PositionGroupReferenceKeyFactory  implements Factory<CompositeKeysGeneric, Position>
{
    public static final String WILD_CARD = "*";
    protected Log log = LogFactory.getLog(PositionGroupReferenceKeyFactory.class);

    public CompositeKeysGeneric create(PositionSubscriptionInfo positionSubscriptionInfo)
    {
        return getCacheKey(positionSubscriptionInfo);
    }

    @Override
    public CompositeKeysGeneric create(Position position)
    {
        return getCacheKey(position.getAccountOwner().getAccountOwnerLevel().name(), WILD_CARD , position.getNamespaceName());
    }

    protected CompositeKeysGeneric getCacheKey(PositionSubscriptionInfo info)
    {
        return getCompositeKeys(info);
    }

    /**
     *
     * @param value
     * @param positionType
     * @return
     */
    public static CompositeKeysGeneric getCacheKey(String positionType, String value ,String namespace)
    {
        return CompositeKeysGeneric.getCompositeKeys(positionType, value, namespace);
    }

    /**
     * This is a bridge between old world and new world of position service. {@link com.integral.finance.positions.util.PositionSubscriptionHelperC}} defines types/level of positions
     * as column. In world they are defined by {@link com.integral.model.AccountOwner.AccountOwnerLevel}.      *
     */
    protected CompositeKeysGeneric getCompositeKeys(PositionSubscriptionInfo info)
    {
        String positionLevel = info.getPositionLevel();
        if(positionLevel==null){
            for(String col : info.groupByColums())
            {
                if( PositionSubscriptionHelper.getSettlementDateColName().equals(col) || PositionSubscriptionHelper.getCurrencyPairColName().equals(col))
                {
                    continue;
                }

                else if( PositionSubscriptionHelper.getleColName().equals(col) )
                {
                   return getCacheKey( AccountOwner.AccountOwnerLevel.LE.name() , WILD_CARD , info.getLegalEntity().getNamespace().getShortName());
                }
            }

            // requested view - USER - Summary
            if( info.getUser() != null ){
                return getCacheKey( AccountOwner.AccountOwnerLevel.USER.name() , WILD_CARD , info.getOrganization().getNamespace().getShortName());
            }

            // requested view ORG - Summary
            return getCacheKey( AccountOwner.AccountOwnerLevel.ORG.name() , WILD_CARD , info.getOrganization().getNamespace().getShortName());
        }else{
            //positionLevel is pre-validated for null and valid Levels
            AccountOwner.AccountOwnerLevel accountOwnerLevel = AccountOwner.AccountOwnerLevel.valueOf(positionLevel);
            switch (accountOwnerLevel ){
                case ORG:
                case USER:
                case LE:
                    return getCacheKey( accountOwnerLevel.name() , WILD_CARD , info.getOrganization().getNamespace().getShortName());
            }
            return getCacheKey( AccountOwner.AccountOwnerLevel.ORG.name() , WILD_CARD , info.getOrganization().getNamespace().getShortName() );
        }
    }
}
