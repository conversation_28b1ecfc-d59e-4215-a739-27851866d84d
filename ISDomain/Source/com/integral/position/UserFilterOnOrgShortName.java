package com.integral.position;

import com.google.common.collect.ImmutableSet;
import com.integral.model.AccountOwner;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;

/**
 * Created by raghunathans on 9/26/16.
 */
public class UserFilterOnOrgShortName extends AbstractFilterOnShortName  {

    public UserFilterOnOrgShortName(ImmutableSet<String> allowedOrganizations_)
    {
        super(allowedOrganizations_);
    }

    @Override
    public PositionFilterType getId()
    {
        return PositionFilterType.User;
    }

    @Override
    public AccountOwner.AccountOwnerLevel getAccountOwnerLevel() {
        return AccountOwner.AccountOwnerLevel.USER;
    }

    @Override
    public String getOrgShortName(Position position) {
        return position.getNamespaceName();
    }
}
