package com.integral.position;

import com.integral.event.SupplyableDataSource;
import com.integral.event.SupplyableReplayingEvent;
import com.integral.model.Position;
import com.integral.model.PositionFilterType;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.
public class SupplyablePositionEvent extends ReplayablePositionEvent implements SupplyableReplayingEvent<String, Position, PositionFilterType>
{

	public SupplyablePositionEvent(String eventDescription,
			PositionCache cachingStrategy_,
			boolean supplyCacheUpdatedValueOnNewUpdate)
	{
		super(eventDescription, new SupplyableDataSource<Position>(), cachingStrategy_, supplyCacheUpdatedValueOnNewUpdate);
		start();
	}

	@Override
	public void supplyData(Position t)
	{
		((SupplyableDataSource<Position>)source).supply(t);
	}
}
