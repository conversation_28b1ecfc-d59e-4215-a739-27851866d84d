package com.integral.position.reconciliation;

import java.util.ArrayList;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.google.common.base.Optional;
import com.integral.dbms.maintenance.purgedb.purge.MongoUtil;
import com.integral.event.CacheOfIdentifiable;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.TradeInfo.TradeEvent;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.persistence.spaces.PersistenceConstants;
import com.integral.persistence.spaces.PersistenceServiceConfiguration;
import com.integral.position.PositionTradeAuditCache;
import com.integral.position.TradeStateSynonyms;
import com.integral.position.calculator.PositionCalculationLevels;
import com.integral.position.persistence.PositionTradeAudit;
import com.integral.position.persistence.PositionTradeAuditPersistenceUtil;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.query.spaces.SpacesQueryService.QueryResult;
import com.integral.query.spaces.SpacesQueryService.QueryResult.Status;
import com.integral.spaces.SpaceIterator;
import com.integral.util.Converters;
import com.integral.util.StringUtil;
import com.mongodb.AggregationOutput;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBObject;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.
public class TradeBasedPositionReconciliationStrategy extends AbstractPositionReconciliationStrategy
{
	private static final Log log = LogFactory.getLog(TradeBasedPositionReconciliationStrategy.class);
	private static final ThreadLocal<StringBuilder> _tlBuilder = new ThreadLocal<StringBuilder>() {
        @Override protected StringBuilder initialValue() {
            return new StringBuilder();
        }
    };
    
    private final StringBuilder getEmptyStringBuilder()
    {
    	StringBuilder sb = _tlBuilder.get();
    	sb.setLength(0);
    	return sb;
    }
	
	@Override
	public void queryAndReconcilePositionForAllOrg(Date toDate, Date fromDate, StringBuilder discrepancies, boolean forced, boolean sendAlert) throws Exception
	{
		QueryResult<List<TradeInfo>> tradesToReconcileResult = queryTradesByModifiedDate(fromDate, toDate, sendAlert);
		if(SpacesQueryService.QueryResult.Status.SUCCESS == tradesToReconcileResult.getStatus())
		{
			StringBuilder discrepanciesLocal = getEmptyStringBuilder();
			Map<String, PositionTradeAuditCache> positionTradeAuditNamespaceMap = new HashMap<String, PositionTradeAuditCache>();
			for(TradeInfo tradeInfo : tradesToReconcileResult.getResult())
			{
				if(forced || ISFactory.getInstance().getPositionServiceConfig().isDisableOldPSGlobally() || ISFactory.getInstance().getPositionServiceConfig().isNewPositionServiceEnabled(tradeInfo.makerNamespace))
				{
					log.debugAsFormat("reconciling for maker org{%s}", tradeInfo.makerNamespace);
					reconcileAndReportAlertForNamespace(discrepancies, forced, sendAlert,
							discrepanciesLocal, positionTradeAuditNamespaceMap,
							tradeInfo, tradeInfo.makerNamespace);
				}
				else
				{
					log.debugAsFormat("Skipping reconciliation for maker org{%s} as per configuation", tradeInfo.makerNamespace);
				}
				
				if(forced || ISFactory.getInstance().getPositionServiceConfig().isDisableOldPSGlobally() || ISFactory.getInstance().getPositionServiceConfig().isNewPositionServiceEnabled(tradeInfo.takerNamespace))
				{
					log.debugAsFormat("reconciling for taker org{%s}", tradeInfo.takerNamespace);
					reconcileAndReportAlertForNamespace(discrepancies, forced, sendAlert,
							discrepanciesLocal, positionTradeAuditNamespaceMap,
							tradeInfo, tradeInfo.takerNamespace);
				}	
				else
				{
					log.debugAsFormat("Skipping reconciliation for taker org{%s} as per configuation", tradeInfo.takerNamespace);
				}
			}
		}
		else
		{
			throw new Exception("Query for trade failed");
		}
	}

	public void reconcileAndReportAlertForNamespace(
			StringBuilder discrepancies,
			boolean forced,
			boolean sendAlert,
			StringBuilder discrepanciesLocal,
			Map<String, PositionTradeAuditCache> positionTradeAuditNamespaceMap,
			TradeInfo tradeInfo, String namespace) throws Exception
	{
		discrepanciesLocal.setLength(0);
		PositionTradeAuditCache auditRecordCacheForNamespace = positionTradeAuditNamespaceMap.get(namespace);
		if(auditRecordCacheForNamespace == null)
		{
			QueryResult<SpaceIterator<PositionTradeAudit>> positionTradeAuditQueryResult = PositionTradeAuditPersistenceUtil.queryPositionTradeAuditByOrganization(namespace, forced);
			if(SpacesQueryService.QueryResult.Status.FAILURE == positionTradeAuditQueryResult.getStatus())
			{
				throw new Exception("Query for PositionTradeAudit failed for organization-"+namespace);
			}
			
			auditRecordCacheForNamespace = new PositionTradeAuditCache();
			auditRecordCacheForNamespace.cache(positionTradeAuditQueryResult.getResult());
			
			if(log.isDebugEnabled())
			{
				log.debugAsFormat("Fetched trade audit for org{%s} size{%s}", namespace, auditRecordCacheForNamespace.size());
			}
			
			positionTradeAuditNamespaceMap.put(namespace, auditRecordCacheForNamespace);
		}
		reconcileWithAuditCache(discrepanciesLocal, namespace, tradeInfo, auditRecordCacheForNamespace);
		
		if(discrepanciesLocal.length()!=0)
		{
			if(discrepancies!=null)
			{
				discrepancies.append(discrepanciesLocal.toString()).append("\n");
			}
			
			if(sendAlert)
				MessageLogger.getInstance().log("POSITION_MISSED_TRADE", PositionReconciliationService.class.getSimpleName(), "Position is not updated for a trade", discrepanciesLocal.toString());
		}
	}
	
	/* 
	 * {@inheritDoc} 
	 * @see com.integral.position.PositionReconciliationStrategy#queryAndReconcilePositionByOrg(java.util.Date, java.util.Date, java.lang.String, java.lang.StringBuilder, boolean, boolean)
	 */
	@Override
	public void queryAndReconcilePositionByOrg(Date toDate, Date fromDate, String takerOrg, StringBuilder discrepancies, boolean forced, boolean sendAlert) throws Exception
	{
		_queryAndReconcilePositionByOrg(toDate, fromDate, takerOrg, discrepancies, forced, sendAlert);
	}

	public static void _queryAndReconcilePositionByOrg(Date toDate, Date fromDate, String takerOrg, StringBuilder discrepancies, boolean forced, boolean sendAlert) throws Exception
	{
		QueryResult<List<TradeInfo>> tradesToReconcileResult = queryTradesByModifiedDate(fromDate, toDate, takerOrg, sendAlert);
		if(SpacesQueryService.QueryResult.Status.SUCCESS == tradesToReconcileResult.getStatus())
		{
			Map<String, PositionTradeAuditCache> positionTradeAuditNamespaceMap = new HashMap<String, PositionTradeAuditCache>();
			for(TradeInfo tradeInfo : tradesToReconcileResult.getResult())
			{
				if(forced || ISFactory.getInstance().getPositionServiceConfig().isDisableOldPSGlobally() || ISFactory.getInstance().getPositionServiceConfig().isNewPositionServiceEnabled(takerOrg))
				{
					PositionTradeAuditCache takerAuditRecordCache = positionTradeAuditNamespaceMap.get(takerOrg);
					if(takerAuditRecordCache == null)
					{
						QueryResult<SpaceIterator<PositionTradeAudit>> positionTradeAuditQueryResult = PositionTradeAuditPersistenceUtil.queryPositionTradeAuditByOrganization(takerOrg, forced);
						if(SpacesQueryService.QueryResult.Status.FAILURE == positionTradeAuditQueryResult.getStatus())
						{
							throw new Exception("Query for PositionTradeAudit failed for organization-"+takerOrg);
						}
						
						takerAuditRecordCache = new PositionTradeAuditCache();
						takerAuditRecordCache.cache(positionTradeAuditQueryResult.getResult());
						
						positionTradeAuditNamespaceMap.put(takerOrg, takerAuditRecordCache);
					}
					if(discrepancies==null)
					{
						discrepancies = new StringBuilder();
					}
					reconcileWithAuditCache(discrepancies, takerOrg, tradeInfo, takerAuditRecordCache);
					if(sendAlert && discrepancies.length()!=0)
					{
						MessageLogger.getInstance().log("POSITION_MISSED_TRADE", PositionReconciliationService.class.getSimpleName(), "Position is not updated for a trade", discrepancies.toString());
					}
				}
			}
		}
		else
		{
			throw new Exception("Query for trade failed");
		}
	}
	
	private static void reconcileWithAuditCache(StringBuilder sb, String orgName, TradeInfo trade, CacheOfIdentifiable<String, PositionTradeAudit> positionTradeAuditCache)
	{
		 Optional<PositionTradeAudit> positionAuditInfo = positionTradeAuditCache.get(StringUtil.fastConcat(trade.id,orgName));
		 if(positionAuditInfo.isPresent())
		 {
			 List<PositionCalculationLevels> notProcessedCalculators = getNotProcessedCalculators(positionAuditInfo.get());
			 
			 // 1. Match if all calculators processed
			 if(!notProcessedCalculators.isEmpty())
			 {
				 // Some calculator not processed this trade
				 // No audit info, so not processed by any calculator
				 sb.append("[").append(trade.id).append(",").append(orgName).append(",CalculatorNotCalled(").append(StringUtil.join(',', Converters.ToString.<PositionCalculationLevels>getEnumToNameStringConverter(), notProcessedCalculators)).append(")],");
			 }
			 
			 // 2. Match if last trade state in trade and audit are same
			 EnumSet<TradeEvent> tradeEventsFromAuditRecords = positionAuditInfo.get().getTradeEvents();
			 Optional<TradeStateSynonyms> tradeState = TradeStateSynonyms.getTradeState(trade.tradeState);
			 if(!tradeState.isPresent())
			 {
				 sb.append("[").append(trade.id).append(",").append(orgName).append(",(Trade state '").append(trade.tradeState).append("' cannot be verified)],");
			 }
			 else if((tradeState.get().isCancelled() &&  !tradeEventsFromAuditRecords.contains(TradeEvent.CANCEL)) // trade state is cancelled and audit doesn't have cancelled recorded
					 || (tradeState.get().isVerified() &&  !tradeEventsFromAuditRecords.contains(TradeEvent.VERIFY))) // trade state is verified and audit doesn't have verified recorded
			 {
				 sb.append("[").append(trade.id).append(",").append(orgName).append(",StateMissed(Trade=").append(trade.tradeState).append(", Recon=").append(StringUtil.join(',', Converters.ToString.<TradeEvent>getEnumToNameStringConverter(), tradeEventsFromAuditRecords)).append(")],");
			 }
			 
			 // Note: Update state is ignored in reconciliation because trade table doesn't have this state. 
			 
			 // 3. Mark trade checked
			 List<PositionTradeAudit> members = positionAuditInfo.get().getMembers();
			 PositionTradeAuditPersistenceUtil.markCheckedAndPersistPositionAuditRecs(orgName, members);
		 }
		 else
		 {
			 // No audit info, so not processed by any calculator
			 sb.append("[").append(trade.id).append(",").append(orgName).append(",(Missed all trade event by all calculators)],");
		 }
	}
	
	 private static QueryResult<List<TradeInfo>> queryTradesByModifiedDate(Date fromDate, Date toDate, boolean sendAlert)
	 {
        QueryResult<List<TradeInfo>> result = new QueryResult<List<TradeInfo>>();
        try 
        {
            String metaspaceName = PersistenceServiceConfiguration.getTradingMetaspaceName();
            String spaceName = PersistenceConstants.TRADE;
            
            DBCollection tradingCollection = MongoUtil.getCollection(metaspaceName,spaceName);
            
            BasicDBObject project = new BasicDBObject().append("$project", new BasicDBObject().append("_id", 1).append("nssh", 1).append("mkrusr", 1).append("mt", 1).append("stt", 1));
            BasicDBObject match = new BasicDBObject().append("$match", new BasicDBObject().append("mt", new BasicDBObject("$gte", fromDate.getTime()).append("$lte", toDate.getTime())));
            BasicDBObject group = new BasicDBObject().append("$group", new BasicDBObject().append("_id", new BasicDBObject("nssh", "$nssh").append("_id", "$_id").append("mkrusr", "$mkrusr").append("stt", "$stt.nm")));
            
            AggregationOutput output = tradingCollection.aggregate(project, match, group);
            
            Iterator<DBObject> resultIter = output.results().iterator();
            
            List<TradeInfo> trades = new ArrayList<TradeBasedPositionReconciliationStrategy.TradeInfo>();
            
            while(resultIter.hasNext())
            {
            	Object tradeInfo = resultIter.next();
            	
				try
				{
					String tradeState = ((BasicDBObject)((BasicDBObject)tradeInfo).get("_id")).getString("stt");
					Optional<TradeStateSynonyms> tradeStateSyn = TradeStateSynonyms.getTradeState(tradeState);
					
					if(!tradeStateSyn.isPresent())
					{
						log.debug( "queryTradesByModifiedDate: ignoring trade record for reconciliation because of unknown trade state - "+ tradeInfo.toString());
						continue;
					}
					
					if(!(tradeStateSyn.get().isCancelled() || tradeStateSyn.get().isVerified())) // ignore traded state other than Cancelled or Verified
					{
						log.debug( "queryTradesByModifiedDate: ignoring trade record for reconciliation because trade state is not Cancelled or Verified - "+ tradeInfo.toString());
						continue;
					}
					
					BasicDBObject mkrusr = (BasicDBObject)((BasicDBObject)((BasicDBObject)tradeInfo).get("_id")).get("mkrusr");
					
					if(mkrusr==null)
					{
						log.warn( "queryTradesByModifiedDate: unable to reconcile because of missing maker information in trade record - "+ tradeInfo.toString());
						continue;
					}
					
					long makerUserRefId = mkrusr.getLong("fxid");
					com.integral.user.User makerUser = (com.integral.user.User)ReferenceDataCacheC.getInstance().getEntityByObjectId(makerUserRefId, com.integral.user.User.class);
					
					if(makerUser==null)
					{
						log.warn( "queryTradesByModifiedDate: unable to reconcile because of missing maker information in ReferenceDataCacheC for trade record - "+ tradeInfo.toString());
						continue;
					}
					
					String makerNamespace = makerUser.getOrganization().getShortName();
					String tradeId = ((BasicDBObject)((BasicDBObject)tradeInfo).get("_id")).getString("_id");
					String takerNamespace = ((BasicDBObject)((BasicDBObject)tradeInfo).get("_id")).getString("nssh");
					trades.add(new TradeInfo(tradeId, makerNamespace, takerNamespace, tradeState));
				} 
				catch (Exception e)
				{
					String message = "queryTradesByModifiedDate: unable to reconcile becuase of missing information {nssh||_id||mkrusr||stt.nm} in trade record - "+ tradeInfo.toString();
					log.error( message , e);
					if(sendAlert)
					{
						MessageLogger.getInstance().log("POSITION_RECONCILIATION_FAILED", PositionReconciliationService.class.getSimpleName(), "unable to parse trade record", tradeInfo.toString());
					}
				}
            }
            
            result.setStatus(Status.SUCCESS);
            result.setResult(trades);
        }
        catch ( Exception ex ) 
        {
        	log.error( "queryTradesByModifiedDate [ fromDate=" + fromDate + " toDate=" + toDate + "]: Exception in query ", ex );
            result.setStatus( QueryResult.Status.FAILURE );
        }
        return result;
	}
	 
	 
	 private static QueryResult<List<TradeInfo>> queryTradesByModifiedDate(Date fromDate, Date toDate, String takerOrg, boolean sendAlert)
	 {
        QueryResult<List<TradeInfo>> result = new QueryResult<List<TradeInfo>>();
        try 
        {
            String metaspaceName = PersistenceServiceConfiguration.getTradingMetaspaceName();
            String spaceName = PersistenceConstants.TRADE;
            
            DBCollection tradingCollection = MongoUtil.getCollection(metaspaceName,spaceName);
            
            BasicDBObject project = new BasicDBObject().append("$project", new BasicDBObject().append("_id", 1).append("nssh", 1).append("mkrusr", 1).append("mt", 1).append("stt", 1));
            BasicDBObject match = new BasicDBObject().append("$match", new BasicDBObject().append("nssh", takerOrg).append("mt", new BasicDBObject("$gte", fromDate.getTime()).append("$lte", toDate.getTime())));
            BasicDBObject group = new BasicDBObject().append("$group", new BasicDBObject().append("_id", new BasicDBObject("nssh", "$nssh").append("_id", "$_id").append("mkrusr", "$mkrusr").append("stt", "$stt.nm")));
            
            AggregationOutput output = tradingCollection.aggregate(project, match, group);
            
            Iterator<DBObject> resultIter = output.results().iterator();
            
            List<TradeInfo> trades = new ArrayList<TradeBasedPositionReconciliationStrategy.TradeInfo>();
            
            while(resultIter.hasNext())
            {
            	Object o = resultIter.next();
            	
				try
				{
					String tradeState = ((BasicDBObject)((BasicDBObject)o).get("_id")).getString("stt");
					Optional<TradeStateSynonyms> tradeStateSyn = TradeStateSynonyms.getTradeState(tradeState);
					if(tradeStateSyn.isPresent() && (tradeStateSyn.get().isCancelled() || tradeStateSyn.get().isVerified()))
					{
						long makerUserRefId = ((BasicDBObject)((BasicDBObject)((BasicDBObject)o).get("_id")).get("mkrusr")).getLong("fxid");
						com.integral.user.User makerUser = (com.integral.user.User)ReferenceDataCacheC.getInstance().getEntityByObjectId(makerUserRefId, com.integral.user.User.class);
						String makerNamespace = makerUser.getOrganization().getShortName();
						String tradeId = ((BasicDBObject)((BasicDBObject)o).get("_id")).getString("_id");
						String takerNamespace = ((BasicDBObject)((BasicDBObject)o).get("_id")).getString("nssh");
						trades.add(new TradeInfo(tradeId, makerNamespace, takerNamespace, tradeState));
					}
					else
					{
						log.debug( "queryTradesByModifiedDate: ignoring irrelavent trade record - "+ o.toString());
					}
				} 
				catch (Exception e)
				{
					log.error( "queryTradesByModifiedDate: unable to parse trade record - "+ o.toString() , e);
					if(sendAlert)
					{
						MessageLogger.getInstance().log("POSITION_RECONCILIATION_FAILED", PositionReconciliationService.class.getSimpleName(), "unable to parse trade record", o.toString());
					}
				}
            }
            
            result.setStatus(Status.SUCCESS);
            result.setResult(trades);
        }
        catch ( Exception ex ) 
        {
        	log.error( "queryTradesByModifiedDate [ fromDate=" + fromDate + " toDate=" + toDate + "]: Exception in query ", ex );
            result.setStatus( QueryResult.Status.FAILURE );
        }
        return result;
	}
	 
	private static class TradeInfo
	{
		final String id;
		final String makerNamespace;
		final String takerNamespace;
		final String tradeState;
		
		public TradeInfo(String id_, String makerNamespace_, String takerNamespace_, String tradeState_)
		{
			id= id_;
			makerNamespace = makerNamespace_;
			takerNamespace = takerNamespace_;
			tradeState = tradeState_; 
		}
	}
	
}
