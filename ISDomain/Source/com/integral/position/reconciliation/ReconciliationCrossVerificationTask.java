package com.integral.position.reconciliation;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

import com.integral.is.common.mbean.ISFactory;
import com.integral.is.log.MessageLogger;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.position.PositionTradeAuditCache;
import com.integral.position.persistence.PositionTradeAudit;
import com.integral.position.persistence.PositionTradeAuditPersistenceUtil;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.query.spaces.SpacesQueryService.QueryResult;
import com.integral.spaces.SpaceIterator;
import com.integral.user.Organization;
import com.integral.util.Converters;
import com.integral.util.StringUtil;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.
final class ReconciliationCrossVerificationTask implements Runnable
{
	/**
	 * 
	 */
	private final PositionReconciliationService reconciliationCrossVerificationTask;
	private final int reconciliationLagInMin;
	private final int durationForTradeToBeVerifiedInMin;

	ReconciliationCrossVerificationTask(PositionReconciliationService positionReconciliationService, int reconciliationLagInMin,int durationForTradeToBeVerifiedInMin)
	{
		reconciliationCrossVerificationTask = positionReconciliationService;
		this.reconciliationLagInMin = reconciliationLagInMin;
		this.durationForTradeToBeVerifiedInMin = durationForTradeToBeVerifiedInMin;
	}

	@Override
	public void run()
	{
		try
		{
			Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT"));
			
			calendar.add(Calendar.MINUTE, reconciliationLagInMin*-1);
			final Date toDate = calendar.getTime();
			
			calendar.add(Calendar.MINUTE, durationForTradeToBeVerifiedInMin*-1);
			final Date fromDate = calendar.getTime();
			
			if(ISFactory.getInstance().getPositionServiceConfig().isDisableOldPSGlobally())
			{
				for (Organization org : ReferenceDataCacheC.getInstance().getOrgs())
				{
					if(org !=null)
					{
						QueryResult<SpaceIterator<PositionTradeAudit>> positionTradeAuditQueryResult = PositionTradeAuditPersistenceUtil.queryNotCheckedPositionTradeAuditByOrganization(org.getShortName(), fromDate, toDate);
						
						if(SpacesQueryService.QueryResult.Status.SUCCESS == positionTradeAuditQueryResult.getStatus())
						{
							PositionTradeAuditCache positionTradeAuditCache = new PositionTradeAuditCache();
							positionTradeAuditCache.cache(positionTradeAuditQueryResult.getResult());
							if(!positionTradeAuditCache.isEmpty())
							{
								MessageLogger.getInstance().log("POSITION_RECONCILIATION_NOT_RUN", PositionReconciliationService.class.getSimpleName(), "Unable to reconcile position for audit data", StringUtil.join(',', Converters.ToString.<PositionTradeAudit>getToStringConverter(), positionTradeAuditCache));
								for (PositionTradeAudit positionTradeAudit : positionTradeAuditCache)
								{
									 List<PositionTradeAudit> members = positionTradeAudit.getMembers();
									 PositionTradeAuditPersistenceUtil.markCheckedAndPersistPositionAuditRecs(positionTradeAudit.getNamespaceName(), members);
								}
							}
						}
						else
						{
							PositionReconciliationService.log.error(String.format("Unable to cross validate that all positions audit record for 'org:%s' are checked:", org.getShortName(), positionTradeAuditQueryResult.getFailureReason()));
						}
					}
				}
			}
			else
			{
				for (String org : ISFactory.getInstance().getPositionServiceConfig().getEnabledNewPositionServiceOrganization())
				{
					if(org !=null)
					{
						QueryResult<SpaceIterator<PositionTradeAudit>> positionTradeAuditQueryResult = PositionTradeAuditPersistenceUtil.queryNotCheckedPositionTradeAuditByOrganization(org, fromDate, toDate);
						if(SpacesQueryService.QueryResult.Status.SUCCESS == positionTradeAuditQueryResult.getStatus())
						{
							PositionTradeAuditCache positionTradeAuditCache = new PositionTradeAuditCache();
							positionTradeAuditCache.cache(positionTradeAuditQueryResult.getResult());
							if(!positionTradeAuditCache.isEmpty())
							{
								MessageLogger.getInstance().log("POSITION_RECONCILIATION_NOT_RUN", PositionReconciliationService.class.getSimpleName(), "Unable to reconcile position for audit data", StringUtil.join(',', Converters.ToString.<PositionTradeAudit>getToStringConverter(), positionTradeAuditCache));
								for (PositionTradeAudit positionTradeAudit : positionTradeAuditCache)
								{
									 List<PositionTradeAudit> members = positionTradeAudit.getMembers();
									 PositionTradeAuditPersistenceUtil.markCheckedAndPersistPositionAuditRecs(positionTradeAudit.getNamespaceName(), members);
								}
							}
						}
						else
						{
							PositionReconciliationService.log.error(String.format("Unable to cross validate that all positions audit record for 'org:%s' are checked:", org, positionTradeAuditQueryResult.getFailureReason()));
						}
					}
				}
			}
		}
		catch(Exception e)
		{
			PositionReconciliationService.log.error("Unable to cross validate that all positions audit record are checked", e);
		}
	}
}