package com.integral.position.reconciliation;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.pool.ThreadPoolFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * <AUTHOR>
 *
 */
public class PositionReconciliationService
{
	protected static PositionReconciliationService Instance = new PositionReconciliationService();
	static final Log log = LogFactory.getLog(PositionReconciliationService.class);
	
	private final AtomicReference<ScheduledFuture<?>> reconciliationTaskResult = new AtomicReference<ScheduledFuture<?>>();
	private final AtomicReference<ScheduledFuture<?>> reconciliationCrossVerificationResult = new AtomicReference<ScheduledFuture<?>>();
	
	private final PositionReconciliationStrategy positionReconciliationStrategy = new TradeBasedPositionReconciliationStrategy();
	
	public static PositionReconciliationService getInstance()
	{
		return Instance;
	}

	public void start()
	{
		final int reconciliationLagInMin = ISFactory.getInstance().getPositionServiceConfig().getReconciliationLagInMin();	
		final int durationForTradeToBeVerifiedInMin = ISFactory.getInstance().getPositionServiceConfig().getDurationForTradeToBeVerifiedInMin();	
		final int maxRetryCount = ISFactory.getInstance().getPositionServiceConfig().getMaxRetryCount();
		final int retryIntervalInMin = ISFactory.getInstance().getPositionServiceConfig().getRetryIntervalInMin();
		if(reconciliationTaskResult.get()==null)
		{
			reconciliationTaskResult.set(getExecutor().scheduleAtFixedRate(new AllOrgReconciliationTask(positionReconciliationStrategy, getExecutor(), reconciliationLagInMin, maxRetryCount,	durationForTradeToBeVerifiedInMin, retryIntervalInMin), reconciliationLagInMin, durationForTradeToBeVerifiedInMin, TimeUnit.MINUTES));
		}
		
		final int reconciliationCrossVerificationLagInMin = 5*durationForTradeToBeVerifiedInMin;
		final int durationForTradeToBeCrossVerifiedInMin = 10*durationForTradeToBeVerifiedInMin;	
		
		if(reconciliationCrossVerificationResult.get()==null)
		{
			reconciliationCrossVerificationResult.set(getExecutor().scheduleAtFixedRate(new ReconciliationCrossVerificationTask(this, reconciliationCrossVerificationLagInMin, durationForTradeToBeCrossVerifiedInMin), reconciliationCrossVerificationLagInMin, durationForTradeToBeCrossVerifiedInMin, TimeUnit.MINUTES));
		}
		
		log.infoAsFormat("Starting PositionReconciliationService: reconciliationLagInMin{%s} durationForTradeToBeVerifiedInMin{%s} maxRetryCount{%s} retryIntervalInMin{%s} reconciliationCrossVerificationLagInMin{%s} durationForTradeToBeCrossVerifiedInMin{%s}", reconciliationLagInMin, durationForTradeToBeVerifiedInMin, maxRetryCount, retryIntervalInMin, reconciliationCrossVerificationLagInMin, durationForTradeToBeCrossVerifiedInMin);
	}
	
	public void stop()
	{
		if(reconciliationTaskResult.get()!=null)
		{
			ScheduledFuture<?> task = reconciliationTaskResult.getAndSet(null);
			if(task!=null)
			{
				task.cancel(false);
			}
		} 
		
		if(reconciliationCrossVerificationResult.get()!=null)
		{
			ScheduledFuture<?> task = reconciliationCrossVerificationResult.getAndSet(null);
			if(task!=null)
			{
				task.cancel(false);
			}
		}
		
		log.info("Stopping PositionReconciliationService");
	}
	
	static ScheduledExecutorService getExecutor()
	{
		return ThreadPoolFactory.getInstance().getPositionReconciliationExecutor();
	}
}
