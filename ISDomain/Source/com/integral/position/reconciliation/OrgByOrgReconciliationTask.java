package com.integral.position.reconciliation;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import com.integral.is.common.mbean.ISFactory;
import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.
final class OrgByOrgReconciliationTask implements Runnable
{
	private final PositionReconciliationStrategy positionReconciliationStrategy;
	private final ScheduledExecutorService executorForRetry;
	private final int reconciliationLagInMin;
	private final int maxRetryCount;
	private final int durationForTradeToBeVerifiedInMin;
	private final int retryIntervalInMin;
	private final Map<String, Integer> reconciliationRetryCounter = new HashMap<String, Integer>();

	private static final Log log = LogFactory
			.getLog(OrgByOrgReconciliationTask.class);

	private OrgByOrgReconciliationTask(
			PositionReconciliationStrategy positionReconciliationStrategy,
			ScheduledExecutorService executorForRetry,
			int reconciliationLagInMin,
			int maxRetryCount, 
			int durationForTradeToBeVerifiedInMin,
			int retryIntervalInMin)
	{
		this.positionReconciliationStrategy = positionReconciliationStrategy;
		this.executorForRetry = executorForRetry;
		this.reconciliationLagInMin = reconciliationLagInMin;
		this.maxRetryCount = maxRetryCount;
		this.durationForTradeToBeVerifiedInMin = durationForTradeToBeVerifiedInMin;
		this.retryIntervalInMin = retryIntervalInMin;
	}
	
	private void reconcilePositionWithRetryForANamespace(final int maxRetryCount,
			final int retryIntervalInMin, final Date toDate, final Date fromDate,
			final String namespaceName)
	{
		String retryKey = namespaceName+fromDate.getTime()+toDate.getTime();
		try
		{
			log.info("Reconciling position for "+namespaceName+" from "+fromDate+" to "+toDate);
			positionReconciliationStrategy.queryAndReconcilePositionByOrg(toDate, fromDate, namespaceName, null, false, true);
			reconciliationRetryCounter.remove(retryKey);
		} catch (Exception e)
		{
			Integer count = reconciliationRetryCounter.get(retryKey);
			if(count==null || count<=maxRetryCount)
			{
				log.error("Reconciliation failed for "+retryKey+ " will retry in some time. Failure reason: "+e.getMessage(), e);
				
				count = count==null?1:(count+1);
				reconciliationRetryCounter.put(retryKey, count);
				
				executorForRetry.schedule(new Runnable()
				{
					@Override
					public void run()
					{
						reconcilePositionWithRetryForANamespace(maxRetryCount, retryIntervalInMin, toDate, fromDate, namespaceName);										
					}
				}, retryIntervalInMin, TimeUnit.MINUTES);
			}
			else
			{
				log.error("Sending alert reconciliation failed "+maxRetryCount+" times. No more retry. Failure reason: "+e.getMessage(), e);
				reconciliationRetryCounter.remove(retryKey);
				MessageLogger.getInstance().log("POSITION_RECONCILIATION_FAILED", PositionReconciliationService.class.getSimpleName(), e.getMessage(), String.format("{Org:%s, from:%s, to:%s",namespaceName, fromDate, toDate));
			}
		}
	}

	@Override
	public void run()
	{
		try
		{
			Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT"));
			
			calendar.add(Calendar.MINUTE, reconciliationLagInMin*-1);
			final Date toDate = calendar.getTime();
			
			calendar.add(Calendar.MINUTE, durationForTradeToBeVerifiedInMin*-1);
			final Date fromDate = calendar.getTime();
			
			if(ISFactory.getInstance().getPositionServiceConfig().isDisableOldPSGlobally())
			{
				for (Organization org : ReferenceDataCacheC.getInstance().getOrgs())
				{
					if(org !=null)
					{
						reconcilePositionWithRetryForANamespace(maxRetryCount, retryIntervalInMin, toDate, fromDate, org.getShortName());
					}
				}
			}
			else
			{
				for (String org : ISFactory.getInstance().getPositionServiceConfig().getEnabledNewPositionServiceOrganization())
				{
					if(org !=null)
					{
						reconcilePositionWithRetryForANamespace(maxRetryCount, retryIntervalInMin, toDate, fromDate, org);
					}
				}
			}
		}
		catch(Exception e)
		{
			MessageLogger.getInstance().log("POSITION_RECONCILIATION_FAILED", PositionReconciliationService.class.getSimpleName(), "Unable to reconcile position because of bad reference data", "");
			log.error("Unable to reconcile position because of bad reference data", e);
		}
	}
}