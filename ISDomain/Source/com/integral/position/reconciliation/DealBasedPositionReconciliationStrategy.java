package com.integral.position.reconciliation;

import java.util.Date;
import java.util.EnumSet;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.google.common.base.Optional;
import com.integral.event.CacheOfIdentifiable;
import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.fx.FXSingleLegDeal;
import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.TradeInfo.TradeEvent;
import com.integral.position.PositionTradeAuditCache;
import com.integral.position.TradeStateSynonyms;
import com.integral.position.calculator.PositionCalculationLevels;
import com.integral.position.persistence.PositionTradeAudit;
import com.integral.position.persistence.PositionTradeAuditPersistenceUtil;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.query.spaces.SpacesQueryService.QueryResult;
import com.integral.query.spaces.fx.esp.query.DealQueryService;
import com.integral.spaces.SpaceIterator;
import com.integral.util.Converters;
import com.integral.util.StringUtil;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.
/**
 * TODO: Incomplete implementation because we are using TradeBasedPositionReconciliationStrategy 
 */
public class DealBasedPositionReconciliationStrategy extends AbstractPositionReconciliationStrategy
{
	private static final Log log = LogFactory.getLog(DealBasedPositionReconciliationStrategy.class);
	
	/* 
	 * {@inheritDoc} 
	 * @see com.integral.position.PositionReconciliationStrategy#queryAndReconcilePositionByOrg(java.util.Date, java.util.Date, java.lang.String, java.lang.StringBuilder, boolean, boolean)
	 */
	@Override
	public void queryAndReconcilePositionByOrg(Date toDate, Date fromDate, String organization, StringBuilder discrepancies, boolean forced, boolean sendAlert) throws Exception
	{
		QueryResult<SpaceIterator<Deal>> dealQueryResult = DealQueryService.queryForDealByModifiedDate(organization, fromDate, toDate, true);	
		if(SpacesQueryService.QueryResult.Status.SUCCESS == dealQueryResult.getStatus())
		{
			Set<String> positionTradeAuditNamespaceSet = new HashSet<String>();
			positionTradeAuditNamespaceSet.add(organization);
			QueryResult<SpaceIterator<PositionTradeAudit>> positionTradeAuditQueryResult = PositionTradeAuditPersistenceUtil.queryPositionTradeAuditByOrganization(organization, forced);
			if(SpacesQueryService.QueryResult.Status.SUCCESS == positionTradeAuditQueryResult.getStatus())
			{
				StringBuilder discrepanciesLocal = new StringBuilder();
				
				PositionTradeAuditCache positionTradeAuditCache = new PositionTradeAuditCache();
				positionTradeAuditCache.cache(positionTradeAuditQueryResult.getResult());
				
				SpaceIterator<Deal> dealIter = dealQueryResult.getResult();
				int dealCount = 0;
				while(dealIter.hasNext())
				{
					++dealCount;
					discrepanciesLocal.setLength(0);//reset builder for each deal
					Deal deal = dealIter.next();
					
					if(deal instanceof FXSingleLegDeal)
			        {
			        	FXSingleLegDeal singleLegDeal = (FXSingleLegDeal) deal;
			        	String tradeId = deal.getTransactionId();
			        	
			        	String makerNamespace = deal.getCounterpartyBOrg().getShortName();
			        	
			        	ensureFetchAuditAndReconcileForOrg(discrepancies,
								forced, sendAlert,
								positionTradeAuditNamespaceSet,
								discrepanciesLocal, positionTradeAuditCache,
								singleLegDeal, tradeId, makerNamespace); // reconcile maker
			        	
			        	String takerNamespace = deal.getCounterpartyAOrg().getShortName();
			        	ensureFetchAuditAndReconcileForOrg(discrepancies,
								forced, sendAlert,
								positionTradeAuditNamespaceSet,
								discrepanciesLocal, positionTradeAuditCache,
								singleLegDeal, tradeId, takerNamespace); // reconcile taker
			        }
					
					if(discrepanciesLocal.length()!=0)
					{
						if(discrepancies!=null)
						{
							discrepancies.append(discrepanciesLocal.toString()).append("\n");
						}
						
						if(sendAlert)
							MessageLogger.getInstance().log("POSITION_MISSED_TRADE", PositionReconciliationService.class.getSimpleName(), "Position is not updated for a trade", discrepanciesLocal.toString());
					}
				}
				log.infoAsFormat("Checked org:%s deal%s auditRec:%s", organization, dealCount, positionTradeAuditCache.size());
			}
			else
			{
				throw new Exception("Query for PositionTradeAudit by organization failed");
			}
		}
		else
		{
			throw new Exception("Query for deal by organization failed");
		}
	}

	private void ensureFetchAuditAndReconcileForOrg(
			StringBuilder discrepancies, boolean forced, boolean sendAlert,
			Set<String> positionTradeAuditNamespaceSet,
			StringBuilder discrepanciesLocal,
			PositionTradeAuditCache positionTradeAuditCache,
			FXSingleLegDeal singleLegDeal, String tradeId, String org)
	{
		if(!positionTradeAuditNamespaceSet.contains(org))
		{
			QueryResult<SpaceIterator<PositionTradeAudit>> result = PositionTradeAuditPersistenceUtil.queryPositionTradeAuditByOrganization(org, forced);
			if(SpacesQueryService.QueryResult.Status.SUCCESS == result.getStatus())
			{
				positionTradeAuditCache.cache(result.getResult());
				positionTradeAuditNamespaceSet.add(org);
				reconcileWithAuditCache(discrepanciesLocal, tradeId, org, singleLegDeal, positionTradeAuditCache); 
			}
			else
			{
				String message = "Unable to query PositionTradeAudit records for org:"+org;
				log.error(message);
				discrepancies.append(message);
				if(sendAlert)
					MessageLogger.getInstance().log("POSITION_RECONCILIATION_FAILED", PositionReconciliationService.class.getSimpleName(), message, org);
			}
		}
		else
		{
			reconcileWithAuditCache(discrepanciesLocal, tradeId, org, singleLegDeal, positionTradeAuditCache); 
		}
	}
	
	private static void reconcileWithAuditCache(StringBuilder sb, String tradeId, String orgName, FXSingleLegDeal singleLegDeal, CacheOfIdentifiable<String, PositionTradeAudit> positionTradeAuditCache)
	{
		 Optional<PositionTradeAudit> makerAuditInfo = positionTradeAuditCache.get(StringUtil.fastConcat(tradeId,orgName));
		 if(makerAuditInfo.isPresent())
		 {
			 List<PositionCalculationLevels> notProcessedCalculators = getNotProcessedCalculators(makerAuditInfo.get());
			 
			 // 1. Match if all calculators processed
			 if(!notProcessedCalculators.isEmpty())
			 {
				 // Some calculator not processed this trade
				 // No audit info, so not processed by any calculator
				 sb.append("[").append(tradeId).append(",").append(orgName).append(",CalculatorNotCalled(").append(StringUtil.join(',', Converters.ToString.<PositionCalculationLevels>getEnumToNameStringConverter(), notProcessedCalculators)).append(")],");
			 }
			 
			 // 2. Match if last trade state in deal and audit are same
			 EnumSet<TradeEvent> tradeEventsFromAuditRecords = makerAuditInfo.get().getTradeEvents();
			 Optional<TradeStateSynonyms> dealState = TradeStateSynonyms.getTradeState(singleLegDeal.getState().getShortName());
			 if(!dealState.isPresent())
			 {
				 sb.append("[").append(tradeId).append(",").append(orgName).append(",(Deal state '").append(singleLegDeal.getState().getShortName()).append("' cannot be verified)],");
			 }
			 else if(!dealState.get().matchAny(tradeEventsFromAuditRecords))
			 {
				 sb.append("[").append(tradeId).append(",").append(orgName).append(",StateMissed(Deal=").append(singleLegDeal.getState().getShortName()).append(", Recon=").append(StringUtil.join(',', Converters.ToString.<TradeEvent>getEnumToNameStringConverter(), tradeEventsFromAuditRecords)).append(")],");
			 }
			 
			 // 3. Mark trade checked
			 List<PositionTradeAudit> members = makerAuditInfo.get().getMembers();
			 PositionTradeAuditPersistenceUtil.markCheckedAndPersistPositionAuditRecs(orgName, members);
		 }
		 else
		 {
			 // No audit info, so not processed by any calculator
			 sb.append("[").append(tradeId).append(",").append(orgName).append(",(Missed all trade event by all calculators)],");
		 }
	}

	@Override
	public void queryAndReconcilePositionForAllOrg(Date toDate, Date fromDate,
			StringBuilder discrepancies, boolean forced, boolean sendAlert)
			throws Exception
	{
		try
		{
			throw new UnsupportedOperationException("Deal reconcile by all org not implemented");
		}
		catch(Exception e)
		{
			log.error( "queryAndReconcilePositionForAllOrg is not supported "+this.getClass().getSimpleName(), e);
		}
	}
}
