package com.integral.position.reconciliation;

import java.util.Date;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.
public interface PositionReconciliationStrategy
{
	public abstract void queryAndReconcilePositionByOrg(Date toDate,
			Date fromDate, String organization, StringBuilder discrepancies,
			boolean forced, boolean sendAlert) throws Exception;

	public abstract void queryAndReconcilePositionForAllOrg(Date toDate, Date fromDate,
			StringBuilder discrepancies, boolean forced, boolean sendAlert)
			throws Exception;

}