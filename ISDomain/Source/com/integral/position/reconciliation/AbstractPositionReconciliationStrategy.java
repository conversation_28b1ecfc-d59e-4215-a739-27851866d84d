package com.integral.position.reconciliation;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.integral.position.PositionServiceConfigC;
import com.integral.position.calculator.PositionCalculationLevels;
import com.integral.position.persistence.PositionTradeAudit;
import com.integral.util.BitMaskEnum;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.
public abstract class AbstractPositionReconciliationStrategy implements PositionReconciliationStrategy
{
	/**
	 * This is the value of mask when all calculator bits are set.
	 */
	private static final long allPositionCalculatorSetMask;
	static
	{
		BitMaskEnum<PositionCalculationLevels> calculatorsProcessedMask = new BitMaskEnum<PositionCalculationLevels>();
		calculatorsProcessedMask.setAllFrom(PositionServiceConfigC.Instance.getSupportedPositionCalculationLevels());
		allPositionCalculatorSetMask = calculatorsProcessedMask.getMask();
	}
	
	static List<PositionCalculationLevels> getNotProcessedCalculators(PositionTradeAudit tradeAudit)
	{
		if(allPositionCalculatorSetMask == tradeAudit.getPositionCalculatorsMask())
		{
			return Collections.emptyList();
		}
				
		List<PositionCalculationLevels> levels = new ArrayList<PositionCalculationLevels>(PositionCalculationLevels.values().length);
		BitMaskEnum<PositionCalculationLevels> calculatorsProcessedMask = new BitMaskEnum<PositionCalculationLevels>(tradeAudit.getPositionCalculatorsMask());
		for(PositionCalculationLevels e :  PositionServiceConfigC.Instance.getSupportedPositionCalculationLevels())
		{
			if(!calculatorsProcessedMask.isSet(e))
			{
				levels.add(e);
			}
		}
		return levels;
	}
}
