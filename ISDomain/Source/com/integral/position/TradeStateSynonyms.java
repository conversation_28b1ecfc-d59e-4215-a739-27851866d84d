package com.integral.position;

import java.util.EnumSet;
import java.util.Set;

import com.google.common.base.Optional;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import com.integral.model.TradeInfo.TradeEvent;
import com.integral.model.dealing.State;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.
public enum TradeStateSynonyms
{
	TradeStateVerified(Sets.newHashSet("Verified","Verify",State.Name.TSCONFIRMED.name(),State.Name.TSPREVERIFIED.name(),State.Name.TSVERIFIED.name())),
	TradeStateAmend("Amend,Update,Amended,Updated"),
	TradeStateCancelled(Sets.newHashSet("Cancel","Cancelled",State.Name.TSCANCELLED.name(),State.Name.TSREJECTED.name())),
	TradeStateEOD("EOD,EndOfDay");
	
	private final Set<String> _synonyms;
	
	private TradeStateSynonyms(String synonyms_)
	{
		_synonyms = Sets.<String>newHashSet(Splitter.on(",").split(synonyms_));
	}
	
	private TradeStateSynonyms(Set<String> synonyms_)
	{
		_synonyms = synonyms_;
	}
	
	public boolean isVerified()
	{
		return this == TradeStateVerified;
	}
	
	public boolean isAmend()
	{
		return this == TradeStateAmend;
	}
	
	public boolean isCancelled()
	{
		return this == TradeStateCancelled;
	}
	
	public boolean isEOD()
	{
		return this == TradeStateEOD;
	}
	
	public boolean is(TradeEvent tradeEvent)
	{
		switch(tradeEvent)
		{
			case AMEND: return isAmend();
			case CANCEL: return isCancelled();
			case VERIFY: return isVerified();
			case EOD: return isEOD();
		}
		
		return false;		
	}
	
	public boolean matchAny(EnumSet<TradeEvent> tradeEvents)
	{
		switch(this)
		{
			case TradeStateVerified: return tradeEvents.contains(TradeEvent.VERIFY); 
			case TradeStateAmend: return tradeEvents.contains(TradeEvent.AMEND); 
			case TradeStateCancelled: return tradeEvents.contains(TradeEvent.CANCEL); 
			case TradeStateEOD: return tradeEvents.contains(TradeEvent.EOD); 
		}
		
		return false;		
	}
	
	public static Optional<TradeStateSynonyms> getTradeState(String value)
	{
		if(value == null)
		{
			return Optional.absent();
		}
		value = value.trim();
		if(value.isEmpty())
		{
			return Optional.absent();
		}
		
		Optional<TradeStateSynonyms> toReturn = Optional.absent();
		
		toReturn = matchWithNotNullString(TradeStateVerified, value);
		if(toReturn.isPresent())
		{
			return toReturn;
		}
		
		toReturn = matchWithNotNullString(TradeStateAmend, value);
		if(toReturn.isPresent())
		{
			return toReturn;
		}
		
		toReturn = matchWithNotNullString(TradeStateCancelled, value);
		if(toReturn.isPresent())
		{
			return toReturn;
		}
		
		return toReturn;
	}
	
	
	private static Optional<TradeStateSynonyms> matchWithNotNullString(TradeStateSynonyms e, String value)
	{
		for(String s: e._synonyms)
		{
			if(s.equalsIgnoreCase(value))
			{
				return Optional.of(e);
			}
		}
		return Optional.absent();
	}
}
