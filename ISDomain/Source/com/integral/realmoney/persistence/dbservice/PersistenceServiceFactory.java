/**
 * <AUTHOR>
 */
package com.integral.realmoney.persistence.dbservice;

/**
 * PersistenceSerivice is abstracted out to support different storages.
 * <AUTHOR>
 *
 */
public class PersistenceServiceFactory
{
	private static PersistenceServiceFactory _instance = new PersistenceServiceFactory();
	
	private PersistenceServiceFactory(){}
	
	private RMPersistenceService rmPersistenceService;
	
	public static PersistenceServiceFactory getPersistenceServiceFactory()
	{
		return _instance;
	}
	
	public void setPersistenceService(RMPersistenceService rmPersistenceService)
	{
		this.rmPersistenceService = rmPersistenceService;  
	}
	
	public RMPersistenceService getRMPersistenceService()
	{
		return this.rmPersistenceService;
	}

}
