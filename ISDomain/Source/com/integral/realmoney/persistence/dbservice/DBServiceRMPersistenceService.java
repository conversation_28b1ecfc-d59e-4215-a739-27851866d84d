/**
 * <AUTHOR>
 */
package com.integral.realmoney.persistence.dbservice;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Vector;

import com.integral.netting.model.MarketSpotPrice;
import com.integral.netting.model.NettingTradeRequest;
import org.eclipse.persistence.exceptions.DatabaseException;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;

import com.integral.dbservice.DBService;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestC;
import com.integral.is.ISCommonConstants;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.message.ErrorMessageC;
import com.integral.netting.NettingPortfolioState;
import com.integral.netting.model.NettingPortfolio;
import com.integral.netting.model.NettingPortfolioC;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.Namespace;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.SecondaryPersistenceFactory;
import com.integral.user.User;

/**
 * <AUTHOR>
 *
 */
public class DBServiceRMPersistenceService implements RMPersistenceService
{

	private static Log log = LogFactory.getLog( DBServiceRMPersistenceService.class );

	private static DBServiceRMPersistenceService instance = null;

	private DBService dbService = null;

    protected Log getLogger(){
        return log;
    }

	/**
	 * Even though this is a public method, this should be only set by the startup task once for the lifetime of the system.
	 *
	 * @param globalInstance
	 */
	public static void __private_startup_setInstance( DBServiceRMPersistenceService globalInstance )
	{
		instance = globalInstance;
	}

	public DBServiceRMPersistenceService( DBService dbService )
	{
		this.dbService = dbService;
	}

	public void setup()
	{

	}

	public static RMPersistenceService instance()
	{
		return instance;
	}

	/**
	 * Submits Create Netting Portfolio triggered event to DBService.
	 *
	 * @param nettingPortfolio
	 */
	public ErrorMessage createNettingPortfolio( NettingPortfolio nettingPortfolio )
	{
		try
		{
			String portfolioID = getCorelationId( nettingPortfolio );
			dbService.processEvent( portfolioID, PersistenceHandlers.StorePortfolioEventHandler.EVENT, (NettingPortfolioC)nettingPortfolio, null, false );
		}
		catch ( Exception e )
		{
			log.error( "RMPersistenceService.createNettingPortfolio : Error in storing NettingPortfolio " + nettingPortfolio.getPortfolioID(), e );
			new ErrorMessageC().addError( e );
		}
		return null;
	}

	/* (non-Javadoc)
	* @see com.integral.realmoney.persistence.dbservice.RMPersistenceService#createNettingMKTSpotPrice(com.integral.netting.model.MarketSpotPriceC)
	*/
	@Override
	public ErrorMessage createNettingMKTSpotPrice( MarketSpotPrice mktSpotPrice )
	{
		return null;
	}

	/* (non-Javadoc)
	* @see com.integral.realmoney.persistence.dbservice.RMPersistenceService#createNettingTradeRequest(com.integral.netting.model.NettingTradeRequest)
	*/
	public ErrorMessage createNettingTradeRequest( NettingTradeRequest nettingTradeRequest )
	{
		return  null;
	}
	
	public ErrorMessage updateNettingTradeRequest( NettingTradeRequest nettingTradeRequest )
	{
		return  null;
	}


	/* (non-Javadoc)
	 * @see com.integral.realmoney.persistence.dbservice.RMPersistenceService#updateNettingPortfolioState(com.integral.netting.model.NettingPortfolio)
	 */
	@Override
	public ErrorMessage updateNettingPortfolioState( NettingPortfolio nettingPortfolio )
	{
		try
		{
			String portfolioID = getCorelationId( nettingPortfolio );
			dbService.processEvent( portfolioID, PersistenceHandlers.PortfolioStateUpdateEventHandler.EVENT, (NettingPortfolioC)nettingPortfolio, null, false );
		}
		catch ( Exception e )
		{
			log.error( "RMPersistenceService.updateNettingPortfolioState : Error in updating NettingPortfolio " + nettingPortfolio.getPortfolioID(), e );
			new ErrorMessageC().addError( e );
		}
		return null;
	}
	
	
	@Override
	public ErrorMessage updateNettingPortfolioState( NettingPortfolio nettingPortfolio, String event, Map<String,String> properties)
	{
		try
		{
			String portfolioID = getCorelationId( nettingPortfolio );
			dbService.processEvent( portfolioID, event, (NettingPortfolioC)nettingPortfolio, properties, false );
		}
		catch ( Exception e )
		{
			log.error( "RMPersistenceService.updateNettingPortfolioState UTI: Error in updating NettingPortfolio " + nettingPortfolio.getPortfolioID(), e );
			new ErrorMessageC().addError( e );
		}
		return null;
	}

	public String getCorelationId( NettingPortfolio nettingPortfolio )
	{
		return nettingPortfolio.getPortfolioID();
	}

	protected ErrorMessage getErrorMessage( Exception e )
	{
		ErrorMessage errMessage = new ErrorMessageC();
		errMessage.addError( e );
		return errMessage;
	}

	//shutdown and cleanup for persistenceservice

	public void shutdown()
	{
		dbService.stop();
		dbService = null;
	}

	/* (non-Javadoc)
	 * @see com.integral.realmoney.persistence.dbservice.RMPersistenceService#getLastActivePortfolio(com.integral.user.User)
	 */
	@Override
	public NettingPortfolio getLastActivePortfolio( User user )
	{
		try
		{
			Session session = SecondaryPersistenceFactory.newSecondarySession();
			ExpressionBuilder eb = new ExpressionBuilder();
			Expression nsExp = eb.get( "namespace" ).equal( user.getNamespace() );
            Expression userExp = eb.get( "user" ).equal( user );
			Expression txnIdIdExp = eb.get( "state" ).notEqual( NettingPortfolioState.Allocated.getId() );
			Vector portfolios = ( Vector ) session.readAllObjects( NettingPortfolioC.class, txnIdIdExp.and( nsExp ).and( userExp ) );
			NettingPortfolioC selectedPortfolio = null;
			for ( Object object : portfolios )
			{
				NettingPortfolioC active = (NettingPortfolioC)object;
				if( selectedPortfolio == null )
				{
					selectedPortfolio = active;
				}
				else if ( active.getObjectId() > selectedPortfolio.getObjectId() )
				{
					selectedPortfolio = active;
				}
			}
			return selectedPortfolio;
		}
		catch ( DatabaseException e )
		{
			e.printStackTrace();
		}
		catch ( PersistenceException e )
		{
			e.printStackTrace();
		}
		return null;
	}
	
	
	/**
	 * Returns active portfolios for a given org.
	 * Used for showing all portfolios of an org to a chief dealer.
	 * @param user
	 * @return
	 */
	public List<NettingPortfolio> getActivePortfoliosByOrg (User user, Set<Integer> pfStatesToGet)
	{
		List<NettingPortfolio> pfs = null;
		
		try
		{
			Session session = SecondaryPersistenceFactory.newSecondarySession();
			ExpressionBuilder eb = new ExpressionBuilder();
			
			Expression nsExp = eb.get( "namespace" ).equal( user.getNamespace() );
			Expression typeExp = eb.get( "state" ).in(pfStatesToGet); 
					
			Vector <NettingPortfolio> portfolios = session.readAllObjects( NettingPortfolioC.class, nsExp.and(typeExp) );
			
			return portfolios;
		}
		catch ( DatabaseException e )
		{
			e.printStackTrace();
		}
		catch ( PersistenceException e )
		{
			e.printStackTrace();
		}
		return null;
	}
	
	
	/**
	 * Returns most recently uploaded portfolio for a given name.
	 * @param name
	 * @param user
	 * @return
	 */
	public NettingPortfolio getPortfolioByName(String name, User user, boolean isUserSpecific)
	{
		try
		{
			Session session = SecondaryPersistenceFactory.newSecondarySession();
			ExpressionBuilder eb = new ExpressionBuilder();
			Expression nsExp = eb.get( "namespace" ).equal( user.getNamespace() );
			Expression txnIdIdExp = eb.get( "name" ).equal( name );
			Vector portfolios = ( Vector ) session.readAllObjects( NettingPortfolioC.class, txnIdIdExp.and( nsExp ) );
			NettingPortfolioC selectedPortfolio = null;
			for ( Object object : portfolios )
			{
				NettingPortfolioC active = (NettingPortfolioC)object;
				if( selectedPortfolio == null )
				{
					selectedPortfolio = active;
				}
				else if ( active.getObjectId() > selectedPortfolio.getObjectId() )
				{
					selectedPortfolio = active;
				}
			}
			return selectedPortfolio;
		}
		catch ( DatabaseException e )
		{
			e.printStackTrace();
		}
		catch ( PersistenceException e )
		{
			e.printStackTrace();
		}
		return null;
	}

    public NettingPortfolio getPortfolioById(String Id, User user)
	{
		try
		{
			Session session = SecondaryPersistenceFactory.newSecondarySession();
			ExpressionBuilder eb = new ExpressionBuilder();
			Expression nsExp = eb.get( "namespace" ).equal( user.getNamespace() );
			Expression txnIdIdExp = eb.get( "portfolioID" ).equal( Id );
			Vector portfolios = ( Vector ) session.readAllObjects( NettingPortfolioC.class, txnIdIdExp.and( nsExp ) );
			NettingPortfolioC selectedPortfolio = null;
			for ( Object object : portfolios )
			{
				NettingPortfolioC active = (NettingPortfolioC)object;
				if( selectedPortfolio == null )
				{
					selectedPortfolio = active;
				}
				else if ( active.getObjectId() > selectedPortfolio.getObjectId() )
				{
					selectedPortfolio = active;
				}
			}
			return selectedPortfolio;
		}
		catch ( DatabaseException e )
		{
			e.printStackTrace();
		}
		catch ( PersistenceException e )
		{
			e.printStackTrace();
		}
		return null;
	}


	@Override
	public ErrorMessage completeNettingPortfolioState( NettingPortfolio nettingPortfolio) 
	{
		try
		{
			String portfolioID = getCorelationId( nettingPortfolio );
			dbService.processEvent( portfolioID, PersistenceHandlers.PortfolioCompleteEventHandler.EVENT, (NettingPortfolioC)nettingPortfolio, null, false );
		}
		catch ( Exception e )
		{
			log.error( "RMPersistenceService.completeNettingPortfolioState : Error in updating NettingPortfolio " + nettingPortfolio.getPortfolioID(), e );
			new ErrorMessageC().addError( e );
		}
		return null;
	}

	/* (non-Javadoc)
	 * @see com.integral.realmoney.persistence.dbservice.RMPersistenceService#getPortfolioByExternalId(java.lang.String, com.integral.user.User)
	 */
	@Override
	public NettingPortfolio getPortfolioByExternalId( String Id, Namespace namespace )
	{
		try
		{
			Session session = SecondaryPersistenceFactory.newSecondarySession();
			ExpressionBuilder eb = new ExpressionBuilder();
			Expression nsExp = eb.get( "namespace" ).equal( namespace );
			Expression extIdExp = eb.get( "externalRequestId" ).equal( Id );
			Vector portfolios = ( Vector ) session.readAllObjects( NettingPortfolioC.class, extIdExp.and( nsExp ) );
			NettingPortfolioC selectedPortfolio = null;
			for ( Object object : portfolios )
			{
				NettingPortfolioC active = (NettingPortfolioC)object;
				if( selectedPortfolio == null )
				{
					selectedPortfolio = active;
				}
				else if ( active.getObjectId() > selectedPortfolio.getObjectId() )
				{
					selectedPortfolio = active;
				}
			}
			return selectedPortfolio;
		}
		catch ( DatabaseException e )
		{
			e.printStackTrace();
		}
		catch ( PersistenceException e )
		{
			e.printStackTrace();
		}
		return null;
	}


}
