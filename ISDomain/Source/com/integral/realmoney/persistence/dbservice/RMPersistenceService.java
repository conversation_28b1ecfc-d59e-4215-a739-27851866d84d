package com.integral.realmoney.persistence.dbservice;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.integral.finance.dealing.Request;
import com.integral.message.ErrorMessage;
import com.integral.netting.model.MarketSpotPrice;
import com.integral.netting.model.NettingPortfolio;
import com.integral.netting.model.NettingTradeRequest;
import com.integral.persistence.Namespace;
import com.integral.user.User;

/**
 * Persistence Service for Real Money specific db transactions
 */

public interface RMPersistenceService
{

    /**
     * Triggers Submit Netting Portfolio event and sends it to DBService.
     *
     * @param nettingPortfolio
     */
    public ErrorMessage createNettingPortfolio( NettingPortfolio nettingPortfolio );

    public ErrorMessage createNettingMKTSpotPrice( MarketSpotPrice mktSpotPrice );

    public ErrorMessage createNettingTradeRequest( NettingTradeRequest nettingTradeRequest );
    
    public ErrorMessage updateNettingTradeRequest( NettingTradeRequest nettingTradeRequest);

    public ErrorMessage updateNettingPortfolioState( NettingPortfolio nettingPortfolio );
    
	public ErrorMessage updateNettingPortfolioState( NettingPortfolio nettingPortfolio, String event, Map<String,String> properties);
    
    public ErrorMessage completeNettingPortfolioState( NettingPortfolio nettingPortfolio );
    
    /**
     * Returns most recent un-finished/incomplete portfolio for given user.  
     * @param user
     * @return
     */
    public NettingPortfolio getLastActivePortfolio(User user);
    
    public List<NettingPortfolio> getActivePortfoliosByOrg (User user, Set<Integer> pfStatesToGet);
    
	public NettingPortfolio getPortfolioByName(String name, User user, boolean isUserSpecific);

    public NettingPortfolio getPortfolioById(String Id, User user);
    
    public NettingPortfolio getPortfolioByExternalId(String Id, Namespace namespace);

}
