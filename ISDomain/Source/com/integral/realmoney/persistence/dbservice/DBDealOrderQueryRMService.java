package com.integral.realmoney.persistence.dbservice;

import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestC;
import com.integral.is.ISCommonConstants;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.SecondaryPersistenceFactory;
import com.integral.user.User;
import org.eclipse.persistence.exceptions.DatabaseException;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;

import java.util.List;
import java.util.Vector;

/**
 * Created by bhaskars on 3/23/14.
 */
public class DBDealOrderQueryRMService {

    private static DBDealOrderQueryRMService instance = new DBDealOrderQueryRMService();

    public static DBDealOrderQueryRMService getInstance()
    {
        return instance;
    }

    protected Log log = LogFactory.getLog(this.getClass());

    /* (non-Javadoc)
      * @see com.integral.realmoney.persistence.dbservice.RMPersistenceService#getGTMOrders(java.lang.String[])
      */
    public List<Request> getGTMOrders( User user, String clorderIDStr, boolean spotsOnly )
    {
        log.info("DBDealOrderQueryRMService.getGTMOrders : START  - user - " + user.getFullName() + " ClOrderIds - " + clorderIDStr);
        try
        {
            Session session = SecondaryPersistenceFactory.newSecondarySession();
            ExpressionBuilder eb = new ExpressionBuilder();
            //Expression nsExp = eb.get( "namespace" ).equal( user.getNamespace() );
            String[] clorderIDs = clorderIDStr.split( "," );
            Expression extOrdIdsExp = eb.get( "externalRequestId" ).in( clorderIDs );
            Expression noQuotedClsf = eb.get( "requestClassification" ).get( NamedEntity.ShortName ).notEqual( ISCommonConstants.QTQ_ACCEPT_TYPE );
            Expression noRfsClsf = eb.get( "requestClassification" ).get( NamedEntity.ShortName ).notEqual( ISCommonConstants.RFQ_CREATE_TYPE );
            Expression userExpr = eb.get("user").equal(user);

            Expression finalExpr = extOrdIdsExp.and( noQuotedClsf ).and(userExpr);
            if (spotsOnly)
                finalExpr = finalExpr.and( noRfsClsf );

            return  (Vector) session.readAllObjects( RequestC.class, finalExpr );
        }
        catch ( DatabaseException e )
        {
            e.printStackTrace();
        }
        catch ( PersistenceException e )
        {
            e.printStackTrace();
        }
        finally {
            log.info("DBDealOrderQueryRMService.getGTMOrders : END  - user - " + user.getFullName());
        }
        return null;
    }

    public List<Request> getFSRRequests( User user,String requestIDS )
    {
        log.info("DBDealOrderQueryRMService.getFSRRequests : START  - user - " + user.getFullName() + " RequestIds - " + requestIDS);
        try
        {
            Session session = SecondaryPersistenceFactory.newSecondarySession();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression nsExp = eb.get( "namespace" ).equal( user.getNamespace() );
            String[] clorderIDs = requestIDS.split( "," );
            Expression extOrdIdsExp = eb.get( "externalRequestId" ).in( clorderIDs );
            Expression quotedClsfExpr = eb.get( "requestClassification" ).get( NamedEntity.ShortName ).notEqual( ISCommonConstants.QTQ_ACCEPT_TYPE );
            Expression finalExpr = nsExp.and(extOrdIdsExp.and(quotedClsfExpr));

            return  ( Vector ) session.readAllObjects( RequestC.class, finalExpr );
        }
        catch ( DatabaseException e )
        {
            e.printStackTrace();
        }
        catch ( PersistenceException e )
        {
            e.printStackTrace();
        }
        finally {
            log.info("DBDealOrderQueryRMService.getFSRRequests : END  - user - " + user.getFullName());
        }
        return null;
    }
}
