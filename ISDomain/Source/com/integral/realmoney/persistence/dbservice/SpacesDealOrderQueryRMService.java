package com.integral.realmoney.persistence.dbservice;

import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.fx.FXSingleLegDealC;
import com.integral.finance.dealing.fx.FXSingleLegOrderC;
import com.integral.finance.dealing.fx.FXSwapDealC;
import com.integral.is.spaces.fx.persistence.*;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.spaces.PersistenceConstants;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.query.spaces.fx.esp.query.DealQueryService;
import com.integral.spaces.Metaspace;
import com.integral.spaces.Metaspaces;
import com.integral.spaces.QueryBuilder;
import com.integral.spaces.SpaceIterator;
import com.integral.spaces.spi.CriteriaSet;
import com.integral.user.User;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bhaskars on 3/23/14.
 */
public class SpacesDealOrderQueryRMService {

    private static SpacesDealOrderQueryRMService instance = new SpacesDealOrderQueryRMService();

    public static SpacesDealOrderQueryRMService getInstance()
    {
        return instance;
    }

    protected Log log = LogFactory.getLog(this.getClass());

    /**
     * Returns all Spot Orders for the Client Order IDs using CDQ
     *
     * @param User
     * @param clorderIDStr
     */
    public List<FXSingleLegOrderC> getGTMSpotOrders(User user,  String clorderIDStr) {
        String namespaceName = user.getNamespace().getShortName();
        String[] clorderIDs = clorderIDStr.split( "," );
        SpacesQueryService.QueryResult<SpaceIterator> queryResult = new SpacesQueryService.QueryResult<SpaceIterator>();
        queryResult.setStatus( SpacesQueryService.QueryResult.Status.FAILURE );
        String metaspaceName = com.integral.is.spaces.fx.persistence.PersistenceServiceFactory.getCDQMetaspaceName();
        String spaceName = PersistenceConstants.ORDER;
        try {
            Metaspace ms = Metaspaces.getInstance().getMetaspace( namespaceName,metaspaceName  );
            if ( ms != null ) {
                QueryBuilder queryBuilder = ms.createNewQueryBuilder( namespaceName, spaceName );
                CriteriaSet cs = ms.defaultCriteriaSet();
                queryBuilder = queryBuilder.add(cs.is("nssh", namespaceName) );
                queryBuilder = queryBuilder.add( cs.in("cr", (Object[])clorderIDs) );
                SpaceIterator<FXSingleLegOrderC> itr = queryBuilder.build().getIterator( FXSingleLegOrderC.class );

                queryResult.setStatus( SpacesQueryService.QueryResult.Status.SUCCESS );

                List<FXSingleLegOrderC> orders = new ArrayList<FXSingleLegOrderC>();
                while (itr.hasNext()) {
                    orders.add((FXSingleLegOrderC)itr.next());
                }
                return orders;
            }
        }
        catch ( Exception ex ) {
            log.error("getGTMSpotOrders [namespaceName=" + namespaceName + " clientReferenceId=" + clorderIDStr + "]: Exception in query ", ex);
            queryResult.setStatus( SpacesQueryService.QueryResult.Status.FAILURE );
            queryResult.setResult( null );
        }
        return null;
    }

/**
 * Returns all True-up Orders for the Client Order IDs using CDQ
 *
 * @param User
 * @param clorderIDStr
 **/
    public List<FXSingleLegDealC> getGTMTrueUpOrders(User user, String clorderIDStr) {
        String namespaceName = user.getNamespace().getShortName();
        String[] clorderIDs = clorderIDStr.split( "," );
        SpacesQueryService.QueryResult<SpaceIterator> queryResult = new SpacesQueryService.QueryResult<SpaceIterator>();
        queryResult.setStatus( SpacesQueryService.QueryResult.Status.FAILURE );
        String metaspaceName = com.integral.is.spaces.fx.persistence.PersistenceServiceFactory.getCDQMetaspaceName();
        String spaceName = PersistenceConstants.DEAL;

        try {
            Metaspace ms = Metaspaces.getInstance().getMetaspace( namespaceName,metaspaceName  );
            if ( ms != null ) {
                QueryBuilder queryBuilder = ms.createNewQueryBuilder( namespaceName, spaceName );
                CriteriaSet cs = ms.defaultCriteriaSet();
                queryBuilder = queryBuilder.add(cs.is("nssh", namespaceName) );
                queryBuilder = queryBuilder.add( cs.in("erid", (Object[])clorderIDs) );
                queryBuilder = queryBuilder.add( cs.in("swp", false) );
                SpaceIterator<FXSingleLegDealC> itr = queryBuilder.build().getIterator( FXSingleLegDealC.class );

                queryResult.setStatus( SpacesQueryService.QueryResult.Status.SUCCESS );

                List<FXSingleLegDealC> deals = new ArrayList<FXSingleLegDealC>();
                while (itr.hasNext()) {
                    deals.add((FXSingleLegDealC)itr.next());
                }
                return deals;
            }
        }
        catch ( Exception ex ) {
            log.error("getGTMTrueUpOrders [namespaceName=" + namespaceName + " clientReferenceId=" + clorderIDStr + "]: Exception in query ", ex);
            queryResult.setStatus( SpacesQueryService.QueryResult.Status.FAILURE );
            queryResult.setResult( null );
        }
        return null;
    }

    /**
     * Returns deal (Single Leg or Swap) using CDQ
     *
     * @param externalId
     * @param User
     **/
    public Deal getDealByExternalID(String externalId,User user )
    {
        SpacesQueryService.QueryResult<Deal> dealQueryResult=null;
        Deal deal = null;
        //TODO -- check for the latest order to ensure we have the latest
        dealQueryResult=
                queryForDealsByClientReferenceId(user, externalId, true);
        deal = dealQueryResult.getResult();

        // check for if the deal is null; then query for swap deal;
        if (deal == null)
        {
            dealQueryResult=
                    queryForDealsByClientReferenceId( user, externalId,false );
            deal = dealQueryResult.getResult();
        }

        return deal;
    }


    private SpacesQueryService.QueryResult<Deal> queryForDealsByClientReferenceId(User user,String clientReferenceId,boolean isSingleLeg){
        SpacesQueryService.QueryResult<Deal> queryResult = new SpacesQueryService.QueryResult<Deal>();
        queryResult.setStatus( SpacesQueryService.QueryResult.Status.FAILURE );
        String metaspaceName = com.integral.is.spaces.fx.persistence.PersistenceServiceFactory.getCDQMetaspaceName();
        String spaceName = PersistenceConstants.DEAL;
        String namespaceName = user.getOrganization().getShortName();
        Deal deal = null;
        try {
            Metaspace ms = Metaspaces.getInstance().getMetaspace( namespaceName,metaspaceName  );
            if ( ms != null ) {
                QueryBuilder queryBuilder = ms.createNewQueryBuilder( namespaceName, spaceName );
                CriteriaSet cs=ms.defaultCriteriaSet();
                queryBuilder = queryBuilder.add(cs.is( "swp",!isSingleLeg));
                queryBuilder = queryBuilder.add(cs.is( "nssh",namespaceName) );
                queryBuilder = queryBuilder.add(cs.is( "dr.erI",clientReferenceId) );
                if(isSingleLeg)
                    deal = ( Deal ) queryBuilder.build().getSingleResult( FXSingleLegDealC.class );
                else
                    deal = ( Deal ) queryBuilder.build().getSingleResult( FXSwapDealC.class );
                if ( deal != null && deal.get_id()!= null && deal.getNamespaceName() != null) {
                    queryResult.setResult( deal );
                }
                queryResult.setStatus( SpacesQueryService.QueryResult.Status.SUCCESS );
            }
        }
        catch ( Exception ex ) {
            log.error( "queryForDealsByClientReferenceId [user=" + user.getShortName() + " clientReferenceId=" + clientReferenceId + " isSingleLeg=" + isSingleLeg + " ] :Exception in query ", ex );
            queryResult.setStatus( SpacesQueryService.QueryResult.Status.FAILURE );
            queryResult.setResult( null );
        }
        return queryResult;
    }


}
