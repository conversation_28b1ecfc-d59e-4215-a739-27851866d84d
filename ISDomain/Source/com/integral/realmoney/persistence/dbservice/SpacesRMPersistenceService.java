/**
 * <AUTHOR>
 */
package com.integral.realmoney.persistence.dbservice;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.spaces.fx.persistence.ISSpacesPersistenceService;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.message.ErrorMessageC;
import com.integral.netting.NettingPortfolioState;
import com.integral.netting.model.MarketSpotPrice;
import com.integral.netting.model.NettingPortfolio;
import com.integral.netting.model.NettingPortfolioC;
import com.integral.netting.model.NettingPortfolioDealingModelC;
import com.integral.netting.model.NettingTradeRequest;
import com.integral.persistence.Namespace;
import com.integral.persistence.spaces.PersistenceConstants;
import com.integral.persistence.spaces.PersistenceServiceConfiguration;
import com.integral.spaces.Metaspace;
import com.integral.spaces.Metaspaces;
import com.integral.spaces.QueryBuilder;
import com.integral.spaces.SpaceIterator;
import com.integral.spaces.spi.CriteriaSet;
import com.integral.user.User;

/**
 * <AUTHOR>
 *
 */
public class SpacesRMPersistenceService implements RMPersistenceService
{

    private final ISSpacesPersistenceService rmmPeristenceService = com.integral.is.spaces.fx.persistence.PersistenceServiceFactory.getRmmPersistenceService();
    private static Log log = LogFactory.getLog(SpacesRMPersistenceService.class);
    private static SpacesRMPersistenceService instance = null;

    private static final String USERID_FIELD = "us.fxid";
    private static final String NAMESPACE_FIELD = "nssh";
    private static final String ID_FIELD = "_id";
    private static final String NAME_FIELD = "nm";
    private static final String STATE_FIELD = "st";
    private static final String EXTERNAL_ID_FIELD = "ei";

    protected Log getLogger(){
        return log;
    }

    /**
     * Even though this is a public method, this should be only set by the startup task once for the lifetime of the system.
     *
     * @param globalInstance
     */
    public static void __private_startup_setInstance( SpacesRMPersistenceService globalInstance )
    {
        instance = globalInstance;
    }

    public static RMPersistenceService instance()
    {
        return instance;
    }


	/* (non-Javadoc)
	 * @see com.integral.realmoney.persistence.dbservice.RMPersistenceService#createNettingPortfolio(com.integral.netting.model.NettingPortfolioC)
	 */
	@Override
	public ErrorMessage createNettingPortfolio( NettingPortfolio nettingPortfolio )
	{

        ErrorMessage errorMessage=null;
        try
        {
            nettingPortfolio.set_id(nettingPortfolio.getPortfolioID());
            nettingPortfolio.setNamespaceName(nettingPortfolio.getUser().getNamespace().getShortName());
            ((NettingPortfolioC)nettingPortfolio).setPropertiesForSpace();
            errorMessage = rmmPeristenceService.persist( nettingPortfolio, ApplicationEventCodes.EVENT_NETTING_PORTFOLIO_CREATE );
        }
        catch ( Exception e )
        {
            log.error( "SRMPS.createNettingPortfolio : Error in storing NettingPortfolio " + nettingPortfolio.getPortfolioID(), e );
            new ErrorMessageC().addError( e );
        }
        return errorMessage;
	}

    /* (non-Javadoc)
    * @see com.integral.realmoney.persistence.dbservice.RMPersistenceService#createNettingMKTSpotPrice(com.integral.netting.model.MarketSpotPriceC)
    */
    @Override
    public ErrorMessage createNettingMKTSpotPrice( MarketSpotPrice mktSpotPrice )
    {

        ErrorMessage errorMessage=null;
        try
        {
            errorMessage = rmmPeristenceService.persist( mktSpotPrice, ApplicationEventCodes.EVENT_NETTING_MKTPRC_CREATE );
        }
        catch ( Exception e )
        {
            log.error( "SRMPS.createNettingMKTSpotPrice : Error in storing MarketSpotPrice " + mktSpotPrice.get_id(), e );
            new ErrorMessageC().addError( e );
        }
        return errorMessage;
    }


    @Override
    public ErrorMessage createNettingTradeRequest( NettingTradeRequest nettingTradeRequest )
    {

        ErrorMessage errorMessage=null;
        try
        {
            errorMessage = rmmPeristenceService.persist( nettingTradeRequest, ApplicationEventCodes.EVENT_NETTING_NTR_CREATE );
        }
        catch ( Exception e )
        {
            log.error( "SRMPS.createNettingTradeRequest : Error in storing NettingTradeRequest " + nettingTradeRequest.get_id(), e );
            new ErrorMessageC().addError( e );
        }
        return errorMessage;
    }
    
    /* (non-Javadoc)
    * @see com.integral.realmoney.persistence.dbservice.RMPersistenceService#createNettingMKTSpotPrice(com.integral.netting.model.MarketSpotPriceC)
    */
    @Override
    public ErrorMessage updateNettingTradeRequest( NettingTradeRequest nettingTradeRequest)
    {

        ErrorMessage errorMessage=null;
        try
        {
            errorMessage = rmmPeristenceService.persist( nettingTradeRequest, ApplicationEventCodes.EVENT_NETTING_NTR_UPDATE );
        }
        catch ( Exception e )
        {
            log.error( "SRMPS.updateNettingTradeRequest : Error in updating NettingTradeRequest " + nettingTradeRequest.get_id(), e );
            new ErrorMessageC().addError( e );
        }
        return errorMessage;
    }
    /* (non-Javadoc)
	 * @see com.integral.realmoney.persistence.dbservice.RMPersistenceService#updateNettingPortfolioState(com.integral.netting.model.NettingPortfolio)
	 */
	@Override
	public ErrorMessage updateNettingPortfolioState( NettingPortfolio nettingPortfolio )
	{
        ErrorMessage errorMessage=null;
        try
        {
            nettingPortfolio.set_id(nettingPortfolio.getPortfolioID());
            nettingPortfolio.setNamespaceName(nettingPortfolio.getUser().getNamespace().getShortName());
            NettingPortfolio nrtPortfolioSpaces = getPortfolioById( nettingPortfolio.getPortfolioID(), nettingPortfolio.getUser() );
            if(null!=nrtPortfolioSpaces && nrtPortfolioSpaces.getVersionId() > nettingPortfolio.getVersionId())
            {
                nettingPortfolio.setVersionId(nrtPortfolioSpaces.getVersionId());
            }
            ((NettingPortfolioC)nettingPortfolio).setPropertiesForSpace();
            ((NettingPortfolioC)nettingPortfolio).setModifiedDtTime(new Timestamp(new Date().getTime()));
            errorMessage = rmmPeristenceService.persist( nettingPortfolio, ApplicationEventCodes.EVENT_NETTING_PORTFOLIO_UPDATE );
        }
        catch ( Exception e )
        {
            log.error( "SRMPS.updateNettingPortfolioState : Error in updating NettingPortfolio " + nettingPortfolio.getPortfolioID(), e );
            new ErrorMessageC().addError( e );
        }
        return errorMessage;
	}


	/* (non-Javadoc)
	 * @see com.integral.realmoney.persistence.dbservice.RMPersistenceService#getLastActivePortfolio(com.integral.user.User)
	 */
	@Override
	public NettingPortfolio getLastActivePortfolio( User user )
	{
        long st = System.nanoTime();
        String nameSpace = user.getNamespace().getShortName();
        try
        {
            Metaspace metaSpace = Metaspaces.getInstance().getMetaspace(nameSpace, PersistenceServiceConfiguration.getRmmMetaspaceName());

            if ( metaSpace != null )
            {
                QueryBuilder queryBuilder = metaSpace.createNewQueryBuilder(nameSpace, PersistenceConstants.NETTING);
                CriteriaSet cs = metaSpace.defaultCriteriaSet();
                queryBuilder.add(cs.is(NAMESPACE_FIELD, nameSpace))
                            .add(cs.is("npe."+USERID_FIELD, user.getObjectID()))
                            .add(cs.notEquals("npe."+STATE_FIELD, NettingPortfolioState.Allocated.getId()));

                SpaceIterator<NettingPortfolioDealingModelC> spaceIterator = queryBuilder.build().getIterator(NettingPortfolioDealingModelC.class);
                NettingPortfolioC selectedPortfolio = null;
                while(spaceIterator.hasNext()){
                    NettingPortfolioDealingModelC activeDM = spaceIterator.next();
                    NettingPortfolioC active = (NettingPortfolioC)activeDM.getNettingPortfolioEntity();
                    if( selectedPortfolio == null )
                    {
                        selectedPortfolio = active;
                    }
                    else if ( active.getModifiedDtTime().after(selectedPortfolio.getModifiedDtTime()))
                    {
                        selectedPortfolio = active;
                    }


                }
                log.info("SRMPS.getLastActivePortfolio Portfolio name=" + (null!=selectedPortfolio?selectedPortfolio.getName():"") + ",nettingPortfolio=" + selectedPortfolio + ",timetaken=" + (System.nanoTime() - st) / 1000);
                if(null!=selectedPortfolio){
                    selectedPortfolio.setPropertiesFromSpace();
                }
                return selectedPortfolio;
            }
        }
        catch ( Exception ex )
        {
            log.error("SRMPS.getLastActivePortfolio: Exception in query ", ex);
        }
        return null;
	}

	/* (non-Javadoc)
	 * @see com.integral.realmoney.persistence.dbservice.RMPersistenceService#getPortfolioByName(java.lang.String, com.integral.user.User)
	 */
	@Override
	public NettingPortfolio getPortfolioByName( String name, User user, boolean isUserSpecific )
	{
        long st = System.nanoTime();
        String nameSpace = user.getNamespace().getShortName();
        try
        {
            Metaspace metaSpace = Metaspaces.getInstance().getMetaspace(nameSpace, PersistenceServiceConfiguration.getRmmMetaspaceName());

            if ( metaSpace != null )
            {
                CriteriaSet cs = metaSpace.defaultCriteriaSet();
                QueryBuilder queryBuilder = metaSpace.createNewQueryBuilder(nameSpace, PersistenceConstants.NETTING);
                queryBuilder.add(cs.is("npe."+NAME_FIELD, name));
                
                if(isUserSpecific)
                {
                	queryBuilder.add(cs.is("npe."+USERID_FIELD, user.getObjectID()));
                }
                
                SpaceIterator<NettingPortfolioDealingModelC> spaceIterator = queryBuilder.build().getIterator(NettingPortfolioDealingModelC.class);
                NettingPortfolioC selectedPortfolio = null;
                while(spaceIterator.hasNext()){
                    NettingPortfolioDealingModelC activeDM = spaceIterator.next();
                    NettingPortfolioC active = (NettingPortfolioC)activeDM.getNettingPortfolioEntity();
                    if( selectedPortfolio == null )
                    {
                        selectedPortfolio = active;
                    }
                    else if ( active.getModifiedDtTime().after(selectedPortfolio.getModifiedDtTime()))
                    {
                        selectedPortfolio = active;
                    }
                }
                log.info("SRMPS.getPortfolioByName Portfolio name=" + name + ",nettingPortfolio=" + selectedPortfolio + ",timetaken=" + (System.nanoTime() - st) / 1000);
                if(null!=selectedPortfolio){
                    selectedPortfolio.setPropertiesFromSpace();
                }
                return selectedPortfolio;
            }
        }
        catch ( Exception ex )
        {
            log.error("SRMPS.getPortfolioByName: Exception in query ", ex);
        }
        return null;
	}

    /* (non-Javadoc)
    * @see com.integral.realmoney.persistence.dbservice.RMPersistenceService#getPortfolioById(java.lang.String, com.integral.user.User)
    */
	@Override
	public NettingPortfolio getPortfolioById( String Id, User user )
	{
        long st = System.nanoTime();
        String nameSpace = user.getNamespace().getShortName();
        try
        {
            Metaspace metaSpace = Metaspaces.getInstance().getMetaspace(nameSpace, PersistenceServiceConfiguration.getRmmMetaspaceName());
            if ( metaSpace != null )
            {
                CriteriaSet cs = metaSpace.defaultCriteriaSet();
                QueryBuilder queryBuilder = metaSpace.createNewQueryBuilder(nameSpace, PersistenceConstants.NETTING);
                queryBuilder.add(cs.is(NAMESPACE_FIELD, nameSpace))
                        .add(cs.is("npe."+USERID_FIELD, user.getObjectID()))
                        .add(cs.is(ID_FIELD, Id));

                NettingPortfolioC nettingPortfolio=null;
                NettingPortfolioDealingModelC nettingPortfolioDM = (NettingPortfolioDealingModelC) queryBuilder.build().getSingleResult(NettingPortfolioDealingModelC.class);
                if(null!=nettingPortfolioDM){
                	nettingPortfolio = (NettingPortfolioC)nettingPortfolioDM.getNettingPortfolioEntity();
                    nettingPortfolio.setPropertiesFromSpace();
                }
                log.info("SRMPS.getPortfolioById Portfolio Id=" + Id + ",nettingPortfolio=" + nettingPortfolio + ",timetaken=" + (System.nanoTime() - st) / 1000);
                return nettingPortfolio;
            }
        }
        catch ( Exception ex )
        {
            log.error("SRMPS.getPortfolioById: Exception in query ", ex);
        }
        return null;
	}

	/* (non-Javadoc)
	   * @see com.integral.realmoney.persistence.dbservice.RMPersistenceService#getActivePortfoliosByOrg(java.lang.String, com.integral.user.User)
	   */
	@Override
	public List<NettingPortfolio> getActivePortfoliosByOrg( User user, Set<Integer> pfStatesToGet )
	{
		// TODO Auto-generated method stub
        // Sid - Check how to get portfolios for all users in one org. The schema is currently by user
        long st = System.nanoTime();
        String nameSpace = user.getNamespace().getShortName();
        try
        {
            Metaspace metaSpace = Metaspaces.getInstance().getMetaspace(nameSpace, PersistenceServiceConfiguration.getRmmMetaspaceName());
            if ( metaSpace != null )
            {
                CriteriaSet cs = metaSpace.defaultCriteriaSet();
                QueryBuilder queryBuilder = metaSpace.createNewQueryBuilder(nameSpace, PersistenceConstants.NETTING);
                queryBuilder.add(cs.is(NAMESPACE_FIELD, nameSpace))
                        .add(cs.is("npe."+USERID_FIELD, user.getObjectID()))
                        .add(cs.in("npe."+STATE_FIELD, pfStatesToGet.toArray()));

                List<NettingPortfolio> portfolios = new ArrayList<NettingPortfolio>();
                SpaceIterator<NettingPortfolioDealingModelC> itr  = queryBuilder.build().getIterator(NettingPortfolioDealingModelC.class);

                while (itr.hasNext()) {
                    NettingPortfolioDealingModelC snPortfolioDM = itr.next();
                    NettingPortfolioC snPort = (NettingPortfolioC)snPortfolioDM.getNettingPortfolioEntity();
                    snPort.setPropertiesFromSpace();
                    portfolios.add(snPort);
                }


                log.info("SRMPS.getActivePortfoliosByOrg Portfolio pfStatesToGet=" + pfStatesToGet + ",portfolios=" + portfolios + ",timetaken=" + (System.nanoTime() - st) / 1000);
                return portfolios;
            }
        }
        catch ( Exception ex )
        {
            log.error("SRMPS.getActivePortfoliosByOrg: Exception in query ", ex);
        }
        return null;
	}

	@Override
	public ErrorMessage completeNettingPortfolioState( NettingPortfolio nettingPortfolio )
	{

		return updateNettingPortfolioState(nettingPortfolio);
	}

	/* (non-Javadoc)
	 * @see com.integral.realmoney.persistence.dbservice.RMPersistenceService#getPortfolioByExternalId(java.lang.String, com.integral.user.User)
	 */
	@Override
	public NettingPortfolio getPortfolioByExternalId( String Id, Namespace namespace )
	{
		long st = System.nanoTime();
        String nameSpace = namespace.getShortName();
        try
        {
            Metaspace metaSpace = Metaspaces.getInstance().getMetaspace(nameSpace, PersistenceServiceConfiguration.getRmmMetaspaceName());
            if ( metaSpace != null )
            {
                CriteriaSet cs = metaSpace.defaultCriteriaSet();
                QueryBuilder queryBuilder = metaSpace.createNewQueryBuilder(nameSpace, PersistenceConstants.NETTING);
                queryBuilder.add(cs.is(NAMESPACE_FIELD, nameSpace))
                        .add(cs.is("npe."+EXTERNAL_ID_FIELD, Id));

                NettingPortfolioC nettingPortfolio=null;
                NettingPortfolioDealingModelC nettingPortfolioDM = (NettingPortfolioDealingModelC) queryBuilder.build().getSingleResult(NettingPortfolioDealingModelC.class);
                if(null!=nettingPortfolioDM){
                	nettingPortfolio = (NettingPortfolioC)nettingPortfolioDM.getNettingPortfolioEntity();
                    nettingPortfolio.setPropertiesFromSpace();
                }
                log.info("SRMPS.getPortfolioByExternalId Portfolio externalId=" + Id + ",nettingPortfolio=" + nettingPortfolio + ",timetaken=" + (System.nanoTime() - st) / 1000);
                return nettingPortfolio;
            }
        }
        catch ( Exception ex )
        {
            log.error("SRMPS.getPortfolioByExternalId: Exception in query ", ex);
        }
        return null;
	}

	@Override
	public ErrorMessage updateNettingPortfolioState(NettingPortfolio nettingPortfolio, String event, Map<String, String> properties) {
        return updateNettingPortfolioState(nettingPortfolio);
	}

	
}
