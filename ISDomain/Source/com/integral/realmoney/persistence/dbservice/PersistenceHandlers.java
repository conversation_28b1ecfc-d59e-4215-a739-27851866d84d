/**
 * <AUTHOR>
 */
package com.integral.realmoney.persistence.dbservice;

import java.util.Collection;
import java.util.Iterator;
import java.util.concurrent.atomic.AtomicInteger;

import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

import com.integral.SEF.UTIService;
import com.integral.dbservice.TransactionLogMessage;
import com.integral.dbservice.is.ISPersistenceServiceCacheManager;
import com.integral.is.common.util.ISTransactionManager;
import com.integral.netting.model.MarketSpotPrice;
import com.integral.netting.model.MarketSpotPriceC;
import com.integral.netting.model.NettingPortfolio;
import com.integral.netting.model.NettingPortfolioC;
import com.integral.netting.model.NettingTradeRequest;
import com.integral.netting.model.NettingTradeRequestC;
import com.integral.netting.model.TradeRequestLeg;
import com.integral.netting.model.TradeRequestLegC;
import com.integral.persistence.Entity;
import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import com.integral.session.IdcTransaction;

/**
 * <AUTHOR>
 *
 */
public class PersistenceHandlers
{

	private static AtomicInteger BASE_INDEX = new AtomicInteger(800);

	public static class StorePortfolioEventHandler extends Processor
	{
		public static String EVENT = "storeNettingPortfolio";
		public static int INDEX = BASE_INDEX.incrementAndGet();
		private final ISPersistenceServiceCacheManager cache;

		public StorePortfolioEventHandler( ISPersistenceServiceCacheManager cache )
		{
			this.cache = cache;
		}

		@Override
		public PipelineMessage process( PipelineMessage msg )
		{
			try
			{
				TransactionLogMessage tlm = (TransactionLogMessage) msg;
				NettingPortfolio dPortfolio = (NettingPortfolio) tlm.entity;
				log.warn("Tenant:" + cache.getPeerVirtualServer() + ". StorePortfolioEventHandler.process called for portfolio= " + dPortfolio.getPortfolioID()
						+ ", msg correlationID = " + msg.getCorelationId());
				ISTransactionManager.setUser(dPortfolio.getUser());
				IdcTransaction tx = ISTransactionManager.startTransaction(ISTransactionManager.getNormalWorkflowReadOnlyClasses(), EVENT);
				NettingPortfolio registeredPortfolio = (NettingPortfolio) ((Entity) dPortfolio).getRegisteredObject();
				Iterator<NettingTradeRequest> inputRequests = registeredPortfolio.getInputRequestPojos().iterator();
				while ( inputRequests.hasNext() )
				{
					NettingTradeRequestC current = (NettingTradeRequestC)((Entity) inputRequests.next()).getRegisteredObject();
					current.setNettingPortfolio(registeredPortfolio);
					Iterator<TradeRequestLeg> legs = current.getTradeLegs().iterator();
					while ( legs.hasNext() )
					{
						TradeRequestLegC aLeg = (TradeRequestLegC) legs.next();
						aLeg.setTradeRequest(current);
					}
				}
				Iterator<MarketSpotPrice> marketPrices = registeredPortfolio.getMarketPrices().iterator();
				while ( marketPrices.hasNext() )
				{
					MarketSpotPriceC price = (MarketSpotPriceC) marketPrices.next();
					price.setPortfolio(registeredPortfolio);
				}
				ISTransactionManager.endTransaction(tx, EVENT);
			}
			catch ( Exception e )
			{
				throw new PipelineException(e);
			}
			return msg;
		}
	}


	public static class PortfolioStateUpdateEventHandler extends Processor
	{
		public static String EVENT = "updateNettingPortfolio";
		public static int INDEX = BASE_INDEX.incrementAndGet();
		private final ISPersistenceServiceCacheManager cache;

		public PortfolioStateUpdateEventHandler( ISPersistenceServiceCacheManager cache )
		{
			this.cache = cache;
		}

		@Override
		public PipelineMessage process( PipelineMessage msg )
		{
			try
			{
				TransactionLogMessage tlm = (TransactionLogMessage) msg;
				NettingPortfolio dPortfolio = (NettingPortfolio) tlm.entity;
				log.warn("Tenant:" + cache.getPeerVirtualServer() + ". PortfolioStateUpdateEventHandler.process called for portfolio= " + dPortfolio.getPortfolioID()
						+ ", msg correlationID = " + msg.getCorelationId());
				ISTransactionManager.setUser(dPortfolio.getUser());
	            ExpressionBuilder eb = new ExpressionBuilder();
	            Expression portfolioIdExp = eb.get( "portfolioID" ).equal( dPortfolio.getPortfolioID() );
	            Expression nsExp = eb.get( "namespace" ).equal( dPortfolio.getOrganization().getNamespace() );
	            IdcTransaction tx = ISTransactionManager.startTransaction(ISTransactionManager.getNormalWorkflowReadOnlyClasses(), EVENT);
	            NettingPortfolioC portfolio = ( NettingPortfolioC ) tx.getUOW().readObject( NettingPortfolioC.class, portfolioIdExp.and( nsExp ) );
				NettingPortfolio registeredPortfolio = (NettingPortfolio) ((Entity) portfolio).getRegisteredObject();
				registeredPortfolio.setState(dPortfolio.getState());
				registeredPortfolio.setClOrderIds(dPortfolio.getClOrderIds());
				registeredPortfolio.setTrueupClOrderIds(dPortfolio.getTrueupClOrderIds());
				registeredPortfolio.setXtrueupClOrderIds(dPortfolio.getXtrueupClOrderIds());
				registeredPortfolio.setFsrRequestIds(dPortfolio.getFsrRequestIds());

                //update nettingtraderequest in case of allocated
				/*
                if( NettingPortfolioState.Allocated.getId() == dPortfolio.getState()){
                    Collection<NettingTradeRequest> desealizedInputRequests = dPortfolio.getInputRequestPojos();
                    Iterator<NettingTradeRequest> dbRequests = registeredPortfolio.getInputRequestPojos().iterator();
                    while ( dbRequests.hasNext() )
                    {
                    	NettingTradeRequestC registeredNTR = (NettingTradeRequestC) ((Entity) dbRequests.next()).getRegisteredObject();
                    	NettingTradeRequest dNettingTradeRequest = lookupNettingTradeRequest( registeredNTR.getSequenceId() , desealizedInputRequests );
                    	if( dNettingTradeRequest != null )
                    	{
                    		for( TradeRequestLeg registeredNTRLeg : registeredNTR.getTradeLegs() )
                    		{
                    			TradeRequestLeg dLeg = dNettingTradeRequest.getTradeLegs(registeredNTRLeg.getLegId() );
                    			if( dLeg != null )
                    			{
                    				registeredNTRLeg.setBaseAmount( dLeg.getBaseAmount() );
                    				registeredNTRLeg.setTermAmount( dLeg.getTermAmount() );
                    				registeredNTR.setTradeRate( dNettingTradeRequest.getTradeRate());
                    			}
                    		}
                    	}
                    }
                }*/
				ISTransactionManager.endTransaction(tx, EVENT);
			}
			catch ( Exception e )
			{
				throw new PipelineException(e);
			}
			return msg;
		}
	}
	
	
	public static class PortfolioCompleteEventHandler extends Processor
	{
		public static String EVENT = "completeNettingPortfolio";
		public static int INDEX = BASE_INDEX.incrementAndGet();
		private final ISPersistenceServiceCacheManager cache;

		public PortfolioCompleteEventHandler ( ISPersistenceServiceCacheManager cache )
		{
			this.cache = cache;
		}

		@Override
		public PipelineMessage process( PipelineMessage msg )
		{
			try
			{
				TransactionLogMessage tlm = (TransactionLogMessage) msg;
				NettingPortfolio dPortfolio = (NettingPortfolio) tlm.entity;
				log.warn("Tenant:" + cache.getPeerVirtualServer() + ". PortfolioCompleteEventHandler.process called for portfolio= " + dPortfolio.getPortfolioID()
						+ ", msg correlationID = " + msg.getCorelationId());
				ISTransactionManager.setUser(dPortfolio.getUser());
	            ExpressionBuilder eb = new ExpressionBuilder();
	            Expression portfolioIdExp = eb.get( "portfolioID" ).equal( dPortfolio.getPortfolioID() );
	            Expression nsExp = eb.get( "namespace" ).equal( dPortfolio.getOrganization().getNamespace() );
	            IdcTransaction tx = ISTransactionManager.startTransaction(ISTransactionManager.getNormalWorkflowReadOnlyClasses(), EVENT);
	            NettingPortfolioC portfolio = ( NettingPortfolioC ) tx.getUOW().readObject( NettingPortfolioC.class, portfolioIdExp.and( nsExp ) );
				NettingPortfolio registeredPortfolio = (NettingPortfolio) ((Entity) portfolio).getRegisteredObject();
				registeredPortfolio.setState(dPortfolio.getState());
				registeredPortfolio.setClOrderIds(dPortfolio.getClOrderIds());
				registeredPortfolio.setTrueupClOrderIds(dPortfolio.getTrueupClOrderIds());
				registeredPortfolio.setXtrueupClOrderIds(dPortfolio.getXtrueupClOrderIds());
				registeredPortfolio.setFsrRequestIds(dPortfolio.getFsrRequestIds());
				Collection<NettingTradeRequest> desealizedInputRequests = dPortfolio.getInputRequestPojos();
				Iterator<NettingTradeRequest> dbRequests = registeredPortfolio.getInputRequestPojos().iterator();
				while ( dbRequests.hasNext() )
				{
					NettingTradeRequestC registeredNTR = (NettingTradeRequestC) ((Entity) dbRequests.next()).getRegisteredObject();
					NettingTradeRequest dNettingTradeRequest = lookupNettingTradeRequest( registeredNTR.getSequenceId() , desealizedInputRequests );
					if( dNettingTradeRequest != null )
					{
						registeredNTR.setTradeId(dNettingTradeRequest.getTradeId());
					}
				}
				ISTransactionManager.endTransaction(tx, EVENT);
			}
			catch ( Exception e )
			{
				throw new PipelineException(e);
			}
			return msg;
		}

        private NettingTradeRequest lookupNettingTradeRequest(long seqId, Collection<NettingTradeRequest> inputRequests){
            for(NettingTradeRequest nettingTradeRequest : inputRequests){
                if(seqId == nettingTradeRequest.getSequenceId()){
                    return nettingTradeRequest;
                }
            }
            return null;
        }
	}
	
	public static class PortfolioUTIUpdateEventHandler extends Processor
	{
		public static String EVENT = "updateNettingPortfolioUTI";
		public static int INDEX = BASE_INDEX.incrementAndGet();
		private final ISPersistenceServiceCacheManager cache;

		public PortfolioUTIUpdateEventHandler( ISPersistenceServiceCacheManager cache )
		{
			this.cache = cache;
		}

		@Override
		public PipelineMessage process( PipelineMessage msg )
		{
			try
			{
				TransactionLogMessage tlm = (TransactionLogMessage) msg;
				NettingPortfolio dPortfolio = (NettingPortfolio) tlm.entity;
				log.warn("Tenant:" + cache.getPeerVirtualServer() + ". PortfolioUTIUpdateEventHandler.process called for portfolio= " + dPortfolio.getPortfolioID()
						+ ", msg correlationID = " + msg.getCorelationId());
				ISTransactionManager.setUser(dPortfolio.getUser());
	            ExpressionBuilder eb = new ExpressionBuilder();
	            Expression portfolioIdExp = eb.get( "portfolioID" ).equal( dPortfolio.getPortfolioID() );
	            Expression nsExp = eb.get( "namespace" ).equal( dPortfolio.getOrganization().getNamespace() );
	            IdcTransaction tx = ISTransactionManager.startTransaction(ISTransactionManager.getNormalWorkflowReadOnlyClasses(), EVENT);
	            NettingPortfolioC portfolio = ( NettingPortfolioC ) tx.getUOW().readObject( NettingPortfolioC.class, portfolioIdExp.and( nsExp ) );
				NettingPortfolio registeredPortfolio = (NettingPortfolio) ((Entity) portfolio).getRegisteredObject();
				
                if( UTIService.isUTIEnabled()){
                    Collection<NettingTradeRequest> desealizedInputRequests = dPortfolio.getInputRequestPojos();
                    Iterator<NettingTradeRequest> dbRequests = registeredPortfolio.getInputRequestPojos().iterator();
                    while ( dbRequests.hasNext() )
                    {
                    	NettingTradeRequestC registeredNTR = (NettingTradeRequestC) ((Entity) dbRequests.next()).getRegisteredObject();
                    	NettingTradeRequest dNettingTradeRequest = lookupNettingTradeRequest( registeredNTR.getSequenceId() , desealizedInputRequests );
                    	if( dNettingTradeRequest != null )
                    	{
                    		for( TradeRequestLeg registeredNTRLeg : registeredNTR.getTradeLegs() )
                    		{
                    			TradeRequestLeg dLeg = dNettingTradeRequest.getTradeLegs(registeredNTRLeg.getLegId() );
                    			if( dLeg != null )
                    			{
                    				if(dLeg.getUTI() != null && dLeg.getUTINamespace() != null) {
                    					registeredNTRLeg.setUTI(dLeg.getUTI());
                    					registeredNTRLeg.setUTINamespace(dLeg.getUTINamespace());
                    				}
                    			}
                    		}
                    	}
                    }
                }
				ISTransactionManager.endTransaction(tx, EVENT);
			}
			catch ( Exception e )
			{
				throw new PipelineException(e);
			}
			return msg;
		}

        private NettingTradeRequest lookupNettingTradeRequest(long seqId, Collection<NettingTradeRequest> inputRequests){
            for(NettingTradeRequest nettingTradeRequest : inputRequests){
                if(seqId == nettingTradeRequest.getSequenceId()){
                    return nettingTradeRequest;
                }
            }
            return null;
        }
	}
	
	
	public static class StorePortfolioAcceptanceRequestEventHandler extends Processor
	{
		public static String EVENT = "storePortfolioAcceptanceRequest";
		public static int INDEX = BASE_INDEX.incrementAndGet();
		private final ISPersistenceServiceCacheManager cache;

		public StorePortfolioAcceptanceRequestEventHandler ( ISPersistenceServiceCacheManager cache )
		{
			this.cache = cache;
		}

		@Override
		public PipelineMessage process( PipelineMessage msg )
		{
			try
			{
				TransactionLogMessage tlm = (TransactionLogMessage) msg;
				NettingPortfolio dPortfolio = (NettingPortfolio) tlm.entity;
				log.warn("Tenant:" + cache.getPeerVirtualServer() + ". StorePortfolioAcceptanceRequestEventHandler.process called for portfolio= " + dPortfolio.getPortfolioID()
						+ ", msg correlationID = " + msg.getCorelationId());
				ISTransactionManager.setUser(dPortfolio.getUser());
	            ExpressionBuilder eb = new ExpressionBuilder();
	            Expression portfolioIdExp = eb.get( "portfolioID" ).equal( dPortfolio.getPortfolioID() );
	            Expression nsExp = eb.get( "namespace" ).equal( dPortfolio.getOrganization().getNamespace() );
	            IdcTransaction tx = ISTransactionManager.startTransaction(ISTransactionManager.getNormalWorkflowReadOnlyClasses(), EVENT);
	            NettingPortfolioC portfolio = ( NettingPortfolioC ) tx.getUOW().readObject( NettingPortfolioC.class, portfolioIdExp.and( nsExp ) );
				NettingPortfolio registeredPortfolio = (NettingPortfolio) ((Entity) portfolio).getRegisteredObject();
				registeredPortfolio.setState(dPortfolio.getState());
				registeredPortfolio.setClOrderIds(dPortfolio.getClOrderIds());
				registeredPortfolio.setTrueupClOrderIds(dPortfolio.getTrueupClOrderIds());
				registeredPortfolio.setXtrueupClOrderIds(dPortfolio.getXtrueupClOrderIds());
				registeredPortfolio.setFsrRequestIds(dPortfolio.getFsrRequestIds());
				
				Collection<NettingTradeRequest> desealizedAcceptanceRequests = dPortfolio.getAcceptedRequests();
				for ( NettingTradeRequest current : desealizedAcceptanceRequests )
				{
					current = (NettingTradeRequestC)((Entity)current).getRegisteredObject();
					current.setOwningPortfolio(registeredPortfolio);
					Iterator<TradeRequestLeg> legs = current.getTradeLegs().iterator();
					while ( legs.hasNext() )
					{
						TradeRequestLegC aLeg = (TradeRequestLegC) legs.next();
						aLeg.setTradeRequest(current);
					}
				}
				

                if( UTIService.isUTIEnabled()){
                    Collection<NettingTradeRequest> desealizedInputRequests = dPortfolio.getInputRequestPojos();
                    Iterator<NettingTradeRequest> dbRequests = registeredPortfolio.getInputRequestPojos().iterator();
                    while ( dbRequests.hasNext() )
                    {
                    	NettingTradeRequestC registeredNTR = (NettingTradeRequestC) ((Entity) dbRequests.next()).getRegisteredObject();
                    	NettingTradeRequest dNettingTradeRequest = lookupNettingTradeRequest( registeredNTR.getSequenceId() , desealizedInputRequests );
                    	if( dNettingTradeRequest != null )
                    	{
                    		for( TradeRequestLeg registeredNTRLeg : registeredNTR.getTradeLegs() )
                    		{
                    			TradeRequestLeg dLeg = dNettingTradeRequest.getTradeLegs(registeredNTRLeg.getLegId() );
                    			if( dLeg != null )
                    			{
                    				if(dLeg.getUTI() != null && dLeg.getUTINamespace() != null) {
                    					registeredNTRLeg.setUTI(dLeg.getUTI());
                    					registeredNTRLeg.setUTINamespace(dLeg.getUTINamespace());
                    				}
                    			}
                    		}
                    	}
                    }
                }
				ISTransactionManager.endTransaction(tx, EVENT);
			}
			catch ( Exception e )
			{
				throw new PipelineException(e);
			}
			return msg;
		}
		
		
		 private NettingTradeRequest lookupNettingTradeRequest(long seqId, Collection<NettingTradeRequest> inputRequests){
	            for(NettingTradeRequest nettingTradeRequest : inputRequests){
	                if(seqId == nettingTradeRequest.getSequenceId()){
	                    return nettingTradeRequest;
	                }
	            }
	            return null;
	        }
		
	}

}
