package com.integral.orderbook.view;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.integral.is.common.util.JMSProxyUtil;
import com.integral.is.oms.Order;
import com.integral.is.oms.OrderBook;
import com.integral.is.oms.OrderBookC;
import com.integral.is.oms.OrderBookCacheC;
import com.integral.is.oms.OrderC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.User;

public class OrderBookViewNotifier implements Runnable{
	
	private Log log = LogFactory.getLog(this.getClass());

	private static final int ORDER_BOOK_VIEW_MSG_PRIORITY = 10; //TODO:expose it via property?
	private static final String ORDER_BOOK_VIEW_HANDLER = "OBV";

	public void notifyAllSubscribedOrg() {
		// get the list of orgs having atleast one subscribed user
		Enumeration<String> activeOrgList = OrderBookSubscriptionService
				.getInstance().getOrganizationsWithActiveSubscriptions();
		while (activeOrgList.hasMoreElements()) {
			String orgKey = activeOrgList.nextElement();
			// retrieve active users for each org
			ArrayList<String> activeUsers = OrderBookSubscriptionService
					.getInstance().getSubscribedUsersForOrg(orgKey);
			if (activeUsers != null && !activeUsers.isEmpty()) {
				notifySubscribedUsersOfOrg(orgKey, activeUsers);
			}
		}
	}

	private void notifySubscribedUsersOfOrg(String orgKey,
			ArrayList<String> subscribedUsers) {
		// get the order books belonging to this org
		List<OrderBook> orderBooks = OrderBookCacheC.getInstance()
				.findOrderBooksByOrganizationShortname(orgKey);
		for (OrderBook ob : orderBooks) {
			sendUpdatesForOrderBook(ob, subscribedUsers);
		}
	}
	
	private void sendUpdatesForOrderBook(OrderBook ob, ArrayList<String> subscribedUsers){
		//get the corresponding orderbook view
		//OrderBookView obv = OrderBookViewUtil.getOrderBookView(orderBook);
		
		//Iterator<OrderView> orderViewItr = obv.getOrderViews().iterator();
		//while (orderViewItr.hasNext()) {
		
		//Currently iterating only on pegged orders 
		for(Map.Entry<String, Order>orderItr : ((OrderBookC)ob).getPeggedOrders().entrySet()){
			//OrderView ov = orderViewItr.next();
			OrderView ov = ((OrderC)orderItr.getValue()).getOrderView();
			String ovUserName = ov.getUser().getShortName();
			//check if order view belongs to one of the subscribed users
			if (subscribedUsers.contains(ovUserName)) {
				//send only if OV is updated since the last flush
				if(ov.isLatestSinceLastFlush()){
					logMessage("Sending update about order view:" + ov);
					publishOrderView(ov.generateOrderViewResponse());
				}
			}
		}		
	}

	private void publishOrderView(OrderView ov) {
		//send to the jms proxy queue with the same key
		//replaces the older OV[if still not sent to the client] 
		//with the same key in the queue with this new OV.
		JMSProxyUtil.getInstance().addToJmsProxy(
				"OrderView:" + ov.getOrderId(), ORDER_BOOK_VIEW_MSG_PRIORITY,
				ORDER_BOOK_VIEW_HANDLER, ov, ov.getUser(), true);
	}

	@Override
	public void run() {
		try{
			notifyAllSubscribedOrg();			
		}catch (Exception exc) {
            log.error("OrderBookViewNotifier.run Exception " + exc.getMessage());
        }		
	}
	
	private void logMessage(String message) {
		if(log.isInfoEnabled()) 
			log.info(message);		
	}
}
