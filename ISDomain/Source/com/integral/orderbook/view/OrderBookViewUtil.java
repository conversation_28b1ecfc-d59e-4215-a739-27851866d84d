package com.integral.orderbook.view;

import java.util.ArrayList;
import java.util.Map;

import com.integral.is.oms.Order;
import com.integral.is.oms.OrderC;
import com.integral.is.oms.OrderBookC;
import com.integral.is.oms.OrderBook;

public class OrderBookViewUtil {

	public static OrderBookView getOrderBookView(OrderBook orderBook){
		//Currently we generate OrderBookView only for pegged orders
		//TODO: extend it to cover other order types
		ArrayList<OrderView> orderViews = new ArrayList<OrderView>();
		for(Map.Entry<String, Order>orderItr : ((OrderBookC)orderBook).getPeggedOrders().entrySet()){
			orderViews.add(((OrderC)orderItr.getValue()).getOrderView());
		}
		return new OrderBookView(orderViews);		
	}
}
