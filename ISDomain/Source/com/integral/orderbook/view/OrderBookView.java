package com.integral.orderbook.view;

import java.util.ArrayList;
import java.util.Collection;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public class OrderBookView {
	
	//key : orderID, value : OrderView
	private ConcurrentMap<String, OrderView> orderViews = new ConcurrentHashMap<String, OrderView>();

	public OrderBookView(ArrayList<OrderView> orderViews){
		for(OrderView ov : orderViews){
			add(ov);
		}		
	}
	
	public void add(OrderView orderView) {		
		orderViews.put(orderView.getOrderId(), orderView);
	}

	public void remove(String orderID) {
		orderViews.remove(orderID);
	}
	
	public Collection<OrderView> getOrderViews(){
		return orderViews.values();
	}
}
