package com.integral.orderbook.view;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import com.integral.log.Log;
import com.integral.log.LogFactory;


public class OrderBookViewNotifyScheduler {
	private ScheduledExecutorService obvNotifierExecutorService;
	private ScheduledFuture schedule;
	private int notificationInterval;
	private OrderBookViewNotifier notifyTask;
	private Log log = LogFactory.getLog(this.getClass());

	private static OrderBookViewNotifyScheduler instance = new OrderBookViewNotifyScheduler();

	public static OrderBookViewNotifyScheduler getInstance() {
		return instance;
	}

	private OrderBookViewNotifyScheduler() {		
		this.notifyTask = new OrderBookViewNotifier();		
	}

	public void start() {
		if (!isRunning()) {
			obvNotifierExecutorService = Executors.newScheduledThreadPool(1);
			notificationInterval = OrderBookViewAttributeMBeanC.getInstance().getOBVNotificationInterval();
			schedule = obvNotifierExecutorService.scheduleWithFixedDelay(
					notifyTask, 0, notificationInterval,
					TimeUnit.MILLISECONDS);
			if (log.isInfoEnabled()) {
				StringBuilder sb = new StringBuilder(
						"OrderBookViewNotifyScheduler is started with in between delay:")
						.append(notificationInterval);
				log.info(sb.toString());
			}
		}
	}
	
	public void stop() {
		try {
			if (obvNotifierExecutorService != null) {
				if (schedule != null) {
					schedule.cancel(false);
				}
				obvNotifierExecutorService.shutdown();
				// block till the executor shuts down
				while (!obvNotifierExecutorService.awaitTermination(10,
						TimeUnit.MILLISECONDS))
					;
				if (log.isInfoEnabled()) {
					StringBuilder sb = new StringBuilder(
							"OrderBookViewNotifyScheduler shutdown");
					log.info(sb.toString());
				}
			}
		} catch (InterruptedException ie) {
		}
	}

	private boolean isRunning() {
		return schedule != null && !schedule.isDone();
	}
}
