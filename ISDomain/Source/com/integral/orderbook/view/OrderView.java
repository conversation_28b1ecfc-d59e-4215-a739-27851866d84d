package com.integral.orderbook.view;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

import com.integral.model.dealing.BidOfferMode;
import org.codehaus.jackson.annotate.JsonIgnore;

import com.integral.is.oms.Order;
import com.integral.user.User;

public class OrderView {

    private static final String DELIMITER = "_";

    private static DateFormat df = new SimpleDateFormat("yyyyMMdd-HHmmss");

    static{
        df.setTimeZone(TimeZone.getTimeZone("GMT"));
    }

    public OrderView(Order order) {
        //TODO:currently create order views only for pegged orders

        if ( order.getEntityDescriptor().isPegOrder() ) {
            orderId                = order.getOrderId();
            rate                   = order.getEntityDescriptor().getOrderPrice();
            isLatestSinceLastFlush = true;
            user                   = order.getEntityDescriptor().getUserReference();
            eventDetails           = "Creation";

            execFlags = "PEG";

            // for peg orders, it can either be MARKET OR LIMIT
            if (order.getEntityDescriptor().isMarketOrder()) {
                orderType = "MARKET";
            }
            else {
                orderType = "LIMIT";
            }

            orderSize = order.getOrderAmount();
            orderSide = ( order.getEntityDescriptor().isBid() ) ? BidOfferMode.BID : BidOfferMode.OFFER;
            ccyPair   = order.getEntityDescriptor().getCurrencyPair();

            // this.sliceSubmittedTime
            // this.twapTobRangeAppliedPrice
        }
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        setLatestSinceLastFlush(true);
        this.orderType = orderType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        setLatestSinceLastFlush(true);
        this.orderId = orderId;
    }

    public double getRate() {
        return rate;
    }

    public void setRate(double rate) {
        if (rate != this.rate) {
            setLatestSinceLastFlush(true);
            this.rate = rate;
        }
    }

    public String getEventDetails() {
        return eventDetails;
    }

    public void setEventDetails(String eventDetails) {
        setLatestSinceLastFlush(true);
        this.eventDetails = eventDetails;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    private void setTimeStamp(String timeStamp) {
        setLatestSinceLastFlush(true);
        this.timeStamp = timeStamp;
    }

    private String orderId;
    private String orderType; // Limit, stop, market
    private String execFlags; // PEG, TWAP, PASSIVEAGGR
    private double rate; // peg rate in case of peg order
    private String eventDetails; // event update notifications
    private String timeStamp; // response updated time stamp
    private Long   sliceSubmittedTime; // twap slice submitted time
    private Double twapTobRangeAppliedPrice; // only for twap order

    private double       orderSize;
    private BidOfferMode orderSide;
    private String       ccyPair;

    public double getOrderSize() {
        return orderSize;
    }

    public void setOrderSize( double orderSize ) {
        this.orderSize = orderSize;
    }

    public BidOfferMode getOrderSide() {
        return orderSide;
    }

    public void setOrderSide( BidOfferMode orderSide ) {
        this.orderSide = orderSide;
    }

    public String getCcyPair() {
        return ccyPair;
    }

    public void setCcyPair( String ccyPair ) {
        this.ccyPair = ccyPair;
    }

    @JsonIgnore
    private User user;

    @JsonIgnore
    public User getUser() {
        return user;
    }

    @JsonIgnore
    private void setUser(User user) {
        setLatestSinceLastFlush(true);
        this.user = user;
    }

    // true, when any of the fields are updated; false if no
    // data update since the last update sent to client
    @JsonIgnore
    private boolean isLatestSinceLastFlush;

    @JsonIgnore
    public boolean isLatestSinceLastFlush() {
        return isLatestSinceLastFlush;
    }

    @JsonIgnore
    public void setLatestSinceLastFlush(boolean isLatestSinceLastFlush) {
        this.isLatestSinceLastFlush = isLatestSinceLastFlush;
    }

    public String getExecFlags() {
        return execFlags;
    }

    public void setExecFlags(String execFlags) {
        setLatestSinceLastFlush(true);
        this.execFlags = execFlags;
    }

    @JsonIgnore
    public OrderView generateOrderViewResponse() {
        setTimeStamp(getCurrentTimeStamp());
        // flip the flag to indicate this data has been sent to client
        setLatestSinceLastFlush(false);
        return this;
    }

    private String getCurrentTimeStamp() {
        return df.format(new Date());
    }

    public Long getSliceSubmittedTime() {
        return sliceSubmittedTime;
    }

    public void setSliceSubmittedTime(Long sliceSubmittedTime) {
        setLatestSinceLastFlush(true);
        this.sliceSubmittedTime = sliceSubmittedTime;
    }

    public Double getTwapTobRangeAppliedPrice() {
        return twapTobRangeAppliedPrice;
    }

    public void setTwapTobRangeAppliedPrice(Double twapTobRangeAppliedPrice) {
        setLatestSinceLastFlush(true);
        this.twapTobRangeAppliedPrice = twapTobRangeAppliedPrice;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Order ID:").append(orderId);
        sb.append(DELIMITER);
        sb.append("orderType:").append(orderType);
        sb.append(DELIMITER);
        sb.append("strategyName:").append(execFlags);
        sb.append(DELIMITER);
        sb.append("rate:").append(rate);
        sb.append(DELIMITER);
        sb.append("eventDetails:").append(eventDetails);
        sb.append(DELIMITER);
        sb.append("timeStamp:").append(timeStamp);
        sb.append(DELIMITER);
        sb.append("user:" + user.getShortName());
        sb.append(DELIMITER);
        sb.append("Org:" + user.getOrganization().getShortName());
        sb.append( DELIMITER );
        sb.append( "OrderSide: " ).append( ( orderSide == BidOfferMode.BID ? "Buy" : "Sell" ) );
        sb.append( DELIMITER );
        sb.append( "OrderSize: " ).append( orderSize );
        sb.append( DELIMITER );
        sb.append( "OrderCcyPair: " ).append( ccyPair );
        return sb.toString();
    }

    public void update(Order order, String eventMesg) {
        setRate(order.getEntityDescriptor().getOrderPrice());
        setEventDetails(eventMesg);
    }
}
