package com.integral.orderbook.view;

import com.integral.system.configuration.IdcMBeanC;

public class OrderBookViewAttributeMBeanC extends IdcMBeanC implements
		OrderBookViewAttributeMBean {

	public static OrderBookViewAttributeMBeanC mBeanInstance = new OrderBookViewAttributeMBeanC();
	
	public static OrderBookViewAttributeMBeanC getInstance(){
		return mBeanInstance;
	}
	
	private OrderBookViewAttributeMBeanC(){
		super(MBEAN_NAME);
	}
	
	private int OBV_NOTIFICATION_INTERVAL;
	
	@Override
	public void initialize(){
		OBV_NOTIFICATION_INTERVAL = getIntProperty(ORDER_BOOK_VIEW_NOTIFICATION_INTERVAL, 100); // 100 ms default
	}

	@Override
	public int getOBVNotificationInterval() {		
		return OBV_NOTIFICATION_INTERVAL;
	}
	
	public void setProperty(String key, String aValue, int scope,
			String oldValue) {
		super.setProperty(key, aValue, scope, oldValue);
		if (key.contentEquals((ORDER_BOOK_VIEW_NOTIFICATION_INTERVAL))) {
			if (aValue != null) {
				int newValue = Integer.valueOf(aValue);				
				OBV_NOTIFICATION_INTERVAL = newValue;
				// stop and start the OBVNotifyScheduler with the new delay
				if (log.isInfoEnabled()) {
					log.info("New delay set for Idc.OrderBook.View.NotificationInterval. Restarting OrderBookViewNotifyScheduler with new delay:"
							+ OBV_NOTIFICATION_INTERVAL);
				}
				OrderBookViewNotifyScheduler.getInstance().stop();
				OrderBookViewNotifyScheduler.getInstance().start();				
			}
		}
	}	
}
