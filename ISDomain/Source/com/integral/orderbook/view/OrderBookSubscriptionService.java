package com.integral.orderbook.view;

import java.util.ArrayList;
import java.util.Enumeration;
import java.util.concurrent.ConcurrentHashMap;

import org.json.JSONException;
import org.json.JSONStringer;

import com.integral.is.common.util.JMSProxyUtil;
import com.integral.is.oms.Order;
import com.integral.user.User;
import com.integral.jmsproxy.fxiapi.ApiCallback;
import com.integral.jmsproxy.fxiapi.ApiCommand;
import com.integral.jmsproxy.server.stream.StreamElement;
import com.integral.log.Log;
import com.integral.log.LogFactory;

public class OrderBookSubscriptionService {

	private static OrderBookSubscriptionService instance = new OrderBookSubscriptionService();

	private Log log = LogFactory.getLog(this.getClass().getName());

	private OrderBookSubscriptionService() {
	}

	public static OrderBookSubscriptionService getInstance() {
		return instance;
	}

	// Map containing list of users subscribed to orderbook view
	// key : <org_shortname>
	// value: <List of <user_shortname>>
	private ConcurrentHashMap<String, ArrayList<String>> orgToSubscribedUsers = new ConcurrentHashMap<String, ArrayList<String>>();

	public void registerUser(User user) {
		if (orgToSubscribedUsers.size() == 0) {
			// start the OBVNotifyScheduler on the first subscription request
			logMessage("First subscription for Order Book View recieved from user:"
					+ user.getShortName() + ".Starting the OBVNotifyScheduler");
			OrderBookViewNotifyScheduler.getInstance().start();
		}
		String orgKey = user.getOrganization().getShortName();
		ArrayList<String> subscribedUsers = orgToSubscribedUsers.get(orgKey);
		if (subscribedUsers == null) {
			subscribedUsers = new ArrayList<String>();
			subscribedUsers.add(user.getShortName());
			orgToSubscribedUsers.put(orgKey, subscribedUsers);
		} else {
			// ignore duplicate subscription requests
			if (!subscribedUsers.contains(user.getShortName())) {
				subscribedUsers.add(user.getShortName());
				orgToSubscribedUsers.put(orgKey, subscribedUsers);
			}else{				
				logMessage("Duplicate order book view subscription request received from user:"
						+ user.getShortName() + ":Org:"+ orgKey + ".Ignoring....");
			}
		}
	}

	public void deRegisterUser(User user) {
		String orgKey = user.getOrganization().getShortName();
		ArrayList<String> subscribedUsers = orgToSubscribedUsers.get(orgKey);
		// ignore un-subscribe requests from users who have not subscribed
		if (subscribedUsers != null) {
			subscribedUsers.remove(user.getShortName());
			if (subscribedUsers.isEmpty()) {
				orgToSubscribedUsers.remove(orgKey);
				if (orgToSubscribedUsers.size() == 0) {
					// shut down the OBVNotifyScheduler on the
					// de-registration of the last user of the last
					// organization
					logMessage("Last unsubscription for Order Book View recieved from user:"
							+ user.getShortName()
							+ ".Shutting down OBVNotifyScheduler");
					OrderBookViewNotifyScheduler.getInstance().stop();
				}
			}
		}
	}

	/**
	 * Returns a list of users subscribed to the order book view belonging to an
	 * org
	 * 
	 * @param orgKey
	 *            - Organization for which the subscribed users to be fetched
	 * @return List of users subscribed for the notifications ; null if none
	 */
	public ArrayList<String> getSubscribedUsersForOrg(String orgKey) {
		return orgToSubscribedUsers.get(orgKey);
	}

	/**
	 * Returns a list of organizations with users registered for order book view
	 * notifications.
	 * 
	 * @return List of organizations
	 */
	public Enumeration<String> getOrganizationsWithActiveSubscriptions() {
		return orgToSubscribedUsers.keys();
	}

	private void logMessage(String message) {
		if (log.isInfoEnabled())
			log.info(message);
	}
}
