package com.integral.gridmidrate.startup;

import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.Stream;
import com.integral.gridmidrate.GridMidRateTickEvent;
import com.integral.gridmidrate.config.GridMidRateTickerConfig;
import com.integral.gridmidrate.serializer.GridMidRateTickEventSerializer;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.runtime.StartupTask;
import com.integral.ticker.TickerFactory;
import com.integral.ticker.TickerService;
import com.integral.transport.multicast.MulticastGroup;
import com.integral.user.Organization;

import java.util.Hashtable;

/**
 * Created by pranabdas on 11/26/14.
 */
public class GridMidRateTickerStartupC implements StartupTask{
    private static final Log log = LogFactory.getLog( GridMidRateTickerStartupC.class );
    private static final String DEFAULT_GRIDMIDRATE_PRICE_SOURCE = "GMR";
    private int gmrMulticastPort;
    private String gmrMulticastAddress;
    private static ISMBean isMBean = ISFactory.getInstance().getISMBean();
    public static int gmrSourceAdaptorIndex;
    public static String gmrSourceStreamName;
    public static MulticastGroup gmrSourecMCastGrp;

    public String startup(String aName, Hashtable args) throws Exception {
        String result = "Started";
        TickerService<GridMidRateTickEvent, GridMidRateTickerConfig> service =
                TickerFactory.getInstance().getTickerService( GridMidRateTickEvent.class );
        if ( service.getConfiguration().isTickServiceEnabled() ) {
            try {
                service.setTickEventSerializer( new GridMidRateTickEventSerializer( service.getConfiguration() ) );
                getMulticastInfo();
                if( gmrMulticastAddress != null )
                {
                    service.getConfiguration().setMulticastAddress( gmrMulticastAddress, gmrMulticastPort );
                }
                else{
                    log.info( "GridMidRate Ticker Service not started as price source property is not configured or the value is not correct." );
                    result = "Not Configured";
                    return result;
                }
                service.start();
                log.info( "GridMidRate Ticker Service started successfully. MuticastAddress " + gmrMulticastAddress + ", port " + gmrMulticastPort );
            } catch ( Exception e ) {
                log.error( "Unable to start GridMidRate Ticker Service", e );
                throw e;
            }
        } else {
            log.info( "GridMidRate Ticker Service not started as it is not configured on this server." );
            result = "Not Configured";
        }
        return result;
    }

    public void getMulticastInfo(){
        String priceSrcString = isMBean.getGridMidRatePriceSource( DEFAULT_GRIDMIDRATE_PRICE_SOURCE );
        if( priceSrcString == null ){
            log.error( "GMRTSC.getMulticastInfo : price source not defined for this server." );
            return;
        }
        String adaptorName = null;
        String streamName = null;
        Organization adaptorOrg = null;
        Stream stream = null;
        if( priceSrcString != null ){
            String[] priceSources = priceSrcString.split( "," );
            if( priceSources != null && priceSources.length > 0 ){
                String[] source = priceSources[0].split( ":" );
                if( source != null && source.length == 2 ){
                    adaptorName = source[0];
                    streamName = source[1];
                }
            }
        }
        if( adaptorName != null && streamName != null ){
            adaptorOrg = ReferenceDataCacheC.getInstance().getOrganization( adaptorName );
            if( adaptorOrg == null ){
                log.error( "GMRTSC.getMulticastInfo : adaptor org not found." );
                return;
            }
            gmrSourceAdaptorIndex = adaptorOrg.getIndex();
            BrokerOrganizationFunction bof = adaptorOrg.getBrokerOrganizationFunction();
            if( bof == null ) return;
            stream = bof.getStream( streamName );
            if( stream == null ){
                log.error( "GMRTSC.getMulticastInfo : stream not configured for adaptor " + adaptorName );
                return;
            }
        }
        else{
            log.error( "GMRTSC.getMulticastInfo : price source not defined properly for this server." );
            return;
        }

        StringBuilder sb = new StringBuilder( 200 );
        sb.append( "GMRTSC.getMulticastInfo : Subscribing to Multicast address for Adaptor[" );
        sb.append( adaptorName ).append( ']' );
        sb.append( ",Stream[" ).append( streamName ).append( "]" );
        MulticastGroup mcastGroup = adaptorOrg.getMulticastGroup();
        if ( mcastGroup != null && mcastGroup.isEnabled() && !mcastGroup.getAddresses().isEmpty() )
        {
            gmrMulticastPort = mcastGroup.getLogicalPort().getPort();
            try
            {
                gmrMulticastAddress = stream.getMulticastAddress().getAddress();
                gmrSourceStreamName = streamName;
                gmrSourecMCastGrp = mcastGroup;
                log.info( sb.toString() );
            }
            catch ( Exception e )
            {
                log.error( "GMRTSC.getMulticastInfo : Exception ", e );
            }
        }
    }

}
