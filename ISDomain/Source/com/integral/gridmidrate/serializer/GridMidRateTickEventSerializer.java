package com.integral.gridmidrate.serializer;

import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXRateBasis;
import com.integral.gridmidrate.GridMidRateTickEvent;
import com.integral.gridmidrate.config.GridMidRateTickerConfig;
import com.integral.gridmidrate.startup.GridMidRateTickerStartupC;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.message.MarketRate;
import com.integral.is.message.MarketRateC;
import com.integral.is.message.MarketRateSerializer;
import com.integral.is.message.MarketRateSerializerFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.math.MathUtil;
import com.integral.ticker.TickEventSerializer;
import com.integral.transport.multicast.MulticastAddress;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.nio.ByteBuffer;

/**
 * Created by pranabdas on 11/26/14.
 */
public class GridMidRateTickEventSerializer implements TickEventSerializer<ByteBuffer, GridMidRateTickEvent>{

    private static final String UTF8 = "UTF-8";
    private GridMidRateTickerConfig config;
    private MarketRateSerializerFactory serializerFactory = MarketRateSerializerFactory.instance();
    private static final Log log = LogFactory.getLog( GridMidRateTickEventSerializer.class );

    public GridMidRateTickEventSerializer( GridMidRateTickerConfig config ) {
        this.config = config;
    }

    public ByteBuffer serialize( ByteBuffer byteBuffer, GridMidRateTickEvent tickEvent ) {
        byte[] currPairBytes;
        byte[] guidBytes;
        try {
            currPairBytes = tickEvent.getCurrencyPair().getBytes( UTF8 );
            guidBytes = tickEvent.getGuid().getBytes( UTF8 );
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("UTF-8 Encoding Unsupported", e);
        }
        byteBuffer.putInt( tickEvent.getIndex() );
        byteBuffer.putInt( currPairBytes.length );
        byteBuffer.put( currPairBytes );
        byteBuffer.putDouble( tickEvent.getRate() );
        byteBuffer.putInt( guidBytes.length );
        byteBuffer.put( guidBytes );
        byteBuffer.putLong( tickEvent.getTimestamp() );
        return byteBuffer;
    }

    public GridMidRateTickEvent deserialize( ByteBuffer buffer ) {
        GridMidRateTickEvent event = new GridMidRateTickEvent();
        try{
            byte[] packet = buffer.array();
            int offset = buffer.position();
            MarketRateSerializer serializer = serializerFactory.getSerializerForVersion( buffer.get() );
            int orgIndex = serializer.getProviderIndex( packet, offset );
            int streamIndex = serializer.getStreamIndex( packet, offset );
            if( orgIndex != GridMidRateTickerStartupC.gmrSourceAdaptorIndex ){
                if( log.isDebugEnabled() ){
                    log.debug( "GMRTE.deserialize : expected orgIndex=" + GridMidRateTickerStartupC.gmrSourceAdaptorIndex + ", received=" + orgIndex );
                }
                return null;
            }
            // once the providerIndex matches, the multicastgroup will be same.
            MulticastAddress addr = GridMidRateTickerStartupC.gmrSourecMCastGrp.getMulticastAddressForIndex( streamIndex );
            if (addr == null) {
                if( log.isDebugEnabled() ){
                    log.debug("GMRTE.deserialize : No mcast address configured for stream with index: " + streamIndex + " for org index: " + orgIndex );
                }
                return null;
            }

            if(  !addr.getStream().getShortName().equals( GridMidRateTickerStartupC.gmrSourceStreamName ) ){
                if( log.isDebugEnabled() ){
                    log.debug( "GMRTE.deserialize : expected stream=" + GridMidRateTickerStartupC.gmrSourceStreamName + ", received=" + addr.getStream().getShortName() );
                }
                return null;
            }

            MarketRate rate = new MarketRateC( 0, 0 );
            serializer.deserialize( buffer, rate );
            if( rate.isStale() ){
                if( log.isDebugEnabled() ){
                    log.debug( "FXBTE.deserialize : rate is stale, so dropping it for " + rate.getQuoteId() );
                }
                return null;
            }
            event.setCurrencyPair( CurrencyFactory.getCurrencyPairName( rate.getBaseCcy(), rate.getVariableCcy() ) );
            event.setIndex( config.getBufferKeyIndex( event.getCurrencyPair() ) );
            final FXRateBasis fxRateBasis = QuoteConventionUtilC.getInstance().getDefault().getFXRateBasis( CurrencyFactory.getCurrency( rate.getBaseCcy() ), CurrencyFactory.getCurrency( rate.getVariableCcy() ) );
            final double bidRate = rate.getBidTierSize() != 0 ? rate.getBidRate() : 0.0;
            final double offerRate = rate.getOfferTierSize() != 0 ? rate.getOfferRate() : 0.0;
            event.setRate( MathUtil.round( ( bidRate == 0.0 || offerRate == 0.0 ? bidRate + offerRate : ( bidRate + offerRate ) / 2.0 ) , fxRateBasis.getSpotPrecision(), BigDecimal.ROUND_HALF_UP) );
            //event.setRate( MathUtil.round( ( ( rate.getBidTierSize() != 0 ? rate.getBidRate() : 0.0 ) + ( rate.getOfferTierSize() != 0 ? rate.getOfferRate() : 0.0 ) ) / 2.0 , fxRateBasis.getForwardRatePrecision(), fxRateBasis.getRoundingType() ) );
            event.setGuid( rate.getQuoteId() );
            event.setTimestamp( rate.getRateEffective() );
        }
        catch ( Exception ex )
        {
            log.error( "GridMidRateTickEventSerializer.de-serialize : error de-serializing grid mid rate.", ex );
            return null;
        }
        return event;
    }


    private String getString( final byte[] bytes, String encoding ) {
        try {
            String str = new String( bytes, encoding );
            return str;
        } catch (UnsupportedEncodingException e) {
            //This should not happen.
            throw new RuntimeException("UTF-8 Encoding Unsupported", e);
        }

    }

}
