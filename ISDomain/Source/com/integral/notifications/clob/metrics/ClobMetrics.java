package com.integral.notifications.clob.metrics;

public class ClobMetrics {

	

	//private String eventType;
	private String et;
	//CorrelationId
	private String cId;
	//GateWayArrivalTime
	private long gE;
	//MV Server sent Time/(Order Enqueued on Rabbit)
	private long mvS;
	//Order Arrived on MVPS server
	private long oEq;
	//Order Deserialized
	private long oDs;
	//OrderEventSubmitTime
	private long oSt;
	//Order Match Event Start
	private long mEs;
	//Order Match Event End
	private long mEe;
	//Order Match Event End
	private long tcS;
	//Trade Creation End
	private long tcE;
	//Cover Creation Start
	private long ccS;
	//Cover Creation End
	private long ccE;
	//Credit Start
	private long cS;
	//Credit End
	private long cE;
	//Ack Sent
	private long aS;

	
	
	
	
	public String getEventType() {
		return et;
	}
	public void setEventType(String eventType) {
		this.et = eventType;
	}
	public String getOrderId() {
		return cId;
	}
	public void setOrderId(String orderId) {
		this.cId = orderId;
	}

	public long getOrderEventSubmitTime() {
		return oSt;
	}
	public void setOrderEventSubmitTime(long orderEventSubmitTime) {
		this.oSt = orderEventSubmitTime;
	}
	public long getOrderMatchEventStart() {
		return mEs;
	}
	public void setOrderMatchEventStart(long orderMatchEventStart) {
		this.mEs = orderMatchEventStart;
	}
	public long getOrderMatchEventEnd() {
		return mEe;
	}
	public void setOrderMatchEventEnd(long orderMatchEventEnd) {
		this.mEe = orderMatchEventEnd;
	}
	public long getTradecreationStart() {
		return tcS;
	}
	public void setTradecreationStart(long tradecreationStart) {
		this.tcS = tradecreationStart;
	}
	public long getTradecreationEnd() {
		return tcE;
	}
	public void setTradecreationEnd(long tradecreationEnd) {
		this.tcE = tradecreationEnd;
	}

	public long getCovercreationStart() {
		return ccS;
	}
	public void setCovercreationStart(long covercreationStart) {
		this.ccS = covercreationStart;
	}
	public long getCovercreationEnd() {
		return ccE;
	}
	public void setCovercreationEnd(long covercreationEnd) {
		this.ccE = covercreationEnd;
	}
	public long getCreditStart() {
		return cS;
	}
	public long getOrderDeserialized() {
		return oDs;
	}
	public void setOrderDeserialized(long orderDeserialized) {
		this.oDs = orderDeserialized;
	}
	public void setCreditStart(long creditStart) {
		this.cS = creditStart;
	}
	public long getAckSent() {
		return aS;
	}
	public void setAckSent(long ackSent) {
		this.aS = ackSent;
	}
	public long getCreditEnd() {
		return cE;
	}
	public void setCreditEnd(long creditEnd) {
		this.cE = creditEnd;
	}
	public long getOrderEnqued() {
		return oEq;
	}
	public void setOrderEnqued(long orderEnqued) {
		this.oEq = orderEnqued;
	}
	
	public long getGateWayArrivaleTime() {
		return gE;
	}
	public void setGateWayArrivalTime(long gE) {
		this.gE = gE;
	}
	public long getServerSentTime() {
		return mvS;
	}
	public void setServerSentTime(long mvS) {
		this.mvS = mvS;
	}
	
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("ClobMetrics [et=");
		builder.append(et);
		builder.append(", cId=");
		builder.append(cId);
		builder.append(", gE=");
		builder.append(gE);
		builder.append(", mvS=");
		builder.append(mvS);
		builder.append(", oEq=");
		builder.append(oEq);
		builder.append(", oDs=");
		builder.append(oDs);
		builder.append(", oSt=");
		builder.append(oSt);
		builder.append(", mEs=");
		builder.append(mEs);
		builder.append(", mEe=");
		builder.append(mEe);
		builder.append(", tcS=");
		builder.append(tcS);
		builder.append(", tcE=");
		builder.append(tcE);
		builder.append(", ccS=");
		builder.append(ccS);
		builder.append(", ccE=");
		builder.append(ccE);
		builder.append(", cS=");
		builder.append(cS);
		builder.append(", cE=");
		builder.append(cE);
		builder.append(", aS=");
		builder.append(aS);
		builder.append("]");
		return builder.toString();
	}
	
	
}
