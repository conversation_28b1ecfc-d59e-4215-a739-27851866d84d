package com.integral.notifications.clob.metrics;

import java.io.*;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class MetricsReader {

	static Map<Long,Map<String,Long>> theMap = new HashMap<Long,Map<String,Long>>();
	public static final long ORDER_CANCEL = 0;
	public static final long ORDER_FILL = 1;
	static final List<String> types = Arrays.asList(new String[]{"ORDER_FILL", "ORDER_EXPIRY", "ORDER_CANCEL"});
	static final String PROCESS_TIME = "pt";
	static final String CTRADE_TIME = "coverTradeTime";
	static final String TRADE_TIME = "tTime";
	static final String MATCH_EVENT_TIME = "mTime";
	static final String DESERIALIZED_TIME = "dStime";
	static final String ORDER_STORE_TIME = "oSt";
	static final String NOTIFICATIONS_TIME = "nT";
	static final double NANOS_IN_SEC = 1000000000.0;
	static final double NANOS_IN_MICROS = 1000.0;
	static final double NANOS_IN_MILLIS = 1000000.0;
	static final Map<String,String> stateNameMap = new HashMap<String,String>();
	
	static
	{
		stateNameMap.put(PROCESS_TIME, "Processing Time");
		stateNameMap.put(CTRADE_TIME, "Cover Trade And Match Workflow Time");
		stateNameMap.put(TRADE_TIME, "Trade Store Time");
		stateNameMap.put(MATCH_EVENT_TIME, "Match Event Store Time");
		stateNameMap.put(DESERIALIZED_TIME, "Deserialization Time");
		stateNameMap.put(ORDER_STORE_TIME, "Order Store Time");
		stateNameMap.put(NOTIFICATIONS_TIME, "Position And Credit Notifier Time");
	}
	
	public static void main(String[] args) throws IOException
	{
		File myFile = new File(args[0]);
		BufferedReader myRd = new BufferedReader(new InputStreamReader(new FileInputStream(myFile)));
		String myLine;
		
		while ((myLine = myRd.readLine()) != null)
		{
			String myStr = myLine.substring(myLine.indexOf("ClobMetrics"), myLine.length());
			myStr = myStr.replace("ClobMetrics", "");
			myStr = myStr.replace("[", "");
			myStr = myStr.replace("]", "");
			//System.out.println(myStr);
			String[] myArr = myStr.split(",");
			Map<String,Long> myMap = new HashMap<String,Long>();
			for(String myContent : myArr)
			{
				//System.out.println(myContent);
				String[] split = myContent.split("=");
				//System.out.println(split[0]);
				String myKey = split[0].trim();
				//System.out.println(myKey);
				if(myKey.equals("et"))
				myMap.put(myKey, Long.valueOf(types.indexOf(split[1])));
				else
				myMap.put(myKey, Long.valueOf(split[1]));
			}
			theMap.put(myMap.get("cId"), myMap);
			long processTime = myMap.get("aS") - myMap.get("oEq");
			long coverTrade = myMap.get("ccE") - myMap.get("ccS");
			long matchEvent = myMap.get("mEe") - myMap.get("mEs");
			long tradeCreation = myMap.get("tcE") - myMap.get("tcS");
			long notifications = myMap.get("cE") - myMap.get("cS");
			long deserialization = myMap.get("oSt") - myMap.get("oEq");
			myMap.put(PROCESS_TIME, processTime);
			myMap.put(CTRADE_TIME, coverTrade);
			myMap.put(TRADE_TIME, tradeCreation);
			myMap.put(MATCH_EVENT_TIME, matchEvent);
			myMap.put(DESERIALIZED_TIME, deserialization);
			myMap.put(NOTIFICATIONS_TIME, notifications);
		}
		
		printMetrics(PROCESS_TIME);
		printMetrics(CTRADE_TIME);
		printMetrics(TRADE_TIME);
		printMetrics(MATCH_EVENT_TIME);
		printMetrics(DESERIALIZED_TIME);
		printMetrics(NOTIFICATIONS_TIME);
	}
	
	static void printMetrics(String key)
	{
		System.out.println("Prining Statistics for :" + stateNameMap.get(key) + ",Nanos , Micros , Millis , Secs");
		long[] myVal = new long[theMap.size()];
		int i = 0;
		double avg = 0;
		double stDev = 0;
		for(Map<String,Long> myMap : theMap.values())
		{
			
			myVal[i++] = myMap.get(key);
			avg = (avg*(i-1) +  myVal[i-1])/i;
		}
		Arrays.sort(myVal);
		for(long myVals : myVal)
		{
			stDev += (myVals - avg)* (myVals - avg);
		}
		stDev = Math.sqrt(stDev/myVal.length);
		long median = myVal[theMap.size()/2];
		long p90 = myVal[theMap.size()*90/100];
		long p95 = myVal[theMap.size()*95/100];
		long p99 = myVal[theMap.size()*99/100];
		long p99_9 = myVal[theMap.size()*999/1000];
		long p99_99 = myVal[theMap.size()*9999/10000];
		System.out.println("Average :," + avg + "," + avg/NANOS_IN_MICROS + "," +  avg/NANOS_IN_MILLIS  + "," +  avg/NANOS_IN_SEC);
		System.out.println("Median :," + median+ "," + median/NANOS_IN_MICROS + "," +  median/NANOS_IN_MILLIS  + "," +  median/NANOS_IN_SEC);
		System.out.println("St Dev :," + stDev+ "," + stDev/NANOS_IN_MICROS + "," +  stDev/NANOS_IN_MILLIS  + "," +  stDev/NANOS_IN_SEC);
		System.out.println("p90 :," + p90+ "," + p90/NANOS_IN_MICROS + "," +  p90/NANOS_IN_MILLIS  + "," +  p90/NANOS_IN_SEC);
		System.out.println("p95 :," + p95+ "," + p95/NANOS_IN_MICROS + "," +  p95/NANOS_IN_MILLIS  + "," +  p95/NANOS_IN_SEC);
		System.out.println("p99 :," + p99+ "," + p99/NANOS_IN_MICROS + "," +  p99/NANOS_IN_MILLIS  + "," +  p99/NANOS_IN_SEC);
		System.out.println("p99_9 :," + p99_9+ "," + p99_9/NANOS_IN_MICROS + "," +  p99_9/NANOS_IN_MILLIS  + "," +  p99_9/NANOS_IN_SEC);
		System.out.println("p99_99 :," + p99_99+ "," + p99_99/NANOS_IN_MICROS + "," +  p99_99/NANOS_IN_MILLIS  + "," +  p99_99/NANOS_IN_SEC);
		
	}
	
	
}
