package com.integral.notifications.clob.serializer;

import com.integral.commons.buffers.UnSafeBuffer;

public class Brokerv4ClobOrderSerializer extends ClobOrderSerializer{
    @Override
    public void deserialize(ClobOrder fillResponse, UnSafeBuffer safeBuf) {
        safeBuf.get(); // msg type
        fillResponse.setVersion(safeBuf.get());
        super.deserialize(fillResponse, safeBuf);
    }
}
