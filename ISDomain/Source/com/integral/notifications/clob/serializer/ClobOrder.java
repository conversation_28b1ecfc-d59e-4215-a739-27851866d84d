package com.integral.notifications.clob.serializer;

import com.integral.broker.model.Stream;
import com.integral.broker.model.StreamC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.message.directed.orders.MsgType;
import com.integral.is.message.directed.orders.OrderStatus;
import com.integral.is.message.directed.orders.request.DOCMessage;
import com.integral.model.dealing.OrderRequest.RequestLeg.BuySellMode;
import com.integral.model.dealing.OrderRequest.Type;
import com.integral.model.dealing.TimeInForce;

import com.integral.notifications.clob.metrics.ClobMetrics;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.Arrays;
import java.util.Date;

public class ClobOrder  implements DOCMessage {

	public static final byte GATEWAY_ORIGIN_MAKER = (byte) 1;
	public static final byte GATEWAY_ORIGIN_TAKER = (byte) 2;
	public static final byte PROVIDER_REQUEST = (byte) 3;
	public static final byte BROKER_REQUEST = (byte) 4;
	public static final byte EMS_CHANNEL_CODE = (byte) 0;
	public static final byte BROKER_V4_EMS_CHANNEL_CODE = (byte) 5;
	public static final byte V4_EMS_CHANNEL_CODE = (byte) 6;
	public static final String MAKER_GATEWAY = "FIX/Order/Stream";
	public static final String NORMAL_GATEWAY ="FIX/Order/Stream";
	public static final String PROVIDER_CHANNEL ="FIX/Order/ME";

	public static final String BROKER_CHANNEL ="FIX/Order/ME";
	public static final String TAKER_GATEWAY = "FIX/Order/ME";

	public static final String BROKER_V4_EMS = "BrokerV4EMS";

	public static final String V4_EMS = "V4EMS";
	public static final String EMS = "EMS";
	public static final byte EVENT_TYPE_ORDER_UNSOLICITED_CANCEL = (byte) 1;
    public static final byte EVENT_TYPE_ORDER_FILL = (byte) 3;
	public static final byte EVENT_TYPE_ORDER_CANCEL_SUCCESS = (byte) 6;
	public static final byte EVENT_TYPE_ORDER_EXPIRY = (byte) 8;
	public static final byte EVENT_TYPE_ORDER_REPLACED = (byte) 7;
	public static final byte EVENT_TYPE_DONT_KNOW_REPONSE = (byte)21;
	public static final byte EVENT_TYPE_NEW_ORDER = (byte)22;
	public static final byte MULTI_QUOTE = (byte)0;
	public static final byte MULTI_TIER = (byte)1;

	
	private OrderResponseType responseType;

	/**
	 * OrderId. Generated by Venue. Each order gets a unique ID.
	 */
	private String orderId;

	/**
	 * {@link #orderId} for the Taker. Optional. Received only when a single trade is created
	 */
	private String origOrderId;

	/**
	 * Maps to ExternalOrderId.
	 */
	private String clOrdId;

	/**NUE
	 *
	 *
	 *
	 *
	 *  Not Used
	 */
	private String cpClOrdId;

	/**
	 * CounterpartyA LegalEntity
	 */
    private LegalEntity customerLE;

	/**
	 * W.r.t Base Currency.
	 */
	private BuySellMode side;

	/**
	 *
	 */
	private OrderStatus status;

	/**
	 * Amount filled so far
	 */
	private double cumQty;

	/**
	 * Not used in persistence workflow
	 */
	private double remainingQty;

	/**
	 * Set to true on last message sent by Venue for the order.
	 */
	private boolean terminal;

	/**
	 * Venue generated {@link #orderId} for the counterparty's order. Relevant in FILL messages.
	 */
	private String cptyOrderId;

	/**
	 * CounterpartyB LegalEntity for Maker Side Message Only. Not relevant for Taker
	 */
	private LegalEntity counterPartyLE = null;

	/**
	 * CounterpartyA for Maker. Not relevant for Taker
	 */
	private LegalEntity cptyClearingMemberLE = null;

	/**
	 * Indicates if the order is aggressor
	 */
	private boolean aggressor;

	/**
	 * Fill Amount
	 */
	private double fillQty;

	/**
	 * Venue generated ID for a FILL.
	 */
	private String fillId;

	/**
	 * Venue generated ID for the fill corresponding to the counterparty
	 */
	private String counterPartyFillId;

	/**
	 *
	 */
	private String fillTransId;
	private String cpFillTransId;

	private Date valueDate;
	private Date tradeDate;

	private double rate;
	private long timestamp;
	private Organization venue;
	public long[] timings;
	private transient boolean warmup;
	private boolean rateAvailable = true;
	private String rateId;
	private boolean manual;
	private CurrencyPair ccyPair;
	private int streamIdx;
	private double maxShow;
	private double orderQty;
	 /** counterParty B for  FI order*/
	 /** counterParty A's clearing Member for  FI order*/
	private LegalEntity customerCM;
	private Type orderType;
	private TimeInForce tif;
	private boolean makerVenue = false;
	private byte channelCode = EMS_CHANNEL_CODE;
	private String channel;
	private  boolean orderNLLPOrigin;
	private  boolean firstFill;
	/*
		true for maker orders
	 */
	private boolean isPQOrder;
	private double avgRate;
	private double fillRate;
	private byte providerRejectReason;
	private final ClobMetrics metrics = new ClobMetrics();
	private String providerExecId;
	private int streamIndex;
	private StreamC streamLE;
	private long quoteId;
	private byte quoteType;
	private LegalEntity originatingLE = null;
	private  byte origChannel;
	private long arrivalTime;
	private double refPrice;


	private double topOfBidBookPrice;
	private double topOfOfferBookPrice;
	private Organization topOfBidBookProvider;



	private Organization topOfOfferBookProvider;
	
	/** self clearing changes*/
	private boolean isTakerSelfClearing;
	private boolean isMakerSelfClearing;
	/** counterpartyC */
	private LegalEntity counterParty_C_LE = null;
	/** counterpartyD */
	private LegalEntity counterParty_D_LE = null;

	/**
	 * Central Clearing Party
	 */
	private LegalEntity takerCCPY = null;


	private LegalEntity makerCCPY = null;



	private byte coverTradeDisableStatus;
     
	private boolean isTakerCoverEnabled= true;

	public boolean isTakerCoverEnabled() {
		return isTakerCoverEnabled;
	}

	public void setTakerCoverEnabled(boolean takerCoverEnabled) {
		isTakerCoverEnabled = takerCoverEnabled;
	}

	public boolean isMakerCoverEnabled() {
		return isMakerCoverEnabled;
	}

	public void setMakerCoverEnabled(boolean makerCoverEnabled) {
		isMakerCoverEnabled = makerCoverEnabled;
	}

	private boolean isMakerCoverEnabled = true;

	public byte getVersion() {
		return version;
	}

	public void setVersion(byte version) {
		this.version = version;
	}

	private byte version ;

	@Override
	public byte getMsgType() {
		return MsgType.CLOB_ORDER;
	}

	@Override
	public boolean isWarmupObject() {
		return false;
	}

	@Override
	public void setWarmupObject(boolean warmup) {

	}

	public static enum OrderResponseType {
		    ORDER_FILL, ORDER_EXPIRY, ORDER_CANCEL,ORDER_DK,ORDER_NEW
		  }




	private byte providerType;

	/**
	 * Originating order avg Rate
	 */
	private double orgOrdAvgRate;



	/**
	 * Originating order cumulative Quantity
	 */
	private double orgOrdCumQty;



	/**
	 * Originating Order Client tag
	 */

	private double orgOrdQty;

	private String clientTag;

	private byte cptyVisibilityType;

	private double spreadPrice;

	private double cmOrderPrice;

	private double cmFillRate;

	private double cmAvgRate;


	private String orgClOrderId;

	private User user;



	private int clobOrderFlags;

	public ClobOrder() {
		
	}
	
	
	
	public boolean isOrderNLLPOrigin() {
		return orderNLLPOrigin;
	}


	public void setOrderNLLPOrigin(boolean orderSOGOrigin) {
		this.orderNLLPOrigin = orderSOGOrigin;
	}

	public void setFirstFill(boolean firstFill) {
		this.firstFill = firstFill;
	}
	
	
	public OrderResponseType getResponseType() {
		return responseType;
	}

	public void setResponseType(OrderResponseType responseType) {
		this.responseType = responseType;
	}

	public String  getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getClOrdId() {
		return clOrdId;
	}

    public String getCpClOrdId()
    {
        return cpClOrdId;
    }

    public void setCpClOrdId( String cpClOrdId )
    {
        this.cpClOrdId = cpClOrdId;
    }

    public void setClOrdId(String referenceOrderId) {
		this.clOrdId = referenceOrderId;
	}

	public LegalEntity getCustomerLE() {
		return customerLE;
	}

	public void setCustomerLE(LegalEntity customerLE) {
		this.customerLE = customerLE;
	}

	public BuySellMode getSide() {
		return side;
	}

	public void setSide(BuySellMode side) {
		this.side = side;
	}

	public byte getChannelCode() {
		return channelCode;
	}



	public void setChannelCode(byte isGateWay) {
		this.channelCode = isGateWay;
	}



	public OrderStatus getStatus() {
		return status;
	}

	public byte getCptyVisibilityType() {
		return cptyVisibilityType;
	}

	public void setCptyVisibilityType(byte cptyVisibilityType) {
		this.cptyVisibilityType = cptyVisibilityType;
	}


	@Override
	public String toString() {
		final StringBuilder sb = getStringBuilder();
		sb.append("ClobOrder[");
		sb.append("responseType: ").append(responseType);
		sb.append(", orderId: \"").append(orderId).append('\"');
		sb.append(", origOrderId: \"").append(origOrderId).append('\"');
		sb.append(", clOrdId: \"").append(clOrdId).append('\"');
		sb.append(", cpClOrdId: \"").append(cpClOrdId).append('\"');
		sb.append(", customerLE: ").append(toString(customerLE));
		sb.append(", side: ").append(side);
		sb.append(", status: ").append(status);
		sb.append(", cumQty: ").append(cumQty);
		sb.append(", remainingQty: ").append(remainingQty);
		sb.append(", terminal: ").append(terminal);
		sb.append(", cptyOrderId: \"").append(cptyOrderId).append('\"');
		sb.append(", counterPartyLE: ").append(toString(counterPartyLE));
		sb.append(", cptyClearingMemberLE: ").append(toString(cptyClearingMemberLE));
		sb.append(", aggressor: ").append(aggressor);
		sb.append(", fillQty: ").append(fillQty);
		sb.append(", fillId: \"").append(fillId).append('\"');
		sb.append(", counterPartyFillId: \"").append(counterPartyFillId).append('\"');
		sb.append(", fillTransId: \"").append(fillTransId).append('\"');
		sb.append(", cpFillTransId: \"").append(cpFillTransId).append('\"');
		sb.append(", valueDate: ").append(toString(valueDate));
		sb.append(", tradeDate: ").append(toString(tradeDate));
		sb.append(", rate: ").append(rate);
		sb.append(", timestamp: ").append(timestamp);
		sb.append(", venueCode: ").append(toString(venue));
		sb.append(", timings: ").append(Arrays.toString(timings));
		sb.append(", warmup: ").append(warmup);
		sb.append(", rateAvailable: ").append(rateAvailable);
		sb.append(", rateId: \"").append(rateId).append('\"');
		sb.append(", manual: ").append(manual);
		sb.append(", ccyPairIdx: ").append(toString(ccyPair));
		sb.append(", streamIdx: ").append(streamIdx);
		sb.append(", maxShow: ").append(maxShow);
		sb.append(", orderQty: ").append(orderQty);
		sb.append(", customerCM: ").append(toString(customerCM));
		sb.append(", orderType: ").append(orderType);
		sb.append(", tif: ").append(tif);
		sb.append(", makerVenue: ").append(makerVenue);
		sb.append(", channelCode: ").append(channelCode);
		sb.append(", channel: \"").append(channel).append('\"');
		sb.append(", orderNLLPOrigin: ").append(orderNLLPOrigin);
		sb.append(", firstFill: ").append(firstFill);
		sb.append(", isPQOrder: ").append(isPQOrder);
		sb.append(", avgRate: ").append(avgRate);
		sb.append(", fillRate: ").append(fillRate);
		sb.append(", providerRejectReason: ").append(providerRejectReason);
		sb.append(", metrics: ").append(metrics);
		sb.append(", providerExecId: \"").append(providerExecId).append('\"');
		sb.append(", streamIndex: ").append(streamIndex);
		sb.append(", streamLE: ").append(toString(streamLE));
		sb.append(", quoteId: ").append(quoteId);
		sb.append(", quoteType: ").append(quoteType);
		sb.append(", originatingLE: ").append(toString(originatingLE));
		sb.append(", origChannel: ").append(origChannel);
		sb.append(", arrivalTime: ").append(arrivalTime);
		sb.append(", refPrice: ").append(refPrice);
		sb.append(", topOfBidBookPrice: ").append(topOfBidBookPrice);
		sb.append(", topOfOfferBookPrice: ").append(topOfOfferBookPrice);
		sb.append(", topOfBidBookProvider: ").append(toString(topOfBidBookProvider));
		sb.append(", isTakerSelfClearing: ").append(isTakerSelfClearing);
		sb.append(", isMakerSelfClearing: ").append(isMakerSelfClearing);
		sb.append(", counterParty_C_LE: ").append(toString(counterParty_C_LE));
		sb.append(", counterParty_D_LE: ").append(toString(counterParty_D_LE));
		sb.append(", takerCCPY: ").append(toString(takerCCPY));
		sb.append(", makerCCPY: ").append(toString(makerCCPY));
		sb.append(", isTakerCoverEnabled: ").append(isTakerCoverEnabled);
		sb.append(", isMakerCoverEnabled: ").append(isMakerCoverEnabled);
		sb.append(", isPQFillInvolved: ").append(providerType);
		sb.append(", orgOrdAvgRate: ").append(orgOrdAvgRate);
		sb.append(", orgOrdCumQty: ").append(orgOrdCumQty);
		sb.append(", clientTag: ").append(clientTag);
		sb.append(", cptyVisibilityType: ").append(cptyVisibilityType);
		sb.append(", cmOrderPrice: ").append(cmOrderPrice);
		sb.append(", spreadPrice: ").append(spreadPrice);
		sb.append(", cmFillRate: ").append(cmFillRate);
		sb.append(", cmAvgRate: ").append(cmAvgRate);
		sb.append(", version: ").append(version);
		sb.append(", user: ").append(user);
		sb.append(", orgOrdQty: ").append(orgOrdQty);
		sb.append(", orgClOrderId: ").append(orgClOrderId);
		sb.append(", isTermCCYOrder: ").append(isTermCCYOrder());
		sb.append(']');
		return sb.toString();
	}

	private String toString(Stream stream) {
		return stream == null ? null : stream.getFullyQualifiedName();
	}

	private String toString(CurrencyPair ccyPair) {
		return ccyPair == null ? null : ccyPair.getName();
	}

	private String toString(Organization organization) {
		return organization == null ? null : organization.getShortName();
	}

	private String toString(LegalEntity legalEntity) {
		return legalEntity == null ? null : legalEntity.getFullyQualifiedName();
	}

	private String toString(Date date) {
		return date == null ? null : date.toString();
	}

	public ClobMetrics getMetrics() {
		return metrics;
	}

	public void setStatus(OrderStatus status) {
		this.status = status;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public double getCumQty() {
		return cumQty;
	}

	public void setCumQty(double cumQty) {
		this.cumQty = cumQty;
	}

	public double getRemainingQty() {
		return remainingQty;
	}

	public void setRemainingQty(double remainingQty) {
		this.remainingQty = remainingQty;
	}

	public boolean isTerminal() {
		return terminal;
	}

	public LegalEntity getCustomerCM() {
		return customerCM;
	}

	public void setCustomerCM(LegalEntity customerCM) {
		this.customerCM = customerCM;
	}

	public void setTerminal(boolean terminal) {
		this.terminal = terminal;
	}

	public String getCptyOrderId() {
		return cptyOrderId;
	}

	public void setCptyOrderId(String cptyOrderId) {
		this.cptyOrderId = cptyOrderId;
	}

	public LegalEntity getCounterPartyLE() {
		return counterPartyLE;
	}

	public void setCounterPartyLE(LegalEntity counterPartyLE) {
		this.counterPartyLE = counterPartyLE;
	}

	public LegalEntity getCptyClearingMemberLE() {
		return cptyClearingMemberLE;
	}

	public void setCptyClearingMemberLE(LegalEntity cptyClearingMemberLE) {
		this.cptyClearingMemberLE = cptyClearingMemberLE;
	}

	public boolean isAggressor() {
		return aggressor;
	}

	public void setAggressor(boolean aggressor) {
		this.aggressor = aggressor;
	}

	public double getFillQty() {
		return fillQty;
	}

	public void setFillQty(double fillQty) {
		this.fillQty = fillQty;
	}

	public String getFillId() {
		return fillId;
	}

	public void setFillId(String fillId) {
		this.fillId = fillId;
	}

	public String getCounterPartyFillId() {
		return counterPartyFillId;
	}

	public boolean isMakerVenue() {
		return makerVenue;
	}

	public void setMakerVenue(boolean makerVenue) {
		this.makerVenue = makerVenue;
	}

	public void setCounterPartyFillId(String counterPartyFillId) {
		this.counterPartyFillId = counterPartyFillId;
	}

	public Date getValueDate() {
		return valueDate;
	}

	public void setValueDate(Date valueDate) {
		this.valueDate = valueDate;
	}

	public double getRate() {
		return rate;
	}

	public void setRate(double rate) {
		this.rate = rate;
		setCmOrderPrice(rate);
	}

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public Organization getVenue() {
		return venue;
	}

	public void setVenue(Organization venue) {
		this.venue = venue;
	}

	public long[] getTimings() {
		return timings;
	}

	public void setTimings(long[] timings) {
		this.timings = timings;
	}

	public boolean isWarmup() {
		return warmup;
	}

	public void setWarmup(boolean warmup) {
		this.warmup = warmup;
	}

	public boolean isRateAvailable() {
		return rateAvailable;
	}

	public void setRateAvailable(boolean rateAvailable) {
		this.rateAvailable = rateAvailable;
	}

	public String getRateId() {
		return rateId;
	}

	public void setRateId(String rateId) {
		this.rateId = rateId;
	}

	public boolean isManual() {
		return manual;
	}

	public void setManual(boolean manual) {
		this.manual = manual;
	}

	public CurrencyPair getCcyPair() {
		return ccyPair;
	}

	public void setCcyPair(CurrencyPair ccyPair) {
		this.ccyPair = ccyPair;
	}

	public int getStreamIdx() {
		return streamIdx;
	}

	public void setStreamIdx(int streamIdx) {
		this.streamIdx = streamIdx;
	}

	public double getMaxShow() {
		return maxShow;
	}

	public void setMaxShow(double maxShow) {
		this.maxShow = maxShow;
	}

	public double getOrderQty() {
		return orderQty;
	}

	public void setOrderQty(double orderQty) {
		this.orderQty = orderQty;
	}


	public Type getOrderType() {
		return orderType;
	}

	public void setOrderType(Type orderType) {
		this.orderType = orderType;
	}


	public TimeInForce getTif() {
		return tif;
	}


	public void setTif(TimeInForce tif) {
		this.tif = tif;
	}

	public boolean isFirstFill() {
		return firstFill;
	}

	public String getFillTransId() {
		return fillTransId;
	}

	public void setFillTransId(String fillTransId) {
		this.fillTransId = fillTransId;
	}

	public String getCpFillTransId() {
		return cpFillTransId;
	}

	public void setCpFillTransId(String cpFillTransId) {
		this.cpFillTransId = cpFillTransId;
	}



	public Date getTradeDate() {
		return tradeDate;
	}



	public void setTradeDate(Date tradeDate) {
		this.tradeDate = tradeDate;
	}



	public double getAvgRate() {
		return avgRate;
	}



	public void setAvgRate(double avgRate) {
		this.avgRate = avgRate;
	}



	public double getFillRate() {
		return fillRate;
	}



	public void setFillRate(double fillRate) {
		this.fillRate = fillRate;
		setCmFillRate(fillRate);
	}



	public String getOrigOrderId() {
		return origOrderId;
	}



	public void setOrigOrderId(String origOrderId) {
		this.origOrderId = origOrderId;
	}



	public boolean isPQOrder() {
		return isPQOrder;
	}



	public void setPQOrder(boolean isPQOrder) {
		this.isPQOrder = isPQOrder;
	}



	public byte getProviderRejectReason() {
		return providerRejectReason;
	}



	public void setProviderRejectReason(byte providerRejectReason) {
		this.providerRejectReason = providerRejectReason;
	}



	public String getProviderExecId() {
		return providerExecId;
	}



	public void setProviderExecId(String providerExecId) {
		this.providerExecId = providerExecId;
	}



	public int getStreamIndex() {
		return streamIndex;
	}



	public void setStreamIndex(int streamIndex) {
		this.streamIndex = streamIndex;
	}



	public StreamC getStreamLE() {
		return streamLE;
	}



	public void setStreamLE(StreamC streamLE) {
		this.streamLE = streamLE;
	}



	public long getQuoteId() {
		return quoteId;
	}



	public void setQuoteId(long quoteId) {
		this.quoteId = quoteId;
	}



	public byte getQuoteType() {
		return quoteType;
	}



	public void setQuoteType(byte quoteType) {
		this.quoteType = quoteType;
	}

	public LegalEntity getOriginatingLE() {
		return originatingLE;
	}

	public void setOriginatingLE( LegalEntity originatingLE ) { this.originatingLE = originatingLE; }

	public byte getOrigChannel() { return origChannel; }

	public void setOrigChannel( byte origChannel ) { this.origChannel = origChannel; }
	public void setRefPrice(double refPrice) {
		this.refPrice = refPrice;
	}
	public double getRefPrice() {
		return refPrice;
	}

	public long getArrivalTime() {
		return arrivalTime;
	}

	public void setArrivalTime(long arrivalTime) {
		this.arrivalTime = arrivalTime;
	}
	public double getTopOfOfferBookPrice() {
		return topOfOfferBookPrice;
	}

	public void setTopOfOfferBookPrice(double topOfOfferBookPrice) {
		this.topOfOfferBookPrice = topOfOfferBookPrice;
	}
	public double getTopOfBidBookPrice() {
		return topOfBidBookPrice;
	}

	public void setTopOfBidBookPrice(double topOfBidBookPrice) {
		this.topOfBidBookPrice = topOfBidBookPrice;
	}
	  
	  public Organization getTopOfBidBookProvider() {
		return topOfBidBookProvider;
	}

	  public boolean isTakerSelfClearing() {
			return isTakerSelfClearing;
		}



		public void setTakerSelfClearing(boolean isTakerSelfClearing) {
			this.isTakerSelfClearing = isTakerSelfClearing;
		}



		public boolean isMakerSelfClearing() {
			return isMakerSelfClearing;
		}



		public void setMakerSelfClearing(boolean isMakerSelfClearing) {
			this.isMakerSelfClearing = isMakerSelfClearing;
		}


		public LegalEntity getCounterParty_C_LE() {
			return counterParty_C_LE;
		}



		public void setCounterParty_C_LE(LegalEntity counterParty_C_LE) {
			this.counterParty_C_LE = counterParty_C_LE;
		}



		public LegalEntity getCounterParty_D_LE() {
			return counterParty_D_LE;
		}



		public void setCounterParty_D_LE(LegalEntity counterParty_D_LE) {
			this.counterParty_D_LE = counterParty_D_LE;
		}


		
		public LegalEntity getTakerCCPY() {
			return takerCCPY;
		}



		public void setTakerCCPY(LegalEntity takerCCPY) {
			this.takerCCPY = takerCCPY;
		}



		public LegalEntity getMakerCCPY() {
			return makerCCPY;
		}



		public void setMakerCCPY(LegalEntity makerCCPY) {
			this.makerCCPY = makerCCPY;
		}

	public void setTopOfBidBookProvider(Organization topOfBidBookProvider) {
		this.topOfBidBookProvider = topOfBidBookProvider;
	}

	private static final ThreadLocal<StringBuilder> localStringBuilder = new ThreadLocal<StringBuilder>();
	public byte getCoverTradeDisableStatus() {
		return coverTradeDisableStatus;
	}

	public void setCoverTradeDisableStatus(byte coverTradeDisableStatus) {
		this.coverTradeDisableStatus = coverTradeDisableStatus;
	}
	public boolean isPQFillInvolved() {

		 if(providerType==ProviderType.PQ_PROVIDER){
		 	return true;
		 }
		 return false;
	}
	public byte getProviderType() {
		return providerType;
	}

	public void setProviderType(byte providerType) {
		this.providerType = providerType;
	}


	public double getOrgOrdAvgRate() {
		return orgOrdAvgRate;
	}

	public void setOrgOrdAvgRate(double orgOrdAvgRate) {
		this.orgOrdAvgRate = orgOrdAvgRate;
	}

	public double getOrgOrdCumQty() {
		return orgOrdCumQty;
	}

	public void setOrgOrdCumQty(double orgOrdCumQty) {
		this.orgOrdCumQty = orgOrdCumQty;
	}
	public double getCmOrderPrice() {
		return cmOrderPrice;
	}

	public void setCmOrderPrice(double cmOrderPrice) {
		this.cmOrderPrice = cmOrderPrice;
	}
	public double getCmFillRate() {
		return cmFillRate;
	}

	public void setCmFillRate(double cmFillRate) {
		this.cmFillRate = cmFillRate;
	}

	public String getClientTag() {
		return clientTag;
	}
	public double getSpreadPrice() {
		return spreadPrice;
	}

	public void setSpreadPrice(double spreadPrice) {
		this.spreadPrice = spreadPrice;
	}

	public double getCmAvgRate() {
		return cmAvgRate;
	}

	public void setCmAvgRate(double cmAvgRate) {
		this.cmAvgRate = cmAvgRate;
	}

	public void setClientTag(String clientTag) {
		this.clientTag = clientTag;
	}
	protected StringBuilder getStringBuilder() {
		StringBuilder sb = localStringBuilder.get();
		if( sb == null ){
			sb = new StringBuilder(1024);
			localStringBuilder.set(sb);
		}
		sb.setLength(0);
		return sb;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}
	public Organization getTopOfOfferBookProvider() {
		return topOfOfferBookProvider;
	}

	public void setTopOfOfferBookProvider(Organization topOfOfferBookProvider) {
		this.topOfOfferBookProvider = topOfOfferBookProvider;
	}

	public double getOrgOrdQty() {
		return orgOrdQty;
	}

	public void setOrgOrdQty(double orgOrdQty) {
		this.orgOrdQty = orgOrdQty;
	}
	public String getOrgClOrderId() {
		return orgClOrderId;
	}

	public void setOrgClOrderId(String orgClOrderId) {
		this.orgClOrderId = orgClOrderId;
	}

	private boolean isFlagEnabled( int flag ) {
		return ( clobOrderFlags & flag ) == flag;
	}

	private void enableFlag( int flag ) {
		clobOrderFlags = clobOrderFlags | flag;
	}

	private void disableFlag( int flag ) {
		clobOrderFlags = clobOrderFlags & ~flag;
	}
	public boolean isTermCCYOrder(){
		return isFlagEnabled(ClobOrderFlags.TERM_CCY_ORDER);
	}


	public void setTermCCYOrder(boolean isTermCCYOrder){
		if ( isTermCCYOrder ) {
			enableFlag( ClobOrderFlags.TERM_CCY_ORDER);
		}
		else {
			disableFlag( ClobOrderFlags.TERM_CCY_ORDER);
		}
	}
	public int getClobOrderFlags() {
		return clobOrderFlags;
	}

	public void setClobOrderFlags(int clobOrderFlags) {
		this.clobOrderFlags = clobOrderFlags;
	}
	public static interface ClobOrderFlags{
		int TERMINAL = 0x1;
		int AGGRESSOR = 0x2;
		int RATE_AVAILABLE = 0x4;
		int MANUAL = 0x8;
		int TAKER_SELFCLEARING = 0x10;
		int MAKER_SELFCLEARING = 0x20;
		int TERM_CCY_ORDER = 0x40;
	}
}
