package com.integral.notifications.clob.serializer;

import com.integral.admin.utils.tv.TradingVenueUtil;
import com.integral.broker.model.StreamC;
import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.finance.currency.CurrencyPairUtil;
import com.integral.is.message.directed.orders.serializer.MsgSerializer;

import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.OrderRequest.RequestLeg.BuySellMode;

import com.integral.notifications.clob.rmq.ClobListenerMBean;
import com.integral.notifications.clob.rmq.ClobListenerMBeanC;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.tradingvenue.TradingVenueClassificationEnums;
import com.integral.tradingvenue.TradingVenueEntity;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.Date;

public class ClobOrderSerializer  implements MsgSerializer<ClobOrder>{
	 public static final int INDEX_NA_INT = -1;
	  public static final int INDEX_NA_LONG = -1;
	  private static final long MILLIS_TO_DAY = 86400000l;
	public static final byte BOTH_COVER_ENABLED = (byte) 0;
	public static final byte TAKER_COVER_DISABLED = (byte) 1;
	public static final byte MAKER_COVER_DISABLED = (byte) 2;
	public static final byte BOTH_COVER_DISABLED = (byte) 3;
	  private final ClobListenerMBean bean = ClobListenerMBeanC.getInstance();
	  private static Log log = LogFactory.getLog( ClobOrderSerializer.class );
	  public byte[] serialize(ClobOrder order) {
		    UnSafeBuffer safeBuf = new UnSafeBuffer();
		    safeBuf.init(new byte[2048]);
		    //safeBuf.put(order.getEventType());
		    safeBuf.putInt(order.getVenue().getIndex());
		    safeBuf.putInt(order.getStreamIdx());
		    safeBuf.putInt(order.getCcyPair().getIndex());
		    safeBuf.putLong(order.getCustomerLE().getObjectID());
		    safeBuf.putLong(order.getCustomerCM().getObjectID());
		    safeBuf.putLong(Long.valueOf(order.getOrderId()));
		    safeBuf.putString(order.getClOrdId());
		    //safeBuf.put(order.getSide());
		    safeBuf.putDouble(order.getMaxShow());
		    safeBuf.putDouble(order.getOrderQty());
		    //safeBuf.putInt(order.getOrg());
		    if(order.getFillId()!=null){
		      safeBuf.putBoolean(true);
		      safeBuf.putString(order.getFillId());
		    }else{
		    	safeBuf.putBoolean(false);
		    }
		    if(order.getCounterPartyFillId()!=null){
		      safeBuf.putBoolean(true);
		      safeBuf.putString(order.getCounterPartyFillId());
		    }else{
		    	safeBuf.putBoolean(false);
		    }
		    
		    safeBuf.putDouble(order.getFillQty());
		    safeBuf.putDouble(order.getCumQty());
		    safeBuf.putDouble(order.getRemainingQty());
		    safeBuf.putBoolean(order.getRate() != 0);
		    if (order.getRate() != 0) {
		      safeBuf.putDouble(order.getRate());
		    }
		    //safeBuf.putLong(order.getRateId());
		    safeBuf.putLong(Long.valueOf(order.getCptyOrderId()));
		    //safeBuf.putLong(order.getCounterPartyLE());
		    //safeBuf.putLong(order.getCptyClearingMemberLE());
		    safeBuf.putBoolean(order.isAggressor());
		    safeBuf.putBoolean(order.isTerminal());
		    //safeBuf.put(order.getOrderType());
		    //safeBuf.put(order.getTif());
		    safeBuf.putLong(order.getTimestamp());
		    //safeBuf.putShort(order.getTradeDate());
		    //safeBuf.putShort(order.getValueDate());
		    byte no = (byte) order.getTimings().length;
		    safeBuf.put(no);
		    for (int i = 0; i < no; i++) {
		      safeBuf.putLong(order.getTimings()[i]);
		    }
		    //safeBuf.put(order.getIsGateWay());
		    safeBuf.putDouble(order.getAvgRate());
		    safeBuf.putDouble(order.getFillRate());
		    return safeBuf.array();
	  }




	public void deserialize(ClobOrder fillResponse,UnSafeBuffer safeBuf) {
		  try {
			  byte msgType = safeBuf.get();
			  switch (msgType) {
				  case ClobOrder.EVENT_TYPE_ORDER_FILL:
					  fillResponse.setResponseType(ClobOrder.OrderResponseType.ORDER_FILL);
					  break;
				  case ClobOrder.EVENT_TYPE_ORDER_EXPIRY:
					  fillResponse.setResponseType(ClobOrder.OrderResponseType.ORDER_EXPIRY);
					  break;
				  case ClobOrder.EVENT_TYPE_ORDER_CANCEL_SUCCESS:
					  fillResponse.setResponseType(ClobOrder.OrderResponseType.ORDER_CANCEL);
					  break;
				  case ClobOrder.EVENT_TYPE_ORDER_UNSOLICITED_CANCEL:
					  fillResponse.setResponseType(ClobOrder.OrderResponseType.ORDER_CANCEL);
				  case ClobOrder.EVENT_TYPE_ORDER_REPLACED:
					  fillResponse.setResponseType(ClobOrder.OrderResponseType.ORDER_CANCEL);
					  break;
				  case ClobOrder.EVENT_TYPE_DONT_KNOW_REPONSE:
					  fillResponse.setResponseType(ClobOrder.OrderResponseType.ORDER_DK);
					  break;
				  case ClobOrder.EVENT_TYPE_NEW_ORDER:
					  fillResponse.setResponseType(ClobOrder.OrderResponseType.ORDER_NEW);
					  break;
			  }
			  log.info(" clob Order msgType: " + msgType);
			  fillResponse.setVenue(getOrg(safeBuf.getInt())); // read venue index but it's not used currently
			  fillResponse.setStreamIdx(safeBuf.getInt()); // read stream index but it's not used currently
			  fillResponse.setCcyPair(getPair(safeBuf.getInt())); // read currency pair index but it's not used
			  fillResponse.setCustomerLE(getLegalEntity(safeBuf.getLong()));
			  fillResponse.setCustomerCM(getLegalEntity(safeBuf.getLong()));
			  fillResponse.setOrderId(Long.toString(safeBuf.getLong()));
			  fillResponse.setClOrdId(safeBuf.getString());
			  fillResponse.setSide(getSide(safeBuf.get()));
			  fillResponse.setMaxShow(safeBuf.getDouble());
			  fillResponse.setOrderQty(safeBuf.getDouble());
			  //Remove this Peice of code later Org is redundant
			  safeBuf.getInt();
			  boolean fillIdSet = safeBuf.getBoolean();
			  if (fillIdSet) {
				  fillResponse.setFillId(safeBuf.getString());
			  }
			  boolean cpfillIdSet = safeBuf.getBoolean();
			  if (cpfillIdSet) {
				  fillResponse.setCounterPartyFillId(safeBuf.getString());
			  }
			  fillResponse.setFillQty(safeBuf.getDouble());
			  fillResponse.setCumQty(safeBuf.getDouble());
			  fillResponse.setRemainingQty(safeBuf.getDouble());
			  fillResponse.setRateAvailable(safeBuf.getBoolean());
			  if (fillResponse.isRateAvailable()) {
				  fillResponse.setRate(safeBuf.getDouble());
			  }
			  fillResponse.setRateId(Long.toString(safeBuf.getLong()));
			  fillResponse.setCptyOrderId(Long.toString(safeBuf.getLong()));
			  fillResponse.setCounterPartyLE(getLegalEntity(safeBuf.getLong()));
			  long cpCMLE = safeBuf.getLong(); // In case of bilateral case the clearing member will not be
			  // present. REX is sending -1 in this case.
			  if (cpCMLE >= 0) {
				  fillResponse.setCptyClearingMemberLE(getLegalEntity(cpCMLE));
			  }
			  fillResponse.setAggressor(safeBuf.getBoolean());
			  fillResponse.setTerminal(safeBuf.getBoolean());
			  byte type = safeBuf.get();
			  fillResponse.setOrderType(OrderType.getType(type));
			  fillResponse.setPQOrder(type == OrderType.PQ);
			  fillResponse.setTif(Tif.get(safeBuf.get()));
			  fillResponse.setTimestamp(safeBuf.getLong());
			  fillResponse.setValueDate(getDate(safeBuf.getShort()));
			  fillResponse.setTradeDate(getDate(safeBuf.getShort()));
			  byte no = safeBuf.get();
			  long[] eventTimes = new long[no + 1];
			  for (int i = 0; i < no; i++) {
				  eventTimes[i] = safeBuf.getLong();
			  }
			  eventTimes[no] = System.currentTimeMillis();
			  fillResponse.setTimings(eventTimes);
			  fillResponse.setChannelCode(safeBuf.get());
			  fillResponse.setAvgRate(safeBuf.getDouble());
			  fillResponse.setFillRate(safeBuf.getDouble());
			  if (msgType == ClobOrder.EVENT_TYPE_ORDER_REPLACED || safeBuf.remaining() > 0) {
				  fillResponse.setOrigOrderId(Long.toString(safeBuf.getLong()));
			  }

			  fillResponse.setProviderRejectReason(safeBuf.get());
			  final boolean providerIdSet = safeBuf.getBoolean();
			  if (providerIdSet) {
				  fillResponse.setProviderExecId(safeBuf.getString());
			  }
			  fillResponse.setStreamIdx(safeBuf.getInt());
			  fillResponse.setQuoteId(safeBuf.getLong());
			  fillResponse.setQuoteType(safeBuf.get());

			  if (fillResponse.isPQOrder()) {
				  fillResponse.setStreamLE(getStream(fillResponse.getStreamIdx()));
				  fillResponse.setOrderId(fillResponse.getOrderId() + bean.getProviderOrderIdSuffix());

			  }

			  if (safeBuf.remaining() > 0) fillResponse.setOriginatingLE(getLegalEntity(safeBuf.getLong()));
			  if (safeBuf.remaining() > 0) fillResponse.setOrigChannel(safeBuf.get());

			  setOtherFields(fillResponse, fillResponse.getChannelCode());
			  fillResponse.getMetrics().setOrderDeserialized(System.nanoTime());
			  fillResponse.getMetrics().setOrderId(fillResponse.getOrderId());
			  fillResponse.getMetrics().setEventType(fillResponse.getResponseType().toString());

			  fillResponse.setRefPrice(safeBuf.getDouble());
			  fillResponse.setTopOfBidBookPrice(safeBuf.getDouble());
			  fillResponse.setTopOfOfferBookPrice(safeBuf.getDouble());
			  fillResponse.setArrivalTime(safeBuf.getLong());

			  if (fillResponse.getSide() == BuySellMode.BUY) {
				  fillResponse.setTopOfOfferBookProvider(getOrg(safeBuf.getInt()));
			  } else {
				  fillResponse.setTopOfBidBookProvider(getOrg(safeBuf.getInt()));
			  }

			  fillResponse.setOrgOrdQty(fillResponse.getOrderQty());
			  try {
				  //if new version, MVP decides the path based on clob type
				  if (bean.getClobOrderSerializerVersion() == 1) {
					  TradingVenueEntity tradingVenue = TradingVenueUtil.getTradingVenue(fillResponse.getVenue().getShortName());
					  if (tradingVenue.getTvType().equals(TradingVenueClassificationEnums.TvType.CLEARING_MEMBER_PLUS_CENTRAL_CLEARING)) {
						  fillResponse.setCounterParty_C_LE(getLegalEntity(safeBuf.getLong()));
						  fillResponse.setCounterParty_D_LE(getLegalEntity(safeBuf.getLong()));
						  fillResponse.setTakerSelfClearing(safeBuf.getBoolean());
						  fillResponse.setMakerSelfClearing(safeBuf.getBoolean());
						  fillResponse.setTakerCCPY(getLegalEntity(safeBuf.getLong()));
						  fillResponse.setMakerCCPY(getLegalEntity(safeBuf.getLong()));
						  byte coverTradeStatus = safeBuf.get();
						  if (coverTradeStatus == BOTH_COVER_DISABLED) {
							  fillResponse.setTakerCoverEnabled(false);
							  fillResponse.setMakerCoverEnabled(false);
						  }
						  if (coverTradeStatus == TAKER_COVER_DISABLED) {
							  fillResponse.setTakerCoverEnabled(false);
						  }
						  if (coverTradeStatus == MAKER_COVER_DISABLED) {
							  fillResponse.setMakerCoverEnabled(false);
						  }
						  fillResponse.setProviderType(safeBuf.get());
						  fillResponse.setOrgOrdAvgRate(safeBuf.getDouble());
						  fillResponse.setOrgOrdCumQty(safeBuf.getDouble());
						  fillResponse.setClientTag(safeBuf.getString());
						  fillResponse.setCptyVisibilityType(safeBuf.get());
						  byte version = safeBuf.get();
						  switch (version) {
							  case 2:
								  setVersion2Fields(safeBuf, fillResponse);
								  break;
							  case 3:
								  setVersion2Fields(safeBuf, fillResponse);
								  long userIndex = safeBuf.getLong();
								  log.info("User Index : " + userIndex);
								  if (userIndex > -1l) {
									  User user = (User) ReferenceDataCacheC.getInstance().getEntityByObjectId(userIndex, com.integral.user.UserC.class);
									  log.info("User : " + user);
									  if (user != null) {
										  fillResponse.setUser(user);
									  }
								  }
								  break;
							  case 4:
								  setVersion2Fields(safeBuf, fillResponse);
								  userIndex = safeBuf.getLong();

								  if (userIndex > -1l) {
									  User user = (User) ReferenceDataCacheC.getInstance().getEntityByObjectId(userIndex, com.integral.user.UserC.class);
									  log.info("User : " + user);
									  if (user != null) {
										  fillResponse.setUser(user);
									  }
								  }
								  if (fillResponse.getSide() == BuySellMode.BUY) {
									  fillResponse.setTopOfBidBookProvider(getOrg(safeBuf.getInt()));
								  } else {
									  fillResponse.setTopOfOfferBookProvider(getOrg(safeBuf.getInt()));
								  }
								  break;
							  case 5:
								  setVersion2Fields(safeBuf, fillResponse);
								  userIndex = safeBuf.getLong();

								  if (userIndex > -1l) {
									  User user = (User) ReferenceDataCacheC.getInstance().getEntityByObjectId(userIndex, com.integral.user.UserC.class);
									  log.info("User : " + user);
									  if (user != null) {
										  fillResponse.setUser(user);
									  }
								  }
								  if (fillResponse.getSide() == BuySellMode.BUY) {
									  fillResponse.setTopOfBidBookProvider(getOrg(safeBuf.getInt()));
								  } else {
									  fillResponse.setTopOfOfferBookProvider(getOrg(safeBuf.getInt()));
								  }
								  fillResponse.setOrgOrdQty(safeBuf.getDouble());
								  break;
							  case 6:
								  setVersion2Fields(safeBuf, fillResponse);
								  userIndex = safeBuf.getLong();

								  if (userIndex > -1l) {
									  User user = (User) ReferenceDataCacheC.getInstance().getEntityByObjectId(userIndex, com.integral.user.UserC.class);
									  log.info("User : " + user);
									  if (user != null) {
										  fillResponse.setUser(user);
									  }
								  }
								  if (fillResponse.getSide() == BuySellMode.BUY) {
									  fillResponse.setTopOfBidBookProvider(getOrg(safeBuf.getInt()));
								  } else {
									  fillResponse.setTopOfOfferBookProvider(getOrg(safeBuf.getInt()));
								  }
								  fillResponse.setOrgOrdQty(safeBuf.getDouble());
								  fillResponse.setOrgClOrderId(safeBuf.getString());
								  fillResponse.setClobOrderFlags(safeBuf.getInt());
								  break;
							  default:
								  fillResponse.setCmOrderPrice(fillResponse.getRate());
								  fillResponse.setCmFillRate(fillResponse.getFillRate());
								  fillResponse.setSpreadPrice(0);
								  fillResponse.setOrgOrdQty(fillResponse.getOrderQty());
								  version = 1;
						  }
						  fillResponse.setVersion(version);
					  }
				  }
			  } catch (Exception e) {
				  log.error("Exception deserializing cover trade self clearing changes : " + fillResponse, e);
			  }
		  }catch(Exception e){
			  log.error("Exception deserializing clobOrder : " + fillResponse);
		  }

	}

	@Override
	  public ClobOrder deserialize(byte[] data) {
	    ClobOrder fillResponse = new ClobOrder();
	    fillResponse.getMetrics().setOrderEnqued(System.nanoTime());
	    UnSafeBuffer safeBuf = new UnSafeBuffer();
	    safeBuf.init(data, 0, data.length);
		deserialize(fillResponse,safeBuf);
		return fillResponse;
	  }



	  private void setVersion2Fields( UnSafeBuffer safeBuf, ClobOrder fillResponse){
		  fillResponse.setCmOrderPrice(safeBuf.getDouble());
		  if(fillResponse.getCmOrderPrice()<=0.0D){
			  fillResponse.setCmOrderPrice(fillResponse.getRate());
		  }
		  fillResponse.setCmFillRate(safeBuf.getDouble());
		  fillResponse.setSpreadPrice(safeBuf.getDouble());
	  }
	  private Date getDate(short aTime)
	  {
		  long myDate = (long) aTime * MILLIS_TO_DAY;
		  return new Date(myDate);
	  }

	  private void setOtherFields(ClobOrder fillResponse , byte aGateWay)
	  {
		    boolean makerVenue = fillResponse.getVenue() != null ?  fillResponse.getVenue().isMakerVenue() : false;
		   	String channel = ClobOrder.EMS;
             if(aGateWay ==ClobOrder.GATEWAY_ORIGIN_MAKER  && makerVenue){
				 channel = ClobOrder.MAKER_GATEWAY;
			 }else if(aGateWay == ClobOrder.GATEWAY_ORIGIN_MAKER){
				 channel = ClobOrder.NORMAL_GATEWAY;
			 }else if(aGateWay == ClobOrder.PROVIDER_REQUEST || aGateWay == ClobOrder.BROKER_REQUEST){
				 channel = ClobOrder.PROVIDER_CHANNEL;
			 }else if(aGateWay == ClobOrder.BROKER_V4_EMS_CHANNEL_CODE){
				 channel = ClobOrder.BROKER_V4_EMS;
			 }else if(aGateWay == ClobOrder.V4_EMS_CHANNEL_CODE){
				 channel = ClobOrder.V4_EMS;
			 }

			fillResponse.setMakerVenue(makerVenue);
			fillResponse.setChannel(channel);
		    fillResponse.setOrderNLLPOrigin(aGateWay == ClobOrder.GATEWAY_ORIGIN_MAKER  && makerVenue) ;
			fillResponse.setFirstFill(fillResponse.getFillQty() == fillResponse.getCumQty());
	  	  	String myPrefix = fillResponse.isOrderNLLPOrigin() ? DealingModelUtil.getTransactionIdPrefix() : DealingModelUtil.getDOTransactionIdPrefix();
	  	  	String mySuffix = fillResponse.isOrderNLLPOrigin() ? "" : DealingModelUtil.getDOTransactionIdSuffix();
		    mySuffix = fillResponse.isPQOrder() ? bean.getProviderTransactionIdSuffix() : mySuffix;
	  	  	fillResponse.setFillTransId(myPrefix + fillResponse.getFillId() + mySuffix);
		    fillResponse.setCpFillTransId(myPrefix + fillResponse.getCounterPartyFillId() + mySuffix);
	  }
	
		private CurrencyPair getPair(int index)
		{
			    CurrencyPair myPair = CurrencyPairUtil.getCurrencyPairObject(index);
			    return myPair;
		}
	  
		private LegalEntity getLegalEntity(long anId)
		{
			if(anId < 0) return null;
			Object myObj = ReferenceDataCacheC.getInstance().getEntityByObjectId(anId,LegalEntity.class);
			return myObj == null ? null : (LegalEntity) myObj;
		}
		
		private StreamC getStream(long anId)
		{
			if(anId < 0) return null;
			Object myObj = null;
			if(bean.isSuperBankIndexLookUpEnabled()){
				myObj = ReferenceDataCacheC.getInstance().getSuperBankStreamByIndex((int)anId);
			}else{
				myObj = ReferenceDataCacheC.getInstance().getEntityByObjectId(anId,StreamC.class);
			}
			 
			return myObj == null ? null : (StreamC) myObj;
		}
		
		private BuySellMode getSide(byte aSide)
		{
			return aSide == (byte) 0 ? BuySellMode.BUY : BuySellMode.SELL;
		}
		private Organization getOrg(int anId)
		{
			return anId < 0 ? null : ReferenceDataCacheC.getInstance().getOrganization(anId);
		}
}
