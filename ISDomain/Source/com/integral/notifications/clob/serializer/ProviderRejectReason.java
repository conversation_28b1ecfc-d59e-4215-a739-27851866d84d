package com.integral.notifications.clob.serializer;

import com.integral.is.ISCommonConstants;

import java.util.HashMap;
import java.util.Map;

public class ProviderRejectReason {

	public static final byte PROVIDER_SUBMISSION_FAILED = 41;
	public static final byte PROVIDER_SUBMISSION_FAILED_PROVIDER_NOT_FOUND = 42;
	public static final byte PROVIDER_SUBMISSION_FAILED_UNSUPPORTED_STREAM = 43;
	public static final byte PROVIDER_SUBMISSION_FAILED_SUBMIT_EXCEPTION = 44;
	public static final byte VALIDATION_FAILED = 51;
	public static final byte VALIDATION_FAILED_ACCOUNTID = 52;
	public static final byte PQ_QUOTE_NOT_AVAILABLE = 55;
	public static final byte AUTOCANCEL_TIMED_OUT = 60;
	public static final byte REJECTED_BY_ADAPTOR = 61;
	public static final byte REJECTED_BY_PROVIDER = 65;
	public static final byte AUTOCANCELLED_TIMED_OUT_ME = 90;
	public static final byte MISSING_VALUE = -1;
	
	public static final String PROVIDER_SUBMISSION_FAILED_STR = "Provider Submission Failed";
	public static final String PROVIDER_SUBMISSION_FAILED_PROVIDER_NOT_FOUND_STR = "Provider Not Found at Adaptor";
	public static final String PROVIDER_SUBMISSION_FAILED_UNSUPPORTED_STREAM_STR = "Stream Not Supported";
	public static final String PROVIDER_SUBMISSION_FAILED_SUBMIT_EXCEPTION_STR = "Exception While Submitting Order";
	public static final String VALIDATION_FAILED_STR = "Validation Failed at Provider";
	public static final String VALIDATION_FAILED_ACCOUNTID_STR = "Account Id Validation Failed";
	public static final String PQ_QUOTE_NOT_AVAILABLE_STR = "PQ Quote not Available";
	public static final String AUTOCANCEL_TIMED_OUT_STR = "Time Out due to AutoCancellation";
	public static final String REJECTED_BY_PROVIDER_STR = "Rejected By Provider";
	public static final String REJECTED_BY_ADAPTOR_STR = "Rejected By Adaptor";
	
	
	public static final Map<Byte,String> reasonString;
	
	static{
		reasonString = new HashMap<Byte,String>();
		reasonString.put(MISSING_VALUE, ISCommonConstants.ACCEPTANCE_REJECTION_REASON_QUOTE_NOT_FOUND);
		reasonString.put(PROVIDER_SUBMISSION_FAILED, PROVIDER_SUBMISSION_FAILED_STR);
		reasonString.put(PROVIDER_SUBMISSION_FAILED_PROVIDER_NOT_FOUND ,PROVIDER_SUBMISSION_FAILED_PROVIDER_NOT_FOUND_STR);
		reasonString.put(PROVIDER_SUBMISSION_FAILED_UNSUPPORTED_STREAM,PROVIDER_SUBMISSION_FAILED_UNSUPPORTED_STREAM_STR );
		reasonString.put(PROVIDER_SUBMISSION_FAILED_SUBMIT_EXCEPTION,PROVIDER_SUBMISSION_FAILED_SUBMIT_EXCEPTION_STR  );
		reasonString.put(VALIDATION_FAILED,VALIDATION_FAILED_STR );
		reasonString.put(VALIDATION_FAILED_ACCOUNTID,VALIDATION_FAILED_ACCOUNTID_STR );
		reasonString.put(PQ_QUOTE_NOT_AVAILABLE,PQ_QUOTE_NOT_AVAILABLE_STR );
		reasonString.put(AUTOCANCEL_TIMED_OUT,AUTOCANCEL_TIMED_OUT_STR );
		reasonString.put(REJECTED_BY_PROVIDER,REJECTED_BY_PROVIDER_STR );
		reasonString.put(REJECTED_BY_ADAPTOR, REJECTED_BY_ADAPTOR_STR);
		reasonString.put(AUTOCANCELLED_TIMED_OUT_ME, AUTOCANCEL_TIMED_OUT_STR);
	}
	
	public static String getRejectReason(Byte aKey)
	{
		return reasonString.get(aKey);
	}

}
