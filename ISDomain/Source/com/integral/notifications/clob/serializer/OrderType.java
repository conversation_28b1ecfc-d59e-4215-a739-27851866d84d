package com.integral.notifications.clob.serializer;
import com.integral.model.dealing.OrderRequest.Type;

public class OrderType {

  public static byte MKT = 0;
  public static byte LMT = 1;
  public static byte PEG = 2;
  public static byte PQ = 3;

  public static final String name(byte val) {

    switch (val) {
      case 0:
        return "MKT";
      case 1:
        return "LMT";
      case 2:
        return "PEG";
      case 3:
    	return "PQ";
      default:
        return null;
    }
  }
  
  public static Type getType(byte val)
  {

	    switch (val) {
	      case 0:
	        return Type.MARKET;
	      case 1:
	        return Type.LIMIT;
	      case 2:
	        return Type.STOPLIMIT;
	      case 3:
		        return Type.LIMIT;
	      default:
	        return null;
	    }
  }

}