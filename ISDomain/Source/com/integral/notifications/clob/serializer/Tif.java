package com.integral.notifications.clob.serializer;


import com.integral.model.dealing.TimeInForce;
public class Tif {

	  public static final byte DAY = 0;
	  public static final byte IOC = 2;
	  public static final byte FOK = 3;
	  public static final byte GTC = 4;
	  public static final byte FXG = 7;
	  public static final byte GTB = 8;

  public static final String name(byte side) {
	    switch (side) {
	      case 0:
	        return "DAY";
	      case 2:
	        return "IOC";
	      case 3:
	        return "FOK";
	      case 4:
	        return "GTC";
	      case 7:
	        return "FXG";
	      case 8:
	        return "GTB";
	      default:
	        return null;
	    }
  }
  
  public static TimeInForce get(byte side)
  {
	  switch (side) {
      case 2:
        return TimeInForce.IOC;
      case 3:
        return TimeInForce.FOK;
      case 4:
        return TimeInForce.GTC;
      case 0:
    	  return TimeInForce.DAY;
      case 7:
    	  return TimeInForce.GTF;
      case 8:
    	  return TimeInForce.GTB;
      //case 7:
        //return TimeInForce.;
      default:
        return TimeInForce.UNDEFINED;
    }
  }
}
