package com.integral.notifications.clob.rmq;

import com.integral.system.configuration.IdcMBean;
import com.integral.user.Organization;

public interface ClobListenerMBean extends IdcMBean {

	 public final static String CLOB_MBEAN_NAME = "ClobListenerMBean";
	 public final static String CLOB_CLUSTER_SHORT_NAME = "Idc.Mvps.Receiver.Cluster.ShortName";
	 public final static String CLOB_CLUSTER_ADDRESS = "Idc.Mvps.Receiver.Cluster.Address";
	 public final static String CLOB_CLUSTER_RECOVERY_INTERVAL = "Idc.Mvps.Receiver.Cluster.Recovery.Interval";
	 public final static String CLOB_CLUSTER_USERNAME = "Idc.Mvps.Receiver.Cluster.UserName";
	 public final static String CLOB_CLUSTER_PASSWORD = "Idc.Mvps.Receiver.Cluster.Password";
	 
	 public final static String CLOB_MBEAN_VHOST_NAME = "Idc.Mvps.Receiver.Vhost.ShortName";
	 public final static String CLOB_MBEAN_VHOST_LISTENER_THREAD_POOL_SIZE = "Idc.Mvps.Receiver.Vhost.ThreadPoolSize";   
	 public final static String CLOB_MBEAN_VHOST_CHANNEL_POOL_SIZE = "Idc.Mvps.Receiver.Vhost.ChannelPoolSize";   
	 public final static String CLOB_MBEAN_VHOST_CLUSTER_NAME = "Idc.Mvps.Receiver.Vhost.ClusterName";

	 public final static String CLOB_EXCHANGE_SHORT_NAME = "Idc.Mvps.Receiver.Exchange.ShortName";
	 public final static String CLOB_EXCHANGE_TYPE = "Idc.Mvps.Receiver.Exchange.Type";
	 public final static String CLOB_EXCHANGE_POLICY = "Idc.Mvps.Receiver.Exchange.Policy";
	 public final static String CLOB_EXCHANGE_DURABLE = "Idc.Mvps.Receiver.Exchange.Durable";
	 public final static String CLOB_EXCHANGE_PUBLISHRETRIES = "Idc.Mvps.Receiver.Exchange.PublishRetries";
	 public final static String CLOB_MBEAN_EXCHANGE_VHOSTNAME = "Idc.Mvps.Receiver.Exchange.VhostName";
	 public final static String CLOB_MBEAN_EXCHANGE_CLUSTER_NAME = "Idc.Mvps.Receiver.Exchange.ClusterName";
	 public final static String CLOB_MBEAN_QOS = "Idc.Mvps.Receiver.QOS";
	 public final static String CLOB_HEARTBEAT_INTERVAL_SEC = "Idc.Mvps.Receicver.HeartBeat.Seconds";
	 public final static String MVP_NUM_SERVERS = "Idc.Mvps.Server.NumInstances";
	 public final static String MVP_INSTANCE_NUM = "Idc.Mvps.Server.Instance.Number";
	 public final static String CHANNEL_NAME_CONFIG_PROP = "Idc.Mvps.Channel.Name.";
	 public final static String MAKER_CHANNEL = "Maker";
	 public final static String TAKER_CHANNEL = "Taker";
	 public final static String PQ_CHANNEL = "ProviderRequest";
	public final static String BROKER_CHANNEL = "BrokerRequest";
	public final static String BROKER_V4_EMS_CHANNEL = "BrokerV4EMS";

	public final static String V4_EMS_CHANNEL = "V4EMS";
	 public final static String PROVIDER_SUFFIX = "Idc.Mvps.Provider.TransactionId.Suffix";
	 public final static String PROVIDER_ORDER_SUFFIX = "Idc.Mvps.Provider.OrderId.Suffix";
	 public final static String CLOB_ORDER_VERSION = "Idc.Mvps.ClobOrder.Serializer.Version";
	 public final static String SUPER_BANK_INDEX_LOOKUP_ENABLED = "Idc.Adaptor.SuperBankIndex.Enabled";

	 public final static String PREFIX = "Idc.Mvps.TradePrefix";
	 public final static String SUFFIX = "Idc.Mvps.TradeSuffix";
	 public final static String BILATERAL_UNDISCLOSED_SELF_CLEARING_ORG_ENABLED = "Idc.Mvps.BilateralUndisclosed.SelfClearingOrg.Enabled";
	 public final static String BILATERAL_UNDISCLOSED_SELF_CLEARING_ORG = "Idc.Mvps.BilateralUndisclosed.SelfClearingOrg";

	public  String getClobMbeanName();
	public  String getClobClusterShortName() ;
	public  String getClobClusterAddress() ;
	public  long getClobClusterRecoveryInterval() ;
	public  String getClobClusterUsername() ;
	public  String getClobClusterPassword() ;
	public  String getClobMbeanVhostName() ;
	public  int getClobMbeanVhostListenerThreadPoolSize() ;
	public  int getClobMbeanVhostChannelPoolSize() ;
	public  String getClobMbeanVhostClusterName() ;
	public  String getClobExchangeShortName() ;
	public  String getClobExchangeType() ;
	public  String getClobExchangePolicy() ;
	public  boolean getClobExchangeDurable();
	public  int getClobExchangePublishretries() ;
	public  String getClobMbeanExchangeVhostname();
	public  String getClobMbeanExchangeClusterName();
	public  int getQOS();
	public  int getConnectionHBIntervalSeconds();
	public  int getNumServerInstances();
	public  int getServerInstanceNumber();
	public String getChannelName(Byte channel);
	public String getProviderTransactionIdSuffix();
	public String getProviderOrderIdSuffix();
	public boolean isSuperBankIndexLookUpEnabled();
	public byte getClobOrderSerializerVersion();
	public boolean isBilateralUndisclosedSelfClearingOrgEnabled();
	public Organization getBilateralUndisclosedSelfClearingOrg();
	public String getTakerCCPSuffix();
	public String getMakerCCPSuffix();

}
