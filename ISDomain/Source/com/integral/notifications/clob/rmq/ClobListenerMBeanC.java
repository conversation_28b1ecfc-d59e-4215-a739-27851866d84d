package com.integral.notifications.clob.rmq;

import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.log.MessageLogger;

import com.integral.notifications.clob.serializer.ClobOrder;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.configuration.IdcMBeanC;
import com.integral.user.Organization;

import java.util.HashMap;
import java.util.Map;

public class ClobListenerMBeanC extends IdcMBeanC implements ClobListenerMBean {

	private static ClobListenerMBean instance = createInstanceAndLoadProperties();
	private String clobClusterShortName;
	private String clobClusterAddress;
	private long clobClusterRecoveryInterval;
	private String clobClusterUsername;
	private String clobClusterPassword;
	private String clobMbeanVhostName;
	private int clobMbeanVhostListenerThreadPoolSize;
	private int clobMbeanVhostChannelPoolSize;
	private String clobMbeanVhostClusterName;
	private String clobExchangeShortName;
	private String clobExchangeType;
	private String clobExchangePolicy;
	private boolean clobExchangeDurable;
	private int clobExchangePublishretries;
	private String clobMbeanExchangeVhostname;
	private String clobMbeanExchangeClusterName;
	private int qOS;
	private int heartBeatSeconds;
	private int numInstances;
	private int instanceNum;
	private String providerSuffix;
	private String providerOrderIdSuffix;
	private Map<Byte,String> channelNameMap;
	private boolean isSuperBankIndexLookEnabled;
	private byte clobOrderSerializerVersion;

	private boolean isBilateralUndisclosedSelfClearingOrgEnabled;

	private Organization bilateralUndisclosedSelfClearingOrg;

	public String getTakerCCPSuffix() {
		return takerCCPSuffix;
	}

	public String getMakerCCPSuffix() {
		return makerCCPSuffix;
	}

	private String takerCCPSuffix;
	private String makerCCPSuffix;

    protected ClobListenerMBeanC()
    {
        super( CLOB_MBEAN_NAME);
    }

    private static ClobListenerMBean createInstanceAndLoadProperties()
    {
        final ClobListenerMBean result = new ClobListenerMBeanC();
        return result;
    }

    /**
     * Returns a singleton instance.
     *
     * @return a singleton instance of <code>OrderConfigurationMBean</code>
     */
    public static ClobListenerMBean getInstance()
    {
        return instance;
    }

    @Override
    public void initialize()
    {
        super.initialize();
        loadProperties();
    }
    

    public void loadProperties()
    {
      this.clobClusterShortName = getStringProperty(CLOB_CLUSTER_SHORT_NAME,"cluster1");
      this.clobClusterAddress = getStringProperty(CLOB_CLUSTER_ADDRESS,"cluster1");
      this.clobClusterRecoveryInterval = getLongProperty(CLOB_CLUSTER_RECOVERY_INTERVAL,5000);
      this.clobClusterUsername= getStringProperty(CLOB_CLUSTER_USERNAME,"integral");
      this.clobClusterPassword= getStringProperty(CLOB_CLUSTER_PASSWORD,"integral");
      this.clobMbeanVhostName= getStringProperty(CLOB_MBEAN_VHOST_NAME,"host1");;
      this.clobMbeanVhostListenerThreadPoolSize = getIntProperty(CLOB_MBEAN_VHOST_LISTENER_THREAD_POOL_SIZE,1);
      this.clobMbeanVhostChannelPoolSize = getIntProperty(CLOB_MBEAN_VHOST_CHANNEL_POOL_SIZE,5);
      this.clobMbeanVhostClusterName = getStringProperty(CLOB_MBEAN_VHOST_CLUSTER_NAME,"cluster1");
      this.clobExchangeShortName = getStringProperty(CLOB_EXCHANGE_SHORT_NAME,"testClob");
      this.clobExchangeType= getStringProperty(CLOB_EXCHANGE_TYPE,"direct");
      this.clobExchangePolicy = getStringProperty(CLOB_EXCHANGE_POLICY,"");
      this.clobExchangeDurable = getBooleanProperty(CLOB_EXCHANGE_DURABLE,true);;
      this.clobExchangePublishretries = getIntProperty(CLOB_EXCHANGE_PUBLISHRETRIES,2);
      this.clobMbeanExchangeVhostname= getStringProperty(CLOB_MBEAN_EXCHANGE_VHOSTNAME,"host1");
      this.clobMbeanExchangeClusterName= getStringProperty(CLOB_MBEAN_EXCHANGE_CLUSTER_NAME,"cluster1");
      this.qOS = getIntProperty(CLOB_MBEAN_QOS,50);
      this.heartBeatSeconds = getIntProperty(CLOB_HEARTBEAT_INTERVAL_SEC,240);
      this.numInstances = getIntProperty(MVP_NUM_SERVERS,1);
      this.instanceNum = getIntProperty(MVP_INSTANCE_NUM,0);
      this.providerSuffix = getStringProperty(PROVIDER_SUFFIX,"");
      this.providerOrderIdSuffix = getStringProperty(PROVIDER_ORDER_SUFFIX,"P");
      this.isSuperBankIndexLookEnabled =  getBooleanProperty(SUPER_BANK_INDEX_LOOKUP_ENABLED,false);
      this.clobOrderSerializerVersion = (byte)getIntProperty(CLOB_ORDER_VERSION, 0);
	  this.takerCCPSuffix = getStringProperty(PREFIX,"TCCP");
	  this.makerCCPSuffix = getStringProperty(SUFFIX,"MCCP");

	  this.isBilateralUndisclosedSelfClearingOrgEnabled = getBooleanProperty(BILATERAL_UNDISCLOSED_SELF_CLEARING_ORG_ENABLED,false);
	  		String bilateralUndisclosedSelfClearingOrgStr = getStringProperty(BILATERAL_UNDISCLOSED_SELF_CLEARING_ORG, "");
	  		if(isBilateralUndisclosedSelfClearingOrgEnabled) {
					this.bilateralUndisclosedSelfClearingOrg = ReferenceDataCacheC.getInstance().getOrganization(bilateralUndisclosedSelfClearingOrgStr);
					if(bilateralUndisclosedSelfClearingOrg==null){
						createUndiclosedOrgNotFoundAlert();
					}
			}

      initChannels();
    }

    private void createUndiclosedOrgNotFoundAlert(){
		isBilateralUndisclosedSelfClearingOrgEnabled = false;
		log.error("Exception in getting bilateralSelfClearingOrg: " + bilateralUndisclosedSelfClearingOrg);
		MessageLogger.getInstance().log(ISAlertMBean.ALERT_MVPS_BILATERAL_UNDISCLOSEDORG_NOTFOUND,
				"", "", "Please check admin server for Idc.Mvps.BilateralUndisclosed.SelfClearingOrg");
	}
    
    private void initChannels()
    {
        channelNameMap = new HashMap<Byte,String>();
    	channelNameMap.put(ClobOrder.GATEWAY_ORIGIN_MAKER,getStringProperty(CHANNEL_NAME_CONFIG_PROP+MAKER_CHANNEL,
    			ClobOrder.MAKER_GATEWAY));
    	channelNameMap.put(ClobOrder.GATEWAY_ORIGIN_TAKER,getStringProperty(CHANNEL_NAME_CONFIG_PROP+TAKER_CHANNEL,
    			ClobOrder.TAKER_GATEWAY));
    	channelNameMap.put(ClobOrder.PROVIDER_REQUEST,getStringProperty(CHANNEL_NAME_CONFIG_PROP+ PQ_CHANNEL,
    			ClobOrder.PROVIDER_CHANNEL));
		channelNameMap.put(ClobOrder.BROKER_REQUEST,getStringProperty(CHANNEL_NAME_CONFIG_PROP+ BROKER_CHANNEL,
				ClobOrder.BROKER_CHANNEL));
		channelNameMap.put(ClobOrder.BROKER_V4_EMS_CHANNEL_CODE,getStringProperty(CHANNEL_NAME_CONFIG_PROP+ BROKER_V4_EMS_CHANNEL,
				ClobOrder.BROKER_V4_EMS));
		channelNameMap.put(ClobOrder.V4_EMS_CHANNEL_CODE,getStringProperty(CHANNEL_NAME_CONFIG_PROP+ V4_EMS_CHANNEL,
				ClobOrder.V4_EMS));
    	
    }

	@Override
	public String getClobMbeanName() {
		// TODO Auto-generated method stub
		return CLOB_MBEAN_NAME;
	}

	@Override
	public String getClobClusterShortName() {
		// TODO Auto-generated method stub
		return clobClusterShortName;
	}

	@Override
	public String getClobClusterAddress() {
		// TODO Auto-generated method stub
		return clobClusterAddress;
	}

	@Override
	public long getClobClusterRecoveryInterval() {
		// TODO Auto-generated method stub
		return clobClusterRecoveryInterval;
	}

	@Override
	public String getClobClusterUsername() {
		// TODO Auto-generated method stub
		return clobClusterUsername;
	}

	@Override
	public String getClobClusterPassword() {
		// TODO Auto-generated method stub
		return clobClusterPassword;
	}

	@Override
	public String getClobMbeanVhostName() {
		// TODO Auto-generated method stub
		return clobMbeanVhostName;
	}

	@Override
	public int getClobMbeanVhostListenerThreadPoolSize() {
		// TODO Auto-generated method stub
		return clobMbeanVhostListenerThreadPoolSize;
	}

	@Override
	public int getClobMbeanVhostChannelPoolSize() {
		// TODO Auto-generated method stub
		return clobMbeanVhostChannelPoolSize;
	}

	@Override
	public String getClobMbeanVhostClusterName() {
		// TODO Auto-generated method stub
		return clobMbeanVhostClusterName;
	}

	@Override
	public String getClobExchangeShortName() {
		// TODO Auto-generated method stub
		return clobExchangeShortName;
	}

	@Override
	public String getClobExchangeType() {
		// TODO Auto-generated method stub
		return clobExchangeType;
	}

	@Override
	public String getClobExchangePolicy() {
		// TODO Auto-generated method stub
		return clobExchangePolicy;
	}

	@Override
	public boolean getClobExchangeDurable() {
		// TODO Auto-generated method stub
		return clobExchangeDurable;
	}

	@Override
	public int getClobExchangePublishretries() {
		// TODO Auto-generated method stub
		return clobExchangePublishretries;
	}

	@Override
	public String getClobMbeanExchangeVhostname() {
		// TODO Auto-generated method stub
		return clobMbeanExchangeVhostname;
	}

	@Override
	public String getClobMbeanExchangeClusterName() {
		// TODO Auto-generated method stub
		return clobMbeanExchangeClusterName;
	}

	@Override
	public int getQOS() {
		// TODO Auto-generated method stub
		return qOS;
	}

	@Override
	public int getConnectionHBIntervalSeconds() {
		// TODO Auto-generated method stub
		return heartBeatSeconds;
	}

	@Override
	public int getNumServerInstances() {
		// TODO Auto-generated method stub
		return numInstances;
	}

	@Override
	public int getServerInstanceNumber() {
		return instanceNum;
	}

	@Override
	public String getChannelName(Byte channel) {
		// TODO Auto-generated method stub
		return channelNameMap.get(channel);
	}

	@Override
	public String getProviderTransactionIdSuffix() {
		// TODO Auto-generated method stub
		return providerSuffix;
	}

	@Override
	public String getProviderOrderIdSuffix() {
		// TODO Auto-generated method stub
		return providerOrderIdSuffix;
	}

	public boolean isSuperBankIndexLookUpEnabled(){
		return isSuperBankIndexLookEnabled;
	}

	public byte getClobOrderSerializerVersion() {
		return clobOrderSerializerVersion;
	}

	public boolean isBilateralUndisclosedSelfClearingOrgEnabled(){
    	return isBilateralUndisclosedSelfClearingOrgEnabled;
	}

	public Organization getBilateralUndisclosedSelfClearingOrg(){
		return bilateralUndisclosedSelfClearingOrg;
	}

}
