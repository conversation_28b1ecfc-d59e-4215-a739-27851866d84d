package com.integral.migration;

import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import org.eclipse.persistence.queries.ReadAllQuery;

import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationFactory;
import com.integral.dbms.migration.CursorMigrationC;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.MetaspacesShutdownTask;
import com.integral.is.common.MetaspacesStartupC;
import com.integral.is.spaces.fx.persistence.ISSpacesPersistenceService;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.jmsx.JMSDBMBeanC;
import com.integral.jmsx.JMSManager;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.client.ClientMessage;
import com.integral.message.client.ClientMessageC;
import com.integral.message.client.SpacesClientMessageC;
import com.integral.persistence.Entity;
import com.integral.persistence.Namespace;
import com.integral.spaces.ApplicationSpaceEvent;
import com.integral.system.io.DataLoader;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;

/**
 * migrates client alert data messages from oracle to mongo 
 * 
 * <AUTHOR> D
 *
 */
public class ClientAlertDataMigration extends CursorMigrationC implements DataLoader
{
    private static final Log log = LogFactory.getLog(ClientAlertDataMigration.class);

	private AtomicLong sequenceForMongo = null;

	private AtomicLong incrementalCorrelationId = null;
	
	private final String CLIENT_ALERTS_METRICS_NAME = "ClientAlert";
	
	private AtomicInteger numberOfSuccessfulMigrations = null;
	
	private AtomicInteger numberOfUnSuccessfulMigrations = null;
	
    /**
     * @param name
     */
    public ClientAlertDataMigration(String name)
    {
        super(name);
        init();
    }
    
    private void init() {
    	sequenceForMongo = new AtomicLong(0);
    	incrementalCorrelationId = new AtomicLong(0);
    	numberOfSuccessfulMigrations = new AtomicInteger(0);
    	numberOfUnSuccessfulMigrations = new AtomicInteger(0);
    }

    @Override
    protected boolean preloadData()
    {
        JMSDBMBeanC._getInstance();
        JMSManager.initStandalone();
    	MetaspacesStartupC msc = new MetaspacesStartupC();
    	try {
			msc.startup(null, null);
		} catch (Exception e) {
			log.error("Error in initializing metaspaces:" + e.getMessage(), e);
		}
        return true;
    }

    @Override
    protected boolean migrateEntity(Entity entity)
    {
        boolean result = true;
        try
        {
            ClientMessageC clientMessage = (ClientMessageC)entity;
            doMigration(clientMessage);
        }
        catch(Exception e) {
            result = false;
            log.error("ClientAlertDataMigration of " + entity, e);
        }
        return result;
    }

    @Override
    protected ReadAllQuery getQuery()
    {
        ReadAllQuery raq = new ReadAllQuery();
        raq.setReferenceClass(ClientMessageC.class);
        return raq;
    }

	private String getNameSpaceNameForClientAlertMessage(Organization org) {
		Namespace ns = org.getNamespace();		
		String nameSpaceName = ns.getShortName();
		return nameSpaceName;
	}
	
	private String generateAndReturnIDForClientAlertMessage(User user) {
		StringBuilder sb = new StringBuilder("CLIENT_ALERT_MESSAGE").append("_")
				.append(user.getName()).append("_")
				.append(System.currentTimeMillis())
				.append("_").append( sequenceForMongo.incrementAndGet());
		String id = sb.toString();
		return id;
	}
    
    private SpacesClientMessageC createAndReturnClientMessage(ClientMessage clientMessage) {
    	
    	Organization org = clientMessage.getOrg();
    	User user = clientMessage.getUser();
    	String message = clientMessage.getMessage();
    	
		String nameSpaceName = getNameSpaceNameForClientAlertMessage(org);
		SpacesClientMessageC spacesClientMessage = new SpacesClientMessageC();
		
		IdcDate idcDate = (DateTimeFactory.newDate(clientMessage.getMessageDateTime()));
		Date ttlDate = DealingDataPurgeConfigurationFactory.getDealingDataPurgeConfigurationMBean().getTTL( spacesClientMessage.getClass(), idcDate );
		
		spacesClientMessage.setMessageDateTime(idcDate);
		spacesClientMessage.setMessage(message);
		spacesClientMessage.setUser(user);
		spacesClientMessage.setOrg(org);
		spacesClientMessage.set_id(generateAndReturnIDForClientAlertMessage(user));		
		spacesClientMessage.setNamespaceName(nameSpaceName);
		spacesClientMessage.setTimeToLive( ttlDate );

		if ( log.isDebugEnabled() ) {
			log.debug(" PROCESS_CLIENT_ALERT_MESSAGE " + clientMessage);
		}
		
		return spacesClientMessage;
	}
    
    private long getCorrelationId() {
		return incrementalCorrelationId.incrementAndGet();
	}
    
    
    private void persistClientAlertEvent(SpacesClientMessageC clientMessage) {
		ISSpacesPersistenceService cdqPersistentService = PersistenceServiceFactory.getCDQPersistenceService();
		ApplicationSpaceEvent ase = cdqPersistentService.createEvent(clientMessage, 
				ApplicationEventCodes.EVENT_CLIENT_ALERT_MESSAGE_PERSIST);
		long correlationId = getCorrelationId();
		cdqPersistentService.persist( ase, Long.toString(correlationId), false, CLIENT_ALERTS_METRICS_NAME);
	}
    
    
    private void doMigration(ClientMessage clientMessage)    
    {
    	try
		{			
			SpacesClientMessageC spacesClientMessage = createAndReturnClientMessage(clientMessage);			
			persistClientAlertEvent(spacesClientMessage);
			numberOfSuccessfulMigrations.incrementAndGet();			
		}
		catch ( Exception e )
		{
			log.error("EXCEPTION. Exception. client_alert_data_user=" + clientMessage.getUser(), e);
			numberOfUnSuccessfulMigrations.incrementAndGet();
		}
    }
    
    
    public void trailer()
    {
    	MetaspacesShutdownTask shutdownTask = new MetaspacesShutdownTask();
    	shutdownTask.shutdown(null, null);
    }

    private void printStats() {
    	System.out.println(" SUCCESSFUL_MIGRATION_COUNT " + numberOfSuccessfulMigrations.get());
    	System.out.println(" UNSUCCESSFUL_MIGRATION_COUNT " + numberOfUnSuccessfulMigrations.get());
    }
 
    /**
     * Main method to execute the UserSessionDataMigration
     *
     * @param args
     */
    public static void main(String[] args)
    {
    	log.info(" PERFORMING CLIENT_ALERT_DATA_MIGRATION ");
        ClientAlertDataMigration loader = new ClientAlertDataMigration("UserSessionDataMigration");
        try
        {
            loader.setOptions(args);
            loader.run();          
        }
        catch (Exception ex)
        {
            loader.logError("", ex);
        }
        System.out.println(" PERFORMED CLIENT_ALERT_DATA_MIGRATION ");
    	loader.printStats();
    	
    }
}