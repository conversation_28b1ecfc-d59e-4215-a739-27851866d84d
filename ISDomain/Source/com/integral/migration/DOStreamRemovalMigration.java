package com.integral.migration;

import com.integral.admin.utils.ObjectUtils;
import com.integral.admin.utils.pricemaking.PriceMakingUtil;
import com.integral.broker.admin.StreamService;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.Stream;
import com.integral.dbms.migration.CursorMigrationC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.persistence.Entity;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.system.configuration.ServerMBean;
import com.integral.system.property.SystemPropertyC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.CursoredStream;
import org.eclipse.persistence.queries.DeleteAllQuery;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;

import java.util.Collection;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 6/21/12
 * Time: 3:59 PM
 * This migration creates an internal DO Stream for existing FI orgs and multi-stream LP orgs.
 */
public class DOStreamRemovalMigration extends CursorMigrationC {

    private final static StreamService STREAM_SERVICE = com.integral.broker.admin.AdminFactory.getInstance().getStreamService();

    public DOStreamRemovalMigration(String name) {
        super(name);
    }

    @Override
    public boolean load(String aFileName) {
        boolean passed = true;
        passed &= preloadData();
        passed &= migrateEntities();
        return passed;
    }


    @Override
    protected boolean preloadData() {
        return true;
    }

    @Override
    protected boolean migrateEntities() {
        try {
            int numEntities = 0;
            int numMigratedEntities = 0;

            Session xact = PersistenceFactory.newSession();
            IdcSessionContext ctxt = IdcSessionManager.getInstance().getSessionContext("Integral");
            IdcSessionManager.getInstance().setSessionContext(ctxt);

            ReadAllQuery raq = getQuery();
            raq.useCursoredStream(500, 500);

            CursoredStream cursor = (CursoredStream) xact.executeQuery(raq);
            while (!cursor.atEnd()) {
                Entity entity = (Entity) cursor.read();
                try {
                    numEntities++;
                    boolean didMigrate = migrateEntity(entity);
                    if (didMigrate) {
                        numMigratedEntities++;
                        log("\tsuccessfully migrated " + ((Organization) entity).getShortName());
                    } else {
                        log("\tnot migrating " + ((Organization) entity).getShortName());
                    }
                } catch (Exception e) {
                    logError("\terror when migrating " + ((Organization) entity).getShortName(), e);
                }
            }

            DeleteAllQuery daq = new DeleteAllQuery();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get("shortName").like("%NoStream.Override");
            daq.setReferenceClass(SystemPropertyC.class);
            daq.setSelectionCriteria(expr);
            log.info("Removed NoStream Propertyset: " + xact.executeQuery(daq));

            log("Migration completed: migrated " + numMigratedEntities + " of a total of " + numEntities + " entities");
            cursor.close();

        } catch (Exception e) {
            logError("Error encountered during migration: ", e);
        }
        return true;
    }

    @Override
    protected boolean migrateEntity(Entity entity) {
        boolean result = false;
        Organization org = (Organization) entity;
        BrokerOrganizationFunction brokerOrganizationFunction = org.getBrokerOrganizationFunction();
        if (brokerOrganizationFunction != null) {
            Collection<Stream> streams = brokerOrganizationFunction.getStreams();
            if (!streams.isEmpty()) {
                Stream doStream = brokerOrganizationFunction.getStream(ServerMBean.DEFAULT_DO_STREAM_NAME);
                if (doStream != null) {
                    try {
                        Stream streamDefault = brokerOrganizationFunction.getDefaultStream();
                        if (!ObjectUtils.isNull(streamDefault) && streamDefault.isSameAs(doStream) && streams.size() > 1) {
                            log.error("Can't remove DOStream as it is set the default stream for Org:" + org.getShortName() + ". Please choose some other stream as default.");
                            return false;
                        }
                        Collection<TradingParty> tps = org.getTradingParties();
                        for (TradingParty tp : tps) {
                            if (ServerMBean.DEFAULT_DO_STREAM_NAME.equals(tp.getProviderStreamId())) {
                                log.error("Trying to remove DoStream for Org:" + org.getShortName() + " when it is assigned to trading party:" + tp.getShortName());
                                return false;
                            }
                        }
                        removeStream(org, ServerMBean.DEFAULT_DO_STREAM_NAME);
                        result = true;
                    } catch (Exception ex) {
                        log.error("Exception generated while migrating org:" + org.getShortName(), ex);
                        result = false;
                    }
                }
            }
        }
        return result;
    }

    private void removeStream(Organization org, String streamName) {
        Stream stream = PriceMakingUtil.getStream(org, streamName);
        STREAM_SERVICE.removeStream(stream);
    }

    @Override
    protected ReadAllQuery getQuery() {
        ReadAllQuery raq = new ReadAllQuery();
        raq.setReferenceClass(OrganizationC.class);
        return raq;
    }

    /**
     * @param args
     */
    public static void main(String[] args) {
        DOStreamRemovalMigration loader = new DOStreamRemovalMigration("DOStreamRemovalMigration");
        loader.log.warn("DOStreamRemovalMigration removing DO Stream for all orgs. ");
        try {
            loader.setOptions(args);
            loader.run();
        } catch (Exception ex) {
            loader.logError("DOStreamRemovalMigration Exception ", ex);
        }
    }
}
