
package com.integral.migration;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.cluster.ClusterMetaData;
import com.integral.persistence.Entity;
import com.integral.rds.message.RDSResponse;
import com.integral.rds.persistence.PersistenceServiceC;
import com.integral.rds.spaces.PersistenceServiceFactory;
import com.integral.services.ServiceContainerMBean;
import com.integral.services.cluster.ClusterRegistry;
import com.integral.services.cluster.CustomMetaDataProvider;
import com.integral.services.cluster.config.ClusterConfigurationLoader;
import com.integral.spaces.services.SpacesIDServiceFactory;

import java.io.FileReader;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 1/11/16
 * Time: 2:03 PM
 * To change this template use File | Settings | File Templates.
 */
public class ClusterConfigMigration extends AbstractRDSConfigMigration {

    private String filePath;
    private String userPrefix;

    private static final Log log = LogFactory.getLog(ClusterConfigMigration.class);

    public ClusterConfigMigration(String name, String userPrefix,
                                  String filePath) {
        super(name);
        this.userPrefix = userPrefix;
        this.filePath = filePath;
    }

    @Override
    protected boolean migrateEntity(Entity entity) {

        ServiceContainerMBean serviceContainerMBean = ServiceContainerMBean.getInstance();
        ClusterRegistry clusterRegistry = new ClusterRegistry(serviceContainerMBean);
        clusterRegistry.init();

        SpacesIDServiceFactory.getInstance().init(10,
                PersistenceServiceFactory.getRDSMetaspaceName(), "RDS-SEQ");
        try {

            String servicesNamespace = serviceContainerMBean.getServicesNamespace();

            log.info("Connecting to the zookeeper cluster:" + serviceContainerMBean.getZookeeperConnectString() + ",with namespace:" + servicesNamespace);

            // initialize the configuration
            List<ClusterMetaData> clustersMetaData = ClusterConfigurationLoader.getInstance().load(servicesNamespace, new FileReader(filePath));

            updateDynamicProperties(clustersMetaData);

            for (ClusterMetaData clusterMetaData : clustersMetaData) {

                try {
                    ClusterMetaData existingClusterConfig = (ClusterMetaData) PersistenceServiceC
                            .getInstance().getEntityByObjectId(
                                    ClusterMetaData.class,
                                    clusterMetaData.getShortName(),
                                    ClusterMetaData.NAMESPACE, 1);

                    if(clusterMetaData.equals(existingClusterConfig)){
                        log.info("No change in the cluster meta data;Skipping cluster migration for:" + clusterMetaData);
                    }else {
                        RDSResponse rdsResponse;
                        if (existingClusterConfig == null) {
                            rdsResponse = PersistenceServiceC.getInstance()
                                    .createEntity(clusterMetaData, "CREATE");
                        } else {
                            rdsResponse = PersistenceServiceC.getInstance()
                                    .updateEntity(clusterMetaData, "UPDATE");
                        }
                        if (rdsResponse.getStatus() == RDSResponse.Status.SUCCESS) {
                            log.info("Successfully created/updated Cluster configuration for:"
                                    + clusterMetaData.getShortName());
                        } else {
                            throw new Exception(
                                    "Creation/Updation of Messaging Configuration failed:Cause:"
                                            + rdsResponse.getText());
                        }
                        boolean isSuccess = clusterRegistry.createCluster(clusterMetaData);
                        if (isSuccess) {
                            log.info("Successfully created/updated Cluster configuration for:"
                                    + clusterMetaData.getShortName());
                        } else {
                            log.error("Failed to create/update cluster configuration for:"
                                    + clusterMetaData.getShortName());
                        }
                    }
                } catch (Exception ie) {
                    log.error("Error in cluster setup:" + clusterMetaData, ie);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        } finally {
            clusterRegistry.shutdown();
        }
        return true;
    }

    private void updateDynamicProperties(List<ClusterMetaData> clustersMetaData) {
        for (ClusterMetaData clusterMetaData : clustersMetaData) {
            log.info("Resolving dynamic properties for cluster:" + clusterMetaData);

            String metaDataCustomizationClass = clusterMetaData.getMetaDataCustomizationClass();
            if (null != metaDataCustomizationClass) {
                try {
                    Class<?> customMetaDataClass = Class.forName(metaDataCustomizationClass);
                    CustomMetaDataProvider metaDataProvider = (CustomMetaDataProvider) customMetaDataClass.newInstance();
                    metaDataProvider.updateMetaData(clusterMetaData);
                    log.info("Successfully resolved dynamic properties:" + clusterMetaData);
                } catch (Exception e) {
                    log.error("Unable to resolve dynamic properties for the cluster:" + clusterMetaData, e);
                }
            }
        }
    }


    public static void main(String[] args) {
        log.info(" PERFORMING Cluster ConfigMigration:Adding/Updating cluster configurations");

        if (args.length != 2) {
            log.error("Insufficient parameters, Useage: <user_prefix> <filepath>");
            System.exit(-1);
        }

        ClusterConfigMigration loader = new ClusterConfigMigration(
                "ClusterConfigMigration", args[0], args[1]);
        try {
            loader.setOptions(args);
            loader.run();
        } catch (Exception ex) {
            loader.logError("Cluster Config Migration failed", ex);
        }
        log.info(" PERFORMED Cluster Config Migration: Successfully updated Cluster configurations");
    }

}
