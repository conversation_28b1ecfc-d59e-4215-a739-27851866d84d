package com.integral.migration;

import com.integral.dbms.migration.CursorMigrationC;
import com.integral.is.common.MetaspacesShutdownTask;
import com.integral.is.common.MetaspacesStartupC;
import com.integral.jmsx.JMSDBMBeanC;
import com.integral.jmsx.JMSManager;
import com.integral.spaces.config.MetaspacesConfigMBean;
import com.integral.spaces.config.MetaspacesConfigMBeanImpl;
import com.integral.spaces.notification.NotificationManager;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.io.DataLoader;

import org.eclipse.persistence.queries.ReadAllQuery;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 1/11/16
 * Time: 2:18 PM
 * To change this template use File | Settings | File Templates.
 */
public abstract class AbstractRDSConfigMigration extends CursorMigrationC implements
        DataLoader {

    public AbstractRDSConfigMigration(String name) {
        super(name);
    }

    @Override
    protected boolean preloadData() {
        JMSDBMBeanC._getInstance();
        JMSManager.initStandalone();

        String oldValue = MetaspacesConfigMBeanImpl.getInstance()
                .getMetaspacesToInitialize();

        // Initialize RDS metaspace
        String newValue = oldValue + ",RDS";
        MetaspacesConfigMBeanImpl configMBeanImpl = (MetaspacesConfigMBeanImpl) MetaspacesConfigMBeanImpl
                .getInstance();
        String virtualServerProperty = MetaspacesConfigMBean.METASPACES_TO_INITIALIZE
                + "."
                + ConfigurationFactory.getServerMBean().getVirtualServerType();
        configMBeanImpl.setStringProperty(virtualServerProperty, newValue);
        configMBeanImpl.initialize();

        MetaspacesStartupC msc = new MetaspacesStartupC();
        try {
			msc.startup(null, null);
		} catch (Exception e) {
			log.error("Error in initializing metaspaces:" + e.getMessage(), e);
		}

        return true;
    }

    @Override
    public boolean load(String aFileName) {
        boolean passed = true;
        passed &= preloadData();
        passed &= migrateEntity(null);
        return passed;
    }

    public void trailer() {
        NotificationManager.getInstance().shutdown();
        MetaspacesShutdownTask shutdownTask = new MetaspacesShutdownTask();
        shutdownTask.shutdown(null, null);
        JMSManager.stopInstance();
    }

    @Override
    protected ReadAllQuery getQuery() {
        return null;
    }
}
