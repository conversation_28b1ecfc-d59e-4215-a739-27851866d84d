package com.integral.migration;

import com.integral.dbms.migration.CursorMigrationC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.jmsx.JNDIEntry;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.messaging.MessageReceiver;
import com.integral.messaging.MessageReceiverFactory;
import com.integral.persistence.Entity;
import com.integral.persistence.ExternalSystem;
import com.integral.persistence.PersistenceFactory;
import com.integral.rds.client.startup.RDSClientShutdown;
import com.integral.rds.client.startup.RDSClientStartup;
import com.integral.system.configuration.JNDIEntryC;
import com.integral.system.io.DataLoader;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.system.server.VirtualServerType;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

public class RabbitMQQueueMigration extends CursorMigrationC implements DataLoader
{
    private static final Log log = LogFactory.getLog( RabbitMQQueueMigration.class );
    private static Map<String, List<JNDIEntry>> map = new HashMap<String, List<JNDIEntry>>();

    /**
     * @param name
     */
    public RabbitMQQueueMigration( String name )
    {
        super( name );
    }

    @Override
    protected boolean preloadData()
    {
        RDSClientStartup startup = new RDSClientStartup();

        try
        {
            startup.startup( "RDSClientStartup", null );

            //preload map with jndiEntries
            Session session = PersistenceFactory.newSession();
            ReadAllQuery raq = new ReadAllQuery();
            raq.setReferenceClass( JNDIEntryC.class );
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression exp1 = eb.get( "queueEntry" ).equal( true );
            Expression exp2 = eb.get( "jmsBroker" ).get( "virtualServerType" ).get( "shortName" ).equal( VirtualServerType.SonicBroker );
            raq.setSelectionCriteria( exp1.and( exp2 ) );
            List<JNDIEntry> jndiEntries = ( Vector ) session.executeQuery( raq );

            for ( JNDIEntry jndiEntry : jndiEntries )
            {
                String jndiName = jndiEntry.getJndiName();
                List<JNDIEntry> entries = map.get( jndiName );
                if ( entries == null )
                {
                    entries = new ArrayList<JNDIEntry>();
                    map.put( jndiName, entries );
                }
                entries.add( jndiEntry );
            }

        }
        catch ( Exception e )
        {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    protected boolean migrateEntity( Entity entity )
    {
        boolean result = true;
        try
        {
            OrganizationC org = ( OrganizationC ) entity;
            execute( org );
        }
        catch ( Exception e )
        {
            result = false;
            log.error( "RabbitMQQueueMigration of " + entity, e );
        }
        return result;
    }

    @Override
    protected ReadAllQuery getQuery()
    {
        ReadAllQuery raq = new ReadAllQuery();
        raq.setReferenceClass( OrganizationC.class );
        return raq;
    }

    private void execute( Organization org )
    {
        if ( org == null )
        {
            return;
        }
        ExternalSystem extSys = ( ExternalSystem ) org.getCustomFieldValue( "DirectFX_DownloadExtSys" );
        if ( extSys != null )
        {
            String inDest = extSys.getInDestination();

            if ( inDest != null && inDest.trim().length() > 0 )
            {
                doMigration( inDest.trim() );
            }
        }

        //For tradingparties
        for ( Object obj : org.getTradingParties() )
        {
            TradingParty tp = ( TradingParty ) obj;
            String jndiName = tp.getStpDownloadQueue();
            if ( jndiName != null && jndiName.trim().length() > 0 )
            {
                doMigration( jndiName.trim() );
            }
        }

        //for LE
        for ( Object obj : org.getLegalEntities() )
        {
            LegalEntity le = ( LegalEntity ) obj;
            String jndiName = le.getStpDownloadQueue();
            if ( jndiName != null && jndiName.trim().length() > 0 )
            {
                doMigration( jndiName.trim() );
            }
        }
    }

    private void doMigration( String jndiName )
    {
        try
        {
            //get all jndientries for given name and the create queues for all jndientries
            List<JNDIEntry> jndiEntries = map.get( jndiName );
            if ( jndiEntries == null || jndiEntries.size() == 0 )
            {
                log.error( "Unable to get JNDIEntry for jndiName : " + jndiName + " Hence Skipping migration." );
            }
            else
            {
                for ( JNDIEntry jndiEntry : jndiEntries )
                {
                    log.info( "RMQM.doMigration. Creating RabbitMQ Queue from jndi=" + jndiEntry.getJndiName() + " jmsName=" + jndiEntry.getJmsName() );
                    MessageReceiver receiver = MessageReceiverFactory.newMessageReceiver( ServerRuntimeMBean.STP_EXCHANGE, jndiEntry.getJmsName(), true );
                    receiver.addBinding( jndiEntry.getJmsName() );
                    log.info( "RMQM.doMigration. done. jndi=" + jndiEntry.getJndiName() + " jmsName=" + jndiEntry.getJmsName() );
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "RMQM.doMigration. Exception. jndi=" + jndiName, e );
        }
    }


    public void trailer()
    {
    }


    /**
     * Main method to execute the RabbitMQQueueMigration
     *
     * @param args
     */
    public static void main( String[] args )
    {
        RabbitMQQueueMigration loader = new RabbitMQQueueMigration( "RabbitMQQueueMigration" );
        try
        {
            log.info( " ################### Starting RabbitMQQueueMigration" );
            loader.setOptions( args );
            loader.run();
            log.info( " ################### Migration finished. Shutting down connections." );
            RDSClientShutdown shutdown = new RDSClientShutdown();
            shutdown.shutdown( "RDSClientShutdown", null );
            log.info( " ################### RDSClient shutdown finished. RabbitMQQueueMigration completed." );
            System.exit( -1 );
        }
        catch ( Exception ex )
        {
            loader.logError( "", ex );
        }
    }
}