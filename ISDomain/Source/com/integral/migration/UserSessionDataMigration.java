package com.integral.migration;

import org.eclipse.persistence.queries.ReadAllQuery;

import com.integral.dbms.migration.CursorMigrationC;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.MetaspacesShutdownTask;
import com.integral.is.common.MetaspacesStartupC;
import com.integral.is.spaces.fx.persistence.ISSpacesPersistenceService;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.jmsx.JMSDBMBeanC;
import com.integral.jmsx.JMSManager;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Entity;
import com.integral.spaces.ApplicationSpaceEvent;
import com.integral.system.io.DataLoader;
import com.integral.user.User;
import com.integral.user.UserC;
import com.integral.user.UserSession;
import com.integral.user.UserSessionQueryService;

/**
 * Migrates user's sesson data to spaces.
 * <AUTHOR>
 *
 */
public class UserSessionDataMigration extends CursorMigrationC implements DataLoader
{
    private static final Log log = LogFactory.getLog(UserSessionDataMigration.class);

    /**
     * @param name
     */
    public UserSessionDataMigration(String name)
    {
        super(name);
    }

    @Override
    protected boolean preloadData()
    {
        JMSDBMBeanC._getInstance();
        JMSManager.initStandalone();
    	MetaspacesStartupC msc = new MetaspacesStartupC();
    	try {
			msc.startup(null, null);
		} catch (Exception e) {
			log.error("Error in initializing metaspaces:" + e.getMessage(), e);
		}
        return true;
    }

    @Override
    protected boolean migrateEntity(Entity entity)
    {
        boolean result = true;
        try
        {
            User user = (User)entity;
            doMigration(user);
        }
        catch(Exception e) {
            result = false;
            log.error("UserSessionDataMigration of " + entity, e);
        }
        return result;
    }

    @Override
    protected ReadAllQuery getQuery()
    {
        ReadAllQuery raq = new ReadAllQuery();
        raq.setReferenceClass(UserC.class);
        return raq;
    }

    private void doMigration(User user)
    {
    	try
		{
			ApplicationEventCodes apCode = ApplicationEventCodes.EVENT_USER_SESSION_UPDATE;
			UserSession sess = UserSessionQueryService.getUserSession(user);
			if ( sess == null )
			{
				sess = new UserSession();
				apCode = ApplicationEventCodes.EVENT_USER_SESSION_CREATE;
			}
			sess.setNamespaceName(user.getNamespace().getShortName());
			sess.setModifiedTime(System.currentTimeMillis());
			sess.set_id(String.valueOf(user.getObjectId()));
			if ( user.getLastFailedLoginDate() != null )
			{
				sess.setLastFailedLoginTime(user.getLastFailedLoginDate().getTime());
			}
			if ( user.getLastLoginDate() != null )
			{
				sess.setLastLoginTime(user.getLastLoginDate().getTime());
			}
			if ( user.getLastLogoutDate() != null )
			{
				sess.setLastLogoutTime(user.getLastLogoutDate().getTime());
			}
			sess.setNumberFailedLoginAttempts(user.getFailedLoginAttempts());
			sess.setUserName(user.getShortName());
			if( user.getCreatedDate() != null )
			{
				sess.setCreatedTime(user.getCreatedDate().getTime());
			}
			else
			{
				sess.setCreatedTime(System.currentTimeMillis());
				log.info("User's createdDate is null. Using current time.user="+user);
			}
			ISSpacesPersistenceService usps = PersistenceServiceFactory.getUserSessionPersistenceService();
			ApplicationSpaceEvent evt = usps.createEvent(sess, apCode);
			usps.persist(evt, String.valueOf(user.getObjectId()), false, "UserSession");
			log.info("USDM.doMigration. done. uid="+user.getFullName());
		}
		catch ( Exception e )
		{
			log.error("USDM.doMigration. Exception. uid=" + user.getFullName(), e);
		}
    }
    
    
    public void trailer()
    {
    	MetaspacesShutdownTask shutdownTask = new MetaspacesShutdownTask();
    	shutdownTask.shutdown(null, null);
    }


    /**
     * Main method to execute the UserSessionDataMigration
     *
     * @param args
     */
    public static void main(String[] args)
    {
        UserSessionDataMigration loader = new UserSessionDataMigration("UserSessionDataMigration");
        try
        {
            loader.setOptions(args);
            loader.run();
        }
        catch (Exception ex)
        {
            loader.logError("", ex);
        }
    }
}