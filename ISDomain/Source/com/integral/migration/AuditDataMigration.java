package com.integral.migration;

import com.integral.admin.audit.AuditConstant;
import com.integral.audit.AuditConverter;
import com.integral.audit.AuditEvent;
import com.integral.audit.AuditEventC;
import com.integral.audit.SpacesAuditEventC;
import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationFactory;
import com.integral.dbms.migration.CursorMigrationC;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.MetaspacesShutdownTask;
import com.integral.is.common.MetaspacesStartupC;
import com.integral.is.spaces.fx.persistence.ISSpacesPersistenceService;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.jmsx.JMSDBMBeanC;
import com.integral.jmsx.JMSManager;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.client.ClientMessage;
import com.integral.message.client.ClientMessageC;
import com.integral.message.client.SpacesClientMessageC;
import com.integral.persistence.Entity;
import com.integral.persistence.Namespace;
import com.integral.persistence.spaces.PersistenceConstants;
import com.integral.persistence.spaces.PersistenceServiceConfiguration;
import com.integral.query.QueryFactory;
import com.integral.query.QueryService;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.spaces.*;
import com.integral.spaces.spi.CriteriaSet;
import com.integral.system.io.DataLoader;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateC;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.IdcUtilC;

import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.queries.UpdateAllQuery;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * migrates audit event data messages from oracle to mongo
 *
 */
public class AuditDataMigration extends CursorMigrationC implements DataLoader
{
    private static final Log log = LogFactory.getLog(AuditDataMigration.class);

	private AtomicLong sequenceForMongo = null;


    private static AtomicLong incrementalCorrelationId = new AtomicLong();

    private final String AUDIT_EVENT = "AuditEvent";

    private AtomicInteger numberOfSuccessfulMigrations = null;

    private AtomicInteger numberOfUnSuccessfulMigrations = null;

    private static boolean useArgs = false;

    public IdcDate getFromDate()
    {
        return fromDate;
    }

    public void setFromDate(IdcDate fromDate)
    {
        this.fromDate = fromDate;
    }

    public String getComponent()
    {
        return component;
    }

    public void setComponent(String component)
    {
        this.component = component;
    }

    public IdcDate getToDate()
    {
        return toDate;
    }

    public void setToDate(IdcDate toDate)
    {
        this.toDate = toDate;
    }
    private IdcDate fromDate;
    private IdcDate toDate;
    private String component;
    /**
     * @param name
     */
    public AuditDataMigration(String name)
    {
        super(name);
        init();
    }

    public AuditDataMigration(String name, IdcDate start, IdcDate to, String component)
    {
        super(name);
        this.fromDate = start;
        this.toDate = to;
        this.component = component;
        init();
    }


    private void init() {
	       sequenceForMongo = new AtomicLong(0);
           incrementalCorrelationId = new AtomicLong(0);
           numberOfSuccessfulMigrations = new AtomicInteger(0);
           numberOfUnSuccessfulMigrations = new AtomicInteger(0);
    }

    @Override
    protected boolean preloadData()
    {
        JMSDBMBeanC._getInstance();
        JMSManager.initStandalone();
        MetaspacesStartupC msc = new MetaspacesStartupC();
        try {
			msc.startup(null, null);
		} catch (Exception e) {
			log.error("Error in initializing metaspaces:" + e.getMessage(), e);
		}
        return true;
    }

    @Override
    protected boolean migrateEntity(Entity entity)
    {
        boolean result = true;
        try
        {
            AuditEventC auditEvent = (AuditEventC)entity;
            doMigration(auditEvent);
        }
        catch(Exception e) {
            result = false;
            log.error("AuditEvent of " + entity, e);
        }
        return result;
    }

    @Override
    protected ReadAllQuery getQuery()
    {
        if(useArgs == false)
            intializeMigrationDateRange();
        ReadAllQuery raq = new ReadAllQuery();
        raq.setReferenceClass(AuditEventC.class);
        ExpressionBuilder expBuilder = new ExpressionBuilder();
        Expression fromDateExp=expBuilder.get("dateTime").greaterThanEqual(getFromDate());
        Expression toDateExp=expBuilder.get("dateTime").lessThanEqual(getToDate());
        Expression componentExpression=expBuilder.get("component").in(new String[]{"ADMIN_USER","CREDITADMIN"});
        log.info("QUERY CRITERIA : startDate = " + getFromDate() + " , toDate = " + getToDate() + " , component = ADMIN_USER, CREDITADMIN" ); ;
        raq.setSelectionCriteria(componentExpression.and(fromDateExp).and(toDateExp));
        return raq;
    }

    private SpacesAuditEventC createOutgoingAuditEvent(AuditEventC auditEvent) {

        AuditEventC incomingAuditEvent = auditEvent;
        SpacesAuditEventC outgoingAuditEvent = AuditConverter.convertNonSpacesToSpaces(incomingAuditEvent);
        outgoingAuditEvent.set_id(auditEvent.getAudit_guid());
        outgoingAuditEvent.setNamespaceName(PersistenceConstants.AUDIT.toString());

        if ( log.isDebugEnabled() ) {
            log.debug(" PROCESS AUDIT EVENT OBJECT " + auditEvent);
        }
        return outgoingAuditEvent;
    }

    private long getCorrelationId() {
        return incrementalCorrelationId.incrementAndGet();
    }


    private void persistAuditEvent(SpacesAuditEventC auditEvent) {
        ISSpacesPersistenceService auditPersistentService = PersistenceServiceFactory.getAuditPersistenceService();
        ApplicationSpaceEvent ase = auditPersistentService.createEvent(auditEvent,ApplicationEventCodes.EVENT_AUDIT_EVENT_PERSIST);
        long correlationId = getCorrelationId();
        auditPersistentService.persist(ase, Long.toString(correlationId), false, AUDIT_EVENT);
    }


    private void doMigration(AuditEventC auditEvent)
    {
        try
        {
            SpacesAuditEventC outgoingAuditEvent = createOutgoingAuditEvent(auditEvent);
            persistAuditEvent(outgoingAuditEvent);
            numberOfSuccessfulMigrations.incrementAndGet();
        }
        catch ( Exception e )
        {
            log.error("EXCEPTION: Unable to process record. GUID: " + auditEvent.getAudit_guid(), e);
            numberOfUnSuccessfulMigrations.incrementAndGet();
        }
    }


    public void trailer()
    {
        MetaspacesShutdownTask shutdownTask = new MetaspacesShutdownTask();
        shutdownTask.shutdown(null, null);
    }

    private void printStats() {
        System.out.println(" SUCCESSFUL_MIGRATION_COUNT " + numberOfSuccessfulMigrations.get());
        System.out.println(" UNSUCCESSFUL_MIGRATION_COUNT " + numberOfUnSuccessfulMigrations.get());
    }

    private void intializeMigrationDateRange()
    {
        SpacesQueryService.QueryResult<SpaceIterator> result = new SpacesQueryService.QueryResult<SpaceIterator>();
        String namespaceName =  PersistenceConstants.AUDIT.toString();
        String spaceName = PersistenceConstants.AUDIT;
        String metaspaceName = PersistenceServiceConfiguration.getAuditMetaspaceName();
        Metaspace metaspace = Metaspaces.getInstance().getMetaspace(namespaceName, metaspaceName);
        QueryBuilder queryBuilder = metaspace.createNewQueryBuilder(namespaceName, spaceName);
        CriteriaSet cs = metaspace.defaultCriteriaSet();
        Criteria parameterCriteria = cs.is("cmp", "MIGRATE_USER_CREDIT");
        parameterCriteria = cs.and(parameterCriteria, cs.in("nssh","AUDIT"));
        queryBuilder.add(parameterCriteria);
        result.setResult( queryBuilder.build().getIterator(AuditEventC.class));
        if(result.getResult().count()!=0)
        {
            log.info("****************************************************************");
            log.info("Found Migration Date in DB, calculating next date range..");
            //Calculate the most recent date
            SpaceIterator<AuditEventC> i = result.getResult();
            Calendar c = Calendar.getInstance();
            c.setTime(new Date());
            c.add(Calendar.MONTH, -36);
            SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
            String output = sdf.format(c.getTime());
            Date startDate = new Date(output) ;
            setFromDate(DateTimeFactory.newDate(startDate));
            try
            {
                while (i.hasNext())
                {
                    AuditEventC auditEvent = (AuditEventC) i.next();
                    startDate =  sdf.parse(auditEvent.getStringArg1());
                    IdcDate tmpDate = DateTimeFactory.newDate(startDate);
                    if(tmpDate.isLaterThanOrEqualTo(getFromDate()))
                    {
                        setFromDate(tmpDate);
                    }
                }
            } catch (ParseException e)
            {
                log.error("Unable to parse incoming date for persisting audit records " + e.getMessage());
            }
            c.setTime(getFromDate().asJdkDate());
            c.add(Calendar.MONTH, 6);
            output = sdf.format(c.getTime());
            startDate = new Date(output) ;
            setToDate(DateTimeFactory.newDate(startDate));

        }
        else
        {
            System.out.println("No migration date in DB, setting to start date");
            SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
            Calendar c = Calendar.getInstance();
            c.setTime(new Date());
            c.add(Calendar.MONTH, -36);
            String output = sdf.format(c.getTime());
            Date startDate = new Date(output);
            setFromDate(DateTimeFactory.newDate(startDate));
            c.setTime(getFromDate().asJdkDate());
            c.add(Calendar.MONTH, 6);
            output = sdf.format(c.getTime());
            Date toDate = new Date(output);
            setToDate(DateTimeFactory.newDate(toDate));
        }


        log.info("****************************************************************");
        log.info("Start Date = "+getFromDate());
        log.info("To Date = "+ getToDate());
        log.info("****************************************************************");
        //Persist the new date
        ISSpacesPersistenceService auditSessionPersistenceService;
        auditSessionPersistenceService = PersistenceServiceFactory.getAuditPersistenceService();
        SpacesAuditEventC anAuditEvent = new SpacesAuditEventC();
        anAuditEvent.setComponent("MIGRATE_USER_CREDIT");
        anAuditEvent.set_id(System.currentTimeMillis()+"");
        anAuditEvent.setNamespaceName("AUDIT");
        SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
        String persistDate = sdf.format(getToDate().asJdkDate());
        anAuditEvent.setStringArg1(persistDate);
        ApplicationSpaceEvent ase = auditSessionPersistenceService.createEvent(anAuditEvent,ApplicationEventCodes.EVENT_AUDIT_EVENT_PERSIST);
        long correlationId = incrementalCorrelationId.incrementAndGet();
        auditSessionPersistenceService.persist(ase, Long.toString(correlationId), false,"Audit Message");
    }

    /**
     * Main method to execute the Audit Event Migration
     *
     * @param args
     */
    public static void main(String[] args)
    {
        log.info(" PERFORMING AUDIT EVENT MIGRATION ");
        AuditDataMigration loader = null;
        int id = -1;
        try
        {

            if(args.length > 2)
            {
                log.info(" Input Args : " +args[2]+" "+args[3] + " "+args[4]);
                SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
                Date tmp =  sdf.parse(args[2]);
                IdcDate startDate = DateTimeFactory.newDate(tmp);
                tmp =  sdf.parse(args[3]);
                IdcDate toDate = DateTimeFactory.newDate(tmp);
                String component = args[4];
                useArgs = true;
                loader = new AuditDataMigration("AuditEventMigration", startDate, toDate, component);
            }
            else
                loader = new AuditDataMigration("AuditEventMigration");


            loader.setOptions(args);
            loader.run();
            log.info(" PERFORMED AUDIT EVENT MIGRATION ");
            loader.printStats();
        } catch (ParseException e)
        {
            e.printStackTrace();
            if(loader == null)
                log.info("Unable to initialize loader");
            else
                loader.logError("Unable to parse command line arguments");
        } catch (Exception ex)
        {        ex.printStackTrace();
            if(loader == null)
                log.info("Unable to initialize loader");
            else
                loader.logError("", ex);
        }
    }
}