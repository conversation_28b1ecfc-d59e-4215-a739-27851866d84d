package com.integral.migration;

import com.integral.broker.model.Stream;
import com.integral.dbms.migration.CursorMigrationC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.jmsx.JMSDBMBeanC;
import com.integral.jmsx.JMSManager;
import com.integral.persistence.Entity;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Record;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.math.BigDecimal;
import java.util.Set;
import java.util.Vector;

/**
 * Set superbank flag on orgs which have Idc.Multihost.<Org> property.
 * Assign unique index to all the streams across super lp org.
 * Created by katar<PERSON> on 4/27/2017.
 */
public class SuperBankOrgSettingMigration extends CursorMigrationC
{
    public SuperBankOrgSettingMigration(String name) {
        super(name);
    }

    @Override
    protected boolean preloadData()
    {
        JMSDBMBeanC._getInstance();
        JMSManager.initStandalone();
        return true;
    }

    protected boolean migrateEntities()
    {
        try {
            Set<String> multihostOrgs = ISFactory.getInstance().getMultihostMBean().getMultihostAdaptorOrgs();
            UnitOfWork uow = getTransaction();
            log.info("Multihost orgs to be migrated " + multihostOrgs);

            for (String multihostOrg : multihostOrgs)
            {
                Organization org = ReferenceDataCacheC.getInstance().getOrganization( multihostOrg );
                if ( org != null )
                {
                    if ( org.getRealLP () != null )
                    {
                        log.error ( "Organization is a mask lp and cannot be a super bank. org=" + multihostOrg );
                        continue;
                    }
                    Organization registeredOrg = (Organization) uow.registerObject(org);
                    if ( !registeredOrg.isSuperBank () )
                    {
                        registeredOrg.setSuperBank ( true );
                        log.info ( "Setting superbank flag to true for " + org.getShortName () );
                    }
                    else
                    {
                        log.info ( "Org is found to be set as a super bank. " + org.getShortName () );
                    }
                    for ( Stream stream : org.getBrokerOrganizationFunction().getStreams() )
                    {
                        if ( stream.getSuperLPIndex() == 0 )
                        {
                            Stream registeredStream = ( Stream ) uow.registerObject( stream );

                            // reserve the index 1.
                            short index = nextSuperLPIndex ();
                            if ( index == 1 )
                            {
                                index = nextSuperLPIndex ();
                                log.info("Index 1 is reserved and index incremented to " + index + " for org=" + org.getShortName () );
                            }
                            registeredStream.setSuperLPIndex( index );
                            log.info("Assigned index " + registeredStream.getSuperLPIndex() + " to stream " + stream.getShortName() + " of " + org.getShortName() );
                        }
                    }
                }
                else
                {
                    log.error ( "No org found with short name=" + multihostOrg );
                }
            }
            uow.commit();
        } catch ( Exception ex )
        {
            ex.printStackTrace();
        }
        log.info("Migrated all organizations to super bank for all multihost configurations.");
        return true;

    }

    private short nextSuperLPIndex()
    {
        StringBuilder sql = new StringBuilder( 100 ).append( "SELECT " ).append( "IDCSUPERLPSTREAM" ).append( "INDEX_SEQ.NEXTVAL FROM DUAL" );
        try
        {
            Vector resultSet = PersistenceFactory.newSession().executeSQL( sql.toString() );
            Record row = ( Record ) resultSet.elementAt( 0 );
            BigDecimal val = ( BigDecimal ) row.get( "NEXTVAL" );
            return val.shortValue();
        }
        catch ( Exception e )
        {
            log.error( "SuperBankOrgSettingMigration.nextIndexSequence.ERROR : Error when retrieving next index sequence number from DB for table= IDCSUPERLPSTREAM", e );
            e.printStackTrace();
        }
        return 0;
    }

    @Override
    protected boolean migrateEntity(Entity entity) {
        return false;
    }

    @Override
    protected ReadAllQuery getQuery()
    {
        return null;
    }

    public static void main(String[] args) throws Exception {
        SuperBankOrgSettingMigration script = new SuperBankOrgSettingMigration("SuperBankOrgSettingMigration");
        try
        {
            script.setOptions( args );
            script.run();
        }
        catch ( Exception ex )
        {
            script.log.error( "Exception", ex );
        }
    }
}
