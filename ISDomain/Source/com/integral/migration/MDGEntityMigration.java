package com.integral.migration;

import java.util.List;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Entity;
import com.integral.rds.config.ServerConfigMBeanC;
import com.integral.rds.message.RDSResponse;
import com.integral.rds.message.RDSResponse.Status;
import com.integral.rds.persistence.PersistenceServiceC;
import com.integral.rds.spaces.PersistenceServiceFactory;
import com.integral.spaces.notification.NotificationConfiguration;
import com.integral.spaces.services.SpacesIDServiceFactory;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.rds.message.Query;
import com.integral.rds.persistence.QueryService.QueryType;
import com.integral.rds.message.QueryBuilder;
import com.integral.rds.persistence.QueryService;
import com.integral.virtualserver.mdg.MDGEntity;
import com.integral.model.ReferenceEntity;
import com.integral.mdf.MDFConfigMBeanC;
import com.integral.mdf.MDFConfigMBean;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;
import com.integral.system.server.VirtualServer;
import com.integral.system.server.VirtualServerC;
import com.integral.system.property.SystemPropertyC;
import com.integral.persistence.PersistenceFactory;
import com.integral.query.QueryFactory;

/**
 * Created by nagarajap on 1/8/2018.
 */
public class MDGEntityMigration extends AbstractRDSConfigMigration {

    private static final Log log = LogFactory
            .getLog(MDGEntityMigration.class);


    public MDGEntityMigration(String name) {
        super(name);

    }

    @Override
    protected boolean migrateEntity(Entity entity) {
        SpacesIDServiceFactory.getInstance().init(10,
                PersistenceServiceFactory.getRDSMetaspaceName(), "RDS-SEQ");
        try {

            if (MDFConfigMBeanC.getInstance().isMDGMigrationEnabled()) {

                // retrieve all the MDG entities
                Query query = new QueryBuilder(MDGEntity.class).addStringParam(QueryService.NAMESPACE_FIELD, MDGEntity
                        .NAMESPACE).build();
                query.setQueryId("MDGEntityMigration");
                query.setQueryType(QueryType.DEFAULT_QUERY);
                List<ReferenceEntity> mdgServers = PersistenceServiceC.getInstance().getEntityByParams(MDGEntity.class,
                        MDGEntity.NAMESPACE, query);

                for (ReferenceEntity mdgServerItr : mdgServers) {
                    MDGEntity mdgServer = (MDGEntity) mdgServerItr;
                    RDSResponse rdsResponse;
                    if (mdgServer.getDeployedOrgs() != null && !mdgServer.getDeployedOrgs().isEmpty()) {
                        String deployedOrgs = "";
                        for (String org : mdgServer.getDeployedOrgs()) {
                            deployedOrgs += org + ",";
                        }

                        mdgServer.setDeployedOrgStr(deployedOrgs.substring(0, deployedOrgs.length() - 1));

                        rdsResponse = PersistenceServiceC.getInstance()
                                .updateEntity(mdgServer, "UPDATE");

                        if (rdsResponse.getStatus() == Status.SUCCESS) {
                            log.info("Successfully updated MDGEntity:" + mdgServer);
                        } else {
                            throw new Exception(
                                    "Updating MDGEntity:" + mdgServer + ":Failed:Cause:" + rdsResponse.getText());
                        }
                    } else {
                        log.info("Skipping MDG Server:" + mdgServer.getShortName() + ":Org list(now deprecated) is " +
                                "empty");
                    }
                }
                updateMDGMigrationCheck(MDFConfigMBean.MDG_MIGRATION);
            } else {
                log.warn("MDG Entity migration has already been run in this environment; Hence skipping");
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }

        return true;
    }

    private void updateMDGMigrationCheck(String propName)  throws Exception{
        VirtualServer vs = (VirtualServer) QueryFactory.getQueryService().find(VirtualServerC.class, SystemPropertyC
                .VIRTUAL_SERVER_TYPE_GLOBAL);
        Session session = PersistenceFactory.newSession();
        UnitOfWork uow = session.acquireUnitOfWork();
        VirtualServer vsReg = (VirtualServer) uow.registerObject(vs);

        SystemPropertyC sp = new SystemPropertyC();
        SystemPropertyC spReg = (SystemPropertyC) uow.registerObject(sp);
        spReg.setShortName(propName);
        spReg.setStringValue("false");
        spReg.setVirtualServer(vsReg);
        spReg.setServerGroup(vsReg.getServerGroup());
        spReg.setScope(SystemPropertyC.SCOPE_TYPE_GLOBAL);
        spReg.setDescription(propName + ":Updated by MDGEntity Migration");
        uow.commit();
    }

    public static void main(String[] args) {
        log.info("Updating MDG Servers");


        MDGEntityMigration loader = new MDGEntityMigration("MDGEntityMigration");
        try {
            loader.setOptions(args);
            loader.run();
        } catch (Exception ex) {
            loader.logError("", ex);
        }
        log.info(" PERFORMED MDG Server Migration: Successfully updated MDG servers");
    }

}
