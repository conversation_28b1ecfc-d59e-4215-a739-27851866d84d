package com.integral.migration;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.organization.MulticastAddress;
import com.integral.persistence.Entity;
import com.integral.model.ReferenceEntity;
import com.integral.rds.message.Query;
import com.integral.rds.message.QueryBuilder;
import com.integral.rds.message.RDSResponse;
import com.integral.rds.persistence.PersistenceServiceC;
import com.integral.rds.persistence.QueryService;
import com.integral.rds.spaces.PersistenceServiceFactory;
import com.integral.spaces.services.SpacesIDServiceFactory;

import java.util.List;

public class MulticastAddressPoolCreate extends AbstractRDSConfigMigration {

    private static final Log log = LogFactory
            .getLog(MulticastAddressPoolCreate.class);

    private String firstOctate = "229";


    public MulticastAddressPoolCreate(String name, String firstOctate) {
        super(name);
        if( firstOctate != null )
            this.firstOctate = firstOctate;
    }

    @Override
    protected boolean migrateEntity(Entity entity) {
        SpacesIDServiceFactory.getInstance().init(10,
                PersistenceServiceFactory.getRDSMetaspaceName(), "RDS-SEQ");
        try {

            String shortName = "MAIN-Default";
            Query query = new QueryBuilder(MulticastAddress.class).addStringParam(QueryService.SHORTNAME_FIELD, shortName).build();
            query.setQueryId("MulticastAddressPoolCreate");
            query.setQueryType(QueryService.QueryType.DEFAULT_QUERY);
            List<ReferenceEntity> multicastAddressList = PersistenceServiceC.getInstance().getEntityByParams(MulticastAddress.class,
                    "MAIN", query);

            if( multicastAddressList != null && multicastAddressList.size() > 0) {
                log.info("Default Multicast Address already created... " + multicastAddressList.get(0));
            } else {
                MulticastAddress ma = new MulticastAddress();
                ma.setMulitcastAddress(firstOctate + ".0.0.0");
                ma.setType(MulticastAddress.DEFAULT_TYPE);
                ma.setNamespaceName("MAIN");
                ma.setOrganization("MAIN");
                ma.set_id(ma.getMulitcastAddress());
                ma.setShortName(shortName);
                ma.setOffset(0);

                RDSResponse rdsResponse = PersistenceServiceC.getInstance().createEntity(ma, "MulticastAddressPoolCreate");
                if (rdsResponse.getStatus() == RDSResponse.Status.SUCCESS) {
                    log.info("MulticastAddress created successfully:" + rdsResponse.getEntities().get(0));
                } else {
                    throw new Exception(
                            "Failed to created default multicast address" + rdsResponse.getText());
                }
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }

        return true;
    }
    public static void main(String[] args) {
        log.info("Create base multicast address with " + args[0]);

        String arg1 = args[0];
        Integer.parseInt(arg1);

        MulticastAddressPoolCreate loader = new MulticastAddressPoolCreate("MulticastAddressPoolCreate", arg1);
        try {
            loader.setOptions(args);
            loader.run();
        } catch (Exception ex) {
            loader.logError("", ex);
        }
    }
}
