package com.integral.migration;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;

import com.integral.dbms.migration.CursorMigrationC;
import com.integral.persistence.Entity;
import com.integral.system.io.DataLoader;
import com.integral.user.Organization;


public class MigrateOrgVsMapping extends CursorMigrationC implements DataLoader 
{

	private static final int SUCCESS_CODE = 200;
	private static final String PROTOCOL = "http://";
	private static final String ADD_ORG_URI = "/orgVsMap/addOrg?password=";
	private static final String a10Host_Key = "a10Host";
	private static final String a10Password_Key = "a10Password";
	
	private static String a10Host;
	private static String a10Password; 
	private static String a10URL;
	
	public MigrateOrgVsMapping(String name) {
		super(name);
	}

	@Override
	protected boolean preloadData() {
		return false;
	}

	/**
	 * HTTP GET request client
	 * @param GET URL
	 * @return true if able to place GET request
	 * @throws Exception
	 * */
	private boolean httpGET ( final String url ) throws Exception
	{
		HttpClient client = new DefaultHttpClient();
		HttpGet request = new HttpGet(url);
		HttpResponse response = client.execute(request);
		
		if( response.getStatusLine().getStatusCode() != SUCCESS_CODE )
		{
			log.error("URL:"+url+", Response="+response.getStatusLine().getStatusCode()+":"+response.getStatusLine().getReasonPhrase());
			
			BufferedReader reader = new BufferedReader( new InputStreamReader( response.getEntity().getContent() ) );
			
			String line = "";
			StringBuffer result = new StringBuffer();

			while( ( line = reader.readLine() ) != null )
				result.append(line);
			
			throw new Exception( result.toString() );
		}

		return true;
		
	}
	
	
	private void addOrgVsMapping ( final String orgName, final String vsName ) throws Exception
	{

		final String ORG_PARAM = "&org=";
		final String VS_PARAM = "&vs=";
		
		final String connectURL = a10URL + ORG_PARAM + orgName + VS_PARAM + vsName;
		httpGET( connectURL );
		
	}
	
	@Override
	protected boolean migrateEntity(Entity entity) {
		
		Organization org = (Organization) entity;
		boolean isSuccess = false;
		
		if( null == org.getVirtualServer() )
		{
			log.error("Org="+org.getShortName()+", does not have virtual server assigned to it.");
			return false;
			
		}
		
		try {
			addOrgVsMapping( org.getShortName(), org.getVirtualServer().getShortName() );
			isSuccess = true;
		} catch (Exception e) {
			log.error( "Error adding orgName-vsName mapping org="+org.getShortName()+", vsName="+org.getVirtualServer().getShortName()+"Error Message:" + e.getMessage() );
		}
		
		return isSuccess;
	}

	@Override
	protected ReadAllQuery getQuery() {
		ReadAllQuery query = new ReadAllQuery( Organization.class );
		query.useCollectionClass( ArrayList.class );
		ExpressionBuilder eb = new ExpressionBuilder();
        Expression exp = eb.get( "status" ).equal( Entity.ACTIVE_STATUS );
        query.setSelectionCriteria( exp );
        return query;
	}

	public static void main(String args[])
	 {
		String doRun = System.getProperty( "doRun" );
		
		MigrateOrgVsMapping loader = new MigrateOrgVsMapping("MigrateOrgVsMapping");
		
		if( doRun == null || !"true".equals(doRun) )
		{
			System.out.println("Skipping A10 migration as doRun System Property is not set");
			return;
		}
		
		a10Host = System.getProperty(a10Host_Key);
		a10Password = System.getProperty(a10Password_Key);
		a10URL = ( a10Host != null && a10Password != null ) ? PROTOCOL + a10Host + ADD_ORG_URI + a10Password : null ;
		
		if( a10URL == null )
		{
			System.out.println("Please provide <a10Host> and <a10Password> parameters as system properties to run migration");
			return;
		}
		
		System.out.println("MigrateOrgVsMapping creating org-VS Mapping in A10 server");
		try
		{
			loader.setOptions(args);
			loader.run();
		}
		catch (Exception e)
		{
			System.out.println("MigrateOrgVsMapping, error encountered during migration: "+e.getMessage());
		}
	 }

}
