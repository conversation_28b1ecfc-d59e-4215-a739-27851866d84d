package com.integral.migration;

import com.integral.is.common.MetaspacesShutdownTask;
import com.integral.is.common.MetaspacesStartupC;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * Created by pulkit on 4/9/14.
 * This class resyncs the changes with the databases.
 */
public class ManageMongoSchema {

    private static Log log = LogFactory.getLog(ManageMongoSchema.class);
    private String userPrefix;
    private String filePath;
    private String action;
    private static final String CREATE = "create";
    private static final String DROP = "drop";

    public ManageMongoSchema(String userPrefix, String filePath,String action) {
        this.userPrefix = userPrefix;
        this.filePath = filePath;
        this.action = action;
    }

    private void setUp() throws Exception {
        new MetaspacesStartupC().startMetaspaces(null,true);
    }

    private void tearDown() {
        new MetaspacesShutdownTask().shutdown(null, null);
    }

    public static void main(String[] args) {
        if (args.length != 3) {
            log.error("Insufficient parameters, run as java ManageMongoSchema dbprefix create/drop filepath");
            System.exit(-1);
        }
        ManageMongoSchema manager = new ManageMongoSchema(args[0], args[2],args[1]);
        try {
            manager.setUp();
            manager.start();
        } catch (Exception ex) {
            log.error("exception while migrating.", ex);
        } finally {
            manager.tearDown();
        }
    }

    private void start() throws Exception {
        MigrateMetaspaces migrate = new MigrateMetaspaces(userPrefix, filePath);
        log.info("Executing action:-"+action);
        if(CREATE.equals(action)) {
            migrate.process();
        }else if(DROP.equals(action)){
            migrate.dropAllMetaspaces();
        }
    }

}
