package com.integral.migration;

import com.integral.admin.utils.transaction.AdminTransactionUtil;
import com.integral.broker.admin.OrganizationService;
import com.integral.broker.admin.StreamService;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.ModelFactory;
import com.integral.broker.model.Stream;
import com.integral.dbms.loader.SystemPropertyLoaderC;
import com.integral.dbms.migration.CursorMigrationC;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.persistence.Entity;
import com.integral.persistence.Namespace;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.system.configuration.ServerMBean;
import com.integral.system.property.SystemPropertyC;
import com.integral.system.server.VirtualServer;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.util.IdcUtilC;
import org.eclipse.persistence.queries.CursoredStream;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 6/21/12
 * Time: 3:59 PM
 * This migration creates an internal DO Stream for existing FI orgs and multi-stream LP orgs.
 */
public class DOStreamCreationMigration extends CursorMigrationC {

    private final static StreamService STREAM_SERVICE = com.integral.broker.admin.AdminFactory.getInstance().getStreamService();
    private final static OrganizationService ORGANIZATION_SERVICE = com.integral.broker.admin.AdminFactory.getInstance().getOrganizationService();

    private final boolean testRun;
    private final Set<String> namespaces = new HashSet<String>(100);
    private final SystemPropertyLoaderC propertyLoader;

    public DOStreamCreationMigration(String name) {
        super(name);
        namespaces.add( IdcUtilC.MAIN_NAMESPACE );
        String testRunStr = System.getProperty("testRun");
        testRun = org.apache.commons.lang.StringUtils.isEmpty(testRunStr) ? true : Boolean.getBoolean(testRunStr);
        log.info("Running with TestMode:" + testRun);
        propertyLoader = new SystemPropertyLoaderC();
    }

    @Override
    public boolean load(String aFileName) {
        boolean passed = true;
        passed &= preloadData();
        passed &= migrateEntities();
        return passed;
    }


    @Override
    protected boolean preloadData() {
        return true;
    }

    @Override
    protected boolean migrateEntities() {
        try {
            int numEntities = 0;
            int numMigratedEntities = 0;

            Session xact = PersistenceFactory.newSession();

            ReadAllQuery raq = getQuery();
            raq.useCursoredStream(500, 500);
            IdcSessionContext ctxt = IdcSessionManager.getInstance().getSessionContext("Integral");
            IdcTransaction idcTransaction = IdcSessionManager.getInstance().newTransaction(ctxt);
            IdcSessionManager.getInstance().setTransaction(null);//Use this transaction only for system property loader.
            setupSystemPropertyLoader(idcTransaction);

            CursoredStream cursor = (CursoredStream) xact.executeQuery(raq);
            while (!cursor.atEnd()) {
                Entity entity = (Entity) cursor.read();
                try {
                    numEntities++;
                    boolean didMigrate = migrateEntity(entity);
                    if (didMigrate) {
                        numMigratedEntities++;
                        log("\tsuccessfully migrated " + ((Organization) entity).getShortName());
                    } else {
                        log("\tnot migrating " + ((Organization) entity).getShortName());
                    }
                } catch (Exception e) {
                    logError("\terror when migrating " + ((Organization) entity).getShortName(), e);
                }
            }

            cursor.close();

            if (!testRun) {
                idcTransaction.commit();
            }

            log("Migration completed: migrated " + numMigratedEntities + " of a total of " + numEntities + " entities");
        } catch (Exception e) {
            logError("Error encountered during migration: ", e);
        }
        return true;
    }

    private boolean setupSystemPropertyLoader(IdcTransaction idcTransaction) throws IdcNoSuchObjectException {
        propertyLoader.setTransaction(idcTransaction.getUOW());
        return propertyLoader.preLoad();
    }

    @Override
    protected boolean migrateEntity(Entity entity) {
        boolean result;
        Organization org = (Organization) entity;
        Namespace namespace = org.getNamespace();
        String namespaceName = namespace.getName();
        if (!IdcUtilC.MAIN_NAMESPACE.equals(org.getShortName()) && !namespaces.add(namespaceName)) {
            log.error("Namespace Violation with org:" + org.getShortName() + ", Namespace:" + namespaceName);
            return false;
        }
        if ( IdcUtilC.MAIN_NAMESPACE.equals( org.getShortName() ) )
        {
            log.info("Skipping MAIN org for DO stream creation.");
            return false;
        }
        BrokerOrganizationFunction brokerOrganizationFunction = org.getBrokerOrganizationFunction();
        if (brokerOrganizationFunction != null && !brokerOrganizationFunction.getStreams().isEmpty())
        {
            //if there already exists stream and DO stream is not present, add it.
            result = brokerOrganizationFunction.getStream(ServerMBean.DEFAULT_DO_STREAM_NAME) == null;
        }
        else
        {
            result = !(org.isExternalProvider() || org.isECN());
        }
        if (!testRun) {
            if (result) {
                Stream stream = ModelFactory.getInstance().newStream();
                try {
                    stream.setShortName(ServerMBean.DEFAULT_DO_STREAM_NAME);
                    stream.setNamespace(namespace);
                    stream.setUser(org.getDefaultDealingUser());
                    stream.setLegalEntity(org.getDefaultDealingEntity());
                    stream.setDescription("Display Order Stream");
                    VirtualServer virtualServer = org.getVirtualServer();
                    if (virtualServer != null) {
                        stream.setVirtualServerName(virtualServer.getName());
                    }
                    addStream(org, stream);
                } catch (Exception ex) {
                    log.error("Exception generated while migrating org:" + org.getShortName(), ex);
                    throw new RuntimeException(ex);
                }
            } else {
                if (brokerOrganizationFunction == null || brokerOrganizationFunction.getStream(ServerMBean.DEFAULT_DO_STREAM_NAME) == null) {
                    try {
                        log.info( "Adding NoStream Override property for org=" + org );
                        HashMap<String, String> props = new HashMap<String, String>(6);
                        props.put(SHORT_NAME_KEY, new StringBuilder(60).append("IDC.IS.").append(org.getShortName()).append(".NoStream.Override").toString());
                        props.put(SystemPropertyLoaderC.VIRTUAL_SERVER_KEY, "");
                        props.put(SystemPropertyLoaderC.SERVER_GROUP_KEY, "");
                        props.put(SystemPropertyLoaderC.SCOPE_KEY, SystemPropertyC.SCOPE_TYPE_GLOBAL);
                        props.put(SystemPropertyLoaderC.VALUE_KEY, "true");
                        propertyLoader.loadSystemProp(props);
                    } catch (Exception ex) {
                        log.error("Exception while creating NoStream override property for Org:" + org.getShortName(), ex);
                        throw new RuntimeException(ex);
                    }
                }
            }
        }
        return result;
    }

    private Stream addStream(Organization organization, Stream stream) {
        IdcTransaction idcTransaction = null;
        IdcTransaction exTx = IdcSessionManager.getInstance().getTransaction();
        try {
            log.info( "DSCM.addStream : adding display order stream to org=" + organization );
            idcTransaction = IdcSessionManager.getInstance().newTransaction();
            stream = AdminTransactionUtil.register(stream);
            organization = AdminTransactionUtil.register(organization);
            stream.setNamespace( organization.getNamespace() );
            BrokerOrganizationFunction brokerOrganizationFunction = organization.getBrokerOrganizationFunction();
            if (null == brokerOrganizationFunction) {
                brokerOrganizationFunction = ORGANIZATION_SERVICE.addOrganizationFunction(organization);
            }
            brokerOrganizationFunction = AdminTransactionUtil.register(brokerOrganizationFunction);
            STREAM_SERVICE.addStream(brokerOrganizationFunction, stream);
            if (null == brokerOrganizationFunction.getDefaultStream()) {
                ORGANIZATION_SERVICE.setDefaultStream(brokerOrganizationFunction, stream);
            }
            idcTransaction.commit();
        } catch (Exception e) {
            log.error("Failed to add Streams from org " + organization.getShortName(), e);
            if ( exTx == null && idcTransaction != null)
            {
                idcTransaction.release();
            }
            throw new RuntimeException(e);
        }
        finally
        {
            if ( exTx == null && idcTransaction != null )
            {
                idcTransaction.release();
            }
        }
        return stream;
    }

    @Override
    protected ReadAllQuery getQuery() {
        ReadAllQuery raq = new ReadAllQuery();
        raq.setReferenceClass(OrganizationC.class);
        return raq;
    }

    /**
     * @param args
     */
    public static void main(String[] args) {
        DOStreamCreationMigration loader = new DOStreamCreationMigration("DOStreamCreationMigration");
        loader.log.warn("DOStreamCreationMigration creating DO Stream for FI orgs and multi-stream LP orgs. ");
        try {
            loader.setOptions(args);
            loader.run();
        } catch (Exception ex) {
            loader.logError("DOStreamCreationMigration Exception ", ex);
        }
    }
}
