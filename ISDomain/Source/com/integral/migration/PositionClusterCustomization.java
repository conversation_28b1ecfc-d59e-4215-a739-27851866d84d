package com.integral.migration;

import com.integral.model.cluster.ClusterMetaData;
import com.integral.model.cluster.ResourceInfo;
import com.integral.services.cluster.CustomMetaDataProvider;
import com.integral.trade.notification.config.TradeNotificationConfigMBean;
import com.integral.trade.notification.config.TradeNotificationConfigurationFactory;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 1/25/16
 * Time: 10:22 AM
 * To change this template use File | Settings | File Templates.
 */
public class PositionClusterCustomization implements CustomMetaDataProvider {

    private static final String TOPIC = "topic";

    @Override
    public void updateMetaData(ClusterMetaData clusterMetaData) {
        TradeNotificationConfigMBean tradeNotificationConfig = TradeNotificationConfigurationFactory.getTradeNotificationConfig();
        if (null != tradeNotificationConfig) {
            int concurrencyFactor = tradeNotificationConfig.getConcurrencyFactor();
            if (concurrencyFactor > 0) {
                List<ResourceInfo> resources = clusterMetaData.getResources();
                if (null != resources) {
                    for (ResourceInfo resource : resources) {
                        if (resource.getName().equalsIgnoreCase(TOPIC)) {
                            resource.getPayloadInfo().setCount(concurrencyFactor);
                        }
                    }
                }
            }
        }


    }
}
