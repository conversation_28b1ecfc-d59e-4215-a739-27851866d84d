package com.integral.migration;

import com.integral.broker.model.*;
import com.integral.dbms.migration.CursorMigrationC;
import com.integral.jmsx.JMSDBMBeanC;
import com.integral.jmsx.JMSManager;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.persistence.Entity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;
import org.eclipse.persistence.queries.ReadAllQuery;

import java.util.Collection;
import java.util.List;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 7/17/17
 */
public class MMConfigurationMigration extends CursorMigrationC {

    public MMConfigurationMigration(String name) {
        super(name);
    }

    @Override
    protected boolean migrateEntities(){
        try{
            startTransaction();
            List<String> mmBrokers = MarketMakerConfig.getInstance().getMarketMakerEnabledBrokers();
            log.info("MMConfigurationMigration.migration:: orgs=" + mmBrokers);
            for(String orgName : mmBrokers){
                log.info("MMConfigurationMigration.migration:: processing org " + orgName);
                Organization organization = ReferenceDataCacheC.getInstance().getOrganization(orgName);
                if(organization == null) {
                    log.info("MMConfigurationMigration.migration:: org is null " + orgName);
                    continue;
                }
                BrokerOrganizationFunction bof = organization.getBrokerOrganizationFunction();
                if(bof == null){
                    log.info("MMConfigurationMigration.migration:: bof is null " + orgName);
                    continue;
                }
                Collection<Stream> streams = bof.getStreams();
                for(Stream stream : streams){
                    log.info("MMConfigurationMigration.migration:: processing stream " + stream.getShortName());
                    Collection<Configuration> configs = stream.getConfigurations();
                    for(Configuration config : configs){
                        if(config.isMarketMaker()){
                            Configuration registeredObj = (Configuration) getTransaction().registerObject(config);
                            registeredObj.setRFSSpreadPreserved(true);
                            log.info("MMConfigurationMigration.migration:: processed config " + config.getShortName());
                        }else {
                            log.info("MMConfigurationMigration.migration:: not MM, skipping " + config.getShortName());
                        }
                    }
                }
            }
            endTransaction(true);
            log.info("MMConfigurationMigration.migration:: Successfully migrated MM Config RFS Exec Rules.");
            return true;
        }catch (Exception e){
            endTransaction(false);
            log.error("MMConfigurationMigration.migration:: Exception during running migration", e);
            return false;
        }
    }

    @Override
    protected boolean preloadData() {
        JMSDBMBeanC._getInstance();
        JMSManager.initStandalone();
        return true;
    }

    @Override
    protected boolean migrateEntity(Entity entity) {
        return false;
    }

    @Override
    protected ReadAllQuery getQuery() {
        return null;
    }

    public static void main(String[] args) throws Exception {
        MMConfigurationMigration script = new MMConfigurationMigration("MMConfigurationMigration");
        try
        {
            script.setOptions( args );
            script.run();
        }
        catch ( Exception ex )
        {
            script.log.error( "Exception", ex );
        }
    }
}
