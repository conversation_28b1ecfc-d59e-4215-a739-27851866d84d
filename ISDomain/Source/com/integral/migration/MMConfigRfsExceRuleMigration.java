package com.integral.migration;

import com.integral.broker.model.*;
import com.integral.dbms.migration.CursorMigrationC;
import com.integral.jmsx.JMSDBMBeanC;
import com.integral.jmsx.JMSManager;
import com.integral.marketmaker.config.MarketMakerConfig;
import com.integral.persistence.Entity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;
import org.eclipse.persistence.queries.ReadAllQuery;

import java.util.Collection;
import java.util.List;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 7/17/17
 */
public class MMConfigRfsExceRuleMigration extends CursorMigrationC {

    public MMConfigRfsExceRuleMigration(String name) {
        super(name);
    }

    @Override
    protected boolean migrateEntities(){
        try{
            startTransaction();
            List<String> mmBrokers = MarketMakerConfig.getInstance().getMarketMakerEnabledBrokers();
            log.info("MMConfigRfsExceRuleMigration.migration:: orgs=" + mmBrokers);
            for(String orgName : mmBrokers){
                log.info("MMConfigRfsExceRuleMigration.migration:: processing org " + orgName);
                Organization organization = ReferenceDataCacheC.getInstance().getOrganization(orgName);
                if(organization == null) {
                    log.info("MMConfigRfsExceRuleMigration.migration:: org is null " + orgName);
                    continue;
                }
                BrokerOrganizationFunction bof = organization.getBrokerOrganizationFunction();
                if(bof == null){
                    log.info("MMConfigRfsExceRuleMigration.migration:: bof is null " + orgName);
                    continue;
                }
                Collection<Stream> streams = bof.getStreams();
                for(Stream stream : streams){
                    log.info("MMConfigRfsExceRuleMigration.migration:: processing stream " + stream.getShortName());
                    Collection<Configuration> configs = stream.getConfigurations();
                    for(Configuration config : configs){
                        if(config.isMarketMaker()){
                            log.info("MMConfigRfsExceRuleMigration.migration:: processing config " + config.getShortName());
                            updateEspExecutionRule(config);
                            updateRfsExecutionRule(config);
                        }else {
                            log.info("MMConfigRfsExceRuleMigration.migration:: not MM, skipping " + config.getShortName());
                        }
                    }
                }
            }
            endTransaction(true);
            log.info("MMConfigRfsExceRuleMigration.migration:: Successfully migrated MM Config RFS Exec Rules.");
            return true;
        }catch (Exception e){
            endTransaction(false);
            log.error("MMConfigRfsExceRuleMigration.migration:: Exception during running migration", e);
            return false;
        }
    }

    private void updateRfsExecutionRule(Configuration config){
        try{
            Collection<ExecutionRuleCondition> ercs = config.getRFSExecutionRule().getExecutionRuleConditions();
            for(ExecutionRuleCondition erc : ercs){
                ExecutionRuleCondition registeredObj = (ExecutionRuleCondition) getTransaction().registerObject(erc);
                registeredObj.setFullFillEnabled(true);
                registeredObj.setFullFill(100);
                registeredObj.setAutoCoverEnabled(true);
                log.info("MMConfigRfsExceRuleMigration.migration:: updated rfs exec rule " + config.getShortName() + " " + erc.getSortOrder());
            }
        }catch (Exception e){
            log.warn("MMConfigRfsExceRuleMigration.migration:: exception while updating rfs exec rule, skipping " + config.getShortName(), e);
        }
    }

    private void updateEspExecutionRule(Configuration config){
        try{
            Collection<ExecutionRuleCondition> ercs = config.getESPExecutionRule().getExecutionRuleConditions();
            if(ercs.size() > 1){
                log.error("MMConfigRfsExceRuleMigration.migration:: more than one esp exec rule " + config.getShortName() + " " + ercs.size());
            }
            ExecutionRuleCondition lastRule = null;
            for(ExecutionRuleCondition erc : ercs){
                ExecutionRuleCondition registeredObj = (ExecutionRuleCondition) getTransaction().registerObject(erc);
                ExecutionMethod executionMethod = registeredObj.getExecutionMethod();
                boolean isMarket = executionMethod != null && (executionMethod.equals(ExecutionMethod.MarketIOC) || executionMethod.equals(ExecutionMethod.MarketFOK));
                ExecutionMethodParameters methodParameters = registeredObj.getExecutionMethodParameters();
                methodParameters.setRangeEnabled(!isMarket);
                if(lastRule == null) lastRule = registeredObj;
                if(lastRule.getSortOrder() < registeredObj.getSortOrder()) lastRule = registeredObj;
                log.info("MMConfigRfsExceRuleMigration.migration:: updated esp exec rule " + config.getShortName() + " " + erc.getSortOrder());
            }
            if(lastRule != null){
                int i = 2147483647;
                lastRule.setSortOrder(i);
            }
        }catch (Exception e){
            log.warn("MMConfigRfsExceRuleMigration.migration:: exception while updating esp exec rule, skipping " + config.getShortName(), e);
        }
    }

    @Override
    protected boolean preloadData() {
        JMSDBMBeanC._getInstance();
        JMSManager.initStandalone();
        return true;
    }

    @Override
    protected boolean migrateEntity(Entity entity) {
        return false;
    }

    @Override
    protected ReadAllQuery getQuery() {
        return null;
    }

    public static void main(String[] args) throws Exception {
        MMConfigRfsExceRuleMigration script = new MMConfigRfsExceRuleMigration("MMConfigRfsExceRuleMigration");
        try
        {
            script.setOptions( args );
            script.run();
        }
        catch ( Exception ex )
        {
            script.log.error( "Exception", ex );
        }
    }
}
