package com.integral.migration;

import com.integral.is.common.MetaspacesShutdownTask;
import com.integral.is.common.MetaspacesStartupC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.jmsx.JMSDBMBeanC;
import com.integral.jmsx.JMSManager;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.TransactionIdService;
import com.integral.persistence.TransactionIdServiceC;
import com.integral.persistence.TransactionIdServiceFactory;
import com.integral.rds.config.ServiceConfigMBeanC;
import com.integral.rds.spaces.PersistenceServiceFactory;
import com.integral.spaces.services.SpacesIDService;
import com.integral.spaces.services.SpacesIDServiceFactory;

public class SequenceIdMigration {
	
	static Log log = LogFactory.getLog(SequenceIdMigration.class);
	
	public static void main(String[] args) {
		log.info("SIDM.main start");
		long start = System.currentTimeMillis();
		init();
		migrate();
		log.info("SIDM.main end. time taken="+(System.currentTimeMillis()-start));
		shutdown();
	}
	
	private static void init(){
		JMSDBMBeanC._getInstance();
        JMSManager.initStandalone();
    	MetaspacesStartupC msc = new MetaspacesStartupC();
    	try {
			msc.startup(null, null);
		} catch (Exception e) {
			log.error("Error in initializing metaspaces:" + e.getMessage(), e);
		}
	}
	
	private static void shutdown(){
		try{
			new MetaspacesShutdownTask().shutdown("", null);
			System.exit(0);
		}catch(Exception e){
			e.printStackTrace();
		}
	}

	private static void migrate() {
		
		try{
			TransactionIdService oracleService = TransactionIdServiceFactory.getTransactionIdService();
			
			//this schema is hard-coded everywhere.
			String oid_schema = "IS";
			String tid_schema = ISFactory.getInstance().getISMBean().getTransactionIDAppName();
			
			//1. find out current value of OId and TId sequences, they will be basis for mongo based ids.  
			long oid = oracleService.nextOracleSequence(oid_schema,TransactionIdServiceC.ORDERID_SEQUENCE_PREFIX);
			
			long tid = oracleService.nextOracleSequence(tid_schema, TransactionIdServiceC.TRANSACTIONID_SEQUENCE_PREFIX);
			
			String oid_collection_name = TransactionIdServiceC.ORDERID_SEQUENCE_PREFIX + oid_schema;
			String tid_collection_name = TransactionIdServiceC.TRANSACTIONID_SEQUENCE_PREFIX + tid_schema;

			int connectionSize = ServiceConfigMBeanC.getInstance().getAutoIDSeqConnections();
			String dbID = ServiceConfigMBeanC.getInstance().getAutoIDSeqName();
			String metaSpaceName = PersistenceServiceFactory.getRDSMetaspaceName();
			SpacesIDServiceFactory.getInstance().init(connectionSize,metaSpaceName, dbID);
			int temp_db_increment_factor = 50000;
			int seq_increment = 500;
			
			//seed values
			long jump_factor = ISFactory.getInstance().getISMBean().getSequenceIdJumpByFactor();
			long oid_seed = oid + jump_factor;
			long tid_seed = tid + jump_factor;
			
			//create service and initialize it.
			SpacesIDService oidservice = SpacesIDServiceFactory.getInstance().getSpacesIDService(oid_collection_name,temp_db_increment_factor,seq_increment,oid_seed,true);
			SpacesIDService tidservice = SpacesIDServiceFactory.getInstance().getSpacesIDService(tid_collection_name,temp_db_increment_factor,seq_increment,tid_seed,true);
			
			long newoid = oidservice.getNextSequence();
			
			long newtid = tidservice.getNextSequence();
			
			log.info("SIdM.migrate OID sequencce migration completed successfully. OracleId" + oid+
					", jumpBy="+jump_factor + 
					", mongoId="+newoid+
					", collection=" + oid_collection_name +
					", seed="+oid_seed +
					", dbId" +dbID+
					", ms=" + metaSpaceName);
			
			log.info("SIdM.migrate TID sequencce migration completed successfully. OracleId" + tid+
					", jumpBy="+jump_factor + 
					", mongoId="+newtid+
					", collection=" + tid_collection_name +
					", seed="+tid_seed +
					", dbId" +dbID+
					", ms=" + metaSpaceName);
			
		}catch(Exception e){
			log.error("SIdM.migrate failed. ", e);
		}
		
	}

}
