package com.integral.migration;

import com.integral.commons.Tuple;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.MDFServiceC;
import com.integral.mdf.MDFServiceImpl;
import com.integral.persistence.Entity;

/**
 * Created by raghunathans on 7/21/17.
 */
public class MDFClusterMigration  extends AbstractRDSConfigMigration  {

    private static final Log log = LogFactory.getLog(MDFClusterMigration.class);

    public MDFClusterMigration(String name, String userPrefix,String filePath) {
        super(name);
    }

    @Override
    protected boolean migrateEntity(Entity entity) {
        MDFServiceC mdfServiceC = new MDFServiceImpl();
        Tuple<String,String> mdfClusters = mdfServiceC.createMDFClusters();
        if(mdfClusters.first.equals("OK")){
            log.info("MDFClusterMigration:Successfully created MDF clusters:"+mdfClusters.second);
        }else{
            log.info("MDFClusterMigration:Failed during the creation of MDF clusters:"+mdfClusters.second);
        }
        return true;
    }

    public static void main(String[] args) {
        log.info(" PERFORMING MDF Cluster migration:Adding MDF cluster for venues");

        MDFClusterMigration loader = new MDFClusterMigration(
                "MDFClusterMigration",null,null);
        try {
            loader.setOptions(args);
            loader.run();
        } catch (Exception ex) {
            loader.logError("", ex);
        }
        log.info(" PERFORMED MDF Cluster migration: Successfully created MDF clusters for venues");
    }

}
