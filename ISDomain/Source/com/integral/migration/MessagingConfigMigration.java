package com.integral.migration;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.messaging.config.MessagingConfigUtil;
import com.integral.messaging.config.MessagingConfiguration;
import com.integral.messaging.config.model.ClusterConfiguration;
import com.integral.messaging.config.model.ExchangeConfiguration;
import com.integral.messaging.config.model.VirtualHostConfiguration;
import com.integral.persistence.Entity;
import com.integral.rds.config.ServerConfigMBeanC;
import com.integral.rds.message.RDSResponse;
import com.integral.rds.message.RDSResponse.Status;
import com.integral.rds.persistence.PersistenceServiceC;
import com.integral.rds.spaces.PersistenceServiceFactory;
import com.integral.spaces.notification.NotificationConfiguration;
import com.integral.spaces.services.SpacesIDServiceFactory;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.ServerRuntimeMBean;

import java.io.FileReader;

public class MessagingConfigMigration extends AbstractRDSConfigMigration {

    private static final Log log = LogFactory
            .getLog(MessagingConfigMigration.class);

    private String filePath;
    private String userPrefix;

    public MessagingConfigMigration(String name, String userPrefix,
                                    String filePath) {
        super(name);
        this.userPrefix = userPrefix;
        this.filePath = filePath;
    }

    @Override
    protected boolean migrateEntity(Entity entity) {
        SpacesIDServiceFactory.getInstance().init(10,
                PersistenceServiceFactory.getRDSMetaspaceName(), "RDS-SEQ");
        try {
            // initialize the configuration
            MessagingConfiguration.getInstance().init(userPrefix,
                    new FileReader(filePath));

			// set the Cluster address for Default, RDS and GM
			String clusterAddress = NotificationConfiguration.getInstance()
					.getCluster();
			ClusterConfiguration defaultClusterConfiguration = MessagingConfiguration
					.getInstance().getClusterConfiguration(
							ServerRuntimeMBean.DEFAULT_CLUSTER);
			defaultClusterConfiguration.setClusterAddress(clusterAddress);
            
			// RDS exchange configuration
			ServerConfigMBeanC.getInstance();
			String rdsClusterAddress = ServerConfigMBeanC
					.getServiceConfigMBean().getAMQServerUrl();
			ClusterConfiguration rdsClusterConfig = MessagingConfiguration
					.getInstance().getClusterConfiguration(
							ServerRuntimeMBean.RDS_CLUSTER);
			rdsClusterConfig.setClusterAddress(rdsClusterAddress);

			// GM Cluster
			String gmClusterAddress = RuntimeFactory.getServerRuntimeMBean()
					.getGMNotificationAMQPUrl();
			ClusterConfiguration gmClusterConfig = MessagingConfiguration
					.getInstance().getClusterConfiguration(
							ServerRuntimeMBean.GM_CLUSTER);
			gmClusterConfig.setClusterAddress(gmClusterAddress);
			
			
			// IWS Cluster
			String iwsClusterAddress = ServerConfigMBeanC.getServiceConfigMBean().getAMQServerUrl();
			ClusterConfiguration iwsClusterConfig = MessagingConfiguration.getInstance().getClusterConfiguration(
							"IWS-CLUSTER");
			iwsClusterConfig.setClusterAddress(iwsClusterAddress);
            
            for (ClusterConfiguration clusterConfig : MessagingConfiguration
                    .getInstance().getAllClusterConfiguration()) {
                ClusterConfiguration existingClusterConfig = (ClusterConfiguration) PersistenceServiceC
                        .getInstance().getEntityByObjectId(
                                ClusterConfiguration.class,
                                clusterConfig.getShortName(),
                                MessagingConfigUtil.NAMESPACE, 1);

                RDSResponse rdsResponse;
                if (existingClusterConfig == null) {
                    rdsResponse = PersistenceServiceC.getInstance()
                            .createEntity(clusterConfig, "CREATE");
                } else {
                    rdsResponse = PersistenceServiceC.getInstance()
                            .updateEntity(clusterConfig, "UPDATE");
                }
                if (rdsResponse.getStatus() == Status.SUCCESS) {
                    log.info("Successfully created/updated Cluster configuration for:"
                            + clusterConfig.getShortName()
                            + " @ "
                            + clusterConfig.getClusterAddress()
                            + ":With Virtual Hosts:");
                    for (VirtualHostConfiguration vHostConfig : clusterConfig
                            .getVirtualHostConfigurations()) {
                        log.info("	Virtual Host:" + vHostConfig.getShortName()
                                + ":With Exchanges:");
                        for (ExchangeConfiguration exchangeConfig : vHostConfig
                                .getExchangeConfigurations()) {
                            log.info("		Exchange:"
                                    + exchangeConfig.getShortName() + ":Type:"
                                    + exchangeConfig.getType());
                        }
                    }
                } else {
                    throw new Exception(
                            "Creation/Updation of Messaging Configuration failed:Cause:"
                                    + rdsResponse.getText());
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }

        return true;
    }

    public static void main(String[] args) {
        log.info(" PERFORMING Messaging ConfigMigration:Adding/Updating Messaging configurations");

        if (args.length != 2) {
            log.error("Insufficient parameters, run as java MessagingConfigMigration user_prefix filepath");
            System.exit(-1);
        }

        MessagingConfigMigration loader = new MessagingConfigMigration(
                "MessagingConfigMigration", args[0], args[1]);
        try {
            loader.setOptions(args);
            loader.run();
        } catch (Exception ex) {
            loader.logError("", ex);
        }
        log.info(" PERFORMED Messaging Config Migration: Successfully updated Messaging configurations");
    }

}
