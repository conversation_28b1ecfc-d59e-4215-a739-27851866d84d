package com.integral.services;

import java.net.InetAddress;
import java.net.UnknownHostException;

import com.integral.services.config.ServicesMBean;
import com.integral.system.configuration.IdcMBeanC;

public class ServiceContainerMBean extends IdcMBeanC implements ServicesMBean 
{
	private static final ServiceContainerMBean instance;
	
	private String ZOO_KEEPER_CONNECT_STRING = "IDC.SERVICES.Zoo.Keeper.Connect.String";
	private String SERVICES_NAMESPACE = "IDC.SERVICES.NameSpace";
	private String INSTANCE_ID = "IDC.VirtualServer.Name";
	private String ZOO_KEEPER_SESSION_TIMEOUT_MILLISECONDS = "IDC.SERVICES.Zoo.Keeper.Session.Timeout.Milliseconds";
	private String ZOO_KEEPER_CONNECTION_TIMEOUT_MILLISECONDS = "IDC.SERVICES.Zoo.Keeper.Connection.Timeout.Milliseconds";
	private String SERVICES_PORT = "IDC.SERVICES.PORT";
	
	private String ZOO_KEEPER_STATUS_LISTENER_POOL_SIZE = "IDC.SERVICES.Zoo.Keeper.Status.Listener.Pool.Size";

	private String ZOO_KEEPER_EVENT_LISTENER_POOL_SIZE = "IDC.SERVICES.Zoo.Keeper.Event.Listener.Pool.Size";

    private String ZOO_KEEPER_SASL_AUTH_ENABLED = "IDC.SERVICES.Zoo.Keeper.SASL.Auth.Enabled";

    private String SERVICES_CLUSTER_CONTROLLER_ENABLED = "IDC.SERVICES.Cluster.Controller.Enabled";
	
	private static final String DEFAULT_ZOO_KEEPER_CONNECT_STRING = "127.0.0.1:2181";
	private static final String DEFAULT_SERVICES_NAMESPACE = "Integral.Services";
	private static final int DEFAULT_ZOO_KEEPER_SESSION_TIMEOUT_MILLISECONDS = 2000;
	private static final int DEFAULT_ZOO_KEEPER_CONNECTION_TIMEOUT_MILLISECONDS = 15000;
	private static final int DEFAULT_SERVICES_PORT = 2181;
    private static final boolean DEFAULT_ZOO_KEEPER_SASL_AUTH_ENABLED = false;
    private static final boolean DEFAULT_SERVICES_CLUSTER_CONTROLLER_ENABLED=false;
	
    private String zookeeperConnectString;
    private String servicesNamespace;
    private String instanceId;
    private int port;
    private ServiceTransport serviceTransport = ServiceTransport.IMTP;
    private String address = getLocalServerName();
    private int zookeeperSessionTimeoutMilliseconds;
    private int zookeeperConnectionTimeoutMilliseconds;
    private boolean isSASLAuthEnabled;
    private boolean isClusterControllerEnabled;

    static
    {
    	instance = new ServiceContainerMBean();
    	instance.initialize();
    }
    
    public static ServiceContainerMBean getInstance()
    {
    	return instance;
    }
    
    @Override
    public void initialize() {
    	zookeeperConnectString = getStringProperty( ZOO_KEEPER_CONNECT_STRING, DEFAULT_ZOO_KEEPER_CONNECT_STRING );
    	servicesNamespace = getStringProperty( SERVICES_NAMESPACE, DEFAULT_SERVICES_NAMESPACE );
    	instanceId = getStringProperty( INSTANCE_ID, "" );
    	port = getIntProperty( SERVICES_PORT, DEFAULT_SERVICES_PORT );
    	zookeeperSessionTimeoutMilliseconds = getIntProperty( ZOO_KEEPER_SESSION_TIMEOUT_MILLISECONDS, DEFAULT_ZOO_KEEPER_SESSION_TIMEOUT_MILLISECONDS );
        zookeeperConnectionTimeoutMilliseconds = getIntProperty(ZOO_KEEPER_CONNECTION_TIMEOUT_MILLISECONDS,DEFAULT_ZOO_KEEPER_CONNECTION_TIMEOUT_MILLISECONDS);
        isSASLAuthEnabled = getBooleanProperty(ZOO_KEEPER_SASL_AUTH_ENABLED,DEFAULT_ZOO_KEEPER_SASL_AUTH_ENABLED);
        isClusterControllerEnabled =  getBooleanProperty(SERVICES_CLUSTER_CONTROLLER_ENABLED,DEFAULT_SERVICES_CLUSTER_CONTROLLER_ENABLED);
    }
    
    public String getZookeeperConnectString() {
        return zookeeperConnectString;
    }

    public void setZookeeperConnectString(String zookeeperConnectString) {
        this.zookeeperConnectString = zookeeperConnectString;
    }

    public String getServicesNamespace() {
        return servicesNamespace;
    }

    @Override
    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public void setServicesNamespace(String servicesNamespace) {
        this.servicesNamespace = servicesNamespace;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public ServiceTransport getServiceTransport() {
        return serviceTransport;
    }

    public void setServiceTransport(ServiceTransport serviceTransport) {
        this.serviceTransport = serviceTransport;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public static String getLocalServerName() {
        String hostname = null;
        try {
            hostname = InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            //log.warn("ServicesMBean: ServerHostAddress is not found");
        }
        return hostname;
    }

    public boolean isClusterControllerEnabled() {
        return isClusterControllerEnabled;
    }

    @Override
	public int getZookeeperSessionTimeoutMilliseconds() {
		return zookeeperSessionTimeoutMilliseconds;
	}

    @Override
    public int getConnectionTimeoutMilliseconds() {
        return zookeeperConnectionTimeoutMilliseconds;
    }

    @Override
	public int getDefaultServicePort() {
		return 0;
	}

	@Override
	public ServiceTransport getDefaultServiceTransport() {
		return ServiceTransport.NONE;
	}

	@Override
	public boolean isTestMode() {
		return false;
	}

    public boolean isSASLAuthEnabled(){
        return isSASLAuthEnabled;
    }

	@Override
	public String getHostName() {
		return address;
	}
}
