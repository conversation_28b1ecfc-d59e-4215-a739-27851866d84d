package com.integral.services;

import java.util.Collection;
import java.util.Hashtable;

import com.integral.imtp.config.IMTPConfigMBeanC;
import com.integral.imtp.config.IMTPConfigMBeanFactory;
import com.integral.imtp.config.IMTPVersion;
import com.integral.imtp.connection.Acceptor;
import com.integral.imtp.connection.IMTPConnectionManager;
import com.integral.is.common.mbean.ISFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.services.ServicesFactory;
import com.integral.services.container.ServiceContainer;
import com.integral.services.Service;
import com.integral.system.runtime.StartupTask;


public class ServiceContainerStartupC implements StartupTask {

	
	Log log = LogFactory.getLog( this.getClass() );
		
	
	@Override
	public String startup(String aName, Hashtable args) throws Exception{
		//mBean = ;
        boolean servicesEnabled = ISFactory.getInstance().getISMBean().isServicesEnabled();
        if(servicesEnabled) {
            log.info("Starting initialization of services containers");
            IMTPConfigMBeanFactory imtpConfigMBeanFactory = IMTPConfigMBeanFactory.getInstance();
            if(imtpConfigMBeanFactory.getIMTPConfigMBean()==null){
                imtpConfigMBeanFactory.setIMTPConfigMBean(IMTPConfigMBeanC.getInstance());
            }
            Acceptor acceptor = IMTPConnectionManager.getInstance().getAcceptor();
            try {
                acceptor.bind(); //Safe to call multiple times... would do nothing if already bound.
                log.info("IMTPStartupC: IMTP Acceptor started on port:" + acceptor.getBoundPort() + ", IMTPVersion:" + IMTPVersion.IMTP_VERSION_ID);
            } catch (Exception e) {
                log.error("IMTPStartupC: Unable to start IMTP Acceptor on port:" + acceptor.getIMTPConfig().getAcceptorPort(), e);
                throw e;
            }
            ServiceContainerMBean.getInstance().setPort(acceptor.getBoundPort());
            ServicesFactory.setServicesMBean(ServiceContainerMBean.getInstance());
            ServicesFactory.initRegistry();
            ServicesFactory.initContainer();
            ServicesFactory.startContainer();
            ServiceContainer container = ServicesFactory.getInstance().getServiceContainer();
            Collection<Service> registeredServices = container.getRegisteredServices();
            for (Service service : registeredServices) {
                log.info("Found one more service: " + service.getDescription() + ", Status:" + service.getStatus());
            }
        }else{
            log.info("Services initialization is disabled on this server, not starting the containers");
        }
		return "true";
	}
	

	
	
}
