package com.integral.services.api.ccyexchange;

import com.integral.rex.xchng.CcyExchangeConfig;
import com.integral.services.RunningServiceInstanceIdentity;
import com.integral.services.ServiceConfiguration;
import com.integral.services.filter.ServiceInstanceFilter;

/**
 * User: jainv
 * Date: 8/20/14
 * Time: 2:25 PM
 */
public class RexFilter implements ServiceInstanceFilter<CcyExchangeConfig>
{

    private final String shortName;
    private final String ccyPair;
    private final CcyExchangeConfig config;

    public RexFilter(String shortName,String ccyPair){
        this.shortName = shortName;
        this.ccyPair = ccyPair;
        this.config = new CcyExchangeConfig();
        config.setRexName(shortName);
        config.setCcyPair(ccyPair);
        config.setUniqueServiceConfigId(shortName+"----"+ccyPair);
    }

    @Override
    public boolean apply( RunningServiceInstanceIdentity<CcyExchangeConfig> service )
    {
        ServiceConfiguration configuration = (ServiceConfiguration) service.getConfiguration();
        return configuration.getUniqueServiceConfigId().equals(config.getUniqueServiceConfigId());
    }
}
