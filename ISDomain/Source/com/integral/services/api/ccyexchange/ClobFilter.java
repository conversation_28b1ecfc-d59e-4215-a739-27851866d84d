package com.integral.services.api.ccyexchange;

import com.integral.clob.api.ClobExchangeConfig;
import com.integral.services.RunningServiceInstanceIdentity;
import com.integral.services.ServiceConfiguration;
import com.integral.services.filter.ServiceInstanceFilter;

/**
 * Created by dasp on 5/1/2015.
 */
public class ClobFilter implements ServiceInstanceFilter<ClobExchangeConfig>
{

    private final String shortName;
    private final String ccyPair;
    private final ClobExchangeConfig config;

    public ClobFilter( String shortName, String ccyPair )
    {
        this.shortName = shortName;
        this.ccyPair = ccyPair;
        this.config = new ClobExchangeConfig();
        config.setClobName( shortName );
        config.setCcyPair( ccyPair );
        config.setUniqueServiceConfigId( shortName + "----" + ccyPair );
    }

    @Override
    public boolean apply( RunningServiceInstanceIdentity<ClobExchangeConfig> service )
    {
        ServiceConfiguration configuration = ( ServiceConfiguration ) service.getConfiguration();
        return configuration.getUniqueServiceConfigId().equals( config.getUniqueServiceConfigId() );
    }

}
