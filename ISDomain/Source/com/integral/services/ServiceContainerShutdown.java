package com.integral.services;

import com.integral.is.common.mbean.ISFactory;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.services.ServicesFactory;
import com.integral.spaces.Metaspaces;
import com.integral.system.runtime.ShutdownTask;

import java.util.Hashtable;

/**
 * Created by pulkit on 2/24/15.
 */
public class ServiceContainerShutdown implements ShutdownTask {
    protected Log log = LogFactory.getLog(ServiceContainerShutdown.class);

    public String shutdown(String aName, Hashtable args) {
        log.info("Shutting down Services");
        boolean servicesEnabled = ISFactory.getInstance().getISMBean().isServicesEnabled();
        if (servicesEnabled) {
            ServicesFactory.stopContainer();
            ServicesFactory.destroyContainer();
            ServicesFactory.destroyRegistry();
            log.info("Services shutdown complete");
        }
        return "Services Shutdown".trim();
    }
}