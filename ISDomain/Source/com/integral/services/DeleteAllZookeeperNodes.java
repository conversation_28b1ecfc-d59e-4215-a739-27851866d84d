package com.integral.services;

import com.integral.imtp.config.IMTPVersion;
import com.integral.imtp.connection.Acceptor;
import com.integral.imtp.connection.IMTPConnectionManager;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.services.registry.ZookeeperServiceRegistry;

/**
 * Created by pulkit on 3/19/15.
 */
public class DeleteAllZookeeperNodes {

    private static Log log = LogFactory.getLog(DeleteAllZookeeperNodes.class);

    public static void main(String[] args) {
        log.info("Deleting all zookeeper nodes.");
        try {
            log.info("Starting initialization of services containers");
            Acceptor acceptor = IMTPConnectionManager.getInstance().getAcceptor();
            try {
                acceptor.bind(); //Safe to call multiple times... would do nothing if already bound.
                log.info("IMTPStartupC: IMTP Acceptor started on port:" + acceptor.getBoundPort() + ", IMTPVersion:" + IMTPVersion.IMTP_VERSION_ID);
            } catch (Exception e) {
                log.error("IMTPStartupC: Unable to start IMTP Acceptor on port:" + acceptor.getIMTPConfig().getAcceptorPort(), e);
            }
            ServiceContainerMBean.getInstance().setPort(acceptor.getBoundPort());
            ServicesFactory.setServicesMBean(ServiceContainerMBean.getInstance());
            ServicesFactory.initRegistry();
            ServicesFactory.initContainer();
            ServicesFactory.startContainer();
            ((ZookeeperServiceRegistry) ServicesFactory.getInstance().getServiceContainer().getServiceRegistry()).deleteAllNodes();

        } catch (Exception ex) {
            log.error("Unable to delete zookeeper nodes", ex);
        }
        System.exit(0);
    }
}
