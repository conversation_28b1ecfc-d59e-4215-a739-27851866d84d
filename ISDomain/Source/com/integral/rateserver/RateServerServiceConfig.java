package com.integral.rateserver;

import com.integral.services.ServiceConfiguration;
import com.integral.services.ServiceLevel;

public class RateServerServiceConfig extends ServiceConfiguration {

	private int id;

	private String mvContainerName;

	public RateServerServiceConfig() {
		super("");
	}

	public RateServerServiceConfig(String serviceConfigurationId) {
		super(serviceConfigurationId);
	}

	public RateServerServiceConfig(ServiceLevel serviceLevel, String serviceConfigurationId) {
		super(serviceLevel, serviceConfigurationId);
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getMvContainerName() {
		return mvContainerName;
	}

	public void setMvContainerName(String mvContainerName) {
		this.mvContainerName = mvContainerName;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((mvContainerName == null) ? 0 : mvContainerName.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RateServerServiceConfig other = (RateServerServiceConfig) obj;
		if (mvContainerName == null) {
			if (other.mvContainerName != null)
				return false;
		} else if (!mvContainerName.equals(other.mvContainerName))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "RateServerServiceConfig [id=" + id + ", mvContainerName=" + mvContainerName + "]";
	}

	
}
