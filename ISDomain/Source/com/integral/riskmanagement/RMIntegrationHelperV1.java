package com.integral.riskmanagement;

import com.integral.broker.util.SyntheticCrossUtil;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.trade.TradeClassification;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.model.dealing.SingleLegTrade;

public class RMIntegrationHelperV1 implements RMIntegrationHelper{
    @Override
    public LegalEntity getCounterPartyLegalEntity(SingleLegTrade singleLegTrade) {
        return DealingModelUtil.getCounterPartyLegalEntity(singleLegTrade);
    }

    @Override
    public double calculateSyntheticCrossCompBaseAmt(CurrencyPair synCP, CurrencyPair compCP, double syntheticBaseAmt, double syntheticTermAmt, double vehicleCCYAmount) {
        return SyntheticCrossUtil.calculateCompBaseAmt(synCP,compCP,syntheticBaseAmt,syntheticTermAmt,vehicleCCYAmount);
    }

    @Override
    public double calculateSyntheticCrossCompTermAmt(CurrencyPair synCP, CurrencyPair compCP, double syntheticBaseAmt, double syntheticTermAmt, double vehicleCCYAmount) {
        return SyntheticCrossUtil.calculateCompTermAmt(synCP,compCP,syntheticBaseAmt,syntheticTermAmt,vehicleCCYAmount);
    }

    @Override
    public boolean isSyntheticCrossComponentBuyingBase(CurrencyPair synCP, CurrencyPair compCP, boolean netBuyingBase) {
        return SyntheticCrossUtil.isComponentBuyingBase(synCP,compCP,netBuyingBase);
    }

    @Override
    public TradeClassification getFXOutrightTradeClassification() {
        return ISUtilImpl.TRD_OUTRIGHT_CLSF;
    }
}
