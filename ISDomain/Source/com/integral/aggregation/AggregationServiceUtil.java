package com.integral.aggregation;

import com.integral.aggregation.model.AggregationTierLimit;
import com.integral.broker.model.Product;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.cache.MDSFactory;
import com.integral.is.common.mbean.ClientConfMBean;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.finance.currency.CurrencyService;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.QuoteAmountRoundingMode;
import com.integral.lp.provision.ProvisionInfoCalculator;
import com.integral.query.QueryFactory;
import com.integral.query.QueryService;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.MathUtilC;

import java.math.BigDecimal;
import java.util.*;

/**
 *
 */
public class AggregationServiceUtil {
    private static Log log = LogFactory.getLog(AggregationServiceUtil.class);
    private static ClientConfMBean clientConf = ISFactory.getInstance().getClientConfMBean();
    
    public static enum AGG_TYPE {
    	OA_AGGREGATION, BROKER_AGGREGATION
    }

    public static Set<Organization> getProviders(Product product, AggregationProcessorConfig config) {
        Set<Organization> providers = new HashSet<Organization>(product.getConfiguration().getOriginalSortedPriceProviders());
        if (product.getConfiguration().isLiquidityGroupOnly()) {
            providers = AggregationServiceUtil.getBrokerProvidersPresentInLiquidityGroup(product, providers);
        }
        providers.addAll(config.getReferenceProviders());
        return providers;
    }

    public static Set<Organization> getBrokerProvidersPresentInLiquidityGroup(Product product, Collection<Organization> brokerLps) {
        Organization lgOrg = product.getConfiguration().getLiquidityGroup();
        if (lgOrg != null) {
            Collection<Organization> lps = product.getConfiguration().getLiquidityGroupPriceProviders(product.getCurrencyPair().getName());
            if (lps != null) {
                Vector<Organization> lgAggLps = new Vector<Organization>(lps);
                lgAggLps.retainAll(brokerLps);
                return new HashSet<Organization>(lgAggLps);
            }
        }
        return Collections.emptySet();
    }
    public static boolean isTradableQuote(Quote quote) {
       String quoteClassification = quote.getQuoteClassification().getName();
       if( ISConstantsC.QTE_CLSF_TRADEABLE.equals(quoteClassification) ){
           return true;
       }else{
           int provisioningFlags = quote.getProvisioningFlags();
            /*
                Quote limit was set to 0 by credit. Before credit was applied the limit was available
            */
           provisioningFlags = provisioningFlags & ProvisionInfoCalculator.ProvisioningFlags.PROVISIONING_FLAGS_BID_MASK;
           if ((provisioningFlags & -provisioningFlags) == ProvisionInfoCalculator.ProvisioningFlags.CREDIT_BID_LIMIT_NOT_AVAILABLE) {
               return true;
           }
       }
       return false;
    }

    public static String generateUniqueNewId() {
        return UUID.randomUUID().toString();
    }

    public static String getDealtCurrencyProperty(String ccyPair, String dealtCcy) {
        if (isDealingBaseCurrency(ccyPair, dealtCcy)) {
            return FXLegDealingPrice.CCY1;
        } else if (isDealingVariableCurrency(ccyPair, dealtCcy)) {
            return FXLegDealingPrice.CCY2;
        } else {
            return null;
        }
    }

    public static boolean isDealingBaseCurrency(String ccyPair, String dealtCcy) {
        return ccyPair.startsWith(dealtCcy);
    }

    public static boolean isDealingVariableCurrency(String ccyPair, String dealtCcy) {
        return ccyPair.endsWith(dealtCcy);
    }

    public static Currency getSettledCcy(CurrencyPair ccyPair, Currency dealtCcy) {
        return isDealingBaseCurrency(ccyPair.getName(), dealtCcy.getName()) ? ccyPair.getBaseCurrency() : ccyPair.getVariableCurrency();
    }

    public static String getProvidersList(Collection<Organization> priceProviders) {
        StringBuilder sb = new StringBuilder(75);

        boolean isFirstElement = true;

        for (Organization org : priceProviders) {
            if (!isFirstElement) {
                sb.append(",");
            }
            sb.append(org.getShortName());
            isFirstElement = false;
        }

        return sb.toString();
    }

    public static IdcDate getValueDate(Product product) {
        //get the current trade date
        IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
        CurrencyPairGroup ccyPairGroup = product.getConfiguration().getCurrencyPairGroup();
        FXRateConvention rateConvention = ccyPairGroup.getFXRateConvention();
        FXRateBasis rateBasis = rateConvention.getFXRateBasis(product.getCurrencyPair());

        if (rateBasis == null) {
            log.info("PublisherC.getValueDate - unable to determine value date as rate basis is null.");
            return null;
        }

        if (log.isDebugEnabled()) {
            StringBuilder sb = new StringBuilder(100);
            sb.append("AggregationServiceUtil.getValueDate - trade date returned by EndOfDayService is ").append(tradeDate);
            sb.append(", ccyPairGroup = ").append(ccyPairGroup.getShortName());
            sb.append(", rateConvention = ").append(rateConvention.getShortName());
            sb.append(", rateBasis = ").append(rateBasis);
            sb.append(", base =").append(rateBasis.getBaseCurrency().getShortName());
            sb.append(", var = ").append(rateBasis.getVariableCurrency().getShortName());
            log.debug(sb.toString());
        }

        //get the value using the rate basis
        IdcDate valueDate = rateBasis.getSpotDate(tradeDate);
        if (log.isDebugEnabled()) {
            log.debug("AggregationServiceUtil.getValueDate - calculated value date is " + valueDate);
        }
        return valueDate;
    }

    public static Organization getOrganization(String orgName) {
        Organization org = null;

        if (orgName == null || orgName.equals("")) {
            return org;
        }

        // set default user in session context for query service
        User user = ISUtilImpl.getInstance().getDefaultUser();
        IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext(user);
        IdcSessionManager.getInstance().setSessionContext(ctx);

        try {
            QueryService qs = QueryFactory.getQueryService();
            org = (Organization) qs.find(Organization.class, orgName);
        } catch (IdcNoSuchObjectException e) {
            log.warn("Unable to find organization-" + orgName + " msg- " + e.getMessage());
        }
        return org;
    }

    public static int decrementRefCounter(Quote quote) {
        try {
            if (quote != null) {
                return quote.decReference(Quote.ReferenceHolder.AggregationService);
            }
        } catch (Exception exc) {
            exc.printStackTrace();
            return 0;
        }
        return 0;
    }

    public static int incrementRefCounter(Quote quote) {
        try {
            if (quote != null) {
                return quote.incReference(Quote.ReferenceHolder.AggregationService);
            }
        } catch (Exception exc) {
            //exc.printStackTrace(); Removed the stack trace as the handlers of this function are properly using the return
            // value to determine the course of action in case we were unable to increment the quote reference.
            log.warn("Unable to increment Reference for Quote:" + quote.getGUID() +", StackTrace:"+ Arrays.toString(Thread.currentThread().getStackTrace()));
            return 0;
        }
        return 0;
    }

    public static void decrementRefCounter(Collection<Quote> quotes) {
        try {
            if (quotes != null) {
                for (Quote quote : quotes) {
                    decrementRefCounter(quote);
                }
            }
        } catch (Exception exc) {
            exc.printStackTrace();
        }
    }

    public static void incrementRefCounter(Collection<Quote> quotes) {
        try {
            if (quotes != null) {
                Iterator<Quote> quoteItr = quotes.iterator();
                while ( quoteItr.hasNext() ) {
                    Quote quote = quoteItr.next();
                    if( incrementRefCounter(quote) <= 0 ){
                        quoteItr.remove();
                    }
                }
            }
        } catch (Exception exc) {
            exc.printStackTrace();
        }
    }

    public static boolean isStreamCategoryProviderPriorityEnabled(Organization organization, CurrencyPair ccyPair) {
        ISMBean ismBean = ISFactory.getInstance().getISMBean();
        if(ismBean.isProviderPriorityEnabled(organization.getShortName())) {
            boolean isStreamGroupingEnabled = ismBean.isStreamGroupingBasedProviderPriorityEnabled(organization.getShortName());
            boolean isMetal = ISUtilImpl.getInstance().isMetal(ccyPair);
            if(isMetal) isStreamGroupingEnabled = isStreamGroupingEnabled && ismBean.isStreamCategoryEnabledForMetals();
            return isStreamGroupingEnabled;
        }
        return false;
    }

    public static List<Double> getTierLimitsFromAggregationRule(Organization organization, CurrencyPair ccyPair) {
        List<Double> tierSize;
        tierSize = new ArrayList<Double>();
        Collection<AggregationTierLimit> tierLimits = organization.getTakerOrganizationFunction().getAggregationParameters().getAggregationTierLimits();
        String tierCcyStr = clientConf.getAggregationTierCcy(organization);
        Currency tierCcy = CurrencyService.getCurrency(tierCcyStr);
        Currency baseCcy = ccyPair.getBaseCurrency();
        boolean convertAmt = false;
        log.info("getTierLimitsFromAggregationRule : tierCcy="+tierCcy+" for "+ organization.getShortName());
        if( tierCcy != null && !tierCcy.isSameAs(ccyPair.getBaseCurrency())){
            convertAmt = true;
        }
        long roundingFactor = ISFactory.getInstance().getISMBean().getRoundingFactor(ccyPair.getName(), organization.getShortName());
        QuoteAmountRoundingMode roundingMode = ISFactory.getInstance().getISMBean().getQuoteAmountRoundingMode(baseCcy.getInstrumentClassification().getShortName());
        if( roundingMode == null ){
            roundingMode = QuoteAmountRoundingMode.ROUNDING_FACTOR;
        }
        for(AggregationTierLimit tierLimit : tierLimits) {
            if( convertAmt ) {
                double amt = MDSFactory.getInstance().convertAmount(organization.getShortName(),tierCcy,baseCcy,tierLimit.getBidLimit(),AggregationServiceUtil.class.getSimpleName(),true);
                switch (roundingMode){
                    case ROUNDING_FACTOR:
                        if( roundingFactor > 1 ){
                            long lAmt = ((Double)amt).longValue();
                            lAmt = (lAmt / roundingFactor) * roundingFactor;
                            amt = lAmt;
                        }
                        else{
                            long lAmt = ((Double)amt).longValue();
                            amt = lAmt;
                        }
                        break;
                    default:
                        double tickFactor = MathUtilC.divide(1.0D,baseCcy.getTickValue(),0, BigDecimal.ROUND_HALF_EVEN);
                        amt = MathUtilC.roundDown(amt, tickFactor);
                }
                log.info("getTierLimitsFromAggregationRule : Converted tier amount "+tierLimit.getBidLimit()+" "+tierCcy.getName()+" to "+amt+" "+baseCcy.getName());
                tierSize.add(amt);
            }
            else{
                tierSize.add(tierLimit.getBidLimit()); // BidLimit and OfferLimit should be same. Use only one of them.
            }
        }
        return tierSize;
    }
}
