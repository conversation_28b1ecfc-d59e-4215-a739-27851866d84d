package com.integral.aggregation;

import com.integral.aggregation.stream.Aggregator;
import com.integral.exception.IdcException;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.user.Organization;

import java.util.Map;
import java.util.concurrent.ConcurrentMap;

/**
 * Created by IntelliJ IDEA.
 * User: poddars
 * Date: 3/28/13
 * Time: 10:11 AM
 */
public class ASEndOfDayHandler implements MessageHandler
{
    protected Log log = LogFactory.getLog( this.getClass() );

    @Override
    public Message handle( Message message ) throws IdcException
    {
        resetAggregatorsValueDate();
        return null;
    }

    private void resetAggregatorsValueDate()
    {
        log.info( "ASEndOfDayHandler.resetAggregatorsValueDate: Reset value dates for all aggregators" );
        ConcurrentMap<Organization, AggregationProcessor> processorCache = AggregationProcessorManager.getInstance().getAggregationProcessorCache();
        for ( AggregationProcessor aggProcessor : processorCache.values() )
        {
            //Compute the updated value dates for all the aggregators.
            for ( Aggregator aggregator : aggProcessor.getClientSubscriptionManager().getAggregators() )
            {
                aggregator.getAggregatorFactory().setValueDate();
            }
        }

    }
}
