package com.integral.aggregation.price;

import com.integral.user.Organization;

/**
 *
 */
public class FXPriceC implements FXPrice {
    private int tier;
    private double rate;
    private double limit;
    private Organization provider;
    private String lpName;
    private int lpTier;
    private String quoteId;
    private double spotRate;
    private double forwardPoint;
    private double midRate;
    private double settleAmount;

    public double getRate() {
        return rate;
    }

    public void setRate(double rate) {
        this.rate = rate;
    }

    public double getLimit() {
        return limit;
    }

    public void setLimit(double limit) {
        this.limit = limit;
    }

    public Organization getProvider() {
        return provider;
    }

    public void setProvider(Organization provider) {
        this.provider = provider;
    }

    public int getTier() {
        return tier;
    }

    public void setTier(int tier) {
        this.tier = tier;
    }

    public String getLPName() {
        return lpName;
    }

    public void setLPName(String lpName) {
        this.lpName = lpName;
    }

    public int getLPTier()
    {
        return lpTier;
    }

    public void setLPTier( int lpTier )
    {
        this.lpTier = lpTier;
    }

	@Override
	public void setQuoteId(String quoteId) {
		this.quoteId = quoteId;		
	}

	@Override
	public String getQuoteId() {		
		return quoteId;
	}

	@Override
	public double getSpotRate() {		
		return this.spotRate;
	}

	@Override
	public double getForwardPoint() {		
		return this.forwardPoint;
	}

	@Override
	public void setSpotRate(double spotRate) {		
		this.spotRate = spotRate;
	}

	@Override
	public void setForwardPoint(double forwardPoint) {		
		this.forwardPoint = forwardPoint;
	}

	@Override
	public void setMidRate(double midRate) {
		this.midRate = midRate;
		
	}

	@Override
	public double getMidRate() {		
		return this.midRate;
	}

	@Override
	public double getSettleAmount() {		
		return this.settleAmount;
	}

	@Override
	public void setSettleAmount(double settleAmount) {
		this.settleAmount = settleAmount;
		
	}
    
	
	
    
}
