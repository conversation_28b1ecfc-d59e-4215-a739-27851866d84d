package com.integral.aggregation.price;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class SwapFXPriceBookC extends FXPriceBookC implements SwapFXPriceBook {
    private List<FXPrice> farBids;
    private List<FXPrice> farOffers;
    private String fixingDate2;
    private double size2;

	public SwapFXPriceBookC(int bidSize, int offerSize) {
		super(bidSize, offerSize);	
		farBids = new ArrayList<FXPrice>();
		farOffers = new ArrayList<FXPrice>();
	}

	@Override
	public Collection<FXPrice> getFarBids() {		
		return farBids;
	}

	@Override
	public Collection<FXPrice> getFarOffers() {
		return farOffers;
	}

	@Override
	public String getFixingDate2() {		
		return fixingDate2;
	}

	@Override
	public double getSize2() {		
		return size2;
	}

	@Override
	public void addFarBid(FXPrice farBidPrice) {
		farBids.add(farBidPrice);		
	}

	@Override
	public void addFarOffer(FXPrice farOfferPrice) {
		farOffers.add(farOfferPrice);		
	}
	
	

}
