package com.integral.aggregation.price;

import com.integral.finance.currency.CurrencyPair;
import com.integral.user.Organization;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 *
 */
public class FXPriceBookC implements FXPriceBook {
    private String bookId;
    private boolean isMultiTier;
    private boolean isStale;
    private List<FXPrice> bids;
    private List<FXPrice> offers;
    private CurrencyPair currencyPair;
    private Organization organization;
    private String valueDate;
    private long valueDateMilliseconds;
    private int sequenceNo;
    private String fixingDate;
    private long generationTimeStamp;
    // This is max of expire time from all providers(bid/offer)
    private int bookExpiryTime = -1;

    public FXPriceBookC(int bidSize, int offerSize) {
        bids = new ArrayList<FXPrice>(bidSize);
        offers = new ArrayList<FXPrice>(offerSize);
    }

    @Override
	public String getFixingDate() {		
		return fixingDate;
	}



	public boolean isMultiTier() {
        return isMultiTier;
    }

    public void setMultiTier(boolean multiTier) {
        this.isMultiTier = multiTier;
    }

    public Collection<FXPrice> getBids() {
        return bids;
    }

    public Collection<FXPrice> getOffers() {
        return offers;
    }

    public FXPrice getBid(int tierSize) {
        if (bids != null) {
            return bids.get(tierSize);
        }
        return null;
    }

    public FXPrice getOffer(int tierSize) {
        if (offers != null) {
            return offers.get(tierSize);
        }
        return null;
    }

    public int size() {
        return Math.max(getBids().size(), getOffers().size());
    }

    public void addBid(double rate, double amount) {
        FXPrice price = new FXPriceC();
        price.setRate(rate);
        price.setLimit(amount);
        bids.add(price);
    }

    public void addOffer(double rate, double amount) {
        FXPrice price = new FXPriceC();
        price.setRate(rate);
        price.setLimit(amount);
        offers.add(price);
    }

    public void addBid(FXPrice bid) {
        bids.add(bid);
    }

    public void addOffer(FXPrice offer) {
        offers.add(offer);
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public boolean isStale() {
        return isStale;
    }

    public void setStale(boolean stale) {
        isStale = stale;
    }

    public CurrencyPair getCurrencyPair() {
        return currencyPair;
    }

    public void setCurrencyPair(CurrencyPair currencyPair) {
        this.currencyPair = currencyPair;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public String getValueDate()
    {
        return valueDate;
    }

    public void setValueDate( String valueDate )
    {
        this.valueDate = valueDate;
    }

    public long getValueDateMilliseconds() {
        return valueDateMilliseconds;
    }

    public void setValueDateMilliseconds(long valueDateMilliseconds) {
        this.valueDateMilliseconds = valueDateMilliseconds;
    }

    public int getSequenceNo()
    {
        return sequenceNo;
    }

    public void setSequenceNo( int sequenceNo )
    {
        this.sequenceNo = sequenceNo;
    }

	@Override
	public long getGenerationTimeStamp() 
	{		
		return generationTimeStamp;
	}

	@Override
	public void setGenerationTimeStamp(long generationTimeStamp) 
	{
		this.generationTimeStamp = generationTimeStamp;
		
	}

	@Override
	public void setExpiryTime(int expiryTime) {
		this.bookExpiryTime = Math.max(this.bookExpiryTime, expiryTime);		
	}

	@Override
	public int getExpiryTime() {		
		return this.bookExpiryTime;
	}



    @Override
    public String toString() {
        return "FXPriceBookC{" +
                "bookId='" + bookId + '\'' +
                ", isMultiTier=" + isMultiTier +
                ", isStale=" + isStale +
                ", bids=" + bids +
                ", offers=" + offers +
                ", currencyPair=" + currencyPair +
                ", organization=" + organization +
                ", valueDate='" + valueDate + '\'' +
                ", sequenceNo=" + sequenceNo +
                ", fixingDate='" + fixingDate + '\'' +
                ", generationTimeStamp=" + generationTimeStamp +
                ", bookExpiryTime=" + bookExpiryTime +
                '}';
    }
}
