package com.integral.aggregation;

/**
 * Callback interface to be implemented for processing the aggregated price books and
 * subscription/un-subscription response(Response of async subscription/un-subscription requests).
 *
 */
public interface AggregationResponseHandler<T, V> {

    /**
     * Called when subscription/un-subscription request response is ready. This is a case when request is submitted to
     * aggregation service asynchronously.
     *
     * @param response
     */
    public void onCompleted(T response);

    /**
     * Called on every aggregated price book update.
     *
     * @param rate aggregated view
     */
    public void onPriceUpdate(V rate);
    String getSubscriptionKey();
}
