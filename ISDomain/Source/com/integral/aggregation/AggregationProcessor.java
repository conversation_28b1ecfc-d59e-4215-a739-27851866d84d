package com.integral.aggregation;

import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.subscription.client.ClientSubscriptionManager;
import com.integral.broker.model.*;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.aggregation.config.AggregationServiceMBean;
import com.integral.aggregation.subscription.*;
import com.integral.taker.TakerOrganizationFunction;
import com.integral.user.Organization;

/**
 *
 */
public class AggregationProcessor {
    protected Log log = LogFactory.getLog(this.getClass());

    private Organization fiOrg;
    private BrokerOrganizationFunction brokerOrganizationFunction;
    private TakerOrganizationFunction takerOrganizationFunction;
    protected AggregationServiceMBean aggregationMBean;
    protected AggregationProcessorConfig config;
    protected AggregatorFactory factory;
    protected ClientSubscriptionManager clientSubscriptionManager;

    protected AggregationProcessor(Organization org) {    	
        this.fiOrg = org;
        this.factory = newAggregatorFactory();
        this.config = this.factory.getAggregationProcessorConfig();
        this.clientSubscriptionManager = newClientSubscriptionManager();
        updateAggregationProcessorConfig();
        log.info("AGGREGATION_PROCESSOR_CREATED_FOR_ORG=" + org.getShortName());
    }
    
    protected void updateAggregationProcessorConfig() {
    	AggregationServiceFactory.getInstance().getAggregationMBean()
         					.updateAggregationProcessorConfig(this.config);
    }
    
    protected ClientSubscriptionManager newClientSubscriptionManager() {
    	return this.factory.getClientSubManager();
    }
    
    protected AggregatorFactory newAggregatorFactory() {
    	return new AggregatorFactory(this);
    }


    public Response subscribe(AggregationProcessorRequest request) {
        Response response = request.createResponse();

        try {
            if (log.isInfoEnabled()) {
                StringBuilder sb = new StringBuilder(150)
                    .append(' ').append("AGGREGATION_PROCESSOR_SUBSCRIBE")
                    .append(" REQUEST_ID=").append(request.getRequestId())
                    .append(" STREAM_ID").append(request.getStreamId())
                    .append(" CCY_PAIR=").append(request.getProduct().getCurrencyPair().getName())
                    .append(" QUOTE_AGGREGATOR=").append(request.getProduct().getConfiguration().getQuoteAggregator());
                log.info(sb.toString());
            }

            response = this.clientSubscriptionManager.subscribe(request);
            
            if (log.isInfoEnabled()) {
                StringBuilder sb = new StringBuilder(100)
                    .append(' ').append("AGGREGATION_PROCESSOR_SUBSCRIBE_SUCESS")
                    .append(" REQUEST_ID=").append(request.getRequestId());                    
                log.info(sb.toString());
            }

        } catch (Exception exc) {
        	StringBuilder sb = new StringBuilder(this.getClass().getName())
        	.append(' ').append("AGGREGATION_PROCESSOR_SUBSCRIBE_EXCEPTION")
        	.append(" REQUEST_ID=").append(request.getRequestId())
        	.append(" STREAM_ID").append(request.getStreamId());
            log.error(sb.toString(), exc);
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(exc.getMessage());
        }
        return response;
    }

    public Response unSubscribe(AggregationProcessorRequest request) {
    	Response response = request.createResponse();
        
        try {
            if (log.isInfoEnabled()) {
                StringBuilder sb = new StringBuilder(150)
                	.append(' ').append("AGGREGATION_PROCESSOR_UNSUBSCRIBE")
                    .append(" REQUEST_ID=").append(request.getRequestId())
                    .append(" STREAM_ID").append(request.getStreamId());
                log.info(sb.toString());
            }
            
            response = this.clientSubscriptionManager.unSubscribe(request);
            
            if (log.isInfoEnabled()) {
                StringBuilder sb = new StringBuilder(100)
                    .append(' ').append("AGGREGATION_PROCESSOR_UNSUBSCRIBE_SUCESS")
                    .append(" REQUEST_ID=").append(request.getRequestId());                    
                log.info(sb.toString());
            }
            
        } catch (Exception exc) {
        	StringBuilder sb = new StringBuilder(150)
        	.append(' ').append("AGGREGATION_PROCESSOR_UNSUBSCRIBE_EXCEPTION")
            .append(" REQUEST_ID=").append(request.getRequestId())
            .append(" STREAM_ID").append(request.getStreamId());
            log.error(sb.toString(), exc);
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(exc.getMessage());
        }

        return response;
    }

    public Response update(AggregationProcessorRequest request) {
        return null;
    }

    public Response subscribe(RFSAggregationProcessorRequest request) {
        Response response = request.createResponse();

        try {
            response = this.clientSubscriptionManager.subscribe(request);

            if (log.isDebugEnabled()) {

            }
        } catch (Exception exc) {
            log.error("");
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(exc.getMessage());
        }
        return response;
    }

    public Response stop() {
        Response response = new ResponseC("");

        try {
            response = this.clientSubscriptionManager.stop();

            if (log.isDebugEnabled()) {

            }
        } catch (Exception exc) {
            log.error("");
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(exc.getMessage());
        }
        return response;
    }

    /*public SubscriptionRequestBuilder getRequestBuilder() {
        return new SubscriptionRequestBuilder(this);
    }*/

    public AggregationRequestBuilder getRequestBuilder() {
        return new AggregationRequestBuilder(this);
    }

    public RFSAggregationRequestBuilder getRFSRequestBuilder() {
        return new RFSAggregationRequestBuilder(this);
    }

    public AggregationRequestValidator getRequestValidator() {
        return null;
    }

    public AggregationProcessorConfig getConfig() {
        return config;
    }

    public AggregatorFactory getFactory() {
        return factory;
    }

    public Organization getFiOrg() {
        return fiOrg;
    }

    public BrokerOrganizationFunction getBrokerOrganizationFunction() {
        return brokerOrganizationFunction;
    }

    public void setBrokerOrganizationFunction(BrokerOrganizationFunction brokerOrganizationFunction) {
        this.brokerOrganizationFunction = brokerOrganizationFunction;
    }

    public TakerOrganizationFunction getTakerOrganizationFunction() {
        return takerOrganizationFunction;
    }

    public void setTakerOrganizationFunction(TakerOrganizationFunction takerOrganizationFunction) {
        this.takerOrganizationFunction = takerOrganizationFunction;
    }

    public boolean isSubscriptionAvailable() {
        return this.clientSubscriptionManager.isSubscriptionAvailable() ||
                this.factory.getProviderSubManager().isSubscriptionAvailable();
    }

    public ClientSubscriptionManager getClientSubscriptionManager() {
        return clientSubscriptionManager;
    }
}
