package com.integral.aggregation;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.FutureTask;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

import com.integral.aggregation.config.AggregationServiceMBean;
import com.integral.aggregation.config.rfs.RFSAggregationServiceMBean;
import com.integral.aggregation.stream.Aggregator;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 *
 */
public class AggregatorThread {
    private Log log = LogFactory.getLog(this.getClass());

    public static int AGG_OPERATION_ADD_STREAM_PUBLISHER = 0;
    public static int AGG_OPERATION_REMOVE_STREAM_PUBLISHER = 1;

    //private static AggregatorThread instance;
    private ScheduledExecutorService aggregatorExecutorService;
    private ThreadPoolExecutor aggregatorExecutor;
    private Thread aggregatorThread;
    private AggregatorSchedulingTask schedulingTask;
    private ScheduledFuture schedule;
    private int sweepInterval;
    private int size;
    private static int threadNumber = 0;

    private List<Aggregator> aggregators;
    private List<Runnable> pendingOperationCache;
    private volatile boolean isPendingOperationsAvailable = false;
    private final ReentrantLock operationCacheLock = new ReentrantLock();
    
    private static AtomicInteger aggFactoryThreadNumber = new AtomicInteger(1);    
    private volatile boolean rfs;

    public AggregatorThread(AggregationServiceMBean aggregationServiceMBean) {
    	boolean rfsAggregation = (aggregationServiceMBean instanceof RFSAggregationServiceMBean);
        this.aggregatorExecutorService = Executors.newScheduledThreadPool(1, new AggregatorThreadFactory(rfsAggregation));  
        this.aggregators = new ArrayList<Aggregator>(50);
        this.pendingOperationCache = new ArrayList<Runnable>(5);
        this.schedulingTask = new AggregatorSchedulingTask();
        this.sweepInterval = aggregationServiceMBean.getAggregatorThreadSweepInterval();
        this.rfs = rfsAggregation;
    }
    
    /*public static AggregatorThread instance() {
        if (instance == null) {
            instance = new AggregatorThread();
        }
        return instance;
    }*/

    public void start() {
        if (!this.isRunning()) {
            schedule = aggregatorExecutorService.scheduleWithFixedDelay(this.schedulingTask, 100, this.sweepInterval, TimeUnit.MILLISECONDS);

            if (log.isInfoEnabled()) {
                StringBuilder sb = new StringBuilder(150)
                        .append("AggregatorThread is started.")
                        .append(' ').append(this.sweepInterval);
                log.info(sb.toString());
            }
        }
    }

    // Obsoleting this method for ESP, as AggregatorThread will be stopped internally once it finds no aggregators in the cache.
    // This method should be called after removing that particular thread from thread pool cache , other wise this thread may be 
    // picked up again and exception will be thrown on restart
    public void stop() {
    	// RFS AggregatorThread should be 
    	// stopped if there is no active RFS request.
    	// ESP, We can live with not stopping Aggregator thread pool because subscriptions are done once and remain active
    	// where as RFS gets un-subscribed on user withdraw or expiry of RFQ
    	try {
        	if (rfs && this.aggregatorExecutorService != null) {        		
        		this.aggregatorExecutorService.shutdown();
        		log.info("RFSAggregatorThread is stopped.");        		
        	}
    	} catch (Exception e) {
    		log.error("problem with stopping RFSAggregatorThread.", e);
    	}

    }

    private void internalStop() {
        if (schedule != null) {
            schedule.cancel(false);
            if (log.isInfoEnabled()) {
                log.info("AggregatorThread is stopped.");
            }
        }
    }

    public boolean isRunning() {
        return schedule != null && !schedule.isDone();
    }

    public void addAggregator(Aggregator aggregator, CallBackErrorHandler handler) throws Exception {

        if (Thread.currentThread() == aggregatorThread) {
            addAggregatorNow(aggregator);
        } else {
            addAggregatorLater(aggregator, handler);
        }
    }

    private void addAggregatorLater(Aggregator aggregator, CallBackErrorHandler handler) {
        AggregatorOperation operation = new AggregatorOperation(aggregator, handler, AGG_OPERATION_ADD_STREAM_PUBLISHER);
        operationCacheLock.lock();
        try {
            // If aggregator thread is not started, start it.
            if (!this.isRunning()) {
                start();
            }
            pendingOperationCache.add(operation);
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(100)
                    .append(this.getClass().getName()).append(".addAggregatorLater()")
                    .append(' ').append("Exception while adding the aggregator - ")
                    .append(' ').append(aggregator.getProduct().getCurrencyPair().getName())
                    .append(' ').append(aggregator.getAggregatorId())
                    .append(' ').append(aggregator);
            log.error(sb.toString());
        } finally {
            operationCacheLock.unlock();
        }

        isPendingOperationsAvailable = true;
    }

    private void addAggregatorNow(Aggregator aggregator) throws Exception {
        if (Thread.currentThread() != aggregatorThread) {
            throw new Exception("This can be invoked only from aggregatorThread.");
        }

        aggregators.add(aggregator);
    }

    public void removeAggregator(Aggregator aggregator, CallBackErrorHandler handler) throws Exception {
        if (Thread.currentThread() == aggregatorThread) {
            removeAggregatorNow(aggregator);
        } else {
            removeAggregatorLater(aggregator, handler);
        }
    }

    private void removeAggregatorLater(Aggregator aggregator, CallBackErrorHandler handler) {
        AggregatorOperation operation = new AggregatorOperation(aggregator, handler, AGG_OPERATION_REMOVE_STREAM_PUBLISHER);
        operationCacheLock.lock();
        try {
            pendingOperationCache.add(operation);
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(100)
                    .append(this.getClass().getName()).append(".removeAggregatorLater()")
                    .append(' ').append("Exception while removing the aggregator - ")
                    .append(' ').append(aggregator.getProduct().getCurrencyPair().getName())
                    .append(' ').append(aggregator.getAggregatorId())
                    .append(' ').append(aggregator)
                    .append(' ').append(exc.getMessage());
            log.error(sb.toString());
        } finally {
            operationCacheLock.unlock();
        }
        isPendingOperationsAvailable = true;
    }

    private void removeAggregatorNow(Aggregator aggregator) throws Exception {
        if (Thread.currentThread() != aggregatorThread) {
            throw new Exception("This can be invoked only from aggregatorThread.");
        }

        aggregator.cleanUp();
        aggregators.remove(aggregator);
        // if no aggregators are left, stop the AggregatorThread.
        if (aggregators.size() == 0) {
            internalStop();
        }
    }

    private class AggregatorSchedulingTask implements Runnable {

        public void run() {
            try {
                if (isPendingOperationsAvailable) {
                    operationCacheLock.lock();
                    try {
                        for (Runnable operation : pendingOperationCache) {
                            operation.run();
                        }
                        pendingOperationCache.clear();
                    } catch (Exception exc) {
                        StringBuilder sb = new StringBuilder(100)
                                .append(this.getClass().getName()).append(".invokePendingOperations()")
                                .append(' ').append("Exception while invoking the pending operations - ")
                                .append(' ').append(pendingOperationCache)
                                .append(' ').append(exc.getMessage());
                        log.error(sb.toString());
                        exc.printStackTrace();
                    } finally {
                        operationCacheLock.unlock();
                        isPendingOperationsAvailable = false;
                        size = aggregators.size();
                    }
                }

                for (Aggregator aggregator : aggregators) {
                    try {
                        aggregator.aggregate();
                        /*if (aggregator.isQuoteBookUpdated()) {
                            //streamPublisherExecutor.execute(publisher);
                            aggregator.run();
                        }*/
                    } catch (Exception exc) {
                        StringBuilder sb = new StringBuilder(100)
                            .append(this.getClass().getName()).append(".run()")
                            .append(' ').append("Exception while executing the aggregator - ")
                            .append(' ').append(aggregator.getAggregatorId())
                            .append(' ').append(exc.getMessage());
                        log.error(sb.toString());
                    }
                }
            } catch (Exception exc) {
                log.error("AggregatorSchedulingTask.run Exception " + exc.getMessage());
            }
        }
    }
    
    private class AggregatorThreadFactory implements ThreadFactory {
        private String THREAD_POOL_NAME_PREFIX = "AggregatorThread-";

        public AggregatorThreadFactory(boolean rfsAggregation) {
        	if (rfsAggregation) {
        		THREAD_POOL_NAME_PREFIX = "RFSAggregatorThread-";
        	}
        }

        public Thread newThread(Runnable r) {
            aggregatorThread = new Thread(r, THREAD_POOL_NAME_PREFIX + aggFactoryThreadNumber.getAndIncrement());
            return aggregatorThread;
        }
    }

    private class AggregatorOperation implements Runnable {
        private Aggregator aggregator;
        private CallBackErrorHandler handler;
        private int action;

        private AggregatorOperation(Aggregator aggregator, CallBackErrorHandler handler, int action) {
            this.handler = handler;
            this.aggregator = aggregator;
            this.action = action;
        }

        public void run() {
            try {
                if (action == AGG_OPERATION_ADD_STREAM_PUBLISHER) {
                    addAggregator(aggregator, handler);
                } else {
                    removeAggregator(aggregator, handler);
                }
            } catch (Exception exc) {
                log.error("Exception while handling the aggregator operation.");
                handler.handleError(exc);
            }
        }
    }

    public Future<List<Aggregator>> getAggregators() {
        if (isRunning()) {
            Future<List<Aggregator>> futureTask = new FutureTask<List<Aggregator>>(new Callable<List<Aggregator>>() {
                public List<Aggregator> call() throws Exception {
                    if (Thread.currentThread() == aggregatorThread) {
                        List<Aggregator> _aggregators = new ArrayList<Aggregator>(25);
                        _aggregators.addAll(aggregators);
                        return _aggregators;
                    }
                    return null;
                }
            });

            operationCacheLock.lock();
            try {
                pendingOperationCache.add((Runnable) futureTask);
            } catch (Exception exc) {
                StringBuilder sb = new StringBuilder(100)
                        .append(this.getClass().getName()).append(".getAggregators()")
                        .append(' ').append("Exception while adding the task - ");
                log.error(sb.toString());
            } finally {
                operationCacheLock.unlock();
            }

            isPendingOperationsAvailable = true;
            return futureTask;
        } else {
            List<Aggregator> _aggregators = new ArrayList<Aggregator>(25);
            _aggregators.addAll(aggregators);
            Future<List<Aggregator>> futureTask = new FutureTask<List<Aggregator>>(new Runnable() {
                public void run() {
                }
            }, _aggregators);
            ((Runnable)futureTask).run();
            return futureTask;
        }

    }

    public int getSize() {
        return size;
    }

    /*private class AggregatorExecutorThreadFactory implements ThreadFactory {
        AtomicInteger threadNumber = new AtomicInteger(1);
        ThreadGroup g = null;
        String name;

        public AggregatorExecutorThreadFactory(String name) {
            this.name = name + "-Thread-";
            this.g = new ThreadGroup(name);
        }

        public Thread newThread(Runnable r) {
            aggregatorThread = new Thread(g, r, name + threadNumber.getAndIncrement());
            return aggregatorThread;
        }
    }*/
}
