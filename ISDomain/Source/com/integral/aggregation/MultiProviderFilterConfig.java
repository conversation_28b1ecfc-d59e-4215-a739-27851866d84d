package com.integral.aggregation;

import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.broker.model.*;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.taker.TakerOrganizationFunction;
import com.integral.user.Organization;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 *
 */
public class MultiProviderFilterConfig {
    public static final int DEFAULT_VALUE = Integer.MIN_VALUE;
    public static final int DEFAULT_ZERO_VALUE = 0;

    private AggregationProcessor aggregationProcessor;
    private Organization organization;

    private boolean isMultiProviderFilterEnabled;
    private boolean isStalenessCheckEnabled;
    private boolean isOffMarketCheckEnabled;
    private boolean isInvertedRateFilterCheckEnabled = true;

    private int offMarketMinProviders = DEFAULT_VALUE;
    private int offMarketMaxProviders = DEFAULT_VALUE;
    private long stalenessCheckInterval = DEFAULT_VALUE;
    private double offMarketMaxInverseSpread = DEFAULT_VALUE;
    private double invertedRateTolerance = DEFAULT_ZERO_VALUE;

    private Map<String, Long> ccyPairStalenessCheckInterval;
    private Map<String, Double> ccyPairMaxInverseSpread;
    private Map<String, Double> ccyPairInvertedRateTolerance;

    public MultiProviderFilterConfig(AggregationProcessor aggregationProcessor) {
        this.aggregationProcessor = aggregationProcessor;
        this.organization = this.aggregationProcessor.getFiOrg();

        this.ccyPairStalenessCheckInterval = new ConcurrentHashMap<String, Long>();
        this.ccyPairMaxInverseSpread = new ConcurrentHashMap<String, Double>();
        this.ccyPairInvertedRateTolerance = new ConcurrentHashMap<String, Double>();
    }

    public void reset() {
        this.isMultiProviderFilterEnabled = false;
        this.isStalenessCheckEnabled = false;
        this.isOffMarketCheckEnabled = false;
        this.isInvertedRateFilterCheckEnabled = true;

        this.stalenessCheckInterval = DEFAULT_VALUE;
        this.offMarketMinProviders = DEFAULT_VALUE;
        this.offMarketMaxProviders = DEFAULT_VALUE;
        this.offMarketMaxInverseSpread = DEFAULT_VALUE;
        this.invertedRateTolerance = DEFAULT_ZERO_VALUE;
        ccyPairStalenessCheckInterval.clear();
        ccyPairMaxInverseSpread.clear();
        ccyPairInvertedRateTolerance.clear();
    }

    public boolean isMultiProviderFilterEnabled() {
        return isMultiProviderFilterEnabled;
    }

    public void setMultiProviderFilterEnabled(boolean multiProviderFilterEnabled) {
        isMultiProviderFilterEnabled = multiProviderFilterEnabled;
    }

    public boolean isStalenessCheckEnabled() {
        return isStalenessCheckEnabled;
    }

    public void setStalenessCheckEnabled(boolean stalenessCheckEnabled) {
        isStalenessCheckEnabled = stalenessCheckEnabled;
    }

    public boolean isOffMarketCheckEnabled() {
        return isOffMarketCheckEnabled;
    }

    public void setOffMarketCheckEnabled(boolean offMarketCheckEnabled) {
        isOffMarketCheckEnabled = offMarketCheckEnabled;
    }

    public boolean isInvertedRateFilterCheckEnabled() {
        return isInvertedRateFilterCheckEnabled;
    }

    public void setInvertedRateFilterCheckEnabled(boolean invertedRateFilterCheckEnabled) {
        isInvertedRateFilterCheckEnabled = invertedRateFilterCheckEnabled;
    }

    public long getStalenessCheckInterval() {
        return stalenessCheckInterval;
    }

    public void setStalenessCheckInterval(long stalenessCheckInterval) {
        this.stalenessCheckInterval = stalenessCheckInterval;
    }

    public int getOffMarketMinProviders() {
        return offMarketMinProviders;
    }

    public void setOffMarketMinProviders(int offMarketMinProviders) {
        this.offMarketMinProviders = offMarketMinProviders;
    }

    public int getOffMarketMaxProviders() {
        return offMarketMaxProviders;
    }

    public void setOffMarketMaxProviders(int offMarketMaxProviders) {
        this.offMarketMaxProviders = offMarketMaxProviders;
    }

    public double getOffMarketMaxInverseSpread() {
        return offMarketMaxInverseSpread;
    }

    public void setOffMarketMaxInverseSpread(double offMarketMaxInverseSpread) {
        this.offMarketMaxInverseSpread = offMarketMaxInverseSpread;
    }

    public Map<String, Long> getCcyPairStalenessCheckInterval() {
        return ccyPairStalenessCheckInterval;
    }

    public void addCcyPairStalenessCheckInterval(String ccyPair, Long interval) {
        this.ccyPairStalenessCheckInterval.put(ccyPair, interval);
    }

    public void clearCcyPairStalenessCheckIntervalCache() {
        this.ccyPairStalenessCheckInterval.clear();
    }

    public void removeCcyPairStalenessCheckInterval(String ccyPair) {
        this.ccyPairStalenessCheckInterval.remove(ccyPair);
    }

    public Map<String, Double> getCcyPairMaxInverseSpread() {
        return ccyPairMaxInverseSpread;
    }

    public void addCcyPairMaxInverseSpread(String ccyPair, Double spread) {
        this.ccyPairMaxInverseSpread.put(ccyPair, spread);
    }

    public void clearCcyPairMaxInverseSpreadCache() {
        this.ccyPairMaxInverseSpread.clear();
    }

    public void removeCcyPairMaxInverseSpread(String ccyPair) {
        this.ccyPairMaxInverseSpread.remove(ccyPair);
    }

    public void addCcyPairInvertedRateTolerance(String ccyPair, Double invertedRateTolerance) {
        this.ccyPairInvertedRateTolerance.put(ccyPair,invertedRateTolerance);
    }

    public void clearCcyPairInvertedRateToleranceCache() {
        this.ccyPairInvertedRateTolerance.clear();
    }

    public void removeCcyPairInvertedRateTolerance(String ccyPair){
        this.ccyPairInvertedRateTolerance.remove(ccyPair);
    }

    public Map<String,Double> getCcyPairInvertedRateTolerance(){
        return this.ccyPairInvertedRateTolerance;
    }

    public double getInvertedRateTolerance() {
        return invertedRateTolerance;
    }

    public void setInvertedRateTolerance(double invertedRateTolerance) {
        this.invertedRateTolerance = invertedRateTolerance;
    }

    public Organization getOrganization() {
        return organization;
    }

    private RateFilterDefinition createRateFilterDefinition()
    {
        RateFilterDefinition activeRateFilterDefinition = ModelFactory.getInstance().newRateFilterDefination();
        MultiProviderRateFilterParams rateFilterParams = ModelFactory.getInstance().newMultiProviderRateFilterParams();
        rateFilterParams.setRateFilterDefinition( activeRateFilterDefinition );
        activeRateFilterDefinition.setMultiProviderRateFilterParams( rateFilterParams );
        return activeRateFilterDefinition;
    }

    public void broadcastUpdates() {
        if (!AggregationServiceFactory.getInstance().getAggregationMBean().isAggExecRuleEnabled(organization.getShortName())) {
            BrokerOrganizationFunction orgFunction = this.aggregationProcessor.getBrokerOrganizationFunction();
            if (orgFunction == null) return;

            //If isMultiProviderFilterEnabled toggled from false -> true, activeRateFilterDefinition should be re-initialized
            RateFilterDefinition activeRateFilterDef = orgFunction.getActiveRateFilterDefinition();
            if (activeRateFilterDef == null && isMultiProviderFilterEnabled()) {
                orgFunction.setActiveRateFilterDefinition(createRateFilterDefinition());
            }
            //if isMultiProviderFilterEnabled toggled from true -> false, activeRateFilterDefinition should be made null
            else if (activeRateFilterDef != null && !isMultiProviderFilterEnabled()) {
                orgFunction.setActiveRateFilterDefinition(null);
            }

            RateFilterDefinition rateFilterDefinition = orgFunction.getActiveRateFilterDefinition();
            if (rateFilterDefinition == null) return;

            MultiProviderRateFilterParams rateFilterParams = rateFilterDefinition.getMultiProviderRateFilterParams();

            rateFilterParams.setStalenessCheckEnabled(isStalenessCheckEnabled());
            rateFilterParams.setOffmarketCheckEnabled(isOffMarketCheckEnabled());
            rateFilterParams.setInvertedRateFilterCheckEnabled(isInvertedRateFilterCheckEnabled());
            rateFilterParams.setStalenessCheckInterval(getStalenessCheckInterval());
            rateFilterParams.setOffMarketMaxProviders(getOffMarketMaxProviders());
            rateFilterParams.setOffMarketMinProviders(getOffMarketMinProviders());
            rateFilterParams.setOffMarketMaxInverseSpread(getOffMarketMaxInverseSpread());
            rateFilterParams.setInvertedRateTolerance(getInvertedRateTolerance());

            Map<String, Double> ccyPairMaxInverseSpreads = getCcyPairMaxInverseSpread();
            Map<String, Long> ccyPairStalenessCheckInterval = getCcyPairStalenessCheckInterval();
            Map<String, Double> ccyPairInvertedRateTolerance = getCcyPairInvertedRateTolerance();

            Set<String> ccyPairs = new HashSet<String>();
            ccyPairs.addAll(ccyPairMaxInverseSpreads.keySet());

            for (String ccyPair : ccyPairStalenessCheckInterval.keySet()) {
                if (!ccyPairs.contains(ccyPair)) {
                    ccyPairs.add(ccyPair);
                }
            }

            for (String ccyPair : ccyPairInvertedRateTolerance.keySet()) {
                if (!ccyPairs.contains(ccyPair)) {
                    ccyPairs.add(ccyPair);
                }
            }

            if (ccyPairs.isEmpty()) {
                rateFilterParams.getMultiProviderRateFilterCcyPairParams().clear();
            } else {
                //ccy pair parameters are reconstructed
                rateFilterParams.getMultiProviderRateFilterCcyPairParams().clear();
                int sortOrder = 0;
                for (String ccyPair : ccyPairs) {
                    Double spread = ccyPairMaxInverseSpreads.get(ccyPair);
                    Long interval = ccyPairStalenessCheckInterval.get(ccyPair);
                    Double tolerance = ccyPairInvertedRateTolerance.get(ccyPair);

                    MultiProviderRateFilterCcyPairParams ccyPairRateFilterParams = ModelFactory.getInstance().newMultiProviderRateFilterCcyPairParams();
                    ccyPairRateFilterParams.setMultiProviderRateFilterParams(rateFilterParams);
                    CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(ccyPair);
                    if(currencyPair != null) {
                        ccyPairRateFilterParams.setBaseCurrency(currencyPair.getBaseCurrency());
                        ccyPairRateFilterParams.setVariableCurrency(currencyPair.getVariableCurrency());
                    }
                    ccyPairRateFilterParams.setSortOrder(sortOrder++);

                    rateFilterParams.addMultiProviderRateFilterCcyPairParams(ccyPairRateFilterParams);

                    if (spread != null) {
                        ccyPairRateFilterParams.setOffMarketMaxInverseSpread(spread);
                    }

                    if (interval != null) {
                        ccyPairRateFilterParams.setStalenessCheckInterval(interval);
                    }

                    if (tolerance != null) {
                        ccyPairRateFilterParams.setInvertedRateTolerance(tolerance);
                    }
                }
            }

            rateFilterParams.reloadInMemory();
        }
    }
}
