package com.integral.aggregation.config;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
//import com.lmax.disruptor.RingBuffer;
//import com.lmax.disruptor.SingleThreadedClaimStrategy;
//import com.lmax.disruptor.SleepingWaitStrategy;
//import com.lmax.disruptor.dsl.Disruptor;

import com.integral.aggregation.config.rfs.RFSAggregationServiceMBeanC;

/**
 *
 */
public class AggregationServiceFactory {
    private static AggregationServiceFactory instance;
    private AggregationServiceMBean aggregationMBean;
    private AggregationServiceMBean rfsAggregationMBean;
    private ExecutorService requestExecutor;

    private static final int ENTRIES = 64;
//    private final Disruptor<AggregationTaskExecutor> disruptor;
//    private final RingBuffer<AggregationTaskExecutor> ringBuffer;

    private AggregationServiceFactory() {
//        disruptor = new Disruptor<AggregationTaskExecutor>(AggregationTaskExecutor.FACTORY, requestExecutor,
//                new SingleThreadedClaimStrategy(ENTRIES), new SleepingWaitStrategy());
//        disruptor.handleEventsWith(new AggregationTaskExecutorHandler());
//        ringBuffer = disruptor.start();
    }

    /**
     * SingletonHolder is loaded on the first execution of Singleton.getInstance()
     * or the first access to SingletonHolder.INSTANCE, not before.
     */
    private static class SingletonHolder {
        public static final AggregationServiceFactory instance = new AggregationServiceFactory();
    }

    public static AggregationServiceFactory getInstance() {
        return SingletonHolder.instance;
    }

    public AggregationServiceMBean getAggregationMBean() {
        if (aggregationMBean == null) {
            aggregationMBean = new AggregationServiceMBeanC();
//            ConfigurationFactory.registerMBean(aggregationMBean);
        }
        return aggregationMBean;
    }
    
    public AggregationServiceMBean getRFSAggregationMBean() {
        if (rfsAggregationMBean == null) {
        	rfsAggregationMBean = new RFSAggregationServiceMBeanC();
//            ConfigurationFactory.registerMBean(aggregationMBean);
        }
        return rfsAggregationMBean;
    }

    public ExecutorService getRequestExecutor() {
        if (requestExecutor == null) {
            requestExecutor  = Executors.newFixedThreadPool(getAggregationMBean().getRequestExecutorPoolSize());
        }
        return requestExecutor;
    }

//    public RingBuffer<AggregationTaskExecutor> getRingBuffer() {
//        return ringBuffer;
//    }

    public void stop() {
//        disruptor.shutdown();
        requestExecutor.shutdownNow();
    }
}
