package com.integral.aggregation.config;

import com.integral.aggregation.ASEndOfDayHandler;
import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.AggregationProcessorConfig;
import com.integral.aggregation.AggregationProcessorManager;
import com.integral.aggregation.AggregationServiceUtil;
import com.integral.aggregation.MultiProviderFilterConfig;
import com.integral.aggregation.util.FXHashMap;
import com.integral.aggregation.util.FXMap;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.is.common.liquidityProvision.functor.LiquidityProvisionRemoteNotificationFunctor;
import com.integral.is.common.pricemaking.functor.LiquidityRulePriceMakingRemoteNotificationFunctor;
import com.integral.log.GZipFileAppender;
import com.integral.log.GZipFileAppenderContainer;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.configuration.IdcMBeanC;
import com.integral.taker.TakerOrganizationFunction;
import com.integral.user.Organization;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 *
 */
public class AggregationServiceMBeanC extends IdcMBeanC implements AggregationServiceMBean {

    public static final String AGGREGATION_SERVICE_KEY = "IDC.Aggregation";
    public static final String QUOTE_TIER = "IDC.Aggregation.Quote.Tier";
    public static final String QUOTE_PUBLICATION_INTERVAL = "IDC.Aggregation.Quote.Publication.Interval";
    public static final String BEST_BID_OFFER_PUBLICATION_INTERVAL = "IDC.Aggregation.Quote.BestBidOffer.Publication.Interval";
    public static final String SAME_PRICE_BOOK_PUBLISHING_ENABLED = "IDC.Aggregation.SamePriceBookPublishing.Enabled";
    public static final String OFF_MARKET_REFERENCE_PROVIDERS = "IDC.Aggregation.OffMarketExclude.ReferenceProviders";
    public static final String REQUEST_EXECUTOR_POOL_SIZE = "IDC.Aggregation.Request.Executor.Pool.Size";
    public static final String AGGREGATION_SHOW_PROVIDER_NAME = "IDC.Aggregation.ShowProviderName";

    public static final String FILTER_MULTI_PROVIDER_FILTER_KEY = "IDC.Aggregation.MultiProviderFilter";
    public static final String FILTER_MULTI_PROVIDER_FILTER_ENABLED = "IDC.Aggregation.MultiProviderFilter.Enabled";
    public static final String FILTER_MULTI_PROVIDER_STALENESS_CHECK_ENABLED = "IDC.Aggregation.MultiProviderFilter.StalenessCheck.Enabled";
    public static final String FILTER_MULTI_PROVIDER_OFFMARKET_CHECK_ENABLED = "IDC.Aggregation.MultiProviderFilter.OffMarketFilter.Enabled";
    public static final String FILTER_MULTI_PROVIDER_INVERTED_RATE_CHECK_ENABLED = "IDC.Aggregation.MultiProviderFilter.InvertedRateFilter.Enabled";
    public static final String FILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCE = "IDC.Aggregation.MultiProviderFilter.InvertedRateTolerance";
    public static final String FILTER_MULTI_PROVIDER_OFFMARKET_MAX_PROVIDERS = "IDC.Aggregation.MultiProviderFilter.OffMarket.MaxProviders";
    public static final String FILTER_MULTI_PROVIDER_OFFMARKET_MIN_PROVIDERS = "IDC.Aggregation.MultiProviderFilter.OffMarket.MinProviders";
    public static final String FILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVAL = "IDC.Aggregation.MultiProviderFilter.StalenessCheck.Interval";
    public static final String FILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREAD = "IDC.Aggregation.MultiProviderFilter.OffMarket.Max.InverseSpread";

    public static final String AGGREGATOR_THREAD_SWEEP_INTERVAL = "IDC.Aggregation.Aggregator.Thread.Sweep.Interval";
    public static final String AGGREGATORS_PER_AGGREGATOR_THREAD = "IDC.Aggregation.Aggregators.Per.Aggregator.Thread";
    public static final String AGGREGATOR_THREAD_POOL_SIZE = "IDC.Aggregation.Aggregator.Thread.Pool.Size";
    public static final String AGGREGATOR_USER_NAME = "IDC.Aggregation.User.Name";

    public static final String AGGREGATOR_METRICS_PUB_TIME_BUCKETS = "IDC.Aggregation.Metrics.Pub.Time.Buckets";
    public static final String AGGREGATOR_METRICS_AGG_TIME_BUCKETS = "IDC.Aggregation.Metrics.Agg.Time.Buckets";
    public static final String AGGREGATOR_METRICS_SEND_TIME_BUCKETS = "IDC.Aggregation.Metrics.Send.Time.Buckets";
    public static final String AGGREGATOR_METRICS_LOG_TIME_BUCKETS = "IDC.Aggregation.Metrics.Log.Time.Buckets";

    public static final String AGGREGATION_TERM_ROUNDING_FACTOR = "IDC.Aggregation.Term.RoundingFactor";
    public static final String AGGREGATION_TERM_DISPLAY_AMOUNT_IN_BASE = "IDC.Aggregation.Term.DisplayAmountInBase";

    public static final String AGGREGATION_LOG_COMPRESSION_ENABLED = "IDC.Aggregation.Log.Compression.Enabled";

    public static final String AGGREGATION_LOG_BINARY_ENABLED = "IDC.Aggregation.Log.Binary.Enabled";
    public static final String AGGREGATION_LOG_RING_BUFFER_THRESHOLD = "IDC.Aggregation.Log.RingBuffer.Threshold";
    public static final String AGGREGATION_LOG_BYTE_BUFFER_SIZE = "IDC.Aggregation.Log.ByteBuffer.Size";
    public static final String AGGREGATION_LOG_PRINT_BYTES_CREATION_TIME = "IDC.Aggregation.Log.print.bytes.creation.time";
    public static final String AGGREGATION_LOG_HEALTH_CHECK_COUNT = "IDC.Aggregation.Log.health.check.count";
    public static final String AGGREGATION_LOG_BINARY_COMPRESSION_ENABLED = "IDC.Aggregation.Log.Binary.Compression.Enabled";
    public static final String AGGREGATION_LOG_BINARY_COMPRESSION_TYPE = "IDC.Aggregation.Log.Binary.Compression.type";
    public static final String AGGREGATION_LOG_MAX_TIERS_TO_LOG = "IDC.Aggregation.MaxTiers.To.Log";


    public static final String AGGREGATION_METRICS_LOG_TIME_PERIOD_IN_MINS = "IDC.Aggregation.metrics.log.time.period";

    private final String AGG_LOGGING_DISRUPTOR_COUNT_PROPERTY_NAME = "IDC.Aggregation.Log.Disruptor.Count";


    public static final String AGGREGATION_CACHED_QUOTE_ON_SUBSCRIPTION = "IDC.Aggregation.Cached.Quote.On.Subscription";

    public static final String AGGREGATION_EXECUTION_RULE_ENABLED = "IDC.Aggregation.Execution.Rule.Enabled";

    public static final String AGGREGATION_DISPLAY_NAME_FOR_FMA_PROVIDERS = "IDC.Aggregation.DisplayName.For.FMAProviders";

    public static final String AGGREGATION_SUBSCRIPTION_TO_CLOB_ENABLED = AGGREGATION_SERVICE_KEY + ".Clob.Subscription.Enabled";

    public static final String AGGREGATION_SUBSCRIPTION_TO_RISKNET_ENABLED = AGGREGATION_SERVICE_KEY + ".RiskNet.Subscription.Enabled";

    public static final String AGGREGATION_SUBSCRIPTION_CLIENT_TAG_MAPPING = AGGREGATION_SERVICE_KEY + ".ClientTag.Mapping";

    public static final String IDC_IS_PARTIAL_FILL_PROVIDERS = "Idc.IS.PartialFillProviders";

    public static final String AGGREGATION_FA_ENABLED = AGGREGATION_SERVICE_KEY + ".FullAmountAggregationEnabled";

    private int providerQuoteTier;
    private ConcurrentMap<String, FXMap<String, FXMap<String, Integer, Integer>, Integer>> providerQuoteTiers;

    private Set<String> referenceProviders;
    private ConcurrentMap<String, Set<String>> fiReferenceProviders;

    private Set<Organization> partialFillProviders;
    private boolean faAggregationEnabled;
    private Map<String,Boolean> faAggregationEnabledMap;
    private Integer quotePublicationInterval;
    private ConcurrentMap<String, Integer> fiQuotePublicationInterval;

    private Integer bestBidOfferPublicationInterval;
    private ConcurrentMap<String, Integer> fiBestBidOfferPublicationInterval;

    private Integer termAggregationRoundingFactor;
    private ConcurrentMap<String, FXMap<String, Integer, Integer>> fiTermAggregationRoundingFactor;
    private ConcurrentMap<String, Integer> ccyPairTermAggregationRoundingFactor;

    private Boolean isTermAggregationDisplayAmtInBase;
    private ConcurrentMap<String, Boolean> fiTermAggregationDisplayAmtInBase;

    private Boolean samePriceBookPublishing_Enabled;
    private ConcurrentMap<String, Boolean> fiSamePriceBookPublishingEnabled;

    private Boolean isProviderNameDisplayEnabled;
    private ConcurrentMap<String, Boolean> fiProviderNameDisplayEnabled;

    // MultiProvider filter related properties.
    private Boolean isMultiProviderFilterEnabled;
    private ConcurrentMap<String, Boolean> fiMultiProviderFilterEnabled;

    private Boolean isStalenessCheckEnabled;
    private ConcurrentMap<String, Boolean> fiStalenessCheckEnabled;

    private Boolean isOffMarketCheckEnabled;
    private ConcurrentMap<String, Boolean> fiOffMarketCheckEnabled;

    private Boolean isInvertedRateFilterCheckEnabled = true;
    private ConcurrentMap<String, Boolean> fiInvertedRateFilterCheckEnabled;

    private Double invertedRateTolerance;
    private ConcurrentMap<String, FXMap<String, Double, Double>> fiCcyPairInvertedRateTolerance;

    private Integer offMarketMinProviders;
    private ConcurrentMap<String, Integer> fiOffMarketMinProviders;

    private Integer offMarketMaxProviders;
    private ConcurrentMap<String, Integer> fiOffMarketMaxProviders;

    private Long stalenessCheckInterval;
    private ConcurrentMap<String, FXMap<String, Long, Long>> fiCcyPairStalenessCheckInterval;

    private Double offMarketMaxInverseSpread;
    private ConcurrentMap<String, FXMap<String, Double, Double>> fiCcyPairOffMarketMaxInverseSpread;

    private boolean isCachesInitialized = false;

    private String displayNameForFMAProviders;
    private ConcurrentMap<String,String> fiDisplayNameForFMAProviders;

    /**
     * Not initializing with any value because child class variables get initialized after
     * base class is initialized completely and if any value is assigned on class level then it will override
     * the value set in the initialize method
     */
    private int aggregatorThreadSweepInterval;
    private int aggregatorsPerThread;
    private int aggregatorThreadPoolSize;
    private boolean shouldLogAggServiceBinaryLoggingByteCreationTime;
    private int disruptorHealthCheckEventsCount;
    private int numberOfDisruptorsForAggLogging;
    private int maxTiersToLog;
    private int timePeriodToLogAggServiceMetrics;

    private String aggregationUserShortName;
    // Aggregation metrics buckets
    private long publicationTimeBuckets[];
    private long aggregationTimeBuckets[];
    private long sendTimeBuckets[];
    private long logTimeBuckets[];

    private boolean isAggLogCompressionEnabled;

    private boolean isCachedQuoteOnSubscription;
    private ConcurrentMap<String, Boolean> fiCachedQuoteOnSubscription;
    private boolean isAggExecRuleEnabled;
    private ConcurrentMap<String, Boolean> fiAggExecRuleEnabled;

    private String[] binaryLogEnabledOrgs;
    private boolean isAggLogBinaryEnabled;
    private int aggBinaryLogBufferSize;
    public final int AGG_SERVICE_LOGGER_RING_BUFFER_SIZE = 2048;
    private Map<String, Boolean> fiVsBinaryLogEnabledLog = new ConcurrentHashMap<String, Boolean>();
    private int aggLogRingBufThreshold;
    private int aggLogByteBufferSize;

    private Boolean clobSubscriptionEnabled;
    private ConcurrentHashMap<String,Boolean> fiClobSubscriptionEnabled;

    private Boolean riskNetSubscriptionEnabled;
    private ConcurrentHashMap<String,Boolean> fiRiskNetSubscriptionEnabled;

    private ConcurrentHashMap<String,Map<String,String>> clientTagVersusOrgShortName;

    static {
        //Register Change notification handler for Liquidity Rules and Price Making page.
        LiquidityProvisionRemoteNotificationFunctor.addObserver(new LiquidityProvisionAggregationChangeHandler());
        LiquidityRulePriceMakingRemoteNotificationFunctor.addObserver(new LiquidityRulePriceMakingChangeHandler());
        ASEndOfDayHandler handler = new ASEndOfDayHandler();
        EndOfDayServiceFactory.getEndOfDayService().addEndOfDayHandler(handler);
        EndOfDayServiceFactory.getEndOfDayService().addNZDEndOfDayHandler(handler);
    }

    public AggregationServiceMBeanC(String fileName) {
        super(fileName);
        this.isCachesInitialized = true;
    }
    public AggregationServiceMBeanC() {
        this("IdcAggregationServiceMBean");       
    }

    private void initializeCaches() {
        this.referenceProviders = new HashSet<String>(8);
        this.fiReferenceProviders = new ConcurrentHashMap<String, Set<String>>(8);
        this.fiQuotePublicationInterval = new ConcurrentHashMap<String, Integer>(8);
        this.fiBestBidOfferPublicationInterval = new ConcurrentHashMap<String, Integer>(8);
        this.fiTermAggregationRoundingFactor = new ConcurrentHashMap<String, FXMap<String, Integer, Integer>>(8);
        this.ccyPairTermAggregationRoundingFactor = new ConcurrentHashMap<String, Integer>(8);
        this.fiTermAggregationDisplayAmtInBase = new ConcurrentHashMap<String, Boolean>(8);
        this.fiSamePriceBookPublishingEnabled = new ConcurrentHashMap<String, Boolean>(8);
        this.fiProviderNameDisplayEnabled = new ConcurrentHashMap<String, Boolean>(8);
        this.fiMultiProviderFilterEnabled = new ConcurrentHashMap<String, Boolean>(8);
        this.fiStalenessCheckEnabled = new ConcurrentHashMap<String, Boolean>(8);
        this.fiOffMarketCheckEnabled = new ConcurrentHashMap<String, Boolean>(8);
        this.fiInvertedRateFilterCheckEnabled = new ConcurrentHashMap<String, Boolean>(8);
        this.fiOffMarketMinProviders = new ConcurrentHashMap<String, Integer>(8);
        this.fiOffMarketMaxProviders = new ConcurrentHashMap<String, Integer>(8);
        this.fiCcyPairStalenessCheckInterval = new ConcurrentHashMap<String, FXMap<String, Long, Long>>(8);
        this.fiCcyPairOffMarketMaxInverseSpread = new ConcurrentHashMap<String, FXMap<String, Double, Double>>(8);
        this.fiCcyPairInvertedRateTolerance = new ConcurrentHashMap<String, FXMap<String, Double, Double>>(8);
        this.fiCachedQuoteOnSubscription = new ConcurrentHashMap<String, Boolean>(8);
        this.fiAggExecRuleEnabled = new ConcurrentHashMap<String, Boolean>(8);
        this.fiDisplayNameForFMAProviders = new ConcurrentHashMap<String, String>(8);
        this.fiClobSubscriptionEnabled = new ConcurrentHashMap<String, Boolean>( 8 );
        this.fiRiskNetSubscriptionEnabled = new ConcurrentHashMap<String, Boolean>( 8 );
        this.clientTagVersusOrgShortName = new ConcurrentHashMap<String, Map<String,String>>( 8 );
        this.partialFillProviders= new HashSet<Organization>(8);
    }


    private void logBinaryLoggingProperties() {
        log.info(" AGG_SERVICE_BINARY_LOGGING_PROPERTIES ");
        log.info(AGGREGATION_LOG_BINARY_ENABLED + "=" + isAggLogBinaryEnabled);
        log.info(" IDC.Aggregation.Log.Binary.Disruptor.Buffer.Size" + "=" + aggBinaryLogBufferSize);
        log.info(AGGREGATION_LOG_RING_BUFFER_THRESHOLD + "=" + aggLogRingBufThreshold);
        log.info(AGGREGATION_LOG_BYTE_BUFFER_SIZE + "=" + aggLogByteBufferSize);
        log.info(AGGREGATION_LOG_PRINT_BYTES_CREATION_TIME + "=" + shouldLogAggServiceBinaryLoggingByteCreationTime);
        log.info(AGGREGATION_LOG_HEALTH_CHECK_COUNT + "=" + disruptorHealthCheckEventsCount);
        log.info(AGG_LOGGING_DISRUPTOR_COUNT_PROPERTY_NAME + "=" + numberOfDisruptorsForAggLogging);
    }

    private void initializeBinaryLoggingRelatedConfig() {
        isAggLogBinaryEnabled = getBooleanProperty(getAGGREGATION_LOG_BINARY_ENABLEDProperty(), getDefaultAGGREGATION_LOG_BINARY_ENABLED());
        aggBinaryLogBufferSize = getIntProperty("IDC.Aggregation.Log.Binary.Disruptor.Buffer.Size", AGG_SERVICE_LOGGER_RING_BUFFER_SIZE);
        aggLogRingBufThreshold = getIntProperty(AGGREGATION_LOG_RING_BUFFER_THRESHOLD, 60);
        aggLogByteBufferSize = getIntProperty(AGGREGATION_LOG_BYTE_BUFFER_SIZE, 2048);
        shouldLogAggServiceBinaryLoggingByteCreationTime = getBooleanProperty(AGGREGATION_LOG_PRINT_BYTES_CREATION_TIME, false);
        disruptorHealthCheckEventsCount = getIntProperty(AGGREGATION_LOG_HEALTH_CHECK_COUNT, 500);
        numberOfDisruptorsForAggLogging = getIntProperty(AGG_LOGGING_DISRUPTOR_COUNT_PROPERTY_NAME, 2);
        maxTiersToLog = getIntProperty(AGGREGATION_LOG_MAX_TIERS_TO_LOG, 5);
        logBinaryLoggingProperties();
    }

    public void initialize() {
        if (!isCachesInitialized) {
            aggregatorThreadSweepInterval = getIntProperty(getAGGREGATOR_THREAD_SWEEP_INTERVALProperty(), getDefaultAGGREGATOR_THREAD_SWEEP_INTERVAL());
            aggregatorsPerThread = getIntProperty(getAGGREGATORS_PER_AGGREGATOR_THREADProperty(), getDefaultAGGREGATORS_PER_AGGREGATOR_THREAD());
            aggregatorThreadPoolSize = getIntProperty(getAGGREGATOR_THREAD_POOL_SIZEProperty(), getDefaultAGGREGATOR_THREAD_POOL_SIZE());
            aggregationUserShortName = getStringProperty(getAGGREGATOR_USER_NAMEProperty(), getDefaultAGGREGATOR_USER_NAME());
            isAggLogCompressionEnabled = getBooleanProperty(getAGGREGATION_LOG_COMPRESSION_ENABLEDProperty(), getDefaultAGGREGATION_LOG_COMPRESSION_ENABLED());


            timePeriodToLogAggServiceMetrics = getIntProperty(AGGREGATION_METRICS_LOG_TIME_PERIOD_IN_MINS, 10);

            initializeCaches();
            initializeAggExecRuleEnabled();
            initializeQuotePublicationInterval();
            initializeBestBidOfferPublicationInterval();
            initializeTermAggregationRoundingFactors();
            initializeTermAggregationAmtInBase();
            initializeSamePriceBookPublishingEnabled();
            initializeProviderQuoteTiers();
            initializeReferenceProviders();
            initializePartialFillProviders();
            initializeProviderNameDisplayEnabled();
            initializeRateFilterDetails();
            initializeAggregatorMetricsBuckets();
            initializeCachedQuoteSubscription();
            initializeBinaryLoggingRelatedConfig();
            initializeDisplayNameForFMAProviders();
            initializeClobSubscriptionEnabled();
            initializeRiskNetSubscriptionEnabled();
            initializeClientTagMappings();
            initializeFAAggregationProperties();
        }
    }

    protected void initializeFAAggregationProperties() {
        faAggregationEnabled = getBooleanProperty( AGGREGATION_FA_ENABLED,false );
        Map<String,Boolean> bMap = initSingleSuffixBooleanPropertyMap(AGGREGATION_FA_ENABLED,null);
        if( bMap != null && !bMap.isEmpty()){
            faAggregationEnabledMap = bMap;
        }
        else{
            faAggregationEnabledMap = null;
        }
    }

    protected String getAGGREGATOR_THREAD_SWEEP_INTERVALProperty() {
        return AGGREGATOR_THREAD_SWEEP_INTERVAL;
    }

    protected int getDefaultAGGREGATOR_THREAD_SWEEP_INTERVAL() {
        return 5;
    }

    protected String getAGGREGATORS_PER_AGGREGATOR_THREADProperty() {
        return AGGREGATORS_PER_AGGREGATOR_THREAD;
    }

    protected int getDefaultAGGREGATORS_PER_AGGREGATOR_THREAD() {
        return 75;
    }

    protected String getAGGREGATOR_THREAD_POOL_SIZEProperty() {
        return AGGREGATOR_THREAD_POOL_SIZE;
    }

    protected int getDefaultAGGREGATOR_THREAD_POOL_SIZE() {
        return 4;
    }

    protected String getAGGREGATOR_USER_NAMEProperty() {
        return AGGREGATOR_USER_NAME;
    }

    protected String getDefaultAGGREGATOR_USER_NAME() {
        return "AggregationUser";
    }

    protected String getAGGREGATION_LOG_COMPRESSION_ENABLEDProperty() {
        return AGGREGATION_LOG_COMPRESSION_ENABLED;
    }

    protected boolean getDefaultAGGREGATION_LOG_COMPRESSION_ENABLED() {
        return true;
    }

    protected String getAGGREGATION_LOG_BINARY_ENABLEDProperty() {
        return AGGREGATION_LOG_BINARY_ENABLED;
    }

    protected boolean getDefaultAGGREGATION_LOG_BINARY_ENABLED() {
        return false;
    }

    protected String getAGGREGATION_TERM_DISPLAY_AMOUNT_IN_BASEProperty() {
        return AGGREGATION_TERM_DISPLAY_AMOUNT_IN_BASE;
    }

    protected boolean getDefaultAGGREGATION_TERM_DISPLAY_AMOUNT_IN_BASE() {
        return false;
    }

    protected String getAGGREGATION_TERM_ROUNDING_FACTORProperty() {
        return AGGREGATION_TERM_ROUNDING_FACTOR;
    }

    protected int getDefaultAGGREGATION_TERM_ROUNDING_FACTOR() {
        return 1000;
    }

    protected String getAGGREGATOR_METRICS_PUB_TIME_BUCKETSProperty() {
        return AGGREGATOR_METRICS_PUB_TIME_BUCKETS;
    }

    protected String getDefaultAGGREGATOR_METRICS_PUB_TIME_BUCKETS() {
        return "250,500,750,1000,1250,1500,1750,2000,2250,2500,2750,3000";
    }

    protected String getAGGREGATOR_METRICS_AGG_TIME_BUCKETSProperty() {
        return AGGREGATOR_METRICS_AGG_TIME_BUCKETS;
    }

    protected String getDefaultAGGREGATOR_METRICS_AGG_TIME_BUCKETS() {
        return "100,200,300,400,500,600,700,800,900,1000,1200,1500,2000";
    }

    protected String getAGGREGATOR_METRICS_SEND_TIME_BUCKETSProperty() {
        return AGGREGATOR_METRICS_SEND_TIME_BUCKETS;
    }

    protected String getDefaultAGGREGATOR_METRICS_SEND_TIME_BUCKETS() {
        return "100,200,300,400,500,600,700,800,900,1000,1200,1500,2000";
    }

    protected String getAGGREGATOR_METRICS_LOG_TIME_BUCKETSProperty() {
        return AGGREGATOR_METRICS_LOG_TIME_BUCKETS;
    }

    protected String getDefaultAGGREGATOR_METRICS_LOG_TIME_BUCKETS() {
        return "100,200,300,400,500,600,700,800,900,1000,1200,1500,2000";
    }

    protected String getAGGREGATION_CACHED_QUOTE_ON_SUBSCRIPTIONProperty() {
        return AGGREGATION_CACHED_QUOTE_ON_SUBSCRIPTION;
    }

    protected boolean getDefaultAGGREGATION_CACHED_QUOTE_ON_SUBSCRIPTION() {
        return false;
    }

    protected String getAGGREGATION_EXECUTION_RULE_ENABLEDProperty() {
        return AGGREGATION_EXECUTION_RULE_ENABLED;
    }

    protected boolean getDefaultAGGREGATION_EXECUTION_RULE_ENABLED() {
        return false;
    }


    protected String getSAME_PRICE_BOOK_PUBLISHING_ENABLEDProperty() {
        return SAME_PRICE_BOOK_PUBLISHING_ENABLED;
    }

    protected boolean getDefaultSAME_PRICE_BOOK_PUBLISHING_ENABLED() {
        return true;
    }

    protected String getBEST_BID_OFFER_PUBLICATION_INTERVALProperty() {
        return BEST_BID_OFFER_PUBLICATION_INTERVAL;
    }

    protected int getDefaultBEST_BID_OFFER_PUBLICATION_INTERVAL() {
        return 50;
    }


    protected String getQUOTE_PUBLICATION_INTERVALProperty() {
        return QUOTE_PUBLICATION_INTERVAL;
    }

    protected int getDefaultQUOTE_PUBLICATION_INTERVAL() {
        return 100;
    }

    protected String getAGGREGATION_SHOW_PROVIDER_NAMEProperty() {
        return AGGREGATION_SHOW_PROVIDER_NAME;
    }

    protected String getAGGREGATION_DISPLAY_NAME_FOR_FMA_PROVIDERSProperty() {
        return AGGREGATION_DISPLAY_NAME_FOR_FMA_PROVIDERS;
    }

    protected boolean getDefaultAGGREGATION_SHOW_PROVIDER_NAME() {
        return false;
    }

    protected String getFILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREADProperty() {
        return FILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREAD;
    }

    protected double getDefaultFILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREAD() {
        return 0;
    }


    protected String getFILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVALProperty() {
        return FILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVAL;
    }

    protected long getDefaultFILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVAL() {
        return 5;
    }

    protected String getFILTER_MULTI_PROVIDER_OFFMARKET_MIN_PROVIDERSProperty() {
        return FILTER_MULTI_PROVIDER_OFFMARKET_MIN_PROVIDERS;
    }

    protected int getDefaultFILTER_MULTI_PROVIDER_OFFMARKET_MIN_PROVIDERS() {
        return 3;
    }

    protected String getFILTER_MULTI_PROVIDER_OFFMARKET_MAX_PROVIDERSProperty() {
        return FILTER_MULTI_PROVIDER_OFFMARKET_MAX_PROVIDERS;
    }

    protected int getDefaultFILTER_MULTI_PROVIDER_OFFMARKET_MAX_PROVIDERS() {
        return 5;
    }

    protected String getFILTER_MULTI_PROVIDER_INVERTED_RATE_CHECK_ENABLEDProperty() {
        return FILTER_MULTI_PROVIDER_INVERTED_RATE_CHECK_ENABLED;
    }

    protected boolean getDefaultFILTER_MULTI_PROVIDER_INVERTED_RATE_CHECK_ENABLED() {
        return false;
    }

    protected String getFILTER_MULTI_PROVIDER_OFFMARKET_CHECK_ENABLEDProperty() {
        return FILTER_MULTI_PROVIDER_OFFMARKET_CHECK_ENABLED;
    }

    protected boolean getDefaultFILTER_MULTI_PROVIDER_OFFMARKET_CHECK_ENABLED() {
        return false;
    }

    protected String getFILTER_MULTI_PROVIDER_STALENESS_CHECK_ENABLEDProperty() {
        return FILTER_MULTI_PROVIDER_STALENESS_CHECK_ENABLED;
    }

    protected boolean getDefaultFILTER_MULTI_PROVIDER_STALENESS_CHECK_ENABLED() {
        return false;
    }

    protected String getFILTER_MULTI_PROVIDER_FILTER_ENABLEDProperty() {
        return FILTER_MULTI_PROVIDER_FILTER_ENABLED;
    }

    protected boolean getDefaultFILTER_MULTI_PROVIDER_FILTER_ENABLED() {
        return false;
    }

    protected String getFILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCEProperty() {
        return FILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCE;
    }

    protected double getDefaultFILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCE() {
        return 0;
    }

    protected String getREQUEST_EXECUTOR_POOL_SIZEProperty() {
        return REQUEST_EXECUTOR_POOL_SIZE;
    }

    protected int getDefaultREQUEST_EXECUTOR_POOL_SIZE() {
        return 1;
    }


    protected String getQUOTE_TIERProperty() {
        return QUOTE_TIER;
    }

    protected int getDefaultQUOTE_TIER() {
        return 1;
    }

    protected String getOFF_MARKET_REFERENCE_PROVIDERSProperty() {
        return OFF_MARKET_REFERENCE_PROVIDERS;
    }

    protected String getFILTER_MULTI_PROVIDER_FILTER_KEYProperty() {
        return FILTER_MULTI_PROVIDER_FILTER_KEY;
    }

    protected String getAGGREGATION_SERVICE_KEYProperty() {
        return AGGREGATION_SERVICE_KEY;
    }

    private void initializeTermAggregationAmtInBase() {
        this.isTermAggregationDisplayAmtInBase = getBooleanProperty(getAGGREGATION_TERM_DISPLAY_AMOUNT_IN_BASEProperty(), getDefaultAGGREGATION_TERM_DISPLAY_AMOUNT_IN_BASE());
        initializeBooleanTypeFICache(fiTermAggregationDisplayAmtInBase, getAGGREGATION_TERM_DISPLAY_AMOUNT_IN_BASEProperty());
    }

    private void initializeTermAggregationRoundingFactors() {
        this.termAggregationRoundingFactor = getIntProperty(getAGGREGATION_TERM_ROUNDING_FACTORProperty(), getDefaultAGGREGATION_TERM_ROUNDING_FACTOR());
        initializeFITermAggregationRoundingFactor();
    }

    private void initializeAggregatorMetricsBuckets() {
        String strPublicationTimeBuckets = getStringProperty(getAGGREGATOR_METRICS_PUB_TIME_BUCKETSProperty(), getDefaultAGGREGATOR_METRICS_PUB_TIME_BUCKETS());
        String strAggregationTimeBuckets = getStringProperty(getAGGREGATOR_METRICS_AGG_TIME_BUCKETSProperty(), getDefaultAGGREGATOR_METRICS_AGG_TIME_BUCKETS());
        String strSendTimeBuckets = getStringProperty(getAGGREGATOR_METRICS_SEND_TIME_BUCKETSProperty(), getDefaultAGGREGATOR_METRICS_SEND_TIME_BUCKETS());
        String strLogTimeBuckets = getStringProperty(getAGGREGATOR_METRICS_LOG_TIME_BUCKETSProperty(), getDefaultAGGREGATOR_METRICS_LOG_TIME_BUCKETS());

        publicationTimeBuckets = parseBucketSizes(strPublicationTimeBuckets, publicationTimeBuckets);
        aggregationTimeBuckets = parseBucketSizes(strAggregationTimeBuckets, aggregationTimeBuckets);
        sendTimeBuckets = parseBucketSizes(strSendTimeBuckets, sendTimeBuckets);
        logTimeBuckets = parseBucketSizes(strLogTimeBuckets, logTimeBuckets);
    }

    private void initializeCachedQuoteSubscription() {
        this.isCachedQuoteOnSubscription = getBooleanProperty(getAGGREGATION_CACHED_QUOTE_ON_SUBSCRIPTIONProperty(), getDefaultAGGREGATION_CACHED_QUOTE_ON_SUBSCRIPTION());
        initializeBooleanTypeFICache(fiCachedQuoteOnSubscription, getAGGREGATION_CACHED_QUOTE_ON_SUBSCRIPTIONProperty());
    }

    private void initializeAggExecRuleEnabled() {
        this.isAggExecRuleEnabled = getBooleanProperty(getAGGREGATION_EXECUTION_RULE_ENABLEDProperty(), getDefaultAGGREGATION_EXECUTION_RULE_ENABLED());
        initializeBooleanTypeFICache(fiAggExecRuleEnabled, getAGGREGATION_EXECUTION_RULE_ENABLEDProperty());
    }

    private long[] parseBucketSizes(String strBuckets, long[] defaultValue) {
        try {
            if (strBuckets != null && !strBuckets.trim().equals("")) {
                String buckets[] = strBuckets.split(",");
                long[] publicationTimeBuckets = new long[buckets.length];

                for (int i = 0; i < buckets.length; i++) {
                    publicationTimeBuckets[i] = Long.parseLong(buckets[i]);
                }
                return publicationTimeBuckets;
            }
        } catch (Exception exc) {
            log.error("Exception while parsing the aggregator metrics bucket ranges." + strBuckets);
            return defaultValue;
        }
        return defaultValue;
    }

    private void initializeRateFilterDetails() {
        initializeMultiProviderRateFilterDetails();
    }

    private void initializeMultiProviderRateFilterDetails() {
        initializeMultiProviderFilterEnabled();
        initializeStalenessCheckEnabled();
        initializeOffMarketCheckEnabled();
        initializeInvertedRateFilterCheckEnabled();

        initializeOffMarketMaxProviders();
        initializeOffMarketMinProviders();

        initializeStalenessCheckInterval();
        initializeOffMarketMaxInverseSpread();
        initializeInvertedRateTolerance();

    }

    private void initializeSamePriceBookPublishingEnabled() {
        samePriceBookPublishing_Enabled = getBooleanProperty(getSAME_PRICE_BOOK_PUBLISHING_ENABLEDProperty(), getDefaultSAME_PRICE_BOOK_PUBLISHING_ENABLED());
        initializeBooleanTypeFICache(fiSamePriceBookPublishingEnabled, getSAME_PRICE_BOOK_PUBLISHING_ENABLEDProperty());
    }

    private void initializeBestBidOfferPublicationInterval() {
        bestBidOfferPublicationInterval = getIntProperty(getBEST_BID_OFFER_PUBLICATION_INTERVALProperty(), getDefaultBEST_BID_OFFER_PUBLICATION_INTERVAL());
        initializeIntTypeFICache(fiBestBidOfferPublicationInterval, getBEST_BID_OFFER_PUBLICATION_INTERVALProperty());
    }

    private void initializeQuotePublicationInterval() {
        this.quotePublicationInterval = getIntProperty(getQUOTE_PUBLICATION_INTERVALProperty(), getDefaultQUOTE_PUBLICATION_INTERVAL());
        initializeIntTypeFICache(this.fiQuotePublicationInterval, getQUOTE_PUBLICATION_INTERVALProperty());
    }

    private void initializeProviderNameDisplayEnabled() {
        isProviderNameDisplayEnabled = getBooleanProperty(getAGGREGATION_SHOW_PROVIDER_NAMEProperty(), getDefaultAGGREGATION_SHOW_PROVIDER_NAME());
        initializeBooleanTypeFICache(fiProviderNameDisplayEnabled, getAGGREGATION_SHOW_PROVIDER_NAMEProperty());
    }

    private void initializeDisplayNameForFMAProviders() {
        displayNameForFMAProviders = getStringProperty(getAGGREGATION_DISPLAY_NAME_FOR_FMA_PROVIDERSProperty(), null);
        initializeStringTypeFICache(fiDisplayNameForFMAProviders, getAGGREGATION_DISPLAY_NAME_FOR_FMA_PROVIDERSProperty());
    }

    private void initializeOffMarketMaxInverseSpread() {
        offMarketMaxInverseSpread = getDoubleProperty(getFILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREADProperty(), getDefaultFILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREAD());
        initializeOffMarketInverseSpreads();
    }

    private void initializeInvertedRateTolerance() {
        invertedRateTolerance = getDoubleProperty(getFILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCEProperty(), getDefaultFILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCE());
        initializeInvertedRateTolerances();
    }

    private void initializeStalenessCheckInterval() {
        stalenessCheckInterval = getLongProperty(getFILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVALProperty(), getDefaultFILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVAL());
        initializeFIStalenessCheckIntervals();
    }

    private void initializeOffMarketMinProviders() {
        offMarketMinProviders = getIntProperty(getFILTER_MULTI_PROVIDER_OFFMARKET_MIN_PROVIDERSProperty(), getDefaultFILTER_MULTI_PROVIDER_OFFMARKET_MIN_PROVIDERS());
        initializeIntTypeFICache(fiOffMarketMinProviders, getFILTER_MULTI_PROVIDER_OFFMARKET_MIN_PROVIDERSProperty());
    }

    private void initializeOffMarketMaxProviders() {
        offMarketMaxProviders = getIntProperty(getFILTER_MULTI_PROVIDER_OFFMARKET_MAX_PROVIDERSProperty(), getDefaultFILTER_MULTI_PROVIDER_OFFMARKET_MAX_PROVIDERS());
        initializeIntTypeFICache(fiOffMarketMaxProviders, getFILTER_MULTI_PROVIDER_OFFMARKET_MAX_PROVIDERSProperty());
    }

    private void initializeInvertedRateFilterCheckEnabled() {
        isInvertedRateFilterCheckEnabled = getBooleanProperty(getFILTER_MULTI_PROVIDER_INVERTED_RATE_CHECK_ENABLEDProperty(), getDefaultFILTER_MULTI_PROVIDER_INVERTED_RATE_CHECK_ENABLED());
        initializeBooleanTypeFICache(fiInvertedRateFilterCheckEnabled, getFILTER_MULTI_PROVIDER_INVERTED_RATE_CHECK_ENABLEDProperty());
    }

    private void initializeOffMarketCheckEnabled() {
        isOffMarketCheckEnabled = getBooleanProperty(getFILTER_MULTI_PROVIDER_OFFMARKET_CHECK_ENABLEDProperty(), getDefaultFILTER_MULTI_PROVIDER_OFFMARKET_CHECK_ENABLED());
        initializeBooleanTypeFICache(fiOffMarketCheckEnabled, getFILTER_MULTI_PROVIDER_OFFMARKET_CHECK_ENABLEDProperty());
    }

    private void initializeStalenessCheckEnabled() {
        isStalenessCheckEnabled = getBooleanProperty(getFILTER_MULTI_PROVIDER_STALENESS_CHECK_ENABLEDProperty(), getDefaultFILTER_MULTI_PROVIDER_STALENESS_CHECK_ENABLED());
        initializeBooleanTypeFICache(fiStalenessCheckEnabled, getFILTER_MULTI_PROVIDER_STALENESS_CHECK_ENABLEDProperty());
    }

    private void initializeMultiProviderFilterEnabled() {
        isMultiProviderFilterEnabled = getBooleanProperty(getFILTER_MULTI_PROVIDER_FILTER_ENABLEDProperty(), getDefaultFILTER_MULTI_PROVIDER_FILTER_ENABLED());
        initializeBooleanTypeFICache(fiMultiProviderFilterEnabled, getFILTER_MULTI_PROVIDER_FILTER_ENABLEDProperty());
    }

    private void initializeClobSubscriptionEnabled(){
        clobSubscriptionEnabled = getBooleanProperty( AGGREGATION_SUBSCRIPTION_TO_CLOB_ENABLED,false );
        initializeBooleanTypeFICache( fiClobSubscriptionEnabled, AGGREGATION_SUBSCRIPTION_TO_CLOB_ENABLED);
    }

    private void initializeRiskNetSubscriptionEnabled(){
        riskNetSubscriptionEnabled = getBooleanProperty( AGGREGATION_SUBSCRIPTION_TO_RISKNET_ENABLED,false );
        initializeBooleanTypeFICache( fiRiskNetSubscriptionEnabled, AGGREGATION_SUBSCRIPTION_TO_RISKNET_ENABLED);
    }

    private void initializeClientTagMappings(){
        initializeClientTagMappingCache( clientTagVersusOrgShortName,AGGREGATION_SUBSCRIPTION_CLIENT_TAG_MAPPING );
    }

    private void initializeBooleanTypeFICache(ConcurrentMap<String, Boolean> fiCache, String key) {
        if (fiCache == null) {
            fiCache = new ConcurrentHashMap<String, Boolean>(8);
        } else {
            fiCache.clear();
        }
        Properties fiProperties = getPropertiesWithPrefix(key);

        for (Map.Entry entry : fiProperties.entrySet()) {
            String _key = (String) entry.getKey();
            String value = (String) entry.getValue();

            if (_key.length() == key.length()) continue;

            String fiName = _key.substring(key.length() + 1);

            if (!fiName.trim().equals("")) {
                fiCache.put(fiName, Boolean.valueOf(value));
            }
        }
    }

    private void initializeIntTypeFICache(ConcurrentMap<String, Integer> fiCache, String key) {
        if (fiCache == null) {
            fiCache = new ConcurrentHashMap<String, Integer>(8);
        } else {
            fiCache.clear();
        }
        Properties fiProperties = getPropertiesWithPrefix(key);

        for (Map.Entry entry : fiProperties.entrySet()) {
            String _key = (String) entry.getKey();
            String value = (String) entry.getValue();

            if (_key.length() == key.length()) continue;

            String fiName = _key.substring(key.length() + 1);

            if (!fiName.trim().equals("")) {
                fiCache.put(fiName, Integer.valueOf(value));
            }
        }
    }

    private void initializeStringTypeFICache(ConcurrentMap<String,String> fiCache, String key) {
        if(fiCache == null) {
            fiCache = new ConcurrentHashMap<String, String>(8);
        } else {
            fiCache.clear();
        }

        Properties fiProperties = getPropertiesWithPrefix(key);

        for(Map.Entry entry : fiProperties.entrySet()) {
            String _key = (String) entry.getKey();
            String value = (String) entry.getValue();

            if (_key.length() == key.length()) continue;

            String fiName = _key.substring(key.length() + 1);

            if (!fiName.trim().equals("")) {
                fiCache.put(fiName, value);
            }
        }
    }

    private void initializeClientTagMappingCache(ConcurrentMap<String,Map<String,String>> clientTagMappingCache, String key){
        if(clientTagMappingCache == null) {
            clientTagMappingCache = new ConcurrentHashMap<String, Map<String,String>>(8);
        } else {
            clientTagMappingCache.clear();
        }

        Properties fiProperties = getPropertiesWithPrefix(key);

        for(Map.Entry entry : fiProperties.entrySet()) {
            String _key = (String) entry.getKey();
            String value = (String) entry.getValue();

            if (_key.length() == key.length()) continue;

            String fiName = _key.substring(key.length() + 1);

            if (!fiName.trim().equals("")) {
                String[] clientTagFiMappings = value.split( "," );
                HashMap<String,String> clientTagFIMappings = new HashMap<String, String>(  );
                for(String clientTagFiMapping : clientTagFiMappings){
                    String[] clientTagFi = clientTagFiMapping.split( "~" );
                    clientTagFIMappings.put( clientTagFi[0] ,clientTagFi[1]);
                }
                clientTagMappingCache.put(fiName, clientTagFIMappings);
            }
        }
    }

    private void initializeOffMarketInverseSpreads() {
        if (fiCcyPairOffMarketMaxInverseSpread == null) {
            fiCcyPairOffMarketMaxInverseSpread = new ConcurrentHashMap<String, FXMap<String, Double, Double>>(8);
        } else {
            fiCcyPairOffMarketMaxInverseSpread.clear();
        }
        String propertyFILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREAD = getFILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREADProperty();
        Properties inverseSpreads = getPropertiesWithPrefix(propertyFILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREAD);

        for (Map.Entry entry : inverseSpreads.entrySet()) {
            String key = (String) entry.getKey();
            String value = (String) entry.getValue();

            if (key.length() == propertyFILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREAD.length()) continue;

            Double doubleValue = offMarketMaxInverseSpread;
            try {
                doubleValue = Double.valueOf(value);
            } catch (Exception exc) {
                log.error("AggregationServiceMBeanC.initializeOffMarketInverseSpreads wrong value for key - " + key + " value - " + value);
            }

            String attributes = key.substring(propertyFILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREAD.length() + 1);
            String keys[] = attributes.split("\\.");
            int size = keys.length;

            if (size >= 1) {
                String fiName = keys[0];
                FXMap<String, Double, Double> fiMap = fiCcyPairOffMarketMaxInverseSpread.get(fiName);
                if (fiMap == null) {
                    fiMap = new FXHashMap<String, Double, Double>(10);
                    fiCcyPairOffMarketMaxInverseSpread.put(fiName, fiMap);
                }

                Double fiDefaultValue = fiMap.getDefaultValue();
                fiDefaultValue = size == 1 ? doubleValue : (fiDefaultValue == null ? offMarketMaxInverseSpread : fiDefaultValue);
                fiMap.setDefaultValue(fiDefaultValue);

                if (size >= 2) {
                    String cpName = keys[1];
                    fiMap.put(cpName, doubleValue);
                }
            }
        }
    }

    private void initializeInvertedRateTolerances() {
        if (fiCcyPairInvertedRateTolerance == null) {
            fiCcyPairInvertedRateTolerance = new ConcurrentHashMap<String, FXMap<String, Double, Double>>(8);
        } else {
            fiCcyPairInvertedRateTolerance.clear();
        }
        String propertyFILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCE = getFILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCEProperty();
        Properties inverseSpreads = getPropertiesWithPrefix(propertyFILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCE);

        for (Map.Entry entry : inverseSpreads.entrySet()) {
            String key = (String) entry.getKey();
            String value = (String) entry.getValue();

            if (key.length() == propertyFILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCE.length()) continue;

            Double doubleValue = invertedRateTolerance;
            try {
                doubleValue = Double.valueOf(value);
            } catch (Exception exc) {
                log.error("AggregationServiceMBeanC.initializeInvertedRateTolerances wrong value for key - " + key + " value - " + value);
            }

            String attributes = key.substring(propertyFILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCE.length() + 1);
            String keys[] = attributes.split("\\.");
            int size = keys.length;

            if (size >= 1) {
                String fiName = keys[0];
                FXMap<String, Double, Double> fiMap = fiCcyPairInvertedRateTolerance.get(fiName);
                if (fiMap == null) {
                    fiMap = new FXHashMap<String, Double, Double>(10);
                    fiCcyPairInvertedRateTolerance.put(fiName, fiMap);
                }

                Double fiDefaultValue = fiMap.getDefaultValue();
                fiDefaultValue = size == 1 ? doubleValue : (fiDefaultValue == null ? invertedRateTolerance : fiDefaultValue);
                fiMap.setDefaultValue(fiDefaultValue);

                if (size >= 2) {
                    String cpName = keys[1];
                    fiMap.put(cpName, doubleValue);
                }
            }
        }
    }

    //If value specified is null, 0 or negative, the staleness filter should fall back to org or default level
    private boolean validateStaleInterval(String key, String value) {
        if (value == null || value.trim().length() == 0) {
            return false;
        } else {
            try {
                Long longValue = Long.valueOf(value);
                if (longValue <= 0) {
                    return false;
                }
            } catch (Exception exc) {
                log.error("AggregationServiceMBeanC.validateStaleInterval wrong value for key - " + key + " value - " + value);
                return false;
            }
        }
        return true;
    }

    private void initializeFIStalenessCheckIntervals() {
        if (fiCcyPairStalenessCheckInterval == null) {
            fiCcyPairStalenessCheckInterval = new ConcurrentHashMap<String, FXMap<String, Long, Long>>(8);
        } else {
            fiCcyPairStalenessCheckInterval.clear();
        }
        String propertyFILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVAL = getFILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVALProperty();
        Properties stalenessIntervals = getPropertiesWithPrefix(propertyFILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVAL);

        for (Map.Entry entry : stalenessIntervals.entrySet()) {
            String key = (String) entry.getKey();
            String value = (String) entry.getValue();

            if (key.length() == propertyFILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVAL.length()) continue;

            boolean isValidStaleInterval = validateStaleInterval(key, value);
            Long longValue = stalenessCheckInterval;

            try {
                if (isValidStaleInterval) {
                    longValue = Long.valueOf(value);
                } else {
                    continue;
                }
            } catch (Exception exc) {
                log.error("AggregationServiceMBeanC.initializeStalenessCheckIntervals wrong value for key - " + key + " value - " + value);
            }

            String attributes = key.substring(propertyFILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVAL.length() + 1);
            String keys[] = attributes.split("\\.");
            int size = keys.length;

            if (size >= 1) {
                String fiName = keys[0];
                FXMap<String, Long, Long> fiMap = fiCcyPairStalenessCheckInterval.get(fiName);
                if (fiMap == null) {
                    fiMap = new FXHashMap<String, Long, Long>(10);
                    fiCcyPairStalenessCheckInterval.put(fiName, fiMap);
                }

                Long fiDefaultValue = fiMap.getDefaultValue();
                fiDefaultValue = size == 1 ? longValue : (fiDefaultValue == null ? stalenessCheckInterval : fiDefaultValue);
                fiMap.setDefaultValue(fiDefaultValue);

                if (size >= 2) {
                    String cpName = keys[1];
                    fiMap.put(cpName, longValue);
                }
            }
        }
    }

    private void initializeFITermAggregationRoundingFactor() {
        if (fiTermAggregationRoundingFactor == null) {
            fiTermAggregationRoundingFactor = new ConcurrentHashMap<String, FXMap<String, Integer, Integer>>(8);
        } else {
            fiTermAggregationRoundingFactor.clear();
        }

        if (ccyPairTermAggregationRoundingFactor == null) {
            ccyPairTermAggregationRoundingFactor = new ConcurrentHashMap<String, Integer>(8);
        } else {
            ccyPairTermAggregationRoundingFactor.clear();
        }

        String propertyAGGREGATION_TERM_ROUNDING_FACTOR = getAGGREGATION_TERM_ROUNDING_FACTORProperty();
        Properties roundingFactors = getPropertiesWithPrefix(propertyAGGREGATION_TERM_ROUNDING_FACTOR);

        for (Map.Entry entry : roundingFactors.entrySet()) {
            String key = (String) entry.getKey();
            String value = (String) entry.getValue();

            if (key.length() == propertyAGGREGATION_TERM_ROUNDING_FACTOR.length()) continue;

            Integer intValue = null;
            try {
                intValue = Integer.valueOf(value);
            } catch (Exception exc) {
                log.error("AggregationServiceMBeanC.initializeFITermAggregationRoundingFactor wrong value for key - " + key + " value - " + value);
            }

            String attributes = key.substring(propertyAGGREGATION_TERM_ROUNDING_FACTOR.length() + 1);
            String keys[] = attributes.split("\\.");

            int size = keys.length;
            if (size > 2) continue;

            if (size == 1) {
                String fiName = keys[0];
                if (fiName.indexOf("/") != -1) { // only ccyPair case
                    ccyPairTermAggregationRoundingFactor.put(fiName, intValue);
                } else { // only fi case
                    FXMap<String, Integer, Integer> fiMap = fiTermAggregationRoundingFactor.get(fiName);
                    if (fiMap == null) {
                        fiMap = new FXHashMap<String, Integer, Integer>(10);
                        fiTermAggregationRoundingFactor.put(fiName, fiMap);
                    }
                    fiMap.setDefaultValue(intValue);
                }
            } else if (size == 2) {
                String fiName = keys[0];
                String cpName = keys[1];
                FXMap<String, Integer, Integer> fiMap = fiTermAggregationRoundingFactor.get(fiName);
                if (fiMap == null) {
                    fiMap = new FXHashMap<String, Integer, Integer>(10);
                    fiTermAggregationRoundingFactor.put(fiName, fiMap);
                }
                fiMap.put(cpName, intValue);
            }
        }
    }

    private void initializeReferenceProviders() {
        if (fiReferenceProviders == null) {
            fiReferenceProviders = new ConcurrentHashMap<String, Set<String>>(8);
        } else {
            fiReferenceProviders.clear();
        }

        String propertyOFF_MARKET_REFERENCE_PROVIDERS = getOFF_MARKET_REFERENCE_PROVIDERSProperty();
        Properties refProviders = getPropertiesWithPrefix(propertyOFF_MARKET_REFERENCE_PROVIDERS);

        for (Map.Entry entry : refProviders.entrySet()) {
            String key = (String) entry.getKey();
            String value = (String) entry.getValue();

            if (key.length() == propertyOFF_MARKET_REFERENCE_PROVIDERS.length()) {
                if (referenceProviders == null) {
                    referenceProviders = new HashSet<String>(8);
                } else {
                    referenceProviders.clear();
                }

                referenceProviders.addAll(Arrays.asList(value.split(",")));
            } else {
                String fiName = key.substring(propertyOFF_MARKET_REFERENCE_PROVIDERS.length() + 1);

                if (!fiName.trim().equals("")) {
                    fiReferenceProviders.put(fiName, new HashSet<String>(Arrays.asList(value.split(","))));
                }
            }
        }
    }


    /**
     * Initialises the partial provider list.
     *
     * @return partial providers list
     */
    private void initializePartialFillProviders() {
        Set<Organization> providers = new HashSet<Organization>(8);
        try {
            String partialFillProvidersStr = getStringProperty(IDC_IS_PARTIAL_FILL_PROVIDERS, "");
            StringTokenizer tokenizer = new StringTokenizer(partialFillProvidersStr, ",");
            while (tokenizer.hasMoreTokens()) {
                String providerName = tokenizer.nextToken();
                Organization providerOrg = ReferenceDataCacheC.getInstance().getOrganization(providerName);

                if (null != providerOrg) {
                    providers.add(providerOrg);
                } else {
                    log.info("AggregationServiceMBeanC.initializePartialFillProviders Invalid partial fill provider name " + providerName);
                }
            }
            //log.warn("AggregationServiceMBeanC.initializePartialFillProviders: Partial fill providers list loaded:" + partialFillProvidersStr);
        } catch (Exception e) {
            log.error("AggregationServiceMBeanC.initializePartialFillProviders: Error -", e);
        }
        partialFillProviders = providers;
    }



    private void initializeProviderQuoteTiers() {
        String propertyQUOTE_TIER = getQUOTE_TIERProperty();
        providerQuoteTier = getIntProperty(propertyQUOTE_TIER, getDefaultQUOTE_TIER());

        if (providerQuoteTiers == null) {
            providerQuoteTiers = new ConcurrentHashMap<String, FXMap<String, FXMap<String, Integer, Integer>, Integer>>(25);
        } else {
            providerQuoteTiers.clear();
        }

        Properties lpQuoteTiers = getPropertiesWithPrefix(propertyQUOTE_TIER);

        for (Map.Entry entry : lpQuoteTiers.entrySet()) {
            String key = (String) entry.getKey();
            String value = (String) entry.getValue();

            Integer intValue = providerQuoteTier;
            try {
                intValue = Integer.valueOf(value);
            } catch (Exception exc) {
                log.error("AggregationServiceMBeanC.initializeProviderQuoteTiers wrong value for key - " + key + " value - " + value);
            }

            if (key.length() == propertyQUOTE_TIER.length()) continue;

            String attributes = key.substring(propertyQUOTE_TIER.length() + 1);
            String keys[] = attributes.split("\\.");
            int size = keys.length;

            if (size >= 1) {
                String fiName = keys[0];
                FXMap<String, FXMap<String, Integer, Integer>, Integer> fiMap = providerQuoteTiers.get(fiName);
                if (fiMap == null) {
                    fiMap = new FXHashMap<String, FXMap<String, Integer, Integer>, Integer>(5);
                    providerQuoteTiers.put(fiName, fiMap);
                }

                Integer fiDefaultValue = fiMap.getDefaultValue();
                fiDefaultValue = size == 1 ? intValue : (fiDefaultValue == null ? providerQuoteTier : fiDefaultValue);
                fiMap.setDefaultValue(fiDefaultValue);

                if (size >= 2) {
                    String lpName = keys[1];
                    FXMap<String, Integer, Integer> lpMap = fiMap.get(lpName);
                    if (lpMap == null) {
                        lpMap = new FXHashMap<String, Integer, Integer>(5);
                        fiMap.put(lpName, lpMap);
                    }

                    Integer lpDefaultValue = lpMap.getDefaultValue();
                    lpDefaultValue = size == 2 ? intValue : (lpDefaultValue == null ? providerQuoteTier : lpDefaultValue);
                    lpMap.setDefaultValue(lpDefaultValue);

                    if (size >= 3) {
                        String cpName = keys[2];
                        lpMap.put(cpName, intValue);
                    }
                }
            }
        }
    }

    public int getProviderQuoteTier() {
        return providerQuoteTier;
    }

    public FXMap<String, FXMap<String, Integer, Integer>, Integer> getProviderQuoteTiers(String fiOrg) {
        return providerQuoteTiers.get(fiOrg);
    }

    public FXMap<String, Integer, Integer> getProviderQuoteTiers(String fiOrg, String lpOrg) {
        FXMap<String, FXMap<String, Integer, Integer>, Integer> fiMap = providerQuoteTiers.get(fiOrg);
        if (fiMap != null) {
            return fiMap.get(lpOrg);
        }
        return null;
    }

    public Integer getProviderQuoteTiers(String fiOrg, String lpOrg, String ccyPair) {
        FXMap<String, FXMap<String, Integer, Integer>, Integer> fiMap = providerQuoteTiers.get(fiOrg);
        if (fiMap != null) {
            FXMap<String, Integer, Integer> lpMap = fiMap.get(lpOrg);

            if (lpMap != null) {
                Integer tier = lpMap.get(ccyPair);
                return tier == null ? lpMap.getDefaultValue() : tier;
            } else {
                return fiMap.getDefaultValue();
            }
        } else {
            return getProviderQuoteTier();
        }
    }

    public Set<String> getReferenceProviders() {
        return referenceProviders;
    }

    public Set<String> getReferenceProviders(String fiOrg) {
        Set<String> refProviders = fiReferenceProviders.get(fiOrg);
        return refProviders == null ? getReferenceProviders() : refProviders;
    }

    public Integer getQuotePublicationInterval() {
        return quotePublicationInterval;
    }

    public Integer getQuotePublicationInterval(String fiOrg) {
        Integer interval = fiQuotePublicationInterval.get(fiOrg);
        return interval == null ? getQuotePublicationInterval() : interval;
    }

    public Integer getBestBidOfferPublicationInterval() {
        return bestBidOfferPublicationInterval;
    }

    public Integer getBestBidOfferPublicationInterval(String fiOrg) {
        Integer bestBidOfferInterval = fiBestBidOfferPublicationInterval.get(fiOrg);
        return bestBidOfferInterval == null ? getBestBidOfferPublicationInterval() : bestBidOfferInterval;
    }

    @Override
    public Integer getTermAggregationRoundingFactor() {
        return termAggregationRoundingFactor;
    }

    @Override
    public Integer getTermAggregationRoundingFactor(String fiOrg) {
        FXMap<String, Integer, Integer> interval = fiTermAggregationRoundingFactor.get(fiOrg);
        return interval == null ? getTermAggregationRoundingFactor() : interval.getDefaultValue();
    }

    public FXMap<String, Integer, Integer> getCcyPairTermAggregationRoundingFactor(String fiOrg) {
        return fiTermAggregationRoundingFactor.get(fiOrg);
    }

    public Integer getTermAggregationRoundingFactor(String fiOrg, String ccyPair) {
        FXMap<String, Integer, Integer> fiMap = fiTermAggregationRoundingFactor.get(fiOrg);
        if (fiMap == null) {
            return getTermAggregationRoundingFactorBasedOnCcyPair(ccyPair);
        } else {
            Integer factor = fiMap.get(ccyPair);
            if (factor == null) {
                factor = fiMap.getDefaultValue();
                if (factor == null) {
                    return getTermAggregationRoundingFactorBasedOnCcyPair(ccyPair);
                } else {
                    return factor;
                }
            } else {
                return factor;
            }
        }
    }

    private Integer getTermAggregationRoundingFactorBasedOnCcyPair(String ccyPair) {
        Integer factor = ccyPairTermAggregationRoundingFactor.get(ccyPair);
        return factor == null ? getTermAggregationRoundingFactor() : factor;
    }

    @Override
    public Boolean isTermAggregationDisplayAmtInBase() {
        return isTermAggregationDisplayAmtInBase;
    }

    @Override
    public Boolean isTermAggregationDisplayAmtInBase(String fiOrg) {
        Boolean value = fiTermAggregationDisplayAmtInBase.get(fiOrg);
        return value == null ? isTermAggregationDisplayAmtInBase() : value;
    }

    public Boolean isSamePriceBookPublishingEnabled() {
        return samePriceBookPublishing_Enabled;
    }

    public Boolean isSamePriceBookPublishingEnabled(String fiOrg) {
        Boolean value = fiSamePriceBookPublishingEnabled.get(fiOrg);
        return value == null ? isSamePriceBookPublishingEnabled() : value;
    }

    @Override
    public Boolean isProviderNameDisplayEnabled() {
        return isProviderNameDisplayEnabled;
    }

    @Override
    public Boolean isProviderNameDisplayEnabled(String fiOrg) {
        Boolean value = fiProviderNameDisplayEnabled.get(fiOrg);
        return value == null ? isProviderNameDisplayEnabled() : value;
    }

    @Override
    public String getDisplayNameForFMAProviders() {
        return displayNameForFMAProviders;
    }

    @Override
    public String getDisplayNameForFMAProviders(String fiOrg) {
        String value = fiDisplayNameForFMAProviders.get(fiOrg);
        return (value == null || "".equals(value))? getDisplayNameForFMAProviders() : value;
    }

    public Boolean isStalenessCheckEnabled() {
        return isStalenessCheckEnabled;
    }

    public Boolean isInvertedRateFilterCheckEnabled() {
        return isInvertedRateFilterCheckEnabled;
    }

    public Boolean isMultiProviderFilterEnabled() {
        return isMultiProviderFilterEnabled;
    }

    public Boolean isOffMarketCheckEnabled() {
        return isOffMarketCheckEnabled;
    }

    public Integer getOffMarketMinProviders() {
        return offMarketMinProviders;
    }

    public Integer getOffMarketMaxProviders() {
        return offMarketMaxProviders;
    }

    public Long getStalenessCheckInterval() {
        return stalenessCheckInterval;
    }

    public Double getOffMarketMaxInverseSpread() {
        return offMarketMaxInverseSpread;
    }

    public Boolean isStalenessCheckEnabled(String fiOrg) {
        Boolean value = fiStalenessCheckEnabled.get(fiOrg);
        return value == null ? isStalenessCheckEnabled() : value;
    }

    public Boolean isInvertedRateFilterCheckEnabled(String fiOrg) {
        Boolean value = fiInvertedRateFilterCheckEnabled.get(fiOrg);
        return value == null ? isInvertedRateFilterCheckEnabled() : value;
    }

    public Boolean isMultiProviderFilterEnabled(String fiOrg) {
        Boolean value = fiMultiProviderFilterEnabled.get(fiOrg);
        return value == null ? isMultiProviderFilterEnabled() : value;
    }

    public Boolean isOffMarketCheckEnabled(String fiOrg) {
        Boolean value = fiOffMarketCheckEnabled.get(fiOrg);
        return value == null ? isOffMarketCheckEnabled() : value;
    }

    public Integer getOffMarketMinProviders(String fiOrg) {
        Integer value = fiOffMarketMinProviders.get(fiOrg);
        return value == null ? getOffMarketMinProviders() : value;
    }

    public Integer getOffMarketMaxProviders(String fiOrg) {
        Integer value = fiOffMarketMaxProviders.get(fiOrg);
        return value == null ? getOffMarketMaxProviders() : value;
    }

    public Long getStalenessCheckInterval(String fiOrg) {
        FXMap<String, Long, Long> fiMap = fiCcyPairStalenessCheckInterval.get(fiOrg);
        return fiMap == null ? getStalenessCheckInterval() : fiMap.getDefaultValue();
    }

    public Double getOffMarketMaxInverseSpread(String fiOrg) {
        FXMap<String, Double, Double> fiMap = fiCcyPairOffMarketMaxInverseSpread.get(fiOrg);
        return fiMap == null ? getOffMarketMaxInverseSpread() : fiMap.getDefaultValue();
    }

    @Override
    public FXMap<String, Long, Long> getCcyPairStalenessCheckIntervals(String fiOrg) {
        return fiCcyPairStalenessCheckInterval.get(fiOrg);
    }

    @Override
    public FXMap<String, Double, Double> getCcyPairOffMarketMaxInverseSpreads(String fiOrg) {
        return fiCcyPairOffMarketMaxInverseSpread.get(fiOrg);
    }

    public Long getStalenessCheckInterval(String fiOrg, String ccyPair) {
        FXMap<String, Long, Long> fiMap = fiCcyPairStalenessCheckInterval.get(fiOrg);
        if (fiMap != null) {
            Long interval = fiMap.get(ccyPair);
            return interval == null ? fiMap.getDefaultValue() : interval;
        }
        return getStalenessCheckInterval();
    }

    public Double getOffMarketMaxInverseSpread(String fiOrg, String ccyPair) {
        FXMap<String, Double, Double> fiMap = fiCcyPairOffMarketMaxInverseSpread.get(fiOrg);
        if (fiMap != null) {
            Double spread = fiMap.get(ccyPair);
            return spread == null ? fiMap.getDefaultValue() : spread;
        }
        return getOffMarketMaxInverseSpread();
    }

    public int getAggregatorThreadSweepInterval() {
        return aggregatorThreadSweepInterval;
    }

    @Override
    public boolean isAggLogCompressionEnabled() {
        return isAggLogCompressionEnabled;
    }

    @Override
    public boolean isAggLogBinaryEnabled(String fiOrg) {
        boolean isAggLogBinaryEnabledForFI = false;
        if (fiOrg != null) {
            fiOrg = fiOrg.trim();
            Boolean binAggEnabledValueFromMap = fiVsBinaryLogEnabledLog.get(fiOrg);
            if (binAggEnabledValueFromMap != null) {
                isAggLogBinaryEnabledForFI = binAggEnabledValueFromMap;
            } else {
                log.info("BINARY_LOGGING_ENABLED_CHECK_FOR_ORG=" + fiOrg);
                String key = AGGREGATION_LOG_BINARY_ENABLED + "." + fiOrg;
                isAggLogBinaryEnabledForFI = getBooleanProperty(key, this.isAggLogBinaryEnabled);
                fiVsBinaryLogEnabledLog.put(fiOrg, isAggLogBinaryEnabledForFI);
            }
        }
        log.info("IS_BINARY_LOGGING_ENABLED_FOR_ORG=" + fiOrg + " IS " + isAggLogBinaryEnabledForFI);
        return isAggLogBinaryEnabledForFI;
    }


    @Override
    public int getAggLogBinaryDisruptorBufferSize() {
        return aggBinaryLogBufferSize;
    }

    @Override
    public int getAggLogRingBufferThresholdPercent() {
        return aggLogRingBufThreshold;
    }

    @Override
    public int getAggLogByteBufferSize() {
        return aggLogByteBufferSize;
    }

    public boolean isAggLogBinaryEnabled() {
        return isAggLogBinaryEnabled;
    }

    public Double getInvertedRateTolerance() {
        return invertedRateTolerance;
    }

    public Double getInvertedRateTolerance(String fiOrg) {
        FXMap<String, Double, Double> fiMap = fiCcyPairInvertedRateTolerance.get(fiOrg);
        return fiMap == null ? getInvertedRateTolerance() : fiMap.getDefaultValue();
    }

    public Double getInvertedRateTolerance(String fiOrg, String ccyPair) {
        FXMap<String, Double, Double> fiMap = fiCcyPairInvertedRateTolerance.get(fiOrg);
        if (fiMap != null) {
            Double tolerance = fiMap.get(ccyPair);
            return tolerance == null ? fiMap.getDefaultValue() : tolerance;
        }
        return getInvertedRateTolerance();
    }

    public FXMap<String, Double, Double> getCcyPairInvertedRateTolerances(String fiOrg) {
        return fiCcyPairInvertedRateTolerance.get(fiOrg);
    }

    public Boolean isCachedQuoteOnSubscription() {
        return isCachedQuoteOnSubscription;
    }

    public Boolean isCachedQuoteOnSubscription(String fiOrg) {
        Boolean value = fiCachedQuoteOnSubscription.get(fiOrg);
        return value == null ? isCachedQuoteOnSubscription() : value;
    }

    @Override
    public Boolean isAggExecRuleEnabled() {
        return isAggExecRuleEnabled;
    }

    @Override
    public Boolean isAggExecRuleEnabled(String fiOrg) {
        Boolean value = fiAggExecRuleEnabled.get(fiOrg);
        return value == null ? isAggExecRuleEnabled() : value;
    }

    @Override
    public int getAggregatorsPerThread() {
        return aggregatorsPerThread;
    }

    @Override
    public int getAggregatorThreadPoolSize() {
        return aggregatorThreadPoolSize;
    }

    public String getAggregationUserShortName() {
        return aggregationUserShortName;
    }

    @Override
    public long[] getPublicationTimeBuckets() {
        return publicationTimeBuckets;
    }

    @Override
    public long[] getAggregationTimeBuckets() {
        return aggregationTimeBuckets;
    }

    @Override
    public long[] getSendTimeBuckets() {
        return sendTimeBuckets;
    }

    @Override
    public long[] getLogTimeBuckets() {
        return logTimeBuckets;
    }

    /*public void notifyAggregationProcessors() {
        ConcurrentMap<Organization, AggregationProcessor> aggregationProcessors = AggregationProcessorManager
                .getInstance().getAggregationProcessorCache();

        for (Map.Entry<Organization, AggregationProcessor> entry : aggregationProcessors.entrySet()) {
            AggregationProcessor processor = entry.getValue();
            updateAggregationProcessor(processor);
        }
    }

    public void updateAggregationProcessor(AggregationProcessor processor) {
        AggregationProcessorConfig config = processor.getConfig();
        updateAggregationProcessorConfig(config);
    }*/

    public void updateAggregationProcessorConfig(AggregationProcessorConfig config) {
        Organization fiOrg = config.getOrganization();
        if (isAggExecRuleEnabled(fiOrg.getShortName())) {
            TakerOrganizationFunction orgFunction = fiOrg.getTakerOrganizationFunction();
            config.setQuotePublicationInterval(orgFunction.getAggregationParameters().getFullStreamUpdateInterval());
            config.setBestBifOfferPublicationInterval(orgFunction.getAggregationParameters().getBestBidOfferStreamUpdateInterval());
        } else {
            updateQuotePublicationInterval(config);
            updateBestBifOfferPublicationInterval(config);
        }
        updateSamePriceBookPublishingEnabled(config);
        updateProviderNameDisplayEnabled(config);
        updateReferenceProviders(config);
        updatePartialProviders(config);
        updateProviderQuoteTier(config);
        updateTermAggregationAmtInBase(config);
        updateTermAggregationRoundingFactor(config);
        updateCachedQuoteOnSubscription(config);
        updateDisplayNameForFMAProviders(config);
    }

    private void updateCachedQuoteOnSubscription(AggregationProcessorConfig config) {
        String fiOrg = config.getOrganization().getShortName();
        config.setCachedQuoteOnSubscription(isCachedQuoteOnSubscription(fiOrg));
    }

    private void updateReferenceProviders(AggregationProcessorConfig config) {
        String fiOrg = config.getOrganization().getShortName();
        Set<String> _refProviders = getReferenceProviders(fiOrg);
        Set<Organization> _referenceProviders = getReferenceProviders(_refProviders);
        config.setReferenceProviders(_referenceProviders);
    }

    private void updatePartialProviders(AggregationProcessorConfig config) {
         config.setPartialProviders(partialFillProviders);
    }

    private void updateQuotePublicationInterval(AggregationProcessorConfig config) {
        String fiOrg = config.getOrganization().getShortName();
        config.setQuotePublicationInterval(getQuotePublicationInterval(fiOrg));
    }

    private void updateBestBifOfferPublicationInterval(AggregationProcessorConfig config) {
        String fiOrg = config.getOrganization().getShortName();
        config.setBestBifOfferPublicationInterval(getBestBidOfferPublicationInterval(fiOrg));
    }

    private void updateTermAggregationRoundingFactor(AggregationProcessorConfig config) {
        String fiOrg = config.getOrganization().getShortName();
        config.setTermAggregationRoundingFactor(getTermAggregationRoundingFactor(fiOrg));
    }

    private void updateTermAggregationAmtInBase(AggregationProcessorConfig config) {
        String fiOrg = config.getOrganization().getShortName();
        config.setTermAggregationDisplayAmtInBase(isTermAggregationDisplayAmtInBase(fiOrg));
    }

    private void updateSamePriceBookPublishingEnabled(AggregationProcessorConfig config) {
        String fiOrg = config.getOrganization().getShortName();
        config.setSamePriceBokPublishingEnabled(isSamePriceBookPublishingEnabled(fiOrg));
    }

    private void updateProviderNameDisplayEnabled(AggregationProcessorConfig config) {
        String fiOrg = config.getOrganization().getShortName();
        config.setProviderNameDisplayEnabled(isProviderNameDisplayEnabled(fiOrg));
    }

    private void updateDisplayNameForFMAProviders(AggregationProcessorConfig config) {
        String fiOrg = config.getOrganization().getShortName();
        config.setDisplayNameForFMAProviders(getDisplayNameForFMAProviders(fiOrg));
    }

    private void updateProviderQuoteTier(AggregationProcessorConfig config) {
        String fiOrg = config.getOrganization().getShortName();

        FXMap<String, FXMap<String, Integer, Integer>, Integer> fiProviderQuoteTiers = getProviderQuoteTiers(fiOrg);
        Integer fiDefaultProviderQuoteTier = fiProviderQuoteTiers == null ? getProviderQuoteTier() : fiProviderQuoteTiers.getDefaultValue();
        config.setDefaultProviderQuoteTier(fiDefaultProviderQuoteTier);
        config.setProviderQuoteTiers(fiProviderQuoteTiers);
    }

    public void updateMultiProviderFilterConfig(MultiProviderFilterConfig filterConfig) {
        String fiOrg = filterConfig.getOrganization().getShortName();

        filterConfig.setMultiProviderFilterEnabled(isMultiProviderFilterEnabled(fiOrg));
        filterConfig.setInvertedRateFilterCheckEnabled(isInvertedRateFilterCheckEnabled(fiOrg));
        filterConfig.setOffMarketCheckEnabled(isOffMarketCheckEnabled(fiOrg));
        filterConfig.setStalenessCheckEnabled(isStalenessCheckEnabled(fiOrg));
        filterConfig.setOffMarketMaxInverseSpread(getOffMarketMaxInverseSpread(fiOrg));
        filterConfig.setOffMarketMaxProviders(getOffMarketMaxProviders(fiOrg));
        filterConfig.setOffMarketMinProviders(getOffMarketMinProviders(fiOrg));
        filterConfig.setStalenessCheckInterval(getStalenessCheckInterval(fiOrg));
        filterConfig.setInvertedRateTolerance(getInvertedRateTolerance(fiOrg));

        FXMap<String, Long, Long> ccyPairIntervals = getCcyPairStalenessCheckIntervals(fiOrg);
        filterConfig.clearCcyPairStalenessCheckIntervalCache();
        if (ccyPairIntervals != null && ccyPairIntervals.size() >= 0) {
            for (Map.Entry<String, Long> entry : ccyPairIntervals.entrySet()) {
                filterConfig.addCcyPairStalenessCheckInterval(entry.getKey(), entry.getValue());
            }
        }

        FXMap<String, Double, Double> offMarketInverseSpreads = getCcyPairOffMarketMaxInverseSpreads(fiOrg);
        filterConfig.clearCcyPairMaxInverseSpreadCache();
        if (offMarketInverseSpreads != null && offMarketInverseSpreads.size() >= 0) {
            for (Map.Entry<String, Double> entry : offMarketInverseSpreads.entrySet()) {
                filterConfig.addCcyPairMaxInverseSpread(entry.getKey(), entry.getValue());
            }
        }

        FXMap<String, Double, Double> ccyPairInvertedRateTolerances = getCcyPairInvertedRateTolerances(fiOrg);
        filterConfig.clearCcyPairInvertedRateToleranceCache();
        if (ccyPairInvertedRateTolerances != null && ccyPairInvertedRateTolerances.size() >= 0) {
            for (Map.Entry<String, Double> entry : ccyPairInvertedRateTolerances.entrySet()) {
                filterConfig.addCcyPairInvertedRateTolerance(entry.getKey(), entry.getValue());
            }
        }
        filterConfig.broadcastUpdates();
    }

    public int getRequestExecutorPoolSize() {
        return getIntProperty(getREQUEST_EXECUTOR_POOL_SIZEProperty(), getDefaultREQUEST_EXECUTOR_POOL_SIZE());
    }

    private Set<Organization> getReferenceProviders(Set<String> providers) {
        Set<Organization> refProviders = new HashSet<Organization>(providers.size(), 1);
        for (String orgName : providers) {
            Organization org = AggregationServiceUtil.getOrganization(orgName);
            if (org != null) {
                refProviders.add(org);
            }
        }
        return refProviders;
    }

    public void setProperty(String key, String aValue, int scope, String oldValue) {
        super.setProperty(key, aValue, scope, oldValue);
        // todo: Broadcast the events for the change.
        String propertyQUOTE_TIER = getQUOTE_TIERProperty();
        String propertyQUOTE_PUBLICATION_INTERVAL = getQUOTE_PUBLICATION_INTERVALProperty();
        String propertyBEST_BID_OFFER_PUBLICATION_INTERVAL = getBEST_BID_OFFER_PUBLICATION_INTERVALProperty();
        String propertyAGGREGATION_TERM_ROUNDING_FACTOR = getAGGREGATION_TERM_ROUNDING_FACTORProperty();
        String propertyAGGREGATION_TERM_DISPLAY_AMOUNT_IN_BASE = getAGGREGATION_TERM_DISPLAY_AMOUNT_IN_BASEProperty();
        String propertySAME_PRICE_BOOK_PUBLISHING_ENABLED = getSAME_PRICE_BOOK_PUBLISHING_ENABLEDProperty();
        String propertyAGGREGATION_SHOW_PROVIDER_NAME = getAGGREGATION_SHOW_PROVIDER_NAMEProperty();
        String propertyOFF_MARKET_REFERENCE_PROVIDERS = getOFF_MARKET_REFERENCE_PROVIDERSProperty();
        String propertyFILTER_MULTI_PROVIDER_FILTER_KEY = getFILTER_MULTI_PROVIDER_FILTER_KEYProperty();
        String propertyAGGREGATION_DISPLAY_NAME_FOR_FMA_PROVIDERS = getAGGREGATION_DISPLAY_NAME_FOR_FMA_PROVIDERSProperty();

        if (isAggregationServiceProperty(key)) {
            if (key.startsWith(propertyQUOTE_TIER)) {
                initializeProviderQuoteTiers();
                String fiName = getOrgName(key, propertyQUOTE_TIER);
                updateProviderQuoteTier(fiName);
            } else if (key.startsWith(propertyQUOTE_PUBLICATION_INTERVAL)) {
                String fiName = getOrgName(key, propertyQUOTE_PUBLICATION_INTERVAL);
                if (!isAggExecRuleEnabled(fiName)) {
                    initializeQuotePublicationInterval();
                    updateQuotePublicationInterval(fiName);
                }
            } else if (key.startsWith(propertyBEST_BID_OFFER_PUBLICATION_INTERVAL)) {
                String fiName = getOrgName(key, propertyBEST_BID_OFFER_PUBLICATION_INTERVAL);
                if (!isAggExecRuleEnabled(fiName)) {
                    initializeBestBidOfferPublicationInterval();
                    updateBestBifOfferPublicationInterval(fiName);
                }
            } else if (key.startsWith(propertyAGGREGATION_TERM_ROUNDING_FACTOR)) {
                String fiName = getOrgName(key, propertyAGGREGATION_TERM_ROUNDING_FACTOR);
                initializeTermAggregationRoundingFactors();
                if (fiName.indexOf("/") == -1) updateTermAggregationRoundingFactors(fiName);
            } else if (key.startsWith(propertyAGGREGATION_TERM_DISPLAY_AMOUNT_IN_BASE)) {
                String fiName = getOrgName(key, propertyAGGREGATION_TERM_DISPLAY_AMOUNT_IN_BASE);
                initializeTermAggregationAmtInBase();
                updateTermAggregationAmtInBase(fiName);
            } else if (key.startsWith(propertySAME_PRICE_BOOK_PUBLISHING_ENABLED)) {
                initializeSamePriceBookPublishingEnabled();
                String fiName = getOrgName(key, propertySAME_PRICE_BOOK_PUBLISHING_ENABLED);
                updateSamePriceBookPublishingEnabled(fiName);
            } else if (key.startsWith(propertyAGGREGATION_SHOW_PROVIDER_NAME)) {
                initializeProviderNameDisplayEnabled();
                String fiName = getOrgName(key, propertyAGGREGATION_SHOW_PROVIDER_NAME);
                updateProviderNameDisplayEnabled(fiName);
            } else if (key.startsWith(propertyAGGREGATION_DISPLAY_NAME_FOR_FMA_PROVIDERS)) {
                initializeDisplayNameForFMAProviders();
                String fiName = getOrgName(key, propertyAGGREGATION_DISPLAY_NAME_FOR_FMA_PROVIDERS);
                updateDisplayNameForFMAProviders(fiName);
            } else if (key.startsWith(propertyOFF_MARKET_REFERENCE_PROVIDERS)) {
                initializeReferenceProviders();
                String fiName = getOrgName(key, propertyOFF_MARKET_REFERENCE_PROVIDERS);
                updateReferenceProviders(fiName);
            } else if (key.startsWith(propertyFILTER_MULTI_PROVIDER_FILTER_KEY)) {
                setMultiProviderFilterProperty(key, aValue, scope, oldValue);
            } else if (key.startsWith(getAGGREGATOR_THREAD_SWEEP_INTERVALProperty())) {
                aggregatorThreadSweepInterval = aValue != null ? Integer.valueOf(aValue) : aggregatorThreadSweepInterval;
            } else if (key.startsWith(getAGGREGATORS_PER_AGGREGATOR_THREADProperty())) {
                aggregatorsPerThread = aValue != null ? Integer.valueOf(aValue) : aggregatorsPerThread;
            } else if (key.startsWith(getAGGREGATOR_THREAD_POOL_SIZEProperty())) {
                aggregatorThreadPoolSize = aValue != null ? Integer.valueOf(aValue) : aggregatorThreadPoolSize;
            } else if (key.startsWith(getAGGREGATOR_USER_NAMEProperty())) {
                aggregationUserShortName = aValue != null ? aValue : aggregationUserShortName;
            } else if (key.startsWith(getAGGREGATOR_METRICS_PUB_TIME_BUCKETSProperty())) {
                publicationTimeBuckets = parseBucketSizes(aValue, publicationTimeBuckets);
            } else if (key.startsWith(getAGGREGATOR_METRICS_AGG_TIME_BUCKETSProperty())) {
                aggregationTimeBuckets = parseBucketSizes(aValue, aggregationTimeBuckets);
            } else if (key.startsWith(getAGGREGATOR_METRICS_SEND_TIME_BUCKETSProperty())) {
                sendTimeBuckets = parseBucketSizes(aValue, sendTimeBuckets);
            } else if (key.startsWith(getAGGREGATOR_METRICS_LOG_TIME_BUCKETSProperty())) {
                logTimeBuckets = parseBucketSizes(aValue, logTimeBuckets);
            } else if (key.startsWith(getAGGREGATION_LOG_COMPRESSION_ENABLEDProperty())) {
                isAggLogCompressionEnabled = aValue != null ? Boolean.valueOf(aValue) : isAggLogCompressionEnabled;
                updateAggregatorsLogCompressionSupport();
            } else if (key.startsWith(getAGGREGATION_LOG_BINARY_ENABLEDProperty())) {
                isAggLogBinaryEnabled = aValue != null ? Boolean.valueOf(aValue) : isAggLogBinaryEnabled;
            } else if (key.startsWith(getAGGREGATION_CACHED_QUOTE_ON_SUBSCRIPTIONProperty())) {
                String fiName = getOrgName(key, getAGGREGATION_CACHED_QUOTE_ON_SUBSCRIPTIONProperty());
                initializeCachedQuoteSubscription();
                updateCachedQuoteOnSubscription(fiName);
            } else if (key.startsWith(getAGGREGATION_EXECUTION_RULE_ENABLEDProperty())) {
                initializeAggExecRuleEnabled();
            }else if(key.startsWith( AGGREGATION_SUBSCRIPTION_TO_CLOB_ENABLED )){
                initializeClobSubscriptionEnabled();
            }else if(key.startsWith( AGGREGATION_SUBSCRIPTION_TO_RISKNET_ENABLED )){
                initializeRiskNetSubscriptionEnabled();
            }else if(key.startsWith( AGGREGATION_SUBSCRIPTION_CLIENT_TAG_MAPPING )){
                initializeClientTagMappings();
            }else if(key.equals(IDC_IS_PARTIAL_FILL_PROVIDERS)){
            	initializePartialFillProviders();
            	updatePartialProviders();
            }
            else if(key.startsWith(AGGREGATION_FA_ENABLED)){
                initializeFAAggregationProperties();
            }
        }
    }

    public void removeProperty(String key, int scope) {
        super.removeProperty(key, scope);
        if (isAggregationServiceProperty(key)) {
            if (key.startsWith(getFILTER_MULTI_PROVIDER_FILTER_KEYProperty())) {
                setMultiProviderFilterProperty(key, null, scope, null);
            } else if (key.startsWith(getAGGREGATION_TERM_ROUNDING_FACTORProperty())) {
                initializeTermAggregationRoundingFactors();
            } else if(key.startsWith(getAGGREGATION_DISPLAY_NAME_FOR_FMA_PROVIDERSProperty())) {
                initializeDisplayNameForFMAProviders();
                String fiName = getOrgName(key, getAGGREGATION_DISPLAY_NAME_FOR_FMA_PROVIDERSProperty());
                updateDisplayNameForFMAProviders(fiName);
            }
            else if(key.startsWith(AGGREGATION_FA_ENABLED)){
                initializeFAAggregationProperties();
            }
        }
    }

    private void updateAggregatorsLogCompressionSupport() {
        Set<Organization> orgEntries = getAggregationProcessorCache().keySet();

        for (Organization org : orgEntries) {
            String aggAppenderKey = "aggregation." + org.getShortName();

            // this will get all gzip appenderrs from it's container will update the mode and restart appender
            for (GZipFileAppender appender : GZipFileAppenderContainer.getList()) {
                if (appender.getName().contains(aggAppenderKey)) {
                    appender.setGzip(isAggLogCompressionEnabled);
                }
            }
        }
    }

    private void setMultiProviderFilterProperty(String key, String aValue, int scope, String oldValue) {
        initializeMultiProviderRateFilterDetails();
        String filterKey = null;

        if (key.startsWith(getFILTER_MULTI_PROVIDER_FILTER_ENABLEDProperty())) {
            filterKey = getFILTER_MULTI_PROVIDER_FILTER_ENABLEDProperty();
        } else if (key.startsWith(getFILTER_MULTI_PROVIDER_STALENESS_CHECK_ENABLEDProperty())) {
            filterKey = getFILTER_MULTI_PROVIDER_STALENESS_CHECK_ENABLEDProperty();
        } else if (key.startsWith(getFILTER_MULTI_PROVIDER_OFFMARKET_CHECK_ENABLEDProperty())) {
            filterKey = getFILTER_MULTI_PROVIDER_OFFMARKET_CHECK_ENABLEDProperty();
        } else if (key.startsWith(getFILTER_MULTI_PROVIDER_INVERTED_RATE_CHECK_ENABLEDProperty())) {
            filterKey = getFILTER_MULTI_PROVIDER_INVERTED_RATE_CHECK_ENABLEDProperty();
        } else if (key.startsWith(getFILTER_MULTI_PROVIDER_OFFMARKET_MAX_PROVIDERSProperty())) {
            filterKey = getFILTER_MULTI_PROVIDER_OFFMARKET_MAX_PROVIDERSProperty();
        } else if (key.startsWith(getFILTER_MULTI_PROVIDER_OFFMARKET_MIN_PROVIDERSProperty())) {
            filterKey = getFILTER_MULTI_PROVIDER_OFFMARKET_MIN_PROVIDERSProperty();
        } else if (key.startsWith(getFILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVALProperty())) {
            filterKey = getFILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVALProperty();
        } else if (key.startsWith(getFILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREADProperty())) {
            filterKey = getFILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREADProperty();
        } else if (key.startsWith(getFILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCEProperty())) {
            filterKey = getFILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCEProperty();
        }

        if (filterKey != null) {
            String fiName = getOrgName(key, filterKey);
            updateMultiProviderFilterConfig(fiName);
        }

    }

    private String getOrgName(String key, String subKey) {
        String str = key.substring(subKey.length());
        str = str.indexOf(".") == 0 ? str.substring(1) : str;
        int fiIndex = str.indexOf(".");
        return fiIndex == -1 ? str : str.substring(0, fiIndex);
    }
    
    private void updatePartialProviders() {
    	Set<Map.Entry<Organization, AggregationProcessor>> entries = getAggregationProcessorCache().entrySet();

        for (Map.Entry<Organization, AggregationProcessor> entry : entries) {
        	updatePartialProviders(entry.getValue().getConfig());
        }
    }

    private void updateMultiProviderFilterConfig(String fiName) {
        Set<Map.Entry<Organization, AggregationProcessor>> entries = getAggregationProcessorCache().entrySet();

        for (Map.Entry<Organization, AggregationProcessor> entry : entries) {
            if (fiName != null && !fiName.trim().equals("")) {
                if (entry.getKey().getShortName().equals(fiName)) {
                    updateMultiProviderFilterConfig(entry.getValue().getConfig().getMultiProviderFilterConfig());
                }
            } else {
                updateMultiProviderFilterConfig(entry.getValue().getConfig().getMultiProviderFilterConfig());
            }
        }
    }

    private void updateQuotePublicationInterval(String fiName) {
        Set<Map.Entry<Organization, AggregationProcessor>> entries = getAggregationProcessorCache().entrySet();

        for (Map.Entry<Organization, AggregationProcessor> entry : entries) {
            if (fiName != null && !fiName.trim().equals("")) {
                if (entry.getKey().getShortName().equals(fiName)) {
                    updateQuotePublicationInterval(entry.getValue().getConfig());
                }
            } else {
                updateQuotePublicationInterval(entry.getValue().getConfig());
            }
        }
    }

    private void updateBestBifOfferPublicationInterval(String fiName) {
        Set<Map.Entry<Organization, AggregationProcessor>> entries = getAggregationProcessorCache().entrySet();

        for (Map.Entry<Organization, AggregationProcessor> entry : entries) {
            if (fiName != null && !fiName.trim().equals("")) {
                if (entry.getKey().getShortName().equals(fiName)) {
                    updateBestBifOfferPublicationInterval(entry.getValue().getConfig());
                }
            } else {
                updateBestBifOfferPublicationInterval(entry.getValue().getConfig());
            }
        }
    }

    private void updateTermAggregationRoundingFactors(String fiName) {
        Set<Map.Entry<Organization, AggregationProcessor>> entries = getAggregationProcessorCache().entrySet();

        for (Map.Entry<Organization, AggregationProcessor> entry : entries) {
            if (fiName != null && !fiName.trim().equals("")) {
                if (entry.getKey().getShortName().equals(fiName)) {
                    updateTermAggregationRoundingFactor(entry.getValue().getConfig());
                }
            } else {
                updateTermAggregationRoundingFactor(entry.getValue().getConfig());
            }
        }
    }

    private void updateTermAggregationAmtInBase(String fiName) {
        Set<Map.Entry<Organization, AggregationProcessor>> entries = getAggregationProcessorCache().entrySet();

        for (Map.Entry<Organization, AggregationProcessor> entry : entries) {
            if (fiName != null && !fiName.trim().equals("")) {
                if (entry.getKey().getShortName().equals(fiName)) {
                    updateTermAggregationAmtInBase(entry.getValue().getConfig());
                }
            } else {
                updateTermAggregationAmtInBase(entry.getValue().getConfig());
            }
        }
    }

    private void updateCachedQuoteOnSubscription(String fiName) {
        Set<Map.Entry<Organization, AggregationProcessor>> entries = getAggregationProcessorCache().entrySet();

        for (Map.Entry<Organization, AggregationProcessor> entry : entries) {
            if (fiName != null && !fiName.trim().equals("")) {
                if (entry.getKey().getShortName().equals(fiName)) {
                    updateCachedQuoteOnSubscription(entry.getValue().getConfig());
                }
            } else {
                updateCachedQuoteOnSubscription(entry.getValue().getConfig());
            }
        }
    }

    private void updateSamePriceBookPublishingEnabled(String fiName) {
        Set<Map.Entry<Organization, AggregationProcessor>> entries = getAggregationProcessorCache().entrySet();

        for (Map.Entry<Organization, AggregationProcessor> entry : entries) {
            if (fiName != null && !fiName.trim().equals("")) {
                if (entry.getKey().getShortName().equals(fiName)) {
                    updateSamePriceBookPublishingEnabled(entry.getValue().getConfig());
                }
            } else {
                updateSamePriceBookPublishingEnabled(entry.getValue().getConfig());
            }
        }
    }

    private void updateProviderNameDisplayEnabled(String fiName) {
        Set<Map.Entry<Organization, AggregationProcessor>> entries = getAggregationProcessorCache().entrySet();

        for (Map.Entry<Organization, AggregationProcessor> entry : entries) {
            if (fiName != null && !fiName.trim().equals("")) {
                if (entry.getKey().getShortName().equals(fiName)) {
                    updateProviderNameDisplayEnabled(entry.getValue().getConfig());
                }
            } else {
                updateProviderNameDisplayEnabled(entry.getValue().getConfig());
            }
        }
    }

    private void updateDisplayNameForFMAProviders(String fiName) {
        Set<Map.Entry<Organization, AggregationProcessor>> entries = getAggregationProcessorCache().entrySet();
        for (Map.Entry<Organization, AggregationProcessor> entry : entries) {
            if (fiName != null && !fiName.trim().equals("")) {
                if (entry.getKey().getShortName().equals(fiName)) {
                    updateDisplayNameForFMAProviders(entry.getValue().getConfig());
                }
            } else {
                updateDisplayNameForFMAProviders(entry.getValue().getConfig());
            }
        }
    }
    
    protected ConcurrentMap<Organization, AggregationProcessor> getAggregationProcessorCache() {
    	return AggregationProcessorManager.getInstance().getAggregationProcessorCache();
    }
    
    private void updateReferenceProviders(String fiName) {
        Set<Map.Entry<Organization, AggregationProcessor>> entries = getAggregationProcessorCache().entrySet();

        for (Map.Entry<Organization, AggregationProcessor> entry : entries) {
            if (fiName != null && !fiName.trim().equals("")) {
                if (entry.getKey().getShortName().equals(fiName)) {
                    updateReferenceProviders(entry.getValue().getConfig());
                }
            } else {
                updateReferenceProviders(entry.getValue().getConfig());
            }
        }
    }

    private void updateProviderQuoteTier(String fiName) {
        Set<Map.Entry<Organization, AggregationProcessor>> entries = getAggregationProcessorCache().entrySet();

        for (Map.Entry<Organization, AggregationProcessor> entry : entries) {
            if (fiName != null && !fiName.trim().equals("")) {
                if (entry.getKey().getShortName().equals(fiName)) {
                    updateProviderQuoteTier(entry.getValue().getConfig());
                }
            } else {
                updateProviderQuoteTier(entry.getValue().getConfig());
            }
        }
    }

    private boolean isProviderQuoteTierProperty(String key) {
        return key.startsWith(getQUOTE_TIERProperty());
    }

    public boolean isAggregationServiceProperty(String key) {
        return key.startsWith(getAGGREGATION_SERVICE_KEYProperty()) || key.equals(IDC_IS_PARTIAL_FILL_PROVIDERS );
    }

    public boolean shouldLogAggServiceBinaryLoggingByteCreationTime() {
        return shouldLogAggServiceBinaryLoggingByteCreationTime;
    }

    public int getBinaryLoggingDisruptorHealthCheckEventsCount() {
        return disruptorHealthCheckEventsCount;
    }

    @Override
    public int getAggLogDisruptorsCount() {
        return numberOfDisruptorsForAggLogging;
    }

    @Override
    public int getTimePeriodInMinsToLogTheAggServiceMetrics() {
        return timePeriodToLogAggServiceMetrics;
    }

    public boolean isClobSubscriptionEnabled(String orgName){
        Boolean value = fiClobSubscriptionEnabled.get(orgName);
        return value == null ? clobSubscriptionEnabled : value;
    }

    public boolean isRiskNetSubscriptionEnabled(String orgName){
        Boolean value = fiRiskNetSubscriptionEnabled.get(orgName);
        return value == null ? riskNetSubscriptionEnabled : value;
    }

    public String getCustomerOrgForClientTag(String makerOrg,String clientTag){
        Map<String, String> stringStringMap = clientTagVersusOrgShortName.get( makerOrg );
        if(stringStringMap != null){
            return stringStringMap.get( clientTag );
        }
        return null;
    }

    @Override
    public int getMaxTiersToLog() {
        return maxTiersToLog;
    }

    @Override
    public boolean isFullAmountAggregationEnabled(String orgName) {
        Map<String,Boolean> bMap = faAggregationEnabledMap;
        if( bMap != null ){
            Boolean v = bMap.get(orgName);
            if( v != null ){
                return v;
            }
        }
        return faAggregationEnabled;
    }
}
