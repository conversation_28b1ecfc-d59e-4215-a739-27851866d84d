package com.integral.aggregation.config;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.AggregationProcessorManager;
import com.integral.aggregation.stream.Aggregator;
import com.integral.is.common.liquidityProvision.LiquidityProvisionChangeHandler;
import com.integral.is.common.mbean.ISFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;

import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 11/29/12
 * Time: 2:16 PM
 * A change handler to listen and update aggregation related changes for Liquidity Rules page.
 */
public class LiquidityProvisionAggregationChangeHandler implements LiquidityProvisionChangeHandler {

    public void handleChange(String orgName, Map props) {
        Organization org = ReferenceDataCacheC.getInstance().getOrganization(orgName);
        if( org!= null && ISFactory.getInstance().getISMBean().isLiquidityProvisioningEnabled(orgName) ){
            AggregationProcessor aggProcessor = AggregationProcessorManager.getInstance().getAggregationProcessor(org);
            if(aggProcessor != null) {
                for(Aggregator aggregator : aggProcessor.getClientSubscriptionManager().getAggregators()) {
                    aggregator.getAggregatorFactory().recomputeLiquidityProvisionParams();
                    aggregator.resetProviderPriceHandlers();
                }
            }
        }
    }
}
