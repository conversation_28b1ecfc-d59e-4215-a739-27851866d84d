//package com.integral.aggregation.config;
//
//import com.integral.aggregation.AggregationResponseHandler;
//import com.integral.aggregation.Response;
//import com.integral.aggregation.price.FXPriceBook;
//import com.lmax.disruptor.EventHandler;
//
//import java.util.List;
//
///**
//*
//*/
//public class AggregationTaskExecutorHandler implements EventHandler<AggregationTaskExecutor> {
//
//    public void onEvent(AggregationTaskExecutor event, long sequence, boolean endOfBatch) throws Exception {
//        List<AggregationResponseHandler<Response, FXPriceBook>> handlers = event.getHandlers();
//
//        for (AggregationResponseHandler<Response, FXPriceBook> handler : handlers) {
//            handler.onPriceUpdate(event.getPriceBook());
//        }
//    }
//}
