package com.integral.aggregation.config.rfs;

import java.util.concurrent.ConcurrentMap;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.config.AggregationServiceMBeanC;
import com.integral.aggregation.rfs.RFSAggregationProcessorManager;
import com.integral.user.Organization;
/**
 * If some properties are not defined for RFS but defined for ESP then ESP value is used for RFS but that property value will not be 
 * in effect if updated at run time as during update at run time it checks if updated property key starts with RFS prefix and then 
 * only change is applied. So for dynamic behaviour always define a corresponding property for RFS
 */
public class RFSAggregationServiceMBeanC extends AggregationServiceMBeanC implements RFSAggregationServiceMBean{
    private static final String AGGREGATION_SERVICE_KEY = "IDC.RFS.Aggregation";
    private static final String QUOTE_TIER = "IDC.RFS.Aggregation.Quote.Tier";
    private static final String QUOTE_PUBLICATION_INTERVAL = "IDC.RFS.Aggregation.Quote.Publication.Interval";
    private static final String BEST_BID_OFFER_PUBLICATION_INTERVAL = "IDC.RFS.Aggregation.Quote.BestBidOffer.Publication.Interval";
    private static final String SAME_PRICE_BOOK_PUBLISHING_ENABLED = "IDC.RFS.Aggregation.SamePriceBookPublishing.Enabled";
    private static final String OFF_MARKET_REFERENCE_PROVIDERS = "IDC.RFS.Aggregation.OffMarketExclude.ReferenceProviders";
    private static final String REQUEST_EXECUTOR_POOL_SIZE = "IDC.RFS.Aggregation.Request.Executor.Pool.Size";
    private static final String AGGREGATION_SHOW_PROVIDER_NAME = "IDC.RFS.Aggregation.ShowProviderName";

    private static final String FILTER_MULTI_PROVIDER_FILTER_KEY = "IDC.RFS.Aggregation.MultiProviderFilter";
    private static final String FILTER_MULTI_PROVIDER_FILTER_ENABLED = "IDC.RFS.Aggregation.MultiProviderFilter.Enabled";
    private static final String FILTER_MULTI_PROVIDER_STALENESS_CHECK_ENABLED = "IDC.RFS.Aggregation.MultiProviderFilter.StalenessCheck.Enabled";
    private static final String FILTER_MULTI_PROVIDER_OFFMARKET_CHECK_ENABLED = "IDC.RFS.Aggregation.MultiProviderFilter.OffMarketFilter.Enabled";
    private static final String FILTER_MULTI_PROVIDER_INVERTED_RATE_CHECK_ENABLED = "IDC.RFS.Aggregation.MultiProviderFilter.InvertedRateFilter.Enabled";
    private static final String FILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCE = "IDC.RFS.Aggregation.MultiProviderFilter.InvertedRateTolerance";
    private static final String FILTER_MULTI_PROVIDER_OFFMARKET_MAX_PROVIDERS = "IDC.RFS.Aggregation.MultiProviderFilter.OffMarket.MaxProviders";
    private static final String FILTER_MULTI_PROVIDER_OFFMARKET_MIN_PROVIDERS = "IDC.RFS.Aggregation.MultiProviderFilter.OffMarket.MinProviders";
    private static final String FILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVAL = "IDC.RFS.Aggregation.MultiProviderFilter.StalenessCheck.Interval";
    private static final String FILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREAD = "IDC.RFS.Aggregation.MultiProviderFilter.OffMarket.Max.InverseSpread";

    private static final String AGGREGATOR_THREAD_SWEEP_INTERVAL = "IDC.RFS.Aggregation.Aggregator.Thread.Sweep.Interval";
    private static final String AGGREGATORS_PER_AGGREGATOR_THREAD = "IDC.RFS.Aggregation.Aggregators.Per.Aggregator.Thread";
    private static final String AGGREGATOR_THREAD_POOL_SIZE = "IDC.RFS.Aggregation.Aggregator.Thread.Pool.Size";
    private static final String AGGREGATOR_USER_NAME = "IDC.RFS.Aggregation.User.Name";

    private static final String AGGREGATOR_METRICS_PUB_TIME_BUCKETS = "IDC.RFS.Aggregation.Metrics.Pub.Time.Buckets";
    private static final String AGGREGATOR_METRICS_AGG_TIME_BUCKETS = "IDC.RFS.Aggregation.Metrics.Agg.Time.Buckets";
    private static final String AGGREGATOR_METRICS_SEND_TIME_BUCKETS = "IDC.RFS.Aggregation.Metrics.Send.Time.Buckets";
    private static final String AGGREGATOR_METRICS_LOG_TIME_BUCKETS = "IDC.RFS.Aggregation.Metrics.Log.Time.Buckets";

    private static final String AGGREGATION_TERM_ROUNDING_FACTOR = "IDC.RFS.Aggregation.Term.RoundingFactor";
    private static final String AGGREGATION_TERM_DISPLAY_AMOUNT_IN_BASE = "IDC.RFS.Aggregation.Term.DisplayAmountInBase";

    private static final String AGGREGATION_LOG_COMPRESSION_ENABLED = "IDC.RFS.Aggregation.Log.Compression.Enabled";
    private static final String AGGREGATION_LOG_BINARY_ENABLED = "IDC.RFS.Aggregation.Log.Binary.Enabled";

    private static final String AGGREGATION_CACHED_QUOTE_ON_SUBSCRIPTION = "IDC.RFS.Aggregation.Cached.Quote.On.Subscription";

    private static final String AGGREGATION_EXECUTION_RULE_ENABLED = "IDC.RFS.Aggregation.Execution.Rule.Enabled";

	public RFSAggregationServiceMBeanC() {
        super("IdcRFSAggregationServiceMBean");    
    }

	@Override
	protected String getAGGREGATOR_THREAD_SWEEP_INTERVALProperty() {		
		return AGGREGATOR_THREAD_SWEEP_INTERVAL;
	}

	@Override
	protected int getDefaultAGGREGATOR_THREAD_SWEEP_INTERVAL() {		
		return 5;
	}

	@Override
	protected String getAGGREGATORS_PER_AGGREGATOR_THREADProperty() {		
		return AGGREGATORS_PER_AGGREGATOR_THREAD;
	}

	@Override
	protected int getDefaultAGGREGATORS_PER_AGGREGATOR_THREAD() {		
		return 50;
	}

	@Override
	protected String getAGGREGATOR_THREAD_POOL_SIZEProperty() {		
		return AGGREGATOR_THREAD_POOL_SIZE;
	}

	@Override
	protected int getDefaultAGGREGATOR_THREAD_POOL_SIZE() {		
		return 1;
	}

	@Override
	protected String getAGGREGATOR_USER_NAMEProperty() {		
		return AGGREGATOR_USER_NAME;
	}

	@Override
	protected String getDefaultAGGREGATOR_USER_NAME() {		
		return super.getDefaultAGGREGATOR_USER_NAME();
	}

	@Override
	protected String getAGGREGATION_LOG_COMPRESSION_ENABLEDProperty() {		
		return AGGREGATION_LOG_COMPRESSION_ENABLED;
	}

	@Override
	protected boolean getDefaultAGGREGATION_LOG_COMPRESSION_ENABLED() {		
		return true;
	}

	@Override
	protected String getAGGREGATION_LOG_BINARY_ENABLEDProperty() {		
		return AGGREGATION_LOG_BINARY_ENABLED;
	}

	@Override
	protected boolean getDefaultAGGREGATION_LOG_BINARY_ENABLED() {		
		return false;
	}

	@Override
	protected String getAGGREGATION_TERM_DISPLAY_AMOUNT_IN_BASEProperty() {		
		return AGGREGATION_TERM_DISPLAY_AMOUNT_IN_BASE;
	}

	@Override
	protected boolean getDefaultAGGREGATION_TERM_DISPLAY_AMOUNT_IN_BASE() {		
		return false;
	}

	@Override
	protected String getAGGREGATION_TERM_ROUNDING_FACTORProperty() {		
		return AGGREGATION_TERM_ROUNDING_FACTOR;
	}

	@Override
	protected int getDefaultAGGREGATION_TERM_ROUNDING_FACTOR() {		
		return super.getDefaultAGGREGATION_TERM_ROUNDING_FACTOR();
	}

	@Override
	protected String getAGGREGATOR_METRICS_PUB_TIME_BUCKETSProperty() {		
		return AGGREGATOR_METRICS_PUB_TIME_BUCKETS;
	}

	@Override
	protected String getDefaultAGGREGATOR_METRICS_PUB_TIME_BUCKETS() {		
		return "250,500,750,1000,1250,1500,1750,2000,2250,2500,2750,3000";
	}

	@Override
	protected String getAGGREGATOR_METRICS_AGG_TIME_BUCKETSProperty() {		
		return AGGREGATOR_METRICS_AGG_TIME_BUCKETS;
	}

	@Override
	protected String getDefaultAGGREGATOR_METRICS_AGG_TIME_BUCKETS() {		
		return "100,200,300,400,500,600,700,800,900,1000,1200,1500,2000";
	}

	@Override
	protected String getAGGREGATOR_METRICS_SEND_TIME_BUCKETSProperty() {		
		return AGGREGATOR_METRICS_SEND_TIME_BUCKETS;
	}

	@Override
	protected String getDefaultAGGREGATOR_METRICS_SEND_TIME_BUCKETS() {		
		return "100,200,300,400,500,600,700,800,900,1000,1200,1500,2000";
	}

	@Override
	protected String getAGGREGATOR_METRICS_LOG_TIME_BUCKETSProperty() {		
		return AGGREGATOR_METRICS_LOG_TIME_BUCKETS;
	}

	@Override
	protected String getDefaultAGGREGATOR_METRICS_LOG_TIME_BUCKETS() {		
		return "100,200,300,400,500,600,700,800,900,1000,1200,1500,2000";
	}

	@Override
	protected String getAGGREGATION_CACHED_QUOTE_ON_SUBSCRIPTIONProperty() {		
		return AGGREGATION_CACHED_QUOTE_ON_SUBSCRIPTION;
	}

	@Override
	protected boolean getDefaultAGGREGATION_CACHED_QUOTE_ON_SUBSCRIPTION() {		
		return false;
	}

	@Override
	protected String getAGGREGATION_EXECUTION_RULE_ENABLEDProperty() {		
		return AGGREGATION_EXECUTION_RULE_ENABLED;
	}

	@Override
	protected boolean getDefaultAGGREGATION_EXECUTION_RULE_ENABLED() {		
		return false;
	}

	@Override
	protected String getSAME_PRICE_BOOK_PUBLISHING_ENABLEDProperty() {		
		return SAME_PRICE_BOOK_PUBLISHING_ENABLED;
	}

	@Override
	protected boolean getDefaultSAME_PRICE_BOOK_PUBLISHING_ENABLED() {		
		return true;
	}

	@Override
	protected String getBEST_BID_OFFER_PUBLICATION_INTERVALProperty() {		
		return BEST_BID_OFFER_PUBLICATION_INTERVAL;
	}

	@Override
	protected int getDefaultBEST_BID_OFFER_PUBLICATION_INTERVAL() {		
		return 0;
	}

	@Override
	protected String getQUOTE_PUBLICATION_INTERVALProperty() {		
		return QUOTE_PUBLICATION_INTERVAL;
	}

	@Override
	protected int getDefaultQUOTE_PUBLICATION_INTERVAL() {		
		return 100;
	}

	@Override
	protected String getAGGREGATION_SHOW_PROVIDER_NAMEProperty() {		
		return AGGREGATION_SHOW_PROVIDER_NAME;
	}

	@Override
	protected boolean getDefaultAGGREGATION_SHOW_PROVIDER_NAME() {		
		return true;
	}

	@Override
	protected String getFILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREADProperty() {	
		return FILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREAD;
	}

	@Override
	protected double getDefaultFILTER_MULTI_PROVIDER_OFFMARKET_MAX_INVERSESPREAD() {	
		return 0;
	}

	@Override
	protected String getFILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVALProperty() {	
		return FILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVAL;
	}

	@Override
	protected long getDefaultFILTER_MULTI_PROVIDER_STALENESS_CHECK_INTERVAL() {
		return 5;
	}

	@Override
	protected String getFILTER_MULTI_PROVIDER_OFFMARKET_MIN_PROVIDERSProperty() {	
		return FILTER_MULTI_PROVIDER_OFFMARKET_MIN_PROVIDERS;
	}

	@Override
	protected int getDefaultFILTER_MULTI_PROVIDER_OFFMARKET_MIN_PROVIDERS() {
		return 3;
	}

	@Override
	protected String getFILTER_MULTI_PROVIDER_OFFMARKET_MAX_PROVIDERSProperty() {		
		return FILTER_MULTI_PROVIDER_OFFMARKET_MAX_PROVIDERS;
	}

	@Override
	protected int getDefaultFILTER_MULTI_PROVIDER_OFFMARKET_MAX_PROVIDERS() {		
		return 5;
	}

	@Override
	protected String getFILTER_MULTI_PROVIDER_INVERTED_RATE_CHECK_ENABLEDProperty() {		
		return FILTER_MULTI_PROVIDER_INVERTED_RATE_CHECK_ENABLED;
	}

	@Override
	protected boolean getDefaultFILTER_MULTI_PROVIDER_INVERTED_RATE_CHECK_ENABLED() {		
		return false;
	}

	@Override
	protected String getFILTER_MULTI_PROVIDER_OFFMARKET_CHECK_ENABLEDProperty() {		
		return FILTER_MULTI_PROVIDER_OFFMARKET_CHECK_ENABLED;
	}

	@Override
	protected boolean getDefaultFILTER_MULTI_PROVIDER_OFFMARKET_CHECK_ENABLED() {		
		return false;
	}

	@Override
	protected String getFILTER_MULTI_PROVIDER_STALENESS_CHECK_ENABLEDProperty() {		
		return FILTER_MULTI_PROVIDER_STALENESS_CHECK_ENABLED;
	}

	@Override
	protected boolean getDefaultFILTER_MULTI_PROVIDER_STALENESS_CHECK_ENABLED() {		
		return false;
	}

	@Override
	protected String getFILTER_MULTI_PROVIDER_FILTER_ENABLEDProperty() {		
		return FILTER_MULTI_PROVIDER_FILTER_ENABLED;
	}

	@Override
	protected boolean getDefaultFILTER_MULTI_PROVIDER_FILTER_ENABLED() {		
		return false;
	}

	@Override
	protected String getFILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCEProperty() {		
		return FILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCE;
	}

	@Override
	protected double getDefaultFILTER_MULTI_PROVIDER_INVERTED_RATE_TOLERANCE() {		
		return 0;
	}

	@Override
	protected String getREQUEST_EXECUTOR_POOL_SIZEProperty() {		
		return REQUEST_EXECUTOR_POOL_SIZE;
	}

	@Override
	protected int getDefaultREQUEST_EXECUTOR_POOL_SIZE() {		
		return 1;
	}

	@Override
	protected String getQUOTE_TIERProperty() {		
		return QUOTE_TIER;
	}

	@Override
	protected int getDefaultQUOTE_TIER() {		
		return 1;
	}

	@Override
	protected String getOFF_MARKET_REFERENCE_PROVIDERSProperty() {		
		return OFF_MARKET_REFERENCE_PROVIDERS;
	}

	@Override
	protected String getFILTER_MULTI_PROVIDER_FILTER_KEYProperty() {		
		return FILTER_MULTI_PROVIDER_FILTER_KEY;
	}

	@Override
	protected String getAGGREGATION_SERVICE_KEYProperty() {		
		return AGGREGATION_SERVICE_KEY;
	}
	
    protected ConcurrentMap<Organization, AggregationProcessor> getAggregationProcessorCache() {
    	return RFSAggregationProcessorManager.getInstance().getAggregationProcessorCache();
    }

	@Override
	public boolean isAggLogBinaryEnabled( String fiOrg )
	{
		boolean isRFSAggLogBinaryEnabledForFI = false;
		if ( fiOrg != null )
		{
			String key = AGGREGATION_LOG_BINARY_ENABLED + "." + fiOrg.trim();
			if ( containsProperty ( key ) )
			{
				isRFSAggLogBinaryEnabledForFI = getBooleanProperty ( key, false );
			}
		}
		else
		{
			isRFSAggLogBinaryEnabledForFI = getBooleanProperty ( AGGREGATION_LOG_BINARY_ENABLED, false );
		}
		return isRFSAggLogBinaryEnabledForFI;
	}
}
