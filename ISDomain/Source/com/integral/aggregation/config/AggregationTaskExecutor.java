//package com.integral.aggregation.config;
//
//import com.integral.aggregation.AggregationResponseHandler;
//import com.integral.aggregation.Response;
//import com.integral.aggregation.price.FXPriceBook;
//import com.lmax.disruptor.EventFactory;
//
//import java.util.List;
//
///**
//*
//*/
//public class AggregationTaskExecutor {
//    public static final EventFactory<AggregationTaskExecutor> FACTORY = new AggregationTaskExecutorFactory();
//
//    private List<AggregationResponseHandler<Response, FXPriceBook>> handlers;
//    private FXPriceBook priceBook;
//
//    public List<AggregationResponseHandler<Response, FXPriceBook>> getHandlers() {
//        return handlers;
//    }
//
//    public void setHandlers(List<AggregationResponseHandler<Response, FXPriceBook>> handlers) {
//        this.handlers = handlers;
//    }
//
//    public FXPriceBook getPriceBook() {
//        return priceBook;
//    }
//
//    public void setPriceBook(FXPriceBook priceBook) {
//        this.priceBook = priceBook;
//    }
//
//    private static class AggregationTaskExecutorFactory implements EventFactory<AggregationTaskExecutor> {
//
//        public AggregationTaskExecutor newInstance() {
//            return new AggregationTaskExecutor();
//        }
//    }
//}
