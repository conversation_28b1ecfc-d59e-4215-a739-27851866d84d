package com.integral.aggregation.config;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.AggregationProcessorConfig;
import com.integral.aggregation.AggregationProcessorManager;
import com.integral.aggregation.model.AggregationParameters;
import com.integral.aggregation.stream.Aggregator;
import com.integral.is.common.liquidityProvision.LiquidityProvisionChangeHandler;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.taker.TakerOrganizationFunction;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * Created with IntelliJ IDEA.
 * User: anatarajan
 * Date: 6/11/13
 * Time: 11:41 AM
 * To change this template use File | Settings | File Templates.
 */
public class LiquidityRulePriceMakingChangeHandler implements LiquidityProvisionChangeHandler {
    private static final Log log = LogFactory.getLog(LiquidityRulePriceMakingChangeHandler.class);

    @Override
    public void handleChange(String orgName, Map props) {
        log.info("LiquidityRulePriceMakingChangeHandler.handleChange: Processing Liquidity Rule Price Making changes for org " + orgName);
        Organization org = ReferenceDataCacheC.getInstance().getOrganization(orgName);
        if (org != null && AggregationServiceFactory.getInstance().getAggregationMBean().isAggExecRuleEnabled(orgName)) {
            //Update parameters.
            TakerOrganizationFunction takerOrganizationFunction = org.getTakerOrganizationFunction();
            AggregationParameters aggregationParameters = takerOrganizationFunction.getAggregationParameters();
            AggregationProcessor aggProcessor = AggregationProcessorManager.getInstance().getAggregationProcessor(org);
            if (aggProcessor != null) {
                for(Aggregator aggregator : aggProcessor.getClientSubscriptionManager().getAggregators()) {
                    aggregator.reset(aggregationParameters.getFullStreamUpdateInterval(), aggregationParameters.getBestBidOfferStreamUpdateInterval());
                }
            }
        }
    }
}
