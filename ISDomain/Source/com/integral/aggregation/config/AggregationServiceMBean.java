package com.integral.aggregation.config;

import com.integral.aggregation.AggregationProcessorConfig;
import com.integral.aggregation.MultiProviderFilterConfig;
import com.integral.aggregation.util.FXMap;
import com.integral.system.configuration.IdcMBean;

import java.util.Set;

/**
 *
 */
public interface AggregationServiceMBean extends IdcMBean {

    int getProviderQuoteTier();

    FXMap<String, FXMap<String, Integer, Integer>, Integer> getProviderQuoteTiers(String fiOrg);

    FXMap<String, Integer, Integer> getProviderQuoteTiers(String fiOrg, String lpOrg);

    Integer getProviderQuoteTiers(String fiOrg, String lpOrg, String ccyPair);

    Set<String> getReferenceProviders();

    Set<String> getReferenceProviders(String fiOrg);

    Integer getQuotePublicationInterval();

    Integer getQuotePublicationInterval(String fiOrg);

    Integer getBestBidOfferPublicationInterval();

    Integer getBestBidOfferPublicationInterval(String fiOrg);

    Boolean isSamePriceBookPublishingEnabled();

    Boolean isSamePriceBookPublishingEnabled(String fiOrg);

    int getRequestExecutorPoolSize();

    void updateAggregationProcessorConfig(AggregationProcessorConfig config);

    void updateMultiProviderFilterConfig(MultiProviderFilterConfig filterConfig);

    Boolean isStalenessCheckEnabled();

    Boolean isInvertedRateFilterCheckEnabled();

    Boolean isMultiProviderFilterEnabled();

    Boolean isOffMarketCheckEnabled();

    Integer getOffMarketMinProviders();

    Integer getOffMarketMaxProviders();

    Long getStalenessCheckInterval();

    Double getOffMarketMaxInverseSpread();

    Boolean isStalenessCheckEnabled(String fiOrg);

    Boolean isInvertedRateFilterCheckEnabled(String fiOrg);

    Boolean isMultiProviderFilterEnabled(String fiOrg);

    Boolean isOffMarketCheckEnabled(String fiOrg);

    Integer getOffMarketMinProviders(String fiOrg);

    Integer getOffMarketMaxProviders(String fiOrg);

    Long getStalenessCheckInterval(String fiOrg);

    Double getOffMarketMaxInverseSpread(String fiOrg);

    Long getStalenessCheckInterval(String fiOrg, String ccyPair);

    Double getOffMarketMaxInverseSpread(String fiOrg, String ccyPair);

    int getAggregatorThreadSweepInterval();

    String getAggregationUserShortName();

    int getAggregatorsPerThread();

    int getAggregatorThreadPoolSize();

    long[] getPublicationTimeBuckets();

    long[] getAggregationTimeBuckets();

    long[] getSendTimeBuckets();

    long[] getLogTimeBuckets();

    Boolean isProviderNameDisplayEnabled();

    Boolean isProviderNameDisplayEnabled(String fiOrg);

    String getDisplayNameForFMAProviders();

    String getDisplayNameForFMAProviders(String fiOrg);

    FXMap<String, Long, Long> getCcyPairStalenessCheckIntervals(String fiOrg);

    FXMap<String, Double, Double> getCcyPairOffMarketMaxInverseSpreads(String fiOrg);

    Integer getTermAggregationRoundingFactor();

    Integer getTermAggregationRoundingFactor(String fiOrg);

    Integer getTermAggregationRoundingFactor(String fiOrg, String ccyPair);

    Boolean isTermAggregationDisplayAmtInBase();

    Boolean isTermAggregationDisplayAmtInBase(String fiOrg);

    boolean isAggLogCompressionEnabled();

    boolean isAggLogBinaryEnabled();

    Double getInvertedRateTolerance();

    Double getInvertedRateTolerance(String fiOrg);

    Double getInvertedRateTolerance(String fiOrg, String ccyPair);

    FXMap<String, Double, Double> getCcyPairInvertedRateTolerances(String fiOrg);

    Boolean isCachedQuoteOnSubscription();

    Boolean isCachedQuoteOnSubscription(String fiOrg);

    Boolean isAggExecRuleEnabled();

    Boolean isAggExecRuleEnabled(String fiOrg);
    
    public boolean isAggLogBinaryEnabled(String fiOrg); 
    
    public int getAggLogBinaryDisruptorBufferSize();
    
    public int getAggLogRingBufferThresholdPercent();
    
    public int getAggLogByteBufferSize();
    
    public boolean shouldLogAggServiceBinaryLoggingByteCreationTime();
    
    public int getBinaryLoggingDisruptorHealthCheckEventsCount();
    
    public int getAggLogDisruptorsCount();

    public int getTimePeriodInMinsToLogTheAggServiceMetrics();

    public boolean isClobSubscriptionEnabled(String orgName);

    public boolean isRiskNetSubscriptionEnabled(String orgName);

    public String getCustomerOrgForClientTag(String makerOrg,String clientTag);

    public int getMaxTiersToLog();

    /**
     *
     * @param orgName Organization short name
     * @return true if Full Amount quotes can be aggregated for price discovery
     * If true then full amount quotes will be aggregated in price discovery for specific aggregation methods
     * Not all aggregation methods will aggregate full amount quotes even if this property is true
     */
    public boolean isFullAmountAggregationEnabled(String orgName);

}


