package com.integral.aggregation;

import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.log.ASAggregationLogger;
import com.integral.aggregation.log.ASAggregationLoggerC;
import com.integral.aggregation.log.ASBinaryAggregationLogger;
import com.integral.aggregation.price.FXPrice;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.price.FXPriceBookC;
import com.integral.aggregation.price.FXPriceC;
import com.integral.aggregation.price.SwapFXPriceBookC;
import com.integral.aggregation.rfs.RFSAggregator;
import com.integral.aggregation.stream.ASQuoteAggregatorFactoryC;
import com.integral.aggregation.stream.ESPAggregator;
import com.integral.aggregation.stream.FXPriceBookBuilder;
import com.integral.aggregation.stream.MarketRateBuilder;
import com.integral.aggregation.stream.ProviderPriceObserver;
import com.integral.aggregation.stream.view.LadderViewRequest;
import com.integral.aggregation.stream.view.LadderedViewAggregator;
import com.integral.aggregation.subscription.AggregationProcessorRequest;
import com.integral.aggregation.subscription.AggregationProcessorRequestC;
import com.integral.aggregation.subscription.client.ClientSubscriptionManager;
import com.integral.aggregation.subscription.provider.ProviderSubscriptionManager;
import com.integral.aggregation.subscription.provider.RFSProviderSubscriptionManager;
import com.integral.aggregation.subscription.provider.quote.ProviderQuoteHandler;
import com.integral.broker.aggregate.QuoteAggregatorFactory;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.ModelFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.Organization;

/**
 *
 */
public class AggregatorFactory {
    protected AggregationProcessor aggregationProcessor;
    protected ClientSubscriptionManager clientSubManager;
    protected ProviderSubscriptionManager providerSubManager;
    protected RFSProviderSubscriptionManager rfsProviderSubManager;
    protected AggregationProcessorConfig config;
    protected MultiProviderFilterConfig filterConfig;
    protected MarketRateBuilder rateBuilder;
    protected FXPriceBookBuilder priceBookBuilder;

    protected static Log log = LogFactory.getLog( AggregatorFactory.class );
    
    protected ConcurrentMap<CurrencyPair, ConcurrentMap<Organization, ProviderQuoteHandler>> quoteHandlers =
            				new ConcurrentHashMap<CurrencyPair, ConcurrentMap<Organization , ProviderQuoteHandler>>();
    protected BrokerOrganizationFunction orgFunction;
    protected ASAggregationLogger defaultAggregator;

    public AggregatorFactory(AggregationProcessor processor) {
        this.aggregationProcessor = processor;
        log.info("AGGREGATOR_FACTORY_CREATED_FOR_ORG=" + processor.getFiOrg().getShortName());
        
    }

    public ClientSubscriptionManager getClientSubManager() {
        if (clientSubManager == null) {
            clientSubManager = new ClientSubscriptionManager(this.aggregationProcessor);
        }
        return clientSubManager;
    }

    public ProviderSubscriptionManager getProviderSubManager() {
        if (providerSubManager == null) {
            providerSubManager = new ProviderSubscriptionManager(this.aggregationProcessor);
        }
        return providerSubManager;
    }

    public AggregationProcessorConfig getAggregationProcessorConfig() {
        if (config == null) {
            config = newAggregationProcessorConfig();
        }
        return config;
    }

    private AggregationProcessorConfig newAggregationProcessorConfig() {
        return new AggregationProcessorConfig(this.aggregationProcessor);
    }

    public MultiProviderFilterConfig getMultiProviderFilterConfig() {
        if (filterConfig == null) {
            filterConfig = newMultiProviderConfig();
        }
        return filterConfig;
    }

    private MultiProviderFilterConfig newMultiProviderConfig() {
        return new MultiProviderFilterConfig(this.aggregationProcessor);
    }

    public RFSProviderSubscriptionManager getRFSProviderSubManager() {
        if (rfsProviderSubManager == null) {
            rfsProviderSubManager = new RFSProviderSubscriptionManager(this.aggregationProcessor);
        }
        return rfsProviderSubManager;
    }

    /**
     * returns QuoteHandler for deployed organization .
     *
     * @param provider
     * @return
     */
    public ProviderQuoteHandler getOrCreateQuoteHandler(CurrencyPair ccyPair, Organization provider) {
        ProviderQuoteHandler quoteHandler;
        ConcurrentMap<Organization, ProviderQuoteHandler> ccyPairQuoteHandlers = quoteHandlers.get(ccyPair);
        if (ccyPairQuoteHandlers == null) {
            ccyPairQuoteHandlers = new ConcurrentHashMap<Organization, ProviderQuoteHandler>();
            quoteHandlers.put(ccyPair, ccyPairQuoteHandlers);

            quoteHandler = new ProviderQuoteHandler(this.aggregationProcessor, provider, ccyPair);
            ccyPairQuoteHandlers.put(provider, quoteHandler);
        } else {
            quoteHandler = ccyPairQuoteHandlers.get(provider);
            if (quoteHandler == null) {
                quoteHandler = new ProviderQuoteHandler(this.aggregationProcessor, provider, ccyPair);
                ccyPairQuoteHandlers.put(provider, quoteHandler);
            }
        }
        return quoteHandler;
    }

    public void removeQuoteHandler(CurrencyPair ccyPair, Organization provider)
    {
        ConcurrentMap<Organization, ProviderQuoteHandler> ccyPairQuoteHandlers = quoteHandlers.get(ccyPair);
        if(ccyPairQuoteHandlers != null)
        {
        	ProviderQuoteHandler quoteHandler = ccyPairQuoteHandlers.get(provider);
            // quotehandler can be null if it has already been removed by a different thread , if there are two users logged in for same customer.
        	if(quoteHandler != null){
                quoteHandler.stop();
                ccyPairQuoteHandlers.remove(provider);
            }
        }
    }

    public ProviderQuoteHandler getQuoteHandler(CurrencyPair ccyPair, Organization provider) {
        ProviderQuoteHandler quoteHandler = null;
        ConcurrentMap<Organization, ProviderQuoteHandler> ccyPairQuoteHandlers = quoteHandlers.get(ccyPair);
        if (ccyPairQuoteHandlers != null) {
            quoteHandler = ccyPairQuoteHandlers.get(provider);
        }
        return quoteHandler;
    }

    public BrokerOrganizationFunction getBrokerOrganizationFunction() {
        if (orgFunction == null) {
            Organization org = this.aggregationProcessor.getFiOrg();
            orgFunction = org.getBrokerOrganizationFunction();

            if (orgFunction == null) {
                orgFunction =  createBrokerOrganizationFunction(org);
            }
        }
        return orgFunction;
    }

    private static BrokerOrganizationFunction createBrokerOrganizationFunction( Organization organization ) {
        BrokerOrganizationFunction brokerOrganizationFunction = ModelFactory.getInstance().newBrokerOrganizationFunction();
        brokerOrganizationFunction.setOrderExecutionActive( true );
        brokerOrganizationFunction.setPriceMakingActive( true );
        brokerOrganizationFunction.setOrganization( organization );
        brokerOrganizationFunction.setDisabledProviders( Collections.<Organization>emptyList() );
        return brokerOrganizationFunction;
    }

    public ESPAggregator newESPAggregator( AggregationProcessorRequest request ) {
        return new ESPAggregator(this.aggregationProcessor, request);
    }

    public RFSAggregator newRFSAggregator( AggregationProcessorRequest request ) {
        return new RFSAggregator(this.aggregationProcessor, request);
    }

    public LadderedViewAggregator newLadderedViewAggregator( ESPAggregator espAggregator ) {
        return new LadderedViewAggregator(espAggregator);
    }

    public ProviderPriceObserver newPriceObserver(ESPAggregator publisher, ProviderQuoteHandler quoteHandler, Organization provider) {
        return new ProviderPriceObserver(publisher, quoteHandler, provider);
    }

    public ASAggregationLogger newAggregatorLogger(ESPAggregator aggregator) {
        try {
        	String fiOrg = aggregator.getAggregationProcessor().getFiOrg().getShortName();
        	
            if(AggregationServiceFactory.getInstance().getAggregationMBean().isAggLogBinaryEnabled(fiOrg)){
                return new ASBinaryAggregationLogger(aggregator);
            } else {
                return new ASAggregationLoggerC(aggregator);
            }
            
        } catch(Exception e) {
            e.printStackTrace();
            return getDefaultAggregator();
        }
    }

    public ASAggregationLogger newAggregatorLogger(RFSAggregator aggregator) {
        return getDefaultAggregator();
    }

    public MarketRateBuilder getMarketRateBuilder() {
        if (rateBuilder == null) {
            rateBuilder = new MarketRateBuilder(this.aggregationProcessor);
        }
        return rateBuilder;
    }

    public FXPriceBook newPriceBook(boolean isSwap) {
        return newPriceBook(isSwap, 5, 5);
    }

    public FXPrice newPrice() {
        return new FXPriceC();
    }

    public FXPriceBook newPriceBook(boolean isSwap, int bidSize, int offerSize) {
    	if (isSwap) {
    		return new SwapFXPriceBookC(bidSize, offerSize);
    	}
        return new FXPriceBookC(bidSize, offerSize);
    }

    public FXPriceBookBuilder getPriceBookBuilder() {
        if (priceBookBuilder == null) {
            priceBookBuilder = new FXPriceBookBuilder(this.aggregationProcessor);
        }
        return priceBookBuilder;
    }

    public AggregationProcessorRequest newAggregationProcessorRequest(String requestId) {
        AggregationProcessorRequest request = new AggregationProcessorRequestC();
        request.setRequestId(requestId);
        return request;
    }

    public LadderViewRequest newLadderViewRequest() {
        return new LadderViewRequest();
    }

    public ASAggregationLogger getDefaultAggregator() {
        if (defaultAggregator == null) {
            defaultAggregator = new ASAggregationLoggerC();
        }
        return defaultAggregator;
    }

    public QuoteAggregatorFactory newQuoteAggregatorFactory(ESPAggregator espAggregator) {
        return new ASQuoteAggregatorFactoryC(espAggregator);
    }

    public FXPrice newFXPrice() {
        return new FXPriceC();
    }
}
