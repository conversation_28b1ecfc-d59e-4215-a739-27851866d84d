package com.integral.aggregation;

import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.rfs.RFSAggregationProcessor;
import com.integral.aggregation.stream.Aggregator;
import com.integral.aggregation.util.FXMap;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.liquidityProvision.LiquidityProvision;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.List;
import java.util.Set;

/**
 *
 */
public class AggregationProcessorConfig {
    public static final int ADMIN = 0;
    public static final int REQUEST = 1;
    private int configChannel = ADMIN;

    private AggregationProcessor aggregationProcessor;

    private Organization organization;
    private LegalEntity legalEntity;
    private User user;

    private double subscriptionSize;
    private boolean isSamePriceBokPublishingEnabled;
    private long quotePublicationInterval;
    private long bestBidOfferPublicationInterval;
    private String providerRequestChannel;
    private boolean isProviderNameDisplayEnabled;
    private String displayNameForFMAProviders;
    private List<Organization> priceProviders;
    private Set<Organization> referenceProviders;
    private Set<Organization> partialProviders;

    private AggregationType aggregator;
    private BrokerOrganizationFunction brokerOrgFunction;

    private int defaultProviderQuoteTier;
    private FXMap<String, FXMap<String, Integer, Integer>, Integer> providerQuoteTiers;

    private AggregationView view;
    private MultiProviderFilterConfig multiProviderFilterConfig;

    private boolean isTermAggregationDisplayAmtInBase;
    private int termAggregationRoundingFactor;
    private boolean isCachedQuoteOnSubscription;

    public AggregationProcessorConfig(AggregationProcessor processor) {
        this.aggregationProcessor = processor;
        this.organization = this.aggregationProcessor.getFiOrg();
        this.legalEntity = this.organization.getDefaultDealingEntity();
        this.multiProviderFilterConfig = this.aggregationProcessor.getFactory().getMultiProviderFilterConfig();
        if (processor instanceof RFSAggregationProcessor) {
        	AggregationServiceFactory.getInstance().getRFSAggregationMBean()
            					.updateMultiProviderFilterConfig(this.multiProviderFilterConfig);
        } else {
        	AggregationServiceFactory.getInstance().getAggregationMBean()
            					.updateMultiProviderFilterConfig(this.multiProviderFilterConfig);
        }
        
    }

    public void reset() {
    }

    public LegalEntity getLegalEntity() {
        return legalEntity;
    }

    public AggregationProcessorConfig setLegalEntity(LegalEntity legalEntity) {
        this.legalEntity = legalEntity;
        return this;
    }

    public int getConfigChannel() {
        return configChannel;
    }

    public void setConfigChannel(int configChannel) {
        this.configChannel = configChannel;
    }

    public Organization getOrganization() {
        return this.organization;
    }

    public BrokerOrganizationFunction getBrokerOrgFunction() {
        if (brokerOrgFunction == null) {
            brokerOrgFunction = organization.getBrokerOrganizationFunction();
        }
        return brokerOrgFunction;
    }

    public List<Organization> getPriceProviders() {
        return priceProviders;
    }

    public AggregationProcessorConfig setPriceProviders(List<Organization> priceProviders) {
        this.priceProviders = priceProviders;
        return this;
    }

    public Set<Organization> getReferenceProviders() {
        return referenceProviders;
    }

    public AggregationProcessorConfig setReferenceProviders(Set<Organization> referenceProviders) {
        this.referenceProviders = referenceProviders;
        return this;
    }

    public Set<Organization> getPartialProviders() {
        return partialProviders;
    }

    public AggregationProcessorConfig setPartialProviders(Set<Organization> partialProviders) {
        this.partialProviders = partialProviders;
        return this;
    }

    public double getSubscriptionSize() {
        return subscriptionSize;
    }

    public AggregationProcessorConfig setSubscriptionSize(double subscriptionSize) {
        this.subscriptionSize = subscriptionSize;
        return this;
    }

    public long getQuotePublicationInterval() {
        return quotePublicationInterval;
    }

    public AggregationProcessorConfig setQuotePublicationInterval(long quotePublicationInterval) {
        if (this.quotePublicationInterval != quotePublicationInterval) {
            this.quotePublicationInterval = quotePublicationInterval;

            for (Aggregator aggregator : this.aggregationProcessor.getFactory().getClientSubManager().getAggregators()) {
                aggregator.reset(this.quotePublicationInterval, this.bestBidOfferPublicationInterval);
            }
        }
        return this;
    }

    public long getBestBidOfferPublicationInterval() {
        return bestBidOfferPublicationInterval;
    }

    public AggregationProcessorConfig setBestBifOfferPublicationInterval(long bestBidOfferPublicationInterval) {
        if (this.bestBidOfferPublicationInterval != bestBidOfferPublicationInterval) {
            this.bestBidOfferPublicationInterval = bestBidOfferPublicationInterval;

            for (Aggregator aggregator : this.aggregationProcessor.getFactory().getClientSubManager().getAggregators()) {
                aggregator.reset(this.quotePublicationInterval, this.bestBidOfferPublicationInterval);
            }
        }
        return this;
    }

    public String getProviderRequestChannel() {
        return providerRequestChannel;
    }

    public AggregationProcessorConfig setProviderRequestChannel(String providerRequestChannel) {
        this.providerRequestChannel = providerRequestChannel;
        return this;
    }

    public AggregationType getAggregator() {
        return aggregator;
    }

    public AggregationProcessorConfig setAggregator(AggregationType aggregator) {
        this.aggregator = aggregator;
        return this;
    }

    public User getUser() {
        return user;
    }

    public AggregationProcessorConfig setUser(User user) {
        this.user = user;
        return this;
    }

    public AggregationView getView() {
        return view;
    }

    public void setView(AggregationView view) {
        this.view = view;
    }

    public boolean isSamePriceBokPublishingEnabled() {
        return isSamePriceBokPublishingEnabled;
    }

    public void setSamePriceBokPublishingEnabled(boolean samePriceBokPublishingEnabled) {
        isSamePriceBokPublishingEnabled = samePriceBokPublishingEnabled;
    }

    public boolean isProviderNameDisplayEnabled() {
        return isProviderNameDisplayEnabled;
    }

    public void setProviderNameDisplayEnabled(boolean providerNameDisplayEnabled) {
        isProviderNameDisplayEnabled = providerNameDisplayEnabled;
    }

    public String getDisplayNameForFMAProviders() {
        return displayNameForFMAProviders;
    }

    public void setDisplayNameForFMAProviders(String displayNameForFMAProviders) {
        this.displayNameForFMAProviders = displayNameForFMAProviders;
    }

    public int getDefaultProviderQuoteTier() {
        return defaultProviderQuoteTier;
    }

    public void setDefaultProviderQuoteTier(int defaultProviderQuoteTier) {
        this.defaultProviderQuoteTier = defaultProviderQuoteTier;
    }

    public FXMap<String, FXMap<String, Integer, Integer>, Integer> getProviderQuoteTiers() {
        return providerQuoteTiers;
    }

    public void setProviderQuoteTiers(FXMap<String, FXMap<String, Integer, Integer>, Integer> providerQuoteTiers) {
        this.providerQuoteTiers = providerQuoteTiers;
    }

    public int getProviderQuoteTier(String lpName, String ccyPair) {
        if (providerQuoteTiers == null) {
            return getDefaultProviderQuoteTier();
        } else {
            FXMap<String, Integer, Integer> lpCcyPairMap = providerQuoteTiers.get(lpName);
            if (lpCcyPairMap == null) {
                return getDefaultProviderQuoteTier();
            } else {
                Integer tierValue = lpCcyPairMap.get(ccyPair);
                return tierValue == null ? lpCcyPairMap.getDefaultValue() : tierValue;
            }
        }
    }

    public MultiProviderFilterConfig getMultiProviderFilterConfig() {
        return multiProviderFilterConfig;
    }

    public Double getMinTradeSize(CurrencyPair ccyPair) {
        if(!ISFactory.getInstance().getISMBean().isLiquidityProvisioningEnabled(this.organization.getShortName())) {
            return ISFactory.getInstance().getISMBean().getMinimumTradeSize( this.organization, ccyPair ) ;
        } else {
            LiquidityProvision liqProv = ISUtilImpl.getLiquidityProvision(null, this.organization, ccyPair);
            return liqProv == null ? null : liqProv.getHierarchicalMinMatchSize();
        }
    }

    public String getValueDate(Organization customerOrg, CurrencyPair ccyPair) {
        String valueDate;

        FXRateConvention rateConvention = QuoteConventionUtilC.getInstance().getFXRateConvention( customerOrg );
        valueDate = getValueDate( rateConvention, ccyPair );
        //Get the value date from customer quote convention, if not available use STDQOTCNV
        if (valueDate == null)
        {
            rateConvention = QuoteConventionUtilC.getInstance().getFXRateConvention( ISCommonConstants.QUOTE_CONVENTION );
            valueDate = getValueDate( rateConvention, ccyPair );
        }
        return valueDate;
    }

    private String getValueDate( FXRateConvention rateConvention, CurrencyPair ccyPair )
    {
        String valueDate = null;
        FXRateBasis rateBasis = rateConvention != null ? rateConvention.getFXRateBasis( ccyPair ) : null;
        if ( rateBasis != null )
        {
            valueDate = rateBasis.getSpotDate( EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate() ).getFormattedDate( IdcDate.YYYY_MM_DD );
        }
        return valueDate;
    }

    public Double getMinQuoteSize(CurrencyPair ccyPair) {
        Double result = null;
        if(ISFactory.getInstance().getISMBean().isLiquidityProvisioningEnabled(this.organization.getShortName())) {
            LiquidityProvision liqProv = ISUtilImpl.getLiquidityProvision(null, this.organization, ccyPair);
            result = liqProv == null ? null : liqProv.getHierarchicalMinQuoteSize();
        }
        return result;
    }

    public void addCcyPairTermAggregationRoundingFactor(String ccyPair, Integer roundingFactor) {
    }

    public int getTermAggregationRoundingFactor(String fiOrg, String ccyPair) {
        return AggregationServiceFactory.getInstance().getAggregationMBean().getTermAggregationRoundingFactor(fiOrg, ccyPair);
    }

    public void clearTermAggregationRoundingFactorCache() {
    }

    public void removeCcyPairStalenessCheckInterval(String ccyPair) {
    }

    public int getTermAggregationRoundingFactor() {
        return termAggregationRoundingFactor;
    }

    public void setTermAggregationRoundingFactor(int termAggregationRoundingFactor) {
        this.termAggregationRoundingFactor = termAggregationRoundingFactor;
    }

    public void setTermAggregationDisplayAmtInBase(Boolean termAggregationDisplayAmtInBase) {
        this.isTermAggregationDisplayAmtInBase = termAggregationDisplayAmtInBase;
    }

    public boolean isTermAggregationDisplayAmtInBase() {
        return isTermAggregationDisplayAmtInBase;
    }

    public void setCachedQuoteOnSubscription(Boolean isCachedQuoteOnSubscription) {
        this.isCachedQuoteOnSubscription = isCachedQuoteOnSubscription;
    }

    public boolean isCachedQuoteOnSubscription() {
        return isCachedQuoteOnSubscription;
    }
}


