package com.integral.aggregation;

import com.integral.exception.IdcInvalidArgumentException;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 *
 */
public abstract class RequestBuilder<T extends RequestBuilder<T>> {
    protected Log log = LogFactory.getLog(this.getClass());

    private final Class<T> derived;

    protected RequestBuilder(Class<T> derived) {
        this.derived = derived;
    }

    protected void validateField(Object field, String errMsg) throws IdcInvalidArgumentException {
        if (field == null) throw new IdcInvalidArgumentException(errMsg);
    }
}
