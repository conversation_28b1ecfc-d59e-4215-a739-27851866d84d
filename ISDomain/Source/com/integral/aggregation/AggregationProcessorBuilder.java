package com.integral.aggregation;

import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.config.AggregationServiceMBean;
import com.integral.broker.model.*;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.taker.TakerOrganizationFunction;
import com.integral.user.UserUtilC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 *
 */
public class AggregationProcessorBuilder {
    protected Log log = LogFactory.getLog(this.getClass());
    protected Organization organization;

    public AggregationProcessorBuilder setOrganization(Organization organization) {
        this.organization = organization;
        return this;
    }

    public AggregationProcessor build() {
        //AggregationProcessor processor = new AggregationProcessor(this.organization);
    	AggregationProcessor processor = newAggregationProcessor();
        AggregationProcessorConfig config = processor.getConfig();
        AggregationServiceMBean aggregationServiceMBean = getAggregationServiceMBean();
        User aggregationUser = getAggregationUser(this.organization, aggregationServiceMBean);
        config.setUser(aggregationUser);
        if(aggregationServiceMBean.isAggExecRuleEnabled(this.organization.getShortName())) {
            TakerOrganizationFunction takerOrganizationFunction = this.organization.getTakerOrganizationFunction();
            processor.setTakerOrganizationFunction(takerOrganizationFunction);
            updateRateFilterDefinition(takerOrganizationFunction.getActiveRateFilterDefinition(), config.getMultiProviderFilterConfig());
        } else {
            BrokerOrganizationFunction brokerOrganizationFunction = createBrokerOrgFunction(config);
            processor.setBrokerOrganizationFunction(brokerOrganizationFunction);
        }
        return processor;
    }
    
    protected AggregationServiceMBean getAggregationServiceMBean() {
    	return AggregationServiceFactory.getInstance().getAggregationMBean();
    }
    
    protected AggregationProcessor newAggregationProcessor() {
    	return new AggregationProcessor(this.organization);
    }

    private User getAggregationUser(Organization organization, AggregationServiceMBean aggregationServiceMBean) {
        String userShortName = aggregationServiceMBean.getAggregationUserShortName();
        User user =null;
        if(RuntimeFactory.getServerRuntimeMBean().isTakerOrgDefaultUserCreationEnabled()){
        	  user = UserUtilC.createUser(userShortName, userShortName, organization,UserUtilC.MARKET_MAKER_ROLE_NAME );
        }else{
        	 user = UserUtilC.getUser(userShortName, organization.getNamespace());      
        }
                
      

        if (user == null) {
            StringBuilder sb = new StringBuilder(150)
                .append(this.getClass().getName())
                .append(' ').append("Could not create or get the aggregation user -")
                .append(' ').append(userShortName)
                .append(' ').append("Using default dealing user of")
                .append(' ').append(organization.getShortName())
                .append(' ').append(organization.getDefaultDealingUser().getShortName());

            log.warn(sb.toString());
            user = organization.getDefaultDealingUser();
        }
        return user;
    }

    private BrokerOrganizationFunction createBrokerOrgFunction(AggregationProcessorConfig config) {
        BrokerOrganizationFunction orgFunction = createBrokerOrganizationFunction();
        MultiProviderFilterConfig filterConfig = config.getMultiProviderFilterConfig();

        RateFilterDefinition activeRateFilterDefinition = createRateFilterDefinition(filterConfig);
        orgFunction.setActiveRateFilterDefinition(activeRateFilterDefinition);
        return orgFunction;
    }

    private BrokerOrganizationFunction createBrokerOrganizationFunction() {
        BrokerOrganizationFunction brokerOrganizationFunction = ModelFactory.getInstance().newBrokerOrganizationFunction();
        brokerOrganizationFunction.setOrderExecutionActive( true );
        brokerOrganizationFunction.setPriceMakingActive( true );
        brokerOrganizationFunction.setOrganization( this.organization );
        brokerOrganizationFunction.setDisabledProviders( Collections.<Organization>emptyList() );
        return brokerOrganizationFunction;
    }

    private RateFilterDefinition createRateFilterDefinition(MultiProviderFilterConfig filterConfig) {
        RateFilterDefinition activeRateFilterDefinition = null;
        if (filterConfig.isMultiProviderFilterEnabled()) {
            activeRateFilterDefinition = ModelFactory.getInstance().newRateFilterDefination();

            MultiProviderRateFilterParams rateFilterParams = ModelFactory.getInstance().newMultiProviderRateFilterParams();
            rateFilterParams.setRateFilterDefinition(activeRateFilterDefinition);
            activeRateFilterDefinition.setMultiProviderRateFilterParams(rateFilterParams);

            rateFilterParams.setStalenessCheckEnabled(filterConfig.isStalenessCheckEnabled());
            rateFilterParams.setOffmarketCheckEnabled(filterConfig.isOffMarketCheckEnabled());
            rateFilterParams.setInvertedRateFilterCheckEnabled(filterConfig.isInvertedRateFilterCheckEnabled());
            rateFilterParams.setStalenessCheckInterval(filterConfig.getStalenessCheckInterval());
            rateFilterParams.setOffMarketMaxProviders(filterConfig.getOffMarketMaxProviders());
            rateFilterParams.setOffMarketMinProviders(filterConfig.getOffMarketMinProviders());
            rateFilterParams.setOffMarketMaxInverseSpread(filterConfig.getOffMarketMaxInverseSpread());
            rateFilterParams.setInvertedRateTolerance(filterConfig.getInvertedRateTolerance());

            Map<String, Double> ccyPairMaxInverseSpreads = filterConfig.getCcyPairMaxInverseSpread();
            Map<String, Long> ccyPairStalenessCheckInterval = filterConfig.getCcyPairStalenessCheckInterval();
            Map<String, Double> ccyPairInvertedRateTolerances = filterConfig.getCcyPairInvertedRateTolerance();

            Set<String> ccyPairs = new HashSet<String>(  );
            ccyPairs.addAll( ccyPairMaxInverseSpreads.keySet() );

            for (String ccyPair : ccyPairStalenessCheckInterval.keySet()) {
                if (!ccyPairs.contains(ccyPair)) {
                    ccyPairs.add(ccyPair);
                }
            }

            for (String ccyPair : ccyPairInvertedRateTolerances.keySet()) {
                if (!ccyPairs.contains(ccyPair)) {
                    ccyPairs.add(ccyPair);
                }
            }

            int sortOrder = 0;
            for (String ccyPair : ccyPairs) {
                Double spread = ccyPairMaxInverseSpreads.get(ccyPair);
                Long interval = ccyPairStalenessCheckInterval.get(ccyPair);
                Double tolerance = ccyPairInvertedRateTolerances.get(ccyPair);

                MultiProviderRateFilterCcyPairParams ccyPairRateFilterParams = ModelFactory.getInstance().newMultiProviderRateFilterCcyPairParams();
                ccyPairRateFilterParams.setMultiProviderRateFilterParams(rateFilterParams);
                CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(ccyPair);
                if(currencyPair != null) {
                    ccyPairRateFilterParams.setBaseCurrency(currencyPair.getBaseCurrency());
                    ccyPairRateFilterParams.setVariableCurrency(currencyPair.getVariableCurrency());
                }
                ccyPairRateFilterParams.setSortOrder( sortOrder++ );

                rateFilterParams.addMultiProviderRateFilterCcyPairParams(ccyPairRateFilterParams);

                if (spread != null) {
                    ccyPairRateFilterParams.setOffMarketMaxInverseSpread(spread);
                }

                if (interval != null) {
                    ccyPairRateFilterParams.setStalenessCheckInterval(interval);
                }

                if(tolerance != null) {
                    ccyPairRateFilterParams.setInvertedRateTolerance(tolerance);
                }


            }
        }
        return activeRateFilterDefinition;
    }

    private void updateRateFilterDefinition(RateFilterDefinition activeRateFilterDefinition, MultiProviderFilterConfig filterConfig) {
        if (filterConfig.isMultiProviderFilterEnabled()) {
            MultiProviderRateFilterParams rateFilterParams = activeRateFilterDefinition.getMultiProviderRateFilterParams();
            rateFilterParams.setOffmarketCheckEnabled(filterConfig.isOffMarketCheckEnabled());
            rateFilterParams.setOffMarketMaxProviders(filterConfig.getOffMarketMaxProviders());
            rateFilterParams.setOffMarketMinProviders(filterConfig.getOffMarketMinProviders());
            rateFilterParams.setOffMarketMaxInverseSpread(filterConfig.getOffMarketMaxInverseSpread());
            rateFilterParams.setInvertedRateTolerance(filterConfig.getInvertedRateTolerance());

            Map<String, Double> ccyPairMaxInverseSpreads = filterConfig.getCcyPairMaxInverseSpread();
            Map<String, Double> ccyPairInvertedRateTolerances = filterConfig.getCcyPairInvertedRateTolerance();

            Set<String> ccyPairs = new HashSet<String>(  );
            ccyPairs.addAll( ccyPairMaxInverseSpreads.keySet() );


            for (String ccyPair : ccyPairInvertedRateTolerances.keySet()) {
                if (!ccyPairs.contains(ccyPair)) {
                    ccyPairs.add(ccyPair);
                }
            }

            int sortOrder = 0;
            for (String ccyPair : ccyPairs) {
                Double spread = ccyPairMaxInverseSpreads.get(ccyPair);
                Double tolerance = ccyPairInvertedRateTolerances.get(ccyPair);

                MultiProviderRateFilterCcyPairParams ccyPairRateFilterParams = rateFilterParams.getMultiProviderRateFilterCcyPairParams(ccyPair);
                if(ccyPairRateFilterParams != null) {
                    ccyPairRateFilterParams.setSortOrder( sortOrder++ );
                    if (spread != null) {
                        ccyPairRateFilterParams.setOffMarketMaxInverseSpread(spread);
                    }

                    if(tolerance != null) {
                        ccyPairRateFilterParams.setInvertedRateTolerance(tolerance);
                    }
                }
            }
        }
    }
}
