package com.integral.aggregation.log;

import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.config.AggregationServiceMBean;
import com.integral.aggregation.price.FXPrice;
import com.integral.aggregation.stream.ESPAggregator;
import com.integral.broker.aggregate.ProxyQuoteAggregatorC;
import com.integral.broker.aggregate.QuoteAggregator;
import com.integral.broker.aggregate.QuoteAggregatorFactory;
import com.integral.broker.log.BrokerLoggerC;
import com.integral.broker.model.Product;
import com.integral.broker.util.appender.AppenderHelper;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.instrument.Instrument;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.log.LoggerUtilC;
import com.integral.user.Organization;
import com.integral.util.Tuple;

import java.text.DateFormat;
import java.util.List;

/**
 *
 */
public class ASAggregationLoggerC implements ASAggregationLogger {
    protected Log log;
    public static final AppenderHelper HELPER = AppenderHelper.SINGLETON;
    protected Organization fiOrg;
    private ThreadLocal<StringBuilder> localBuilder = BrokerLoggerC.getLocalBuilder();

    // SimpleDateFormat is not a thread safe. It starts giving error if the same instance is shared across many threads.
    // Maintaining the SimpleDateFormat at the thread level. http://bugs.sun.com/bugdatabase/view_bug.do?bug_id=6231579
    final static ThreadLocal<DateFormat> localDateFormat = new ThreadLocal<DateFormat>();
    public static final char deLimiter = ';';
    private QuoteAggregatorFactory factory;
    private static int maxTiersToLog = 5;

    public ASAggregationLoggerC() {
        this.log = LogFactory.getLog(this.getClass());
    }

    public ASAggregationLoggerC(ESPAggregator espAggregator) {        
    	this.log = LogFactory.getLog(this.getClass());
        this.fiOrg = espAggregator.getAggregationProcessor().getFiOrg();       
        String logFileName = getLogFileName(this.fiOrg);        
        boolean isAggregationGzipEnabled = getAggregationServiceMBean().isAggLogCompressionEnabled();
        this.log = LoggerUtilC.getInstance().createSpecializedLog(this.log, logFileName, isAggregationGzipEnabled);
        factory = espAggregator.getAggregatorFactory();
        maxTiersToLog = getAggregationServiceMBean().getMaxTiersToLog();
    }
    
    protected String getLogFileName(final Organization org) {
    	return org.getShortName();
    }
    
    protected AggregationServiceMBean getAggregationServiceMBean() {
    	return AggregationServiceFactory.getInstance().getAggregationMBean();
    }

    @Override
    public void reset() {
        if(localBuilder.get() != null) {
            localBuilder.get().setLength(0);
        }
    }

    public void logBook(List<Quote> quotes, Product product) {

        if (log.isDebugEnabled()) {
            StringBuilder sb = createLocalStringBuilder(quotes.size() * 100);
            sb.append(product.getCurrencyPair().getName());
            for (Quote quote : quotes) {
                sb.append(' ').append(BOOK_CODE).append(' ');
                appendQuoteDetails(true, quote, sb);
            }
        } else if (log.isInfoEnabled()) {
            StringBuilder sb = createLocalStringBuilder(quotes.size() * 100);
            sb.append(product.getCurrencyPair().getName());
            for (Quote quote : quotes) {
                sb.append(' ').append(BOOK_CODE).append(' ');
                appendQuoteDetails(false, quote, sb);
            }
        }
    }

    public void logProviderPrice(FXLegDealingPrice dp, double amount, Instrument ccy, String stream) {
        if (log.isDebugEnabled()) {
            StringBuilder sb = getLocalStringBuilder(300);
            sb.append(' ');

            switch (dp.getBidOfferMode()) {
                case FXLegDealingPrice.BID:
                    sb.append("PB ");
                    break;
                case FXLegDealingPrice.OFFER:
                    sb.append("PO ");
                    break;
            }

            Quote quote = dp.getQuote();
            sb.append( quote.getOrganization().getShortName()).append(' ');
            sb.append(quote.getCurrencyPair().getName()).append(' ');
            sb.append(dp.getSpotRate()).append(' ');
            sb.append(amount).append(' ');
            sb.append(ccy).append(' ');
            sb.append(stream).append(' ');
            sb.append(quote.getPriceType() == Quote.PRICE_TYPE_MULTI_TIER ? "MT": (quote.getPriceType() == Quote.PRICE_TYPE_QUOTES ? "MQ" : "DO"));
            sb.append(' ');
        }
    }

    public void logAggregatedRate(FXPriceBook priceBook, Product product, String aggregatorId, boolean isBestBidOfferUpdated) {
        if (log.isDebugEnabled()) {
            StringBuilder sb = getLocalStringBuilder((priceBook.getBids().size() + priceBook.getOffers().size()) * 15);
            QuoteAggregator aggregator = product.getConfiguration().getQuoteAggregator();
            sb.append(' ').append('(');
            sb.append(aggregator instanceof ProxyQuoteAggregatorC ?
                    ((ProxyQuoteAggregatorC) aggregator).getTarget().getAbbreviatedName() : aggregator.getAbbreviatedName());
            if(factory != null && factory.isHourglass()) sb.append(" H");
            sb.append(')');
            sb.append(' ').append('(');
            sb.append(aggregatorId);
            sb.append(')');
            sb.append(product.getConfiguration().getCurrencyPairGroup().getFXRateConvention().getFullyQualifiedName());
            sb.append(' ').append(isBestBidOfferUpdated ? BEST_UPDATE : FULL_UPDATE);
            appendPriceBookDetails(true, priceBook, sb);
        } else if (log.isInfoEnabled()) {
            StringBuilder sb = getLocalStringBuilder((priceBook.getBids().size() + priceBook.getOffers().size()) * 15);
            QuoteAggregator aggregator = product.getConfiguration().getQuoteAggregator();
            sb.append(' ').append('(');
            sb.append(aggregator instanceof ProxyQuoteAggregatorC ?
                    ((ProxyQuoteAggregatorC) aggregator).getTarget().getAbbreviatedName() : aggregator.getAbbreviatedName());
            if(factory != null && factory.isHourglass()) sb.append(" H");
            sb.append(')');
            sb.append(' ').append('(');
            sb.append(aggregatorId);
            sb.append(')').append(' ');
            sb.append(product.getConfiguration().getCurrencyPairGroup().getFXRateConvention().getFullyQualifiedName());
            sb.append(' ').append(isBestBidOfferUpdated ? BEST_UPDATE : FULL_UPDATE);
            appendPriceBookDetails(false, priceBook, sb);
        }
    }

    public void logSkippedRate(FXPriceBook lastPriceBook, FXPriceBook priceBook, Product product) {
        if (log.isInfoEnabled()) {
            StringBuilder sb = getLocalStringBuilder((priceBook.getBids().size() + priceBook.getOffers().size()) * 15);
            sb.append(SKP_RATE_FIELD);
            sb.append(' ').append(lastPriceBook.getBookId()).append(' ');
            sb.append(' ').append(priceBook.getBookId()).append(' ');
        }
    }

    @Override
    public boolean isDebugEnabled() {
        return log.isDebugEnabled();
    }

    @Override
    public boolean isInfoEnabled() {
        return log.isInfoEnabled();
    }

    public void logData() {

        if (log.isDebugEnabled()) {
            StringBuilder sb = getLocalStringBuilder(0);
            log.debug(sb.toString());
        } else if (log.isInfoEnabled()) {
            StringBuilder sb = getLocalStringBuilder(0);
            log.info(sb.toString());
        }
    }

    protected void appendQuoteDetails(boolean isDebug, Quote quote, StringBuilder sb) {
        sb.append(' ').append(quote.getOrganization().getShortName());

        if (isDebug) {
            sb.append(' ').append(quote.getGUID());
            sb.append(' ').append(quote.getRateId());
            sb.append(' ').append(quote.hashCode());
        }

        sb.append(' ').append(quote.getStream() == null ? "" : quote.getStream()); // this is provider stream on the quote
        sb.append(' ').append(quote.getCurrencyPair().getName());

        if (isDebug) {
            DateFormat dateFormat = getDateFormat();
            String valueDate = quote.getValueDate() != null ? dateFormat.format(quote.getValueDate().asJdkDate()) : "NA";

            sb.append(' ').append(quote.getQuoteClassification().getName());
            sb.append(' ').append(valueDate);

        }

        FXLegDealingPrice dp;
        List<FXLegDealingPrice> bids = quote.getBids();
        List<FXLegDealingPrice> offers = quote.getOffers();
        for (int i = 0; i < bids.size(); i++) {
            FXLegDealingPrice bid = bids.get(i);

            sb.append(' ').append('0');
            sb.append(' ');
            HELPER.append(sb, bid.getSpotRate());
            sb.append(' ');
            HELPER.append(sb, bid.getDealtAmount());
            if (isDebug) {
                sb.append(' ').append(bid.getName());
            }
            FXLegDealingPrice offer = offers.get(i);
            sb.append(' ').append('2');
            sb.append(' ');
            HELPER.append(sb, offer.getSpotRate());
            sb.append(' ');
            HELPER.append(sb, offer.getDealtAmount());

            if (isDebug) {
                sb.append(' ').append(offer.getName());
            }

        }
        if (!isDebug) sb.append(' ').append(quote.getSequenceNumberAsString());
        sb.append(' ').append(quote.getPriceType() == Quote.PRICE_TYPE_MULTI_TIER ? "MT" : (quote.getPriceType() == Quote.PRICE_TYPE_QUOTES ? "MQ" : "DO")).append(' ');
        if (isDebug) sb.append(' ').append(time(quote)).append(' ');
    }

    protected void appendPriceBookDetails(boolean isDebug, FXPriceBook book, StringBuilder sb) {
        sb.append(' ').append(this.fiOrg.getShortName());
        sb.append(' ').append(book.getBookId());
        sb.append(' ').append(book.getCurrencyPair().getName());

        int size = book.getBids().size();
        if(!isDebug) {
            if(size > maxTiersToLog) size = maxTiersToLog;
        }
        for (int i = 0; i < size; i++) {
            FXPrice price = book.getBid(i);
            sb.append(' ').append('0');
            sb.append(' ');
            HELPER.append(sb, price.getRate());
            appendNonSpotPriceDateails(price, sb);
            sb.append(' ');
            HELPER.append(sb, price.getLimit());
            sb.append(' ');
            HELPER.append(sb, price.getLPName());
        }

        size = book.getOffers().size();
        if(!isDebug) {
            if(size > maxTiersToLog) size = maxTiersToLog;
        }
        for (int i = 0; i < size; i++) {
            FXPrice price = book.getOffer(i);
            sb.append(' ').append('2');
            sb.append(' ');
            HELPER.append(sb, price.getRate());
            appendNonSpotPriceDateails(price, sb);
            sb.append(' ');
            HELPER.append(sb, price.getLimit());
            sb.append(' ');
            HELPER.append(sb, price.getLPName());
        }
    }
    
    protected void appendNonSpotPriceDateails(FXPrice price, StringBuilder sb) {
    	// Do nothing 
    }

    public StringBuilder createLocalStringBuilder(int size) {
        if (localBuilder.get() == null) {
            StringBuilder sb = new StringBuilder(size);
            localBuilder.set(sb);
        } else {
            localBuilder.get().setLength(0);
        }
        return localBuilder.get();
    }

    public StringBuilder getLocalStringBuilder(int size) {
        if (localBuilder.get() == null) {
            createLocalStringBuilder(size);
        }
        return localBuilder.get();
    }

    protected long time(Quote quote) {
        long time = quote.getEventTimes().quoteCreatedByApp;
        if (time <= 0) {
            log.warn(new StringBuilder().append(this).append("time: ").append(quote.getGUID()).append(" ")
                    .append(time).toString());
        }
        return time;
    }

    public DateFormat getDateFormat() {
        DateFormat dateformat = localDateFormat.get();
        if (dateformat == null) {
            dateformat = DateFormat.getDateInstance(DateFormat.SHORT);
            localDateFormat.set(dateformat);
        }
        return dateformat;
    }

    public void setFactory(QuoteAggregatorFactory value){
        this.factory = value;
    }

    public void logSkewValues(Tuple<Double, Double>[] values){
        // do nothing
    }
}
