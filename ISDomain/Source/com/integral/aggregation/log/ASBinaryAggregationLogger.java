package com.integral.aggregation.log;

import java.nio.ByteBuffer;
import java.text.DateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.integral.util.Tuple;
import org.slf4j.Logger;

import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.config.AggregationServiceMBean;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.stream.ESPAggregator;
import com.integral.aggregation.util.AggLogStatsC;
import com.integral.blog.BlogUtil;
import com.integral.blog.entries.aggregation.AggServiceLogEventHandler;
import com.integral.blog.entries.aggregation.AggregationServiceLogEvent;
import com.integral.blog.entries.aggregation.LogAggregationEntryV1DataGenerator;
import com.integral.blog.entries.aggregation.disruptor.AggregationServiceLoggerDisruptorWrapper;
import com.integral.broker.model.Product;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.instrument.Instrument;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.Organization;


/*8
 * 
 * LogAggregationEntryV1DataGenerator
LogAggregationEntryV1Reader
LogAggregationEntryV1
ASBinaryAggregationLogger
AggregationServiceLogEvent
LogAggregationEntryV1Writer

 <appender name="FILE" class="ch.qos.logback.core.FileAppender">
    <file>testFile.log</file>
    <append>true</append>
    <!-- encoders are assigned the type
         ch.qos.logback.classic.encoder.PatternLayoutEncoder by default -->
    <encoder>
      <pattern>%-
      

 */
public class ASBinaryAggregationLogger implements ASAggregationLogger {
    private Organization fiOrg_;

    private static final DateFormat dateFormat_ = DateFormat.getDateInstance(DateFormat.SHORT);

    private static final Log log = LogFactory.getLog(ASBinaryAggregationLogger.class);

    /**
     * Binary logger using logback
     */
    private final org.slf4j.Logger logger_;


    public static Map<Integer, AggregationServiceLoggerDisruptorWrapper> hashVsAggLogDisruptorMap = null;


    private static int healthCheckSequenceForLogging = 0;

    public static ConcurrentHashMap<String, Logger> loggers;

    private static int numberOfAggLoggingDisruptors = 0;

    private static int maxAggPriceTierToLog = 5;


    public void addLoggerForFI(String fiName, Logger logger) {
        loggers.put(fiName, logger);
    }

    public Logger getLoggerForFI(String fiName) {
        Logger logger = loggers.get(fiName);
        return logger;
    }

   /*private void sendLogWriterToDisruptor(String fiOrg, byte[] aggLogByteArray) {	   
	   long seqNum = aggregationServiceLoggerDisruptorWrapper.getNextSequenceNumberForProducer();
	   AggregationServiceLogEvent aggregationServiceLogEvent =  aggregationServiceLoggerDisruptorWrapper.getTradeEventForSeqNum(seqNum);         
	   aggregationServiceLogEvent.setFiOrg(fiOrg);
	   aggregationServiceLogEvent.setAggLogByteArray(aggLogByteArray);      
	   aggregationServiceLoggerDisruptorWrapper.publishSeqNumToDisruptor(seqNum);
   }*/

    protected static String metaDataDirectory = null;

    static {
        loggers = new ConcurrentHashMap<String, Logger>();
        hashVsAggLogDisruptorMap = new HashMap<Integer, AggregationServiceLoggerDisruptorWrapper>();

        metaDataDirectory = BlogUtil.getDirectoryForAppender(ASBinaryAggregationLogger.class.getName());

        numberOfAggLoggingDisruptors = AggregationServiceFactory.getInstance().getAggregationMBean().getAggLogDisruptorsCount();

        for (int i = 0; i < numberOfAggLoggingDisruptors; i++) {
            AggregationServiceLoggerDisruptorWrapper aggregationServiceLoggerDisruptorWrapper = new AggregationServiceLoggerDisruptorWrapper(i);
            AggServiceLogEventHandler aggServiceLogEventHandler = new AggServiceLogEventHandler(aggregationServiceLoggerDisruptorWrapper);
            aggregationServiceLoggerDisruptorWrapper.handleEvents(AggServiceLogEventHandler.AGG_SERVICE_LOG_EVENT_HANDLER, aggServiceLogEventHandler);
            hashVsAggLogDisruptorMap.put(i, aggregationServiceLoggerDisruptorWrapper);
            aggregationServiceLoggerDisruptorWrapper.startTheDisruptor();
        }
        healthCheckSequenceForLogging = AggregationServiceFactory.getInstance().getAggregationMBean().getBinaryLoggingDisruptorHealthCheckEventsCount();
        maxAggPriceTierToLog = AggregationServiceFactory.getInstance().getAggregationMBean().getMaxTiersToLog();
    }


    /**
     * Holder for current log entry.
     */
    private LogAggregationEntryV1DataGenerator logEntry_ = null;


    public ASBinaryAggregationLogger(ESPAggregator espAggregator) {
        logEntry_ = createAggregationEntryataGenerator();
        fiOrg_ = espAggregator.getAggregationProcessor().getFiOrg();
        String fiName = fiOrg_.getShortName();
        //String logFileName = fiName;
        String logFileName = getLogFileName(fiOrg_);
        org.slf4j.Logger i = getLoggerForFI(logFileName);
        if (i == null) {
            // logger_ = BlogUtil.getLogger(ASBinaryAggregationLogger.class.getName(), "aggregation_" + logFileName);
            logger_ = BlogUtil.getAggServiceLogger(ASBinaryAggregationLogger.class.getName(), "aggregation_" + logFileName);
            addLoggerForFI(logFileName, logger_);
            AggLogStatsC.getInstance().incrementCountOfOrganizations();

            AggregationServiceLoggerDisruptorWrapper aggregationServiceLoggerDisruptorWrapper = getDisruptorForFI(fiName);
            aggregationServiceLoggerDisruptorWrapper.addOrgToTheDirsuptorData(fiName);

        } else {
            logger_ = i;
        }

    }

    protected LogAggregationEntryV1DataGenerator createAggregationEntryataGenerator() {
        return new LogAggregationEntryV1DataGenerator(metaDataDirectory);
    }

    protected String getLogFileName(final Organization org) {
        return org.getShortName();
    }

    protected AggregationServiceMBean getAggregationServiceMBean() {
        return AggregationServiceFactory.getInstance().getAggregationMBean();
    }

    @Override
    public void reset() {
        logEntry_.reset();
    }

    private AggregationServiceLoggerDisruptorWrapper getDisruptorForFI(String fiName) {
        int hashCodeOfOrg = Math.abs(fiName.hashCode());
        int disruptorNumber = hashCodeOfOrg % numberOfAggLoggingDisruptors;
        AggregationServiceLoggerDisruptorWrapper aggregationServiceLoggerDisruptorWrapper = hashVsAggLogDisruptorMap.get(disruptorNumber);
        return aggregationServiceLoggerDisruptorWrapper;
    }

    @Override
    public void logData() {
        ByteBuffer byteBuffer = null;
        String orgName = fiOrg_.getShortName();
        AggregationServiceLoggerDisruptorWrapper aggregationServiceLoggerDisruptorWrapper = getDisruptorForFI(orgName);
        long seqNum = aggregationServiceLoggerDisruptorWrapper.getNextSequenceNumberForProducer();
        if (seqNum != -1) {
            try {
                AggregationServiceLogEvent disruptorEvent = aggregationServiceLoggerDisruptorWrapper.getTradeEventForSeqNum(seqNum);
                AggregationServiceLogEvent aggregationServiceLogEvent = (AggregationServiceLogEvent) disruptorEvent;
                byteBuffer = aggregationServiceLogEvent.getByteBuffer();
                byteBuffer.clear();
                aggregationServiceLogEvent.setFiOrg(orgName);
                aggregationServiceLogEvent.setTimeInMills(System.currentTimeMillis());
                boolean logBytesCreationTime = false;
                if (AggregationServiceFactory.getInstance().getAggregationMBean().shouldLogAggServiceBinaryLoggingByteCreationTime()) {
                    if (seqNum % healthCheckSequenceForLogging == 0) {
                        logBytesCreationTime = true;
                    }
                }
                logEntry_.populateByteBuffer(byteBuffer, logBytesCreationTime);
            } catch (Exception e) {
                log.error("EXCEPTION_WHILE_GENERATING_AGGREGATION_LOG_DATA ", e);
                e.printStackTrace();
                if (byteBuffer != null) {
                    byteBuffer.clear();
                }
                AggLogStatsC.getInstance().incrementExceptionCountOnProducer();
            } finally {
                aggregationServiceLoggerDisruptorWrapper.publishSeqNumToDisruptor(seqNum);
            }
        } else {
            log.warn("DROPPING_AGG_LOG_ENTRY_FOR_ORG=" + fiOrg_.getShortName());
            AggLogStatsC.getInstance().incrementNumberOfEventsDropped();
        }
    }


    @Override
    public void logProviderPrice(FXLegDealingPrice dp, double amount, Instrument ccy, String stream) {
        if (logger_.isDebugEnabled()) {
            logEntry_.logProviderPrice(dp, amount, ccy, stream);
        }
    }

    @Override
    public void logBook(List<Quote> quotes, Product product) {
        logEntry_.logBook(logger_.isDebugEnabled(), quotes, product, dateFormat_);
    }

    @Override
    public void logAggregatedRate(FXPriceBook priceBook, Product product, String aggregatorId, boolean isBestBidOfferUpdated) {
        logEntry_.logAggregatedRate(fiOrg_.getShortName(), priceBook, product, aggregatorId, isBestBidOfferUpdated, maxAggPriceTierToLog, logger_.isDebugEnabled());
    }

    @Override
    public void logSkippedRate(FXPriceBook lastPriceBook, FXPriceBook priceBook, Product product) {
        if (logger_.isInfoEnabled()) {
            logEntry_.logSkippedRate(lastPriceBook, priceBook);
        }
    }

    @Override
    public boolean isDebugEnabled() {
        return logger_.isDebugEnabled();
    }

    @Override
    public boolean isInfoEnabled() {
        return logger_.isInfoEnabled();
    }

    public void logSkewValues(Tuple<Double, Double>[] values){
        // do nothing
    }
}
