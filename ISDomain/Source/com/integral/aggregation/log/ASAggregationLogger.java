package com.integral.aggregation.log;

import com.integral.aggregation.price.FXPriceBook;
import com.integral.broker.aggregate.log.AggregationLogger;
import com.integral.broker.model.Product;
import com.integral.finance.dealing.Quote;

import java.util.List;

/**
 *
 *
 */
public interface ASAggregationLogger extends AggregationLogger {
    public static final String BEST_UPDATE = "Best";
    public static final String FULL_UPDATE = "Full";
    public static final String STALE_RATE = "STALE";
    public static final String TRADEABLE_RATE = "TRADEABLE";
    public static final String BOOK_CODE = "Bk";
    public static final String SKP_RATE_FIELD = "SkpdRate";

    public void logBook(List<Quote> quotes, Product product);

    public void logAggregatedRate(FXPriceBook priceBook, Product product, String aggregatorId, boolean isBestBidOfferUpdated);

    public void logSkippedRate(FXPriceBook lastPriceBook, FXPriceBook priceBook, Product product);

    public boolean isDebugEnabled();

    public boolean isInfoEnabled();
}
