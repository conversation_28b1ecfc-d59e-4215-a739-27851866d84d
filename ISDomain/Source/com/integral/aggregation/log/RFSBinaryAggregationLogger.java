package com.integral.aggregation.log;

import com.integral.aggregation.stream.ESPAggregator;
import com.integral.user.Organization;
import com.integral.util.Tuple;

public class RFSBinaryAggregationLogger extends ASBinaryAggregationLogger {
	private static final String RFS = "RFS-";
	private static final String NEAR_LEG = "NearLeg";
	private static final String FAR_LEG = "FarLeg";
	public RFSBinaryAggregationLogger(ESPAggregator espAggregator) {
		super(espAggregator);
		
	}
	
	@Override
	protected String getLogFileName(Organization org) {
		
		return RFS + org.getShortName();
	}

	@Override
	public void logSkewValues(Tuple<Double, Double>[] values){
		//do nothing
	}

}
