package com.integral.aggregation.log;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.util.Collection;

import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.config.AggregationServiceMBean;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.price.SwapFXPriceBook;
import com.integral.aggregation.stream.ESPAggregator;
import com.integral.broker.aggregate.ProxyQuoteAggregatorC;
import com.integral.broker.aggregate.QuoteAggregator;
import com.integral.broker.model.Product;
import com.integral.finance.dealing.DealingPriceElement;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.FXRate;
import com.integral.finance.price.Price;
import com.integral.finance.price.fx.FXPrice;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.util.Tuple;

public class RFSAggregationLoggerC extends ASAggregationLoggerC {
	private static final String RFS = "RFS-";
	private static final String NEAR_LEG = "NearLeg";
	private static final String FAR_LEG = "FarLeg";

	public RFSAggregationLoggerC() {
		super();		
	}

	public RFSAggregationLoggerC(ESPAggregator espAggregator) {
		super(espAggregator);
		
	}

	@Override
	protected String getLogFileName(Organization org) {
		
		return RFS + org.getShortName();
	}

	@Override
	protected AggregationServiceMBean getAggregationServiceMBean() {		
		return AggregationServiceFactory.getInstance().getRFSAggregationMBean();
	}

	@Override
	protected void appendQuoteDetails(boolean isDebug, Quote quote, StringBuilder sb) {
		sb.append(' ').append(quote.getOrganization().getShortName());

		if (isDebug) {
			sb.append(' ').append(quote.getGUID());
			sb.append(' ').append(quote.getRateId());
			sb.append(' ').append(quote.hashCode());
		}

		sb.append(' ').append(
				quote.getStream() == null ? "" : quote.getStream()); // provider stream on quote
		sb.append(' ').append(quote.getCurrencyPair().getName());

		if (isDebug) {
			sb.append(' ').append(quote.getQuoteClassification().getName());
		}
		Collection<?> quotePrices = quote.getQuotePrices();
		if (quotePrices != null) {
			for (Object dealingPrice : quotePrices) {
				if (dealingPrice instanceof FXLegDealingPrice) {
					FXLegDealingPrice fxLegDealingPrice = (FXLegDealingPrice)dealingPrice;
					String legName = fxLegDealingPrice.getName();
					IdcDate valDate = fxLegDealingPrice.getValueDate();
					DateFormat dateFormat = getDateFormat();
					String valueDate = valDate != null ? dateFormat
											.format(valDate.asJdkDate()) : "NA";
					double dealtAmount = fxLegDealingPrice.getDealtAmount();
					DealingPriceElement dealingPriceElement = fxLegDealingPrice.getPriceElement();
					if (dealingPriceElement != null) {
						Price price = dealingPriceElement.getPrice();
						if (price instanceof FXPrice) {
							FXPrice fxPrice = (FXPrice)price;
							FXRate bidFXRate = fxPrice.getBidFXRate();
							FXRate offerFXRate = fxPrice.getOfferFXRate();
							FXRate midFXRate = fxPrice.getMidFXRate();
							if (bidFXRate != null) {
					            sb.append(' ').append('0');
					            sb.append(' ');
					            HELPER.append(sb, bidFXRate.getRate());
					            sb.append(' ');
					            HELPER.append(sb, bidFXRate.getSpotRate());	
					            sb.append(' ');
					            HELPER.append(sb, bidFXRate.getForwardPoints());
							}
							if (midFXRate != null) {
					            sb.append(' ').append('1');
					            sb.append(' ');
					            HELPER.append(sb, midFXRate.getRate());				        
					            sb.append(' ');
					            HELPER.append(sb, midFXRate.getSpotRate());		
					            sb.append(' ');
					            HELPER.append(sb, midFXRate.getForwardPoints());	
							}
							if (offerFXRate != null) {
					            sb.append(' ').append('2');
					            sb.append(' ');
					            HELPER.append(sb, offerFXRate.getRate());
					            sb.append(' ');					         
					            HELPER.append(sb, offerFXRate.getSpotRate());		
					            sb.append(' ');
					            HELPER.append(sb, offerFXRate.getForwardPoints());	
							}
							//sb.append(' ').append(((FXPrice)price).getToString());
						}
					}	
		            sb.append(' ');
		            HELPER.append(sb, dealtAmount);
		            sb.append(' ');
		            HELPER.append(sb, valueDate);
					sb.append(' ').append(legName);
				}
			}

		}

		if (!isDebug) {
			sb.append(' ').append(quote.getSequenceNumberAsString());
		}			
		sb.append(' ')
				.append(Quote.PRICE_TYPE_MULTI_TIER == quote.getPriceType() ? "MT"
						: (Quote.PRICE_TYPE_QUOTES == quote.getPriceType() ? "MQ"
								: "DO")).append(' ');
		if (isDebug)
			sb.append(' ').append(time(quote)).append(' ');
	}

	@Override
	protected long time(Quote quote) {
		Timestamp timeStamp = quote.getCreatedTimestamp();
		if (timeStamp != null) {
			return timeStamp.getTime();
		}
		return -1;
	}

	@Override
	public void logAggregatedRate(FXPriceBook priceBook, Product product, String aggregatorId, boolean isBestBidOfferUpdated) {
        StringBuilder sb = getLocalStringBuilder((priceBook.getBids().size() + priceBook.getOffers().size()) * 15);
        QuoteAggregator aggregator = product.getConfiguration().getQuoteAggregator();
        sb.append(' ').append('(');
        sb.append(aggregator instanceof ProxyQuoteAggregatorC ?
                ((ProxyQuoteAggregatorC) aggregator).getTarget().getAbbreviatedName() : aggregator.getAbbreviatedName());
        sb.append(')');
        sb.append(' ').append('(');
        sb.append(aggregatorId);
        sb.append(')');
        sb.append(product.getConfiguration().getCurrencyPairGroup().getFXRateConvention().getFullyQualifiedName());
        sb.append(' ').append(isBestBidOfferUpdated ? BEST_UPDATE : FULL_UPDATE);

        if (log.isDebugEnabled()) {
            appendPriceBookDetails(true, priceBook, sb);
        } else if (log.isInfoEnabled()) {
            appendPriceBookDetails(false, priceBook, sb);
        }
	}

	@Override
	protected void appendPriceBookDetails(boolean isDebug, FXPriceBook book, StringBuilder sb) {
		if (book instanceof SwapFXPriceBook) {
			sb.append(NEAR_LEG);
			super.appendPriceBookDetails(isDebug, book, sb);
			sb.append(' ').append(FAR_LEG);
			appendFarLegPriceBookDetails(isDebug, (SwapFXPriceBook)book, sb);
		} else {
			super.appendPriceBookDetails(isDebug, book, sb);
		}
	}
	
	private void appendFarLegPriceBookDetails(boolean isDebug, SwapFXPriceBook book, StringBuilder sb) {
		
		Collection<com.integral.aggregation.price.FXPrice> farBids = book.getFarBids();
		for (com.integral.aggregation.price.FXPrice price : farBids) {
			sb.append(' ').append('0');
			sb.append(' ');
			HELPER.append(sb, price.getRate());
			appendNonSpotPriceDateails(price, sb);
			sb.append(' ');
			HELPER.append(sb, price.getLimit());
			sb.append(' ');
			HELPER.append(sb, price.getLPName());
		}

		Collection<com.integral.aggregation.price.FXPrice> farOffers = book.getFarOffers();
		for (com.integral.aggregation.price.FXPrice price : farOffers) {			
			sb.append(' ').append('2');
			sb.append(' ');
			HELPER.append(sb, price.getRate());
			appendNonSpotPriceDateails(price, sb);
			sb.append(' ');
			HELPER.append(sb, price.getLimit());
			sb.append(' ');
			HELPER.append(sb, price.getLPName());
		}
	}

	@Override
	protected void appendNonSpotPriceDateails(com.integral.aggregation.price.FXPrice price, StringBuilder sb) {
		sb.append(' ');
		HELPER.append(sb, price.getSpotRate());
		sb.append(' ');
		HELPER.append(sb, price.getForwardPoint());
	}

	@Override
	public void logSkippedRate(FXPriceBook lastPriceBook, FXPriceBook priceBook, Product product) {		
		super.logSkippedRate(lastPriceBook, priceBook, product);
	}

	@Override
	public void logSkewValues(Tuple<Double, Double>[] values){
		// do nothing
	}
	
}
