package com.integral.aggregation.rfs.metrics;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentMap;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.AggregationProcessorManager;
import com.integral.aggregation.stream.Aggregator;
import com.integral.aggregation.stream.metrics.AggregatorMetrics;
import com.integral.aggregation.stream.metrics.GlobalAggregatorMetrics;
import com.integral.finance.currency.CurrencyPair;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.pipeline.metrics.MetricsProxy;
import com.integral.pipeline.metrics.VolatileMetrics;
import com.integral.user.Organization;
import com.integral.util.Metrics;

public class GlobalRFSAggregatorMetrics implements VolatileMetrics, MetricsProxy {
    protected Log log = LogFactory.getLog(this.getClass());
    private static GlobalRFSAggregatorMetrics instance = new GlobalRFSAggregatorMetrics();

    public static Log lAggregator;
    public static Log lTotalCcyp;
    public static Log lTotalOrg;
    public static Log lTotalGrand;

    private GlobalRFSAggregatorMetrics() {
        lAggregator = LogFactory.getLog(MetricsManager.METRICS_CATEGORY + ".aggregator");
        lTotalCcyp = LogFactory.getLog(MetricsManager.METRICS_CATEGORY + ".aggregator.totals.ccyPair");
        lTotalOrg = LogFactory.getLog(MetricsManager.METRICS_CATEGORY + ".aggregator.totals.organization");
        lTotalGrand = LogFactory.getLog(MetricsManager.METRICS_CATEGORY + ".aggregator.totals.grand");
    }

    public static GlobalRFSAggregatorMetrics getInstance() {
         return instance;
    }

    public StringBuilder report() {
        StringBuilder rsb = new StringBuilder();
        try {
            long ms0 = System.currentTimeMillis();
            boolean logAggregator = lAggregator != null && lAggregator.isInfoEnabled();
            boolean logTotalCcyp = lTotalCcyp != null && lTotalCcyp.isInfoEnabled();
            boolean logTotalOrg = lTotalOrg != null && lTotalOrg.isInfoEnabled();
            boolean logTotalGrand = lTotalGrand != null && lTotalGrand.isInfoEnabled();

            if (logAggregator) {
                Log metricsLog = MetricsManager.metricsLog;

                AggregatorMetrics gTotal = new AggregatorMetrics();
                gTotal.prefix = Metrics.makePrefix(new String[]{AggregatorMetrics.AGGREGATOR_METRIC_PREFIX, "All", "All", "All"});
                Map<CurrencyPair, AggregatorMetrics> ccyTotals = new HashMap<CurrencyPair, AggregatorMetrics>();

                ConcurrentMap<Organization, AggregationProcessor> processorCache = AggregationProcessorManager.getInstance().getAggregationProcessorCache();

                for (Map.Entry<Organization, AggregationProcessor> entry : processorCache.entrySet()) {
                    Organization org = entry.getKey();
                    AggregationProcessor processor = entry.getValue();

                    AggregatorMetrics orgTotal = new AggregatorMetrics();
                    orgTotal.prefix = Metrics.makePrefix(new String[]{AggregatorMetrics.AGGREGATOR_METRIC_PREFIX, org.getShortName(), "All", "All"});

                    Collection<Aggregator> aggregators = processor.getFactory().getClientSubManager().getAggregators();

                    for (Aggregator aggregator : aggregators) {
                        AggregatorMetrics metrics = aggregator.getMetrics();

                        if (metrics != null) {
                            CurrencyPair ccyPair = aggregator.getProduct().getCurrencyPair();
                            AggregatorMetrics ccyPairTotal = null;

                            if (logTotalCcyp) {
                                ccyPairTotal = ccyTotals.get(ccyPair);
                                if (ccyPairTotal == null) {
                                    ccyPairTotal = new AggregatorMetrics();
                                    ccyPairTotal.prefix = Metrics.makePrefix(new String[]{AggregatorMetrics.AGGREGATOR_METRIC_PREFIX, "All", "All", ccyPair.getName()});
                                    ccyTotals.put(ccyPair, ccyPairTotal);
                                }
                            }

                            AggregatorMetrics snap = metrics.snapshot();
                            if (snap != null) {
                                snap.logOn(metricsLog);
                                if (logTotalCcyp) ccyPairTotal.add(snap);
                                if (logTotalOrg) orgTotal.add(snap);
                                if (logTotalGrand) gTotal.add(snap);
                            }
                        }
                    }

                    metricsLog.info(AggregatorMetrics.AGGREGATOR_METRIC_PREFIX + " " + org.getShortName() + " aggregators: " + aggregators.size());
                    if (logTotalOrg) orgTotal.logOn(metricsLog);
                }

                metricsLog.info(AggregatorMetrics.AGGREGATOR_METRIC_PREFIX + " " + "Organizations: " + processorCache.size());
                if (logTotalGrand) gTotal.logOn(metricsLog);

                if (logTotalCcyp) {
                    for (AggregatorMetrics aggregatorMetrics : ccyTotals.values()) {
                        aggregatorMetrics.logOn(metricsLog);
                    }
                    metricsLog.info(AggregatorMetrics.AGGREGATOR_METRIC_PREFIX + " " + "Currency Pairs: " + ccyTotals.size());
                }
                metricsLog.info(AggregatorMetrics.AGGREGATOR_METRIC_PREFIX + " " + "aggregator metrics logging ms: " + (System.currentTimeMillis() - ms0));
            }
        } catch (Exception e) {
            log.error(GlobalAggregatorMetrics.class + " problem creating metrics report", e);
            rsb.append(e);
        }
        return rsb;
    }

    public void swapMetrics() {
    }

    public VolatileMetrics getMetrics() {
        return this;
    }

    public void register() {
        MetricsManager.instance().register((VolatileMetrics) instance);
    }

    public void unRegister() {
        MetricsManager.instance().unregister((VolatileMetrics) instance);
    }
}
