package com.integral.aggregation.rfs;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.AggregationProcessorBuilder;
import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.config.AggregationServiceMBean;

public class RFSAggregationProcessorBuilder extends AggregationProcessorBuilder {
	
	protected AggregationProcessor newAggregationProcessor() {
		return new RFSAggregationProcessor(this.organization);
	}

	protected AggregationServiceMBean getAggregationServiceMBean() {
		return AggregationServiceFactory.getInstance().getRFSAggregationMBean();
	}
}
