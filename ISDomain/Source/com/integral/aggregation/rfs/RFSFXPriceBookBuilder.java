package com.integral.aggregation.rfs;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.price.FXPrice;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.stream.FXPriceBookBuilder;
import com.integral.broker.aggregate.QuoteAggregator;
import com.integral.broker.aggregate.QuoteAggregatorFactory;
import com.integral.broker.model.Product;
import com.integral.broker.price.Price;
import com.integral.broker.price.PriceBook;
import com.integral.broker.price.RFSPriceC;
import com.integral.broker.price.SwapPriceC;
import com.integral.user.Organization;

public class RFSFXPriceBookBuilder extends FXPriceBookBuilder{

	public RFSFXPriceBookBuilder(AggregationProcessor service) {
		super(service);		
	}

	@Override
	public FXPriceBook build(PriceBook prices, Product product,
			QuoteAggregatorFactory aggregatorFactory) {		
		return super.build(prices, product, aggregatorFactory);
	}

	@Override
	protected String getProviderQuoteId(Price price) {
    	if (price == null) return null;
    	return price.getProviderQuoteId();
	}

	@Override
	protected String getLPName(QuoteAggregator aggregator, Price price) {
        if (price.getOrganization() != null && price.getOrganization().isFMALP()) { 
            String displayName = getDisplayNameForFMAProvider();
            if (displayName != null) {
            	return displayName;
            }
        }
        String lpName = price.getLPName();
        return this.service.getConfig().isProviderNameDisplayEnabled() ?
                  						getDisplayNameForFMA(lpName) : lpName;		
	}
	
	

	@Override
	protected Organization getProvider(Price price, boolean rawAggregator) {		
		return price.getOrganization();
	}

	@Override
	protected void populateNearLeg(final QuoteAggregatorFactory aggregatorFactory, final Price price, final FXPrice fxPrice) {		
		if (price instanceof RFSPriceC) {
			RFSPriceC rfsPrice = (RFSPriceC)price;
			double rate = 0.0;
			if (rfsPrice instanceof SwapPriceC) {
				rate = ((SwapPriceC)rfsPrice).getNearRate();
			} else {
				rate = rfsPrice.getRate();
			}
			fxPrice.setRate(rate);
			fxPrice.setSpotRate(rfsPrice.getSpotRate());
			fxPrice.setForwardPoint(rfsPrice.getForwardPoints());
			Double midRate = rfsPrice.getMidRate();
			if (midRate != null) {
				fxPrice.setMidRate(midRate);  
			}
			double dealtAmount = rfsPrice.getAmount();
			fxPrice.setLimit(dealtAmount);
			double settleAmount = rfsPrice.getSettleAmount();
			fxPrice.setSettleAmount(settleAmount);
		}
	}


	@Override
	protected void populateFarLeg(final QuoteAggregatorFactory aggregatorFactory, final Price farPrice, final FXPrice fxPrice) {
		if (farPrice instanceof SwapPriceC) {
			SwapPriceC swapPrice = (SwapPriceC)farPrice;
			fxPrice.setRate(swapPrice.getFarRate());
			fxPrice.setSpotRate(swapPrice.getFarSpotRate());
			fxPrice.setForwardPoint(swapPrice.getFarForwardPoints());
			Double midRate = swapPrice.getFarMidRate();
			if (midRate != null) {
				fxPrice.setMidRate(midRate);
			}		
			double farDealtAmount = swapPrice.getFarAmount();
			fxPrice.setLimit(farDealtAmount);
			double farSettleAmount = swapPrice.getFarSettleAmount();
			fxPrice.setSettleAmount(farSettleAmount);
		}  

	}

	@Override
	protected boolean isSwap(final QuoteAggregatorFactory aggregatorFactory) {
		
		return aggregatorFactory.isSwap();
	}

	@Override
	protected void populateExpiryTime(final Price price, final FXPriceBook priceBook) {
		if (price instanceof RFSPriceC) {
			RFSPriceC rfsPrice = (RFSPriceC)price;
			int timeToLive = rfsPrice.getTimeToLive();
			priceBook.setExpiryTime(timeToLive);
		}
	}

	
	
}
