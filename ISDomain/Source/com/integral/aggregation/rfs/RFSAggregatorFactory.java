package com.integral.aggregation.rfs;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.AggregationProcessorConfig;
import com.integral.aggregation.AggregatorFactory;
import com.integral.aggregation.MultiProviderFilterConfig;
import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.log.ASAggregationLogger;
import com.integral.aggregation.log.RFSAggregationLoggerC;
import com.integral.aggregation.log.RFSBinaryAggregationLogger;
import com.integral.aggregation.price.FXPrice;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.stream.ESPAggregator;
import com.integral.aggregation.stream.FXPriceBookBuilder;
import com.integral.aggregation.stream.MarketRateBuilder;
import com.integral.aggregation.stream.ProviderPriceObserver;
import com.integral.aggregation.stream.view.LadderViewRequest;
import com.integral.aggregation.stream.view.LadderedViewAggregator;
import com.integral.aggregation.subscription.AggregationProcessorRequest;
import com.integral.aggregation.subscription.RFSAggregationProcessorRequestC;
import com.integral.aggregation.subscription.client.ClientSubscriptionManager;
import com.integral.aggregation.subscription.provider.ProviderSubscriptionManager;
import com.integral.aggregation.subscription.provider.RFSProviderSubscriptionManager;
import com.integral.aggregation.subscription.provider.quote.ProviderQuoteHandler;
import com.integral.aggregation.subscription.provider.quote.RFSProviderQuoteHandler;
import com.integral.broker.aggregate.QuoteAggregatorFactory;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.finance.currency.CurrencyPair;
import com.integral.user.Organization;

public class RFSAggregatorFactory extends AggregatorFactory {
	

	public RFSAggregatorFactory(AggregationProcessor processor) {
		super(processor);
		
	}

	@Override
	public ClientSubscriptionManager getClientSubManager() {		
        if (clientSubManager == null) {
            clientSubManager = new RFSClientSubscriptionManager(this.aggregationProcessor);
        }
        return clientSubManager;
	}

	@Override
	public ProviderSubscriptionManager getProviderSubManager() {
	    if (providerSubManager == null) {
	        providerSubManager = new RFSProviderSubscriptionManager(this.aggregationProcessor);
	    }
	    return providerSubManager;		

	}

	@Override
	public AggregationProcessorConfig getAggregationProcessorConfig() {		
		return super.getAggregationProcessorConfig();
	}

	@Override
	public MultiProviderFilterConfig getMultiProviderFilterConfig() {		
		return super.getMultiProviderFilterConfig();
	}

	@Override
	public RFSProviderSubscriptionManager getRFSProviderSubManager() {
        if (rfsProviderSubManager == null) {
            rfsProviderSubManager = new RFSProviderSubscriptionManager(this.aggregationProcessor);
        }
        return rfsProviderSubManager;
	}

	@Override
	public ProviderQuoteHandler getOrCreateQuoteHandler(CurrencyPair ccyPair, Organization provider) {		
		return new RFSProviderQuoteHandler(this.aggregationProcessor, provider, ccyPair);
	}
	
	/**
	 * For each RFS request a new Quote Handler will be created
	 */

	@Override
	public ProviderQuoteHandler getQuoteHandler(CurrencyPair ccyPair, Organization provider) {	
		
		return new RFSProviderQuoteHandler(this.aggregationProcessor, provider, ccyPair);
	
	}	
	
	@Override
	public BrokerOrganizationFunction getBrokerOrganizationFunction() {		
		return super.getBrokerOrganizationFunction();
	}


	@Override
	public RFSAggregator newRFSAggregator(AggregationProcessorRequest request) {		 
		return new RFSAggregator(this.aggregationProcessor, request);
	}
	
	@Override
	public LadderedViewAggregator newLadderedViewAggregator(
			ESPAggregator espAggregator) {		
		return super.newLadderedViewAggregator(espAggregator);
	}

	@Override
	public ProviderPriceObserver newPriceObserver(ESPAggregator publisher, ProviderQuoteHandler quoteHandler, Organization provider) {		
		return super.newPriceObserver(publisher, quoteHandler, provider);
	}

	@Override
	public ASAggregationLogger newAggregatorLogger(ESPAggregator aggregator) {
	
		try {
			String fiOrg = aggregator.getAggregationProcessor().getFiOrg().getShortName();
			if (AggregationServiceFactory.getInstance().getRFSAggregationMBean().isAggLogBinaryEnabled(fiOrg)) {
				return new RFSBinaryAggregationLogger(aggregator);
			} else {
				return new RFSAggregationLoggerC(aggregator);
			}

		} catch (Exception e) {
			e.printStackTrace();
			return getDefaultAggregator();
		}
	
	}

	@Override
	public ASAggregationLogger newAggregatorLogger(RFSAggregator aggregator) {		
		return super.newAggregatorLogger(aggregator);
	}

	@Override
	public MarketRateBuilder getMarketRateBuilder() {		
		return super.getMarketRateBuilder();
	}

	@Override
	public FXPriceBook newPriceBook(boolean isSwap) {		
		return super.newPriceBook(isSwap);
	}

	@Override
	public FXPrice newPrice() {		
		return super.newPrice();
	}

	@Override
	public FXPriceBook newPriceBook(boolean isSwap, int bidSize, int offerSize) {		
		return super.newPriceBook(isSwap, bidSize, offerSize);
	}

	@Override
	public FXPriceBookBuilder getPriceBookBuilder() {		
	       if (priceBookBuilder == null) {
	            priceBookBuilder = new RFSFXPriceBookBuilder(this.aggregationProcessor);
	        }
	        return priceBookBuilder;
	}

	@Override
	public AggregationProcessorRequest newAggregationProcessorRequest(String requestId) {		
        AggregationProcessorRequest request = new RFSAggregationProcessorRequestC();
        request.setRequestId(requestId);
        return request;
	}

	@Override
	public LadderViewRequest newLadderViewRequest() {		
		return super.newLadderViewRequest();
	}

	@Override
	public ASAggregationLogger getDefaultAggregator() {
	      if (defaultAggregator == null) {
	            defaultAggregator = new RFSAggregationLoggerC();
	        }
	        return defaultAggregator;
	}

	@Override
	public QuoteAggregatorFactory newQuoteAggregatorFactory(ESPAggregator espAggregator) {		
		return super.newQuoteAggregatorFactory(espAggregator);
	}

	@Override
	public FXPrice newFXPrice() {		
		return super.newFXPrice();
	}
	
	

}
