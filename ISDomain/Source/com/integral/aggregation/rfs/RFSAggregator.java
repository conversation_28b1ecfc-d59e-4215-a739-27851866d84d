// Copyright (c) 2007 Integral Development Corp. All rights reserved.
package com.integral.aggregation.rfs;

import java.util.Collection;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.AggregatorThread;
import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.config.AggregationServiceMBean;
import com.integral.aggregation.stream.ESPAggregator;
import com.integral.aggregation.stream.ProviderPriceObserver;
import com.integral.aggregation.subscription.AggregationProcessorRequest;
import com.integral.aggregation.subscription.RFSAggregationProcessorRequest;
import com.integral.aggregation.subscription.provider.quote.ProviderQuoteHandler;
import com.integral.broker.price.PriceBook;
import com.integral.finance.dealing.Quote;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.Organization;

/**
 * Publish quotes for a RFS Request.
 *
 * <AUTHOR> Development Corp.
 */
public class RFSAggregator extends ESPAggregator {
    protected Log log = LogFactory.getLog(RFSAggregator.class);
   
     public RFSAggregator(AggregationProcessor service, AggregationProcessorRequest request) {
    	super(service, request);  
    }


    public void start(long publicationInterval, long bestBidOfferPublicationInterval) {
        super.start(publicationInterval, bestBidOfferPublicationInterval);
    }

    protected void registerProviderPriceHandlers() {
    	RFSAggregationProcessorRequest aggregationProcessorRequest = (RFSAggregationProcessorRequest)getAggregationProcessorRequest();
    	this.priceProviders = aggregationProcessorRequest.getAllProviders(); 
        orgToPriceObservers.clear();
        providerPrices.clear();
        ProviderQuoteHandler providerQuoteHandler = aggregationProcessorRequest.getProviderQuoteHandler(null);
        for (Organization provider : this.priceProviders) {
            ProviderPriceObserver priceObserver = this.factory.newPriceObserver(this, providerQuoteHandler, provider);
            priceObserver.register();
            orgToPriceObservers.put(provider, priceObserver);
        }
     }
    
    protected AggregatorThread getAggregatorThread() {
    	return RFSAggregatorThreadManager.
	               getInstance().getAggregatorThread();  
    }
    
    protected AggregationServiceMBean getAggregationServiceMBean() {
    	return AggregationServiceFactory.getInstance().getRFSAggregationMBean();
    }
 
    public void stop() {
        super.stop();
    }
    
    
    protected boolean logBook() {
    	return true;
    }

    /**
     * For RFS filtering is not required
     * need some discussion
     */
	
	@Override
	protected Collection<Quote> filterQuotes(Collection<Quote> quotes) {		
		return quotes;
	}


	protected boolean isFiltered(PriceBook priceBook) {
		return true;
	}

	protected boolean isRateMoved(final PriceBook currentPriceBook) {
		return false;
	}
	
	/**
	 * Best Bid/Offer updated check is done only if best bid/offer publish interval is less than normal publish interval and 
	 * new tick has come before bid/offer publish interval.
	 * For RFS it was never working as this method in parent class was throwing NullPointerException because of the problem with QuoteC API
	 * and was always returning false.Generally normal publish interval is in such a way that tick update comes after normal publish interval 
	 * and there is no issue but if tick is updated before best bid/offer publish interval then for RFS this method always returned false because of NPE
	 * Currently for RFS , it will return false and aggregation will happen after every publish interval
	 */
    protected boolean isBestBidOfferUpdated() {
    	lastBestBidOfferCheckedTime = System.currentTimeMillis();
        return false;
    }
    
}


 
