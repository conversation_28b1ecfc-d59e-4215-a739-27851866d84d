package com.integral.aggregation.rfs;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.AggregationResponseHandler;
import com.integral.aggregation.Response;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.subscription.AggregationProcessorRequest;
import com.integral.aggregation.subscription.client.ClientSubscriptionManager;

public class RFSClientSubscriptionManager extends ClientSubscriptionManager{

	public RFSClientSubscriptionManager(AggregationProcessor processor) {
		super(processor);		
	}

	@Override
	public Response subscribe(AggregationProcessorRequest request) {
		Response response = request.createResponse();
		try {
			AggregationResponseHandler<Response, FXPriceBook> handler = request.getPriceHandler();
			RFSAggregator aggregator = this.factory.newRFSAggregator(request);			
			String requestId = request.getRequestId();
			aggregatorCache.put(requestId, aggregator);
			Response providerSubResponse = factory.getProviderSubManager().subscribe(aggregator); 
            response.setMsg(providerSubResponse.getMsg());
            if (providerSubResponse.getStatus() == Response.STATUS_OK) {
                aggregator.addPriceListener(handler);  
                subscriptionRequestCache.put(requestId, request);
                long quotePubInterval = request.getProduct().getConfiguration().getStream().getQuotePublicationInterval();
                long bestBidOfferPubInterval = request.getProduct().getConfiguration().getStream().getQuotePublicationCheckInterval();                
                aggregator.start(quotePubInterval, bestBidOfferPubInterval);
            } else {                
                response.setStatus(Response.STATUS_FAIL);
            }
	
		} catch (Exception exc) {
			log.error("Subscription failed for RFS Aggregation Request :" + request.getRequestId(), exc);
			response.setStatus(Response.STATUS_FAIL);
			
		}
		return response;
	}
}
