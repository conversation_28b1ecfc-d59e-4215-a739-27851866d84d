package com.integral.aggregation.rfs;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.AggregatorFactory;
import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.subscription.AggregationRequestBuilder;
import com.integral.aggregation.subscription.RFSAggregationRequestBuilder;
import com.integral.aggregation.subscription.client.ClientSubscriptionManager;
import com.integral.user.Organization;

public class RFSAggregationProcessor extends AggregationProcessor{

	public RFSAggregationProcessor(Organization org) {
		super(org);		
	}
	
    protected ClientSubscriptionManager newClientSubscriptionManager() {
    	return this.factory.getClientSubManager();
    }

	@Override
	public AggregationRequestBuilder getRequestBuilder() {
		
		return new RFSAggregationRequestBuilder(this);
	}

	protected AggregatorFactory newAggregatorFactory() {
	  	return new RFSAggregatorFactory(this);
	}
	
	protected void updateAggregationProcessorConfig() {
		AggregationServiceFactory.getInstance().getRFSAggregationMBean()
									.updateAggregationProcessorConfig(this.config);
	}
}
