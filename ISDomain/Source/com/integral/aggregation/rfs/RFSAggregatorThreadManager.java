package com.integral.aggregation.rfs;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import com.integral.aggregation.AggregatorThread;
import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.config.AggregationServiceMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;

public class RFSAggregatorThreadManager {
	
    private Log log = LogFactory.getLog(this.getClass());
    private static RFSAggregatorThreadManager instance = new RFSAggregatorThreadManager();

    private CopyOnWriteArrayList<AggregatorThread> aggregatorThreadCache = new CopyOnWriteArrayList<AggregatorThread>();
    private ReentrantReadWriteLock readWriteLock = new ReentrantReadWriteLock();
    private AggregatorThread defaultAggThread;

    private RFSAggregatorThreadManager() {
    }

    public static RFSAggregatorThreadManager getInstance() {
          return instance;
    }

    public AggregatorThread getAggregatorThread() {
        AggregatorThread aggThread = null;

        try  {
            // fill each scheduler before allocating a new one up to limit, stream 2 scheduler association is 'sticky'
        	AggregationServiceMBean aggregationServiceMBean = AggregationServiceFactory.getInstance().getRFSAggregationMBean();
            int aggregatorThreadPoolSize = aggregationServiceMBean.getAggregatorThreadPoolSize();
            int aggregatorsPerThread = aggregationServiceMBean.getAggregatorsPerThread();

            int threadPoolSize = Math.max(1, aggregatorThreadPoolSize);

            // Shrink the size.
            while (threadPoolSize < aggregatorThreadCache.size()) {
                readWriteLock.writeLock().lock();
                try {
                    aggregatorThreadCache.remove(aggregatorThreadCache.size() - 1);
                } finally {
                    readWriteLock.writeLock().unlock();
                }
            }

            readWriteLock.readLock().lock();
            try {
                for (AggregatorThread thread : aggregatorThreadCache) {
                    if (thread.getSize() < aggregatorsPerThread) {
                        aggThread = thread;
                        break;
                    }
                }
            } finally {
                readWriteLock.readLock().unlock();
            }

            if (aggThread == null) {
                if (aggregatorThreadCache.size() < aggregatorThreadPoolSize) {
                    readWriteLock.writeLock().lock();
                    try {
                        aggThread = new AggregatorThread(aggregationServiceMBean);
                        aggregatorThreadCache.add(aggThread);
                    } finally {
                        readWriteLock.writeLock().unlock();
                    }
                } else {
                    aggThread = getAggregatorThreadWithMinimumAggregators();
                }
            }
        } catch (Exception exc) {
            log.error("AggregatorThreadManager.getAggregatorThread() Exception while allocating the AggregatorThread. " +
                    "Returning the default AggregatorThread." + " - " + aggThread + "- " + exc.getMessage());
            if (aggThread == null) {
                aggThread = getDefaultAggregatorThread();
            }
        }

        return aggThread;
    }

    public void stopAggregatorThreads() {
    	// remove it from the cache first and then stop 
    	List<AggregatorThread> copy = new ArrayList<AggregatorThread>(this.aggregatorThreadCache);
    	aggregatorThreadCache.clear();
        for (AggregatorThread thread : copy) {
            thread.stop();
        }
        
    }

    private AggregatorThread getDefaultAggregatorThread() {
        if (defaultAggThread == null) {
            defaultAggThread = new AggregatorThread(AggregationServiceFactory.getInstance().getRFSAggregationMBean());
        }
        return defaultAggThread;
    }

    private AggregatorThread getAggregatorThreadWithMinimumAggregators() {
        AggregatorThread availableThread = null;
        int minimumTasksCounter = Integer.MAX_VALUE;

        for (AggregatorThread aggThread : aggregatorThreadCache) {
            if (aggThread.getSize() < minimumTasksCounter) {
                minimumTasksCounter = aggThread.getSize();
                availableThread = aggThread;
            }
        }
        return availableThread;
    }

    public Collection<AggregatorThread> getAggregatorThreads() {
        return Collections.unmodifiableCollection(aggregatorThreadCache);
    }

}
