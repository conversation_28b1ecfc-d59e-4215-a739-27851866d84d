package com.integral.aggregation.rfs;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.rfs.metrics.GlobalRFSAggregatorMetrics;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.Organization;

public class RFSAggregationProcessorManager {
	
    private Log log = LogFactory.getLog(RFSAggregationProcessorManager.class);
    private static RFSAggregationProcessorManager instance = new RFSAggregationProcessorManager();

    private ConcurrentMap<Organization, AggregationProcessor> aggregationProcessorCache =
            							new ConcurrentHashMap<Organization, AggregationProcessor>();

    private RFSAggregationProcessorManager() {
    	
    }
    
    public static RFSAggregationProcessorManager getInstance() {
            return instance;
    }

    public AggregationProcessor getAggregationProcessor(Organization org) {
        return aggregationProcessorCache.get(org);
    }

    
    public AggregationProcessor getOrCreateAggregationProcessor(Organization org) {
        AggregationProcessor aggregationProcessor  = aggregationProcessorCache.get(org);

        if (aggregationProcessor == null) {
            aggregationProcessor = newAggregatorProcessor(org);
            AggregationProcessor oldAggregationProcessor = aggregationProcessorCache.putIfAbsent(org, aggregationProcessor);

            if (oldAggregationProcessor != null) {
                aggregationProcessor = oldAggregationProcessor;
            }
            manageAggregatorThread();
        }
        return aggregationProcessor;
    }
  
    public ConcurrentMap<Organization, AggregationProcessor> getAggregationProcessorCache() {
        return aggregationProcessorCache;
    }

    public boolean removeAggregatorProcessor(Organization org) {
        AggregationProcessor removedProcessor = aggregationProcessorCache.remove(org);
        boolean response = false;
        if (removedProcessor != null) {
            removedProcessor.stop();
            response = true;
            manageAggregatorThread();
        }
        return response;
    }

    private void manageAggregatorThread() {
        if (aggregationProcessorCache.size() == 1) {
//            AggregatorThread.instance().start();
            GlobalRFSAggregatorMetrics.getInstance().register();
        } else if (aggregationProcessorCache.size() == 0) {
            RFSAggregatorThreadManager.getInstance().stopAggregatorThreads(); 
            GlobalRFSAggregatorMetrics.getInstance().unRegister();
        }
    }
    
    private AggregationProcessor newAggregatorProcessor(Organization org) {
        return new RFSAggregationProcessorBuilder()
                .setOrganization(org)
                .build();
    }

}
