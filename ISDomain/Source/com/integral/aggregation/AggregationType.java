package com.integral.aggregation;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

import com.integral.aggregation.stream.view.DynamicLadderAggregator;
import com.integral.broker.aggregate.*;
import com.integral.broker.aggregate.rfs.RFSFullBookQuoteAggregatorC;
import com.integral.broker.aggregate.rfs.RFSSwapFullBookQuoteAggregatorC;
import com.integral.broker.aggregate.rfs.RawRFSBestPriceQuoteAggregatorC;
import com.integral.broker.aggregate.rfs.RawSwapBestPriceQuoteAggregatorC;

/**
 *
 */
public enum AggregationType {
    RawFullBookQuoteAggregation("RFBA", AggregationMethod.RAW_BOOK, "RawFullBookQuoteAggregator", new RawFullBookQuoteAggregatorC()),
    FullBookTierQuoteAggregation("FBTA", AggregationMethod.FULL_BOOK, "FullBookTierQuoteAggregator", new FullBookTierQuoteAggregatorC()),
    BestPriceTierQuoteAggregation("BPA", AggregationMethod.BEST_PRICE, "BestPriceQuoteAggregator", new BestPriceTierQuoteAggregatorC()),
    MultiPriceMarketQuoteAggregation("MTMA", AggregationMethod.MULTI_PRICE_MARKET, "MultiTierAverageQuoteAggregator", new MultiTierMarketQuoteAggregatorC()),
    MultiPriceTiersQuoteAggregation("MTWA", AggregationMethod.MULTI_PRICE_TIERS, "MultiPriceTierQuoteAggregator", new MultiTierWorstQuoteAggregatorC()),
    WeightedAverageQuoteAggregation("VWAPA", AggregationMethod.WEIGHTED_AVERAGE, "VWapQuoteAggregation", new DynamicLadderAggregator()),
    FBWeightedAverageQuoteAggregation("FBMTWA", AggregationMethod.FB_WEIGHTED_AVERAGE, "SyntheticFullBookQuoteAggregation", new FullBookMTAQuoteAggregatorC()),
    FBMultiPriceTiersQuoteAggregation("FBMTWA", AggregationMethod.FB_MULTI_PRICE_TIERS, "SyntheticFullBookQuoteAggregation", new FullBookMTWQuoteAggregatorC()),

    MultiTierFOKQuoteAggregation("MTFOKQA", AggregationMethod.MULTI_TIER_FOK, "MultiTierFOKQuoteAggregator", new MultiTierFOKQuoteAggregatorC()),
    FBMultiTierFOKQuoteAggregation("FBMTFOK", AggregationMethod.FB_MULTI_TIER_FOK, "SyntheticFullBookMultiTierFOKQuoteAggregator", new FullBookMTFOKQuoteAggregatorC()),
    FAMultiTierQuoteAggregation("MTFAA", AggregationMethod.FA_MULTI_TIER, "MultiTierFullAmountAggregator", new MultiTierFullAmountAggregatorC()),
    FAMultiQuoteAggregation("MQFAA", AggregationMethod.FA_MULTI_QUOTE, "SyntheticFullAmountFullBookAggregator", new SyntheticFullAmountFullBookAggregatorC()),
    //Added for RFS Full Book/Best Price
    RFSFullBookAggregation("RFSFBPA", AggregationMethod.RFS_FULL_BOOK, "RFSFullBookQuoteAggregation", new RFSFullBookQuoteAggregatorC()),
    RFSSwapFullBookAggregation("RFSSFBA", AggregationMethod.RFS_SWAP_FULL_BOOK, "RFSSwapFullBookQuoteAggregation", new RFSSwapFullBookQuoteAggregatorC()),
    RFSBestPriceAggregation("RRFSBPA", AggregationMethod.RFS_BEST_PRICE, "RFSBestPriceQuoteAggregation", new RawRFSBestPriceQuoteAggregatorC()),
    RFSSwapBestPriceAggregation("RSBPA", AggregationMethod.RFS_SWAP_BEST_PRICE, "RFSSwapBestPriceQuoteAggregation", new RawSwapBestPriceQuoteAggregatorC()),

    // Benchmark aggregation
    BenchmarkRateAggregation("BMPA", AggregationMethod.BENCHMARK, "BenchmarkRateAggregation", new BenchmarkQuoteAggregatorC());

    private final String shortName;
    private final AggregationMethod aggMethod;
    private final String description;
    private final QuoteAggregator aggregator;

    private static final Map<String, AggregationType> lookup = new HashMap<String, AggregationType>();

    static {
        for (AggregationType s : EnumSet.allOf(AggregationType.class))
            lookup.put(s.getAggMethod().getId(), s);
    }

    AggregationType(String shortName, AggregationMethod aggMethod, String description, QuoteAggregator aggregator) {
        this.shortName = shortName;
        this.aggMethod = aggMethod;
        this.description = description;
        this.aggregator = aggregator;
    }

    public String getShortName() {
        return shortName;
    }

    public AggregationMethod getAggMethod() {
        return aggMethod;
    }

    public String getDescription() {
        return description;
    }

    public QuoteAggregator getAggregator() {
        return aggregator;
    }

    public static AggregationType getAggregationType(AggregationMethod aggMethod) {
        return lookup.get(aggMethod.getId());
    }
}
