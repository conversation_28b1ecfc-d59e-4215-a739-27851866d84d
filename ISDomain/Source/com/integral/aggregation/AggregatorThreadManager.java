package com.integral.aggregation;

import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.config.AggregationServiceMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * This class is responsible for managing the instances of AggregatorThread.
 * Depending on the load, each individual Aggregator Thread is handling,
 * new instance will be created.
 */
public class AggregatorThreadManager {
    private Log log = LogFactory.getLog(this.getClass());
    private static AggregatorThreadManager instance;

    private CopyOnWriteArrayList<AggregatorThread> aggregatorThreadCache = new CopyOnWriteArrayList<AggregatorThread>();
    private ReentrantReadWriteLock readWriteLock = new ReentrantReadWriteLock();
    private AggregatorThread defaultAggThread;

    private AggregatorThreadManager() {
    }

    public static AggregatorThreadManager getInstance() {
        if (instance == null) {
            instance = new AggregatorThreadManager();
        }
        return instance;
    }

    public AggregatorThread getAggregatorThread() {
        AggregatorThread aggThread = null;

        try  {
            // fill each scheduler before allocating a new one up to limit, stream 2 scheduler association is 'sticky'
        	AggregationServiceMBean aggregationServiceMBean = AggregationServiceFactory.getInstance().getAggregationMBean();
            int aggregatorThreadPoolSize = aggregationServiceMBean.getAggregatorThreadPoolSize();
            int aggregatorsPerThread = aggregationServiceMBean.getAggregatorsPerThread();
  
            int threadPoolSize = Math.max(1, aggregatorThreadPoolSize);

            // Shrink the size.
            while (threadPoolSize < aggregatorThreadCache.size()) {
                readWriteLock.writeLock().lock();
                try {
                    aggregatorThreadCache.remove(aggregatorThreadCache.size() - 1);
                } finally {
                    readWriteLock.writeLock().unlock();
                }
            }

            readWriteLock.readLock().lock();
            try {
                for (AggregatorThread thread : aggregatorThreadCache) {
                    if (thread.getSize() < aggregatorsPerThread) {
                        aggThread = thread;
                        break;
                    }
                }
            } finally {
                readWriteLock.readLock().unlock();
            }

            if (aggThread == null) {
                if (aggregatorThreadCache.size() < aggregatorThreadPoolSize) {
                    readWriteLock.writeLock().lock();
                    try {
                        aggThread = new AggregatorThread(aggregationServiceMBean);
                        aggregatorThreadCache.add(aggThread);
                    } finally {
                        readWriteLock.writeLock().unlock();
                    }
                } else {
                    aggThread = getAggregatorThreadWithMinimumAggregators();
                }
            }
        } catch (Exception exc) {
            log.error("AggregatorThreadManager.getAggregatorThread() Exception while allocating the AggregatorThread. " +
                    "Returning the default AggregatorThread." + " - " + aggThread + "- " + exc.getMessage());
            if (aggThread == null) {
                aggThread = getDefaultAggregatorThread();
            }
        }

        return aggThread;
    }

    public void stopAggregatorThreads() {
        for (AggregatorThread thread : aggregatorThreadCache) {
            thread.stop();
        }
        aggregatorThreadCache.clear();
    }

    private AggregatorThread getDefaultAggregatorThread() {
        if (defaultAggThread == null) {
            defaultAggThread = new AggregatorThread(AggregationServiceFactory.getInstance().getAggregationMBean());
        }
        return defaultAggThread;
    }

    private AggregatorThread getAggregatorThreadWithMinimumAggregators() {
        AggregatorThread availableThread = null;
        int minimumTasksCounter = Integer.MAX_VALUE;

        for (AggregatorThread aggThread : aggregatorThreadCache) {
            if (aggThread.getSize() < minimumTasksCounter) {
                minimumTasksCounter = aggThread.getSize();
                availableThread = aggThread;
            }
        }
        return availableThread;
    }

    public Collection<AggregatorThread> getAggregatorThreads() {
        return Collections.unmodifiableCollection(aggregatorThreadCache);
    }
}
