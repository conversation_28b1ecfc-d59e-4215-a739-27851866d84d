package com.integral.aggregation.util;

import java.util.HashMap;

/**
 *
 */
public class FXHashMap<K, V, D> extends HashMap<K, V> implements FXMap<K, V, D> {
    private D defaultValue;

    public FXHashMap(int initialCapacity, float loadFactor) {
        super(initialCapacity, loadFactor);
    }

    public FXHashMap(int initialCapacity) {
        super(initialCapacity);
    }

    public FXHashMap() {
    }

    public D getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(D defaultValue) {
        this.defaultValue = defaultValue;
    }
}
