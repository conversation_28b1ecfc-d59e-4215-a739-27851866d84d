package com.integral.aggregation.util;

import java.util.concurrent.atomic.AtomicInteger;



public class AggLogStatsC implements AggLogStats { 

	private double timeTakenForPopulatingByteBufferInMicros;
	
	private double timeTakenForCompressingInMicros;
	
	private AtomicInteger numberOfOrganizations;
	
	private AtomicInteger numberOfExceptionsOnProducerSide;
	
	private AtomicInteger numberOfExceptionsOnConsumerSide;
	
	private AtomicInteger numberOfLogEventsDropped;
	
	private static AggLogStatsC instance = null;
 

	private AggLogStatsC() {
		numberOfExceptionsOnProducerSide = new AtomicInteger(0);
		numberOfExceptionsOnConsumerSide = new AtomicInteger(0);
		numberOfLogEventsDropped = new AtomicInteger(0);
		numberOfOrganizations = new AtomicInteger(0);
		
	}

	public static AggLogStatsC getInstance() {
		if(instance == null) {
			synchronized (AggLogStatsC.class) {
				if(instance == null) {
					instance = new AggLogStatsC();
				}
			}
		}
		return instance;
	}
	
	
	@Override
	public void setTimeTakenForByteBufferPopulation(double timeInMicros) {
		timeTakenForPopulatingByteBufferInMicros = timeInMicros;
		
	}

	@Override
	public void setTimeTakenForCompressingByteArray(double timeInMicros) {
		timeTakenForCompressingInMicros = timeInMicros;	
	}
	
	public double getTimeTakenForByteBufferPopulation() {
		return timeTakenForPopulatingByteBufferInMicros;
	}
	
	public double getTimeTakenForCompressingInMicros() {
		return timeTakenForCompressingInMicros;
	}
	
	public void incrementExceptionCountOnProducer() {
		numberOfExceptionsOnProducerSide.incrementAndGet();
	}

	public void incrementExceptionCountOnConsumer() {
		numberOfExceptionsOnConsumerSide.incrementAndGet();
	}
	
	public void incrementNumberOfEventsDropped() {
		numberOfLogEventsDropped.incrementAndGet();
	}
	
	public void incrementCountOfOrganizations() {
		numberOfOrganizations.incrementAndGet();
	}
	
	public int getNumberOfOrganizations() {
		return numberOfOrganizations.intValue();
	}
	
	public int getNumberOfEventsDropped() {
		return numberOfLogEventsDropped.intValue();
	}
	
	public int getExceptionCountOnProducer() {
		return numberOfExceptionsOnProducerSide.intValue();
	}
	
	public int getExceptionCountOnConsumer() {
		return numberOfExceptionsOnConsumerSide.intValue();
	}
	
	 
}
