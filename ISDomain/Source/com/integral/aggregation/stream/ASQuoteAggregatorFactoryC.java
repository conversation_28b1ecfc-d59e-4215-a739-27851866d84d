package com.integral.aggregation.stream;

import com.integral.aggregation.AggregationProcessorConfig;
import com.integral.aggregation.AggregationServiceUtil;
import com.integral.aggregation.log.ASAggregationLogger;
import com.integral.aggregation.log.ASAggregationLoggerC;
import com.integral.aggregation.subscription.AggregationProcessorRequest;
import com.integral.aggregation.subscription.AggregationRequest;
import com.integral.broker.HourglassPricingUtil;
import com.integral.broker.aggregate.MultiTierFullAmountAggregatorC;
import com.integral.broker.aggregate.QuoteAggregator;
import com.integral.broker.aggregate.QuoteAggregatorFactoryC;
import com.integral.broker.aggregate.log.AggregationLogger;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.Product;
import com.integral.broker.model.Stream;
import com.integral.finance.config.FinanceConfigurationFactory;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.Request;
import com.integral.finance.instrument.InstrumentClassification;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeClassification;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.spaces.fx.esp.provision.ProvisionCache;
import com.integral.is.spaces.fx.esp.provision.RelationshipProvision;
import com.integral.lp.StreamManager;
import com.integral.user.Organization;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;
import java.util.Set;

/**
 *
 */
public class ASQuoteAggregatorFactoryC extends QuoteAggregatorFactoryC {
    private ASAggregationLogger aggregatorLogger;
    private AggregationProcessorConfig config;
    private AggregationProcessorRequest request;
    private QuoteAggregator aggregator;
    private boolean isTermCcyAggregationSupported;
    private double requestedSize = DEFAULT_VALUE;
    private Double minQty = DEFAULT_VALUE;
    private Double minTradeSize = DEFAULT_VALUE;
    private Double minQuoteSize = DEFAULT_VALUE;
    private int roundingFactor;
    private volatile String valueDate;
    private long valueDateMilliseconds;
    final SimpleDateFormat dateFormat;

    public ASQuoteAggregatorFactoryC(ESPAggregator espAggregator) {
        super(espAggregator.getProduct());
        this.request = espAggregator.getAggregationProcessorRequest();
        this.aggregatorLogger = espAggregator.getAggregatorLogger();
        if(aggregatorLogger instanceof ASAggregationLoggerC) ((ASAggregationLoggerC) aggregatorLogger).setFactory(this);
        this.config = espAggregator.getAggregationProcessor().getFactory().getAggregationProcessorConfig();
        this.aggregator = this.product.getConfiguration().getQuoteAggregator();
        this.isTermCcyAggregationSupported = isTermCcyAggregationSupported(request);
        recomputeLiquidityProvisionParams();
        String orgShortName = espAggregator.getAggregationProcessor().getFiOrg().getShortName();
        this.roundingFactor = ccyPair == null ? 1000 :this.config.getTermAggregationRoundingFactor(orgShortName, ccyPair.getName());
        this.hourglass = getHourglassSettings(espAggregator);
        this.dateFormat = new SimpleDateFormat("yyyyMMdd");
        setValueDate();
        InstrumentClassification instrumentClassification = ccyPair.getBaseCurrency().getInstrumentClassification();
        String instrClsf = instrumentClassification != null ? instrumentClassification.getShortName() : null;
        boolean baseRoundingValue = FinanceConfigurationFactory.getFinanceMBean().isAmountRoundingUsingCurrencyTick(instrClsf, orgShortName, ccyPair.getBaseCurrency().getShortName());
        instrumentClassification = ccyPair.getVariableCurrency().getInstrumentClassification();
        instrClsf = instrumentClassification != null ? instrumentClassification.getShortName() : null;
        boolean termRoundingValue = FinanceConfigurationFactory.getFinanceMBean().isAmountRoundingUsingCurrencyTick(instrClsf, orgShortName, ccyPair.getVariableCurrency().getShortName());
        super.baseCcyAmtRoundingUsingTickValue = baseRoundingValue;
        super.termCcyAmtRoundingUsingTickValue = termRoundingValue;
        this.organization = espAggregator.getAggregationProcessor().getFiOrg();
    }

    public boolean getHourglassSettings(ESPAggregator espAggregator){
            Organization fiOrg = espAggregator.getAggregationProcessor().getFiOrg();
            LegalEntity le = config.getLegalEntity();
            hourglass = HourglassPricingUtil.getInstance().isHourglassPricingSupported(fiOrg, espAggregator.getCurrencyPair(), le);
            log.info("ASQuoteAggregatorFactoryC.init: isHourglass=" + hourglass);
            return hourglass;
    }

    @Override
    public void preAggregation(Collection<Quote> quotes) {
        if (isTermCcyAggregationSupported()) {
            //if the aggregator is composite, it has to be calculated once by the wrapper aggregator (MultiTierFullAmountAggregator)
            boolean compositeAggregation = aggregator instanceof MultiTierFullAmountAggregatorC;
            if(!compositeAggregation) calculateBestPriceMidRate(quotes);
        }

        initialize();
    }

    private void initialize() {
        requestedSize = DEFAULT_VALUE;
        minQty = DEFAULT_VALUE;
        bestBidPriceLPName = null;
        bestOfferPriceLPName = null;
    }

    @Override
    public boolean isTermCcyAggregationSupported() {
        return isTermCcyAggregationSupported;
    }

    @Override
    public int getMaxTierSize() {
        return 0;
    }

    public boolean isFullBookTruncateEnabled() {
        return false;
    }

    public double applyRoundingFactorToAmount(double amount) {
        return ((long) ((amount) / roundingFactor)) * roundingFactor;
    }

    @Override
    public double getRequestedSize() {
        if (requestedSize == DEFAULT_VALUE) {
            requestedSize = this.product.getSubscriptionSize();

            if (requestedSize != 0 && isTermCcyAggregationSupported()) {
                requestedSize = getAmountOfBase(requestedSize);
            }
        }
        return requestedSize;
    }

    @Override
    public double getMinAmountSize() {
        if (minQty.equals(DEFAULT_VALUE)) {
            minQty = this.request.getMinQty();
            minQty = minQty == null ? 0.0 : minQty;

            if (minQty != 0.0 && isTermCcyAggregationSupported()) {
                minQty = getAmountOfBase(minQty);
            }
        }

        double minAmtSize = Math.max(minQty, minTradeSize == null ? 0.0 : minTradeSize);

        if (log.isDebugEnabled()) {
            StringBuilder sb = new StringBuilder(200);
            sb.append(this.getClass().getName()).append(".getMinAmountSize()")
            .append(' ').append(aggregator.getAbbreviatedName())
            .append(' ').append(this.request.getMinQty())
            .append(' ').append(this.minTradeSize)
            .append(' ').append(isTermCcyAggregationSupported())
            .append(' ').append(minQty)
            .append(' ').append(minAmtSize);

            log.debug(sb.toString());
        }
        return minAmtSize;
    }

    public double getMinQuoteSize() {
        return  minQuoteSize == null ? 0.0 : minQuoteSize;
    }

    public String getValueDate() {
        return valueDate;
    }

    public void setValueDate() {
        this.valueDate = this.config.getValueDate( this.config.getOrganization(), ccyPair  );
        try {
            if(valueDate != null) {
                Date date = dateFormat.parse(valueDate);
                valueDateMilliseconds = date.getTime();
            }
        } catch (Exception e) {
            log.error("ASQuoteAggregationFactory: parsing error during parsing value date", e);
        }
    }

    @Override
    public long getValueDateMilliseconds(){
        return valueDateMilliseconds;
    }

    public AggregationLogger getAggregatorLogger() {
        return this.aggregatorLogger;
    }

    public int getQuoteTier(String orgName, String ccyPair) {
        return this.config.getProviderQuoteTier(orgName, ccyPair);
    }

    @Override
    public boolean isLPPriorityBasedSortingSupported() {
        return config.isProviderNameDisplayEnabled();
    }

    private boolean isTermCcyAggregationSupported(AggregationProcessorRequest request) {
        Currency dltCcy = request.getDltCcy();
        if (dltCcy != null) {
            CurrencyPair ccyPair = request.getProduct().getCurrencyPair();
            return ccyPair.getVariableCurrency().equals(dltCcy);
        } else {
            return false;
        }
    }

    public void recomputeLiquidityProvisionParams() {
        this.minTradeSize = ccyPair == null ? null : this.config.getMinTradeSize(ccyPair);
        this.minQuoteSize = ccyPair == null ? null : this.config.getMinQuoteSize(ccyPair);
    }

	@Override
	public Integer getRequestedBidOfferMode()
	{
		// TODO Throw unsupported exception in this case
		return null;
	}

	/**
	 * checks if aggregation request is for a swap
	 */
	@Override
	public boolean isSwap() {		
		if (this.request == null) return false;
		AggregationRequest aggregationRequest = this.request.getAggregationRequest();
		if (aggregationRequest == null) return false;
		Request req = aggregationRequest.getWrappedRequest();
		if (req == null) return false;
		Trade trade = req.getTrade();
		if (trade == null) return false;
		TradeClassification tradeClassification = trade.getTradeClassification();
		if (tradeClassification == null) return false;
        return ISUtilImpl.isSwap(trade);
	}
}
