package com.integral.aggregation.stream;

import com.integral.aggregation.AggregationServiceUtil;
import com.integral.broker.model.Product;
import com.integral.broker.price.Price;
import com.integral.broker.price.PriceBook;
import com.integral.finance.dealing.Quote;
import com.integral.is.common.ProviderQuoteIdFactory;
import com.integral.is.message.MarketRate;
import com.integral.is.message.MessageFactory;
import com.integral.aggregation.AggregationProcessor;
import com.integral.time.IdcDate;
import com.integral.user.Organization;

/**
 *
 */
public class MarketRateBuilder {
    private AggregationProcessor service;
    private Organization brokerOrg;

    public MarketRateBuilder(AggregationProcessor service) {
        this.service = service;
        this.brokerOrg = service.getConfig().getOrganization();
    }

    public MarketRate build(PriceBook prices, Product product) {
        final MarketRate marketRate;

        IdcDate valueDate = AggregationServiceUtil.getValueDate(product);

        // generate the provider quote id
        final String quoteId = getQuoteId();

        int maxTiers = countTiers( prices );
        if ( maxTiers == 0 || valueDate == null ) {
            // no tiers; create stale rate
            marketRate = MessageFactory.newMarketRate();
            marketRate.setStale( true );
            marketRate.setProviderQuoteId( quoteId, 0 );
        } else {
            // MarketRateC constructor requires number of tiers upfront. If set to 1, may cause array overflow.
            marketRate = MessageFactory.newMarketRate( maxTiers, maxTiers );

            int bidTier = 0;
            int offerTier = 0;

            // set amounts and rates
            for ( Price bid : prices.getBids() ) {
                marketRate.setBidLimit( (long) bid.getAmount(), bidTier );
                marketRate.setBidRate( bid.getRate(), bidTier );
                marketRate.setProviderQuoteId( quoteId, bidTier );
                bidTier++;
            }
            for ( Price offer : prices.getOffers() ) {
                marketRate.setOfferLimit( (long) offer.getAmount(), offerTier );
                marketRate.setOfferRate( offer.getRate(), offerTier );
                marketRate.setProviderQuoteId( quoteId, offerTier );
                offerTier++;
            }

            // todo: Remove this workaround once bug 31355 is fixed by IS team.
            // add dummy bid prices to balance offer tiers
            while ( bidTier < offerTier ) {
                marketRate.setBidLimit( 0, bidTier );
                marketRate.setBidRate( 0, bidTier );
                marketRate.setProviderQuoteId( quoteId, bidTier );
                bidTier++;
            }
            // add dummy offer prices to balance bid tiers
            while ( bidTier > offerTier ) {
                marketRate.setOfferLimit( 0, offerTier );
                marketRate.setOfferRate( 0, offerTier );
                marketRate.setProviderQuoteId( quoteId, offerTier );
                offerTier++;
            }

            // flag rate as multi-tier or multi-price
            if ( prices.isMultiTier() ) {
                marketRate.setPriceType( Quote.PRICE_TYPE_MULTI_TIER );
            } else {
                marketRate.setPriceType( Quote.PRICE_TYPE_QUOTES );
            }
        }

        marketRate.setStreamId( product.getConfiguration().getStream().getShortName() );
        marketRate.setValueDate( valueDate );
        marketRate.setQuoteId( quoteId );
        marketRate.setProviderShortName( this.brokerOrg.getShortName() );
        marketRate.setBaseCcy( product.getCurrencyPair().getBaseCurrency().getName() );
        marketRate.setVarCcy( product.getCurrencyPair().getVariableCurrency().getName() );

        setTimingInfo(marketRate);
        return marketRate;
    }

    private String getQuoteId() {
        return ProviderQuoteIdFactory.newQuoteIdWithProviderName(this.brokerOrg.getShortName(),System.currentTimeMillis());
    }

    private int countTiers( PriceBook prices ) {
        final int tiers;
        if ( prices == null ) {
            tiers = 0;
        } else {
            int numBids = prices.getBids().size();
            int numOffers = prices.getOffers().size();
            tiers = Math.max( numBids, numOffers );
        }
        return tiers;
    }

    private void setTimingInfo( final MarketRate message ) {
        long time = System.currentTimeMillis();
        message.setRateEffective(time);
        message.setRateReceivedByAdapter(time);
        message.setRateSentByAdapter(time);
    }
}
