package com.integral.aggregation.stream;

import com.integral.broker.aggregate.FilterConfigFactory;
import com.integral.broker.filter.FilterStats;

/**
 * Created by IntelliJ IDEA.
 * User: poddars
 * Date: 1/11/13
 * Time: 2:24 PM
 */
public class ASFilterFactoryC implements FilterConfigFactory
{
    private static ASFilterFactoryC ASFilterFactory = new ASFilterFactoryC();



    private Double requestedBidSpotRate;
    private Double requestedOfferSpotRate;
    private Double pipsDeviation;
    private FilterStats stats;

    private ASFilterFactoryC()
    {
    }

    public static FilterConfigFactory getASFilterFactory()
    {
        return ASFilterFactory;
    }

    @Override
    public boolean isOffmarketNotificationRequired()
    {
        return false;
    }

    @Override
    public boolean allowZeroInvertedRateTolerance()
    {
        return true;
    }

    public Double getRequestedBidSpotRate() {
        return requestedBidSpotRate;
    }

    public void setRequestedBidSpotRate(Double requestedBidSpotRate) {
        this.requestedBidSpotRate = requestedBidSpotRate;
    }

    public Double getRequestedOfferSpotRate() {
        return requestedOfferSpotRate;
    }

    public void setRequestedOfferSpotRate(Double requestedOfferSpotRate) {
        this.requestedOfferSpotRate = requestedOfferSpotRate;
    }

    public Double getPipsDeviation() {
        return pipsDeviation;
    }

    public void setPipsDeviation(Double pipsDeviation) {
        this.pipsDeviation = pipsDeviation;
    }

	@Override
	public FilterStats getFilterStats() {
		return stats;
	}

	@Override
	public void setFilterStats(FilterStats filterStats) {
		this.stats = filterStats;
	}


}
