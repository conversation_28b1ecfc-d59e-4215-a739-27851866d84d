package com.integral.aggregation.stream;

import com.integral.aggregation.AggregationServiceUtil;
import com.integral.finance.dealing.Quote;
import com.integral.user.Organization;
import com.integral.aggregation.subscription.provider.quote.PriceObserver;
import com.integral.aggregation.subscription.provider.quote.ProviderQuoteHandler;


/**
 *
 */
public class ProviderPriceObserver implements PriceObserver<Quote>, ProviderPrice<Quote> {
	// It needs to be volatile as reference is accessed from a different thread than the one sets it
	// If synchronization is not done then other thread may not see the latest updated copy
    volatile private Quote quote;
    private ESPAggregator publisher;
    private ProviderQuoteHandler quoteHandler;
    private Organization provider;

    public ProviderPriceObserver(ESPAggregator publisher, ProviderQuoteHandler quoteHandler, Organization provider) {
        this.publisher = publisher;
        this.quoteHandler = quoteHandler;
        this.provider = provider;
    }

    public void newQuote(Quote quote) {
        if(AggregationServiceUtil.incrementRefCounter(quote) > 0){
            Quote lastQuote = this.quote;
            this.quote = quote;

            AggregationServiceUtil.decrementRefCounter(lastQuote);
            this.publisher.setProviderPriceUpdated(true);
        }
    }

    public Quote getPrice() {
        return this.quote;
    }

    public void addPrice(Quote price) {
        //To change body of implemented methods use File | Settings | File Templates.
    }

    public void removeQuote() {
        AggregationServiceUtil.decrementRefCounter(this.quote);
        boolean cachedQuoteNotNull = (quote != null);
        this.quote = null;
        if(cachedQuoteNotNull) {
            this.publisher.setProviderPriceUpdated(true);
        }
    }

    public void register() {
    	String providerName = (this.provider == null ? null : this.provider.getShortName());
        this.quoteHandler.addQuoteObserver(providerName, this); 
        if(this.publisher.getAggregationProcessor().getConfig().isCachedQuoteOnSubscription()) {
            Quote cachedQuote = quoteHandler.getCachedQuote();
            if(cachedQuote != null){
                newQuote(cachedQuote);
            }
        }
    }

    public void unRegister() {
    	String providerName = (this.provider == null ? null : this.provider.getShortName());
        this.quoteHandler.removeQuoteObserver(providerName, this);
        AggregationServiceUtil.decrementRefCounter(this.quote);

        this.quote = null;
        this.publisher = null;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder(100)
           .append(super.toString()).append(' ')
           .append(this.publisher).append(' ')
           .append(this.quoteHandler)
           .append(this.provider);

        return sb.toString();
    }


}
