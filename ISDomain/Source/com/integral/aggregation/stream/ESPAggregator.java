// Copyright (c) 2007 Integral Development Corp. All rights reserved.
package com.integral.aggregation.stream;

import com.integral.aggregation.*;
import com.integral.aggregation.AggregationServiceUtil.AGG_TYPE;
import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.config.AggregationServiceMBean;
import com.integral.aggregation.filter.AggregationFilterFactory;
import com.integral.aggregation.log.ASAggregationLogger;
import com.integral.aggregation.metrics.AggMetricsUtil.METRICS_EVENT_NAME;
import com.integral.aggregation.metrics.AggregatorMetrics;
import com.integral.aggregation.metrics.InMemoryAggregatorMetrics;
import com.integral.aggregation.price.FXPrice;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.stream.view.DynamicLadderAggregator;
import com.integral.aggregation.stream.view.LadderViewRequest;
import com.integral.aggregation.stream.view.LadderedViewAggregator;
import com.integral.aggregation.subscription.AggregationProcessorRequest;
import com.integral.aggregation.subscription.provider.quote.ProviderQuoteHandler;
import com.integral.broker.BrokerAdaptorUtil;
import com.integral.broker.aggregate.*;
import com.integral.broker.filter.BrokerFilterFactory;
import com.integral.broker.filter.PartialFillProviderFilterC;
import com.integral.broker.filter.QuoteFilterFactory;
import com.integral.broker.filter.ReferenceProviderFilterC;
import com.integral.broker.log.QuoteDropMetrics;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.Configuration;
import com.integral.broker.model.Product;
import com.integral.broker.model.Stream;
import com.integral.broker.price.PriceBook;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.subscription.SubscriptionRuleEngine;
import com.integral.taker.TakerOrganizationFunction;
import com.integral.user.Organization;
import com.integral.user.OrganizationFunction;
import com.integral.util.Tuple;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Publish quotes for a product.
 *
 * <AUTHOR> Development Corp.
 */
public class ESPAggregator implements Aggregator {
    protected Log log = LogFactory.getLog(this.getClass());

    public static final int AGG_OPERATION_ADD_PRICE_HANDLER = 0;
    public static final int AGG_OPERATION_REMOVE_PRICE_HANDLER = 1;
    public static final int AGG_OPERATION_ADD_LADDER_VIEW_REQUEST = 2;
    public static final int AGG_OPERATION_REMOVE_LADDER_VIEW_REQUEST = 3;

    protected AggregationProcessor aggregationProcessor;
    protected AggregationProcessorConfig config;
    protected AggregatorFactory factory;

    protected Product product;
    private boolean fokAggregator;
    private boolean vwapFaAggregator;
    private boolean faStream;
    private boolean dropFxiDirectStreamPrices;
    protected long publicationInterval;
    protected long bestBidOfferPublicationInterval;
    private String objectId;

    private final ReentrantLock instanceLock = new ReentrantLock();

    private volatile boolean isProviderPriceUpdated = false;
    protected volatile boolean isBestBidOfferUpdated = false;
    protected Map<Organization, ProviderPriceObserver> orgToPriceObservers;
    protected Set<Organization> priceProviders;
    protected List<Quote> providerPrices;
    private long lastPublishedTime;
    protected long lastBestBidOfferCheckedTime;
    private volatile boolean isAggregationGoingOn = false;
    private ASAggregationLogger aggregatorLogger;
    private FXPriceBookBuilder priceBookBuilder;
    private FXPriceBook lastPriceBook;
    private LadderedViewAggregator ladderViewAggregator;
    private volatile boolean isLadderViewAggregation = false;
    private QuoteAggregatorFactory aggregatorFactory;

    private InMemoryAggregatorMetrics aggMetrics = null;
    
    private List<AggregationResponseHandler<Response, FXPriceBook>> priceHandlers;

    private double bidBestRate = 0;
    private double offerBestRate = Double.MAX_VALUE;

    protected String aggregatorId;

    private volatile boolean isPendingOperationsAvailable = false;
    private List<Runnable> pendingOperationCache;
    private final ReentrantLock operationCacheLock = new ReentrantLock();

    private AtomicInteger priceHandlersCounter = new AtomicInteger(0);
    private AtomicInteger ladderViewRequestsCounter = new AtomicInteger(0);
    private AggregationProcessorRequest aggregationProcessorRequest;
    private AggregatorThread aggregatorThread;
    private FilterConfigFactory filterFactory = ASFilterFactoryC.getASFilterFactory();
    private List<Quote> copyOfQuotes;
    private QuoteDropMetrics metrics;
    private boolean clientTagSubscription;
    private String clientTag;

    public ESPAggregator(AggregationProcessor processor, AggregationProcessorRequest request) {
        this.aggregationProcessor = processor;
        this.aggregationProcessorRequest = request;
        this.product = request.getProduct();
        this.aggregatorId = request.getAggregatorId();
        this.objectId = AggregationServiceUtil.generateUniqueNewId(); 
        this.aggregatorThread = getAggregatorThread();
        this.config = this.aggregationProcessor.getConfig();
        this.factory = this.aggregationProcessor.getFactory();
        this.aggregatorLogger = this.factory.newAggregatorLogger( this );
        this.priceBookBuilder = this.factory.getPriceBookBuilder();
        this.clientTagSubscription = this.aggregationProcessorRequest.isClientTagSubscription();
        this.clientTag = this.aggregationProcessorRequest.getSubscriptionClientTag();
        this.aggregatorFactory = this.factory.newQuoteAggregatorFactory( this );
        this.ladderViewAggregator = this.factory.newLadderedViewAggregator(this);
        this.ladderViewAggregator.setTierBasedSpread( request.getTierBasedSpread() );
        QuoteAggregator aggregator = product.getConfiguration().getQuoteAggregator();
		 String aggregatorName_ = (aggregator instanceof ProxyQuoteAggregatorC) ?
				((ProxyQuoteAggregatorC) aggregator).getTarget().getAbbreviatedName() :
					aggregator.getAbbreviatedName();
        OrganizationFunction orgFunction = getBrokerOrganizationFunction() == null ? getTakerOrganizationFunction() : getBrokerOrganizationFunction();
        this.fokAggregator = isFOKAggregator(product.getConfiguration());
        this.vwapFaAggregator = isVwapFaAggregator(product.getConfiguration());
        this.faStream = product.getConfiguration().getStream().isFXIDirectStream();
        this.dropFxiDirectStreamPrices = request.dropFXIDirectStreamPrices();
        this.aggMetrics = new InMemoryAggregatorMetrics(AGG_TYPE.OA_AGGREGATION,
        												aggregatorName_, orgFunction.getOrganization().getShortName(), 
        												objectId,
        											    getConfiguration().getStream().getShortName(), 
        											    getProduct().getCurrencyPair().getName());
        this.priceHandlers = new ArrayList<AggregationResponseHandler<Response, FXPriceBook>>(3);
        this.orgToPriceObservers = new ConcurrentHashMap<Organization, ProviderPriceObserver>();
        this.providerPrices = new ArrayList<Quote>(10);
        this.pendingOperationCache = new ArrayList<Runnable>(5);
        this.copyOfQuotes = new ArrayList<Quote>();
        metrics = new QuoteDropMetrics("ESPAggregator:" + aggregationProcessor.getFiOrg().getShortName() + ":" + getCurrencyPair().getName());
    }
    
    protected AggregatorThread getAggregatorThread() {
    	return AggregatorThreadManager.
	               getInstance().getAggregatorThread();  
    }
     
    protected AggregationServiceMBean getAggregationServiceMBean() {
    	return AggregationServiceFactory.getInstance().getAggregationMBean();
    }

    /**
     * Starts publishing quotes.
     */
    public void start(long publicationInterval, long bestBidOfferPublicationInterval) {
        this.publicationInterval = publicationInterval;
        this.bestBidOfferPublicationInterval = bestBidOfferPublicationInterval;
        if (log.isInfoEnabled()) {
            StringBuilder sb = new StringBuilder(150)
                    .append(this.getClass().getName()).append(".start()")
                    .append(' ').append(this.product.getConfiguration().getStream().getName())
                    .append(' ').append(this.product.getCurrencyPair().getName())
                    .append(' ').append(this.aggregationProcessor.getFiOrg().getName())
                    .append(' ').append(this.aggregatorId)
                    .append(' ').append(publicationInterval)
                    .append(' ').append(bestBidOfferPublicationInterval)
                    .append(' ').append(this.product)
                    .append(' ').append(this.fokAggregator)
                    .append(' ').append(this.faStream)
                    .append(' ').append(this.vwapFaAggregator)
                    .append(' ').append(this.dropFxiDirectStreamPrices)
                    .append(' ').append(this.product.getConfiguration().getQuoteAggregator());
            log.info(sb.toString());
        }

        // register quote handler and price observer for a quote handler
        registerProviderPriceHandlers();

        try {
            aggregatorThread.addAggregator(this, new CallBackErrorHandler() {
                public void handleError(Exception exc) {
                    StringBuilder sb = new StringBuilder(200)
                            .append("Could not add ESPAggregator to AggregatorThread")
                            .append(' ').append(toString())
                            .append(' ').append(exc);
                    log.error(sb.toString());
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (log.isDebugEnabled()) {
            StringBuilder sb = new StringBuilder();
            sb.append("PublisherC.start() : ");
            sb.append("product ").append(getProduct()).append(' ');
            log.debug(sb.toString());
        }
        MetricsManager.instance().register(metrics);
        MetricsManager.instance().register(aggMetrics);
    }

    public void resetProviderPriceHandlers() {
        Collection<Organization> aggProviders = ISUtilImpl.getInstance().getAggregationProvidersFromLiquidityRule(config.getOrganization(), product.getCurrencyPair());
        aggProviders = SubscriptionRuleEngine.filterBasedOnSubscriptionRules(config.getLegalEntity(), this.product.getCurrencyPair(), new ArrayList<Organization>(aggProviders));
        aggProviders.addAll(ISUtilImpl.getInstance().getVenueProvidersFromLiquidityRules(config.getOrganization()));
        Set<Organization> newProviders = new HashSet<Organization>();
        if(priceProviders != null){
	        for (Organization org: aggProviders) {
	            if (!priceProviders.contains(org)) {
	                // Add provider which have been added in aggregation provider list.
	                newProviders.add(org);
	            }
	        }
	
	        for (Organization org : priceProviders) {
	            if (!aggProviders.contains(org)) {
	                // Remove provider which are no longer into aggregation provider list.
	                ProviderPriceObserver priceObserver = orgToPriceObservers.remove(org);
	                if (priceObserver != null) {
	                    priceObserver.unRegister();
	                    isProviderPriceUpdated = true;
	                }
	            }
	        }
		} else {
			newProviders = new HashSet<Organization>(aggProviders);
        }
        priceProviders = new HashSet<Organization>(aggProviders);

        for (Organization provider : newProviders) {
            addProvider(provider);
        }
    }

    protected void registerProviderPriceHandlers() {
        priceProviders = AggregationServiceUtil.getProviders(this.product, this.config);

        //apply subscription rules
        List<Organization> filteredProviders = SubscriptionRuleEngine.filterBasedOnSubscriptionRules(config.getLegalEntity(), this.product.getCurrencyPair(), new ArrayList<Organization>(priceProviders));

        priceProviders = new HashSet<Organization>(filteredProviders);
        orgToPriceObservers.clear();
        providerPrices.clear();

        for (Organization provider : priceProviders) {
            addProvider(provider);
        }
    }

    private void addProvider(Organization provider)
    {
        ProviderQuoteHandler quoteHandler = this.factory.getQuoteHandler(this.product.getCurrencyPair(), provider);
        if (quoteHandler != null) {
            ProviderPriceObserver priceObserver = this.factory.newPriceObserver(this, quoteHandler, provider);
            priceObserver.register();
            orgToPriceObservers.put(provider, priceObserver);
        }
    }

    /**
     * Stops publishing quotes.
     */
    public void stop() {
        if (log.isInfoEnabled()) {
            StringBuilder sb = new StringBuilder(150)
                    .append(this.getClass().getName()).append(".stop()")
                    .append(' ').append(this.product.getConfiguration().getStream().getName())
                    .append(' ').append(this.product.getCurrencyPair().getName())
                    .append(' ').append(this.aggregationProcessor.getFiOrg().getName())
                    .append(' ').append(this.aggregatorId)
                    .append(' ').append(publicationInterval)
                    .append(' ').append(bestBidOfferPublicationInterval)
                    .append(' ').append(this.product);
            log.info(sb.toString());
        }        

        try {
            aggregatorThread.removeAggregator(this, new CallBackErrorHandler() {
                public void handleError(Exception exc) {
                }
            });
        } catch (Exception e) {
            log.error(this + ".stop: unable to publish stale rate", e);
        }
        MetricsManager.instance().unregister(metrics);
        MetricsManager.instance().unregister(aggMetrics);
    }

    @Override
    public void cleanUp() {
        try {
            if (log.isDebugEnabled()) {
                StringBuilder sb = new StringBuilder(150)
                    .append(this.getClass().getName()).append(".cleanUp()")
                    .append(' ').append(this.product.getConfiguration().getStream().getName())
                    .append(' ').append(this.product.getCurrencyPair().getName())
                    .append(' ').append(this.aggregationProcessor.getFiOrg().getName())
                    .append(' ').append(this.orgToPriceObservers.size());

                log.debug(sb.toString());
            }

            for (ProviderPriceObserver priceCache : orgToPriceObservers.values()) {
                priceCache.unRegister();
            }

            orgToPriceObservers.clear();

            // Execute any pending operations to this Aggregator.
            if (isPendingOperationsAvailable) {
                invokePendingOperations();
            }

            priceHandlers.clear();
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(250)
                .append(this.getClass().getName()).append(".cleanUp: ")
                .append(' ').append(product.getCurrencyPair())
                .append(' ').append(exc.getMessage());
            log.error(sb.toString());
        }
    }

    public void reset(long quotePublicationInterval, long bestBidOfferPublicationInterval) {
        this.publicationInterval = quotePublicationInterval;
        this.bestBidOfferPublicationInterval = bestBidOfferPublicationInterval;
    }

    public void run() {
        aggregate();
    }

    public boolean aggregate() {
        long aggStartTimeInNanos = System.nanoTime();              
        boolean isQuoteBookUpdated = false;
        METRICS_EVENT_NAME eventName = null;
        if (isPendingOperationsAvailable) {
            isQuoteBookUpdated = config.isCachedQuoteOnSubscription();
            invokePendingOperations();
            // If no listeners are available, don't aggregate.
            if (!isAnyListenersAvailable()) return false;
        }
        isQuoteBookUpdated = isQuoteBookUpdated() || isQuoteBookUpdated;
        if (isQuoteBookUpdated) {
            doAggregation();
            eventName = METRICS_EVENT_NAME.PERFORMED_AGG;
        } else {
        	eventName = METRICS_EVENT_NAME.REJECTED_AGG;
        }
        long aggEndTimeNanos = System.nanoTime();
        long timeTakenForAggInNanos = aggEndTimeNanos - aggStartTimeInNanos;
        aggMetrics.addTimeTaken(eventName, timeTakenForAggInNanos);
        return isQuoteBookUpdated;
    }

    private void doAggregation() {        
    	long startTimeInNanos = System.nanoTime();
        instanceLock.lock();
        PriceBook priceBook = null;
        Collection<Quote> providerQuotes = null;
        try {
            aggregatorLogger.reset();

            // Get the latest provider quotes.
            providerQuotes = getQuotes();
            for(Quote aQuote : providerQuotes) { // copy all provider quotes.
                copyOfQuotes.add(aQuote);
            }
            // Filter out the bad prices, but work with a copy of quotes
            Collection<Quote> filteredProviderQuotes = filterQuotes(copyOfQuotes);

            boolean _isLadderViewAggregation = this.isLadderViewAggregation;

            // Aggregate the prices.
            if (_isLadderViewAggregation) {
                ladderViewAggregator.aggregate(filteredProviderQuotes);
            } else {
                QuoteAggregator aggregator = getConfiguration().getQuoteAggregator();
                if( DynamicLadderAggregator.class.equals(aggregator.getClass()) ) {
                    /*
                     This is a rare race condition -  AggregatorOperation sets isLadderViewAggregation to false,
                     but has not stopped the aggregator yet, and an aggregation is triggered.
                     Please visit the JIRA MTX-1115 for more details.
                    */
                    return;
                } else {
                    priceBook = getProduct().aggregateQuotes(filteredProviderQuotes, this.aggregatorFactory);
                }
            }
            if(priceBook != null){
                priceBook.setShouldOverrideLPName( clientTagSubscription );
                priceBook.setLpNameToOverride( clientTag );
            }
            long currentTime = System.nanoTime();
            long timeTakenForActualAggregation = currentTime - startTimeInNanos;
            aggMetrics.addTimeTaken(METRICS_EVENT_NAME.AGGREGATE, timeTakenForActualAggregation);

            // Publish the aggregated prices to customer.            
            if (_isLadderViewAggregation) {
                publishViews();
            } else {
                publishFullBook(priceBook);
            }

            long endTime = System.nanoTime();
            aggMetrics.addTimeTaken(METRICS_EVENT_NAME.PUBLISH, (endTime - currentTime));
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder(250)
                    .append(this.getClass().getName()).append(".doAggregation: unable to publish the aggregated view")
                    .append(' ').append(product.getCurrencyPair())
                    .append(' ').append(product.isESP());

            Configuration configuration = product.getConfiguration();

            if (configuration != null) {
                sb.append(' ').append(configuration.getShortName());
                sb.append(' ').append(configuration.getStream().getShortName());
            }

            log.error(sb.toString(), e);
            
            aggMetrics.incrementExceptionCount();
        } finally {
            if(providerQuotes != null) {
                AggregationServiceUtil.decrementRefCounter(providerQuotes);
            }
            copyOfQuotes.clear();
            instanceLock.unlock();
            lastPublishedTime = System.currentTimeMillis();
            isAggregationGoingOn = false;
        }
    }

    private void publishViews() {    	
        ladderViewAggregator.publish(objectId, isBestBidOfferUpdated); 
        long logStartTime = System.nanoTime();
        ladderViewAggregator.log();
        long logEndTime = System.nanoTime();
        aggMetrics.addTimeTaken(METRICS_EVENT_NAME.LOG, (logEndTime - logStartTime));
    }

    protected void publishFullBook(PriceBook prices) {        
        FXPriceBook priceBook = priceBookBuilder.build(prices, product, aggregatorFactory);
        boolean isFiltered = isFiltered(priceBook);
        if (!isFiltered) {
            if (log.isDebugEnabled()) {
                log.debug(this + ".publishFullBook Filters dropped the deviated market rate. " + getCurrencyPair().getName() +
                        " - " + getStream().getShortName());
            }
            return;
        }

        boolean isRateMoved = isRateMoved(priceBook);
        if (!isRateMoved) {
            invokeListeners(priceBook);
            getProduct().setPricePublished(System.currentTimeMillis());
        }
        
        if (!isRateMoved) {
            aggregatorLogger.logAggregatedRate(priceBook, product, objectId, isBestBidOfferUpdated);
        } else {
            aggregatorLogger.logSkippedRate(lastPriceBook, priceBook, product);
        }        
        
        long logStartTime = System.nanoTime();
        aggregatorLogger.logData();
        long logEndTime = System.nanoTime();
        aggMetrics.addTimeTaken(METRICS_EVENT_NAME.LOG, (logEndTime - logStartTime));

        this.lastPriceBook = priceBook;
    }

    protected boolean isRateMoved(FXPriceBook currentPriceBook) {
        boolean isBestPriceQuoteAggregator = getQuoteAggregator() instanceof BestPriceQuoteAggregatorC;

        if (isBestPriceQuoteAggregator) {
            boolean isSameBookPublishingEnabled = this.config.isSamePriceBokPublishingEnabled();
            if (isSameBookPublishingEnabled) {
                boolean isSameRate = isSameAs(currentPriceBook, lastPriceBook);

                if (log.isDebugEnabled()) {
                    StringBuilder sbf = new StringBuilder(this.getClass().getName())
                            .append(' ').append(isBestPriceQuoteAggregator)
                            .append(' ').append(isSameBookPublishingEnabled)
                            .append(' ').append(isSameRate);
                    log.debug(sbf.toString());
                    return true;
                }
            }
        }

        return false;
    }

    public boolean isQuoteBookUpdated() {
        // If last aggregation going on, don't aggregate the price.
        if (isAggregationGoingOn) {
            return false;
        }

        if (isProviderPriceUpdated) {
            long lapsedTime = System.currentTimeMillis() - lastPublishedTime;
            long lapsedTimeBidOfferCheck = System.currentTimeMillis() - lastBestBidOfferCheckedTime;

            if (lapsedTime >= publicationInterval) {
                isAggregationGoingOn = true;
                isProviderPriceUpdated = false;
                isBestBidOfferUpdated = false;
                return true;
            } else if (isBestBidOfferCheckEnabled() && (lapsedTimeBidOfferCheck >= bestBidOfferPublicationInterval)) {
                isBestBidOfferUpdated = isBestBidOfferUpdated();
                if (isBestBidOfferUpdated) {
                    isAggregationGoingOn = true;
                    isProviderPriceUpdated = false;
                    return true;
                }
            } else {
                return false;
            }
        }

        return false;
    }

    protected boolean isBestBidOfferUpdated() {
        boolean isBestBidOfferUpdated = false;

        try {
            double _bidBestRate = 0;
            double _offerBestRate = Double.MAX_VALUE;

            for (ProviderPriceObserver cache : orgToPriceObservers.values()) {
                Quote quote = cache.getPrice();
                if (quote != null) {
                    if (AggregationServiceUtil.incrementRefCounter(quote) > 0) {
                        try {
                            FXLegDealingPrice bidDealingPrice = (FXLegDealingPrice) quote.getBidQuotePriceAtIndex(0);
                            FXLegDealingPrice offerDealingPrice = (FXLegDealingPrice) quote.getOfferQuotePriceAtIndex(0);

                            if (bidDealingPrice.isActive()) {
                                _bidBestRate = bidDealingPrice.getRate() > _bidBestRate ? bidDealingPrice.getRate() : _bidBestRate;
                            }

                            if (offerDealingPrice.isActive()) {
                                _offerBestRate = offerDealingPrice.getRate() < _offerBestRate ? offerDealingPrice.getRate() : _offerBestRate;
                            }
                        } finally {
                            AggregationServiceUtil.decrementRefCounter(quote);
                        }
                    }
                }
            }
            isBestBidOfferUpdated = bidBestRate != _bidBestRate || offerBestRate != _offerBestRate;

            bidBestRate = _bidBestRate;
            offerBestRate = _offerBestRate;
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(250)
                    .append(this.getClass().getName()).append(".isBestBidOfferUpdated()")
                    .append(' ').append(this.product.getCurrencyPair())
                    .append(' ').append(this.aggregationProcessor.getFiOrg())
                    .append(' ').append(exc.getMessage());
            log.error(sb.toString());
        }

        lastBestBidOfferCheckedTime = System.currentTimeMillis();
        return isBestBidOfferUpdated;
    }

    public boolean isProviderPriceUpdated() {
        return isProviderPriceUpdated;
    }

    public void setProviderPriceUpdated(boolean isUpdated) {
        isProviderPriceUpdated = isUpdated;
        //log.info(" - setNewPriceArrived " + isNewPrice);
    }

    protected QuoteAggregator getQuoteAggregator() {
        QuoteAggregator aggregator = getConfiguration().getQuoteAggregator();
        return aggregator instanceof ProxyQuoteAggregatorC ? ((ProxyQuoteAggregatorC) aggregator).getTarget() : aggregator;
    }

    private boolean isSameAs(FXPriceBook currentPriceBook, FXPriceBook lastPriceBook) {
        return lastPriceBook != null
                && currentPriceBook != null
                && isSameAs(currentPriceBook.getBid(0), lastPriceBook.getBid(0))
                && isSameAs(currentPriceBook.getOffer(0), lastPriceBook.getOffer(0));
    }

    private boolean isSameAs(FXPrice currentPrice, FXPrice lastPrice) {
        return lastPrice != null
                && currentPrice != null
                && currentPrice.getRate() == lastPrice.getRate()
                && currentPrice.getLimit() == lastPrice.getLimit();
    }

    private void invokeListeners(FXPriceBook rate) {
        try {
            for (AggregationResponseHandler<Response, FXPriceBook> handler : priceHandlers) {
                try {
                    handler.onPriceUpdate(rate);
                    aggMetrics.incrementSubscriberCount(handler.getSubscriptionKey());
                } catch (Exception exc) {
                    StringBuilder sb = new StringBuilder(150)
                    .append(this.getClass().getName()).append(".invokeListeners() customer AggregationResponseHandler has send the exception. ")
                    .append(' ').append(handler)
                    .append(' ').append(exc);
                    log.error(sb.toString());
                }
            }
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(150)
                .append(this.getClass().getName()).append(".invokeListeners() ")
                .append(' ').append(exc);
            log.error(sb.toString());
        }
    }

    // let people hook it
    protected PriceBook aggregateQuotes(Collection<Quote> providerQuotes) {
        return getProduct().aggregateQuotes(providerQuotes, this.aggregatorFactory); // per existing FIXPublisher
    }

    public QuoteAggregatorFactory getAggregatorFactory() {
        return aggregatorFactory;
    }
 
    protected Collection<Quote> getQuotes() {
        providerPrices.clear();
        for (ProviderPriceObserver cache : orgToPriceObservers.values()) {
            Quote quote = cache.getPrice();
            if (quote != null && !quote.isRejected()) {            	
                if(AggregationServiceUtil.incrementRefCounter(quote) > 0 ) {
                    providerPrices.add(quote);
                }
            } else {
                if(log.isDebugEnabled()) {
                    if(quote == null) {
                        log.debug("ESPAggregator.getQuotes() : Quote was NULL");
                    } else if(quote.isRejected()) {
                        log.debug("ESPAggregator.getQuotes() : Quote was rejected by provider. Dropping quote with GUID-> " + quote.getGUID() + "in aggregation.");
                    }
                }
            }
        }

        // Making sure till this moment all the latest quotes are used in the aggregation. After this if any new price come,
        // that will make the isProviderPriceUpdated flag true and trigger the next aggregation.
        isProviderPriceUpdated = false;
        if (logBook()) {
        	aggregatorLogger.logBook(providerPrices, this.product);
        }        
        return providerPrices;
    }
    
    protected boolean logBook() {
    	return aggregatorLogger.isDebugEnabled();
    }

    protected Collection<Quote> filterQuotes(Collection<Quote> quotes) {
        Configuration configuration = product.getConfiguration();
        Collection<Organization> allProviders = BrokerAdaptorUtil.getInstance().getAllProviders(product.getConfiguration(), product.getCurrencyPair());

        Tuple<Boolean, Boolean> venueAllowedFlags = new Tuple<Boolean, Boolean>();
        venueAllowedFlags.first = getAggregationServiceMBean().isClobSubscriptionEnabled(config.getOrganization().getShortName());
        venueAllowedFlags.second = getAggregationServiceMBean().isRiskNetSubscriptionEnabled(config.getOrganization().getShortName());

        Collection<Quote> filteredQuotes = AggregationFilterFactory.getInstance().getVenueProviderFilter().filter(quotes, venueAllowedFlags, filterFactory);

        filteredQuotes = getProduct().filterQuotes(filteredQuotes, filterFactory);

        // Filter all the partial fill providers in case of FOK aggregations.
        if (fokAggregator) {
            PartialFillProviderFilterC partialFillProviderFilter = BrokerFilterFactory.getInstance().getPartialFillProviderFilter();
            filteredQuotes = partialFillProviderFilter.filter( filteredQuotes , config.getPartialProviders(), filterFactory);
        }

        ReferenceProviderFilterC referenceProviderFilter = BrokerFilterFactory.getInstance().getReferenceProviderFilter();
        filteredQuotes = referenceProviderFilter.filter(filteredQuotes, allProviders, filterFactory);
        if( faStream ){
            filteredQuotes = QuoteFilterFactory.getInstance().getFXIDirectStreamQuoteFilter().filter(filteredQuotes, getProduct(), filterFactory);
        }
        else{
            if( fokAggregator){
                if( dropFxiDirectStreamPrices ){
                    filteredQuotes = QuoteFilterFactory.getInstance().getFXIDirectStreamQuoteFilter().filter(filteredQuotes, getProduct(), filterFactory);
                }
            }
            else{
                if(!vwapFaAggregator){
                    filteredQuotes = QuoteFilterFactory.getInstance().getFXIDirectStreamQuoteFilter().filter(filteredQuotes, getProduct(), filterFactory);
                }
            }
        }
        // Filter out the providers who do not support the term currency trades.
        if (this.aggregatorFactory.isTermCcyAggregationSupported()) {
            filteredQuotes = AggregationFilterFactory.getInstance().getTermCcyProviderFilter().filter(filteredQuotes, this.product, filterFactory);
        }
        filteredQuotes = AggregationFilterFactory.getInstance().getHourglassFilter().filter(filteredQuotes, metrics, aggregatorFactory.isHourglass(), aggregationProcessor.getFiOrg());

        return filteredQuotes;
    }


    private boolean isFOKAggregator(Configuration configuration) {
        QuoteAggregator aggregator = configuration.getQuoteAggregator() ;

        return aggregator instanceof BestPriceFOKQuoteAggregatorC || aggregator instanceof MultiTierFOKQuoteAggregatorC;
    }

    private boolean isVwapFaAggregator(Configuration configuration){
        QuoteAggregator aggregator = configuration.getQuoteAggregator() ;
        return aggregator instanceof MultiTierFullAmountAggregatorC;
    }

    protected boolean isFiltered(FXPriceBook priceBook) {
        return true;
    }

    private void log(long publicationInterval, long publicationCheckInterval) {
        StringBuilder sb = new StringBuilder(200)
                .append(this.getClass().getName()).append(" quote publication period changed from ")
                .append(this.publicationInterval).append(" to ").append(publicationInterval)
                .append(" and best bid/offer check interval from ")
                .append(this.bestBidOfferPublicationInterval).append(" to ").append(publicationCheckInterval)
                .append(" for ")
                .append(getStream().getShortName()).append(' ')
                .append(product.getCurrencyPair()).append(' ')
                .append(product.isESP() ? "ESP" : "RFS");
        log.info(sb.toString());
    }

    public Product getProduct() {
        return product;
    }

    public Configuration getConfiguration() {
        return getProduct().getConfiguration();
    }

    public String getAggregatorId() {
        return this.aggregatorId;
    }

    public List getPriceListeners() {
        try {
            return this.priceHandlers;
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(150)
                    .append(this.getClass().getName()).append(".getPriceListeners() ")
                    .append(' ').append(exc);
            log.error(sb.toString());
            return Collections.EMPTY_LIST;
        }
    }

    @Override
    public Stream getStream() {
        return getConfiguration().getStream();
    }

    @Override
    public CurrencyPair getCurrencyPair() {
        return getProduct().getCurrencyPair();
    }

    @Override
    public BrokerOrganizationFunction getBrokerOrganizationFunction() {
        return getStream().getBrokerOrganizationFunction();
    }

    @Override
    public TakerOrganizationFunction getTakerOrganizationFunction() {
        return getStream().getTakerOrganizationFunction();
    }

    @Override
    public AggregationProcessorRequest getAggregationProcessorRequest() {
        return aggregationProcessorRequest;
    }

    @Override
    public AggregationProcessor getAggregationProcessor() {
        return aggregationProcessor;
    }

    @Override
    public ASAggregationLogger getAggregatorLogger() {
        return aggregatorLogger;
    }

    public void addPriceListener(AggregationResponseHandler<Response, FXPriceBook> handler) {
        if (handler == null) return;

        AggregatorOperation operation = new AggregatorOperation(handler, new CallBackErrorHandler() {
            public void handleError(Exception exc) {
                StringBuilder sb = new StringBuilder(200)
                        .append("Could not add response handler to ESPAggregator.")
                        .append(' ').append(toString())
                        .append(' ').append(exc);
                log.error(sb.toString());
                priceHandlersCounter.decrementAndGet();
            }
        }, AGG_OPERATION_ADD_PRICE_HANDLER);

        operationCacheLock.lock();

        try {
            pendingOperationCache.add(operation);
            priceHandlersCounter.incrementAndGet();
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(100)
                    .append(this.getClass().getName()).append(".addPriceListener()")
                    .append(' ').append("Exception while adding the response handler.")
                    .append(' ').append(toString());

            log.error(sb.toString());
        } finally {
            operationCacheLock.unlock();
        }

        isPendingOperationsAvailable = true;
    }

    public boolean removePriceListener(AggregationResponseHandler<Response, FXPriceBook> handler) {
        if (handler == null) return false;

        AggregatorOperation operation = new AggregatorOperation(handler, new CallBackErrorHandler() {
            public void handleError(Exception exc) {
                StringBuilder sb = new StringBuilder(200)
                        .append("Could not remove response handler to ESPAggregator.")
                        .append(' ').append(toString())
                        .append(' ').append(exc);
                log.error(sb.toString());
            }
        }, AGG_OPERATION_REMOVE_PRICE_HANDLER);

        operationCacheLock.lock();
        try {
            pendingOperationCache.add(operation);
            priceHandlersCounter.decrementAndGet();
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(100)
                    .append(this.getClass().getName()).append(".removePriceListener()")
                    .append(' ').append("Exception while removing the response handler.")
                    .append(' ').append(toString());

            log.error(sb.toString());
        } finally {
            operationCacheLock.unlock();
        }

        isPendingOperationsAvailable = true;
        return true;
    }

    public void addViewRequest(LadderViewRequest viewReq) {
        if (viewReq == null) return;

        AggregatorOperation operation = new AggregatorOperation(viewReq, new CallBackErrorHandler() {
            public void handleError(Exception exc) {
                StringBuilder sb = new StringBuilder(200)
                        .append("Could not add ladder view request to ESPAggregator.")
                        .append(' ').append(toString())
                        .append(' ').append(exc);
                log.error(sb.toString());
                ladderViewRequestsCounter.decrementAndGet();
            }
        }, AGG_OPERATION_ADD_LADDER_VIEW_REQUEST);

        operationCacheLock.lock();
        try {
            pendingOperationCache.add(operation);
            ladderViewRequestsCounter.incrementAndGet();
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(100)
                    .append(this.getClass().getName()).append(".addViewRequest()")
                    .append(' ').append("Exception while adding the ladder view request.")
                    .append(' ').append(toString());

            log.error(sb.toString());
        } finally {
            operationCacheLock.unlock();
        }

        isPendingOperationsAvailable = true;
    }

    public void removeViewRequest(LadderViewRequest viewReq) {
        if (viewReq == null) return;

        AggregatorOperation operation = new AggregatorOperation(viewReq, new CallBackErrorHandler() {
            public void handleError(Exception exc) {
                StringBuilder sb = new StringBuilder(200)
                        .append("Could not remove ladder view request to ESPAggregator.")
                        .append(' ').append(toString())
                        .append(' ').append(exc);
                log.error(sb.toString());
            }
        }, AGG_OPERATION_REMOVE_LADDER_VIEW_REQUEST);

        operationCacheLock.lock();
        try {
            pendingOperationCache.add(operation);
            ladderViewRequestsCounter.decrementAndGet();
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(100)
                    .append(this.getClass().getName()).append(".removeViewRequest()")
                    .append(' ').append("Exception while removing the ladder view request.")
                    .append(' ').append(toString());

            log.error(sb.toString());
        } finally {
            operationCacheLock.unlock();
        }

        isPendingOperationsAvailable = true;
    }

    private void invokePendingOperations() {
        operationCacheLock.lock();
        try {
            for (Runnable operation : pendingOperationCache) {
                operation.run();
            }
            pendingOperationCache.clear();
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(100)
                    .append(this.getClass().getName()).append(".invokePendingOperations()")
                    .append(' ').append("Exception while invoking the ending operations - ")
                    .append(' ').append(exc.getMessage());
            log.error(sb.toString());
        } finally {
            operationCacheLock.unlock();
            isPendingOperationsAvailable = false;
        }
    }

    public boolean isAnyListenersAvailable() {
        return priceHandlersCounter.get() != 0 || ladderViewRequestsCounter.get() != 0;
    }

    @Override
    public String getObjectId() {
        return objectId;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder(200).append(this.getClass().getName())
                .append(' ').append(this.aggregationProcessor.getFiOrg().getShortName())
                .append(' ').append(this.getStream().getShortName())
                .append(' ').append(this.product.getCurrencyPair().getName())
                .append(' ').append(this.product.getConfiguration().getQuoteAggregator())
                .append(' ').append(this.objectId)
                .append(' ').append(this.isLadderViewAggregation)
                .append(' ').append(this.priceHandlers.size())
                .append(' ').append(this.ladderViewAggregator.consumerCount())
                .append(' ').append(this.priceHandlersCounter.get())
                .append(' ').append(this.ladderViewRequestsCounter.get());

        if (priceProviders != null) {
            sb.append(' ').append('(').append(' ');
            for (Organization org : this.priceProviders) {
                sb.append(' ').append(org.getShortName()).append(',');
            }
            sb.append(' ').append(')').append(' ');
        }
        return sb.toString();
    }

    public boolean isBestBidOfferCheckEnabled() {
        return bestBidOfferPublicationInterval > 0 && bestBidOfferPublicationInterval < publicationInterval;
    }

    private class AggregatorOperation implements Runnable {
        private AggregationResponseHandler<Response, FXPriceBook> responseHandler;
        private LadderViewRequest viewReq;
        private CallBackErrorHandler handler;
        private int action;

        private AggregatorOperation(AggregationResponseHandler<Response, FXPriceBook> responseHandler, CallBackErrorHandler handler, int action) {
            this.responseHandler = responseHandler;
            this.handler = handler;
            this.action = action;
        }

        private AggregatorOperation(LadderViewRequest viewReq, CallBackErrorHandler handler, int action) {
            this.viewReq = viewReq;
            this.handler = handler;
            this.action = action;
        }

        public void run() {
            try {
                if (log.isDebugEnabled()) {
                    StringBuilder sb = new StringBuilder(150)
                            .append(this.getClass().getName())
                            .append(' ').append(action)
                            .append(' ').append(responseHandler)
                            .append(' ').append(viewReq)
                            .append(' ').append(handler);
                    log.debug(sb.toString());
                }

                switch (action) {
                    case AGG_OPERATION_ADD_PRICE_HANDLER:
                        priceHandlers.add(responseHandler);
                        break;

                    case AGG_OPERATION_REMOVE_PRICE_HANDLER:
                        priceHandlers.remove(responseHandler);
                        break;

                    case AGG_OPERATION_ADD_LADDER_VIEW_REQUEST:
                        ladderViewAggregator.addConsumer(viewReq);
                        isLadderViewAggregation = true;
                        break;

                    case AGG_OPERATION_REMOVE_LADDER_VIEW_REQUEST:
                        ladderViewAggregator.removeConsumer(viewReq);

                        if (ladderViewAggregator.hasNoConsumers()) {
                            isLadderViewAggregation = false;
                        }
                        break;
                    default:
                }
            } catch (Exception exc) {
                StringBuilder sb = new StringBuilder(150)
                        .append(this.getClass().getName())
                        .append(' ').append(action)
                        .append(' ').append(responseHandler)
                        .append(' ').append(ladderViewAggregator);
                log.error("Exception while handling the aggregator operation. " + sb.toString());
                handler.handleError(exc);
            }
        }
    }

	@Override
	public com.integral.aggregation.stream.metrics.AggregatorMetrics getMetrics() {
		// TODO Auto-generated method stub
		return null;
	}

    @Override
    public AggregationProcessorConfig getConfig(){
        return config;
    }

    public Map<Organization, ProviderPriceObserver> getOrgToPriceObservers() {
        return orgToPriceObservers;
    }
}
