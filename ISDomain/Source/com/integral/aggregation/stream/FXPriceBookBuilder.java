package com.integral.aggregation.stream;

import java.util.Collection;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.AggregationType;
import com.integral.aggregation.AggregatorFactory;
import com.integral.aggregation.price.FXPrice;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.price.SwapFXPriceBook;
import com.integral.broker.aggregate.QuoteAggregator;
import com.integral.broker.aggregate.QuoteAggregatorFactory;
import com.integral.broker.aggregate.RawFullBookQuoteAggregatorC;
import com.integral.broker.model.Product;
import com.integral.broker.price.Price;
import com.integral.broker.price.PriceBook;
import com.integral.broker.price.SwapPriceC;
import com.integral.is.common.ProviderQuoteIdFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;

/**
 *
 */
public class FXPriceBookBuilder {
    protected AggregationProcessor service;
    protected AggregatorFactory factory;
    protected Organization brokerOrg;

    public FXPriceBookBuilder(AggregationProcessor service) {
        this.service = service;
        this.factory = this.service.getFactory();
        this.brokerOrg = this.service.getConfig().getOrganization();
    }

    public FXPriceBook build(PriceBook prices, Product product, QuoteAggregatorFactory aggregatorFactory) {    	
        FXPriceBook priceBook = null;
    	//boolean isSwap = isSwap(bids, offers);
    	boolean isSwap = isSwap(aggregatorFactory);
        int maxTiers = countTiers(prices);        		
        // generate the provider quote id
        final String quoteId = getQuoteId();
        QuoteAggregator aggregator = product.getConfiguration().getQuoteAggregator();

        boolean isRawAggregator = aggregator.getAbbreviatedName().equals(
                AggregationType.RawFullBookQuoteAggregation.getAggregator().getAbbreviatedName());

    	/**
    	 * Calling getBids() and getOffers() multiple times not a good idea , because some of the PriceBook implementations do sorting 
    	 * when these methods are called so better to capture in a variable and use it.
    	 */
        if (maxTiers == 0) {
            priceBook = this.factory.newPriceBook(isSwap);
            priceBook.setStale(true);
        } else { 
           	Collection<Price> bids = prices.getBids();
        	Collection<Price> offers = prices.getOffers();
            priceBook = this.factory.newPriceBook(isSwap, bids.size(), offers.size());

            // set amounts and rates
            for (Price bid : bids) {
                FXPrice price = this.factory.newFXPrice();
                populateNearLeg(aggregatorFactory, bid, price);
                //price.setLimit(getPriceLimit(aggregatorFactory, bid));                
                Organization provider = getProvider(bid, isRawAggregator);
                price.setProvider(provider);
                String lpName = null;
                if(prices.isTaggedSubscription()){
                    lpName =prices.getLpNameToOverride();
                    price.setLimit( 0 );
                }else{
                    lpName = getLPName(aggregator, bid);
                }
                price.setLPName( lpName );
                int tier = (isRawAggregator ? bid.getTier() : 0);
                price.setTier(tier);
                int lpTier = getLPTier(bid);
                price.setLPTier(lpTier);
                String providerQuoteId = getProviderQuoteId(bid);
                if (providerQuoteId != null) {
                	price.setQuoteId(providerQuoteId);
                }   
                // populate expiry time for RFS    
                populateExpiryTime(bid, priceBook);
                if (isSwap) {
                	priceBook.addOffer(price);
                } else {
                	priceBook.addBid(price);
                }               
                
                if (isSwap) {
                	price = this.factory.newFXPrice();
                	populateFarLeg(aggregatorFactory, bid, price);  
                    //price.setLimit(getFarPriceLimit(aggregatorFactory, (SwapPriceC)bid));
                    price.setProvider( provider );
                    price.setLPName( lpName );
                    price.setTier( tier );
                    price.setLPTier(lpTier);
                    if (providerQuoteId != null) {
                    	price.setQuoteId(providerQuoteId);
                    }   
                    ((SwapFXPriceBook)priceBook).addFarBid(price);
                }
            }

            for (Price offer : offers) {
                FXPrice price = this.factory.newFXPrice();
                populateNearLeg(aggregatorFactory, offer, price); 
                //price.setLimit(getPriceLimit(aggregatorFactory, offer));                
                Organization provider = getProvider(offer, isRawAggregator);
                price.setProvider(provider);
                int tier = isRawAggregator ? offer.getTier() : 0;
                price.setTier(tier);
                String lpName = null;
                if(prices.isTaggedSubscription()){
                    lpName =prices.getLpNameToOverride();
                    price.setLimit( 0 );
                }else{
                    lpName = getLPName(aggregator, offer);
                }
                price.setLPName(lpName);
                int lpTier = getLPTier(offer);
                price.setLPTier(lpTier);
                String providerQuoteId = getProviderQuoteId(offer);
                price.setQuoteId(providerQuoteId);
                // populate expiry time for RFS
                populateExpiryTime(offer, priceBook);
                if (isSwap) {
                	priceBook.addBid(price);
                } else {
                	 priceBook.addOffer(price);
                }               
                
                if (isSwap) {
                	price = this.factory.newFXPrice();  
                	populateFarLeg(aggregatorFactory, offer, price);
                    //price.setLimit(getFarPriceLimit(aggregatorFactory, (SwapPriceC)offer));
                    price.setProvider(provider);
                    price.setTier(tier);
                    price.setLPName(lpName);
                    price.setLPTier(lpTier);
                    if (providerQuoteId != null) {
                    	price.setQuoteId(providerQuoteId);
                    }   
                    ((SwapFXPriceBook)priceBook).addFarOffer((price));
                }
            }

            // flag rate as multi-tier or multi-price
            priceBook.setMultiTier(prices.isMultiTier());
        }

        priceBook.setBookId(quoteId);
        priceBook.setSequenceNo(aggregatorFactory.getNextSequenceNumber());
        priceBook.setOrganization(this.brokerOrg);
        priceBook.setCurrencyPair(product.getCurrencyPair());
        priceBook.setValueDate( aggregatorFactory.getValueDate() );
        priceBook.setValueDateMilliseconds(aggregatorFactory.getValueDateMilliseconds());
        return priceBook;
    }
  
    
    protected void populateExpiryTime(final Price price, final FXPriceBook priceBook) {
    	// Do nothis as we dont want expiry for ESP
    }
    
    protected boolean isSwap(final QuoteAggregatorFactory aggregatorFactory) {
    	return false;
    }
    
    protected Organization getProvider(final Price price, final boolean rawAggregator) {
    	return rawAggregator ? price.getOrganization() : this.brokerOrg;
    }
    
    protected void populateNearLeg(final QuoteAggregatorFactory aggregatorFactory, final Price price, final FXPrice fxPrice) {
    	fxPrice.setRate(price.getRate());
    	fxPrice.setLimit(getPriceLimit(aggregatorFactory, price));
    }
 
    
    protected void populateFarLeg(final QuoteAggregatorFactory aggregatorFactory, final Price farBid, final FXPrice price) {
    	// do nothing as ESP does not stream outright and swap
    }
 
      
    protected String getProviderQuoteId(final Price price) {    	
    	return null;
    }
    
    private boolean isSwap(final Collection<Price> bids, final Collection<Price> offers) {
    	if (bids.size() == 0 && offers.size() == 0) {
    		return false;
    	}
    	
    	for (Price price : bids) {
    		if (price != null) {
    			return price instanceof SwapPriceC;
    		}
    	}
    	
    	for (Price price : offers) {
     		if (price != null) {
    			return price instanceof SwapPriceC;
    		}
    	}
    	
    	return false;
    }

    private double getPriceLimit(QuoteAggregatorFactory aggregatorFactory, Price price) {
        if(aggregatorFactory.isTermCcyAggregationSupported()){
            if(this.service.getConfig().isTermAggregationDisplayAmtInBase()){
                if( aggregatorFactory.isBaseCcyAmtRoundingUsingTickValue()) {
                    return aggregatorFactory.getBaseCurrency().round(price.getAmount());
                }
                return (long)aggregatorFactory.applyRoundingFactorToAmount(price.getAmount());
            }else{
                if( aggregatorFactory.isTermCcyAmtRoundingUsingTickValue()) {
                    return aggregatorFactory.getVariableCurrency().round(aggregatorFactory.getAmountOfVariable(price.getAmount()));
                }
                return (long)aggregatorFactory.applyRoundingFactorToAmount(Math.round(aggregatorFactory.getAmountOfVariable(price.getAmount())));
            }
        }
        else{
            if( aggregatorFactory.isBaseCcyAmtRoundingUsingTickValue()) {
                return aggregatorFactory.getBaseCurrency().round(price.getAmount());
            }
            return (long) price.getAmount();
        }
    }
    
    
    private long getFarPriceLimit(QuoteAggregatorFactory aggregatorFactory, SwapPriceC price) {
        if(aggregatorFactory.isTermCcyAggregationSupported()){
            if(this.service.getConfig().isTermAggregationDisplayAmtInBase()){
                return (long)aggregatorFactory.applyRoundingFactorToAmount(price.getFarAmount());
            }else{
                return (long)aggregatorFactory.applyRoundingFactorToAmount(Math.round(aggregatorFactory.getAmountOfVariable(price.getFarAmount())));
            }
        }else{
            return (long)price.getFarAmount();
        }        
    }


    protected String getLPName(QuoteAggregator aggregator, Price price) {
        if (price.getOrganization() != null && price.getOrganization().isFMALP()) { // try to get org and check if its FMA
            String displayName = getDisplayNameForFMAProvider();
            if (displayName != null) return displayName;
        }
        return (this.service.getConfig().isProviderNameDisplayEnabled() || aggregator instanceof RawFullBookQuoteAggregatorC) ?
                getDisplayNameForFMA(price.getLPName()) : this.brokerOrg.getShortName();
    }

    protected String getDisplayNameForFMAProvider() {
        return this.service.getConfig().getDisplayNameForFMAProviders();        
    }
    protected String getDisplayNameForFMA(String lpShortName) {
        String result = lpShortName;
        if (lpShortName != null) {
            int index = lpShortName.indexOf('+');
            if (index == -1) {
                Organization lpOrg = ReferenceDataCacheC.getInstance().getOrganization(lpShortName);
                if ((lpOrg != null) && lpOrg.isFMALP()) {
                    String displayName = this.service.getConfig().getDisplayNameForFMAProviders();
                    if (displayName != null) return displayName;
                }

            } else {
                lpShortName = lpShortName.substring(0, index);
                Organization lpOrg = ReferenceDataCacheC.getInstance().getOrganization(lpShortName);
                if ((lpOrg != null) && lpOrg.isFMALP()) {
                    String displayName = this.service.getConfig().getDisplayNameForFMAProviders();
                    if (displayName != null) return displayName + '+';
                }
            }
        }
        return result;
    }
    /**
     * Returns specific tier used by provider to construct the price.
     * In case of multiple providers, tier is set to 0.
     *
     * @param price price
     * @return  LP tier
     */
    private int getLPTier( Price price )
    {
        int tier;
        if ( !price.isMultiProviderPrice() ) {
            //For multi-tier provider, increment tier by 1 since tier starts from 0.
            tier = price.isMultiTierProviderPrice() ? price.getTier() + 1 : 0;
        }
        else {
            tier = 0;
        }
        return tier;
    }

    protected String getQuoteId() {
        return ProviderQuoteIdFactory.newProviderQuoteId(this.brokerOrg.getShortName(), System.currentTimeMillis());
    }

    private int countTiers(PriceBook prices) {
        final int tiers;
        if (prices == null) {
            tiers = 0;
        } else {
            int numBids = prices.getBids().size();
            int numOffers = prices.getOffers().size();
            tiers = Math.max(numBids, numOffers);
        }
        return tiers;
    }
    
    private int countTiers(final Collection<Price> bids, final Collection<Price> offers) {
        final int tiers;
        if (bids.size() == 0 && offers.size() == 0) {
            tiers = 0;
        } else {
            int numBids = bids.size();
            int numOffers = offers.size();
            tiers = Math.max(numBids, numOffers);
        }
        return tiers;
    }
}
