package com.integral.aggregation.stream.view;

import com.integral.broker.aggregate.MultiTierAverageQuoteAggregatorC;
import com.integral.broker.model.Product;
import com.integral.broker.model.Tier;
import com.integral.broker.price.Price;
import com.integral.broker.price.PriceBook;

import java.util.Collection;

/*
	Does not use product.configuration.tiers, has a local collection of Tiers.
	super{} Tiers are per ccy pair but Configuration is shared over {ccyPairs}
	=> either have Configuration per ccyPair or have 'override' aggregator with tiers.

	Local Tier instances may (should) contain the FanOut instances that need that Tier.
	Overridden add price methods also add the price to a working PriceBook on the FanOut.
*/
public class DynamicLadderAggregator extends MultiTierAverageQuoteAggregatorC {
    boolean debug = false;
    public Collection<? extends Tier> tiers;
    public Price lastTierBidPrice, lastTierOfferPrice;

    protected void addBidToFor(Price p, PriceBook pb, Tier t) {
        super.addBidToFor(p, pb, t);
        if (t instanceof DynamicTier) {
            lastTierBidPrice = p;
            DynamicTier dt = (DynamicTier) t;
            for (FanOut fo : dt.getFanOuts()) {
                fo.addBid(p);
            }
        }
    }

    protected void addOfferToFor(Price p, PriceBook pb, Tier t) {
        super.addOfferToFor(p, pb, t);
        if (t instanceof DynamicTier) {
            lastTierOfferPrice = p;
            DynamicTier dt = (DynamicTier) t;
            for (FanOut fo : dt.getFanOuts()) {
                fo.addOffer(p);
            }
        }
    }

    protected void addBidToForUnFilledTier(PriceBook cooked, Tier tier) {
        if (tier instanceof DynamicTier) {
            DynamicTier dt = (DynamicTier)tier;
            if (lastTierBidPrice == null) return;

            for (FanOut fo : dt.getFanOuts()) {
                double foLastTierAmt = fo.lastTierBidPrice == null ? 0.0 : fo.lastTierBidPrice.getAmount();
                if (lastTierBidPrice.getAmount() > foLastTierAmt)
                    fo.pricesWorking.addBid(lastTierBidPrice);
            }

            if (log.isDebugEnabled()) {
                StringBuilder sb = new StringBuilder(200);
                sb.append(this.getClass().getName()).append(' ').append("addBidToForUnFilledTier()")
                .append(' ').append(lastTierBidPrice)
                .append(' ').append(lastTierBidPrice.getAmount());

                for (FanOut fo : dt.getFanOuts()) {
                    sb.append(' ').append(fo.lastTierBidPrice);
                    double foLastTierAmt = fo.lastTierBidPrice == null ? 0.0 : fo.lastTierBidPrice.getAmount();
                    sb.append(' ').append(foLastTierAmt);
                    sb.append(' ').append(fo.pricesWorking.getBids());
                }
            }
        }
    }

    protected void addOfferToForUnFilledTier(PriceBook cooked, Tier tier) {
        if (tier instanceof DynamicTier) {
            DynamicTier dt = (DynamicTier)tier;
            if (lastTierOfferPrice == null) return;

            for (FanOut fo : dt.getFanOuts()) {
                double foLastTierAmt = fo.lastTierOfferPrice == null ? 0.0 : fo.lastTierOfferPrice.getAmount();
                if (lastTierOfferPrice.getAmount() > foLastTierAmt)
                    fo.pricesWorking.addOffer(lastTierOfferPrice);
            }

            if (log.isDebugEnabled()) {
                StringBuilder sb = new StringBuilder(200);
                sb.append(this.getClass().getName()).append(' ').append("addOfferToForUnFilledTier()")
                .append(' ').append(lastTierOfferPrice)
                .append(' ').append(lastTierOfferPrice.getAmount());

                for (FanOut fo : dt.getFanOuts()) {
                    sb.append(' ').append(fo.lastTierOfferPrice);
                    double foLastTierAmt = fo.lastTierOfferPrice == null ? 0.0 : fo.lastTierOfferPrice.getAmount();
                    sb.append(' ').append(foLastTierAmt);
                    sb.append(' ').append(fo.pricesWorking.getOffers());
                }
            }
        }
    }

    public Collection<? extends Tier> getTiers(Product p) {
        return tiers;
    }
}
