package com.integral.aggregation.stream.view;

import com.integral.aggregation.AggregationServiceUtil;
import com.integral.aggregation.log.ASAggregationLogger;
import com.integral.aggregation.model.TierBasedSpread;
import com.integral.aggregation.stream.ESPAggregator;
import com.integral.broker.aggregate.QuoteAggregatorFactory;
import com.integral.broker.model.Product;
import com.integral.broker.model.Tier;
import com.integral.broker.price.PriceBook;
import com.integral.broker.price.PriceListC;
import com.integral.categorization.StreamCategory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.stream.FXPriceBookBuilder;
import com.integral.user.Organization;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 *
 */
public class LadderedViewAggregator {
    protected Log log = LogFactory.getLog(this.getClass());

    protected ASAggregationLogger logger;
    protected ESPAggregator espAggregator;
    private QuoteAggregatorFactory aggregatorFactory;
    private FXPriceBookBuilder priceBookBuilder;
    protected Product product;

    // each set of Tiers has it's own set of Publishers
    ConcurrentHashMap<SortedSet<Double>, FanOut> consumers = new ConcurrentHashMap<SortedSet<Double>, FanOut>();

    // the superset of all required Tiers and an Aggregator to hold them
    Collection<? extends Tier> dTiers = null;
    DynamicLadderAggregator dlAgg = new DynamicLadderAggregator();
    TierBasedSpread tierBasedSpread;

    public LadderedViewAggregator(ESPAggregator espAggregator) {
        this.espAggregator = espAggregator;
        this.product = espAggregator.getProduct();
        this.aggregatorFactory = espAggregator.getAggregatorFactory();
        this.logger = espAggregator.getAggregatorLogger();
        this.priceBookBuilder = espAggregator.getAggregationProcessor().getFactory().getPriceBookBuilder();
    }

    private PriceBook newPriceBook(QuoteAggregatorFactory aggregatorFactory) {
        return new PriceListC(aggregatorFactory);
    }

    boolean debugAgg = false;

    public void aggregate(Collection<Quote> providerQuotes) {
        // here make new working price book for the fan outs
        for (FanOut f : consumers.values()) {
            f.pricesWorking = newPriceBook(this.aggregatorFactory);
            //VWAP book type should be set as multi-tier
            f.pricesWorking.setMultiTier( true );
            f.lastTierBidPrice = f.lastTierOfferPrice = null;
        }

        // Tiers may have been invalidated by an 'update'
        dlAgg.tiers = getDTiers();
        //if( debugAgg ) ystem.out.println("NJB PDL::aggregateQuotes("+providerQuotes+") dlAgg.tiers: " + stringDTiers(dlAgg.tiers));
        // Reset last bid/offer prices.
        dlAgg.lastTierBidPrice = dlAgg.lastTierOfferPrice = null;

        // note there has to be no internal double dispatch stuff which might 'loose' the locally tweaked tiers !!!
        dlAgg.aggregate(providerQuotes, product, aggregatorFactory);
        //if( debugAgg ) ystem.out.println("NJB PDL::aggregateQuotes("+providerQuotes+") aggedBook: " + aggedBook);
    }

    public void publish(String aggregatorId, boolean isBestBidOfferUpdated) {
        // now promote the working prices to the current prices
        for (FanOut f : consumers.values()) {
            FXPriceBook priceBook = this.priceBookBuilder.build(f.pricesWorking, product, aggregatorFactory);
            f.publish(priceBook);
            logger.logAggregatedRate(priceBook, product, aggregatorId, isBestBidOfferUpdated);
        }
    }

    public void log() {
        logger.logData();
    }

    // consumer management
    // todo maybe we can do more focused invalidation of Tiers, are there synchro issues here - many clients could invalidate....
    public void addConsumer(LadderViewRequest viewRequest) {
        debugConsumerState("addConsumer()");

        FanOut fanOut = consumers.get(viewRequest.tiers);
        if (fanOut == null) {
            consumers.putIfAbsent(viewRequest.tiers, new FanOut());
            fanOut = consumers.get(viewRequest.tiers);
        }
        fanOut.addConsumer(viewRequest);
        dTiers = null;

        debugConsumerState("addConsumer()");
    }

    public void removeConsumer(LadderViewRequest viewRequest) {
        debugConsumerState("addConsumer()");

        FanOut fanOut = consumers.get(viewRequest.tiers);
        if (fanOut != null) {
            fanOut.removeConsumer(viewRequest);
            if (fanOut.hasNoConsumers()) {
                consumers.remove(viewRequest.tiers);
            }
            dTiers = null;
        }

        debugConsumerState("addConsumer()");
    }

    public boolean hasNoConsumers() {
        for (FanOut f : consumers.values()) {
            if (!f.hasNoConsumers()) return false;
        }
        return true;
    }

    public int consumerCount() {
        int c = 0;
        for (FanOut f : consumers.values()) {
            c += f.consumerCount();
        }
        return c;
    }

    // Dynamic Tier management
    public Collection<? extends Tier> getDTiers() {
        if (dTiers == null) {
            dTiers = computeDTiers();
        }
        return dTiers;
    }

    // Each Tier will be given a reference to the FanOut instances that want that Tier
    public Collection<? extends Tier> computeDTiers() {
        if (log.isDebugEnabled()) log.debug("ProcessorDynamicLadder::computeDTiers()");
        Organization organization = espAggregator.getConfig().getOrganization();
        CurrencyPair ccyPair = product.getCurrencyPair();
        boolean isStreamCategoryEnabled = AggregationServiceUtil.isStreamCategoryProviderPriorityEnabled(organization, ccyPair);
        TreeMap<Double, DynamicTier> t2dt = new TreeMap<Double, DynamicTier>();
        for (Map.Entry<SortedSet<Double>, FanOut> tiers2fanOut : consumers.entrySet()) {
            if (log.isDebugEnabled())
                log.debug("ProcessorDynamicLadder::computeDTiers() {}-> fo: {" + tiers2fanOut.getKey() + "} -> " + stringFanOut(tiers2fanOut.getValue()));
            if(tierBasedSpread != null){
                for (Double t : tiers2fanOut.getKey()) {
                    if (log.isDebugEnabled()) log.debug("ProcessorDynamicLadder::computeDTiers()  T t: " + t);
                    DynamicTier dt = t2dt.get(t);
                    if (log.isDebugEnabled())
                        log.debug("ProcessorDynamicLadder::computeDTiers() T found: " + stringDynamicTier(dt));
                    if (dt == null) {
                        dt = new DynamicTier();
                        dt.setBidLimit(t);
                        dt.setOfferLimit( t );
                        if(isStreamCategoryEnabled) {
                            setTierBasedStreamCategory(dt, t, organization, ccyPair);
                        }
                        TierBasedSpread.TierAndSpread spread = tierBasedSpread.getSpread( t );
                        if(spread != null){
                            dt.setSpreadFixedOffer( spread.getSpread() );
                            dt.setSpreadFixedBid( spread.getSpread() );
                        }
                        t2dt.put(t, dt);
                    }
                    dt.addFanOut(tiers2fanOut.getValue());

                    if (log.isDebugEnabled())
                        log.debug("ProcessorDynamicLadder::computeDTiers() T t->dt: " + t + " -> " + stringDynamicTier(dt));
                }
            }else{
                for (Double t : tiers2fanOut.getKey()) {
                    if (log.isDebugEnabled()) log.debug("ProcessorDynamicLadder::computeDTiers() t: " + t);
                    DynamicTier dt = t2dt.get(t);
                    if (log.isDebugEnabled())
                        log.debug("ProcessorDynamicLadder::computeDTiers() found: " + stringDynamicTier(dt));
                    if (dt == null) {
                        dt = new DynamicTier();
                        dt.setBidLimit(t);
                        dt.setOfferLimit( t );
                        if(isStreamCategoryEnabled) {
                            setTierBasedStreamCategory(dt, t, organization, ccyPair);
                        }
                        t2dt.put(t, dt);
                    }
                    dt.addFanOut(tiers2fanOut.getValue());

                    if (log.isDebugEnabled())
                        log.debug("ProcessorDynamicLadder::computeDTiers() t->dt: " + t + " -> " + stringDynamicTier(dt));
                }
            }

        }
        return t2dt.values();
    }

    // debug / printing state
    public void debugConsumerState(String note) {
        if (!log.isDebugEnabled()) return;
        log.debug("ProcessorDynamicLadder::" + note);
        for (Map.Entry me : consumers.entrySet()) {
            log.debug("    " + stringSet((SortedSet<Double>) me.getKey()) + "->" + (stringFanOut((FanOut) me.getValue())));
        }
    }

    public String stringSet(SortedSet<Double> ssd) {
        String s = "";
        for (Double d : ssd) {
            s += ((s.length() != 0) ? ", " : "") + d;
        }
        return "{" + s + "}";
    }

    public String stringFanOut(FanOut fo) {
        String s = "";
        for (LadderViewRequest cpf : fo.consumers) {
            s += ((s.length() != 0) ? ", " : "") + cpf;
        }
        return "(" + s + ")";
    }

    public String stringDTiers(Collection dts) {
        String s = "";
        for (Object dto : dts) {
            DynamicTier dt = (DynamicTier) dto;
            s += ((s.length() != 0) ? ", " : "") + stringDynamicTier(dt);
        }
        return "{" + s + "}";
    }

    public String stringDynamicTier(DynamicTier dt) {
        if (dt == null) return "null";

        String s = "";
        for (FanOut fo : dt.fanOuts)
            s += ((s.length() != 0) ? ", " : "") + stringFanOut(fo);
        return "[" + dt.getOfferLimit() + ", " + dt.getBidLimit() + ": " + s + "]";
    }

    public void setTierBasedSpread( TierBasedSpread tierBasedSpread )
    {
        this.tierBasedSpread = tierBasedSpread;
    }

    private void setTierBasedStreamCategory(Tier tier, Double tierLimit, Organization organization, CurrencyPair ccyPair) {
        ISMBean ismBean = ISFactory.getInstance().getISMBean();
        int streamCategoryFlags = 0;
        List<StreamCategory> strmCategories = ismBean.getStreamCategoriesForOrg(organization.getShortName(), ccyPair.getName(), tierLimit);
        for(StreamCategory category : strmCategories) {
            streamCategoryFlags |= category.getStreamCategoryFlag();
        }
        streamCategoryFlags |= StreamCategory.ENABLE_DISABLE_FLAG;
        tier.setTierStreamCategoryFlags(streamCategoryFlags);
    }
}
