package com.integral.aggregation.stream.view;

import com.integral.broker.price.Price;
import com.integral.broker.price.PriceBook;
import com.integral.aggregation.price.FXPriceBook;

import java.util.concurrent.CopyOnWriteArraySet;

/**
 *
 */
public class FanOut {
    // we expect low tier set changes compared to rate updates
    CopyOnWriteArraySet<LadderViewRequest> consumers = new CopyOnWriteArraySet<LadderViewRequest>();
    public PriceBook pricesWorking = null;
    public Price lastTierBidPrice, lastTierOfferPrice;

    // consumer management

    public void addConsumer(LadderViewRequest viewRequest) {
        consumers.add(viewRequest);
    }

    public void removeConsumer(LadderViewRequest viewRequest) {
        consumers.remove(viewRequest);
    }

    public boolean hasNoConsumers() {
        return consumers.isEmpty();
    }

    public int consumerCount() {
        return consumers.size();
    }

    protected void publish(FXPriceBook prices) {
        for (LadderViewRequest viewRequest : consumers)
            viewRequest.publish(prices);
    }

    public void addBid(Price p) {
        lastTierBidPrice = p;
        pricesWorking.addBid(p);
    }

    public void addOffer(Price p) {
        lastTierOfferPrice = p;
        pricesWorking.addOffer(p);
    }
}
