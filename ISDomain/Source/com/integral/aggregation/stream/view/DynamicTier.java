package com.integral.aggregation.stream.view;

import com.integral.broker.model.TierC;

import java.util.*;

/*
	holds the FanOut instances interested in the this
	used by DynamicLadderAggregator durring aggregation to distribute
	derived vwap tier prices
*/
public class DynamicTier extends TierC
{
	public Set<FanOut> fanOuts = new HashSet<FanOut>();

	public void addFanOut(FanOut f) {
		fanOuts.add(f);
	}
	public Set<FanOut> getFanOuts() {
		return fanOuts;
	}
}