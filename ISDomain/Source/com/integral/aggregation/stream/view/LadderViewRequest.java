package com.integral.aggregation.stream.view;

import com.integral.aggregation.AggregationResponseHandler;
import com.integral.aggregation.Response;
import com.integral.aggregation.price.FXPriceBook;

import java.util.SortedSet;

/**
 *
 */
public class LadderViewRequest {
    public AggregationResponseHandler<Response, FXPriceBook> handler;
    // the superset of all required Tiers and an Aggregator to hold them
    public SortedSet<Double> tiers;

    public LadderViewRequest() {
    }

    public void publish(FXPriceBook prices) {
        handler.onPriceUpdate(prices);
    }

    public AggregationResponseHandler<Response, FXPriceBook> getHandler() {
        return handler;
    }

    public void setHandler(AggregationResponseHandler<Response, FXPriceBook> handler) {
        this.handler = handler;
    }

    public SortedSet<Double> getTiers() {
        return tiers;
    }

    public void setTiers(SortedSet<Double> tiers) {
        this.tiers = tiers;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder(50)
            .append(this.getClass().getName())
            .append(' ').append(handler);

        if (tiers != null) {
            for (Double tier : tiers) {
                sb.append(' ').append(tier);
            }
        }

        return sb.toString();
    }

}
