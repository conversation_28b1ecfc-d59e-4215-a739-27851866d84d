package com.integral.aggregation.stream;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.AggregationProcessorConfig;
import com.integral.aggregation.AggregationResponseHandler;
import com.integral.aggregation.Response;
import com.integral.aggregation.log.ASAggregationLogger;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.stream.metrics.AggregatorMetrics;
import com.integral.aggregation.stream.view.LadderViewRequest;
import com.integral.aggregation.subscription.AggregationProcessorRequest;
import com.integral.broker.aggregate.QuoteAggregatorFactory;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.Configuration;
import com.integral.broker.model.Product;
import com.integral.broker.model.Stream;
import com.integral.finance.currency.CurrencyPair;
import com.integral.taker.TakerOrganizationFunction;

import java.util.List;

/**
 *  Responsible for getting the latest provider prices, filtering out the bad prices, aggregating them
 *  and broadcasting the aggregated view to all the registered price handlers.
 *
 */
public interface Aggregator extends Runnable {
    /**
     * This method will do the quote aggregation if any new provider price is available.
     *
     * @return boolean Returns true if actual aggregation happens else returns false
     */
    public boolean aggregate();

    /**
     * Tells whether Aggregator quote book is updated or not.
     * @return
     */
    boolean isQuoteBookUpdated();

    /**
     * Starts the aggregation at the defined interval if any new provider prices are available.
     *
     * @param publicationInterval
     * @param bestBidOfferPublicationInterval
     */
    void start(long publicationInterval, long bestBidOfferPublicationInterval);

    /**
     * Stops the aggregation.
     */
    void stop();

    /**
     * Reset the publication interval of aggregated views.
     *
     * @param publicationInterval
     * @param bestBidOfferPublicationInterval
     */
    void reset(long publicationInterval, long bestBidOfferPublicationInterval);

    void resetProviderPriceHandlers();

    /**
     * Adds the aggregation response handler.
     *
     * @param handler
     */
    void addPriceListener(AggregationResponseHandler<Response, FXPriceBook> handler);

    /**
     * Removes the aggregation response handler.
     * @param handler
     * @return
     */
    boolean removePriceListener(AggregationResponseHandler<Response, FXPriceBook> handler);

    /**
     * Gets the list registered aggregation response handlers.
     * @return
     */
    List getPriceListeners();

    /**
     * Adds the ladder view request.
     *
     * @param viewReq
     */
    void addViewRequest(LadderViewRequest viewReq);

    /**
     * Removes the ladder view request.
     *
     * @param viewReq
     */
    void removeViewRequest(LadderViewRequest viewReq);

    /**
     * Checks if any aggregation response handler is available to this aggregator or not.
     *
     * @return
     */
    boolean isAnyListenersAvailable();

    /**
     * Returns the product associated with this Aggregator
     *
     * @return
     */
    Product getProduct();

    /**
     * Returns the configuration associated with this Aggregator.
     * @return
     */
    Configuration getConfiguration();

    /**
     * Returns the AggregatorMetrics of this Aggregator.
     *
     * @return
     */
    AggregatorMetrics getMetrics();

    /**
     * Returns the Aggregator Id. This represents the detail representation of the Aggregator.
     * @return
     */
    String getAggregatorId();

    String getObjectId();

    void cleanUp();

    Stream getStream();

    CurrencyPair getCurrencyPair();

    BrokerOrganizationFunction getBrokerOrganizationFunction();

    TakerOrganizationFunction getTakerOrganizationFunction();

    AggregationProcessorRequest getAggregationProcessorRequest();

    AggregationProcessor getAggregationProcessor();

    ASAggregationLogger getAggregatorLogger();

    QuoteAggregatorFactory getAggregatorFactory();

    AggregationProcessorConfig getConfig();
}
