package com.integral.aggregation.stream.metrics;

import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.stream.ESPAggregator;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.user.OrganizationFunction;
import com.integral.user.OrganizationFunctionC;
import com.integral.util.MetricAbstract;
import com.integral.util.MetricLong;
import com.integral.util.Metrics;

import java.util.concurrent.ConcurrentHashMap;

/**
 *
 */
public class AggregatorMetrics extends Metrics {
    public static String AGGREGATOR_METRIC_PREFIX = "(AS)";
    public static int AGGPSECBUCKETS = 25;

    public static Log lAggPerSec;
    public static Log lAggCheckedPerSec;
    public static Log lAggTime;
    public static Log lPubTime;
    public static Log lSendTime;
    public static Log lLogTime;

    static {
        try {
            lAggCheckedPerSec = LogFactory.getLog(MetricsManager.METRICS_CATEGORY + ".aggregator.aggChecked/sec");
            lAggPerSec = LogFactory.getLog(MetricsManager.METRICS_CATEGORY + ".aggregator.agg/sec");
            lAggTime = LogFactory.getLog(MetricsManager.METRICS_CATEGORY + ".aggregator.usAgg");
            lPubTime = LogFactory.getLog(MetricsManager.METRICS_CATEGORY + ".aggregator.usPub");
            lSendTime = LogFactory.getLog(MetricsManager.METRICS_CATEGORY + ".aggregator.usSend");
            lLogTime = LogFactory.getLog(MetricsManager.METRICS_CATEGORY + ".aggregator.usAggLog");
        } catch (Exception e) {
            error("AggregatorMetrics::static exception", e);
        }
    }

    protected MetricLong mAggCheckedPerSec;
    protected MetricLong mAggPerSec;
    protected MetricLong mPubTime;
    protected MetricLong mAggTime;
    protected MetricLong mLogTime;
    protected MetricLong mSndTime;

    protected ESPAggregator aggregator;

    public AggregatorMetrics() {
        super();
        initMetrics();
    }

    public AggregatorMetrics(ESPAggregator espAggregator) {
        super();
        this.aggregator = espAggregator;
        initMetrics();
    }

    protected void initMetrics() {
        try {
            mAggPerSec = new MetricLong(AGGPSECBUCKETS);
            mAggCheckedPerSec = new MetricLong(AGGPSECBUCKETS);
            mAggTime = new MetricLong(AggregationServiceFactory.getInstance().getAggregationMBean().getAggregationTimeBuckets());
            mPubTime = new MetricLong(AggregationServiceFactory.getInstance().getAggregationMBean().getPublicationTimeBuckets());
            mSndTime = new MetricLong(AggregationServiceFactory.getInstance().getAggregationMBean().getSendTimeBuckets());
            mLogTime = new MetricLong(AggregationServiceFactory.getInstance().getAggregationMBean().getLogTimeBuckets());
        } catch (Exception e) {
            error("AggregatorMetrics::AggregatorMetrics() exception", e);
        }
    }


    public void addAggPerSecFromMilli(long milliseconds) {
        try {
            mAggPerSec.addMilliAsPerSecond(milliseconds);
        } catch (Exception e) {
            error("AggregatorMetrics::addRunPerSecFromMilli(long) exception", e);
        }
    }

    public void addAggTimeFromNano(long nanoseconds) {
        try {
            mAggTime.addNanoAsMicro(nanoseconds);
        } catch (Exception e) {
            error("AggregatorMetrics::addRunTimeFromNano(long) exception", e);
        }
    }

    public void addPubTimeFromNano(long nanoseconds) {
        try {
            mPubTime.addNanoAsMicro(nanoseconds);
        } catch (Exception e) {
            error("AggregatorMetrics::addPubTimeFromNano(long) exception", e);
        }
    }

    public void addLogTimeFromNano(long nanoseconds) {
        try {
            mLogTime.addNanoAsMicro(nanoseconds);
        } catch (Exception e) {
            error("AggregatorMetrics::addLogTimeFromNano(long) exception", e);
        }
    }

    public void addSendTimeFromNano(long nanoseconds) {
        try {
            mSndTime.addNanoAsMicro(nanoseconds);
        } catch (Exception e) {
            error("AggregatorMetrics::addSendTimeFromNano(long) exception", e);
        }
    }

    public void addAggCheckedPerSecFromMilli(long milliseconds) {
        try {
            mAggCheckedPerSec.addMilliAsPerSecond(milliseconds);
        } catch (Exception e) {
            error("AggregatorMetrics::addPubPerSecFromMilli(long) exception", e);
        }
    }

    public void add(AggregatorMetrics pm) {
        try {
            mAggPerSec.add(pm.mAggPerSec);
            mAggCheckedPerSec.add(pm.mAggCheckedPerSec);
            mAggTime.add(pm.mAggTime);
            mPubTime.add(pm.mPubTime);
            mLogTime.add(pm.mLogTime);
            mSndTime.add(pm.mSndTime);
            super.add(pm);
        } catch (Exception e) {
            error("PublisherMetrics::snapshot() exception", e);
        }
    }

    public AggregatorMetrics snapshot() {
        AggregatorMetrics snap = this;
        try {
            snap = (AggregatorMetrics)clone();
            snap.mAggPerSec = mAggPerSec.snapshot();
            snap.mAggTime = mAggTime.snapshot();
            snap.mPubTime = mPubTime.snapshot();
            snap.mLogTime = mLogTime.snapshot();
            snap.mSndTime = mSndTime.snapshot();
            snap.mAggCheckedPerSec = mAggTime.snapshot();
            snap.mAdHoc = new ConcurrentHashMap<String, MetricAbstract>(); // todo should be in super
            snapshotTo(snap);
            snap.stopMetrics();
            resetMetrics();
        } catch (Exception e) {
            error("AggregatorMetrics::snapshot() exception", e);
        }
        return snap;
    }

    protected void stopMetrics() {
        try {
            super.stop();
            mAggPerSec.stop();
            mAggTime.stop();
            mPubTime.stop();
            mLogTime.stop();
            mSndTime.stop();
            mAggCheckedPerSec.stop();
        } catch (Exception e) {
            error("AggregatorMetrics::stopMetrics() exception", e);
        }
    }

    protected void resetMetrics() {
        try {
            super.reset();
            mAggPerSec = new MetricLong(AGGPSECBUCKETS);
            mAggCheckedPerSec = new MetricLong(AGGPSECBUCKETS);
            mAggTime = new MetricLong(AggregationServiceFactory.getInstance().getAggregationMBean().getAggregationTimeBuckets());
            mPubTime = new MetricLong(AggregationServiceFactory.getInstance().getAggregationMBean().getPublicationTimeBuckets());
            mSndTime = new MetricLong(AggregationServiceFactory.getInstance().getAggregationMBean().getSendTimeBuckets());
            mLogTime = new MetricLong(AggregationServiceFactory.getInstance().getAggregationMBean().getLogTimeBuckets());
        } catch (Exception e) {
            error("AggregatorMetrics::reset() exception", e);
        }
    }

    public void logOn(Log ml) {
        try {
            if( GlobalAggregatorMetrics.lAggregator == null || !GlobalAggregatorMetrics.lAggregator.isInfoEnabled() ) return;
            if( prefix == null ) {
                prefix = aggregator != null ? getPrefix() : "";
            }
            if( lAggPerSec != null && lAggPerSec.isInfoEnabled() ) ml.info(mAggPerSec.toString(prefix + " agg/sec: "));
            if( lAggCheckedPerSec != null && lAggCheckedPerSec.isInfoEnabled() ) ml.info(mAggCheckedPerSec.toString(prefix + " aggChecked/sec: "));
            if( lPubTime != null && lPubTime.isInfoEnabled() ) ml.info(mPubTime.toString(prefix + " usPub: "));
            if( lAggTime != null && lAggTime.isInfoEnabled() ) ml.info(mAggTime.toString(prefix + " usAgg: "));
            if( lSendTime != null && lSendTime.isInfoEnabled() ) ml.info(mSndTime.toString(prefix + " usSend: "));
            if( lLogTime != null && lLogTime.isInfoEnabled() ) ml.info(mLogTime.toString(prefix + " usAggLog: "));
            super.logOn(ml);
        } catch (Exception e) {
            error("PublisherMetrics::getPrefix() exception", e);
        }
    }

    public String getPrefix() {
        if( prefix == null ){
            try {
                OrganizationFunction orgFunction = aggregator.getBrokerOrganizationFunction() == null ? aggregator.getTakerOrganizationFunction() : aggregator.getBrokerOrganizationFunction();
                prefix = makePrefix(new String[]{
                    AGGREGATOR_METRIC_PREFIX,
                    orgFunction.getOrganization().getShortName(),
                    aggregator.getConfiguration().getStream().getShortName(),
                    aggregator.getProduct().getCurrencyPair().getName()});
            } catch (Exception e) {
                error("PublisherMetrics::getPrefix() exception", e);
            }
        }
        return prefix;
    }


}
