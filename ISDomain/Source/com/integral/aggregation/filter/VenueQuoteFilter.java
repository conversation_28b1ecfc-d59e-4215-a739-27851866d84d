package com.integral.aggregation.filter;

import com.integral.aggregation.config.AggregationServiceMBean;
import com.integral.broker.aggregate.FilterConfigFactory;
import com.integral.broker.filter.FilterStats;
import com.integral.broker.filter.MultiProviderQuoteFilter;
import com.integral.broker.filter.RateFilterLogger;
import com.integral.finance.dealing.Quote;
import com.integral.tradingvenue.TradingVenueClassificationEnums;
import com.integral.tradingvenue.TradingVenueEntity;
import com.integral.user.Organization;
import com.integral.util.Tuple;

import java.util.Collection;
import java.util.Iterator;

/**
 * Created with IntelliJ IDEA.
 * User: anatarajan
 * Date: 8/19/15
 * Time: 2:00 PM
 * To change this template use File | Settings | File Templates.
 */
public class VenueQuoteFilter implements MultiProviderQuoteFilter<Quote,Tuple<Boolean,Boolean>> {

    private static final String name = "VenueQuoteFilter";

    @Override
    public Collection<Quote> filter(Collection<Quote> quotes, <PERSON><PERSON><<PERSON>ole<PERSON>,<PERSON><PERSON><PERSON>> venueAllowedFlags, FilterConfigFactory filterFactory) {
        boolean isIncludeClobInAggregation = venueAllowedFlags.first;
        boolean isIncludeRiskNetInAggregation = venueAllowedFlags.second;

        Iterator<Quote> iter = quotes.iterator();
        while(iter.hasNext()) {
            Quote quote = iter.next();
            if(quote != null) {
                if(quote.getPriceType() != Quote.PRICE_TYPE_VENUE) {
                    continue;
                }

                Organization providerOrg = quote.getOrganization();
                if(providerOrg.getTradingVenueOrgFunction() != null) {
                    TradingVenueEntity tvEntity = providerOrg.getTradingVenueOrgFunction().getTradingVenue();
                    TradingVenueClassificationEnums.VenueType venueType = tvEntity.getVenueType();

                    boolean remove = ((venueType == TradingVenueClassificationEnums.VenueType.CLOB) && !isIncludeClobInAggregation)
                            || ((venueType == TradingVenueClassificationEnums.VenueType.REX) && !isIncludeRiskNetInAggregation);

                    if(remove) {
                        iter.remove();
                        if(RateFilterLogger.getInstance().getLog().isDebugEnabled()) {
                            RateFilterLogger.getInstance().logRateDropped(name, quote, null);
                        }
                        FilterStats stats = filterFactory.getFilterStats();
                        if(stats != null) stats.addEvent(name, quote);
                    }

                }
            }
        }

        return quotes;
    }
}
