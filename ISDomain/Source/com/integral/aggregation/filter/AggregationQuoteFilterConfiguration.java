package com.integral.aggregation.filter;

import com.integral.broker.filter.QuoteFilterConfiguration;
import com.integral.broker.filter.RateFilterLoggerInf;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.liquidityProvision.LiquidityProvision;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.ISUtilImpl;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 10/17/12
 * Time: 4:07 PM
 */
public class AggregationQuoteFilterConfiguration implements QuoteFilterConfiguration {

    private static final AggregationQuoteFilterConfiguration instance = new AggregationQuoteFilterConfiguration();
    private static final ISMBean isMBean = ISFactory.getInstance().getISMBean();

    private RateFilterLoggerInf rateFilterLogger = new AggregationRateFilterLogger();

    private AggregationQuoteFilterConfiguration() {
    }

    public static final AggregationQuoteFilterConfiguration getInstance() {
        return instance;
    }

    public RateFilterLoggerInf getRateFilterLogger() {
        return rateFilterLogger;
    }

    public boolean isQuoteSizeFilterEnabled(Quote quote) {
        return isMBean.isLiquidityProvisioningEnabled(quote.getCustomerOrganization().getShortName());
    }

    public Double getMinQuoteSize(Quote quote) {
        Double currencyPairLimit = null;
        LiquidityProvision prov = ISUtilImpl.getLiquidityProvision(null, quote.getCustomerOrganization(), quote.getCurrencyPair());
        if (prov != null) {
            currencyPairLimit = prov.getHierarchicalMinQuoteSize();
        }
        return currencyPairLimit;
    }

}
