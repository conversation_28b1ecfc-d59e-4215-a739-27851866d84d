package com.integral.aggregation.filter;

import com.integral.broker.aggregate.FilterConfigFactory;
import com.integral.broker.filter.FilterStats;
import com.integral.broker.filter.MultiProviderQuoteFilter;
import com.integral.broker.filter.RateFilterLogger;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.Product;
import com.integral.broker.model.RateFilterDefinition;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.is.common.Provider;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.ProviderManagerC;
import com.integral.taker.TakerOrganizationFunction;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.Iterator;

/**
 * Filters out the providers which doesn't support the term currency trades.
 */
public class TermCurrencyTradesProviderFilter implements MultiProviderQuoteFilter<Quote, Product> {
    protected Log log = LogFactory.getLog(this.getClass());
    private static final String name = "TermCcyProvider";

    @Override
    public Collection<Quote> filter(Collection<Quote> quotes, Product product, FilterConfigFactory filterFactory) {
        CurrencyPair ccyPair = product.getCurrencyPair();
        Iterator<Quote> quotesIterator = quotes.iterator();

        while(quotesIterator.hasNext()) {
            Quote quote = quotesIterator.next();
            Provider provider = ProviderManagerC.getInstance().getProvider(quote.getOrganization().getShortName());
            BrokerOrganizationFunction bFunc = product.getConfiguration().getStream().getBrokerOrganizationFunction();
            TakerOrganizationFunction tFunc = null;
            if( bFunc == null ){
                tFunc = product.getConfiguration().getStream().getTakerOrganizationFunction();
            }
            Organization custOrg = bFunc != null ? bFunc.getOrganization() : tFunc.getOrganization();
            double regularSize = provider.getProviderConfig().getRegularSize( custOrg.getShortName(),ccyPair.getName() );
            if ( regularSize != -1 ) {
                RateFilterLogger.getInstance().logTermCcyProviderRate(name , quote , regularSize);
                FilterStats stats = filterFactory.getFilterStats();
                if(stats != null) stats.addEvent(name, quote);
                quotesIterator.remove();
            }
        }
        return quotes;
    }
}
