package com.integral.aggregation.filter;

import com.integral.broker.log.QuoteDropMetrics;
import com.integral.finance.dealing.Quote;
import com.integral.is.message.MarketRate;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.Iterator;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 8/24/15
 */
public class HourglassRateFilter {
    public Collection<Quote> filter(Collection<Quote> quotes, QuoteDropMetrics metrics, boolean hourglass, Organization fiOrg){
        if(hourglass) {
            return filterQuotesFromNonBroker(quotes, metrics, fiOrg);
        }else {
            return filterHourglassQuotes(quotes, metrics);
        }
    }

    private Collection<Quote> filterHourglassQuotes(Collection<Quote> quotes, QuoteDropMetrics metrics){
        Iterator<Quote> iterator = quotes.iterator();
        while (iterator.hasNext()){
            Quote quote = iterator.next();
            if(quote.getPriceSubType() == MarketRate.PRICE_SUBTYPE_HOURGLASS){
                iterator.remove();
                metrics.add(quote.getOrganization().getShortName(), "NON_HOURGLASS_FI:HOURGLASS_QUOTE");
            }
        }
        return quotes;
    }

    private Collection<Quote> filterQuotesFromNonBroker(Collection<Quote> quotes, QuoteDropMetrics metrics, Organization fiOrg){
        Organization broker = fiOrg.getBrokerOrganization();
        Iterator<Quote> iterator = quotes.iterator();
        while (iterator.hasNext()){
            Quote quote = iterator.next();
            if(!quote.getOrganization().isSameAs(broker)){
                iterator.remove();
                metrics.add(quote.getOrganization().getShortName(), "HOURGLASS_FI:NON_BROKER_QUOTE");
            }
        }
        return quotes;
    }
}
