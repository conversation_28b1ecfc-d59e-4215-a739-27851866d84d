package com.integral.aggregation.filter;

import com.integral.broker.filter.RateFilterLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 10/17/12
 * Time: 4:09 PM
 */
public class AggregationRateFilterLogger extends RateFilterLogger {

    @Override
    public Log getLog() {
        return LogFactory.getLog(AggregationRateFilterLogger.class);
    }
}
