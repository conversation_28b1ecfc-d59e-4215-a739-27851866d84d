package com.integral.aggregation.filter;

/**
 *
 */
public class AggregationFilterFactory {
    private static AggregationFilterFactory instance;
    private TermCurrencyTradesProviderFilter termCcyProviderFilter;
    private VenueQuoteFilter venueQuoteFilter;
    private HourglassRateFilter hourglassRateFilter;

    /**
     * SingletonHolder is loaded on the first execution of Singleton.getInstance()
     * or the first access to SingletonHolder.INSTANCE, not before.
     */
    private static class SingletonHolder {
        public static final AggregationFilterFactory instance = new AggregationFilterFactory();
    }

    public static AggregationFilterFactory getInstance() {
        return SingletonHolder.instance;
    }

    public TermCurrencyTradesProviderFilter getTermCcyProviderFilter() {
        if (termCcyProviderFilter == null) {
            termCcyProviderFilter = new TermCurrencyTradesProviderFilter();
        }
        return termCcyProviderFilter;
    }

    public VenueQuoteFilter getVenueProviderFilter() {
        if (venueQuoteFilter == null) {
            venueQuoteFilter = new VenueQuoteFilter();
        }
        return venueQuoteFilter;
    }

    public HourglassRateFilter getHourglassFilter(){
        if(hourglassRateFilter == null){
            hourglassRateFilter = new HourglassRateFilter();
        }
        return hourglassRateFilter;
    }
}
