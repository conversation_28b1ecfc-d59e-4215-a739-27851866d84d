package com.integral.aggregation;


import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import com.integral.aggregation.stream.metrics.GlobalAggregatorMetrics;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;

/**
 *
 */
public class AggregationProcessorManager {
    private Log log = LogFactory.getLog(this.getClass());
    private static AggregationProcessorManager instance = new AggregationProcessorManager();
    private ConcurrentMap<Organization, AggregationProcessor> aggregationProcessorCache = new ConcurrentHashMap<Organization, AggregationProcessor>();

    private AggregationProcessorManager() {
    }

    public static AggregationProcessorManager getInstance() {
        return instance;
    }

    public AggregationProcessor getAggregationProcessor(Organization org) {
        return aggregationProcessorCache.get(org);
    }

    
    public AggregationProcessor getOrCreateAggregationProcessor(Organization org) {
        org = ReferenceDataCacheC.getInstance().getOrganization(org.getShortName());
        AggregationProcessor aggregationProcessor  = aggregationProcessorCache.get(org);

        if (aggregationProcessor == null) {
            aggregationProcessor = newAggregatorProcessor(org);
            AggregationProcessor oldAggregationProcessor = aggregationProcessorCache.putIfAbsent(org, aggregationProcessor);

            if (oldAggregationProcessor != null) {
                aggregationProcessor = oldAggregationProcessor;
            }
            manageAggregatorThread();
        }
        return aggregationProcessor;
    }
  

    public ConcurrentMap<Organization, AggregationProcessor> getAggregationProcessorCache() {
        return aggregationProcessorCache;
    }

    public boolean removeAggregatorProcessor(Organization org) {
        org = ReferenceDataCacheC.getInstance().getOrganization(org.getShortName());
        AggregationProcessor removedProcessor = aggregationProcessorCache.remove(org);
        boolean response = false;
        if (removedProcessor != null) {
            removedProcessor.stop();
            response = true;
            manageAggregatorThread();
        }
        return response;
    }

    private void manageAggregatorThread() {
        if (aggregationProcessorCache.size() == 1) {
            GlobalAggregatorMetrics.getInstance().register();
        } else if (aggregationProcessorCache.size() == 0) {
            AggregatorThreadManager.getInstance().stopAggregatorThreads();
            GlobalAggregatorMetrics.getInstance().unRegister();
        }
    }

    private AggregationProcessor newAggregatorProcessor(Organization org) {
        return new AggregationProcessorBuilder()
                .setOrganization(org)
                .build();
    }

}
