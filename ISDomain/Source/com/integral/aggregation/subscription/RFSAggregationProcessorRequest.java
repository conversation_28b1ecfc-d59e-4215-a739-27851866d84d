package com.integral.aggregation.subscription;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

import com.integral.aggregation.subscription.provider.quote.ProviderQuoteHandler;
import com.integral.user.Organization;

/**
 *
 */
public interface RFSAggregationProcessorRequest extends AggregationProcessorRequest {
	
    void addProviderQuoteHandler(Organization provider, ProviderQuoteHandler providerQuoteHandler);
    
    ProviderQuoteHandler getProviderQuoteHandler(Organization provider);
    
    Collection<ProviderQuoteHandler> getAllProviderQuoteHandlers();
    
    Map<Organization, ProviderQuoteHandler> getAllProviderQuoteHandlersMap();
    
    void removeProviderQuoteHandler(Organization provider);
    
    void addProviders(Collection<Organization> providers);
    
    Set<Organization> getAllProviders();
}
