package com.integral.aggregation.subscription.client;

import com.integral.aggregation.*;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.rfs.RFSAggregator;
import com.integral.aggregation.stream.Aggregator;
import com.integral.aggregation.stream.view.LadderViewRequest;
import com.integral.aggregation.subscription.AggregationProcessorRequest;
import com.integral.aggregation.subscription.RFSAggregationProcessorRequest;
import com.integral.aggregation.validator.ESPAggregationRequestValidator;
import com.integral.broker.aggregate.FullBookMTAQuoteAggregatorC;
import com.integral.broker.aggregate.FullBookMTWQuoteAggregatorC;
import com.integral.broker.aggregate.QuoteAggregator;
import com.integral.broker.model.Product;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 *
 */
public class ClientSubscriptionManager {
    protected Log log = LogFactory.getLog(this.getClass());
    protected AggregationProcessor aggregationProcessor;
    protected AggregationProcessorConfig config;
    protected AggregatorFactory factory;

    protected ConcurrentMap<String, Aggregator> aggregatorCache = new ConcurrentHashMap<String, Aggregator>();
    protected ConcurrentMap<String, AggregationProcessorRequest> subscriptionRequestCache =
            								new ConcurrentHashMap<String, AggregationProcessorRequest>();

    public ClientSubscriptionManager(AggregationProcessor processor) {
        this.aggregationProcessor = processor;
        this.factory = this.aggregationProcessor.getFactory();
        this.config = this.aggregationProcessor.getFactory().getAggregationProcessorConfig();
        log.info("CLIENT_SUBSCRIPTION_MANAGER_CREATED_FOR_ORG" + aggregationProcessor.getFiOrg().getShortName());
    }

    /**
     * @param request
     * @return
     */
    public Response subscribe(AggregationProcessorRequest request) {
    	
    	if (log.isInfoEnabled()) {
            StringBuilder sb = new StringBuilder(150).append("CSM_SUBSCRIBE_START ").append("REQUEST_ID=").append(request.getRequestId());                                   
            log.info(sb.toString());
        }
    	
        Response response = request.createResponse();
        try {
            // todo: validate the subscription request.
            Product product = request.getProduct();
            AggregationResponseHandler<Response, FXPriceBook> handler = request.getPriceHandler();
            LadderViewRequest viewReq = request.getViewRequest();

            Aggregator aggregator = aggregatorCache.get(request.getAggregatorId());

            if (aggregator == null) {
                aggregator = this.factory.newESPAggregator(request);
                aggregator = aggregatorCache.putIfAbsent(request.getAggregatorId(), aggregator);

                if (aggregator == null) {
                    aggregator = aggregatorCache.get(request.getAggregatorId());
                    if(checkMultiQuoteAndHourglass(aggregator, response, request)) {
                        aggregatorCache.remove(request.getAggregatorId());
                        return response;
                    }
                    Response providerSubResponse = factory.getProviderSubManager().subscribe(aggregator);
                    response.setMsg(providerSubResponse.getMsg());

                    if (providerSubResponse.getStatus() == Response.STATUS_OK) {
                        aggregator.addPriceListener(handler);
                        aggregator.addViewRequest(viewReq);
                        subscriptionRequestCache.put(request.getRequestId(), request);
                        long quotePubInterval = request.getProduct().getConfiguration().getStream().getQuotePublicationInterval();
                        long bestBidOfferPubInterval = request.getProduct().getConfiguration().getStream().getQuotePublicationCheckInterval();
                        //aggregator.start(this.config.getQuotePublicationInterval(), this.config.getBestBidOfferPublicationInterval());
                        aggregator.start(quotePubInterval, bestBidOfferPubInterval);
                    } else {
                        aggregatorCache.remove(request.getAggregatorId());
                        response.setStatus(Response.STATUS_FAIL);
                    }
                } else {
                    if(checkMultiQuoteAndHourglass(aggregator, response, request)) return response;
                    aggregator.addPriceListener(handler);
                    aggregator.addViewRequest(viewReq);
                    subscriptionRequestCache.put(request.getRequestId(), request);
                }
            } else {
                if(checkMultiQuoteAndHourglass(aggregator, response, request)) return response;
                aggregator.addPriceListener(handler);
                aggregator.addViewRequest(viewReq);
                subscriptionRequestCache.put(request.getRequestId(), request);
            }

            if (log.isInfoEnabled()) {
                StringBuilder sb = new StringBuilder(150)
                	.append("CSM_SUBSCRIBE_SUCCESS ")
                	.append(" REQUEST_ID=").append(request.getRequestId())
                    .append(" AGGREGATOR_DETAILS=").append(aggregator)                    
                    .append(" LADDER_VIEW_REQUEST=").append(viewReq);
                log.info(sb.toString());
            }

        } catch (Exception exc) {
            Collection<Organization> effectivePriceProviders = null;
            if (request.getProduct().getConfiguration().isLiquidityGroupOnly()) {
                effectivePriceProviders = request.getProduct().getConfiguration().getLiquidityGroupPriceProviders(request.getProduct().getCurrencyPair().getName());
            } else {
                effectivePriceProviders = request.getProduct().getConfiguration().getPriceProviders();
            }
            StringBuilder sb = new StringBuilder().append("CSM_SUBSCRIBE_EXCEPTION")                
                .append(" REQUEST_ID=").append(request.getRequestId())
                .append(" STREAM_ID =").append(request.getStreamId())
                .append(" CCY_PAIR=").append(request.getProduct().getCurrencyPair().getName())
                .append(" PROVIDERS=").append(AggregationServiceUtil.getProvidersList(effectivePriceProviders));
            log.error(sb.toString(), exc);
            response.setStatus(Response.STATUS_FAIL);
        }
        return response;
    }
    private boolean checkMultiQuoteAndHourglass(Aggregator aggregator, Response response, AggregationProcessorRequest request){
        if(aggregator.getAggregatorFactory().isHourglass()){
            QuoteAggregator quoteAggregator = request.getProduct().getConfiguration().getQuoteAggregator();
            if(quoteAggregator == null) return false;
            boolean multiQuote = quoteAggregator instanceof FullBookMTAQuoteAggregatorC || quoteAggregator instanceof FullBookMTWQuoteAggregatorC;
            if(multiQuote){
                response.setStatus(Response.STATUS_FAIL);
                response.setMsg(ESPAggregationRequestValidator.ERROR_MSG_HOURGLASS_MULTI_QUOTE);
                Collection<Organization> effectivePriceProviders = null;
                if (request.getProduct().getConfiguration().isLiquidityGroupOnly()) {
                    effectivePriceProviders = request.getProduct().getConfiguration().getLiquidityGroupPriceProviders(request.getProduct().getCurrencyPair().getName());
                } else {
                    effectivePriceProviders = request.getProduct().getConfiguration().getPriceProviders();
                }
                StringBuilder sb = new StringBuilder().append("CSM_SUBSCRIBE_FAIL")
                        .append(" REASON=MultiQuote Aggregation not supported if Hourglass pricing enabled")
                        .append(" REQUEST_ID=").append(request.getRequestId())
                        .append(" STREAM_ID =").append(request.getStreamId())
                        .append(" CCY_PAIR=").append(request.getProduct().getCurrencyPair().getName())
                        .append(" AGGR=").append(quoteAggregator.getAbbreviatedName())
                        .append(" PROVIDERS=").append(AggregationServiceUtil.getProvidersList(effectivePriceProviders));
                log.info(sb.toString());
                return true;
            }
        }
        return false;
    }

    /**
     * @param request
     * @return
     */
    public Response unSubscribe(AggregationProcessorRequest request) {
    	
    	if (log.isInfoEnabled()) {
            StringBuilder sb = new StringBuilder(150).append("CSM_UNSUBSCRIBE_START ").append("REQUEST_ID=").append(request.getRequestId());                                   
            log.info(sb.toString());
        }
    	
        Response response = request.createResponse();
        try {
            String subscriptionRequestId = request.getRequestId();
            AggregationProcessorRequest subRequest = subscriptionRequestCache.get(subscriptionRequestId);

            if (subRequest == null) {
                response.setStatus(Response.STATUS_FAIL);
                response.setMsg("No Subscription Request Found for id " + subscriptionRequestId);

                StringBuilder sb = new StringBuilder(75).append(this.getClass().getName())
                        .append(' ').append("No Subscription Request Found for id ")
                        .append(subscriptionRequestId);
                log.warn(sb.toString());
                return response;
            }

            Aggregator aggregator = aggregatorCache.get(subRequest.getAggregatorId());

            if (log.isInfoEnabled()) {
            	StringBuilder sb = new StringBuilder(150).append(this.getClass().getName())
            			.append("CSM_UNSUBSCRIBE ")
            			.append(" REQUEST_ID=").append(request.getRequestId())
            			.append(" AGGREGATOR_DETAILS=").append(aggregator)
            			.append(" LADDER_VIEW_REQUEST").append(subRequest.getViewRequest());
            	log.info(sb.toString());
            }
            
            if (aggregator != null) {
                AggregationResponseHandler<Response, FXPriceBook> handler = subRequest.getPriceHandler();
                LadderViewRequest viewRequest = subRequest.getViewRequest();

                aggregator.removePriceListener(handler);
                if (viewRequest != null) {
                	aggregator.removeViewRequest(viewRequest);
                }                

                if (!aggregator.isAnyListenersAvailable()) {
                    Aggregator removedAggregator = aggregatorCache.remove(subRequest.getAggregatorId());

                    if (removedAggregator != null) {
                        removedAggregator.stop();
                        factory.getProviderSubManager().unSubscribe(removedAggregator);
                    } else {
                        response.setStatus(Response.STATUS_FAIL);
                    }
                }
            } else {
                response.setStatus(Response.STATUS_FAIL);
            }

            subscriptionRequestCache.remove(subscriptionRequestId);
            
        	if (log.isInfoEnabled()) {
                StringBuilder sb = new StringBuilder(150).append("CSM_UNSUBSCRIBE_SUCCESS ").append("REQUEST_ID=").append(request.getRequestId());                                   
                log.info(sb.toString());
            }
            
        } catch (Exception exc) {              
            StringBuilder sb = new StringBuilder().append("CSM_UNSUBSCRIBE_EXCEPTION")                
                    .append(" REQUEST_ID=").append(request.getRequestId())
                    .append(" STREAM_ID =").append(request.getStreamId());                 
                log.error(sb.toString(), exc);
            response.setStatus(Response.STATUS_FAIL);
        }
        return response;
    }

    /**
     * @return
     */
    public Response stop() {
        Response response = new ResponseC("");
        try {
            for (Aggregator aggregator : aggregatorCache.values()) {
                aggregator.stop();
                this.factory.getProviderSubManager().unSubscribe(aggregator);
            }

            aggregatorCache.clear();
            subscriptionRequestCache.clear();
        } catch (Exception exc) {
            log.error("");
            response.setStatus(Response.STATUS_FAIL);
        }
        return response;
    }

    public Response subscribe(RFSAggregationProcessorRequest request) {
        Response response = request.createResponse();
        try {
            // todo: validate the subscription request.
            Product product = request.getProduct();
            AggregationResponseHandler handler = request.getPriceHandler();

            RFSAggregator publisher = this.factory.newRFSAggregator(request);
            factory.getRFSProviderSubManager().subscribe(publisher);    // todo: if subscription fails to provider, then don't start the publisher.
            publisher.start(100, 50); // todo: Add to some common service to trigger the publisher run.

            subscriptionRequestCache.put(request.getRequestId(), request);
        } catch (Exception exc) {
            log.error("");
            response.setStatus(Response.STATUS_FAIL);
        }
        return response;
    }

    public Product getProduct(String requestId) {
        AggregationProcessorRequest request = subscriptionRequestCache.get(requestId);
        return request != null ? request.getProduct() : null;
    }

    public Collection<Aggregator> getAggregators() {
        return aggregatorCache.values();
    }

    public ConcurrentMap<String, AggregationProcessorRequest> getSubscriptionRequestCache() {
        return subscriptionRequestCache;
    }

    public boolean isSubscriptionAvailable() {
        return subscriptionRequestCache.size() != 0 || aggregatorCache.size() != 0;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder(150)
            .append(this.getClass().getName())
            .append(' ').append(this.aggregationProcessor.getFiOrg().getShortName())
            .append('{');

        for (Aggregator aggregator : aggregatorCache.values()) {
            sb.append(' ').append(aggregator);
        }

        sb.append("} {");

        for (AggregationProcessorRequest request : subscriptionRequestCache.values()) {
            sb.append(' ').append(request);
        }

        sb.append('}');
        return sb.toString();
    }
}
