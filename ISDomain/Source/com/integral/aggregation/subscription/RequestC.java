package com.integral.aggregation.subscription;

import com.integral.aggregation.Response;
import com.integral.aggregation.ResponseC;

/**
 *
 */
public class RequestC implements Request {
    private String requestId;


    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Response createResponse() {
        return new ResponseC(requestId);
    }
}
