package com.integral.aggregation.subscription;

import com.integral.aggregation.subscription.provider.quote.ProviderQuoteHandler;
import com.integral.finance.currency.Currency;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.trade.Tenor;
import com.integral.time.IdcDate;

/**
 *
 */
public class RFSAggregationRequestC extends AggregationRequestC implements RFSAggregationRequest {
    private int expiryInterval = -1;
    private String tradeClassification;
    private Currency dealtCurrency;
    private boolean isSwap;
    private double dealtAmt;
    private Tenor tenor;
    private IdcDate valueDate;
    private int bidOfferMode = DealingPrice.BID;

    private double farLegDealtAmt;
    private Tenor farLegTenor;
    private IdcDate farLegValueDate;
    private int farBidOfferMode = DealingPrice.BID;
    private IdcDate fixingDate;
    private IdcDate farLegFixingDate;
    private ProviderQuoteHandler providerQuoteHandler;
    private RFSAggregationProcessorRequest rfsAggregationProcessorRequest;
    
    
   
    public RFSAggregationRequestC() {

    }
    
    public int getExpiryInterval() { 
        return expiryInterval;
    }

    public void setExpiryInterval(int expiryInterval) {
        this.expiryInterval = expiryInterval;
    }

    public String getTradeClassification() { 
        return tradeClassification;
    }

    public void setTradeClassification(String tradeClassification) {
        this.tradeClassification = tradeClassification;
    }

    public Currency getDealtCurrency() {
        return dealtCurrency;
    }

    public void setDealtCurrency(Currency dealtCurrency) {
        this.dealtCurrency = dealtCurrency;
    }

    public boolean isSwap() {
    	return isSwap;
    }

    public void setSwap(boolean swap) {
        isSwap = swap;
    }

    public double getDealtAmt() {
        return dealtAmt;
    }

    public void setDealtAmt(double dealtAmt) {
        this.dealtAmt = dealtAmt;
    }

    public Tenor getTenor() {
        return tenor;
    }

    public void setTenor(Tenor tenor) {
        this.tenor = tenor;
    }

    public IdcDate getValueDate() {
        return valueDate;
    }

    public void setValueDate(IdcDate valueDate) {
        this.valueDate = valueDate;
    }

    public double getFarLegDealtAmt() {
        return farLegDealtAmt;
    }

    public void setFarLegDealtAmt(double farLegDealtAmt) {
        this.farLegDealtAmt = farLegDealtAmt;
    }

    public Tenor getFarLegTenor() {
        return farLegTenor;
    }

    public void setFarLegTenor(Tenor farLegTenor) {
        this.farLegTenor = farLegTenor;
    }

    public IdcDate getFarLegValueDate() {
        return farLegValueDate;
    }

    public void setFarLegValueDate(IdcDate farLegValueDate) {
        this.farLegValueDate = farLegValueDate;
    }

    public int getBidOfferMode() {
        return bidOfferMode;
    }

    public void setBidOfferMode(int bidOfferMode) {
        this.bidOfferMode = bidOfferMode;
    }

    public int getFarBidOfferMode() {
        return farBidOfferMode;
    }

    public void setFarBidOfferMode(int farBidOfferMode) {
        this.farBidOfferMode = farBidOfferMode;
    }

	@Override
	public IdcDate getFixingDate() {		
		return fixingDate;
	}

	@Override
	public IdcDate getFarLegFixingDate() {		
		return farLegFixingDate;
	}

	@Override
	public void setFixingDate(IdcDate fixingDate) {		
		this.fixingDate = fixingDate;
	}

	@Override
	public void setFarLegFixingDate(IdcDate farLegFixingDate) {
		this.farLegFixingDate = farLegFixingDate;
	}

	@Override
	public void setProviderQuoteHandler(ProviderQuoteHandler providerQuoteHandler) {		
		this.providerQuoteHandler = providerQuoteHandler;
	}

	@Override
	public ProviderQuoteHandler getProviderQuoteHandler() {		
		return this.providerQuoteHandler;
	}

	@Override
	public void setRFSAggregationProcessorRequest(RFSAggregationProcessorRequest rfsAggregationProcessorRequest) {
		this.rfsAggregationProcessorRequest = rfsAggregationProcessorRequest;
		
	}

	@Override
	public RFSAggregationProcessorRequest getRFSAggregationProcessorRequest() {		
		return this.rfsAggregationProcessorRequest;
	}

	
    
    
    
}
