// Copyright (c) 2006-2007 Integral Development Corp. All rights reserved.
package com.integral.aggregation.subscription.provider;

import com.integral.is.common.BaseProvider;
import com.integral.is.common.ProviderStatusObserver;
import com.integral.util.Factory;

/**
 * Factory to create broker provider service objects.
 *
 * <AUTHOR> Development Corp.
 * @version $Revision: ******* $ $Date: 2007-06-08 23:08:16 $ $Author: narang $
 */
public class ProviderFactory extends Factory {
    /*
     * Singleton instance of ProviderFactory.
     */
    protected static ProviderFactory current;
    /*
     * Instance of ProviderUtil.
     */
    private ProviderUtil providerUtil;
    /*
     * Instance of ProviderHandler.
     */
    private ProviderHandler providerHandler;

    static {
        ProviderFactory.current = new ProviderFactory();
    }

    /**
     * Default constructor.
     */
    private ProviderFactory() {
        super();
    }

    /**
     * Returns an instance of the ProviderFactory.
     *
     * @return singleton instance of ProviderFactory
     */
    public static ProviderFactory getInstance() {
        return current;
    }

    /**
     * Create a new provider utility.
     *
     * @return a new provider utility
     */
    private ProviderUtil newProviderUtil() {
        return new ProviderUtilC();
    }

    /**
     * Get instance of provider utility.
     *
     * @return instance of provider utility.
     */
    public ProviderUtil getProviderUtil() {
        return providerUtil == null ? providerUtil = newProviderUtil() : providerUtil;
    }

    /**
     * Create a new provider handler.
     *
     * @return a new provider handler
     */
    private ProviderHandler newProviderHandler() {
        return new ProviderHandlerC();
    }

    /**
     * Get instance of provider handler.
     *
     * @return instance of provider handler.
     */
    public ProviderHandler getProviderHandler() {
        if (providerHandler == null) {
            synchronized (this) {
                if (providerHandler == null) {
                    providerHandler = newProviderHandler();
                }
            }
        }
        return providerHandler;
    }

    /**
     * Create a new provider status observer.
     *
     * @param provider whose status will be observed
     * @param handler  for callbacks from the provider status observer
     * @return a new provider status observer
     */
    public ProviderStatusObserver newProviderStatusObserver(BaseProvider provider, ProviderObserver handler) {
        return new ProviderStatusObserverC(provider, handler);
    }
}
