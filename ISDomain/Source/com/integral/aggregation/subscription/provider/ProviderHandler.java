// Copyright (c) 2006-2007 Integral Development Corp. All rights reserved.
package com.integral.aggregation.subscription.provider;

import com.integral.broker.quote.StaleQuoteObserver;

/**
 * Source of provider updates. Notify all registered observers when a new provider has been added or removed, or when
 * the status of the provider has changed.
 *
 * <AUTHOR> Development Corp.
 * @version $Revision: ******* $ $Date: 2008-04-30 00:32:47 $ $Author: gupta $
 */
public interface ProviderHandler {
    /**
     * Adds an observer to the collection of observers for this object, provided
     * that it is not the same as some observer already in the set.
     * The order in which notifications will be delivered to multiple
     * observers is not specified.
     *
     * @param o an observer to be added.
     * @return true if added
     * @throws NullPointerException if the parameter o is null.
     */
    boolean addObserver(ProviderObserver o);

    /**
     * Deletes an observer from the set of observers of this object.
     * Passing <code>null</code> to this method will have no effect.
     *
     * @param o the observer to be deleted.
     * @return <code>true</code> if removed
     */
    boolean removeObserver(ProviderObserver o);

    /**
     * Adds an observer to the collection of observers for this object, provided
     * that it is not the same as some observer already in the set.
     * The order in which notifications will be delivered to multiple
     * observers is not specified.
     * @param observer
     * @return
     */
    boolean addStaleQuoteObserver(StaleQuoteObserver observer);

    /**
     * Deletes an observer from the set of observers of this object.
     * Passing <code>null</code> to this method will have no effect.
     * @param observer
     * @return
     */
    boolean removeStaleQuoteObserver(StaleQuoteObserver observer);
}
