// Copyright (c) 2007-2008 Integral Development Corp. All rights reserved.
package com.integral.aggregation.subscription.provider;

import com.integral.is.common.BaseProvider;
import com.integral.is.common.mbean.ProviderConfigMBean;
import com.integral.user.Organization;

/**
 * Utility methods to convert between {@link com.integral.user.Organization} and {@link com.integral.is.common.BaseProvider}.
 *
 * <AUTHOR> Development Corp.
 */
public interface ProviderUtil {
    /**
     * Convert from an {@link com.integral.user.Organization} to a {@link com.integral.is.common.BaseProvider}.
     *
     * @param org to convert from
     * @return provider
     */
    BaseProvider toProvider(Organization org);

    /**
     * Convert from an {@link com.integral.user.Organization} short name to a {@link com.integral.is.common.BaseProvider}.
     *
     * @param orgName short name of an organization to convert from
     * @return provider
     */
    BaseProvider toProvider(String orgName);

    /**
     * Convert from a {@link com.integral.is.common.BaseProvider} to an {@link com.integral.user.Organization}.
     *
     * @param provider to convert from
     * @return organization
     */
    Organization toOrganization(BaseProvider provider);

    /**
     * Gets configuration properties for an organization.
     *
     * @param org to get configuration
     * @return configuration properties
     */
    ProviderConfigMBean getProviderConfig(Organization org);
}
