package com.integral.aggregation.subscription.provider.quote;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.AggregationServiceUtil;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.exception.IdcException;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.Request;
import com.integral.is.common.BaseProvider;
import com.integral.is.common.ISConstantsC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.message.WorkflowMessage;
import com.integral.user.Organization;

public class RFSProviderQuoteHandler extends ProviderQuoteHandler {
	private static final Log LOGGER = LogFactory.getLog(RFSProviderQuoteHandler.class);
	private Map<String, Quote> cachedQuoteMap = new ConcurrentHashMap<String, Quote>();
	//private Map<String, Quote> cachedQuoteMap = Collections.synchronizedMap(new HashMap<String, Quote>());
	private Map<String, PriceObserver<Quote>> quoteObserversMap;
	private MessageHandler aggregatedRateHandler;


	public MessageHandler getAggregatedRateHandler() {
		return aggregatedRateHandler;
	}

	public void setAggregatedRateHandler(MessageHandler aggregatedRateHandler) {
		this.aggregatedRateHandler = aggregatedRateHandler;
	}


	public RFSProviderQuoteHandler(AggregationProcessor processor, Organization provider, CurrencyPair ccyPair) {
		super(processor, provider, ccyPair);		
	}
	
	protected boolean isHandlerForProvider(final BaseProvider baseProvider) {
		if (baseProvider == null) return false;
		String providerName = baseProvider.getName();
		if (providerName == null) return false;		
		return quoteObserversMap.containsKey(providerName);
	}
	
	protected void initializeQuoteObservers() {
		this.quoteObserversMap = new ConcurrentHashMap<String, PriceObserver<Quote>>();
	}
	
    protected int getProviderQuoteTier() {
    	// not needed.
    	return -1;
    }

    protected void updateCachedQuote(final String provider, final Quote quote) {
        if (provider == null) return;
        if (quote != null) {
            if( AggregationServiceUtil.incrementRefCounter(quote) <= 0){
                //Can't increment the reference for quote. Don't proceed from here.
                return;
            }
        }
        Quote prev = null;
        if (quote == null) {
        	// remove that provider entry from Map
        	prev = this.cachedQuoteMap.remove(provider);
        } else {
        	prev = this.cachedQuoteMap.put(provider, quote);
        }        
        if (prev != null) {
            AggregationServiceUtil.decrementRefCounter(prev);
        }
    }
    
    public Quote getCachedQuote(final String provider) {
    	return this.cachedQuoteMap.get(provider);
    }


    protected BrokerOrganizationFunction getBrokerOrgFunction() {
    	return null;
    }
    
    protected boolean isTradableQuote(final Quote quote) {
        String quoteClassification = quote.getQuoteClassification().getName();
        return ISConstantsC.QUOTE_RFQ_CREATE_TYPE.equals(quoteClassification);
    }

    protected boolean checkSequenceId() {
    	return false;
    }

	@Override
	protected void notifyObservers(Quote quote, boolean isTradable) {
		Organization providerOrg = quote.getOrganization();
		if (providerOrg == null) return;
		String providerName = providerOrg.getShortName();
		if (providerName == null) return;
		PriceObserver<Quote> observer = this.quoteObserversMap.get(providerName);
		if (observer == null) return;
		if (isTradable) {
			observer.newQuote(quote);
		} else {
			observer.removeQuote();
		}
		if (LOGGER.isDebugEnabled()) {
			StringBuilder sb = new StringBuilder();
			sb.append("RFSProviderQuoteHandler:notifyObservers():: Quote observer notified for provider :");
			sb.append(providerName);
			sb.append(": isTradable :");
			sb.append(isTradable);
			sb.append(":Quote:");
			sb.append(quote);					
			LOGGER.debug(sb.toString());
		}
	}
	
	protected List<PriceObserver<Quote>> getQuoteObservers(final String providerName) {
		if (providerName == null) return Collections.emptyList();
		List<PriceObserver<Quote>> quoteObservers = new ArrayList<PriceObserver<Quote>>(1);
		PriceObserver<Quote> priceObserver = quoteObserversMap.get(providerName);
		if (priceObserver != null) {
			quoteObservers.add(priceObserver);
		}		
		return quoteObservers;
	}

	
	@Override
	public boolean addQuoteObserver(final String providerName, final PriceObserver<Quote> quoteObserver) {
		LOGGER.info("RFSProviderQuoteHandler:addQuoteObserver():: adding Quote Observer or provider :" + providerName);
		if (providerName ==  null || quoteObserver == null) {
			return true;
		}
		this.quoteObserversMap.put(providerName, quoteObserver);
		return true;
	}
	
	protected int getQuoteObserversSize() {
		return this.quoteObserversMap.size();
	}


	@Override
	public boolean removeQuoteObserver(final String providerName, final PriceObserver<Quote> quoteObserver) {
		LOGGER.info("RFSProviderQuoteHandler:removeQuoteObserver():: removing Quote Observer or provider :" + providerName);
		if (providerName == null) {
			return true;
		}
		this.quoteObserversMap.remove(providerName);
		return true;
	}
	
	public Message handle(Message message) throws IdcException {
		if (message == null) {
			return null;
		}
		WorkflowMessage wfMsg = (WorkflowMessage) message;
		String event = wfMsg.getEventName();
		String topic = wfMsg.getTopic();
		try {
			
			if (topic.equals(ISConstantsC.MSG_TOPIC_QUOTE) && event.equals(ISConstantsC.MSG_EVENT_CREATE)) {
				createQuote(message);
			} else {
				removeObserverForProvider(wfMsg);
				aggregatedRateHandler.handle(wfMsg);
			}
		} catch (Exception ex) {
			LOGGER.error("RFSProviderQuoteHandler.handle :: Exception Occurred for " + event + " " + topic, ex);
		}
		return message;
	}
	
	protected void removeObserverForProvider(final WorkflowMessage wfMsg) {
		Object request = wfMsg.getObject();	
		if (!(request instanceof Request)) {
			LOGGER.info("RFSProviderQuoteHandler:removeObserverForProvider()::Work flow message does not contain Request for provider request Reject.");
			return;
		}
		Object parameterValue = wfMsg.getParameterValue(ISConstantsC.LP_ORG_SHORTNAME);
		if (parameterValue instanceof String) {
			String provider = (String)parameterValue;
			updateCachedQuote(provider, null);
			PriceObserver<Quote> observer = this.quoteObserversMap.get(provider);
			if (observer != null) {
				observer.removeQuote();
				LOGGER.info("RFSProviderQuoteHandler:removeObserverForProvider()::Quote observer invalidated for provider :" + provider);
			}
		}
	}
    
	protected void rejectRequest(WorkflowMessage wfMsg) {
		Object request = wfMsg.getObject();	
		if (request instanceof Request) {
			LOGGER.info("RFSProviderQuoteHandler:rejectRequest()::" +
					"Reject Request Received for Transaction Id:" + ((Request)request).getTransactionID());
		}
		removeObserverForProvider(wfMsg);
	}
	


	protected void withdrawRequest(WorkflowMessage wfMsg) {
		Object request = wfMsg.getObject();	
		if (request instanceof Request) {
			LOGGER.info("RFSProviderQuoteHandler:withdrawRequest()::" +
					"Withdraw Request Received for Transaction Id:" + ((Request)request).getTransactionID());
		}
		removeObserverForProvider(wfMsg);
	}

	protected void expireRequest(WorkflowMessage wfMsg) {
		Object request = wfMsg.getObject();	
		if (request instanceof Request) {
			LOGGER.info("RFSProviderQuoteHandler:expireRequest()::" +
					"Expire Request Received for Transaction Id:" + ((Request)request).getTransactionID());
		}
		removeObserverForProvider(wfMsg);
	}

	protected void declineRequest(WorkflowMessage wfMsg) {
		Object request = wfMsg.getObject();	
		if (request instanceof Request) {
			LOGGER.info("RFSProviderQuoteHandler:declineRequest()::" +
					"Decline Request Received for Transaction Id:" + ((Request)request).getTransactionID());
		}
		removeObserverForProvider(wfMsg);
	}

	protected void createQuote(com.integral.message.Message message) {
		Quote quote = (Quote) ((WorkflowMessage) message).getObject();
		sendRate(quote, null);
	}

	protected void acceptQuote(WorkflowMessage wfMsg) {
		// Do nothing.
	}

	protected void withdrawQuote(WorkflowMessage wfMsg) {
		Object request = wfMsg.getObject();	
		if (request instanceof Request) {
			LOGGER.info("RFSProviderQuoteHandler:withdrawQuote()::" +
					"Withdraw Quote Received for Transaction Id:" + ((Request)request).getTransactionID());
		}
		removeObserverForProvider(wfMsg);
	}

	protected void expireQuote(WorkflowMessage wfMsg) {
		Object request = wfMsg.getObject();	
		if (request instanceof Request) {
			LOGGER.info("RFSProviderQuoteHandler:expireQuote()::" +
					"Expire Quote Received for Transaction Id:" + ((Request)request).getTransactionID());
		}
		removeObserverForProvider(wfMsg);
	}

	public void stop() {
		removeObsererFromProviderHandler();
		String cp = ccyPair != null ? ccyPair.getName() : null;
		LOGGER.info("RFS PROVIDER_QUOTE_HANDLER_STOPPED_FOR_ORG=" + aggregationProcessor.getFiOrg().getShortName()
				+ " CCY_PAIR=" + cp);
	}
    
}
