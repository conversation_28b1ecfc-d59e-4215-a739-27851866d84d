package com.integral.aggregation.subscription.provider.quote;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.AggregationProcessorConfig;
import com.integral.aggregation.AggregationServiceUtil;
import com.integral.aggregation.filter.AggregationQuoteFilterConfiguration;
import com.integral.aggregation.subscription.provider.ProviderFactory;
import com.integral.aggregation.subscription.provider.ProviderHandler;
import com.integral.aggregation.subscription.provider.ProviderObserver;
import com.integral.broker.filter.QuoteFilterConfiguration;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.exception.IdcException;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.is.common.BaseProvider;
import com.integral.is.common.cache.bucket.MessageNotifier;
import com.integral.is.common.cache.bucket.MessageNotifierType;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.user.Organization;
import com.integral.lp.quote.ProviderQuoteCacheService;
import com.integral.is.finance.dealing.ServiceFactory;
/**
 *
 */
public class ProviderQuoteHandler implements ProviderObserver, MessageHandler, MessageNotifier {
    private static final ISMBean isMBean = ISFactory.getInstance().getISMBean();

    private Log log = LogFactory.getLog(this.getClass());
    protected AggregationProcessor aggregationProcessor;
    protected AggregationProcessorConfig aggregationConfig;
    protected Organization brokerOrg;
    protected BrokerOrganizationFunction orgFunction;
    protected LegalEntity brokerLE;
    protected Organization provider;
    protected CurrencyPair ccyPair;
    protected int providerQuoteTier;

    private List<PriceObserver<Quote>> quoteObservers;
    private Quote cachedQuote;
    private ReentrantLock observersLock = new ReentrantLock();
    private QuoteFilterConfiguration quoteFilterConfiguration = AggregationQuoteFilterConfiguration.getInstance();

    public ProviderQuoteHandler(AggregationProcessor processor, Organization provider, CurrencyPair ccyPair) {
        super();
        this.aggregationProcessor = processor;
        this.aggregationConfig = this.aggregationProcessor.getConfig();
        this.brokerLE = this.aggregationConfig.getLegalEntity();
        if (this.brokerLE != null) {
        	this.brokerOrg = brokerLE.getOrganization();
        }        
        this.provider = provider;
        this.ccyPair = ccyPair;
        this.providerQuoteTier = getProviderQuoteTier();

        this.orgFunction = getBrokerOrgFunction();
        initializeQuoteObservers();

        ProviderFactory providerFactory = ProviderFactory.getInstance();
        ProviderHandler providerHandler = providerFactory.getProviderHandler();
        providerHandler.addObserver(this);
        String providerShotName = (provider != null ? provider.getShortName() : null);
        String fiOrgShortName = (aggregationProcessor.getFiOrg() != null ? aggregationProcessor.getFiOrg().getShortName() : null);
        log.info("PROVIDER_QUOTE_HANDLER_CREATED_FOR_ORG=" + fiOrgShortName
                				+ " CCY_PAIR=" + ccyPair.getName() + " PROVIDER=" + providerShotName);
    }
    
    protected void initializeQuoteObservers() {
    	 this.quoteObservers = new ArrayList<PriceObserver<Quote>>(20);
	}
    
    protected int getProviderQuoteTier() {
    	return this.aggregationConfig.getProviderQuoteTier(this.provider.getShortName(), this.ccyPair.getName());
    }

    protected BrokerOrganizationFunction getBrokerOrgFunction() {
    	return this.aggregationProcessor.getFactory().getBrokerOrganizationFunction();
    }
    
    public void sendRate(Quote quote, Map properties) {
        CurrencyPair ccyPair = quote.getCurrencyPair();
        handleRate(ccyPair, quote);

        //BrokerAdaptorUtil.getInstance().decrementRefCounter(quote);
    }
    
    protected void updateCachedQuote(final String provider , final Quote quote) {
        if(this.cachedQuote != null) {
            AggregationServiceUtil.decrementRefCounter(cachedQuote);
        }
    	this.cachedQuote = quote;
        if(this.cachedQuote != null) {
            if(AggregationServiceUtil.incrementRefCounter(cachedQuote) <= 0){
                //Cant increment the reference on Quote, so don't use it.
                this.cachedQuote = null;
            }
        }
    }
    
    public Quote getCachedQuote(final String provider) {
    	return this.cachedQuote;
    }
    
    protected String getProviderName(final Quote quote) {
        Organization provider = quote.getOrganization();
        String providerName = (provider == null ? null : provider.getShortName());
        return providerName;
    }
    
    protected boolean isTradableQuote(final Quote quote) {
    	return AggregationServiceUtil.isTradableQuote(quote);
    }
    
    public void handleRate(CurrencyPair ccyPair, Quote quote) {
        boolean isTradable = isTradableQuote(quote);
        String providerName = getProviderName(quote);
        if (!isTradable) {
            if (log.isDebugEnabled()) {
                StringBuilder sb = new StringBuilder(this.getClass().getName())
                    .append(".handleRate() Received inactive rate").append(' ')
                    .append(ccyPair.getName()).append(' ')
                    .append(quote.getOrganization().getShortName()).append(' ')
                    .append(isTradable).append(' ')
                    .append(quote.getGUID());
                log.debug(sb.toString());
            }
            //cachedQuote = null;
            // Modified call to support RFS full book
            updateCachedQuote(providerName, null);
            notifyObservers(quote, false);
            return;
        }

        String externalQuoteId = quote.getRateId();
        if (externalQuoteId == null || externalQuoteId.trim().equals("")) {
            StringBuilder sb = new StringBuilder(this.getClass().getName())
                .append(".handleRate()").append(' ')
                .append("External quote id is missing").append(' ')
                .append(quote.getGUID());
            log.error(sb.toString());
            return;
        }
        
        if (log.isDebugEnabled()) {
            StringBuilder sb = new StringBuilder(this.getClass().getName())
                .append(".handleRate()").append(' ')
                .append(this.toString()).append(' ')
                .append(quote.getCurrencyPair().getName()).append(' ')
                .append(quote.getOrganization().getShortName()).append(' ')
                .append(quote.getGUID()).append(' ')
                .append(quote.getExternalQuoteId()).append(' ')
                .append(quote.getPriceType());
            log.debug(sb.toString());
        }

        notifyObservers(quote, true);
        updateCachedQuote(providerName, quote);
    }

    protected void notifyObservers(Quote quote, boolean isTradable) {
        observersLock.lock();
        try {
            if (isTradable) {
                for (PriceObserver<Quote> observer : quoteObservers) {
                    observer.newQuote(quote);
                }
            } else {
                for (PriceObserver<Quote> observer : quoteObservers) {
                    observer.removeQuote();
                }
            }

            if (log.isDebugEnabled()) {
                StringBuilder sb = new StringBuilder(200).append(this.getClass().getName())
                    .append(".notifyObservers()").append(' ')
                    .append(this.toString()).append(' ');

                for (PriceObserver<Quote> observer : quoteObservers) {
                    sb.append(observer).append(' ');
                }

                log.debug(sb.toString());
            }
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(75).append(this.getClass().getName())
                .append(' ').append("Exception while notifying the price observers.")
                .append(' ').append(this.toString())
                .append(' ').append(isTradable)
                .append(' ').append(quote);
            log.error(sb.toString());
        } finally {
            observersLock.unlock();
        }
    }

    public boolean addQuoteObserver(String providerName, PriceObserver<Quote> quoteObserver) {
        observersLock.lock();
        try {
            if (log.isDebugEnabled()) {
                StringBuilder sb = new StringBuilder(75).append(this.getClass().getName())
                    .append(".addQuoteObserver()").append(' ')
                    .append(this.toString()).append(' ')
                    .append(quoteObserver);
                log.debug(sb.toString());
            }

            return quoteObservers.add(quoteObserver);
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(75).append(this.getClass().getName())
                .append(' ').append("Exception while adding the price observer.")
                .append(' ').append(this.toString())
                .append(' ').append(quoteObserver);
            log.error(sb.toString());
            return false;
        } finally {
            observersLock.unlock();
        }
    }

    public boolean removeQuoteObserver(String providerName, PriceObserver<Quote> quoteObserver) {
        observersLock.lock();
        try {
            if (log.isDebugEnabled()) {
                StringBuilder sb = new StringBuilder(75).append(this.getClass().getName())
                    .append(".removeQuoteObserver()").append(' ')
                    .append(this.toString()).append(' ')
                    .append(quoteObserver);
                log.debug(sb.toString());
            }

            return quoteObservers.remove(quoteObserver);
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(75).append(this.getClass().getName())
                .append(' ').append("Exception while removing the price observer.")
                .append(' ').append(this.toString())
                .append(' ').append(quoteObserver);
            log.error(sb.toString());
            return false;
        } finally {
            observersLock.unlock();
        }
    }

    /*private <T, J, V> boolean isFiltered(T newQuote, J oldQuote, V parameter, List<SingleProviderQuoteFilter<T, J, V>> singleProviderQuoteFilters) {
        for (SingleProviderQuoteFilter<T, J, V> filter : singleProviderQuoteFilters) {// Apply filters
            if (!filter.isFiltered(newQuote, oldQuote, parameter)) {
                return false;
            }
        }
        return true;
    }*/

    public void providerAdd(BaseProvider provider) {
        //To change body of implemented methods use File | Settings | File Templates.
    }

    public void providerRemove(BaseProvider provider) {
        providerInactive(provider);
    }

    public void providerActive(BaseProvider provider) {
        //To change body of implemented methods use File | Settings | File Templates.
    }
    
    protected boolean isHandlerForProvider(final BaseProvider baseProvider) {
    	return (baseProvider != null && baseProvider.getName().equals(this.provider.getShortName()));
    }
    
    protected List<PriceObserver<Quote>> getQuoteObservers(final String providerName) {
    	return this.quoteObservers;
    }
    
    public void providerInactive(BaseProvider provider) {    	
        try {
            if (log.isDebugEnabled()) {
                StringBuilder sb = new StringBuilder(this.getClass().getName())
                    .append(' ').append("providerInactive")
                    .append(' ').append(provider)
                    .append(' ').append(ccyPair)
                    .append(' ').append(this.provider);
                log.debug(sb.toString());
            }

            //if (provider != null && provider.getName().equals(this.provider.getShortName())) {
            if (isHandlerForProvider(provider)) {
                //cachedQuote = null;
            	String providerName = provider.getName();
            	updateCachedQuote(providerName, null);
            	
                for (PriceObserver<Quote> observer : getQuoteObservers(providerName)) {
                	if (observer != null) {
                		observer.removeQuote();
                	}                    
                }
            }
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(this.getClass().getName())
                .append(' ').append("providerInactive")
                .append(' ').append(provider)
                .append(' ').append(this.ccyPair)
                .append(' ').append(this.provider)
                .append(' ').append(exc);
            log.error(sb.toString());
            exc.printStackTrace();
        }
    }

    public void providerStale(BaseProvider provider) {
        //To change body of implemented methods use File | Settings | File Templates.
    }
    
    

    @Override
	public Message handle(Message message) throws IdcException {		
		return null;
	}
    
    protected int getQuoteObserversSize() {
    	return this.quoteObservers.size();
    }

	public String toString() {
        StringBuilder sb = new StringBuilder(150)
            .append(super.toString()).append(' ')
            .append((this.ccyPair == null ? "" : this.ccyPair.getName())).append(' ')
            .append((this.provider == null ? "" : this.provider.getShortName())).append(' ')
            .append((this.brokerOrg == null ? "" : this.brokerOrg.getShortName())).append(' ')
            .append(this.cachedQuote == null ? "" : this.cachedQuote.getGUID()).append(' ')            
            .append(getQuoteObserversSize());

        return sb.toString();
    }

    @Override
    public MessageNotifierType getType() {
        return MessageNotifierType.AggregationService;
    }

    public Quote getCachedQuote(){
        if(cachedQuote == null)
        {
            try{
                log.info("Querying ISQuote Cache:" + this.toString());
                ProviderQuoteCacheService cacheService = ServiceFactory.getProviderQuoteCacheService();
                cachedQuote = cacheService.getLatestQuote(this.provider, brokerOrg, this.ccyPair, Quote.ReferenceHolder.AggregationService);
                if(cachedQuote == null)
                    log.info("ISQuote cache returned null quote");
                else{
                    boolean isTradable = isTradableQuote(cachedQuote);
                    if (!isTradable) {
                        if (log.isDebugEnabled()) {
                            StringBuilder sb = new StringBuilder(this.getClass().getName())
                                    .append("Dropping: ISQuote cache returned inactive rate").append(' ')
                                    .append(ccyPair.getName()).append(' ')
                                    .append(cachedQuote.getOrganization().getShortName()).append(' ')
                                    .append(isTradable).append(' ')
                                    .append(cachedQuote.getGUID());
                            log.debug(sb.toString());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc){
                StringBuilder sb = new StringBuilder(this.getClass().getName())
                        .append(' ').append("getCachedQuote")
                        .append((this.ccyPair == null ? "" : this.ccyPair.getName())).append(' ')
                        .append((this.provider == null ? "" : this.provider.getShortName())).append(' ')
                        .append((this.brokerOrg == null ? "" : this.brokerOrg.getShortName())).append(' ');
                exc.printStackTrace();
            }
        }

        return cachedQuote;
    }
    
    public void stop() {
        //LO-4870 - First remove the handler and then decrement quote ref count.
        removeObsererFromProviderHandler();
        if(cachedQuote != null) {
            AggregationServiceUtil.decrementRefCounter(cachedQuote);
        }
        String cp = ccyPair != null ? ccyPair.getName() : null;
        String providerName = provider != null ? provider.getShortName() : null;
        log.info("PROVIDER_QUOTE_HANDLER_STOPPED_FOR_ORG=" + aggregationProcessor.getFiOrg().getShortName()
                										   + " CCY_PAIR=" + cp
                										   + " PROVIDER=" + providerName);
    }
    
    protected void removeObsererFromProviderHandler() {
    	ProviderFactory providerFactory = ProviderFactory.getInstance();
        ProviderHandler providerHandler = providerFactory.getProviderHandler();
        providerHandler.removeObserver(this);
    }
    
}
