// Copyright (c) 2006-2007 Integral Development Corp. All rights reserved.
package com.integral.aggregation.subscription.provider;

import com.integral.is.common.BaseProvider;
import com.integral.is.common.ProviderStatusObserver;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * Observes the status of a provider, and pass notifications to handler via {@link ProviderObserver} callbacks.
 * This observer should be registered via {@link com.integral.is.common.Provider#addProviderStatusObserver}.
 *
 * <AUTHOR> Development Corp.
 * @version $Revision: ******* $ $Date: 2007-03-06 18:21:42 $ $Author: solomon $
 */
public class ProviderStatusObserverC implements ProviderStatusObserver {
    /**
     * Logger for this class and its descendants.
     */
    protected Log log = LogFactory.getLog(this.getClass());
    /**
     * Provider under observation.
     */
    private final BaseProvider provider;
    /**
     * Observer to be notified upon a status change.
     */
    private final ProviderObserver handler;

    /**
     * Constructor is protected, to ensure this class can only be instantiated by the package factory.
     *
     * @param provider whose status will be monitored
     * @param handler  delegate to receive provider status notifications.
     */
    protected ProviderStatusObserverC(BaseProvider provider, ProviderObserver handler) {
        super();
        this.provider = provider;
        this.handler = handler;
    }

    public void providerActive() {
        handler.providerActive(provider);

        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(200);
            s.append(this).append(".providerActive ");
            s.append("{ handler=").append(handler);
            s.append(", provider=").append(provider);
            s.append('}');
            log.debug(s.toString());
        }
    }

    public void providerInactive() {
        handler.providerInactive(provider);

        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(200);
            s.append(this).append(".providerInactive ");
            s.append("{ handler=").append(handler);
            s.append(", provider=").append(provider);
            s.append('}');
            log.debug(s.toString());
        }
    }

    public void providerStale() {
        handler.providerStale(provider);

        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(200);
            s.append(this).append(".providerStale ");
            s.append("{ handler=").append(handler);
            s.append(", provider=").append(provider);
            s.append('}');
            log.debug(s.toString());
        }
    }
}
