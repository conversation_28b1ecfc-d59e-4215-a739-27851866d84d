// Copyright (c) 2006-2007 Integral Development Corp. All rights reserved.
package com.integral.aggregation.subscription.provider;

import com.integral.broker.quote.StaleQuoteObserver;
import com.integral.is.common.*;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.Organization;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * Source of provider updates. Notify all registered observers when a new provider has been added or removed, or when
 * the status of the provider has changed.
 *
 * <AUTHOR> Development Corp.
 * @version $Revision: ******* $ $Date: 2008-04-30 00:32:47 $ $Author: gupta $
 */
public class ProviderHandlerC implements ProviderHandler, ProviderObserver, ProviderManagerObserver {
    /**
     * Logger for this class and its descendants.
     */
    protected Log log = LogFactory.getLog(this.getClass());
    /**
     * Observers of this handler.
     */
    private final Set<ProviderObserver> observers = new CopyOnWriteArraySet<ProviderObserver>();
    private final Set<StaleQuoteObserver> staleQuoteObservers = new CopyOnWriteArraySet<StaleQuoteObserver>();

    /**
     * Constructor is protected, to ensure this class can only be instantiated by the package factory.
     */
    protected ProviderHandlerC() {
        super();

        // Provider Handler needs to be notified when a provider is added or removed.
        ProviderManagerC manager = ProviderManagerC.getProviderManager();
        manager.addObserver(this);

        // Register new status observer with all existing providers.
        for (Object o : com.integral.lp.ProviderManagerC.getInstance().getProviderMap().values()) {
            Provider provider = (Provider) o;
            observeProviderStatus(provider);
        }
    }

    public boolean addObserver(ProviderObserver observer) {
        boolean isAdded = observers.add(observer);

        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(100);
            s.append(this).append(".addObserver ");
            s.append("{ isAdded = ").append(isAdded);
            s.append(", observer = ").append(observer);
            s.append(", observers = ").append(observers);
            s.append('}');
            log.debug(s.toString());
        }

        return isAdded;
    }

    public boolean removeObserver(ProviderObserver observer) {
        boolean isRemoved = observers.remove(observer);

        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(100);
            s.append(this).append(".removeObserver ");
            s.append("{ isRemoved = ").append(isRemoved);
            s.append(", observer = ").append(observer);
            s.append(", observers = ").append(observers);
            s.append('}');
            log.debug(s.toString());
        }

        return isRemoved;
    }


    public boolean addStaleQuoteObserver(StaleQuoteObserver observer) {
        boolean isAdded = staleQuoteObservers.add(observer);

        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(100);
            s.append(this).append(".addStaleQuoteObserver ");
            s.append("{ isAdded = ").append(isAdded);
            s.append(", observer = ").append(observer);
            s.append(", observers = ").append(staleQuoteObservers);
            s.append('}');
            log.debug(s.toString());
        }

        return isAdded;
    }

    public boolean removeStaleQuoteObserver(StaleQuoteObserver observer) {
        boolean isRemoved = staleQuoteObservers.remove(observer);

        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(100);
            s.append(this).append(".removeStaleQuoteObserver ");
            s.append("{ isRemoved = ").append(isRemoved);
            s.append(", observer = ").append(observer);
            s.append(", observers = ").append(staleQuoteObservers);
            s.append('}');
            log.debug(s.toString());
        }

        return isRemoved;
    }

    /**
     * Notify observers that a provider has been added.
     *
     * @param provider that was added
     */
    public void providerAdd(BaseProvider provider) {
        for (ProviderObserver observer : observers) {
            observer.providerAdd(provider);
        }

        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(100);
            s.append(this).append(".providerAdd ");
            s.append("{ provider = ").append(provider);
            s.append(", observers = ").append(observers);
            s.append('}');
            log.debug(s.toString());
        }
    }

    /**
     * Notify observers that a provider has been removed.
     *
     * @param provider that was removed
     */
    public void providerRemove(BaseProvider provider) {
        for (ProviderObserver observer : observers) {
            observer.providerRemove(provider);
        }
        Organization org = getOrg(provider);
        for (StaleQuoteObserver staleQuoteObserver : staleQuoteObservers) {
            staleQuoteObserver.removeQuote(org);
        }

        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(100);
            s.append(this).append(".providerRemove ");
            s.append("{ provider = ").append(provider);
            s.append(", observers = ").append(observers);
            s.append(", staleQuoteObservers = ").append(staleQuoteObservers);
            s.append('}');
            log.debug(s.toString());
        }
    }

    private Organization getOrg(BaseProvider provider) {
        ProviderUtil util = ProviderFactory.getInstance().getProviderUtil();
        return util.toOrganization(provider);
    }

    /**
     * Notify observers that a provider is now active.
     *
     * @param provider that is now active
     */
    public void providerActive(BaseProvider provider) {
        for (ProviderObserver observer : observers) {
            observer.providerActive(provider);
        }

        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(100);
            s.append(this).append(".providerActive ");
            s.append("{ provider = ").append(provider);
            s.append(", observers = ").append(observers);
            s.append('}');
            log.debug(s.toString());
        }
    }

    /**
     * Notify observers that a provider is now inactive.
     *
     * @param provider that is now inactive
     */
    public void providerInactive(BaseProvider provider) {
        for (ProviderObserver observer : observers) {
            observer.providerInactive(provider);
        }
        Organization org = getOrg(provider);
        for (StaleQuoteObserver staleQuoteObserver : staleQuoteObservers) {
            staleQuoteObserver.removeQuote(org);
        }
        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(100);
            s.append(this).append(".providerInactive ");
            s.append("{ provider = ").append(provider);
            s.append(", observers = ").append(observers);
            s.append(", staleQuoteobservers = ").append(staleQuoteObservers);
            s.append('}');
            log.debug(s.toString());
        }
    }

    /**
     * Notify observers that a provider is now stale.
     *
     * @param provider that is now stale
     */
    public void providerStale(BaseProvider provider) {
        for (ProviderObserver observer : observers) {
            observer.providerStale(provider);
        }
        Organization org = getOrg(provider);
        for (StaleQuoteObserver staleQuoteObserver : staleQuoteObservers) {
            staleQuoteObserver.removeQuote(org);
        }
        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(100);
            s.append(this).append(".providerStale ");
            s.append("{ provider = ").append(provider);
            s.append(", observers = ").append(observers);
            s.append(", staleQuoteObservers = ").append(staleQuoteObservers);
            s.append('}');
            log.debug(s.toString());
        }
    }

    public void providerAdded(String orgList) {
        List<BaseProvider> providers = toProviders(orgList);
        for (BaseProvider provider : providers) {
            providerAdd(provider);
            observeProviderStatus(provider);
        }

        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(200);
            s.append(this).append(".providerAdded ");
            s.append("{ orgList=").append(orgList);
            s.append(", providers = ").append(providers);
            s.append('}');
            log.debug(s.toString());
        }
    }

    public void providerRemoved(String orgList) {
        List<BaseProvider> providers = toProviders(orgList);
        for (BaseProvider provider : providers) {
            providerRemove(provider);
        }

        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(200);
            s.append(this).append(".providerAdded ");
            s.append("{ orgList=").append(orgList);
            s.append(", providers = ").append(providers);
            s.append('}');
            log.debug(s.toString());
        }
    }

    /**
     * Parse a comma-separated string of external provider names into a list of providers.
     *
     * @param orgList provider names to be parsed
     * @return providers
     */
    private List<BaseProvider> toProviders(String orgList) {
        String[] orgNames = orgList.split(",");
        List<BaseProvider> providers = new ArrayList<BaseProvider>(orgNames.length);
        ProviderUtil util = ProviderFactory.getInstance().getProviderUtil();
        BaseProvider provider;
        for (String orgName : orgNames) {
            provider = util.toProvider(orgName);
            if (provider != null && !providers.contains( provider )) {
                providers.add(provider);
            }
        }
        return providers;
    }

    /**
     * Register a new {@link com.integral.is.common.ProviderStatusObserver} with a provider. The status observer will pass along
     * provider status notifications back to this handler via the following callbacks: {@link #providerActive},
     * {@link #providerInactive}, and {@link #providerStale}.
     *
     * @param provider whose status will be observed
     */
    private void observeProviderStatus(BaseProvider provider) {
        ProviderFactory providerFactory = ProviderFactory.getInstance();
        ProviderStatusObserver statusObserver = providerFactory.newProviderStatusObserver(provider, this);
        provider.addProviderStatusObserver(statusObserver);

        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(100);
            s.append(this).append(".observeProviderStatus ");
            s.append("{ provider = ").append(provider);
            s.append(", providerFactory = ").append(providerFactory);
            s.append(", statusObserver = ").append(statusObserver);
            s.append(", observers = ").append(observers);
            s.append('}');
            log.debug(s.toString());
        }
    }
}
