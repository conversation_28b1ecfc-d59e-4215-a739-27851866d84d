// Copyright (c) 2007-2008 Integral Development Corp. All rights reserved.
package com.integral.aggregation.subscription.provider;

import com.integral.is.common.BaseProvider;
import com.integral.is.common.ProviderManagerC;
import com.integral.is.common.mbean.ProviderConfigMBean;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.Organization;

/**
 * Utility methods to convert between {@link com.integral.user.Organization} and {@link com.integral.is.common.BaseProvider}.
 *
 * <AUTHOR> Development Corp.
 */
public class ProviderUtilC implements ProviderUtil {
    /**
     * Logger for this class and its descendants.
     */
    protected Log log = LogFactory.getLog(this.getClass());

    /**
     * Constructor is protected, to ensure this class can only be instantiated by the package factory.
     */
    protected ProviderUtilC() {
        super();
    }

    public BaseProvider toProvider(Organization org) {
        return toProvider(org.getShortName());
    }

    public BaseProvider toProvider(String orgName) {
        BaseProvider provider = com.integral.lp.ProviderManagerC.getInstance().getProvider( orgName );
        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(100);
            s.append(this).append(".toProvider ");
            s.append("{ orgName = ").append(orgName);
            s.append(", provider = ").append(provider);
            s.append('}');
            log.debug(s.toString());
        }

        return provider;
    }

    public Organization toOrganization(BaseProvider provider) {
        String internalName = provider.getName();
        Organization org = ISUtilImpl.getInstance().getOrg(internalName);

        if (log.isDebugEnabled()) {
            StringBuilder s = new StringBuilder(100);
            s.append(this).append(".toOrganization ");
            s.append("{ provider = ").append(provider);
            s.append(", internalName = ").append(internalName);
            s.append(", org = ").append(org);
            s.append('}');
            log.debug(s.toString());
        }

        return org;
    }

    public ProviderConfigMBean getProviderConfig(Organization org) {
        return ProviderManagerC.getProviderManager().getProviderConfig( org.getShortName() );
    }
}
