// Copyright (c) 2006-2007 Integral Development Corp. All rights reserved.
package com.integral.aggregation.subscription.provider;

import com.integral.is.common.BaseProvider;

/**
 * Observes providers. Can be registered with {@link com.integral.aggregation.subscription.provider.ProviderHandler} to receive provider notifications.
 *
 * <AUTHOR> Development Corp.
 * @version $Revision: ******* $ $Date: 2007-03-06 18:21:42 $ $Author: solomon $
 */
public interface ProviderObserver {
    /**
     * Notify the observer that a provider has been added.
     *
     * @param provider to be added
     */
    void providerAdd(BaseProvider provider);

    /**
     * Notify the observer that a provider has been removed.
     *
     * @param provider to be removed
     */
    void providerRemove(BaseProvider provider);

    /**
     * Notify the observer that a provider is now active.
     *
     * @param provider that is now active
     */
    void providerActive(BaseProvider provider);

    /**
     * Notify the observer that a provider is now inactive.
     *
     * @param provider that is now inactive
     */
    void providerInactive(BaseProvider provider);

    /**
     * Notify the observer that a provider is now stale.
     *
     * @param provider that is now stale
     */
    void providerStale(BaseProvider provider);
}
