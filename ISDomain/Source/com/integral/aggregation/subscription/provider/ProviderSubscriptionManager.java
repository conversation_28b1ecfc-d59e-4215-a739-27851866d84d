package com.integral.aggregation.subscription.provider;

import com.integral.aggregation.*;
import com.integral.aggregation.stream.Aggregator;
import com.integral.aggregation.subscription.provider.quote.ProviderQuoteHandler;
import com.integral.broker.model.Product;
import com.integral.broker.model.Stream;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.*;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.trade.Tenor;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.*;
import com.integral.persistence.ExternalSystem;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.user.Organization;
import com.integral.user.User;

import java.rmi.RemoteException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ConcurrentSkipListSet;

/**
 * Responsible for managing all the provider subscriptions.
 */
public class ProviderSubscriptionManager {
    private Log log = LogFactory.getLog(this.getClass());

    protected AggregationProcessor aggregationProcessor;
    protected AggregationProcessorConfig config;
    protected AggregatorFactory factory;
    protected ConcurrentMap<Organization, ConcurrentMap<String, Set<Aggregator>>> providerCcyPairAggregatorCache;
    protected ConcurrentMap<String, Request> providerCcyPairRequestCache;
    public static final String REQUEST_HOLD_EXECUTION_TYPE = "S";

    public ProviderSubscriptionManager(AggregationProcessor processor) {
        this.aggregationProcessor = processor;
        this.config = this.aggregationProcessor.getConfig();
        this.factory = this.aggregationProcessor.getFactory();
        this.providerCcyPairAggregatorCache = new ConcurrentHashMap<Organization, ConcurrentMap<String, Set<Aggregator>>>(15);
        this.providerCcyPairRequestCache = new ConcurrentHashMap<String, Request>(15);
        log.info("PROVIDER_SUBSCRIPTION_MANAGER_CREATED_FOR_ORG=" + aggregationProcessor.getFiOrg().getShortName());
    }

    /**
     * Subscribes to all the liquidity providers of request.
     *
     * @param product
     * @return
     */
    public Response subscribe(Aggregator aggregator) {
        Response response = new ResponseC();
        try {
            Set<Organization> providers = getProviders(aggregator);
            List<Organization> failedProviders = new ArrayList<Organization>(5);

            for (Organization provider : providers) {
                ConcurrentMap<String, Set<Aggregator>> ccyPairProducts = providerCcyPairAggregatorCache.get(provider);
                if (ccyPairProducts == null) {
                    ccyPairProducts = new ConcurrentHashMap<String, Set<Aggregator>>();
                    ConcurrentMap<String, Set<Aggregator>> cachedCcyPairProducts = providerCcyPairAggregatorCache.putIfAbsent(provider, ccyPairProducts);

                    if (cachedCcyPairProducts != null) {
                        ccyPairProducts = providerCcyPairAggregatorCache.get(provider);
                    }
                    subscribe(aggregator, failedProviders, provider, ccyPairProducts);

                    // If the provider subscription fails remove the cache entry.
                    if (failedProviders.contains(provider)) {
                        providerCcyPairAggregatorCache.remove(provider);
                    }
                } else {
                    subscribe(aggregator, failedProviders, provider, ccyPairProducts);
                }
            }

            if (failedProviders.size() != 0) {
                if (failedProviders.size() == providers.size()) {
                    response.setStatus(Response.STATUS_FAIL);
                }
                StringBuilder _failedProviders = new StringBuilder(failedProviders.size() * 15);

                for (Organization org : failedProviders) {
                    _failedProviders.append(org.getShortName()).append(',');
                }
                response.setMsg("Failed provider subscriptions. ");

                if (log.isInfoEnabled()) {
                    StringBuilder sb = new StringBuilder(150)
                            .append(this.getClass().getName()).append(".subscribe")
                            .append(' ').append("Could not subscribe to these providers.")
                            .append(' ').append(_failedProviders.toString());

                    log.info(sb.toString());
                }
            }
        } catch (Exception exc) {
            log.warn("ProviderSubscriptionManager.subscribe() Could not subscribe to provider " + aggregator.getProduct().getCurrencyPair().getName(), exc);
            response.setStatus(Response.STATUS_FAIL);
        }
        return response;
    }

    private void subscribe(Aggregator aggregator, List<Organization> failedProviders,
                           Organization provider, ConcurrentMap<String, Set<Aggregator>> ccyPairProducts) {
        Product product = aggregator.getProduct();
        String ccyPair = product.getCurrencyPair().getName();
        Set<Aggregator> requests = ccyPairProducts.get(ccyPair);

        if (requests == null || requests.size() == 0 ) {
            requests = new ConcurrentSkipListSet<Aggregator>(new Comparator<Aggregator>() {
                @Override
                public int compare(Aggregator o1, Aggregator o2) {
                    return o1.getObjectId().compareTo(o2.getObjectId());
                }
            }); //need concurrent hash set
            Set<Aggregator> cachedProducts = ccyPairProducts.putIfAbsent(ccyPair, requests);

            if (cachedProducts != null) {
                requests = ccyPairProducts.get(ccyPair);
            }

            if (requests.size() == 0) {
                Response subscriptionRes = subscribe(provider, product);

                if (subscriptionRes.getStatus() == Response.STATUS_OK) {
                    requests.add(aggregator);
                } else {
                    // Removing the provider handler from cache for the failed subscription.
                    this.factory.removeQuoteHandler(product.getCurrencyPair(), provider);

                    failedProviders.add(provider);
                    // If the subscription fails, remove the entry.
                    ccyPairProducts.remove(ccyPair);
                }
            } else {
                requests.add(aggregator);
            }
        } else {
            requests.add(aggregator);
        }
    }

    /**
     * @param provider
     * @param product
     * @return
     */
    private Response subscribe(Organization provider, Product product) {
        Response response = new ResponseC();
        Request request = null;
        Stream stream = null;
        User owner = null;
        WorkflowMessage subscriptionMessage = null;

        try {
            String key = createProviderCcyPairKey(provider, product.getCurrencyPair());
            synchronized (providerCcyPairRequestCache) {
                request = providerCcyPairRequestCache.get(key);
                if (request == null) {
                	log.info("PSM_SUBSCRIBE, SUBSCRIPTION_NOT_PRESENT_FOR_KEY=" + key + " so creating one");
                    stream = product.getConfiguration().getStream();
                    owner = stream.getUser();
                    subscriptionMessage = createSubscriptionMessage(provider, product);
                    WorkflowMessage subscriptionResponse = sendToProvider(subscriptionMessage, product);
                    Message reply = subscriptionResponse.getReplyMessage();
                    MessageStatus replyStatus = reply.getStatus();
                    if (MessageStatus.SUCCESS.equals(replyStatus)) {
                        providerCcyPairRequestCache.putIfAbsent(key, (Request) subscriptionMessage.getObject());
                    } else {
                        response.setStatus(Response.STATUS_FAIL);
                    }
                } else {
                	log.info("PSM_SUBSCRIBE, SUBSCRIPTION_ALREADY_PRESENT_FOR_KEY=" + key);
                }
            }

            if (log.isInfoEnabled()) {
                StringBuilder sb = new StringBuilder(200);
                sb.append("PSM_SUBSCRIBE_SUCCESS");
                sb.append(" PRODUCT = ").append(product);
                sb.append(" PROVIDER = ").append(provider);
                sb.append(" REQUEST = ").append(request);
                sb.append(" CCYPAIR = ").append(product.getCurrencyPair().getName());
                sb.append(" STREAM = ").append(stream);
                sb.append(" OWNER = ").append(owner);
                sb.append(" MSG = ").append(subscriptionMessage);                
                log.info(sb.toString());
            }
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder(100);
            sb.append(this.getClass().getName()).append(".start ");
            sb.append("{ product = ").append(product);
            sb.append(", provider = ").append(provider);
            sb.append(", request = ").append(request);
            sb.append(", ccyPair = ").append(product.getCurrencyPair().getName());
            sb.append(", stream = ").append(stream);
            sb.append(", owner = ").append(owner);
            sb.append(", msg = ").append(subscriptionMessage);
            sb.append('}');
            log.warn(sb.toString(), e);
            response.setStatus(Response.STATUS_FAIL);
        }
        return response;
    }

    private String createProviderCcyPairKey(Organization provider, CurrencyPair currencyPair) {
        return provider.getShortName() + "." + currencyPair.getName();
    }

    public Response unSubscribe(Aggregator aggregator) {
        Response response = new ResponseC();

        try {
            Set<Organization> providers = getProviders(aggregator);
            Product product = aggregator.getProduct();
            String ccyPair = product.getCurrencyPair().getName();
            List<Organization> failedProviders = new ArrayList<Organization>(5);
            for (Organization provider : providers) {
                ConcurrentMap<String, Set<Aggregator>> ccyPairProducts = providerCcyPairAggregatorCache.get(provider);
                if (ccyPairProducts != null) {
                    Set<Aggregator> requests = ccyPairProducts.get(ccyPair);

                    if (requests != null) {
                        boolean isRemoved = requests.remove(aggregator);
                        if (isRemoved && requests.size() == 0) {
                            ccyPairProducts.remove(ccyPair);
                            Response unSubResponse = unSubscribe(provider, product);
                            //ccyPairProducts.remove(ccyPair);

                            if (unSubResponse.getStatus() != Response.STATUS_OK) {
                                failedProviders.add(provider);
                            }
                        }

                        if (log.isDebugEnabled()) {
                            StringBuilder sb = new StringBuilder(150);
                            sb.append("unSubscribe()")
                            .append(' ').append(provider.getShortName())
                            .append(' ').append(ccyPair)
                            .append(' ').append(isRemoved)
                            .append(' ').append(requests.size())
                            .append(' ').append(failedProviders);
                            log.debug(sb.toString());
                        }
                    }

                    if (ccyPairProducts.size() == 0) {
                        providerCcyPairAggregatorCache.remove(provider);
                    }
                }
            }

            if (failedProviders.size() != 0) {
                if (failedProviders.size() == providers.size()) {
                    response.setStatus(Response.STATUS_FAIL);
                }
                StringBuilder _failedProviders = new StringBuilder(failedProviders.size() * 15);

                for (Organization org : failedProviders) {
                    _failedProviders.append(org.getShortName()).append(',');
                }
                response.setMsg("Failed provider subscriptions. " + _failedProviders.toString());

                if (log.isInfoEnabled()) {
                    StringBuilder sb = new StringBuilder(150)
                            .append(this.getClass().getName()).append(".unSubscribe")
                            .append(' ').append("Could not un-subscribe to these providers.")
                            .append(' ').append(response.getMsg());

                    log.info(sb.toString());
                }
            }
        } catch (Exception exc) {
            log.warn("ProviderSubscriptionManager.unSubscribe() could not unsubscribe to provider " +
                    aggregator.getProduct().getCurrencyPair().getName(), exc);
            response.setStatus(Response.STATUS_FAIL);
        }
        return response;
    }

    private Response unSubscribe(Organization provider, Product product) {
        Response response = new ResponseC();
        Request request = null;
        WorkflowMessage unSubscriptionMsg = null;

        try {
            String key = createProviderCcyPairKey(provider, product.getCurrencyPair());
            request = providerCcyPairRequestCache.get(key);
            if (request == null) {
                log.info(this.getClass().getName() + ".stop() request is null for  " + key);
                response.setStatus(Response.STATUS_FAIL);
                return response;
            }

            synchronized (providerCcyPairRequestCache) {
                unSubscriptionMsg = createUnsubscriptionMessage(provider, request);
                providerCcyPairRequestCache.remove(key);

                WorkflowMessage unSubscriptionResponse = sendToProvider(unSubscriptionMsg, product);

                //providerCcyPairRequestCache.remove(key);
                this.factory.removeQuoteHandler(product.getCurrencyPair(), provider);

                Message reply = unSubscriptionResponse.getReplyMessage();
                MessageStatus replyStatus = reply.getStatus();
                if (MessageStatus.FAILURE.equals(replyStatus)) {
                    response.setStatus(Response.STATUS_FAIL);
                }

                if (log.isDebugEnabled()) {
                    StringBuilder sb = new StringBuilder(100);
                    sb.append(this.getClass().getName()).append(".stop ");
                    sb.append("{ product = ").append(product);
                    sb.append(", ccyPair = ").append(product.getCurrencyPair().getName());
                    sb.append(", provider = ").append(provider);
                    sb.append(", request = ").append(request);
                    sb.append(", msg = ").append(unSubscriptionMsg);
                    sb.append('}');
                    log.debug(sb.toString());
                }
            }
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder(100);
            sb.append(this.getClass().getName()).append(".stop ");
            sb.append("{ product = ").append(product);
            sb.append(", ccyPair = ").append(product.getCurrencyPair().getName());
            sb.append(", provider = ").append(provider);
            sb.append(", request = ").append(request);
            sb.append(", msg = ").append(unSubscriptionMsg);
            sb.append('}');
            log.warn(sb.toString(), e);
        }
        return response;
    }

    protected WorkflowMessage sendToProvider(WorkflowMessage msg, Product product) throws RemoteException {
        Request request = (Request) msg.getObject();

        if (log.isInfoEnabled()) {
            StringBuilder sb = new StringBuilder(250)
                .append("PSM_sendToProvider")
                .append(" NAME=").append(msg.getEvent().getName())
                .append(" SENDER").append(msg.getSender().getShortName())
                .append(" MSG=").append(msg.getTo() == null ? "" : ((Organization)msg.getTo()).getShortName())
                .append(" ORG=").append(request.getOrganization().getShortName())
                .append(" CCY_PAIR=").append(request.getCurrencyPair().getName())
                .append(" CHANNEL=").append(request.getChannel().getName());
            log.info(sb.toString());
        }

        RequestService rs = getRequestService(request);
        WorkflowMessage response = rs.process(msg);
        log(msg, response, product);
        return response;
    }

    private static RequestService getRequestService(Request request) throws RemoteException {
        setSessionContext(request.getUser());
        return ServiceFactory.getISRequestService();
    }

    private static void setSessionContext(User user) {
        IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext(user);
        IdcSessionManager.getInstance().setSessionContext(ctx);
    }

    protected WorkflowMessage createSubscriptionMessage(Organization provider, Product product) {
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        Request request = createRequest(product.getCurrencyPair(), provider);        
        ProviderQuoteHandler quoteHandler = this.factory.getOrCreateQuoteHandler(product.getCurrencyPair(), provider);
        msg.setTo(provider);
        msg.setSender(request.getUser());
        msg.setEvent(MessageEvent.CREATE);
        msg.setTopic(ISConstantsC.MSG_TOPIC_REQUEST);
        msg.setParameterValue(ISConstantsC.MSG_REC_AT_WORKFLOW_TS, String.valueOf(System.currentTimeMillis()));
        msg.setParameterValue("messageHandler", quoteHandler);
        msg.setObject(request);
        return msg;
    }

    protected Request createRequest(CurrencyPair ccyPair, Organization provider) {
        Request request = DealingFactory.newRequest();
        request.setRequestClassification(getRequestClassification());
        request.getRequestAttributes().setExecutionType(REQUEST_HOLD_EXECUTION_TYPE);
        request.setChannel(getChannel());
        request.setToOrganizations(Collections.singletonList(provider));
        request.setRequestPrice(ISConstantsC.SINGLE_LEG, createPrice(ccyPair));
        request.setCurrencyPair(ccyPair);

        User aggregationUSer = this.config.getUser();
        request.setOrganization(aggregationUSer.getOrganization());
        request.setUser(aggregationUSer);
        return request;
    }

    private RequestClassification getRequestClassification() {
        return ISUtilImpl.getInstance().getRequestClassification(ISConstantsC.REQ_CLSF_PRICE);
    }

    protected FXLegDealingPrice createPrice(CurrencyPair ccyPair) {
        FXLegDealingPrice fxLegDealingPrice = FXDealingFactory.newFXLegDealingPrice();
        fxLegDealingPrice.setBidOfferMode(DealingPrice.TWO_WAY);
        fxLegDealingPrice.setTenor(Tenor.SPOT_TENOR);
        fxLegDealingPrice.setDealtCurrency(ccyPair.getBaseCurrency());
        fxLegDealingPrice.setSettledCurrency(ccyPair.getVariableCurrency());
        fxLegDealingPrice.setDealtAmount(0d);
        return fxLegDealingPrice;
    }

    private ExternalSystem getChannel() {
        String channelName = this.config.getProviderRequestChannel();
        channelName = channelName == null ? "ClientSDK" : channelName; // todo: Define new channel name for the aggregation service. Or force the end user to define there own channel.
        return ISUtilImpl.getInstance().getExternalSys(channelName);
    }

    protected WorkflowMessage createUnsubscriptionMessage(Organization provider, Request request) {
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setTo(provider);
        msg.setSender(request.getUser());
        msg.setEvent(MessageEvent.WITHDRAW);
        msg.setTopic(ISConstantsC.MSG_TOPIC_REQUEST);
        msg.setObject(request);
        return msg;
    }

    private void log(Message msg, Message response, Product product) {
        // log result
        Message reply = response.getReplyMessage();
        MessageStatus replyStatus = reply.getStatus();
        if (MessageStatus.SUCCESS.equals(replyStatus)) {
            // log success
            if (log.isDebugEnabled()) {
                StringBuilder s = new StringBuilder(100);
                s.append(this.getClass().getName()).append(".send succeeded : ");
                s.append("{ product = ").append(product);
                s.append(", msg = ").append(msg);
                s.append(", response = ").append(response);
                s.append(", reply = ").append(reply);
                s.append('}');
                log.debug(s.toString());
            }
        } else {
            // log failure
            StringBuilder s = new StringBuilder(100);
            s.append(this.getClass().getName()).append(".send failed : ");
            s.append("{ product = ").append(product);
            s.append(", msg = ").append(msg);
            s.append(", response = ").append(response);
            s.append(", reply = ").append(reply);
            for (Object o : reply.getErrors()) {
                ErrorMessage error = (ErrorMessage) o;
                s.append(", error code = ").append(error.getCode());
            }
            s.append('}');
            log.info(s.toString());
        }
    }

    protected Set<Organization> getProviders(Aggregator aggregator) {
        return AggregationServiceUtil.getProviders(aggregator.getProduct(), this.config);
    }

    public ConcurrentMap<Organization, ConcurrentMap<String, Set<Aggregator>>> getProviderCcyPairAggregatorCache() {
        return providerCcyPairAggregatorCache;
    }

    public ConcurrentMap<String, Request> getCcyPairRequestCache() {
        return providerCcyPairRequestCache;
    }

    public boolean isSubscriptionAvailable() {
        return providerCcyPairAggregatorCache.size() != 0 || providerCcyPairRequestCache.size() != 0;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder(200)
            .append(this.getClass().getName())
            .append(' ').append(this.aggregationProcessor.getFiOrg().getName());

        sb.append(' ').append('{');

        for (Map.Entry<Organization, ConcurrentMap<String, Set<Aggregator>>> record : providerCcyPairAggregatorCache.entrySet()) {
            sb.append(record.getKey().getName()).append('[');

            for (Map.Entry<String, Set<Aggregator>> ccyPairRequest : record.getValue().entrySet()) {
                sb.append(' ').append(ccyPairRequest.getKey()).append(' ').append('[');

                for (Aggregator aggregator : ccyPairRequest.getValue()) {
                    sb.append(aggregator.getObjectId()).append(',');
                }
                sb.append(']').append(' ');
            }
        }
        sb.append('}').append(' ');

        sb.append('{');

        for (Map.Entry<String, Request> record : providerCcyPairRequestCache.entrySet()) {
            sb.append(' ').append(record.getKey()).append(' ').append('[');

            sb.append(' ').append(record.getValue())
                    .append(' ').append(record.getValue().getCurrencyPair().getName())
                    .append(' ').append(record.getValue().getOrganization().getShortName())
                    .append(' ').append(record.getValue().getToOrganization().getShortName())
                    .append(']').append(' ');
        }

        sb.append('}');
        return sb.toString();
    }
}
