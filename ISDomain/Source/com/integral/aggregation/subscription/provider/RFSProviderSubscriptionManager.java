package com.integral.aggregation.subscription.provider;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.Response;
import com.integral.aggregation.ResponseC;
import com.integral.aggregation.stream.Aggregator;
import com.integral.aggregation.subscription.AggregationProcessorRequest;
import com.integral.aggregation.subscription.AggregationRequest;
import com.integral.aggregation.subscription.RFSAggregationProcessorRequest;
import com.integral.aggregation.subscription.RFSAggregationRequest;
import com.integral.aggregation.subscription.provider.quote.ProviderQuoteHandler;
import com.integral.aggregation.subscription.provider.quote.RFSProviderQuoteHandler;
import com.integral.broker.model.Product;
import com.integral.finance.dealing.Request;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.rfs.util.RfsUtils;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageHandler;
import com.integral.user.Organization;

/**
 *   Handles RFS Subscription for RFS Aggregation Request
 */
public class RFSProviderSubscriptionManager extends ProviderSubscriptionManager {
    private Log LOGGER = LogFactory.getLog(RFSProviderSubscriptionManager.class);
    private static final RfsUtils RFS_UTILS = RfsUtils.getInstance();
    
    public RFSProviderSubscriptionManager(AggregationProcessor service) {
        super(service);        
    }

    /**
     *
     * @param product
     * @return
     */
    public Response subscribe(Aggregator aggregator) {
    	AggregationProcessorRequest request = aggregator.getAggregationProcessorRequest();
        Response response = request.createResponse();
        Product product = request.getProduct();

        try {
            Set<Organization> providers = getProviders(aggregator);
            updateRequest(request, providers, product);
        } catch (Exception exc) {
        	LOGGER.error("RFSProviderSubscriptionManager.subscribe() error while subscribing to provider " + product.getCurrencyPair().getName());
            response.setStatus(Response.STATUS_FAIL);
        }
        return response;
    }

    
    @Override
	public Response unSubscribe(Aggregator aggregator) {
        AggregationProcessorRequest processorRequest = aggregator.getAggregationProcessorRequest();
        LOGGER.info("RFSProviderSubscriptionManager.unSubscribe:: stopping ProviderQuoteHandler for " + aggregator.getAggregatorId());
        if(processorRequest instanceof RFSAggregationProcessorRequest){
            ProviderQuoteHandler quoteHandler = ((RFSAggregationProcessorRequest) processorRequest).getProviderQuoteHandler(null);
            if(quoteHandler != null) {
                quoteHandler.stop();
                LOGGER.info("RFSProviderSubscriptionManager.unSubscribe:: stopped ProviderQuoteHandler for " + aggregator.getAggregatorId());
            }
        }
        Response response = new ResponseC();
  		response.setStatus(Response.STATUS_OK);
		return response;
	}
    
    /**
     * Modify request so that appropriate handler is called.
     * @param rfsAggregationProcessorRequest
     * @param providers
     * @param product
     */
    private void updateRequest(AggregationProcessorRequest rfsAggregationProcessorRequest, Set<Organization> providers, Product product) {
    	RFSAggregationRequest rfsRequest = (RFSAggregationRequest)rfsAggregationProcessorRequest.getAggregationRequest();
    	Request request = rfsRequest.getWrappedRequest();
    	request.setToOrganizations( Collections.unmodifiableSet(providers));
    	ProviderQuoteHandler quoteHandler = this.aggregationProcessor.getFactory().getOrCreateQuoteHandler(product.getCurrencyPair(), null);
    	// Set Aggregated Rate Handler with RFS Provider Quote Handler
    	MessageHandler aggregatedRateHandler = (MessageHandler)rfsAggregationProcessorRequest.getPriceHandler();
    	((RFSProviderQuoteHandler)quoteHandler).setAggregatedRateHandler(aggregatedRateHandler);
    	// set quote handler with Aggregation Processor Request
        ((RFSAggregationProcessorRequest)rfsAggregationProcessorRequest).addProviderQuoteHandler(null, quoteHandler); 
        ((RFSAggregationProcessorRequest)rfsAggregationProcessorRequest).addProviders(providers);
        RFSAggregationRequest rfsAggregationRequest = (RFSAggregationRequest)rfsAggregationProcessorRequest.getAggregationRequest();
        rfsAggregationRequest.setProviderQuoteHandler(quoteHandler);
        
    }
    

    @Override
	protected Set<Organization> getProviders(Aggregator aggregator) {	
    	AggregationProcessorRequest aggregationProcessorRequest = aggregator.getAggregationProcessorRequest();
    	AggregationRequest aggregationRequest = aggregationProcessorRequest.getAggregationRequest();
    	Request wrappedRequest = aggregationRequest.getWrappedRequest(); 
    	boolean isSEF = wrappedRequest.isSEF();
        boolean isMTF = wrappedRequest.isMTF();
    	List<String> orgsToBeSubscribed = isSEF || isMTF ? wrappedRequest.getOrgsToBeSubscribedPostValidations(): RFS_UTILS.getOrgsToBeSubscribed(wrappedRequest);
        Set<Organization> providers = new HashSet<Organization>();
        for (String org : orgsToBeSubscribed) {
        	Organization organization = ISUtilImpl.getInstance().getOrg(org);
        	providers.add(organization);
        }        
		return providers;
	}    

}
