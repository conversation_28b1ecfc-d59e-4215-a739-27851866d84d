package com.integral.aggregation.subscription;

import com.integral.aggregation.model.TierBasedSpread;
import com.integral.aggregation.stream.view.LadderViewRequest;
import com.integral.broker.model.Product;
import com.integral.broker.model.Stream;
import com.integral.aggregation.AggregationResponseHandler;
import com.integral.aggregation.Response;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.finance.currency.Currency;
import com.integral.user.User;

/**
 *
 */
public class AggregationProcessorRequestC extends RequestC implements AggregationProcessorRequest {
    private User user;
    private Stream stream;
    private Product product;
    private String aggregatorId;
    private Currency dltCcy;
    private Double minQty;
    private boolean isZeroSizePricesEnabled;
    private boolean dropFXIDirectStreamPrices;
    private AggregationResponseHandler<Response, FXPriceBook> priceHandler;
    private LadderViewRequest viewRequest;
    protected AggregationRequest aggregationRequest;
    private TierBasedSpread tierBasedSpread;
    private boolean isClientTagSubscription;
    private String clientTag;

    public AggregationProcessorRequestC() {
        //super(AggregationProcessorRequestC.class);
    }

    public String getStreamId() {
        return stream != null ? String.valueOf(stream.getObjectID()) : "";
    }

    public Product getProduct() {
        return product;
    }

    public AggregationResponseHandler<Response, FXPriceBook> getPriceHandler() {
        return priceHandler;
    }

    public void setProduct(Product product) {
        this.product = product;
        this.stream = product.getConfiguration().getStream();
    }

    public void setPriceHandler(AggregationResponseHandler<Response, FXPriceBook> priceHandler) {
        this.priceHandler = priceHandler;
    }

    public LadderViewRequest getViewRequest() {
        return viewRequest;
    }

    public void setViewRequest(LadderViewRequest viewRequest) {
        this.viewRequest = viewRequest;
    }

    public String getAggregatorId() {
        return aggregatorId == null ? String.valueOf(product.getObjectID()) : aggregatorId;
    }

    public void setAggregatorId(String aggregatorId) {
        this.aggregatorId = aggregatorId;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    @Override
    public Currency getDltCcy() {
        return dltCcy;
    }

    @Override
    public void setDltCcy(Currency dltCcy) {
        this.dltCcy = dltCcy;
    }

    @Override
    public Double getMinQty() {
        return minQty;
    }

    @Override
    public void setMinQty(Double minQty) {
        this.minQty = minQty;
    }

    @Override
    public boolean isZeroSizePricesEnabled() {
        return isZeroSizePricesEnabled;
    }

    @Override
    public void setZeroSizePricesEnabled(boolean isZeroSizePricesEnabled) {
        this.isZeroSizePricesEnabled = isZeroSizePricesEnabled;
    }

    @Override
    public boolean dropFXIDirectStreamPrices() {
        return dropFXIDirectStreamPrices;
    }

    @Override
    public void setDropFXIDirectStreamPrices(boolean dropFXIDirectStreamPrices) {
        this.dropFXIDirectStreamPrices = dropFXIDirectStreamPrices;
    }

    @Override
	public AggregationRequest getAggregationRequest() {
		return this.aggregationRequest;
	}

	@Override
	public void setAggregationRequest(AggregationRequest aggregationRequest) {
		this.aggregationRequest = aggregationRequest;
	}

    public TierBasedSpread getTierBasedSpread()
    {
        return tierBasedSpread;
    }

    public void setTierBasedSpread( TierBasedSpread tierBasedSpread )
    {
        this.tierBasedSpread = tierBasedSpread;
    }

    public void setClientTagSubscription(boolean clientTagSubscription){
        this.isClientTagSubscription = clientTagSubscription;
    }

    public boolean isClientTagSubscription(){
        return this.isClientTagSubscription;
    }

    public void setSubscriptionClientTag(String clientTag){
        this.clientTag = clientTag;
    }

    public String getSubscriptionClientTag(){
        return clientTag;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder(150)
            .append(this.getClass().getName())
            .append(' ').append( getRequestId() )
            .append(' ').append(user == null ? ' ' : user.getShortName())
            .append(' ').append(product.getCurrencyPair().getName())
            .append(' ').append(dltCcy == null ? ' ' : dltCcy.getName())
            .append(' ').append(minQty == null ? ' ' : minQty)
            .append(' ').append(isZeroSizePricesEnabled)
            .append(' ').append(dropFXIDirectStreamPrices)
            .append(' ').append(stream.getBrokerOrganizationFunction().getOrganization().getName())
            .append(' ').append(priceHandler)
            .append(' ').append(viewRequest)
            .append(' ').append(isClientTagSubscription);
        return sb.toString();
    }

}
