package com.integral.aggregation.subscription;

import com.integral.aggregation.AggregationMethod;
import com.integral.aggregation.model.TierBasedSpread;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Request;
import com.integral.aggregation.AggregationResponseHandler;
import com.integral.aggregation.Response;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.ArrayList;
import java.util.List;

/**
 *  It is used to request the AggregationService for the aggregated feed.
 *
 */
public class AggregationRequestC extends RequestC implements AggregationRequest {
	private AggregationRequestType requestType;
    private Organization organization;
    private LegalEntity legalEntity;
    private User user;
    private CurrencyPair ccyPair;
    private Currency dealtCcy;
    private List<Organization> priceProviders;
    private double subscriptionSize;
    private AggregationMethod aggregationMethod;
    private AggregationResponseHandler<Response, FXPriceBook> priceHandler;
    private List<Double> viewTiers;
    private Double minQty ;
    private Request wrappedRequest;
    private boolean isZeroSizePricesEnabled;
    private boolean dropFXIDirectStreamPrices;
    private boolean fullAmount;
    private TierBasedSpread tierBasedSpread;
    public boolean isTierBasedSpreadEnabled = false;
    public boolean isClientTagSubscription = false;
    private String clientTag;
    private Long aggregationPublishInterval;
    private Long aggregationCheckInterval;

    public AggregationRequestC() {
        //super(AggregationRequestC.class);
    }

    public AggregationRequestType getRequestType() {
        return requestType;
    }

    public void setRequestType(AggregationRequestType requestType) {
        this.requestType = requestType;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public LegalEntity getLegalEntity() {
        return legalEntity;
    }

    public void setLegalEntity(LegalEntity legalEntity) {
        this.legalEntity = legalEntity;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public CurrencyPair getCcyPair() {
        return ccyPair;
    }

    public void setCcyPair(CurrencyPair ccyPair) {
        this.ccyPair = ccyPair;
    }

    @Override
    public Currency getDealtCcy() {
        return dealtCcy;
    }

    @Override
    public void setDealtCcy(Currency dealtCcy) {
        this.dealtCcy = dealtCcy;
    }

    public List<Organization> getPriceProviders() {
        return priceProviders;
    }

    public void setPriceProviders(List<Organization> priceProviders) {
        this.priceProviders = priceProviders;
    }

    public double getSubscriptionSize() {
        return subscriptionSize;
    }

    public void setSubscriptionSize(double subscriptionSize) {
        this.subscriptionSize = subscriptionSize;
    }

    public AggregationMethod getAggregationMethod() {
        return aggregationMethod;
    }

    public void setAggregationMethod(AggregationMethod aggregationMethod) {
        this.aggregationMethod = aggregationMethod;
    }

    public AggregationResponseHandler<Response, FXPriceBook> getPriceHandler() {
        return priceHandler;
    }

    public void setPriceHandler(AggregationResponseHandler<Response, FXPriceBook> priceHandler) {
        this.priceHandler = priceHandler;
    }

    public List<Double> getViewTiers() {
        return viewTiers;
    }

    public void setViewTiers(List<Double> tiers) {
        this.viewTiers = tiers;
    }

    public void addViewTier(Double tier) {
        if (viewTiers == null) {
            viewTiers = new ArrayList<Double>();
        }
        viewTiers.add(tier);
    }

    public Double getMinQty() {
        return minQty;
    }

    public void setMinQty(Double minQty) {
        this.minQty = minQty;
    }

    public boolean isZeroSizePricesEnabled() {
        return isZeroSizePricesEnabled;
    }

    public void setZeroSizePricesEnabled(boolean isZeroSizePricesEnabled) {
        this.isZeroSizePricesEnabled = isZeroSizePricesEnabled;
    }

    @Override
    public boolean dropFXIDirectStreamPrices() {
        return dropFXIDirectStreamPrices;
    }

    @Override
    public void setDropFXIDirectStreamPrices(boolean dropFXIDirectStreamPrices) {
        this.dropFXIDirectStreamPrices = dropFXIDirectStreamPrices;
    }

    public boolean isFullAmount() {
        return fullAmount;
    }

    public void setFullAmount(boolean fullAmount) {
        this.fullAmount = fullAmount;
    }

    @Override
	public Request getWrappedRequest() {
    	return this.wrappedRequest;
	}

	@Override
	public void setWrappedRequest(Request wrappedRequest) {
		this.wrappedRequest = wrappedRequest;
	}

    @Override
    public void setTierBasedSpread( TierBasedSpread tierBasedSpread )
    {
        this.tierBasedSpread = tierBasedSpread;
    }

    @Override
    public TierBasedSpread getTierBasedSpread()
    {
        return tierBasedSpread;
    }

    @Override
    public boolean isTierBasedSpreadEnabled()
    {
        return isTierBasedSpreadEnabled;
    }

    @Override
    public void setTierBasedSpreadEnabled( boolean isEnabled )
    {
        this.isTierBasedSpreadEnabled = isEnabled;
    }

    public void setClientTagSubscription(boolean clientTagSubscription){
        this.isClientTagSubscription = clientTagSubscription;
    }

    public String getSubscriptionClientTag(){
        return clientTag;
    }

    public void setSubscriptionClientTag(String clientTag){
        this.clientTag = clientTag;
    }

    public boolean isCleintTagSubscription(){
        return this.isClientTagSubscription;
    }

    @Override
    public Long getAggregationPublishInterval() {
        return aggregationPublishInterval;
    }
    @Override
    public void setAggregationPublishInterval(Long aggregationPublishInterval) {
        this.aggregationPublishInterval = aggregationPublishInterval;
    }
    @Override
    public Long getAggregationCheckInterval() {
        return aggregationCheckInterval;
    }
    @Override
    public void setAggregationCheckInterval(Long aggregationCheckInterval) {
        this.aggregationCheckInterval = aggregationCheckInterval;
    }

    //TODO please change this method for minQty field accordingly
    @Override
	public String toString() {
		return "AggregationRequestC [requestType=" + requestType
				+ ", organization=" + organization + ", legalEntity="
				+ legalEntity + ", user=" + user + ", ccyPair=" + ccyPair
				//+ ", priceProviders=" + priceProviders // It's getting very big string in the performance lab.
                + ", subscriptionSize="
				+ subscriptionSize + ", aggregator=" + (aggregationMethod == null ? "" : aggregationMethod.getShortName())
				+ ", priceHandler=" + priceHandler + ", viewTiers=" + viewTiers
                + ", minQty=" + (minQty == null ? " " : minQty)
                + ", checkInt=" + aggregationCheckInterval
                + ", publishInt=" + aggregationPublishInterval
				+ "]";
	}

}
