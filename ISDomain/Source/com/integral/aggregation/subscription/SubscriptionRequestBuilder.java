package com.integral.aggregation.subscription;

import com.integral.broker.model.Product;
import com.integral.broker.model.Stream;
import com.integral.exception.IdcInvalidArgumentException;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.currency.CurrencyPair;
import com.integral.aggregation.AggregationServiceUtil;
import com.integral.aggregation.*;
import com.integral.aggregation.price.FXPriceBook;

/**
 *
 */
public class SubscriptionRequestBuilder extends RequestBuilder<AggregationRequestBuilder> {
    private AggregationProcessorConfig config;

    private LegalEntity brokerLE;
    private TradingParty customerTradingParty;
    private CurrencyPair ccyPair;

    private AggregationResponseHandler<Response, FXPriceBook> priceHandler;

    public SubscriptionRequestBuilder(AggregationProcessor service) {
        super(AggregationRequestBuilder.class);
        this.config = service.getConfig();
        this.brokerLE = config.getLegalEntity();
    }

    public SubscriptionRequestBuilder setCcyPair(CurrencyPair ccyPair) {
        this.ccyPair = ccyPair;
        return this;
    }

    public SubscriptionRequestBuilder setCustomerTradingParty(TradingParty customerTradingParty) {
        this.customerTradingParty = customerTradingParty;
        return this;
    }

    public SubscriptionRequestBuilder setPriceHandler(AggregationResponseHandler<Response, FXPriceBook> priceHandler) {
        this.priceHandler = priceHandler;
        return this;
    }

    public AggregationProcessorRequest build() throws IdcInvalidArgumentException {
        validate();
        Stream stream = customerTradingParty.getBrokerStream();

        Product product = stream.getProduct(ccyPair);
        if (product == null) {
            throw new IdcInvalidArgumentException(stream.getShortName() + " does not contain any active configuration(s) which support currency pair "
                    + ccyPair.getName() +  " broker LE " + this.brokerLE);
        }

        // todo: populate all the custom properties here.
        //:todo remove from here after, have a generic mechanism to update the property settings to product like this.
//        Double minTradeSize = ISFactory.getInstance().getISMBean().getMinimumTradeSize
//                (product.getConfiguration().getStream().getBrokerOrganizationFunction().getOrganization()
//                        , product.getCurrencyPair());
//        minTradeSize = minTradeSize == null ? 0.0 : minTradeSize;
//        product.setMinTradeSize(minTradeSize);

        AggregationProcessorRequest processorRequest = new AggregationProcessorRequestC();
        processorRequest.setRequestId(AggregationServiceUtil.generateUniqueNewId());
        processorRequest.setProduct(product);
        processorRequest.setPriceHandler(priceHandler);

        return processorRequest;
    }

    public void validate()throws IdcInvalidArgumentException {
        validateField(priceHandler, "PriceHandler is not defined for broker LE " + this.brokerLE);
        validateField(customerTradingParty, "Customer trading party is not defined for broker LE " + this.brokerLE);
        validateField(ccyPair, "CurrencyPair is not defined for broker LE " + this.brokerLE);
    }

}
