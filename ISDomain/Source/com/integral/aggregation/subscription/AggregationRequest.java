package com.integral.aggregation.subscription;

import java.util.List;

import com.integral.aggregation.AggregationMethod;
import com.integral.aggregation.AggregationResponseHandler;
import com.integral.aggregation.Response;
import com.integral.aggregation.model.TierBasedSpread;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPair;
import com.integral.user.Organization;
import com.integral.user.User;

/**
 *  It is used to request the AggregationService for the aggregated feed.
 *
 */
public interface AggregationRequest extends Request {

    /**
     * Returns the request type (AggregationRequestType.SUBSCRIBE, AggregationRequestType.UNSUBSCRIBE)
     * @return
     */
    AggregationRequestType getRequestType();

    /**
     * Sets the request type.
     * @param requestType
     * @return
     */
    void setRequestType(AggregationRequestType requestType);

    /**
     * Returns the customer organization.
     * @return
     */
    Organization getOrganization();

    /**
     * Sets the customer organization.
     * @param organization
     * @return
     */
    void setOrganization(Organization organization);

    /**
     * Returns the customer legal entity.
     * @return
     */
    LegalEntity getLegalEntity();

    /**
     * Sets the customer legal entity. (Optional)
     * @param legalEntity
     * @return
     */
    void setLegalEntity(LegalEntity legalEntity);

    /**
     * Returns the customer.
     * @return
     */
    User getUser();

    /**
     * Sets the customer.
     * @param user
     * @return
     */
    void setUser(User user);

    /**
     * Returns the currency pair.
     * @return
     */
    CurrencyPair getCcyPair();

    /**
     * Sets the currency pair.
     * @param ccyPair
     * @return
     */
    void setCcyPair(CurrencyPair ccyPair);

    /**
     * Returns the list of price providers.
     * @return
     */
    List<Organization> getPriceProviders();

    /**
     * Sets the list of price providers. Price of these providers will be used to created the aggregated view.
     *
     * @param priceProviders
     * @return
     */
    void setPriceProviders(List<Organization> priceProviders);

    /**
     * Returns the user request size.
     * @return
     */
    double getSubscriptionSize();

    /**
     * Sets the request size to be used while creating the aggregated view. (Optional)
     * @param subscriptionSize
     * @return
     */
    void setSubscriptionSize(double subscriptionSize);

    /**
     * Returns the aggregation response handler.
     * @return
     */
    AggregationResponseHandler<Response, FXPriceBook> getPriceHandler();

    /**
     * Sets the aggregation response handler. This handler will be call backed for the aggregated view.
     * In case of asynchronous request, this handler will be invoked for the response also.
     * @param priceHandler
     * @return
     */
    void setPriceHandler(AggregationResponseHandler<Response, FXPriceBook> priceHandler);

    /**
     * Returns the list requested view tiers.
     * @return
     */
    List<Double> getViewTiers();

    /**
     * Sets the list of view tiers. Aggregated view will be created for these tiers only. (Optional)
     * @param tiers
     * @return
     */
    void setViewTiers(List<Double> tiers);

    /**
     * Add the view tier.
     *
     * @param tier
     * @return
     */
    void addViewTier(Double tier);

    /**
     * Returns the aggregation method.
     * @return
     */
    AggregationMethod getAggregationMethod();

    /**
     * Sets the aggregation method to be used for creating the aggregated view.
     * @param aggregationMethod
     */
    void setAggregationMethod(AggregationMethod aggregationMethod);
    
    /**
     * Returns the minimum quantity.
     * @return Double
     */
    Double getMinQty();

    /**
     * Sets the minimum quantity size. Aggregation will not use the provider rates, if their sizes are less than minQty.
     * @param minQty
     */
    void setMinQty(Double minQty);

    /**
     *  Returns whether zero size prices are considered in aggregation
     * @return Boolean
     */
    boolean isZeroSizePricesEnabled();

    /**
     * sets whether zero size prices should be considered in aggregation or not
     * @param zeroSizePricesEnabled
     */
    void setZeroSizePricesEnabled(boolean zeroSizePricesEnabled);

    /**
     * Returns whether fxi-direct stream prices should be dropped in aggregation
     * @return Boolean
     */
    boolean dropFXIDirectStreamPrices();

    /**
     * Sets whether fxi-direct stream prices should be dropped in aggregation
     * @return
     */
    void setDropFXIDirectStreamPrices(boolean dropFXIDirectStreamPrices);

    Currency getDealtCcy();

    void setDealtCcy(Currency dealtCcy);
    
    com.integral.finance.dealing.Request getWrappedRequest();
    
    void setWrappedRequest(com.integral.finance.dealing.Request request);

    void setTierBasedSpread(TierBasedSpread tierBasedSpread);

    public TierBasedSpread getTierBasedSpread();

    public boolean isTierBasedSpreadEnabled();

    public void setTierBasedSpreadEnabled(boolean isEnabled);

    public boolean isCleintTagSubscription();

    public void setClientTagSubscription(boolean clientTagSubscription);

    public String getSubscriptionClientTag();

    public void setSubscriptionClientTag(String clientTag);

    public boolean isFullAmount();

    public void setFullAmount(boolean fullAmount);
    Long getAggregationCheckInterval();
    void setAggregationCheckInterval(Long interval);
    void setAggregationPublishInterval(Long interval);
    Long getAggregationPublishInterval();
}
