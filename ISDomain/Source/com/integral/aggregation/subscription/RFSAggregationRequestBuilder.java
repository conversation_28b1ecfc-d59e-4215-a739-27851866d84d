package com.integral.aggregation.subscription;

import com.integral.aggregation.AggregationProcessor;
import com.integral.aggregation.AggregationType;
import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.config.AggregationServiceMBean;
import com.integral.broker.model.Configuration;
import com.integral.broker.model.ModelFactory;
import com.integral.broker.model.Product;
import com.integral.finance.dealing.Request;

public class RFSAggregationRequestBuilder extends AggregationRequestBuilder {
    private RFSAggregationRequest rfsRequest;

    public RFSAggregationRequestBuilder(AggregationProcessor aggregator) {
        super(aggregator);
    }

    public RFSAggregationProcessorRequest build(AggregationRequest request) {
    	Request wrappedRequest = request.getWrappedRequest();    	
        setRequestId(wrappedRequest.getTransactionID());
        setRequestType(request.getRequestType());
        setAggregator(AggregationType.getAggregationType(request.getAggregationMethod()));
        setOrganization(request.getOrganization());
        setCcyPair(request.getCcyPair());
        setLegalEntity(request.getLegalEntity());
        setPriceProviders(request.getPriceProviders());
        setSubscriptionSize(request.getSubscriptionSize());
        setPriceHandler(request.getPriceHandler());
        rfsRequest = (RFSAggregationRequest)request;
        return build();
    }

    public RFSAggregationProcessorRequest build() {    
        Product product = createNewProduct();
        RFSAggregationProcessorRequest request = new RFSAggregationProcessorRequestC();
        request.setRequestId(requestId);
        request.setProduct(product);
        request.setPriceHandler(priceHandler);        
        request.setAggregationRequest(rfsRequest);
        rfsRequest.setRFSAggregationProcessorRequest(request);
        return request;
    }
    
    protected Product createNewProduct() {
        Product product = ModelFactory.getInstance().newProduct();
        Configuration configuration = createConfiguration();
        configuration.setSpreadMinimumEnabled(false);
        configuration.setSpreadMaximumEnabled(false);
        long prodID = productID.getAndIncrement();
        product.setObjectID(prodID);
        product.setCurrencyPair(this.ccyPair);
        product.setConfiguration(configuration);
        product.setSubscriptionSize(this.subscriptionSize);
        product.setESP(false);
        product.setActive(true);
        product.setPriceMakingActive(true);
        product.setOrderExecutionActive(true);
        product.setMinTradeSize(getMinTradeSize());
        product.setMinQuoteSize(getMinQuoteSize());
        product.setAggExecutionRuleEnabled(AggregationServiceFactory.getInstance().getRFSAggregationMBean().isAggExecRuleEnabled(this.organization.getShortName()));
        if (log.isDebugEnabled()) {
        	log.debug("RFSAggregationRequestBuilder::createProduct() product: " + product);
        }
        return product;
    }

	@Override
	protected Configuration createConfiguration() {		
		Configuration configuration = super.createConfiguration();	
		return configuration;
	}
	
    protected void updateConfigurationParameters(final Configuration configuration) { 
    	configuration.setRFSEnabled(true);
        configuration.setRFSSpreadMinimumEnabled(false);
    }
	
	protected AggregationServiceMBean getAggregationServiceMBean() {
	    return AggregationServiceFactory.getInstance().getRFSAggregationMBean();
	}
    
}
