package com.integral.aggregation.subscription;

import com.integral.aggregation.*;
import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.config.AggregationServiceMBean;
import com.integral.aggregation.model.AggregationParameters;
import com.integral.aggregation.model.TierBasedSpread;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.stream.view.LadderViewRequest;
import com.integral.broker.model.Tier;
import com.integral.broker.model.*;
import com.integral.categorization.StreamCategory;
import com.integral.exception.IdcInvalidArgumentException;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.fx.FXRateConvention;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.taker.TakerOrganizationFunction;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 *
 */
public class AggregationRequestBuilder extends RequestBuilder<AggregationRequestBuilder> {
    protected AggregationProcessor processor;
    protected AggregationProcessorConfig config;

    protected Organization organization;
    protected CurrencyPair ccyPair;
    protected Currency dltCcy;
    protected LegalEntity legalEntity;
    protected User user;
    protected List<Organization> priceProviders;
    protected double subscriptionSize;
    protected long quotePublicationInterval;
    protected long bestBifOfferPublicationInterval;
    protected boolean useAggregationIntervalFromRequest;
    protected AggregationType aggregator;
    protected AggregationRequestType requestType;
    protected AggregationResponseHandler<Response, FXPriceBook> priceHandler;
    protected String requestId;
    protected List<Double> tiers;
    protected TierBasedSpread tierBasedSpread;
    protected Double minQty ;
    protected  boolean isZeroSizePricesEnabled;
    protected boolean dropFXIDirectStreamPrices;
    protected boolean fullAmount;
    public boolean isTierBasedSpreadEnabled;
    public String subscriptionClientTag;

	protected static AtomicLong productID = new AtomicLong( 1 );

    public AggregationRequestBuilder(AggregationProcessor aggregationProcessor) {
        super(AggregationRequestBuilder.class);
        this.processor = aggregationProcessor;
        this.config = this.processor.getConfig();
        this.organization = config.getOrganization();
        this.legalEntity = config.getLegalEntity();
        this.priceProviders = config.getPriceProviders();
        this.subscriptionSize = config.getSubscriptionSize();
        this.quotePublicationInterval = config.getQuotePublicationInterval();
        this.bestBifOfferPublicationInterval = config.getBestBidOfferPublicationInterval();
        this.aggregator = config.getAggregator();
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public void setCcyPair(CurrencyPair ccyPair) {
        this.ccyPair = ccyPair;
    }

    public void setDltCcy(Currency dltCcy) {
        this.dltCcy = dltCcy;
    }

    public void setLegalEntity(LegalEntity legalEntity) {
        this.legalEntity = legalEntity;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public void setPriceProviders(List<Organization> priceProviders) {
        this.priceProviders = priceProviders;
    }

    public void setSubscriptionSize(double subscriptionSize) {
        this.subscriptionSize = subscriptionSize;
    }

    public void setQuotePublicationInterval(long quotePublicationInterval) {
        this.quotePublicationInterval = quotePublicationInterval;
    }

    public void setBestBifOfferPublicationInterval(long bestBifOfferPublicationInterval) {
        this.bestBifOfferPublicationInterval = bestBifOfferPublicationInterval;
    }

    public void setAggregator(AggregationType aggregator) {
        this.aggregator = aggregator;
    }

    public void setPriceHandler(AggregationResponseHandler<Response, FXPriceBook> priceHandler) {
        this.priceHandler = priceHandler;
    }

    public void setRequestType(AggregationRequestType requestType) {
        this.requestType = requestType;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public void setTiers(List<Double> tiers) {
        this.tiers = tiers;
    }

	public void setMinQty(Double minQty) {
		this.minQty = minQty;
	}

    public void setSubscriptionClientTag(String clientTag){
        this.subscriptionClientTag = clientTag;
    }

    public void setZeroSizePricesEnabled(boolean isZeroSizePricesEnabled) {
        this.isZeroSizePricesEnabled = isZeroSizePricesEnabled;
    }

    public void setDropFXIDirectStreamPrices(boolean dropFXIDirectStreamPrices) {
        this.dropFXIDirectStreamPrices = dropFXIDirectStreamPrices;
    }

    public void setTierBasedSpreadEnabled(boolean isTierBasedSpreadEnabled) {
        this.isTierBasedSpreadEnabled = isTierBasedSpreadEnabled;
    }

    public void setFullAmount(boolean fullAmount) {
        this.fullAmount = fullAmount;
    }

    public boolean isUseAggregationIntervalFromRequest() {
        return useAggregationIntervalFromRequest;
    }

    public void setUseAggregationIntervalFromRequest(boolean useAggregationIntervalFromRequest) {
        this.useAggregationIntervalFromRequest = useAggregationIntervalFromRequest;
    }

    public AggregationProcessorRequest build() {
        //validate();

        AggregationProcessorRequest processorRequest = null;
        Product product = null;
        processorRequest = this.processor.getFactory().newAggregationProcessorRequest(requestId);
        processorRequest.setUser(this.user);
        processorRequest.setDltCcy(this.dltCcy);
        processorRequest.setMinQty(getMinQty());
        processorRequest.setZeroSizePricesEnabled(isZeroSizePricesEnabled());
        processorRequest.setDropFXIDirectStreamPrices(dropFXIDirectStreamPrices());
        if (requestType == AggregationRequestType.SUBSCRIBE) {
            product = createNewProduct();
            processorRequest.setPriceHandler(this.priceHandler);
            processorRequest.setProduct(product);
            processorRequest.setAggregatorId(createAggregatorId());

            if ((tiers != null && tiers.size() > 0) &&
                    (aggregator == null || isLadderViewAggregator())) {
                LadderViewRequest viewRequest = this.processor.getFactory().newLadderViewRequest();
                viewRequest.setTiers(new TreeSet<Double>(tiers));
                viewRequest.setHandler(this.priceHandler);
                processorRequest.setViewRequest(viewRequest);
            }
        } else if (requestType == AggregationRequestType.UN_SUBSCRIBE) {

        } else if (requestType == AggregationRequestType.UPDATE) {
            product = createNewProduct();
            processorRequest.setPriceHandler(this.priceHandler);
            processorRequest.setProduct(product);
            processorRequest.setAggregatorId(createAggregatorId());
        }
        processorRequest.setTierBasedSpread( tierBasedSpread );
        if(this.subscriptionClientTag != null){
            processorRequest.setSubscriptionClientTag( this.subscriptionClientTag );
            processorRequest.setClientTagSubscription( true );
        }

        return processorRequest;
    }

    private boolean isLadderViewAggregator() {
        return aggregator.equals(AggregationType.WeightedAverageQuoteAggregation);
    }

    private String createAggregatorId() {
        StringBuilder sb = new StringBuilder(150);
        sb.append('[');
        sb.append(this.organization.getShortName()).append('|');
        sb.append(this.ccyPair.getName()).append('|');

        if (this.dltCcy != null) {
            sb.append(this.dltCcy.getName());
        }
        sb.append('|');

        if (this.minQty != null) {
            sb.append(this.minQty);
        }
        sb.append('|');
        
        if(this.isZeroSizePricesEnabled()){
            sb.append(this.isZeroSizePricesEnabled());
        }
        sb.append('|');

        if(this.dropFXIDirectStreamPrices()) {
            sb.append(this.dropFXIDirectStreamPrices());
        }
        sb.append('|');

        if (this.aggregator != null) {
            sb.append(this.aggregator.getShortName());
        }
        sb.append('|');

        if (this.priceProviders != null) {
            for (Organization provider : this.priceProviders) {
                sb.append(provider.getShortName()).append(',');
            }
        }
        sb.append('|');
        boolean isStreamCategoryEnabled = AggregationServiceUtil.isStreamCategoryProviderPriorityEnabled(organization, ccyPair);
        if(isStreamCategoryEnabled) {
            if(this.tiers != null) {
                for (Double tier : this.tiers) {
                    sb.append(tier).append(',');
                }
            }
        }
        else {
            if (!isLadderViewAggregator() && this.tiers != null) {
                for (Double tier : this.tiers) {
                    sb.append(tier).append(',');
                }
            }
        }
        sb.append('|');
        sb.append(this.subscriptionSize).append('|').append(getMinTradeSize());
        sb.append('|');
        if(this.subscriptionClientTag != null){
            sb.append( this.subscriptionClientTag );
        }
        sb.append('|');
        sb.append(fullAmount);
        sb.append(']');
        return sb.toString();
    }

    public AggregationProcessorRequest build(AggregationRequest request) {
        setRequestType(request.getRequestType());
        setAggregator( request.getAggregationMethod() == null ? null : AggregationType.getAggregationType( request.getAggregationMethod() ) );
        setOrganization( request.getOrganization() );
        setUser( request.getUser() );
        setCcyPair( request.getCcyPair() );
        setDltCcy( request.getDealtCcy() );
        setLegalEntity( request.getLegalEntity() );
        setPriceProviders( request.getPriceProviders() );
        setSubscriptionSize( request.getSubscriptionSize() );
        setPriceHandler( request.getPriceHandler() );
        setRequestId( request.getRequestId() );
        setTiers( createTiers( request ) );
        setMinQty( request.getMinQty() );
        setZeroSizePricesEnabled( request.isZeroSizePricesEnabled() );
        setFullAmount(request.isFullAmount());
        setDropFXIDirectStreamPrices( request.dropFXIDirectStreamPrices() );
        setTierBasedSpreadEnabled( request.isTierBasedSpreadEnabled() );
        setSubscriptionClientTag( request.getSubscriptionClientTag() );
        setTierBasedSpread( request.getTierBasedSpread() );
        setAggregationInterval(request.getAggregationCheckInterval(), request.getAggregationPublishInterval());
        return build();
    }
    private void setAggregationInterval(Long checkInt, Long pubInt){
        if(checkInt != null && pubInt != null){
            setBestBifOfferPublicationInterval(checkInt);
            setQuotePublicationInterval(pubInt);
            setUseAggregationIntervalFromRequest(true);
        }
    }

    public void setTierBasedSpread(TierBasedSpread tierBasedSpread){
        this.tierBasedSpread = tierBasedSpread;
    }

    private List<Double> createTiers(AggregationRequest request) {
        if (request.getAggregationMethod() == AggregationMethod.FB_MULTI_PRICE_TIERS
                || request.getAggregationMethod() == AggregationMethod.FB_WEIGHTED_AVERAGE) {
            List<Double> tiers = request.getViewTiers();
            List<Double> newTiers = new ArrayList<Double>();
            Double lastTier = 0.0;

            for (Double tier : tiers) {
                newTiers.add(lastTier+=tier);

            }
            return newTiers;
        }
        return request.getViewTiers();
    }
    
    public Double getMinQty() {
		return minQty;
	}

    protected void validate()throws IdcInvalidArgumentException {
        // todo validate all the required fields only for SUBSCRIBE.
        validateField(priceHandler, "PriceHandler is not defined for broker LE " + this.legalEntity);
        validateField(ccyPair, "CurrencyPair is not defined for broker LE " + this.legalEntity);
        // todo: validate request id. If request type is not SUBSCRIBE, request id is compulsory.
    }

    protected Product createNewProduct() {
        Product product = ModelFactory.getInstance().newProduct();
        Configuration configuration = createConfiguration();
        long productID = this.productID.getAndIncrement();
        product.setObjectID(productID);
        product.setCurrencyPair(this.ccyPair);
        product.setConfiguration(configuration);
        product.setSubscriptionSize(this.subscriptionSize);
        product.setStreamCategoryFlags(getApplicableStreamCategories());
        product.setESP(true);
        product.setActive(true);
        product.setPriceMakingActive(true);
        product.setOrderExecutionActive(true);
        product.setMinTradeSize(getMinTradeSize());
        product.setMinQuoteSize(getMinQuoteSize());
        product.setZeroSizePricesEnabled(isZeroSizePricesEnabled());
// to be removed
       // product.setDropFXIDirectStreamPrices(dropFXIDirectStreamPrices());
        product.setAggExecutionRuleEnabled(AggregationServiceFactory.getInstance().getAggregationMBean().isAggExecRuleEnabled(this.organization.getShortName()));
        if (log.isDebugEnabled()) log.debug("SubscriptionRequestBuilder1::createProduct() product: " + product);
        return product;
    }

    private int getApplicableStreamCategories() {
        int streamCategoryFlags = 0;
        ISMBean ismBean = ISFactory.getInstance().getISMBean();
        Set<StreamCategory> streamCategorySet = new HashSet<StreamCategory>();
        if(AggregationServiceUtil.isStreamCategoryProviderPriorityEnabled(organization, ccyPair)) {
            if(tiers != null && !tiers.isEmpty()) {
                // filters out categories other than the ones defined for various tiers.
                // We get to work with a smaller dataset of prices.
                for(Double tier : tiers) {
                    List<StreamCategory> strmCategories = ismBean.getStreamCategoriesForOrg(organization.getShortName(),
                            ccyPair.getName(),
                            tier);
                    if(strmCategories != null && !strmCategories.isEmpty()) streamCategorySet.addAll(strmCategories);
                }
            } else {
                List<StreamCategory> strmCategories = ismBean.getStreamCategoriesForOrg(organization.getShortName(),
                        ccyPair.getName(),
                        subscriptionSize); // this should include V and X by default.
                if(strmCategories != null && !strmCategories.isEmpty()) streamCategorySet.addAll(strmCategories);
            }
        }
        if(!streamCategorySet.isEmpty()) {
            StringBuilder sb = new StringBuilder(100).append("ARB.getApplicableStreamCategories: ");
            for(StreamCategory category : streamCategorySet) {
                streamCategoryFlags |= category.getStreamCategoryFlag();
                sb.append(category.getDisplayValue()).append(' ');
            }
            streamCategoryFlags |= StreamCategory.ENABLE_DISABLE_FLAG;
            log.info(sb.toString());
        }
        return streamCategoryFlags;
    }

    protected Configuration createConfiguration() {
        Configuration configuration = ModelFactory.getInstance().newConfiguration();
        CurrencyPairGroup ccyPairGroup = createCurrencyPairGroup();
        List<Organization> orderProviders = Collections.emptyList(); // note matching is done elsewhere so OP irrelevant
        String orgName = this.organization.getShortName();
        String configName = new StringBuilder(30).append(orgName).append("Config").toString();
        configuration.setShortName(configName);
        configuration.setActive(true);
        updateConfigurationParameters(configuration);        
        configuration.setPriceProviders(this.priceProviders);
        configuration.setOrderProviders(orderProviders);

        // note dynamic ladders will ignore the aggregator set here and use it's own local aggregator
        if (this.aggregator != null) {
            configuration.setQuoteAggregator(this.aggregator.getAggregator());
        }
        if (tiers != null && tiers.size() > 0) {
            if(isTierBasedSpreadEnabled){
                setTiersWithSpread( configuration );
            }else{
                setTiers(configuration);
            }
        }
        configuration.setCurrencyPairGroup(ccyPairGroup);
        configuration.setStream(createStream());
        return configuration;
    }
    
    protected void updateConfigurationParameters(final Configuration configuration) { 
        configuration.setESPEnabled(true);
        configuration.setSpreadMinimumEnabled(false);
    }

    private void setTiers(Configuration configuration) {
        List<Tier> tiers = new ArrayList<Tier>(this.tiers.size());
        int i = 0;
        boolean isStreamCategoryEnabled = AggregationServiceUtil.isStreamCategoryProviderPriorityEnabled(organization, ccyPair);
        for (Double tierLimit : this.tiers) {
            Tier tier = ModelFactory.getInstance().newTier();
            tier.setBidLimit(tierLimit);
            tier.setOfferLimit(tierLimit);
            if(isStreamCategoryEnabled) setTierBasedStreamCategory(tier, tierLimit);
            tier.setSortOrder(i++);
            tiers.add(tier);
        }
        configuration.setTiers(tiers);
    }

    private void setTiersWithSpread(Configuration configuration){
        List<Tier> tiers = new ArrayList<Tier>(this.tiers.size());
        int i = 0;
        boolean isStreamCategoryEnabled = AggregationServiceUtil.isStreamCategoryProviderPriorityEnabled(organization, ccyPair);
        for (Double tierLimit : this.tiers) {
            Tier tier = ModelFactory.getInstance().newTier();
            tier.setBidLimit(tierLimit);
            tier.setOfferLimit( tierLimit );
            if(isStreamCategoryEnabled) setTierBasedStreamCategory(tier, tierLimit);
            tier.setSortOrder( i++ );
            TierBasedSpread.TierAndSpread tierAndSpread = tierBasedSpread.getSpread( tierLimit );
            if(tierAndSpread != null){
                tier.setSpreadFixedBid( tierAndSpread.getSpread() );
                tier.setSpreadFixedOffer( tierAndSpread.getSpread() );
            }
            tiers.add( tier );
        }
        configuration.setTiers(tiers);
        Spread spread = new SpreadC();
        spread.setType( SpreadType.BPS );
        configuration.setESPSpread( spread);
    }

    private void setTierBasedStreamCategory(Tier tier, Double tierLimit) {
        ISMBean ismBean = ISFactory.getInstance().getISMBean();
        int streamCategoryFlags = 0;
        List<StreamCategory> strmCategories = ismBean.getStreamCategoriesForOrg(organization.getShortName(), ccyPair.getName(), tierLimit);
        StringBuilder sb = new StringBuilder(100).append("ARB.setTierBasedStreamCategory: ").append(tierLimit).append(' ');
        for(StreamCategory category : strmCategories) {
            streamCategoryFlags |= category.getStreamCategoryFlag();
            sb.append(category.getDisplayValue()).append(' ');
        }
        streamCategoryFlags |= StreamCategory.ENABLE_DISABLE_FLAG;
        tier.setTierStreamCategoryFlags(streamCategoryFlags);
        log.info(sb.toString());
    }

    protected Stream createStream() {
        Stream stream = ModelFactory.getInstance().newStream();
        String orgName = this.organization.getShortName();
        String streamName = new StringBuilder(30).append(orgName).append("Stream").toString();
        stream.setShortName(streamName);
        stream.setESPEnabled(true);
        stream.setActive(true);
        stream.setStreamEnabled(true);
        AggregationServiceMBean aggregationServiceMBean = getAggregationServiceMBean();
        if( aggregationServiceMBean.isAggExecRuleEnabled(this.organization.getShortName())) {
            AggregationParameters aggParams = this.organization.getTakerOrganizationFunction().getAggregationParameters();
            if(!useAggregationIntervalFromRequest){
                stream.setQuotePublicationInterval(aggParams.getFullStreamUpdateInterval());
                stream.setQuotePublicationCheckInterval(aggParams.getBestBidOfferStreamUpdateInterval());
            }else {
                stream.setQuotePublicationInterval(this.quotePublicationInterval);
                stream.setQuotePublicationCheckInterval(this.bestBifOfferPublicationInterval);
            }
            TakerOrganizationFunction takerOrganizationFunction = this.processor.getTakerOrganizationFunction();
            takerOrganizationFunction.setStream(stream);
            stream.setTakerOrganizationFunction(takerOrganizationFunction);
        } else {
            stream.setQuotePublicationInterval(this.quotePublicationInterval);
            stream.setQuotePublicationCheckInterval(this.bestBifOfferPublicationInterval);
            BrokerOrganizationFunction brkrOrgFunction = this.processor.getBrokerOrganizationFunction();
            brkrOrgFunction.setDefaultStream(stream);
            stream.setBrokerOrganizationFunction(brkrOrgFunction);
        }
        stream.setUser(this.config.getUser() == null ? this.organization.getDefaultDealingUser() : this.config.getUser());
        stream.setFXIDirectStream(fullAmount);
        return stream;
    }
    
    protected AggregationServiceMBean getAggregationServiceMBean() {
    	return AggregationServiceFactory.getInstance().getAggregationMBean();
    }

    private CurrencyPairGroup createCurrencyPairGroup() {
        CurrencyPairGroup ccyPairGroup = CurrencyFactory.newCurrencyPairGroup();
        FXRateConvention rateConvention = QuoteConventionUtilC.getInstance().getFXRateConvention(this.organization);
        ccyPairGroup.setFXRateConvention(rateConvention);
        return ccyPairGroup;
    }

    private void linkStream(BrokerOrganizationFunction brokerOrganizationFunction, Stream stream) {
        brokerOrganizationFunction.setDefaultStream(stream);
        stream.setBrokerOrganizationFunction(brokerOrganizationFunction);
    }

    protected double getMinTradeSize() {
        Double minTradeSize = ccyPair == null ? null : this.config.getMinTradeSize(ccyPair);
        return minTradeSize == null ? 0.0 : minTradeSize;
    }

    protected double getMinQuoteSize() {
        Double minQuoteSize = ccyPair == null ? null : this.config.getMinQuoteSize(ccyPair);
        return minQuoteSize == null ? 0.0 : minQuoteSize;
    }

    public boolean isZeroSizePricesEnabled(){
        return isZeroSizePricesEnabled;
    }

    public boolean dropFXIDirectStreamPrices() {
        return dropFXIDirectStreamPrices;
    }
}
