package com.integral.aggregation.subscription;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.integral.aggregation.subscription.provider.quote.ProviderQuoteHandler;
import com.integral.user.Organization;

/**
 *
 */
public class RFSAggregationProcessorRequestC extends AggregationProcessorRequestC implements RFSAggregationProcessorRequest{
	
	// Not using any of the concurrent implementation as iteration operations are not thread safe 
	// So underlying collection view may be undefined if map is being updated from other thread
	// So using explicit locking just because of getting consistent view of underlying collection
	//private Map<Organization, ProviderQuoteHandler> providerQuoteHandlerMap = 
		//											new HashMap<Organization, ProviderQuoteHandler>();
	private ProviderQuoteHandler providerQuoteHandler;
	private Collection<Organization> providers = new ArrayList<Organization>();

    public RFSAggregationProcessorRequestC() {
    }
 
	@Override
	public AggregationRequest getAggregationRequest() {		
		return super.getAggregationRequest();
	}

	@Override
	public void setAggregationRequest(AggregationRequest aggregationRequest) {		
		super.setAggregationRequest(aggregationRequest);
	}


	@Override
	public void addProviderQuoteHandler(final Organization provider, final ProviderQuoteHandler providerQuoteHandler) {
		/*
		synchronized (providerQuoteHandlerMap) {
			providerQuoteHandlerMap.put(provider, providerQuoteHandler);
		}
		*/
		this.providerQuoteHandler = providerQuoteHandler;
	}


	@Override
	public ProviderQuoteHandler getProviderQuoteHandler(final Organization provider) {	
		/*
		synchronized (providerQuoteHandlerMap) {
			return providerQuoteHandlerMap.get(provider);
		}
		*/
		return this.providerQuoteHandler;
	}


	@Override
	public void removeProviderQuoteHandler(final Organization provider) {
		/*
		synchronized (providerQuoteHandlerMap) {
			providerQuoteHandlerMap.remove(provider);
		}
		*/
		this.providerQuoteHandler = null;
		
	}


	@Override
	public Collection<ProviderQuoteHandler> getAllProviderQuoteHandlers() {
		/*
		synchronized (providerQuoteHandlerMap) {
			return new ArrayList<ProviderQuoteHandler>(providerQuoteHandlerMap.values());
		}
		*/
		if (this.providerQuoteHandler == null) {
			return Collections.emptyList();
		}
		List<ProviderQuoteHandler> providerQuoteHandlers = new ArrayList<ProviderQuoteHandler>(1);
		providerQuoteHandlers.add(this.providerQuoteHandler);
		return providerQuoteHandlers;
	}


	@Override
	public Map<Organization, ProviderQuoteHandler> getAllProviderQuoteHandlersMap() {
		throw new UnsupportedOperationException();
	}


	@Override
	public void addProviders(Collection<Organization> providers) {
		this.providers.clear();
		this.providers.addAll(providers);		
	}


	@Override
	public Set<Organization> getAllProviders() {		
		return new HashSet<Organization>(this.providers);
	}


	@Override
	public String getAggregatorId() {		
		return getRequestId();
	}


	
	

}
