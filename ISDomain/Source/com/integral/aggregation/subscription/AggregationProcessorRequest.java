package com.integral.aggregation.subscription;

import com.integral.aggregation.model.TierBasedSpread;
import com.integral.aggregation.stream.view.LadderViewRequest;
import com.integral.broker.model.Product;
import com.integral.aggregation.AggregationResponseHandler;
import com.integral.aggregation.Response;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.finance.currency.Currency;
import com.integral.user.User;


/**
 *
 */
public interface AggregationProcessorRequest extends Request {

    public String getStreamId();

    public Product getProduct();
    
    AggregationRequest getAggregationRequest();

    AggregationResponseHandler<Response, FXPriceBook> getPriceHandler();

    void setProduct(Product product);
    
    void setAggregationRequest(AggregationRequest aggregationRequest);

    void setPriceHandler(AggregationResponseHandler<Response, FXPriceBook> priceHandler);

    LadderViewRequest getViewRequest();

    void setViewRequest(LadderViewRequest viewRequest);

    String getAggregatorId();

    void setAggregatorId(String productId);

    User getUser();

    void setUser(User user);

    Currency getDltCcy();

    void setDltCcy(Currency dltCcy);

    Double getMinQty();

    void setMinQty(Double minQty);

    boolean isZeroSizePricesEnabled();

    void setZeroSizePricesEnabled(boolean isZeroSizePricesEnabled);

    boolean dropFXIDirectStreamPrices();

    void setDropFXIDirectStreamPrices(boolean dropFXIDirectStreamPrices);

    public TierBasedSpread getTierBasedSpread();

    public void setTierBasedSpread( TierBasedSpread tierBasedSpread );

    public boolean isClientTagSubscription();

    public void setClientTagSubscription(boolean clientTagSubscription);

    public void setSubscriptionClientTag(String clientTag);

    public String getSubscriptionClientTag();
}
