package com.integral.aggregation.subscription;

import com.integral.aggregation.subscription.provider.quote.ProviderQuoteHandler;
import com.integral.finance.currency.Currency;
import com.integral.finance.trade.Tenor;
import com.integral.time.IdcDate;

/**
 *
 */
public interface RFSAggregationRequest extends AggregationRequest {

    int getExpiryInterval();

    void setExpiryInterval(int expiryInterval);

    String getTradeClassification();

    void setTradeClassification(String tradeClassification);

    Currency getDealtCurrency();

    void setDealtCurrency(Currency dealtCurrency);

    boolean isSwap();

    void setSwap(boolean swap);

    double getDealtAmt();

    void setDealtAmt(double dealtAmt);

    Tenor getTenor();

    void setTenor(Tenor tenor);

    IdcDate getValueDate();

    void setValueDate(IdcDate valueDate);

    double getFarLegDealtAmt();

    void setFarLegDealtAmt(double farLegDealtAmt);

    Tenor getFarLegTenor();

    void setFarLegTenor(Tenor farLegTenor);

    IdcDate getFarLegValueDate();

    void setFarLegValueDate(IdcDate farLegValueDate);

    int getBidOfferMode();

    void setBidOfferMode(int bidOfferMode);

    int getFarBidOfferMode();

    void setFarBidOfferMode(int farBidOfferMode);
    
    IdcDate getFixingDate();
    
    IdcDate getFarLegFixingDate();
    
    void setFixingDate(IdcDate fixingDate);
    
    void setFarLegFixingDate(IdcDate farLegFixingDate);
    
    void setProviderQuoteHandler(ProviderQuoteHandler providerQuoteHandler);
    
    ProviderQuoteHandler getProviderQuoteHandler();
    
    void setRFSAggregationProcessorRequest(RFSAggregationProcessorRequest aggregationProcessorRequest);
    
    RFSAggregationProcessorRequest getRFSAggregationProcessorRequest();
}
