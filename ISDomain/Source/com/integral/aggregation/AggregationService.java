package com.integral.aggregation;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.FutureTask;

import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.aggregation.rfs.RFSAggregationProcessorManager;
import com.integral.aggregation.subscription.AggregationProcessorRequest;
import com.integral.aggregation.subscription.AggregationRequest;
import com.integral.aggregation.subscription.AggregationRequestType;
import com.integral.aggregation.subscription.RFSAggregationRequest;
import com.integral.aggregation.validator.ESPAggregationRequestValidator;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.Organization;

/**
 * AggregationService is a singleton class responsible for aggregating the provider prices for the customer. As part of
 * the aggregation request user passes all the required parameters and {@link AggregationResponseHandler} to
 * the service. On successful subscription, user will start receiving the aggregated view on his response handler.
 * <p>
 * This class supports the asynchronous and synchronous subscribe/unsubscribe of aggregation requests.
 * <p>
 * To execute the synchronous subscription request, user needs to follow these steps -
 * <p>
 * <pre>
 * {@code
 *      // Create the Aggregation response handler.
 *      AggregationResponseHandler<Response, FXPriceBook> handler = new AggregationResponseHandler<Response, FXPriceBook>() {
 *           public void onCompleted(Response response) {
 *              //To change body of implemented methods use File | Settings | File Templates.
 *          }
 *
 *          public void onPriceUpdate(FXPriceBook rate) {
 *              //To change body of implemented methods use File | Settings | File Templates.
 *          }
 *      };
 *
 *      AggregationService service = AggregationService.getInstance();
 *      AggregationRequest request = new AggregationRequestC()
 *              .setOrganization(customerOrg)
 *              .setLegalEntity(customerLegalEntity)
 *              .setUser(fiUser)
 *              .setPriceHandler(handler)
 *              .setCcyPair(convention.getFXRateBasis("EUR/USD").getCurrencyPair())
 *              .setPriceProviders(priceProviders)
 *              .setSubscriptionSize(20000)
 *              .setAggregator(AggregationType.MultiTierAverageQuoteAggregation)
 *              .addViewTier(i * 1000000.00).addViewTier(i * 2000000.00).addViewTier(i * 3000000.00);
 *
 *      Response response = service.subscribe(request)
 * }
 * </pre>
 * <p>
 * The above code will block the subscription thread, until the response is fully received.  The {@link AggregationResponseHandler#onPriceUpdate(V) }
 * will start receiving the aggregated price books on the successful subscription.<p>
 *
 * <p>To execute the asynchronous request, user need to do<p>
 *  <pre>
 * {@code
 *
 *      AggregationService service = AggregationService.getInstance();
 *
 *      Future<Response> asyncResponse = service.subscribeAsync(request);
 *
 *      if (asyncResponse.isDone()) {
 *          Response response = asynchronous.get();
 *      }
 * }
 * </pre>
 * <p>
 * The above code will return the Future<Response> immediately and when the subscription response is ready, {@link AggregationResponseHandler#onCompleted(T) }
 * will be invoked or user can also read the response from Future<Response>.  <p>
 *
 */
public class AggregationService {
    protected Log log = LogFactory.getLog(this.getClass());
    private static AggregationService instance;

    private AggregationService() {
    	log.info(" AGGREGATION_SERVICE_CREATED");
    }

    public static AggregationService getInstance() {
        if (instance == null) {
            instance = new AggregationService();
        }
        return instance;
    }

    /**
     * Subscribes asynchronously to aggregation service. AggregationResponseHandler will be invoked once
     * the response is ready. User can even call the get() of Future<Response> to get the response.
     *
     * @param request
     * @return
     */
    public Future<Response> subscribeAsync(final AggregationRequest request) {
        FutureTask<Response> requestTask = new FutureTask<Response>(new AggregationRequestTask(request, AggregationRequestType.SUBSCRIBE));
        AggregationServiceFactory.getInstance().getRequestExecutor().execute(requestTask);
        return requestTask;
    }

    /**
     * Subscribes synchronously to aggregation service. User request will be in hold until the response is ready.
     *
     * @param request
     * @return
     */
    
    
    private boolean isRFS(final AggregationRequest request) {
    	return request instanceof RFSAggregationRequest;
    }
    
    public Response subscribe(final AggregationRequest request) {
    	
    	if (log.isInfoEnabled()) {
            StringBuilder sb = new StringBuilder(100).append(' ').append("AGGREGATION_SERVICE_SUBSCRIBE").append(" REQUEST_ID=").append(request.getRequestId());                    
            log.info(sb.toString());
    	}
    	
        Response response = request.createResponse();
        try {
            request.setRequestType(AggregationRequestType.SUBSCRIBE);
            if (!isRFS(request)) {                   
            	response = ESPAggregationRequestValidator.validateSubscriptionRequest(request);
            }
            
            if (response.isFailed()) {
            	log.info("VALIDATION_FAILED_FOR_AGG_REQUEST ID=" + request.getRequestId() + " MSG=" + response.getMsg());            	
                return response;
            }

            if (log.isInfoEnabled()) {
                StringBuilder sb = new StringBuilder(150)
                    .append("AGGREGATION_SERVICE_SUBSCRIBE")
                    .append(" REQUEST_ID=").append(request.getRequestId())
                    .append(" ORG=").append(request.getOrganization() == null ? "" : request.getOrganization().getShortName())
                    .append(" LEGAL_ENTITY=").append(request.getLegalEntity() == null ? null : request.getLegalEntity().getShortName())
                    .append(" USER=").append(request.getUser() == null ? "" : request.getUser().getShortName())
                    .append(" CCY_PAIR=").append(request.getCcyPair() == null ? "" : request.getCcyPair().getName())
                    .append(" DESCRIPTION=").append(request.getAggregationMethod().getDescription())
                    .append(" VIEW_TIERS=").append(request.getViewTiers())
                    .append(" SUBSCRIPTION_SIZE=").append(request.getSubscriptionSize())
                    .append(" MIN_QTY=").append(request.getMinQty())
                    .append(" PROVIDERS=");

                    for (Organization provider : request.getPriceProviders()) {
                        sb.append(provider.getShortName()).append(',');
                    }
                     sb.append(' ').append(request.getAggregationMethod() == null ? "" : request.getAggregationMethod().getDescription())
                    .append(' ').append(request.getViewTiers())
                    .append(' ').append(request.getSubscriptionSize())
                    .append(' ').append(request.getMinQty());

                log.info(sb.toString());
            }

            AggregationProcessor processor = getAggregationProcessor(request);
            AggregationProcessorRequest aggregatorRequest = processor.getRequestBuilder().build(request);
            response = processor.subscribe(aggregatorRequest);
            
        	if (log.isInfoEnabled()) {
                StringBuilder sb = new StringBuilder(100).append(' ').append("AGGREGATION_SERVICE_SUBSCRIBE_SUCCESS").append(" REQUEST_ID=").append(request.getRequestId());                    
                log.info(sb.toString());
        	}
        	
        } catch (Exception exc) {
            StringBuilder sb = new StringBuilder(200)
                    .append("AGGREGATION_SERVICE_SUBSCRIBE_EXCEPTION")
                    .append(" REQUEST_ID=").append(request.getRequestId());
            log.error(sb.toString(), exc);
            response.setStatus(Response.STATUS_FAIL);            
            response.setMsg(exc.getMessage());
        }

        return response;
    }

    /**
     * Un-subscribes asynchronously to aggregation service. AggregationResponseHandler will be invoked once
     * the response is ready. User can even call the get() of Future<Response> to get the response.
     *
     * @param request
     * @return
     */
    public Future<Response> unSubscribeAsync(final AggregationRequest request) {
        FutureTask<Response> requestTask = new FutureTask<Response>(new AggregationRequestTask(request, AggregationRequestType.UN_SUBSCRIBE));
        AggregationServiceFactory.getInstance().getRequestExecutor().execute(requestTask);

        return requestTask;
    }

    /**
     * Un-subscribes synchronously to aggregation service. User request will be in hold until the response is ready.
     *
     * @param request
     * @return
     */
    public Response unSubscribe(final AggregationRequest request) {
        Response response = request.createResponse();
        
        if (log.isInfoEnabled()) {
            StringBuilder sb = new StringBuilder(100).append(' ').append("AGGREGATION_SERVICE_UNSUBSCRIBE").append(" REQUEST_ID=").append(request.getRequestId());                    
            log.info(sb.toString());
    	}

        try {
            request.setRequestType(AggregationRequestType.UN_SUBSCRIBE);
            boolean isRFS = isRFS(request);
            if (!isRFS) {
            	response = ESPAggregationRequestValidator.validateUnsubscriptionRequest(request);
            }
            
            if (response.isFailed()) {
            	log.info("VALIDATION_FAILED_FOR_AGG_REQUEST ID=" + request.getRequestId() + " MSG=" + response.getMsg());
                return response;
            }

            if (log.isInfoEnabled()) {
                StringBuilder sb = new StringBuilder(150)
                    .append("AGGREGATION_SERVICE_UNSUBSCRIBE")
                    .append(" REQUEST_ID").append(request.getRequestId())
                    .append(" ORG=").append(request.getOrganization() == null ? "" : request.getOrganization().getShortName())
                    .append(" LEGAL_ENTITY=").append(request.getLegalEntity() == null ? null : request.getLegalEntity().getShortName())
                    .append(" USER=").append(request.getUser() == null ? "" : request.getUser().getShortName())
                    .append(" CCY_PAIR=").append(request.getCcyPair() == null ? "" : request.getCcyPair().getName())
                    .append(" DESCRIPTION=").append(request.getAggregationMethod().getDescription())
                    .append(" VIEW_TIERS=").append(request.getViewTiers())
                    .append(" SUBSCRIPTION_SIZE=").append(request.getSubscriptionSize())
                    .append(" MIN_QTY=").append(request.getMinQty())
                    .append(" PROVIDERS=");

                    for (Organization provider : request.getPriceProviders()) {
                        sb.append(provider.getShortName()).append(',');
                    }
                     sb.append(' ').append(request.getAggregationMethod() == null ? "" : request.getAggregationMethod().getDescription())
                    .append(' ').append(request.getViewTiers())
                    .append(' ').append(request.getSubscriptionSize())
                    .append(' ').append(request.getMinQty());

                log.info(sb.toString());
            }

            AggregationProcessor processor = getAggregationProcessor(request);
            AggregationProcessorRequest aggregatorRequest = processor.getRequestBuilder().build(request);
            response = processor.unSubscribe(aggregatorRequest);

            if (!processor.isSubscriptionAvailable()) {
                removeAggregationProcessor(request);
            }

        } catch (Exception exc) {
        	StringBuilder sb = new StringBuilder(200)
            .append("AGGREGATION_SERVICE_UNSUBSCRIBE_EXCEPTION")
            .append(" REQUEST_ID=").append(request.getRequestId());
        	log.error(sb.toString(), exc);
            response.setStatus(Response.STATUS_FAIL);
            response.setMsg(exc.getMessage());
        }

        return response;
    }
    
     
  
    public AggregationProcessor getAggregationProcessor(Organization fiOrg) {
        return AggregationProcessorManager.getInstance().getOrCreateAggregationProcessor(fiOrg);
    }
    
    
    /**
     * 
     * @param request
     * @return
     */
    
    public AggregationProcessor getAggregationProcessor(AggregationRequest request) {   
    	boolean isRFS = (request instanceof RFSAggregationRequest);
    	Organization org = request.getOrganization();
    	if (isRFS) {
    		return RFSAggregationProcessorManager.getInstance().getOrCreateAggregationProcessor(org);
    	} else {
    		return AggregationProcessorManager.getInstance().getOrCreateAggregationProcessor(org);
    	}
        
    }

    private void removeAggregationProcessor(AggregationRequest request) {
    	boolean isRFS = (request instanceof RFSAggregationRequest);
    	Organization org = request.getOrganization();
    	if (isRFS) {
    		RFSAggregationProcessorManager.getInstance().removeAggregatorProcessor(org);
    	} else {
    		AggregationProcessorManager.getInstance().removeAggregatorProcessor(org);
    	}
        
    }

    private class AggregationRequestTask implements Callable<Response> {
        AggregationRequest request;
        AggregationResponseHandler<Response, FXPriceBook> responseHandler;
        AggregationRequestType type;

        private AggregationRequestTask(AggregationRequest request, AggregationRequestType requestType) {
            this.request = request;
            this.type = requestType;
            this.responseHandler = this.request.getPriceHandler();
        }

        public Response call() throws Exception {
            Response response = null;
            if (type == AggregationRequestType.SUBSCRIBE) {
                response = subscribe(request);
            } else if (type == AggregationRequestType.UN_SUBSCRIBE) {
                response = unSubscribe(request);
            }

            if (this.responseHandler != null) {
                this.responseHandler.onCompleted(response);
            }

            return response;
        }
    }
}
