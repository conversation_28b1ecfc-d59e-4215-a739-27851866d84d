package com.integral.aggregation;

/**
 *
 */
public class ResponseC implements Response {
    protected int status = STATUS_OK;
    protected String requestId;
    protected String msg;

    public ResponseC() {
    }

    public ResponseC(String requestId) {
        this.requestId = requestId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getRequestId() {
        return requestId;
    }
    
    public boolean isFailed() {
    	return status == STATUS_FAIL;
    }
}
