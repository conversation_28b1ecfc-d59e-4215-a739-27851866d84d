package com.integral.aggregation.metrics.reader;




import java.io.BufferedReader;
import java.io.DataInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.zip.GZIPInputStream;

public class AggMetricsReader {

	private static Set<String> uniqueAggKeys = new HashSet<String>();

	private DecimalFormat decimalFormat = new DecimalFormat("#.###");

	private Map<String, Double[]> uniqueAggKeyVsRejectedAggDataMap = new HashMap<String, Double[]>();
	private Map<String, Double[]> uniqueAggKeyVsPerformedAggDataMap = new HashMap<String, Double[]>();
	private Map<String, Double[]> uniqueAggKeyVsAggDataMap = new HashMap<String, Double[]>();
	private Map<String, Double[]> uniqueAggKeyVsPubDataMap = new HashMap<String, Double[]>();
	private Map<String, Double[]> uniqueAggKeyVsLogDataMap = new HashMap<String, Double[]>();

	private static Map<String, Integer> uniqueAggKeyVsNumberofExceptions = new HashMap<String, Integer>();



	private String parseLineAndReturnData(String line) {
		int startIndex = line.indexOf("(ASM)-");
		String metricsData = line.substring(startIndex + 6);

		return metricsData;
	}

	private String[] splitMetricsDataIntoArray(String metricsData) {
		String[] metricsDataArray = metricsData.split(",");
		return metricsDataArray;
	}

	private String getUniqueKeyFromMetricsDataArray(String[] metricsDataArray) {
		String aggType = metricsDataArray[0];
		String org = metricsDataArray[1];
		String streamName = metricsDataArray[2];
		String currency = metricsDataArray[3];
		String aggregatorName = metricsDataArray[4];
		String aggregatorID = metricsDataArray[5];

		String uniqueKey = aggType + "." + org + "." + streamName + "." + currency + "." + aggregatorName + "." + aggregatorID;
		return uniqueKey;
	}

	private String[] getDataFromMetricsArray(String[] metricsDataArray, int startingIndex) {
		String[] aggData = new String[4];
		aggData[0] = metricsDataArray[startingIndex];
		aggData[1] = metricsDataArray[startingIndex + 1];
		aggData[2] = metricsDataArray[startingIndex + 2];
		aggData[3] = metricsDataArray[startingIndex + 3];
		return aggData;
	}

	private void addExceptionData(String uniqueKey, String exceptioncount) {
		Integer data = uniqueAggKeyVsNumberofExceptions.get(uniqueKey);
		if(data == null) {
			data = 0;
		}
		uniqueAggKeyVsNumberofExceptions.put(uniqueKey, data + Integer.parseInt(exceptioncount));
	}

	private String formatDouble(double doubleValue) {
		return decimalFormat.format(doubleValue);
	}

	private void addDataAndUpdateMap(Map<String, Double[]> map, String uniqueKey, String[] dataToAdd) {
		Double[] data = map.get(uniqueKey);
		if(data == null) {
			data = new Double[4];
			data[0] = Double.parseDouble(dataToAdd[0]);
			data[1] = Double.parseDouble(dataToAdd[1]);
			data[2] = 0D;
			data[3] = Double.parseDouble(dataToAdd[3]);
		}
		else {
			data[0] += Double.parseDouble(dataToAdd[0]);
			data[1] += Double.parseDouble(dataToAdd[1]);

			if(Double.parseDouble(dataToAdd[3]) > data[3]) {
				data[3] = Double.parseDouble(dataToAdd[3]);
			}
		}
		map.put(uniqueKey, data);
	}


	private void updateMetrics(String line) {

		String metricsData = parseLineAndReturnData(line);
		String[] metricsDataArray = splitMetricsDataIntoArray(metricsData);
		String uniqueKey = getUniqueKeyFromMetricsDataArray(metricsDataArray);

		String exceptionCount = metricsDataArray[6];

		String[] rejectedAggData = getDataFromMetricsArray(metricsDataArray, 7);
		String[] performedAggData = getDataFromMetricsArray(metricsDataArray, 11);
		String[] aggData = getDataFromMetricsArray(metricsDataArray, 15);
		String[] publishData = getDataFromMetricsArray(metricsDataArray, 19);
		String[] logData = getDataFromMetricsArray(metricsDataArray, 23);



		uniqueAggKeys.add(uniqueKey);

		addDataAndUpdateMap(uniqueAggKeyVsRejectedAggDataMap, uniqueKey, rejectedAggData);
		addDataAndUpdateMap(uniqueAggKeyVsPerformedAggDataMap, uniqueKey, performedAggData);
		addDataAndUpdateMap(uniqueAggKeyVsAggDataMap, uniqueKey, aggData);
		addDataAndUpdateMap(uniqueAggKeyVsPubDataMap, uniqueKey, publishData);
		addDataAndUpdateMap(uniqueAggKeyVsLogDataMap, uniqueKey, logData);

		addExceptionData(uniqueKey, exceptionCount);
	}


	private  void printData(String string, Double[] data) {

		double average = 0.0D;
		if(data[0] > 0.0) {
			average = (data[1]) / (data[0]);
		}
		data[2] = average;

		data = formatDoubleValues(data);

		/* System.out.println(eventName + " OCCURANCES="  
				+ formatDouble(data[0]) + " TOTAL_TIME(micros)=" 
				+ formatDouble(data[1])  + " AVG_TIME(micros)=" 
				+ formatDouble(average) + " MAX_TIME(micros)=" 
				+ formatDouble(data[3]));*/

		String[] strArrayToPrintDetails = new String[data.length + 1];
		strArrayToPrintDetails[0] = string;
		strArrayToPrintDetails[1] = Integer.toString(data[0].intValue());

		int arrayIndex = 0;
		for(Double doubleData : data) {
			/* the array index 0 is the number of occurances, we need to present it as an integer and not floating point  */
			String strData = null;
			if(arrayIndex == 0) {
				strData = Integer.toString(data[0].intValue());        
			} else {
				strData = Double.toString(doubleData);
			}
			strArrayToPrintDetails[arrayIndex + 1] = strData; 
			arrayIndex++;
		}

		System.out.format("%30s%20s%20s%20s%20s\n", (Object[])strArrayToPrintDetails);		
	}

	private Double[] formatDoubleValues(Double[] data) {
		int i = 0;
		for(Double doubleData : data) {
			data[i] = Double.parseDouble(formatDouble(doubleData));
			i++;
		}
		return data;
	}

	private void printData() {
		for(String uniqueKey : uniqueAggKeys) {
			System.out.println();

			System.out.println(uniqueKey + " EXCEPTION_COUNT=" + uniqueAggKeyVsNumberofExceptions.get(uniqueKey));

			System.out.println();

			String[] strArrayToPrintDetails = new String[5];
			strArrayToPrintDetails[0] = "NAME";
			strArrayToPrintDetails[1] = "OCCURANCES";
			strArrayToPrintDetails[2] = "TOTAL_TIME(micros)";
			strArrayToPrintDetails[3] = "AVG_TIME(micros)";
			strArrayToPrintDetails[4] = "MAX_TIME(micros)";

			
			System.out.format("%30s%20s%20s%20s%20s\n", (Object[])strArrayToPrintDetails);


			Double[] rejectedAggData = uniqueAggKeyVsRejectedAggDataMap.get(uniqueKey);
			Double[] performedAggData = uniqueAggKeyVsPerformedAggDataMap.get(uniqueKey);
			Double[] aggData = uniqueAggKeyVsAggDataMap.get(uniqueKey);
			Double[] publishData = uniqueAggKeyVsPubDataMap.get(uniqueKey);
			Double[] logData = uniqueAggKeyVsLogDataMap.get(uniqueKey);

			// System.out.println("EXCEPTION_COUNT=" + uniqueAggKeyVsNumberofExceptions.get(uniqueKey));

			/*			printData("REJECTED_AGGEGRATION_STATS", rejectedAggData);
			printData("PERFORMED_AGGEGRATION_STATS", performedAggData);
			printData("AGGEREGATION_STATS", aggData);
			printData("PUBLISH_STATS", publishData);
			printData("LOG_STATS", logData);
			 */

			printData("REJECTED_AGGEGRATION", rejectedAggData);
			printData("PERFORMED_AGGEGRATION", performedAggData);
			printData("AGGEREGATION", aggData);
			printData("PUBLISH", publishData);
			printData("LOG", logData);


			System.out.println();


		}
	}

	private void readUnZippedFile(String fileName)  {
		boolean isFullFilePathSpecified = false;
		try {
			BufferedReader br = null;
			String sCurrentLine;
			

			File file = new File(fileName);
			int rollingIndex = 0;
			if (file.exists()) { 
				isFullFilePathSpecified = true;     				
				FileInputStream fin = new FileInputStream(file);
				DataInputStream dis = new DataInputStream(fin);
				InputStreamReader xover = new InputStreamReader(dis);		
				  br = new BufferedReader(xover);  				
			}
			
			
			while(br != null) {
				while ((sCurrentLine = br.readLine()) != null) {
					if(sCurrentLine.contains("ASM")) {
						updateMetrics(sCurrentLine);
					}
				}
				br.close();
				++rollingIndex;
				if(! isFullFilePathSpecified) {
					br = getUnZippedStream(fileName, ".log", rollingIndex);    
				} else {
					break;
				}
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			
		}
	}

	private void readZippedFile(String fileName)  {
		boolean isFullFilePathSpecified = false;
		try {
			BufferedReader br = null;
			String sCurrentLine;
			

			File file = new File(fileName);
			int rollingIndex = 0;
			if (file.exists()) { 
				isFullFilePathSpecified = true;     				
				FileInputStream fin = new FileInputStream(file);
				GZIPInputStream gzipStream = new GZIPInputStream(fin);
				InputStreamReader xover = new InputStreamReader(gzipStream);		
				  br = new BufferedReader(xover);  				
			}
			
			
			while(br != null) {
				while ((sCurrentLine = br.readLine()) != null) {
					if(sCurrentLine.contains("ASM")) {
						updateMetrics(sCurrentLine);
					}
				}
				br.close();
				++rollingIndex;
				if(! isFullFilePathSpecified) {
					br = getZippedStream(fileName, ".log", rollingIndex);    
				} else {
					break;
				}
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			
		}
	}
	
	
	private   BufferedReader getZippedStream(String filename, String extension, int rollingIndex) throws Exception { 
		File file = new File(filename + "." + rollingIndex + '.' + extension);
		FileInputStream fin = new FileInputStream(file);
		GZIPInputStream gzis = new GZIPInputStream(fin);
		InputStreamReader xover = new InputStreamReader(gzis);		
		BufferedReader br = new BufferedReader(xover);   		
		return br;
	}
	
	
	private   BufferedReader getUnZippedStream(String filename, String extension, int rollingIndex) throws Exception { 
				
		File file = new File(filename + "." + rollingIndex + '.' + extension);
		FileInputStream fin = new FileInputStream(file);
		DataInputStream gzis = new DataInputStream(fin);
		InputStreamReader xover = new InputStreamReader(gzis);		
		BufferedReader br = new BufferedReader(xover);   		
		return br;
	}

	private void readData(String fileName) {		 	
		if(fileName.endsWith(".gz")) {
			readZippedFile(fileName);
		} else {
			readUnZippedFile(fileName);
		}			
	}

	public static void main(String[] args) throws Exception {
		AggMetricsReader aggMetricsReader = new AggMetricsReader();

		if (args.length < 1)
		{
			System.out.println("Missing filename parameter.");
			System.out.println("Usage: AggMetricsReader [-debug] <FILE_NAME>");
			System.exit(0);
		}  

		String fileName = args[0];

		aggMetricsReader.readData(fileName);
		aggMetricsReader.printData();

	}

}


//  GZIPInputStream gzip = new GZIPInputStream(new FileInputStream("F:/gawiki-20090614

/*
FileInputStream fin = new FileInputStream(FILENAME);
    GZIPInputStream gzis = new GZIPInputStream(fin);
    InputStreamReader xover = new InputStreamReader(gzis);
    BufferedReader is = new BufferedReader(xover);
 */
