package com.integral.aggregation.metrics;

import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;

public class AggMetricsUtil {
	
	public static Log log = LogFactory.getLog("AGG_METRICS");
	
	public static enum METRICS_EVENT_NAME {
		PERFORMED_AGG, AGGREGATE, LOG, PUBLISH, REJECTED_AGG
	}
	
	public static enum AGG_TYPE {
		BROKER_AGG,AGG_SERVICE
	}
	
	public static final String AGG_METRICS_SHORT_NAME = "(ASM)";
	
	public static final String AGG_LOGGING_METRICS_SHORT_NAME = "(ASL)";
	
	public static final long metricsLogTimeInNanos;
	
	static {
		int timePeriodInMinsToLogAggServiceMetrics = AggregationServiceFactory.getInstance().getAggregationMBean().getTimePeriodInMinsToLogTheAggServiceMetrics();
		metricsLogTimeInNanos = timePeriodInMinsToLogAggServiceMetrics * 60L * 1000L * 1000L * 1000L;		
	}

}
