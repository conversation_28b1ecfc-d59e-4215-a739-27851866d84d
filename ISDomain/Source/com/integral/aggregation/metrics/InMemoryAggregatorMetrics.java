package com.integral.aggregation.metrics;

import com.integral.aggregation.AggregationServiceUtil.AGG_TYPE;
import com.integral.aggregation.metrics.AggMetricsUtil.METRICS_EVENT_NAME;
import com.integral.pipeline.metrics.Metrics;
import com.integral.pipeline.metrics.ServerStatistic;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * The whole class accessed inside a single aggregator thread, so no need to synchronization
 *
 * <AUTHOR>
 */
public class InMemoryAggregatorMetrics implements Metrics {

    private final AGG_TYPE aggType;
    private final ServerStatistic serverStatistics;
    private final Map<String, Long> subscriberCountMap = new ConcurrentHashMap<String, Long>();
    private String aggregatorName = null;
    private String orgName = null;
    private String streamName = null;
    private String currencyPair = null;
    private String aggregatorID = null;
    private Map<METRICS_EVENT_NAME, Long> eventVsTimeTakenMap = null;
    private Map<METRICS_EVENT_NAME, Long> eventVsMaxTimeMap = null;
    private Map<METRICS_EVENT_NAME, Integer> eventVsNumberOfOccurances = null;
    private int numberOfExceptions = 0;

    public InMemoryAggregatorMetrics(AGG_TYPE aggType, String aggregatorName, String orgName, String aggregatorID, String streamName, String currencyPair) {
        serverStatistics = ServerStatistic.getInstance();
        this.aggType = aggType;
        this.orgName = orgName;
        this.aggregatorName = aggregatorName;
        this.aggregatorID = aggregatorID;
        this.streamName = streamName;
        this.currencyPair = currencyPair;
        init();
    }

    public void addTimeTaken(METRICS_EVENT_NAME event, long timeTakenInNanos) {
        long timeTakenInMicros = timeTakenInNanos / 1000;

        Long existingTimeTaken = eventVsTimeTakenMap.get(event);
        Long totalTimeTaken = existingTimeTaken + timeTakenInMicros;
        eventVsTimeTakenMap.put(event, totalTimeTaken);

        long maxTimeForEvent = eventVsMaxTimeMap.get(event);
        if (timeTakenInMicros > maxTimeForEvent) {
            eventVsMaxTimeMap.put(event, timeTakenInMicros);
        }

        int numberOfOccurances = eventVsNumberOfOccurances.get(event);
        eventVsNumberOfOccurances.put(event, numberOfOccurances + 1);

        if (event == METRICS_EVENT_NAME.AGGREGATE) {
            if (aggType == AGG_TYPE.BROKER_AGGREGATION) {
                serverStatistics.incrementBAAggregation(orgName);
            } else {
                serverStatistics.totalOAAggregations++;
            }
        }
    }

    @Override
    public StringBuilder report() {
        StringBuilder sb = new StringBuilder();

        long avgTimeForRejectedAgg = 0L;
        long avgTimeForPeformedAgg = 0L;
        long avgTimeForActualAgg = 0L;
        long avgTimeForPublish = 0L;
        long avgTimeForLog = 0L;

        int occOfRejectedAgg = (eventVsNumberOfOccurances.get(METRICS_EVENT_NAME.REJECTED_AGG));
        int occOfPeformedAgg = (eventVsNumberOfOccurances.get(METRICS_EVENT_NAME.PERFORMED_AGG));
        int occOfAgg = (eventVsNumberOfOccurances.get(METRICS_EVENT_NAME.AGGREGATE));
        int occOfPublish = (eventVsNumberOfOccurances.get(METRICS_EVENT_NAME.PUBLISH));
        int occOfLog = (eventVsNumberOfOccurances.get(METRICS_EVENT_NAME.LOG));

        if (occOfRejectedAgg != 0) {
            avgTimeForRejectedAgg = (eventVsTimeTakenMap.get(METRICS_EVENT_NAME.REJECTED_AGG)) / occOfRejectedAgg;
        }

        if (occOfPeformedAgg != 0) {
            avgTimeForPeformedAgg = (eventVsTimeTakenMap.get(METRICS_EVENT_NAME.PERFORMED_AGG)) / occOfPeformedAgg;
        }

        if (occOfAgg != 0) {
            avgTimeForActualAgg = (eventVsTimeTakenMap.get(METRICS_EVENT_NAME.AGGREGATE)) / occOfAgg;
        }

        if (occOfPublish != 0) {
            avgTimeForPublish = (eventVsTimeTakenMap.get(METRICS_EVENT_NAME.PUBLISH)) / occOfPublish;
        }

        if (occOfLog != 0) {
            avgTimeForLog = (eventVsTimeTakenMap.get(METRICS_EVENT_NAME.LOG)) / occOfLog;
        }

        sb.append(AggMetricsUtil.AGG_METRICS_SHORT_NAME).append("-").append(aggType.toString()).append(",").append(orgName).append(",").append(streamName).append(",").append(currencyPair).append(",").append(aggregatorName).append(",").append(aggregatorID).append(",")

                .append("excCt=").append(numberOfExceptions).append(",")

                .append("rejCt=").append(occOfRejectedAgg).append(",").append(eventVsTimeTakenMap.get(METRICS_EVENT_NAME.REJECTED_AGG)).append(",").append(avgTimeForRejectedAgg).append(",").append(eventVsMaxTimeMap.get(METRICS_EVENT_NAME.REJECTED_AGG)).append(",")

                .append("succCt=").append(occOfPeformedAgg).append(",").append(eventVsTimeTakenMap.get(METRICS_EVENT_NAME.PERFORMED_AGG)).append(",").append(avgTimeForPeformedAgg).append(",").append(eventVsMaxTimeMap.get(METRICS_EVENT_NAME.PERFORMED_AGG)).append(",")

                .append("aggCt=").append(occOfAgg).append(",").append(eventVsTimeTakenMap.get(METRICS_EVENT_NAME.AGGREGATE)).append(",").append(avgTimeForActualAgg).append(",").append(eventVsMaxTimeMap.get(METRICS_EVENT_NAME.AGGREGATE)).append(",")

                .append("pubCt=").append(occOfPublish).append(",").append(eventVsTimeTakenMap.get(METRICS_EVENT_NAME.PUBLISH)).append(",").append(avgTimeForPublish).append(",").append(eventVsMaxTimeMap.get(METRICS_EVENT_NAME.PUBLISH)).append(",")

                .append("logCt=").append(occOfLog).append(",").append(eventVsTimeTakenMap.get(METRICS_EVENT_NAME.LOG)).append(",").append(avgTimeForLog).append(",").append(eventVsMaxTimeMap.get(METRICS_EVENT_NAME.LOG));
        sb.append(", pubCounts:[");
        for (Map.Entry<String, Long> entry : subscriberCountMap.entrySet()) {
            sb.append(",").append(entry.getKey()).append(":").append(entry.getValue());
        }
        sb.append("]");
        resetData();
        return sb;
    }

    private void resetData() {
        numberOfExceptions = 0;
        subscriberCountMap.clear();
        for (METRICS_EVENT_NAME eventName : METRICS_EVENT_NAME.values()) {
            eventVsTimeTakenMap.put(eventName, 0L);
            eventVsMaxTimeMap.put(eventName, 0L);
            eventVsNumberOfOccurances.put(eventName, 0);
        }
    }

    private void init() {
        eventVsTimeTakenMap = new ConcurrentHashMap<METRICS_EVENT_NAME, Long>(6);
        eventVsMaxTimeMap = new ConcurrentHashMap<METRICS_EVENT_NAME, Long>(6);
        eventVsNumberOfOccurances = new ConcurrentHashMap<METRICS_EVENT_NAME, Integer>(6);
        resetData();
    }

    public void incrementExceptionCount() {
        numberOfExceptions++;
    }

    public void incrementSubscriberCount(String subscriberKey) {
        Long l = subscriberCountMap.get(subscriberKey);
        if (l == null) l = 0L;
        subscriberCountMap.put(subscriberKey, l + 1);
    }
}
