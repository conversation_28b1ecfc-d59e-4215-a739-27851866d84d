package com.integral.circuitbreaker;

import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.functor.OrganizationModificationRemoteTransactionFunctor;
import com.integral.is.spaces.fx.esp.provision.ProvisionCache;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.rds.client.CacheCleanupObserver;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationObserver;
import com.integral.system.server.VirtualServer;
import com.integral.user.Organization;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 9/30/15
 * Time: 4:14 PM
 * To change this template use File | Settings | File Templates.
 */
public class RuleProvisionService {

    private static final String GLOBAL_ORG_NAME = "MAIN";

    private RuleDataService ruleDataService;

    protected static ISMBean isMBean = ISFactory.getInstance().getISMBean();

    //global org meta data
    private volatile OrgMetaDataProvision globalOrgMetadata;

    //Map of key (ruletype_rulelevel) vs rule provision data for global rule.
    private volatile ConcurrentHashMap<String, RuleProvision> globalRules = new ConcurrentHashMap<String, RuleProvision>();

    //Org id vs org meta data
    private ConcurrentHashMap<Integer, OrgMetaDataProvision> metadataCache = new ConcurrentHashMap<Integer, OrgMetaDataProvision>();

    //Org id vs Map of key (ruletype_rulelevel) vs rule provision data.
    private ConcurrentHashMap<Integer, ConcurrentHashMap<String, RuleProvision>> ruleCache = new ConcurrentHashMap<Integer, ConcurrentHashMap<String, RuleProvision>>();

    private static final Log log = LogFactory.getLog(RuleProvisionService.class);

    private OrgMetadataChangeHandler orgMetadataChangeHandler = new OrgMetadataChangeHandler();

    private RuleChangeHandler ruleChangeHandler = new RuleChangeHandler();

    private GlobalOrgMetadataChangeHandler globalOrgMetadataChangeHandler = new GlobalOrgMetadataChangeHandler();

    private GlobalRuleChangeHandler globalRuleChangeHandler = new GlobalRuleChangeHandler();

    private RuleCleanUpHandler ruleCleanUpHandler = new RuleCleanUpHandler();

    private RuleUniqueKeyBuilderFactory factory = RuleUniqueKeyBuilderFactory.getFactory();

    public RuleProvisionService() {

        ruleDataService = new RuleDataService();
        VirtualServer virtualServer = isMBean.getVirtualServer();

        if ( isMBean.isCircuitBreakerRulesPreloadEnabled () )
        {
            Collection<Organization> organizations = virtualServer.getOrganizations ();

            if ( null != organizations )
            {
                String orgName;
                for ( Organization organization : organizations )
                {
                    orgName = organization.getShortName ();
                    initRuleProvision ( orgName );
                }
            }
        }

        initGlobalOrgRuleProvision();

        ruleDataService.registerForRuleCleanUp(ruleCleanUpHandler);
        log.info("Initialized circuit breaker rule provision objects for the organizations.size:" + virtualServer.getOrganizations ().size () );

        //Dynamic org additions handled
        OrgUpdateHandler handler = new OrgUpdateHandler();
        OrganizationModificationRemoteTransactionFunctor.addObserver(handler);
    }

    public boolean isOrgRule(String orgName, Type type, Rule.Level level,String... keys) {
        Integer orgIndex = getOrgIndex(orgName);
        return isOrgRule(orgIndex, type, level,keys);
    }

    public boolean isOrgRule(Integer orgIndex, Type type, Rule.Level level,String... keys) {

        String key = factory.createOrGetBuilder(type).getKey(type, level,keys);
        try {
            ConcurrentHashMap<String, RuleProvision> orgRules = ruleCache.get(orgIndex);
            if (null != orgRules) {
                RuleProvision ruleProvision = orgRules.get(key);
                return ((null!=ruleProvision )&& ruleProvision.isEnabled());
            }
        } catch (Exception e) {
            log.info("getRule():Exception occurred while reading the rule for orgIndex:" + orgIndex + ",type:" + type + ",level:" + level);
            return false;
        }

        return false;
    }


    public RuleProvision getRule(Integer orgIndex, Type type, Rule.Level level,String... keys) {

        RuleProvision ruleProvision = null;
        String key = factory.createOrGetBuilder(type).getKey(type, level,keys);
        try {
            ConcurrentHashMap<String, RuleProvision> orgRules = ruleCache.get(orgIndex);
            if (null != orgRules) {
                ruleProvision = orgRules.get(key);
            }
            if (Type.isGlobalOverrideSupported(type) && (ruleProvision == null || !ruleProvision.isEnabled())) {
                return globalRules.get(key);
            }
        } catch (Exception e) {
            log.info("getRule():Exception occurred while reading the rule for orgIndex:" + orgIndex + ",type:" + type + ",level:" + level);
            return null;
        }

        return ruleProvision;
    }

    public RuleProvision getRule(String orgName, Type type, Rule.Level level) {
        try {
            Integer orgIndex = getOrgIndex(orgName);
            return getRule(orgIndex, type, level);
        } catch (Exception e) {
            log.info("getRule():Exception occurred while reading the rule for orgName:" + orgName + ",type:" + type + ",level:" + level);
            return null;
        }
    }

    public void initRuleProvision(String orgName) {

        if (log.isDebugEnabled()) {
            log.debug("initRuleProvision: initializing rule provision for the org:" + orgName);
        }
        try {
            getOrLoadOrgRuleMetadata(orgName);
            getOrLoadRules(orgName);

            ruleDataService.registerForOrgMetaDataChange(orgName, orgMetadataChangeHandler);
            ruleDataService.registerForRuleChange(orgName, ruleChangeHandler);
        } catch (Exception e) {
            log.error("initRuleProvision:Failed to init rule provision for the org:" + orgName, e);
        }
    }


    Collection<RuleProvision> getRules(String orgName) {
        try {
            return getOrLoadRules(orgName).values();
        } catch (Exception e) {
            return null;
        }
    }

    OrgMetaDataProvision getOrgMetaData(String orgName) {
        try {
            return getOrLoadOrgRuleMetadata(orgName);
        } catch (Exception e) {
            return null;
        }
    }


    private void initGlobalOrgRuleProvision() {

        if (log.isDebugEnabled()) {
            log.debug("initGlobalOrgRuleProvision: initializing global rule provision for the org:main");
        }
        try {
            getOrUpdateGlobalOrgMetaData();
            getOrUpdateGlobalOrgRules();

            ruleDataService.registerForOrgMetaDataChange(GLOBAL_ORG_NAME, globalOrgMetadataChangeHandler);
            ruleDataService.registerForRuleChange(GLOBAL_ORG_NAME, globalRuleChangeHandler);
        } catch (Exception e) {
            log.error("initGlobalOrgRuleProvision:Failed to init global rule provision for the org:" + GLOBAL_ORG_NAME, e);
        }
    }

    private ConcurrentHashMap<String, RuleProvision> getOrUpdateGlobalOrgRules() {
        if (null != globalRules && !globalRules.isEmpty()) {
            return globalRules;
        }
        updateGlobalOrgRules();
        return globalRules;
    }

    private void updateGlobalOrgRules() {
        List<Rule> rules = ruleDataService.getRules(GLOBAL_ORG_NAME);
        if (null != rules && !rules.isEmpty()) {
            RuleProvision ruleProvision;
            RuleOrgMetaData ruleOrgMetaData = ruleDataService.getOrgMetadataByShortName(GLOBAL_ORG_NAME);
            for (Rule rule : rules) {
                ruleProvision = ProvisionTranslator.translateRule(rule, ruleOrgMetaData);
                globalRules.put(factory.createOrGetBuilder(ruleProvision.getType()).getKey(ruleProvision), ruleProvision);
                if (log.isDebugEnabled()) {
                    log.debug("updateGlobalOrgRules:Updating the cb global rule provision to cache:" + ruleProvision);
                }
            }
        } else {
            log.info("updateGlobalOrgRules:No global rules found for the org:" + GLOBAL_ORG_NAME);
        }
    }

    private OrgMetaDataProvision getOrUpdateGlobalOrgMetaData() {
        if (null == globalOrgMetadata) {
            updateGlobalOrgMetaData();
        }
        return globalOrgMetadata;
    }

    private void updateGlobalOrgMetaData() {
        RuleOrgMetaData ruleOrgMetaData = ruleDataService.getOrgMetadataByShortName(GLOBAL_ORG_NAME);
        if (null != ruleOrgMetaData) {
            globalOrgMetadata = ProvisionTranslator.translateOrgMetaData(ruleOrgMetaData);
            if (log.isDebugEnabled()) {
                log.debug("updateGlobalOrgMetaData:Updating the cb rule global org meta data provision:" + globalOrgMetadata);
            }
        } else {
            log.info("updateGlobalOrgMetaData:No global org meta data found for the org:" + GLOBAL_ORG_NAME);
        }
    }


    private ConcurrentHashMap<String, RuleProvision> getOrLoadRules(String orgName) {
        Integer orgIndex = getOrgIndex(orgName);
        ConcurrentHashMap<String, RuleProvision> orgRules = ruleCache.get(orgIndex);
        RuleOrgMetaData orgMetaDataProvision = ruleDataService.getOrgMetadataByShortName(orgName);
        if (orgRules == null) {
            return buildRuleProvision(orgName, orgIndex, orgMetaDataProvision);
        }
        return orgRules;
    }

    private ConcurrentHashMap<String, RuleProvision> buildRuleProvision(String orgName, Integer orgIndex, RuleOrgMetaData orgMetaDataProvision) {
        ConcurrentHashMap<String, RuleProvision> orgRules;
        List<Rule> rules = ruleDataService.getRules(orgName);
        if (null != rules && !rules.isEmpty()) {

            orgRules = ruleCache.get(orgIndex);
            if (null == orgRules) {
                ruleCache.putIfAbsent(orgIndex, new ConcurrentHashMap<String, RuleProvision>());
                orgRules = ruleCache.get(orgIndex);
            }

            RuleProvision ruleProvision;
            for (Rule rule : rules) {
                ruleProvision = ProvisionTranslator.translateRule(rule, orgMetaDataProvision);
                orgRules.put(factory.createOrGetBuilder(ruleProvision.getType()).getKey(ruleProvision), ruleProvision);
                if (log.isDebugEnabled()) {
                    log.debug("getOrLoadRules:Updating the cb rule provision to cache:" + ruleProvision);
                }
            }

            return orgRules;
        } else {
            orgRules = new ConcurrentHashMap<String, RuleProvision>();
            ruleCache.putIfAbsent(orgIndex, orgRules);
            log.info("getOrLoadRules:No rules found for the org:" + orgName);
            return null;
        }
    }

    private OrgMetaDataProvision getOrLoadOrgRuleMetadata(String orgName) {
        Integer orgIndex = getOrgIndex(orgName);
        OrgMetaDataProvision ruleMetaData = metadataCache.get(orgIndex);
        if (null == ruleMetaData) {
            return buildOrgRuleProvision(orgName, orgIndex);
        }

        return ruleMetaData;
    }

    private OrgMetaDataProvision buildOrgRuleProvision(String orgName, Integer orgIndex) {
        RuleOrgMetaData orgMetadataByShortName = ruleDataService.getOrgMetadataByShortName(orgName);
        if (null != orgMetadataByShortName) {
            OrgMetaDataProvision orgMetaDataProvision = ProvisionTranslator.translateOrgMetaData(orgMetadataByShortName);
            metadataCache.put(orgIndex, orgMetaDataProvision);
            if (log.isDebugEnabled()) {
                log.debug("getOrLoadOrgRuleMetadata:Updating the cb rule org meta data to cache:" + orgMetaDataProvision);
            }
            return orgMetaDataProvision;
        } else {
            log.info("getOrLoadOrgRuleMetadata:No org meta data found for the org:" + orgName);
            return null;
        }
    }

    private Integer getOrgIndex(String orgName) {
        Organization organization = ReferenceDataCacheC.getInstance().getOrganization(orgName);
        Assert.notNull(organization, "getOrgIndex:Unable to obtain the organization index for the org:" + orgName);
        return organization.getIndex();
    }

    public class RuleChangeHandler implements NotificationObserver {

        @Override
        public void notifyAdd(Notification notification) {
            refreshRuleCache(notification);
        }

        @Override
        public void notifyUpdate(Notification notification) {

            refreshRuleCache(notification);
        }

        private void refreshRuleCache(Notification notification) {
            try {
                long startTime = System.currentTimeMillis();
                String entityNameSpace = notification.getEntityNameSpace();
                String entityId = notification.getEntityId();
                if (log.isDebugEnabled()) {
                    log.debug("RuleChangeHandler:Notification received for namespace:" + entityNameSpace + ",entity id:" + entityId
                            + ",Notification type:" + notification.getNotificationType());
                }

                Rule rule = ruleDataService.getRule(entityNameSpace, entityId);
                Integer orgIndex = getOrgIndex(entityNameSpace);
                ConcurrentHashMap<String, RuleProvision> orgRules = ruleCache.get(orgIndex);
                if (null == orgRules) {
                    ruleCache.put(orgIndex, new ConcurrentHashMap<String, RuleProvision>());
                    orgRules = ruleCache.get(orgIndex);
                }

                RuleProvision ruleProvision = null;

                String key = factory.createOrGetBuilder(rule.getType()).getKey(rule);
                if (rule.isActive()) {
                    RuleOrgMetaData orgMetaDataProvision = ruleDataService.getOrgMetadataByShortName(entityNameSpace);
                    ruleProvision = ProvisionTranslator.translateRule(rule, orgMetaDataProvision);
                    orgRules.put(key, ruleProvision);
                } else {
                    orgRules.remove(key);
                    log.info("RuleChangeHandler:Rule provision cache updated.Deleted rule provision for key:" + key);
                }

                long endTime = System.currentTimeMillis();
                log.info("RuleChangeHandler:Rule provision cache updated:" + ruleProvision
                        + ",Notification type:" + notification.getNotificationType() + ",Time taken:" + (endTime - startTime));

                if (rebuildOrgProvision(rule)) {
                    startTime = System.currentTimeMillis();
                    Organization organization = ReferenceDataCacheC.getInstance().getOrganization(entityNameSpace);
                    //If max order size changes at organization level need to update organization provision
                    if (null != organization) {
                        ProvisionCache.updateOrganizationProvisions(organization);
                    }
                    endTime = System.currentTimeMillis();
                    log.info("RuleChangeHandler:Organization provision cache updated:" + ruleProvision
                            + ",Notification type:" + notification.getNotificationType() + ",Time taken:" + (endTime - startTime));
                }

            } catch (Exception e) {
                log.error("RuleChangeHandler:Rule cache update failed:" + ",Notification type:" + notification.getNotificationType(), e);
            }
        }
    }

    public class OrgMetadataChangeHandler implements NotificationObserver {

        @Override
        public void notifyAdd(Notification notification) {
            refreshMetaData(notification);
        }

        @Override
        public void notifyUpdate(Notification notification) {

            refreshMetaData(notification);
        }

        private void refreshMetaData(Notification notification) {

            try {
                long startTime = System.currentTimeMillis();
                String entityNameSpace = notification.getEntityNameSpace();
                String entityId = notification.getEntityId();
                if (log.isDebugEnabled()) {
                    log.debug("OrgMetadataChangeHandler:Notification received for namespace:" + entityNameSpace + ",entity id:" + entityId
                            + ",Notification type:" + notification.getNotificationType());
                }

                RuleOrgMetaData orgMetadata = ruleDataService.getOrgMetadata(entityNameSpace, entityId);
                Integer orgIndex = getOrgIndex(entityNameSpace);
                metadataCache.put(orgIndex, ProvisionTranslator.translateOrgMetaData(orgMetadata));

                long endTime = System.currentTimeMillis();
                log.info("OrgMetadataChangeHandler:Org meta data cache updated :" + orgMetadata + ",Notification type:" + notification.getNotificationType()
                        + "Time taken:" + (endTime - startTime));

                startTime = System.currentTimeMillis();
                //refresh all rules :possible change in default meta data values.
                List<Rule> orgRules = ruleDataService.getRules(entityNameSpace);

                if (null != orgRules) {
                    ConcurrentHashMap<String, RuleProvision> orgRulesProvision = new ConcurrentHashMap<String, RuleProvision>();
                    RuleProvision value;
                    boolean rebuildOrgProvision = false;
                    for (Rule rule : orgRules) {
                        value = ProvisionTranslator.translateRule(rule, orgMetadata);
                        orgRulesProvision.put(factory.createOrGetBuilder(rule.getType()).getKey(rule), value);
                        rebuildOrgProvision=rebuildOrgProvision ||rebuildOrgProvision(rule) ;
                    }
                    //This is an atomic operation
                    ruleCache.put(orgIndex, orgRulesProvision);
                    endTime = System.currentTimeMillis();
                    log.info("OrgMetadataChangeHandler:rule cache updated :" + orgRulesProvision + ",Notification type:"
                            + notification.getNotificationType() + "Time taken:" + (endTime - startTime));

                    if(rebuildOrgProvision){
                        startTime = System.currentTimeMillis();
                        Organization organization = ReferenceDataCacheC.getInstance().getOrganization(entityNameSpace);
                        //If rule is enabled/disabled at org level provision needs to be updated
                        ProvisionCache.updateOrganizationProvisions(organization);
                        endTime = System.currentTimeMillis();
                        log.info("OrgMetadataChangeHandler:Organization provision cache updated:" + entityNameSpace
                                + ",Notification type:" + notification.getNotificationType() + ",Time taken:" + (endTime - startTime));
                    }
                }
            } catch (Exception e) {
                log.error("OrgMetadataChangeHandler:meta data refresh failed:" + ",Notification type:" + notification.getNotificationType(), e);
            }
        }
    }

    public class GlobalRuleChangeHandler implements NotificationObserver {

        @Override
        public void notifyAdd(Notification notification) {
            refreshRuleCache(notification);
        }

        @Override
        public void notifyUpdate(Notification notification) {

            refreshRuleCache(notification);
        }

        private void refreshRuleCache(Notification notification) {
            try {
                long startTime = System.currentTimeMillis();
                String entityNameSpace = notification.getEntityNameSpace();
                String entityId = notification.getEntityId();
                if (log.isDebugEnabled()) {
                    log.debug("GlobalRuleChangeHandler:Notification received for namespace:" + entityNameSpace + ",entity id:" + entityId
                            + ",Notification type:" + notification.getNotificationType());
                }

                Rule rule = ruleDataService.getRule(entityNameSpace, entityId);

                RuleProvision ruleProvision = null;
                String key = factory.createOrGetBuilder(rule.getType()).getKey(rule);
                if (rule.isActive()) {
                    RuleOrgMetaData orgMetaDataProvision = ruleDataService.getOrgMetadataByShortName(entityNameSpace);
                    ruleProvision = ProvisionTranslator.translateRule(rule, orgMetaDataProvision);
                    globalRules.put(key, ruleProvision);
                } else {
                    globalRules.remove(key);
                    log.info("GlobalRuleChangeHandler:Global Rule provision cache updated.Deleted rule provision for key:" + key);
                }

                long endTime = System.currentTimeMillis();

                log.info("GlobalRuleChangeHandler:Global Rule provision cache updated:" + ruleProvision
                        + ",Notification type:" + notification.getNotificationType() + ",Time taken:" + (endTime - startTime));

                if (rebuildOrgProvision(rule)) {
                    startTime = System.currentTimeMillis();
                    //If max order size changes globally need to update all organization provision
                    ProvisionCache.updateOrganizationProvisions();
                    endTime = System.currentTimeMillis();
                    log.info("GlobalRuleChangeHandler:All the Organization provision cache updated:" + ruleProvision
                            + ",Notification type:" + notification.getNotificationType() + ",Time taken:" + (endTime - startTime));
                }

            } catch (Exception e) {
                log.error("GlobalRuleChangeHandler:Global Rule cache update failed:" + ",Notification type:" + notification.getNotificationType(), e);
            }
        }
    }

    public class GlobalOrgMetadataChangeHandler implements NotificationObserver {

        @Override
        public void notifyAdd(Notification notification) {
            refreshMetaData(notification);
        }

        @Override
        public void notifyUpdate(Notification notification) {

            refreshMetaData(notification);
        }

        private void refreshMetaData(Notification notification) {

            try {
                long startTime = System.currentTimeMillis();
                String entityNameSpace = notification.getEntityNameSpace();
                String entityId = notification.getEntityId();
                if (log.isDebugEnabled()) {
                    log.debug("GlobalOrgMetadataChangeHandler:Notification received for namespace:" + entityNameSpace + ",entity id:" + entityId
                            + ",Notification type:" + notification.getNotificationType());
                }

                RuleOrgMetaData orgMetadata = ruleDataService.getOrgMetadata(entityNameSpace, entityId);

                globalOrgMetadata = ProvisionTranslator.translateOrgMetaData(orgMetadata);

                long endTime = System.currentTimeMillis();
                log.info("GlobalOrgMetadataChangeHandler:global Org meta data updated :" + orgMetadata + ",Notification type:" + notification.getNotificationType()
                        + "Time taken:" + (endTime - startTime));

                startTime = System.currentTimeMillis();
                //refresh all rules :possible change in default meta data values.
                List<Rule> orgRules = ruleDataService.getRules(entityNameSpace);

                boolean rebuildOrgProvision = false;
                if (null != orgRules) {
                    ConcurrentHashMap<String, RuleProvision> orgRulesProvision = new ConcurrentHashMap<String, RuleProvision>();
                    RuleProvision value;
                    for (Rule rule : orgRules) {
                        value = ProvisionTranslator.translateRule(rule, orgMetadata);
                        orgRulesProvision.put(factory.createOrGetBuilder(rule.getType()).getKey(rule), value);
                        rebuildOrgProvision = rebuildOrgProvision || rebuildOrgProvision(rule);
                    }
                    //This is an atomic operation
                    globalRules = orgRulesProvision;
                    endTime = System.currentTimeMillis();
                    log.info("GlobalOrgMetadataChangeHandler:Rule cache updated :" + orgRulesProvision + ",Notification type:"
                            + notification.getNotificationType() + "Time taken:" + (endTime - startTime));

                    if (rebuildOrgProvision) {
                        startTime = System.currentTimeMillis();
                        //If rule is enabled/disabled at global level provision needs to be updated
                        ProvisionCache.updateOrganizationProvisions();
                        endTime = System.currentTimeMillis();
                        log.info("GlobalOrgMetadataChangeHandler:All the Organization provision cache updated:,Notification type:" +
                                notification.getNotificationType() + ",Time taken:" + (endTime - startTime));
                    }
                }
            } catch (Exception e) {
                log.error("GlobalOrgMetadataChangeHandler:meta data refresh failed:" + ",Notification type:" + notification.getNotificationType(), e);
            }
        }
    }

    private boolean rebuildOrgProvision(Rule rule) {
        return  Type.MAX_ORDER_SIZE ==rule.getType() && TradeType.ESP == rule.getTradeType();
    }

    public ConcurrentHashMap<String, RuleProvision> viewRuleProvisions(String orgName) {
        Integer orgIndex = getOrgIndex(orgName);
        return ruleCache.get(orgIndex);
    }

    public OrgMetaDataProvision viewOrgRuleProvisions(String orgName) {
        Integer orgIndex = getOrgIndex(orgName);
        return metadataCache.get(orgIndex);
    }

    public OrgMetaDataProvision getGlobalOrgRuleProvisions() {
        return globalOrgMetadata;
    }

    public void clearAllProvisions() {
        metadataCache.clear();
        ruleCache.clear();
    }

    public class RuleCleanUpHandler implements CacheCleanupObserver {

        @Override
        public void onClearAll() {
            try {
                Collection<Organization> organizations = isMBean.getVirtualServer().getOrganizations();
                if (null != organizations) {
                    log.info("RuleCleanUpHandler:received rule provision clean up notification in circuit breaker:");
                    long startTime = System.currentTimeMillis();
                    String orgName;
                    Integer orgIndex;
                    for (Organization organization : organizations) {
                        orgName = organization.getShortName();
                        orgIndex = getOrgIndex(orgName);
                        buildOrgRuleProvision(orgName, orgIndex);
                        buildRuleProvisions(orgName, orgIndex);
                    }
                    log.info("RuleCleanUpHandler:Rebuilt rule provision(s) on rds clean up notification" +
                            " in circuit breaker,Time taken:" + (System.currentTimeMillis() - startTime));
                    startTime = System.currentTimeMillis();
                    updateGlobalOrgMetaData();
                    updateGlobalOrgRules();
                    log.info("RuleCleanUpHandler:Rebuilt global rule provision(s) on rds clean up notification" +
                            " in circuit breaker,Time taken:" + (System.currentTimeMillis() - startTime));

                    //If max order size changes globally need to update all organization provision
                    startTime = System.currentTimeMillis();
                    ProvisionCache.updateOrganizationProvisions();
                    log.info("RuleCleanUpHandler:Rebuilt organization provision(s) on rds clean up notification" +
                            " in circuit breaker,Time taken:" + (System.currentTimeMillis() - startTime));
                }
            } catch (Exception e) {
                log.error("RuleCleanUpHandler:Rule provision refresh failed:", e);
            }
        }

        private void buildRuleProvisions(String orgName, Integer orgIndex) {
            List<Rule> orgRules = ruleDataService.getRules(orgName);
            if (null != orgRules && !orgRules.isEmpty()) {
                RuleOrgMetaData orgMetadata = ruleDataService.getOrgMetadataByShortName(orgName);
                ConcurrentHashMap<String, RuleProvision> orgRulesProvision = new ConcurrentHashMap<String, RuleProvision>();
                for (Rule rule : orgRules) {
                    orgRulesProvision.put(factory.createOrGetBuilder(rule.getType()).getKey(rule), ProvisionTranslator.translateRule(rule, orgMetadata));
                }
                //This is an atomic operation
                ruleCache.put(orgIndex, orgRulesProvision);
            }
        }
    }
}
