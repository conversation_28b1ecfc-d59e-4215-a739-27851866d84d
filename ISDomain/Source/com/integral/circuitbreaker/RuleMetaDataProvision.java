package com.integral.circuitbreaker;

import java.util.HashSet;
import java.util.Set;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 10/5/15
 * Time: 2:31 PM
 * To change this template use File | Settings | File Templates.
 */
public class RuleMetaDataProvision {

    private static final String COMMA_SEPARATOR = ",";
    private String emailIds;

    private long durationInSeconds;

    private Set<RuleConditionProvision> ruleConditions;

    public String getEmailIds() {
        return emailIds;
    }

    public void setEmailIds(String emailIds) {
        this.emailIds = emailIds;
    }

    public long getDurationInSeconds() {
        return durationInSeconds;
    }

    public void setDurationInSeconds(long durationInSeconds) {
        this.durationInSeconds = durationInSeconds;
    }

    public Set<RuleConditionProvision> getRuleConditions() {
        return ruleConditions;
    }

    public void setRuleConditions(Set<RuleConditionProvision> ruleConditions) {
        this.ruleConditions = ruleConditions;
    }

    @Override
    public String toString() {
        return "RuleMetaDataProvision{" +
                ", emailIds='" + emailIds + '\'' +
                ", durationInSeconds=" + durationInSeconds +
                ", ruleConditions=" + ruleConditions +
                '}';
    }
}
