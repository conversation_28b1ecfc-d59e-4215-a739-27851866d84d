package com.integral.circuitbreaker;

import com.integral.is.spaces.fx.esp.provision.OrganizationProvision;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.AccountRef;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.organization.Account;
import com.integral.rds.service.AccountsServiceC;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 3/29/16
 * Time: 1:22 PM
 * To change this template use File | Settings | File Templates.
 */
public class MaxOrderSizeRuleExecutor extends RuleExecutor<SingleLegOrder> {

    private static final Log log = LogFactory.getLog(MaxOrderSizeRuleExecutor.class);

    private static final String CIRCUIT_BREAKER_MAX_ORDER_SIZE = "CircuitBreaker.MaxOrderSize";

    public MaxOrderSizeRuleExecutor(RuleProvisionService service) {
        super(service);
    }

    @Override
    protected String getGMReason(SingleLegOrder input, Object... inOut) {
        return getData(input, inOut);
    }

    @Override
    public boolean executeRules(SingleLegOrder input, Object... inOut) {

        boolean isCircuitBreakerExecuted = false;
        long startTime = System.currentTimeMillis();
        try {
            if (!isMBean.isCircuitBreakerEnabled()) {
                return isCircuitBreakerExecuted;
            }

            if (null == service) {
                log.info("executeRules:Rule provision service is not initialized");
                return isCircuitBreakerExecuted;
            }

            Organization organization = input.getOrganization();
            isCircuitBreakerExecuted = executeRuleForOrg(organization, input, inOut);

            if (!isCircuitBreakerExecuted) {
                //Check for sales dealer organization rule
                String orgNameIfSalesDealerOrder = getOrgNameIfSalesDealerOrder(input);
                if (null != orgNameIfSalesDealerOrder) {
                    User userRef = getUserRef(input);
                    if (null != userRef) {
                        isCircuitBreakerExecuted = executeRuleForOrg(userRef.getOrganization(), input, inOut);
                        if (isCircuitBreakerExecuted) {
                            log.info("ESP Max order size circuit breaker was triggered for sales dealer organization rule");
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("executeRules:Failed to execute max order size rules", e);
            isCircuitBreakerExecuted = false;
        }
        log.info("Time Taken for max order size rule execution:" + (System.currentTimeMillis() - startTime) + "(ms)");
        return isCircuitBreakerExecuted;
    }

    private boolean executeRuleForOrg(Organization organization, SingleLegOrder input, Object... inOut) {
        boolean isRuleActionExecuted = false;
        if (null == organization) {
            log.info("executeRules:Unable to find the organization in max order size rule execution for the orderId:" + input.get_id());
            return isRuleActionExecuted;
        }

        RuleProvision rule = service.getRule(organization.getIndex(), Type.MAX_ORDER_SIZE, Rule.Level.ORG);
        if (rule == null || !rule.isEnabled()) {
            return isRuleActionExecuted;
        }

        //Unique id but no correlation exist for the same today
        String correlationId = String.valueOf(System.currentTimeMillis());
        for (Integer action : rule.getActions()) {
            executeAction(input,correlationId, Rule.Level.ORG, Type.MAX_ORDER_SIZE, ActionType.getType(action), rule, inOut);
            isRuleActionExecuted = true;
        }

        return isRuleActionExecuted;
    }

    protected Map<String, String> getValues(String correlationId, SingleLegOrder input, Rule.Level level, Object... inOut) {
        Map<String, String> values = super.getValues(correlationId, input, level, inOut);
        OrganizationProvision.AmountsProvision amtProv = (OrganizationProvision.AmountsProvision) inOut[0];
        values.put(ORDER_AMOUNT, getFormattedValue(input.getRequestLeg().getDealtAmountInUSD()));
        values.put(ORDER_LIMIT, getFormattedValue(amtProv.getMaxOrderAmountInUSD()));
        findAndAddRuleLevel(input, values,Type.MAX_ORDER_SIZE);
        return values;
    }


    @Override
    protected String getData(SingleLegOrder input, Object... inOut) {
        OrganizationProvision.AmountsProvision amtProv = (OrganizationProvision.AmountsProvision) inOut[0];
        double maxOrderAmountInUSD = amtProv.getMaxOrderAmountInUSD();
        double dealtAmountInUSD = input.getRequestLeg().getDealtAmountInUSD();
        return "ESP Max Order Size circuit breaker triggered, Limit in USD:" + getFormattedValue(maxOrderAmountInUSD) + " and the Order Amount in USD:" + getFormattedValue(dealtAmountInUSD);
    }

    @Override
    public String getOrgName(SingleLegOrder input) {
        return input.getOrganization().getShortName();
    }

    @Override
    public String getUserOrgName(SingleLegOrder input) {
        User user = input.getUser();
        if (null != user) {
            Organization organization = user.getOrganization();
            if (null != organization) {
                return organization.getName();
            }
        }
        return null;
    }

    @Override
    public Integer getOrgIndex(SingleLegOrder input) {
        return input.getOrganization().getIndex();
    }

    @Override
    public String getLegalEntity(SingleLegOrder input) {
        return input.getLegalEntityName();
    }

    @Override
    public String getAccount(SingleLegOrder input) {
        AccountRef accountRef = input.getAccountRef();
        if (null != accountRef) {
            Account accountObject = AccountsServiceC.getInstance().getAccountById(accountRef.getNamespace(),accountRef.getUid());
            if (null != accountObject) {
                accountObject.get_id();
            }
        }
        return null;
    }

    @Override
    public String getUser(SingleLegOrder input) {
        return input.getUserName();
    }

    @Override
    public String getOrderId(SingleLegOrder input) {
        return input.get_id();
    }

    @Override
    public String getClientOrderId(SingleLegOrder input) {
        return input.getClientDescriptor().getClientReferenceId();
    }

    @Override
    public String getTradeId(SingleLegOrder input) {
        return null;
    }

    @Override
    public Organization getOrganization(SingleLegOrder input) {
        return input.getOrganization();
    }

    @Override
    public User getUserRef(SingleLegOrder input) {
        return input.getUser();
    }

    @Override
    public String getEventType() {
        return CIRCUIT_BREAKER_MAX_ORDER_SIZE;
    }
}
