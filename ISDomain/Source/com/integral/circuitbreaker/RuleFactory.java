package com.integral.circuitbreaker;

import com.integral.is.finance.dealing.ServiceFactory;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 10/15/15
 * Time: 1:43 PM
 * To change this template use File | Settings | File Templates.
 */
public class RuleFactory {

    private static final String DELIMITTER = "_";
    private ConcurrentHashMap<String, RuleExecutor> strategies = new ConcurrentHashMap<String, RuleExecutor>();

    private static class Holder {
        public static final RuleFactory INSTANCE = new RuleFactory();
    }

    public static RuleFactory getFactory() {
        return Holder.INSTANCE;
    }

    /**
     * Strategies are wired to threads on server start up.
     *
     * @param type
     * @return
     */
    public RuleExecutor createOrGetExecutor(Type type, TradeType tradeType) {
        String key = getKey(type, tradeType);
        RuleExecutor executor = strategies.get(key);
        if (executor != null) {
            return executor;
        }
        RuleProvisionService ruleProvisionService = ServiceFactory.getRuleProvisionService();
        switch (type) {
            case DK:
                DKRuleExecutor dkRuleExecutor = new DKRuleExecutor(ruleProvisionService);
                executor = strategies.putIfAbsent(key, dkRuleExecutor);
                if (executor == null) {
                    executor = dkRuleExecutor;
                }
                return executor;
            case MAX_ORDER_SIZE:
                switch (tradeType) {
                    case ESP:
                        MaxOrderSizeRuleExecutor maxOrderSizeRuleExecutor = new MaxOrderSizeRuleExecutor(ruleProvisionService);
                        executor = strategies.putIfAbsent(key, maxOrderSizeRuleExecutor);
                        if (executor == null) {
                            executor = maxOrderSizeRuleExecutor;
                        }
                        return executor;

                    case RFS:
                        RFSMaxOrderSizeRuleExecutor rfsMaxOrderSizeRuleExecutor = new RFSMaxOrderSizeRuleExecutor(ruleProvisionService);
                        executor = strategies.putIfAbsent(key, rfsMaxOrderSizeRuleExecutor);
                        if (executor == null) {
                            executor = rfsMaxOrderSizeRuleExecutor;
                        }
                        return executor;
                    default:
                        throw new UnsupportedOperationException("Unsupported trade type " + tradeType + " for rule execution type:" + type);
                }
            case VOLUME:
                    return null;
            default:
                throw new UnsupportedOperationException("Unsupported rule execution type:" + type);
        }
    }

    private String getKey(Type type, TradeType tradeType) {
        return type + DELIMITTER + tradeType;
    }
}
