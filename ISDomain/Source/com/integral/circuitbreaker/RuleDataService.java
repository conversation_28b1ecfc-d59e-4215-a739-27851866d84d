package com.integral.circuitbreaker;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.rds.client.CacheCleanupObserver;
import com.integral.rds.client.RDSClientStore;
import com.integral.rds.notification.NotificationObserver;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 9/29/15
 * Time: 4:03 PM
 * To change this template use File | Settings | File Templates.
 */
public class RuleDataService {

    private static final String SHORT_NAME_SEPARATOR = "-";
    private RDSClientStore clientStore = new RDSClientStore();
    protected Log log = LogFactory.getLog(RuleDataService.class);

    public void registerForOrgMetaDataChange(String nameSpace, NotificationObserver observer) {
        clientStore.addObserver(RuleOrgMetaData.class, nameSpace, observer);
    }

    public void registerForRuleChange(String nameSpace, NotificationObserver observer) {
        clientStore.addObserver(Rule.class, nameSpace, observer);
    }

    public void registerForRuleCleanUp(CacheCleanupObserver observer) {
        clientStore.addCleanUpObserver(observer);
    }

    public Rule updateRule(Rule rule) {
        return (Rule) clientStore.updateObject(rule);
    }

    public Rule createRule(Rule rule) {
        return (Rule) clientStore.insertObject(rule);
    }

    public List<Rule> getRulesByType(String orgName,Type type) {
        Map<String, String> queryMap = new HashMap<String, String>();
        queryMap.put("org",orgName);
        queryMap.put("typ", type.name());
        queryMap.put("act",Boolean.TRUE.toString());
        return (List<Rule>) clientStore.getObjectByQuery(orgName, Rule.class,queryMap );
    }

    public List<Rule> getRules(String orgName) {
        Map<String, String> queryMap = new HashMap<String, String>();
        queryMap.put("org",orgName);
        queryMap.put("act",Boolean.TRUE.toString());
        return (List<Rule>) clientStore.getObjectByQuery(orgName, Rule.class,queryMap );
    }

    public Rule getRuleForUpdate(String orgName, String id) {
        Rule rule = getRule(orgName, id);
        return getLatestRule(rule);
    }

    private Rule getLatestRule(Rule rule) {
        if (null == rule) {
            return rule;
        }
        return (Rule) clientStore.getLatestObject(Rule.class, rule.get_id(), rule.getNamespaceName());
    }

    public Rule getRuleByShortName(String orgName, String shortName) {
        return (Rule) clientStore.getFirstObjectByShortName(orgName, shortName, Rule.class);
    }

    public Rule getRule(String orgName, String id) {
        return (Rule) clientStore.getObjectById(orgName, id, Rule.class);
    }

    public RuleOrgMetaData createOrgMetadata(RuleOrgMetaData orgMetaData) {
        return (RuleOrgMetaData) clientStore.insertObject(orgMetaData);
    }

    public RuleOrgMetaData updateOrgMetadata(RuleOrgMetaData cbRule) {
        return (RuleOrgMetaData) clientStore.updateObject(cbRule);
    }

    public RuleOrgMetaData getOrgMetadataForUpdate(String orgName, String id) {
        RuleOrgMetaData cbRule = getOrgMetadata(orgName, id);
        return getLatestOrgMetadata(cbRule);
    }

    public RuleOrgMetaData getOrgMetadataByShortName(String orgName) {
        return (RuleOrgMetaData) clientStore.getFirstObjectByShortName(orgName, orgName, RuleOrgMetaData.class);
    }

    public RuleOrgMetaData getOrgMetadata(String orgName, String id) {
        return (RuleOrgMetaData) clientStore.getObjectById(orgName, id, RuleOrgMetaData.class);
    }

    private RuleOrgMetaData getLatestOrgMetadata(RuleOrgMetaData orgMetaData) {
        if (null == orgMetaData) {
            return orgMetaData;
        }
        return (RuleOrgMetaData) clientStore.getLatestObject(RuleOrgMetaData.class, orgMetaData.get_id(), orgMetaData.getNamespaceName());
    }
}
