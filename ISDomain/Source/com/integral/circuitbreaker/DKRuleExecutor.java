package com.integral.circuitbreaker;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.fix.client.FixConstants;
import com.integral.is.ISCommonConstants;
import com.integral.is.oms.OrderConstants;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.WorkflowMessage;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 10/13/15
 * Time: 1:58 PM
 * To change this template use File | Settings | File Templates.
 */
public class DKRuleExecutor extends RuleExecutor<WorkflowMessage> {

    private static final Log log = LogFactory.getLog(DKRuleExecutor.class);

    private static final String CIRCUIT_BREAKER_DK_TRADE = "CircuitBreaker.DKTrade";

    public DKRuleExecutor(RuleProvisionService service) {
        super(service);
    }

    @Override
    public boolean executeRules(WorkflowMessage input, Object... inOut) {
        return this.executeRules(input);
    }

    public boolean executeRules(WorkflowMessage input) {
        long startTime = System.currentTimeMillis();
        boolean isExecuted = false;
        try {
            if (null == input || !isMBean.isCircuitBreakerEnabled()) {
                return isExecuted;
            }

            if (null == service) {
                log.info("executeRules:Rule provision service is not initialized");
                return isExecuted;
            }

            Organization organization = (Organization) input.getParameterValue(ISCommonConstants.WF_PARAM_DK_CUST_ORG);

            if (null == organization) {
                log.info("executeRules:Unable to find the organization in don't know workflow execution");
                return isExecuted;
            }

            for (Rule.Level level : Rule.Level.values()) {
                RuleProvision rule = service.getRule(getOrgIndex(input), Type.DK, level);
                if (rule == null || !rule.isEnabled()) {
                    continue;
                }

                //Unique id but no correlation exist for the same today
                String correlationId = String.valueOf(System.currentTimeMillis());
                for (Integer action : rule.getActions()) {
                    executeAction(input,correlationId , level, Type.DK, ActionType.getType(action), rule);
                }
            }
            isExecuted = true;
        } catch (Exception e) {
            log.error("executeRules:Failed to execute Don't Know Trade rules", e);
            isExecuted = false;
        }
        log.info("Time Taken for DK rule execution:" + (System.currentTimeMillis() - startTime) + "(ms)");
        return isExecuted;
    }

    @Override
    protected String getGMReason(WorkflowMessage input, Object... inOut) {
        return "Don't KnowTrade circuit breaker triggered";
    }

    @Override
    protected String getData(WorkflowMessage input, Object... inOut) {
        return null;
    }

    protected Map<String, String> getValues(String correlationId, WorkflowMessage input, Rule.Level level, Object... inOut) {
        Map<String, String> values = super.getValues(correlationId, input, level, inOut);
        findAndAddRuleLevel(input, values, Type.DK);
        return values;
    }

    protected void findAndAddRuleLevel(WorkflowMessage input, Map<String, String> values,Type type,String... keys) {

        Rule.Level level = Rule.Level.valueOf(values.get(LEVEL_ENUM));

        boolean isOrgRule = service.isOrgRule(getOrgIndex(input), type,level,keys);
        if (!isOrgRule) {
            //Check for sales dealer org
            String orgNameIfSalesDealerOrder = getOrgNameIfSalesDealerOrder(input);
            if (null != orgNameIfSalesDealerOrder) {
                isOrgRule = service.isOrgRule(orgNameIfSalesDealerOrder, type,level,keys);
                if (!isOrgRule) {
                    //Global rule is applied
                    values.put(LEVEL, GLOBAL);
                }
            }
            else{
                values.put(LEVEL, GLOBAL);
            }
        }
    }

    @Override
    public String getOrgName(WorkflowMessage input) {
        Organization parameterValue = (Organization) input.getParameterValue(ISCommonConstants.WF_PARAM_DK_CUST_ORG);
        return parameterValue.getShortName();
    }

    @Override
    public String getUserOrgName(WorkflowMessage input) {
        return (String) input.getParameterValue(FixConstants.WF_PARAM_USER_ORG);
    }

    @Override
    public Integer getOrgIndex(WorkflowMessage input) {
        Organization parameterValue = (Organization) input.getParameterValue(ISCommonConstants.WF_PARAM_DK_CUST_ORG);
        return parameterValue.getIndex();
    }

    @Override
    public String getLegalEntity(WorkflowMessage input) {
        LegalEntity le = (LegalEntity) input.getParameterValue(FixConstants.WF_PARAM_CUSTOMER_LE);
        if (le != null) {
            return le.getShortName();
        }
        return null;
    }

    @Override
    public String getAccount(WorkflowMessage input) {
        return (String) input.getParameterValue(FixConstants.WF_PARAM_ACCOUNTID);
    }

    @Override
    public String getUser(WorkflowMessage input) {
        User sender = input.getSender();
        if (null != sender) {
            return sender.getShortName();
        }
        return null;
    }

//    protected void raiseGMNotification(WorkflowMessage input, AlertRuntimeMonitor instance, Alert alert, Set<Integer> actions) {
//
//        if (actions.contains(ActionType.SUSPEND_TRADING.getType())) {
//            try {
//                Account accountObject = getAccountObject(input);
//                if (null != accountObject) {
//                    String serverURL = ConfigurationFactory.getServerMBean().getServerURL();
//                    String serverPortString = serverURL.substring(serverURL.indexOf(':', 8) + 1);
//                    String serverHostName = InetAddress.getLocalHost().getHostName();
//                    if (null != serverHostName && null != serverPortString) {
//                        StringBuilder url = new StringBuilder("http://");
//                        url.append(serverHostName);
//                        url.append(":");
//                        url.append(serverPortString);
//                        url.append("/fxi/refdata/account/setTradingStatus?org=");
//                        url.append(accountObject.getNamespaceName());
//                        url.append("&id=");
//                        url.append(accountObject.get_id());
//                        url.append("&tradingEnabled=true");
//                        alert.setResURL(url.toString());
//                    } else {
//                        log.info("raiseGMNotification:Unable to find host and port information for GM notification resolution URL host:" + serverHostName + ",port:" + serverPortString);
//                    }
//                } else {
//                    log.info("raiseGMNotification:Unable to find account reference data to update resolution URL for GM notification:" + getAccount(input));
//                }
//            } catch (Exception e) {
//                log.error("raiseGMNotification:Exception occurred while updating resolution URL for GM notification", e);
//            }
//        } else {
//            if (log.isDebugEnabled()) {
//                log.debug("raiseGMNotification:Resolution URL for GM notification is not required since there's no suspend trading action for the rule");
//            }
//        }
//        super.raiseGMNotification(input, instance, alert, actions);
//    }

    @Override
    public String getOrderId(WorkflowMessage input) {
        return (String) input.getParameterValue(OrderConstants.WF_Param_OrderId);
    }

    @Override
    public String getClientOrderId(WorkflowMessage input) {
        return (String) input.getParameterValue(OrderConstants.WF_Param_ClOrdId);
    }

    @Override
    public String getTradeId(WorkflowMessage input) {
        return (String) input.getParameterValue(FixConstants.WF_PARAM_EXECID);
    }

    public Organization getOrganization(WorkflowMessage input) {
        Organization parameterValue = (Organization) input.getParameterValue(ISCommonConstants.WF_PARAM_DK_CUST_ORG);
        return parameterValue;
    }

    public User getUserRef(WorkflowMessage input) {
        return input.getSender();
    }

    @Override
    public String getEventType() {
        return CIRCUIT_BREAKER_DK_TRADE;
    }

}
