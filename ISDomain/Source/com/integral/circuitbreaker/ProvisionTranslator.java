package com.integral.circuitbreaker;

import java.util.HashSet;
import java.util.Set;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 9/30/15
 * Time: 5:14 PM
 * To change this template use File | Settings | File Templates.
 */
public class ProvisionTranslator {

    public static OrgMetaDataProvision translateOrgMetaData(RuleOrgMetaData ruleOrgMetaData) {
        OrgMetaDataProvision orgMetaDataProvision = new OrgMetaDataProvision();
        orgMetaDataProvision.setOrg(ruleOrgMetaData.getShortName());
        orgMetaDataProvision.setEmailIds(ruleOrgMetaData.getEmailIds());
        orgMetaDataProvision.setDurationInSeconds(ruleOrgMetaData.getDurationInSeconds());
        orgMetaDataProvision.setChangePercentage(ruleOrgMetaData.getChangePercentage());
        orgMetaDataProvision.setEnabled(ruleOrgMetaData.isEnabled());
        return orgMetaDataProvision;
    }

    public static RuleProvision translateRule(Rule rule, RuleOrgMetaData orgMetaDataProvision) {
        RuleProvision provision = new RuleProvision();
        provision.setOrg(rule.getOrg());
        provision.setType(rule.getType());
        provision.setLevel(rule.getLevel());
        provision.setTradeType(rule.getTradeType());
        provision.setActions(rule.getActions());
        provision.setMetaData(translateMetaData(rule.getMetaData(), orgMetaDataProvision));

        if (rule.isEnabled() && (orgMetaDataProvision != null && orgMetaDataProvision.isEnabled())) {
            provision.setEnabled(true);
        } else if (rule.isEnabled() && (orgMetaDataProvision == null)) {
            provision.setEnabled(true);
        } else {
            provision.setEnabled(false);
        }
        return provision;
    }

    public static RuleMetaDataProvision translateMetaData(RuleMetaData ruleMetaData, RuleOrgMetaData orgMetaDataProvision) {
        RuleMetaDataProvision ruleMetaDataProvision = new RuleMetaDataProvision();
        String emailIds = ruleMetaData.getEmailIds();
        if (emailIds != null) {
            ruleMetaDataProvision.setEmailIds(emailIds);
        } else if (null != orgMetaDataProvision) {
            ruleMetaDataProvision.setEmailIds(orgMetaDataProvision.getEmailIds());
        }

        long durationInSeconds = ruleMetaData.getDurationInSeconds();
        Long defaultDurationInSeconds = Long.valueOf("0");
        if (defaultDurationInSeconds != durationInSeconds) {
            ruleMetaDataProvision.setDurationInSeconds(durationInSeconds);
        } else if (null != orgMetaDataProvision) {
            ruleMetaDataProvision.setDurationInSeconds(orgMetaDataProvision.getDurationInSeconds());
        }

        Double defaultChangePercentage = Double.valueOf("0");

        Set<RuleCondition> ruleConditions = ruleMetaData.getRuleConditions();
        if (null != ruleConditions && !ruleConditions.isEmpty()) {
            Set<RuleConditionProvision> ruleConditionProvisions = new HashSet<RuleConditionProvision>();
            for (RuleCondition ruleCondition : ruleConditions) {
                RuleConditionProvision ruleConditionProvision = new RuleConditionProvision();
                ruleConditionProvision.setRuleConditionType(ruleCondition.getRuleConditionType());
                ruleConditionProvision.setKey(ruleCondition.getKey());

                if (defaultChangePercentage != ruleCondition.getValue()) {
                    ruleConditionProvision.setValue(ruleCondition.getValue());
                } else if (null != orgMetaDataProvision) {
                    ruleConditionProvision.setValue(orgMetaDataProvision.getChangePercentage());
                }
                ruleConditionProvisions.add(ruleConditionProvision);
            }
            ruleMetaDataProvision.setRuleConditions(ruleConditionProvisions);
        } else {
            ruleMetaDataProvision.setRuleConditions(new HashSet<RuleConditionProvision>());
        }

        return ruleMetaDataProvision;
    }
}
