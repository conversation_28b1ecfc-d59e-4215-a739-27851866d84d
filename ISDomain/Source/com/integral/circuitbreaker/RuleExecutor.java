package com.integral.circuitbreaker;

import com.integral.is.AlertMBean;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.email.EmailConstants;
import com.integral.is.common.email.TradeEmailMBean;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.log.MessageLogger;
import com.integral.is.management.monitor.AlertRuntimeMonitor;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mail.MailService;
import com.integral.management.alert.*;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.organization.Account;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.rds.service.AccountsServiceC;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.Tuple;

import java.text.DecimalFormat;
import java.util.*;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 9/30/15
 * Time: 4:12 PM
 * To change this template use File | Settings | File Templates.
 */
public abstract class RuleExecutor<T> {


    private static final String ID = "@Id";
    private static final String CORRELATION_ID = "@CorrelationId";

    protected static final String LEVEL = "@Level";
    protected static final String LEVEL_ENUM = "@Level_Enum";
    protected static final String GLOBAL = "Global";
    protected static final String ORG = "ORG";

    protected static final String ORDER_AMOUNT = "@OrderAmount";
    protected static final String ORDER_LIMIT = "@OrderLimit";
    public static final String FORMATTING_PATTERN = "#,###.00";

    protected RuleProvisionService service;

    protected static MessageLogger messageLogger = MessageLogger.getInstance();

    protected static ISMBean isMBean = ISFactory.getInstance().getISMBean();

    protected TradeEmailMBean tradeEmailMBean = ISFactory.getInstance().getTradeEmailMBean();

    private static final Log log = LogFactory.getLog(RuleExecutor.class);
    private Object eventType;

    public RuleExecutor(RuleProvisionService service) {
        this.service = service;
    }

    protected abstract String getGMReason(T input,Object... inOut);

    public abstract boolean executeRules(T input,Object... inOut);

    protected abstract String getData(T input, Object[] inOut) ;

    public void executeAction(T input,String correlationId, Rule.Level level, Type type, ActionType actionType, RuleProvision rule,Object... inOut) {

        RuleMetaDataProvision metaData = rule.getMetaData();
        long startTime = System.currentTimeMillis();

        Map<String, String> values = getValues(correlationId, input, level, inOut);
        switch (actionType) {
            case FXI_ALERT_LOGGING:
                executeAlertLoggingAction(values,input,type,inOut);
                break;
            case EMAIL_NOTIFICATION:
                executeEmailNotificationAction(values,input, metaData,inOut);
                break;
            case GM_NOTIFICATION:
                executeGMNotification(values,input, type, rule.getActions(),inOut);
                break;
            case SUSPEND_TRADING:
                executeSuspendTrading(input, level);
                break;
            default:
                throw new UnsupportedOperationException("Action type un supported:" + actionType);
        }

        if(log.isDebugEnabled()){
            log.debug("Time taken to execute action:" + actionType + ",is:" + (System.currentTimeMillis() - startTime) + "(ms)");
        }
    }

    private void executeGMNotification(Map<String, String> values,T input, Type type, Set<Integer> actions,Object... inOut) {
        AlertRuntimeMonitor instance = AlertRuntimeMonitor.getInstance();
        Alert alert = instance.newAlert(values.get(CORRELATION_ID), AlertState.RAISED, AlertLevels.NOTIFICATION, AlertType.TRADE);
        Organization userOrg = getOrganization(input);
        alert.setUserOrganization((userOrg != null) ? userOrg.getShortName() : "");
        if(userOrg != null && userOrg.getBrokerOrganization() != null){
        	alert.setBrokerOrganization(userOrg.getBrokerOrganization().getShortName());
		}
        alert.setUser((getUserRef(input) != null) ? getUserRef(input).getShortName() : "");
        String gmReason = getGMReason(input, inOut);
        alert.setText(gmReason+" .The Rule is Defined for:"+values.get(LEVEL));
        alert.setData(getData(input));
        raiseGMNotification(input, instance, alert, actions);
        log.info("executeGMNotification:circuit breaker triggered for the rule type:" + type + ",details:" + alert);
    }

    protected String getOrgNameIfSalesDealerOrder(T input) {
        String orgName = getOrgName(input);
        String userOrgName = getUserOrgName(input);
        if (null != orgName && null != userOrgName) {
            if (!orgName.equalsIgnoreCase(userOrgName)) {
                return userOrgName;
            }
        }
        return null;
    }

    protected void raiseGMNotification(T input, AlertRuntimeMonitor instance, Alert alert, Set<Integer> actions) {
        instance.sendAlertMessage(alert);
    }

    private List<Tuple<AlertDataKey, String>> getData(T input) {
        List<Tuple<AlertDataKey, String>> data = new ArrayList<Tuple<AlertDataKey, String>>();
        addNonNullValues(data, AlertDataKey.TradeId, getTradeId(input));
        addNonNullValues(data, AlertDataKey.OrderId, getOrderId(input));
        addNonNullValues(data, AlertDataKey.Account, getAccount(input));
        addNonNullValues(data, AlertDataKey.LE, getLegalEntity(input));
        addNonNullValues(data, AlertDataKey.ClOrdId, getClientOrderId(input));
        return data;
    }

    private void addNonNullValues(List<Tuple<AlertDataKey, String>> data, AlertDataKey key, String value) {
        if (null != key && null != value) {
            data.add(new Tuple<AlertDataKey, String>(key, value));
        }
    }

    private void executeSuspendTrading(T input, Rule.Level level) {

        switch (level) {
            case ACCOUNT: {
                String accountName = getAccount(input);
                if (accountName != null) {
                    Account account = getAccountObject(input);
                    suspendTradingForAccount(account, accountName);
                } else {
                    log.info("executeSuspendTrading:Unable to get the account name for the suspend trading action:" + accountName);
                }
                break;
            }
            case ORG:
            case LE:
            case USER:
                break;
            default:
                throw new UnsupportedOperationException("Level un supported:" + level);

        }
    }

    protected Account getAccountObject(T input) {
        String accountName = getAccount(input);
        if (accountName != null) {
            String orgName = getOrgName(input);
            String userOrgName = getUserOrgName(input);
            if (userOrgName == null) {
                log.info("getAccountObject:Unable to get account reference object:" + accountName + ",user org is invalid:" + userOrgName);
                return null;
            }
            if (orgName == null) {
                log.info("getAccountObject:Unable to get account reference object:" + accountName + ",customer org is invalid:" + userOrgName);
                return null;
            }
            AccountsServiceC accountService = AccountsServiceC.getInstance();
            Account account = accountService.getAccount(userOrgName, orgName, accountName);
            return account;
        }
        return null;
    }


    protected void suspendTradingForAccount(Account account, String accountName) {
        AccountsServiceC accountService = AccountsServiceC.getInstance();
        if (account != null) {
            try {
                account.setTradingEnabled(false);
                accountService.updateAccount(account);
            } catch (Exception ex) {
                log.error("executeAction.SUSPEND_TRADING:Unable to suspended Trading for the account: " + accountName, ex);
            }
            log.info("executeSuspendTrading:Trading suspended for the account:" + accountName);
        } else {
            log.info("executeSuspendTrading:Unable to get the account for the suspend trading action:" + accountName);
        }
    }

    private void executeEmailNotificationAction(Map<String, String> values,T input, RuleMetaDataProvision metaData,Object... inOut) {
        String orgName = getOrgName(input);
        values.put(EmailConstants.TRADE_ORGANIZATION, getNotNullValue(orgName));
        WorkflowMessage emailWflMsg = MessageFactory.newWorkflowMessage();
        emailWflMsg.setTopic(ISConstantsC.MSG_TOPIC_TRADE);
        emailWflMsg.setEvent(ISConstantsC.MSG_EVT_CREATE);
        emailWflMsg.setStatus(MessageStatus.SUCCESS);
        emailWflMsg.setParameterValue(EmailConstants.TRADE_EVENT_TYPE, getEventType());
        emailWflMsg.setParameterValue(EmailConstants.TRADE_MAIL_CONTENTS, values);
        Organization organization = ReferenceDataCacheC.getInstance().getOrganization(orgName);
        values.put(EmailConstants.EMAIL_SUPPORT_MSG, getNotNullValue(tradeEmailMBean.getSupportMsg(organization)));
        values.put(EmailConstants.EMAIL_COPYRIGHT, getNotNullValue(tradeEmailMBean.getCopyright(organization)));
        String emailId = ISUtilImpl.getInstance().replaceSemicolonDelimiter(metaData.getEmailIds());
        emailWflMsg.setParameterValue(EmailConstants.TRADE_CLIENT_MAIL_ID, emailId);

        sendEmail(emailWflMsg);
    }

    protected Map<String, String> getValues(String correlationId, T input, Rule.Level level, Object... inOut) {
        Map<String, String> values = new HashMap<String, String>();
        values.put(EmailConstants.TRADE_ORDER_ID, getNotNullValue(getOrderId(input)));
        values.put(EmailConstants.TRADE_EXTERNAL_TRADE_ID, getNotNullValue(getTradeId(input)));
        values.put(LEVEL,ORG );
        values.put(LEVEL_ENUM, level.toString());
        values.put(EmailConstants.EMAIL_CLIENT_ORDER_ID, getNotNullValue(getClientOrderId(input)));
        values.put(ID, getNotNullValue(getId(input, level)));
        values.put(CORRELATION_ID,correlationId);
        return values;
    }


    protected void findAndAddRuleLevel(T input, Map<String, String> values,Type type,String... keys) {
        //default level is rule level : ORG
        boolean isOrgRule = service.isOrgRule(getOrgIndex(input), type, Rule.Level.ORG,keys);
        if (!isOrgRule) {
            //Check for sales dealer org
            String orgNameIfSalesDealerOrder = getOrgNameIfSalesDealerOrder(input);
            if (null != orgNameIfSalesDealerOrder) {
                isOrgRule = service.isOrgRule(orgNameIfSalesDealerOrder, type, Rule.Level.ORG,keys);
                if (!isOrgRule) {
                    //Global rule is applied
                    values.put(LEVEL, GLOBAL);
                }
            }
            else{
                values.put(LEVEL, GLOBAL);
            }
        }
    }

    protected String getFormattedValue(double value) {
        DecimalFormat formatter = new DecimalFormat(FORMATTING_PATTERN);
        return formatter.format(value);
    }


    protected String getNotNullValue(String value) {
        return (null != value) ? value : "";
    }

    protected void sendEmail(WorkflowMessage emailWflMsg) {
        try {
            MailService.getInstance().sendEmail(emailWflMsg, Collections.emptyMap());
        } catch (Exception ex) {
            log.error("executeAction.EMAIL_NOTIFICATION: Error in sending mail on circuit breaker trigger action: ", ex);
            messageLogger.log(AlertMBean.EMAIL, this.getClass().getName(),
                    "Error in sending email on circuit breaker trigger action. exception[" + ex.getMessage() + "] ", emailWflMsg.toString());
        }
    }

    protected void executeAlertLoggingAction(Map<String, String> values,T input, Type type,Object... inOut) {
        String message = getMessage(input, type, values);
        String data = getData(input, inOut);
        messageLogger.log(ISCommonConstants.CIRCUIT_BREAKER_TRIGGERED, "RuleExecutor.executeAlertLoggingAction",
                "CIRCUIT-BREAKER-TRIGGERED " + message, data);
        log.warn("CIRCUIT-BREAKER-TRIGGERED " + message+" "+data);
    }



    public String getMessage(T input, Type type, Map<String, String> values) {
        Rule.Level level = Rule.Level.valueOf(values.get(LEVEL_ENUM));
        StringBuilder message = new StringBuilder();
        message.append("For the rule type:" + type);
        message.append(",defined for:");
        message.append(values.get(LEVEL));
        message.append(",whose organization name is:");
        message.append(getOrgName(input));
        message.append(",at the rule level of:");
        message.append(level);
        message.append(",with the id:");
        message.append(getId(input, level));
        String tradeId = getTradeId(input);
        if (null != tradeId) {
            message.append(",with trade id:");
            message.append(tradeId);
        }
        message.append(",for the following order id:");
        message.append(getOrderId(input));

        String clientOrderId = getClientOrderId(input);
        if (null != clientOrderId) {
            message.append(" and client order id:");
            message.append(clientOrderId);
        }
        return message.toString();
    }

    private String getId(T input, Rule.Level level) {
        switch (level) {
            case ORG:
                return getOrgName(input);
            case LE:
                return getLegalEntity(input);
            case USER:
                return getUser(input);
            case ACCOUNT:
                return getAccount(input);
            default:
                throw new UnsupportedOperationException("Rule.Level un supported:" + level);
        }
    }

    public abstract String getOrgName(T input);

    public abstract String getUserOrgName(T input);

    public abstract Integer getOrgIndex(T input);

    public abstract String getLegalEntity(T input);

    public abstract String getAccount(T input);

    public abstract String getUser(T input);

    public abstract String getOrderId(T input);

    public abstract String getClientOrderId(T input);

    public abstract String getTradeId(T input);

    public abstract Organization getOrganization(T input);

    public abstract User getUserRef(T input);

    public abstract String getEventType() ;
}
