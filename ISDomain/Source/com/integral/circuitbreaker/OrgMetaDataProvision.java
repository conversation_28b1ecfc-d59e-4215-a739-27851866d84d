package com.integral.circuitbreaker;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 9/30/15
 * Time: 5:13 PM
 * To change this template use File | Settings | File Templates.
 */
public class OrgMetaDataProvision {

    private String org;
    private String emailIds;
    private double changePercentage;
    private long durationInSeconds;
    private boolean enabled;

    public String getOrg() {
        return org;
    }

    public void setOrg(String org) {
        this.org = org;
    }

    public String getEmailIds() {
        return emailIds;
    }

    public void setEmailIds(String emailIds) {
        this.emailIds = emailIds;
    }

    public long getDurationInSeconds() {
        return durationInSeconds;
    }

    public void setDurationInSeconds(long durationInSeconds) {
        this.durationInSeconds = durationInSeconds;
    }

    public double getChangePercentage() {
        return changePercentage;
    }

    public void setChangePercentage(double changePercentage) {
        this.changePercentage = changePercentage;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    @Override
    public String toString() {
        return "OrgMetaDataProvision{" +
                "org='" + org + '\'' +
                ", emailIds='" + emailIds + '\'' +
                ", durationInSeconds=" + durationInSeconds +
                ", changePercentage=" + changePercentage +
                ", enabled=" + enabled +
                '}';
    }
}
