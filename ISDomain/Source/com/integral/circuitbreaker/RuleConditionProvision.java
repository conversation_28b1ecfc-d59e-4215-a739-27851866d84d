package com.integral.circuitbreaker;

import com.integral.spaces.serialize.DoubleSerializer;
import com.integral.spaces.serialize.EnumSerializer;
import com.integral.spaces.serialize.FieldSerializer;
import com.integral.spaces.serialize.RDSUpdateView;
import org.codehaus.jackson.annotate.JsonProperty;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 10/8/15
 * Time: 2:11 PM
 * To change this template use File | Settings | File Templates.
 */
public class RuleConditionProvision {

    private RuleConditionType ruleConditionType;

    private Double value;

    private String key;

    public RuleConditionType getRuleConditionType() {
        return ruleConditionType;
    }

    public void setRuleConditionType(RuleConditionType ruleConditionType) {
        this.ruleConditionType = ruleConditionType;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    @Override
    public String toString() {
        return "RuleConditionProvision{" +
                "ruleConditionType=" + ruleConditionType +
                ", value=" + value +
                ", key=" + key +
                '}';
    }
}
