package com.integral.circuitbreaker;

import java.util.Iterator;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 4/11/16
 * Time: 4:16 PM
 * To change this template use File | Settings | File Templates.
 */
public class MaxOrderSizeRuleUniqueKeyBuilder implements RuleUniqueKeyBuilder {

    private static final String UNDERSCORE = "_";

    @Override
    public String getKey(Rule rule) {
        if (rule.getTradeType() == TradeType.RFS) {
            Iterator<RuleCondition> iterator = rule.getMetaData().getRuleConditions().iterator();
            if (iterator.hasNext()) {
                RuleCondition ruleCondition = iterator.next();
                return getKey(rule.getType(), rule.getLevel(), ruleCondition.getKey());
            }
        }
        return getKey(rule.getType(), rule.getLevel());
    }

    @Override
    public String getKey(RuleProvision rule) {
        if (rule.getTradeType() == TradeType.RFS) {
            Iterator<RuleConditionProvision> iterator = rule.getMetaData().getRuleConditions().iterator();
            if (iterator.hasNext()) {
                RuleConditionProvision ruleConditionProvision = iterator.next();
                return getKey(rule.getType(), rule.getLevel(), ruleConditionProvision.getKey());
            }
        }
        return getKey(rule.getType(), rule.getLevel());
    }

    public String getKey(Type type, Rule.Level level, String... key) {
        if (key == null || key.length == 0) {
            return level + UNDERSCORE + type;
        } else {
            return level + UNDERSCORE + type + UNDERSCORE + key[0];
        }
    }
}
