package com.integral.circuitbreaker;

import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.exception.IdcException;
import com.integral.is.functor.OrganizationModificationRemoteTransactionFunctor;

import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 10/6/15
 * Time: 1:35 PM
 * To change this template use File | Settings | File Templates.
 */
public class OrgUpdateHandler implements MessageHandler {

    private Log log = LogFactory.getLog(OrgUpdateHandler.class);

    protected static ISMBean isMBean = ISFactory.getInstance().getISMBean();

    public Message handle(Message message) throws IdcException {

        RuleProvisionService ruleProvisionService = ServiceFactory.getRuleProvisionService();
        if (null == ruleProvisionService) {
            log.info("handle():Provision service is not available!");
            return message;
        }

        Map props = message.getMap();
        String notificationType = (String) props.get(OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_KEY_NOTIFICATIONTYPE);

        if (!OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_VALUE_VIRTUALSERVER_UPDATE.equals(notificationType)) {
            return message;
        }

        try {
            String orgName = (String) props.get(OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_KEY_SHORTNAME);
            String newVirtualServer = (String) props.get(OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_KEY_NEW_VIRTUAL_SERVER);

            String vsShortName = isMBean.getVirtualServer().getShortName();
            if (null != newVirtualServer && vsShortName.equalsIgnoreCase(newVirtualServer)) {
                log.info("handle():Received notification for virtual server change for organization:" + orgName + ",message:" + message);
                ruleProvisionService.initRuleProvision(orgName);
                log.info("handle():Updated circuit breaker rules provision for organization:" + orgName + ",message:" + message);
            }
        } catch (Exception e) {
            log.error("handle():Exception occurred during the processing of organization virtual server update notification:" + message, e);
        }

        return message;
    }
}
