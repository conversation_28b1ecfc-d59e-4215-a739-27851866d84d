package com.integral.circuitbreaker;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 4/11/16
 * Time: 4:01 PM
 * To change this template use File | Settings | File Templates.
 */
public class RuleUniqueKeyBuilderFactory {

    private ConcurrentHashMap<Type, RuleUniqueKeyBuilder> strategies = new ConcurrentHashMap<Type, RuleUniqueKeyBuilder>();

    private static class Holder {
        public static final RuleUniqueKeyBuilderFactory INSTANCE = new RuleUniqueKeyBuilderFactory();
    }

    public static RuleUniqueKeyBuilderFactory getFactory() {
        return Holder.INSTANCE;
    }

    /**
     * Strategies are wired to threads on server sart up.
     *
     * @param type
     * @return
     */
    public RuleUniqueKeyBuilder createOrGetBuilder(Type type) {
        RuleUniqueKeyBuilder ruleShortNameBuilder = strategies.get(type);
        if (null != ruleShortNameBuilder) {
            return ruleShortNameBuilder;
        }
        switch (type) {
            case DK:
            case VOLUME:
                DefaultRuleUniqueKeyBuilder builder = new DefaultRuleUniqueKeyBuilder();
                ruleShortNameBuilder = strategies.putIfAbsent(type, builder);
                if (ruleShortNameBuilder == null) {
                    ruleShortNameBuilder = builder;
                }
                return ruleShortNameBuilder;
            case MAX_ORDER_SIZE:
                MaxOrderSizeRuleUniqueKeyBuilder maxOrderSizeRuleUniqueKeyBuilder = new MaxOrderSizeRuleUniqueKeyBuilder();
                ruleShortNameBuilder = strategies.putIfAbsent(type, maxOrderSizeRuleUniqueKeyBuilder);
                if (ruleShortNameBuilder == null) {
                    ruleShortNameBuilder = maxOrderSizeRuleUniqueKeyBuilder;
                }
                return ruleShortNameBuilder;
            default:
                throw new UnsupportedOperationException("Unsupported rule type:" + type);
        }

    }
}
