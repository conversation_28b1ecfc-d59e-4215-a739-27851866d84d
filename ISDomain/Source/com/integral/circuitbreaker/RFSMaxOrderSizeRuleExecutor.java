package com.integral.circuitbreaker;

import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.cache.MDSFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 4/12/16
 * Time: 3:44 PM
 * To change this template use File | Settings | File Templates.
 */
public class RFSMaxOrderSizeRuleExecutor extends RuleExecutor<Request> {

    private static final Log log = LogFactory.getLog(RFSMaxOrderSizeRuleExecutor.class);

    private static final String CIRCUIT_BREAKER_MAX_ORDER_SIZE = "CircuitBreaker.MaxOrderSize";
    private static final String USD = "USD";


    public RFSMaxOrderSizeRuleExecutor(RuleProvisionService service) {
        super(service);
    }


    @Override
    public String getClientOrderId(Request input) {
        return input.getExternalRequestId();
    }

    @Override
    public String getAccount(Request input) {
        return null;
    }

    @Override
    public boolean executeRules(Request input, Object... inOut) {

        boolean isCircuitBreakerExecuted = false;
        long startTime = System.currentTimeMillis();
        try {
            if (!isMBean.isCircuitBreakerEnabled()) {
                return isCircuitBreakerExecuted;
            }

            if (null == service) {
                log.info("executeRules:Rule provision service is not initialized");
                return isCircuitBreakerExecuted;
            }
            Organization organization = input.getOrganization();
            isCircuitBreakerExecuted = executeRuleForOrg(organization, input, inOut);
            if (!isCircuitBreakerExecuted) {
                //Check for sales dealer organization rule
                String orgNameIfSalesDealerOrder = getOrgNameIfSalesDealerOrder(input);
                if (null != orgNameIfSalesDealerOrder) {
                    User userRef = getUserRef(input);
                    if (null != userRef) {
                        isCircuitBreakerExecuted = executeRuleForOrg(userRef.getOrganization(), input, inOut);
                        if(isCircuitBreakerExecuted ){
                            log.info("RFS Max order size circuit breaker was triggered for sales dealer organization rule");
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("executeRules:Failed to execute max order size rules", e);
            isCircuitBreakerExecuted = false;
        }
        log.info("Time Taken for max order size rule execution:" + (System.currentTimeMillis() - startTime) + "(ms)");
        return isCircuitBreakerExecuted;
    }

    private boolean executeRuleForOrg(Organization organization, Request input, Object... inOut) {
        boolean isCircuitBreakerExecuted = false;
        if (null == organization) {
            log.info("executeRules:Unable to find the organization in rfs max order size rule execution for the orderId:" + input.getOrderId());
            return isCircuitBreakerExecuted;
        }
        String rfsType = input.getTradeClassification().getShortName();

        RuleProvision rule = service.getRule(organization.getIndex(), Type.MAX_ORDER_SIZE, Rule.Level.ORG, rfsType);
        if (rule == null || !rule.isEnabled()) {
            return isCircuitBreakerExecuted;
        }
        double maxOrderSizeLimit = getMaxOrderSizeLimit(rule);

        if (Double.MAX_VALUE != maxOrderSizeLimit) {
            String shortName = input.getOrganization().getShortName();
            CurrencyPair ccyPair = input.getCurrencyPair();
            FXLegDealingPrice price;
            double dealtAmountInUSD;
            Iterator<DealingPrice> iterator = input.getRequestPrices().iterator();
            while (iterator.hasNext()) {
                price = (FXLegDealingPrice) iterator.next();
                dealtAmountInUSD = getDealtAmountInUSD(shortName, ccyPair, price);
                isCircuitBreakerExecuted = compareLimits(maxOrderSizeLimit, dealtAmountInUSD, input, price.getDealtCurrencyProperty());
                if (isCircuitBreakerExecuted) {
                    onCircuitBreakerBreach(input, rule, maxOrderSizeLimit, dealtAmountInUSD, inOut);
                    break;
                }
            }
        }
        return isCircuitBreakerExecuted;
    }

    private void onCircuitBreakerBreach(Request input, RuleProvision rule, double maxOrderSizeLimit, double dealtAmountInUSD, Object[] inOut) {
        Object[] params = buildParams(maxOrderSizeLimit, dealtAmountInUSD, inOut);

        updateWorkFlowMessage((WorkflowMessage) params[0]);

        //Unique id but no correlation exist for the same today
        String correlationId = String.valueOf(System.currentTimeMillis());
        for (Integer action : rule.getActions()) {
            executeAction(input,correlationId, Rule.Level.ORG, Type.MAX_ORDER_SIZE, ActionType.getType(action), rule, params);
        }
    }

    private void updateWorkFlowMessage(WorkflowMessage replyMsg) {
        ErrorMessage er = MessageFactory.newErrorMessage();
        er.setErrorCode(ISCommonConstants.REQUEST_VALIDATION_ORDER_AMOUNT_MORETHANMAX);
        replyMsg.addError(er);
    }

    private Object[] buildParams(double maxOrderSizeLimit, double dealtAmountInUSD, Object[] inOut) {
        Object[] params = new Object[3];
        params[0] = inOut[0];
        params[1] = dealtAmountInUSD;
        params[2] = maxOrderSizeLimit;
        return params;
    }

    private boolean compareLimits(double maxOrderSizeLimit, double dealtAmountInUSD, Request input, String dealtCurrencyProperty) {

        if (dealtAmountInUSD > maxOrderSizeLimit) {
            logMessage(maxOrderSizeLimit, input, dealtAmountInUSD, dealtCurrencyProperty);
            return true;
        }
        return false;
    }

    private void logMessage(double maxOrderSizeLimit, Request input, double dealtAmountInUSD, String dealtCurrencyProperty) {
        StringBuilder message = new StringBuilder(500);
        message.append("Max Order Size circuit breaker triggered: RFS Order size in USD exceeds allowed maximum size in USD. OrderId->");
        message.append(getOrderId(input)).append(" External Request Id -  ").append(input.getExternalRequestId());
        message.append(" Order Amount in USD -  ").append(dealtAmountInUSD);
        message.append(" MaxOrderSize in USD -  ").append(maxOrderSizeLimit);
        message.append(" CcyPair - ").append(input.getCurrencyPair());
        message.append(" DealingInTerm -  ").append(FXLegDealingPrice.CCY2.equals(dealtCurrencyProperty));
        log.warn(message.toString());
    }

    protected double getDealtAmountInUSD(String shortName, CurrencyPair ccyPair, FXLegDealingPrice price) {
        Currency usdCurrency = CurrencyFactory.getCurrency(USD);
        return MDSFactory.getInstance().convertAmount(shortName, price.getDealtCurrency(), usdCurrency, price.getDealtAmount(), "RFSMaxOrderSizeRuleExecutor - Max order size check");
    }

    protected Map<String, String> getValues(String correlationId, Request input, Rule.Level level, Object... inOut) {
        Map<String, String> values = super.getValues(correlationId, input, level,  inOut);
        values.put(ORDER_AMOUNT, getFormattedValue((Double) inOut[1]));
        values.put(ORDER_LIMIT, getFormattedValue((Double) inOut[2]));
        String rfsType = input.getTradeClassification().getShortName();
        findAndAddRuleLevel(input, values,Type.MAX_ORDER_SIZE,rfsType);
        return values;
    }

    @Override
    protected String getGMReason(Request input, Object... inOut) {
        return getData(input, inOut);
    }

    private double getMaxOrderSizeLimit(RuleProvision rule) {
        RuleMetaDataProvision metaData = rule.getMetaData();
        if (null != metaData) {
            Set<RuleConditionProvision> ruleConditions = metaData.getRuleConditions();
            if (null != ruleConditions && !ruleConditions.isEmpty()) {
                return ruleConditions.iterator().next().getValue();
            }
        }
        return Double.MAX_VALUE;
    }

    @Override
    protected String getData(Request input, Object[] inOut) {
        return "RFS Max Order Size circuit breaker triggered, Limit in USD:" + getFormattedValue((Double) inOut[2]) + " and the Order Amount in USD:" + getFormattedValue((Double) inOut[1]);
    }

    @Override
    public String getTradeId(Request input) {
        return input.getExternalTradeId();
    }

    @Override
    public String getOrgName(Request input) {
        return input.getOrganization().getName();
    }

    @Override
    public String getUserOrgName(Request input) {
        User user = input.getUser();
        if (null != user) {
            Organization organization = user.getOrganization();
            if (null != organization) {
                return organization.getName();
            }
        }
        return null;
    }

    @Override
    public Integer getOrgIndex(Request input) {
        return input.getOrganization().getIndex();
    }

    @Override
    public String getLegalEntity(Request input) {
        Counterparty counterparty = input.getCounterparty();
        if (null != counterparty) {
            return counterparty.getShortName();
        }
        return null;
    }

    @Override
    public String getUser(Request input) {
        User user = input.getUser();
        if (null != user) {
            return user.getShortName();
        }
        return null;
    }

    @Override
    public String getOrderId(Request input) {
        return input.getTransactionID();
    }

    @Override
    public Organization getOrganization(Request input) {
        return input.getOrganization();
    }

    @Override
    public User getUserRef(Request input) {
        return input.getUser();
    }

    @Override
    public String getEventType() {
        return CIRCUIT_BREAKER_MAX_ORDER_SIZE;
    }

}

