package com.integral.circuitbreaker;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 4/11/16
 * Time: 4:02 PM
 * To change this template use File | Settings | File Templates.
 */
public class DefaultRuleUniqueKeyBuilder implements RuleUniqueKeyBuilder {

    private static final String UNDERSCORE = "_";

    @Override
    public String getKey(Rule rule) {
        return getKey(rule.getType(),rule.getLevel());
    }

    @Override
    public String getKey(RuleProvision rule) {
        return getKey(rule.getType(),rule.getLevel());
    }

    public String getKey(Type type, Rule.Level level,String... key) {
        return level + UNDERSCORE + type;
    }
}
