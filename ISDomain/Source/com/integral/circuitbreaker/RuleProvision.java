package com.integral.circuitbreaker;

import java.util.HashSet;
import java.util.Set;

/**
 * Created with IntelliJ IDEA.
 * User: raghunathans
 * Date: 9/30/15
 * Time: 5:13 PM
 * To change this template use File | Settings | File Templates.
 */
public class RuleProvision {
    private String org;
    private Type type;
    private Rule.Level level;
    private TradeType tradeType;
    private Set<Integer> actions= new HashSet<Integer>();
    private RuleMetaDataProvision metaData;
    private boolean enabled;

    public void setOrg(String org) {
        this.org =org;
    }

    public String getOrg() {
        return org;
    }

    public void setType(Type type) {
        this.type = type;
    }

    public Type getType() {
        return type;
    }

    public void setLevel(Rule.Level level) {
        this.level = level;
    }

    public Rule.Level getLevel() {
        return level;
    }

    public void setActions(Set<Integer> actions) {
        this.actions = actions;
    }

    public Set<Integer> getActions() {
        return actions;
    }

    public void setMetaData(RuleMetaDataProvision metaData) {
        this.metaData = metaData;
    }

    public RuleMetaDataProvision getMetaData() {
        return metaData;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public TradeType getTradeType() {
        return tradeType;
    }

    public void setTradeType(TradeType tradeType) {
        this.tradeType = tradeType;
    }

    @Override
    public String toString() {
        return "RuleProvision{" +
                "org='" + org + '\'' +
                ", type=" + type +
                ", level=" + level +
                ", tradeType=" + tradeType +
                ", actions=" + actions +
                ", metaData=" + metaData +
                ", enabled=" + enabled +
                '}';
    }
}
