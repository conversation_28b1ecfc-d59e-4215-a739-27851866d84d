package com.integral.metrics;

import java.util.Arrays;

public class Counter
{
	long min,max,avg;
	
	long totalCounted;
	
	//For calculating average
	private long sum;
	
	/* Buckets of delays - First bucket up to 200us.    
	 * 200,400 and so on, tenth bucket will hold 
	 * */
	long[] buckets = new long[10];
	
	String name;
	
	public Counter(String name)
	{
		this.name = name;
	}
		
	public void record(long nanos)
	{
		totalCounted++;
		sum = sum + nanos;
		avg = sum/totalCounted;
		if( nanos < min || min == 0)
		{
			min = nanos;
		}
		if( nanos > max )
		{
			max = nanos;
		}
		int i = (int)(nanos/(200*1000));
		if( i > 9 )
			buckets[9]++;
		else
			buckets[i]++;
	}
	
	public void reset()
	{
		min = 0; max = 0; avg = 0; totalCounted = 0; sum = 0;
		buckets = new long[10];
	}

	@Override
	public String toString()
	{
		return "Counter [name=" + name + ", min=" + min + ", max=" + max + ", avg=" + avg + ", totalCounted=" + totalCounted + ", buckets(200us)=" + Arrays.toString(buckets) + "]";
	}
	
	public static void main( String[] args )
	{
		Counter c = new Counter("test");
		c.record(200*1000);
		c.record(200*1000);
		c.record(200*1000);
		c.record(200*1000);
		c.record(200*1000);
		
		c.record(200*1000);
		c.record(1000*1000);
		c.record(100*1000);
		c.record(300*1000);
		long l = 4000000l*1000l;
		//c.record(l);
		System.out.println(c);
	}

}
