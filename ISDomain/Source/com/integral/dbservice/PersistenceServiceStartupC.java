package com.integral.dbservice;

import com.integral.dbservice.is.ISPersistenceServiceCacheManager;
import com.integral.dbservice.txlog.RolloverTask;
import com.integral.dbservice.txlog.SyncTask;
import com.integral.dbservice.txlog.TransactionLog.State;
import com.integral.facade.EventTimeFacade;
import com.integral.facade.EventTimeFacadeC;
import com.integral.facade.FacadeFactory;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.QuoteC;
import com.integral.finance.dealing.RequestC;
import com.integral.finance.dealing.facade.QuoteFacade;
import com.integral.finance.dealing.facade.QuoteFacadeC;
import com.integral.finance.dealing.facade.RequestPriceFacade;
import com.integral.finance.dealing.fx.facade.FXSingleLegRequestPriceFacadeC;
import com.integral.finance.dealing.priceRegeneration.PriceRegenerationParameters;
import com.integral.finance.dealing.priceRegeneration.PriceRegenerationParametersFacade;
import com.integral.finance.dealing.priceRegeneration.PriceRegenerationParametersFacadeC;
import com.integral.finance.marketData.fx.FXMarketDataElementC;
import com.integral.fx.dealing.quote.facade.QuoteTransactionIdFacadeC;
import com.integral.is.audit.ISLegalAgreementAuditEventFacadeC;
import com.integral.is.common.cache.ISTradeCache;
import com.integral.is.common.facade.ISRequestTransactionIdFacadeC;
import com.integral.is.common.facade.ISTradeTransactionIdFacadeC;
import com.integral.is.common.facade.RFSWorkflowFacade;
import com.integral.is.common.facade.RFSWorkflowFacadeC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.is.common.pool.ThreadPoolFactory;
import com.integral.is.management.facade.ISQuoteEventTimeFacadeC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.log.LogLevel;
import com.integral.persistence.TransactionIdFacadeC;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.runtime.StartupTask;
import com.integral.system.server.VirtualServer;
import com.integral.user.UserC;

import java.io.IOException;
import java.util.Hashtable;
import java.util.concurrent.TimeUnit;

public abstract class PersistenceServiceStartupC implements StartupTask
{

    protected DBService dbService;
    protected static final Log log = LogFactory.getLog( PersistenceServiceStartupC.class );

    public String startup( String aName, Hashtable htArgs )
    {
        log.setLevel( LogLevel.INFO );
        try
        {
            log.info( "Registering Entity Facades." );
            registerEntityFacades();
            if (!ConfigurationFactory.getServerMBean().isIntegralSpacesEnabled() || ConfigurationFactory.getServerMBean().isIntegralRFSSpacesEnabled()) {
                log.info( "Initializing persitence service." );
                initializePeristenceService();
                postInitializePropcessing();
                log.info( "Started Persistence service successfully." );
            }
        }
        catch ( IOException e )
        {
            log.error( "Failed to initialized ISPersistenceService due to: " + e.getMessage(), e );
            throw new RuntimeException( "Exception during PersistenceService initialization.", e );
        }
        catch ( Exception e )
        {
            log.error( "Failed to initialized ISPersistenceService due to: " + e.getMessage(), e );
            throw new RuntimeException( "Exception during PersistenceService initialization.", e );
        }
        
        return null;
    }

    protected void registerEntityFacades()
    {
        FacadeFactory.setFacade( EventTimeFacade.NAME, FXMarketDataElementC.class, EventTimeFacadeC.class );
        FacadeFactory.setFacade( EventTimeFacade.NAME, QuoteC.class, ISQuoteEventTimeFacadeC.class );
        FacadeFactory.setFacade( RFSWorkflowFacade.NAME, RequestC.class, RFSWorkflowFacadeC.class );
        FacadeFactory.setFacade( RequestPriceFacade.FACADE_NAME, DealingPrice.class, FXSingleLegRequestPriceFacadeC.class );
        FacadeFactory.setFacade( EventTimeFacade.NAME, RequestC.class, EventTimeFacadeC.class );
        FacadeFactory.setFacade( TransactionIdFacadeC.FACADE_NAME, com.integral.finance.dealing.Request.class, ISRequestTransactionIdFacadeC.class );
        FacadeFactory.setFacade( TransactionIdFacadeC.FACADE_NAME, com.integral.finance.dealing.Quote.class, QuoteTransactionIdFacadeC.class );
        FacadeFactory.setFacade( TransactionIdFacadeC.FACADE_NAME, com.integral.finance.trade.Trade.class, ISTradeTransactionIdFacadeC.class );
        FacadeFactory.setFacade( PriceRegenerationParametersFacade.FACADE_NAME, PriceRegenerationParameters.class, PriceRegenerationParametersFacadeC.class );
        //Price facades for rate filters
        FacadeFactory.setFacade( QuoteFacade.class.getName(), Quote.class, QuoteFacadeC.class );
        FacadeFactory.setFacade(ISLegalAgreementAuditEventFacadeC.IS_LEGALAGREEMENT_AUDIT_FACADE, UserC.class, ISLegalAgreementAuditEventFacadeC.class);
    }

    protected void postInitializePropcessing()
    {
        ISPersistenceMBean mbean = dbService.getConfig();

        //MetricsManager.instance().setTimerInterval(30000);
        log.info( "Start Metrics Manager." );
        MetricsManager.instance().start();

        if ( mbean.isTXLogFileWriterTaskEnabled() )
        {
            long interval = mbean.getTxLogRolloverIntervalInSeconds();
            log.info( "Setting Rollover interval as: " + interval + " seconds." );
            ThreadPoolFactory.getInstance().initializeScheduledThreadPool();
            ThreadPoolFactory.getInstance().initializeDBServiceScheduledThreadPool();
            ThreadPoolFactory.getInstance().getDbServiceScheduledExecutor().scheduleAtFixedRate( new RolloverTask( dbService ), interval, interval, TimeUnit.SECONDS );

            interval = mbean.getTxLogSyncIntervalInSeconds();
            log.info( "Setting Sync interval as: " + interval + " seconds." );
            ThreadPoolFactory.getInstance().getDbServiceScheduledExecutor().scheduleAtFixedRate( new SyncTask( dbService ), interval, interval, TimeUnit.SECONDS );

            //check if the transaction server is not processing any task
            while(true){
                TXNServerState state;
                String peerVirtualServer = ConfigurationFactory.getServerMBean().getPeerVirtualServerName();
                VirtualServer vs = ISFactory.getInstance().getISMBean().getVirtualServer();
                VirtualServer txnServerVs = vs.getRemoteTxnServer();
                if(("".equals( mbean.getTXNServerUrl()) && txnServerVs == null) || mbean.getTXNServerUrl().contains( "localhost" )){
                    //if local server
                    state = TXNServerStateHolderC.getInstance(peerVirtualServer).getState();
                }
                else if(!("".equals( mbean.getTXNServerUrl()) || mbean.getTXNServerUrl().contains( "localhost" ))){
                    //if remote server 
                    state = TXNServerStateHolderC.getInstance(peerVirtualServer).getRemoteTXNServerState();
                }else{
                    //remote server with no shared transaction logs.
                    state = TXNServerStateHolderC.getInstance(peerVirtualServer).getState(); //return local state as readers run locally.
                }
                
                if(state != null && state.getReaderTaskState() == State.STARTED){
                    // reader tasks are fully started 
                    log.warn( "TXN Server state : STARTED and reader tasks are ready to process new Transaction logs. Hence exiting from initialize state." );
                    break;
                } else if(mbean.isReplayModeEnabled()){
                    log.warn( "TXN Server state :"+ (state==null?"null":state.getReaderTaskState()) + ". Since Replay mode is enabled, hence exiting from initialize state." );
                    break;
                }
                else{
                   try{
                       log.warn( "######Waiting ... Either TXN Server is not up or not ready to accept new events. Hence sleeping for 1 sec to retry.. " );
                       Thread.sleep( 1000 );
                   } 
                   catch(Exception e){
                       
                   }
                }
            }

            ISTradeCache.__init_at_startup();
        }

        if (mbean.isTXQueueConsumerTaskEnabled()) {
            ISPersistenceServiceCacheManager.__init_at_startup();
        }
        
    }

    protected abstract void initializePeristenceService() throws Exception;

}
