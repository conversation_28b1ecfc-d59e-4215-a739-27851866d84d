package com.integral.dbservice;

import com.integral.cache.distributed.Message;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mtf.message.MTFEventMessage;
import com.integral.pipeline.PipelineMessage;
import com.integral.spaces.ApplicationSpaceEvent;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Map;

public class TransactionLogMessage extends PipelineMessage {

    public static final String TLM_EVENT_MESSAGE_READ = "MessageRead";
    public static final String TLM_EVENT_REMOTE_SENT = "RemoteSent";
    public static final String TLM_EVENT_MESSAGE_QUEUED = "MessageQueued";

    private static final byte[] PIPE = "|".getBytes();
    public static final char PIPE_SEPARATOR ='|';
    private static final Log log = LogFactory.getLog(TransactionLogMessage.class);

	// The transaction log file in which this message exists.
	public String fileName;

	// Location in the file where this message exists.
	public long seekPtr;

	// Serially increasing transactionId.
	public long transactionId;

	// The entity that was persisted.
	public Object entity = null;

	// String representation of the entity.
	public StringBuffer entityText = null;

	// Unit of Work, if acquired. May not be available/used by all processors in
	// a pipeline.
	public UnitOfWork uow = null;

    public ByteBufferWriter entityAsBytes = null;

    public Map<String,String> properties = null;

    public long creationTime;

    // This flag is set only for the first event that is passed through the pipeline on startup.
    public boolean isWarmupEvent = false;

    public Message message = null;

    public MTFEventMessage mtfEventMessage;

    // VS is used in case the log messages are sent to remote transaction server for processing.
    public String virtualServer;

    //index is used in case the log messages are sent to remote transaction server for processing.
    public int index;

    //session id is used in case the log messages are sent to remote transaction server for processing.
    //every time sync takes place, a new session id is defined to safeguard against pending delayed messages that may show up out of order.
    public String sessionId;

    public ArrayList<ApplicationSpaceEvent> deals = new ArrayList<ApplicationSpaceEvent>(4);

    public ArrayList<ApplicationSpaceEvent> orders = new ArrayList<ApplicationSpaceEvent>(2);


	/**
	 * Get an index between 0 and maxIndex-1 which is based on corelationId.
	 * maxIndex typically refers to the concurrency level of the system.
	 *
	 * @param maxIndex
	 * @return
	 */
	public int getCorelationIndex(int maxIndex) {
		if (corelationId == null) {
			throw new NullPointerException("NULL corelationId");
		}
		int hashCode = corelationId.hashCode();
		return Math.abs(hashCode) % maxIndex;
	}

    public void writeHeaders( ByteBuffer target )
    {

        String txIdStr = Long.toString( transactionId );

        int bodySize =  entityAsBytes.getWrittenCharSize();
        // The capacity of sourceBytes is the length of the byte[] array that it
        // wraps.
        String sizeStr = Integer.toString( bodySize );

        String creationTime = Long.toString( this.creationTime ) ;

        // txId | corelationId | event | creationTime | sizeOfSerializedData | seekPtr |
        target.put( txIdStr.getBytes() ).put( PIPE ).put( getCorelationId().getBytes() ).put( PIPE ).
                put( getEventName().getBytes() ).put( PIPE ).put( creationTime.getBytes() ).put( PIPE ).
                put(sizeStr.getBytes() ).put( PIPE ).put(Long.toString(seekPtr).getBytes()).put(PIPE);
    }

    public String serializeToString(){
        StringBuilder serialized = new StringBuilder();
        serialized.append(transactionId).append(PIPE_SEPARATOR).append(corelationId).append(PIPE_SEPARATOR).
                append(fileName).append(PIPE_SEPARATOR).append(seekPtr).append(PIPE_SEPARATOR).
                append(eventName).append(PIPE_SEPARATOR).append(creationTime).append(PIPE_SEPARATOR).
                append(virtualServer).append(PIPE_SEPARATOR).append(index).append(PIPE_SEPARATOR).
                append(sessionId).append(PIPE_SEPARATOR);
        eventTimesString(serialized);
                serialized.append(PIPE_SEPARATOR).append(entityText.length()).append(PIPE_SEPARATOR).
                append(entityText);
        return serialized.toString();
    }

    public static TransactionLogMessage deserializeFromString(String serialized) {
        TransactionLogMessage result = new TransactionLogMessage();
        int next = 0;
        int last = next;
        SerializedState state = SerializedState.transactionId;
        int entitySize = 0;
        String token;
        while (true) {
            if (state == SerializedState.entityText) {
                token = serialized.substring(last);
                if (!((token.length() == (entitySize - 2)) || token.length() == entitySize)) { //"\r and \n characters at the end are getting truncated over the protocol in some environments.
                    log.error("Error: Expected Length:" + entitySize + " ActualLength:" + token.length() + " for entity received:" + token);
                }
            } else {
                next = serialized.indexOf(PIPE_SEPARATOR, last);
                if (next == -1) {
                    return null;
                } else {
                    token = serialized.substring(last, next);
                }
            }
            if ("null".equals(token)) {
                token = null;
            }
            switch (state) {
                case transactionId:
                    result.transactionId = Long.parseLong(token);
                    state = SerializedState.corelationId;
                    break;
                case corelationId:
                    result.corelationId = token;
                    state = SerializedState.fileName;
                    break;
                case fileName:
                    result.fileName = token;
                    state = SerializedState.seekPtr;
                    break;
                case seekPtr:
                    result.seekPtr = Long.parseLong(token);
                    state = SerializedState.eventName;
                    break;
                case eventName:
                    result.eventName = token;
                    state = SerializedState.creationTime;
                    break;
                case creationTime:
                    result.creationTime = Long.parseLong(token);
                    state = SerializedState.virtualServer;
                    break;
                case virtualServer:
                    result.virtualServer = token;
                    state = SerializedState.index;
                    break;
                case index:
                    result.index = Integer.parseInt(token);
                    state = SerializedState.sessionId;
                    break;
                case sessionId:
                    result.sessionId = token;
                    state = SerializedState.eventTimes;
                    break;
                case eventTimes:
                    result.setEventTimes(token);
                    state = SerializedState.entitySize;
                    break;
                case entitySize:
                    entitySize = Integer.parseInt(token);
                    state = SerializedState.entityText;
                    break;
                case entityText:
                    result.entityText = new StringBuffer(token);
                    return result;
            }
            last = next + 1;
        }
    }

    private enum SerializedState {
        transactionId, corelationId, fileName, seekPtr, eventName, creationTime, virtualServer, index, sessionId, eventTimes, entitySize, entityText
    }

    TransactionLogMessage getDuplicateMessage(){
        TransactionLogMessage result = new TransactionLogMessage();
        result.transactionId = transactionId;
        result.corelationId = corelationId;
        result.fileName = fileName;
        result.seekPtr = seekPtr;
        result.eventName = eventName;
        result.creationTime = creationTime;
        result.virtualServer = virtualServer;
        result.index = index;
        result.sessionId = sessionId;
        result.addEventTimes(eventTimes);
        result.entityText = entityText;
        result.entity = entity;
        return result;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder(super.toString());
        sb.append("TransactionLogMessage");
        sb.append("{fileName='").append(fileName).append('\'');
        sb.append(", transactionId=").append(transactionId);
        sb.append(", virtualServer='").append(virtualServer).append('\'');
        sb.append(", index=").append(index);
        sb.append(", sessionId=").append(sessionId);
        sb.append('}');
        return sb.toString();
    }
}
