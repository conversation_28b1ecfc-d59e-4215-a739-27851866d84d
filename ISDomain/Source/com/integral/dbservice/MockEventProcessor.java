package com.integral.dbservice;

import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;

/**
* Created by IntelliJ IDEA.
* User: anejaa
* Date: Nov 18, 2010
* Time: 12:01:28 PM
* A mock processor that throws exception on process.
*/
public class MockEventProcessor extends Processor {

    @Override
    public PipelineMessage process(PipelineMessage msg) {
        throw new PipelineException("Mock Processor Called. No implementation Available.");
    }
}
