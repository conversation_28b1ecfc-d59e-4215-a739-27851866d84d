package com.integral.dbservice;

import java.nio.ByteBuffer;

import com.integral.persistence.NotificationEntity;
import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.spaces.notification.Notification;
import com.integral.spaces.notification.NotificationConfiguration;
import com.integral.spaces.notification.NotificationManager;
import com.integral.spaces.notification.NotificationPerformanceMetrics;

/**
 * Processor to process the received notifications from the DBService and invoke
 * all the notification handlers.
 * 
 * <AUTHOR>
 * 
 */
public class DBPersistNotificationPipelineProcessor extends Processor {

	public static String EVENT = "createNotificationEntity";
	public static int INDEX = 10002;
	private final DBService dbService;
	private final int numLogFiles;

	public DBPersistNotificationPipelineProcessor(DBService dbService) {
		this.dbService = dbService;
		numLogFiles = dbService.getConfig().getNumPersistenceThreads();
	}

	@Override
	public PipelineMessage process(PipelineMessage msg) {
		NotificationEntity entity = null;
		try {
			TransactionLogMessage tlm = (TransactionLogMessage) msg;
			Object obj = tlm.entity;
			if ( obj instanceof NotificationEntity )
			{
				entity = ( NotificationEntity ) obj;
				Notification<ByteBuffer> notification = entity.deSerializeNotification ();

				if ( notification == null )
				{
					throw new NullPointerException (
							"Failed to deserialize received notification" );
				}

				NotificationPerformanceMetrics performanceMetrics = notification
						.getPerformanceMetrics ();
				performanceMetrics.setHeader ( notification );
				performanceMetrics.setTransportEndTime ( System.currentTimeMillis () );
				performanceMetrics.setChannelNumHandlerGroupName ( entity
						.getQueueName (), NotificationConfiguration.getInstance ()
						.getQueueSetNameBase () );

				// set the handler group
				notification.setHandlerGroup ( entity.getHandlerGroup () );

				log.info ( "Processing notification received on Queue:"
						+ entity.getQueueName () + ":notification:" + notification );

				NotificationManager.getInstance ().packageHandle ( notification );

				// Update the meta information
				updateMetaInformation ( tlm );
			}

		} catch (Exception e) {
			log.error(
					"Processing notification received on Queue:"
							+ entity.getQueueName(), e);
			throw new PipelineException(e);
		}
		return msg;
	}

	private void updateMetaInformation(TransactionLogMessage tlm) {
		int txLogIndex = tlm.getCorelationIndex(numLogFiles);		
		dbService.getTransactionLog().updateMetaInformation(txLogIndex, tlm);
	}

}
