package com.integral.dbservice.spaces;

import com.integral.dbservice.TransactionLogMessage;
import com.integral.dbservice.is.ISPersistenceServiceCacheManager;
import com.integral.dbservice.txlog.TransactionLog;
import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.Order;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.PersistenceMBean;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import com.integral.spaces.ApplicationSpaceEvent;
import com.integral.system.configuration.ConfigurationFactory;

import java.util.List;

/**
 * Special EndTransactionProcessor for persisting spaces entities.
 */
public class SpacesEndTransactionProcessor extends Processor {

    TransactionLog txLogSystem;
    static int BATCHSIZE=4;
    private static PersistenceMBean persistenceMBean = PersistenceFactory.getPersistenceMBean();

    public SpacesEndTransactionProcessor(TransactionLog txLogSystem) {
        super();
        this.txLogSystem = txLogSystem;
    }

    @Override
    public PipelineMessage process(PipelineMessage msg) {
        TransactionLogMessage tlm = (TransactionLogMessage) msg;
        try {
            // Persist spaces Entities
            ISPersistenceServiceCacheManager cache= null;
            if(tlm.deals.size() != 0) {
                log.info("Persisting deals to spaces. CorelationId=" + msg.getCorelationId() +  ", event=" + msg.getEventName());
                // tlm.virtual server is null on local servers.
                String vs = tlm.virtualServer == null ? ConfigurationFactory.getServerMBean().getPeerVirtualServerName() : tlm.virtualServer;
                cache = ISPersistenceServiceCacheManager.instance(vs);
                for(ApplicationSpaceEvent ase : tlm.deals)
                {
                    if(ase.isCacheable()){
                        Deal deal = (Deal)ase.getEntity();
                        cache.addSpacesDeal( deal);
                    }
                }
                if (tlm.deals.size() > BATCHSIZE) {
                    //split into chunk of 4 and persist each chunk separately.
                    for (int i = 0; i < tlm.deals.size(); i += BATCHSIZE) {
                        List<ApplicationSpaceEvent> deals = tlm.deals.subList(i, i + Math.min(BATCHSIZE, tlm.deals.size() - i));
                        if (persistenceMBean.isCDQSpacesPersistenceSynchronous())
                            PersistenceServiceFactory.getCDQPersistenceService().syncronousPersist(deals, msg.getCorelationId(), false, msg.getEventName());
                        else
                            PersistenceServiceFactory.getCDQPersistenceService().persist(deals, msg.getCorelationId(), false, msg.getEventName());
                    }
                } else {
                    if (persistenceMBean.isCDQSpacesPersistenceSynchronous())
                        PersistenceServiceFactory.getCDQPersistenceService().syncronousPersist(tlm.deals, msg.getCorelationId(), false, msg.getEventName());
                    else
                        PersistenceServiceFactory.getCDQPersistenceService().persist(tlm.deals, msg.getCorelationId(), false, msg.getEventName());
                }
            }
        } catch ( Exception exl ) {
            // Do not throw pipeline exception here.
            log.error("Tenant:"+ txLogSystem.getDbService().getPeerVirtualServer()+ " .ETP : Execption while pesisting data to spaces" + exl );
            exl.printStackTrace();
        }

        return msg;
    }

}
