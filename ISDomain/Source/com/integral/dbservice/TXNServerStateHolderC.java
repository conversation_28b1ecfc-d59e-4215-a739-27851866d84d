package com.integral.dbservice;

import java.io.BufferedReader;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.codehaus.jackson.annotate.JsonWriteNullProperties;
import org.codehaus.jackson.map.ObjectMapper;
import com.integral.is.common.mbean.ISFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;

//matintains the state of TXNServer and it should be use globally to get the state. 
@JsonWriteNullProperties(false)
public class TXNServerStateHolderC
{
    //state per tenant
    private static final Map<String,TXNServerStateHolderC> instances = new ConcurrentHashMap<String,TXNServerStateHolderC>();
    private TXNServerState state = new TXNServerStateC();
    private static Log log = LogFactory.getLog(TXNServerStateHolderC.class);
    private final String tenant;

    private TXNServerStateHolderC(String tenant)
    {
        this.tenant = tenant;
    }

    public static TXNServerStateHolderC getInstance(String peerVirtualServer)
    {
        TXNServerStateHolderC result = instances.get(peerVirtualServer);
        if(result == null){
            synchronized (TXNServerStateHolderC.class){
                result = instances.get(peerVirtualServer);
                if(result == null){
                    result = new TXNServerStateHolderC(peerVirtualServer);
                    instances.put(peerVirtualServer,result);
                }
            }
        }
        return result;
    }

    public TXNServerState getState()
    {
        return state;
    }

    //returns remote TXN Server state mentioned in 
    public TXNServerState getRemoteTXNServerState()
    {
        TXNServerState remoteState;
        ObjectMapper mapper = new ObjectMapper();
        String txnServerUrl = ISFactory.getInstance().getPersistenceMBean().getTXNServerUrl();
        URL url;
        URLConnection urlConn;
        DataOutputStream output;
        BufferedReader input;
        String connectStr = "";
        if ( txnServerUrl == null || "".equals( txnServerUrl ) )
        {
            return null;
        }
        else
        {
            try
            {
                connectStr = new StringBuffer( txnServerUrl ).append( "/devapp/state/TXNServerState.jsp?tenant="+tenant ).toString();
                url = new URL( connectStr );
                urlConn = url.openConnection();
                urlConn.setDoInput( true );
                urlConn.setDoOutput( true );
                urlConn.setUseCaches( false );
                urlConn.setConnectTimeout( 10000 );
                urlConn.setReadTimeout( 10000 );
                output = new DataOutputStream( urlConn.getOutputStream() );
                output.flush();
                output.close();
                DataInputStream in = new DataInputStream( urlConn.getInputStream() );
                input = new BufferedReader( new InputStreamReader( in ) );
                String str;
                while ( (str = input.readLine()) != null )
                {
                    if ( str.trim().length() != 0 )
                    {
                        remoteState = mapper.readValue( str.trim(), TXNServerStateC.class );
                        input.close();
                        return remoteState;
                    }
                }
                input.close();
            }
            catch ( MalformedURLException e )
            {
                log.error( "Get Remote Server state failure. Root Cause : Connection URL" + connectStr + " not proper." );
            }
            catch ( java.net.ConnectException e1 )
            {
                log.error( "Get Remote Server state failure. Root Cause : Connection " + connectStr + " Failed. Request TimeOut." );
            }
            catch ( Exception e2 )
            {
                log.error( "Get remote server state failure. Connection " + connectStr + " Failed. Root Cause : " );
            }
            
            log.error( "Unable to get proper value from specified URL. Please check the url mentioned.");
            return null;
        }
    }
}
