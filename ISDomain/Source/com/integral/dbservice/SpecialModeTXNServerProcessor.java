package com.integral.dbservice;

import com.integral.dbservice.txlog.TransactionLog;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;

/**
 * The objective of this processor is to run server in special testing mode where dbservice is shutdown and restarted on every event. * 
 *
 */
public class SpecialModeTXNServerProcessor extends Processor
{
    TransactionLog txLogSystem;

    public SpecialModeTXNServerProcessor( TransactionLog txLogSystem )
    {
        super();
        this.txLogSystem = txLogSystem;
    }

    @Override
    public PipelineMessage process( PipelineMessage request )
    {
        log.warn( "#### Stopping DBService." );
        txLogSystem.getDbService().stopImmediate();
        
        new Thread(new SpecialModeTXNServerRunner()).start();
        return request;
    }

    private class SpecialModeTXNServerRunner implements Runnable
    {

        public void run()
        {
        // TODO Auto-generated method stub
            //stop dbservice
            try
            {
                log.warn( "### Starting DB Service." );
                txLogSystem.getDbService().setup();
            }
            catch ( Exception e )
            {
                log.error( "Error in dbService setup.", e );
            }
        }

    }
}
