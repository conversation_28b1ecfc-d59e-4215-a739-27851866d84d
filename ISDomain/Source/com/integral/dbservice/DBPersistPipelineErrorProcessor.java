package com.integral.dbservice;

import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.PipelineSetupFunctor;
import com.integral.pipeline.Processor;
import com.integral.pipeline.strategy.SyncPipelineController;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Sep 20, 2010
 * Time: 5:30:11 PM
 * This processor handles the errors in the db persist pipeline.
 */
public class DBPersistPipelineErrorProcessor extends Processor {

    public static final String NEW_LINE = new String("\r\n");
    private final DBService dbService;
    private final SyncPipelineController serializeAndSavePipeline;
    private final int numLogFiles;

    public DBPersistPipelineErrorProcessor(DBService dbService) {
        this(dbService, new SerializeAndSavePipelineSetupFunctor(dbService));        
    }

    public DBPersistPipelineErrorProcessor(DBService dbService, PipelineSetupFunctor serializeAndSavePipelineSetupFunctor) {
        super();
        this.dbService = dbService;
        serializeAndSavePipeline = new SyncPipelineController(serializeAndSavePipelineSetupFunctor);
        numLogFiles = this.dbService.getConfig().getNumPersistenceThreads();
    }

    @Override
    public PipelineMessage process(PipelineMessage msg) {
        TransactionLogMessage tlm = (TransactionLogMessage) msg;
        try {
            IdcTransaction tx = IdcSessionManager.getInstance().getTransaction();

            log.warn("ErrorProcessor: Last processor could not process PipelineMessage successfully. Releasing transaction and committing MetaFile in separate txn ");
            if (tx != null) {
                tx.release();
            }

            TransactionLogMessage cloneMsg = tlm.getDuplicateMessage();
            //Copy tlm to error log.
            if (tlm.entityText == null) {
                //need to serialize the message before saving.
                serializeAndSavePipeline.process(cloneMsg);
            } else {
                ByteBufferWriter wri = new ByteBufferWriter(1024 * 8);
                cloneMsg.entityAsBytes = wri;
                String str = tlm.entityText.toString();
                wri.write(str);
                if (!str.endsWith(NEW_LINE)) {
                    //This happens only in case of multi-tenant Txn server where tlm is deserialized from http payload.
                    wri.write(NEW_LINE);
                }
                dbService.getTransactionLog().writeErrorMessage(cloneMsg);
                //tlm.entityText = null; //set the text to null as it is written to the error log.
            }
            msg.clearErrors(); //clear the existing errors as errors are handled by this processor.
            return msg;
        } catch (Exception ex) {
            throw new PipelineException("Unable to copy tlm to error log.", ex);
        } finally {
            updateMetaInformation(tlm); // update META file and move ahead.
        }
    }

    private void updateMetaInformation(TransactionLogMessage tlm) {
        int txLogIndex = tlm.getCorelationIndex(numLogFiles);        
        dbService.getTransactionLog().updateMetaInformation(txLogIndex, tlm);
    }
}
