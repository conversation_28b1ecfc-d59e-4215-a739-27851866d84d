package com.integral.dbservice;

import com.integral.dbservice.is.ISPersistenceService;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.runtime.ShutdownTask;

import java.util.Hashtable;

/**
 * <AUTHOR>
 */
public class DBServiceShutdownTask implements ShutdownTask {
    private static Log log = LogFactory.getLog( DBServiceShutdownTask.class );

    public String shutdown(String aName, Hashtable args) {
        return shutdown();
    }

    public String shutdown() {
        log.info("Shutting down DBService.");
        ISPersistenceService persistenceService = ISPersistenceService.instance();
        if(persistenceService == null){
            log.warn( "DBService is not initialized for this server. Skipping DBService.shutdown() call." );
        }
        else{
            persistenceService.getDBServiceInstance().stop();
        }
        return "DBService has been shutdown.";
    }
}
