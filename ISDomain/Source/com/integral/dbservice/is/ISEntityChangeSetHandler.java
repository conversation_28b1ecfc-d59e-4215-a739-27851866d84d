package com.integral.dbservice.is;

import com.integral.facade.EventTimeFacade;
import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditUtilizationEvent;
import com.integral.finance.dealing.*;
import com.integral.finance.dealing.facade.RequestStateFacade;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXDealingPriceElement;
import com.integral.finance.dealing.fx.FXDealingPriceElementDependentC;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.*;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceC;
import com.integral.finance.trade.CptyTrade;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.ISTransactionManager;
import com.integral.is.management.facade.ISQuoteEventTimeFacadeC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.ExternalSystemId;
import com.integral.scheduler.Expiration;
import com.integral.scheduler.ExpirationC;
import com.integral.user.Organization;
import com.integral.workflow.State;
import com.integral.workflow.WorkflowStateMap;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Class to update the change set from one Enity to another
 */
public class ISEntityChangeSetHandler
{
	private Log log = LogFactory.getLog(ISEntityChangeSetHandler.class);

	private static ISEntityChangeSetHandler instance = null;

	static
	{
		ISEntityChangeSetHandler.instance = new ISEntityChangeSetHandler();
	}

	public static ISEntityChangeSetHandler instance()
	{
		return instance;
	}

	/**
	 * Updates data from the deserialized clonedRequest to the
	 * request
	 *
	 * @param clonedRequest
	 * @param request
	 */
	public void updateRequest( Request clonedRequest, Request request )
	{
		if ( clonedRequest.getSentDate() != null )
		{
			request.setSentDate(clonedRequest.getSentDate());
		}
		if ( clonedRequest.getAcceptedDate() != null )
		{
			request.setAcceptedDate(clonedRequest.getAcceptedDate());
		}
		if ( clonedRequest.getEffectiveDate() != null )
		{
			request.setEffectiveDate(clonedRequest.getEffectiveDate());
		}
		if ( clonedRequest.getSubmissionTime() != null )
		{
			request.setSubmissionTime(clonedRequest.getSubmissionTime());
		}
		if ( clonedRequest.getCancellationTime() != null )
		{
			request.setCancellationTime(clonedRequest.getCancellationTime());
		}
		if ( clonedRequest.getExpirationTime() != null )
		{
			request.setExpirationTime(clonedRequest.getExpirationTime());
		}
		if ( clonedRequest.getMarketSnapshot() != null )
		{
			request.setMarketSnapshot(clonedRequest.getMarketSnapshot());
		}
		if ( clonedRequest.getUser() != null )
		{
			request.setUser(clonedRequest.getUser());
		}
		if ( clonedRequest.getChannel() != null )
		{
			request.setChannel(clonedRequest.getChannel());
		}
		if ( clonedRequest.getTradeChannel() != null )
		{
			request.setTradeChannel(clonedRequest.getTradeChannel());
		}
		if ( clonedRequest.getCounterparty() != null )
		{
			request.setCounterparty(clonedRequest.getCounterparty());
		}
		if ( clonedRequest.getExpiration() != null )
		{
		    request.setExpiration( updateExpiration( clonedRequest.getExpiration(), request.getExpiration() ) );
		}
		if ( clonedRequest.getNamespace() == null )
		{
			request.setNamespace(clonedRequest.getNamespace());
		}

		if ( clonedRequest.getNotes() != null )
		{
			request.setNotes(clonedRequest.getNotes());
		}
		if ( clonedRequest.getOrderId() != null )
		{
			request.setOrderId(clonedRequest.getOrderId());
		}
		if ( clonedRequest.getOrganization() != null )
		{
			request.setOrganization(clonedRequest.getOrganization());
			request.setOrganizationFirm(clonedRequest.getOrganization().getFirm());
		}

		if ( clonedRequest.getRequestClassification() != null )
		{
			request.setRequestClassification(clonedRequest.getRequestClassification());
		}
		if ( clonedRequest.getExternalRequestId() != null )
		{
			request.setExternalRequestId(clonedRequest.getExternalRequestId());
		}
		if ( clonedRequest.getTradeClassification() != null )
		{
			request.setTradeClassification(clonedRequest.getTradeClassification());
		}
		if ( clonedRequest.getOriginatingCptyId() != null )
		{
			request.setOriginatingCptyId(clonedRequest.getOriginatingCptyId());
		}
		if ( clonedRequest.getOriginatingUserId() != null )
		{
			request.setOriginatingUserId(clonedRequest.getOriginatingUserId());
		}
		if ( clonedRequest.getOriginatingOrderId() != null )
		{
			request.setOriginatingOrderId(clonedRequest.getOriginatingOrderId());
		}
		if ( clonedRequest.getExternalTradeId() != null )
		{
			request.setExternalTradeId(clonedRequest.getExternalTradeId());
		}
		if ( clonedRequest.getMarketSnapshot() != null )
		{
			request.setMarketSnapshot(clonedRequest.getMarketSnapshot());
		}
		if ( clonedRequest.getVirtualServer() != null )
		{
			request.setVirtualServer(clonedRequest.getVirtualServer());
		}
		if ( clonedRequest.getRequestPrices().size() > 0 )
		{
			for ( Object object : clonedRequest.getRequestPrices() )
			{
				FXLegDealingPrice dp = (FXLegDealingPrice) object;
				if ( request.getRequestPrice(dp.getName()) == null )
				{
					log.warn("ISEntityChangeSetHandler.updateRequest : Dealing Price with name " + dp.getName() + " not found.");
					//request.getRequestPrices().add( updateFXLegDealingPrice( dp, ( FXLegDealingPrice ) request.getRequestPrice( dp.getName() ) ) );
				}
				else
				{
					updateFXLegDealingPrice(dp, (FXLegDealingPrice) request.getRequestPrice(dp.getName()));
				}
			}
		}
		if ( clonedRequest.getTransactionID() != null )
		{
			request.setTransactionID(clonedRequest.getTransactionID());
		}
        request.setNetTradeId(clonedRequest.getNetTradeId()); //oa netting
        request.setNettingEnabled(clonedRequest.isNettingEnabled());
        request.setShowFills(clonedRequest.isNettingEnabled());
        request.setTimeInForce(clonedRequest.getTimeInForce());
		request.setAnonymous(clonedRequest.isAnonymous());
		request.setSentCount(clonedRequest.getSentCount());
		request.setInCompetition(clonedRequest.isInCompetition());
		request.setPriceType(clonedRequest.getPriceType());
		request.setExecutionFlags(clonedRequest.getExecutionFlags());
		//request.setPriceTakingIntended( clonedRequest.isPriceTakingIntended() );
		request.setExternalSpread(clonedRequest.getExternalSpread());
		request.setISExtSys(clonedRequest.getISExtSys());
		for ( Object object : clonedRequest.getExternalSystemIds() )
		{
			ExternalSystemId clonedExtSysId = (ExternalSystemId) object;
			ExternalSystemId extSysId = request.getExternalSystemId(clonedExtSysId.getExternalSystem().getShortName());
			if ( extSysId == null )
			{
				extSysId = request.newExternalSystemId();
				if ( ISTransactionManager.isTransactionOn() )
				{
					extSysId = (ExternalSystemId) extSysId.getRegisteredObject();
				}
				extSysId.setExternalSystem(clonedExtSysId.getExternalSystem());
				extSysId.setSystemId(clonedExtSysId.getSystemId());
				request.addExternalSystemId(extSysId);
			}
		}
		if ( clonedRequest.getTriggeredBy() != null )
		{
			request.setTriggeredBy(clonedRequest.getTriggeredBy());
		}
		if ( clonedRequest.getTriggerReachedAt() != null )
		{
			request.setTriggerReachedAt(clonedRequest.getTriggerReachedAt());
		}

		if ( clonedRequest.getUnsolicitedCancelBy() != null )
		{
			request.setUnsolicitedCancelBy(clonedRequest.getUnsolicitedCancelBy());
		}

		if ( clonedRequest.getISExtSys() != null )
		{
			request.setISExtSys(clonedRequest.getISExtSys());
		}

		if ( clonedRequest.getCustomerStreamId() != null )
		{
			request.setCustomerStreamId(clonedRequest.getCustomerStreamId());
		}

		request.setPersistentOrder(clonedRequest.isPersistentOrder());

		if ( clonedRequest.getPreferredProviders() != null )
		{
			request.setPreferredProviders(clonedRequest.getPreferredProviders());
		}

		if ( clonedRequest.getSecondarySortPriority() != null )
		{
			request.setSecondarySortPriority(clonedRequest.getSecondarySortPriority());
		}

		if ( clonedRequest.getCoveredOrderId() != null )
		{
			request.setCoveredOrderId(clonedRequest.getCoveredOrderId());
		}

		if ( clonedRequest.getCoverOrderIds() != null )
		{
			request.setCoverOrderIds(clonedRequest.getCoverOrderIds());
		}

		if ( clonedRequest.getCreatedBusinessDate() != null )
		{
			request.setCreatedBusinessDate(clonedRequest.getCreatedBusinessDate());
		}

		if ( clonedRequest.getTakerTradeIds() != null )
		{
			request.setTakerTradeIds(clonedRequest.getTakerTradeIds());
		}

		//do delta update
		State clonedRequestState = clonedRequest.getWorkflowStateMap() != null ? clonedRequest.getWorkflowStateMap().getState() : null;
		if ( clonedRequestState != null )
		{
			updateRequestState(request, clonedRequestState);
		}

		if ( clonedRequest.getExecutionDateTime() != null )
		{
			request.setExecutionDateTime(clonedRequest.getExecutionDateTime());
		}
		//Will set only on order request. Trade request associated with them
		//will have value false.
		request.setServerManagedOrder(clonedRequest.isServerManagedOrder());

		request.setExternalRequestClassification(clonedRequest.getExternalRequestClassification());
		if ( clonedRequest.getCancelledBy() != null )
		{
			request.setCancelledBy(clonedRequest.getCancelledBy());
		}

		if ( clonedRequest.getVolumeWeightedQuotePrice() != null )
		{
			request.setVolumeWeightedQuotePrice(clonedRequest.getVolumeWeightedQuotePrice());
		}

		if ( clonedRequest.getOrderExecutionStartTime() != null )
		{
			request.setOrderExecutionStartTime(clonedRequest.getOrderExecutionStartTime());
		}
		if ( clonedRequest.getOrderExecutionEndTime() != null )
		{
			request.setOrderExecutionEndTime(clonedRequest.getOrderExecutionEndTime());
		}
        if ( clonedRequest.getTakerReferenceId() != null )
        {
            request.setTakerReferenceId(clonedRequest.getTakerReferenceId());
        }
		request.setTwapSliceInterval(clonedRequest.getTwapSliceInterval());
		request.setRandomizeTwapSliceInterval(clonedRequest.isRandomizeTwapSliceInterval());
		request.setTwapSliceSize(clonedRequest.getTwapSliceSize());
		request.setRandomizeTwapSliceSize(clonedRequest.isRandomizeTwapSliceSize());
		request.setActionOnOrderExpitation(clonedRequest.getActionOnOrderExpitation());
		request.setLinkedOrderId(clonedRequest.getLinkedOrderId());
		request.setPassiveTime(clonedRequest.getPassiveTime());
		request.setOrderExecutionStrategyName(clonedRequest.getOrderExecutionStrategyName());
		request.setTwapSliceRegularSize(clonedRequest.getTwapSliceRegularSize());
		request.setTwapMinimumSliceInterval(clonedRequest.getTwapMinimumSliceInterval());
		request.setTwapSliceTopOfBookPercent(clonedRequest.getTwapSliceTopOfBookPercent());
		request.setTwapFOKSlice(clonedRequest.isTwapFOKSlice());

		request.setPegType(clonedRequest.getPegType());
		request.setPegOffset(clonedRequest.getPegOffset());
		request.setPegOffsetIncrement(clonedRequest.getPegOffsetIncrement());
		request.setPegOffsetIncrementInterval(clonedRequest.getPegOffsetIncrementInterval());
		request.setRandomizePegOffsetIncrement(clonedRequest.isRandomizePegOffsetIncrement());
		
		request.setStrategyMetaData(clonedRequest.getStrategyMetaData());
		request.setOrderMetaData(clonedRequest.getOrderMetaData());
		request.setSEF(clonedRequest.isSEF());
		
		if ( clonedRequest.getUPI() != null )
	    {
			request.setUPI(clonedRequest.getUPI());
	    }
		
		request.setFSRRequest(clonedRequest.isFSRRequest());
        request.setNetRequest( clonedRequest.isNetRequest() );
        request.setOutrightLimitOrder(clonedRequest.isOutrightLimitOrder());
        request.setContractId(clonedRequest.getContractId());
        
        if( clonedRequest.getPortfolioRefId() != null )
        {
        	request.setPortfolioRefId(clonedRequest.getPortfolioRefId());
            request.setOriginatingPortfolioId(clonedRequest.getOriginatingPortfolioId());
        }
        request.setRFSBroadcast(clonedRequest.isRFSBroadcast());
        request.setOrderSession(clonedRequest.isOrderSession());
        request.setAllocation(clonedRequest.isAllocation());
        request.setContingencyParameters(clonedRequest.getContingencyParameters());
        request.setOTOInactiveOrder(clonedRequest.isOTOInactiveOrder());
        request.setEmsExecutionType(clonedRequest.getEmsExecutionType());
		request.setCustomParameters ( clonedRequest.getCustomParameters () );
		request.setMT300Field72( clonedRequest.getMT300Field72() );
		request.setDeliverableNDFParameter ( clonedRequest.getDeliverableNDFParameter () );
	}

	/**
	 * @param clonedTrade
	 * @param trade
	 * @return
	 */
	public Trade updateTrade( Trade clonedTrade, Trade trade )
	{
		if ( trade == null )
		{
			if ( clonedTrade instanceof FXSingleLeg )
			{
				trade = FXFactory.newFXSingleLeg();
			}
			else if ( clonedTrade instanceof FXSwap )
			{
				trade = FXFactory.newFXSwap();
			}
			else
			{
				return null;
			}
		}
		if ( clonedTrade.getActualSettlementDate() != null )
		{
			trade.setActualSettlementDate(clonedTrade.getActualSettlementDate());
		}
		if ( clonedTrade.getBusinessCenter() != null )
		{
			trade.setBusinessCenter(clonedTrade.getBusinessCenter());
		}
		if ( clonedTrade.getChannel() != null )
		{
			trade.setChannel(clonedTrade.getChannel());
		}
		if ( clonedTrade.getCounterpartyA() != null )
		{
			trade.setCounterpartyA(clonedTrade.getCounterpartyA());
			trade.setCounterpartyAFirm(getFirm(clonedTrade.getCounterpartyA()));
		}
		if ( clonedTrade.getCounterpartyB() != null )
		{
			trade.setCounterpartyB(clonedTrade.getCounterpartyB());
			trade.setCounterpartyBFirm(getFirm(clonedTrade.getCounterpartyB()));
		}
		if ( clonedTrade.isManuallyUpdated() )
		{
			trade.setManuallyUpdated(true);
		}
		trade.setCounterpartyC(clonedTrade.getCounterpartyC());
		if( clonedTrade.getCounterpartyC() != null ){
			trade.setCounterpartyCFirm(getFirm(clonedTrade.getCounterpartyC()));
		}
		else{
			trade.setCounterpartyCFirm(null);
		}
		trade.setCounterpartyD(clonedTrade.getCounterpartyD());
		if( clonedTrade.getCounterpartyD() != null ){
			trade.setCounterpartyDFirm(getFirm(clonedTrade.getCounterpartyD()));
		}
		else{
			trade.setCounterpartyDFirm(null);
		}
		if ( clonedTrade.getExchange() != null )
		{
			trade.setExchange(clonedTrade.getExchange());
		}
		if ( clonedTrade.getMaturityDate() != null )
		{
			trade.setMaturityDate(clonedTrade.getMaturityDate());
		}
		trade.setNamespace(clonedTrade.getNamespace());
		if ( clonedTrade.getOrganization() != null )
		{
			trade.setOrganization(clonedTrade.getOrganization());
		}
		if ( clonedTrade.getSettlementBusinessCalendar() != null )
		{
			trade.setSettlementBusinessCalendar(clonedTrade.getSettlementBusinessCalendar());
		}
		if ( clonedTrade.getSettlementDate() != null )
		{
			trade.setSettlementDate(clonedTrade.getSettlementDate());
		}
		if ( clonedTrade.getEntryDate() != null )
		{
			trade.setEntryDate(clonedTrade.getEntryDate());
		}
		if ( clonedTrade.getExecutionDateTime() != null )
		{
			trade.setExecutionDateTime(clonedTrade.getExecutionDateTime());
		}
		if ( clonedTrade.getConfirmationDateTime() != null )
		{
			trade.setConfirmationDateTime(clonedTrade.getConfirmationDateTime());
		}
		if ( clonedTrade.getSettlementDateRule() != null )
		{
			trade.setSettlementDateRule(clonedTrade.getSettlementDateRule());
		}
		if ( clonedTrade.getTradeClassification() != null )
		{
			trade.setTradeClassification(clonedTrade.getTradeClassification());
		}
		if ( clonedTrade.getTradeDate() != null )
		{
			trade.setTradeDate(clonedTrade.getTradeDate());
		}
		if ( clonedTrade.getEntryUser() != null )
		{
			trade.setEntryUser(clonedTrade.getEntryUser());
		}
		if ( clonedTrade.getFrontOfficeID() != null )
		{
			trade.setFrontOfficeID(clonedTrade.getFrontOfficeID());
		}
		if ( clonedTrade.getBackOfficeID() != null )
		{
			trade.setBackOfficeID(clonedTrade.getBackOfficeID());
		}
		if ( clonedTrade.getTakerReferenceId() != null )
		{
			trade.setTakerReferenceId(clonedTrade.getTakerReferenceId());
		}
		if ( clonedTrade.getMakerReferenceId() != null )
		{
			trade.setMakerReferenceId(clonedTrade.getMakerReferenceId());
		}
		if ( clonedTrade.getMakerUser() != null )
		{
			trade.setMakerUser(clonedTrade.getMakerUser());
		}
		if ( clonedTrade.getVirtualServer() != null )
		{
			trade.setVirtualServer(clonedTrade.getVirtualServer());
		}
		if ( clonedTrade.getStream() != null )
		{
			trade.setStream(clonedTrade.getStream());
		}
		if ( clonedTrade.getRFSQuoteStateChangeSnapshot() != null )
		{
			trade.setRFSQuoteStateChangeSnapshot(clonedTrade.getRFSQuoteStateChangeSnapshot());
		}
		if ( clonedTrade.getCoveredTradeTxId() != null )
		{
			trade.setCoveredTradeTxId(clonedTrade.getCoveredTradeTxId());
		}
		if ( clonedTrade.getCoverTradeTxIds() != null )
		{
			trade.setCoverTradeTxIds(clonedTrade.getCoverTradeTxIds());
		}
		if ( clonedTrade.getPriceRegenerationKey() != null )
		{
			trade.setPriceRegenerationKey(clonedTrade.getPriceRegenerationKey());
		}
		if ( clonedTrade.getMakerCounterparty() != null )
		{
			trade.setMakerCounterparty(clonedTrade.getMakerCounterparty());
		}
		if ( clonedTrade.getTakerCounterparty() != null )
		{
			trade.setTakerCounterparty(clonedTrade.getTakerCounterparty());
		}
		if ( clonedTrade.getState() != null )
		{
			trade.setState(clonedTrade.getState());
		}
		if ( clonedTrade.getCoveredTradeCounterparty() != null )
		{
			trade.setCoveredTradeCounterparty(clonedTrade.getCoveredTradeCounterparty());
		}
		if ( clonedTrade.getCoveredTradeUser() != null )
		{
			trade.setCoveredTradeUser(clonedTrade.getCoveredTradeUser());
		}
        if ( clonedTrade.getBusinessExecutionDate() != null )
        {
            trade.setBusinessExecutionDate( clonedTrade.getBusinessExecutionDate() );
        }

        if ( clonedTrade.getCoverTrdMkrIds() != null )
        {
            trade.setCoverTrdMkrIds( clonedTrade.getCoverTrdMkrIds() );
        }
        trade.setClientCPUUsage(clonedTrade.getClientCPUUsage());
		trade.setClientMemoryUsage(clonedTrade.getClientMemoryUsage());
		trade.setClientPollLatency(clonedTrade.getClientPollLatency());
		trade.setUserLatency(clonedTrade.getUserLatency());
		trade.setNoOfAttemptsByClient(clonedTrade.getNoOfAttemptsByClient());
		trade.setPriceRegenerationState(clonedTrade.getPriceRegenerationState());
		trade.setNote(clonedTrade.getNote());
        trade.setSymbol( clonedTrade.getSymbol() );
        trade.setFreshQuoteUsed(clonedTrade.isFreshQuoteUsed());
        if ( clonedTrade.getTransactionID() != null )
		{
			trade.setTransactionID(clonedTrade.getTransactionID());
		}

		TradeStateFacade tsf = (TradeStateFacade) trade.getFacade(TradeStateFacade.TRADE_STATE_FACADE);
		State clonedTradeState = clonedTrade.getWorkflowStateMap() != null ? clonedTrade.getWorkflowStateMap().getState() : null;
		if ( clonedTradeState != null )
		{
			tsf.setTradeState(clonedTradeState);
			WorkflowStateMap cWsm = clonedTrade.getWorkflowStateMap();
			if ( cWsm.getWorkflowCodeArgument() != null )
			{
				WorkflowStateMap wsm = trade.getWorkflowStateMap();
				wsm.setWorkflowCode(cWsm.getWorkflowCode());
				wsm.setWorkflowCodeArgument(cWsm.getWorkflowCodeArgument());
			}
		}

		for ( Object object : clonedTrade.getTradeLegs() )
		{
			FXLeg fxLeg = (FXLeg) object;
			if ( trade.getTradeLeg(fxLeg.getName()) == null )
			{
				log.warn("ISEntityChangeSetHandler.updateTrade : Trade Leg with name " + fxLeg.getName() + " not found.");
				trade.getTradeLegs().add(updateFXLeg(fxLeg, null));
			}
			else
			{
				updateFXLeg(fxLeg, (FXLeg) trade.getTradeLeg(fxLeg.getName()));
			}
		}
		for ( Object object : clonedTrade.getExternalSystemIds() )
		{
			ExternalSystemId clonedExtSysId = (ExternalSystemId) object;
			ExternalSystemId extSysId = trade.getExternalSystemId(clonedExtSysId.getExternalSystem().getShortName());
			if ( extSysId == null )
			{
				extSysId = trade.newExternalSystemId();
				if ( ISTransactionManager.isTransactionOn() )
				{
					extSysId = (ExternalSystemId) extSysId.getRegisteredObject();
					extSysId.setExternalSystem(clonedExtSysId.getExternalSystem());
					extSysId.setSystemId(clonedExtSysId.getSystemId());
					trade.addExternalSystemId(extSysId);
				}
			}
			else
			{
				if ( ISTransactionManager.isTransactionOn() )
				{
					extSysId = (ExternalSystemId) extSysId.getRegisteredObject();
					extSysId.setExternalSystem(clonedExtSysId.getExternalSystem());
					extSysId.setSystemId(clonedExtSysId.getSystemId());
				}
			}
		}
		updateEventTimes(clonedTrade, trade);
		updateCptyTrades(clonedTrade, trade);
		trade.setCptyReqIdExtSys(clonedTrade.getCptyReqIdExtSys());
		trade.setCptyTradeIdExtSys(clonedTrade.getCptyTradeIdExtSys());
		trade.setISExtSys(clonedTrade.getISExtSys());
		trade.setCoveredTradeExtSys(clonedTrade.getCoveredTradeExtSys());
		trade.setDirectFXTraderExtSys(clonedTrade.getDirectFXTraderExtSys());
		trade.setFIX43ExtSys(clonedTrade.getFIX43ExtSys());
		trade.setRequestIdExtSys(clonedTrade.getRequestIdExtSys());
		trade.setTraderESPExtSys(clonedTrade.getTraderESPExtSys());
		trade.setProviderExtSys(clonedTrade.getProviderExtSys());
		trade.setCSDKExtSys(clonedTrade.getCSDKExtSys());
		trade.setMakerMarketSnapshot(clonedTrade.getMakerMarketSnapshot());
		trade.setOriginatingOrderId(clonedTrade.getOriginatingOrderId());
		if ( clonedTrade.getMaskedLP() != null )
			trade.setMaskedLP(clonedTrade.getMaskedLP());
		trade.setExecutionFlags(clonedTrade.getExecutionFlags());
		trade.setSalesDealerCounterparty(clonedTrade.getSalesDealerCounterparty());
		trade.setSalesDealerUser(clonedTrade.getSalesDealerUser());
		trade.setMakerSalesDealerCounterparty(clonedTrade.getMakerSalesDealerCounterparty());
		trade.setMakerSalesDealerUser(clonedTrade.getMakerSalesDealerUser());
		trade.setRiskPosition(clonedTrade.isRiskPosition());
        trade.setOrderId( clonedTrade.getOrderId() );
        if( clonedTrade.getClientTag() != null )
        {
        	trade.setClientTag( clonedTrade.getClientTag() );
        }
        trade.setCounterpartyALEI( clonedTrade.getCounterpartyALEI() );
        trade.setCounterpartyBLEI( clonedTrade.getCounterpartyBLEI() );
        trade.setUPI( clonedTrade.getUPI() );
        
        trade.setSEF( clonedTrade.isSEF() );
        trade.setSEFOrg( clonedTrade.getSEFOrg() );
        trade.setClearingHouse( clonedTrade.getClearingHouse() );
        trade.setSDR( clonedTrade.getSDR() );
        trade.setUSI( clonedTrade.getUSI() );
		trade.setSDRAckId( clonedTrade.getSDRAckId() );
		trade.setSDRTradeStatus( clonedTrade.getSDRTradeStatus() );
		trade.setClearingHouseAckId( clonedTrade.getClearingHouseAckId() );
		trade.setClearingHouseTradeStatus( clonedTrade.getClearingHouseTradeStatus() );
		trade.setBuySideClearedUSI( clonedTrade.getBuySideClearedUSI() );
		trade.setSellSideClearedUSI( clonedTrade.getSellSideClearedUSI() );
		trade.setCreditLimitHubId(clonedTrade.getCreditLimitHubId());
		trade.setCreditLimitHubStatus(clonedTrade.getCreditLimitHubStatus());
        trade.setReportingParty( clonedTrade.getReportingParty() );
        
        trade.setClearingTime(clonedTrade.getClearingTime());
        trade.setUSILinkId( clonedTrade.getUSILinkId() );
        trade.setUTILinkId( clonedTrade.getUTILinkId() );
        trade.setPortfolioRefId( clonedTrade.getPortfolioRefId() );
        trade.setOriginatingPortfolioId(clonedTrade.getOriginatingPortfolioId());
        trade.setContractId( clonedTrade.getContractId() );
        trade.setBlockTrade( clonedTrade.isBlockTrade() );
        trade.setLargeSizeTrade( clonedTrade.isLargeSizeTrade() );
        trade.setAcceptedPriceSkewed( clonedTrade.isAcceptedPriceSkewed() );
        trade.setAllocation( clonedTrade.isAllocation() );
        trade.setExternalRequestId( clonedTrade.getExternalRequestId() );
        trade.setManualTrade( clonedTrade.isManualTrade() );
        trade.setMandatorilyClearable(clonedTrade.isMandatorilyClearable());
        trade.setIntentToClear(clonedTrade.isIntentToClear());
        trade.setEndUserException(clonedTrade.isEndUserException());
        trade.setSyntheticCross(clonedTrade.isSyntheticCross());
        trade.setSyntheticCrossComponent(clonedTrade.getSyntheticCrossComponent());
        trade.setForeignCurrencyPair(clonedTrade.getForeignCurrencyPair());
        trade.setLocalCurrencyPair(clonedTrade.getLocalCurrencyPair());
        trade.setVehicleCCY(clonedTrade.getVehicleCCY());
        trade.setVehicleCCYAmount(clonedTrade.getVehicleCCYAmount());
		trade.setABooking(clonedTrade.isABooking());
		trade.setPrimarySpotValueDate(clonedTrade.getPrimarySpotValueDate());
		trade.setSecondarySpotValueDate(clonedTrade.getSecondarySpotValueDate());
		trade.setBrokerExecutionType(clonedTrade.getBrokerExecutionType());
		trade.setNetSpotAmount(clonedTrade.getNetSpotAmount());
		trade.setNetSpotAmountTerm(clonedTrade.getNetSpotAmountTerm());
		trade.setNetBuyingBase(clonedTrade.isNetBuyingBase());
		trade.setSpotValueDate(clonedTrade.getSpotValueDate());
		trade.setCptyABookName(clonedTrade.getCptyABookName());
		trade.setCptyBBookName(clonedTrade.getCptyBBookName());
		trade.setCptyBPrimaryBookName(clonedTrade.getCptyBPrimaryBookName());
		trade.setCptyBSecondaryBookName(clonedTrade.getCptyBSecondaryBookName());
		trade.setAlgoType(clonedTrade.getAlgoType());
		trade.setOrderNetTrade(clonedTrade.isOrderNetTrade());
        trade.setMTFTrade( clonedTrade.isMTFTrade() );
        trade.setExecutionVenue( clonedTrade.getExecutionVenue() );
        trade.setMiFIDTradeParams( clonedTrade.getMiFIDTradeParams() );
        trade.setMiFIDVenueExecutionTime( clonedTrade.getMiFIDVenueExecutionTime() );
        trade.setPricingType(clonedTrade.getPricingType());
        if ( clonedTrade.getAccountId () != null )
		{
			trade.setAccountId ( clonedTrade.getAccountId () );
		}
		if ( clonedTrade.getAccountShortname() != null )
		{
			trade.setAccountShortname ( clonedTrade.getAccountShortname () );
		}
		trade.setFeatureFlags(clonedTrade.getFeatureFlags());
		trade.setMT300Field72(clonedTrade.getMT300Field72());
		trade.setStreamingNonSpot ( clonedTrade.isStreamingNonSpot () );
		trade.setCreditValuationAdjustment ( clonedTrade.getCreditValuationAdjustment () );
        trade.setClientRequestId ( clonedTrade.getClientRequestId () );
        trade.setBrokerExecutionType(clonedTrade.getBrokerExecutionType());
        trade.setPrimaryExecutionType(clonedTrade.getPrimaryExecutionType());
        trade.setSecondaryExecutionType(clonedTrade.getSecondaryExecutionType());
		trade.setCustomParameters ( clonedTrade.getCustomParameters () );
		trade.setProrataForwardSchedule ( clonedTrade.getProrataForwardSchedule () );
		if (clonedTrade.getPnlCurrency() != null && clonedTrade.getPnlAmount() != null){
			trade.setPnlCurrency(clonedTrade.getPnlCurrency());
			trade.setHomeCurrencySpotRate(clonedTrade.getHomeCurrencySpotRate());
			trade.setPnlAmount(clonedTrade.getPnlAmount());
			trade.setBaseRate(clonedTrade.getBaseRate());
			trade.setBaseSwapPoints(clonedTrade.getBaseSwapPoints());
		}
		try{
			trade.setDepositsParamsStr(clonedTrade.getDepositsParamsStr());
		}catch (IOException e){
			log.error("Exception in parsing Deposits Params", e);
		}
        return clonedTrade;
	}

	private Organization getFirm(Counterparty cpty) {
		if( cpty instanceof LegalEntity){
			return cpty.getOrganization().getFirm();
		}
		else if( cpty instanceof TradingParty){
			return ((TradingParty)cpty).getLegalEntityOrganization().getFirm();
		}
		return null;
	}

	private void updateCptyTrades( Trade clonedTrade, Trade trade )
	{
		Collection<CptyTrade> clonedCptyTrades = clonedTrade.getCptyTrades();
		Collection<CptyTrade> cptyTrades = trade.getCptyTrades();
		for ( CptyTrade clonedCptyTrd : clonedCptyTrades )
		{
			CptyTrade cptyTrd = trade.getCptyTrade(clonedCptyTrd.getOwningCptyRef());
			if ( cptyTrd == null )
			{
				cptyTrd = clonedCptyTrd;
				if ( ISTransactionManager.isTransactionOn() )
				{
					cptyTrd.setTrade(null);
					cptyTrd = (CptyTrade) cptyTrd.getRegisteredObject();
				}
				cptyTrd.setTrade(trade);
				cptyTrades.add(cptyTrd);
			}
			else
			{
				if ( clonedCptyTrd.getUser() != null )
				{
					cptyTrd.setUser(clonedCptyTrd.getUser());
				}
				if ( clonedCptyTrd.getTradingPartyUser() != null )
				{
					cptyTrd.setTradingPartyUser(clonedCptyTrd.getTradingPartyUser());
				}
				if ( clonedCptyTrd.getLegalEntity() != null )
				{
					cptyTrd.setLegalEntity(clonedCptyTrd.getLegalEntity());
				}
				if ( clonedCptyTrd.getTradingParty() != null )
				{
					cptyTrd.setTradingParty(clonedCptyTrd.getTradingParty());
				}
				cptyTrd.setTaker(clonedCptyTrd.isTaker());
				if ( clonedCptyTrd.getCoveredTradeTxId() != null )
				{
					cptyTrd.setCoveredTradeTxId(clonedCptyTrd.getCoveredTradeTxId());
				}
				if ( clonedCptyTrd.getCoverTradeTxIds() != null )
				{
					cptyTrd.setCoverTradeTxIds(clonedCptyTrd.getCoverTradeTxIds());
				}
				if ( clonedCptyTrd.getTradeDate() != null )
				{
					cptyTrd.setTradeDate(clonedCptyTrd.getTradeDate());
				}
				if ( clonedCptyTrd.getNamespace() != null )
				{
					cptyTrd.setNamespace(clonedCptyTrd.getNamespace());
				}
                if ( clonedCptyTrd.getTradingPartyLegalEntity() != null )
                {
                    cptyTrd.setTradingPartyLegalEntity( clonedCptyTrd.getTradingPartyLegalEntity() );
                }
                if ( clonedCptyTrd.getTradingPartyOrganization() != null )
                {
                    cptyTrd.setTradingPartyOrganization( clonedCptyTrd.getTradingPartyOrganization() );
                }
            }
			if ( trade.getWorkflowStateMap() != null && (cptyTrd.getWorkflowStateMap() == null || cptyTrd.getWorkflowStateMap() != trade.getWorkflowStateMap()) )
			{
				cptyTrd.setWorkflowStateMap(trade.getWorkflowStateMap());
			}
		}
	}

	private void updateEventTimes( Trade clonedTrade, Trade trade )
	{
		if ( clonedTrade.getRateEffective() != null )
		{
			trade.setRateEffective(clonedTrade.getRateEffective());
		}
		if ( clonedTrade.getRateRecvdByAdpt() != null )
		{
			trade.setRateRecvdByAdpt(clonedTrade.getRateRecvdByAdpt());
		}
		if ( clonedTrade.getRateSentByAdpt() != null )
		{
			trade.setRateSentByAdpt(clonedTrade.getRateSentByAdpt());
		}
		if ( clonedTrade.getRateRecvdByApp() != null )
		{
			trade.setRateRecvdByApp(clonedTrade.getRateRecvdByApp());
		}
		if ( clonedTrade.getQuoteCreatedByApp() != null )
		{
			trade.setQuoteCreatedByApp(clonedTrade.getQuoteCreatedByApp());
		}
		if ( clonedTrade.getRateSentByApp() != null )
		{
			trade.setRateSentByApp(clonedTrade.getRateSentByApp());
		}
		if ( clonedTrade.getRateRecvdByPrxy() != null )
		{
			trade.setRateRecvdByPrxy(clonedTrade.getRateRecvdByPrxy());
		}
		if ( clonedTrade.getRateQueriedByUser() != null )
		{
			trade.setRateQueriedByUser(clonedTrade.getRateQueriedByUser());
		}
		if ( clonedTrade.getRateSentByPrxy() != null )
		{
			trade.setRateSentByPrxy(clonedTrade.getRateSentByPrxy());
		}
		if ( clonedTrade.getRateRecvdByUser() != null )
		{
			trade.setRateRecvdByUser(clonedTrade.getRateRecvdByUser());
		}
		if ( clonedTrade.getRateDsplyByUser() != null )
		{
			trade.setRateDsplyByUser(clonedTrade.getRateDsplyByUser());
		}
		if ( clonedTrade.getRateAcptByUser() != null )
		{
			trade.setRateAcptByUser(clonedTrade.getRateAcptByUser());
		}
		if ( clonedTrade.getAcptSentByUser() != null )
		{
			trade.setAcptSentByUser(clonedTrade.getAcptSentByUser());
		}
		if ( clonedTrade.getAcptRecvdByApp() != null )
		{
			trade.setAcptRecvdByApp(clonedTrade.getAcptRecvdByApp());
		}
		if ( clonedTrade.getAcptSentByApp() != null )
		{
			trade.setAcptSentByApp(clonedTrade.getAcptSentByApp());
		}
		if ( clonedTrade.getAcptRecvdByAdpt() != null )
		{
			trade.setAcptRecvdByAdpt(clonedTrade.getAcptRecvdByAdpt());
		}
		if ( clonedTrade.getAcptSentByAdpt() != null )
		{
			trade.setAcptSentByAdpt(clonedTrade.getAcptSentByAdpt());
		}
		if ( clonedTrade.getVrfyRecvdFrmPrvdr() != null )
		{
			trade.setVrfyRecvdFrmPrvdr(clonedTrade.getVrfyRecvdFrmPrvdr());
		}
		if ( clonedTrade.getRespSentByAdpt() != null )
		{
			trade.setRespSentByAdpt(clonedTrade.getRespSentByAdpt());
		}
		if ( clonedTrade.getVrfyRecvdByApp() != null )
		{
			trade.setVrfyRecvdByApp(clonedTrade.getVrfyRecvdByApp());
		}
		if ( clonedTrade.getVrfySentByApp() != null )
		{
			trade.setVrfySentByApp(clonedTrade.getVrfySentByApp());
		}
		if ( clonedTrade.getCnfrmByUser() != null )
		{
			trade.setCnfrmByUser(clonedTrade.getCnfrmByUser());
		}
		if ( clonedTrade.getRjctRecvdFrmPrvdr() != null )
		{
			trade.setRjctRecvdFrmPrvdr(clonedTrade.getRjctRecvdFrmPrvdr());
		}
		if ( clonedTrade.getRjctRecvdByApp() != null )
		{
			trade.setRjctRecvdByApp(clonedTrade.getRjctRecvdByApp());
		}
		if ( clonedTrade.getRjctSentByApp() != null )
		{
			trade.setRjctSentByApp(clonedTrade.getRjctSentByApp());
		}
		if ( clonedTrade.getRateAggrtByApp() != null )
		{
			trade.setRateAggrtByApp(clonedTrade.getRateAggrtByApp());
		}
		if ( clonedTrade.getOrdRecvdByApp() != null )
		{
			trade.setOrdRecvdByApp(clonedTrade.getOrdRecvdByApp());
		}
		if ( clonedTrade.getOrdMtchdByApp() != null )
		{
			trade.setOrdMtchdByApp(clonedTrade.getOrdMtchdByApp());
		}
		if ( clonedTrade.getNxtRateRecvdByApp() != null )
		{
			trade.setNxtRateRecvdByApp(clonedTrade.getNxtRateRecvdByApp());
		}
		if ( clonedTrade.getTriggerReachedAt() != null )
		{
			trade.setTriggerReachedAt(clonedTrade.getTriggerReachedAt());
		}
		if ( clonedTrade.getOAOrderReceivedByAdaptor() != null )
		{
			trade.setOAOrderReceivedByAdaptor(clonedTrade.getOAOrderReceivedByAdaptor());
		}
		if ( clonedTrade.getOAOrderPublishedByAdaptor() != null )
		{
			trade.setOAOrderPublishedByAdaptor(clonedTrade.getOAOrderPublishedByAdaptor());
		}
		if ( clonedTrade.getOAOrderMatchedByAdaptor() != null )
		{
			trade.setOAOrderMatchedByAdaptor(clonedTrade.getOAOrderMatchedByAdaptor());
		}
		if ( clonedTrade.getJMSProxyWaitTime() != null )
		{
			trade.setJMSProxyWaitTime(clonedTrade.getJMSProxyWaitTime());
		}
		if ( clonedTrade.getEtlTimestamp() != null )
		{
			trade.setEtlTimestamp(clonedTrade.getEtlTimestamp());
		}
		if ( clonedTrade.getMakerOrderId() != null )
		{
			trade.setMakerOrderId(clonedTrade.getMakerOrderId());
		}
        if ( clonedTrade.getMakerOrderUserChannel() != null )
        {
            trade.setMakerOrderUserChannel(clonedTrade.getMakerOrderUserChannel());
        }
        if ( clonedTrade.getMakerOrderDealtCurrency() != null )
        {
            trade.setMakerOrderDealtCurrency(clonedTrade.getMakerOrderDealtCurrency());
        }
    }

	public Quote updateQuote( Quote clonedQuote, Quote quote )
	{
		if ( quote == null )
		{
			quote = new QuoteC();
		}

		if ( clonedQuote.getNamespace() != null )
		{
			quote.setNamespace(clonedQuote.getNamespace());
		}
		if ( clonedQuote.getLegalEntity() != null )
		{
			quote.setLegalEntity(clonedQuote.getLegalEntity());
		}
		if ( clonedQuote.getOrganization() != null )
		{
			quote.setOrganization(clonedQuote.getOrganization());
		}
		if ( clonedQuote.getQuoteClassification() != null )
		{
			quote.setQuoteClassification(clonedQuote.getQuoteClassification());
		}

		if ( clonedQuote.getQuotePrices().size() > 0 )
		{
			for ( Object object : clonedQuote.getQuotePrices() )
			{
				FXLegDealingPrice clonedDp = (FXLegDealingPrice) object;
				FXLegDealingPrice dp = (FXLegDealingPrice) quote.getQuotePrice(clonedDp.getName());
				if ( dp == null )
				{
					log.warn("ISEntityChangeSetHandler.updateQuote : Quote Price with name " + clonedDp.getName() + " not found");
					quote.getQuotePrices().add(updateFXLegDealingPrice(clonedDp, null));
				}
				else
				{
					updateFXLegDealingPrice(clonedDp, dp);
				}

			}
		}

		if ( clonedQuote.getContact() != null )
		{
			quote.setContact(clonedQuote.getContact());
		}

		/*
		Commenting as Expiration is not being serialized.
		if ( clonedQuote.getExpiration() != null )
		{
		    quote.setExpiration( updateExpiration( clonedQuote.getExpiration(), quote.getExpiration() ) );
		}
		*/

		if ( clonedQuote.getNotes() != null )
		{
			quote.setNotes(clonedQuote.getNotes());
		}

		quote.setPriceType(clonedQuote.getPriceType());

		if ( clonedQuote.getTransactionID() != null )
		{
			quote.setTransactionID(clonedQuote.getTransactionID());
		}

		quote.setCurrencyPair(clonedQuote.getCurrencyPair());

		quote.setStream(clonedQuote.getStream());
		quote.setSentDate(clonedQuote.getSentDate());
		quote.setSequenceNumber(clonedQuote.getSequenceNumber());
		quote.setExternalQuoteId(clonedQuote.getExternalQuoteId());
		quote.setRateId(clonedQuote.getRateId());
		quote.setSpreadsApplied( clonedQuote.isSpreadsApplied() );

		// Copy EventTimes
		EventTimeFacade origEtFacade = (EventTimeFacade) clonedQuote.getFacade(EventTimeFacade.NAME);
		ISQuoteEventTimeFacadeC etFacade = (ISQuoteEventTimeFacadeC) quote.getFacade(EventTimeFacade.NAME);
		if ( null != etFacade && null != origEtFacade )
		{
			etFacade.setTimes(origEtFacade.getTimes());
		}
		quote.setSentCount(0);

		return quote;
	}

	protected FXLeg updateFXLeg( FXLeg clonedFXLeg, FXLeg fxLeg )
	{

		if ( fxLeg == null )
		{
			fxLeg = FXFactory.newFXLeg();
		}
		if ( clonedFXLeg.getName() != null )
		{
			fxLeg.setName(clonedFXLeg.getName());
		}
		clonedFXLeg.getFinancialEvents();
		FXPaymentParameters clonedFXPayment = clonedFXLeg.getFXPayment();
		FXPaymentParameters fxPayment = fxLeg.getFXPayment();
		updateFXPayment(clonedFXPayment, fxPayment);
		fxLeg.setName(clonedFXLeg.getName());
		fxLeg.setNamespace(clonedFXLeg.getNamespace());
		if ( clonedFXLeg.getTradeLegClassification() != null )
		{
			fxLeg.setTradeLegClassification(clonedFXLeg.getTradeLegClassification());
		}
		fxLeg.setUSI(clonedFXLeg.getUSI());
		fxLeg.setUSINamespace(clonedFXLeg.getUSINamespace());
		fxLeg.setUSIIdentifier(clonedFXLeg.getUSIIdentifier());		
		fxLeg.setUTI(clonedFXLeg.getUTI());
		fxLeg.setUTINamespace(clonedFXLeg.getUTINamespace());
		fxLeg.setUTIIdentifier(clonedFXLeg.getUTIIdentifier());
		fxLeg.setMakerUnfilledAmount(clonedFXLeg.getMakerUnfilledAmount());
		fxLeg.setSwapTransactionType ( clonedFXLeg.getSwapTransactionType ()  );
		return fxLeg;
	}

	protected void updateFXPayment( FXPaymentParameters clonedfxPayment, FXPaymentParameters fxPayment )
	{
		if ( clonedfxPayment.getBusinessCalendar() != null )
		{
			fxPayment.setBusinessCalendar(clonedfxPayment.getBusinessCalendar());
		}
		if ( clonedfxPayment.getCurrency1() != null )
		{
			fxPayment.setCurrency1(clonedfxPayment.getCurrency1());
		}
		fxPayment.setCurrency1Amount(clonedfxPayment.getCurrency1Amount());
		if ( clonedfxPayment.getCurrency2() != null )
		{
			fxPayment.setCurrency2(clonedfxPayment.getCurrency2());
		}
		fxPayment.setBuyingCurrency1(clonedfxPayment.isBuyingCurrency1());
		fxPayment.setCurrency2Amount(clonedfxPayment.getCurrency2Amount());
		if ( clonedfxPayment.getDateRule() != null )
		{
			fxPayment.setDateRule(clonedfxPayment.getDateRule());
		}
		if ( clonedfxPayment.getFXCoverRate() != null )
		{
			FXCoverRate coverRate = fxPayment.getFXCoverRate();
			if ( coverRate == null )
			{
				coverRate = new FXCoverRateC();
				fxPayment.setFXCoverRate(coverRate);
			}
			coverRate.copy(clonedfxPayment.getFXCoverRate());
			//            fxPayment.getFXCoverRate().setFXRate( ( FXRate ) clonedfxPayment.getFXCoverRate().getFXRate().clone() );
			//            Map<String, SpreadElement> spreadsMap = clonedfxPayment.getFXCoverRate().getAllSpreads();
			//            Set<String> keySet = spreadsMap.keySet();
			//            for ( String spread : keySet )
			//            {
			//                fxPayment.getFXCoverRate().addSpread( spread, spreadsMap.get( spread ).getSpreadValue() );
			//            }
			//            fxPayment.getFXCoverRate().setNamespace( clonedfxPayment.getFXCoverRate().getNamespace() );
			//            fxPayment.getFXCoverRate().setMarketRate( clonedfxPayment.getFXCoverRate().getNamespace());
		}
		
		if ( clonedfxPayment.getFXRate() != null )
		{
			fxPayment.setFXRate((FXRate) clonedfxPayment.getFXRate().clone());
		}
		
		if ( clonedfxPayment.getFXSettlementDateRule() != null )
		{
			fxPayment.setFXSettlementDateRule(clonedfxPayment.getFXSettlementDateRule());
		}
		
		fxPayment.setNamespace(clonedfxPayment.getNamespace());
		if ( clonedfxPayment.getSpotDate() != null )
		{
			fxPayment.setSpotDate(clonedfxPayment.getSpotDate());
		}
		
		if ( clonedfxPayment.getTenor() != null )
		{
			fxPayment.setTenor(clonedfxPayment.getTenor());
		}
		
		if ( clonedfxPayment.getValueDate() != null )
		{
			fxPayment.setValueDate(clonedfxPayment.getValueDate());
		}
		
		if ( clonedfxPayment.getFixingDate() != null )
		{
			fxPayment.setFixingDate(clonedfxPayment.getFixingDate());
		}
		
		if ( clonedfxPayment.getFixingTenor() != null )
		{
			fxPayment.setFixingTenor(clonedfxPayment.getFixingTenor());
		}
		
		if ( clonedfxPayment.getFXMidRate()!= null )
		{
			fxPayment.setFXMidRate(clonedfxPayment.getFXMidRate());
		}
		
		fxPayment.setDealtCurrency1(clonedfxPayment.isDealtCurrency1());
		
	}

	protected FXLegDealingPrice updateFXLegDealingPrice( FXLegDealingPrice clonedDP, FXLegDealingPrice dp )
	{
		if ( dp == null )
		{
			dp = FXDealingFactory.newFXLegDealingPrice();
		}
		if ( clonedDP.getName() != null )
		{
			dp.setName(clonedDP.getName());
		}
		dp.setGroupIndex(clonedDP.getGroupIndex());
		dp.setBidOfferMode(clonedDP.getBidOfferMode());
		dp.setBuySellMode(clonedDP.getBuySellMode());
		dp.setDealtCurrency(clonedDP.getDealtCurrency());
		dp.setSettledCurrency(clonedDP.getSettledCurrency());
		dp.setDealtCurrencyProperty(clonedDP.getDealtCurrencyProperty());
		dp.setDealtAmount(clonedDP.getDealtAmount());
		dp.setStatus(clonedDP.getStatus());
		if ( clonedDP.getTenor() != null )
		{
			dp.setTenor(clonedDP.getTenor());
		}

		if ( clonedDP.getValueDate() != null )
		{
			dp.setValueDate(clonedDP.getValueDate());
		}

		if( clonedDP.getFixingDate() != null )
		{
			dp.setFixingDate( clonedDP.getFixingDate() );
		}

		if( clonedDP.getFixingTenor() != null )
		{
			dp.setFixingTenor( clonedDP.getFixingTenor() );
		}

		if ( clonedDP.getNamespace() == null )
		{
			dp.setNamespace(clonedDP.getNamespace());
		}
		if ( clonedDP.getMaxShowAmount() != null )
		{
			dp.setMaxShowAmount(clonedDP.getMaxShowAmount());
		}
		if ( clonedDP.getMinDealtAmount() != null )
		{
			dp.setMinDealtAmount(clonedDP.getMinDealtAmount());
		}
		if ( clonedDP.getFillDealingPrice() != null )
		{
			dp.setFillDealingPrice(updateFXLegDealingPrice((FXLegDealingPrice) clonedDP.getFillDealingPrice(), (FXLegDealingPrice) dp.getFillDealingPrice()));
		}
		if ( clonedDP.getPriceElement() != null )
		{
			if ( clonedDP.getPriceElement() instanceof FXDealingPriceElementDependentC )
			{
				dp.setPriceElement(updateFXDealingPriceElementDependentC((FXDealingPriceElementDependentC) clonedDP.getPriceElement(), (FXDealingPriceElementDependentC) dp.getPriceElement()));
			}
			else
			{
				log.warn("ISEntityChangeSetHandler.updateFXLegDealingPrice : clonedDP.getPriceElement() instanceof FXDealingPriceElementDependentC is false");
				dp.setPriceElement(updateFXDealingPriceElement((FXDealingPriceElement) clonedDP.getPriceElement(), (FXDealingPriceElement) dp.getPriceElement()));
			}
		}
		if ( clonedDP.getPriceElements().size() > 0 )
		{
			log.warn("ISEntityChangeSetHandler.updateFXLegDealingPrice : clonedDP.getPriceElements().size() is " + clonedDP.getPriceElements().size());
			for ( DealingPriceElement clonedDpe : clonedDP.getPriceElements() )
			{
				DealingPriceElement dpe = null;
				for ( DealingPriceElement o : dp.getPriceElements() )
				{
					if ( o.getName().equals(clonedDpe.getName()) )
					{
						dpe = o;
						break;
					}
				}
				if ( dpe != null )
				{
					if ( clonedDpe instanceof FXDealingPriceElementDependentC )
					{
						updateFXDealingPriceElementDependentC((FXDealingPriceElementDependentC) clonedDpe, (FXDealingPriceElementDependentC) dpe);
					}
					else
					{
						updateFXDealingPriceElement((FXDealingPriceElement) clonedDpe, (FXDealingPriceElement) dpe);
					}
				}
				else
				{
					if ( clonedDpe instanceof FXDealingPriceElementDependentC )
					{
						dp.getPriceElements().add(updateFXDealingPriceElementDependentC((FXDealingPriceElementDependentC) clonedDpe, null));
					}
					else
					{
						dp.getPriceElements().add(updateFXDealingPriceElement((FXDealingPriceElement) clonedDpe, null));
					}
				}
			}
		}
		if ( clonedDP.getMarketRange() != null )
		{
			if ( dp.getFXRate() != null )
			{
				dp.setMarketRange(clonedDP.getMarketRange());
			}
			else
			{
				log.error("ISEntityChangeSetHandler.updateFXLegDealingPrice : dp.getFXRate is NULL");
			}
		}
		dp.setTier(clonedDP.getTier());
		dp.setTriggerPriceType(clonedDP.getTriggerPriceType());
		dp.setAcceptedPriceBidOfferMode(clonedDP.getAcceptedPriceBidOfferMode());
		dp.setStopLossTriggered(clonedDP.isStopLossTriggered());
		dp.setStopLossInitialTriggerRate(clonedDP.getStopLossInitialTriggerRate());
		dp.setStopLossTriggerRate(clonedDP.getStopLossTriggerRate());
		dp.setStopPrice(clonedDP.getStopPrice());
		dp.setStopLossTriggerPoints(clonedDP.getStopLossTriggerPoints());
		dp.setFilledAmount(clonedDP.getFilledAmount());
		dp.setAverageRate(clonedDP.getAverageRate());
		dp.setPriceImprovement(clonedDP.getPriceImprovement());
		dp.setAcceptedDealtCurrencyAmount(clonedDP.getAcceptedDealtCurrencyAmount());
		dp.setAcceptedSettledCurrencyAmount(clonedDP.getAcceptedSettledCurrencyAmount());
		dp.setVerifiedDealtCurrencyAmount(clonedDP.getVerifiedDealtCurrencyAmount());
		dp.setVerifiedSettledCurrencyAmount(clonedDP.getVerifiedSettledCurrencyAmount());
		dp.setOriginalOrderAmount(clonedDP.getOriginalOrderAmount());
		dp.setOriginalOrderRate(clonedDP.getOriginalOrderRate());
		dp.setTOBBidRateAtTerminalState(clonedDP.getTOBBidRateAtTerminalState());
		dp.setTOBOfferRateAtTerminalState(clonedDP.getTOBOfferRateAtTerminalState());
		return dp;
	}

	protected FXDealingPriceElement updateFXDealingPriceElement( FXDealingPriceElement clonedDpe, FXDealingPriceElement Dpe )
	{
		if ( Dpe == null )
		{
			Dpe = FXDealingFactory.newFXDealingPriceElement();
		}
		Dpe.setName(clonedDpe.getName());
		if ( clonedDpe.getNamespace() == null )
		{
			Dpe.setNamespace(clonedDpe.getNamespace());
		}
		if ( clonedDpe.getOrganization() != null )
		{
			Dpe.setOrganization(clonedDpe.getOrganization());
		}
		Dpe.setBidNotionalAmount(clonedDpe.getBidNotionalAmount());
		Dpe.setOfferNotionalAmount(clonedDpe.getOfferNotionalAmount());
		if ( clonedDpe.getBasePriceElement() != null )
		{
			if ( clonedDpe.getBasePriceElement() instanceof FXDealingPriceElementDependentC )
			{
				Dpe.setBasePriceElement(updateFXDealingPriceElementDependentC((FXDealingPriceElementDependentC) clonedDpe.getBasePriceElement(), (FXDealingPriceElementDependentC) Dpe.getBasePriceElement()));
			}
			else
			{
				log.warn("ISEntityChangeSetHandler.updateFXDealingPriceElement : clonedDpe.getBasePriceElement() instanceof FXDealingPriceElementDependentC is false");
				Dpe.setBasePriceElement(updateFXDealingPriceElement((FXDealingPriceElement) clonedDpe.getBasePriceElement(), (FXDealingPriceElement) Dpe.getBasePriceElement()));
			}
		}
		if ( clonedDpe.getPriceElements() != null && clonedDpe.getPriceElements().size() > 0 )
		{
			log.warn("ISEntityChangeSetHandler.updateFXDealingPriceElement : clonedDpe.getPriceElements().size() is " + clonedDpe.getPriceElements().size());
			for ( Object childDpe : clonedDpe.getPriceElements() )
			{
				DealingPriceElement dpe = null;
				for ( Object o : Dpe.getPriceElements() )
				{
					if ( ((DealingPriceElement) o).getName().equals(((DealingPriceElement) childDpe).getName()) )
					{
						dpe = (DealingPriceElement) o;
						break;
					}
				}
				if ( dpe == null )
				{
					if ( childDpe instanceof FXDealingPriceElementDependentC )
					{
						Dpe.getPriceElements().add(updateFXDealingPriceElementDependentC((FXDealingPriceElementDependentC) childDpe, null));
					}
					else
					{
						Dpe.getPriceElements().add(updateFXDealingPriceElement((FXDealingPriceElement) childDpe, null));
					}
				}
				else
				{
					if ( childDpe instanceof FXDealingPriceElementDependentC )
					{
						updateFXDealingPriceElementDependentC((FXDealingPriceElementDependentC) childDpe, (FXDealingPriceElement) dpe);
					}
					else
					{
						updateFXDealingPriceElement((FXDealingPriceElement) childDpe, (FXDealingPriceElement) dpe);
					}
				}
			}
		}
		if ( clonedDpe.getFXPrice() != null )
		{
			Dpe.setPrice(updateFXPrice(clonedDpe.getFXPrice(), Dpe.getFXPrice()));
		}
		return Dpe;
	}

	protected FXDealingPriceElement updateFXDealingPriceElementDependentC( FXDealingPriceElementDependentC clonedDpe, FXDealingPriceElement Dpe )
	{
		if ( Dpe == null )
		{
			Dpe = FXDealingFactory.newFXDealingPriceElementDependent();
		}
		Dpe.setName(clonedDpe.getName());
		if ( clonedDpe.getNamespace() == null )
		{
			Dpe.setNamespace(clonedDpe.getNamespace());
		}

		Dpe.setBidNotionalAmount(clonedDpe.getBidNotionalAmount());
		Dpe.setOfferNotionalAmount(clonedDpe.getOfferNotionalAmount());
		if ( clonedDpe.getOrganization() != null )
		{
			Dpe.setOrganization(clonedDpe.getOrganization());
		}
		if ( clonedDpe.getFXPrice() != null )
		{
			Dpe.setPrice(updateFXPrice(clonedDpe.getFXPrice(), Dpe.getFXPrice()));
		}
		return Dpe;
	}

	protected FXPrice updateFXPrice( FXPrice clonedFxPrice, FXPrice fxPrice )
	{
		if ( fxPrice == null )
		{
			fxPrice = new FXPriceC();
		}
		if ( clonedFxPrice.getBidFXRate() != null )
		{
			fxPrice.setBidFXRate(updateFXRate(clonedFxPrice.getBidFXRate(), fxPrice.getBidFXRate()));
		}
		if ( clonedFxPrice.getOfferFXRate() != null )
		{
			fxPrice.setOfferFXRate(updateFXRate(clonedFxPrice.getOfferFXRate(), fxPrice.getOfferFXRate()));
		}		
		if ( clonedFxPrice.getMidFXRate() != null )
		{
			fxPrice.setMidFXRate(updateFXRate(clonedFxPrice.getMidFXRate(), fxPrice.getMidFXRate()));
		}		
				
		return fxPrice;
	}

	protected FXRate updateFXRate( FXRate clonedFXRate, FXRate fxRate )
	{
		if ( fxRate == null )
		{
			fxRate = FXFactory.newFXRate();
		}
		fxRate.setBaseCurrency(clonedFXRate.getBaseCurrency());
		fxRate.setVariableCurrency(clonedFXRate.getVariableCurrency());
		boolean isSpotRecalculationEnabled = ISFactory.getInstance ().getISRfsMBean ().isSpotRateRecalculationEnabledForRFSTrades ();
		if ( isSpotRecalculationEnabled )
		{
			fxRate.setFXRateConvention(clonedFXRate.getFXRateConvention());
			fxRate.setFXRateBasis(clonedFXRate.getFXRateBasis());
			fxRate.setSpotRateField (  0.0 );
			fxRate.setForwardPoints ( clonedFXRate.getForwardPoints () );
			fxRate.setRate ( clonedFXRate.getRate () );
		}
		else
		{
			fxRate.setSpotRate ( clonedFXRate.getSpotRate () );
			fxRate.setRate ( clonedFXRate.getRate () );
			fxRate.setFXRateConvention(clonedFXRate.getFXRateConvention());
			fxRate.setFXRateBasis(clonedFXRate.getFXRateBasis());
		}
		if ( clonedFXRate.getSideRates() != null )
		{
			FXRate clonedBaseCcySideRate = clonedFXRate.getSideRates().getBaseCurrencySideRate();
			FXRate clonedVarCcySideRate = clonedFXRate.getSideRates().getVariableCurrencySideRate();
			FXRate baseSideRate = null;
			FXRate varSideRate = null;
			if ( fxRate.getSideRates() != null )
			{
				baseSideRate = fxRate.getSideRates().getBaseCurrencySideRate();
				varSideRate = fxRate.getSideRates().getVariableCurrencySideRate();
			}
			if ( clonedBaseCcySideRate != null )
			{
				if ( baseSideRate == null )
				{
					baseSideRate = FXFactory.newFXRate(clonedBaseCcySideRate.getBaseCurrency(), clonedBaseCcySideRate.getVariableCurrency(), clonedBaseCcySideRate.getFXRateConvention());
				}
				baseSideRate.setSpotRate(clonedBaseCcySideRate.getSpotRate());
			}
			if ( clonedVarCcySideRate != null )
			{
				if ( varSideRate == null )
				{
					varSideRate = FXFactory.newFXRate(clonedVarCcySideRate.getBaseCurrency(), clonedVarCcySideRate.getVariableCurrency(), clonedVarCcySideRate.getFXRateConvention());
				}
				varSideRate.setSpotRate(clonedVarCcySideRate.getSpotRate());
			}
			fxRate.setSideRates(baseSideRate, varSideRate);
		}
		return fxRate;
	}

	protected Expiration updateExpiration( Expiration clonedExpiration, Expiration expiration )
	{
		if ( expiration == null )
		{
			expiration = new ExpirationC();
		}
		expiration.setMilliSeconds(clonedExpiration.getMilliSeconds());
		expiration.setSeconds(clonedExpiration.getSeconds());
		return expiration;
	}

	public void updateRequestState( Request request, State state )
	{
		RequestStateFacade requestStateFacade = (RequestStateFacade) request.getFacade(RequestStateFacade.REQUEST_STATE_FACADE);
		String stateName = state.getName();

		if ( stateName.equalsIgnoreCase(ISConstantsC.REQUEST_STATE_INIT) )
		{
			requestStateFacade.setInitial();
		}
		else if ( stateName.equalsIgnoreCase(ISConstantsC.REQUEST_STATE_ACCEPTED) )
		{
			requestStateFacade.setAccepted();
		}
		else if ( stateName.equalsIgnoreCase(ISConstantsC.REQUEST_STATE_EXCEPTION) )
		{
			requestStateFacade.setException();
		}
		else if ( stateName.equalsIgnoreCase(ISConstantsC.REQUEST_STATE_PARTIAL) )
		{
			requestStateFacade.setPartial();
		}
		else if ( stateName.equalsIgnoreCase(ISConstantsC.REQUEST_STATE_EXECUTED) )
		{
			requestStateFacade.setAcceptVerified();
		}
		else if ( stateName.equalsIgnoreCase(ISConstantsC.REQUEST_STATE_DECLINED) )
		{
			requestStateFacade.setDeclined();
		}
		else if ( stateName.equalsIgnoreCase(ISConstantsC.REQUEST_STATE_CANCELLED) )
		{
			requestStateFacade.setCancelled();
		}
		else if ( stateName.equalsIgnoreCase(ISConstantsC.REQUEST_STATE_PENDING) )
		{
			requestStateFacade.setPending();
		}
		else if ( stateName.equalsIgnoreCase(ISConstantsC.REQUEST_STATE_ACTIVE) )
		{
			requestStateFacade.setActive();
		}
		else if ( stateName.equalsIgnoreCase(ISConstantsC.REQUEST_STATE_QUOTED) )
		{
			requestStateFacade.setQuoted();
		}
		else if ( stateName.equalsIgnoreCase(ISConstantsC.REQUEST_STATE_EXPIRED) )
		{
			requestStateFacade.setExpired();
		}
		else if ( stateName.equalsIgnoreCase(ISConstantsC.REQUEST_STATE_SUSPENDED) )
		{
			requestStateFacade.setSuspended();
		}
		else if ( stateName.equalsIgnoreCase(ISConstantsC.REQUEST_STATE_WITHDRAWN) )
		{
			requestStateFacade.setWithdrawn();
		}
		else
		{
			log.warn("ISEntityChangeSetHandler.updateRequestState: Invalid Request State - " + state);
		}
	}

	public void updateCreditUtilizationEvent( CreditUtilizationEvent registeredCUE, CreditUtilizationEvent cue )
	{
		registeredCUE.setCounterpartyCreditLimitRule(cue.getCounterpartyCreditLimitRule());
		registeredCUE.setCreditUtilizationCalculator(cue.getCreditUtilizationCalculator());
		registeredCUE.setUsedAmount(cue.getUsedAmount());
		registeredCUE.setReservedAmount(cue.getReservedAmount());
		registeredCUE.setTotalUsedAmount(cue.getTotalUsedAmount());
		registeredCUE.setTotalReservedAmount(cue.getTotalReservedAmount());
		registeredCUE.setAvailableReserveAmount(cue.getAvailableReserveAmount());
		registeredCUE.setLimitAmount(cue.getLimitAmount());
		registeredCUE.setLastAction(cue.getLastAction());
		registeredCUE.setErrorCode(cue.getErrorCode());
		registeredCUE.setStatus(cue.getStatus());

		registeredCUE.setPrincipal(cue.getPrincipal());
		registeredCUE.setPrice(cue.getPrice());
		registeredCUE.setPrincipalCurrency( cue.getPrincipalCurrency());
		registeredCUE.setPriceCurrency( cue.getPriceCurrency());
		registeredCUE.setTradeLegName( cue.getTradeLegName());
		registeredCUE.setTradeRate( cue.getTradeRate());
		registeredCUE.setOriginalPrincipalAmount(cue.getOriginalPrincipalAmount());
		registeredCUE.setOriginalPriceAmount(cue.getOriginalPriceAmount());

		registeredCUE.setPositionSnapshot(cue.getPositionSnapshot());
		registeredCUE.setMarketSnapshot(cue.getMarketSnapshot());
        registeredCUE.setCreditUtilization( cue.getCreditUtilization() );
        registeredCUE.setSpacesCreditUtilizationKey( cue.getSpacesCreditUtilizationKey() );
        registeredCUE.setPrincipalBalance ( cue.getPrincipalBalance () != null ? cue.getPrincipalBalance () : BigDecimal.ZERO );
        registeredCUE.setPriceBalance ( cue.getPriceBalance () != null ? cue.getPriceBalance () : BigDecimal.ZERO );
        registeredCUE.setEventType ( cue.getEventType () );
        registeredCUE.setUser ( cue.getUser () );
        registeredCUE.setNotes ( cue.getNotes () );
        registeredCUE.setTicket ( cue.getTicket () );
        registeredCUE.setOrderId ( cue.getOrderId () );
        registeredCUE.setExternalTransactionId ( cue.getExternalTransactionId () );
        registeredCUE.setOverUsed ( cue.isOverUsed () );
	}

	public void updateRequestOnOrderFill( Request dOrderRequest, Request request )
	{
		if ( dOrderRequest.getRequestPrices().size() > 0 )
		{
			for ( Object object : dOrderRequest.getRequestPrices() )
			{
				FXLegDealingPrice dp = (FXLegDealingPrice) object;
				if ( request.getRequestPrice(dp.getName()) == null )
				{
					log.warn("ISEntityChangeSetHandler.updateRequestOnOrderFill : Dealing price with name " + dp.getName() + " not found");
					request.getRequestPrices().add(updateFXLegDealingPrice(dp, (FXLegDealingPrice) request.getRequestPrice(dp.getName())));
				}
				else
				{
					updateFXLegDealingPrice(dp, (FXLegDealingPrice) request.getRequestPrice(dp.getName()));
				}
			}
		}
		State clonedRequestState = dOrderRequest.getWorkflowStateMap() != null ? dOrderRequest.getWorkflowStateMap().getState() : null;
		if ( clonedRequestState != null )
		{
			updateRequestState(request, clonedRequestState);
		}

		if ( dOrderRequest.getExecutionDateTime() != null )
		{
			request.setExecutionDateTime(dOrderRequest.getExecutionDateTime());
		}
	}

	public void updateRequestOnOrderUndoFill( Request dOrderRequest, Request request )
	{
		if ( dOrderRequest.getRequestPrices().size() > 0 )
		{
			for ( Object object : dOrderRequest.getRequestPrices() )
			{
				FXLegDealingPrice dp = (FXLegDealingPrice) object;
				if ( request.getRequestPrice(dp.getName()) == null )
				{
					log.warn("ISEntityChangeSetHandler.updateRequestOnOrderUndoFill : Dealing price with name " + dp.getName() + " not found");
					request.getRequestPrices().add(updateFXLegDealingPrice(dp, (FXLegDealingPrice) request.getRequestPrice(dp.getName())));
				}
				else
				{
					FXLegDealingPrice clonedDP = (FXLegDealingPrice) request.getRequestPrice(dp.getName());
					updateFXLegDealingPrice(dp, clonedDP);
					clonedDP.setAcceptedDealtCurrencyAmount(dp.getAcceptedDealtCurrencyAmount());
					clonedDP.setAcceptedSettledCurrencyAmount(dp.getAcceptedSettledCurrencyAmount());
				}
			}
		}
		State clonedRequestState = dOrderRequest.getWorkflowStateMap() != null ? dOrderRequest.getWorkflowStateMap().getState() : null;
		if ( clonedRequestState != null )
		{
			updateRequestState(request, clonedRequestState);
		}
		if ( dOrderRequest.getExecutionDateTime() != null )
		{
			request.setExecutionDateTime(dOrderRequest.getExecutionDateTime());
		}

	}

	public void updateRequestOnOrderFilled( Request dOrderRequest, Request request )
	{
		State clonedRequestState = dOrderRequest.getWorkflowStateMap() != null ? dOrderRequest.getWorkflowStateMap().getState() : null;
		if ( clonedRequestState != null )
		{
			updateRequestState(request, clonedRequestState);
		}

		if ( dOrderRequest.getExecutionDateTime() != null )
		{
			request.setExecutionDateTime(dOrderRequest.getExecutionDateTime());
		}
	}

	public void updateRequestOnOrderCancel( Request dOrderRequest, Request request )
	{
		State clonedRequestState = dOrderRequest.getWorkflowStateMap() != null ? dOrderRequest.getWorkflowStateMap().getState() : null;
		if ( clonedRequestState != null )
		{
			updateRequestState(request, clonedRequestState);
		}

		if ( dOrderRequest.getExecutionDateTime() != null )
		{
			request.setExecutionDateTime(dOrderRequest.getExecutionDateTime());
		}

		if ( dOrderRequest.getCancellationTime() != null )
		{
			request.setCancellationTime(dOrderRequest.getCancellationTime());
		}
		request.setCancelReceived(dOrderRequest.isCancelReceived());

		if ( dOrderRequest.getCancelledBy() != null )
		{
			request.setCancelledBy(dOrderRequest.getCancelledBy());
		}

		FXLegDealingPrice dDP = (FXLegDealingPrice)dOrderRequest.getRequestPrice(ISCommonConstants.SINGLE_LEG);
		FXLegDealingPrice dp = (FXLegDealingPrice)request.getRequestPrice(ISCommonConstants.SINGLE_LEG);

		dp.setTOBBidRateAtTerminalState( dDP.getTOBBidRateAtTerminalState() );
		dp.setTOBOfferRateAtTerminalState( dDP.getTOBOfferRateAtTerminalState() );
		
		if( dOrderRequest.getOrderCancelReason() != null )
		{
			request.setOrderCancelReason( dOrderRequest.getOrderCancelReason() );
		}
	}

	public void updateRequestOnOrderFailed( Request dOrderRequest, Request request )
	{
		State clonedRequestState = dOrderRequest.getWorkflowStateMap() != null ? dOrderRequest.getWorkflowStateMap().getState() : null;
		if ( clonedRequestState != null )
		{
			updateRequestState(request, clonedRequestState);
		}
		if ( dOrderRequest.getExecutionDateTime() != null )
		{
			request.setExecutionDateTime(dOrderRequest.getExecutionDateTime());
		}
	}

	public void updateRequestOnESPPrimeOrderUpdate( Request dOrderRequest, Request request )
	{
		if ( dOrderRequest.getRequestPrices().size() > 0 )
		{
			for ( Object object : dOrderRequest.getRequestPrices() )
			{
				FXLegDealingPrice dp = (FXLegDealingPrice) object;
				if ( request.getRequestPrice(dp.getName()) == null )
				{
					log.warn("ISEntityChangeSetHandler.updateRequestOnESPPrimeOrderUpdate : Dealing price with name " + dp.getName() + " not found");
					request.getRequestPrices().add(updateFXLegDealingPrice(dp, (FXLegDealingPrice) request.getRequestPrice(dp.getName())));
				}
				else
				{
					updateFXLegDealingPrice(dp, (FXLegDealingPrice) request.getRequestPrice(dp.getName()));
				}
			}
		}
		State clonedRequestState = dOrderRequest.getWorkflowStateMap() != null ? dOrderRequest.getWorkflowStateMap().getState() : null;
		if ( clonedRequestState != null )
		{
			updateRequestState(request, clonedRequestState);
		}

		if ( dOrderRequest.getExecutionDateTime() != null )
		{
			request.setExecutionDateTime(dOrderRequest.getExecutionDateTime());
		}
	}

	public void updateRequestOnMakerOrderUpdate( Request dOrderRequest, Request request )
	{
		if ( dOrderRequest.getRequestPrices().size() > 0 )
		{
			for ( Object object : dOrderRequest.getRequestPrices() )
			{
				FXLegDealingPrice dp = (FXLegDealingPrice) object;
				if ( request.getRequestPrice(dp.getName()) == null )
				{
					log.warn("ISEntityChangeSetHandler.updateRequestOnMakerOrderUpdate : Dealing price with name " + dp.getName() + " not found");
					request.getRequestPrices().add(updateFXLegDealingPrice(dp, (FXLegDealingPrice) request.getRequestPrice(dp.getName())));
				}
				else
				{
					updateFXLegDealingPrice(dp, (FXLegDealingPrice) request.getRequestPrice(dp.getName()));
				}
			}
		}
		State clonedRequestState = dOrderRequest.getWorkflowStateMap() != null ? dOrderRequest.getWorkflowStateMap().getState() : null;
		if ( clonedRequestState != null )
		{
			updateRequestState(request, clonedRequestState);
		}

		if ( dOrderRequest.getExecutionDateTime() != null )
		{
			request.setExecutionDateTime(dOrderRequest.getExecutionDateTime());
		}
	}

	public void updateTradeRequestOnExpire( Request dTradeRequest, Request request )
	{
		if ( dTradeRequest.getRequestPrices().size() > 0 )
		{
			for ( Object object : dTradeRequest.getRequestPrices() )
			{
				FXLegDealingPrice dp = (FXLegDealingPrice) object;
				if ( request.getRequestPrice(dp.getName()) == null )
				{
					log.warn("ISEntityChangeSetHandler.updateTradeRequestOnExpire : Dealing price with name " + dp.getName() + " not found");
					request.getRequestPrices().add(updateFXLegDealingPrice(dp, (FXLegDealingPrice) request.getRequestPrice(dp.getName())));
				}
				else
				{
					updateFXLegDealingPrice(dp, (FXLegDealingPrice) request.getRequestPrice(dp.getName()));
				}
			}
		}
		State clonedRequestState = dTradeRequest.getWorkflowStateMap() != null ? dTradeRequest.getWorkflowStateMap().getState() : null;
		if ( clonedRequestState != null )
		{
			updateRequestState(request, clonedRequestState);
		}

		if ( dTradeRequest.getExecutionDateTime() != null )
		{
			request.setExecutionDateTime(dTradeRequest.getExecutionDateTime());
		}
	}

	public void updateTradeOnAmend( Trade dTrade, Trade trade )
	{
		
		trade.setCounterpartyA(dTrade.getCounterpartyA());
		trade.setCounterpartyB(dTrade.getCounterpartyB());
		trade.setCounterpartyC(dTrade.getCounterpartyC());
		trade.setCounterpartyD(dTrade.getCounterpartyD());
		trade.setTakerCounterparty(dTrade.getTakerCounterparty());
		trade.setMakerCounterparty(dTrade.getMakerCounterparty());
		updateCptyTrades(dTrade, trade);
		TradeStateFacade tsf = (TradeStateFacade) trade.getFacade(TradeStateFacade.TRADE_STATE_FACADE);
		State dTradeState = dTrade.getWorkflowStateMap() != null ? dTrade.getWorkflowStateMap().getState() : null;
		if ( dTradeState != null && !tsf.isNet() && !tsf.isNetted())
		{
			tsf.setTradeState(dTradeState);
			WorkflowStateMap cWsm = dTrade.getWorkflowStateMap();
			if ( cWsm.getWorkflowCodeArgument() != null )
			{
				WorkflowStateMap wsm = trade.getWorkflowStateMap();
				wsm.setWorkflowCode(cWsm.getWorkflowCode());
				wsm.setWorkflowCodeArgument(cWsm.getWorkflowCodeArgument());
			}
		}
		if ( dTrade.getExecutionDateTime() != null )
		{
			trade.setExecutionDateTime(dTrade.getExecutionDateTime());
		}
        trade.setCounterpartyALEI( dTrade.getCounterpartyALEI() );
        trade.setCounterpartyBLEI( dTrade.getCounterpartyBLEI() );
        trade.setUPI( dTrade.getUPI() );
        trade.setUSI( dTrade.getUSI() );
        trade.setSDRAckId( dTrade.getSDRAckId() );
        trade.setSDRTradeStatus( dTrade.getSDRTradeStatus() );
        trade.setClearingHouseAckId( dTrade.getClearingHouseAckId() );
        trade.setClearingHouseTradeStatus( dTrade.getClearingHouseTradeStatus() );
        trade.setCreditLimitHubId(dTrade.getCreditLimitHubId());
        trade.setCreditLimitHubStatus(dTrade.getCreditLimitHubStatus());
        trade.setUSILinkId( dTrade.getUSILinkId() );
        trade.setUTILinkId( dTrade.getUTILinkId() );
        trade.setContractId( dTrade.getContractId() );
        trade.setBlockTrade( dTrade.isBlockTrade() );
        trade.setLargeSizeTrade( dTrade.isLargeSizeTrade() );
        trade.setAcceptedPriceSkewed( dTrade.isAcceptedPriceSkewed() );
        if(dTrade.getTradeLeg("fxLeg") != null){        
        	trade.setTradeLeg("fxLeg", dTrade.getTradeLeg("fxLeg"));
        }
	}

	public void updateTradeOnCancel( Trade dTrade, Trade trade )
	{
		TradeStateFacade tsf = (TradeStateFacade) trade.getFacade(TradeStateFacade.TRADE_STATE_FACADE);
		State dTradeState = dTrade.getWorkflowStateMap() != null ? dTrade.getWorkflowStateMap().getState() : null;
		if ( dTradeState != null )
		{
			tsf.setTradeState(dTradeState);
			WorkflowStateMap cWsm = dTrade.getWorkflowStateMap();
			if ( cWsm.getWorkflowCodeArgument() != null )
			{
				WorkflowStateMap wsm = trade.getWorkflowStateMap();
				wsm.setWorkflowCode(cWsm.getWorkflowCode());
				wsm.setWorkflowCodeArgument(cWsm.getWorkflowCodeArgument());
			}
		}
		if ( dTrade.getExecutionDateTime() != null )
		{
			trade.setExecutionDateTime(dTrade.getExecutionDateTime());
		}
	}

	public void updateOrderRequestOnPendingCancel( Request dOrderRequest, Request request )
	{
		State clonedRequestState = dOrderRequest.getWorkflowStateMap() != null ? dOrderRequest.getWorkflowStateMap().getState() : null;
		if ( clonedRequestState != null )
		{
			updateRequestState(request, clonedRequestState);
		}

		if ( dOrderRequest.getExecutionDateTime() != null )
		{
			request.setExecutionDateTime(dOrderRequest.getExecutionDateTime());
		}
		request.setCancelReceived(dOrderRequest.isCancelReceived());
	}

	public void updateRequestEvents( Collection<RequestEvent> deSerializedEvents, Request requests )
	{
		List<RequestEvent> events = new ArrayList<RequestEvent>();
		for ( RequestEvent deserializedEvent : deSerializedEvents )
		{
			RequestEvent event = new RequestEventC();
			if( deserializedEvent.getName() != null )
			{
				event.setName(deserializedEvent.getName());
			}
			if( deserializedEvent.getParam1() != null )
			{
				event.setParam1(deserializedEvent.getParam1());
			}
			if( deserializedEvent.getParam2() != null )
			{
				event.setParam2(deserializedEvent.getParam2());
			}
			if( deserializedEvent.getParam3() != null )
			{
				event.setParam3(deserializedEvent.getParam3());
			}
			if( deserializedEvent.getParam4() != null )
			{
				event.setParam4(deserializedEvent.getParam4());
			}
			if( deserializedEvent.getParam5() != null )
			{
				event.setParam5(deserializedEvent.getParam5());
			}
			if( deserializedEvent.getParam6() != null )
			{
				event.setParam6(deserializedEvent.getParam6());
			}
			if( deserializedEvent.getParam7() != null )
			{
				event.setParam7(deserializedEvent.getParam7());
			}
			if( deserializedEvent.getParam8() != null )
			{
				event.setParam8(deserializedEvent.getParam8());
			}
			event.setSeqID( deserializedEvent.getSeqID() );
			event.setRequest(requests);
			events.add(event);
		}

		if( events.size() > 0 )
		{
			requests.setNewRequestEvents(events);
		}
	}

	public void updateTradeOnMiFiDAmend( Trade dTrade, Trade trade )
	{
		trade.setMiFIDTradeParams( dTrade.getMiFIDTradeParams() );

		if ( dTrade.getMiFIDVenueExecutionTime() != null )
		{
			trade.setMiFIDVenueExecutionTime(dTrade.getMiFIDVenueExecutionTime());
		}

		trade.setISINLinkId( dTrade.getISINLinkId() );
		trade.setExecutionVenue( dTrade.getExecutionVenue() );
		trade.setMTFTrade( dTrade.isMTFTrade() );

		Collection<FXLeg> dlegs = dTrade.getTradeLegs();

		for( FXLeg dleg : dlegs)
		{
			Collection<FXLeg> legs = trade.getTradeLegs();
			for( FXLeg leg : legs)
			{
				if( leg.getGUID().equals( dleg.getGUID() ))
				{
					leg.setISIN( dleg.getISIN() );
					leg.setUPI( dleg.getUPI() );
					break;
				}
			}
		}
	}
}
