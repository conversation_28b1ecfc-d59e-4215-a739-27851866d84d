package com.integral.dbservice.is;

import com.integral.dbservice.*;
import com.integral.pipeline.*;
import com.integral.pipeline.metrics.MetricsManager;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Nov 8, 2010
 * Time: 11:55:12 AM
 * A Pipeline functor that creates an event handler pipeline with Mock Processor.
 */
public class MockEventProcessingPipelineSetupFunctor implements PipelineSetupFunctor {

    private DBService dbService;
    private ProcessorDescriptor errorHandler;

    public MockEventProcessingPipelineSetupFunctor(DBService dbService) {
        this.dbService = dbService;
        errorHandler = new ProcessorDescriptor("PersistToDBPipelineErrorHandler", new DBPersistPipelineErrorProcessor(dbService));
    }

    public void setErrorHandler(ProcessorDescriptor errorHandler){
        this.errorHandler = errorHandler;
    }

    public Pipeline createPipeline() {

        //Create all processors that will be part of this pipeline.
        ProcessorDescriptor deSerializeFromText = new ProcessorDescriptor("DeserializeFromText", new DeSerializeFromJsonProcessor(dbService.getConfig()));
        ProcessorDescriptor startTransaction = new ProcessorDescriptor("StartTransaction", new StartTransactionProcessor());
        ProcessorDescriptor eventHandler = new ProcessorDescriptor("EventHandler", new MockEventProcessor());
        ProcessorDescriptor endTransaction = new ProcessorDescriptor("EndTransaction", new EndTransactionProcessor(dbService.getTransactionLog()));

        //Setup the chain of processors in a pipeline.
        // start transaction -> handle event -> end transaction
        deSerializeFromText.setNext(startTransaction).setNext(eventHandler).setNext(endTransaction);

        //Set the endTransaction processor to be errorHandler - to ensure that we close the transaction even in case
        //of errors.
        Pipeline p = new Pipeline("PersistToDBPipeline", deSerializeFromText, errorHandler);
        MetricsManager.instance().register(p);
        return p;
    }

    public void reclaim(ProcessorDescriptor pipeline) {
        //Do nothing
    }

}