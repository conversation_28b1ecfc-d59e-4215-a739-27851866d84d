package com.integral.dbservice.is;

import com.integral.dbservice.TransactionLogMessage;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationFactory;
import com.integral.finance.creditLimit.db.CreditDataQueryServiceFactory;
import com.integral.finance.trade.Trade;
import com.integral.is.common.util.ISTransactionManager;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageEvent;
import com.integral.persistence.Entity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import com.integral.session.IdcTransaction;
import com.integral.time.IdcDate;
import com.integral.workflow.WorkflowStateMap;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <PERSON><PERSON> having static methods for Credit Event processing.
 * 
 * <AUTHOR>
 * 
 */
public class CreditEventHandler
{
    private static Log log = LogFactory.getLog( CreditEventHandler.class );
	private static ISEntityChangeSetHandler entityChangeSetHandler = ISEntityChangeSetHandler.instance();
    public static final AtomicInteger BASE_INDEX = new AtomicInteger(600);

    /**
	 * UndoCredit Event Handler
	 * <AUTHOR>
	 *
	 */
	public static class UndoCreditEventHandler extends Processor
	{
		public static String EVENT = "undoCredit";
		public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public UndoCreditEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
		public PipelineMessage process( PipelineMessage msg )
		{
			try
			{
				TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
				if ( tlm.entity instanceof Collection )
				{
					List<Entity> entities = ( List<Entity> ) tlm.entity;
					Trade cTrade = ( Trade ) entities.get( 0 );
					Trade trade = cache.getTrade( cTrade, EVENT );
					ISTransactionManager.setUser( trade.getEntryUser() );
					IdcTransaction tx = ISTransactionManager.startTransaction( EVENT );

                    undoCredit( trade, entities, cache );

//					ISTransactionManager.endTransaction( tx, EVENT );
				}
			}
			catch ( Exception e )
			{
				throw new PipelineException( e );
			}

			return msg;
		}
	}
	
	/**
	 * UpdateMultiFillCredit EventHandler.
	 * Right now it invokes takeCredit workflows
	 * <AUTHOR>
	 *
	 */
	public static class UpdateMultiFillCreditEventHandler extends Processor
	{
		public static String EVENT = "updateMultiFillCredit";
		public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public UpdateMultiFillCreditEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
		public PipelineMessage process( PipelineMessage msg )
		{
			try
			{
				TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
				if ( tlm.entity instanceof Collection )
				{
					List<Entity> entities = ( List<Entity> ) tlm.entity;
					Trade cTrade = ( Trade ) entities.get( 0 );
					Trade trade = cache.getTrade( cTrade, EVENT );
					
					ISTransactionManager.setUser( trade.getEntryUser() );
					IdcTransaction tx = ISTransactionManager.startTransaction( EVENT );
					
					trade = (Trade)tx.getRegisteredObject( trade );
					takeCredit( trade, entities, cache );
					
//					ISTransactionManager.endTransaction( tx, EVENT );
					
				}
			}
			catch ( Exception e )
			{
				throw new PipelineException( e );
			}

			return msg;
		}
	}

	/*
	 * Calls undo and take credit workflow for trade cancel and amend workflow.
	 */
	public static class AmendEventHandler extends Processor
	{
		public static String EVENT = "tradeAmendAndCredit";
		public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public AmendEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
		public PipelineMessage process( PipelineMessage msg )
		{
			try
			{
            	TransactionLogMessage tlm  = ( TransactionLogMessage ) msg;
            	if(tlm.entity instanceof Collection) 
            	{
            		List<Entity> entities = (List<Entity>) tlm.entity;
            		
					Trade cTrade = ( Trade ) entities.get( 0 );
					Trade trade = cache.getTrade( cTrade, EVENT );
    				
    				ISTransactionManager.setUser( trade.getEntryUser() );
					IdcTransaction tx = ISTransactionManager.startTransaction( EVENT );
	
					List<Entity> undoCws = new ArrayList<Entity>();
					undoCws.add(null);
					List<Entity> takeCws = new ArrayList<Entity>();
					takeCws.add(null); // set first entity as null
					// Go through credit workflow states and call undo and take separately
					for ( int i = 1; i < entities.size() ; i++ )
					{
		        		CreditLimitWorkflowState cws = (CreditLimitWorkflowState)entities.get( i );
						CreditLimitWorkflowStateFacade fcd = ( CreditLimitWorkflowStateFacade ) cws
						.getFacade( CreditLimitWorkflowStateFacadeC.class.getName() );
						if( fcd.isCreditUndone() )
							undoCws.add( cws );
						else
							takeCws.add( cws );
					}
	
					trade = (Trade)trade.getRegisteredObject();
					
					undoCredit( trade, undoCws, cache );
					if( tlm.properties != null && 
							tlm.properties.get("CreditEvent") != null &&  
							CreditMessageEvent.UPDATEAMOUNT.getName().equals(tlm.properties.get("CreditEvent")))
						updateCredit( trade, takeCws, cache );
					else
						takeCredit( trade, takeCws, cache );
					
//					ISTransactionManager.endTransaction( tx, EVENT );
            	}
			}
			catch ( Exception e )
			{
				throw new PipelineException( e );
			}

			return msg;
		}
	}
	
	/**
	 * Take Credit processing.
	 * It is called from TradeAcceptance handlers of ESP and RFS Trade Handlers.
	 * @param trade trade
	 * @param entities entities
     * @param cache cache
	 */
	public static void takeCredit(Trade trade, List<Entity> entities, ISPersistenceServiceCacheManager cache)
	{
        // Now all entites are CreditWorkflowStateMap.
		IdcTransaction tx = ISTransactionManager.getTransaction();
        WorkflowStateMap wsm = trade.getWorkflowStateMap();
        for(int i = 1; i < entities.size() ; i++)
        {
        	Entity ent = entities.get( i );
        	if( ent instanceof CreditLimitWorkflowState)
        	{
        		CreditLimitWorkflowState cws = (CreditLimitWorkflowState)ent;
        		
                List<CreditUtilizationEvent> cues = new ArrayList<CreditUtilizationEvent>( 2 );
                List<CreditUtilizationEvent> passiveCues = new ArrayList<CreditUtilizationEvent>( 2 );
                for ( CreditUtilizationEvent event : cws.getCreditUtilizationEvents() )
                {
                    CreditUtilizationEvent cue = ( CreditUtilizationEvent ) event;
                    if(cue.getStatus() == Entity.PASSIVE_STATUS )
                    {
                    	passiveCues.add( cue ); // do not add register object. The register object will get added further below.
                    }
                    else
                    {
                    	cues.add( cue );
                    }
                    	
                }
                cws.setCreditUtilizationEvents( (Collection) cues ); //add all active cues.
                List<CreditUtilizationEvent> cachedCUEforPersistence = new ArrayList<CreditUtilizationEvent>( 2 );
                List<CreditUtilizationEvent> cachedCUEForNotification = new ArrayList<CreditUtilizationEvent>( 2 );
                if( passiveCues.size() > 0 ) // if one or more cues are in passive state then run change state handler
                {
//                	log.warn( "No of passive credit utilizations are " + passiveCues.size ());
            		CreditLimitWorkflowState cachedCWS = (CreditLimitWorkflowState)wsm.getWorkflowState( cws.getName() );
                    if(cachedCWS != null)
            		{
            			log.info("Tenant:" + cache.getPeerVirtualServer() +". CreditEventHandler.TakeCredit Found CreditLimitWorkflowState for " + cws.getName() + " in trade " + trade.getTransactionID());
    	            	Iterator cueIt = passiveCues.iterator();
    	            	Iterator cacheCueIt = cachedCWS.getCreditUtilizationEvents().iterator();
    	                while( cueIt.hasNext() && cacheCueIt.hasNext() )
    	            	{
    	            		CreditUtilizationEvent cue = (CreditUtilizationEvent) cueIt.next();
    	            		CreditUtilizationEvent cachedCue = (CreditUtilizationEvent) cacheCueIt.next();
                            boolean alreadyPassive = cachedCue.getStatus() == Entity.PASSIVE_STATUS && cachedCue.getObjectID() > 0;
                            CreditUtilizationEvent registeredCachedCue = (CreditUtilizationEvent) cachedCue.getRegisteredObject();
    	                    
    	                    // set Counterparty Rule and Calculator on serialized entity so that changeset handler can work properly
    	                    cue.setCounterpartyCreditLimitRule( registeredCachedCue.getCounterpartyCreditLimitRule() );

                            //CU was persisted already on the OA. So why set something on CU here ? So commenting out the below statement
    	                    //cue.getCreditUtilization().setCreditLimitRule( registeredCachedCue.getCreditUtilization().getCreditLimitRule() );
    	                    
    	                    entityChangeSetHandler.updateCreditUtilizationEvent( registeredCachedCue, cue );
    	            		cachedCUEforPersistence.add( registeredCachedCue );

                            if ( !alreadyPassive )
                            {
                                cachedCUEForNotification.add( registeredCachedCue );
                            }
                        }
    	                cws.getCreditUtilizationEvents().addAll( cachedCUEforPersistence );
    	                
            		}
                }
//                log.warn( "No of credit util events in cws are " + cws.getCreditUtilizationEvents().size() );
                if ( wsm != null ) // It should never be null
                {
                    wsm = ( WorkflowStateMap ) tx.getRegisteredObject( wsm );
                    trade.setWorkflowStateMap( wsm );
                	cws = (CreditLimitWorkflowState)tx.getRegisteredObject( cws );
                    wsm.setWorkflowState( cws.getName(), cws );
                    if ( wsm.getNamespace() == null )
                    {
                        cws.setNamespace( trade.getNamespace() );
                    }
                }
            	processCreditUtilizations( trade, cues, cws, CreditMessageEvent.USE, cache);
            	if( passiveCues.size() > 0 )
            		processCreditUtilizations( trade, cachedCUEForNotification, cws, CreditMessageEvent.REMOVE, cache );
            		
        	}
        }
	}
	
	
	/**
	 * Update Credit processing.
	 * @param trade trade
	 * @param entities entities
     * @param cache cache
	 */
	public static void updateCredit(Trade trade, List<Entity> entities, ISPersistenceServiceCacheManager cache)
	{
        // Now all entites are CreditWorkflowStateMap.
		IdcTransaction tx = ISTransactionManager.getTransaction();
        WorkflowStateMap wsm = trade.getWorkflowStateMap();
        for(int i = 1; i < entities.size() ; i++)
        {
        	Entity ent = entities.get( i );
        	if( ent instanceof CreditLimitWorkflowState)
        	{
        		CreditLimitWorkflowState cws = (CreditLimitWorkflowState)ent;
        		
                List<CreditUtilizationEvent> cues = new ArrayList<CreditUtilizationEvent>( 2 );
                for ( CreditUtilizationEvent event : cws.getCreditUtilizationEvents() )
                {
                    CreditUtilizationEvent cue = ( CreditUtilizationEvent ) event;
                   	cues.add( cue );
                }
                List<CreditUtilizationEvent> cachedCUEForPersistence = new ArrayList<CreditUtilizationEvent>( 2 );
                List<CreditUtilizationEvent> cachedCUEForNotification = new ArrayList<CreditUtilizationEvent>( 2 );
                if( cues.size() > 0 )
                {
            		CreditLimitWorkflowState cachedCWS = (CreditLimitWorkflowState)wsm.getWorkflowState( cws.getName() );
                    if(cachedCWS != null)
            		{
            			log.info("Tenant:" + cache.getPeerVirtualServer() +". CreditEventHandler.UpdateCredit Found CreditLimitWorkflowState for " + cws.getName() + " in trade " + trade.getTransactionID());
    	            	Iterator cueIt = cues.iterator();
    	            	Iterator cacheCueIt = cachedCWS.getCreditUtilizationEvents().iterator();
    	                while( cueIt.hasNext() && cacheCueIt.hasNext() )
    	            	{
    	            		CreditUtilizationEvent cue = (CreditUtilizationEvent) cueIt.next();
    	            		CreditUtilizationEvent cachedCue = (CreditUtilizationEvent) cacheCueIt.next();
                            boolean alreadyPassive = cachedCue.getStatus() == Entity.PASSIVE_STATUS && cachedCue.getObjectID() > 0;
                            CreditUtilizationEvent registeredCachedCue = (CreditUtilizationEvent) cachedCue.getRegisteredObject();
    	                    
    	                    cue.setCounterpartyCreditLimitRule( registeredCachedCue.getCounterpartyCreditLimitRule() );
                            //CU was persisted already on the OA. So why set something on CU here ? So commenting out the below statement
                            //cue.getCreditUtilization().setCreditLimitRule(registeredCachedCue.getCreditUtilization().getCreditLimitRule(););
    	                    
    	                    entityChangeSetHandler.updateCreditUtilizationEvent( registeredCachedCue, cue );
    	            		cachedCUEForPersistence.add( registeredCachedCue );

                            if ( !alreadyPassive )
                            {
                                cachedCUEForNotification.add( registeredCachedCue );
                            }
                        }
                        cachedCWS.setCreditUtilizationEvents( (Collection) cachedCUEForPersistence );
            		}
                	processCreditUtilizations( trade, cachedCUEForNotification, cachedCWS, CreditMessageEvent.UPDATEAMOUNT, cache);
                }
        	}
        }
	}
	

	/**
	 * Process undoCredit event on Trade.
	 * @param trade
	 * @param entities
     * @param cache cache
	 */
	public static void undoCredit( Trade trade, List<Entity> entities, ISPersistenceServiceCacheManager cache )
	{
		// Now all entites are CreditUtilizationEvents.
        WorkflowStateMap wsm = trade.getWorkflowStateMap();
		for ( int i = 1; i < entities.size(); i++ )
		{
        	Entity ent = entities.get( i );
        	if( ent instanceof CreditLimitWorkflowState)
        	{
        		CreditLimitWorkflowState cws = (CreditLimitWorkflowState)ent;
        		CreditLimitWorkflowState cachedCWS = (CreditLimitWorkflowState)wsm.getWorkflowState( cws.getName() );
                if(cachedCWS == null)
        		{
        			log.error("CreditEventHandler.UndoCredit Could not found CreditLimitWorkflowState for " + cws.getName() + " in trade " + trade.getTransactionID());
        			continue;
        		}
                CreditLimitWorkflowState registeredCachedCWS = (CreditLimitWorkflowState) cachedCWS.getRegisteredObject();
                List<CreditUtilizationEvent> cachedCUEForPersistence = new ArrayList<CreditUtilizationEvent>( 2 );
                List<CreditUtilizationEvent> cachedCUEForNotification = new ArrayList<CreditUtilizationEvent>( 2 );
            	Iterator cueIt = cws.getCreditUtilizationEvents().iterator();
            	Iterator cacheCueIt = registeredCachedCWS.getCreditUtilizationEvents().iterator();
                while( cueIt.hasNext() && cacheCueIt.hasNext() )
            	{
            		CreditUtilizationEvent cue = (CreditUtilizationEvent) cueIt.next();
            		CreditUtilizationEvent cachedCue = (CreditUtilizationEvent) cacheCueIt.next();
                    boolean alreadyPassive = cachedCue.getStatus() == Entity.PASSIVE_STATUS && cachedCue.getObjectID() > 0 ;
                    CreditUtilizationEvent registeredCachedCue = (CreditUtilizationEvent) cachedCue.getRegisteredObject();
                    
                    // set Counterparty Rule and Calculator on serialized entity so that changeset handler can work properly
                    cue.setCounterpartyCreditLimitRule( registeredCachedCue.getCounterpartyCreditLimitRule() );

                    //CU was persisted already on the OA. So why set something on CU here ? So commenting out the below statement
                    //cue.getCreditUtilization().setCreditLimitRule( registeredCachedCue.getCreditUtilization().getCreditLimitRule() );
                    
                    entityChangeSetHandler.updateCreditUtilizationEvent( registeredCachedCue, cue );
            		cachedCUEForPersistence.add( registeredCachedCue );

                    // add it for notification if not already passive
                    if ( !alreadyPassive )
                    {
                        cachedCUEForNotification.add( registeredCachedCue );
                    }
                }
    			
				CreditLimitWorkflowStateFacade fcd = ( CreditLimitWorkflowStateFacade ) registeredCachedCWS
				.getFacade( CreditLimitWorkflowStateFacadeC.class.getName() );
				fcd.setCreditUndone();
			
				registeredCachedCWS.getCreditUtilizationEvents().clear();
				registeredCachedCWS.getCreditUtilizationEvents().addAll( cachedCUEForPersistence );
				
            	processCreditUtilizations( trade, cachedCUEForNotification, registeredCachedCWS, CreditMessageEvent.REMOVE, cache);
        	}
		}
	}


	/**
	 * Process CreditUtilizationEvent and setups CommitHandler and Remote Functor
	 * @param trade trade
	 * @param cues credit utilization events
	 * @param cws credit limit workflow state
	 * @param event message event
     * @param cache cache
	 */
	private static void processCreditUtilizations( Trade trade,
			List<CreditUtilizationEvent> cues,
			CreditLimitWorkflowState cws,
			MessageEvent event,
            ISPersistenceServiceCacheManager cache)
	{
		for ( CreditUtilizationEvent cue : cues )
		{
			CreditUtilization seCu = cue.getCreditUtilization();
            if( !CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationLookupSpacesEnabled(cue.getNamespace().getShortName()))
            {
                CreditUtilization dbCu = ( CreditUtilization ) ReferenceDataCacheC.getInstance().getEntityByObjectId(seCu.getObjectID(), CreditUtilization.class );
                cue.setCreditUtilization( dbCu );
            }
		}
    }
}
