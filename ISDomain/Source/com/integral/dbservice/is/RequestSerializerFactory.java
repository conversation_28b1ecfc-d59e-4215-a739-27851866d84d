package com.integral.dbservice.is;

import com.integral.serialize.EntityHolder;
import com.integral.serialize.RequestSerializer;
import com.integral.serialize.SerializerMap;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class RequestSerializerFactory {

    private static RequestSerializerFactory instance = null;
    private final Map<String, RequestSerializer> requestSerializerMap = new ConcurrentHashMap<String, RequestSerializer>();

    static
    {
        instance = new RequestSerializerFactory();
    }

    public RequestSerializerFactory() {
        requestSerializerMap.put( "DEFAULT", new RequestSerializer( new String[]{"Request", "R"}, EntityHolder.class, new SerializerMap() ) );

        requestSerializerMap.put( RFSTradeHandlers.TradeAcceptanceEventHandler.EVENT,new RequestSerializer(new String[]{"Request", "R"}, EntityHolder.class, new SerializerMap(), RequestSerializer.RequestSerializationType.RFSTradeAccept ) );
        requestSerializerMap.put( RFSTradeHandlers.TradeAcceptanceAndTakeCreditEventHandler.EVENT,new RequestSerializer(new String[]{"Request", "R"}, EntityHolder.class, new SerializerMap(), RequestSerializer.RequestSerializationType.RFSTradeAccept ) );
        requestSerializerMap.put( RFSTradeHandlers.TradePendingVerificationEventHandler.EVENT,new RequestSerializer(new String[]{"Request", "R"}, EntityHolder.class, new SerializerMap(), RequestSerializer.RequestSerializationType.RFSTradePending ) );
        requestSerializerMap.put( RFSTradeHandlers.TradeFailedEventHandler.EVENT,new RequestSerializer(new String[]{"Request", "R"}, EntityHolder.class, new SerializerMap(), RequestSerializer.RequestSerializationType.RFSTradeFailed ) );
        requestSerializerMap.put( RFSTradeHandlers.TradeRequestExpireHandler.EVENT,new RequestSerializer(new String[]{"Request", "R"}, EntityHolder.class, new SerializerMap(), RequestSerializer.RequestSerializationType.RFSTradeRequestExpire ) );
        requestSerializerMap.put( RFSTradeHandlers.RequestExpireEventHandler.EVENT,new RequestSerializer(new String[]{"Request", "R"}, EntityHolder.class, new SerializerMap(), RequestSerializer.RequestSerializationType.RFSRequestExpire ) );
        requestSerializerMap.put( RFSTradeHandlers.RequestCancelEventHandler.EVENT,new RequestSerializer(new String[]{"Request", "R"}, EntityHolder.class, new SerializerMap(), RequestSerializer.RequestSerializationType.RFSRequestCancel ) );
        requestSerializerMap.put( RFSTradeHandlers.RequestPublishedEventHandler.EVENT,new RequestSerializer(new String[]{"Request", "R"}, EntityHolder.class, new SerializerMap(), RequestSerializer.RequestSerializationType.RFSRequestPublish ) );

    }

    public static RequestSerializerFactory instance() {
        return instance;
    }

    
    public void addSerializer(String event, RequestSerializer serializer)
    {
    	requestSerializerMap.put(event, serializer);
    }
    
    public RequestSerializer getSerializer(String eventName) {
        RequestSerializer serializer = requestSerializerMap.get(eventName);
        if (serializer == null) {
            serializer = requestSerializerMap.get("DEFAULT");
        }
        return serializer;
    }
}
