package com.integral.dbservice.is;


import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.Order;
import com.integral.finance.dealing.Request;
import com.integral.finance.trade.Trade;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.facade.ISPersistenceFactory;
import com.integral.is.common.facade.OrderFacadeC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.DBQueryUtil;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Namespace;
import com.integral.pipeline.metrics.Metrics;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.query.spaces.fx.esp.query.DealQueryService;
import com.integral.query.spaces.fx.esp.query.OrderQueryService;
import com.integral.util.Tuple;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * This class provides interface to store entities such Request,Trade,Order and Deal
 * to help persistance handlers to store and later have reference to update  entity in
 * subsequent workflow
 * <p/>
 * This also provides methods to look up from database when entity is not found in cache
 */
public class ISPersistenceServiceCacheManager
{

    /*
       multiple caches maintained to split for concurrency. Do remember to call new cache instance in clearAllcache for proper cleanup.
     */
    private final ConcurrentMap<String, Request> requests = new ConcurrentHashMap<String, Request>();

    private final ConcurrentMap<String, Trade> trades = new ConcurrentHashMap<String, Trade>();

    private final ConcurrentMap<String, Deal> deals = new ConcurrentHashMap<String, Deal>();

    //as part of oa-netting
    private final ConcurrentMap<String, List<String>> makerDealsForCancel = new ConcurrentHashMap<String, List<String>>();
    
    private DealExpirableCache spacesDeals = null;

    private final ConcurrentMap<String, UnitOfWork> cachedUnitOfWork = new ConcurrentHashMap<String, UnitOfWork>();

    //One cache per VirtualServer Tenant.
    private static final Map<String, ISPersistenceServiceCacheManager> cacheInstances = new ConcurrentHashMap<String, ISPersistenceServiceCacheManager>( 5 );

    private final CacheMetrics metrics = new CacheMetrics();

    private final String peerVirtualServer;

    private Log log = LogFactory.getLog( this.getClass() );

    protected CacheCleanupTaskC cacheCleanupTask;
    private boolean cleanupThreadInitialized = false;

    private static ISMBean isMBean = ISFactory.getInstance().getISMBean();

    private ISPersistenceServiceCacheManager( String peerVirtualServer )
    {
        this.peerVirtualServer = peerVirtualServer;
        initCacheCleanupTask();
        initSpacesDealCache();
    }

    private void initSpacesDealCache() {
        int numOfContainers = isMBean.getSpacesDealCacheNumContainer();
        int rotationIntervalInMilliSeconds = 5000;
        boolean moveToFirstContainerOnLookup = true;
        spacesDeals = new DealExpirableCache(numOfContainers,rotationIntervalInMilliSeconds,moveToFirstContainerOnLookup);
    }

    public String getPeerVirtualServer()
    {
        return peerVirtualServer;
    }

    //This method should only be called once to initialize this service.
    public static synchronized void __init_at_startup()
    {
        for ( ISPersistenceServiceCacheManager cache : cacheInstances.values() )
        {
            MetricsManager.instance().register( cache.metrics );
        }
    }

    /**
     * return the  ISPersistenceServiceCacheManager singleton
     *
     * @param peerVirtualServer peer vs
     * @return cache manager
     */
    public static ISPersistenceServiceCacheManager instance( String peerVirtualServer )
    {
        ISPersistenceServiceCacheManager instance = cacheInstances.get( peerVirtualServer );
        if ( instance == null )
        {
            synchronized ( ISPersistenceServiceCacheManager.class )
            {
                instance = cacheInstances.get( peerVirtualServer );
                if ( instance == null )
                { //double check to avoid creating duplicate copies of the ISPersistenceServiceCacheManager as the Object is pretty heavy.
                    instance = new ISPersistenceServiceCacheManager( peerVirtualServer );
                    MetricsManager.instance().register( instance.metrics );
                    cacheInstances.put( peerVirtualServer, instance );
                }
            }
        }
        return instance;
    }

    public static Map<String, ISPersistenceServiceCacheManager> getCacheInstances()
    {
        return Collections.unmodifiableMap( cacheInstances );
    }

    public Metrics getCacheMetrics()
    {
        return metrics;
    }

    /**
     * clears All underneath caches.
     */
    public void clearAllCache()
    {
        requests.clear();
        trades.clear();
        deals.clear();
        cachedUnitOfWork.clear();
        makerDealsForCancel.clear();
        spacesDeals.removeAll();
    }

    /**
     * Add Request into  cache against the request key
     *
     * @param request request
     */
    public void addRequest( Request request )
    {
        requests.put( getRequestKey( request ), request );
    }


    /**
     * This method returns request if found in cache
     * does not look into database .Parameter passed is
     * clone of the  request
     *
     * @param cRequest request
     * @return cached request
     */
    public Request getRequestFromCache( Request cRequest )
    {
        String key = getRequestKey( cRequest );
        Request request = requests.get( key );

        return request;

    }


    /**
     * Return cached or queried request for the Request
     *
     * @param cRequest request
     * @param event    event
     * @return request
     */
    public Request getRequest( Request cRequest, String event )
    {
        String key = getRequestKey( cRequest );
        Request request = requests.get( key );

        if ( request == null )
        {
            request = getRequestFromDatabase( cRequest, event );
        }

        return request;
    }

    public Request getRequest( String requestTxId, Namespace ns, String event )
    {
        String key = getRequestKey( requestTxId, ns.getShortName() );
        Request request = requests.get( key );

        if ( request == null )
        {
            request = getRequestFromDatabase( requestTxId, ns, event );
        }

        return request;
    }

    /**
     * Returns true if the cache contains an Request corresponding to the key for
     * the request passed as parameter
     *
     * @param request request
     * @return contains
     */
    public boolean containsRequest( Request request )
    {
        return requests.containsKey( getRequestKey( request ) );
    }


    /**
     * Removes request from cache
     *
     * @param request request
     */
    public void removeRequest( Request request )
    {
        requests.remove( getRequestKey( request ) );
    }

    public void addSpacesDeal( Deal deal )
    {
        String key = getSpacesDealKey(deal.getTransactionId(),deal.getNamespace().getShortName());
        log.info("Adding spaces deal to cache for key=" + key +" , vs=" + peerVirtualServer);
        spacesDeals.add(key, deal);
    }


    public void removeSpacesDeal( String key )
    {
        log.info("Removing spaces deal from cache for key=" + key + " , vs=" + peerVirtualServer);
        spacesDeals.remove(key);
    }

    public void removeSpacesDeal(Trade trade) {
        removeSpacesDeal(getSpacesDealKey(trade.getTransactionID(),trade.getNamespace().getShortName()));
    }

    public void removeSpacesDeal(String transactionID,String nameSpace) {
        removeSpacesDeal(getSpacesDealKey(transactionID,nameSpace));
    }

    private void removeSpacesDeal(Request tradeRequest) {
        removeSpacesDeal(getSpacesDealKey(tradeRequest.getTransactionID(),tradeRequest.getNamespace().getShortName()));
    }


    private String getSpacesDealKey(String tradeTransactionId, String shortName) {
        return new StringBuilder().append( tradeTransactionId ).append("_").append(shortName).toString();
    }

    public void addTradeIdForMakerDealCancellation(String orderId, String tradeId) {
        List<String> nettedTradeIds = makerDealsForCancel.get(orderId);
        if(nettedTradeIds == null) {
            nettedTradeIds = new Vector<String>();
            List<String> prev = makerDealsForCancel.putIfAbsent(orderId, nettedTradeIds);
            if (prev != null) {
                nettedTradeIds = prev;
            }
        }
        nettedTradeIds.add(tradeId);
    }

    public List<String> getTradeIdsForMakerDealCancellation(String orderId) {
        return makerDealsForCancel.remove(orderId);
    }

    /**
     * Add Trade into cache against the trade key
     *
     * @param trade trade
     */
    public void addTrade( Trade trade )
    {

        trades.put( getTradeKey( trade ), trade );
    }


    /**
     * This method returns trade if found in cache
     * does not look into database .Parameter passed is
     * the clone of the trade
     *
     * @param cTrade trade
     * @return trade
     */
    public Trade getTradeFromCache( Trade cTrade )
    {
        String key = getTradeKey( cTrade );
        return trades.get( key );
    }


    /**
     * Return cached or queried request for the Trade
     *
     * @param dTrade trade
     * @param event  event
     * @return trade
     */
    public Trade getTrade( Trade dTrade, String event )
    {
        String key = getTradeKey( dTrade );
        Trade trade = trades.get( key );

        if ( trade == null )
        {
            trade = getTradeFromDatabase( dTrade, event );
        }

        return trade;
    }

    /**
     * Return cached or queried request for the Trade
     *
     * @param tid   transaction id
     * @param ns    namespace
     * @param event event
     * @return trade
     */
    public Trade getTrade( String tid, Namespace ns, String event )
    {
        String key = getTradeKey( tid, ns );
        Trade trade = trades.get( key );

        if ( trade == null )
        {
            trade = DBQueryUtil.getTradeFromDatabase( tid, ns, event );
        }

        return trade;
    }

    /**
     * Returns true if the cache contains a Trade corresponding to the key for
     * the clone of Trade passed as parameter
     *
     * @param cTrade trade
     * @return contains
     */
    public boolean containsTrade( Trade cTrade )
    {
        return trades.containsKey( getTradeKey( cTrade ) );
    }


    /**
     * Removes trade from cache
     *
     * @param cTrade trade
     */
    public void removeTrade( Trade cTrade )
    {
        trades.remove( getTradeKey( cTrade ) );
    }


    /**
     * Add Deal into the cache against key that is based on trade
     *
     * @param trade trade
     * @param deal  deal
     */
    public void addDeal( Trade trade, Deal deal )
    {
        deals.put( getDealKey( trade ), deal );
    }


    /**
     * Returns Deal corresponding to the trade
     *
     * @param trade trade
     * @return deal
     */
    public Deal getDeal( Trade trade, String event )
    {
        Deal deal = deals.get( getDealKey( trade ) );

        if ( deal == null )
        {
            deal = getDealFromDatabase( trade, event );
        }

        return deal;
    }

    public Deal getDeal( String tradeTransactionId, Namespace namespace, Boolean takerFlag,String event )
    {
        Deal deal = deals.get( getDealKey( tradeTransactionId, namespace.getShortName(),takerFlag ) );
        if ( deal == null )
        {
            deal = getDealFromDatabase( tradeTransactionId, namespace, event );
        }
        return deal;
    }


    /**
     * Removed the deal from the cache
     *
     * @param trade trade
     */
    public void removeDeal( Trade trade )
    {
        deals.remove( getDealKey( trade ) );
    }

    public void removeDeal( String tradeTransactionId, Namespace namespace ,boolean  takerFlag)
    {
        deals.remove( getDealKey( tradeTransactionId, namespace.getShortName() ,takerFlag ));
    }


    /**
     * Add Parent Deal into the cache against key that is based
     * on trade request
     *
     * @param request request
     * @param deal    deal
     */
    public void addParentDeal( Request request, Deal deal )
    {
        deals.put( getParentDealKey( request ), deal );
    }


    /**
     * Add Parent Deal into the cache against key that is based on trade
     *
     * @param trade trade
     * @param deal  deal
     */
    public void addParentDeal( Trade trade, Deal deal )
    {
        deals.put( getParentDealKey( trade ), deal );
    }

    /**
     * Returns Parent Deal corresponding to the trade
     *
     * @param trade trade
     * @param event
     * @return parent deal
     */
    public Deal getParentDeal( Trade trade, String event )
    {
        Deal parentDeal = deals.get( getParentDealKey( trade ) );

        if ( parentDeal == null )
        {
            parentDeal = getParentDealFromDatabase( trade.getRequest(), event );
        }

        return parentDeal;
    }


    /**
     * Returns Parent Deal corresponding to the trade
     *
     * @param request request
     * @param event   event
     * @return parent deal
     */
    public Deal getParentDeal( Request request, String event )
    {
        Deal parentDeal = deals.get( getParentDealKey( request ) );
        if ( parentDeal == null )
        {
            parentDeal = getParentDealFromDatabase( request, event );
        }
        return parentDeal;
    }

    public Deal getParentDeal( String requestTransactionId, Namespace namespace, boolean takerFlag , String event)
    {
        Deal parentDeal = deals.get( getParentDealKey( requestTransactionId, namespace ,takerFlag) );
        if ( parentDeal == null )
        {
            parentDeal = getParentDealFromDatabase( requestTransactionId, namespace, event );
        }
        return parentDeal;
    }


    /**
     * Removed the parent deal from the cache
     *
     * @param trade trade
     */
    public void removeParentDeal( Trade trade )
    {
        deals.remove( getParentDealKey( trade ) );
    }


    /**
     * Removed the parent deal from the cache
     *
     * @param request request
     */
    public void removeParentDeal( Request request )
    {
        deals.remove( getParentDealKey( request ) );
    }

    public void removeParentDeal( String requestTransactionId, Namespace namespace , boolean  takerFlag)
    {
        deals.remove( getParentDealKey( requestTransactionId, namespace ,takerFlag) );
    }


    /**
     * Adds Unit of Work for Order request
     * The order persistence handlers could decide to maintain one UnitOfWork
     * across the life of an order and commit the UnitOfWork at the end of
     * the order transaction
     * <p/>
     * Request parameter passed here could be an Order or Trade Request
     * <p/>
     * But typically this unit  of work creation for Order will happen Order
     * persistence handler and by passing an Order Request along with UnitOfWork
     */
    public void setUnitOfWorkForOrderRequest( Request request, UnitOfWork uow )
    {
        cachedUnitOfWork.put( getOrderKey( request ), uow );
    }

    /**
     * Returns the UnitOfWork stored by
     * order persistence handler for an Order Request
     * <p/>
     * Request parameter passed here could be an Order or Trade Request
     *
     * @return UnitOfWork
     */
    public UnitOfWork getUnitOfWorkForOrderRequest( Request request )
    {
        return cachedUnitOfWork.get( getOrderKey( request ) );
    }

    /**
     * Removes  order UnitOfWork for give request
     *
     * @param request request
     */
    public void removeUnitOfWorkForOrderRequest( Request request )
    {
        cachedUnitOfWork.remove( getOrderKey( request ) );
    }


    /**
     * Adds UnitOfWork for Trade request
     * The trade persistence handlers could decide to maintain one UnitOfWork
     * across the life of an order and commit the UnitOfWork at the end of
     * the trade transaction
     */
    public void setUnitOfWorkForTradeRequest( Request tradeRequest, UnitOfWork uow )
    {
        cachedUnitOfWork.put( getRequestKey( tradeRequest ), uow );
    }

    /**
     * Returns UnitOfWork stored by trade persistence handlers
     * for the trade request
     *
     * @return UnitOfWork
     */
    public UnitOfWork getUnitOfWorkForTradeRequest( Request tradeRequest )
    {
        return cachedUnitOfWork.get( getRequestKey( tradeRequest ) );
    }


    /**
     * Removes   UnitOfWork for give trade request
     *
     * @param tradeRequest trade request
     */
    public void removeUnitOfWorkForTradeRequest( Request tradeRequest )
    {
        cachedUnitOfWork.remove( getRequestKey( tradeRequest ) );
    }


    /**
     * Returns UnitOfWork stored by persistence handlers
     * for the  request
     * <p/>
     * First attempt is made to return the Order Request UnitOfWork
     * next attempt is made to check trade Request UnitOfWork
     *
     * @param request request
     * @return UnitOfWork
     */
    public UnitOfWork getUnitOfWorkForRequest( Request request )
    {
        UnitOfWork uow = null;

        uow = getUnitOfWorkForOrderRequest( request );

        if ( uow == null )
        {
            uow = getUnitOfWorkForTradeRequest( request );
        }

        return uow;
    }

    public void clearCachesForTradeRequestWithDelay( Request tradeRequest, String event )
    {
        if ( isMBean.isISPersistenceCacheEntryDelayedRemovalEnabled() )
        {
            cacheCleanupTask.addTradeRequest( tradeRequest );
        }
        else
        {
            clearCachesForTradeRequest( tradeRequest, event );
        }
    }

    /**
     * Clears the cache of the Request,Trades and deals corresponding to
     * a trade request
     *
     * @param tradeRequest trade request
     */
    public void clearCachesForTradeRequest( Request tradeRequest, String event )
    {
        requests.remove( getRequestKey( tradeRequest ) );
        cachedUnitOfWork.remove( getRequestKey( tradeRequest ) );
        deals.remove( getParentDealKey( tradeRequest ) );
        removeSpacesDeal(tradeRequest);
        for ( Trade trade : tradeRequest.getTrades() )
        {
            clearCachesForTrade( trade, event );
        }
        if ( event != null )
        {
            log.info( "PSCM.clearCachesForTradeRequest : removing trade request=" + ISUtilImpl.getInstance().getRequestDescription( tradeRequest ) + ",event=" + event );
        }
    }


    /**
     * Clears the cache of the Trades and deals corresponding to
     * a trade
     *
     * @param trade trade
     */
    public void clearCachesForTrade( Trade trade, String event )
    {
        trades.remove( getTradeKey( trade ) );
        deals.remove( getDealKey( trade ) );
        removeSpacesDeal(trade);
        if ( event != null )
        {
            log.info( "PSCM.clearCachesForTrade : removing trade txId=" + trade.getTransactionID() + ",event=" + event );
        }
    }


    /**
     * Queries Request for the given transaction id from the database
     *
     * @param request request
     * @param event   event
     * @return request
     */
    private Request getRequestFromDatabase( Request request, String event )
    {
        return DBQueryUtil.getRequestFromDatabase( request.getTransactionID(), request.getNamespace(), event );
    }

    /**
     * Queries Request for the given transaction id and namespace from database
     *
     * @param requestTxId request transaction id
     * @param ns          namespace
     * @param event       event
     * @return request
     */
    private Request getRequestFromDatabase( String requestTxId, Namespace ns, String event )
    {
        return DBQueryUtil.getRequestFromDatabase( requestTxId, ns, event );
    }

    /**
     * Queries Trade for the given transaction id from the database
     *
     * @param trade trade
     * @param event event
     * @return trade
     */
    public Trade getTradeFromDatabase( Trade trade, String event )
    {
        return DBQueryUtil.getTradeFromDatabase( trade.getTransactionID(), trade.getNamespace(), event );
    }

    public Map<String, Request> getRequests()
    {
        return Collections.unmodifiableMap(requests);
    }

    public Map<String, Trade> getTrades()
    {
        return Collections.unmodifiableMap( trades );
    }

    public Map<String, Deal> getDeals()
    {
        return Collections.unmodifiableMap( deals );
    }

    public Map<String, UnitOfWork> getUOWs()
    {
        return Collections.unmodifiableMap( cachedUnitOfWork );
    }

    /**
     * Queries parent Deal for the given transaction id from the database
     *
     * @param request request
     * @param event   event
     * @return parent deal
     */
    private Deal getParentDealFromDatabase( Request request, String event )
    {
        return DBQueryUtil.getDealFromDatabase(request.getTransactionID(), request.getNamespace(), event);
    }

    private Deal getParentDealFromDatabase( String requestTransactionId, Namespace namespace, String event )
    {
        return DBQueryUtil.getDealFromDatabase( requestTransactionId, namespace, event );
    }

    /**
     * Queries Deal for the given transaction id from the database
     *
     * @param trade trade
     * @param event event
     * @return deal
     */
    public Deal getDealFromDatabase( Trade trade, String event )
    {
        return getDealFromDatabase(trade.getTransactionID(), trade.getNamespace(), event);
    }

    public Deal getDealFromDatabase( String tradeTransactionId, Namespace namespace, String event )
    {
        String key = getSpacesDealKey(tradeTransactionId, namespace.getShortName());
        Deal spacesDeal = (Deal)spacesDeals.get( key );
        if( spacesDeal != null){
            return spacesDeal;
        }
        metrics.dealSpacesQuery++;
        SpacesQueryService.QueryResult<Deal> result = DealQueryService.queryForDealById(namespace.getShortName(), tradeTransactionId, true);
        if( result.getResult() == null )
            result = DealQueryService.queryForDealById(namespace.getShortName(), tradeTransactionId, false);
        return result.getResult();
    }


    //:todo look at the overall cost of appending namespace to transaction id to generate key

    private String getRequestKey( Request request )
    {
        return new StringBuilder().append( request.getTransactionID() ).append( request.getNamespace().getShortName() ).toString();
    }

    private String getRequestKey( String requestTxId, String namespace )
    {
        return new StringBuilder().append( requestTxId ).append( namespace ).toString();
    }

    private String getTradeKey( Trade trade )
    {
        return new StringBuilder().append( trade.getTransactionID() ).append( trade.getNamespace()!=null?trade.getNamespace().getShortName():"" ).toString();
    }

    private String getTradeKey( String tid, Namespace ns )
    {
        return new StringBuilder().append( tid ).append( ns.getShortName() ).toString();
    }

    private String getOrderKey( Request request )
    {
        return new StringBuilder().append( request.getOrderId() ).append( request.getNamespace().getShortName() ).toString();
    }

    public String getOrderKey( String orderId, String namespace )
    {
        return new StringBuilder().append( orderId ).append( namespace ).toString();
    }


    private String getDealKey( Trade trade )
    {
        StringBuilder sb = new StringBuilder().append( trade.getTransactionID() ).append( trade.getNamespace().getShortName());
        // Append 'M'/'T' respectively for maker/taker flag if trade request is present, 'N' (for None) Otherwise.
        if (trade.getRequest() != null){
            sb.append(trade.getRequest().isPriceTakingIntended()?"T":"M");
        }
        else{
            sb.append("N");
        }
        return  sb.toString();
    }

    private String getDealKey( String tradeTransactionId, String namespaceShortName , Boolean takerFlag)
    {
        StringBuilder sb = new StringBuilder().append( tradeTransactionId ).append( namespaceShortName );
        if(takerFlag != null){
            sb.append(takerFlag?"T":"M");
        }
        else{
            sb.append("N");
        }
        return  sb.toString();
    }

    private String getParentDealKey( Trade trade )
    {
        Request request = trade.getRequest();
        return getParentDealKey(request);
    }

    private String getParentDealKey( Request request )
    {
        return new StringBuilder().append( request.getTransactionID() ).append( request.getNamespace().getShortName() ).append(request.isPriceTakingIntended()?"T":"M").toString();
    }

    private String getParentDealKey( String requestTransactionId, Namespace namespace, Boolean takerFlag)
    {
        StringBuilder sb = new StringBuilder().append( requestTransactionId ).append( namespace.getShortName() );
        if(takerFlag != null){
            sb.append(takerFlag?"T":"M");
        }
        else{
            sb.append("N");
        }
        return  sb.toString();
    }

    private synchronized void initCacheCleanupTask()
    {
        try
        {
            if ( !cleanupThreadInitialized )
            {
                cacheCleanupTask = new CacheCleanupTaskC();
                Thread cacheCleanupThread = new Thread( cacheCleanupTask, peerVirtualServer != null ? "ISPersistCacheClearThread_" + peerVirtualServer : "ISPersistCacheClearThread" );
                cacheCleanupThread.setDaemon( true );
                cacheCleanupThread.start();
                cleanupThreadInitialized = true;
            }
        }
        catch ( Exception e )
        {
            log.error( "PSCM.initCacheCleanupTask : Error initializing the cleanup task", e );
        }
    }

    private class CacheMetrics implements Metrics
    {
        long dealQuery;
        long dealSpacesQuery;
        public StringBuilder report()
        {
            StringBuilder builder = new StringBuilder( 256 );
            builder.append( "t=ispsc, requests=" ).append( requests.size() );
            builder.append( ", trades=" ).append( trades.size() );
            builder.append( ", deals=" ).append( deals.size() );
            builder.append( ", spacesDeals=" ).append( spacesDeals.size() );
            builder.append( ", uows=" ).append( cachedUnitOfWork.size() );
            builder.append(", dealQuery=").append( dealQuery);
            builder.append(", dealSpacesQuery=").append( dealSpacesQuery);

            dealQuery=0;dealSpacesQuery=0;
            return builder;
        }
    }

    private class CacheCleanupTaskC implements Runnable
    {
        private String ORDER_REQUEST = "OR";
        private String TRADE_REQUEST = "TR";
        private final Map<String, Collection<Tuple<Request, Long>>> requestsMap = new HashMap<String, Collection<Tuple<Request, Long>>>();
        private boolean stopped = false;

        public CacheCleanupTaskC()
        {
        }

        public void addOrderRequest( Request request )
        {
            synchronized ( requestsMap )
            {
                if ( request != null )
                {
                    Collection<Tuple<Request, Long>> requests = requestsMap.get( ORDER_REQUEST );
                    if ( requests == null )
                    {
                        requests = new ArrayList<Tuple<Request, Long>>();
                        requestsMap.put( ORDER_REQUEST, requests );
                    }
                    requests.add( new Tuple<Request, Long>( request, System.currentTimeMillis() ) );
                }
            }
        }

        public void addTradeRequest( Request request )
        {
            synchronized ( requestsMap )
            {
                if ( request != null )
                {
                    Collection<Tuple<Request, Long>> requests = requestsMap.get( TRADE_REQUEST );
                    if ( requests == null )
                    {
                        requests = new ArrayList<Tuple<Request, Long>>();
                        requestsMap.put( TRADE_REQUEST, requests );
                    }
                    requests.add( new Tuple<Request, Long>( request, System.currentTimeMillis() ) );
                }
            }
        }

        public Map<String, Collection<Tuple<Request, Long>>> getOrderRequests()
        {
            return requestsMap;
        }

        public void stop()
        {
            synchronized ( requestsMap )
            {
                requestsMap.notify();
            }
            stopped = true;
        }

        public void run()
        {
            while ( !stopped )
            {
                try
                {
                    Collection<Tuple<Request, Long>> tradeRequests;
                    Collection<Request> removeTradeList = new ArrayList<Request>();
                    final long ttl = ISFactory.getInstance().getISMBean().getISPersistenceCacheEntryTimeToLivePeriod();
                    synchronized ( requestsMap )
                    {
                        try
                        {
                            requestsMap.wait( ttl / 10 );
                        }
                        catch ( InterruptedException e )
                        {
                            //ignore
                        }

                        Collection<Tuple<Request, Long>> trs = requestsMap.get( TRADE_REQUEST );
                        boolean trsEmpty = trs == null || trs.isEmpty();
                        if ( trsEmpty )
                        {
                            if ( log.isDebugEnabled() )
                            {
                                log.debug( "CCT.run : order requests and trade requests are empty. ttl=" + ttl + ",metrics="
                                        + metrics.report() + ",vs=" + peerVirtualServer );
                            }
                            continue;
                        }
                        // go through all the entries and determine which needs to be removed.
                        long now = System.currentTimeMillis();
                        tradeRequests = new ArrayList<Tuple<Request, Long>>();
                        if ( !trsEmpty )
                        {
                            tradeRequests.addAll( trs );
                        }

                        if ( log.isDebugEnabled() )
                        {
                            int trsSize = trs != null ? trs.size() : -1;
                            log.debug( "CCT.run : order requests trade requests count=" + trsSize
                                    + ",ttl=" + ttl + ",metrics=" + metrics.report() + ",vs=" + peerVirtualServer );
                        }

                        for ( Tuple<Request, Long> tpl : tradeRequests )
                        {
                            if ( now - tpl.second > ttl )
                            {
                                removeTradeList.add( tpl.first );
                                trs.remove( tpl );
                            }
                        }

                    }

                    removeTradeRequestsFromCache( removeTradeList );
                }
                catch ( Exception e )
                {
                    log.warn( "CCT.run : Exception: " + e.getMessage(), e );
                }
            }
        }

        private void removeTradeRequestsFromCache( Collection<Request> requests )
        {
            try
            {
                for ( Request request : requests )
                {
                    clearCachesForTradeRequest( request, "DelayedClearTradeRequest" );

                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "CCT.removeTradeRequestsFromCache : Removed from caches trade request="
                                + ISUtilImpl.getInstance().getRequestDescription( request ) + ",vs=" + peerVirtualServer );
                    }
                }
            }
            catch ( Exception e )
            {
                log.warn( "CCT.removeOrderRequestsFromCache : Exception while removing requests=" + requests + ",vs=" + peerVirtualServer, e );
            }
        }
    }
}
