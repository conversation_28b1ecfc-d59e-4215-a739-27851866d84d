package com.integral.dbservice.is;

import com.integral.realmoney.persistence.dbservice.PersistenceHandlers;
import com.integral.serialize.EntityHolder;
import com.integral.serialize.NettingPortfolioSerializer;
import com.integral.serialize.RequestSerializer;
import com.integral.serialize.SerializerMap;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class NettingPortfolioSerializerFactory
{

    private static NettingPortfolioSerializerFactory instance = null;
    private final Map<String, NettingPortfolioSerializer> portfolioSerializerMap = new ConcurrentHashMap<String, NettingPortfolioSerializer>();

    static
    {
        instance = new NettingPortfolioSerializerFactory();
    }

    public NettingPortfolioSerializerFactory() {
        portfolioSerializerMap.put( "DEFAULT", new NettingPortfolioSerializer(new String[] {"NettingPortfolio","nP"}, EntityHolder.class, new SerializerMap() , false ) );
        portfolioSerializerMap.put( PersistenceHandlers.PortfolioStateUpdateEventHandler.EVENT, new NettingPortfolioSerializer(new String[] {"NettingPortfolio","nP"}, EntityHolder.class, new SerializerMap() , true ) );
        portfolioSerializerMap.put( PersistenceHandlers.PortfolioUTIUpdateEventHandler.EVENT, new NettingPortfolioSerializer(new String[] {"NettingPortfolio","nP"}, EntityHolder.class, new SerializerMap() , false ) );
    }

    public static NettingPortfolioSerializerFactory instance() {
        return instance;
    }


    public NettingPortfolioSerializer getSerializer(String eventName) {
        NettingPortfolioSerializer serializer = portfolioSerializerMap.get(eventName);
        if (serializer == null) {
            serializer = portfolioSerializerMap.get("DEFAULT");
        }
        return serializer;
    }
}
