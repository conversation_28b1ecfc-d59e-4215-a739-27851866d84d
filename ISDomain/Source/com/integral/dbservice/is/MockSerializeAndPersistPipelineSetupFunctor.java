package com.integral.dbservice.is;

import com.integral.dbservice.*;
import com.integral.pipeline.Pipeline;
import com.integral.pipeline.PipelineSetupFunctor;
import com.integral.pipeline.ProcessorDescriptor;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Nov 18, 2010
 * Time: 11:59:59 AM
 * A Pipeline functor that creates an serialize and persist pipeline with Mock Processor.
 */
public class MockSerializeAndPersistPipelineSetupFunctor implements PipelineSetupFunctor {

    private DBService dbService;
    private ProcessorDescriptor errorHandler;
    private AtomicInteger index = new AtomicInteger(0);

    public MockSerializeAndPersistPipelineSetupFunctor(DBService dbService) {
        this.dbService = dbService;
        errorHandler = new ProcessorDescriptor("DisableTradingErrorHandler", new DisableTradingProcessor());
    }

    public void setErrorHandler(ProcessorDescriptor errorHandler){
        this.errorHandler = errorHandler;
    }

    public Pipeline createPipeline() {

        ByteBufferWriter wri = new ByteBufferWriter(1024*8);

		ProcessorDescriptor serialize2Json = new ProcessorDescriptor(
				"SerializeToJson", new SerializeToJsonProcessor(wri, dbService.getConfig()));
		ProcessorDescriptor w2Log = new ProcessorDescriptor(
				"WriteTransactionLog", new MockEventProcessor());         
		serialize2Json.setNext(w2Log);

		Pipeline p = new Pipeline("SerializeAndEnque-" + index.getAndIncrement(), serialize2Json, null);
        p.setErrorHandler(errorHandler);
        return p;
    }

    public void reclaim(ProcessorDescriptor pipeline) {
        //Do nothing
    }

}