/**
 * <AUTHOR>
 */
package com.integral.dbservice.is;

import com.integral.dbservice.TransactionLogMessage;
import com.integral.exception.IdcDatabaseException;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeLoaderC;
import com.integral.finance.trade.TradeServiceFactory;
import com.integral.finance.trade.functor.TradeServiceTransactionFunctor;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.ISTransactionManager;
import com.integral.mtf.config.ConfigManager;
import com.integral.mtf.config.MTFMessageConfig;
import com.integral.mtf.message.builder.MTFEventMessageBuilder;
import com.integral.mtf.model.EventCode;
import com.integral.persistence.Entity;
import com.integral.persistence.ExternalSystemId;
import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import com.integral.spaces.ApplicationSpaceEvent;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class CreateManualTradeEventHandler extends Processor
{
	public static String EVENT = "createManualTrade";
	public static int INDEX = 10001;
	private final ISPersistenceServiceCacheManager cache;
	private static MTFMessageConfig messageConfig = ConfigManager.getMtfMessageConfig();

	/**
	 * 
	 */
	public CreateManualTradeEventHandler( ISPersistenceServiceCacheManager cache )
	{
		this.cache = cache;
	}

	/* (non-Javadoc)
	 * @see com.integral.pipeline.Processor#process(com.integral.pipeline.PipelineMessage)
	 */
	@Override
	public PipelineMessage process( PipelineMessage msg )
	{
		try
		{
			TransactionLogMessage tlm = (TransactionLogMessage) msg;
			List<Entity> entities = (List<Entity>) tlm.entity;
			Trade trade = (Trade) entities.get(0);
			log.info(new StringBuilder("Tenant:" + cache.getPeerVirtualServer() + ". CreateManualTradeEventHandler.process called for trade=").append(trade.getTransactionID()).append(", msg correlationID = ").append(msg.getCorelationId()).toString());
			ISTransactionManager.setUser(trade.getEntryUser());
			ISTransactionManager.startTransaction(EVENT);

			ExternalSystemId sysId = trade.getExternalSystemId(ISConstantsC.EXT_REQUEST_ID);
			if ( sysId != null && (sysId.getName() == null || sysId.getName().trim().equals("")) )
			{
				trade.removeExternalSystemId(sysId);
				//TODO - answer - is it really required ?
				//            	sysId = trade.newExternalSystemId();
				//            	sysId.setExternalSystem( ISUtilImpl.getInstance().getExtSys(ISConstantsC.EXT_REQUEST_ID) );
				//                sysId.setSystemId( " " );
				//                trade.setExternalSystemId( ISConstantsC.EXT_REQUEST_ID, sysId );
			}

			trade = (Trade) trade.getRegisteredObject();

			TradeLoaderC loader = TradeServiceFactory.getTradeService().getTradeLoader();

			List<ApplicationSpaceEvent> ases = loader.createDeal(trade);
            if( ases != null && ases.size() > 0 )
            {
                tlm.deals.addAll(ases);
            }

            TradeServiceTransactionFunctor.createManualTrade(trade, false);

			if ( log.isDebugEnabled() )
			{
				for ( Entity e : entities )
				{
					log.debug("CreateManualTradeEventHandler Entity : " + e.getClass().getName());
				}
			}
			CreditEventHandler.takeCredit(trade, entities, cache);
			tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(trade,EventCode.TXN_RFS_CREATE_MANUAL_TRADE);
		}
		catch ( Exception e )
		{
			log.error("Tenant:" + cache.getPeerVirtualServer() + ". CreateManualTradeEventHandler.process : Exception occured " + e);
			if ( e instanceof IdcDatabaseException )
			{
				if ( ISTransactionManager.isTransactionOn() )
				{
					try
					{
						UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
						uow.printRegisteredObjects();
					}
					catch ( IdcNoSuchObjectException e1 )
					{
						log.error("Tenant:" + cache.getPeerVirtualServer() + ". CreateManualTradeEventHandler.process : Exception in printing registered objects " + e1);
					}
				}
			}
			throw new PipelineException(e);
		}
		return msg;
	}
}
