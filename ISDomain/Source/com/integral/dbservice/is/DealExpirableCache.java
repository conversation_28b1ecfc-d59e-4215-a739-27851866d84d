package com.integral.dbservice.is;


import com.integral.cache.ExpirableCacheC;
import com.integral.finance.dealing.Deal;


/**
 * User: biswa
 * Date: 8/13/13
 * Time: 3:47 PM
 */



/**
 * This class is used to keep the Deal objects which are currently used. Least used Deal objects
 * are removed from the cache periodically.
 *
 */
public class DealExpirableCache extends ExpirableCacheC {

    public DealExpirableCache(int numOfContainers, int rotationIntervalInMilliSeconds, boolean moveToFirstContainerOnLookup) {
        super(numOfContainers, rotationIntervalInMilliSeconds, moveToFirstContainerOnLookup);
        log.info("configured DealExpirableCache: numOfContainers=" + numOfContainers +
                " rotationIntervalInMilliSeconds=" + rotationIntervalInMilliSeconds +
                " moveToFirstContainerOnLookup=" + moveToFirstContainerOnLookup );
    }

    /**
     * Adds the object to the cache by associating the object in the cache with its derived key.
     * If the key already exists in the cache the new object will replace the old one.
     *
     * @param deal object to be associated in the cache
     */
    public void add(String key,Deal deal) {
        if(deal == null){
            log.info("DealExpirableCache.add: received null Deal");
            return;
        }
        super.add(key, deal);
        log.info("DealExpirableCache.add: Added deal to cache. key=" + key);
    }

    public Deal get(String key) {
        return (Deal) super.get(key);
    }



}
