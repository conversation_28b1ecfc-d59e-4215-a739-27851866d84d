package com.integral.dbservice.is;

import com.integral.cache.distributed.Message;
import com.integral.dbservice.TransactionLogMessage;
import com.integral.exception.IdcDatabaseException;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.*;
import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.FXTrade;
import com.integral.finance.trade.*;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.finance.trade.functor.TradeServiceTransactionFunctor;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.facade.DealFacade;
import com.integral.is.common.facade.ISPersistenceFactory;
import com.integral.is.common.util.ISEntityCloneBuilder;
import com.integral.is.common.util.ISTransactionManager;
import com.integral.is.management.ManagementConstants;
import com.integral.is.management.monitor.WorkflowRuntimeMonitor;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.mtf.message.builder.MTFEventMessageBuilder;
import com.integral.mtf.model.EventCode;
import com.integral.persistence.Entity;
import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import com.integral.session.IdcTransaction;
import com.integral.spaces.ApplicationSpaceEvent;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.util.StringUtilC;
import com.integral.workflow.State;

import org.eclipse.persistence.sessions.UnitOfWork;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * Trade event handlers for RFS trades
 */
public class RFSTradeHandlers
{
    private static ISPersistenceFactory persistenceFactory = ISPersistenceFactory.getInstance();
    private static ISEntityChangeSetHandler entityChangeSetHandler = ISEntityChangeSetHandler.instance();
    public static final AtomicInteger BASE_INDEX = new AtomicInteger(200);
    private static Log log = LogFactory.getLog(RFSTradeHandlers.class);

    public static class TradeAcceptanceEventHandler extends Processor
    {
        public static String EVENT = "rfsTradeAcceptance";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        protected final ISPersistenceServiceCacheManager cache;

        public TradeAcceptanceEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Request tradeRequest = ( Request ) tlm.entity;
                if(tradeRequest.getRequestAttributes() != null && tradeRequest.getRequestAttributes().isTransient())
                {
                	StringBuilder sb = new StringBuilder();
                	sb.append(this).append(" - process : skip persisting event - ")
                	.append(" Tenant=").append(cache.getPeerVirtualServer())
                	.append(", Event=").append(EVENT)
                	.append(", Request=").append(tradeRequest.getTransactionID())
                	.append(", Msg CorrelationID=").append(msg.getCorelationId())
                	.append(", isTransientWF=").append(true);
                	log.info(sb.toString());
                	return msg;
                }
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeAcceptanceEventHandler.process called for request= " ).append( tradeRequest.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );
                Trade trade = cache.getTrade(tradeRequest.getTrade(), EVENT);
                Request request = cache.getRequest(tradeRequest, EVENT);
                if (request == null || !request.isRfq()) {
                    ISTransactionManager.setUser(tradeRequest.getUser());
                    ISTransactionManager.startTransaction(ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT);
                    process(tradeRequest, false, null, tlm);
                } else {
                    log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeAcceptanceEventHandler.process called for RFQ request= " ).append( tradeRequest.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );
                    processRFQ(tradeRequest, false, null, tlm, trade, request);
                }
                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(tradeRequest.getTrade(),EventCode.TXN_RFS_TRADE_ACCEPTANCE);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeAcceptanceEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeAcceptanceEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }

        protected void process( Request tradeRequest, boolean takeCredit, List<Entity> entities, TransactionLogMessage msg )
        {
        	if( tradeRequest.isNetRequest() )
        	{
        		process_NetRequest( tradeRequest, takeCredit, entities );
        		return;
        	}
            IdcTransaction tx = ISTransactionManager.getTransaction();

            setAcceptedDealingPrices( tradeRequest );

            Trade trade = tradeRequest.getTrade();
            trade.setRequest( tradeRequest );
            Quote acceptedQuote = tradeRequest.getAcceptedQuote();
            acceptedQuote.setRequest( tradeRequest );
            Request registeredTradeRequest = ( Request ) tradeRequest.getRegisteredObject();
            for ( CptyTrade cptyTrd : trade.getCptyTrades() )
            {
                cptyTrd = ( CptyTrade ) cptyTrd.getRegisteredObject();
                cptyTrd.setWorkflowStateMap( registeredTradeRequest.getTrade().getWorkflowStateMap() );
                cptyTrd.setTrade( registeredTradeRequest.getTrade() );
            }

            if ( takeCredit )
            {
                if ( log.isDebugEnabled() )
                {
                    for ( Entity e : entities )
                    {
                        log.debug( "RFSTradeHandler Entity : " + e.getClass().getName() );
                    }
                }
                CreditEventHandler.takeCredit( registeredTradeRequest.getTrade(), entities, cache );
            }
            Deal deal = getDealFacade( tradeRequest ).dealCreate( tradeRequest, EVENT );
            ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_TRADE_ACCEPT);
            msg.deals.add(ase);

            cache.addRequest( tradeRequest );
            cache.addTrade( trade );
            cache.addDeal( trade, deal );
            deal.setPricingType(trade.getPricingType());

            if( isSalesDealerWorkflow( tradeRequest ))
            {
                Request sdTradeRequest = ISEntityCloneBuilder.getInstance().cloneRequest( tradeRequest, tradeRequest.getUser().getNamespace() );
                sdTradeRequest.setRequestAttributes( tradeRequest.getRequestAttributes().clone( sdTradeRequest ) );
                Trade sdTrade = ISEntityCloneBuilder.getInstance().cloneTrade(  (FXTrade)trade, tradeRequest.getUser().getNamespace(), false );
                sdTradeRequest.setTrade( sdTrade );
                sdTrade.setRequest( sdTradeRequest );
                Deal sdDeal = getDealFacade( sdTradeRequest,tradeRequest.getUser().getOrganization() ).dealCreate( sdTradeRequest, EVENT );
                ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( sdDeal, ApplicationEventCodes.EVENT_CD_TRADE_ACCEPT);
                sdDeal.setPricingType(trade.getPricingType());
                msg.deals.add(ase);
                cache.addDeal( sdTrade,sdDeal );
            }
        }

        protected void processRFQ(Request dRequest, boolean takeCredit, List<Entity> entities, TransactionLogMessage msg, Trade trade, Request request) {

            ISTransactionManager.setUser(dRequest.getUser());
            IdcTransaction tx = ISTransactionManager.startTransaction(ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT);

            Request registeredTradeRequest = (Request) request.getRegisteredObject();
            Trade registeredTrade = (Trade) trade.getRegisteredObject();

            setAcceptedDealingPricesForRFQ(dRequest, registeredTradeRequest);

            registeredTrade.setRequest(registeredTradeRequest);
            registeredTradeRequest.setTrade(registeredTrade);
            registeredTradeRequest.setAcceptedQuote(dRequest.getAcceptedQuote());

            entityChangeSetHandler.updateRequest(dRequest, registeredTradeRequest);
            entityChangeSetHandler.updateTrade(dRequest.getTrade(), registeredTrade);


            for (CptyTrade cptyTrd : dRequest.getTrade().getCptyTrades()) {
                cptyTrd = (CptyTrade) cptyTrd.getRegisteredObject();
                cptyTrd.setWorkflowStateMap(registeredTradeRequest.getTrade().getWorkflowStateMap());
                cptyTrd.setTrade(registeredTradeRequest.getTrade());
            }

            if (takeCredit) {
                if (log.isDebugEnabled()) {
                    for (Entity e : entities) {
                        log.debug("RFSTradeHandler Entity : " + e.getClass().getName());
                    }
                }
                CreditEventHandler.takeCredit(registeredTradeRequest.getTrade(), entities, cache);
            }
            Deal deal = getDealFacade(dRequest).dealCreate(dRequest, EVENT);
            ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent(deal, ApplicationEventCodes.EVENT_CD_TRADE_ACCEPT);
            msg.deals.add(ase);

            cache.addRequest(registeredTradeRequest);
            cache.addTrade(registeredTrade);
            cache.addDeal(registeredTrade, deal);
            deal.setPricingType(trade.getPricingType());

            if (isSalesDealerWorkflow(dRequest)) {
                Request sdTradeRequest = ISEntityCloneBuilder.getInstance().cloneRequest(dRequest, dRequest.getUser().getNamespace());
                sdTradeRequest.setRequestAttributes(dRequest.getRequestAttributes().clone(sdTradeRequest));
                Trade sdTrade = ISEntityCloneBuilder.getInstance().cloneTrade((FXTrade) trade, dRequest.getUser().getNamespace(), false);
                sdTradeRequest.setTrade(sdTrade);
                sdTrade.setRequest(sdTradeRequest);
                Deal sdDeal = getDealFacade(sdTradeRequest, dRequest.getUser().getOrganization()).dealCreate(sdTradeRequest, EVENT);
                ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent(sdDeal, ApplicationEventCodes.EVENT_CD_TRADE_ACCEPT);
                sdDeal.setPricingType(trade.getPricingType());
                msg.deals.add(ase);
                cache.addDeal(sdTrade, sdDeal);
            }
        }

        protected void process_NetRequest( Request tradeRequest, boolean takeCredit, List<Entity> entities )
        {
            IdcTransaction tx = ISTransactionManager.getTransaction();

            setAcceptedDealingPrices( tradeRequest );

            Trade trade = tradeRequest.getTrade();
            trade.setRequest( tradeRequest );
            Quote acceptedQuote = tradeRequest.getAcceptedQuote();
            acceptedQuote.setRequest( tradeRequest );
            Request registeredTradeRequest = ( Request ) tradeRequest.getRegisteredObject();

            for ( CptyTrade cptyTrd : trade.getCptyTrades() )
            {
                cptyTrd = ( CptyTrade ) cptyTrd.getRegisteredObject();
                cptyTrd.setWorkflowStateMap( registeredTradeRequest.getTrade().getWorkflowStateMap() );
                cptyTrd.setTrade( registeredTradeRequest.getTrade() );
            }


            if ( takeCredit )
            {
                if ( log.isDebugEnabled() )
                {
                    for ( Entity e : entities )
                    {
                        log.debug( "RFSTradeHandler Entity : " + e.getClass().getName() );
                    }
                }
                CreditEventHandler.takeCredit( registeredTradeRequest.getTrade(), entities, cache );
            }
            cache.addRequest( tradeRequest );
            cache.addTrade( trade );

            if( isSalesDealerWorkflow( tradeRequest ))
            {
                Request sdTradeRequest = ISEntityCloneBuilder.getInstance().cloneRequest( tradeRequest, tradeRequest.getUser().getNamespace() );
                sdTradeRequest.setRequestAttributes( tradeRequest.getRequestAttributes().clone( sdTradeRequest ) );
                Trade sdTrade = ISEntityCloneBuilder.getInstance().cloneTrade(  (FXTrade)trade, tradeRequest.getUser().getNamespace(), false );
                sdTradeRequest.setTrade( sdTrade );
                sdTrade.setRequest( sdTradeRequest );
            }
        }

        private void setAcceptedDealingPrices( Request tradeRequest )
        {
            Quote acceptedQuote = tradeRequest.getAcceptedQuote();
            if ( tradeRequest.getRequestAttributes().isSwap() )
            {
                FXLegDealingPrice nearRequestDp = ( FXLegDealingPrice ) tradeRequest.getRequestPrice( ISConstantsC.NEAR_LEG );
                FXLegDealingPrice acceptedQuoteDp = ( FXLegDealingPrice ) acceptedQuote.getQuotePrice( ISConstantsC.NEAR_LEG );
                nearRequestDp.setAcceptedDealingPrice( acceptedQuoteDp );

                FXLegDealingPrice farRequestdp = ( FXLegDealingPrice ) tradeRequest.getRequestPrice( ISConstantsC.FAR_LEG );
                FXLegDealingPrice farAcceptedQuoteDp = ( FXLegDealingPrice ) acceptedQuote.getQuotePrice( ISConstantsC.FAR_LEG );
                farRequestdp.setAcceptedDealingPrice( farAcceptedQuoteDp );
            }
            else if( tradeRequest.getRequestAttributes().isSSP() )
            {
            	for( DealingPrice dealingPrice : tradeRequest.getRequestPrices() )
            	{
            		FXLegDealingPrice acceptedQuoteDp = ( FXLegDealingPrice ) acceptedQuote.getQuotePrice( dealingPrice.getName() );
                    dealingPrice.setAcceptedDealingPrice( acceptedQuoteDp );
            	}
            }
            else
            {
                FXLegDealingPrice requestdp = ( FXLegDealingPrice ) tradeRequest.getRequestPrice( ISConstantsC.SINGLE_LEG );
                FXLegDealingPrice acceptedQuoteDp = ( FXLegDealingPrice ) acceptedQuote.getQuotePrice( "singleLeg" );
                requestdp.setAcceptedDealingPrice( acceptedQuoteDp );
            }
        }

        private void setAcceptedDealingPricesForRFQ( Request tradeRequest, Request registeredTradeRequest  )
        {
            Quote acceptedQuote = tradeRequest.getAcceptedQuote();
            if ( tradeRequest.getRequestAttributes().isSwap() )
            {
                FXLegDealingPrice nearRequestDp = ( FXLegDealingPrice ) registeredTradeRequest.getRequestPrice( ISConstantsC.NEAR_LEG );
                FXLegDealingPrice acceptedQuoteDp = ( FXLegDealingPrice ) acceptedQuote.getQuotePrice( ISConstantsC.NEAR_LEG );
                nearRequestDp.setAcceptedDealingPrice( acceptedQuoteDp );

                FXLegDealingPrice farRequestdp = ( FXLegDealingPrice ) registeredTradeRequest.getRequestPrice( ISConstantsC.FAR_LEG );
                FXLegDealingPrice farAcceptedQuoteDp = ( FXLegDealingPrice ) acceptedQuote.getQuotePrice( ISConstantsC.FAR_LEG );
                farRequestdp.setAcceptedDealingPrice( farAcceptedQuoteDp );
            }
            else if( tradeRequest.getRequestAttributes().isSSP() )
            {
                for( DealingPrice dealingPrice : registeredTradeRequest.getRequestPrices() )
                {
                    FXLegDealingPrice acceptedQuoteDp = ( FXLegDealingPrice ) acceptedQuote.getQuotePrice( dealingPrice.getName() );
                    dealingPrice.setAcceptedDealingPrice( acceptedQuoteDp );
                }
            }
            else
            {
                FXLegDealingPrice requestdp = ( FXLegDealingPrice ) registeredTradeRequest.getRequestPrice( ISConstantsC.SINGLE_LEG );
                FXLegDealingPrice acceptedQuoteDp = ( FXLegDealingPrice ) acceptedQuote.getQuotePrice( "singleLeg" );
                requestdp.setAcceptedDealingPrice( acceptedQuoteDp );
            }
        }
    }



    public static class RequestPublishedEventHandler extends Processor
    {
        public static String EVENT = "rfsRequestPublished";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        protected final ISPersistenceServiceCacheManager cache;

        public RequestPublishedEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Request tradeRequest = ( Request ) tlm.entity;
                if(tradeRequest.getRequestAttributes() != null && tradeRequest.getRequestAttributes().isTransient())
                {
                    StringBuilder sb = new StringBuilder();
                    sb.append(this).append(" - process : skip persisting event - ")
                            .append(" Tenant=").append(cache.getPeerVirtualServer())
                            .append(", Event=").append(EVENT)
                            .append(", Request=").append(tradeRequest.getTransactionID())
                            .append(", Msg CorrelationID=").append(msg.getCorelationId())
                            .append(", isTransientWF=").append(true);
                    log.info(sb.toString());
                    return msg;
                }
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.RequestPublishedEventHandler.process called for request= " ).append( tradeRequest.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );
                ISTransactionManager.setUser( tradeRequest.getUser() );
                ISTransactionManager.startTransaction( ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT );
                process( tradeRequest, false, null,tlm );
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.RequestPublishedEventHandler.process : Exception occured :  " , e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.RequestPublishedEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }

        protected void process( Request tradeRequest, boolean takeCredit, List<Entity> entities, TransactionLogMessage msg )
        {
            IdcTransaction tx = ISTransactionManager.getTransaction();
            try {
                setAcceptedDealingPrices(tradeRequest);
            }catch(Exception e){
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.RequestPublishedEventHandler.process : Exception occured :  " , e );
            }

            Trade trade = tradeRequest.getTrade();
            trade.setRequest( tradeRequest );
            Quote publishedQuote = tradeRequest.getPublishedQuote();
            publishedQuote.setRequest( tradeRequest );
            Request registeredTradeRequest = ( Request ) tradeRequest.getRegisteredObject();
            for ( CptyTrade cptyTrd : trade.getCptyTrades() )
            {
                cptyTrd = ( CptyTrade ) cptyTrd.getRegisteredObject();
                cptyTrd.setWorkflowStateMap( registeredTradeRequest.getTrade().getWorkflowStateMap() );
                cptyTrd.setTrade( registeredTradeRequest.getTrade() );
            }

            cache.addRequest( tradeRequest );
            cache.addTrade( trade );

            if( isSalesDealerWorkflow( tradeRequest ))
            {
                Request sdTradeRequest = ISEntityCloneBuilder.getInstance().cloneRequest( tradeRequest, tradeRequest.getUser().getNamespace() );
                sdTradeRequest.setRequestAttributes( tradeRequest.getRequestAttributes().clone( sdTradeRequest ) );
                Trade sdTrade = ISEntityCloneBuilder.getInstance().cloneTrade(  (FXTrade)trade, tradeRequest.getUser().getNamespace(), false );
                sdTradeRequest.setTrade( sdTrade );
                sdTrade.setRequest( sdTradeRequest );
            }
        }



        private void setAcceptedDealingPrices( Request tradeRequest )
        {
            Quote acceptedQuote = tradeRequest.getPublishedQuote();
            if ( tradeRequest.getRequestAttributes().isSwap() )
            {
                FXLegDealingPrice nearRequestDp = ( FXLegDealingPrice ) tradeRequest.getRequestPrice( ISConstantsC.NEAR_LEG );
                FXLegDealingPrice acceptedQuoteDp = ( FXLegDealingPrice ) acceptedQuote.getQuotePrice( ISConstantsC.NEAR_LEG );
                nearRequestDp.setAcceptedDealingPrice( acceptedQuoteDp );

                FXLegDealingPrice farRequestdp = ( FXLegDealingPrice ) tradeRequest.getRequestPrice( ISConstantsC.FAR_LEG );
                FXLegDealingPrice farAcceptedQuoteDp = ( FXLegDealingPrice ) acceptedQuote.getQuotePrice( ISConstantsC.FAR_LEG );
                farRequestdp.setAcceptedDealingPrice( farAcceptedQuoteDp );
            }
            else if( tradeRequest.getRequestAttributes().isSSP() )
            {
                for( DealingPrice dealingPrice : tradeRequest.getRequestPrices() )
                {
                    FXLegDealingPrice acceptedQuoteDp = ( FXLegDealingPrice ) acceptedQuote.getQuotePrice( dealingPrice.getName() );
                    dealingPrice.setAcceptedDealingPrice( acceptedQuoteDp );
                }
            }
            else
            {
                FXLegDealingPrice requestdp = ( FXLegDealingPrice ) tradeRequest.getRequestPrice( ISConstantsC.SINGLE_LEG );
                FXLegDealingPrice acceptedQuoteDp = ( FXLegDealingPrice ) acceptedQuote.getQuotePrice( "singleLeg" );
                requestdp.setAcceptedDealingPrice( acceptedQuoteDp );
            }
        }
    }

    public static class TradeAcceptanceAndTakeCreditEventHandler extends TradeAcceptanceEventHandler
    {
        public static String EVENT = "rfsTradeAcceptanceAndTakeCredit";
        public static int INDEX = BASE_INDEX.incrementAndGet();

        public TradeAcceptanceAndTakeCreditEventHandler(ISPersistenceServiceCacheManager cache) {
            super(cache);
        }

        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                List<Entity> entities = ( List<Entity> ) tlm.entity;
                // First entity is Request
                Request tradeRequest = ( Request ) entities.get( 0 );
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeAcceptanceAndTakeCreditEventHandler.process called for request= " ).append( tradeRequest.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );

                //Bug#44224 fix. Setting namespace from trade
                //todo check why namespace is not being set in deserialized object
                tradeRequest.setNamespace( tradeRequest.getTrade().getNamespace() );
                Trade trade = cache.getTrade(tradeRequest.getTrade(), EVENT);
                Request request = cache.getRequest(tradeRequest, EVENT);
                if (request == null || !request.isRfq()) {
                    ISTransactionManager.setUser(tradeRequest.getUser());
                    ISTransactionManager.startTransaction(ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT);
                    process( tradeRequest, true, entities ,tlm);
                } else {
                    processRFQ( tradeRequest, true, entities ,tlm, trade, request);
                }
                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(tradeRequest.getTrade(),EventCode.TXN_RFS_TRADE_ACCEPTANCE_TAKE_CREDIT);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeAcceptanceAndTakeCreditEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeAcceptanceAndTakeCreditEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }

    
    public static class ChangeLPLEOnTradeEventHandler extends Processor
    {
    	public static String EVENT = "changeLPLEOnTrade";
    	private final ISPersistenceServiceCacheManager cache;
    	
        public ChangeLPLEOnTradeEventHandler(ISPersistenceServiceCacheManager cache) 
        {
            this.cache = cache;
        }
        
        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Trade dTrade = ( Trade ) tlm.entity;
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.ChangeLPLEOnTradeEventHandler.process called for trade= " ).append( dTrade.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );
                
                Request request = cache.getRequest( dTrade.getRequest(), EVENT );
                Organization lpOrg = request.getAcceptedQuote().getOrganization();
                Organization fiOrg = request.getOrganization();
                LegalEntity fiLe = (LegalEntity)dTrade.getCounterpartyA();
                LegalEntity newMakerLe = (LegalEntity)dTrade.getCounterpartyB();
                
                Trade trade = cache.getTrade( dTrade, EVENT );
                
                Counterparty oldCounterPartyB = trade.getCounterpartyB();

                ISTransactionManager.setUser( dTrade.getRequest().getUser() );
                IdcTransaction tx = ISTransactionManager.startTransaction( ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT );
                
        		TradeConfigurationMBean config = TradeConfigurationFactory.getTradeConfigurationMBean();
        		CreditLimitRuleService creditLimitSvc = CreditLimitRuleServiceFactory.getCreditLimitRuleService();

                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(trade,EventCode.TXN_RFS_TRADE_CHANGE_LP_ON_VERIFICATION);
                
        		//NOTE: When calling undoCredit, make sure that the trade object has the old CPTYB. 
        		//After undoCredit, set the new CPTYB on trade and consume credit.	            		
        		//4. Undo credit consumption with the old CPTYB	            		
        		long t3 = System.currentTimeMillis();
        		if(config.isEnableCredit())
        		{
                	StringBuilder sb2 = new StringBuilder(330);
                	sb2.append("RFSTradeHandlers.ChangeLPLEOnTradeEventHandler: Undoing credit with old CPTYB for trade ");
                	sb2.append(request.getTransactionID());
                	sb2.append(" | LPOrg=").append(lpOrg.getShortName());
                	sb2.append(" | OldMakerLe=").append(oldCounterPartyB.getShortName());
                	sb2.append(" | FIOrg=").append(fiOrg.getShortName());
                	sb2.append(" | FILe=").append(fiLe.getShortName());		                			                	
                    log.info(sb2.toString());
                    
        		   creditLimitSvc.undoBilateralCredit( fiLe, ((TradingParty)oldCounterPartyB).getLegalEntity(), trade, CreditWorkflowRidersDefault.DEFAULT_CREDIT_WORKFLOW_RIDERS );
        		}
        		
        		//5. set the new CPTYB on trade and persist.
        		long t4 = System.currentTimeMillis();
                trade.setCounterpartyB(newMakerLe);
                //TradeCloneServiceC.refreshCptysOnCptyTrades(trade);
                
    		    //6. Do credit consumption with the new CPTYB
                long t5 = System.currentTimeMillis();
        		if(config.isEnableCredit())
        		{
                	StringBuilder sb2 = new StringBuilder(330);
                	sb2.append("RFSTradeHandlers.ChangeLPLEOnTradeEventHandler: taking credit with new CPTYB for trade ");
                	sb2.append(request.getTransactionID());
                	sb2.append(" | LPOrg=").append(lpOrg.getShortName());
                	sb2.append(" | NewMakerLe=").append(newMakerLe.getShortName());
                	sb2.append(" | FIPOrg=").append(fiOrg.getShortName());
                	sb2.append(" | FILe=").append(fiLe.getShortName());		                			                	
                    log.info(sb2.toString());
                    
        			//TODO: This credit consumption with new CPTYB can possibly fail. How to handle it ?
                    CreditWorkflowRiders riders = new CreditWorkflowRiders ();
                    riders.setExcessCreditUtilizationAllowed ( true );
                    riders.setSkipCheckSuspendedStatus ( true );
                    riders.setBypassTenorRestrictions ( true );
        		    CreditWorkflowMessage cwm = creditLimitSvc.takeBilateralCredit(fiLe, newMakerLe, trade, riders );
        		}
        		
        		long t6 = System.currentTimeMillis();
        		
        		StringBuilder timingString = new StringBuilder(50);
        		timingString.append("RFSTradeHandlers.ChangeLPLEOnTradeEventHandler: Time (mills) taken to change CPTYB on Trade: ").append('\n');
        		timingString.append(" | Total Time = ").append(t6-t3).append('\n');
        		timingString.append(" | Time taken to undocredit = ").append(t4-t3).append('\n');
        		timingString.append(" | Time taken to refreshCptyB on trade = ").append(t5-t4).append('\n');
        		timingString.append(" | Time to consume credit against new MakerLE = ").append(t6-t5);
        		log.info(timingString.toString());
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.ChangeLPLEOnTradeEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.ChangeLPLEOnTradeEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }
    
    public static class TradeVerificationEventHandler extends Processor
    {
        public static String EVENT = "rfsTradeVerification";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public TradeVerificationEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Trade dTrade = ( Trade ) tlm.entity;
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeVerificationEventHandler.process called for trade= " ).append( dTrade.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );
                Trade trade = cache.getTrade( dTrade, EVENT );
                Request request = cache.getRequest( dTrade.getRequest(), EVENT );
                Quote acceptedQuote = request.getAcceptedQuote();

                ISTransactionManager.setUser( dTrade.getRequest().getUser() );
                IdcTransaction tx = ISTransactionManager.startTransaction( ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT );

                Request registeredRequest = ( Request ) request.getRegisteredObject();
                Trade registeredTrade = ( Trade ) trade.getRegisteredObject();
                Quote registeredAcceptedQuote = ( Quote ) acceptedQuote.getRegisteredObject();

                registeredRequest.setAcceptedQuote( registeredAcceptedQuote );
                acceptedQuote.setRequest( registeredRequest );
                registeredTrade.setRequest( registeredRequest );
                registeredRequest.setTrade( registeredTrade );

                if(request.isRfq() && dTrade.getEntryUser() != null){
                    registeredTrade.setEntryUser(dTrade.getEntryUser());
                }

                entityChangeSetHandler.updateRequest( dTrade.getRequest(), registeredRequest );
                entityChangeSetHandler.updateTrade( dTrade, registeredTrade );
                entityChangeSetHandler.updateQuote( dTrade.getRequest().getAcceptedQuote(), registeredAcceptedQuote );
                if( !request.isNetRequest() )
                {
                	Deal deal = cache.getDeal( trade, EVENT );
                    Request tradeRequest = trade.getRequest();
                    deal.setAccountId(trade.getAccountShortname());
                	getDealFacade( tradeRequest ).dealVerified( dTrade, deal, null );
                    ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_TRADE_VERIFY);
                    deal.setPricingType(dTrade.getPricingType());
                    if ( dTrade.getPnlCurrency() != null && dTrade.getPnlAmount() != null ) {
                        deal.setPnlCurrency("");
                        deal.setHomeCurrencySpotRate(0.0);
                        deal.setPnlAmount(0.0);
                        deal.setBaseRate(0.0);
                        deal.setBaseSwapPoints(0.0);
                        log.info("TradeVerificationEventHandler.process, removed homeCcy details in CDQ Deal for FI's trade, tradeId: " +trade.getTransactionID() );
                    }
                    tlm.deals.add(ase);
                }

                /*
                if( registeredTrade.getTradeClassification().getShortName().equalsIgnoreCase( ISConstantsC.TRD_CLSF_FXSSP ) )
                {
                	// for SSP trades, persisting the net trade and request as passive.
                	registeredTrade.setStatus( Entity.PASSIVE_STATUS );
                	registeredRequest.setStatus( Entity.PASSIVE_STATUS );
                }
                */
                TradeServiceTransactionFunctor.verifyTrade( registeredTrade );

                if( isSalesDealerWorkflow( request ))
                {
                	if( !request.isNetRequest() )
                    {
                		User sdUser = request.getUser();
                        Boolean takerFlag = (trade.getRequest() == null ) ? null: trade.getRequest().isPriceTakingIntended();
                        Deal sdDeal = cache.getDeal( trade.getTransactionID(),sdUser.getNamespace(),takerFlag, EVENT );
                        sdDeal.setAccountId(trade.getAccountShortname());
                		getDealFacade( trade.getRequest() ,sdUser.getOrganization()).dealVerified(dTrade, sdDeal, null);
                        sdDeal.setPricingType(dTrade.getPricingType());
                        ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( sdDeal, ApplicationEventCodes.EVENT_CD_TRADE_VERIFY);
                        tlm.deals.add(ase);
                    }
                }
                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(trade,EventCode.TXN_RFS_TRADE_VERIFICATION);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeVerificationEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeVerificationEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }


    public static class TradeRejectionEventHandler extends Processor
    {
        public static String EVENT = "rfsTradeRejection";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public TradeRejectionEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Trade dTrade = ( Trade ) tlm.entity;

                if(dTrade.getRequest() != null && dTrade.getRequest().getRequestAttributes() != null && dTrade.getRequest().getRequestAttributes().isTransient())
                {
                	StringBuilder sb = new StringBuilder();
                	sb.append(this).append(" - process : skip persisting event - ")
                	.append(" Tenant=").append(cache.getPeerVirtualServer())
                	.append(", Event=").append(EVENT)
                	.append(", Request=").append(dTrade.getRequest().getTransactionID())
                	.append(", Msg CorrelationID=").append(msg.getCorelationId())
                	.append(", isTransientWF=").append(true);
                	log.info(sb.toString());
                	return msg;
                }
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeRejectionEventHandler.process called for trade= " ).append( dTrade.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );

                ISTransactionManager.setUser( dTrade.getRequest().getUser() );
                IdcTransaction tx = ISTransactionManager.startTransaction( ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT );

                Trade trade = cache.getTrade( dTrade, EVENT );
                Request request = cache.getRequest( dTrade.getRequest(), EVENT );
                if( trade.getRequest() == null )
                {
                    log.warn("Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeRejectionEventHandler.process : No request set on trade from cache ");
                }
                Quote acceptedQuote = request.getAcceptedQuote();

                Request registeredRequest = ( Request ) request.getRegisteredObject();
                Trade registeredTrade = ( Trade ) trade.getRegisteredObject();
                Quote registeredAcceptedQuote = ( Quote ) acceptedQuote.getRegisteredObject();

                registeredRequest.setAcceptedQuote( registeredAcceptedQuote );
                registeredAcceptedQuote.setRequest( registeredRequest );
                registeredTrade.setRequest( registeredRequest );
                registeredRequest.setTrade( registeredTrade );

                entityChangeSetHandler.updateRequest( dTrade.getRequest(), registeredRequest );
                entityChangeSetHandler.updateTrade( dTrade, registeredTrade );
                entityChangeSetHandler.updateQuote( dTrade.getRequest().getAcceptedQuote(), registeredAcceptedQuote );
                //Set only in this handler. Do not use in entityChangeSetHandler
                registeredRequest.setResponseType( dTrade.getRequest().getResponseType() );

                if( !request.isNetRequest() )
                {
                	Deal deal = cache.getDeal( trade, EVENT );
                	deal.setAccountId(trade.getAccountShortname());
                	getDealFacade( trade.getRequest() ).dealRejected( dTrade, deal, null );
                    ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_TRADE_REJECT);
                    deal.setPricingType(dTrade.getPricingType());
                    tlm.deals.add(ase);
                }
                /*
                if( registeredTrade.getTradeClassification().getShortName().equalsIgnoreCase( ISConstantsC.TRD_CLSF_FXSSP ) )
                {
                	// for SSP trades, persisting the net trade and request as passive.
                	registeredTrade.setStatus( Entity.PASSIVE_STATUS );
                	registeredRequest.setStatus( Entity.PASSIVE_STATUS );
                }
                */
                cache.clearCachesForTradeRequestWithDelay( request, EVENT );
                if( isSalesDealerWorkflow( request ))
                {
                	if( !request.isNetRequest() )
                	{
                		Deal sdDeal = cache.getDeal( trade.getTransactionID(),request.getUser().getNamespace(),request.isPriceTakingIntended() , EVENT );
                        sdDeal.setAccountId(trade.getAccountShortname());
                        getDealFacade( trade.getRequest() ).dealRejected( dTrade, sdDeal, null );
                        ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( sdDeal, ApplicationEventCodes.EVENT_CD_TRADE_REJECT);
                        sdDeal.setPricingType(dTrade.getPricingType());
                        tlm.deals.add(ase);
                        cache.removeDeal( trade.getTransactionID(),request.getUser().getNamespace() ,  request.isPriceTakingIntended());
                    }
                }
                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(trade,EventCode.TXN_RFS_TRADE_REJECT);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeRejectionEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeRejectionEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }


    public static class TradeConfirmationEventHandler extends Processor
    {
        public static String EVENT = "rfsTradeConfirmation";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public TradeConfirmationEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Trade dTrade = ( Trade ) tlm.entity;
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeConfirmationEventHandler.process called for trade= " ).append( dTrade.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );

                Trade trade = cache.getTrade( dTrade, EVENT );
                TradeStateFacade tsf = ( TradeStateFacade ) trade.getFacade( TradeStateFacade.TRADE_STATE_FACADE );
                if ( tsf.isRejected() || tsf.isConfirmed() )
                {
                    return msg;
                }
                if ( !tsf.isVerified() )
                {
                    log.warn( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeConfirmationEventHandler.process :: IGNORE  TRADE CONFIRMATION. Trade is not Verified for TXID=" + trade.getTransactionID() );
                    return msg;
                }
                Request request = trade.getRequest();
                ISTransactionManager.setUser( null!=request?request.getUser() : trade.getEntryUser());
                IdcTransaction tx = ISTransactionManager.startTransaction( ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT );

                Trade registeredTrade = ( Trade ) trade.getRegisteredObject();
                if( null!=request) {
                    Request registeredRequest = (Request) request.getRegisteredObject();
                registeredTrade.setRequest( registeredRequest );
                registeredRequest.setTrade( registeredTrade );
                }
                if ( dTrade.getConfirmationDateTime() != null )
                {
                    registeredTrade.setConfirmationDateTime( dTrade.getConfirmationDateTime() );
                }
                if( dTrade.getExecutionDateTime() != null )
                {
                    registeredTrade.setExecutionDateTime( dTrade.getExecutionDateTime() );
                }
                if( dTrade.getCnfrmByUser() != null )
                {
                	registeredTrade.setCnfrmByUser( dTrade.getCnfrmByUser() );
                }
                if(dTrade.getMiFIDTradeParams() != null)
                {
                    registeredTrade.setMiFIDTradeParams(dTrade.getMiFIDTradeParams());
                }
                registeredTrade.setVrfySentByApp( dTrade.getVrfySentByApp() );
                if ( dTrade.getAccountId() != null )
                {
                    registeredTrade.setAccountId ( dTrade.getAccountId () );
                }
                if ( dTrade.getAccountShortname() != null )
                {
                    registeredTrade.setAccountShortname ( dTrade.getAccountShortname () );
                }
                tsf = ( TradeStateFacade ) registeredTrade.getFacade( TradeStateFacade.TRADE_STATE_FACADE );
                State clonedTradeState = dTrade.getWorkflowStateMap() != null ? dTrade.getWorkflowStateMap().getState() : null;
                if ( clonedTradeState != null )
                {
                    tsf.setTradeState( clonedTradeState );
                }
                if(null!=request && !request.isNetRequest() ) {
                	Deal deal = cache.getDeal( trade, EVENT );
                    deal.setAccountId(dTrade.getAccountShortname());
                	getDealFacade( trade.getRequest() ).dealConfirmed( deal );
                    cache.clearCachesForTradeRequestWithDelay( request, EVENT );
                    if( isSalesDealerWorkflow( request ))
                    {
                         if( !request.isNetRequest() )
                         {
                            Deal sdDeal = cache.getDeal( trade.getTransactionID(),request.getUser().getNamespace(),request.isPriceTakingIntended(), EVENT );
                            sdDeal.setAccountId(dTrade.getAccountShortname());
                            getDealFacade( trade.getRequest() ).dealConfirmed( sdDeal );
                            cache.removeDeal( trade.getTransactionID(),request.getUser().getNamespace(),request.isPriceTakingIntended() );
                        }
                    }
                }

                Map<String,String> valueMap = new HashMap<String,String>(1);
                valueMap.put( "txId",trade.getTransactionID() );
                Message message  = new Message(Message.MESSAGE_TYPE_REMOVE_TRADE,valueMap );
                message.setTargetVS( cache.getPeerVirtualServer() );
                tlm.message = message;
                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(trade,EventCode.TXN_RFS_TRADE_CONFIRM);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeConfirmationEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeConfirmationEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }
    
    public static class PostTradeUpdateEventHandler extends Processor
    {
        public static String EVENT = "rfsPostTradeUpdate";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public PostTradeUpdateEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Trade dTrade = ( Trade ) tlm.entity;
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.PostTradeUpdateEventHandler.process called for trade= " ).append( dTrade.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );

                Trade trade = cache.getTrade( dTrade, EVENT );
                TradeStateFacade tsf = ( TradeStateFacade ) trade.getFacade( TradeStateFacade.TRADE_STATE_FACADE );
                Request request = trade.getRequest();
                ISTransactionManager.setUser( null!=request?request.getUser() : trade.getEntryUser());
                IdcTransaction tx = ISTransactionManager.startTransaction( ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT );

                Trade registeredTrade = ( Trade ) trade.getRegisteredObject();
                if( null!=request) {
                    Request registeredRequest = (Request) request.getRegisteredObject();
                    registeredTrade.setRequest( registeredRequest );
                    registeredRequest.setTrade( registeredTrade );
                }
                if ( dTrade.getBackOfficeID() != null ) {
                	registeredTrade.setBackOfficeID( dTrade.getBackOfficeID() );
                }
                if(null != request && !request.isNetRequest() ) {
                	Deal deal = cache.getDeal( trade, EVENT );
                	getDealFacade( trade.getRequest() ).postDealUpdate(deal, dTrade);
                    ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_POST_TRADE_UPDATE);
                    deal.setPricingType(dTrade.getPricingType());
                    tlm.deals.add(ase);
                 // Amend deal in cptyB namespace if exists.
                    Boolean takerFlag = (trade.getRequest() == null ) ? null: !trade.getRequest().isPriceTakingIntended();
                    Deal cptyBDeal = cache.getDeal( trade.getTransactionID(), trade.getCounterpartyB().getNamespace(), takerFlag, EVENT );
                    if ( cptyBDeal != null && trade.getRequest() != null ) {
                    	getDealFacade( trade.getRequest() ).postDealUpdate(cptyBDeal, dTrade);
                    	ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( cptyBDeal, ApplicationEventCodes.EVENT_CD_POST_TRADE_UPDATE);
                        deal.setPricingType(dTrade.getPricingType());
                        tlm.deals.add(ase);
                    }
                    cache.clearCachesForTradeRequestWithDelay( request, EVENT );
                    if( isSalesDealerWorkflow( request ))
                    {
                         if( !request.isNetRequest() )
                         {
                            Deal sdDeal = cache.getDeal( trade.getTransactionID(),request.getUser().getNamespace(),request.isPriceTakingIntended(), EVENT );
                            getDealFacade( trade.getRequest() ).postDealUpdate( sdDeal, dTrade );
                            ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( sdDeal, ApplicationEventCodes.EVENT_CD_POST_TRADE_UPDATE);
                            sdDeal.setPricingType(dTrade.getPricingType());
                            tlm.deals.add(ase);
                            cache.removeDeal( trade.getTransactionID(),request.getUser().getNamespace(),request.isPriceTakingIntended() );
                        }
                    }
                }
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.PostTradeUpdateEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.PostTradeUpdateEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }    


    public static class TradePendingVerificationEventHandler extends Processor
    {
        public static String EVENT = "rfsTradePendingVerification";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public TradePendingVerificationEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Request cRequest = ( Request ) tlm.entity;
                log.warn( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradePendingVerificationEventHandler.process called for trade= " ).append( cRequest.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );
                Request request = cache.getRequest( cRequest, EVENT );
                ISTransactionManager.setUser( cRequest.getUser() );
                IdcTransaction tx = ISTransactionManager.startTransaction( ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT );
                if( !request.isNetRequest() )
                {
	                Deal deal = cache.getDeal( request.getTrade(), EVENT );
	                getDealFacade( request ).dealPendingVerification( deal );
                    ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_TRADE_PENDING_VERIFY);
                    deal.setPricingType(request.getTrade().getPricingType());
                    tlm.deals.add(ase);
                }
                if( isSalesDealerWorkflow( request ))
                {
                	 if( !request.isNetRequest() )
                     {
	                	Deal sdDeal = cache.getDeal( request.getTrade().getTransactionID(),request.getUser().getNamespace(), request.isPriceTakingIntended(),EVENT );
	                    getDealFacade( request ).dealPendingVerification( sdDeal );
                        ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( sdDeal, ApplicationEventCodes.EVENT_CD_TRADE_PENDING_VERIFY);
                        sdDeal.setPricingType(request.getTrade().getPricingType());
                        tlm.deals.add(ase);
                    }
                }
                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(request.getTrade(),EventCode.TXN_RFS_TRADE_PENDING_VERIFICATION);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradePendingVerificationEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradePendingVerificationEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }


    public static class TradeFailedEventHandler extends Processor
    {
        public static String EVENT = "rfsTradeFailed";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public TradeFailedEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Request dTradeRequest = ( Request ) tlm.entity;
                if(dTradeRequest.getRequestAttributes() != null && dTradeRequest.getRequestAttributes().isTransient())
                {
                	StringBuilder sb = new StringBuilder();
                	sb.append(this).append(" - process : skip persisting event - ")
                	.append(" Tenant=").append(cache.getPeerVirtualServer())
                	.append(", Event=").append(EVENT)
                	.append(", Request=").append(dTradeRequest.getTransactionID())
                	.append(", Msg CorrelationID=").append(msg.getCorelationId())
                	.append(", isTransientWF=").append(true);
                	log.info(sb.toString());
                	return msg;
                }
                
                log.warn( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeFailedEventHandler.process called for request= " ).append( dTradeRequest.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );
                Request request = cache.getRequest( dTradeRequest, EVENT );
                Trade trade = cache.getTrade( dTradeRequest.getTrade(), EVENT );

                ISTransactionManager.setUser( dTradeRequest.getUser() );
                IdcTransaction tx = ISTransactionManager.startTransaction( ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT );

                Request registeredRequest = ( Request ) request.getRegisteredObject();
                Trade registeredTrade = ( Trade ) trade.getRegisteredObject();
                registeredRequest.setTrade( registeredTrade );
                registeredTrade.setRequest( registeredRequest );
                entityChangeSetHandler.updateRequest( dTradeRequest, registeredRequest );
                entityChangeSetHandler.updateTrade( dTradeRequest.getTrade(), registeredTrade );
                if( !request.isNetRequest() )
                {
                	Deal deal = cache.getDeal( trade, EVENT );
                	getDealFacade( request ).dealRejected( dTradeRequest.getTrade(), deal, null );
                    ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_TRADE_REJECT);
                    deal.setPricingType(trade.getPricingType());
                    tlm.deals.add(ase);
                }
                cache.clearCachesForTradeRequestWithDelay( request, EVENT );
                if( isSalesDealerWorkflow( request ))
                {
                	 if( !request.isNetRequest() )
                     {
	                	Deal sdDeal = cache.getDeal( trade.getTransactionID(),request.getUser().getNamespace(), request.isPriceTakingIntended(),EVENT );
	                    getDealFacade( request ).dealRejected( dTradeRequest.getTrade(), sdDeal, null );
                        ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( sdDeal, ApplicationEventCodes.EVENT_CD_TRADE_REJECT);
                        sdDeal.setPricingType(trade.getPricingType());
                        tlm.deals.add(ase);
	                    cache.removeDeal( trade.getTransactionID(),request.getUser().getNamespace(),request.isPriceTakingIntended() );
                     }
                }
                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(trade,EventCode.TXN_RFS_TRADE_FAILED);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeFailedEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeFailedEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }


    public static class TradeCancelEventHandler extends Processor
    {
        public static String EVENT = "rfsTradeCancel";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public TradeCancelEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Trade dTrade = ( Trade ) tlm.entity;
                if(dTrade.getRequest() != null && dTrade.getRequest().getRequestAttributes() != null && dTrade.getRequest().getRequestAttributes().isTransient())
                {
                	StringBuilder sb = new StringBuilder();
                	sb.append(this).append(" - process : skip persisting event - ")
                	.append(" Tenant=").append(cache.getPeerVirtualServer())
                	.append(", Event=").append(EVENT)
                	.append(", Request=").append(dTrade.getRequest().getTransactionID())
                	.append(", Msg CorrelationID=").append(msg.getCorelationId())
                	.append(", isTransientWF=").append(true);
                	log.info(sb.toString());
                	return msg;
                }

                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeCancelEventHandler.process called for trade= " ).append( dTrade.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );

                Trade trade = cache.getTrade( dTrade, EVENT );
                User sessionUser = null;
                Map<String, String> propMap = tlm.properties;
                if ( propMap != null )
                {
                    String userFullName = propMap.get ( ISConstantsC.JMS_PROP_USERNAME );
                    if ( !StringUtilC.isNullOrEmpty ( userFullName ) )
                    {
                        sessionUser = UserFactory.getUser ( userFullName );
                    }
                }
                ISTransactionManager.setUser( sessionUser != null ? sessionUser : trade.getEntryUser() );

                IdcTransaction tx = ISTransactionManager.startTransaction( EVENT );
                Trade registeredTrade = ( Trade ) trade.getRegisteredObject();
                entityChangeSetHandler.updateTradeOnCancel( dTrade,registeredTrade );
                registeredTrade.setEtlTimestamp( new Timestamp( System.currentTimeMillis() ) );
                registeredTrade.setNote( dTrade.getNote() );
                registeredTrade.setMT300Field72( dTrade.getMT300Field72() );
                if( trade.getRequest() != null && trade.getRequest().isNetRequest() )
                {
                	// do nothing
                }
                else
                {
                	Deal deal = cache.getDeal( trade, EVENT );
                	persistenceFactory.getDealFacade( ISConstantsC.TRD_CLSF_OR ,trade.getCounterpartyA().getOrganization()).dealCancelled( dTrade, deal );
                    ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_TRADE_CANCEL);
                    deal.setPricingType(trade.getPricingType());
                    tlm.deals.add(ase);

                    // Cancel deal in cptyB namespace if exists.
                    Boolean takerFlag = (trade.getRequest() == null ) ? null: !trade.getRequest().isPriceTakingIntended();
                    Deal cptyBDeal = cache.getDeal( trade.getTransactionID(), trade.getCounterpartyB().getNamespace(), takerFlag, EVENT );
                    if ( cptyBDeal != null )
                    {
                        DealFacade df = null;
                        if ( trade.getRequest() != null ) {
                            df = getDealFacade( trade.getRequest(), trade.getCounterpartyB().getOrganization() );
                        }
                        else {
                            df = getDealFacade( trade, trade.getCounterpartyB().getOrganization() );
                        }

                        if ( df != null )
                        {
                            df.dealCancelled( trade, cptyBDeal );

                            ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( cptyBDeal, ApplicationEventCodes.EVENT_CD_TRADE_CANCEL);
                            cptyBDeal.setPricingType(trade.getPricingType());
                            tlm.deals.add(ase);
                        }
                    }
                }

                TradeServiceTransactionFunctor.cancelTrade( trade );
//                ISTransactionManager.endTransaction( tx, "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeCancelEventHandler.process" );
                cache.clearCachesForTrade( trade, EVENT );
                if( trade.getRequest() != null && isSalesDealerWorkflow( trade.getRequest() )) // For manual trades request can be null.
                {
                	if( !trade.getRequest().isNetRequest() )
                	{
	                    Deal sdDeal = cache.getDeal( trade.getTransactionID(), trade.getRequest().getUser().getNamespace(), trade.getRequest().isPriceTakingIntended(), EVENT );
	                    if( sdDeal != null )
	                    {
	                        persistenceFactory.getDealFacade( ISConstantsC.TRD_CLSF_OR,trade.getRequest().getUser().getOrganization() ).dealCancelledSalesDealer( dTrade,sdDeal );
                            ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( sdDeal, ApplicationEventCodes.EVENT_CD_TRADE_CANCEL);
                            sdDeal.setPricingType(trade.getPricingType());
                            tlm.deals.add(ase);
	                    }
                	}
                }
                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(trade,EventCode.TXN_RFS_TRADE_CANCEL);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeCancelEventHandler.process : Exception occured " , e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeCancelEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }


    public static class TradeMiFiDAmendEventHandler extends Processor
    {
        public static String EVENT = "rfsTradeMiFiDAmend";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public TradeMiFiDAmendEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Trade dTrade = ( Trade ) tlm.entity;
                StringBuilder dealVersions = new StringBuilder();
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeMiFiDAmendEventHandler.process called for trade= " ).append( dTrade.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );

                Trade trade = cache.getTrade( dTrade, EVENT );

                ISTransactionManager.setUser( trade.getEntryUser() );
                IdcTransaction tx = ISTransactionManager.startTransaction( ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT );
                trade = ( Trade ) trade.getRegisteredObject();
                entityChangeSetHandler.updateTradeOnMiFiDAmend( dTrade,trade );

                WorkflowMessage trdWfMsg = MessageFactory.newWorkflowMessage();
                TradeServiceTransactionFunctor.amendMiFiDTrade( trade, trdWfMsg,dealVersions.toString());

                cache.clearCachesForTrade( trade, EVENT );

                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(trade,EventCode.TXN_RFS_TRADE_MIFID_AMEND);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeMiFiDAmendEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeMiFiDAmendEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }


    public static class TradeAmendEventHandler extends Processor
    {
        public static String EVENT = "rfsTradeAmend";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public TradeAmendEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Trade dTrade = ( Trade ) tlm.entity;
                StringBuilder dealVersions = new StringBuilder();
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeAmendEventHandler.process called for trade= " ).append( dTrade.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );

                Trade trade = cache.getTrade( dTrade, EVENT );

                boolean isCptyA;
                if ( !trade.getCounterpartyA().getShortName().equals( dTrade.getCounterpartyA().getShortName() ) )
                {
                    isCptyA = true;
                }
                else if ( !trade.getCounterpartyB().getShortName().equals( dTrade.getCounterpartyB().getShortName() ) )
                {
                    isCptyA = false;
                }
                else
                {
                    return msg;
                }
                Counterparty oldCounterparty = isCptyA ? trade.getCounterpartyA() : trade.getCounterpartyB();

                ISTransactionManager.setUser( trade.getEntryUser() );
                IdcTransaction tx = ISTransactionManager.startTransaction( EVENT );
                trade = ( Trade ) trade.getRegisteredObject();
                entityChangeSetHandler.updateTradeOnAmend( dTrade,trade );
                trade.setEtlTimestamp( new Timestamp( System.currentTimeMillis() ) );
                final boolean isNetRequest = trade.getRequest() != null && trade.getRequest().isNetRequest();
                if( !isNetRequest )
                {
                    Deal deal= cache.getDeal( trade, EVENT );
                    log.info("Deal object: "+ deal+" "+ trade.getTransactionID());
                    final Organization takerOrg = trade.getRequest() != null ? trade.getRequest().getOrganization() : trade.getCounterpartyA().getOrganization();
                    persistenceFactory.getDealFacade( ISConstantsC.TRD_CLSF_OR, takerOrg ).dealAmend( trade, deal );
                    ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_TRADE_AMEND );
                    deal.setPricingType(trade.getPricingType());
                    tlm.deals.add( ase );
                    dealVersions.append( deal.getVersionId() );

                    // Amend deal in cptyB namespace if exists.
                    Boolean takerFlag = (trade.getRequest() == null ) ? null: !trade.getRequest().isPriceTakingIntended();
                    Deal cptyBDeal = cache.getDeal( trade.getTransactionID(), trade.getCounterpartyB().getNamespace(),takerFlag, EVENT );
                    if ( cptyBDeal != null && trade.getRequest() != null )
                    {
                        DealFacade df = getDealFacade( trade.getRequest(), trade.getCounterpartyB().getOrganization() );
                        if ( df != null )
                        {
                            df.dealAmend( trade, cptyBDeal );

                            // for maker deal, the cptyA and cptyB has to be reversed.
                            cptyBDeal.setCounterpartyA( CounterpartyUtilC.getLegalEntity( trade.getCounterpartyB() ) );
                            cptyBDeal.setCounterpartyB( CounterpartyUtilC.getLegalEntity( trade.getCounterpartyA() ) );

                            ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( cptyBDeal, ApplicationEventCodes.EVENT_CD_TRADE_AMEND);
                            cptyBDeal.setPricingType(trade.getPricingType());
                            tlm.deals.add(ase);
                            dealVersions.append(",").append(cptyBDeal.getVersionId());
                        }
                    }

                }
                WorkflowMessage trdWfMsg = MessageFactory.newWorkflowMessage();
                trdWfMsg.setParameterValue( "OriginalCounterparty", oldCounterparty );
                trdWfMsg.setParameterValue( "isCounterpartyA", isCptyA );
                TradeServiceTransactionFunctor.amendTrade( trade, trdWfMsg,dealVersions.toString());

                cache.clearCachesForTrade( trade, EVENT );
                if( isSalesDealerWorkflow( trade.getRequest() ))
                {
                	 if( !trade.getRequest().isNetRequest() )
                     {
	                    Deal sdDeal = cache.getDeal( trade.getTransactionID(), trade.getRequest().getUser().getNamespace(),trade.getRequest().isPriceTakingIntended(), EVENT );
	                    if( sdDeal != null )
	                    {
	                        persistenceFactory.getDealFacade( ISConstantsC.TRD_CLSF_OR,trade.getRequest().getUser().getOrganization() ).dealAmendedSalesDealer( dTrade,sdDeal );
                            ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( sdDeal, ApplicationEventCodes.EVENT_CD_TRADE_AMEND);
                            sdDeal.setPricingType(trade.getPricingType());
                            tlm.deals.add(ase);
	                    }
                     }
                }

                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(trade,EventCode.TXN_RFS_TRADE_AMEND);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeAmendEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeAmendEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }


    public static class RequestExpireEventHandler extends Processor
    {
        public static String EVENT = "rfsRequestExpire";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public RequestExpireEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Request dRequest = ( Request ) tlm.entity;
                if(dRequest.getRequestAttributes() != null && dRequest.getRequestAttributes().isTransient())
                {
                	StringBuilder sb = new StringBuilder();
                	sb.append(this).append(" - process : skip persisting event - ")
                	.append(" Tenant=").append(cache.getPeerVirtualServer())
                	.append(", Event=").append(EVENT)
                	.append(", Request=").append(dRequest.getTransactionID())
                	.append(", Msg CorrelationID=").append(msg.getCorelationId())
                	.append(", isTransientWF=").append(true);
                	log.info(sb.toString());
                	return msg;
                }
                log.warn( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.RequestExpireEventHandler.process called for request= " ).append( dRequest.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );

                Request request = cache.getRequest( dRequest, EVENT );
                if ( request == null )
                {
                    request = dRequest;
                }
                Trade trade = cache.getTrade(dRequest.getTrade(), EVENT);
                Request tradeRequest = cache.getRequest(dRequest, EVENT);
                if (tradeRequest == null || !tradeRequest.isRfq()) {
                    ISTransactionManager.setUser(request.getUser());
                    IdcTransaction tx = ISTransactionManager.startTransaction(ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT);
                    tx.getUOW().addReadOnlyClass(TradeC.class);
                    tx.getUOW().addReadOnlyClass(TradeLegC.class);
                    tx.getUOW().addReadOnlyClass(CptyTradeC.class);
                    tx.getUOW().addReadOnlyClass(TradeExternalSystemIdC.class);

                    Request registeredRequest = (Request) request.getRegisteredObject();
                    entityChangeSetHandler.updateRequest(dRequest, registeredRequest);
                /*
                for ( Quote quote : dRequest.getQuotes() )
                {
                    Quote registeredQuote = ( Quote ) quote.getRegisteredObject();
                    registeredQuote.setRequest( registeredRequest );
                    registeredRequest.setQuote( quote.getOrganization(),registeredQuote );
                }
                */
//                ISTransactionManager.endTransaction( tx, "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.RequestExpireEventHandler.process" );
                    cache.clearCachesForTradeRequestWithDelay(request, EVENT);
                    tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(request, EventCode.TXN_RFS_REQUEST_EXPIRE);
                }else{
                    log.warn( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.RequestExpireEventHandler.process called for RFQ request= " ).append( dRequest.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );
                    ISTransactionManager.setUser( dRequest.getUser() );
                    IdcTransaction tx = ISTransactionManager.startTransaction( ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT );
                    Request registeredTradeRequest = (Request) tradeRequest.getRegisteredObject();
                    Trade registeredTrade = (Trade) trade.getRegisteredObject();
                    entityChangeSetHandler.updateRequest(dRequest, registeredTradeRequest);
                    entityChangeSetHandler.updateTrade(dRequest.getTrade(), registeredTrade);
                    cache.clearCachesForTradeRequestWithDelay(tradeRequest, EVENT);
                    tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(tradeRequest,EventCode.TXN_RFS_REQUEST_CANCEL);
                }
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.RequestExpireEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.RequestExpireEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }


    public static class RequestCancelEventHandler extends Processor
    {
        public static String EVENT = "rfsRequestCancel";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public RequestCancelEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Request dRequest = ( Request ) tlm.entity;

                if(dRequest.getRequestAttributes() != null && dRequest.getRequestAttributes().isTransient())
                {
                	StringBuilder sb = new StringBuilder();
                	sb.append(this).append(" - process : skip persisting event - ")
                	.append(" Tenant=").append(cache.getPeerVirtualServer())
                	.append(", Event=").append(EVENT)
                	.append(", Request=").append(dRequest.getTransactionID())
                	.append(", Msg CorrelationID=").append(msg.getCorelationId())
                	.append(", isTransientWF=").append(true);
                	log.info(sb.toString());
                	return msg;
                }
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.RequestCancelEventHandler.process called for request= " ).append( dRequest.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );

               Trade trade = cache.getTrade(dRequest.getTrade(), EVENT);
               Request tradeRequest = cache.getRequest(dRequest, EVENT);
               if (tradeRequest == null || !tradeRequest.isRfq()) {
                Request request = dRequest;
                ISTransactionManager.setUser( request.getUser() );
                IdcTransaction tx = ISTransactionManager.startTransaction( ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT );
                tx.getUOW().addReadOnlyClass( TradeC.class );
                tx.getUOW().addReadOnlyClass( TradeLegC.class );
                tx.getUOW().addReadOnlyClass( CptyTradeC.class );
                tx.getUOW().addReadOnlyClass( TradeExternalSystemIdC.class );

                Request registeredRequest = ( Request ) request.getRegisteredObject();
                entityChangeSetHandler.updateRequest( dRequest, registeredRequest );
                /*
                for ( Quote quote : dRequest.getQuotes() )
                {
                    Quote registeredQuote = ( Quote ) quote.getRegisteredObject();
                    registeredQuote.setRequest( registeredRequest );
                    registeredRequest.setQuote( quote.getOrganization(),registeredQuote );
                }
                */
//                ISTransactionManager.endTransaction( tx, "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.RequestCancelEventHandler.process" );
                    cache.clearCachesForTradeRequestWithDelay(request, EVENT);
                    tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(request,EventCode.TXN_RFS_REQUEST_CANCEL);
               }else{
                   log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.RequestCancelEventHandler.process called for RFQ request= " ).append( dRequest.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );
                   ISTransactionManager.setUser( dRequest.getUser() );
                    IdcTransaction tx = ISTransactionManager.startTransaction( ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT );
                    Request registeredTradeRequest = (Request) tradeRequest.getRegisteredObject();
                    Trade registeredTrade = (Trade) trade.getRegisteredObject();
                    entityChangeSetHandler.updateRequest(dRequest, registeredTradeRequest);
                    entityChangeSetHandler.updateTrade(dRequest.getTrade(), registeredTrade);
                    cache.clearCachesForTradeRequestWithDelay(tradeRequest, EVENT);
                    tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(tradeRequest,EventCode.TXN_RFS_REQUEST_CANCEL);
               }

            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.RequestCancelEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.RequestCancelEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }


    public static class TradeRequestExpireHandler extends Processor
    {
        public static String EVENT = "rfsTradeRequestExpire";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public TradeRequestExpireHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Request dTradeRequest = ( Request ) tlm.entity;
                if(dTradeRequest.getRequestAttributes() != null && dTradeRequest.getRequestAttributes().isTransient())
                {
                	StringBuilder sb = new StringBuilder();
                	sb.append(this).append(" - process : skip persisting event - ")
                	.append(" Tenant=").append(cache.getPeerVirtualServer())
                	.append(", Event=").append(EVENT)
                	.append(", Request=").append(dTradeRequest.getTransactionID())
                	.append(", Msg CorrelationID=").append(msg.getCorelationId())
                	.append(", isTransientWF=").append(true);
                	log.info(sb.toString());
                	return msg;
                }
                log.info( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeRequestExpireHandler.process called for request= " + dTradeRequest.getTransactionID() + ", msg correlationID = " + msg.getCorelationId() );

                Request tradeRequest = cache.getRequest( dTradeRequest, EVENT );
                ISTransactionManager.setUser( dTradeRequest.getUser() );
                IdcTransaction tx = ISTransactionManager.startTransaction( EVENT );
                Request registeredTradeRequest = ( Request ) tradeRequest.getRegisteredObject();
                entityChangeSetHandler.updateRequest( dTradeRequest, registeredTradeRequest );
//                ISTransactionManager.endTransaction( tx, "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeRequestExpireHandler.process" );
                cache.clearCachesForTradeRequestWithDelay( tradeRequest, EVENT );

                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(tradeRequest,EventCode.TXN_RFS_TRADE_REQUEST_EXPIRE);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeRequestExpireHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.TradeRequestExpireHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }

    
    public static class STPTradeStatusUpdateToGMEventHandler extends Processor
    {
        public static String EVENT = "stpTradeStatusUpdateSentToGM";        
        private final ISPersistenceServiceCacheManager cache;

        public STPTradeStatusUpdateToGMEventHandler(ISPersistenceServiceCacheManager cache) 
        {
            this.cache = cache;
        }
        
		@Override
		public PipelineMessage process(PipelineMessage msg) 
		{
            TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
            Trade dTrade = ( Trade ) tlm.entity;            
            String errorReason = tlm.properties!=null?tlm.properties.get(ManagementConstants.CH_SDR_ERRORREASON_KEY):null;            
            log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.STPTradeStatusUpdateToGMEventHandler.process called for trade= " )
            .append( dTrade.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );
            Trade trade = cache.getTrade( dTrade, EVENT );            
            reallySendClearingHouseUpdateToGM(errorReason, trade);
            tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(trade,EventCode.TXN_RFS_STP_TRADE_STATUS_UPDATE_TO_GM);
            return msg;
		}		

		public void reallySendClearingHouseUpdateToGM(String errorReason, Trade tradeObject)
		{
			try
			{
				Map<String, Object> additionalProps = null;
				if(errorReason != null && !"".equals(errorReason))
				{
					additionalProps = new HashMap<String, Object>();
					additionalProps.put(ManagementConstants.CH_SDR_ERRORREASON_KEY, errorReason);
				}
				
				if(log.isDebugEnabled())
				{
					StringBuilder sb = new StringBuilder(100);
					sb.append("Sending CH_SDR_UPDATE_EVENT notification to GM for tradeID=").append(tradeObject.getTransactionID());
					sb.append(" with ").append(" SDRAckId=").append(tradeObject.getSDRAckId());
					sb.append(" | SDRStatus=").append(tradeObject.getSDRTradeStatus());
					sb.append(" | CHAckId=").append(tradeObject.getClearingHouseAckId());
					sb.append(" | CHStatus=").append(tradeObject.getClearingHouseTradeStatus());
					sb.append(" | B_ClearedUSI=").append(tradeObject.getBuySideClearedUSI());
					sb.append(" | S_ClearedUSI=").append(tradeObject.getSellSideClearedUSI());
					log.debug(sb.toString());
				}
				WorkflowRuntimeMonitor.getInstance().notifyWorkflowEvent( tradeObject.getRequest(), tradeObject, ManagementConstants.CH_SDR_UPDATE_EVENT, additionalProps );
			}
			catch ( Exception e )
			{
				log.error("TradeController.sendClearingHouseErrorToGridMonitor: Exception while sending ClearingHouse error to grid monitor ", e);
			}
		}
    	
    }
    
    public static class STPTradeStatusUpdateEventHandler extends Processor
    {
        public static String EVENT = "stpTradeStatusUpdated";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;
        private STPTradeStatusUpdateToGMEventHandler gmUpdater;

        public STPTradeStatusUpdateEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
            gmUpdater = new STPTradeStatusUpdateToGMEventHandler(cache);
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Trade dTrade = ( Trade ) tlm.entity;
                String errorReason = tlm.properties!=null?tlm.properties.get(ManagementConstants.CH_SDR_ERRORREASON_KEY):null;
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.STPTradeStatusUpdateEventHandler.process called for trade= " ).append( dTrade.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );

                Trade trade = cache.getTrade( dTrade, EVENT );
                ISTransactionManager.setUser( trade.getEntryUser() );
                IdcTransaction tx = ISTransactionManager.startTransaction( ISTransactionManager.getRFSWorkflowReadOnlyClasses(), EVENT );

                Trade registeredTrade = ( Trade ) trade.getRegisteredObject();
                
                Request request = trade.getRequest();
                if(request != null)
                {
	                Request registeredRequest = ( Request ) request.getRegisteredObject();                
	                registeredTrade.setRequest( registeredRequest );
	                registeredRequest.setTrade( registeredTrade );
                }

                tx.getUOW().refreshObject(registeredTrade);

                if ( dTrade.getSDRTradeStatus() != null )
                {
                    registeredTrade.setSDRTradeStatus( dTrade.getSDRTradeStatus() );
                    trade.setSDRTradeStatus( dTrade.getSDRTradeStatus() );
                }
                if ( dTrade.getClearingHouseTradeStatus() != null )
                {
                    registeredTrade.setClearingHouseTradeStatus( dTrade.getClearingHouseTradeStatus() );
                    trade.setClearingHouseTradeStatus( dTrade.getClearingHouseTradeStatus() );
                }
                if ( dTrade.getSDRAckId() != null )
                {
                    registeredTrade.setSDRAckId( dTrade.getSDRAckId() );
                    trade.setSDRAckId( dTrade.getSDRAckId() );
                }
                if ( dTrade.getClearingHouseAckId() != null )
                {
                	registeredTrade.setClearingHouseAckId( dTrade.getClearingHouseAckId() );
                	trade.setClearingHouseAckId( dTrade.getClearingHouseAckId() );
                }
                String buySideClearedUSI = dTrade.getBuySideClearedUSI();
                String sellSideClearedUSI = dTrade.getSellSideClearedUSI();
                		
                if (  buySideClearedUSI != null )
                {
                	registeredTrade.setBuySideClearedUSI( buySideClearedUSI );
                	trade.setBuySideClearedUSI( buySideClearedUSI );
                }

                if (  sellSideClearedUSI != null )
                {
                	registeredTrade.setSellSideClearedUSI( sellSideClearedUSI );
                	trade.setSellSideClearedUSI( sellSideClearedUSI );
                }
                
                if(request != null)
                {
                	cache.clearCachesForTradeRequestWithDelay( request, EVENT );
                }
                else
                {
                	cache.clearCachesForTrade(trade, EVENT);
                }
				
				// Clearinghouse and SDR details are available on <registeredTrade>. So we could have sent that object
				// in the following method call. But doing so was resulting in ConcurrencyException intermittently. So instead
				// update these values on <trade> and using it as the DTO.
                gmUpdater.reallySendClearingHouseUpdateToGM(errorReason, trade);
                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(trade,EventCode.TXN_RFS_STP_TRADE_STATUS_UPDATE);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.STPTradeStatusUpdateEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSTradeHandlers.STPTradeStatusUpdateEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }

    public static DealFacade getDealFacade( Request tradeRequest )
    {
        return   getDealFacade(tradeRequest, tradeRequest.getOrganization());
    }

    public static DealFacade getDealFacade( Trade trade, Organization org )
    {
        if ( ISConstantsC.TRD_CLSF_SWAP.equals( trade.getTradeClassification().getShortName() ) ) {
            return persistenceFactory.getDealFacade( ISConstantsC.TRD_CLSF_SWAP , org);
        }
        else {
            return persistenceFactory.getDealFacade( ISConstantsC.TRD_CLSF_OR , org);
        }
    }

    public static DealFacade getDealFacade( Request tradeRequest ,Organization org)
    {
        if ( tradeRequest.getRequestAttributes().isSwap() )
        {
            return persistenceFactory.getDealFacade( ISConstantsC.TRD_CLSF_SWAP , org);
        }
        else
        {
            return persistenceFactory.getDealFacade( ISConstantsC.TRD_CLSF_OR , org);
        }
    }

    private static boolean isSalesDealerWorkflow( Request origOrderRequest )
    {
        return origOrderRequest != null && !origOrderRequest.getUser().getOrganization().isSameAs( origOrderRequest.getOrganization() );
    }
}
