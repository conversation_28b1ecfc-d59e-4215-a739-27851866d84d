package com.integral.dbservice.is;

import com.integral.dbservice.*;
import com.integral.dbservice.spaces.SpacesEndTransactionProcessor;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.pipeline.*;
import com.integral.pipeline.metrics.MetricsManager;

public class EventProcessingPipelineSetupFunctor implements EventProcessingPipelineSetupFunctorInf
{

    DBService dbService;
    private static final Log log = LogFactory.getLog(EventProcessingPipelineSetupFunctor.class);
    private EventBasedProcessorDescriptor eventHandler = new EventBasedProcessorDescriptor( "EventHandler" );

    public EventProcessingPipelineSetupFunctor( DBService dbService )
    {
        this.dbService = dbService;
        initRequestSerializers();
        setPipelineEventHandlers( eventHandler );
    }

    public Pipeline createPipeline()
    {

        //Create all processors that will be part of this pipeline.
        ProcessorDescriptor deSerializeFromText = new ProcessorDescriptor( "DeserializeFromText", new DeSerializeFromJsonProcessor(dbService.getConfig()) );
        ProcessorDescriptor startTransaction = new ProcessorDescriptor( "StartTransaction", new StartTransactionProcessor() );

        ProcessorDescriptor endTransaction = new ProcessorDescriptor( "EndTransaction", new EndTransactionProcessor( dbService.getTransactionLog() ) );

        ProcessorDescriptor spacesEndTransaction = new ProcessorDescriptor( "SpacesEndTransaction", new SpacesEndTransactionProcessor( dbService.getTransactionLog()));

        ProcessorDescriptor errorHandler = new ProcessorDescriptor("PersistToDBPipelineErrorHandler", new DBPersistPipelineErrorProcessor(dbService));

        //Setup the chain of processors in a pipeline.
        // start transaction -> handle event -> end transaction
        deSerializeFromText.setNext( startTransaction ).setNext( eventHandler ).setNext( endTransaction ).setNext( spacesEndTransaction );

        ProcessorDescriptor startDesc;
        if(dbService.getConfig().isReplayModeEnabled()){
            ProcessorDescriptor replayProcessor = new ProcessorDescriptor("IntroduceDelay", new DBTxnLogReplayProcessor(dbService));
            replayProcessor.setNext(deSerializeFromText);
            log.warn("########## Setting up Transaction server in replay mode.");
            startDesc = replayProcessor;
        }else{
            startDesc = deSerializeFromText;
        }
        //Set the errorHandler - to ensure that we close the transaction even in case of errors.
        Pipeline p = new Pipeline( "PersistToDBPipeline", startDesc, errorHandler, dbService.getConfig().getPipelineBlockedAlertDuration() );
        MetricsManager.instance().register( p );
        return p;
    }


    /**
     * Sets up IS transaction specific  pipelines
     */
    public void setPipelineEventHandlers( EventBasedProcessorDescriptor eventHandler )
    {
        ISPersistenceServiceCacheManager cache = ISPersistenceServiceCacheManager.instance(dbService.getPeerVirtualServer());

        eventHandler.setEventHandler( CreditEventHandler.UndoCreditEventHandler.EVENT, new CreditEventHandler.UndoCreditEventHandler(cache) );
        eventHandler.setEventHandler( CreditEventHandler.UpdateMultiFillCreditEventHandler.EVENT, new CreditEventHandler.UpdateMultiFillCreditEventHandler(cache) );
        eventHandler.setEventHandler( CreditEventHandler.AmendEventHandler.EVENT, new CreditEventHandler.AmendEventHandler(cache) );

        eventHandler.setEventHandler( RFSTradeHandlers.TradeAcceptanceEventHandler.EVENT, new RFSTradeHandlers.TradeAcceptanceEventHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.TradeAcceptanceAndTakeCreditEventHandler.EVENT, new RFSTradeHandlers.TradeAcceptanceAndTakeCreditEventHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.TradeVerificationEventHandler.EVENT, new RFSTradeHandlers.TradeVerificationEventHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.ChangeLPLEOnTradeEventHandler.EVENT, new RFSTradeHandlers.ChangeLPLEOnTradeEventHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.TradeRejectionEventHandler.EVENT, new RFSTradeHandlers.TradeRejectionEventHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.TradeConfirmationEventHandler.EVENT, new RFSTradeHandlers.TradeConfirmationEventHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.TradePendingVerificationEventHandler.EVENT, new RFSTradeHandlers.TradePendingVerificationEventHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.TradeFailedEventHandler.EVENT, new RFSTradeHandlers.TradeFailedEventHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.TradeCancelEventHandler.EVENT, new RFSTradeHandlers.TradeCancelEventHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.TradeMiFiDAmendEventHandler.EVENT, new RFSTradeHandlers.TradeMiFiDAmendEventHandler(cache ) );
        eventHandler.setEventHandler( RFSTradeHandlers.TradeAmendEventHandler.EVENT, new RFSTradeHandlers.TradeAmendEventHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.TradeRequestExpireHandler.EVENT, new RFSTradeHandlers.TradeRequestExpireHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.STPTradeStatusUpdateEventHandler.EVENT, new RFSTradeHandlers.STPTradeStatusUpdateEventHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.STPTradeStatusUpdateToGMEventHandler.EVENT, new RFSTradeHandlers.STPTradeStatusUpdateToGMEventHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.RequestExpireEventHandler.EVENT, new RFSTradeHandlers.RequestExpireEventHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.RequestPublishedEventHandler.EVENT, new RFSTradeHandlers.RequestPublishedEventHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.RequestCancelEventHandler.EVENT, new RFSTradeHandlers.RequestCancelEventHandler(cache) );

        eventHandler.setEventHandler( RFSPrimeBrokerHandlers.RFSPrimeBrokerMakerTradeRejectionEventHandler.EVENT, new RFSPrimeBrokerHandlers.RFSPrimeBrokerMakerTradeRejectionEventHandler(cache) );
        eventHandler.setEventHandler( RFSPrimeBrokerHandlers.RFSPrimeBrokerMakerTradeVerificationEventHandler.EVENT, new RFSPrimeBrokerHandlers.RFSPrimeBrokerMakerTradeVerificationEventHandler(cache) );
        eventHandler.setEventHandler( RFSPrimeBrokerHandlers.RFSPrimeBrokerCoverTradeRejectionEventHandler.EVENT, new RFSPrimeBrokerHandlers.RFSPrimeBrokerCoverTradeRejectionEventHandler(cache) );
        eventHandler.setEventHandler( RFSPrimeBrokerHandlers.RFSPrimeBrokerCoverTradeVerificationEventHandler.EVENT, new RFSPrimeBrokerHandlers.RFSPrimeBrokerCoverTradeVerificationEventHandler(cache) );

        eventHandler.setEventHandler( CreateManualTradeEventHandler.EVENT , new CreateManualTradeEventHandler(cache) );
        eventHandler.setEventHandler( RFSTradeHandlers.PostTradeUpdateEventHandler.EVENT, new RFSTradeHandlers.PostTradeUpdateEventHandler(cache) );
    }

    public void initRequestSerializers() {
        //Do Nothing
    }


    public EventBasedProcessorDescriptor getEventHandler()
    {
        return eventHandler;
    }

    public void reclaim( ProcessorDescriptor pipeline )
    {
        // TODO Auto-generated method stub

    }

}
