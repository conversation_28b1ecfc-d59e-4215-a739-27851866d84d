package com.integral.dbservice.is;

/**
 * <AUTHOR>
 */
public class BrokerTradeHandlerTypes {

    public static String espCreateCustBrokerTrade = "espCreateCustBrokerTrade";
    public static String espTraderCreateTrade = "espTraderCreateTrade";
    public static String espCustBrokerTradeVerified = "espCustBrokerTradeVerified";
    public static String espCustBrokerTradeRejected = "espCustBrokerTradeRejected";

    public static String rfsTraderCreateTrade = "rfsTraderCreateTrade";
    public static String rfsCreateCustBrokerTrade = "rfsCreateCustBrokerTrade";
    public static String rfsCustBrokerTradeVerified = "rfsCustBrokerTradeVerified";
    public static String rfsCustBrokerTradeRejected = "rfsCustBrokerTradeRejected";
}
