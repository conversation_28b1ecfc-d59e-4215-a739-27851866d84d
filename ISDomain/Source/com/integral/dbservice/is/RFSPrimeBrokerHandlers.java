package com.integral.dbservice.is;

import com.integral.cache.distributed.Message;
import com.integral.dbservice.TransactionLogMessage;
import com.integral.exception.IdcDatabaseException;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.fx.FXSingleLegDealC;
import com.integral.finance.dealing.fx.FXSwapDealC;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.trade.CptyTrade;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.functor.TradeServiceTransactionFunctor;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.facade.DealFacade;
import com.integral.is.common.facade.ISPersistenceFactory;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.ISTransactionManager;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mtf.config.ConfigManager;
import com.integral.mtf.config.MTFMessageConfig;
import com.integral.mtf.message.builder.MTFEventMessageBuilder;
import com.integral.mtf.model.EventCode;
import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import com.integral.session.IdcTransaction;
import com.integral.spaces.ApplicationSpaceEvent;
import com.integral.user.Organization;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by IntelliJ IDEA.
 * User: verma
 * Date: Apr 16, 2010
 * Time: 5:16:29 PM
 * To change this template use File | Settings | File Templates.
 */
public class RFSPrimeBrokerHandlers
{
    private static ISPersistenceFactory persistenceFactory = ISPersistenceFactory.getInstance();
    
    public static final AtomicInteger BASE_INDEX = new AtomicInteger(400);

    private static ISMBean isMBean = ISFactory.getInstance().getISMBean();

    private static MTFMessageConfig messageConfig = ConfigManager.getMtfMessageConfig();

    private static Log log = LogFactory.getLog(RFSPrimeBrokerHandlers.class);

    public static class RFSPrimeBrokerMakerTradeVerificationEventHandler extends Processor
    {
        public static String EVENT = "rfsPrimeBrokerMakerTradeVerification";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public RFSPrimeBrokerMakerTradeVerificationEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Trade makerTrade = ( Trade ) tlm.entity;
                Request makerTradeRequest = makerTrade.getRequest();
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSPrimeBrokerHandlers.RFSPrimeBrokerMakerTradeVerificationEventHandler.process called for makerTrade=" ).append( makerTrade.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );
                Quote makerRequestAcceptedQuote = makerTradeRequest.getAcceptedQuote();
                makerTradeRequest.setAcceptedQuote( makerRequestAcceptedQuote );
                makerRequestAcceptedQuote.setRequest( makerTradeRequest );
                makerTradeRequest.setTrade( makerTrade );
                makerTrade.setRequest( makerTradeRequest );

                ISTransactionManager.setUser( makerTrade.getRequest().getUser() );
                IdcTransaction tx = ISTransactionManager.startTransaction( ISTransactionManager.getDealWorkflowReadOnlyClasses(),EVENT );
                DealFacade dealFacade = getDealFacade( makerTradeRequest,makerTradeRequest.getUser().getOrganization() );
                Deal makerDeal = dealFacade.dealCreate( makerTradeRequest, EVENT );

				dealFacade.dealVerified( makerTrade, makerDeal, null );

				Deal registeredMakerDeal = makerDeal;
                registeredMakerDeal.setTaker( false );
                flipDeal( registeredMakerDeal );

                if( makerTrade instanceof FXSwap )
                {
	                FXSwap swapTrade = (FXSwap)makerTrade;
	                if( swapTrade.getNearLeg() != null && swapTrade.getNearLeg().getFXPayment() != null && swapTrade.getNearLeg().getFXPayment().getFXCoverRate() != null )
	                {
	                	dealFacade.updateMarketRate(registeredMakerDeal, swapTrade.getNearLeg().getFXPayment().getFXCoverRate().getMarketFxRate(), true);
	                }
	                if( swapTrade.getFarLeg() != null && swapTrade.getFarLeg().getFXPayment() != null && swapTrade.getFarLeg().getFXPayment().getFXCoverRate() != null )
	                {
	                	dealFacade.updateMarketRate(registeredMakerDeal, swapTrade.getFarLeg().getFXPayment().getFXCoverRate().getMarketFxRate(), false);
	                }
                }
                else
                {
                	FXSingleLeg singleLeg = (FXSingleLeg) makerTrade;
	                if( singleLeg.getFXLeg() != null && singleLeg.getFXLeg().getFXPayment() != null && singleLeg.getFXLeg().getFXPayment().getFXCoverRate() != null )
	                	dealFacade.updateMarketRate(registeredMakerDeal, singleLeg.getFXLeg().getFXPayment().getFXCoverRate().getMarketFxRate(), true);
                }

                ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( makerDeal, ApplicationEventCodes.EVENT_CD_TRADE_ACCEPT_VERIFY);
                tlm.deals.add(ase);

                cache.clearCachesForTradeRequestWithDelay( makerTradeRequest, EVENT );
                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(makerTrade,EventCode.TXN_RFS_PRIME_BROKER_MAKER_TRADE_VERIFY);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSPrimeBrokerHandlers.RFSPrimeBrokerMakerTradeVerificationEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSPrimeBrokerHandlers.RFSPrimeBrokerMakerTradeVerificationEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }

    public static class RFSPrimeBrokerCoverTradeVerificationEventHandler extends Processor
    {
        public static String EVENT = "rfsPrimeBrokerCoverTradeVerification";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public RFSPrimeBrokerCoverTradeVerificationEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Trade coverTrade = ( Trade ) tlm.entity;
                Request coverTradeRequest = coverTrade.getRequest();
                Quote coverRequestAcceptedQuote = coverTradeRequest.getAcceptedQuote();
                coverRequestAcceptedQuote.setRequest( coverTradeRequest );
                coverTradeRequest.setTrade( coverTrade );
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSPrimeBrokerHandlers.RFSPrimeBrokerCoverTradeVerificationEventHandler.process called for coverTrade=" ).append( coverTrade.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );

                ISTransactionManager.setUser( coverTrade.getRequest().getUser() );
                IdcTransaction tx = ISTransactionManager.startTransaction( EVENT );

                Request registeredCoverTradeRequest = ( Request ) coverTradeRequest.getRegisteredObject();

                DealFacade df = getDealFacade( coverTradeRequest ,coverTradeRequest.getUser().getOrganization());
                Deal coverDeal = df.dealCreate( coverTradeRequest, EVENT );
                df.dealVerified( coverTrade, coverDeal, null );

                Deal registeredCoverDeal = coverDeal;
                registeredCoverDeal.setTaker( true );

                ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( coverDeal, ApplicationEventCodes.EVENT_CD_TRADE_ACCEPT_VERIFY);
                tlm.deals.add(ase);

                for ( CptyTrade cptyTrd : coverTrade.getCptyTrades() )
                {
                    cptyTrd = ( CptyTrade ) cptyTrd.getRegisteredObject();
                    cptyTrd.setWorkflowStateMap( registeredCoverTradeRequest.getTrade().getWorkflowStateMap() );
                    cptyTrd.setTrade( registeredCoverTradeRequest.getTrade() );
                }

                TradeServiceTransactionFunctor.verifyTrade( coverTrade );
                cache.addDeal( coverTrade,coverDeal );
                cache.addRequest( coverTradeRequest );
                cache.addTrade( coverTrade );

                Map<String,String> valueMap = new HashMap<String,String>(1);
                valueMap.put( "txId",coverTrade.getTransactionID() );
                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(coverTrade,EventCode.TXN_RFS_PRIME_BROKER_COVER_TRADE_VERIFY);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSPrimeBrokerHandlers.RFSPrimeBrokerCoverTradeVerificationEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSPrimeBrokerHandlers.RFSPrimeBrokerCoverTradeVerificationEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }

    public static class RFSPrimeBrokerMakerTradeRejectionEventHandler extends Processor
    {
        public static String EVENT = "rfsPrimeBrokerMakerTradeRejection";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public RFSPrimeBrokerMakerTradeRejectionEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Trade makerTrade = ( Trade ) tlm.entity;
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSPrimeBrokerHandlers.RFSPrimeBrokerMakerTradeRejectionEventHandler.process called for makerTrade= " ).append( makerTrade.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );

                Request makerTradeRequest = makerTrade.getRequest();
                Quote makerRequestAcceptedQuote = makerTrade.getRequest().getAcceptedQuote();
                makerTradeRequest.setAcceptedQuote( makerRequestAcceptedQuote );
                makerTradeRequest.setTrade( makerTrade );
                makerRequestAcceptedQuote.setRequest( makerTradeRequest );

                ISTransactionManager.setUser( makerTradeRequest.getUser() );
                IdcTransaction tx = ISTransactionManager.startTransaction( ISTransactionManager.getDealWorkflowReadOnlyClasses(),EVENT );
                Deal makerDeal = getDealFacade( makerTradeRequest ).dealCreate( makerTradeRequest, EVENT);
                getDealFacade( makerTradeRequest ).dealRejected( makerTrade, makerDeal, null );

                Deal registeredMakerDeal = makerDeal;
                flipDeal( registeredMakerDeal );
                registeredMakerDeal.setTaker( false );

                ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( makerDeal, ApplicationEventCodes.EVENT_CD_TRADE_ACCEPT_REJECT);
                tlm.deals.add(ase);

                cache.clearCachesForTradeRequestWithDelay( makerTradeRequest, EVENT );
                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(makerTrade,EventCode.TXN_RFS_PRIME_BROKER_MAKER_TRADE_REJECT);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSPrimeBrokerHandlers.RFSPrimeBrokerMakerTradeRejectionEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSPrimeBrokerHandlers.RFSPrimeBrokerMakerTradeRejectionEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }

    public static class RFSPrimeBrokerCoverTradeRejectionEventHandler extends Processor
    {
        public static String EVENT = "rfsPrimeBrokerCoverTradeRejection";
        public static int INDEX = BASE_INDEX.incrementAndGet();
        private final ISPersistenceServiceCacheManager cache;

        public RFSPrimeBrokerCoverTradeRejectionEventHandler(ISPersistenceServiceCacheManager cache) {
            this.cache = cache;
        }

        @Override
        public PipelineMessage process( PipelineMessage msg )
        {
            try
            {
                TransactionLogMessage tlm = ( TransactionLogMessage ) msg;
                Trade trade = ( Trade ) tlm.entity;
                log.info( new StringBuilder( "Tenant:" + cache.getPeerVirtualServer() +". RFSPrimeBrokerHandlers.RFSPrimeBrokerCoverTradeRejectionEventHandler.process called for trade= " ).append( trade.getTransactionID() ).append( ", msg correlationID = " ).append( msg.getCorelationId() ).toString() );

                Request coverTradeRequest = trade.getRequest();
                Quote coverRequestAcceptedQuote = trade.getRequest().getAcceptedQuote();
                coverTradeRequest.setTrade( trade );
                coverRequestAcceptedQuote.setRequest( coverTradeRequest );
                ISTransactionManager.setUser( coverTradeRequest.getUser() );
                IdcTransaction tx = ISTransactionManager.startTransaction( EVENT );
                Request registeredCoverTradeRequest = ( Request ) coverTradeRequest.getRegisteredObject();
                Deal coverDeal = getDealFacade( coverTradeRequest ).dealCreate( coverTradeRequest, EVENT);
                getDealFacade( coverTradeRequest ).dealRejected( trade, coverDeal, null );

                Deal registeredCoverDeal = coverDeal;
                registeredCoverDeal.setTaker( true );


                ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( coverDeal, ApplicationEventCodes.EVENT_CD_TRADE_ACCEPT_REJECT);
                tlm.deals.add(ase);

                for ( CptyTrade cptyTrd : trade.getCptyTrades() )
                {
                    cptyTrd = ( CptyTrade ) cptyTrd.getRegisteredObject();
                    cptyTrd.setWorkflowStateMap( registeredCoverTradeRequest.getTrade().getWorkflowStateMap() );
                    cptyTrd.setTrade( registeredCoverTradeRequest.getTrade() );
                }
                cache.clearCachesForTradeRequestWithDelay( coverTradeRequest, EVENT );
                tlm.mtfEventMessage = MTFEventMessageBuilder.createMTFEventMessage(trade,EventCode.TXN_RFS_PRIME_BROKER_COVER_TRADE_REJECT);
            }
            catch ( Exception e )
            {
                log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSPrimeBrokerHandlers.RFSPrimeBrokerCoverTradeRejectionEventHandler.process : Exception occured " + e );
                if ( e instanceof IdcDatabaseException )
                {
                    if ( ISTransactionManager.isTransactionOn() )
                    {
                        try
                        {
                            UnitOfWork uow = ISTransactionManager.getTransaction().getUOW();
                            uow.printRegisteredObjects();
                        }
                        catch ( IdcNoSuchObjectException e1 )
                        {
                            log.error( "Tenant:" + cache.getPeerVirtualServer() +". RFSPrimeBrokerHandlers.RFSPrimeBrokerCoverTradeRejectionEventHandler.process : Exception in printing registered objects " + e1 );
                        }
                    }
                }
                throw new PipelineException( e );
            }
            return msg;
        }
    }

    public static DealFacade getDealFacade( Request tradeRequest )
    {
        return   getDealFacade(tradeRequest, tradeRequest.getOrganization());
    }

    public static DealFacade getDealFacade( Request tradeRequest ,Organization org)
    {
        if ( tradeRequest.getRequestAttributes().isSwap() )
        {
            return persistenceFactory.getDealFacade( ISConstantsC.TRD_CLSF_SWAP , org);
        }
        else
        {
            return persistenceFactory.getDealFacade( ISConstantsC.TRD_CLSF_OR , org);
        }
    }

    private static void flipDeal( Deal makerDeal )
    {
        boolean isSingleLeg = makerDeal.getDealLegs().size() == 1;
        if ( isSingleLeg )
        {
            FXSingleLegDealC deal = ( FXSingleLegDealC ) makerDeal;
            if ( deal.getFXDealLeg().getAcceptedBidOfferMode() == DealingPrice.BID )
            {
                deal.getFXDealLeg().setAcceptedBidOfferMode( DealingPrice.OFFER );
            }
            else
            {
                deal.getFXDealLeg().setAcceptedBidOfferMode( DealingPrice.BID );
            }
        }
        else
        {
            FXSwapDealC deal = ( FXSwapDealC ) makerDeal;
            if ( deal.getNearDealLeg().getAcceptedBidOfferMode() == DealingPrice.BID )
            {
                deal.getNearDealLeg().setAcceptedBidOfferMode( DealingPrice.OFFER );
            }
            else
            {
                deal.getNearDealLeg().setAcceptedBidOfferMode( DealingPrice.BID );
            }

            if ( deal.getFarDealLeg().getAcceptedBidOfferMode() == DealingPrice.BID )
            {
                deal.getFarDealLeg().setAcceptedBidOfferMode( DealingPrice.OFFER );
            }
            else
            {
                deal.getFarDealLeg().setAcceptedBidOfferMode( DealingPrice.BID );
            }
        }
    }
}
