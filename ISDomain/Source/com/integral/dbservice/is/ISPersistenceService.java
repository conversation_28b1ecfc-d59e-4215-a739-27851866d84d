package com.integral.dbservice.is;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.integral.dbservice.DBService;
import com.integral.finance.dealing.Request;
import com.integral.finance.trade.Trade;
import com.integral.is.ISCommonConstants;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.management.ManagementConstants;
import com.integral.message.ErrorMessage;
import com.integral.message.ErrorMessageC;
import com.integral.persistence.Entity;
import com.integral.spaces.CorelationId;
import com.integral.user.User;
import com.integral.util.IdcUtilC;

public class ISPersistenceService
{

	private static Log log = LogFactory.getLog(ISPersistenceService.class);

	private static ISPersistenceService instance = null;

	private DBService dbService = null;

	/**
	 * Even though this is a public method, this should be only set by the startup task once for the lifetime of the system.
	 *
	 * @param globalInstance
	 */
	public static void __private_startup_setInstance( ISPersistenceService globalInstance )
	{
		instance = globalInstance;
	}

	public ISPersistenceService( DBService dbService )
	{
		this.dbService = dbService;
	}

	public DBService getDBServiceInstance()
	{
		return this.dbService;
	}

	public static ISPersistenceService instance()
	{
		return instance;
	}


	public ErrorMessage espTradeCancellation( Trade trade )
	{
		try
		{
			String orderId = null;
			if ( trade.getRequest() != null )
				orderId = getCorelationId(trade.getRequest());
			else
				orderId = trade.getTransactionID(); // For ManualTrades
//			dbService.processEvent(orderId, ESPTradeHandlers.TradeCancelEventHandler.EVENT, trade, null, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.espTradeCancellation : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

    public ErrorMessage espNetTradeCancellation( Trade trade )
    {
        try
        {
            String orderId = null;
            if ( trade.getRequest() != null )
                orderId = getCorelationId(trade.getRequest());
            else
                orderId = trade.getTransactionID(); // For ManualTrades
//            dbService.processEvent(orderId, ESPTradeHandlers.NetTradeCancelEventHandler.EVENT, trade, null, ISUtilImpl.getInstance().isWarmUpObject(trade));
        }
        catch ( Exception e )
        {
            log.error("ISPersistenceService.espTradeCancellation : Error in updating  trade  " + trade.getTransactionID(), e);
            return getErrorMessage(e);
        }
        return null;
    }


    public ErrorMessage espTradeAmend( Trade trade )
	{
		try
		{
			String orderId = null;
			if ( trade.getRequest() != null )
				orderId = getCorelationId(trade.getRequest());
			else
				orderId = trade.getTransactionID(); // For ManualTrades
//			dbService.processEvent(orderId, ESPTradeHandlers.TradeAmendEventHandler.EVENT, trade, null, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.espTradeAmend : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

	public ErrorMessage rfsTradeAcceptance( Request tradeRequest )
	{
		try
		{
			String orderId = getCorelationId(tradeRequest);
			dbService.processEvent(orderId, RFSTradeHandlers.TradeAcceptanceEventHandler.EVENT, tradeRequest, null, ISUtilImpl.getInstance().isWarmUpObject(tradeRequest));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsTradeAcceptance : Error in updating  trade request " + tradeRequest.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;

	}

	public ErrorMessage rfsRequestPublished( Request request )
	{
		try
		{
			String orderId = getCorelationId(request);
			dbService.processEvent(orderId, RFSTradeHandlers.RequestPublishedEventHandler.EVENT, request, null, ISUtilImpl.getInstance().isWarmUpObject(request));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsRequestPublished : Error in updating  trade request " + request.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;

	}

	public ErrorMessage rfsTradePendingVerification( Request tradeRequest )
	{
		try
		{
			String orderId = getCorelationId(tradeRequest);
			dbService.processEvent(orderId, RFSTradeHandlers.TradePendingVerificationEventHandler.EVENT, tradeRequest, null, ISUtilImpl.getInstance().isWarmUpObject(tradeRequest));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsTradePendingVerification : Error in updating  trade request  " + tradeRequest.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;

	}

	public ErrorMessage rfsTradeVerification( Trade trade )
	{
		try
		{
			String orderId = getCorelationId(trade.getRequest());
			dbService.processEvent(orderId, RFSTradeHandlers.TradeVerificationEventHandler.EVENT, trade, null, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsTradeVerification : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}
	
	public ErrorMessage changeLPLEOnTrade(Trade trade)
	{
		try
		{
			String orderId = getCorelationId(trade.getRequest());
			dbService.processEvent(orderId, RFSTradeHandlers.ChangeLPLEOnTradeEventHandler.EVENT, trade, null, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.changeLPLEOnTrade : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

	public ErrorMessage rfsTradeRejection( Trade trade )
	{
		try
		{
			String orderId = getCorelationId(trade.getRequest());
			dbService.processEvent(orderId, RFSTradeHandlers.TradeRejectionEventHandler.EVENT, trade, null, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsTradeRejection : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;

	}

	public ErrorMessage rfsTradeConfirmation( Trade trade )
	{

		try
		{
			String orderId = null!=trade.getRequest() ? getCorelationId(trade.getRequest()): trade.getTransactionID(); // For ManualTrades
			dbService.processEvent(orderId, RFSTradeHandlers.TradeConfirmationEventHandler.EVENT, trade, null, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsTradeConfirmation : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}
	
	public ErrorMessage rfsPostTradeUpdate( Trade trade )
	{

		try
		{
			String orderId = null!=trade.getRequest() ? getCorelationId(trade.getRequest()): trade.getTransactionID(); // For ManualTrades
			dbService.processEvent(orderId, RFSTradeHandlers.PostTradeUpdateEventHandler.EVENT, trade, null, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsPostTradeUpdate : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

	public ErrorMessage rfsCoverTradeConfirmation( Trade trade )
	{

		try
		{
			String orderId = getCorelationId(trade.getRequest());
			dbService.processEvent(orderId, RFSTradeHandlers.TradeConfirmationEventHandler.EVENT, trade, null, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsCoverTradeConfirmation : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

	public ErrorMessage rfsTradeFailed( Request request )
	{
		try
		{
			String orderId = getCorelationId(request);
			dbService.processEvent(orderId, RFSTradeHandlers.TradeFailedEventHandler.EVENT, request, null, ISUtilImpl.getInstance().isWarmUpObject(request));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsTradeFailed : Error in updating  trade request " + request.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

	public ErrorMessage rfsTradeCancellation( Trade trade )
	{
		try
		{
			String orderId = null;
			if ( trade.getRequest() != null )
				orderId = getCorelationId(trade.getRequest());
			else
				orderId = trade.getTransactionID(); // For ManualTrades
			Map<String, String> propertyMap = null;
			User sessionUser = IdcUtilC.getSessionContextUser ();
			if ( sessionUser != null )
			{
				propertyMap = new HashMap<String, String> ();
				propertyMap.put ( ISCommonConstants.JMS_PROP_USERNAME, IdcUtilC.getSessionContextUser ().getFullyQualifiedName () );
			}
			dbService.processEvent(orderId, RFSTradeHandlers.TradeCancelEventHandler.EVENT, trade, propertyMap, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsTradeCancellation : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

	public ErrorMessage rfsRequestExpire( Request request )
	{
		try
		{
			String orderId = request.getTransactionID(); //Note correlation id is different here as no trade workflow involved
			if ( request.getCorelationId() != null )
				orderId = request.getCorelationId();
			dbService.processEvent(orderId, RFSTradeHandlers.RequestExpireEventHandler.EVENT, request, null, ISUtilImpl.getInstance().isWarmUpObject(request));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsRequestExpire : Error in updating  request  " + request.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

	public ErrorMessage rfsTradeRequestExpire( Request request )
	{
		try
		{
			String orderId = getCorelationId(request);
			dbService.processEvent(orderId, RFSTradeHandlers.TradeRequestExpireHandler.EVENT, request, null, ISUtilImpl.getInstance().isWarmUpObject(request));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.espTradeRequestExpire : Error in updating  request  " + request.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

	public ErrorMessage rfsRequestCancel( Request request )
	{
		try
		{
			String orderId = request.getTransactionID(); //Note correlation id is different here as no trade workflow involved
			if ( request.getCorelationId() != null )
				orderId = request.getCorelationId();
			dbService.processEvent(orderId, RFSTradeHandlers.RequestCancelEventHandler.EVENT, request, null, ISUtilImpl.getInstance().isWarmUpObject(request));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsRequestCancel : Error in updating  request  " + request.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

	public ErrorMessage rfsTradeAmend( Trade trade )
	{
		try
		{
			String orderId = null;
			if ( trade.getRequest() != null )
				orderId = getCorelationId(trade.getRequest());
			else
				orderId = trade.getTransactionID(); // For ManualTrades
			dbService.processEvent(orderId, RFSTradeHandlers.TradeAmendEventHandler.EVENT, trade, null, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsTradeAmend : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

	public ErrorMessage sendRFSSTPTradeStautusUpdateToGM(Trade trade, String errorReason)
	{
		try
		{
			Map<String, String> properties = new HashMap<String, String>();
			properties.put(ManagementConstants.CH_SDR_ERRORREASON_KEY, errorReason);
			String orderId = trade.getRequest()!=null ? getCorelationId(trade.getRequest()):trade.getTransactionID();
			orderId = trimOrderId(orderId);
			dbService.processEvent(orderId, RFSTradeHandlers.STPTradeStatusUpdateToGMEventHandler.EVENT, trade, properties, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.sendRFSSTPTradeStautusUpdateToGM : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}
	
	public ErrorMessage rfsSTPTradeStatusUpdated( Trade trade, boolean updateGridMonitor, String errorMsgForGM )
	{
		try
		{
			Map<String, String> properties = new HashMap<String, String>();
			if(errorMsgForGM != null && !"".equals(errorMsgForGM.trim()))
			{
				properties.put(ManagementConstants.CH_SDR_ERRORREASON_KEY, errorMsgForGM);
			}			
			String orderId = trade.getRequest()!=null ? getCorelationId(trade.getRequest()):trade.getTransactionID();
			orderId = trimOrderId(orderId);
			dbService.processEvent(orderId, RFSTradeHandlers.STPTradeStatusUpdateEventHandler.EVENT, trade, properties, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsSTPTradeStatusUpdated : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

	public ErrorMessage rfsPrimeBrokerMakerTradeVerified( Trade trade )
	{
		try
		{
			String orderId = getCorelationId(trade.getRequest());
			dbService.processEvent(orderId, RFSPrimeBrokerHandlers.RFSPrimeBrokerMakerTradeVerificationEventHandler.EVENT, trade, null, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsPrimeBrokerMakerTradeVerified : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

	public ErrorMessage rfsPrimeBrokerCoverTradeVerified( Trade trade )
	{
		try
		{
			String orderId = getCorelationId(trade.getRequest());
			dbService.processEvent(orderId, RFSPrimeBrokerHandlers.RFSPrimeBrokerCoverTradeVerificationEventHandler.EVENT, trade, null, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsPrimeBrokerCoverTradeVerified : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

	public ErrorMessage rfsPrimeBrokerMakerTradeRejected( Trade trade )
	{
		try
		{
			String orderId = getCorelationId(trade.getRequest());
			dbService.processEvent(orderId, RFSPrimeBrokerHandlers.RFSPrimeBrokerMakerTradeRejectionEventHandler.EVENT, trade, null, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsPrimeBrokerMakerTradeRejected : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

	public ErrorMessage rfsPrimeBrokerCoverTradeRejected( Trade trade )
	{
		try
		{
			String orderId = getCorelationId(trade.getRequest());
			dbService.processEvent(orderId, RFSPrimeBrokerHandlers.RFSPrimeBrokerCoverTradeRejectionEventHandler.EVENT, trade, null, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsPrimeBrokerMakerTradeRejected : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}

	public ErrorMessage processEvents( String corelationId, String event, List<Entity> entities )
	{
		return processEvents(corelationId, event, entities, null);
	}

	public ErrorMessage processEvents( String corelationId, String event, List<Entity> entities, HashMap<String, String> propMap )
	{
		boolean isWarmup = false;
		for ( Entity e : entities )
		{
			if ( ISUtilImpl.getInstance().isWarmUpObject(e) )
			{
				isWarmup = true;
				break;
			}
		}
		if ( !isWarmup )
		{
			try
			{
				dbService.processEvents(corelationId, event, entities, propMap, isWarmup);
			}
			catch ( Exception ex )
			{
				log.error("ISPersistenceService.processEvent : Error in Processing Event " + entities + " for corelationId " + corelationId + " and entity " + entities, ex);
				return getErrorMessage(ex);
			}
		}
		return null;
	}

	protected ErrorMessage getErrorMessage( Exception e )
	{
		ErrorMessage errMessage = new ErrorMessageC();
		errMessage.addError(e);
		return errMessage;
	}

	public String getCorelationId( Request request )
	{
		String coId = request.getCorelationId();
		if ( coId != null )
			return coId;
		else
		{
			String orderId = request.getOrderId();
			orderId = trimOrderId(orderId);
			return orderId;
		}
	}

	private String trimOrderId(String orderId) 
	{
		int lastIndexOfC = orderId.lastIndexOf('C');
		if (  lastIndexOfC != -1 )
		{
			orderId = orderId.substring(0, lastIndexOfC);
		}
		return orderId;
	}

	/**
	 *
	 * @param corelationId
	 * @param entities
	 * @param isWarmup
	 * @return
	 */
	public ErrorMessage persistManualTrade( String corelationId, List<Entity> entities, boolean isWarmup )
	{
		try
		{
			dbService.processEvents(corelationId, CreateManualTradeEventHandler.EVENT, entities, null, isWarmup);
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.persistManualTrade : Failed to persist manual trade " + corelationId, e);
			return getErrorMessage(e);
		}
		return null;

	}

	public ErrorMessage rfsTradeMiFiDTradeAmend( Trade trade )
	{
		try
		{

			String tradeId = trade.getTransactionID(); // For ManualTrades
			dbService.processEvent(tradeId, RFSTradeHandlers.TradeMiFiDAmendEventHandler.EVENT, trade, null, ISUtilImpl.getInstance().isWarmUpObject(trade));
		}
		catch ( Exception e )
		{
			log.error("ISPersistenceService.rfsTradeMiFiDTradeAmend : Error in updating  trade  " + trade.getTransactionID(), e);
			return getErrorMessage(e);
		}
		return null;
	}
}
