package com.integral.dbservice;

import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Jan 6, 2011
 * Time: 5:53:06 PM
 * This processor is used to replay log messages in a server. The creation times of the messages are read and based on the
 * replay factor, the process introduces a delay before forwarding the message to the rest of the pipeline. This way, we
 * can simulate a set of messages as they were played in real time.
 */
public class DBTxnLogReplayProcessor extends Processor {

    private final ISPersistenceMBean config;
    private long lastProcessedTime; //No need to synchronize as there is one thread assigned per pipeline.

    public DBTxnLogReplayProcessor(DBService dbService) {
        this.config = dbService.getConfig();
        log.info("DBTxnLogReplayProcessor initialized with replay factor:" + config.getReplayFactor());
    }

    @Override
    public PipelineMessage process(PipelineMessage msg) {
        long creationTime = ((TransactionLogMessage) msg).creationTime; //set in millis in SerializeToJsonProcessor
        long delayInterval = 0;
        if (lastProcessedTime != 0) {
            double replayFactor = config.getReplayFactor();//Checking on every call allows the replay factor to change at runtime.            
            delayInterval = (long) ((creationTime - lastProcessedTime) / replayFactor);
            if (delayInterval > 0) {
                log.warn("DBTxnLogReplayProcessor: Introducing a delay of " + delayInterval + " milliseconds. Last Processed Time:" + lastProcessedTime + ". Current Msg Creation Time:" + creationTime + ". ReplayFactor:" + replayFactor + ". Message :" +msg);
                try {
                    Thread.sleep(delayInterval);
                } catch (InterruptedException e) {
                    //ignore
                }
            } else if (delayInterval < 0) {
                log.warn("Received out of order message in DBTxnLogReplayProcessor. Last Msg Creation Time:" + lastProcessedTime + ", CurrentMsg Creation Time:" + creationTime + ". Message:" + msg);
            }
        }
        lastProcessedTime = creationTime;
        return msg;
    }
}
