package com.integral.dbservice;

import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.ServerRuntimeMBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;

/**
 * <AUTHOR>
 */
public class DisableTradingProcessor extends Processor {
    @Override
    public PipelineMessage process(PipelineMessage msg) {
        ArrayList<Exception> errors = msg.getErrors();
        for (Iterator<Exception> iterator = errors.iterator(); iterator.hasNext();) {
            Exception exception = iterator.next();
            if (exception instanceof PipelineException) {
                PipelineException pe  = (PipelineException) exception;
                if (pe.sourceException != null && pe.sourceException instanceof java.io.IOException) {
                   ServerRuntimeMBean runTimeMBean = ( ServerRuntimeMBean ) RuntimeFactory.getServerRuntimeMBean();
                    HashMap props = new HashMap();
                    props.put("error", pe.sourceException.toString());
                    runTimeMBean.setTradesEnabled(false, false, props);
                    log.error("Disable trading due to IOException: " + pe.sourceException.getMessage(), pe.sourceException);
                }
            }
        }
        return null;  //To change body of implemented methods use File | Settings | File Templates.
    }
}
