/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.integral.dbservice.mongo;

import com.mongodb.DB;
import com.mongodb.DBCollection;

/**
 *
 * <AUTHOR>
 */
public interface MongoDBPersistanceService {

    DBCollection getOrCreateCollection(String collectionName) throws MongoDBException;

    DBCollection getOrCreateCollection(String collectionName, String concern) throws MongoDBException;

    DB getDatBase();
}