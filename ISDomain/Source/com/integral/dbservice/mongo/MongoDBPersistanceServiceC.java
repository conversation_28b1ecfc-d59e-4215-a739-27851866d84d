/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package com.integral.dbservice.mongo;

import com.integral.is.common.rfs.RFSConstants;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.PersistenceMBean;
import com.integral.util.collections.ConcurrentHashSet;
import com.mongodb.*;

import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class MongoDBPersistanceServiceC implements MongoDBPersistanceService {

    private Mongo mongo;
    private DB database;
    private static Log logger = LogFactory.getLog(MongoDBPersistanceServiceC.class);
    private PersistenceMBean persistenceBean = null;
    private ConcurrentHashMap<String,Boolean> allCollectionNames = new ConcurrentHashMap<String,Boolean>();
    static String DEFAULT_CONCERN = "SAFE";

    private static MongoDBPersistanceService instance;
    private WriteConcern writeConcern;

    private MongoDBPersistanceServiceC() {
        persistenceBean = PersistenceFactory.getPersistenceMBean();
        try {
            initialize();
        } catch (com.integral.dbservice.mongo.MongoDBException e) {
            logger.error("ERROR: unable to initialize the persistence service");
        }
    }

    // this is used in junits
    protected MongoDBPersistanceServiceC(PersistenceMBean persistenceBean) {
        logger.info("Method called is intended for unit test only. Raise alert if this is production environment.");
        this.persistenceBean = persistenceBean;
        try {
            initialize();
        } catch (com.integral.dbservice.mongo.MongoDBException e) {
            logger.error("ERROR: unable to initialize the persistence service");
            throw new RuntimeException("Error creation MongoDBPersistanceService");
        }
    }

    public static MongoDBPersistanceService getInstance() {
        if (instance == null) {
            synchronized (MongoDBPersistanceServiceC.class) {
                if (instance == null) {
                    instance = new MongoDBPersistanceServiceC();
                }
            }
        }
        return instance;
    }

    private void initialize() throws com.integral.dbservice.mongo.MongoDBException {
        try {
            MongoOptions options = new MongoOptions();
            options.connectTimeout = 60000;  // TODO Make it configurable?    setting this as 1 minute as this time should be good enough to select the master from the replica set
            options.connectionsPerHost = 5; //Mongo Connection pool size. TODO Make it configurable?
            List<ServerAddress> allServerAddresses = getServerAddresses(persistenceBean.getMongoDBServerAddress());
            // this configuration will be used when the data is being sharded
            logger.info("Mongo: records in Config " + persistenceBean.getMongoDBServerAddress().size() + " hosts connected: " + allServerAddresses.size());
            if (persistenceBean.getMongoDBServerAddress().size() == 1 && allServerAddresses.size() == 1) {
                mongo = new Mongo(allServerAddresses.get(0), options);
            } else if (allServerAddresses.size() >= 1) { // this configuration will be used when there are multiple replica sets
                mongo = new Mongo(allServerAddresses, options);
            } else {  // no mongo server found
                throw new RuntimeException("No valid configuration found for Mongo");
            }
            writeConcern = persistenceBean.getMongoDBCollectionWriteConcern() == null ? WriteConcern.valueOf(DEFAULT_CONCERN) :  WriteConcern.valueOf(persistenceBean.getMongoDBCollectionWriteConcern());
            /*ServerAddress address = new ServerAddress(persistenceBean.getMongoDBDatabaseServerName(), persistenceBean.getMongoDBDatabaseServerPort());
            mongo = new Mongo(address, options);*/
            mongo.setReadPreference(ReadPreference.SECONDARY);
            database = mongo.getDB(persistenceBean.getMongoDBSchemaName());
        } catch (MongoException ex) {
            logger.error("ERROR: Unable to connect to MongoDB engine", ex);
            throw new com.integral.dbservice.mongo.MongoDBException("ERROR: Unable to connect to MongoDB engine");
        }
    }

    private List<ServerAddress> getServerAddresses(List<String> servers) {
        List<ServerAddress> allServer = new ArrayList<ServerAddress>();
        logger.info("Tokenizing Mongo server addresses");
        for (String current : servers) {
            String[] info = current.split(":");
            if (info.length != 2) {
                logger.error("ERROR: Wrong configuration for Mongo found: " + current);
            } else {
                try {
                    ServerAddress sa = new ServerAddress(info[0], Integer.parseInt(info[1]));
                    allServer.add(sa);
                } catch (UnknownHostException uhe) {
                    logger.error("ERROR: creating server address from " + info[0] + " : " + info[1], uhe);
                }

            }
        }
        return allServer;
    }

    public DBCollection getOrCreateCollection(String collectionName) throws com.integral.dbservice.mongo.MongoDBException {
        return getOrCreateCollection(collectionName, writeConcern);
    }

    private DBCollection getOrCreateCollection(String collectionName, WriteConcern writeConcern) {
        DBCollection dbc = database.getCollection(collectionName);
        dbc.setWriteConcern(writeConcern);
        if(allCollectionNames.get(collectionName) == null){
            /**
             * This may be the case when a first query is based on for a collection.
             * As of first implementation there is a need for an index on "TRANSACTIONID" for both the request and the stream collection.
             * so ensuring index creation here.
             */
             dbc.ensureIndex(RFSConstants.TRANSACTIONID);
            allCollectionNames.putIfAbsent(collectionName,Boolean.TRUE);
        }
        return dbc;
    }

    public DBCollection getOrCreateCollection(String collectionName, String concern) throws com.integral.dbservice.mongo.MongoDBException {
        return getOrCreateCollection(collectionName, WriteConcern.valueOf(concern));
    }

    public DB getDatBase() {
        return database;
    }


}
