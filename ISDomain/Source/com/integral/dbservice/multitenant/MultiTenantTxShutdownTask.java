package com.integral.dbservice.multitenant;

import com.integral.dbservice.DBService;
import com.integral.dbservice.is.ISPersistenceService;
import com.integral.dbservice.remote.MultiTenantPersistenceService;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.runtime.ShutdownTask;

import java.util.Hashtable;

/**
 * User: pulkit
 * This class does cleanup for multitenant tx server. Will block unless all the tenants are stopped
 */
public class MultiTenantTxShutdownTask implements ShutdownTask {

    private static Log log = LogFactory.getLog(MultiTenantTxShutdownTask.class);


    @Override
    public String shutdown(String aName, Hashtable args) {
        log.info("Shutting down MultiTenantTxServer.");
        MultiTenantPersistenceServiceC persistenceService = MultiTenantPersistenceServiceC.instance();
        if(persistenceService == null){
            log.warn( "DBService is not initialized for this server. Skipping DBService.shutdown() call." );
        }
        else{
            for (String tenantName : persistenceService.getConfiguredTenants()){
                log.info("Attempting to stop DBService for: " + tenantName);
                try {
                    DBService dbService = persistenceService.getDBService(tenantName);
                    if (dbService != null) {
                        dbService.stop();
                    }
                } catch (Exception ex) {
                    log.error("Error trying to stop dbservice for:" + tenantName, ex);
                }
                log.info("DBService stopped for: " + tenantName);
            }
        }
        return "MultiTenantTxServer has been shutdown.";
    }
}
