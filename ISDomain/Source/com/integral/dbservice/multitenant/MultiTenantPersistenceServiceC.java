package com.integral.dbservice.multitenant;

import com.integral.dbservice.DBService;
import com.integral.dbservice.remote.MultiTenantRemoteTransactionServer;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import com.integral.system.server.VirtualServer;
import com.integral.system.server.VirtualServerC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Jan 14, 2011
 * Time: 4:07:53 PM
 * persistence Service for Multi Tenant Transaction Server related operations.
 */
public class MultiTenantPersistenceServiceC implements com.integral.dbservice.remote.MultiTenantPersistenceService {

    protected static Log log = LogFactory.getLog(MultiTenantPersistenceServiceC.class);

    private static MultiTenantPersistenceServiceC instance = null;

    protected final MultiTenantRemoteTransactionServer txnServer;

    protected final ISPersistenceMBean config;

    private final List<String> configuredTenants;

    private final DBService dbService;

    /**
     * Even though this is a public method, this should be only set by the startup task once for the lifetime of the system.
     *
     * @param globalInstance
     */
    public static void __private_startup_setInstance(MultiTenantPersistenceServiceC globalInstance) {
        instance = globalInstance;
    }

    public MultiTenantPersistenceServiceC(ISPersistenceMBean config, MultiTenantRemoteTransactionServer txnServer, DBService dbService) {
        this.config = config;
        this.txnServer = txnServer;
        this.dbService = dbService;
        List tenants = queryTenantsForCurrentTxnServer();
        configuredTenants = new ArrayList<String>(tenants.size());
        for (Object tenant : tenants) {
            configuredTenants.add(((VirtualServer) tenant).getShortName());
        }
        txnServer.setMultiTenantPersistenceService(this);
    }

    public static MultiTenantPersistenceServiceC instance() {
        return instance;
    }

    public synchronized void addTenant(String virtualServer) throws Exception {
        if (!txnServer.getTenants().contains(virtualServer)) { //Synchronized block here else, dbservice setup would be called multiple times.
            //DO we want to put a check here against Configured List of tenants. SO far TXn server doesn't care, it simply adds tenant if client sync to it.
            log.info("Setting up the DBService framework for tenant:" + virtualServer);
            DBService dbService = new DBService(config, virtualServer);

            EventProcessingPipelineSetupFunctor functor = new EventProcessingPipelineSetupFunctor(dbService);
            dbService.setEventPipelineSetupFunctor(functor);

            dbService.setup();
            txnServer.addTenant(dbService);
        }
    }

    public java.util.List queryTenantsForCurrentTxnServer() {
        return queryTenantsForTxnServer(ISFactory.getInstance().getISMBean().getVirtualServer());
    }

    /**
     * Query tenants for the given txn server.
     *
     * @param txnServer Virtual Server.
     * @return list of Virtual Servers assigned the given txn server.
     */
    public java.util.List queryTenantsForTxnServer(VirtualServer txnServer) {
        java.util.List<VirtualServer> result = Collections.emptyList();
        try {
            log.warn("Querying VirtualServer objects from DB for txn server " + txnServer.getShortName());
            ReadAllQuery query = new ReadAllQuery();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression txnServerExpr = eb.get("remoteTxnServer").equal(txnServer);

            query.setReferenceClass(VirtualServerC.class);
            query.setSelectionCriteria(txnServerExpr);
            query.useCollectionClass(ArrayList.class);
            Session session = PersistenceFactory.newSession();

            result = (List) session.executeQuery(query);

        } catch (PersistenceException e) {
            log.error("Unable to query VSs for Txn Server:" + txnServer.getShortName(), e);
        }
        return result;
    }

    public List<String> getConfiguredTenants() {
        return configuredTenants;
    }

    public void removeTenant(String virtualServer) throws Exception {
        txnServer.removeTenant(virtualServer);
    }

    public DBService getDBService(String tenantName){
        return txnServer.getDBServiceForTenant(tenantName);
    }
}
