package com.integral.dbservice.multitenant;

import com.integral.dbservice.DBService;
import com.integral.dbservice.is.MockEventProcessingPipelineSetupFunctor;
import com.integral.dbservice.remote.MultiTenantRemoteTransactionServer;
import com.integral.is.common.mbean.ISPersistenceMBean;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 3/28/13
 * Time: 11:18 AM
 */
public class MockMultiTenantPersistenceServiceC extends MultiTenantPersistenceServiceC {

    public MockMultiTenantPersistenceServiceC(ISPersistenceMBean config, MultiTenantRemoteTransactionServer txnServer, DBService dbService) {
        super(config, txnServer, dbService);
    }

    public synchronized void addTenant(String virtualServer) throws Exception {
        if (!txnServer.getTenants().contains(virtualServer)) { //Synchronized block here else, dbservice setup would be called multiple times.
            log.info("Setting up the DBService framework for tenant:" + virtualServer);
            DBService dbService = new DBService(config, virtualServer);

            MockEventProcessingPipelineSetupFunctor functor = new MockEventProcessingPipelineSetupFunctor(dbService);
            dbService.setEventPipelineSetupFunctor(functor);

            dbService.setup();
            txnServer.addTenant(dbService);
        }
    }
}
