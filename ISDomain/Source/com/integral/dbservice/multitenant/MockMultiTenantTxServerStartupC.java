package com.integral.dbservice.multitenant;

import com.integral.dbservice.remote.MultiTenantRemoteTransactionServer;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Jan 28, 2011
 * Time: 2:00:09 PM
 */
public class MockMultiTenantTxServerStartupC extends MultiTenantTxServerStartupC {
    MultiTenantPersistenceServiceC multiTenantPersistenceService;

    @Override
    protected void initializePeristenceService() throws Exception {
        super.initializePeristenceService();
        ISPersistenceMBean mbean = ISFactory.getInstance().getPersistenceMBean();
        log.info("Mock MultiTenantTxServerStartupC. Setting up the DBService framework.");
        MultiTenantRemoteTransactionServer remoteTxServer = MultiTenantRemoteTransactionServer.getInstance();
        multiTenantPersistenceService = new MockMultiTenantPersistenceServiceC(mbean, remoteTxServer, dbService);
        //Set the singleton instance.
        MultiTenantPersistenceServiceC.__private_startup_setInstance(multiTenantPersistenceService);
    }
}