package com.integral.dbservice.multitenant;

import com.integral.is.common.mbean.ISPersistenceMBean;

import javax.management.ListenerNotFoundException;
import javax.management.MBeanNotificationInfo;
import javax.management.NotificationFilter;
import javax.management.NotificationListener;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Jan 28, 2011
 * Time: 3:14:12 PM
 * ISPersistenceMBean catering to MultiTenant Tx Server environment.
 */
public class MultiTenantTxServerPersistanceMBean implements ISPersistenceMBean
{
	private final ISPersistenceMBean mbean;

	public MultiTenantTxServerPersistanceMBean( ISPersistenceMBean mbean )
	{
		this.mbean = mbean;
	}

	public int getNumPersistenceThreads()
	{
		return mbean.getNumPersistenceThreads();
	}

	public int getCompactionLevel()
	{
		return mbean.getCompactionLevel();
	}

	public boolean isPrettyPrintingEnabled()
	{
		return mbean.isPrettyPrintingEnabled();
	}

	public long getTxLogRolloverIntervalInSeconds()
	{
		return mbean.getTxLogRolloverIntervalInSeconds();
	}

	public long getTxLogSyncIntervalInSeconds()
	{
		return mbean.getTxLogSyncIntervalInSeconds();
	}

	public long getMaxTxLogSyncWaitTimeInSeconds()
	{
		return mbean.getMaxTxLogSyncWaitTimeInSeconds();
	}

	public boolean isNonBlockingFileIOEnabled()
	{
		return mbean.isNonBlockingFileIOEnabled();
	}

	public boolean isTXLogFileReaderTaskEnabled()
	{
		return true; //required for replay
	}

	public boolean isTXLogFileWriterTaskEnabled()
	{
		return true; //required for replay
	}

	public boolean isTXQueueConsumerTaskEnabled()
	{
		return true; //required for processing logs off tenants and replay.
	}

	public String getLogRootDirPath()
	{
		return mbean.getLogRootDirPath();
	}

	public String getTXNServerUrl()
	{
		return mbean.getTXNServerUrl();
	}

	public long getRemoteTXNServerPingInterval()
	{
		return mbean.getRemoteTXNServerPingInterval();
	}

	public boolean isSpecialModeTXNServer()
	{
		return mbean.isSpecialModeTXNServer();
	}

	public int getPipelineQueueLength()
	{
		return mbean.getPipelineQueueLength();
	}

	public int getPipelineBlockedAlertDuration()
	{
		return mbean.getPipelineBlockedAlertDuration();
	}

	public boolean doSaveMultiTenantTxnServerMessages()
	{
		return false;
	}

	public String getMultiTenantTxnServerMessagesSaveRootDir()
	{
		return null;
	}

	public boolean isReplayModeEnabled()
	{
		return mbean.isReplayModeEnabled();
	}

	public double getReplayFactor()
	{
		return mbean.getReplayFactor();
	}

	public int getTransactionLogReaderBufferCapacity()
	{
		return mbean.getTransactionLogReaderBufferCapacity();
	}

	public boolean isMultiTenantRemoteTxnServerEnabled()
	{
		return mbean.isMultiTenantRemoteTxnServerEnabled();
	}

	public boolean isAutoSelectRemoteTxnServerEnabled()
	{
		return mbean.isAutoSelectRemoteTxnServerEnabled();
	}

    public int getSendBufferSize() {
        return mbean.getSendBufferSize();
    }

    public void addNotificationListener( NotificationListener listener, NotificationFilter filter, Object handback ) throws IllegalArgumentException
	{
		mbean.addNotificationListener(listener, filter, handback);
	}

	public void removeNotificationListener( NotificationListener listener ) throws ListenerNotFoundException
	{
		mbean.removeNotificationListener(listener);
	}

	public MBeanNotificationInfo[] getNotificationInfo()
	{
		return mbean.getNotificationInfo();
	}
}
