package com.integral.dbservice.multitenant;

import com.integral.dbservice.DBService;
import com.integral.dbservice.order.OAPersistenceServiceStartupC;
import com.integral.dbservice.remote.MultiTenantRemoteTransactionServer;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Sep 29, 2010
 * Time: 5:17:26 PM
 * Startup Class for Multi-tenant Transaction Server.
 */
public class MultiTenantTxServerStartupC extends OAPersistenceServiceStartupC
{
	MultiTenantPersistenceServiceC multiTenantPersistenceService;

	@Override
	protected void initializePeristenceService() throws Exception
	{
		ISPersistenceMBean mbean = ISFactory.getInstance().getPersistenceMBean();
		log.info("MultiTenantTxServerStartupC. Setting up the DBService framework.");
		MultiTenantRemoteTransactionServer remoteTxServer = MultiTenantRemoteTransactionServer.getInstance();
		remoteTxServer.setConfig(mbean);
		remoteTxServer.setup();

		super.initializePeristenceService();

		multiTenantPersistenceService = new MultiTenantPersistenceServiceC(mbean, remoteTxServer, dbService);
		//Set the singleton instance.
		MultiTenantPersistenceServiceC.__private_startup_setInstance(multiTenantPersistenceService);
	}

    @Override
    protected void createDBService() {
        //Setting up DB Service for the Txn Server itself. This would be used for replaying transactions.
        ISPersistenceMBean mbean = ISFactory.getInstance().getPersistenceMBean();
		ISPersistenceMBean txnMbean = new MultiTenantTxServerPersistanceMBean(mbean);
		dbService = new DBService(txnMbean);
    }
}
