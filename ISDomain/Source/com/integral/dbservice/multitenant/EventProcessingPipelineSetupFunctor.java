package com.integral.dbservice.multitenant;

import com.integral.dbservice.*;
import com.integral.dbservice.spaces.SpacesEndTransactionProcessor;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.pipeline.EventBasedProcessorDescriptor;
import com.integral.pipeline.EventProcessingPipelineSetupFunctorInf;
import com.integral.pipeline.Pipeline;
import com.integral.pipeline.ProcessorDescriptor;
import com.integral.pipeline.metrics.MetricsManager;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Jan 14, 2011
 * Time: 11:32:49 AM
 * Functor for setting up pipeline for event processing.
 */
public class EventProcessingPipelineSetupFunctor implements EventProcessingPipelineSetupFunctorInf
{

	private static Log log = LogFactory.getLog(EventProcessingPipelineSetupFunctor.class);
	private final DBService dbService;
	private EventBasedProcessorDescriptor eventHandler = new EventBasedProcessorDescriptor("EventHandler");

	public EventProcessingPipelineSetupFunctor( DBService dbService )
	{
		this.dbService = dbService;
		initRequestSerializers();

		EventProcessingPipelineSetupFunctorInf isSetupFunctor = new com.integral.dbservice.is.EventProcessingPipelineSetupFunctor(dbService);
		EventProcessingPipelineSetupFunctorInf brokerSetupFunctor = new com.integral.broker.dbservice.EventProcessingPipelineSetupFunctor(dbService);
		EventProcessingPipelineSetupFunctorInf orderSetupFunctor = new com.integral.dbservice.order.EventProcessingPipelineSetupFunctor(dbService);

		isSetupFunctor.setPipelineEventHandlers(eventHandler);
		brokerSetupFunctor.setPipelineEventHandlers(eventHandler);
		orderSetupFunctor.setPipelineEventHandlers(eventHandler);
		setPipelineEventHandlers(eventHandler);
	}

	public Pipeline createPipeline()
	{

		//Create all processors that will be part of this pipeline.
		ProcessorDescriptor deSerializeFromText = new ProcessorDescriptor("DeserializeFromText", new DeSerializeFromJsonProcessor(dbService.getConfig()));
		ProcessorDescriptor startTransaction = new ProcessorDescriptor("StartTransaction", new StartTransactionProcessor());

		ProcessorDescriptor endTransaction = new ProcessorDescriptor("EndTransaction", new EndTransactionProcessor(dbService.getTransactionLog()));

        ProcessorDescriptor spacesEndTransaction = new ProcessorDescriptor( "SpacesEndTransaction", new SpacesEndTransactionProcessor( dbService.getTransactionLog()));

        ProcessorDescriptor errorHandler = new ProcessorDescriptor("PersistToDBPipelineErrorHandler", new DBPersistPipelineErrorProcessor(dbService));

		//Setup the chain of processors in a pipeline.
		// start transaction -> handle event -> end transaction
		deSerializeFromText.setNext(startTransaction).setNext(eventHandler).setNext(endTransaction).setNext( spacesEndTransaction );

		ProcessorDescriptor startDesc;
		if ( dbService.getConfig().isReplayModeEnabled() )
		{
			ProcessorDescriptor replayProcessor = new ProcessorDescriptor("IntroduceDelay", new DBTxnLogReplayProcessor(dbService));
			replayProcessor.setNext(deSerializeFromText);
			log.warn("########## Setting up Transaction server in replay mode.");
			startDesc = replayProcessor;
		}
		else if ( dbService.getConfig().doSaveMultiTenantTxnServerMessages() )
		{
			ProcessorDescriptor saveMsgProcessor = null;
			try
			{
				saveMsgProcessor = new ProcessorDescriptor("SaveMessage", new DBPersistPipelineWriterProcessor(dbService));
			}
			catch ( Exception e )
			{
				throw new RuntimeException(e);
			}
			saveMsgProcessor.setNext(deSerializeFromText);
			log.warn("Multi Tenant Txn Server Write tenant msg to Txn Logs mode enabled.");
			startDesc = saveMsgProcessor;
		}
		else
		{
			startDesc = deSerializeFromText;
		}
		//Set the errorHandler - to ensure that we close the transaction even in case of errors.
		Pipeline p = new Pipeline("PersistToDBPipeline", startDesc, errorHandler, dbService.getConfig().getPipelineBlockedAlertDuration());
		MetricsManager.instance().register(p);
		return p;
	}

	public EventBasedProcessorDescriptor getEventHandler()
	{
		return eventHandler;
	}

	public void reclaim( ProcessorDescriptor pipeline )
	{

	}

	public void setPipelineEventHandlers( EventBasedProcessorDescriptor eventDescriptor )
	{
		//do nothing.
	}

	public void initRequestSerializers()
	{
		//Do Nothing
	}
}
