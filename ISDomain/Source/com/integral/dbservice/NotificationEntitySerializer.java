package com.integral.dbservice;

import com.integral.persistence.NotificationEntity;
import com.integral.serialize.BaseEntitySerializer;
import com.integral.serialize.Serializer;
import com.integral.serialize.SerializerMap;
import com.integral.serialize.StringSerializer;

public class NotificationEntitySerializer extends BaseEntitySerializer implements Serializer{
	
	private Class c = NotificationEntity.class;
	
	private StringSerializer SERIALIZED_NOTIF = new StringSerializer( new String[]{"serializedNotificationStr", "serNs"}, c, serializerMap);
			
	private StringSerializer QUEUE_NAME = new StringSerializer(new String[]{"queueName", "qName"}, c, serializerMap);
	
	private StringSerializer HANDLER_GROUP = new StringSerializer(new String[]{"handlerGroup", "hGr"}, c, serializerMap);
	
	public NotificationEntitySerializer(String[] attributeNames, Class<? extends Object> sourceClass, SerializerMap sourceSerializerMap){
		super(attributeNames, sourceSerializerMap);
		generateGetterAndSetter(sourceClass, c);
	}
	
	@Override
	protected Object createNewInstance(){
		return new NotificationEntity();
	}
	
}
