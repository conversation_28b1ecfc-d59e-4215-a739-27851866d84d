package com.integral.dbservice.order;

import com.integral.broker.dbservice.BrokerPersistenceService;
import com.integral.dbservice.DBService;
import com.integral.dbservice.PersistenceServiceStartupC;
import com.integral.dbservice.is.ISPersistenceService;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.log.LogLevel;
import com.integral.realmoney.persistence.dbservice.DBServiceRMPersistenceService;
import com.integral.realmoney.persistence.dbservice.PersistenceServiceFactory;
import com.integral.realmoney.persistence.dbservice.RMPersistenceService;
import com.integral.realmoney.persistence.dbservice.SpacesRMPersistenceService;
import com.integral.system.configuration.ConfigurationFactory;

import java.io.IOException;
import java.util.Hashtable;

public class OAPersistenceServiceStartupC extends PersistenceServiceStartupC
{
	ISPersistenceService isPersistenceService;
	BrokerPersistenceService baPersistenceService;
    RMPersistenceService rmPersistenceService;

	public OAPersistenceServiceStartupC()
	{
		super();
	}

	@Override
	protected void initializePeristenceService() throws Exception
	{
        createDBService();

		EventProcessingPipelineSetupFunctor functor = new EventProcessingPipelineSetupFunctor(dbService);
		dbService.setEventPipelineSetupFunctor(functor);

		dbService.setup();

		isPersistenceService = new ISPersistenceService(dbService);
		//Set the singleton instance.
		ISPersistenceService.__private_startup_setInstance(isPersistenceService);

		baPersistenceService = new BrokerPersistenceService(dbService);
		baPersistenceService.__private_startup_setInstance(baPersistenceService);

	}


    @Override
    public String startup( String aName, Hashtable htArgs )
    {
        super.startup(aName, htArgs);
        log.setLevel( LogLevel.INFO );
        try
        {
            if (ConfigurationFactory.getServerMBean().isIntegralSpacesEnabled()){
                log.info( "Initializing spaces persitence service." );
                rmPersistenceService = new SpacesRMPersistenceService();
                SpacesRMPersistenceService.__private_startup_setInstance((SpacesRMPersistenceService)rmPersistenceService);
                PersistenceServiceFactory.getPersistenceServiceFactory().setPersistenceService(rmPersistenceService);
            }else{
                rmPersistenceService = new DBServiceRMPersistenceService(dbService);
                DBServiceRMPersistenceService.__private_startup_setInstance((DBServiceRMPersistenceService)rmPersistenceService);
                PersistenceServiceFactory.getPersistenceServiceFactory().setPersistenceService(rmPersistenceService);
            }
        }
        catch ( Exception e )
        {
            log.error( "Failed to initialized OAPersistenceServiceStartupC due to: " + e.getMessage(), e );
            throw new RuntimeException( "Exception during PersistenceService initialization.", e );
        }

        return null;
    }



    protected void createDBService() {
        ISPersistenceMBean mbean = ISFactory.getInstance().getPersistenceMBean();

        dbService = new DBService(mbean);
    }

}
