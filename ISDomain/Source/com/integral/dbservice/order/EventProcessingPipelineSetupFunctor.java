package com.integral.dbservice.order;

import com.integral.dbservice.*;
import com.integral.dbservice.is.ISPersistenceServiceCacheManager;
import com.integral.dbservice.is.RequestSerializerFactory;
import com.integral.dbservice.spaces.SpacesEndTransactionProcessor;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.pipeline.*;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.realmoney.persistence.dbservice.PersistenceHandlers;
import com.integral.serialize.EntityHolder;
import com.integral.serialize.RequestSerializer;
import com.integral.serialize.SerializerMap;

public class EventProcessingPipelineSetupFunctor implements EventProcessingPipelineSetupFunctorInf
{

	DBService dbService;
	private Log log = LogFactory.getLog(EventProcessingPipelineSetupFunctor.class);
	private EventBasedProcessorDescriptor eventHandler = new EventBasedProcessorDescriptor("EventHandler");

	public EventProcessingPipelineSetupFunctor( DBService dbService )
	{
		this.dbService = dbService;
		initRequestSerializers();

		com.integral.dbservice.is.EventProcessingPipelineSetupFunctor isSetupFunctor = new com.integral.dbservice.is.EventProcessingPipelineSetupFunctor(dbService);
		com.integral.broker.dbservice.EventProcessingPipelineSetupFunctor brokerSetupFunctor = new com.integral.broker.dbservice.EventProcessingPipelineSetupFunctor(dbService);
		isSetupFunctor.setPipelineEventHandlers(eventHandler);
		brokerSetupFunctor.setPipelineEventHandlers(eventHandler);
		setPipelineEventHandlers(eventHandler);
	}

	public Pipeline createPipeline()
	{

		//Create all processors that will be part of this pipeline.
		ProcessorDescriptor deSerializeFromText = new ProcessorDescriptor("DeserializeFromText", new DeSerializeFromJsonProcessor(dbService.getConfig()));
		ProcessorDescriptor startTransaction = new ProcessorDescriptor("StartTransaction", new StartTransactionProcessor());

		ProcessorDescriptor endTransaction = new ProcessorDescriptor("EndTransaction", new EndTransactionProcessor(dbService.getTransactionLog()));

        ProcessorDescriptor spacesEndTransaction = new ProcessorDescriptor( "SpacesEndTransaction", new SpacesEndTransactionProcessor( dbService.getTransactionLog()));

        ProcessorDescriptor errorHandler = new ProcessorDescriptor("PersistToDBPipelineErrorHandler", new DBPersistPipelineErrorProcessor(dbService));

		//special mode processor --- This processor should be registered only for QA testing
		ISPersistenceMBean mbean = dbService.getConfig();
		if ( mbean.isSpecialModeTXNServer() )
		{
			log.warn("########### Setting up Special Mode Transaction Server Processor.");
			ProcessorDescriptor specialModeProcessor = new ProcessorDescriptor("specialModeProcessor", new SpecialModeTXNServerProcessor(dbService.getTransactionLog()));

			//Setup the chain of processors in a pipeline.
			// start transaction -> handle event -> end transaction --> SpecialModeProcessor
			deSerializeFromText.setNext(startTransaction).setNext(eventHandler).setNext(endTransaction).setNext(specialModeProcessor);
		}
		else
		{
			deSerializeFromText.setNext(startTransaction).setNext(eventHandler).setNext(endTransaction).setNext( spacesEndTransaction );
		}

		ProcessorDescriptor startDesc;
		if ( dbService.getConfig().isReplayModeEnabled() )
		{
			ProcessorDescriptor replayProcessor = new ProcessorDescriptor("IntroduceDelay", new DBTxnLogReplayProcessor(dbService));
			replayProcessor.setNext(deSerializeFromText);
			log.warn("########## Setting up Transaction server in replay mode.");
			startDesc = replayProcessor;
		}
		else
		{
			startDesc = deSerializeFromText;
		}

		//Set the errorHandler - to ensure that we close the transaction even in case of errors.
		Pipeline p = new Pipeline("PersistToDBPipeline", startDesc, errorHandler, dbService.getConfig().getPipelineBlockedAlertDuration());

		MetricsManager.instance().register(p);
		return p;
	}

	/**
	 * Sets up OA transaction specific pipeline
	 */
	public void setPipelineEventHandlers( EventBasedProcessorDescriptor eventHandler )
	{
		ISPersistenceServiceCacheManager cache = ISPersistenceServiceCacheManager.instance(dbService.getPeerVirtualServer());
		//OMS is pre-req for real money service, as of registration of event handler will be done here.
		//TODO - consider creating RMM specific EventProcessingPipelineSetupFunctor
		addPortfolioEventHandlers(eventHandler);
	}

	public void initRequestSerializers()
	{
	}

	/**
	 * @param eventHandler2
	 */
	private void addPortfolioEventHandlers( EventBasedProcessorDescriptor eventHandler )
	{
		ISPersistenceServiceCacheManager cache = ISPersistenceServiceCacheManager.instance(dbService.getPeerVirtualServer());
		eventHandler.setEventHandler(PersistenceHandlers.StorePortfolioEventHandler.EVENT, new PersistenceHandlers.StorePortfolioEventHandler(cache));
		eventHandler.setEventHandler(PersistenceHandlers.PortfolioStateUpdateEventHandler.EVENT, new PersistenceHandlers.PortfolioStateUpdateEventHandler(cache));
		eventHandler.setEventHandler(PersistenceHandlers.PortfolioCompleteEventHandler.EVENT, new PersistenceHandlers.PortfolioCompleteEventHandler(cache));
		eventHandler.setEventHandler(PersistenceHandlers.PortfolioUTIUpdateEventHandler.EVENT, new PersistenceHandlers.PortfolioUTIUpdateEventHandler(cache));
		eventHandler.setEventHandler(PersistenceHandlers.StorePortfolioAcceptanceRequestEventHandler.EVENT, new PersistenceHandlers.StorePortfolioAcceptanceRequestEventHandler(cache));
	}

	public EventBasedProcessorDescriptor getEventHandler()
	{
		return eventHandler;
	}

	public void reclaim( ProcessorDescriptor pipeline )
	{

	}

}
