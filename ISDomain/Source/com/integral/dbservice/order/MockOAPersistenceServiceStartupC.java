package com.integral.dbservice.order;

import com.integral.broker.dbservice.BrokerPersistenceService;
import com.integral.dbservice.DBService;
import com.integral.dbservice.PersistenceServiceStartupC;
import com.integral.dbservice.is.ISPersistenceService;
import com.integral.dbservice.is.MockEventProcessingPipelineSetupFunctor;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Jan 19, 2011
 * Time: 10:34:41 PM
 * Mock startup to test pipeline error handler.
 */
public class MockOAPersistenceServiceStartupC extends PersistenceServiceStartupC
{
	ISPersistenceService isPersistenceService;
	BrokerPersistenceService baPersistenceService;

	public MockOAPersistenceServiceStartupC()
	{
		super();
	}

	@Override
	protected void initializePeristenceService() throws Exception
	{
		ISPersistenceMBean mbean = ISFactory.getInstance().getPersistenceMBean();

		dbService = new DBService(mbean);

		MockEventProcessingPipelineSetupFunctor functor = new MockEventProcessingPipelineSetupFunctor(dbService);
		dbService.setEventPipelineSetupFunctor(functor);

		dbService.setup();

		isPersistenceService = new ISPersistenceService(dbService);
		//Set the singleton instance.
		ISPersistenceService.__private_startup_setInstance(isPersistenceService);

		baPersistenceService = new BrokerPersistenceService(dbService);
		baPersistenceService.__private_startup_setInstance(baPersistenceService);

	}

}
