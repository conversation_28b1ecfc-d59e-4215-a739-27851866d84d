package com.integral.dbservice;

import com.integral.dbservice.is.NettingPortfolioSerializerFactory;
import com.integral.dbservice.is.RequestSerializerFactory;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.serialize.CreditLimitWorkflowStateSerializer;
import com.integral.serialize.EntityHolder;
import com.integral.serialize.FXCreditUtilzationSerializer;
import com.integral.serialize.SerializerMap;
import org.codehaus.jackson.JsonParser;


public class DeSerializeFromJsonProcessor extends AbstractDeSerializeFromJsonProcessor
{

    private static final TradeSerializerFactory tradeSerializerFactory = TradeSerializerFactory.getInstance();
    private static final RequestSerializerFactory requestSerializerFactory = RequestSerializerFactory.instance();

    private final FXCreditUtilzationSerializer fxCreditUtilizatonSerializer = new FXCreditUtilzationSerializer(
			new String[] { "CreditUtilizationEvent", "CU" }, EntityHolder.class, new SerializerMap() );
    private final CreditLimitWorkflowStateSerializer creditLimitWfStSerializer = new CreditLimitWorkflowStateSerializer(
    		new String[] {"CreditLimitWorkflowState","clws"}, EntityHolder.class, new SerializerMap() );
    private final NettingPortfolioSerializerFactory nettingPortfolioSerializerFactory = NettingPortfolioSerializerFactory.instance();
    
    private final NotificationEntitySerializer notificationEntitySerializer = new NotificationEntitySerializer(new String[] {"NotificationEntity","ne"}, EntityHolder.class, new SerializerMap() );

	protected final EntityHolder wrapper = new EntityHolder();

    public DeSerializeFromJsonProcessor() {
        this(ISFactory.getInstance().getPersistenceMBean());
    }

    public DeSerializeFromJsonProcessor(ISPersistenceMBean mbean) {
        super(mbean);
    }

	protected Object processMsg( PipelineMessage msg, JsonParser parser, String objectName ) throws Exception
	{
		Object clonedEntity;
		if ( "Request".equals( objectName ) || "R".equals( objectName ) )
		{
            requestSerializerFactory.getSerializer( msg.getEventName() ).deserialize( parser, wrapper, mbean.getCompactionLevel() );
			clonedEntity = wrapper.getRequest();
		} 
		else if ( "Trade".equals( objectName ) || "T".equals( objectName ) )
		{
            tradeSerializerFactory.getTradeSerializer( msg.getEventName() ).deserialize( parser,wrapper,mbean.getCompactionLevel() );
			clonedEntity = wrapper.getTrade();
		} 
		else if ( "CU".equals( objectName ) || "CreditUtilizationEvent".equals( objectName) )
		{
			fxCreditUtilizatonSerializer.deserialize( parser, wrapper, mbean.getCompactionLevel() );
			clonedEntity = wrapper.getCreditUtilizationEvent();
		} 
		else if ( "clws".equals( objectName ) || "CreditLimitWorkflowState".equals( objectName ))
		{
			creditLimitWfStSerializer.deserialize( parser, wrapper, mbean.getCompactionLevel() );
			clonedEntity = wrapper.getCreditLimitWorkflowState();
		}
        else if ( "nP".equals( objectName ) || "NettingPortfolio".equals( objectName ))
        {
            nettingPortfolioSerializerFactory.getSerializer( msg.getEventName() ).deserialize( parser, wrapper, mbean.getCompactionLevel() );
            clonedEntity = wrapper.getNettingPortfolio();
        }
        else if("ne".equals(objectName) || "NotificationEntity".equals(objectName))
        {
        	notificationEntitySerializer.deserialize(parser, wrapper, mbean.getCompactionLevel());
        	clonedEntity = wrapper.getNotificationEntity();
        }
        else
		{
			throw new PipelineException( "No deserializer found for objectName: " + objectName );
		}
		return clonedEntity;
	}

}
