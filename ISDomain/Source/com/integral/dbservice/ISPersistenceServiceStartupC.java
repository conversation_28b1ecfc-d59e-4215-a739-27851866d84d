package com.integral.dbservice;

import java.io.IOException;
import java.util.Hashtable;
import java.util.concurrent.TimeUnit;
import com.integral.dbservice.DBService;
import com.integral.dbservice.is.EventProcessingPipelineSetupFunctor;
import com.integral.dbservice.is.ISPersistenceService;
import com.integral.dbservice.txlog.RolloverTask;
import com.integral.dbservice.txlog.TransactionLog.State;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.is.common.pool.ThreadPoolFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.system.runtime.StartupTask;

public class ISPersistenceServiceStartupC extends PersistenceServiceStartupC
{
    ISPersistenceService isPersistenceService;

    public ISPersistenceServiceStartupC()
    {
        super();
    }
    
    @Override
    protected void initializePeristenceService() throws Exception
    {
        ISPersistenceMBean mbean = ISFactory.getInstance().getPersistenceMBean();
        log.info( "Setting up the DBService framework." );
        dbService = new DBService( mbean );

        EventProcessingPipelineSetupFunctor functor = new EventProcessingPipelineSetupFunctor( dbService );
        dbService.setEventPipelineSetupFunctor( functor );

        dbService.setup();

        log.info( "Initializing and Starting Persistence Service." );
        isPersistenceService = new ISPersistenceService( dbService );
        //Set the singleton instance.
        ISPersistenceService.__private_startup_setInstance( isPersistenceService );

    }

}
