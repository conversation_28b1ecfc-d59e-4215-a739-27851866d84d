package com.integral.dbservice.notifications;

import com.integral.dbservice.DBPersistNotificationPipelineProcessor;
import com.integral.dbservice.DBService;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.NotificationEntity;

public class NotificationPersistenceService {

	private static Log log = LogFactory
			.getLog(NotificationPersistenceService.class);

	private DBService dbService = null;

	private static NotificationPersistenceService instance = null;

	public static void __private_startup_setInstance(
			NotificationPersistenceService globalInstance) {
		instance = globalInstance;
	}

	public NotificationPersistenceService(DBService dbService) {
		this.dbService = dbService;
	}

	public static NotificationPersistenceService instance() {
		return instance;
	}

	public void writeNotification(NotificationEntity notificationEntity) {
		try {

			if (log.isDebugEnabled()) {
				log.debug("saving the notification received from Queue:"
						+ notificationEntity.getQueueName() + ":handler group:"
						+ notificationEntity.getHandlerGroup()
						+ ":notification:"
						+ notificationEntity.getSerializedNotificationStr());
			}
			dbService.processEvent(notificationEntity.getNotification()
					.getCorrelationId(),
					DBPersistNotificationPipelineProcessor.EVENT,
					notificationEntity, null, false);
		} catch (Exception e) {
			log.error(
					"ISPersistenceService.writeNotification : Error in writing  notification:"
							+ notificationEntity.getNotification(), e);
		}
	}
}
