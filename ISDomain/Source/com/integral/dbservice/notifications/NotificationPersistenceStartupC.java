package com.integral.dbservice.notifications;

import java.util.Hashtable;

import com.integral.dbservice.DBService;
import com.integral.dbservice.PersistenceServiceStartupC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.is.common.mbean.ISPersistenceMBeanC;
import com.integral.log.LogLevel;
import com.integral.pipeline.EventProcessingPipelineSetupFunctorInf;
import com.integral.spaces.notification.NotificationConfiguration;

public class NotificationPersistenceStartupC extends PersistenceServiceStartupC {

	NotificationPersistenceService notificationPersistenceService;

	public NotificationPersistenceStartupC() {
		super();
	}

	@Override
	public String startup(String aName, Hashtable htArgs) {
		log.setLevel(LogLevel.INFO);
		try {

			log.info("Initializing NotificationPersistence DB Service with persistence threads="
					+ NotificationConfiguration.getInstance()
							.getNumberOfQueueSets());
			
			// disable remote Transaction server explicitly and set the number of persistence threads to be
			// same as the number of queue sets
			ISPersistenceMBeanC mbean = (ISPersistenceMBeanC)ISFactory.getInstance()
					.getPersistenceMBean();
			mbean.setMultiTenantRemoteTxnServerEnabled(false);
			mbean.setNumPersistenceThreads(NotificationConfiguration.getInstance().getNumberOfQueueSets());
						
			initializePeristenceService();
			postInitializePropcessing();
		} catch (Exception e) {
			log.error("Failed to initialized NotificationPersistence due to: "
					+ e.getMessage(), e);
			throw new RuntimeException(
					"Exception during NotificationPersistence initialization.",
					e);
		}
		return null;
	}

	@Override
	protected void initializePeristenceService() throws Exception {

		createDBService();
		EventProcessingPipelineSetupFunctorInf functor = new NotificationEventProcessingPipelineSetupFunctor(
				dbService);
		dbService.setEventPipelineSetupFunctor(functor);

		dbService.setup();

		notificationPersistenceService = new NotificationPersistenceService(
				dbService);

		// set the singleton instance
		NotificationPersistenceService
				.__private_startup_setInstance(notificationPersistenceService);
	}
	
	protected void createDBService() {
		ISPersistenceMBean mbean = ISFactory.getInstance()
				.getPersistenceMBean();

		dbService = new DBService(mbean);
	}

}
