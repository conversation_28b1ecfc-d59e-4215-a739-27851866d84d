package com.integral.dbservice.notifications;

import com.integral.dbservice.DBPersistNotificationPipelineProcessor;
import com.integral.dbservice.DBPersistPipelineErrorProcessor;
import com.integral.dbservice.DBService;
import com.integral.dbservice.DeSerializeFromJsonProcessor;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.pipeline.EventBasedProcessorDescriptor;
import com.integral.pipeline.EventProcessingPipelineSetupFunctorInf;
import com.integral.pipeline.Pipeline;
import com.integral.pipeline.ProcessorDescriptor;
import com.integral.pipeline.metrics.MetricsManager;

public class NotificationEventProcessingPipelineSetupFunctor implements
		EventProcessingPipelineSetupFunctorInf {

	DBService dbService;
	private Log log = LogFactory
			.getLog(NotificationEventProcessingPipelineSetupFunctor.class);

	public NotificationEventProcessingPipelineSetupFunctor(DBService dbService) {
		this.dbService = dbService;
	}

	@Override
	public Pipeline createPipeline() {
		// Create all processors that will be part of this pipeline.
		log.info("Setting up Pipeline for file backed notifications");

		ProcessorDescriptor deSerializeFromText = new ProcessorDescriptor(
				"DeserializeFromText", new DeSerializeFromJsonProcessor(
						dbService.getConfig()));

		ProcessorDescriptor dbPersistNotificationProcessor = new ProcessorDescriptor(
				DBPersistNotificationPipelineProcessor.EVENT,
				new DBPersistNotificationPipelineProcessor(dbService));

		ProcessorDescriptor errorHandler = new ProcessorDescriptor(
				"PersistToDBPipelineErrorHandler",
				new DBPersistPipelineErrorProcessor(dbService));

		deSerializeFromText.setNext(dbPersistNotificationProcessor);

		ProcessorDescriptor startDesc = deSerializeFromText;

		Pipeline p = new Pipeline("PersistToDBPipeline", startDesc, errorHandler,
				dbService.getConfig().getPipelineBlockedAlertDuration());
		MetricsManager.instance().register(p);
		return p;
	}

	@Override
	public void reclaim(ProcessorDescriptor pipeline) {
		// TODO Auto-generated method stub

	}

	@Override
	public void setPipelineEventHandlers(
			EventBasedProcessorDescriptor eventDescriptor) {
		// TODO Auto-generated method stub

	}

	@Override
	public void initRequestSerializers() {
		// TODO Auto-generated method stub

	}

	@Override
	public EventBasedProcessorDescriptor getEventHandler() {
		return null;
	}
}
