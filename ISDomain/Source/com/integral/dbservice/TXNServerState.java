package com.integral.dbservice;

import java.util.Map;
import com.integral.dbservice.txlog.TransactionLog.State;

//maintains various states of transaction server modules
public interface TXNServerState
{
    public State getTxLogState();

    public void setTxLogState( State state );

    public Map<Integer, State> getReaderTaskStates();

    public void setReaderTaskState( int i, State state );

    public State getReaderTaskState( int i );
    
    public State getReaderTaskState();

}
