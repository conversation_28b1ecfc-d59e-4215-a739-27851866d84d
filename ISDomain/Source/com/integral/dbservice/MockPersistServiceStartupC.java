package com.integral.dbservice;

import com.integral.dbservice.is.EventProcessingPipelineSetupFunctor;
import com.integral.dbservice.is.ISPersistenceService;
import com.integral.dbservice.is.MockEventProcessingPipelineSetupFunctor;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Nov 8, 2010
 * Time: 11:46:36 AM
 * As startup class that initializes the DBPerisist Pipeline with a task that throws exception.
 * The scenario is used to test the service in case of pipeline failures.
 */
public class MockPersistServiceStartupC extends PersistenceServiceStartupC
{
    ISPersistenceService isPersistenceService;

    public MockPersistServiceStartupC()
    {
        super();
    }

    @Override
    protected void initializePeristenceService() throws Exception
    {
        ISPersistenceMBean mbean = ISFactory.getInstance().getPersistenceMBean();
        log.info( "Setting up the DBService framework." );
        dbService = new DBService( mbean );

        MockEventProcessingPipelineSetupFunctor functor = new MockEventProcessingPipelineSetupFunctor( dbService );
        dbService.setEventPipelineSetupFunctor( functor );

        dbService.setup();

        log.info( "Initializing and Starting Persistence Service." );
        isPersistenceService = new ISPersistenceService( dbService );
        //Set the singleton instance.
        ISPersistenceService.__private_startup_setInstance( isPersistenceService );

    }

}