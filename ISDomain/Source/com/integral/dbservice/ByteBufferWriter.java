package com.integral.dbservice;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.dbservice.txlog.ByteBufferFactory;
import com.integral.pool.ArrayListObjectPool;
import com.integral.util.Tuple;

import java.io.IOException;
import java.io.Writer;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.nio.charset.CharsetEncoder;
import java.nio.charset.CoderResult;
import java.util.ArrayList;

/**
 *
 * A wrapper around byte buffer for writing character data using the {@link Writer} interface.
 * This implementation can be used in scenarios when you are trying to integrate components that deal with Writer instances
 * and NIO components - like {@link java.nio.channels.Channel channels} and such.
 *
 * <AUTHOR>
 */
public class ByteBufferWriter extends Writer {
	private static Log log = LogFactory.getLog(ByteBufferWriter.class);

    private int bufferSize = 16*1024;

    //Factory for creating backing byte buffers.
    final ByteBufferFactory byteBufferFactory;
    final ArrayListObjectPool<ByteBuffer> backingByteBufferPool;
    final ArrayList<Tuple<ByteBuffer, CharBuffer>> borrowedBuffers = new ArrayList<Tuple<ByteBuffer, CharBuffer>>(2);

    //The current character buffer.
    CharBuffer currCharBuffer = null;

    //Factory for creating encoded byte buffers.
    final ByteBufferFactory encodedBufferFactory;
    final ArrayListObjectPool<ByteBuffer> encodedByteBufferPool;
    final ArrayList<ByteBuffer> borrowedEncodedBuffers = new ArrayList<ByteBuffer>(2);

    ByteBuffer currEncodedBuffer = null;

    final CharsetEncoder encoder = Charset.forName("UTF-8").newEncoder(); //Fix for Bug:48158 UTF-8 is used while Reading txl files by TransactionLogParser class.

    int charsWritten = 0;

    public ByteBufferWriter(int bufferSize) {
        this(bufferSize,true);
    }

    public ByteBufferWriter(int bufferSize, boolean isDirect) {
        this.bufferSize = bufferSize;

        byteBufferFactory = new ByteBufferFactory(bufferSize, isDirect);
        backingByteBufferPool = new ArrayListObjectPool<ByteBuffer>(byteBufferFactory, 1);
        encodedBufferFactory = new ByteBufferFactory(bufferSize, isDirect);
        encodedByteBufferPool = new ArrayListObjectPool<ByteBuffer>(encodedBufferFactory, 1);

        allocateBackingByteBuffer();
        allocateEncodedByteBuffer();
    }

    private void allocateEncodedByteBuffer() {
        if (currEncodedBuffer != null) {
            currEncodedBuffer.flip();
        }
        ByteBuffer bb = encodedByteBufferPool.borrowObject();
        borrowedEncodedBuffers.add(bb);
        currEncodedBuffer = bb;
    }

    private void allocateBackingByteBuffer() {
        ByteBuffer bb = backingByteBufferPool.borrowObject();
        currCharBuffer = bb.asCharBuffer();
        borrowedBuffers.add(new Tuple<ByteBuffer, CharBuffer>(bb, currCharBuffer));
    }

    @Override
    public void write(char[] cbuf, int off, int len) throws IOException {
        charsWritten += len;
        //this is the most likely case, so let's just deal with it.
        //some code redundancy, but removes a few checks.
        if (currCharBuffer.remaining() >= len) {
            currCharBuffer.put(cbuf, off, len);
            return;
        }

        int currOff = off;
        int currLen = len;

        while (true) {
            if (currCharBuffer.remaining() >= currLen) {
                currCharBuffer.put(cbuf, currOff, currLen);
                break;
            }
            else {
                int charsToWrite = currCharBuffer.remaining() - currLen;
                if (charsToWrite >= 0) {
                     currCharBuffer.put(cbuf, currOff, currLen);
                }
                else {
                    charsToWrite = currCharBuffer.remaining();
                    currCharBuffer.put(cbuf, currOff, charsToWrite);
                    currLen -= charsToWrite;
                    currOff += charsToWrite;
                    allocateBackingByteBuffer();
                }
            }
        }
    }

    @Override
    public void flush() throws IOException {
        //noop
    }

    @Override
    public void close() throws IOException {
        //noop
    }

    public ArrayList<ByteBuffer> getEncodedBuffers() {
        //Allocate one buffer that will hold all encoded data.
        if (currEncodedBuffer == null)
            allocateEncodedByteBuffer();
        
        encoder.reset();

        for (int i=0; i < borrowedBuffers.size(); i++) {
            currCharBuffer = borrowedBuffers.get(i).second;
            currCharBuffer.flip();
            while (currCharBuffer.hasRemaining()) {
                if (!currEncodedBuffer.hasRemaining()) {
                    allocateEncodedByteBuffer();
                }
                encoder.encode(currCharBuffer, currEncodedBuffer, false);
            }
        }

        //As per javadoc, I need to call the encode method one final time with  endOfInput=true
        CoderResult result = encoder.encode(currCharBuffer, currEncodedBuffer, true);
        //Ensure that the encoder was able to do its job right, and had enough space in the destination - ie no OVERFLOW
        while (result.isOverflow()) {
            allocateEncodedByteBuffer();
            result =  encoder.encode(currCharBuffer, currEncodedBuffer, true);
        }

        //The next step is to do a flush, and provide as many bytes as needed to perform a good flush.
        result = encoder.flush(currEncodedBuffer);
        while (result.isOverflow()) {
           allocateEncodedByteBuffer();
           result =  encoder.encode(currCharBuffer, currEncodedBuffer, true);
        }

        //allodateEncodedByteBuffer also does a flip() on the current buffer, before allocating a new one.
        //We need to flip() on just the currEncodedBuffer.
        currEncodedBuffer.flip();

        return borrowedEncodedBuffers;
    }

    public void reinitialize() {

        if (borrowedBuffers.size() == 1) {
            //this is an optimization for the most common case.
            currCharBuffer.clear();
        }
        else {
            for (int i=0; i < borrowedBuffers.size(); i++) {
                backingByteBufferPool.returnObject(borrowedBuffers.get(i).first);
            }
            //clearing buffers list
            borrowedBuffers.clear();
            currCharBuffer = null;
            allocateBackingByteBuffer();
        }
        
        if (borrowedEncodedBuffers.size() == 1) {
            //this is an optimization for the most common case.
              currEncodedBuffer.clear();
        }
        else {
            for (int i=0; i < borrowedEncodedBuffers.size(); i++) {
                encodedByteBufferPool.returnObject(borrowedEncodedBuffers.get(i));
            }
			//clearing Buffers list
            borrowedEncodedBuffers.clear();
            currEncodedBuffer = null;
            allocateEncodedByteBuffer();
        }
        charsWritten = 0;
    }

    public int getEncodedBytesSize() {
        return ((borrowedEncodedBuffers.size() - 1) * bufferSize + currEncodedBuffer.remaining());
    }

    public int getWrittenCharSize() {
        return charsWritten;
    }
}

