package com.integral.dbservice;

import java.io.StringReader;

import com.integral.persistence.Entity;
import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import com.integral.xml.XMLConvertor;
import com.integral.xml.XMLConvertorFactory;
import com.integral.xml.mapping.XMLException;

public class DeSerializeFromTextProcessor extends Processor {
	
	private static String MAPPING_NAME = "IntegrationServer";
	
	XMLConvertor xmlConvertor = XMLConvertorFactory.getXMLConvertor();
	
	@Override
	public PipelineMessage process(PipelineMessage request) {
		// TODO Auto-generated method stub
		TransactionLogMessage tlm = (TransactionLogMessage)request;
		
		try {
			tlm.entity = (Entity) xmlConvertor.convertFromXML(new StringReader(tlm.entityText.toString()), MAPPING_NAME);
		} catch (XMLException e) {
			StringBuilder builder = new StringBuilder();
			builder.append("Unable to convert from XML -> Entity due to: ").append(e.getMessage());
			throw new PipelineException(builder.toString(), e);
		}
		return request;
	}

}
