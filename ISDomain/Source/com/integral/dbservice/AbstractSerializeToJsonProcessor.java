package com.integral.dbservice;

import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Entity;
import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import com.integral.serialize.EntityHolder;
import org.codehaus.jackson.JsonFactory;
import org.codehaus.jackson.JsonGenerator;

import java.io.Writer;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.integral.serialize.SerializerConstants.NULL_STR;
import static com.integral.serialize.SerializerConstants.PROPERTY_MAP_TOKEN;


public abstract class AbstractSerializeToJsonProcessor extends Processor {

	private static Log log = LogFactory.getLog(AbstractSerializeToJsonProcessor.class);

    public static int DEFAULT_SIZE = 4*1024;
    private static JsonFactory factory = new JsonFactory();

    private static final char[] CRLF = new String("\r\n").toCharArray();
    protected final EntityHolder holder = new EntityHolder();
    protected final ISPersistenceMBean mbean;

    protected Writer wri = null;
    protected JsonGenerator g = null;

    public AbstractSerializeToJsonProcessor(Writer wri) {
        this(wri, ISFactory.getInstance().getPersistenceMBean());
    }

    public AbstractSerializeToJsonProcessor(Writer wri, ISPersistenceMBean mbean) {
        this.wri = wri;
        this.mbean = mbean;
    }

    @Override
    public PipelineMessage process(PipelineMessage msg) {
      try {
        TransactionLogMessage tlm = (TransactionLogMessage) msg;
        tlm.creationTime = System.currentTimeMillis();

        g = factory.createJsonGenerator(wri);
        if(mbean.isPrettyPrintingEnabled()){
            g.useDefaultPrettyPrinter();
        }
        //g.disableFeature(JsonGenerator.Feature.QUOTE_FIELD_NAMES);

        g.writeStartObject();
        
        if(tlm.entity instanceof Collection)
        {
        	List<Entity> entities = (List<Entity>) tlm.entity;
    		g.writeArrayFieldStart("entities");
        	for(Entity e : entities)
        	{
                g.writeStartObject();
        		processMsg(e, msg.getEventName());
                g.writeEndObject();
        	}
    		g.writeEndArray();
        }
        else
        {
        	processMsg( (Entity)tlm.entity, msg.getEventName() );
        }

        g.flush();

        //writes properties 
        processPropertyMap( tlm.properties );
        g.writeEndObject();
        
        
        g.flush();

        wri.write(CRLF);
        wri.flush();

        g.close();

      } catch (Exception e) {
        throw new PipelineException("Unable to serialize: " + msg.corelationId, e);
      }
      finally {
    	  //release references.
    	  holder.setRequest(null);
    	  holder.setTrade(null);
    	  holder.setCreditUtilizationEvent( null );
    	  holder.setEntities( null );
      }
      return msg;
    }
    
    protected abstract void processMsg( Entity e, String eventName ) throws Exception;

    
    private void processPropertyMap(Map<String,String> propertyMap) throws Exception{
        if(propertyMap != null && propertyMap.size() > 0){
            StringBuilder propertyStrBuilder = new StringBuilder(100);
            for(Map.Entry<String, String> entry : propertyMap.entrySet()){
                propertyStrBuilder.append( entry.getKey()).append('=');
                if(entry.getValue() == null){
                    propertyStrBuilder.append( NULL_STR ).append( ',' );
                }
                else{
                    propertyStrBuilder.append( entry.getValue() ).append( ',' );
                }
            }
            int len = propertyStrBuilder.length();
            if(len > 0 && propertyStrBuilder.charAt( len - 1) == ','){
                propertyStrBuilder.deleteCharAt( len - 1 );
            }
            
            g.writeStringField(PROPERTY_MAP_TOKEN, propertyStrBuilder.toString() );
        }
    }
}

