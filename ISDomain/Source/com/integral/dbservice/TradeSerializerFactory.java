package com.integral.dbservice;

import com.integral.dbservice.is.CreditEventHandler;
import com.integral.dbservice.is.RFSPrimeBrokerHandlers;
import com.integral.dbservice.is.RFSTradeHandlers;
import com.integral.serialize.EntityHolder;
import com.integral.serialize.FXTradeSerializer;
import com.integral.serialize.SerializerMap;
import com.integral.serialize.TradeSerializer;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 * User: verma
 * Date: May 16, 2010
 * Time: 2:07:58 PM
 * To change this template use File | Settings | File Templates.
 */
public class TradeSerializerFactory
{
    private static TradeSerializerFactory instance;
    private final Map<String, FXTradeSerializer> tradeSerializerMap;

    static
    {
        instance = new TradeSerializerFactory();
    }

    public static TradeSerializerFactory getInstance()
    {
        return instance;
    }

    private TradeSerializerFactory()
    {
        tradeSerializerMap = new HashMap<String, FXTradeSerializer>();
        FXTradeSerializer creditEventTradeSerializer = new FXTradeSerializer( new String[]{"Trade", "T"},
                EntityHolder.class, new SerializerMap(), TradeSerializer.TradeSerializationType.ForCredit );
        tradeSerializerMap.put( CreditEventHandler.UndoCreditEventHandler.EVENT, creditEventTradeSerializer );
        tradeSerializerMap.put( CreditEventHandler.AmendEventHandler.EVENT, creditEventTradeSerializer );
        tradeSerializerMap.put( CreditEventHandler.UpdateMultiFillCreditEventHandler.EVENT, creditEventTradeSerializer );
        tradeSerializerMap.put( "DEFAULT", new FXTradeSerializer( new String[]{"Trade", "T"}, EntityHolder.class, new SerializerMap(), TradeSerializer.TradeSerializationType.Default ) );

        tradeSerializerMap.put( RFSTradeHandlers.TradeVerificationEventHandler.EVENT, new FXTradeSerializer( new String[]{"Trade", "T"}, EntityHolder.class, new SerializerMap(), TradeSerializer.TradeSerializationType.RFSTradeVerification ) );
        tradeSerializerMap.put( RFSTradeHandlers.TradeRejectionEventHandler.EVENT, new FXTradeSerializer( new String[]{"Trade", "T"}, EntityHolder.class, new SerializerMap(), TradeSerializer.TradeSerializationType.RFSTradeRejection ) );
        tradeSerializerMap.put( RFSTradeHandlers.TradeConfirmationEventHandler.EVENT, new FXTradeSerializer( new String[]{"Trade", "T"}, EntityHolder.class, new SerializerMap(), TradeSerializer.TradeSerializationType.RFSTradeConfirmation ) );
        tradeSerializerMap.put( RFSTradeHandlers.TradeCancelEventHandler.EVENT, new FXTradeSerializer( new String[]{"Trade", "T"}, EntityHolder.class, new SerializerMap(), TradeSerializer.TradeSerializationType.RFSTradeCancel ) );
        tradeSerializerMap.put( RFSTradeHandlers.TradeAmendEventHandler.EVENT, new FXTradeSerializer( new String[]{"Trade", "T"}, EntityHolder.class, new SerializerMap(), TradeSerializer.TradeSerializationType.RFSTradeAmend ) );
        tradeSerializerMap.put( RFSTradeHandlers.TradeMiFiDAmendEventHandler.EVENT, new FXTradeSerializer( new String[]{"Trade", "T"}, EntityHolder.class, new SerializerMap(), TradeSerializer.TradeSerializationType.RFSTradeMiFidAmend));
        tradeSerializerMap.put( RFSTradeHandlers.STPTradeStatusUpdateEventHandler.EVENT, new FXTradeSerializer( new String[]{"Trade", "T"}, EntityHolder.class, new SerializerMap(), TradeSerializer.TradeSerializationType.RFSSTPTradeStatusUpdate ) );
        tradeSerializerMap.put( RFSTradeHandlers.STPTradeStatusUpdateToGMEventHandler.EVENT, new FXTradeSerializer( new String[]{"Trade", "T"}, EntityHolder.class, new SerializerMap(), TradeSerializer.TradeSerializationType.RFSSTPTradeStatusUpdate ) );
        
        tradeSerializerMap.put( RFSPrimeBrokerHandlers.RFSPrimeBrokerMakerTradeVerificationEventHandler.EVENT, new FXTradeSerializer( new String[]{"Trade", "T"}, EntityHolder.class, new SerializerMap(), TradeSerializer.TradeSerializationType.RFSPrimeMakerTradeVerification ) );
        tradeSerializerMap.put( RFSPrimeBrokerHandlers.RFSPrimeBrokerCoverTradeVerificationEventHandler.EVENT, new FXTradeSerializer( new String[]{"Trade", "T"}, EntityHolder.class, new SerializerMap(), TradeSerializer.TradeSerializationType.RFSPrimeCoverTradeVerification ) );
        tradeSerializerMap.put( RFSPrimeBrokerHandlers.RFSPrimeBrokerMakerTradeRejectionEventHandler.EVENT, new FXTradeSerializer( new String[]{"Trade", "T"}, EntityHolder.class, new SerializerMap(), TradeSerializer.TradeSerializationType.RFSPrimeMakerTradeRejection ) );
        tradeSerializerMap.put( RFSPrimeBrokerHandlers.RFSPrimeBrokerCoverTradeRejectionEventHandler.EVENT, new FXTradeSerializer( new String[]{"Trade", "T"}, EntityHolder.class, new SerializerMap(), TradeSerializer.TradeSerializationType.RFSPrimeCoverTradeRejection ) );
        tradeSerializerMap.put( RFSTradeHandlers.PostTradeUpdateEventHandler.EVENT, new FXTradeSerializer( new String[]{"Trade", "T"}, EntityHolder.class, new SerializerMap(), TradeSerializer.TradeSerializationType.RFSPostTradeUpdate ) );
    }

    public FXTradeSerializer getTradeSerializer( String eventName )
    {
        FXTradeSerializer serializer = tradeSerializerMap.get( eventName );
        if ( serializer == null )
        {
            return tradeSerializerMap.get( "DEFAULT" );
        }
        return serializer;
    }
}