package com.integral.dbservice;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import com.integral.session.IdcSessionManager;

/**
 * The objective of this processor is to start a transaction.
 *
 * <AUTHOR>
 */
public class
        StartTransactionProcessor extends Processor {
    private static Log log = LogFactory.getLog(StartTransactionProcessor.class);

    @Override
    public PipelineMessage process(PipelineMessage request) {
        IdcSessionManager.getInstance().setSessionContext(null);
        IdcSessionManager.getInstance().setTransaction(null);
        log.setUserName(null);
        return request;
    }

}
