package com.integral.dbservice;

import com.integral.dbservice.txlog.SyncTask;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import org.apache.commons.lang.StringUtils;

import javax.management.ListenerNotFoundException;
import javax.management.MBeanNotificationInfo;
import javax.management.NotificationFilter;
import javax.management.NotificationListener;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 3/25/11
 * Time: 11:54 AM
 * This processor helps to write the pipeline msg to the txn log. In a multitenant txn mode, tenants sent messages
 * can be written to tenant space on Multitenant Txn Serevr machine using this processor. This allows these messages
 * to be read and replayed to diffrent db.
 */
public class DBPersistPipelineWriterProcessor extends Processor {

    private static final Log log = LogFactory.getLog(DBPersistPipelineWriterProcessor.class);
    private static final char[] CRLF = new String("\r\n").toCharArray();
    private static final ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
    private static Map<String, DBService> dbServices = new ConcurrentHashMap<String, DBService>();
    private final DBService dbService;
    private ByteBufferWriter wri = new ByteBufferWriter(1024 * 8);

    public DBPersistPipelineWriterProcessor(DBService origDbService) throws Exception {
        DBService cachedDBService = dbServices.get(origDbService.getPeerVirtualServer());
        if (cachedDBService == null) {
            synchronized (dbServices) {
                cachedDBService = dbServices.get(origDbService.getPeerVirtualServer());
                if (cachedDBService == null) { //As the processor is created per pipeline instance, we don't want to create a duplicate DB Service for the same.
                    ISPersistenceMBean origConfig = origDbService.getConfig();
                    String multiTenantTxnServerMessagesSaveRootDir = origConfig.getMultiTenantTxnServerMessagesSaveRootDir();
                    if (StringUtils.isBlank(multiTenantTxnServerMessagesSaveRootDir)) {
                        throw new IllegalArgumentException("MultiTenantTxnServerMessagesSaveRootDir not set while running MultiTenantTransaction Server with doSaveMsgs enabled.");
                    }
                    DBPersistPipelineWriterProcessorPersistanceMBean mbean = new DBPersistPipelineWriterProcessorPersistanceMBean(origConfig, multiTenantTxnServerMessagesSaveRootDir);
                    dbService = new DBService(mbean, origDbService.getPeerVirtualServer());
                    dbService.setup();
                    dbServices.put(dbService.getPeerVirtualServer(), dbService);

                    long interval = mbean.getTxLogRolloverIntervalInSeconds();
                    log.info("Setting Rollover interval as: " + interval + " seconds for Copy dbService for PeerVirtualServer:" + dbService.getPeerVirtualServer());
                    executorService.scheduleAtFixedRate(new Runnable() {
                        public void run() {
                            log.info("Starting ROLLOVER.");
                            try {
                                dbService.getTransactionLog().rollover();
                            } catch (Exception e) {
                                log.error("There was a problem during Rollover due to: " + e.getMessage(), e);
                            }
                            log.info("Rollover DONE.");
                        }
                    }, interval, interval, TimeUnit.SECONDS);

                    interval = mbean.getTxLogSyncIntervalInSeconds();
                    log.info("Setting Sync interval as: " + interval + " seconds for Copy dbService for PeerVirtualServer:" + dbService.getPeerVirtualServer());
                    executorService.scheduleAtFixedRate(new SyncTask(dbService), interval, interval, TimeUnit.SECONDS);
                } else {
                    dbService = cachedDBService;
                }
            }
        } else {
            dbService = cachedDBService;
        }
    }

    @Override
    public PipelineMessage process(PipelineMessage msg) {
        TransactionLogMessage tlm = (TransactionLogMessage) msg;
        try {
            if (!tlm.isWarmupEvent) {
                TransactionLogMessage cloneMsg = tlm.getDuplicateMessage();
                //Write tlm to txn log.
                if (tlm.entityText != null) {
                    cloneMsg.entityAsBytes = wri;
                    wri.write(tlm.entityText.toString());
                    wri.write(CRLF);
                    dbService.getTransactionLog().write(cloneMsg);
                } else {
                    throw new IllegalArgumentException("Found entity text null.");
                }
            } else {
                log.info("Skipping warmup event for corelationId: " + tlm.corelationId + " for eventName: " + tlm.eventName);
            }

        } catch (Exception ex) {
            log.error("Unable to write Transaction to the txn file. Absorbing the exception here as we don't want the actual pipeline to go in error condition. Msg:" + tlm, ex);
        }
        return msg;
    }

    private class DBPersistPipelineWriterProcessorPersistanceMBean implements ISPersistenceMBean {
        private final ISPersistenceMBean mbean;
        private final String rootDirPath;

        public DBPersistPipelineWriterProcessorPersistanceMBean(ISPersistenceMBean mbean, String rootDirPath) {
            this.mbean = mbean;
            this.rootDirPath = rootDirPath;
        }

        public int getNumPersistenceThreads() {
            return mbean.getNumPersistenceThreads();
        }

        public int getCompactionLevel() {
            return mbean.getCompactionLevel();
        }

        public boolean isPrettyPrintingEnabled() {
            return mbean.isPrettyPrintingEnabled();
        }

        public long getTxLogRolloverIntervalInSeconds() {
            return mbean.getTxLogRolloverIntervalInSeconds();
        }

        public long getTxLogSyncIntervalInSeconds() {
            return mbean.getTxLogSyncIntervalInSeconds();
        }

        public long getMaxTxLogSyncWaitTimeInSeconds() {
            return mbean.getMaxTxLogSyncWaitTimeInSeconds();
        }

        public boolean isNonBlockingFileIOEnabled() {
            return mbean.isNonBlockingFileIOEnabled();
        }

        public boolean isTXLogFileReaderTaskEnabled() {
            return false; //This process only copies logs, doesen't cnsume it.
        }

        public boolean isTXLogFileWriterTaskEnabled() {
            return true; //This process copies logs to a a new DBService file space.
        }

        public boolean isTXQueueConsumerTaskEnabled() {
            return false; //This process only copies logs, doesen't cnsume it.
        }

        public String getLogRootDirPath() {
            return rootDirPath;
        }

        public String getTXNServerUrl() {
            return ""; //Don't run Multitenant mode for copy dbService
        }

        public long getRemoteTXNServerPingInterval() {
            return mbean.getRemoteTXNServerPingInterval();
        }

        public boolean isSpecialModeTXNServer() {
            return mbean.isSpecialModeTXNServer();
        }

        public int getPipelineQueueLength() {
            return mbean.getPipelineQueueLength();
        }

        public int getPipelineBlockedAlertDuration() {
            return mbean.getPipelineBlockedAlertDuration();
        }

        public boolean doSaveMultiTenantTxnServerMessages() {
            return false;
        }

        public String getMultiTenantTxnServerMessagesSaveRootDir() {
            return null;
        }

        public boolean isReplayModeEnabled() {
            return false; //No replay mode for copy dbService
        }

        public double getReplayFactor() {
            return mbean.getReplayFactor();
        }

		public int getTransactionLogReaderBufferCapacity() {
            return mbean.getTransactionLogReaderBufferCapacity();
        }

        public boolean isMultiTenantRemoteTxnServerEnabled() {
            return mbean.isMultiTenantRemoteTxnServerEnabled();
        }

        public boolean isAutoSelectRemoteTxnServerEnabled() {
            return mbean.isAutoSelectRemoteTxnServerEnabled();
        }

        public int getSendBufferSize() {
            return mbean.getSendBufferSize();
        }

        public void addNotificationListener(NotificationListener listener, NotificationFilter filter, Object handback) throws IllegalArgumentException {
            mbean.addNotificationListener(listener, filter, handback);
        }

        public void removeNotificationListener(NotificationListener listener) throws ListenerNotFoundException {
            mbean.removeNotificationListener(listener);
        }

        public MBeanNotificationInfo[] getNotificationInfo() {
            return mbean.getNotificationInfo();
        }
    }
}
