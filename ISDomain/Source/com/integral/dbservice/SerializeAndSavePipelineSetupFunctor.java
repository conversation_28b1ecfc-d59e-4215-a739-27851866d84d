package com.integral.dbservice;

import com.integral.pipeline.Pipeline;
import com.integral.pipeline.PipelineSetupFunctor;
import com.integral.pipeline.ProcessorDescriptor;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Oct 12, 2010
 * Time: 11:50:13 AM
 * The class is responsible to setup a pipeline to serialize and save transaction log messages.
 */
public class SerializeAndSavePipelineSetupFunctor implements PipelineSetupFunctor {
    private final DBService dbService;
    private final AtomicInteger index;

    public SerializeAndSavePipelineSetupFunctor(DBService dbService) {
        this.dbService = dbService;
        index = new AtomicInteger();
    }

    public Pipeline createPipeline() {
        ByteBufferWriter wri = new ByteBufferWriter(1024 * 8);

        ProcessorDescriptor serialize2Json = new ProcessorDescriptor(
                "SerializeToJson", new SerializeToJsonProcessor(wri, dbService.getConfig()));
        ProcessorDescriptor w2Log = new ProcessorDescriptor(
                "WriteTransactionLog", new WriteToErrorLogProcessor(wri, dbService.getTransactionLog()));
        serialize2Json.setNext(w2Log);
        //ProcessorDescriptor errorHandler = new ProcessorDescriptor("DisableTradingErrorHandler", new DisableTradingProcessor());

        Pipeline p = new Pipeline("SerializeAndSave-" + index.getAndIncrement() + "-" +
                dbService.getPeerVirtualServer(), serialize2Json, null, dbService.getConfig().getPipelineBlockedAlertDuration());
        //p.setErrorHandler(errorHandler);
        
        return p;
    }

    public void reclaim(ProcessorDescriptor pipeline) {
        //do nothing
    }
}
