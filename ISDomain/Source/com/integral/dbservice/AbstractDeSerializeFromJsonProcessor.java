package com.integral.dbservice;

import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.persistence.Entity;
import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.serialize.EntityHolder;
import org.codehaus.jackson.JsonFactory;
import org.codehaus.jackson.JsonParser;
import org.codehaus.jackson.JsonToken;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.integral.serialize.SerializerConstants.NULL_STR;
import static com.integral.serialize.SerializerConstants.PROPERTY_MAP_TOKEN;


public abstract class AbstractDeSerializeFromJsonProcessor extends Processor
{

	private static JsonFactory factory = new JsonFactory();

    protected final ISPersistenceMBean mbean;

	protected final EntityHolder wrapper = new EntityHolder();

    public AbstractDeSerializeFromJsonProcessor() {
        this(ISFactory.getInstance().getPersistenceMBean());
    }

    public AbstractDeSerializeFromJsonProcessor(ISPersistenceMBean mbean) {
        this.mbean = mbean;
    }

    @Override
	public PipelineMessage process( PipelineMessage msg )
	{
		try
		{
			TransactionLogMessage tlm = ( TransactionLogMessage ) msg;

			JsonParser parser = factory.createJsonParser( tlm.entityText.toString() );
			//parser.enableFeature(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES
			// );

			// Opening wrapper curly {
			parser.nextToken();
			// Type name.
			parser.nextToken();
			
			String objectName = parser.getCurrentName(); // entities
			// Advance to object boundary.
			parser.nextToken();

			Object entity;
			// {"entities":[{"T":{"fxSL":{
			if("entities".equals(objectName))
			{
				List<Entity> coll = new ArrayList<Entity>();
				while (parser.nextToken() != JsonToken.END_ARRAY)
				{
					parser.nextToken(); // Object boundary
					objectName = parser.getCurrentName(); // Object like R, T etc
					parser.nextToken();
					Object e = processMsg( msg, parser, objectName );
					coll.add( ( Entity ) e );
					parser.nextToken();
				}
				entity = coll;
				wrapper.setEntities( coll ); // Not needed
			}
			else // R or T or CU
			{
				Object e = processMsg( msg, parser, objectName );
				entity = e;
			}

			tlm.entity = entity;
			//tlm.entityText = null; keep entity text around so that it can be written to error log without the need of serializing the object again.
			
			//process propertymap
			processPropertyMap(tlm,parser);
			long endDerializeTime = System.currentTimeMillis();
			if(MetricsManager.metricsLog.isInfoEnabled())
			    MetricsManager.metricsLog.info( new StringBuilder().append("Metrics (ms): ").append("Event=").append(tlm.eventName).append(", corelationId=").append(tlm.corelationId).append(", startSerializeTime=").append(tlm.creationTime).append(", endDeserializeTime=" ).append(endDerializeTime).append(", timeTaken=").append(endDerializeTime - tlm.creationTime).toString() );
		}
		catch ( Exception e )
		{
			throw new PipelineException( "Unable to convert Json to Entity for corelationId: " + msg.corelationId, e );
		}
		finally
		{
			wrapper.setRequest( null );
			wrapper.setTrade( null );
			wrapper.setEntities( null );
		}
		return msg;
	}

	protected abstract Object processMsg( PipelineMessage msg, JsonParser parser, String objectName ) throws Exception;
	
    private void processPropertyMap(TransactionLogMessage tlm, JsonParser parser) throws Exception{
        parser.nextToken();
        String propertyMapTokenStr = parser.getCurrentName();
        //validate if the next token is propertymap
        if(PROPERTY_MAP_TOKEN.equals( propertyMapTokenStr )){
            parser.nextToken();
            String propertyMapStr = parser.getText();
            Map<String,String> propertyMap = new HashMap<String,String>();
            String[] properties = propertyMapStr.split( "," );
            for ( int i = 0; i < properties.length; i++ )
            {
                if ( properties[i].length() > 0 )
                {
                    int indexOfSeparator = properties[i].indexOf( "=" );
                    if(indexOfSeparator <= 0)
                        continue;
                    String key = properties[i].substring( 0, indexOfSeparator );
                    String value = properties[i].substring( indexOfSeparator + 1 );
                    if(NULL_STR.equals( value )){
                        value = null;
                    }
                    propertyMap.put( key, value );
                }
            }    
            tlm.properties = propertyMap;
        }
    }
}
