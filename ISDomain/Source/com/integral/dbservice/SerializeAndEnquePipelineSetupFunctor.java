package com.integral.dbservice;

import com.integral.pipeline.Pipeline;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.PipelineSetupFunctor;
import com.integral.pipeline.ProcessorDescriptor;
import com.integral.pipeline.WriteToQueueProcessor.QueueResolver;
import com.integral.pipeline.queue.QueueManager;
import com.integral.pipeline.queue.QueueManager.QueueNotFoundException;

import java.util.ArrayList;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * This class is responsible for configuring the SerializeAndEnque pipeline.
 * 
 * <AUTHOR>
 * 
 */
public class SerializeAndEnquePipelineSetupFunctor implements
		PipelineSetupFunctor, QueueResolver {

	protected ArrayList<String> queueNames = new ArrayList<String>();
	protected DBService dbService;
    protected AtomicInteger index = new AtomicInteger(0);
	
	/**
	 * @param queueNames
	 */
	public SerializeAndEnquePipelineSetupFunctor(DBService dbSvc, List<String> queueNames, int queueLen) {
		this.dbService = dbSvc;
		this.queueNames.addAll(queueNames);
		for (String s : queueNames) {
			QueueManager.instance().setupQueue(s, queueLen);
		}
	}

	public Pipeline createPipeline() {
        ByteBufferWriter wri = new ByteBufferWriter(1024*32);

		ProcessorDescriptor serialize2Json = new ProcessorDescriptor(
				"SerializeToJson", new SerializeToJsonProcessor(wri, dbService.getConfig()));
		ProcessorDescriptor w2Log = new ProcessorDescriptor(
				"WriteTransactionLog", new WriteToTransactionLogProcessor(dbService.getTransactionLog(), wri));
        ProcessorDescriptor errorHandler = new ProcessorDescriptor("DisableTradingErrorHandler", new DisableTradingProcessor());

		serialize2Json.setNext(w2Log);

		Pipeline p = new Pipeline("SerializeAndEnque-" + index.getAndIncrement(), serialize2Json, null, dbService.getConfig().getPipelineBlockedAlertDuration());
        p.setErrorHandler(errorHandler);
        return p;
	}

	public void reclaim(ProcessorDescriptor pipeline) {

	}

	/**
	 * Based on the corelationId return an instance of {@link Queue<PipelineMessage>}.
	 * The same corelationId is guaranteed to always return the same instance of
	 * the queue. The <code>corelationId</code> is <b>case-sensitive</b>.
	 * 
	 * @param corelationId
	 * @return
	 */
	public Queue<PipelineMessage> getQueue(String corelationId) {
		int qIdx = corelationId.hashCode() % queueNames.size();
		String queueName = queueNames.get(qIdx);

		Queue<PipelineMessage> que;
		try {
			que = QueueManager.instance().getQueue(queueName.toString());
		} catch (QueueNotFoundException e) {
			que = QueueManager.instance().setupQueue(queueName.toString());
		}
		return que;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.integral.dbservice.WriteToQueueProcessor.QueueFactory#getQueue(com
	 * .integral.message.Message)
	 */
	public Queue<PipelineMessage> getQueue(PipelineMessage msg) {

		return getQueue(msg.getCorelationId());
	}

}
