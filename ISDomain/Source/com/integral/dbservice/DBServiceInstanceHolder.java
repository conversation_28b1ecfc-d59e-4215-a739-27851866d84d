package com.integral.dbservice;

import com.integral.system.configuration.ConfigurationFactory;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Sep 16, 2010
 * Time: 2:03:41 PM
 * A holder class for DBService instance.
 */
public class DBServiceInstanceHolder {

    private static Map<String, DBService> dbServiceInstances = new ConcurrentHashMap<String, DBService>();

    public static DBService getDBServiceInstance() {
        return getDBServiceInstance(ConfigurationFactory.getServerMBean().getPeerVirtualServerName());
    }

    public static DBService getDBServiceInstance(String peerVirtualServer) {
        return dbServiceInstances.get(peerVirtualServer);
    }

    public static void setDBServiceInstance(DBService dbService) {
        DBServiceInstanceHolder.dbServiceInstances.put(dbService.getPeerVirtualServer(), dbService);
    }

    public static Set<String> getPeerVirtualServers() {
        return dbServiceInstances.keySet();
    }
}
