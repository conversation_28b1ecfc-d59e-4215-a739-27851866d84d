package com.integral.dbservice;

import com.integral.dbservice.is.NettingPortfolioSerializerFactory;
import com.integral.dbservice.is.RequestSerializerFactory;
import com.integral.finance.creditLimit.CreditLimitWorkflowState;
import com.integral.finance.creditLimit.CreditUtilizationEvent;
import com.integral.finance.dealing.Request;
import com.integral.finance.trade.Trade;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.netting.model.NettingPortfolioC;
import com.integral.persistence.Entity;
import com.integral.persistence.NotificationEntity;
import com.integral.serialize.*;

import java.io.Writer;


public class SerializeToJsonProcessor extends AbstractSerializeToJsonProcessor {

	private static Log log = LogFactory.getLog(SerializeToJsonProcessor.class);

    private static final TradeSerializerFactory tradeSerializerFactory = TradeSerializerFactory.getInstance();
    private final FXCreditUtilzationSerializer fxCreditUtilizatonSerializer = new FXCreditUtilzationSerializer(new String[] {"CreditUtilizationEvent","CU"}, EntityHolder.class, new SerializerMap() );
    private final CreditLimitWorkflowStateSerializer creditLimitWfStSerializer = new CreditLimitWorkflowStateSerializer(new String[] {"CreditLimitWorkflowState","clws"}, EntityHolder.class, new SerializerMap() );
    private final NotificationEntitySerializer notificationEntitySerializer = new NotificationEntitySerializer(new String[] {"NotificationEntity","ne"}, EntityHolder.class, new SerializerMap() );
    private static final RequestSerializerFactory requestSerializerFactory = RequestSerializerFactory.instance();
    private final NettingPortfolioSerializerFactory nettingPortfolioSerializerFactory = NettingPortfolioSerializerFactory.instance();
    protected final EntityHolder holder = new EntityHolder();


    public SerializeToJsonProcessor(Writer wri) {
        this(wri, ISFactory.getInstance().getPersistenceMBean());
    }

    public SerializeToJsonProcessor(Writer wri, ISPersistenceMBean mbean) {
        super(wri,mbean);
    }

    
    protected void processMsg( Entity e, String eventName ) throws Exception
    {
        Serializer serializer = null;
        if ( e instanceof Request) 
        {
        	holder.setRequest((Request)e);
            serializer = requestSerializerFactory.getSerializer(eventName);
        }
        else if (e instanceof Trade) 
        {
        	holder.setTrade((Trade)e);
            serializer = tradeSerializerFactory.getTradeSerializer( eventName );
        }
        else if ( e instanceof CreditUtilizationEvent)
        {
        	holder.setCreditUtilizationEvent( (CreditUtilizationEvent )e);
        	serializer = fxCreditUtilizatonSerializer;
        } 
        else if ( e instanceof CreditLimitWorkflowState)
        {
        	holder.setCreditLimitWorkflowState( (CreditLimitWorkflowState ) e);
        	serializer = creditLimitWfStSerializer;
        }
        else if ( e instanceof NettingPortfolioC)
        {
            holder.setNettingPortfolio( (NettingPortfolioC ) e);
            serializer = nettingPortfolioSerializerFactory.getSerializer( eventName );
            
        }else if ( e instanceof NotificationEntity){
        	
        	holder.setNotificationEntity((NotificationEntity) e);
        	serializer = notificationEntitySerializer;
        	
        }
        serializer.serialize(holder, g, mbean.getCompactionLevel());
    }
}

