package com.integral.dbservice;

import com.integral.dbservice.is.EventProcessingPipelineSetupFunctor;
import com.integral.dbservice.is.ISPersistenceService;
import com.integral.dbservice.is.MockSerializeAndPersistPipelineSetupFunctor;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Nov 18, 2010
 * Time: 11:58:11 AM
 * As startup class that initializes the DBPerisist Pipeline with a task that throws exception.
 * The scenario is used to test the service in case of pipeline failures.
 */
public class MockPersistPipelinePersistServiceStartupC extends PersistenceServiceStartupC
{
    ISPersistenceService isPersistenceService;

    public MockPersistPipelinePersistServiceStartupC()
    {
        super();
    }

    @Override
    protected void initializePeristenceService() throws Exception
    {
        ISPersistenceMBean mbean = ISFactory.getInstance().getPersistenceMBean();
        log.info( "Setting up the DBService framework." );
        dbService = new DBService( mbean );

        MockSerializeAndPersistPipelineSetupFunctor functor = new MockSerializeAndPersistPipelineSetupFunctor( dbService );
        dbService.setSerializeAndEnqueSetupFunctor( functor );

        EventProcessingPipelineSetupFunctor functor2 = new EventProcessingPipelineSetupFunctor( dbService );
        dbService.setEventPipelineSetupFunctor( functor2 );

        dbService.setup();

        log.info( "Initializing and Starting Persistence Service." );
        isPersistenceService = new ISPersistenceService( dbService );
        //Set the singleton instance.
        ISPersistenceService.__private_startup_setInstance( isPersistenceService );

    }

}