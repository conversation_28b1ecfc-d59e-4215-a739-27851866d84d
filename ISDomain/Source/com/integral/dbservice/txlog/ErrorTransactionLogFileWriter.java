package com.integral.dbservice.txlog;

import com.integral.dbservice.TransactionLogMessage;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Sep 23, 2010
 * Time: 11:41:54 AM
 * Transaction file writer to write to Error Files.
 */
public class ErrorTransactionLogFileWriter {

    private static Log log = LogFactory.getLog( ErrorTransactionLogFileWriter.class );

    private final Lock instanceLock = new ReentrantLock();
    private final AtomicLong transactionId = new AtomicLong(System.currentTimeMillis()); //instead of starting from 0, start with current time as the logs are appended to same log file after restart and we want txIds to be unique within a txlog file.
    private final TransactionLog txSystem;
    private final int index;
    private File txFile;
    private FileChannel txOut;
    final private ByteBuffer writeBuffer;
    
    public ErrorTransactionLogFileWriter(TransactionLog txSystem, int index, int bufferSizeInBytes) {
        this.txSystem = txSystem;
        this.index = index;
        writeBuffer = ByteBuffer.allocateDirect( bufferSizeInBytes );
    }

    public ErrorTransactionLogFileWriter(TransactionLog txSystem, int index) {
        this(txSystem, index,4 * 1024);
    }

    public void setup() throws IOException {
        setLatestErrorTxLogFile();
    }

    private void setLatestErrorTxLogFile() throws IOException {
        txFile = txSystem.getLatestErrorTxLogFile(index);

        //close previous stream
        closeWriter();

        // reset the transaction id to the start - this may change in future.
        transactionId.set( System.currentTimeMillis() );

        // true=append
        this.txOut = new FileOutputStream( txFile, true ).getChannel();

        log.info( "Latest ErrorTxLogFile for index: [" + index + "] is: [" + this.txFile.getAbsolutePath() + "]" );
    }

    /**
     * Write requests can be from multiple threads - therefore, this method
     * should be thread-safe.
     *
     * @param message
     * @throws IOException
     */
    public void write( TransactionLogMessage message ) throws IOException
    {
        instanceLock.lock();

        try
        {
            // Append CRLF to serialized data. We do this to ensure all
            // transactions are nicely separated,
            // and readable.

            //message.seekPtr = txOut.position(); Don't change the original message.

            // clear the buffer, and clear all existing pointers.
            writeBuffer.clear();

            ArrayList<ByteBuffer> encodedBuffers = message.entityAsBytes.getEncodedBuffers();

            // increment and get the next transaction id - this is our internal id,
            // and not the corelationId.
            long nextTxId = transactionId.incrementAndGet();
            //assign a new trx id to the message as it is written to error log to support the assumption that txid is unique in a file.
            //Also as error logs would be replayed as trx logs, it is important that all assumptions in txn logs are maintained.
            message.transactionId = nextTxId;

            // get the headers into the buffer.
            message.writeHeaders( writeBuffer );
            writeBuffer.flip();
            // write the headers to the file, and flip it.
            txOut.write( writeBuffer );

            for ( int i = 0; i < encodedBuffers.size(); i++ )
            {
                ByteBuffer bb = encodedBuffers.get( i );
                while ( bb.hasRemaining() )
                {
                    txOut.write( bb );
                }
            }
            // non-blocking IO.
            this.txOut.force(false);
        }
        finally
        {
            message.entityAsBytes.reinitialize();
            instanceLock.unlock();
        }
    }

     public void rollover() throws IOException
    {
        instanceLock.lock();
        try
        {
            // Create a brand new error tx logging file.
            txSystem.createNewErrorTxLog( index );
            // Switch to the latest file.
            setLatestErrorTxLogFile();
        }
        finally
        {
            instanceLock.unlock();
        }
    }

    private void closeWriter()
    {
        if ( this.txOut != null )
        {
            instanceLock.lock(); //make sure no writers are in the process of writing a log.
            try
            {
                this.txOut.force( true );
                this.txOut.close();
            }
            catch(java.nio.channels.ClosedChannelException e1){
                log.warn( "Writer is already in closed state.");
            }
            catch ( Exception e )
            {
                log.error( "Unable to close the stream.", e );
            }
            finally {
                instanceLock.unlock();
            }
        }

    }

    public void shutdown(){
        closeWriter();
    }
}
