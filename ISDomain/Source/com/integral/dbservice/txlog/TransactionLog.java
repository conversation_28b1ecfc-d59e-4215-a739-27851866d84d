package com.integral.dbservice.txlog;

import com.integral.dbservice.DBService;
import com.integral.dbservice.DBServiceInstanceHolder;
import com.integral.dbservice.TXNServerStateHolderC;
import com.integral.dbservice.TransactionLogMessage;
import com.integral.dbservice.remote.MultiTenantRemoteTransactionServerHelper;
import com.integral.dbservice.txlog.MetaFileManager.MetaFileDescriptor;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.transaction.AsyncDBMetaInfo;
import com.integral.util.Triplet;
import com.integral.util.Tuple;

import java.io.*;
import java.nio.channels.FileChannel;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * The TransactionLog to be used by DBService, or any other entity wishing to
 * utilize the transaction logging facilities. The primary functions of this
 * logging system are: <li>Accepts a {@link TransactionLogMessage}, and
 * determines the log file to write to based on the
 * {@link TransactionLogMessage#getCorelationId() corelationId}. <li>The
 * parallelism factor is determined by {@link DBService#getNumQueues()}. This
 * value is consumed during the {@link #setup()} workflow. <li>Generate a unique
 * id for each transaction that is logged. This <code>txid</code> is guaranteed
 * to be a serially increasing number. <li>At regular intervals roll the log
 * file. This interval can be configured based on file size of time of day.
 * Also, where required it is possible to manually rollover using
 * {@link #rollover()}. <br>
 * <p/>
 * "index" is always 0-based, in all contexts. <br>
 * <p/>
 * TransactionLog file naming depends on the parallelism factor. There are as
 * many transaction logs created as the parallelism factory. The naming scheme
 * is such: txns_&lt;index&gt;.txl. <br>
 * <p/>
 * When a transaction log is rolled, an integer suffix is attached to the end of
 * the file name as such: txns_&lt;index&gt;.txl.1, txns_&lt;index&gt;.txl.2,
 * etc.. The most recent rolled file is at index 1, and so on... <br>
 * <p/>
 * <p/>
 * Transaction log files can be either "hot" or "cold". Hot files are the ones
 * into which transactions have been written, but have not yet been processed by
 * the event processing system. It reflects the TBD transactions in the system.
 * Once all transactions have been processed, the log files are moved to the
 * "cold" directory. Typically hot directory is named "log-hot", and cold dir is
 * named "log-cold".
 *
 * <AUTHOR>
 */
public class TransactionLog {
    private static Log log = LogFactory.getLog(TransactionLog.class);

    public static final String HOT_DIR = "log-hot";
    public static final String COLD_DIR = "log-cold";
    public static final String TX_LOG_SUFFIX = "txl";
    public static final String META_SUFFIX = "META";

    /**
     * This represents all the active transaction log files in the system.
     */
    private ArrayList<TransactionLogFileWriter> logFiles = new ArrayList<TransactionLogFileWriter>(10);

    /**
     * This represents all the active error transaction log files in the system.
     */
    private ArrayList<ErrorTransactionLogFileWriter> errorTxLogFiles = new ArrayList<ErrorTransactionLogFileWriter>(10);

    /**
     * This represents all the threads reading from their respective
     * transactiong log files.
     */
    private ArrayList<Tuple<Thread, TransactionLogReaderTask>> readerTasks = new ArrayList<Tuple<Thread, TransactionLogReaderTask>>(10);
    private int numLogFiles = 2;
    //private State state;

    private final String rootPath;

    private String homedir = null;
    private String hotdir = null;
    private String colddir = null;

    private final MetaFileManager metaFileMgr;

    private ISPersistenceMBean config;
    private DBService dbService;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH");

    public TransactionLog(DBService dbSvc, ISPersistenceMBean config) {
        this.dbService = dbSvc;
        this.config = config;
        this.numLogFiles = config.getNumPersistenceThreads();
        metaFileMgr = new MetaFileManager(this);
        rootPath = config.getLogRootDirPath();
    }

    /**
     * Initialize the transaction logging system.
     *
     * @throws TransactionLogException
     * @throws IOException
     */
    public void setup() throws Exception {
        TXNServerStateHolderC.getInstance(dbService.getPeerVirtualServer()).getState().setTxLogState(State.STARTUP);
        internalSetup();

        if (config.isTXLogFileWriterTaskEnabled()) {

            log.info("Setting up TransactionLogFileWriter instances.");
            for (int i = 0; i < numLogFiles; i++) {
                TransactionLogFileWriter writer = new TransactionLogFileWriter(this, i,config);
                writer.setup();
                logFiles.add(writer);
            }
            log.info("DONE: Setup: [" + numLogFiles + "] TransactionLogFileWriter instances.");

        }
        metaFileMgr.setup();
        setupErrorLogWriters();
        setupReaderTasks();
        TXNServerStateHolderC.getInstance(dbService.getPeerVirtualServer()).getState().setTxLogState(State.STARTED);

    }

    public void setupErrorLogWriters() throws IOException {
        if(config.isTXQueueConsumerTaskEnabled()){
            log.info("Setting up TransactionLogFileWriter instances for error logs.");
            for (int i = 0; i < numLogFiles; i++) {
                ErrorTransactionLogFileWriter writer = new ErrorTransactionLogFileWriter(this, i);
                writer.setup();
                errorTxLogFiles.add(writer);
            }
            log.info("DONE: Setup: [" + numLogFiles + "] TransactionLogFileWriter instances for error logs.");
        }
    }

    public void setupReaderTasks() throws Exception {

        if (config.isTXLogFileReaderTaskEnabled()) {
            log.info("Setting up TransactionLogReader instances.");

            for (int i = 0; i < metaFileMgr.getNumMetaFiles(); i++) {
                TransactionLogReaderTask readerTask = new TransactionLogReaderTask(this.dbService, i);
                MetaFileDescriptor descriptor = metaFileMgr.getMetaFileForIndex(i);
                if (descriptor != null) {
                    StringBuilder fqname = new StringBuilder();
                    fqname.append(getHotDirFile().getAbsolutePath()).append(File.separator).append(descriptor.state.fileName);
                    readerTask.seekTo(fqname.toString(), descriptor.state.seekPtr, descriptor.state.transactionId);

                    readerTask.setStartup(true);

                    String name = "Thread-TLReader" + "-" + i + "-" + dbService.getPeerVirtualServer();
                    Thread t = new Thread(readerTask, name);
                    readerTasks.add(new Tuple<Thread, TransactionLogReaderTask>(t, readerTask));

                    if (i >= this.numLogFiles) {
                        // Tell the reader to auto-exit, when there is no more data
                        // to be read for it.
                        // These were the extra descriptors created - in case of
                        // recovery - to handle the old tx log files.
                        readerTask.setAutoExit(true);
                    }

                    readerTask.setup();

                    t.start();
                }
            }

            log.info("DONE: Setting: [" + numLogFiles + "] TransactionLogReader instances.");
        }
    }

    // This method just initializes the internals without creating any
    // readers/writers.
    // mostly used by unit tests to initialize the TxLog system.
    /* package */

    void internalSetup() throws Exception {
        // The number of log files is set at startup time, and matches the
        // number of parallel queues we have in the system.
        this.numLogFiles = config.getNumPersistenceThreads();
        setupHomeDir();
    }

    public void updateMetaInformation(int index, TransactionLogMessage tlm) {
        try {        	
            this.metaFileMgr.updateMetaState(index, tlm);
        }
        catch (Exception e) {
            log.error("There was a problem writing META information to disk. This needs to be resolved ASAP. The exception was: " + e.getMessage(), e);
        }
    }

    /* package */

    String getHotDirName() {
        return hotdir;
    }

    /* package */

    File getHotDirFile() throws IOException {
        return setupDir(hotdir);
    }

    /* package */

    String getColdDirName() {
        return colddir;
    }

    public void shutdown() {
        TXNServerStateHolderC.getInstance(dbService.getPeerVirtualServer()).getState().setTxLogState(State.SHUTDOWN);

        //shutdown all writers
        for (TransactionLogFileWriter writer : logFiles) {
            writer.shutdown();
        }

        //shutdown all error writers
        for (ErrorTransactionLogFileWriter writer : errorTxLogFiles) {
            writer.shutdown();
        }

        // Shutdown all readers.
        for (Tuple<Thread, TransactionLogReaderTask> t : readerTasks) {
            t.second.shutdown();
        }

        metaFileMgr.shutdown();

    }

    public void restartReaderTasks() throws Exception {
        // Shutdown all readers.
        for (Tuple<Thread, TransactionLogReaderTask> t : readerTasks) {
            t.second.shutdown();
        }

        //start all readertasks
        setupReaderTasks();
    }

    public TransactionLogMessage write(TransactionLogMessage message) throws IOException {

        if (TXNServerStateHolderC.getInstance(dbService.getPeerVirtualServer()).getState().getTxLogState() == State.SHUTDOWN) {
            throw new IllegalAccessError("TransactionLog system is currently shutdown.");
        }

        int idx = message.getCorelationIndex(this.numLogFiles);
        if (idx >= this.numLogFiles) { // in case getCorelationIndex was screwed
            // up, let's be defensive..
            idx = this.numLogFiles - 1;
        }
        TransactionLogFileWriter target = logFiles.get(idx);
        target.write(message);
        return message;
    }

    public TransactionLogMessage writeErrorMessage(TransactionLogMessage message) throws IOException {

        if (TXNServerStateHolderC.getInstance(dbService.getPeerVirtualServer()).getState().getTxLogState() == State.SHUTDOWN) {
            throw new IllegalAccessError("TransactionLog system is currently shutdown.");
        }

        int idx = message.getCorelationIndex(this.numLogFiles);
        if (idx >= this.numLogFiles) { // in case getCorelationIndex was screwed
            // up, let's be defensive..
            idx = this.numLogFiles - 1;
        }
        ErrorTransactionLogFileWriter target = errorTxLogFiles.get(idx);
        target.write(message);
        return message;
    }

    public void setupHomeDir() throws IOException {
        homedir = rootPath + File.separator + "tx";
        String vs = dbService.getPeerVirtualServer();
        hotdir = homedir + File.separator + (vs==null?HOT_DIR:vs+File.separator+HOT_DIR);
        colddir = homedir + File.separator + (vs==null?COLD_DIR:vs+File.separator+COLD_DIR);
        File hotdirFile = setupDir(hotdir);
        log.info("Hot-dir for transaction log files is now set to: [" + hotdirFile.getAbsolutePath() + "]");
        File colddirFile = setupDir(colddir);
        log.info("Inactive transaction log files will get moved to cold-dir: [" + colddirFile.getAbsolutePath() + "]");
    }

    /**
     * To be used by unit tests to clean up tx log files.
     * @throws Exception
     */
    void cleanupHomeDir() throws Exception {
        cleanupDirectory(hotdir);
        cleanupDirectory(colddir);
    }

    private void cleanupDirectory(String directory) throws Exception {
        File f = new File(directory);
        if(f.exists()){
            for(File txFile:f.listFiles()){
                if(!txFile.delete()){
                    log.error("Unable to delete file:"+txFile.getAbsolutePath());
                }
            }
        }
    }

    /* package */

    File setupDir(String dir) throws IOException {
        File f = new File(dir);
        if (f.exists()) {
            if (!f.isDirectory()) {
                String msg = "Cannot create home directory for transaction log files. Cannot create directory: [" + dir + "]";
                log.error(msg);
                throw new IOException(msg);
            }
        } else {
            if (!f.mkdirs()) {
                String msg = "Cannot create directory: [" + dir + "]";
                log.error(msg);
                throw new IOException(msg);
            } else {
                log.info("Created directory: [" + dir + "].");
            }
        }
        return f;
    }

    Lock rolloverLock = new ReentrantLock();

    public void resetReaderTask(int index, AsyncDBMetaInfo metaInfo) throws IOException {
        StringBuilder fqname = new StringBuilder();
        fqname.append(getHotDirFile().getAbsolutePath()).append(File.separator).append(metaInfo.getTxFileName());
        TransactionLogReaderTask logReaderTask = readerTasks.get(index).second;
        if(!logReaderTask.seekTo(fqname.toString(), metaInfo.getSeekPtr(), metaInfo.getTransactionId())){
           	StringBuilder data = new StringBuilder();
        	data.append(" index: ");
        	data.append(index);
        	data.append(" to file: ");
        	data.append(fqname.toString());
        	data.append(" seekPtr:");
        	data.append(metaInfo.getSeekPtr());
        	data.append(" and expected trxId:");
        	data.append(metaInfo.getTransactionId());
        	data.append(". As recovery, calling setup again.");
            if (!MultiTenantRemoteTransactionServerHelper.DEFAULT_META_FILE_NAME.equals(metaInfo.getTxFileName())) {
            	MessageLogger.getInstance().log(ISAlertMBean.ALERT_EVENT_UNABLE_TO_RESET_READER_TASK, "TransactionLog.resetReaderTask()", "Unable to reset the reader task", data.toString());
                log.warn("Unable to reset the reader task for" + data.toString());
            } else {
                log.info("New Setup. Resetting the reader task for" + data.toString());
            }
            logReaderTask.setup();
        }
    }

    public void rollover() {
        rolloverLock.lock();
        try {
            for (int i = 0; i < logFiles.size(); i++) {
                TransactionLogFileWriter wri = logFiles.get(i);
                try {
                    wri.rollover();
                    readerTasks.get(i).second.notifyRolledOver();
                }
                catch (IOException e) {
                    log.error("There was a problem rolling over transaction log at index: [" + i + "] due to: " + e.getMessage(), e);
                }
            }
        }
        finally {
            rolloverLock.unlock();
        }
    }

    public void rollOverErrorTxLog(int index) {
        try {
            errorTxLogFiles.get(index).rollover();
        } catch (IOException e) {
            log.error("There was a problem rolloing over error tx log fle for index:"+index, e);
        }
    }

    public void onDemandRollover(String fileName, int index) throws IOException {
        rolloverLock.lock();
        try {
            try {
                logFiles.get(index).onDemandRollover(fileName);
                readerTasks.get(index).second.notifyRolledOver();
            } catch (IOException e) {
                log.error("There was a problem for on demand rollover for file:" + fileName, e);
                throw e;
            }
        } finally {

            rolloverLock.unlock();
        }

    }

    public void onDemandErrorLogReplay(int idx) throws IOException {

        log.info("onDemandErrorLogReplay called for index:" + idx + ", tenant:" + dbService.getPeerVirtualServer());
        rollOverErrorTxLog(idx);
        File dropInFile = getEarliestErrorTxLogFile(idx);
        DBServiceInstanceHolder.getDBServiceInstance().getTransactionLog().onDemandRollover(dropInFile.getAbsolutePath(), idx); //rollover should always be on the current Tx Server.
        moveToCold(dropInFile);
        log.info("onDemandErrorLogReplay Successful.");

    }

    public void syncToFile() {
        for(TransactionLogFileWriter wri:logFiles){
            wri.trySyncToFile();
        }
    }

    public void moveOverToCold(){
        List<AsyncDBMetaInfo> metaFiles =  metaFileMgr.queryMetaInfoObjects();
        for(AsyncDBMetaInfo metaInfo:metaFiles){
            List<File> coldFiles = Collections.emptyList();
            try {
                coldFiles = getEarlierTxLogFiles(metaInfo.getThreadNum(),metaInfo.getTxFileName());
            } catch (Exception e) {
                log.error("Error getting earlier files from hot directory for file name:"+metaInfo.getTxFileName(),e);
            }
            for(File coldFile:coldFiles){
                try {
                    moveToCold(coldFile);
                } catch (IOException e) {
                    log.error("There was a problem moving transaction log file :"+coldFile.getName()+" to tx-cold.",e);
                }
            }
        }
    }

    /* package */

    static enum When {
        Earliest, Latest
    }

    /* package */

    File createNewTxLog(int index) throws IOException {
        setupDir(this.hotdir);
        String prefix = getTxLogPrefix(index);

        // This is the file that we will return, once we create it.
        File theFile = null;
        Date theDate = null;

        theDate = new Date();
        String formattedDate = getFormattedDate(theDate);
        StringBuilder builder = new StringBuilder();
        builder.append(hotdir).append(File.separator).append(prefix).append(formattedDate).append('-').append(theDate.getTime()).append(".").append(TX_LOG_SUFFIX);
        theFile = new File(builder.toString());
        theFile.createNewFile();

        //Set writeable only by owner - 644
        /*
        try {
            Runtime.getRuntime().exec("chmod 644 " + theFile.getCanonicalPath());
        } catch (IOException e) {
        }
        */

        StringBuilder msg = new StringBuilder();
        msg.append("Create new tx log file [").append(builder.toString()).append("]");

        log.info(msg.toString());

        return theFile;
    }

    File createNewErrorTxLog(int index) throws IOException {
        setupDir(this.hotdir);
        String prefix = getErrorTxLogPrefix(index);

        // This is the file that we will return, once we create it.
        File theFile = null;
        Date theDate = null;

        theDate = new Date();
        String formattedDate = getFormattedDate(theDate);
        StringBuilder builder = new StringBuilder();
        builder.append(hotdir).append(File.separator).append(prefix).append(formattedDate).append('-').append(theDate.getTime()).append(".").append(TX_LOG_SUFFIX);
        theFile = new File(builder.toString());
        theFile.createNewFile();

        //Set writeable only by owner - 644
        try {
            Runtime.getRuntime().exec("chmod 644 " + theFile.getCanonicalPath());
        } catch (IOException e) {
        }

        StringBuilder msg = new StringBuilder();
        msg.append("Create new error tx log file [").append(builder.toString()).append("]");

        log.info(msg.toString());

        return theFile;
    }

    private synchronized String getFormattedDate(Date theDate) {
        return sdf.format(theDate);
    }

    /**
     * Scan the file system, and return the earliest or latest file for the
     * index, depending on the {@link When when} parameter.
     *
     * @param index
     * @param when
     * @return
     * @throws IOException
     */
    /* package */File getEarliestOrLatestTxLogFile(int index, When when) throws IOException {
        File[] txLogs = getHotTxLogFiles(index);

        // This is the file that we will return, once we find it.
        File theFile = null;
        Date theDate = null;

        for (File f : txLogs) {
            Triplet<String, Integer, Date> t = new Triplet<String, Integer, Date>();
            if (parseTxLogFileName(f.getName(), t)) {
                if (theDate == null) {
                    theDate = t.third;
                    theFile = f;
                } else {
                    // only the <,> operator changes for Earliest vs Latest.
                    if (when == When.Earliest) {
                        if (t.third.getTime() < theDate.getTime()) {
                            theDate = t.third;
                            theFile = f;
                        }
                    } else if (when == When.Latest) {
                        if (t.third.getTime() > theDate.getTime()) {
                            theDate = t.third;
                            theFile = f;
                        }
                    }
                }

            }
        }

        if (theFile == null) {
            theFile = createNewTxLog(index);
        }

        return theFile;
    }

     /**
     * Scan the file system, and return the earliest or latest error file for the
     * index, depending on the {@link When when} parameter.
     *
     * @param index
     * @param when
     * @return
     * @throws IOException
     */
    /* package */File getEarliestOrLatestErrorTxLogFile(int index, When when) throws IOException {
        File result = null;
        File[] txLogs = getHotErrorTxLogFiles(index);
        if (txLogs != null) {
            Date theDate = null;
            for (File f : txLogs) {
                Triplet<String, Integer, Date> t = new Triplet<String, Integer, Date>();
                if (parseTxLogFileName(f.getName(), t)) {
                    if (theDate == null) {
                        theDate = t.third;
                        result = f;
                    } else {
                        // only the <,> operator changes for Earliest vs Latest.
                        if (when == When.Earliest) {
                            if (t.third.getTime() < theDate.getTime()) {
                                theDate = t.third;
                                result = f;
                            }
                        } else if (when == When.Latest) {
                            if (t.third.getTime() > theDate.getTime()) {
                                theDate = t.third;
                                result = f;
                            }
                        }
                    }
                }
            }
        }

        if(result == null){
            result = createNewErrorTxLog(index);
        }
        return result;
    }

    /**
     * Returns the log files earlier than the given file.
     */
    List<File> getEarlierTxLogFiles(int index, String fileName) throws IOException {
        List<File> result = new ArrayList<File>();
        Triplet<String, Integer, Date> t = new Triplet<String, Integer, Date>();
        if(parseTxLogFileName(fileName, t)){
            Date theDate = t.third;
            File hotDirFile = setupDir(this.hotdir);
            String prefix = getTxLogPrefix(index);
            TxLogFilenameFilter filter = new TxLogFilenameFilter(prefix, TX_LOG_SUFFIX);
            File[] txLogs = getFileList(hotDirFile,filter);
            for (File f : txLogs) {
               if (parseTxLogFileName(f.getName(), t)) {
                   if(t.third.getTime() < theDate.getTime()){
                       result.add(f);
                   }
               }
            }
        }
        return result;
    }

    /**
     * Get a transaction log file that is one newer/later than the
     * <code>currentFile</code> that was passed in.
     *
     * @param index
     * @return
     * @throws IOException
     */
    public File getLaterTxLogFile(File currentFile, int index) throws IOException {
        Triplet<String, Integer, Date> t = new Triplet<String, Integer, Date>();
        if(parseTxLogFileName(currentFile.getName(), t)){
            Date currentFileDate = t.third;
            Tuple<File,Date> result = new Tuple<File,Date>();
            File[] txLogs = getHotTxLogFiles(index);
            for (File f : txLogs) {
                if(parseTxLogFileName(f.getName(),t)){
                    if(t.third.compareTo(currentFileDate)>0){
                        if(result.second == null || result.second.compareTo(t.third) > 0){
                            result.first = f;
                            result.second = t.third;
                        }
                    }
                }
            }


            // We got nothing, so we are still the latest n greatest.
            if (result.first == null) {
                return null;
            }

            // Yoohoo - we have a newer file!
            return result.first;
        }
        return null;
    }

    private static Lock listFilesLock = new ReentrantLock(); //a static lock for JVM level locking.

    /**
     * Creating a lock protected method to protect File.listFiles() from heap corruption as a result of JVM bug.
     * Refer to Bug 45449 for more details.
     * @param dirFile
     * @param filter
     * @return
     */
    private File[] getFileList(File dirFile, TxLogFilenameFilter filter) throws IOException{
        listFilesLock.lock();
        try {
            File[] txLogs = dirFile.listFiles(filter);
            return txLogs;
        } finally {
            listFilesLock.unlock();
        }
    }

    private File[] getHotTxLogFiles(int index) throws IOException {
        File hotDirFile = setupDir(this.hotdir);
        String prefix = getTxLogPrefix(index);

        // Retrieve all files for this index from the hotDir.
        TxLogFilenameFilter filter = new TxLogFilenameFilter(prefix, TX_LOG_SUFFIX);
        return getFileList(hotDirFile, filter);
    }

    private File[] getHotErrorTxLogFiles(int index) throws IOException {
        File hotDirFile = setupDir(this.hotdir);
        String prefix = getErrorTxLogPrefix(index);

        // Retrieve all files for this index from the hotDir.
        TxLogFilenameFilter filter = new TxLogFilenameFilter(prefix, TX_LOG_SUFFIX);
        return getFileList(hotDirFile, filter);
    }


    // These should not be invoked very intensively. I am hoping this is a rare
    // event - when somebody has to find the latest or
    // earliest file.

    /**
     * Return the latest transaction log file for an index. Create the file, if
     * one did not exist. Filenames are of the format:
     * {@link DBService#PERSIST_Q_PREFIX &lt;Prefix&gt;}
     * -&lt;index&gt;-&lt;creationTimeInMillis&gt;.{@link #TX_LOG_SUFFIX
     * &lt;suffix&gt;}.
     */
    public File getLatestTxLogFile(int index) throws IOException {
        return getEarliestOrLatestTxLogFile(index, When.Latest);
    }

    public File getLatestErrorTxLogFile(int index) throws IOException {
        return getEarliestOrLatestErrorTxLogFile(index, When.Latest);
    }

    /**
     * Return the earliest transaction log file for an index. Create the file,
     * if one did not exist. Filenames are of the format:
     * {@link DBService#PERSIST_Q_PREFIX &lt;Prefix&gt;}
     * -&lt;index&gt;-&lt;creationTimeInMillis&gt;.{@link #TX_LOG_SUFFIX
     * &lt;suffix&gt;}.
     */
    public File getEarliestTxLogFile(int index) throws IOException {
        return getEarliestOrLatestTxLogFile(index, When.Earliest);
    }

    public File getEarliestErrorTxLogFile(int index) throws IOException {
        return getEarliestOrLatestErrorTxLogFile(index, When.Earliest);
    }

    /**
     * This method deduces the prefix of a transaction log file, given it's
     * index.
     *
     * @param index
     * @return
     */
    /* package */String getTxLogPrefix(int index) {
        StringBuilder builder = new StringBuilder();
        builder.append(DBService.PERSIST_Q_PREFIX).append(index).append('-');
        String fqname = builder.toString();
        return fqname;
    }

	/**
      * This method deduces the prefix of a drop in transaction log file, given it's
      * index.
      *
      * @param index
      * @return
      */
     String getErrorTxLogPrefix(int index) {
         StringBuilder builder = new StringBuilder();
         builder.append(DBService.ERROR_Q_PREFIX).append(index).append('-');
         String fqname = builder.toString();
         return fqname;
     }

    /**
     * Breakdown a filename in the format Prefix-<index>-<timestamp>.suffix into
     * it's individual components.
     *
     * @param fileName
     * @param components
     */
    /* package */boolean parseTxLogFileName(String fileName, Triplet<String, Integer, Date> components) {
        int state = 0; // 0 = prefix, 1 = index, 2=date-hr, 3=dateLongTime 4=suffix
        StringBuilder prefixStr = new StringBuilder();
        StringBuilder indexStr = new StringBuilder();
        StringBuilder dateYrStr = new StringBuilder();
        StringBuilder dateMonthStr = new StringBuilder();
        StringBuilder dateDayStr = new StringBuilder();
        StringBuilder dateHrStr = new StringBuilder();
        StringBuilder dateTimeStr = new StringBuilder();

        for (char ch : fileName.toCharArray()) {
            // we hit a separator, so increment to next state, and continue.
            if (ch == '-') {
                state++;
                continue;
            }

            switch (state) {
                case 0:
                    prefixStr.append(ch);
                    break;
                case 1:
                    indexStr.append(ch);
                    break;
                case 2:
                    dateYrStr.append(ch);
                    break;
                case 3:
                    if (ch == '.') {
                        //make backwardly compatible with the old file name format, so that Reader tasks can read earliest files.
                        state = 7;
                        dateTimeStr = dateMonthStr;
                        if(log.isDebugEnabled()){
                            log.debug("Parsing txLog FileName ["+fileName+"]. The file follows old naming format.");
                        }
                        break;
                    }
                    dateMonthStr.append(ch);
                    break;
                case 4:
                    dateDayStr.append(ch);
                    break;
                case 5:
                    dateHrStr.append(ch);
                    break;
                case 6:
                    if (ch == '.') {
                        state++;
                        break;
                    }
                    dateTimeStr.append(ch);
            }
        }
        if (state != 7) {
            log.error("Error in Parsing txLog FileName [" + fileName + "]. State  = [" + state + "]. But it should be [7].");
            return false;
        }

        try {
            components.first = prefixStr.toString();
            components.second = Integer.parseInt(indexStr.toString());
            components.third = new Date(Long.parseLong(dateTimeStr.toString()));
        }
        catch (Exception e) {
            log.warn("Error parsing filename: [" + fileName + "]. Error message is: " + e.getMessage(), e);
            return false;
        }

        return true;
    }

    public void cleanupHotDir() throws IOException {
        cleanupDir(hotdir);

    }

    public void cleanupColdDir() throws IOException {
        cleanupDir(colddir);
    }

    private void cleanupDir(String dirname) throws IOException {
        File theDir = setupDir(dirname);
        File[] files = getFileList(theDir,null);
        for (File f : files) {
            f.delete();
        }
    }

    @SuppressWarnings("serial")
    public static class TransactionLogException extends Exception {

        public TransactionLogException(String message) {
            super(message);
        }

        public TransactionLogException(String message, Throwable cause) {
            super(message, cause);
            // TODO Auto-generated constructor stub
        }
    }    

    public void moveToCold(File txFile) throws IOException {
        File colddirFile = setupDir(this.colddir);
        StringBuilder target = new StringBuilder();
        target.append(colddirFile.getAbsolutePath()).append(File.separator).append(txFile.getName());
        File dest = new File(target.toString());

        if (txFile.renameTo(dest)) {
            log.info("Moved txFile [" + txFile.getAbsolutePath() + "] to cold [" + dest.getAbsolutePath() + "]");
        } else {
            log
                    .error("There was a problem moving txFile [" + txFile.getAbsolutePath() + "] to cold [" + dest.getAbsolutePath() + "]. Please check the file system to ensure there is enough space in cold-dir. You can manually move these files to cold dir.");
        }
    }

    @SuppressWarnings("serial")
    public static class TransactionLogSetupException extends TransactionLogException {

        public TransactionLogSetupException(String message) {
            super(message);
        }
    }

    public static class TxLogFilenameFilter implements FilenameFilter {
        private String prefix;
        private String ext;

        public TxLogFilenameFilter(String prefix, String extension) {
            super();
            this.prefix = prefix;
            this.ext = "." + extension;
        }

        public boolean accept(File dir, String name) {
            return (name.startsWith(prefix) && name.endsWith(ext));
        }
    }

    public DBService getDbService() {
        return this.dbService;
    }

    public MetaFileManager getMetaFileManager() {
        return metaFileMgr;
    }

    public void pauseReaderTasks() {
        for (Tuple<Thread, TransactionLogReaderTask> task : readerTasks) {
            TransactionLogReaderTask readerTask = task.second;
            readerTask.setPaused(true);
        }
    }

    public void resumeReaderTasks() {
        for (Tuple<Thread, TransactionLogReaderTask> task : readerTasks) {
            TransactionLogReaderTask readerTask = task.second;
            readerTask.setPaused(false);
        }
    }

    public enum State {
        STARTED, STARTUP, PAUSED, SHUTDOWN , RUNNING, IDLE;
    }

    public State getState() {
        return TXNServerStateHolderC.getInstance(dbService.getPeerVirtualServer()).getState().getTxLogState();
    }

    public State getReaderTaskState() {
        return TXNServerStateHolderC.getInstance(dbService.getPeerVirtualServer()).getState().getReaderTaskState();
    }
}
