package com.integral.dbservice.txlog;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.CoderResult;

import com.integral.dbservice.TransactionLogMessage;
import com.integral.is.common.mbean.ISFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * This class is responsible for parsing transaction log messages from a log
 * file. The {@link read()} method will return the next log message, if
 * available. It will return null, if it was unable to read a fully-formed
 * message or it reached {@link #isEof() EOF}. Reaching EOF does not necessarily
 * mean no more transaction logs will be available in future. It just means that
 * <bold>currently</bold> no more data is available. The client should wait a
 * while and try reading again.
 * 
 * <p>
 * {@link #closeChannel()} will close the underlying channel, and release the
 * reference to the log file that should have been set earlier using
 * {@link #setTxLogFile(File)}.
 * <p>
 * {@link #openChannel()} should be preceded by {@link #setTxLogFile(File)}. It
 * will close the channel, if it was opened earlier.
 * 
 * <AUTHOR>
 * 
 */
public class TransactionLogParser {
	private static Log log = LogFactory.getLog(TransactionLogParser.class);

	private static Charset UTF8Charset = Charset.forName("UTF-8");

    private File txFile;
	private FileChannel txIn;
	private final ByteBuffer readBuffer;
	private ParsingState state = ParsingState.initial;
	private final CharBuffer charBuff;

	private CharsetDecoder charDecoder = UTF8Charset.newDecoder();

	// number of bytes read during the previous read operation.
	private int numBytesRead = 0;
	
	public TransactionLogParser(File txFile, int readCapacity) {
        super();
        if(readCapacity < 3){
            throw new IllegalArgumentException("readCapacity should be at least three bytes for reading and decoding unicode characters.");//Should be atleast two as we need to have all bytes in buffer to perform decode operation.
        }
		this.txFile = txFile;
        readBuffer = ByteBuffer.allocateDirect(readCapacity);
        charBuff = CharBuffer.allocate(readCapacity);
        charBuff.flip();
	}

	public void setup() throws IOException {
		openChannel();
	}

	public boolean isEof() {
		// We reach eof when there are no more bytes to be read.
		return numBytesRead == -1;
	}

	/**
	 * Set the physical transaction log file, that this reader is reading from.
	 * 
	 * @param txFile
	 * @throws IOException
	 */
	public void setTxLogFile(File txFile) throws IOException {
		closeChannel();
		this.txFile = txFile;
		openChannel();
	}

	/**
	 * close the channel, and release reference to {@link #setTxLogFile(File)
	 * transaction log file}.
	 */
	public void closeChannel() {
        charBuff.position(charBuff.limit()); //clear the buffer so that read doesn't return stale data possibly from previous file.
		if (txIn == null) {
			return;
		}
		try {
			txIn.close();
		} catch (IOException e) {
			log.error("Exception when closing channel: " + e.getMessage(), e);
		} finally {
			txIn = null;
			txFile = null;
		}
	}

	public void openChannel() throws IOException {
		if (txIn != null) {
			closeChannel();
		}
		txIn = new FileInputStream(txFile).getChannel();
		parseSeekPtr=0;
		setToInitialState();
	}

	public void seekTo(long seekPtr)
			throws IOException {
		if (txIn == null) {
			throw new NullPointerException(
					"Cannot perform seekTo on a Null channel.");
		}

		txIn.position(seekPtr);
		parseSeekPtr = seekPtr;
	}

	private void readNextBuff() throws IOException {
        assert charBuff.remaining() == 0;
		numBytesRead = txIn.read(readBuffer);
		if (numBytesRead > 0) {
			// get the buffer ready to be read from by the decoder.
			readBuffer.flip();
            charBuff.clear(); //since we have read everything written so far in character buffer, clear it before reading more from file.
			CoderResult result = charDecoder.decode(readBuffer, charBuff, false);
            if(result.isError() || result.isOverflow()){
                IOException exception;
                if(result.isUnmappable()){
                    exception = new IOException("UnMappable character found while parsing txl logs:"+charBuff.toString());
                }else if(result.isMalformed()){
                    exception = new IOException("Malformed exception found while parsing txl logs:"+charBuff.toString());
                }else {
                    exception = new IOException("Internal error encountered while parsing txl logs:"+charBuff.toString());
                }
                throw exception;
            }
            // get the buffer ready to be read.
            charBuff.flip();
            if(readBuffer.hasRemaining()){
                //Complete bytes could not be encoded. We may have a multi-byte character here. Need more input to decode this character.
                ByteBuffer remaining = readBuffer.slice();
                readBuffer.clear();
                readBuffer.put(remaining); //retain the remaining at the start of the read buffer and read the remaining input during next read call.
            }else{
                // get the buffer ready to be written to again.
                readBuffer.clear();
            }
		}
	}

	private StringBuilder txIdStr = new StringBuilder();
	private long txId = 0;
	private StringBuilder corelationId = new StringBuilder();
	private StringBuilder event = new StringBuilder();
	private StringBuilder bodySizeStr = new StringBuilder();
	private StringBuffer parsedBody = new StringBuffer();
    private StringBuilder createdDateTimeStr = new StringBuilder();
    private StringBuilder seekPtrStr = new StringBuilder();
	private long parseSeekPtr=0;
	private long currTxSeekPosition=0;
	
	private int bodySize = 0;
	private long createdDateTime = 0;

	/**
	 * Prepare the reader to start reading the next transaction log from the
	 * channel.
	 * @throws IOException 
	 */
	private void setToInitialState() throws IOException {
		txIdStr = new StringBuilder();
		txId = 0;
		corelationId = new StringBuilder();
		event = new StringBuilder();
		bodySizeStr = new StringBuilder();
		parsedBody = new StringBuffer(4 * 1024);
		event = new StringBuilder();
		createdDateTimeStr = new StringBuilder();
		// This is the first entry in a transaction log.
		state = ParsingState.readingTxId;
		// At this point we are at the end of a record, or begining of the file.
		// Therefore, set the currTxSeekPosition to the next char. Num Chars don't directly translate to bytes if unicode special chars show up.
        //Use file pointer instead.
        seekPtrStr = new StringBuilder();
		currTxSeekPosition = parseSeekPtr;
	}

	public TransactionLogMessage read() throws IOException {

		char ch;

		// txIdStr | corelationId | event | createdDate | sizeOfSerializedData | data
		// (suffixed with CRLF)

		while (true) {
			if (!charBuff.hasRemaining()) {
				readNextBuff();
				if (numBytesRead <= 0) {
					return null;
				}
                if (!charBuff.hasRemaining()) { //all bytes read may be part of a multi-byte character, a full character may not have been read yet.
                    continue;
                }
			}

			ch = charBuff.get();
			parseSeekPtr++;
			
			switch (state) {

			case readingTxId:
				if ('|' == ch) {
					state = ParsingState.readingCorelationId;
					try {
						txId = Long.parseLong(txIdStr.toString());
					} catch (Exception e) {
						txId = -1;
						state = ParsingState.errorReadingToCRLF;
					}
				} else
					txIdStr.append(ch);
				break;

			case readingCorelationId:
				if ('|' == ch) {
					state = ParsingState.readingEvent;
				} else
					corelationId.append(ch);
				break;

			case readingEvent:
				if ('|' == ch) {
					state = ParsingState.readingCreatedTime;
				} else
					event.append(ch);
				break;

            case readingCreatedTime:
                if ('|' == ch) {
                    state = ParsingState.readingSize;
                    try {
                        createdDateTime = Long.parseLong(createdDateTimeStr.toString());
                    } catch (Exception e) {
                        createdDateTime = 0;
                    }
                } else
                    createdDateTimeStr.append(ch);
                break;

			case readingSize:
				if ('|' == ch) {
					state = ParsingState.readingSeekPtr;
					try {
						bodySize = Integer.parseInt(bodySizeStr.toString());
						if (bodySize == 0) {
							state = ParsingState.done;
						}
					} catch (Exception e) {
						bodySize = -1;
						state = ParsingState.errorReadingToCRLF;
					}
				} else
                    bodySizeStr.append(ch);
                break;

            case readingSeekPtr:
                if ('|' == ch) {
                    state = ParsingState.readingBody;
                    try {
                        currTxSeekPosition = Long.parseLong(seekPtrStr.toString());
                    } catch (Exception e) {
                        state = ParsingState.errorReadingToCRLF;
                    }
                } else
                    seekPtrStr.append(ch);
                break;

            case readingBody:
                parsedBody.append(ch);
				if (parsedBody.length() == bodySize) {
					state = ParsingState.done;
					TransactionLogMessage tlm = new TransactionLogMessage();
					tlm.corelationId = corelationId.toString();
					tlm.entityText = parsedBody;
					tlm.transactionId = txId;
					tlm.eventName = event.toString();
					tlm.fileName = txFile.getName();
					tlm.seekPtr = currTxSeekPosition;
					tlm.creationTime = createdDateTime;
                    tlm.addEventTime(TransactionLogMessage.TLM_EVENT_MESSAGE_READ, System.currentTimeMillis());
					setToInitialState();
					return tlm;
				}
				break;

			case errorReadingToCRLF:
				if ('\n' == ch) {
					if (parsedBody.length() > 0) {
						char lastCh = parsedBody
								.charAt(parsedBody.length() - 1);
						if ('\r' == lastCh) {
							// We have a CRLF!!!
							// Let's loop back the state machine, and try to
							// read a new transaction log entry.
                            log.warn("Skipped reading one record from file:"+txFile.getName()+", TxId:"+txId);
							setToInitialState();
						}
					}
				}
				break;
			}
		}

	}

	private static enum ParsingState {
		initial, readingTxId, readingCorelationId, readingEvent, readingSize, readingCreatedTime , readingSeekPtr, readingBody,
		// Error encountered during parsing - I will just try and read to CRLF.
		errorReadingToCRLF, done
	}
}
