package com.integral.dbservice.txlog;

import com.integral.dbservice.TransactionLogMessage;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;

public class CheckpointTransactionLogProcessor extends Processor {

	TransactionLog txLogSystem;
	
	
	public CheckpointTransactionLogProcessor(TransactionLog txLogSystem) {
		super();
		this.txLogSystem = txLogSystem;
	}


	@Override
	public PipelineMessage process(PipelineMessage msg) {
		TransactionLogMessage tlm = (TransactionLogMessage) msg;
		int txLogIndex = tlm.getCorelationIndex(txLogSystem.getDbService().getConfig().getNumPersistenceThreads());		
		txLogSystem.updateMetaInformation(txLogIndex, tlm);
		return msg;
	}

}
