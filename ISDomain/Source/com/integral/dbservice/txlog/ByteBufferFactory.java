package com.integral.dbservice.txlog;

import com.integral.pool.PoolFactory;

import java.nio.ByteBuffer;



public class ByteBufferFactory implements PoolFactory<ByteBuffer>
{

    final boolean direct;

    public int getBufferSize() {
        return bufferSize;
    }

    public void setBufferSize(int bufferSize) {
        this.bufferSize = bufferSize;
    }

    int bufferSize=4096;
	
	public ByteBufferFactory(int bufferSize) {
        this(bufferSize, true);
    }

    public ByteBufferFactory(int bufferSize, boolean direct) {
		super();
        this.direct = direct;
        this.bufferSize = bufferSize;
	}

	public ByteBuffer make() {
		return (direct ? ByteBuffer.allocateDirect(bufferSize) :
                ByteBuffer.allocate(bufferSize));
	}

	public void recycle(ByteBuffer pooled) {
		pooled.clear();	
	}

}
