package com.integral.dbservice.txlog;

import com.integral.dbservice.DBService;
import com.integral.dbservice.TXNServerState;
import com.integral.dbservice.TXNServerStateHolderC;
import com.integral.dbservice.TransactionLogMessage;
import com.integral.dbservice.remote.MultiTenantRemoteTxServerClient;
import com.integral.dbservice.remote.MultiTenantRemoteTxnServerUtils;
import com.integral.dbservice.txlog.TransactionLog.State;
import com.integral.dbservice.txlog.TransactionLog.When;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Persistence;
import com.integral.persistence.PersistenceFactory;
import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.queue.QueueManager;
import com.integral.system.server.VirtualServer;
import com.integral.system.server.VirtualServerC;
import com.integral.system.server.VirtualServerType;
import com.integral.system.server.VirtualServerTypeC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;
import org.apache.commons.lang.StringUtils;

import javax.management.*;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * This is an asynchronous task, which reads from the transaction log using
 * {@link TransactionLogParser}, and feeds the read messages into queues for
 * processing by downstream {@link Processor processors}.
 *
 * <AUTHOR>
 *
 */
public class TransactionLogReaderTask implements Runnable
{
    private static Log log = LogFactory.getLog( TransactionLogReaderTask.class );
    private static final int TX_SEND_INTERVAL = 500; //expressed in milliseconds

    private boolean isShutdown = false;
    private final TransactionLogParser txLogParser;
    private final TransactionLog txLogSystem;
    private final Lock instanceLock = new ReentrantLock();
    private final Condition interruptedCondition = instanceLock.newCondition();
    private final int index;
    private final DBService dbService;
    private final MultiTenantRemoteTxServerClient remoteTxServerClient;
    private AtomicBoolean checkForRolledOverFile = new AtomicBoolean(true);
    private long lastRemoteSent;

    /**
     This flag tells the reader task to exit automatically once an end of file is reached, and there are no more files to be read.
     This is done in cases when we are trying to read transaction logs for indexes that are no longer in use - i.e. you just shrank the number of
     threads for DBService.
     */
    private boolean autoExit = false;

    // Whenever you get a new txFile, ensure that you also set it on the txLogParser!
    private File txFile;

    // This is the queue that the "PersistToDB" pipeline will be listening to.
    private LinkedBlockingQueue<PipelineMessage> txLogQueue;
    private final ISPersistenceMBean config;

    // private State state;

    public TransactionLogReaderTask( DBService dbSvc, int index )
    {
        this.dbService = dbSvc;
        this.txLogSystem = dbSvc.getTransactionLog();
        this.index = index;
        config = dbService.getConfig();
		txLogParser = new TransactionLogParser(null, config.getTransactionLogReaderBufferCapacity());
        VirtualServer txnServerVs = null;
        if (config.isMultiTenantRemoteTxnServerEnabled()) {
            VirtualServer vs = ISFactory.getInstance().getISMBean().getVirtualServer();
            txnServerVs = vs.getRemoteTxnServer();
            if (txnServerVs == null && config.isAutoSelectRemoteTxnServerEnabled()) {
                txnServerVs = MultiTenantRemoteTxnServerUtils.selectNewTxnServer(vs);
            }
        }
        if (txnServerVs != null) {
            String remoteURL = "http://" + txnServerVs.getIpAddress();
            log.info("Setting up MultiTenantRemoteTxServerClient as found remoteURL set to " + remoteURL + ", TxnServerVS:" + txnServerVs.getName());
            remoteTxServerClient = new MultiTenantRemoteTxServerClient(txLogSystem, remoteURL, index);
            lastRemoteSent = System.currentTimeMillis();
        } else {
            remoteTxServerClient = null;
        }
    }

    public void setup() throws IOException
    {
        //txFile could have been set before setup() is called by making this reader task seekTo() some file. This is done when recovering in error scenarios.
        if ( this.txFile == null ) {
            getEarliestTxLogFile();
            txLogParser.setTxLogFile( txFile );
        }

        String name = "";
        try {
            name = this.dbService.getQueueNameForIndex( index );
            txLogQueue = QueueManager.instance().getQueue( name );
            log.info( "TransactionLogReaderTask [" + index + "," + dbService.getPeerVirtualServer() + "] will be reading from queue [" + name + "]" );
        }
        catch ( IllegalAccessException e ) {
            String msg = "Unable to get queue [" + name + "] for index [" + index + "]. This implies something really bad has happened in the system.";
            log.error( msg );
            throw new PipelineException( msg, e );
        }
        checkForRolledOverFile.set(true); //setting roll over check to true during setup.
    }

    /**
     * Make this reader task point to the specified transaction log file name, at the location specified by <code>seekPtr</code>.
     * @param txFileName
     * @param seekPtr
     * @param txId
     * @param expectedTxId
     */
    public boolean seekTo( String txFileName, long seekPtr, long expectedTxId )
    {
        log.debug("seekTo for txFileName: " + txFileName + " to seekPtr: "+ seekPtr + " with expectedTxId: " + expectedTxId);

        instanceLock.lock();
        try {
            File f = new File( txFileName );
            if ( f.exists() ) {
                txLogParser.closeChannel();
                txFile = f;

                txLogParser.setTxLogFile( txFile );
                txLogParser.seekTo( seekPtr );

                //TxIds are zeros only for new files.
                if ( expectedTxId == 0 ) {
                    return true;
                }

                //Read the record at this seekPtr. Since this record was processed, we do not have to process it again.
                //Seek pointers are stored for records that are processed, so we have to go to the next record (i.e. ++).
                TransactionLogMessage tlm = txLogParser.read();
                if ( tlm != null ) {
                    //We should verify that the transactionId we are trying to locate actually exists!
                    if ( tlm.transactionId == expectedTxId ) {
                        log.info( "Succesful seek to txFile [" + txFileName + "], seekPtr [" + seekPtr + "], txId [" + expectedTxId + "]" );
                        return true;
                    }
                    else {
                        StringBuilder builder = new StringBuilder();
                        builder.append( "Expected TransactionId NOT found at seek location [" ).append( seekPtr ).append( "] in file [" ).append(
                                this.txFile.getName() ).append( "] Excepted[" ).append( expectedTxId ).append( "] <> Actual [" ).append( tlm.transactionId )
                                .append( "]" );
                        log.error( builder.toString() );

                    }
                }
                log.warn( "Invalid seekPtr [" + seekPtr + "] for file [" + this.txFile.getName() + "]" );
            }
        }
        catch ( IOException e ) {
            log.error( "There was an error when trying to seek to txFile [" + txFileName + "], seekPtr [" + seekPtr + "], txId [" + expectedTxId + "]", e );
        }
        finally {
            checkForRolledOverFile.set(true); //set the check for rollover test here as we may have reset to a previous file.
            instanceLock.unlock();
        }

        this.txFile = null;
        this.txLogParser.closeChannel();
        return false;

    }

    /**
     * Return the earliest transaction log file for an index. Create the file,
     * if one did not exist. Filenames are of the format:
     * {@link DBService#PERSIST_Q_PREFIX &lt;Prefix&gt;}
     * -&lt;index&gt;-&lt;creationTimeInMillis&gt;.{@link #TX_LOG_SUFFIX
     * &lt;suffix&gt;}.
     */
    public void getEarliestTxLogFile() throws IOException
    {
        instanceLock.lock();
        try
        {
            this.txFile = txLogSystem.getEarliestOrLatestTxLogFile( index, When.Earliest );
        }
        finally
        {
            instanceLock.unlock();
        }
        log.info( "Earliest TxLogFile for index: [" + index + "] is: [" + this.txFile.getAbsolutePath() + "]" );
    }

    public void shutdown()
    {
        isShutdown = true;
        if(remoteTxServerClient != null){
            remoteTxServerClient.shutDown();
        }
        instanceLock.lock();
        try {
            interruptedCondition.signal();
        }
        finally {
            instanceLock.unlock();
        }
    }

    private TransactionLogMessage readNextRecord() throws IOException
    {
        return txLogParser.read();
    }

    private boolean testForNewerTransactionLog() throws IOException
    {
        File newTxFile = null;
        // check if there is a newer transaction file.
        try{
            newTxFile = txLogSystem.getLaterTxLogFile( txFile, index );
        } catch(Exception ex){
            log.error("Error received while testing for new transaction log. Unable to find new transaction Log. Moving on.",ex);
        }

        return (newTxFile != null);
    }

    private boolean scanForNewerTransactionLog() throws IOException
    {
        File newTxFile = null;
        // check if there is a newer transaction file.
        try{
            newTxFile = txLogSystem.getLaterTxLogFile( txFile, index );
        } catch(Exception ex){
            log.error("Error received while scanning new transaction log. Unable to find new transaction Log. Moving on.",ex);
        }

        if ( newTxFile == null ) {
            // nope, let's go back.
            return false;
        }

        // First close the channel,and release the underlying file descriptor.
        // Not doing this will work on Linux, but fail on Windoze.
        txLogParser.closeChannel();

        /*
        The reader task is adding tlm to a queue. It is not aware when those messages actually get committed to db.
        Move a file to cold may create a problem if the messages read from that file are not yet committed and the process
        dies. In that case meta file would indicate that we were reading from that file, but that file is no longer and in hot
        dir and would not be loaded by the reader. The remaining messages would never be retried.
        try {
            // We are done processing this file, so move this to cold.
            txLogSystem.moveToCold( this.txFile );
        }
        catch ( IOException e ) {
            // We have to ignore this exception. The system should keep running
            // even if we could not successfully clean the hot-dir.
            log
                    .warn(
                            "There was a problem while backing up a TxLog [" + txFile.getName() + "] to cold. This error will be ignored for now, but you have to verify what's happening.\n The exception was: " + e
                                    .getMessage(), e );
        } */

        // set the parser to start reading from the new file.
        this.txFile = newTxFile;
        txLogParser.setTxLogFile( txFile );
        return true;
    }

    public void run()
    {
        log.info( "Started TransactionLogReaderTask for index: [" + index + "]." );
        try {
            while ( !isShutdown ) {
                TransactionLogMessage tlm = readNextRecord();

                if ( tlm == null ) {
                    //Fix for bug#  47026. In this case one event was skipped by the reader task when the rollover happended.
                    //This was due to the fact that between readNextRecord and scanForNewereTransactionLog() , a record
                    //was written to the old txlog, and also the file rolled by the rollover thread.

                    //1.) Let us ensure we are at eof.
                    if ( checkForRolledOverFile.get() && txLogParser.isEof() ) {

                        //2.) Is a new transaction log available?
                        if (testForNewerTransactionLog()) {

                            //3.) Between steps (1) and (2) above, a new event could have been written to the current txlog file, followed
                            // by a rollover. So, we need to do equivalent of a double-checked locking to ensure
                            // that did not happen.
                            tlm = readNextRecord();

                            //4.) The old file does not have any record any more - we really need to move on to the newer file.
                            if (tlm == null) {
                                File oldTxnFile = txFile;
                                if (scanForNewerTransactionLog()) {
                                    log.info("NEW TxLog found to parse events from: [" + this.txFile.getName() + "]");
                                    //Now that we have moved to new file, see if old file needs to be moved to cold.
                                    if (oldTxnFile.length() == 0) {
                                        //Fix for Bug 47222. Presence of 0Kb files is causing the discovery of new file to take a lot of CPU.
                                        // Discovery function lists and sorts files in hot dir to find the latest txn file and as the number of empty files increase,
                                        // so does the time taken.
                                        log.info("Discovered an empty file during Read Operation:" + oldTxnFile.getName() + ". Moving it to cold dir.");
                                        txLogSystem.moveToCold(oldTxnFile);
                                    }
                                    // A new transaction log was found - so let's start
                                    // reading from it.
                                    continue;
                                } else {
                                    if (autoExit == true) {
                                        log.info("AUTOEXIT TransactionLogReader for index: [" + index + "].");
                                        isShutdown = true;
                                        break;
                                    }
                                }

                            }
                        } else {
                            // 2.1.) Since the check failed to find a new file, it means we have reached the file that writer is
                            //writing to. At this point, we should wait for the roll over to notify us when a new file is created.
                            checkForRolledOverFile.set(false);
                            //Check once more As between step 2 and Step 2.1, writer may have rolled over.
                            if (testForNewerTransactionLog()) {
                                checkForRolledOverFile.set(true);
                            } else {
                                log.info("TransactionLogReaderTask waiting for Roll Over Notification for Index:" + index);
                            }
                        }
                    }

                    if (tlm == null) {
                        //All events are processed
                        TXNServerState state = TXNServerStateHolderC.getInstance(dbService.getPeerVirtualServer()).getState();
                        if (state.getReaderTaskState( index ) == State.STARTUP && txLogQueue.isEmpty()) {
                            state.setReaderTaskState( index,  State.STARTED ) ;
                        }
                        //ping remote server if one is configured
                        if(remoteTxServerClient != null && ((System.currentTimeMillis() - lastRemoteSent) > config.getRemoteTXNServerPingInterval())){
                            remoteTxServerClient.pingRemoteTxnServer();
                            lastRemoteSent = System.currentTimeMillis();
                        }
                        try {
                            instanceLock.lock();
                            // sleep, and see if another record is available.
                            interruptedCondition.await( 100, TimeUnit.MILLISECONDS );
                        }
                        catch ( InterruptedException e ){}

                        finally {
                            instanceLock.unlock();
                        }

                        continue;
                    }
                }

                if (tlm != null) {
                    int counter = 0;
                    //  while(state == state.STARTUP || state == State.PAUSED)
                    TXNServerState state = TXNServerStateHolderC.getInstance(dbService.getPeerVirtualServer()).getState();
                    if ( state.getReaderTaskState( index ) == State.PAUSED ) {
                        while ( state.getReaderTaskState( index ) == State.PAUSED ) {
                            if ( counter % 100 == 0 ) {
                                log
                                        .info( "TransactionLog ReaderTask is in PAUSED state. This could be because Transaction Server is still in startup or it's manually paused." );
                            }
                            counter++;
                            try {
                                instanceLock.lock();
                                // sleep, and see if another record is available.
                                interruptedCondition.await( 100, TimeUnit.MILLISECONDS );
                            }
                            catch ( InterruptedException e ){}

                            finally {
                                instanceLock.unlock();
                            }
                        }
                    }

                    boolean addedToQueue = false;
                    do {
                        tlm.index = index; //set the index here as index is not set with tlm read from the file.
                        //We will try and add this tlm to the queue, until we can succeed.
                        //The reason this is timed is so that we do not miss the shutdown event, if that were to happen.
                        if(remoteTxServerClient == null){
                            addedToQueue = txLogQueue.offer(tlm, TX_SEND_INTERVAL, TimeUnit.MILLISECONDS);
                            if(addedToQueue){
                                tlm.addEventTime(TransactionLogMessage.TLM_EVENT_MESSAGE_QUEUED, System.currentTimeMillis());
                            }
                        }else{
                            //send to remote server.
                            remoteTxServerClient.sendTransactionMessage(tlm);
                            lastRemoteSent = System.currentTimeMillis();
                            addedToQueue = true;
                        }
                    } while (!isShutdown && !addedToQueue);
                }
            }
            //shutdown here, close file channels. LogParser is still holding the file channel open.
            txLogParser.closeChannel();
        }
        catch ( Exception e )
		{
			log.error("There was an exception when trying to read transaction logs for index: [" + index + "]. The exception mesage is: " + e.getMessage(), e);
			isShutdown = true;
			StringBuilder sb = new StringBuilder(50).append("index=").append(index).append(" FileName=").append(txFile != null ? txFile.getName() : "");
			MessageLogger.getInstance().log(ISAlertMBean.ALERT_ACTION_TRANSACTION_SERVER_READ_FAILED, this.getClass().getName(), "TX Log read failed: " + e.getMessage(), sb.toString());
		}

        log.info( "TransactionLogReaderTask for index [" + index + "] is SHUTDOWN." );
    }

    public void notifyRolledOver() {
        checkForRolledOverFile.set(true);
        log.info("TransactionLogReaderTask.notifyRolledOver() called for index:"+index);
    }


    /**
     * @see #autoExit
     * @param b
     */
    public void setAutoExit( boolean b )
    {
        this.autoExit = b;
    }

    /**
     * @see #paused
     * @param b
     */
    public void setPaused( boolean b )
    {
        if ( b ) {
            TXNServerStateHolderC.getInstance(dbService.getPeerVirtualServer()).getState().setReaderTaskState( index , State.PAUSED );
        }
        else {
            TXNServerStateHolderC.getInstance(dbService.getPeerVirtualServer()).getState().setReaderTaskState( index , State.STARTED );
        }
    }

    /**
     * @see #startup
     * @param b
     */
    public void setStartup( boolean b )
    {
        if ( b ) {
            TXNServerStateHolderC.getInstance(dbService.getPeerVirtualServer()).getState().setReaderTaskState( index , State.STARTUP );
        }
        else {
            TXNServerStateHolderC.getInstance(dbService.getPeerVirtualServer()).getState().setReaderTaskState( index , State.STARTED );
        }
    }

    public void changeTxnServer(String remoteURL) {
        log.warn("TransactionLogReaderTask.changeTxnServer " + dbService.getPeerVirtualServer() +":"+ index + ": Received change Txn Server notification. New txn Server URL : "+remoteURL);
        if(remoteTxServerClient != null){
            try{
                this.remoteTxServerClient.setNewRemoteURL(remoteURL);
            }
            catch(Exception e){
                log.error("TransactionLogReaderTask.changeTxnServer " + dbService.getPeerVirtualServer() +":"+ index + ":: Error in handling notification.", e);
            }
        }
    }
}
