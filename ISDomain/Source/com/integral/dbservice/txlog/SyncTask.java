package com.integral.dbservice.txlog;

import com.integral.dbservice.DBService;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Aug 27, 2010
 * Time: 3:03:51 PM
 * Sync task is an async task to sync Transaction logs to physical files on the disk. Since sync is a costly operation,
 * it is not performed in the application thread. Instead we sync files to disk at regular intervals.
 */
public class SyncTask implements Runnable{
    private static Log log = LogFactory.getLog(SyncTask.class);

    final DBService dbSvc;

    public SyncTask(DBService dbSvc) {
       this.dbSvc = dbSvc;
    }

    public void run() {
        try {
            this.dbSvc.getTransactionLog().syncToFile();
        } catch (Exception e) {
            log.error("There was a problem during Sync due to: " + e.getMessage(), e);
        }
    }
}
