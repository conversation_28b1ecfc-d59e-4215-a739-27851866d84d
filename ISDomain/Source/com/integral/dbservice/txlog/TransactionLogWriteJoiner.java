package com.integral.dbservice.txlog;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

public class TransactionLogWriteJoiner {

	AtomicInteger counter = new AtomicInteger(0);
	ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
	Lock readLock = lock.readLock();
	Lock writeLock = lock.writeLock();
	
	boolean chooseCoordinator() {
		int counterVal = 0;
		do {
			counterVal = counter.get();
		}while (!counter.compareAndSet(counterVal, counterVal + 1));
		
		return counterVal == 0;
	}
	
	//Enter a window of opportunity to do this transaction with other threads.
	void enterGate() {
		readLock.lock();
	}
	
	//Close the window of opportunity - everybody now has to wait.
	void closeGate() {
		readLock.unlock();
		writeLock.lock();
	}
	
	void leaveAsCoordinator() {
		counter.set(0);
		writeLock.unlock();
	}
	
	void leave() {
		readLock.unlock();
	}
	
	void waitForCoordinatorToLeave() {
		while (counter.get() != 0)
			try {
				Thread.sleep(1);
			} catch (InterruptedException e) {
				
			}
	}
	
}
