package com.integral.dbservice.txlog;

import com.integral.dbservice.DBService;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * This is an async task that is responsible for rolling the transaction log files at regular intervals.
 * Author: inder
 */
public class RolloverTask implements Runnable {

    private static Log log = LogFactory.getLog(RolloverTask.class);

    final DBService dbSvc;

    public RolloverTask(DBService dbSvc) {
       this.dbSvc = dbSvc;
    }

    public void run() {
        log.info("Starting ROLLOVER.");
        try {
            this.dbSvc.getTransactionLog().rollover();
            this.dbSvc.getTransactionLog().moveOverToCold();
        } catch (Exception e) {
            log.error("There was a problem during Rollover due to: " + e.getMessage(), e);
        }
        log.info("Rollover DONE.");
    }


}
