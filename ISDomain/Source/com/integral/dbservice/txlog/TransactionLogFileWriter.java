package com.integral.dbservice.txlog;

import com.integral.dbservice.DBService;
import com.integral.dbservice.TransactionLogMessage;
import com.integral.dbservice.txlog.TransactionLog.When;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class TransactionLogFileWriter
{
    private static Log log = LogFactory.getLog( TransactionLogFileWriter.class );

    private final AtomicLong transactionId = new AtomicLong();
    private boolean isDirty; //no need to synchronize as this is accessed within instance lock.
    private final Lock instanceLock = new ReentrantLock();

    private final TransactionLog txSystem;
    private final int index;

    private File txFile;

    private FileChannel txOut;

    final private ByteBuffer writeBuffer;

    private long prevSyncWaitIntervals; //not synchronized as accessed by Sync Task that is running with a single threaded scheduler. If the task is run in a pool of more threads in future, synchronize this.
    private long curSyncWaitIntervals; //same comments as prevSyncWaitIntervals.
    private final long maxSyncWaitInterval;
    private final long syncTimeInterval;

    public TransactionLogFileWriter( TransactionLog txSystem, int index, int bufferSizeInBytes, ISPersistenceMBean config)
    {
        super();
        this.index = index;
        this.txSystem = txSystem;
        writeBuffer = ByteBuffer.allocateDirect( bufferSizeInBytes );
        ISPersistenceMBean mbean = config;
        maxSyncWaitInterval = mbean.getMaxTxLogSyncWaitTimeInSeconds();
        syncTimeInterval = mbean.getTxLogSyncIntervalInSeconds();
        prevSyncWaitIntervals = syncTimeInterval;
        curSyncWaitIntervals = prevSyncWaitIntervals;
    }

    public TransactionLogFileWriter( TransactionLog txSystem, int index, ISPersistenceMBean config )
    {
        this( txSystem, index, 4 * 1024, config);
    }

    public void setup() throws IOException
    {
        //At startup time, always rollover to a new file.
        //There could be issues with the old file - just disregard it. Reader tasks may look at the old files,
        //but we want to start with a clean slate.

        //Also, fixes 45074. In this case, the virtual server name of the VM was changed between restarts. So, no
        //META-INFO was found for the new virtual server. This resulted
        //in the reader task going to the latest file that it could find on the disk. Since the latest file already had
        //old records, it replayed the old txlog on the database - resulting in errors. This should never have happened
        //if this object would have rolled over to a new file at startup.
        rollover();
//        setLatestTxLogFile();

    }

    public void rollover() throws IOException
    {
        instanceLock.lock();
        try
        {
            // Create a brand new tx logging file.
            txSystem.createNewTxLog( index );
            // Switch to the latest file.
            setLatestTxLogFile();
        }
        finally
        {
            instanceLock.unlock();
        }
    }

    public void onDemandRollover(String fileName) throws IOException {
        File inputFile = new File(fileName);
        if (inputFile.exists()) {
            FileChannel ic = null;
            FileChannel oc = null;
            instanceLock.lock();
            try {
                //copy the file to hot directory
                File dropInfile = txSystem.createNewTxLog(index);
                ic = new FileInputStream(inputFile).getChannel();
                oc = new FileOutputStream(dropInfile).getChannel();
                ic.transferTo(0, ic.size(), oc);
                log.info("Copied drop in file:"+fileName+" to "+dropInfile.getAbsolutePath()+" as part of On Demand RollOver.");
                //rollover the writer task to start writing in a new file.
                try {
                    Thread.sleep(100); //wait a while before rollover so that Time stamp can be incremented for new file.
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
                //rollover();
                // Create a brand new tx logging file.
                txSystem.createNewTxLog(index);
                // Switch to the latest file.
                setLatestTxLogFile();
            } finally {
                if (ic != null) {
                    try {
                        ic.close();
                    } catch (Exception ex) {
                        log.error(ex.getMessage(), ex);
                    }
                }
                if (oc != null) {
                    try {
                        oc.close();
                    } catch (Exception ex) {
                        log.error(ex.getMessage(), ex);
                    }
                }
                instanceLock.unlock();
            }
        } else {
            String message = new StringBuilder("Could not find input file:").append(fileName).append(" for on demand rollover.").toString();
            log.error(message);
            throw new IOException(message);
        }
    }

    /**
     * Return the latest transaction log file for an index. Create the file, if
     * one did not exist. Filenames are of the format:
     * {@link DBService#PERSIST_Q_PREFIX &lt;Prefix&gt;}
     * -&lt;index&gt;-&lt;creationTimeInMillis&gt;.{@link com.integral.dbservice.txlog.TransactionLog#TX_LOG_SUFFIX &lt;suffix&gt;}.
     */
    /* package */void setLatestTxLogFile() throws IOException
    {
        boolean done = false;
        int count = 0;
        do {
            try{
                /*
                    Refer to Bug 45449. This method can throw runtime exception. We want to make sure that the writer moves to the new file
                    as the reader might already have discovered the new file created in the rollover function. Deleting already created file
                    is dangerous because if reader has already moved to the new file and then it is deleted, it would discover the previous
                    file again as latest file and start processing from the start of the file resulting in duplicate transactions. Since the
                    creation of the new file and rolling over to it is decoupled, we want to make sure that the call below succeeds. In case
                    of failure, we retry 5 times in 100 ms gap. During this time the instance lock is held by rollover function that would
                    make sure that no other writes happen to previous file. It may introduce a delay in the main workflow, but considering
                    the frequency of rollover() and chances of the call below failing due to race condition outside VM, we should still retry here.
                 */
                this.txFile = txSystem.getEarliestOrLatestTxLogFile( index, When.Latest );
                done = true;
            }catch(Exception ex){
                if(count > 5){
                    IOException ioException = new IOException(ex.getMessage());
                    ioException.initCause(ex);
                    throw ioException;
                }
                log.error("Encountered exception while retrieving the latest file in TxLogWriter.rollover(). Would Retry after 100ms. Retrying count:" + count, ex);
                count++;
                try{
                    Thread.sleep(100);
                }catch(Exception ex1){
                    //Ignore    
                }
            }
        } while(!done);

        //close previous stream
        closeWriter();

        // true=append
        this.txOut = new FileOutputStream( txFile, true ).getChannel();
        // reset the transaction id to the start - this may change in future.
        transactionId.set( 0 );

        log.info( "Latest TxLogFile for index: [" + index + "] is: [" + this.txFile.getAbsolutePath() + "]" );
    }

    public long getCurrentTransactionId()
    {
        return transactionId.get();
    }

    /**
     * Write requests can be from multiple threads - therefore, this method
     * should be thread-safe.
     * 
     * @param message
     * @throws IOException
     */
    public void write( TransactionLogMessage message ) throws IOException
    {
        instanceLock.lock();

        try
        {
            // Append CRLF to serialized data. We do this to ensure all
            // transactions are nicely separated,
            // and readable.

            message.seekPtr = txOut.position();

            // clear the buffer, and clear all existing pointers.
            writeBuffer.clear();

            ArrayList<ByteBuffer> encodedBuffers = message.entityAsBytes.getEncodedBuffers();

            // increment and get the next transaction id - this is our internal id,
            // and not the corelationId.
            long nextTxId = transactionId.incrementAndGet();
            message.transactionId = nextTxId;

            // get the headers into the buffer.
            message.writeHeaders( writeBuffer );
            writeBuffer.flip();
            // write the headers to the file, and flip it.
            txOut.write( writeBuffer );

            for ( int i = 0; i < encodedBuffers.size(); i++ )
            {
                ByteBuffer bb = encodedBuffers.get( i );
                while ( bb.hasRemaining() )
                {
                    txOut.write( bb );
                }
            }
            isDirty = true;
            // non-blocking IO.
            //this.txOut.force(false);

        }
        finally
        {
            message.entityAsBytes.reinitialize();
            instanceLock.unlock();
        }
    }

    /**
     * The function tries to sync the transaction logs in a non-intrusive manner.
     * @return true if able to sync, false otherwise
     */
    protected boolean trySyncToFile(){
        boolean result = false;
        curSyncWaitIntervals -= syncTimeInterval;
        if(curSyncWaitIntervals <= 0){
            result = trySyncToFileImpl();
            if(result == false){
                prevSyncWaitIntervals = prevSyncWaitIntervals*2;
                if(prevSyncWaitIntervals > maxSyncWaitInterval){
                    prevSyncWaitIntervals = maxSyncWaitInterval;
                }                
            }else{
                //reset to the start. We are not backing off slowly, but instead resetting the counter.
                prevSyncWaitIntervals = syncTimeInterval;
            }
            curSyncWaitIntervals = prevSyncWaitIntervals;
        }
        return result;
    }

    private boolean trySyncToFileImpl() {
        boolean result = false;
        if(instanceLock.tryLock()){
            try{
                if(isDirty){
                    txOut.force(false); //writing meta information may result in an additional system call and we don't want to hold instance lock unnecessarily.
                    isDirty = false;
                }
                result = true;
            } catch (IOException e) {
                log.error("There was a problem syncing Transaction Log "+txFile.getName()+" to the physical file.",e);
            } finally{
                instanceLock.unlock();
            }
        }
        return result;
    }

    private void closeWriter()
    {
        if ( this.txOut != null )
        {
            instanceLock.lock(); //make sure no writers are in the process of writing a log.
            try
            {
                this.txOut.force( true );
                this.txOut.close();
                isDirty = false;
            }
            catch(java.nio.channels.ClosedChannelException e1){
                log.warn( "Writer is already in closed state.");
            }
            catch ( Exception e )
            {
                log.error( "Unable to close the stream.", e );
            }
            finally {
                instanceLock.unlock();
            }
        }

    }
    
    public void shutdown(){
        closeWriter();
    }
}
