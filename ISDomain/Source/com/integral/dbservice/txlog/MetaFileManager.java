package com.integral.dbservice.txlog;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FilenameFilter;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;

import com.integral.exception.IdcOptimisticLockException;
import com.integral.is.common.mbean.ISFactory;
import com.integral.persistence.ClusterCommitEventAdapterC;
import com.integral.user.OrganizationC;
import com.integral.user.UserC;
import com.integral.util.IdcUtilC;
import org.eclipse.persistence.exceptions.OptimisticLockException;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.logging.SessionLog;
import org.eclipse.persistence.queries.ReadAllQuery;

import com.integral.dbservice.DBService;
import com.integral.dbservice.TransactionLogMessage;
import com.integral.dbservice.txlog.TransactionLog.When;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.transaction.AsyncDBMetaInfo;
import com.integral.transaction.AsyncDBMetaInfoC;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.util.Tuple;
import com.integral.util.Triplet;
import org.eclipse.persistence.sessions.Session;

/**
 * This class is responsible for managing the meta files on the system. There is
 * a 1-1 correspondence between an <code>index</code> and a META file. <br>
 * <p>
 * The naming convention for a meta file is: {@link DBService#PERSIST_Q_PREFIX
 * DBServicePersistQ-}&lt;index&gt;.META.
 * 
 * <AUTHOR>
 * 
 */
public class MetaFileManager {
	private static com.integral.log.Log log = com.integral.log.LogFactory
			.getLog(MetaFileManager.class);

	private final TransactionLog txLogSystem;
	private final ArrayList<MetaFileDescriptor> metaFiles = new ArrayList<MetaFileDescriptor>(20);
	private final DBService dbService;
	private static User sysUser = null;
    private String peerVirtualServer = null;
    private static String virtualServer = null;
	private static final int MAX_RETRY = 3;

	public MetaFileManager(TransactionLog txLogSystem) {
		this.txLogSystem = txLogSystem;
		this.dbService = txLogSystem.getDbService();
		this.sysUser = UserFactory.getUser(ISFactory.getInstance().getISMBean().getISSystemUser() );
	    ServerMBean serverMBean = ConfigurationFactory.getServerMBean();
	    peerVirtualServer = txLogSystem.getDbService().getPeerVirtualServer();
	    virtualServer = serverMBean.getVirtualServerName();
	}

	public void setup() throws Exception {

		int maxIndex = dbService.getConfig().getNumPersistenceThreads();
		metaFiles.ensureCapacity(maxIndex);
		// Initialize to nulls.
		for (int i = metaFiles.size(); i < maxIndex; i++) {
			metaFiles.add(null);
		}

		initializeFromDb();

		for (int i = 0; i < maxIndex; i++) {
			MetaFileDescriptor descriptor = metaFiles.get(i);
			if (descriptor == null) {
				File txLogFile = txLogSystem.getEarliestOrLatestTxLogFile(i,
						When.Earliest);
				StringBuilder metaFileName = new StringBuilder().append(
						txLogFile.getAbsoluteFile().getParent()).append(
						File.separator).append(DBService.PERSIST_Q_PREFIX)
						.append(i).append(".").append(
								TransactionLog.META_SUFFIX);
				File metaFile = new File(metaFileName.toString());
				metaFile.createNewFile();
                //Only writable by owner - 644.
                try {
                    Runtime.getRuntime().exec("chmod 644 " + metaFile.getCanonicalPath());
                } catch (IOException e) {
                    //
                }
				log.info("Meta file for index: [" + i + "] is file: ["
						+ metaFile.toString() + "]");
				
				AsyncDBMetaInfo metaInfo = new AsyncDBMetaInfoC();
				metaInfo.setTxFileName( txLogFile.getName() );
				metaInfo.setPeerVirtualServer( peerVirtualServer );
				metaInfo.setTxnServerVirtualServer( virtualServer );
				metaInfo.setSeekPtr( 0 );
				metaInfo.setThreadNum( i );
				metaInfo.setCorelationId ( "0" );
				MetaState meta = new MetaState(txLogFile.getName(), 0, 0, "0");
				descriptor = new MetaFileDescriptor(metaInfo, meta, peerVirtualServer);
				
				descriptor.metaFile = metaFile;
				descriptor.openChannel();
				descriptor.syncStateToDisk();
				metaFiles.set(i, descriptor);
			}
            else {
            	if( descriptor.metaFile != null ) // Require for unit tests to work
            	{
            		descriptor.reopenChannel();
            		descriptor.syncStateToDisk();
            	}
            	else
            	{
    				// create file on disk
    				File txLogFile = txLogSystem.getEarliestOrLatestTxLogFile(i,
    						When.Earliest);
    				StringBuilder metaFileName = new StringBuilder().append(
    						txLogFile.getAbsoluteFile().getParent()).append(
    						File.separator).append(DBService.PERSIST_Q_PREFIX)
    						.append(i).append(".").append(
    								TransactionLog.META_SUFFIX);
    				File metaFile = new File(metaFileName.toString());
    				metaFile.createNewFile();
    				descriptor.metaFile = metaFile;
    				descriptor.openChannel();
    				descriptor.syncStateToDisk();
            	}
            }
			log.info("Initialized from META file: ["
					+ descriptor.metaInfo + "] state: ["
					+ descriptor.state.toString() + "]");
		}
	}

	public void shutdown() {
		for (MetaFileDescriptor metaFile : metaFiles) {
			try {
				metaFile.closeChannel();
			} catch (IOException e) {
				log.error("There was a problem closing channel for metafile: "
						+ metaFile.toString(), e);
			}
		}
	}

	/**
	 * This method should only be called from ONE thread.
	 * 
	 * @param index
	 * @param tlm
	 * @throws IOException
	 */
	public void updateMetaState(int index, TransactionLogMessage tlm)
			throws Exception {
		MetaFileDescriptor descriptor = metaFiles.get(index);
		
//		if (descriptor == null) {
//            log.warn("No MetaState descriptor was found for index: " + index);
//			File metaFile = txLogSystem.getEarliestOrLatestTxLogFile(index,
//					When.Earliest);
//			MetaState state = new MetaState(metaFile.getName(), 0, 0, "0");
//			descriptor = new MetaFileDescriptor(metaFile, state);
//			metaFiles.set(index, descriptor);
//		}
		if( descriptor == null )
			throw new RuntimeException ( "Descriptor for file should not be null " + tlm.fileName);		
		descriptor.updateState(tlm);
	}
	
	List<AsyncDBMetaInfo> queryMetaInfoObjects()
	{
        try
        {
            log.warn( "Querying MetaInfo objects from DB for peer virtual server " + peerVirtualServer );
            ReadAllQuery query = new ReadAllQuery();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression virtualServerExp = eb.get( "peerVirtualServer" ).equal( peerVirtualServer );
            
            query.setReferenceClass(AsyncDBMetaInfoC.class);
            query.setSelectionCriteria( virtualServerExp );
            query.useCollectionClass( ArrayList.class );
            List result = (List) PersistenceFactory.newSession().executeQuery(query);
            return result;
        }
        catch ( PersistenceException e )
        {
            e.printStackTrace();
        }
		return null;
	}


    /**
     * Query MetaInfo object from db for an Index.
     *
     * @param index
     * @return
     */
    public AsyncDBMetaInfo queryMetaInfoObjectForIndex(int index) throws PersistenceException {
        log.warn("Querying MetaInfo objects from DB for virtual server " + peerVirtualServer + " index:" + index);
        ReadAllQuery query = new ReadAllQuery();
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression virtualServerExp = eb.get("peerVirtualServer").equal(peerVirtualServer);
        Expression indexExp = eb.get("threadNum").equal(index);

        query.setReferenceClass(AsyncDBMetaInfoC.class);
        query.setSelectionCriteria(virtualServerExp.and(indexExp));
        query.useCollectionClass(ArrayList.class);
        AsyncDBMetaInfo metaInfo = null;
        Session session = PersistenceFactory.newSession();
        int logLevel = session.getLogLevel();
        session.setLogLevel(SessionLog.ALL);
        try {
            List result = (List) session.executeQuery(query);
            if (!result.isEmpty()) {
                metaInfo = (AsyncDBMetaInfo) result.get(0);
                long version = metaInfo.getVersion();
                session.refreshObject(metaInfo); //force a db refresh here to get the latest value from db.
                if (version != metaInfo.getVersion()) {
                    log.warn("Top Link returned the queried object from cache. Version Changed after refresh. Previous Version:" + version + ". Version after refresh:" + metaInfo.getVersion());
                }
            } else {
                log.warn("QueryMetaInfoObject: No object found for virtual server " + virtualServer + " and thread " + index + ". This can happen if this is a new VS.");
            }
            if (result.size() > 1)
                log.error("ERROR: QueryMetaInfoObject: More than one object for virtual server " + virtualServer + " and thread " + index);
        } catch (Exception ex){
            throw new PersistenceException(ex);
        }
        finally {
            session.setLogLevel(logLevel);
        }
        return metaInfo;
    }
	
	/**
	 * initialize all metafiles from disk.
	 * 
	 * @throws IOException
	 */
	public void initializeFromDb() throws IOException {
		
		// Load all the MetaInfo object for the peer virtual server.
		List<AsyncDBMetaInfo> metaInfoObjects = queryMetaInfoObjects();
		if( metaInfoObjects == null || metaInfoObjects.isEmpty())
		{
			log.warn(" No MetaInfo objects found in DB ");
			return;
		}
		for (AsyncDBMetaInfo metaInfo : metaInfoObjects) {
			// Prefix, index
			log.warn(" Processing MetaInfo for " + metaInfo.getTxFileName());
			int threadNum = metaInfo.getThreadNum();
			
			// Create new descriptor.
			MetaState meta =  MetaState.from( metaInfo );
			MetaFileDescriptor descriptor = new MetaFileDescriptor(metaInfo,meta,peerVirtualServer);
            if (threadNum < metaFiles.size()) {
                metaFiles.set(threadNum, descriptor);
            } else {
                log.warn("Found Meta Info Entry in DB for threadNum not used in App:" + meta + " for PeerVirtualServer:" + peerVirtualServer);
            }
		}
		
		// Now udpate the descriptor with MetaFile information.
		File f = txLogSystem.getHotDirFile();

		// Load all meta files. All files are prefixed with
		// DBService#PERSIST_Q_PREFIX-<index>
		FilenameFilter filter = new TransactionLog.TxLogFilenameFilter(
				DBService.PERSIST_Q_PREFIX, TransactionLog.META_SUFFIX);
		File[] files = f.listFiles(filter);

		for (int i = 0; i < files.length; i++) 
		{
			File metafile = files[i];
			log.warn("Attempting META file: " + metafile.getName());

			// Prefix, index
			Tuple<String, Integer> t = new Tuple<String, Integer>();
			parseMetaFileName(metafile.getName(), t);

			// update file information in descriptor
			MetaFileDescriptor descriptor = null;
            if(t.second.intValue() < metaFiles.size()){
                descriptor = metaFiles.get( t.second.intValue() );
            }
			// For some reason, no of files are more than database entries. descriptor would be null in that case.
			if( descriptor != null) 
				descriptor.metaFile = metafile;
		}
	}

	/**
	 * Split a meta fileName into it's individual components.
	 * 
	 * @param fileName
	 * @param components
	 * @return
	 */
	boolean parseMetaFileName(String fileName, Tuple<String, Integer> components) {
		int state = 0; // 0 = prefix, 1 = index, 2=date, 3=suffix
		StringBuilder prefixStr = new StringBuilder();
		StringBuilder indexStr = new StringBuilder();

		for (char ch : fileName.toCharArray()) {
			// we hit a separator, so increment to next state, and continue.
			if (ch == '-' || ch == '.') {
				state++;
				continue;
			}

			switch (state) {
			case 0:
				prefixStr.append(ch);
				break;
			case 1:
				indexStr.append(ch);
				break;
			}
		}
		if (state != 2) {
			return false;
		}

		try {
			components.first = prefixStr.toString();
			components.second = Integer.parseInt(indexStr.toString());
		} catch (Exception e) {
			log.warn("Error parsing filename: [" + fileName
					+ "]. Error message is: " + e.getMessage(), e);
			return false;
		}

		return true;
	}

	MetaState parseMetaState(File metafile) {
		MetaState meta = null;
		BufferedReader reader = null;

		try {
			reader = new BufferedReader(new FileReader(metafile));
			String statusLine = reader.readLine();
			if(statusLine != null){
	          meta = MetaState.from(statusLine);
			}
		} catch (Exception e) {
			log.warn(
					"There was an exception when trying to parse state from meta file: "
							+ metafile.getName() + ". The exception is: "
							+ e.getMessage(), e);
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (IOException e) {
					log.error("Unexpected IO error: " + e.getMessage(), e);
				}
			}
		}

		return meta;
	}


	/* package */String getMetaFilenameFromTxLog(String txLogFilename) {
		Triplet<String, Integer, Date> t = new Triplet<String, Integer, Date>();

		if (txLogSystem.parseTxLogFileName(txLogFilename, t)) {
			StringBuilder builder = new StringBuilder();
			builder.append(DBService.PERSIST_Q_PREFIX).append(
					t.second.intValue());
			return builder.toString();
		}
		return null;
	}

	/* package */ int getNumMetaFiles() {
		return metaFiles.size();
	}
    
	/* package */ MetaFileDescriptor getMetaFileForIndex(int index) {
		return metaFiles.get(index);
	}
	
	public static class MetaFileDescriptor {
		MetaState state;
		public File metaFile;
		FileChannel channel;
		ByteBuffer buffer;
		AsyncDBMetaInfo metaInfo;
        String peerVirtualServer;

		public MetaFileDescriptor(File metaFile, MetaState state, String peerVirtualServer)
				throws FileNotFoundException {
			super();
			this.state = state;
			this.metaFile = metaFile;
			channel = null;
			buffer = ByteBuffer.allocateDirect(2 * 1024);
            this.peerVirtualServer = peerVirtualServer;
		}

		public MetaFileDescriptor(AsyncDBMetaInfo metaInfo, MetaState state, String peerVirtualServer)
		{
			super();
			this.state = state;
			this.metaInfo = metaInfo;
			channel = null;
			buffer = ByteBuffer.allocateDirect(2 * 1024);
            this.peerVirtualServer = peerVirtualServer;
		}

		public void reopenChannel() throws IOException {
			closeChannel();
			channel = new FileOutputStream(metaFile).getChannel();
		}

		public void openChannel() throws IOException {
			if (channel == null) {
				channel = new FileOutputStream(metaFile).getChannel();
			}
		}

		public void closeChannel() throws IOException {
			if (this.channel != null) {
				this.channel.close();
				this.channel = null;
			}
		}
		
		private static byte[] CRLF_BYTES = new String("\r\n").getBytes();
		
		public void syncState() throws Exception {			
			syncStateToDB();			
            syncStateToDisk();

            log.info("syncState: file=" + metaFile.getCanonicalPath() + " state=" +  state.toString());
		}

        public void syncStateToDisk() throws IOException {
            String str = state.toString();

            channel.truncate(0);
            channel.position(0);

            byte[] bytes = str.getBytes();

            //CRLF = 2 bytes.
            if ((bytes.length + 2) > buffer.capacity()) {
                buffer = ByteBuffer.allocateDirect(bytes.length + 2);
            }

            buffer.clear();
            buffer.put(str.getBytes());
            buffer.put(CRLF_BYTES);

            buffer.flip();
            channel.write(buffer);
            // the force call will make the file written to actual
            // storage device involving large i/o time.
//			channel.force(false);            
        }

		public void syncStateToDB() throws Exception
		{
			syncStateToDB ( 0 );
		}

		private void syncStateToDB( int retryNum ) throws Exception
		{
			IdcTransaction tx = null;
	        boolean localTxn = false;
			try {
		        tx = IdcSessionManager.getInstance().getTransaction();
		        if( tx == null )
		        {
		        	IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext(sysUser);
		        	IdcSessionManager.getInstance().setSessionContext(ctx);
		        	tx = IdcSessionManager.getInstance().newIdcTransaction( "AsyncDBMetaInfo" );
					tx.addReadOnlyClass(UserC.class);
					tx.addReadOnlyClass(OrganizationC.class);
					tx.getUOW().setProperty(ClusterCommitEventAdapterC.MULTI_APP, ClusterCommitEventAdapterC.MULTI_APP_DISABLED );
					localTxn = true;
		        }
		        if(metaInfo.getObjectId() == 0)
		        	log.warn( "Inserting MetaInfo for " + state.fileName );
		        
		        metaInfo = (AsyncDBMetaInfo)metaInfo.getRegisteredObject();
		        metaInfo.setTxFileName( state.fileName );
		        metaInfo.setTransactionId( state.transactionId );
		        metaInfo.setSeekPtr( state.seekPtr );
		        metaInfo.setCorelationId ( state.corelationId );
		        metaInfo.setTxnServerVirtualServer( virtualServer );
                metaInfo.setPeerVirtualServer(peerVirtualServer);        
                
		        if( localTxn ) // Commit only locally started txn. Otherwise calling API should do that.
		        	tx.commit();
			}
			catch ( IdcOptimisticLockException iole )
			{
				log.warn( "MFM.syncStateToDB : OLE happened for metaInfo=" + metaInfo + ",retry=" + retryNum , iole );
				if ( metaInfo != null )
				{
					metaInfo = ( AsyncDBMetaInfo ) IdcUtilC.refreshObject( metaInfo );
					if ( retryNum > MAX_RETRY )
					{
						log.error( "MFM.syncStateToDB : Maximum retry exceededOLE happened for metaInfo=" + metaInfo, iole );
					}
					else
					{
						syncStateToDB( ++retryNum );
					}
				}
			}
			catch ( OptimisticLockException ole )
			{
				log.warn( "MFM.syncStateToDB : OLE happened for metaInfo=" + metaInfo + ",retry=" + retryNum, ole );
				if ( metaInfo != null )
				{
					metaInfo = ( AsyncDBMetaInfo ) IdcUtilC.refreshObject( metaInfo );
					if ( retryNum > MAX_RETRY )
					{
						log.error( "MFM.syncStateToDB : Maximum retry exceeded. OLE happened for metaInfo=" + metaInfo, ole );
					}
					else
					{
						syncStateToDB( ++retryNum );
					}
				}
			}
			catch ( Exception ex )
			{
				if( localTxn && tx != null )
					tx.release();
				log.error("syncStateToDB : Sync state to DB failed. Exception ",ex);
				throw new Exception( "Could not update MetaInfo object into database " + ex);
			}
			
		}
		
		public void updateState(TransactionLogMessage tlm) throws Exception {
			try {				
				state.init(tlm);
				syncState();
			} catch (IOException e) {
				closeChannel();
				throw e;
			}
		}
	}

	public static class MetaState {
		String fileName;
		long seekPtr;
		long transactionId;
        //the corelationId corresponding to transactionId - redundant, but for debugging.
		String corelationId;

		public MetaState () {
		}

		public MetaState (String fileName, long seekPtr, long transactionId,String corelationId) {
			super();
			this.fileName = fileName;
			this.seekPtr = seekPtr;
			this.transactionId = transactionId;
			this.corelationId = corelationId;
		}

		@Override
        public String toString() {
			StringBuilder builder = new StringBuilder();
			builder.append(fileName).append('|').append(seekPtr).append('|')
					.append(transactionId).append('|').append( corelationId );
			return builder.toString();
		}

		public void init(TransactionLogMessage tlm) {
			this.fileName = tlm.fileName;
			this.seekPtr = tlm.seekPtr;
			this.transactionId = tlm.transactionId;
            this.corelationId = tlm.corelationId;
		}

		public static MetaState from(TransactionLogMessage tlm) {
			MetaState meta = new MetaState();
			meta.fileName = tlm.fileName;
			meta.seekPtr = tlm.seekPtr;
			meta.transactionId = tlm.transactionId;
            meta.corelationId = tlm.corelationId;
			return meta;
		}

		public static MetaState from(AsyncDBMetaInfo metaInfo) {
			MetaState meta = new MetaState();
			meta.fileName = metaInfo.getTxFileName();
			meta.seekPtr = metaInfo.getSeekPtr();
			meta.transactionId = metaInfo.getTransactionId();
            meta.corelationId = metaInfo.getCorelationId();
			return meta;
		}
		
		// Parse a meta record into a MetaState object.
		public static MetaState from(String str) {
			MetaState meta = new MetaState();
			int state = 0;
			StringBuilder b = new StringBuilder();

			try {
				for (char ch : str.toCharArray()) {
					if (ch == '|' || ch == '\r' || ch == '\n') {
						switch (state) {
						case 0:
							meta.fileName = b.toString();
							break;
						case 1:
							meta.seekPtr = Long.parseLong(b.toString());
							break;
						case 2:

							meta.transactionId = Long.parseLong(b.toString());
							// coming here.
							break;
                        case 3:
                            // this condition will ideally never be reached in
							// this loop, as we will break out before coming
							// here.
							// But if this was hand-edited, and the user enters
							// a carriage return at the end, then we may hit
							// here.
                            meta.corelationId = b.toString();
                            break;
						}
						state++;
						b = new StringBuilder();
					} else {
						b.append(ch);
					}
				}

				// This is the normal case, but may not be reached, if case 3:
				// above gets encountered. Read comments, dude!
				if (state == 3) {
					meta.corelationId = b.toString();
				}

			} catch (NumberFormatException e) {
				// Bad transactionId of seekPtr passed!
				log.error("Unable to parse MetaState from String: " + str
						+ " due to: " + e.getMessage());
				return null;
			}

			if (state < 3) {
                log.warn("Did not reach terminal state in parsing: " + str + ", state=" + state);
				return null;
			}

			return meta;

		}
	}

}
