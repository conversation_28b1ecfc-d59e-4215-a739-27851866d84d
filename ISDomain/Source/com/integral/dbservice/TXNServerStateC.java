package com.integral.dbservice;

import com.integral.dbservice.txlog.TransactionLog.State;
import org.codehaus.jackson.annotate.JsonIgnore;

import java.util.Hashtable;
import java.util.Map;

public class TXNServerStateC implements TXNServerState
{
    private Map<Integer, State> readerTaskState = new Hashtable<Integer,State>(); //synchronization required here.
    private State txLogState = State.STARTUP;

    public TXNServerStateC()
    {
    }

    public Map<Integer, State> getReaderTaskStates()
    {
        return readerTaskState;
    }

    public void setReaderTaskState( int i, State state )
    {
        readerTaskState.put( i, state );
    }

    public State getReaderTaskState( int i )
    {
        return readerTaskState.get( i );
    }

    public State getTxLogState()
    {
        return txLogState;
    }

    public void setTxLogState( State state )
    {
        txLogState = state;
    }

    @JsonIgnore 
    public State getReaderTaskState()
    {
        for ( State _state : readerTaskState.values() )
        {
            if ( _state != State.STARTED )
            {
                return _state;
            }
        }
        return txLogState;
    }
    
}
