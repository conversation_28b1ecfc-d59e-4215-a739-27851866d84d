package com.integral.dbservice;

import java.io.IOException;
import java.util.HashMap;

import com.integral.dbservice.txlog.TransactionLog;
import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.ServerRuntime;

/**
 * This processor is responsible for writing a {@link PipelineMessage pipeline
 * message} to a log file.
 * 
 * <AUTHOR>
 * 
 */
public class WriteToTransactionLogProcessor extends Processor {

	TransactionLog txLogSystem = null;
	private ByteBufferWriter wri = null;

	public WriteToTransactionLogProcessor(TransactionLog txLogSystem, ByteBufferWriter wri) {
		super();
		this.txLogSystem = txLogSystem;
        this.wri = wri;
	}

	public PipelineMessage process(PipelineMessage message) {
        try {
            TransactionLogMessage tlm = (TransactionLogMessage) message;
            if (tlm.isWarmupEvent) {
                //We should reinitialize to clear this buffer, otherwise this buffer gets backed-up.
            	if(tlm.entityAsBytes != null)
            		tlm.entityAsBytes.reinitialize();
            	if( wri != null )
            		wri.reinitialize();
                log.info("Skipping warmup event for corelationId: " + tlm.corelationId  + " for eventName: " + tlm.eventName);
                return message;
            }
            try {
                tlm.entityAsBytes = wri;
                message = txLogSystem.write(tlm);
            } catch (IOException e) {
                throw new PipelineException(e);
            }
        } finally {
            try {
                wri.close();
            } catch (IOException e) {
                log.error("Exception due to: " + e.getMessage(), e);
            }
        }

        return message;
	}

	public Processor prototype() {
		return null;
	}

}
