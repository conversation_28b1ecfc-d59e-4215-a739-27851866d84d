package com.integral.dbservice;

import java.io.StringWriter;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.persistence.Entity;
import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import com.integral.xml.XMLConvertor;
import com.integral.xml.XMLConvertorFactory;
import com.integral.xml.mapping.XMLException;

/**
 * A processor that is responsible for creating a signature from an 
 * {@link Entity} in a {@link Message}.
 * 
 * <AUTHOR>
 *
 */
public class SerializeToTextProcessor extends Processor {
	private static Log log = LogFactory.getLog(SerializeToTextProcessor.class);
	
	private static String MAPPING_NAME = "IntegrationServer";
	
	//Create a StringWriter with a 4k cache upfront.
	StringWriter writer = new StringWriter(4096);
	XMLConvertor xmlConvertor = XMLConvertorFactory.getXMLConvertor();
	
	public PipelineMessage process(PipelineMessage request) {
		
		TransactionLogMessage tlm = (TransactionLogMessage) request;
		if (tlm.entity == null) {
			throw new NullPointerException("entity is null for corelationId: [" + tlm.getCorelationId() + "]");
		}
		
		try {
			xmlConvertor.convertToXML(writer, tlm.entity, MAPPING_NAME);
			tlm.entityText = writer.getBuffer();
			if (log.isDebugEnabled()) {
				StringBuilder builder = new StringBuilder();
				builder.append("Serialized corelationId [").append(tlm.corelationId).append("] to [").append(tlm.entityText.toString()).append("]");
				log.debug(builder.toString());
			}
		} catch (XMLException e) {
			throw new PipelineException(e);
		}		
		return request;
	}

}
