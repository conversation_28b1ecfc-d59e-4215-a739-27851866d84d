package com.integral.dbservice;

import com.integral.dbservice.txlog.TransactionLog;
import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;

import java.io.IOException;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Oct 12, 2010
 * Time: 12:00:30 PM
 * The processor writes a transaction log message to an error log.
 */
public class WriteToErrorLogProcessor extends Processor {

    private final TransactionLog txLogSystem;
	private final ByteBufferWriter wri;

    public WriteToErrorLogProcessor(ByteBufferWriter wri, TransactionLog txLogSystem) {
        this.wri = wri;
        this.txLogSystem = txLogSystem;
    }

    @Override
    public PipelineMessage process(PipelineMessage msg) {
       try {
            TransactionLogMessage tlm = (TransactionLogMessage) msg;
            try {
                tlm.entityAsBytes = wri;
                msg = txLogSystem.writeErrorMessage(tlm);
            } catch (IOException e) {
                throw new PipelineException(e);
            }
        } finally {
            try {
                wri.close();
            } catch (IOException e) {
                log.error("Exception due to: " + e.getMessage(), e);
            }
        }

        return msg;
    }
}
