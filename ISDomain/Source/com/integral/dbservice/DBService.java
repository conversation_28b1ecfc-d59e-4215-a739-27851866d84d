package com.integral.dbservice;

import com.integral.dbservice.is.ISPersistenceServiceCacheManager;
import com.integral.dbservice.txlog.TransactionLog;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Entity;
import com.integral.pipeline.PipelineController;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.PipelineSetupFunctor;
import com.integral.pipeline.queue.QueueManager;
import com.integral.pipeline.strategy.QueueInputPipelineController;
import com.integral.pipeline.strategy.SyncPipelineController;
import com.integral.system.configuration.ConfigurationFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Queue;

/**
 * The generic DB service interface to users. By default it will setup two
 * queues for processing.
 *
 * <AUTHOR>
 */
public class DBService {
	private static Log log = LogFactory.getLog(DBService.class);

	// This is the prefix used by all queues used by DBService.
	public static final String PERSIST_Q_PREFIX = "DBServicePersistQ-";

    //This is the prefix used by all error queues used by DBService.    
    public static final String ERROR_Q_PREFIX = "DBServiceErrorQ-";

	// These functors are responsible for setting up the pipeline.
	protected PipelineSetupFunctor serializeAndEnqueSetupFunctor = null;
	protected PipelineSetupFunctor eventPipelineSetupFunctor = null;

	protected SyncPipelineController serializeAndEnquePipeline = null;
	protected QueueInputPipelineController eventHandlerPipeline = null;

//	private int numQueues = 2;
	protected ArrayList<String> queueNames = new ArrayList<String>();

	private boolean shutdown = false;
	private boolean initialized = false;

//	private static DBService instance = new DBService();
    private final String peerVirtualServer; //Useful for multi-tenant Transaction Server.

	protected ISPersistenceMBean config;

	protected TransactionLog txLogSystem;

	/*public static DBService instance() {
		return instance;
	}*/

    public DBService(ISPersistenceMBean config) {
        this(config, ConfigurationFactory.getServerMBean().getPeerVirtualServerName());
    }

    public DBService(ISPersistenceMBean config, String peerVirtualServer) {
		this.config = config;
        this.peerVirtualServer = peerVirtualServer;
		txLogSystem = new TransactionLog(this, config);        
	}

	public ISPersistenceMBean getConfig() {
		return config;
	}

    public String getPeerVirtualServer() {
        return peerVirtualServer;
    }

	public boolean isInitialized() {
		return initialized;
	}

	public TransactionLog getTransactionLog() {
		return txLogSystem;
	}

	/**
	 * This should be called once during startup for the instance.
	 * @throws IOException
	 */
	public void setup() throws Exception {

		if (initialized) {
			log.warn("Attempt to setup an initialized DBService will be ignored.");
			return;
		}

		internalSetup();

		this.txLogSystem.setup();
		initialized = true;
	}

	/*package*/ void internalSetup() {
		//There is one queue per persistence thread - this is the concurrency factor of the system.
		this.setNumQueues(config.getNumPersistenceThreads());
		serializeAndEnqueSetupFunctor = getSerializeAndEnqueSetupFunctor();
		eventPipelineSetupFunctor = getEventPipelineSetupFunctor();

		serializeAndEnquePipeline = new SyncPipelineController(
				serializeAndEnqueSetupFunctor);
        setupQueueTasks();
        DBServiceInstanceHolder.setDBServiceInstance(this); //set the instance in the holder.
	}

    protected void setupQueueTasks() {
        if(config.isTXQueueConsumerTaskEnabled()){ //don't create queue consumer tasks if not configured. Consumer Task is not required for remote Tx Server.
            eventHandlerPipeline = new QueueInputPipelineController(
                    eventPipelineSetupFunctor, queueNames, config.getPipelineQueueLength());
        }
    }

    public void stop() {
		shutdown = true;
        if(serializeAndEnquePipeline != null){ //can happen for unit tests.
		    serializeAndEnquePipeline.shutdown();
        }
        if(eventHandlerPipeline != null){
            eventHandlerPipeline.shutdown();
            // wait for the underlying threads to go away.
            eventHandlerPipeline.waitForPipelineToShutdown();
        }
		this.txLogSystem.shutdown();
        clearPersistanceCache();
        //Remove the queues created by this service. With multi-tenant Tx Server config, a single JVM may host more than one service.
        QueueManager queueManager = QueueManager.instance();
        for(String queue:queueNames){
            queueManager.removeQueue(queue);
        }             
		initialized = false;
	}

    protected void clearPersistanceCache() {
        ISPersistenceServiceCacheManager.instance(peerVirtualServer).clearAllCache();
    }

    public void stopImmediate() {
        shutdown = true;
        if(serializeAndEnquePipeline != null){ //can happen for unit tests.
            serializeAndEnquePipeline.shutdown();
        }
        if(eventHandlerPipeline != null){
            eventHandlerPipeline.shutdown();
        }
        this.txLogSystem.shutdown();
        clearPersistanceCache();
        //Remove the queues created by this service. With multi-tenant Tx Server config, a single JVM may host more than one service.
        QueueManager queueManager = QueueManager.instance();
        for(String queue:queueNames){
            queueManager.removeQueue(queue);
        }   
        initialized = false;
    }

    public QueueInputPipelineController getEventHandlerPipeline(){
        return eventHandlerPipeline;
    }

	/**
	 * @return the numQueues
	 */
	public int getNumQueues() {
		return config.getNumPersistenceThreads();
	}

	/**
	 * @param numQueues
	 *            the numQueues to set
	 */
	public void setNumQueues(int numQueues) {
		if (initialized) {
			throw new IllegalAccessError(
					"DBService has already been setup. Cannot setNumQueues in this state.");
		}

		queueNames.clear();
		for (int i = 0; i < numQueues; i++) {
			StringBuilder builder = new StringBuilder(PERSIST_Q_PREFIX);
			builder.append(i).append("-").append(peerVirtualServer);            
			queueNames.add(builder.toString());
			QueueManager.instance().setupQueue(builder.toString(),config.getPipelineQueueLength());
		}
	}

	/**
	 * Get the queue for the specified <code>index</code>. If the specified
	 * queue does not exist, then an {@link IllegalAccessException} will be
	 * thrown.
	 *
	 * @param index
	 * @return
	 * @throws IllegalAccessException
	 */
	public Queue<PipelineMessage> getQueue(int index)
			throws IllegalAccessException {
		String name = getQueueNameForIndex(index);
		return QueueManager.instance().getQueue(name);
	}

	public String getQueueNameForIndex(int qIndex)
			throws IllegalAccessException {
		if (qIndex < 0 || qIndex >= queueNames.size()) {
			throw new IllegalAccessException("No queue for index: " + qIndex);
		}
		return queueNames.get(qIndex);
	}

	/**
	 * Process an event for an Entity. CorelationId is used by this service to
	 * guarantee order of processing. This is an identifier which is used to tie
	 * different events together into one logical transaction. For e.g. when
	 * multiple Trade accept/verify events occur for an Order, the orderId is
	 * the identifier that co-relates all of these events into one logical
	 * transaction. All events with the same corelationId are guaranteed to be
	 * processed serially - by a single thread.
	 *
	 * @param corelationId
	 * @param event
	 * @param entity
	 * @param propertyMap
	 */
	public void processEvent(String corelationId, String event, Entity entity, Map<String,String> propertyMap, boolean isWarmup) {
        TransactionLogMessage tx = new TransactionLogMessage();
        tx.isWarmupEvent = isWarmup;
        tx.setCorelationId(corelationId);
        tx.setEventName(event);
        tx.entity = entity;
        tx.properties = propertyMap;
        serializeAndEnquePipeline.process(tx);
    }

//    public void processWarmupEvent(String corelationId, String event, Entity entity) {
//          processEvent( corelationId, event, entity, null, true );
//    }
//
//	public void processEvent(String corelationId, String event, Entity entity) {
//	    processEvent( corelationId, event, entity, null, false );
//	}


	/**
	 * Process an event for collection of Entities.
	 * Entities are serialized in the same order they were added to the collection.
	 * Entities are deserialized in the same order.
	 * The specific event handler should know the order of entity to process them
	 * effectively.
	 *
	 * @param corelationId
	 * @param event
	 * @param entities
	 * @param propertyMap
	 */
	public void processEvents(String corelationId, String event, List<Entity> entities, Map<String,String> propertyMap, boolean isWarmup)
	{
		TransactionLogMessage tx = new TransactionLogMessage();
        tx.isWarmupEvent = isWarmup;
		tx.setCorelationId(corelationId);
		tx.setEventName(event);
		tx.entity = entities;
        tx.properties = propertyMap;
		serializeAndEnquePipeline.process(tx);
	}

	public void processEvents(String corelationId, String event, List<Entity> entities, boolean isWarmup)
    {
        processEvents( corelationId, event, entities, null, isWarmup );
    }

	public Entity getEntity(String id) {
		return null;
	}

	public PipelineSetupFunctor getSerializeAndEnqueSetupFunctor() {
		if (serializeAndEnqueSetupFunctor == null) {
			serializeAndEnqueSetupFunctor = new SerializeAndEnquePipelineSetupFunctor(this, queueNames,config.getPipelineQueueLength());
		}
		return serializeAndEnqueSetupFunctor;
	}

	public void setSerializeAndEnqueSetupFunctor(
			PipelineSetupFunctor serializeAndEnqueSetupFunctor) {
		if (initialized) {
			throw new IllegalAccessError(
                "DBService has already been initialized. Cannot set SerializeAndEnqueFunctor in this state.");
		}
		this.serializeAndEnqueSetupFunctor = serializeAndEnqueSetupFunctor;
	}

	public PipelineSetupFunctor getEventPipelineSetupFunctor() {
		return eventPipelineSetupFunctor;
	}

	public void setEventPipelineSetupFunctor(
			PipelineSetupFunctor eventPipelineSetupFunctor) {
		if (initialized) {
			throw new IllegalAccessError(
					"DBService has already beein initialized. Cannot set EventPipelineSetupFunctor in this state.");
		}
		this.eventPipelineSetupFunctor = eventPipelineSetupFunctor;
	}

	public PipelineController getSerializeAndEnquePipelineController()
	{
		return serializeAndEnquePipeline;
	}
}
