package com.integral.dbservice.remote;

import com.integral.dbservice.TransactionLogMessage;
import com.integral.is.common.comm.http.HttpHandler;
import com.integral.is.common.comm.http.HttpHandlerC;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.transaction.AsyncDBMetaInfo;

import java.io.OutputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.ByteBuffer;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Sep 14, 2010
 * Time: 2:43:23 PM
 * A helper class for handling remote transaction server operations.
 */
public class MultiTenantRemoteTransactionServerHelperImpl implements MultiTenantRemoteTransactionServerHelper {

    private static final Log log = LogFactory.getLog(MultiTenantRemoteTransactionServerHelperImpl.class);

    private final MTHttpHandler logSender; //not using a synchronized pool as each reader thread uses a single instance of this class to send Tx log messages.
    private final HttpHandler syncSender;
    private final ByteBuffer logBuffer; //reusing the buffer here as each reader has its own instance of this class.
    private String remoteURL;

    public MultiTenantRemoteTransactionServerHelperImpl(ISPersistenceMBean config, String remoteURL) {
        this.remoteURL = remoteURL;
        logSender = new MTHttpHandler(remoteURL + "/devapp/tx/reqLogMessage.jsp");
        logSender.setUseCaches(false);
        logSender.setRequestMethod("POST");
        syncSender = new HttpHandlerC(remoteURL + "/devapp/tx/pingRemoteServer.jsp");
        syncSender.setUseCaches(false);
        syncSender.setRequestMethod("POST");
        logBuffer = ByteBuffer.allocate(config.getSendBufferSize());
    }

    public String getRemoteURL() {
        return remoteURL;
    }

    public MultiTenantTransactionServerResponse sendTransactionLogMessage(TransactionLogMessage tlm, int timeout) {
        MultiTenantTransactionServerResponse result = MultiTenantTransactionServerResponse.FAILURE;
        logSender.setReadTimeOut(timeout);
        try {
            int httpRes = logSender.execute(tlm, true);
            if (httpRes == 200) {
                String res = logSender.getResponse();
                result = MultiTenantTransactionServerResponse.getFromResponseString(res);
            }
        } catch (Exception e) {
            log.error("Error encountered while trying to send Transaction Log Message to remote server:" + logSender.getURLString(), e);
        }
        return result;
    }

    public MultiTenantTransactionServerResponse sendSyncMetaInfo(AsyncDBMetaInfo metaInfo, int timeout) {
        MultiTenantTransactionServerResponse result = MultiTenantTransactionServerResponse.FAILURE;
        HttpHandler syncSender = new HttpHandlerC(remoteURL + "/devapp/tx/syncMetaInfo.jsp");
        syncSender.setUseCaches(false);
        syncSender.setRequestMethod("POST");
        syncSender.setReadTimeOut(timeout);
        try {
            int httpRes = syncSender.execute(metaInfo.serializeToString(), true);
            if (httpRes == 200) {
                String res = syncSender.getResponse();
                result = MultiTenantTransactionServerResponse.getFromResponseString(res);
            }
        } catch (Exception e) {
            log.error("Error encountered while trying to send Sync Meta Info to remote server:" + logSender.getURLString(), e);
        }
        return result;
    }

    public MultiTenantTransactionServerResponse ping(RemoteTransactionServerPing ping, int timeout) {
        MultiTenantTransactionServerResponse result = MultiTenantTransactionServerResponse.FAILURE;
        syncSender.setReadTimeOut(timeout);
        try {
            int httpRes = syncSender.execute(ping.serializeToString(), true);
            if (httpRes == 200) {
                String res = syncSender.getResponse();
                result = MultiTenantTransactionServerResponse.getFromResponseString(res);
            }
        } catch (Exception e) {
            log.error("Error encountered while trying to send Ping Info to remote server:" + logSender.getURLString(), e);
        }
        return result;
    }

    private class MTHttpHandler extends HttpHandlerC {

        private static final String US_ASCII_ENCODING = "US-ASCII";

        public MTHttpHandler(String urlStr) {
            super(urlStr);
        }


        public int execute(TransactionLogMessage tlm, boolean timeoutEnabled) {

            int iResponse = 0;
            try {
                long starttime = System.nanoTime();
                openConnection(timeoutEnabled);
                // put query params on output stream.
                // note : always do stream handling just before connect.
                OutputStream os = con.getOutputStream();
                ByteBuffer buffer = logBuffer;
                buffer.clear();
                byte[] PIPE = "|".getBytes(US_ASCII_ENCODING);
                buffer.put(Long.toString(tlm.transactionId).getBytes(US_ASCII_ENCODING));
                buffer.put(PIPE).put(tlm.corelationId.getBytes(US_ASCII_ENCODING));
                buffer.put(PIPE).put(tlm.fileName.getBytes(US_ASCII_ENCODING));
                buffer.put(PIPE).put(Long.toString(tlm.seekPtr).getBytes(US_ASCII_ENCODING));
                buffer.put(PIPE).put(tlm.eventName.getBytes(US_ASCII_ENCODING));
                buffer.put(PIPE).put(Long.toString(tlm.creationTime).getBytes(US_ASCII_ENCODING));
                buffer.put(PIPE).put(tlm.virtualServer.getBytes(US_ASCII_ENCODING));
                buffer.put(PIPE).put(Integer.toString(tlm.index).getBytes(US_ASCII_ENCODING));
                buffer.put(PIPE).put(tlm.sessionId.getBytes(US_ASCII_ENCODING));
                StringBuilder times = new StringBuilder();
                tlm.eventTimesString(times);
                buffer.put(PIPE).put(times.toString().getBytes(US_ASCII_ENCODING));
                buffer.put(PIPE).put(Integer.toString(tlm.entityText.length()).getBytes(US_ASCII_ENCODING));
                buffer.put(PIPE).put(tlm.entityText.toString().getBytes(US_ASCII_ENCODING));
                buffer.flip();
                os.write(buffer.array(), 0, buffer.remaining());
                os.close();
                iResponse = con.getResponseCode();
                timeTaken = System.nanoTime() - starttime;
            } catch (java.net.SocketTimeoutException ex) {
                log.warn("MTHttpHandlerC.execute : Read timeout occured while getting response from " + urlStr);
                if (!timeoutEnabled) {
                    log.warn("MTHttpHandlerC.execute : SocketTimeoutException should not happen if Read timeout is not enabled for this connection to " + urlStr);
                    ex.printStackTrace();
                }
                con.disconnect();
            } catch (Exception ex) {
                log.error("MTHttpHandlerC.execute : Could not connect to " + urlStr + ". Exception generated: " + ex + ", message:" + ex.getMessage(), ex);
                //reset the URL object here.
                if (con != null) {
                    con.disconnect();
                }
                try {
                    server = new URL(urlStr);
                } catch (MalformedURLException ex1) {
                    log.error("HttpHandler: Error in Creating URL object : " + ex1);
                }
            }
            return iResponse;
        }
    }

}
