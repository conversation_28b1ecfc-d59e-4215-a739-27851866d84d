package com.integral.dbservice.remote;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.PersistenceFactory;
import com.integral.system.server.VirtualServer;
import com.integral.system.server.VirtualServerC;
import com.integral.system.server.VirtualServerType;
import com.integral.system.server.VirtualServerTypeC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 2/16/12
 * Time: 11:07 AM
 */
public class MultiTenantRemoteTxnServerUtils {
    private static final Log log = LogFactory.getLog(MultiTenantRemoteTxnServerUtils.class);

    public static List<VirtualServer> getAllTxnServers(Session oSession) {
        List<VirtualServer> txnServerCol = Collections.emptyList();
        try {
            ReadAllQuery query = new ReadAllQuery();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression virtualServerTypeExp = eb.get("shortName").equal("TxnServer");

            query.setReferenceClass(VirtualServerTypeC.class);
            query.setSelectionCriteria(virtualServerTypeExp);
            query.useCollectionClass(ArrayList.class);
            List<VirtualServerType> serverTypes = (List) oSession.executeQuery(query);

            if (serverTypes != null && serverTypes.size() > 0) {
                query = new ReadAllQuery();
                eb = new ExpressionBuilder();
                Expression virtualServerExp = eb.get("virtualServerType").equal(serverTypes.get(0));

                query.setReferenceClass(VirtualServerC.class);
                query.setSelectionCriteria(virtualServerExp);
                query.useCollectionClass(ArrayList.class);
                txnServerCol = (List) oSession.executeQuery(query);
            }

        } catch (Exception e) {
            log.error("Encountered exception while querying for all txn server Virtual Servers", e);
        }

        return txnServerCol;
    }

    public static VirtualServer selectNewTxnServer(VirtualServer vs) {
        VirtualServer txnServerVs = null;
        try {
            Session oSession = PersistenceFactory.newSession();
            UnitOfWork uow = oSession.acquireUnitOfWork();
            vs = (VirtualServer) uow.refreshObject(vs);
            txnServerVs = vs.getRemoteTxnServer();
            if (txnServerVs == null) {
                List<VirtualServer> txnServers = getAllTxnServers(oSession);
                int numTxnServers = txnServers.size();
                if (numTxnServers > 0) {
                    txnServerVs = txnServers.get((int) (System.currentTimeMillis() % numTxnServers));
                    vs = (VirtualServer) uow.registerObject(vs);
                    if (txnServerVs != null) {
                        txnServerVs = (VirtualServer) uow.refreshObject(txnServerVs);
                        txnServerVs = (VirtualServer) uow.registerObject(txnServerVs);
                    }
                    vs.setRemoteTxnServer(txnServerVs);
                    uow.commit();
                }
            }
        } catch (Exception e) {
            log.error("Encountered exception while selecting Txn Server.", e);
        }
        return txnServerVs;
    }
}
