package com.integral.dbservice.remote;

import com.integral.dbservice.DBService;
import com.integral.dbservice.TransactionLogMessage;
import com.integral.is.alerttest.AlertCheckC;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.mbean.ISPersistenceMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.PersistenceException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.queue.QueueManager;
import com.integral.transaction.AsyncDBMetaInfo;
import com.integral.transaction.AsyncDBMetaInfoC;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import static com.integral.dbservice.remote.MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse.*;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Sep 28, 2010
 * Time: 2:43:46 PM
 * The server side implementation of the remote transaction server.
 */
public class MultiTenantRemoteTransactionServer {

    protected static final Log log = LogFactory.getLog(MultiTenantRemoteTransactionServer.class);
    private static final long RESPONSE_WAIT_MILLIS = 100;//wait time in milliseconds TODO should we make it configurable?
    private static final long SYNC_WAIT_MILLIS = 2500;//wait time in milliseconds takes longer in Sync Operation. TODO should we make it configurable?

    private ISPersistenceMBean config;
    private Map<String, RemoteTransactionServerTenantHolder> remoteTxHolders = Collections.emptyMap();
    private Lock instanceLock = new ReentrantLock(); //instance lock used while adding/removing new tenants.
    private static AtomicLong sessionIdGenerator = new AtomicLong();
    private MultiTenantPersistenceService persistenceService;


    private static class RemoteTransactionServerHolder {
        private static MultiTenantRemoteTransactionServer instance = new MultiTenantRemoteTransactionServer();
    }

    private MultiTenantRemoteTransactionServer() {
    }

    protected MultiTenantRemoteTransactionServer(String mode) {
        log.error("Invoked MultiTenantRemoteTransactionServer constructor meant for Unit Tests With Mode:" + mode);
    }

    public static MultiTenantRemoteTransactionServer getInstance() {
        return RemoteTransactionServerHolder.instance;
    }

    public void setConfig(ISPersistenceMBean config) {
        this.config = config;
    }

    public void setMultiTenantPersistenceService(MultiTenantPersistenceService persistenceService) {
        this.persistenceService = persistenceService;
    }

    /**
     * Setup function initializes the txn server with dbservices for multiple tenants.
     *
     * @param dbServices
     * @throws Exception
     */
    public void setup(List<DBService> dbServices) throws Exception {
        remoteTxHolders = new Hashtable<String, RemoteTransactionServerTenantHolder>(dbServices.size());
        instanceLock.lock();
        try {
            for (DBService dbService : dbServices) {
                String tenant = dbService.getPeerVirtualServer();
                log.info("Multi tenant Transaction Server: Adding tenant:" + tenant);
                remoteTxHolders.put(tenant, new RemoteTransactionServerTenantHolder(dbService, tenant));
            }
        } finally {
            instanceLock.unlock();
        }
    }

    public void setup() throws Exception {
        remoteTxHolders = new Hashtable<String, RemoteTransactionServerTenantHolder>();
    }

    public MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse reqLogMessage(String param) {
        MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse response = MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse.FAILURE;
        try {
            AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_MTXN_SERVER_REQ_LOG);
            TransactionLogMessage message = TransactionLogMessage.deserializeFromString(param);
            RemoteTransactionServerTenantHolder remoteServerTenant = remoteTxHolders.get(message.virtualServer);
            if (remoteServerTenant == null) {
                persistenceService.addTenant(message.virtualServer);
                remoteServerTenant = remoteTxHolders.get(message.virtualServer);
            }
            synchronized (remoteServerTenant) { //synchronize with sync operation here for each remote tenant to avoid queue corruption.
                if (remoteServerTenant.getTenantConsumerState(message.index) == RemoteTransactionServerTenantConsumerState.PROCESS) {

                    if (remoteServerTenant.getTenantSessionId(message.index).equals(message.sessionId)) {
                        String name = "";
                        try {
                            DBService dbService = remoteServerTenant.dbService;
                            // During shutdown we set the initialized flag to false. If dbService is not initialized return FAILURE
                            if(! dbService.isInitialized()){
                                return response;
                            }
                            name = dbService.getQueueNameForIndex(message.index);
                            LinkedBlockingQueue<PipelineMessage> txLogQueue = QueueManager.instance().getQueue(name);
                            boolean addedToQueue = txLogQueue.offer(message, RESPONSE_WAIT_MILLIS, TimeUnit.MILLISECONDS);
                            if (addedToQueue) {
                                message.addEventTime(TransactionLogMessage.TLM_EVENT_MESSAGE_QUEUED, System.currentTimeMillis());
                                response = MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse.OK;
                            } else {
                                response = MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse.WAIT;
                            }
                        } catch (IllegalAccessException e) {
                            String msg = "Unable to get queue [" + name + "] for index [" + message.index + "] for Virtual Server [" + message.virtualServer + "]. This implies something really bad has happened in the system.";
                            log.error(msg);
                            remoteServerTenant.updateStateWithLastResponseSent(message.index, response); //would change state only in case of error condition.
                        } catch (Exception ex) {
                            log.error("Caught exception while processing transaction log for Virtual Server:" + message.virtualServer, ex);
                            remoteServerTenant.updateStateWithLastResponseSent(message.index, response); //would change state only in case of error condition.
                        }
                    } else {
                        /*A new session id is generated every time sync operation completes. Session id check protects against following race condition:
                            Step 1: client sends tx 1, tx2 that are queued on the server.
                            Step 2: client sends tx3, but http thread on server side doesn't schedule it.
                            Step 3: client times out and sends a Sync request that clears the queue on server after waiting for processing for tx1.
                            Step 4: Now both server and client are SYNC on tx1, so client sends tx2.
                            Step 5: tx3 thread on server finally shows up and inserts tx3 in queue out of order.
                            Step 6: tx2 request from client is received and inserted in queue. Now the queue is in bad shape.
                             To fix this scenario, every time SYNC completes we generate a new session id. Step 5 could have been avoided as follows:
                            Step 5: tx3 thread on server finally shows up, but session ID is different than current session id. Server discards the message and sends back a sync.
                            Step 5.1: The client already time out on that response so no one listens to response. */
                        log.warn("Received transaction log for undefined session from VirtualServer:" + message.virtualServer + ", sessionId received:" + message.sessionId + ", current session id:" + remoteServerTenant.getTenantSessionId(message.index) + "Message Received:" + param);
                        //Don't update the state here as it would lead to multiple unnecessary sync operations.
                        response = MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse.FAILURE;
                    }

                } else {
                    response = MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse.SYNC;
                }
            }

            log.warn("Tenant:" + message.virtualServer + ". Log request Processed. TxnId:" + message.transactionId + " CorrelationId:" + message.corelationId + " ThreadNum:" + message.index + ". Response sent:" + response);
        } catch (Exception ex) {
            log.error("Caught exception while processing transaction log.", ex);
        }
        if (log.isDebugEnabled()) {
            log.debug("Response returned for param:" + param + ".:" + response);
        }
        return response;
    }

    public MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse syncMetaInfo(String param) {
        MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse response = MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse.FAILURE;
        try {
            AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_MTXN_SERVER_REQ_SYNC);
            AsyncDBMetaInfo metaInfo = AsyncDBMetaInfoC.deserializeFromString(param);
            String peerVirtualServer = metaInfo.getPeerVirtualServer();
            RemoteTransactionServerTenantHolder remoteServerTenant = remoteTxHolders.get(peerVirtualServer);
            if (remoteServerTenant == null) {
                persistenceService.addTenant(peerVirtualServer);
                remoteServerTenant = remoteTxHolders.get(peerVirtualServer);
            }
            synchronized (remoteServerTenant) { //synchronize with process operation here for each remote tenant to avoid queue corruption.
                String name = "";
                int index = metaInfo.getThreadNum();
                remoteServerTenant.setTenantConsumerState(index, RemoteTransactionServerTenantConsumerState.SYNC); //Change the state here to avoid a race condition where a pending log request may be processed as current queue is cleared.
                try {
                    DBService dbService = remoteServerTenant.dbService;
                    // During shutdown we set the initialized flag to false. If dbService is not initialized return FAILURE
                    if(!dbService.isInitialized()){
                        return response;
                    }
                    name = dbService.getQueueNameForIndex(index);
                    LinkedBlockingQueue<PipelineMessage> txLogQueue = QueueManager.instance().getQueue(name);
                    txLogQueue.clear(); //clear the current queue
                    if (dbService.getEventHandlerPipeline().isQueueTaskProcessing(index, SYNC_WAIT_MILLIS, TimeUnit.MILLISECONDS)) {
                        response = MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse.WAIT;
                    } else {
                        AsyncDBMetaInfo dbMetaInfo = getMetaInfo(index, dbService);
                        if (log.isDebugEnabled()) {
                            log.debug("Requested Meta:" + metaInfo.serializeToString());
                            log.debug("DB Read Meta:" + (dbMetaInfo != null ? dbMetaInfo.serializeToString() : "null"));
                        }
                        if (dbMetaInfo == null) {
                            if (metaInfo.getTxFileName().equals(MultiTenantRemoteTransactionServerHelper.DEFAULT_META_FILE_NAME)) {
                                //Loading Db Meta for the Virtual Server Tenant for the first time....
                                String sessionId = String.valueOf(sessionIdGenerator.incrementAndGet());
                                response = new MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse(OK.getName());
                                response.addToResponse(sessionId);
                                remoteServerTenant.setTenantSessionId(index, sessionId);
                            } else {
                                log.error("Sync request received from " + peerVirtualServer + " didn't match the queried info for index:" + index);
                                log.error("Requested Meta:" + metaInfo.serializeToString());
                                log.error("DB Read Meta:null. Expected TxFileName:" + MultiTenantRemoteTransactionServerHelper.DEFAULT_META_FILE_NAME);
                                response = MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse.SYNC;
                            }
                        } else if ((dbMetaInfo.getTransactionId() == metaInfo.getTransactionId()) && StringUtils.equals(dbMetaInfo.getTxFileName(), metaInfo.getTxFileName())) {
                            //The combination of Tx file name and transaction id should be unique in the system.
                            String sessionId = String.valueOf(sessionIdGenerator.incrementAndGet());
                            response = new MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse(OK.getName());
                            response.addToResponse(sessionId);
                            remoteServerTenant.setTenantSessionId(index, sessionId);
                        } else {
                            log.error("Sync request received from " + peerVirtualServer + " didn't match the queried info for index:" + index);
                            log.error("Requested Meta:" + metaInfo.serializeToString());
                            log.error("DB Read Meta:" + dbMetaInfo.serializeToString() + ", Version:" + dbMetaInfo.getVersion() + ", Modified Time:" + dbMetaInfo.getModifiedDate());
                            response = MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse.SYNC;
                        }
                    }
                } catch (IllegalAccessException e) {
                    String msg = "Unable to get queue [" + name + "] for index [" + metaInfo.getThreadNum() + "] for Virtual Server [" + peerVirtualServer + "]. This implies something really bad has happened in the system.";
                    log.error(msg);
                } catch (Exception ex) {
                    log.error("Caught exception while processing transaction log for Virtual Server:" + peerVirtualServer, ex);
                } finally {
                    remoteServerTenant.updateStateWithLastResponseSent(index, response);
                }
            }
            log.warn("Tenant:" + peerVirtualServer + ". Sync request Processed. TxnId:" + metaInfo.getTransactionId() + " CorrelationId:" + metaInfo.getCorelationId() + " ThreadNum:" + metaInfo.getThreadNum() + ". Response sent:" + response);
        } catch (Exception ex) {
            log.error("Caught exception while processing transaction log.", ex);
        }
        if (log.isDebugEnabled()) {
            log.debug("Response returned for param:" + param + ".:" + response);
        }
        return response;
    }

    public MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse ping(String param) {
        MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse response = MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse.FAILURE;
        try {
            AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_MTXN_SERVER_REQ_PING);
            MultiTenantRemoteTransactionServerHelper.RemoteTransactionServerPing ping = MultiTenantRemoteTransactionServerHelper.RemoteTransactionServerPing.deserializeFromString(param);
            if (ping == null) {
                log.error("Error deserializing ping request:" + param);
                return response;
            }
            String virtualServer = ping.getVirtualServer();
            RemoteTransactionServerTenantHolder remoteServerTenant = remoteTxHolders.get(virtualServer);
            // During shutdown we set the initialized flag to false. If dbService is not initialized return FAILURE
            if (remoteServerTenant == null) {
                persistenceService.addTenant(virtualServer);
                remoteServerTenant = remoteTxHolders.get(virtualServer);
            }
            if(remoteServerTenant.getDBService() != null && !remoteServerTenant.getDBService().isInitialized()){
                return response;
            }
            synchronized (remoteServerTenant) { //synchronize with sync operation here for each remote tenant to avoid queue corruption.
                if (remoteServerTenant.getTenantConsumerState(ping.getThreadNum()) == RemoteTransactionServerTenantConsumerState.PROCESS) {
                    response = MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse.OK;
                } else {
                    response = MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse.SYNC;
                }
            }

        } catch (Exception ex) {
            log.error("Caught exception while processing transaction log.", ex);
        }
        if (log.isDebugEnabled()) {
            log.debug("Response returned for param:" + param + ".:" + response);
        }
        return response;
    }

    protected synchronized AsyncDBMetaInfo getMetaInfo(int index, DBService dbService) throws PersistenceException {
        //making the method synchronized as log level for top link session level needs to be enabled and set back for the duration of the query.
        return dbService.getTransactionLog().getMetaFileManager().queryMetaInfoObjectForIndex(index);
    }

    public Set<String> getTenants() {
        return remoteTxHolders.keySet();
    }

    public void addTenant(DBService dbService) {
        String tenant = dbService.getPeerVirtualServer();
        instanceLock.lock();
        try {
            if (!remoteTxHolders.containsKey(tenant)) {
                log.info("Multi tenant Transaction Server: Adding tenant:" + tenant);
                remoteTxHolders.put(tenant, new RemoteTransactionServerTenantHolder(dbService, tenant));
            }
        } finally {
            instanceLock.unlock();
        }
    }

    /**
     * To be used by unit tests only.
     * This function is provided for unit tests to avoid dbservice setup.
     *
     * @param virtualServer
     * @param dbService
     */
    void innerAddTenant(String virtualServer, DBService dbService) {
        instanceLock.lock();
        try {
            remoteTxHolders.put(virtualServer, new RemoteTransactionServerTenantHolder(dbService, virtualServer));
        } finally {
            instanceLock.unlock();
        }
    }

    public void removeTenant(String virtualServer) throws Exception {
        instanceLock.lock();
        try {
            log.info("Multi tenant Transaction Server: Removing tenant:" + virtualServer);
            RemoteTransactionServerTenantHolder tenantHolder = remoteTxHolders.remove(virtualServer);
            if (tenantHolder != null) {
                DBService dbService = tenantHolder.dbService;
                for (int index = 0; index < dbService.getNumQueues(); ++index) {
                    String name = dbService.getQueueNameForIndex(index);
                    LinkedBlockingQueue<PipelineMessage> txLogQueue = QueueManager.instance().getQueue(name);
                    txLogQueue.clear(); //clear the current queue
                    while (dbService.getEventHandlerPipeline().isQueueTaskProcessing(index, 2500, TimeUnit.MILLISECONDS)) {
                        log.warn("Waiting for Queue Task for Queue:" + name + " for Virtual Server:" + virtualServer + "and index:" + index + " to finish after 2500 milliseconds.");
                    }
                }
                dbService.stop();
            }
        } finally {
            instanceLock.unlock();
        }
    }

    public DBService getDBServiceForTenant(String tenantName){
      if(remoteTxHolders.get(tenantName) != null){
          return remoteTxHolders.get(tenantName).getDBService();
      }
        return null;
    }

    private class RemoteTransactionServerTenantHolder {
        final String virtualServer;
        final DBService dbService;
        List<RemoteTransactionServerTenantConsumerState> tenantConsumersState;
        List<String> tenantSessionIds;


        public RemoteTransactionServerTenantHolder(DBService dbService, String virtualServer) {
            this.dbService = dbService;
            int size = dbService.getNumQueues();
            this.virtualServer = virtualServer;
            tenantConsumersState = new Vector<RemoteTransactionServerTenantConsumerState>(size);
            for (int i = 0; i < size; ++i) {
                tenantConsumersState.add(RemoteTransactionServerTenantConsumerState.SYNC);
            }
            tenantSessionIds = new Vector<String>(size);
            for (int i = 0; i < size; ++i) {
                tenantSessionIds.add(String.valueOf(-1));
            }
        }

        public RemoteTransactionServerTenantConsumerState getTenantConsumerState(int index) {
            return tenantConsumersState.get(index);
        }

        public String getTenantSessionId(int index) {
            return tenantSessionIds.get(index);
        }

        public void updateStateWithLastResponseSent(int index, MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse response) {
            if (OK.equals(response)) {
                tenantConsumersState.set(index, RemoteTransactionServerTenantConsumerState.PROCESS);
            } else if (FAILURE.equals(response) || SYNC.equals(response)) {
                tenantConsumersState.set(index, RemoteTransactionServerTenantConsumerState.SYNC);
            }
        }


        public void setTenantConsumerState(int index, RemoteTransactionServerTenantConsumerState response) {
            tenantConsumersState.set(index, response);
        }

        public void setTenantSessionId(int index, String sessionId) {
            tenantSessionIds.set(index, sessionId);
        }

        public DBService getDBService(){
            return dbService;
        }
    }

    private enum RemoteTransactionServerTenantConsumerState {
        SYNC, PROCESS
    }

}
