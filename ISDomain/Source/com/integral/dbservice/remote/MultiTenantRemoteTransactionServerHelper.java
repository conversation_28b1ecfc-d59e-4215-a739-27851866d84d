package com.integral.dbservice.remote;

import com.integral.dbservice.TransactionLogMessage;
import com.integral.transaction.AsyncDBMetaInfo;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Oct 1, 2010
 * Time: 2:09:51 PM
 * Helper for handling remote transaction server operations.
 */
public interface MultiTenantRemoteTransactionServerHelper {

    String DEFAULT_META_FILE_NAME = "MultiTenantRemoteTransactionServer.Default"; //This property is used when no meta object is found in db for a peervirtual server and index combination.

    String SYNC_PARAM_NAME = "Sync";

    String REQ_PARAM_NAME = "Req";

    String EQUALS = "=";

    String RESPONSE_SEPARATOR = ":";

    String getRemoteURL();

    MultiTenantTransactionServerResponse sendTransactionLogMessage(TransactionLogMessage tlm, int timeout);

    MultiTenantTransactionServerResponse sendSyncMetaInfo(AsyncDBMetaInfo metaInfo, int timeout);

    MultiTenantTransactionServerResponse ping(RemoteTransactionServerPing ping, int timeout);

    public static class RemoteTransactionServerPing {
        private static final String PIPE = "|";
        private final String virtualServer;
        private final int threadNum;

        public RemoteTransactionServerPing(String virtualServer, int threadNum) {
            this.virtualServer = virtualServer;
            this.threadNum = threadNum;
        }

        public String serializeToString() {
            StringBuilder result = new StringBuilder();
            result.append(virtualServer).append(PIPE).append(threadNum);
            return result.toString();
        }

        public static RemoteTransactionServerPing deserializeFromString(String pingInfo) {
            RemoteTransactionServerPing result = null;
            String[] tokens = pingInfo.split("\\" + PIPE);
            for (int i = 0; i < tokens.length; ++i) {
                if ("null".equals(tokens[i])) {
                    tokens[i] = null;
                }
            }
            if (tokens.length == 2) {
                result = new RemoteTransactionServerPing(tokens[0], Integer.parseInt(tokens[1]));
            }
            return result;
        }

        public String getVirtualServer() {
            return virtualServer;
        }

        public int getThreadNum() {
            return threadNum;
        }
    }

    public static class MultiTenantTransactionServerResponse {

        public final static MultiTenantTransactionServerResponse OK = new MultiTenantTransactionServerResponse("OK");
        public final static MultiTenantTransactionServerResponse WAIT = new MultiTenantTransactionServerResponse("WAIT");
        public final static MultiTenantTransactionServerResponse SYNC = new MultiTenantTransactionServerResponse("SYNC");
        public final static MultiTenantTransactionServerResponse FAILURE = new MultiTenantTransactionServerResponse("FAILURE");

        private final String name;
        private String responseString;

        public MultiTenantTransactionServerResponse(String name) {
            this.name = name;
        }

        public void addToResponse(String responseString) {
            this.responseString = getName() + MultiTenantRemoteTransactionServerHelper.RESPONSE_SEPARATOR + responseString;
        }

        @Override
        public String toString() {
            if (responseString != null) {
                return responseString;
            }
            return getName();
        }

        public String getResponseString() {
            return responseString;
        }

        public static MultiTenantTransactionServerResponse getFromResponseString(String responseString) {
            String name = responseString;
            int separatorIdx = responseString.indexOf(MultiTenantRemoteTransactionServerHelper.RESPONSE_SEPARATOR);
            if (separatorIdx != -1) {
                name = name.substring(0, separatorIdx);
            }
            MultiTenantTransactionServerResponse result = new MultiTenantTransactionServerResponse(name);
            result.responseString = responseString;
            return result;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof MultiTenantTransactionServerResponse)) return false;

            MultiTenantTransactionServerResponse that = (MultiTenantTransactionServerResponse) o;

            if (name != null ? !name.equals(that.name) : that.name != null) return false;

            return true;
        }

        @Override
        public int hashCode() {
            return name != null ? name.hashCode() : 0;
        }

        public String getName() {
            return name;
        }
    }
}


