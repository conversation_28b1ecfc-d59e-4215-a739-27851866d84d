package com.integral.dbservice.remote;

import com.integral.dbservice.TransactionLogMessage;
import com.integral.dbservice.txlog.TransactionLog;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.PersistenceException;
import com.integral.transaction.AsyncDBMetaInfo;
import com.integral.transaction.AsyncDBMetaInfoC;

import java.io.IOException;

import static com.integral.dbservice.remote.MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse.*;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: Sep 28, 2010
 * Time: 11:35:51 AM
 * The client class to handle transactions with remote tx server.
 */
public class MultiTenantRemoteTxServerClient {

    private static final Log log = LogFactory.getLog(MultiTenantRemoteTxServerClient.class);
    private static final int TX_SEND_INTERVAL = 500; //expressed in milliseconds. Send Operation should be completed faster as it should return after adding to local queue. TODO, should we make it configurable?
    private static final int TX_SYNC_INTERVAL = 5000; //expressed in milliseconds. Sync Operation takes longer as it queries the db. TODO, should we make it configurable?
    private static final int TX_PING_INTERVAL = 5000; //expressed in milliseconds. TODO, should we make it configurable?
    private static final int SYNC_NUM_ATTEMPTS_WARN_THRESHOLD = 5;
    private static final int PING_NUM_ATTEMPTS_WARN_THRESHOLD = 10;

    private volatile boolean isShutdown = false;
    private final TransactionLog txLogSystem;
    private MultiTenantRemoteTransactionServerHelper remoteTxServerHelper;
    private String remoteURL;
    private boolean isSetup = false;
    private String currentSessionId;
    private final String virtualServer;
    protected final int index; //Created one client per TransactionLog.
    private volatile String nextRemoteURL; //indicates the location of the new Remote server. Set when the tenant is moved.

    public MultiTenantRemoteTxServerClient(TransactionLog txLogSystem, String remoteURL, int index) {
        this(txLogSystem, new MultiTenantRemoteTransactionServerHelperImpl(txLogSystem.getDbService().getConfig(), remoteURL), txLogSystem.getDbService().getPeerVirtualServer(), index);
    }

    /**
     * This constructor is used by Unit Tests to inject dependencies.
     *
     * @param txLogSystem
     * @param remoteHelper
     * @param virtualServerName
     * @param index
     */
    public MultiTenantRemoteTxServerClient(TransactionLog txLogSystem, MultiTenantRemoteTransactionServerHelper remoteHelper, String virtualServerName, int index) {
        this.txLogSystem = txLogSystem;
        remoteTxServerHelper = remoteHelper;
        this.index = index;
        this.remoteURL = remoteTxServerHelper.getRemoteURL();
        this.nextRemoteURL = remoteURL;
        virtualServer = virtualServerName;
    }

    /**
     * A blocking method that sends the given transaction log message to remote server.
     * The function blocks until the msg is successfully sent. if an error is received
     * while sending, the function retries after syncing with the server.
     *
     * @param tlm transaction log message to be sent
     */
    public void sendTransactionMessage(TransactionLogMessage tlm) {
        boolean sent = false;
        do {
            if (isSetup) { //should sync meta state before sending any log messages during startup.
                tlm.virtualServer = virtualServer; //set the virtual server required by the remote Tx Server.
                tlm.sessionId = currentSessionId; //set the session id required by the remote Tx Server.
                tlm.addEventTime(TransactionLogMessage.TLM_EVENT_REMOTE_SENT, System.currentTimeMillis());
                MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse res = remoteTxServerHelper.sendTransactionLogMessage(tlm, TX_SEND_INTERVAL); //timeout in milliseconds
                log.warn("Sent::" + tlm.transactionId + ":" + tlm.getCorelationId() + ":" + tlm.getEventName() + ":" + tlm.index + ":" + res);
                if (log.isDebugEnabled()) {
                    log.debug("Log Request Sent:" + tlm.serializeToString() + ". Response received:" + res);
                }
                if (res != null) {
                    if (OK.equals(res)) {
                        sent = true;
                    } else if (WAIT.equals(res)) {
                        try {
                            Thread.sleep(TX_SEND_INTERVAL);
                        } catch (InterruptedException e) {
                            //check for shutdown.....
                        }
                    } else {

                        boolean doSync = false;
                        if (FAILURE.equals(res)) {
                            doSync = true;
                            if (!remoteURL.equals(nextRemoteURL)) {
                                log.warn("MultiTenantRemoteTxServerClient. Received new Remote txn Server URL: " + nextRemoteURL);
                                remoteURL = nextRemoteURL;
                                remoteTxServerHelper = new MultiTenantRemoteTransactionServerHelperImpl(txLogSystem.getDbService().getConfig(), remoteURL);
                                isSetup = false;
                            } else {
                                try {
                                    Thread.sleep(TX_SEND_INTERVAL); //give enough time for the server to finish current processing before sending the sync request.
                                } catch (InterruptedException e) {
                                    //ignore...
                                }
                            }
                        }
                        if (doSync || SYNC.equals(res)) {
                            syncRemoteServerState(); //after the sync, don't send the current message, instead let reader read fresh message where sync has pointed and then try sending that instead.
                            sent = true;
                            break;
                        }
                    }
                }
            } else {
                syncRemoteServerState();
                sent = true; //after the sync, don't send the current message, instead let reader read fresh message where sync has pointed and then try sending that instead.
            }
        } while (!isShutdown && !sent);
    }

    /**
     * A blocking method that pings remote server.
     * The function blocks until it is sure remote server is up and in sync.
     */
    public void pingRemoteTxnServer() {
        int numAttempts = 0;
        do {
            MultiTenantRemoteTransactionServerHelper.RemoteTransactionServerPing ping = new MultiTenantRemoteTransactionServerHelper.RemoteTransactionServerPing(virtualServer, index);
            MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse res = remoteTxServerHelper.ping(ping, TX_SEND_INTERVAL); //timeout in milliseconds
            numAttempts++;
            if (log.isDebugEnabled()) {
                log.debug("HeartBeat Request Sent. Response received:" + res);
            }
            if (res != null) {
                if (OK.equals(res)) {
                    break; //ping successful.
                } else if (SYNC.equals(res)) {
                    syncRemoteServerState();
                    break; //ping done after sync.
                } else {
                    //Failure, Wait or Void case.
                    //log.warn("HeartBeat Request Sent to remote server:" + remoteURL + ". Response received:" + res);
                    if (!remoteURL.equals(nextRemoteURL)) {
                        log.warn("MultiTenantRemoteTxServerClient. Received new Remote txn Server URL: " + nextRemoteURL);
                        remoteURL = nextRemoteURL;
                        remoteTxServerHelper = new MultiTenantRemoteTransactionServerHelperImpl(txLogSystem.getDbService().getConfig(), remoteURL);
                        isSetup = false;
                        numAttempts = 0;
                    }
                }

            }
            if (numAttempts % PING_NUM_ATTEMPTS_WARN_THRESHOLD == 0) {
            	StringBuilder data = new StringBuilder();            	
            	data.append(remoteURL);
            	data.append(". NumAttempts:");
            	data.append(numAttempts);
            	data.append(". Last Response Received:");
            	data.append(res);
            	MessageLogger.getInstance().log(ISAlertMBean.ALERT_EVENT_UNABLE_TO_PING_REMOTE_HOST, "MultiTenantRemoteTxServerClient.pingRemoteTxnServer()", "Unable to ping to Multi-Tennat Remote Host", data.toString());
                log.warn("Unable to ping to Multi-Tennat Remote Host:" + data.toString());
            }
            try {
                Thread.sleep(TX_PING_INTERVAL); //Try pinging again after a while.
            } catch (InterruptedException e) {
                //ignore...
            }
        } while (!isShutdown);
    }

    /**
     * a blocking method that returns only after syncing the state of the reader task with the remote tx server.
     */
    private void syncRemoteServerState() {
        boolean sync = false;
        MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse res = MultiTenantRemoteTransactionServerHelper.MultiTenantTransactionServerResponse.FAILURE;
        int numAttempts = 0;
        do {
            if (numAttempts > SYNC_NUM_ATTEMPTS_WARN_THRESHOLD) {
            	StringBuilder data = new StringBuilder();
            	data.append(remoteURL);
            	data.append(" for index:");
            	data.append(index);
            	data.append(" in last ");
            	data.append(SYNC_NUM_ATTEMPTS_WARN_THRESHOLD);
            	data.append("attempts. Last State returned from server:");
            	data.append(res);            	
            	MessageLogger.getInstance().log(ISAlertMBean.ALERT_EVENT_UNABLE_TO_SYNC_WITH_REMOTE_HOST, "MultiTenantRemoteTxServerClient.syncRemoteServerState()", "Unable to sync with remote server", data.toString());
                log.error("Unable to sync with remote server:" + data.toString());
                numAttempts = 0;
            }
            numAttempts++;
            try {
                AsyncDBMetaInfo metaInfo = getMetaInfo();
                if (metaInfo == null) {
                    //Can happen for a new VS
                    metaInfo = new AsyncDBMetaInfoC();
                    metaInfo.setPeerVirtualServer(virtualServer);
                    metaInfo.setThreadNum(index);
                    metaInfo.setTxFileName(MultiTenantRemoteTransactionServerHelper.DEFAULT_META_FILE_NAME);
                }
                res = remoteTxServerHelper.sendSyncMetaInfo(metaInfo, TX_SYNC_INTERVAL);
                if (log.isDebugEnabled()) {
                    log.debug("Sync Request Sent:" + metaInfo.serializeToString() + ". Response received:" + res);
                }
                if (OK.equals(res)) {
                    resetReader(metaInfo);
                    String[] values = res.getResponseString().split(MultiTenantRemoteTransactionServerHelper.RESPONSE_SEPARATOR);
                    if (values.length != 2) {
                        log.error("Multi Tenant Txn Server Protocol Broken. Invalid response received for Sync operation:" + res.getResponseString());
                        return;
                    }
                    currentSessionId = values[1]; //reset sessionID after SYNC operation.
                    sync = true;
                } else if (SYNC.equals(res)) {
                    log.warn("Sync info sent didn't match with the server sync Info. Meta Info sent:" + metaInfo.serializeToString() + " Version:" + metaInfo.getVersion() + ", Modified Date:" + metaInfo.getModifiedDate());
                } else {
                    if (FAILURE.equals(res)) {
                        if (!remoteURL.equals(nextRemoteURL)) {
                            log.warn("MultiTenantRemoteTxServerClient. Received new Remote txn Server URL: " + nextRemoteURL);
                            remoteURL = nextRemoteURL;
                            remoteTxServerHelper = new MultiTenantRemoteTransactionServerHelperImpl(txLogSystem.getDbService().getConfig(), remoteURL);
                            numAttempts = 0; //try to sync with the new server.
                        }
                    }
                }
            } catch (Exception ex) {
                log.error("Exception while querying the metaInfo", ex);
            }
            if (!sync && !isShutdown) {
                try {
                    Thread.sleep(TX_SYNC_INTERVAL);
                } catch (InterruptedException e) {
                    if (!isShutdown) {
                        log.error("Interrupted while waiting for Sync. ShutDownState: false", e);
                    } else {
                        log.warn("Interrupted while waiting for Sync. ShutDownState: true", e);
                    }
                }
            }
        } while (!isShutdown && !sync);
        isSetup = true;
        log.info("Sync Operation for index:" + index + " complete with remote Tx Server:" + remoteURL);
    }

    protected synchronized AsyncDBMetaInfo getMetaInfo() throws PersistenceException {
        //making the method synchronized as log level for top link session level needs to be enabled and set back for the duration of the query.
        AsyncDBMetaInfo metaInfo = txLogSystem.getMetaFileManager().queryMetaInfoObjectForIndex(index);
        return metaInfo;
    }

    public void shutDown() {
        isShutdown = true;
    }

    protected void resetReader(AsyncDBMetaInfo metaInfo) {
        try {
            txLogSystem.resetReaderTask(index, metaInfo);
        } catch (IOException e) {
            String message = "Unexpected error occurred while trying to reset the reader task after sync.";
            log.error(message, e);
        }
    }

    public void setNewRemoteURL(String newRemoteURL) {
        this.nextRemoteURL = newRemoteURL;
    }
}
