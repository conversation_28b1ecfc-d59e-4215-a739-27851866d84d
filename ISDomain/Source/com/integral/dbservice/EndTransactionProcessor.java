package com.integral.dbservice;

import com.integral.dbservice.txlog.TransactionLog;
import com.integral.is.common.cache.DistributedCacheFactory;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.log.MessageLogger;
import com.integral.message.ErrorMessage;
import com.integral.mtf.MTFServices;
import com.integral.mtf.config.ConfigManager;
import com.integral.mtf.config.MTFMessageConfig;
import com.integral.mtf.message.MTFEventMessage;
import com.integral.mtf.service.messaging.ErrorCodes;
import com.integral.mtf.service.messaging.MTFEventMessageSenderService;
import com.integral.persistence.ClusterCommitEventAdapterC;
import com.integral.pipeline.Pipeline;
import com.integral.pipeline.PipelineException;
import com.integral.pipeline.PipelineMessage;
import com.integral.pipeline.Processor;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;

/**
 * The objective of this processor is to try and commit an existing transaction, if there were no errors earlier in the {@link Pipeline}.
 * Errors in pipeline processing are available as part of {@link PipelineMessage#getErrors()}. 
 * 
 * This processor also acts as an {@link Pipeline#getErrorHandler() error handler} is there were errors previously in the execution of the pipeline.
 * A separate error processor is written, so this handler no longer handles exceptions. Refer to DBPersistPipelineErrorProcessor for more details.
 * <AUTHOR>
 *
 */
public class EndTransactionProcessor extends Processor {
	TransactionLog txLogSystem;
	private final int numLogFiles;
	
	public EndTransactionProcessor(TransactionLog txLogSystem) {
		super();
		this.txLogSystem = txLogSystem;
		numLogFiles = txLogSystem.getDbService().getConfig().getNumPersistenceThreads();
	}
	
	@Override
	public PipelineMessage process(PipelineMessage msg) {
		TransactionLogMessage tlm = (TransactionLogMessage) msg;
    	IdcTransaction tx = IdcSessionManager.getInstance().getTransaction();
    	
        try {
        	
        	if( tx == null ) // if txn is null then call updateMetaInformation which will use internal local txn. 
        	{
        		log.warn("Tenant:"+ txLogSystem.getDbService().getPeerVirtualServer()+ " .ETP: Transaction is null. Persisting META FILE");
	            updateMetaInformation( tlm ); // Just update META file and move ahead.
	            return msg;
        	}

            // make multi-app message sending configurable.
            if ( !ISFactory.getInstance().getISMBean().isDealingTransactionMultiAppUpdateEnabled() )
            {
                final String eventName = tlm.getEventName();
                final String multiAppProp = ( String ) tx.getUOW().getProperty( ClusterCommitEventAdapterC.MULTI_APP );
                if ( multiAppProp == null || ClusterCommitEventAdapterC.MULTI_APP_DISABLED.equals( multiAppProp ) ) // if an event handler already marked it as enabled then, don't disable it.
                {
                    final boolean eventEnabledForMultiApp = ISFactory.getInstance().getISMBean().isDealingTransactionMultiAppUpdateEnabled( eventName );
                    tx.getUOW().setProperty( ClusterCommitEventAdapterC.MULTI_APP, eventEnabledForMultiApp ? ClusterCommitEventAdapterC.MULTI_APP_ENABLED : ClusterCommitEventAdapterC.MULTI_APP_DISABLED );
                    log.info( new StringBuilder( 200 ).append( "ETP.process : set multi-app message flag for tx=" ).append( tx )
                            .append( ",eventName=" ).append( eventName ).append( ",multiAppFlag=" ).append( multiAppProp )
                            .append( ",eventEnabledForMultiApp=" ).append( eventEnabledForMultiApp ).toString() );
                }
                else
                {
                    log.info( new StringBuilder( 200 ).append( "ETP.process : multi-app flag is already set for tx=" ).append( tx )
                            .append( ",eventName=" ).append( eventName ).append( ",multiAppFlag=" ).append( multiAppProp ).toString() );
                }
            }

            // Update Meta File and call commit txn.
	        updateMetaInformation( tlm );
	        long startTime = System.currentTimeMillis();
	        tx.commit();
            log.warn("Tenant:"+ txLogSystem.getDbService().getPeerVirtualServer()+ " .ETP : Transaction " + tx + " commited by " + tlm.eventName + ". Time taken - " + (System.currentTimeMillis() - startTime));
            if( tlm.message != null )
            {
                DistributedCacheFactory.getInstance().getDistributedCache().sendMessage( tlm.message );
            }

            if(tlm.mtfEventMessage !=null){
                sendMTFEventMessage(tlm.mtfEventMessage);
            }

        } 
        catch ( Exception ex )
        {
        	log.error("Tenant:"+ txLogSystem.getDbService().getPeerVirtualServer()+ " .ETP : Execption " + ex );
        	throw new PipelineException("Unable to commit transaction.",ex);            
        }
		return msg;
	}
	
	private void updateMetaInformation(TransactionLogMessage tlm){
        int txLogIndex = tlm.getCorelationIndex(numLogFiles);        
        txLogSystem.updateMetaInformation(txLogIndex, tlm);
	}

    private void sendMTFEventMessage(MTFEventMessage message) {
	    try {
            MTFMessageConfig messageConfig = ConfigManager.getMtfMessageConfig();
            if (messageConfig.isMtfEventMessagingEnabled() && messageConfig.isMtfEventMessageSenderEnabled()) {
                MTFEventMessageSenderService senderService = MTFServices.getSenderService();
                ErrorMessage errorMessage = senderService.sendMessage(message);
                if (errorMessage != null) {
                    log.warn("sendMTFEventMessage : Failed for message=" + message);
                    MessageLogger.getInstance().log(ErrorCodes.MTFEventMessagePublishFailed.getAlertCode(), this.getClass().getSimpleName()
                            , "MTFEventMessage Publish Failed - SenderServiceError", "message=" + message.toString());
                }
            }
            else {
                if (log.isDebugEnabled()) {
                    log.debug("sendMTFEventMessage : Not sending message = " + message + ", isMtfEventMessagingEnabled=" + messageConfig.isMtfEventMessagingEnabled() +
                            ", isMtfEventMessageSenderEnabled=" + messageConfig.isMtfEventMessageSenderEnabled() +
                            ", SenderService=" + (MTFServices.getSenderService() != null));
                }
            }
        }
        catch (Throwable th){
	        log.error("sendMTFEventMessage : Failed for message="+message+", cause="+th);
	        if( log.isDebugEnabled() ){
                log.debug("sendMTFEventMessage : Failed for message="+message,th);
            }
            log.warn("sendMTFEventMessage : Failed for message="+message);
            MessageLogger.getInstance().log(ErrorCodes.MTFEventMessagePublishFailed.getAlertCode(),this.getClass().getSimpleName()
                    ,"MTFEventMessage Publish Failed - Exception","message="+message.toString());
        }
    }
}
