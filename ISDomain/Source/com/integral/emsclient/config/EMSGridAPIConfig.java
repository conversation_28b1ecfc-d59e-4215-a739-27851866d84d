package com.integral.emsclient.config;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.integral.gridapi.client.config.GridApiConfig;
import com.integral.system.configuration.IdcMBeanC;

/**
 * 
 * <AUTHOR>
 *
 */
public class EMSGridAPIConfig extends IdcMBeanC
{
	
	protected ConcurrentHashMap<String, GridApiConfig> gridApiConfigMap;
	
	public static final String RW_GRID_API_CONNECTION_CONFIG = "IDC.EMS.GridApi.Config";
	
	
	public EMSGridAPIConfig()
	{
		super("com.integral.emsclient.config.EMSConfig");
		initialize();
	}
	
	public Map<String, GridApiConfig> getGridApiConfiguration()
	{
		return this.gridApiConfigMap;
	}

	@Override
	public final void initialize()
	{
		if ( gridApiConfigMap == null )
		{
			gridApiConfigMap = new ConcurrentHashMap<String, GridApiConfig>();
		}
		GridApiConfig.populateGridApiConfigMap(initSingleSuffixStringPropertyMap(RW_GRID_API_CONNECTION_CONFIG, null), gridApiConfigMap);
	}
	
	public EMSGridAPIConfig(String aPropertyFileName) {
        super(aPropertyFileName);
    }

}
