package com.integral.emsclient;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import com.integral.emsclient.config.EMSGridAPIConfig;
import com.integral.executionservice.client.api.APIOrderRequest;
import com.integral.executionservice.client.api.APIOrderResponse;
import com.integral.executionservice.client.api.APIOrderResponse.Status;
import com.integral.executionservice.client.api.APIOrderResponseC;
import com.integral.gridapi.client.GridAPIConnection;
import com.integral.gridapi.client.GridAPIConnectionManager;
import com.integral.gridapi.client.config.GridApiConfig;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.util.CompositeKeys;

/**
 * 
 * Grid API client for EMS integration
 * <AUTHOR>
 *
 */
public class EMSClient
{
	private GridAPIConnectionManager connectionManager;
	private EMSGridAPIConfig config;
	private Set<CompositeKeys> connectionKeys = new HashSet<CompositeKeys>();
	private Log log = LogFactory.getLog(EMSClient.class);

	public boolean start()
	{
		try
		{
			connectionManager = GridAPIConnectionManager.getInstance();
			config = new EMSGridAPIConfig();
		}
		catch ( Exception e )
		{
			log.error("Initialization Failed for EMSClient", e);
			return false;
		}
		return true;
	}

	public boolean stop()
	{
		try
		{
			connectionManager.stop(connectionKeys);
		}
		catch ( Exception e )
		{
			log.error(".stop Error in shutting down EMSClient", e);
			return false;
		}
		return true;
	}

	public APIOrderResponse placeOrder( APIOrderRequest orderRequest )
	{
		APIOrderResponse response = new APIOrderResponseC(orderRequest.getOrder());
		try
		{
			GridApiConfig gridAPIConfig = config.getGridApiConfiguration().get(orderRequest.getOrder().getCustomerOrg());
			boolean success = connectionManager.processOrderTask(gridAPIConfig, orderRequest);
			if ( success )
			{
				response.setStatus(Status.SUCCESS);
				return response;
			}
		}
		catch ( Exception e )
		{
			log.error(".placeOrder Error Placing Order->" + orderRequest, e);
		}
		response.setStatus(Status.FAILED);
		return response;
	}

	/**
	 * Initialize GridAPI connection and start heartbeat task
	 * @param org
	 * @return true if successful
	 */
	public boolean initializeGridAPIConnection(String org)
	{
		GridApiConfig gridApiConfig = config.getGridApiConfiguration().get(org);
		if ( gridApiConfig == null )
		{
			log.warn("Initialization of EMSClient Grid API connection  FAILED for " + org + " No grid config found");
			return false;
		}

		return initializeGridAPIConnection(gridApiConfig);
	}
	
	private boolean initializeGridAPIConnection(GridApiConfig config)
	{

		GridAPIConnection connection = null;
		try
		{
			connection = connectionManager.getConnection(config);
		}
		catch ( Exception e )
		{
			log.error("Exception in creating GridAPI connection", e);
		}
		if ( connection == null )
		{
			log.warn("Initialization of EMSClient Grid API connection  FAILED for org " + config.getOrg());
		}
		else
		{
			connectionManager.startHearbeatTask(config);
			connectionKeys.add(connection.getConnectionKey());
			log.info("Initialization of EMSClient Grid API connection  SUCCESSFUL for org " + config.getOrg());
			return true;
		}

		return false;
	}
	
	public void initializeAllGridAPIConnections()
	{
		Map<String, GridApiConfig> configsMap = config.getGridApiConfiguration();
		for ( Map.Entry<String, GridApiConfig> entry : configsMap.entrySet() )
		{
			initializeGridAPIConnection(entry.getValue());
		}
	}
}
