package com.integral.finance.marketdata.fx.calculator;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.integral.aggregation.price.FXPrice;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.marketData.MarketTierPrice;
import com.integral.finance.marketData.MarketTierPriceC;
import com.integral.finance.marketData.MarketTierPriceChangeSet;
import com.integral.log.Log;
import com.integral.log.LogFactory;

public class FXPriceBookChangeCalculator implements MarketSnapshotChangeCalculator<FXPriceBook, FXPrice> {
	protected Log log = LogFactory.getLog(this.getClass());

	public MarketTierPriceChangeSet computeChange( FXPriceBook latest, FXPriceBook last, CurrencyPair ccyPair )
	{
		if ( log.isDebugEnabled() )
		{
			StringBuilder logSb = new StringBuilder(this.getClass().getName()).append("PriceBook Latest {");
			logPriceBook(latest, ccyPair, logSb);
			logSb.append(" } ");

			if ( last != null )
			{
				logSb.append(" Last {");
				logPriceBook(last, ccyPair, logSb);
				logSb.append(" }");
			}

			log.debug(logSb.toString());
		}

		MarketTierPriceChangeSet priceChangeSet = new MarketTierPriceChangeSet();
		boolean isLastBookAvailable = last != null;

		if ( latest == null )
			return null;

		if ( isLastBookAvailable )
		{
			computePriceChange(priceChangeSet, ccyPair, latest.getBids(), last.getBids(), true);
			computePriceChange(priceChangeSet, ccyPair, latest.getOffers(), last.getOffers(), false);
		}
		else
		{
			computePriceChange(priceChangeSet, ccyPair, latest.getBids(), true);
			computePriceChange(priceChangeSet, ccyPair, latest.getOffers(), false);
		}

		if ( log.isDebugEnabled() )
		{
			StringBuilder logSb = new StringBuilder("MarketTierPriceChangeSet {");
			logMarketTierPriceChangeSet(priceChangeSet, logSb);
			logSb.append(" }");

			log.debug(logSb.toString());
		}

		return priceChangeSet;
	}

	protected void computePriceChange( MarketTierPriceChangeSet priceChangeSet, CurrencyPair ccyPair, Collection<FXPrice> latestPrices, boolean isBid )
	{

		for ( FXPrice latestPrice : latestPrices )
		{
            String priceId = generateNewPriceId(latestPrice, ccyPair, isBid);
			priceChangeSet.addMarketTierPrice(createNewMarketTierPrice(latestPrice, isBid, priceId));
		}
	}

	protected void computePriceChange( MarketTierPriceChangeSet priceChangeSet, CurrencyPair ccyPair, Collection<FXPrice> latestPrices, Collection<FXPrice> lastPrices, boolean isBid )
	{
		List<FXPrice> _lastPrices = new ArrayList<FXPrice>(lastPrices);

		// Iterating with latest price book
		for ( FXPrice latestPrice : latestPrices )
		{
			boolean isPriceMatched = false;
			boolean isRateMatched = false;
			FXPrice matchedLastPrice = null;

			// Match each latest book price with the last price book to get the difference.
			for ( FXPrice lastPrice : _lastPrices )
			{
                if ( isSame(latestPrice, lastPrice) )
				{
					isPriceMatched = true;
					matchedLastPrice = lastPrice;
					break;
				}
				else if ( isSameRate(latestPrice, lastPrice) )
				{
					isRateMatched = true;
					matchedLastPrice = lastPrice;
					break;
				}
			}

			// If complete price is matched, copy the last price Id to the latest price object and
			// remove the matched price entry from the temporary last price collection. This price won't be
			// considered as part of incremental update.
			if ( isPriceMatched )
			{
				_lastPrices.remove(matchedLastPrice);
			}
			else
			{
				// If only rate and provider of prices are matched, copy the last price Id to the latest price object
				// and remove the matched price entry from the temporary last price collection. This is an update action.
				if ( isRateMatched )
				{
					_lastPrices.remove(matchedLastPrice);
				}

                String priceId = generateNewPriceId(latestPrice, ccyPair, isBid);
				// Add the new NewMarketTierPrice update.
				priceChangeSet.addMarketTierPrice(createNewMarketTierPrice(latestPrice, isBid, priceId));
			}
		}

		// Add the delete MarketTierPrice for all the remaining prices of temporary last prices collection. This is a delete action.
		for ( FXPrice lastPrice : _lastPrices )
		{
            String priceId = generateNewPriceId(lastPrice, ccyPair, isBid);
            priceChangeSet.addMarketTierPrice(createDeleteMarketTierPrice(lastPrice, isBid, priceId));
		}
	}

	public String generateNewPriceId( FXPrice latestPrice, CurrencyPair ccyPair, boolean isBid )
	{
		return MarketTierPriceIdGenerator.generateNewId(latestPrice, ccyPair, isBid);
	}

	protected boolean isSameRate( FXPrice latestPrice, FXPrice lastPrice )
	{
		return latestPrice.getRate() == lastPrice.getRate();
	}

	protected boolean isSame( FXPrice latestPrice, FXPrice lastPrice )
	{
		return latestPrice.getRate() == lastPrice.getRate() && latestPrice.getLimit() == lastPrice.getLimit();
	}

	protected MarketTierPrice createNewMarketTierPrice(FXPrice price, boolean isBid, String priceId)
	{
		MarketTierPrice _price = newMarketTierPrice(price, isBid, priceId);
		_price.setAction(MarketTierPrice.NEW_ACTION);
		return _price;
	}

	protected MarketTierPrice newMarketTierPrice(FXPrice price, boolean isBid, String priceId)
	{
		MarketTierPrice _price = new MarketTierPriceC();
		_price.setPriceId(priceId);
		_price.setRate(price.getRate());
		_price.setLimit(price.getLimit());
		_price.setProvider(price.getLPName() == null ? "" : price.getLPName());
		_price.setSide(isBid ? MarketTierPrice.BID : MarketTierPrice.OFFER);
		return _price;
	}

	protected MarketTierPrice createDeleteMarketTierPrice( FXPrice price, boolean isBid, String priceId)
	{
		MarketTierPrice _price = newMarketTierPrice(price, isBid, priceId);
		_price.setAction(MarketTierPrice.DELETE_ACTION);
		return _price;
	}

	protected MarketTierPrice createUpdateMarketTierPrice( FXPrice price, boolean isBid, String priceId )
	{
		MarketTierPrice _price = newMarketTierPrice(price, isBid, priceId);
		_price.setAction(MarketTierPrice.UPDATE_ACTION);
		return _price;
	}

	protected void logMarketTierPriceChangeSet( MarketTierPriceChangeSet priceChangeSet, StringBuilder changeSb )
	{
		boolean isFirstPrice = true;
		for ( MarketTierPrice price : priceChangeSet.getMarketTierPrices() )
		{
			if ( !isFirstPrice )
				changeSb.append(',');

			changeSb.append('[').append(getAction(price)).append(' ').append(price.getPriceId()).append(' ').append(price.getRate()).append(' ').append(price.getLimit()).append(' ').append(price.getSide() == 0 ? 'B' : '0').append(' ').append(price.getProvider()).append(']');

			isFirstPrice = false;
		}
	}

	private char getAction( MarketTierPrice price )
	{
		switch ( price.getAction() )
		{
		case MarketTierPrice.NEW_ACTION :
			return 'N';
		case MarketTierPrice.UPDATE_ACTION :
			return 'U';
		case MarketTierPrice.DELETE_ACTION :
			return 'D';
		default :
			return ' ';
		}
	}

	protected void logPriceBook( FXPriceBook book, CurrencyPair ccyPair, StringBuilder logBuilder )
	{
		boolean isFirstPrice = true;
		for ( FXPrice bid : book.getBids() )
		{
			if ( !isFirstPrice )
				logBuilder.append(',');

			logBuilder.append('[').append(generateNewPriceId(bid, ccyPair, true)).append(' ').append(ccyPair == null ? "" :
                    ccyPair.getName()).append(' ').append(bid.getRate()).append(' ').append(bid.getLimit())
                    .append(' ').append('B').append(' ').append(bid.getTier()).append(' ').append(bid.getLPName() == null ? "" : bid.getLPName()).append(']');

			isFirstPrice = false;
		}

		for ( FXPrice offer : book.getOffers() )
		{
			if ( !isFirstPrice )
				logBuilder.append(',');

			logBuilder.append('[').append(generateNewPriceId(offer, ccyPair, false)).append(' ').append(ccyPair == null ? "" :
                    ccyPair.getName()).append(' ').append(offer.getRate()).append(' ').append(offer.getLimit()).append(' ').append('O').append(' ')
                    .append(offer.getTier()).append(' ').append(offer.getLPName() == null ? "" : offer.getLPName()).append(' ').append(']');

			isFirstPrice = false;
		}
	}
}
