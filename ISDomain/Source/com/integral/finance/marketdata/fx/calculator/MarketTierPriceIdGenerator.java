package com.integral.finance.marketdata.fx.calculator;

import com.integral.aggregation.price.FXPrice;
import com.integral.broker.price.Price;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.user.Organization;

/**
 * 
 */
public class MarketTierPriceIdGenerator {
    private static int sequencePriceId = 0;    
    private static String doubleDigitNumber[] = {"00", "01", "02", "03", "04", "05", "06", "07", "08", "09"};

    public static String generateNewId(Price price) {
        return String.valueOf(sequencePriceId++);
    }
    

    public static String generateNewId(Price price, CurrencyPair ccyPair, boolean isBid) {
        int baseCcyIndex = ccyPair.getBaseCurrency().getIndex();
        int termCcyIndex = ccyPair.getVariableCurrency().getIndex();
        int side = isBid ? 0 : 1;        

        return new StringBuilder()
                .append(baseCcyIndex < 10 ? doubleDigitNumber[baseCcyIndex] : baseCcyIndex)
                .append(termCcyIndex < 10 ? doubleDigitNumber[termCcyIndex] : termCcyIndex)
                .append(side)
                .append(String.valueOf(price.getRate()).replace(".", "")).toString();
    } 
    
	public static String generateNewId( FXPrice price, CurrencyPair ccyPair, boolean isBid )
	{
		int baseCcyIndex = ccyPair.getBaseCurrency().getIndex();
		int termCcyIndex = ccyPair.getVariableCurrency().getIndex();
		int side = isBid ? 0 : 1;

		return new StringBuilder().append(baseCcyIndex < 10 ? doubleDigitNumber[baseCcyIndex] : baseCcyIndex).append(termCcyIndex < 10 ? doubleDigitNumber[termCcyIndex] : termCcyIndex).append(side).append(String.valueOf(price.getRate()).replace(".", "")).toString();
	}
	
    public static String generateNewId(FXLegDealingPrice price, CurrencyPair ccyPair, boolean isBid) {
        int baseCcyIndex = ccyPair.getBaseCurrency().getIndex();
        int termCcyIndex = ccyPair.getVariableCurrency().getIndex();
        int side = isBid ? 0 : 1;        

        return new StringBuilder()
                .append(baseCcyIndex < 10 ? doubleDigitNumber[baseCcyIndex] : baseCcyIndex)
                .append(termCcyIndex < 10 ? doubleDigitNumber[termCcyIndex] : termCcyIndex)
                .append(side)
                .append(String.valueOf(price.getRate()).replace(".", "")).toString();
    } 

}
