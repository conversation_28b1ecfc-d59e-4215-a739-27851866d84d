package com.integral.finance.marketdata.fx.calculator;

import com.integral.broker.price.Price;
import com.integral.broker.price.PriceBook;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.marketData.MarketTierPrice;
import com.integral.finance.marketData.MarketTierPriceC;
import com.integral.finance.marketData.MarketTierPriceChangeSet;
import com.integral.log.Log;
import com.integral.log.LogFactory;

import java.util.*;

/**
 *
 */
public class PriceBookChangeCalculator implements MarketSnapshotChangeCalculator<PriceBook, Price> {
    protected Log log = LogFactory.getLog(this.getClass());

    public MarketTierPriceChangeSet computeChange(PriceBook latest, PriceBook last, CurrencyPair ccyPair) {
        if (log.isDebugEnabled()) {
            StringBuilder logSb = new StringBuilder(this.getClass().getName()).append("PriceBook Latest {");
            logPriceBook(latest, ccyPair, logSb);
            logSb.append(" } ");

            if (last != null) {
                logSb.append(" Last {");
                logPriceBook(last, ccyPair, logSb);
                logSb.append(" }");
            }

            log.debug(logSb.toString());
        }

        MarketTierPriceChangeSet priceChangeSet = new MarketTierPriceChangeSet();
        boolean isLastBookAvailable = last != null;

        if (latest == null) return null;

        if (isLastBookAvailable) {
            computePriceChange(priceChangeSet, ccyPair, latest.getBids(), last.getBids(), true);
            computePriceChange(priceChangeSet, ccyPair, latest.getOffers(), last.getOffers(), false);
        } else {
            computePriceChange(priceChangeSet, ccyPair, latest.getBids(), true);
            computePriceChange(priceChangeSet, ccyPair, latest.getOffers(), false);
        }


        if (log.isDebugEnabled()) {
            StringBuilder logSb = new StringBuilder("MarketTierPriceChangeSet {");
            logMarketTierPriceChangeSet(priceChangeSet, logSb);
            logSb.append(" }");

            log.debug(logSb.toString());
        }

        return priceChangeSet;
    }

    protected void computePriceChange(MarketTierPriceChangeSet priceChangeSet, CurrencyPair ccyPair,
                                      Collection<Price> latestPrices, boolean isBid) {

        for (Price latestPrice : latestPrices) {
            latestPrice.setPriceId( generateNewPriceId(latestPrice, ccyPair, isBid) );
            priceChangeSet.addMarketTierPrice(createNewMarketTierPrice(latestPrice, isBid));
        }
    }

    protected void computePriceChange(MarketTierPriceChangeSet priceChangeSet, CurrencyPair ccyPair, Collection<Price> latestPrices,
                                      Collection<Price> lastPrices, boolean isBid) {
        List<Price> _lastPrices = new ArrayList<Price>(lastPrices);

        // Iterating with latest price book
        for (Price latestPrice : latestPrices) {
            boolean isPriceMatched = false;
            boolean isRateMatched = false;
            Price matchedLastPrice = null;

            // Match each latest book price with the last price book to get the difference.
            for (Price lastPrice : _lastPrices) {

                // If latest and last prices matches, copy the price Id of last price into the latest price and
                // store the last matched price.
                if (isSame(latestPrice, lastPrice)) {
                    isPriceMatched = true;
                    matchedLastPrice = lastPrice;
                    break;
                } else if (isSameRate(latestPrice, lastPrice)) {
                    isRateMatched = true;
                    matchedLastPrice = lastPrice;
                    break;
                }
            }

            // If complete price is matched, copy the last price Id to the latest price object and
            // remove the matched price entry from the temporary last price collection. This price won't be
            // considered as part of incremental update.
            if (isPriceMatched) {
                latestPrice.setPriceId(matchedLastPrice.getPriceId());
                _lastPrices.remove(matchedLastPrice);
            } else {
                // If only rate and provider of prices are matched, copy the last price Id to the latest price object
                // and remove the matched price entry from the temporary last price collection. This is an update action.
                if (isRateMatched) {
                    latestPrice.setPriceId(matchedLastPrice.getPriceId());
                    _lastPrices.remove(matchedLastPrice);
                } else {
                    // If price is new, get the new price Id. This is a new action.
                    latestPrice.setPriceId(generateNewPriceId(latestPrice, ccyPair, isBid));
                }

                // Add the new NewMarketTierPrice update.
                priceChangeSet.addMarketTierPrice(createNewMarketTierPrice(latestPrice, isBid));
            }
        }

        // Add the delete MarketTierPrice for all the remaining prices of temporary last prices collection. This is a delete action.
        for (Price lastPrice : _lastPrices) {
            priceChangeSet.addMarketTierPrice(createDeleteMarketTierPrice(lastPrice, isBid));
        }
    }

    public String generateNewPriceId(Price latestPrice, CurrencyPair ccyPair, boolean isBid) {
        return MarketTierPriceIdGenerator.generateNewId(latestPrice, ccyPair, isBid);
    }

    protected boolean isSameRate(Price latestPrice, Price lastPrice) {
        return latestPrice.getRate() == lastPrice.getRate();
    }

    protected boolean isSame(Price latestPrice, Price lastPrice) {
        return latestPrice.getRate() == lastPrice.getRate() && latestPrice.getAmount() == lastPrice.getAmount();
    }

    protected MarketTierPrice createNewMarketTierPrice(Price price, boolean isBid) {
        MarketTierPrice _price = newMarketTierPrice(price, isBid);
        _price.setAction(MarketTierPrice.NEW_ACTION);
        return _price;
    }

    protected MarketTierPrice newMarketTierPrice(Price price, boolean isBid) {
        MarketTierPrice _price = new MarketTierPriceC();
        _price.setPriceId(price.getPriceId());
        _price.setRate(price.getRate());
        _price.setLimit(price.getAmount());
        _price.setProvider(price.getLPName() == null ? "" : price.getLPName());
        _price.setSide(isBid ? MarketTierPrice.BID : MarketTierPrice.OFFER);
        return _price;
    }

    protected MarketTierPrice createDeleteMarketTierPrice(Price price, boolean isBid) {
        MarketTierPrice _price = newMarketTierPrice(price, isBid);
        _price.setAction(MarketTierPrice.DELETE_ACTION);
        return _price;
    }

    protected MarketTierPrice createUpdateMarketTierPrice(Price price, boolean isBid) {
        MarketTierPrice _price = newMarketTierPrice(price, isBid);
        _price.setAction(MarketTierPrice.UPDATE_ACTION);
        return _price;
    }

    protected void logMarketTierPriceChangeSet(MarketTierPriceChangeSet priceChangeSet, StringBuilder changeSb) {
        boolean isFirstPrice = true;
        for (MarketTierPrice price : priceChangeSet.getMarketTierPrices()) {
            if (!isFirstPrice) changeSb.append(',');

            changeSb.append('[')
                    .append(getAction(price)).append(' ')
                    .append(price.getPriceId()).append(' ')
                    .append(price.getRate()).append(' ')
                    .append(price.getLimit()).append(' ')
                    .append(price.getSide() == 0 ? 'B' : '0').append(' ')
                    .append(price.getProvider())
                    .append(']');

            isFirstPrice = false;
        }
    }

    private char getAction(MarketTierPrice price) {
        switch(price.getAction()) {
            case MarketTierPrice.NEW_ACTION :
                return 'N';
            case MarketTierPrice.UPDATE_ACTION :
                return 'U';
            case MarketTierPrice.DELETE_ACTION :
                return 'D';
            default :
                return ' ';
        }
    }

    protected void logPriceBook(PriceBook book, CurrencyPair ccyPair, StringBuilder logBuilder) {
        boolean isFirstPrice = true;
        for (Price bid : book.getBids()) {
            if (!isFirstPrice) logBuilder.append(',');

            logBuilder.append('[')
                    .append(bid.getPriceId()).append(' ')
                    .append(ccyPair == null ? "" : ccyPair.getName()).append(' ')
                    .append(bid.getRate()).append(' ')
                    .append(bid.getAmount()).append(' ')
                    .append('B').append(' ')
                    .append(bid.getTier()).append(' ')
                    .append(bid.getOrganization() == null ? "" : bid.getOrganization().getShortName())
                    .append(']');

            isFirstPrice = false;
        }

        for (Price offer : book.getOffers()) {
            if (!isFirstPrice) logBuilder.append(',');

            logBuilder.append('[')
                    .append(offer.getPriceId()).append(' ')
                    .append(ccyPair == null ? "" : ccyPair.getName()).append(' ')
                    .append(offer.getRate()).append(' ')
                    .append(offer.getAmount()).append(' ')
                    .append('O').append(' ')
                    .append(offer.getTier()).append(' ')
                    .append(offer.getOrganization() == null ? "" : offer.getOrganization().getShortName()).append(' ')
                    .append(']');

            isFirstPrice = false;
        }
    }
}
