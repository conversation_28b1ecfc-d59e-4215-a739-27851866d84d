package com.integral.finance.marketdata.fx.calculator;

import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 *
 */
public class MarketSnapshotChangeCalculatorFactory {
    protected static MarketSnapshotChangeCalculatorFactory current = null;
    private static Log log = LogFactory.getLog(MarketSnapshotChangeCalculatorFactory.class);

    static {
        try {
            current = new MarketSnapshotChangeCalculatorFactory();
        } catch (Exception e) {
            log.error("MarketSnapshotChangeCalculatorFactory: problem creating class in static initializer");
        }
    }

    protected MarketSnapshotChangeCalculatorFactory() {
    }

    public static MarketSnapshotChangeCalculatorFactory getInstance() {
        return current;
    }

    public PriceBookChangeCalculator getPriceBookChangeCalculator() {
        return new PriceBookChangeCalculator();
    }

    public RawPriceBookChangeCalculator getRawPriceBookChangeCalculator() {
        return new RawPriceBookChangeCalculator();
    }

    public TopPriceBookChangeCalculator getTopPriceBookChangeCalculator() {
        return new TopPriceBookChangeCalculator();
    }

    public QuoteChangeCalculator getQuoteChangeCalculator() {
        return new QuoteChangeCalculator();
    }
    
    public TopFXPriceBookChangeCalculator getTopFXPriceBookChangeCalculator() {
        return new TopFXPriceBookChangeCalculator();
    }
    public FXPriceBookChangeCalculator getFXPriceBookChangeCalculator() {
        return new FXPriceBookChangeCalculator();
    }
    public RawFXPriceBookChangeCalculator getRawFXPriceBookChangeCalculator() {
        return new RawFXPriceBookChangeCalculator();
    }
}
