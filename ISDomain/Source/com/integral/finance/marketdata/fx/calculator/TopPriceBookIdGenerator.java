package com.integral.finance.marketdata.fx.calculator;

import com.integral.broker.price.Price;
import com.integral.finance.currency.CurrencyPair;
import com.integral.user.Organization;

/**
 *
 */
public class TopPriceBookIdGenerator {
    private static int sequencePriceId = 0;
    private static String doubleDigitNumber[] = {"00", "01", "02", "03", "04", "05", "06", "07", "08", "09"};
    
    public static String generateNewId(Price price) {
        return String.valueOf(sequencePriceId++);
    }

    public static String generateNewId(Price price,  CurrencyPair ccyPair, boolean isBid) {
        Organization provider = price.getOrganization();

        int baseCcyIndex = ccyPair.getBaseCurrency().getIndex();
        int termCcyIndex = ccyPair.getVariableCurrency().getIndex();
        int orgIndex = provider.getIndex();
        int side = isBid ? 0 : 1;

        return new StringBuilder(7)
                .append(orgIndex < 10 ? doubleDigitNumber[orgIndex] : orgIndex)
                .append(baseCcyIndex < 10 ? doubleDigitNumber[baseCcyIndex] : baseCcyIndex)
                .append(termCcyIndex < 10 ? doubleDigitNumber[termCcyIndex] : termCcyIndex)
                .append(side).toString();
    }
}
