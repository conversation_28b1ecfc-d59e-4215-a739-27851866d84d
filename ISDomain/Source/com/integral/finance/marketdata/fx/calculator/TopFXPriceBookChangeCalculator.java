package com.integral.finance.marketdata.fx.calculator;

import com.integral.aggregation.price.FXPrice;
import com.integral.aggregation.price.FXPriceBook;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.marketData.MarketTierPriceChangeSet;

public class TopFXPriceBookChangeCalculator extends FXPriceBookChangeCalculator
{

	public MarketTierPriceChangeSet computeChange( FXPriceBook latest, FXPriceBook last, CurrencyPair ccyPair )
	{
		return super.computeChange(latest, last, ccyPair);
	}

	public String generateNewPriceId( FXPrice latestPrice, CurrencyPair ccyPair, boolean isBid )
	{
		return TopFXPriceBookIdGenerator.generateNewId(latestPrice, ccyPair, isBid);
	}

	protected boolean isSameRate( FXPrice latestPrice, FXPrice lastPrice )
	{
		return latestPrice != null && lastPrice != null;
	}
}