package com.integral.finance.marketdata.fx.calculator;

import com.integral.broker.price.Price;
import com.integral.broker.price.PriceBook;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.marketData.MarketTierPriceChangeSet;

/**
 *
 */
public class TopPriceBookChangeCalculator extends PriceBookChangeCalculator {

    public MarketTierPriceChangeSet computeChange(PriceBook latest, PriceBook last, CurrencyPair ccyPair) {
        return super.computeChange(latest, last, ccyPair);
    }

    public String generateNewPriceId(Price latestPrice, CurrencyPair ccyPair, boolean isBid) {
        return TopPriceBookIdGenerator.generateNewId(latestPrice, ccyPair, isBid);
    }

    protected boolean isSameRate(Price latestPrice, Price lastPrice) {
        return latestPrice != null && lastPrice != null;
    }
}
