package com.integral.finance.marketdata.fx.calculator;

import com.integral.aggregation.price.FXPrice;
import com.integral.broker.price.Price;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.marketData.MarketTierPriceChangeSet;

public interface MarketSnapshotChangeCalculator<E, F>
{
	public MarketTierPriceChangeSet computeChange(E latest, E last, CurrencyPair ccyPair);

    String generateNewPriceId(F latestPrice, CurrencyPair ccyPair, boolean isBid);
}
