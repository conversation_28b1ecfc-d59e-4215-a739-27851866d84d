package com.integral.finance.marketdata.fx.calculator;

import com.integral.aggregation.price.FXPrice;
import com.integral.finance.currency.CurrencyPair;

public class RawFXPriceBookChangeCalculator extends FXPriceBookChangeCalculator
{

	protected boolean isSame( FXPrice latestPrice, FXPrice lastPrice )
	{
		return latestPrice.getRate() == lastPrice.getRate() && latestPrice.getLimit() == lastPrice.getLimit() && latestPrice.getLPName().equals(lastPrice.getLPName()) && latestPrice.getTier() == lastPrice.getTier();
	}

	public String generateNewPriceId( FXPrice latestPrice, CurrencyPair ccyPair, boolean isBid )
	{
		return RawFXPriceBookIdGenerator.generateNewId(latestPrice, ccyPair, isBid);
	}

	protected boolean isSameRate( FXPrice latestPrice, FXPrice lastPrice )
	{
		return latestPrice.getLPName().equals(lastPrice.getLPName()) && latestPrice.getTier() == lastPrice.getTier();
	}
}

