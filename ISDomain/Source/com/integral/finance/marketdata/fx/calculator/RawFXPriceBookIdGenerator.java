package com.integral.finance.marketdata.fx.calculator;

import com.integral.aggregation.price.FXPrice;
import com.integral.broker.price.Price;
import com.integral.finance.currency.CurrencyPair;
import com.integral.user.Organization;

/**
 *
 */
public class RawFXPriceBookIdGenerator
{
	private static int sequencePriceId = 0;
	private static String doubleDigitNumber[] = { "00", "01", "02", "03", "04", "05", "06", "07", "08", "09" };

	public static String generateNewId( Price price )
	{
		return String.valueOf(sequencePriceId++);
	}

	public static String generateNewId( FXPrice price, CurrencyPair ccyPair, boolean isBid )
	{
		Organization provider = price.getProvider();

		int baseCcyIndex = ccyPair.getBaseCurrency().getIndex();
		int termCcyIndex = ccyPair.getVariableCurrency().getIndex();
		int orgIndex = provider.getIndex();
		int side = isBid ? 0 : 1;
		int tier = price.getTier();

		return new StringBuilder(8).append(orgIndex < 10 ? doubleDigitNumber[orgIndex] : orgIndex).append(baseCcyIndex < 10 ? doubleDigitNumber[baseCcyIndex] : baseCcyIndex).append(termCcyIndex < 10 ? doubleDigitNumber[termCcyIndex] : termCcyIndex).append(side).append(tier).toString();
	}
}
