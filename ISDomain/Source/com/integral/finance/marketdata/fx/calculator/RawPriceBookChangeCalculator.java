package com.integral.finance.marketdata.fx.calculator;

import com.integral.broker.price.Price;
import com.integral.finance.currency.CurrencyPair;

/**
 *
 */
public class RawPriceBookChangeCalculator extends PriceBookChangeCalculator {

    protected boolean isSame(Price latestPrice, Price lastPrice) {
        return latestPrice.getRate() == lastPrice.getRate() && latestPrice.getAmount() == lastPrice.getAmount() &&
                latestPrice.getOrganization().equals(lastPrice.getOrganization()) && latestPrice.getTier() == lastPrice.getTier();
    }

    public String generateNewPriceId(Price latestPrice, CurrencyPair ccyPair, boolean isBid) {
        return RawPriceBookIdGenerator.generateNewId(latestPrice, ccyPair, isBid);
    }

    protected boolean isSameRate(Price latestPrice, Price lastPrice) {
        return latestPrice.getOrganization().equals(lastPrice.getOrganization()) && latestPrice.getTier() == lastPrice.getTier();
    }
}
