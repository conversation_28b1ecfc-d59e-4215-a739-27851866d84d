package com.integral.finance.marketdata.fx.calculator;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.marketData.MarketTierPrice;
import com.integral.finance.marketData.MarketTierPriceC;
import com.integral.finance.marketData.MarketTierPriceChangeSet;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * 
 */
public class QuoteChangeCalculator implements MarketSnapshotChangeCalculator<Quote, DealingPrice> {

	protected Log log = LogFactory.getLog(this.getClass());
	
    public MarketTierPriceChangeSet computeChange(Quote latest, Quote last, CurrencyPair ccyPair) {

		if ( log.isDebugEnabled() )
		{
			StringBuilder logSb = new StringBuilder(this.getClass().getName()).append("PriceBook Latest {");
			logPriceBook(latest, ccyPair, logSb);
			logSb.append(" } ");

			if ( last != null )
			{
				logSb.append(" Last {");
				logPriceBook(last, ccyPair, logSb);
				logSb.append(" }");
			}

			log.debug(logSb.toString());
		}
		if ( latest == null )
			return null;
		MarketTierPriceChangeSet priceChangeSet = new MarketTierPriceChangeSet();
		boolean isLastBookAvailable = last != null;


		String orgName = latest.getOrganization().getShortName();
		if ( isLastBookAvailable )
		{
			computePriceChange(priceChangeSet, ccyPair, latest.getBids(), last.getBids(), true, orgName);
			computePriceChange(priceChangeSet, ccyPair, latest.getOffers(), last.getOffers(), false, orgName);
		}
		else
		{
			computePriceChange(priceChangeSet, ccyPair, latest.getBids(), true, orgName);
			computePriceChange(priceChangeSet, ccyPair, latest.getOffers(), false, orgName);
		}

		if ( log.isDebugEnabled() )
		{
			StringBuilder logSb = new StringBuilder("MarketTierPriceChangeSet {");
			logMarketTierPriceChangeSet(priceChangeSet, logSb);
			logSb.append(" }");

			log.debug(logSb.toString());
		}

		return priceChangeSet;
	
    }

    protected void computePriceChange( MarketTierPriceChangeSet priceChangeSet, CurrencyPair ccyPair, Collection<FXLegDealingPrice> latestPrices, boolean isBid, String orgName )
	{
    	int i = 0;
		for ( FXLegDealingPrice latestPrice : latestPrices )
		{
            String priceId = generateNewPriceId(i++,isBid);
			priceChangeSet.addMarketTierPrice(createNewMarketTierPrice(latestPrice, isBid, priceId,orgName));
		}
	}
    
	protected boolean isValidRate( FXLegDealingPrice price )
	{
		if ( price.getRate() <= 0.0 || price.getDealtAmount() <= 0.0 ) {
			return false;
		}
		return true;
	}

	protected void computePriceChange( MarketTierPriceChangeSet priceChangeSet, CurrencyPair ccyPair, Collection<FXLegDealingPrice> latestPrices, Collection<FXLegDealingPrice> lastPrices, boolean isBid, String orgName)
	{
		List<FXLegDealingPrice> _lastPrices = new ArrayList<FXLegDealingPrice>(lastPrices);

		int latestPricesSize = latestPrices.size();
		// Iterating with latest price book
		Iterator<FXLegDealingPrice> newPriceIterator = latestPrices.iterator();
		Iterator<FXLegDealingPrice> lastPriceIterator = _lastPrices.iterator();
		
		for (int i = 0; newPriceIterator.hasNext(); i++) {
		    FXLegDealingPrice latestPrice = newPriceIterator.next();
			FXLegDealingPrice lastPrice = null;
			
			if(lastPriceIterator.hasNext()) {
				lastPrice = lastPriceIterator.next();
			}

			// Match each latest book price with the last price book to get the difference.

			String priceId = generateNewPriceId(i, isBid);

			if ( lastPrice != null ) {
				if ( !isSame(latestPrice, lastPrice) ) {
					priceChangeSet.addMarketTierPrice(createDeleteMarketTierPrice(lastPrice, isBid, priceId, orgName));
					if ( isValidRate(latestPrice) ) {
						priceChangeSet.addMarketTierPrice(createNewMarketTierPrice(latestPrice, isBid, priceId, orgName));
					}
				}
				continue;
			} 
			//Add a new entry
			priceChangeSet.addMarketTierPrice(createNewMarketTierPrice(latestPrice, isBid, priceId, orgName));
		}

		// Add the delete MarketTierPrice for all the remaining prices of temporary last prices collection. This is a delete action.
		for (int j = 0; lastPriceIterator.hasNext(); j++) {
			FXLegDealingPrice lastPrice = lastPriceIterator.next();
			String priceId = generateNewPriceId(j+latestPricesSize, isBid);
			priceChangeSet.addMarketTierPrice(createDeleteMarketTierPrice(lastPrice, isBid, priceId, orgName));
		}
	}

	public String generateNewPriceId( FXLegDealingPrice latestPrice, CurrencyPair ccyPair, boolean isBid )
	{
		return MarketTierPriceIdGenerator.generateNewId(latestPrice, ccyPair, isBid);
	}
	
	public String generateNewPriceId(int index, boolean isBid)
	{
		return isBid ? ("B"+index) : ("O"+index);
	}


	protected boolean isSameRate( FXLegDealingPrice latestPrice, FXLegDealingPrice lastPrice )
	{
		return latestPrice.getRate() == lastPrice.getRate();
	}

	protected boolean isSame( FXLegDealingPrice latestPrice, FXLegDealingPrice lastPrice )
	{
		return latestPrice.getRate() == lastPrice.getRate() && latestPrice.getDealtAmount() == lastPrice.getDealtAmount();
	}

	protected MarketTierPrice createNewMarketTierPrice(FXLegDealingPrice price, boolean isBid, String priceId, String orgName)
	{
		MarketTierPrice _price = newMarketTierPrice(price, isBid, priceId,orgName);
		_price.setAction(MarketTierPrice.NEW_ACTION);
		return _price;
	}

	protected MarketTierPrice newMarketTierPrice(FXLegDealingPrice price, boolean isBid, String priceId, String orgName)
	{
		MarketTierPrice _price = new MarketTierPriceC();
		_price.setPriceId(priceId);
		_price.setRate(price.getRate());
		_price.setLimit(price.getDealtAmount());
		_price.setProvider(orgName);
		_price.setSide(isBid ? MarketTierPrice.BID : MarketTierPrice.OFFER);
		return _price;
	}

	protected MarketTierPrice createDeleteMarketTierPrice( FXLegDealingPrice price, boolean isBid, String priceId, String orgName)
	{
		MarketTierPrice _price = newMarketTierPrice(price, isBid, priceId, orgName);
		_price.setAction(MarketTierPrice.DELETE_ACTION);
		return _price;
	}

	protected MarketTierPrice createUpdateMarketTierPrice( FXLegDealingPrice price, boolean isBid, String priceId, String orgName)
	{
		MarketTierPrice _price = newMarketTierPrice(price, isBid, priceId, orgName);
		_price.setAction(MarketTierPrice.UPDATE_ACTION);
		return _price;
	}

	protected void logMarketTierPriceChangeSet( MarketTierPriceChangeSet priceChangeSet, StringBuilder changeSb )
	{
		boolean isFirstPrice = true;
		for ( MarketTierPrice price : priceChangeSet.getMarketTierPrices() )
		{
			if ( !isFirstPrice )
				changeSb.append(',');

			changeSb.append('[').append(getAction(price)).append(' ').append(price.getPriceId()).append(' ').append(price.getRate()).append(' ').append(price.getLimit()).append(' ').append(price.getSide() == 0 ? 'B' : '0').append(' ').append(price.getProvider()).append(']');

			isFirstPrice = false;
		}
	}

	private char getAction( MarketTierPrice price )
	{
		switch ( price.getAction() )
		{
		case MarketTierPrice.NEW_ACTION :
			return 'N';
		case MarketTierPrice.UPDATE_ACTION :
			return 'U';
		case MarketTierPrice.DELETE_ACTION :
			return 'D';
		default :
			return ' ';
		}
	}

	protected void logPriceBook( Quote quote, CurrencyPair ccyPair, StringBuilder logBuilder )
	{
		boolean isFirstPrice = true;
		for (int i=0;i<quote.getBids().size();i++)
		{
			FXLegDealingPrice bid = quote.getBids().get(i);
			if ( !isFirstPrice )
				logBuilder.append(',');

			logBuilder.append('[').append(i).append(' ').append(ccyPair == null ? "" :
                    ccyPair.getName()).append(' ').append(bid.getRate()).append(' ').append(bid.getDealtAmount())
                    .append(' ').append('B').append(' ').append(bid.getTier()).append(' ').append(quote.getOrganization() == null ? "" : quote.getOrganization().getShortName()).append(']');

			isFirstPrice = false;
		}

		for (int i=0;i<quote.getOffers().size();i++)
		{
			FXLegDealingPrice offer = quote.getOffers().get(i);
			if ( !isFirstPrice )
				logBuilder.append(',');

			logBuilder.append('[').append(i).append(' ').append(ccyPair == null ? "" :
                    ccyPair.getName()).append(' ').append(offer.getRate()).append(' ').append(offer.getDealtAmount()).append(' ').append('O').append(' ')
                    .append(offer.getTier()).append(' ').append(quote.getOrganization() == null ? "" : quote.getOrganization().getShortName()).append(' ').append(']');

			isFirstPrice = false;
		}
	}

    public String generateNewPriceId(DealingPrice latestPrice, CurrencyPair ccyPair, boolean isBid) {
        return null;  //To change body of implemented methods use File | Settings | File Templates.
    }
}
