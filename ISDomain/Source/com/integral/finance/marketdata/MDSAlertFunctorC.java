package com.integral.finance.marketdata;

import com.integral.finance.marketData.configuration.MarketDataConfigurationFactory;
import com.integral.finance.marketData.configuration.MarketDataConfigurationMBean;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.marketData.fx.RTMDSPublishService;
import com.integral.finance.marketData.model.MDSUpdateEvent;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.email.ISEmailUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Namespace;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.query.QueryFactory;
import com.integral.query.QueryService;
import com.integral.scheduler.ScheduleFunctorC;
import com.integral.persistence.PersistenceFactory;
import com.integral.message.WorkflowMessage;

import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDateTime;
import com.integral.user.Organization;
import org.eclipse.persistence.sessions.DatabaseRecord;

import java.math.BigDecimal;
import java.util.*;

public class MDSAlertFunctorC extends ScheduleFunctorC
{
    public static final String STALE_MDS_IDS_PARAM = "StaleMDSIdsParam";
    public static final String FRESH_MDS_IDS_PARAM = "FreshMDSIdsParam";

    Log log = LogFactory.getLog(this.getClass());
    public String getDescription() { return "MDSAlertFunctorC"; }

    static Map<String, MDSEntry> knownStale = new HashMap<String, MDSEntry>();
	
    public void execute( WorkflowMessage msg ) {
		try {
            MarketDataConfigurationMBean marketDataConfig = MarketDataConfigurationFactory.getMarketDataConfigurationMBean();
            String alertableSQL = marketDataConfig.getMDSAlertFunctorQuery();
            boolean sendEMails = marketDataConfig.getMDSAlertFunctorEmailEnabled();

			Vector results = PersistenceFactory.newSession().executeSQL( alertableSQL );
            log.info( "MAF.execute : query results=" + results );
            Map<String, MDSEntry> dbStale = new HashMap<String, MDSEntry>();
        	for ( DatabaseRecord result : ( Vector<DatabaseRecord> ) results )
			{
                MDSEntry mdse = new MDSEntry(result);
                if ( mdse.isRealtime() )
                {
                    FXMarketDataSet mds = ( FXMarketDataSet ) ReferenceDataCacheC.getInstance().getEntityByObjectId( Long.valueOf( mdse.id ), FXMarketDataSet.class );
                    if ( !isStale ( mds ))
                    {
                        continue;
                    }
                }
                dbStale.put( mdse.fn, mdse );
			}
            Set<String> newStale = new HashSet<String>(dbStale.keySet());
            newStale.removeAll(knownStale.keySet());
            Set<String> newFresh = new HashSet<String>(knownStale.keySet());
            newFresh.removeAll( dbStale.keySet() );
            for( String id : newStale )
            {
                MDSEntry mdse = dbStale.get( id );
                Map details = mdse.toMap();
                if( log.isDebugEnabled() ) log.debug(".execute() newStale: " + mdse.id + " " + mdse.fn);
                knownStale.put( mdse.fn, mdse );
                if( sendEMails )
                {
                    log.info( "MAF.execute : sending stale notification for mds=" + mdse.fn );
                    ISEmailUtil.getInstance().sendMDSStaleNotification( mdse.addresses, details, ISConstantsC.EVENT_MDS_STALE, mdse.sn );
                    if ( msg != null )
                    {
                        Collection<String> ids = ( Collection<String> ) msg.getParameterValue( STALE_MDS_IDS_PARAM );
                        if ( ids == null )
                        {
                            ids = new HashSet<String>();
                            msg.setParameterValue( STALE_MDS_IDS_PARAM, ids );
                        }
                        ids.add( id );
                    }
                }

                // publish real-time mds.
                if ( mdse.isRealtime() )
                {
                    FXMarketDataSet mds = ( FXMarketDataSet ) ReferenceDataCacheC.getInstance().getEntityByObjectId( Long.valueOf( mdse.id ), FXMarketDataSet.class );
                    final Organization org = ReferenceDataCacheC.getInstance().getOrganization( mds.getNamespace().getShortName() );
                    if ( marketDataConfig.isRealtimeMDSStaleElementsRemoveEnabled( org ) )
                    {
                        FXMarketDataSet rtmds = ( FXMarketDataSet ) mds.getSessionInstance();
                        log.info( "MAF.execute : removing elements from real-time mds=" + rtmds  );
                        Collection<FXMarketDataElement> mdes = (Collection<FXMarketDataElement>) rtmds.getMarketDataElements();
                        int size = mdes.size ();
                        RTMDSPublishService.getInstance().publish(org, mds, mdes, MDSUpdateEvent.Type.STALE , MDSUpdateEvent.Source.MDSCOPY, true);
                        log.info( "MAF.execute : sent notification of stale removal from mds=" + rtmds + ",elementsSize=" + size );
                    }
                    else
                    {
                        log.info( "MAF.execute : stale removal is not enabled for real-time mds=" + mds  );
                    }
                }
            }
            for( String id : newFresh )
            {
                MDSEntry mdse = knownStale.get( id );
                Map details = mdse.toMap();
                if( log.isDebugEnabled() ) log.debug(".execute() newFresh: " + mdse.id + " " + mdse.fn);
                knownStale.remove( id );
                if( sendEMails )
                {
                    log.info( "MAF.execute : sending 'No More Stale' notification for mds=" + mdse.fn);
                    ISEmailUtil.getInstance().sendMDSStaleNotification( mdse.addresses, details, ISConstantsC.EVENT_MDS_FRESH, mdse.sn );
                    if ( msg != null )
                    {
                        Collection<String> ids = ( Collection<String> ) msg.getParameterValue( FRESH_MDS_IDS_PARAM );
                        if ( ids == null )
                        {
                            ids = new HashSet<String>();
                            msg.setParameterValue( FRESH_MDS_IDS_PARAM, ids );
                        }
                        ids.add( id );
                    }
                }
            }
		} catch ( Exception e) {
            log.error(".execute() exception ", e);
		}
	}

    private boolean isStale ( FXMarketDataSet mds )
    {
        if ( mds.isStaleAlertEnabled() )
        {
            IdcDateTime dt = mds.isRealtime() ? mds.getSessionInstance().getEffectiveDateTime() : mds.getModifiedDateTime();
            IdcDateTime now = DateTimeFactory.newDateTime();
            long effectiveDate = dt != null ? dt.asSeconds() : 0;
            long staleThreshold = mds.getStaleAlertThresholdSeconds();
            boolean stale = now.asSeconds() > effectiveDate + staleThreshold;
            log.info( new StringBuilder( 300 ).append( "MAF.isStale : effectiveDatTime=" ).append( dt ).append( ",now=")
                    .append( now ).append ( ",effDateAsSeconds=" ).append ( effectiveDate ).append ( ",staleThreshold=" )
                    .append( staleThreshold ).append ( ",mds=" ).append ( mds ).toString() );
            return stale;
        }
        return false;
    }

    // todo could put details names in property also and have no dependencies here (just some 'id' would be necessary for map key)
    class MDSEntry {
        public String id = "";
        public long nsid;
        Namespace ns;
        String nssn = "";
        String fn = "";
        public String sn = "";
        public String addresses = "";
        public String threshold = "";
        public boolean realtime = false;
        MDSEntry(DatabaseRecord dbr) {
            id = ""+dbr.get("ID");
            nsid = ((BigDecimal)dbr.get("NAMESPACEID")).longValue();
            try {
                QueryService queryService = QueryFactory.getQueryService();
                ns = (Namespace)queryService.find( Namespace.class, nsid );
                nssn = ns.getShortName();
            } catch( Exception e) {
                log.warn(".MDSEntry() could not find namespace: "+nsid, e);
            }

            sn = (String)dbr.get("SHORTNAME");
            fn = sn + '@' + nssn;
            threshold = ""+dbr.get("STALETHRESHOLD");
            addresses = (String)dbr.get("STALEADDRESSES");
            String rtMds = "" + dbr.get("REALTIME");
            if ( "T".equals( rtMds ))
            {
                realtime = true;
            }
        }
        Map<String, String>toMap() {
            Map<String, String> details = new HashMap<String, String>();
            details.put( "$SHORTNAME", sn );     // todo email template subs wants leading $ sigh
            details.put( "$NAMESPACE", nssn );
            details.put( "$STALETHRESHOLD", threshold );
            details.put( "$STALEADDRESSES", addresses );
            return details;
        }

        boolean isRealtime()
        {
            return realtime;
        }
    }

}
