package com.integral.finance.creditLimit.external;

import com.integral.system.configuration.IdcMBeanC;

public class ExternalCreditMBeanC extends IdcMBeanC implements ExternalCreditMBean {
	
	private static final long serialVersionUID = 1L;
	private static ExternalCreditMBean externalCreditMBean = new ExternalCreditMBeanC();
	private String routingKey;
	private String receivingQueue;
	private boolean receivingQueueDurable;
	private String bindingKey;
	private long creditCheckTimeout;
    private ExternalCreditMBeanC()
    {
        super( "com.integral.externalcredit.ExternalCreditMBean" );
    }
    
    public static ExternalCreditMBean getInstance()
    {
    	return externalCreditMBean;
    }

    public void initialize()
    {
        super.initialize();
        this.routingKey = getStringProperty(IDC_EXTERNAL_CREDIT_CHECK_ROUTING_KEY, "STPX.EXTERNAL.CREDIT.REQUEST");
        this.receivingQueue = getStringProperty(IDC_EXTERNAL_CREDIT_CHECK_QUEUE_RECEIVE, "STPX.EXTERNAL.CREDIT.QUEUE.RECEIVE");
        this.receivingQueueDurable = getBooleanProperty(IDC_EXTERNAL_CREDIT_CHECK_QUEUE_RECEIVE_DURABLE, true);
        this.bindingKey = getStringProperty(IDC_EXTERNAL_CREDIT_CHECK_BINDING_KEY, "STPX.EXTERNAL.CREDIT.RESPONSE");
        this.creditCheckTimeout = getLongProperty(IDC_EXTERNAL_CREDIT_CHECK_TIMEOUT, 5000);
    }

	
	@Override
	public String getRoutingKey() 
	{		
		return this.routingKey;
	}

	@Override
	public String getReceivingQueue()
	{		
		return this.receivingQueue;
	}

	@Override
	public boolean isReceivingQueueDurable() 
	{		
		return this.receivingQueueDurable;
	}

	@Override
	public String getBindingKey() 
	{		
		return this.bindingKey;
	}

	@Override
	public long getCreditCheckTimeout() 
	{		
		return this.creditCheckTimeout;
	}
	
	
}
