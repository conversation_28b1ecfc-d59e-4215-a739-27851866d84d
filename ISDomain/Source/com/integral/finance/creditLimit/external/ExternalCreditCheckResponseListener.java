package com.integral.finance.creditLimit.external;

import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.TextMessage;

import com.integral.log.Log;
import com.integral.log.LogFactory;


public class ExternalCreditCheckResponseListener implements MessageListener 
{
    private Log log = LogFactory.getLog( this.getClass() );
    
	@Override
	public void onMessage(Message message) 
	{
		try
		{
			TextMessage txtMessage = (TextMessage)message;
			
			if(txtMessage != null)
			{
				String text = txtMessage.getText();
				log.info("ExternalCreditCheckResponseListener : Received external credit check response : " + text);
				ExternalCreditCheckUtil.getInstance().handleCreditLimitResponse(text);
			}			
		}
		catch(Exception ex)
		{
			log.error("ExternalCreditCheckResponseListener : Exception listening to external credit check response", ex);
		}
	}

}
