package com.integral.finance.creditLimit.external;


import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.messaging.MessageListener;
import com.integral.messaging.RMQMessage;


public class ExternalCreditCheckResponseRMQListener implements MessageListener
{
    private Log log = LogFactory.getLog( this.getClass() );

	@Override
	public void onMessage(RMQMessage rmqMessage) 
	{
		try 
		{
			if (rmqMessage == null)
			{
				return;
			}		
			String textMessage = (String)rmqMessage.getPayload();
			log.info("ExternalCreditCheckResponseRMQListener.onMessage : Received external credit check response : " + textMessage);
			ExternalCreditCheckUtil.getInstance().handleExternalCreditCheckResponse(textMessage);
		}
		catch (Throwable t) 
		{
			log.error("ExternalCreditCheckResponseRMQListener.onMessage :Problem with processing message:" + rmqMessage, t);
		}
		finally 
		{
			if (rmqMessage != null)
			{
				try 
				{
					rmqMessage.ack();
				}
				catch (Throwable t) 
				{
					log.error("ExternalCreditCheckResponseRMQListener.onMessage :Problem with acking message:" + rmqMessage, t);
				}
			}
		}
	}
 

}
