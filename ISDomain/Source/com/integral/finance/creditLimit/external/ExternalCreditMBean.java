package com.integral.finance.creditLimit.external;

import com.integral.system.configuration.IdcMBean;

public interface ExternalCreditMBean extends IdcMBean {
	String IDC_EXTERNAL_CREDIT_CHECK_ROUTING_KEY = "IDC.EXTERNAL.CREDIT.CHECK.ROUTING.KEY";
	String IDC_EXTERNAL_CREDIT_CHECK_QUEUE_RECEIVE = "IDC.EXTERNAL.CREDIT.CHECK.QUEUE.RECEIVE";
	String IDC_EXTERNAL_CREDIT_CHECK_QUEUE_RECEIVE_DURABLE = "IDC.EXTERNAL.CREDIT.CHECK.QUEUE.RECEIVE.DURABLE";
	String IDC_EXTERNAL_CREDIT_CHECK_BINDING_KEY = "IDC.EXTERNAL.CREDIT.CHECK.BINDING.KEY";
	String IDC_EXTERNAL_CREDIT_CHECK_TIMEOUT = "IDC.EXTERNAL.CREDIT.CHECK.TIMEOUT";
	
	String getRoutingKey();
	
	String getReceivingQueue();
	
	boolean isReceivingQueueDurable();
	
	String getBindingKey();
	
	long getCreditCheckTimeout();

}
