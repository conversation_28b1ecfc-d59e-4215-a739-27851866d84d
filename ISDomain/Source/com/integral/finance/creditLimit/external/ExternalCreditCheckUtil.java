package com.integral.finance.creditLimit.external;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import com.integral.SEF.UTIGenerator;
import com.integral.SEF.UTIInfo;
import com.integral.admin.utils.StringUtils;
import com.integral.commons.Tuple;
import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditEntity;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.admin.CreditLimitAdminService;
import com.integral.finance.creditLimit.admin.CreditLimitAdminServiceFactory;
import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationFactory;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.mifid.MiFIDTradeParams;
import com.integral.finance.fx.FXLeg;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeDownloadServiceC;
import com.integral.finance.trade.TradeLeg;
import com.integral.finance.trade.TradeServiceFactory;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.cache.HandlerCacheC;
import com.integral.is.common.cache.MDSFactory;
import com.integral.is.common.email.ISEmailUtil;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.messaging.MessageHolder;
import com.integral.netting.model.NettingPortfolio;
import com.integral.netting.model.NettingTradeRequest;
import com.integral.netting.model.TradeRequestLeg;
import com.integral.persistence.ExternalSystemId;
import com.integral.persistence.util.MessagingUtil;
import com.integral.pms.cache.PortfolioServiceCache;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.MathUtilC;
import com.integral.util.StringUtilC;
import java.text.DecimalFormat;
import java.text.NumberFormat;

public class ExternalCreditCheckUtil 
{
	private Log log = LogFactory.getLog( this.getClass() );
	private static final String EMPTY = "";
	private static final char PIPE_SEPARATOR = '|';
	private static final char TILDE_SEPARATOR = '~';
	private static final char COMMA_SEPARATOR = ',';
	public static final String MESSAGE_TYPE = "MsgType";
	public static final String MSG_FORMAT = "MsgFormat";
	public static final String MSG_FORMAT_JSON = "JSON";
	public static final String REPLY_TO = "ReplyTo";
	public static final String REQUEST_LIMIT_CHECK = "requestLimitCheck";
	public static final String ORDER_STATUS_NOTIFICATION = "orderStatusNotification";
	private static final String SERVER_PROPERTY = "serverIPAddress";
	private static final String SETTLEMENT_CODE = "SettlementCode";
	private static final ConcurrentMap<String, CountDownLatch> requestId2CountDownLatch = new ConcurrentHashMap<String, CountDownLatch>();
	private static final ConcurrentMap<String, Long> tradeId2ExpireTime = new ConcurrentHashMap<String, Long>();
	private static final ConcurrentMap<String, Map<String, String>> tradeId2correlationIdnFundId = new ConcurrentHashMap<String, Map<String, String>>();
	private static final ExternalCreditCheckUtil _instance = new ExternalCreditCheckUtil();
	private ConcurrentMap<String, ConcurrentMap<String, CountDownLatch>> countDownLatchMap = new ConcurrentHashMap<String, ConcurrentMap<String,CountDownLatch>>();
	private ConcurrentMap<String, ConcurrentMap<String, Long>> expireTimeMap = new ConcurrentHashMap<String, ConcurrentMap<String,Long>>();
	private ConcurrentMap<String, ConcurrentMap<String, ConcurrentMap<String, CreditLimitHubStatus>>> statusMap = 
			                                 new ConcurrentHashMap<String, ConcurrentMap<String,ConcurrentMap<String,CreditLimitHubStatus>>>();
	private ConcurrentMap<String, ConcurrentMap<String, ConcurrentMap<String, List<String>>>> errorCodeAndDescriptionMap = 
            new ConcurrentHashMap<String, ConcurrentMap<String,ConcurrentMap<String,List<String>>>>();

	public ConcurrentMap<String, ConcurrentMap<String, String>> getExecNotNotRequiredMap() {
		return execNotNotRequiredMap;
	}

	private ConcurrentMap<String, ConcurrentMap<String, String>> execNotNotRequiredMap =
            										new ConcurrentHashMap<String, ConcurrentMap<String, String>>();
	private ConcurrentMap<String, List<Boolean>> twoWayApprovalHandlingMap =
			new ConcurrentHashMap<String, List<Boolean>>();

	public ConcurrentMap<String, List<Boolean>> getTwoWayApprovalHandlingMap() {
		return twoWayApprovalHandlingMap;
	}

	private ExternalCreditCheckUtil()
	{
	}
	
	public static ExternalCreditCheckUtil getInstance()
	{
		return _instance;
	}
	 
	
    /**
     *  (ExternalCreditCheck enabled) && (trade is SEF)  
     * @param request
     * @return
     */
    private ExternalCreditCheckConfig getCreditProviderNameIfExternalCreditCheckRequired(Request request, Organization extCreditProvOrg)
    {
    	if(ConfigurationFactory.getSefMBean().isExternalCreditCheckEnabled())
    	{
    		log.info("getCreditProviderNameIfExternalCreditCheckRequired :: " +
    				"External Credit Check is required for trade " + request.getTransactionID());
    		ExternalCreditCheckConfig externalCreditConf = new ExternalCreditCheckConfig(request, null, true, null);
        	return externalCreditConf;
    	}
    	else
    	{
    		log.info("External credit check is not required for trade " + request.getTransactionID());
    	}
    	return null;
    }


	private String getExternalCreditLimitProvider(Counterparty cmCpty, Counterparty fiCpty, Trade trade) 
	{
		CreditLimitAdminService creditLimitAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService();
		TradingParty cmTp = (TradingParty)cmCpty;
		LegalEntity cmLe = cmTp.getLegalEntity();
		Organization cmOrg = cmLe.getOrganization();	
		TradingParty fiTp = ((LegalEntity)fiCpty).getTradingParty(cmOrg);
		String provider = null;
		if(fiTp != null)
		{
			provider = creditLimitAdminSvc.getExternalCreditLimitProvider(cmOrg, fiTp);
		}
		return provider;
	}
	
	
	
	public void performExternalCreditCheck(Request request, Organization extCreditProvOrg, WorkflowMessage wfMsg, boolean hasInternalCreditFailed)
	{
    	boolean externalCreditCheckSuccessful = false;   
    	boolean externalCreditCheckTimedout = false; 
    	List<String> errorCodeAndDescription = null;
    	String maker = null;
    	String transactionID = request.getTransactionID();
    	String workflow = (extCreditProvOrg == null ?  ISConstantsC.WF_ACCEPTANCE : ISConstantsC.WF_SUBSCRIPTION);
    	try
    	{
    		ExternalCreditCheckConfig externalCreditConf = new ExternalCreditCheckConfig(request, extCreditProvOrg, false, null);
        	
        	if(null == externalCreditConf || !externalCreditConf.isCreditCheckRequired())
        	{
        		return;
        	}        	
			CountDownLatch countDownLatch = new CountDownLatch(externalCreditConf.getNoOfCreditCheckRequests());
			//TODO: How do you cope with this when the server bounces ??
			maker = externalCreditConf.getMaker();
			ConcurrentMap<String, CountDownLatch> makerMap = countDownLatchMap.get(transactionID);
			if (makerMap == null)
			{
				makerMap = new ConcurrentHashMap<String, CountDownLatch>();
			}
			ConcurrentMap<String, CountDownLatch> oldMap = countDownLatchMap.putIfAbsent(transactionID, makerMap);
			if (oldMap != null)
			{
				makerMap = oldMap;
			}			
			makerMap.put(maker, countDownLatch);			
    		boolean messageSent = sendExternalCreditCheckRequest(request, externalCreditConf);
    		long timeOutInMills = ExternalCreditMBeanC.getInstance().getCreditCheckTimeout();   
    		

    		if(!messageSent)
    		{
    			log.error("performExternalCreditCheck :: could not send external " +
    					"credit check request for trade " + request.getTransactionID());
    			// The message was not even sent. So don't bother waiting for a reply. Just clean up the map and move on.
    			countDownLatchMap.get(request.getTransactionID()).remove(maker);	
    			if (countDownLatchMap.get(transactionID).size() == 0)
    			{
    				countDownLatchMap.remove(transactionID);
    			}
    		}
    		else
    		{  	
    			boolean countDownReached = countDownLatch.await(timeOutInMills, TimeUnit.MILLISECONDS);
	    		log.info("performExternalCreditCheck :: CountDownReached="+countDownReached+" | LatchCount="+countDownLatch.getCount());
	    		
	    		if(!countDownReached)
	    		{
	    			//Timed Out. Response not received. So log warn and remove the countDownLatch entry from map so that if a response comes later in time,
	    			//the handler will see that there is no mapped countdownLatch for that requestId and will un-reserve the credit.
	    			log.warn("performExternalCreditCheck :: Request for external credit check timed out for trade " + request.getTransactionID());
	    			updateStatus(transactionID, maker, CreditLimitHubStatus.Timedout);
	    			externalCreditCheckTimedout = true;
	    		}
	    		else
	    		{
	    			//CountDown reached. That means response received from adaptor. 
	    			log.info("performExternalCreditCheck :: Response for external credit check received for trade: " 
	    			                                                        + request.getTransactionID() + ":Maker:" + maker);   	
	    			externalCreditCheckSuccessful = isCreditCheckSuccessful(transactionID, maker);
	    			if(externalCreditCheckSuccessful && hasInternalCreditFailed){
                        request.getRequestAttributes ().setMakerCreditOverride ( true );
                    }
	    			
	    		}	    		
    		}
    	}
    	catch(Exception ex)
    	{        		
    		log.error("performExternalCreditCheck :: Exception while sending CreditLimit Request to Limit Hub. extCreditProvOrg=" + extCreditProvOrg, ex);
    		wfMsg.setStatus(MessageStatus.FAILURE);
    		ISUtilImpl.getInstance().addError( wfMsg, ISConstantsC.CREDIT_FAILURE_ERROR_DESCRIPTION, new Object[]{workflow, request.getTransactionID() + ":Maker:" + maker, ex.getMessage()} );
    	}
    	finally
    	{
    		ConcurrentMap<String, CountDownLatch> transactIdMap = countDownLatchMap.get(transactionID);
    		if (transactIdMap != null)
    		{
    			transactIdMap.remove(maker);
        		if (transactIdMap.size() == 0)
        		{
        			countDownLatchMap.remove(transactionID);
        		}
    		}
    		ConcurrentMap<String, ConcurrentMap<String, CreditLimitHubStatus>> makerStatusMap = this.statusMap.get(transactionID);
    		if (makerStatusMap != null)
    		{
    			makerStatusMap.remove(maker);
    			if (makerStatusMap.size() == 0)
    			{
    				this.statusMap.remove(transactionID);
    			}
    		}
    		if(!externalCreditCheckSuccessful){
    			errorCodeAndDescription = getErrorCodeAndDescription(transactionID, maker);
    		}
    		ConcurrentMap<String, ConcurrentMap<String, List<String>>> makerErrorCodeAndDescriptionMap = this.errorCodeAndDescriptionMap.get(transactionID);
    		if (makerErrorCodeAndDescriptionMap != null)
    		{
    			makerErrorCodeAndDescriptionMap.remove(maker);
    			if (makerErrorCodeAndDescriptionMap.size() == 0)
    			{
    				this.errorCodeAndDescriptionMap.remove(transactionID);
    			}
    		}
			twoWayApprovalHandlingMap.remove(request.getTransactionID());
    	}
    	    	

    	if(externalCreditCheckTimedout){
    		wfMsg.setStatus(MessageStatus.FAILURE);
    		ISUtilImpl.getInstance().addError( wfMsg, ISConstantsC.CREDIT_FAILURE_ERROR_DESCRIPTION1, 
    				new Object[]{workflow, request.getTransactionID(), "External Credit Check - Request Limit check timed out"} );
            log.warn( "performExternalCreditCheck :: CreditLimitValidation failed for TXID = " + request.getTransactionID() + ":Maker:" + maker);
    	}
    	else if(!externalCreditCheckSuccessful)
    	{     		
    		wfMsg.setStatus(MessageStatus.FAILURE);    		
    		if(errorCodeAndDescription != null && errorCodeAndDescription.size() > 1){
    		ISUtilImpl.getInstance().addError( wfMsg, ISConstantsC.CREDIT_FAILURE_ERROR_DESCRIPTION0, 
    				new Object[]{errorCodeAndDescription.get(0), errorCodeAndDescription.get(1)} );
    		}else{
    			ISUtilImpl.getInstance().addError( wfMsg, ISConstantsC.CREDIT_FAILURE_ERROR_DESCRIPTION0, 
        				new Object[]{"", ""} );
    		}
            log.warn( "performExternalCreditCheck :: CreditLimitValidation failed for TXID = " + request.getTransactionID() + ":Maker:" + maker);
    	}  
    	else
    	{
    		wfMsg.setStatus(MessageStatus.SUCCESS);
    	}
	}
    
	/**
	 * Check if external credit check is required. If yes, then get the details and send out an external credit check request.
	 * 
	 * @param request
	 * @param wfMsg
	 */
    public void doExternalCreditLimitCheckIfRequired(Request request, Organization extCreditProvOrg, WorkflowMessage wfMsg)
    {   
    	boolean externalCreditCheckSuccessful = false;    
    	String workflow = (extCreditProvOrg == null ?  ISConstantsC.WF_ACCEPTANCE : ISConstantsC.WF_SUBSCRIPTION);
    	try
    	{    		
        	ExternalCreditCheckConfig externalCreditConf = getCreditProviderNameIfExternalCreditCheckRequired(request, extCreditProvOrg);
        	if(null == externalCreditConf || !externalCreditConf.isCreditCheckRequired())
        	{
        		return;
        	}        	
			CountDownLatch countDownLatch = new CountDownLatch(externalCreditConf.getNoOfCreditCheckRequests());
			//TODO: How do you cope with this when the server bounces ??
			requestId2CountDownLatch.put(request.getTransactionID(), countDownLatch);

    		boolean messageSent = sendCreditLimitSTPDownload(request, externalCreditConf);

    		long timeOutInMills = ConfigurationFactory.getSefMBean().getExternalCreditCheckRequestTimeoutInMills();
    		
    		if(!messageSent)
    		{
    			log.error("doExternalCreditLimitCheckIfRequired :: could not send external " +
    					"credit check request for trade " + request.getTransactionID());
    			// The message was not even sent. So don't bother waiting for a reply. Just clean up the map and move on.
    			requestId2CountDownLatch.remove(request.getTransactionID());	
    		}
    		else
    		{    			    	
    			//The message was sent out the door. Await the response until a configured timeout.
    			Request actualRequest = HandlerCacheC.getHandlerCache().getRequest( request.getTransactionID());
    	    	Trade trade = actualRequest.getTrade();	 
    			boolean countDownReached = countDownLatch.await(timeOutInMills, TimeUnit.MILLISECONDS);
	    		log.info("doExternalCreditLimitCheckIfRequired :: CountDownReached="+countDownReached+" | LatchCount="+countDownLatch.getCount());
	    		
	    		if(!countDownReached)
	    		{
	    			//Timed Out. Response not received. So log warn and remove the countDownLatch entry from map so that if a response comes later in time,
	    			//the handler will see that there is no mapped countdownLatch for that requestId and will un-reserve the credit.
	    			log.warn("doExternalCreditLimitCheckIfRequired :: Request for external credit check timed out for trade " + request.getTransactionID());
	    			
	    			// Synchronize the following just in case a response arrives after timeout, there could be a race condition in setting the status on trade.
            		synchronized(trade)
            		{
            			trade.setCreditLimitHubStatus(CreditLimitHubStatus.Timedout.toString());
            		}
	    		}
	    		else
	    		{
	    			//CountDown reached. That means response received from adaptor. 
	    			// The map should have been ideally cleaned by the response listener. But cleaning it just in case.This should be a NOOP
	    			log.warn("doExternalCreditLimitCheckIfRequired :: Response for external credit check received for trade: " + request.getTransactionID());   	    	
	    			if(trade.getCreditLimitHubStatus()!=null && !CreditLimitHubStatus.Rejected.toString().equals(trade.getCreditLimitHubStatus()))
	    	    	{	    	    		
	    	    		wfMsg.setStatus(MessageStatus.SUCCESS);
	    	    		externalCreditCheckSuccessful = true;
	    	    	}
	    			else
	    			{
                		sendOrderStatusNotificationToExternalCreditProvider(request, OrderStatusNotificationType.Cancelled);
	    			}
	    		}	    		
    		}
    	}
    	catch(Exception ex)
    	{        		
    		log.error("doExternalCreditLimitCheckIfRequired :: Exception while sending CreditLimit Request to Limit Hub", ex);  
    		wfMsg.setStatus(MessageStatus.FAILURE);
    		ISUtilImpl.getInstance().addError( wfMsg, ISConstantsC.ERROR_INTERNALSERVER, new Object[]{workflow, request.getTransactionID(), ex.getMessage()} );
    	}
    	finally
    	{
    		requestId2CountDownLatch.remove(request.getTransactionID());
    		tradeId2correlationIdnFundId.remove(request.getTransactionID());
    	}
    	    	

    	if(!externalCreditCheckSuccessful)
    	{
    		wfMsg.setStatus(MessageStatus.FAILURE);
    		ISUtilImpl.getInstance().addError( wfMsg, ISConstantsC.ERROR_INTERNALSERVER, 
    				new Object[]{workflow, request.getTransactionID(), "External Credit check failed"} );
            log.warn( "doExternalCreditLimitCheckIfRequired :: CreditLimitValidation failed for TXID = " + request.getTransactionID() );
    	}        
    }
        
    private boolean sendCreditLimitSTPDownload(Request request, ExternalCreditCheckConfig externalCreditConf)
    {
    	boolean messageSent = false;
    	Map<String, List<CptyCreditCheckConfig>> externalCreditCheckConfigMap = externalCreditConf.getExternalCreditCheckConfigMap();
    	
    	for(Map.Entry<String ,List<CptyCreditCheckConfig>> entry : externalCreditCheckConfigMap.entrySet())
    	{
    		String creditHubName = entry.getKey();    	
	    	String externalCreditProviderDetails = ConfigurationFactory.getSefMBean().getExternalCreditProviderDetails(creditHubName);
	    	if(externalCreditProviderDetails != null && !"".equals(externalCreditProviderDetails))
	    	{
		    	String transactionId = request.getTransactionID();
		    	Trade trade = request.getTrade();
	    		String[] tokens = externalCreditProviderDetails.split(";");
	    		if(tokens.length >= 2)
	    		{
	    			String finalDestination = tokens[0];
	    			String externalCreditHubLei = tokens[1];
	    	    	String requestForCreditLimit = constructCreditLimitRequestString(request, entry.getValue(), externalCreditHubLei);
	    	    	
			    	// We send the STP message if download is enabled or for a re-send to the SDR.	    			
			        TradeDownloadServiceC downloadService = TradeServiceFactory.getTradeDownloadService();
			        Map<String, String> properties = new HashMap<String, String>();
			        setJMSProperties(properties, finalDestination, transactionId, trade.getChannel(), creditHubName);
			        String vsName = ConfigurationFactory.getServerMBean().getVirtualServerName();
			        properties.put(SERVER_PROPERTY, vsName);
			        properties.put(MESSAGE_TYPE, REQUEST_LIMIT_CHECK);	
			        
	    	    	StringBuilder sb = new StringBuilder(200);
	    	    	sb.append("sendCLR :: Sending External CreditCheck request");
	    	    	sb.append(" | TradeId=").append(transactionId);
	    	    	sb.append(" | QueueName=").append(finalDestination);
	    	    	sb.append(" | ExternalCreditValidatorLEI=").append(externalCreditHubLei);
	    	    	sb.append(" | Msg = [").append(requestForCreditLimit).append(']');
	    	    	sb.append(" | Properties = [").append(properties).append(']');
	    	    	log.info(sb.toString());
			        messageSent = downloadService.sendMessage(finalDestination, requestForCreditLimit, properties, transactionId, creditHubName, null);
	    		}
	    	}
	    	else
	    	{
	    		log.warn("sendCLR :: External credit provider " +
	    				"details not configured for provider="+creditHubName+" | TradeId="+request.getTransactionID());
	    	}
    	}
        return messageSent;
    }
    private ConcurrentMap<String, CreditLimitHubStatus> initializeStatusMap(final String transactionID, final String maker)
    {
    	ConcurrentMap<String, ConcurrentMap<String, CreditLimitHubStatus>> makerStatusMap = this.statusMap.get(transactionID);
    	if (makerStatusMap == null)
    	{
    		makerStatusMap = new ConcurrentHashMap<String, ConcurrentMap<String,CreditLimitHubStatus>>();
    	}
    	ConcurrentMap<String, ConcurrentMap<String, CreditLimitHubStatus>> oldMap = this.statusMap.putIfAbsent(transactionID, makerStatusMap);
    	if (oldMap != null)
    	{
    		makerStatusMap = oldMap;
    	}
    	ConcurrentMap<String, CreditLimitHubStatus> correlationIdMap = makerStatusMap.get(maker);
    	if (correlationIdMap == null)
    	{
    		correlationIdMap = new ConcurrentHashMap<String, ExternalCreditCheckUtil.CreditLimitHubStatus>();
    	}
    	ConcurrentMap<String, CreditLimitHubStatus> old = makerStatusMap.putIfAbsent(maker, correlationIdMap);
    	if (old != null)
    	{
    		correlationIdMap = old;
    	}
    	return correlationIdMap;
    }
    
    private ConcurrentMap<String, List<String>> initializeErrorCodeAndDescriptionMap(final String transactionID, final String maker)
    {
    	ConcurrentMap<String, ConcurrentMap<String, List<String>>> makerErrorCodeAndDescriptionMap = this.errorCodeAndDescriptionMap.get(transactionID);
    	if (makerErrorCodeAndDescriptionMap == null)
    	{
    		makerErrorCodeAndDescriptionMap = new ConcurrentHashMap<String, ConcurrentMap<String,List<String>>>();
    	}
    	ConcurrentMap<String, ConcurrentMap<String, List<String>>> oldMap = this.errorCodeAndDescriptionMap.putIfAbsent(transactionID, makerErrorCodeAndDescriptionMap);
    	if (oldMap != null)
    	{
    		makerErrorCodeAndDescriptionMap = oldMap;
    	}
    	ConcurrentMap<String, List<String>> correlationIdMap = makerErrorCodeAndDescriptionMap.get(maker);
    	if (correlationIdMap == null)
    	{
    		correlationIdMap = new ConcurrentHashMap<String, List<String>>();
    	}
    	ConcurrentMap<String, List<String>> old = makerErrorCodeAndDescriptionMap.putIfAbsent(maker, correlationIdMap);
    	if (old != null)
    	{
    		correlationIdMap = old;
    	}
    	return correlationIdMap;
    }
    
    private boolean sendExternalCreditCheckRequest(Request request, ExternalCreditCheckConfig externalCreditConf)
    {
    	boolean messageSent = false;
		ExternalCreditMBean externalCreditMBean = ExternalCreditMBeanC.getInstance();
		String routingKey = externalCreditMBean.getRoutingKey();
		String bindingKey = externalCreditMBean.getBindingKey();
		String maker = externalCreditConf.getMaker();
		if (routingKey == null || "".equals(routingKey.trim()))
		{
			log.error("ExternalCreditCheckUtil.sendCreditLimitSTPDownload:routing key is not setup correctly :Runting Key: " + routingKey);
			return false;
		}
		String transactionID = request.getTransactionID();
		ConcurrentMap<String, CreditLimitHubStatus> correlationIdMap = initializeStatusMap(transactionID, maker);
		initializeErrorCodeAndDescriptionMap(transactionID, maker);
    	Map<String, List<CptyCreditCheckConfig>> externalCreditCheckConfigMap = externalCreditConf.getExternalCreditCheckConfigMap();
    	
    	for(Map.Entry<String ,List<CptyCreditCheckConfig>> entry : externalCreditCheckConfigMap.entrySet())
    	{		    	
			List<CptyCreditCheckConfig> creditCheckConfigList = entry.getValue();
			for (CptyCreditCheckConfig creditCheckConfig : creditCheckConfigList)
			{	
				String takerCorrelationId = creditCheckConfig.getTakerCorrelationId();
				correlationIdMap.put(takerCorrelationId, CreditLimitHubStatus.INITIAL);
				List<CptyCreditCheckConfig> list = new ArrayList<CptyCreditCheckConfig>(1);
				list.add(creditCheckConfig);
    	    	String requestForCreditLimit = constructNonSEFCreditLimitRequestString(request, list, maker);
		        Map<String, String> properties = new HashMap<String, String>();			        
		        String vsName = ConfigurationFactory.getServerMBean().getVirtualServerName();
		        properties.put(SERVER_PROPERTY, vsName);
		        properties.put(MESSAGE_TYPE, REQUEST_LIMIT_CHECK);	
		        if (bindingKey != null)
		        {
		        	properties.put(REPLY_TO, bindingKey + ":" + vsName);	
		        }
				String externalCreditCheckMessageFormat = ISFactory.getInstance().getISMBean().getExternalCreditCheckMessageFormat(maker);
		        if(externalCreditCheckMessageFormat.equals(MSG_FORMAT_JSON)){
					properties.put(MSG_FORMAT, MSG_FORMAT_JSON);
				}
    	    	StringBuilder sb = new StringBuilder(200);
    	    	sb.append("sendCLR :: Sending External CreditCheck request");
    	    	sb.append(" | TradeId=").append(transactionID);
    	    	sb.append(" | RoutingKey=").append(routingKey);	    	    	
    	    	sb.append(" | Msg = [").append(requestForCreditLimit).append(']');
    	    	sb.append(" | Properties = [").append(properties).append(']');
    	    	log.info(sb.toString());
    	        MessageHolder holder = new MessageHolder();
    	        holder.setMessage( requestForCreditLimit );
    	        holder.setMessageProperties( properties );	    	        
    	        try 
    	        {
    	        	MessagingUtil.sendSTPMessageOverIntegralMessaging(routingKey , holder);
    	        	messageSent = true;
    	        }
    	        catch (Throwable t)
    	        {
    	        	log.error("ExternalCreditCheckUtil.sendCreditLimitSTPDownload:Problem with sending message to rabit MQ over routing key :" + routingKey, t);
    	        	messageSent = false; 
    	        }	    	    				       
			}
    	}

        return messageSent;
    }
    
    
    private void setJMSProperties(Map<String, String> properties, String finalDestination, String transactionId, String channel, String orgName)
    {
        properties.put( TradeServiceConstants.DOWNLOAD_DESTINATION_KEY, finalDestination );
        //properties.put( TradeServiceConstants.USER_KEY, ( ( User ) IdcSessionManager.getInstance().getSessionContext().getUser() ).getFullName() );
        properties.put( TradeServiceConstants.DOWNLOAD_MESSAGE_EVENT, "ExtCreditChk");
        properties.put( TradeServiceConstants.DOWNLOAD_MESSAGE_TOPIC, TradeServiceConstants.TOPIC_TRADE );
        properties.put( TradeServiceConstants.DOWNLOAD_MESSAGE_ID, transactionId);
        properties.put( TradeServiceConstants.DOWNLOAD_MESSAGE_GUID, transactionId );
        properties.put( TradeServiceConstants.DOWNLOAD_MESSAGE_TO, orgName);
        properties.put( TradeServiceConstants.DOWNLOAD_MESSAGE_CHANNEL, channel);
        properties.put( TradeServiceConstants.DOWNLOAD_FORMAT_KEY, TradeServiceConstants.FPML53_FORMAT);
		properties.put( TradeServiceConstants.ORGANIZATION_KEY, orgName);
    }
    
    /**
     *  Return String format : TradeClassification | TradeDate | INFX | takerOrg | makerOrg | takerCorrelationId | makerCorrelationId | LimitHub | UPI | TID | ExternalID | No.Allocations | [Allocation details] | TradeLeg~TradeLeg |
     *  
     *  TradeLeg format : USI, USINamespace, Currency1, Currency1Amount, Currency2, Currency2Amount, DealtCurrency, SettledCurrency, 
     *                    FixingDate, ValueDate, Side, Rate, SpotRate, ForwardPoints, LegClassification ~
     *  
     *  Allocation details format : Fund, FCM, CreditHub, Currency, Amount, USI, USINamespace, CHLei~ 
     */
	private String constructCreditLimitRequestString(Request request,            
			List<CptyCreditCheckConfig> cptyCreditCheckConfigList, String externalCreditProviderLei)
	{
		StringBuilder sb = new StringBuilder(100);
		
		Trade trade = request.getTrade();
	
		sb.append(trade.getTradeClassification().getShortName()).append(PIPE_SEPARATOR);
		sb.append(trade.getTradeDate()!=null?trade.getTradeDate().getFormattedDate(IdcDate.YYYY_MM_DD_HYPHEN):EMPTY).append(PIPE_SEPARATOR);		
		addCommonTradeIdentifiersAndPartyIdentifiers(externalCreditProviderLei, sb, trade);
		addCreditCheckUnits(trade.getTransactionID(), cptyCreditCheckConfigList, sb);
		UTIInfo info = UTIGenerator.generateUTI(request);
		FXMarketDataElement mde = MDSFactory.getInstance().getMarketDataElement(request.getCurrencyPair().getBaseCurrency(), request.getCurrencyPair().getVariableCurrency(), true);
		double bidSpotRate = 0.0;
		double offerSpotRate = 0.0;
		double midSpotRate = 0.0;
		if(mde != null && mde.getFXPrice() != null){
			bidSpotRate = mde.getFXPrice().getBidFXRate().getSpotRate();
			offerSpotRate = mde.getFXPrice().getOfferFXRate().getSpotRate();
			midSpotRate = mde.getFXRate().getRate();
		}
		// Trade Leg level details
		sb.append(serializeTradeLegs(trade.getTradeLegs(), info.getUTI(), getBidOfferMode(request),
				bidSpotRate, offerSpotRate, midSpotRate)).append(PIPE_SEPARATOR);					
		return sb.toString();
	}

	private int getBidOfferMode(Request request) {
		int bidOfferMode = -1;
		boolean singleLegTrade = request.getRequestPrices().size() == 1 ;
		for(DealingPrice dealingPrice :request.getRequestPrices()){
			if(singleLegTrade || dealingPrice.getTradeLeg().getName().equals("farLeg")){
				bidOfferMode = dealingPrice.getBidOfferMode();
			}
		}
		return bidOfferMode;
	}
	
	private String constructNonSEFCreditLimitRequestString(Request request,            
			            List<CptyCreditCheckConfig> cptyCreditCheckConfigList, String maker)
	{
		StringBuilder sb = new StringBuilder(100);
		
		Trade trade = request.getTrade();
		
		sb.append(trade.getTradeClassification().getShortName()).append(PIPE_SEPARATOR);
		sb.append(trade.getTradeDate()!=null?trade.getTradeDate().getFormattedDate(IdcDate.YYYY_MM_DD_HYPHEN):EMPTY).append(PIPE_SEPARATOR);		
		addCommonNonSEFTradeIdentifiersAndPartyIdentifiers(cptyCreditCheckConfigList.get(0), maker, sb, trade);		

		User user = request.getUser();
		String userStr = "";
		if (user != null)
		{
			userStr = user.getShortName();
		}
		sb.append(userStr).append(PIPE_SEPARATOR);
		int bidOfferMode = DealingPrice.TWO_WAY;
		// always send far leg side in case of swaps
		Collection<DealingPrice> legs = request.getRequestPrices();
		if (legs != null && legs.size() > 0)
		{
			if (legs.size() == 1)
			{
				// single leg
				bidOfferMode = legs.iterator().next().getBidOfferMode();
			}
			else
			{
				// swap get bid offer mode from far leg
				DealingPrice fardp = request.getRequestPrice(ISConstantsC.FAR_LEG);
				if (fardp != null)
				{
					bidOfferMode = fardp.getBidOfferMode();
				}
			}
			
		}
		sb.append(bidOfferMode).append(PIPE_SEPARATOR);
		Timestamp submissionTimeStamp = request.getSubmissionTime();
		long submissionTime = System.currentTimeMillis();
		if (submissionTimeStamp != null)
		{
			submissionTime = submissionTimeStamp.getTime();
		}
		sb.append(submissionTime).append(PIPE_SEPARATOR);
		String executionVenue = "";		
		if (request.isMTF())
		{
			sb.append("true").append(PIPE_SEPARATOR);
			String mtfVenue = request.getMTFVenueName();
			if (mtfVenue != null)
			{
				executionVenue = mtfVenue;
			}
		}
		else
		{
			sb.append("false").append(PIPE_SEPARATOR);
		}		
		sb.append(executionVenue).append(PIPE_SEPARATOR);
		
		MiFIDTradeParams mifidTradeParams = trade.getMiFIDTradeParams();
		String siExecution = "false";
		if (mifidTradeParams != null)
		{
			Boolean siExecObj = mifidTradeParams.getSIExecution();
			if (siExecObj != null && siExecObj.equals(Boolean.TRUE))
			{
				siExecution = "true";
			}
		}
		sb.append(siExecution).append(PIPE_SEPARATOR);
		CptyCreditCheckConfig ccconfig = cptyCreditCheckConfigList.get(0);
	    String takerLEI = (ccconfig.getTakerLEI() == null ? "" : ccconfig.getTakerLEI());
	    String makerLEI = (ccconfig.getMakerLEI() == null ? "" : ccconfig.getMakerLEI());
	    sb.append(takerLEI).append(PIPE_SEPARATOR);
	    sb.append(makerLEI).append(PIPE_SEPARATOR);
		addCreditCheckUnitsNonSEF(trade.getTransactionID(), cptyCreditCheckConfigList, sb, maker);
		UTIInfo info = UTIGenerator.generateUTI(request);
		FXMarketDataElement mde = MDSFactory.getInstance().getMarketDataElement(request.getCurrencyPair().getBaseCurrency(), request.getCurrencyPair().getVariableCurrency(), true);
		double bidSpotRate = 0.0;
		double offerSpotRate = 0.0;
		double midSpotRate = 0.0;
		if(mde != null && mde.getFXPrice() != null){
			bidSpotRate = mde.getFXPrice().getBidFXRate().getSpotRate();
			offerSpotRate = mde.getFXPrice().getOfferFXRate().getSpotRate();			
			midSpotRate = mde.getFXRate().getRate();			
		}
		// Trade Leg level details
		sb.append(serializeTradeLegs(trade.getTradeLegs(), info.getUTI(), getBidOfferMode(request),
				bidSpotRate, offerSpotRate, midSpotRate)).append(PIPE_SEPARATOR);					
		return sb.toString();
	} 
    
	
    private boolean sendOrderStatusNotificationToExternalCreditProvider(Request request, 
    		OrderStatusNotificationType notificationType)
    {      
    	boolean messageSent = false;        
        try
        {

        	ExternalCreditCheckConfig externalCreditConf = getCreditProviderNameIfExternalCreditCheckRequired(request, null);
        	if(null == externalCreditConf || !externalCreditConf.isCreditCheckRequired())
        	{
        		return messageSent;
        	}
        	
        	Map<String, List<CptyCreditCheckConfig>> externalCreditCheckConfigMap = externalCreditConf.getExternalCreditCheckConfigMap();
        	
        	for(Map.Entry<String ,List<CptyCreditCheckConfig>> entry : externalCreditCheckConfigMap.entrySet())
        	{
        		String creditHubName = entry.getKey(); 
		    	String externalCreditProviderDetails = ConfigurationFactory.getSefMBean().getExternalCreditProviderDetails(creditHubName);
		    	if(externalCreditProviderDetails != null && !"".equals(externalCreditProviderDetails))
		    	{
			    	String transactionId = request.getTransactionID();			    	
		    		String[] tokens = externalCreditProviderDetails.split(";");
		    		if(tokens.length >= 2)
		    		{
		    			String finalDestination = tokens[0];
		    			String externalCreditHubLei = tokens[1];
		    		
		    			for(CptyCreditCheckConfig creditCheckConfig: entry.getValue())
		    			{
			    	    	String orderStatusNotificationString = constructOrderStatusNotificationString(creditCheckConfig.getUSI(), creditCheckConfig.getCreditCheckAckId(), notificationType);
			    	    	
					    	// We send the STP message if download is enabled or for a re-send to the SDR.	    			
					        TradeDownloadServiceC downloadService = TradeServiceFactory.getTradeDownloadService();
					        Map<String, String> properties = new HashMap<String, String>();					        
					        String vsName = ConfigurationFactory.getServerMBean().getVirtualServerName();
					        properties.put(SERVER_PROPERTY, vsName);
					        properties.put(MESSAGE_TYPE, ORDER_STATUS_NOTIFICATION);
			    	    	
			    	    	StringBuilder sb = new StringBuilder(200);
			    	    	sb.append("sendOSN :: Sending External CreditCheck request");
			    	    	sb.append(" | TradeId=").append(creditCheckConfig.getUSI());
			    	    	sb.append(" | QueueName=").append(finalDestination);
			    	    	sb.append(" | ExternalCreditValidatorLEI=").append(externalCreditHubLei);
			    	    	sb.append(" | NotificationType=").append(notificationType);
			    	    	sb.append(" | Msg = [").append(orderStatusNotificationString).append(']');
			    	    	sb.append(" | Properties = [").append(properties).append(']');
			    	    	log.info(sb.toString());
			    	    	
					        messageSent = downloadService.sendMessage(finalDestination, orderStatusNotificationString, properties, 
					        		transactionId, creditHubName, null);
		    			}
		    		}
		    	}
		    	else
		    	{
		    		log.warn("sendOrderStatusNotificationToExternalCreditProvider :: External credit provider " +
		    				"details not configured for provider="+creditHubName+" | TradeId="+request.getTransactionID());
		    	}
        	}
        }
    	catch(Exception ex)
    	{        		
    		StringBuilder sb = new StringBuilder(200);
    		sb.append("sendOrderStatusNotificationToExternalCreditProvider :: " +
    				"Exception while sending OrderStatusNotification to Limit Hub");
    		sb.append(" | TradeId=").append(request.getTransactionID());
    		sb.append(" | NotificationType=").append(notificationType);
    		log.error( sb.toString(), ex);        		
    	}    
        return messageSent;        
    }
    
    public void unReserveExternalCreditLimitIfRequired(Request request)
    {      	
    	sendOrderStatusNotificationToExternalCreditProvider(request, OrderStatusNotificationType.Cancelled);    	
    }
    
    public void confirmExternalCreditLimitIfRequired(Request request)
    {  	
    	sendOrderStatusNotificationToExternalCreditProvider(request, OrderStatusNotificationType.Completed);
    }
    
    /**
     * filter map if notification need not to be send for a particular correlation id is not required 
     * @param externalCreditConf
     */
    private void filterExternlCreditCheckMap(final ExternalCreditCheckConfig externalCreditConf, String transactionID,
											 String maker, OrderStatusNotificationType notificationType)
    {
    	if (externalCreditConf == null)
    	{
    		return;
    	}
   
    	Map<String, List<CptyCreditCheckConfig>> externalCreditCheckConfigMap 
    												= externalCreditConf.getExternalCreditCheckConfigMap();	
    	if (externalCreditCheckConfigMap == null || externalCreditCheckConfigMap.size() == 0)
    	{
    		return;
    	}
    	Iterator<Map.Entry<String, List<CptyCreditCheckConfig>>> it = externalCreditCheckConfigMap.entrySet().iterator();
    	if (it != null)
    	{
    		while (it.hasNext())
    		{
    			Map.Entry<String, List<CptyCreditCheckConfig>> entry = it.next();
    			List<CptyCreditCheckConfig> configList = entry.getValue();
        		if (configList == null)
        		{
        			continue;
        		}
        		List<CptyCreditCheckConfig> eligibleForRemoval = new ArrayList<CptyCreditCheckConfig>();
        		for (CptyCreditCheckConfig config : configList)
        		{
        			String correlationId = config.getTakerCorrelationId();
        			if (!isExecNotificationRequired(transactionID, maker, correlationId)
							&& !OrderStatusNotificationType.Cancelled.equals(notificationType))
        			{
        				log.info("ExternalCreditCheckUtil.filterExternlCreditCheckMap:ExecutionNotification not required for TransacionId:" 
        																+ transactionID + ":Maker:" + maker + ":CorrelationID:" + correlationId);
        				eligibleForRemoval.add(config);        				
        			}
        		}
        		configList.removeAll(eligibleForRemoval);
        		if (configList.isEmpty())
        		{
        			it.remove();
        		}
    		}
    	}
    	
    }
    
    
    public void tradeVerified(Request request)
    {
    	String transactionID = request.getTransactionID();
    	Trade trade = request.getTrade();
    	if (trade == null)
    	{
    		log.info("ExternalCreditCheckUtil.tradeVerified:Trade Null , not cleaning up exec notification not required map:TransactionID:" + transactionID);
    		return;
    	}
    	String maker = trade.getCounterpartyB().getOrganization().getShortName();
    	removeEntryFromExecNotificationNotRequiredMap(transactionID, maker);
    }

	public boolean isExternalCreditEnabledWithNewFlow(Request request) {
		Trade trade = request.getTrade();
		LegalEntity requestorLE = ((TradingParty) request.getCounterparty()).getLegalEntity();
		LegalEntity providerLE = CounterpartyUtilC.getLegalEntity(trade.getCounterpartyB());
		List<CreditEntity> creditEntities = CreditUtilC.getAllCreditEntitiesBetween(requestorLE, providerLE);
		if (creditEntities == null || creditEntities.size() == 0) {
			log.info("ExternalCreditCheckUtil.isExternalCreditEnabledWithNewFlow() :: txnId: " + request.getTransactionID()+", creditEntities: "+creditEntities
			+", requestorLE: "+requestorLE+", providerLE: "+providerLE);
			return false;
		}
		for (CreditEntity ce : creditEntities) {
			LegalEntity creditTakerLE = ce.getLegalEntity();
			Organization creditProviderOrg = ce.getOrganization();
			TradingParty creditTakerOrgTP = ce.getTradingParty();
			TradingParty creditProviderOrgTP = creditTakerLE.getTradingParty(creditProviderOrg);
			boolean isExternalCreditEnabled = false;
			if (creditTakerOrgTP != null) {
				CreditLimitAdminService creditLimitAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService();
				isExternalCreditEnabled = creditLimitAdminSvc.isExternalCreditEnabled(creditProviderOrg, creditProviderOrgTP);
			}
			String externalCreditProvider = CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean().
					getExternalCreditProviderName(creditProviderOrg);
			if (isExternalCreditEnabled && externalCreditProvider != null) {
				if (CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean().isExternalCreditProviderNewFlowEnabled(creditProviderOrg)) {
					return true;
				}
			}
			log.info("ExternalCreditCheckUtil.isExternalCreditEnabledWithNewFlow() :: txnId: " + request.getTransactionID()+", creditEntities: "+creditEntities
					+", creditTakerOrgTP: "+creditTakerOrgTP+", externalCreditProvider: "+externalCreditProvider+", isExternalCreditEnabled: "+isExternalCreditEnabled);
		}
		log.info("ExternalCreditCheckUtil.isExternalCreditEnabledWithNewFlow() :: txnId: " + request.getTransactionID()+", creditEntities: "+creditEntities
				+", requestorLE: "+requestorLE+", providerLE: "+providerLE);
		return false;
	}
    
    /**
     * This is accpetance workflow for NON SEF 
     * @param request
     * @param notificationType
     * @param wfm
     */
	public void notifyExternalCredit(Request request, OrderStatusNotificationType notificationType, WorkflowMessage wfm, Organization externalcreditProvOrg) {
		boolean orderStatusNotificationSuccessful = false;
		boolean orderStatusNotificationTimedout = false; 
		List<String> errorCodeAndDescription = null;
		if (notificationType == null)
		{
			notificationType = OrderStatusNotificationType.Completed;
		}
    	String workflow = (OrderStatusNotificationType.Updated == notificationType ? ISConstantsC.WF_VERIFICATION :ISConstantsC.WF_ACCEPTANCE) ;
    	String transactionID = request.getTransactionID();
    	String maker = null;
    	try
    	{    		
        	ExternalCreditCheckConfig externalCreditConf = new ExternalCreditCheckConfig(request, externalcreditProvOrg, false, notificationType);
        	maker = externalCreditConf.getMaker();
        	filterExternlCreditCheckMap(externalCreditConf, transactionID, maker, notificationType);
        	if(null == externalCreditConf || !externalCreditConf.isCreditCheckRequired())
        	{
        		return;
        	}
      
        	CountDownLatch countDownLatch = null;
        	if (OrderStatusNotificationType.Completed == notificationType || OrderStatusNotificationType.Updated == notificationType)
        	{          	       	
        		countDownLatch = new CountDownLatch(externalCreditConf.getNoOfCreditCheckRequests());
    			//TODO: How do you cope with this when the server bounces ??
    			maker = externalCreditConf.getMaker();
    			ConcurrentMap<String, CountDownLatch> makerMap = countDownLatchMap.get(transactionID);
    			if (makerMap == null)
    			{
    				makerMap = new ConcurrentHashMap<String, CountDownLatch>();
    			}
    			ConcurrentMap<String, CountDownLatch> oldMap = countDownLatchMap.putIfAbsent(transactionID, makerMap);
    			if (oldMap != null)
    			{
    				makerMap = oldMap;
    			}			
    			makerMap.put(maker, countDownLatch);
        	}

    		boolean messageSent = sendOrderStatusNotification(request, externalCreditConf, notificationType);

    		long timeOutInMills = ExternalCreditMBeanC.getInstance().getCreditCheckTimeout();
    	

    		if(!messageSent)
    		{
    			log.error("ExternalCreditCheckUtil.notifyExternalCredit :: could not send OrderStatusNotification for Trade" +  request.getTransactionID());
    			// The message was not even sent. So don't bother waiting for a reply. Just clean up the map and move on.
    			countDownLatchMap.remove(request.getTransactionID());	
    		}
    		else
    		{    			    	
    			//The message was sent out the door. Await the response until a configured timeout.
    	    	boolean countDownReached = true;
    	    	if (countDownLatch != null)
    	    	{
    	    		countDownReached = countDownLatch.await(timeOutInMills, TimeUnit.MILLISECONDS);
    	    		log.info("ExternalCreditCheckUtil.notifyExternalCredit :: CountDownReached="
	                         			+ countDownReached +" | LatchCount= " + countDownLatch.getCount());
    	    	}
	    		
	    		if(!countDownReached)
	    		{
	    			//Timed Out. Response not received. So log warn and remove the countDownLatch entry from map so that if a response comes later in time,
	    			//the handler will see that there is no mapped countdownLatch for that requestId and will un-reserve the credit.
	    			log.warn("ExternalCreditCheckUtil.notifyExternalCredit :: Order Status Notification timed out for trade " + request.getTransactionID() + ":Maker:" + maker);    	    		
    	    		orderStatusNotificationSuccessful = false;
    	    		orderStatusNotificationTimedout = true;
	    		}
	    		else
	    		{
	    			//CountDown reached. That means response received from adaptor. 
	    			// The map should have been ideally cleaned by the response listener. But cleaning it just in case.This should be a NOOP
	    			orderStatusNotificationSuccessful = isCreditCheckSuccessful(transactionID, maker);	    			
	    			log.info("ExternalCreditCheckUtil.notifyExternalCredit  :: Response for OrderStatusNotification received for trade: " + request.getTransactionID());  	    	
	    		 		
	    		}	    		
    		}
    	}
    	catch(Exception ex)
    	{        		
    		log.error("ExternalCreditCheckUtil.notifyExternalCredit :: Exception while sending OrderStatusNotification to Limit Hub", ex);  
    		orderStatusNotificationSuccessful = false;
    	}
    	finally
    	{
    		if(!orderStatusNotificationSuccessful){
    			errorCodeAndDescription = getErrorCodeAndDescription(transactionID, maker);
    		}
    		this.countDownLatchMap.remove(transactionID);  
    		this.statusMap.remove(transactionID);
    		this.errorCodeAndDescriptionMap.remove(transactionID);
			removeEntryFromExecNotificationNotRequiredMap(transactionID, maker);
    	}
    	    	

    	if(orderStatusNotificationTimedout){
    		wfm.setStatus(MessageStatus.FAILURE);
    		ISUtilImpl.getInstance().addError( wfm, ISConstantsC.CREDIT_FAILURE_ERROR_DESCRIPTION2, 
    				new Object[]{workflow, request.getTransactionID(), "External Credit Check - Take Credit Execution Notification timed out"} );
            log.warn( "ExternalCreditCheckUtil.notifyExternalCredit ::OrderStatusNitification failed for TXID = " + request.getTransactionID() );
    		
    	}
    	else if(!orderStatusNotificationSuccessful)
    	{    		
    		wfm.setStatus(MessageStatus.FAILURE);
    		if(errorCodeAndDescription != null&& errorCodeAndDescription.size() > 1){
    		ISUtilImpl.getInstance().addError( wfm, ISConstantsC.CREDIT_FAILURE_ERROR_DESCRIPTION3, 
    				new Object[]{errorCodeAndDescription.get(0), errorCodeAndDescription.get(1)} );
    		}else{
    			ISUtilImpl.getInstance().addError( wfm, ISConstantsC.CREDIT_FAILURE_ERROR_DESCRIPTION3, 
        				new Object[]{"", ""} );
    		}
            log.warn( "ExternalCreditCheckUtil.notifyExternalCredit ::OrderStatusNitification failed for TXID = " + request.getTransactionID() );
    	}   
    	else
    	{
    		wfm.setStatus(MessageStatus.SUCCESS);
    	}
	}
	
	private boolean sendOrderStatusNotification(Request request, ExternalCreditCheckConfig externalCreditConf, OrderStatusNotificationType notificationType)
	{
		boolean messageSent = false;		
		try 
		{
			ExternalCreditMBean externalCreditMBean = ExternalCreditMBeanC.getInstance();
			String routingKey = externalCreditMBean.getRoutingKey();
			String bindingKey = externalCreditMBean.getBindingKey();
			if (routingKey == null || "".equals(routingKey.trim())) {
				log.error("ExternalCreditCheckUtil.sendOrderStatusNotification:routing key is not setup correctly :Runting Key:" + routingKey);
				return false;
			}
			String maker = externalCreditConf.getMaker();
			String transactionID = request.getTransactionID();
			ConcurrentMap<String, CreditLimitHubStatus> makerMap = initializeStatusMap(transactionID, maker);
			initializeErrorCodeAndDescriptionMap(transactionID, maker);
			Map<String, List<CptyCreditCheckConfig>> externalCreditCheckConfigMap = externalCreditConf
					.getExternalCreditCheckConfigMap();			

			for (Map.Entry<String, List<CptyCreditCheckConfig>> entry : externalCreditCheckConfigMap.entrySet()) {
				String transactionId = request.getTransactionID();
				List<CptyCreditCheckConfig> creditCheckConfigList = entry.getValue();
				for (CptyCreditCheckConfig creditCheckConfig : creditCheckConfigList) {
					String takerCorrelationID = creditCheckConfig.getTakerCorrelationId();
					makerMap.put(takerCorrelationID, CreditLimitHubStatus.INITIAL);
					List<CptyCreditCheckConfig> list = new ArrayList<CptyCreditCheckConfig>(1);
					list.add(creditCheckConfig);
					String orderStatusNotificationStr = constructNonSEFOrderStatusNotification(creditCheckConfig, notificationType, request);
					Map<String, String> properties = new HashMap<String, String>();
					String vsName = ConfigurationFactory.getServerMBean().getVirtualServerName();
					properties.put(SERVER_PROPERTY, vsName);
					properties.put(MESSAGE_TYPE, ORDER_STATUS_NOTIFICATION);
					if (bindingKey != null) {
						properties.put(REPLY_TO, bindingKey + ":" + vsName);
					}
					String externalCreditCheckMessageFormat = ISFactory.getInstance().getISMBean().getExternalCreditCheckMessageFormat(maker);
					if(externalCreditCheckMessageFormat.equals(MSG_FORMAT_JSON)){
						properties.put(MSG_FORMAT, MSG_FORMAT_JSON);
					}
					StringBuilder sb = new StringBuilder(200);
					sb.append("ExternalCreditCheckUtil.sendOrderStatusNotification ::");
					sb.append(" | TradeId=").append(transactionId);
					sb.append(" | RoutingKey=").append(routingKey);
					sb.append(" | Msg = [").append(orderStatusNotificationStr).append(']');
					sb.append(" | Properties = [").append(properties).append(']');
					log.info(sb.toString());
					MessageHolder holder = new MessageHolder();
					holder.setMessage(orderStatusNotificationStr);
					holder.setMessageProperties(properties);
					try {
						MessagingUtil.sendSTPMessageOverIntegralMessaging(routingKey, holder);		
						messageSent = true;
					} catch (Throwable t) {
						log.error("ExternalCreditCheckUtil.sendOrderStatusNotification:Problem with sending OrderStatusNotification to rabit MQ over routing key :" + routingKey, t);	
						messageSent = false;
					}
				}
			}
		}
		catch(Throwable t)
		{
			log.error("ExternalCreditCheckUtil.sendOrderStatusNotification:Problem with confirming credit:TransactionId:" + request.getTransactionID(), t);
			messageSent = false;
		}
		return messageSent;
	}
    
    
	// creditLimitResponse is in the format "msgType|approvalStatus|expiryTime|correlationId|USI|parentTradeId|approvalId"
    // Even in case of rejections, don't fail fast after receiving just the first one. Collect all the responses you can.
    public void handleCreditLimitResponse(String creditLimitResponse)
    {
    	log.info("Received external credit check response : " + creditLimitResponse);
    	if(!StringUtilC.isNullOrEmpty(creditLimitResponse))
    	{
    		String[] tokens = creditLimitResponse.split("\\|");
    		
    		if(tokens.length >= 7)
    		{
            	String messageType = tokens[0];
            	String creditLimitApprovalStatus = tokens[1];
            	long expiryTimeInMills = 0;
            	String correlationId = tokens[3];
            	String usi = tokens[4];
            	String parentTradeId = tokens[5]; 
    			String expiryTimeInMillsStr = tokens[2];
    			if(!StringUtilC.isNullOrEmpty(expiryTimeInMillsStr))
    			{
    				expiryTimeInMills = Long.parseLong(expiryTimeInMillsStr);
    			}
    			if(StringUtilC.isNullOrEmpty(parentTradeId) || "null".equals(parentTradeId))
    			{
    				parentTradeId = usi;
    			}
                if(!StringUtilC.isNullOrEmpty(parentTradeId))
                {   
                	CountDownLatch countDownLatch = requestId2CountDownLatch.get(parentTradeId);                	
                	Request actualRequest = HandlerCacheC.getHandlerCache().getRequest(parentTradeId);                	                	
                	
                	if(countDownLatch != null && actualRequest != null)
                	{
                		Trade trade = actualRequest.getTrade();
                		//set CreditLimitApprovalAckId like fund1-id1;fund2-id2;fund3-id3;
                		
                		CreditLimitHubStatus approvalStatus = "true".equalsIgnoreCase(creditLimitApprovalStatus)?
                				CreditLimitHubStatus.Approved:CreditLimitHubStatus.Rejected;
                		synchronized(trade)
                		{
	                		if(trade.getCreditLimitHubStatus() == null || 
	                				(!CreditLimitHubStatus.Rejected.toString().equals(trade.getCreditLimitHubStatus())
	                						&& !CreditLimitHubStatus.Timedout.toString().equals(trade.getCreditLimitHubStatus())))
	                		{
	                			trade.setCreditLimitHubStatus(approvalStatus.toString());
	                		}
	                		if(parentTradeId.equals(usi)) //TradeId is the actual trade itself
	                		{
	                			Map<String, String> correlationId2FundId = tradeId2correlationIdnFundId.get(parentTradeId);
	                			if(correlationId2FundId!=null){
	                				String fund = correlationId2FundId.get(correlationId);
	                				if(!StringUtilC.isNullOrEmpty(fund)){
	                					String currentCrdLmtAckId = trade.getCreditLimitHubId();
	    	                			if(!StringUtilC.isNullOrEmpty( currentCrdLmtAckId )){
	    	                				currentCrdLmtAckId+=';'+(fund+"="+correlationId);	    	                				
	    	                			}else{
	    	                				currentCrdLmtAckId=(fund+"="+correlationId);
	    	                			}
	    	                			trade.setCreditLimitHubId(currentCrdLmtAckId);		
	                				}else{
	                					log.error("No fund found in correlationId map for correlationId " + correlationId);
	                				}
	                			}else{
	                				log.error("No entry found in correlationId map for trade " + parentTradeId);
	                			}
	                			
	                		}
	                		else //TradeId must be an allocation trade
	                		{                
	                			
	                			trade.setFund2ExternalCreditCheckId(usi, correlationId);		
	                		}
                		}
                		if(expiryTimeInMills > 0)
                		{
                			tradeId2ExpireTime.putIfAbsent(parentTradeId, expiryTimeInMills);
                		}                		                		
    	            	countDownLatch.countDown();
    	            	log.info("handleCreditLimitResponse :: Processed the credit check response and " +
    	            			"counteddown the latch : tradeId=" + usi + " | parentTradeId=" + parentTradeId + "| LatchCount=" + countDownLatch.getCount());
                	}
                	else if(actualRequest != null)
                	{
                		//If we reached here, then the CountDownLatch timed-out already and got removed. So, lets us just unreserve credit.
                		sendOrderStatusNotificationToExternalCreditProvider(actualRequest, OrderStatusNotificationType.Cancelled);
                	}
                }
                else
                {
        			log.warn("Cannot process the external credit check response as parentTradeId is null");                	
                }
    		}
    		else
    		{
    			log.warn("Malformed response. Unexpected number of tokens noticed in response [ " + creditLimitResponse + ']' );
    		}
    	}
    }
	// creditLimitResponse is in the format "msgType|approvalStatus|expiryTime|correlationId|USI|parentTradeId|approvalId"
    // Even in case of rejections, don't fail fast after receiving just the first one. Collect all the responses you can.
    public void handleExternalCreditCheckResponse(String creditLimitResponse)
    {
    	log.info("Received external credit check response : " + creditLimitResponse);
    	if(!StringUtilC.isNullOrEmpty(creditLimitResponse))
    	{
    		String[] tokens = creditLimitResponse.split("\\|");
    		
    		if(tokens.length >= 9)
    		{
            	String messageType = tokens[0];
            	String creditLimitApprovalStatus = tokens[1];
            	long expiryTimeInMills = 0;
            	String correlationId = tokens[3];
            	String usi = tokens[4];
            	String parentTradeId = tokens[5]; 
            	String maker = tokens[7];
            	String sendExecNotificationStr = tokens[8];
            	boolean sendExecNotification = true;
            	try {
					sendExecNotification = Boolean.parseBoolean(sendExecNotificationStr);
				} catch (Exception e) {
					sendExecNotification = true;
				}
    			String expiryTimeInMillsStr = tokens[2];
    			if(!StringUtilC.isNullOrEmpty(expiryTimeInMillsStr))
    			{
    				expiryTimeInMills = Long.parseLong(expiryTimeInMillsStr);
    			}
    			if(StringUtilC.isNullOrEmpty(parentTradeId) || "null".equals(parentTradeId))
    			{
    				parentTradeId = usi;
    			}
                if(!StringUtilC.isNullOrEmpty(parentTradeId))
                {   
                	Map<String, CountDownLatch> latchMap = countDownLatchMap.get(parentTradeId);
                	if (latchMap == null)
                	{
                		log.info("ExternalCreditCheckUtil.handleExternalCreditCheckRespons:Request already timed out: " + parentTradeId + ":Maker:" + maker);
                		return;
                	}
                	CountDownLatch countDownLatch = latchMap.get(maker); 
                
                	Request actualRequest = HandlerCacheC.getHandlerCache().getRequest(parentTradeId);                	                	
                	
                	if(countDownLatch != null && actualRequest != null)
                	{                		
                		//set CreditLimitApprovalAckId like fund1-id1;fund2-id2;fund3-id3;
						CreditLimitHubStatus approvalStatus;
						List<Boolean> bidAndOfferApprovals = twoWayApprovalHandlingMap.get(parentTradeId);
						if (bidAndOfferApprovals != null) {
							String[] approvals = creditLimitApprovalStatus.split(":");
							if (approvals.length== 2 && ("true".equalsIgnoreCase(approvals[0]) || "true".equalsIgnoreCase(approvals[1]))) {
								approvalStatus = CreditLimitHubStatus.Approved;
								bidAndOfferApprovals.add(Boolean.parseBoolean(approvals[0]));
								bidAndOfferApprovals.add(Boolean.parseBoolean(approvals[1]));
							} else {
								approvalStatus = CreditLimitHubStatus.Rejected;
							}
						} else {
							approvalStatus = "true".equalsIgnoreCase(creditLimitApprovalStatus) ?
									CreditLimitHubStatus.Approved : CreditLimitHubStatus.Rejected;
						}
                		updateStatus(parentTradeId, maker, correlationId, approvalStatus);
                		if(approvalStatus.equals(CreditLimitHubStatus.Rejected) && (!tokens[tokens.length-2].equals("null") || !tokens[tokens.length-1].equals("null"))){
                			updateErrorCodeAndDescription(parentTradeId, maker, correlationId, tokens[tokens.length-2],tokens[tokens.length-1]);
                		}
                		if(expiryTimeInMills > 0)
                		{
                			ConcurrentMap<String, Long> map = expireTimeMap.get(parentTradeId);
                			if (map == null)
                			{
                				map = new ConcurrentHashMap<String, Long>();                				
                			}   
                			ConcurrentMap<String, Long> old = expireTimeMap.putIfAbsent(parentTradeId, map);
                			if (old != null)
                			{
                				map = old;
                			}
                			map.putIfAbsent(maker, expiryTimeInMills);
                		}    
                		if (!sendExecNotification)
                		{
                			updateExecNotificationNotRequired(parentTradeId, maker, correlationId);
                		}
    	            	countDownLatch.countDown();
    	            	log.info("handleExternalCreditCheckResponse :: Processed the credit check response and " +
    	            			"counteddown the latch : tradeId=" + usi + " | parentTradeId=" + parentTradeId + "| LatchCount=" + countDownLatch.getCount());

                	}
                	else if(actualRequest != null)
                	{
                		//If we reached here, then the CountDownLatch timed-out already and got removed. So, lets us just unreserve credit.
                		sendOrderStatusNotificationToExternalCreditProvider(actualRequest, OrderStatusNotificationType.Cancelled);
                	}
                }
                else
                {
        			log.warn("Cannot process the external credit check response as parentTradeId is null");                	
                }
    		}
    		else
    		{
    			log.warn("Malformed response. Unexpected number of tokens noticed in response [ " + creditLimitResponse + ']' );
    		}
    	}
    }
    
    private boolean isCreditCheckSuccessful(final String transactionID, final String maker)
    {
      	ConcurrentMap<String, ConcurrentMap<String, CreditLimitHubStatus>> makerStatusMap = this.statusMap.get(transactionID);
    	if (makerStatusMap == null)
    	{
    		log.info("ExternalCreditCheckUtil.updateStatus:could not update staus, no mapping found for transactionID:" + transactionID);
    		return false;
    	}
    
    	ConcurrentMap<String, CreditLimitHubStatus> correlationIdMap = makerStatusMap.get(maker);
    	if (correlationIdMap == null)
    	{
    		log.info("ExternalCreditCheckUtil.updateStatus:could not update staus, no mapping found for transactionID:" 
    																				+ transactionID + ":Maker:" + maker);
    		return false;
    	}
    	for (Map.Entry<String, CreditLimitHubStatus> entry : correlationIdMap.entrySet())
    	{
    		CreditLimitHubStatus value = entry.getValue();
    		if (CreditLimitHubStatus.Approved != value)
    		{
    			return false;
    		}
    	}
    	return true;
    }
    
    private List<String> getErrorCodeAndDescription(final String transactionID, final String maker)
    {
      	ConcurrentMap<String, ConcurrentMap<String, List<String>>> makerStatusMap = this.errorCodeAndDescriptionMap.get(transactionID);
    	if (makerStatusMap == null)
    	{
    		log.info("ExternalCreditCheckUtil.getErrorCodeAndDescription, no mapping found for transactionID:" + transactionID);
    		return null;
    	}
    
    	ConcurrentMap<String, List<String>> correlationIdMap = makerStatusMap.get(maker);
    	if (correlationIdMap == null)
    	{
    		log.info("ExternalCreditCheckUtil.getErrorCodeAndDescription, no mapping found for transactionID:" 
    																				+ transactionID + ":Maker:" + maker);
    		return null;
    	}         	
    	for (Map.Entry<String, List<String>> entry : correlationIdMap.entrySet())
    	{
    		return entry.getValue();
    	}
    	return null;
    }
    
    private boolean updateStatus(final String transactionID, final String maker, final String correlationId, final CreditLimitHubStatus status)
    {
       	ConcurrentMap<String, ConcurrentMap<String, CreditLimitHubStatus>> makerStatusMap = this.statusMap.get(transactionID);
    	if (makerStatusMap == null)
    	{
    		log.info("ExternalCreditCheckUtil.updateStatus:could not update staus, no mapping found for transactionID:" + transactionID);
    		return false;
    	}
    
    	ConcurrentMap<String, CreditLimitHubStatus> correlationIdMap = makerStatusMap.get(maker);
    	if (correlationIdMap == null)
    	{
    		log.info("ExternalCreditCheckUtil.updateStatus:could not update staus, no mapping found for transactionID:" 
    																				+ transactionID + ":Maker:" + maker);
    		return false;
    	}
    	CreditLimitHubStatus oldStatus = correlationIdMap.get(correlationId);
    	if (oldStatus == null)
    	{
    		log.info("ExternalCreditCheckUtil.updateStatus:could not update staus, no mapping found for transactionID:" 
    															+ transactionID + ":Maker:" + maker + ":CorrelationId:" + correlationId);
    		return false;    		
    	}
    	correlationIdMap.put(correlationId, status);
    	return true;
    }
    
    private boolean updateErrorCodeAndDescription(final String transactionID, final String maker, final String correlationId, final String errorCode, final String errorDescription)
    {
       	ConcurrentMap<String, ConcurrentMap<String, List<String>>> makerStatusMap = this.errorCodeAndDescriptionMap.get(transactionID);
    	if (makerStatusMap == null)
    	{
    		log.info("ExternalCreditCheckUtil.updateErrorCodeAndDescription : could not update Error Code And Description, no mapping found for transactionID:" + transactionID);
    		return false;
    	}
    
    	ConcurrentMap<String, List<String>> correlationIdMap = makerStatusMap.get(maker);
    	if (correlationIdMap == null)
    	{
    		log.info("ExternalCreditCheckUtil.updateErrorCodeAndDescription : could not update Error Code And Description, no mapping found for transactionID:" 
    																				+ transactionID + ":Maker:" + maker);
    		return false;
    	}
    	List<String> errorCodeAndDescription = new ArrayList<String>();
    	if(!errorCode.equals("null")){
    		errorCodeAndDescription.add(errorCode);
    	}else{
    		errorCodeAndDescription.add("");
    	}
    	if(!errorDescription.equals("null")){
    		errorCodeAndDescription.add(errorDescription);
    	}else{
    		errorCodeAndDescription.add("");
    	}
    	correlationIdMap.put(correlationId, errorCodeAndDescription);
    	return true;
    }
    private void updateExecNotificationNotRequired(final String transactionID, final String maker, final String correlationId)
    {
       	ConcurrentMap<String, String> makerMap = this.execNotNotRequiredMap.get(transactionID);
    	if (makerMap == null)
    	{
    		makerMap = new ConcurrentHashMap<String, String>();
    		ConcurrentMap<String, String> old = this.execNotNotRequiredMap.putIfAbsent(transactionID, makerMap);
    		if (old != null)
    		{
    			makerMap = old;
    		}
    	}
    	makerMap.put(maker, correlationId);
    }
    
    private boolean isExecNotificationRequired(final String transactionID, final String maker, final String correlationId)
    {
    	if (transactionID == null || maker == null || correlationId == null)
    	{
    		return true;
    	}
    	ConcurrentMap<String, String> makerMap = this.execNotNotRequiredMap.get(transactionID);
    	if (makerMap == null)
    	{
    		log.info("ExternalCreditCheckUtil.isExecNotificationRequired:Execution Notification not required for transactionID:" 
    																+ transactionID + ":Maker:" + maker + ":CorrelationId:" + correlationId);
    		return true;
    	}
    	return !(correlationId.equals(makerMap.get(maker)));
    }
    
    private void removeEntryFromExecNotificationNotRequiredMap(final String transactionID, final String maker)
    {
    	if (transactionID == null || maker == null)
    	{
    		return;
    	}
    	this.execNotNotRequiredMap.remove(transactionID);
    	log.info("ExternalCreditCheckUtil.removeEntryFromExecNotificationNotRequiredMap:removed Entry for transactionID:" + transactionID);    	
    }
    
    private boolean updateStatus(final String transactionID, final String maker, final CreditLimitHubStatus status)
    {
       	ConcurrentMap<String, ConcurrentMap<String, CreditLimitHubStatus>> makerStatusMap = this.statusMap.get(transactionID);
    	if (makerStatusMap == null)
    	{
    		log.info("ExternalCreditCheckUtil.updateStatus:could not update staus, no mapping found for transactionID:" + transactionID);
    		return false;
    	}
    
    	ConcurrentMap<String, CreditLimitHubStatus> correlationIdMap = makerStatusMap.get(maker);
    	if (correlationIdMap == null)
    	{
    		log.info("ExternalCreditCheckUtil.updateStatus:could not update staus, no mapping found for transactionID:" 
    																				+ transactionID + ":Maker:" + maker);
    		return false;
    	}
    	Set<String> keys = correlationIdMap.keySet();
    	if (keys != null)
    	{
    		for (String key : keys)
    		{
    			if (correlationIdMap != null)
    			{
    				correlationIdMap.put(key, status);
    			}
    		}
    	}
    	return true;
    }
    
	private String constructOrderStatusNotificationString(String usi, String ackId, OrderStatusNotificationType notificationType)
	{
		StringBuilder sb = new StringBuilder(80);							
		sb.append(notificationType).append(PIPE_SEPARATOR);
		sb.append(ackId).append(PIPE_SEPARATOR);				
		sb.append(usi).append(PIPE_SEPARATOR);		
		return sb.toString();
	}
	
	
	private String constructNonSEFOrderStatusNotification(CptyCreditCheckConfig creditCheckConfig,  OrderStatusNotificationType notificationType, Request request)
	{
		String usi = creditCheckConfig.getUSI();
		String ackId = creditCheckConfig.getCreditCheckAckId();
		StringBuilder sb = new StringBuilder(80);							
		sb.append(notificationType).append(PIPE_SEPARATOR);
		sb.append(ackId).append(PIPE_SEPARATOR);				
		sb.append(usi).append(PIPE_SEPARATOR);
		String takerClientId = creditCheckConfig.getFund();
		String makerClientId = creditCheckConfig.getFcm();	
		sb.append(creditCheckConfig.getExtCrediProviderNameSpace()).append(PIPE_SEPARATOR);
		sb.append(takerClientId).append(PIPE_SEPARATOR);
		sb.append(makerClientId).append(PIPE_SEPARATOR);
		Trade trade = request.getTrade();
		if (trade == null)
		{
			sb.append("").append(PIPE_SEPARATOR);
		}
		else
		{
			sb.append(trade.getTradeClassification().getShortName()).append(PIPE_SEPARATOR);
		}
		Date acceptanceDateTime = request.getAcceptedDate();
		long accptTime = System.currentTimeMillis();
		if (acceptanceDateTime != null)
		{
			accptTime = acceptanceDateTime.getTime();
		}
		sb.append(accptTime).append(PIPE_SEPARATOR);
		String executionVenue = "";		
		if (request.isMTF())
		{
			sb.append("true").append(PIPE_SEPARATOR);
			String mtfVenue = request.getMTFVenueName();
			if (mtfVenue != null)
			{
				executionVenue = mtfVenue;
			}
		}
		else
		{
			sb.append("false").append(PIPE_SEPARATOR);
		}		
		sb.append(executionVenue).append(PIPE_SEPARATOR);
		MiFIDTradeParams mifidTradeParams = trade.getMiFIDTradeParams();
		String siExecution = "false";
		if (mifidTradeParams != null)
		{
			Boolean siExecObj = mifidTradeParams.getSIExecution();
			if (siExecObj != null && siExecObj.equals(Boolean.TRUE))
			{
				siExecution = "true";
			}
			
		}		
		sb.append(siExecution).append(PIPE_SEPARATOR);		
	    String takerLEI = (creditCheckConfig.getTakerLEI() == null ? "" : creditCheckConfig.getTakerLEI());
	    String makerLEI = (creditCheckConfig.getMakerLEI() == null ? "" : creditCheckConfig.getMakerLEI());
	    sb.append(takerLEI).append(PIPE_SEPARATOR);
	    sb.append(makerLEI).append(PIPE_SEPARATOR);
		if (trade != null)
		{
			
			Collection<?> tradeLegs = trade.getTradeLegs();
			if (tradeLegs != null)
			{
				UTIInfo info = UTIGenerator.generateUTI(request);
				FXMarketDataElement mde = MDSFactory.getInstance().getMarketDataElement(request.getCurrencyPair().getBaseCurrency(), request.getCurrencyPair().getVariableCurrency(), true);
				double bidSpotRate = 0.0;
				double offerSpotRate = 0.0;
				double midSpotRate = 0.0;
				if(mde != null && mde.getFXPrice() != null){
					bidSpotRate = mde.getFXPrice().getBidFXRate().getSpotRate();
					offerSpotRate = mde.getFXPrice().getOfferFXRate().getSpotRate();
					midSpotRate = mde.getFXRate().getRate();
				}
				sb.append(serializeTradeLegs(tradeLegs, info.getUTI(), getBidOfferMode(request),
						bidSpotRate, offerSpotRate, midSpotRate));
			}
		}			
		return sb.toString();
	}

	private void addCommonTradeIdentifiersAndPartyIdentifiers(
			String externalCreditProviderLei, StringBuilder sb, Trade trade) 
	{			
		String takerClientId = getLimitHubClientIdFromOrg(trade.getCounterpartyA().getOrganization());
		String makerClientId = getLimitHubClientIdFromOrg(trade.getCounterpartyB().getOrganization());
		Organization infx = trade.getSEFOrg();
		LegalEntity defaultInfxDealingEntity = infx.getDefaultDealingEntity();
		sb.append(defaultInfxDealingEntity != null ? defaultInfxDealingEntity.getLEI():EMPTY).append(PIPE_SEPARATOR);
		sb.append(takerClientId).append(PIPE_SEPARATOR);
		sb.append(makerClientId).append(PIPE_SEPARATOR);
		sb.append(trade.getTransactionID()+"_1").append(PIPE_SEPARATOR);
		sb.append(trade.getTransactionID()+"_2").append(PIPE_SEPARATOR);
		sb.append(externalCreditProviderLei).append(PIPE_SEPARATOR);
		
		// Trade Identifiers		
		sb.append(trade.getUPI()).append(PIPE_SEPARATOR);
		sb.append(trade.getTransactionID()).append(PIPE_SEPARATOR);
		sb.append(trade.getExternalRequestId()!=null?trade.getExternalRequestId():EMPTY).append(PIPE_SEPARATOR);
	}
	
	private void addCommonNonSEFTradeIdentifiersAndPartyIdentifiers(CptyCreditCheckConfig creditCheckConfig,
			                                           						String maker, StringBuilder sb, Trade trade) 
	{			
		//String takerClientId = getLimitHubClientIdFromOrg(trade.getCounterpartyA().getOrganization());
		//String makerClientId = getLimitHubClientIdFromOrg(trade.getCounterpartyB().getOrganization());
		String takerClientId = creditCheckConfig.getFund();
		String makerClientId = creditCheckConfig.getFcm();	
		sb.append(creditCheckConfig.getExtCrediProviderNameSpace()).append(PIPE_SEPARATOR);
		sb.append(takerClientId).append(PIPE_SEPARATOR);
		sb.append(makerClientId).append(PIPE_SEPARATOR);
		sb.append(creditCheckConfig.getTakerCorrelationId()).append(PIPE_SEPARATOR);
		sb.append(creditCheckConfig.getMakerCorrelationId()).append(PIPE_SEPARATOR);
		sb.append(maker).append(PIPE_SEPARATOR);
		
		// Trade Identifiers		
		sb.append(trade.getUPI()).append(PIPE_SEPARATOR);
		sb.append(trade.getTransactionID()).append(PIPE_SEPARATOR);
		sb.append(trade.getExternalRequestId()!=null ? trade.getExternalRequestId():EMPTY).append(PIPE_SEPARATOR);
	}
	
	private String getLimitHubClientIdFromOrg(Organization org)
	{
		ExternalSystemId clientIdExtSys = org.getExternalSystemId(TradeServiceConstants.TRAIANA_LH_ACCOUNT_ID);			
		String accountId = clientIdExtSys!=null?clientIdExtSys.getSystemId():getOrgLeIdOrName(org);
		return accountId;
	}

	private String getOrgLeIdOrName(Organization org)
	{
		String leiOrShortName = org.getLEI();
		if(StringUtils.isNullOrEmptyString(leiOrShortName)){
			LegalEntity defaultSefLe = org.getDefaultSEFDealingEntity();
			if(defaultSefLe!=null){
				leiOrShortName = defaultSefLe.getLEI();
			}
			if(leiOrShortName == null){				
				LegalEntity defaultLe = org.getDefaultDealingEntity();
				if(defaultLe!=null){
					leiOrShortName = defaultLe.getLEI();
				}
			}
		}
		if(StringUtils.isNullOrEmptyString(leiOrShortName))
		{
			leiOrShortName = org.getShortName();
		}
		return leiOrShortName;
	}
	
	
	private void addCreditCheckUnits(String tradeId, List<CptyCreditCheckConfig> cptyCreditCheckConfigList, StringBuilder messageBuilder) 
	{	
		StringBuilder sb = new StringBuilder(100);
		int noOfAllocations = 0;
		for(CptyCreditCheckConfig cptyCreditCheckConfig:cptyCreditCheckConfigList)
		{
			sb.append(cptyCreditCheckConfig.getFund()).append(COMMA_SEPARATOR);
			sb.append(cptyCreditCheckConfig.getFcm()).append(COMMA_SEPARATOR);
			sb.append(cptyCreditCheckConfig.getExternalCreditHub()).append(COMMA_SEPARATOR);
			sb.append(cptyCreditCheckConfig.getCurrency()).append(COMMA_SEPARATOR);
			sb.append(cptyCreditCheckConfig.getAmount()).append(COMMA_SEPARATOR);
			sb.append(cptyCreditCheckConfig.getUSI()).append(COMMA_SEPARATOR);
			sb.append(cptyCreditCheckConfig.getUSINamespace()).append(COMMA_SEPARATOR);
			sb.append(cptyCreditCheckConfig.getCH()).append(TILDE_SEPARATOR);	
			if(cptyCreditCheckConfig.isChildTrade()){
				noOfAllocations++;
			}else{
				
				Map<String, String> correlationId2FundId = tradeId2correlationIdnFundId.get(tradeId);
				if(correlationId2FundId==null)
				{
					correlationId2FundId = new HashMap<String, String>();
					tradeId2correlationIdnFundId.putIfAbsent(tradeId, correlationId2FundId);
				}
				if(cptyCreditCheckConfig.isFI()){
					correlationId2FundId.put(tradeId+"_1", cptyCreditCheckConfig.getFund());
				}else{
					correlationId2FundId.put(tradeId+"_2", cptyCreditCheckConfig.getFund());
				}
			}
		}
		sb.append(PIPE_SEPARATOR);
		messageBuilder.append(noOfAllocations).append(PIPE_SEPARATOR).append(sb);
	}
	
	private void addCreditCheckUnitsNonSEF(String tradeId, List<CptyCreditCheckConfig> cptyCreditCheckConfigList, StringBuilder messageBuilder, String maker) 
	{	
		StringBuilder sb = new StringBuilder(100);
		int noOfAllocations = 0;
		for(CptyCreditCheckConfig cptyCreditCheckConfig:cptyCreditCheckConfigList)
		{
			sb.append(cptyCreditCheckConfig.getFund()).append(COMMA_SEPARATOR);
			sb.append(cptyCreditCheckConfig.getFcm()).append(COMMA_SEPARATOR);
			sb.append(cptyCreditCheckConfig.getExternalCreditHub()).append(COMMA_SEPARATOR);
			sb.append(cptyCreditCheckConfig.getCurrency()).append(COMMA_SEPARATOR);
			sb.append(cptyCreditCheckConfig.getAmount()).append(COMMA_SEPARATOR);
			sb.append(cptyCreditCheckConfig.getUSI()).append(COMMA_SEPARATOR);
			sb.append(cptyCreditCheckConfig.getUSINamespace()).append(COMMA_SEPARATOR);
			sb.append(cptyCreditCheckConfig.getCH()).append(TILDE_SEPARATOR);	
			if(cptyCreditCheckConfig.isChildTrade())
			{
				noOfAllocations++;
			}
		}
		sb.append(PIPE_SEPARATOR);
		messageBuilder.append(noOfAllocations).append(PIPE_SEPARATOR).append(sb);
	}
	
	
	private String serializeTradeLegs(Collection tradeLegs, String generatedUTI, int bidOfferMode,
			double bidSpotRate, double offerSpotRate, double midSpotRate)
	{
		List<Object> copy = new ArrayList<Object>(tradeLegs);
		
		if (tradeLegs.size() > 1)
		{
			// sort it as per value date ..
			Collections.sort(copy, new DateComparator());
		}			
			
		StringBuilder sb = new StringBuilder(100);
		for(Object leg : copy)
		{
			TradeLeg tradeLeg = (TradeLeg)leg;
			FXLeg fxLeg = (FXLeg)leg;
			FXPaymentParameters fxPaymentParameters = fxLeg.getFXPayment();
			int side = 0;
    		if(fxPaymentParameters.isBuyingCurrency1())
    		{
    			//BUY SIDE
    			side = 1;
    		}
    		else
    		{
    			//SELL SIDE
    			side = 2;    			
    		}    		
			sb.append(tradeLeg.getUSI()).append(COMMA_SEPARATOR);
			if (tradeLeg.getUTI() != null)
			{
				sb.append(tradeLeg.getUTI());
			}
			else
			{
				if(tradeLeg.getName().equals("farLeg")){
					sb.append(generatedUTI+"F");
				}else{
					sb.append(generatedUTI);
				}
			}
			sb.append(COMMA_SEPARATOR);
			sb.append(tradeLeg.getUSINamespace()).append(COMMA_SEPARATOR);
			sb.append(fxPaymentParameters.getCurrency1().getShortName()).append(COMMA_SEPARATOR);
			String currency1Amount = fxPaymentParameters.getCurrency1AmountSTP();	
			if(fxPaymentParameters.getCurrency1Amount() == 0){
				//Term ccy trade is not yet used for calculating BuyingCurrency1 flag
				if(bidOfferMode == 3){
					fxPaymentParameters.setCurrency1Amount(MathUtilC.correctFloatingPointsCalculationPrecision(fxPaymentParameters.getCurrency2Amount() / midSpotRate));
				}else if(bidOfferMode == 2){
					fxPaymentParameters.setCurrency1Amount(MathUtilC.correctFloatingPointsCalculationPrecision(fxPaymentParameters.getCurrency2Amount() / offerSpotRate));
	    		}else{
	    			fxPaymentParameters.setCurrency1Amount(MathUtilC.correctFloatingPointsCalculationPrecision(fxPaymentParameters.getCurrency2Amount() / bidSpotRate));	    			    			
	    		}
				sb.append(formatAmount(fxPaymentParameters.getCurrency1Amount())).append(COMMA_SEPARATOR);
				fxPaymentParameters.setCurrency1Amount(0);
			}else{
				sb.append(currency1Amount).append(COMMA_SEPARATOR);
			}
			sb.append(fxPaymentParameters.getCurrency2().getShortName()).append(COMMA_SEPARATOR);
			String currency2Amount = fxPaymentParameters.getCurrency2AmountSTP();	
			if(fxPaymentParameters.getCurrency2Amount() == 0){
				if(bidOfferMode == 3){
					fxPaymentParameters.setCurrency2Amount(MathUtilC.multiply(fxPaymentParameters.getCurrency1Amount(), midSpotRate));
				}else if(bidOfferMode == 2){					
					fxPaymentParameters.setCurrency2Amount(MathUtilC.multiply(fxPaymentParameters.getCurrency1Amount(), offerSpotRate));
	    		}else{
	    			fxPaymentParameters.setCurrency2Amount(MathUtilC.multiply(fxPaymentParameters.getCurrency1Amount(), bidSpotRate));	    			
	    		}
				sb.append(formatAmount(fxPaymentParameters.getCurrency2Amount())).append(COMMA_SEPARATOR);
                fxPaymentParameters.setCurrency2Amount(0);
			}else{
				sb.append(currency2Amount).append(COMMA_SEPARATOR);
			}
			sb.append(fxPaymentParameters.getDealtCurrency().getShortName()).append(COMMA_SEPARATOR);
			sb.append(fxPaymentParameters.getCurrency1().getShortName()).append(COMMA_SEPARATOR);
			sb.append(fxPaymentParameters.getFixingDate()!=null?fxPaymentParameters.getFixingDate().getFormattedDate(IdcDate.YYYY_MM_DD_HYPHEN):EMPTY).append(COMMA_SEPARATOR);
			String valueDate = (fxPaymentParameters.getValueDate()!=null?fxPaymentParameters.getValueDate().getFormattedDate(IdcDate.YYYY_MM_DD_HYPHEN):EMPTY);
			sb.append(valueDate).append(COMMA_SEPARATOR);// Value Date
			Tenor t = fxPaymentParameters.getTenor();
			String tenorStr = null;
			if (t != null)
			{
				tenorStr = t.getName();
			}
			if (tenorStr == null)
			{
				tenorStr = valueDate;
			}
			sb.append(tenorStr).append(COMMA_SEPARATOR);
			sb.append(side).append(COMMA_SEPARATOR);
			
			if(fxPaymentParameters.getFXRate().getRate() == 0){
				if(bidOfferMode == 3){
					sb.append(midSpotRate).append(COMMA_SEPARATOR);
					sb.append(midSpotRate).append(COMMA_SEPARATOR);
				}else if(bidOfferMode == 2){
					sb.append(offerSpotRate).append(COMMA_SEPARATOR);
					sb.append(offerSpotRate).append(COMMA_SEPARATOR);
	    		}else{
	    			sb.append(bidSpotRate).append(COMMA_SEPARATOR);
	    			sb.append(bidSpotRate).append(COMMA_SEPARATOR);
	    		}
				
			}else{
				sb.append(fxPaymentParameters.getFXRate().getRate()).append(COMMA_SEPARATOR);
				sb.append(fxPaymentParameters.getFXRate().getSpotRate()).append(COMMA_SEPARATOR);
			}
			sb.append(fxPaymentParameters.getFXRate().getForwardPoints()).append(COMMA_SEPARATOR);			
			sb.append(tradeLeg.getTradeLegClassification().getShortName());			
			sb.append(TILDE_SEPARATOR);
		}
		return sb.toString();
	}

	public static String formatAmount(double amount) {
		NumberFormat formatter = new DecimalFormat("#.###########");
		return formatter.format(amount);
	}
	
	private static class DateComparator implements Comparator<Object>{

		@Override
		public int compare(Object obj1, Object obj2) {
			IdcDate valueDate1 = null;
			if (obj1 instanceof FXLeg)
			{
				FXLeg fxLeg1 = (FXLeg)obj1;
				FXPaymentParameters fxPaymentParameters1 = fxLeg1.getFXPayment();
				if (fxPaymentParameters1 != null)
				{
					valueDate1 = fxPaymentParameters1.getValueDate();
					
				}				
			}
			
			IdcDate valueDate2 = null;
			if (obj2 instanceof FXLeg)
			{
				FXLeg fxLeg2 = (FXLeg)obj2;
				FXPaymentParameters fxPaymentParameters2 = fxLeg2.getFXPayment();
				if (fxPaymentParameters2 != null)
				{
					valueDate2 = fxPaymentParameters2.getValueDate();
					
				}				
			}
			if (valueDate1 == null && valueDate2 != null)
			{
				return -1;
			}
			if (valueDate1 == null && valueDate2 == null)
			{
				return 0;
			}
			// now valueDate1 is not null
			if (valueDate2 == null)
			{
				return 1;
			}

			if (valueDate1.isEarlierThanOrEqualTo(valueDate2))
			{
				return -1;
			}
			return 1;
		}
		
		
	}
	
	class ExternalCreditCheckConfig
	{
		// The following data structure maps the external credit config for each allocation to the credit hub configured for the respective parties.
		// For example, if there are five allocations (child trades) in a request, and say for two of the allocations, 
		// Traiana is the configured external credit hub and Markit for the rest three, then the map looks like
		// TraianaPing-> <List of two CptyCreditCheckConfig objects>
		// Markit-> <List of three CptyCreditCheckConfig objects>
		// A similar CptyCreditCheckConfig object is also created for the LP side credit check and added to this map.		
		private Map<String, List<CptyCreditCheckConfig>> externalCreditHub2fiCreditCheckConfig = new HashMap<String, List<CptyCreditCheckConfig>>();
		private String maker;
		
		
		public String getMaker() {
			return maker;
		}

		public void setMaker(String maker) {
			this.maker = maker;
		}

		public ExternalCreditCheckConfig(Request request, Organization externalcreditProvOrg, boolean sef, OrderStatusNotificationType notificationType)
		{
			Trade trade = request.getTrade();
			if (sef)
			{
				//Iterate through all the tradeLegs in the portfolio request and categorize them according to 
				//the creditHub each leg is supposed to do credit check with.
				if(trade.isSEF())
				{							
					if(request.isAllocation())
					{								
						NettingPortfolio portfolio = PortfolioServiceCache.getInstance().retrieve(request.getPortfolioRefId());
						
						StringBuilder sb = new StringBuilder();
						for ( NettingTradeRequest rp : portfolio.getInputRequestPojos() )
						{
							TradeRequestLeg tradeRequestLeg = rp.getTradeLegs( ISCommonConstants.SINGLE_LEG);
							if(tradeRequestLeg == null)
							{
								tradeRequestLeg = rp.getTradeLegs( ISCommonConstants.NEAR_LEG);
							}			
							sb.append(tradeRequestLeg.getUSI()!=null?tradeRequestLeg.getUSI():"").append(COMMA_SEPARATOR).append(TILDE_SEPARATOR);

							LegalEntity lpLe = CounterpartyUtilC.getLegalEntity( trade.getCounterpartyB());
				            TradingParty tp = CounterpartyUtilC.getTradingParty( lpLe, rp.getFund().getOrganization() );
			                Organization pbOrg = tp.getPrimeBrokerOrganization();
			                TradingParty pbTp = tp.getPrimeBrokerTradingParty();
			                LegalEntity fundLe = CounterpartyUtilC.getLegalEntity(rp.getFund());
			                Organization ch = CounterpartyUtilC.getClearingHouse( fundLe, lpLe);
			                LegalEntity chLe = (ch != null) ? ch.getDefaultDealingEntity():null;
			                String chLei = (chLe != null) ? chLe.getLEI():null;
			                if ( pbOrg != null && pbTp != null)
			                {
			                	String creditHub = getExternalCreditLimitProvider(pbTp, rp.getFund(), trade);
			                	if(!StringUtilC.isNullOrEmpty(creditHub))
			                	{
			                		String fundId = getLimitHubExtSysId(fundLe, pbOrg);
			                		ExternalSystemId fcmExtId = pbTp.getLegalEntity().getExternalSystemId(TradeServiceConstants.TRAIANA_LH_ACCOUNT_ID);
			                		String fcmId = fcmExtId!=null?fcmExtId.getSystemId():pbTp.getLEI();
				                	CptyCreditCheckConfig cptyCreditCheckConfig = new CptyCreditCheckConfig(fundId, 
				                			fcmId, tradeRequestLeg.getBaseCurrency().getShortName(), tradeRequestLeg.getBaseAmount(), 
				                			creditHub, tradeRequestLeg.getUSI(), tradeRequestLeg.getUSINamespace(), chLei, 
				                			trade.getFund2ExternalCreditCheckId(tradeRequestLeg.getUSI()), true, true);
		
				                	addCptyCreditCheckConfig(creditHub, cptyCreditCheckConfig);
			                	}
			                }					
						}
					}
					else
					{
						addTradingPartyExternalCreditConfig(trade, trade.getCounterpartyC(), trade.getCounterpartyA(), trade.getClearingHouse(), false);					
					}
					addTradingPartyExternalCreditConfig(trade, trade.getCounterpartyD(), trade.getCounterpartyB(), trade.getClearingHouse(), true);
				}
			}
			else
			{
				LegalEntity requestorLE = ( ( TradingParty ) request.getCounterparty() ).getLegalEntity();	
				LegalEntity providerLE = null;
				if (externalcreditProvOrg != null)
				{
					this.maker = externalcreditProvOrg.getShortName();
					providerLE = CounterpartyUtilC.getCounterpartyB(requestorLE, externalcreditProvOrg);
				}
				else
				{
					this.maker = trade.getCounterpartyB().getOrganization().getShortName();
					providerLE = CounterpartyUtilC.getLegalEntity( trade.getCounterpartyB());
				}	 
				// get all credit entities between two legal entities
				List<CreditEntity> creditEntities = CreditUtilC.getAllCreditEntitiesBetween(requestorLE, providerLE);
				if (creditEntities == null || creditEntities.size() == 0)
				{
					return;
				}
				for (CreditEntity ce : creditEntities)
				{			
					addTradingPartyExternalCreditConfig(trade, ce, notificationType);
				}
			}
			  /*
	            Organization org1 = le1.getOrganization();
	            Organization org2 = le2.getOrganization();
	            TradingParty org1TpForLe2 = le2.getTradingParty( org1 );
	            TradingParty org2TpForLe1 = le1.getTradingParty( org2 );
	            if ( org1TpForLe2 == null || org2TpForLe1 == null )
	            {
	                return ;
	            }

	            TradingParty org1PbTp = CreditUtilC.getCreditPrimeBrokerTradingparty( org1TpForLe2 );
	            TradingParty org2PbTp = CreditUtilC.getCreditPrimeBrokerTradingparty( org2TpForLe1 );

	            boolean isLe1PrimeBrokerCreditEnabled = org1PbTp != null;
	            boolean isLe2PrimeBrokerCreditEnabled = org2PbTp != null;
	            boolean isBothPrimeBrokerCreditEnabled = isLe1PrimeBrokerCreditEnabled && isLe2PrimeBrokerCreditEnabled;

	            if ( isLe1PrimeBrokerCreditEnabled || isLe2PrimeBrokerCreditEnabled )
	            {
	            	 if ( isBothPrimeBrokerCreditEnabled )
	                 {
	            		 // le1 & org1PbTp.getLegalEntity()
	            		// le2, org2PbTp.getLegalEntity()
	            		 addTradingPartyExternalCreditConfig(trade, le1, org1PbTp.getLegalEntity(), true);
	            		 addTradingPartyExternalCreditConfig(trade, le2, org2PbTp.getLegalEntity(), false);
	                 }
	            	 else if ( isLe1PrimeBrokerCreditEnabled )
	                 {
	            		 // le1 & org1PbTp.getLegalEntity()
	            		 addTradingPartyExternalCreditConfig(trade, le1, org1PbTp.getLegalEntity(), true);
	                 }
	            	 else
	                 {
	            		// le2, org2PbTp.getLegalEntity()
	            		 addTradingPartyExternalCreditConfig(trade, le2, org2PbTp.getLegalEntity(), false);
	                 }	            	
	            }
	            else
	            {
	            	// le1 &le2
	            	addTradingPartyExternalCreditConfig(trade, le1, le2, true);
	            }
			}
			*/

		}
		
		private String getLimitHubExtSysId(LegalEntity fundLe, Organization fcmOrg)
		{
			TradingParty fundTPInPBOrg = CounterpartyUtilC.getTradingParty( fundLe, fcmOrg);
			ExternalSystemId accountIdExtSys = fundTPInPBOrg.getExternalSystemId(TradeServiceConstants.TRAIANA_LH_ACCOUNT_ID);			
			String accountId = accountIdExtSys!=null?accountIdExtSys.getSystemId():fundLe.getLEI();			
			return accountId;
		}

		private void addTradingPartyExternalCreditConfig(Trade trade, Counterparty cmCpty, Counterparty tradingParty, Organization chOrg, boolean isLP) {
			String fiCreditHub = null;
			if(cmCpty != null)
			{
				fiCreditHub = getExternalCreditLimitProvider(cmCpty, tradingParty, trade);			
			}	
			if(!StringUtilC.isNullOrEmpty(fiCreditHub))
			{
				LegalEntity chLe = (chOrg!=null)?chOrg.getDefaultDealingEntity():null;
                String chLei = (chLe != null) ? chLe.getLEI():null;
				for(Object leg:trade.getTradeLegs())
				{
					FXLeg fxLeg = (FXLeg)leg;
					FXPaymentParameters fxPaymentParameters = fxLeg.getFXPayment();
					

					TradingParty cmTp = (TradingParty)cmCpty;
            		String fundId = getLimitHubExtSysId(CounterpartyUtilC.getLegalEntity(tradingParty), cmTp.getLegalEntityOrganization());
            		ExternalSystemId fcmExtId = cmTp.getLegalEntity().getExternalSystemId(TradeServiceConstants.TRAIANA_LH_ACCOUNT_ID);
            		String fcmId = fcmExtId!=null?fcmExtId.getSystemId():cmCpty.getLEI();
            		String ackId = getCreditLimitHubAckId(trade.getCreditLimitHubId(), fundId);
            		
					CptyCreditCheckConfig cptyCreditCheckConfig = new CptyCreditCheckConfig(fundId, fcmId,  
							fxPaymentParameters.getCurrency1().getShortName(), fxPaymentParameters.getCurrency1Amount(), 
							fiCreditHub, fxLeg.getUSI(), fxLeg.getUSINamespace(), chLei, ackId, false, !isLP);
					addCptyCreditCheckConfig(fiCreditHub, cptyCreditCheckConfig);
				}				
			}
		}
		
		private void addTradingPartyExternalCreditConfig(Trade trade,  CreditEntity ce, OrderStatusNotificationType notificationType) {

			LegalEntity creditTakerLE = ce.getLegalEntity();
			Organization creditProviderOrg = ce.getOrganization();
			TradingParty creditTakerOrgTP = ce.getTradingParty();
			TradingParty creditProviderOrgTP = creditTakerLE.getTradingParty(creditProviderOrg);
			boolean isLP = ce.isMaker();
			String creditProvider = null;
			boolean isExternalCreditEnabled = false;
			if(creditTakerOrgTP != null)
			{
				CreditLimitAdminService creditLimitAdminSvc = CreditLimitAdminServiceFactory.getCreditLimitAdminService();		
				creditProvider =  creditLimitAdminSvc.getExternalCreditLimitProvider(creditProviderOrg, creditProviderOrgTP);
				isExternalCreditEnabled = creditLimitAdminSvc.isExternalCreditEnabled(creditProviderOrg, creditProviderOrgTP);
			}
			StringBuilder sb = new StringBuilder();
			sb.append("ECU.addTradingPartyExternalCreditConfig:Checking credit line for External Credit:");
			sb.append(ce);
			sb.append(":TransactionID:");
			sb.append(trade.getTransactionID());
			sb.append(":External Credit provider:");
			sb.append(creditProvider);
			log.info(sb.toString());
			String externalCreditProvider = CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean().
					getExternalCreditProviderName(creditProviderOrg);
			Request request = trade.getRequest();
			if(isExternalCreditEnabled && externalCreditProvider != null ) {
				if (request.getRequestAttributes().isMakerCreditOverride()) {
					log.info("Skipping external credit check for manual RFQ credit override: :TransactionID: " + trade.getTransactionID());
					if (request.getRequestAttributes().isExternalCreditFailed() && OrderStatusNotificationType.Completed == notificationType) {
						String fromAddress = CreditUtilC.getSenderEmailAddress(creditProviderOrg);
						Collection<String> toAddress = CreditUtilC.getNotificationEmailAddress(creditProviderOrg, creditProviderOrgTP);
						ISEmailUtil.getInstance().sendExternalCreditOverrideEmail(trade, ce, fromAddress, toAddress);
					}
				} else {

					LegalEntity fundLE = (isLP ? creditTakerOrgTP.getLegalEntity() : creditTakerLE);

					LegalEntity fcmLE = (isLP ? creditTakerLE : creditTakerOrgTP.getLegalEntity());
					Tuple<String, String> settlementCodeTuple = getSettlementCode(fundLE, fcmLE);
					String fundId = settlementCodeTuple.first;
					String fcmId = settlementCodeTuple.second;
					sb = new StringBuilder();
					sb.append("ECU.addTradingPartyExternalCreditConfig:Credit line is eligible for external credit check:");
					sb.append(ce);
					sb.append(":TransactionID:");
					sb.append(trade.getTransactionID());
					sb.append(":External Credit provider:");
					sb.append(externalCreditProvider);
					sb.append("Taker Settlement Code:");
					sb.append(fundId);
					sb.append("Maker Settlement Code:");
					sb.append(fcmId);

					log.info(sb.toString());

					String transactionId = trade.getTransactionID();
					String creditTakerNameSpace = creditTakerLE.getOrganization().getName();
					String creditproviderNamespace = creditProviderOrg.getName();
					String takerCorrelationId = new StringBuilder().
							append(transactionId).append("_").append(creditTakerNameSpace).append("_").append(creditproviderNamespace).append("_1").toString();
					String makerCorrelationId = new StringBuilder().
							append(transactionId).append("_").append(creditTakerNameSpace).append("_").append(creditproviderNamespace).append("_2").toString();
					CptyCreditCheckConfig cptyCreditCheckConfig = new CptyCreditCheckConfig(fundId, fcmId, null, 0, creditProvider, transactionId,
							null, null, takerCorrelationId, false, !isLP, creditproviderNamespace);

					cptyCreditCheckConfig.setTakerCorrelationId(takerCorrelationId);
					cptyCreditCheckConfig.setMakerCorrelationId(makerCorrelationId);
					if (fundLE.getLEI() != null) {
						cptyCreditCheckConfig.setTakerLEI(fundLE.getLEI());
					} else {
						cptyCreditCheckConfig.setTakerLEI(fundLE.getOrganization().getLEI());
					}
					if (fcmLE.getLEI() != null) {
						cptyCreditCheckConfig.setMakerLEI(fcmLE.getLEI());
					} else {
						cptyCreditCheckConfig.setMakerLEI(fcmLE.getOrganization().getLEI());
					}
					addCptyCreditCheckConfig(externalCreditProvider, cptyCreditCheckConfig);
				}
			}
		}
		
		private Tuple<String, String> getSettlementCode(LegalEntity takerLE, LegalEntity makerLE)
		{
			// get trading party from maker to taker LE
			TradingParty takerTradingParty = takerLE.getTradingParty(makerLE.getOrganization());
			String takerSettlementCode = getSettlementCode(takerTradingParty, takerLE);
			
			TradingParty makerTradingParty = makerLE.getTradingParty(takerLE.getOrganization());
			String makerSettlementCode = getSettlementCode(makerTradingParty, makerLE);
			
			return new Tuple<String, String>(takerSettlementCode, makerSettlementCode);
					
		}
		
		private String getSettlementCode(TradingParty tp, LegalEntity le)
		{
			ExternalSystemId extSysID = tp.getExternalSystemId(SETTLEMENT_CODE);			
			String settlementCode = getSystemId(extSysID);
			if (settlementCode == null)
			{
				// check it on LE
				extSysID = le.getExternalSystemId(SETTLEMENT_CODE);				
				settlementCode = getSystemId(extSysID);
			}
			// check it on org
			if (settlementCode == null)
			{
				extSysID = le.getOrganization().getExternalSystemId(SETTLEMENT_CODE);				
				settlementCode = getSystemId(extSysID);
			}
			if (settlementCode == null)
			{
				// return LE short name
				return le.getShortName();
			}
	      
			return settlementCode;
		}
		
		private String getSystemId(final ExternalSystemId extSysID)
		{
			if (extSysID == null)
			{
				return null;
			}
			String extSystemIdStr = extSysID.getSystemId();
			if(extSystemIdStr != null)
			{
				extSystemIdStr = extSystemIdStr.trim();
				if (extSystemIdStr.length() != 0)
				{
					return extSystemIdStr;
				}
			}
			return null;
		}

		private String getCreditLimitHubAckId(String compositeAckId, String lei)
		{
			if(StringUtilC.isNullOrEmpty(compositeAckId)){
				return null;
			}
			String[] tokens = compositeAckId.split(";");
			for(String token:tokens)
			{
				String[] ackIdTokens = token.split("=");
				if(ackIdTokens[0].equals(lei))
				{
					return ackIdTokens[1];
				}
			}
			return null;
		}
		
		private void addCptyCreditCheckConfig(String fiCreditHub, CptyCreditCheckConfig cptyCreditCheckConfig) 
		{
			List<CptyCreditCheckConfig> fiCreditCheckConfigList = externalCreditHub2fiCreditCheckConfig.get(fiCreditHub);
			if(fiCreditCheckConfigList == null)
			{
				fiCreditCheckConfigList = new ArrayList<CptyCreditCheckConfig>();
				externalCreditHub2fiCreditCheckConfig.put(fiCreditHub, fiCreditCheckConfigList);
			}
			fiCreditCheckConfigList.add(cptyCreditCheckConfig);
		}
		
		private boolean isCreditCheckRequired()
		{
			return externalCreditHub2fiCreditCheckConfig.size()>0;
		}
		
		private int getNoOfCreditCheckRequests()
		{
			int count=0;
			for(Entry<String, List<CptyCreditCheckConfig>> entry :externalCreditHub2fiCreditCheckConfig.entrySet())
			{
				count+=entry.getValue().size();
			}
			return count;
		}
		
		private Map<String, List<CptyCreditCheckConfig>> getExternalCreditCheckConfigMap()
		{
			return externalCreditHub2fiCreditCheckConfig;
		}
	}

	class CptyCreditCheckConfig
	{
		private String fund; //ex: FI1
		private String fcm; //ex: CM1
		private String ch; //ex: CME or LCH
		private String currency;
		private double amount;
		private String externalCreditHub; //ex: Traiana-ping
		private String usi;
		private String usiNamespace;
		private String creditCheckAckId;
		private boolean isChildTrade;
		private boolean isFI;
		private String extCrediProviderNameSpace;
		private String takerCorrelationId;
		private String makerCorrelationId;
		private String takerLEI;
		private String makerLEI;
		
		public String getTakerLEI() {
			return takerLEI;
		}
		public void setTakerLEI(String takerLEI) {
			this.takerLEI = takerLEI;
		}
		public String getMakerLEI() {
			return makerLEI;
		}
		public void setMakerLEI(String makerLEI) {
			this.makerLEI = makerLEI;
		}
		public String getFund() {
			return fund;
		}
		public String getFcm() {
			return fcm;
		}
		public String getCurrency() {
			return currency;
		}
		public double getAmount() {
			return amount;
		}
		public String getExternalCreditHub() {
			return externalCreditHub;
		}		
		public String getUSI(){
			return usi;
		}
		public String getUSINamespace(){
			return usiNamespace;
		}
		public String getCH(){
			return ch;
		}		
		public String getCreditCheckAckId(){
			return creditCheckAckId;
		}
		public boolean isChildTrade(){
			return isChildTrade;
		}		
		public boolean isFI(){
			return isFI;
		}
		
		public String getExtCrediProviderNameSpace() {
			return extCrediProviderNameSpace;
		}
		
		
		public String getTakerCorrelationId() {
			return takerCorrelationId;
		}
		public void setTakerCorrelationId(String takerCorrelationId) {
			this.takerCorrelationId = takerCorrelationId;
		}
		public String getMakerCorrelationId() {
			return makerCorrelationId;
		}
		public void setMakerCorrelationId(String makerCorrelationId) {
			this.makerCorrelationId = makerCorrelationId;
		}
		public CptyCreditCheckConfig(String fiFund, String fcm,
				String currency, double amount, String externalCreditHub, 
				String usi, String usiNamespace, String ch, String ackId, boolean isChildTrade, boolean isFI) 
		{
			this.fund = fiFund;
			this.fcm = fcm;
			this.ch = ch;
			this.currency = currency;
			this.amount = amount;
			this.externalCreditHub = externalCreditHub;
			this.usi = usi;
			this.usiNamespace = usiNamespace;
			this.creditCheckAckId = ackId;
			this.isChildTrade = isChildTrade;
			this.isFI = isFI;			
		}
		
		public CptyCreditCheckConfig(String fiFund, String fcm,
				String currency, double amount, String externalCreditHub, 
				String usi, String usiNamespace, String ch, String ackId, boolean isChildTrade, boolean isFI, String extCrediProviderNameSpace) 
		{
			this.fund = fiFund;
			this.fcm = fcm;
			this.ch = ch;
			this.currency = currency;
			this.amount = amount;
			this.externalCreditHub = externalCreditHub;
			this.usi = usi;
			this.usiNamespace = usiNamespace;
			this.creditCheckAckId = ackId;
			this.isChildTrade = isChildTrade;
			this.isFI = isFI;
			this.extCrediProviderNameSpace = extCrediProviderNameSpace;
		}
	}
	
	public enum OrderStatusNotificationType
	{
		Cancelled,
		Completed,
		Updated
	}
	
	public enum CreditLimitHubStatus
	{
		INITIAL,
		Approved,
		Rejected,
		Timedout
	}
}
