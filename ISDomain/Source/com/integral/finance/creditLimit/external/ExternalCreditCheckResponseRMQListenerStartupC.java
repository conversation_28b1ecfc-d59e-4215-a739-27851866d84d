package com.integral.finance.creditLimit.external;

import java.util.Hashtable;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.util.MessagingUtil;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.system.runtime.StartupTask;

public class ExternalCreditCheckResponseRMQListenerStartupC implements StartupTask 
{
    private final Log log = LogFactory.getLog(this.getClass());

    public String startup(String aName, @SuppressWarnings("rawtypes") Hashtable args) throws Exception 
    {
        StringBuilder result = new StringBuilder(200);
        result.append("ExternalCreditResponseRMQListenerStartupC.startup: Attempting to start ExternalCreditResponseRMQListener - ");
        try
        {     
        	ExternalCreditMBean externalCreditMBean = ExternalCreditMBeanC.getInstance();
        	String receivingQueue = externalCreditMBean.getReceivingQueue();
        	String bindingKey = externalCreditMBean.getBindingKey();
        	if (receivingQueue == null || bindingKey == null)
        	{
        		result.append("ExternalCreditResponseRMQListenerStartupC.startup: could not start listener, reason: invalid parameters : receivingQueue: ");
        		result.append(receivingQueue);
        		result.append(" :bindingKey: ");
        		result.append(bindingKey);        		
        		return result.toString();
        	}
        	boolean durableQueue = externalCreditMBean.isReceivingQueueDurable();
        	String vsName = ConfigurationFactory.getServerMBean().getVirtualServerName();
        	bindingKey = bindingKey + ":" + vsName;
        	receivingQueue = receivingQueue + ":" + vsName;
           	ExternalCreditCheckResponseRMQListener listener = new ExternalCreditCheckResponseRMQListener();
        	if (durableQueue)
        	{
        		MessagingUtil.setupDurableMessageListener(listener, receivingQueue, ServerRuntimeMBean.STP_EXCHANGE, bindingKey);
        	}
        	else
        	{
        		MessagingUtil.setupMessageListener(listener, receivingQueue, ServerRuntimeMBean.STP_EXCHANGE, bindingKey);
        	}
        	
            result.append("ExternalCreditResponseRMQListenerStartupC.startup:listener started for vsName = ").append(vsName);
            result.append(":receivingQueue:");
            result.append(receivingQueue);
            result.append(":bindingKey:");
            result.append(bindingKey);
            log.info(result.toString());
        }
        catch ( Exception e )
        {
            result.append("ExternalCreditResponseRMQListenerStartupC.startup:Problem with starting RMQ Listener.Reason:");
            result.append(e.getMessage());
            log.error( result.toString(), e );
            throw e;
        }

        return result.toString();
    }
}
