package com.integral.finance.creditLimit.startup;

// Copyright (c) 2015 Integral Development Corporation.  All Rights Reserved.

import java.util.Collection;
import java.util.HashSet;

import com.integral.config.util.ConfigUtil;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.creditLimit.CreditNotificationManagerC;
import com.integral.finance.creditLimit.CreditUtilizationCacheFetchFromSubscriptionsC;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.is.ISCommonConstants;
import com.integral.is.credit.CreditLimitUpdateListenerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.property.SystemPropertyC;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.system.runtime.StartupTask;
import com.integral.system.server.VirtualServer;
import com.integral.system.server.VirtualServerType;
import com.integral.user.Organization;

/**
 * This startup task is used for tasks associated with the credit workflow execution in notification servers.
 *
 * <AUTHOR> Development Corp.
 */
public class NSCreditLimitStartupC extends CreditLimitStartupC implements StartupTask
{
    private static final Log log = LogFactory.getLog( NSCreditLimitStartupC.class );

    public String startup( String aName, java.util.Hashtable args ) throws Exception
    {
        try
        {
            if ( VirtualServerType.NotificationServer.equals( ConfigurationFactory.getServerMBean().getVirtualServerType() ) )
            {
                log.info( "NSCLS.startup : starting the notification server specific credit limit startup. " );

                // call the general credit startup class.
                super.startup( aName, args );

                // start the credit limit updates listener.
                CreditLimitUpdateListenerC.enableCreditUpdateListener();

                // do the warm-up of all credit relationships in OCX servers.
                Collection<Organization> ocxTakers = getQuickCreditCheckOrOCXEnabledTakerOrgs();
                log.info ( "NSCLS.startup : taker orgs for subscription=" + ocxTakers.size () );
                for ( Organization taker : ocxTakers )
                {
                    Collection<Organization> makers = taker.getRelatedOrganizations( ISCommonConstants.FI_ORG_RELATIONSHIP );
                    if ( makers != null )
                    {
                        for ( Organization maker : makers )
                        {
                            if ( !maker.isActive () )
                            {
                                continue;
                            }
                            LegalEntity takerLe = taker.getDefaultDealingEntity();
                            LegalEntity makerLe = maker.getDefaultDealingEntity();
                            if ( CounterpartyUtilC.isValidRelationShip( takerLe, makerLe ) )
                            {
                                CreditUtilizationManagerC.getInstance().subscribeBilateralCreditLimitUpdate( makerLe, takerLe, CurrencyFactory.getCurrency( "EUR" ), CurrencyFactory.getCurrency( "USD" ), true, true, null );
                            }
                        }
                    }
                }
                CreditNotificationManagerC.getInstance().getPooledExecutor().execute( new CreditUtilizationCacheFetchFromSubscriptionsC( CreditUtilizationCacheFetchFromSubscriptionsC.FETCH ) );
            }
        }
        catch ( Exception e )
        {
            log.error( "NSCLS.startup : Exception while starting up the credit startup in notification server.", e );
            throw e;
        }
        return "Notification Server Credit limit startup completed.";
    }

    private Collection<VirtualServer> getQuickCheckOrOCXEnabledDealingServers()
    {
        Collection<VirtualServer> quickCreditCheckEnabledorOCXServers = new HashSet<VirtualServer>();
        try
        {
            // ocx deployments
            Collection<SystemPropertyC> ocxEnabledProps = ConfigUtil.getSystemPropertiesWithName(ServerRuntimeMBean.IDC_IS_OCX_OA_DEPLOYMENT);
            for ( SystemPropertyC prop : ocxEnabledProps )
            {
                if ( prop.getVirtualServer() != null && !VirtualServer.GLOBAL_VIRTUAL_SERVER_NAME.equals( prop.getVirtualServer().getShortName() ) )
                {
                    if ( Boolean.TRUE.toString ().equalsIgnoreCase ( (String) prop.getValue () ) )
                    {
                        quickCreditCheckEnabledorOCXServers.add ( prop.getVirtualServer () );
                    }
                }
            }

            log.info ( "NSCLS.startup - ocxEnabled servers count=" + quickCreditCheckEnabledorOCXServers );

            // quick check credit enabled
            Collection<VirtualServer> quickCreditCheckEnabledServers = new HashSet<VirtualServer> ();
            Collection<SystemPropertyC> quickCreditCheckEnabledProps = ConfigUtil.getSystemPropertiesWithName( ServerRuntimeMBean.IDC_QUICK_CREDIT_CHECK_ENABLED );
            for ( SystemPropertyC prop : quickCreditCheckEnabledProps )
            {
                if ( prop.getVirtualServer() != null && !VirtualServer.GLOBAL_VIRTUAL_SERVER_NAME.equals( prop.getVirtualServer().getShortName() ) )
                {
                    if ( Boolean.TRUE.toString ().equalsIgnoreCase ( (String) prop.getValue () ) )
                    {
                        quickCreditCheckEnabledorOCXServers.add ( prop.getVirtualServer () );
                        quickCreditCheckEnabledServers.add ( prop.getVirtualServer () );
                    }
                }
            }
            log.info ( "NSCLS.startup - quick credit check enabled servers count=" + quickCreditCheckEnabledServers );
        }
        catch ( Exception e )
        {
            log.error( "NSCLS.getQuickCheckOrOCXEnabledDealingServers : Exception while retrieving the OCX enabled or quick credit check enabled servers.", e );
        }
        return quickCreditCheckEnabledorOCXServers;
    }

    private Collection<Organization> getQuickCreditCheckOrOCXEnabledTakerOrgs()
    {
        Collection<Organization> takerOrgs = new HashSet<Organization>();
        Collection<VirtualServer> ocxServers = getQuickCheckOrOCXEnabledDealingServers();
        for ( VirtualServer vs : ocxServers )
        {
            takerOrgs.addAll( vs.getActiveOrganizations () );
        }
        return takerOrgs;
    }
}
