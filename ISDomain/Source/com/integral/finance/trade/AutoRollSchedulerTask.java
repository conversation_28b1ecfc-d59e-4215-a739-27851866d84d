package com.integral.finance.trade;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringTokenizer;

import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.user.User;
import com.integral.util.MathUtilC;
import org.apache.commons.lang.StringUtils;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import com.integral.alert.AlertLoggerFactory;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.instrument.InstrumentClassification;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.scheduler.ScheduleFunctor;
import com.integral.scheduler.ScheduleFunctorC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.time.IdcDate;
import com.integral.trade.manual.ManualTradeService;
import com.integral.user.Organization;
import com.integral.user.UserFactory;

/**
 * This scheduler task is the replacement of autoRoll.jsp which used to be executed using a cron job.
 * this task would be used by brokers to auto roll their positions.
 */
public class AutoRollSchedulerTask extends ScheduleFunctorC implements ScheduleFunctor
{

    String makerOrg;
    String takerOrg = "Takers";
    String makerUser;
    String stpFlag = "F";
    String useMid = "N";
    String noOfDays = "1";
    String usePositionRate = "T";
    String mdsUsed;
    private String assetClasses;

    private static final char SIGNATURE_DELIMITER = '~';

    public String getAssetClasses() {
		return assetClasses;
	}

	public void setAssetClasses(String assetClasses) {
		this.assetClasses = assetClasses;
	}

	protected static Log log = LogFactory.getLog( AutoRollSchedulerTask.class );
    List<String> MISReturnedItems = new ArrayList<String>( 16 );

    public List<String> getFunctorProperties()
    {
        List<String> functorProperties = super.getFunctorProperties();
        functorProperties.add( "makerOrg" );
        functorProperties.add( "takerOrg" );
        functorProperties.add( "makerUser" );
        functorProperties.add( "stpFlag" );
        functorProperties.add( "useMid" );
        functorProperties.add( "noOfDays" );
        functorProperties.add( "usePositionRate" );
        functorProperties.add( "mdsUsed" );
        functorProperties.add("assetClasses");
        return functorProperties;
    }

    @Override
    public String getDescription()
    {
        return "Functor to roll the positions at end of day for the brokers";
    }

    public String validate()
    {
        String superValidate = super.validate();
        if ( !SUCCESS.equals( superValidate ) )
        {
            return superValidate;
        }

        if ( makerOrg == null || makerOrg.trim().length() == 0 )
        {
            return "makerOrg not specified";
        }
        if ( !this.stpFlag.equalsIgnoreCase( "T" ) && !this.stpFlag.equalsIgnoreCase( "F" ) )
        {
            return "STP Flag can be null or T/F";
        }
        if ( !this.useMid.equalsIgnoreCase( "Y" ) && !this.useMid.equalsIgnoreCase( "N" ) )
        {
            return "useMid Flag can be null or T/F";
        }

        if ( !this.usePositionRate.equalsIgnoreCase( "T" ) && !this.usePositionRate.equalsIgnoreCase( "F" ) )
        {
            return "usePositionRate Flag can be null or T/F";
        }
        if ( StringUtils.isNotEmpty(assetClasses) && !assetClasses.equals("ALL"))
        {
			if (assetClasses.length() > InstrumentClassification.INDEX_CLSF.length()
					+ InstrumentClassification.METAL_CLSF.length() && assetClasses.indexOf(',') == -1) {
				return "Asset Class Separator should be ','";
			}
        	String assetClassesValues[] = assetClasses.split(",");
        	for(String assetClass : assetClassesValues){
				if (!assetClass.equals(InstrumentClassification.CURRENCY_CLSF)
						&& !assetClass.equals(InstrumentClassification.INDEX_CLSF)
						&& !assetClass.equals(InstrumentClassification.METAL_CLSF)
						&& !assetClass.equals(InstrumentClassification.ENERGY_CLSF)
						&& !assetClass.equals(InstrumentClassification.CRYPTO_CLSF ) )
				{
					return "Asset Class should be among (CURRENCY, INDEX, METAL, ENERGY, CRYPTO and ALL)";
				}
        	}
        }
        return SUCCESS;
    }

	private String getCurrencyPairAssetType(Currency baseCcy, Currency varCcy) {
		if (baseCcy.getInstrumentClassification().getShortName().equals(InstrumentClassification.METAL_CLSF)
				|| varCcy.getInstrumentClassification().getShortName().equals(InstrumentClassification.METAL_CLSF)) {
			return InstrumentClassification.METAL_CLSF;
		}
		if (baseCcy.getInstrumentClassification().getShortName().equals(InstrumentClassification.INDEX_CLSF)
				|| varCcy.getInstrumentClassification().getShortName().equals(InstrumentClassification.INDEX_CLSF)) {
			return InstrumentClassification.INDEX_CLSF;
		}
        if (baseCcy.getInstrumentClassification().getShortName().equals(InstrumentClassification.ENERGY_CLSF)
                || varCcy.getInstrumentClassification().getShortName().equals(InstrumentClassification.ENERGY_CLSF)) {
            return InstrumentClassification.ENERGY_CLSF;
        }
        if (baseCcy.getInstrumentClassification().getShortName().equals(InstrumentClassification.CRYPTO_CLSF)
                || varCcy.getInstrumentClassification().getShortName().equals(InstrumentClassification.CRYPTO_CLSF)) {
            return InstrumentClassification.CRYPTO_CLSF;
        }
		return InstrumentClassification.CURRENCY_CLSF;
	}
    
    public void execute( WorkflowMessage msg )
    {
        try
        {
            FXMarketDataSet mds = null;
            if ( makerUser == null || makerUser.trim().length() == 0 )
            {
                Organization aMakerOrg = ReferenceDataCacheC.getInstance().getOrganization( makerOrg );
                if ( aMakerOrg == null )
                {
                    String logStr = "AutoRollSchedulerTask:: No maker org found with name=" + makerOrg;
                    log.error( logStr );
                    AlertLoggerFactory.getMessageLogger().log(ISAlertMBean.AUTOROLL_SCHEDULER_TASK_EXECUTE, this.getClass().getName(), logStr, msg != null ? msg.getEventName () : "" );
                    return;
                }
                else
                {
                    User makerOrgDefaultUser = aMakerOrg.getDefaultDealingUser ();
                    if ( makerOrgDefaultUser == null  )
                    {
                        String logStr = "AutoRollSchedulerTask:: default dealing user not set for makerOrg=" + makerOrg;
                        log.error( logStr );
                        AlertLoggerFactory.getMessageLogger().log(ISAlertMBean.AUTOROLL_SCHEDULER_TASK_EXECUTE, this.getClass().getName(), logStr, msg != null ? msg.getEventName () : "" );
                        return;
                    }
                }
                this.makerUser = ReferenceDataCacheC.getInstance().getOrganization( makerOrg ).getDefaultDealingUser().getShortName();
            }

            if ( usePositionRate.equalsIgnoreCase( "F" ) )
            {
                if ( this.mdsUsed == null || this.mdsUsed.trim().length() == 0 )
                {
                    this.mdsUsed = "EODMDS";
                }
                Session sessionTopLink = PersistenceFactory.newSession();
                ExpressionBuilder eb = new ExpressionBuilder();
                Expression expr = eb.get( FXMarketDataSet.ShortName ).equal( mdsUsed );
                if ( mdsUsed.equals( "EODMDS" ) )
                {
                    IdcDate currentTradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
                    IdcDate previousDate = currentTradeDate.subtractDays( 1 );
                    expr = expr.and( eb.get( "baseDate" ).equal( previousDate.asSqlDate() ) );
                }
                mds = ( FXMarketDataSet ) sessionTopLink.readObject( FXMarketDataSet.class, expr );
                if ( mds == null )
                {
                    log.info( "AutoRollSchedulerTask:: Main static MDS used mdsUsed is  " + mdsUsed );
                    mds = ReferenceDataCacheC.getInstance().getMainStaticMds();
                }
            }
            if ( takerOrg == null || takerOrg.trim().length() == 0 )
            {
                takerOrg = "Takers";
            }
            if ( StringUtils.isEmpty(assetClasses))
            {
            	assetClasses = "ALL";
            }
            log.info( MessageFormat.format( "AutoRollSchedulerTask:: MDS USED is {0}, makerOrg is {1}, takerOrg is {2}, makerUser is {3}, stpFlag is {4}, useMid={5}, noOfDays={6},usePositionRate={7}", mds, makerOrg, takerOrg, makerUser, stpFlag, useMid, noOfDays, usePositionRate ) );
            String fqMakerUser = makerUser + "@" + makerOrg;
            if ( UserFactory.getUser( fqMakerUser ) == null )
            {
                log.warn( "AutoRollSchedulerTask::Maker user " + fqMakerUser + " not valid" );
                throw new IllegalArgumentException( "Maker user " + fqMakerUser + " not valid" );
            }
            IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext( fqMakerUser );
            IdcSessionManager.getInstance().setSessionContext( ctx );

            IdcDate currentTradeDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            ManualTradeService manualTradeService = new ManualTradeService();

            Map<String, String> positionSignatureMap = new HashMap<String, String> (  );
            for ( int i = 1; i <= Integer.parseInt( noOfDays ); i++ )
            {

                IdcDate fromDate = currentTradeDate.subtractDays( i );
                String makerOrgForMIS = makerOrg;
                Organization makerOrgObject = ReferenceDataCacheC.getInstance().getOrganization( makerOrg );
                if ( makerOrgObject.getOrganizationGroup() != null && makerOrgObject.getOrganizationGroup().trim().length() > 0 )
                {
                    makerOrgForMIS = makerOrgObject.getOrganizationGroup();
                }

                String misUrl = getMISPortalURL( makerOrgForMIS, fromDate );
                log.info( "AutoRollSchedulerTask::  URL is " + misUrl );
                BufferedReader dataIS;
                try
                {
                    dataIS = new BufferedReader( new InputStreamReader( new URL( misUrl ).openStream() ) );
                }
                catch ( Exception e )
                {
                    StringBuilder sb = new StringBuilder(50)
                            .append("AutoRollSchedulerTask:: Connection to MIS Portal URL failed. url=")
                            .append(misUrl);
                    log.error( sb.toString(), e );
                    AlertLoggerFactory.getMessageLogger().log( ISAlertMBean.AUTOROLL_MIS_CONNECT, this.getClass().getName(), sb.toString(), e.getMessage());
                    return;
                }
                Collection<String> positions = extractPositions( dataIS );
                if ( positions == null || positions.isEmpty() )
                {
                    log.error( "AutoRollSchedulerTask:: No headers returned by MIS for URL " + misUrl );
                    MessageLogger.getInstance().log(ISAlertMBean.AUTOROLL_MIS_NO_HEADERS, "AutoRollSchedulerTask.execute", "No headers returned by MIS for URL " + misUrl, null);
                }
                int counter = -1;
                boolean positionsReturned = false;
                for ( String input : positions )
                {
                    try
                    {
                        counter++;

                        // clear the loader exceptions so that previous exceptions or failures will not influence the current one.
                        manualTradeService.getTradeLoadExceptions().clear();

                        if ( counter == 0 )
                        {
                            log.info( "AutoRollSchedulerTask::  header returned  is " + input );
                            continue;
                        }
                        log.info( "AutoRollSchedulerTask::  position # " + counter + " returned  is " + input );
                        if ( input.trim().length() == 0 )
                        {
                            continue;
                        }

                        StringTokenizer tok = new StringTokenizer( input, "," );
                        if ( !tok.hasMoreTokens() || tok.countTokens() < 5 )
                        {
                            continue;
                        }
                        positionsReturned = true;
                        String org = tok.nextToken();
                        if ( !org.equals( makerOrg ) )
                        {
                            log.info( "AutoRollSchedulerTask:: Dropping Position number " + counter + " as the org is not same as maker org for AutoRoll." );
                            continue;
                        }
                        String cp = tok.nextToken().trim();
                        CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(cp);
                        Currency baseCcy = null;
                        Currency varCcy = null;
                        if(currencyPair != null) {
                            baseCcy = currencyPair.getBaseCurrency();
                            varCcy = currencyPair.getVariableCurrency();
                        }
                        //Check asset class of baseCcy and varCcy and skip roll for ccpPair if it does not match with the specified list
						if (!assetClasses.equals("ALL")) {
							List<String> assetClassesList = Arrays.asList(assetClasses.split(","));
							if (!assetClassesList.contains(getCurrencyPairAssetType(baseCcy, varCcy))) {
								continue;
							}
						}
                        if ( baseCcy != null && varCcy != null )
                        {
                            FXRateBasis rb = CreditUtilC.getStdQuoteConvention().getFXRateBasis( baseCcy, varCcy );
                            if ( rb != null )
                            {
                                if ( rb.isNonDeliverable() )
                                {
                                    log.info( "AutoRollSchedulerTask.execute : Skipping non-deliverable currency pair. cp=" + cp );
                                    continue;
                                }
                            }
                            else
                            {
                                if ( !baseCcy.isDeliverable() || !varCcy.isDeliverable() )
                                {
                                    log.info( "AutoRollSchedulerTask.execute : Skipping non-deliverable currencies in the pair. cp=" + cp );
                                    continue;
                                }
                            }
                        }
                        String takerOrgLocal = tok.nextToken().trim();
                        Organization aTakerOrgLocal = ReferenceDataCacheC.getInstance ().getOrganization ( takerOrgLocal );
                        if ( aTakerOrgLocal == null )
                        {
                            String logStr = "AutoRollSchedulerTask:: No taker org found with name=" + takerOrgLocal;
                            log.error( logStr );
                            AlertLoggerFactory.getMessageLogger().log(ISAlertMBean.AUTOROLL_SCHEDULER_TASK_EXECUTE, this.getClass().getName(), logStr, msg != null ? msg.getEventName () : "" );
                            continue;
                        }
                        else
                        {
                            User takerOrgLocalDefaultUser = aTakerOrgLocal.getDefaultDealingUser ();
                            if ( takerOrgLocalDefaultUser == null  )
                            {
                                String logStr = "AutoRollSchedulerTask:: default dealing user not set for takerOrg=" + takerOrgLocal;
                                log.error( logStr );
                                AlertLoggerFactory.getMessageLogger().log(ISAlertMBean.AUTOROLL_SCHEDULER_TASK_EXECUTE, this.getClass().getName(), logStr, msg != null ? msg.getEventName () : "" );
                                continue;
                            }
                        }


                        String baseAmt = tok.nextToken().trim();
                        String termAmt = tok.nextToken().trim();
                        double absBaseAmt = Double.parseDouble( baseAmt.startsWith( "-" ) ? baseAmt.substring( 1 ) : baseAmt );
                        double absTermAmt = Double.parseDouble( termAmt.startsWith( "-" ) ? termAmt.substring( 1 ) : termAmt );

                        if ( absBaseAmt == 0 || absTermAmt == 0 ) // position has been squarred off
                        {
                            log.info( "AutoRollSchedulerTask:: Position number " + counter + " already settled" );
                            continue;
                        }
                        if ( i == 1 )
                        {
                            MISReturnedItems.add( cp + ',' + takerOrgLocal + ',' + baseAmt + ',' + termAmt );
                        }
                        double rate = Math.abs( Double.parseDouble( termAmt ) / Double.parseDouble( baseAmt ) );
                        if( baseCcy.isAnIndex()){
                            Double contractMultiplier = QuoteConventionUtilC.getInstance().getContractMultiplier(currencyPair);
                            if( contractMultiplier != null) {
                                rate = rate / contractMultiplier;
                                if (log.isDebugEnabled()) {
                                    log.debug("applied contract multiplier to rate for ccy: " + currencyPair.getName());
                                }
                            }
                        }
                        rate = MathUtilC.correctFloatingPointsCalculationPrecision(rate);


                        double offerRate = 0;
                        double bidRate = 0;
                        if ( usePositionRate.equalsIgnoreCase( "F" ) )
                        {

                            FXMarketDataElement mdeTemp = mds.findSpotConversionMarketDataElement( baseCcy, varCcy, false );
                            boolean invertFlag = false;
                            if ( mdeTemp == null || mdeTemp.getFXPrice() == null )
                            {
                                invertFlag = true;
                                mdeTemp = mds.findSpotConversionMarketDataElement( varCcy, baseCcy, false );
                            }
                            if ( mdeTemp == null || mdeTemp.getFXPrice() == null )
                            {
                                log.warn( "AutoRollSchedulerTask:: MDE not found mds is  " + mds + ", cp is " + cp + ", base date is " + mds.getBaseDate() + ", mde is " + mdeTemp );
                            }
                            else
                            {
                                // if the market data element currency pair base is not same the base currency, then also it needs to be inverted.
                                CurrencyPair mdeCcyPair = mdeTemp.getCurrencyPair();
                                if ( !baseCcy.isSameAs( mdeCcyPair.getBaseCurrency() ) )
                                {
                                    if ( !invertFlag )
                                    {
                                        log.warn( "ARST.execute : direct currency pair rate not found for ccypair=" + mdeCcyPair );
                                    }
                                    invertFlag = true;
                                }

                                FXPrice priceFinal = mdeTemp.getFXPrice();
                                if ( invertFlag )
                                {
                                    priceFinal = mdeTemp.getFXPrice().getInverted();
                                }
                                if ( useMid.equals( "N" ) )
                                {
                                    if ( priceFinal.getOfferFXRate() != null && priceFinal.getOfferFXRate().getSpotRate() > 0 &&
                                            priceFinal.getBidFXRate() != null && priceFinal.getBidFXRate().getSpotRate() > 0 )
                                    {
                                        offerRate = priceFinal.getOfferFXRate().getSpotRate();
                                        bidRate = priceFinal.getBidFXRate().getSpotRate();
                                        rate = baseAmt.startsWith( "-" ) ? bidRate : offerRate;
                                    }
                                    else
                                    {
                                        log.error( "AutoRollSchedulerTask:: Bid rate or offer rate is null for mde " + mdeTemp );
                                        if ( priceFinal.getMidFXRate() != null && priceFinal.getMidFXRate().getSpotRate() > 0 )
                                        {
                                            rate = priceFinal.getMidFXRate().getSpotRate();
                                        }
                                        else if ( priceFinal.getComputedMidFXRate() != null && priceFinal.getComputedMidFXRate().getSpotRate() > 0 )
                                        {
                                            rate = priceFinal.getComputedMidFXRate().getSpotRate();
                                            log.error( "AutoRollSchedulerTask:: Bid rate or offer rate is null for mde " + mdeTemp + ", mid rate is used " );
                                        }
                                    }
                                }
                                else
                                {
                                    if ( priceFinal.getMidFXRate() != null && priceFinal.getMidFXRate().getSpotRate() > 0 )
                                    {
                                        rate = priceFinal.getMidFXRate().getSpotRate();
                                    }
                                    else if ( priceFinal.getComputedMidFXRate() != null && priceFinal.getComputedMidFXRate().getSpotRate() > 0 )
                                    {
                                        rate = priceFinal.getComputedMidFXRate().getSpotRate();
                                    }
                                    else
                                    {
                                        log.error( "AutoRollSchedulerTask:: Mid rate is null for mde " + mdeTemp );
                                        if ( priceFinal.getOfferFXRate() != null && priceFinal.getOfferFXRate().getSpotRate() > 0 &&
                                                priceFinal.getBidFXRate() != null && priceFinal.getBidFXRate().getSpotRate() > 0 )
                                        {
                                            offerRate = priceFinal.getOfferFXRate().getSpotRate();
                                            bidRate = priceFinal.getBidFXRate().getSpotRate();
                                            rate = baseAmt.startsWith( "-" ) ? bidRate : offerRate;
                                            log.error( "AutoRollSchedulerTask:: Bid/Offer rates used for mde " + mdeTemp );
                                        }

                                    }
                                }
                                double calculatedTermAmount = absBaseAmt * rate;
                                absTermAmt = MathUtilC.correctFloatingPointsCalculationPrecision ( calculatedTermAmount );
                                log.info( "AutoRollSchedulerTask:: trade Rate used is " + rate + ", absTermAmt="
                                        + absTermAmt + ",absBaseAmt=" + absBaseAmt + ",offerRate=" + offerRate
                                        + ",bidRate=" + bidRate + ",rawCalcTermAmt=" + calculatedTermAmount );
                            }
                        }
                        // before proceeding with the trade, check whether this position signature was handled already.
                        String positionSignature = createPositionSignature ( makerOrgObject, aTakerOrgLocal, cp, fromDate );
                        String existingPos = positionSignatureMap.get ( positionSignature );
                        if ( existingPos != null )
                        {
                            log.warn ( new StringBuilder( 300 ).append ("AutoRollSchedulerTask:: Skipping duplicate position from MIS. signature=" )
                                    .append ( positionSignature ).append ( ",existingValue=" ).append ( existingPos )
                                    .append ( ",newValue=" ).append ( input ).toString () );
                            continue;
                        }
                        positionSignatureMap.put ( positionSignature, input );
                        log.info ( "AutoRollSchedulerTask:: positionSignature=" + positionSignature + ",positionInfoFromMISPortal=" + input );

                        HashMap<String, String> tradeData = new HashMap<String, String>( 21 );
                        tradeData.put( TradeLoaderC.TRADE_DATE_COLUMN + "(dd-MMM-yyyy)", fromDate.getFormattedDate( IdcDate.DD_MMM_YYYY_HYPHEN ) );
                        tradeData.put( TradeLoaderC.VALUE_DATE_COLUMN, "" );
                        tradeData.put( TradeLoaderC.CURRENCY_PAIR_COLUMN, cp );
                        tradeData.put( TradeLoaderC.TRADE_TYPE_COLUMN, "FXSpot" );
                        tradeData.put( TradeLoaderC.BUY_SELL_COLUMN, baseAmt.startsWith( "-" ) ? "S" : "B" ); //do a reverse trade first
                        tradeData.put( TradeLoaderC.BASE_CCY_AMOUNT_COLUMN, absBaseAmt + "" );
                        tradeData.put( TradeLoaderC.TERM_CCY_AMOUNT_COLUMN, absTermAmt + "" );
                        tradeData.put( TradeLoaderC.RATE_COLUMN, "" + rate );
                        tradeData.put( TradeLoaderC.ORG_COLUMN, takerOrgLocal );
                        tradeData.put( TradeLoaderC.CPTY_ORG_COLUMN, makerOrg );
                        tradeData.put( TradeLoaderC.TAKER_LE_COLUMN, "" );
                        tradeData.put( TradeLoaderC.MAKER_LE_COLUMN, "" );
                        tradeData.put( TradeLoaderC.STP_DOWNLOAD_COLUMN, stpFlag );
                        tradeData.put( TradeLoaderC.LE_REF_ID_COLUMN, "" );
                        tradeData.put( TradeLoaderC.TP_REF_ID_COLUMN, "" );
                        tradeData.put( TradeLoaderC.USER_COLUMN, aTakerOrgLocal.getDefaultDealingUser().getShortName() );
                        tradeData.put( TradeLoaderC.CPTY_USER_COLUMN, makerUser );
                        String tradeChannel = TradeConfigurationFactory.getTradeConfigurationMBean().isAutoRollTradeChannelEnabled( makerOrgObject ) ?
                                TradeServiceConstants.ADMIN_MANUAL_AUTOROLL_TRADE_CHANNEL : TradeServiceConstants.ADMIN_MANUAL_TRADE_CHANNEL;
                        tradeData.put( TradeLoaderC.CHANNEL_COLUMN, tradeChannel );
                        tradeData.put( TradeLoaderC.DEALT_CCY_COLUMN, cp.substring(0, cp.indexOf( "/" ) ) );

                        tradeData.put( TradeLoaderC.FORWARD_POINTS_COLUMN, "" );
                        tradeData.put( TradeLoaderC.SPOT_RATE_COLUMN, "" + rate );
                        tradeData.put ( TradeLoaderC.SKIP_ACCOUNT_CHECK_PARAM, Boolean.TRUE.toString () );

                        // set the quote convention from market data set
                        if ( mds != null && mds.getFXRateConvention() != null )
                        {
                            tradeData.put( TradeLoaderC.FXRATE_CONVENTION, mds.getFXRateConvention().getShortName() );
                        }
                        boolean asyncPersistence = TradeConfigurationFactory.getTradeConfigurationMBean().isAsyncPersistenceForManualTrade();
                        if(asyncPersistence) tradeData.put(TradeLoaderC.ASYNC_PERSIST, "true");

                        Trade trade = manualTradeService.loadTrade(tradeData);
                        Map<String, Exception> exceptions = manualTradeService.getTradeLoadExceptions();
                        Set<String> errors = exceptions.keySet();
                        if ( errors.size() > 0 )
                        {
                            StringBuilder sb = new StringBuilder(100).append("AutoRollSchedulerTask::  Position #")
                                    .append(counter).append(": Trade #1 Failed. Exception is : ").append(errors);
                            log.warn( sb.toString() );
                            String tid = trade != null ? trade.getTransactionID() : "N/A";
                            AlertLoggerFactory.getMessageLogger().log(ISAlertMBean.AUTOROLL_SCHEDULER_TASK_EXECUTE, this.getClass().getName(), sb.toString(), tid );
                            continue;
                        }
                        double calculatedRate = absTermAmt / absBaseAmt;
                        tradeData.put( TradeLoaderC.BUY_SELL_COLUMN, baseAmt.startsWith( "-" ) ? "B" : "S" );
                        tradeData.put( TradeLoaderC.TRADE_DATE_COLUMN + "(dd-MMM-yyyy)", currentTradeDate.getFormattedDate( IdcDate.DD_MMM_YYYY_HYPHEN ) );
                        if ( usePositionRate.equalsIgnoreCase( "F" ) )
                        {
                            if ( bidRate > 0 && offerRate > 0 )
                            {
                                rate = baseAmt.startsWith( "-" ) ? offerRate : bidRate;
                            }   // else it is for MidRate; so use the same rate
                            absTermAmt = absBaseAmt * rate;
                            tradeData.put( TradeLoaderC.TERM_CCY_AMOUNT_COLUMN, absTermAmt + "" );
                            tradeData.put( TradeLoaderC.RATE_COLUMN, "" + rate );
                            tradeData.put( TradeLoaderC.SPOT_RATE_COLUMN, "" + rate );
                            log.info( "AutoRollSchedulerTask:: Second trade Rate used is " + rate + ", absTermAmt=" + absTermAmt + ",absBaseAmt=" + absBaseAmt + ",offerRate=" + offerRate + ",bidRate=" + bidRate );
                        }
                        //if ( params.getFXRate().getRate() < calculatedRate * 0.99 || params.getFXRate().getRate() > calculatedRate * 1.01 )
                        calculatedRate = absTermAmt / absBaseAmt;
                        manualTradeService.getTradeLoadExceptions().clear();
                        Trade trade2 = manualTradeService.loadTrade(tradeData);
                        exceptions = manualTradeService.getTradeLoadExceptions();
                        errors = exceptions.keySet();
                        if ( errors.size() > 0 )
                        {
                            StringBuilder sb = new StringBuilder(100).append("AutoRollSchedulerTask::  Position #")
                                    .append(counter).append("Trade #2 Failed. Exception is : ").append(errors);
                            log.warn( sb.toString() );
                            String tid = trade2 != null ? trade2.getTransactionID() : "N/A";
                            AlertLoggerFactory.getMessageLogger().log(ISAlertMBean.AUTOROLL_SCHEDULER_TASK_EXECUTE, this.getClass().getName(), sb.toString(), tid );

                        }
                        else
                        {
                            log.info( "AutoRollSchedulerTask::  Position #" + counter + ". Trade #1 Successful - " + trade.getTransactionID() + ". Trade #2 Successful - " + trade2.getTransactionID() + "." );
                        }
                    }
                    catch ( Exception ee )
                    {
                        StringBuilder sb = new StringBuilder(50)
                                .append("AutoRollSchedulerTask:: Exception happened while processing input ").append(input);
                        log.error( sb.toString(), ee );
                        AlertLoggerFactory.getMessageLogger().log(ISAlertMBean.AUTOROLL_SCHEDULER_TASK_EXECUTE, this.getClass().getName(), sb.toString(), ee.getMessage());
                    }
                }
                if ( !positionsReturned )
                {
                    log.warn( "AutoRollSchedulerTask:: No Positions returned by MIS for URL " + misUrl );
                }
            }
            //compareMISPositionServiceResults();
            log.info( "AutoRollSchedulerTask:: ends successfully" );

        }
        catch ( Exception ex )
        {
            String logStr = "AutoRollSchedulerTask:: Exception happened" + ex;
            log.error( logStr, ex );
            AlertLoggerFactory.getMessageLogger().log(ISAlertMBean.AUTOROLL_SCHEDULER_TASK_EXECUTE, this.getClass().getName(), logStr, ex.getMessage());
        }
    }

    public String getMakerOrg()
    {
        return makerOrg;
    }

    public void setMakerOrg( String makerOrg )
    {
        this.makerOrg = makerOrg;
    }

    public String getTakerOrg()
    {
        return takerOrg;
    }

    public void setTakerOrg( String takerOrg )
    {
        this.takerOrg = takerOrg;
    }

    public String getMakerUser()
    {
        return makerUser;
    }

    public void setMakerUser( String makerUser )
    {
        this.makerUser = makerUser;
    }

    public String getStpFlag()
    {
        return stpFlag;
    }

    public void setStpFlag( String stpFlag )
    {
        this.stpFlag = stpFlag;
    }

    public String getUseMid()
    {
        return useMid;
    }

    public void setUseMid( String useMid )
    {
        this.useMid = useMid;
    }

    public String getNoOfDays()
    {
        return noOfDays;
    }

    public void setNoOfDays( String noOfDays )
    {
        this.noOfDays = noOfDays;
    }

    public String getUsePositionRate()
    {
        return usePositionRate;
    }

    public void setUsePositionRate( String usePositionRate )
    {
        this.usePositionRate = usePositionRate;
    }

    public String getMdsUsed()
    {
        return mdsUsed;
    }

    public void setMdsUsed( String mdsUsed )
    {
        this.mdsUsed = mdsUsed;
    }

    // used by utility jsp
    public String getMISPortalURL()
    {
        IdcDate fromDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate().subtractDays( 1 );
        return getMISPortalURL( makerOrg, fromDate );
    }


    public String getMISPortalURL( String maker, IdcDate date )
    {
        String birtServerHostForAutoRoll = ConfigurationFactory.getServerMBean().getBIRTServerHostForAutoRoll();
        String url = ConfigurationFactory.getServerMBean().getBIRTServerHost();
        if ( null != birtServerHostForAutoRoll && !"".equals( birtServerHostForAutoRoll.trim() ) )
        {
            url = birtServerHostForAutoRoll;
        }
        String newMISUrlPrefix = ConfigurationFactory.getServerMBean().getBIRTServerUri();
        // example http://bqc1-app3-503:8000/birt/jsp/positions.jsp?type=positions&org=GrupoSantander&cptyOrg=RajeevOrg1PB&fromDate=28-Jun-2012&toDate=28-Jun-2012
        return url + newMISUrlPrefix + "?type=positions&org=" + maker + "&cptyOrg=" + takerOrg.replace( "+", "%2B" ) + "&fromDate=" + date.getFormattedDate( IdcDate.DD_MMM_YYYY_HYPHEN )
                + "&toDate=" + date.getFormattedDate( IdcDate.DD_MMM_YYYY_HYPHEN );
    }

    private Collection<String> extractPositions( BufferedReader response )
    {
        Collection<String> positions = new ArrayList<String>();
        try
        {
            int count = 0;
            String line = null;
            do
            {
                if ( response != null )
                {
                    line = response.readLine();
                    if ( line != null && line.trim().length() > 0 )
                    {
                        positions.add( line.trim() );
                    }
                    count++;
                    if ( count > 10000 )  // to avoid indefinite loop
                    {
                        log.error( "ARST.extractPositions : abnormal response from MIS portal. Skipping to avoid infinite loop." );
                        break;
                    }
                }
            }
            while ( line != null );
        }
        catch ( Exception e )
        {
            log.error( "ARST.extractPositions : Exception while extracting the positions. response=" + response, e );
        }
        finally
        {
            try
            {
                if ( response != null )
                {
                    response.close();
                }
            }
            catch ( Exception ex )
            {
                log.error( "ARST.extractPositions : Exception while closing the buffer.", ex );
            }
        }
        return positions;
    }

    private String createPositionSignature( Organization makerOrg, Organization takerOrg, String ccyPair, IdcDate fromDate )
    {
        return new StringBuilder ( 200 ).append ( makerOrg.getShortName () ).append ( SIGNATURE_DELIMITER  )
                .append ( takerOrg.getShortName () ).append ( SIGNATURE_DELIMITER ).append ( ccyPair )
                .append ( SIGNATURE_DELIMITER ).append ( fromDate.getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) ).toString ();

    }
}
