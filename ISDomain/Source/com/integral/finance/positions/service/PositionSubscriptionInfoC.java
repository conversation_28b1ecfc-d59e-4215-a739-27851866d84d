package com.integral.finance.positions.service;
// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.Currency;
import com.integral.finance.positions.exception.PositionSubscriptionException;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.List;

/**
 * Represents a subscription for registering with the PositionService.
 *
 * <AUTHOR> Development Corp.
 */

public class PositionSubscriptionInfoC implements PositionSubscriptionInfo
{
    private LegalEntity legalEntity;
    private Organization organization;
    private User user;
    private LegalEntity tradingParty;
    private Organization tradingPartyOrganization;
    private List<Integer> cptyOrgsType;
    private IdcDate fromSettlementDate;
    private IdcDate toSettlementDate;
    private IdcDate fromTradeDate;
    private IdcDate toTradeDate;
    private User tradingPartyUser;
    private String cp;
    private boolean pnlEnabled;
    private Currency pnlCurrency;
    private List<String> groupByColumns;
    private boolean doSorting = true;
    private boolean enableJSON;
    private long publishInterval;
    private int settledOpenPositions = RETURN_OPEN_POSITIONS_ONLY ;
    private boolean settleTodayOpen;
    private boolean isCustomerPositionIncluded =false;

    private IdcDate fromRangeValueDate;
    private IdcDate fromRangetradeDate;
    private boolean treeViewEnabled=false;
    private boolean getOpenPosition = false;
    private User requestUser;
    private String positionLevel;

    private boolean wildCardSubscription = false;
    
    /**
     * This is position request ID which client provided while subscribing position and can reference while un-subscribing.
     */
    private String clientReferenceId;
    
	public PositionSubscriptionInfoC( LegalEntity legalEntity, Organization organization, User user, LegalEntity tradingParty,
                                      Organization tradingPartyOrganization, IdcDate fromSettlementDate,
                                      IdcDate toSettlementDate, IdcDate fromTradeDate, IdcDate toTradeDate, User tradingPartyUser,
                                      String cp,
                                      Currency limitCurrency )
    {

        this.legalEntity = legalEntity;
        this.organization = organization;
        this.user = user;
        this.tradingParty = tradingParty;
        this.tradingPartyOrganization = tradingPartyOrganization;
        this.fromSettlementDate = fromSettlementDate;
        this.toSettlementDate = toSettlementDate;
        this.fromTradeDate = fromTradeDate;
        this.toTradeDate = toTradeDate;
        this.tradingPartyUser = tradingPartyUser;
        this.cp = cp;
        this.pnlCurrency = limitCurrency;
    }

    public PositionSubscriptionInfoC()
    {
    }

    public PositionSubscriptionInfoC(PositionSubscriptionInfo positionSubscriptionInfo)
    {
        this.legalEntity = positionSubscriptionInfo.getLegalEntity();
        this.organization = positionSubscriptionInfo.getOrganization();
        this.user = positionSubscriptionInfo.getUser();
        this.tradingParty = positionSubscriptionInfo.getTradingParty();
        this.tradingPartyOrganization = positionSubscriptionInfo.getTradingPartyOrganization();
        this.fromSettlementDate = positionSubscriptionInfo.getFromSettlementDate();
        this.toSettlementDate = positionSubscriptionInfo.getToSettlementDate();
        this.fromTradeDate = positionSubscriptionInfo.getFromTradeDate();
        this.toTradeDate = positionSubscriptionInfo.getToTradeDate();
        this.tradingPartyUser = positionSubscriptionInfo.getTradingPartyUser();
        this.cp = positionSubscriptionInfo.getCurrencyPair();
        this.pnlCurrency = positionSubscriptionInfo.getLimitCurrency();
        this.fromRangeValueDate=positionSubscriptionInfo.getFromRangeSettlementDate();
        this.fromRangetradeDate=positionSubscriptionInfo.getFromRangeTradeDate();
        this.groupByColumns=positionSubscriptionInfo.groupByColums();
        this.treeViewEnabled=positionSubscriptionInfo.isAggregateViewEnabled();
        this.pnlEnabled=positionSubscriptionInfo.isPNLEnabled();
        this.publishInterval=positionSubscriptionInfo.getPublishInterval();
        this.settledOpenPositions=positionSubscriptionInfo.getSettledOpenPositions();
        this.treeViewEnabled=positionSubscriptionInfo.isAggregateViewEnabled();
        this.setCustomerPositionIncluded(positionSubscriptionInfo.isCustomerPositionIncluded());
        this.setPositionLevel(positionSubscriptionInfo.getPositionLevel());
        this.setWildCardSubscription(positionSubscriptionInfo.isWildCardSubscription());
    }

    public String getCp()
    {
        return cp;
    }

    public void setCp( String cp )
    {
        this.cp = cp;
    }

    public LegalEntity getLegalEntity()
    {
        return legalEntity;
    }

    public void setLegalEntity( LegalEntity legalEntity )
    {
        this.legalEntity = legalEntity;
    }

    public Organization getOrganization()
    {
        return organization;
    }

    public void setOrganization( Organization organization )
    {
        this.organization = organization;
    }

    public User getUser()
    {
        return user;
    }

    @Override
	public void setUser( User user )
    {
        this.user = user;
    }

    public LegalEntity getTradingParty()
    {
        return tradingParty;
    }

    public void setTradingParty( LegalEntity tradingParty )
    {
        this.tradingParty = tradingParty;
    }

    public Organization getTradingPartyOrganization()
    {
        return tradingPartyOrganization;
    }

    public void setTradingPartyOrganization( Organization tradingPartyOrganization )
    {
        this.tradingPartyOrganization = tradingPartyOrganization;
    }

    public IdcDate getFromSettlementDate()
    {
        return fromSettlementDate;
    }

    public void setToSettlementDate( IdcDate toSettlementDate )
    {
        this.toSettlementDate = toSettlementDate;
    }

    public IdcDate getToSettlementDate()
    {
        return toSettlementDate;
    }

    public void setFromSettlementDate( IdcDate fromSettlementDate )
    {
        this.fromSettlementDate = fromSettlementDate;
    }

    public boolean isCustomerPositionIncluded() {
        return isCustomerPositionIncluded;
    }

    public void setCustomerPositionIncluded(boolean isCustomerPositionIncluded) {
        this.isCustomerPositionIncluded = isCustomerPositionIncluded;
    }

    public IdcDate getFromTradeDate()
    {
        return fromTradeDate;
    }

    public void setFromTradeDate( IdcDate fromTradeDate )
    {
        this.fromTradeDate = fromTradeDate;
    }

    public IdcDate getToTradeDate()
    {
        return toTradeDate;
    }

    public void setToTradeDate( IdcDate toTradeDate )
    {
        this.toTradeDate = toTradeDate;
    }

    public User getTradingPartyUser()
    {
        return tradingPartyUser;
    }

    public void setTradingPartyUser( User tradingPartyUser )
    {
        this.tradingPartyUser = tradingPartyUser;
    }


    public String getCurrencyPair()
    {
        return cp;
    }

    public void setPNLEnabled( boolean pnlSubscribe )
    {
        pnlEnabled = pnlSubscribe;
    }

    public boolean isPNLEnabled()
    {
        return pnlEnabled;
    }

    public void setGroupByColumns( List<String> columns )
    {
        this.groupByColumns = columns;
    }

    public List<String> groupByColums()
    {
        return groupByColumns;
    }

    public String getPositionLevel() {
        return positionLevel;
    }

    public void setPositionLevel(String positionLevel) {
        this.positionLevel = positionLevel;
    }

//    public PositionSubscriptionSecondaryKeyC getSecondaryKey()
//    {
//
//        return new PositionSubscriptionSecondaryKeyC(
//                user != null ? user.getObjectID() : 0,
//                legalEntity != null ? legalEntity.getObjectID() : 0,
//                tradingPartyUser != null ? tradingPartyUser.getObjectID() : 0,
//                tradingParty != null ? tradingParty.getObjectID() : 0,
//                cp, fromSettlementDate, toSettlementDate, fromTradeDate, toTradeDate,cptyOrgsType
//                /*toSettlementDate*/ );
//
//    }

//    public PositionSubscriptionPrimaryKeyC getPrimaryKey() throws PositionSubscriptionException
//    {
//        long orgID = 0L;
//        long tpOrgID = 0L;
//
//        orgID = organization != null ? organization.getObjectID() :
//                ( legalEntity != null ?
//                        legalEntity.getOrganization().getObjectID() :
//                        ( user != null ? user.getOrganization().getObjectID() : 0 ) );
//
//        tpOrgID = tradingPartyOrganization != null ? +tradingPartyOrganization.getObjectID() :
//                ( tradingParty != null ?
//                        tradingParty.getOrganization().getObjectID() :
//                        ( tradingPartyUser != null ? tradingPartyUser.getOrganization().getObjectID() : 0 ) );
//
//        if ( ( orgID == 0L ) && ( tpOrgID == 0L ) )
//        {
//            throw new PositionSubscriptionException( "Unable to retreive both the organization information" );
//        }
//
//        return new PositionSubscriptionPrimaryKeyC( orgID, tpOrgID );
//    }

    public Currency getLimitCurrency()
    {
        return pnlCurrency;
    }

    public void setLimitCurrency( Currency pnlCurrency )
    {
        this.pnlCurrency = pnlCurrency;
    }


    public boolean isDoSorting()
    {
        return doSorting;
    }

    public void setDoSorting( boolean doSorting )
    {
        this.doSorting = doSorting;
    }


    public List<Integer> getCptyOrgsType()
    {
        return cptyOrgsType;
    }

    public void setCptyOrgsType( List<Integer> cptyOrgsType )
    {
        this.cptyOrgsType = cptyOrgsType;
    }

    public void enableJSONFormat(boolean option)
    {
        this.enableJSON  = option;
    }

    public boolean isJSONEnabled()
    {
        return enableJSON;
    }

    public long getPublishInterval() {
        return publishInterval;  //To change body of implemented methods use File | Settings | File Templates.
    }

    public void setPublishInterval(long publishInterval) {
        this.publishInterval = publishInterval;
    }
    
    public boolean isGetOpenPosition()
	{
		return getOpenPosition;
	}

	@Override
	public void setGetOpenPosition(boolean getOpenPosition)
	{
		this.getOpenPosition = getOpenPosition;
	}

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PositionSubscriptionInfoC)) return false;

        PositionSubscriptionInfoC that = (PositionSubscriptionInfoC) o;

        if (doSorting != that.doSorting) return false;
        if (enableJSON != that.enableJSON) return false;
        if (pnlEnabled != that.pnlEnabled) return false;
        if (publishInterval != that.publishInterval) return false;
        if (settleTodayOpen != that.settleTodayOpen) return false;
        if (settledOpenPositions != that.settledOpenPositions) return false;
        if (treeViewEnabled != that.treeViewEnabled) return false;
        if (getOpenPosition != that.getOpenPosition) return false;
        if (cp != null ? !cp.equals(that.cp) : that.cp != null) return false;
        if (cptyOrgsType != null ? !cptyOrgsType.equals(that.cptyOrgsType) : that.cptyOrgsType != null) return false;
        if (fromRangeValueDate != null ? !fromRangeValueDate.equals(that.fromRangeValueDate) : that.fromRangeValueDate != null)
            return false;
        if (fromRangetradeDate != null ? !fromRangetradeDate.equals(that.fromRangetradeDate) : that.fromRangetradeDate != null)
            return false;
        if (fromSettlementDate != null ? !fromSettlementDate.equals(that.fromSettlementDate) : that.fromSettlementDate != null)
            return false;
        if (fromTradeDate != null ? !fromTradeDate.equals(that.fromTradeDate) : that.fromTradeDate != null)
            return false;
        if (groupByColumns != null ? !groupByColumns.equals(that.groupByColumns) : that.groupByColumns != null)
            return false;
        if (legalEntity != null ? !legalEntity.equals(that.legalEntity) : that.legalEntity != null) return false;
        if (pnlCurrency != null ? !pnlCurrency.equals(that.pnlCurrency) : that.pnlCurrency != null)
            return false;
        if (organization != null ? !organization.equals(that.organization) : that.organization != null) return false;
        if (toSettlementDate != null ? !toSettlementDate.equals(that.toSettlementDate) : that.toSettlementDate != null)
            return false;
        if (toTradeDate != null ? !toTradeDate.equals(that.toTradeDate) : that.toTradeDate != null) return false;
        if (tradingParty != null ? !tradingParty.equals(that.tradingParty) : that.tradingParty != null) return false;
        if (tradingPartyOrganization != null ? !tradingPartyOrganization.equals(that.tradingPartyOrganization) : that.tradingPartyOrganization != null)
            return false;
        if (tradingPartyUser != null ? !tradingPartyUser.equals(that.tradingPartyUser) : that.tradingPartyUser != null)
            return false;
        if (user != null ? !user.equals(that.user) : that.user != null) return false;
        if (isCustomerPositionIncluded != that.isCustomerPositionIncluded)return false;
        if (positionLevel != null ? !positionLevel.equals(that.positionLevel) : that.positionLevel != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = legalEntity != null ? legalEntity.hashCode() : 0;
        result = 31 * result + (organization != null ? organization.hashCode() : 0);
        result = 31 * result + (user != null ? user.hashCode() : 0);
        result = 31 * result + (tradingParty != null ? tradingParty.hashCode() : 0);
        result = 31 * result + (tradingPartyOrganization != null ? tradingPartyOrganization.hashCode() : 0);
        result = 31 * result + (cptyOrgsType != null ? cptyOrgsType.hashCode() : 0);
        result = 31 * result + (fromSettlementDate != null ? fromSettlementDate.hashCode() : 0);
        result = 31 * result + (toSettlementDate != null ? toSettlementDate.hashCode() : 0);
        result = 31 * result + (fromTradeDate != null ? fromTradeDate.hashCode() : 0);
        result = 31 * result + (toTradeDate != null ? toTradeDate.hashCode() : 0);
        result = 31 * result + (tradingPartyUser != null ? tradingPartyUser.hashCode() : 0);
        result = 31 * result + (cp != null ? cp.hashCode() : 0);
        result = 31 * result + (pnlEnabled ? 1 : 0);
        result = 31 * result + (pnlCurrency != null ? pnlCurrency.hashCode() : 0);
        result = 31 * result + (groupByColumns != null ? groupByColumns.hashCode() : 0);
        result = 31 * result + (doSorting ? 1 : 0);
        result = 31 * result + (enableJSON ? 1 : 0);
        result = 31 * result + (int) (publishInterval ^ (publishInterval >>> 32));
        result = 31 * result + settledOpenPositions;
        result = 31 * result + (settleTodayOpen ? 1 : 0);
        result = 31 * result + (fromRangeValueDate != null ? fromRangeValueDate.hashCode() : 0);
        result = 31 * result + (fromRangetradeDate != null ? fromRangetradeDate.hashCode() : 0);
        result = 31 * result + (treeViewEnabled ? 1 : 0);
        result = 31 * result + (clientReferenceId != null ? clientReferenceId.hashCode() : 0);
        result = 31 * result + (isCustomerPositionIncluded ? 1 : 0);
        result = 31 * result + (positionLevel != null ? positionLevel.hashCode() : 0);
        return result;
    }

    public int getSettledOpenPositions()
    {
        return settledOpenPositions;
    }

    public void setSettledOpenPositions( int settledOpenPositions )
    {
        this.settledOpenPositions = settledOpenPositions;
    }

    public boolean isSettleTodayOpen()
    {
        return settleTodayOpen;
    }

    public void setSettleTodayOpen( boolean settleTodayOpen )
    {
        this.settleTodayOpen = settleTodayOpen;
    }

    @Override
    public void setFromRangeSettlementDate(IdcDate fromRangeValueDate)
    {
        this.fromRangeValueDate=fromRangeValueDate;
    }

    @Override
    public void setFromRangeTradeDate(IdcDate fromRangetradeDate)
    {
        this.fromRangetradeDate=fromRangetradeDate;
    }

    @Override
    public IdcDate getFromRangeSettlementDate()
    {
        return fromRangeValueDate;
    }

    @Override
    public IdcDate getFromRangeTradeDate()
    {
        return fromRangetradeDate;
    }

    @Override
    public void setAggregateViewEnabled(boolean selection)
    {
        treeViewEnabled=selection;
    }

    @Override
    public boolean isAggregateViewEnabled()
    {
        return treeViewEnabled;
    }
    
    @Override
	public String getClientReferenceId()
	{
		return clientReferenceId;
	}

	@Override
	public void setClientReferenceId(String clientReferenceId)
	{
		this.clientReferenceId = clientReferenceId;
	}


    @Override
    public String toString()
    {
        final StringBuilder sb = new StringBuilder();
        sb.append( "PositionSubscriptionInfoC" );
        sb.append( "{legalEntity=" ).append(legalEntity);
        sb.append( ", organization=" ).append( organization );
        sb.append( ", user=" ).append( user );
        sb.append( ", tradingParty=" ).append( tradingParty );
        sb.append( ", tradingPartyOrganization=" ).append( tradingPartyOrganization );
        sb.append( ", cptyOrgsType=" ).append( cptyOrgsType );
        sb.append( ", fromSettlementDate=" ).append( fromSettlementDate );
        sb.append( ", toSettlementDate=" ).append( toSettlementDate );
        sb.append( ", fromTradeDate=" ).append( fromTradeDate );
        sb.append( ", toTradeDate=" ).append( toTradeDate );
        sb.append(",  fromRangeSettlementDate=" ).append( fromRangeValueDate );
        sb.append(",  fromRangeTradeDate=" ).append( fromRangetradeDate );
        sb.append( ", tradingPartyUser=" ).append( tradingPartyUser );
        sb.append( ", cp='" ).append(cp).append( '\'' );
        sb.append(", pnlEnabled=").append( pnlEnabled );
        sb.append( ", limitCurrency=" ).append( pnlCurrency );
        sb.append( ", groupByColumns=" ).append( groupByColumns );
        sb.append( ", doSorting=" ).append( doSorting );
        sb.append( ", enableJSON=" ).append( enableJSON );
        sb.append( ", publishInterval=" ).append( publishInterval );
        sb.append( ", settledOpenPositions=" ).append( settledOpenPositions );
        sb.append( ", settleTodayOpen=" ).append( settleTodayOpen );
        sb.append( ", isAggregatedViewEnabled=" ).append( treeViewEnabled )
        .append( ", clientReferenceId=" ).append( clientReferenceId );
        sb.append( ", isCustomerPositionIncluded=" ).append(isCustomerPositionIncluded);
        sb.append( ", positionLevel=" ).append(positionLevel);
        sb.append( '}' );
        return sb.toString();
    }

	/* (non-Javadoc)
	 * @see com.integral.finance.positions.service.PositionSubscriptionInfo#getRequestUser()
	 */
	@Override
	public User getRequestUser()
	{
		return this.requestUser;
	}

	/* (non-Javadoc)
	 * @see com.integral.finance.positions.service.PositionSubscriptionInfo#setRequestUser(com.integral.user.User)
	 */
	@Override
	public void setRequestUser( User user )
	{
		this.requestUser = user;
	}


    public void setWildCardSubscription(boolean isWildCardSubscription ) {
        this.wildCardSubscription = isWildCardSubscription;
    }

    public boolean isWildCardSubscription() {
        return this.wildCardSubscription;
    }
}
