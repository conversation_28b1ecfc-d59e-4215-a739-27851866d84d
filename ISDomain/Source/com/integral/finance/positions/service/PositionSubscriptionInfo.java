package com.integral.finance.positions.service;
// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.google.common.base.Objects;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.Currency;
import com.integral.finance.positions.exception.PositionSubscriptionException;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.List;
/**
 * Represents a subscription for registering with the PositionService.
 <AUTHOR> Development Corp.
 */

/**
 * Immutable object, subscription objects cannot be changed during runtime
 */
public interface PositionSubscriptionInfo
{
    public static final int TP_ORG_TYPE_PROVIDERS = 0;
    public static final int TP_ORG_TYPE_BROKERS = 1;
    public static final int TP_ORG_TYPE_TAKERS = 2;
    public static final int TP_ORG_TYPE_CUSTOMERS = 3;
    public static final int TP_ORG_TYPE_PRIME_BROKER = 4;

    public static final int RETURN_ALL_POSITIONS = 2;
    public static final int RETURN_OPEN_POSITIONS_ONLY = 0;
    public static final int RETURN_SETTLED_POSITIONS_ONLY = 1;

    public LegalEntity getLegalEntity();

    public Organization getOrganization();

    public User getUser();

    public LegalEntity getTradingParty();

    public Organization getTradingPartyOrganization();

    public IdcDate getFromSettlementDate();

    public IdcDate getToSettlementDate();

    public IdcDate getFromTradeDate();

    public void setFromTradeDate(IdcDate fromTradeDate);

    public IdcDate getToTradeDate();

    public void setToTradeDate(IdcDate toTradeDate);

    public User getTradingPartyUser();

    public void setCp(String cp);

    public Currency getLimitCurrency();

    public String getCurrencyPair();

    public void setPNLEnabled(boolean pnlSubscribe);

    public boolean isPNLEnabled();

    public void setGroupByColumns(List<String> columns);

    public List<String> groupByColums();

    //public PositionSubscriptionPrimaryKeyC getPrimaryKey() throws PositionSubscriptionException;

    //public PositionSubscriptionSecondaryKeyC getSecondaryKey() throws PositionSubscriptionException;

    public boolean isDoSorting();

    public void setDoSorting(boolean doSorting);

    public List<Integer> getCptyOrgsType();

    public void setCptyOrgsType(List<Integer> cptyOrgsType);

    public void enableJSONFormat(boolean option);

    boolean isJSONEnabled();

    public boolean isCustomerPositionIncluded();

    public void setCustomerPositionIncluded(boolean isSalesDealerIncluded);
    /**
     *
     * @return  the publish interval for  which we need to send
     * updates, if not specified will be real time.
     */
    long getPublishInterval();

    void setPublishInterval(long publishInterval);

    public void setOrganization(Organization organization);

    /**
     * returns if user wants settled/open/all positions
     * @return one of RETURN_ALL_POSITIONS, RETURN_OPEN_POSITIONS_ONLY, RETURN_SETTLED_POSITIONS_ONLY
     */
    public int getSettledOpenPositions();

    /**
     * sets if user wants settled/open/all positions
     * @param settledOpenPositions one of RETURN_ALL_POSITIONS, RETURN_OPEN_POSITIONS_ONLY, RETURN_SETTLED_POSITIONS_ONLY
     */
    public void setSettledOpenPositions(int settledOpenPositions);

    /**
     * By default positions with settlement date as today are considered as Settled; but user can over-ride it while making subscription.
     * @return boolean
     */
    public boolean isSettleTodayOpen();

    /**
     *  By default positions with settlement date as today are considered as Settled; but user can over-ride it while making subscription.
     * @param settleTodayOpen   true if user wants positions settling today as OPEN
     */
    public void setSettleTodayOpen(boolean settleTodayOpen);

    public void setTradingPartyOrganization(Organization tradingPartyOrganization);

    public void setLimitCurrency(Currency limitCurrency);

    public void setToSettlementDate(IdcDate toSettlementDate);

    public void setFromSettlementDate(IdcDate toSettlementDate);

    public void setFromRangeSettlementDate(IdcDate fromRangeValueDate);

    public void setFromRangeTradeDate(IdcDate fromRangetradeDate);

    public IdcDate getFromRangeSettlementDate();

    public IdcDate getFromRangeTradeDate();

	public boolean isGetOpenPosition();

	public abstract void setGetOpenPosition(boolean getOpenPosition);

	public abstract void setClientReferenceId(String clientReferenceId);

	public abstract String getClientReferenceId();

	public abstract void setUser(User user);

	public abstract User getRequestUser();

	public abstract void setRequestUser(User user);

    public void setAggregateViewEnabled(boolean selection) ;

    public boolean isAggregateViewEnabled();

    public String getPositionLevel() ;

    public void setPositionLevel(String positionLevel) ;

    public void setWildCardSubscription(boolean isWildCardSubscription) ;

    public boolean isWildCardSubscription() ;
	
	public static class Util
	{
		/**
		 * Give an ID based on request parameters. It is guaranteed that is request parameters are same (except clientRereferenceId) then ID returned by this method will be same.
		 * @param request
		 * @return
		 */
		public static String getRequestParamId(PositionSubscriptionInfo request)
		{
			return String.valueOf(Objects.hashCode(request.getLegalEntity(), 
					request.getOrganization(), 
					request.getUser(), 
					request.getTradingParty(), 
					request.getTradingPartyOrganization(),
					request.getCptyOrgsType(), 
					request.getFromSettlementDate(), 
					request.getToTradeDate(), 
					request.getTradingPartyUser(), 
					request.getCurrencyPair(), 
					request.isPNLEnabled(),
					request.getLimitCurrency(), 
					request.groupByColums(), 
					request.isDoSorting(), 
					request.isJSONEnabled(), 
					request.getPublishInterval(),
					request.getSettledOpenPositions(),
					request. isSettleTodayOpen(), request.getFromRangeSettlementDate(), request.getFromRangeTradeDate(),
					request.isAggregateViewEnabled(),
                    request.isCustomerPositionIncluded(),
                    request.getPositionLevel()));
		}
		
	}
	
}
