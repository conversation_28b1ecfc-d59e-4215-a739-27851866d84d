package com.integral.serialization;

import com.integral.is.common.mbean.ISFactory;
import com.integral.is.message.BrokerOrderRequest;
import com.integral.is.message.ISMessage;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.spaces.serialize.ByteBufferInputStream;
import com.integral.spaces.serialize.SerializationHandler;
import com.integral.spaces.serialize.SerializerFactory;

import java.nio.ByteBuffer;

/**
 * Utility class used to serialize the ISMessage message either in old style string format or using
 * new spaces JSON serialization framework. Note that only the classes which need to use the JSON
 * serialization framework must have defined {@code ClassSerializer} and {@code FieldSerializer} annotations.
 *
 * <AUTHOR>
 */
public class ISMessageSerializerUtil
{
    /**
     * Log instance we use to write log messages.
     */
    private static final Log log = LogFactory.getLog(ISMessageSerializerUtil.class);

    /**
     * The encoding format we use to encode ISMessage into a string.
     */
    private final static String ENCODING = "UTF-8";

    /**
     * Size of the ByteBuffer we use in serialization. We might need to change the size of this if
     * the max message size is going to change in future.
     */
    private final static int BUFFER_SIZE = ISFactory.getInstance().getISMBean().getISMessageSerializerBufferSize();

    /**
     * Thread local instance of ByteBuffer we use in serialization of ISMessage. Having a ThreadLocal
     * copy would make sure that we don't create new instances of byte buffer always.
     */
    private static ThreadLocal<ByteBuffer> localByteBuffer =
        new ThreadLocal<ByteBuffer>() {
            @Override
            protected ByteBuffer initialValue()
            {
                return ByteBuffer.allocate(BUFFER_SIZE);
            }
        };

    /**
     * Serializes the given ISMessage into a string. The string could either be a JSON string
     * or plain old format string based on the message type.
     */
    public static String serialize(ISMessage message) throws Exception
    {
        switch (message.getSerializationType()) {
            case JSON:
                if (log.isDebugEnabled()) {
                    log.debug("Serializing the message: " + message + " into JSON format.");
                }

                long s = System.currentTimeMillis();
                SerializationHandler handler =
                        SerializerFactory.getHandlerForType(SerializerFactory.Type.JSON);

                ByteBuffer buf = localByteBuffer.get();

                // Reset the ByteBuffer before using it.
                buf.clear();
                handler.serializeObject(message, buf);
                buf.flip();

                // Encode the string in UTF-8 format and return it.
                String msgStr = new String(buf.array(), buf.arrayOffset(), buf.limit(), ENCODING);
                long endTime = System.currentTimeMillis();
                if((endTime-s)>1){
                    log.info("serialize: TimeTaken="+(endTime-s )+" ms");
                }
                return msgStr;

            case STRING:
                if (log.isDebugEnabled()) {
                    log.debug("Serializing the message: " + message + " into STRING format.");
                }
                return message.getToString();

            default:
                throw new IllegalArgumentException("Unknown serialization type: " + message.getSerializationType()
                                                   + " set in the message: " + message.getClass().getName());
        }
    }

    /**
     * Deserializes the given string into the given ISMessage and returns the remaining string.
     */
    public static String deserialize(ISMessage message, String str)
        throws Exception
    {
        switch (message.getSerializationType()) {
            case JSON:
                if (log.isDebugEnabled()) {
                    log.debug("Deserializing the message string: " + str + " into ISMessage of type: "
                            + message.getClass().getName());
                }
                long s = System.currentTimeMillis();
                SerializationHandler handler =
                        SerializerFactory.getHandlerForType(SerializerFactory.Type.JSON);

                ByteBuffer buf = localByteBuffer.get();
                buf.clear();

                // Decode the string from UTF-8 format.
                buf.put(str.getBytes(ENCODING));
                buf.flip();
                handler.deserializeObject(message, new ByteBufferInputStream(buf));
                long endTime = System.currentTimeMillis();
                if((endTime-s)>1){
                    log.info("deserialize: TimeTaken="+(endTime-s )+" ms");
                }
                // There is no remaining string in case of JSON serialization.
                return "";

            case STRING:
                if (log.isDebugEnabled()) {
                    log.debug("Deserializing the message string: " + str + " into ISMessage of type: "
                            + message.getClass().getName());
                }
                return message.populateObject(str);

            default:
                throw new IllegalArgumentException("Unknown serialization type: " + message.getSerializationType()
                                                   + " set in the message: " + message.getClass().getName());
        }
    }
}
