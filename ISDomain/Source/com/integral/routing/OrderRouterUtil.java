package com.integral.routing;

import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.oms.OrderConstants;
import com.integral.message.MessageEvent;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.SingleLegOrder;

/**
 * Created with IntelliJ IDEA.
 * User: anatarajan
 * Date: 7/30/14
 * Time: 1:29 PM
 * To change this template use File | Settings | File Templates.
 */
public class OrderRouterUtil {

    public static WorkflowMessage getCancelPendingWfMessage(SingleLegOrder orderRequest) {
        WorkflowMessage msg = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, MessageEvent.WITHDRAW, ISCommonConstants.MSG_TOPIC_REQUEST);
        ISUtilImpl.getInstance().addError( msg, ISCommonConstants.ORDER_CANCEL_PENDING_CANCEL);
        msg.setStatus(MessageStatus.SUCCESS); // Should it be success OR failure
        msg.setParameterValue( OrderConstants.Key_HoldOrderCancelReplaceRequest,Boolean.TRUE );
        return msg;
    }
}
