package com.integral.routing;

/**
 * Created with IntelliJ IDEA.
 * User: an<PERSON><PERSON><PERSON>
 * Date: 10/8/15
 * Time: 2:09 PM
 * To change this template use File | Settings | File Templates.
 */
public class RoutingDecision {

    public static final RoutingDecision TT = new RoutingDecision(true, true);
    public static final RoutingDecision FT = new RoutingDecision(false, true);
    public static final RoutingDecision FF = new RoutingDecision(false, false);

    private boolean result;
    private boolean furtherRoutingDecision;

    protected RoutingDecision(boolean result, boolean furtherRoutingDecision) {
        this.result = result;
        this.furtherRoutingDecision = furtherRoutingDecision;
    }

    public boolean getResult() {
        return result;
    }

    public boolean getFurtherRoutingDecision() {
        return furtherRoutingDecision;
    }
}
