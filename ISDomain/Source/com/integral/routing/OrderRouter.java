package com.integral.routing;

import com.integral.is.oms.Order;
import com.integral.is.oms.OrderExecutionDetails;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.OrderMatchRequest;
import com.integral.model.dealing.SingleLegOrder;

/**
 * Created with IntelliJ IDEA.
 * User: anatarajan
 * Date: 7/17/14
 * Time: 4:25 PM
 * To change this template use File | Settings | File Templates.
 */
public interface OrderRouter {

    WorkflowMessage route(SingleLegOrder orderRequest, boolean isPartOfBatch, boolean reloadOMS);

    WorkflowMessage cancel(WorkflowMessage wfm);

    WorkflowMessage cancelOnStartup(WorkflowMessage wfm);

    void reRoute(SingleLegOrder orderRequest, Order order);

    RoutingDecision routeToClobNow(Order order, OrderExecutionDetails oed);

    void expireVenueOrder( Order order );

    void expire(Order order);

    void status(Order order);

    void handleOrderSubmittedResponse(OrderMatchRequest matchRequest);

    void handleOrderSubmitFailedResponse(SingleLegOrder orderRequest);
     void handleOrderRejected(SingleLegOrder orderRequest);

    void handleRequestExpiryResponse( SingleLegOrder orderRequest);

    void handleRoutedRequestAmendSuccess(OrderMatchRequest orderMatchRequest);

    void handleRoutedRequestAmendFailure(OrderMatchRequest omRequest, WorkflowMessage msg);

    void handleCancelRejectResponse(SingleLegOrder orderRequest,boolean shouldAlert);

    void handleTerminalFillResponse(SingleLegOrder orderRequest);

    String amend(SingleLegOrder orderRequest, WorkflowMessage wfm);
    WorkflowMessage amendExternalVenueDirectedOrder(SingleLegOrder orderRequest, WorkflowMessage wfm);
    boolean amendVenueRoutedRequest(Order order, double amount);


    boolean cancelVenueRoutedRequest(Order order);

    boolean cancelVenueRoutedRequestSynchronous(WorkflowMessage wfm);
    
    boolean cancelVenueRoutedRequestSynchronous(SingleLegOrder orderRequest);


}
