package com.integral.routing;

import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.adaptor.order.configuration.OrderConfigurationMBean;
import com.integral.finance.dealing.ExecutionFlags;
import com.integral.is.ISCommonConstants;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.log.MessageLogger;
import com.integral.is.message.MessageFactory;
import com.integral.is.oms.Order;
import com.integral.is.oms.OrderBookCache;
import com.integral.is.oms.OrderConstants;
import com.integral.is.oms.OrderExecutionDetails;
import com.integral.is.oms.OrderFactory;
import com.integral.is.oms.calculator.OrderCalculatorFactory;
import com.integral.is.oms.scheduler.OrderExpirationExecutorC;
import com.integral.is.oms.scheduler.OrderScheduler;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.message.MessageEvent;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.MatchEvent;
import com.integral.model.dealing.OrderMatchRequest;
import com.integral.model.dealing.RoutingInstruction;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.oms.OrderLiftRequest;
import com.integral.oms.spaces.fx.esp.FXOrder;
import com.integral.oms.spaces.fx.esp.cache.FXOrderCache;
import com.integral.staging.oms.OMSUtil;
import com.integral.staging.oms.passorder.EMSOMSRouter;
import com.integral.v4ems.util.EMSServerUtil;

import java.util.Collection;

/**
 * Created with IntelliJ IDEA.
 * User: anatarajan
 * Date: 7/18/14
 * Time: 10:37 AM
 * To change this template use File | Settings | File Templates.
 */
public class OrderRouterC implements OrderRouter {

    private static Log log = LogFactory.getLog(OrderRouterC.class);
    private final OrderExpirationExecutorC expirationExecutorC = new OrderExpirationExecutorC();
    private static OrderBookCache orderBookCache = OrderFactory.getOrderBookCache();
    private static ISMBean isMbean = ISFactory.getInstance().getISMBean();

    @Override
    public WorkflowMessage route(SingleLegOrder orderRequest, boolean isPartOfBatch, boolean reloadOMS) {
        // initial call only should create a new order. Subsequent routing should deal with the same order.
        Order order;
        try {
            order = createOrder(orderRequest);
        } catch (Exception e) {
            log.error("route() : Order routing/submission failed", e);
            WorkflowMessage wfm = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, MessageEvent.CREATE, ISConstantsC.MSG_TOPIC_REQUEST);
            wfm.setStatus(MessageStatus.FAILURE);
            return wfm;
        }
        boolean isDirectedOrder = order.getEntityDescriptor().isDirectedOrder();
        boolean isSwitchOrder = order.getEntityDescriptor().isLitSwitchOrder() || order.getEntityDescriptor().isDarkSwitchOrder();
        boolean isOMSOrder = isOMSOrder(order, orderRequest);
        if (reloadOMS && orderRequest.getActiveMatchRequests().size() == 0) {
            order.setActiveInOB(true);
        } else {
            order.setActiveInOB(!isDirectedOrder && !isOMSOrder);
        }
        boolean isWarmupOrder = ISUtilImpl.getInstance().isWarmUpObject(orderRequest);
        if (isOMSOrder && !reloadOMS) {
            ErrorMessage errorMsg = EMSOMSRouter.routeToOMS(order);
            if (!OMSUtil.isSuccess(errorMsg)) {
                log.warn("route() : ORDER_SUBMIT_FAILURE in OMS workflow.submitOrder with Code->" + errorMsg.getErrorCode() + " for " + orderRequest.getExecutionInstructions().getRoutingInstruction().getTradingVenue());
                WorkflowMessage wfm = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, MessageEvent.CREATE, ISConstantsC.MSG_TOPIC_REQUEST);
                wfm.setStatus(MessageStatus.FAILURE);
                wfm.addError(errorMsg);
                OrderScheduler.getInstance().cancel(order);
                OrderFactory.getOrderBookCache().cancelOrder(order, false);
                return wfm;
            }
        } else if (!isSwitchOrder && isDirectedOrder && !orderRequest.getOrderRequestAttributes().isReloadedOrderRequest()) {
            boolean isLPCross = ((orderRequest.getExecutionFlags() & ExecutionFlags.ALLOW_QUOTE_CROSS) == ExecutionFlags.ALLOW_QUOTE_CROSS);
            if (isLPCross) {
                if (!isWarmupOrder) {
                    OrderScheduler.getInstance().scheduledTradingVenueExpiryTask(order, orderRequest.getExecutionInstructions().getRoutingInstruction().getTimeIntervalAtTradingVenue());
                }
            }
            ErrorMessage errorMessage = OrderCalculatorFactory.getInstance().getDirectedOrdersWorkflowHandler().submitOrder(order);
            if (errorMessage != null) {
                // reject the order.
                log.warn("route() : ORDER_SUBMIT_FAILURE in DirectedOrdersWorkflowHandler.submitOrder with Code->" + errorMessage.getErrorCode() + " for " + orderRequest.getExecutionInstructions().getRoutingInstruction().getTradingVenue());
                WorkflowMessage wfm = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, MessageEvent.CREATE, ISConstantsC.MSG_TOPIC_REQUEST);
                wfm.setStatus(MessageStatus.FAILURE);
                wfm.addError(errorMessage);
                OrderScheduler.getInstance().cancel(order);
                OrderFactory.getOrderBookCache().cancelOrder(order, false);
                return wfm;
            }
            if (!isWarmupOrder) {
                OrderScheduler.getInstance().scheduleTradingVenueOrderSubmitStatusQuery(order, OrderConfiguration.getInstance().getDirectedOrderAlertTimeForSubmit());
            }
        }
        return OrderCalculatorFactory.getInstance().getSubmitOrderWorkflowCalculator().submitOrder(orderRequest, order, isPartOfBatch);
    }


    private boolean isOMSOrder(Order order, SingleLegOrder orderRequest) {
        if (order.getEntityDescriptor().isOMSOrder()) {
            return true;
        }
        return false;
    }


    @Override
    public void reRoute(SingleLegOrder orderRequest, Order order) {
        //TODO: for subsequent routing if applicable (future)
    }

    @Override
    public RoutingDecision routeToClobNow(Order order, OrderExecutionDetails oed) {
        //Protection against sending multiple venue amend requests triggered by various events.
        if (order.isVenueAmendInProgress()) {
            log.info(new StringBuilder(150).append("Dropping this Venue_Amend_Trigger as Previous_Amend is in progress - ").append(order.getOrderId())
                    .append(", Further Venue_Amend will be triggered (if applicable) by future Non-Marketability/Rate-Update/LP_Rejection events.").toString());
            return RoutingDecision.FT;
        }

        RoutingDecision result = RoutingDecision.TT;
        SingleLegOrder orderRequest = (SingleLegOrder) order.getEntityDescriptor().getEntity();
        if (!(orderRequest.isCancelReceived() || orderRequest.isMassCancelReceived())) {
            ErrorMessage errorMessage = null;

            if (orderRequest.getRoutedMatchRequest() == null ||
                    ((orderRequest.isV4Initiated() || EMSServerUtil.isEMSEnabledOrg(order.getEntityDescriptor().getOrganization()))
                            && order.getEntityDescriptor().isTWAPOrder() && !(OrderConfiguration.getInstance().isSmartAdjustingTWAPEnabled(order.getEntityDescriptor().getOrganization())))) {
                if (order.setVenueAmendInProgress(true)) {
                    order.getNoOfTimesRouted().incrementAndGet();
                    errorMessage = OrderCalculatorFactory.getInstance().getDirectedOrdersWorkflowHandler().submitRequestToVenuePostEMSMatch(orderRequest, oed);
                } else {
                    log.info(new StringBuilder(50).append(order.getOrderId()).append(" Unable to set Venue_Amend_Flag to true. Not Sending NewRequest to Venue.").toString());
                    return RoutingDecision.FT;
                }
            } else if (orderRequest.getRoutedMatchRequest().getDirectedOrderInfo().getExternalReferenceId() != null) {
                if (order.setVenueAmendInProgress(true)) {
                    OrderMatchRequest routedMatchRequest = orderRequest.getRoutedMatchRequest();
                    MatchEvent.MatchEventLeg routedMatchEventLeg = routedMatchRequest.getMatchEventLeg();
                    double marketableAmount = oed.getMatchableAmount();
                    boolean isSuccess = order.fill(marketableAmount, routedMatchEventLeg.getMatchRate(), null, routedMatchRequest.get_id(), routedMatchRequest.getMatchingVenue().getShortName());
                    if (isSuccess) {
                        order.getNoOfTimesRouted().incrementAndGet();
                        boolean amendSuccess = amendVenueRoutedRequest(order, marketableAmount);
                        if (!amendSuccess) {
                            boolean amountUndoSuccess = order.fill(-marketableAmount, routedMatchEventLeg.getMatchRate(), null, routedMatchRequest.get_id(), routedMatchRequest.getMatchingVenue().getShortName());
                            if (!amountUndoSuccess) {
                                log.info("Order undoFill unsuccessful on Venue_Amend_Event for OrderId: " + order.getOrderId());
                            }
                            errorMessage = MessageFactory.newErrorMessage();
                            errorMessage.setStatus(MessageStatus.FAILURE);
                            errorMessage.setCode(ISCommonConstants.UNABLE_TO_SEND_VENUE_AMEND_REQUEST);
                        }
                    } else {
                        log.info("Order fill unsuccessful on Venue_Amend_Event for OrderId: " + order.getOrderId());
                        errorMessage = MessageFactory.newErrorMessage();
                        errorMessage.setStatus(MessageStatus.FAILURE);
                        errorMessage.setCode(ISCommonConstants.UNABLE_TO_FILL_ORDER_ON_VENUE_AMEND_REQUEST);
                    }
                } else {
                    log.info(new StringBuilder(50).append(order.getOrderId()).append(" Unable to set Venue_Amend_Flag to true. Not Sending AmendRequest to Venue.").toString());
                    return RoutingDecision.FT;
                }
            } else {
                result = RoutingDecision.FT;
            }
            if (errorMessage != null) {
                // Unable to send request to Venue. Further matching of order will occur based on incoming rates.
                String errorCode = errorMessage.getErrorCode();
                StringBuilder sb = new StringBuilder(75).append("Unable to send request to Venue.").append(" ")
                        .append("OrderId=").append(order.getOrderId()).append(" ").append("ErrorCode=").append(errorCode).append(" ");
                boolean isSuccess = order.setVenueAmendInProgress(false);
                sb.append("VenueAmendFlagChange=").append(isSuccess);
                log.info(sb.toString());
                if (ISCommonConstants.REQUEST_VALIDATION_VENUE_IS_INACTIVE.equals(errorCode) ||
                        ISCommonConstants.REQUEST_VALIDATION_REX_CURRENCYPAIR_NOT_SUPPORTED.equals(errorCode) ||
                        ISCommonConstants.REQUEST_VALIDATION_TRADINGVENUE_INVALID.equals(errorCode)) {
                    result = RoutingDecision.FF;
                } else if (ISCommonConstants.UNABLE_TO_SEND_VENUE_AMEND_REQUEST.equals(errorCode) ||
                        ISCommonConstants.UNABLE_TO_FILL_ORDER_ON_VENUE_AMEND_REQUEST.equals(errorCode)) {
                    result = RoutingDecision.FT;
                }
            }
        }
        return result;
    }

    /**
     * User initiated cancel.
     *
     * @param wfm
     */
    @Override
    public WorkflowMessage cancel(WorkflowMessage wfm) {
        Object obj = wfm.getObject();
        if (obj == null || !SingleLegOrder.class.equals(obj.getClass())) {
            WorkflowMessage respMsg = OrderCalculatorFactory.getInstance().getCancelOrderWorkflowCalculator().processWithdrawRequest(wfm);
            return respMsg;
        } else {
            return doCancel(wfm);
        }
    }

    public WorkflowMessage cancelOnStartup(WorkflowMessage wfMsg) {
        SingleLegOrder singleLegOrder = (SingleLegOrder) wfMsg.getObject();
        if (singleLegOrder.isDirectedOrder()) {
            ErrorMessage errorMessage = OrderCalculatorFactory.getInstance().getDirectedOrdersWorkflowHandler().cancelMatchRequest(singleLegOrder);
            if (errorMessage != null) {
                if (errorMessage.getStatus() == MessageStatus.FAILURE) {
                    // reject the order.
                    log.error("doCancelOnStartup() : ORDER_CANCEL FAILURE in DirectedOrdersWorkflowHandler with ErrorCode->" + errorMessage.getErrorCode());
                    WorkflowMessage replyWfm = ISUtilImpl.getInstance().createWorkflowMessage(singleLegOrder, MessageEvent.WITHDRAW, ISConstantsC.MSG_TOPIC_REQUEST);
                    replyWfm.setStatus(MessageStatus.FAILURE);
                    replyWfm.addError(errorMessage);
                    wfMsg.setReplyMessage(replyWfm);
                    return wfMsg;
                }
            } else {
                return ServiceFactory.getISRequestService().process(wfMsg);
            }
            wfMsg.setReplyMessage(OrderCalculatorFactory.getInstance().getCancelOrderWorkflowCalculator().requestPreWithdraw(singleLegOrder, wfMsg));
            return wfMsg;
        } else {
            WorkflowMessage responseMessage = ServiceFactory.getISRequestService().process(wfMsg);
            return responseMessage;
        }
    }

    private WorkflowMessage doCancel(WorkflowMessage wfMsg) {
        SingleLegOrder orderRequest = (SingleLegOrder) wfMsg.getObject();
        log.info("doCancel() : Sending Order cancel request for OrderId->" + orderRequest.get_id());
        FXOrder order = FXOrderCache.get(orderRequest.get_id());
        if (order != null) {
            if (System.currentTimeMillis() < orderRequest.getOrderRequestAttributes().getMinRestingTimeInRiskNet()) {
                log.info("doCancel() : Order Cancel/Replace request came before min resting time in RiskNet elapsed for OrderId->" + orderRequest.get_id());
                WorkflowMessage replyWfm = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, MessageEvent.WITHDRAW, ISConstantsC.MSG_TOPIC_REQUEST);
                replyWfm.setStatus(MessageStatus.FAILURE);
                ISUtilImpl.getInstance().addError(replyWfm, ISCommonConstants.REQUEST_VALIDATION_DIRECTED_ORDER_CANCEL_OR_REPLACE_NOT_ALLOWED_BEFORE_MIN_RESTING_TIME);
                wfMsg.setReplyMessage(replyWfm);
                return wfMsg;
            }
            if (orderRequest.getActiveMatchRequests().size() > 0) {
                if (!orderRequest.getOrderRequestAttributes().isRouterCancellationDone()) { // router cancellation already done - not resending cancel request to TV.
                    order.setActiveInOB(false); // no further matching occurs in OB
                    orderRequest.getOrderRequestAttributes().setRouterCancellationDone(true);
                    order.setMarkedForCancel(true);  // marking for internal cancellation
                    ErrorMessage errorMessage = null;
                    if (order.getEntityDescriptor().isOMSOrder()) {
                        errorMessage = EMSOMSRouter.withdrawFromOMS(order, false, false);
                    } else {
                        errorMessage = OrderCalculatorFactory.getInstance().getDirectedOrdersWorkflowHandler().cancelOrder(order);
                    }
                    wfMsg.setReplyMessage(OrderCalculatorFactory.getInstance().getCancelOrderWorkflowCalculator().requestPreWithdraw(orderRequest, wfMsg));
                    if (errorMessage != null) {
                        if (errorMessage.getStatus() == MessageStatus.FAILURE) {
                            log.warn("doCancel() : ORDER_CANCEL FAILURE in DirectedOrdersWorkflowHandler.cancel with ErrorCode->" + errorMessage.getErrorCode());
                            WorkflowMessage replyWfm = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, MessageEvent.WITHDRAW, ISConstantsC.MSG_TOPIC_REQUEST);
                            replyWfm.setParameterValue(OrderConstants.Key_HoldOrderCancelReplaceRequest, Boolean.TRUE);
                            wfMsg.setReplyMessage(replyWfm);
                            return wfMsg;
                        }
                    }
                } else {
                    wfMsg.setReplyMessage(OrderCalculatorFactory.getInstance().getCancelOrderWorkflowCalculator().requestPreWithdraw(orderRequest, wfMsg));
                }
                return wfMsg;
            } else {
                if (order.getEntityDescriptor().isOMSOrder() && wfMsg.getParameterValue("CANCELLED_BY_DESK") == null) {
                    orderRequest.setCancelReceived(false);
                    EMSOMSRouter.enableMatch(order, false, null);
                    orderRequest.setCancelReceived(true);
                    order.setActiveInOB(false); // no further matching occurs in OB
                    orderRequest.getOrderRequestAttributes().setRouterCancellationDone(true);
                    order.setMarkedForCancel(true);  // marking for internal cancellation
                    ErrorMessage errorMessage = EMSOMSRouter.withdrawFromOMS(order, false, true);
                    wfMsg.setReplyMessage(OrderCalculatorFactory.getInstance().getCancelOrderWorkflowCalculator().requestPreWithdraw(orderRequest, wfMsg));
                    if (errorMessage != null) {
                        if (errorMessage.getStatus() == MessageStatus.FAILURE) {
                            log.warn("doCancel() : ORDER_CANCEL FAILURE in DirectedOrdersWorkflowHandler.cancel with ErrorCode->" + errorMessage.getErrorCode());
                            WorkflowMessage replyWfm = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, MessageEvent.WITHDRAW, ISConstantsC.MSG_TOPIC_REQUEST);
                            replyWfm.setParameterValue(OrderConstants.Key_HoldOrderCancelReplaceRequest, Boolean.TRUE);
                            wfMsg.setReplyMessage(replyWfm);
                            return wfMsg;
                        }
                    }
                } else {
                    return OrderCalculatorFactory.getInstance().getCancelOrderWorkflowCalculator().processWithdrawRequest(wfMsg);
                }
                return wfMsg;
            }
        } else {
            log.warn("doCancel() : OA Order not found in Order Book. OrderId " + orderRequest.get_id());
            WorkflowMessage replyWfm = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, MessageEvent.WITHDRAW, ISConstantsC.MSG_TOPIC_REQUEST);
            replyWfm.setStatus(MessageStatus.FAILURE);
            ISUtilImpl.getInstance().addError(replyWfm, ISCommonConstants.ORDER_CANCEL_UNKNOWN_ORDER);
            wfMsg.setReplyMessage(replyWfm);
            return wfMsg;
        }
    }

    public boolean cancelVenueRoutedRequestSynchronous(WorkflowMessage wfMsg) {
        Object obj = wfMsg.getObject();
        if (obj == null || !SingleLegOrder.class.equals(obj.getClass())) {
            return false;
        }
        SingleLegOrder orderRequest = (SingleLegOrder) wfMsg.getObject();
        if (!isMbean.isSuspendEnabledForVenueRequests(orderRequest.getOrgShortName()))
            return true;
        return cancelVenueRoutedRequestSynchronous(orderRequest);
    }

    public boolean cancelVenueRoutedRequestSynchronous(SingleLegOrder orderRequest) {
        if (!ISUtilImpl.getInstance().requestRoutedToVenue(orderRequest))
            return true;
        FXOrder order = FXOrderCache.get(orderRequest.get_id());
        if (null == order) {
            log.error("cancelVenueRoutedRequestSynchronous failed. order not found in cache");
            return false;
        }
        order.setHoldVenueRequestRouting(true);
        if (!cancelVenueRoutedRequest(order)) {
            log.info("cancelVenueRoutedRequestSynchronous failed");
            order.setHoldVenueRequestRouting(false);
            return false;
        }
        int retryCount = isMbean.cancelVenueRequestSynchronousRetryCount();
        for (int i = 0; i < retryCount; i++) {
            try {
                int state = order.getState();
                if (state != order.ORDER_STATE_INITIAL) {
                    Thread.sleep(1);
                } else {
                    return true;
                }
            } catch (Exception ex) {
                log.error("cancelVenueRoutedRequestSynchronous: error while waiting for cancel match request response", ex);
                order.setHoldVenueRequestRouting(false);
                return false;
            }
        }
        return false;
    }


    /**
     * Trigger Cancel of directed order.
     *
     * @param order
     */
    @Override
    public void expireVenueOrder(Order order) {
        OrderCalculatorFactory.getInstance().getDirectedOrdersWorkflowHandler().expireVenueOrder(order);
    }

    @Override
    public void expire(Order order) {
        Object obj = order.getEntityDescriptor().getEntity();
        if (obj instanceof OrderLiftRequest) {
            expirationExecutorC.expireOrder(order);
        } else {
            SingleLegOrder singleLegOrder = (SingleLegOrder) obj;
            singleLegOrder.getOrderRequestAttributes().setRouterExpirationDone(true);
            if (singleLegOrder.getActiveMatchRequests().size() > 0) {
                order.setActiveInOB(false); // no further matching occurs in OB
                ErrorMessage errorMessage = null;
                if (order.getEntityDescriptor().isOMSOrder()) {
                    errorMessage = EMSOMSRouter.withdrawFromOMS(order, true, false);
                } else {
                    errorMessage = OrderCalculatorFactory.getInstance().getDirectedOrdersWorkflowHandler().expireVenueOrder(order);
                }
                if (errorMessage != null) {
                    if (errorMessage.getStatus() == MessageStatus.FAILURE) {
                        log.error("route() : ORDER_EXPIRE FAILURE in DirectedOrdersWorkflowHandler.submitOrder with ErrorCode->" + errorMessage.getErrorCode());
                    }
                } else {
                    expirationExecutorC.expireOrder(order);
                }
            } else {
                expirationExecutorC.expireOrder(order);
            }
        }
    }

    private static final OrderConfigurationMBean _orderConfig = OrderConfiguration.getInstance();

    @Override
    public void handleOrderSubmittedResponse(OrderMatchRequest matchRequest) {
        SingleLegOrder orderRequest = matchRequest.getOrderRequest();
        OrderScheduler.getInstance().cancelTradingVenueOrderSubmitStatusTask(orderRequest.get_id());
        if (matchRequest.isUnMatchedRoutedRequest()) {
            FXOrder order = FXOrderCache.get(orderRequest.get_id());
            if (order != null) {
                order.addVenueRoutedAmount(matchRequest.getMatchEventLeg().getMatchedAmount());
                order.setVenueAmendInProgress(false);
            }
        }
        if (orderRequest.getOrderRequestAttributes().isRouterCancellationDone()) {
            orderRequest.getOrderRequestAttributes().setRouterCancellationDone(false);
            WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, ISConstantsC.MSG_EVT_WITHDRAW, ISConstantsC.MSG_TOPIC_REQUEST);
            wfMsg.setObject(orderRequest);
            cancel(wfMsg);
        } else if (orderRequest.getOrderRequestAttributes().isRouterExpirationDone()) {
            FXOrder order = FXOrderCache.get(orderRequest.get_id()); // check for null order
            if (order != null) {
                boolean isFillAtMarket = order.getEntityDescriptor().isRoutingPermitted() && order.isFillAtMarketExecution() && !order.isExpiredForMatch() && order.isExpired();
                if (!isFillAtMarket)
                    expire(order);
            }
        }

        if (orderRequest.isExternalVenueDirectedOrder() && _orderConfig.ShowRejectReason(orderRequest.getOrganization().getShortName())) {
            OrderCalculatorFactory.getInstance().getOrderWorkflowCalculator().sendOrderSubmissionReportForExternalVenueDirectedOrder(orderRequest);
        }

    }

    public void status(Order order) {
        MessageLogger.getInstance().log(ISAlertMBean.DO_ALERT_EVENT_ACK_MISSING, "OrderRouter", "Ack not received from Venue", order.getOrderId());
    }

    @Override
    public void handleOrderSubmitFailedResponse(SingleLegOrder orderRequest) {
        OrderScheduler.getInstance().cancelTradingVenueOrderSubmitStatusTask(orderRequest.get_id());
        OrderScheduler.getInstance().cancelTradingVenueExpiryTask(orderRequest.get_id());
        if (orderRequest.isDirectedOrder()) {
            WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, ISConstantsC.MSG_EVT_WITHDRAW, ISConstantsC.MSG_TOPIC_REQUEST);
            OrderCalculatorFactory.getInstance().getCancelOrderWorkflowCalculator().processWithdrawRequest(wfMsg);

        } else {
            FXOrder order = FXOrderCache.get(orderRequest.get_id()); // check for null order
            handleOrderRequestWithdraw(orderRequest, order);
        }
    }

    @Override
    public void handleOrderRejected(SingleLegOrder orderRequest) {
        OrderScheduler.getInstance().cancelTradingVenueOrderSubmitStatusTask(orderRequest.get_id());
        OrderScheduler.getInstance().cancelTradingVenueExpiryTask(orderRequest.get_id());
        if (orderRequest.isDirectedOrder()) {
            WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, ISConstantsC.MSG_EVT_WITHDRAW, ISConstantsC.MSG_TOPIC_REQUEST);
            wfMsg.setParameterValue(ISCommonConstants.EVENT_ORDER_MATCH_REJECTED, true);
            OrderCalculatorFactory.getInstance().getCancelOrderWorkflowCalculator().processWithdrawRequest(wfMsg);
        } else {
            FXOrder order = FXOrderCache.get(orderRequest.get_id()); // check for null order
            handleOrderRequestWithdraw(orderRequest, order);
        }
    }

    @Override
    public void handleTerminalFillResponse(SingleLegOrder orderRequest) {
        handleRequestExpiryResponse(orderRequest);
    }

    @Override
    public void handleRequestExpiryResponse(SingleLegOrder orderRequest) {
        if (orderRequest.getOrderRequestAttributes().isCancellationOnStartup()) {
            WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, ISConstantsC.MSG_EVT_WITHDRAW, ISConstantsC.MSG_TOPIC_REQUEST);
            if (orderRequest.isDirectedOrder() && !orderRequest.isFixing()) {
                ServiceFactory.getISRequestService().process(wfMsg);
            } else {
                OrderCalculatorFactory.getInstance().getCancelOrderWorkflowCalculator().processWithdrawRequest(wfMsg);
            }
        } else {
            FXOrder order = FXOrderCache.get(orderRequest.get_id()); // check for null order
            if (order == null) {
                return;
            }
            OrderScheduler.getInstance().cancelTradingVenueExpiryTask(orderRequest.get_id());

            if (order.getEntityDescriptor().isDirectedOrder()) {


                if (orderRequest.getOrderRequestAttributes().isRouterCancellationDone() || orderRequest.getOrderRequestAttributes().isDoCancellationPendingCancel()) {
                    WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, ISConstantsC.MSG_EVT_WITHDRAW, ISConstantsC.MSG_TOPIC_REQUEST);
                    OrderCalculatorFactory.getInstance().getCancelOrderWorkflowCalculator().requestPostWithDraw(orderRequest, wfMsg);
                } else if (orderRequest.getOrderRequestAttributes().isRouterExpirationDone()) {
                    expirationExecutorC.expireOrder(order);
                } else if (orderRequest.getExecutionInstructions().getRoutingInstruction().getActionAtExpiry() == RoutingInstruction.ActionAtExpiry.GO_TO_MARKET) {
                    OrderCalculatorFactory.getInstance().getOrderWorkflowCalculator().updateOrderOnMatchRequestCancel(orderRequest, order);
                } else {
                    expirationExecutorC.expireOrder(order);
                }
            } else {
                handleOrderRequestWithdraw(orderRequest, order);
            }
        }
    }


    public void handleRoutedRequestAmendSuccess(OrderMatchRequest matchRequest) {
        SingleLegOrder singleLegOrder = matchRequest.getOrderRequest();
        FXOrder order = FXOrderCache.get(singleLegOrder.get_id()); // check for null order
        if (order == null) {
            return;
        }

        // OA Handling for order

        long amendCountId = matchRequest.getAmendCount();
        MatchEvent.MatchEventLeg matchEventLeg = matchRequest.getMatchEventLeg();
        double newVenueRoutedAmount = matchEventLeg.getAmendAmount();
        double previousVenueRoutedAmount = matchEventLeg.getFinalAcceptanceAmount();
        double deltaUnFilledAmount = newVenueRoutedAmount - previousVenueRoutedAmount;
        order.addVenueRoutedAmount(deltaUnFilledAmount);
        if (matchRequest.isExternalVenueDirectedOrder()) {
            boolean isSuccess = order.setVenueAmendInProgress(false);

            StringBuilder sb = new StringBuilder(100).append("handleRoutedRequestAmendSuccess() ").append(matchRequest.get_id()).append(' ');
            sb.append(matchRequest.get_id()).append(" AMId=").append(amendCountId).append(" NewRtdAmt=").append(newVenueRoutedAmount).append(", PrevRtdAmt=").append(previousVenueRoutedAmount).append("FlagChangeSuccess=").append(isSuccess);;
            log.info(sb.toString());
        } else {
            boolean isDeltaLessThanZero = deltaUnFilledAmount < 0;
            StringBuilder sb = new StringBuilder(100).append("handleRoutedRequestAmendSuccess() ").append(matchRequest.get_id()).append(' ');
            sb.append(matchRequest.get_id()).append(" AMId=").append(amendCountId).append(" NewRtdAmt=").append(newVenueRoutedAmount).append(", PrevRtdAmt=").append(previousVenueRoutedAmount);
            sb.append(", Delta=").append(deltaUnFilledAmount);

            if (isDeltaLessThanZero) {
                boolean isSuccess = order.fill(deltaUnFilledAmount, matchEventLeg.getMatchRate(), null, matchRequest.get_id(), matchRequest.getMatchingVenue().getShortName());
                sb.append(", Fill/UndoFill Success=").append(isSuccess);
            }

            boolean isSuccess = order.setVenueAmendInProgress(false);
            sb.append("FlagChangeSuccess=").append(isSuccess);
            log.info(sb.toString());
            if (isDeltaLessThanZero) { // if amount is positive, it will most likely not result in a successful match.
                triggerMatch(order, OrderConstants.MATCH_UPON_VENUE_AMEND_SUCCESS, false);
            }
        }


    }

    public void handleRoutedRequestAmendFailure(OrderMatchRequest matchRequest, WorkflowMessage msg) {
        SingleLegOrder singleLegOrder = matchRequest.getOrderRequest();
        FXOrder order = FXOrderCache.get(singleLegOrder.get_id()); // check for nck for null order
        if (order == null) {
            return;
        }
        Integer cancelClassification = (Integer) msg.getParameterValue(ISCommonConstants.CANCEL_CLASSIFICATION);
        // OA Handling for order
        if (cancelClassification != null) {
            switch (cancelClassification) {
                case ISCommonConstants.ROUTED_ORDER_REJECT_CLASSIFICATION_ERROR:
                    order.setRouteOrderRejectedOnError(true);
                    break;
                case ISCommonConstants.ROUTED_ORDER_REJECT_CLASSIFICATION_ORDER_ATTRIBUTES:
                    order.setRoutedOrderRejectedOnOrderAttributes(true);
                    break;

                case ISCommonConstants.ROUTED_ORDER_REJECT_CLASSIFICATION_VENUE_UNAVAILABILITY:
                    order.setRouteOrderRejectedOnVenueAvailability(true);
                    break;
            }
        }

        // OA Handling for order
        long amendCountId = matchRequest.getAmendCount();
        MatchEvent.MatchEventLeg matchEventLeg = matchRequest.getMatchEventLeg();
        double previousVenueRoutedAmount = matchEventLeg.getFinalAcceptanceAmount();
        double newVenueRoutedAmount = matchEventLeg.getLastAmendAmount();

        double deltaUnFilledAmount = previousVenueRoutedAmount - newVenueRoutedAmount;
        StringBuilder sb = new StringBuilder(100).append("handleRoutedRequestAmendFailure() ").append(singleLegOrder.get_id()).append(' ');
        sb.append(matchRequest.get_id()).append(" AMId=").append(amendCountId).append(" NewRtdAmt=").append(newVenueRoutedAmount).append(", PrevRtdAmt=").append(previousVenueRoutedAmount);
        sb.append(", Delta=").append(deltaUnFilledAmount);

        if (matchRequest.isExternalVenueDirectedOrder()) {
            order.setVenueAmendInProgress(false);
        }else{
            boolean isDeltaLessThanZero = deltaUnFilledAmount < 0;
            if (isDeltaLessThanZero) {
                boolean isSuccess = order.fill(deltaUnFilledAmount, matchEventLeg.getMatchRate(), null, matchRequest.get_id(), matchRequest.getMatchingVenue().getShortName());
                sb.append(", Fill/UndoFill Success=").append(isSuccess);
            }
            log.info(sb.toString());


            if (order.setVenueAmendInProgress(false)) {
                triggerMatch(order, OrderConstants.MATCH_UPON_VENUE_AMEND_FAILURE, false);
            } else {
                log.info(new StringBuilder(50).append(order.getOrderId()).append(" - Unable to set Venue_Amend_In_Progress to False. Not triggering Match").toString());
            }
        }

    }

    @Override
    public void handleCancelRejectResponse(SingleLegOrder orderRequest, boolean shouldAlert) {
        log.error("handleCancelRejectResponse() - Cancel request of Directed order-" + orderRequest.get_id() + " was Rejected.");
        MessageLogger.getInstance().log(ISAlertMBean.DO_ALERT_EVENT_CANCEL_RESPONSE_UNKNOWN, "OrderRouter", "Order Cancel Failed at Venue", orderRequest.get_id());
    }


    public boolean amendVenueRoutedRequest(Order order, double amount) {
        SingleLegOrder orderRequest = (SingleLegOrder) order.getEntityDescriptor().getEntity();
        OrderMatchRequest omRequest = orderRequest.getRoutedMatchRequest();
        if (omRequest == null) {
            return false;
        }

        double prevRoutedAmount = omRequest.getMatchEventLeg().getAmendAmount();
        double newAmendAmount = prevRoutedAmount + amount;

        omRequest.getMatchEventLeg().setLastAmendAmount(newAmendAmount);

        boolean result = OrderCalculatorFactory.getInstance().getDirectedOrdersWorkflowHandler().amendMatchRequest(omRequest,
                newAmendAmount, omRequest.getMatchEventLeg().getMatchedSpotRate());
        long amendCountId = omRequest.getAmendCount();

        StringBuilder sb = new StringBuilder(100).append("amendVenueRoutedRequest() ").append(orderRequest.get_id()).append(' ');
        sb.append(omRequest.get_id()).append(" AMId=").append(amendCountId).append(" NewRtdAmt=").append(newAmendAmount);
        sb.append(", PrevRtdAmt=").append(prevRoutedAmount).append(", Delta=").append(amount);
        log.info(sb.toString());

        return result;
    }

    public boolean cancelVenueRoutedRequest(Order order) {
        SingleLegOrder orderRequest = (SingleLegOrder) order.getEntityDescriptor().getEntity();
        return OrderCalculatorFactory.getInstance().getDirectedOrdersWorkflowHandler().cancelMatchRequest(orderRequest.getRoutedMatchRequest());
    }

    @Override
    public String amend(SingleLegOrder orderRequest, WorkflowMessage wfm) {
        return null; // TODO: to be implemented in Phase II
    }


    @Override
    public WorkflowMessage amendExternalVenueDirectedOrder(SingleLegOrder orderRequest, WorkflowMessage wfm) {
        WorkflowMessage wfmNew = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, wfm.getEvent(), wfm.getTopic());
        Collection<OrderMatchRequest> activeMatchRequests = orderRequest.getActiveMatchRequests();

        if (activeMatchRequests == null || activeMatchRequests.size() == 0) {
            wfmNew.setStatus(MessageStatus.FAILURE);
            wfm.addError("matchrequest is null");
            return wfm;
        }
        OrderMatchRequest omRequest = activeMatchRequests.iterator().next();
        if (omRequest == null) {
            wfmNew.setStatus(MessageStatus.FAILURE);
            wfm.addError("matchrequest is null");
            return wfm;
        }
        double prevRoutedAmount = omRequest.getMatchEventLeg().getAmendAmount();
        Double newAmendAmount = (Double) wfm.getParameterValue(ISCommonConstants.WF_PARAM_NEW_ORDER_AMOUNT);
        Double newSpotRate = (Double) wfm.getParameterValue(ISCommonConstants.WF_PARAM_NEW_ORDER_RATE);
        String venueTargetStrategyParams = (String) wfm.getParameterValue(ISCommonConstants.VENUE_TARGET_STRATEGY_PARAMS);

        // omRequest.getMatchEventLeg().setLastAmendAmount(newAmendAmount);

        ErrorMessage errorMessage = OrderCalculatorFactory.getInstance().getDirectedOrdersWorkflowHandler().amendExternalVenueDirectedOrder(orderRequest, omRequest, newAmendAmount != null && newAmendAmount > 0 ? newAmendAmount : 0, newSpotRate != null && newSpotRate > 0 ? newSpotRate : 0, venueTargetStrategyParams);
        // long amendCountId = omRequest.getAmendCount();

        StringBuilder sb = new StringBuilder(100).append("amendVenueRoutedRequest() ").append(orderRequest.get_id()).append(' ');
        sb.append(omRequest.get_id()).append(" NewRtdAmt=").append(newAmendAmount);
        sb.append(", PrevRtdAmt=").append(prevRoutedAmount).append(", newRate=").append(newSpotRate).append(", previousRate=").append(omRequest.getMatchEventLeg().getMatchedSpotRate());
        log.info(sb.toString());

        if (errorMessage != null) {
            wfmNew.setStatus(MessageStatus.FAILURE);
            wfm.addError(errorMessage);
        } else {
            wfmNew.setStatus(MessageStatus.SUCCESS);
        }

        return wfm;
    }

    private Order createOrder(SingleLegOrder orderRequest) throws Exception {
        return OrderFactory.newOrder(orderRequest);
    }

    private void handleOrderRequestWithdraw(SingleLegOrder orderRequest, FXOrder order) {
        orderRequest.getCancelReplaceLock().lock();
        try {
            if (orderRequest.getOrderRequestAttributes().isDoCancellationPendingCancel()
                    || order.isMarkedForCancel()
                    || orderRequest.isCancelReceived()
                    || orderRequest.isMassCancelReceived()) {
                WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(orderRequest, ISConstantsC.MSG_EVT_WITHDRAW, ISConstantsC.MSG_TOPIC_REQUEST);
                OrderCalculatorFactory.getInstance().getCancelOrderWorkflowCalculator().requestPostWithDraw(orderRequest, wfMsg);
            } else if (orderRequest.getOrderRequestAttributes().isRouterExpirationDone() || ((order.getEntityDescriptor().isIOC() || order.getEntityDescriptor().isFOK()) && !order.getEntityDescriptor().isExpiryEnabled())) {
                expirationExecutorC.expireOrder(order);
            } else {
                OrderCalculatorFactory.getInstance().getOrderWorkflowCalculator().updateOrderOnMatchRequestCancel(orderRequest, order);
            }
        } finally {
            orderRequest.getCancelReplaceLock().unlock();
        }
    }

    private void triggerMatch(Order order, String initiatedBy, boolean matchLiftOrders) {
        orderBookCache.synchronousMatchOrder(order, initiatedBy, matchLiftOrders);
    }

}
