package com.integral.routing;

/**
 * Created by an<PERSON><PERSON><PERSON> on 1/12/16.
 */
public class AmendVenueRoutedRequestDecision extends RoutingDecision {

    public static final AmendVenueRoutedRequestDecision TT_ZERO = new AmendVenueRoutedRequestDecision(true, true, 0d);
    public static final AmendVenueRoutedRequestDecision FF_ZERO = new AmendVenueRoutedRequestDecision(false, false, 0d);
    public static final AmendVenueRoutedRequestDecision FT_ZERO = new AmendVenueRoutedRequestDecision(false, true, 0d);

    private double amount;

    public AmendVenueRoutedRequestDecision(boolean result, boolean furtherRoutingDecision, double amount) {
        super(result, furtherRoutingDecision);
        this.amount = amount;
    }

    public double getAmount() {
        return amount;
    }

}
