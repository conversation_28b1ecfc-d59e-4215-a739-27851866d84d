package com.integral.marketpulse;

/**
 *  {
 *     "broker": "SUCD",
 *     "cp": "EUR/USD",
 *     "vf": 4.9,
 *     "time": 1695668053869,
 *     "pMode": 1 (0=NA, 1=Mid, 2-Bid-Offer)
 *   }
 */
public class VolatilityMetric {
    private String broker;
    private String cp;
    private double vf;
    private long time;
    private int pMode;

    public String getBroker() {
        return broker;
    }

    public void setBroker(String broker) {
        this.broker = broker;
    }

    public String getCp() {
        return cp;
    }

    public void setCp(String cp) {
        this.cp = cp;
    }

    public double getVf() {
        return vf;
    }

    public void setVf(double vf) {
        this.vf = vf;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public int getpMode() {
        return pMode;
    }

    public void setpMode(int pMode) {
        this.pMode = pMode;
    }

    @Override
    public String toString() {
        return "{" +
                "broker='" + broker + '\'' +
                ", cp='" + cp + '\'' +
                ", vf=" + vf +
                ", time=" + time +
                ", pMode=" + pMode +
                '}';
    }
}
