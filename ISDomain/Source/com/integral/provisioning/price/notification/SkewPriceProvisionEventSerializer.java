package com.integral.provisioning.price.notification;

import java.util.HashMap;
import java.util.Map;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.provisioning.price.skew.notification.SkewPriceProvisionEvent;
import com.integral.util.StringUtilC;

/**
 * <AUTHOR>
 *
 */
public class SkewPriceProvisionEventSerializer implements PriceProvisionEventSerializer
{
	protected Log log = LogFactory.getLog(this.getClass());
	
	/**
	 * Format:
	 * 		sN=1234567890,eT=SKEW,a=START,pL=key:value~key:value~......
	 * 
	 * Mandatory fields:
	 * 		eT, aT and pL
	 */
	@Override
	public String serialize(PriceProvisionEvent event) 
	{
		StringBuilder sb = new StringBuilder(256);
		sb.append("sN=").append(event.getSeqNo()).append(",");
		sb.append("eT=").append(event.getEventType().toString()).append(",");
		sb.append("aT=").append(event.getActionType().toString()).append(",");
		sb.append("pL=").append( mapToString(event.getPayload()) );
		return sb.toString();
	}

	@Override
	public PriceProvisionEvent deserialize(String str) 
	{
		if ( !StringUtilC.isNullOrEmpty(str) )
		{
			String[] tokens = str.split(",");
			
			String[] sN_tokens = tokens[0].split("=");
			if ( sN_tokens.length != 2 || !sN_tokens[0].equals("sN") )
			{
				throw new IllegalArgumentException("SkewPriceProvisionEvent.deserialize : Invalid format, key 'sN' (seqNo) missing in input string - " + str);
			}

			String[] eT_tokens = tokens[1].split("=");
			if ( eT_tokens.length != 2 || !eT_tokens[0].equals("eT") )
			{
				throw new IllegalArgumentException("SkewPriceProvisionEvent.deserialize : Invalid format, key 'eT' (eventType) missing in input string - " + str);
			}
			
			if( !PriceProvisionEvent.EventType.valueOf(eT_tokens[1]).equals(PriceProvisionEvent.EventType.SKEW) )
			{
				throw new IllegalArgumentException("SkewPriceProvisionEvent.deserialize : Invalid format, key 'eT' (eventType) has unsupported event type " +
						"in input string - " + str);
			}
			
			String[] aT_tokens = tokens[2].split("=");
			if ( aT_tokens.length != 2 || !aT_tokens[0].equals("aT") )
			{
				throw new IllegalArgumentException("SkewPriceProvisionEvent.deserialize : Invalid format, key 'aT' (actionType) missing in input string - " + str);
			}
			
			String[] pL_tokens = tokens[3].split("=");
			if ( pL_tokens.length != 2 || !pL_tokens[0].equals("pL") )
			{
				throw new IllegalArgumentException("SkewPriceProvisionEvent.deserialize : Invalid format, key 'pL' (payload) missing in input string - " + str);
			}
			HashMap<String, String> pL_map = parseToHashMap( pL_tokens[1] );
			if ( null == pL_map )
			{
				log.warn("SkewPriceProvisionEvent.deserialize : payload is null or empty in input string - " + str);
			}
			
			SkewPriceProvisionEvent skewEvent = new SkewPriceProvisionEvent();
			skewEvent.setSeqNo(Long.parseLong(sN_tokens[1]));
			skewEvent.setActionType(PriceProvisionEvent.ActionType.valueOf(aT_tokens[1]));
			skewEvent.setPayload(pL_map);
			
			return skewEvent;
		}
		return null;
	}
	
	/**
	 * Converts key:value~key:value~...... to HashMap<String, String>
	 * @param Flat string
	 * @return HashMap
	 */
	private static HashMap<String, String> parseToHashMap(String flatstr)
	{
		if ( StringUtilC.isNullOrEmpty(flatstr) )
		{
			return null;
		}
		
		String[] tokens = flatstr.split("~");
		HashMap<String, String> map = new HashMap<String, String>();
		
		for( String token : tokens )
		{
			String[] in_tokens = token.split(":");
			if ( in_tokens.length != 2 )
			{
				throw new IllegalArgumentException("SkewPriceProvisionEvent.parseToHashMap : Invalid payload format, 'pL' (payload) string - " + flatstr);
			}
			map.put(in_tokens[0], in_tokens[1]);
		}
		return map;
	}
	
	/**
	 * Converts HashMap<String, Object> to key:value~key:value~......
	 * @param HashMap<String, Object>
	 * @return Flat string
	 */
	private static String mapToString(HashMap<String, Object> map)
	{
		StringBuilder sb = new StringBuilder(256);
		for( Map.Entry<String, Object> key : map.entrySet() )
		{
			sb.append(key.getKey()).append(':').append(key.getValue().toString()).append('~');
		}
		return sb.toString();
	}

}
