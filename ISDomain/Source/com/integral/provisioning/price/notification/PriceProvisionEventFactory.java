package com.integral.provisioning.price.notification;

import com.integral.provisioning.price.notification.PriceProvisionEvent.EventType;
import com.integral.provisioning.price.skew.notification.SkewPriceProvisionEvent;

/**
 * <AUTHOR>
 *
 */
public class PriceProvisionEventFactory 
{
	private PriceProvisionEventFactory()
	{
		
	}
	
	private static class Holder
	{
		public static final PriceProvisionEventFactory INSTANCE = new PriceProvisionEventFactory();
	}
	
	public static PriceProvisionEventFactory getInstance()
	{
		return Holder.INSTANCE;
	}
	
	public PriceProvisionEvent getPriceProvisionEvent(EventType eventType)
	{
		switch( eventType )
		{
			case SKEW:
				return new SkewPriceProvisionEvent();
			default:
				throw new IllegalArgumentException( "PriceProvisionEventFactory.getPriceProvisionEvent : Unsupported eventType - " + eventType.toString() );
		}
	}

}
