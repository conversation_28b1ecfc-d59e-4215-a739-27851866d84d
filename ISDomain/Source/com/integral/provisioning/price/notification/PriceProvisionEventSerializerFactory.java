package com.integral.provisioning.price.notification;

import java.util.HashMap;

import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * <AUTHOR>
 *
 */
public class PriceProvisionEventSerializerFactory 
{
	protected Log log = LogFactory.getLog(this.getClass());
	private HashMap<String, PriceProvisionEventSerializer> serializerMap = new HashMap<String, PriceProvisionEventSerializer>();
	
	private static enum SerializerType
	{
		skew
	}
	
	private PriceProvisionEventSerializerFactory()
	{
		
	}
	
	private static class Holder
	{
		public static final PriceProvisionEventSerializerFactory INSTANCE = new PriceProvisionEventSerializerFactory();
	}
	
	public static PriceProvisionEventSerializerFactory getInstance()
	{
		return Holder.INSTANCE;
	}
	
	public PriceProvisionEventSerializer getPriceProvisionEventSerializer(String type)
	{
		PriceProvisionEventSerializer ppes = null;
		switch(SerializerType.valueOf(type))
		{
			case skew:
				ppes = serializerMap.get(type);
				if ( null == ppes )
				{
					ppes = new SkewPriceProvisionEventSerializer();
					serializerMap.put(type, ppes);
				}
				break;
			default:
				log.error("PriceProvisionEventSerializerFactory.getPriceProvisionEventSerializer : Unsupported serializer type - " + type);
				break;
		}
		return ppes;
	}

}
