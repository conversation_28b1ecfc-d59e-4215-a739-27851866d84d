package com.integral.provisioning.price.notification;

import java.util.HashMap;

/**
 * <AUTHOR>
 *
 */
/**
 * <AUTHOR>
 *
 */
public interface PriceProvisionEvent 
{
	/**
	 * Keys for payload.
	 */
	public String FI = "fi";
	public String LP = "lp";
	public String CCYP = "cp";
	public String BS = "bs";
	public String OS = "os";
	public String SPREADCLSF = "sclsf";
	public String CBA = "cba";
	public String SP = "sp";
	// spot spread multiplier
	public String SSM = "ssm";
	// Back To Back
	public String BB = "bb";
	// Skew Side
	public String SS = "ss";
	
	
	
	/**
	 * It supported price provisioning events
	 * e.g. SKEW event etc.
	 *
	 */
	public static enum EventType
	{
		SKEW
	};
	
	/**
	 * It supported price provisioning actions
	 * e.g START, STOP
	 *
	 */
	public static enum ActionType
	{
		START,
		STOP,
		RESET,
		EXPIRE,
		MAXRISK
	}
	
	/**
	 * @return eventType
	 */
	public EventType getEventType();
	
	/**
	 * @return sequence number
	 */
	public long getSeqNo();
	
	/**
	 * @param sequence number
	 */
	public void setSeqNo(long seqNo);
	
	/**
	 * @return action type
	 */
	public ActionType getActionType();
	
	/**
	 * @param sets action type
	 */
	public void setActionType(ActionType action);
	
	/**
	 * @return payload
	 */
	@SuppressWarnings("rawtypes")
	public HashMap getPayload();
	
	/**
	 * @param sets payload
	 */
	@SuppressWarnings("rawtypes")
	public void setPayload(HashMap payload);
	
	/**
	 * @return value from payload associated with key
	 */
	public Object getValue(String key);
	
	/**
	 * @param key goes in payload
	 * @param value goes in payload
	 */
	public void setValue(String key, Object value);
	
	/**
	 * @return String
	 */
	public String toString();
}
