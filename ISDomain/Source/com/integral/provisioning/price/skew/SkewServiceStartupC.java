package com.integral.provisioning.price.skew;

import java.util.Hashtable;


import com.integral.is.functor.PriceProvisionTradingLimitRuleFunctorC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.provisioning.price.skew.admin.SkewParameterModificationHandler;
import com.integral.provisioning.price.skew.config.PriceSkewServiceConfig;
import com.integral.provisioning.price.skew.config.PriceSkewServiceMbean;
import static com.integral.provisioning.price.skew.config.PriceSkewServiceMbean.ServiceMode.*;

import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.runtime.StartupTask;

/**
 * 
 * <AUTHOR>
 *
 */
public class SkewServiceStartupC implements StartupTask
{
	Log log = LogFactory.getLog(this.getClass());

	@Override
	public String startup( String aName, Hashtable args )
	{
		long st = System.currentTimeMillis();
		log.info("SSSC.startup - starting ");
		PriceSkewServiceMbean config = PriceSkewServiceConfig.getInstance();
		
		if( client.name().equals(config.getServiceMode()) )
		{
			startClient(config);
		}
		else if( server.name().equals(config.getServiceMode()) )
		{
			startServer(config);
		}
		else if( clientserver.name().equals(config.getServiceMode()) )
		{
			startClient(config);
			startServer(config);
		}
		log.info("SkewServiceStartupC - started .t=" + (System.currentTimeMillis() - st));
		return null;
	}
	
	private void startServer(PriceSkewServiceMbean config)
	{
		String errorCode = SkewServiceFactory.getPriceSkewService().setup(config);
		
		if ( errorCode != null )
		{
			log.warn("SSSC.startup - server started with errors. Error=" + errorCode);
		}
		
		// Register observer on skew server
		PriceProvisionTradingLimitRuleFunctorC.addObserver(new SkewParameterModificationHandler());
	}

    private void startClient( PriceSkewServiceMbean config )
    {
        if ( ConfigurationFactory.getServerMBean().isTradingServer() )
        {
            String errorCode = SkewServiceFactory.getPriceSkewClientService().setup( config );
            if ( errorCode != null )
            {
                log.warn( "SSSC.startup - client started with errors. Error=" + errorCode );
            }
        }
        else
        {
            log.info( "SSSC.startClient : skipping skew client startup." );
        }
    }
}
