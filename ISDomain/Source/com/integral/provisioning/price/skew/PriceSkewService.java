/**
 * 
 */
package com.integral.provisioning.price.skew;

import com.integral.user.Organization;
import com.integral.finance.currency.CurrencyPair;
import com.integral.provisioning.price.notification.PriceProvisionEvent;
import com.integral.provisioning.price.skew.config.PriceSkewServiceMbean;

/**
 * This is central service to which owns processors,
 * <AUTHOR>
 */
public interface PriceSkewService
{
	/**
	 * Initial setup
	 * @param config
	 */
	public String setup(PriceSkewServiceMbean config);
	
	/**
	 * updates config at runtime.
	 * @param config
	 */
	public String reconfig(/*TODO parameters*/);
	
	/**
	 * enqueues trade for processing.
	 * @param orgIndex 
	 * @param trade
	 */
	public void handleTradeNotification(Object tradeInfo, int orgIndex);
	
	/**
	 * Broadcasts start/stop skew events to the grid.
	 * @param ppe 
	 */
	public void publishSkewEvent(PriceProvisionEvent ppe , int fiOrgIndex);

	
	/**
	 * handles strategy level changes from admin
	 * @param fi
	 * @param lp
	 * @param currencyPair
	 */
	public void handleAdminNotification(Organization fi, Organization lp, CurrencyPair currencyPair);
}
