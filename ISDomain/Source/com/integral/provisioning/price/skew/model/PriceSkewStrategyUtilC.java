package com.integral.provisioning.price.skew.model;

import com.integral.admin.utils.marketdefinition.MarketDefinitionUtil;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.priceProvision.SpotSpreadProfile;
import com.integral.finance.dealing.skew.SkewSpread;
import com.integral.finance.dealing.skew.SkewStrategy;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.is.common.cache.MDSFactory;
import com.integral.provisioning.price.notification.PriceProvisionEvent;
import com.integral.provisioning.price.notification.PriceProvisionEvent.ActionType;
import com.integral.provisioning.price.notification.PriceProvisionEventFactory;
import com.integral.provisioning.price.skew.SkewServiceFactory;
import com.integral.provisioning.price.skew.cache.SkewSpreadCacheC;
import com.integral.user.Organization;

public class PriceSkewStrategyUtilC 
{
	public static final Currency CURRENCY_USD = CurrencyFactory.getCurrency("USD");
	public static PriceProvisionEvent sendStartSkewEvent (Organization fi, Organization lp, CurrencyPair ccyp, int skewedSide, double cumulativeBaseAmount)
	{
		SkewSpread spread = SkewSpreadCacheC.getInstance().get(fi, lp, ccyp);
		if (cumulativeBaseAmount == 0.0) {
			// set to at minimum skew threshold limit
			SkewStrategy ss = spread.getSkewStrategy();
			if (ss != null) {
				cumulativeBaseAmount = ss.getAmountThreshold();
				cumulativeBaseAmount = MDSFactory.getInstance().convertAmount(fi.getShortName(), CURRENCY_USD, ccyp.getBaseCurrency(), cumulativeBaseAmount, null);
			}
		}
		PriceProvisionEvent ppe = getStartSkewEvent(fi, lp, ccyp, spread, skewedSide, cumulativeBaseAmount);
		SkewServiceFactory.getPriceSkewService().publishSkewEvent(ppe,fi.getIndex());
		return ppe;
	}
	
	public static PriceProvisionEvent sendMaxRiskSkewEvent (Organization fi, Organization lp, CurrencyPair ccyp, 
			int skewedSide, double cumulativeBaseAmount, int spotSpreadMultiplier, boolean backToBack)
	{
		SkewSpread spread = SkewSpreadCacheC.getInstance().get(fi, lp, ccyp);
		PriceProvisionEvent ppe = getMaxRiskSkewEvent(fi, lp, ccyp, spread, skewedSide, cumulativeBaseAmount, spotSpreadMultiplier, backToBack);
		SkewServiceFactory.getPriceSkewService().publishSkewEvent(ppe,fi.getIndex());
		return ppe;
	}
	
	public static PriceProvisionEvent getStartSkewEvent(Organization fi, Organization lp, CurrencyPair ccyp, SkewSpread spread, int skewedSide, double cumulativeBaseAmount)
	{
		PriceProvisionEvent skewEvent = PriceProvisionEventFactory.getInstance()
				.getPriceProvisionEvent(PriceProvisionEvent.EventType.SKEW);
		skewEvent.setActionType(ActionType.START);
		skewEvent.setValue(PriceProvisionEvent.FI, fi.getShortName());
		skewEvent.setValue(PriceProvisionEvent.LP, lp.getShortName());
		skewEvent.setValue(PriceProvisionEvent.CCYP, ccyp.getName());
		skewEvent.setValue(PriceProvisionEvent.CBA, cumulativeBaseAmount);
		if (spread.getSpotSpreadProfile() != null) {
			skewEvent.setValue(PriceProvisionEvent.SP, spread.getSpotSpreadProfile().getShortName());
		}
		if( skewedSide == DealingPrice.BID )
		{
			skewEvent.setValue(PriceProvisionEvent.BS, spread.getSpreadValue());
			skewEvent.setValue(PriceProvisionEvent.OS, -1 * spread.getSpreadValue());
			skewEvent.setValue(PriceProvisionEvent.SS, DealingPrice.BID);
		}
		else
		{
			skewEvent.setValue(PriceProvisionEvent.BS, -1 * spread.getSpreadValue());
			skewEvent.setValue(PriceProvisionEvent.OS, spread.getSpreadValue());
			skewEvent.setValue(PriceProvisionEvent.SS, DealingPrice.OFFER);
		}
		skewEvent.setValue(PriceProvisionEvent.SPREADCLSF, spread.getSpreadClsf());
		return skewEvent;
	}
	
	public static PriceProvisionEvent getMaxRiskSkewEvent(Organization fi, Organization lp, CurrencyPair ccyp, 
			SkewSpread spread, int skewedSide, double cumulativeBaseAmount, int spotSpreadMultiplier, boolean backToBack)
	{
		PriceProvisionEvent skewEvent = PriceProvisionEventFactory.getInstance()
				.getPriceProvisionEvent(PriceProvisionEvent.EventType.SKEW);
		skewEvent.setActionType(ActionType.MAXRISK);
		skewEvent.setValue(PriceProvisionEvent.FI, fi.getShortName());
		skewEvent.setValue(PriceProvisionEvent.LP, lp.getShortName());
		skewEvent.setValue(PriceProvisionEvent.CCYP, ccyp.getName());
		skewEvent.setValue(PriceProvisionEvent.CBA, cumulativeBaseAmount);
		if (spread.getSpotSpreadProfile() != null) {
			skewEvent.setValue(PriceProvisionEvent.SP, spread.getSpotSpreadProfile().getShortName());
		}
		if( skewedSide == DealingPrice.BID )
		{
			skewEvent.setValue(PriceProvisionEvent.BS, spread.getSpreadValue());
			skewEvent.setValue(PriceProvisionEvent.OS, -1 * spread.getSpreadValue());
		}
		else
		{
			skewEvent.setValue(PriceProvisionEvent.BS, -1 * spread.getSpreadValue());
			skewEvent.setValue(PriceProvisionEvent.OS, spread.getSpreadValue());
		}
		skewEvent.setValue(PriceProvisionEvent.SPREADCLSF, spread.getSpreadClsf());
		skewEvent.setValue(PriceProvisionEvent.SSM, spotSpreadMultiplier);
		skewEvent.setValue(PriceProvisionEvent.BB, backToBack);
		return skewEvent;
	}
	
	public static PriceProvisionEvent sendStopSkewEvent (Organization fi, Organization lp, CurrencyPair ccyp)
	{
		SkewSpread spread = SkewSpreadCacheC.getInstance().get(fi, lp, ccyp);
		PriceProvisionEvent ppe = getStopSkewEvent(fi, lp, ccyp, spread);
		SkewServiceFactory.getPriceSkewService().publishSkewEvent(ppe,fi.getIndex());
		return ppe;
	}
	
	public static PriceProvisionEvent getStopSkewEvent( Organization fi, Organization lp, CurrencyPair ccyp, SkewSpread spread )
	{
		PriceProvisionEvent skewEvent = PriceProvisionEventFactory.getInstance()
				.getPriceProvisionEvent(PriceProvisionEvent.EventType.SKEW);
		skewEvent.setActionType(ActionType.STOP);
		skewEvent.setValue(PriceProvisionEvent.FI, fi.getShortName());
		skewEvent.setValue(PriceProvisionEvent.LP, lp.getShortName());
		skewEvent.setValue(PriceProvisionEvent.CCYP, ccyp.getName());
		skewEvent.setValue(PriceProvisionEvent.BS, 0.0);
		skewEvent.setValue(PriceProvisionEvent.OS, 0.0);
		skewEvent.setValue(PriceProvisionEvent.SPREADCLSF, spread.getSpreadClsf());
		return skewEvent;
	}
	
	public static PriceProvisionEvent sendExpireSkewEvent(Organization fi, Organization lp, CurrencyPair ccyp)
	{
		SkewSpread spread = SkewSpreadCacheC.getInstance().get(fi, lp, ccyp);
		PriceProvisionEvent ppe = getExpireSkewEvent(fi, lp, ccyp, spread);
		SkewServiceFactory.getPriceSkewService().publishSkewEvent(ppe,fi.getIndex());
		return ppe;
	}
	
	public static PriceProvisionEvent getExpireSkewEvent( Organization fi, Organization lp, CurrencyPair ccyp, SkewSpread spread )
	{
		PriceProvisionEvent skewEvent = PriceProvisionEventFactory.getInstance()
				.getPriceProvisionEvent(PriceProvisionEvent.EventType.SKEW);
		skewEvent.setActionType(ActionType.EXPIRE);
		skewEvent.setValue(PriceProvisionEvent.FI, fi.getShortName());
		skewEvent.setValue(PriceProvisionEvent.LP, lp.getShortName());
		skewEvent.setValue(PriceProvisionEvent.CCYP, ccyp.getName());
		skewEvent.setValue(PriceProvisionEvent.BS, 0.0);
		skewEvent.setValue(PriceProvisionEvent.OS, 0.0);
		skewEvent.setValue(PriceProvisionEvent.SPREADCLSF, spread.getSpreadClsf());
		return skewEvent;
	}
	
	public static PriceProvisionEvent sendResetSkewEvent ( )
	{
		PriceProvisionEvent ppe = getResetSkewEvent();
		SkewServiceFactory.getPriceSkewService().publishSkewEvent(ppe, 0);
		return ppe;
	}
	
	public static PriceProvisionEvent getResetSkewEvent()
	{
		PriceProvisionEvent skewEvent = PriceProvisionEventFactory.getInstance()
				.getPriceProvisionEvent(PriceProvisionEvent.EventType.SKEW);
		skewEvent.setActionType(ActionType.RESET);
		skewEvent.setValue(PriceProvisionEvent.FI, "ALL");
		skewEvent.setValue(PriceProvisionEvent.LP, "ALL");
		skewEvent.setValue(PriceProvisionEvent.CCYP, "ALL");
		skewEvent.setValue(PriceProvisionEvent.BS, 0.0);
		skewEvent.setValue(PriceProvisionEvent.OS, 0.0);
		return skewEvent;
	}

}
