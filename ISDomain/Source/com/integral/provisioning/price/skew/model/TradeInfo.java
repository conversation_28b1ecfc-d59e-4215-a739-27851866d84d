package com.integral.provisioning.price.skew.model;

import com.integral.finance.currency.Currency;
import com.integral.user.Organization;

/**
 * This object capture
 * <AUTHOR>
 *
 */
public class TradeInfo
{
	private Organization cptyA;
	private Organization cptyB;
	private String tid;
	private boolean isBuyingBase;
	private Currency dealtCcy;
	private Currency baseCcy;
	private Currency termCcy;
	private double baseAmount;
	private double termAmount;
	private long timestamp;
	private boolean isESP;
	private boolean isMaskedLPTrade;
	private Organization maskedLP;
	private Organization realLP;
	private String orderId;
	
	public TradeInfo()
	{
		this.timestamp = System.currentTimeMillis();  
	}
	
	public TradeInfo(Organization cptyA, Organization cptyB, String tid,
			boolean isBuyingBase, Currency dealtCcy, Currency baseCcy,
			Currency termCcy, double baseAmount, double termAmount, boolean isESP)
	{
		this.cptyA = cptyA;
		this.cptyB = cptyB;
		this.tid = tid;
		this.isBuyingBase = isBuyingBase;
		this.dealtCcy = dealtCcy;
		this.baseCcy = baseCcy;
		this.termCcy = termCcy;
		this.baseAmount = baseAmount;
		this.termAmount = termAmount;
		this.timestamp= System.currentTimeMillis();
		this.isESP = isESP;
	}

	public Organization getCptyA()
	{
		return cptyA;
	}

	public void setCptyA( Organization cptyA )
	{
		this.cptyA = cptyA;
	}

	public Organization getCptyB()
	{
		return cptyB;
	}

	public void setCptyB( Organization cptyB )
	{
		this.cptyB = cptyB;
	}

	public String getTid()
	{
		return tid;
	}

	public void setTid( String tid )
	{
		this.tid = tid;
	}

	public boolean isBuyingBase()
	{
		return isBuyingBase;
	}

	public void setBuyingBase( boolean isBuy )
	{
		this.isBuyingBase = isBuy;
	}

	public Currency getDealtCcy()
	{
		return dealtCcy;
	}

	public void setDealtCcy( Currency dealtCcy )
	{
		this.dealtCcy = dealtCcy;
	}

	public Currency getBaseCcy()
	{
		return baseCcy;
	}

	public void setBaseCcy( Currency baseCcy )
	{
		this.baseCcy = baseCcy;
	}

	public Currency getTermCcy()
	{
		return termCcy;
	}

	public void setTermCcy( Currency termCcy )
	{
		this.termCcy = termCcy;
	}

	public double getBaseAmount()
	{
		return baseAmount;
	}

	public void setBaseAmount( double baseAmount )
	{
		this.baseAmount = baseAmount;
	}

	public double getTermAmount()
	{
		return termAmount;
	}

	public void setTermAmount( double termAmount )
	{
		this.termAmount = termAmount;
	}
	
	public long getTimestamp()
	{
		return timestamp;
	}
	
	public void setTimestamp( long timestamp )
	{
		this.timestamp = timestamp;
	}

	@Override
	public String toString()
	{
		return "TradeInfo [tid" + tid + ",cptyA=" + cptyA.getShortName() + ", cptyB=" + cptyB.getShortName() + ", isMaskedLP=" + isMaskedLPTrade
				+ ", maskedLP=" + maskedLP + ", realLP=" + realLP
				+ ", isBuyingBase=" + isBuyingBase + ", dealtCcy=" + dealtCcy.getShortName() 
				+ ", baseCcy=" + baseCcy.getShortName() + ", termCcy=" + termCcy.getShortName() + ",isESP=" + isESP
				+ ", baseAmount=" + baseAmount + ", termAmount=" + termAmount + ", timestamp=" + timestamp + "]";
	}

	public boolean isESP()
	{
		return isESP;
	}

	public void setESP( boolean isESP )
	{
		this.isESP = isESP;
	}

	/**
	 * @return the isMaskedLPTrade
	 */
	public boolean isMaskedLPTrade()
	{
		return isMaskedLPTrade;
	}

	/**
	 * @param isMaskedLPTrade the isMaskedLPTrade to set
	 */
	public void setMaskedLPTrade( boolean isMaskedLPTrade )
	{
		this.isMaskedLPTrade = isMaskedLPTrade;
	}

	/**
	 * @return the maskedLP
	 */
	public Organization getMaskedLP()
	{
		return maskedLP;
	}

	/**
	 * @param maskedLP the maskedLP to set
	 */
	public void setMaskedLP( Organization maskedLP )
	{
		this.maskedLP = maskedLP;
	}

	/**
	 * @return the realLP
	 */
	public Organization getRealLP()
	{
		return realLP;
	}

	/**
	 * @param realLP the realLP to set
	 */
	public void setRealLP( Organization realLP )
	{
		this.realLP = realLP;
	}

	/**
	 * @return the orderId
	 */
	public String getOrderId() 
	{
		return orderId;
	}

	/**
	 * @param orderId the ISOrderId to set
	 */
	public void setOrderId(String orderId) 
	{
		this.orderId = orderId;
	}

}
