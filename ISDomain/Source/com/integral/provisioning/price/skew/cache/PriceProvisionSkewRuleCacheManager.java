package com.integral.provisioning.price.skew.cache;

import java.util.List;

import com.integral.finance.dealing.skew.SkewSpread;
import com.integral.finance.dealing.skew.SkewStrategy;
import com.integral.is.priceprovision.PriceProvisionInput;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.provisioning.price.skew.SkewServiceFactory;
import com.integral.provisioning.price.skew.model.PriceSkewStrategy;
import com.integral.user.PriceProvision;

public class PriceProvisionSkewRuleCacheManager
{
	private static Log log = LogFactory.getLog(PriceProvisionSkewRuleCacheManager.class);
	
	private PriceProvisionSkewRuleCacheManager()
	{
		
	}
	
	private static class Holder 
	{
		public static final PriceProvisionSkewRuleCacheManager INSTANCE = new PriceProvisionSkewRuleCacheManager();
	}
	
	public static PriceProvisionSkewRuleCacheManager getInstance()
	{
		return Holder.INSTANCE;
	}

	public boolean notify(PriceProvisionInput ppi, List<PriceProvision> pps)
	{
		PriceSkewStrategy strategy = PriceSkewStrategyCacheC.getInstance().get(ppi.fi, ppi.lp, ppi.ccyPair);
		if( strategy == null)
		{
			if( log.isDebugEnabled() )
			{
				log.debug("PPSRCM.notify : Price skew strategy does not exists for FI=" + ppi.fi.getShortName()
						+ ", LP=" + ppi.lp.getShortName() + ", CCYP=" + ppi.ccyPair.getName() + ", skipping.");
			}
			return false;
		}
		
		boolean notifySkewSerivce = false;
		
		// Usually pps will contain 2 elements, price provision for BID and OFFER
		for( PriceProvision pp : pps)
		{
			notifySkewSerivce = notifySkewSerivce || isSkewParamsUpdated( strategy, pp );
		}
		
		if ( notifySkewSerivce )
		{
			/*
			 * Notify skew service
			 */
			if ( log.isDebugEnabled() )
			{
				log.debug("PPSRCM.notify : Notifying admin changes to Skew service");
			}
			SkewServiceFactory.getPriceSkewService().handleAdminNotification( strategy.getFI(), strategy.getLP(), strategy.getCcyp() );
		}
		return true;
	}
	
	private boolean isSkewParamsUpdated(PriceSkewStrategy strategy, PriceProvision pp) 
	{
		boolean isSkewStrategyEnabledChanged	= false;
//		boolean isAmountThresholdChanged		= false;
//		boolean isExpiryTimeChanged				= false;
		
		boolean isSkewEnabledChanged			= false;
//		boolean isBidSkewSpreadChanged			= false;
//		boolean isOfferSkewSpreadChanged		= false;
//		boolean isBidSkewTypeChanged			= false;
//		boolean isOfferSkewTypeChanged			= false;
		
		SkewSpread ss = pp.getSkewSpread();
		SkewStrategy sstgy = ss.getSkewStrategy();
		SkewSpread skewSpread = SkewSpreadCacheC.getInstance().get(strategy.getFI(), strategy.getLP(), strategy.getCcyp());
		if ( skewSpread == null )
		{
			log.warn("PPSRCM.isSkewParamsUpdated : Skew Spread does not exists in cache for strategy - " + strategy);
			return false;
		}
		
		/*
		 * Attribute level comparisons
		 * 											old	value			!=			new value
		 */
		isSkewStrategyEnabledChanged 	= strategy.isSkewStrategyEnabled() != sstgy.isEnabled(); // Check is strategy level skew is enabled
//		isAmountThresholdChanged		= strategy.getAmountThreashold() != sstgy.getAmountThreshold(); 
//		isExpiryTimeChanged				= strategy.getExpiryTime() != sstgy.getExpiryTime();
		
		isSkewEnabledChanged			= strategy.isSkewStrategyEnabled() != ss.isEnabled(); // Check is strategy level skew is enabled
//		isBidSkewSpreadChanged			= skewSpread.getSpreadValue() != ss.getSpreadValue();
//		isOfferSkewSpreadChanged		= skewSpread.getSpreadValue() != ss.getSpreadValue();
//		isBidSkewTypeChanged			= skewSpread.getSpreadClsf() != ss.getSpreadClsf();
//		isOfferSkewTypeChanged			= skewSpread.getSpreadClsf() != ss.getSpreadClsf();
		
		if ( isSkewStrategyEnabledChanged || isSkewEnabledChanged  
//				|| isExpiryTimeChanged || isAmountThresholdChanged
//				|| isBidSkewSpreadChanged || isOfferSkewSpreadChanged
//				|| isBidSkewTypeChanged || isOfferSkewTypeChanged 
			)
		{
			StringBuilder sb = new StringBuilder(512);
			sb.append("PPSRCM - Skew Param updated for FI=").append(strategy.getFI().getShortName());
			sb.append(", LP=").append(strategy.getLP().getShortName());
			sb.append(", CCYP=").append(strategy.getCcyp().getName()).append(". Params: ");
			
			sb.append("isSkewStrategyEnabledChanged=").append(isSkewStrategyEnabledChanged);
			if ( isSkewStrategyEnabledChanged ) sb.append(" (").append(strategy.isSkewStrategyEnabled()).append(" to ").append(sstgy.isEnabled()).append(")");
			
//			sb.append(", isAmountThresholdChanged").append(isAmountThresholdChanged);
//			if ( isAmountThresholdChanged ) sb.append(" (").append(strategy.getAmountThreashold()).append(" to ").append(sstgy.getAmountThreshold()).append(")");
//			
//			sb.append(", isExpiryTimeChanged").append(isExpiryTimeChanged);
//			if ( isExpiryTimeChanged ) sb.append(" (").append(strategy.getExpiryTime()).append(" to ").append(sstgy.getExpiryTime()).append(")");
			
			sb.append(", isEnabledSkewChanged=").append(isSkewEnabledChanged);
			if ( isSkewEnabledChanged ) sb.append(" (").append(strategy.isSkewStrategyEnabled()).append(" to ").append(ss.isEnabled()).append(")");
			
//			sb.append(", isBidSkewSpreadChanged=").append(isBidSkewSpreadChanged);
//			if ( isBidSkewSpreadChanged ) sb.append(" (").append(skewSpread.getSpreadValue()).append(" to ").append(ss.getSpreadValue()).append(")");
//			
//			sb.append(", isBidSkewTypeChanged=").append(isBidSkewTypeChanged);
//			if ( isBidSkewTypeChanged ) sb.append(" (").append(skewSpread.getSpreadClsf()).append(" to ").append(ss.getSpreadClsf()).append(")");
//			
//			sb.append(", isOfferSkewSpreadChanged=").append(isOfferSkewSpreadChanged);
//			if ( isOfferSkewSpreadChanged ) sb.append(" (").append(skewSpread.getSpreadValue()).append(" to ").append(ss.getSpreadValue()).append(")");
//			
//			sb.append(", isOfferSkewTypeChanged=").append(isOfferSkewTypeChanged);
//			if ( isOfferSkewTypeChanged ) sb.append(" (").append(skewSpread.getSpreadClsf()).append(" to ").append(ss.getSpreadClsf()).append(")");
			
			log.info(sb.toString());
			return true;
		}
		return false;
	}
}
