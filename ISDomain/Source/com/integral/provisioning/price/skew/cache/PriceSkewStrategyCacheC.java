package com.integral.provisioning.price.skew.cache;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import com.integral.finance.currency.CurrencyPair;
import com.integral.provisioning.price.skew.model.PriceSkewStrategy;
import com.integral.user.Organization;

/**
 * <AUTHOR>
 *
 */
public class PriceSkewStrategyCacheC
{
	private ConcurrentMap<String, PriceSkewStrategy> cache = new ConcurrentHashMap<String, PriceSkewStrategy>();
	
	private PriceSkewStrategyCacheC()
	{
		
	}
	
	private static class Holder
	{
		public static final PriceSkewStrategyCacheC INSTANCE = new PriceSkewStrategyCacheC();
	}
	
	public static PriceSkewStrategyCacheC getInstance()
	{
		return Holder.INSTANCE;
	}
	
	public PriceSkewStrategy get(Organization fi, Organization lp, CurrencyPair ccyp)
	{
		String key = PriceProvisionSkewCacheUtilC.getKey(fi, lp, ccyp);
		return cache.get(key);
	}
	
	public void put(Organization fi, Organization lp, CurrencyPair ccyp, PriceSkewStrategy value)
	{
		String key = PriceProvisionSkewCacheUtilC.getKey(fi, lp, ccyp);
		cache.put(key, value);
	}
	
	public boolean remove(Organization fi, Organization lp, CurrencyPair ccyp)
	{
		String key = PriceProvisionSkewCacheUtilC.getKey(fi, lp, ccyp);
		return cache.remove(key) != null;
	}
	
	public boolean remove(PriceSkewStrategy pss)
	{
		String key = PriceProvisionSkewCacheUtilC.getKey(pss.getFI(), pss.getLP(), pss.getCcyp());
		return cache.remove(key) != null ;
	}
	
	public Map<String, PriceSkewStrategy> getCacheContent()
	{
		return Collections.unmodifiableMap(cache);
	}
	
	/**
	 * Warning: Panic button for JSP utility
	 */
	public void flush()
	{
		cache.clear();
	}
}
