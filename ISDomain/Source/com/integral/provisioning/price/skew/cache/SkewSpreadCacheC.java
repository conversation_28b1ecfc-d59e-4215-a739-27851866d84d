package com.integral.provisioning.price.skew.cache;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.skew.SkewSpread;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.priceprovision.PriceProvisionDBUtil;
import com.integral.user.Organization;
import com.integral.user.PriceProvision;

/**
 * <AUTHOR>
 *
 */
public class SkewSpreadCacheC
{
	private ConcurrentMap<String, SkewSpread> cache = new ConcurrentHashMap<String, SkewSpread>();
	
	private SkewSpreadCacheC()
	{
		
	}
	
	private static class Holder
	{
		public static final SkewSpreadCacheC INSTANCE = new SkewSpreadCacheC();
	}
	
	public static SkewSpreadCacheC getInstance()
	{
		return Holder.INSTANCE;
	}
	
	public SkewSpread get(Organization fi, Organization lp, CurrencyPair ccyp)
	{
		String key = PriceProvisionSkewCacheUtilC.getKey(fi, lp, ccyp);
		SkewSpread ss = cache.get(key);

		if ( null == ss )
		{
			// Lookup from DB
			List<PriceProvision> pPS = ServiceFactory.getPriceProvisionDataSource().queryPriceProvision(fi, lp, ccyp);
			if ( null != pPS)
			{
				for( PriceProvision pp : pPS)
				{
					ss = pp.getSkewSpread();
					if ( null != ss )
					{
						cache.put(key, ss);
						break;
					}
				}
			}
		}
		return ss;		
	}
	
	public void put(Organization fi, Organization lp, CurrencyPair ccyp, SkewSpread value)
	{
		String key = PriceProvisionSkewCacheUtilC.getKey(fi, lp, ccyp);
		cache.put(key, value);
	}
	
	public boolean remove(Organization fi, Organization lp, CurrencyPair ccyp)
	{
		String key = PriceProvisionSkewCacheUtilC.getKey(fi, lp, ccyp);
		return cache.remove(key) != null;
	}
	
	public Map<String, SkewSpread> getCacheContent()
	{
		return Collections.unmodifiableMap(cache);
	}
	
	/**
	 * Warning: Panic button for JSP utility
	 */
	public void flush()
	{
		cache.clear();
	}
}
