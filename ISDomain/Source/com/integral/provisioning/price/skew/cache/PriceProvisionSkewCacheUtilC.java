package com.integral.provisioning.price.skew.cache;

import com.integral.finance.currency.CurrencyPair;
import com.integral.user.Organization;

/**
 * <AUTHOR>
 *
 */
public class PriceProvisionSkewCacheUtilC
{
	/**
	 * Format:
	 * 		|------ FI Index -------|------ LP Index -------|-- Currency Pair CCY --| 
	 * 		|------- 22 bits -------|------- 22 bits -------|------- 20 bits -------|    64 bits
	 * @param tradeInfo
	 * @return key
	 */
	@Deprecated
	public static long getKeyV0(Organization fi, Organization lp, CurrencyPair ccyp)
	{
		long key = 0;
		long fi_idx = fi.getIndex();
		long lp_idx = lp.getIndex();
		
		long ccyp_idx = ccyp.getIndex();
		
		fi_idx <<= 42;
		lp_idx <<= 20;
		
		key = fi_idx | lp_idx | ccyp_idx;
		return key;
	}
	
	/**
	 * Format:
	 * 		<FI_SHORTNAME>_<LP_SHORTNAME>_<CCYP_NAME>
	 * @param fi
	 * @param lp
	 * @param ccyp
	 * @return String key
	 */
	public static String getKey(Organization fi, Organization lp, CurrencyPair ccyp)
	{
		return fi.getShortName() + "_" + lp.getShortName() + "_" + ccyp.getName();
	}
}
