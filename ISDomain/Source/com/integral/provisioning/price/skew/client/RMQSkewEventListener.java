package com.integral.provisioning.price.skew.client;

import java.util.Map;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.messaging.MessageListener;
import com.integral.messaging.MessageReceiver;
import com.integral.messaging.MessageReceiverFactory;
import com.integral.messaging.MessagingException;
import com.integral.messaging.RMQMessage;
import com.integral.messaging.config.MessagingConfiguration;
import com.integral.provisioning.price.skew.config.PriceSkewServiceMbean;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.runtime.ServerRuntimeMBean;

public class RMQSkewEventListener extends SkewEventListenerC implements SkewEventListener,MessageListener {
	
	Log log = LogFactory.getLog(this.getClass());

	@Override
	public String setup(PriceSkewServiceMbean config) {
		String error = super.setup(config);
		if( error == null ){
			String exchangeName = ServerRuntimeMBean.PriceProvision_EXCHANGE;
			String queueName = MessagingConfiguration.getInstance().getUserDBPrefix() + "_" + ConfigurationFactory.getServerMBean().getVirtualServerName() + "_" + exchangeName + "_skewevents";
			try {
				MessageReceiver receiver = MessageReceiverFactory.newMessageReceiver(exchangeName, queueName, false, true,
						true, false, this);
				receiver.addBinding("");
			} catch (Exception ex) {
				log.error("Error in creating MessaingFramework receiver: ", ex);
				return "Listener setup failed. ex=" + ex.getMessage();
			}
		}
		return error;
	}

	@Override
	public void onMessage(RMQMessage message) {
		try {
			String notificationMessage = (String) message.getPayload();
			handleMessage(notificationMessage);
		} catch (Exception e) {
			log.error("JTNL.onMessage : exception while handling Notification message. msg="
					+ message);
		}
		try {
			message.ack();
		} catch (MessagingException e) {
			log.error("Error in sending the ack for message:" + message.getPayload()
					+ ":routingKey=" + message.getRoutingKey() +":ackId:" + message.getAckId(), e);
		}
	}

}
