package com.integral.provisioning.price.skew.client;

import com.integral.provisioning.price.skew.SkewServiceFactory;
import com.integral.provisioning.price.skew.config.PriceSkewServiceMbean;
import com.integral.provisioning.price.skew.notification.SkewEventNotifier;
import com.integral.provisioning.price.skew.notification.SkewEventNotifier.SkewEventNotifierTypes;

public class PriceSkewClientServiceC implements PriceSkewClientService
{

	@Override
	public String setup( PriceSkewServiceMbean config )
	{
		int typeValue = config.getSkewEventNotifierType();

		SkewEventNotifier.SkewEventNotifierTypes type = SkewEventNotifier.SkewEventNotifierTypes.getById(typeValue);
		
		//setup skew event listener...
		SkewEventListener listener = SkewServiceFactory.getSkewEventListener(type);
		listener.setup(config);

		return null;
	}

	/**
	 * @param args
	 */
	public static void main( String[] args )
	{
		// TODO Auto-generated method stub

	}

}
