package com.integral.provisioning.price.skew.log;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.integral.is.priceprovision.rules.SpreadRuleParameter;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.provisioning.price.notification.PriceProvisionEvent;
import com.integral.provisioning.price.skew.config.PriceSkewServiceConfig;
import com.integral.provisioning.price.skew.model.PriceSkewStrategy;
import com.integral.provisioning.price.skew.model.TradeInfo;
import com.integral.util.StringUtilC;
/**
 * Price Skew Service Logger to log strategy & skew events
 *  
 * <AUTHOR>
 *
 */
public class PSSLogger 
{
	private static final char whitespace = ' ';
	private static final char colon = ':';
	private static final char comma = ',';
	
	private static final String skewStarted 			= "skewStart";
	private static final String skewStopped  			= "skewStop";
	private static final String skewReset  				= "skewReset";
	private static final String skewFlipped 			= "skewFlip";
	private static final String skewExpired 			= "skewExpire";
	private static final String skewHeartBeat 			= "skewHeartbeat";
	private static final String skewEventDrop 			= "skewEventDrop";
	private static final String skewSpreadTriggered 	= "skewSpreadTriggered";
	private static final String skewSpreadReset			= "skewSpreadReset";
	
	private static final String strategyCreated = "strategyCreated";
	private static final String strategyUpdated = "strategyUpdated";
	private static final String strategyRemoved = "strategyRemoved";
	
	private static final String strategyAddTradeInfo = "strategyAddTradeInfo";
	
	private static final String defaultSentBy = "System";
	
	protected Log log = null;
	
	static Log strdLog = LogFactory.getLog(PSSLogger.class);
	
	private PSSLogger()
	{
		init();
	}
	
	private static class Holder
	{
		public static PSSLogger INSTANCE = new PSSLogger();
	}
	
	public static PSSLogger getInstance()
	{
		return Holder.INSTANCE;
	}
	
	private void init()
	{
		if ( null == log )
		{
			log = LogFactory.getLog( PriceSkewServiceConfig.getInstance().getLogCategory() );
		}
	}
	
	public void logInfo ( String message )
	{
		if ( log.isInfoEnabled() )
		{
			log.info(message);
		}
	}
	
	public void logDebug ( String message )
	{
		if ( log.isDebugEnabled() )
		{
			log.debug(message);
		}
	}
	
	/*
	 * Skew Events related log methods
	 */
	
	public void logStartSkewEvent( PriceSkewStrategy pss, PriceProvisionEvent ppe )
	{
		logStartSkewEvent( pss, ppe, null );
	}
	
	public void logStartSkewEvent( PriceSkewStrategy pss, PriceProvisionEvent ppe, String sentBy )
	{
		String msg = null;
		try
		{
			StringBuilder sb = new StringBuilder(512);
			sb.append(skewStarted).append(whitespace);
			sb.append(getSkewEventMessage(ppe)).append(whitespace);
			sb.append(getSkewStrategyMessage(pss)).append(whitespace);
			sb.append("sentBy=").append(StringUtilC.isNullOrEmpty(sentBy) ? defaultSentBy : sentBy);
			msg = sb.toString();
		}
		catch ( Exception e )
		{
			strdLog.error("PSSLogger.logStartSkewEvent - Error ", e);
		}
		finally
		{
			logInfo(msg);
		}
	}
	
	public void logStopSkewEvent( PriceSkewStrategy pss, PriceProvisionEvent ppe )
	{
		logStopSkewEvent( pss, ppe, null );
	}
	
	public void logStopSkewEvent( PriceSkewStrategy pss, PriceProvisionEvent ppe, String sentBy )
	{
		String msg = null;
		try
		{
			StringBuilder sb = new StringBuilder(512);
			sb.append(skewStopped).append(whitespace);
			sb.append(getSkewEventMessage(ppe)).append(whitespace);
			sb.append(getSkewStrategyMessage(pss)).append(whitespace);
			sb.append("sentBy=").append(StringUtilC.isNullOrEmpty(sentBy) ? defaultSentBy : sentBy);
			msg = sb.toString();
		}
		catch ( Exception e )
		{
			strdLog.error("PSSLogger.logStopSkewEvent - Error ", e);
		}
		finally
		{
			logInfo(msg);
		}
	}
	
	public void logResetSkewEvent( PriceProvisionEvent ppe )
	{
		logResetSkewEvent( ppe, null );
	}
	
	public void logResetSkewEvent( PriceProvisionEvent ppe, String sentBy )
	{
		String msg = null;
		try
		{
			StringBuilder sb = new StringBuilder(512);
			sb.append(skewReset).append(whitespace);
			sb.append(getSkewEventMessage(ppe)).append(whitespace);
			sb.append("sentBy=").append(StringUtilC.isNullOrEmpty(sentBy) ? defaultSentBy : sentBy);
			msg = sb.toString();
		}
		catch ( Exception e )
		{
			strdLog.error("PSSLogger.logResetSkewEvent - Error ", e);
		}
		finally
		{
			logInfo(msg);
		}
	}
	
	public void logFlippedSkewEvent( PriceSkewStrategy pss, PriceProvisionEvent ppe )
	{
		String msg = null;
		try
		{
			StringBuilder sb = new StringBuilder(512);
			sb.append(skewFlipped).append(whitespace);
			sb.append(getSkewEventMessage(ppe)).append(whitespace);
			sb.append(getSkewStrategyMessage(pss));
			msg = sb.toString();
		}
		catch ( Exception e )
		{
			strdLog.error("PSSLogger.logFlippedSkewEvent - Error ", e);
		}
		finally
		{
			logInfo(msg);
		}
	}
	
	public void logHearbeatSkewEvent( PriceSkewStrategy pss, PriceProvisionEvent ppe )
	{
		String msg = null;
		try
		{
			StringBuilder sb = new StringBuilder(512);
			sb.append(skewHeartBeat).append(whitespace);
			sb.append(getSkewEventMessage(ppe)).append(whitespace);
			sb.append(getSkewStrategyMessage(pss));
			msg = sb.toString();
		}
		catch ( Exception e )
		{
			strdLog.error("PSSLogger.logHearbeatSkewEvent - Error ", e);
		}
		finally
		{
			logInfo(msg);
		}
	}
	
	public void logExpireSkewEvent( PriceSkewStrategy pss, PriceProvisionEvent ppe )
	{
		String msg = null;
		try
		{
			StringBuilder sb = new StringBuilder(512);
			sb.append(skewExpired).append(whitespace);
			sb.append(getSkewEventMessage(ppe)).append(whitespace);
			sb.append(getSkewStrategyMessage(pss));
			msg = sb.toString();
		}
		catch ( Exception e )
		{
			strdLog.error("PSSLogger.logExpireSkewEvent - Error ", e);
		}
		finally
		{
			logInfo(msg);
		}
	}
	
	public void logDroppedSkewEvent( PriceProvisionEvent ppe, Long lastSeq )
	{
		String msg = null;
		try
		{
			StringBuilder sb = new StringBuilder(512);
			sb.append(skewEventDrop).append(whitespace);
			sb.append(getSkewEventMessage(ppe)).append(whitespace);
			sb.append("lastSeq=").append(lastSeq);
			msg = sb.toString();
		}
		catch ( Exception e )
		{
			strdLog.error("PSSLogger.logDroppedSkewEvent - Error ", e);
		}
		finally
		{
			logInfo(msg);
		}
	}
	
	/**
	 * Keys --> Interpretation
	 * sN	Sequence Number
	 * eT	Event type
	 * aT	Action type
	 * pL	Payload
	 */
	private String getSkewEventMessage( PriceProvisionEvent ppe )
	{
		StringBuilder sb = new StringBuilder(512);
		sb.append("evt[");
		
		if ( null == ppe )
		{
			sb.append("null]");
			return sb.toString();
		}
		
		sb.append("sN=").append(ppe.getSeqNo()).append(comma);
		sb.append("eT=").append(ppe.getEventType()).append(comma);
		sb.append("aT=").append(ppe.getActionType()).append(comma);
		sb.append("pL{").append(getFlatMap(ppe.getPayload()));
		sb.append("}]");
		return sb.toString();
	}
	
	/*
	 * Skew strategy related log methods
	 */
	
	public void logSkewStrategyCreation( PriceSkewStrategy pss )
	{
		String msg = null;
		try
		{
			StringBuilder sb = new StringBuilder(128);
			sb.append(strategyCreated).append(whitespace);
			sb.append(getSkewStrategyMessage(pss));
			msg = sb.toString();
		}
		catch ( Exception e )
		{
			strdLog.error("PSSLogger.logSkewStrategyCreation - Error ", e);
		}
		finally
		{
			logInfo(msg);
		}
	}
	
	public void logSkewStrategyUpdated( PriceSkewStrategy pss )
	{
		String msg = null;
		try
		{
			StringBuilder sb = new StringBuilder(128);
			sb.append(strategyUpdated).append(whitespace);
			sb.append(getSkewStrategyMessage(pss));
			msg = sb.toString();
		}
		catch ( Exception e )
		{
			strdLog.error("PSSLogger.logSkewStrategyUpdated - Error ", e);
		}
		finally
		{
			logInfo(msg);
		}
	}
	
	public void logSkewStrategyRemoval( PriceSkewStrategy pss )
	{
		String msg = null;
		try
		{
			StringBuilder sb = new StringBuilder(128);
			sb.append(strategyRemoved).append(whitespace);
			sb.append(getSkewStrategyMessage(pss));
			msg = sb.toString();
		}
		catch ( Exception e )
		{
			strdLog.error("PSSLogger.logSkewStrategyRemoval - Error ", e);
		}
		finally
		{
			logInfo(msg);
		}
	}
	
	public void logSkewStrategyNewTradeInfo( PriceSkewStrategy pss, TradeInfo tI )
	{
		String msg = null;
		try
		{
			StringBuilder sb = new StringBuilder(256);
			sb.append(strategyAddTradeInfo).append(whitespace);
			sb.append(getTradeInfoMessage(tI)).append(whitespace);
			sb.append(getSkewStrategyMessage(pss));
			msg = sb.toString();
		}
		catch ( Exception e )
		{
			strdLog.error("PSSLogger.logSkewStrategyNewTradeInfo - Error ", e);
		}
		finally
		{
			logInfo(msg);
		}
	}
	
	/**
	 * Keys -> Interpretations
	 * n	Strategy Name
	 * ef	Is skew strategy effective
	 * ca	Cumulative base amount
	 * css	Current skew side
	 * th	Threashold amount
	 * tids	TradeInfo IDs
	 */
	private String getSkewStrategyMessage( PriceSkewStrategy pss )
	{
		StringBuilder sb = new StringBuilder(512);
		
		sb.append("stgy[");
		if ( null == pss )
		{
			sb.append("null]");
			return sb.toString();
		}
		sb.append("n=").append(pss.getName()).append(comma);
		sb.append("ef=").append(pss.isSkewEffective()?"T":"F").append(comma);
		sb.append("css=").append(pss.getCurrentSkewedSide()).append(comma);
		sb.append("th=").append(String.valueOf(pss.getSkewStrategy().getAmountThreshold())).append(comma);
		sb.append("tids{").append(getFlatList(pss.getParticipantTrades())).append("}");
		
		sb.append("]");
		return sb.toString();
	}
	
	/**
	 * Keys --> Interpretations
	 * tid	Trade ID
	 * oid	Order ID
	 * ibb	isBuyingBase
	 * ba	Base Amount
	 * ta	Term Amount
	 * bc	Base Currency
	 * tc	Term Currency
	 * dc	Dealt Currency
	 */
	private String getTradeInfoMessage( TradeInfo ti )
	{
		StringBuilder sb = new StringBuilder(128);
		sb.append("ti[");
		if ( null == ti )
		{
			sb.append("null]");
			return sb.toString();
		}
		sb.append("tid=").append(ti.getTid()).append(comma);
		sb.append("oid=").append(ti.getOrderId()).append(comma);
		sb.append("ibb=").append(ti.isBuyingBase()?"T":"F").append(comma);
		sb.append("ba=").append(String.valueOf(ti.getBaseAmount())).append(comma);
		sb.append("ta=").append(String.valueOf(ti.getTermAmount())).append(comma);
		sb.append("bc=").append(ti.getBaseCcy().getShortName()).append(comma);
		sb.append("tc=").append(ti.getTermCcy().getShortName()).append(comma);
		sb.append("dc=").append(ti.getDealtCcy().getShortName());
		sb.append("]");
		return sb.toString();
	}
	
	/*
	 * Skew spread related log events
	 */
	
	public void logSkewSpreadTriggeredEvent(SpreadRuleParameter srp)
	{
		String msg = null;
		try
		{
			StringBuilder sb = new StringBuilder(256);
			sb.append(skewSpreadTriggered).append(whitespace);
			sb.append(getSpreadRuleParameterMessage(srp));
			msg = sb.toString();
		}
		catch ( Exception e )
		{
			strdLog.error("PSSLogger.logSkewSpreadTriggeredEvent - Error ", e);
		}
		finally
		{
			logInfo(msg);
		}
	}
	
	public void logSkewSpreadResetEvent(SpreadRuleParameter srp)
	{
		String msg = null;
		try
		{
			StringBuilder sb = new StringBuilder(256);
			sb.append(skewSpreadReset).append(whitespace);
			sb.append(getSpreadRuleParameterMessage(srp));
			msg = sb.toString();
		}
		catch ( Exception e )
		{
			strdLog.error("PSSLogger.logSkewSpreadResetEvent - Error ", e);
		}
		finally
		{
			logInfo(msg);
		}
	}
	
	/**
	 * Keys --> Interpretations
	 * fi	Financial Institute
	 * lp	Liquidity Provider ( / Broker )
	 * cp	Currency Pair
	 * eS	Enable Skew
	 * bS	Bid Skew spread
	 * bST	Bid skew type ( 0-PIPS, 1-BPS, 2-ABS)
	 * oS	Offer Skew spread
	 * oST	Offer skew type ( 0-PIPS, 1-BPS, 2-ABS)
	 * ss	Skewed Spread
	 */
	
	private String getSpreadRuleParameterMessage( SpreadRuleParameter srp )
	{
		StringBuilder sb = new StringBuilder(256);
		sb.append("srp[");
		if( null == srp )
		{
			sb.append("null]");
			return sb.toString();
		}
		sb.append("fi=").append(srp.getFI().getShortName()).append(comma);
		sb.append("lp=").append(srp.getLP().getShortName()).append(comma);
		sb.append("cp=").append(srp.getCurrencyPairName() != null ? srp.getCurrencyPairName() : "null").append(comma);
		sb.append("eS=").append(srp.isSkewEnabled()?"T":"F").append(comma);
		sb.append("bS=").append(String.valueOf(srp.getRawBidSkew())).append(comma);
		sb.append("bST=").append(String.valueOf(srp.getBidSkewType())).append(comma);
		sb.append("oS=").append(String.valueOf(srp.getRawOfferSkew())).append(comma);
		sb.append("oST=").append(String.valueOf(srp.getOfferSkewType())).append(comma);
		sb.append("ss=").append(String.valueOf(srp.getPipsBidSkew())).append('/').append(String.valueOf(srp.getPipsOfferSkew()));
		sb.append("]");
		return sb.toString();
	}
	
	private String getFlatMap( HashMap<String, Object> hp)
	{
		if ( null == hp || hp.isEmpty() )
		{
			return "empty";
		}

		StringBuilder sb = new StringBuilder(256);
		for( Map.Entry<String, Object> key : hp.entrySet() )
		{
			sb.append(key.getKey()).append(colon).append(key.getValue()).append(comma);
		}
		sb.setLength(sb.length() - 1); // Remove last char
		return sb.toString();
	}
	
	private String getFlatList( List<TradeInfo> list )
	{
		StringBuilder sb = new StringBuilder(512);
		if ( null == list || list.isEmpty() )
		{
			sb.append("empty");
			return sb.toString();
		}
		for( TradeInfo ti : list )
		{
			sb.append(ti.getTid()).append(comma);
		}
		sb.setLength(sb.length() - 1);
		return sb.toString();
	}
}
