package com.integral.provisioning.price.skew;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

import com.integral.provisioning.price.skew.cache.PriceProvisionSkewRuleCacheManager;
import com.integral.provisioning.price.skew.cache.PriceSkewStrategyCacheC;
import com.integral.provisioning.price.skew.client.PriceSkewClientService;
import com.integral.provisioning.price.skew.client.PriceSkewClientServiceC;
import com.integral.provisioning.price.skew.client.RMQSkewEventListener;
import com.integral.provisioning.price.skew.client.SkewEventListener;
import com.integral.provisioning.price.skew.notification.CDMessageParser;
import com.integral.provisioning.price.skew.notification.JMSSkewEventNotifier;
import com.integral.provisioning.price.skew.notification.JMSTradeNotificationListener;
import com.integral.provisioning.price.skew.notification.MessageParser;
import com.integral.provisioning.price.skew.notification.SkewEventNotifier;
import com.integral.provisioning.price.skew.notification.TradeNotificationListener;

public class SkewServiceFactory
{	
	
	protected MessageParser[] parsers = {
		new CDMessageParser()
	};
	
	protected TradeNotificationListener[] listeners = {
		new JMSTradeNotificationListener()	
	};
	
	protected SkewEventNotifier notifiers[] = {
		new JMSSkewEventNotifier()
	};
	
	protected SkewEventListener skewEventListeners[] = {
		new RMQSkewEventListener()
	};
	
	protected PriceSkewService skewService = new PriceSkewServiceC();
	
	protected PriceSkewClientService skewClientService = new PriceSkewClientServiceC();
	
	private SkewServiceFactory() {}
	
	private static class Holder{
		public static final SkewServiceFactory INSTANCE = new SkewServiceFactory();
	}
	
	public static SkewServiceFactory getFactory()
	{
		return Holder.INSTANCE;
	}
	
	public static MessageParser getParser(MessageParser.MessageParserTypes type)
	{
		return Holder.INSTANCE.parsers[type.getId()];
	}
	
	public static TradeNotificationListener getNotificationListener(TradeNotificationListener.TradeNotificationListenerTypes type)
	{
		return Holder.INSTANCE.listeners[type.getId()];
	}
	
	public static SkewEventNotifier getSkewEventNotifier(SkewEventNotifier.SkewEventNotifierTypes type)
	{
		return Holder.INSTANCE.notifiers[type.getId()];
	}
	
	public static SkewEventListener getSkewEventListener(SkewEventNotifier.SkewEventNotifierTypes type)
	{
		return Holder.INSTANCE.skewEventListeners[type.getId()];
	}
	
	public static PriceSkewService getPriceSkewService()
	{
		return Holder.INSTANCE.skewService;
	}
	
	public static PriceSkewClientService getPriceSkewClientService()
	{
		return Holder.INSTANCE.skewClientService;
	}
	
	public ThreadFactory newThreadFactory(String name)
	{
		return new DefaultThredFactory(name);
	}
	
	
	private class DefaultThredFactory implements ThreadFactory
	{
		AtomicInteger threadNumber = new AtomicInteger(1);
		ThreadGroup tg = null;
		String name;

		public DefaultThredFactory( String name )
		{
			tg = new ThreadGroup(name);
			this.name = name;
		}

		public Thread newThread( Runnable runnable )
		{
			return new Thread(tg, runnable, name + "-" + threadNumber.getAndIncrement());
		}
	}
	
	public static PriceSkewStrategyCacheC getPriceSkewStrategyCacheC()
	{
		return PriceSkewStrategyCacheC.getInstance();
	}
	
	public static PriceProvisionSkewRuleCacheManager getPriceProvisionSkewRuleCacheManager()
	{
		return PriceProvisionSkewRuleCacheManager.getInstance();
	}
	
}
