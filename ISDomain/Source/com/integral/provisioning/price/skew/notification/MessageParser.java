package com.integral.provisioning.price.skew.notification;

import com.integral.provisioning.price.skew.model.TradeInfo;

public interface MessageParser
{
	enum MessageParserTypes {
		CD(0);
		
		int id;
		
		MessageParserTypes(int id)
		{
			this.id = id;
		}
		
		public int getId()
		{
			return this.id;
		}
		
		public static MessageParserTypes getById(int id)
		{
			for ( MessageParserTypes type : MessageParserTypes.values() )
			{
				if( type.getId() == id )
				{
					return type;
				}
			}
			return null;
		}
		
	}
	
	public TradeInfo parse(Object message) throws Exception;

}
