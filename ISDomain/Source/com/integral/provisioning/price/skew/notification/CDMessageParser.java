package com.integral.provisioning.price.skew.notification;

import javax.jms.Message;
import javax.jms.TextMessage;

import org.codehaus.jackson.map.ObjectMapper;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.is.ISCommonConstants;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.provisioning.price.skew.model.TradeInfo;
import com.integral.user.Organization;
import com.integral.util.StringUtilC;

/**
 * Parses chief dealer (notification) into TradeInfo object and then passes.
 * <AUTHOR>
 *
 */
public class CDMessageParser implements MessageParser
{
	
	protected Log log = LogFactory.getLog(this.getClass());
	
	public CDMessageParser()
	{
	}

	/**
	 * message is 
	 * @throws Exception 
	 */
	@Override
	public TradeInfo parse( Object message ) throws Exception
	{
		String msg = extractMessage(message);
		if ( StringUtilC.isNullOrEmpty(msg) )
		{
			log.error("CDMessageParser.parse : Unexpected message for msg - " + message);
			throw new IllegalArgumentException("CDMessageParser cannot parse message - " + message);
		}
		return parseMe( msg );
	}
	
	public TradeInfo parseMe( String msg ) throws Exception
	{			
		try
		{
			/*
			 * Format:
			 * messageId~
			 * workflowMessages[workflowMessageId,referenceId,event,topic,status,requestReferenceId,requestModifiedDate,isMaker,   				<0--7>
			 * ISOrderId,requestClassification,orgShortName,toOrgShortName,userShortName,legPriceName,bidOffer,tenor,dealtCurrencyProperty, 	<8--16>
			 * dealtCurrency,settledCurrency,dealtAmount,tradeReferenceId,tradeModifiedDate,ISExternalId,requestExternalId,tradeClassification, <17--24>
			 * tradeDate,counterpartyA,counterpartyB,tradeState,tradeLegClassification,valueDate,currency1,currency2,baseCurrency, 				<25--33>
			 * variableCurrency,rate,spotRate,forwardPoints,fxRateConvention,currency1Amount,currency2Amount,buyingCurrency1,errorCode, 		<34--42>
			 * errorSeverity,errorParameter,{workflowMessageParameters}]
			 * 
			 * Eg.
			 * 1378839241086~
			 * [1378839241086|3115359201|CREATE|TRADE|SUCCESS|G479697a5514109391c60e|2013-09-10 18:54:01 GMT|false|                          <0--7>
			 * 12406000|QUOTED|FI2|Broker1|Sameer|singleLeg|BID|SPOT|currency1|EUR|USD|1000000.0000||2013-09-10 18:54:01 GMT|                <8--21>
			 * FXI13507002||FXSpot|2013-09-10|FI2-le1|Broker1-le1|TSVERIFIED|FXSPOTLEG|2013-09-12|EUR|USD|EUR|USD|1.21290|1.21290|0.00000|	 <22--37>
			 * STDQOTCNV|1000000.0000|1212900.0000|false||0||{AverageFilledPrice;1.2129}|{SEF;false}|										 <38-->
			 * {LimitRequestReferenceId;G479697a5514109391a9d9}|{AvgFillPrice;1.2129}|{maskedLP;Broker1}|{errorDescription;}|
			 * {PlacedByOrg;FI2}|{FilledAmount;1000000.0}|{RequestDealtAmount;1000000.0}|{LpCrossing;true}|{UnFilledAmount;0.0}|
			 * {RequestFilledAmount;1000000.0}|{ExternalRequestId;3115359201}|{UPI;EUR_USD_SPOT}|{ResponseRecievedTime;1378839241036}|
			 * {RequestTransactionId;FXI13507001}]
			 */
			
			// TODO Remove this log; made it debug
			if ( log.isDebugEnabled() )
			{
				log.debug("CDMessageParser.parse - message - " + msg);
			}
			
			String wfMsg = msg.split("~")[1];
			
			String[] tokens = wfMsg.split("\\|");
			
			String event = tokens[2];
			String topic = tokens[3];
			String status = tokens[4];
			String clsf = tokens[9];
			String orderId = tokens[8];
			
			if ( !event.equalsIgnoreCase("CREATE") || !topic.equalsIgnoreCase("TRADE") || !status.equalsIgnoreCase("SUCCESS") )
			{
				// drop and return ??
				log.debug("CDMessageParser.parse : Recieved message is not CREATE|TRADE|SUCCESS, hence dropping. Message - " + msg);
				return null;
			}
			
			Organization cptyA = ReferenceDataCacheC.getInstance().getOrganization( tokens[10] ); // orgShortName
			Organization cptyB = ReferenceDataCacheC.getInstance().getOrganization( tokens[11] ); // toOrgShortName, Do I need this?
			String tid = tokens[22]; // ISExternalId
			boolean isBuy = tokens[14].equalsIgnoreCase("OFFER") ? true : false;   // bidOffer
			Currency dealtCcy = CurrencyFactory.getCurrency( tokens[17] ); // dealtCurrency
			Currency baseCcy = CurrencyFactory.getCurrency( tokens[33] ); // baseCurrency
			Currency termCcy = CurrencyFactory.getCurrency( tokens[34] ); // variableCurrency
			
			Currency ccy1 = CurrencyFactory.getCurrency( tokens[31] );  // currency1
			Currency ccy2 = CurrencyFactory.getCurrency( tokens[32] );  // currency2
			
			double ccy1Amt = Double.parseDouble( tokens[39] ); // currency1Amount
			double ccy2Amt = Double.parseDouble( tokens[40] ); // currency2Amount
			
			double baseAmount = ccy1.equals(baseCcy) ? ccy1Amt : ccy2Amt;
			double termAmount = ccy1.equals(termCcy) ? ccy1Amt : ccy2Amt;
			
			TradeInfo tF = new TradeInfo();
			tF.setCptyA(cptyA);
			tF.setCptyB(cptyB);
			tF.setTid(tid);
			tF.setBuyingBase(isBuy);
			tF.setDealtCcy(dealtCcy);
			tF.setBaseCcy(baseCcy);
			tF.setTermCcy(termCcy);
			tF.setBaseAmount(baseAmount);
			tF.setTermAmount(termAmount);
			tF.setESP(!ISCommonConstants.RFQ_CREATE_TYPE.equals(clsf));
			tF.setOrderId(orderId);
			
			String mlp = getExtractMaskedLP(msg);
			if( mlp != null && !cptyB.equals(mlp) )
			{
				tF.setMaskedLPTrade(true);
				Organization mlpOrg = ReferenceDataCacheC.getInstance().getOrganization(mlp);
				tF.setMaskedLP(mlpOrg);
				tF.setRealLP(cptyB);
			}
		
			log.info("CDMessageParser.parse - final TradeInfo - " + tF);
			return tF;
		}
		catch(Exception e)
		{
			log.error("CDMessageParser.parse : Error while parsing message, msg - " + msg, e);
			throw e;
		}
	}
	
	
	private String getExtractMaskedLP(String msg)
	{
		int idx = msg.indexOf(ISCommonConstants.MASKED_LP);
		if( idx == -1 )
		{
			return null;
		}
		idx = idx + ISCommonConstants.MASKED_LP.length();
		int endIdx = msg.indexOf('}', idx);
		return msg.substring(idx+1,endIdx);
	}
	
	 private String extractMessage( Object message )
	    {
			try
			{
				ObjectMapper mapper = new ObjectMapper();
				Object[] objects = mapper.readValue(((TextMessage) message).getText(), Object[].class);
				return (String) objects[0];
			}
			catch ( Exception e )
			{
				log.error("CDMessageParser.extractMessage : Error while extracting message content from JMS Message, msg - " + message, e);
			}
			return null;
	    }


}
