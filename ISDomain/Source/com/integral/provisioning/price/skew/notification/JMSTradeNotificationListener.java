package com.integral.provisioning.price.skew.notification;

import javax.jms.Message;
import javax.jms.MessageListener;

import com.integral.finance.trade.configuration.TradeConfiguration;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.ISTextMessageC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.messaging.DefaultMessageListener;
import com.integral.messaging.MessageHolder;
import com.integral.messaging.MessageReceiver;
import com.integral.messaging.MessageReceiverFactory;
import com.integral.messaging.MessagingException;
import com.integral.messaging.RMQMessage;
import com.integral.messaging.config.MessagingConfiguration;
import com.integral.persistence.cache.ReferenceDataCache;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.provisioning.price.skew.SkewServiceFactory;
import com.integral.provisioning.price.skew.config.PriceSkewServiceMbean;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.user.Organization;

public class JMSTradeNotificationListener extends DefaultMessageListener implements TradeNotificationListener, MessageListener
{
	Log log = LogFactory.getLog(this.getClass());
	ReferenceDataCache rdc = ReferenceDataCacheC.getInstance();
	static ServerMBean _serverMBean = ConfigurationFactory.getServerMBean();

	/* (non-Javadoc)
	 * @see com.integral.provisioning.price.skew.notification.TradeNotificationListener#setup(com.integral.provisioning.price.skew.config.PriceSkewServiceMbean)
	 */
	@Override
	public void setup( PriceSkewServiceMbean config )
	{
		String topic = config.getNotificationJMSTopic();
		String selector = ISCommonConstants.KEY_CD_MESSAGE_TYPE+"='dt' AND perm='"+ISConstantsC.CHIEFDEALER_DBVIEW_PERM +"'" ;
		try
		{
            String exchangeName = ServerRuntimeMBean.NOTIFICATION_EXCHANGE;
            String queueName = MessagingConfiguration.getInstance().getUserDBPrefix() + "_" + _serverMBean.getVirtualServerName() + "_" + exchangeName + "_TrdNotificationLstnr";
            try{
            	setupMessageListener(this, exchangeName, queueName);
            }catch(Exception ex){
            	log.error("Error in creating MessaingFramework receiver: ", ex);
            }
		}
		catch ( Exception e )
		{
			log.error("Failed to setup Trade message listener.t="+topic, e);
		}
	}
	
    public static void setupMessageListener(com.integral.messaging.MessageListener listener, String exchangeName, String queueName) throws MessagingException 
    {
        MessageReceiver receiver = MessageReceiverFactory.newMessageReceiver(exchangeName, queueName, false, true, true, false, listener);
        String bindingKey = getBindingKey();
        receiver.addBinding(bindingKey);
    }
    
    private static String getBindingKey()
    {
    	//String selector = ISCommonConstants.KEY_CD_MESSAGE_TYPE+"='dt' AND perm='"+ISConstantsC.CHIEFDEALER_DBVIEW_PERM +"'" ;    	
    	StringBuilder sb = new StringBuilder(50);
    	sb.append("dt").append('.');
    	sb.append(ISConstantsC.CHIEFDEALER_DBVIEW_PERM).append('.');
    	sb.append('*').append('.');
    	sb.append('*');
    	return sb.toString();
    }
    
	@Override
	public void handleMessage( Object message )
	{
		Message jmsMessage = (Message) message;
		try
		{
			String _lpIdx = jmsMessage.getStringProperty(ISCommonConstants.KEY_CD_LP_INDEX);
			if( _lpIdx != null )
			{
				int lpIdx = Integer.parseInt(_lpIdx);
				Organization lp = rdc.getOrganization(lpIdx);
				//TODO CHECK FOR MASKED LP at property level.
				boolean isSkewEnabled  = TradeConfiguration.getInstance().isPriceSkewEnabled(lp);
				if( isSkewEnabled )
				{
					if( jmsMessage != null )
					{
						SkewServiceFactory.getPriceSkewService().handleTradeNotification(jmsMessage, Integer.valueOf(_lpIdx) );
					}
				}
			}
		}
		catch ( Exception e )
		{
			log.error("JMSTNL.handleMessage. message dropped, required properties not found : m="+message ,e);
		}
	}
	
   	@Override
	public void onMessage( Message message )
	{
		handleMessage(message);
	}
   	
    
    @Override
    public void onMessage(RMQMessage message) {
        try {
        	MessageHolder notificationMessage = (MessageHolder) message.getPayload();
        	Message textMessage = new ISTextMessageC(notificationMessage.getMessageProperties(), (String)notificationMessage.getMessage());
            onMessage(textMessage);            
        } catch (Exception e) {
            log.error("JTNL.onMessage : exception while handling Notification message. msg=" + message);
        }
        try {
			message.ack();
		} catch (MessagingException e) {
			log.error("Error in sending the ack for message:" + message.getPayload()
					+ ":routingKey=" + message.getRoutingKey(), e);
		}
    }
}
