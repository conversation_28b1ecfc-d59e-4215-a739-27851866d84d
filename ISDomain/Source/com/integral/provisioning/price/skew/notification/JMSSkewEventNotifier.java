package com.integral.provisioning.price.skew.notification;

import java.util.Map;

import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.messaging.MessageSender;
import com.integral.messaging.MessageSenderFactory;
import com.integral.messaging.MessagingException;
import com.integral.provisioning.price.skew.config.PriceSkewServiceMbean;
import com.integral.system.runtime.ServerRuntimeMBean;

public class JMSSkewEventNotifier implements SkewEventNotifier
{
	Log log = LogFactory.getLog(this.getClass());
	String topic;
	MessageSender sender;

	@Override
	public void setup( PriceSkewServiceMbean config )
	{
		topic = config.getSkewEventNotifierJMSTopic();
		try {
			sender = MessageSenderFactory.newMessageSender(ServerRuntimeMBean.PriceProvision_EXCHANGE);
		} catch (MessagingException msge) {
			MessageLogger.getInstance().log("MESG_INIT_FAILED", JMSSkewEventNotifier.class.getName(), "Error when initializing MessageSender;Check log for more details", null);
		}		
	}

	@Override
	public void sendNotification(Object message, Map<String, String> properties) {
		try {
			sender.sendMessage("PP.SE", message);
		} catch (Exception e) {
			log.warn("JMSSEN.sendNotification#2 .Failed.m=" + message, e);
		}
	}

}
