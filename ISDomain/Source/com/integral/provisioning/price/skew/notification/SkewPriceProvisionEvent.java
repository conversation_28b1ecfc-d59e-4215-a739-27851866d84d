package com.integral.provisioning.price.skew.notification;

import java.util.HashMap;
import java.util.concurrent.atomic.AtomicLong;

import com.integral.provisioning.price.notification.PriceProvisionEvent;

/**
 * <AUTHOR>
 *
 */
public class SkewPriceProvisionEvent implements PriceProvisionEvent
{
	private long seqNo;
	private static AtomicLong num = new AtomicLong(1);
	private static final PriceProvisionEvent.EventType eventType = PriceProvisionEvent.EventType.SKEW;
	private PriceProvisionEvent.ActionType actionType;
	
	/*
	 * Payload should only contain Skew related data.
	 * Null or Empty values allowed
	 */
	private HashMap<String, Object> payload = new HashMap<String, Object>();
	
	public SkewPriceProvisionEvent()
	{
		this.seqNo = num.getAndIncrement();
	}

	@Override
	public EventType getEventType()
	{
		return eventType;
	}
	
	public PriceProvisionEvent.ActionType getActionType()
	{
		return this.actionType;
	}
	
	public void setActionType(PriceProvisionEvent.ActionType actionType)
	{
		this.actionType = actionType;
	}

	@Override
	public long getSeqNo()
	{
		return this.seqNo;
	}

	@Override
	public void setSeqNo(long seqNo) 
	{
		this.seqNo = seqNo;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public HashMap getPayload()
	{
		if( null != payload )
		{
			return payload;
		}
		return new HashMap<String, Object>();		
	}

	@SuppressWarnings("rawtypes")
	@Override
	public void setPayload(HashMap payload)
	{
		this.payload = payload;
	}

	@Override
	public Object getValue(String key)
	{
		if ( null != this.payload )
		{
			return payload.get(key);
		}
		return null;
	}

	@Override
	public void setValue(String key, Object value)
	{
		if ( null != this.payload )
		{
			payload.put(key, value);
		}
	}

	@Override
	public String toString() 
	{
		return "SkewPriceProvisionEvent [seqNo=" + seqNo + ", actionType="
				+ actionType + ", payload=" + payload + "]";
	}
}
