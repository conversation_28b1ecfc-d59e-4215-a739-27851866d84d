package com.integral.provisioning.price.skew.notification;

import java.util.Map;

import com.integral.provisioning.price.skew.config.PriceSkewServiceMbean;


public interface SkewEventNotifier
{
	
	enum SkewEventNotifierTypes {

		JMS(0),
		RMQ(1);
		
		int id;
		
		SkewEventNotifierTypes(int id)
		{
			this.id = id;
		}
		
		public int getId()
		{
			return this.id;
		}
		
		public static SkewEventNotifierTypes getById(int id)
		{
			for ( SkewEventNotifierTypes type : SkewEventNotifierTypes.values() )
			{
				if( type.getId() == id )
				{
					return type;
				}
			}
			return null;
		}
		
	}
	
	public void setup(PriceSkewServiceMbean config);

	public void sendNotification(Object message,Map<String, String> properties);

}
