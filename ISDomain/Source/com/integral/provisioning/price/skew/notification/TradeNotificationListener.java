package com.integral.provisioning.price.skew.notification;

import com.integral.provisioning.price.skew.config.PriceSkewServiceMbean;


public interface TradeNotificationListener
{
	
	enum TradeNotificationListenerTypes {
		JMS(0);
		
		int id;
		
		TradeNotificationListenerTypes(int id)
		{
			this.id = id;
		}
		
		public int getId()
		{
			return this.id;
		}
		
		public static TradeNotificationListenerTypes getById(int id)
		{
			for ( TradeNotificationListenerTypes type : TradeNotificationListenerTypes.values() )
			{
				if( type.getId() == id )
				{
					return type;
				}
			}
			return null;
		}
		
	}
	
	public void setup(PriceSkewServiceMbean config);

	public void handleMessage(Object message);

}
