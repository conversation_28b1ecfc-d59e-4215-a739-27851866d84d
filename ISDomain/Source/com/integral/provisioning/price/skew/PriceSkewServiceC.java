package com.integral.provisioning.price.skew;

import com.integral.finance.currency.CurrencyPair;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.Organization;
import com.integral.provisioning.price.notification.PriceProvisionEvent;
import com.integral.provisioning.price.skew.cache.PriceSkewStrategyCacheC;
import com.integral.provisioning.price.skew.config.PriceSkewServiceConfig;
import com.integral.provisioning.price.skew.config.PriceSkewServiceMbean;
import com.integral.provisioning.price.skew.log.PSSLogger;
import com.integral.provisioning.price.skew.model.PriceSkewStrategy;
import com.integral.provisioning.price.skew.model.PriceSkewStrategyUtilC;
import com.integral.provisioning.price.skew.notification.MessageParser;
import com.integral.provisioning.price.skew.notification.SkewEventNotifier;
import com.integral.provisioning.price.skew.notification.TradeNotificationListener;
import com.integral.provisioning.price.skew.notification.TradeNotificationListener.TradeNotificationListenerTypes;
import com.integral.provisioning.price.skew.processor.Event;
import com.integral.provisioning.price.skew.processor.SkewEventProcessor;
import com.integral.provisioning.price.skew.processor.SkewEventProcessor.SkewEventHandler;
import com.integral.provisioning.price.skew.processor.TradeEventProcessor;
import com.integral.provisioning.price.skew.processor.TradeEventProcessor.TradeEventHandler;
import com.integral.provisioning.price.skew.scheduler.SkewEventScheduler;
import com.lmax.disruptor.RingBuffer;

public class PriceSkewServiceC implements PriceSkewService
{
	Log log = LogFactory.getLog(this.getClass());

	/*
	 * Notifier to send skew event to grid.
	 */
	SkewEventNotifier skewEventNotifier;

	/*
	 * Outbound events processor to send Skew start/stop events to grid.
	 */
	SkewEventProcessor<Event> skewEventProcessor;

	/*
	 * Inbound events processor to process trade notifications.
	 */
	TradeEventProcessor<Event> tradeEventProcessor;

	/*
	 * Listener to receive trade notification.
	 */
	TradeNotificationListener tradeNotificationListener;

	PriceSkewServiceC()
	{
	}

	/**
	 * 
	 */
	@Override
	public String setup( PriceSkewServiceMbean config )
	{
		// 1.check is skew function is on or not.
		// 2. start scheduler

		// 3. setup up skew event notifier
		SkewEventNotifier.SkewEventNotifierTypes nType = SkewEventNotifier.SkewEventNotifierTypes.getById(config.getSkewEventNotifierType());
		skewEventNotifier = SkewServiceFactory.getSkewEventNotifier(nType);
		skewEventNotifier.setup(config);

		// 4. start out bound processors
		skewEventProcessor = new SkewEventProcessor<Event>();
		SkewEventHandler seh = skewEventProcessor.new SkewEventHandler();
		skewEventProcessor.setup(PriceSkewServiceConfig.getInstance(), seh);
		skewEventProcessor.start();

		// 5. start in bound processors
		MessageParser.MessageParserTypes mType = MessageParser.MessageParserTypes.getById(config.getMessageParserType());
		tradeEventProcessor = new TradeEventProcessor<Event>();
		TradeEventHandler teh = tradeEventProcessor.new TradeEventHandler(mType, config);
		tradeEventProcessor.setup(config, teh);
		tradeEventProcessor.start();
		// 6. Change server state to started - notify other services
		// TODO
		
		//7 - Send out reset event.
		PriceProvisionEvent ppe = PriceSkewStrategyUtilC.sendResetSkewEvent();
		PSSLogger.getInstance().logResetSkewEvent(ppe);

		// 8. setup trade listener. start listening new trade notifications. 
		TradeNotificationListenerTypes type = TradeNotificationListenerTypes.getById(config.getNotificationListenerType());
		TradeNotificationListener tradeNotificationListener = SkewServiceFactory.getNotificationListener(type);
		tradeNotificationListener.setup(config);
		
		SkewEventScheduler.getInstance().setup(config);
		
		return null;
	}

	@Override
	public String reconfig()
	{
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void handleTradeNotification( Object message, int orgIndex )
	{
		log.info("PriceSkewServiceC.handleTradeNotification - message - " + message + ", orgIndex - " + orgIndex);
		RingBuffer<Event> buffer = tradeEventProcessor.getRingBuffer(orgIndex);
		long seq = buffer.next();
		Event event = buffer.get(seq);
		event.setData(message);
		buffer.publish(seq);
	}

	@Override
	public void publishSkewEvent( PriceProvisionEvent ppe , int fiOrgIndex )
	{
		RingBuffer<Event> buffer = skewEventProcessor.getRingBuffer(fiOrgIndex);
		long seq = buffer.next();
		Event event = buffer.get(seq);
		event.setData(ppe);
		buffer.publish(seq);
	}

	@Override
	public void handleAdminNotification(Organization fi, Organization lp, CurrencyPair currencyPair) 
	{
		PriceSkewStrategy strategy = PriceSkewStrategyCacheC.getInstance().get(fi, lp, currencyPair);
		if ( strategy != null )
		{
			log.info("PriceSkewServiceC.handleAdminNotification - Skew params changed at admin for FI=" + fi.getShortName()
					+ ", LP=" + lp.getShortName() + ", CCYP=" + currencyPair.getName());
			strategy.handleChanged();
		}
		else
		{
			if ( log.isDebugEnabled() )
			{
				log.debug("PriceSkewServiceC.handleAdminNotification : Strategy does not exists in cache for FI=" + fi.getShortName()
						+ ", LP=" + lp.getShortName() + ", CCYP=" + currencyPair.getName() + ", skipping.");
			}
		}
	}
}
