package com.integral.provisioning.price.skew.config;


public interface PriceSkewServiceMbean extends com.integral.system.configuration.IdcMBean
{
	enum ServiceMode
	{
		client,server,clientserver
	}
	
    public static String Key_Inbound_Processor_ConcurrencyFactor = "Idc.SkewService.Notification.InBound.Processor.ConcurrencyFactor";
    public static String Key_Inbound_Processor_BufferSize = "Idc.SkewService.Notification.InBound.Processor.BufferSize";

    public static String Key_Outbound_Processor_ConcurrencyFactor = "Idc.SkewService.Notification.OutBound.Processor.ConcurrencyFactor";
    public static String Key_Outbound_Processor_BufferSize = "Idc.SkewService.Notification.OutBound.Processor.BufferSize";

    public static String Key_Trade_Msg_Parser_Type = "Idc.SkewService.Notification.Trade.MessageParser";
    public static String Key_Trade_NotificationListener_Type = "Idc.SkewService.Notification.Trade.NotificationListener"; 
    public static String Key_Trade_Notification_JMS_Topic = "Idc.SkewService.Notification.Trade.JMS.Topic";
    
    public static String Key_SkewEvent_Notifier_Type = "Idc.SkewService.Notification.SkewEvent.Notifier";
    public static String Key_SkewEvent_Notifier_JMS_Topic = "Idc.SkewService.Notification.SkewEvent.Notifier.JMS.Topic";
    
    public static String Key_SkewService_Mode = "Idc.SkewService.Mode";
    
    public static String Key_SkewService_Log_Catogory = "Idc.SkewService.log.category";
    
    public static String Key_Periodic_Update_Interval = "Idc.SkewService.Notification.SkewEvent.Periodic.Update.Interval";
    
    public static String Key_SkewService_Supported_Orgs = "Idc.SkewService.Supported.Orgs";
    
    
    public int getInboundProcessorCurrencyFactor();
    
    public int getInboundProcessorBufferSize();
    
    public int getOutboundProcessorCurrencyFactor();
    
    public int getOutboundProcessorBufferSize();
    
    public int getMessageParserType();
    
    public int getNotificationListenerType();
    
    public String getNotificationJMSTopic();

    public int getSkewEventNotifierType();
    
    public String getSkewEventNotifierJMSTopic();
    
    public String getServiceMode();
    
    public String getLogCategory();
    
    public long getPeriodicUpdateInterval();
    
    public boolean isSupportedOrg(String orgName);
    
}
