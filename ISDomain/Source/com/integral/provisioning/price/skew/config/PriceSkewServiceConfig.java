
package com.integral.provisioning.price.skew.config;

import java.util.Map;

import com.integral.provisioning.price.skew.notification.MessageParser;
import com.integral.provisioning.price.skew.notification.SkewEventNotifier;
import com.integral.provisioning.price.skew.notification.TradeNotificationListener;
import com.integral.system.configuration.IdcMBeanC;

@SuppressWarnings("serial")
public class PriceSkewServiceConfig extends IdcMBeanC implements PriceSkewServiceMbean
{
	private int inBPCurrencyFactor;
	private int inBPBufferSize;
	private int outBPCurrencyFactor;
	private int outBPBufferSize;
	private String trdNotificationJMSTopic;
	private int trdNotificationParserType;
	private int trdNotificationListenerType;
	private int skewEventNotifierType;
	private String skewEventJMSTopic;
	private String serviceMode;
	private String logCategory;
	private long periodicUpdateInterval;
	private Map<String, Boolean> supportedOrgs;
	
	
	private PriceSkewServiceConfig() {
		super("com.integral.provisioning.price.skew.PriceSkewServiceMbean");
	}
	
	private static class Holder {
		public static final PriceSkewServiceMbean INSTANCE = new PriceSkewServiceConfig();
	}
	
	public static PriceSkewServiceMbean getInstance()
	{
		return Holder.INSTANCE;
	}

	
	@Override
	public void initialize()
	{
		inBPCurrencyFactor = getIntProperty(Key_Inbound_Processor_ConcurrencyFactor, 0);
		inBPBufferSize = getIntProperty(Key_Inbound_Processor_BufferSize, 1000);
		outBPCurrencyFactor = getIntProperty(Key_Outbound_Processor_ConcurrencyFactor, 0);
		outBPBufferSize = getIntProperty(Key_Outbound_Processor_BufferSize, 1000);
		trdNotificationJMSTopic = getStringProperty(Key_Trade_Notification_JMS_Topic, null);
		trdNotificationParserType = getIntProperty(Key_Trade_Msg_Parser_Type, MessageParser.MessageParserTypes.CD.getId());
		trdNotificationListenerType	= getIntProperty(Key_Trade_NotificationListener_Type, TradeNotificationListener.TradeNotificationListenerTypes.JMS.getId());	
		skewEventNotifierType = getIntProperty(Key_SkewEvent_Notifier_Type, SkewEventNotifier.SkewEventNotifierTypes.JMS.getId());
		skewEventJMSTopic = getStringProperty(Key_SkewEvent_Notifier_JMS_Topic, null);
		serviceMode = getStringProperty(Key_SkewService_Mode, ServiceMode.client.name());
		logCategory = getStringProperty(Key_SkewService_Log_Catogory, null);
		periodicUpdateInterval = getLongProperty(Key_Periodic_Update_Interval, 300*1000);
		initSupportedOrgs(getStringProperty(Key_SkewService_Supported_Orgs, "ALL"));
	}

	@Override
	public int getInboundProcessorCurrencyFactor()
	{
		return inBPCurrencyFactor;
	}


	@Override
	public int getInboundProcessorBufferSize()
	{
		return inBPBufferSize;
	}


	@Override
	public int getOutboundProcessorCurrencyFactor()
	{
		return outBPCurrencyFactor;
	}


	@Override
	public int getOutboundProcessorBufferSize()
	{
		return outBPBufferSize;
	}


	@Override
	public int getMessageParserType()
	{
		return trdNotificationParserType;
	}


	@Override
	public int getNotificationListenerType()
	{
		return trdNotificationListenerType;
	}


	@Override
	public String getNotificationJMSTopic()
	{
		return trdNotificationJMSTopic;
	}


	@Override
	public int getSkewEventNotifierType()
	{
		return skewEventNotifierType;
	}


	@Override
	public String getSkewEventNotifierJMSTopic()
	{
		return skewEventJMSTopic;
	}


	/* (non-Javadoc)
	 * @see com.integral.provisioning.price.skew.config.PriceSkewServiceMbean#getServiceMode()
	 */
	@Override
	public String getServiceMode()
	{
		return serviceMode;
	}


	@Override
	public String getLogCategory() 
	{
		return logCategory;
	}


	/* (non-Javadoc)
	 * @see com.integral.provisioning.price.skew.config.PriceSkewServiceMbean#getPeriodicUpdateInterval()
	 */
	@Override
	public long getPeriodicUpdateInterval()
	{
		return periodicUpdateInterval;
	}


	/* (non-Javadoc)
	 * @see com.integral.provisioning.price.skew.config.PriceSkewServiceMbean#isSupportedOrg()
	 */
	@Override
	public boolean isSupportedOrg(String orgName) 
	{
		if(supportedOrgs.containsKey("ALL"))
		{
			return true;
		}
		return supportedOrgs.containsKey(orgName);
	}
	
	private void initSupportedOrgs(String shortNames)
	{
		supportedOrgs = PriceSkewServiceConfigUtilC.populateMap(shortNames);
	}

}
