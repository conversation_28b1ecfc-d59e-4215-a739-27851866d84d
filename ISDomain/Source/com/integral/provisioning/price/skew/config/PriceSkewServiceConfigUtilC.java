package com.integral.provisioning.price.skew.config;

import java.util.concurrent.ConcurrentHashMap;

import com.integral.util.StringUtilC;

/**
 * <AUTHOR>
 *
 */
public class PriceSkewServiceConfigUtilC 
{
	/** Assuming input to be comma separated providers (broker) short names, it will give back
	 * map of shortname,boolean
	 * @param shortNames (eg. "Broker1,GrupoSantander")
	 * @return ConcurrentMap<String, Boolean> of shortname,true/false
	 */
	public static ConcurrentHashMap<String, Boolean> populateMap(String shortNames)
	{
		ConcurrentHashMap<String, Boolean> map = new ConcurrentHashMap<String, Boolean>();
		
		if(StringUtilC.isNullOrEmpty(shortNames))
		{
			return map;
		}

		String[] providerNames = shortNames.split(",");
		for(String name : providerNames)
		{
			name = name.trim();
			map.put(name, true);
		}
		return map;
	}
}
