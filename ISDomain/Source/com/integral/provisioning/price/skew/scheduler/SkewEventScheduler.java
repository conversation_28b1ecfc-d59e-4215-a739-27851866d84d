package com.integral.provisioning.price.skew.scheduler;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.provisioning.price.skew.cache.PriceSkewStrategyCacheC;
import com.integral.provisioning.price.skew.config.PriceSkewServiceMbean;
import com.integral.provisioning.price.skew.model.PriceSkewStrategy;
import com.integral.util.StringUtilC;

/**
 * <AUTHOR>
 *
 */
public class SkewEventScheduler 
{
	protected ScheduledThreadPoolExecutor timer = new ScheduledThreadPoolExecutor(1, new SkewSchedulerThreadFactory("SkewSchedulerWorkers"));
	protected Map<String, ScheduledFuture> scheduledSkewExpiryTasks = new ConcurrentHashMap<String, ScheduledFuture>();
	
	protected Log log = LogFactory.getLog(this.getClass());

	
	private static class Holder
	{
		public static SkewEventScheduler INSTANCE = new SkewEventScheduler();
	}
	
	private SkewEventScheduler()
	{
		
	}
	
	public static SkewEventScheduler getInstance()
	{
		return Holder.INSTANCE;
	}
	
	public void setup(PriceSkewServiceMbean config)
	{
		log.info("SES.setup . completed. periodic interval = " + config.getPeriodicUpdateInterval());
		timer.scheduleAtFixedRate(new HeartbeatTask(), config.getPeriodicUpdateInterval(), config.getPeriodicUpdateInterval(), TimeUnit.MILLISECONDS);
	}
	
	public void stop()
	{
		timer.shutdown();
	}
	
	public boolean scheduleExpiry( PriceSkewStrategy skewStrategy, long expiryTimeMillis )
	{
		if ( expiryTimeMillis <= 0 )
		{
			log.info(new StringBuilder(200).append("SkewEventScheduler.scheduleExpiry : Cannot schedule skew expiry. Negative/zero expiry time=")
					.append(expiryTimeMillis).append(", skewStrategy=").append(skewStrategy.toString()).toString());
			return false;
		}
		
		long absoluteExpTime = System.currentTimeMillis() + expiryTimeMillis;
		log.info(new StringBuilder(200).append("SkewEventScheduler.scheduleExpiry : Schedule skew expiry for skewStrategy=").append(skewStrategy.toString())
				.append(", expiryPeriod=").append(expiryTimeMillis).append(", absExpiryTime=").append(absoluteExpTime).toString());
		
		final SkewExpiryTask set = new SkewExpiryTask(skewStrategy);
		ScheduledFuture taskHandle = timer.schedule(set, expiryTimeMillis, TimeUnit.MILLISECONDS);
		scheduledSkewExpiryTasks.put(getSkewExpiryEventKey( skewStrategy ), taskHandle);
		return true;
	}
	
	public boolean rescheduleExpiry( PriceSkewStrategy skewStrategy, long expiryTimeMillis )
	{
		if ( expiryTimeMillis <= 0 )
		{
			log.info(new StringBuilder(200).append("SkewEventScheduler.rescheduleExpiry : Cannot reschedule skew expiry. Negative/zero expiry time=")
					.append(expiryTimeMillis).append(", skewStrategy=").append(skewStrategy.toString()).toString());
			return false;
		}
		
		long absoluteExpTime = System.currentTimeMillis() + expiryTimeMillis;
		log.info(new StringBuilder(200).append("SkewEventScheduler.rescheduleExpiry : Reschedule skew expiry for skewStrategy=").append(skewStrategy.toString())
				.append(", expiryPeriod=").append(expiryTimeMillis).append(", absExpiryTime=").append(absoluteExpTime).toString());
		
		String key = getSkewExpiryEventKey( skewStrategy );
		ScheduledFuture oldTaskHandle = scheduledSkewExpiryTasks.get(key);
		boolean isCancelled = oldTaskHandle.cancel(false);
		
		if ( isCancelled )
		{
			scheduledSkewExpiryTasks.remove(key);
			final SkewExpiryTask set = new SkewExpiryTask(skewStrategy);
			ScheduledFuture taskHandle = timer.schedule(set, expiryTimeMillis, TimeUnit.MILLISECONDS);
			scheduledSkewExpiryTasks.put(getSkewExpiryEventKey( skewStrategy ), taskHandle);
			return true;
		}
		else
		{
			log.error("SkewEventScheduler.rescheduleExpiry : Old skewEvent cancel failed, cannot reschedule new skewStrategy=" + skewStrategy);
		}
		return false;
	}
	
	public boolean cancel( PriceSkewStrategy skewStrategy )
	{
		String key = getSkewExpiryEventKey( skewStrategy );
		ScheduledFuture oldTaskHandle = scheduledSkewExpiryTasks.get(key);
		
		boolean isCancelled = false;
		if ( null != oldTaskHandle )
		{
			isCancelled = oldTaskHandle.cancel(false);
			if ( isCancelled )
			{
				scheduledSkewExpiryTasks.remove(key);
				return true;
			}
			else
			{
				log.debug("SkewEventScheduler.cancel : Cannot cancel skewStrategy=" + skewStrategy);
			}
		}
		else
		{
			log.debug("SkewEventScheduler.cancel : No expiry task for skew strategy - " + skewStrategy);
		}
		return false;
	}
	
	private static class SkewSchedulerThreadFactory implements ThreadFactory
	{
		AtomicInteger threadNo = new AtomicInteger(1);
		ThreadGroup tg = null;
		
		public SkewSchedulerThreadFactory( String name )
		{
			tg = new ThreadGroup(name);
		}
		
		public Thread newThread( Runnable runnable )
		{
			return new Thread( tg, runnable, "SkewSchedulerWorker-" + threadNo.getAndIncrement() );
		}
	}
	
	class SkewExpiryTask implements Runnable
	{
		PriceSkewStrategy skewStrategy = null;

		public SkewExpiryTask( PriceSkewStrategy skewStrategy )
		{
			this.skewStrategy =	skewStrategy;
		}

		/* (non-Javadoc)
		   * @see java.util.TimerTask#run()
		   */
		public void run()
		{
			// This method should never throw an exception because any exception from this code block will stop the timer itself.
			try
			{
				try
				{
					log.info("SES.SET.run : SKEW-EXPIRED for " + getSkewExpiryEventKey(skewStrategy));
					skewStrategy.handleExpired();
				}
				catch ( Exception e )
				{
					log.error("SES.SET.run : Failed. skewStrategy - " + skewStrategy.toString(), e);
				}
				finally
				{
					scheduledSkewExpiryTasks.remove(getSkewExpiryEventKey(skewStrategy));
				}
			}
			catch ( Exception e )
			{
				log.error("SES.SET.run : Exception happened in the skew timer task run - skewStrategy " + skewStrategy.toString(), e);
			}
		}

		public void cancel()
		{
			this.skewStrategy = null;
		}

		public String toString()
		{
			return new StringBuilder(128).append(this.getClass().getName()).append("@SkewEventKey").append(skewStrategy != null ? getSkewExpiryEventKey(skewStrategy) : null).toString();
		}
	}
	
	
	class HeartbeatTask implements Runnable
	{

		@Override
		public void run() 
		{
			try
			{
				PriceSkewStrategy strategy = null;
				Map<String, PriceSkewStrategy> cache = PriceSkewStrategyCacheC.getInstance().getCacheContent();
				for ( Map.Entry<String, PriceSkewStrategy> key : cache.entrySet() )
				{
					// Send start or stop events (kindof heartbeat event)
					strategy = key.getValue();
					strategy.sendHeartbeatEvent();
				}
			}
			catch ( Exception e )
			{
				log.error("SES.HBT.run : Exception happened in Heartbeat task", e);
			}
		}
	}
	
	
	/**
	 * @param skewStrategy
	 * @return String key a combination of <FI>_<LP>_<CCYP>
	 */
	public static String getSkewExpiryEventKey( PriceSkewStrategy skewStrategy )
	{
		String key;
		String fi = skewStrategy.getFI().getShortName();
		String lp = skewStrategy.getLP().getShortName();
		String ccyp = skewStrategy.getCcyp().toString();
		if ( StringUtilC.isNullOrEmpty(fi) || StringUtilC.isNullOrEmpty(lp) || StringUtilC.isNullOrEmpty(ccyp) )
		{
			throw new IllegalArgumentException("SkewEventScheduler.getSkewExpiryEventKey : Missing paramater in skewStrategy - " + skewStrategy );
		}
		key = fi + "_" + lp + "_" + ccyp;
		return key;
	}

}
