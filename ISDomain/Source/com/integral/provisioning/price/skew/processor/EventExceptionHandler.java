/**
 * <AUTHOR>
 */
package com.integral.provisioning.price.skew.processor;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.lmax.disruptor.ExceptionHandler;

/**
 * <AUTHOR>
 *
 */
public class EventExceptionHandler implements ExceptionHandler
{
	Log log = LogFactory.getLog(this.getClass());

	/* (non-Javadoc)
	 * @see com.lmax.disruptor.ExceptionHandler#handleEventException(java.lang.Throwable, long, java.lang.Object)
	 */
	@Override
	public void handleEventException( Throwable ex, long sequence, Object event )
	{
		log.error("EEH. Failed to process event=" + event + ",sequence=" + sequence, ex);
	}

	/* (non-Javadoc)
	 * @see com.lmax.disruptor.ExceptionHandler#handleOnStartException(java.lang.Throwable)
	 */
	@Override
	public void handleOnStartException( Throwable ex )
	{
		log.error("EEH. Failed to start batch event processor", ex);
	}

	/* (non-Javadoc)
	 * @see com.lmax.disruptor.ExceptionHandler#handleOnShutdownException(java.lang.Throwable)
	 */
	@Override
	public void handleOnShutdownException( Throwable ex )
	{
		log.error("EEH. Failed to shutdown batch event processor", ex);
	}

}
