package com.integral.provisioning.price.skew.processor;

import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.provisioning.price.notification.PriceProvisionEvent;
import com.integral.provisioning.price.notification.PriceProvisionEventSerializer;
import com.integral.provisioning.price.notification.PriceProvisionEventSerializerFactory;
import com.integral.provisioning.price.skew.SkewServiceFactory;
import com.integral.provisioning.price.skew.config.PriceSkewServiceConfig;
import com.integral.provisioning.price.skew.config.PriceSkewServiceMbean;
import com.integral.provisioning.price.skew.notification.SkewEventNotifier;
import com.lmax.disruptor.BatchEventProcessor;
import com.lmax.disruptor.EventHandler;
import com.lmax.disruptor.EventProcessor;
import com.lmax.disruptor.MultiThreadedLowContentionClaimStrategy;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.SequenceBarrier;
import com.lmax.disruptor.SleepingWaitStrategy;
import com.lmax.disruptor.dsl.Disruptor;

public class SkewEventProcessor<T>
{
	protected ExecutorService executors[];
	protected Disruptor<T> disruptors[];
	protected RingBuffer<T> ringBuffers[];
	protected EventProcessor eventProcessors[];
	protected Log log = LogFactory.getLog(this.getClass());
	int concurrencyFactor;

	@SuppressWarnings("unchecked")
	public void setup( PriceSkewServiceMbean config, EventHandler<T> eventHandler )
	{
		concurrencyFactor = config.getOutboundProcessorCurrencyFactor();
		final int bufferSize = config.getOutboundProcessorBufferSize();

		executors = new ExecutorService[concurrencyFactor];
		disruptors = new Disruptor[concurrencyFactor];
		ringBuffers = new RingBuffer[concurrencyFactor];
		eventProcessors = new EventProcessor[concurrencyFactor];

		for ( int i = 0 ; i < concurrencyFactor ; i++ )
		{
			this.executors[i] = Executors.newSingleThreadExecutor(SkewServiceFactory.getFactory().newThreadFactory("SkewEventProcessor-" + i));
			disruptors[i] = new Disruptor<T>(new DefaultEventFactory(), executors[i], new MultiThreadedLowContentionClaimStrategy(bufferSize), new SleepingWaitStrategy());
			ringBuffers[i] = disruptors[i].getRingBuffer();
			SequenceBarrier sequenceBarrier = ringBuffers[i].newBarrier();
			eventProcessors[i] = new BatchEventProcessor<T>(ringBuffers[i], sequenceBarrier, eventHandler);
			disruptors[i].handleEventsWith(eventProcessors[i]);
		}
		log.info("SS.SEP.setup - complete.cf="+concurrencyFactor+",bSize="+bufferSize);

	}

	public void start()
	{
		for ( Disruptor<T> disruptor : disruptors )
		{
			disruptor.start();
		}
	}

	public class SkewEventHandler implements EventHandler<Event>
	{
		SkewEventNotifier notifier;
		PriceProvisionEventSerializer serializer;

		public SkewEventHandler()
		{
			int typ = PriceSkewServiceConfig.getInstance().getSkewEventNotifierType();
			SkewEventNotifier.SkewEventNotifierTypes type = SkewEventNotifier.SkewEventNotifierTypes.getById(typ);
			notifier = SkewServiceFactory.getSkewEventNotifier(type);

			serializer = PriceProvisionEventSerializerFactory.getInstance().getPriceProvisionEventSerializer("skew");
		}

		@Override
		public void onEvent( Event event, long sequence, boolean endOfBatch ) throws Exception
		{
			PriceProvisionEvent data = (PriceProvisionEvent) event.getData();
			Map<String, String> props = data.getPayload();
			String message = serializer.serialize(data);
			notifier.sendNotification(message,props);
		}

	}

	/**
	 * @param orgIndex
	 * @return
	 */
	public RingBuffer<T> getRingBuffer( int orgIndex )
	{
		int idx = orgIndex % concurrencyFactor;
		return ringBuffers[idx];
	}

}
