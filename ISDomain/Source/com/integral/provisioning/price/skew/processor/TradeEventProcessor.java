package com.integral.provisioning.price.skew.processor;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.skew.SkewSpread;
import com.integral.finance.dealing.skew.SkewStrategy;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.provisioning.price.skew.SkewServiceFactory;
import com.integral.provisioning.price.skew.cache.PriceSkewStrategyCacheC;
import com.integral.provisioning.price.skew.cache.SkewSpreadCacheC;
import com.integral.provisioning.price.skew.config.PriceSkewServiceMbean;
import com.integral.provisioning.price.skew.log.PSSLogger;
import com.integral.provisioning.price.skew.model.PriceSkewStrategy;
import com.integral.provisioning.price.skew.model.TradeInfo;
import com.integral.provisioning.price.skew.notification.MessageParser;
import com.integral.user.Organization;
import com.lmax.disruptor.BatchEventProcessor;
import com.lmax.disruptor.EventHandler;
import com.lmax.disruptor.EventProcessor;
import com.lmax.disruptor.MultiThreadedLowContentionClaimStrategy;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.SequenceBarrier;
import com.lmax.disruptor.SleepingWaitStrategy;
import com.lmax.disruptor.dsl.Disruptor;

public class TradeEventProcessor<T>
{
	int concurrencyFactor;
	protected ExecutorService executors[];
	protected Disruptor<T> disruptors[];
	protected RingBuffer<T> ringBuffers[];
	protected EventProcessor eventProcessors[];
	protected Log log = LogFactory.getLog(this.getClass());

	@SuppressWarnings("unchecked")
	public void setup( PriceSkewServiceMbean config, EventHandler<T> eventHandler )
	{
		concurrencyFactor = config.getInboundProcessorCurrencyFactor();
		final int bufferSize = config.getInboundProcessorBufferSize();

		executors = new ExecutorService[concurrencyFactor];
		disruptors = new Disruptor[concurrencyFactor];
		ringBuffers = new RingBuffer[concurrencyFactor];
		eventProcessors = new EventProcessor[concurrencyFactor];

		for ( int i = 0 ; i < concurrencyFactor ; i++ )
		{
			this.executors[i] = Executors.newSingleThreadExecutor(SkewServiceFactory.getFactory().newThreadFactory("TradeEventProcessor-" + i));
			disruptors[i] = new Disruptor<T>(new DefaultEventFactory(), executors[i], new MultiThreadedLowContentionClaimStrategy(bufferSize), new SleepingWaitStrategy());
			ringBuffers[i] = disruptors[i].getRingBuffer();
			SequenceBarrier sequenceBarrier = ringBuffers[i].newBarrier();
			eventProcessors[i] = new BatchEventProcessor<T>(ringBuffers[i], sequenceBarrier, eventHandler);
			disruptors[i].handleEventsWith(eventProcessors[i]);
		}

		log.info("SS.TEP.setup - complete.cf=" + concurrencyFactor + ", bSize=" + bufferSize);
	}

	public void start()
	{
		for ( Disruptor<T> disruptor : disruptors )
		{
			disruptor.start();
		}
	}

	public RingBuffer<T> getRingBuffer(int orgIndex)
	{
		int i = orgIndex % concurrencyFactor;
		return ringBuffers[i];
	}

	public class TradeEventHandler implements EventHandler<Event>
	{
		MessageParser parser;
		PriceSkewServiceMbean config;

		public TradeEventHandler( MessageParser.MessageParserTypes type, PriceSkewServiceMbean config )
		{
			parser = SkewServiceFactory.getParser(type);
			this.config = config;
		}

		@Override
		public void onEvent( Event event, long sequence, boolean endOfBatch )
		{
			try
			{
				Object message = event.getData();
				TradeInfo trdInfo = null ;
				try
				{
					trdInfo = parser.parse(message);
				}
				catch ( Exception e )
				{
					if( log.isDebugEnabled() )
					{
						log.debug("TEH.onEvent : Failed to parse message. Exception - " ,e);
					}
				}
				if( trdInfo == null )
				{
					if( log.isDebugEnabled() )
					{
						log.debug("TEH.onEvent message parsing failed. message=" + message);
					}
					return;
				}
				else if( !trdInfo.isESP() )
				{
					if( log.isDebugEnabled() )
					{
						log.debug("TEH.onEvent Not an ESP trade . trd=" + trdInfo.getTid());
					}
					return;
				}

				log.info("TEH.onEvent Received Trade=" + trdInfo);

				Organization fi = trdInfo.getCptyA();
				Organization lp = trdInfo.getCptyB();

				String orgName = lp.getShortName();
				if( trdInfo.isMaskedLPTrade() )
				{
					Organization mlp = trdInfo.getMaskedLP();
					if( mlp != null && mlp.isMasked() && mlp.isMaskLPProvisioning() )
					{
						//change cpty for strategy to masked lp.
						trdInfo.setCptyB(mlp);
					}
					orgName = trdInfo.getRealLP().getShortName();
				}

				// Allow only if LP (or its real LP in case of masked LP) is supported
				if( !this.config.isSupportedOrg(orgName) )
				{
					if ( log.isDebugEnabled() )
					{
						log.debug("TEH.onEvent - CptyB/lp - " + orgName + " is not supported by skew server");
					}
					return;
				}

				//if LP is masked then check if real LP is broker or not. if not masked then check with real lp.
	//			if( trdInfo.isMaskedLPTrade() && !trdInfo.getRealLP().isBroker() )
	//			{
	//
	//				if( log.isDebugEnabled() )
	//				{
	//					log.debug("TEH.onEvent - real CptyB.lp is not a broker .fi="+fi.getShortName()+
	//						",lp="+lp.getShortName()+
	//						",maskedLP="+trdInfo.getMaskedLP().getShortName()+
	//						",cp="+trdInfo.getBaseCcy() + "/" + trdInfo.getTermCcy() );
	//				}
	//				return;
	//			}
	//			else if(  !trdInfo.isMaskedLPTrade() && !lp.isBroker() )
	//			{
	//				if( log.isDebugEnabled() )
	//				{
	//					log.debug("TEH.onEvent - CptyB/lp is not a broker .fi="+fi.getShortName()+
	//						",lp="+lp.getShortName()+
	//						",cp="+trdInfo.getBaseCcy() + "/" + trdInfo.getTermCcy() );
	//				}
	//				return;
	//			}
	//			else if( fi.isBroker() || fi.isPrimeBroker() )
	//			{
	//				if( log.isDebugEnabled() )
	//				{
	//					log.debug("TEH.onEvent - CptyA/fi is a broker or prime broker.fi="+fi.getShortName()+
	//						",lp="+lp.getShortName()+
	//						",cp="+trdInfo.getBaseCcy() + "/" + trdInfo.getTermCcy() );
	//				}
	//				return;
	//			}

				Currency bc = trdInfo.getBaseCcy();
				Currency tc = trdInfo.getTermCcy();

				CurrencyPair ccyp = CurrencyFactory.getCurrencyPair(bc, tc);


				//	check if strategy exist - Use strategy cache...(Sameer already wrote it)

				//1. if strategy doesn't then look into SkewSpread cache at ccyp level.
					//1.1 if skewing is not enabled at ccyp level then drop message.
				//2. If exist then update strategy with latest tradeInfo

				PriceSkewStrategyCacheC cache = SkewServiceFactory.getPriceSkewStrategyCacheC();
				PriceSkewStrategy strategy = cache.get(fi, trdInfo.getCptyB(), ccyp);

				if( strategy != null )
				{
					strategy.addTrade(trdInfo);
				}
				else
				{
					SkewSpread ss = SkewSpreadCacheC.getInstance().get(fi, trdInfo.getCptyB(), ccyp);
					if( ss != null && ss.isEnabled() )
					{
						SkewStrategy skewStrategy = ss.getSkewStrategy();
						if ( skewStrategy != null && skewStrategy.isEnabled() )
						{
							PriceSkewStrategy newStrategy = new PriceSkewStrategy(fi, trdInfo.getCptyB(), ccyp);
							cache.put(fi, trdInfo.getCptyB(), ccyp, newStrategy);
							PSSLogger.getInstance().logSkewStrategyCreation(newStrategy);
							newStrategy.addTrade(trdInfo);
						}
						else
						{
							if( log.isDebugEnabled() )
							{
								log.debug("TEH.onEvent : Skew spread is not enabled at strategy level for FI=" + fi.getShortName()
										+ ", LP=" + trdInfo.getCptyB().getShortName() + ", CCPY=" + ccyp.getName());
							}
						}
					}
					else
					{
						if( log.isDebugEnabled() )
						{
							log.debug("TEH.onEvent : Skew spread is not enabled at currency pair level for FI=" + fi.getShortName()
									+ ", LP=" + trdInfo.getCptyB().getShortName() + ", CCPY=" + ccyp.getName());
						}
					}
				}
			}
			catch ( Exception e )
			{
				log.error ( "TEH.onEvent : Exception in handling the trade notification. event=" + getEventDetails( event ), e );
			}

		}

		private String getEventDetails( Event event )
		{
			try
			{
				new StringBuilder( 200 ).append( event.getData() );
			}
			catch ( Exception e )
			{
				log.error( "TEH.getEventDetails : exception", e );
			}
			return null;
		}

	}

}
