/**
 * <AUTHOR>
 */
package com.integral.provisioning.price.skew.processor;

import com.lmax.disruptor.EventFactory;

/**
 * Sample implementation of com.lmax.disruptor.EventFactory
 * <AUTHOR>
 *
 */
public class DefaultEventFactory implements EventFactory
{
	/* (non-Javadoc)
	 * @see com.lmax.disruptor.EventFactory#newInstance()
	 */
	@Override
	public Event newInstance()
	{
		return new Event();
	}

}
