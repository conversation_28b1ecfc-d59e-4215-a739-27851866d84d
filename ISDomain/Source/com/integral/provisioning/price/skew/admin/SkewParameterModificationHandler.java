package com.integral.provisioning.price.skew.admin;

import java.util.List;
import java.util.Map;

import com.integral.exception.IdcException;
import com.integral.finance.dealing.priceProvision.PriceProvisionConstants;
import com.integral.is.priceprovision.PriceProvisionInput;
import com.integral.is.priceprovision.PriceProvisionInputBuilder;
import com.integral.is.priceprovision.util.PriceProvisionUtilC;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.provisioning.price.skew.SkewServiceFactory;
import com.integral.provisioning.price.skew.cache.PriceProvisionSkewRuleCacheManager;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.PriceProvision;


public class SkewParameterModificationHandler implements MessageHandler
{
	private static final String ACTION_KEY 		= "ACTION";
	private static final String ACTION_ADMIN 	= "ADMINUI";
	
	@Override
	public Message handle(Message message) throws IdcException 
	{
		@SuppressWarnings("rawtypes")
		Map props = message.getMap();
		String providerOrgName = ( String ) props.get( PriceProvisionConstants.PROVIDER_ORGANIZATION );
        String fiOrgNames = ( String ) props.get( PriceProvisionConstants.FI_ORGANIZATIONS );
        String action = ( String ) props.get( ACTION_KEY );
        
        Organization lpOrg = (Organization) ReferenceDataCacheC.getInstance().getEntityByShortName(providerOrgName, OrganizationC.class, null, 'A');
        List<Organization> fiOrgs = PriceProvisionUtilC.getOrganizations(fiOrgNames);
        
        if ( action.equals(ACTION_ADMIN) )
        {
        	handleSkewRuleUpdate( lpOrg, fiOrgs );
        }
		return message;
	}
	
	private void handleSkewRuleUpdate(Organization lpOrg, List<Organization> fiOrgs) 
    {
    	PriceProvisionSkewRuleCacheManager ppsrcm = SkewServiceFactory.getPriceProvisionSkewRuleCacheManager();
    	for( Organization fiOrg : fiOrgs )
    	{
    		Map<String, List<PriceProvision>> priceProvisionMap = PriceProvisionUtilC.retrievePriceProvisions(fiOrg, lpOrg);
    		for ( Map.Entry<String, List<PriceProvision>> ccPair : priceProvisionMap.entrySet() )
    		{
    			PriceProvisionInput ppi = PriceProvisionInputBuilder.buildInput(lpOrg, fiOrg, ccPair.getKey());
    			List<PriceProvision> pps = ccPair.getValue();
    			ppsrcm.notify(ppi, pps);
    		}
    	}
	}
}
