package com.integral.persistence.test;

import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.FXLeg;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.fx.FXTrade;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.trade.Tenor;
import com.integral.jmsx.JMSManager;
import com.integral.message.EntityReference;
import com.integral.message.MappedEntityReference;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.Entity;
import com.integral.persistence.ExternalSystemId;
import com.integral.persistence.PersistenceFactory;
import com.integral.system.mail.SendEmailC;
import com.integral.system.monitor.DiagnosticC;
import com.integral.user.DisplayPreference;
import com.integral.user.User;

import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.ObjectMessage;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.Vector;


/**
 * Tests business events and demonstrates how to send an email for a event
 */
public class BusinessEventTestC
        extends DiagnosticC {
    public static final String TRD_CLSF_SP = "FXSpot";
    public static final String TRD_CLSF_OR = "FXOutright";
    public static final String TRD_CLSF_SWAP = "FXSpotFwd";
    public static final String TRD_CLSF_FWD = "FXFwdFwd";
    public static String LOGO_PATH = null;
    static String name = "BusinessEventTestC Test";
    private MessageListener listener = new BusinessEventObserverC();

    private String emailTos = "<EMAIL>";
    private String emailSubject = "Trade Execution";
    private boolean toUser = false;
    private boolean sendFullMessage = true;
    private boolean sendCSVMessage = true;
    private boolean verbose = false;
    private boolean batchMode = false;
    private long batchInterval = 1000;
    private Vector batchedFullEmails = new Vector(100);
    private Vector batchedCSVEmails = new Vector(100);
    private boolean isFXI = false;

    public BusinessEventTestC() {
        super("BusinessEventTestC Test", "BusinessEventTestC Test", "BusinessEventTestC Test");
    }

    public boolean check() {
        boolean passed = true;
        try {
            setUp();
        } catch (Exception e) {
            log.error("Test Email: error in setup...", e);
        }

        return passed;
    }


    protected void setArguments(String args[]) {
        super.setArguments(args);
        for (int i = 0; i < args.length; i++) {
            if (args[i].equalsIgnoreCase("-to")) {
                i++;
                emailTos = args[i];
            } else if (args[i].equalsIgnoreCase("-subject")) {
                i++;
                emailSubject = args[i];
            } else if (args[i].equalsIgnoreCase("-verbose")) {
                verbose = true;
            } else if (args[i].equalsIgnoreCase("-emailToUser")) {
                toUser = true;
            } else if (args[i].equalsIgnoreCase("-batch")) {
                batchMode = true;
            } else if (args[i].equalsIgnoreCase("-batchInterval")) {
                batchMode = true;
                i++;
                Long interval = new Long(args[i]);
                batchInterval = interval.longValue();

            } else if (args[i].equalsIgnoreCase("-format")) {
                i++;
                String mode = args[i];
                if ("FULL".equalsIgnoreCase(mode)) {
                    sendFullMessage = true;
                    sendCSVMessage = false;
                } else if ("CSV".equalsIgnoreCase(mode)) {
                    sendFullMessage = false;
                    sendCSVMessage = true;
                } else if ("ALL".equalsIgnoreCase(mode)) {
                    sendFullMessage = true;
                    sendCSVMessage = true;
                }
            } else if (args[i].equalsIgnoreCase("-lp")) {
                i++;
                LOGO_PATH = args[i];
            } else if (args[i].equalsIgnoreCase("-csv")) {
                i++;
                if (args[i] != null) {
                    if (args[i].trim().equals("false"))
                        sendCSVMessage = false;
                }
            }
        }
        if (LOGO_PATH == null) {
            log.error("Missing Log Path Usage -lp <Logo Location> ");
            System.exit(0);
        }

    }

    public void setUp() {
        try {
            Session session = PersistenceFactory.newSession();
            if (batchMode) {
                BusinessEventEmailSenderC sender = new BusinessEventEmailSenderC();
                Thread th = new Thread(sender);
                th.start();
            }

            // TODO: listen only on messages with event=CREATE
            JMSManager.getInstance().setMessageListenerForDestination("TOPIC.ENTITY",
                    listener,
                    null);
        } catch (Exception e) {
            log.error("BusinessEventTestC: unable to register listener on JNDI destination TOPIC.ENTITY", e);
        }
    }

    private class BusinessEventEmailSenderC
            implements Runnable {
        public void run() {
            try {
                while (true) {
                    try {
                        log.debug("BusinessEventTestC: sleep " + batchInterval + "msec until sending next email");
                        Thread.sleep(batchInterval);

                        String msg1 = "";
                        try {
                            while (true) {
                                String e = (String) batchedFullEmails.remove(0);
                                msg1 = msg1 + e + "\n\n\n";
                            }
                        } catch (ArrayIndexOutOfBoundsException aioob) {
                        }
                        if (sendFullMessage && (msg1.length() > 1)) {
                            FXISendEmailC.sendEmail(emailTos, emailSubject, msg1);
                            log.debug("BusinessEventTestC: sent batched full emails");
                        }

                        String msg2 = "";
                        try {
                            while (true) {
                                String e = (String) batchedCSVEmails.remove(0);
                                msg2 = msg2 + e + "\n";
                            }
                        } catch (ArrayIndexOutOfBoundsException aioob) {
                        }
                        if (sendCSVMessage && (msg2.length() > 1)) {
                            FXISendEmailC.sendFXIEmail(emailTos, emailSubject + " [CSV]", msg2);
                            log.debug("BusinessEventTestC: sent batched CSV emails");
                        }
                    } catch (Exception e) {
                    }
                }
            } catch (Exception e) {
            }
        }
    }

    /**
     * Private helper class as a cache synchronization observer
     */
    private class BusinessEventObserverC
            implements MessageListener {
        private double totalUSD = 0.0;
        private Currency usdCcy = null;
        private FXMarketDataSet mds = null;

        public BusinessEventObserverC() {
            try {
                Session session = PersistenceFactory.newSession();

                ExpressionBuilder eb = new ExpressionBuilder();

                Expression expr = eb.get("shortName").equal("USD");
                usdCcy = (Currency) session.readObject(Currency.class, expr);

                expr = eb.get("shortName").equal("MAINStaticFXMDS");
                mds = (FXMarketDataSet) session.readObject(FXMarketDataSet.class, expr);
            } catch (Exception e) {
            }

        }

        public void onMessage(Message aMessage) {
            log.debug("BusinessEventTestC:  BusinessEventObserverC.onMessage() called");
            try {
                // get changes
                ObjectMessage om = (ObjectMessage) aMessage;
                Object o = om.getObject();
                log.debug("BusinessEventTestC: received message with body <<<" + o + ">>>");

                Enumeration props = om.getPropertyNames();
                while (props.hasMoreElements()) {
                    String prop = (String) props.nextElement();
                    log.debug("BusinessEventTestC:  prop = " + prop + " with value = " + om.getStringProperty(prop));
                }

                // double-check event type
                if ("CREATE".equals(om.getStringProperty("event"))) {
                    if (o instanceof WorkflowMessage) {
                        WorkflowMessage wm = (WorkflowMessage) o;
                        sendEmail(om, emailTos, emailSubject);

                    }
                }
            } catch (Exception e) {
                log.debug("BusinessEventTestC: error when processing message " + aMessage, e);
            }
        }

        private String sendEmail(ObjectMessage msg, String to, String subject) {
            StringBuffer buf = new StringBuffer();

            User user = null;
            FXTrade trade = null;

            try {
                Object o = msg.getObject();
                if (o instanceof WorkflowMessage) {
                    WorkflowMessage wm = (WorkflowMessage) o;
                    Iterator it = wm.getEntityReferences().iterator();
                    while (it.hasNext()) {
                        Object ref = it.next();
                        if (ref instanceof MappedEntityReference) {
                            MappedEntityReference mer = (MappedEntityReference) ref;
                            log.debug("BusinessEventTestC:\tmapped entity ref = " + mer);
                            EntityReference er = (EntityReference) mer.getEntityReference();
                            log.debug("BusinessEventTestC:\tentity ref value = " + er);
                            if (er != null) {
                                Entity entity = er.getEntity();
                                log.debug("BusinessEventTestC:\t  entity  = " + entity);
                                if (entity instanceof User) {
                                    user = (User) entity;
                                } else if (entity instanceof FXTrade) {
                                    trade = (FXTrade) entity;
                                }
                            }
                        } else if (ref instanceof EntityReference) {
                            EntityReference er = (EntityReference) ref;
                            log.debug("BusinessEventTestC:\tmapped entity ref = " + er);
                            Entity entity = er.getEntity();
                            log.debug(" BusinessEventTestC:\t  entity  = " + entity);
                        }
                    }

                    // Test whether the mail message body is already set by the caller,
                    // in which case we don't need to construct the message, but only sent it
                    //
                    // FXInside/IS does set it since the trade is never saved in the DB.
                    //
                    String mailMsg = (String) wm.getParameterValue("MailMessage");
                    String clientMailMsg = (String) wm.getParameterValue("ClientMailMessage");
                    String declinedMailMsg = (String) wm.getParameterValue("DeclinedMailMessage");
                    subject = (String) wm.getParameterValue("Subject");
                    String clientMailId = (String) wm.getParameterValue("ClientMailId");
                    String priceMailMessage = (String) wm.getParameterValue("PriceMailMessage");

                    if (mailMsg != null) {
                        isFXI = true;
                        log.debug("BusinessEventTestC: MailMessage = <" + mailMsg + ">");
                        if (user == null) {
                            String oid = msg.getStringProperty("UserObjectId");
                            user = getUser(mailMsg, oid);
                            log.info("BusinessEventTestC: using FXI user " + user);
                        }
                    }


                    if (priceMailMessage != null) {
                        buf = new StringBuffer();
                        buf.append(priceMailMessage);
                        if (sendFullMessage) {
                            if (batchMode) {
                                batchedFullEmails.add(buf.toString());
                            } else {
                                /*if(buf.toString().startsWith("<HTML>")){
                                if(clientMailId!=null){
                                FXISendEmailC.sendFXIEmail(clientMailId, subject, buf.toString());
                                }else{
                                log.info("BusinessEventTestC:  email id is null can't send Email");
                                }
                                }else{*/
                                SendEmailC.sendEmail(to, subject, buf.toString());
                                //}
                            }
                        }
                    }


                    if (mailMsg != null) {
                        buf = new StringBuffer();
                        buf.append(mailMsg);
                        if (sendFullMessage) {
                            if (batchMode) {
                                batchedFullEmails.add(buf.toString());
                            } else {
                                if (buf.toString().startsWith("<HTML>")) {
                                    if (to != null)
                                        FXISendEmailC.sendFXIEmail(to, subject, buf.toString());
                                } else {
                                    if (to != null)
                                        SendEmailC.sendEmail(to, subject, buf.toString());
                                }
                            }
                        }
                        if (sendCSVMessage) {
                            String csvMailMessage = (String) wm.getParameterValue("CSVMailMessage");
                            buf = new StringBuffer();
                            buf.append(csvMailMessage);
                            if (batchMode) {
                                batchedCSVEmails.add(buf.toString());
                            } else {
                                SendEmailC.sendEmail(to, subject + " [CSV]", buf.toString());
                            }
                        }
                    }


                    if (declinedMailMsg != null) {
                        buf = new StringBuffer();
                        buf.append(declinedMailMsg);
                        if (sendFullMessage) {
                            if (batchMode) {
                                batchedFullEmails.add(buf.toString());
                            } else {
                                if (buf.toString().startsWith("<HTML>")) {
                                    FXISendEmailC.sendFXIEmail(to, subject, buf.toString());
                                } else {
                                    SendEmailC.sendEmail(to, subject, buf.toString());
                                }
                            }
                        }
                        if (sendCSVMessage) {
                            String csvMailMessage = (String) wm.getParameterValue("CSVMailMessage");
                            buf = new StringBuffer();
                            buf.append(csvMailMessage);
                            if (batchMode) {
                                batchedCSVEmails.add(buf.toString());
                            } else {
                                SendEmailC.sendEmail(to, subject + " [CSV]", buf.toString());
                            }
                        }
                    }


                    if (clientMailMsg != null) {
                        if (toUser) {
                            buf = new StringBuffer();
                            buf.append(clientMailMsg);
                            if (sendFullMessage) {
                                if (batchMode) {
                                    batchedFullEmails.add(buf.toString());
                                } else {
                                    if (clientMailId != null) {
                                        if (clientMailId.trim().length() != 0) {
                                            FXISendEmailC.sendFXIEmail(clientMailId, subject, buf.toString());
                                        }
                                    } else {
                                        //    FXISendEmailC.sendFXIEmail(to, subject, buf.toString());
                                        log.info("BusinessEventTestC:  email id is null can't send Email");
                                    }
                                }
                            }
                        }
                    }

/*
                    if (user != null) {
                        DisplayPreference pref = user.getDisplayPreference();
                        DecimalFormat decFormat = user.getDisplayPreference().getDecimalFormat();
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
                        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");

                        String tid = "TBD";
                        String entryDate = "TBD";
                        String entryTime = "TBD";
                        String org = "TBD";
                        String orgRole = "TBD";
                        String type = "TBD";
                        String tradeType = "TBD";
                        String crncPair = "TBD";
                        String dealtAmt = "TBD";
                        String stltAmt = "TBD";
                        String stltAmtUSD = "TBD";
                        String buyCrnc = "TBD";
                        String sellCrnc = "TBD";
                        String userid = "TBD";
                        String rate = null;
                        String provider = null;

                        if (isFXI) {
                            tid = getValue(mailMsg, "Trade Transaction ID");
                            entryDate = getValue(mailMsg, "Trade Execution Date");
                            entryTime = getValue(mailMsg, "Trade Execution Time");
                            org = getValue(mailMsg, "Organization");
                            orgRole = getValue(mailMsg, "Organization Role");
                            type = getValue(mailMsg, "Type");
                            tradeType = getValue(mailMsg, "Trade Type");
                            crncPair = getValue(mailMsg, "Currency Pair");
                            dealtAmt = getValue(mailMsg, "Dealt Amount");
                            stltAmt = getValue(mailMsg, "Settled Amount");
                            stltAmtUSD = getValue(mailMsg, "Settled Amount in USD");
                            buyCrnc = getValue(mailMsg, "Buy Ccy");
                            sellCrnc = getValue(mailMsg, "Sell Ccy");
                            userid = getValue(mailMsg, "User Id");
                            rate = getValue(mailMsg, "Rate");

                            provider = getValue(mailMsg, "Provider");
                            if (provider == null) {
                                provider = msg.getStringProperty("Organization");
                            }
                        } else {
                            tid = getTransactionId(trade);
                            entryDate = getEntryDate(trade, pref);
                            entryTime = getEntryTime(trade, pref);
                            org = trade.getNamespace().getShortName();
                            orgRole = trade.getNamespace().getDescription();
                            type = getRequestClassification(trade);
                            tradeType = trade.getTradeClassification().getShortName().toUpperCase();
                            crncPair = getCurrencyPair(trade).toString();
                            dealtAmt = getDealtAmount(trade, pref);
                            stltAmt = getSettledAmount(trade, pref);
                            stltAmtUSD = getSettledAmountInUSD(trade, pref, mds, usdCcy);
                            buyCrnc = getBuyCurrency(trade);
                            sellCrnc = getSellCurrency(trade);
                            userid = trade.getEntryUser().getShortName();
                            rate = getRate(trade, decFormat);
                        }

// long messages
                        buf = new StringBuffer();
                        buf.append("User " + user.getShortName() + " from organization " + user.getNamespace().getShortName());
                        buf.append(" executed the following trade:\n\n");

                        buf.append("\tTrade Transaction ID  : " + tid + "\n");
                        buf.append("\tTrade Execution Date  : " + entryDate + "\n");
                        buf.append("\tTrade Execution Time  : " + entryTime + "\n");
                        buf.append("\tOrganization          : " + org + "\n");
                        buf.append("\tOrganization Role     : " + orgRole + "\n");
                        buf.append("\tType                  : " + type + "\n");
                        buf.append("\tTrade Type            : " + tradeType + "\n");
                        buf.append("\tCurrency Pair         : " + crncPair + "\n");
                        buf.append("\tDealt Amount          : " + dealtAmt + "\n");
                        buf.append("\tSettled Amount        : " + stltAmt + "\n");
                        buf.append("\tSettled Amount in USD : " + stltAmtUSD + "\n");
                        buf.append("\tBuy Ccy               : " + buyCrnc + "\n");
                        buf.append("\tSell Ccy              : " + sellCrnc + "\n");
                        if (rate != null) {
                            buf.append("\tRate                  : " + rate + "\n");
                        }
                        buf.append("\tUser Id               : " + userid + "\n");
                        if (isFXI) {
                            if (provider != null) {
                                buf.append("\tProvider               : " + provider + "\n");
                            }
                        }

                        if (sendFullMessage) {
                            if (batchMode) {
                                batchedFullEmails.add(buf.toString());
                            } else {
                                //FXISendEmailC.sendFXIEmail(to, subject, buf.toString());
                                SendEmailC.sendEmail(to, subject, buf.toString());
                                log.info("BusinessEventTestC: full email sent...");
                            }
                        }

                        if (toUser) {
                            String userSubject = null;
                            if (isFXI) {
                                userSubject = "FXInside Trade Message: Trade id " + tid + " Currency Pair " + crncPair;
                            } else {
                                userSubject = "CitiFX Trader Message: Trade id " + tid + " Currency Pair " + crncPair;
                            }

                            String userEmail = null;
                            if (user.getContact() != null) {
                                userEmail = user.getContact().getEmailAddress();
                            }

                            if (userEmail == null) {
                                log.warn("BusinessEventTestC: no email address available for user " + user.getShortName()
                                        + " from organization " + user.getNamespace().getShortName());
                            } else {
                             //   FXISendEmailC.sendFXIEmail(userEmail, userSubject, buf.toString());
                                   SendEmailC.sendEmail(userEmail, userSubject, buf.toString());
                                log.info("BusinessEventTestC: sent email for trade id " + tid
                                        + " for user " + user.getShortName() + "@" + user.getNamespace().getShortName()
                                        + " to email address " + userEmail);
                            }
                        }

                        if (sendCSVMessage) {
                            // CSV message
                            buf = new StringBuffer();
                            buf.append(tid + ",");
                            buf.append(entryDate + ",");
                            buf.append(entryTime + ",");
                            buf.append(org + ",");
                            buf.append(orgRole + ",");
                            buf.append(type + ",");
                            buf.append(tradeType + ",");
                            buf.append(crncPair + ",");
                            buf.append(dealtAmt + ",");
                            buf.append(stltAmt + ",");
                            buf.append(stltAmtUSD + ",");
                            buf.append(buyCrnc + ",");
                            buf.append(sellCrnc + ",");
                            buf.append(rate + ",");

                            buf.append(userid);

                            if (batchMode) {
                                batchedCSVEmails.add(buf.toString());
                            } else {
                                //FXISendEmailC.sendFXIEmail(to, subject + " [CSV]", buf.toString());
                                SendEmailC.sendEmail(to, subject + " [CSV]", buf.toString());
                            }
                            log.info("BusinessEventTestC: csv email sent...");
                        }

                    } else {
                        log.info("BusinessEventTestC: no user specified - no email sent...");
                    }
*/

                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            return buf.toString();
        }

        protected User getUser(String msg, String oid) {
            User user = null;

            String userName = "";
            String orgName = "";
            try {
                if (oid != null) {
                    ExpressionBuilder eb = new ExpressionBuilder();
                    Expression expr = eb.get("objectID").equal(oid);

                    Session session = PersistenceFactory.newSession();
                    user = (User) session.readObject(User.class, expr);
                }

                if (user == null) {
                    userName = getValue(msg, "User Id");
                    orgName = getValue(msg, "Organization");
                    if ("Financial Institution 1".equals(orgName)) {
                        orgName = "FI1";
                    }

                    ExpressionBuilder eb = new ExpressionBuilder();
                    Expression expr1 = eb.get("shortName").equal(userName);
                    Expression expr2 = eb.get("organization").get("shortName").equal(orgName);
                    Expression expr = expr1.and(expr2);

                    Session session = PersistenceFactory.newSession();
                    user = (User) session.readObject(User.class, expr);
                }
            } catch (Exception e) {
                log.warn("BusinessEventTestC: error when retrieving user " + userName + "@" + orgName, e);
            }
            if (user == null) {
                log.warn("BusinessEventTestC: unable to retrieve user " + userName + "@" + orgName);
            }

            return user;
        }

        protected String getValue(String msg, String key) {
            String result = null;

            int start = msg.indexOf(key);
            if (start > 0) {
                start = msg.indexOf(':', start);
                if (start > 0) {
                    start++;
                    start++;
                    int end = msg.indexOf('\n', start);
                    if (end == -1) {
                        end = msg.indexOf(' ', start);
                    }
                    if (end > start) {
                        result = msg.substring(start, end);
                    } else {
                        result = msg.substring(start);
                    }
                }
            }

            return result;
        }


        protected void sendFXIEmail(String to, String subject, User user, WorkflowMessage wm)
                throws Exception {
            String mailMsg = (String) wm.getParameterValue("MailMessage");

            StringBuffer buf = new StringBuffer();
            buf.append(mailMsg);

            if (sendFullMessage) {
                if (batchMode) {
                    batchedFullEmails.add(buf.toString());
                } else {
                    FXISendEmailC.sendFXIEmail(to, subject, buf.toString());
                }
            }

            if (sendCSVMessage) {
                mailMsg = (String) wm.getParameterValue("CSVMailMessage");
                if (mailMsg != null) {
                    buf = new StringBuffer();
                    buf.append(mailMsg);
                    if (batchMode) {
                        batchedCSVEmails.add(buf.toString());
                    } else {
                        FXISendEmailC.sendFXIEmail(to, subject + " [CSV]", buf.toString());
                    }
                }
            }

            if (toUser) {
                if (user == null) {
                    log.warn("BusinessEventTestC: skip user email since no user set in trade message");
                } else {
                    String crncPair = "XXX/XXX";
                    String tid = "XXX";

                    String userSubject = "FXI Message: Trade id " + tid + " Currency Pair " + crncPair;

                    String userEmail = null;
                    if (user.getContact() != null) {
                        userEmail = user.getContact().getEmailAddress();
                    }

                    if (userEmail == null) {
                        log.warn("BusinessEventTestC: no email address available for user " + user.getShortName()
                                + " from organization " + user.getNamespace().getShortName());
                    } else {
                        FXISendEmailC.sendFXIEmail(userEmail, userSubject, buf.toString());
                        log.info("BusinessEventTestC: sent email for trade id " + tid
                                + " for user " + user.getShortName() + "@" + user.getNamespace().getShortName()
                                + " to email address " + userEmail);
                    }
                }
            }

        }

        protected String getSettledAmountInUSD(FXTrade aTrade, DisplayPreference pref, FXMarketDataSet mds, Currency usdCcy) {

            double amount = 0.0;

            FXLegDealingPrice fxLegDealingPrice = getFXLegDealingPrice(aTrade);

            DecimalFormat df = pref.getDecimalFormat();

            Currency settledCurrency = fxLegDealingPrice.getSettledCurrency();

            Currency dealtCurrency = fxLegDealingPrice.getDealtCurrency();

            FXRate acceptedFXRate = fxLegDealingPrice.getAcceptedFXRate();

            if (settledCurrency.isSameAs(usdCcy)) {

                amount = fxLegDealingPrice.getAcceptedSettledAmount();

            } else if (dealtCurrency.isSameAs(usdCcy)) {

                amount = fxLegDealingPrice.getDealtAmount();

            } else {


                Tenor tenor = new Tenor("SPOT");

                try {

                    amount = mds.convert(fxLegDealingPrice.getAcceptedSettledAmount(), fxLegDealingPrice.getSettledCurrency(), usdCcy, tenor);

                } catch (Exception e) {

                    e.printStackTrace();

                }

            }


            totalUSD += amount;


            if (amount == 0.0)

                return "";

            else

                return Double.toString(amount);


        }


        protected String getEntryDate(FXTrade aTrade, DisplayPreference pref) {

            SimpleDateFormat dateFormat = pref.getDateFormat();

            return dateFormat.format(aTrade.getEntryDate());

        }


        protected String getEntryTime(FXTrade aTrade, DisplayPreference pref) {

            SimpleDateFormat dateFormat = pref.getTimeFormat();

            return dateFormat.format(aTrade.getEntryDate());

        }


        protected Request getRequest(FXTrade trade) {
        	return trade.getRequest();
        }


        protected CurrencyPair getCurrencyPair(FXTrade aTrade) {

            FXLegDealingPrice fxLegDealingPrice = getFXLegDealingPrice(aTrade);

            return fxLegDealingPrice.getAcceptedFXRate().getCurrencyPair();

        }


        protected FXLegDealingPrice getFXLegDealingPrice(FXTrade aTrade) {

            FXLegDealingPrice fxLegDealingPrice = null;

            if (aTrade instanceof FXSingleLeg)

                fxLegDealingPrice = (FXLegDealingPrice) getRequest(aTrade).getRequestPrice("singleLeg");

            else if (aTrade instanceof FXSwap)

                fxLegDealingPrice = (FXLegDealingPrice) getRequest(aTrade).getRequestPrice("farLeg");


            return fxLegDealingPrice;

        }


        protected String getDealtAmount(FXTrade aTrade, DisplayPreference pref) {

            FXLegDealingPrice fxLegDealingPrice = getFXLegDealingPrice(aTrade);

            String ccy = fxLegDealingPrice.getDealtCurrency().getShortName();

            DecimalFormat df = pref.getDecimalFormat();

            // String amount = df.format(fxLegDealingPrice.getDealtAmount());

            String amount = String.valueOf(fxLegDealingPrice.getDealtAmount());

            // return ccy + " " + amount;

            return amount;

        }


        protected String getSettledAmount(FXTrade aTrade, DisplayPreference pref) {

            FXLegDealingPrice fxLegDealingPrice = getFXLegDealingPrice(aTrade);

            String ccy = fxLegDealingPrice.getSettledCurrency().getShortName();

            DecimalFormat df = pref.getDecimalFormat();

            //String amount = df.format(fxLegDealingPrice.getAcceptedSettledAmount());

            String amount = String.valueOf(fxLegDealingPrice.getAcceptedSettledAmount());

            // return ccy + " " + amount;

            return amount;

        }


        protected String getTransactionId(FXTrade aTrade) {

            ExternalSystemId es = aTrade.getExternalSystemId("DIRECTFX");

            if (es != null)

                return es.getSystemId();


            return null;

        }


        protected String getRequestClassification(FXTrade aTrade) {

            String formattedName = "";

            Request request = getRequest(aTrade);

            if (request.getStatus() == 'A') {

                String clsfShortName = request.getRequestClassification().getShortName();

                if (clsfShortName.equals("RFQ"))

                    formattedName = "Request";

                else if (clsfShortName.equals("QUOTED"))

                    formattedName = "Quick Trade";

            }

            return formattedName;


        }


        protected String getBuyCurrency(FXTrade aTrade) {

            FXLegDealingPrice fxLegDealingPrice = getFXLegDealingPrice(aTrade);

            if (fxLegDealingPrice.isBuyingDealtCurrency())

                return fxLegDealingPrice.getDealtCurrency().getShortName();

            else

                return fxLegDealingPrice.getSettledCurrency().getShortName();

        }


        protected String getSellCurrency(FXTrade aTrade) {

            FXLegDealingPrice fxLegDealingPrice = getFXLegDealingPrice(aTrade);

            if (fxLegDealingPrice.isBuyingDealtCurrency())

                return fxLegDealingPrice.getSettledCurrency().getShortName();

            else

                return fxLegDealingPrice.getDealtCurrency().getShortName();

        }


    }

    ;

    public static void main(String args[]) {
        BusinessEventTestC report = new BusinessEventTestC();
        try {
            report.setArguments(args);
            report.run();
        } catch (Exception e) {
            // slog.error("Error encountered during report: ", e);
        }
    }

    //Added for the bugs 17949 and 17933 as a work around
    public String getRate(FXTrade currentTrade, DecimalFormat decFormat) {
        String finalRate = null;
        DecimalFormat pointsFormat = null;
        DecimalFormat rateFormat = null;
        DecimalFormat spotRateFormat = null;
        DecimalFormat formatter = null;
        String tradeClassification = "";

        FXLeg fxLeg = getFXLeg(currentTrade);
        pointsFormat = fxLeg.getFXPayment().getFXRate().getScaledForwardPointsFormat(decFormat.clone());
        rateFormat = fxLeg.getFXPayment().getFXRate().getRateFormat(decFormat.clone());
        spotRateFormat = fxLeg.getFXPayment().getFXRate().getSpotRateFormat(decFormat.clone());

        if (currentTrade.getTradeClassification().getShortName().equals(TRD_CLSF_SP)) {
            double spotRate = fxLeg.getFXPayment().getFXRate().getSpotRate();
            finalRate = spotRateFormat.format(spotRate);
            tradeClassification = "label.tradeBlotter.spot";
        } else if (currentTrade.getTradeClassification().getShortName().equals(TRD_CLSF_OR)) {
            double rate = fxLeg.getFXPayment().getFXRate().getRate();
            finalRate = rateFormat.format(rate);
            tradeClassification = "label.tradeBlotter.outright";
        } else if (currentTrade.getTradeClassification().getShortName().equals(TRD_CLSF_SWAP)) {

            double swapPoints = fxLeg.getFXPayment().getFXRate().getScaledForwardPoints();
            finalRate = pointsFormat.format(swapPoints);
            tradeClassification = "label.tradeBlotter.swap";
        } else if (currentTrade.getTradeClassification().getShortName().equals(TRD_CLSF_FWD)) {
            formatter = fxLeg.getFXPayment().getFXRate().getScaledForwardPointsFormat(decFormat.clone());
            double finalFwdPoints = fxLeg.getFXPayment().getFXRate().getScaledForwardPoints();
            finalRate = formatter.format(finalFwdPoints);
            tradeClassification = "label.tradeBlotter.fwdfwd";
        }

        return finalRate;
    }

    public FXLeg getFXLeg(FXTrade currentTrade) {

        FXLeg fxLeg = null;
        if (currentTrade instanceof FXSwap) {
            fxLeg = ((FXSwap) currentTrade).getFarLeg();
        } else if (currentTrade instanceof FXSingleLeg) {
            fxLeg = ((FXSingleLeg) currentTrade).getFXLeg();
        }

        return fxLeg;
    }

}