package com.integral.persistence.test;

import com.integral.system.mail.MailFactory;
import com.integral.system.mail.SendEmailC;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import java.util.Date;
import java.util.Properties;

// Copyright (c) 2003-2004 Integral Development Corp.  All rights reserved.

/**
 * Returns formatted mails message in WorkflowMessage.
 *
 * <AUTHOR> Development Corp.
 */
public class FXISendEmailC extends SendEmailC {
    /**
     * Send an email. If the email body starts with "<HTML>" the message is sent as an HTML email messages.
     *
     * @param to 'to' address for mail
     * @param subject subject for mail
     * @param body message body of mail
     */
    public static void sendFXIEmail(String to, String subject, String body)
            throws MessagingException {
        String from = null;
        String smtpHost = MailFactory.getMailMBean().getDefaultSMTPHost();
        if (smtpHost == null) {
            throw new RuntimeException("SendEmailC: SMTP host not defined");
        }

        // properties
        Properties props = new Properties();
        if (from == null) {
            from = MailFactory.getMailMBean().getSMTPSender();
            if (from == null) {
                throw new RuntimeException("SMTP sender not defined");
            }
        }

        props.put("mail.smtp.host", smtpHost);
        props.put("mail.transport.protocol", "pop3");
        props.put("mail.host", smtpHost);
        props.put("mail.user", "Integral");
        props.put("mail.from", from);

        // Get a Session object
        Session session = Session.getDefaultInstance(props, null);

        try {
            // construct the message
            Message msg = new MimeMessage(session);
            msg.setFrom(new InternetAddress(from));

            // allow for convenient debugging without sending mail to the official destinations
            String debugTo = MailFactory.getMailMBean().getSMTPOverrideMailbox();
            if (debugTo != null) {
                subject += " (to: " + to + ")";
                to = debugTo;
            }
            msg.setRecipients(Message.RecipientType.TO, InternetAddress.parse(to, false));

            if (subject != null) {
                msg.setSubject(subject);
            }

            Multipart multipart = new MimeMultipart();
            BodyPart bodyPart = new MimeBodyPart();
            bodyPart.setContent(body, "text/html");
            multipart.addBodyPart(bodyPart);


            bodyPart = new MimeBodyPart();
            DataSource source = new FileDataSource(BusinessEventTestC.LOGO_PATH + "/ISWeb/Web Content/theme/images/logo.gif");
            bodyPart.setDataHandler(new DataHandler(source));
            bodyPart.setHeader("Content-ID", "logo");
            multipart.addBodyPart(bodyPart);


            bodyPart = new MimeBodyPart();
            source = new FileDataSource(BusinessEventTestC.LOGO_PATH + "/ISWeb/Web Content/theme/images/fxi.gif");
            bodyPart.setDataHandler(new DataHandler(source));
            bodyPart.setHeader("Content-ID", "fxi");
            multipart.addBodyPart(bodyPart);

            msg.setSentDate(new Date());
            msg.setContent(multipart);
            // send the thing off
            Transport.send(msg);
        }
        catch (MessagingException e) {
            //log.error("SendEmailC: error sending mail", e);
            e.printStackTrace();
            throw e;
        }
    }

}
