package com.integral.fxbenchmark;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.Stream;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.tradingvenue.REXConfig;
import com.integral.transport.multicast.MulticastGroup;
import com.integral.user.Organization;
import com.integral.util.Tuple;

public class FXBenchMarkService {
	private static final Log LOG = LogFactory.getLog(FXBenchMarkService.class);
	public static final String FX_BENCHMARK_PRICE_SOURCE_DEFAULT = "FXB";
	public static final String FX_BENCHMARK_ORG_STREAM_DELIMITER = ":";
	private static final FXBenchMarkService INSTANCE = new FXBenchMarkService();
	private List<FXBenchMarkMulticastListener> fxBenchMarkMulticastListenerList = new ArrayList<FXBenchMarkMulticastListener>();

	private FXBenchMarkRateCacheC fxBenchMarkRateCache;
	private FXBenchMarkService()	
	{
		this.fxBenchMarkRateCache = new FXBenchMarkRateCacheC();
	}
	
	public static final FXBenchMarkService getInstance()
	{
		return INSTANCE;
	}
	
	public List<FXBenchMarkMulticastListener> getFxBenchMarkMulticastListenerList() 
	{
		return this.fxBenchMarkMulticastListenerList;
	}
	
	public void start()
	{
		try 
		{	    	
			this.fxBenchMarkMulticastListenerList.clear();
			String rateSrcString = REXConfig.getInstance().getPriceSource(FX_BENCHMARK_PRICE_SOURCE_DEFAULT);
			LOG.info("FXBenchMarkService.start():FXBenchMarkRate price source :" + rateSrcString);
	        if( rateSrcString == null )
	        {
	        	LOG.warn("start : BenchMark rate source not defined. Not starting the FXBenchMark service");
	            return;
	        }
	        String[] sources = rateSrcString.split(",");
	        if (sources == null || sources.length == 0)
	        {
	        	throw new RuntimeException("BenchMark rate source not defined correctly.");
	        }
	        String primaryPriceSource = sources[0];
	        List<String> secondaryPriceSources = new ArrayList<String>();
	        Map<Integer, Set<MultiCastInfo>> mcAddressInfoMap = new HashMap<Integer, Set<MultiCastInfo>>();
	        int counter = 1;
	        for (String str : sources)
	        {
		        String[] source = str.split(FX_BENCHMARK_ORG_STREAM_DELIMITER);
		        if (source.length < 2)
		        {	        	
		        	throw new RuntimeException("BenchMark rate source not defined correctly.");
		        }
		        String orgName = source[0];
		        String streamName = source[1];
		        Organization benchMarkOrg = ReferenceDataCacheC.getInstance().getOrganization(orgName);
		        if( benchMarkOrg == null )
		        {	            
		            throw new RuntimeException("BenchMark rate source org not found.");
		        }        
		        BrokerOrganizationFunction bof = benchMarkOrg.getBrokerOrganizationFunction();
		        if( bof == null ) 
		        {	        	
		        	throw new RuntimeException("BenchMark rate source org's Broker Org functor not found.");
		        }  	
		        Stream stream = bof.getStream(streamName);
		        if( stream == null )
		        {	            
		            throw new RuntimeException("stream not configured for BenchMark rate source org " + orgName);
		        } 
		        MulticastGroup mcastGroup = benchMarkOrg.getMulticastGroup();
		        int fxbMulticastPort = 0;
		        String fxbMulticastAddress = null;
		        if ( mcastGroup != null && mcastGroup.isEnabled() && !mcastGroup.getAddresses().isEmpty() )
		        {
		            fxbMulticastPort = mcastGroup.getLogicalPort().getPort();
		            fxbMulticastAddress = stream.getMulticastAddress().getAddress();
		            if (fxbMulticastPort <=0 || fxbMulticastAddress == null)
		            {	                
		                throw new RuntimeException("Multicast address or port not valid for Stream " + orgName + ":" + streamName);
		            }
		        }   
		        else
		        {	            
		            throw new RuntimeException("Problem with retrieving Muticast address and port for stream:" + orgName + ":" + streamName);
		        }
		        Set<MultiCastInfo> mcInfoSet = mcAddressInfoMap.get(fxbMulticastPort);
		        if (mcInfoSet == null)
		        {
		        	mcInfoSet = new HashSet<MultiCastInfo>();
		        	mcAddressInfoMap.put(fxbMulticastPort, mcInfoSet);
		        }
		        int orgIndex = benchMarkOrg.getIndex();
		        MultiCastInfo mcInfo = new MultiCastInfo(fxbMulticastAddress, fxbMulticastPort, mcastGroup, streamName, orgName, orgIndex);
		        mcInfoSet.add(mcInfo);
		        if (counter > 1)
		        {
					if (!secondaryPriceSources.contains( str ))
					{
						secondaryPriceSources.add(str);
					}
		        }
		        ++counter;
	        }
	        // create one listener for each port
	        fxBenchMarkRateCache.setPrimaryPriceSource(primaryPriceSource);
	        fxBenchMarkRateCache.setSecondaryPriceSources(secondaryPriceSources);
	        for (Map.Entry<Integer, Set<MultiCastInfo>> entry : mcAddressInfoMap.entrySet()) 
	        {
	        	int port = entry.getKey();
	        	Set<MultiCastInfo> mcInfoSet = entry.getValue();
	        	Set<String> mcAddresses = new HashSet<String>();
	        	Map<Integer, Tuple<String, MulticastGroup>> orgInfoMap 
	        				= new HashMap<Integer, Tuple<String,MulticastGroup>>();
	        	Map<Integer, Set<String>> streamInfoMap = new HashMap<Integer, Set<String>>();
	        	for (MultiCastInfo mcInfo : mcInfoSet)
	        	{
	        		String addr = mcInfo.getAddress();
	        		String streamName = mcInfo.getStreamName();
	        		int orgIndex = mcInfo.getOrgIndex();
	        		String orgName = mcInfo.getOrgName();
	        		MulticastGroup mcg = mcInfo.getMulticastGroup();
	        		mcAddresses.add(addr);
	        		orgInfoMap.put(orgIndex, new Tuple<String, MulticastGroup>(orgName, mcg));
	        		Set<String> streams = streamInfoMap.get(orgIndex);
	        		if (streams == null)
	        		{
	        			streams = new HashSet<String>();
	        			streamInfoMap.put(orgIndex, streams);
	        		}
	        		streams.add(streamName);
	        	}
	        	Tuple<Integer, Set<String>> mcAddresInfo = new Tuple<Integer, Set<String>>(port, mcAddresses);
        		FXBenchMarkMulticastListener fxBenchMarkMulticastListener = 
	  				             new FXBenchMarkMulticastListener(fxBenchMarkRateCache, mcAddresInfo);
        		fxBenchMarkMulticastListener.setBenchMarkOrgInfo(orgInfoMap);
        		fxBenchMarkMulticastListener.setBenchMarkStreamInfo(streamInfoMap);
        		fxBenchMarkMulticastListener.start();
        		fxBenchMarkMulticastListenerList.add(fxBenchMarkMulticastListener);
    			StringBuilder sb = new StringBuilder();
    			sb.append("FXBenchMarkService.start():FXBenchMarkService started successfully on:");    		
    			sb.append(":MulticastAddress:");
    			sb.append(mcAddresses);
    			sb.append(":Port:");
    			sb.append(port);
    			LOG.info(sb.toString());
	        }
		}
		catch (Throwable t)
		{
			LOG.error("Problem with starting FXBenchMarkService.", t);
		}

	}	
 
	public void stop()
	{
		try 
		{
			if (fxBenchMarkMulticastListenerList != null)
			{
				for (FXBenchMarkMulticastListener fxBenchMarkMulticastListener : fxBenchMarkMulticastListenerList)
				{
					fxBenchMarkMulticastListener.stop();
				}
				fxBenchMarkMulticastListenerList.clear();
			}
			LOG.info("FXBenchMarkService.start():FXBenchMarkService stopped successfully.");
		}
		catch (Throwable t)
		{
			LOG.error("Problem with stopping FXBenchMarkService.", t);
		}
	}
	
	public FXBenchMarkRateProvider getFXBenchMarkRateProvider()	
	{
		return this.fxBenchMarkRateCache;
	}
	
	private static class MultiCastInfo {
		private String address;
		private int port;
		private MulticastGroup multicastGroup;
		private String streamName;
		private String orgName;
		private int orgIndex;
		public MultiCastInfo(String address, int port, MulticastGroup multicastGroup, String streamName, String orgName, int orgIndex)
		{
			this.address = address;
			this.port = port;
			this.multicastGroup = multicastGroup;
			this.streamName = streamName;
			this.orgName = orgName;
			this.orgIndex = orgIndex;
		}
		public String getAddress() {
			return address;
		}
		public int getPort() {
			return port;
		}
		public MulticastGroup getMulticastGroup() {
			return multicastGroup;
		}
		public String getStreamName() {
			return streamName;
		}
		public String getOrgName() {
			return orgName;
		}
		public int getOrgIndex() {
			return orgIndex;
		}
		@Override
		public int hashCode() {
			final int prime = 31;
			int result = 1;
			result = prime * result
					+ ((address == null) ? 0 : address.hashCode());
			result = prime * result + orgIndex;
			result = prime * result
					+ ((orgName == null) ? 0 : orgName.hashCode());
			result = prime * result + port;
			result = prime * result
					+ ((streamName == null) ? 0 : streamName.hashCode());
			return result;
		}
		@Override
		public boolean equals(Object obj) {
			if (this == obj)
				return true;
			if (obj == null)
				return false;
			if (getClass() != obj.getClass())
				return false;
			MultiCastInfo other = (MultiCastInfo) obj;
			if (address == null) 
			{
				if (other.address != null)
					return false;
			} 
			else if (!address.equals(other.address))
				return false;
			if (orgIndex != other.orgIndex)
				return false;
			if (orgName == null) 
			{
				if (other.orgName != null)
					return false;
			} 
			else if (!orgName.equals(other.orgName))
				return false;
			if (port != other.port)
				return false;
			if (streamName == null) 
			{
				if (other.streamName != null)
					return false;
			} 
			else if (!streamName.equals(other.streamName))
				return false;
			return true;
		}
		@Override
		public String toString() {
			StringBuilder sb = new StringBuilder();
			sb.append("MultiCastInfo [address=");
			sb.append(address);
			sb.append(", port=");
			sb.append(port);
			sb.append(", orgName=");
			sb.append(orgIndex);			
			sb.append(", streamName=");
			sb.append(streamName);
			sb.append("]");
			return sb.toString();
		}
		
	}
}
