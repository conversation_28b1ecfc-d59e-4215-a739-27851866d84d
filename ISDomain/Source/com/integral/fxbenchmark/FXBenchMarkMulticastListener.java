package com.integral.fxbenchmark;
import java.io.IOException;
import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.nio.ByteBuffer;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

import com.integral.is.common.mbean.ISFactory;
import com.integral.is.message.MarketRate;
import com.integral.is.message.MarketRateC;
import com.integral.is.message.MarketRateSerializer;
import com.integral.is.message.MarketRateSerializerFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.transport.multicast.MulticastAddress;
import com.integral.transport.multicast.MulticastGroup;
import com.integral.util.Tuple;

public class FXBenchMarkMulticastListener {
    private static final Log LOG = LogFactory.getLog(FXBenchMarkMulticastListener.class);
    private static final String FX_BENCHMARK_LISTENER_THREAD_NAME = "FXBenchMarkListener";
    private MarketRateSerializerFactory serializerFactory = MarketRateSerializerFactory.instance();
    private final FXBenchMarkRateCache cache;
    private final MulticastSocket socket;
    private final ThreadLocal<ByteBuffer> messageBuffer;
    private final AtomicBoolean isAlive = new AtomicBoolean(true);
    private final Thread readerThread;   
    private Map<Integer, Tuple<String, MulticastGroup>> benchMarkOrgInfoMap;
    private Map<Integer, Set<String>> benchMarkStreamInfoMap;

    public FXBenchMarkMulticastListener(final FXBenchMarkRateCache cache, final Tuple<Integer, Set<String>> mcAddressInfo) throws IOException 
    {
        this.cache = cache;     
        int port = mcAddressInfo.first;
        socket = new MulticastSocket(port);
        Set<String> addresses = mcAddressInfo.second;
        for (String address : addresses)
        {
        	socket.joinGroup(InetAddress.getByName(address));
        }        
        final int bufferSize = ISFactory.getInstance().getISMBean().getBenchMarkRateBufferSize();
        messageBuffer = new ThreadLocal<ByteBuffer>() {
            @Override
            protected ByteBuffer initialValue() {
                return ByteBuffer.allocate(bufferSize);
            }
        };
        readerThread = new Thread(new FXBenchMarkListenerTask());
        readerThread.setName(FX_BENCHMARK_LISTENER_THREAD_NAME + ":" + port);
        readerThread.setDaemon(true); 
     }
 
    public void setBenchMarkOrgInfo(Map<Integer, Tuple<String, MulticastGroup>> benchMarkOrgInfoMap) {
		this.benchMarkOrgInfoMap = benchMarkOrgInfoMap;
	}
    
    public void setBenchMarkStreamInfo(Map<Integer, Set<String>> benchMarkStreamInfoMap) {
		this.benchMarkStreamInfoMap = benchMarkStreamInfoMap;
	}


	public void start()
    {
    	if (readerThread != null)
    	{
    		readerThread.start();
    	    LOG.info(FX_BENCHMARK_LISTENER_THREAD_NAME + " Thread Started ");
    	} 
    }

    public void stop() 
    {
        isAlive.set(false);
        if (readerThread != null)
        {
        	readerThread.interrupt(); //interrupt the thread if it is blocked on socket receive call.
        }
        if (socket != null)
        {
        	 socket.close();
        }
    }

    private class FXBenchMarkListenerTask implements Runnable {
        public void run()
        {
            while (isAlive.get()) 
            {
                try 
                {
                    ByteBuffer byteBuffer = messageBuffer.get();
                    byteBuffer.clear();
                    DatagramPacket packet = new DatagramPacket(byteBuffer.array(), byteBuffer.capacity());
                    socket.receive(packet); //blocking call
                    ByteBuffer received = ByteBuffer.wrap(packet.getData());
                    byte[] receivedBytes = received.array();
                    int offset = received.position();
                    MarketRateSerializer serializer = serializerFactory.getSerializerForVersion(received.get());
                    String priceSource = getValidPriceSource(receivedBytes, offset, serializer);
                    if (priceSource != null)
                    {
                        MarketRate rate = new MarketRateC(0, 0);
                        serializer.deserialize(received, rate);
                        if( rate.isStale() )
                        {
                            if( LOG.isDebugEnabled() )
                            {
                                LOG.debug("FXBenchMarkListenerTask.run(): rate is stale, so dropping it for Stream:" + priceSource + ":QuoteId:" + rate.getQuoteId());
                            }
                            continue;
                        }                        
                        cache.add(priceSource, rate);
                    }                    
                }
                catch (Throwable ex) 
                {
                    if(LOG.isDebugEnabled())
                    {
                        LOG.error( "Error while receiving/processing packet.", ex );
                    }
                }
            }
        }
        private String getValidPriceSource(final byte[] receivedBytes, final int offset, final MarketRateSerializer serializer)
        {
        	if (benchMarkOrgInfoMap == null || benchMarkStreamInfoMap == null)
        	{
                if(LOG.isDebugEnabled())
                {
                    LOG.debug( "FXBenchMarkMulticastListener.processData(): Not enough information to filter data so not processing.");
                }
        		return null;
        	}
            int orgIndex = serializer.getProviderIndex(receivedBytes, offset);
            int streamIndex = serializer.getStreamIndex(receivedBytes, offset);
            Tuple<String, MulticastGroup> orgInfo = benchMarkOrgInfoMap.get(orgIndex);
            if (orgInfo == null)
            {
                if(LOG.isDebugEnabled())
                {
                    LOG.debug( "FXBenchMarkMulticastListener.processData(): Expected Org Indices:" 
                    		+ benchMarkOrgInfoMap.keySet() + ":Received orgIndex=" + orgIndex + ":So not processing data.");
                }
                return null;
            }

            // once the providerIndex matches, the multicast group will be same.
            MulticastAddress addr = orgInfo.second.getMulticastAddressForIndex(streamIndex);            
            if (addr == null) 
            {
                if(LOG.isDebugEnabled())
                {
                    LOG.debug("FXBenchMarkMulticastListener.processData(): No multicast address configured for stream with index: " 
                    										 + streamIndex + " for org index: " + orgIndex + ":So not processing data.");
                }
                return null;
            }
            String receivedStreamName = (addr.getStream() != null ? addr.getStream().getShortName() : null);
            Set<String> benchMarkOrgStreamNames = benchMarkStreamInfoMap.get(orgIndex);
            if (benchMarkOrgStreamNames == null || !benchMarkOrgStreamNames.contains(receivedStreamName))
            {
                if( LOG.isDebugEnabled() )
                {
                    LOG.debug( "FXBenchMarkMulticastListener.processData(): expected streams=" + 
                    				benchMarkOrgStreamNames + ":received=" + receivedStreamName + ": So not processing data.");
                }
                return null;
            }
            String priceSource = orgInfo.first + FXBenchMarkService.FX_BENCHMARK_ORG_STREAM_DELIMITER + receivedStreamName;
            return priceSource;

        }
    }
    
    

}
