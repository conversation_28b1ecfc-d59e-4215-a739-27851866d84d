package com.integral.fxbenchmark;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.message.MarketRate;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.math.MathUtil;

import static com.integral.fxbenchmark.FXBenchMarkRateUtil.invalidRate;

public class FXBenchMarkRateCacheC implements FXBenchMarkRateCache, FXBenchMarkRateProvider, FXBenchMarkUpdateNotifier {
	private static final Log LOG = LogFactory.getLog(FXBenchMarkRateCacheC.class);
	private static final String CURRENCY_PAIR_SEPARATOR = "/";
	private static final int DEFAULT_MID_RATE_PRECISION = 6;
	private ConcurrentMap<String, ConcurrentMap<String, FXBenchMarkRateEntry>> cache 
							= new ConcurrentHashMap<String, ConcurrentMap<String, FXBenchMarkRateEntry>>();
	private List<FXBenchMarkUpdateListener> listeners = new ArrayList<FXBenchMarkUpdateListener>();
	private String primaryPriceSource;
	private List<String> secondaryPriceSources;
	
	@Override
	public void notifyListeners(MarketRate marketRate) 
	{
		for (FXBenchMarkUpdateListener updateListener : listeners)
		{
			updateListener.onUpdate(marketRate);
		}
	}

	@Override
	public void add(String priceSource, MarketRate marketRate) 
	{
		if (priceSource == null || marketRate == null)
		{
			return;
		}
		ConcurrentMap<String, FXBenchMarkRateEntry> marketRateMap = cache.get(priceSource);
		if (marketRateMap == null)
		{
			marketRateMap = new ConcurrentHashMap<String, FXBenchMarkRateEntry>();
			ConcurrentMap<String, FXBenchMarkRateEntry> previousValue = cache.putIfAbsent(priceSource, marketRateMap);
			if (previousValue != null)
			{
				marketRateMap = previousValue;
			}
		}
		String baseCurrency = marketRate.getBaseCcy();
		String variableCurrency = marketRate.getVariableCcy();
		String currencyPair = baseCurrency + CURRENCY_PAIR_SEPARATOR + variableCurrency;
		if (LOG.isDebugEnabled())
		{
			StringBuilder sb = new StringBuilder();
			sb.append("FXBenchMarkRateCacheC.add()::Adding FXBenchMarkRate:PriceSource:");
			sb.append(priceSource);
			sb.append(":CurrencyPair:");
			sb.append(currencyPair);
			sb.append(":QuoteId:");
			sb.append(marketRate.getQuoteId());
			sb.append(":Bid:");
			sb.append(marketRate.getBidRate());
			sb.append(":Offer:");
			sb.append(marketRate.getOfferRate());
			LOG.debug(sb.toString());
		}
		marketRateMap.put(currencyPair, new FXBenchMarkRateEntry(marketRate));
		notifyListeners(marketRate);
	}

	@Override
	public MarketRate getRate(String baseCurrency, String variableCurrency) 
	{
		if (baseCurrency == null || variableCurrency == null)
		{
			LOG.warn("FXBenchMarkRateCacheC.getRate():Base currency or Variable Currency Supplied is wrong : BaseCurrency:" 
											+ baseCurrency + ":VariableCurrency:" + variableCurrency);
			return null;
		}
		String currencyPair = baseCurrency + CURRENCY_PAIR_SEPARATOR + variableCurrency;
		MarketRate marketRate = getRate(currencyPair);
		return marketRate;
	}
	
	@Override
	public MarketRate getRate(String currencyPair) 
	{
		if (currencyPair == null)
		{
			LOG.warn("FXBenchMarkRateCacheC.getRate():Currency Pair Supplied is wrong:" + currencyPair);
			return null;
		}
		// try primary source first followed by secondary sources
		FXBenchMarkRateEntry fxBenchMarkRateEntry = getFXBenchMarkRateEntry(currencyPair, false);
		if (fxBenchMarkRateEntry == null)
		{
			return null;
		}	
		return fxBenchMarkRateEntry.getRate();
	}

	
	
	@Override
	public MarketRate getCurrentRate(String baseCurrency, String variableCurrency) {
		if (baseCurrency == null || variableCurrency == null)
		{
			LOG.warn("FXBenchMarkRateCacheC.getCurrentRate():Base currency or Variable Currency Supplied is wrong : BaseCurrency:" 
											+ baseCurrency + ":VariableCurrency:" + variableCurrency);
			return null;
		}
		String currencyPair = baseCurrency + CURRENCY_PAIR_SEPARATOR + variableCurrency;
		MarketRate marketRate = getCurrentRate(currencyPair);
		return marketRate;		
	}

	@Override
	@Deprecated
	public MarketRate getCurrentRate(String currencyPair) {
		if (currencyPair == null)
		{
			LOG.warn("FXBenchMarkRateCacheC.getCurrentRate():Currency Pair Supplied is wrong:" + currencyPair);
			return null;
		}
		// try primary source first followed by secondary sources
		FXBenchMarkRateEntry fxBenchMarkRateEntry = getFXBenchMarkRateEntry( currencyPair, true);
		if (fxBenchMarkRateEntry == null)
		{
			return null;
		}	
		return fxBenchMarkRateEntry.getRate();		
	}

	private double getMid(final MarketRate marketRate)
	{
		if (marketRate == null)
		{
			return 0.0;
		}
		double mid = 0.0;
		String currencyPair = marketRate.getBaseCcy();
		double bid = marketRate.getBidRate();
		double offer = marketRate.getOfferRate();        			   			
		if (invalidRate(bid) && invalidRate(offer))
		{
			StringBuilder sb = new StringBuilder();
			sb.append("FXBenchMarkRateCacheC.getMid():Invalid BenchMark bid/offer rate received:CurrencyPair:");
			sb.append(currencyPair);
			sb.append(":QuoteId:");
			sb.append(marketRate.getQuoteId());
			sb.append(":bid:");
			sb.append(bid);
			sb.append(":offer:");
			sb.append(offer);
			LOG.error(sb.toString());
			return 0.0;
		}
		if (invalidRate(bid))
		{
			mid = offer;
		}
		else if (invalidRate(offer))
		{
			mid = bid;
		}
		else if (bid == offer)
		{
			mid = bid;
		}
		else
		{
			// calculate mid rate
			mid = (bid + offer)/2;
		}
		// As of now rounding it to 6 decimal places and going forward , we will modify
		mid = MathUtil.round(mid, DEFAULT_MID_RATE_PRECISION, BigDecimal.ROUND_HALF_UP);	
		if (LOG.isDebugEnabled())
		{
			StringBuilder sb = new StringBuilder();
			sb.append("FXBenchMarkRateCacheC.getMid()::FXBenchMarkRate:");
			sb.append(currencyPair);
			sb.append(":Bid:");
			sb.append(bid);
			sb.append(":Offer:");
			sb.append(offer);
			sb.append(":Mid:");
			sb.append(mid);
			LOG.debug(sb.toString());
		}
		return mid;
	}

	@Override
	public double getMidRate(String currencyPair) 
	{
   		MarketRate marketRate = getRate(currencyPair); 
		return getMid(marketRate);
	}
	


	@Override
	public double getMidRate(String baseCurrency, String variableCurrency) {
		if (baseCurrency == null || variableCurrency == null)
		{
			LOG.warn("FXBenchMarkRateCacheC.getMidRate():" +
					"Base currency or Variable Currency Supplied is wrong : BaseCurrency:" 
											 + baseCurrency + ":VariableCurrency:" + variableCurrency);
			return 0;
		}
		String currencyPair = baseCurrency + CURRENCY_PAIR_SEPARATOR + variableCurrency;		
		return getMidRate(currencyPair);
	}

	private boolean isRateStale(FXBenchMarkRateEntry rateEntry)
	{
		ISMBean isMbean = ISFactory.getInstance().getISMBean();
		long staleTimeInterval = isMbean.getBenchMarkPriceSourceStaleInterval();
		long entryTime = rateEntry.getEntryTime();
		return (System.currentTimeMillis() - entryTime >= staleTimeInterval);
		
	}
	private FXBenchMarkRateEntry getFXBenchMarkRateEntry(final String currencyPair, final boolean stalenessCheck)
	{
		// try primary source first followed by secondary sources		
		FXBenchMarkRateEntry primaryFXBenchMarkRateEntry = null;
		if (primaryPriceSource != null)
		{
			ConcurrentMap<String, FXBenchMarkRateEntry> marketRateMap = cache.get(primaryPriceSource);
			if (marketRateMap != null)
			{
				primaryFXBenchMarkRateEntry = marketRateMap.get(currencyPair);
			}			
		}
		if (primaryFXBenchMarkRateEntry != null)
		{
			if (stalenessCheck)
			{
				// check if rate from primary source is stale			
				if (isRateStale(primaryFXBenchMarkRateEntry))
				{
					StringBuilder sb = new StringBuilder();
					sb.append("FXBenchMarkRateCacheC.getFXBenchMarkRateEntry():");
					sb.append("BenchMark Rate is stale from Prmary Source for :CurrencyPair:");
					sb.append(currencyPair);
					sb.append(":PrimaryPriceSource:");
					sb.append(primaryPriceSource);
					sb.append(":QuoteId:");
					sb.append(primaryFXBenchMarkRateEntry.getRate().getQuoteId());
					sb.append(":ReceivedTime:");
					sb.append(primaryFXBenchMarkRateEntry.getEntryTime());
					sb.append(":Bid:");
					sb.append(primaryFXBenchMarkRateEntry.getRate().getBidRate());
					sb.append(":Offer:");
					sb.append(primaryFXBenchMarkRateEntry.getRate().getOfferRate());			
					LOG.info(sb.toString());					
				}
				else
				{
					// if Rate is not stale the return
					if (LOG.isDebugEnabled())
					{
						LOG.debug("FXBenchMarkRateCacheC.getFXBenchMarkRateEntry():PriceSource:" 
													+ primaryPriceSource + ":" + primaryFXBenchMarkRateEntry);
					}
					return primaryFXBenchMarkRateEntry;
				}
			}
			else
			{
				// if staleness check is not required the return what ever is present
				if (LOG.isDebugEnabled())
				{
					LOG.debug("FXBenchMarkRateCacheC.getFXBenchMarkRateEntry():PrimaryPriceSource:" 
														+ primaryPriceSource + ":" + primaryFXBenchMarkRateEntry);
				}
				return primaryFXBenchMarkRateEntry;
			}			
		}
		if (LOG.isDebugEnabled())
		{
			LOG.debug("FXBenchMarkRateCacheC.getFXBenchMarkRateEntry():" +
				"FXBenchMark is not available with Primary source so trying secondary sources:" + secondaryPriceSources);
		}
		// check all secondary sources
		FXBenchMarkRateEntry secondaryFXBenchMarkRateEntry = null;
		if (secondaryPriceSources != null)
		{
			for (String secondaryPriceSource : secondaryPriceSources)
			{
				ConcurrentMap<String, FXBenchMarkRateEntry> marketRateMap = cache.get(secondaryPriceSource);
				if (marketRateMap != null)
				{
					secondaryFXBenchMarkRateEntry = marketRateMap.get(currencyPair);						
					if (secondaryFXBenchMarkRateEntry != null)
					{
						if (stalenessCheck)
						{
							if (isRateStale(secondaryFXBenchMarkRateEntry))
							{
								// check other secondary sources
								if (LOG.isDebugEnabled())
								{
									StringBuilder sb = new StringBuilder();
									sb.append("FXBenchMarkRateCacheC.getFXBenchMarkRateEntry():BenchMarkRate received from secondary source is stale:");
									sb.append(secondaryPriceSource);
									sb.append(":CurrencyPair:");
									sb.append(currencyPair);
									sb.append(":Bid:");
									sb.append(secondaryFXBenchMarkRateEntry.getRate().getBidRate());
									sb.append(":Offer:");
									sb.append(secondaryFXBenchMarkRateEntry.getRate().getOfferRate());								
									LOG.debug(sb.toString());
								}
								continue;
							}
							else
							{
								// If rate is not stale then return
								if (LOG.isDebugEnabled())
								{
									LOG.debug("FXBenchMarkRateCacheC.getFXBenchMarkRateEntry():SecondaryPriceSource:" 
																+ secondaryPriceSource + ":" + secondaryFXBenchMarkRateEntry);
								}
								return secondaryFXBenchMarkRateEntry;
							}
						}
						else
						{
							// If staleness check is not required then return from whatever first source 
							// rate is found from
							if (LOG.isDebugEnabled())
							{
								LOG.debug("FXBenchMarkRateCacheC.getFXBenchMarkRateEntry():SecondaryPriceSource:" 
															+ secondaryPriceSource + ":" + secondaryFXBenchMarkRateEntry);
							}

							return secondaryFXBenchMarkRateEntry;
						}
					}
				}					
			}
		}
		return null;
	}	


	@Override
	public void registerUpdateListener(FXBenchMarkUpdateListener updateListener) 
	{
		if (updateListener == null)
		{
			return;
		}
		listeners.add(updateListener);
	}

	@Override
	@Deprecated
	public double getCurrentMidRate(String currencyPair) 
	{
		MarketRate marketRate = getCurrentRate(currencyPair);
		return getMid(marketRate);
	}

	@Override
	public double getCurrentMidRate(String baseCurrency, String variableCurrency, boolean useDisplayName )
	{
		if (baseCurrency == null || variableCurrency == null)
		{
			LOG.warn("FXBenchMarkRateCacheC.getCurrentMidRate():" +
					 "Base currency or Variable Currency Supplied is wrong : BaseCurrency:" 
											 + baseCurrency + ":VariableCurrency:" + variableCurrency);
			
			return 0.0;
		}
		return getCurrentMidRate ( CurrencyFactory.getCurrency ( baseCurrency ), CurrencyFactory.getCurrency ( variableCurrency  ), useDisplayName );
	}

	public double getCurrentMidRate(String baseCurrency, String variableCurrency )
	{
		return getCurrentMidRate ( baseCurrency, variableCurrency, false );
	}

	@Override
	public double getCurrentMidRate( Currency baseCcy, Currency varCcy, boolean useDisplayName )
	{
		if (baseCcy == null || varCcy == null)
		{
			LOG.warn("FXBenchMarkRateCacheC.getCurrentMidRate():" +
					"Base currency or Variable Currency Supplied is null : BaseCurrency:"
					+ baseCcy + ":VariableCurrency:" + varCcy);

			return 0.0;
		}
		String baseStr = useDisplayName ? baseCcy.getDisplayName () : baseCcy.getShortName ();
		String varStr = useDisplayName ? varCcy.getRealCurrency ().getDisplayName () : varCcy.getRealCurrency ().getShortName ();
		String currencyPair = baseStr + CURRENCY_PAIR_SEPARATOR + varStr;
		return getCurrentMidRate(currencyPair);
	}

	@Override
	public double getCurrentMidRate( Currency baseCcy, Currency varCcy )
	{
		return getCurrentMidRate ( baseCcy, varCcy, false );
	}

	@Override
	public double getCurrentMidRate ( CurrencyPair ccyPair, boolean useDisplayName )
	{
		return getCurrentMidRate( ccyPair.getBaseCurrency (), ccyPair.getVariableCurrency (), useDisplayName );
	}

	@Override
	public double getCurrentMidRate ( CurrencyPair ccyPair )
	{
		return getCurrentMidRate( ccyPair.getBaseCurrency (), ccyPair.getVariableCurrency (), false );
	}

	@Override
	public void setPrimaryPriceSource(String primaryPriceSource) 
	{
		this.primaryPriceSource = primaryPriceSource;
	}

	@Override
	public void setSecondaryPriceSources(List<String> secondaryPriceSources)
	{
		this.secondaryPriceSources = secondaryPriceSources;
	}

	private static final class FXBenchMarkRateEntry {
		private MarketRate rate;
		private long entryTime;
		
		public FXBenchMarkRateEntry(MarketRate rate)
		{
			this.rate = rate;
			this.entryTime = System.currentTimeMillis();
		}

		public MarketRate getRate() 
		{
			return rate;
		}

		public long getEntryTime() 
		{
			return entryTime;
		}	

		@Override
		public String toString() {
			StringBuilder sb = new StringBuilder();
			sb.append("FXBenchMarkRateEntry[");
			sb.append(",CurrencyPair=");
			sb.append(rate.getBaseCcy());
			sb.append(CURRENCY_PAIR_SEPARATOR);
			sb.append(rate.getVariableCcy());
			sb.append(",EntryTime=");
			sb.append(entryTime);
			sb.append(",Bid=");
			sb.append(rate.getBidRate());
			sb.append(",Offer=");
			sb.append(rate.getOfferRate());
			sb.append("]");
			return sb.toString();
		}
		
		
	}
}
