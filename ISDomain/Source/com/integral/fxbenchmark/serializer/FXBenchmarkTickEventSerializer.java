package com.integral.fxbenchmark.serializer;

import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXRateBasis;
import com.integral.fxbenchmark.FXBenchmarkTickEvent;
import com.integral.fxbenchmark.config.FXBenchmarkTickerConfig;
import com.integral.fxbenchmark.startup.FXBenchmarkTickerStartupC;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.message.MarketRate;
import com.integral.is.message.MarketRateC;
import com.integral.is.message.MarketRateSerializer;
import com.integral.is.message.MarketRateSerializerFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.math.MathUtil;
import com.integral.ticker.TickEventSerializer;
import com.integral.transport.multicast.MulticastAddress;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.nio.ByteBuffer;

/**
 * Created by pranabdas on 11/11/14.
 */
public class FXBenchmarkTickEventSerializer implements TickEventSerializer<ByteBuffer, FXBenchmarkTickEvent>
{
    private static final String UTF8 = "UTF-8";
    private FXBenchmarkTickerConfig config;
    private MarketRateSerializerFactory serializerFactory = MarketRateSerializerFactory.instance();
    private static final Log log = LogFactory.getLog( FXBenchmarkTickEventSerializer.class );
    public FXBenchmarkTickEventSerializer( FXBenchmarkTickerConfig config ) {
        this.config = config;
    }

    public ByteBuffer serialize( ByteBuffer byteBuffer, FXBenchmarkTickEvent tickEvent ) {
        byte[] currPairBytes;
        byte[] guidBytes;
        try {
            currPairBytes = tickEvent.getCurrencyPair().getBytes( UTF8 );
            guidBytes = tickEvent.getGuid().getBytes( UTF8 );
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("UTF-8 Encoding Unsupported", e);
        }
        byteBuffer.putInt( tickEvent.getIndex() );
        byteBuffer.putInt( currPairBytes.length );
        byteBuffer.put( currPairBytes );
        byteBuffer.putDouble( tickEvent.getRate() );
        byteBuffer.putInt( guidBytes.length );
        byteBuffer.put( guidBytes );
        byteBuffer.putLong( tickEvent.getTimestamp() );
        return byteBuffer;
    }

    public FXBenchmarkTickEvent deserialize( ByteBuffer buffer ) {
        FXBenchmarkTickEvent event = new FXBenchmarkTickEvent();
        try{
            byte[] packet = buffer.array();
            int offset = buffer.position();
            MarketRateSerializer serializer = serializerFactory.getSerializerForVersion( buffer.get() );
            int orgIndex = serializer.getProviderIndex( packet, offset );
            int streamIndex = serializer.getStreamIndex( packet, offset );
            if( orgIndex != FXBenchmarkTickerStartupC.fxbSourceAdaptorIndex ){
                if( log.isDebugEnabled() ){
                    log.debug( "FXBTE.deserialize : expected orgIndex=" + FXBenchmarkTickerStartupC.fxbSourceAdaptorIndex + ", received=" + orgIndex );
                }
                return null;
            }
            // once the providerIndex matches, the multicastgroup will be same.
            MulticastAddress addr = FXBenchmarkTickerStartupC.fxbSourecMCastGrp.getMulticastAddressForIndex( streamIndex );
            if (addr == null) {
                if( log.isDebugEnabled() ){
                    log.debug("FXBTE.deserialize : No mcast address configured for stream with index: " + streamIndex + " for org index: " + orgIndex );
                }
                return null;
            }

            if( !addr.getStream().getShortName().equals( FXBenchmarkTickerStartupC.fxbSourceStreamName ) ){
                if( log.isDebugEnabled() ){
                    log.debug( "FXBTE.deserialize : expected stream=" + FXBenchmarkTickerStartupC.fxbSourceStreamName + ", received=" + addr.getStream().getShortName() );
                }
                return null;
            }

            MarketRate rate = new MarketRateC( 0, 0 );
            serializer.deserialize( buffer, rate );
            if( rate.isStale() ){
                if( log.isDebugEnabled() ){
                    log.debug( "FXBTE.deserialize : rate is stale, so dropping it for " + rate.getQuoteId() );
                }
                return null;
            }
            event.setCurrencyPair( CurrencyFactory.getCurrencyPairName( rate.getBaseCcy(), rate.getVariableCcy() ) );
            event.setIndex( config.getBufferKeyIndex( event.getCurrencyPair() ) );
            
            final FXRateBasis fxRateBasis = QuoteConventionUtilC.getInstance().getDefault().getFXRateBasis( CurrencyFactory.getCurrency( rate.getBaseCcy() ), CurrencyFactory.getCurrency( rate.getVariableCcy() ) );
            double bidRate = rate.getBidRate();
            double offerRate = rate.getOfferRate();
            event.setRate( MathUtil.round( ( bidRate == 0.0 || offerRate == 0.0 ? bidRate + offerRate : ( bidRate + offerRate ) / 2.0 ) , fxRateBasis.getSpotPrecision(), BigDecimal.ROUND_HALF_UP) );
            event.setGuid( rate.getQuoteId() );
            event.setTimestamp( rate.getRateEffective() );
        }
        catch ( Exception ex )
        {
            log.error( "FXBenchmarkTickEventSerializer.de-serialize : error de-serializing benchmark rate. ", ex );
            return null;
        }
        return event;
    }


    private String getString( final byte[] bytes, String encoding ) {
        try {
            String str = new String( bytes, encoding );
            return str;
        } catch (UnsupportedEncodingException e) {
            //This should not happen.
            throw new RuntimeException("UTF-8 Encoding Unsupported", e);
        }

    }

}
