package com.integral.fxbenchmark.startup;

import java.util.Hashtable;

import com.integral.fxbenchmark.FXBenchMarkService;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.runtime.StartupTask;

public class FXBenchMarkServiceStartup implements StartupTask{
	private Log LOG = LogFactory.getLog(FXBenchMarkServiceStartup.class);
	@SuppressWarnings("rawtypes")
	@Override
	public String startup(String aName, Hashtable args) throws Exception 
	{
    	ISMBean isMbean = ISFactory.getInstance().getISMBean();
    	if (isMbean.isBenchMarkServiceEnabled())
    	{
    		try 
    		{
    			FXBenchMarkService.getInstance().start();  
    		}
    		catch(Throwable t)
    		{
    			LOG.error("FXBenchMarkServiceStartup.startup():Problem with starting FXBenchMarkService.", t);
    			throw new Exception(t);
    		}    		  		
    	}
    	else
    	{
    		LOG.warn("FXBenchMarkServiceStartup.startup():FXBenchMarkService is not enabled so not starting.");
    		
    	}
    	return null;
	}
}
