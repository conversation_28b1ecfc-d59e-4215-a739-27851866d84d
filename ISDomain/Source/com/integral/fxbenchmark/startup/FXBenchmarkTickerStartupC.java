package com.integral.fxbenchmark.startup;

import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.Stream;
import com.integral.fxbenchmark.FXBenchmarkTickEvent;
import com.integral.fxbenchmark.serializer.FXBenchmarkTickEventSerializer;
import com.integral.fxbenchmark.config.FXBenchmarkTickerConfig;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.runtime.StartupTask;
import com.integral.ticker.TickerFactory;
import com.integral.ticker.TickerService;
import com.integral.tradingvenue.REXConfig;
import com.integral.transport.multicast.MulticastGroup;
import com.integral.user.Organization;

import java.util.Hashtable;


/**
 * Created by pranabdas on 11/11/14.
 */
public class FXBenchmarkTickerStartupC implements StartupTask{
    private static final Log log = LogFactory.getLog( FXBenchmarkTickerStartupC.class );
    private static final String DEFAULT_FXBENCHMARK_PRICE_SOURCE = "FXB";
    private int fxbMulticastPort;
    private String fxbMulticastAddress;
    public static int fxbSourceAdaptorIndex;
    public static String fxbSourceStreamName;
    public static MulticastGroup fxbSourecMCastGrp;

    public String startup(String aName, Hashtable args) throws Exception {
        String result = "Started";
        TickerService<FXBenchmarkTickEvent, FXBenchmarkTickerConfig> service =
                TickerFactory.getInstance().getTickerService( FXBenchmarkTickEvent.class );
        if ( service.getConfiguration().isTickServiceEnabled() ) {
            try {
                service.setTickEventSerializer( new FXBenchmarkTickEventSerializer( service.getConfiguration() ) );
                getMulticastInfo();
                if( fxbMulticastAddress != null )
                {
                    service.getConfiguration().setMulticastAddress( fxbMulticastAddress, fxbMulticastPort );
                }
                else{
                    log.info( "FXBenchmark Ticker Service not started as price source property is not configured or the value is not correct." );
                    result = "Not Configured";
                    return result;
                }
                service.start();
                log.info( "FXBenchmark Ticker Service started successfully. MuticastAddress " + fxbMulticastAddress + ", port " + fxbMulticastPort );
            } catch ( Exception e ) {
                log.error( "Unable to start FXBenchmark Ticker Service", e );
                throw e;
            }
        } else {
            log.info( "FXBenchmark Ticker Service not started as it is not configured on this server." );
            result = "Not Configured";
        }
        return result;
    }

    public void getMulticastInfo(){
        String priceSrcString = REXConfig.getInstance().getPriceSource( DEFAULT_FXBENCHMARK_PRICE_SOURCE );
        if( priceSrcString == null ){
            log.error( "FXBTSC.getMulticastInfo : price source not defined for this server." );
            return;
        }
        String adaptorName = null;
        String streamName = null;
        Organization adaptorOrg = null;
        Stream stream = null;
        if( priceSrcString != null ){
            String[] priceSources = priceSrcString.split( "," );
            if( priceSources != null && priceSources.length > 0 ){
                String[] source = priceSources[0].split( ":" );
                if( source != null && source.length == 2 ){
                    adaptorName = source[0];
                    streamName = source[1];
                }
            }
        }
        if( adaptorName != null && streamName != null ){
            adaptorOrg = ReferenceDataCacheC.getInstance().getOrganization( adaptorName );
            if( adaptorOrg == null ){
                log.error( "FXBTSC.getMulticastInfo : adaptor org not found." );
                return;
            }
            fxbSourceAdaptorIndex = adaptorOrg.getIndex();
            BrokerOrganizationFunction bof = adaptorOrg.getBrokerOrganizationFunction();
            if( bof == null ) return;
            stream = bof.getStream( streamName );
            if( stream == null ){
                log.error( "FXBTSC.getMulticastInfo : stream not configured for adaptor " + adaptorName );
                return;
            }
        }
        else{
            log.error( "FXBTSC.getMulticastInfo : price source property not defined properly for this server." );
            return;
        }

        StringBuilder sb = new StringBuilder( 200 );
        sb.append( "FXBTSC.getMulticastInfo : Subscribing to Multicast address for Adaptor[" );
        sb.append( adaptorName ).append( ']' );
        sb.append( ",Stream[" ).append( streamName ).append( "]" );
        MulticastGroup mcastGroup = adaptorOrg.getMulticastGroup();
        if ( mcastGroup != null && mcastGroup.isEnabled() && !mcastGroup.getAddresses().isEmpty() )
        {
            fxbMulticastPort = mcastGroup.getLogicalPort().getPort();
            try
            {
                fxbMulticastAddress = stream.getMulticastAddress().getAddress();
                fxbSourceStreamName = streamName;
                fxbSourecMCastGrp = mcastGroup;
                log.info( sb.toString() );
            }
            catch ( Exception e )
            {
                log.error( "FXBTSC.getMulticastInfo : Exception ", e );
            }
        }
    }

}
