package com.integral.fxbenchmark;

import java.util.List;

import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.message.MarketRate;

public interface FXBenchMarkRateProvider {
	/**
	 * Get Last MarketRate regardless of rate is stale or not
	 * @param baseCurrency
	 * @param variableCurrency
	 * @return
	 */
	MarketRate getRate(String baseCurrency, String variableCurrency);

	/**
	 * Get Last MarketRate regardless of rate is stale or not
	 * @param currencyPair
	 * @return
	 */
	MarketRate getRate(String currencyPair);
	
	/**
	 * This method returns current non-stale MarketRate
	 * @param baseCurrency
	 * @param variableCurrency
	 * @return --> Null if no non-stale rate is available
	 */
	MarketRate getCurrentRate(String baseCurrency, String variableCurrency);
	
	
	/**
	 * This method returns current non-stale MarketRate
	 * @param currencyPair
	 * @return --> Null if no non-stale rate is available
	 */
	MarketRate getCurrentRate(String currencyPair);
	
	/**
	 * Register listeners for updates
	 * @param updateListener
	 */
	void registerUpdateListener(FXBenchMarkUpdateListener updateListener);
	
	/**	
	 * returns last Mid rate regardless of MarketRate is stale or not
	 * if bid rate is invalid then offer rate is return and vice versa
	 * but if bid and offer , both are valid then mid is calculated and returned.
	 * return 0.0 if mid cannot be calculated.
	 * @param currencyPair
	 * @return
	 */
	double getMidRate(String currencyPair);
	
	/**
	 * returns last Mid rate regardless of MarketRate is stale or not
	 * @param baseCurrency
	 * @param variableCurreny
	 * @return
	 */
	double getMidRate(String baseCurrency, String variableCurreny);
	
	/**
	 * This will return only valid mid rate which is not stale.
	 * If rate was published before time out period then 
	 * mid rate returned is zero.
	 * If primary source is not having non-stale mid rate then secondary sources are checked
	 * @param currencyPair
	 * @return --> returns 0.0 if no valid mid rate can be calculated
	 */
	double getCurrentMidRate(String currencyPair);
	
	/**
	 * This will return only valid mid rate which is not stale.
	 * If rate was published before time out period then 
	 * mid rate returned is zero.
	 * If primary source is not having non-stale mid rate then secondary sources are checked
	 * @param baseCurrency base currency
	 * @param variableCurrency variable currency
	 * @param useDisplayName use display name
	 * @return mid rate
	 */
	double getCurrentMidRate(String baseCurrency, String variableCurrency, boolean useDisplayName );

	double getCurrentMidRate(String baseCurrency, String variableCurrency );

	double getCurrentMidRate ( Currency baseCcy, Currency varCcy );

	double getCurrentMidRate ( Currency baseCcy, Currency varCcy, boolean useDisplayName );

	double getCurrentMidRate ( CurrencyPair ccyPair, boolean useDisplayName );

	double getCurrentMidRate ( CurrencyPair ccyPair );

	/**
	 * Primary Source for bench mark rate
	 * @param primaryPriceSource
	 */
	void setPrimaryPriceSource(String primaryPriceSource);
	
	/**
	 * Secondary sources for bench mark rate
	 * @param secondaryPriceSources
	 */
	void setSecondaryPriceSources(List<String> secondaryPriceSources);
	

}
