package com.integral.is.util;

import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.Stream;
import com.integral.broker.model.StreamC;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Entity;
import com.integral.query.QueryFactory;
import com.integral.query.QueryService;
import com.integral.user.Organization;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;

import java.util.*;

public class StreamUtil {
	private static Log log = LogFactory.getLog(StreamUtil.class);

	public static Stream getStream(String streamId, Organization toOrg) {
		Stream stream = null;
		BrokerOrganizationFunction bof = toOrg.getBrokerOrganizationFunction();
		if (bof != null) {
			stream = bof.getStream(streamId);
			if (stream == null) {
				log.warn("getStream : Stream is null for Organization " + toOrg.getShortName() + ",StreamId "
						+ streamId);
			}
		} else {
			log.warn("getStream : BrokerOrganizationFunction is null for Organization " + toOrg.getShortName());
		}
		return stream;
	}
	
	public static Stream getStream(LegalEntity le, Organization toOrg) {
		if (le == null || toOrg == null) {
			return null;
		}
		Stream stream = null;
		TradingParty tp = le.getTradingParty(toOrg);
		if (tp != null) {
			String streamId = getStreamId(tp);
			if (streamId != null) {
				stream = getStream(streamId, toOrg);
			}
		} else {
			if (log.isDebugEnabled()) {
				log.debug("StreamUtil.getStream - Couldn't return stream as tp is null .For LE - " + le + "\t TOORG - "
						+ toOrg);
			}
		}
		return stream;
	}

	public static String getStreamId(LegalEntity le, Organization toOrg) {
		TradingParty tp = le.getTradingParty(toOrg);
		if (tp != null) {
			return getStreamId(tp);
		} else {
			if (log.isDebugEnabled()) {
				log.debug("getStreamId - Couldn't return stream id as tp is null .For LE - " + le + "\t TOORG - "
						+ toOrg);
			}
		}
		return null;
	}

	public static String getStreamId(TradingParty tp) {
		String streamId = tp.getProviderStreamId();
		if (streamId == null) {
			BrokerOrganizationFunction bof = tp.getOrganization().getBrokerOrganizationFunction();
			if (bof != null) {
				if (bof.getDefaultStream() != null) {
					streamId = bof.getDefaultStream().getShortName();
					if (log.isDebugEnabled()) {
						log.debug("getStreamId Trading party " + tp.getShortName()
								+ " stream is null hence using default stream " + streamId
								+ " from BrokerOrganizationFunction for provider "
								+ tp.getOrganization().getShortName());
					}
				} else {
					if (log.isDebugEnabled()) {
						log.debug("getStreamId - BrokerOrganizationFunction.getDefaulStream is null for provider "
								+ tp.getOrganization().getShortName());
					}
				}
			} else {
				if (log.isDebugEnabled()) {
					log.debug("getStreamId - BrokerOrganizationFunction is null for provider "
							+ tp.getOrganization().getShortName());
				}
			}
		}
		log.info("Found stream=" + streamId + " for tradingParty=" + tp);
		return streamId;
	}

	public static Set<Stream> getStreams(String virtualServer) {
		Set<Stream> streams = new HashSet<Stream>();
		try{
			QueryService qryService = QueryFactory.getQueryService();
			ExpressionBuilder builder = new ExpressionBuilder();
			Expression vsExpr = builder.get( "virtualServerName" ).equal( virtualServer );
			ReadAllQuery qry = new ReadAllQuery( StreamC.class, vsExpr );
			Vector<StreamC> streamCVector = ( Vector<StreamC> ) qryService.find( qry, Entity.ACTIVE_STATUS );
			if ( streamCVector != null ) {
				streams.addAll(streamCVector);
			}
		}catch ( IdcNoSuchObjectException e ) {
			log.error( ".getStreams : Exception while retrieving stream for virtual server=" + virtualServer, e );
		}
		return streams;
	}
}
