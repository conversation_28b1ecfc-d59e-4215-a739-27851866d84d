package com.integral.is.util;

import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyComparatorC;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.dealing.TradeChannelSupport;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.is.common.ISConstantsC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.provider.ProviderOrgFunction;
import com.integral.tradingvenue.TradingVenueEntity;
import com.integral.tradingvenue.TradingVenueRelationShip;
import com.integral.user.Organization;
import com.integral.user.OrganizationRelationship;

import java.util.*;

/**
 * Created by nagarajap on 6/29/2017.
 */
public class CurrencyUtil {

    private static Log log = LogFactory.getLog(CurrencyUtil.class);

    public static CurrencyPairGroup getCurrencyPairGroup(Organization fi, Organization lp) {
        OrganizationRelationship or = lp.getOrgRelationship(fi, ISConstantsC.LP_ORG_RELATIONSHIP);
        if (or == null) {
            log.warn("LP_FI relation does not exist b/w FI:" + fi.getShortName() + ":LP:" + lp.getShortName());
            return null;
        }

        CurrencyPairGroup cpg = or.getOneClickCurrencyPairGroup();
        if (cpg != null) {
            return cpg;
        } else if (lp.getProviderOrgFunction() != null) {
            ProviderOrgFunction providerOrgFunc = lp.getProviderOrgFunction();
            CurrencyPairGroup pofCpg = providerOrgFunc.getOneClickCurrencyPairGroup();
            if (pofCpg != null) {
                return pofCpg;
            } else {
                log.warn("One click currency paircount group is not set for lp:" + lp.getShortName());
            }
        } else {
            log.warn("CurrencyPair Group is null. " + lp.getShortName());
        }
        return null;
    }

    public static Collection<CurrencyPair> getCurrencyPairs ( Organization fi, Organization lp )
    {
        CurrencyPairGroup cpg = getCurrencyPairGroup ( fi, lp );
        return cpg != null ? cpg.getCurrencyPairs () : null;
    }

    public static Collection<Currency> getCurrencies( Organization fi, Organization lp )
    {
        Collection<Currency> ccyList = null;
        Collection<CurrencyPair> ccyPairs = getCurrencyPairs ( fi, lp );
        if ( ccyPairs != null && !ccyPairs.isEmpty () )
        {
            ccyList = new HashSet<Currency> ();
            for ( CurrencyPair ccyPair: ccyPairs )
            {
                if ( ccyPair.getBaseCurrency () != null )
                {
                    ccyList.add ( ccyPair.getBaseCurrency () );
                }
                if ( ccyPair.getVariableCurrency () != null )
                {
                    ccyList.add ( ccyPair.getVariableCurrency () );
                }
            }
        }
        else
        {
            log.info ( "CU.getCurrencies : no currency pairs configured for lp=" + lp + ",fi=" + fi );
        }
        return ccyList;
    }

    public static Collection<CurrencyPair> getCurrencyPairsForTVLP(Organization fi, Organization tvORg) {

        List<CurrencyPair> commonCurrencyPairs = new ArrayList<CurrencyPair>();
        if (tvORg.getTradingVenueOrgFunction() == null) {
            // treat as  normal bilateral relationship if the TVOrgFunction is null
            return getCurrencyPairs(fi, tvORg);
        }

        TradingVenueRelationShip tradingVenueRelationship = fi.getTradingVenueRelationShip(tvORg);
        if (tradingVenueRelationship == null) {
            log.warn("CurrencyPair Group is empty since there is no trading venue relationship between LP:" + tvORg
                    .getShortName() + ":and FI:" + fi.getShortName());
            return commonCurrencyPairs;
        }

        TradingVenueEntity tradingVenue = tvORg.getTradingVenueOrgFunction().getTradingVenue();
        CurrencyPairGroup cpgTV = tradingVenue.getSupportedCurrencyPairs();
        if (cpgTV == null || cpgTV.getCurrencyPairs().isEmpty()) {
            log.warn("CurrencyPair Group is empty since there are no supported currency pairs in TV:" + tradingVenue
                    .getShortName());
            return commonCurrencyPairs;
        }

        if (tradingVenueRelationship.getClearingMember() == null) {
            return cpgTV.getCurrencyPairs();
        }

        Organization cm = tradingVenueRelationship.getClearingMember().getOrganization();
        Collection<CurrencyPair> cpgForFICM = getCurrencyPairsForTVLP(fi, cm);
        if (cpgForFICM == null || cpgForFICM.isEmpty()) {
            return cpgTV.getCurrencyPairs();
        }

        // consider only the common currency pairs between TV Currency Pairs and FI's CM currency pairs
        for (CurrencyPair ccyP : cpgForFICM) {
            if (cpgTV.getCurrencyPairs().contains(ccyP)) {
                commonCurrencyPairs.add(ccyP);
            }
        }
        return commonCurrencyPairs;
    }

    public static CurrencyPairGroup getDefaultSupportedCurrencyPairGroup( Organization lp )
    {
        ProviderOrgFunction providerOrgFunc = lp.getProviderOrgFunction();
        if ( providerOrgFunc != null )
        {
            CurrencyPairGroup pofCpg = providerOrgFunc.getOneClickCurrencyPairGroup();
            if ( pofCpg != null )
            {
                return pofCpg;
            }
            else
            {
                log.warn( "CU:getDefaultCurrencyPairGroup : One click currency paircount group is not set for lp:" + lp.getShortName() );
            }
        }
        else
        {
            log.warn( "CU:getDefaultCurrencyPairGroup : CurrencyPair Group is null. " + lp.getShortName() );
        }
        return null;
    }

    public static Collection<CurrencyPair> getDefaultSupportedCurrencyPairs ( Organization lp )
    {
        CurrencyPairGroup cpg = getDefaultSupportedCurrencyPairGroup ( lp );
        return cpg != null ? cpg.getCurrencyPairs () : null;
    }

    public static Collection<Currency> getDefaultSupportedCurrencies( Organization lp )
    {
        Collection<Currency> ccyList = null;
        Collection<CurrencyPair> ccyPairs = getDefaultSupportedCurrencyPairs ( lp );
        if ( ccyPairs != null && !ccyPairs.isEmpty () )
        {
            ccyList = new HashSet<Currency> ();
            for ( CurrencyPair ccyPair: ccyPairs )
            {
                if ( ccyPair.getBaseCurrency () != null )
                {
                    ccyList.add ( ccyPair.getBaseCurrency () );
                }
                if ( ccyPair.getVariableCurrency () != null )
                {
                    ccyList.add ( ccyPair.getVariableCurrency () );
                }
            }
        }
        else
        {
            log.info ( "CU.getDefaultSupportedCurrencies : no currency pairs configured for lp=" + lp );
        }
        return ccyList;
    }

    public static Collection<FXRateBasis> getEffectiveFXRateBasis( FXRateConvention conv, TradeChannelSupport tcs )
    {
        return getEffectiveFXRateBasis( conv, tcs.isESPOutrightSupported (), tcs.isESPSwapSupported () );
    }

    public static Collection<FXRateBasis> getEffectiveFXRateBasis( Collection<FXRateBasis> fxRateBases, Organization org )
    {
        return getEffectiveFXRateBasis( fxRateBases, org.getProviderOrgFunction ().getTradeChannelSupport () );
        
    }
    
    public static Collection<FXRateBasis> getEffectiveFXRateBasis( Collection<FXRateBasis> fxRateBases, TradeChannelSupport tcs )
    {
    	return getEffectiveFXRateBasis( fxRateBases, tcs.isESPOutrightSupported (), tcs.isESPSwapSupported () );
    }
    
    public static Collection<FXRateBasis> getEffectiveFXRateBasis( Collection<FXRateBasis> fxRateBases, boolean isESPOutrightSupported, boolean isESPSwapSupported )
    {
    	if (fxRateBases == null || fxRateBases.size() == 0)
    	{
    		return Collections.emptyList();
    	}
        Collection<FXRateBasis> effectiveFXRateBases = new ArrayList<FXRateBasis> (  );
        for ( FXRateBasis fxRateBasis : fxRateBases )
        {
            if ( ( fxRateBasis.isForwardSettlementType () && !isESPOutrightSupported ) || ( fxRateBasis.isSwapSettlementType () && !isESPSwapSupported ) )
            {
                continue;
            }
            effectiveFXRateBases.add( fxRateBasis );
        }
        return effectiveFXRateBases;
    }
    
    public static Collection<FXRateBasis> getEffectiveFXRateBasis( FXRateConvention conv, Organization org )
    {
        if ( org == null || org.getProviderOrgFunction () == null )
        {
            log.info ( "CU.getEffectiveFXRateBasis : either org is null or org does not have provider org function. conv=" + conv + ",org=" + org );
            return getEffectiveFXRateBasis ( conv, true, true );
        }
        return getEffectiveFXRateBasis ( conv, org.getProviderOrgFunction ().getTradeChannelSupport() );
    }

    public static Collection<FXRateBasis> getEffectiveFXRateBasis( FXRateConvention conv, Organization fiOrg, Organization lpOrg )
    {
        OrganizationRelationship orgRel = lpOrg.getOrgRelationship ( fiOrg, CounterpartyUtilC.LP_FI_RELATIONSHIP );
        if ( orgRel != null )
        {
            TradeChannelSupport lpOrgTcs = lpOrg.getProviderOrgFunction ().getTradeChannelSupport ();
            TradeChannelSupport orgRelTcs = orgRel.getTradeChannelSupport ();
            final boolean isESPOutrightSupported = lpOrgTcs.isESPOutrightSupported () && orgRelTcs.isESPOutrightSupported ();
            final boolean isESPSwapSupported = lpOrgTcs.isESPSwapSupported () && orgRelTcs.isESPSwapSupported ();
            return getEffectiveFXRateBasis ( conv, isESPOutrightSupported, isESPSwapSupported );
        }
        else
        {
            log.info ( "CU.getEffectiveFXRateBasis : no lp-fi relationship between lpOrg=" + lpOrg + ",fiOrg=" + fiOrg + ",conv=" + conv );
        }
        return Collections.emptyList();
    }

    public static Collection<FXRateBasis> getEffectiveFXRateBasis( FXRateConvention conv, boolean isESPOutrightSupported, boolean isESPSwapSupported )
    {
        Collection<FXRateBasis> fxRateBases = new ArrayList<FXRateBasis> (  );
        for ( FXRateBasis fxRateBasis : conv.getEffectiveFXRateBasis () )
        {
            if ( ( fxRateBasis.isForwardSettlementType () && !isESPOutrightSupported ) || ( fxRateBasis.isSwapSettlementType () && !isESPSwapSupported ) )
            {
                continue;
            }
            fxRateBases.add( fxRateBasis );
        }
        return fxRateBases;
    }

    public static Collection<FXRateBasis> getCustomIncludedFXRateBasisReferences( CurrencyPairGroup cpg, Organization org )
    {
        if ( org == null || org.getProviderOrgFunction () == null )
        {
            log.info ( "CU.getCustomIncludedFXRateBasisReferences : either org is null or org does not have provider org function. cpg=" + cpg + ",org=" + org );
            return getCustomIncludedFXRateBasisReferences ( cpg, true, true );
        }
        return getCustomIncludedFXRateBasisReferences( cpg, org.getProviderOrgFunction ().getTradeChannelSupport () );
    }

    public static Collection<FXRateBasis> getCustomIncludedFXRateBasisReferences( CurrencyPairGroup cpg, TradeChannelSupport tcs )
    {
        return getCustomIncludedFXRateBasisReferences( cpg, tcs.isESPOutrightSupported (), tcs.isESPSwapSupported () );
    }

    /**
     * This provices a list of included currency pairs filtered for settlement type support.
     * However, it does not consider excluded currency pairs and currency groups part of currency pair group.
     * @param cpg currency pair group
     * @param isESPOutrightSupported ESP outright channel supported
     * @param isESPSwapSupported ESP swap channel supported
     * @return custom currency pairs that is included.
     */
    public static Collection<FXRateBasis> getCustomIncludedFXRateBasisReferences( CurrencyPairGroup cpg, boolean isESPOutrightSupported, boolean isESPSwapSupported )
    {
        Collection<FXRateBasis> fxRateBases = cpg.getFXRateBasisReferences ();
        if ( fxRateBases != null )
        {
            Collection<FXRateBasis> rateBases = new ArrayList<FXRateBasis>();
            for ( FXRateBasis rb: fxRateBases )
            {
                if ( ( rb.isForwardSettlementType () && !isESPOutrightSupported ) || ( rb.isSwapSettlementType () && !isESPSwapSupported ) )
                {
                    continue;
                }
                rateBases.add ( rb );
            }
            return rateBases;
        }
        return Collections.emptyList();
    }

    /**
     * This provices a list of included currency pairs filtered for settlement type support.
     * However, it does not consider excluded currency pairs and currency groups part of currency pair group.
     * @param cpg currency pair group
     * @param fiOrg fi org
     * @param lpOrg lp org
     * @return custom currency pairs that is included.
     */
    public static Collection<FXRateBasis> getCustomIncludedFXRateBasisReferences( CurrencyPairGroup cpg, Organization fiOrg, Organization lpOrg )
    {
        OrganizationRelationship orgRel = lpOrg.getOrgRelationship ( fiOrg, CounterpartyUtilC.LP_FI_RELATIONSHIP );
        if ( orgRel != null )
        {
            TradeChannelSupport lpOrgTcs = lpOrg.getProviderOrgFunction ().getTradeChannelSupport ();
            TradeChannelSupport orgRelTcs = orgRel.getTradeChannelSupport ();
            final boolean isESPOutrightSupported = lpOrgTcs.isESPOutrightSupported () && orgRelTcs.isESPOutrightSupported ();
            final boolean isESPSwapSupported = lpOrgTcs.isESPSwapSupported () && orgRelTcs.isESPSwapSupported ();
            return getCustomIncludedFXRateBasisReferences ( cpg, isESPOutrightSupported, isESPSwapSupported );
        }
        else
        {
            log.info ( "CU.getCustomIncludedFXRateBasisReferences : no lp-fi relationship between lpOrg=" + lpOrg + ",fiOrg=" + fiOrg + ",cpg=" + cpg );
        }
        return Collections.emptyList();
    }

    public static Collection<FXRateBasis> getFXRateBases( FXRateConvention conv, Organization org )
    {
        if ( org == null || org.getProviderOrgFunction () == null )
        {
            log.info ( "CU.getFXRateBases : either org is null or org does not have provider org function. conv=" + conv + ",org=" + org );
            return getFXRateBases ( conv, true, true );
        }
        return getFXRateBases ( conv, org.getProviderOrgFunction ().getTradeChannelSupport () );
    }

    public static Collection<FXRateBasis> getFXRateBases( FXRateConvention conv, TradeChannelSupport tcs )
    {
        return getFXRateBases( conv, tcs.isESPOutrightSupported (), tcs.isESPSwapSupported () );
    }

    public static Collection<FXRateBasis> getFXRateBases( FXRateConvention conv, boolean isESPOutrightSupported, boolean isESPSwapSupported )
    {
        Collection<FXRateBasis> fxRateBases = new ArrayList<FXRateBasis> (  );
        for ( FXRateBasis fxRateBasis : conv.getFXRateBasis () )
        {
            if ( ( fxRateBasis.isForwardSettlementType () && !isESPOutrightSupported ) || ( fxRateBasis.isSwapSettlementType () && !isESPSwapSupported ) )
            {
                continue;
            }
            fxRateBases.add( fxRateBasis );
        }
        return fxRateBases;
    }


    public static Collection<CurrencyPair> getEffectiveCurrencyPairs( CurrencyPairGroup cpg, Organization fiOrg, Organization lpOrg )
    {
        OrganizationRelationship orgRel = lpOrg.getOrgRelationship ( fiOrg, CounterpartyUtilC.LP_FI_RELATIONSHIP );
        if ( orgRel != null )
        {
            TradeChannelSupport lpOrgTcs = lpOrg.getProviderOrgFunction ().getTradeChannelSupport ();
            TradeChannelSupport orgRelTcs = orgRel.getTradeChannelSupport ();
            final boolean isESPOutrightSupported = lpOrgTcs.isESPOutrightSupported () && orgRelTcs.isESPOutrightSupported ();
            final boolean isESPSwapSupported = lpOrgTcs.isESPSwapSupported () && orgRelTcs.isESPSwapSupported ();
            return getEffectiveCurrencyPairs ( cpg, isESPOutrightSupported, isESPSwapSupported );
        }
        else
        {
            log.info ( "CU.getEffectiveCurrencyPairs : no lp-fi relationship between lpOrg=" + lpOrg + ",fiOrg=" + fiOrg + ",cpg=" + cpg );
        }
        return Collections.emptyList();
    }

    public static Collection<CurrencyPair> getEffectiveCurrencyPairs( CurrencyPairGroup cpg, boolean isESPOutrightSupported, boolean isESPSwapSupported )
    {
        Collection<CurrencyPair> grpCcyPairList = cpg.getCurrencyPairs ();
        if ( grpCcyPairList != null )
        {
            Collection<CurrencyPair> currencyPairs = new ArrayList<CurrencyPair>();
            for ( CurrencyPair cp: grpCcyPairList )
            {
                if ( ( cp.isForwardSettlementType () && !isESPOutrightSupported ) || ( cp.isSwapSettlementType () && !isESPSwapSupported ) )
                {
                    continue;
                }
                currencyPairs.add ( cp );
            }
            return currencyPairs;
        }
        return Collections.emptyList();
    }

    public static Collection<CurrencyPair> getEffectiveCurrencyPairs( CurrencyPairGroup cpg, Organization org )
    {
        return getEffectiveCurrencyPairs ( cpg, org.getProviderOrgFunction ().getTradeChannelSupport () );
    }

    public static Collection<CurrencyPair> getEffectiveCurrencyPairs( CurrencyPairGroup cpg, TradeChannelSupport tcs )
    {
        return getEffectiveCurrencyPairs( cpg, tcs.isESPOutrightSupported (), tcs.isESPSwapSupported () );
    }

	public static Collection<Currency> getSupportedSpotCurrencies( FXRateConvention conv )
    {
        Collection<Currency> ccySet = new TreeSet<Currency> ( new CurrencyComparatorC () );
        for ( FXRateBasis fxRateBasis : conv.getEffectiveFXRateBasis () )
        {
            Currency base = fxRateBasis.getBaseCurrency ();
            if ( base.isSpotSettlementType () )
            {
                ccySet.add ( base );
            }

            Currency var = fxRateBasis.getVariableCurrency ();
            if ( var.isSpotSettlementType () )
            {
                ccySet.add ( var );
            }
        }
        return ccySet;
    }
}
