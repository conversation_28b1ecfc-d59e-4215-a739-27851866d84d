package com.integral.security;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.Vector;

import com.integral.audit.*;

import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import com.integral.exception.IdcOptimisticLockException;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.spaces.fx.persistence.ISSpacesPersistenceService;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.spaces.ApplicationSpaceEvent;
import com.integral.spaces.concurrent.Future;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.time.DateTimeFactory;
import com.integral.user.User;
import com.integral.user.UserAuthenticationException;
import com.integral.user.UserC;
import com.integral.user.UserFactory;
import com.integral.user.UserGroup;
import com.integral.user.UserSession;
import com.integral.user.UserSessionQueryService;
import com.integral.util.Tuple;

public class SecurityServiceC implements SecurityService
{
    /**
     * The Global groupName key which has all the users of the system
     */
    private final static String groupEveryOne = "IntegralEveryOneGroup";

    /**
     * The number of hours which is used for user inactivation after successive invalid login attempts (by default = 24)
     */
    private final static String failedSinceHours = "FailedSinceHours";

    private final static String failedPasswordChageAttempts_key = "FailedPasswordChageAttempts";

    private final static Log log = LogFactory.getLog( SecurityServiceC.class );
    
    private final static Log metricsLog = LogFactory.getLog( "com.integral.metrics.SessionMetrics" );

    private final static SecurityServiceC instance = new SecurityServiceC();
    private static final String ADMIN_USER = "ADMIN_USER";
    private static final String SUSPENDED = "SUSPENDED";
    private static final String USERLOGINFAILED = "USERLOGINFAILED";
    private static final String EXTERNAL_AUTH = "EXTERNAL_AUTH";
    private static final String EXTERNAL_SSO_USER_LOGIN_FAILED = "EXTERNAL_SSO_LOGIN_FAILED";
    private static final String EXTERNAL_SSO_USER_LOGIN_SUCCESS = "EXTERNA_SSO_LOGIN_SUCCESS";
    private static final String USERLOGIN = "USERLOGIN";
    private static final String USERLASTLOGIN = "USERLASTLOGIN";
    private static final String PASSED = "PASSED";
    private static final String FAILED = "FAILED";
    private static final String LOGOUT  = "LOGOUT";
    private static final String LOGOUT_SESSIONTIMEOUT = "LOGOUT_SESSIONTIMEOUT";

    public static final String USER_DISABLED_ERROR = "user.account.disabled";
    public static final String PASSWORD_FAILED_ATTEMPTS_EXCEEDED_ERROR = "user.password.maxFailedAttempts.exceedsLimit";
    private static final String PASSWORDFORGOTREQUEST = "PASSWORD_FORGOT_REQUEST";
    private static final String PASSWORDFORGOTREQUEST_SUCCESS = "PASSWORD_FORGOT_REQUEST_SUCCESS";
    private static final String PASSWORDFORGOTREQUEST_FAILED = "PASSWORD_FORGOT_REQUEST_FAILED";
    private static final String PASSWORDRESETREQUEST = "PASSWORD_RESET_REQUEST";
    private static final String PASSWORDRESETREQUEST_SUCCESS = "PASSWORD_RESET_REQUEST_SUCCESS";
    private static final String PASSWORDRESETREQUEST_FAILED = "PASSWORD_RESET_REQUEST_FAILED";
    public static final String PASSWORD_EXPIRED =  "Password expired";
    

    /**
     * Construtor
     */
    private SecurityServiceC()
    {
    }

    public static SecurityServiceC getInstance(){
        return instance;
    }

    /**
     * The method returns a User object for the given <UserName>@<Namespace> combination
     * Using the namespace, get the namespaceiId/ list of namespaceId if the namespace is a namespaceGroup
     * Using the shortName and the namespace combination identify the user in the IDCUser table (User status must not be D)
     *
     * @param name
     * @return com.integral.user.User
     */
    private User _getUser( String name )
    {
        return _getUser( name, false );
    }

    /**
     * The method returns a User object for the given <UserName>@<Namespace> combination
     * Using the namespace, get the namespaceiId/ list of namespaceId if the namespace is a namespaceGroup
     * Using the shortName and the namespace combination identify the user in the IDCUser table (User status must not be D)
     *
     * @param name
     * @param shdRefresh
     * @return com.integral.user.User
     */
    private User _getUser( String name, boolean shdRefresh )
    {
        return UserFactory.getUser( name, shdRefresh );
    }

    /**
     * This method is a substitute method for the _getUser(user) which returns com.integral.user.User
     *
     * @param name
     * @return IntegralUser // NOTE: convert IntegralUser to com.integral.user.User
     */
    public IntegralUser getUser( String name )
    {
        User user = _getUser( name );
        if ( user != null )
        {
            return new IntegralUser( name, user.getObjectID(), null );
        }
        return null;
    }

    /**
     * Authenticate the User (The suspended status and the password validation is taken care here).
     *
     * @param name
     * @param password
     * @return IntegralUser // NOTE: convert IntegralUser to com.integral.user.User
     */
    public IntegralUser authenticateUser( String name, String password, boolean isSSOUser )
    {
        IntegralUser integralUser = null;
        User user = null;

        user = _getUser( name );
        if ( user != null )
        {
            //check for org if declared for external authentication
            try
            {
                integralUser = _authenticateUser( user, name, password, isSSOUser );
            }
            catch ( IdcOptimisticLockException iole )
            {
                if ( log.isDebugEnabled() )
                {
                    log.debug( "SecurityServiceC: IdcOptimisticLockException occured. Retrying" );
                }
                integralUser = _authenticateUser( _getUser( name, true ), name, password, isSSOUser );
            }
        }
        return integralUser;
    }

    protected IntegralUser _authenticateUser( User user, String name, String password, boolean isSSOUser )
    {
        return _authenticateUser( user, name, password, true, isSSOUser );
    }

    private IntegralUser _authenticateUser( User user, String name, String password, boolean retryOnFailure, boolean isSSOUser )
    {
        IntegralUser integralUser = null;
        try
        {
            if ( user != null )
            {
                //if user logged in via integral SSO then get the password from DB and use DB password 
                if ( isSSOUser )
                {
                    String digest = ( ( UserC ) user ).getDigest();
                    if ( digest != null && digest.length() > 0 )
                    {
                        String uPass = CryptC.decrypt( digest );
                        password = CryptC.md5encrypt( uPass );
                    }
                }
                else
                {
                    user.verifyMD5Password( password );
                }
                integralUser = new IntegralUser( name, user.getObjectID(), password );
                if ( log.isDebugEnabled() )
                {
                    log.debug( "SecurityServiceC: password successfully verified for user " + user.getFullName() );
                }
            }
            else
            {
                if ( log.isDebugEnabled() )
                {
                    log.debug( "SecurityServiceC: user with name " + name + " cannot be found or is suspended." );
                }
            }
        }
        catch ( UserAuthenticationException e )
        {
//        	GMLogger.sendLoginFailedAlert( user, "Authentication Failed");
            if ( log.isDebugEnabled() )
            {
                log.warn( "SecurityServiceC: authentication failure when verifying password for user " + user.getFullName(), e );
            }
            else
            {
                log.warn( "SecurityServiceC: authentication failure when verifying password for user " + user.getFullName() );
            }
            // Now check if the user needs to be suspended.and
            handlerAuthenticatioFailed(user);
            user = null;
        }
        catch ( IdcOptimisticLockException ole )
        {
            if ( retryOnFailure )
            {
                User user2 = user;
                try
                {
                    user2 = ( User ) PersistenceFactory.newSession().refreshObject( user );
                }
                catch ( Exception e )
                {
                    //
                }
                integralUser = _authenticateUser( user2, name, password, false );
            }
            else
            {
                if ( log.isDebugEnabled() )
                {
                    log.debug( "SecurityServiceC: optimistic lock exception when authenticating user " + name );
                }
            }
        }
        return integralUser;
    }

    /**
     * Audit the Login Failed event in the IdcAudit table
     *
     * @param user
     */
    public void auditLoginFailed( String user )
    {
        if ( user != null )
        {
            User usr = _getUser( user );
            if ( usr != null )
            {
                auditLoginFailed( usr, true );
            }
        }
    }

    /**
     * Audit the Login Success event in the IdcAudit table
     *
     * @param user
     */
    public void auditLoginSuccess( String user )
    {
        if ( user != null )
        {
            User usr = _getUser( user );
            if ( usr != null )
            {
                auditLoginSuccess( usr, true );
            }
        }
    }


    /**
     * @return int - maximumAllowedFailedAttempts
     */
    protected int getMaxAllowedFailedAttempts()
    {
        /*  Integer obj = (Integer)getContextVariable(maximumAllowedFailedAttempts);
      if( obj != null){
          return obj.intValue();
      }
      return 0; */
        return UserFactory.getUserMBean().getMaxUserLoginPasswordFailedAttempts();
    }

    /**
     * @return int - failedSince in the last given (configured hours)
     */
    protected int getFailedSinceHours()
    {
        return 24;
    }

    /**
     * Get the group for the given group name (by default get the group memebers)
     *
     * @param name
     * @return IntegralGroup
     */
    public IntegralGroup getGroup( String name )
    {
        return getGroup( name, true );
    }

    /**
     * Get the group for the given group name.
     * If the given name is of the global group, skip the DB call and form the group.
     *
     * @param name
     * @param withMembers
     * @return IntegralGroup  // NOTE: convert IntegralGroup to ??
     */
    public IntegralGroup getGroup( String name, boolean withMembers )
    {
        IntegralGroup group = null;
        Hashtable members = null;

        //Check if the group requested is IntegralEveryOneGroup
        String everyOneGroup = "IntegralEveryone";
        if ( name.equalsIgnoreCase( everyOneGroup ) )
        {
            if ( log.isDebugEnabled() )
            {
                log.debug( "IntegralDBStore(getGroup): ... Group object " + everyOneGroup + " created." );
            }
            if ( withMembers )
            {
                members = getGroupMembers( name );
            }

            group = new IntegralGroup( name, members );
        }
        else
        {
            //DONE: write TOPLINK code to extract group from DB
            UserGroup userGroup = null;
            try
            {
                Session dbSession = PersistenceFactory.newSession();
                ExpressionBuilder eb = new ExpressionBuilder();
                Expression groupExp = eb.get( "shortName" ).equal( name );
                userGroup = ( UserGroup ) dbSession.readObject( com.integral.user.UserGroup.class, groupExp );
            }
            catch ( PersistenceException pe )
            {
                if ( log.isDebugEnabled() )
                {
                    log.debug( "SecurityServiceC: Exception occured when getting a new persistence session.couldnot get given group" );
                }
            }
            catch ( Exception e )
            {
                log.warn( "SecurityServiceC: Exception when getting a group", e );
            }
            if ( log.isDebugEnabled() )
            {
                log.debug( "SecurityServiceC: Group in DB: " + userGroup );
            }

            if ( userGroup != null )
            {
                if ( withMembers )
                {
                    members = convertUsersToIntegralUsers( userGroup.getUsers() );
                }
                group = new IntegralGroup( userGroup.getName(), members );
            }
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( "IntegralDBStore(getGroup): ... Returning Group object for " + group );
        }
        return group;
    }

    /**
     * For OC4J
     * Get all the groups for the given user object id.
     *
     * @param objectId long
     * @return Collection  // NOTE: collection of IntegralGroups
     */
    public Collection getGroups( long objectId )
    {
        Collection groups = new ArrayList();
        User user = null;
        try
        {
            Session dbSession = PersistenceFactory.newSession();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression userExp = eb.get( "objectID" ).equal( objectId );
            user = ( User ) dbSession.readObject( com.integral.user.User.class, userExp );
            if ( user != null )
            {
                Collection userGroups = user.getUserGroups();
                Iterator userGroupsIter = userGroups.iterator();
                while ( userGroupsIter.hasNext() )
                {
                    UserGroup userGroup = ( UserGroup ) userGroupsIter.next();
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "SecurityServiceC: Group in DB: " + userGroup );
                    }
                    groups.add( new IntegralGroup( userGroup.getName(), convertUsersToIntegralUsers( userGroup.getUsers() ) ) );
                }
            }
        }
        catch ( PersistenceException pe )
        {
            if ( log.isDebugEnabled() )
            {
                log.debug( "SecurityServiceC: Exception occured when getting a new persistence session.couldnot get groups for the user" );
            }
        }
        catch ( Exception e )
        {
            log.warn( "SecurityServiceC: Exception when getting a group for the user", e );
        }
        return groups;
    }

    /**
     * A substitue which needs to be removed once the IntegralGroup is converted to UserGroup
     *
     * @param groupMemebers
     * @return Hashtable of IntegralUser
     */
    protected Hashtable convertUsersToIntegralUsers( Collection groupMemebers )
    {
        Hashtable members = new Hashtable();
        if ( groupMemebers == null )
        {
            return members;
        }

        Iterator iter = groupMemebers.iterator();
        while ( iter.hasNext() )
        {
            User user = ( User ) iter.next();
            IntegralUser integralUser = new IntegralUser( user.getName(), user.getObjectID(), null );
            if ( log.isDebugEnabled() )
            {
                log.debug( "SecurityServiceC: Adding User " + integralUser.getUserName() );
            }
            members.put( integralUser.getUserName(), integralUser );
        }
        return members;
    }

    /**
     * @return Enumeration of Groups
     */
    public Enumeration getGroups()
    {
        Vector groups = null;
        Vector integralGroups = new Vector();
        try
        {
            Session dbSession = PersistenceFactory.newSession();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression statusExpr = eb.get( "status" ).equal( UserGroup.ACTIVE_STATUS );
            groups = dbSession.readAllObjects( com.integral.user.UserGroup.class, statusExpr );
            for ( int i = 0; i < groups.size(); i++ )
            {
                UserGroup userGroup = ( UserGroup ) groups.elementAt( i );
                integralGroups.add( new IntegralGroup( userGroup.getName(), null ) );
            }
        }
        catch ( PersistenceException pe )
        {
            if ( log.isDebugEnabled() )
            {
                log.debug( "SecurityServiceC: Exception occured when getting a new persistence session. couldnot get groups" );
            }
        }
        catch ( Exception e )
        {
            log.warn( "SecurityServiceC: Exception when getting all groups", e );
        }
        return integralGroups.elements();
    }

    /**
     * @return Enumeration of Users
     */
    public Enumeration getUsers()
    {
        Vector users = null;
        Vector integralUsers = new Vector();
        try
        {
            Session dbSession = PersistenceFactory.newSession();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression statusExpr = eb.get( "status" ).equal( User.ACTIVE_STATUS );
            users = dbSession.readAllObjects( com.integral.user.User.class, statusExpr );
            for ( int i = 0; i < users.size(); i++ )
            {
                User user = ( User ) users.elementAt( i );
                integralUsers.add( new IntegralUser( user.getName() + '@' + user.getOrganization().getNamespace().getName(), user.getObjectID(), null ) );
            }
        }
        catch ( PersistenceException pe )
        {
            if ( log.isDebugEnabled() )
            {
                log.debug( "SecurityServiceC: Exception occured when getting a new persistence session" );
            }
        }
        catch ( Exception e )
        {
            log.warn( "SecurityServiceC: Exception when getting all users", e );
        }
        return integralUsers.elements();
    }

    /**
     * Get the memebers for the given groupName
     *
     * @param groupName
     * @return Hashtable of GroupMembers for the given group
     */
    public Hashtable getGroupMembers( String groupName )
    {
        Hashtable members = new Hashtable();

        if ( groupName != null )
        {
            return members;
        }

        UserGroup userGroup = null;
        if ( groupName.equals( groupEveryOne ) )
        {     // Get all the users
            Enumeration userEnum = getUsers();
            while ( userEnum.hasMoreElements() )
            {
                IntegralUser integralUser = ( IntegralUser ) userEnum.nextElement();
                members.put( integralUser.getUserName(), integralUser );
            }
        }
        else
        {
            try
            {
                Session dbSession = PersistenceFactory.newSession();
                ExpressionBuilder eb = new ExpressionBuilder();
                Expression groupExp = eb.get( "shortName" ).equal( groupName );
                userGroup = ( UserGroup ) dbSession.readObject( com.integral.user.UserGroup.class, groupExp );
                if ( userGroup != null )
                {
                    members = convertUsersToIntegralUsers( userGroup.getUsers() );
                }
            }
            catch ( PersistenceException pe )
            {
                if ( log.isDebugEnabled() )
                {
                    log.debug( "SecurityServiceC: Exception occured when getting a new persistence session. couldnot get groupmembers" );
                }
            }
            catch ( Exception e )
            {
                log.warn( "SecurityServiceC: Exception when getting a user", e );
            }
        }
        return members;
    }

    private void updateUserFailedLogin( User user )
    {
        // If the user is already suspended
        if ( user.getAccountStatus() != User.ACCOUNT_STATUS_ACCOUNT_DISABLED )
        {
            // update last failed login time
            Calendar yesterdayCal = Calendar.getInstance( TimeZone.getTimeZone( "GMT" ) );
            yesterdayCal.add( Calendar.HOUR, -getFailedSinceHours() );
            if ( yesterdayCal.after( getLastFailedLoginDate(user) ) )
            {
                user.resetFailedLoginAttempts();
            }
            user.setLastFailedLoginDate( Calendar.getInstance( TimeZone.getTimeZone( "GMT" ) ).getTime() );
            user.incFailedLoginAttempts();
        }
    }


    public boolean isExternalAuthEnabled( String user )
    {
        User usr = UserFactory.getUser( user, false );
        if ( usr == null )
        {
            return false;
        }
        else
        {
            Boolean isExternalAuthenticate = ( Boolean ) usr.getOrganization().getCustomFieldValue( "DirectFX_IsExternalAuthenticate" );
            return ( isExternalAuthenticate != null && isExternalAuthenticate );
        }
    }

    public void suspendUser( User user )
    {
    	suspendUser(user,true);
    }
    
    private void suspendUser( User user, boolean retryOnFailure )
    {
    	suspendUser(user, retryOnFailure, true);
    }
  
    
    private void suspendUser( User user, boolean retryOnFailure, boolean updateUserSession)
    {
        if ( PersistenceFactory.getPersistenceMBean().isPersistenceReadOnlyMode() )
        {
            if ( log.isDebugEnabled() )
            {
                log.debug( "SecurityServiceC: skip saving suspention to DB for user " + user.getFullName() + " since app is in read-only mode" );
            }
        }
        else
        {
            try
            {
                // starts a transaction
                UnitOfWork uow = PersistenceFactory.newSession().acquireUnitOfWork();
                uow.removeReadOnlyClass( UserC.class );
                User registeredUser = ( User ) uow.registerObject( user );
                registeredUser.setAccountStatus( User.ACCOUNT_STATUS_ACCOUNT_DISABLED );
                registeredUser.resetFailedLoginAttempts();
                registeredUser.putCustomField( failedPasswordChageAttempts_key, null );
                uow.commit();
                auditEvent(user, SUSPENDED, SUSPENDED, retryOnFailure, true, updateUserSession);
            }
            catch ( IdcOptimisticLockException ole )
            {
                if ( retryOnFailure )
                {
                    User user2 = user;
                    try
                    {
                        user2 = ( User ) PersistenceFactory.newSession().refreshObject( user );
                    }
                    catch ( Exception e )
                    {
                        //
                    }
                    auditEvent( user2, SUSPENDED, SUSPENDED, false , true, updateUserSession );
                }
                else
                {
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "SecurityServiceC: optimistic lock exception when suspending user " + user.getFullName() );
                    }
                }
            }
            catch ( PersistenceException e )
            {
                log.error( "SecurityServiceC: persistence exception when suspending user " + user.getFullName() );
            }
        }
    }

    public void auditLoginFailed( User user, boolean retryOnFailure )
    {
        auditEvent( user, USERLOGINFAILED, USERLOGINFAILED, retryOnFailure, true,true );
    }
    
    @Override
    public void auditLoginFailed(User user,  boolean retryOnFailure, boolean updateUserSession) 
    {
    	auditEvent( user, USERLOGINFAILED, USERLOGINFAILED, retryOnFailure, true , updateUserSession);
    }
	
    public void auditExternalLoginFailed( User user )
    {
        auditEvent( user, EXTERNAL_AUTH, EXTERNAL_SSO_USER_LOGIN_FAILED, false, true , true);
    }
    
    public void handleLoginFailed( User user, boolean auditEvent )
    {
        auditEvent( user, USERLOGINFAILED, USERLOGINFAILED, true , false , true);
    }

    public void auditLoginSuccess( User user, boolean retryOnFailure )
    {
        auditEvent( user, USERLOGIN, USERLOGIN, retryOnFailure , true , true);
    }
    
    @Override
	public void auditLoginSuccess(User user, boolean retryOnFailure,  boolean updateUserSession)
    {
		auditEvent( user, USERLOGIN, USERLOGIN, retryOnFailure , true , updateUserSession);
	}
    
    public void auditExternalLoginSuccess( User user )
    {
        auditEvent( user, EXTERNAL_AUTH, EXTERNAL_SSO_USER_LOGIN_SUCCESS, false, true, true );
    }

    public void auditLastLogin(User user, boolean retryOnFailure)
    {
        auditEvent(user, USERLASTLOGIN, USERLASTLOGIN, retryOnFailure , true, true);
    }

    public void handleLogout( User user, boolean auditEvent )
    {
        auditEvent( user, LOGOUT, LOGOUT, true , auditEvent , true);
    }

    public void auditLogout1( User user, Boolean isSessionTimeout )
    {
        if ( isSessionTimeout == null || !isSessionTimeout )
        {
            instance.auditEvent( user, LOGOUT, LOGOUT, true , true, true );
        }
        else
        {
            instance.auditEvent( user, LOGOUT_SESSIONTIMEOUT, LOGOUT_SESSIONTIMEOUT, true , true , true);
        }
    }
    

    private void auditEvent( User user, String eventAction, String eventType, boolean retryOnFailure, boolean auditEvent, boolean updateUserSession)
    {
    	StringBuilder buffer = new StringBuilder(100);
    	buffer.append("SS u=").append(user.getShortName());
    	buffer.append(",e=").append(eventType);
    	//boolean isSpacesPersistenceEnabled = UserFactory.getUserMBean().isSpacesPersistenceEnabled();
        try
        {
            if ( PersistenceFactory.getPersistenceMBean().isPersistenceReadOnlyMode() )
            {
                log.warn( "SecurityServiceC: skip saving login failure to DB for user " + user.getFullName() + " since app is in read-only mode" + ", Action : " + eventAction + ", EventType : " + eventType );
            }
            else
            {
            	long t0 = System.nanoTime();
                // starts a transaction
               
                if ( auditEvent )
				{
					// create the audit event
					AuditEvent event = AuditFactory.newAuditEvent(ADMIN_USER, eventAction);
//					event = (AuditEvent) uow.registerObject(event);
					event.setType(eventType);
					event.setUser(user);
					event.setOrganization(user.getOrganization());
					event.setNamespace(user.getOrganization().getNamespace());
					event.setEntity1(user);
					event.setEntity2(user.getOrganization());
					event.setStringArg1(user.getShortName());
					event.setStringArg2(user.getOrganization().getShortName());
					event.setStringArg3(user.getShortName());
					event.setStringArg4(user.getOrganization().getShortName());

                    /*
                       Setting audit metadata fields
                    */
                    ((AuditEventC)event).setModifiedByUser(user.getShortName());
                    ((AuditEventC)event).setModifiedByNamespace(user.getNamespace().getShortName());
                    ((AuditEventC)event).setModifiedEntity(user.getShortName());
                    ((AuditEventC)event).setModifiedNamespace(user.getNamespace().getShortName());
                    /**
					 * Important - Set the session context.
					 * Oracle Audit Service uses SessionContext to 
					 * build the Toplink related queries
					 */
					IdcSessionContext sessContext = IdcSessionManager.getInstance().getSessionContext(user);
					IdcSessionManager.getInstance().setSessionContext( sessContext);
                    AuditInfo auditInfo = (AuditInfo) sessContext.getAttribute("audit-info");
                    if(auditInfo != null)
                    {
                    ((AuditEventC)event).setSourceIP(auditInfo.getIpAddress());
                    ((AuditEventC)event).setSourceVirtualServer(auditInfo.getSourceVirtualServer());
                    ((AuditEventC)event).setSourceMachineName(auditInfo.getSourceVirtualServerMachineName());
                    }
                    AuditManager.audit("ADMIN_USER",event);
				}
                
				if ( USERLOGIN.equals( eventType ) || EXTERNAL_SSO_USER_LOGIN_SUCCESS.equals(eventType) )
                {
                    // update last login time
                    user.setLastLogin( DateTimeFactory.newDateTime( TimeZone.getTimeZone( "GMT" ) ) );
                    // reset the failed attempts
                    user.resetFailedLoginAttempts();
                }
                else if (USERLASTLOGIN.equals(eventType)) {
                	user.setLastLogin( DateTimeFactory.newDateTime( TimeZone.getTimeZone( "GMT" ) ) );
                }
                else if ( USERLOGINFAILED.equals( eventType )  ||EXTERNAL_SSO_USER_LOGIN_FAILED.equals(eventType) )
                {
                    updateUserFailedLogin( user );
                }
                else if( SUSPENDED.equals( eventType ) )
                {
                	
                }
                else if ( LOGOUT.equals( eventType ) || LOGOUT_SESSIONTIMEOUT.equals(eventType) ) 
                {
                	user.setLastLogoutDate(new Date());
                }
                
				long t1 = System.nanoTime();
				buffer.append(",pre=").append(t1-t0);
				
                if( updateUserSession)
                {
                	updateUserSession( 	user, eventType, buffer,
                						user.getFailedLoginAttempts(),
                						user.getLastFailedLoginDate() ,
                						user.getLastLoginDate(),
                						user.getLastLogoutDate(),
                						user.getCreatedDate());
                }
                
                long t2 = System.nanoTime();
                long t3 = System.nanoTime();
                buffer.append(",uow=").append(t3-t2);
            	buffer.append(",timeTaken=").append(t3-t0);

            }
        }
/*        catch ( IdcOptimisticLockException ole )
        {
            if ( retryOnFailure )
            {
                User user2 = user;
                try
                {
                    user2 = ( User ) PersistenceFactory.newSession().refreshObject( user );
                }
                catch ( Exception e )
                {
                    //
                }
                auditEvent( user2, eventAction, eventType, false, auditEvent );
            }
            else
            {
                log.warn( "SecurityServiceC: optimistic lock exception when recording audit for user " + user.getFullName() + ", Action : " + eventAction + ", EventType : " + eventType );
            }
        }
        catch ( PersistenceException pe )
        {
            log.debug( "SecurityServiceC: persistence exception when recording audit for user " + user.getFullName() + ", Action : " + eventAction + ", EventType : " + eventType );
        }
*/        
        finally
        {
        	metricsLog.info(buffer.toString());
        }
    }
    
    
    private void updateUserSession(User user, String event, StringBuilder buffer , int numFailedLoginAttempts, Date lastFailedLoginDate,Date lastLoginDate , Date lastLogoutDate,Date createdDate)
    {
    	long t0 = System.nanoTime(), t1=0, t2=0, t3=0 ;
    	try
    	{
    		synchronized ( user )
    		{
    			t1 = System.nanoTime();
    			ApplicationEventCodes apCode = ApplicationEventCodes.EVENT_USER_SESSION_UPDATE;
    			UserSession userSession = UserSessionQueryService.refreshCache(user, true);
    			t2 = System.nanoTime();
    			if ( userSession == null )
    			{
    				userSession = new UserSession();
    				apCode = ApplicationEventCodes.EVENT_USER_SESSION_CREATE;
    				userSession.setCreatedTime(createdDate.getTime());
    				userSession.set_id(String.valueOf(user.getObjectId()));
    				userSession.setNamespaceName(user.getNamespace().getShortName());
    				userSession.setUserName(user.getShortName());

    			}
    			if ( USERLOGIN.equals(event) || EXTERNAL_SSO_USER_LOGIN_SUCCESS.equals( event ) )
    			{
    				userSession.setLastLoginTime(lastLoginDate.getTime());
    				userSession.setNumberFailedLoginAttempts(numFailedLoginAttempts);
    			}
    			else if (USERLASTLOGIN.equals(event)) {
    				userSession.setLastLoginTime(user.getLastLoginDate().getTime());
    			}
    			else if ( USERLOGINFAILED.equals(event) )
    			{
    				userSession.setNumberFailedLoginAttempts(numFailedLoginAttempts);
    				if ( lastFailedLoginDate != null )
    				{
    					userSession.setLastFailedLoginTime(lastFailedLoginDate.getTime());
    				}
    			}
    			else if ( LOGOUT.equals(event) || LOGOUT_SESSIONTIMEOUT.equals(event) )
    			{
    				if ( user.getLastLogoutDate() != null )
    				{
    					userSession.setLastLogoutTime(lastLogoutDate.getTime());
    				}
    			}
    			else if ( PASSWORDFORGOTREQUEST_SUCCESS.equals(event)) {
    				Long lpfrt = user.getLastPasswordForgotRequestTime();
    				if (lpfrt != null) {
    					List<Long> lpfrts = user.getPasswordForgotRequestTimes();
    					if (lpfrts != null) {
    						lpfrts.add(lpfrt);
    						String lpfrtsStr = getStringValue(lpfrts);
    						userSession.setPasswordForgotRequestTimes(lpfrtsStr);
    					} else {
    						//userSession.addPasswordForgotRequestTime(lpfrt);
    						userSession.setPasswordForgotRequestTimes("" + lpfrt);
    					}
    				}
    			} 
    			
    			else if (PASSWORDFORGOTREQUEST_FAILED.equals(event)) {
    				List<Long> lpfrts = user.getPasswordForgotRequestTimes();
    				String lpfrtsStr = getStringValue(lpfrts);
    				if (lpfrtsStr != null) {
    					userSession.setPasswordForgotRequestTimes(lpfrtsStr);
    				}
    			}
    			
    			else if ( PASSWORDRESETREQUEST_SUCCESS.equals(event)) {
    				Long lprrt = user.getLastPasswordResetRequestTime();
    				if (lprrt != null) {
    					List<Long> lprrts = user.getPasswordResetRequestTimes();
    					if (lprrts != null) {
    						lprrts.add(lprrt);
    						String lprrtsStr = getStringValue(lprrts);
    						userSession.setPasswordResetRequestTimes(lprrtsStr);
    					} else {
    						//userSession.addPasswordResetRequestTime(lprrt);
    						userSession.setPasswordResetRequestTimes("" + lprrt);
    					}
    				}
    			}
    			else if (PASSWORDRESETREQUEST_FAILED.equals(event)) {
    				List<Long> lprrts = user.getPasswordResetRequestTimes();
    				String lprrtsStr = getStringValue(lprrts);
    				if (lprrtsStr != null) {
    					userSession.setPasswordResetRequestTimes(lprrtsStr);
    				}
    				
    			}
    			userSession.setVirtualServer(ConfigurationFactory.getServerMBean().getVirtualServerName());
    			userSession.setModifiedTime(System.currentTimeMillis());
    			ISSpacesPersistenceService usps = PersistenceServiceFactory.getUserSessionPersistenceService();
    			ApplicationSpaceEvent evt = usps.createEvent(userSession, apCode);
    			Tuple<ErrorMessage, Future> response = usps.save(evt, String.valueOf(user.getObjectId()), false, "UserSession");
				if( response != null && response.first != null )
				{
					log.warn("Failed to update user session Info. u="+userSession+",evt="+event +",errorCode="+response.first.getCode());
				}
				buffer.append(",vid=").append(evt.getObjectVersion());
				t3 = System.nanoTime();
    		}
    	}
    	catch ( Exception e )
    	{
    		log.error("SS.updateUserSession. Failed to update user session. u="+user.getFullName()+",e="+event, e);
    	}
    	finally
    	{
    		buffer.append(",mw=").append(t3-t2);
    		buffer.append(",mq=").append(t2-t1);
    		buffer.append(",wait=").append(t1-t0);
    	}
    }
    
    
    private String getStringValue(final List<Long> eventTimes) {
    	if (eventTimes != null && eventTimes.size() > 0) {
			StringBuilder sb = new StringBuilder();
			int counter = 1;
			int size = eventTimes.size();
			for (Long eventTime : eventTimes) {
				if (eventTime != null) {
					sb.append(eventTime);
					if (counter < size) {
						sb.append(",");
					}
				}
				counter ++;
			}
			return sb.toString();
    	}
    	return null;
    }
    
    
    public static Date getLastLoginDate(User user)
    {
		try
		{
			UserSession userSession = UserSessionQueryService.getUserSession(user);
			if( userSession != null )
			{
				return new Date(userSession.getLastLoginTime());
			}
		}catch(Exception e)
		{
			log.error("SS.getLastLoginDate. Failed to retrieve lastLoginDate. u="+user.getFullName(), e);
		}
		return null;
    }
    
    
    public static Date getLastFailedLoginDate(User user)
    {
		try
		{
			UserSession userSession = UserSessionQueryService.getUserSession(user);
			if( userSession != null )
			{
				return new Date(userSession.getLastFailedLoginTime());
			}
		}catch(Exception e)
		{
			log.error("SS.getLastFailedLoginDate. Failed to retrieve lastFailedLoginDate. u="+user.getFullName(), e);
		}
		return null;
    }
    
    public static Date getLastLogoutDate(User user)
    {
		try
		{
			UserSession userSession = UserSessionQueryService.getUserSession(user);
			if( userSession != null )
			{
				return new Date(userSession.getLastLogoutTime());
			}
		}
		catch(Exception e)
		{
			log.error("SS.getLastLogoutDate. Failed to retrieve lastLogoutDate. u="+user.getFullName(), e);
		}
    	return null;	
    }
    
    public static int getFailedLoginAttempts(User user)
    {
        return getFailedLoginAttempts( user, true );
    }

    public static int getFailedLoginAttempts(User user, boolean useUserSessionCache )
    {
        try
        {
            UserSession userSession = useUserSessionCache ? UserSessionQueryService.getUserSession( user ) : UserSessionQueryService.refreshCache( user );
            if( userSession != null )
            {
                return userSession.getNumberFailedLoginAttempts();
            }
        }
        catch(Exception e)
        {
            log.error("SS.getFailedLoginAttempts. Failed to retrieve NumberFailedLoginAttempts. u="+user.getFullName(), e);
        }
       return 0;
    }
    
    public static int getConsistentFailedLoginAttempts(User user) 
    {
		UserSession userSession = null;
		try 
		{
			boolean isMultipleCASServerEnabled = UserFactory.getUserMBean().isMultipleCASServersEnabled();
			userSession = isMultipleCASServerEnabled ? UserSessionQueryService.refreshCache(user, true) :  UserSessionQueryService.getUserSession( user );
			if( userSession != null ) 
			{
				return userSession.getNumberFailedLoginAttempts();
			}
		}
		catch(Exception e) 
		{
			log.error("SS.getFailedLoginAttempts. Failed to retrieve NumberFailedLoginAttempts. u="+user.getFullName(), e);
		}
		return 0;
    }


    public void handlerAuthenticatioFailed(User user)
    {
    	// Now check if the user needs to be suspended based on user status and max failed attempt
        boolean isMaxFailureLoginAttemptsExempt = UserFactory.getUserMBean().isMaxFailureLoginAttemptsExempt( user );
        int failedLoginAttemptsCount = getConsistentFailedLoginAttempts(user);
        if ( !isMaxFailureLoginAttemptsExempt && ( user.getAccountStatus() != User.ACCOUNT_STATUS_ACCOUNT_DISABLED ) &&
                ( getMaxAllowedFailedAttempts() > 0 && failedLoginAttemptsCount >= getMaxAllowedFailedAttempts() ) )
        {
            try
            {
                suspendUser( user,true);
                log.warn( "SSC.handlerAuthenticatioFailed: Disable the account due to maximum failed login attempts: " + failedLoginAttemptsCount+ " for user: " + user );
            }
            catch ( Exception re )
            {
                log.error( "Error while suspending the User : " + user.getShortName(), re );
            }
        }
    }

	@Override
	public void handlePasswordResetRequestSuccess(User user) {
		try {
			if (user == null) {
				return;
			}
			// audit event
			auditEvent(user, PASSWORDRESETREQUEST, PASSWORDRESETREQUEST_SUCCESS, true, true, true);
			log.info("SecurityServiceC:handlePasswordResetRequestSuccess:auditEvent processed successfully" + user);
		} catch (Throwable t) {
			log.error( "Problem with handlePasswordResetRequestFailed : " + user.getShortName(), t);
		}
	}

	@Override
	public void handlePasswordResetRequestFailed(User user) {
		try {
			if (user == null) {
				return;
			}
			// audit event
			auditEvent(user, PASSWORDRESETREQUEST, PASSWORDRESETREQUEST_FAILED, true, true, true);
			log.info("SecurityServiceC:handlePasswordResetRequestFailed:auditEvent processed successfully" + user);
		} catch (Throwable t) {
			log.error( "Problem with handlePasswordResetRequestFailed : " + user.getShortName(), t);
		}
	}

	@Override
	public void handlePasswordForgetRequestSuccess(User user) {
		try {
			if (user == null) {
				return;
			}
			// audit event
			auditEvent(user, PASSWORDFORGOTREQUEST, PASSWORDFORGOTREQUEST_SUCCESS, true, true, true);
			log.info("SecurityServiceC:handlePasswordForgetRequestSuccess:auditEvent processed successfully" + user);
		} catch (Throwable t) {
			log.error( "Problem with handlePasswordForgetRequestSuccess : " + user.getShortName(), t);
		}
	}
	
	@Override
	public void handlePasswordForgetRequestSuccess(User user, boolean auditEvent, boolean updateUserSession) {
		try {
			if (user == null) {
				return;
			}
			// audit event
			auditEvent(user, PASSWORDFORGOTREQUEST, PASSWORDFORGOTREQUEST_SUCCESS, true, auditEvent, updateUserSession);
		} catch (Throwable t) {
			log.error( "Problem with handlePasswordForgetRequestSuccess : " + user.getShortName(), t);
		}
	}
	
	@Override
	public void handlePasswordForgetRequestFailed(User user) {
		try {
			if (user == null) {
				return;
			}
			// suspend user
			suspendUser(user, true, false);
			log.info("SecurityServiceC:handlePasswordForgetRequestFailed:user locked successfully:" + user);
			// audit event
			auditEvent(user, PASSWORDFORGOTREQUEST, PASSWORDFORGOTREQUEST_FAILED, true, true, true);
			log.info("SecurityServiceC:handlePasswordForgetRequestFailed:auditEvent processed successfully" + user);
		} catch (Throwable t) {
			log.error( "Problem with handlePasswordForgetRequestFailed : " + user.getShortName(), t);
		}
	}
    
    

}
