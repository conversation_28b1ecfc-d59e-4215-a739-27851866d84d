package com.integral.session;

import com.integral.exception.IdcNoSuchObjectException;
import com.integral.user.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.security.Principal;

public class IdcSessionUtilC {


    public static User getUserFromSession(HttpServletRequest request) throws Exception {
        HttpSession session = request.getSession(false);
        if (session != null) {
            Object u = session.getAttribute("user");
            if (u instanceof User) {
                User user = (User)u;
                return user;
            } else {
                throw new Exception("Invalid Session");
            }
        } else {
            throw new Exception("Invalid Session");
        }
    }

    /**
     * Return a new session context for the user represented by the "uid"
     * cookie in the HttpServletRequest.
     *
     * @param request http request
     * @return an IdcSessionContext object for this user
     */
    public IdcSessionContext getSessionContext( HttpServletRequest request )
    {
        return IdcSessionManager.getInstance().getSessionContext( (Principal)
                request.getAttribute( IdcSessionContext.USER_KEY ) );
    }


    /**
     * Return a transaction for this httpSession. A session context is
     * associated with every httpSession. This method will return a
     * transaction for that session context.
     * Since this method looks up for user information from IdcSessionContext
     * httpSession must have an IdcSessionContext object in it.
     *
     * @param httpSession a session context of a user
     * @return an IdcTransaction object to be used for updating the
     * database.
     * @throws IdcNoSuchObjectException is throw if the user's httpSession
     *                                  doesn't have a value for key IdcSessionContext.SESSION_CONTEXT
     */
    public static IdcTransaction newTransaction( HttpSession httpSession ) throws
            IdcNoSuchObjectException
    {
        Object sessCtx = httpSession.getAttribute(
                IdcSessionContext.SESSION_CONTEXT );
        if ( sessCtx == null )
        {
            throw new IdcNoSuchObjectException( "httpSession did not have a IdcSessionContext object in it. A valid session context object is needed to create a new transaction." );
        }
        return IdcSessionManager.getInstance().newTransaction( ( IdcSessionContext ) sessCtx );
    }

}
