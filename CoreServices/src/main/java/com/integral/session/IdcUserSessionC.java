package com.integral.session;

import javax.servlet.http.HttpSession;
import java.util.Enumeration;

public class IdcUserSessionC implements IdcUserSession
{

    private HttpSession httpSession = null;

    public IdcUserSessionC( HttpSession session )
    {
        httpSession = session;
    }

    public String getId()
    {
        return httpSession == null ? null : httpSession.getId();
    }

    public Object getAttribute( String attrKey )
    {
        return httpSession == null ? null : httpSession.getAttribute( attrKey );
    }

    public void setAttribute( String attrKey, Object val )
    {
        if ( httpSession != null )
        {
            httpSession.setAttribute( attrKey, val );
        }
    }

    public void invalidate()
    {
        if ( httpSession != null )
        {
            httpSession.invalidate();
        }
    }

    public Long getCreationTime()
    {
        return httpSession == null ? null : httpSession.getCreationTime();
    }

    public Long getLastAccessedTime()
    {
        return httpSession == null ? null : httpSession.getLastAccessedTime();
    }

    public Enumeration getAttributeNames()
    {
        return httpSession == null ? null : httpSession.getAttributeNames();
    }

    public HttpSession getHttpSession()
    {
        return httpSession;
    }

    public quickfix.Session getQuickFixSession()
    {
        return null;
    }

    public int getSessiontype()
    {
        return SESSION_TYPE_HTTP;
    }
}