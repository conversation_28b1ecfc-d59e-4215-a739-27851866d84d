package com.integral.session;

// Copyright (c) 2011 Integral Development Corp. All rights reserved.

import com.integral.is.ISCommonConstants;
import com.integral.is.log.MessageLogger;
import com.integral.jsp.JSPApplication;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.messaging.MessageSender;
import com.integral.messaging.MessageSenderFactory;
import com.integral.messaging.MessagingException;
import com.integral.persistence.BusinessEventsManager;
import com.integral.persistence.PersistenceFactory;
import com.integral.security.CryptC;
import com.integral.security.LoginMBeanC;
import com.integral.security.SecurityServiceC;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.user.*;
import com.integral.userContainer.ChiefDealerUserCounterContainerC;
import com.integral.userContainer.UserCounterContainer;
import com.integral.userContainer.UserCounterContainerC;
import com.integral.userContainer.UserCounterContainerHandler;

import javax.servlet.http.HttpSession;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


/**
 * Provides the management functions required by UserService. This class is a singleton, and only has package visibility.<br>
 *
 * <AUTHOR> Development Corp.
 */
public class UserServiceManager
{
    protected Log log = LogFactory.getLog( this.getClass() );

    public final static String TOPIC = ISCommonConstants.TOPIC;
    public final static String EVENT = ISCommonConstants.EVENT;

    public final static String SESSION_ID = "sessionId";
    public final static String USER_ID = "userId";
    public final static String REDIRECTOR_URL = "redirector";
    public final String VIRTUAL_SERVER_NAME = "VIRTUAL_SERVER_NAME";
    private static UserServiceManager _singleton = new UserServiceManager();

    //todo - use more efficient structure to store users and webappinfo 
    private final Hashtable<String, WebAppInfo> _webappTable = new Hashtable<String, WebAppInfo>();
    private Hashtable<String, WebUser> _sessionTable = new Hashtable<String, WebUser>();
    private UserCounterContainer _loggedInUsersOrgContainer = new UserCounterContainerC();
    private UserCounterContainer _loggedInCDUsersOrgContainer = new ChiefDealerUserCounterContainerC();

    public final static String WEBAPP = "webapp";
    public static final String REASON = "REASON";

    //user cache containing user's info like authtoken, apiVersion for that user session
    private static ConcurrentHashMap<Long, UserInfo> authTokenUserCache = new ConcurrentHashMap<Long, UserInfo>();
    //default Grid API version used if not supplied in login request
    public static String DEFAULT_API_VERSION = "1.0";
    public static char AUTH_TOKEN_SEPARATOR = '~';
    public static final String GRID_API_WEBAPP = "/fxiapi";
    private static final String routingKey = "#";
    private static ServerRuntimeMBean serverRuntime = RuntimeFactory.getServerRuntimeMBean();

	private static class MessageSenderHolder {

		private static MessageSender messageSender;
		static {
			try {
				messageSender = MessageSenderFactory
						.newMessageSender(ServerRuntimeMBean.SESSION_CONTROL_EXCHANGE);
			} catch (MessagingException msge) {
				MessageLogger
						.getInstance()
						.log("MESSAGING.PUBLISHER.INIT.FAILED",
								UserServiceManager.class.getName(),
								"Error when initializing MessageSender;Check log for more details",
								null);
			}
		}
	}
	
    /**
     * private constructor.
     */
    private UserServiceManager()
    {
        _loggedInUsersOrgContainer.subscribeHandler( "LoggedInUsersOrgCounterContainerHandler", new com.integral.userContainer.LoggedInUsersOrgCounterContainerHandler() );
    }

    public static UserServiceManager getInstance()
    {
        return _singleton;
    }

    public void init() {}

    public void init( String jmsDestination ) {} //temporary

    /**
     * Shuts down user service mangager .Called by shutdown class.
     */
    public void shutdown()
    {
        log.info( "USM.shutdown logging out users." );
        for ( String webApp : getAllWebApps() )
        {
            WebAppInfo app = getWebApp( webApp );
            for ( User user : app.getUsersList() )
            {
                String userId = Long.toString( user.getObjectID() );
                WebUser wu = app.getUser( userId );
                for ( IdcUserSession session : wu.getSessionsList() )
                {
                    removeUserSession( webApp, user, session, "ServerShutDown" );
                }
            }
        }
        log.info( "USM.shutdown logging out users completed." );
    }

    /**
     * Set the single login value for a webapp.
     *
     * @param webapp web app
     * @param value  value
     */
    public void setSingleLogin( String webapp, boolean value )
    {
        WebAppInfo o;
        o = getWebApp( webapp );
        o.singleLogin = value;
    }

    /**
     * Get the WebAppInfo object for a webapp. One will be created if it didnot already exist.
     *
     * @param webapp webapp
     * @return web app info
     */
    protected WebAppInfo getWebApp( String webapp )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( "UserServiceManager.getWebApp: " + webapp );
        }

        if ( webapp == null )
        {
            log.warn( "UserServiceManager: webapp is null" );
            return null;
        }
        else
        {
            synchronized ( _webappTable )
            {
                WebAppInfo o = _webappTable.get( webapp );
                if ( o == null )
                {
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "UserServiceManager: Webapp not available, returning a new webapp named " + webapp );
                    }
                    o = new WebAppInfo( webapp );
                    _webappTable.put( webapp, o );
                }
                return o;
            }
        }
    }

    /**
     * Add a user session. Based on flag 'allWebs' determine whether to log-out user across different single-login webapps.
     *
     * @param webapp     web app
     * @param user       user
     * @param session    session
     * @param allWebApps boolean indicating whether to consider all web apps for duplicate session logout
     */
    public void addUserSession( String webapp, User user, IdcUserSession session, boolean allWebApps )
    {
        //iterate over all WebApps and the ones with different webApp name and singlelogin enabled - invalidate session for the given user
        //this was done to ensure single user is allowed to login cross client types - HTML client/.NET client
        if ( allWebApps )
        {
            try
            {
                List<String> allWebAppInfos = getAllWebApps();
                for ( String wa : allWebAppInfos )
                {
                    if ( !webapp.equals( wa ) )
                    {
                        WebAppInfo webAppInfo = getWebApp( wa );
                        if ( webAppInfo.singleLogin )
                        {
                            WebUser webUser = webAppInfo.getUser( Long.toString( user.getObjectID() ) );
                            if ( webUser != null )
                            {
                                webUser.invalidateAllSessions( UserService.SINGLE_LOGOUT );
                            }
                        }
                    }
                }
            }
            catch ( RuntimeException e )
            {
                log.error( "Exception --> " + e.getMessage(), e );
            }
        }

        addUserSession( webapp, user, session );
    }

    /**
     * Add a user session.
     *
     * @param webapp  web app
     * @param user    user
     * @param session session
     */
    public void addUserSession( String webapp, User user, IdcUserSession session )
    {
        try
        {
            log.warn( "UserServiceManager.addUserSession.INFO - WebApp : " + webapp + " User : " + user + " Session : " + session + " sessionid : " + session.getId() );
            WebAppInfo o = getWebApp( webapp );
            o.addUserSession( user, session );
            String userId = Long.toString( user.getObjectID() );
            _sessionTable.put( session.getId() + session.hashCode(), o.getUser( userId ) );
            incrementLoggedInUsersOrgCount( user, webapp );
            if ( session instanceof IdcUserSessionC )
            {
                HttpSession httpSession = session.getHttpSession();
                if ( httpSession != null )
                {
                    int sessionTimeout = ServerMBean.UNDEFINED_SESSION_TIMEOUT;
                    if ( webapp != null && webapp.startsWith( "/" ) )
                    {
                        sessionTimeout = ConfigurationFactory.getServerMBean().getWebAppSessionTimeout( webapp.substring( 1 ), user );
                    }
                    else if ( webapp != null )
                    {
                        sessionTimeout = ConfigurationFactory.getServerMBean().getWebAppSessionTimeout( webapp, user );
                    }
                    if ( sessionTimeout != ServerMBean.UNDEFINED_SESSION_TIMEOUT )
                    {
                        log.warn( new StringBuilder( 50 ).append( "User : " ).append( user.getShortName() ).append(
                                " using session MaxInactive interval as " ).append( sessionTimeout ).append( " seconds." ).toString() );
                        httpSession.setMaxInactiveInterval( sessionTimeout );
                    }
                }
            }
        }
        catch ( RuntimeException e )
        {
            log.error( "Exception --> " + e.getMessage(), e );
        }
    }

    /**
     * Remove a user from the
     *
     * @param webapp webapp
     * @param user   user
     * @param reason reason
     */
    public void removeUser( String webapp, User user, String reason )
    {
        removeUser( webapp, Long.toString( user.getObjectId() ), reason );
    }

    public void removeUser( String webapp, String userId, String reason )
    {
        WebAppInfo o = getWebApp( webapp );
        o.removeUser( userId, reason );
        removeWebappsWithoutUsers( o );

    }

    /**
     * calls session invalidate on all users of an Organization's sessions and remove users from cache
     *
     * @param orgName Organization shortname
     * @param reason reason
     */
    public Map<User, Collection<IdcUserSession>> invalidateAllSessionsOfLoggedInUsersForOrg( String orgName, String reason )
    {
    	Collection<User> loggedInUsersForOrg = getAllLoggedInUsersForOrg(orgName);
    	Map< User, Collection<IdcUserSession> > invalidatedUserSessionMap = new HashMap<User, Collection<IdcUserSession>>();
    	for(User user : loggedInUsersForOrg )
    	{
    		try
    		{
    			Collection<IdcUserSession> invalidatedUserSessions = invalidateAllSessionsOfUser(user, reason);
    			invalidatedUserSessionMap.put(user, invalidatedUserSessions);
    		}
    		catch( Exception e )
    		{
    			log.error( "UserServiceManager.invalidateAllSessionsOfLoggedInUsersForOrg.ERROR : Exception while invalidating session of user="+user.getGUID()
    					+" for org="+orgName, e );
    		}
    	}
    	return invalidatedUserSessionMap;
    }
    
    /**
     * calls session invalidate on all specified user's sessions and removes user from cache
     *
     * @param user   user
     * @param reason reason
     */
    public Collection<IdcUserSession> invalidateAllSessionsOfUser( User user, String reason )
    {
        String userID = Long.toString( user.getObjectID() );
        List<String> allWebApps = getAllWebApps();
        Collection<IdcUserSession> removedUserSessions = new HashSet<IdcUserSession>();
        for ( String webApp : allWebApps )
        {
            List<IdcUserSession> userSessions = getAllUserSessions( webApp, userID );
            for ( IdcUserSession userSession : userSessions )
            {
                removeUserSession( webApp, user, userSession, reason );
                removedUserSessions.add(userSession);
                log.info("UserServiceManager.invalidateAllSessionsOfUser: Invalidated user session="+userSession.getId()+", for user="+user.getFullName());
            }
        }
        return removedUserSessions;
    }


    /**
     * Remove a User's session, specifying the reason - {@link UserService#LOGGED_OUT} or {@link UserService#TIMED_OUT}.
     *
     * @param webapp  webapp
     * @param user    user
     * @param session session
     * @param reason  reason
     */
    public void removeUserSession( String webapp, User user, IdcUserSession session, String reason )
    {
        removeUserSession( webapp, user, session, reason, true );
    }

    /**
     * Remove a User's session, specifying the reason - {@link UserService#LOGGED_OUT} or {@link UserService#TIMED_OUT}.
     * @param webapp     webapp
     * @param user       user
     * @param session    session
     * @param reason     reason
     * @param invalidate set to true if you want to invalidate the session
     */
    public void removeUserSession( String webapp, User user, IdcUserSession session, String reason, boolean invalidate )
    {
        if ( user == null )
        {
            log.error( "UserServiceManager.removeUserSession.ERROR - Unable to perform user session cleanup. Null User Passed. Details - webapp: " + webapp + " user: " + user + " sessionId: " + session.getId() + " invalidate: " + invalidate );
            return;
        }

        log.warn( "UserServiceManager.removeUserSession.INFO - webapp: " + webapp + " user: " + user + " userId: " + user.getObjectID() + " sessionId: " + session.getId() + " invalidate: " + invalidate );

        if ( webapp == null )
        {
            log.warn( "UserServiceManager: removeUserSession for NULL webapp user: " + user + " userId: " + user.getObjectID() + " sessionId: " + session.getId() + " invalidate: " + invalidate );
        }
        else
        {
            String userId = Long.toString( user.getObjectID() );
            WebAppInfo o = getWebApp( webapp );
            if ( o != null )
            {
                WebUser wu = o.getUser( userId );
                if ( wu != null )
                {
                    wu.invalidateAndRemoveSession( session, reason, invalidate );
                }

                _sessionTable.remove( session.getId() + session.hashCode() );
                decrementLoggedInUsersOrgCount( user, webapp );
                removeWebappsWithoutUsers( o );
            }
        }
    }

    /**
     * Remove a User's session, specifying the reason.
     *
     * @param webapp    web app
     * @param userId    user id
     * @param sessionId the sessionId of the actual session.
     * @param reason    reason
     */
    public void removeUserSession( String webapp, String userId, String sessionId, String reason )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( "UserServiceManager: removeUserSession for webapp: " + webapp + " userId: " + userId + " sessionId: " + sessionId );
        }
        if ( webapp == null )
        {
            log.warn( "UserServiceManager: removeUserSession for NULL webapp userId: " + userId + " sessionId: " + sessionId );
        }
        else
        {
            WebAppInfo o = getWebApp( webapp );
            if ( o != null )
            {
                synchronized ( o )
                {
                    WebUser wu = o.getUser( userId );
                    if ( wu != null )
                    {
                        wu.invalidateAndRemoveSession( null, sessionId, reason, true );
                    }
                    removeWebappsWithoutUsers( o );
                }
            }
        }
    }

    /**
     * @param session HttpSession
     * @return User
     */
    public User getUser( IdcUserSession session )
    {
        if ( null == session )
        {
            return null;
        }

        WebUser webUser = _sessionTable.get( session.getId() + session.hashCode() );
        if ( webUser != null )
        {
            return webUser.getUser();
        }
        return null;
    }

    /**
     * @param session HttpSession
     * @return JSPApplication
     */
    public JSPApplication getJSPApplication( IdcUserSession session )
    {
        WebUser webUser = _sessionTable.get( session.getId() + session.hashCode() );
        if ( webUser != null )
        {
            return webUser.getJSPApplication();
        }
        return null;
    }

    /**
     * @param session HttpSession
     * @return String WebApplication name
     */
    public String getWebAppName( IdcUserSession session )
    {
        WebUser webUser = _sessionTable.get( session.getId() + session.hashCode() );
        if ( webUser != null )
        {
            return webUser.getWebAppName();
        }
        return null;
    }


    /**
     * Remove webappinfo objects from webapptable if there are no users
     *
     * @param o the WebAppInfo object for the webapp.
     */
    private void removeWebappsWithoutUsers( WebAppInfo o )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( "UserServiceManager: removing webapps without users, webapp name is " + o.webapp );
        }
        synchronized ( o )
        {
            int size = o.users.size();

            if ( size == 0 )
            {
                _webappTable.remove( o.webapp );
            }
        }
    }

    /**
     * Get all the webapps that this service knows of.
     *
     * @return ArrayList of webapp names.
     */
    public ArrayList<String> getAllWebApps()
    {
        ArrayList<String> l = new ArrayList<String>();

        synchronized ( _webappTable )
        {

            for ( String o : _webappTable.keySet() )
            {
                l.add( o );
            }

        }
        return l;
    }

    /**
     * Get all thte users that are logged into a webapp.
     *
     * @param webapp webapp
     * @return ArrayList of <code>userId</code>s ({@link User#getObjectID}) of the users
     */
    public ArrayList<User> getAllUsers( String webapp )
    {
        WebAppInfo o = getWebApp( webapp );
        return o.getUsersList();
    }

    /**
     * Get all the sessions for a user in a webapp.
     *
     * @param webapp       webapp name without the slash
     * @param userObjectId user's object id
     * @return ArrayList of HttpSession objects.
     */
    public ArrayList<IdcUserSession> getAllUserSessions( String webapp, String userObjectId )
    {
        WebAppInfo w = getWebApp( webapp );
        if ( w != null )
        {
            WebUser wu = w.getUser( userObjectId );
            if( wu != null)
                return wu.getSessionsList();
        }
        return new ArrayList<IdcUserSession>( 0 );
    }

    /**
     * Get a user's session represented by <code>sessionId</code>.
     *
     * @param webapp       webapp
     * @param userObjectId user object id
     * @param sessionId    session id
     * @return user session
     */
    public IdcUserSession getUserSession( String webapp, String userObjectId, String sessionId )
    {

        ArrayList<IdcUserSession> l = getAllUserSessions( webapp, userObjectId );
        for ( IdcUserSession session : l )
        {
            if ( session.getId().equals( sessionId ) )
            {
                return session;
            }
        }

        return null;
    }
    
    /**
	 * Get all the sessions for a broker's users in a webapp. If brokerName is
	 * null, Integral user wants to sent message to all customers
	 *
	 * @param webapp
	 *            webapp
	 * @param brokersName
	 *            brokerName
	 * @return users session
	 */
	public ArrayList<IdcUserSession> getUserSessionsForBroker(String webapp, String brokersName, boolean onlyBrokerUsers) {
		List<String> brokers = null;
		if (brokersName != null) {
			brokers = Arrays.asList(brokersName.split(","));
		}
		ArrayList<IdcUserSession> idcUserSessions = new ArrayList<IdcUserSession>();
		WebAppInfo webAppInfo = getWebApp(webapp);
		for (User user : webAppInfo.getUsersList()) {
			String userBrokerOrg = null;
			if(!onlyBrokerUsers && user.getOrganization().getBrokerOrganization() != null){
				userBrokerOrg = user.getOrganization().getBrokerOrganization().getShortName();
			}
			if ((brokersName == null && !onlyBrokerUsers)
					|| (brokersName == null && onlyBrokerUsers && user.getOrganization().isBroker())
					|| (brokers != null && brokers.contains(user.getOrganization().getShortName()))
					|| (brokers != null && brokers.contains(userBrokerOrg))) {
				String userObjectId = Long.toString(user.getObjectID());
				WebUser wu = webAppInfo.getUser(userObjectId);
				if (wu != null) {
					idcUserSessions.addAll(wu.getSessionsList());
				}
			}
		}

		return idcUserSessions;
	}

    public Hashtable<? extends String, ? extends WebAppInfo> getWebappTable()
    {
        return _webappTable;
    }

/*  private void _sendJMSMsg(String event, String webapp, String userId, String sessionId, String reason) {
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setParameterValue(SESSION_ID, sessionId);
        msg.setParameterValue(USER_ID, userId);
        msg.setParameterValue(WEBAPP, webapp);
        msg.setTopic("SESSION");
        msg.setEventName(event);

        HashMap jmsProperties = new HashMap();
        jmsProperties.put(SESSION_ID, sessionId);
        jmsProperties.put(USER_ID, userId);
        jmsProperties.put(WEBAPP, webapp);
        jmsProperties.put(IdcSessionC.TOPIC, "SESSION");
        jmsProperties.put(IdcSessionC.EVENT, event);
        if (reason != null)
            jmsProperties.put(REASON, reason);

        try {
            BusinessEventsManager.getInstance().update(_jmsDestination, msg, jmsProperties, BusinessEventsManager.SERIALIZE_AS_BINARY,null, null);
        } catch (JMSException e) {
            log.error("UserServiceManager: Exception: ", e);
        } catch (IOException e) {
            log.error("UserServiceManager: Exception: ", e);
        }
    }*/

    private void _sendJMSMsg( String event, String webapp, User user, String sessionId, String reason,
                              String sessionTimeoutURL, String redirectorPath )
    {
        String userId = "";
        String brokerOrgName = "";
        String orgName = "";
        if ( user != null )
        {
            userId = Long.toString( user.getObjectID() );
            Organization brokerOrg = user.getOrganization().getBrokerOrganization();
            if ( brokerOrg != null )
            {
                brokerOrgName = brokerOrg.getShortName();
            }
            orgName = user.getOrganization().getShortName();
        }

        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setParameterValue( SESSION_ID, sessionId );
        msg.setParameterValue( USER_ID, userId );
        msg.setParameterValue( WEBAPP, webapp );
        msg.setTopic( "SESSION" );
        msg.setEventName( event );

        HashMap<String, String> jmsProperties = new HashMap<String, String>();
        ServerMBean serverMBean = ConfigurationFactory.getServerMBean();
        jmsProperties.put( SESSION_ID, sessionId );
        jmsProperties.put( USER_ID, userId );
        jmsProperties.put( WEBAPP, webapp );
        jmsProperties.put( ISCommonConstants.TOPIC, "SESSION" );
        jmsProperties.put( ISCommonConstants.EVENT, event );
        jmsProperties.put( REDIRECTOR_URL, redirectorPath );
        jmsProperties.put( VIRTUAL_SERVER_NAME, serverMBean.getVirtualServerName() );
        if ( reason != null )
        {
            jmsProperties.put( REASON, reason );
        }
        sessionTimeoutURL = ( null != sessionTimeoutURL ) ? sessionTimeoutURL : "";
        jmsProperties.put( "sessionTimeoutURL", sessionTimeoutURL );
        //adding organization and broker organization
        jmsProperties.put( "BrokerOrganization", brokerOrgName );
        jmsProperties.put( "Organization", orgName );

        try {
           sendMessage(msg,jmsProperties);
        }
        catch ( IOException e )
        {
            log.error( "UserServiceManager: Exception: ", e );
        } catch (MessagingException e) {
        	 log.error( "UserServiceManager: Exception: ", e );
		}
    }

    /**
     * Descriptor class for holding information related to a webapp.
     */
    private class WebAppInfo
    {
        private boolean singleLogin = true;
        private HashMap<String, WebUser> users = new HashMap<String, WebUser>();
        private String webapp = null;

        public WebAppInfo( String webapp )
        {
            this.webapp = webapp;
        }

        void addUserSession( User user, IdcUserSession session )
        {
            String userId = Long.toString( user.getObjectID() );
            WebUser wu;
            synchronized ( this )
            {
                wu = users.get( userId );
                if ( wu == null )
                {
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "UserServiceManager: WebUser not available, creating a new one to be added. WebUser's userid is " + userId );
                    }
                    JSPApplication jspApplication = ( JSPApplication ) session.getAttribute( JSPApplication.SESSION_ATTRIBUTE );
                    wu = new WebUser( webapp, user, jspApplication );
                    users.put( userId, wu );
                }
            }

            if ( singleLogin )
            {
                wu.invalidateAllSessions( UserService.SINGLE_LOGOUT );
            }
            wu.addSession( session );

        }

        synchronized WebUser getUser( String userId )
        {
            return users.get( userId );
        }

        void removeUser( String userId, String reason )
        {
            removeUser( userId, true, reason );
        }

        //When session is already invalidated, you don't want to invalidate them again.
        //In those cases, set invalidateAllSessions to false
        void removeUser( String userId, boolean invalidateAllSessions, String reason )
        {
            WebUser u;
            synchronized ( this )
            {
                u = users.get( userId );
            }

            if ( u != null )
            {
                if ( invalidateAllSessions )
                {
                    u.invalidateAllSessions( reason );
                }
            }

            synchronized ( this )
            {
                users.remove( userId );
            }
        }

        synchronized ArrayList<User> getUsersList()
        {
            ArrayList<User> l = new ArrayList<User>();

            for ( WebUser wUser : this.users.values() )
            {
                User user = wUser.user;
                l.add( user );
            }

            return l;
        }
    }

    private class WebUser
    {
        private User user;
        private String userId;
        private long accessTime = System.currentTimeMillis();
        private HashSet<IdcUserSession> sessions = new HashSet<IdcUserSession>();
        private String webapp;
        private JSPApplication jspApplication;

        public WebUser( String webapp, User user, JSPApplication jspApplication )
        {
            this.user = user;
            this.userId = Long.toString( user.getObjectID() );
            this.webapp = webapp;
            this.jspApplication = jspApplication;
        }

        JSPApplication getJSPApplication()
        {
            return jspApplication;
        }

        User getUser()
        {
            return user;
        }

        String getWebAppName()
        {
            return webapp;
        }

        void invalidateAllSessions( String reason )
        {
            ArrayList<IdcUserSession> l = new ArrayList<IdcUserSession>();

            synchronized ( this.user )
            {
                if ( log.isDebugEnabled() )
                {
                    printAll();
                    log.debug( "UserServiceManager: invalidating all sessions for user " + user );
                }
                _ping();
                Iterator<IdcUserSession> it = sessions.iterator();

                // make a copy of the sessions HashSet. donot invalidate in this loop, as invalidate causes
                // HttpSessionListener to callback invalidateAndRemoveSession - resulting in a ConcurrentModificationException.
                while ( it.hasNext() )
                {
                    IdcUserSession httpSession = it.next();
                    l.add( httpSession );
                    it.remove();
                }
            }

            String sessionId = null;

            for ( IdcUserSession httpSession : l )
            {
                try
                {
                    sessionId = httpSession.getId();
                    User loggedInUser = ( User ) httpSession.getAttribute( IdcSessionContext.USER_KEY );
                    String redirectorPath = ( String ) httpSession.getAttribute( REDIRECTOR_URL );
                    String sessionTimeoutURL = null;
                    if ( loggedInUser != null )
                    {
                        sessionTimeoutURL = ( String ) loggedInUser.getOrganization().getCustomFieldValue( JSPApplication.SESSION_TIMEOUT_URL );
                    }

                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "UserServiceManager: invalidating session... " + httpSession );
                    }
                    if ( reason != null )
                    {
                        httpSession.setAttribute( REASON, reason );
                    }
                    try
                    {
                        httpSession.invalidate();
                    }
                    catch ( Exception e )
                    {
                        log.error( "Exception(Ignored): ", e );
                    }
                    setUserLogoutTime( httpSession, loggedInUser );
                    _sendJMSMsg( "logout", webapp, user, sessionId, reason, sessionTimeoutURL, redirectorPath );
                }
                catch ( IllegalStateException eI )
                {
                    log.warn( "UserServiceManager: Session " + sessionId + " is already invalidated for user "
                            + ( ( user == null ) ? "NULL" : user.getFullName() ) );
                }
                catch ( Exception e )
                {
                    log.warn( "UserServiceManager: error when invalidating http session for user: " + user, e );
                }
            }

        }

        void addSession( IdcUserSession session )
        {
            synchronized ( this.user )
            {
                printAll();
                _ping();
                sessions.add( session );
            }
            _sendJMSMsg( "login", webapp, user, session.getId(), null, null, null );
        }

        //Will always invalidate and remove a session
        //If you just want to remove a session and not invalidate it, call the overloaded method of the same signature
        void invalidateAndRemoveSession( IdcUserSession session, String reason )
        {
            synchronized (this.user) {
                invalidateAndRemoveSession( session, reason, true );
            }
        }

        //Will remove session and invalidate it if invalidateSession argument is true
        void invalidateAndRemoveSession( IdcUserSession session, String reason, boolean invalidateSession )
        {
            synchronized (this.user) {
                invalidateAndRemoveSession( session, session.getId(), reason, invalidateSession );
            }
        }

        //Will remove session and invalidate it if invalidateSession argument is true
        void invalidateAndRemoveSession( IdcUserSession userSession, String sessionId, String reason, boolean invalidateSession )
        {
            synchronized (this.user){
            if ( log.isDebugEnabled() )
            {
                printAll();
                log.debug( "UserServiceManager$WebUser.invalidateAndRemoveSession: sessionId=" + sessionId
                        + " : reason=" + reason + " : invalidateSession=" + invalidateSession );
            }
            _ping();

            String sessionIdToRemove;
            String redirectorPath;
            String sessionTimeoutURL;
            User loggedInUser;

            ArrayList<IdcUserSession> l = new ArrayList<IdcUserSession>();
            Iterator<IdcUserSession> it = sessions.iterator();
            while ( it.hasNext() )
            {
                IdcUserSession httpSession = it.next();
                l.add( httpSession );
                if ( httpSession.getId().equals( sessionId ) )
                {
                    it.remove();
                }
            }

            for ( IdcUserSession session : l )
            {
                sessionIdToRemove = session.getId();
                try
                {
                    //This is included before sessionid check so as to throw IllegalStateException if session is already invalidated.
                    //This will help in removing the invalidated sessions from sessions list
                    loggedInUser = ( User ) session.getAttribute( IdcSessionContext.USER_KEY );
                    if ( session.getId().equals( sessionId ) )
                    {
                        redirectorPath = ( String ) session.getAttribute( REDIRECTOR_URL );
                        sessionTimeoutURL = ( String ) loggedInUser.getOrganization().getCustomFieldValue( JSPApplication.SESSION_TIMEOUT_URL );

                        if ( log.isDebugEnabled() )
                        {
                            log.debug( "UserServiceManager$WebUser.invalidateAndRemoveSession: MATCH: " );
                        }

                        if ( reason != null )
                        {
                            session.setAttribute( REASON, reason );
                        }
                        if ( invalidateSession )
                        {
                            try
                            {
                                session.invalidate();
                            }
                            catch ( Exception e1 )
                            {
                                if ( log.isInfoEnabled() )
                                {
                                    log.info( "UserServiceManager$WebUser.invalidateAndRemoveSession: error invalidating session " + session.getId(), e1 );
                                }
                            }
                        }
                        setUserLogoutTime( session, loggedInUser );
                        _sendJMSMsg( "logout", webapp, user, sessionIdToRemove, reason, sessionTimeoutURL, redirectorPath );
                        break;
                    }
                    else
                    {
                        if ( log.isDebugEnabled() )
                        {
                            log.debug( "UserServiceManager$WebUser.invalidateAndRemoveSession: NOT A MATCH: " );
                        }
                    }
                }
                catch ( IllegalStateException eI )
                {
                    //calling settter / getter on session object which is already invalidated causes IllegalStateException.
                    //For these cases removing session from sessions list
                    try
                    {
                        it.remove();
                    }
                    catch ( IllegalStateException e1 )
                    {
                        //Only thrown if session is already removed from sessionlist // Do nothing
                    }
                    catch ( ConcurrentModificationException e2 )
                    {
                        //Only thrown if ssssion is already removed from sessionlist //Do nothing
                    }

                    if ( log.isDebugEnabled() )
                    {
                        log.warn( "UserServiceManager: Session " + sessionId + " is already invalidated for user "
                                + ( ( user == null ) ? "NULL" : user.getFullName() ), eI );
                    }
                    else
                    {
                        log.warn( "UserServiceManager: Session " + sessionId + " is already invalidated for user "
                                + ( ( user == null ) ? "NULL" : user.getFullName() ) );
                    }
                }
                catch ( Exception e )
                {
                    if ( log.isDebugEnabled() )
                    {
                        log.warn( "UserServiceManager$WebUser.invalidateAndRemoveSession: error processing session " + sessionIdToRemove, e );
                    }
                    else
                    {
                        log.warn( "UserServiceManager$WebUser.invalidateAndRemoveSession: error processing session " + sessionIdToRemove );
                    }
                }
            }
            }
        }

        ArrayList<IdcUserSession> getSessionsList()
        {
            synchronized (this.user) {
                ArrayList<IdcUserSession> l = new ArrayList<IdcUserSession>();
                for ( IdcUserSession session : this.sessions )
                {
                    l.add( session );
                }

                return l;
            }
        }

        void _ping()
        {
            accessTime = System.currentTimeMillis();
        }

        /**
         * Returns true if the user has been active in the <code>duration</code> ms.
         *
         * @param duration time in ms.
         * @return active
         */
        boolean isUserActive( long duration )
        {
            synchronized (this.user) {
                return ( sessions.size() > 0 && ( ( System.currentTimeMillis() - accessTime ) <= duration ) );
            }
        }

        void printAll()
        {
            if ( log.isDebugEnabled() )
            {
                log.debug( "UserServiceManager$WebUser.printAll: START." );
                for ( IdcUserSession session : sessions )
                {
                    log.debug( userId + "-->" + session.getId() );
                }
                log.debug( "UserServiceManager$WebUser.printAll: END." );
            }
        }
    }


    /**
     * Sends a system alert message to all logged in users.
     *
     * @param alertMsg     alert message
     * @param alertTimeout timeout
     */
    public void sendSystemAlert( String alertMsg, long alertTimeout )
    {
        if ( log.isInfoEnabled() )
        {
            log.info( "UserServiceManager: sending system alert <" + alertMsg + "> to all users using timeout " + alertTimeout );
        }
        for ( String webApp : UserServiceManager.getInstance().getAllWebApps() )
        {
            for ( User user : UserServiceManager.getInstance().getAllUsers( webApp ) )
            {
                for ( IdcUserSession hs : getAllUserSessions( webApp, "" + user.getObjectId() ) )
                {
                    if ( log.isInfoEnabled() )
                    {
                        log.info( "UserServiceManager: sending alert to user " + user.getName() + " (" + webApp + "): " + hs );
                    }

                    WorkflowMessage msg = MessageFactory.newWorkflowMessage();
                    msg.setParameterValue( SESSION_ID, hs.getId() );
                    msg.setParameterValue( USER_ID, "" + user.getObjectId() );
                    msg.setParameterValue( WEBAPP, webApp );
                    msg.setTopic( "SESSION" );
                    msg.setEventName( "systemalert" );

                    HashMap<String, String> jmsProperties = new HashMap<String, String>();
                    jmsProperties.put( SESSION_ID, hs.getId() );
                    jmsProperties.put( USER_ID, "" + user.getObjectId() );
                    jmsProperties.put( WEBAPP, webApp );
                    jmsProperties.put( ISCommonConstants.TOPIC, "SESSION" );
                    jmsProperties.put( ISCommonConstants.EVENT, "systemalert" );
                    jmsProperties.put( REASON, alertMsg );
                    jmsProperties.put( "TIMEOUT", "" + alertTimeout );

                    try {
                        sendMessage(msg, jmsProperties);
                    }
                    catch ( IOException e )
                    {
                        log.error( "UserServiceManager:  error when sending system alert: " + e );
                    } catch (MessagingException e) {
                    	log.error( "UserServiceManager: error when sending system alert: " + e );
					}
                }
            }
        }
    }


    /**
     * Sends an upload request to the client
     *
     * @param user - the user for which the upload request is intended for
     * @param data - which data should be uploaded
     */
    public void sendUploadRequest( User user, String data )
    {
        if ( user == null )
        {
            log.warn( "UserServiceManager: skip sending upload request for data <" + data + "> to NULL user" );
            return;
        }
        if ( log.isInfoEnabled() )
        {
            log.info( "UserServiceManager: sending upload request for data <" + data + "> to user " + user.getFullName() );
        }
        for ( String webApp : UserServiceManager.getInstance().getAllWebApps() )
        {
            for ( IdcUserSession hs : getAllUserSessions( webApp, "" + user.getObjectId() ) )
            {
                if ( log.isInfoEnabled() )
                {
                    log.info( "UserServiceManager: sending upload request for data <" + data + "> to user " + user.getName() + " (" + webApp + "): " + hs );
                }

                WorkflowMessage msg = MessageFactory.newWorkflowMessage();
                msg.setParameterValue( SESSION_ID, hs.getId() );
                msg.setParameterValue( USER_ID, "" + user.getObjectId() );
                msg.setParameterValue( WEBAPP, webApp );
                msg.setTopic( "SESSION" );
                msg.setEventName( "upload" );

                HashMap<String, String> jmsProperties = new HashMap<String, String>();
                jmsProperties.put( SESSION_ID, hs.getId() );
                jmsProperties.put( USER_ID, "" + user.getObjectId() );
                jmsProperties.put( WEBAPP, webApp );
                jmsProperties.put( ISCommonConstants.TOPIC, "SESSION" );
                jmsProperties.put( ISCommonConstants.EVENT, "upload" );
                jmsProperties.put( REASON, "upload request" );
                jmsProperties.put( "DATA", data );

                try {
                    sendMessage(msg, jmsProperties);
                }
                catch ( IOException e )
                {
                    log.error( "UserServiceManager:  error when sending upload request: " + e );
                } catch (MessagingException e) {
                	log.error( "UserServiceManager:  error when sending upload request: " + e );
				}
            }
        }
    }

    /**
     * Sends an upload request to the client
     *
     * @param userName - the full user name of the user for which the upload request is intended for
     * @param data     - which data should be uploaded
     */
    public void sendUploadRequest( String userName, String data )
    {
        User user = UserFactory.getUser( userName );
        sendUploadRequest( user, data );
    }

    /**
     * Sends a logout message to all currently logged in users.
     */
    public void sendLogoutMessage()
    {
        if ( log.isInfoEnabled() )
        {
            log.info( "UserServiceManager: sending system logout message to all users" );
        }
        for ( String webApp : UserServiceManager.getInstance().getAllWebApps() )
        {
            for ( User user : UserServiceManager.getInstance().getAllUsers( webApp ) )
            {
                for ( IdcUserSession hs : getAllUserSessions( webApp, "" + user.getObjectId() ) )
                {
                    if ( log.isInfoEnabled() )
                    {
                        log.info( "UserServiceManager: sending logout message to user " + user.getName() + " (" + webApp + "): " + hs );
                    }

                    WorkflowMessage msg = MessageFactory.newWorkflowMessage();
                    msg.setParameterValue( SESSION_ID, hs.getId() );
                    msg.setParameterValue( USER_ID, "" + user.getObjectId() );
                    msg.setParameterValue( WEBAPP, webApp );
                    msg.setTopic( "SESSION" );
                    msg.setEventName( "systemalert" );

                    HashMap<String, String> jmsProperties = new HashMap<String, String>();
                    jmsProperties.put( SESSION_ID, hs.getId() );
                    jmsProperties.put( USER_ID, "" + user.getObjectId() );
                    jmsProperties.put( WEBAPP, webApp );
                    jmsProperties.put( ISCommonConstants.TOPIC, "SESSION" );
                    jmsProperties.put( ISCommonConstants.EVENT, "logout" );
                    jmsProperties.put( REASON, "Forced System Logout" );

                    try {
                        sendMessage(msg, jmsProperties);
                    }
                    catch ( IOException e )
                    {
                        log.error( "UserServiceManager:  error when sending logout: " + e );
                    } catch (MessagingException e) {
                    	 log.error( "UserServiceManager:  error when sending logout: " + e );
					}
                }
            }
        }
    }

    /**
     * Sends a logout message to a currently logged in user with the given user id.
     *
     * @param userId user id
     */
    public void sendLogoutMessage( long userId )
    {
        String uid = Long.toString( userId );

        if ( log.isInfoEnabled() )
        {
            log.info( "UserServiceManager: sending system logout message user #" + uid );
        }
        for ( String webApp : UserServiceManager.getInstance().getAllWebApps() )
        {
            for ( IdcUserSession hs : getAllUserSessions( webApp, uid ) )
            {
                if ( log.isInfoEnabled() )
                {
                    log.info( "UserServiceManager: sending logout message to user #" + uid + " (" + webApp + "): " + hs );
                }

                WorkflowMessage msg = MessageFactory.newWorkflowMessage();
                msg.setParameterValue( SESSION_ID, hs.getId() );
                msg.setParameterValue( USER_ID, uid );
                msg.setParameterValue( WEBAPP, webApp );
                msg.setTopic( "SESSION" );
                msg.setEventName( "systemalert" );

                HashMap<String, String> jmsProperties = new HashMap<String, String>();
                jmsProperties.put( SESSION_ID, hs.getId() );
                jmsProperties.put( USER_ID, uid );
                jmsProperties.put( WEBAPP, webApp );
                jmsProperties.put( ISCommonConstants.TOPIC, "SESSION" );
                jmsProperties.put( ISCommonConstants.EVENT, "logout" );
                jmsProperties.put( REASON, "Forced System Logout" );

                try {
                    sendMessage(msg, jmsProperties);
                }
                catch ( IOException e )
                {
                    log.error( "UserServiceManager:  error when sending logout: " + e );
                } catch (MessagingException e) {
                	log.error( "UserServiceManager:  error when sending logout: " + e );
				}
            }

        }
    }

    public void sendLogoutMessage( String userName )
    {
        if ( userName == null || userName.indexOf( '@' ) <= 0 )
        {
            log.warn( "UserName passed is null or does not contain Namespace" );
        }
        else
        {
            try
            {
                sendLogoutMessage( UserFactory.getUser( userName ).getObjectID() );
            }
            catch ( NullPointerException e )
            {
                log.warn( "Username passed in sendLogoutMessage() does not exist in DB." );
            }
        }
    }

    protected void setUserLogoutTime( IdcUserSession userSession, User user )
    {
        if ( user == null )
        {
            log.warn( "UserServiceManager: skip setting last logout time for null user" );
        }
        else if ( PersistenceFactory.getPersistenceMBean().isPersistenceReadOnlyMode() )
        {
            if ( log.isDebugEnabled() )
            {
                log.debug( "UserServiceManager: skip setting last logout time for user " + user.getFullName() + " since app is in read-only mode" );
            }
        }
        else
        {
            try
            {
                if ( user.getUserClassification() == null ||
                        !UserUtilC.DYNAMIC_USER_CLSF.getShortName().equals( user.getUserClassification().getShortName() ) )
                {
                    boolean auditEnabled = userSession != null && userSession.getSessiontype() == IdcUserSession.SESSION_TYPE_FIX && LoginMBeanC.getInstance().isAuditEnabled( LoginChannel.FIX_API );
                    SecurityServiceC.getInstance().handleLogout( user, auditEnabled );
                }

            }
            catch ( com.integral.exception.IdcOptimisticLockException ole )
            {
                if ( log.isDebugEnabled() )
                {
                    log.debug( "UserServiceManager: exception in setting last logout time for user "
                            + user.getFullName() + " -- retrying..." );
                }
                try
                {
                    if ( user.getUserClassification() == null ||
                            !UserUtilC.DYNAMIC_USER_CLSF.getShortName().equals( user.getUserClassification().getShortName() ) )
                    {
                        SecurityServiceC.getInstance().handleLogout( user, false );
                    }
                }
                catch ( Exception e )
                {
                    log.error( "UserServiceManager: exception in setting last logout time for user " + user.getFullName(), e );
                }
            }
            catch ( Exception pe )
            {
                log.error( "UserServiceManager:  exception in setting last logout time for user "
                        + user.getFullName(), pe );
            }
        }
    }


    /**
     * Sends a logout message to a currently logged in user with the given user id.
     *
     * @param userId   user id
     * @param isForced forced
     */
    public void sendLogoutMessage( long userId, boolean isForced )
    {
        if ( isForced )
        {
            String uid = Long.toString( userId );

            if ( log.isInfoEnabled() )
            {
                log.info( "UserServiceManager: sending forced logout message to user #" + uid );
            }

            WorkflowMessage msg = MessageFactory.newWorkflowMessage();
            msg.setParameterValue( USER_ID, uid );
            msg.setTopic( "SESSION" );
            msg.setEventName( "systemalert" );

            HashMap<String, String> jmsProperties = new HashMap<String, String>();
            jmsProperties.put( USER_ID, uid );
            jmsProperties.put( ISCommonConstants.TOPIC, "SESSION" );
            jmsProperties.put( ISCommonConstants.EVENT, "logout" );
            jmsProperties.put( REASON, "Forced System Logout" );

            try {
                sendMessage(msg, jmsProperties);
            }
            catch ( IOException e )
            {
                log.error( "UserServiceManager:  error when sending logout: " + e );
            } catch (MessagingException e) {
            	 log.error( "UserServiceManager: error when sending logout: " + e );
			}
        }
        else
        {
            sendLogoutMessage( userId );
        }
    }

    protected void sendMessage(WorkflowMessage msg, HashMap<String, String> jmsProperties) throws IOException, MessagingException {
    	BusinessEventsManager.getInstance().updateViaIntegralMessaging(getMessageSender(), routingKey, msg, jmsProperties, null, null);
    }

    private MessageSender getMessageSender() {
        return MessageSenderHolder.messageSender;
    }

    public void sendLogoutMessage( String userName, boolean isForced )
    {
        if ( userName == null || userName.indexOf( '@' ) <= 0 )
        {
            log.warn( "UserName passed is null or does not contain Namespace" );
        }
        else
        {
            try
            {
                sendLogoutMessage( UserFactory.getUser( userName ).getObjectID(), isForced );
            }
            catch ( NullPointerException e )
            {
                log.warn( "Username passed in sendLogoutMessage() does not exist in DB." );
            }
        }
    }


    public boolean isUserCurrentlyLoggedin( String userName, String namespace )
    {
        User user = UserFactory.getUser( new StringBuilder( 60 ).append( userName ).append( '@' ).append( namespace ).toString() );
        return user != null && isUserCurrentlyLoggedin( user );
    }

    public boolean isUserCurrentlyLoggedin( String userFullName )
    {
        User user = UserFactory.getUser( userFullName );
        return user != null && isUserCurrentlyLoggedin( user );
    }

    public boolean isUserCurrentlyLoggedin( User user )
    {
        String userID = Long.toString( user.getObjectID() );
        List<String> allWebApps = getAllWebApps();
        for ( String webApp : allWebApps )
        {
            List<IdcUserSession> userSessions = getAllUserSessions( webApp, userID );
            if ( userSessions != null && userSessions.size() > 0 )
            {
                return true;
            }
        }
        return false;
    }

    public boolean isUserCurrentlyLoggedin( User user , String webApp)
    {
        String userID = Long.toString( user.getObjectID() );
        List<IdcUserSession> userSessions = getAllUserSessions( webApp, userID );
        return userSessions != null && userSessions.size() > 0;
    }

    public boolean isUserCurrentlyLoggedInGridAPI( User user)
    {
        return isUserCurrentlyLoggedin( user, GRID_API_WEBAPP );
    }

    /**
     * Returns a list of users logged in to all web apps.
     *
     * @return list of users
     */
    public Collection<User> getAllLoggedInUsers()
    {
        Collection<User> loggedInUsers = new HashSet<User>();
        try
        {
            List<String> allWebApps = getAllWebApps();
            for ( String webApp : allWebApps )
            {
                List<User> users = getAllUsers( webApp );
                if ( users != null )
                {
                    loggedInUsers.addAll( users );
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "UserServiceManager.getAllLoggedInUsers.ERROR : Exception while getting all logged in users.", e );
        }
        return loggedInUsers;
    }

    /**
     * Returns a list of users logged in to all web apps for an Organization.
     *
     *@param orgName Organization short name
     * @return list of users
     */    
    public Collection<User> getAllLoggedInUsersForOrg( String orgName )
    {
    	Collection<User> loggedInUsersForOrg = new HashSet<User>();
    	Collection<User> loggedInUsers = getAllLoggedInUsers();
    	try
    	{
        	for( User user : loggedInUsers )
        	{
        		if( orgName.equals( user.getOrganization().getShortName() ) )
        		{
        			loggedInUsersForOrg.add(user);
        		}
        	}    		
    	}
    	catch ( Exception e )
    	{
    		log.error( "UserServiceManager.getAllLoggedInUsersForOrg.ERROR : Exception while getting all logged in users for org="+orgName, e );
    	}
    	return loggedInUsersForOrg;
    }
    
    public boolean hasLoggedInUsers( String orgName )
    {
        return orgName != null && _loggedInUsersOrgContainer.getCount( orgName ) > 0;
    }

    public boolean hasLoggedInCDUsers( String orgName )
    {
        return orgName != null && _loggedInCDUsersOrgContainer.getCount( orgName ) > 0;
    }

    public void incrementLoggedInUsersOrgCount( User user, String webApp )
    {
        _loggedInUsersOrgContainer.incCount( user, webApp );
        _loggedInCDUsersOrgContainer.incCount( user, webApp );
    }

    public void decrementLoggedInUsersOrgCount( User user, String webApp )
    {
        _loggedInUsersOrgContainer.decCount( user, webApp );
        _loggedInCDUsersOrgContainer.decCount( user, webApp );
    }

    public final Map<String, Map<String, User>> getLoggedInUsersOrgContainerDetails()
    {
        return _loggedInUsersOrgContainer.getInternalMap();
    }

    public final Map<String, Map<String, User>> getLoggedInCDUsersOrgContainerDetails()
    {
        return _loggedInCDUsersOrgContainer.getInternalMap();
    }

    public void subscribeLoggedInCDUsersHandler( String name, UserCounterContainerHandler handler )
    {
        _loggedInCDUsersOrgContainer.subscribeHandler( name, handler );
    }

    public void unsubscribeLoggedInCDUsersHandler( String name )
    {
        _loggedInCDUsersOrgContainer.unsubscribeHandler( name );
    }

    public void addUserInfo( User user, String authToken )
    {
        UserInfo userInfo = new UserInfo( authToken );
        authTokenUserCache.put( user.getObjectId(), userInfo );
    }

    public void addUserInfo( User user, String authToken, String apiVersion )
    {
        UserInfo userInfo = new UserInfo( authToken, apiVersion );
        authTokenUserCache.put( user.getObjectId(), userInfo );
    }

    public void addUserInfo( User user, String authToken, String channel,
                             String apiVersion, boolean fixedFormatting, String clientName )
    {
        UserInfo userInfo = new UserInfo( authToken, channel, apiVersion, fixedFormatting, clientName );
        authTokenUserCache.put( user.getObjectId(), userInfo );
    }

    public void removeAuthToken( User user )
    {
        authTokenUserCache.remove( user.getObjectId() );
    }

    public String getAuthToken( User user )
    {
        UserInfo userInfo = authTokenUserCache.get( user.getObjectId() );
        if ( userInfo != null )
        {
            return userInfo.getAuthToken();
        }
        return null;
    }

    public User getLoggedInUserFromAuthToken( String authToken )
    {
        String decryptedValue = CryptC.decryptURLSafeString( authToken );
        String userId = decryptedValue.substring( 0, decryptedValue.indexOf( AUTH_TOKEN_SEPARATOR ) );
        User user = UserFactory.getUser( userId );
        String authTokeOfLoggedinUser = user == null ? null : getAuthToken( user );
        if ( authTokeOfLoggedinUser != null && authTokeOfLoggedinUser.equals( authToken ) )
        {
            return user;
        }
        return null;
    }

    public User getSSOUser( String authToken )
    {
        String decryptedToken = CryptC.decryptURLSafeString( authToken );
        String userId = decryptedToken.substring( 0, decryptedToken.indexOf( AUTH_TOKEN_SEPARATOR ) );
        return UserFactory.getUser( userId );
    }

    public String getUserChannel( User user )
    {
        UserInfo userInfo = authTokenUserCache.get( user.getObjectId() );
        if ( userInfo != null )
        {
            return userInfo.getChannel();
        }
        return null;
    }

    public String getApiVersion( User user )
    {
        UserInfo userInfo = authTokenUserCache.get( user.getObjectId() );
        if ( userInfo != null )
        {
            return ( userInfo.getApiVersion() == null || userInfo.getApiVersion().trim().length() == 0 ) ? DEFAULT_API_VERSION : userInfo.getApiVersion();
        }
        return null;
    }

    public boolean isFixedFormatting( User user )
    {
        UserInfo userInfo = authTokenUserCache.get( user.getObjectId() );
        if ( userInfo != null )
        {
            return userInfo.isFixedFormatting();
        }
        return true;
    }

    public void setFixedFormatting(User user, boolean fixedFormatting) {
        UserInfo userInfo = authTokenUserCache.get( user.getObjectId() );
        if ( userInfo != null )
        {
            userInfo.setFixedFormatting(fixedFormatting);
        }

    }


    public UserInfo getUserInfo( User user )
    {
        return authTokenUserCache.get( user.getObjectID() );
    }


    public void handleAddFirstUser( User user )
    {
        _loggedInCDUsersOrgContainer.handleAddFirstUser( user );
    }


    public String getClientName(User user )
    {
        UserInfo userInfo = authTokenUserCache.get( user.getObjectId() );
        if ( userInfo != null )
        {
            return userInfo.getClientName();
        }
        return null;
    }
}

