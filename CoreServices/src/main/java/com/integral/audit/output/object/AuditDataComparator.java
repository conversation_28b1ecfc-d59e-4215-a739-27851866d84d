package com.integral.audit.output.object;

// Copyright (c) 2018 Integral Development Corp.  All rights reserved.

import java.util.Comparator;

/**
 * Comparator used for comparing the audit data.
 *
 * <AUTHOR> Development Corp.
 */
public class AuditDataComparator implements Comparator<AuditData>
{
    public int compare( AuditData ad1, AuditData ad2 )
    {
        return ad1.compareTo ( ad2 );
    }
}
