package com.integral.audit;

// Copyright (c) 2018 Integral Development Corp.  All rights reserved.


import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;

import com.integral.audit.configuration.AuditAlertConfigurationFactory;
import com.integral.audit.configuration.AuditAlertConfigurationMBean;
import com.integral.audit.output.object.AuditData;
import com.integral.audit.output.object.AuditDataComparator;
import com.integral.audit.output.object.AuditDataGenerator;
import com.integral.audit.output.object.AuditViewDataGeneratorFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageFactory;
import com.integral.message.MessageHandler;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.rule.SendEmailAction;
import com.integral.rule.SendEmailActionC;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.system.mail.SendEmailC;
import com.integral.system.notification.SendEmailThread;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.time.DateTimeFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.BodyPart;
import javax.mail.internet.MimeBodyPart;


/**
 * This class is used to manage audit alert email notifications
 *
 * <AUTHOR> Development Corp.
 */
public class AuditAlertEmailSenderC
{
    protected Log log = LogFactory.getLog( this.getClass() );
    private static AuditAlertConfigurationMBean _auditAlertConfig = AuditAlertConfigurationFactory.getAuditAlertConfigurationMBean ();
    private static final ServerMBean _serverMBean = ConfigurationFactory.getServerMBean();

    private static final String BANK_ORG_PARAM = "BankOrg";
    private static final String MODULE_PARAM = "Module";
    private static final String USER_PARAM = "User";
    private static final String USER_ORG_PARAM = "UserOrg";
    private static final String MODIFIED_NAMESPACE_PARAM = "ModifiedNamespace";
    private static final String DATE_TIME_PARAM = "DateTime";
    private static final String USER_ACTION_PARAM = "UserAction";
    private static final String DESCRIPTION_PARAM = "Description";
    private static final String ENV_NAME_PARAM = "EnvName";
    private static final String APP_NAME_PARAM = "AppName";
    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final String AUDIT_EVENT_TABLE_EMPTY_VALUE = "";

    private static final Map<String, Collection<AuditEvent>> auditEventsMap = new HashMap<String, Collection<AuditEvent>>( );
    private ScheduledExecutorService auditAlertScheduler;

    /**
     * Singleton instance
     */
    private static AuditAlertEmailSenderC _auditAlertEmailSender = new AuditAlertEmailSenderC();

    private MessageHandler notificationHandler = null;

    /**
     * Private empty constructor.
     */
    private AuditAlertEmailSenderC()
    {
        log.info("AAES() : Starting periodic audit alert notifier task.");
        initAuditAlertPeriodicTaskScheduler ();
    }

    public void setNotificationHandler ( MessageHandler mh )
    {
        this.notificationHandler = mh;
    }

    /**
     * Returns the singleton instance of AuditAlertEmailSenderC.
     *
     * @return audit alert email sender
     */
    public static AuditAlertEmailSenderC getInstance()
    {
        return _auditAlertEmailSender;
    }

    void sendAuditAlertEmail ( WorkflowMessage wm, Organization org )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "AAES.sendAuditAlertEmail : wm=" )
                    .append( wm ).append ( ",org=" ).append ( org ).toString() );
        }
        try
        {
            Collection<String> emailAddress = _auditAlertConfig.getAuditAlertEmailTo ( org );
            boolean alertEnabled = _auditAlertConfig.isAuditAlertEnabled ( org );
            if ( alertEnabled && emailAddress != null )
            {
                SendEmailAction emailAction = buildAuditAlertNotificationEmail( wm, org );
                long elapsed = System.currentTimeMillis() - t0;
                if ( emailAction != null )
                {
                    log.info( new StringBuilder( 200 ).append( "AAES.sendAuditAlertEmail : sending audit alert email. org=" )
                            .append( org ).append( ",tos=" ).append( emailAction.getTos() ).append( ",timeTaken=" )
                            .append( elapsed ).toString() );

                    if ( notificationHandler != null )
                    {
                        WorkflowMessage workflowMessage = MessageFactory.newWorkflowMessage ();
                        workflowMessage.setParameterValue ( "SendEmailAction", emailAction );
                        workflowMessage.setObject ( wm.getObject () );
                        notificationHandler.handle ( workflowMessage );
                    }

                    SendEmailC.getEventsPool ().execute( new SendEmailThread( emailAction ) );
                }
            }
            else if ( log.isDebugEnabled () )
            {
                log.debug( new StringBuilder( 150 ).append( "AAES.sendAuditAlertEmail : Either email is not enabled or Email not specified for provider org=" )
                        .append( org ).append( ",wm=" ).append( wm )
                        .append( ",emailAddress=" ).append( emailAddress ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "AAES.sendAuditAlertEmail : Error sending the audit alert email. wm=" + wm + ",org=" + org, e );
        }
    }

    private SendEmailAction buildAuditAlertNotificationEmail( WorkflowMessage wm, Organization org  )
    {
        SendEmailAction emailAction = null;
        try
        {
            String subject = _auditAlertConfig.getAuditAlertEmailSubject ( org );
            String emailMainContent = _auditAlertConfig.getAuditAlertEmailContent ( org );
            User user = getUser( org );

            if ( subject != null && emailMainContent != null )
            {
                if ( user == null )
                {
                    log.info ( "AAES.buildAuditAlertNotificationEmail : skipping creating audit alert as there is no user for org=" + org );
                    return null;
                }
                emailAction = new SendEmailActionC();
                emailAction.setTos( _auditAlertConfig.getAuditAlertEmailTo ( org ) );
                Collection<String> ccs = _auditAlertConfig.getAuditAlertEmailCC( org );
                if ( ccs != null )
                {
                    emailAction.setCCs( ccs );
                }

                Collection<String> bccs = _auditAlertConfig.getAuditAlertEmailBCC ( org );
                if ( bccs != null )
                {
                    emailAction.setBCCs ( bccs );
                }


                emailAction.setFrom( _auditAlertConfig.getAuditAlertEmailSender ( org ) );
                emailAction.setSubject( subject );

                // set parameters in the send mail action.
                Collection<AuditEvent> auditEvents = ( Collection<AuditEvent> ) wm.getObject ();
                List<AuditData> auditDataSet = new ArrayList<AuditData> (  );
                Map<String, Collection<AuditEvent>> auditEventsMap = new HashMap<String, Collection<AuditEvent>> (  );
                for ( AuditEvent ae: auditEvents )
                {
                    Collection<AuditEvent> aes = auditEventsMap.get ( ae.getComponent () );
                    if ( aes == null )
                    {
                        aes = new ArrayList<AuditEvent>();
                        auditEventsMap.put ( ae.getComponent (), aes );
                    }
                    aes.add ( ae  );
                }
                for ( String component: auditEventsMap.keySet () )
                {
                    AuditDataGenerator auditDataGenerator = AuditViewDataGeneratorFactory.getAuditViewDataGenerator ( component, auditEventsMap.get ( component ) );
                    if ( auditDataGenerator != null )
                    {
                        auditDataGenerator.setLoggedInUser ( user );
                        Collection<AuditData> data = auditDataGenerator.generateAuditData ();
                        if ( data != null )
                        {
                            auditDataSet.addAll ( data );
                        }
                    }
                }

                if ( auditDataSet.isEmpty () )
                {
                    log.info( new StringBuilder( 300 ).append( "AAES.buildAuditAlertNotificationEmail : no audit data to send. org=" )
                            .append( org ).append( ",components=" ).append( auditEventsMap.keySet () ).toString() );
                    return null;
                }

                emailAction.putCustomField( BANK_ORG_PARAM, getAuditAlertOrg ( org ).getShortName () );
                String configuredEnvName = _serverMBean.getServerEnvironmentName ();
                boolean validConfiguredEnvName = configuredEnvName != null && !ServerMBean.IDC_SERVER_ENV_NAME_DEFAULT.equals ( configuredEnvName );
                String envName = validConfiguredEnvName ? configuredEnvName : ConfigurationFactory.getServerMBean().getEnvironment ();
                emailAction.putCustomField ( ENV_NAME_PARAM, envName );
                emailAction.putCustomField ( APP_NAME_PARAM, _auditAlertConfig.getAuditAlertEmailContentApplicationName ( org ) );

                ArrayList<String> doNotFormatCustomFields = new ArrayList<String>();
                doNotFormatCustomFields.add( BANK_ORG_PARAM );
                doNotFormatCustomFields.add( ENV_NAME_PARAM );
                doNotFormatCustomFields.add ( APP_NAME_PARAM );
                emailAction.setNonDisplayFormaterKeyList( doNotFormatCustomFields );

                Collection<String> tableColumns = _auditAlertConfig.getAuditAlertEmailContentTableColumns ( org );
                List<String> tableHeaders = new ArrayList<String>();
                for ( String tableColumn: tableColumns )
                {
                    String label = _auditAlertConfig.getAuditAlertEmailContentTableColumnLabel ( tableColumn );
                    tableHeaders.add ( label );
                }

                List<List<String>> eventsInfo = new ArrayList<List<String>>();
                Collections.sort ( auditDataSet, new AuditDataComparator () );
                for ( AuditData auditData: auditDataSet )
                {
                    List<String> rowValues = getAuditEventValues( auditData, tableColumns );
                    eventsInfo.add ( rowValues );
                }

                String body = getAuditEventsSummaryHTML ( tableHeaders, eventsInfo, emailMainContent );
                emailAction.setBody ( body );
                if(_auditAlertConfig.isAuditAlertAttachmentEnabled( org )){
                	setBodyPart(emailAction, tableHeaders, eventsInfo, org);
                }
              
                if ( log.isDebugEnabled() )
                {
                    log.debug( new StringBuilder( 300 ).append( "AAES.buildAuditAlertNotificationEmail : email details. subject=" )
                            .append( subject ).append( ",body=" ).append( body ).toString() );
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "AAES.buildAuditAlertNotificationEmail : Exception. org=" + org, e );
        }
        return emailAction;
    }
    
    private void setBodyPart(SendEmailAction emailAction, List<String> headers, List<List<String>> rowValues, Organization org){
    	try {   
    		String DATE_TIME_FORMAT = "yyyy-MM-dd-HH-mm";
    		SimpleDateFormat format = new SimpleDateFormat(DATE_TIME_FORMAT);
    		format.setTimeZone(DateTimeFactory.newDateTime().getTimeZone());
    		StringBuilder csvFilePath = new StringBuilder();
    		csvFilePath.append("./").append(org.getShortName()).append("-Audit-")
    		.append(format.format(new Date())).append(".csv");
    		
	    	writeAuditEventsToCSV(headers,rowValues,  csvFilePath.toString());
	    	File file = new File(csvFilePath.toString());
	    	DataSource source = new FileDataSource(file);
	    	BodyPart bodyPart = new MimeBodyPart();
	    	bodyPart.setDataHandler(new DataHandler(source));
	    	bodyPart.setFileName(file.getName());
	    	BodyPart[] bodyParts = new BodyPart[1];
	    	bodyParts[0] = bodyPart;
	    	emailAction.setBodyParts(bodyParts);
    	} 
        catch (Exception e) { 
        	log.error( "AAES.setBodyPart : Exception." , e );
        } 
    }
    
    private void writeAuditEventsToCSV(List<String> headers, List<List<String>> rowValues, String csvFilePath){    	
    	File file = new File(csvFilePath); 
        try { 
        	String[] headerArr = new String[headers.size()]; 
	    	headerArr = headers.toArray(headerArr);
	    	log.info("AAES.writeAuditEventsToCSV : Header "+headerArr);
	    	
        	FileWriter fileWriter = new FileWriter(csvFilePath);
        	BufferedWriter writer = new BufferedWriter(fileWriter);   
	    	CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT
                    .withHeader(headerArr));
	    	
	    	for ( List<String> row: rowValues )
	        {
	    		csvPrinter.printRecord(row); 
	        }
	    	csvPrinter.flush(); 
	    	csvPrinter.close();
	    	writer.close();
	    	fileWriter.close();
        } 
        catch (Exception e) { 
        	log.error( "AAES.writeAuditEventsToCSV : Exception." , e );
        } 
    }

    private String getAuditEventsSummaryHTML( List<String> headers, List<List<String>> rowValues, String emailContent)
    {
        StringBuilder mailBody = new StringBuilder();
        mailBody.append("<HTML>");
        mailBody.append(getCSS());
        mailBody.append("<body>");
        mailBody.append ( emailContent );
        mailBody.append ( "<br><br>" );
        mailBody.append("<H3>Audit Events Summary</H3></br></br>");
  
	        try
	        {
	            StringBuilder tableHeader = new StringBuilder();
	            tableHeader.append("<table><tr>" );//<th colspan=\"9\">Trade Date </th></tr>");
	            for ( String header: headers )
	            {
	                tableHeader.append ( "<th>" ).append ( header ).append ( "</th>" );
	            }
	            tableHeader.append ( "</tr>" );
	            mailBody.append ( tableHeader );
	            StringBuilder tableValues = new StringBuilder( 500 );
	            for ( List<String> row: rowValues )
	            {
	                tableValues.append("<tr>");
	                for ( String val: row )
	                {
	                    tableValues.append ( "<td>" ).append ( val ).append ( "</td>" );
	                }
	                tableValues.append("</tr>");
	            }
	            mailBody.append ( tableValues );
	            mailBody.append("</table></br></br>");	  
	            mailBody.append("</body></HTML>");
	        }
	        catch (Exception e)
	        {
	            log.error("Error : " + e);
	        }
                
        return mailBody.toString();
    }

    private static String getCSS()
    {
        StringBuilder css = new StringBuilder( 200 );
        css.append("<style>");
        css.append("table, td, th {");
        css.append("border: 1px solid black");
        css.append("}");
        css.append("table {");
        css.append("width: 80%;");
        css.append("}");
        css.append("th {");
        css.append("height: 50px;");
        css.append("}");
        css.append("tr {background-color: #f2f2f2}");
        css.append("th, td {");
        css.append("text-align: left;");
        css.append("padding: 8px;");
        css.append("}");
        css.append("</style>");
        return css.toString();
    }

    private List<String> getAuditEventValues ( AuditData auditData, Collection<String> tableColumns )
    {
        List<String> attributeValues = new ArrayList<String>();
        for ( String column: tableColumns )
        {
            if ( MODULE_PARAM.equalsIgnoreCase ( column ) )
            {
                attributeValues.add ( auditData.getComponent () );
            }
            else if ( USER_PARAM.equalsIgnoreCase ( column ) )
            {
                attributeValues.add ( auditData.getModifiedby () != null ? auditData.getModifiedby () : auditData.getUser () );
            }
            else if ( USER_ORG_PARAM.equalsIgnoreCase ( column ) )
            {
                attributeValues.add ( auditData.getModifiedByNamespace () );
            }
            else if ( MODIFIED_NAMESPACE_PARAM.equalsIgnoreCase ( column ) )
            {
                attributeValues.add ( auditData.getModifiedNamespace () );
            }
            else if ( DATE_TIME_PARAM.equalsIgnoreCase ( column ) )
            {
                if ( auditData.getEntryDateTime () != null )
                {
                    attributeValues.add ( auditData.getFormattedEntryDateTime ( DATE_TIME_FORMAT ) );
                }
                else
                {
                    attributeValues.add ( AUDIT_EVENT_TABLE_EMPTY_VALUE );
                }

            }
            else if ( DESCRIPTION_PARAM.equalsIgnoreCase ( column ) )
            {
                String description = auditData.getFullDescription ();
                attributeValues.add ( description != null ? description : AUDIT_EVENT_TABLE_EMPTY_VALUE );
            }
            else if ( USER_ACTION_PARAM.equalsIgnoreCase ( column ) )
            {
                attributeValues.add ( auditData.getUserAction () );
            }
        }
        return attributeValues;
    }

    private Organization getAuditAlertOrg( Organization org )
    {
        return org.getBrokerOrganization () != null ? org.getBrokerOrganization () : org.getRealLP () != null ? org.getRealLP () : org;
    }

    private User getUser ( Organization org )
    {
        User orgUser = org.getDefaultDealingUser ();
        if ( orgUser == null )
        {
            log.info ( "AAES.getUser : No default dealing user for org=" + org );
            Collection<User> users = org.getUsers ();
            if ( users != null && !users.isEmpty () )
            {
                orgUser = users.iterator ().next ();
            }
            else
            {
                log.info ( "AAES.getUser : No users found for org=" + org );
            }
        }
        return orgUser;
    }

    void addAuditEvents ( Organization org, Collection<AuditEvent> auditEvents )
    {
        if ( ConfigurationFactory.getServerMBean ().isAuditAlertEnabledServer () )
        {
            synchronized ( auditEventsMap )
            {
                Collection<AuditEvent> eventsList = auditEventsMap.get ( org.getShortName () );
                if ( eventsList == null )
                {
                    eventsList = new ArrayList<AuditEvent> ();
                    auditEventsMap.put ( org.getShortName (), eventsList );
                }
                eventsList.addAll ( auditEvents );
            }
        }
    }

    // utility method to expose for monitoring. to be used in a devapp utility.
    public Map<String, Collection<AuditEvent>> getAuditEventsMap()
    {
        return auditEventsMap;
    }

    public void clear()
    {
        synchronized ( auditEventsMap )
        {
            auditEventsMap.clear();
        }
    }

    synchronized void initAuditAlertPeriodicTaskScheduler ()
    {
        if ( ConfigurationFactory.getServerMBean ().isAuditAlertEnabledServer () )
        {
            if ( auditAlertScheduler != null && ! auditAlertScheduler.isShutdown () )
            {
                auditAlertScheduler.shutdown ();
            }
            AuditAlertNotifierTask auditAlertNotifierTask = new AuditAlertNotifierTask ();
            auditAlertScheduler = Executors.newScheduledThreadPool ( 1, new ThreadFactory ()
            {
                @Override
                public Thread newThread ( Runnable r )
                {
                    return new Thread ( new ThreadGroup ( "AuditAlertEmailSenderGroup" ), r, "AuditAlertEmailSenderTask" );
                }
            } );
            long period = _auditAlertConfig.getAuditAlertEmailNotificationInterval ();
            auditAlertScheduler.scheduleWithFixedDelay ( auditAlertNotifierTask, period, period, TimeUnit.MILLISECONDS );
        }
        else
        {
            log.warn ( "AAES.initAuditAlertPeriodicTaskScheduler : skipping as it is not a trading server." );
        }
    }

    /**
     * Handles any audit events that are not sent as alert. to be used in a shutdown task to send out balance emails.
     */
    public void handlePendingAuditAlerts()
    {
        try
        {
            if ( ConfigurationFactory.getServerMBean ().isAuditAlertEnabledServer () )
            {
                new AuditAlertNotifierTask ().run ();
                log.info ( "AAES.handlePendingAuditAlerts : finished handling pending audit alerts." );
            }
        }
        catch ( Exception e )
        {
            log.warn ( "AAES.handlePendingAuditAlerts : exception while handling pending audit alerts.", e );
        }
    }

    /**
     * This task goes through all the audit events that needs to be consolidated for sending alert notifications.
     */
    class AuditAlertNotifierTask implements Runnable
    {
        public void run()
        {
            try
            {
                Map<String, Collection<AuditEvent>> eventsMap = new HashMap<String, Collection<AuditEvent>> ( );
                synchronized ( auditEventsMap )
                {
                    eventsMap.putAll ( auditEventsMap );
                    auditEventsMap.clear ();
                }

                // handle
                for ( String orgName: eventsMap.keySet () )
                {
                    WorkflowMessage wm = MessageFactory.newWorkflowMessage ();
                    wm.setObject ( eventsMap.get ( orgName ) );
                    AuditAlertEmailSenderC.getInstance ().sendAuditAlertEmail ( wm, ReferenceDataCacheC.getInstance ().getOrganization ( orgName ) );
                }
            }
            catch(Exception ex)
            {
                log.error( "Error in AuditAlertNotifierTask", ex );
            }
        }
    }
}

