package com.integral.admin.services.tradingvenue.mbean;

import com.integral.system.configuration.IdcMBeanC;
import com.integral.tradingvenue.ClobStream;
import com.integral.tradingvenue.FixingDetails;
import com.integral.tradingvenue.PriceSource;
import com.integral.tradingvenue.PriceSourceC;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.StringTokenizer;
import java.util.concurrent.ConcurrentHashMap;

/**
 * User: jainv Date: 10/10/14 Time: 10:33 AM
 */
public class RexCommonMBeanC extends IdcMBeanC implements RexCommonMBean {

	private static RexCommonMBean _instance = new RexCommonMBeanC();

	private static final String DEFAULT_SRC = "FXB";
	private static final String DEFAULT_CLOB_STREAM = "FX|CLOB|";
	private static final Double DEFAULT_REGULAR_SIZE = 1.0;
	private static final String DEFAULT_MDS = "Market_Data_Stream";
	private static final String DEFAULT_CUST_CATEGORY = "DefaultCust";
	private static final Integer DEFAULT_CUST_CATEGORY_VAL = 0;
	private static final Integer DEFAULT_MASK = 1;
	public static final Byte DEFAULT_MASK_BYTE = (byte)1;
	private static final String DEFAULT_REG_SIZE_VENUE_KEY = "Default";
	private static final String DEFAULT_CM_PRIO_KEY = "Default";
	private static final Boolean DEFAULT_MEMBER_REGEN = false;
	private static final Double DEFAULT_MIN_MATCH_QTY = 0.0;
	public static final Integer MAX_CMS_PER_PROVIDER = 100000;
    private Map<String, String> partialFillProviders;
    private Collection<String> superBankMatchEnabledList;

	public static RexCommonMBean getInstance() {
		return _instance;
	}

	private RexCommonMBeanC() {
		super("RexCommonMBean");
	}

	private long matchDelay;
	private long switchInterval;
	private Map<String, PriceSource> priceSourcesByName;
	private Map<String, Map<String, Double>> regularSizeN;
	private Map<String, FixingDetails> fixingDetailsByName;
	private Map<Long, FixingDetails> fixingDetailsById;
	private Map<String, List<ClobStream>> clobStreamsByName;
	private Map<String, List<Integer>> customerCategories;
	private Map<String,Map<Integer,String>> clobStreamsByMask;
	private Map<String, Integer> customerCategoriesMask;
	private Map<String,List<String>> provs;
	private Map<String,Double> provMinMatchQty;
	private Map<String,Double> provRegularSize;
	private Map<String,Boolean> provLiqRegen;
    private boolean allCMLEsAllowed;
    private Map<String,List<String>> cmLePrioForProvider;
	private Map<String,List<String>> ccpPrioForProvider;
	private int supportedSuperBankIndexMaxSize;
	@Override
	public void initialize() {
		super.initialize();
		matchDelay = getLongProperty(MATCHING_START_DELAY, 3000);
		switchInterval = getLongProperty(PRICE_SRC_SWITCH_INTVL, 3000);
		fixingDetailsByName = new HashMap<String, FixingDetails>();
		priceSourcesByName = new HashMap<String, PriceSource>();
		fixingDetailsById = new HashMap<Long, FixingDetails>();
		clobStreamsByName = new HashMap<String, List<ClobStream>>();
		regularSizeN = new HashMap<String, Map<String, Double>>();
		customerCategories = new HashMap<String, List<Integer>>();
		clobStreamsByMask = new HashMap<String, Map<Integer, String>>();
		customerCategoriesMask = new HashMap<String,Integer>();
		initPriceSources();
		initClobStreams();
		initRegularSize();
		initCustomerCategories();
		initMembers();
        allCMLEsAllowed = getBooleanProperty( MATCHING_VENUE_ALL_CM_LEGAL_ENTITIES_ALLOWED, false );
        partialFillProviders = initPartialFillProvidersMap();
        superBankMatchEnabledList = initCommaSperatedPropertyList( VENUE_SUPER_BANK_PROVISIONED_ORGS, null );
		supportedSuperBankIndexMaxSize = getIntProperty(SUPPORTED_SUPERBANK_INDEX_MAXSIZE,1600);
	}

	  private int getCategoryMask(int[] categories) {
		    int category = 0;

		    for (int cat : categories) {
		      category |= 1 << cat;
		    }

		    return category;
		  }
	  
	  
	private void initPriceSources() {
		Properties priceSrcProperties = getPropertiesWithPrefix(PRICE_SOURCES_STRING);
		for (Map.Entry entry : priceSrcProperties.entrySet()) {
			String key = (String) entry.getKey();
			String fixingName = key
					.substring(PRICE_SOURCES_STRING.length() + 1);
			String value = (String) entry.getValue();
			String[] fixingDetailsVals = value.split(",");
			FixingDetails fixingDetails = new FixingDetails();
			PriceSource priceSource = new PriceSourceC();
			fixingDetails.setPriceSource(priceSource);
			String[] primarySrc = fixingDetailsVals[0].split(":");
			String[] secondarySrc = fixingDetailsVals[1].split(":");
			priceSource.setPrimaryOrgName(primarySrc[0]);
			priceSource.setPrimaryStreamName(primarySrc[1]);
			priceSource.setSecondaryOrgName(secondarySrc[0]);
			priceSource.setSecondaryStreamName(secondarySrc[1]);
			priceSource.setName(fixingName);
			priceSourcesByName.put(fixingName, priceSource);
			if (fixingDetailsVals.length > 2) {
				fixingDetails.setFixingName(fixingName);
				fixingDetails.setFixingTime(fixingDetailsVals[4]);
				fixingDetails.setFixingTimeZone(fixingDetailsVals[5]);
				long fixingId = Long.parseLong(fixingDetailsVals[2]);
				fixingDetails.setObjectID(fixingId);
				fixingDetailsById.put(fixingId, fixingDetails);
				priceSourcesByName.remove(fixingName);
				String priceSrcNameForFixing = fixingDetailsVals[3];
				priceSourcesByName.put(priceSrcNameForFixing, priceSource);
				priceSource.setName(priceSrcNameForFixing);
				fixingDetailsByName.put(
						key.substring(PRICE_SOURCES_STRING.length() + 1),
						fixingDetails);
			}
		}

	}
	
	private void initMembers() {
		Properties mems = getPropertiesWithPrefix(REX_EMS_PROVISIONED_MEMBER_ORG_PROPERTY);
		provs =  new HashMap<String,List<String>>();
		if (mems.size() > 0)
			for (Map.Entry entry : mems.entrySet()) {
				String key = (String) entry.getKey();
				String rexName = key.substring(REX_EMS_PROVISIONED_MEMBER_ORG_PROPERTY.length() + 1);
				String value = (String) entry.getValue();
				provs.put(rexName, new ArrayList<String>());
				String[] orgs = value.split(",");
				for(String o1 : orgs)
					provs.get(rexName).add(o1);
			}
		
		Properties regs = getPropertiesWithPrefix(REX_EMS_PROVISIONED_MEMBER_REGULAR_SIZE);
		provRegularSize =  new HashMap<String,Double>();
		if (regs.size() > 0)
			for (Map.Entry entry : regs.entrySet()) {
				String key = (String) entry.getKey();
				if (key.length() > REX_EMS_PROVISIONED_MEMBER_REGULAR_SIZE.length()) {
					String provName = key.substring(REX_EMS_PROVISIONED_MEMBER_REGULAR_SIZE
									.length() + 1);
					Double value = Double.parseDouble((String) entry.getValue());
					provRegularSize.put(provName, value);
				}
			}
		
		Properties minMatch = getPropertiesWithPrefix(REX_EMS_PROVISIONED_MEMBER_MIN_MATCH_QTY);
		provMinMatchQty =  new HashMap<String,Double>();
		if (minMatch.size() > 0)
			for (Map.Entry entry : minMatch.entrySet()) {
				String key = (String) entry.getKey();
				if (key.length() > REX_EMS_PROVISIONED_MEMBER_MIN_MATCH_QTY.length()) {
				String provName = key.substring(REX_EMS_PROVISIONED_MEMBER_MIN_MATCH_QTY.length() + 1);
				Double value = Double.parseDouble((String)entry.getValue());
				provMinMatchQty.put(provName, value);
				}
		}
		
		Properties liqRegen = getPropertiesWithPrefix(REX_EMS_PROVISIONED_MEMBER_REGEN_QUOTES);
		provLiqRegen =  new HashMap<String,Boolean>();
		if (liqRegen.size() > 0)
			for (Map.Entry entry : liqRegen.entrySet()) {
				String key = (String) entry.getKey();
				if (key.length() > REX_EMS_PROVISIONED_MEMBER_REGEN_QUOTES.length()) {

				String provName = key.substring(REX_EMS_PROVISIONED_MEMBER_REGEN_QUOTES.length() + 1);
				Boolean value = Boolean.parseBoolean((String)entry.getValue());
				provLiqRegen.put(provName, value);
				}
		}
		
		cmLePrioForProvider = new HashMap<String,List<String>>();
		Properties cmLeList = getPropertiesWithPrefix(REX_EMS_CM_PRIORITY);
		populateListFromProperties(cmLeList,cmLePrioForProvider,REX_EMS_CM_PRIORITY);

		ccpPrioForProvider = new HashMap<String,List<String>>();
		Properties ccpLeList = getPropertiesWithPrefix(REX_EMS_CCP_PRIORITY);
		populateListFromProperties(ccpLeList,ccpPrioForProvider,REX_EMS_CCP_PRIORITY);


	}
	private void populateListFromProperties(Properties props,Map<String,List<String>> colList,String property){
		if(props.size() > 0){
			for (Map.Entry entry : props.entrySet()) {
				String key = (String) entry.getKey();
				if (key.length() > property.length()) {
					String provName = key.substring(property.length() + 1);
					String value = (String)entry.getValue();
					List<String> valueList = new ArrayList<String>();
					for(String myVal : value.split(","))
					{
						valueList.add(myVal);
					}
					colList.put(provName, valueList);
				}
			}
		}

	}

	private void initRegularSize() {
		Properties regSizeProps = getPropertiesWithPrefix(REX_REGULAR_SIZE);
		if (regSizeProps.size() > 0)
			for (Map.Entry entry : regSizeProps.entrySet()) {
				String key = (String) entry.getKey();
				String rexName = key.substring(REX_REGULAR_SIZE.length() + 1);
				String value = (String) entry.getValue();
				Map<String, Double> regSizeDouble = getPropertySizeMap(value,
						rexName);
				regularSizeN.put(rexName, regSizeDouble);
			}
		;
	}

	private void initCustomerCategories() {
		Properties streamProperties = getPropertiesWithPrefix(CLOB_CUSTOMER_CATEGORIES);
		if (streamProperties.size() > 0)
			for (Map.Entry entry : streamProperties.entrySet()) {
				String key = (String) entry.getKey();
				String custOrgName = key.substring(CLOB_CUSTOMER_CATEGORIES
						.length() + 1);
				String value = (String) entry.getValue();
				String[] clobStreamMaps = value.split(",");
				List<Integer> streams = new ArrayList<Integer>();
				int [] masks = new int[clobStreamMaps.length];
				int i = 0;
				for (String myStr : clobStreamMaps) {
					Integer myCat = Integer.parseInt(myStr);
					streams.add(Integer.parseInt(myStr));
					masks[i++] = myCat;
				}
				customerCategoriesMask.put(custOrgName, getCategoryMask(masks));
				customerCategories.put(custOrgName, streams);
			}
		List<Integer> myStreams = new ArrayList<Integer>();
		myStreams.add(DEFAULT_CUST_CATEGORY_VAL);
		customerCategories.put(DEFAULT_CUST_CATEGORY, myStreams);
		customerCategoriesMask.put(DEFAULT_CUST_CATEGORY, DEFAULT_MASK);
	}

	private void initClobStreams() {
		Properties streamProperties = getPropertiesWithPrefix(CLOB_SUPPORTED_STREAM);
		if (streamProperties.size() > 0)
			for (Map.Entry entry : streamProperties.entrySet()) {
				String key = (String) entry.getKey();
				String rexName = key
						.substring(CLOB_SUPPORTED_STREAM.length() + 1);
				String value = (String) entry.getValue();
				String[] clobStreamMaps = value.split(",");
				List<ClobStream> streams = new ArrayList<ClobStream>();
				Map<Integer,String> clobMask = new HashMap<Integer,String>();
				for (String myStr : clobStreamMaps) {
					ClobStream stream = new ClobStream();
					String[] strStreams = myStr.split("\\|");
					stream.setStreamName(strStreams[0]);
					stream.setStreamIndex(Integer.parseInt(strStreams[1]));
					String[] categories = strStreams[2].split(":");
					
					if (categories.length > 0) {
						int [] masks  = new int[categories.length];
						int i = 0;
						for (String myCat : categories)
							{
							Integer myCat0 = Integer.parseInt(myCat);
							stream.addCategory(myCat0);
							masks[i++] = myCat0;
							}
						clobMask.put(getCategoryMask(masks), strStreams[0]);
					} else {
						stream.addCategory(DEFAULT_CUST_CATEGORY_VAL);
						clobMask.put(DEFAULT_MASK, DEFAULT_MDS);
					}
					streams.add(stream);
				}
				clobStreamsByMask.put(rexName, clobMask);
				clobStreamsByName.put(rexName, streams);
			}

		ClobStream stream = new ClobStream();
		stream.setStreamName(DEFAULT_MDS);
		stream.setStreamIndex(0);
		stream.addCategory(0);
		List<ClobStream> myStreams = new ArrayList<ClobStream>();
		myStreams.add(stream);
		clobStreamsByName.put(DEFAULT_CLOB_STREAM, myStreams);
		Map<Integer,String> clobMask = new HashMap<Integer,String>();
		clobMask.put(DEFAULT_MASK, DEFAULT_MDS);
		clobStreamsByMask.put(DEFAULT_CLOB_STREAM, clobMask);
	}

	public Map<String, PriceSource> getPriceSources() {
		return priceSourcesByName;
	}

	public Map<String, FixingDetails> getFixingDetails() {
		return fixingDetailsByName;
	}

	public PriceSource getPriceSource(String priceSrcName) {
		PriceSource src = priceSourcesByName.get(priceSrcName);
		if (src == null) {
			src = priceSourcesByName.get(DEFAULT_SRC);
		}
		if (src != null) {
			return src;
		}
		return null;
	}

	@Override
	public FixingDetails getFixingDetailsById(long id) {
		return fixingDetailsById.get(id);
	}

	public FixingDetails getFixingDetailsByName(String fixingName) {
		return fixingDetailsByName.get(fixingName);
	}

	public long getSwitchInterval() {
		return switchInterval;
	}

	@Override
	public long getPriceMatchInitialDelay() {
		return matchDelay;
	}

	public String getHostMatchingVenueName(String venueName) {
		return getStringProperty(MATCHING_VENUE_HOST_NAME_PREFIX + venueName,
				"");
	}

	@Override
	public Map<String, List<ClobStream>> getClobStreams() {
		// TODO Auto-generated method stub
		return clobStreamsByName;
	}

	@Override
	public List<ClobStream> getClobStreamByName(String venueName) {
		// TODO Auto-generated method stub
		List<ClobStream> myList = clobStreamsByName.get(venueName);
		if (myList == null)
			myList = clobStreamsByName.get(DEFAULT_CLOB_STREAM);
		return myList;
	}
	
	private Double getDefaultSizeForVenue(String venueName)
	{
		Map<String, Double> myMap = regularSizeN.get(venueName);
		Double mySize = myMap == null ? DEFAULT_REGULAR_SIZE : myMap.get(DEFAULT_REG_SIZE_VENUE_KEY);
		return mySize;
	}

	@Override
	public Double getRegularSize(String venueName, String ccyPair) {
		// TODO Auto-generated method stub
		Map<String, Double> myMap = regularSizeN.get(venueName);
		Double mySizeV = getDefaultSizeForVenue(venueName);
		Double mySize = myMap == null ? mySizeV : myMap
				.get(ccyPair) == null ? mySizeV : myMap
				.get(ccyPair);
		return mySize;
	}

	public static Map<String, Double> getPropertySizeMap(String str,
			String rexName) {
		Map<String, Double> ccyPairSizeMap = new ConcurrentHashMap<String, Double>();
		if (str != null && !str.trim().equals("")) {
			StringTokenizer st = new StringTokenizer(str, "|");
			String key;
			String value;
			int idx;
			ccyPairSizeMap.clear();
			while (st.hasMoreTokens()) {
				String token = st.nextToken().trim();
				idx = token.indexOf('~');
				if (idx != -1) {
					key = token.substring(0, idx).trim();
					value = token.substring(idx + 1).trim();
					if (!key.equals("")) {
						try {
							double amount = Double.parseDouble(value);
							ccyPairSizeMap.put(key, amount);
						} catch (NumberFormatException ex) {
							log.error("tvAdaptorConfig.getCurrencyPairSizeMap : Error in parsing "
									+ str + " for Adaptor->" + rexName);
						}
					}
				}
			}
		}
		return ccyPairSizeMap;
	}

	@Override
	public List<Integer> getCustomerStreams(String cutomerOrgName) {
		List<Integer> myList = customerCategories.get(cutomerOrgName);
		if (myList == null)
			myList = customerCategories.get(DEFAULT_CUST_CATEGORY);
		return myList;
	}

	@Override
	public Integer getCustomerStreamMask(String customerOrgName) {
		// TODO Auto-generated method stub
		return customerCategoriesMask.get(customerOrgName) == null ? DEFAULT_MASK : customerCategoriesMask.get(customerOrgName) ;
	}

	@Override
	public String getClobStreamNameByMask(Integer mask , String venue) {
		// TODO Auto-generated method stub
		if(clobStreamsByMask.containsKey(venue))
		return 
				clobStreamsByMask.get(venue).get(mask);
		else 
		return 
				clobStreamsByMask.get(DEFAULT_CLOB_STREAM).get(DEFAULT_MASK);
			
	}

	@Override
	public List<String> getProvisionedMemberNames(String venueNames) {
		// TODO Auto-generated method stub
		return provs.get(venueNames);
	}

	@Override
	public boolean isRegenQuotes(String providerName) {
		// TODO Auto-generated method stub
		return provLiqRegen.containsKey(providerName) ?
				provLiqRegen.get(providerName) : DEFAULT_MEMBER_REGEN;
	}

	@Override
	public Double getMinMatchAmount(String providerName) {
		// TODO Auto-generated method stub
		return provMinMatchQty.containsKey(providerName) ?
				provMinMatchQty.get(providerName) : DEFAULT_MIN_MATCH_QTY;
	}

	@Override
	public Double getRegularSize(String providerName) {
		// TODO Auto-generated method stub
		return provRegularSize.containsKey(providerName) ?
				provRegularSize.get(providerName) : DEFAULT_REGULAR_SIZE;
	}

    @Override
    public boolean isAllCMLEsAllowed(){
        return allCMLEsAllowed;
    }

    /**
     * Initialises the partial provider map.
     *
     * @return partial providers map
     */
    private Map<String, String> initPartialFillProvidersMap()
    {
        Map<String, String> providers = new HashMap<String, String>();
        try
        {
            String partialFillProvidersStr = getStringProperty(IDC_IS_PARTIAL_FILL_PROVIDERS, "");
            StringTokenizer tokenizer = new StringTokenizer(partialFillProvidersStr, ",");
            while ( tokenizer.hasMoreTokens() )
            {
                String providerName = tokenizer.nextToken();
                providers.put(providerName, providerName);
            }
            log.warn("OMSConfigC.initPartialFillProvidersMap: Partial fill providers list loaded:" + partialFillProvidersStr);
        }
        catch ( Exception e )
        {
            log.error("OMSConfigC.initPartialFillProvidersMap: Error -", e);
        }
        return providers;
    }

    public boolean isPartialFillProvider( String providerName ) {
        return partialFillProviders.get(providerName) != null;
    }

	@Override
	public List<String> getCMLEPriority(String providerName) {
		// TODO Auto-generated method stub
		List<String> myRet = cmLePrioForProvider.get(providerName);
		return myRet == null ? cmLePrioForProvider.get(DEFAULT_CM_PRIO_KEY) : myRet;
	}
	
	public boolean isSuperBankMatchSupperted( String orgName ) {
    	return superBankMatchEnabledList!=null &&superBankMatchEnabledList.contains(orgName);
	}
	public Collection<String> getSuperBankMatchSupportedOrgs() {
		return superBankMatchEnabledList;
	}
	@Override
	public boolean isCmRelationshipCheckDisabled() {
    	return getBooleanProperty(CLOB_DISABLE_CM_RELATIONSHIP_CHECK, false);
	}

	@Override
	public boolean isNewLogicForClobTypePrimeBrokerEnabled() {
    	return getBooleanProperty(CLOB_USE_NEW_LOGIC_FOR_CLOBTYPE_PRIMEBROKER, false);
	}

	@Override
	public boolean isVenueProvisioningQueryFromRdsEnabled() {
    	return getBooleanProperty(CLOB_PROVISIONING_QUERY_FROM_RDS, false);
	}

	@Override
	public boolean isRdsQueryPaginationEnabledForLpProvision() {
    	return getBooleanProperty(RDS_QUERY_PAGINATION_ENABLED_LPPROVISION, true);
	}

	@Override
	public boolean isStreamVisibilityQueryFromAdminEnabled() {
    	return getBooleanProperty(CLOB_STREAM_VISIBILITY_QUERY_FROM_ADMIN, false);
	}

	@Override
	public List<String> getCCPPriority(String memberName) {

		List<String> myRet = ccpPrioForProvider.get(memberName);
		return myRet == null ? ccpPrioForProvider.get(DEFAULT_CM_PRIO_KEY) : myRet;
	}

	public int getSupportedSuperBankIndexMaxSize(){
    	return supportedSuperBankIndexMaxSize;
	}

	
}
