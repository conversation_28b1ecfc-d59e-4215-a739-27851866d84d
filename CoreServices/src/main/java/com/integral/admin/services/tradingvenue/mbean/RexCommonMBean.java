package com.integral.admin.services.tradingvenue.mbean;

import com.integral.tradingvenue.ClobStream;
import com.integral.tradingvenue.FixingDetails;
import com.integral.tradingvenue.PriceSource;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * User: jainv
 * Date: 10/10/14
 * Time: 10:36 AM
 */
public interface RexCommonMBean
{

    public static final String PRICE_SOURCES_STRING = "Idc.Rex.Supported.Price.Source";

    public static final String PRICE_SRC_SWITCH_INTVL = "Idc.Rex.Price.Sources.Switch.Interval";

    public static final String MATCHING_START_DELAY = "Idc.Rex.Price.Match.Initial.Delay";

    public static final String MATCHING_VENUE_HOST_NAME_PREFIX="Idc.MV.Host.Venue.Name.";
    
    public static final String CLOB_SUPPORTED_STREAM = "Idc.Venue.Supported.Streams";

    public static final String CLOB_DISABLE_CM_RELATIONSHIP_CHECK = "Idc.Venue.Disable.CmRelationship.Check";

    public static final String CLOB_USE_NEW_LOGIC_FOR_CLOBTYPE_PRIMEBROKER = "Idc.Venue.Use.New.Logic.For.ClobType.PrimeBroker";

    public static final String CLOB_PROVISIONING_QUERY_FROM_RDS = "Idc.Venue.Provisioning.Query.From.RDS";

    public static final String CLOB_STREAM_VISIBILITY_QUERY_FROM_ADMIN = "Idc.Venue.Stream.Visibility.Query.From.Admin";

    public static final String CLOB_CUSTOMER_CATEGORIES = "Idc.Venue.Customer.Categories";
    
    public static final String REX_REGULAR_SIZE = "Idc.Venue.RegularSize";
    
	public static final String REX_EMS_PROVISIONED_MEMBER_ORG_PROPERTY = "Idc.Rex.Ems.ReferenceData.Supported.Members";

	public static final String REX_EMS_PROVISIONED_MEMBER_REGULAR_SIZE = "Idc.Rex.Ems.Provider.RegularSize";

	public static final String REX_EMS_PROVISIONED_MEMBER_MIN_MATCH_QTY = "Idc.Rex.Ems.Provider.MinMatchQty";

	public static final String REX_EMS_PROVISIONED_MEMBER_REGEN_QUOTES = "Idc.Rex.Ems.Provider.LiquidityRegen";

    public static final String MATCHING_VENUE_ALL_CM_LEGAL_ENTITIES_ALLOWED = "Idc.MV.All.CM.LegalEntities.Allowed";

    public String IDC_IS_PARTIAL_FILL_PROVIDERS = "Idc.IS.PartialFillProviders";
    
    public String REX_EMS_CM_PRIORITY = "Idc.Rex.Ems.CM.Priority";
    public String REX_EMS_CCP_PRIORITY = "Idc.Rex.Ems.CCP.Priority";
    
    public String VENUE_SUPER_BANK_PROVISIONED_ORGS = "Idc.Venue.StreamRelationship.Supported.Orgs";


    public String SUPPORTED_SUPERBANK_INDEX_MAXSIZE = "Idc.MV.Clob.Supported.StreamIndex.MaxSize";
    String RDS_QUERY_PAGINATION_ENABLED_LPPROVISION = "Idc.RDS.Query.Pagination.Enabled.LPProvision";

    public Map<String,PriceSource> getPriceSources();

    public Map<String,FixingDetails> getFixingDetails();

    public PriceSource getPriceSource(String priceSrcName);

    public FixingDetails getFixingDetailsById(long id);

    public FixingDetails getFixingDetailsByName(String fixingName);

    public long getSwitchInterval();

    public long getPriceMatchInitialDelay();

    public String getHostMatchingVenueName(String venueName);
    
    public Map<String,List<ClobStream>> getClobStreams();
    
    public List<ClobStream> getClobStreamByName(String venueName);
    
    public Double getRegularSize(String venueName, String ccyPair);
        
    public List<Integer> getCustomerStreams(String cutomerOrgName);
    
    public Integer getCustomerStreamMask(String cutomerOrgName);
    
    public String getClobStreamNameByMask(Integer mask, String venueName);

    public List<String> getProvisionedMemberNames(String venueNames);
    
    public boolean isRegenQuotes(String providerName);
    
    public Double getMinMatchAmount(String providerName);
    
    public Double getRegularSize(String providerName);

    public boolean isAllCMLEsAllowed();

    public boolean isPartialFillProvider( String providerName );
    
    public boolean isSuperBankMatchSupperted( String orgName );

    public Collection<String> getSuperBankMatchSupportedOrgs();
    
    public List<String> getCMLEPriority(String providerName);

    public boolean isCmRelationshipCheckDisabled();

    public boolean isNewLogicForClobTypePrimeBrokerEnabled();

    public boolean isVenueProvisioningQueryFromRdsEnabled();
    public boolean isRdsQueryPaginationEnabledForLpProvision();

    public boolean isStreamVisibilityQueryFromAdminEnabled();

    public List<String> getCCPPriority(String providerName);
    public int getSupportedSuperBankIndexMaxSize();
}
