package com.integral.userContainer;

// Copyright (c) 2011 Integral Development Corp. All rights reserved.

import com.integral.is.ISCommonConstants;
import com.integral.user.User;

/**
 * This class is used to manage the status of users who are logged in and has chief dealer permissions.
 */
public class Chief<PERSON><PERSON><PERSON>U<PERSON>CounterContainerC extends UserCounterContainerC implements UserCounterContainer
{

    public void decCount( User user, String webApp )
    {
        if ( user == null )
        {
            log.error( "ChiefDealerUserCounterContainerC.decCount.ERROR : User passed is null. Skipping decrement count." );
            return;
        }

        if ( user.hasPermission( ISCommonConstants.CHIEFDEALER_DBVIEW_PERM ) || user.hasPermission( ISCommonConstants.CHIEFDEALER_MPVIEW_PERM ) )
        {
            super.decCount( user, webApp );
        }
        else
        {
            if ( log.isDebugEnabled() )
            {
                log.debug( "ChiefDealerUserCounterContainerC.decCount.debug : User : " + user + " doesn't have CD permision. Skipping decrement count." );
            }
        }
    }

    public void incCount( User user, String webApp )
    {
        if ( user == null )
        {
            log.error( "ChiefDealerUserCounterContainerC.incCount.ERROR : User passed is null. Skipping increment count." );
            return;
        }

        if ( user.hasPermission( ISCommonConstants.CHIEFDEALER_DBVIEW_PERM ) || user.hasPermission( ISCommonConstants.CHIEFDEALER_MPVIEW_PERM ) )
        {
            super.incCount( user, webApp );
        }
        else
        {
            if ( log.isDebugEnabled() )
            {
                log.debug( "ChiefDealerUserCounterContainerC.incCount.debug : User : " + user + " doesn't have CD permision. Skipping increment count." );
            }
        }

    }

}
