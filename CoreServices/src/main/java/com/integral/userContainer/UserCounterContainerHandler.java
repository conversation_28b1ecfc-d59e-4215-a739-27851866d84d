package com.integral.userContainer;

// Copyright (c) 2011 Integral Development Corp. All rights reserved.

import com.integral.user.User;

/**
 * This interface is used to register for handling first user login and last user logout at an org level.
 */
public interface UserCounterContainerHandler
{
    /**
     * Handles the event of first login by any user of an organization. This event will not be triggered for subsequent user
     * logins while at least one user of the org stays logged in.
     *
     * @param key  key
     * @param user user
     */
    public void handleAddFirstUser( String key, User user );

    /**
     * Handles the event of last user logout of an organization. This event will not be triggered for a user logout when at
     * least one user stays logged in for that org.
     *
     * @param key  key
     * @param user user
     */
    public void handleLastUserLogout( String key, User user );

    public void setContainer(UserCounterContainer container);
}
