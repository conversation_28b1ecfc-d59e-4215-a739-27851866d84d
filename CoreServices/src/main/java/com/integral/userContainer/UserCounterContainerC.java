package com.integral.userContainer;

// Copyright (c) 2011 Integral Development Corp. All rights reserved.

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.User;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class UserCounterContainerC implements UserCounterContainer
{
    private static final String WEBAPP_SEPERATOR = "_||_";
    private Map<String, Map<String, User>> _map = new ConcurrentHashMap<String, Map<String, User>>( 10 );
    protected Log log = LogFactory.getLog( this.getClass() );
    private Map<String, UserCounterContainerHandler> handlers = new ConcurrentHashMap<String, UserCounterContainerHandler>();

    public void decCount( User user, String webApp )
    {
        if ( user == null )
        {
            log.error( "UserCounterContainerC.decCount.ERROR : User passed is null. Skipping decrement count." );
            return;
        }

        if ( webApp == null )
        {
            log.error( "UserCounterContainerC.decCount.ERROR : WebApp passed is null. Skipping decrement count." );
            return;
        }

        String key = user.getOrganization().getShortName();
        Map<String, User> counterMap = _map.get( key );
        if ( counterMap == null )
        {
            log.warn( "UserCounterContainerC.decCount.WARN : Trying to decrease count, but count key : " + user.getOrganization().getShortName() + " doesn't exists." );
            return;
        }

        int origSize = counterMap.size();

        User removedUser = counterMap.remove( new StringBuilder( 50 ).append( user.getFullName() ).append( WEBAPP_SEPERATOR ).append( webApp ).toString() );
        if ( removedUser == null )
        {
            log.warn( "UserCounterContainerC.decCount.WARN : User - " + user.getFullName() + " doesn't exists in counter map. Hence not decrementing count." );
        }

        int finalSize = counterMap.size();

        if ( finalSize == 0 && finalSize < origSize )
        {
            _map.remove( key );
            for ( Map.Entry<String, UserCounterContainerHandler> handlerName : handlers.entrySet() )
            {
                UserCounterContainerHandler handler = handlerName.getValue();
                log.warn( "UserCounterContainerC.decCount.INFO : Calling handleLastUserLogout - Handler : " + handlerName.getKey() + ", Key : " + key );
                handler.handleLastUserLogout( key, user );
            }
        }
    }

    public void incCount( User user, String webApp )
    {
        if ( user == null )
        {
            log.error( "UserCounterContainerC.incCount.ERROR : User passed is null. Skipping increment count." );
            return;
        }

        if ( webApp == null )
        {
            log.error( "UserCounterContainerC.decCount.ERROR : WebApp passed is null. Skipping decrement count for User " + user.getFullName() );
            return;
        }

        String key = user.getOrganization().getShortName();
        Map<String, User> counterMap = _map.get( key );
        if ( counterMap == null )
        {
            boolean isFirstUserAdded = false;
            synchronized ( this )
            {
                //making sure countermap is null
                if ( counterMap == null )
                {
                    counterMap = new ConcurrentHashMap<String, User>();
                    _map.put( key, counterMap );
                    isFirstUserAdded = true;
                }
            }

            if ( isFirstUserAdded )
            {
                for ( Map.Entry<String, UserCounterContainerHandler> handlerName : handlers.entrySet() )
                {
                    UserCounterContainerHandler handler = handlerName.getValue();
                    log.warn( "UserCounterContainerC.incCount.INFO : Calling handleAddFirstUser - Handler : " + handlerName.getKey() + ", Key : " + key );
                    handler.handleAddFirstUser( key, user );
                }
            }
        }
        counterMap.put( user.getFullName() + WEBAPP_SEPERATOR + webApp, user );
    }

    
    public void handleAddFirstUser(User user) {
    	String key = user.getOrganization().getShortName();
    	 for ( String handlerName : handlers.keySet() )
         {
             UserCounterContainerHandler handler = handlers.get( handlerName );    
             handler.handleAddFirstUser( key, user );
         }
    }
    
    public int getCount( String key )
    {
        Map<String, User> counterMap = _map.get( key );
        if ( counterMap == null )
        {
            return 0;
        }
        else
        {
            return counterMap.size();
        }
    }

    public final Map<String, Map<String, User>> getInternalMap()
    {
        return _map;
    }

    public void subscribeHandler( String name, UserCounterContainerHandler handler )
    {
        log.warn( "UserCounterContainer.subscribeHandler.INFO - Adding Handler name : " + name + ", handler : " + handler );
        handler.setContainer(this);
        handlers.put( name, handler );
    }

    public void unsubscribeHandler( String name )
    {
        log.warn( "UserCounterContainer.unsubscribeHandler.INFO - Removing Handler name : " + name );
        handlers.remove( name );
    }
}
