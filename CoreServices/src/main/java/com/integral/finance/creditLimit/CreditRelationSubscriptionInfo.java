package com.integral.finance.creditLimit;

// Copyright (c) 2012 Integral Development Corp.  All rights reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.workflow.dealing.DealingLimitCollection;

/**
 * This contains information related to a credit limit subscription for a credit relationship between credit provider legal entity
 * and credit counterparty.
 *
 * <AUTHOR> Development Corporation.
 */
public interface CreditRelationSubscriptionInfo
{
    /**
     * Returns the credit provider legal entity of the subscription
     *
     * @return credit provider legal entity
     */
    public LegalEntity getCreditProviderLegalEntity();

    /**
     * Returns the credit counterparty part of subscription info
     *
     * @return credit counterparty
     */
    public TradingParty getCreditCounterparty();

    /**
     * Returns the dealing limit collection associated with the credit relationship.
     *
     * @return dealing limits
     */
    public DealingLimitCollection getDealingLimitCollection();

    /**
     * Sets the dealing limit collection associated with the credit relationship.
     *
     * @param dlc dealing limits
     */
    public void setDealingLimitCollection( DealingLimitCollection dlc );
}
