package com.integral.finance.creditLimit.handler;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.finance.creditLimit.CreditLimitSubscriptionManagerC;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.finance.creditLimit.admin.CreditAdminServiceLoggerC;
import com.integral.finance.creditLimit.configuration.CreditConfigurationFactory;
import com.integral.finance.creditLimit.quickcheck.CreditLineManagerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;

/**
 * This class is used an end of day service handler which will be executed at the end of the business day.
 * This is used to reset all the credit utilizations which are present in the credit utilization cache.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditUtilizationCacheResetHandlerC implements MessageHandler
{
    protected Log log = LogFactory.getLog( CreditAdminServiceLoggerC.class );

    public Message handle( Message message )
    {
        try
        {
            log.info( "CURH.handle : Resetting all credit utilizations in cache at the end of business day." );

            // remove all the credit utilization cache entries which are older than the current business date.
            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeAllObsoleteCacheEntries();

            if ( CreditConfigurationFactory.getCreditConfigurationMBean().isCreditEndOfDayPreCalculationEnabled () )
            {
                // rebuild currency positions from the next date's position snapshot or mark it as stale.
                CreditUtilizationManagerC.getInstance ().getCreditUtilizationCache ().rebuildAllCreditUtilizationsOptimized ();
            }
            else
            {
                if ( CreditConfigurationFactory.getCreditConfigurationMBean ().isCreditEndOfDayCacheEntryStaleEnabled () )
                {
                    CreditUtilizationManagerC.getInstance ().getCreditUtilizationCache ().setAllCreditUtilizationsStale ();
                }
                else
                {
                    CreditUtilizationManagerC.getInstance ().getCreditUtilizationCache ().rebuildAllCreditUtilizations ();
                }
            }
            CreditLimitSubscriptionManagerC.getInstance().resetLockMap();

            // re-initialize all the tenor coefficients.
            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().updateAllCreditTenorProfiles();

            CreditLineManagerC.getInstance().purgeRolledOverSubscriptionsAtEOD();

        }
        catch ( Exception e )
        {
            log.warn( "CURH.handle : Error resetting credit cache.", e );
        }
        return message;
    }

}

