package com.integral.finance.creditLimit.admin;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.CreditTenorParameters;
import com.integral.finance.creditLimit.CreditTenorProfile;
import com.integral.finance.creditLimit.model.*;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.finance.trade.Tenor;
import com.integral.persistence.Entity;
import com.integral.util.IdcUtilC;
import org.apache.commons.lang.StringUtils;

import com.integral.exception.IdcOptimisticLockException;
import com.integral.exception.IdcRuntimeException;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.audit.CreditLimitAdminAuditManagerC;
import com.integral.finance.creditLimit.functor.CreditRemoteNotificationFunctorServerC;
import com.integral.finance.creditLimit.functor.CreditTenorProfileNotificationFunctorC;
import com.integral.finance.creditLimit.functor.CreditUtilizationCacheResetNotificationFunctorC;
import com.integral.finance.creditLimit.handler.CreditUtilizationRecalculationHandlerC;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.session.IdcTransaction;
import com.integral.user.Organization;

/**
 * Implementation of CreditLimitAdminService which is used to create and maintain the credit related data for
 * the credit provider organization. This service works within the existing transaction and if it is not available, creates
 * new transaction and commit the transaction. Some methods such as setting of netting methodology and changing
 * the credit exposure etc., removes the credit data structure and create new credit limit rules and migrates the existing
 * credit utilization events.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitPFEAdminServiceC extends CreditLimitAdminServiceC
{
    public Double getDefaultLeverageFactor( Organization creditProviderOrg )
    {
        if ( !validateCreditProvider( creditProviderOrg, "getDefaultLeverageFactor" ) )
        {
            return null;
        }
        try
        {
            return CreditUtilC.getCreditLimitRuleSet( creditProviderOrg ).getLeverageFactor();
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.getDefaultLeverageFactor : Error getting default leverage factor for creditProviderOrg=" + creditProviderOrg, e );
        }
        return null;
    }

    public void setDefaultLeverageFactor( Organization creditProviderOrg, Double leverageFactor )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.setDefaultLeverageFactor : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",leverageFactor=" ).append( leverageFactor ).toString() );
        }
        if ( !validateCreditProvider( creditProviderOrg, "setDefaultLeverageFactor" ) )
        {
            return;
        }
        if ( !CreditUtilC.validateLeverageFactor( leverageFactor ) )
        {
            log.warn( "CPAS.setDefaultLeverageFactor : Invalid leverage factor=" + leverageFactor );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_LEVERAGE_FACTOR_ERROR_CODE, leverageFactor );
        }

        IdcTransaction tx = null;
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( creditProviderOrg );
            Double existingFactor = clrs.getLeverageFactor();
            if ( (leverageFactor != null && leverageFactor.equals ( existingFactor )) )
            {
                log.info( new StringBuilder( 200 ).append( "CPAS.setDefaultLeverageFactor : Same as existing leverage factor=" )
                        .append( leverageFactor ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",existingLeverageFactor=" ).append( existingFactor ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitOrgFunctionC.class );

            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setDefaultLeverageFactor : org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",leverageFactor=" ).append( leverageFactor )
                        .append( ",existingLeverageFactor" ).append( existingFactor )
                        .append( ",newTx=" ).append( tx != null ).toString() );
            }

            registeredClrs.setLeverageFactor( leverageFactor );

            // add a commit handler for recalculation of credit utilization.
            addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( ( Collection<CounterpartyCreditLimitRule> ) clrs.getRules(), false ) );

            // audit
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelLeverageFactor( creditProviderOrg, existingFactor, leverageFactor );

            // clear the credit utilization cache after commit
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETLEVERAGEFACTOR.getName() );
            getTransaction().addRemoteFunctor( CreditUtilizationCacheResetNotificationFunctorC.class.getName(), propertiesMap );

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setDefaultLeverageFactor : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setDefaultLeverageFactor : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    public Double getLeverageFactor( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf )
    {
        if ( clsf == null || !validateCreditCounterparty( creditProviderOrg, creditCpty, "getLeverageFactor" ) )
        {
            return null;
        }
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty );
            if ( cptyRule == null )
            {
                log.info( "CPAS.getLeverageFactor : No cpty rule for org=" + creditProviderOrg );
            }

            Collection<CreditLimitRule> rules = new ArrayList<CreditLimitRule>( cptyRule.getChildrenRules() );
            for ( CreditLimitRule clr : rules )
            {
                if ( clsf.isSameAs( clr.getClassification() ) )
                {
                    return clr.getLeverageFactor();
                }
            }
            log.info( "CPAS.getLeverageFactor : No credit limit rule with the clsf=" + clsf );
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( 200 ).append( "CPAS.getLeverageFactor : Error getting leverage factor for creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",clsf=" ).append( clsf ).toString(), e );
        }
        return null;
    }

    public void setLeverageFactor( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, Double leverageFactor )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.setLeverageFactor : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",clsf=" ).append( clsf ).toString() );
        }
        if ( clsf == null )
        {
            log.warn( "CPAS.setLeverageFactor : Parameters not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( !CreditUtilC.validateLeverageFactor( leverageFactor ) )
        {
            log.warn( "CPAS.setLeverageFactor : Invalid leverage factor. factor=" + leverageFactor + ",clsf=" + clsf );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_LEVERAGE_FACTOR_ERROR_CODE, leverageFactor );
        }

        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "setLeverageFactor" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CPAS.setLeverageFactor : No cpty rule. factor=" )
                        .append( leverageFactor ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" ).append( clsf.getShortName() ).toString() );
                return;
            }

            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cptyRule, clsf );
            if ( clr == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CPAS.setLeverageFactor : No credit limit rule found in cpty rule=" )
                        .append( cptyRule ).append( ",creditProviderOrg=" ).append( creditProviderOrg ).append( ",clsf=" ).append( clsf ).toString() );
                return;
            }

            Double existingLeverage = clr.getLeverageFactor();
            if ( leverageFactor != null && existingLeverage != null && leverageFactor.equals( existingLeverage ) )
            {
                log.info( new StringBuilder( 200 ).append( "CPAS.setLeverageFactor : Same as existing leverage factor=" )
                        .append( leverageFactor ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" ).append( clsf.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitOrgFunctionC.class );
            addReadOnlyClass( tx, CreditLimitRuleSetC.class );
            CreditLimitRule registeredClr = ( CreditLimitRule ) clr.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setLeverageFactor : cptyRule=" )
                        .append( cptyRule ).append( ",creditLimitRule=" ).append( clr ).append( ",rules=" ).append( cptyRule.getChildrenRules() )
                        .append( ",new tx=" ).append( tx != null ).toString() );
            }

            // set the calculator
            registeredClr.setLeverageFactor( leverageFactor );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;
            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelLeverageFactor( creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization(), existingLeverage, leverageFactor );

            // add a commit handler for recalculation of credit utilization.
            addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( clr ) );

            // add notification functors for clearing the credit utilization cache.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETLEVERAGEFACTOR.getName() );
            getTransaction().addRemoteFunctor( CreditUtilizationCacheResetNotificationFunctorC.class.getName(), propertiesMap );
            if ( cptyRule.getTradingParty() != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cptyRule.getTradingParty().getGUID() );
            }

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETLEVERAGEFACTOR.getName() );
            if ( remoteFunctor != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, creditCpty.getGUID() );
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CPAS.setLeverageFactor : No remote notification functor registered. event=" + CreditMessageEvent.SETLEVERAGEFACTOR.getName() );
            }
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setLeverageFactor : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" )
                        .append( clsf.getShortName() ).append( ",leverageFactor=" ).append( leverageFactor ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setLeverageFactor : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    public void setCreditTenorProfile( Organization cpo, TradingParty cc, CreditTenorProfile ctp, boolean useDefaultTenorProfile )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : Begin. cpo=" )
                    .append( cpo ).append( ",cc=" ).append( cc ).append( ",tenorProfile=" ).append( ctp ).append( ",useDefault=" )
                    .append( useDefaultTenorProfile ).toString() );
        }

        if ( !validateCreditCounterparty( cpo, cc, "setCreditTenorProfile" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( cc, cpo );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : No cpty rule. ctp=" )
                        .append( ctp ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            CreditTenorProfile existingCtp = cptyRule.getCreditTenorProfile();
            final boolean existingUseDefault = cptyRule.isUseDefaultTenorProfile();
            final boolean defaultSame = useDefaultTenorProfile == existingUseDefault;
            final boolean profileSame = ctp == null && existingCtp == null || ( existingCtp != null && ctp != null && existingCtp.isSameAs( ctp ) );

            if ( defaultSame && profileSame )
            {
                log.info( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : Same as existing values. existingUseDefault=" )
                        .append( existingUseDefault ).append( ",new=" ).append( useDefaultTenorProfile ).append( ",existingCtp=" )
                        .append( existingCtp ).append( ",newCtp=" ).append( ctp ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            CreditLimitRule dailyClr = CreditUtilC.getCreditLimitRule( cptyRule, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
            Entity existingTenorProfileHolder = CreditUtilC.getCreditTenorProfileHolder( cptyRule, dailyClr );

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();
            registeredCclr.setUseDefaultTenorProfile( useDefaultTenorProfile );
            CreditTenorProfile regCtp = ctp != null ? ( CreditTenorProfile ) ctp.getRegisteredObject() : null;
            registeredCclr.setCreditTenorProfile( regCtp );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : Begin. cptyRule=" ).append( cptyRule ).append( ",tenorProfile=" )
                        .append( ctp ).append( ",existingCtp=" ).append( existingCtp ).append( ",existingUseDefault=" ).append( existingUseDefault )
                        .append( ",newUseDefault=" ).append( useDefaultTenorProfile ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            // add notification functors for updating credit tenor profile
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETCREDITTENORPROFILE.getName() );
            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;
            if ( !isCptyOrgLevel )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cc.getGUID() );
            }
            getTransaction().addRemoteFunctor( CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETCREDITTENORPROFILE.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CPAS.setCreditTenorProfile : No remote notification functor registered. event=" + CreditMessageEvent.SETCREDITTENORPROFILE.getName() );
            }

            //audit
            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelCreditTenorProfile( cpo, isCptyOrgLevel, cc, cc.getLegalEntityOrganization(), existingCtp, ctp, useDefaultTenorProfile, existingUseDefault );

            dailyClr = CreditUtilC.getCreditLimitRule( registeredCclr, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
            Entity newTenorProfileHolder = CreditUtilC.getCreditTenorProfileHolder( registeredCclr, dailyClr );
            boolean recalculateDailyCUs = false;
            if ( existingTenorProfileHolder != null && newTenorProfileHolder != null )
            {
                recalculateDailyCUs = !existingTenorProfileHolder.isSameAs( newTenorProfileHolder );
            }
            else if ( existingTenorProfileHolder != null || newTenorProfileHolder != null  )
            {
                recalculateDailyCUs = true;
            }

            if ( recalculateDailyCUs )
            {
                addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( dailyClr ) );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : End. cptyRule=" ).append( cptyRule ).append( ",tenorProfile=" )
                        .append( ctp ).append( ",existingCtp=" ).append( existingCtp ).append( ",existingUseDefault=" ).append( existingUseDefault )
                        .append( ",newUseDefault=" ).append( useDefaultTenorProfile ).append( ",new tx=" ).append( tx != null ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setCreditTenorProfile with default: org=" + cpo.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void setDefaultCreditTenorProfile( Organization cpo, CreditTenorProfile ctp )
    {
        IdcTransaction tx = null;
        if ( !validateCreditProvider( cpo, "setDefaultCreditTenorProfile" ) )
        {
            return;
        }
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( cpo );
            CreditTenorProfile existingCtp = clrs.getCreditTenorProfile();
            if ( ( ctp == null && existingCtp == null ) || ( existingCtp != null && ctp != null && existingCtp.isSameAs( ctp ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : Same as existing tenor profile=" )
                        .append( ctp ).append( ",existing=" ).append( existingCtp ).append( ",cpo=" ).append( cpo.getShortName() ).toString() );
                return;
            }

            Map<String, String> existingDailyPFEMap = getDailyRulesTenorProfileMap( clrs );

            tx = startTransaction( ruleSetTransactionReadOnlyClasses );
            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();
            CreditTenorProfile regCtp = ctp != null ? ( CreditTenorProfile ) ctp.getRegisteredObject() : null;
            registeredClrs.setCreditTenorProfile( regCtp );
            String action = CreditMessageEvent.SETCREDITTENORPROFILE.getName();
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelCreditTenorProfile( cpo, existingCtp, ctp, action );

            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            String eventName = CreditMessageEvent.SETCREDITTENORPROFILE.getName();
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, eventName );
            getTransaction().addRemoteFunctor( CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( eventName );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CPAS.setDefaultCreditTenorProfile : No remote notification functor registered. event=" + eventName );
            }

            Map<String, String> newDailyPFEMap = getDailyRulesTenorProfileMap( registeredClrs );
            for ( Object rule: clrs.getRules() )
            {
                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule) rule;
                if ( cclr.isActive() )
                {
                    CreditLimitRule dailyClr = CreditUtilC.getCreditLimitRule( cclr, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
                    if ( dailyClr != null )
                    {
                        String existingEntry = existingDailyPFEMap.get( dailyClr.getGUID() );
                        String newEntry = newDailyPFEMap.get( dailyClr.getGUID() );
                        if ( newEntry != null && newEntry.equals( existingEntry ) )
                        {
                            continue;
                        }
                        addCreditUtilizationRecalculationCommitHandler(  new CreditUtilizationRecalculationHandlerC( dailyClr ) );
                    }
                }
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setDefaultCreditTenorProfile : org=" + cpo.getShortName() + ",tenorProfile=" + ( null != ctp ? ctp.getShortName() : "NA" ), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void setCreditTenorProfile( Organization cpo, TradingParty cc, CreditTenorProfile ctp )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : Begin. cpo=" )
                    .append( cpo ).append( ",cc=" ).append( cc ).append( ",tenorProfile=" ).append( ctp ).toString() );
        }

        if ( !validateCreditCounterparty( cpo, cc, "setCreditTenorProfile" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( cc, cpo );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : No cpty rule. ctp=" )
                        .append( ctp ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }
            CreditTenorProfile existingCtp = cptyRule.getCreditTenorProfile();

            if ( ( ctp == null && existingCtp == null ) || ( existingCtp != null && ctp != null && existingCtp.isSameAs( ctp ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : Same as existing tenor profile=" )
                        .append( ctp ).append( ",existing=" ).append( existingCtp ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            CreditLimitRule dailyClr = CreditUtilC.getCreditLimitRule( cptyRule, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
            Entity existingTenorProfileHolder = CreditUtilC.getCreditTenorProfileHolder( cptyRule, dailyClr );

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : cptyRule=" ).append( cptyRule ).append( ",tenorProfile=" )
                        .append( ctp ).append( ",existing=" ).append( existingCtp ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            // set the tenor profile
            CreditTenorProfile regCtp = ctp != null ? ( CreditTenorProfile ) ctp.getRegisteredObject() : null;
            registeredCclr.setCreditTenorProfile( regCtp );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelCreditTenorProfile( cpo, isCptyOrgLevel, cc, cc.getLegalEntityOrganization(), existingCtp, ctp );

            // add notification functors for updating credit tenor profile
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETCREDITTENORPROFILE.getName() );
            if(!isCptyOrgLevel)
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cc.getGUID() );

            getTransaction().addRemoteFunctor( CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETCREDITTENORPROFILE.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CPAS.setCreditTenorProfile : No remote notification functor registered. event=" + CreditMessageEvent.SETCREDITTENORPROFILE.getName() );
            }

            dailyClr = CreditUtilC.getCreditLimitRule( registeredCclr, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
            Entity newTenorProfileHolder = CreditUtilC.getCreditTenorProfileHolder( registeredCclr, dailyClr );
            boolean recalculateDailyCUs = false;
            if ( existingTenorProfileHolder != null && newTenorProfileHolder != null )
            {
                recalculateDailyCUs = !existingTenorProfileHolder.isSameAs( newTenorProfileHolder );
            }
            else if ( existingTenorProfileHolder != null || newTenorProfileHolder != null  )
            {
                recalculateDailyCUs = true;
            }

            if ( recalculateDailyCUs )
            {
                addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( dailyClr ) );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : End. cpo=" )
                        .append( cpo.getShortName() ).append( ",cc=" ).append( cc.getShortName() )
                        .append( ",tenorProfile=" ).append( ctp ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setCreditTenorProfile : org=" + cpo.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void setCreditTenorProfile( Organization cpo, TradingParty cc, CreditLimitClassification clsf, CreditTenorProfile ctp )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : Begin. cpo=" )
                    .append( cpo ).append( ",cc=" ).append( cc ).append( ",tenorProfile=" ).append( ctp )
                    .append( ",clsf=" ).append ( clsf ).toString() );
        }

        if ( !validateCreditCounterparty( cpo, cc, "setCreditTenorProfile" ) )
        {
            return;
        }

        if ( clsf == null )
        {
            log.warn( "CPAS.setCreditTenorProfile : Parameters not specified. clsf is null. cpo=" + cpo + ",cc=" + cc + ",ctp=" + ctp );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( cc, cpo );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : No cpty rule. ctp=" )
                        .append( ctp ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }
            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cptyRule, clsf );
            if ( clr == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setCreditTenorProfile : No credit limit rule for cpo=" )
                        .append( cpo ).append( ",cc=" ).append( cc ).append( ",cclr=" )
                        .append( cptyRule ).append( ",creditRules=" ).append( cptyRule.getChildrenRules() ).toString() );
                return;
            }
            CreditTenorProfile existingCtp = clr.getCreditTenorProfile();

            if ( ( ctp == null && existingCtp == null ) || ( existingCtp != null && ctp != null && existingCtp.isSameAs( ctp ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : Same as existing tenor profile=" )
                        .append( ctp ).append( ",existing=" ).append( existingCtp ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).append( ",clsf=" ).append ( clsf ).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CreditLimitRule registeredClr = ( CreditLimitRule ) clr.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : cptyRule=" )
                        .append( cptyRule ).append( ",tenorProfile=" ).append( ctp ).append( ",existing=" )
                        .append( existingCtp ).append( ",new tx=" ).append( tx != null ).append( ",clsf=" )
                        .append ( clsf ).toString() );
            }

            // set the tenor profile
            CreditTenorProfile regCtp = ctp != null ? ( CreditTenorProfile ) ctp.getRegisteredObject() : null;
            registeredClr.setCreditTenorProfile( regCtp );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelCreditLimitRuleLevelCreditTenorProfile( cpo, isCptyOrgLevel, cc, cc.getLegalEntityOrganization(), clsf, existingCtp, ctp );

            // add notification functors for updating credit tenor profile
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETCREDITTENORPROFILE.getName() );
            propertiesMap.put ( CreditLimitConstants.CREDIT_RULE_TYPE_PROPERTY, clsf.getShortName () );
            if( !isCptyOrgLevel )
            {
                propertiesMap.put ( CreditLimit.CREDIT_COUNTERPARTY, cc.getGUID () );
            }

            getTransaction().addRemoteFunctor( CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETCREDITTENORPROFILE.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CPAS.setCreditTenorProfile : No remote notification functor registered. event=" + CreditMessageEvent.SETCREDITTENORPROFILE.getName() );
            }

            if ( CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION.isSameAs( clsf ) )
            {
                addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( clr ) );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : End. cpo=" )
                        .append( cpo.getShortName() ).append( ",cc=" ).append( cc.getShortName() )
                        .append( ",tenorProfile=" ).append( ctp ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setCreditTenorProfile : Exception while setting ctp. cpo=" + cpo.getShortName() + ",cc=" + cc + ",clsf=" + clsf, e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    /**
     * Sets whether to use the default tenor profile specified in the credit provider level.
     *
     * @param cpo                    credit provider
     * @param cc                     credit counterparty
     * @param useDefaultTenorProfile use default tenor profile flag
     */
    public void setUseDefaultTenorProfile( Organization cpo, TradingParty cc, boolean useDefaultTenorProfile )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.setUseDefaultTenorProfile : Begin. cpo=" )
                    .append( cpo ).append( ",cc=" ).append( cc ).append( ",useDefaultTenorProfile=" ).append( useDefaultTenorProfile ).toString() );
        }

        if ( !validateCreditCounterparty( cpo, cc, "setUseDefaultTenorProfile" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( cc, cpo );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CPAS.setUseDefaultTenorProfile : No cpty rule. useDefaultTenorProfile=" )
                        .append( useDefaultTenorProfile ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            final boolean existingUseDefault = cptyRule.isUseDefaultTenorProfile();

            if ( existingUseDefault == useDefaultTenorProfile )
            {
                log.info( new StringBuilder( 200 ).append( "CPAS.setUseDefaultTenorProfile : Same as existing use default tenor profile flag=" )
                        .append( existingUseDefault ).append( ",new=" ).append( useDefaultTenorProfile ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            CreditLimitRule dailyClr = CreditUtilC.getCreditLimitRule( cptyRule, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
            Entity existingTenorProfileHolder = CreditUtilC.getCreditTenorProfileHolder( cptyRule, dailyClr );

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setUseDefaultTenorProfile : cptyRule=" ).append( cptyRule ).append( ",useDefaultTenorProfile=" )
                        .append( useDefaultTenorProfile ).append( ",existing=" ).append( existingUseDefault ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            // set the new flag
            registeredCclr.setUseDefaultTenorProfile( useDefaultTenorProfile );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;
            String event = useDefaultTenorProfile ? CreditMessageEvent.USEDEFAULTTENORPROFILE.getName() : CreditMessageEvent.USECPTYLEVELTENORPROFILE.getName();

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelUseDefaultTenorProfile( cpo, isCptyOrgLevel, cc, cc.getLegalEntityOrganization(), useDefaultTenorProfile );

            // add notification functors for updating exempt currency pairs.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, event );
            if ( cptyRule.getTradingParty() != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cptyRule.getTradingParty().getGUID() );
            }
            getTransaction().addRemoteFunctor( CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( event );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CPAS.setUseDefaultTenorProfile : No remote notification functor registered. event=" + event );
            }

            dailyClr = CreditUtilC.getCreditLimitRule( registeredCclr, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
            Entity newTenorProfileHolder = CreditUtilC.getCreditTenorProfileHolder( registeredCclr, dailyClr );
            boolean recalculateDailyCUs = false;
            if ( existingTenorProfileHolder != null && newTenorProfileHolder != null )
            {
                recalculateDailyCUs = !existingTenorProfileHolder.isSameAs( newTenorProfileHolder );
            }
            else if ( existingTenorProfileHolder != null || newTenorProfileHolder != null  )
            {
                recalculateDailyCUs = true;
            }

            if ( recalculateDailyCUs )
            {
                addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( dailyClr ) );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setUseDefaultTenorProfile : End. cpo=" )
                        .append( cpo.getShortName() ).append( ",cc=" ).append( cc.getShortName() )
                        .append( ",useDefaultTenorProfile=" ).append( useDefaultTenorProfile ).append( " took ms=" )
                        .append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setUseDefaultTenorProfile : org=" + cpo.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }
    public void addCreditTenorProfile( Organization creditProviderOrg, PFEConfigurationProfile pfeConfigurationProfile,CreditTenorProfile creditTenorProfile )
    {
        if ( creditProviderOrg == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.addCreditTenorProfile" ).append( " : Provider Organization not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( creditTenorProfile == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.addCreditTenorProfile" ).append( " : Credit Tenor Profile not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }
        if( null== pfeConfigurationProfile)
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.addCreditTenorProfile" ).append( " : PFE Profile not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.addCreditTenorProfile : Begin. cpo=" )
                    .append( creditProviderOrg ).append( ",tenorProfile=" ).append( creditTenorProfile ).toString() );
        }

        IdcTransaction tx = null;
        try
        {

            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg );
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            removeReadOnlyClass( tx, CreditLimitOrgFunctionC.class );
            removeReadOnlyClass( tx, CreditTenorProfileC.class );
            removeReadOnlyClass( tx, CreditTenorParametersC.class );

            CreditTenorProfile creditTenorProfileClone = ( CreditTenorProfile ) creditTenorProfile.getRegisteredObject(  );
            PFEConfigurationProfile pfeConfig = (PFEConfigurationProfile)pfeConfigurationProfile.getRegisteredObject();
            pfeConfig.setCreditTenorProfile(creditTenorProfileClone);
            CreditLimitOrgFunction creditLimitOrgFunctionClone = ( CreditLimitOrgFunction ) orgFunc.getRegisteredObject(  );
            Collection<CreditTenorParameters> parametersCollection = new ArrayList<CreditTenorParameters>();
            for ( CreditTenorParameters creditTenorParameters : creditTenorProfileClone.getCreditTenorParameters() )
            {
                CreditTenorParameters creditTenorParametersClone = ( CreditTenorParameters ) creditTenorParameters.getRegisteredObject(  );
                creditTenorParametersClone.setOwner( creditTenorProfileClone );
                creditTenorParametersClone.setNamespace( creditTenorProfileClone.getNamespace() );
                parametersCollection.add( creditTenorParametersClone );
            }
            creditTenorProfileClone.setOwner( creditLimitOrgFunctionClone );
            creditTenorProfileClone.setNamespace( creditLimitOrgFunctionClone.getNamespace() );
            creditTenorProfileClone.setCreditTenorParameters( parametersCollection );
            creditLimitOrgFunctionClone.getCreditTenorProfiles().add( creditTenorProfileClone );

            String action = CreditMessageEvent.ADDCREDITTENORPROFILE.getName();
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelCreditTenorProfile( creditProviderOrg, null, creditTenorProfile, action );

            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.addCreditTenorProfile : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }
    public void addCreditTenorProfile( Organization creditProviderOrg, CreditTenorProfile creditTenorProfile )
    {
        if ( creditProviderOrg == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.addCreditTenorProfile" ).append( " : Provider Organization not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( creditTenorProfile == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.addCreditTenorProfile" ).append( " : Credit Tenor Profile not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.addCreditTenorProfile : Begin. cpo=" )
                    .append( creditProviderOrg ).append( ",tenorProfile=" ).append( creditTenorProfile ).toString() );
        }

        IdcTransaction tx = null;
        try
        {

            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg );
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            removeReadOnlyClass( tx, CreditLimitOrgFunctionC.class );
            removeReadOnlyClass( tx, CreditTenorProfileC.class );
            removeReadOnlyClass( tx, CreditTenorParametersC.class );

            CreditTenorProfile creditTenorProfileClone = ( CreditTenorProfile ) creditTenorProfile.getRegisteredObject(  );
            CreditLimitOrgFunction creditLimitOrgFunctionClone = ( CreditLimitOrgFunction ) orgFunc.getRegisteredObject(  );
            Collection<CreditTenorParameters> parametersCollection = new ArrayList<CreditTenorParameters>();
            for ( CreditTenorParameters creditTenorParameters : creditTenorProfileClone.getCreditTenorParameters() )
            {
                CreditTenorParameters creditTenorParametersClone = ( CreditTenorParameters ) creditTenorParameters.getRegisteredObject(  );
                creditTenorParametersClone.setOwner( creditTenorProfileClone );
                creditTenorParametersClone.setNamespace( creditTenorProfileClone.getNamespace() );
                parametersCollection.add( creditTenorParametersClone );
            }
            creditTenorProfileClone.setOwner( creditLimitOrgFunctionClone );
            creditTenorProfileClone.setNamespace( creditLimitOrgFunctionClone.getNamespace() );
            creditTenorProfileClone.setCreditTenorParameters( parametersCollection );
            creditLimitOrgFunctionClone.getCreditTenorProfiles().add( creditTenorProfileClone );

            String action = CreditMessageEvent.ADDCREDITTENORPROFILE.getName();
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelCreditTenorProfile( creditProviderOrg, null, creditTenorProfile, action );

            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.addCreditTenorProfile : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void addPfeConfiguration( Organization creditProviderOrg, PFEConfiguration pfeConfiguration )
    {
        if ( creditProviderOrg == null )
        {
            log.warn( "CPAS.addPfeConfiguration: Provider Organization not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( pfeConfiguration == null )
        {
            log.warn( "CPAS.addPfeConfiguration: PFE configuration not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.addPfeConfiguration : Begin. cpo=" )
                    .append( creditProviderOrg ).append( ",PFE Configuration=" ).append( pfeConfiguration ).toString() );
        }

        IdcTransaction tx = null;
        try
        {
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg );
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            removeReadOnlyClass( tx, CreditLimitOrgFunctionC.class );
            removeReadOnlyClass( tx, CreditTenorProfileC.class );
            removeReadOnlyClass( tx, CreditTenorParametersC.class );
            removeReadOnlyClass( tx, PFEConfigurationC.class );
            removeReadOnlyClass( tx, PFEConfigurationProfileC.class );

            PFEConfiguration pfeConfigurationClone = (PFEConfiguration)pfeConfiguration.getRegisteredObject();
            CreditLimitOrgFunction creditLimitOrgFunctionClone =(CreditLimitOrgFunction)orgFunc.getRegisteredObject();
            List<PFEConfigurationProfile> pfeConfigurationProfiles = new ArrayList<PFEConfigurationProfile>();
            for ( PFEConfigurationProfile pfeConfigurationProfile : pfeConfiguration.getPfeConfigurationProfiles() )
            {
                PFEConfigurationProfile pfeCPClone = (PFEConfigurationProfile)pfeConfigurationProfile.getRegisteredObject(  );
                pfeCPClone.setOwner(pfeConfigurationClone);
                pfeCPClone.setNamespace(pfeConfigurationClone.getNamespace());
                CreditTenorProfile creditTenorProfile = pfeConfigurationProfile.getCreditTenorProfile();
                CreditTenorProfile ctp= orgFunc.getCreditTenorProfile(creditTenorProfile.getShortName());
                if(null==ctp)
                {
                    addCreditTenorProfile(orgFunc.getOrganization(), pfeCPClone, creditTenorProfile);
                }
                else
                {
                    updateCreditTenorProfile(orgFunc.getOrganization(),pfeCPClone,creditTenorProfile);
                }
                pfeConfigurationProfiles.add(pfeCPClone);
            }
            pfeConfigurationClone.setPfeConfigurationProfiles(pfeConfigurationProfiles);
            //-------------  Add the newly added PFE Configuration ------------
            if( null== creditLimitOrgFunctionClone.getPfeConfiguration(pfeConfigurationClone.getShortName()))
            {
                creditLimitOrgFunctionClone.getPfeConfigurations().add(pfeConfigurationClone);
            }

            pfeConfigurationClone.setOwner( creditLimitOrgFunctionClone );
            pfeConfigurationClone.setNamespace( creditLimitOrgFunctionClone.getNamespace() );
            creditLimitOrgFunctionClone.update();

            String action = CreditMessageEvent.ADDPFECONFIGURATION.getName();
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelPFEConfiguration(creditProviderOrg, null, pfeConfiguration, action);

            CreditLimitRuleSet  creditLimitRuleSet = CreditUtilC.getCreditLimitRuleSet(creditProviderOrg);
            if(CreditUtilC.isPFEConfigured(creditLimitRuleSet))
            {
                PFEConfiguration pfeConfgured = creditLimitRuleSet.getPfeConfiguration();
                boolean isChanged = (null!= pfeConfgured && pfeConfgured.getShortName().equals(pfeConfiguration.getShortName()));
                if(isChanged)
                {
                    // add notification functors for updating credit tenor profile
                    HashMap<String, String> propertiesMap = new HashMap<String, String>( 2 );
                    propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
                    propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.ADDPFECONFIGURATION.getName() );
                    getTransaction().addRemoteFunctor( CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap );
                    // Adds the remote notification functor registered for the event.
                    Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.ADDPFECONFIGURATION.getName() );
                    if ( remoteFunctor != null )
                    {
                        getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
                    }
                    else
                    {
                        log.info( "CPAS.addPfeConfiguration : No remote notification functor registered. event=" + CreditMessageEvent.ADDPFECONFIGURATION.getName() );
                    }
                }
            }
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.addPfeConfiguration : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }
    public CreditTenorProfile getCreditTenorProfile( Organization creditProviderOrg,String profileName )
    {
        if ( creditProviderOrg == null ||null==profileName  )
        {
            log.warn( "CPAS.getCreditTenorProfile: Provider Organization or Profile name not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }
        CreditLimitOrgFunction creditLimitOrgFunction= creditProviderOrg.getCreditLimitOrgFunction();
        return creditLimitOrgFunction.getCreditTenorProfile( profileName );
    }

    public PFEConfiguration getPfeConfiguration( Organization creditProviderOrg,String pfeName )
    {
        if ( creditProviderOrg == null ||null==pfeName  )
        {
            log.warn( "CPAS.getPfeConfiguration: Provider Organization or PFE name not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }
        CreditLimitOrgFunction creditLimitOrgFunction= creditProviderOrg.getCreditLimitOrgFunction();
        return creditLimitOrgFunction.getPfeConfiguration( pfeName );
    }

    public void updatePfeConfiguration( Organization creditProviderOrg, PFEConfiguration pfeConfiguration )
    {
        if ( creditProviderOrg == null )
        {
            log.warn( "CPAS.updatePfeConfiguration: Provider Organization not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( pfeConfiguration == null )
        {
            log.warn( "CPAS.updatePfeConfiguration: Credit Tenor Profile not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.updatePfeConfiguration : Begin. cpo=" )
                    .append( creditProviderOrg ).append( ",pfeConfiguration=" ).append( pfeConfiguration ).toString() );
        }

        IdcTransaction tx = null;
        try
        {
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CreditLimitOrgFunction creditLimitOrgFunctionClone = (CreditLimitOrgFunctionC)creditProviderOrg.getCreditLimitOrgFunction().getRegisteredObject();
            PFEConfiguration existingPFE = creditLimitOrgFunctionClone.getPfeConfiguration( pfeConfiguration.getShortName() );
            PFEConfiguration pfeConfigurationClone = ( PFEConfiguration ) existingPFE.getRegisteredObject();

            if ( !StringUtils.equals( pfeConfigurationClone.getDescription(), pfeConfiguration.getDescription() ) )
            {
                pfeConfigurationClone.setDescription( pfeConfiguration.getDescription() );
            }

            Collection<PFEConfigurationProfile> exitingPfeConfigurationProfile =
                    pfeConfigurationClone.getPfeConfigurationProfiles();

            Map<String, PFEConfigurationProfile> existingConfigurationProfiles =
                    new HashMap<String, PFEConfigurationProfile>();

            for ( PFEConfigurationProfile existingPfeConfigurationProfile : exitingPfeConfigurationProfile )
            {
                String key = new StringBuilder().append( existingPfeConfigurationProfile.getCurrencyPairGroup().getShortName() )
                        .append( "_" ).append( existingPfeConfigurationProfile.getSortOrder() ).toString();

                existingConfigurationProfiles.put( key, existingPfeConfigurationProfile );
            }

            for ( PFEConfigurationProfile pfeConfigurationProfile : pfeConfiguration.getPfeConfigurationProfiles() )
            {
                String key = new StringBuilder().append( pfeConfigurationProfile.getCurrencyPairGroup().getShortName() )
                        .append( "_" ).append( pfeConfigurationProfile.getSortOrder() ).toString();

                // Add
                if ( !existingConfigurationProfiles.containsKey( key ) )
                {
                    pfeConfigurationClone.getPfeConfigurationProfiles().add( pfeConfigurationProfile );
                }
                else // Update
                {
                    PFEConfigurationProfile existingProfile = existingConfigurationProfiles.get( key );

                    if ( !StringUtils.equals( pfeConfigurationProfile.getCreditTenorProfile().getShortName(),
                            existingProfile.getCreditTenorProfile().getShortName() ) )
                    {
                        PFEConfigurationProfile pfeConfigurationProfileClone =(PFEConfigurationProfile)existingProfile.getRegisteredObject();
                        pfeConfigurationProfileClone.setCreditTenorProfile(pfeConfigurationProfile.getCreditTenorProfile() );
                    }
                    existingConfigurationProfiles.remove( key );
                }
            }

            // Remove
            for ( Map.Entry<String, PFEConfigurationProfile> deletedConfProfile : existingConfigurationProfiles.entrySet() )
            {
                pfeConfigurationClone.getPfeConfigurationProfiles().remove( deletedConfProfile );
            }

            String action = CreditMessageEvent.UPDATEPFECONFIGURATION.getName();
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelPFEConfiguration( creditProviderOrg, existingPFE, pfeConfiguration, action );

            CreditLimitRuleSet  creditLimitRuleSet = CreditUtilC.getCreditLimitRuleSet(creditProviderOrg);
            if(CreditUtilC.isPFEConfigured(creditLimitRuleSet))
            {
                PFEConfiguration pfeConfgured = creditLimitRuleSet.getPfeConfiguration();
                if(pfeConfgured.getShortName().equals(pfeConfiguration.getShortName()))
                {
                    // add notification functors for updating credit tenor profile
                    HashMap<String, String> propertiesMap = new HashMap<String, String>( 2 );
                    propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
                    propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.UPDATEPFECONFIGURATION.getName() );
                    getTransaction().addRemoteFunctor( CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap );
                    // Adds the remote notification functor registered for the event.
                    Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.UPDATEPFECONFIGURATION.getName() );
                    if ( remoteFunctor != null )
                    {
                        getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
                    }
                    else
                    {
                        log.info( "CPAS.updatePfeConfiguration : No remote notification functor registered. event=" + CreditMessageEvent.UPDATEPFECONFIGURATION.getName() );
                    }
                }
            }
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.updatePfeConfiguration : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void removePfeConfiguration( Organization creditProviderOrg, PFEConfiguration pfeConfiguration )
    {
        removePfeConfiguration( creditProviderOrg, pfeConfiguration, true );
    }

    private void removePfeConfiguration( Organization creditProviderOrg, PFEConfiguration pfeConfiguration, boolean retryOnFailure )
    {
        if ( creditProviderOrg == null )
        {
            log.warn( "CPAS.removePfeConfiguartion: Provider Organization not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( pfeConfiguration == null )
        {
            log.warn( "CPAS.removePfeConfiguartion: Credit Tenor Profile not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.removePfeConfiguartion : Begin. cpo=" )
                    .append( creditProviderOrg ).append( ",pfeConfiguration=" ).append( pfeConfiguration ).toString() );
        }

        IdcTransaction tx = null;
        try
        {
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            removeReadOnlyClass( tx, CreditLimitOrgFunctionC.class );
            removeReadOnlyClass( tx, CreditTenorProfileC.class );
            removeReadOnlyClass( tx, CreditTenorParametersC.class );
            removeReadOnlyClass( tx, PFEConfigurationC.class );
            removeReadOnlyClass( tx, PFEConfigurationProfileC.class );

            CreditLimitOrgFunction orgFunc = ( CreditLimitOrgFunction ) creditProviderOrg.getCreditLimitOrgFunction().getRegisteredObject( );
            PFEConfiguration pfeConfigurationClone = ( PFEConfiguration ) pfeConfiguration.getRegisteredObject(  );
            List<PFEConfiguration> groups = new ArrayList<PFEConfiguration>( orgFunc.getPfeConfigurations() );

            for ( Iterator<PFEConfiguration> iterator = groups.iterator(); iterator.hasNext(); )
            {
                PFEConfiguration pfeConfiguration1 = iterator.next();
                if ( pfeConfiguration1.getObjectID() == pfeConfigurationClone.getObjectID() )
                {
                    tx.getUOW().deleteObject( pfeConfigurationClone );
                    iterator.remove();
                    ReferenceDataCacheC.getInstance().removeEntity( pfeConfigurationClone );
                    log.info( "Removing credit tenor profile " + pfeConfiguration1.getShortName()
                            + " , in organization:" + creditProviderOrg.getShortName() );
                    break;
                }
            }

            orgFunc.setPfeConfigurations( groups );

            String action = CreditMessageEvent.REMOVEPFECONFIGURATION.getName();
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelPFEConfiguration(creditProviderOrg, pfeConfiguration, null, action);
            CreditUtilC.endTransaction( tx );
        }
        catch ( IdcOptimisticLockException ide )
        {
            if ( retryOnFailure )
            {
                CreditUtilC.closeTransaction( tx );
                removePfeConfiguration( creditProviderOrg, pfeConfiguration, false );
            }
            log.warn( new StringBuilder( "CPAS.removePfeConfiguartion : Failed to remove credit tenor profile even " )
                    .append( "after retry " ).append( pfeConfiguration.getShortName() ).append( ",in org=" )
                    .append( creditProviderOrg.getShortName() ).toString(), ide );
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( "CPAS.removePfeConfiguartion : Failed to remove credit tenor profile " )
                    .append( pfeConfiguration.getShortName() ).append( ",in org=" )
                    .append( creditProviderOrg.getShortName() ).toString(), e );

            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void addPfeConfigurationProfile( Organization creditProviderOrg,
                                            PFEConfiguration pfeConfiguration,
                                            Collection<PFEConfigurationProfile> pfeConfigurationProfiles )
    {
        if ( creditProviderOrg == null )
        {
            log.warn( "CPAS.addPfeConfigurationProfile: Provider Organization not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( pfeConfiguration == null )
        {
            log.warn( "CPAS.addPfeConfigurationProfile: PFE configuration not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( pfeConfigurationProfiles == null || pfeConfigurationProfiles.isEmpty() )
        {
            log.warn( "CPAS.addPfeConfigurationProfile: PFE configuration profiles not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.addPfeConfigurationProfile : Begin. cpo=" )
                    .append( creditProviderOrg ).append( ",pfeConfiguration=" ).append( pfeConfiguration )
                    .append( ",number of PFE configuration profiles=" ).append( pfeConfigurationProfiles.size() )
                    .toString() );
        }

        IdcTransaction tx = null;
        try
        {
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            PFEConfiguration pfeConfigurationClone = ( PFEConfiguration ) pfeConfiguration.getRegisteredObject(  );
            for ( PFEConfigurationProfile element : pfeConfigurationProfiles )
            {
                element.setOwner( pfeConfigurationClone );
                element.setNamespace( pfeConfigurationClone.getNamespace() );
            }
            pfeConfigurationClone.getPfeConfigurationProfiles().addAll( pfeConfigurationProfiles );
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.addCreditTenorProfile : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void removePfeConfigurationProfiles( Organization creditProviderOrg,
                                                PFEConfiguration pfeConfiguration,
                                                Collection<PFEConfigurationProfile> pfeConfigurationProfiles )
    {
        if ( creditProviderOrg == null )
        {
            log.warn( "CPAS.removePfeConfigurationProfiles: Provider Organization not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( pfeConfiguration == null )
        {
            log.warn( "CPAS.removePfeConfigurationProfiles: PFE configuration not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( pfeConfigurationProfiles == null || pfeConfigurationProfiles.isEmpty() )
        {
            log.warn( "CPAS.removePfeConfigurationProfiles: PFE configuration profiles not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.removePfeConfigurationProfiles : Begin. cpo=" )
                    .append( creditProviderOrg ).append( ",pfeConfiguration=" ).append( pfeConfiguration )
                    .append( ",number of pfeConfigurationProfiles=" ).append( pfeConfigurationProfiles.size() )
                    .toString() );
        }

        IdcTransaction tx = null;
        try
        {
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            PFEConfiguration pfeConfigurationClone = ( PFEConfiguration ) pfeConfiguration.getRegisteredObject(  );

            Collection<PFEConfigurationProfile> pfeConfigurationProfileCollection =
                    new LinkedHashSet<PFEConfigurationProfile>();

            for ( PFEConfigurationProfile element : pfeConfigurationProfiles )
            {
                PFEConfigurationProfile pfeConfigurationProfileClone = ( PFEConfigurationProfile ) element.getRegisteredObject(  );
                pfeConfigurationProfileCollection.add( pfeConfigurationProfileClone );
            }

            pfeConfiguration.setPfeConfigurationProfiles( pfeConfigurationProfileCollection );
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.removePfeConfigurationProfiles : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void setPFEConfiguration( Organization cpo, PFEConfiguration pfeConfiguration )
    {
        IdcTransaction tx = null;

        if ( !validateCreditProvider( cpo, "setDefaultPfeConfiguration" ) )
        {
            return;
        }

        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( cpo );
            PFEConfiguration existingPfeConfiguration = clrs.getPfeConfiguration();

            if ( ( pfeConfiguration == null && existingPfeConfiguration == null )
                    || ( existingPfeConfiguration != null && pfeConfiguration != null &&
                    existingPfeConfiguration.isSameAs( pfeConfiguration ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CPAS.setCreditTenorProfile : Same as existing tenor profile=" )
                        .append( pfeConfiguration ).append( ",existing=" ).append( existingPfeConfiguration )
                        .append( ",cpo=" ).append( cpo.getShortName() ).toString() );
                return;
            }

            Map<String, String> existingDailyPFEMap = getDailyRulesTenorProfileMap( clrs );

            tx = startTransaction( ruleSetTransactionReadOnlyClasses );

            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();

            PFEConfiguration pfeConfigurationExisting = clrs.getPfeConfiguration();

            PFEConfiguration regPfeConf = ( pfeConfiguration != null ) ?
                    ( PFEConfiguration ) pfeConfiguration.getRegisteredObject() : null;

            registeredClrs.setPfeConfiguration( regPfeConf );

            String action = CreditMessageEvent.SETPFECONFIGURATION.getName();
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelPFEConfiguration( cpo, pfeConfigurationExisting, pfeConfiguration, action );

            if(clrs.isUsePFEConfiguration())
            {
            // add notification functors for updating credit tenor profile
                HashMap<String, String> propertiesMap = new HashMap<String, String>(2);
                propertiesMap.put(CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID());
                propertiesMap.put(CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETPFECONFIGURATION.getName());
                getTransaction().addRemoteFunctor(CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap);
                // Adds the remote notification functor registered for the event.
                Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor(CreditMessageEvent.SETPFECONFIGURATION.getName());
                if (remoteFunctor != null)
                {
                    getTransaction().addRemoteFunctor(remoteFunctor.getName(), propertiesMap);
                }
                else
                {
                    log.info("CPAS.setPFEConfiguration : No remote notification functor registered. event=" + CreditMessageEvent.SETPFECONFIGURATION.getName());
                }
            }

            Map<String, String> newDailyPFEMap = getDailyRulesTenorProfileMap( registeredClrs );
            for ( Object rule: clrs.getRules() )
            {
                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule) rule;
                if ( cclr.isActive() )
                {
                    CreditLimitRule dailyClr = CreditUtilC.getCreditLimitRule( cclr, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
                    if ( dailyClr != null )
                    {
                        String existingEntry = existingDailyPFEMap.get( dailyClr.getGUID() );
                        String newEntry = newDailyPFEMap.get( dailyClr.getGUID() );
                        if ( newEntry != null && newEntry.equals( existingEntry ) )
                        {
                            continue;
                        }
                        addCreditUtilizationRecalculationCommitHandler(  new CreditUtilizationRecalculationHandlerC( dailyClr ) );
                    }
                }
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setDefaultCreditTenorProfile : org=" + cpo.getShortName() + ",tenorProfile=" + ( null != pfeConfiguration ? pfeConfiguration.getShortName() : "NA" ), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void setPFEConfiguration( Organization cpo, TradingParty cc, PFEConfiguration pfeConfiguration )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.setPfeConfiguration : Begin. cpo=" )
                    .append( cpo ).append( ",cc=" ).append( cc ).append( ",pfeConfiguration=" )
                    .append( pfeConfiguration ).toString() );
        }

        if ( !validateCreditCounterparty( cpo, cc, "setPfeConfiguration" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( cc, cpo );

        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule =
                    CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );

            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CPAS.setPfeConfiguration : No cpty rule. pfeConfiguration=" )
                        .append( pfeConfiguration ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            PFEConfiguration existingPfeConfiguration = cptyRule.getPfeConfiguration();

            if ( ( pfeConfiguration == null && existingPfeConfiguration == null )
                    || ( pfeConfiguration != null && existingPfeConfiguration != null
                    && existingPfeConfiguration.isSameAs( pfeConfiguration ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CPAS.setPfeConfiguration : Same as existing PFE configuration=" )
                        .append( pfeConfiguration ).append( ",existing=" ).append( existingPfeConfiguration )
                        .append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            CreditLimitRule dailyClr = CreditUtilC.getCreditLimitRule( cptyRule, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
            Entity existingTenorProfileHolder = CreditUtilC.getCreditTenorProfileHolder( cptyRule, dailyClr );

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );

            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setPfeConfiguration : cptyRule=" ).append( cptyRule )
                        .append( ",pfeConfiguration=" ).append( pfeConfiguration ).append( ",existing=" )
                        .append( existingPfeConfiguration ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            // set the tenor profile
            PFEConfiguration regPfeConfiguration =
                    pfeConfiguration != null ? ( PFEConfiguration ) pfeConfiguration.getRegisteredObject() : null;

            registeredCclr.setPfeConfiguration( regPfeConfiguration );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelPFEConfiguration( cpo, isCptyOrgLevel, cc, cc.getLegalEntityOrganization(), existingPfeConfiguration, pfeConfiguration );

            if(cptyRule.isUsePFEConfiguration())
            {
                // add notification functors for updating credit tenor profile
                HashMap<String, String> propertiesMap = new HashMap<String, String>(2);
                propertiesMap.put(CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID());
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
                propertiesMap.put(CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETPFECONFIGURATION.getName());
                getTransaction().addRemoteFunctor(CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap);
                // Adds the remote notification functor registered for the event.
                Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor(CreditMessageEvent.SETPFECONFIGURATION.getName());
                if (remoteFunctor != null)
                {
                    propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cc.getGUID() );
                    getTransaction().addRemoteFunctor(remoteFunctor.getName(), propertiesMap);
                }
                else
                {
                    log.info("CPAS.setPFEConfiguration : No remote notification functor registered. event=" + CreditMessageEvent.SETPFECONFIGURATION.getName());
                }
            }

            dailyClr = CreditUtilC.getCreditLimitRule( registeredCclr, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
            Entity newTenorProfileHolder = CreditUtilC.getCreditTenorProfileHolder( registeredCclr, dailyClr );
            boolean recalculateDailyCUs = false;
            if ( existingTenorProfileHolder != null && newTenorProfileHolder != null )
            {
                recalculateDailyCUs = !existingTenorProfileHolder.isSameAs( newTenorProfileHolder );
            }
            else if ( existingTenorProfileHolder != null || newTenorProfileHolder != null  )
            {
                recalculateDailyCUs = true;
            }

            if ( recalculateDailyCUs )
            {
                addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( dailyClr ) );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setPfeConfiguration : End. cpo=" )
                        .append( cpo.getShortName() ).append( ",cc=" ).append( cc.getShortName() )
                        .append( ",pfeConfiguration=" ).append( pfeConfiguration ).append( " took ms=" )
                        .append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setPfeConfiguration : org=" + cpo.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void setPFEConfiguration( Organization cpo, TradingParty cc, CreditLimitClassification clsf, PFEConfiguration pfeConfiguration )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.setPfeConfiguration : Begin. cpo=" )
                    .append( cpo ).append( ",cc=" ).append( cc ).append( ",pfeConfiguration=" )
                    .append( pfeConfiguration ).append ( ",clsf=" ).append ( clsf ).toString() );
        }

        if ( !validateCreditCounterparty( cpo, cc, "setPfeConfiguration" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( cc, cpo );

        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule =
                    CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );

            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CPAS.setPfeConfiguration : No cpty rule. pfeConfiguration=" )
                        .append( pfeConfiguration ).append( ",cpo=" ).append( cpo.getShortName() ).append( ",cc=" )
                        .append( cc.getShortName() ).append ( ",clsf=" ).append ( clsf ).toString() );
                return;
            }

            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cptyRule, clsf );
            if ( clr == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setPfeConfiguration : No credit limit rule for cpo=" )
                        .append( cpo ).append( ",cc=" ).append( cc ).append( ",cclr=" )
                        .append( cptyRule ).append( ",creditRules=" ).append( cptyRule.getChildrenRules() ).toString() );
                return;
            }

            PFEConfiguration existingPfeConfiguration = clr.getPfeConfiguration();

            if ( ( pfeConfiguration == null && existingPfeConfiguration == null )
                    || ( pfeConfiguration != null && existingPfeConfiguration != null
                    && existingPfeConfiguration.isSameAs( pfeConfiguration ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CPAS.setPfeConfiguration : Same as existing PFE configuration=" )
                        .append( pfeConfiguration ).append( ",existing=" ).append( existingPfeConfiguration )
                        .append( ",cpo=" ).append( cpo.getShortName() ).append( ",cc=" ).append( cc.getShortName() )
                        .append ( ",clsf=" ).append ( clsf ).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );

            CreditLimitRule registeredClr = ( CreditLimitRule ) clr.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setPfeConfiguration : cptyRule=" ).append( cptyRule )
                        .append( ",pfeConfiguration=" ).append( pfeConfiguration ).append( ",existing=" )
                        .append( existingPfeConfiguration ).append( ",new tx=" ).append( tx != null )
                        .append ( ",clsf=" ).append ( clsf ).toString() );
            }

            // set the tenor profile
            PFEConfiguration regPfeConfiguration =
                    pfeConfiguration != null ? ( PFEConfiguration ) pfeConfiguration.getRegisteredObject() : null;

            registeredClr.setPfeConfiguration( regPfeConfiguration );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelCreditLimitRuleLevelPFEConfiguration( cpo, isCptyOrgLevel, cc, cc.getLegalEntityOrganization(), clsf, existingPfeConfiguration, pfeConfiguration );

            if( clr.isUsePFEConfiguration() )
            {
                // add notification functors for updating credit tenor profile
                HashMap<String, String> propertiesMap = new HashMap<String, String>(2);
                propertiesMap.put(CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID());
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
                propertiesMap.put(CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETPFECONFIGURATION.getName());
                propertiesMap.put ( CreditLimitConstants.CREDIT_RULE_TYPE_PROPERTY, clsf.getShortName () );
                getTransaction().addRemoteFunctor(CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap);
                // Adds the remote notification functor registered for the event.
                Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor(CreditMessageEvent.SETPFECONFIGURATION.getName());
                if (remoteFunctor != null)
                {
                    propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cc.getGUID() );
                    getTransaction().addRemoteFunctor(remoteFunctor.getName(), propertiesMap);
                }
                else
                {
                    log.info( "CPAS.setPFEConfiguration : No remote notification functor registered. event=" + CreditMessageEvent.SETPFECONFIGURATION.getName() );
                }
            }

            if ( CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION.isSameAs( clsf ) )
            {
                addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( clr ) );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setPfeConfiguration : End. cpo=" )
                        .append( cpo.getShortName() ).append( ",cc=" ).append( cc.getShortName() )
                        .append( ",pfeConfiguration=" ).append( pfeConfiguration ).append ( ",clsf=" ).append ( clsf )
                        .append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setPfeConfiguration : org=" + cpo.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void removeCreditTenorProfile( Organization creditProviderOrg, CreditTenorProfile creditTenorProfile )
    {
        removeCreditTenorProfile( creditProviderOrg, creditTenorProfile, true );
    }

    private void removeCreditTenorProfile( Organization creditProviderOrg, CreditTenorProfile creditTenorProfile, boolean retryOnFailure )
    {
        if ( creditProviderOrg == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.removeCreditTenorProfile" ).append( " : Provider Organization not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( creditTenorProfile == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.removeCreditTenorProfile" ).append( " : Credit Tenor Profile not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.removeCreditTenorProfile : Begin. cpo=" )
                    .append( creditProviderOrg ).append( ",tenorProfile=" ).append( creditTenorProfile ).toString() );
        }

        IdcTransaction tx = null;
        try
        {
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            removeReadOnlyClass( tx, CreditLimitOrgFunctionC.class );
            removeReadOnlyClass( tx, CreditTenorProfileC.class );
            removeReadOnlyClass( tx, CreditTenorParametersC.class );
            CreditLimitOrgFunction orgFunc =  CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg );
            orgFunc = ( CreditLimitOrgFunction ) orgFunc.getRegisteredObject( );
            CreditTenorProfile creditTenorProfileClone = ( CreditTenorProfile ) creditTenorProfile.getRegisteredObject( );

            List<CreditTenorProfile> groups = new ArrayList<CreditTenorProfile>( orgFunc.getCreditTenorProfiles() );
            for ( Iterator<CreditTenorProfile> iterator = groups.iterator(); iterator.hasNext(); )
            {
                CreditTenorProfile creditTenorProfile1 = iterator.next();
                if ( creditTenorProfile1.getObjectID() == creditTenorProfileClone.getObjectID() )
                {
                    tx.getUOW().deleteObject( creditTenorProfileClone );
                    iterator.remove();
                    ReferenceDataCacheC.getInstance().removeEntity( creditTenorProfileClone );
                    log.info( "Removing credit tenor profile " + creditTenorProfile1.getShortName() + " , in organization:" + creditProviderOrg.getShortName() );
                }
            }
            orgFunc.setCreditTenorProfiles( groups );
            String action = CreditMessageEvent.REMOVECREDITTENORPROFILE.getName();
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelCreditTenorProfile( creditProviderOrg, creditTenorProfile, null, action );
            CreditUtilC.endTransaction( tx );
        }
        catch ( IdcOptimisticLockException ide )
        {
            if ( retryOnFailure )
            {
                CreditUtilC.closeTransaction( tx );
                removeCreditTenorProfile( creditProviderOrg, creditTenorProfile, false );
            }
            log.warn( new StringBuilder( "CPAS.removeCreditTenorProfile : Failed to remove credit tenor profile even after retry " ).append( creditTenorProfile.getShortName() ).append( ",in org=" ).append( creditProviderOrg.getShortName() ).toString(), ide );
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( "CPAS.removeCreditTenorProfile : Failed to remove credit tenor profile " ).append( creditTenorProfile.getShortName() ).append( ",in org=" ).append( creditProviderOrg.getShortName() ).toString(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void addCreditTenorParameters( Organization creditProviderOrg, CreditTenorProfile creditTenorProfile, Collection<CreditTenorParameters> creditTenorParameters )
    {
        if ( creditProviderOrg == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.addCreditTenorParameters" ).append( " : Provider Organization not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( creditTenorProfile == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.addCreditTenorParameters" ).append( " : Credit Tenor Profile not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( creditTenorParameters == null || creditTenorParameters.isEmpty() )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.addCreditTenorParameters" ).append( " : Credit Tenor Parameters not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.addCreditTenorParameters : Begin. cpo=" )
                    .append( creditProviderOrg ).append( ",creditTenorProfile=" ).append( creditTenorProfile )
                    .append( creditProviderOrg ).append( ",number of creditTenorParameters=" ).append( creditTenorParameters.size() )
                    .toString() );
        }

        IdcTransaction tx = null;
        try
        {
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg );
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CreditTenorProfile creditTenorProfileClone = ( CreditTenorProfile ) creditTenorProfile.getRegisteredObject(  );
            for ( CreditTenorParameters element : creditTenorParameters )
            {
                element.setOwner( creditTenorProfileClone );
                element.setNamespace( creditTenorProfileClone.getNamespace() );
            }
            creditTenorProfileClone.getCreditTenorParameters().addAll( creditTenorParameters );
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.addCreditTenorProfile : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void removeCreditTenorParameters( Organization creditProviderOrg, CreditTenorProfile creditTenorProfile, Collection<CreditTenorParameters> creditTenorParameters )
    {
        if ( creditProviderOrg == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.addCreditTenorParameters" ).append( " : Provider Organization not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( creditTenorProfile == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.addCreditTenorParameters" ).append( " : Credit Tenor Profile not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( creditTenorParameters == null || creditTenorParameters.isEmpty() )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.addCreditTenorParameters" ).append( " : Credit Tenor Parameters not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.addCreditTenorParameters : Begin. cpo=" )
                    .append( creditProviderOrg ).append( ",creditTenorProfile=" ).append( creditTenorProfile )
                    .append( creditProviderOrg ).append( ",number of creditTenorParameters=" ).append( creditTenorParameters.size() )
                    .toString() );
        }

        IdcTransaction tx = null;
        try
        {
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg );
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CreditTenorProfile creditTenorProfileClone = ( CreditTenorProfile ) creditTenorProfile.getRegisteredObject(  );
            Collection<CreditTenorParameters> creditTenorParametersCollection = new LinkedHashSet<CreditTenorParameters>();
            for ( CreditTenorParameters element : creditTenorParameters )
            {
                CreditTenorParameters creditTenorParametersClone = ( CreditTenorParameters ) element.getRegisteredObject(  );
                creditTenorParametersCollection.add( creditTenorParametersClone );
            }
            creditTenorProfileClone.setCreditTenorParameters( creditTenorParametersCollection );
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.addCreditTenorProfile : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void setPFEExcludeForDailyExposure( Organization creditProviderOrg, int pFEExcludeForDailyExposure )
    {
        IdcTransaction tx = null;
        if ( !validateCreditProvider( creditProviderOrg, "setPFEExcludeForDailyExposure" ) )
        {
            return;
        }
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( creditProviderOrg );
            Integer existingPFEExcludeForDailyExposure = clrs.getPFEExcludeForDailyExposure();

            if ( pFEExcludeForDailyExposure == existingPFEExcludeForDailyExposure )
            {
                log.info( new StringBuilder( 200 ).append( "CPAS.setPFEExcludeForDailyExposure : Same as existing PFE mode=" )
                        .append( existingPFEExcludeForDailyExposure ).append( ",new=" ).append( pFEExcludeForDailyExposure ).append( ",cpo=" ).append( creditProviderOrg.getShortName() ).toString() );
                return;
            }

            Map<String, String> existingDailyPFEMap = getDailyRulesTenorProfileMap( clrs );
            tx = startTransaction( ruleSetTransactionReadOnlyClasses );
            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();
            registeredClrs.setPFEExcludeForDailyExposure( pFEExcludeForDailyExposure );
            String action = CreditMessageEvent.SETCREDITPFEMODE.getName();
            // TODO: Audit
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelCreditPFEMode( creditProviderOrg, existingPFEExcludeForDailyExposure, pFEExcludeForDailyExposure, action );

            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            String eventName = CreditMessageEvent.SETCREDITPFEMODE.getName();
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, eventName );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( eventName );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CPAS.setPFEExcludeForDailyExposure : No remote notification functor registered. event=" + eventName );
            }

            Map<String, String> newDailyPFEMap = getDailyRulesTenorProfileMap( registeredClrs );
            for ( Object rule: clrs.getRules() )
            {
                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule) rule;
                if ( cclr.isActive() )
                {
                    CreditLimitRule dailyClr = CreditUtilC.getCreditLimitRule( cclr, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
                    if ( dailyClr != null )
                    {
                        String existingEntry = existingDailyPFEMap.get( dailyClr.getGUID() );
                        String newEntry = newDailyPFEMap.get( dailyClr.getGUID() );
                        if ( newEntry != null && newEntry.equals( existingEntry ) )
                        {
                            continue;
                        }
                        addCreditUtilizationRecalculationCommitHandler(  new CreditUtilizationRecalculationHandlerC( dailyClr ) );
                    }
                }
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setDailyPFEMode : org=" + creditProviderOrg.getShortName() + ",PFE Mode=" + pFEExcludeForDailyExposure, e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void setPFEExcludeForDailyExposure( Organization cpo, TradingParty cc, int pFEExcludeForDailyExposure )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.setPFEExcludeForDailyExposure : Begin. cpo=" )
                    .append( cpo ).append( ",cc=" ).append( cc ).append( ",pFEExcludeForDailyExposure=" ).append( pFEExcludeForDailyExposure ).toString() );
        }

        if ( !validateCreditCounterparty( cpo, cc, "setPFEExcludeForDailyExposure" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( cc, cpo );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CPAS.setPFEExcludeForDailyExposure : No cpty rule. pFEExcludeForDailyExposure=" )
                        .append( pFEExcludeForDailyExposure ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }
            Integer existingPFEExcludeForDailyExposure = cptyRule.getPFEExcludeForDailyExposure();
            if ( existingPFEExcludeForDailyExposure != null && existingPFEExcludeForDailyExposure == pFEExcludeForDailyExposure )
            {
                log.info( new StringBuilder( 200 ).append( "CPAS.setPFEExcludeForDailyExposure : Same as existing pFEExcludeForDailyExposure=" )
                        .append( existingPFEExcludeForDailyExposure ).append( ",new PFEExcludeForDailyExposure=" ).append( pFEExcludeForDailyExposure ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            CreditLimitRule dailyClr = CreditUtilC.getCreditLimitRule( cptyRule, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
            Entity existingTenorProfileHolder = CreditUtilC.getCreditTenorProfileHolder( cptyRule, dailyClr );

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setPFEExcludeForDailyExposure : cptyRule=" ).append( cptyRule ).append( ",PFEExcludeForDailyExposure=" )
                        .append( pFEExcludeForDailyExposure ).append( ",existing pFEExcludeForDailyExposure=" ).append( existingPFEExcludeForDailyExposure ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            registeredCclr.setPFEExcludeForDailyExposure( pFEExcludeForDailyExposure );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelCreditPFEMode( cpo, isCptyOrgLevel, cc, cc.getLegalEntityOrganization(), existingPFEExcludeForDailyExposure, pFEExcludeForDailyExposure );

            // add notification functors for setting the mode
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETCREDITPFEMODE.getName() );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETCREDITPFEMODE.getName() );
            if ( remoteFunctor != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cc.getGUID() );
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CPAS.setPFEExcludeForDailyExposure : No remote notification functor registered. event=" + CreditMessageEvent.SETCREDITPFEMODE.getName() );
            }

            dailyClr = CreditUtilC.getCreditLimitRule( registeredCclr, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
            Entity newTenorProfileHolder = CreditUtilC.getCreditTenorProfileHolder( registeredCclr, dailyClr );
            boolean recalculateDailyCUs = false;
            if ( existingTenorProfileHolder != null && newTenorProfileHolder != null )
            {
                recalculateDailyCUs = !existingTenorProfileHolder.isSameAs( newTenorProfileHolder );
            }
            else if ( existingTenorProfileHolder != null || newTenorProfileHolder != null  )
            {
                recalculateDailyCUs = true;
            }

            if ( recalculateDailyCUs )
            {
                addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( dailyClr ) );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CPAS.setPFEExcludeForDailyExposure : End. cpo=" )
                        .append( cpo.getShortName() ).append( ",cc=" ).append( cc.getShortName() )
                        .append( ",PFEExcludeForDailyExposure=" ).append( pFEExcludeForDailyExposure ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setPFEExcludeForDailyExposure : org=" + cpo.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void updateCreditTenorProfile( Organization creditProviderOrg, CreditTenorProfile creditTenorProfile )
    {
        if ( creditProviderOrg == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.updateCreditTenorProfile" ).append( " : Provider Organization not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( creditTenorProfile == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.updateCreditTenorProfile" ).append( " : Credit Tenor Profile not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.updateCreditTenorProfile : Begin. cpo=" )
                    .append( creditProviderOrg ).append( ",tenorProfile=" ).append( creditTenorProfile ).toString() );
        }

        IdcTransaction tx = null;
        try
        {
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CreditLimitOrgFunction creditLimitOrgFunctionClone = ( CreditLimitOrgFunctionC ) creditProviderOrg.getCreditLimitOrgFunction().getRegisteredObject();
            CreditTenorProfile existingProfile = creditLimitOrgFunctionClone.getCreditTenorProfile( creditTenorProfile.getShortName() );
            CreditTenorProfile creditTenorProfileClone = ( CreditTenorProfile ) creditLimitOrgFunctionClone.getCreditTenorProfile( creditTenorProfile.getShortName()).getRegisteredObject();

            if ( !StringUtils.equals( creditTenorProfileClone.getDescription(), creditTenorProfile.getDescription() ) )
            {
                creditTenorProfileClone.setDescription( creditTenorProfile.getDescription() );
            }

            double existingGrossSpreadMargin = existingProfile.getGrossPositionSpreadMargin ();
            creditTenorProfileClone.setGrossPositionSpreadMargin ( creditTenorProfile.getGrossPositionSpreadMargin () );

            Map<String, CreditTenorParameters> inputTenorList = new LinkedHashMap<String, CreditTenorParameters>();
            Map<String, CreditTenorParameters> existingTenorList = new LinkedHashMap<String, CreditTenorParameters>();

            for ( CreditTenorParameters element : creditTenorProfile.getCreditTenorParameters() )
            {
                inputTenorList.put( element.getTenor().getName(), element );
            }

            for ( CreditTenorParameters element : existingProfile.getCreditTenorParameters() )
            {
                existingTenorList.put( element.getTenor().getName(), element );
            }


            //Add
            for ( CreditTenorParameters inputElement : inputTenorList.values() )
            {
                if ( !existingTenorList.containsKey( inputElement.getTenor().getName() ) )
                {
                    creditTenorProfileClone.getCreditTenorParameters().add( inputElement );
                }
            }

            // Update & Remove
            for ( CreditTenorParameters existingElement : existingProfile.getCreditTenorParameters() )
            {
                if ( inputTenorList.containsKey( existingElement.getTenor().getName() ) )
                {
                    CreditTenorParameters inputElement = inputTenorList.get( existingElement.getTenor().getName() );
                    if ( inputElement.getUtilizationPercent() != existingElement.getUtilizationPercent() )
                    {
                        CreditTenorParameters creditTenorParametersClone = ( CreditTenorParameters ) existingElement.getRegisteredObject(  );
                        creditTenorParametersClone.setUtilizationPercent( inputElement.getUtilizationPercent() );
                    }
                }
                else
                {
                    creditTenorProfileClone.getCreditTenorParameters().remove( existingElement );
                }
            }
            // add notification functors for updating credit tenor profile
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            String eventName = CreditMessageEvent.UPDATECREDITTENORPROFILE.getName();
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY_ENTITY_SHORTNAME, creditTenorProfile.getShortName() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, eventName );
            getTransaction().addRemoteFunctor( CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( eventName );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CPAS.updateCreditTenorProfile : No remote notification functor registered. event=" + eventName );
            }
            String action = CreditMessageEvent.UPDATECREDITTENORPROFILE.getName();
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelCreditTenorProfile( creditProviderOrg, creditTenorProfile, null, action );
            if ( existingGrossSpreadMargin != creditTenorProfile.getGrossPositionSpreadMargin () )
            {
                CreditLimitAdminAuditManagerC.getInstance().auditUpdateGrossSpreadMargin ( creditProviderOrg, creditTenorProfile, existingGrossSpreadMargin, creditTenorProfile.getGrossPositionSpreadMargin () );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.updateCreditTenorProfile : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void updateCreditTenorProfile( Organization creditProviderOrg, PFEConfigurationProfile pfeConfigurationProfile,CreditTenorProfile creditTenorProfile )
    {
        if ( creditProviderOrg == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.updateCreditTenorProfile" ).append( " : Provider Organization not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( creditTenorProfile == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.updateCreditTenorProfile" ).append( " : Credit Tenor Profile not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }


        if ( pfeConfigurationProfile == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CPAS.updateCreditTenorProfile" ).append( " : PFE Configuration Profile Profile not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.updateCreditTenorProfile : Begin. cpo=" )
                    .append( creditProviderOrg ).append( ",tenorProfile=" ).append( creditTenorProfile ).toString() );
        }

        IdcTransaction tx = null;
        try
        {
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CreditLimitOrgFunction creditLimitOrgFunctionClone = ( CreditLimitOrgFunctionC ) creditProviderOrg.getCreditLimitOrgFunction().getRegisteredObject();
            PFEConfigurationProfile pfeConfig = (PFEConfigurationProfile)pfeConfigurationProfile.getRegisteredObject();
            CreditTenorProfile creditTenorProfileClone = (CreditTenorProfile)creditTenorProfile.getRegisteredObject();
            pfeConfig.setCreditTenorProfile(creditTenorProfileClone);
            Collection<CreditTenorParameters> creditTenorParameterses = creditTenorProfileClone.getCreditTenorParameters();
            List<CreditTenorParameters>  parametersList = new ArrayList<CreditTenorParameters>();
            for(CreditTenorParameters creditTenorParameters:creditTenorParameterses)
            {
                parametersList.add((CreditTenorParameters) creditTenorParameters.getRegisteredObject());
            }
            creditTenorProfileClone.setCreditTenorParameters(parametersList);

            // some other api is invoking tx hence the started tx should register for notifiction
            if(null==tx)
            {
                // add notification functors for updating credit tenor profile
                HashMap<String, String> propertiesMap = new HashMap<String, String>(3);
                propertiesMap.put(CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID());
                String eventName = CreditMessageEvent.UPDATECREDITTENORPROFILE.getName();
                propertiesMap.put(CreditLimitConstants.EVENT_PROPERTY, eventName);
                getTransaction().addRemoteFunctor(CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap);

                // Adds the remote notification functor registered for the event.
                Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor(eventName);
                if (remoteFunctor != null) {
                    getTransaction().addRemoteFunctor(remoteFunctor.getName(), propertiesMap);
                } else {
                    log.info("CPAS.updateCreditTenorProfile : No remote notification functor registered. event=" + eventName);
                }
            }
            String action = CreditMessageEvent.UPDATECREDITTENORPROFILE.getName();
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelCreditTenorProfile( creditProviderOrg, creditTenorProfile, null, action );

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.updateCreditTenorProfile : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }


    /**
     * Enable to use PFE Configuration instead of Credit Tenor Profile
     * for the combination for Credit Provider / Cpty Org
     * @param cpo credit provider
     * @param usePFEConfig use PFE config
     */
    public void setUsePFEConfiguration( Organization cpo,  boolean usePFEConfig )
    {
        IdcTransaction tx = null;
        if ( !validateCreditProvider( cpo, "setUsePFEConfiguration" ) )
        {
            return;
        }
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( cpo );
            boolean isUsePFE = clrs.isUsePFEConfiguration();
            if ( isUsePFE == usePFEConfig)
            {
                log.info(new StringBuilder(200).append("CPAS.setUsePFEConfiguration : No change for UsePFEConfig at Provider =")
                        .append(",existing=").append(isUsePFE).append(",cpo=").append(cpo.getShortName()).toString());
                return;
            }

            Map<String, String> existingDailyPFEMap = getDailyRulesTenorProfileMap( clrs );

            tx = startTransaction( ruleSetTransactionReadOnlyClasses );
            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();
            registeredClrs.setUsePFEConfiguration(usePFEConfig);
            String action = CreditMessageEvent.SETUSEPFECONFIGURATION.getName();
            CreditLimitAdminAuditManagerC.getInstance().auditUsePFE(cpo, isUsePFE, usePFEConfig);

            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            String eventName = CreditMessageEvent.SETUSEPFECONFIGURATION.getName();
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, eventName );
            getTransaction().addRemoteFunctor( CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( eventName );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CPAS.setUsePFEConfiguration : No remote notification functor registered. event=" + eventName );
            }

            Map<String, String> newDailyPFEMap = getDailyRulesTenorProfileMap( registeredClrs );
            for ( Object rule: clrs.getRules() )
            {
                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule) rule;
                if ( cclr.isActive() )
                {
                    CreditLimitRule dailyClr = CreditUtilC.getCreditLimitRule( cclr, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
                    if ( dailyClr != null )
                    {
                        String existingEntry = existingDailyPFEMap.get( dailyClr.getGUID() );
                        String newEntry = newDailyPFEMap.get( dailyClr.getGUID() );
                        if ( newEntry != null && newEntry.equals( existingEntry ) )
                        {
                            continue;
                        }
                        addCreditUtilizationRecalculationCommitHandler(  new CreditUtilizationRecalculationHandlerC( dailyClr ) );
                    }
                }
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setUsePFEConfiguration : org=" + cpo.getShortName() + ",usePFEConfig=" +  usePFEConfig , e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    /**
     * Enable to use PFE Configuration instead of Credit Tenor Profile
     * for the combination for Credit Provider / Cpty Org
     * @param cpo credit provider
     * @param ctp tenor profile
     * @param usePFEConfig use PFE config
     */
    public void setUsePFEConfiguration( Organization cpo, TradingParty ctp, boolean usePFEConfig )
    {
        IdcTransaction tx = null;
        if ( !validateCreditProvider( cpo, "setUsePFEConfiguration" ) )
        {
            return;
        }
        try
        {
            TradingParty creditTp = CounterpartyUtilC.getTradingParty( ctp, cpo );
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );
            boolean isUsePFE = cptyRule.isUsePFEConfiguration();
            if ( isUsePFE == usePFEConfig)
            {
                log.info( new StringBuilder( 200 ).append( "CPAS.setUsePFEConfiguration : No change for UsePFEConfig =" )
                        .append( ctp ).append( ",existing=" ).append( isUsePFE ).append( ",cpo=" ).append( cpo.getShortName() ).toString() );
                return;
            }
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredClrs = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();
            registeredClrs.setUsePFEConfiguration(usePFEConfig );
            String action = CreditMessageEvent.SETUSEPFECONFIGURATION.getName();
            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            CreditLimitAdminAuditManagerC.getInstance().auditUsePFE( cpo, isCptyOrgLevel, ctp, cptyRule.getTradingPartyOrganization (), isUsePFE, usePFEConfig );

            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            String eventName = CreditMessageEvent.SETUSEPFECONFIGURATION.getName();
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, eventName );
            getTransaction().addRemoteFunctor( CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( eventName );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CPAS.setUsePFEConfiguration : No remote notification functor registered. event=" + eventName );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setUsePFEConfiguration : org=" + cpo.getShortName() + ",usePFEConfig=" +  usePFEConfig , e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    /**
     * Enable to use PFE Configuration instead of Credit Tenor Profile
     * for the combination for Credit Provider / Cpty Org
     * @param cpo credit provider
     * @param clsf credit type
     * @param ctp tenor profile
     * @param usePFEConfig use PFE config
     */
    public void setUsePFEConfiguration( Organization cpo, TradingParty ctp, CreditLimitClassification clsf, boolean usePFEConfig )
    {
        IdcTransaction tx = null;
        if ( !validateCreditProvider( cpo, "setUsePFEConfiguration" ) )
        {
            return;
        }
        if ( clsf == null )
        {
            log.warn( "CPAS.setUsePFEConfiguration : Parameters not specified. clsf is null. cpo=" + cpo + ",ctp=" + ctp );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }
        if ( !validateCreditCounterparty( cpo, ctp, "setUsePFEConfiguration" ) )
        {
            return;
        }
        try
        {
            TradingParty creditTp = CounterpartyUtilC.getTradingParty( ctp, cpo );
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CPAS.setUsePFEConfiguration : No cpty rule. usePFEConfig=" )
                        .append( usePFEConfig ).append( ",cpo=" ).append( cpo.getShortName() ).append( ",cc=" )
                        .append( ctp.getShortName() ).append ( ",clsf=" ).append ( clsf ).toString() );
                return;
            }

            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cptyRule, clsf );
            if ( clr == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setUsePFEConfiguration : No credit limit rule for cpo=" )
                        .append( cpo ).append( ",cc=" ).append( ctp ).append( ",cclr=" )
                        .append( cptyRule ).append( ",creditRules=" ).append( cptyRule.getChildrenRules() ).toString() );
                return;
            }
            boolean isUsePFE = clr.isUsePFEConfiguration();
            if ( isUsePFE == usePFEConfig)
            {
                log.info( new StringBuilder( 200 ).append( "CPAS.setUsePFEConfiguration : No change for UsePFEConfig =" )
                        .append( ctp ).append( ",existing=" ).append( isUsePFE ).append( ",cpo=" )
                        .append( cpo.getShortName() ).append( ",cc=" ).append( ctp.getShortName () ).append ( ",clsf=" )
                        .append ( clsf.getShortName () ).toString() );
                return;
            }
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CreditLimitRule registeredClr = ( CreditLimitRule ) clr.getRegisteredObject();
            registeredClr.setUsePFEConfiguration( usePFEConfig );
            String eventName = CreditMessageEvent.SETUSEPFECONFIGURATION.getName();
            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            CreditLimitAdminAuditManagerC.getInstance().auditCreditLimitRuleLevelUsePFE( cpo, isCptyOrgLevel, ctp, cptyRule.getTradingPartyOrganization (), clsf, isUsePFE, usePFEConfig );

            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, eventName );
            propertiesMap.put ( CreditLimitConstants.CREDIT_RULE_TYPE_PROPERTY, clsf.getShortName () );
            getTransaction().addRemoteFunctor( CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( eventName );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CPAS.setUsePFEConfiguration : No remote notification functor registered. event=" + eventName );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setUsePFEConfiguration : org=" + cpo.getShortName() + ",usePFEConfig=" +  usePFEConfig + ",cc=" + ctp + ",clsf=" + clsf , e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public CreditTenorProfileDetails getCreditTenorProfiles( Organization cpo, TradingParty creditCpty, String profileOrConfigName, boolean usePFE)
    {
		boolean isUseDefault = false;
		CounterpartyCreditLimitRule cclr = null;
		CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( cpo );
		double avlAmt = 0;
		if(creditCpty != null) {
    		//If we reached here, then configName is empty. So see what is configured for the CreditProviderOrg, CreditCpty combo and retrieve it.
	        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, cpo );
	        cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );	        
	        isUseDefault = cclr.isUseDefaultTenorProfile();
	        Collection<CreditLimitRule> crdLmtRules = CreditUtilC.getCreditLimitRules(cclr);
	        for(CreditLimitRule crdLmtRule:crdLmtRules){
	            if (crdLmtRule!=null && crdLmtRule instanceof SingleCreditLimitRule)  {
	                SingleCreditLimitRule singleCredLmtRule = (SingleCreditLimitRule)crdLmtRule;
                    CreditUtilization aggCu = CreditUtilizationManagerC.getInstance().getCreditUtilizationForDate( singleCredLmtRule, null );
	                avlAmt = aggCu.getAvailableUsedAmount();
	             }
	        }
		} else {
			// If we reached here, then, either useDefault is checked on creditCpty page or we want to retrieve the Config for the CPO directly
			isUseDefault = true;
		}
		
    	//name is not empty. So retrieve by name.
    	if(!StringUtils.isEmpty(profileOrConfigName))
    	{    		 
    		// IF usePFE is true, then retrieve the corresponding PFEConfiguration from creditProviderOrg namespace
    		// ELSE retrieve the corresponding tenorProfile from creditProviderOrg namespace.
    		if(usePFE) {
    			PFEConfiguration pfeConfiguration = getPfeConfiguration(cpo, profileOrConfigName);
    			return flattenPFEConfiguration(pfeConfiguration, avlAmt);
    		} else {
    			CreditTenorProfile tenorProfile = getCreditTenorProfile(cpo, profileOrConfigName);
    			return flattenCreditTenorProfile(tenorProfile, avlAmt);
    		}
    	}
    	else
    	{
	        if(isUseDefault) //i.e. Get the parent (CPO) level configuration
	        {
	        	boolean isUsePFE = clrs.isUsePFEConfiguration();
	            if(isUsePFE)
	            {
	            	PFEConfiguration pfeConfiguration = clrs.getPfeConfiguration();
	            	return flattenPFEConfiguration(pfeConfiguration, avlAmt);
	            }	    
	            else
	            {
	            	return flattenCreditTenorProfile(clrs.getCreditTenorProfile(), avlAmt);
	            }
	        }
	        else
	        {
	        	boolean isUsePFE = cclr.isUsePFEConfiguration();
	            if(isUsePFE)
	            {
	            	PFEConfiguration pfeConfiguration = cclr.getPfeConfiguration();
	            	return flattenPFEConfiguration(pfeConfiguration, avlAmt);
	            }	
	            else
	            {
	            	return flattenCreditTenorProfile(cclr.getCreditTenorProfile(), avlAmt);
	            }
	        }
    	}
    }

    public void setUseCreditLimitRuleLevelTenorCoefficients( Organization cpo, TradingParty ctp, boolean useCLRTenorCoefficients )
    {
        IdcTransaction tx = null;
        if ( !validateCreditProvider( cpo, "setUseCreditLimitRuleLevelTenorCoefficients" ) )
        {
            return;
        }
        try
        {
            TradingParty creditTp = CounterpartyUtilC.getTradingParty( ctp, cpo );
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );
            boolean isUseCLRTenorCoefficients = cptyRule.isCreditLimitRuleLevelTenorCoefficients ();
            if ( isUseCLRTenorCoefficients == useCLRTenorCoefficients )
            {
                log.info( new StringBuilder( 200 ).append( "CPAS.setUseCreditLimitRuleLevelTenorCoefficients : No change for useCreditLimitRuleLevelTenorCoefficients =" )
                        .append( ctp ).append( ",existing=" ).append( isUseCLRTenorCoefficients ).append( ",cpo=" ).append( cpo.getShortName() ).toString() );
                return;
            }

            CreditLimitRule dailyClr = CreditUtilC.getCreditLimitRule( cptyRule, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
            Entity existingTenorProfileHolder = CreditUtilC.getCreditTenorProfileHolder( cptyRule, dailyClr );
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();
            registeredCclr.setCreditLimitRuleLevelTenorCoefficients ( useCLRTenorCoefficients );
            String eventName = CreditMessageEvent.SETUSECREDITLIMITRULELEVELTENORCOEFFICIENTS.getName();
            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            CreditLimitAdminAuditManagerC.getInstance().auditUseCreditLimitRuleLevelTenorCoefficients( cpo, isCptyOrgLevel, ctp, cptyRule.getTradingPartyOrganization (), isUseCLRTenorCoefficients, useCLRTenorCoefficients );

            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, eventName );
            getTransaction().addRemoteFunctor( CreditTenorProfileNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( eventName );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CPAS.setUseCreditLimitRuleLevelTenorCoefficients : No remote notification functor registered. event=" + eventName );
            }

            dailyClr = CreditUtilC.getCreditLimitRule( registeredCclr, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
            Entity newTenorProfileHolder = CreditUtilC.getCreditTenorProfileHolder( registeredCclr, dailyClr );
            boolean recalculateDailyCUs = false;
            if ( existingTenorProfileHolder != null && newTenorProfileHolder != null )
            {
                recalculateDailyCUs = !existingTenorProfileHolder.isSameAs( newTenorProfileHolder );
            }
            else if ( existingTenorProfileHolder != null || newTenorProfileHolder != null  )
            {
                recalculateDailyCUs = true;
            }

            if ( recalculateDailyCUs )
            {
                addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( dailyClr ) );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setUseCreditLimitRuleLevelTenorCoefficients : org=" + cpo.getShortName() + ",useCLRTenorCoefficients=" +  useCLRTenorCoefficients , e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    private CreditTenorProfileDetails flattenCreditTenorProfile(CreditTenorProfile tenorProfile, double avlAmt)
    {
    	if(tenorProfile!=null){
	    	Map<String, CreditTenorProfile> currencyPair2TenorProfile = new HashMap<String, CreditTenorProfile>();    	
	    	currencyPair2TenorProfile.put("ALL", tenorProfile);    	
	    	return new CreditTenorProfileDetails(tenorProfile.getShortName(), currencyPair2TenorProfile, avlAmt);
    	}
    	return null;
    }
    
    
    private CreditTenorProfileDetails flattenPFEConfiguration(PFEConfiguration pfeConfiguration, double avlAmt)    
    {
    	if(pfeConfiguration!=null)
    	{
    		Map<String, CreditTenorProfile> currencyPair2TenorProfile = new HashMap<String, CreditTenorProfile>(pfeConfiguration.getCreditTenorProfiles());
    		currencyPair2TenorProfile.put("ALL", pfeConfiguration.getDefaultTenorProfile());
    		return new CreditTenorProfileDetails(pfeConfiguration.getShortName(), currencyPair2TenorProfile, avlAmt);
    	}
    	return null;
    }

    /**
     * Convert from Grid API object model to Toplink
     *
     * @param organization organization
     * @param model model
     */
    public  void persist (Organization organization, PfeConfiguration model )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CPAS.persist : Begin. cpo=" )
                    .append( organization ).append( ",PfeConfiguration=" ).append( model ).toString());
        }

        if ( !validateCreditProvider( organization, "persist" ) )
        {
            return;
        }
        IdcTransaction tx = null;
        try {
            CreditLimitOrgFunction orgFunc = ( CreditLimitOrgFunction ) IdcUtilC.refreshObject ( CreditUtilC.getCreditLimitOrgFunction(organization) );
            tx = startTransaction(cptyRuleTransactionReadOnlyClasses);
            removeReadOnlyClass(tx, CreditLimitOrgFunctionC.class);
            removeReadOnlyClass(tx, CreditTenorProfileC.class);
            removeReadOnlyClass(tx, CreditTenorParametersC.class);
            removeReadOnlyClass(tx, PFEConfigurationC.class);
            removeReadOnlyClass(tx, PFEConfigurationProfileC.class);

            PFEConfiguration pfeConfiguration = getPfeConfiguration(organization, model.getName());
            log.info ( "CPAS.persist : updating pfe config for org=" + organization + ",new=" + model + ",existing=" + pfeConfiguration );
            Map<String, com.integral.finance.creditLimit.CreditTenorProfile> tenorProfileMap = new HashMap<String,  com.integral.finance.creditLimit.CreditTenorProfile>();
            if (null == pfeConfiguration)
            {
                pfeConfiguration = CreditLimitFactory.newPFEConfiguration();
                pfeConfiguration.setNamespace(organization.getNamespace());
                pfeConfiguration.setShortName(model.getName());
            }
            pfeConfiguration = (PFEConfiguration)pfeConfiguration.getRegisteredObject();
            pfeConfiguration.setOwner(orgFunc.getRegisteredObject());
            pfeConfiguration.setDescription(model.getDescription());

            String defaultTenorProfile = model.getDefaultTenorProfile();
            com.integral.finance.creditLimit.CreditTenorProfile tenorProfile = getCreditTenorProfile(organization, defaultTenorProfile);
            if (null == tenorProfile)
            {
                throw new IdcRuntimeException("Default Tenor Profile " + defaultTenorProfile + " not found ");
            }
            pfeConfiguration.setDefaultTenorProfile((com.integral.finance.creditLimit.CreditTenorProfile)tenorProfile.getRegisteredObject());

            Map<String,PFEConfigurationProfile> profileMap = new HashMap<String, PFEConfigurationProfile>();

            for(PFEConfigurationProfile pfeConfigurationProfile:pfeConfiguration.getPfeConfigurationProfiles())
            {
                profileMap.put(pfeConfigurationProfile.getCurrencyPairGroup().getShortName(),pfeConfigurationProfile);
            }

            List<PFEConfigurationProfile> lstPFEProfiles = new ArrayList<PFEConfigurationProfile>();
            for (PfeConfigurationProfile pfeProfiles : model.getPfeConfigurationProfiles())
            {
                PFEConfigurationProfile pfeConfigurationProfile = convertToPersistentModel(organization, pfeConfiguration,pfeProfiles,tenorProfileMap);
                lstPFEProfiles.add(pfeConfigurationProfile);
            }

            for(PFEConfigurationProfile pfeConfigurationProfile:lstPFEProfiles)
            {
                profileMap.remove(pfeConfigurationProfile.getCurrencyPairGroup().getName());
            }
            Collection<PFEConfigurationProfile> toBeRemove = profileMap.values();
            if(!com.integral.util.CollectionUtil.isEmpty(toBeRemove)) {
                for (PFEConfigurationProfile pfeConfigurationProfile : profileMap.values()) {
                    tx.getUOW().deleteObject(pfeConfigurationProfile.getRegisteredObject());
                }
            }
            pfeConfiguration.setPfeConfigurationProfiles(lstPFEProfiles);
            addPfeConfiguration(organization, pfeConfiguration);

            CreditUtilC.endTransaction( tx );

            // refresh the credit limit org function if the tx is committed.
            if ( tx != null )
            {
                IdcUtilC.refreshObject ( orgFunc );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.persist : org=" + organization.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }

    }

    private  PFEConfigurationProfile convertToPersistentModel (Organization organization,PFEConfiguration pfeConfiguration,PfeConfigurationProfile model,
                                                               Map<String, com.integral.finance.creditLimit.CreditTenorProfile> profileMap )
    {
        Collection<PFEConfigurationProfile> pfeConfigurationProfiles = pfeConfiguration.getPfeConfigurationProfiles();
        PFEConfigurationProfile existingProfile = null;
        for(PFEConfigurationProfile pfeConfigurationProfile:pfeConfigurationProfiles)
        {
            if(pfeConfigurationProfile.getCurrencyPairGroup().getName().equals(model.getCurrencyPairGroup()))
            {
                existingProfile = pfeConfigurationProfile;
                log.info("PFE-convertToPersistentModel - Existing Profile found for Currency Pair Group Updating Currency Pair Group : " + model.getCurrencyPairGroup());
                break;
            }
        }
        PFEConfigurationProfile domainModel = null==existingProfile ? CreditLimitFactory.newPFEConfigurationProfile() : existingProfile;
        domainModel = (PFEConfigurationProfile)domainModel.getRegisteredObject();
        domainModel.setNamespace(organization.getNamespace());
        domainModel.setSortOrder( model.getSortOrder() );

        CurrencyPairGroup ccyPairGrp =  getCurrencyPairGroup(organization, model.getCurrencyPairGroup());

        domainModel.setCurrencyPairGroup( (CurrencyPairGroup)ccyPairGrp.getRegisteredObject() );
        domainModel.setCreditTenorProfile( convertToPersistentModel(organization,model.getTenorProfile(),profileMap) );
        domainModel.setOwner(pfeConfiguration);

        return domainModel;
    }

    private CreditTenorProfile convertToPersistentModel(Organization organization, com.integral.finance.creditLimit.model.CreditTenorProfile model,
                             Map<String, com.integral.finance.creditLimit.CreditTenorProfile> profileMap )
    {
        com.integral.finance.creditLimit.CreditTenorProfile domainModel;
        domainModel = getCreditTenorProfile(organization,model.getName());
        String description = model.getDescription();
        if( null==domainModel)
        {
            domainModel = profileMap.get(model.getName());
            if( null==domainModel)
            {
                domainModel = CreditLimitFactory.newCreditTenorProfile();
                domainModel = (com.integral.finance.creditLimit.CreditTenorProfile) domainModel.getRegisteredObject();
                domainModel.setShortName(model.getName());
                profileMap.put(model.getName(),domainModel);
            }
        }
        else
        {
            domainModel = (com.integral.finance.creditLimit.CreditTenorProfile)domainModel.getRegisteredObject();
            if( null == description || 0 == description.trim ().length () )
            {
                description = domainModel.getDescription();
            }
        }
        if( null == description || 0 == description.trim ().length () )
        {
            description = model.getName();
        }
        domainModel.setDescription(description);
        domainModel.setOwner(organization.getCreditLimitOrgFunction().getRegisteredObject());
        domainModel.setCreditTenorParameters(convertToPersistentModel(organization,domainModel,model.getTenorParameters() ) );
        return domainModel;
    }

    /**
     * Convert From  Simple POJO Model to PersistentModel
     * @param model model
     * @return tenor parameters collection
     */
    private Collection<com.integral.finance.creditLimit.CreditTenorParameters>
    convertToPersistentModel(Organization org,com.integral.finance.creditLimit.CreditTenorProfile owner,
                            Collection<com.integral.finance.creditLimit.model.CreditTenorParameters> model )
    {
        Collection<com.integral.finance.creditLimit.CreditTenorParameters> creditTenorParameters =
                new ArrayList<com.integral.finance.creditLimit.CreditTenorParameters>();

        Map<String, com.integral.finance.creditLimit.CreditTenorParameters> tenorParametersMap = new HashMap<String, com.integral.finance.creditLimit.CreditTenorParameters>();
        for(com.integral.finance.creditLimit.CreditTenorParameters creditTenorParameters1:owner.getCreditTenorParameters())
        {
            tenorParametersMap.put(creditTenorParameters1.getTenor().getName().toLowerCase(),creditTenorParameters1);
        }
        if ( model != null && !model.isEmpty() )
        {
            for ( com.integral.finance.creditLimit.model.CreditTenorParameters ctpApiModel : model )
            {
                com.integral.finance.creditLimit.CreditTenorParameters domainModel;
                domainModel = tenorParametersMap.get(ctpApiModel.getTenor().toLowerCase() );
                if(null == domainModel ) domainModel=  CreditLimitFactory.newCreditTenorParameters();
                domainModel = ( com.integral.finance.creditLimit.CreditTenorParameters)domainModel.getRegisteredObject();
                domainModel.setNamespace(org.getNamespaceGroup());
                domainModel.setOwner(owner);
                domainModel.setTenor( new Tenor( ctpApiModel.getTenor() ) );
                domainModel.setUtilizationPercent( ctpApiModel.getUtilizationPercent() );
                creditTenorParameters.add(domainModel);
            }
        }
        return creditTenorParameters;
    }

    public CurrencyPairGroup getCurrencyPairGroup(Organization org, String shortName)
    {
        return (CurrencyPairGroup)ReferenceDataCacheC.getInstance().getEntityByShortName( shortName, CurrencyPairGroupC.class, org.getNamespace(), Entity.ACTIVE_STATUS );
    }

    public void setGrossPositionSpreadMargin( Organization cpo, CreditTenorProfile ctp, double margin )
    {
        if ( cpo == null || ctp == null || margin < 0.0 )
        {
            log.warn ( "CPAS.setGrossPositionSpreadMargin : Parameters not specified." );
            throw new IdcRuntimeException ( new IllegalArgumentException ( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        IdcTransaction tx = null;
        try
        {
            double existingMargin = ctp.getGrossPositionSpreadMargin ();
            if ( margin == existingMargin )
            {
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            removeReadOnlyClass( tx, CreditTenorProfileC.class );

            CreditTenorProfile registeredCTP = ( CreditTenorProfile ) ctp.getRegisteredObject(  );
            registeredCTP.setGrossPositionSpreadMargin ( margin );

            CreditLimitAdminAuditManagerC.getInstance().auditUpdateGrossSpreadMargin( cpo, registeredCTP, existingMargin, margin );

            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CPAS.setGrossPositionSpreadMargin : org=" + cpo.getShortName() + ",ctp=" + ctp + ",margin=" + margin, e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    private Map<String, String> getDailyRulesTenorProfileMap( CreditLimitRuleSet clrs )
    {
        Map<String, String> dailyPFEMap = new HashMap<String, String>();
        for ( Object rule: clrs.getRules() )
        {
            CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule) rule;
            if ( cclr.isActive() )
            {
                CreditLimitRule dailyClr = CreditUtilC.getCreditLimitRule( cclr, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION );
                if ( dailyClr != null )
                {
                    Entity tenorProfileHolder = CreditUtilC.getCreditTenorProfileHolder( cclr, dailyClr );
                    dailyPFEMap.put( dailyClr.getGUID(), tenorProfileHolder != null ? tenorProfileHolder.getGUID() : "NULL" );
                }
            }
        }
        return dailyPFEMap;
    }
}
