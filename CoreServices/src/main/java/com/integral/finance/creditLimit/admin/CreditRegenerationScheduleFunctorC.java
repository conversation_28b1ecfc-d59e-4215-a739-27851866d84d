package com.integral.finance.creditLimit.admin;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.WorkflowMessage;
import com.integral.scheduler.ScheduleFunctorC;

/**
 * This implements ScheduleFunctor interface and is used in connection with schedule event invocation. This schedule event
 * is used for credit regeneration.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditRegenerationScheduleFunctorC extends ScheduleFunctorC
{
    private Log log = LogFactory.getLog( CreditAdminServiceLoggerC.class );

    public void execute( WorkflowMessage wm )
    {
        try
        {
            log.warn( "CreditRegenerationScheduleFunctorC.execute.INFO : Begin credit regeneration." );
            CreditFunctorServerC.getInstance().execute();
            log.warn( "CreditRegenerationScheduleFunctorC.execute.INFO : End credit regeneration." );
        }
        catch ( Exception e )
        {
            log.error( "CreditRegenerationScheduleFunctorC.execute.ERROR : Error while executing the credit regeneration schedule functor.", e );
        }
    }

    @Override
    public String getDescription()
    {
        return "Functor to trigger credit Regeneration";
    }
}
