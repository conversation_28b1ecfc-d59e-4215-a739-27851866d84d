package com.integral.finance.creditLimit.handler;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.finance.creditLimit.admin.CreditAdminServiceLoggerC;
import com.integral.finance.creditLimit.configuration.CreditConfigurationFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;

/**
 * This class is used an end of day service handler which will be executed at the end of the business day.
 * This is used to reset all the credit utilizations which are present in the credit utilization cache.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditUtilizationCacheRebuildHandlerC implements MessageHandler
{
    protected Log log = LogFactory.getLog( CreditAdminServiceLoggerC.class );

    public Message handle( Message message )
    {
        try
        {
            log.info( "CreditUtilizationCacheRebuildHandlerC.handle : Rebuilding all credit utilizations in cache at the end of business day." );
            if ( CreditConfigurationFactory.getCreditConfigurationMBean().isCreditEndOfDayPreCalculationEnabled () )
            {
                log.info( "CreditUtilizationCacheRebuildHandlerC.handle.INFO : rebuilding only stale credit utilizations in cache at the end of business day." );
                CreditUtilizationManagerC.getInstance ().getCreditUtilizationCache ().rebuildAllStaleEntries (  );
            }
            else
            {

                if ( CreditConfigurationFactory.getCreditConfigurationMBean ().isCreditEndOfDayCacheEntryStaleEnabled () )
                {
                    CreditUtilizationManagerC.getInstance ().getCreditUtilizationCache ().rebuildAllCreditUtilizationsWithDelay ( CreditConfigurationFactory.getCreditConfigurationMBean ().getCreditEndOfDayCacheEntryRebuildDelay () );
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "CreditUtilizationCacheRebuildHandlerC.handle.ERROR : Error sending notifications.", e );
        }
        return message;
    }

}

