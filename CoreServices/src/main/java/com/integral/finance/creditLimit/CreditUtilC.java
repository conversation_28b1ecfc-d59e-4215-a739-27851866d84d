package com.integral.finance.creditLimit;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.admin.ha.PrimaryAdminIdentifier;
import com.integral.admin.services.tradingvenue.mbean.RexCommonMBeanC;
import com.integral.businessCenter.BusinessCenter;
import com.integral.exception.IdcDatabaseException;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.exception.IdcOptimisticLockException;
import com.integral.exception.IdcRuntimeException;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationFactory;
import com.integral.finance.creditLimit.configuration.CreditConfigurationFactory;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationFactory;
import com.integral.finance.creditLimit.db.CreditDataQueryServiceFactory;
import com.integral.finance.creditLimit.handler.CreditUtilizationCacheSynchronizationHandlerC;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.*;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.Request;
import com.integral.finance.fx.*;
import com.integral.finance.marketData.MarketDataSet;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeLeg;
import com.integral.finance.trade.cache.TradeCacheC;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.util.CurrencyUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.*;
import com.integral.persistence.Entity;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.Namespace;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.persistence.util.LogUtil;
import com.integral.primebroker.PrimeBrokerPathUtil;
import com.integral.rule.Rule;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.time.DatePeriod;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.FormatUtilC;
import com.integral.util.IdcUtilC;
import com.integral.util.MathUtilC;
import com.integral.workflow.dealing.fx.FXDealingLimit;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.expressions.ExpressionMath;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.queries.ReportQuery;
import org.eclipse.persistence.queries.ReportQueryResult;
import org.eclipse.persistence.sessions.Session;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Matcher;

/**
 * This class is used to provide commonly used utility methods for credit limit service and credit limit admin service.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditUtilC implements CreditLimitConstants
{
    protected final static String INSUFFICIENT_PARAMETERS_ERROR = "Insufficient parameters";
    private static final Log log = LogFactory.getLog( CreditUtilC.class );
    private static final FXRateConvention stdQuoteConv = ReferenceDataCacheC.getInstance().getStandardFXRateConvention();
    private static final Double MIN_DOUBLE = 0.000000001;

    static {
        PrimeBrokerPathUtil.getInstance().setIntegrationHelper(new PrimeBrokerPathUtilIntegrationHelperC());
    }

    /**
     * Returns the current transaction from the session manager.
     *
     * @return transaction
     */
    public static IdcTransaction getTransaction()
    {
        return IdcSessionManager.getInstance().getTransaction();
    }

    /**
     * Creates new transaction from the session manager.
     *
     * @return new transaction
     */
    public static IdcTransaction newTransaction()
    {
        try
        {
            return IdcSessionManager.getInstance().newIdcTransaction(Thread.currentThread().getName());
        }
        catch ( IdcNoSuchObjectException e )
        {
            return null;
        }
    }

    /**
     * Creates new fake transaction from the session manager.
     *
     * @return new transaction
     */
    public static IdcTransaction newFakeTransaction()
    {
        try
        {
            return IdcSessionManager.getInstance().newFakeTransaction(IdcUtilC.getSessionContextUser(), Thread.currentThread().getName());
        }
        catch ( IdcNoSuchObjectException e )
        {
            return null;
        }
    }

    /**
     * Returns the session context user.
     *
     * @return session context user
     */
    public static User getSessionContextUser()
    {
        return IdcUtilC.getSessionContextUser();
    }

    /**
     * Commits the transaction if it is created inside the service method.
     *
     * @param tx transaction
     */
    public static void endTransaction( IdcTransaction tx )
    {
        endTransaction ( tx,false );
    }

    /**
     * Commits the transaction if it is created inside the service method.
     *
     * @param tx transaction
     */
    public static void endTransaction( IdcTransaction tx, boolean throwException )
    {
        endTransaction ( tx,false, true );
    }

    /**
     * Commits the transaction if it is created inside the service method.
     *
     * @param tx transaction
     */
    public static void endTransaction( IdcTransaction tx, boolean throwException, boolean logDBException )
    {
        if ( tx != null )
        {
            try
            {
                tx.commit();
            }
            catch ( IdcOptimisticLockException iole )
            {
                log.warn( "CU.endTransaction - Optimistic lock occured.", iole );
                if ( throwException )
                {
                    throw iole;
                }
            }
            catch ( IdcDatabaseException dbe )
            {
                if ( logDBException )
                {
                    log.error ( "CU.endTransaction - database exception.", dbe );
                }
                if ( throwException )
                {
                    throw dbe;
                }
            }
        }
    }

    /**
     * Closes the transaction properly. Transaction is either committed or released properly.
     * This process makes sure that if either of them happened, then it cleans up the loose transaction from the session context.
     *
     * @param tx transaction
     */
    public static void closeTransaction( IdcTransaction tx )
    {
        if ( tx != null )
        {
            IdcSessionManager.getInstance().setTransaction( null );
        }
    }

    /**
     * Returns the credit limit org function for the credit provider org.
     *
     * @param creditProviderOrg credit provider org
     * @return credit limit org function
     */
    public static CreditLimitOrgFunction getCreditLimitOrgFunction( Organization creditProviderOrg )
    {
        try
        {
            return creditProviderOrg.getCreditLimitOrgFunction();
        }
        catch ( Exception e )
        {
            log.error( "CU.getCreditLimitOrgFunction : org=" + creditProviderOrg, e );
        }
        return null;
    }

    /**
     * Returns the credit limit ruleset for the organization.
     *
     * @param cpo credit provider org
     * @return credit limit rule set
     */
    public static CreditLimitRuleSet getCreditLimitRuleSet( Organization cpo )
    {
        return CreditDataQueryServiceFactory.getCreditDataQueryService().getCreditLimitRuleSet( cpo );
    }

    /**
     * Start the transaction if there is no existing transaction. Return null if there is an existing transaction.
     *
     * @param readOnlyClasses read only classes
     * @return transaction
     */
    public static IdcTransaction startTransaction( Vector readOnlyClasses )
    {
        IdcTransaction tx = null;
        try
        {
            IdcTransaction existingTx = getTransaction();
            tx = existingTx == null ? newTransaction() : null;
            if ( tx != null )
            {
                tx.getUOW().addReadOnlyClasses( readOnlyClasses );
                IdcSessionManager.getInstance().setTransaction( tx );
            }
            else
            {
                if ( existingTx != null )
                {
                    if ( !existingTx.isActive() /*|| existingTx.getUOW() == null || !existingTx.getUOW().isActive() */ )
                    {
                        log.error( "CU.startTransaction : Inactive Transaction or UnitOfWork. tx.Active=" + existingTx.isActive() + ",uow=" + existingTx.getUOW() );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.startTransaction", e );
        }
        return tx;
    }

    /**
     * Start the fake transaction if there is no existing transaction. Return null if there is an existing transaction.
     *
     * @param readOnlyClasses read only classes
     * @return transaction
     */
    public static IdcTransaction startFakeTransaction( Vector readOnlyClasses )
    {
        IdcTransaction tx = null;
        try
        {
            IdcTransaction existingTx = getTransaction();
            tx = existingTx == null ? newFakeTransaction() : null;
            if ( tx != null )
            {
                IdcSessionManager.getInstance().setTransaction( tx );
                tx.setFakeTransactionRemoteFunctorEnabled( true );
                tx.setRemoteFunctorExecutionEnabled( true );
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.startFakeTransaction : Exception while creating a fake transaction. roClasses=" + readOnlyClasses, e );
        }
        return tx;
    }


    /**
     * Set the credit workflow message status based on the success flag specified.
     *
     * @param cwm     credit workflow message
     * @param success flag indicating the status of credit opertaion
     */
    public static void setCreditWorkflowMessageStatus( CreditWorkflowMessage cwm, boolean success )
    {
        cwm.setStatus( success ? MessageStatus.SUCCESS : MessageStatus.FAILURE );
    }

    /**
     * Return the collection of counterparty credit limit rules for the counterparty organization.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCptyOrg     credit cpty org
     * @return cpty rules
     */
    public static Collection<CounterpartyCreditLimitRule> getCounterpartyCreditLimitRules( Organization creditProviderOrg, Organization creditCptyOrg )
    {
        Collection<CounterpartyCreditLimitRule> rules = new ArrayList<CounterpartyCreditLimitRule>();
        CreditLimitRuleSet clrs = getCreditLimitRuleSet( creditProviderOrg );
        Collection<Rule> cptyRules = clrs.getRules();
        for ( Rule rule : cptyRules )
        {
            CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) rule;
            Organization cclrOrg = cclr.getTradingParty() != null ? cclr.getTradingParty().getLegalEntityOrganization() : cclr.getTradingPartyOrganization();
            if ( creditCptyOrg.isSameAs( cclrOrg ) )
            {
                rules.add( cclr );
            }
        }
        return rules;
    }

    /**
     * This returns list of all counterparty credit limit rules for given provider for a given status
     *
     * @param creditProviderOrg credit provider org
     * @param active            boolean indicating whether to retrieve all active or inactive cpty rules
     * @return list of all counterparty credit limit rules for given provider
     */
    public static List<CounterpartyCreditLimitRule> getAllCptyCreditLimitRulesForProvider( Organization creditProviderOrg, boolean active )
    {
        ArrayList<CounterpartyCreditLimitRule> cptyRulesCollection = new ArrayList<CounterpartyCreditLimitRule>();
        try
        {
            CreditLimitRuleSet clrs = getCreditLimitRuleSet( creditProviderOrg );
            Collection<Rule> cptyRules = clrs.getRules();

            for ( Rule cptyRule : cptyRules )
            {
                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cptyRule;
                TradingParty tradParty = cclr.getTradingParty();
                Organization countPartyCredLimitRuleOrg = cclr.getTradingPartyOrganization();
                if ( null == countPartyCredLimitRuleOrg )
                {
                    log.warn( " Counter Party Organizations not found for Trading Party " + tradParty.getShortName() );
                    continue;
                }
                if ( ( "FXI".equals( countPartyCredLimitRuleOrg.getShortName() ) ) )
                {
                    continue;
                }

                if ( active )
                {
                    if ( tradParty != null )
                    {
                        LegalEntity legalEntity = tradParty.getLegalEntity();
                        if ( legalEntity != null && cclr.isActive() )
                        { // remove check for tp active

                            cptyRulesCollection.add( cclr );
                        }
                    }
                    else
                    {
                        if ( cclr.isActive() )
                        {
                            cptyRulesCollection.add( cclr );
                        }
                    }
                }
                else // All inactive cclrs
                {
                    if ( tradParty != null )
                    {
                        LegalEntity legalEntity = tradParty.getLegalEntity();
                        if ( legalEntity != null )
                        {
                            if ( !cclr.isActive() && !cclr.isOrgLevel() )
                            {
                                cptyRulesCollection.add( cclr );
                            }
                        }
                    }
                    else
                    {
                        if ( !cclr.isActive() && cclr.isOrgLevel() )
                        {
                            cptyRulesCollection.add( cclr );
                        }
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.getAllCptyCreditLimitRulesForProvider ", e );
        }
        return cptyRulesCollection;
    }

    /**
     * This returns list of all counterparty credit limit rules for given provider for a given status
     *
     * @param creditProviderOrg credit provider org
     * @param cptyOrg boolean indicating whether to retrieve all active or inactive cpty rules
     * @return list of all counterparty credit limit rules for given provider
     */
    public static List<CounterpartyCreditLimitRule> getInactiveCounterpartyTradingParyRules( Organization creditProviderOrg,
                                                                                             Organization cptyOrg)
    {
        ArrayList<CounterpartyCreditLimitRule> cptyRulesCollection = new ArrayList<CounterpartyCreditLimitRule>();

        try
        {
            CreditLimitRuleSet clrs = getCreditLimitRuleSet( creditProviderOrg );
            Collection<Rule> cptyRules = clrs.getRules();

            for ( Rule cptyRule : cptyRules )
            {
                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cptyRule;
                TradingParty tradParty = cclr.getTradingParty();
                Organization countPartyCredLimitRuleOrg = cclr.getTradingPartyOrganization();
                if ( null == countPartyCredLimitRuleOrg )
                {
                    log.warn( " Counter Party Organizations not found for Trading Party " + tradParty.getShortName() );
                    continue;
                }
                if ( ( "FXI".equals( countPartyCredLimitRuleOrg.getShortName() ) ) ||
                        !(cptyOrg.getShortName().equals(countPartyCredLimitRuleOrg.getShortName())))
                {
                    continue;
                }
                if ( tradParty != null )
                {
                    LegalEntity legalEntity = tradParty.getLegalEntity();
                    if ( legalEntity != null )
                    {
                        if ( !cclr.isActive() )
                        {
                            cptyRulesCollection.add( cclr );
                        }
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.getAllCptyCreditLimitRulesForProvider ", e );
        }
        return cptyRulesCollection;
    }

    public static Collection<CreditUtilization> getAllCreditUtilizationsWithUsedOrLimitAmount(DailyCreditLimitRule clr)
    {
        if( CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationLookupSpacesEnabled(clr.getNamespace().getShortName()))
        {
            return CreditDataQueryServiceFactory.getCreditReferenceDataService().getAllCreditUtilizations( clr, true );
        }
        return getAllCreditUtilizationsWithUsedOrLimitAmountFromOracle( clr);
    }

    private static Collection<CreditUtilization> getAllCreditUtilizationsWithUsedOrLimitAmountFromOracle(DailyCreditLimitRule clr)
    {
        Collection<CreditUtilization> col = null;
        try {

            ReadAllQuery raq = new ReadAllQuery(CreditUtilization.class);
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression activeExpr = eb.get(Entity.Status).equal(Entity.ACTIVE_STATUS);
            Expression clrExpr = eb.get("creditLimitRule").equal( clr );
            Expression usedExpr = eb.get("usedAmount").notEqual(0).or(eb.get("limitAmount").notNull());
            Expression dateExpr = eb.get("date").greaterThanEqual(EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate().asSqlDate());
            Expression finalExpression = clrExpr.and(activeExpr.and( usedExpr.and(dateExpr)));
            raq.setSelectionCriteria(finalExpression);

            col = (Collection<CreditUtilization>) PersistenceFactory.newSession().executeQuery(raq);
            if ( col == null || col.isEmpty() ) {
                log.warn("CU.getCreditUtilizations : No credit utilizations for clr=" + clr);
            }
        }
        catch ( Exception e ) {
            log.error("CU.getCreditUtilizations ", e);
        }
        return col;
    }

    /**
     * This returns list of all credit utilizations from oracle for given provider for aggregate and daily
     * for a span of given horizon
     *
     * Note: Even when the dataSource for CreditUtiliztion lookup is RDS, this method queries Oracle. This is
     * by design. coz it is used to retrieve oracle CUs and migrate them to RDS
     *
     *
     * Logic: Get All (Active Aggregate) and (Active Daily with usedAmt!=0 or limitAmt!=null) utilizations
     *
     * @param creditProviderOrg credit provider org
     * @return list of all credit utilizations for given provider
     */
    public static Collection<CreditUtilization> getAllCreditUtilizationsForMigration( Organization creditProviderOrg)
    {
        Collection<CreditUtilization> col = null;
        try {

            ReadAllQuery raq = new ReadAllQuery(CreditUtilization.class);

            ExpressionBuilder eb = new ExpressionBuilder();
            Expression activeExpr = eb.get(Entity.Status).equal(Entity.ACTIVE_STATUS);
            Expression aggregateExpr = eb.get("date").isNull();
            Expression usedExpr = eb.get("usedAmount").notEqual(0).or(eb.get("limitAmount").notNull());
            Expression dateExpr = eb.get("date").greaterThanEqual(EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate().asSqlDate());
            Expression finalExpression = activeExpr.and( aggregateExpr.or( usedExpr.and( dateExpr ) ) );
            if ( creditProviderOrg != null ) {
                Expression namespaceExp = eb.get(Entity.Namespace).equal(creditProviderOrg.getNamespace());
                finalExpression = finalExpression.and(namespaceExp);
            }
            raq.setSelectionCriteria(finalExpression);

            col = (Collection<CreditUtilization>) PersistenceFactory.newSession().executeQuery(raq);
            if ( col == null || col.isEmpty() ) {
                log.warn("CU.getCreditUtilizations : No credit utilizations for creditProviderOrg=" + creditProviderOrg);
            }
        }
        catch ( Exception e ) {
            log.error("CU.getCreditUtilizations ", e);
        }
        return col;
    }

    public static Collection<CounterpartyCreditLimitRule> getAutoStopOutEnabledRules( Organization cpo )
    {
        Collection<CounterpartyCreditLimitRule> col = null;
        try
        {
            ReadAllQuery raq = new ReadAllQuery( CounterpartyCreditLimitRule.class );
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression activeExpr = eb.get( Entity.Status ).equal( Entity.ACTIVE_STATUS );
            Expression stopOutEnabledCPOLevel = eb.get( "ruleSet" ).get ( "stopOutPercentage" ).notNull ();
            Expression stopOutEnabledCptyLevel = eb.get ( "stopOutPercentage" ).notNull ();
            Expression finalExpression = activeExpr.and( stopOutEnabledCPOLevel.or( stopOutEnabledCptyLevel ) );
            if ( cpo != null )
            {
                Expression namespaceExp = eb.get( Entity.Namespace ).equal( cpo.getNamespace() );
                finalExpression = finalExpression.and( namespaceExp );
            }
            raq.setSelectionCriteria( finalExpression );
            raq.addOrdering ( eb.get ( Entity.Namespace ).get ( NamedEntity.ShortName ).ascending () );

            col = ( Collection<CounterpartyCreditLimitRule> ) PersistenceFactory.newSession().executeQuery( raq );
            if ( col == null || col.isEmpty() )
            {
                log.info( "CU.getAutoStopOutEnabledRules : no cpty rules with stop-out enabled" );
            }
        }
        catch ( Exception e )
        {
            log.error("CU.getAutoStopOutEnabledRules - Exception while retrieving the stop-out enabled cpty rules.", e);
        }
        return col;
    }

    public static String migrateCreditUtilizationToRDS( String cptyOrg )
    {
        try
        {
            if ( !cptyOrg.equalsIgnoreCase("ALL") )
            {
                StringBuilder sb = new StringBuilder();
                String[] cptyOrgStrs = cptyOrg.split(",");
                for(String eachcptyOrg: cptyOrgStrs)
                {
                    Organization creditProviderOrg = ReferenceDataCacheC.getInstance().getOrganization(eachcptyOrg.trim());

                    if(creditProviderOrg!=null){
                        migrateCreditUtilizationToRDSByOrg( creditProviderOrg );
                    }else{
                        sb.append("Invalid Credit Provider Org: ").append( eachcptyOrg ).append( ", " );
                    }
                }
                String result = sb.toString();
                if(result!=null && !"".equals( result.trim())){
                    return result;
                }
            }
            else
            {
                migrateCreditUtilizationToRDSByOrg( null );
            }
        }
        catch ( Exception e ) {
            log.error("Error while migrating to RDS", e);
            return "Error while migrating to RDS, Check logs";
        }
        return null;
    }

    private static boolean migrateCreditUtilizationToRDSByOrg( Organization creditProviderOrg )
    {
        Collection<CreditUtilization> cuList = CreditUtilC.getAllCreditUtilizationsForMigration( creditProviderOrg );
        String providerOrgName = creditProviderOrg!=null?creditProviderOrg.getShortName():"ALL";
        if ( cuList != null && cuList.size() > 0 )
        {
            log.info("migrateCreditUtilizationToRDS: Started migrating CUs to RDS for CPO: " + providerOrgName);
            long t0 = System.currentTimeMillis();
            for ( CreditUtilization cu : cuList ) {
                CreditDataQueryServiceFactory.getCreditReferenceDataService().createOrUpdateCreditUtilization(cu);
            }
            log.info("migrateCreditUtilizationToRDS: Finished migrating a total of " + cuList.size() +" CUs to RDS for CPO: "
                    + providerOrgName + ". Took " + (System.currentTimeMillis()-t0) + " mills.");
        }
        else {
            log.info("No credit utilizations found for provider: " + providerOrgName);
            return true;
        }
        return false;
    }

    public static List<CreditUtilization> getAllCreditUtilizationsFromRDS( Organization creditProviderOrg, String cptyOrg, String cptyLE )
    {
        List<CreditUtilization> list = new ArrayList<CreditUtilization>();
        Set<CreditUtilization> creditUtilizations = new TreeSet<CreditUtilization> ( new CreditUtilizationComparatorC () );
        try
        {
            CreditLimitRuleSet clrs = getCreditLimitRuleSet(creditProviderOrg);
            CreditLimitOrgFunction creditOrgFunc = CreditUtilC.getCreditLimitOrgFunction(creditProviderOrg);
            int dailyExpHorizon = creditOrgFunc.getDailyExposureHorizon();
            String displayCreditLimitPeriod = Integer.toString( dailyExpHorizon - 1 ) + "D";
            DatePeriod dp = DateTimeFactory.newDatePeriod(displayCreditLimitPeriod);

            if(clrs.isActive())
            {
                Collection<CounterpartyCreditLimitRule> cclrs = clrs.getRules();
                for(CounterpartyCreditLimitRule cclr: cclrs)
                {
                    if( (cptyOrg!=null && !cptyOrg.equals( cclr.getTradingPartyOrganization().getShortName()))
                            ||(cptyLE!=null && !cptyLE.equals( cclr.getTradingParty().getLegalEntity().getShortName())) )
                    {
                        continue;
                    }
                    if(cclr.isActive() && cclr.getTradingPartyOrganization().isActive())
                    {
                        Collection<Rule> childRules = cclr.getChildrenRules();
                        for(Rule rule : childRules)
                        {
                            if(rule.isActive())
                            {
                                CreditLimitRule clr = (CreditLimitRule)rule;
                                if(clr instanceof SingleCreditLimitRule)
                                {
                                    creditUtilizations.addAll( CreditDataQueryServiceFactory.getCreditReferenceDataService().getAllCreditUtilizations(clr ));
                                }
                                else
                                {
                                    creditUtilizations.addAll( getCreditUtilizations( clr, dp ));
                                }
                            }
                        }
                    }
                }
            }
        }
        catch(Exception ex)
        {
            log.error("Error while getAllCreditUtilizationsFromRDS: ", ex);
        }
        list.addAll ( creditUtilizations );
        return list;
    }
    /**
     * This returns list of all credit utilizations for given provider for aggregate and daily
     * for a span of given horizon
     *
     * @param creditProviderOrg credit provider org
     * @param cptyOrg           counterparty org
     * @param cptyLE            counterparty le
     * @return list of all credit utilizations for given provider
     */
    public static List<ReportQueryResult> getAllCreditUtilizationsFor( Organization creditProviderOrg, String cptyOrg, String cptyLE )
    {
        List<ReportQueryResult> creditUtilCollection = new ArrayList<ReportQueryResult>();

        try
        {
            CreditLimitRuleSet clrs = getCreditLimitRuleSet(creditProviderOrg);
            CreditLimitOrgFunction creditOrgFunc = CreditUtilC.getCreditLimitOrgFunction(creditProviderOrg);
            int dailyExpHorizon = creditOrgFunc.getDailyExposureHorizon();
            String displayCreditLimitPeriod = Integer.toString( dailyExpHorizon - 1 ) + "D";
            IdcDate businessDate2 = clrs.getMarketDataSet().getCurrentBaseDate();
            IdcDate businessDate1 = businessDate2.addDatePeriod( DateTimeFactory.newDatePeriod( displayCreditLimitPeriod ) );
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression dateExpr = eb.get( "cuDate" ).isNull().or( eb.get( "cuDate" ).greaterThanEqual(EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate().asSqlDate()) );
            Expression dateExpr2 = eb.get( "cuDate" ).isNull().or(eb.get("cuDate").lessThanEqual(businessDate1));
            Expression finalDateExp = dateExpr.and(dateExpr2);
            Expression creditRuleSetExpr = eb.get( "ruleSetID" ).equal(clrs.getObjectId());
            Expression fxiRemovalExpr = eb.get("orgShortName").notEqual("FXI");

            Expression finalExpr = creditRuleSetExpr.and( finalDateExp ).and(fxiRemovalExpr);

            if ( cptyOrg != null && cptyOrg.trim().length() > 0 )
            {
                Expression cptyOrgExpr = eb.get( "orgShortName" ).equal( cptyOrg.trim() );
                finalExpr = finalExpr.and( cptyOrgExpr );
                if ( cptyLE != null && cptyLE.trim().length() > 0 )
                {
                    Expression cptyLEExpr = eb.get( "legalEntityShortName" ).equal( cptyLE.trim() );
                    finalExpr = finalExpr.and( cptyLEExpr );
                }
            }

            Expression expUtil = ExpressionMath.multiply( ExpressionMath.divide( eb.get( "usedAmount" ), ExpressionMath.add( eb.get( "limitAmount" ), MIN_DOUBLE ) ), 100 );
            Expression adjustedLimitExpr = ExpressionMath.multiply( eb.get("limitAmount"), ExpressionMath.divide( eb.get( "suspensionPercent" ), 100 ));
            Expression expAvail = ExpressionMath.subtract( adjustedLimitExpr, ExpressionMath.add( eb.get( "usedAmount" ), eb.get( "reservedAmount" ) ) );

            ReportQuery reportQuery = new ReportQuery( new ExpressionBuilder() );
            reportQuery.setReferenceClass( com.integral.finance.creditLimit.CreditUtilizationViewC.class );
            reportQuery.useCollectionClass( ArrayList.class );

            reportQuery.setSelectionCriteria( finalExpr );

            reportQuery.addAttribute( "orgShortName", eb.get( "orgShortName" ) );
            reportQuery.addAttribute( "orgLongName", eb.get( "orgLongName" ) );
            reportQuery.addAttribute( "leShortName", eb.get( "legalEntityShortName" ) );
            reportQuery.addAttribute( "methodology", eb.get( "methodologyShortName" ) );
            reportQuery.addAttribute( "limit", eb.get( "limitAmount" ) );
            reportQuery.addAttribute( "limitCcy", eb.get( "ccyShortName" ) );
            reportQuery.addAttribute( "date", eb.get( "cuDate" ) );
            reportQuery.addAttribute( "usedAmt", eb.get( "usedAmount" ) );
            reportQuery.addAttribute( "utilizedPercentage", expUtil );
            reportQuery.addAttribute( "availableAmount", expAvail );
            reportQuery.addAttribute( "cptyRuleObjectId", eb.get( "cptyRuleID" ) );
            reportQuery.addAttribute( "credLmtRuleObjectId", eb.get( "ruleID" ) );
            reportQuery.addAttribute( "objectID", eb.get( "objectID" ) );
            reportQuery.addOrdering( ( ExpressionMath.multiply( ExpressionMath.divide( eb.get( "usedAmount" ), ExpressionMath.add( eb.get( "limitAmount" ), MIN_DOUBLE ) ), 100 ) ).descending() );
            reportQuery.addOrdering( eb.get( "orgShortName" ).ascending() );
            reportQuery.addOrdering( eb.get( "legalEntityShortName" ).ascending() );
            reportQuery.addOrdering( eb.get( "cuDate" ).ascending() );

            creditUtilCollection = ( ArrayList<ReportQueryResult> ) PersistenceFactory.newSession().executeQuery( reportQuery );

        }
        catch ( Exception e )
        {
            log.error( "CU.getAllCreditUtilizationsForProvider ", e );
        }
        return creditUtilCollection;
    }

    /**
     * Returns the expression for all counterparty credit limit rules associated with a credit provider organization.
     *
     * @param org        organization
     * @param onlyActive active only
     * @return expression
     */
    public static Expression getAllCounterpartyCreditLimitRulesForCreditProviderOrganizationExpression( Organization org, boolean onlyActive )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression expr = eb.get( "ruleSet" ).equal( CreditUtilC.getCreditLimitRuleSet( org ) );
        return onlyActive ? expr.and( eb.get( Entity.Status ).equal( Entity.ACTIVE_STATUS ) ) : expr;
    }

    /**
     * Returns all the counterparty credit limit rules associated with a counterparty organization. If specified then it only returns the active rules.
     *
     * @param cpo        credit provider org
     * @param cco        org
     * @param onlyActive active only
     * @return collection of counterparty credit limit rules.
     */
    public static Collection<CounterpartyCreditLimitRule> queryAllCounterpartyCreditLimitRulesForCounterpartyOrganization( Organization cpo, Organization cco, boolean onlyActive )
    {
        return CreditDataQueryServiceFactory.getCreditDataQueryService().getAllCptyRulesForCounterpartyOrg( cpo, cco, onlyActive );
    }

    /**
     * Returns the expression for all counterparty credit limit rules associated with a trading party. This will have one
     * org level credit limit rule and one trading party level credit limit rule.
     *
     * @param creditProviderOrg credit provider org
     * @param onlyActive        active only
     * @param creditCpty        credit cpty
     * @return expression
     */
    public static Collection<CounterpartyCreditLimitRule> getAllCounterpartyCreditLimitRulesForTradingParty( Organization creditProviderOrg, TradingParty creditCpty, boolean onlyActive )
    {
        Collection<CounterpartyCreditLimitRule> allCptyRules = queryAllCounterpartyCreditLimitRulesForCounterpartyOrganization(creditProviderOrg, creditCpty.getLegalEntityOrganization(), onlyActive);
        Collection<CounterpartyCreditLimitRule> tpCclrs = new HashSet<CounterpartyCreditLimitRule>( 2 );
        if ( allCptyRules != null )
        {
            for ( CounterpartyCreditLimitRule cclr : allCptyRules )
            {
                if ( cclr.getTradingParty() == null || creditCpty.isSameAs( cclr.getTradingParty() ) )
                {
                    if (!onlyActive || cclr.isActive())
                    {
                        tpCclrs.add( cclr );
                    }
                }
            }
        }
        return tpCclrs;
    }

    /**
     * Returns the expression for all counterparty credit limit rules associated with a trading party from the credit limit ruleset. This will have one
     * org level credit limit rule and one trading party level credit limit rule.
     *
     * @param clrs          credit limit rule set
     * @param creditCptyOrg credit cpty org
     * @param creditCpty    credit cpty
     * @param onlyActive    active only
     * @return expression
     */
    public static Collection<CounterpartyCreditLimitRule> getAllCounterpartyCreditLimitRulesForTradingPartyFromCreditLimitRuleSet( CreditLimitRuleSet clrs, Organization creditCptyOrg, TradingParty creditCpty, boolean onlyActive )
    {
        Collection<CounterpartyCreditLimitRule> allCptyRules = clrs.getRules();
        Collection<CounterpartyCreditLimitRule> tpCclrs = new HashSet<CounterpartyCreditLimitRule>( 2 );
        if ( allCptyRules != null )
        {
            for ( CounterpartyCreditLimitRule cclr : allCptyRules )
            {
                if ( ( cclr.getTradingParty() == null && creditCptyOrg.isSameAs( cclr.getTradingPartyOrganization() ) ) || creditCpty.isSameAs( cclr.getTradingParty() ) )
                {
                    if (!onlyActive || cclr.isActive())
                    {
                        tpCclrs.add( cclr );
                    }
                }
            }
        }
        return tpCclrs;
    }

    /**
     * Returns the counterparty credit limit rule for the trading party regardles whether it is active or passive.
     *
     * @param cpo credit provider org
     * @param cc  credit cpty
     * @return tp level counterparty credit limit rule
     */
    public static CounterpartyCreditLimitRule getTradingPartyLevelCounterpartyCreditLimitRule( Organization cpo, TradingParty cc )
    {
        return CreditDataQueryServiceFactory.getCreditDataQueryService().getTradingPartyLevelCptyRule(cpo, cc);
    }

    /**
     * Returns the counterparty credit limit rule for the counterparty organization regardles whether it is active or passive.
     *
     * @param cpo credit provider org
     * @param cc  credit cpty
     * @return org level counterparty rule
     */
    public static CounterpartyCreditLimitRule getOrgLevelCounterpartyCreditLimitRule( Organization cpo, TradingParty cc )
    {
        return CreditDataQueryServiceFactory.getCreditDataQueryService().getOrgLevelCptyRule(cpo, cc);
    }

    /**
     * Returns the trading party level counterparty credit limit rule from the collection of org level and tp level counterparty rules.
     *
     * @param tpCptyRules cpty rules for tp
     * @return tp level counterparty rule
     */
    public static CounterpartyCreditLimitRule getTradingPartyLevelCounterpartyCreditLimitRule( Collection<CounterpartyCreditLimitRule> tpCptyRules )
    {
        for ( CounterpartyCreditLimitRule cclr : tpCptyRules )
        {
            if ( cclr.getTradingParty() != null )
            {
                return cclr;
            }
        }
        return null;
    }

    /**
     * Returns the org level counterparty credit limit rule from the collection of org level rule and tp level credit limit rule.
     *
     * @param tpCptyRules cpty rules for tp
     * @return org level counterparty credit limit rule
     */
    public static CounterpartyCreditLimitRule getOrgLevelCounterpartyCreditLimitRule( Collection<CounterpartyCreditLimitRule> tpCptyRules )
    {
        for ( CounterpartyCreditLimitRule cclr : tpCptyRules )
        {
            if ( cclr.getTradingParty() == null )
            {
                return cclr;
            }
        }
        return null;
    }

    /**
     * Returns the active counterparty credit limit rule for the trading party. The active status determines at which level credit exposure is now set.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit cpty
     * @return credit exposure counterparty credit limit rule
     */
    public static CounterpartyCreditLimitRule getActiveCounterpartyCreditLimitRuleForTradingParty( Organization creditProviderOrg, TradingParty creditCpty )
    {
        return getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty, false );
    }

    /**
     * Returns the active counterparty credit limit rule for the trading party. The active status determines at which level credit exposure is now set.
     *
     * @param cpo        credit provider org
     * @param cc         credit cpty
     * @param checkCache check cache
     * @return credit exposure counterparty credit limit rule
     */
    public static CounterpartyCreditLimitRule getActiveCounterpartyCreditLimitRuleForTradingParty( Organization cpo, TradingParty cc, boolean checkCache )
    {
        if( cc == null ){
            return null;
        }
        CounterpartyCreditLimitRule cclr = null;
        if ( checkCache )
        {
            cclr = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getActiveCounterpartyCreditLimitRule( cpo, cc );
            if ( cclr == null )
            {
                CreditRelationship cr = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditRelationship ( cpo, cc );
                if ( cr != null && cr.isInactivated () )
                {
                    return null;
                }
            }
        }
        if ( cclr == null || cclr.getChildrenRules ().size () == 0 )
        {
            long t0 = System.currentTimeMillis();
            if ( cclr != null )
            {
                log.info ( "CU.getActiveCounterpartyCreditLimitRuleForTradingParty - stale cclr found. cclr=" + cclr + ",cpo=" + cpo + ",cc=" + cc );
                CreditUtilizationManagerC.getInstance ().getCreditUtilizationCache ().resetCreditCounterpartyCptyRules ( cpo, cc );
                cclr = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getActiveCounterpartyCreditLimitRule( cpo, cc );
                if ( cclr != null && cclr.getChildrenRules ().size () != 0 )
                {
                    log.info ( "CU.getActiveCounterpartyCreditLimitRuleForTradingParty - retrieved proper cclr after reset. cclr=" + cclr + ",cpo=" + cpo + ",cc=" + cc );
                    return cclr;
                }
            }
            cclr = CreditDataQueryServiceFactory.getCreditDataQueryService().getActiveCptyRuleForTradingParty( cpo, cc );
            if ( checkCache || LogUtil.getSwitch() )
            {
                log.info( new StringBuilder( 200 ).append( "CU.getActiveCounterpartyCreditLimitRuleForTradingParty : Querying cpty credit limit rule for cpo=" )
                        .append( cpo ).append( ",cc=" ).append( cc ).append( " took " )
                        .append( System.currentTimeMillis() - t0 ).append( ",cclr=" ).append( cclr ).append( ",checkCache=" )
                        .append( checkCache ).append( ",logUtil.switch=" ).append( LogUtil.getSwitch() ).toString() );
            }
        }
        return cclr;
    }

    /**
     * Returns the credit limit classification which is supported by the credit utilization calculator.
     *
     * @param calc calc
     * @return credit limit classification
     */
    public static CreditLimitClassification getCreditLimitClassification( CreditUtilizationCalculator calc )
    {
        if ( calc != null )
        {
            if ( isSingleCreditLimitRule( calc ) )
            {
                return GROSS_NOTIONAL_CLASSIFICATION;
            }
            else if ( isDailyCreditLimitRule( calc ) )
            {
                return DAILY_SETTLEMENT_CLASSIFICATION;
            }
        }
        return null;
    }

    /**
     * Returns whether credit is maintained at the org level or counterparty level.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCptyOrg     credit cpty org
     * @return isOrgLevelCredit
     */
    public static boolean isOrgLevelCredit( Organization creditProviderOrg, Organization creditCptyOrg )
    {
        TradingParty tp = CounterpartyUtilC.getTradingParty( getDefaultlegalEntity( creditCptyOrg ), creditProviderOrg );
        CounterpartyCreditLimitRule orgCclr = getOrgLevelCounterpartyCreditLimitRule( creditProviderOrg, tp );
        return isOrgLevelCreditExposure(orgCclr);
    }

    /**
     * Returns whether the counterparty credit limit rule is at the org level or at the trading party level.
     *
     * @param cclr cclr
     * @return org level
     */
    public static boolean isOrgLevelCreditExposure( CounterpartyCreditLimitRule cclr )
    {
        return cclr != null && cclr.isOrgLevel();
    }

    /**
     * Returns whether the External Credit Enabled
     *
     * @param cclr cclr
     */
    public static boolean isExternalCreditEnabled( CounterpartyCreditLimitRule cclr )
    {
        return cclr != null && cclr.isExternalCreditEnabled();
    }

    /**
     * Releases the transaction if it is created inside the service method.
     *
     * @param tx transaction
     */
    public static void releaseTransaction( IdcTransaction tx )
    {
        if ( tx != null )
        {
            if ( log.isDebugEnabled() )
            {
                log.debug( "CU.releaseTransaction : Releasing the transaction created by the service." );
            }
            tx.release();
        }
    }

    /**
     * Returns the credit limit classification based on the name specified
     *
     * @param type type
     * @return credit limit classification such as gross notional, daily settlement etc.
     */
    public static CreditLimitClassification getCreditLimitClassification( String type )
    {
        try
        {
            if ( type != null )
            {
                return ( CreditLimitClassification ) ReferenceDataCacheC.getInstance().getEntityByShortName( type, CreditLimitClassification.class, null, null );
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.getCreditLimitClassification : type=" + type, e );
        }
        return null;
    }

    /**
     * Returns the credit utilization calculaotr based on the name specified
     *
     * @param calcName calculator name
     * @return credit utilization calculator such as daily settlement limit, aggregate limit etc.
     */
    public static CreditUtilizationCalculator getCreditUtilizationCalculator( String calcName )
    {
        try
        {
            if ( calcName != null )
            {
                CreditUtilizationCalculator calc =  ( CreditUtilizationCalculator ) ReferenceDataCacheC.getInstance().getEntityByShortName( calcName, CreditUtilizationCalculatorC.class, null, null );
                if ( calc == null )
                {
                    log.info( "CU.getCreditUtilizationCalculator -  No credit utilization calculator found with name=" + calcName + ",className=CreditUtilizationCalculatorC.class");
                }
                return calc;
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.getCreditUtilizationCalculator : calcName=" + calcName, e );
        }
        return null;
    }

    /**
     * Returns true if credit utilization calculator supports single credit limit rule type.
     *
     * @param calc calculator
     * @return boolean
     */
    public static boolean isSingleCreditLimitRule( CreditUtilizationCalculator calc )
    {
        return AGGREGATE_LIMIT_CALCULATOR.isSameAs( calc ) || NET_OPEN_POSITION_CALCULATOR.isSameAs( calc )
                || GROSS_AGGREGATE_LIMIT_CALCULATOR.isSameAs( calc ) || AGGREGATE_NPR_SETTLEMENT_CALCULATOR.isSameAs( calc )
                || AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR.isSameAs ( calc ) || AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR.isSameAs( calc )
                || AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR.isSameAs ( calc ) || AGGREGATE_NET_CREDITNET_SETTLEMENT_CALCULATOR.isSameAs( calc );
    }

    /**
     * Returns true if credit utilization calculator supports single credit limit rule.
     *
     * @param calc calculator
     * @return boolean
     */
    public static boolean isDailyCreditLimitRule( CreditUtilizationCalculator calc )
    {
        return DAILY_SETTLEMENT_LIMIT_CALCULATOR.isSameAs( calc ) || GROSS_DAILY_LIMIT_CALCULATOR.isSameAs( calc );
    }


    /**
     * Returns the credit limit rule from the counterparty credit limit rule matching the credit limit classification.
     *
     * @param cclr cclr
     * @param clsf clsf
     * @return credit limit rule.
     */
    public static CreditLimitRule getCreditLimitRule( CounterpartyCreditLimitRule cclr, CreditLimitClassification clsf )
    {
        Collection<Rule> rules = cclr.getChildrenRules();
        for ( Rule rule : rules )
        {
            CreditLimitRule clr = ( CreditLimitRule ) rule;
            if ( clsf != null && clsf.isSameAs( clr.getClassification() ) )
            {
                return clr;
            }
        }
        return null;
    }

    /**
     * Returns the default legal entity for the organization.
     *
     * @param org org
     * @return default legal entity
     */
    public static LegalEntity getDefaultlegalEntity( Organization org )
    {
        return org.getDefaultDealingEntity();
    }

    /**
     * Returns the expression for credit utilizations of the credit limit rule which has non-zero used or reserved amounts.
     * This includes daily and single type credit utilizations.
     *
     * @param clr credit limit rule
     * @return expression
     */
    public static Expression getUtilizedCreditUtilizationExpression( CreditLimitRule clr )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression activeCUExpr = eb.get(Entity.Status).equal( Entity.ACTIVE_STATUS );
        Expression clrExpr = eb.get("creditLimitRule").equal( clr );
        Expression usedExpr = eb.get( "usedAmount" ).notEqual( 0 ).or( eb.get( "reservedAmount" ).notEqual( 0 ) );
        Expression dateExpr = eb.get( "date" ).isNull().or( eb.get( "date" ).greaterThanEqual( EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate().asSqlDate() ) );
        return activeCUExpr.and(clrExpr).and(usedExpr).and(dateExpr);
    }

    /**
     * Returns the expression for credit utilizations of the credit limit rule set which has non-zero used or reserved amounts.
     * This includes daily and single type credit utilizations.
     *
     * @param clrs credit limit rule set
     * @return expression
     */
    public static Expression getUtilizedCreditUtilizationExpression( CreditLimitRuleSet clrs )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression activeCUExpr = eb.get( Entity.Status ).equal( Entity.ACTIVE_STATUS );
        Expression activeClrExpr = eb.get( "creditLimitRule" ).get( Entity.Status ).equal( Entity.ACTIVE_STATUS );
        Expression activeCclrExpr = eb.get( "creditLimitRule" ).get( "ruleParent" ).get(Entity.Status).equal( Entity.ACTIVE_STATUS );
        Expression clrExpr = eb.get( "creditLimitRule" ).get( "ruleParent" ).get("ruleSet").equal( clrs );
        Expression usedExpr = eb.get( "usedAmount" ).notEqual( 0 ).or( eb.get( "reservedAmount" ).notEqual( 0 ) );
        Expression dateExpr = eb.get( "date" ).isNull().or( eb.get( "date" ).greaterThanEqual( EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate().asSqlDate() ) );
        return activeCUExpr.and( activeClrExpr ).and( activeCclrExpr ).and(clrExpr).and(usedExpr).and(dateExpr);
    }

    /**
     * Returns the collection of aggregate credit utilizations for the organization specified.
     *
     * @param cpo credit provider org
     * @return aggregate credit utilization collection
     */
    public static Collection<CreditUtilization> getAggregateCreditUtilizations( Organization cpo )
    {
        return CreditDataQueryServiceFactory.getCreditDataQueryService().getAggregateCreditUtilizations(cpo);
    }

    /**
     * Returns the collection of credit utilizations which has non-zero used or reserved amounts.
     *
     * @param clr credit limit rule
     * @return credit utilization collection
     */
    public static Collection<CreditUtilization> getUtilizedCreditUtilizations( CreditLimitRule clr )
    {
        try
        {
            if( CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationLookupSpacesEnabled(clr.getNamespace().getShortName()))
            {
                return CreditDataQueryServiceFactory.getCreditReferenceDataService().getAllCreditUtilizations( clr );
            }

            ReadAllQuery raq = new ReadAllQuery( CreditUtilization.class );
            raq.setSelectionCriteria( getUtilizedCreditUtilizationExpression( clr ) );
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression dateExpr = eb.get( "date" );
            raq.addOrdering( dateExpr );
            return ( Collection<CreditUtilization> ) PersistenceFactory.newSession().executeQuery(raq);
        }
        catch ( Exception e )
        {
            log.error( "CU.getUtilizedCreditUtilizations : clr=" + clr, e );
        }
        return new ArrayList<CreditUtilization>();
    }

    /**
     * Returns the collection of credit utilizations which has non-zero used or reserved amounts for the credit limit ruleset.
     *
     * @param clrs credit limit rule set
     * @return credit utilization collection
     */
    public static Collection<CreditUtilization> getUtilizedCreditUtilizations( CreditLimitRuleSet clrs )
    {
        try
        {
            return ( Collection<CreditUtilization> ) PersistenceFactory.newSession().readAllObjects(CreditUtilization.class, getUtilizedCreditUtilizationExpression(clrs));
        }
        catch ( Exception e )
        {
            log.error( "CU.getUtilizedCreditUtilizations : clrs=" + clrs, e );
        }
        return new ArrayList<CreditUtilization>();
    }

    /**
     * Returns the collection of credit types which are supported for the organization type. This is obtained from the credit configuration property files.
     *
     * @param creditProviderOrg credit provider org
     * @return credit types
     */
    public static Collection<CreditLimitClassification> getConfiguredSupportedCreditLimitClassifications( Organization creditProviderOrg )
    {
        Collection<CreditLimitClassification> supportedTypes = new HashSet<CreditLimitClassification>( 2 );
        Collection<String> types = CreditConfigurationFactory.getCreditConfigurationMBean().getSupportedCreditTypes();

        if ( types != null && !types.isEmpty() )
        {
            for ( String type : types )
            {
                if ( GROSS_NOTIONAL_CLASSIFICATION_SHORT_NAME.equals( type ) )
                {
                    supportedTypes.add( GROSS_NOTIONAL_CLASSIFICATION );
                }
                else if ( DAILY_SETTLEMENT_CLASSIFICATION_SHORT_NAME.equals( type ) )
                {
                    supportedTypes.add( DAILY_SETTLEMENT_CLASSIFICATION );
                }
                else
                {
                    log.error( "CU.getSupportedCreditLimitClassifications : Unsupported credit type=" + type );
                }
            }
        }
        else
        {
            log.error( new StringBuilder( 200 ).append( "CU.getSupportedCreditLimitClassifications : Supported credit types not specified. org=" )
                    .append( creditProviderOrg ).toString() );
        }
        return supportedTypes;
    }

    /**
     * Returns true if credit utilization calculator is a netting calculator.
     *
     * @param calc calc
     * @return is netting allowed
     */
    public static boolean isNettingCalculator( CreditUtilizationCalculator calc )
    {
        return AGGREGATE_LIMIT_CALCULATOR.isSameAs( calc )
                || DAILY_SETTLEMENT_LIMIT_CALCULATOR.isSameAs( calc )
                || NET_OPEN_POSITION_CALCULATOR.isSameAs( calc ) || AGGREGATE_NPR_SETTLEMENT_CALCULATOR.isSameAs( calc )
                || AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR.isSameAs ( calc )
                || AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR.isSameAs( calc )
                || AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR.isSameAs ( calc )
                || AGGREGATE_NET_CREDITNET_SETTLEMENT_CALCULATOR.isSameAs ( calc );
    }

    /**
     * Returns the default credit utilization calculator for single credit limit rules.
     *
     * @param creditProviderOrg credit provider org
     * @return netting calculator
     */
    public static CreditUtilizationCalculator getDefaultAggregateCreditUtilizationCalculator( Organization creditProviderOrg )
    {
        CreditLimitOrgFunction orgFunc = getCreditLimitOrgFunction( creditProviderOrg );
        return orgFunc != null ? orgFunc.getDefaultAggregateCreditUtilizationCalculator() : GROSS_AGGREGATE_LIMIT_CALCULATOR;
    }

    /**
     * Returns the default credit utilization calculator for daily credit limit rule types.
     *
     * @param creditProviderOrg credit provider org
     * @return netting calculator
     */
    public static CreditUtilizationCalculator getDefaultDailyCreditUtilizationCalculator( Organization creditProviderOrg )
    {
        CreditLimitOrgFunction orgFunc = getCreditLimitOrgFunction( creditProviderOrg );
        return orgFunc != null ? orgFunc.getDefaultDailyCreditUtilizationCalculator() : GROSS_DAILY_LIMIT_CALCULATOR;
    }

    /**
     * Returns a system wide default credit utilization calculator for a credit limit classification.
     *
     * @param clsf clsf
     * @return credit utilization calculator
     */
    public static CreditUtilizationCalculator getDefaultCreditUtilizationCalculator( CreditLimitClassification clsf )
    {
        if ( DAILY_SETTLEMENT_CLASSIFICATION.isSameAs( clsf ) )
        {
            return GROSS_DAILY_LIMIT_CALCULATOR;
        }
        else if ( GROSS_NOTIONAL_CLASSIFICATION.isSameAs( clsf ) )
        {
            return GROSS_AGGREGATE_LIMIT_CALCULATOR;
        }
        return null;
    }

    /**
     * Returns the default credit utilization calculator for the credit provider organization based on the credit rule type.
     *
     * @param creditProviderOrg credit provider org
     * @param clsf              clsf
     * @return netting calculator
     */
    public static CreditUtilizationCalculator getDefaultCreditUtilizationCalculator( Organization creditProviderOrg, CreditLimitClassification clsf )
    {
        if ( DAILY_SETTLEMENT_CLASSIFICATION.isSameAs( clsf ) )
        {
            return getDefaultDailyCreditUtilizationCalculator( creditProviderOrg );
        }
        else if ( GROSS_NOTIONAL_CLASSIFICATION.isSameAs( clsf ) )
        {
            return getDefaultAggregateCreditUtilizationCalculator( creditProviderOrg );
        }
        return null;
    }

    /**
     * Returns the market data set for the organization. If market data set is specified at the org function level, then it
     * is returned. Otherwise, market data set associated with the credit limit rule set is returned.
     *
     * @param org org
     * @return market data set
     */
    public static MarketDataSet getMarketDataSet( Organization org )
    {
        CreditLimitOrgFunction orgFunction = getCreditLimitOrgFunction( org );
        if ( orgFunction != null && orgFunction.getMarketDataSet() != null )
        {
            return orgFunction.getMarketDataSet();
        }
        CreditLimitRuleSet clrs = getCreditLimitRuleSet( org );
        return clrs != null ? clrs.getMarketDataSet() : null;
    }

    public static FXMarketDataSet getMarketDataSet( CreditUtilization cu )
    {
        try
        {
            return ( FXMarketDataSet ) ( ( CreditLimitRuleSet ) cu.getCreditLimitRule().getParentRule().getRuleSet() ).getMarketDataSet();
        }
        catch ( Exception e )
        {
            log.error( "CU.getMarketDataSet : Exception while getting the market data set. cu=" + cu, e );
        }
        return null;
    }

    public static CreditLimitRuleSet getCreditLimitRuleSet ( CreditUtilization cu )
    {
        try
        {
            return ( CreditLimitRuleSet ) cu.getCreditLimitRule().getParentRule().getRuleSet();
        }
        catch ( Exception e )
        {
            log.error( "CU.getCreditLimitRuleSet : Exception while getting the credit limit rule set. cu=" + cu, e );
        }
        return null;
    }

    public static Organization getCreditProviderOrganization ( CreditUtilization cu )
    {
        return getCreditProviderOrganization ( getCreditLimitRuleSet ( cu ) );
    }

    public static String getCreditUtilizationDescription ( CreditUtilization cu )
    {
        try
        {
            CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule ().getParentRule ();
            CreditLimitRuleSet clrs = ( CreditLimitRuleSet ) cclr.getRuleSet ();
            Organization cpo = getCreditProviderOrganization ( clrs );
            Organization cco = cclr.getTradingPartyOrganization ();
            TradingParty cc = cclr.getTradingParty ();
            StringBuilder sb = new StringBuilder( 100 ).append ( cpo.getShortName () ).append ( "->" );
            if ( cc != null )
            {
                sb.append ( cc.getShortName () ).append ( '@' ).append ( cco.getShortName () );
            }
            else
            {
                sb.append ( cco.getShortName () );
            }
            if ( cu instanceof DailyCreditUtilization )
            {
                sb.append ( ':' ).append ( ( ( DailyCreditUtilization ) cu ).getDate ().getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) );
            }
            return sb.toString ();
        }
        catch ( Exception e )
        {
            log.error ( "CU.getCreditUtilizationDescription : exception while getting the credit line info from CU=" + cu, e );
        }
        return null;
    }

    /**
     * Returns a log message with information about the credit parties.
     *
     * @param contextInfo      context info
     * @param creditEntity     credit entity
     * @param cpl credit provider le
     * @param cc       credit cpty
     * @param cco    credit cpty org
     * @return log message
     */
    public static String getCreditWorkflowLogMessage( String contextInfo, Entity creditEntity, LegalEntity cpl, TradingParty cc, Organization cco )
    {
        String cpoName = cpl != null ? cpl.getOrganization().getShortName() : null;
        String ccName = cc != null ? cc.getShortName() : null;
        String ccoName = cco != null ? cco.getShortName () : null;
        return new StringBuilder( 200 ).append( contextInfo ).append( " - TxId=" )
                .append( getTransactionId( creditEntity ) ).append( ",cr=" ).append( cpoName ).append( '>' )
                .append( ccName ).append( '@' ).append( ccoName ).toString();
    }

    /**
     * Returns a log message with information of credit parties in a bilateral credit workflow.
     *
     * @param contextInfo      context info
     * @param creditEntity     credit entity
     * @param creditProviderLe credit provider le
     * @param creditCpty       credit cpty
     * @param isLe1Maker       le1 maker
     * @param cwm              credit workflow message
     * @return log message
     */
    public static String getCreditWorkflowResultLogMessage( String contextInfo, Entity creditEntity, LegalEntity creditProviderLe, TradingParty creditCpty, boolean isLe1Maker, CreditWorkflowMessage cwm )
    {
        StringBuilder buf = new StringBuilder( 200 );
        buf.append( contextInfo ).append( " - TxId=" )
                .append( getTransactionId( creditEntity ) ).append( ",creditProviderLe=" ).append( creditProviderLe != null ? creditProviderLe.getShortName() : null )
                .append( ",creditCpty=" ).append( creditCpty != null ? creditCpty.getShortName() : null ).append( ",isLe1Maker=" )
                .append( isLe1Maker ).append( ",cwm.status=" ).append( cwm != null ? cwm.getStatus() : null );
        if ( cwm != null && cwm.getErrors() != null )
        {
            for (Object o : cwm.getErrors()) {
                ErrorMessage errMsg = (ErrorMessage) o;
                buf.append(",errorMsg=").append(errMsg.getErrorCode());
            }
        }
        return buf.toString();
    }

    /**
     * Returns a log message with information of credit parties in a bilateral credit workflow.
     *
     * @param contextInfo  context info
     * @param creditEntity credit entity
     * @param le1          le1
     * @param le2          le2
     * @param isLe1Maker   le1 maker
     * @param cwm          credit workflow message
     * @return log message
     */
    public static String getBilateralCreditWorkflowLogMessage( String contextInfo, Entity creditEntity, LegalEntity le1, LegalEntity le2, boolean isLe1Maker, CreditWorkflowMessage cwm )
    {
        StringBuilder buf = new StringBuilder( 200 );
        buf.append( contextInfo ).append( " - TxId=" )
                .append( getTransactionId( creditEntity ) ).append( ",le1=" ).append( le1 != null ? le1.getShortName() : null )
                .append( ",le2=" ).append( le2 != null ? le2.getShortName() : null ).append( ",isLe1Maker=" )
                .append( isLe1Maker ).append( ",cwm.status=" ).append( cwm != null ? cwm.getStatus() : null );
        if ( cwm != null && cwm.getErrors() != null )
        {
            for (Object o : cwm.getErrors()) {
                ErrorMessage errMsg = (ErrorMessage) o;
                buf.append(",errorMsg=").append(errMsg.getErrorCode());
            }
        }
        return buf.toString();
    }

    /**
     * Returns the transaction id for the credit entity.
     *
     * @param creditEntity credit entity
     * @return transaction id
     */
    public static String getTransactionId( Entity creditEntity )
    {
        try
        {
            if ( creditEntity instanceof Trade )
            {
                return ( ( Trade ) creditEntity ).getTransactionID();
            }
            else if ( creditEntity instanceof Quote )
            {
                return ( ( Quote ) creditEntity ).getTransactionID();
            }
            else if ( creditEntity instanceof Request )
            {
                return ( ( Request ) creditEntity ).getTransactionID();
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.getTransactionId : Exception while getting the transaction id info from entity=" + creditEntity, e );
        }
        return null;
    }

    /**
     * Returns the trade date for the credit entity.
     *
     * @param creditEntity credit entity
     * @return trade date
     */
    public static IdcDate getTradeDate( Entity creditEntity )
    {
        try
        {
            if ( creditEntity instanceof Trade )
            {
                return ( ( Trade ) creditEntity ).getTradeDate();
            }
            else if ( creditEntity instanceof Quote )
            {
                return ( ( Quote ) creditEntity ).getRequest().getCreatedBusinessDate();
            }
            else if ( creditEntity instanceof Request )
            {
                return ( ( Request ) creditEntity ).getCreatedBusinessDate();
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.getTradeDate : Exception while getting the trade date info from entity=" + creditEntity, e );
        }
        return null;
    }


    /**
     * Returns the first error from the list of error messages of credit workflow message.
     *
     * @param cwm credit workflow message
     * @return error code
     */
    public static String getErrorCode( CreditWorkflowMessage cwm )
    {
        if ( cwm != null && cwm.getErrors() != null )
        {
            return ( ( ErrorMessage ) cwm.getErrors().iterator().next() ).getErrorCode();
        }
        return null;
    }

    public static boolean isAutoStopOutEnabled ( CounterpartyCreditLimitRule cclr )
    {
        if ( cclr != null )
        {
            Double stopOutPercentage = cclr.getStopOutPercentage ();
            if ( stopOutPercentage == null || stopOutPercentage <= 0.0 )
            {
                stopOutPercentage =  ( ( CreditLimitRuleSet ) cclr.getRuleSet () ).getStopOutPercentage ();
            }
            return stopOutPercentage != null && stopOutPercentage > 0.0;
        }
        return false;
    }

    /**
     * Returns the percentage of utilization at which positions are closed out.
     *
     * @param cclr cpty rule
     * @return percentage
     */
    public static Double getStopOutPercentage( CounterpartyCreditLimitRule cclr )
    {
        if ( cclr != null )
        {
            Double tpPercent = cclr.getStopOutPercentage ();
            return tpPercent != null ? tpPercent : (( CreditLimitRuleSet ) cclr.getRuleSet ()).getStopOutPercentage ();
        }
        return null;
    }

    /**
     * Returns the percentage of utilization at which positions are closed out.
     *
     * @param cu credit utilization
     * @return percentage
     */
    public static Double getStopOutPercentage( CreditUtilization cu )
    {
        CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule().getParentRule();
        return getStopOutPercentage ( cclr );
    }

    /**
     * Returns the percentage of utilization at which trading suspension email will be sent.
     *
     * @param cu credit utilization
     * @return percentage
     */
    public static Double getSuspensionPercentage( CreditUtilization cu )
    {
        CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule().getParentRule();
        Double tpPercent = cclr.getSuspensionPercentage();
        return tpPercent != null ? tpPercent : ( ( CreditLimitRuleSet ) cclr.getRuleSet() ).getSuspensionPercentage();
    }

    /**
     * Returns the percentage of utilization at which the warning email will be sent.
     *
     * @param cu credit utilization
     * @return percentage
     */
    public static Double getWarningPercentage( CreditUtilization cu )
    {
        CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule().getParentRule();
        Double tpPercent = cclr.getWarningPercentage();
        return tpPercent != null ? tpPercent : ( ( CreditLimitRuleSet ) cclr.getRuleSet() ).getWarningPercentage();
    }

    /**
     * Returns the percentage of utilization at which notification email will be sent out.
     *
     * @param cu credit utilization
     * @return percentage
     */
    public static Double getNotificationPercentage( CreditUtilization cu )
    {
        CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule().getParentRule();
        Double tpPercent = cclr.getNotificationPercentage();
        return tpPercent != null ? tpPercent : ( ( CreditLimitRuleSet ) cclr.getRuleSet() ).getNotificationPercentage();
    }

    /**
     * Returns the list of email addresses based the counterparty level as well as org level notification email address settting.
     *
     * @param cu credit utilization
     * @return email ids
     */
    public static Collection<String> getNotificationEmailAddress( CreditUtilization cu )
    {
        CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule().getParentRule();
        return getNotificationEmailAddress( cclr );
    }

    /**
     * Returns the list of email addresses based the counterparty level as well as org level notification email address settting.
     *
     * @param cclr cclr
     * @return email ids
     */
    public static Collection<String> getNotificationEmailAddress( CounterpartyCreditLimitRule cclr )
    {
        Collection<String> emailList = null;
        String cptyEmail = cclr.getNotificationEmailAddress();
        String orgEmail = ( ( CreditLimitRuleSet ) cclr.getRuleSet() ).getNotificationEmailAddress();
        if ( cptyEmail != null || orgEmail != null )
        {
            emailList = new HashSet<String>();
            if ( cptyEmail != null )
            {
                emailList.addAll( IdcUtilC.arrayAsArrayList( IdcUtilC.getSubstring( cptyEmail, cptyEmail.indexOf( "," ) != -1 ? "," : ";" ) ) );
            }
            if ( orgEmail != null )
            {
                emailList.addAll( IdcUtilC.arrayAsArrayList( IdcUtilC.getSubstring( orgEmail, orgEmail.indexOf( "," ) != -1 ? "," : ";" ) ) );
            }
        }
        return emailList;
    }

    /**
     * Returns the list of email addresses based the counterparty level as well as org level notification email address settting.
     *
     * @param cpo credit provider org
     * @return email ids
     */
    public static Collection<String> getNotificationEmailAddress( Organization cpo, TradingParty cc  )
    {
        Collection<String> emailList = null;
        CounterpartyCreditLimitRule cclr = getActiveCounterpartyCreditLimitRuleForTradingParty ( cpo, cc, true );
        if ( cclr != null )
        {
            String cptyEmail = cclr.getNotificationEmailAddress ();
            String orgEmail = ( ( CreditLimitRuleSet ) cclr.getRuleSet () ).getNotificationEmailAddress ();
            if ( cptyEmail != null || orgEmail != null )
            {
                emailList = new HashSet<String> ();
                if ( cptyEmail != null )
                {
                    emailList.addAll ( IdcUtilC.arrayAsArrayList ( IdcUtilC.getSubstring ( cptyEmail, cptyEmail.indexOf ( "," ) != - 1 ? "," : ";" ) ) );
                }
                if ( orgEmail != null )
                {
                    emailList.addAll ( IdcUtilC.arrayAsArrayList ( IdcUtilC.getSubstring ( orgEmail, orgEmail.indexOf ( "," ) != - 1 ? "," : ";" ) ) );
                }
            }
        }
        return emailList;
    }

    /**
     * Return the from email address for the email notifications.
     *
     * @param cclr cclr
     * @return sender email address
     */
    public static String getSenderEmailAddress( CounterpartyCreditLimitRule cclr )
    {
        String senderEmailAddress = null;
        try
        {
            senderEmailAddress = getCreditLimitOrgFunction( ( ( CreditLimitRuleSet ) cclr.getRuleSet() ).getOrganization() ).getSenderEmailAddress();
        }
        catch ( Exception e )
        {
            log.error( "CU.getSenderEmailAddress : exception. cclr=" + cclr );
        }
        return senderEmailAddress != null ? senderEmailAddress : CreditConfigurationFactory.getCreditConfigurationMBean().getCreditNotificationSenderEmailAddress();
    }

    /**
     * Return the from email address for the email notifications.
     *
     * @param cpo credit provider org
     * @return sender email address
     */
    public static String getSenderEmailAddress( Organization cpo )
    {
        String senderEmailAddress = null;
        try
        {
            senderEmailAddress = getCreditLimitOrgFunction( cpo ).getSenderEmailAddress();
        }
        catch ( Exception e )
        {
            log.error( "CU.getSenderEmailAddress : exception. cpo=" + cpo );
        }
        return senderEmailAddress != null ? senderEmailAddress : CreditConfigurationFactory.getCreditConfigurationMBean().getCreditNotificationSenderEmailAddress();
    }

    /**
     * Return the credit workflow message created and initialised with the credit provider organization, credit counterparty
     * and credit limit ruleset of the organization.
     *
     * @param creditCpty       credit counterparty
     * @param creditCptyOrg    credit counterparty organization
     * @param creditProviderLe credit provider organization legal entity
     * @return the credit workflow message for performing the credit action
     */
    public static CreditWorkflowMessage initialiseCreditWorkflowMessage( TradingParty creditCpty, Organization creditCptyOrg, LegalEntity creditProviderLe )
    {
        CreditWorkflowMessage cwm = new CreditWorkflowMessageImpl();
        Organization creditProviderOrg = creditProviderLe.getOrganization();
        setCreditWorkflowMessageStatus( cwm, true );
        cwm.setTradingParty( creditCpty );
        cwm.setOrganization( creditProviderOrg );
        cwm.setLegalEntity( creditProviderLe );
        if ( creditCptyOrg == null && creditCpty != null )
        {
            creditCptyOrg = creditCpty.getLegalEntityOrganization();
        }
        cwm.setTradingPartyOrganization( creditCptyOrg );
        return cwm;
    }

    /**
     * Returns the credit limit currency for the credit provider organization.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCptyOrg     credit cpty org
     * @param creditCpty        credit cpty
     * @return limitCcy
     */
    public static Currency getCounterpartyCreditLimitCurrency( Organization creditProviderOrg, Organization creditCptyOrg, TradingParty creditCpty )
    {
        try
        {
            if ( creditCpty == null && creditCptyOrg != null )
            {
                creditCpty = CounterpartyUtilC.getTradingParty( getDefaultlegalEntity( creditCptyOrg ), creditProviderOrg );
            }
            if ( creditCpty != null )
            {
                CounterpartyCreditLimitRule cclr = getOrgLevelCounterpartyCreditLimitRule( creditProviderOrg, creditCpty );
                if ( cclr != null )
                {
                    Collection<Rule> rules = cclr.getChildrenRules();
                    for ( Rule rule : rules )
                    {
                        CreditLimitRule creditLimitRule = ( CreditLimitRule ) rule;
                        if ( creditLimitRule.getCurrency() != null )
                        {
                            return creditLimitRule.getCurrency();
                        }
                    }
                }
                else
                {
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( new StringBuilder( 200 ).append( "CU.getCounterpartyCreditLimitCurrency : No org level cpty rule for creditProviderOrg= " )
                                .append( creditProviderOrg ).append( ",creditCptyOrg=" ).append( creditCptyOrg ).append( ",creditCpty=" )
                                .append( creditCpty ).toString() );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.getCounterpartyCreditLimitCurrency : creditProviderOrg=" + creditProviderOrg, e );
        }
        return getLimitCurrency(creditProviderOrg);
    }

    public static Currency getLimitCurrency(CounterpartyCreditLimitRule cclr, Organization creditProviderOrg)
    {
        if ( cclr != null )
        {
            Collection<Rule> rules = cclr.getChildrenRules();
            for ( Rule rule : rules )
            {
                CreditLimitRule creditLimitRule = ( CreditLimitRule ) rule;
                if ( creditLimitRule.getCurrency() != null )
                {
                    return creditLimitRule.getCurrency();
                }
            }
        }
        return getLimitCurrency(creditProviderOrg);
    }
    /**
     * Returns the reporting currency of the organization.
     *
     * @param creditProviderOrg credit provider org
     * @return reporting currency
     */
    public static Currency getReportingCurrency( Organization creditProviderOrg )
    {
        LegalEntity defaultLe = CreditUtilC.getDefaultlegalEntity( creditProviderOrg );
        BusinessCenter businessCenter = defaultLe.getBusinessCenter();
        return businessCenter.getReportingCurrency();
    }

    /**
     * Returns the credit limit currency for the credit provider organization.
     *
     * @param creditProviderOrg credit provider org
     * @return limitCcy
     */
    public static Currency getLimitCurrency( Organization creditProviderOrg )
    {
        try
        {
            Currency ccy = getCreditLimitOrgFunction( creditProviderOrg ).getCreditLimitCurrency();
            if ( ccy == null )
            {
                log.warn("CU.getLimitCurrency : No limit currency in credit limit org function. org=" + creditProviderOrg);
            }
            return ccy != null ? ccy : getReportingCurrency( creditProviderOrg );
        }
        catch ( Exception e )
        {
            log.error( "CU.getLimitCurrency : creditProviderOrg=" + creditProviderOrg, e );
        }
        return null;
    }

    public static Collection<Organization> getCounterpartyOrganizations( Organization creditProviderOrg )
    {
        Collection<Organization> cptyOrgs = new HashSet<Organization>();
        try
        {
            Session session = PersistenceFactory.newSession();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression expr = eb.get( Entity.Namespace ).equal( creditProviderOrg.getNamespace() );
            ReportQuery query = new ReportQuery( eb );
            query.setReferenceClass( CounterpartyCreditLimitRuleC.class );
            query.setSelectionCriteria(expr);

            query.useCollectionClass( HashSet.class );
            query.addAttribute("cptyOrg", eb.get("tradingPartyOrganization").get(Entity.ObjectID));

            Collection<ReportQueryResult> results = ( Collection<ReportQueryResult> ) session.executeQuery( query );
            if ( results != null )
            {
                EntityReference entityRef = MessageFactory.newEntityReference();
                entityRef.setClassName( Organization.class.getName() );
                entityRef.setReferenceOnly(true);
                for ( ReportQueryResult row : results )
                {
                    long orgObjectId = ( ( Number ) row.get( "cptyOrg" ) ).longValue();
                    entityRef.setObjectID( orgObjectId );
                    cptyOrgs.add( ( Organization ) entityRef.getObject() );
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.getCounterpartyOrganizations : creditProviderOrg=" + creditProviderOrg, e );
        }
        return cptyOrgs;
    }

    /**
     * Returns a collection of credit utilization events owned by the credit utilization.
     *
     * @param cu credit utilization
     * @return collection
     */
    public static Collection<CreditUtilizationEvent> getCreditUtilizationEvents( CreditUtilization cu )
    {
        try
        {
            Session session = PersistenceFactory.newSession();
            ReadAllQuery raq = new ReadAllQuery();
            raq.setReferenceClass(CreditUtilizationEvent.class);
            raq.useCollectionClass( ArrayList.class );
            raq.setSelectionCriteria(getCreditUtilizationEventExpression(cu));
            return ( Collection<CreditUtilizationEvent> ) session.executeQuery( raq );
        }
        catch ( Exception e )
        {
            log.error( "CU.getCreditUtilizationEvents : Error getting credit utilization events for cu=" + cu, e );
        }
        return null;
    }

    /**
     * Returns a collection of credit utilization events associated with the credit provider and credit counterparty organization combination.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCptyOrg     credit cpty org
     * @return collection
     */
    public static Collection<CreditUtilizationEvent> getCreditUtilizationEvents( Organization creditProviderOrg, Organization creditCptyOrg )
    {
        try
        {
            Session session = PersistenceFactory.newSession();
            ReadAllQuery raq = new ReadAllQuery();
            raq.setReferenceClass( CreditUtilizationEvent.class );
            raq.useCollectionClass( ArrayList.class );
            raq.setSelectionCriteria( getCreditUtilizationEventExpression( creditProviderOrg, creditCptyOrg ) );
            return ( Collection<CreditUtilizationEvent> ) session.executeQuery( raq );
        }
        catch ( Exception e )
        {
            log.error( new StringBuilder( 200 ).append( "CU.getCreditUtilizationEvents : Error getting credit utilization events for creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCptyOrg=" ).append( creditCptyOrg ).toString(), e );
        }
        return null;
    }

    /**
     * Returns the query expression for getting all credit utilization events for a credit provider and credit counterparty organization combination.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCptyOrg     credit cpty org
     * @return expression
     */
    public static Expression getCreditUtilizationEventExpression( Organization creditProviderOrg, Organization creditCptyOrg )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        Expression providerExpr = eb.get( Entity.Namespace ).equal( creditProviderOrg.getNamespace() );
        Expression creditCptyExpr = eb.get( "tradingPartyOrganization" ).equal( creditCptyOrg );
        return providerExpr.and(creditCptyExpr);
    }

    /**
     * Returns the query expression for getting all credit utilization events of a credit utilization.
     *
     * @param cu credit utilization
     * @return expression
     */
    public static Expression getCreditUtilizationEventExpression( CreditUtilization cu )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        return eb.get( "creditUtilization" ).get( Entity.ObjectID ).equal(cu.getObjectID());
    }

    /**
     * Returns a collection of credit utilization events owned by the credit utilizations of the specified credit limit rule.
     *
     * @param clr credit limit rule
     * @return collection
     */
    public static Collection<CreditUtilizationEvent> getCreditUtilizationEvents( CreditLimitRule clr )
    {
        try
        {
            Session session = PersistenceFactory.newSession();
            ReadAllQuery raq = new ReadAllQuery();
            raq.setReferenceClass(CreditUtilizationEvent.class);
            raq.useCollectionClass( ArrayList.class );
            raq.setSelectionCriteria( getCreditUtilizationEventExpression( clr ) );
            return ( Collection<CreditUtilizationEvent> ) session.executeQuery( raq );
        }
        catch ( Exception e )
        {
            log.error( "CU.getCreditUtilizationEvents : Error getting credit utilization events for clr=" + clr, e );
        }
        return null;
    }

    /**
     * Return a collection of credit utilization event dates for the credit limit rule.
     *
     * @param clr credit limit rule
     * @return dates collection
     */
    public static Collection<IdcDate> getCreditUtilizationEventDates( CreditLimitRule clr )
    {
        Collection<IdcDate> eventDates = new HashSet<IdcDate>();
        try
        {
            Session session = PersistenceFactory.newSession();
            ExpressionBuilder eb = new ExpressionBuilder();
            ReportQuery query = new ReportQuery( eb );
            query.setReferenceClass( CreditUtilizationEventC.class );
            query.setSelectionCriteria( getCounterpartyOrgCreditUtilizationEventExpression( clr ) );

            query.useCollectionClass( HashSet.class );
            query.addAttribute("date", eb.getField("POSSTLMTDATE"));

            Collection<ReportQueryResult> results = ( Collection<ReportQueryResult> ) session.executeQuery( query );
            if ( results != null )
            {
                for ( ReportQueryResult row : results )
                {
                    IdcDate date = DateTimeFactory.newDate( ( Date ) row.get( "date" ) );
                    if ( date != null )
                    {
                        eventDates.add( date );
                    }
                }
            }

            // query spaces events.
            if ( clr instanceof DailyCreditLimitRule )
            {
                final CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) clr.getParentRule();
                final Organization cpo = getCreditProviderOrganization( (CreditLimitRuleSet)cclr.getRuleSet() );
                Collection<CreditUtilizationEvent> spaceCues = CreditUtilizationManagerC.getInstance().getSpacesCreditUtilizationEventsForCounterpartyOrg( cpo, cclr.getTradingPartyOrganization() );
                if ( spaceCues != null )
                {
                    for ( CreditUtilizationEvent cue: spaceCues )
                    {
                        eventDates.add( cue.getSettlementDate() );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.getCreditUtilizationEventDates : clr=" + clr, e );
        }
        return eventDates;
    }

    /**
     * Returns the query expression for getting all credit utilization events applicable to the credit limit rule. This
     * can be at the org level or le level which is determined based on the counterparty credit limit rule.
     *
     * @param clr credit limit rule
     * @return expression
     */
    public static Expression getCreditUtilizationEventExpression( CreditLimitRule clr )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) clr.getParentRule();
        CreditLimitRuleSet clrs = ( CreditLimitRuleSet ) cclr.getRuleSet();
        Organization creditProviderOrg = clrs.getOrganization();
        Expression namespaceExpr = eb.get( Entity.Namespace ).equal( creditProviderOrg.getNamespace() );
        Expression cptyExpr;
        if ( cclr.getTradingParty() != null )
        {
            cptyExpr = eb.get( "tradingParty" ).equal( cclr.getTradingParty() );
        }
        else
        {
            cptyExpr = eb.get( "tradingPartyOrganization" ).equal( cclr.getTradingPartyOrganization() );
        }
        Expression statusExpr = eb.get( Entity.Status ).equal( Entity.ACTIVE_STATUS );
        Expression lastActionExpr = eb.get( "lastAction" ).equal( Entity.ACTIVE_STATUS );
        IdcDate currentDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
        Expression dateExpr = eb.get( "settlementDate" ).notNull().and( eb.get( "settlementDate" ).greaterThanEqual( currentDate ) );
        Expression cptyRuleExpr = eb.get( "counterpartyCreditLimitRule" ).notNull();
        return statusExpr.and( namespaceExpr ).and( cptyExpr ).and( dateExpr ).and( lastActionExpr ).and( cptyRuleExpr );
    }

    /**
     * Returns the query expression for getting all credit utilization events applicable to the credit limit rule. This
     * can be at the org level or le level which is determined based on the counterparty credit limit rule.
     *
     * @param clr credit limit rule
     * @return expression
     */
    public static Expression getCounterpartyOrgCreditUtilizationEventExpression( CreditLimitRule clr )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) clr.getParentRule();
        CreditLimitRuleSet clrs = ( CreditLimitRuleSet ) cclr.getRuleSet();
        Organization creditProviderOrg = clrs.getOrganization();
        Expression namespaceExpr = eb.get( Entity.Namespace ).equal( creditProviderOrg.getNamespace() );
        Expression cptyExpr;
        cptyExpr = eb.get( "tradingPartyOrganization" ).equal( cclr.getTradingPartyOrganization() );
        Expression statusExpr = eb.get( Entity.Status ).equal( Entity.ACTIVE_STATUS );
        Expression lastActionExpr = eb.get( "lastAction" ).equal( Entity.ACTIVE_STATUS );
        IdcDate currentDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
        Expression dateExpr = eb.get( "settlementDate" ).notNull().and( eb.get( "settlementDate" ).greaterThanEqual( currentDate ) );
        Expression cptyRuleExpr = eb.get( "counterpartyCreditLimitRule" ).notNull();
        return statusExpr.and( namespaceExpr ).and( cptyExpr ).and( dateExpr ).and( lastActionExpr ).and( cptyRuleExpr );
    }


    /**
     * Checks whether the credit utilization event is valid. Credit utilization event is considered invalid if the settlement
     * date is prior to the current business date or if it is passive or if its last action is apply or earmark.
     *
     * @param cue          credit utilization event
     * @param businessDate business date
     * @return valid
     */
    public static boolean isValidCreditUtilizationEvent( CreditUtilizationEvent cue, IdcDate businessDate ) {
        if (cue.isPassive())
        {
            return false;
        }

        // remove prior business date events.
        return !(cue.getSettlementDate() == null || cue.getSettlementDate().isEarlierThan(businessDate))
                && (CreditLimit.ACTION_APPLY == cue.getLastAction().charValue() || CreditLimit.ACTION_EARMARK == cue.getLastAction().charValue());

    }

    /**
     * Returns a collection of all credit limit rules owned by the counterparty credit limit rule.
     *
     * @param cclr cclr
     * @return collection of credit limit rules
     */
    public static Collection<CreditLimitRule> getCreditLimitRules( CounterpartyCreditLimitRule cclr )
    {
        Collection<CreditLimitRule> creditLimitRules = new ArrayList<CreditLimitRule>( 2 );
        Collection<Rule> rules = cclr.getChildrenRules();
        for ( Rule rule : rules )
        {
            CreditLimitRule clr = ( CreditLimitRule ) rule;
            if ( clr.isActive() )
            {
                creditLimitRules.add( clr );
            }
        }
        return creditLimitRules;
    }

    /**
     * Returns max tenor owned by the counterparty credit limit rule. If not set in cclr then
     * it is retrieved form the rule set.
     *
     * @param cclr cclr
     * @return maximum tenor
     */
    public static CreditTenorRestriction getMaximumTenor( CounterpartyCreditLimitRule cclr, boolean parentLookup )
    {
        CreditTenorRestriction maxTenorRestriction = null;
        if ( cclr != null )
        {
            Tenor maxTenor = cclr.getMaximumTenor();

            if ( maxTenor == null && parentLookup )
            {
                maxTenor = ( ( CreditLimitRuleSet ) cclr.getRuleSet() ).getMaximumTenor();
            }
            if ( maxTenor != null )
            {
                boolean businessDays = cclr.isTenorRestrictionInBusinessDays() && maxTenor.isDays();
                maxTenorRestriction = new CreditTenorRestriction( maxTenor, businessDays );
            }
        }
        return maxTenorRestriction;
    }

    public static IdcDate getValueDateForTenor( FXRateBasis rb, CreditTenorRestriction ctr )
    {
        return getValueDateForTenor( rb.getNZDTradeDate( EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate() ), rb, ctr );
    }

    public static IdcDate getValueDateForTenor ( Currency base, Currency var, CreditTenorRestriction ctr )
    {
        return getValueDateForTenor( getStdQuoteConvention().getFXRateBasis( base, var ), ctr);
    }

    public static IdcDate getValueDateForTenor( IdcDate tradeDate, FXRateBasis rb, CreditTenorRestriction ctr )
    {
        if ( rb != null && ctr != null )
        {
            return ctr.isBusinessDays() ? rb.getValueDateWithBusinessDaysLag( tradeDate, ctr.getTenor(), true ) : rb.getValueDate( tradeDate, ctr.getTenor() );
        }
        return null;
    }

    /**
     * Returns minimum tenor owned by the counterparty credit limit rule. If not set in cclr then
     * it is retrieved form the rule set.
     *
     * @param cclr cclr
     * @param parentLookup parent lookup
     * @return minimum tenor
     */
    public static CreditTenorRestriction getMinimumTenor( CounterpartyCreditLimitRule cclr, boolean parentLookup )
    {
        CreditTenorRestriction minTenorRestriction = null;
        if ( cclr != null )
        {
            Tenor minTenor = cclr.getMinimumTenor();

            if ( minTenor == null && parentLookup )
            {
                minTenor = ( ( CreditLimitRuleSet ) cclr.getRuleSet() ).getMinimumTenor();
            }
            if ( minTenor != null )
            {
                boolean businessDays = cclr.isTenorRestrictionInBusinessDays() && minTenor.isDays();
                minTenorRestriction = new CreditTenorRestriction( minTenor, businessDays );
            }
        }
        return minTenorRestriction;
    }

    /**
     * Returns a collection of enabled credit limit rules owned by the counterparty credit limit rule.
     *
     * @param cclr cclr
     * @return collection of enabled credit limit rules
     */
    public static Collection<CreditLimitRule> getEnabledCreditLimitRules( CounterpartyCreditLimitRule cclr )
    {
        Collection<CreditLimitRule> creditLimitRules = new ArrayList<CreditLimitRule>( 2 );
        Collection<Rule> rules = cclr.getChildrenRules();
        for ( Rule rule : rules )
        {
            CreditLimitRule clr = ( CreditLimitRule ) rule;
            if ( clr.isActive() && clr.isEnabled() )
            {
                creditLimitRules.add( clr );
            }
        }
        return creditLimitRules;
    }

    /**
     * Returns a count of enabled credit limit rules owned by the counterparty credit limit rule.
     *
     * @param cclr cclr
     * @return collection of enabled credit limit rules
     */
    public static int getEnabledCreditLimitRulesCount( CounterpartyCreditLimitRule cclr )
    {
        Collection<Rule> rules = cclr.getChildrenRules();
        int num = 0;
        for ( Rule rule : rules )
        {
            CreditLimitRule clr = ( CreditLimitRule ) rule;
            if ( clr.isActive() && clr.isEnabled() )
            {
                num++;
            }
        }
        return num;
    }


    /**
     * Returns the code for the credit status.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit cpty
     * @return enabled
     */
    public static int getCreditStatus( Organization creditProviderOrg, TradingParty creditCpty )
    {
        int status = CREDIT_NO_CHECK;
        if( creditCpty != null && creditCpty.getLegalEntityOrganization().isSameAs(creditProviderOrg)){
            return CREDIT_NO_CHECK;
        }
        if ( CreditConfigurationFactory.getCreditConfigurationMBean().isCreditEnabled() )
        {
            CreditLimitRuleSet clrs = getCreditLimitRuleSet( creditProviderOrg );
            if ( clrs != null && clrs.isEnabled() )
            {
                // check provider level status
                if ( clrs.isTradingSuspended() )
                {
                    return CREDIT_SUSPEND;
                }
                TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
                if ( creditTp != null )
                {
                    CounterpartyCreditLimitRule cclr = getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditTp, true );
                    if ( cclr != null && cclr.isActive() )
                    {
                        status = getCreditStatus( cclr );
                    }
                    else if ( log.isDebugEnabled() )
                    {
                        log.debug( "CU.getCreditStatus : no counterparty credit limt rule for creditProviderOrg=" + creditProviderOrg + ",cpty=" + creditCpty );
                    }
                }
                else if ( log.isDebugEnabled() )
                {
                    log.debug( new StringBuilder( 100 ).append( "CU.getCreditStatus : no credit tp for creditProviderOrg=" )
                            .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).toString() );
                }
            }
            else if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CU.getCreditStatus : null or disabled credit limit ruleset for creditProviderOrg=" )
                        .append( creditProviderOrg ).append( ",clrs=" ).append( clrs ).toString() );
            }
        }
        return status;
    }

    public static boolean isCreditActive( final Organization cpo, final TradingParty cc )
    {
        return getCreditStatus ( cpo, cc ) > CreditLimitConstants.CREDIT_NO_CHECK;
    }

    public static String getCreditType( final Organization cpo, final TradingParty cc )
    {
        return isCashSettlementEnabled ( cpo, cc ) ? CreditLimit.ACCOUNT : CreditLimit.CREDIT;
    }

    /**
     * Returns the status of counterparty credit limit rule. It interprets the value from the cpty rule only and does not
     * consider the ruleset level value as well as whether the rule itself is active or not.
     *
     * @param cclr cpty rule
     * @return status
     */
    public static int getCreditStatus( CounterpartyCreditLimitRule cclr )
    {
        return cclr.isEnabled() ? ( cclr.isTradingSuspended() ? CREDIT_SUSPEND : CREDIT_ACTIVE ) : CREDIT_NO_CHECK;
    }

    /**
     * Returns the status of counterparty credit limit rule. It interprets the value from the cpty rule only and does not
     * consider the ruleset level value as well as whether the rule itself is active or not.
     *
     * @param clrs credit limit rule set
     * @return status
     */
    public static int getCreditStatus( CreditLimitRuleSet clrs )
    {
        return clrs.isEnabled() ? ( clrs.isTradingSuspended() ? CREDIT_SUSPEND : CREDIT_ACTIVE ) : CREDIT_NO_CHECK;
    }

    public static boolean isCreditActive( CounterpartyCreditLimitRule cclr )
    {
        CreditLimitRuleSet clrs = ( CreditLimitRuleSet ) cclr.getRuleSet ();
        return CREDIT_ACTIVE == getCreditStatus ( clrs ) && CREDIT_ACTIVE == getCreditStatus ( cclr );
    }

    /**
     * Validates the credit limit orgfunction for a credit provider organization.
     *
     * @param creditProviderOrg credit provider org
     * @return sanityCheck
     */
    public static boolean sanityCheckOnCreditLimitOrgFunction( Organization creditProviderOrg )
    {
        CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction(creditProviderOrg);
        if ( orgFunc.getCreditLimitCurrency() == null )
        {
            log.error( "CU.sanityCheckOnCreditLimitOrgFunction : No credit limit currency specified in the org function. creditProviderOrg=" + creditProviderOrg );
            return false;
        }
        if ( orgFunc.getCreditLimitRuleSet() == null )
        {
            log.error( "CU.sanityCheckOnCreditLimitOrgFunction : No credit limit ruleset specified in the org function. creditProviderOrg=" + creditProviderOrg );
            return false;
        }
        if ( orgFunc.getDefaultAggregateCreditUtilizationCalculator() == null )
        {
            log.info( "CU.sanityCheckOnCreditLimitOrgFunction : No default aggregate credit utilization calculator specified in the org function. creditProviderOrg=" + creditProviderOrg );
        }
        if ( orgFunc.getDefaultDailyCreditUtilizationCalculator() == null )
        {
            log.info( "CU.sanityCheckOnCreditLimitOrgFunction : No default daily credit utilization calculator specified in the org function. creditProviderOrg=" + creditProviderOrg );
        }
        if ( orgFunc.getSupportedCreditLimitClassifications() == null || orgFunc.getSupportedCreditLimitClassifications().isEmpty() )
        {
            log.error( "CU.sanityCheckOnCreditLimitOrgFunction : No supported credit types specified in the org function. creditProviderOrg=" + creditProviderOrg + ",supportedTypes=" + orgFunc.getSupportedCreditLimitClassifications() );
            return false;
        }
        if ( orgFunc.getCreditUtilizationPeriod() == null || orgFunc.getCreditUtilizationPeriod().asDays() <= 0 )
        {
            log.error( "CU.sanityCheckOnCreditLimitOrgFunction : No credit utilization periodd specified in the org function. creditProviderOrg=" + creditProviderOrg + ",creditUtilPeriod=" + orgFunc.getCreditUtilizationPeriod() );
            return false;
        }
        return true;
    }

    /**
     * Performs a sanity check on the credit provider organization. This is especially done after process intensive
     * admin credit actions also from admin utilities. If a counterparty organization is specified, then only those rules will
     * be checked. If no counterparty organization is specified, then all the rules are checked.
     *
     * @param creditProviderOrg         credit provider org
     * @param creditCptyOrg             credit cpty org
     * @param ignoreUtilizationMismatch ignore
     * @return result of sanity check
     */
    public static boolean sanityCheck( Organization creditProviderOrg, Organization creditCptyOrg, boolean ignoreUtilizationMismatch )
    {
        boolean result = true;
        try
        {
            long t0 = System.currentTimeMillis();
            log.info( "CU.sanityCheck : credit sanity check begin for org=" + creditProviderOrg );
            CreditLimitRuleSet clrs = getCreditLimitRuleSet( creditProviderOrg );
            if ( clrs != null )
            {
                CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg );
                if ( orgFunc == null )
                {
                    log.error( "CU.sanityCheck : No credit limit org function for creditProviderOrg=" + creditProviderOrg );
                    return false;
                }
                if ( !sanityCheckOnCreditLimitOrgFunction( creditProviderOrg ) )
                {
                    return false;
                }
                if ( clrs.getNamespace().getObjectID() == MAIN_NAMESPACE_ID )
                {
                    if ( creditProviderOrg.getNamespace () != null && !IdcUtilC.MAIN_NAMESPACE.equals ( creditProviderOrg.getNamespace ().getShortName () ) )
                    {
                        log.error ( "CU.sanityCheck : Main namespace credit limit rule set. clrs=" + clrs + ",creditProviderOrg=" + creditProviderOrg );
                        return false;
                    }
                }
                if ( clrs.getOrganization() == null )
                {
                    log.error( "CU.sanityCheck : No organization in the credit limit rule set. clrs=" + clrs + ",creditProviderOrg=" + creditProviderOrg );
                    return false;
                }
                if ( creditCptyOrg != null )
                {
                    result = sanityCheckOnCounterpartyCreditLimitRules( creditProviderOrg, creditCptyOrg, ignoreUtilizationMismatch );
                }
                else
                {
                    Set<Organization> cptyOrgs = new HashSet<Organization>();
                    Collection<CounterpartyCreditLimitRule> rules = clrs.getRules();
                    for ( Rule cpRule : rules )
                    {
                        CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cpRule;
                        cptyOrgs.add( cclr.getTradingPartyOrganization() );
                    }

                    for ( Organization cptyOrg : cptyOrgs )
                    {
                        result = sanityCheckOnCounterpartyCreditLimitRules( creditProviderOrg, cptyOrg, ignoreUtilizationMismatch );
                    }
                }
            }
            else
            {
                log.warn( "CU.sanityCheck : No credit limit ruleset for org=" + creditProviderOrg );
            }
            log.info( new StringBuilder( 200 ).append( "CU.sanityCheck : credit sanity check finished successfully for org=" )
                    .append( creditProviderOrg ).append( ",result=" ).append( result ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            return result;
        }
        catch ( Exception e )
        {
            log.error( "CU.sanityCheck : Exception during sanity check for org=" + creditProviderOrg + ",creditCptyOrg=" + creditCptyOrg, e );
        }
        return false;
    }

    /**
     * Returns the result of sanity check on the counterparty credit limit rules for the credit provider organization and
     * credit counterparty organization combination.
     *
     * @param creditProviderOrg         credit provider org
     * @param creditCptyOrg             credit cpty org
     * @param ignoreUtilizationMismatch ignore
     * @return result of sanity check
     */
    public static boolean sanityCheckOnCounterpartyCreditLimitRules( Organization creditProviderOrg, Organization creditCptyOrg, boolean ignoreUtilizationMismatch )
    {
        Collection<CounterpartyCreditLimitRule> cptyRules = queryAllCounterpartyCreditLimitRulesForCounterpartyOrganization( creditProviderOrg, creditCptyOrg, false );
        if ( cptyRules == null || cptyRules.isEmpty() )
        {
            log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : No cpty rules for creditCptyOrg=" + creditCptyOrg + ",creditProviderOrg=" + creditProviderOrg );
            return false;
        }
        Iterator iter = cptyRules.iterator();
        // check for duplications of org level cpty rules and tp level rules. Also, check for availability of credit limit rules.

        CounterpartyCreditLimitRule orgLevelRule = getOrgLevelCounterpartyCreditLimitRule( cptyRules );
        if ( orgLevelRule == null )
        {
            log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : No org level cpty rules for creditCptyOrg=" + creditCptyOrg + ",creditProviderOrg=" + creditProviderOrg );
            return false;
        }
        boolean isOrgLevel = orgLevelRule.isOrgLevel();

        int numberOfOrgLevelRules = 0;
        int numberOfCptyLevelRules = 0;
        Set<TradingParty> tpSet = new HashSet<TradingParty>();
        while ( iter.hasNext() )
        {
            CounterpartyCreditLimitRule cptyRule = ( CounterpartyCreditLimitRule ) iter.next();
            if ( cptyRule.getTradingParty() != null && cptyRule.isActive() && isOrgLevel )
            {
                log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : Active tp rule found for org level credit. creditCptyOrg=" + creditCptyOrg + ",creditProviderOrg=" + creditProviderOrg + ",tp=" + cptyRule.getTradingParty() );
                return false;
            }
            if ( cptyRule.getTradingParty() == null && cptyRule.isActive() && !isOrgLevel )
            {
                log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : Active org level rule found for tp level credit. creditCptyOrg=" + creditCptyOrg + ",creditProviderOrg=" + creditProviderOrg + ",tp=" + cptyRule.getTradingParty() );
                //return false;
            }
            if ( cptyRule.getNamespace().getObjectID() == MAIN_NAMESPACE_ID )
            {
                log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : Failed sanity check. Main namespace cptyRule=" + cptyRule );
                return false;
            }
            boolean isActive = cptyRule.isActive();
            if ( cptyRule.getTradingPartyOrganization() == null )
            {
                log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : Failed sanity check. no tradingPartyOrganization in counterparty credit limit rule=" + cptyRule );
                return false;
            }
            if ( cptyRule.getTradingParty() == null )
            {
                numberOfOrgLevelRules++;
            }
            else
            {
                numberOfCptyLevelRules++;
                tpSet.add( cptyRule.getTradingParty() );
            }
            int numberOfSingleRule = 0;
            int numberOfDailyRule = 0;
            Collection<CreditLimitRule> cRules = cptyRule.getChildrenRules();
            if ( cRules.size() != CreditConfigurationFactory.getCreditConfigurationMBean().getSupportedCreditTypes().size() )
            {
                log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : cRules.size=" + cRules.size() );
                return false;
            }
            for ( CreditLimitRule clr : cRules )
            {
                if ( clr instanceof SingleCreditLimitRule )
                {
                    numberOfSingleRule++;
                }
                if ( clr instanceof DailyCreditLimitRule )
                {
                    numberOfDailyRule++;
                }
                if ( !clr.isActive() )
                {
                    log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : Failed sanity check. Inactive credit limit rule found. clr=" + clr );
                    return false;
                }
                if ( clr.getCurrency() == null )
                {
                    log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : No limit currency is there in credit limit rule=" + clr + ",creditProviderOrg=" + creditProviderOrg );
                }
                if ( clr.getNamespace().getObjectID() == MAIN_NAMESPACE_ID )
                {
                    log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : Main namespace credit limit rule=" + clr + ",creditProviderOrg=" + creditProviderOrg );
                    return false;
                }
                // check whether credit utilization is available for spot date.
                CreditUtilization cu = CreditUtilizationManagerC.getInstance().getCreditUtilizationForDate( clr, DateTimeFactory.newDate().addDays( 2 ) );
                if ( cu == null )
                {
                    log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : Failed sanity check. No credit utilization found. clr=" + clr );
                    return false;
                }
                if ( clr instanceof DailyCreditLimitRule )
                {
                    // check whether credit utilization is available for spot date + 10 days.
                    CreditUtilization cuPostSpot = CreditUtilizationManagerC.getInstance().getCreditUtilizationForDate( clr, DateTimeFactory.newDate().addDays( 10 ) );
                    if ( cuPostSpot == null )
                    {
                        log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : Failed sanity check. No credit utilization found for spot + 10 days. clr=" + clr );
                        return false;
                    }
                    try
                    {
                        DatePeriod dp = DateTimeFactory.newDatePeriod( "10D" );
                        Collection<CreditUtilization> creditUtils = getCreditUtilizations( clr, dp );
                        if ( creditUtils == null || creditUtils.size() != ( dp.asDays() + 1 ) )
                        {
                            log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : Duplicate credit utilizations for clr=" + clr + ",datePeriod=" + dp + ",utils=" + creditUtils );
                            return false;
                        }
                    }
                    catch ( Exception e )
                    {
                        log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : Error getting credit utilizations for clr=" + clr );
                        return false;
                    }
                }
                if ( clr.isEnabled() && isActive )
                {
                    boolean isMismatch = false;
                    try
                    {
                        double usedAmt = cu.getUsedAmount();
                        double availableAmt = cu.getAvailableMarginReserveAmount();
                        cu.resetCurrencyPositions( RESET_SANITY_CHECK, true );
                        cu.getCreditLimitRule().getCreditUtilizationCalculator().recalculateUtilization( cu );
                        double recalcAmt = cu.getAvailableMarginReserveAmount();
                        if ( availableAmt != recalcAmt )
                        {
                            log.warn( "CU.sanityCheckOnCounterpartyCreditLimitRules : Failed sanity check. availableamt=" + availableAmt + ",recalcAmt=" + recalcAmt + ",cu=" + cu + ",origUsedAmt=" + usedAmt + "newUsedAmt=" + cu.getUsedAmount() + ",cptyRule=" + cptyRule.getTradingParty() + ",cptyRule.tporg=" + cptyRule.getTradingPartyOrganization() );
                            isMismatch = true;
                        }
                        if ( cu.getUsedAmount() != usedAmt )
                        {
                            log.warn( "CU.sanityCheckOnCounterpartyCreditLimitRules : Failed sanity check after refreshing. cu=" + cu + ",origUsedAmt=" + usedAmt + "newUsedAmt=" + cu.getUsedAmount() );
                            isMismatch = true;
                        }
                    }
                    catch ( Exception e )
                    {
                        log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : exception during calculation verification.", e );
                        return false;
                    }
                    if ( isMismatch )
                    {
                        log.warn( "CU.sanityCheckOnCounterpartyCreditLimitRules : Failed sanity check. mismatch in utilization amounts." );
                        if ( !ignoreUtilizationMismatch )
                        {
                            return false;
                        }
                    }
                }
            }
            if ( numberOfSingleRule != 1 )
            {
                log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : single credit limit rule count=" + numberOfSingleRule );
            }
            if ( numberOfSingleRule != 1 )
            {
                log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : daily credit limit rule count=" + numberOfDailyRule );
                return false;
            }

        }
        if ( numberOfOrgLevelRules != 1 )
        {
            log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : org level rule count=" + numberOfOrgLevelRules );
            return false;
        }

        if ( numberOfCptyLevelRules != tpSet.size() )
        {
            log.error( "CU.sanityCheckOnCounterpartyCreditLimitRules : tp level rule count=" + numberOfCptyLevelRules );
            return false;
        }
        return true;
    }

    /**
     * Validates the relationship between the credit provider and credit cpty.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit cpty
     * @return valid
     */
    public static boolean validateCreditCounterparty( Organization creditProviderOrg, TradingParty creditCpty )
    {
        if ( creditCpty instanceof LegalEntity )
        {
            return !creditProviderOrg.isSameAs( creditCpty.getOrganization() );
        }
        else
        {
            return creditProviderOrg.isSameAs( creditCpty.getOrganization() );
        }
    }

    /**
     * Validates the credit cpty and credit cpty organization relation.
     *
     * @param creditCpty    credit cpty
     * @param creditCptyOrg credit cpty org
     * @return valid
     */
    public static boolean validateCounterpartyOrganization( TradingParty creditCpty, Organization creditCptyOrg )
    {
        if ( creditCpty instanceof LegalEntity )
        {
            return creditCptyOrg.isSameAs( creditCpty.getOrganization() );
        }
        else
        {
            return creditCptyOrg.isSameAs( creditCpty.getLegalEntityOrganization() );
        }
    }

    /**
     * Returns the organization which owns the credit limit ruleset.
     *
     * @param clrs clrs
     * @return organization
     */
    public static Organization getCreditProviderOrganization( CreditLimitRuleSet clrs )
    {
        return CreditDataQueryServiceFactory.getCreditDataQueryService().getCreditProviderOrganization( clrs );
    }

    /**
     * Returns a set of netting methodologies for the organization. This list is derived from the list of supported credit types.
     *
     * @param clsf clsf
     * @return list of supported netting methodologies
     */
    public static Collection<CreditUtilizationCalculator> getSupportedNettingMethodologies( CreditLimitClassification clsf )
    {
        return getSupportedNettingMethodologies ( clsf, true, true );
    }

    /**
     * Returns a set of netting methodologies for the organization. This list is derived from the list of supported credit types.
     *
     * @param clsf clsf
     * @param includeAccount include accounts
     * @param includeMultiFactor include multi-factor
     * @return list of supported netting methodologies
     */
    public static Collection<CreditUtilizationCalculator> getSupportedNettingMethodologies( CreditLimitClassification clsf, boolean includeAccount, boolean includeMultiFactor )
    {
        return getSupportedNettingMethodologies( clsf, includeAccount, includeMultiFactor, true );
    }

    public static Collection<CreditUtilizationCalculator> getSupportedNettingMethodologies( CreditLimitClassification clsf, boolean includeAccount, boolean includeMultiFactor,  boolean includeCreditNetMethodology )
    {
        Collection<CreditUtilizationCalculator> calcSet = new ArrayList<CreditUtilizationCalculator>( 3 );
        if ( DAILY_SETTLEMENT_CLASSIFICATION.isSameAs( clsf ) )
        {
            calcSet.add( GROSS_DAILY_LIMIT_CALCULATOR );
            calcSet.add( DAILY_SETTLEMENT_LIMIT_CALCULATOR );
        }
        if ( GROSS_NOTIONAL_CLASSIFICATION.isSameAs( clsf ) )
        {
            calcSet.add( GROSS_AGGREGATE_LIMIT_CALCULATOR );
            calcSet.add( AGGREGATE_LIMIT_CALCULATOR );
            calcSet.add( NET_OPEN_POSITION_CALCULATOR );
            calcSet.add( AGGREGATE_NPR_SETTLEMENT_CALCULATOR );
            calcSet.add( AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR );
            if ( includeAccount )
            {
                calcSet.add ( AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR );
            }
            if ( includeMultiFactor )
            {
                calcSet.add ( AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR );
            }
            if ( includeCreditNetMethodology )
            {
                calcSet.add ( AGGREGATE_NET_CREDITNET_SETTLEMENT_CALCULATOR );
            }
        }

        return calcSet;
    }


    /**
     * Returns the collection of supported external credit limit providers such Traiana, Markit etc.
     *
     * @return external credit providers
     */
    public static Collection<String> getSupportedExternalCreditLimitProviders()
    {
        ArrayList<String> externalCreditLimitProviders = new ArrayList<String>();
        //externalCreditLimitProviders.add( EXTERNAL_CREDIT_PROVIDER_TRAIANA_PING );
        //externalCreditLimitProviders.add( EXTERNAL_CREDIT_PROVIDER_TRAIANA_PUSH );
        externalCreditLimitProviders.add( EXTERNAL_CREDIT_PROVIDER_RBIV );
        return externalCreditLimitProviders;
    }


    /**
     * Returns a set of supported aggregate type netting methodologies for the organization. This list is derived from the list of supported credit types.
     *
     * @return list of supported netting methodologies for aggregate type.
     */
    public static Collection<CreditUtilizationCalculator> getSupportedAggregateNettingMethodologies()
    {
        return getSupportedNettingMethodologies( GROSS_NOTIONAL_CLASSIFICATION );
    }

    /**
     * Returns a set of supported aggregate type netting methodologies for the organization. This list is derived from the list of supported credit types.
     *
     * @return list of supported netting methodologies for aggregate type.
     */
    public static Collection<CreditUtilizationCalculator> getSupportedNonAccountAggregateNettingMethodologies()
    {
        return getSupportedNettingMethodologies( GROSS_NOTIONAL_CLASSIFICATION, false, true );
    }

    /**
     * Returns a set of supported aggregate type netting methodologies for the organization. This list is derived from the list of supported credit types.
     *
     * @return list of supported netting methodologies for aggregate type.
     */
    public static Collection<CreditUtilizationCalculator> getSupportedAggregateNettingMethodologies( Organization org )
    {
        Collection<CreditUtilizationCalculator> calcs =  getSupportedNettingMethodologies( GROSS_NOTIONAL_CLASSIFICATION );
        if ( !CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean ().isAggregateNetCashSettlementEnabled ( org  ) )
        {
            calcs.remove ( AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR );
        }
        if ( !CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean ().isAggregateNetSettlementReceivableMethodologyEnabled ( org  ) )
        {
            calcs.remove ( AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR );
        }
        if ( !CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean ().isAggregateNetSettlementReceivableMethodologyEnabled ( org  ) )
        {
            calcs.remove ( AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR );
        }
        if ( !CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean ().isAggregateMultiFactorSettlementMethodologyEnabled ( org  ) )
        {
            calcs.remove ( AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR );
        }
        if ( !CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean ().isAggregateNetCreditNetSettlementMethodologyEnabled ( org  ) )
        {
            calcs.remove ( AGGREGATE_NET_CREDITNET_SETTLEMENT_CALCULATOR );
        }
        return calcs;
    }

    /**
     * Returns a set of supported daily type netting methodologies for the organization. This list is derived from the list of supported credit types.
     *
     * @return list of supported netting methodologies for daily type.
     */
    public static Collection<CreditUtilizationCalculator> getSupportedDailyNettingMethodologies()
    {
        return getSupportedNettingMethodologies( DAILY_SETTLEMENT_CLASSIFICATION );
    }

    /**
     * Returns a set of supported daily type netting methodologies for the organization. This list is derived from the list of supported credit types.
     *
     * @return list of supported netting methodologies for daily type.
     */
    public static Collection<CreditUtilizationCalculator> getSupportedDailyNettingMethodologies( Organization cpo )
    {
        return getSupportedNettingMethodologies( DAILY_SETTLEMENT_CLASSIFICATION );
    }

    /**
     * Returns a collection of credit utilizations which are either used for the credit limit rule. If there are no credit utilization
     * with non-zero used or reserve amount, then an empty collection is returned.
     *
     * @param clr credit limit rule
     * @return collection of credit utils
     */
    public static Collection<CreditUtilization> getUsedCreditUtilizations( CreditLimitRule clr )
    {
        Collection<CreditUtilization> col = getUtilizedCreditUtilizations(clr);
        if ( log.isDebugEnabled() )
        {
            log.debug( "CU.getUsedCreditUtilizations : Used CreditUtilizations for clr=" + clr + ",col=" + col );
        }
        return col != null ? col : new ArrayList<CreditUtilization>();
    }

    /**
     * Returns a collection of credit utilizations for the number of days starting from the current business date. If single
     * type credit limit rule, then single credit utilization is returned.
     *
     * @param clr credit limit rule
     * @param dp  date period
     * @return credit utilization collection
     */
    public static Collection<CreditUtilization> getCreditUtilizations( CreditLimitRule clr, DatePeriod dp )
    {
        return CreditDataQueryServiceFactory.getCreditDataQueryService().getCreditUtilizations(clr, EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate(), dp);
    }

    /**
     * Returns the credit prime broker trading party. If PB credit enabled flag is turned on check
     * whether Credit PB is set, if not use the regular PB.
     *
     * @param creditTp trading party
     * @return Prime broker credit trading party
     */
    public static TradingParty getCreditPrimeBrokerTradingparty( TradingParty creditTp )
    {
        TradingParty pbCreditTp = null;
        if ( creditTp.isPrimeBrokerageCreditUsed() )
        {
            pbCreditTp = creditTp.getPrimeBrokerCreditTradingParty();
            if ( pbCreditTp == null )
            {
                pbCreditTp = creditTp.getPrimeBrokerTradingParty();
            }
        }
        return pbCreditTp;
    }

    /**
     * Returns the credit prime broker organization. If PB credit enabled flag is turned on check
     * whether Credit PB is set, if not use the regular PB.
     *
     * @param creditTp trading party
     * @return Prime broker credit org
     */
    public static Organization getCreditPrimeBrokerOrganization( TradingParty creditTp )
    {
        Organization pbCreditTpOrg = null;
        if ( creditTp.isPrimeBrokerageCreditUsed() )
        {
            pbCreditTpOrg = creditTp.getPrimeBrokerCreditOrganization();
            if ( pbCreditTpOrg == null )
            {
                pbCreditTpOrg = creditTp.getPrimeBrokerOrganization();
            }
        }
        return pbCreditTpOrg;
    }


    /**
     * Validates the email ids separated by semi-colon or comma.
     *
     * @param emailIds email ids
     * @return valid
     */
    public static boolean validateEmailIds( String emailIds )
    {
        if ( emailIds != null && emailIds.length() > 0 )
        {
            String delimiter = emailIds.indexOf( "," ) != -1 ? "," : ";";
            StringTokenizer st = new StringTokenizer( emailIds, delimiter );
            while ( st.hasMoreTokens() )
            {
                String emailId = st.nextToken();
                if ( emailId != null && !validateEmailId( emailId.trim() ) )
                {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Validates the email address.
     *
     * @param emailId email id
     * @return valid email
     */
    public static boolean validateEmailId( String emailId )
    {
        Matcher m = EMAIL_VALIDATION_PATTERN.matcher( emailId.toLowerCase () );
        return m.matches();
    }

    /**
     * Validates the credit utilization percentage.
     *
     * @param percent percent
     * @return valid percentage
     */
    public static boolean validateUtilizationPercentage( double percent )
    {
        return percent >= 0 & percent <= UTILIZATION_NOTIFICATION_PERCENTAGE_LIMIT;
    }

    /**
     * Validates the credit limit amount.
     *
     * @param limit limit
     * @return valid limit
     */
    public static boolean validateCreditLimit( double limit )
    {
        return limit >= 0;
    }

    /**
     * Validates the number of days for the daily credit exposure.
     *
     * @param days days
     * @return valid
     */
    public static boolean validateDailyExposureHorizon( int days )
    {
        return days > 0 && days <= DAILY_EXPOSURE_HORIZON_LIMIT;
    }

    /**
     * Validate the calculator can be applicable to the credit type.
     *
     * @param calcName calc
     * @param clsf     clsf
     * @return validation
     */
    public static boolean validateCreditUtilizationCalculator( String calcName, CreditLimitClassification clsf )
    {
        CreditUtilizationCalculator calc = getCreditUtilizationCalculator( calcName );
        return validateCreditUtilizationCalculator( calc, clsf );
    }

    /**
     * Validates the margin leverage factor. The validation is just > 0
     *
     * @param factor leverage factor
     * @return validation success
     */
    public static boolean validateLeverageFactor( Double factor )
    {
        return factor != null && factor > 0;
    }

    /**
     * Returns whether the specified calculator matches with the specified classification.
     *
     * @param calc calc
     * @param clsf clsf
     * @return valid calc
     */
    public static boolean validateCreditUtilizationCalculator( CreditUtilizationCalculator calc, CreditLimitClassification clsf )
    {
        if ( calc != null )
        {
            Collection<CreditUtilizationCalculator> supportedCalcs = getSupportedNettingMethodologies( clsf );
            for ( CreditUtilizationCalculator nettingCalc : supportedCalcs )
            {
                if ( nettingCalc.isSameAs( calc ) )
                {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Returns the date for the credit utilization. If daily type, credit utilization date is returned. Otherwise, it returns null.
     *
     * @param cu credit utilization
     * @return date
     */
    public static IdcDate getCreditUtilizationDate( CreditUtilization cu )
    {
        if ( cu instanceof DailyCreditUtilization )
        {
            return ( ( DailyCreditUtilization ) cu ).getDate();
        }
        return null;
    }

    /**
     * Returns whether the credit provider legal entity is the same as the maker of the trade.
     *
     * @param trade            trade
     * @param creditProviderLe credit provider le
     * @return maker
     */
    public static boolean isCreditProviderLeMaker( Trade trade, LegalEntity creditProviderLe )
    {
        boolean isCreditProviderLeMaker = true;
        try
        {
            LegalEntity cptyB = CounterpartyUtilC.getLegalEntity( trade.getCounterpartyB() );
            isCreditProviderLeMaker = creditProviderLe.getOrganization().isSameAs( cptyB.getOrganization() );
        }
        catch ( Exception e )
        {
            log.error( new StringBuilder( 200 ).append( "CU.isCreditProviderLeMaker : trade=" )
                    .append( trade ).append( ",creditProviderLe=" ).append( creditProviderLe ).toString(), e );
        }
        return isCreditProviderLeMaker;
    }

    /**
     * Returns whether the organization should be excluded from credit operations.
     *
     * @param organizationName org name
     * @return excludeOrganization
     */
    public static boolean excludeOrganizationFromCreditOperations( String organizationName )
    {
        boolean excludeOrganization = false;
        if ( CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean().getExcludedCreditOperationOrganizationList() != null &&
                CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean().getExcludedCreditOperationOrganizationList().contains( organizationName ) )
        {
            excludeOrganization = true;
        }
        return excludeOrganization;
    }

    /**
     * Returns the standard quote convention.
     *
     * @return standard quote convention
     */
    public static FXRateConvention getStdQuoteConvention()
    {
        return stdQuoteConv;
    }

    /**
     * Computes and returns the spot date for the given currency pair based on standard quote convention.
     *
     * @param baseCcy base ccy
     * @param varCcy  variable ccy
     * @return spot date for the given currency pair
     */
    public static IdcDate getSpotDate( Currency baseCcy, Currency varCcy )
    {
        return getSpotDate( EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate(), baseCcy, varCcy );
    }

    /**
     * Computes and returns the spot date for the given currency pair based on standard quote convention.
     * @param tradeDate trade date
     * @param baseCcy base ccy
     * @param varCcy  variable ccy
     * @return spot date for the given currency pair
     */
    public static IdcDate getSpotDate( IdcDate tradeDate, Currency baseCcy, Currency varCcy )
    {
        return CreditDataQueryServiceFactory.getCreditDataQueryService().getSpotDate( tradeDate, baseCcy, varCcy );
    }

    /**
     * Returns a set of spot dates for common ccy pairs based on standard quote convention
     *
     * @return spot date
     */
    public static Set<IdcDate> getDefaultSpotDates()
    {
        return EndOfDayServiceFactory.getEndOfDayService().getDefaultSpotDates();
    }

    /**
     * Returns a set of spot dates for common ccy pairs based on standard quote convention
     *
     * @return spot date
     */
    public static Set<IdcDate> calculateDefaultSpotDates()
    {
        return calculateDefaultSpotDates( EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate () );
    }

    /**
     * Returns a set of spot dates for common ccy pairs based on standard quote convention
     *
     * @return spot date
     */
    public static Set<IdcDate> calculateDefaultSpotDates( IdcDate tradeDate )
    {
        Set<IdcDate> spotDates = new HashSet<IdcDate>();
        try
        {
            IdcDate spotDate;

            Currency EUR = CurrencyFactory.getCurrency( "EUR" );
            Currency USD = CurrencyFactory.getCurrency( "USD" );
            Currency JPY = CurrencyFactory.getCurrency( "JPY" );
            Currency CAD = CurrencyFactory.getCurrency( "CAD" );
            Currency GBP = CurrencyFactory.getCurrency( "GBP" );
            Currency AUD = CurrencyFactory.getCurrency( "AUD" );
            Currency CHF = CurrencyFactory.getCurrency( "CHF" );


            spotDate = getSpotDate( tradeDate, EUR, USD );
            if ( spotDate != null )
            {
                spotDates.add( spotDate );
            }
            spotDate = getSpotDate( tradeDate, USD, CAD );
            if ( spotDate != null )
            {
                spotDates.add( spotDate );
            }
            spotDate = getSpotDate( tradeDate, USD, JPY );
            if ( spotDate != null )
            {
                spotDates.add( spotDate );
            }
            spotDate = getSpotDate( tradeDate, GBP, USD );
            if ( spotDate != null )
            {
                spotDates.add( spotDate );
            }
            spotDate = getSpotDate( tradeDate, GBP, CAD );
            if ( spotDate != null )
            {
                spotDates.add( spotDate );
            }

            spotDate = getSpotDate( tradeDate, AUD, JPY );
            if ( spotDate != null )
            {
                spotDates.add( spotDate );
            }
            spotDate = getSpotDate( tradeDate, AUD, USD );
            if ( spotDate != null )
            {
                spotDates.add( spotDate );
            }
            spotDate = getSpotDate( tradeDate, EUR, CHF );
            if ( spotDate != null )
            {
                spotDates.add( spotDate );
            }

            spotDate = getSpotDate( tradeDate, EUR, GBP );
            if ( spotDate != null )
            {
                spotDates.add( spotDate );
            }
        }
        catch ( Exception e )
        {
            log.warn("CU.calculateDefaultSpotDates : Exception while calculating the spot dates.", e);
        }
        return spotDates;
    }

    /**
     * Reads an entity from the TOPLink cache. In the case of an exception, the same entity is returned.
     *
     * @param anEntity entity
     * @return entity from cache.
     */
    public static Entity readObject( Entity anEntity )
    {
        try
        {
            if ( anEntity != null )
            {
                if(anEntity instanceof CreditUtilization &&
                        CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationLookupSpacesEnabled(anEntity.getNamespace().getShortName()))
                {
                    IdcDate cuDate = null;
                    CreditUtilization cu = (CreditUtilization)anEntity;
                    if(cu instanceof DailyCreditUtilization){
                        cuDate = ((DailyCreditUtilization)cu).getDate();
                    }

                    return CreditDataQueryServiceFactory.getCreditReferenceDataService().getCreditUtilizationForDate(cu.getCreditLimitRule(), cuDate);
                }
                return ( Entity ) PersistenceFactory.newSession().readObject( anEntity );
            }
        }
        catch ( Exception e )
        {
            log.error("CU.readObject : Error reading the entity=" + anEntity, e);
        }
        return anEntity;
    }

    /**
     * Returns the original lp organization which is being masked by anonymous organization.
     *
     * @param org anonymous organization
     * @return original organization
     */
    public static Organization getMaskedOrganization( Organization org )
    {
        return org.getRealLP();
    }

    /**
     * Returns the prime broker cover trade for the trade.
     *
     * @param trade trade
     * @return prime broker cover trade
     */
    public static Trade getPrimeBrokerCoverTrade( Trade trade )
    {
        if ( trade.getCoverTradeTxIds() != null && trade.getCoverTradeTxIds().endsWith( "C" ) && !trade.getTransactionID().endsWith( "C" ) )
        {
            return TradeCacheC.getInstance().get( trade.getCoverTradeTxIds(), null );
        }
        return null;
    }

    /**
     * Returns whether prime broker cover trade is enabled.
     *
     * @param takerLe taker legal entity
     * @param makerLe maker legal entity
     * @return prime broker cover trade enabled
     */
    public static boolean isPrimeBrokerCoverTradeEnabled( LegalEntity makerLe, LegalEntity takerLe )
    {
        TradingParty takerTpForMaker = makerLe.getTradingParty(takerLe.getOrganization());
        if ( takerTpForMaker != null )
        {
            TradingParty pbTp = takerTpForMaker.getPrimeBrokerTradingParty();
            if ( pbTp != null )
            {
                boolean pbTradingEnabled = takerTpForMaker.isPrimeBrokerCoverTradeEnabled();
                if ( !pbTradingEnabled )
                {
                    pbTradingEnabled = tradeConfigMBean.isPrimeBrokerCoverTradeEnabled( pbTp.getLegalEntityOrganization(), makerLe.getOrganization(), takerLe.getOrganization() );
                }
                return pbTradingEnabled;
            }
        }
        return false;
    }

    /**
     * Returns the le which is cptyA of the trade.
     *
     * @param le1   Legal Entity 1
     * @param le2   Legal Entity 2
     * @param trade trade
     * @return counterpartA (taker) legal entity from the trade
     */
    public static LegalEntity getCptyAFromTrade( LegalEntity le1, LegalEntity le2, Trade trade )
    {
        LegalEntity cptyA = CounterpartyUtilC.getLegalEntity(trade.getCounterpartyA());
        if ( le1.isSameAs(cptyA) )
        {
            return le1;
        }
        else if ( le2.isSameAs( cptyA ) )
        {
            return le2;
        }
        else
        {
            return null;
        }
    }

    public static boolean isCreditProviderLeMaker( CreditLimitWorkflowState clws, LegalEntity takerLe, LegalEntity makerLe )
    {
        boolean creditProviderMaker = true;
        try
        {
            Boolean isCpoMaker = clws.isMaker ();
            if ( isCpoMaker != null )
            {
                log.info( new StringBuilder( 200 ).append( "CU.isCreditProviderLeMaker : clws flag is set. takerLe=" )
                        .append( takerLe ).append( ",makerLe=" ).append( makerLe ).append( ",clws.maker=" ).append( isCpoMaker ).toString() );
                return isCpoMaker;
            }
            Organization taker = takerLe.getOrganization();
            Organization maker = makerLe.getOrganization();
            LegalEntity clwsLe = clws.getLegalEntity();
            Organization clwsOrg = clwsLe.getOrganization();
            TradingParty clwsTp = clws.getTradingParty();
            Organization cptyOrg = clwsTp.getLegalEntityOrganization();
            if ( clwsOrg.isSameAs( taker ) )
            {
                log.info( new StringBuilder( 200 ).append( "CU.isCreditProviderLeMaker : taker is credit provider. taker=" )
                        .append( taker ).append( ",maker=" ).append( maker ).append( ",cptyOrg=" ).append( cptyOrg ).append( ",cpoMaker" ).append( false ).toString() );
                creditProviderMaker = false;
            }
            else if ( clwsOrg.isSameAs( maker ) )
            {
                log.info( new StringBuilder( 200 ).append( "CU.isCreditProviderLeMaker : maker is credit provider. taker=" )
                        .append( taker ).append( ",maker=" ).append( maker ).append( ",cptyOrg=" ).append( cptyOrg ).append( ",cpoMaker" ).append( true ).toString() );
                creditProviderMaker = true;
            }
            else // this is a PB case and clws org can be FI's PB or LP's PB
            {
                TradingParty takerTPForMaker = makerLe.getTradingParty( taker );
                TradingParty makerTPForTaker = takerLe.getTradingParty( maker );
                Organization takerPb = takerTPForMaker != null ? getCreditPrimeBrokerOrganization( takerTPForMaker ) : null;
                Organization makerPb = makerTPForTaker != null ? getCreditPrimeBrokerOrganization( makerTPForTaker ) : null;
                if ( takerPb != null && clwsOrg.isSameAs( takerPb ) ) // FI's PB
                {
                    // For FI's PB, if credit cpty is taker, then PB is a maker
                    creditProviderMaker = taker.isSameAs( cptyOrg );
                    log.info( new StringBuilder( 200 ).append( "CU.isCreditProviderLeMaker : takerPb is credit provider. takerPb=" )
                            .append( takerPb ).append( ",taker=" ).append( taker ).append( ",maker=" ).append( maker )
                            .append( ",cptyOrg=" ).append( cptyOrg ).append( ",creditProviderMaker" ).append( creditProviderMaker ).toString() );
                }
                else if ( makerPb != null && clwsOrg.isSameAs( makerPb ) )
                {
                    // For LP's PB, if credit cpty is maker, then PB is taker.
                    creditProviderMaker = !maker.isSameAs( cptyOrg );
                    log.info( new StringBuilder( 200 ).append( "CU.isCreditProviderLeMaker : makerPb is credit provider. makerPb=" )
                            .append( makerPb ).append( ",taker=" ).append( taker ).append( ",maker=" ).append( maker )
                            .append( ",cptyOrg=" ).append( cptyOrg ).append( ",creditProviderMaker" ).append( creditProviderMaker ).toString() );
                }
                else
                {
                    log.warn(new StringBuilder(200).append("CU.isCreditProviderLeMaker : Possible misconfiguration. makerPb=")
                            .append(makerPb).append(",taker=").append(taker).append(",maker=").append(maker)
                            .append(",cptyOrg=").append(cptyOrg).append(",creditProviderMaker=true").toString());
                }
            }
        }
        catch ( Exception e )
        {
            log.warn("CU.isCreditProviderLeMaker : Exception while evaluating credit provider maker. clws=" + clws, e);
        }
        return creditProviderMaker;
    }

    public static Collection<CreditRelationship> getCreditRelationships( LegalEntity le1, LegalEntity le2 )
    {
        LegalEntity origLe1 = le1;
        LegalEntity origLe2 = le2;
        Organization origOrg1, origOrg2;
        Collection<CreditRelationship> relations = new ArrayList<CreditRelationship>( 3 );
        try
        {
            origOrg1 = le1.getOrganization();
            origOrg2 = le2.getOrganization();
            Organization org1 = origOrg1;
            Organization org2 = origOrg2;
            Organization realOrg1 = CreditUtilC.getMaskedOrganization( org1 );
            Organization realOrg2 = CreditUtilC.getMaskedOrganization( org2 );
            boolean org1Anonymous = false;
            boolean org2Anonymous = false;
            if ( realOrg2 != null )
            {
                org2Anonymous = true;
                org2 = realOrg2;
                le2 = realOrg2.getDefaultDealingEntity();
            }
            else if ( realOrg1 != null )
            {
                org1Anonymous = true;
                org1 = realOrg1;
                le1 = realOrg1.getDefaultDealingEntity();
            }

            if ( le1 == null || le2 == null )
            {
                log.info( new StringBuilder( 200 ).append( "CU.getCreditRelationships : missing either le2 or le1=" )
                        .append( le1 ).append( ",le2=" ).append( le2 ).append( ",realOrg1=" )
                        .append( realOrg1 ).append( ",realOrg1=" ).append( realOrg1 ).toString() );
                return relations;
            }
            TradingParty org1TpForLe2 = origLe2.getTradingParty( origOrg1 );
            TradingParty org2TpForLe1 = origLe1.getTradingParty( origOrg2 );
            if ( org1TpForLe2 == null || org2TpForLe1 == null )
            {
                log.info( new StringBuilder( 200 ).append( "CU.getCreditRelationships : missing trading parties. le1=" )
                        .append( le1 ).append( ",le2=" ).append( le2 ).append( ",realOrg1=" ).append( realOrg1 )
                        .append( ",realOrg2=" ).append( realOrg2 ).toString() );
                return relations;
            }

            TradingParty org1PbTp = CreditUtilC.getCreditPrimeBrokerTradingparty( org1TpForLe2 );
            TradingParty org2PbTp = CreditUtilC.getCreditPrimeBrokerTradingparty( org2TpForLe1 );

            boolean isLe1PrimeBrokerCreditEnabled = org1PbTp != null;
            boolean isLe2PrimeBrokerCreditEnabled = org2PbTp != null;
            boolean showOrg1 = true;
            boolean showOrg2 = true;
            int code;
            if ( isLe1PrimeBrokerCreditEnabled || isLe2PrimeBrokerCreditEnabled )
            {
                Organization pb1 = org1PbTp != null ? org1PbTp.getLegalEntityOrganization() : null;
                Organization pb2 = org2PbTp != null ? org2PbTp.getLegalEntityOrganization() : null;
                boolean org1PbCoverEnabled = CounterpartyUtilC.isPrimeBrokerCoverTradeEnabled( origOrg1, origLe2 );
                showOrg2 = !( org1PbCoverEnabled && org2Anonymous );
                boolean org2PbCoverEnabled = CounterpartyUtilC.isPrimeBrokerCoverTradeEnabled( origOrg2, origLe1 );
                showOrg1 = !( org2PbCoverEnabled && org1Anonymous );

                if ( isLe1PrimeBrokerCreditEnabled && isLe2PrimeBrokerCreditEnabled )
                {
                    code = 100;
                    // same PB set for both sides.
                    if ( pb1.isSameAs( pb2 ) )
                    {
                        code += 10;
                        if ( showOrg1 && isCreditRelationshipValidEffective( org1, org1PbTp ) )
                        {
                            code += 1;
                            relations.add( new CreditRelationship( le1, org1PbTp ) );
                        }

                        // check for anonymous org and pb cover not enabled.
                        if ( showOrg2 && isCreditRelationshipValidEffective( pb2, le2.getTradingParty( pb2 ) ) )
                        {
                            code += 2;
                            relations.add( new CreditRelationship( org2PbTp.getLegalEntity(), le2.getTradingParty( pb2 ) ) );
                        }
                    }
                    else
                    {
                        code += 20;
                        if ( showOrg1 && isCreditRelationshipValidEffective( org1, org1PbTp ) )
                        {
                            code += 1;
                            relations.add( new CreditRelationship( le1, org1PbTp ) );
                        }

                        // special pb1 to pb2 credit relation
                        TradingParty org1PbTpForOrg2Pb = org2PbTp.getLegalEntity().getTradingParty( pb1 );
                        if ( org1PbTpForOrg2Pb != null )
                        {
                            if ( isCreditRelationshipValidEffective( pb1, org2PbTp.getLegalEntity().getTradingParty( pb1 ) ) )
                            {
                                code += 2;
                                relations.add( new CreditRelationship( org1PbTp.getLegalEntity(), org2PbTp.getLegalEntity().getTradingParty( pb1 ) ) );
                            }
                        }

                        if ( showOrg2 && isCreditRelationshipValidEffective( pb2, le2.getTradingParty( pb2 ) ) )
                        {
                            code += 4;
                            relations.add( new CreditRelationship( org2PbTp.getLegalEntity(), le2.getTradingParty( pb2 ) ) );
                        }
                    }
                }
                else if ( isLe1PrimeBrokerCreditEnabled )
                {
                    code = 200;
                    if ( org1Anonymous || org2Anonymous )
                    {
                        code += 10;
                        if ( showOrg1 && isCreditRelationshipValidEffective( org1, org1PbTp ) )
                        {
                            code += 1;
                            relations.add( new CreditRelationship( le1, org1PbTp ) );
                        }
                        if ( showOrg2 && isCreditRelationshipValidEffective( pb1, le2.getTradingParty( pb1 ) ) )
                        {
                            code += 2;
                            relations.add( new CreditRelationship( org1PbTp.getLegalEntity(), le2.getTradingParty( pb1 ) ) );
                        }
                    }
                    else
                    {
                        code += 20;
                        if ( showOrg1 && isCreditRelationshipValidEffective( org1, org1PbTp ) )
                        {
                            code += 1;
                            relations.add( new CreditRelationship( le1, org1PbTp ) );
                        }
                        if ( showOrg2 && isCreditRelationshipValidEffective( pb1, le2.getTradingParty( pb1 ) ) )
                        {
                            code += 2;
                            relations.add( new CreditRelationship( org1PbTp.getLegalEntity(), le2.getTradingParty( pb1 ) ) );
                        }
                    }
                }
                else
                {
                    code = 300;
                    if ( org1Anonymous || org2Anonymous )
                    {
                        code += 10;
                        LegalEntity pb2Le = org2PbTp.getLegalEntity();
                        if ( showOrg1 && isCreditRelationshipValidEffective( org1, pb2Le.getTradingParty( org1 ) ) )
                        {
                            code += 1;
                            relations.add( new CreditRelationship( le1, pb2Le.getTradingParty( org1 ) ) );
                        }
                        if ( showOrg2 && isCreditRelationshipValidEffective( pb2, le2.getTradingParty( pb2 ) ) )
                        {
                            code += 2;
                            relations.add( new CreditRelationship( pb2Le, le2.getTradingParty( pb2 ) ) );
                        }
                    }
                    else
                    {
                        code += 20;
                        if ( isCreditRelationshipValidEffective( org1, org1TpForLe2 ) )
                        {
                            code += 1;
                            relations.add( new CreditRelationship( le1, org1TpForLe2 ) );
                        }
                    }
                }
            }
            else
            {
                code = 400;
                if ( isCreditRelationshipValidEffective( org1, org1TpForLe2 ) )
                {
                    code += 1;
                    relations.add( new CreditRelationship( le1, org1TpForLe2 ) );
                }
            }

            // log all the information for troubleshooting.
            log.info( new StringBuilder( 200 ).append( "CU.getCreditRelationships : credit relationship b/w origLe1=" )
                    .append( origLe1 ).append( ",origLe2=" ).append( origLe2 ).append( ",le1=" ).append( le1 ).append( ",le2=" )
                    .append( le2 ).append( ",realOrg1=" ).append( realOrg1 ).append( ",realOrg2=" ).append( realOrg2 )
                    .append( ",showOrg1=" ).append( showOrg1 ).append( ",showOrg2=" ).append( showOrg2 ).append( ",relations=" )
                    .append( relations ).append( ",code=" ).append( code ).toString() );
        }
        catch ( Exception e )
        {
            log.error("CU.getCreditRelationships : Exception while getting the credit relationships between le1=" + le1 + ",le2=" + le2, e);
        }

        return relations;
    }

    public static String getCreditWorkflow( LegalEntity le1, LegalEntity le2 )
    {
        String htmlArrowStr = " &rarr; ";
        String htmlLineBreak = "<br>";
        StringBuilder sb = new StringBuilder();
        Collection<CreditRelationship> relations = CreditUtilC.getCreditRelationships( le1, le2 );
        Iterator<CreditRelationship> relationsIter = relations.iterator();
        if ( relationsIter.hasNext() )
        {
            CreditRelationship relation = relationsIter.next();
            sb.append( relation.getCreditProviderLe().getOrganization().getShortName() ).append( htmlArrowStr ).append( relation.getCreditCounterparty().getLegalEntity().getOrganization().getShortName() );
            Organization creditOrg = relation.getCreditCounterparty().getLegalEntity().getOrganization();

            while ( relationsIter.hasNext() )
            {
                relation = relationsIter.next();
                if ( creditOrg.isSameAs( relation.getCreditProviderLe().getOrganization() ) )
                {
                    sb.append( htmlArrowStr );
                    sb.append( relation.getCreditCounterparty().getLegalEntity().getOrganization().getShortName() );
                }
                else
                {
                    sb.append( htmlLineBreak );
                    sb.append( relation.getCreditProviderLe().getOrganization().getShortName() ).append( htmlArrowStr ).append( relation.getCreditCounterparty().getLegalEntity().getOrganization().getShortName() );
                }
                creditOrg = relation.getCreditCounterparty().getLegalEntity().getOrganization();
            }
        }
        return sb.toString();
    }

    public static boolean isCreditRelationshipEffective( Organization cpo, TradingParty cc )
    {
        return CreditUtilizationManagerC.getInstance().getCreditStatus( cpo, cc ) > CreditLimitConstants.CREDIT_NO_CHECK;
    }

    public static boolean isCreditRelationshipValidEffective( Organization cpo, TradingParty cc )
    {
        return cc != null && CreditUtilizationManagerC.getInstance().getCreditStatus( cpo, cc ) > CreditLimitConstants.CREDIT_NO_CHECK;
    }

    /**
     * Returns the period for which credit utilizations need be created based on the type of the organization.
     *
     * @param org organization
     * @return credit utilization period
     */
    public static DatePeriod getCreditUtilizationPeriod( Organization org )
    {
        DatePeriod dp = null;
        try
        {
            String dpStr = CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean().getFICreditUtilizationPeriod( org );
            if ( log.isDebugEnabled() )
            {
                log.debug( "CU.getCreditUtilizationPeriod : Org=" + org );
            }
            try
            {
                dp = DateTimeFactory.newDatePeriod( dpStr );
            }
            catch ( Exception e )
            {
                log.error( "CU.getCreditUtilizationPeriod : Error in getting date period from configured value for org=" + org + ",dp=" + dpStr, e );
                dp = DateTimeFactory.newDatePeriod( "1Y" );// this is to protect from user error in the configuration.
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.getCreditUtilizationPeriod : Error in getting date period. org=" + org, e );
        }
        return dp;
    }

    /**
     * Create credit workflow message if not already passed in as a parameter and sets the status and errors.
     *
     * @param cwm          credit workflow message
     * @param errorMessage error message
     * @return credit workflow message
     */
    public static CreditWorkflowMessage getCreditWorkflowMessageWithError( CreditWorkflowMessage cwm, String errorMessage )
    {
        if ( cwm == null )
        {
            cwm = CreditLimitPOJOFactory.newCreditWorkflowMessage();
        }
        cwm.addError( errorMessage );
        setCreditWorkflowMessageStatus( cwm, false );
        return cwm;
    }

    public static boolean isCurrencyPairExempted( CounterpartyCreditLimitRule cclr, CurrencyPair cp )
    {
        Collection<CurrencyPair> ccyPairs = getExemptCurrencyPairs( cclr );
        return ccyPairs != null && ccyPairs.contains( cp );
    }

    public static Collection<CurrencyPair> getExemptCurrencyPairs( CounterpartyCreditLimitRule cclr )
    {
        try
        {
            CurrencyPairGroup cpg = getExemptCurrencyPairGroup( cclr );
            return cpg != null ? cpg.getCurrencyPairs() : null;
        }
        catch ( Exception e )
        {
            log.error( "CU.getExemptCurrencyPairs : Exception while retrieving the exempt currency pair list from cclr=" + cclr, e );
        }
        return null;
    }

    public static CurrencyPairGroup getExemptCurrencyPairGroup( CounterpartyCreditLimitRule cclr )
    {
        CurrencyPairGroup cpg = null;
        try
        {
            if ( cclr != null )
            {
                cpg = cclr.isUseDefaultExemptCurrencyPairGroup() ? ( ( CreditLimitRuleSet ) cclr.getRuleSet() ).getExemptCurrencyPairGroup() : cclr.getExemptCurrencyPairGroup();
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.getExemptCurrencyPairGroup : Exception while retrieving the exempt currency pair group from cclr=" + cclr, e );
        }
        return cpg;
    }

    public static PFEConfiguration getAssociatedPFEConfiguration( CreditLimitRuleSet creditLimitRuleSet )
    {
        PFEConfiguration pfeConfiguration = null;
        if ( creditLimitRuleSet != null )
        {
            pfeConfiguration = creditLimitRuleSet.getPfeConfiguration();
        }
        return pfeConfiguration;
    }

    public static boolean isPFEConfigured( CreditLimitRuleSet creditLimitRuleSet )
    {
        boolean isPFEConfigured = false;
        if ( creditLimitRuleSet != null )
        {
            isPFEConfigured = creditLimitRuleSet.isUsePFEConfiguration();
        }
        return isPFEConfigured;
    }

    /**
     * Returns whether tenor coefficients are configured either as a PFE configuration or tenor profile for the credit relationship.
     *
     * @param cclr counterparty credit limit rule
     * @return tenor coefficients configured
     */
    public static boolean isTenorCoefficientsConfigured( CounterpartyCreditLimitRule cclr )
    {
        boolean tenorCoefficientsConfigured = false;
        if ( cclr != null )
        {
            if ( cclr.isUseDefaultTenorProfile() )
            {
                CreditLimitRuleSet clrs = ( CreditLimitRuleSet ) cclr.getRuleSet();
                tenorCoefficientsConfigured = clrs.isUsePFEConfiguration() ? clrs.getPfeConfiguration() != null : clrs.getCreditTenorProfile() != null;
            }
            else
            {
                if ( cclr.isCreditLimitRuleLevelTenorCoefficients () )
                {
                    Collection<CreditLimitRule> rules = ( Collection<CreditLimitRule> ) cclr.getChildrenRules ();
                    for ( CreditLimitRule clr: rules )
                    {
                        tenorCoefficientsConfigured = clr.isUsePFEConfiguration () ? clr.getPfeConfiguration () != null : clr.getCreditTenorProfile () != null;
                        if ( tenorCoefficientsConfigured )
                        {
                            break;
                        }
                    }
                }
                else
                {
                    tenorCoefficientsConfigured = cclr.isUsePFEConfiguration () ? cclr.getPfeConfiguration () != null : cclr.getCreditTenorProfile () != null;
                }
            }
        }
        return tenorCoefficientsConfigured;
    }

    /**
     * Returns whether tenor coefficients are configured either as a PFE configuration or tenor profile for the credit relationship for the credit type.
     *
     * @param cclr counterparty credit limit rule
     * @return tenor coefficients configured
     */
    public static boolean isTenorCoefficientsConfigured( CounterpartyCreditLimitRule cclr, CreditLimitRule clr )
    {
        return CurrencyPositionCollectionBuilderC.isTenorCoefficientsConfigured( cclr, clr );
    }

    public static CreditTenorProfile getAssociatedCreditTenorProfile( CounterpartyCreditLimitRule cclr )
    {
        CreditTenorProfile creditTenorProfile = null;
        if ( cclr != null )
        {
            creditTenorProfile = cclr.getCreditTenorProfile();
        }
        return creditTenorProfile;
    }

    public static CreditTenorProfile getAssociatedCreditTenorProfile( CreditLimitRule clr )
    {
        CreditTenorProfile creditTenorProfile = null;
        if ( clr != null )
        {
            creditTenorProfile = clr.getCreditTenorProfile();
        }
        return creditTenorProfile;
    }

    public static List<PFEConfiguration> getAssociatedCreditTenorProfile( Organization org, CreditTenorProfile ctp )
    {
        CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction(org);
        List<PFEConfiguration> pfeConfigurations = new ArrayList<PFEConfiguration>();
        if ( orgFunc != null )
        {
            Collection<PFEConfiguration> pfeConfigs = orgFunc.getPfeConfigurations();
            for(PFEConfiguration pfeConfiguration:pfeConfigs)
            {
                if( pfeConfiguration != null && pfeConfiguration.getDefaultTenorProfile () != null
                        && pfeConfiguration.getDefaultTenorProfile().getShortName().equals(ctp.getShortName()))
                {
                    pfeConfigurations.add(pfeConfiguration);
                    continue;
                }
                Collection<PFEConfigurationProfile>  pfeConfigurationProfiles = pfeConfiguration.getPfeConfigurationProfiles();
                for (PFEConfigurationProfile pfeTemp : pfeConfigurationProfiles)
                {
                    if(  pfeTemp.getCreditTenorProfile().getShortName().equals(ctp.getShortName()))
                    {
                        pfeConfigurations.add(pfeConfiguration);
                        break;
                    }
                }
            }
        }
        return pfeConfigurations;
    }

    public static CreditTenorProfile getAssociatedCreditTenorProfile( CreditLimitRuleSet creditLimitRuleSet )
    {
        CreditTenorProfile creditTenorProfile = null;
        if ( creditLimitRuleSet != null )
        {
            creditTenorProfile = creditLimitRuleSet.getCreditTenorProfile();
        }
        return creditTenorProfile;
    }

    public static PFEConfiguration getAssociatedPFEConfiguration( CounterpartyCreditLimitRule cclr )
    {
        PFEConfiguration pfeConfiguration = null;
        if ( cclr != null )
        {
            pfeConfiguration = cclr.getPfeConfiguration();
        }
        return pfeConfiguration;
    }

    public static PFEConfiguration getAssociatedPFEConfiguration( CreditLimitRule clr )
    {
        PFEConfiguration pfeConfiguration = null;
        if ( clr != null )
        {
            pfeConfiguration = clr.getPfeConfiguration();
        }
        return pfeConfiguration;
    }

    /**
     * @param cclr counterparty credit limit rule
     * @param clr credit limit rule
     * @param baseCurrency     base currency
     * @param variableCurrency variable currency
     * @return credit tenor profile
     */
    public static CreditTenorProfile getCreditTenorProfile( CounterpartyCreditLimitRule cclr, CreditLimitRule clr, Currency baseCurrency, Currency variableCurrency )
    {
        return CurrencyPositionCollectionBuilderC.getCreditTenorProfile( cclr, clr, baseCurrency, variableCurrency );
    }

    public static int getCreditMode( CounterpartyCreditLimitRule cclr )
    {
        if ( cclr != null )
        {
            return cclr.isUseDefaultMode() || cclr.getMode() == null ? ( ( CreditLimitRuleSet ) cclr.getRuleSet() ).getMode() : cclr.getMode();
        }
        return CreditLimit.CREDIT_CARVE_OUT_MODE;
    }

    public static int getCreditPFEMode( CounterpartyCreditLimitRule cclr )
    {
        if ( cclr != null )
        {
            return null == cclr.getPFEExcludeForDailyExposure() ? CreditLimit.PFE_MODE_DEFAULT : cclr.getPFEExcludeForDailyExposure();
        }
        return CreditLimit.PFE_MODE_DEFAULT;
    }

    public static boolean isApplyPFEOnDailyCredit( CounterpartyCreditLimitRule cclr )
    {
        if ( cclr != null )
        {
            Integer cptyLevelDailyPFEFlag = cclr.getPFEExcludeForDailyExposure();
            if ( cptyLevelDailyPFEFlag == null || CreditLimit.PFE_MODE_DEFAULT == cptyLevelDailyPFEFlag )
            {
                return CreditLimit.PFE_MODE_NO == ( ( CreditLimitRuleSet ) cclr.getRuleSet() ).getPFEExcludeForDailyExposure();
            }
            return CreditLimit.PFE_MODE_NO == cptyLevelDailyPFEFlag;
        }
        return true;
    }

    /**
     * Returns the value of the tenor coefficient which can be directly applied in the trade amount. The value in db is stored in
     * percentage. But, this method returns the multiplication factor for the trade amounts.
     *
     * @param cclr    counterparty credit limit rule
     * @param clr     credit limit rule
     * @param date    date
     * @param baseCcy base currency
     * @param varCcy  variable currency
     * @return tenor coefficient
     */
    public static double getTenorCoefficient( CounterpartyCreditLimitRule cclr, CreditLimitRule clr, IdcDate date, Currency baseCcy, Currency varCcy )
    {
        return CurrencyPositionCollectionBuilderC.getTenorCoefficient( cclr, clr, date, baseCcy, varCcy );
    }

    public static Entity getCreditTenorProfileHolder( CounterpartyCreditLimitRule cclr, CreditLimitRule clr )
    {
        Entity tenorProfileHolder = null;
        if ( cclr != null )
        {
            if ( clr instanceof DailyCreditLimitRule && !isApplyPFEOnDailyCredit( cclr ) )
            {
                return null;
            }

            boolean useDefault = cclr.isUseDefaultTenorProfile();

            if ( useDefault )
            {
                CreditLimitRuleSet clrs = ( ( CreditLimitRuleSet ) cclr.getRuleSet() );
                tenorProfileHolder = clrs.isUsePFEConfiguration() ? clrs.getPfeConfiguration() : clrs.getCreditTenorProfile();
            }
            else
            {
                if ( cclr.isCreditLimitRuleLevelTenorCoefficients () )
                {
                    tenorProfileHolder = clr.isUsePFEConfiguration() ? clr.getPfeConfiguration() : clr.getCreditTenorProfile();
                }
                else
                {
                    tenorProfileHolder = cclr.isUsePFEConfiguration() ? cclr.getPfeConfiguration() : cclr.getCreditTenorProfile();
                }
            }
        }
        return tenorProfileHolder;
    }

    public static Collection<CreditTenorProfile> getCreditTenorProfiles( Organization org )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( "CU.getCreditTenorProfiles : Org=" + org );
        }
        if ( org == null )
        {
            log.error( "CU.getCreditTenorProfiles : Organization cannot be null while retrieving credit tenor profiles" );
            return null;
        }
        try
        {
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction(org);
            return orgFunc.getCreditTenorProfiles();
        }
        catch ( Exception e )
        {
            log.error( "CU.getCreditTenorProfiles : Exception while retrieving credit tenor profiles for org= " + org.getShortName(), e );
        }
        return null;
    }

    public static CreditTenorProfile getCreditTenorProfile( Organization org ,String profileName)
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( "CU.getCreditTenorProfile : Org=" + org );
        }
        if ( org == null )
        {
            log.error( "CU.getCreditTenorProfile : Organization cannot be null while retrieving credit tenor profiles" );
            return null;
        }
        try
        {
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( org );
            Collection<CreditTenorProfile> creditTenorProfiles = orgFunc.getCreditTenorProfiles();
            for(CreditTenorProfile creditTenorProfile: creditTenorProfiles )
            {
                if( creditTenorProfile.getShortName().equals(profileName))
                {
                    return creditTenorProfile;
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.getCreditTenorProfile : Exception while retrieving credit tenor profiles for org= " + org.getShortName(), e );
        }
        return null;
    }

    public static Collection<PFEConfiguration> getPFEConfiguration( Organization org )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( "CU.getPfeConfigurations : Org=" + org );
        }
        if ( org == null )
        {
            log.error( "CU.getPfeConfigurations : Organization cannot be null while retrieving PFEConfigurations" );
            return null;
        }
        try
        {
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( org );
            return orgFunc.getPfeConfigurations();
        }
        catch ( Exception e )
        {
            log.error( "CU.getPfeConfigurations : Exception while retrieving PFEConfigurations  for org= " + org.getShortName(), e );
        }
        return null;
    }

    /**
     * @return a set of organizations that have CreditTenorProfiles configured. Always returns a non-null collection.
     */
    public static Collection<Organization> getOrganizationsWithPFESet()
    {
        Collection<Organization> orgs = CreditDataQueryServiceFactory.getCreditDataQueryService().getOrganizationsWithCreditProfilesSet();
        orgs.addAll(CreditDataQueryServiceFactory.getCreditDataQueryService().getOrganizationsWithCreditPFEConfigurationSet());
        return orgs;
    }

    public static Collection<CreditUtilizationCacheEntry> getMockCreditUtilizationCacheEntries( CreditWorkflowMessage cwm, Entity creditEntity )
    {
        Collection<CreditUtilizationCacheEntry> warmupEntries = new ArrayList<CreditUtilizationCacheEntry>( 2 );
        if ( creditEntity instanceof Trade )
        {
            int hash = creditEntity.hashCode();
            double limitAmt = hash % 7 == 0 ? 0 : 10000000000.0; // this is to have some random credit rejections as well during warm-up.
            final Namespace ns = cwm.getOrganization().getNamespace();
            final Currency limitCcy = CurrencyFactory.getCurrency( "USD" );
            Trade trd = ( Trade ) creditEntity;
            CounterpartyCreditLimitRule cclr = CreditLimitFactory.newCounterpartyCreditLimitRule();
            cclr.setNamespace( ns );
            cclr.setShortName( "WarmupCCLR" );
            CreditLimitRuleSet clrs = CreditLimitFactory.newCreditLimitRuleSet();
            clrs.setNamespace( ns );
            clrs.setShortName( "WarmupCLRS" );
            clrs.addRule( cclr );
            cclr.setRuleSet( clrs );
            clrs.setOrganization( cwm.getOrganization() );
            cclr.setTradingParty( cwm.getTradingParty() );
            cclr.setTradingPartyOrganization( cwm.getTradingPartyOrganization() );

            SingleCreditLimitRule aggClr = CreditLimitFactory.newSingleCreditLimitRule();
            aggClr.setNamespace( ns );
            aggClr.setShortName( "TestAgg" );
            aggClr.setSortOrder( 1 );
            aggClr.setLimitAmount( limitAmt );
            aggClr.setCurrency( limitCcy );
            aggClr.setCreditUtilizationCalculator( GROSS_AGGREGATE_LIMIT_CALCULATOR );
            CreditUtilization aggCu = CreditLimitFactory.newCreditUtilization();
            aggCu.setNamespace( ns );
            aggCu.setCreditLimitRule( aggClr );
            aggClr.setCreditUtilization( aggCu );
            aggCu.setCreditLimitRule( aggClr );
            cclr.getChildrenRules().add( aggClr );
            aggClr.setParentRule( cclr );
            CurrencyPositionCollection aggCcyPosCol = CreditLimitFactory.newCurrencyPositionCollection();
            aggCcyPosCol.setBaseDate( trd.getTradeDate() );
            aggCcyPosCol.setCreditUtilization( aggCu );
            ( ( CreditUtilizationC ) aggCu ).setCurrencyPositions( aggCcyPosCol );

            DailyCreditLimitRule dailyClr = CreditLimitFactory.newDailyCreditLimitRule();
            dailyClr.setNamespace( ns );
            dailyClr.setShortName( "TestDaily" );
            dailyClr.setSortOrder( 2 );
            dailyClr.setLimitAmount( limitAmt );
            dailyClr.setCurrency( limitCcy );
            dailyClr.setCreditUtilizationCalculator( DAILY_SETTLEMENT_LIMIT_CALCULATOR );
            cclr.getChildrenRules().add( dailyClr );
            dailyClr.setParentRule( cclr );

            for ( TradeLeg tl : ( Collection<TradeLeg> ) trd.getTradeLegs() )
            {
                if ( tl instanceof FXLeg )
                {
                    IdcDate valDate = ( ( FXLeg ) tl ).getFXPayment().getValueDate();
                    DailyCreditUtilization dcu = CreditLimitFactory.newDailyCreditUtilization();
                    dcu.setNamespace( ns );
                    dcu.setDate( valDate );
                    dcu.setCreditLimitRule( dailyClr );
                    dailyClr.getCreditUtilizationMap().put( valDate, dcu );
                    CurrencyPositionCollection dailyCcyPosCol = CreditLimitFactory.newCurrencyPositionCollection();
                    dailyCcyPosCol.setBaseDate( trd.getTradeDate() );
                    dailyCcyPosCol.setCreditUtilization( dcu );
                    ( ( CreditUtilizationC ) dcu ).setCurrencyPositions( dailyCcyPosCol );

                    Collection<CreditUtilization> creditUtils = new ArrayList<CreditUtilization>( 2 );
                    creditUtils.add( aggCu );
                    creditUtils.add( dcu );
                    CreditUtilizationCacheEntry cce = new CreditUtilizationCacheEntryC( valDate, creditUtils, cclr, cwm.getOrganization(), false );
                    warmupEntries.add( cce );
                }
            }
        }
        return warmupEntries;
    }

    public static List<CreditEntity> getAllEnabledCreditEntitiesBetween( LegalEntity providerLe, LegalEntity requestorLe )
    {
        List<CreditEntity> allCreditEntitiesBetween = getAllCreditEntitiesBetween( requestorLe, providerLe );
        ArrayList<CreditEntity> enabledCreditTradingParties = new ArrayList<CreditEntity>();
        for ( CreditEntity creditEntity : allCreditEntitiesBetween )
        {
            if ( CreditUtilizationManagerC.getInstance().isCreditEnabled( creditEntity.getLegalEntity().getOrganization(), creditEntity.getTradingParty() ) )
            {
                enabledCreditTradingParties.add( creditEntity );
            }
        }
        return enabledCreditTradingParties;
    }

    public static List<CreditEntity> getAllCreditEntitiesBetween( LegalEntity requestorLe, LegalEntity providerLe )
    {
        return PrimeBrokerPathUtil.getInstance().getCreditEntities( requestorLe,
                providerLe,
                null );
    }

    /**
     *
     * @param requestorLe
     * @param pbLesOnPath
     * @return CreditEntities
     */
    public static List<CreditEntity> getAllCreditEntitiesBetween( LegalEntity requestorLe, List<LegalEntity> pbLesOnPath )
    {
        List<CreditEntity> creditTradingParties = new ArrayList<CreditEntity>();
        PrimeBrokerPathUtil.getInstance().getCreditEntitiesOnPath( requestorLe,pbLesOnPath,creditTradingParties);
        return creditTradingParties;
    }

    /**
     * Returns the other currency amount which is not rounded based on the currency and currency amount.
     *
     * @param amount amount
     * @param ccy    currency
     * @param fxRate fxRate
     * @return amount of other currency
     */
    public static double getAmount( double amount, Currency ccy, FXRate fxRate )
    {
        double rate = fxRate.getRate();
        if ( fxRate.getBaseCurrency().isSameAs( ccy ) )
        {
            return rate * amount;
        }
        else if ( fxRate.getVariableCurrency().isSameAs( ccy ) && rate != 0.0 )
        {
            return amount / rate;
        }
        return 0.0;
    }

    /**
     * Validates the credit provider and counterparty organization.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCptyOrg     credit counterparty organization
     * @param contextInfo       context information
     * @return validation result
     */
    public static  boolean validateCreditCounterpartyOrg( Organization creditProviderOrg, Organization creditCptyOrg, String contextInfo )
    {
        boolean valid = validateCreditProvider(creditProviderOrg, contextInfo);
        if ( valid )
        {
            if ( creditCptyOrg == null || creditCptyOrg.isSameAs( creditProviderOrg ) )
            {
                log.warn( new StringBuilder( 100 ).append( "CLAS." ).append( contextInfo ).append( " : Parameters not specified." ).toString() );
                throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
            }
        }
        return valid;
    }

    /**
     * Validates the credit provider organization by checking the availability of credit limit org function and
     * credit limit rule set.
     *
     * @param creditProviderOrg credit provider org
     * @param contextInfo       context information
     * @return validation result
     */
    public static boolean validateCreditProvider( Organization creditProviderOrg, String contextInfo )
    {
        if ( creditProviderOrg == null )
        {
            log.warn( new StringBuilder( 100 ).append(contextInfo ).append( " : Parameters not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }
        CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet(creditProviderOrg);
        if ( clrs == null || CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg ) == null )
        {
            log.info(new StringBuilder(200).append(contextInfo).append(" : No credit limit ruleset or org function found for org=")
                    .append(creditProviderOrg.getShortName()).toString() );
            return false;
        }
        return true;
    }

    public static LegalEntity getCreditProviderLegalEntity ( final Organization cpo )
    {
        LegalEntity le = null;
        if ( cpo != null )
        {
            le = cpo.getDefaultDealingEntity();
            if (le == null)
            {
                log.info("CU.getCreditProviderLegalEntity : default legal entity is not set for org=" + cpo );
                Collection<LegalEntity> les = cpo.getLegalEntities();
                if (les != null && !les.isEmpty())
                {
                    le = les.iterator().next();
                }
            }
        }
        return le;
    }

    /***
     *
     * @param cwm
     * @param cu
     * @param creditCptyOrg
     * @return
     */
    public static Map<String, String> getJMSProperties(CreditWorkflowMessage cwm, CreditUtilization cu, Organization creditCptyOrg, TradingParty creditCpty) {
        String cuDateStr = null;
        IdcDate cuDate = getCreditUtilizationDate( cu );
        if ( cuDate != null )
        {
            cuDateStr = cuDate.getFormattedDate( IdcDate.DD_MMM_YYYY_HYPHEN );
        }

        Map<String, String> jmsProperties = new HashMap<String, String>( 12 );
        String legEntityName = cwm.getLegalEntity() != null ? cwm.getLegalEntity().getShortName() : null;
        CreditLimitRule clr = cu.getCreditLimitRule();
        String creditType = clr.getClassification().getShortName();
        String creditProviderOrgName = cwm.getOrganization().getShortName();
        String creditCptyOrgName = creditCptyOrg.getShortName();
        double availableReserveAmount = cu.getAvailableReserveAmount();
        String availableReserveAmountStr = String.valueOf( availableReserveAmount );

        // added temporary for troubleshooting.
        if ( cwm.getTransactionId() == null )
        {
            log.warn("############### begin stack trace. tid null in cwm=" + cwm  + ",threadId=" + Thread.currentThread().getId() );
            Thread.dumpStack();
            log.warn("############### end stack trace. tid null in cwm=" + cwm + ",threadId=" + Thread.currentThread().getId());
        }

        jmsProperties.put( CREDIT_EVENT_PROPERTY, cwm.getEventName() );
        jmsProperties.put( CREDIT_RULE_TYPE_PROPERTY, creditType );
        jmsProperties.put( CREDIT_HOLDER_ORGANIZATION_PROPERTY, creditProviderOrgName );
        jmsProperties.put( CREDIT_HOLDER_LEGALENTITY_PROPERTY, legEntityName );
        jmsProperties.put( CREDIT_REQUESTER_TRADINGPARTY_PROPERRTY, creditCpty.getShortName() );
        jmsProperties.put( CREDIT_REQUESTER_ORGANIZATION_PROPERTY, creditCptyOrgName );
        jmsProperties.put( CREDIT_LIMIT_AVAILABLE_AMOUNT_PROPERTY, availableReserveAmountStr );
        jmsProperties.put( CREDIT_LIMIT_CURRENCY_PROPERTY, clr.getCurrency().getShortName() );
        jmsProperties.put( CREDIT_UTILIZATION_DATE_PROPERTY, cuDateStr );
        jmsProperties.put( CREDIT_ENTITY_TRANSACTIONID_PROPERTY, cwm.getTransactionId() );
        jmsProperties.put( VIRTUAL_SERVER_PROPERTY, ConfigurationFactory.getServerMBean().getVirtualServerName() );
        if ( cwm.getTradeDate() != null )
        {
            jmsProperties.put( TRADE_DATE_PROPERTY, cwm.getTradeDate().getFormattedDate( IdcDate.DD_MMM_YYYY_HYPHEN ) );
        }
        if ( MessageStatus.SUCCESS.equals( cwm.getStatus() ) )
        {
            String eventSnapshot = CreditUtilizationCacheSynchronizationHandlerC.createUtilEventSnapshot( cwm );
            jmsProperties.put( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY, eventSnapshot );
            cwm.setParameterValue( CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY, eventSnapshot );//added for testability
        }
        return jmsProperties;
    }

    /**
     * Used for credit reconciliation for trades.
     * @param tids transaction ids
     * @return trades collection
     */
    public static Collection<Trade> queryTradesByTransactionIdList( Collection<String> tids )
    {
        try
        {
            if ( tids != null && ! tids.isEmpty () )
            {
                ExpressionBuilder eb = new ExpressionBuilder();
                Expression expr = eb.get( Entity.Status ).equal( Entity.ACTIVE_STATUS );
                if ( tids.size() >= IdcUtilC.SQL_IN_CLAUSE_LIMIT )
                {
                    Vector<String> tidList = new Vector<String>();
                    int count = 0;
                    for ( String tid : tids )
                    {
                        count++;
                        tidList.add( tid );
                        if ( count % IdcUtilC.SQL_IN_CLAUSE_LIMIT == 0 )
                        {
                            expr = expr.and( eb.get( "transactionID" ).in( tidList ) );
                            tidList = new Vector<String>();
                        }
                    }
                }
                else
                {
                    expr = expr.and( eb.get( "transactionID" ).in( tids ) );
                }
                ReadAllQuery raq = new ReadAllQuery( eb );
                raq.setReferenceClass( Trade.class );
                raq.setSelectionCriteria( expr );
                return ( Collection<Trade> )PersistenceFactory.newSession ().executeQuery( raq );
            }
        }
        catch ( Exception e )
        {
            log.error ( "CU.queryTradesByTransactionIdList : Exception while getting the trades for the transaction id list=" + tids, e  );
        }
        return null;
    }

    /**
     * Reconcile the credit usage from the trade and credit utilization event.
     * @param trade trade
     * @param cue credit utilization event
     * @return report
     */
    public static WorkflowMessage checkTradeCreditUtilization ( Trade trade, CreditUtilizationEvent cue )
    {
        WorkflowMessage wm = MessageFactory.newWorkflowMessage ();
        boolean hasErrors = false;
        wm.setStatus ( MessageStatus.SUCCESS );

        try
        {
            // check state of the trade and cue state.
            TradeStateFacade tsf = ( TradeStateFacade ) trade.getFacade ( TradeStateFacade.TRADE_STATE_FACADE );
            final boolean doneTrade = tsf.isVerified () || tsf.isConfirmed () || tsf.isManual () || tsf.isNet ();
            if ( !doneTrade && cue.isActive () )
            {
                wm.addError ( "Active CUE on rejected trade." );
                hasErrors = true;
            }
            if ( doneTrade && !cue.isActive () )
            {
                wm.addError ( "Passive CUE on done trade." );
                hasErrors = true;
            }

            wm.setObject ( trade );
            if ( trade instanceof FXSwap )
            {
                FXSwap fxSwap = ( FXSwap ) trade;
                FXLeg nearLeg = fxSwap.getNearLeg ();
                FXLeg farLeg = fxSwap.getFarLeg ();
                if ( cue.getSettlementDate ().isSameAs ( nearLeg.getFXPayment ().getValueDate () ) )
                {
                    String result = validateCreditAmounts( nearLeg, cue );
                    if ( result != null )
                    {
                        wm.addError ( result );
                        hasErrors = true;
                    }

                }
                else if ( cue.getSettlementDate ().isSameAs ( farLeg.getFXPayment ().getValueDate () ) )
                {
                    String result = validateCreditAmounts( farLeg, cue );
                    if ( result != null )
                    {
                        wm.addError ( result );
                        hasErrors = true;
                    }
                }
                else
                {
                    wm.addError ( "CUE.settlementDate not matching with FXSwap near/far value date." );
                    hasErrors = true;
                }
            }
            else if ( trade instanceof FXSingleLeg )
            {
                FXSingleLeg fxSingleLeg = ( FXSingleLeg ) trade;
                String result = validateCreditAmounts( fxSingleLeg.getFXLeg (), cue );
                if ( result != null )
                {
                    wm.addError ( result );
                    hasErrors = true;
                }
            }

            // set the status based on the errors.
            wm.setStatus ( hasErrors ? MessageStatus.FAILURE : MessageStatus.SUCCESS );
        }
        catch ( Exception e )
        {
            String tid = trade != null ? trade.getTransactionID () : null;
            log.error ( "CU.checkTradeCreditUtilization : Exception while reconciling credit utilization for the trade tid="  + tid, e );
        }
        return wm;
    }

    private static String validateCreditAmounts ( FXLeg fxLeg, CreditUtilizationEvent cue )
    {
        double tolerance = CreditLimitConstants.CHECKSUM_TOLERANCE ;
        FXPaymentParameters fxPayment = fxLeg.getFXPayment ();
        double principalAmt = cue.getTradePrincipalAmount ();
        double priceAmt = cue.getTradePriceAmount ();
        boolean isCcy1Principal = fxPayment.getCurrency1 ().isSameAs ( cue.getPrincipalCurrency () );
        boolean principalAmountMatch = isCcy1Principal ? Math.abs ( principalAmt - fxPayment.getCurrency1Amount () ) < tolerance  : Math.abs ( principalAmt - fxPayment.getCurrency2Amount () ) < tolerance;
        boolean priceAmountMatch = isCcy1Principal ? Math.abs ( priceAmt - fxPayment.getCurrency2Amount () ) < tolerance  : Math.abs ( priceAmt - fxPayment.getCurrency1Amount () ) < tolerance;
        return principalAmountMatch && priceAmountMatch ? null : fxLeg.getName () +  " - Amount mismatch - principal/priceAmounts="
                + principalAmt + "/" + priceAmt + ",tradeCcy1/Ccy1Amounts=" + fxPayment.getCurrency1Amount () + "/"
                + fxPayment.getCurrency2Amount ();
    }

    public static boolean isCashSettlement( CreditLimitRule clr )
    {
        return clr != null && CreditLimitConstants.AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR.isSameAs ( clr.getCreditUtilizationCalculator () );
    }

    public static boolean isCashSettlementWithLimitCheckEnabled( CreditLimitRule clr )
    {
        return clr != null && isCashSettlement ( clr ) && clr.isAccountLimitCheckPolicyEnabled ();
    }

    public static boolean isCashSettlementWithoutLimitCheckEnabled( CreditLimitRule clr )
    {
        return clr != null && isCashSettlement ( clr ) && !clr.isAccountLimitCheckPolicyEnabled ();
    }

    public static boolean isCashSettlementEnabled( final Organization cpo, final TradingParty cc )
    {
        try
        {
            CreditLimitRuleSet clrs = getCreditLimitRuleSet ( cpo );
            if ( clrs != null && clrs.isEnabled () )
            {
                CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( cpo, cc, true );
                if ( cclr != null && cclr.isEnabled () )
                {
                    SingleCreditLimitRule clr = ( SingleCreditLimitRule ) cclr.getChildRule ( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
                    return clr != null && CreditUtilC.isCashSettlement ( clr );
                }
            }
        }
        catch ( Exception e )
        {
            log.error ( "CU.isCashSettlementEnabled : Exception. cpo=" + cpo + ",cc=" + cc , e );
        }
        return false;
    }

    public static boolean isCashSettlementEnabledWithLimitCheck( final Organization cpo, final TradingParty cc )
    {
        try
        {
            CreditLimitRuleSet clrs = getCreditLimitRuleSet ( cpo );
            if ( clrs != null && clrs.isEnabled () )
            {
                CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( cpo, cc, true );
                if ( cclr != null && cclr.isEnabled () )
                {
                    SingleCreditLimitRule clr = ( SingleCreditLimitRule ) cclr.getChildRule ( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
                    return clr != null && isCashSettlement ( clr ) && isCashSettlementWithLimitCheckEnabled ( clr ) ;
                }
            }
        }
        catch ( Exception e )
        {
            log.error ( "CU.isCashSettlementEnabledWithLimitCheck : Exception. cpo=" + cpo + ",cc=" + cc , e );
        }
        return false;
    }

    public static boolean isCashSettlementEnabledWithNoLimitCheck( final Organization cpo, final TradingParty cc )
    {
        try
        {
            CreditLimitRuleSet clrs = getCreditLimitRuleSet ( cpo );
            if ( clrs != null && clrs.isEnabled () )
            {
                CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( cpo, cc, true );
                if ( cclr != null && cclr.isEnabled () )
                {
                    SingleCreditLimitRule clr = ( SingleCreditLimitRule ) cclr.getChildRule ( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
                    return clr != null && isCashSettlement ( clr ) && !isCashSettlementWithLimitCheckEnabled ( clr ) ;
                }
            }
        }
        catch ( Exception e )
        {
            log.error ( "CU.isCashSettlementEnabledWithNoLimitCheck : Exception. cpo=" + cpo + ",cc=" + cc , e );
        }
        return false;
    }

    public static CreditUtilization getAggregateCreditUtilization( final Organization cpo, final TradingParty cc )
    {
        try
        {
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( cpo, cc, true );
            if ( cclr != null )
            {
                SingleCreditLimitRule clr = ( SingleCreditLimitRule ) cclr.getChildRule ( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
                return clr != null ? clr.getCreditUtilization () : null;
            }
        }
        catch ( Exception e )
        {
            log.error ( "CU.getAggregateCreditUtilization : Exception. cpo=" + cpo + ",cc=" + cc, e );
        }
        return null;
    }

    public static Map<String,CurrencyPosition> getCurrencyPositionsMapForAccountBalance(Organization cpo, TradingParty cc){
        try
        {
            if ( cpo != null && cc != null )
            {
                CreditLimitRuleSet clrs = getCreditLimitRuleSet ( cpo );
                if ( clrs != null && clrs.isEnabled () )
                {
                    CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( cpo, cc, true );
                    if ( cclr != null && cclr.isEnabled () )
                    {
                        SingleCreditLimitRule clr = ( SingleCreditLimitRule ) cclr.getChildRule ( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
                        if ( clr != null && CreditUtilC.isCashSettlement ( clr ) )
                        {
                            CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( cpo );// cache entry for currency position integrity
                            CreditUtilization cu = clr.getCreditUtilization ();
                            CurrencyPositionCollection cpc = cu.getCurrencyPositions ( true );
                            if ( cpc != null )
                            {
                                Map<IdcDate, Map<String, CurrencyPosition>> posMap = cpc.getCurrencyPositionsMap ();
                                if ( posMap != null )
                                {
                                    Map<String, CurrencyPosition> currencyPositionMap = posMap.get ( cpc.getBaseDate () );
                                    if ( currencyPositionMap != null )
                                    {
                                        return currencyPositionMap;
                                    }
                                    else
                                    {
                                        log.warn ( "CU.getCurrencyPositionsMapForAccountBalance : no currency position collection found for cu=" + cu + ".baseDate=" + cpc.getBaseDate () );
                                    }
                                }
                                else
                                {
                                    log.warn ( "CU.getCurrencyPositionsMapForAccountBalance : no currency positions map found for cu=" + cu );
                                }
                            }
                            else
                            {
                                log.warn ( "CU.getCurrencyPositionsMapForAccountBalance : no currency position collection found for cu=" + cu );
                            }
                        }
                    }
                }
            }
            else
            {
                log.info ( "CU.getCurrencyPositionsMapForAccountBalance : null cpo or null cc=" + cc + ",cpo=" + cpo );
            }
        }
        catch ( Exception e )
        {
            log.error ( "CU.getCurrencyPositionsMapForAccountBalance : Exception while retrieving the currency balances for cpo=" + cpo + ",cc=" + cc, e );
        }
        return null;
    }

    public static Map<String,CurrencyPosition> getCurrencyPositionsMap(Organization cpo, TradingParty cc){
        try
        {
            if ( cpo != null && cc != null )
            {
                CreditLimitRuleSet clrs = getCreditLimitRuleSet ( cpo );
                if ( clrs != null && clrs.isEnabled () )
                {
                    CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( cpo, cc, true );
                    if ( cclr != null && cclr.isEnabled () )
                    {
                        SingleCreditLimitRule clr = ( SingleCreditLimitRule ) cclr.getChildRule ( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
                        if ( clr != null )
                        {
                            CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( cpo );// cache entry for currency position integrity
                            CreditUtilization cu = clr.getCreditUtilization ();
                            CurrencyPositionCollection cpc = cu.getCurrencyPositions ( true );
                            if ( cpc != null )
                            {
                                Map<IdcDate, Map<String, CurrencyPosition>> posMap = cpc.getCurrencyPositionsMap ();
                                if ( posMap != null )
                                {
                                    Map<String, CurrencyPosition> currencyPositionMap = posMap.get ( cpc.getBaseDate () );
                                    if ( currencyPositionMap != null )
                                    {
                                        return currencyPositionMap;
                                    }
                                    else
                                    {
                                        log.warn ( "CU.getCurrencyPositionsMap : no currency position collection found for cu=" + cu + ".baseDate=" + cpc.getBaseDate () );
                                    }
                                }
                                else
                                {
                                    log.warn ( "CU.getCurrencyPositionsMap : no currency positions map found for cu=" + cu );
                                }
                            }
                            else
                            {
                                log.warn ( "CU.getCurrencyPositionsMap : no currency position collection found for cu=" + cu );
                            }
                        }
                    }
                }
            }
            else
            {
                log.info ( "CU.getCurrencyPositionsMap : null cpo or null cc=" + cc + ",cpo=" + cpo );
            }
        }
        catch ( Exception e )
        {
            log.error ( "CU.getCurrencyPositionsMap : Exception while retrieving the currency balances for cpo=" + cpo + ",cc=" + cc, e );
        }
        return null;
    }

    public static Map<String, String> getCurrencyAccountBalances( Organization cpo, TradingParty cc )
    {
        Map<String, String> balances = new TreeMap<String, String>();
        try
        {
            Map<String, CurrencyPosition> currencyPositionMap = getCurrencyPositionsMapForAccountBalance(cpo,cc);
            if( currencyPositionMap != null ) {
                Map<String, CurrencyPosition> tempMap = new HashMap<String, CurrencyPosition>(currencyPositionMap);
                for (String ccy : tempMap.keySet()) {
                    CurrencyPosition cp = currencyPositionMap.get(ccy);
                    Currency currency = cp.getCurrency();
                    balances.put(currency.getShortName(), cp.getFormattedNetAmount ( null, false ) );
                }
            }
        }
        catch ( Exception e )
        {
            log.error ( "CU.getCurrencyAccountBalances : Exception while retrieving the currency balances for cpo=" + cpo + ",cc=" + cc, e );
        }
        return balances;
    }

    public static Map<String, String> getSupportedCurrencyAccountBalances( Organization lpOrg, TradingParty lptTpForFi )
    {
        return getSupportedCurrencyAccountBalances( lpOrg, lptTpForFi, null, false );
    }

    public static Map<String, String> getSupportedCurrencyAccountBalances( Organization lpOrg, TradingParty lptTpForFi, User user, boolean userFormat )
    {
        Map<String, String> balances = new LinkedHashMap<String, String> ();
        boolean accountEnabled = isAccountEnabled ( lpOrg, lptTpForFi );
        if ( accountEnabled )
        {
            DecimalFormat format = user != null && userFormat ? user.getDisplayPreference ().getDecimalFormat () : new DecimalFormat( FormatUtilC.AMOUNT_FORMAT );
            Collection<Currency> currenciesSet = new TreeSet<Currency> ( new CurrencyComparatorC () );
            Collection<Currency> supportedCurrenciesList = CurrencyUtil.getCurrencies ( lptTpForFi.getLegalEntityOrganization (), lpOrg );
            if ( supportedCurrenciesList != null )
            {
                currenciesSet.addAll ( supportedCurrenciesList );
            }

            try
            {
                Map<String, CurrencyPosition> currencyPositionMap = getCurrencyPositionsMapForAccountBalance ( lpOrg, lptTpForFi );
                Map<String, CurrencyPosition> tempCcyPosMap = null;
                if ( currencyPositionMap != null )
                {
                    tempCcyPosMap = new HashMap<String, CurrencyPosition> ( currencyPositionMap );
                    for ( String ccy : tempCcyPosMap.keySet () )
                    {
                        CurrencyPosition cp = currencyPositionMap.get ( ccy );
                        if ( cp != null )
                        {
                            currenciesSet.add ( cp.getCurrency () );
                        }
                    }
                }

                for ( Currency ccy : currenciesSet )
                {
                    CurrencyPosition cp = tempCcyPosMap != null ? tempCcyPosMap.get ( ccy.getShortName () ) : null;
                    if ( cp != null )
                    {
                        balances.put ( ccy.getShortName (), cp.getFormattedNetAmount ( user, userFormat ) );
                    }
                    else
                    {
                        balances.put ( ccy.getShortName (), ccy.getDecimalFormat ( format ).format ( 0.0 ) );
                    }
                }
            }
            catch ( Exception e )
            {
                log.error ( "CU.getSupportedCurrencyAccountBalances : Exception while retrieving the currency balances for lpOrg=" + lpOrg + ",lptTpForFi=" + lptTpForFi, e );
            }
        }
        return balances;
    }

    public static Map<String, String> getCurrencyAccountBalances( Organization lpOrg, TradingParty lptTpForFi, User user, boolean userFormat, Collection<Currency> ccyList )
    {
        Map<String, String> balances = new LinkedHashMap<String, String> ();
        boolean accountEnabled = isAccountEnabled ( lpOrg, lptTpForFi );
        if ( accountEnabled )
        {
            DecimalFormat format = user != null && userFormat ? user.getDisplayPreference ().getDecimalFormat () : new DecimalFormat( FormatUtilC.AMOUNT_FORMAT );
            Collection<Currency> currenciesSet = new TreeSet<Currency> ( new CurrencyComparatorC () );
            currenciesSet.addAll ( ccyList );
            Collection<Currency> supportedCurrenciesList = CurrencyUtil.getCurrencies ( lptTpForFi.getLegalEntityOrganization (), lpOrg );

            try
            {
                Map<String, CurrencyPosition> currencyPositionMap = getCurrencyPositionsMapForAccountBalance ( lpOrg, lptTpForFi );
                Map<String, CurrencyPosition> tempCcyPosMap = null;
                if ( currencyPositionMap != null )
                {
                    tempCcyPosMap = new HashMap<String, CurrencyPosition> ( currencyPositionMap );
                    for ( String ccy : tempCcyPosMap.keySet () )
                    {
                        CurrencyPosition cp = currencyPositionMap.get ( ccy );
                        if ( cp != null )
                        {
                            currenciesSet.add ( cp.getCurrency () );
                        }
                    }
                }

                for ( Currency ccy : currenciesSet )
                {
                    CurrencyPosition cp = tempCcyPosMap != null ? tempCcyPosMap.get ( ccy.getShortName () ) : null;
                    if ( cp != null )
                    {
                        balances.put ( ccy.getShortName (), cp.getFormattedNetAmount ( user, userFormat ) );
                    }
                    else
                    {
                        boolean ccySupportedByLP = supportedCurrenciesList != null && supportedCurrenciesList.contains ( ccy );
                        balances.put ( ccy.getShortName (), ccySupportedByLP ? ccy.getDecimalFormat ( format ).format ( 0.0 ) : ISCommonConstants.NA );
                    }
                }
            }
            catch ( Exception e )
            {
                log.error ( "CU.getCurrencyAccountBalances : Exception while retrieving the currency balances for lpOrg=" + lpOrg + ",lptTpForFi=" + lptTpForFi + ",ccyList=" + ccyList, e );
            }
        }
        return balances;
    }

    public static Map<String, Double> getCurrencyAccountBalanceAmounts( Organization lpOrg, TradingParty lptTpForFi, User user, boolean userFormat, Collection<Currency> ccyList )
    {
        Map<String, Double> balances = new LinkedHashMap<String, Double> ();
        boolean accountEnabled = isAccountEnabled ( lpOrg, lptTpForFi );
        if ( accountEnabled )
        {
            Collection<Currency> currenciesSet = new TreeSet<Currency> ( new CurrencyComparatorC () );
            currenciesSet.addAll ( ccyList );

            try
            {
                Map<String, CurrencyPosition> currencyPositionMap = getCurrencyPositionsMapForAccountBalance ( lpOrg, lptTpForFi );
                Map<String, CurrencyPosition> tempCcyPosMap = null;
                if ( currencyPositionMap != null )
                {
                    tempCcyPosMap = new HashMap<String, CurrencyPosition> ( currencyPositionMap );
                    for ( String ccy : tempCcyPosMap.keySet () )
                    {
                        CurrencyPosition cp = currencyPositionMap.get ( ccy );
                        if ( cp != null )
                        {
                            currenciesSet.add ( cp.getCurrency () );
                        }
                    }
                }

                for ( Currency ccy : currenciesSet )
                {
                    CurrencyPosition cp = tempCcyPosMap != null ? tempCcyPosMap.get ( ccy.getShortName () ) : null;
                    if ( cp != null )
                    {
                        balances.put ( ccy.getShortName (), cp.getNetAmount () );
                    }
                    else
                    {
                        balances.put ( ccy.getShortName (), 0.0 );
                    }
                }
            }
            catch ( Exception e )
            {
                log.error ( "CU.getCurrencyAccountBalanceAmounts : Exception while retrieving the currency balances for lpOrg=" + lpOrg + ",lptTpForFi=" + lptTpForFi + ",ccyList=" + ccyList, e );
            }
        }
        return balances;
    }


    public static Map<String, String> getCurrencyPositions( Organization lpOrg, TradingParty lptTpForFi, User user, boolean userFormat, Collection<Currency> ccyList )
    {
        Map<String, String> balances = new LinkedHashMap<String, String> ();
        boolean creditActive = isCreditActive ( lpOrg, lptTpForFi );
        if ( creditActive )
        {
            DecimalFormat format = user != null && userFormat ? user.getDisplayPreference ().getDecimalFormat () : new DecimalFormat( FormatUtilC.AMOUNT_FORMAT );
            Collection<Currency> currenciesSet = new TreeSet<Currency> ( new CurrencyComparatorC () );
            currenciesSet.addAll ( ccyList );
            Collection<Currency> supportedCurrenciesList = CurrencyUtil.getCurrencies ( lptTpForFi.getLegalEntityOrganization (), lpOrg );

            try
            {
                Map<String, CurrencyPosition> currencyPositionMap = getCurrencyPositionsMap ( lpOrg, lptTpForFi );
                Map<String, CurrencyPosition> tempCcyPosMap = null;
                if ( currencyPositionMap != null )
                {
                    tempCcyPosMap = new HashMap<String, CurrencyPosition> ( currencyPositionMap );
                    for ( String ccy : tempCcyPosMap.keySet () )
                    {
                        CurrencyPosition cp = currencyPositionMap.get ( ccy );
                        if ( cp != null )
                        {
                            currenciesSet.add ( cp.getCurrency () );
                        }
                    }
                }

                for ( Currency ccy : currenciesSet )
                {
                    CurrencyPosition cp = tempCcyPosMap != null ? tempCcyPosMap.get ( ccy.getShortName () ) : null;
                    if ( cp != null )
                    {
                        balances.put ( ccy.getShortName (), cp.getFormattedNetAmount ( user, userFormat ) );
                    }
                    else
                    {
                        boolean ccySupportedByLP = supportedCurrenciesList != null && supportedCurrenciesList.contains ( ccy );
                        balances.put ( ccy.getShortName (), ccySupportedByLP ? ccy.getDecimalFormat ( format ).format ( 0.0 ) : ISCommonConstants.NA );
                    }
                }
            }
            catch ( Exception e )
            {
                log.error ( "CU.getCurrencyAccountBalances : Exception while retrieving the currency balances for lpOrg=" + lpOrg + ",lptTpForFi=" + lptTpForFi + ",ccyList=" + ccyList, e );
            }
        }
        return balances;
    }

    public static CurrencyPosition getCurrencyAccountBalance( Organization cpo, TradingParty cc, Currency ccy )
    {
        CurrencyPosition cp = null;
        try
        {
            if ( cpo != null && cc != null )
            {
                CreditLimitRuleSet clrs = getCreditLimitRuleSet ( cpo );
                if ( clrs != null && clrs.isEnabled () )
                {
                    CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( cpo, cc, true );
                    if ( cclr != null && cclr.isEnabled () )
                    {
                        SingleCreditLimitRule clr = ( SingleCreditLimitRule ) cclr.getChildRule ( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
                        if ( clr != null && CreditUtilC.isCashSettlement ( clr ) )
                        {
                            CreditUtilizationManagerC.getInstance().getAggregateCreditUtililzations( cpo );// cache entry for currency position integrity
                            CreditUtilization cu = clr.getCreditUtilization ();
                            CurrencyPositionCollection cpc = cu.getCurrencyPositions ( true );
                            if ( cpc != null )
                            {
                                cp = new AccountCurrencyPositionC ( ccy );
                                CurrencyPosition origCp = cpc.getCurrencyPosition ( ccy );
                                if ( origCp != null )
                                {
                                    cp.addAmount ( origCp.getNetAmountBigDecimal () );
                                }
                            }
                            else
                            {
                                log.warn ( "CU.getCurrencyAccountBalance : no currency position collection found for cu=" + cu );
                            }
                        }
                    }
                }
            }
            else
            {
                log.info ( "CU.getCurrencyAccountBalance : null cpo or null cc=" + cc + ",cpo=" + cpo + ",ccy=" + ccy );
            }
        }
        catch ( Exception e )
        {
            log.error ( "CU.getCurrencyAccountBalance : Exception while retrieving the currency balance for cpo=" + cpo + ",cc=" + cc + ",ccy=" + ccy, e );
        }
        return cp;
    }

    public static boolean isAccountEnabled( Organization cpo, TradingParty cc )
    {
        if ( cpo != null && cc != null )
        {
            CreditLimitRuleSet clrs = getCreditLimitRuleSet ( cpo );
            if ( clrs != null && clrs.isEnabled () )
            {
                CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( cpo, cc, true );
                if ( cclr != null && cclr.isEnabled () )
                {
                    SingleCreditLimitRule clr = ( SingleCreditLimitRule ) cclr.getChildRule ( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
                    return clr != null && CreditUtilC.isCashSettlement ( clr );
                }
            }
        }
        return false;
    }


    public static double applyContractMultiplierOnReserveCredit(double originValue, CurrencyPair currencyPair){
        Double multiplier = QuoteConventionUtilC.getInstance().getContractMultiplier(currencyPair);
        if (multiplier != null) {
            return MathUtilC.multiply(originValue, multiplier.doubleValue());
        }
        return originValue;
    }

    public static void applyContractMultiplierOnCreditLimit(FXDealingLimit originalLimit, CurrencyPair currencyPair){
        Double multiplier = QuoteConventionUtilC.getInstance().getContractMultiplier(currencyPair);
        if (multiplier != null) {
            if(log.isDebugEnabled()){
                log.debug(String.format("applyContractMultiplierOnCreditLimit for %s. OrginalLimit:%s/%s, multiplier:%s",
                        currencyPair.getDisplayName(), originalLimit.getBidLimit(), originalLimit.getOfferLimit(),multiplier));
            }
            originalLimit.setBidLimit(MathUtilC.divide(
                    originalLimit.getBidLimit(),
                    multiplier.doubleValue(),
                    currencyPair.getVariableCurrency().getPrecision(),
                    BigDecimal.ROUND_FLOOR));

            originalLimit.setOfferLimit(MathUtilC.divide(
                    originalLimit.getOfferLimit(),
                    multiplier.doubleValue(),
                    currencyPair.getVariableCurrency().getPrecision(),
                    BigDecimal.ROUND_FLOOR));
        }
    }

    public static double applyContractMultiplierOnBaseCcyAmt(double originValue, CurrencyPair currencyPair){
        Double multiplier = QuoteConventionUtilC.getInstance().getContractMultiplier(currencyPair);
        if (multiplier != null) {
            if(log.isDebugEnabled()){
                log.debug(String.format("applyContractMultiplierOnBaseCcyAmt for %s. OrginalAmt:%s, multiplier:%s",
                        currencyPair.getDisplayName(), originValue, multiplier));
            }
            return MathUtilC.divide(
                    originValue,
                    multiplier.doubleValue(),
                    currencyPair.getBaseCurrency().getPrecision(),
                    BigDecimal.ROUND_FLOOR);
        }
        return originValue;
    }

    public static boolean isMarginRevaluationEnabled()
    {
        boolean revalMarginConfigured = CreditLimitConstants.adminConfigMBean.isCreditUtilizationStopOutRevaluationEnabled ();
        boolean isPrimaryAdminServer = ConfigurationFactory.getServerMBean ().isAdminServer () && PrimaryAdminIdentifier.getInstance().isPrimary();
        return revalMarginConfigured || isPrimaryAdminServer;
    }

    public static boolean isAggregateMultiFactorSettlement( CreditLimitRule clr )
    {
        return clr != null && CreditLimitConstants.AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR.isSameAs ( clr.getCreditUtilizationCalculator () );
    }

    private static class PrimeBrokerPathUtilIntegrationHelperC implements PrimeBrokerPathUtil.IntegrationHelper{

        @Override
        public List<String> getCCPPriority(String cmOrgName) {
            return RexCommonMBeanC.getInstance().getCCPPriority(cmOrgName);
        }
    }

    public static void updateCurrencyPositionConversionRates( CreditUtilization cu, Map<String, FXPrice> rateMap, boolean reset )
    {
        try
        {
            FXMarketDataSet fxMds = CreditUtilC.getMarketDataSet( cu );
            CurrencyPositionCollection cpc = cu.getCurrencyPositions( false );
            if ( cpc != null )
            {
                cpc.updateCurrencyPositionConversionRates( fxMds, rateMap, reset );
            }
        }
        catch ( Exception e )
        {
            log.error( "CU.updateCurrencyPositionConversionRates : Exception while updating the conversion rates in ccy position for cu=" + cu, e );
        }
    }


}