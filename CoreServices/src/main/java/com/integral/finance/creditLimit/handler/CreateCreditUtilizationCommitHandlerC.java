package com.integral.finance.creditLimit.handler;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.finance.creditLimit.admin.CreditAdminServiceLoggerC;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationFactory;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.time.DatePeriod;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;

/**
 * This class is used as a transaction commit handler which will be executed after transaction is successfully committed.
 * This is used to create credit utilizations for future dates based on credit provider organization configuration.
 *
 * <AUTHOR> Development Corp.
 */
public class CreateCreditUtilizationCommitHandlerC implements MessageHandler
{
    protected Log log = LogFactory.getLog( CreditAdminServiceLoggerC.class );

    /**
     * Instance variable for credit provider organization.
     */
    private Organization creditProviderOrg;

    /**
     * Instance variable for credit counterparty organization.
     */
    private Organization creditCptyOrg;

    /**
     * Instance variable for credit counterparty.
     */
    private TradingParty creditCpty;
    
    private CreditLimitConfigurationMBean creditLimitConfigMBean = CreditLimitConfigurationFactory.getCreditConfigurationMBean();

    public CreateCreditUtilizationCommitHandlerC( Organization providerOrg, Organization cptyOrg, TradingParty creditTp )
    {
        this();
        this.creditProviderOrg = providerOrg;
        this.creditCptyOrg = cptyOrg;
        this.creditCpty = creditTp;
    }

    /**
     * Private constructor to avoid creation without credit limit rule.
     */
    private CreateCreditUtilizationCommitHandlerC()
    {
    }

    public Message handle( Message message )
    {
        try
        {
            log.warn( new StringBuilder( 200 ).append( "CreateCreditUtilizationCommitHandlerC.handle.INFO : Creating credit utilizations for creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCptyOrg=" ).append( creditCptyOrg ).append( ",creditCpty=" )
                    .append( creditCpty ).toString() );

            // create credit utilizations for the new organization level counterparty credit limit rule.
            LegalEntity le = CreditUtilC.getDefaultlegalEntity( creditProviderOrg );
            IdcDate startDate = DateTimeFactory.newDate();
            DatePeriod dp = CreditUtilC.getCreditUtilizationPeriod( creditProviderOrg );
            IdcDate endDate = startDate.addDays( dp.asDays() );
            boolean createAggregateCuOnly = false;
            if ( !creditLimitConfigMBean.isCreditUtilizationPersistenceOracleEnabled(this.creditProviderOrg.getShortName()))
            {
                log.info("CreateCreditUtilizationCommitHandlerC.handle : CreditUtilization Oracle persistence is disabled. So creating only aggregate CU and not creating future daily CUs");
                createAggregateCuOnly = true;
            }
            CreditUtilizationManagerC.getInstance().createCreditUtilizations( le, creditCptyOrg, creditCpty, startDate, endDate, createAggregateCuOnly);
        }
        catch ( Exception e )
        {
            log.error( new StringBuilder( 200 ).append( "CreateCreditUtilizationCommitHandlerC.handle.ERROR : Error creating credit utilizations for creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCptyOrg=" ).append( creditCptyOrg ).append( ",creditCpty=" )
                    .append( creditCpty ).toString(), e );
        }
        return message;
    }

}

