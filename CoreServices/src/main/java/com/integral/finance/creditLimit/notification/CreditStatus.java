package com.integral.finance.creditLimit.notification;

public enum CreditStatus {

  NO_CHECK((byte) 0), ACTIVE((byte) 1), SUSPEND((byte) 2);

  private final byte status;

  private CreditStatus(byte aStatus) {
    this.status = aStatus;
  }

  public static CreditStatus getCreditStatus(byte aStatus) {
    switch (aStatus) {
      case 0:
        return NO_CHECK;
      case 1:
        return ACTIVE;
      case 2:
        return SUSPEND;
      default:
        return null;
    }
  }

  public byte getStatus() {
    return status;
  }

  public boolean isActive() {
    return this == ACTIVE;
  }

  public boolean isNoCheck() {
    return this == NO_CHECK;
  }

  public boolean isSuspended() {
    return this == SUSPEND;
  }
}
