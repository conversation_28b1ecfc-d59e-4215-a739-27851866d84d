package com.integral.finance.creditLimit.handler;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.finance.creditLimit.CounterpartyCreditLimitRule;
import com.integral.finance.creditLimit.CreditLimitConstants;
import com.integral.finance.creditLimit.CreditUtilization;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.finance.creditLimit.admin.CreditAdminServiceLoggerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.persistence.Entity;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.util.IdcUtilC;

import java.util.Collection;

/**
 * This class is used as a transaction commit handler which will be executed after transaction is successfully committed.
 * This is used to assess credit utilization and send notification emails if necessary.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditUtilizationNotificationCommitHandlerC implements MessageHandler
{
    protected static Log log = LogFactory.getLog( CreditAdminServiceLoggerC.class );

    /**
     * Instance variable for credit counterparty.
     */
    private Collection<CreditUtilization> creditUtils;

    /**
     * Instance variable for counterparty credit limit rule
     */
    private CounterpartyCreditLimitRule counterpartyCreditLimitRule;

    /**
     * Instance variable for trade object
     */
    private Entity creditEntity;

    private boolean warmup;

    public CreditUtilizationNotificationCommitHandlerC( CounterpartyCreditLimitRule cclr, Collection<CreditUtilization> utils, Entity trade, boolean warmUpMode )
    {
        this();
        this.creditUtils = utils;
        this.counterpartyCreditLimitRule = cclr;
        this.creditEntity = trade;
        this.warmup = warmUpMode;
    }

    /**
     * Private constructor to avoid creation without credit limit rule.
     */
    private CreditUtilizationNotificationCommitHandlerC()
    {
    }

    public Message handle( Message message )
    {
        try
        {
			if ( ConfigurationFactory.getServerMBean().isTradingServer() && ConfigurationFactory.getServerMBean().isCreditOnNotificationServerEnabled() ) {
				log.info("CreditWorkflowManagerC.performCreditUtilizationTransaction : Credit Persistence disabled on Trading server");
			}
			else {
				CreditUtilizationManagerC.getInstance().getCreditUtilizationPersistenceTask().addCreditUtilizations(counterpartyCreditLimitRule, creditUtils, warmup);
				CreditUtilizationManagerC.getInstance().getCreditUtilizationNotificationTask().addCreditUtilizations(creditUtils, IdcUtilC.getSessionContextUser(), warmup);
			}
		}
        catch ( Exception e )
        {
            log.error( new StringBuilder( 200 ).append( "CreditUtilizationNotificationCommitHandlerC.handle.ERROR : Error sending notifications for credit utils=" )
                    .append( creditUtils ).toString(), e );
        }
        return message;
    }

}

