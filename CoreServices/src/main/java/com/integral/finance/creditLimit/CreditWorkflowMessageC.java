package com.integral.finance.creditLimit;

// Copyright (c) 2001-2005 Integral Development Corp. All rights reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.marketData.MarketDataSet;
import com.integral.message.EntityReference;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessageC;
import com.integral.time.IdcDate;
import com.integral.user.Organization;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;

/**
 * CreditWorkflowMessageC represents a credit workflow message, used to drive the
 * credit rule set.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditWorkflowMessageC extends WorkflowMessageC
        implements CreditWorkflowMessage
{
    private EntityReference organization;
    private EntityReference legalEntity;
    private EntityReference tradingParty;
    private EntityReference marketDataSet;
    private EntityReference tradingPartyOrganization;

    private IdcDate businessDate;
    private Character amountType;
    private String errorCode, errorDescription, transactionId;
    private IdcDate tradeDate;
    private boolean exempt = false;
    private boolean tenorRestrictionsBypassed, overUtilized;

    private transient Collection<CreditUtilizationEvent> utilEvents;
    private transient Collection<CreditUtilizationEvent> utilEventsForNotification;
    private transient CreditLimitRule creditLimitRule;
    private transient Collection<CreditLimitRule> creditLimitRules;
    private transient CounterpartyCreditLimitRule counterpartyCreditLimitRule;
    private final long[] eventTimes = new long[8];
    public static String[] TAKE_CREDIT_EVENTS = new String[]{BEGIN_TAKE_CREDIT, RETRIEVED_RULESET, END_EXECUTE_RULESET, BUILT_EVENTS, BEGIN_PERFORM, END_PERFORM, SET_STATE, END_TAKE_CREDIT};

    public CreditWorkflowMessageC()
    {
        super();
    }

    public CreditLimitRule getCreditLimitRule()
    {
        return creditLimitRule;
    }

    public void setCreditLimitRule( CreditLimitRule clr )
    {
        creditLimitRule = clr;
    }

    public Collection<CreditLimitRule> getCreditLimitRules()
    {
        if ( creditLimitRules == null )
        {
            creditLimitRules = new ArrayList<CreditLimitRule>( 2 );
        }
        return creditLimitRules;
    }

    public void setCreditLimitRules( Collection<CreditLimitRule> c )
    {
        if ( c != null )
        {
            if ( creditLimitRules == null )
            {
                creditLimitRules = c;
            }
            else
            {
                getCreditLimitRules().clear();
                getCreditLimitRules().addAll( c );
            }
        }
        else if ( creditLimitRules != null )
        {
            getCreditLimitRules().clear();
        }
    }

    public Organization getOrganization()
    {
        if ( organization == null )
        {
            return null;
        }
        else
        {
            return ( Organization ) organization.getObject();
        }
    }

    public void setOrganization( Organization org )
    {
        if ( organization == null )
        {
            organization = MessageFactory.newEntityReference();
        }
        organization.setObject( org );
    }

    public LegalEntity getLegalEntity()
    {
        if ( legalEntity != null )
        {
            return ( LegalEntity ) legalEntity.getObject();
        }
        else
        {
            return null;
        }
    }

    public void setLegalEntity( LegalEntity le )
    {
        if ( legalEntity == null )
        {
            legalEntity = MessageFactory.newEntityReference();
        }
        legalEntity.setObject( le );
    }

    public TradingParty getTradingParty()
    {
        if ( tradingParty == null )
        {
            return null;
        }
        else
        {
            return ( TradingParty ) tradingParty.getObject();
        }
    }

    public void setTradingParty( TradingParty tp )
    {
        if ( tradingParty == null )
        {
            tradingParty = MessageFactory.newEntityReference();
        }
        tradingParty.setObject( tp );
    }

    public Organization getTradingPartyOrganization()
    {
        if ( tradingPartyOrganization != null && tradingPartyOrganization.getObject() != null )
        {
            return ( Organization ) tradingPartyOrganization.getObject();
        }
        else if ( getTradingParty() != null )
        {
            return getTradingParty().getLegalEntityOrganization();
        }
        return null;
    }

    public void setTradingPartyOrganization( Organization org )
    {
        if ( tradingPartyOrganization == null )
        {
            tradingPartyOrganization = MessageFactory.newEntityReference();
        }
        tradingPartyOrganization.setObject( org );
    }

    public Collection<CreditUtilizationEvent> getCreditUtilizationEvents()
    {
        if ( utilEvents == null )
        {
            utilEvents = new HashSet<CreditUtilizationEvent>( 4 );
        }
        return utilEvents;
    }

    public void setCreditUtilizationEvents( Collection<CreditUtilizationEvent> events )
    {
        getCreditUtilizationEvents().clear();
        if ( events != null )
        {
            getCreditUtilizationEvents().addAll( events );
        }
    }

    public Collection<CreditUtilizationEvent> getCreditUtilizationEventsForNotification()
    {
        if ( utilEventsForNotification == null )
        {
            utilEventsForNotification = new HashSet<CreditUtilizationEvent>( 4 );
        }
        return utilEventsForNotification;
    }

    public void setCreditUtilizationEventsForNotification( Collection<CreditUtilizationEvent> events )
    {
        getCreditUtilizationEventsForNotification().clear();
        if ( events != null )
        {
            getCreditUtilizationEventsForNotification().addAll( events );
        }
    }

    public IdcDate getBusinessDate()
    {
        return businessDate;
    }


    public void setBusinessDate( IdcDate date )
    {
        businessDate = date;
    }

    public MarketDataSet getMarketDataSet()
    {
        if ( marketDataSet == null )
        {
            return null;
        }
        else
        {
            return ( MarketDataSet ) marketDataSet.getObject();
        }
    }

    public void setMarketDataSet( MarketDataSet mds )
    {
        if ( marketDataSet == null )
        {
            marketDataSet = MessageFactory.newEntityReference();
        }
        marketDataSet.setObject( mds );
    }


    public Character getAmountType()
    {
        return amountType;
    }

    public void setAmountType( Character type )
    {
        amountType = type;
    }

    public CounterpartyCreditLimitRule getCounterpartyCreditLimitRule()
    {
        return counterpartyCreditLimitRule;
    }

    public void setCounterpartyCreditLimitRule( CounterpartyCreditLimitRule cclr )
    {
        this.counterpartyCreditLimitRule = cclr;
    }

    public long getEventTime( int eventIndex )
    {
        return eventTimes[eventIndex];
    }

    public void setEventTime( int eventIndex, long eventTime )
    {
        eventTimes[eventIndex] = eventTime;
    }

    public String getErrorCode()
    {
        return errorCode;
    }

    public void setErrorCode( String code )
    {
        this.errorCode = code;
        this.errorDescription = initErrorDescription();
    }

    public String getErrorDescription()
    {
        if ( errorDescription == null )
        {
            errorDescription = initErrorDescription();
        }
        return errorDescription;
    }

    public void setErrorDescription( String description )
    {
        this.errorDescription = description;
    }

    public void addError( String errorCode )
    {
        super.addError( errorCode );
        setErrorCode( errorCode );
    }

    public void addError( String errorCode, Object arg1 )
    {
        super.addError( errorCode, arg1 );
        setErrorCode( errorCode );
    }

    public String getTransactionId()
    {
        return transactionId;
    }

    public void setTransactionId( String tid )
    {
        this.transactionId = tid;
    }

    public IdcDate getTradeDate()
    {
        return tradeDate;
    }

    public void setTradeDate( IdcDate trdDate )
    {
        this.tradeDate = trdDate;
    }

    public boolean isCreditExempt()
    {
        return exempt;
    }

    public void setCreditExempt( boolean exempted )
    {
        this.exempt = exempted;
    }

    @Override
    public boolean isTenorRestrictionsBypassed()
    {
        return tenorRestrictionsBypassed;
    }

    @Override
    public void setTenorRestrictionsBypassed ( boolean flag )
    {
        this.tenorRestrictionsBypassed = flag;
    }

    @Override
    public boolean isOverUtilized()
    {
        return overUtilized;
    }

    @Override
    public void setOverUtilized ( boolean overUtilized )
    {
        this.overUtilized = overUtilized;
    }

    @Override
    public String getCreditLineDescription()
    {
        StringBuilder sb = new StringBuilder( 128 );
        sb.append( getOrganization() != null ? getOrganization ().getShortName() : "N/A" );
        sb.append( "->" ).append( getTradingParty() != null ? getTradingParty ().getShortName() : "N/A" );
        sb.append ( '@' ).append ( getTradingPartyOrganization () != null ? getTradingPartyOrganization ().getShortName () : "N/A" );
        return sb.toString();
    }

    public String toString()
    {
        StringBuilder text = new StringBuilder( 300 );
        append( text, "object=", getObject () );
        append( text, ",event=", getEventName () );
        append( text, ",status=", getStatusName () );
        append( text, ",le=", getLegalEntity() != null ? getLegalEntity ().getFullyQualifiedName () : null );
        append( text, ",cpo=", getOrganization() != null ? getOrganization ().getShortName () : null );
        append( text, ",tp=", getTradingParty() != null ? getTradingParty ().getFullyQualifiedName () : null );
        append( text, ",tpOrg=", getTradingPartyOrganization() != null ? getTradingPartyOrganization ().getShortName () : null );
        append( text, ",params=", getParameters() );
        return text.toString();
    }

    private String initErrorDescription()
    {
        if ( errorCode != null )
        {
            StringBuilder sb = new StringBuilder( 200 ).append( errorCode );
            if ( getOrganization() != null )
            {
                sb.append( " " ).append( getOrganization().getShortName() );
                String cpty = getTradingParty() != null ? getTradingParty().getShortName() : ( getTradingPartyOrganization() != null ? getTradingPartyOrganization().getShortName() : null );
                if ( cpty != null )
                {
                    sb.append( " -> " ).append( cpty );
                }
            }
            return sb.toString();
        }
        return null;
    }
}


