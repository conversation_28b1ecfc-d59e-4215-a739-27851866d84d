package com.integral.finance.creditLimit.admin;

// Copyright (c) 2001-2006 Integral Development Corp. All rights reserved.

import com.integral.log.Log;
import com.integral.log.LogFactory;


/**
 * This is the logger class for credit admin service.
 *
 * <AUTHOR> Development Corp.
 */

public class CreditAdminServiceLoggerC
{
    /*
     * Logger for this class.
     */
    protected Log log = LogFactory.getLog( CreditAdminServiceLoggerC.class );

    /**
     * Singleton instance
     */
    private static CreditAdminServiceLoggerC _creditAdminServiceLogger;

    /**
     * Static initialization.
     */
    static
    {
        _creditAdminServiceLogger = new CreditAdminServiceLoggerC();
    }

    /**
     * Private constructor.
     */
    private CreditAdminServiceLoggerC()
    {
    }

    public static CreditAdminServiceLoggerC getInstance()
    {
        return _creditAdminServiceLogger;
    }
}
