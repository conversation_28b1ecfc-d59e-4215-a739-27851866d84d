package com.integral.finance.creditLimit.notification;

public enum CreditMessageTypes {

  LIMIT_CURRENCY_LIMITS((byte) 0),
  CCYPAIR_BIDOFFER_LIMITS((byte) 1),
  CCY_POSITIONS((byte) 2),
  ACCOUNT_BALANCES((byte) 3);

  private final byte messageType;

  private CreditMessageTypes(byte msgType ) {
    this.messageType = msgType;
  }

  public static CreditMessageTypes getCreditMessageTypes (byte msgType ) {
    switch (msgType) {
      case 0:
        return LIMIT_CURRENCY_LIMITS;
      case 1:
        return CCYPAIR_BIDOFFER_LIMITS;
      case 2:
        return CCY_POSITIONS;
      case 3:
        return ACCOUNT_BALANCES;
      default:
        return null;
    }
  }

  public byte getMessageType() {
    return messageType;
  }
}
