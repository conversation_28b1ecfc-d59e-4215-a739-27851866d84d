package com.integral.finance.creditLimit.quickcheck;


public class CurrencyPairLevelCreditLineKey
{
	private int cpoIndex;
	private long tpObjectId;
	private int ccyPairIndex;
	private int valueDate;
	private boolean isCpoMaker;

	public CurrencyPairLevelCreditLineKey(){}
	
	public CurrencyPairLevelCreditLineKey(int cpoIndx, long tpObjId, int cpIndx, int date, boolean cpoMaker )
	{
		this();
		this.cpoIndex = cpoIndx;
		this.tpObjectId = tpObjId;
		this.ccyPairIndex = cpIndx;
		this.valueDate = date;
		this.isCpoMaker = cpoMaker;
	}
	
    public boolean isCpoMaker()
    {
        return isCpoMaker;
    }

    public void setCpoMaker( boolean isCpoMaker )
    {
        this.isCpoMaker = isCpoMaker;
    }

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;			
		result = prime * result + ccyPairIndex;
		result = prime * result + cpoIndex;
		result = prime * result + (isCpoMaker ? 1231 : 1237);
		result = prime * result + (int) (tpObjectId ^ (tpObjectId >>> 32));
		result = prime * result + valueDate;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CurrencyPairLevelCreditLineKey other = (CurrencyPairLevelCreditLineKey) obj;
		if (ccyPairIndex != other.ccyPairIndex)
			return false;
		if (cpoIndex != other.cpoIndex)
			return false;
		if (isCpoMaker != other.isCpoMaker)
			return false;
		if (tpObjectId != other.tpObjectId)
			return false;
		if (valueDate != other.valueDate)
			return false;
		return true;
	}    	
}
