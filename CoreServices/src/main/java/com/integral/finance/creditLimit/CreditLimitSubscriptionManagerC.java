package com.integral.finance.creditLimit;

// Copyright (c) 2001-2005 Integral Development Corp.  All rights reserved.

import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.configuration.*;
import com.integral.finance.creditLimit.notification.CreditLimitInfo;
import com.integral.finance.creditLimit.notification.CreditStatus;
import com.integral.finance.creditLimit.quickcheck.CreditLineCollection;
import com.integral.finance.creditLimit.quickcheck.CreditLineManagerC;
import com.integral.finance.creditLimit.quickcheck.CurrencyLevelCreditLine;
import com.integral.finance.creditLimit.server.CreditLimitSubscriptionInfo;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.FXLeg;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.fx.FXTrade;
import com.integral.finance.instrument.AmountOfInstrument;
import com.integral.finance.instrument.InstrumentFactory;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.TradeLeg;
import com.integral.is.ISCommonConstants;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageStatus;
import com.integral.netting.model.NettingPortfolio;
import com.integral.netting.model.NettingTradeRequest;
import com.integral.netting.model.TradeRequestLeg;
import com.integral.persistence.Entity;
import com.integral.util.IdcUtilC;
import com.integral.util.Tuple;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.workflow.dealing.DealingLimit;
import com.integral.workflow.dealing.fx.FXDealingLimit;
import com.integral.workflow.dealing.fx.FXWorkflowFactory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * This class is used to manage the credit limit subscriptions for credit limit updates.
 */
public class CreditLimitSubscriptionManagerC
{
    protected Log log = LogFactory.getLog( this.getClass() );
    private static final ConcurrentMap<String, Object> lockMap = new ConcurrentHashMap<String, Object>();
    protected static CreditConfigurationMBean configMBean = CreditConfigurationFactory.getCreditConfigurationMBean();
    protected static CreditLimitConfigurationMBean creditConfigMBean = CreditLimitConfigurationFactory.getCreditConfigurationMBean();

    /**
     * Singleton instance
     */
    private static final CreditLimitSubscriptionManagerC _creditLimitSubscriptionManager;

    /*
     * Static initialization.
     */
    static
    {
        _creditLimitSubscriptionManager = new CreditLimitSubscriptionManagerC();
    }

    /**
     * Private constructor.
     */
    private CreditLimitSubscriptionManagerC()
    {
    }

    private CreditUtilizationCache getCreditUtilizationCache()
    {
        return CreditUtilizationManagerC.getInstance().getCreditUtilizationCache();
    }

    /**
     * Returns the singleton instance of CreditLimitSubscriptionManagerC.
     *
     * @return credit limit subscription manager.
     */
    public static CreditLimitSubscriptionManagerC getInstance()
    {
        return _creditLimitSubscriptionManager;
    }

    /**
     * Subscribes the currency pair for the credit limit updates for the credit line provided by the credit provider organization
     * for the credit counterparty represented by the trading party or organization.
     *
     * @param creditProviderLe credit provider le
     * @param creditCpty       credit counterparty
     * @param creditCptyOrg    credit counterparty org
     * @param baseCcy          base currency
     * @param varCcy           variable currency
     */
    public void subscribe( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, Currency baseCcy, Currency varCcy )
    {
        getCreditUtilizationCache().addCreditLimitSubscription(creditProviderLe, creditCpty, creditCptyOrg, baseCcy, varCcy);
    }

    /**
     * Subscribes a list of currency pairs for the credit limit updates for the credit line provided by the credit provider organization
     * for the credit counterparty trading party or organization.
     *
     * @param creditProviderLe credit provider le
     * @param creditCpty       credit counterparty
     * @param creditCptyOrg    credit counterparty org
     * @param ccyPairColl      currency pair collection
     */
    public void subscribe( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, Collection<String> ccyPairColl )
    {
        getCreditUtilizationCache().addCreditLimitSubscriptions(creditProviderLe, creditCpty, creditCptyOrg, ccyPairColl);
    }

    /**
     * Unsubscribe the currency pair formed by base currency and variable currency for the credit limit updates for the credit line provided by
     * the credit provider organization for the credit counterparty trading party or organization.
     *
     * @param creditProviderLe credit provider le
     * @param creditCpty       credit counterparty
     * @param creditCptyOrg    credit counterparty org
     * @param baseCcy          base currency
     * @param varCcy           variable currency
     */
    public void unsubscribe( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, Currency baseCcy, Currency varCcy )
    {
        getCreditUtilizationCache().removeCreditLimitSubscription(creditProviderLe, creditCpty, creditCptyOrg, baseCcy, varCcy);
    }

    /**
     * Unsubscribe the list of currency pairs for credit limit updates for the credit line provided by the credit provider organization
     * for the credit counterparty trading party or organization. If currency pair collection is null, then all currency pair subscriptions are
     * removed.
     *
     * @param creditProviderLe credit provider le
     * @param creditCpty       credit counterparty
     * @param creditCptyOrg    credit counterparty org
     * @param ccyPairColl      currency pair collection
     */
    public void unsubscribe( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, Collection<String> ccyPairColl )
    {
        getCreditUtilizationCache().removeCreditLimitSubscriptions( creditProviderLe, creditCpty, creditCptyOrg, ccyPairColl );
    }

    public void subscribeBilateralCreditLimitUpdate( LegalEntity le1, LegalEntity le2, Currency baseCcy, Currency varCcy, boolean isLe1Maker, boolean isServerSideProvisioning, IdcDate spotValueDate )
    {
        Organization takerOrg = isLe1Maker ? le2.getOrganization () : le1.getOrganization ();
        boolean handleAllLegalEntities = CreditConfigurationFactory.getCreditConfigurationMBean ().isCreditWarmupAllLegalEntitiesOnSubscriptionEnabled ( takerOrg );
        subscribeBilateralCreditLimitUpdate( le1, le2, baseCcy, varCcy, isLe1Maker, isServerSideProvisioning, spotValueDate, handleAllLegalEntities );
    }

    /**
     * Subscribes the currency pair for the credit limit updates for the credit lines between the specified legal entities
     * Adds given subscription to cache for periodic recalulations
     *
     * @param le1                      legal entity1
     * @param le2                      legal entity2
     * @param baseCcy                  base currency
     * @param varCcy                   variable currency
     * @param isLe1Maker               boolean representing whether le1 is maker
     * @param isServerSideProvisioning boolean returning whether its server-side provisioning
     */
    public void subscribeBilateralCreditLimitUpdate( LegalEntity le1, LegalEntity le2, Currency baseCcy, Currency varCcy, boolean isLe1Maker, boolean isServerSideProvisioning, IdcDate spotValueDate, boolean handleAllLegalEntities )
    {
        try
        {
        	if(log.isDebugEnabled()){
        		StringBuilder sb = new StringBuilder(100);
        		sb.append("CLSM.subscribeBilateralCreditLimitUpdate : subscribing for bilateral credit ");
        		sb.append(" | le1=").append(le1.getFullyQualifiedName());
        		sb.append(" | le2=").append(le2.getFullyQualifiedName());
        		sb.append(" | baseCcy=").append(baseCcy.getName());
        		sb.append(" | varCcy=").append(varCcy.getName());        		
        		sb.append(" | isLe1Maker=").append(isLe1Maker);
        		sb.append(" | isServerSideProvisioning=").append(isServerSideProvisioning);
        		sb.append(" | spotValueDate=").append(spotValueDate);
        		log.debug(sb.toString());
        	}
            subscribeBilateralCreditLimitUpdate( le1, le2, baseCcy, varCcy, handleAllLegalEntities );
            //If server side provisioning set to true, add to creditDealingSubInfo for periodic revaluation
            if ( isServerSideProvisioning )
            {
                IdcDate spotDate = spotValueDate != null ? spotValueDate : CreditUtilC.getSpotDate( baseCcy, varCcy );
                if ( spotDate != null )
                {
                    // do quick check credit subscription.
                    boolean loadAllLELines = RuntimeFactory.getServerRuntimeMBean().isQuickCreditCheckLoadLinesForAllLEs(le1.getOrganization());
                    boolean venueSetup = CounterpartyUtilC.isVenueSetUp( le1 ) || CounterpartyUtilC.isVenueSetUp( le2 );
                    CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair(baseCcy, varCcy);
                    if( handleAllLegalEntities && loadAllLELines && !venueSetup)
                    {	                    
	                    Organization org1 = le1.getOrganization();
	                    Organization org2 = le2.getOrganization();
	                    long t1 = System.currentTimeMillis();
	                    loadBilaterCreditLimitsForAllLEs(org1, org2, spotDate, ccyPair, isLe1Maker, false);
	                    if(log.isDebugEnabled())
	                    {
	                    	long t2 = System.currentTimeMillis()-t1;
	                    	StringBuilder sb = new StringBuilder(100);
	                    	sb.append("CLSM.subscribeBilateralCreditLimitUpdate : time taken to load creditLimits between All LEs for ")
	                    	.append("Org1=").append(org1.getShortName())
	                    	.append(", Org2=").append(org2.getShortName())
	                    	.append(" : ").append(t2);
	                    	log.debug(sb.toString());
	                    }
                    }
                    else
                    {
                        CreditLineCollection creditLineCollection = CreditLineManagerC.getInstance().
                                getCreditLineCollectionAddIfNotExists(isLe1Maker ? le2 : le1, isLe1Maker ? le1 : le2, ccyPair, spotDate);
                        loadBilateralCreditLimitsForOCX(le1, le2, ccyPair, spotDate, isLe1Maker, false, creditLineCollection);
                    }
                }
            }

            // force fetching of all legal entities asynchronously.
            if ( !handleAllLegalEntities )
            {
                CreditUtilizationCacheRevaluationTaskC task = CreditUtilizationManagerC.getInstance ().getCreditUtilizationCacheRevaluationTask ();
                if ( task != null )
                {
                    Organization takerOrg = isLe1Maker ? le2.getOrganization () : le1.getOrganization ();
                    Organization makerOrg = isLe1Maker ? le1.getOrganization () : le2.getOrganization ();
                    task.forceFetch ( takerOrg, makerOrg );
                }
            }
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( 200 ).append( "CLSM.subscribeBilateralCreditLimitUpdate : le1=" )
                    .append( le1 ).append( ",le2=" ).append( le2 ).append( ",isLe1Maker=" ).append( isLe1Maker ).append( ",baseCcy=" ).append( baseCcy )
                    .append( ",varCcy=" ).append( varCcy ).append( ",isServerSideProvisioning=" ).append( isServerSideProvisioning ).toString(), e );
        }
    }

    private void loadBilaterCreditLimitsForAllLEs(Organization org1, Organization org2, IdcDate spotDate, CurrencyPair ccyPair,
    				boolean isLe1Maker, boolean clearCreditLinesForSubscription)
    {
    	if(log.isDebugEnabled()){
    		StringBuilder sb = new StringBuilder(100);
    		sb.append("CLSM.loadBilaterCreditLimitsForAllLEs : loading bilateral credit for all the LE combinations ");
    		sb.append(" | Org1=").append(org1.getShortName());
    		sb.append(" | Org2=").append(org2.getShortName());
    		sb.append(" | CcyPair=").append(ccyPair.getName());        		
    		sb.append(" | isLe1Maker=").append(isLe1Maker);
    		sb.append(" | spotValueDate=").append(spotDate);
    		log.debug(sb.toString());
    	}
    	Collection<LegalEntity> org1Les = org1.getLegalEntities();
        for ( LegalEntity org1Le : org1Les )
        {
            if ( !org1Le.isActive () )
            {
                continue;
            }
            Collection<LegalEntity> org2Les = org2.getLegalEntities();
            for ( LegalEntity org2Le : org2Les )
            {
                if ( !org2Le.isActive () )
                {
                    continue;
                }
                try
                {
                    if ( CounterpartyUtilC.isValidTradingParties( org1Le, org2Le ) )
                    {
                        CreditLineCollection creditLineCollection = CreditLineManagerC.getInstance().
                                getCreditLineCollectionAddIfNotExists(isLe1Maker ? org2Le : org1Le, isLe1Maker ? org1Le : org2Le, ccyPair, spotDate);
                    	loadBilateralCreditLimitsForOCX(org1Le, org2Le, ccyPair, spotDate, 
                    			isLe1Maker, clearCreditLinesForSubscription, creditLineCollection);
                    }
                }
                catch ( Exception e )
                {
                    log.warn( new StringBuilder( 200 ).append( "CLSM.loadBilaterCreditLimitsForAllLEs : org1Le=" )
                            .append( org1Le ).append( ",org2Le=" ).append( org2Le ).toString(), e );
                }
            }
        }
        loadBilaterCreditLimitsForAllSDLEs(org1, org2, spotDate, ccyPair, isLe1Maker, clearCreditLinesForSubscription);
        
        boolean customerOrgsFetchEnabled = CreditConfigurationFactory.getCreditConfigurationMBean().isCustomerOrgsCreditUtilizationFetchEnabled();
        if(customerOrgsFetchEnabled)
        {
        	loadBilateralCreditLimitsForAllCustomerOrgs(org1, org2, spotDate, ccyPair, isLe1Maker, clearCreditLinesForSubscription);
        }
    }
	
    
    private void loadBilaterCreditLimitsForAllSDLEs(Organization org1, Organization org2, IdcDate spotDate, CurrencyPair ccyPair,
			boolean isLe1Maker, boolean clearCreditLinesForSubscription)
    {
    	if(log.isDebugEnabled()){
    		StringBuilder sb = new StringBuilder(100);
    		sb.append("CLSM.loadBilaterCreditLimitsForAllSDLEs : loading bilateral credit for SalesDealer ");
    		sb.append(" | Org1=").append(org1.getShortName());
    		sb.append(" | Org2=").append(org2.getShortName());
    		sb.append(" | CcyPair=").append(ccyPair.getName());        		
    		sb.append(" | isLe1Maker=").append(isLe1Maker);
    		sb.append(" | spotValueDate=").append(spotDate);
    		log.debug(sb.toString());
    	}
    	
        Collection<TradingParty> custTps = CounterpartyUtilC.getCounterpartyLegalEntitiesForAssociatedSDG( org1 );
        if ( custTps != null && !custTps.isEmpty() )
        {
            for ( TradingParty tp : custTps )
            {
                LegalEntity custLe = tp.getLegalEntity();
                if ( custLe != null )
                {
                    Collection<LegalEntity> org2Les = org2.getLegalEntities();
                    for ( LegalEntity org2Le : org2Les )
                    {
                        try
                        {
                            if ( CounterpartyUtilC.isValidTradingParties( custLe, org2Le ) )
                            {
                                CreditLineCollection creditLineCollection = CreditLineManagerC.getInstance().
                                        getCreditLineCollectionAddIfNotExists(isLe1Maker ? org2Le : custLe, isLe1Maker ? custLe : org2Le, ccyPair, spotDate);
                            	loadBilateralCreditLimitsForOCX(custLe, org2Le, ccyPair, spotDate, 
                            			isLe1Maker, clearCreditLinesForSubscription, creditLineCollection);
                            }
                        }
                        catch ( Exception e )
                        {
                            log.warn( new StringBuilder( 200 ).append( "CLSM.loadBilaterCreditLimitsForAllSDLEs : org1Le=" )
                                    .append( custLe ).append( ",org2Le=" ).append( org2Le ).toString(), e );
                        }
                    }
                }
            }
        }	
    }
    
    private void loadBilateralCreditLimitsForAllCustomerOrgs(Organization org1, Organization org2, IdcDate spotDate, CurrencyPair ccyPair,
			boolean isLe1Maker, boolean clearCreditLinesForSubscription)
    {
    	if(log.isDebugEnabled()){
    		StringBuilder sb = new StringBuilder(100);
    		sb.append("CLSM.loadBilateralCreditLimitsForAllCustomerOrgs : loading bilateral credit for CustomerOrgs ");
    		sb.append(" | Org1=").append(org1.getShortName());
    		sb.append(" | Org2=").append(org2.getShortName());
    		sb.append(" | CcyPair=").append(ccyPair.getName());        		
    		sb.append(" | isLe1Maker=").append(isLe1Maker);
    		sb.append(" | spotValueDate=").append(spotDate);
    		log.debug(sb.toString());
    	}
        Collection<Organization> fiCustOrgs = org1.getRelatedOrganizations(ISCommonConstants.LP_ORG_RELATIONSHIP);
        if ( fiCustOrgs != null && !fiCustOrgs.isEmpty() )
        {
            for ( Organization custOrg : fiCustOrgs )
            {
                Collection<LegalEntity> custOrg1Les = custOrg.getLegalEntities();
                for ( LegalEntity custOrg1Le : custOrg1Les )
                {
                    Collection<LegalEntity> org2Les = org2.getLegalEntities();
                    for ( LegalEntity org2Le : org2Les )
                    {
                        try
                        {
                            if ( CounterpartyUtilC.isValidTradingParties( custOrg1Le, org2Le ) )
                            {
                                CreditLineCollection creditLineCollection = CreditLineManagerC.getInstance().
                                        getCreditLineCollectionAddIfNotExists(isLe1Maker ? org2Le : custOrg1Le, isLe1Maker ? custOrg1Le : org2Le, ccyPair, spotDate);
                            	loadBilateralCreditLimitsForOCX(custOrg1Le, org2Le, ccyPair, spotDate, 
                            			isLe1Maker, clearCreditLinesForSubscription, creditLineCollection);
                            }
                        }
                        catch ( Exception e )
                        {
                            log.warn( new StringBuilder( 200 ).append( "CLSM.addCreditUtilizationsForAllLEs : custOrg1Le=" )
                                    .append( custOrg1Le ).append( ",org2Le=" ).append( org2Le ).toString(), e );
                        }
                    }
                }
            }
        }
    }
    
	public void loadBilateralCreditLimitsForOCX(LegalEntity le1, LegalEntity le2, CurrencyPair ccyPair, IdcDate valueDate, 
			boolean isLe1Maker, boolean clearCreditLinesForSubscription, CreditLineCollection creditLineCollection)
	{	
    	if(log.isDebugEnabled()){
    		StringBuilder sb = new StringBuilder(100);
    		sb.append("CLSM.loadBilateralCreditLimitsForOCX : loading bilateral credit for QuickCheck for ");
    		sb.append(" | le1=").append(le1.getFullyQualifiedName());
    		sb.append(" | le2=").append(le2.getFullyQualifiedName());
    		sb.append(" | CcyPair=").append(ccyPair.getName());
    		sb.append(" | spotValueDate=").append(valueDate);
    		sb.append(" | clearCreditLinesForSubscription=").append(clearCreditLinesForSubscription);    		
    		log.debug(sb.toString());
    	}
		if(valueDate!=null){	
	    	List<CreditEntity> allCreditEntitiesBetween;
			if ( isLe1Maker )
			{
			    allCreditEntitiesBetween = CreditUtilC.getAllCreditEntitiesBetween( le2, le1 );
			}
			else
			{
			    allCreditEntitiesBetween = CreditUtilC.getAllCreditEntitiesBetween( le1, le2 );
			}
			if(allCreditEntitiesBetween!=null)
			{
				Map<String, FXPrice> rateMap = new HashMap<String, FXPrice>();
				CurrencyLevelCreditLine[] currencyLevelCreditLines = new CurrencyLevelCreditLine[allCreditEntitiesBetween.size()];
				int i=0;
				boolean brokenCreditLines = false;
			    for ( CreditEntity creditEntity : allCreditEntitiesBetween )
			    {
			        if( creditEntity.getTradingParty() == null ){
			            /*
			                TradingParty can be null in Firm Based Credit Setup if relationships are not setup.
			                In such a case Credit is not available.
			             */
                        brokenCreditLines = true;
                        continue;
                    }
			    	CurrencyLevelCreditLine currencyLevelCreditLine = getCreditLineFromCreditEntity(
                                                                            ccyPair, valueDate, rateMap, creditEntity.getLegalEntity(),
                                                                            creditEntity.getTradingParty(), creditEntity.isMaker());
			    	if(currencyLevelCreditLine != null)
			    	{
                        currencyLevelCreditLine.setOrphan( false );
			    		currencyLevelCreditLines[i++] = currencyLevelCreditLine;
			    	}
			    }
                creditLineCollection.setBrokenCreditLines(brokenCreditLines);
                try
			    {
			    	creditLineCollection.resetAllCreditLines(currencyLevelCreditLines);
			    	creditLineCollection.markDirty();
					creditLineCollection.reEvaluateSuspensionStatus();
					creditLineCollection.forceCalculateMinAvailableAmounts ();
			    }
			    catch(Exception e)
			    {
			    	log.error( "CLSM.loadBilateralCreditLimitsForOCX : Exception while reloading credit lines=" + creditLineCollection, e );	
			    }
			}
		}
	}

	public CurrencyLevelCreditLine getCreditLineFromCreditEntity(
			CurrencyPair ccyPair,IdcDate valueDate,Map<String, FXPrice> rateMap,
            LegalEntity cpl, TradingParty tp, boolean isMaker)
	{
        Currency baseCcy = ccyPair.getBaseCurrency();
        Currency varCcy = ccyPair.getVariableCurrency();
		Organization cpo = cpl.getOrganization();
		Organization cco = tp.getLegalEntityOrganization();
		// If creditLimitOrgFunction of CPO itself is null, then no point processing any lines.
		if(cpo.isActive() && cco.isActive() && cpo.getCreditLimitOrgFunction()!=null)
		{
		    DealingLimit dealingLimitAtEachLine = getAvailableBaseCurrencyCreditLimit( cpl, tp, cco, valueDate, baseCcy, varCcy, isMaker, rateMap, true, null );
		   
		    boolean isOrgLevelCredit = false;			            
		    Currency limitCcy;
		    boolean isCreditSuspended = false;
		    double bidLimit = Double.MAX_VALUE;
		    double offerLimit = Double.MAX_VALUE;
		    boolean isDummy = true;
		    boolean cashAccount = false;
		    
		    if(dealingLimitAtEachLine!=null)
		    {			            	
		        CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpl.getOrganization(), tp, true );
		        isOrgLevelCredit = (cclr == null) || (cclr.getTradingParty() == null);			            
		        limitCcy = CreditUtilC.getLimitCurrency(cclr, cpo);				            
		        isCreditSuspended = dealingLimitAtEachLine.isSuspended();
		        bidLimit = dealingLimitAtEachLine.getBidLimit();
		        offerLimit = dealingLimitAtEachLine.getOfferLimit();
		        isDummy = false;
		        CreditLimitRule clr = ( CreditLimitRule ) cclr.getChildRule ( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
		        cashAccount = clr != null && CreditLimitConstants.AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR.isSameAs ( clr.getCreditUtilizationCalculator () );
		    }
		    else
		    {
		    	limitCcy = CreditUtilC.getLimitCurrency(cpo);
		    }
		    
			if(log.isDebugEnabled()){
				StringBuilder sb = new StringBuilder(100);
				sb.append("CLSM.loadBilateralCreditLimitsForOCX : Creating/Updating ccyLevelCreditLine ");
				sb.append(" | cpo=").append(cpl.getOrganization());
				sb.append(" | tp=").append(tp);
				sb.append(" | ccyPair=").append(ccyPair.getName());
				sb.append(" | bidLimit=").append(bidLimit).append(" | offerLimit=").append(offerLimit);
				sb.append(" | isCrdtSuspnded=").append(isCreditSuspended);
				sb.append(" | limitCcy=").append(limitCcy.getName());
				sb.append(" | isOrgLevelCredit=").append(isOrgLevelCredit);
				sb.append(" | isDummy=").append(isDummy);
				log.debug(sb.toString());
			}
			
			return CreditLineManagerC.getInstance().
					updateCcyPairLevelCreditLineOrCreateIfNotExisting(cpl, tp, isMaker, ccyPair,valueDate, bidLimit, offerLimit,
                                                                    isCreditSuspended, limitCcy, isOrgLevelCredit, isDummy, cashAccount );
		}
		else
		{
            if(log.isDebugEnabled())
            {
                StringBuilder sb = new StringBuilder(150);
                sb.append("Skipped loading credit lines for CPO=").append(cpo.getShortName())
                        .append(" | CCO=").append(cco.getShortName())
                        .append(" either because org is inactive or doesn't have CreditLimitOrgFunc");
                log.debug(sb.toString());
            }
		}
		return null;
	}   
	
	public void refreshCreditLine(LegalEntity cple, TradingParty tp,
                                  CurrencyPair ccyPair, IdcDate spotDate, boolean isCpoMaker, Currency limitCcy, boolean orgLevel )
	{
        DealingLimit dealingLimitAtEachLine = getAvailableBaseCurrencyCreditLimit( cple, tp, tp.getLegalEntityOrganization (), spotDate,
        																		ccyPair.getBaseCurrency(), ccyPair.getVariableCurrency(),
        																		isCpoMaker, new HashMap<String, FXPrice>(), true, null );
        
        boolean isCreditSuspended = false;
        double bidLimit = Double.MAX_VALUE;
        double offerLimit = Double.MAX_VALUE;
        boolean isDummy = true;
        
        if(dealingLimitAtEachLine!=null){
            isCreditSuspended = dealingLimitAtEachLine.isSuspended();
            bidLimit = dealingLimitAtEachLine.getBidLimit();
            offerLimit = dealingLimitAtEachLine.getOfferLimit();
            isDummy = false;
        }

        boolean cashAccount = false;
        CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cple.getOrganization(), tp, true );
        if ( cclr != null )
        {
            CreditLimitRule clr = ( CreditLimitRule ) cclr.getChildRule ( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
            cashAccount = clr != null && CreditLimitConstants.AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR.isSameAs ( clr.getCreditUtilizationCalculator () );
        }

        CreditLineManagerC.getInstance().updateCcyPairLevelCreditLineOrCreateIfNotExisting(
                cple, tp, isCpoMaker, ccyPair, spotDate, bidLimit,
                offerLimit,isCreditSuspended, limitCcy, orgLevel, isDummy, cashAccount );
	}

    public void subscribeBilateralCreditLimitUpdate( LegalEntity le1, LegalEntity le2, Currency baseCcy, Currency varCcy )
    {
        subscribeBilateralCreditLimitUpdate( le1, le2, baseCcy, varCcy, true );
    }

    /**
     * Subscribes the currency pair for the credit limit updates for the credit lines between the specified legal entities.
     *
     * @param le1     legal entity1
     * @param le2     legal entity2
     * @param baseCcy base currency
     * @param varCcy  variable currency
     * @param handleAllLegalEntities handle all les
     */
    public void subscribeBilateralCreditLimitUpdate( LegalEntity le1, LegalEntity le2, Currency baseCcy, Currency varCcy, boolean handleAllLegalEntities )
    {
        try
        {
            Organization org1 = le1.getOrganization();
            Organization org2 = le2.getOrganization();
            if ( org1.isSameAs( org2 ) )
            {
                return;
            }

            // check first legal entity as credit provider.
            TradingParty org1TpForLe2 = le2.getTradingParty( org1 );
            if ( org1TpForLe2 != null )
            {
                getCreditUtilizationCache().addCreditLimitSubscription( le1, org1TpForLe2, org2, baseCcy, varCcy );
            }

            // now add the second legal entity as credit provider.
            TradingParty org2TpForLe1 = le1.getTradingParty( org2 );
            if ( org2TpForLe1 != null )
            {
                getCreditUtilizationCache().addCreditLimitSubscription( le2, org2TpForLe1, org1, baseCcy, varCcy );
            }
            if ( org1TpForLe2 == null || org2TpForLe1 == null )
            {
                if( CounterpartyUtilC.isVenueSetUp( le1 ) || CounterpartyUtilC.isVenueSetUp( le2 )){
                     //doNothing
                }
                else {
                    String[] param = new String[]{le1.getShortName(), le2.getShortName()};
                    throw new CreditLimitException( CreditLimit.ERROR_COUNTERPARTY_UNAVAILABLE, param );
                }
            }

            if ( CreditConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationPeriodicMemoryFetchEnabled() )
            {
                Set<CurrencyPair> ccyPairs = new HashSet<CurrencyPair>( 1 );
                CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair( baseCcy, varCcy );
                ccyPairs.add( ccyPair );
                IdcDate spotDate = CreditUtilC.getSpotDate( baseCcy, varCcy );
                Set<IdcDate> spotDates = new HashSet<IdcDate>();
                Collection<IdcDate> majorCcySpotDates = CreditUtilC.getDefaultSpotDates();
                if ( spotDate != null )
                {
                    spotDates.add( spotDate );
                }
                if ( majorCcySpotDates != null )
                {
                    spotDates.addAll( majorCcySpotDates );
                }
                if ( spotDates.isEmpty() )
                {
                    log.warn( new StringBuilder( 200 ).append( "CLSM.subscribeBilateralCreditLimitUpdate.No spot dates. le1=" ).append( le1 )
                            .append(",le2=").append( le2 ).append(",baseCcy=").append( baseCcy ).append( ",varCcy=" ).append( varCcy ).toString() );
                }
                else
                {
                    if ( handleAllLegalEntities )
                    {
                        addCreditUtilizationsForAllLEs ( org1, org2, spotDates, false, false, CreditLimitConstants.SUBSCRIBE_EVENT, ccyPairs );
                    }
                    else
                    {
                        addCreditUtilizationsForLEs ( le1, le2, spotDates, CreditLimitConstants.SUBSCRIBE_EVENT, ccyPairs );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( 200 ).append( "CLSM.subscribeBilateralCreditLimitUpdate : le1=" )
                    .append( le1 ).append(",le2=").append(le2).append( ",baseCcy=" ).append( baseCcy )
                    .append(",varCcy=").append( varCcy ).toString(), e );
        }
    }

    /**
     * Subscribes a list of currency pairs for the credit limit updates for the credit lines between the specified
     * legal entities.
     *
     * @param le1         legal entity 1
     * @param le2         legal entity 2
     * @param ccyPairColl currency pair collection
     */
    public void subscribeBilateralCreditLimitUpdate( LegalEntity le1, LegalEntity le2, Collection<String> ccyPairColl )
    {
        try
        {
            Organization org1 = le1.getOrganization();
            Organization org2 = le2.getOrganization();
            if ( org1.isSameAs( org2 ) )
            {
                return;
            }

            // check first legal entity as credit provider.
            TradingParty org1TpForLe2 = le2.getTradingParty( org1 );
            getCreditUtilizationCache().addCreditLimitSubscriptions( le1, org1TpForLe2, org2, ccyPairColl );

            // now add the second legal entity as credit provider.
            TradingParty org2TpForLe1 = le1.getTradingParty( org2 );
            getCreditUtilizationCache().addCreditLimitSubscriptions( le2, org2TpForLe1, org1, ccyPairColl );

            if ( CreditConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationPeriodicMemoryFetchEnabled() )
            {
                Collection<CurrencyPair> ccyPairList = new HashSet<CurrencyPair>();
                Set<IdcDate> spotDates = new HashSet<IdcDate>();
                if ( ccyPairColl != null && !ccyPairColl.isEmpty() )
                {
                    for ( String ccyPair : ccyPairColl )
                    {
                        CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(ccyPair);
                        ccyPairList.add(currencyPair);
                        IdcDate spotDate = CreditUtilC.getSpotDate( currencyPair.getBaseCurrency(), currencyPair.getVariableCurrency() );
                        if ( spotDate != null )
                        {
                            spotDates.add( spotDate );
                        }
                    }
                }
                if ( spotDates.isEmpty() )
                {
                    log.warn( new StringBuilder( 200 ).append( "CLSM.subscribeBilateralCreditLimitUpdate : No spot dates. le1=" ).append( le1 )
                            .append( ",le2=" ).append( le2 ).append( ",ccyPairColl=" ).append( ccyPairColl ).toString() );
                }
                else
                {
                    addCreditUtilizationsForAllLEs( org1, org2, spotDates, false, false, CreditLimitConstants.SUBSCRIBE_EVENT, ccyPairList );
                }
            }
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( 200 ).append( "CLSM.subscribeBilateralCreditLimitUpdate : le1=" )
                    .append( le1 ).append( ",le2=" ).append( le2 ).append(",ccyPairColl=").append(ccyPairColl).toString(), e );
        }
    }

    /**
     * Brings all the credit utilization to cache for for all possible le combinations between 2 organizations.
     * This also considers valid prime brokerage credit realtions between given le's.
     *
     * @param org1                organization
     * @param org2                organization
     * @param spotDates           set of spot dates for which CU's need to be added to cache
     * @param queryStale          query and update stale entries
     * @param queryAllFIRelations query all FI relationships
     * @param eventCode           event code
     * @param ccyPairs            currency pairs
     */
    public void addCreditUtilizationsForAllLEs( Organization org1, Organization org2, Set<IdcDate> spotDates, boolean queryStale, boolean queryAllFIRelations, int eventCode, Collection<CurrencyPair> ccyPairs )
    {
        Collection<LegalEntity> org1Les = org1.getLegalEntities();
        for ( LegalEntity org1Le : org1Les )
        {
            Collection<LegalEntity> org2Les = org2.getLegalEntities();
            for ( LegalEntity org2Le : org2Les )
            {
                try
                {
                    if ( CounterpartyUtilC.isValidTradingParties( org1Le, org2Le ) )
                    {
                        Collection<CreditEntity> creditRelations = getCreditRelationsBetween2Les( org1Le, org2Le );
                        if ( creditRelations != null && !creditRelations.isEmpty() )
                        {
                            for ( CreditEntity relation : creditRelations )
                            {
                                if( relation.getTradingParty() == null ){
                                    continue;
                                }
                                for ( IdcDate spotDate : spotDates )
                                {
                                    Organization creditProviderOrg = relation.getLegalEntity().getOrganization();
                                    TradingParty tp = CounterpartyUtilC.getTradingParty( relation.getTradingParty(), creditProviderOrg );
                                    CreditUtilizationCacheEntry cce = getCreditUtilizationCacheEntry( relation.getLegalEntity(), relation.getTradingParty().getLegalEntityOrganization(), tp, eventCode, spotDate );
                                    if ( cce != null && ccyPairs != null )
                                    {
                                        cce.initTenorCoefficient( ccyPairs );
                                    }
                                }
                            }
                        }
                    }
                }
                catch ( Exception e )
                {
                    log.warn( new StringBuilder( 200 ).append( "CLSM.addCreditUtilizationsForAllLEs : org1Le=" )
                            .append( org1Le ).append( ",org2Le=" ).append( org2Le ).toString(), e );
                }
            }
        }

        // do the sales dealer warm-up
        try
        {
            Collection<TradingParty> custTps = CounterpartyUtilC.getCounterpartyLegalEntitiesForAssociatedSDG( org1 );
            if ( custTps != null && !custTps.isEmpty() )
            {
                for ( TradingParty tp : custTps )
                {
                    LegalEntity custLe = tp.getLegalEntity();
                    if ( custLe != null )
                    {
                        Collection<LegalEntity> org2Les = org2.getLegalEntities();
                        for ( LegalEntity org2Le : org2Les )
                        {
                            try
                            {
                                if ( CounterpartyUtilC.isValidTradingParties( custLe, org2Le ) )
                                {
                                    Collection<CreditEntity> creditRelations = getCreditRelationsBetween2Les( custLe, org2Le );
                                    if ( creditRelations != null && !creditRelations.isEmpty() )
                                    {
                                        for ( CreditEntity relation : creditRelations )
                                        {
                                            if( relation.getTradingParty() == null){
                                                continue;
                                            }

                                            for ( IdcDate spotDate : spotDates )
                                            {
                                                Organization creditProviderOrg = relation.getLegalEntity().getOrganization();
                                                TradingParty tradingParty = CounterpartyUtilC.getTradingParty( relation.getTradingParty(), creditProviderOrg );
                                                CreditUtilizationCacheEntry cce = getCreditUtilizationCacheEntry( relation.getLegalEntity(), relation.getTradingParty().getLegalEntityOrganization(), tradingParty, eventCode, spotDate );
                                                if ( cce != null && ccyPairs != null )
                                                {
                                                    cce.initTenorCoefficient( ccyPairs );
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            catch ( Exception e )
                            {
                                log.warn( new StringBuilder( 200 ).append( "CLSM.addCreditUtilizationsForAllLEs : org1Le=" )
                                        .append( custLe ).append( ",org2Le=" ).append( org2Le ).toString(), e );
                            }
                        }
                    }
                }
            }

            // if query FI relationship needs to be checked
            if ( queryAllFIRelations )
            {
                Collection<Organization> fiCustOrgs = org1.getRelatedOrganizations(ISCommonConstants.LP_ORG_RELATIONSHIP);
                if ( fiCustOrgs != null && !fiCustOrgs.isEmpty() )
                {
                    for ( Organization custOrg : fiCustOrgs )
                    {
                        Collection<LegalEntity> custOrg1Les = custOrg.getLegalEntities();
                        for ( LegalEntity custOrg1Le : custOrg1Les )
                        {
                            Collection<LegalEntity> org2Les = org2.getLegalEntities();
                            for ( LegalEntity org2Le : org2Les )
                            {
                                try
                                {
                                    if ( CounterpartyUtilC.isValidTradingParties( custOrg1Le, org2Le ) )
                                    {
                                        Collection<CreditEntity> creditRelations = getCreditRelationsBetween2Les( custOrg1Le, org2Le );
                                        if ( creditRelations != null && !creditRelations.isEmpty() )
                                        {
                                            for ( CreditEntity relation : creditRelations )
                                            {
                                                if( relation.getTradingParty() == null ){
                                                    continue;
                                                }
                                                for ( IdcDate spotDate : spotDates )
                                                {
                                                    getCreditUtilizations( relation.getLegalEntity(), relation.getTradingParty().getLegalEntityOrganization(), relation.getTradingParty(), spotDate, eventCode, queryStale );
                                                }
                                            }
                                        }
                                    }
                                }
                                catch ( Exception e )
                                {
                                    log.warn( new StringBuilder( 200 ).append( "CLSM.addCreditUtilizationsForAllLEs : custOrg1Le=" )
                                            .append( custOrg1Le ).append( ",org2Le=" ).append( org2Le ).toString(), e );
                                }
                            }
                        }
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( 200 ).append("CLSM.addCreditUtilizationsForAllLEs : Exception while warming up sales dealer relations for org=")
                    .append(org1).toString(), e );
        }
    }

    public void addCreditUtilizationsForLEs( LegalEntity org1Le, LegalEntity org2Le, Set<IdcDate> spotDates, int eventCode, Collection<CurrencyPair> ccyPairs )
    {
        try
        {
            if ( CounterpartyUtilC.isValidTradingParties( org1Le, org2Le ) )
            {
                Collection<CreditEntity> creditRelations = getCreditRelationsBetween2Les( org1Le, org2Le );
                if ( creditRelations != null && !creditRelations.isEmpty() )
                {
                    for ( CreditEntity relation : creditRelations )
                    {
                        if( relation.getTradingParty() == null ){
                            continue;
                        }
                        for ( IdcDate spotDate : spotDates )
                        {
                            Organization creditProviderOrg = relation.getLegalEntity().getOrganization();
                            TradingParty tp = CounterpartyUtilC.getTradingParty( relation.getTradingParty(), creditProviderOrg );
                            CreditUtilizationCacheEntry cce = getCreditUtilizationCacheEntry( relation.getLegalEntity(), relation.getTradingParty().getLegalEntityOrganization(), tp, eventCode, spotDate );
                            if ( cce != null && ccyPairs != null )
                            {
                                cce.initTenorCoefficient( ccyPairs );
                            }
                        }
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( 200 ).append( "CLSM.addCreditUtilizationsForLEs : org1Le=" )
                    .append( org1Le ).append( ",org2Le=" ).append( org2Le ).toString(), e );
        }
    }

    /**
     * Unsubscribe the currency pair formed by base currency and variable currency for the credit limit updates for the credit lines
     * between specified .
     *
     * @param le1                      legal entity 1
     * @param le2                      legal entity 2
     * @param isLe1Maker               boolean representing whether le1 is maker
     * @param baseCcy                  base currency
     * @param varCcy                   variable currency
     * @param isServerSideProvisioning boolean representing whether it is server-sdie provisioning
     */
    public void unsubscribeBilateralCreditLimitUpdate( LegalEntity le1, LegalEntity le2, Currency baseCcy, Currency varCcy, boolean isLe1Maker, boolean isServerSideProvisioning )
    {
        try
        {
            unsubscribeBilateralCreditLimitUpdate(le1, le2, baseCcy, varCcy);
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( 200 ).append( "CLSM.unsubscribeBilateralCreditLimitUpdate : le1=" )
                    .append( le1 ).append(",le2=").append(le2).append( ",baseCcy=" ).append( baseCcy )
                    .append(",varCcy=").append( varCcy ).append(",isServerSideProvisioning=").append( isServerSideProvisioning ).toString(), e );
        }
    }

    /**
     * Unsubscribe the currency pair formed by base currency and variable currency for the credit limit updates for the credit lines
     * between specified .
     *
     * @param le1     legal entity 1
     * @param le2     legal entity 2
     * @param baseCcy base currency
     * @param varCcy  variable currency
     */
    public void unsubscribeBilateralCreditLimitUpdate( LegalEntity le1, LegalEntity le2, Currency baseCcy, Currency varCcy )
    {
        try
        {
            // check first legal entity as credit provider.
            Organization org1 = le1.getOrganization();
            Organization org2 = le2.getOrganization();
            if ( org1.isSameAs( org2 ) )
            {
                return;
            }
            TradingParty org1TpForLe2 = le2.getTradingParty( org1 );
            getCreditUtilizationCache().removeCreditLimitSubscription(le1, org1TpForLe2, org2, baseCcy, varCcy);

            // now add the second legal entity as credit provider.
            TradingParty org2TpForLe1 = le1.getTradingParty( org2 );
            getCreditUtilizationCache().removeCreditLimitSubscription(le2, org2TpForLe1, org1, baseCcy, varCcy);
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( 200 ).append( "CLSM.unsubscribeBilateralCreditLimitUpdate : le1=" )
                    .append( le1 ).append(",le2=").append(le2).append( ",baseCcy=" ).append( baseCcy )
                    .append(",varCcy=").append( varCcy ).toString(), e );
        }
    }

    /**
     * Unsubscribe the list of currency pairs for credit limit updates for the credit lines between specified legal entities.
     *
     * @param le1         legal entity 1
     * @param le2         legal entity2
     * @param ccyPairColl currency pair collection
     */
    public void unsubscribeBilateralCreditLimitUpdate( LegalEntity le1, LegalEntity le2, Collection<String> ccyPairColl )
    {
        try
        {
            // check first legal entity as credit provider.
            Organization org1 = le1.getOrganization();
            Organization org2 = le2.getOrganization();
            if ( org1.isSameAs( org2 ) )
            {
                return;
            }
            TradingParty org1TpForLe2 = le2.getTradingParty( org1 );
            getCreditUtilizationCache().removeCreditLimitSubscriptions( le1, org1TpForLe2, org2, ccyPairColl );

            // now add the second legal entity as credit provider.
            TradingParty org2TpForLe1 = le1.getTradingParty( org2 );
            getCreditUtilizationCache().removeCreditLimitSubscriptions( le2, org2TpForLe1, org1, ccyPairColl );
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( 200 ).append( "CLSM.unsubscribeBilateralCreditLimitUpdate : le1=" )
                    .append( le1 ).append( ",le2=" ).append( le2 ).append( ",ccyPairColl=" ).append( ccyPairColl ).toString(), e );
        }
    }

    /**
     * Returns the available credit limit by finding lowest credit limit available in all credit types. This is a limit amount in
     * credit limit currency and represents unilateral credit by the credit provider legal enity for the credit cpty. A null value
     * indicates that credit is not enabled for this credit provider and counterparty combination. This limit represents an limit
     * amount which can be used for any currency pairs and currency pair can have greater available credit limit based on the
     * netting methodology and current currency positions.
     *
     * @param creditProviderLe credit provider legal entity
     * @param creditCpty       credit counterparty
     * @param creditCptyOrg    credit counterparty org
     * @param settlementDate   settlement date
     * @return limit amount
     */
    public Double getAvailableCreditLimit( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, IdcDate settlementDate )
    {
        if ( getCreditUtilizationCache().isCreditEnabled( creditProviderLe.getOrganization(), creditCpty ) )
        {
            Collection<CreditUtilization> col = getCreditUtilizations( creditProviderLe, creditCpty.getLegalEntityOrganization(), creditCpty, settlementDate, CreditLimitConstants.RATE_QUALIFICATION_EVENT );
            double availableLimit = 0.0;
            boolean isInitial = true;
            for ( CreditUtilization cu : col )
            {
                if ( cu.getCreditLimitRule().getCreditUtilizationCalculator() == null || !cu.getCreditLimitRule().isEnabled() )
                {
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "CLSM.getAvailableCreditLimit : disabled credit methodology. clr=" + cu.getCreditLimitRule() );
                    }
                    continue;
                }
                double cuRealtimeAvailable = cu.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeAvailableAmount( cu );
                if ( isInitial )
                {
                    availableLimit = cuRealtimeAvailable;
                    isInitial = false;
                }
                else if ( cuRealtimeAvailable < availableLimit )
                {
                    availableLimit = cuRealtimeAvailable;
                }
            }
            return availableLimit;
        }
        return null;
    }

    /**
     * Returns the available credit limit by finding lowest credit limit available in all credit types. This is a limit amount in
     * credit limit currency and represents bilateral credit between these legal entities. A null value
     * indicates that credit is not enabled for these legal entities. This limit represents a limit
     * amount which can be used for any currency pairs and currency pair can have greater available credit limit based on the
     * netting methodology and current currency positions. This limit amount is in the limit currency of the first specified
     * legal entity. This is because each credit provider organization can have a limit currency which can be different from the
     * other credit provider organization.
     *
     * @param le1  legal entity 1
     * @param le2  legal entity 2
     * @param date date
     * @return limit amount
     */
    public Double getAvailableBilateralCreditLimit( LegalEntity le1, LegalEntity le2, IdcDate date )
    {
        AmountOfInstrument availableLimitAoi = getAvailableBilateralCreditLimitAmountOfInstrument( le1, le2, date );
        return availableLimitAoi != null ? availableLimitAoi.getAmount() : null;
    }

    /**
     * Returns the available credit limit by finding lowest credit limit available in all credit types. This is a limit amount in
     * credit limit currency and represents unilateral credit by the credit provider legal enity for the credit cpty. A null value
     * indicates that credit is not enabled for this credit provider and counterparty combination. This limit represents a limit
     * amount which can be used for any currency pairs and currency pair can have greater available credit limit based on the
     * netting methodology and current currency positions.
     *
     * @param creditProviderLe credit provider legal entity
     * @param creditCpty       credit counterparty
     * @param creditCptyOrg    credit counterparty org
     * @param settlementDate   settlement date
     * @return limit amount and currency as an amount of instrument
     */
    public AmountOfInstrument getAvailableLimitAmountOfInstrument( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, IdcDate settlementDate )
    {
        if ( getCreditUtilizationCache().isCreditEnabled( creditProviderLe.getOrganization(), creditCpty ) )
        {
            Collection<CreditUtilization> col = getCreditUtilizations( creditProviderLe, creditCptyOrg, creditCpty, settlementDate, CreditLimitConstants.RATE_QUALIFICATION_EVENT );
            double availableLimit = 0.0;
            Currency limitCcy = null;
            boolean isInitial = true;
            for ( CreditUtilization cu : col )
            {
                if ( cu.getCreditLimitRule().getCreditUtilizationCalculator() == null || !cu.getCreditLimitRule().isEnabled() )
                {
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "CLSM.getAvailableLimitAmountOfInstrument : disabled credit methodology. clr=" + cu.getCreditLimitRule() );
                    }
                    continue;
                }
                double cuRealtimeAvailable = cu.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeAvailableAmount( cu );
                if ( isInitial )
                {
                    availableLimit = cuRealtimeAvailable;
                    limitCcy = cu.getCurrency();
                    isInitial = false;
                }
                else if ( cuRealtimeAvailable < availableLimit )
                {
                    availableLimit = cuRealtimeAvailable;
                }
            }
            return limitCcy != null ? InstrumentFactory.newAmountOfInstrument( availableLimit, limitCcy ) : null;
        }
        return null;
    }

    public AmountOfInstrument getAvailableBilateralCreditLimitAmountOfInstrument( LegalEntity le1, LegalEntity le2, IdcDate date )
    {
        AmountOfInstrument availableLimitAoi;
        Organization org1 = le1.getOrganization();
        Organization org2 = le2.getOrganization();
        if ( org1.isSameAs( org2 ) )
        {
            return null;
        }

        TradingParty org1TpForLe2 = le2.getTradingParty( org1 );
        TradingParty org2TpForLe1 = le1.getTradingParty( org2 );
        if ( org1TpForLe2 == null || org2TpForLe1 == null )
        {
            return null;
        }

        boolean isLe1Maker = true;
        Organization maskedOrg = CreditUtilC.getMaskedOrganization( le1.getOrganization() );
        if ( maskedOrg == null )
        {
            maskedOrg = CreditUtilC.getMaskedOrganization( le2.getOrganization() );
            isLe1Maker = false;
        }
        TradingParty org1PbTp = CreditUtilC.getCreditPrimeBrokerTradingparty( org1TpForLe2 );
        TradingParty org2PbTp = CreditUtilC.getCreditPrimeBrokerTradingparty( org2TpForLe1 );

        if ( maskedOrg != null )
        {
            if ( isLe1Maker )
            {
                le1 = CreditUtilC.getDefaultlegalEntity( maskedOrg );
            }
            else
            {
                le2 = CreditUtilC.getDefaultlegalEntity( maskedOrg );
            }
            return getAvailableMaskedBilateralCreditLimitAmountOfInstrument( le1, le2, org1PbTp, org2PbTp, date );
        }
        boolean isLe1PrimeBrokerCreditEnabled = org1PbTp != null;
        boolean isLe2PrimeBrokerCreditEnabled = org2PbTp != null;
        boolean isBothPrimeBrokerCreditEnabled = isLe1PrimeBrokerCreditEnabled && isLe2PrimeBrokerCreditEnabled;
        if ( isLe1PrimeBrokerCreditEnabled || isLe2PrimeBrokerCreditEnabled )
        {
            AmountOfInstrument le1Limit;
            AmountOfInstrument le2Limit;
            AmountOfInstrument pbLimit = null;
            if ( isBothPrimeBrokerCreditEnabled )
            {
                le1Limit = getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities( le1, org1PbTp.getLegalEntity(), date );
                le2Limit = getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities( le2, org2PbTp.getLegalEntity(), date );
                availableLimitAoi = getMinimumAvailableAmountOfInstrument( le1Limit, le2Limit, org1 );
                if ( !org1PbTp.getLegalEntityOrganization().isSameAs( org2PbTp.getLegalEntityOrganization() ) )
                {
                    pbLimit = getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities( org1PbTp.getLegalEntity(), org2PbTp.getLegalEntity(), date );
                    availableLimitAoi = getMinimumAvailableAmountOfInstrument( availableLimitAoi, pbLimit, org1 );
                }
            }
            else if ( isLe1PrimeBrokerCreditEnabled )
            {
                le1Limit = getAvailableLimitAmountOfInstrument( le1, org1PbTp, org1PbTp.getLegalEntityOrganization(), date );
                TradingParty pb1TpForLe2 = le2.getTradingParty( org1PbTp.getLegalEntityOrganization() );
                if ( pb1TpForLe2 != null )
                {
                    pbLimit = getAvailableLimitAmountOfInstrument( org1PbTp.getLegalEntity(), pb1TpForLe2, org2, date );
                }
                else if ( log.isDebugEnabled() )
                {
                    log.debug( new StringBuilder( 200 ).append( "CLSM.getAvailableBilateralCreditLimitAmountOfInstrument : No trading party found for le2=" )
                            .append( le2 ).append( " in org=" ).append( org1PbTp.getLegalEntityOrganization() ).toString() );
                }
                availableLimitAoi = getMinimumAvailableAmountOfInstrument( le1Limit, pbLimit, org1 );
                le2Limit = getAvailableLimitAmountOfInstrument( le2, org2TpForLe1, org1, date );
                availableLimitAoi = getMinimumAvailableAmountOfInstrument( availableLimitAoi, le2Limit, org1 );
            }
            else
            {
                le1Limit = getAvailableLimitAmountOfInstrument( le2, org2PbTp, org2PbTp.getLegalEntityOrganization(), date );
                TradingParty pb2TpForLe1 = le1.getTradingParty( org2PbTp.getLegalEntityOrganization() );
                if ( pb2TpForLe1 != null )
                {
                    pbLimit = getAvailableLimitAmountOfInstrument( org2PbTp.getLegalEntity(), pb2TpForLe1, org1, date );
                }
                else if ( log.isDebugEnabled() )
                {
                    log.debug( new StringBuilder( 200 ).append( "CLSM.getAvailableBilateralCreditLimitAmountOfInstrument : No trading party found for le1=" )
                            .append( le1 ).append( " in org=" ).append( org2PbTp.getLegalEntityOrganization() ).toString() );
                }
                availableLimitAoi = getMinimumAvailableAmountOfInstrument( le1Limit, pbLimit, org1 );
                le2Limit = getAvailableLimitAmountOfInstrument( le1, org1TpForLe2, org2, date );
                availableLimitAoi = getMinimumAvailableAmountOfInstrument( availableLimitAoi, le2Limit, org1 );
            }
        }
        else
        {
            availableLimitAoi = getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities( le1, le2, date );
        }
        return availableLimitAoi;
    }

    public AmountOfInstrument getAvailableMaskedBilateralCreditLimitAmountOfInstrument( LegalEntity le1, LegalEntity le2, TradingParty org1PbTp, TradingParty org2PbTp, IdcDate date )
    {
        AmountOfInstrument availableLimitAoi = null;
        boolean isLe1PrimeBrokerCreditEnabled = org1PbTp != null;
        boolean isLe2PrimeBrokerCreditEnabled = org2PbTp != null;
        boolean isBothPrimeBrokerCreditEnabled = isLe1PrimeBrokerCreditEnabled && isLe2PrimeBrokerCreditEnabled;
        if ( isLe1PrimeBrokerCreditEnabled || isLe2PrimeBrokerCreditEnabled )
        {
            AmountOfInstrument le1Limit;
            AmountOfInstrument le2Limit;
            AmountOfInstrument pbLimit;
            if ( isBothPrimeBrokerCreditEnabled )
            {
                le1Limit = getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities( le1, org1PbTp.getLegalEntity(), date );
                le2Limit = getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities( le2, org2PbTp.getLegalEntity(), date );
                availableLimitAoi = getMinimumAvailableAmountOfInstrument( le1Limit, le2Limit, le1.getOrganization() );
                if ( !org1PbTp.getLegalEntityOrganization().isSameAs( org2PbTp.getLegalEntityOrganization() ) )
                {
                    pbLimit = getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities( org1PbTp.getLegalEntity(), org2PbTp.getLegalEntity(), date );
                    availableLimitAoi = getMinimumAvailableAmountOfInstrument( availableLimitAoi, pbLimit, le1.getOrganization() );
                }
            }
            else if ( isLe1PrimeBrokerCreditEnabled )
            {
                le1Limit = getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities( le1, org1PbTp.getLegalEntity(), date );
                le2Limit = getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities( le2, org1PbTp.getLegalEntity(), date );
                availableLimitAoi = getMinimumAvailableAmountOfInstrument( le1Limit, le2Limit, le1.getOrganization() );
            }
            else
            {
                le1Limit = getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities( le1, org2PbTp.getLegalEntity(), date );
                le2Limit = getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities( le2, org2PbTp.getLegalEntity(), date );
                availableLimitAoi = getMinimumAvailableAmountOfInstrument( le1Limit, le2Limit, le1.getOrganization() );
            }
        }
        return availableLimitAoi;
    }

    /**
     * Returns true if at least on one side credit enabled between the specified legal entities.
     *
     * @param le1 legal entity 1
     * @param le2 legal entity 2
     * @return enabled
     */
    private boolean isCreditEnabledBetweenLegalEntities( LegalEntity le1, LegalEntity le2 )
    {
        boolean enabled = false;
        try
        {
            // check first legal entity as credit provider.
            Organization org1 = le1.getOrganization();
            Organization org2 = le2.getOrganization();
            if ( org1.isSameAs( org2 ) )
            {
                return false;
            }
            TradingParty org1TpForLe2 = le2.getTradingParty( org1 );
            if ( org1TpForLe2 != null )
            {
                enabled = getCreditUtilizationCache().isCreditEnabled(org1, org1TpForLe2);
                if ( log.isDebugEnabled() )
                {
                    log.debug( new StringBuilder( 200 ).append("CLSM.isCreditEnabledBetweenLegalEntities : enabled=")
                            .append(enabled).append( " for creditProviderLe=" ).append( le1 ).append(",creditCpty=").append( org1TpForLe2 ).toString() );
                }
            }
            else if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLSM.isCreditEnabledBetweenLegalEntities : No trading party for le2=" )
                        .append( le2 ).append( " in org1=" ).append( org1 ).toString() );
            }

            // check the second legal enity as credit provider.
            if ( !enabled )
            {
                TradingParty org2TpForLe1 = le1.getTradingParty( org2 );
                if ( org2TpForLe1 != null )
                {
                    enabled = getCreditUtilizationCache().isCreditEnabled( org2, org2TpForLe1 );
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( new StringBuilder( 200 ).append( "CLSM.isCreditEnabledBetweenLegalEntities : enabled=" )
                                .append( enabled ).append( " for creditProviderLe=" ).append( le2 ).append( ",creditCpty=" ).append( org2TpForLe1 ).toString() );
                    }
                }
                else if ( log.isDebugEnabled() )
                {
                    log.debug( new StringBuilder( 200 ).append( "CLSM.isCreditEnabledBetweenLegalEntities : No trading party for le2=" )
                            .append( le2 ).append( " in org1=" ).append( org1 ).toString() );
                }
            }

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append("CLSM.isCreditEnabledBetweenLegalEntities : enabled=")
                        .append(enabled).append( ",le1=" ).append( le1 ).append( ",le2=" ).append( le2 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( 200 ).append("CLSM.isCreditEnabledBetweenLegalEntities : le1=")
                    .append(le1).append( ",le2=" ).append( le2 ).toString(), e );
        }
        return enabled;
    }


    /**
     * Returns the available credit limit by finding lowest credit limit available in all credit types. This is a limit amount in
     * credit limit currency and represents bilateral credit between the specified legal entities. A null value
     * indicates that credit is not enabled between these legal entities. This limit represents a limit
     * amount which can be used for any currency pairs and currency pair can have greater available credit limit based on the
     * netting methodology and current currency positions. The amount of instrument would be having the amount specified in the
     * limit currency of the first specified legal entity.
     *
     * @param le1  legal entity 1
     * @param le2  legal entity 2
     * @param date date
     * @return limit amount and currency as an amount of instrument
     */
    private AmountOfInstrument getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities( LegalEntity le1, LegalEntity le2, IdcDate date )
    {
        AmountOfInstrument availableAoi = null;
        try
        {
            // check first legal entity as credit provider.
            Organization org1 = le1.getOrganization();
            Organization org2 = le2.getOrganization();
            if ( org1.isSameAs( org2 ) )
            {
                return null;
            }
            TradingParty org1TpForLe2 = le2.getTradingParty( org1 );
            if ( org1TpForLe2 != null )
            {
                availableAoi = getAvailableLimitAmountOfInstrument( le1, org1TpForLe2, org2, date );
                if ( log.isDebugEnabled() )
                {
                    log.debug( new StringBuilder( 200 ).append( "CLSM.getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities : availableAoi=" )
                            .append( availableAoi ).append( " for creditProviderLe=" ).append( le1 ).append( ",creditCpty=" ).append( org1TpForLe2 )
                            .append( ",date=" ).append( date ).toString() );
                }
            }
            else if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLSM.getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities : No trading party for le2=" )
                        .append( le2 ).append( " in org1=" ).append( org1 ).toString() );
            }

            // check the second legal enity as credit provider.
            TradingParty org2TpForLe1 = le1.getTradingParty( org2 );
            if ( org2TpForLe1 != null )
            {
                AmountOfInstrument availableAoi2 = getAvailableLimitAmountOfInstrument( le2, org2TpForLe1, org2, date );
                if ( log.isDebugEnabled() )
                {
                    log.debug( new StringBuilder( 200 ).append( "CLSM.getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities : availableAoi=" )
                            .append( availableAoi2 ).append( " for creditProviderLe=" ).append( le2 ).append( ",creditCpty=" ).append( org2TpForLe1 )
                            .append( ",date=" ).append( date ).toString() );
                }
                availableAoi = getMinimumAvailableAmountOfInstrument( availableAoi, availableAoi2, org1 );
            }
            else if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLSM.getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities : No trading party for le2=" )
                        .append( le2 ).append( " in org1=" ).append( org1 ).toString() );
            }

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLSM.getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities : bilateral availableAoi=" )
                        .append( availableAoi ).append( ",le1=" ).append( le1 ).append( ",le2=" ).append( le2 ).append( ",date=" ).append( date ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( 200 ).append( "CLSM.getAvailableCreditLimitAmountOfInstrumentBetweenLegalEntities : le1=" )
                    .append( le1 ).append( ",le2=" ).append( le2 ).append( ",date=" ).append( date ).toString(), e );
        }
        return availableAoi;
    }

    public DealingLimit getAvailableBaseCurrencyCreditLimit( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, IdcDate settlementDate, Currency baseCcy, Currency varCcy, boolean isCreditProviderLeMaker, Map rateMap )
    {
        return getAvailableBaseCurrencyCreditLimit( creditProviderLe, creditCpty, creditCptyOrg, settlementDate, baseCcy, varCcy, isCreditProviderLeMaker, rateMap, false, null );
    }


    /**
     * Returns the available credit limit in the base currency for the currency pair formed by base currency and variable currency.
     * This is calculated by finding lowest credit limit available in all credit types for the currency pair. This is a limit amount in
     * base currency and represents unilateral credit by the credit provider legal enity for the credit cpty. A null value
     * indicates that credit is not enabled for this credit provider and counterparty combination. This limit represents an limit
     * amount which can be used for the currency pair and currency pair can have greater available credit limit based on the
     * netting methodology and current currency positions.
     *
     * @param creditProviderLe        credit provider legal entity
     * @param creditCpty              credit counterparty
     * @param creditCptyOrg           credit counterparty org
     * @param settlementDate          settlement date
     * @param baseCcy                 base currency
     * @param varCcy                  variable currency
     * @param isCreditProviderLeMaker is credit provider legal entity maker
     * @param rateMap                 rate map
     * @param applyTenorCoefficient   apply tenor coefficient
     * @return limit amount in base currency
     */
    
	private DealingLimit getAvailableBaseCurrencyCreditLimit(
			LegalEntity creditProviderLe, TradingParty creditCpty,
			Organization creditCptyOrg, IdcDate settlementDate,
			Currency baseCcy, Currency varCcy, boolean isCreditProviderLeMaker,
			Map rateMap, boolean applyTenorCoefficient, CreditWorkflowRiders riders )
    {
        DealingLimit dealingLimit = null;
        try
        {
            CreditUtilizationCache creditUtilizationCache = getCreditUtilizationCache();
        	int creditStatus = creditUtilizationCache.getCreditStatus(creditProviderLe.getOrganization(), creditCpty);
        	boolean bypassTenorRestrictions = false;
        	boolean allowExcess = false;
        	if ( riders != null && creditProviderLe.getOrganization ().isSameAs ( riders.getMakerOrg () ) )
            {
                bypassTenorRestrictions = riders.isBypassTenorRestrictionsMakerOnly ();
                allowExcess = riders.isExcessUtilizationAllowedMakerOnly ();
            }

            if (creditUtilizationCache.isCreditEnabled(creditStatus))
            {
                if( creditCpty == null ){
                    // zero limits are set if credit cpty is null. This can happen when Firm based credit is enabled and trading party does not exist.
                    FXDealingLimit fxDl = FXWorkflowFactory.newFXDealingLimit();
                    fxDl.setBaseCurrency( baseCcy );
                    fxDl.setVariableCurrency( varCcy );
                    fxDl.setBidLimits( new double[]{0} );
                    fxDl.setOfferLimits( new double[]{0} );
                    fxDl.setErrorCode( CreditLimit.ERROR_INVALID_TRADING_PARTY );
                    return fxDl;
                }

                CreditUtilizationCacheEntry cce = getCreditUtilizationCacheEntry( creditProviderLe, creditCptyOrg, creditCpty, CreditLimitConstants.RATE_QUALIFICATION_EVENT, settlementDate );
                if ( cce == null || cce.getCreditUtilizations() == null )
                {
                    if ( log.isDebugEnabled () )
                    {
                        log.debug ( "CLSM.getAvailableBaseCurrencyCreditLimit : cache entry is null or credit utils is null. cce=" + cce + ",cpl=" + creditProviderLe + ",cc=" + creditCpty );
                    }
                    return null;
                }

                CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule() != null ? cce.getCounterpartyCreditLimitRule() : null;
                if ( cclr == null )
                {
                    return null;
                }

                CreditTenorRestriction maxTenor =  creditUtilizationCache.getMaximumTenor(creditProviderLe.getOrganization(),creditCpty); //CreditUtilC.getMaximumTenor( cclr );
                CreditTenorRestriction minTenor =   creditUtilizationCache.getMinimumTenor(creditProviderLe.getOrganization(),creditCpty);//CreditUtilC.getMinimumTenor( cclr );

                if ( maxTenor != null && !bypassTenorRestrictions )
                {
                    IdcDate maxTenorDate = CreditUtilC.getValueDateForTenor( baseCcy, varCcy.getRealCurrency (), maxTenor );
                    if ( !settlementDate.isEarlierThanOrEqualTo( maxTenorDate ) )
                    {
                        // zero limits are set if settle date exceeds the maxTenorDate
                        FXDealingLimit fxDl = FXWorkflowFactory.newFXDealingLimit();
                        fxDl.setBaseCurrency( baseCcy );
                        fxDl.setVariableCurrency( varCcy );
                        fxDl.setBidLimits( new double[]{0} );
                        fxDl.setOfferLimits( new double[]{0} );
                        fxDl.setErrorCode( CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR );
                        return fxDl;
                    }
                }

                if ( minTenor != null && !bypassTenorRestrictions )
                {
                    IdcDate minTenorDate = CreditUtilC.getValueDateForTenor( baseCcy, varCcy.getRealCurrency (), minTenor );
                    if ( !settlementDate.isLaterThanOrEqualTo( minTenorDate ) )
                    {
                        // zero limits are set if settle date exceeds the minTenorDate
                        FXDealingLimit fxDl = FXWorkflowFactory.newFXDealingLimit();
                        fxDl.setBaseCurrency( baseCcy );
                        fxDl.setVariableCurrency( varCcy );
                        fxDl.setBidLimits( new double[]{0} );
                        fxDl.setOfferLimits( new double[]{0} );
                        fxDl.setErrorCode( CreditLimit.ERROR_EXCEED_MINIMUM_TENOR );
                        return fxDl;
                    }
                }

                if ( cce.isCurrencyPairExempted( CurrencyFactory.getCurrencyPair( baseCcy, varCcy ) ) )
                {
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "CLSM.getAvailableBaseCurrencyCreditLimit : exempted currency pair=" + baseCcy + "/" + varCcy + ",cpl=" + creditProviderLe );
                    }
                    return null;
                }

                if ( allowExcess )
                {
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "CLSM.getAvailableBaseCurrencyCreditLimit : allowed excess credit usage=" + baseCcy + "/" + varCcy + ",cpl=" + creditProviderLe );
                    }
                    return null;
                }

                if ( !cce.validateCreditUtilizations () )
                {
                    // zero limits are set if daily credit utilization does not exist.
                    FXDealingLimit fxDl = FXWorkflowFactory.newFXDealingLimit();
                    fxDl.setBaseCurrency( baseCcy );
                    fxDl.setVariableCurrency( varCcy );
                    fxDl.setBidLimits( new double[]{0} );
                    fxDl.setOfferLimits( new double[]{0} );
                    fxDl.setErrorCode( CreditLimit.ERROR_CREDIT_UTILIZATION_UNAVAILABLE );
                    return fxDl;
                }
                Collection<CreditUtilization> col = cce.getCreditUtilizations();
                if ( col != null && !col.isEmpty() )
                {
                    for ( CreditUtilization cu : col )
                    {
                        if ( cu.getCreditLimitRule().getCreditUtilizationCalculator() == null || !cu.getCreditLimitRule().isEnabled() )
                        {
                            if ( log.isDebugEnabled() )
                            {
                                log.debug( "CLSM.getAvailableBaseCurrencyCreditLimit : disabled credit methodology. clr=" + cu.getCreditLimitRule() );
                            }
                            continue;
                        }

                        if ( dealingLimit == null )
                        {
                            dealingLimit = cu.getCreditLimitRule().getCreditUtilizationCalculator().getDealingLimit( cu, baseCcy, varCcy.getRealCurrency (), settlementDate, rateMap, isCreditProviderLeMaker );
                            if ( applyTenorCoefficient && dealingLimit != null )
                            {
                                double tc = CreditUtilC.getTenorCoefficient( cclr, cu.getCreditLimitRule(), settlementDate, baseCcy, varCcy.getRealCurrency () );
                                if ( bypassTenorRestrictions && CreditLimit.TENOR_COEFFICIENT_NA == tc )
                                {
                                    tc = CreditLimit.DEFAULT_TENOR_COEFFICIENT;
                                }
                                dealingLimit = applyTenorCoefficient( dealingLimit, tc );
                            }
                        }
                        else
                        {
                            double tc = CreditLimit.DEFAULT_TENOR_COEFFICIENT;
                            DealingLimit cuDl = cu.getCreditLimitRule().getCreditUtilizationCalculator().getDealingLimit( cu, baseCcy, varCcy.getRealCurrency (), settlementDate, rateMap, isCreditProviderLeMaker );
                            if ( applyTenorCoefficient && cuDl != null )
                            {
                                tc = CreditUtilC.getTenorCoefficient( cclr, cu.getCreditLimitRule(), settlementDate, baseCcy, varCcy.getRealCurrency () );
                                if ( bypassTenorRestrictions && CreditLimit.TENOR_COEFFICIENT_NA == tc )
                                {
                                    tc = CreditLimit.DEFAULT_TENOR_COEFFICIENT;
                                }
                                cuDl = applyTenorCoefficient( cuDl, tc );
                            }
                            if ( cuDl != null )
                            {
                                dealingLimit.apply( cuDl );
                            }
                            else
                            {
                                if ( tc != CreditLimit.TENOR_COEFFICIENT_ZERO )
                                {
                                    log.warn("CLSM.getAvailableBaseCurrencyCreditLimit : No dealing limit for enabled credit relationship. clr=" + cu.getCreditLimitRule());
                                }
                            }
                        }
                    }

                    if ( dealingLimit instanceof FXDealingLimit )
                    {
                        ( ( FXDealingLimit ) dealingLimit).setVariableCurrency ( varCcy );
                    }
                }
                else
                {
                    FXDealingLimit fxDl = FXWorkflowFactory.newFXDealingLimit();
                    fxDl.setBaseCurrency( baseCcy );
                    fxDl.setVariableCurrency( varCcy );
                    return fxDl;
                }
            }
            if(dealingLimit!=null && (CreditLimitConstants.CREDIT_SUSPEND == creditStatus))
            {
            	dealingLimit.setSuspended(true);
            }
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( 200 ).append( "CLSM.getAvailableBaseCurrencyCreditLimit : creditProviderLe=" )
                    .append( creditProviderLe ).append( ",creditCpty=" ).append( creditCpty ).append( ",creditCptyOrg=" ).append( creditCptyOrg )
                    .append( ",baseCcy=" ).append( baseCcy ).append( ",varCcy=" ).append( varCcy ).append( ",date=" ).append( settlementDate ).toString(), e );
        }
        return dealingLimit;
    }

    /**
     * Converts the amount from limit currency to amount currency. It uses bid/offer side of rate based on whichever leads to
     * lower currency amount.
     *
     * @param limitCcyAmount amount to be converred
     * @param limitCcy       limit currency
     * @param otherCcy       currency which needs to be converted
     * @param fxMds          market data set
     * @return converted base currency amount
     */
    private double getBaseCurrencyAmount( double limitCcyAmount, Currency limitCcy, Currency otherCcy, FXMarketDataSet fxMds )
    {
        if ( limitCcyAmount <= 0.0 || limitCcy.isSameAs( otherCcy ) )
        {
            return limitCcyAmount;
        }
        FXMarketDataElement fxMde = fxMds.findSpotConversionMarketDataElement( limitCcy, otherCcy, true );
        FXPrice fxPrc = fxMde != null ? fxMde.getFXPrice() : null;

        if ( fxPrc != null )
        {
            FXRateBasis fxRateBasis = fxPrc.getBidFXRate() != null ? fxPrc.getBidFXRate().getFXRateBasis() : fxPrc.getMidFXRate().getFXRateBasis();
            boolean isLimitCcyBase = fxRateBasis == null || limitCcy.isSameAs ( fxRateBasis.getBaseCurrency () );
            FXRate fxRate = isLimitCcyBase ? fxPrc.getBidFXRate() : fxPrc.getOfferFXRate();
            if ( fxRate != null )
            {
                isLimitCcyBase = fxRate.isRateInverted () != isLimitCcyBase;
                fxRate = isLimitCcyBase ? fxPrc.getBidFXRate() : fxPrc.getOfferFXRate();
            }
            if ( fxRate == null )
            {
                fxRate = fxPrc.getMidFXRate() != null ? fxPrc.getMidFXRate() : fxPrc.getComputedMidFXRate();
            }
            if ( fxRate != null )
            {
                return getAmount( limitCcyAmount, limitCcy, fxRate );
            }
        }
        else
        {
            log.warn( new StringBuilder( 200 ).append( "CLSM.getBaseCurrencyAmount : No market rate for baseCcy=" )
                    .append( otherCcy ).append( ",limitCcy=" ).append( limitCcy ).append( " in mds=" ).append( fxMds ).toString() );
        }
        return 0.0;
    }

    private double getAmount( double amount, Currency ccy, FXRate fxRate )
    {
        double rate = fxRate.getRate();
        if ( fxRate.getBaseCurrency().isSameAs( ccy ) )
        {
            return fxRate.getVariableCurrency().round( rate * amount );
        }
        else if ( fxRate.getVariableCurrency().isSameAs( ccy ) && rate != 0.0 )
        {
            return fxRate.getBaseCurrency().round( amount / rate );
        }
        return 0.0;
    }


    public boolean getAvailableBaseCurrencyBilateralCreditLimit( LegalEntity le1, LegalEntity le2, List<CreditEntity> creditEntities, IdcDate date, Currency baseCcy, Currency varCcy, boolean isLe1Maker, double[] bidLimit, double[] offerLimit, boolean isMultiTier )
    {
        DealingLimit dl;
        if ( isLe1Maker )
        {
            creditEntities = CreditUtilC.getAllCreditEntitiesBetween( le2, le1 );
            dl = getAvailableBaseCurrencyBilateralCreditLimit( le1, le2, creditEntities, date, baseCcy, varCcy, true );
        }
        else
        {
            creditEntities = CreditUtilC.getAllCreditEntitiesBetween( le2, le1 );
            dl = getAvailableBaseCurrencyBilateralCreditLimit( le1, le2, creditEntities, date, baseCcy, varCcy, false );
        }
        if ( dl != null )   // bid/offer limits are not modified for credit disabled
        {
            bidLimit[0] = dl.getBidLimit();
            offerLimit[0] = dl.getOfferLimit();
            return true;
        }
        else
        {
            return false;
        }
    }

    /**
     * Computes the daily and aggregate limit and avaiable limits for a legal entity - legal entity - value date subscription.
     * Since LE-LE-value date combination can go through same credit utilization multiple times due to aggregate type credit
     * and org level exposures, a map is used to re-use the calculated available amount to avoid double computation.
     * @param clsi subscription info
     * @param cuMap map containing computed available limit against each credit utilization.
     */
    public void recalculateCreditLimits ( CreditLimitSubscriptionInfo clsi, Map<Long, Double> cuMap )
    {
        try
        {
            List<CreditEntity> creditEntities = CreditUtilC.getAllCreditEntitiesBetween(clsi.getFiLe(), clsi.getLpLe());
            boolean creditActive = false;
            double aggLimit = CreditLimitInfo.NO_LIMIT;
            double aggAvailable = CreditLimitInfo.NO_LIMIT;
            double dailyLimit = CreditLimitInfo.NO_LIMIT;
            double dailyAvailable = CreditLimitInfo.NO_LIMIT;
            Currency limitCcy = null;
            boolean suspendStatus = false;
            double noCheckAggLimit = CreditLimitInfo.NO_LIMIT;
            double noCheckDailyLimit = CreditLimitInfo.NO_LIMIT;
            Currency noCheckLimitCcy = null;
            boolean retrieveNoCheckValues = true;
            Currency USD = CurrencyFactory.getCurrency("USD");
            if (creditEntities != null && !creditEntities.isEmpty())
            {
                if ( cuMap == null )
                {
                    cuMap = new HashMap< Long, Double >();
                }

                for ( CreditEntity creditEntity : creditEntities )
                {
                    final Organization cpo = creditEntity.getLegalEntity().getOrganization();
                    final TradingParty cc = creditEntity.getTradingParty();
                    final Organization cco = creditEntity.getOrganization();
                    int status = getCreditUtilizationCache().getCreditStatus( cpo, cc );
                    if ( CreditLimitConstants.CREDIT_SUSPEND == status || CreditLimitConstants.CREDIT_ACTIVE == status )
                    {
                        if ( CreditLimitConstants.CREDIT_ACTIVE == status )
                        {
                            creditActive = true;
                        }

                        if ( CreditLimitConstants.CREDIT_SUSPEND == status )
                        {
                            suspendStatus = true;
                        }

                        /*
                            TradingParty can be NULL in firm based credit
                         */
                        if( cc == null){
                            if( log.isDebugEnabled() ){
                                log.debug("recalculateCreditLimits : Making available limits 0 as TradingParty is null on CreditEntity="+creditEntity);
                            }
                            aggAvailable = 0.0D;
                            dailyAvailable = 0.0D;
                            aggLimit = 0.0D;
                            dailyLimit = 0.0D;
                            retrieveNoCheckValues = false;
                            continue;
                        }

                        // check for tenor restrictions.
                        CreditTenorRestriction minTenor = getCreditUtilizationCache().getMinimumTenor(cpo, cc);
                        CreditTenorRestriction maxTenor = getCreditUtilizationCache().getMaximumTenor(cpo, cc);

                        if ( ( minTenor != null && Tenor.SPOT_TENOR.compareTo( minTenor.getTenor() ) < 0 ) || ( maxTenor != null && Tenor.SPOT_TENOR.compareTo( maxTenor.getTenor() ) > 0 ))
                        {
                            suspendStatus = true;
                            aggAvailable = 0.0;
                            dailyAvailable = 0.0;
                        }

                        Collection<CreditUtilization> col = getCreditUtilizations(creditEntity.getLegalEntity(), cco, cc, clsi.getValueDate(), CreditLimitConstants.RATE_QUALIFICATION_EVENT);
                        for (CreditUtilization cu : col)
                        {
                            Currency cuCcy = cu.getCurrency();
                            if (limitCcy == null)
                            {
                                limitCcy = cuCcy;
                                clsi.setLimitCcy( limitCcy );
                                clsi.getCreditLimitInfo().setLimitCcy((short) limitCcy.getIndex());
                            }

                            if (cu instanceof DailyCreditUtilization)
                            {
                                if (cu.getCreditLimitRule().getCreditUtilizationCalculator() == null || !cu.getCreditLimitRule().isEnabled())
                                {
                                    if (log.isDebugEnabled())
                                    {
                                        log.debug("CLSM.recalculateCreditLimits : disabled credit methodology. clr=" + cu.getCreditLimitRule());
                                    }
                                    continue;
                                }

                                retrieveNoCheckValues = false;

                                // re-use the available amount value if it is already calculated.
                                Double cachedVal = cuMap.get( cu.getObjectID() );
                                double cuRealtimeAvailable = cachedVal != null ? cachedVal : cu.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeAvailableAmount( cu );
                                if ( cuRealtimeAvailable < 0.0 )
                                {
                                    cuRealtimeAvailable = 0.0;
                                }
                                cuMap.put( cu.getObjectID(), cuRealtimeAvailable );

                                double cuLimit = cu.getLimit();

                                if ( !cuCcy.isSameAs( limitCcy ) )
                                {
                                    // in this case limit amount will be specified in the limit currency of the first legal entity specified.
                                    FXMarketDataSet fxMds = ( FXMarketDataSet ) CreditUtilC.getMarketDataSet( cpo );
                                    cuRealtimeAvailable = getBaseCurrencyAmount( cuRealtimeAvailable, cuCcy, limitCcy, fxMds);
                                    cuLimit = getBaseCurrencyAmount( cuLimit, cuCcy, limitCcy, fxMds);
                                }

                                dailyAvailable = (dailyAvailable == CreditLimitInfo.NO_LIMIT) ? cuRealtimeAvailable : dailyAvailable < cuRealtimeAvailable ? dailyAvailable : cuRealtimeAvailable;
                                dailyLimit = (dailyLimit == CreditLimitInfo.NO_LIMIT) ? cuLimit : dailyLimit < cuLimit ? dailyLimit : cuLimit;
                            }
                            else //aggregate type
                            {
                                if (cu.getCreditLimitRule().getCreditUtilizationCalculator() == null || !cu.getCreditLimitRule().isEnabled())
                                {
                                    if (log.isDebugEnabled())
                                    {
                                        log.debug("CLSM.recalculateCreditLimits : disabled credit methodology. clr=" + cu.getCreditLimitRule());
                                    }
                                    continue;
                                }
                                retrieveNoCheckValues = false;

                                // re-use the available amount value if it is already calculated.
                                Double cachedVal = cuMap.get( cu.getObjectID() );
                                double cuRealtimeAvailable = cachedVal != null ? cachedVal : cu.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeAvailableAmount(cu);
                                if ( cuRealtimeAvailable < 0.0 )
                                {
                                    cuRealtimeAvailable = 0.0;
                                }
                                cuMap.put( cu.getObjectID(), cuRealtimeAvailable );

                                double cuLimit = cu.getLimit();

                                if ( !cuCcy.isSameAs( limitCcy ) )
                                {
                                    FXMarketDataSet fxMds = ( FXMarketDataSet ) CreditUtilC.getMarketDataSet( cpo );
                                    cuRealtimeAvailable = getBaseCurrencyAmount( cuRealtimeAvailable, cuCcy, limitCcy, fxMds);
                                    cuLimit = getBaseCurrencyAmount( cuLimit, cuCcy, limitCcy, fxMds);
                                }

                                aggAvailable = (aggAvailable == CreditLimitInfo.NO_LIMIT) ? cuRealtimeAvailable : aggAvailable < cuRealtimeAvailable ? aggAvailable : cuRealtimeAvailable;
                                aggLimit = (aggLimit == CreditLimitInfo.NO_LIMIT) ? cuLimit : aggLimit < cuLimit ? aggLimit : cuLimit;
                            }
                        }
                    }

                    // incase of no check status or no methodology set, retrieve the limits and currency.
                    if ( retrieveNoCheckValues )
                    {
                        CounterpartyCreditLimitRule cclr = getCreditUtilizationCache().getActiveCounterpartyCreditLimitRule ( cpo, cc );
                        if ( cclr != null )
                        {
                            for ( Object rule: cclr.getChildrenRules() )
                            {
                                CreditLimitRule clr = (CreditLimitRule) rule;
                                Currency clrCcy = clr.getCurrency();
                                if ( noCheckLimitCcy == null )
                                {
                                    noCheckLimitCcy = clrCcy;
                                }
                                if ( clr.isEnabled() && clr.getCreditUtilizationCalculator() != null )
                                {
                                    double clrLimit = clr.getLimitAmount();
                                    if ( !clrCcy.isSameAs( noCheckLimitCcy ) )
                                    {
                                        FXMarketDataSet fxMds = (FXMarketDataSet) CreditUtilC.getMarketDataSet(cpo);
                                        clrLimit = getBaseCurrencyAmount( clrLimit, clrCcy, noCheckLimitCcy, fxMds);
                                    }
                                    if ( clr instanceof SingleCreditLimitRule )
                                    {
                                        noCheckAggLimit = (noCheckAggLimit == CreditLimitInfo.NO_LIMIT) ? clrLimit : noCheckAggLimit < clrLimit ? noCheckAggLimit : clrLimit;
                                    }
                                    else if ( clr instanceof DailyCreditLimitRule )
                                    {
                                        noCheckDailyLimit = (noCheckDailyLimit == CreditLimitInfo.NO_LIMIT) ? clrLimit : noCheckDailyLimit < clrLimit ? noCheckDailyLimit : clrLimit;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            clsi.getCreditLimitInfo().setCreditStatus( suspendStatus ? CreditStatus.SUSPEND : ( creditActive ? CreditStatus.ACTIVE : CreditStatus.NO_CHECK ) );

            // if there are no credit lines which are neither active nor suspended, mark it as no check.
            if ( retrieveNoCheckValues )
            {

                // set the limit currency and amounts for the no-check status.
                clsi.getCreditLimitInfo().setAggregateLimit( (long) noCheckAggLimit);
                clsi.getCreditLimitInfo().setAggregateAvailable((long) noCheckAggLimit);
                clsi.getCreditLimitInfo().setDailyLimit( (long) noCheckDailyLimit);
                clsi.getCreditLimitInfo().setDailyAvailable( (long) noCheckDailyLimit);
                if ( noCheckLimitCcy != null )
                {
                    clsi.setLimitCcy( noCheckLimitCcy );
                    clsi.getCreditLimitInfo().setLimitCcy( (short) noCheckLimitCcy.getIndex() );
                }
                else{
                    clsi.setLimitCcy(USD);
                    clsi.getCreditLimitInfo().setLimitCcy((short)USD.getIndex());
                }
            }
            else
            {
                // set the minimum values for active relationship.
                clsi.getCreditLimitInfo().setAggregateLimit( (long) aggLimit );
                clsi.getCreditLimitInfo().setAggregateAvailable( (long) aggAvailable );
                clsi.getCreditLimitInfo().setDailyLimit( (long) dailyLimit );
                clsi.getCreditLimitInfo().setDailyAvailable( (long) dailyAvailable );
            }
        }
        catch ( Exception e )
        {
            log.error( "CLSM.recalculateCreditLimits : Exception while updating the limits on credit limit subscription info=" + clsi, e);
        }
    }

    public boolean getAvailableBaseCurrencyBilateralCreditLimit( LegalEntity le1, LegalEntity le2, IdcDate date, Currency baseCcy, Currency varCcy, boolean isLe1Maker, double[] bidLimit, double[] offerLimit, boolean isMultiTier )
    {
        DealingLimit dl;
        if ( isLe1Maker )
        {
            List<CreditEntity> creditEntities = CreditUtilC.getAllCreditEntitiesBetween( le2, le1 );
            dl = getAvailableBaseCurrencyBilateralCreditLimit( le1, le2, creditEntities, date, baseCcy, varCcy, true );
        }
        else
        {
            List<CreditEntity> creditEntities = CreditUtilC.getAllCreditEntitiesBetween( le1, le2 );
            dl = getAvailableBaseCurrencyBilateralCreditLimit( le1, le2, creditEntities, date, baseCcy, varCcy, false );
        }
        if ( dl != null )   // bid/offer limits are not modified for credit disabled
        {
            bidLimit[0] = dl.getBidLimit();
            offerLimit[0] = dl.getOfferLimit();
            return true;
        }
        else
        {
            return false;
        }
    }


    public DealingLimit getAvailableBaseCurrencyBilateralCreditLimit( LegalEntity le1, LegalEntity le2, List<CreditEntity> creditEntities, IdcDate date, Currency baseCcy, Currency varCcy, boolean isLe1Maker )
    {
        DealingLimit finalDealingLimit = null;
        Organization org1 = le1.getOrganization();
        Organization org2 = le2.getOrganization();
        if ( org1.isSameAs( org2 ) )
        {
            return null;
        }
        Map<String, FXPrice> rateMap = new HashMap<String, FXPrice>();
        for ( CreditEntity creditEntity : creditEntities )
        {
            DealingLimit dealingLimitAtEachLeg = getAvailableBaseCurrencyCreditLimit( creditEntity.getLegalEntity(), creditEntity.getTradingParty(), creditEntity.getOrganization(), date, baseCcy, varCcy, creditEntity.isMaker(), rateMap );
            finalDealingLimit = getMinimumAvailableDealingLimit( finalDealingLimit, dealingLimitAtEachLeg );
        }
        return finalDealingLimit;
    }

    public DealingLimit getAvailableBaseCurrencyBilateralCreditLimit( LegalEntity le1, LegalEntity le2, IdcDate date, Currency baseCcy, Currency varCcy, boolean isLe1Maker )
    {
        return getAvailableBaseCurrencyBilateralCreditLimit( le1, le2, date, baseCcy, varCcy, isLe1Maker, false );
    }

    public DealingLimit getAvailableBaseCurrencyBilateralCreditLimit( LegalEntity le1, LegalEntity le2, IdcDate date, Currency baseCcy, Currency varCcy, boolean isLe1Maker, boolean isApplyTenorCoefficient )
    {
        return getAvailableBaseCurrencyBilateralCreditLimit ( le1, le2, date, baseCcy, varCcy, isLe1Maker, isApplyTenorCoefficient, null );
    }

    public DealingLimit getAvailableBaseCurrencyBilateralCreditLimit( LegalEntity le1, LegalEntity le2, IdcDate date, Currency baseCcy, Currency varCcy, boolean isLe1Maker, boolean isApplyTenorCoefficient, CreditWorkflowRiders riders )
    {
        DealingLimit dealingLimit = null;
        Organization org1 = le1.getOrganization();
        Organization org2 = le2.getOrganization();
        if ( org1.isSameAs( org2 ) )
        {
            return null;
        }

        TradingParty org1TpForLe2 = le2.getTradingParty( org1 );
        TradingParty org2TpForLe1 = le1.getTradingParty( org2 );
        if ( org1TpForLe2 == null || org2TpForLe1 == null )
        {
            return null;
        }
        List<CreditEntity> allCreditEntitiesBetween;
        if ( isLe1Maker )
        {
            allCreditEntitiesBetween = CreditUtilC.getAllCreditEntitiesBetween( le2, le1 );
        }
        else
        {
            allCreditEntitiesBetween = CreditUtilC.getAllCreditEntitiesBetween( le1, le2 );
        }
        if ( allCreditEntitiesBetween != null )
        {
            Map<String, FXPrice> rateMap = new HashMap<String, FXPrice>();
            for ( CreditEntity creditEntity : allCreditEntitiesBetween )
            {
                DealingLimit dealingLimitAtEachLeg = getAvailableBaseCurrencyCreditLimit( creditEntity.getLegalEntity(), creditEntity.getTradingParty(), creditEntity.getOrganization(), date, baseCcy, varCcy, creditEntity.isMaker(), rateMap, isApplyTenorCoefficient, riders );
                dealingLimit = getMinimumAvailableDealingLimit( dealingLimit, dealingLimitAtEachLeg );
            }
        }
        TradingParty org1PbTp = CreditUtilC.getCreditPrimeBrokerTradingparty( org1TpForLe2 );
        TradingParty org2PbTp = CreditUtilC.getCreditPrimeBrokerTradingparty( org2TpForLe1 );
        boolean isLe1PrimeBrokerCreditEnabled = org1PbTp != null;
        boolean isLe2PrimeBrokerCreditEnabled = org2PbTp != null;
        boolean isBothPrimeBrokerCreditEnabled = isLe1PrimeBrokerCreditEnabled && isLe2PrimeBrokerCreditEnabled;
        if ( isLe1PrimeBrokerCreditEnabled || isLe2PrimeBrokerCreditEnabled )
        {
            if ( isBothPrimeBrokerCreditEnabled )
            {
                Organization pb1Org = org1PbTp.getLegalEntityOrganization();
                Organization pb2Org = org2PbTp.getLegalEntityOrganization();
                if ( dealingLimit != null )
                {
                    if ( isLe1Maker )
                    {
                        if ( CreditUtilC.isCreditRelationshipEffective( pb1Org, le1.getTradingParty( pb1Org ) ) )
                        {
                            dealingLimit.setMakerPBLegalEntity( org1PbTp.getLegalEntity() );
                        }
                        if ( CreditUtilC.isCreditRelationshipEffective( pb2Org, le2.getTradingParty( pb2Org ) ) )
                        {
                            dealingLimit.setTakerPBLegalEntity( org2PbTp.getLegalEntity() );
                        }
                    }
                    else
                    {
                        if ( CreditUtilC.isCreditRelationshipEffective( pb1Org, le1.getTradingParty( pb1Org ) ) )
                        {
                            dealingLimit.setTakerPBLegalEntity( org1PbTp.getLegalEntity() );
                        }
                        if ( CreditUtilC.isCreditRelationshipEffective( pb2Org, le2.getTradingParty( pb2Org ) ) )
                        {
                            dealingLimit.setMakerPBLegalEntity( org2PbTp.getLegalEntity() );
                        }
                    }
                }
            }
            else if ( isLe1PrimeBrokerCreditEnabled )
            {
                Organization pb1Org = org1PbTp.getLegalEntityOrganization();
                if ( dealingLimit != null )
                {
                    if ( isLe1Maker )
                    {
                        if ( CreditUtilC.isCreditRelationshipEffective( org2, le1.getTradingParty( org2 ) ) )
                        {
                            dealingLimit.setMakerPBLegalEntity( le2 );
                        }
                        if ( CreditUtilC.isCreditRelationshipEffective( pb1Org, le2.getTradingParty( pb1Org ) ) )
                        {
                            dealingLimit.setTakerPBLegalEntity( org1PbTp.getLegalEntity() );
                        }
                    }
                    else
                    {
                        if ( CreditUtilC.isCreditRelationshipEffective( org2, le1.getTradingParty( org2 ) ) )
                        {
                            dealingLimit.setTakerPBLegalEntity( le2 );
                        }
                        if ( CreditUtilC.isCreditRelationshipEffective( pb1Org, le2.getTradingParty( pb1Org ) ) )
                        {
                            dealingLimit.setMakerPBLegalEntity( org1PbTp.getLegalEntity() );
                        }
                    }
                }
            }
            else
            {
                Organization pb2Org = org2PbTp.getLegalEntityOrganization();
                if ( dealingLimit != null )
                {
                    if ( isLe1Maker )
                    {
                        if ( CreditUtilC.isCreditRelationshipEffective( pb2Org, le1.getTradingParty( pb2Org ) ) )
                        {
                            dealingLimit.setMakerPBLegalEntity( org2PbTp.getLegalEntity() );
                        }
                        if ( CreditUtilC.isCreditRelationshipEffective( org1, le2.getTradingParty( org1 ) ) )
                        {
                            dealingLimit.setTakerPBLegalEntity( le1 );
                        }
                    }
                    else
                    {
                        if ( CreditUtilC.isCreditRelationshipEffective( pb2Org, le1.getTradingParty( pb2Org ) ) )
                        {
                            dealingLimit.setTakerPBLegalEntity( org2PbTp.getLegalEntity() );
                        }
                        if ( CreditUtilC.isCreditRelationshipEffective( org1, le2.getTradingParty( org1 ) ) )
                        {
                            dealingLimit.setMakerPBLegalEntity( le1 );
                        }
                    }
                }
            }
        }
        else
        {
            if ( dealingLimit != null )
            {
                if ( isLe1Maker )
                {
                    if ( CreditUtilC.isCreditRelationshipEffective( org2, le1.getTradingParty( org2 ) ) )
                    {
                        dealingLimit.setMakerPBLegalEntity( le2 );
                    }
                    if ( CreditUtilC.isCreditRelationshipEffective( org1, le2.getTradingParty( org1 ) ) )
                    {
                        dealingLimit.setTakerPBLegalEntity( le1 );
                    }
                }
                else
                {
                    if ( CreditUtilC.isCreditRelationshipEffective( org2, le1.getTradingParty( org2 ) ) )
                    {
                        dealingLimit.setTakerPBLegalEntity( le2 );
                    }
                    if ( CreditUtilC.isCreditRelationshipEffective( org1, le2.getTradingParty( org1 ) ) )
                    {
                        dealingLimit.setMakerPBLegalEntity( le1 );
                    }
                }
            }
        }
        return dealingLimit;
    }

    /**
     * Returns all valid bilateral credit relations between 2 given le's.
     * This takes into consideration if any org has a masked organization or prime brokerage settings.
     *
     * @param le1 legal entity
     * @param le2 legal entity
     * @return collection of all valid credit relations
     */
    public Collection<CreditEntity> getCreditRelationsBetween2Les( LegalEntity le1, LegalEntity le2 )
    {

        Collection<CreditEntity> crs = new ArrayList<CreditEntity>();
        try
        {
            Organization org1 = le1.getOrganization();
            Organization org2 = le2.getOrganization();
            if ( org1.isSameAs( org2 ) )
            {
                return null;
            }

            TradingParty org1TpForLe2 = le2.getTradingParty( org1 );
            TradingParty org2TpForLe1 = le1.getTradingParty( org2 );
            if ( org1TpForLe2 == null || org2TpForLe1 == null )
            {
                if(!(CounterpartyUtilC.isVenueSetUp( le1 ) || CounterpartyUtilC.isVenueSetUp( le2 ))){
                    return null;
                }
            }
            Organization maskedOrg2 = CreditUtilC.getMaskedOrganization( le2.getOrganization() ); // original org
            Organization maskedOrg1 = CreditUtilC.getMaskedOrganization( le1.getOrganization() ); // original org

            if ( maskedOrg2 != null )
            {
                return CreditUtilC.getAllEnabledCreditEntitiesBetween( le2, le1 );
            }
            else if ( maskedOrg1 != null )
            {
                return CreditUtilC.getAllEnabledCreditEntitiesBetween( le1, le2 );
            }
            return CreditUtilC.getAllEnabledCreditEntitiesBetween( le2, le1 );
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( 200 ).append("CLSM.getCreditRelationsBetween2Les : le1=")
                    .append( le1 ).append( ",le2=" ).append( le2 ).toString(), e );
        }
        return crs;
    }

    public CounterpartyCreditLimitRule getCounterpartyCreditLimitRule( LegalEntity creditProviderLe, Organization creditCptyOrg, TradingParty creditCpty, int eventCode, IdcDate settlementDate )
    {
        Organization creditProviderOrg = creditProviderLe.getOrganization();
        TradingParty tp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        CreditUtilizationCacheEntry cce = getCreditUtilizationCacheEntry( creditProviderLe, creditCptyOrg, tp, eventCode, settlementDate);
        return cce != null && cce.getCounterpartyCreditLimitRule() != null ? cce.getCounterpartyCreditLimitRule() : null;
    }

    public Collection<CreditUtilization> getCreditUtilizations( LegalEntity creditProviderLe, Organization creditCptyOrg, TradingParty creditCpty, IdcDate settlementDate, int eventCode )
    {
        return getCreditUtilizations( creditProviderLe, creditCptyOrg, creditCpty, settlementDate, eventCode, false );
    }

    public Collection getCreditUtilizations(LegalEntity creditProviderLe, Organization creditCptyOrg, TradingParty creditCpty, IdcDate settlementDate, int eventCode, boolean updateStale)
    {
        Organization creditProviderOrg = creditProviderLe.getOrganization();
        TradingParty tp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        CreditUtilizationCacheEntry cce = getCreditUtilizationCacheEntry( creditProviderLe, creditCptyOrg, tp, eventCode, settlementDate );
        return cce != null && cce.getCreditUtilizations() != null ? cce.getCreditUtilizations() : new ArrayList();
    }

    public Collection<CreditUtilizationCacheEntry> getCreditUtilizationCacheEntries( LegalEntity creditProviderLe, Organization creditCptyOrg, TradingParty creditCpty, Entity creditEntity )
    {
        Organization creditProviderOrg = creditProviderLe.getOrganization();
        TradingParty tp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        Collection<CreditUtilizationCacheEntry> cces = new ArrayList<CreditUtilizationCacheEntry>( 2 );
        if ( creditEntity instanceof Trade )
        {
            Collection<TradeLeg> tradeLegs = ( ( Trade ) creditEntity ).getTradeLegs();
            for ( TradeLeg trdLeg : tradeLegs )
            {
                FXLeg fxLeg = ( FXLeg ) trdLeg;
                CreditUtilizationCacheEntry cce = getCreditUtilizationCacheEntry( creditProviderLe, creditCptyOrg, tp, CreditLimitConstants.TRADE_EVENT, fxLeg.getFXPayment().getValueDate() );
                if ( cce == null )
                {
                    return null;
                }
                else
                {
                    cces.add( cce );
                }
            }
        }
        else if ( creditEntity instanceof Request || creditEntity instanceof Quote )
        {
            Collection<DealingPrice> dealingPrices = creditEntity instanceof Request ? ( ( Request ) creditEntity ).getRequestPrices() : ( ( Quote ) creditEntity ).getRequest().getRequestPrices();
            for ( DealingPrice dp : dealingPrices )
            {
                FXLegDealingPrice fxDp = ( FXLegDealingPrice ) dp;
                CreditUtilizationCacheEntry cce = getCreditUtilizationCacheEntry( creditProviderLe, creditCptyOrg, tp, CreditLimitConstants.TRADE_EVENT, fxDp.getFXLeg().getFXPayment().getValueDate() );
                if ( cce == null )
                {
                    return null;
                }
                else
                {
                    cces.add( cce );
                }
            }
        }
        return cces;
    }

    public CreditUtilizationCacheEntry getCreditUtilizationCacheEntry( LegalEntity creditProviderLe, Organization creditCptyOrg, TradingParty creditCpty, int eventCode, IdcDate settlementDate )
    {
        Organization creditProviderOrg = creditProviderLe.getOrganization();
        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        boolean rateEvent = CreditLimitConstants.RATE_QUALIFICATION_EVENT == eventCode;

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 300 ).append( "CLSM.getCreditUtilizationCacheEntry : le=" ).append( creditProviderLe )
                    .append( ",org=" ).append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",creditTp=" )
                    .append( creditTp ).append( ",date=" ).append( settlementDate ).toString() );
        }

        if ( ( creditTp == null && creditCptyOrg == null ) || settlementDate == null )
        {
            log.warn( new StringBuilder( 300 ).append( "CLSM.getCreditUtilizationCacheEntry : Null trading party or date. le=" ).append( creditProviderLe )
                    .append( ",org=" ).append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",creditTp=" )
                    .append( creditTp ).append( ",date=" ).append( settlementDate ).toString() );
            return null;
        }

        CounterpartyCreditLimitRule activeCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty, true );
        if ( activeCclr == null )
        {
            if ( !rateEvent )
            {
                log.warn ( new StringBuilder ( 300 ).append ( "CLSM.getCreditUtilizationCacheEntry : No active credit relation. le=" ).append ( creditProviderLe )
                        .append ( ",org=" ).append ( creditProviderOrg ).append ( ",creditCpty=" ).append ( creditCpty ).append ( ",creditTp=" )
                        .append ( creditTp ).append ( ",date=" ).append ( settlementDate ).toString () );
            }
            return null;
        }

        CreditUtilizationCacheEntry cce = getCreditUtilizationCache().getCreditUtilizationCacheEntry( creditProviderLe, creditCptyOrg, activeCclr.getTradingParty(), settlementDate );

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLSM.getCreditUtilizationCacheEntry : cache collection=" )
                    .append( cce ).append( ",creditTp=" ).append( creditTp ).append( ",date=" ).append( settlementDate ).toString() );
        }
        if ( cce != null )
        {
            if ( cce.validateCreditUtilizations() )
            {
                return cce;
            }
            String cacheKey = CreditUtilizationCacheC.getCacheKey( creditProviderLe, creditCptyOrg, activeCclr.getTradingParty(), settlementDate );
            log.warn( new StringBuilder( 300 ).append( "CLSM.getCreditUtilizationCacheEntry : stale or invalid cce found. le=" ).append( creditProviderLe )
                    .append( ",org=" ).append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",creditTp=" )
                    .append( creditTp ).append( ",date=" ).append( settlementDate ).append( ",cacheKey=" ).append ( cacheKey ).toString() );
            CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule ();
            String staleCclrInfo = cclr.getObjectID () + "#" + cclr.hashCode () + ",version=" + cclr.getVersion () + ",size=" + cclr.getChildrenRules ().size ();
            cclr = ( CounterpartyCreditLimitRule ) IdcUtilC.refreshObject ( cclr );
            String refreshedCclrInfo = cclr != null ? cclr.getObjectID () + "#" + cclr.hashCode () + ",version=" + cclr.getVersion () + ",size=" + cclr.getChildrenRules ().size (): null;
            log.warn( new StringBuilder( 300 ).append( "CLSM.getCreditUtilizationCacheEntry : old cce. staleCclrInfo=" ).append( staleCclrInfo )
                    .append( ",refreshedCclrInfo=" ).append( refreshedCclrInfo ).append ( ",activeCclr=" ).append ( activeCclr.getObjectID () )
                    .append ( "#" ).append ( activeCclr.hashCode () ).append ( ",version=" ).append ( activeCclr.getVersion () ).append ( ",size=" )
                    .append ( activeCclr.getChildrenRules ().size () ).append( ",cacheKey=" ).append( cacheKey ).toString () );
        }

        String cacheKey = CreditUtilizationCacheC.getCacheKey( creditProviderLe, creditCptyOrg, activeCclr.getTradingParty(), settlementDate );

        Collection<Integer> eventCodes = configMBean.getEventCodesUsingSnapshotBasedPositions();
        boolean useSnapshotBasedPos = eventCodes != null && eventCodes.contains( eventCode );

        if ( useSnapshotBasedPos ) // allow the trade, rate to continue without any lock using snapshot based currency positions.
        {
            cce = getCacheEntry( creditProviderLe, creditTp, activeCclr, settlementDate, cacheKey, true );
        }
        else // for the subscriptions and rates.
        {
            final Object lock = getLock( cacheKey );
            synchronized ( lock )
            {
                cce = getCacheEntry( creditProviderLe, creditTp, activeCclr, settlementDate, cacheKey, false );
            }
        }
        return cce;
    }
	
    public boolean isCreditEnabled( LegalEntity le1, LegalEntity le2 )
    {
        boolean enabled;
        Organization org1 = le1.getOrganization();
        Organization org2 = le2.getOrganization();
        if ( org1.isSameAs( org2 ) )
        {
            return false;
        }

        TradingParty org1TpForLe2 = le2.getTradingParty( org1 );
        TradingParty org2TpForLe1 = le1.getTradingParty( org2 );
        if ( org1TpForLe2 == null || org2TpForLe1 == null )
        {
            return false;
        }

        boolean isLe1Maker = true;
        Organization maskedOrg = CreditUtilC.getMaskedOrganization( le1.getOrganization() );
        if ( maskedOrg == null )
        {
            isLe1Maker = false;
            maskedOrg = CreditUtilC.getMaskedOrganization( le2.getOrganization() );
        }
        TradingParty org1PbTp = CreditUtilC.getCreditPrimeBrokerTradingparty( org1TpForLe2 );
        TradingParty org2PbTp = CreditUtilC.getCreditPrimeBrokerTradingparty( org2TpForLe1 );

        if ( maskedOrg != null )
        {
            if ( isLe1Maker )
            {
                le1 = CreditUtilC.getDefaultlegalEntity( maskedOrg );
            }
            else
            {
                le2 = CreditUtilC.getDefaultlegalEntity( maskedOrg );
            }
            return isMaskedCreditEnabled( le1, le2, org1PbTp, org2PbTp );
        }
        boolean isLe1PrimeBrokerCreditEnabled = org1PbTp != null;
        boolean isLe2PrimeBrokerCreditEnabled = org2PbTp != null;
        boolean isBothPrimeBrokerCreditEnabled = isLe1PrimeBrokerCreditEnabled && isLe2PrimeBrokerCreditEnabled;
        if ( isLe1PrimeBrokerCreditEnabled || isLe2PrimeBrokerCreditEnabled )
        {
            if ( isBothPrimeBrokerCreditEnabled )
            {
                enabled = isCreditEnabledBetweenLegalEntities( le1, org1PbTp.getLegalEntity() ) || isCreditEnabledBetweenLegalEntities( le2, org2PbTp.getLegalEntity() );
                if ( !enabled && !org1PbTp.getLegalEntityOrganization().isSameAs( org2PbTp.getLegalEntityOrganization() ) )
                {
                    enabled = isCreditEnabledBetweenLegalEntities( org1PbTp.getLegalEntity(), org2PbTp.getLegalEntity() );
                }
            }
            else if ( isLe1PrimeBrokerCreditEnabled )
            {
                enabled = getCreditUtilizationCache().isCreditEnabled( org1, org1PbTp ) || getCreditUtilizationCache().isCreditEnabled( org2, org2TpForLe1 );
                if ( !enabled )
                {
                    TradingParty pb1TpForLe2 = le2.getTradingParty( org1PbTp.getLegalEntityOrganization() );
                    if ( pb1TpForLe2 != null )
                    {
                        enabled = getCreditUtilizationCache().isCreditEnabled( org1PbTp.getLegalEntityOrganization(), pb1TpForLe2 );
                    }
                    else if ( log.isDebugEnabled() )
                    {
                        log.debug( new StringBuilder( 200 ).append( "CLSM.isCreditEnabled : No trading party found for le2=" )
                                .append( le2 ).append( " in org=" ).append( org1PbTp.getLegalEntityOrganization() ).toString() );
                    }
                }
            }
            else
            {
                enabled = getCreditUtilizationCache().isCreditEnabled( org2, org2PbTp ) || getCreditUtilizationCache().isCreditEnabled( org1, org1TpForLe2 );
                if ( !enabled )
                {
                    TradingParty pb2TpForLe1 = le1.getTradingParty( org2PbTp.getLegalEntityOrganization() );
                    if ( pb2TpForLe1 != null )
                    {
                        enabled = getCreditUtilizationCache().isCreditEnabled( org2PbTp.getLegalEntityOrganization(), pb2TpForLe1 );
                    }
                    else if ( log.isDebugEnabled() )
                    {
                        log.debug( new StringBuilder( 200 ).append( "CLSM.isCreditEnabled : No trading party found for le1=" )
                                .append( le1 ).append( " in org=" ).append( org2PbTp.getLegalEntityOrganization() ).toString() );
                    }
                }
            }
        }
        else
        {
            enabled = isCreditEnabledBetweenLegalEntities( le1, le2 );
        }
        return enabled;
    }

    public boolean isMaskedCreditEnabled( LegalEntity le1, LegalEntity le2, TradingParty org1PbTp, TradingParty org2PbTp )
    {
        boolean enabled = false;
        boolean isLe1PrimeBrokerCreditEnabled = org1PbTp != null;
        boolean isLe2PrimeBrokerCreditEnabled = org2PbTp != null;
        boolean isBothPrimeBrokerCreditEnabled = isLe1PrimeBrokerCreditEnabled && isLe2PrimeBrokerCreditEnabled;
        if ( isLe1PrimeBrokerCreditEnabled || isLe2PrimeBrokerCreditEnabled )
        {
            if ( isBothPrimeBrokerCreditEnabled )
            {
                enabled = isCreditEnabledBetweenLegalEntities( le1, org1PbTp.getLegalEntity() ) || isCreditEnabledBetweenLegalEntities( le2, org2PbTp.getLegalEntity() );
                if ( !enabled && !org1PbTp.getLegalEntityOrganization().isSameAs( org2PbTp.getLegalEntityOrganization() ) )
                {
                    enabled = isCreditEnabledBetweenLegalEntities( org1PbTp.getLegalEntity(), org2PbTp.getLegalEntity() );
                }
            }
            else if ( isLe1PrimeBrokerCreditEnabled )
            {
                enabled = isCreditEnabledBetweenLegalEntities( le1, org1PbTp.getLegalEntity() ) || isCreditEnabledBetweenLegalEntities( le2, org1PbTp.getLegalEntity() );
            }
            else
            {
                enabled = isCreditEnabledBetweenLegalEntities( le1, org2PbTp.getLegalEntity() ) || isCreditEnabledBetweenLegalEntities( le2, org2PbTp.getLegalEntity() );
            }
        }
        return enabled;
    }

    /**
     * Returns the minimum of two amount of instruments. One of the amount of instrument parameters will be modified for the minimum amount.
     *
     * @param aoi1 amount of instrument 1
     * @param aoi2 amount of instrument 2
     * @param org  organization
     * @return minimum of amount of instrument
     */
    private AmountOfInstrument getMinimumAvailableAmountOfInstrument( AmountOfInstrument aoi1, AmountOfInstrument aoi2, Organization org )
    {
        AmountOfInstrument limitAoi = aoi1;
        if ( aoi2 != null )
        {
            if ( limitAoi == null )
            {
                limitAoi = aoi2;
            }
            else
            {
                // limit currencies can be different
                if ( limitAoi.getInstrument().isSameAs( aoi2.getInstrument() ) )
                {
                    limitAoi.setAmount( Math.min( limitAoi.getAmount(), aoi2.getAmount() ) );
                }
                else
                {
                    // in this case limit amount will be specified in the limit currency of the first legal entity specified.
                    FXMarketDataSet fxMds = ( FXMarketDataSet ) CreditUtilC.getMarketDataSet( org );
                    FXMarketDataElement fxMde = fxMds.findSpotConversionMarketDataElement( ( Currency ) limitAoi.getInstrument(), ( Currency ) aoi2.getInstrument(), true );
                    if ( fxMde != null )
                    {
                        FXRate fxRate = fxMde.getFXRate();
                        double aoi2AmtInOrg1LimitCcy = fxRate.getAmount( aoi2.getAmount(), ( Currency ) aoi2.getInstrument() );
                        limitAoi.setAmount( Math.min( limitAoi.getAmount(), aoi2AmtInOrg1LimitCcy ) );
                    }
                    else
                    {
                        log.warn( new StringBuilder( 200 ).append( "CLSM.getMinimumAvailableAmountOfInstrument : No rate between limit currencies. aoi1=" )
                                .append( aoi1 ).append( ",aoi2=" ).append( aoi2 ).append( ",org=" ).append( org ).append( ",fxMds=" ).append( fxMds ).toString() );
                        limitAoi.setAmount( 0.0 );
                    }
                }
            }
        }
        return limitAoi;
    }

    /**
     * Returns the minimum of available dealing limit from the specified dealing limits.
     *
     * @param dealingLimit1 dealing limit 1
     * @param dealingLimit2 dealing limit 2
     * @return minimum dealing limit
     */
    private DealingLimit getMinimumAvailableDealingLimit( DealingLimit dealingLimit1, DealingLimit dealingLimit2 )
    {
        DealingLimit dealingLimit = dealingLimit1;
        if ( dealingLimit2 != null )
        {
            if ( dealingLimit == null )
            {
                dealingLimit = dealingLimit2;
            }
            else
            {
                dealingLimit.apply( dealingLimit2 );
            }
        }
        return dealingLimit;
    }

    private Object getLock( String key )
    {
        Object lock = lockMap.get( key );
        if ( lock == null )
        {
            lock = new Object();
            lockMap.putIfAbsent( key, lock );
            lock = lockMap.get( key );
        }
        return lock != null ? lock : new Object();
    }

    public void resetLockMap()
    {
        lockMap.clear();
    }

    private CreditUtilizationCacheEntry getCacheEntry( LegalEntity cpl, TradingParty cc, CounterpartyCreditLimitRule activeCclr, IdcDate settlementDate, String cacheKey, boolean tradeEvent )
    {
        Organization cpo = cpl.getOrganization();
        Organization cco = activeCclr.getTradingPartyOrganization();
        CreditUtilizationCacheEntry cce = getCreditUtilizationCache().getCreditUtilizationCacheEntry( cpl, cco, activeCclr.getTradingParty(), settlementDate );
        if ( cce != null )
        {
            if ( cce.validateCreditUtilizations() )
            {
                return cce;
            }
        }
        Collection<CreditLimitRule> rules = activeCclr.getChildrenRules();
        Collection<CreditUtilization> credtiUtilizations = new ArrayList<CreditUtilization>( 2 );
        if ( rules != null && !rules.isEmpty() )
        {
            boolean firstRule = true;
            boolean rebuiltFromSnapshot = false;
            boolean logFetchCUFromDB = false;
            for ( CreditLimitRule clr : rules )
            {
                if ( !clr.isEnabled() )
                {
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "CLSM.getCacheEntry : credit limit rule is not enabled. clr=" + clr );
                    }
                    continue;
                }
                if ( tradeEvent )
                {
                    if ( firstRule )
                    {
                        firstRule = false;
                    }
                    logFetchCUFromDB = true;
                }
                long t0 = System.currentTimeMillis();
                CreditUtilization cu = CreditUtilizationManagerC.getInstance().getCreditUtilizationForDate( logFetchCUFromDB, clr, settlementDate, "CLSM.getCacheEntry" );

                if( cu == null && (clr instanceof DailyCreditLimitRule)
                       && CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationLookupSpacesEnabled(cpl.getNamespace().getShortName()))
                {
                    // If you have reached here, then there is no CU for this date with a valid utilization or overridden limit.
                    // so create one on-demand and cache it.This guy will be persisted eventually by the CUPersistenceTask.
                    // But do this only if CU lookup from RDS is enabled. Else, DCU is created automatically upfront (by CreateUtilizationManager.createCreditUtilizations).
                    cu = createNewDailyCreditUtilization(clr, settlementDate);
                }
                long t1 = System.currentTimeMillis();
                if ( cu != null )
                {
                    boolean cceNullOrNotStale = cce == null || !cce.isStale();
                    boolean aggGrossWithPL = CreditLimitConstants.GROSS_AGGREGATE_LIMIT_CALCULATOR.isSameAs ( clr.getCreditUtilizationCalculator () ) && cu.isApplyPandL ();
                    boolean cashSettlement = CreditUtilC.isCashSettlement ( cu.getCreditLimitRule () );
                    boolean aggMultiFactor = CreditUtilC.isAggregateMultiFactorSettlement( cu.getCreditLimitRule () );
                    boolean skipRebuildFromSnapshot = aggGrossWithPL || cashSettlement || aggMultiFactor;
                    if ( cceNullOrNotStale && CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getAllCreditUtilizations().contains( cu ) )
                    {
                        if ( log.isDebugEnabled() )
                        {
                            log.debug( "CLSM.getCacheEntry : cu exists in the cache. cu=" + cu + ",skipRebuildFromSnapshot=" + skipRebuildFromSnapshot );
                        }
                        Object cps = cu.getCurrencyPositions( false );
                        if ( cps == null )
                        {
                            if ( tradeEvent && !skipRebuildFromSnapshot )
                            {
                                cu.rebuildCurrencyPositionsFromSnapshot( CreditLimitConstants.RESET_CACHE_ENTRY, true );
                                rebuiltFromSnapshot = true;
                            }
                            else
                            {
                                cu.getCurrencyPositions( true );
                            }
                        }
                    }
                    else
                    {
                        if ( tradeEvent && !skipRebuildFromSnapshot )
                        {
                            cu.rebuildCurrencyPositionsFromSnapshot( CreditLimitConstants.RESET_CACHE_ENTRY, true );
                            rebuiltFromSnapshot = true;
                        }
                        else
                        {
                            cu.rebuildCurrencyPositions( CreditLimitConstants.RESET_CACHE_ENTRY, false );
                        }
                    }
                    if ( clr.isEnabled () )
                    {
                        credtiUtilizations.add ( cu );
                    }
                    else
                    {
                        log.info( new StringBuilder( 500 ).append( "CLSM.getCacheEntry : credit limit rule not enabled. cu=" )
                                .append( cu ).append( ",clr=" ).append( clr ).append( ",cacheKey=" ).append( cacheKey ).toString() );
                    }
                    long t2 = System.currentTimeMillis();
                    if ( ( t1 - t0 >= 300 ) || ( t2 - t1 >= 300 ) )
                    {
                        log.info( new StringBuilder( 500 ).append( "CLSM.getCacheEntry : Time taken to obtain cu=" )
                                .append( ( t1 - t0 ) ).append( " ,to build ccy positions=" ).append( ( t2 - t1 ) ).append( ",cacheKey=" ).
                                        append( cacheKey ).toString() );
                    }
                }
                else
                {
                    log.warn( "CLSM.getCreditUtilizationCacheEntry : No credit utilizations found on date : " + settlementDate + " on rule : " + clr + " for creditCpty : " + cc );
                }
            }
            cce = new CreditUtilizationCacheEntryC( settlementDate, credtiUtilizations, activeCclr, cpo, false );
            getCreditUtilizationCache().addCreditUtilizationCacheEntry( cpl, cco, activeCclr.getTradingParty(), settlementDate, cce );
            if ( rebuiltFromSnapshot )
            {
                cce.setStale( true );
            }
        }
        else
        {
            log.warn( "CLSM.getCacheEntry : No credit limit rules for creditCpty : " + cc + " in org : " + cpo );
        }
        return cce;
    }

    private DailyCreditUtilization createNewDailyCreditUtilization( CreditLimitRule clr, IdcDate settlementDate )
    {
        DailyCreditUtilization dcu = CreditLimitFactory.newDailyCreditUtilization();
        dcu.setNamespace( clr.getNamespace() );
        dcu.setCreditLimitRule( clr );
        dcu.setDate( settlementDate );
        return dcu;
    }

    public CreditWorkflowMessage checkMultiLegFXTradeCredit( Request request, LegalEntity takerLe, LegalEntity makerLe )
    {
        CreditWorkflowRiders riders = new CreditWorkflowRiders ();
        if ( request.getRequestAttributes () != null && request.getRequestAttributes ().isMakerCreditOverride () )
        {
            riders.setExcessUtilizationAllowedMakerOnly ( true );
            riders.setBypassTenorRestrictionsMakerOnly ( true );
            riders.setMakerOrg ( makerLe.getOrganization () );
        }

        FXTrade trade = ( FXTrade ) request.getTrade();
        if ( trade instanceof FXSwap )
        {
            // for swap the bid offer mode of far leg to determine the whether it is bid, offer or two-way
            int boMode = request.getRequestPrice( ISCommonConstants.FAR_LEG ).getBidOfferMode();
            return _checkMultiLegFXTradeCredit( trade, takerLe, makerLe, boMode, riders );
        }
        return _checkMultiLegFXTradeCredit( trade, takerLe, makerLe, DealingPrice.UNDEFINED, riders );
    }

    public CreditWorkflowMessage checkMultiLegFXTradeCredit( FXTrade trade, LegalEntity takerLe, LegalEntity makerLe )
    {
        return _checkMultiLegFXTradeCredit( trade, takerLe, makerLe, DealingPrice.UNDEFINED,  null  );
    }

    private CreditWorkflowMessage _checkMultiLegFXTradeCredit( FXTrade trade, LegalEntity takerLe, LegalEntity makerLe, int bidOfferMode, CreditWorkflowRiders riders )
    {
        Map<LegalEntity, AggregateCurrencyPosition> netPosLEMap = new HashMap<LegalEntity, AggregateCurrencyPosition>(); // to hold the aggregate position at legal entity level
        Map<IdcDate, CurrencyPosition> dateLEMap = new HashMap<IdcDate, CurrencyPosition>(); // to hold the daily positions at legal entity level
        Map<Organization, AggregateCurrencyPosition> netPosOrgMap = new HashMap<Organization, AggregateCurrencyPosition>(); // to hold the aggregate positions at org level
        Map<IdcDate, CurrencyPosition> dateOrgMap = new HashMap<IdcDate, CurrencyPosition>(); // to hold the daily positions at org level
        CurrencyPair ccyPair = trade.getFXRateBasis().getCurrencyPair();
        AggregateCurrencyPosition aggregateLECps = netPosLEMap.get( takerLe );
        AggregateCurrencyPosition aggregateOrgCps = netPosLEMap.get( takerLe.getOrganization() );
        IdcDate farthestDate = null;
        IdcDate earliestDate = null;
        for ( Object tl : trade.getTradeLegs() )
        {
            FXLeg fxLeg = ( FXLeg ) tl;
            FXPaymentParameters fxPmt = fxLeg.getFXPayment();
            IdcDate valDate = fxPmt.getValueDate();
            if ( farthestDate == null || valDate.isLaterThan( farthestDate ) )
            {
                farthestDate = valDate;
            }
            if ( earliestDate == null || valDate.isEarlierThan( earliestDate  ))
            {
                earliestDate = valDate;
            }

            CurrencyPosition cps = dateLEMap.get( valDate );
            Currency dltCcy = fxPmt.getDealtCurrency();
            if ( cps == null )
            {
                cps = new CurrencyPositionC( dltCcy );
                dateLEMap.put( valDate, cps );
            }
            double dltAmt = fxPmt.getDealtAmount();
            boolean isBuyingDealt = fxPmt.isBuyingDealtCurrency();

            // extract buying/selling of dealt currency from bid offer mode.
            if ( bidOfferMode == DealingPrice.BID || bidOfferMode == DealingPrice.OFFER )
            {
                int legBidOfferMode = FXSwap.FAR_LEG.equals ( fxLeg.getName () ) ? bidOfferMode : ( bidOfferMode == DealingPrice.BID ? DealingPrice.OFFER : DealingPrice.BID );
                boolean isDealtBase = ccyPair.getBaseCurrency ().isSameAs( dltCcy );
                isBuyingDealt = (legBidOfferMode == DealingPrice.BID) != isDealtBase;
                if ( log.isDebugEnabled () )
                {
                    log.debug ( "CLSM._checkMultiLegFXTradeCredit : leg Name=" + fxLeg.getName () + ",bidOfferMode=" + bidOfferMode + ",isBuyingDealt="
                            + isBuyingDealt + ",isDealtBase=" + isDealtBase + ",ccyPair=" + ccyPair + ",legBidOfferMode=" + legBidOfferMode );
                }
            }
            cps.addAmount( isBuyingDealt ? dltAmt : -dltAmt );

            // populate the aggregate amounts.
            if ( aggregateLECps == null )
            {
                aggregateLECps = new AggregateCurrencyPositionC( dltCcy, true );
                netPosLEMap.put( takerLe, aggregateLECps );
            }
            aggregateLECps.addAmount( valDate, isBuyingDealt ? dltAmt : -dltAmt );

            // handle org level aggregates
            CurrencyPosition orgCps = dateOrgMap.get( valDate );
            if ( orgCps == null )
            {
                orgCps = new CurrencyPositionC( dltCcy );
                dateOrgMap.put( valDate, orgCps );
            }
            orgCps.addAmount( isBuyingDealt ? dltAmt : -dltAmt );

            // populate the aggregate amounts.
            if ( aggregateOrgCps == null )
            {
                aggregateOrgCps = new AggregateCurrencyPositionC( dltCcy, true );
                netPosOrgMap.put( takerLe.getOrganization(), aggregateOrgCps );
            }
            aggregateOrgCps.addAmount( valDate, isBuyingDealt ? dltAmt : -dltAmt );

        }

        // check credit between taker legal entity and maker legal entity.
        Map<String, FXPrice> rateMap = new HashMap<String, FXPrice>();
        CreditWorkflowMessage cwm = checkAvailableCredit( takerLe, makerLe, netPosLEMap.get( takerLe ), dateLEMap, ccyPair.getBaseCurrency(), ccyPair.getVariableCurrency(), rateMap, bidOfferMode, farthestDate, earliestDate, netPosOrgMap.get( takerLe.getOrganization() ), dateOrgMap, false, riders );

        log.info( new StringBuilder( 200 ).append( "CLSM._checkMultiLegFXTradeCredit : creditAvailable=" ).append( cwm )
                .append( ",takerLe=" ).append( takerLe ).append( ",makerLe=" ).append( makerLe ).append( ",trd.cptyA=" )
                .append( trade.getCounterpartyA() ).append( ",trd.cptyB=" ).append( trade.getCounterpartyB() ).append( ",tid=" )
                .append( trade.getTransactionID() ).toString() );

        return cwm == null || MessageStatus.SUCCESS.equals( cwm.getStatus() ) ? null : CreditUtilC.getCreditWorkflowMessageWithError( null, cwm.getErrorCode () != null ? cwm.getErrorCode() : CreditLimit.ERROR_INSUFFICIENT_CREDIT );
    }

    /**
     * For backward compatibility.
     *
     * @param np netting portfolio
     * @return credit workflow message
     */
    public CreditWorkflowMessage checkPortfolioCredit( NettingPortfolio np )
    {
        return checkPortfolioCredit( np, false, false );
    }

    public CreditWorkflowMessage checkPortfolioCredit( NettingPortfolio np, boolean useAcceptedRequests, boolean isAcceptance )
    {
        Organization toOrg = null;
        if ( np.getToOrganizations () != null && !np.getToOrganizations ().isEmpty () )
        {
            toOrg = np.getToOrganizations ().iterator ().next ();
        }
        return creditConfigMBean.isCreditLineBasedNettingPortfolioCheckEnabled ( toOrg ) ? _checkCreditLineBasedPortfolioCredit( np, useAcceptedRequests, isAcceptance )
                : _checkPortfolioCredit ( np, useAcceptedRequests, isAcceptance );
    }

    private CreditWorkflowMessage _checkPortfolioCredit( NettingPortfolio np, boolean useAcceptedRequests, boolean isAcceptance )
    {
        final Collection<Organization> orgs = np.getToOrganizations();
        if ( orgs == null || orgs.isEmpty() )
        {
            return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_PORTFOLIO_TO_ORGANIZATIONS_NOT_SET );
        }

        // iterate through the batch requests and add the total amounts on a value date basis.
        final Collection<NettingTradeRequest> tradeRequests = useAcceptedRequests ? np.getAcceptedRequests() : np.getInputRequestPojos();
        if ( tradeRequests == null || tradeRequests.isEmpty() )
        {
            return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_PORTFOLIO_NO_TRADE_REQUESTS );
        }
        Map<LegalEntity, Map<IdcDate, CurrencyPosition>> netRequestsLEMap = new HashMap<LegalEntity, Map<IdcDate, CurrencyPosition>>( tradeRequests.size() );
        Map<LegalEntity, AggregateCurrencyPosition> netPosLEMap = new HashMap<LegalEntity, AggregateCurrencyPosition>( tradeRequests.size() );
        Map<Organization, Map<IdcDate, CurrencyPosition>> netRequestsOrgMap = new HashMap<Organization, Map<IdcDate, CurrencyPosition>>( tradeRequests.size() );
        Map<Organization, AggregateCurrencyPosition> netPosOrgMap = new HashMap<Organization, AggregateCurrencyPosition>( tradeRequests.size() );
        CurrencyPair ccyPair = null;
        Map<LegalEntity, IdcDate> earliestDateMap = new HashMap<LegalEntity, IdcDate>( tradeRequests.size() );
        Map<LegalEntity, IdcDate> farthestDateMap = new HashMap<LegalEntity, IdcDate>( tradeRequests.size() );
        for ( NettingTradeRequest ntr : tradeRequests )
        {
            // check for excluded trade requests.
            if ( ntr.isExcluded() )
            {
                continue;
            }

            LegalEntity takerLe = ( LegalEntity ) ntr.getFund();
            Map<IdcDate, CurrencyPosition> dateLEMap = netRequestsLEMap.get( takerLe );
            if ( dateLEMap == null )
            {
                dateLEMap = new HashMap<IdcDate, CurrencyPosition>();
                netRequestsLEMap.put( takerLe, dateLEMap );
            }

            Map<IdcDate, CurrencyPosition> dateOrgMap = netRequestsOrgMap.get( takerLe.getOrganization() );
            if ( dateOrgMap == null )
            {
                dateOrgMap = new HashMap<IdcDate, CurrencyPosition>();
                netRequestsOrgMap.put( takerLe.getOrganization(), dateOrgMap );
            }

            AggregateCurrencyPosition aggregateLECps = netPosLEMap.get( takerLe );
            AggregateCurrencyPosition aggregateOrgCps = netPosOrgMap.get( takerLe.getOrganization() );
            for ( TradeRequestLeg trl : ntr.getTradeLegs() )
            {
                if ( ccyPair == null )
                {
                    ccyPair = CurrencyFactory.getCurrencyPair( trl.getBaseCurrency(), trl.getTermCurrency() );
                }
                IdcDate valDate = trl.getValueDate();

                // populate the earliest and farthest value date on legal entity level.
                final IdcDate farthestDateForLE = farthestDateMap.get( takerLe );
                if ( farthestDateForLE == null || valDate.isLaterThan(farthestDateForLE))
                {
                    farthestDateMap.put( takerLe, valDate );
                }

                final IdcDate earliestDateForLE = earliestDateMap.get( takerLe );
                if ( earliestDateForLE == null || valDate.isEarlierThan(earliestDateForLE))
                {
                    earliestDateMap.put( takerLe, valDate );
                }

                CurrencyPosition leCps = dateLEMap.get( valDate );
                if ( leCps == null )
                {
                    leCps = new CurrencyPositionC( trl.getDealtCurrency() );
                    dateLEMap.put( valDate, leCps );
                }
                leCps.addAmount( trl.isBuyingDealt() ? trl.getDealtAmount() : -trl.getDealtAmount() );

                // populate the aggregate amounts.
                if ( aggregateLECps == null )
                {
                    aggregateLECps = new AggregateCurrencyPositionC( trl.getDealtCurrency(), true );
                    netPosLEMap.put( takerLe, aggregateLECps );
                }
                aggregateLECps.addAmount( valDate, trl.isBuyingDealt() ? trl.getDealtAmount() : -trl.getDealtAmount() );

                // handle org level aggregate positions
                CurrencyPosition orgCps = dateOrgMap.get( valDate );
                if ( orgCps == null )
                {
                    orgCps = new CurrencyPositionC( trl.getDealtCurrency() );
                    dateOrgMap.put( valDate, orgCps );
                }
                orgCps.addAmount( trl.isBuyingDealt() ? trl.getDealtAmount() : -trl.getDealtAmount() );

                // populate the aggregate amounts.
                if ( aggregateOrgCps == null )
                {
                    aggregateOrgCps = new AggregateCurrencyPositionC( trl.getDealtCurrency(), true );
                    netPosOrgMap.put( takerLe.getOrganization(), aggregateOrgCps );
                }
                aggregateOrgCps.addAmount( valDate, trl.isBuyingDealt() ? trl.getDealtAmount() : -trl.getDealtAmount() );
            }
        }

        boolean creditAvailable = false;
        Map<String, FXPrice> rateMap = new HashMap<String, FXPrice>();
        String errorCode = null;
        for ( Organization org : orgs )
        {
            boolean creditAvailableForOrg = true;
            final Organization realLP = org.getRealLP();
            final LegalEntity makerLe = realLP != null ? realLP.getDefaultDealingEntity() : org.getDefaultDealingEntity();

            for ( LegalEntity takerLe : netRequestsLEMap.keySet() )
            {
                // check for valid trading relationships between taker LE and maker LE.
                if ( !CounterpartyUtilC.isValidRelationShip( takerLe, makerLe ) )
                {
                    creditAvailableForOrg = false;
                    errorCode = ISCommonConstants.PORTFOLIO_VALIDATION_TRADINGRELATIONSHIP_NOCOUNTERPARTYBORG;
                    String alertMsg = new StringBuilder( 200 ).append( "CLSM.checkAvailableCredit : no trading relationship between takerLE=" )
                            .append( takerLe.getFullyQualifiedName() ).append( " and makerLE=" ).append( makerLe.getFullyQualifiedName() )
                            .append( ",toOrg=" ).append( org ).append( ",realLP=" ).append( realLP ).toString();
                    log.info( alertMsg );
                    MessageLogger.getInstance().log( ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null );
                    break;
                }

                // iterate through the taker legal entities and check credit between taker legal entity and maker legal entity.
                CreditWorkflowMessage cwm = checkAvailableCredit( takerLe, makerLe, netPosLEMap.get( takerLe ), netRequestsLEMap.get( takerLe ),
                        ccyPair.getBaseCurrency(), ccyPair.getVariableCurrency(), rateMap, DealingPrice.UNDEFINED, farthestDateMap.get(takerLe),
                        earliestDateMap.get( takerLe), netPosOrgMap.get( takerLe.getOrganization() ), netRequestsOrgMap.get( takerLe.getOrganization() ) , isAcceptance, null );

                if ( cwm != null && MessageStatus.FAILURE.equals( cwm.getStatus() ) )
                {
                    creditAvailableForOrg = false;
                    errorCode = cwm.getErrorCode();
                    break;
                }
            }

            // if one org is successfully passed credit, then no need to check for other orgs.
            if ( creditAvailableForOrg )
            {
                creditAvailable = true;
                break;
            }
        }
        return creditAvailable ? null : CreditUtilC.getCreditWorkflowMessageWithError( null, errorCode != null ? errorCode : CreditLimit.ERROR_INSUFFICIENT_CREDIT );
    }

    /**
     * This method is used by the portfolio credit check after date level and aggregate level positions are aggregated in map data structure.
     *
     * @param takerLe         taker legal entity
     * @param makerLe         maker legal entity
     * @param aggregateLEPos    aggregate position map at the legal entity level
     * @param datePosLEMap      date level aggregation map at the legal entity level
     * @param baseCcy         base ccy
     * @param varCcy          var ccy
     * @param rateMap         rate map
     * @param aggregateOrgPos aggregate position at org level
     * @param datePosOrgMap date level aggregation map at org level
     * @return credit workflow message
     */
    private CreditWorkflowMessage checkAvailableCredit( final LegalEntity takerLe, final LegalEntity makerLe, final AggregateCurrencyPosition aggregateLEPos, final Map<IdcDate, CurrencyPosition> datePosLEMap,
                                          Currency baseCcy, Currency varCcy, Map<String, FXPrice> rateMap, int bidOfferMode, IdcDate farthestDate, IdcDate earliestDate,
                                          final AggregateCurrencyPosition aggregateOrgPos, final Map<IdcDate, CurrencyPosition> datePosOrgMap, boolean rejectOnSuspendedStatus, CreditWorkflowRiders riders )
    {
        IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ();
        final CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair( baseCcy, varCcy );
        final Collection<CreditEntity> creditRelationships = getCreditRelationsBetween2Les( takerLe, makerLe );
        if ( creditRelationships != null && !creditRelationships.isEmpty () )
        {
            for ( CreditEntity cr : creditRelationships )
            {
                final Organization cpo = cr.getLegalEntity().getOrganization();
                final TradingParty cc = cr.getTradingParty();
                CreditUtilizationCache creditUtilizationCache = getCreditUtilizationCache();
                boolean makerOverride = riders != null && riders.getMakerOrg () != null && riders.getMakerOrg ().isSameAs ( cpo );
                boolean makerOnlyBypassTenorRestrictions = makerOverride && riders.isBypassTenorRestrictionsMakerOnly ();
                boolean bypassTenorRestrictions = ( riders != null && riders.isBypassTenorRestrictions() ) || makerOnlyBypassTenorRestrictions;
                boolean makerOnlyExcessUtilizationAllowed = makerOverride && riders.isExcessUtilizationAllowedMakerOnly ();
                boolean allowExcessUtilizationAllowed = ( riders != null && riders.isExcessCreditUtilizationAllowed() )  || makerOnlyExcessUtilizationAllowed;
                if ( creditUtilizationCache.isCreditEnabled(cpo, cc) )
                {
                    if ( rejectOnSuspendedStatus && CreditLimitConstants.CREDIT_SUSPEND == getCreditUtilizationCache().getCreditStatus( cpo, cc ) )
                    {
                    	String alertMsg = "CLSM.checkAvailableCredit : credit is suspended from credit provider org=" + cpo + " to counterparty=" + cc; 
                        log.info( alertMsg );
                        MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                        return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_CREDIT_SUSPENDED );
                    }

                    // add minimum tenor validation
                    CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, cc, true );
                    if ( cclr == null )
                    {
                        log.warn( "CLSM.checkAvailableCredit : no active cclr for creditLine=" + cr );
                        continue;
                    }
                    CreditTenorRestriction minTenor = creditUtilizationCache.getMinimumTenor(cpo, cc);// CreditUtilC.getMinimumTenor( cclr );
                    if ( minTenor != null && earliestDate != null && !bypassTenorRestrictions )
                    {
                        IdcDate minTenorDate = CreditUtilC.getValueDateForTenor( baseCcy, varCcy, minTenor );
                        if ( earliestDate.isEarlierThan( minTenorDate ) )
                        {
                        	String alertMsg = new StringBuilder( 200 ).append( "CLSM.checkAvailableCredit : minimum tenor validation failed. cpo=" )
                                    			.append( cpo ).append( ",cc=" ).append( cc ).append( ",minTenor=" ).append( minTenor ).append( ",minTenorDate=" )
                                				.append( minTenorDate ).append( ",earliestDate=" ).append( earliestDate ).toString(); 
                            log.info( alertMsg );
                            MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                            return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_EXCEED_MINIMUM_TENOR );
                        }
                    }

                    CreditTenorRestriction maxTenor = creditUtilizationCache.getMaximumTenor(cpo, cc);//CreditUtilC.getMaximumTenor( cclr );
                    if ( maxTenor != null && farthestDate != null && !bypassTenorRestrictions )
                    {
                        IdcDate maxTenorDate = CreditUtilC.getValueDateForTenor( baseCcy, varCcy, maxTenor );
                        if ( farthestDate.isLaterThan( maxTenorDate ) )
                        {
                        	String alertMsg = new StringBuilder( 200 ).append( "CLSM.checkAvailableCredit : maximum tenor validation failed. cpo=" )
                                    			.append( cpo ).append( ",cc=" ).append( cc ).append( ",maxTenor=" ).append( maxTenor ).append( ",maxTenorDate=" )
                                    			.append( maxTenorDate ).append( ",farthestDate=" ).append( farthestDate ).toString(); 
                            log.info( alertMsg );
                            MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                            return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR );
                        }
                    }

                    final Organization cco = cr.getTradingParty().getLegalEntityOrganization();
                    final FXMarketDataSet fxMds = ( FXMarketDataSet ) CreditUtilC.getMarketDataSet( cpo );
                    boolean aggregateChecked = false;
                    boolean orgLevelAggregateCheckEnabled = cco.isSameAs( takerLe.getOrganization() ) && cclr.isOrgLevel();
                    final Map<IdcDate, CurrencyPosition> datePosMap = orgLevelAggregateCheckEnabled ? datePosOrgMap : datePosLEMap;
                    final AggregateCurrencyPosition aggregatePos = orgLevelAggregateCheckEnabled ? aggregateOrgPos : aggregateLEPos;
                    FXMarketDataElement fxMde = fxMds.findSpotConversionMarketDataElement ( baseCcy, varCcy, true );

                    Map<IdcDate, Tuple<CreditUtilizationEvent,CreditUtilizationEvent>> cuesMap = createCreditUtilizationEvents ( cclr, cr, cpo, tradeDate, baseCcy, varCcy, fxMde, aggregatePos, datePosMap );
                    for ( IdcDate date : datePosMap.keySet() )
                    {
                        CreditUtilizationCacheEntry cce = getCreditUtilizationCacheEntry( cr.getLegalEntity(), cco, cc, CreditLimitConstants.RATE_QUALIFICATION_EVENT, date );
                        if ( cce == null || cce.getCreditUtilizations() == null )
                        {
                        	String alertMsg = "CLSM.checkAvailableCredit : cache entry is null or credit utils is null. cce=" + cce + ",cr=" + cr; 
                            log.info( alertMsg );
                            MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                            continue;

                        }
                        if ( cce.isCurrencyPairExempted( ccyPair ) )
                        {
                            break;
                        }

                        if ( !cce.validateCreditUtilizations () )
                        {
                            String alertMsg = new StringBuilder( 200 ).append( "CLSM.checkAvailableCredit : credit utilization not available. cpo=" )
                                    .append( cpo ).append( ",cc=" ).append( cc ).append( ",date=" ).append( date ).toString();
                            log.info( alertMsg );
                            MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                            return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_CREDIT_UTILIZATION_UNAVAILABLE );
                        }

                        Collection<CreditUtilization> col = cce.getCreditUtilizations();
                        for ( CreditUtilization cu : col )
                        {
                            final CreditLimitRule clr = cu.getCreditLimitRule ();
                            final CreditUtilizationCalculator calc = clr.getCreditUtilizationCalculator();
                            if ( calc == null || !clr.isEnabled() )
                            {
                                if ( log.isDebugEnabled() )
                                {
                                	String alertMsg = "CLSM.checkAvailableCredit : disabled credit methodology. clr=" + cu.getCreditLimitRule(); 
                                    log.debug( alertMsg );
                                    MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                                }
                                continue;
                            }

                            // handle aggregate type credit
                            final boolean daily = cu instanceof DailyCreditUtilization;
                            final CreditTenorProfile ctp = CreditUtilC.getCreditTenorProfile( cclr, clr, baseCcy, varCcy );
                            if ( !daily && !aggregateChecked )
                            {
                                aggregateChecked = true;
                                double requestedAmt = 0.0;
                                // for aggregate net receivable, net positions from each date needs to be added together.
                                if ( CreditLimitConstants.AGGREGATE_LIMIT_CALCULATOR.isSameAs( calc ) )
                                {
                                    if ( ctp != null )
                                    {
                                        Map<IdcDate, CurrencyPosition> posMap = aggregatePos.getPositions();
                                        for ( IdcDate posDate : posMap.keySet() )
                                        {
                                            double tc = CreditUtilC.getTenorCoefficient( cclr, clr, posDate, baseCcy, varCcy );
                                            if ( bypassTenorRestrictions && CreditLimit.TENOR_COEFFICIENT_NA == tc )
                                            {
                                                tc = CreditLimit.DEFAULT_TENOR_COEFFICIENT;
                                            }
                                            if ( tc == CreditLimit.TENOR_COEFFICIENT_NA )
                                            {
                                            	String alertMsg = "CLSM.checkAvailableCredit : tenor coefficient is not available for the date=" + posDate + ",cclr=" + cclr; 
                                                log.warn( alertMsg );
                                                MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                                                return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_TENOR_COEFFICIENT_NOT_SET_FOR_TENOR );
                                            }
                                            CurrencyPosition cp = posMap.get( posDate );
                                            Tuple<CreditUtilizationEvent, CreditUtilizationEvent> cueTpl = cuesMap.get (  posDate );
                                            CreditUtilizationEvent cue = cueTpl != null ? cueTpl.first : null;
                                            if ( cue != null )
                                            {
                                                cue.setPrincipal ( cue.getTradePrincipalAmount () * tc );
                                                cue.setPrice ( cue.getTradePriceAmount () * tc );
                                            }
                                            requestedAmt += Math.abs( cp.getNetAmount() * tc );
                                        }
                                    }
                                    else
                                    {
                                        requestedAmt = aggregatePos.getGrossNetAmount();
                                    }
                                }
                                else
                                {
                                    if ( ctp != null )
                                    {
                                        Map<IdcDate, CurrencyPosition> posMap = aggregatePos.getPositions();
                                        for ( IdcDate posDate : posMap.keySet() )
                                        {
                                            double tc = CreditUtilC.getTenorCoefficient( cclr, clr, posDate, baseCcy, varCcy );
                                            if ( bypassTenorRestrictions && CreditLimit.TENOR_COEFFICIENT_NA == tc )
                                            {
                                                tc = CreditLimit.DEFAULT_TENOR_COEFFICIENT;
                                            }
                                            if ( tc == CreditLimit.TENOR_COEFFICIENT_NA )
                                            {
                                            	String alertMsg = "CLSM.checkAvailableCredit : tenor coefficient is not available for the date=" + posDate + ",cclr=" + cclr; 
                                                log.warn( alertMsg );
                                                MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                                                return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_TENOR_COEFFICIENT_NOT_SET_FOR_TENOR );
                                            }
                                            CurrencyPosition cp = posMap.get( posDate );
                                            double cpAmt = cu.getCreditLimitRule().isAllowNetting() ? cp.getNetAmount() : cp.getGrossAmount();
                                            requestedAmt += cpAmt * tc;
                                            Tuple<CreditUtilizationEvent, CreditUtilizationEvent> cueTpl = cuesMap.get (  posDate );
                                            CreditUtilizationEvent cue = cueTpl != null ? cueTpl.first : null;
                                            if ( cue != null )
                                            {
                                                cue.setPrincipal ( cue.getTradePrincipalAmount () * tc );
                                                cue.setPrice ( cue.getTradePriceAmount () * tc );
                                            }
                                        }
                                    }
                                    else
                                    {
                                        requestedAmt = Math.abs( cu.getCreditLimitRule().isAllowNetting() ? aggregatePos.getNetAmount() : aggregatePos.getGrossAmount() );
                                    }
                                }

                                double realtimeAvailableAmt;
                                double convertedAmt = getBaseCurrencyAmount( Math.abs( requestedAmt ), aggregatePos.getCurrency(), cu.getCurrency(), fxMds );
                                if ( DealingPrice.TWO_WAY == bidOfferMode )
                                {
                                    realtimeAvailableAmt = clr.getCreditUtilizationCalculator().getRealtimeAvailableAmount( cu );
                                    boolean insufficientCredit = convertedAmt > realtimeAvailableAmt;
                                    if ( insufficientCredit )
                                    {
                                        // check whether a currency position breach if not integration mode.
                                        if ( !cu.isIntegrationMode ()  )
                                        {
                                            CurrencyPositionCollection cpsCopy = cu.getCurrencyPositions ().copy ( true );
                                            String cpsSnapshot = null;
                                            if ( log.isDebugEnabled () )
                                            {
                                                cpsSnapshot = cpsCopy.getPositions ();
                                            }

                                            int zeroPFE = 0;
                                            for ( Tuple<CreditUtilizationEvent, CreditUtilizationEvent> tpl: cuesMap.values () )
                                            {
                                                CreditUtilizationEvent aggCue = tpl.first;
                                                if ( aggCue != null )
                                                {
                                                    boolean zeroPFECue = aggCue.getPrincipal () < CreditLimit.MINIMUM && aggCue.getPrice () < CreditLimit.MINIMUM;
                                                    zeroPFE = zeroPFECue && zeroPFE != -1 ? 1 : -1;
                                                    cpsCopy.addCreditUtilizationEvent ( aggCue );
                                                }
                                            }

                                            // if all aggregate credit utilization events has zero PFE, then consider as having credit.
                                            if ( zeroPFE == 1 )
                                            {
                                                insufficientCredit = false;
                                            }
                                            else
                                            {

                                                boolean insufficientCreditOnOneSide = ! (clr.getCreditUtilizationCalculator ().isCreditAvailable ( cpsCopy, cu ));

                                                CurrencyPositionCollection cpsCopy1 = cu.getCurrencyPositions ().copy ( true );
                                                for ( Tuple<CreditUtilizationEvent, CreditUtilizationEvent> tpl : cuesMap.values () )
                                                {
                                                    // flip the buy sell flag to get the other side.
                                                    CreditUtilizationEvent cue = tpl.first;
                                                    if ( cue != null )
                                                    {
                                                        char origBuySell = cue.getBuySell ();
                                                        cue.setBuySell ( origBuySell == CreditLimit.BUY ? CreditLimit.SELL : CreditLimit.BUY );
                                                        cpsCopy1.addCreditUtilizationEvent ( cue );
                                                        cue.setBuySell ( origBuySell );
                                                    }
                                                }
                                                boolean insufficientCreditOnOtherSide = ! (clr.getCreditUtilizationCalculator ().isCreditAvailable ( cpsCopy1, cu ));
                                                if ( CreditConfigurationFactory.getCreditConfigurationMBean ().isTwoWayRFSWithOneSideOnlyAvailableEnabled () )
                                                {
                                                    insufficientCredit = insufficientCreditOnOneSide && insufficientCreditOnOtherSide;
                                                }
                                                else
                                                {
                                                    insufficientCredit = insufficientCreditOnOneSide || insufficientCreditOnOtherSide;
                                                }
                                                if ( log.isDebugEnabled () )
                                                {
                                                    log.debug ( "CLSM.checkAvailableCredit : aggregate ccy position check. pre="
                                                            + cpsSnapshot + ",post1=" + cpsCopy.getPositions ()
                                                            + ",post2=" + cpsCopy1.getPositions () + ",insufficientCredit="
                                                            + insufficientCredit + ",insufficentBid/Offer=" + insufficientCreditOnOneSide + "/" + insufficientCreditOnOtherSide );
                                                }
                                            }
                                        }
                                        if ( insufficientCredit && !allowExcessUtilizationAllowed )
                                        {
                                            String alertMsg = "CLSM.checkAvailableCredit : insufficient aggregate credit. availableAmt=" + realtimeAvailableAmt
                                                    + ",requestedAmt=" + convertedAmt + ",cpo" + cpo + ",cc=" + cc;
                                            log.info ( alertMsg );
                                            MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass ().getName (), alertMsg, null );
                                            return CreditUtilC.getCreditWorkflowMessageWithError ( null, CreditLimit.ERROR_INSUFFICIENT_CREDIT );
                                        }
                                        else
                                        {
                                            log.info ( "CLSM.checkAvailableCredit : aggregate ccy position check passed. requestedAmt=" + requestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt="
                                                    + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt + ",allowExcess=" + allowExcessUtilizationAllowed );
                                        }
                                    }
                                    else
                                    {
                                        log.info ( "CLSM.checkAvailableCredit : aggregate available amount check passed. requestedAmt=" + requestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt="
                                                + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt + ",allowExcess=" + allowExcessUtilizationAllowed );
                                    }
                                }
                                else
                                {
                                    realtimeAvailableAmt = clr.getCreditUtilizationCalculator().getRealtimeAvailableAmount( cu );
                                    boolean insufficientCredit = convertedAmt > realtimeAvailableAmt;
                                    if ( insufficientCredit )
                                    {
                                        // check whether not integration mode.
                                        if ( !cu.isIntegrationMode () )
                                        {
                                            CurrencyPositionCollection cpsCopy = cu.getCurrencyPositions ().copy ( true );
                                            String cpsSnapshot = null;
                                            if ( log.isDebugEnabled () )
                                            {
                                                cpsSnapshot = cpsCopy.getPositions ();
                                            }

                                            int zeroPFE = 0;
                                            for ( Tuple<CreditUtilizationEvent, CreditUtilizationEvent> tpl: cuesMap.values () )
                                            {
                                                CreditUtilizationEvent aggCue = tpl.first;
                                                if ( aggCue != null )
                                                {
                                                    boolean zeroPFECue = aggCue.getPrincipal () < CreditLimit.MINIMUM && aggCue.getPrice () < CreditLimit.MINIMUM;
                                                    zeroPFE = zeroPFECue && zeroPFE != -1 ? 1 : -1;
                                                    cpsCopy.addCreditUtilizationEvent ( aggCue );
                                                }
                                            }

                                            // if all aggregate credit utilization events has zero PFE, then consider as having credit.
                                            if ( zeroPFE == 1 )
                                            {
                                                insufficientCredit = false;
                                            }
                                            else
                                            {
                                                insufficientCredit = ! (clr.getCreditUtilizationCalculator ().isCreditAvailable ( cpsCopy, cu ));
                                                if ( log.isDebugEnabled () )
                                                {
                                                    log.debug ( "CLSM.checkAvailableCredit : aggregate ccy position check. pre="
                                                            + cpsSnapshot + ",post=" + cpsCopy.getPositions () + ",insufficientCredit=" + insufficientCredit );
                                                }
                                            }
                                        }
                                        if ( insufficientCredit && !allowExcessUtilizationAllowed )
                                        {
                                            String alertMsg = "CLSM.checkAvailableCredit : insufficient aggregate credit. available=" + realtimeAvailableAmt
                                                    + ",requestedAmt=" + convertedAmt + ",cpo" + cpo + ",cc=" + cc;
                                            log.info ( alertMsg );
                                            MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass ().getName (), alertMsg, null );
                                            return CreditUtilC.getCreditWorkflowMessageWithError ( null, CreditLimit.ERROR_INSUFFICIENT_CREDIT );
                                        }
                                        else
                                        {
                                            log.info ( "CLSM.checkAvailableCredit : aggregate ccy position check passed. requestedAmt=" + requestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt="
                                                    + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt + ",allowExcess=" + allowExcessUtilizationAllowed );
                                        }
                                    }
                                    else
                                    {
                                        log.info ( "CLSM.checkAvailableCredit : aggregate available amount check passed. requestedAmt=" + requestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt="
                                                + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt + ",allowExcess=" + allowExcessUtilizationAllowed );
                                    }
                                }
                            }

                            // handle daily
                            if ( daily )
                            {
                                double realtimeAvailableAmt;
                                CurrencyPosition dailyCps = datePosMap.get( date );
                                double signedRequestedAmt = clr.isAllowNetting() ? dailyCps.getNetAmount() : dailyCps.getGrossAmount();
                                if ( ctp != null && CreditUtilC.isApplyPFEOnDailyCredit( cclr ) )
                                {
                                    double tc = CreditUtilC.getTenorCoefficient( cclr, clr, date, baseCcy, varCcy );
                                    if ( bypassTenorRestrictions && CreditLimit.TENOR_COEFFICIENT_NA == tc )
                                    {
                                        tc = CreditLimit.DEFAULT_TENOR_COEFFICIENT;
                                    }
                                    if ( tc == CreditLimit.TENOR_COEFFICIENT_NA )
                                    {
                                    	String alertMsg = "CLSM.checkAvailableCredit : tenor coefficient is not available for the date=" + date + ",cclr=" + cclr;
                                        log.warn( alertMsg );
                                        MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                                        return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_TENOR_COEFFICIENT_NOT_SET_FOR_TENOR );
                                    }
                                    signedRequestedAmt = signedRequestedAmt * tc;
                                }
                                double convertedAmt = getBaseCurrencyAmount( Math.abs( signedRequestedAmt ), dailyCps.getCurrency(), cu.getCurrency(), fxMds );
                                if ( DealingPrice.TWO_WAY == bidOfferMode )
                                {
                                    realtimeAvailableAmt = clr.getCreditUtilizationCalculator().getRealtimeAvailableAmount( cu );
                                    boolean insufficientCredit = convertedAmt > realtimeAvailableAmt;
                                    if ( insufficientCredit && Math.abs( signedRequestedAmt ) > CreditLimit.MINIMUM )
                                    {
                                        // check whether a currency position breach if not integration mode.
                                        if ( !cu.isIntegrationMode ()  )
                                        {
                                            boolean insufficientCreditOnOneSide = true;
                                            boolean insufficientCreditOnOtherSide = true;
                                            CurrencyPositionCollection cpsCopy = cu.getCurrencyPositions ().copy ( true );
                                            CurrencyPositionCollection cpsCopy1 = null;
                                            String cpsSnapshot = null;
                                            if ( log.isDebugEnabled () )
                                            {
                                                cpsSnapshot = cpsCopy.getPositions ();
                                            }
                                            Tuple<CreditUtilizationEvent, CreditUtilizationEvent> tpl = cuesMap.get ( date );
                                            CreditUtilizationEvent cue = tpl != null ? tpl.second : null;
                                            if ( cue != null )
                                            {
                                                cpsCopy.addCreditUtilizationEvent ( cue );
                                                insufficientCreditOnOneSide = ! (clr.getCreditUtilizationCalculator ().isCreditAvailable ( cpsCopy, cu ));

                                                cpsCopy1 = cu.getCurrencyPositions ().copy ( true );
                                                // flip the buy sell flag to get the other side.
                                                char origBuySell = cue.getBuySell ();
                                                cue.setBuySell ( origBuySell == CreditLimit.BUY ? CreditLimit.SELL : CreditLimit.BUY );
                                                cpsCopy1.addCreditUtilizationEvent ( cue );
                                                cue.setBuySell ( origBuySell );

                                                insufficientCreditOnOtherSide = !(clr.getCreditUtilizationCalculator ().isCreditAvailable (  cpsCopy1, cu ) );
                                            }

                                            if ( CreditConfigurationFactory.getCreditConfigurationMBean().isTwoWayRFSWithOneSideOnlyAvailableEnabled() )
                                            {
                                                insufficientCredit = insufficientCreditOnOneSide && insufficientCreditOnOtherSide;
                                            }
                                            else
                                            {
                                                insufficientCredit = insufficientCreditOnOneSide || insufficientCreditOnOtherSide;
                                            }
                                            if ( log.isDebugEnabled () )
                                            {
                                                log.debug ( "CLSM.checkAvailableCredit : daily ccy position check. pre="
                                                        + cpsSnapshot + ",post1=" + cpsCopy.getPositions ()
                                                        + ",post2=" + cpsCopy1.getPositions () + ",insufficientCredit="
                                                        + insufficientCredit + ",insufficentBid/Offer=" + insufficientCreditOnOneSide + "/" + insufficientCreditOnOtherSide );
                                            }
                                        }
                                        if ( insufficientCredit && !allowExcessUtilizationAllowed )
                                        {
                                            String alertMsg = "CLSM.checkAvailableCredit : insufficient daily credit. availableAmt=" + realtimeAvailableAmt
                                                    + ",requestedAmt=" + convertedAmt + ",cpo" + cpo + ",cc=" + cc;
                                            log.info ( alertMsg );
                                            MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass ().getName (), alertMsg, null );
                                            return CreditUtilC.getCreditWorkflowMessageWithError ( null, CreditLimit.ERROR_INSUFFICIENT_CREDIT );
                                        }
                                        else
                                        {
                                            log.info ( "CLSM.checkAvailableCredit : daily ccy position check passed. requestedAmt=" + signedRequestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt="
                                                    + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt + ",date=" + date + ",allowExcess=" + allowExcessUtilizationAllowed );
                                        }
                                    }
                                    else
                                    {
                                        log.info ( "CLSM.checkAvailableCredit : daily available amount check passed. requestedAmt=" + signedRequestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt="
                                                + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt + ",date=" + date + ",allowExcess=" + allowExcessUtilizationAllowed );
                                    }
                                }
                                else
                                {
                                    realtimeAvailableAmt = clr.getCreditUtilizationCalculator().getRealtimeAvailableAmount( cu );
                                    boolean insufficientCredit = convertedAmt > realtimeAvailableAmt;
                                    if ( insufficientCredit && Math.abs( signedRequestedAmt ) > CreditLimit.MINIMUM )
                                    {
                                        // check whether not integration mode.
                                        if ( !cu.isIntegrationMode () )
                                        {
                                            CurrencyPositionCollection cpsCopy = cu.getCurrencyPositions ().copy ( true );
                                            String cpsSnapshot = null;
                                            if ( log.isDebugEnabled () )
                                            {
                                                cpsSnapshot = cpsCopy.getPositions ();
                                            }
                                            Tuple<CreditUtilizationEvent, CreditUtilizationEvent> tpl = cuesMap.get ( date );
                                            CreditUtilizationEvent cue = tpl != null ? tpl.second : null;
                                            if ( cue != null )
                                            {
                                                cue.setTradeDate ( cu.getCurrencyPositions ().getBaseDate () );
                                                cpsCopy.addCreditUtilizationEvent ( cue );
                                                insufficientCredit = ! (clr.getCreditUtilizationCalculator ().isCreditAvailable ( cpsCopy, cu ));
                                                if ( log.isDebugEnabled () )
                                                {
                                                    log.debug ( "CLSM.checkAvailableCredit : daily ccy position check. pre="
                                                            + cpsSnapshot + ",post=" + cpsCopy.getPositions () + ",insufficientCredit=" + insufficientCredit  );
                                                }
                                            }
                                        }
                                        if ( insufficientCredit && !allowExcessUtilizationAllowed )
                                        {
                                            String alertMsg = "CLSM.checkAvailableCredit : insufficient daily credit. available=" + realtimeAvailableAmt
                                                    + ",requestedAmt=" + convertedAmt + ",cpo" + cpo + ",cc=" + cc + ",date=" + date;
                                            log.info ( alertMsg );
                                            MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass ().getName (), alertMsg, null );
                                            return CreditUtilC.getCreditWorkflowMessageWithError ( null, CreditLimit.ERROR_INSUFFICIENT_CREDIT );
                                        }
                                        else
                                        {
                                            log.info ( "CLSM.checkAvailableCredit : daily ccy position check passed. requestedAmt=" + signedRequestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt="
                                                    + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt + ",date=" + date + ",allowExcess=" + allowExcessUtilizationAllowed );
                                        }
                                    }
                                    else
                                    {
                                        log.info ( "CLSM.checkAvailableCredit : daily available amount check passed. requestedAmt=" + signedRequestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt="
                                                + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt + ",date=" + date + ",allowExcess=" + allowExcessUtilizationAllowed );
                                    }
                                }
                            }
                        }
                    }
                }
                else if ( log.isDebugEnabled() )
                {
                    log.debug ( "CLSM.checkAvailableCredit : credit is not enabled between cpo=" + cpo + " and cc=" + cc );
                }
            }
        }
        else if ( log.isDebugEnabled() )
        {
            log.debug ( "CLSM.checkAvailableCredit : no credit relations found between takerLe=" + takerLe + " and makerLe=" + makerLe );
        }
        return null;
    }

    /**
     * This method checks credit for a given portfolio at LE level and and value date level. Check is made for aggregate or net level.
     *
     * @param np netting portfolio
     * @return cwm
     */
    public CreditWorkflowMessage checkPortfolioCreditAndMarkExcluded( NettingPortfolio np )
    {
        Collection<Organization> orgs = np.getToOrganizations();
        if ( orgs == null || orgs.isEmpty() )
        {
            return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_PORTFOLIO_TO_ORGANIZATIONS_NOT_SET );
        }

        // iterate through the batch requests and add the total amounts on a value date basis.
        Collection<NettingTradeRequest> tradeRequests = np.getInputRequestPojos();
        if ( tradeRequests == null || tradeRequests.isEmpty() )
        {
            return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_PORTFOLIO_NO_TRADE_REQUESTS );
        }
        Map<LegalEntity, Map<IdcDate, Map<Currency, CurrencyPosition>>> netRequestsLEMap = new HashMap<LegalEntity, Map<IdcDate, Map<Currency, CurrencyPosition>>>( tradeRequests.size() );
        Map<LegalEntity, Map<Currency, AggregateCurrencyPosition>> netPosLEMap = new HashMap<LegalEntity, Map<Currency, AggregateCurrencyPosition>>( tradeRequests.size() );
        Map<Organization, Map<IdcDate, Map<Currency, CurrencyPosition>>> netRequestsOrgMap = new HashMap<Organization, Map<IdcDate, Map<Currency, CurrencyPosition>>>( tradeRequests.size() );
        Map<Organization, Map<Currency, AggregateCurrencyPosition>> netPosOrgMap = new HashMap<Organization, Map<Currency, AggregateCurrencyPosition>>( tradeRequests.size() );

        Map<LegalEntity, Map<IdcDate, Set<CurrencyPair>>> takerLe2ValueDate2CcyPairSet = new HashMap<LegalEntity, Map<IdcDate, Set<CurrencyPair>>>();
        for ( NettingTradeRequest ntr : np.getInputRequestPojos() )
        {
            // check for excluded trade requests.
            if ( ntr.isExcluded() )
            {
                continue;
            }

            LegalEntity takerLe = ( LegalEntity ) ntr.getFund();
            Map<IdcDate, Map<Currency, CurrencyPosition>> dateLEMap = netRequestsLEMap.get( takerLe );
            if ( dateLEMap == null )
            {
                dateLEMap = new HashMap<IdcDate, Map<Currency, CurrencyPosition>>();
                netRequestsLEMap.put( takerLe, dateLEMap );
            }

            Map<IdcDate, Map<Currency, CurrencyPosition>> dateOrgMap = netRequestsOrgMap.get( takerLe.getOrganization() );
            if ( dateOrgMap == null )
            {
                dateOrgMap = new HashMap<IdcDate, Map<Currency, CurrencyPosition>>();
                netRequestsOrgMap.put( takerLe.getOrganization(), dateOrgMap );
            }

            Map<Currency, AggregateCurrencyPosition> aggregateLECpsMap = netPosLEMap.get( takerLe );
            Map<Currency, AggregateCurrencyPosition> aggregateOrgCpsMap = netPosOrgMap.get( takerLe.getOrganization() );
            AggregateCurrencyPosition aggregateLECps, aggregateOrgCps;

            Map<IdcDate, Set<CurrencyPair>> valueDate2CcyPairSet = takerLe2ValueDate2CcyPairSet.get(takerLe);
            if(valueDate2CcyPairSet == null)
            {
            	valueDate2CcyPairSet = new HashMap<IdcDate, Set<CurrencyPair>>();
            	takerLe2ValueDate2CcyPairSet.put(takerLe, valueDate2CcyPairSet);
            }

            for ( TradeRequestLeg trl : ntr.getTradeLegs() )
            {
                IdcDate valDate = trl.getValueDate();
                Set<CurrencyPair> currencyPairsForValueDate = valueDate2CcyPairSet.get(valDate);
                if(currencyPairsForValueDate == null)
                {
                	currencyPairsForValueDate = new HashSet<CurrencyPair>();
                	valueDate2CcyPairSet.put(valDate, currencyPairsForValueDate);
                }
                currencyPairsForValueDate.add( CurrencyFactory.getCurrencyPair( trl.getBaseCurrency(), trl.getTermCurrency() ) );

                Map<Currency, CurrencyPosition> leCpsMap = dateLEMap.get( valDate );
                CurrencyPosition leCps;
                if ( leCpsMap == null )
                {
                    leCpsMap = new HashMap<Currency, CurrencyPosition>();
                    leCps = new CurrencyPositionC( trl.getDealtCurrency() );
                    leCpsMap.put( leCps.getCurrency(), leCps );
                    dateLEMap.put( valDate, leCpsMap );
                }
                else
                {
                    leCps = leCpsMap.get( trl.getDealtCurrency() );
                    if ( leCps == null )
                    {
                        leCps = new CurrencyPositionC( trl.getDealtCurrency() );
                        leCpsMap.put( leCps.getCurrency(), leCps );
                    }
                }
                leCps.addAmount( trl.isBuyingDealt() ? trl.getDealtAmount() : -trl.getDealtAmount() );

                // populate the aggregate amounts.
                if ( aggregateLECpsMap == null )
                {
                    aggregateLECpsMap = new HashMap<Currency, AggregateCurrencyPosition>();
                    aggregateLECps = new AggregateCurrencyPositionC( trl.getDealtCurrency(), true );
                    aggregateLECpsMap.put( aggregateLECps.getCurrency(), aggregateLECps );
                    netPosLEMap.put( takerLe, aggregateLECpsMap );
                }
                else
                {
                    aggregateLECps = aggregateLECpsMap.get( trl.getDealtCurrency() );
                    if ( aggregateLECps == null )
                    {
                        aggregateLECps = new AggregateCurrencyPositionC( trl.getDealtCurrency(), true );
                        aggregateLECpsMap.put( aggregateLECps.getCurrency(), aggregateLECps );
                    }
                }
                aggregateLECps.addAmount( valDate, trl.isBuyingDealt() ? trl.getDealtAmount() : -trl.getDealtAmount() );

                // handle org level aggregate positions
                Map<Currency, CurrencyPosition> orgCpsMap = dateOrgMap.get( valDate );
                CurrencyPosition orgCps;
                if ( orgCpsMap == null )
                {
                    orgCpsMap = new HashMap<Currency, CurrencyPosition>();
                    orgCps = new CurrencyPositionC( trl.getDealtCurrency() );
                    orgCpsMap.put( orgCps.getCurrency(), orgCps );
                    dateOrgMap.put( valDate, orgCpsMap );
                }
                else
                {
                    orgCps = orgCpsMap.get( trl.getDealtCurrency() );
                    if ( orgCps == null )
                    {
                        orgCps = new CurrencyPositionC( trl.getDealtCurrency() );
                        orgCpsMap.put( orgCps.getCurrency(), orgCps );
                    }
                }
                orgCps.addAmount( trl.isBuyingDealt() ? trl.getDealtAmount() : -trl.getDealtAmount() );

                // populate the aggregate amounts.
                if ( aggregateOrgCpsMap == null )
                {
                    aggregateOrgCpsMap = new HashMap<Currency, AggregateCurrencyPosition>();
                    aggregateOrgCps = new AggregateCurrencyPositionC( trl.getDealtCurrency(), true );
                    aggregateOrgCpsMap.put( aggregateOrgCps.getCurrency(), aggregateOrgCps );
                    netPosOrgMap.put( takerLe.getOrganization(), aggregateOrgCpsMap );
                }
                else
                {
                    aggregateOrgCps = aggregateOrgCpsMap.get( trl.getDealtCurrency() );
                    if ( aggregateOrgCps == null )
                    {
                        aggregateOrgCps = new AggregateCurrencyPositionC( trl.getDealtCurrency(), true );
                        aggregateOrgCpsMap.put( aggregateOrgCps.getCurrency(), aggregateOrgCps );
                    }
                }
                aggregateOrgCps.addAmount( valDate, trl.isBuyingDealt() ? trl.getDealtAmount() : -trl.getDealtAmount() );
            }
        }

        for ( Organization org : orgs )//CC or LP orgs
        {
            Object result;
            final LegalEntity makerLe = org.getDefaultDealingEntity();
            for ( LegalEntity takerLe : netRequestsLEMap.keySet() )
            {
                // iterate through the taker legal entities and check credit between taker legal entity and maker legal entity.
                result = checkAvailableCreditV2( takerLe, makerLe, netPosLEMap.get( takerLe ), netRequestsLEMap.get( takerLe ), takerLe2ValueDate2CcyPairSet.get(takerLe), netPosOrgMap.get( takerLe.getOrganization() ), netRequestsOrgMap.get( takerLe.getOrganization() ) );
                if ( result instanceof LegalEntity )
                {
                    excludeRequests( np, null, takerLe );
                }
                else if ( result instanceof Tuple ) // tuple holds collection of failed value dates and failed ccy pair value date map.
                {
                    Tuple tuple = ( Tuple ) result;
                    log.info ( "CLSM.checkPortfolioCreditAndMarkExcluded - value dates with insufficient credit and/or currency pair-value dates with min/max tenor issues. tuple.first="
                            + tuple.first + ",tuple.second=" + tuple.second + ",takerLE=" + takerLe + ",makerLE=" + makerLe );
                    Collection<IdcDate> failedDates = ( Collection<IdcDate> ) tuple.first;
                    if ( failedDates != null && !failedDates.isEmpty () )
                    {
                        for ( IdcDate valueDate : failedDates )
                        {
                            excludeRequests( np, valueDate, takerLe );
                        }
                    }

                    Map<CurrencyPair, Collection<IdcDate>> ccyPairDateMap = ( Map<CurrencyPair, Collection<IdcDate>> ) tuple.second;
                    if ( ccyPairDateMap != null && !ccyPairDateMap.isEmpty () )
                    {
                        for ( CurrencyPair ccyPair : ccyPairDateMap.keySet () )
                        {
                            Collection<IdcDate> valueDates = ccyPairDateMap.get ( ccyPair );
                            if ( valueDates != null )
                            {
                                for ( IdcDate valueDate : valueDates )
                                {
                                    excludeRequests ( np, valueDate, takerLe, ccyPair );
                                }
                            }
                        }
                    }
                }
            }

        }
        return null;
    }

    private CreditWorkflowMessage _checkCreditLineBasedPortfolioCredit( NettingPortfolio np, boolean useAcceptedRequests, boolean isAcceptance )
    {
        final Collection<Organization> orgs = np.getToOrganizations();
        if ( orgs == null || orgs.isEmpty() )
        {
            return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_PORTFOLIO_TO_ORGANIZATIONS_NOT_SET );
        }

        // iterate through the batch requests and add the total amounts on a value date basis.
        final Collection<NettingTradeRequest> tradeRequests = useAcceptedRequests ? np.getAcceptedRequests() : np.getInputRequestPojos();
        if ( tradeRequests == null || tradeRequests.isEmpty() )
        {
            return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_PORTFOLIO_NO_TRADE_REQUESTS );
        }

        boolean creditAvailable = false;
        String errorCode = null;
        for ( Organization org: orgs )
        {
            boolean creditAvailableForOrg = true;
            final Organization realLP = org.getRealLP();
            final LegalEntity makerLe = realLP != null ? realLP.getDefaultDealingEntity() : org.getDefaultDealingEntity();

            // aggregate through the netting trade requests and segregate them based on credit line.
            Map<String, CreditEntity> creditEntityMap = new HashMap<String, CreditEntity> (  );
            Map<String, Collection<NettingTradeRequest>> creditEntityNettingTradeRequestsMap = new HashMap<String, Collection<NettingTradeRequest>> (  );
            Map<LegalEntity, IdcDate> earliestDateMap = new HashMap<LegalEntity, IdcDate>( tradeRequests.size() );
            Map<LegalEntity, IdcDate> farthestDateMap = new HashMap<LegalEntity, IdcDate>( tradeRequests.size() );
            Set<LegalEntity> takerLeSet = new HashSet<LegalEntity>();
            Currency baseCcy = null;
            Currency varCcy = null;

            for ( NettingTradeRequest ntr : tradeRequests )
            {
                // check for excluded trade requests.
                if ( ntr.isExcluded () )
                {
                    continue;
                }

                LegalEntity takerLe = ( LegalEntity ) ntr.getFund ();
                takerLeSet.add ( takerLe );
                final Collection<CreditEntity> creditEntities = getCreditRelationsBetween2Les ( takerLe, makerLe );
                if ( creditEntities != null && ! creditEntities.isEmpty () )
                {
                    for ( CreditEntity ce : creditEntities )
                    {
                        String ceSig = getCreditLineSignature ( ce );
                        if ( ceSig != null )
                        {
                            creditEntityMap.put ( ceSig, ce );
                            Collection<NettingTradeRequest> ntrColl = creditEntityNettingTradeRequestsMap.get ( ceSig );
                            if ( ntrColl == null )
                            {
                                ntrColl = new ArrayList<NettingTradeRequest> ();
                                creditEntityNettingTradeRequestsMap.put ( ceSig, ntrColl );
                            }
                            ntrColl.add ( ntr );
                        }
                    }
                }

                for ( TradeRequestLeg trl : ntr.getTradeLegs() )
                {
                    if ( baseCcy == null || varCcy == null )
                    {
                        baseCcy = trl.getBaseCurrency();
                        varCcy = trl.getTermCurrency ();
                    }

                    // populate the earliest and farthest value date on legal entity level.
                    IdcDate valDate = trl.getValueDate ();
                    final IdcDate farthestDateForLE = farthestDateMap.get( takerLe );
                    if ( farthestDateForLE == null || valDate.isLaterThan(farthestDateForLE))
                    {
                        farthestDateMap.put( takerLe, valDate );
                    }

                    final IdcDate earliestDateForLE = earliestDateMap.get( takerLe );
                    if ( earliestDateForLE == null || valDate.isEarlierThan(earliestDateForLE))
                    {
                        earliestDateMap.put( takerLe, valDate );
                    }
                }
            }

            //check min/max tenor restrictions for the list of taker LEs with the maker Le.
            CreditWorkflowMessage cwm = checkMinMaxTenorRestrictions( takerLeSet, makerLe, baseCcy, varCcy, earliestDateMap, farthestDateMap );
            if ( cwm != null && MessageStatus.FAILURE.equals( cwm.getStatus() ) )
            {
                errorCode = cwm.getErrorCode();
                continue;
            }


            // now go through the credit lines and net trade requests that are involved with that credit line.
            if ( creditEntityNettingTradeRequestsMap.isEmpty () )
            {
                creditAvailable = true;
                break;
            }

            for ( String ceStr: creditEntityNettingTradeRequestsMap.keySet () )
            {
                Collection<NettingTradeRequest> ntrColl = creditEntityNettingTradeRequestsMap.get ( ceStr );
                if ( ntrColl == null || ntrColl.isEmpty () )
                {
                    continue;
                }
                CreditEntity ce = creditEntityMap.get ( ceStr );
                Map<IdcDate, CurrencyPosition> datePosMap = new HashMap<IdcDate, CurrencyPosition> ( tradeRequests.size () );
                AggregateCurrencyPosition aggregatePos = null;
                for ( NettingTradeRequest ntr : ntrColl )
                {
                    if ( ntr == null || ntr.getTradeLegs () == null || ntr.getTradeLegs ().isEmpty () )
                    {
                        continue;
                    }
                    for ( TradeRequestLeg trl : ntr.getTradeLegs () )
                    {
                        if ( baseCcy == null || varCcy == null )
                        {
                            baseCcy = trl.getBaseCurrency();
                            varCcy = trl.getTermCurrency ();
                        }
                        IdcDate valDate = trl.getValueDate ();

                        CurrencyPosition ccCps = datePosMap.get ( valDate );
                        if ( ccCps == null )
                        {
                            ccCps = new CurrencyPositionC ( trl.getDealtCurrency () );
                            datePosMap.put ( valDate, ccCps );
                        }
                        ccCps.addAmount ( ce.isMaker () ? ( trl.isBuyingDealt () ? trl.getDealtAmount () : - trl.getDealtAmount () ) : ( trl.isBuyingDealt () ? -trl.getDealtAmount () : trl.getDealtAmount () ) ) ;

                        // populate the aggregate amounts.
                        if ( aggregatePos == null )
                        {
                            aggregatePos = new AggregateCurrencyPositionC ( trl.getDealtCurrency (), true );
                        }
                        aggregatePos.addAmount ( valDate, ce.isMaker () ? ( trl.isBuyingDealt () ? trl.getDealtAmount () : - trl.getDealtAmount () ) : ( trl.isBuyingDealt () ? -trl.getDealtAmount () : trl.getDealtAmount () ) );
                    }
                }

                //check credit for the credit line considering all the relevant netting trade requests's aggregation of positions.
                CreditWorkflowMessage cwm1 = checkAvailableCreditForCreditLine( ce, aggregatePos, datePosMap, baseCcy, varCcy, DealingPrice.UNDEFINED, isAcceptance, false );
                if ( cwm1 != null && MessageStatus.FAILURE.equals( cwm1.getStatus() ) )
                {
                    creditAvailableForOrg = false;
                    errorCode = cwm1.getErrorCode();
                }
            }
            // if one org is successfully passed credit, then no need to check for other orgs.
            if ( creditAvailableForOrg )
            {
                creditAvailable = true;
                break;
            }
        }
        return creditAvailable ? null : CreditUtilC.getCreditWorkflowMessageWithError( null, errorCode != null ? errorCode : CreditLimit.ERROR_INSUFFICIENT_CREDIT );
    }

    private CreditWorkflowMessage checkAvailableCreditForCreditLine( final CreditEntity ce, final AggregateCurrencyPosition aggregatePos, final Map<IdcDate, CurrencyPosition> datePosMap,
                                                        Currency baseCcy, Currency varCcy, int bidOfferMode, boolean rejectOnSuspendedStatus, boolean bypassTenorRestrictions )
    {
        IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ();
        final CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair( baseCcy, varCcy );
        final Organization cpo = ce.getLegalEntity().getOrganization();
        final TradingParty cc = ce.getTradingParty();
        CreditUtilizationCache creditUtilizationCache = getCreditUtilizationCache();
        if ( creditUtilizationCache.isCreditEnabled(cpo, cc) )
        {
            if ( rejectOnSuspendedStatus && CreditLimitConstants.CREDIT_SUSPEND == getCreditUtilizationCache().getCreditStatus( cpo, cc ) )
            {
                String alertMsg = "CLSM.checkAvailableCreditForCreditLine : credit is suspended from credit provider org=" + cpo + " to counterparty=" + cc;
                log.info( alertMsg );
                MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_CREDIT_SUSPENDED );
            }

            // add minimum tenor validation
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, cc, true );
            final Organization cco = ce.getTradingParty().getLegalEntityOrganization();
            final FXMarketDataSet fxMds = ( FXMarketDataSet ) CreditUtilC.getMarketDataSet( cpo );
            boolean aggregateChecked = false;
            FXMarketDataElement fxMde = fxMds.findSpotConversionMarketDataElement ( baseCcy, varCcy, true );

            Map<IdcDate, Tuple<CreditUtilizationEvent,CreditUtilizationEvent>> cuesMap = createCreditUtilizationEvents ( cclr, ce, cpo, tradeDate, baseCcy, varCcy, fxMde, aggregatePos, datePosMap );
            for ( IdcDate date : datePosMap.keySet() )
            {
                CreditUtilizationCacheEntry cce = getCreditUtilizationCacheEntry( ce.getLegalEntity(), cco, cc, CreditLimitConstants.RATE_QUALIFICATION_EVENT, date );
                if ( cce == null || cce.getCreditUtilizations() == null )
                {
                    String alertMsg = "CLSM.checkAvailableCreditForCreditLine : cache entry is null or credit utils is null. cce=" + cce + ",cr=" + ce;
                    log.info( alertMsg );
                    MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                    continue;

                }
                if ( cce.isCurrencyPairExempted( ccyPair ) )
                {
                    break;
                }

                Collection<CreditUtilization> col = cce.getCreditUtilizations();
                for ( CreditUtilization cu : col )
                {
                    final CreditLimitRule clr = cu.getCreditLimitRule ();
                    final CreditUtilizationCalculator calc = clr.getCreditUtilizationCalculator();
                    if ( calc == null || !clr.isEnabled() )
                    {
                        if ( log.isDebugEnabled() )
                        {
                            String alertMsg = "CLSM.checkAvailableCreditForCreditLine : disabled credit methodology. clr=" + cu.getCreditLimitRule();
                            log.debug( alertMsg );
                            MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                        }
                        continue;
                    }

                    // handle aggregate type credit
                    final boolean daily = cu instanceof DailyCreditUtilization;
                    final CreditTenorProfile ctp = CreditUtilC.getCreditTenorProfile( cclr, clr, baseCcy, varCcy );
                    if ( !daily && !aggregateChecked )
                    {
                        aggregateChecked = true;
                        double requestedAmt = 0.0;
                        // for aggregate net receivable, net positions from each date needs to be added together.
                        if ( CreditLimitConstants.AGGREGATE_LIMIT_CALCULATOR.isSameAs( calc ) )
                        {
                            if ( ctp != null )
                            {
                                Map<IdcDate, CurrencyPosition> posMap = aggregatePos.getPositions();
                                for ( IdcDate posDate : posMap.keySet() )
                                {
                                    double tc = CreditUtilC.getTenorCoefficient( cclr, clr, posDate, baseCcy, varCcy );
                                    if ( bypassTenorRestrictions && CreditLimit.TENOR_COEFFICIENT_NA == tc )
                                    {
                                        tc = CreditLimit.DEFAULT_TENOR_COEFFICIENT;
                                    }
                                    if ( tc == CreditLimit.TENOR_COEFFICIENT_NA )
                                    {
                                        String alertMsg = "CLSM.checkAvailableCreditForCreditLine : tenor coefficient is not available for the date=" + posDate + ",cclr=" + cclr;
                                        log.warn( alertMsg );
                                        MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                                        return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_TENOR_COEFFICIENT_NOT_SET_FOR_TENOR );
                                    }
                                    CurrencyPosition cp = posMap.get( posDate );
                                    Tuple<CreditUtilizationEvent, CreditUtilizationEvent> cueTpl = cuesMap.get (  posDate );
                                    CreditUtilizationEvent cue = cueTpl != null ? cueTpl.first : null;
                                    if ( cue != null )
                                    {
                                        cue.setPrincipal ( cue.getTradePrincipalAmount () * tc );
                                        cue.setPrice ( cue.getTradePriceAmount () * tc );
                                    }
                                    requestedAmt += Math.abs( cp.getNetAmount() * tc );
                                }
                            }
                            else
                            {
                                requestedAmt = aggregatePos.getGrossNetAmount();
                            }
                        }
                        else
                        {
                            if ( ctp != null )
                            {
                                Map<IdcDate, CurrencyPosition> posMap = aggregatePos.getPositions();
                                for ( IdcDate posDate : posMap.keySet() )
                                {
                                    double tc = CreditUtilC.getTenorCoefficient( cclr, clr, posDate, baseCcy, varCcy );
                                    if ( bypassTenorRestrictions && CreditLimit.TENOR_COEFFICIENT_NA == tc )
                                    {
                                        tc = CreditLimit.DEFAULT_TENOR_COEFFICIENT;
                                    }
                                    if ( tc == CreditLimit.TENOR_COEFFICIENT_NA )
                                    {
                                        String alertMsg = "CLSM.checkAvailableCreditForCreditLine : tenor coefficient is not available for the date=" + posDate + ",cclr=" + cclr;
                                        log.warn( alertMsg );
                                        MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                                        return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_TENOR_COEFFICIENT_NOT_SET_FOR_TENOR );
                                    }
                                    CurrencyPosition cp = posMap.get( posDate );
                                    double cpAmt = cu.getCreditLimitRule().isAllowNetting() ? cp.getNetAmount() : cp.getGrossAmount();
                                    requestedAmt += cpAmt * tc;
                                    Tuple<CreditUtilizationEvent, CreditUtilizationEvent> cueTpl = cuesMap.get (  posDate );
                                    CreditUtilizationEvent cue = cueTpl != null ? cueTpl.first : null;
                                    if ( cue != null )
                                    {
                                        cue.setPrincipal ( cue.getTradePrincipalAmount () * tc );
                                        cue.setPrice ( cue.getTradePriceAmount () * tc );
                                    }
                                }
                            }
                            else
                            {
                                requestedAmt = Math.abs( cu.getCreditLimitRule().isAllowNetting() ? aggregatePos.getNetAmount() : aggregatePos.getGrossAmount() );
                            }
                        }

                        double realtimeAvailableAmt = 0.0;
                        double convertedAmt = getBaseCurrencyAmount( Math.abs( requestedAmt ), aggregatePos.getCurrency(), cu.getCurrency(), fxMds );
                        if ( DealingPrice.TWO_WAY == bidOfferMode )
                        {
                            realtimeAvailableAmt = cu.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeAvailableAmount( cu );
                            boolean insufficientCredit = convertedAmt > realtimeAvailableAmt;
                            if ( insufficientCredit )
                            {
                                // check whether a currency position breach if not integration mode.
                                if ( !cu.isIntegrationMode ()  )
                                {
                                    CurrencyPositionCollection cpsCopy = cu.getCurrencyPositions ().copy ( true );
                                    String cpsSnapshot = null;
                                    if ( log.isDebugEnabled () )
                                    {
                                        cpsSnapshot = cpsCopy.getPositions ();
                                    }

                                    int zeroPFE = 0;
                                    for ( Tuple<CreditUtilizationEvent, CreditUtilizationEvent> tpl: cuesMap.values () )
                                    {
                                        CreditUtilizationEvent aggCue = tpl.first;
                                        if ( aggCue != null )
                                        {
                                            boolean zeroPFECue = aggCue.getPrincipal () < CreditLimit.MINIMUM && aggCue.getPrice () < CreditLimit.MINIMUM;
                                            zeroPFE = zeroPFECue && zeroPFE != -1 ? 1 : -1;
                                            cpsCopy.addCreditUtilizationEvent ( aggCue );
                                        }
                                    }

                                    // if all aggregate credit utilization events has zero PFE, then consider as having credit.
                                    if ( zeroPFE == 1 )
                                    {
                                        insufficientCredit = false;
                                    }
                                    else
                                    {

                                        boolean insufficientCreditOnOneSide = ! (cu.getCreditLimitRule ().getCreditUtilizationCalculator ().isCreditAvailable ( cpsCopy, cu ));

                                        CurrencyPositionCollection cpsCopy1 = cu.getCurrencyPositions ().copy ( true );
                                        for ( Tuple<CreditUtilizationEvent, CreditUtilizationEvent> tpl : cuesMap.values () )
                                        {
                                            // flip the buy sell flag to get the other side.
                                            CreditUtilizationEvent cue = tpl.first;
                                            if ( cue != null )
                                            {
                                                char origBuySell = cue.getBuySell ();
                                                cue.setBuySell ( origBuySell == CreditLimit.BUY ? CreditLimit.SELL : CreditLimit.BUY );
                                                cpsCopy1.addCreditUtilizationEvent ( cue );
                                                cue.setBuySell ( origBuySell );
                                            }
                                        }
                                        boolean insufficientCreditOnOtherSide = ! (cu.getCreditLimitRule ().getCreditUtilizationCalculator ().isCreditAvailable ( cpsCopy1, cu ));
                                        if ( CreditConfigurationFactory.getCreditConfigurationMBean ().isTwoWayRFSWithOneSideOnlyAvailableEnabled () )
                                        {
                                            insufficientCredit = insufficientCreditOnOneSide && insufficientCreditOnOtherSide;
                                        }
                                        else
                                        {
                                            insufficientCredit = insufficientCreditOnOneSide || insufficientCreditOnOtherSide;
                                        }
                                        if ( log.isDebugEnabled () )
                                        {
                                            log.debug ( "CLSM.checkAvailableCreditForCreditLine : aggregate ccy position check. pre="
                                                    + cpsSnapshot + ",post1=" + cpsCopy.getPositions ()
                                                    + ",post2=" + cpsCopy1.getPositions () + ",insufficientCredit="
                                                    + insufficientCredit + ",insufficentBid/Offer=" + insufficientCreditOnOneSide + "/" + insufficientCreditOnOtherSide );
                                        }
                                    }
                                }
                                if ( insufficientCredit )
                                {
                                    String alertMsg = "CLSM.checkAvailableCreditForCreditLine : insufficient aggregate credit. availableAmt=" + realtimeAvailableAmt
                                            + ",requestedAmt=" + convertedAmt + ",cpo" + cpo + ",cc=" + cc;
                                    log.info ( alertMsg );
                                    MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass ().getName (), alertMsg, null );
                                    return CreditUtilC.getCreditWorkflowMessageWithError ( null, CreditLimit.ERROR_INSUFFICIENT_CREDIT );
                                }
                                else
                                {
                                    log.info ( "CLSM.checkAvailableCreditForCreditLine : aggregate ccy position check passed. requestedAmt=" + requestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt=" + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt );
                                }
                            }
                            else
                            {
                                log.info ( "CLSM.checkAvailableCreditForCreditLine : aggregate available amount check passed. requestedAmt=" + requestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt=" + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt );
                            }
                        }
                        else
                        {
                            realtimeAvailableAmt = cu.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeAvailableAmount( cu );
                            boolean insufficientCredit = convertedAmt > realtimeAvailableAmt;
                            if ( insufficientCredit )
                            {
                                // check whether not integration mode.
                                if ( !cu.isIntegrationMode () )
                                {
                                    CurrencyPositionCollection cpsCopy = cu.getCurrencyPositions ().copy ( true );
                                    String cpsSnapshot = null;
                                    if ( log.isDebugEnabled () )
                                    {
                                        cpsSnapshot = cpsCopy.getPositions ();
                                    }

                                    int zeroPFE = 0;
                                    for ( Tuple<CreditUtilizationEvent, CreditUtilizationEvent> tpl: cuesMap.values () )
                                    {
                                        CreditUtilizationEvent aggCue = tpl.first;
                                        if ( aggCue != null )
                                        {
                                            boolean zeroPFECue = aggCue.getPrincipal () < CreditLimit.MINIMUM && aggCue.getPrice () < CreditLimit.MINIMUM;
                                            zeroPFE = zeroPFECue && zeroPFE != -1 ? 1 : -1;
                                            cpsCopy.addCreditUtilizationEvent ( aggCue );
                                        }
                                    }

                                    // if all aggregate credit utilization events has zero PFE, then consider as having credit.
                                    if ( zeroPFE == 1 )
                                    {
                                        insufficientCredit = false;
                                    }
                                    else
                                    {
                                        insufficientCredit = ! (cu.getCreditLimitRule ().getCreditUtilizationCalculator ().isCreditAvailable ( cpsCopy, cu ));
                                        if ( log.isDebugEnabled () )
                                        {
                                            log.debug ( "CLSM.checkAvailableCreditForCreditLine : aggregate ccy position check. pre="
                                                    + cpsSnapshot + ",post=" + cpsCopy.getPositions () + ",insufficientCredit=" + insufficientCredit );
                                        }
                                    }
                                }
                                if ( insufficientCredit )
                                {
                                    String alertMsg = "CLSM.checkAvailableCreditForCreditLine : insufficient aggregate credit. available=" + realtimeAvailableAmt
                                            + ",requestedAmt=" + convertedAmt + ",cpo" + cpo + ",cc=" + cc;
                                    log.info ( alertMsg );
                                    MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass ().getName (), alertMsg, null );
                                    return CreditUtilC.getCreditWorkflowMessageWithError ( null, CreditLimit.ERROR_INSUFFICIENT_CREDIT );
                                }
                                else
                                {
                                    log.info ( "CLSM.checkAvailableCreditForCreditLine : aggregate ccy position check passed. requestedAmt=" + requestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt=" + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt );
                                }
                            }
                            else
                            {
                                log.info ( "CLSM.checkAvailableCreditForCreditLine : aggregate available amount check passed. requestedAmt=" + requestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt=" + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt );
                            }
                        }
                    }

                    // handle daily
                    if ( daily )
                    {
                        double realtimeAvailableAmt;
                        CurrencyPosition dailyCps = datePosMap.get( date );
                        double signedRequestedAmt = cu.getCreditLimitRule().isAllowNetting() ? dailyCps.getNetAmount() : dailyCps.getGrossAmount();
                        if ( ctp != null && CreditUtilC.isApplyPFEOnDailyCredit( cclr ) )
                        {
                            double tc = CreditUtilC.getTenorCoefficient( cclr, clr, date, baseCcy, varCcy );
                            if ( bypassTenorRestrictions && CreditLimit.TENOR_COEFFICIENT_NA == tc )
                            {
                                tc = CreditLimit.DEFAULT_TENOR_COEFFICIENT;
                            }
                            if ( tc == CreditLimit.TENOR_COEFFICIENT_NA )
                            {
                                String alertMsg = "CLSM.checkAvailableCreditForCreditLine : tenor coefficient is not available for the date=" + date + ",cclr=" + cclr;
                                log.warn( alertMsg );
                                MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                                return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_TENOR_COEFFICIENT_NOT_SET_FOR_TENOR );
                            }
                            signedRequestedAmt = signedRequestedAmt * tc;
                        }
                        double convertedAmt = getBaseCurrencyAmount( Math.abs( signedRequestedAmt ), dailyCps.getCurrency(), cu.getCurrency(), fxMds );
                        if ( DealingPrice.TWO_WAY == bidOfferMode )
                        {
                            realtimeAvailableAmt = cu.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeAvailableAmount( cu );
                            boolean insufficientCredit = convertedAmt > realtimeAvailableAmt;
                            if ( insufficientCredit && Math.abs( signedRequestedAmt ) > CreditLimit.MINIMUM )
                            {
                                // check whether a currency position breach if not integration mode.
                                if ( !cu.isIntegrationMode ()  )
                                {
                                    boolean insufficientCreditOnOneSide = true;
                                    boolean insufficientCreditOnOtherSide = true;
                                    CurrencyPositionCollection cpsCopy = cu.getCurrencyPositions ().copy ( true );
                                    CurrencyPositionCollection cpsCopy1 = null;
                                    String cpsSnapshot = null;
                                    if ( log.isDebugEnabled () )
                                    {
                                        cpsSnapshot = cpsCopy.getPositions ();
                                    }
                                    Tuple<CreditUtilizationEvent, CreditUtilizationEvent> tpl = cuesMap.get ( date );
                                    CreditUtilizationEvent cue = tpl != null ? tpl.second : null;
                                    if ( cue != null )
                                    {
                                        cpsCopy.addCreditUtilizationEvent ( cue );
                                        insufficientCreditOnOneSide = ! (clr.getCreditUtilizationCalculator ().isCreditAvailable ( cpsCopy, cu ));

                                        cpsCopy1 = cu.getCurrencyPositions ().copy ( true );
                                        // flip the buy sell flag to get the other side.
                                        char origBuySell = cue.getBuySell ();
                                        cue.setBuySell ( origBuySell == CreditLimit.BUY ? CreditLimit.SELL : CreditLimit.BUY );
                                        cpsCopy1.addCreditUtilizationEvent ( cue );
                                        cue.setBuySell ( origBuySell );

                                        insufficientCreditOnOtherSide = !(clr.getCreditUtilizationCalculator ().isCreditAvailable (  cpsCopy1, cu ) );
                                    }

                                    if ( CreditConfigurationFactory.getCreditConfigurationMBean().isTwoWayRFSWithOneSideOnlyAvailableEnabled() )
                                    {
                                        insufficientCredit = insufficientCreditOnOneSide && insufficientCreditOnOtherSide;
                                    }
                                    else
                                    {
                                        insufficientCredit = insufficientCreditOnOneSide || insufficientCreditOnOtherSide;
                                    }
                                    if ( log.isDebugEnabled () )
                                    {
                                        log.debug ( "CLSM.checkAvailableCreditForCreditLine : daily ccy position check. pre="
                                                + cpsSnapshot + ",post1=" + cpsCopy.getPositions ()
                                                + ",post2=" + cpsCopy1.getPositions () + ",insufficientCredit="
                                                + insufficientCredit + ",insufficentBid/Offer=" + insufficientCreditOnOneSide + "/" + insufficientCreditOnOtherSide );
                                    }
                                }
                                if ( insufficientCredit )
                                {
                                    String alertMsg = "CLSM.checkAvailableCreditForCreditLine : insufficient daily credit. availableAmt=" + realtimeAvailableAmt
                                            + ",requestedAmt=" + convertedAmt + ",cpo" + cpo + ",cc=" + cc;
                                    log.info ( alertMsg );
                                    MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass ().getName (), alertMsg, null );
                                    return CreditUtilC.getCreditWorkflowMessageWithError ( null, CreditLimit.ERROR_INSUFFICIENT_CREDIT );
                                }
                                else
                                {
                                    log.info ( "CLSM.checkAvailableCreditForCreditLine : daily ccy position check passed. requestedAmt=" + signedRequestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt=" + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt + ",date=" + date );
                                }
                            }
                            else
                            {
                                log.info ( "CLSM.checkAvailableCreditForCreditLine : daily available amount check passed. requestedAmt=" + signedRequestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt=" + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt + ",date=" + date );
                            }
                        }
                        else
                        {
                            realtimeAvailableAmt = clr.getCreditUtilizationCalculator().getRealtimeAvailableAmount( cu );
                            boolean insufficientCredit = convertedAmt > realtimeAvailableAmt;
                            if ( insufficientCredit && Math.abs( signedRequestedAmt ) > CreditLimit.MINIMUM )
                            {
                                // check whether not integration mode.
                                if ( !cu.isIntegrationMode () )
                                {
                                    CurrencyPositionCollection cpsCopy = cu.getCurrencyPositions ().copy ( true );
                                    String cpsSnapshot = null;
                                    if ( log.isDebugEnabled () )
                                    {
                                        cpsSnapshot = cpsCopy.getPositions ();
                                    }
                                    Tuple<CreditUtilizationEvent, CreditUtilizationEvent> tpl = cuesMap.get ( date );
                                    CreditUtilizationEvent cue = tpl != null ? tpl.second : null;
                                    if ( cue != null )
                                    {
                                        cue.setTradeDate ( cu.getCurrencyPositions ().getBaseDate () );
                                        cpsCopy.addCreditUtilizationEvent ( cue );
                                        insufficientCredit = ! (clr.getCreditUtilizationCalculator ().isCreditAvailable ( cpsCopy, cu ));
                                        if ( log.isDebugEnabled () )
                                        {
                                            log.debug ( "CLSM.checkAvailableCreditForCreditLine : daily ccy position check. pre="
                                                    + cpsSnapshot + ",post=" + cpsCopy.getPositions () + ",insufficientCredit=" + insufficientCredit  );
                                        }
                                    }
                                }
                                if ( insufficientCredit )
                                {
                                    String alertMsg = "CLSM.checkAvailableCreditForCreditLine : insufficient daily credit. available=" + realtimeAvailableAmt
                                            + ",requestedAmt=" + convertedAmt + ",cpo" + cpo + ",cc=" + cc + ",date=" + date;
                                    log.info ( alertMsg );
                                    MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass ().getName (), alertMsg, null );
                                    return CreditUtilC.getCreditWorkflowMessageWithError ( null, CreditLimit.ERROR_INSUFFICIENT_CREDIT );
                                }
                                else
                                {
                                    log.info ( "CLSM.checkAvailableCreditForCreditLine : daily ccy position check passed. requestedAmt=" + signedRequestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt=" + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt + ",date=" + date );
                                }
                            }
                            else
                            {
                                log.info ( "CLSM.checkAvailableCreditForCreditLine : daily available amount check passed. requestedAmt=" + signedRequestedAmt + ",boMode=" + bidOfferMode + ",convertedAmt=" + convertedAmt + ",realtimeAvailableAmt=" + realtimeAvailableAmt + ",date=" + date );
                            }
                        }
                    }
                }
            }
        }
        else if ( log.isDebugEnabled() )
        {
            log.debug ( "CLSM.checkAvailableCreditForCreditLine : credit is not enabled between cpo=" + cpo + " and cc=" + cc );
        }
        return null;
    }

    public CreditWorkflowMessage checkTradeAllocationsCredit( Trade origTrade, Collection<NettingTradeRequest> allocationTradeRequests )
    {
        if ( origTrade == null || allocationTradeRequests == null || allocationTradeRequests.isEmpty () )
        {
            return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_INSUFFICIENT_DATA );
        }

        boolean creditAvailable = true;
        String errorCode = null;
        final Organization org = origTrade.getCounterpartyB().getOrganization ();
        final Organization realLP = org.getRealLP();
        final LegalEntity makerLe = realLP != null ? realLP.getDefaultDealingEntity() : org.getDefaultDealingEntity();

        // aggregate through the netting trade requests and segregate them based on credit line.
        Map<String, CreditEntity> creditEntityMap = new HashMap<String, CreditEntity> (  );
        Map<String, Collection<NettingTradeRequest>> creditEntityNettingTradeRequestsMap = new HashMap<String, Collection<NettingTradeRequest>> (  );
        Map<LegalEntity, IdcDate> earliestDateMap = new HashMap<LegalEntity, IdcDate>( allocationTradeRequests.size() );
        Map<LegalEntity, IdcDate> farthestDateMap = new HashMap<LegalEntity, IdcDate>( allocationTradeRequests.size() );
        Set<LegalEntity> takerLeSet = new HashSet<LegalEntity>();
        Currency baseCcy = null;
        Currency varCcy = null;

        for ( NettingTradeRequest ntr : allocationTradeRequests )
        {
            LegalEntity takerLe = ( LegalEntity ) ntr.getFund ();
            takerLeSet.add ( takerLe );
            final Collection<CreditEntity> creditEntities = getCreditRelationsBetween2Les ( takerLe, makerLe );
            if ( creditEntities != null && ! creditEntities.isEmpty () )
            {
                for ( CreditEntity ce : creditEntities )
                {
                    String ceSig = getCreditLineSignature ( ce );
                    if ( ceSig != null )
                    {
                        creditEntityMap.put ( ceSig, ce );
                        Collection<NettingTradeRequest> ntrColl = creditEntityNettingTradeRequestsMap.get ( ceSig );
                        if ( ntrColl == null )
                        {
                            ntrColl = new ArrayList<NettingTradeRequest> ();
                            creditEntityNettingTradeRequestsMap.put ( ceSig, ntrColl );
                        }
                        ntrColl.add ( ntr );
                    }
                }
            }

            for ( TradeRequestLeg trl : ntr.getTradeLegs() )
            {
                if ( baseCcy == null || varCcy == null )
                {
                    baseCcy = trl.getBaseCurrency();
                    varCcy = trl.getTermCurrency ();
                }

                // populate the earliest and farthest value date on legal entity level.
                IdcDate valDate = trl.getValueDate ();
                final IdcDate farthestDateForLE = farthestDateMap.get( takerLe );
                if ( farthestDateForLE == null || valDate.isLaterThan(farthestDateForLE))
                {
                    farthestDateMap.put( takerLe, valDate );
                }

                final IdcDate earliestDateForLE = earliestDateMap.get( takerLe );
                if ( earliestDateForLE == null || valDate.isEarlierThan(earliestDateForLE))
                {
                    earliestDateMap.put( takerLe, valDate );
                }
            }
        }

        //check min/max tenor restrictions for the list of taker LEs with the maker Le.
        CreditWorkflowMessage cwm = checkMinMaxTenorRestrictions( takerLeSet, makerLe, baseCcy, varCcy, earliestDateMap, farthestDateMap );
        if ( cwm != null && MessageStatus.FAILURE.equals( cwm.getStatus() ) )
        {
            errorCode = cwm.getErrorCode();
            return CreditUtilC.getCreditWorkflowMessageWithError( null, errorCode );
        }


        // now go through the credit lines and net trade requests that are involved with that credit line.
        if ( creditEntityNettingTradeRequestsMap.isEmpty () )
        {
            return null;
        }

        for ( String ceStr: creditEntityNettingTradeRequestsMap.keySet () )
        {
            Collection<NettingTradeRequest> ntrColl = creditEntityNettingTradeRequestsMap.get ( ceStr );
            if ( ntrColl == null || ntrColl.isEmpty () )
            {
                continue;
            }
            CreditEntity ce = creditEntityMap.get ( ceStr );
            Map<IdcDate, CurrencyPosition> datePosMap = new HashMap<IdcDate, CurrencyPosition> ( allocationTradeRequests.size () );
            AggregateCurrencyPosition aggregatePos = null;
            for ( NettingTradeRequest ntr : ntrColl )
            {
                if ( ntr == null || ntr.getTradeLegs () == null || ntr.getTradeLegs ().isEmpty () )
                {
                    continue;
                }
                for ( TradeRequestLeg trl : ntr.getTradeLegs () )
                {
                    if ( baseCcy == null || varCcy == null )
                    {
                        baseCcy = trl.getBaseCurrency();
                        varCcy = trl.getTermCurrency ();
                    }
                    IdcDate valDate = trl.getValueDate ();

                    CurrencyPosition ccCps = datePosMap.get ( valDate );
                    if ( ccCps == null )
                    {
                        ccCps = new CurrencyPositionC ( trl.getDealtCurrency () );
                        datePosMap.put ( valDate, ccCps );
                    }
                    ccCps.addAmount ( ce.isMaker () ? ( trl.isBuyingDealt () ? trl.getDealtAmount () : - trl.getDealtAmount () ) : ( trl.isBuyingDealt () ? -trl.getDealtAmount () : trl.getDealtAmount () ) ) ;

                    // populate the aggregate amounts.
                    if ( aggregatePos == null )
                    {
                        aggregatePos = new AggregateCurrencyPositionC ( trl.getDealtCurrency (), true );
                    }
                    aggregatePos.addAmount ( valDate, ce.isMaker () ? ( trl.isBuyingDealt () ? trl.getDealtAmount () : - trl.getDealtAmount () ) : ( trl.isBuyingDealt () ? -trl.getDealtAmount () : trl.getDealtAmount () ) );
                }
            }

            //check credit for the credit line considering all the relevant netting trade requests's aggregation of positions.
            CreditWorkflowMessage cwm1 = checkTradeAllocationsAvailableCreditForCreditLine( origTrade, ce, aggregatePos, datePosMap, baseCcy, varCcy, DealingPrice.UNDEFINED, true, false );
            if ( cwm1 != null && MessageStatus.FAILURE.equals( cwm1.getStatus() ) )
            {
                creditAvailable = false;
                errorCode = cwm1.getErrorCode();
                break;
            }
        }
        return creditAvailable ? null : CreditUtilC.getCreditWorkflowMessageWithError( null, errorCode != null ? errorCode : CreditLimit.ERROR_INSUFFICIENT_CREDIT );
    }

    private CreditWorkflowMessage checkTradeAllocationsAvailableCreditForCreditLine( final Trade trade, final CreditEntity ce, final AggregateCurrencyPosition aggregatePos, final Map<IdcDate, CurrencyPosition> datePosMap,
                                                                     Currency baseCcy, Currency varCcy, int bidOfferMode, boolean rejectOnSuspendedStatus, boolean bypassTenorRestrictions )
    {
        IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ();
        final CurrencyPair ccyPair = CurrencyFactory.getCurrencyPair( baseCcy, varCcy );
        final Organization cpo = ce.getLegalEntity().getOrganization();
        final TradingParty cc = ce.getTradingParty();
        CreditUtilizationCache creditUtilizationCache = getCreditUtilizationCache();
        if ( creditUtilizationCache.isCreditEnabled(cpo, cc) )
        {
            if ( rejectOnSuspendedStatus && CreditLimitConstants.CREDIT_SUSPEND == getCreditUtilizationCache().getCreditStatus( cpo, cc ) )
            {
                String alertMsg = "CLSM.checkTradeAllocationsAvailableCreditForCreditLine : credit is suspended from credit provider org=" + cpo + " to counterparty=" + cc;
                log.info( alertMsg );
                MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_CREDIT_SUSPENDED );
            }

            // add minimum tenor validation
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, cc, true );
            final boolean isOrgLevelCredit = cclr.getTradingParty () == null;
            final Organization cco = ce.getTradingParty().getLegalEntityOrganization();
            boolean aggregateChecked = false;
            FXLeg fxLeg = ( FXLeg ) trade.getTradeLegs ().iterator ().next ();
            FXRate fxRate = fxLeg.getFXPayment ().getFXRate ();

            Set<CreditUtilizationEvent> removedCues = new HashSet<CreditUtilizationEvent> ();
            Map<IdcDate, Tuple<CreditUtilizationEvent,CreditUtilizationEvent>> cuesMap = createCreditUtilizationEvents ( cclr, ce, cpo, tradeDate, baseCcy, varCcy, fxRate, aggregatePos, datePosMap );
            for ( IdcDate date : datePosMap.keySet() )
            {
                CreditUtilizationCacheEntry cce = getCreditUtilizationCacheEntry( ce.getLegalEntity(), cco, cc, CreditLimitConstants.RATE_QUALIFICATION_EVENT, date );
                if ( cce == null || cce.getCreditUtilizations() == null )
                {
                    String alertMsg = "CLSM.checkTradeAllocationsAvailableCreditForCreditLine : cache entry is null or credit utils is null. cce=" + cce + ",cr=" + ce;
                    log.info( alertMsg );
                    MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                    continue;

                }
                if ( cce.isCurrencyPairExempted( ccyPair ) )
                {
                    break;
                }

                Collection<CreditUtilization> col = cce.getCreditUtilizations();
                for ( CreditUtilization cu : col )
                {
                    final CreditLimitRule clr = cu.getCreditLimitRule ();
                    final CreditUtilizationCalculator calc = clr.getCreditUtilizationCalculator();
                    if ( calc == null || !clr.isEnabled() )
                    {
                        if ( log.isDebugEnabled() )
                        {
                            String alertMsg = "CLSM.checkTradeAllocationsAvailableCreditForCreditLine : disabled credit methodology. clr=" + cu.getCreditLimitRule();
                            log.debug( alertMsg );
                            MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                        }
                        continue;
                    }

                    // handle aggregate type credit
                    final boolean daily = cu instanceof DailyCreditUtilization;
                    final CreditTenorProfile ctp = CreditUtilC.getCreditTenorProfile( cclr, clr, baseCcy, varCcy );
                    if ( !daily && !aggregateChecked )
                    {
                        aggregateChecked = true;
                        boolean insufficientCredit;
                        if ( ctp != null )
                        {
                            Map<IdcDate, CurrencyPosition> posMap = aggregatePos.getPositions ();
                            for ( IdcDate posDate : posMap.keySet () )
                            {
                                double tc = CreditUtilC.getTenorCoefficient ( cclr, clr, posDate, baseCcy, varCcy );
                                if ( bypassTenorRestrictions && CreditLimit.TENOR_COEFFICIENT_NA == tc )
                                {
                                    tc = CreditLimit.DEFAULT_TENOR_COEFFICIENT;
                                }
                                if ( tc == CreditLimit.TENOR_COEFFICIENT_NA )
                                {
                                    String alertMsg = "CLSM.checkTradeAllocationsAvailableCreditForCreditLine : tenor coefficient is not available for the date=" + posDate + ",cclr=" + cclr;
                                    log.warn ( alertMsg );
                                    MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass ().getName (), alertMsg, null );
                                    return CreditUtilC.getCreditWorkflowMessageWithError ( null, CreditLimit.ERROR_TENOR_COEFFICIENT_NOT_SET_FOR_TENOR );
                                }
                                Tuple<CreditUtilizationEvent, CreditUtilizationEvent> cueTpl = cuesMap.get ( posDate );
                                CreditUtilizationEvent cue = cueTpl != null ? cueTpl.first : null;
                                if ( cue != null )
                                {
                                    cue.setPrincipal ( cue.getTradePrincipalAmount () * tc );
                                    cue.setPrice ( cue.getTradePriceAmount () * tc );
                                }
                            }
                        }

                        if ( !cu.isIntegrationMode () )
                        {
                            CurrencyPositionCollection cpsCopy = cu.getCurrencyPositions ().copy ( true );
                            String cpsSnapshot = null;
                            if ( log.isDebugEnabled () )
                            {
                                cpsSnapshot = cpsCopy.getPositions ();
                            }

                            int zeroPFE = 0;
                            for ( Tuple<CreditUtilizationEvent, CreditUtilizationEvent> tpl : cuesMap.values () )
                            {
                                CreditUtilizationEvent aggCue = tpl.first;
                                if ( aggCue != null )
                                {
                                    boolean zeroPFECue = aggCue.getPrincipal () < CreditLimit.MINIMUM && aggCue.getPrice () < CreditLimit.MINIMUM;
                                    zeroPFE = zeroPFECue && zeroPFE != -1 ? 1 : -1;
                                    cpsCopy.addCreditUtilizationEvent ( aggCue );
                                }
                            }

                            CreditLimitWorkflowState clws = isOrgLevelCredit ? CreditLimitWorkflowStateUtilC.getCreditLimitWorkflowState( trade, cpo, cco )
                                    : CreditLimitWorkflowStateUtilC.getCreditLimitWorkflowState( trade, cc );
                            if ( clws != null )
                            {
                                Collection<CreditUtilizationEvent> existingCues = clws.getCreditUtilizationEvents ();
                                if ( existingCues != null )
                                {
                                    for ( CreditUtilizationEvent existingCue: existingCues )
                                    {
                                        if ( existingCue.isActive () && !( existingCue.getCreditUtilization () instanceof DailyCreditUtilization ) )
                                        {
                                            if ( !removedCues.contains ( existingCue ) )
                                            {
                                                cpsCopy.removeCreditUtilizationEvent ( existingCue );
                                                removedCues.add ( existingCue );
                                            }
                                        }
                                    }
                                }
                            }

                            // if all aggregate credit utilization events has zero PFE, then consider as having credit.
                            if ( zeroPFE != 1 )
                            {
                                insufficientCredit = !(cu.getCreditLimitRule ().getCreditUtilizationCalculator ().isCreditAvailable ( cpsCopy, cu ));
                                if ( log.isDebugEnabled () )
                                {
                                    log.debug ( "CLSM.checkTradeAllocationsAvailableCreditForCreditLine : aggregate ccy position check. pre="
                                            + cpsSnapshot + ",post=" + cpsCopy.getPositions () + ",insufficientCredit=" + insufficientCredit );
                                }
                                if ( insufficientCredit )
                                {
                                    String alertMsg = "CLSM.checkTradeAllocationsAvailableCreditForCreditLine : insufficient aggregate credit. ce=" + ce + ",date=" + date;
                                    log.info ( alertMsg );
                                    MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass ().getName (), alertMsg, null );
                                    return CreditUtilC.getCreditWorkflowMessageWithError ( null, CreditLimit.ERROR_INSUFFICIENT_CREDIT );
                                }
                                else
                                {
                                    log.info ( "CLSM.checkTradeAllocationsAvailableCreditForCreditLine : aggregate ccy position check passed. boMode=" + bidOfferMode + ",date=" + date + ",ce=" + ce );
                                }
                            }
                        }
                    }

                    // handle daily
                    if ( daily )
                    {
                        if ( ctp != null && CreditUtilC.isApplyPFEOnDailyCredit( cclr ) )
                        {
                            double tc = CreditUtilC.getTenorCoefficient( cclr, clr, date, baseCcy, varCcy );
                            if ( bypassTenorRestrictions && CreditLimit.TENOR_COEFFICIENT_NA == tc )
                            {
                                tc = CreditLimit.DEFAULT_TENOR_COEFFICIENT;
                            }
                            if ( tc == CreditLimit.TENOR_COEFFICIENT_NA )
                            {
                                String alertMsg = "CLSM.checkTradeAllocationsAvailableCreditForCreditLine : tenor coefficient is not available for the date=" + date + ",cclr=" + cclr;
                                log.warn( alertMsg );
                                MessageLogger.getInstance().log(ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass().getName(), alertMsg, null);
                                return CreditUtilC.getCreditWorkflowMessageWithError( null, CreditLimit.ERROR_TENOR_COEFFICIENT_NOT_SET_FOR_TENOR );
                            }
                            Tuple<CreditUtilizationEvent, CreditUtilizationEvent> cueTpl = cuesMap.get ( date );
                            CreditUtilizationEvent dailyCue = cueTpl != null ? cueTpl.second : null;
                            if ( dailyCue != null )
                            {
                                dailyCue.setPrincipal ( dailyCue.getTradePrincipalAmount () * tc );
                                dailyCue.setPrice ( dailyCue.getTradePriceAmount () * tc );
                            }
                        }
                        boolean insufficientCredit = false;
                        // check whether not integration mode.
                        if ( !cu.isIntegrationMode () )
                        {
                            CurrencyPositionCollection cpsCopy = cu.getCurrencyPositions ().copy ( true );
                            String cpsSnapshot = null;
                            if ( log.isDebugEnabled () )
                            {
                                cpsSnapshot = cpsCopy.getPositions ();
                            }

                            CreditLimitWorkflowState clws = isOrgLevelCredit ? CreditLimitWorkflowStateUtilC.getCreditLimitWorkflowState ( trade, cpo, cco )
                                    : CreditLimitWorkflowStateUtilC.getCreditLimitWorkflowState( trade, cc );
                            if ( clws != null )
                            {
                                Collection<CreditUtilizationEvent> existingCues = clws.getCreditUtilizationEvents ();
                                if ( existingCues != null )
                                {
                                    for ( CreditUtilizationEvent existingCue: existingCues )
                                    {
                                        if ( existingCue.isActive () && existingCue.getCreditUtilization () instanceof DailyCreditUtilization )
                                        {
                                            if ( !removedCues.contains ( existingCue  ) )
                                            {
                                                cpsCopy.removeCreditUtilizationEvent ( existingCue );
                                                removedCues.add ( existingCue );
                                            }
                                        }
                                    }
                                }
                            }

                            Tuple<CreditUtilizationEvent, CreditUtilizationEvent> tpl = cuesMap.get ( date );
                            CreditUtilizationEvent cue = tpl != null ? tpl.second : null;
                            if ( cue != null )
                            {
                                cue.setTradeDate ( cu.getCurrencyPositions ().getBaseDate () );
                                cpsCopy.addCreditUtilizationEvent ( cue );
                                insufficientCredit = ! (clr.getCreditUtilizationCalculator ().isCreditAvailable ( cpsCopy, cu ));
                                if ( log.isDebugEnabled () )
                                {
                                    log.debug ( "CLSM.checkTradeAllocationsAvailableCreditForCreditLine : daily ccy position check. pre="
                                            + cpsSnapshot + ",post=" + cpsCopy.getPositions () + ",insufficientCredit=" + insufficientCredit  );
                                }
                            }
                        }
                        if ( insufficientCredit )
                        {
                            String alertMsg = "CLSM.checkTradeAllocationsAvailableCreditForCreditLine : insufficient daily credit. ce=" + ce + ",date=" + date;
                            log.info ( alertMsg );
                            MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass ().getName (), alertMsg, null );
                            return CreditUtilC.getCreditWorkflowMessageWithError ( null, CreditLimit.ERROR_INSUFFICIENT_CREDIT );
                        }
                        else
                        {
                            log.info ( "CLSM.checkTradeAllocationsAvailableCreditForCreditLine : daily ccy position check passed. boMode=" + bidOfferMode + ",date=" + date + ",ce=" + ce );
                        }
                    }
                }
            }
        }
        else if ( log.isDebugEnabled() )
        {
            log.debug ( "CLSM.checkTradeAllocationsAvailableCreditForCreditLine : credit is not enabled between cpo=" + cpo + " and cc=" + cc );
        }
        return null;
    }

    private void excludeRequests( NettingPortfolio portfolio, IdcDate date, LegalEntity le )
    {
        for ( NettingTradeRequest request : portfolio.getInputRequestPojos() )
        {
            if ( request.getFund().isSameAs( le ) )
            {
                if ( date != null )
                {
                    TradeRequestLeg leg = request.getTradeLegs( ISCommonConstants.SINGLE_LEG );
                    if ( leg.getValueDate().isSameAs( date ) )
                    {
                        request.setCreditAvailable( false );
                    }
                }
                else
                {
                    request.setCreditAvailable( false );
                }
            }
        }
    }

    private void excludeRequests( NettingPortfolio portfolio, IdcDate date, LegalEntity le, CurrencyPair ccyPair )
    {
        for ( NettingTradeRequest request : portfolio.getInputRequestPojos() )
        {
            if ( request.getFund().isSameAs( le ) )
            {
                if ( date != null )
                {
                    TradeRequestLeg leg = request.getTradeLegs( ISCommonConstants.SINGLE_LEG );
                    if ( leg.getValueDate().isSameAs( date ) && leg.getCurrencyPair ().equals ( ccyPair.getName () ) )
                    {
                        request.setCreditAvailable( false );
                    }
                }
            }
        }
    }

    /*
    * This version for checkAvailableCredit returns either Legal Entity or list of value dates for which credit is failing.
    * This will be used by RMM workflow to exclude some input requests.
    */
    private Object checkAvailableCreditV2( final LegalEntity takerLe, final LegalEntity makerLe, final Map<Currency, AggregateCurrencyPosition> aggregateLEPosMap, final Map<IdcDate, Map<Currency, CurrencyPosition>> datePosLEMap, Map<IdcDate, Set<CurrencyPair>> valDate2ccyPairs, final Map<Currency, AggregateCurrencyPosition> aggregateOrgPosMap, final Map<IdcDate, Map<Currency, CurrencyPosition>> datePosOrgMap )
    {
        final Collection<CreditEntity> creditRelationships = getCreditRelationsBetween2Les( takerLe, makerLe );
        Set<IdcDate> failedDates = new HashSet<IdcDate>( 4 );
        Map<CurrencyPair, Collection<IdcDate>> failedCcyPairDatesMap = new HashMap<CurrencyPair, Collection<IdcDate>> (  );
        if ( creditRelationships != null )
        {
            for ( CreditEntity cr : creditRelationships )
            {
                final Organization cpo = cr.getLegalEntity().getOrganization();
                final TradingParty cc = cr.getTradingParty();
                CreditUtilizationCache creditUtilizationCache = getCreditUtilizationCache();
                if ( getCreditUtilizationCache().isCreditEnabled( cpo, cc ) )
                {
                    if ( CreditLimitConstants.CREDIT_SUSPEND == getCreditUtilizationCache().getCreditStatus( cpo, cc ) )
                    {
                        log.info( "CLSM.checkAvailableCreditV2 : credit is suspended from credit provider org=" + cpo + " to counterparty=" + cc );
                        return takerLe;
                    }
                    CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, cc, true );
                    final Organization cco = cr.getTradingParty().getLegalEntityOrganization();
                    final FXMarketDataSet fxMds = ( FXMarketDataSet ) CreditUtilC.getMarketDataSet( cpo );
                    boolean aggregateChecked = false;
                    boolean orgLevelAggregateCheckEnabled = cco.isSameAs( takerLe.getOrganization() ) && cclr.isOrgLevel();
                    final Map<IdcDate, Map<Currency,CurrencyPosition>> datePosMap = orgLevelAggregateCheckEnabled ? datePosOrgMap : datePosLEMap;
                    final Map<Currency, AggregateCurrencyPosition> aggregatePosMap = orgLevelAggregateCheckEnabled ? aggregateOrgPosMap : aggregateLEPosMap;
                    for ( IdcDate date : datePosLEMap.keySet() )
                    {
                        CreditUtilizationCacheEntry cce = getCreditUtilizationCacheEntry( cr.getLegalEntity(), cco, cc, CreditLimitConstants.RATE_QUALIFICATION_EVENT, date );
                        Collection<CreditUtilization> col = cce.getCreditUtilizations();
                        for ( CreditUtilization cu : col )
                        {
                            if ( cu.getCreditLimitRule().getCreditUtilizationCalculator() == null || !cu.getCreditLimitRule().isEnabled() )
                            {
                                if ( log.isDebugEnabled() )
                                {
                                    log.debug( "CLSM.checkAvailableCreditV2 : disabled credit methodology. clr=" + cu.getCreditLimitRule() );
                                }
                                continue;
                            }

                            Set<CurrencyPair> ccyPairs = valDate2ccyPairs.get(date);
                            // add minimum/maximum tenor validation
                            CreditTenorRestriction minTenor = creditUtilizationCache.getMinimumTenor(cpo, cc); //CreditUtilC.getMinimumTenor( cclr );
                            if ( minTenor != null && date != null )
                            {
                                for ( CurrencyPair ccyPair: ccyPairs )
                                {
                                    IdcDate minTenorDate = CreditUtilC.getValueDateForTenor( ccyPair.getBaseCurrency(), ccyPair.getVariableCurrency(), minTenor );
                                    if ( date.isEarlierThan( minTenorDate ) )
                                    {
                                        log.info( new StringBuilder( 200 ).append( "CLSM.checkAvailableCreditV2 : minimum tenor validation failed. cpo=" )
                                                .append( cpo ).append( ",cc=" ).append( cc ).append( ",minTenor=" ).append( minTenor ).append( ",minTenorDate=" )
                                                .append( minTenorDate ).append( ",date=" ).append( date ).append(",ccyPair=").append( ccyPair ).toString() );

                                        Collection<IdcDate> dates = failedCcyPairDatesMap.get ( ccyPair );
                                        if ( dates == null )
                                        {
                                            dates = new HashSet<IdcDate>();
                                            failedCcyPairDatesMap.put ( ccyPair, dates );
                                        }
                                        dates.add( date );
                                    }
                                }
                            }

                            CreditTenorRestriction maxTenor = creditUtilizationCache.getMaximumTenor(cpo, cc);//CreditUtilC.getMaximumTenor( cclr );
                            if ( maxTenor != null && date != null )
                            {
                                for ( CurrencyPair ccyPair: ccyPairs )
                                {
                                    IdcDate maxTenorDate = CreditUtilC.getValueDateForTenor( ccyPair.getBaseCurrency(), ccyPair.getVariableCurrency(), maxTenor );
                                    if ( date.isLaterThan( maxTenorDate ) )
                                    {
                                        log.info( new StringBuilder( 200 ).append( "CLSM.checkAvailableCreditV2 : maximum tenor validation failed. cpo=" )
                                                .append( cpo ).append( ",cc=" ).append( cc ).append( ",maxTenor=" ).append( maxTenor ).append( ",maxTenorDate=" )
                                                .append( maxTenorDate ).append( ",date=" ).append( date ).append(",ccyPair=").append( ccyPair ).toString() );

                                        Collection<IdcDate> dates = failedCcyPairDatesMap.get ( ccyPair );
                                        if ( dates == null )
                                        {
                                            dates = new HashSet<IdcDate>();
                                            failedCcyPairDatesMap.put ( ccyPair, dates );
                                        }
                                        dates.add( date );
                                    }
                                }
                            }

                            //todo handle currency pair exemptions, tenor coefficients and currency pair level tenor coefficients.

                            // handle aggregate type credit. LE level credit check.
                            final boolean daily = cu instanceof DailyCreditUtilization;
                            if ( !daily && !aggregateChecked )
                            {
                                aggregateChecked = true;
                                double availableAmt = cu.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeAvailableAmount( cu );

                                double requestedAmtInLimitCcy = 0;
                                // for aggregate net receivable, net positions from each date needs to be added together.
                                if ( CreditLimitConstants.AGGREGATE_LIMIT_CALCULATOR.isSameAs( cu.getCreditLimitRule().getCreditUtilizationCalculator() ) )
                                {
                                    for ( AggregateCurrencyPosition acp: aggregatePosMap.values() )
                                    {
                                        requestedAmtInLimitCcy += getBaseCurrencyAmount(  acp.getGrossNetAmount(), acp.getCurrency(), cu.getCurrency(), fxMds );
                                    }
                                }
                                else
                                {
                                    for ( AggregateCurrencyPosition acp: aggregatePosMap.values() )
                                    {
                                        double dealtCcyPosAmt = Math.abs( cu.getCreditLimitRule().isAllowNetting() ? acp.getNetAmount() : acp.getGrossAmount());
                                        requestedAmtInLimitCcy += getBaseCurrencyAmount( dealtCcyPosAmt, acp.getCurrency(), cu.getCurrency(), fxMds  );
                                    }
                                }
                                if ( requestedAmtInLimitCcy > availableAmt )
                                {
                                    log.info( "CLSM.checkAvailableCreditV2 : insufficient aggregate credit. available=" + availableAmt
                                            + ",requestedAmtInLimitCcy=" + requestedAmtInLimitCcy + ",cpo" + cpo + ",cc=" + cc );
                                    return takerLe;
                                }
                            }

                            // handle daily
                            if ( daily )
                            {
                                double availableAmt = cu.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeAvailableAmount( cu );
                                Map<Currency, CurrencyPosition> dailyCpsMap = datePosMap.get( date );
                                double requestedAmtInLimitCcy = 0;
                                for ( CurrencyPosition dcp: dailyCpsMap.values() )
                                {
                                    double dealtCcyPosAmt = Math.abs(cu.getCreditLimitRule().isAllowNetting() ? dcp.getNetAmount() : dcp.getGrossAmount());
                                    requestedAmtInLimitCcy += getBaseCurrencyAmount( dealtCcyPosAmt, dcp.getCurrency(), cu.getCurrency(), fxMds );
                                }
                                if ( requestedAmtInLimitCcy > availableAmt )
                                {
                                    log.info( "CLSM.checkAvailableCreditV2 : insufficient daily credit. available=" + availableAmt
                                            + ",requestedAmtInLimitCcy=" + requestedAmtInLimitCcy + ",cpo" + cpo + ",cc=" + cc + ",date=" + date.getFormattedDate( IdcDate.DD_MMM_YYYY_HYPHEN ) );
                                    failedDates.add( date );
                                }
                            }
                        }
                    }
                }
            }
        }
        Tuple tuple = new Tuple();
        tuple.first = failedDates;
        tuple.second = failedCcyPairDatesMap;
        return tuple;
    }


    private DealingLimit applyTenorCoefficient( DealingLimit dl, double tc )
    {
        DealingLimit dealingLimit = dl;
        if ( dealingLimit != null )
        {
            if ( CreditLimit.TENOR_COEFFICIENT_NA == tc )
            {
                dealingLimit.setBidLimits( new double[]{0} );
                dealingLimit.setOfferLimits( new double[]{0} );
            }
            else if ( tc == CreditLimit.TENOR_COEFFICIENT_ZERO )
            {
                dealingLimit = null;
            }
            else if ( CreditLimit.DEFAULT_TENOR_COEFFICIENT != tc )
            {
                dealingLimit.setBidLimits( new double[]{dl.getBidLimit() / tc} );
                dealingLimit.setOfferLimits( new double[]{dl.getOfferLimit() / tc} );
            }
        }
        return dealingLimit;
    }

    private Map<IdcDate, Tuple<CreditUtilizationEvent,CreditUtilizationEvent>> createCreditUtilizationEvents
            ( CounterpartyCreditLimitRule cclr, CreditEntity cr, Organization cpo, IdcDate tradeDate, Currency baseCcy, Currency varCcy, FXMarketDataElement fxMde,
              AggregateCurrencyPosition aggregatePos, Map<IdcDate, CurrencyPosition> datePosMap )
    {
        FXRate fxRate = fxMde != null && fxMde.getFXPrice () != null  ? fxMde.getFXRate () : null;
        return createCreditUtilizationEvents ( cclr, cr, cpo, tradeDate, baseCcy, varCcy, fxRate, aggregatePos, datePosMap );
    }

    private Map<IdcDate, Tuple<CreditUtilizationEvent,CreditUtilizationEvent>> createCreditUtilizationEvents
            ( CounterpartyCreditLimitRule cclr, CreditEntity cr, Organization cpo, IdcDate tradeDate, Currency baseCcy, Currency varCcy, FXRate fxRate,
              AggregateCurrencyPosition aggregatePos, Map<IdcDate, CurrencyPosition> datePosMap )
    {
        Map<IdcDate, Tuple<CreditUtilizationEvent,CreditUtilizationEvent>> cuesMap = new HashMap<IdcDate, Tuple<CreditUtilizationEvent,CreditUtilizationEvent>> (  );
        Map<IdcDate, CurrencyPosition> map = aggregatePos.getPositions ();

        for ( IdcDate posDate : map.keySet () )
        {
            CreditUtilizationCacheEntry cce = getCreditUtilizationCacheEntry( cr.getLegalEntity(), cclr.getTradingPartyOrganization (), cr.getTradingParty (), CreditLimitConstants.RATE_QUALIFICATION_EVENT, posDate );
            if ( cce == null || cce.getCreditUtilizations () == null )
            {
                continue;
            }

            CreditUtilization aggCu = null, dailyCu = null;
            Collection<CreditUtilization> col = cce.getCreditUtilizations ();
            for ( CreditUtilization cu: col )
            {
                if ( cu instanceof DailyCreditUtilization )
                {
                    dailyCu = cu;
                }
                else
                {
                    aggCu = cu;
                }
            }

            CreditUtilizationEvent aggCue = null;
            CreditUtilizationEvent dailyCue = null;

            // create agg event.
            if ( aggCu != null )
            {
                CurrencyPosition aggPos = map.get ( posDate );
                aggCue = CreditLimitFactory.newCreditUtilizationEvent ();
                aggCue.setNamespace ( cpo.getNamespace () );
                aggCue.setCreditUtilization ( aggCu );

                aggCue.setPrincipalCurrency ( aggPos.getCurrency () );
                double aggPosAmt = aggCu.getCreditLimitRule ().isAllowNetting () ? aggPos.getNetAmount () : aggPos.getGrossAmount ();
                double principalAmt = Math.abs ( aggPosAmt );
                aggCue.setPrincipal ( principalAmt );
                aggCue.setTradePrincipalAmount ( principalAmt );

                Currency priceCcy = aggPos.getCurrency ().isSameAs ( baseCcy ) ? varCcy : baseCcy;
                aggCue.setPriceCurrency ( priceCcy );
                double priceAmt = fxRate != null ? fxRate.getAmount ( principalAmt, aggPos.getCurrency () ) : 0.0;
                aggCue.setPrice ( priceAmt );
                aggCue.setTradePriceAmount ( priceAmt );

                // trade position would be always from taker's perspective.
                aggCue.setBuySell ( cr.isMaker () ? (aggPosAmt >= 0.0 ? CreditLimit.SELL : CreditLimit.BUY) : (aggPosAmt >= 0.0 ? CreditLimit.BUY : CreditLimit.SELL) );
                aggCue.setSettlementDate ( posDate );
                aggCue.setTradeDate ( tradeDate );
            }

            // create the daily event
            if ( dailyCu != null )
            {
                CurrencyPosition dailyPos = datePosMap.get ( posDate );
                dailyCue = CreditLimitFactory.newCreditUtilizationEvent ();
                dailyCue.setNamespace ( cpo.getNamespace () );
                dailyCue.setCreditUtilization ( dailyCu );

                dailyCue.setPrincipalCurrency ( dailyPos.getCurrency () );
                double dailyPosAmt = dailyCu.getCreditLimitRule ().isAllowNetting () ? dailyPos.getNetAmount () : dailyPos.getGrossAmount ();
                double dailyPrincipalAmt = Math.abs ( dailyPosAmt );
                dailyCue.setPrincipal ( dailyPrincipalAmt );
                dailyCue.setTradePrincipalAmount ( dailyPrincipalAmt );

                Currency priceCcy = dailyPos.getCurrency ().isSameAs ( baseCcy ) ? varCcy : baseCcy;
                double dailyPriceAmt = fxRate != null ? fxRate.getAmount ( dailyPrincipalAmt, dailyPos.getCurrency () ) : 0.0;
                dailyCue.setPriceCurrency ( priceCcy );
                dailyCue.setPrice ( dailyPriceAmt );
                dailyCue.setTradePriceAmount ( dailyPriceAmt );

                // trade position would be always from taker's perspective.
                dailyCue.setBuySell ( cr.isMaker () ? ( dailyPosAmt >= 0.0 ? CreditLimit.SELL : CreditLimit.BUY) : (dailyPosAmt >= 0.0 ? CreditLimit.BUY : CreditLimit.SELL) );
                dailyCue.setSettlementDate ( posDate );
                dailyCue.setTradeDate ( tradeDate );
            }

            Tuple<CreditUtilizationEvent,CreditUtilizationEvent> cuesTpl = cuesMap.get ( posDate );
            if ( cuesTpl == null )
            {
                cuesTpl = new Tuple<CreditUtilizationEvent, CreditUtilizationEvent> ( aggCue, dailyCue );
            }
            cuesMap.put ( posDate, cuesTpl );
        }
        return cuesMap;
    }

    private String getCreditLineSignature( CreditEntity ce )
    {
        CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( ce.getLegalEntity ().getOrganization (), ce.getTradingParty (), true );
        if ( cclr != null )
        {
            return new StringBuilder( 200 ).append ( cclr.getNamespace ().getShortName () ).append ( ':' )
                    .append ( cclr.getTradingParty () != null ? cclr.getTradingParty ().getLegalEntity ().getFullyQualifiedName ()
                            : cclr.getTradingPartyOrganization ().getShortName () ).toString ();
        }
        return null;
    }

    private CreditWorkflowMessage checkMinMaxTenorRestrictions( final Collection<LegalEntity> takerLeSet, final LegalEntity makerLe,
                                                        Currency baseCcy, Currency varCcy, Map<LegalEntity, IdcDate> earliestDateMap, Map<LegalEntity, IdcDate> farthestDateMap )
    {
        for ( LegalEntity takerLe: takerLeSet )
        {
            final Collection<CreditEntity> creditRelationships = getCreditRelationsBetween2Les ( takerLe, makerLe );
            if ( creditRelationships != null && ! creditRelationships.isEmpty () )
            {
                for ( CreditEntity cr : creditRelationships )
                {
                    final Organization cpo = cr.getLegalEntity ().getOrganization ();
                    final TradingParty cc = cr.getTradingParty ();
                    CreditUtilizationCache creditUtilizationCache = getCreditUtilizationCache ();
                    if ( creditUtilizationCache.isCreditEnabled ( cpo, cc ) )
                    {
                        // add minimum tenor validation
                        CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( cpo, cc, true );
                        if ( cclr == null )
                        {
                            log.warn ( "CLSM.checkMinMaxTenorRestrictions : no active cclr for creditLine=" + cr );
                            continue;
                        }
                        CreditTenorRestriction minTenor = creditUtilizationCache.getMinimumTenor ( cpo, cc );// CreditUtilC.getMinimumTenor( cclr );
                        IdcDate earliestDate = earliestDateMap.get ( takerLe );
                        if ( minTenor != null && earliestDate != null )
                        {
                            IdcDate minTenorDate = CreditUtilC.getValueDateForTenor ( baseCcy, varCcy, minTenor );
                            if ( earliestDate.isEarlierThan ( minTenorDate ) )
                            {
                                String alertMsg = new StringBuilder ( 200 ).append ( "CLSM.checkMinMaxTenorRestrictions : minimum tenor validation failed. cpo=" )
                                        .append ( cpo ).append ( ",cc=" ).append ( cc ).append ( ",minTenor=" ).append ( minTenor ).append ( ",minTenorDate=" )
                                        .append ( minTenorDate ).append ( ",earliestDate=" ).append ( earliestDate ).toString ();
                                log.info ( alertMsg );
                                MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass ().getName (), alertMsg, null );
                                return CreditUtilC.getCreditWorkflowMessageWithError ( null, CreditLimit.ERROR_EXCEED_MINIMUM_TENOR );
                            }
                        }

                        CreditTenorRestriction maxTenor = creditUtilizationCache.getMaximumTenor ( cpo, cc );//CreditUtilC.getMaximumTenor( cclr );
                        IdcDate farthestDate = farthestDateMap.get ( takerLe );
                        if ( maxTenor != null && farthestDate != null )
                        {
                            IdcDate maxTenorDate = CreditUtilC.getValueDateForTenor ( baseCcy, varCcy, maxTenor );
                            if ( farthestDate.isLaterThan ( maxTenorDate ) )
                            {
                                String alertMsg = new StringBuilder ( 200 ).append ( "CLSM.checkMinMaxTenorRestrictions : maximum tenor validation failed. cpo=" )
                                        .append ( cpo ).append ( ",cc=" ).append ( cc ).append ( ",maxTenor=" ).append ( maxTenor ).append ( ",maxTenorDate=" )
                                        .append ( maxTenorDate ).append ( ",farthestDate=" ).append ( farthestDate ).toString ();
                                log.info ( alertMsg );
                                MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CHECK_AVAILABLE_CREDIT, this.getClass ().getName (), alertMsg, null );
                                return CreditUtilC.getCreditWorkflowMessageWithError ( null, CreditLimit.ERROR_EXCEED_MAXIMUM_TENOR );
                            }
                        }

                    }
                    else if ( log.isDebugEnabled () )
                    {
                        log.debug ( "CLSM.checkMinMaxTenorRestrictions : credit is not enabled between cpo=" + cpo + " and cc=" + cc );
                    }
                }
            }
            else if ( log.isDebugEnabled () )
            {
                log.debug ( "CLSM.checkMinMaxTenorRestrictions : no credit relations found between takerLe=" + takerLe + " and makerLe=" + makerLe );
            }
        }
        return null;
    }

}
