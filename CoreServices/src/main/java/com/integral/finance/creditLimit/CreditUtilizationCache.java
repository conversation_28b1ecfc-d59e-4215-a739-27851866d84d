package com.integral.finance.creditLimit;

// Copyright (c) 2001-2005 Integral Development Corp.  All rights reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.workflow.dealing.DealingLimitCollection;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * This interface is used to provide an expirable cache for credit utilizations. The cache can be used to assist in the
 * look-up of credit utilizations for trading party for a value date.
 *
 * <AUTHOR> Development Corp.
 */
public interface CreditUtilizationCache extends CreditUtilizationBaseCache
{

    /**
     * Returns the minimum tenor configured for the credit provider org and counterparty
     * @param cpo cpo
     * @param cc cc
     * @return minimum tenor
     */
    CreditTenorRestriction getMinimumTenor(  Organization cpo, TradingParty cc);

    /**
     * Returns the maximum tenor configured for the credit provider org and counterparty
     * @param cpo cpo
     * @param cc cc
     * @return maximum tenor
     */
    CreditTenorRestriction getMaximumTenor(  Organization cpo, TradingParty cc);

    CreditUtilization getCreditUtilizationFromCache(String cpoStr, String ccoStr, Long creditTpObjectId, IdcDate cuDate, boolean isAggCu);

    CreditUtilization getCreditUtilizationFromCache(String key, boolean isAggCu);

    /**
     * Returns the collection of the credit utilizations for the trading party and value date specified.
     *
     * @param creditProviderLe legal entity
     * @param creditTp counterparty
     * @param date date
     * @return collection of credit utilizations.
     */
    CreditUtilizationCacheEntry getCreditUtilizationCacheEntry( LegalEntity creditProviderLe, Organization ccOrg, TradingParty creditTp, IdcDate date );

    CreditUtilizationCacheEntry getCreditUtilizationCacheEntry( String cpo, String cco, Long creditTpObjectId, IdcDate date );

    /**
     * Adds a credit utilization cache entry which is a collection of credit utilizations and supported credit limit types
     *
     * @param creditProviderLe legal entity
     * @param creditCpty counterparty
     * @param date date
     * @param cce cache entry
     */
    void addCreditUtilizationCacheEntry( LegalEntity creditProviderLe, Organization ccOrg, TradingParty creditCpty, IdcDate date, CreditUtilizationCacheEntry cce );

    void addCreditUtilizationCacheEntry( Organization cpo, CounterpartyCreditLimitRule cclr, IdcDate date, Collection<CreditUtilization> creditUtils);

    /**
     * Removes all the credit utilization entries from the cache which belong to the credit provider organization
     * and meant for the credit counterparty.
     *
     * @param creditProviderOrg cpo
     * @param creditCpty cc
     */
    void removeTradingPartyCreditUtilizations( Organization creditProviderOrg, TradingParty creditCpty );

    /**
     * Removes all the credit utilization entries in the cache which belong to the credit provider organization and
     * are meant for credit counterparty organization.
     *
     * @param creditProviderOrg cpo
     * @param creditCptyOrg cco
     */
    void removeCounterpartyOrganizationCreditUtilizations( Organization creditProviderOrg, Organization creditCptyOrg );

    /**
     * Removes all credit utilization entries associated with a credit provider organization.
     *
     * @param creditProviderOrg cpo
     */
    void removeProviderOrganizationCreditUtilizations( Organization creditProviderOrg );

    /**
     * Removes all the credit utilizations from the cache.
     */
    void removeAllCreditUtilizations();

    /**
     * This method is used to flush out all the existing CUs and reload them afresh from Oracle.
     * It is called when CU Lookup source is changed from RDS to Oracle.
     * So that all RDS CUs are replaced with the corresponding Oracle entities
     *
     * @param cpoShortName short name of the credit provider organization
     */
    void flushAndReloadAllUtilizations(String cpoShortName);
    /**
     * Removes all obsolete cache entries. For example, it removes all the cache entries which has a date previous to the current business date.
     */
    void removeAllObsoleteCacheEntries();

    /**
     * Removes all the credit utilization entries in the cache which belong to the credit provider organization and
     * are meant for credit counterparty organization.
     *
     * @param creditProviderOrg provider
     * @param creditCptyOrg     cpty org
     */
    void removeCounterpartyOrganizationNonAggregateCreditUtilizations( Organization creditProviderOrg, Organization creditCptyOrg );


    /**
     * Resets all credit utilizations in the cache for the credit provider organization and credit counterparty combination. Used in utility jsp.
     *
     * @param creditProviderOrg cpo
     * @param creditCpty cc
     */
    void resetTradingPartyCreditUtilizations( Organization creditProviderOrg, TradingParty creditCpty );

    /**
     * Resets all credit utilizations in the cache for the credit provider organization and counterparty organization combination. Used in utility jsp.
     *
     * @param creditProviderOrg cpo
     * @param creditCptyOrg cc
     */
    void resetCounterpartyOrganizationCreditUtilizations( Organization creditProviderOrg, Organization creditCptyOrg );

    /**
     * Resets all credit utilizations in the cache for the credit provider organization. Used in utility jsp.
     *
     * @param creditProviderOrg cpo
     */
    void resetProviderOrganizationCreditUtilizations( Organization creditProviderOrg );

    /**
     * Resets all the credit utilizations in the cache by resetting the currency positions.
     */
    void resetAllCreditUtilizations();

    /**
     * Rebuilds all the credit utilizations in the cache by resetting the currency positions.
     */
    void rebuildAllCreditUtilizations();

    /**
     * Rebuilds all the credit utilizations in the cache in an optimized way by building from next date's position snapshot.
     * and marking the rest as stale.
     */
    void rebuildAllCreditUtilizationsOptimized();

    /**
     * Rebuilds all the credit utilizations in the cache by resetting the currency positions.
     *
     * @param delaySeed the seed for randomizing the delay in cache rebuilding in milliseconds.
     */
    void rebuildAllCreditUtilizationsWithDelay( long delaySeed );

    /**
     * Sets all the credit utilizations cache entries stale in the cache.
     */
    void setAllCreditUtilizationsStale();


    /**
     * Returns the size of credit utilization kept in the cache.
     *
     * @return cache size
     */
    int size();

    /**
     * Returns from cache whether credit is enabled or disabled for the credit provider org and credit cpty combination.
     *
     * @param cpo creditProviderOrg
     * @param cc  creditTp
     * @return enable
     */
    boolean isCreditEnabled( Organization cpo, TradingParty cc );

    boolean isCreditEnabled(int creditStatus);
    /**
     * Returns the credit status for the credit provider/cpty combination.
     *
     * @param cpo credit provider org
     * @param cc  credit counterparty
     * @return status
     */
    int getCreditStatus( Organization cpo, TradingParty cc );

    /**
     * Sets the credit enabled/disabled in the cache for credit provider organization and credit cpty combination. If credit tp is null then
     * all the trading parties cache content will be removed.
     *
     * @param cpo creditProviderOrg
     * @param cc  creditCpty
     */
    void resetCreditEnabled( Organization cpo, TradingParty cc );

    /**
     * Retrieves all credit utilizations maintained in the cache.
     *
     * @return credit utilization collection
     */
    Collection<CreditUtilization> getAllCreditUtilizations();

    /**
     * Returns a collection of all aggregate credit utilizations from the provider keyed cache entries. This can be either
     * only provider keyed cache entries or also include settlement date based cache entries of aggregate credit utilizations.
     *
     * @param onlyProviderCache only provider keyed cache entries
     * @return aggregate credit utilizations.
     */
    Collection<CreditUtilization> getAllAggregateCreditUtilizations( boolean onlyProviderCache );

    /**
     * Returns a list of currency pair which are subscribed for credit limit updates for the credit line provided by the credit provider
     * organization for the credit counterparty trading party or organization.
     *
     * @param creditProviderLe legal entity
     * @param creditCpty counterparty
     * @return currency pair list
     */
    DealingLimitCollection getCreditLimitSubscriptions( LegalEntity creditProviderLe, TradingParty creditCpty );

    /**
     * Adds the list of currency pairs for credit limit updates for the credit line provided by the credit provider
     * organization for the credit counterparty trading party or organization.
     *
     * @param creditProviderLe legal entity
     * @param creditCpty cc
     * @param creditCptyOrg cco
     * @param ccyPairs ccy pairs
     */
    void addCreditLimitSubscriptions( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, Collection<String> ccyPairs );

    /**
     * Adds the currency pair formed by the base currency and variable currency for credit limit updates for the credit line provided by the credit provider
     * organization for the credit counterparty trading party or organization.
     *
     * @param creditProviderLe legal entity
     * @param creditCpty cc
     * @param creditCptyOrg cco
     * @param baseCcy base ccy
     * @param varCcy var ccy
     */
    void addCreditLimitSubscription( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, Currency baseCcy, Currency varCcy );

    /**
     * Removes the currency pair formed by the base currency and variable currency for credit limit updates for the credit line provided by the credit provider
     * organization for the credit counterparty trading party or organization.
     *
     * @param creditProviderLe legal entity
     * @param creditCpty cc
     * @param creditCptyOrg cco
     * @param baseCcy base ccy
     * @param varCcy var ccy
     */
    void removeCreditLimitSubscription( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, Currency baseCcy, Currency varCcy );

    /**
     * Removes the list of currency pairs for credit limit updates for the credit line provided by the credit provider
     * organization for the credit counterparty trading party or organization. If the currency pair list is null, then all the currency pairs
     * are unsubscribed.
     *
     * @param creditProviderLe legal entity
     * @param creditCpty cc
     * @param creditCptyOrg cco
     * @param ccyPairs ccy pairs
     */
    void removeCreditLimitSubscriptions( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, Collection<String> ccyPairs );

    /**
     * Returns the map which has the credit limit subscription information. The returned map will not be modifiable.
     *
     * @return subscription map
     */
    Map<String, CreditRelationSubscriptionInfo> getCreditLimitSubscriptionMap();

    /**
     * Returns the map which stores the credit enable/disable information for the credit provider/credit counterparty combination.
     *
     * @return credit enable map
     */
    Map getCreditEnableMap();

    /**
     * Returns the aggregate credit utilization cache entry which contains the list of  aggregate credit utilizations.
     *
     * @param creditProviderOrg credit provider org
     * @return aggregate credit utilization entry
     */
    CreditUtilizationCacheEntry getAggregateCreditUtilizationCollectionCacheEntry( Organization creditProviderOrg );

    /**
     * Adds aggregate credit utilization cache entry.
     *
     * @param cacheEntry        aggregate credit utilizations
     * @param creditProviderOrg credit provider organization
     */
    void addAggregateCreditUtilizationCollectionCacheEntry( CreditUtilizationCacheEntry cacheEntry, Organization creditProviderOrg );

    CreditUtilizationCacheEntry getAggregateCreditUtilizationCacheEntry( Organization creditProviderOrg, Organization creditCounterpartyOrg, TradingParty creditTp);

    void addAggregateCreditUtilizationCacheEntry( IdcDate aDate, Collection<CreditUtilization> creditUtils, CounterpartyCreditLimitRule cclr, Organization creditProviderOrg);

    /**
     * Returns the list of credit utilization cache entries in the cache. used in utility jsps
     *
     * @return list
     */
    Collection<CreditUtilizationCacheEntry> getAllCreditUtilizationCacheEntries();

    /**
     * Rebuilds all credit utilizations in the cache for the credit provider organization and credit counterparty combination.
     *
     * @param creditProviderOrg provider
     * @param creditCpty        cpty
     * @param event             event
     */
    void rebuildTradingPartyCreditUtilizations( Organization creditProviderOrg, TradingParty creditCpty, String event );

    /**
     * Rebuilds all credit utilizations in the cache for the credit provider organization and counterparty organization combination.
     *
     * @param creditProviderOrg provider
     * @param creditCptyOrg     cpty org
     * @param event             event
     */
    void rebuildCounterpartyOrganizationCreditUtilizations( Organization creditProviderOrg, Organization creditCptyOrg, String event );

    /**
     * Rebuilds all credit utilizations in the cache for the credit provider organization.
     *
     * @param creditProviderOrg provider org
     * @param event             event
     */
    void rebuildProviderOrganizationCreditUtilizations( Organization creditProviderOrg, String event );

    /**
     * Rebuilds all the stale entries in the cache.
     */
    void rebuildAllStaleEntries();

    /**
     * Returns the active counterparty credit limit rule for credit provider org and credit counterparty.
     *
     * @param cpo credit provider org
     * @param cc  credit cpty
     * @return cclr
     */
    CounterpartyCreditLimitRule getActiveCounterpartyCreditLimitRule( Organization cpo, TradingParty cc );

    CreditRelationship getCreditRelationship( Organization cpo, TradingParty cc );

    /**
     * Returns the org level counterparty credit limit rule for the credit provider and credit counterparty
     *
     * @param cpo credit provider org
     * @param cc  credit cpty
     * @return cclr
     */
    CounterpartyCreditLimitRule getOrgLevelCounterpartyCreditLimitRule( Organization cpo, TradingParty cc );

    /**
     * Re-initializes the cpty rules for credit provider.
     *
     * @param cpo credit provider org
     */
    void resetCreditProviderCptyRules( Organization cpo );

    /**
     * Re-initializes the cpty rules for credit provider and credit cpty org combination.
     *
     * @param cpo credit provider org
     * @param cco credit cpty org
     */
    void resetCreditCounterpartyOrgCptyRules( Organization cpo, Organization cco );

    /**
     * Re-initializes the cpty rules for credit provider and credit cpty combination.
     *
     * @param cpo credit provider org
     * @param cc  credit counterparty
     */
    void resetCreditCounterpartyCptyRules( Organization cpo, TradingParty cc );


    /**
     * Re-initializes the cpty rules for credit provider and all credit cpty combination for tenor changes
     * @param cpo cpo
     */
    void resetCreditCounterpartyCptyTenor( Organization cpo );

    /**
     * Re-initializes the cpty rules for credit provider and credit cpty combination for tenor changes
     * @param cpo cpo
     * @param cco cco
     */
    void resetCreditCounterpartyCptyTenor( Organization cpo, Organization cco );

    /**
     * Re-initializes the cpty rules for credit provider and credit cpty combination for tenor changes
     * @param cpo cpo
     * @param cc cc
     */
    void resetCreditCounterpartyCptyTenor( Organization cpo, TradingParty cc );

    /**
     * Clears all the subscription data including dealing limits and subscriptions.
     */
    void clearAllSubscriptionData();

    /**
     * Updates exemption currency pair list for the credit provider organization and credit counterparty combination.
     *
     * @param cpo provider
     * @param cc  credit counterparty
     */
    void updateTradingPartyCreditExemptionList( Organization cpo, TradingParty cc );

    /**
     * Updates the cache for exemption currency pair list for the credit provider organization and counterparty organization combination.
     *
     * @param cpo provider
     * @param cco counterparty org
     */
    void updateCounterpartyOrganizationCreditExemptionList( Organization cpo, Organization cco );

    /**
     * Updates the cache for exemption currency pair list for the credit provider organization.
     *
     * @param cpo provider org
     */
    void updateProviderOrganizationCreditExemptionList( Organization cpo );

    /**
     * Updates all the cache entries with the exemption currency pair list for the currency pair group.
     *
     * @param cpg currency pair group
     */
    void updateCreditExemptionList( CurrencyPairGroup cpg );

    /**
     * Updates credit tenor profile for the credit provider organization and credit counterparty combination.
     *
     * @param cpo provider
     * @param cc  credit counterparty
     */
    void updateTradingPartyCreditTenorProfile( Organization cpo, TradingParty cc );

    /**
     * Updates the cache for tenor profile for the credit provider organization and counterparty organization combination.
     *
     * @param cpo provider
     * @param cco counterparty org
     */
    void updateCounterpartyOrganizationCreditTenorProfile( Organization cpo, Organization cco );

    /**
     * Updates the cache for tenor profile for the credit provider organization.
     *
     * @param cpo provider org
     */
    void updateProviderOrganizationCreditTenorProfile( Organization cpo );

    /**
     * Updates all the credit tenor coefficients for all cache entries. This will be used end of day to re-calculate the tenor coefficients.
     */
    void updateAllCreditTenorProfiles();

    /**
     * Updates cache for credit mode related changes for the credit provider organization and credit counterparty combination.
     *
     * @param cpo provider
     * @param cc  credit counterparty
     */
    void updateTradingPartyCreditMode( Organization cpo, TradingParty cc );

    /**
     * Updates the cache for credit mode related changes for the credit provider organization and counterparty organization combination.
     *
     * @param cpo provider
     * @param cco counterparty org
     */
    void updateCounterpartyOrganizationCreditMode( Organization cpo, Organization cco );

    /**
     * Updates the cache for credit mode related changes for the credit provider organization.
     *
     * @param cpo provider org
     */
    void updateProviderOrganizationCreditMode( Organization cpo );

    /**
     * Returns the list of currency pairs subscribed for the credit line.
     * @param cpo provider
     * @param cc credit cpty
     * @return currency pairs
     */
    Set<CurrencyPair> getSubscribedCurrencyPairs( Organization cpo, TradingParty cc );

    /**
     * Returns the list of counterparty credit limit rules that are auto stop-out enabled.
     * @return cpty rules
     */
    Collection<CounterpartyCreditLimitRule> getAutoStopOutEnabledRules();

    void updateAutoStopOutEnabledRules( CounterpartyCreditLimitRule cclr );

    void rebuildAutoStopOutEnabledRules();

    Map<Organization, Map<TradingParty, CreditRelationship>> getCounterpartyCreditLimitRuleMap();
}
