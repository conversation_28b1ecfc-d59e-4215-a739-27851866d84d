package com.integral.finance.creditLimit.functor;

import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.*;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;

import java.util.Collection;
import java.util.HashMap;

public class CreditTenorProfileNotificationFunctorC extends CreditRemoteNotificationFunctorC
{
    private static final Log log = LogFactory.getLog( CreditTenorProfileNotificationFunctorC.class );

    public void onCommit( HashMap props )
    {
        // do not process the message if credit remote notification processing is not enabled.
        if ( !creditAdminConfig.isCreditRemoteNotificationProcessingEnabled() )
        {
            return;
        }

        long t0 = System.currentTimeMillis();
        Organization cpo;
        Organization cco = null;
        TradingParty cc = null;
        try
        {
            // Use case#1 of credit admin service calls to assign the credit tenor profile at provider level or counterparty level.
            String creditProviderOrgGuid = ( String ) props.get( CreditLimit.CREDIT_PROVIDER_ORGANIZATION );
            if ( creditProviderOrgGuid != null )
            {
                cpo = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditProviderOrgGuid, Organization.class );

                String creditCptyOrgGuid = ( String ) props.get( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION );
                if ( creditCptyOrgGuid != null )
                {
                    cco = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditCptyOrgGuid, OrganizationC.class );
                }

                String creditCptyGuid = ( String ) props.get( CreditLimit.CREDIT_COUNTERPARTY );
                if ( creditCptyGuid != null )
                {
                    cc = ( TradingParty ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditCptyGuid, TradingParty.class );
                }

                if ( log.isDebugEnabled() )
                {
                    log.debug( "CTPNF.onCommit : updating cache for cpo=" + cpo + ",cco=" + cco + ",cc=" + cc );
                }

                if ( cpo != null )
                {
                    if ( cc != null )
                    {
                        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().updateTradingPartyCreditTenorProfile( cpo, cc );
                    }
                    else if ( cco != null )
                    {
                        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().updateCounterpartyOrganizationCreditTenorProfile( cpo, cco );
                    }
                    else
                    {
                        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().updateProviderOrganizationCreditTenorProfile( cpo );
                    }
                }
                else
                {
                    log.warn( "CTPNF.onCommit : credit provider org is not found for guid=" + creditProviderOrgGuid );
                }

                String eventName = (String)props.get( CreditLimitConstants.EVENT_PROPERTY );
                if ( null!= eventName && eventName.equals(CreditMessageEvent.UPDATECREDITTENORPROFILE.getName()) && creditProviderOrgGuid != null )
                {
                    cpo = (Organization) ReferenceDataCacheC.getInstance().getEntityByGuid(creditProviderOrgGuid, Organization.class);
                    String shortName = (String)props.get( CreditLimitConstants.EVENT_PROPERTY_ENTITY_SHORTNAME );
                    if( null!= shortName )
                    {
                        CreditTenorProfile creditTenorProfile = CreditUtilC.getCreditTenorProfile(cpo,shortName);
                        Collection<PFEConfiguration> configurations = CreditUtilC.getAssociatedCreditTenorProfile(cpo,creditTenorProfile);
                        for(PFEConfiguration pfeConfiguration: configurations )
                        {
                            log.info( "Reinitializing  : PFE Configuration " + pfeConfiguration.getFullyQualifiedName() + " on Tenor Profile change " + shortName + " for Org " +  cpo.getShortName());
                            ((PFEConfigurationC)pfeConfiguration).resetTransients();
                            ((PFEConfigurationC)pfeConfiguration).initTransients();
                        }
                    }

                }
            }

            log.info( new StringBuilder( 200 ).append( "CTPNF.onCommit : updated the cache for credit tenor profile. props=" )
                    .append( props ).append( ",timeTaken=" ).append( System.currentTimeMillis() - t0 ).toString() );
        }
        catch ( Exception e )
        {
            log.error( "CTPNF.onCommit : Exception while handling the admin notification on credit tenor profile. props=" + props, e );
        }
    }
}