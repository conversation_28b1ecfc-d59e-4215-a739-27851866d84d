package com.integral.finance.creditLimit;

// Copyright (c) 2011 Integral Development Corp. All rights reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.marketData.MarketDataSet;
import com.integral.persistence.Entity;
import com.integral.user.Organization;

/**
 * CreditWorkflowMessageImpl represents a credit workflow message used to
 * drive the credit workflow with a non-serializable message.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditWorkflowMessageImpl extends CreditWorkflowMessageC
{
    private Entity entity;
    private Organization organization;
    private LegalEntity legalEntity;
    private TradingParty tradingParty;
    private MarketDataSet marketDataSet;
    private Organization tradingPartyOrganization;

    public CreditWorkflowMessageImpl()
    {
        super();
    }

    public Object getObject()
    {
        return entity;
    }

    public void setObject( Object obj )
    {
        this.entity = ( Entity ) obj;
    }

    public Organization getOrganization()
    {
        return organization;
    }

    public void setOrganization( Organization org )
    {
        this.organization = org;
    }

    public LegalEntity getLegalEntity()
    {
        return legalEntity;
    }

    public void setLegalEntity( LegalEntity le )
    {
        this.legalEntity = le;
    }

    public TradingParty getTradingParty()
    {
        return tradingParty;
    }

    public void setTradingParty( TradingParty tp )
    {
        this.tradingParty = tp;
    }

    public Organization getTradingPartyOrganization()
    {
        return tradingPartyOrganization != null ? tradingPartyOrganization : getTradingParty() != null ? getTradingParty().getLegalEntityOrganization() : null;
    }

    public void setTradingPartyOrganization( Organization org )
    {
        this.tradingPartyOrganization = org;
    }

    public MarketDataSet getMarketDataSet()
    {
        return marketDataSet;
    }

    public void setMarketDataSet( MarketDataSet mds )
    {
        this.marketDataSet = mds;
    }
}


