package com.integral.finance.creditLimit.functor;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.finance.creditLimit.CounterpartyCreditLimitRule;
import com.integral.finance.creditLimit.CreditLimit;
import com.integral.finance.creditLimit.CreditLimitConstants;
import com.integral.finance.creditLimit.CreditMessageEvent;
import com.integral.finance.creditLimit.CreditUtilization;
import com.integral.finance.creditLimit.CreditUtilizationC;
import com.integral.finance.creditLimit.CreditUtilizationEvent;
import com.integral.finance.creditLimit.CurrencyPositionCollection;
import com.integral.finance.creditLimit.spaces.CreditUtilizationEventQueryService;
import com.integral.message.EntityReference;
import com.integral.message.MessageFactory;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import com.integral.system.configuration.ConfigurationFactory;
import org.eclipse.persistence.sessions.Session;

import java.util.HashMap;

/**
 * This class is used to synchronize cache between multiple servers when same credit lines are used in two or more servers.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditUtilizationCacheSynchronizeNotificationFunctorC extends CreditRemoteNotificationFunctorC
{
    public void onCommit( HashMap props )
    {
        // do not process the message if remote notification processing is not enabled.
        if ( !creditAdminConfig.isCreditRemoteNotificationProcessingEnabled() )
        {
            return;
        }

        String creditProviderOrg = ( String ) props.get( CreditLimit.CREDIT_PROVIDER_ORGANIZATION );
        String creditCpty = ( String ) props.get( CreditLimit.CREDIT_COUNTERPARTY );
        String eventName = ( String ) props.get( CreditLimitConstants.EVENT_PROPERTY );
        boolean spacesEnabled = props.get( CreditLimit.SPACES_ENABLED ) == null ? false : Boolean.parseBoolean( ( String ) props.get( CreditLimit.SPACES_ENABLED ) );
        String namespace = ( String ) props.get( CreditLimit.NAMESPACE );

        // If message is coming from spaces enabled server and spaces is not enabled on this server
        // then ignore the credit message.
        if ( spacesEnabled && !ConfigurationFactory.getServerMBean().isIntegralSpacesEnabled() )
        {
            if ( log.isDebugEnabled() )
            {
                log.debug( "CUSF.onCommit : Ingoring credit message since spaces is disabled on this server. props=" + props );
            }
            return;
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( "CUSF.onCommit : Resetting credit utilizations cache. props=" + props );
        }

        // Bug 44862 - If this commit notification is coming from TxnServer corrosponding to this IS then, drop the message since
        // Credit calculations are already happened on this server.
        // May we need to do this level of check at multi-app framework level.
        String vsName = ( String ) props.get( CreditLimitConstants.CREDIT_LIMIT_PEER_VIRTUAL_SERVER );
        if ( vsName != null && vsName.equals( ConfigurationFactory.getServerMBean().getVirtualServerName() ) )
        {
            log.info( "CUSF.onCommit : Dropping message coming from " + vsName + " for CreditUtilizationCacheSynchronizationNotification since its coming from TxnServer for this server " );
            return;
        }

        try
        {
            long[] cuObjectIds = ( long[] ) props.get( CreditLimit.CREDIT_UTILIZATION );
            String[] cueGuids = ( String[] ) props.get( CreditLimit.CREDIT_UTILIZATION_EVENT );
            if ( cuObjectIds != null )
            {
                Session session = getPersistenceSession();
                // iterate through the credit utilization objectId.
                for ( int i = 0; i < cuObjectIds.length; i++ )
                {
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "Processing cuObjectIds  " + cuObjectIds[i] );
                    }
                    CreditUtilization cu = getCreditUtilizationByObjectId( cuObjectIds[i], session );
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( new StringBuilder( 200 ).append( "CreditUtilizationCacheSynchronizeNotificationFunctorC.onCommit.DEBUG : cu from session cache=" )
                                .append( cu ).append( ",creditProviderOrg=" ).append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",cuId=" )
                                .append( cuObjectIds[i] ).append( ",cue=" ).append( cueGuids[i] ).append( ",event=" ).append( eventName ).toString() );
                    }
                    if ( cu == null )
                    {
                        continue;
                    }
                    CurrencyPositionCollection ccyPos = cu.getCurrencyPositions( false );
                    if ( ccyPos != null )
                    {

                        CreditUtilizationEvent cue;
                        if ( spacesEnabled )
                        {
                            cue = CreditUtilizationEventQueryService.getSpacesCreditUtilizationEvent( cueGuids[i], namespace );
                        }
                        else
                        {
                            cue = getCreditUtilizationEventByGUID( cueGuids[i] );
                        }
                        if ( cue != null )
                        {
                            CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule().getParentRule();
                            boolean setRealtimeUsedAmt = false;
                            try
                            {
                                cclr.getLock().lock();
                                long t0 = System.currentTimeMillis();
                                if ( log.isDebugEnabled() )
                                {
                                    log.debug( new StringBuilder( 200 ).append( "CUSF.onCommit.INFO : Before updating the ccyPos=" )
                                            .append( ccyPos ).append( ",creditProviderOrg=" ).append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",cu=" )
                                            .append( cuObjectIds[i] ).append( ",cue=" ).append( cueGuids[i] ).append( ",event=" ).append( eventName ).toString() );
                                }
                                if ( CreditMessageEvent.USE.getName().equals( eventName ) )
                                {
                                    //handle cases where USE event is handled after update event finished in db.
                                    boolean updateEvent = cue.getOriginalPriceAmount() > 0.0 && cue.getOriginalPrincipalAmount() > 0.0;
                                    if ( updateEvent )
                                    {
                                        log.info( new StringBuilder( 200 ).append( "CUSF.onCommit : Use event received with update event data for cue=" ).
                                                append( cue ).append( ",cu=" ).append( cu ).append( ",creditProviderOrg=" )
                                                .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).toString() );

                                    }
                                    ccyPos.addCreditUtilizationEvent( cue, updateEvent );
                                    setRealtimeUsedAmt = true;
                                }
                                else if ( CreditMessageEvent.UNDO.getName().equals( eventName ) || CreditMessageEvent.REMOVE.getName().equals( eventName ) )
                                {
                                    ccyPos.removeCreditUtilizationEvent( cue );
                                    setRealtimeUsedAmt = true;
                                }
                                else if ( CreditMessageEvent.UPDATEAMOUNT.getName().equals( eventName ) )
                                {
                                    ccyPos.updateCreditUtilizationEvent( cue );
                                    setRealtimeUsedAmt = true;
                                }
                                else
                                {
                                    log.warn( new StringBuilder( 200 ).append( "CUSF.onCommit : Unsupported event. Resetting currency positions. props=" )
                                            .append( props ).append( ",ccyPos=" ).append( ccyPos ).toString() );
                                    cu.rebuildCurrencyPositions( CreditLimitConstants.RESET_CACHE_SYNC_MISC, true );
                                    setRealtimeUsedAmt = true;
                                }

                                if ( log.isDebugEnabled() )
                                {
                                    log.debug( new StringBuilder( 200 ).append( "CUSF.onCommit.INFO : After updating the ccyPos=" )
                                            .append( ccyPos ).append( ",creditProviderOrg=" ).append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",cue=" )
                                            .append( cueGuids[i] ).append( ",event=" ).append( eventName ).toString() );
                                }

                                long elapsed = System.currentTimeMillis() - t0;
                                if ( elapsed > 1000 )
                                {
                                    log.warn( new StringBuilder( 200 ).append( "CUSF.onCommit.INFO : Time taken=" )
                                            .append( elapsed ).append( ",cclr=" ).append( cclr ).toString() );
                                }
                            }
                            finally
                            {
                                cclr.getLock().unlock();
                            }

                            // calculate and set new used amounts.
                            if ( setRealtimeUsedAmt )
                            {
                                cu.setUsedAmount( cu.getCreditLimitRule().getCreditUtilizationCalculator().getRealtimeUtilizationAmount( cu ) );
                            }
                        }
                        else
                        {
                            log.warn( "CUSF.onCommit : Null credit utilization event. props=" + props );
                        }
                    }
                }
            }
            else
            {
                log.warn( "CUSF.onCommit : Null credit utilization property. props=" + props );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CUSF.onCommit : Error synchronizing the currency position for org=" + creditProviderOrg, e );
        }
    }

    protected Session getPersistenceSession() throws PersistenceException
    {
        return PersistenceFactory.newSession();
    }

    protected CreditUtilization getCreditUtilizationByObjectId( long objectId, Session session )
    {
        return ( CreditUtilization ) session.getIdentityMapAccessor().getFromIdentityMap( objectId, CreditUtilizationC.class );
    }

    protected CreditUtilizationEvent getCreditUtilizationEventByGUID( String guid )
    {
        EntityReference ef = MessageFactory.newEntityReference();
        ef.setClassName( CreditUtilizationEvent.class.getName() );
        ef.setGuid( guid );
        return ( CreditUtilizationEvent ) ef.getEntity();
    }
}

