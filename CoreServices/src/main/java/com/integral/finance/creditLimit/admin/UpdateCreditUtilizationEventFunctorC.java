package com.integral.finance.creditLimit.admin;

// Copyright (c) 2013 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.time.IdcDate;
import com.integral.user.Organization;

import java.util.Collection;

/**
 * This class is used to update the credit utilization events based on the tenor coefficient and as the well as the date period between
 * the value date and current trade date.
 *
 * <AUTHOR> Development Corp.
 */
public class UpdateCreditUtilizationEventFunctorC extends CreditFunctorC
{
    public UpdateCreditUtilizationEventFunctorC( String name )
    {
        setName( name );
    }

    public void execute()
    {
        try
        {
            if ( isEnabled() && creditAdminConfigMBean.isCreditUtilizationEventRevaluationEnabled() )
            {
                start();
                IdcDate businessDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
                Collection<Organization> orgs = CreditUtilC.getOrganizationsWithPFESet();
                log.info( "UCUEF.execute : orgs set with PFE or tenor profiles = "+orgs );
                if ( orgs != null && !orgs.isEmpty() )
                {
                    CreditUtilizationManagerC.getInstance().recalculateAllCreditUtilizationEvents( businessDate, this, orgs );
                }
                else
                {
                    log.info( "UCUEF.execute : No orgs set with credit tenor profiles." );
                }
                finish();
            }
            else
            {
                log.info( "UCUEF.execute : Functor not executed. this=" + this + ",creditUtilEventRevaluationEnabled=" + creditAdminConfigMBean.isCreditUtilizationEventRevaluationEnabled() );
            }
        }
        catch ( Exception e )
        {
            log.error( "UCUEF.execute : Exception while recalculating events.", e );
            finish();
        }

        // mark suspension as completed if suspend was initiated.
        if ( suspensionInitiated )
        {
            this.suspended = true;
        }
    }

}
