package com.integral.finance.creditLimit.calculator;

// Copyright (c) 2019 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.*;
import com.integral.util.Factory;

import java.util.HashMap;
import java.util.Map;

/**
 * Provides factory methods for instantiating credit utilization calculator proxy objects.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditUtilizationCalculatorProxyFactory extends Factory implements CreditLimitConstants
{
    // Singleton members.
    protected static CreditUtilizationCalculatorProxyFactory current;
    private static final CreditUtilizationCalculatorProxy aggNetReceivableCalc = new AggregateLimitCreditUtilizationCalculatorProxyC( AGGREGATE_LIMIT_CALCULATOR_SHORT_NAME );
    private static final CreditUtilizationCalculatorProxy aggNetCashSettlmentCalc = new AggregateNetCashSettlementCreditUtilizationCalculatorProxyC( AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR_SHORT_NAME );
    private static final CreditUtilizationCalculatorProxy aggNetPRSettlmentCalc = new AggregateNetPRSettlementCreditUtilizationCalculatorProxyC( AGGREGATE_NETPR_SETTLEMENT_CALCULATOR_SHORT_NAME );
    private static final CreditUtilizationCalculatorProxy aggNetSettlmentCalc = new NetOpenPositionCreditUtilizationCalculatorProxyC( NET_OPEN_POSITION_CALCULATOR_SHORT_NAME );
    private static final CreditUtilizationCalculatorProxy aggNetSettlmentReceivableCalc = new AggregateNetSettlementReceivableCreditUtilizationCalculatorProxyC( AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR_SHORT_NAME );
    private static final CreditUtilizationCalculatorProxy dailyNetReceivableCalc = new DailySettlementLimitCreditUtilizationCalculatorProxyC( DAILY_SETTLEMENT_LIMIT_CALCULATOR_SHORT_NAME );
    private static final CreditUtilizationCalculatorProxy aggGrossCalc = new CreditUtilizationCalculatorProxyC( CreditLimitConstants.GROSS_AGGREGATE_LIMIT_CALCULATOR_SHORT_NAME );
    private static final CreditUtilizationCalculatorProxy dailyGrossCalc = new CreditUtilizationCalculatorProxyC( CreditLimitConstants.GROSS_DAILY_LIMIT_CALCULATOR_SHORT_NAME );
    private static final CreditUtilizationCalculatorProxy aggMultiFactorSettlement = new CreditUtilizationCalculatorProxyC( CreditLimitConstants.AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR_SHORT_NAME );

    private static final Map<String, CreditUtilizationCalculatorProxy> _calcProxyMap = new HashMap<String, CreditUtilizationCalculatorProxy> (  );

    // Static initialization.
    static
    {
        current = new CreditUtilizationCalculatorProxyFactory();
        _calcProxyMap.put ( AGGREGATE_LIMIT_CALCULATOR_SHORT_NAME, aggNetReceivableCalc );
        _calcProxyMap.put ( AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR_SHORT_NAME, aggNetCashSettlmentCalc );
        _calcProxyMap.put ( AGGREGATE_NETPR_SETTLEMENT_CALCULATOR_SHORT_NAME, aggNetPRSettlmentCalc );
        _calcProxyMap.put ( AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR_SHORT_NAME, aggNetSettlmentReceivableCalc );
        _calcProxyMap.put ( NET_OPEN_POSITION_CALCULATOR_SHORT_NAME, aggNetSettlmentCalc );
        _calcProxyMap.put ( DAILY_SETTLEMENT_LIMIT_CALCULATOR_SHORT_NAME, dailyNetReceivableCalc );
        _calcProxyMap.put ( GROSS_AGGREGATE_LIMIT_CALCULATOR_SHORT_NAME, aggGrossCalc );
        _calcProxyMap.put ( GROSS_DAILY_LIMIT_CALCULATOR_SHORT_NAME, dailyGrossCalc );
        _calcProxyMap.put ( AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR_SHORT_NAME, aggMultiFactorSettlement );
    }

    public static CreditUtilizationCalculatorProxy getCreditUtilizationCalculatorProxy( CreditUtilizationCalculator creditCalc )
    {
        return current._getCreditUtilizationCalculatorProxy( creditCalc );
    }

    private CreditUtilizationCalculatorProxy _getCreditUtilizationCalculatorProxy( CreditUtilizationCalculator creditCalc )
    {
		return creditCalc != null ? _calcProxyMap.get ( creditCalc.getShortName () ) : null;
    }


    public static CreditUtilizationCalculatorProxy getCreditUtilizationCalculatorProxy( CreditLimitRule clr )
    {
        return current._getCreditUtilizationCalculatorProxy( clr );
    }

    private CreditUtilizationCalculatorProxy _getCreditUtilizationCalculatorProxy( CreditLimitRule clr )
    {
        return clr != null ? _getCreditUtilizationCalculatorProxy ( clr.getCreditUtilizationCalculator () ) : null;
    }
}

