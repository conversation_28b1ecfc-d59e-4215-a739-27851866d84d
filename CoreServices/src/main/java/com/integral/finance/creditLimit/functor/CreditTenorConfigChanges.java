package com.integral.finance.creditLimit.functor;

// Copyright (c) 2016 Integral Development Corp.  All rights reserved.

import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditLimit;
import com.integral.finance.creditLimit.CreditLimitConstants;
import com.integral.finance.creditLimit.CreditMessageEvent;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;

import java.util.HashMap;


public class CreditTenorConfigChanges extends CreditRemoteNotificationFunctorC
{
    public void onCommit( HashMap props ) {
        // do not process the message if remote notification processing is not enabled.
        if (!creditAdminConfig.isCreditRemoteNotificationProcessingEnabled()) {
            return;
        }

        long t0 = System.currentTimeMillis();
        Organization creditProviderOrg = null;
        Organization creditCptyOrg = null;
        TradingParty creditCpty = null;

        String eventName = (String) props.get(CreditLimitConstants.EVENT_PROPERTY);
        String creditProviderOrgGuid = (String) props.get(CreditLimit.CREDIT_PROVIDER_ORGANIZATION);

        if (creditProviderOrgGuid != null) {
            creditProviderOrg = (Organization) ReferenceDataCacheC.getInstance().getEntityByGuid(creditProviderOrgGuid, Organization.class);

            String creditCptyOrgGuid = (String) props.get(CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION);
            if (creditCptyOrgGuid != null) {
                creditCptyOrg = (Organization) ReferenceDataCacheC.getInstance().getEntityByGuid(creditCptyOrgGuid, Organization.class);
            }

            String creditCptyGuid = (String) props.get(CreditLimit.CREDIT_COUNTERPARTY);
            if (creditCptyGuid != null) {
                creditCpty = (TradingParty) ReferenceDataCacheC.getInstance().getEntityByGuid(creditCptyGuid, TradingParty.class);
            }

        }
        // special handling for org/le exposure level changes.
        if ( eventName != null && ( eventName.equalsIgnoreCase(CreditMessageEvent.SETLEOVERRIDE.getName())
                || eventName.equalsIgnoreCase(CreditMessageEvent.SETORGDEFAULT.getName())
                || eventName.equalsIgnoreCase(CreditMessageEvent.SETMAXIMUMTENOR.getName())
                ||  eventName.equalsIgnoreCase(CreditMessageEvent.SETMINIMUMTENOR.getName()) || eventName.equalsIgnoreCase( CreditMessageEvent.SETTENORBUSINESSDAYS.getName() ) ))
        {
            if( null!=creditCpty)
            {
                CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().resetCreditCounterpartyCptyTenor( creditProviderOrg, creditCpty );
            }
            else
            if( null!= creditCptyOrg)
            {
                CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().resetCreditCounterpartyCptyTenor( creditProviderOrg, creditCptyOrg );
            }
            else
            if( null!= creditProviderOrg)
            {
                CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().resetCreditCounterpartyCptyTenor( creditProviderOrg );
            }

        }
    }
}
