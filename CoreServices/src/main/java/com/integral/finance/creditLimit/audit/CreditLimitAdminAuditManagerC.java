package com.integral.finance.creditLimit.audit;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.audit.*;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationFactory;
import com.integral.finance.creditLimit.configuration.CreditConfigurationFactory;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.trade.Tenor;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.session.IdcSessionManager;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.util.IdcUtilC;

import java.util.Collection;

/**
 * This class is used to manage credit admin related auditing.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitAdminAuditManagerC implements CreditLimitAdminAuditManager
{
    protected Log log = LogFactory.getLog( this.getClass() );

    private static final String NONE = "None";

    /**
     * Singleton instance
     */
    private static CreditLimitAdminAuditManager _creditAdminAuditManager;

    /*
     * Static initialization.
     */
    static
    {
        _creditAdminAuditManager = new CreditLimitAdminAuditManagerC();
    }

    /**
     * Private empty constructor.
     */
    private CreditLimitAdminAuditManagerC()
    {
    }

    /**
     * Returns the singleton instance of CreditLimitAdminAuditManagerC.
     *
     * @return credit notification manager.
     */
    public static CreditLimitAdminAuditManager getInstance()
    {
        return _creditAdminAuditManager;
    }

    public static void _setInstance( CreditLimitAdminAuditManager creditAdminAuditManager)
    {
         _creditAdminAuditManager = creditAdminAuditManager;
    }

    @Override
    public void auditCounterpartyCreditLimitRules(Collection<CounterpartyCreditLimitRule> cclrs, Character status)
    {
        try
        {
            for ( CounterpartyCreditLimitRule cclr : cclrs )
            {
                Organization providerOrg = ( ( CreditLimitRuleSet ) cclr.getRuleSet() ).getOrganization();
                Organization creditCptyOrg = cclr.getTradingPartyOrganization();
                boolean isCptyOrgLevel = cclr.getTradingParty() == null;

                auditCounterpartyLevelCreditActivation( providerOrg, isCptyOrgLevel, cclr.getTradingParty(), creditCptyOrg, status );
            }
        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyCreditLimitRuleList : cclrs=" + cclrs, e );
        }
    }

    @Override
    public void auditCounterpartyCreditLimitRule(CounterpartyCreditLimitRule cclr, Character status)
    {
        try
        {
            Organization providerOrg = ( ( CreditLimitRuleSet ) cclr.getRuleSet() ).getOrganization();
            Organization creditCptyOrg = cclr.getTradingPartyOrganization();
            boolean isCptyOrgLevel = cclr.getTradingParty() == null;

            auditCounterpartyLevelCreditActivation( providerOrg, isCptyOrgLevel, cclr.getTradingParty(), creditCptyOrg, status );
        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyCreditLimitRuleList : cclr=" + cclr, e );
        }
    }

    @Override
    public void auditCounterpartyLevelCreditActivation(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Character status)
    {
        try
        {
            String action = status.equals( 'A' ) ? CreditMessageEvent.ACTIVATECREDIT.getName() : CreditMessageEvent.INACTIVATECREDIT.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );

            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit("CREDITADMIN", audit);
        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCreditEnable : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditOrgLevelCreditEnable(Organization creditProviderOrg, boolean enable)
    {
        try
        {
            String action = enable ? CreditMessageEvent.ENABLECREDIT.getName() : CreditMessageEvent.DISABLECREDIT.getName();
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );

           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCreditEnableAtOrgLevel : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditCounterpartyLevelCreditEnable(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, boolean enable)
    {
        try
        {
            String action = enable ? CreditMessageEvent.ENABLECREDIT.getName() : CreditMessageEvent.DISABLECREDIT.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCreditEnable : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditOrgLevelDailyNettingMethodology(Organization creditProviderOrg, CreditUtilizationCalculator oldCalc, CreditUtilizationCalculator newCalc)
    {
        try
        {
            String action = CreditMessageEvent.SETCREDITUTILIZATIONDAILYCALCULATOR.getName();
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            if ( oldCalc != null )
            {
                auditFacade.setExistingNettingMethodology( oldCalc );
            }
            if ( newCalc != null )
            {
                auditFacade.setNewNettingMethodology( newCalc );
            }
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelDailyNettingMethodology : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditOrgLevelAggregateNettingMethodology(Organization creditProviderOrg, CreditUtilizationCalculator oldCalc, CreditUtilizationCalculator newCalc)
    {
        try
        {
            String action = CreditMessageEvent.SETCREDITUTILIZATIONAGGREGATECALCULATOR.getName();
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            if ( oldCalc != null )
            {
                auditFacade.setExistingNettingMethodology( oldCalc );
            }
            if ( newCalc != null )
            {
                auditFacade.setNewNettingMethodology( newCalc );
            }
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelAggregateNettingMethodology : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditCounterpartyLevelDailyNettingMethodology(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, CreditUtilizationCalculator oldCalc, CreditUtilizationCalculator newCalc)
    {
        try
        {
            String action = CreditMessageEvent.SETCREDITUTILIZATIONDAILYCALCULATOR.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            if ( oldCalc != null )
            {
                auditFacade.setExistingNettingMethodology( oldCalc );
            }
            if ( newCalc != null )
            {
                auditFacade.setNewNettingMethodology( newCalc );
            }
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelDailyNettingMethodology : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditCounterpartyLevelAggregateNettingMethodology(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, CreditUtilizationCalculator oldCalc, CreditUtilizationCalculator newCalc)
    {
        try
        {
            String action = CreditMessageEvent.SETCREDITUTILIZATIONAGGREGATECALCULATOR.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            if ( oldCalc != null )
            {
                auditFacade.setExistingNettingMethodology( oldCalc );
            }
            if ( newCalc != null )
            {
                auditFacade.setNewNettingMethodology( newCalc );
            }
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelAggregateNettingMethodology : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditOrgLevelNotificationPercentage(Organization creditProviderOrg, Double existingPercentage, Double newPercentage)
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init(CreditMessageEvent.SETNOTIFICATIONPERCENTAGE.getName(), CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null);
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setExistingNotificationPercentage( existingPercentage );
            auditFacade.setNewNotificationPercentage(newPercentage);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCreditEnableAtOrgLevel : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditOrgLevelRejectionEmailEnable(Organization creditProviderOrg, boolean enable)
    {
        try
        {
            String action = enable ? CreditMessageEvent.ENABLEREJECTIONEMAIL.getName() : CreditMessageEvent.DISABLEREJECTIONEMAIL.getName();
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel(CreditAuditEventParameters.ORG_LEVEL);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelRejectionEmail : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditOrgLevelLimitCurrency(Organization creditProviderOrg, Currency existingLimitCurrency, Currency newLimitCurrency)
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditMessageEvent.SETDEFAULTLIMITCURRENCY.getName(), CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setExistingLimitCurrency( existingLimitCurrency );
            auditFacade.setLimitCurrency(newLimitCurrency);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelLimitCurrency : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditOrgLevelDailyExposureHorizon(Organization creditProviderOrg, int existingDailyExposureHorizon, int newDailyExposureHorizon)
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditMessageEvent.SETDAILYEXPOSUREHORIZON.getName(), CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setExistingDailyExposureHorizon(existingDailyExposureHorizon);
            auditFacade.setNewDailyExposureHorizon(newDailyExposureHorizon);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelDailyExposureHorizon : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditOrgLevelSenderEmailAddress(Organization creditProviderOrg, String existingSenderEmail, String newSenderEmail)
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditMessageEvent.SETSENDEREMAILADDRESS.getName(), CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setExistingSenderEmailAddress(existingSenderEmail);
            auditFacade.setNewSenderEmailAddress(newSenderEmail);
            /*
             * Necessary for org i.e namespace to be set for Mongo
             */
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );


        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelSenderEmailAddress : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void enrichAuditData(AuditEventC audit)
    {
        AuditInfo auditInfo = (AuditInfo) IdcSessionManager.getInstance().getSessionContext(IdcUtilC.getSessionContextUser()).getAttribute("audit-info");
        if(auditInfo != null)
        {
          audit.setSourceIP(auditInfo.getIpAddress());
          audit.setSourceMachineName(auditInfo.getSourceVirtualServerMachineName());
          audit.setSourceVirtualServer(auditInfo.getSourceVirtualServer());
        }
        audit.setModifiedByUser(IdcUtilC.getSessionContextUser().getShortName());
        audit.setModifiedByNamespace(IdcUtilC.getSessionContextUser().getNamespace().getShortName());
    }

    @Override
    public void auditOrgLevelClientReinitialize(Organization creditProviderOrg)
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditMessageEvent.SETCLIENTREINITIALIZE.getName(), CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
             /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelClientReinitialize : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditOrgLevelDefaultCreditExposure(Organization creditProviderOrg, boolean isOrgLevel)
    {
        try
        {
            String action = isOrgLevel ? CreditMessageEvent.SETDEFAULTORGEXPOSURE.getName() : CreditMessageEvent.SETDEFAULTCOUNTERPARTYEXPOSURE.getName();
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditDefaultCreditExposure : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditOrgLevelWarningPercentage(Organization creditProviderOrg, Double existingPercentage, Double newPercentage)
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditMessageEvent.SETWARNINGPERCENTAGE.getName(), CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setExistingWarningPercentage( existingPercentage );
            auditFacade.setNewWarningPercentage(newPercentage);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelWarningPercentage : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditOrgLevelSuspensionPercentage(Organization creditProviderOrg, Double existingPercentage, Double newPercentage)
    {
        try
        {
            boolean marginCall =  CreditConfigurationFactory.getCreditConfigurationMBean ().isCreditRetailModeEnabled ( creditProviderOrg.getShortName () );
            String action = marginCall ? CreditMessageEvent.SETMARGINCALLPERCENTAGE.getName() : CreditMessageEvent.SETSUSPENSIONPERCENTAGE.getName ();
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setExistingSuspensionPercentage(existingPercentage);
            auditFacade.setNewSuspensionPercentage(newPercentage);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelSuspensionPercentage : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditOrgLevelStopOutPercentage(Organization creditProviderOrg, Double existingPercentage, Double newPercentage)
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditMessageEvent.SETSTOPOUTPERCENTAGE.getName(), CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setExistingStopOutPercentage(existingPercentage);
            auditFacade.setNewStopOutPercentage (newPercentage);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelStopOutPercentage : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditCounterpartyLevelNotificationPercentage(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Double existingPercentage, Double newPercentage)
    {
        try
        {
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditMessageEvent.SETNOTIFICATIONPERCENTAGE.getName(), actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingNotificationPercentage(existingPercentage);
            auditFacade.setNewNotificationPercentage(newPercentage);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelNotificationPercentage : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditCounterpartyLevelWarningPercentage(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Double existingPercentage, Double newPercentage)
    {
        try
        {
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditMessageEvent.SETWARNINGPERCENTAGE.getName(), actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingWarningPercentage(existingPercentage);
            auditFacade.setNewWarningPercentage(newPercentage);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelWarningPercentage : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditCounterpartyLevelSuspensionPercentage(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Double existingPercentage, Double newPercentage)
    {
        try
        {
            boolean marginCall =  CreditConfigurationFactory.getCreditConfigurationMBean ().isCreditRetailModeEnabled ( creditProviderOrg.getShortName () );
            String action = marginCall ? CreditMessageEvent.SETMARGINCALLPERCENTAGE.getName() : CreditMessageEvent.SETSUSPENSIONPERCENTAGE.getName ();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingSuspensionPercentage(existingPercentage);
            auditFacade.setNewSuspensionPercentage(newPercentage);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelSuspensionPercentage : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditCounterpartyLevelStopOutPercentage(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Double existingPercentage, Double newPercentage)
    {
        try
        {
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditMessageEvent.SETSTOPOUTPERCENTAGE.getName(), actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingStopOutPercentage(existingPercentage);
            auditFacade.setNewStopOutPercentage(newPercentage);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelStopOutPercentage : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditOrgLevelNotificationEmailAddress(Organization creditProviderOrg, String existingEmail, String newEmail)
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditMessageEvent.SETNOTIFICATIONEMAILADDRESS.getName(), CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setExistingEmailAddress(existingEmail);
            auditFacade.setNewEmailAddress(newEmail);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelNotificationEmailAddress : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditCounterpartyLevelNotificationEmailAddress(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, String existingEmail, String newEmail)
    {
        try
        {
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditMessageEvent.SETNOTIFICATIONEMAILADDRESS.getName(), actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingEmailAddress( existingEmail );
            auditFacade.setNewEmailAddress( newEmail );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelNotificationEmailAddress : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditCounterpartyLevelCreditLimit(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, double existingLimit, double newLimit, CreditUtilizationCalculator calc)
    {
        try
        {
            String action = CreditMessageEvent.UPDATELIMIT.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingLimitAmount(existingLimit);
            auditFacade.setNewLimitAmount(newLimit);
            if ( calc != null )
            {
                auditFacade.setExistingNettingMethodology( calc );
            }
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCreditLimit : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditCounterpartyLevelCreditLimitOnDate(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Double existingLimit, Double newLimit, IdcDate valueDate)
    {
        try
        {
            String action = CreditMessageEvent.UPDATELIMITDAILY.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingLimitAmount(existingLimit);
            auditFacade.setNewLimitAmount(newLimit);
            auditFacade.setSettlementDate(valueDate);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCreditLimitOnDate : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditOrganizationLevelCreditExposure(Organization creditProviderOrg, TradingParty creditCpty, Organization creditCptyOrg, boolean initial)
    {
        try
        {
            String action = CreditMessageEvent.SETORGEXPOSURE.getName() + ( initial ? CreditMessageEvent.ADD.getName() : CreditMessageEvent.UPDATE.getName() );
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.CPTY_LEVEL, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel(CreditAuditEventParameters.CPTY_LEVEL);
            auditFacade.setExistingCreditExposureLevel(initial ? null : CreditLimit.COUNTERPARTY_CREDIT_EXPOSURE);
            auditFacade.setNewCreditExposureLevel(CreditLimit.ORGANIZATION_CREDIT_EXPOSURE);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrganizationLevelCreditExposure : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditCounterpartyLevelCreditExposure(Organization creditProviderOrg, TradingParty creditCpty, Organization creditCptyOrg, boolean initial)
    {
        try
        {
            String action = CreditMessageEvent.SETCOUNTERPARTYEXPOSURE.getName() + ( initial ? CreditMessageEvent.ADD.getName() : CreditMessageEvent.UPDATE.getName() );
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.CPTY_LEVEL, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( CreditAuditEventParameters.CPTY_LEVEL );
            auditFacade.setExistingCreditExposureLevel( initial ? null : CreditLimit.ORGANIZATION_CREDIT_EXPOSURE );
            auditFacade.setNewCreditExposureLevel( CreditLimit.COUNTERPARTY_CREDIT_EXPOSURE );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCreditExposure : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditEstablishCreditRelationship(Organization creditProviderOrg, TradingParty creditCpty, Organization creditCptyOrg, boolean initial, boolean orgLevel)
    {
        try
        {
            String action = CreditMessageEvent.SETCREDITRELATIONSHIP.getName() + ( initial ? CreditMessageEvent.ADD.getName() : CreditMessageEvent.UPDATE.getName() );
            CreditLimitAdminAuditEventFacade auditFacade = init( action, orgLevel ? CreditAuditEventParameters.ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel(CreditAuditEventParameters.CPTY_LEVEL);
            auditFacade.setNewCreditExposureLevel( orgLevel ? CreditLimit.ORGANIZATION_CREDIT_EXPOSURE : CreditLimit.COUNTERPARTY_CREDIT_EXPOSURE );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditEstablishCreditRelationship : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditCreditRejection(Organization creditProviderOrg, TradingParty creditCpty, Organization creditCptyOrg, String tradeDescription)
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditAuditEventParameters.CREDIT_REJECTION, "", creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setTradeDescription(tradeDescription);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCreditRejection : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditUtilizationPercentageBreach(Organization creditProviderOrg, TradingParty creditCpty, Organization creditCptyOrg, double utilizationPercent, int notificationLevel, double percentageLimit)
    {
        try
        {
            String action = null;
            switch ( notificationLevel )
            {
                case CreditNotificationManagerC.NOTIFICATION_LEVEL:
                    action = CreditAuditEventParameters.NOTIFICATION_PERCENTAGE_BREACH;
                    break;
                case CreditNotificationManagerC.WARNING_LEVEL:
                    action = CreditAuditEventParameters.WARNING_PERCENTAGE_BREACH;
                    break;
                case CreditNotificationManagerC.SUSPENSION_LEVEL:
                {
                    boolean marginCall =  CreditConfigurationFactory.getCreditConfigurationMBean ().isCreditRetailModeEnabled ( creditProviderOrg.getShortName () ) && notificationLevel == CreditNotificationManagerC.SUSPENSION_LEVEL;
                    action = marginCall ? CreditAuditEventParameters.MARGIN_CALL_PERCENTAGE_BREACH : CreditAuditEventParameters.SUSPENSION_PERCENTAGE_BREACH;
                    break;
                }
                case CreditNotificationManagerC.STOPOUT_LEVEL:
                    action = CreditAuditEventParameters.STOPOUT_PERCENTAGE_BREACH;
                    break;
            }
            CreditLimitAdminAuditEventFacade auditFacade = init( action, "", creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( CreditAuditEventParameters.CPTY_LEVEL );
            auditFacade.setUtilizationPercentage(utilizationPercent);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditUtilizationPercentageBreach : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditOrgLevelLeverageFactor(Organization creditProviderOrg, Double oldFactor, Double newFactor)
    {
        try
        {
            String action = CreditMessageEvent.SETLEVERAGEFACTOR.getName();
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            if ( oldFactor != null )
            {
                auditFacade.setExistingLeverageFactor( oldFactor );
            }
            if ( newFactor != null )
            {
                auditFacade.setNewLeverageFactor( newFactor );
            }
            auditFacade.setActionLevel(CreditAuditEventParameters.ORG_LEVEL);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelLeverageFactor : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditCounterpartyLevelLeverageFactor(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Double oldFactor, Double newFactor)
    {
        try
        {
            String action = CreditMessageEvent.SETLEVERAGEFACTOR.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            if ( oldFactor != null )
            {
                auditFacade.setExistingLeverageFactor( oldFactor );
            }
            if ( newFactor != null )
            {
                auditFacade.setNewLeverageFactor( newFactor );
            }
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelLeverageFactor : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditOrgLevelApplyPandL(Organization creditProviderOrg, Boolean newApplyPandL)
    {
        try
        {
            String action = newApplyPandL ? CreditMessageEvent.ENABLEPANDL.getName() : CreditMessageEvent.DISABLEPANDL.getName();
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelApplyPandL : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditCounterpartyLevelApplyPandL(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Boolean newApplyPandL)
    {
        try
        {
            String action = newApplyPandL ? CreditMessageEvent.ENABLEPANDL.getName() : CreditMessageEvent.DISABLEPANDL.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelApplyPandL : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditOrgLevelIgnoreCurrentDatePositions(Organization creditProviderOrg, Boolean newIgnoreCurrDatePositions)
    {
        try
        {
            String action = newIgnoreCurrDatePositions ? CreditMessageEvent.ENABLEIGNORECURRENTDATEPOSITIONS.getName() : CreditMessageEvent.DISABLEIGNORECURRENTDATEPOSITIONS.getName();
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelIgnoreCurrentDatePositions : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditCounterpartyLevelIgnoreCurrentDatePositions(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Boolean newIgnoreCurrDatePositions)
    {
        try
        {
            String action = newIgnoreCurrDatePositions ? CreditMessageEvent.ENABLEIGNORECURRENTDATEPOSITIONS.getName() : CreditMessageEvent.DISABLEIGNORECURRENTDATEPOSITIONS.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelIgnoreCurrentDatePositions : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditOrgLevelMaximumTenor(Organization creditProviderOrg, Tenor existingMaximumTenor, Tenor newMaximumTenor)
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditMessageEvent.SETMAXIMUMTENOR.getName(), CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setExistingMaximumTenor( existingMaximumTenor != null ? existingMaximumTenor.toString() : null );
            auditFacade.setNewMaximumTenor(newMaximumTenor != null ? newMaximumTenor.toString() : null);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelMaximumTenor : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditCounterpartyLevelMaximumTenor(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Tenor existingMaximumTenor, Tenor newMaximumTenor)
    {
        try
        {
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditMessageEvent.SETMAXIMUMTENOR.getName(), actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingMaximumTenor( existingMaximumTenor != null ? existingMaximumTenor.toString() : null );
            auditFacade.setNewMaximumTenor( newMaximumTenor != null ? newMaximumTenor.toString() : null );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelMaximumTenor : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditOrgLevelMinimumTenor(Organization creditProviderOrg, Tenor existingMinimumTenor, Tenor newMinimumTenor)
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditMessageEvent.SETMINIMUMTENOR.getName(), CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setExistingMinimumTenor(existingMinimumTenor != null ? existingMinimumTenor.toString() : null);
            auditFacade.setNewMinimumTenor(newMinimumTenor != null ? newMinimumTenor.toString() : null);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelMinimumTenor : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditCounterpartyLevelMinimumTenor(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Tenor existingMinimumTenor, Tenor newMinimumTenor)
    {
        try
        {
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditMessageEvent.SETMINIMUMTENOR.getName(), actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingMinimumTenor( existingMinimumTenor != null ? existingMinimumTenor.toString() : null );
            auditFacade.setNewMinimumTenor( newMinimumTenor != null ? newMinimumTenor.toString() : null );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelMinimumTenor : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditOrgLevelSetUpdateBalanceWithPL(Organization creditProviderOrg, Boolean newUpdatePL)
    {
        try
        {
            String action = newUpdatePL ? CreditMessageEvent.ENABLEUPDATEBALANCEWITHPL.getName() : CreditMessageEvent.DISABLEUPDATEBALANCEWITHPL.getName();
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel(CreditAuditEventParameters.ORG_LEVEL);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelSetUpdateBalanceWithPL : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditCounterpartyLevelSetUpdateBalanceWithPL(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Boolean newUpdateBalanceWithPL)
    {
        try
        {
            String action = newUpdateBalanceWithPL ? CreditMessageEvent.ENABLEUPDATEBALANCEWITHPL.getName() : CreditMessageEvent.DISABLEUPDATEBALANCEWITHPL.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelSetUpdateBalanceWithPL : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditCounterpartyLevelCreditBalanceUpdate(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, double existingLimit, double newLimit, CreditUtilizationCalculator calc)
    {
        try
        {
            String action = CreditMessageEvent.UPDATEBALANCEWITHPL.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            User usr = null;
            String pLUpdateUser = CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean().getCreditEndOfDayPLUpdateUser();
            if ( pLUpdateUser != null )
            {
                usr = UserFactory.getUser( pLUpdateUser );
            }
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg, usr != null ? usr : getUser() );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingLimitAmount( existingLimit );
            auditFacade.setNewLimitAmount(newLimit);
            if ( calc != null )
            {
                auditFacade.setExistingNettingMethodology( calc );
            }
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCreditBalanceUpdate : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditOrgLevelSetDailyPL(Organization creditProviderOrg, Boolean dailyPL)
    {
        try
        {
            String action = dailyPL != null && dailyPL ? CreditMessageEvent.ENABLEDAILYPL.getName() : CreditMessageEvent.DISABLEDAILYPL.getName();
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel(CreditAuditEventParameters.ORG_LEVEL);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelSetDailyPL : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditCounterpartyLevelSetDailyPL(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Boolean newDailyPL)
    {
        try
        {
            String action = newDailyPL != null && newDailyPL ? CreditMessageEvent.ENABLEDAILYPL.getName() : CreditMessageEvent.DISABLEDAILYPL.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
          /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelSetDailyPL : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    /**
     * Creates the new audit event and set it with common properties and wraps it with a audit event facade and returns the facade.
     *
     * @param action            action name indicating what was changed
     * @param type              type of audit event
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit trading party
     * @param creditCptyOrg     credit cpty org
     * @param user              user
     * @return audit event
     */
    private CreditLimitAdminAuditEventFacade init( String action, String type, Organization creditProviderOrg, TradingParty creditCpty, Organization creditCptyOrg, User user )
    {
        AuditEvent newAuditEvent = AuditFactory.newAuditEvent( CreditAuditEventParameters.CREDIT_ADMIN_AUDIT_COMPONENT, action, type );
        //  AuditEvent registeredEvent = ( AuditEvent ) newAuditEvent.getRegisteredObject();
        // registeredEvent.setNamespace( ( Namespace ) creditProviderOrg.getNamespace().getRegisteredObject() );
        CreditLimitAdminAuditEventFacade auditFacade = ( CreditLimitAdminAuditEventFacade ) newAuditEvent.getFacade( CreditLimitAdminAuditEventFacade.FACADE_NAME );  //registeredEvent.getFacade( CreditLimitAdminAuditEventFacade.FACADE_NAME );
        //  registeredEvent.setFacadeName( auditFacade.getClass().getName() );
        newAuditEvent.setFacadeName( auditFacade.getClass().getName() );
        if ( user == null )
        {
            user = getUser ();
        }

        User registeredUser = ( User ) user.getRegisteredObject();
        auditFacade.setUser( registeredUser != null ? registeredUser : user );

        if ( creditProviderOrg != null )
        {
            auditFacade.setCreditProviderOrganization( creditProviderOrg );
            Currency limitCcy = CreditUtilC.getCounterpartyCreditLimitCurrency( creditProviderOrg, creditCptyOrg, creditCpty );
            if ( limitCcy != null )
            {
                auditFacade.setLimitCurrency( limitCcy );
            }
        }
        if ( creditCpty != null )
        {
            auditFacade.setCreditCounterparty( creditCpty );
            auditFacade.setCreditCounterpartyShortName( creditCpty.getShortName() );
        }
        if ( creditCptyOrg != null )
        {
            auditFacade.setCreditCounterpartyOrganization( creditCptyOrg );
            if ( creditCpty == null )
            {
                auditFacade.setCreditCounterpartyShortName( creditCptyOrg.getShortName() );
            }
        }

        return auditFacade;
    }

    /**
     * Creates the new audit event and set it with common properties and wraps it with a audit event facade and returns the facade.
     *
     * @param action            action name indicating what was changed
     * @param type              type of audit event
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit trading party
     * @param creditCptyOrg     credit cpty org
     * @return audit event
     */
    private CreditLimitAdminAuditEventFacade init( String action, String type, Organization creditProviderOrg, TradingParty creditCpty, Organization creditCptyOrg )
    {
        return init( action, type, creditProviderOrg, creditCpty, creditCptyOrg, getUser() );
    }

    /**
     * Returns the logged in user.
     *
     * @return user
     */
    private User getUser()
    {
        return IdcUtilC.getSessionContextUser ();
    }

    @Override
    public void auditCounterpartyLevelCreditStatus(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, String existingCreditStatus, String newCreditStatus)
    {
        try
        {
            String action = CreditMessageEvent.SETCREDITSTATUS.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingCreditStatus( existingCreditStatus );
            auditFacade.setNewCreditStatus( newCreditStatus );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCreditStatus : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditCounterpartyLevelExternalCreditEnabled(Organization creditProviderOrg, boolean isCptyOrgLevel, TradingParty creditCpty,
                                                            Organization creditCptyOrg, boolean externalCreditEnabled)
    {
        try
        {
            String action = externalCreditEnabled ? CreditMessageEvent.ENABLEEXTERNALCREDIT.getName() : CreditMessageEvent.DISABLEEXTERNALCREDIT.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit("CREDITADMIN", audit);
        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelExternalCreditLimitProvider : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditOrgLevelCreditStatus(Organization creditProviderOrg, String existingCreditStatus, String newCreditStatus)
    {
        try
        {
            String action = CreditMessageEvent.SETCREDITSTATUS.getName();
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setExistingCreditStatus( existingCreditStatus );
            auditFacade.setNewCreditStatus( newCreditStatus );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelCreditStatus : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditCurrencyPairGroupExemption(Organization creditProviderOrg,
                                                CurrencyPairGroup existingCpg, CurrencyPairGroup newCpg)
    {
        try
        {
            String action = CreditMessageEvent.EXEMPTCURRENCYPAIRGROUP.getName();
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, creditProviderOrg, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setExistingCurrencyPairGroupExemption((null != existingCpg) ? existingCpg.getShortName() : NONE);
            auditFacade.setNewCurrencyPairGroupExemption((null != newCpg) ? newCpg.getShortName() : NONE);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );


        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCurrencyPairGroupExemption : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditCounterpartyLevelCurrencyPairGroupExemption(Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, CurrencyPairGroup existingCpg, CurrencyPairGroup newCpg)
    {
        try
        {
            String action = CreditMessageEvent.EXEMPTCURRENCYPAIRGROUP.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingCurrencyPairGroupExemption( ( null != existingCpg ) ? existingCpg.getShortName() : NONE );
            auditFacade.setNewCurrencyPairGroupExemption( ( null != newCpg ) ? newCpg.getShortName() : NONE );
            auditFacade.setNamespace( cpo.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCurrencyPairGroupExemption : org=" + cpo, e );
        }
    }

    @Override
    public void auditCounterpartyLevelUseDefaultExemptCurrencyPairGroup(Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, boolean useDefaultExempt)
    {
        try
        {
            String action = useDefaultExempt ? CreditMessageEvent.USEDEFAULTEXEMPTCURRENCYPAIRGROUP.getName() : CreditMessageEvent.USECPTYLEVELEXEMPTCURRENCYPAIRGROUP.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel(actionLevel);
            auditFacade.setNamespace( cpo.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelUseDefaultExemptCurrencyPairGroup : org=" + cpo, e );
        }
    }

    @Override
    public void auditCounterpartyLevelCurrencyPairGroupExemption(Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, CurrencyPairGroup existingCpg, CurrencyPairGroup newCpg, boolean exsitingDefault, boolean useDefault)
    {
        try
        {
            String action = CreditMessageEvent.EXEMPTCURRENCYPAIRGROUP.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            String existingName = "Default";
            if ( !exsitingDefault )
            {
                existingName = ( null != existingCpg ) ? existingCpg.getShortName() : NONE;
            }
            auditFacade.setExistingCurrencyPairGroupExemption( existingName );
            String newName = "Default";
            if ( !useDefault )
            {
                newName = ( null != newCpg ) ? newCpg.getShortName() : NONE;
            }
            auditFacade.setNewCurrencyPairGroupExemption(newName);
            auditFacade.setNamespace( cpo.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCurrencyPairGroupExemption : org=" + cpo, e );
        }
    }

    @Override
    public void auditOrgLevelCreditTenorProfile(Organization cpo, CreditTenorProfile existingCtp, CreditTenorProfile newCtp, String action)
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init(action, CreditAuditEventParameters.ORG_LEVEL, cpo, null, null);
            auditFacade.setActionLevel(CreditAuditEventParameters.ORG_LEVEL);
            auditFacade.setExistingTenorProfile( ( null != existingCtp ) ? existingCtp.getShortName() : NONE );
            auditFacade.setNewTenorProfile((null != newCtp) ? newCtp.getShortName() : NONE);
            auditFacade.setNamespace( cpo.getNamespace() );

           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelCreditTenorProfile : org=" + cpo, e );
        }
    }

    @Override
    public void auditOrgLevelPFEConfiguration(Organization cpo, PFEConfiguration existingPFE, PFEConfiguration newPFE, String action)
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, cpo, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setExistingPFEConfiguration((null != existingPFE) ? existingPFE.getShortName() : NONE);
            auditFacade.setNewPFEConfiguration((null != newPFE) ? newPFE.getShortName() : NONE);
            auditFacade.setNamespace( cpo.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelPFEConfiguration : org=" + cpo, e );
        }
    }
    @Override
    public void auditCounterpartyLevelPFEConfiguration(Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, PFEConfiguration existingPFE, PFEConfiguration newPFE)
    {
        try
        {
            String action = CreditMessageEvent.SETPFECONFIGURATION.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingPFEConfiguration( ( null != existingPFE ) ? existingPFE.getShortName() : NONE );
            auditFacade.setNewPFEConfiguration( ( null != newPFE ) ? newPFE.getShortName() : NONE );
            auditFacade.setNamespace( cpo.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelPFEConfiguration : org=" + cpo, e );
        }
    }

    @Override
    public void auditCounterpartyLevelCreditTenorProfile(Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, CreditTenorProfile existingCtp, CreditTenorProfile newCtp)
    {
        try
        {
            String action = CreditMessageEvent.SETCREDITTENORPROFILE.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingTenorProfile((null != existingCtp) ? existingCtp.getShortName() : NONE);
            auditFacade.setNewTenorProfile((null != newCtp) ? newCtp.getShortName() : NONE);
            auditFacade.setNamespace( cpo.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCreditTenorProfile : org=" + cpo, e );
        }
    }

    /**
     *
     * @param cpo
     * @param currentUsePFE
     * @param newUsePFE
     */
    @Override
    public void auditUsePFE(Organization cpo, boolean currentUsePFE, boolean newUsePFE)
    {
        auditUsePFE(cpo,false,null,null,currentUsePFE,newUsePFE);
    }

    /**
     *
     * @param cpo
     * @param isCptyOrgLevel
     * @param creditCpty
     * @param creditCptyOrg
     * @param currentUsePFE
     * @param newUsePFE
     */
    @Override
    public void auditUsePFE(Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, boolean currentUsePFE, boolean newUsePFE)
    {
        try
        {
            String action = CreditMessageEvent.SETUSEPFECONFIGURATION.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingUsePFEConfig( String.valueOf(currentUsePFE ));
            auditFacade.setNewUsePFEConfig(  String.valueOf(newUsePFE ));
            auditFacade.setNamespace( cpo.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditUsePFE : org=" + cpo, e );
        }
    }

    @Override
    public void auditCounterpartyLevelCreditTenorProfile(Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, CreditTenorProfile existingCtp, CreditTenorProfile newCtp, boolean useDefault, boolean existingUseDefault)
    {
        try
        {
            String action = CreditMessageEvent.SETCREDITTENORPROFILE.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            String newName = "Default";
            if ( !useDefault )
            {
                newName = ( null != newCtp ) ? newCtp.getShortName() : NONE;
            }
            String existingName = "Default";
            if ( !existingUseDefault )
            {
                existingName = ( null != existingCtp ) ? existingCtp.getShortName() : NONE;
            }
            auditFacade.setExistingTenorProfile(existingName);
            auditFacade.setNewTenorProfile(newName);
            auditFacade.setNamespace( cpo.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCreditTenorProfile : org=" + cpo, e );
        }
    }


    @Override
    public void auditCounterpartyLevelUseDefaultTenorProfile(Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, boolean useDefaultTenorProfile)
    {
        try
        {
            String action = useDefaultTenorProfile ? CreditMessageEvent.USEDEFAULTTENORPROFILE.getName() : CreditMessageEvent.USECPTYLEVELTENORPROFILE.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel(actionLevel);
            auditFacade.setNamespace( cpo.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelUseDefaultTenorProfile : org=" + cpo, e );
        }
    }

    @Override
    public void auditOrgLevelCreditMode(Organization cpo, int existingMode, int newMode, String action)
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, cpo, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setExistingMode( CreditLimit.CREDIT_MODES[existingMode] );
            auditFacade.setNewMode(CreditLimit.CREDIT_MODES[newMode]);
            auditFacade.setNamespace( cpo.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelCreditMode : org=" + cpo, e );
        }
    }


    @Override
    public void auditOrgLevelCreditPFEMode(Organization cpo, int existingPFEMode, int newPFEMode, String action)
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init( action, CreditAuditEventParameters.ORG_LEVEL, cpo, null, null );
            auditFacade.setActionLevel( CreditAuditEventParameters.ORG_LEVEL );
            auditFacade.setExistingPFEExcludeMode(CreditLimit.PFE_MODE[existingPFEMode]);
            auditFacade.setPFEExcludeMode(CreditLimit.PFE_MODE[newPFEMode]);
            auditFacade.setNamespace( cpo.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgLevelCreditPFEMode : org=" + cpo, e );
        }
    }

    @Override
    public void auditCounterpartyLevelCreditPFEMode(Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Integer existingPFEMode, int newPFEMode)
    {
        try
        {
            String action = CreditMessageEvent.SETCREDITPFEMODE.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            int currentPFEMode = existingPFEMode != null ? existingPFEMode : CreditLimit.PFE_MODE_DEFAULT;
            String existingName = CreditLimit.PFE_MODE[currentPFEMode];
            auditFacade.setExistingPFEExcludeMode( existingName );
            String newName = CreditLimit.PFE_MODE[newPFEMode];
            auditFacade.setPFEExcludeMode( newName );
            auditFacade.setNamespace( cpo.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCreditPFEMode : org=" + cpo, e );
        }
    }
    
    @Override
    public void auditCounterpartyLevelCreditMode(Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Integer existingMode, Integer newMode)
    {
        try
        {
            String action = CreditMessageEvent.SETCREDITMODE.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            String existingName = existingMode == null ? NONE : CreditLimit.CREDIT_MODES[existingMode];
            auditFacade.setExistingMode(existingName);
            String newName = newMode == null ? NONE : CreditLimit.CREDIT_MODES[newMode];
            auditFacade.setNewMode(newName);
            auditFacade.setNamespace( cpo.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCreditMode : org=" + cpo, e );
        }
    }

    @Override
    public void auditCounterpartyLevelUseDefaultMode(Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, boolean useDefaultMode)
    {
        try
        {
            String action = useDefaultMode ? CreditMessageEvent.USEDEFAULTCREDITMODE.getName() : CreditMessageEvent.USECPTYLEVELCREDITMODE.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel(actionLevel);
            auditFacade.setNamespace( cpo.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelUseDefaultMode : org=" + cpo, e );
        }
    }

    @Override
    public void auditCounterpartyLevelCreditMode(Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, Integer existingMode, Integer newMode, boolean exsitingDefault, boolean useDefault)
    {
        try
        {
            String action = CreditMessageEvent.SETCREDITMODE.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            String existingName = "Default";
            if ( !exsitingDefault )
            {
                existingName = existingMode == null ? NONE : CreditLimit.CREDIT_MODES[existingMode];
            }
            auditFacade.setExistingMode( existingName );
            String newName = "Default";
            if ( !useDefault )
            {
                newName = newMode == null ? NONE : CreditLimit.CREDIT_MODES[newMode];
            }
            auditFacade.setNewMode(newName);
            auditFacade.setNamespace( cpo.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCreditMode : org=" + cpo, e );
        }
    }

    @Override
    public void auditCounterpartyLevelExternalCreditLimitProvider(Organization creditProviderOrg, boolean isCptyOrgLevel,
                                                                  TradingParty creditCpty, Organization creditCptyOrg, String existingCreditLimitProvider, String newCreditLimitProvider)
    {
        try
        {
            String action = CreditMessageEvent.UPDATE_EXTERNAL_CREDITLIMIT_PROVIDER.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit("CREDITADMIN", audit);

            //auditFacade.setExistingCreditLimitProvider( existingCreditLimitProvider );
            //auditFacade.setNewCreditLimitProvider( newCreditLimitProvider );
        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelExternalCreditLimitProvider : org=" + creditProviderOrg + ",cpty=" + creditCpty, e );
        }
    }

    @Override
    public void auditLEOverride(Organization cpo, Organization cco,TradingParty creditCpty, boolean existingLeOverride, boolean leOverride)
    {
        try
        {
            String action = CreditMessageEvent.SETLEOVERRIDE.getName();
            String actionLevel =  CreditAuditEventParameters.CPTY_ORG_LEVEL ;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, cco );
            auditFacade.setActionLevel( actionLevel );
            String existingName = String.valueOf(existingLeOverride);
            auditFacade.setExistingLEOverride( existingName);
            String newName =  String.valueOf(leOverride);
            auditFacade.setLEOverride( newName);
            auditFacade.setNamespace( cpo.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );
        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditLEOverride : org=" + cpo + ",cpty=" + cco, e );
        }
    }

    @Override
    public void auditOrgDefault(Organization cpo, Organization cco,TradingParty creditCpty, boolean existingOrgDefault, boolean orgDefault)
    {
        try
        {
            String action = CreditMessageEvent.SETORGDEFAULT.getName();
            String actionLevel =  CreditAuditEventParameters.CPTY_ORG_LEVEL ;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, cco );
            auditFacade.setActionLevel( actionLevel );
            String existingName = String.valueOf(existingOrgDefault);
            auditFacade.setExistingOrgDefault(existingName);
            String newName =  String.valueOf(orgDefault);
            auditFacade.setOrgDefault(newName);
            auditFacade.setNamespace( cpo.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );
        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditOrgDefault : org=" + cpo + ",cpty=" + cco, e );
        }
    }

    @Override
    public void auditTenorRestrictionsInBusinessDays(Organization cpo, boolean isCptyOrgLevel, Organization cco,TradingParty creditCpty, boolean existingTenorInBusinessDays, boolean tenorInBusinessDays )
    {
        try
        {
            String action = CreditMessageEvent.SETTENORBUSINESSDAYS.getName();
            String actionLevel =  isCptyOrgLevel ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL ;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, cco );
            auditFacade.setActionLevel( actionLevel );
            String existingName = String.valueOf( existingTenorInBusinessDays );
            auditFacade.setExistingTenorInBusinessDays( existingName );
            String newName =  String.valueOf( tenorInBusinessDays );
            auditFacade.setTenorInBusinessDays( newName );
            auditFacade.setNamespace( cpo.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = ( AuditEventC ) auditFacade.getAuditEvent();
            enrichAuditData( audit );
            audit.setModifiedEntity( cpo.getShortName() );
            audit.setModifiedNamespace( cpo.getNamespace().getShortName() );
            AuditManager.audit( CreditLimit.CREDIT_ADMIN, audit );
        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditTenorRestrictionsInBusinessDays : org=" + cpo + ",cpty=" + cco, e );
        }
    }

    @Override
    public void auditDeposit(Organization cpo, boolean isCptyOrgLevel, TradingParty cc, Organization cco, Currency ccy, double amount )
    {
        try
        {
            String action = CreditMessageEvent.DEPOSIT.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, cc, cco );
            auditFacade.setActionLevel( actionLevel );

            auditFacade.setDepositWithdrawCurrency ( ccy );
            auditFacade.setDepositWithdrawAmount ( amount );

            auditFacade.setNamespace( cpo.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );
            log.info( "CLAAM.auditDeposit : audited deposit for cpo=" + cpo + ",cc=" + cc + ",cco=" + cco + ",ccy=" + ccy + ",amount=" + amount );
        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditDeposit : org=" + cpo + ",cc=" + cc + ",cco=" + cco + ",ccy=" + ccy + ",amount=" + amount , e );
        }
    }

    @Override
    public void auditWithdraw(Organization cpo, boolean isCptyOrgLevel, TradingParty cc, Organization cco, Currency ccy, double amount )
    {
        try
        {
            String action = CreditMessageEvent.WITHDRAW.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, cc, cco );
            auditFacade.setActionLevel( actionLevel );

            auditFacade.setDepositWithdrawCurrency ( ccy );
            auditFacade.setDepositWithdrawAmount ( amount );

            auditFacade.setNamespace( cpo.getNamespace() );
            /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );
            log.info( "CLAAM.auditWithdraw : audited withdraw for cpo=" + cpo + ",cc=" + cc + ",cco=" + cco + ",ccy=" + ccy + ",amount=" + amount );
        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditWithdraw : org=" + cpo + ",cc=" + cc + ",cco=" + cco + ",ccy=" + ccy + ",amount=" + amount , e );
        }
    }

    @Override
    public void auditCreditOverride( Organization creditProviderOrg, TradingParty creditCpty, Organization creditCptyOrg, User overrideUser, String tradeDescription )
    {
        try
        {
            CreditLimitAdminAuditEventFacade auditFacade = init( CreditAuditEventParameters.CREDIT_OVERRIDE, "", creditProviderOrg, creditCpty, creditCptyOrg );
            auditFacade.setTradeDescription(tradeDescription);
            auditFacade.setNamespace( creditProviderOrg.getNamespace() );
            auditFacade.setCreditOverrideUser ( overrideUser );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(creditProviderOrg.getShortName());
            audit.setModifiedNamespace(creditProviderOrg.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );
        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCreditOverride : org=" + creditProviderOrg, e );
        }
    }

    @Override
    public void auditUseCreditLimitRuleLevelTenorCoefficients( Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, boolean currentUseCLRLevelTenorCoefficients, boolean newUseCLRLevelTenorCoefficients )
    {
        try
        {
            String action = CreditMessageEvent.SETUSECREDITLIMITRULELEVELTENORCOEFFICIENTS.getName();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingUseCreditLimitRuleLevelTenorCoefficients ( String.valueOf( currentUseCLRLevelTenorCoefficients ) );
            auditFacade.setNewUseCreditLimitRuleLevelTenorCoefficients ( String.valueOf( newUseCLRLevelTenorCoefficients ) );
            auditFacade.setNamespace( cpo.getNamespace() );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditUsePFE : org=" + cpo, e );
        }
    }

    @Override
    public void auditCounterpartyLevelCreditLimitRuleLevelCreditTenorProfile(Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, CreditLimitClassification clsf, CreditTenorProfile existingCtp, CreditTenorProfile newCtp)
    {
        try
        {
            String action = CreditMessageEvent.SETCREDITTENORPROFILE.getName() + clsf.getShortName ();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingTenorProfile((null != existingCtp) ? existingCtp.getShortName() : NONE);
            auditFacade.setNewTenorProfile((null != newCtp) ? newCtp.getShortName() : NONE);
            auditFacade.setNamespace( cpo.getNamespace() );
            auditFacade.setCreditType ( CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION.isSameAs ( clsf ) ? CreditLimitConstants.CREDIT_TYPE_AGGREGATE : CreditLimitConstants.CREDIT_TYPE_DAILY );
           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCreditLimitRuleLevelCreditTenorProfile : org=" + cpo, e );
        }
    }

    @Override
    public void auditCounterpartyLevelCreditLimitRuleLevelPFEConfiguration(Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, CreditLimitClassification clsf, PFEConfiguration existingPFE, PFEConfiguration newPFE)
    {
        try
        {
            String action = CreditMessageEvent.SETPFECONFIGURATION.getName () + clsf.getShortName ();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingPFEConfiguration( ( null != existingPFE ) ? existingPFE.getShortName() : NONE );
            auditFacade.setNewPFEConfiguration( ( null != newPFE ) ? newPFE.getShortName() : NONE );
            auditFacade.setNamespace( cpo.getNamespace() );
            auditFacade.setCreditType ( CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION.isSameAs ( clsf ) ? CreditLimitConstants.CREDIT_TYPE_AGGREGATE : CreditLimitConstants.CREDIT_TYPE_DAILY );

           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );
        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCounterpartyLevelCreditLimitRuleLevelPFEConfiguration : org=" + cpo + ",cc=" + creditCpty + ",clsf=" + clsf, e );
        }
    }

    @Override
    public void auditCreditLimitRuleLevelUsePFE(Organization cpo, boolean isCptyOrgLevel, TradingParty creditCpty, Organization creditCptyOrg, CreditLimitClassification clsf, boolean currentUsePFE, boolean newUsePFE )
    {
        try
        {
            String action = CreditMessageEvent.SETUSEPFECONFIGURATION.getName() + clsf.getShortName ();
            String actionLevel = ( isCptyOrgLevel ) ? CreditAuditEventParameters.CPTY_ORG_LEVEL : CreditAuditEventParameters.CPTY_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, creditCpty, creditCptyOrg );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingUsePFEConfig( String.valueOf(currentUsePFE ));
            auditFacade.setNewUsePFEConfig(  String.valueOf(newUsePFE ));
            auditFacade.setNamespace( cpo.getNamespace() );
            auditFacade.setCreditType ( CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION.isSameAs ( clsf ) ? CreditLimitConstants.CREDIT_TYPE_AGGREGATE : CreditLimitConstants.CREDIT_TYPE_DAILY );

           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );

        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditCreditLimitRuleLevelUsePFE : org=" + cpo + ",cc=" + creditCpty + ",clsf=" + clsf, e );
        }
    }

    @Override
    public void auditUpdateGrossSpreadMargin( Organization cpo, CreditTenorProfile ctp, double existingMargin, double newMargin )
    {
        try
        {
            String action = CreditMessageEvent.UPDATEGROSSSPREADMARGIN.getName();
            String actionLevel = CreditAuditEventParameters.ORG_LEVEL;
            CreditLimitAdminAuditEventFacade auditFacade = init( action, actionLevel, cpo, null, null );
            auditFacade.setActionLevel( actionLevel );
            auditFacade.setExistingTenorProfile( ctp.getShortName () );
            auditFacade.setExistingGrossSpreadMargin ( existingMargin );
            auditFacade.setNewGrossSpreadMargin ( newMargin );
            auditFacade.setNamespace( cpo.getNamespace() );
            auditFacade.setCreditType ( CreditLimitConstants.CREDIT_TYPE_AGGREGATE );

           /*
             Setting audit metadata fields
            */
            AuditEventC audit = (AuditEventC) auditFacade.getAuditEvent();
            enrichAuditData(audit);
            audit.setModifiedEntity(cpo.getShortName());
            audit.setModifiedNamespace(cpo.getNamespace().getShortName());
            AuditManager.audit( "CREDITADMIN", audit );
        }
        catch ( Exception e )
        {
            log.error( "CLAAM.auditUpdateGrossSpreadMargin : org=" + cpo + ",ctp=" + ctp + ",existingMargin="
                    + existingMargin + ",newMargin=" + newMargin, e );
        }
    }
}

