package com.integral.finance.creditLimit.quickcheck;

public class CreditLineCollectionKey {
	
	private int lpLeIndex;
	private int fiLeIndex;
	private int ccyPairIndex;
	
	public CreditLineCollectionKey(){}
	
	public CreditLineCollectionKey(int lpLeIdx, int fiLeIdx, int cpIdx)
	{
		this();
		this.lpLeIndex = lpLeIdx;
		this.fiLeIndex = fiLeIdx;
		this.ccyPairIndex = cpIdx;
	}
	
	public int getLpLeIndex()
	{
		return lpLeIndex;
	}
	
	public int getFiLeIndex()
	{
		return fiLeIndex;
	}
	
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ccyPairIndex;
		result = prime * result + fiLeIndex;
		result = prime * result + lpLeIndex;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CreditLineCollectionKey other = (CreditLineCollectionKey) obj;
		if (ccyPairIndex != other.ccyPairIndex)
			return false;
		if (fiLeIndex != other.fiLeIndex)
			return false;
		if (lpLeIndex != other.lpLeIndex)
			return false;
		return true;
	}

}
