package com.integral.finance.creditLimit.handler;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.finance.creditLimit.admin.CreditAdminServiceLoggerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.user.Organization;
import com.integral.user.OrganizationRelationship;

import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;

/**
 * This class is used to re-initialize the credit data for a credit provider organization. Existing credit data will
 * be deleted and new credit limit rules and utilizations will be created for the credit provider.
 *
 * <AUTHOR> Development Corp.
 */
public class ReinitializeCreditProviderHandlerC extends ReinitializeCreditHandlerC
{
    protected Log log = LogFactory.getLog( CreditAdminServiceLoggerC.class );
    protected static int reinitializeCount;

    /**
     * Instance variable for credit provider organization whose counterparty credit limit rules are going to be reinitialized.
     */
    private Organization creditProviderOrg;

    public ReinitializeCreditProviderHandlerC( Organization providerOrg )
    {
        this.creditProviderOrg = providerOrg;
    }

    public Message handle( Message message )
    {
        int count = ++reinitializeCount;
        try
        {
            log.warn( new StringBuilder( 200 ).append( "ReinitializeCreditProviderHandlerC.handle.INFO : Begin reinitializing credit for the provider org=" )
                    .append( creditProviderOrg ).append( ",user=" ).append( CreditUtilC.getSessionContextUser() ).append( ",count=" )
                    .append( count ).toString() );

            Collection orgRelations = creditProviderOrg.getOrganizationRelationships();
            Collection<Organization> cptyOrgs = new HashSet<Organization>( orgRelations.size() );
            Iterator iter = orgRelations.iterator();
            while ( iter.hasNext() )
            {
                OrganizationRelationship orgRelation = ( OrganizationRelationship ) iter.next();
                cptyOrgs.add( orgRelation.getRelatedOrganization() );
            }

            // add any counterparty organizations which are available in the rule set in addition to the relationship.
            Collection<Organization> rulesetOrgs = CreditUtilC.getCounterpartyOrganizations( creditProviderOrg );
            cptyOrgs.addAll( rulesetOrgs );

            for ( Organization org : cptyOrgs )
            {
                reinitializeCreditCounterpartyOrganization( creditProviderOrg, org );
            }

            // reset the credit enabled cache.
            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().resetCreditEnabled( creditProviderOrg, null );

            // reset the credit utilization cache for the credit provider.
            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeProviderOrganizationCreditUtilizations( creditProviderOrg );

            log.warn( new StringBuilder( 200 ).append( "ReinitializeCreditProviderHandlerC.handle.INFO : Finished reinitializing credit for the provider org=" )
                    .append( creditProviderOrg ).append( ",user=" ).append( CreditUtilC.getSessionContextUser() ).append( ",count=" )
                    .append( count ).toString() );
        }
        catch ( Exception e )
        {
            log.error( new StringBuilder( 200 ).append( "ReinitializeCreditProviderHandlerC.handle.ERROR : Error reinitializing credit for creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",user=" ).append( CreditUtilC.getSessionContextUser() ).append( ",count=" )
                    .append( count ).toString(), e );
        }
        return message;
    }

}

