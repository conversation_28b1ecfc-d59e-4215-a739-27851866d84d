package com.integral.finance.creditLimit.audit;

// Copyright (c) 2001-2006 Integral Development Corp. All rights reserved.

import com.integral.finance.creditLimit.CreditLimit;
import com.integral.finance.dealing.audit.AuditEventParameters;

/**
 * The interface defines the names of the fields in the underlying credit audit event for a facade.
 * It also defines the names of the formats using which the messages are formatted.
 * It is a good idea to be using the audit event field names from this interface instead of hardcoding them
 * in the facade classes.
 */
public interface CreditAuditEventParameters extends AuditEventParameters
{
    String ENTITY1SHORTNAME = "entity1ShortName";
    String ENTITY3SHORTNAME = "entity3ShortName";
    String ENTITY8SHORTNAME = "entity8ShortName";
    String ENTITY7LONGNAME = "entity7.longName";
    String ENTITY8LONGNAME = "entity8.longName";

    String CREDIT_PROVIDER_ORGANIZATION = "creditProviderOrganization";
    String CREDIT_LIMIT_RULESET = "creditLimitRuleSet";
    String CREDIT_COUNTERPARTY_ORGANIZATION = "creditCounterpartyOrganization";
    String CREDIT_PROVIDER_LEGAL_ENTITY = "creditProviderLegalEntity";
    String CREDIT_COUNTERPARTY = "creditCounterparty";
    String LIMIT_CURRENCY = "limitCurrency";
    String EXISTING_LIMIT_CURRENCY = "existingLmitCurrency";
    String EXISTING_NETTING_METHODOLOGY = "ExistingNettingMethodology";
    String NEW_NETTING_METHODOLOGY = "newNettingMethodology";
    String SETTLEMENT_DATE = "settlementDate";
    String EXISTING_LIMIT_AMOUNT = "existingLimitAmount";
    String NEW_LIMIT_AMOUNT = "newLimitAmount";
    String EXISTING_NOTIFICATION_PERCENTAGE = "existingNotificationPercentage";
    String NEW_NOTIFICATION_PERCENTAGE = "newNotificationPercentage";
    String EXISTING_WARNING_PERCENTAGE = "existingWarningPercentage";
    String NEW_WARNING_PERCENTAGE = "newWarningPercentage";
    String EXISTING_SUSPENSION_PERCENTAGE = "existingSuspensionPercentage";
    String NEW_SUSPENSION_PERCENTAGE = "newSuspensionPercentage";
    String EXISTING_STOPOUT_PERCENTAGE = "existingStopOutPercentage";
    String NEW_STOPOUT_PERCENTAGE = "newStopOutPercentage";
    String EXISTING_EMAIL_ADDRESS = "existingEmailAddress";
    String NEW_EMAIL_ADDRESS = "newEmailAddress";
    String EXISTING_SENDER_EMAIL_ADDRESS = "existingSenderEmailAddress";
    String NEW_SENDER_EMAIL_ADDRESS = "newSenderEmailAddress";
    String EXISTING_DAILY_EXPOSURE_HORIZON = "existingDailyExposureHorizon";
    String NEW_DAILY_EXPOSURE_HORIZON = "newDailyExposureHorizon";
    String EXISTING_CREDIT_EXPOSURE_LEVEL = "existingCreditExposureLevel";
    String NEW_CREDIT_EXPOSURE_LEVEL = "newCreditExposureLevel";
    String CREDIT_PROVIDER_ACTION = "creditProviderAction";
    String UTILIZATION_PERCENTAGE = "UtilizationPercentage";
    String TRADE_DESCRIPTION = "TradeDescription";
    String CREDIT_COUNTERPARTY_SHORT_NAME = "creditCounterpartyShortName";
    String EXISTING_LEVERAGE_FACTOR = "existingLeverageFactor";
    String NEW_LEVERAGE_FACTOR = "newLeverageFactor";
    String EXISTING_MAXIMUM_TENOR = "existingMaximumTenor";
    String NEW_MAXIMUM_TENOR = "newMaximumTenor";
    String EXISTING_MINIMUM_TENOR = "existingMinimumTenor";
    String NEW_MINIMUM_TENOR = "newMinimumTenor";
    String EXISTING_CREDIT_STATUS = "existingCreditStatus";
    String NEW_CREDIT_STATUS = "newCreditStatus";
    String EXISTING_CURRENCYPAIR_EXEMPTION = "existingCurrencyPairExemption";
    String NEW_CURRENCYPAIR_EXEMPTION = "newCurrencyPairExemption";
    String EXISTING_TENORPROFILE = "existingTenorProfile";
    String NEW_TENORPROFILE = "newTenorProfile";
    String EXISTING_MODE = "existingMode";
    String NEW_MODE = "newMode";
    String EXISTING_PFE_EXCLUDE_MODE = "existingPFEExcludeMode";
    String PFE_EXCLUDE_MODE = "newPFEExcludeMode";
    String EXISTING_GROSS_SPREAD_MARGIN = "existingGrossSpreadMargin";
    String NEW_GROSS_SPREAD_MARGIN = "newMGrossSpreadMargin";

    String ACTION_LEVEL = "ActionLevel";
    String ORG_LEVEL = "OrgLevel";
    String CPTY_LEVEL = "CptyLevel";
    String CPTY_ORG_LEVEL = "CptyOrgLevel";
    String DAILY_LEVEL = "DailyLevel";
    String EXISTING_LEOVERRIDE = "existingLEOverride";
    String NEW_LEOVERRIDE = "lEOverride";

    String EXISTING_ORGDEFAULT = "existingOrgDefault";
    String ORGDEFAULT = "orgDefault";
    String EXISTING_TENOR_IN_BUSINESS_DAYS = "existingTenorInBusinessDays";
    String TENOR_IN_BUSINESS_DAYS = "tenorInBusinessDays";

    String CREDIT_REJECTION = "CREDITREJECTION";
    String NOTIFICATION_PERCENTAGE_BREACH = "NOTIFICATIONPERCENTAGEBREACH";
    String WARNING_PERCENTAGE_BREACH = "WARNINGPERCENTAGEBREACH";
    String SUSPENSION_PERCENTAGE_BREACH = "SUSPENSIONPERCENTAGEBREACH";
    String MARGIN_CALL_PERCENTAGE_BREACH = "MARGINCALLPERCENTAGEBREACH";
    String STOPOUT_PERCENTAGE_BREACH = "STOPOUTPERCENTAGEBREACH";
    String CREDIT_ADMIN_AUDIT_COMPONENT = CreditLimit.CREDIT_ADMIN;
    String DEPOSIT_WITHDRAW_CURRENCY = "depositWithdrawCurrency";
    String DEPOSIT_WITHDRAW_AMOUNT = "depositWithdrawAmount";
    String CREDIT_OVERRIDE = "CREDITOVERRIDE";
}
