package com.integral.finance.creditLimit.admin;

import java.util.Map;
import com.integral.finance.creditLimit.CreditTenorProfile;

/**
 * 
 * This is a DTO to carry tenor profile details of a given PFEConfig or TenorProfile used from fxiapi
 * 
 * <AUTHOR>
 *
 */
public class CreditTenorProfileDetails
{
	private Map<String, CreditTenorProfile> tenorProfiles;
	private String profileName;
	private double availableAmt;
	
	public CreditTenorProfileDetails(String profileName, Map<String, CreditTenorProfile> profileMap, double avlAmt)
	{
		this.tenorProfiles = profileMap;
		this.profileName = profileName;
		this.availableAmt = avlAmt;
	}
	
	public Map<String, CreditTenorProfile> getTenorProfiles()
	{
		return tenorProfiles; 
	}
	
	public String getProfileName()
	{
		return profileName;
	}
	
	public double getAvailableAmount()
	{
		return availableAmt;
	}
}