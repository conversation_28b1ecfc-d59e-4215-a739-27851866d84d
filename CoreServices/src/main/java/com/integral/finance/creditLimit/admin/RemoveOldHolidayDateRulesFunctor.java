package com.integral.finance.creditLimit.admin;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;

import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.dateGeneration.HolidayCalendar;
import com.integral.finance.dateGeneration.HolidayDateRule;
import com.integral.finance.dateGeneration.mbean.HolidayCalendarMBeanC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.PersistenceFactory;
import com.integral.system.mail.SendEmailC;
import com.integral.time.DatePeriod;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;

public class RemoveOldHolidayDateRulesFunctor extends CreditFunctorC {
	protected static Log LOG = LogFactory.getLog(RemoveOldHolidayDateRulesFunctor.class);
	SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
	private UnitOfWork uow;

	RemoveOldHolidayDateRulesFunctor(String name) {
		setName(name);
	}

	private DatePeriod getPeriodToRemoveOlderHolidayDateRules(String dpStr) {
		DatePeriod dp = null;
		try {
			try {
				dp = DateTimeFactory.newDatePeriod(dpStr);
			} catch (Exception e) {
				LOG.error(
						"getPeriodToRemoveOlderHolidayDateRules : Error in getting date period from configured value");
				dp = DateTimeFactory.newDatePeriod("1M");// this is to protect
															// from user error
															// in the
															// configuration.
			}
		} catch (Exception e) {
			LOG.error("getPeriodToRemoveOlderHolidayDateRules : Error in getting date period");
		}
		return dp;
	}

	private IdcDate getDatePeriodDate(DatePeriod dp) {
		IdcDate currentDate = DateTimeFactory.newDate();
		return dp.subtract(currentDate);
	}

	private String getSubject(int count) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MMM-dd");
		String subject = new StringBuilder( 200 ).append( HolidayCalendarMBeanC.getInstance().getEmailSubjectPrefix() )
				.append ( " Total Older Holiday Date Rules Removed on: " ).append ( sdf.format(new Date()) + " / "
				+ count ).toString();
		if (LOG.isInfoEnabled()) {
			LOG.info("Subject : " + subject);
		}
		return subject;
	}

	@Override
	public void execute() {
		boolean deleteOlderHolidayDateRules = HolidayCalendarMBeanC.getInstance().isRemoveOlderHolidayDateRules();
		String dpStr = HolidayCalendarMBeanC.getInstance().getRemoveOlderHolidayDateRulesPeriod();
		String fromAddress = HolidayCalendarMBeanC.getInstance().getEmailFrom();
		String toAddress = HolidayCalendarMBeanC.getInstance().getEmailTo();
		try {
			int count = 0;
			StringBuilder sb = new StringBuilder();
			if (deleteOlderHolidayDateRules) {
				if (LOG.isInfoEnabled()) {
					LOG.info("Date Period defined in property : " + dpStr);
				}
				DatePeriod dp = getPeriodToRemoveOlderHolidayDateRules(dpStr);
				IdcDate datePeriodDate = getDatePeriodDate(dp);
				if (LOG.isInfoEnabled()) {
					LOG.info("Date Period date : " + datePeriodDate.toString());
				}
				Session aSession = PersistenceFactory.newSession();
				uow = aSession.acquireUnitOfWork();
				Collection<HolidayDateRule> objs = uow.readAllObjects(HolidayDateRule.class);
				for (HolidayDateRule hdr : objs) {
					HolidayCalendar hc = hdr.getHolidayCalendar();

					if (hc == null) {
						continue;
					}
					try {
						IdcDate dateRuleDate = DateTimeFactory.newDate(simpleDateFormat.parse(hdr.getShortName()));
						if (dateRuleDate.isEarlierThan(datePeriodDate)) {
							uow.deleteObject(hdr);
							count++;
							sb.append(hc.getShortName() + "-" + hdr.getShortName()).append("\n");
						}
					} catch (Exception e) {
						LOG.info(e.toString());
					}
				}
				uow.commitAndResume();
				if (LOG.isInfoEnabled()) {
					LOG.info("From : " + fromAddress);
					LOG.info("To : " + toAddress);
					LOG.info("Mail Body : " + sb.toString());
				}
				SendEmailC.sendEmail(fromAddress, toAddress, null, getSubject(count), sb.toString());
			}
			else{
				LOG.info( "CUF.execute : Functor not executed. this=" + this + ",deleteOlderHolidayDateRules=" + deleteOlderHolidayDateRules );
			}
		} catch (Exception e) {
			LOG.error("Error : "+e);
		}

	}

}
