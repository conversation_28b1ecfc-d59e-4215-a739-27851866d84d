package com.integral.finance.creditLimit.functor;

// Copyright (c) 2022 Integral Development Corp.  All rights reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.*;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.HashMap;

/**
 * This class is used to update cache when settings related to auto stop-out changes.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditStopOutNotificationFunctorC extends CreditRemoteNotificationFunctorC
{

    public void onCommit( HashMap props )
    {
        // do not process the message if remote notification processing is not enabled.
        if ( !creditAdminConfig.isCreditRemoteNotificationProcessingEnabled() )
        {
            return;
        }

        // only process in admin server or specifically enabled server.
        if ( !creditAdminConfig.isCreditUtilizationStopOutRevaluationEnabled () && !ConfigurationFactory.getServerMBean ().isAdminServer () )
        {
            return;
        }

        Organization creditProviderOrg = null;
        Organization creditCptyOrg = null;
        TradingParty creditCpty = null;

        log.info( "CSNF.onCommit : Updating auto stop-out configurations. props=" + props );

        try
        {
            String eventName = ( String ) props.get( CreditLimitConstants.EVENT_PROPERTY );
            String creditProviderOrgGuid = ( String ) props.get( CreditLimit.CREDIT_PROVIDER_ORGANIZATION );
            if ( creditProviderOrgGuid != null )
            {
                creditProviderOrg = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditProviderOrgGuid, Organization.class );

                String creditCptyOrgGuid = ( String ) props.get( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION );
                if ( creditCptyOrgGuid != null )
                {
                    creditCptyOrg = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditCptyOrgGuid, Organization.class );
                }
                String creditCptyGuid = ( String ) props.get( CreditLimit.CREDIT_COUNTERPARTY );
                if ( creditCptyGuid != null )
                {
                    creditCpty = ( TradingParty ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditCptyGuid, TradingParty.class );
                }

                if ( log.isDebugEnabled() )
                {
                    log.debug( "CSNF.onCommit : Reset cache for creditProviderOrg=" + creditProviderOrg + ",creditCptyOrg=" + creditCptyOrg + ",eventName=" + eventName );
                }
                if ( creditProviderOrg != null )
                {
                    if ( creditCpty != null )
                    {
                        CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( creditProviderOrg, creditCpty );
                        if ( cclr != null )
                        {
                            CreditUtilizationManagerC.getInstance ().getCreditUtilizationCache ().updateAutoStopOutEnabledRules ( cclr );
                        }
                    }
                    else if ( creditCptyOrg != null )
                    {
                        LegalEntity cptyLe = creditCptyOrg.getDefaultDealingEntity ();
                        if ( cptyLe != null )
                        {
                            TradingParty tp = cptyLe.getTradingParty ( creditProviderOrg );
                            if ( tp != null )
                            {
                                CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( creditProviderOrg, tp );
                                if ( cclr != null )
                                {
                                    CreditUtilizationManagerC.getInstance ().getCreditUtilizationCache ().updateAutoStopOutEnabledRules ( cclr );
                                }
                            }
                        }
                    }
                    else
                    {
                        Collection<CounterpartyCreditLimitRule> rules = ( Collection<CounterpartyCreditLimitRule> ) CreditUtilC.getCreditLimitRuleSet ( creditProviderOrg ).getRules ();
                        for ( CounterpartyCreditLimitRule cclr: rules )
                        {
                            if ( cclr.isActive () )
                            {
                                CreditUtilizationManagerC.getInstance ().getCreditUtilizationCache ().updateAutoStopOutEnabledRules ( cclr );
                            }
                        }
                    }
                }
                else
                {
                    log.warn( "CSNF.onCommit : Null credit provider org. props=" + props + ",guid=" + creditProviderOrgGuid );
                }
            }
            else
            {
                log.warn( "CSNF.onCommit : Null credit provider property. props=" + props );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CSNF.onCommit : Error resetting the cache for auto stop-out settings for org="
                    + creditProviderOrg + ",props=" + props, e );
        }
    }
}

