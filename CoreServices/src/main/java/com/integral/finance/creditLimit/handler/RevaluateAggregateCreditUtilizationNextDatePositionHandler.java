package com.integral.finance.creditLimit.handler;

// Copyright (c) 2018 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;

/**
 * This class is used as an end of day handler to revaluate all the aggregate type credit utilizations.
 *
 * <AUTHOR> Development Corp.
 */
public class RevaluateAggregateCreditUtilizationNextDatePositionHandler implements MessageHandler
{
    protected Log log = LogFactory.getLog( getClass() );

    public Message handle( Message msg )
    {
        try
        {
            CreditUtilizationManagerC.getInstance ().recalculateAllAggregateCreditUtilizationsForNextDate ();
        }
        catch ( Exception e )
        {
            log.error( "RevaluateAggregateCreditUtilizationNextDatePositionHandler.handle. : exception", e );
        }
        return null;
    }
}
