package com.integral.finance.creditLimit.admin;

import com.integral.audit.AuditConverter;
import com.integral.audit.AuditEvent;
import com.integral.audit.AuditEventC;
import com.integral.audit.SpacesAuditEventC;
import com.integral.audit.configuration.AuditConfigurationFactory;
import com.integral.audit.configuration.AuditConfigurationMBean;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.spaces.fx.persistence.ISSpacesPersistenceService;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.spaces.PersistenceConstants;
import com.integral.persistence.spaces.PersistenceServiceConfiguration;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.spaces.*;
import com.integral.spaces.spi.CriteriaSet;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.CursoredStream;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Created with IntelliJ IDEA.
 * User: ranganathanr
 * Date: 4/9/14
 * Time: 10:55 PM
 */
public class AuditDataMigrationFunctor extends CreditFunctorC
{
    protected static AuditConfigurationMBean mbean = AuditConfigurationFactory.getAuditConfigurationMBean();
    private IdcDate fromDate;
    private IdcDate toDate;
    private int migrateMonthRange;
    private String component;
    private String migrateGuid;
    private boolean wasIDmigration;

    private int MAX_STRING_LEN_ADMIN_TAKERPROVISIONING_MONGO = 512;
    private int SPACE_FOR_ELLIPSIS = 5;

    private int numberOfSuccessfulMigrations =0;
    private int numberOfUnSuccessfulMigrations=0;
    private static AtomicLong incrementalCorrelationId = new AtomicLong();

    public void execute()
    {
        try
        {
            if ( mbean.isMigrationEnabled() )
            {
                start();
                // STEP 1 - Initialize Metaspaces
             //  initMetaspaces();

                //STEP 2 - Get the component
                component = mbean.getComponentToMigrate();
                String[] components = component.split(",");

                // STEP 3 - For each component, initialize params and run migration
                for(int i =0; i<components.length; i++)
                {
                  log.info("Performing Audit Migration For "+components[i]);
                  numberOfSuccessfulMigrations =0;
                  numberOfUnSuccessfulMigrations=0;
                  wasIDmigration=false;
                  calculateMigrationInputParams(components[i].trim());
                  migrateAuditRecords(components[i].trim());
                  if(wasIDmigration == false)
                  {
                      persistAuditMigrationDate("MIGRATE_" + components[i].trim());
                  }
                  log.info("Successful Migrated Records "+components[i] +":" + numberOfSuccessfulMigrations);
                  log.info("Un-Successful Migrated Records "+components[i] +":" + numberOfUnSuccessfulMigrations);
                }

                // STEP 4 - Shutdown metaspaces
              //  tearDown();
            }
           else
            {
                log.info("AuditDataMigrationFunctor.execute : Functor not executed Migration is disabled.");
            }
        }
        catch ( Exception e )
        {
            log.error( "AuditDataMigrationFunctorC.execute.ERROR.", e );
        }
        finally
        {
            finish();
        }

        // mark suspension as completed if suspend was initiated.
        if ( suspensionInitiated )
        {
            this.suspended = true;
        }
    }

  /*  protected boolean initMetaspaces()
    {
        JMSDBMBeanC._getInstance();
        JMSManager.initStandalone();
        MetaspacesStartupC msc = new MetaspacesStartupC();
        msc.startup(null, null);
        return true;
    }*/

    private void calculateMigrationInputParams(String component)
    {
        migrateGuid = mbean.getIdToMigrate();
        migrateMonthRange = mbean.getMonthToMigrate();
        calculateMigrationDate("MIGRATE_"+component);
    }

    private void calculateMigrationDate(String component)
    {
        SpacesQueryService.QueryResult<SpaceIterator> result = new SpacesQueryService.QueryResult<SpaceIterator>();
        String namespaceName =  PersistenceConstants.AUDIT.toString();
        String spaceName = PersistenceConstants.AUDIT;
        String metaspaceName = PersistenceServiceConfiguration.getAuditMetaspaceName();
        Metaspace metaspace = Metaspaces.getInstance().getMetaspace(namespaceName, metaspaceName);
        QueryBuilder queryBuilder = metaspace.createNewQueryBuilder(namespaceName, spaceName);
        CriteriaSet cs = metaspace.defaultCriteriaSet();
        Criteria parameterCriteria = cs.is("cmp", component);
        parameterCriteria = cs.and(parameterCriteria, cs.in("nssh","AUDIT"));
        queryBuilder.add(parameterCriteria);
        result.setResult( queryBuilder.build().getIterator(SpacesAuditEventC.class));

        if(result.getResult().count()!=0)
        {
            log.info("Found Migration Date in DB, calculating next date range..");
            //Calculate the most recent date
            SpaceIterator<SpacesAuditEventC> i = result.getResult();
            Calendar c = Calendar.getInstance();
            c.setTime(new Date());
            c.add(Calendar.MONTH, -60);
            SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
            String output = sdf.format(c.getTime());
            Date startDate = new Date(output) ;
            fromDate = DateTimeFactory.newDate(startDate);
            try
            {

                while (i.hasNext())
                {
                    AuditEventC auditEvent = (AuditEventC) AuditConverter.convertSpacesToNonSpaces((SpacesAuditEventC) i.next());
                    startDate =  sdf.parse(auditEvent.getStringArg1());
                    IdcDate tmpDate = DateTimeFactory.newDate(startDate);
                    if(tmpDate.isLaterThanOrEqualTo(fromDate))
                    {
                        fromDate = tmpDate;
                    }
                }
            } catch (ParseException e)
            {
                log.error("Unable to parse incoming date for persisting audit records " + e.getMessage());

            }
            c.setTime(fromDate.asJdkDate());
            c.add(Calendar.MONTH,migrateMonthRange );
            output = sdf.format(c.getTime());
            if(c.getTime().after(new Date()))
            {
                output = sdf.format(new Date());
            }
            startDate = new Date(output) ;
            toDate = DateTimeFactory.newDate(startDate);
        }
        else
        {
            log.info("No migration date in DB, setting to start date");
            fromDate = mbean.getStartDateToMigrate();
            Calendar c = Calendar.getInstance();
            c.setTime(fromDate.asJdkDate());
            c.add(Calendar.MONTH, migrateMonthRange);
            SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
            String output = sdf.format(c.getTime());
            Date tmpToDate = new Date(output);
            if(tmpToDate.after(new Date()))
            {
                tmpToDate = new Date();
            }
            toDate = DateTimeFactory.newDate(tmpToDate);

        }
        if(mbean.useCfgStartDate())
        {
            fromDate = mbean.getStartDateToMigrate();
            Calendar c = Calendar.getInstance();
            c.setTime(fromDate.asJdkDate());
            c.add(Calendar.MONTH, migrateMonthRange);
            SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
            String output = sdf.format(c.getTime());
            Date tmpToDate = new Date(output);
            if(tmpToDate.after(new Date()))
            {
                tmpToDate =  new Date();
            }
            toDate = DateTimeFactory.newDate(tmpToDate);
        }
        log.info("Start Date (inclusive) >= "+ fromDate);
        log.info("To Date (exclusive) < "+ toDate);
    }


    private void migrateAuditRecords(String component)
    {
        CursoredStream cursor = null;
        try
        {
        // 1. Get the Records from Oracle
        ReadAllQuery raq = new ReadAllQuery();
        raq.setReferenceClass(AuditEventC.class);
        ExpressionBuilder expBuilder = new ExpressionBuilder();
        Expression fromDateExp=expBuilder.get("dateTime").greaterThanEqual(fromDate);
        Expression toDateExp=expBuilder.get("dateTime").lessThan(toDate);
        Expression componentExpression=expBuilder.get("component").equalsIgnoreCase(component);


        log.info("QUERY CRITERIA : startDate >= " +fromDate + " , toDate < " + toDate+ " , component = " + component );

        String[] guidStr = migrateGuid.split(",");
        Expression guidExpression = expBuilder.get("objectID").in(guidStr);
        if(migrateGuid.equalsIgnoreCase("") == true)
        {
            raq.setSelectionCriteria(componentExpression.and(fromDateExp).and(toDateExp));
        }
        else
        {
            raq.setSelectionCriteria(guidExpression);
            wasIDmigration= true;
        }

        raq.useCursoredStream( 500, 500 );
        Session session = PersistenceFactory.newSession();
        cursor =  (CursoredStream) session.executeQuery( raq );
        Collection<AuditEvent> events = new ArrayList<AuditEvent>();
        int numberOfRecords =0;
        while ( !cursor.atEnd() )
        {
            AuditEventC event = ( AuditEventC ) cursor.read();
            events.add( event );
            numberOfRecords++;
            if ( numberOfRecords >= 500 )
            {
                persistToMongo(events);
                numberOfRecords = 0;
                events.clear();
                cursor.releasePrevious();
            }

        }
            cursor.close();
            if(numberOfRecords >0)    // last batch
            {
                persistToMongo(events);
                numberOfRecords = 0;
                events.clear();
                cursor.releasePrevious();
            }
        }
        catch (PersistenceException e)
        {
           log.error("Unable to persist object to mongo ");
        }
        finally
        {
            if ( cursor != null )
            {
                cursor.close();
            }
        }
    }

    private long getCorrelationId() {
        return incrementalCorrelationId.incrementAndGet();
    }

    private String getMongoSupportedString(String aString)
    {
        if (aString == null || aString.trim().isEmpty()) {
            return "None";
        }
        if(aString.length() >= MAX_STRING_LEN_ADMIN_TAKERPROVISIONING_MONGO )
        {
            aString = aString.substring(0, MAX_STRING_LEN_ADMIN_TAKERPROVISIONING_MONGO - SPACE_FOR_ELLIPSIS) + "...";
        }

        return aString;
    }
    private void persistToMongo(Collection<AuditEvent> events)
    {
        Iterator<AuditEvent> auditEventCIterator = events.iterator();
        while(auditEventCIterator.hasNext())
        {
         SpacesAuditEventC auditEvent = AuditConverter.convertNonSpacesToSpaces((AuditEventC)auditEventCIterator.next());
         auditEvent.set_id(auditEvent.getAudit_guid());
         auditEvent.setNamespaceName(PersistenceConstants.AUDIT.toString());

         if("ADMIN_TAKERPROVISIONING".equals(auditEvent.getComponent()) )
         {
             auditEvent.setStringArg9(getMongoSupportedString(auditEvent.getStringArg9())); // Old Value of the Action
             auditEvent.setStringArg4(getMongoSupportedString(auditEvent.getStringArg4()));  // New Value of the Action
         }
         try
         {
            ISSpacesPersistenceService auditPersistentService = PersistenceServiceFactory.getAuditPersistenceService();
            ApplicationSpaceEvent ase = auditPersistentService.createEvent(auditEvent,ApplicationEventCodes.EVENT_AUDIT_EVENT_PERSIST);
            long correlationId = getCorrelationId();
            auditPersistentService.persist(ase, Long.toString(correlationId), false, "AUDIT_EVENT");
            numberOfSuccessfulMigrations++;
         }
         catch(Exception e)
         {
             log.error("EXCEPTION: Unable to process record. GUID: " + auditEvent.getAudit_guid(), e);
             numberOfUnSuccessfulMigrations++;
         }

        }
    }


    public void persistAuditMigrationDate(String component)
    {
        //Persist the new date
        ISSpacesPersistenceService auditSessionPersistenceService;
        auditSessionPersistenceService = PersistenceServiceFactory.getAuditPersistenceService();
        SpacesAuditEventC anAuditEvent = new SpacesAuditEventC();
        anAuditEvent.setComponent(component);
        anAuditEvent.set_id(System.currentTimeMillis()+"");
        anAuditEvent.setNamespaceName("AUDIT");
        SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
        String persistDate = sdf.format(toDate.asJdkDate());
        anAuditEvent.setStringArg1(persistDate);
        ApplicationSpaceEvent ase = auditSessionPersistenceService.createEvent(anAuditEvent,ApplicationEventCodes.EVENT_AUDIT_EVENT_PERSIST);
        long correlationId = incrementalCorrelationId.incrementAndGet();
        auditSessionPersistenceService.persist(ase, Long.toString(correlationId), false,"Audit Message");
        log.info("Persisted new migration date to Mongo :" +toDate);
    }

  /*  public void tearDown()
    {
        MetaspacesShutdownTask shutdownTask = new MetaspacesShutdownTask();
        shutdownTask.shutdown(null, null);
    }*/
}