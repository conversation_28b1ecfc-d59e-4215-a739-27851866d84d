package com.integral.finance.creditLimit.quickcheck;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicBoolean;
import com.google.common.util.concurrent.AtomicDouble;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditLimitSubscriptionManagerC;
import com.integral.finance.currency.Currency;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.util.collections.ConcurrentHashSet;

public class CreditLine 
{
	private Currency limitCurrency;
	private AtomicDouble reservedAmtInLimitCcy = new AtomicDouble(0);
	private AtomicBoolean creditSuspended = new AtomicBoolean(false);
	private final ConcurrentMap<String, Double> transactionId2ReservedCreditInLimitCcy = new ConcurrentHashMap<String ,Double>();

	private Map<Currency, AtomicDouble> reservedAmtMap = new ConcurrentHashMap<Currency, AtomicDouble> ( );
	private final ConcurrentMap<String, Map<Currency, AtomicDouble>> transactionId2ReservedMap = new ConcurrentHashMap<String ,Map<Currency, AtomicDouble>>();

	private Log log = LogFactory.getLog( getClass() );
	
	private Set<CreditLineCollection> interestedCreditLineCollections = new ConcurrentHashSet<CreditLineCollection>();

	private boolean orgLevel;

	private boolean cashSettlement;
	
	public CreditLine(Currency limitCurrency, boolean creditSuspended, boolean orgLevelExposure, boolean cash )
	{
		this.limitCurrency = limitCurrency;
		setSuspend(creditSuspended);
		this.orgLevel = orgLevelExposure;
		this.cashSettlement = cash;
	}
	
	public double getReservedAmountInLimitCurrency(){
		return reservedAmtInLimitCcy.get();
	}
	
	public Currency getLimitCurrency()
	{
		return limitCurrency;
	}

	public void setLimitCurrency ( Currency ccy )
	{
		this.limitCurrency = ccy;
	}

	public boolean isOrgLevel()
	{
		return orgLevel;
	}

	public void setOrgLevel ( boolean orgLevelExposure )
	{
		this.orgLevel = orgLevelExposure;
	}

	public boolean isCashSettlement()
	{
		return cashSettlement;
	}

	public void setCashSettlement ( boolean cash )
	{
		this.cashSettlement = cash;
	}

	public double getReserveAmountInCurrency( Currency ccy )
	{
		AtomicDouble dbl = reservedAmtMap.get ( ccy );
		return dbl != null ? dbl.get () : 0.0;
	}

	public boolean reserveCredit( Currency ccy, double requestedCcyAmount, double requestedAmountInLimitCcy, String transactionId, boolean isBid)
	{							
		long t1 = System.currentTimeMillis();
		if ( cashSettlement )
		{
			Map<Currency, AtomicDouble> map = transactionId2ReservedMap.get ( transactionId );
			if ( map != null )
			{
				log.warn ( new StringBuilder ().append ( "reserveCredit: Duplicate credit reserved against TransactionId=" ).
						append ( transactionId ).append ( " | oldValue=" ).append ( map ).
						append ( " | requestedAmount=" ).append ( requestedCcyAmount ).append ( ",ccy=" ).append ( ccy ).toString () );
			}
			else
			{
				map = new ConcurrentHashMap<Currency, AtomicDouble>();
				AtomicDouble dbl = map.get ( ccy );
				if ( dbl == null )
				{
					dbl = new AtomicDouble();
					map.put ( ccy, dbl );
				}
				dbl.addAndGet ( requestedCcyAmount );
				transactionId2ReservedMap.put ( transactionId, map );

				AtomicDouble reserved = reservedAmtMap.get ( ccy );
				if ( reserved == null )
				{
					reserved = new AtomicDouble (  );
					reservedAmtMap.put ( ccy, reserved );
				}
				reserved.addAndGet ( requestedCcyAmount );
			}
		}
		else
		{
			Double oldValue = transactionId2ReservedCreditInLimitCcy.putIfAbsent ( transactionId, requestedAmountInLimitCcy );
			if ( oldValue != null )
			{
				log.info ( new StringBuilder ().append ( "reserveCredit: Duplicate credit reserved against TransactionId=" ).
						append ( transactionId ).append ( " | OldAmtInLimitCcy=" ).append ( oldValue ).
						append ( " | newAmtInLimitCcy=" ).append ( requestedAmountInLimitCcy ).toString () );
			}
			else
			{
				reservedAmtInLimitCcy.addAndGet ( requestedAmountInLimitCcy );
			}
		}
		if(log.isDebugEnabled())
		{
			long t2 = System.currentTimeMillis()-t1;
			log.debug("CreditLine.reserveCredit : TIMETAKEN = " + t2);
		}
		return true;		
	}
	
	public String release( String transactionId )
	{
		String result = null;
		if ( transactionId == null )
		{
			return "Null TransactionId";
		}
		if ( cashSettlement )
		{
			Map<Currency, AtomicDouble> map = transactionId2ReservedMap.remove ( transactionId );
			if ( map != null )
			{
				for ( Currency ccy : map.keySet () )
				{
					AtomicDouble amtBefore = map.get ( ccy );
					if ( amtBefore != null )
					{
						AtomicDouble reserved = reservedAmtMap.get ( ccy );
						double oldReserveAmt = reserved != null ? reserved.get () : 0.0;
						if ( reserved != null && amtBefore.get () > 0 )
						{
							reserved.addAndGet ( - 1 * amtBefore.get () );
						}
						result = new StringBuilder ( 120 ).append ( "{tid=" )
								.append ( transactionId ).append ( ",oldReserveAmt=" ).append ( oldReserveAmt )
								.append ( ",amtBefore=" ).append ( amtBefore ).append ( ",newReservedAmt=" ).
										append ( reserved ).append ( '}' ).toString ();
					}
				}
			}
			else
			{
				log.info ( "CL.release - Not able to find the reserved amount map for txId=" + transactionId );
				result = "Unable to release txId=" + transactionId;
			}
		}
		else
		{
			Double creditReservedInLimitCcy = transactionId2ReservedCreditInLimitCcy.remove ( transactionId );
			double amtBefore = reservedAmtInLimitCcy.get ();
			if ( creditReservedInLimitCcy != null && amtBefore > 0 )
			{
				reservedAmtInLimitCcy.addAndGet ( - 1 * creditReservedInLimitCcy );
			}
			if ( creditReservedInLimitCcy != null )
			{
				result = new StringBuilder ( 120 ).append ( "{tid=" )
						.append ( transactionId ).append ( ",resAmt=" ).append ( creditReservedInLimitCcy )
						.append ( ",amtBefore=" ).append ( amtBefore ).append ( ",amtAfter=" ).
								append ( reservedAmtInLimitCcy.get () ).append ( '}' ).toString ();
			}
		}
		markInterestedCollectionsDirty();
		return result;
	}

    public void roundReservedAmount()
    {
        double amtBeforeRounding = reservedAmtInLimitCcy.get();
        double amtAfterRounding = round( amtBeforeRounding );
        reservedAmtInLimitCcy.compareAndSet( amtBeforeRounding, amtAfterRounding );
    }

    public void markInterestedCollectionsDirty(){
		for(CreditLineCollection creditLineCollection: interestedCreditLineCollections){
			creditLineCollection.markDirty();
		}
	}
	

	public boolean isCreditSuspended(){
		return creditSuspended.get();
	}
	

	public void setSuspend(boolean suspend){
		this.creditSuspended.set(suspend);
	}

    public Map<String, Double> getTradeId2ReservedAmount()
    {
    	if ( !isCashSettlement () )
		{
			Map<String, Double> map = new HashMap<String, Double> ( transactionId2ReservedCreditInLimitCcy );
			for ( String key : map.keySet () )
			{
				map.put ( key, getLimitCurrency ().round ( map.get ( key ) ) );
			}
			return map;
		}
		return null;
    }

	public Map<String, Map<Currency, AtomicDouble>> getTradeId2ReservedAmountMap()
	{
		return isCashSettlement () ? transactionId2ReservedMap : null;
	}


	public void addInterestedCreditLineCollection(CreditLineCollection collection)
	{
		interestedCreditLineCollections.add(collection);
	}

	public Collection<CreditLineCollection> getInterestedCreditLineCollections()
	{
		return interestedCreditLineCollections;
	}
    
	public void refresh(LegalEntity cple, TradingParty tp, boolean isCpoMaker )
	{
		// We reached here bcoz CPO->TP credit limit changed. So for all the subscriptions having this CPO->TP line, no matter the currency, refresh the line
		for(CreditLineCollection creditLineCollection:interestedCreditLineCollections)
		{
			try
			{
				CreditLimitSubscriptionManagerC.getInstance().refreshCreditLine(cple, tp, creditLineCollection.getCurrencyPair(), creditLineCollection.getValueDate(), isCpoMaker, limitCurrency, orgLevel );
			}
			catch(Exception e)
			{
				log.error("Error in CreditLine.refresh. CreditLineCollection=" + creditLineCollection,e);
			}
		}
	}

    private double round( double amtBeforeRounding )
    {
        if ( amtBeforeRounding <= 0 )
        {
            return 0.0;
        }
        else
        {
            return limitCurrency.round( amtBeforeRounding );
        }
    }
}
