package com.integral.finance.creditLimit.admin;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationFactory;
import com.integral.finance.creditLimit.CreditLimit;
import com.integral.finance.creditLimit.CreditNotificationManagerC;
import com.integral.netting.RemoveRMMNettingPortfolioFunctorC;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.util.LogUtil;
import com.integral.rule.SendEmailAction;
import com.integral.rule.SendEmailActionC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.user.User;
import com.integral.util.IdcUtilC;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Vector;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * This class is used as the controller for running a set of credit functors. These functors execution can be stopped at any time and simultaneous
 * running of functors are not allowed. The credit functors can be added/removed dynamically and it will take effect after the current functor
 * executions are completed.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditFunctorServerC extends CreditFunctorC
{
    protected final static String REMOVE_CREDIT_UTILIZATION = "Remove Credit Utilization";
    protected final static String UPDATE_CREDIT_UTILIZATION_EVENT = "Update Credit Utilization Event";
    protected final static String CREATE_CREDIT_UTILIZATION = "Create Credit Utilization";
    protected final static String REMOVE_OBSOLETE_DEALING_DATA = "Remove Obsolete Dealing Data";
    protected final static String REMOVE_OBSOLETE_DEAL_ORDER_DATA = "Remove Obsolete Deal/Order Data";
    protected final static String REMOVE_OBSOLETE_TRADES_DATA = "Remove Obsolete Request/Trade Data";
    protected final static String REMOVE_OBSOLETE_NETTINGPORTFOLIO_DATA = "Remove Obsolete NettingPortfolio Data";
    protected final static String REFERENCE_DATA_CLEANUP = "Reference Data cleanup of Inactive Orgs";
    protected final static String OLDER_HOLIDAY_DATE_RULES_CLEANUP = "Delete Older Holiday Date Rules ";
    protected final static String REMOVE_OLD_FIXED_PERIOD_MARKET_DATASETS = "Remove Old Fixed Period Market DataSets";

    // define stand alone functors.
    public final static String REVALUATE_AGGREGATE_CREDIT_UTILIZATION = "RevaluateAggregateCreditUtilization";
    public final static String PRECALCULATE_AGGREGATE_CREDIT_UTILIZATION = "PreCalculateAggregateCreditUtilization";

    private static Collection<CreditFunctor> creditFunctors = new Vector<CreditFunctor>();
    private static Collection<CreditFunctor> asyncCreditFunctors = new Vector<CreditFunctor>();
    private static Map<String, CreditFunctor> standAloneCreditFunctors = new HashMap<String, CreditFunctor>( 2 );
    private static Collection<CreditFunctor> serialCreditFunctors = new Vector<CreditFunctor>();
    private static CreditFunctorServerC creditFunctorServer = new CreditFunctorServerC();
    private static int regenCount = 0;
    private AtomicInteger asyncFunctorCount = new AtomicInteger();
    private ThreadPoolExecutor executor = null;

    // private constructor for the singleton behavior.
    private CreditFunctorServerC()
    {
        setName( "Credit Regeneration Server" );
        addCreditFunctor( new RemoveObsoleteCreditFunctorC( REMOVE_CREDIT_UTILIZATION ) );
        addCreditFunctor( new UpdateCreditUtilizationEventFunctorC( UPDATE_CREDIT_UTILIZATION_EVENT ) );
        addCreditFunctor( new CreateCreditUtilizationFunctorC( CREATE_CREDIT_UTILIZATION ) );
        if ( creditAdminConfigMBean.isAsyncCreditFunctorInvocationEnabled() )
        {
            addAsyncCreditFunctor( new RemoveObsoleteTradesFunctorC( REMOVE_OBSOLETE_TRADES_DATA ) );
            addAsyncCreditFunctor( new RemoveObsoleteDealingDataFunctorC( REMOVE_OBSOLETE_DEALING_DATA ) );
            addAsyncCreditFunctor( new RemoveDealsFunctorC( REMOVE_OBSOLETE_DEAL_ORDER_DATA ) );
            addAsyncCreditFunctor( new RemoveRMMNettingPortfolioFunctorC( REMOVE_OBSOLETE_NETTINGPORTFOLIO_DATA ) );
        }
        else
        {
            addCreditFunctor( new RemoveObsoleteTradesFunctorC( REMOVE_OBSOLETE_TRADES_DATA ) );
            addCreditFunctor( new RemoveObsoleteDealingDataFunctorC( REMOVE_OBSOLETE_DEALING_DATA ) );
            addCreditFunctor( new RemoveDealsFunctorC( REMOVE_OBSOLETE_DEAL_ORDER_DATA ) );
            addCreditFunctor( new RemoveRMMNettingPortfolioFunctorC( REMOVE_OBSOLETE_NETTINGPORTFOLIO_DATA ) );
        }

        // adding reference data cleanup always for non-async execution
        addSerialCreditFunctor( new RemoveOldHolidayDateRulesFunctor(OLDER_HOLIDAY_DATE_RULES_CLEANUP) );
        addSerialCreditFunctor( new ReferenceDataCleanupFunctor( REFERENCE_DATA_CLEANUP ) );
        addSerialCreditFunctor ( new RemoveOldFixedPeriodMarketDataSetsFunctor ( REMOVE_OLD_FIXED_PERIOD_MARKET_DATASETS ) );

        // add stand alone credit functors.
        addStandAloneCreditFunctor( REVALUATE_AGGREGATE_CREDIT_UTILIZATION, new RevaluateAggregateCreditUtilizationFunctorC( REVALUATE_AGGREGATE_CREDIT_UTILIZATION ) );
        addStandAloneCreditFunctor( PRECALCULATE_AGGREGATE_CREDIT_UTILIZATION, new PreCalculateAggregateCreditUtilizationFunctorC( PRECALCULATE_AGGREGATE_CREDIT_UTILIZATION ) );
    }

    /**
     * Singleton access.
     *
     * @return credit functor server
     */
    public static CreditFunctorServerC getInstance()
    {
        return creditFunctorServer;
    }

    public void incrementAsyncFunctorCount()
    {
        asyncFunctorCount.incrementAndGet();
    }

    public void decrementAsyncFunctorCount()
    {
        asyncFunctorCount.decrementAndGet();
    }

    public void execute()
    {
        if ( PersistenceFactory.getPersistenceMBean ().isPersistenceReadOnlyMode () )
        {
            log.info( "CFS.execute : skipping since persistence read-only mode" );
            return;
        }

        if ( isRunning() || isSuspended() || suspensionInitiated )
        {
            log.info( "CFS.execute : Credit regeneration functor is already running or suspended. this=" + this + ",regenCount=" + regenCount + ",by user=" + user );
            return;
        }

        if ( !creditAdminConfigMBean.isCreditAdminRegenerationEnabled() )
        {
            log.info( "CFS.execute : Admin credit regeneration is not enabled." );
            return;
        }

        try
        {
            LogUtil.setDisableDBQueryLogSwitch( true );
            start();
            if ( this.user == null )
            {
                log.warn( "CFS.execute : This process can't be initiated without a user." );
                return;
            }
            regenCount++;
            long t0 = System.currentTimeMillis();
            log.info( "CFS.execute : Credit regeneration functor execution started. this=" + this + ",regenCount=" + regenCount + ",user=" + user );
            sendRegenerationStartEmail();
            long sleepTime = creditAdminConfigMBean.getCreditRegenerationInitialSleep();
            log.info( new StringBuilder( 200 ).append( "Starting Credit Regeneration. This process should not be executed during trading hours. Sleeping for " )
                    .append( sleepTime ).append( " milliseconds. Please suspend the credit regeneration if done in trading hours using admin credit utilities." ).toString() );
            Thread.sleep( sleepTime );
            log.info( "CFS.execute : Sleep completed. Starting credit regeneration. this=" + this );

            // go through any async functors
            for ( CreditFunctor functor : asyncCreditFunctors )
            {
                if ( isRunning() && functor.isEnabled() )
                {
                    incrementAsyncFunctorCount();
                    CreditNotificationManagerC.getInstance().getPooledExecutor().execute( new FunctorWorker( functor, this, IdcUtilC.getSessionContextUser() ) );
                }
                else
                {
                    log.warn( new StringBuilder( 100 ).append( "CFS.execute : Skip Executing Functor=" )
                            .append( functor ).append( ",this=" ).append( this ).toString() );
                }
            }

            for ( CreditFunctor functor : creditFunctors )
            {
                if ( isRunning() && functor.isEnabled() )
                {
                    long t2 = System.currentTimeMillis();
                    functor.execute();
                    long t3 = System.currentTimeMillis();
                    log.info( new StringBuilder( 100 ).append( "CFS.execute : Functor=" )
                            .append( functor ).append( " execution took " ).append( t3 - t2 ).append( " ms." ).toString() );
                }
                else
                {
                    log.warn( new StringBuilder( 100 ).append( "CFS.execute : Skip Executing Functor=" )
                            .append( functor ).append( ",this=" ).append( this ).toString() );
                }
            }
            while ( asyncFunctorCount.get() > 0 )
            {
                Thread.sleep( 1000 );
            }

            // serial credit functors execute after all the async functors finished executing
            for ( CreditFunctor functor : serialCreditFunctors )
            {
                if ( isRunning() && functor.isEnabled() )
                {
                    long t2 = System.currentTimeMillis();
                    functor.execute();
                    long t3 = System.currentTimeMillis();
                    log.info( new StringBuilder( 100 ).append( "CFS.execute : Functor=" )
                            .append( functor ).append( " execution took " ).append( t3 - t2 ).append( " ms." ).toString() );
                }
                else
                {
                    log.warn( new StringBuilder( 100 ).append( "CFS.execute : Skip Executing Functor=" )
                            .append( functor ).append( ",this=" ).append( this ).toString() );
                }
            }

            finish();
            long t1 = System.currentTimeMillis();
            log.info( "CFS.execute : Credit regeneration functor execution finished. took " + ( t1 - t0 ) + " ms. this=" + this + ",user=" + user + ",regenCount=" + regenCount );
            sendRegenerationFinishEmail();
        }
        catch ( Exception e )
        {
            finish();
            log.error( "CFS.execute : Exception while executing the credit functors. User=" + user, e );
        }
        finally
        {
            finish();
            LogUtil.removeDisableDBQueryLogSwitch();
        }
        if ( suspensionInitiated )
        {
            this.suspended = true;
        }
    }

    public void addCreditFunctor( CreditFunctor functor )
    {
        creditFunctors.add( functor );
    }

    public void addSerialCreditFunctor( CreditFunctor functor )
    {
        serialCreditFunctors.add( functor );
    }

    public void addAsyncCreditFunctor( CreditFunctor functor )
    {
        asyncCreditFunctors.add( functor );
    }

    public void addStandAloneCreditFunctor( String name, CreditFunctor functor )
    {
        standAloneCreditFunctors.put( name, functor );
    }

    public void removeCreditFunctor( CreditFunctor functor )
    {
        creditFunctors.remove( functor );
    }

    public void removeStandAloneCreditFunctor( String name )
    {
        standAloneCreditFunctors.remove( name );
    }

    public Collection<CreditFunctor> getCreditFunctors()
    {
        return creditFunctors;
    }

    public Collection<CreditFunctor> getAsyncCreditFunctors()
    {
        return asyncCreditFunctors;
    }

    public Collection<CreditFunctor> getAllCreditFunctors()
    {
        Collection<CreditFunctor> functors = new ArrayList<CreditFunctor>();
        functors.addAll( getCreditFunctors() );
        functors.addAll( getAsyncCreditFunctors() );
        return functors;
    }

    public CreditFunctor getStandAloneCreditFunctor( String name )
    {
        return standAloneCreditFunctors.get( name );
    }

    public void suspend()
    {
        super.suspend();
        enableFunctors( false );
        for ( CreditFunctor functor : getCreditFunctors() )
        {
            functor.suspend();
        }

        // handle async functors as well.
        if ( getAsyncCreditFunctors() != null )
        {
            for ( CreditFunctor functor : getAsyncCreditFunctors() )
            {
                functor.suspend();
            }
        }
    }

    public void reset()
    {
        super.reset();
        enableFunctors( true );
        for ( CreditFunctor functor : getCreditFunctors() )
        {
            functor.reset();
        }

        // handle async functors as well.
        if ( getAsyncCreditFunctors() != null )
        {
            for ( CreditFunctor functor : getAsyncCreditFunctors() )
            {
                functor.reset();
            }
        }
    }

    public String toString()
    {
        return new StringBuilder( 200 ).append( super.toString() ).append( ",running=" ).append( running )
                .append( ",functors=" ).append( creditFunctors ).append( ",asyncFunctors=" ).append( asyncCreditFunctors )
                .append( ",startTime=" ).append( startTime ).append( ",endTime=" ).append( endTime ).toString();
    }

    /**
     * Starts the server by resetting the children functors.
     */
    protected void start()
    {
        super.start();
        for ( CreditFunctor functor : getCreditFunctors() )
        {
            functor.reset();
        }
        // handle async functors as well.
        if ( getAsyncCreditFunctors() != null )
        {
            for ( CreditFunctor functor : getAsyncCreditFunctors() )
            {
                functor.reset();
            }
        }
    }

    /**
     * Enable/disable the functors based on the specified enable flag.
     *
     * @param enable enable
     */
    private void enableFunctors( boolean enable )
    {
        for ( CreditFunctor functor : creditFunctors )
        {
            functor.setEnabled( enable );
        }
        // handle async functors as well.
        if ( getAsyncCreditFunctors() != null )
        {
            for ( CreditFunctor functor : getAsyncCreditFunctors() )
            {
                functor.setEnabled( enable );
            }
        }

    }

    /**
     * Sends an email indicating that credit regeneration is started.
     */
    private void sendRegenerationStartEmail()
    {
        String subject = creditAdminConfigMBean.getCreditRegenerationStartEmailSubject();
        String body = creditAdminConfigMBean.getCreditRegenerationStartEmailContent();
        sendCreditRegenerationEmail( subject, body );
    }

    /**
     * Sends the email indicating the credit regeneration is completed.
     */
    private void sendRegenerationFinishEmail()
    {
        String subject = creditAdminConfigMBean.getCreditRegenerationEndEmailSubject();
        String body = creditAdminConfigMBean.getCreditRegenerationEndEmailContent();
        sendCreditRegenerationEmail( subject, body );
    }

    /**
     * Sends email with the specified subject and content to the email addresses specified by the configuration.
     *
     * @param subject subject
     * @param body    body
     */
    private void sendCreditRegenerationEmail( String subject, String body )
    {
        try
        {
            Collection<String> emailIds = creditAdminConfigMBean.getCreditAdminRegenerationEmailToAddresses();
            String fromEmailId = creditAdminConfigMBean.getCreditAdminRegenerationEmailFromAddress();
            if ( emailIds != null && fromEmailId != null )
            {
                SendEmailAction action = new SendEmailActionC();
                String hostName = ConfigurationFactory.getServerMBean().getHostName();
                action.putCustomField( CreditLimit.HOSTNAME, hostName );
                action.putCustomField( CreditLimit.ENVIRONMENT, ConfigurationFactory.getServerMBean().getEnvironment() );
                action.putCustomField( CreditLimit.APPLICATION_NAME, ConfigurationFactory.getServerMBean().getApplicationLongName() );
                String serverUrl = ConfigurationFactory.getServerMBean().getServerURL();
                if ( serverUrl.indexOf( "localhost" ) != -1 )
                {
                    serverUrl = serverUrl.replace( "localhost", hostName );
                }
                action.putCustomField( CreditLimit.SERVER_URL, serverUrl );
                action.setFrom( fromEmailId );
                action.setTos( emailIds );
                action.setSubject( subject );
                action.setBody( body );
                action.execute( null );
            }
        }
        catch ( Exception e )
        {
            log.error( "CFS.sendCreditRegenerationEmail : Error sending credit regeneration started email. subject=" + subject + ",body=" + body, e );
        }
    }

    // Returns the thread pool executor which is exclusively used for weekend process.
    public synchronized ThreadPoolExecutor getPooledExecutor()
    {
        if ( executor == null )
        {
            executor = initExcecutor();
        }
        return executor;
    }

    /**
     * Initializes the thread pool executor for weekend process if not already initialized.
     */
    private ThreadPoolExecutor initExcecutor()
    {
        if ( executor == null )
        {
            final int poolSize = DealingDataPurgeConfigurationFactory.getDealingDataPurgeConfigurationMBean().getDataPurgeThreadPoolSize();
            final int poolQueueSize = DealingDataPurgeConfigurationFactory.getDealingDataPurgeConfigurationMBean().getDataPurgeThreadPoolQueueSize();
            executor = new ThreadPoolExecutor( poolSize, poolSize, 120, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>( poolQueueSize ), new WeekendProcessThreadFactory( "WeekendProcessThreadGroup" ) );
            executor.setRejectedExecutionHandler( new ThreadPoolExecutor.CallerRunsPolicy() ); // this policy is selected so that fixed threads will execute the purge process.
        }
        return executor;
    }

    private class FunctorWorker implements Runnable
    {
        private CreditFunctorServerC cfServer;
        private CreditFunctor cf;
        private User user;

        public FunctorWorker( CreditFunctor functor, CreditFunctorServerC server, User usr )
        {
            cf = functor;
            cfServer = server;
            user = usr;
        }

        public void run()
        {
            try
            {
                LogUtil.setDisableDBQueryLogSwitch( true );
                if ( cf != null && user != null && cfServer != null )
                {
                    IdcSessionContext sc = IdcSessionManager.getInstance().getSessionContext( user );
                    IdcSessionManager.getInstance().setSessionContext( sc );
                    cf.execute();
                }
                else
                {
                    log.warn( "FW.run : Insufficient params. cf=" + cf + ",user=" + user + ",cfs=" + cfServer );
                }
            }
            catch ( Exception e )
            {
                log.error( "FW.run : Exception while running the functor=" + cf );
            }
            finally
            {
                if ( cfServer != null )
                {
                    cfServer.decrementAsyncFunctorCount();
                }
                LogUtil.removeDisableDBQueryLogSwitch();
            }
        }
    }

    private static class WeekendProcessThreadFactory implements ThreadFactory
    {
        final AtomicInteger threadNumber = new AtomicInteger( 1 );
        ThreadGroup tg = null;

        public WeekendProcessThreadFactory( String name )
        {
            tg = new ThreadGroup( name );
        }

        public Thread newThread( Runnable runnable )
        {
            return new Thread( tg, runnable, "WeekendProcessThread-" + threadNumber.getAndIncrement() );
        }
    }

}
