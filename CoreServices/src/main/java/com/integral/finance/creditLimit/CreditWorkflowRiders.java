package com.integral.finance.creditLimit;

// Copyright (c) 2019 Integral Development Corp. All rights reserved.

import com.integral.user.Organization;
import com.integral.user.User;

/**
 * This class provides basic flags for executing credit workflow.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditWorkflowRiders
{
    private boolean excessCreditUtilizationAllowed;
    private boolean skipCheckSuspendedStatus;
    private boolean bypassTenorRestrictions;
    private boolean skipAccountCheck;
    private boolean skipUpdateAccountNetReceivable;
    private boolean skipSettledTradeUndoCredit;//skips credit instead of failing
    private boolean excessUtilizationAllowedMakerOnly;//
    private boolean bypassTenorRestrictionsMakerOnly;

    /**
     * if the credit flow is triggered by ACCEPTANCE event
     */
    private boolean isAcceptance;

    private Organization makerOrg;

    private User makerUser;

    public boolean isExcessCreditUtilizationAllowed ()
    {
        return excessCreditUtilizationAllowed;
    }

    public void setExcessCreditUtilizationAllowed ( boolean excessCreditUtilizationAllowed )
    {
        this.excessCreditUtilizationAllowed = excessCreditUtilizationAllowed;
    }

    public boolean isSkipCheckSuspendedStatus ()
    {
        return skipCheckSuspendedStatus;
    }

    public void setSkipCheckSuspendedStatus ( boolean skipCheckSuspendedStatus )
    {
        this.skipCheckSuspendedStatus = skipCheckSuspendedStatus;
    }

    public boolean isBypassTenorRestrictions ()
    {
        return bypassTenorRestrictions;
    }

    public void setBypassTenorRestrictions ( boolean bypassTenorRestrictions )
    {
        this.bypassTenorRestrictions = bypassTenorRestrictions;
    }

    public boolean isSkipAccountCheck ()
    {
        return skipAccountCheck;
    }

    public void setSkipAccountCheck ( boolean skipAccountCheck )
    {
        this.skipAccountCheck = skipAccountCheck;
    }

    public boolean isSkipUpdateAccountNetReceivable ()
    {
        return skipUpdateAccountNetReceivable;
    }

    public void setSkipUpdateAccountNetReceivable ( boolean skipUpdateAccountNetReceivable )
    {
        this.skipUpdateAccountNetReceivable = skipUpdateAccountNetReceivable;
    }

    public boolean isSkipSettledTradeUndoCredit ()
    {
        return skipSettledTradeUndoCredit;
    }

    public void setSkipSettledTradeUndoCredit ( boolean skipSettledTradeUndoCredit )
    {
        this.skipSettledTradeUndoCredit = skipSettledTradeUndoCredit;
    }

    public boolean isExcessUtilizationAllowedMakerOnly ()
    {
        return excessUtilizationAllowedMakerOnly;
    }

    public void setExcessUtilizationAllowedMakerOnly ( boolean excessUtilizationAllowedMakerOnly )
    {
        this.excessUtilizationAllowedMakerOnly = excessUtilizationAllowedMakerOnly;
    }

    public boolean isBypassTenorRestrictionsMakerOnly ()
    {
        return bypassTenorRestrictionsMakerOnly;
    }

    public void setBypassTenorRestrictionsMakerOnly ( boolean bypassTenorRestrictionsMakerOnly )
    {
        this.bypassTenorRestrictionsMakerOnly = bypassTenorRestrictionsMakerOnly;
    }

    public Organization getMakerOrg ()
    {
        return makerOrg;
    }

    public void setMakerOrg ( Organization makerOrg )
    {
        this.makerOrg = makerOrg;
    }

    public User getMakerUser ()
    {
        return makerUser;
    }

    public void setMakerUser ( User makerUser )
    {
        this.makerUser = makerUser;
    }

    public boolean isAcceptance() {
        return isAcceptance;
    }

    public void setAcceptance(boolean acceptance) {
        isAcceptance = acceptance;
    }
}
