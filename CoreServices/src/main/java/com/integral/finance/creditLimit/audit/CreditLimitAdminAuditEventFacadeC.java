package com.integral.finance.creditLimit.audit;

// Copyright (c) 2001-2006 Integral Development Corp. All rights reserved.

import com.integral.audit.AuditEventFacadeC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditLimitRuleSet;
import com.integral.finance.creditLimit.CreditUtilizationCalculator;
import com.integral.finance.currency.Currency;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Entity;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateTime;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Locale;
import java.util.ResourceBundle;

/**
 * CreditLimitAdminAuditEventFacadeC is used to wrap the the credit admin related audit events.
 * And determines how the audit fields are going to be used to store credit admin related information.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitAdminAuditEventFacadeC extends AuditEventFacadeC implements CreditLimitAdminAuditEventFacade
{
    protected static ArrayList<String> properties = new ArrayList<String>( 25 );
    protected static ArrayList<String> formats = new ArrayList<String>( 25 );
    protected static HashMap<String, String> fieldMappings = new HashMap<String, String>( 25 );

    protected static Log log = LogFactory.getLog( CreditLimitAdminAuditEventFacadeC.class );

    static
    {
        //Log date time would require date formatting
        properties.add(CreditAuditEventParameters.LOGDATETIME); //Index 0
        formats.add( null );

        //For user shortName, no formatting is required
        properties.add( CreditAuditEventParameters.USERSHORTNAME ); //Index 1
        formats.add(null);

        // Credit provider organization short name.
        properties.add( CreditAuditEventParameters.ENTITY1SHORTNAME ); //Index 2
        formats.add( null );
        fieldMappings.put(CreditAuditEventParameters.CREDIT_PROVIDER_ORGANIZATION, CreditAuditEventParameters.ENTITY1SHORTNAME);

        // Credit provider legal entity shortname
        properties.add( CreditAuditEventParameters.ENTITY4SHORTNAME ); //Index 3
        formats.add( null );
        fieldMappings.put(CreditAuditEventParameters.CREDIT_PROVIDER_LEGAL_ENTITY, CreditAuditEventParameters.ENTITY4SHORTNAME);

        // Credit counterparty organization short name.
        properties.add( CreditAuditEventParameters.ENTITY3SHORTNAME ); //Index 4
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.CREDIT_COUNTERPARTY_ORGANIZATION, CreditAuditEventParameters.ENTITY3SHORTNAME );

        // Credit counterparty short name.
        properties.add( CreditAuditEventParameters.ENTITY5SHORTNAME ); //Index 5
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.CREDIT_COUNTERPARTY, CreditAuditEventParameters.ENTITY5SHORTNAME );

        // Credit limit currency short name.
        properties.add( CreditAuditEventParameters.ENTITY6SHORTNAME ); //Index 6
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.LIMIT_CURRENCY, CreditAuditEventParameters.ENTITY6SHORTNAME );

        // Existing netting methodology short name.
        properties.add( CreditAuditEventParameters.ENTITY7LONGNAME ); //Index 7
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_NETTING_METHODOLOGY, CreditAuditEventParameters.ENTITY7LONGNAME );

        // New netting methodology short name.
        properties.add( CreditAuditEventParameters.ENTITY8LONGNAME ); //Index 8
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_NETTING_METHODOLOGY, CreditAuditEventParameters.ENTITY8LONGNAME );

        // Value date is stored as DateTime1 in the underlying audit event.
        properties.add( CreditAuditEventParameters.DATETIME1 ); //Index 9
        formats.add( CreditAuditEventParameters.DATE_FORMAT );
        fieldMappings.put( CreditAuditEventParameters.SETTLEMENT_DATE, CreditAuditEventParameters.DATETIME1 );

        // Existing limit amount.
        properties.add( CreditAuditEventParameters.DOUBLE1 ); //Index 10
        formats.add( CreditAuditEventParameters.DECIMAL_FORMAT );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_LIMIT_AMOUNT, CreditAuditEventParameters.DOUBLE1 );

        // New limit amount.
        properties.add( CreditAuditEventParameters.DOUBLE2 ); //Index 11
        formats.add( CreditAuditEventParameters.DECIMAL_FORMAT );
        fieldMappings.put( CreditAuditEventParameters.NEW_LIMIT_AMOUNT, CreditAuditEventParameters.DOUBLE2 );

        // Existing notification percentage.
        properties.add( CreditAuditEventParameters.DOUBLE3 ); //Index 12
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_NOTIFICATION_PERCENTAGE, CreditAuditEventParameters.DOUBLE3 );

        // New notification percentage.
        properties.add( CreditAuditEventParameters.DOUBLE4 ); //Index 13
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_NOTIFICATION_PERCENTAGE, CreditAuditEventParameters.DOUBLE4 );

        // Existing warning percentage.
        properties.add( CreditAuditEventParameters.DOUBLE5 ); //Index 14
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_WARNING_PERCENTAGE, CreditAuditEventParameters.DOUBLE5 );

        // New warning percentage.
        properties.add( CreditAuditEventParameters.DOUBLE6 ); //Index 15
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_WARNING_PERCENTAGE, CreditAuditEventParameters.DOUBLE6 );

        // Existing suspension percentage.
        properties.add( CreditAuditEventParameters.DOUBLE7 ); //Index 16
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_SUSPENSION_PERCENTAGE, CreditAuditEventParameters.DOUBLE7 );

        // New suspension percentage.
        properties.add( CreditAuditEventParameters.DOUBLE8 ); //Index 17
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_SUSPENSION_PERCENTAGE, CreditAuditEventParameters.DOUBLE8 );

        // Utilization percentage.
        properties.add( CreditAuditEventParameters.DOUBLE9 ); //Index 18
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.UTILIZATION_PERCENTAGE, CreditAuditEventParameters.DOUBLE9 );

        // Existing email address.
        properties.add( CreditAuditEventParameters.STRING1 ); //Index 19
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_EMAIL_ADDRESS, CreditAuditEventParameters.STRING1 );

        // New email address.
        properties.add( CreditAuditEventParameters.STRING2 ); //Index 20
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_EMAIL_ADDRESS, CreditAuditEventParameters.STRING2 );

        // Credit counterparty shortname. Can be organization or trading party shortname.
        properties.add( CreditAuditEventParameters.STRING6 ); //Index 21
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.CREDIT_COUNTERPARTY_SHORT_NAME, CreditAuditEventParameters.STRING6 );

        // Existing Sender email address.
        properties.add( CreditAuditEventParameters.STRING3 ); //Index 22
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_SENDER_EMAIL_ADDRESS, CreditAuditEventParameters.STRING3 );

        // New Sender email address.
        properties.add( CreditAuditEventParameters.STRING4 ); //Index 23
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_SENDER_EMAIL_ADDRESS, CreditAuditEventParameters.STRING4 );

        // Existing limit currency
        properties.add( CreditAuditEventParameters.ENTITY9SHORTNAME ); //Index 24
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_LIMIT_CURRENCY, CreditAuditEventParameters.ENTITY9SHORTNAME );

        // Existing Daily Exposure Horizon.
        properties.add( CreditAuditEventParameters.DOUBLE10 ); //Index 25
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_DAILY_EXPOSURE_HORIZON, CreditAuditEventParameters.DOUBLE10 );

        // New Daily Exposure Horizon.
        properties.add( CreditAuditEventParameters.DOUBLE11 ); //Index 26
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_DAILY_EXPOSURE_HORIZON, CreditAuditEventParameters.DOUBLE11 );

        // Trade Description.
        properties.add( CreditAuditEventParameters.STRING5 ); //Index 27
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.TRADE_DESCRIPTION, CreditAuditEventParameters.STRING5 );

        properties.add( CreditAuditEventParameters.DOUBLE12 ); //index 28
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_LEVERAGE_FACTOR, CreditAuditEventParameters.DOUBLE12 );

        properties.add( CreditAuditEventParameters.DOUBLE13 ); //index 29
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_LEVERAGE_FACTOR, CreditAuditEventParameters.DOUBLE13 );

        // Existing maximum tenor.
        properties.add( CreditAuditEventParameters.STRING7 ); //Index 30
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_MAXIMUM_TENOR, CreditAuditEventParameters.STRING7 );

        // New maximum tenor.
        properties.add( CreditAuditEventParameters.STRING8 ); //Index 31
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_MAXIMUM_TENOR, CreditAuditEventParameters.STRING8 );

        // Existing Margin Template.
        properties.add( CreditAuditEventParameters.STRING9 ); //Index 32
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_TENOR_IN_BUSINESS_DAYS, CreditAuditEventParameters.STRING9 );

        // New Margin Template.
        properties.add( CreditAuditEventParameters.STRING10 ); //Index 33
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.TENOR_IN_BUSINESS_DAYS, CreditAuditEventParameters.STRING10 );

        // Existing minimum tenor.
        properties.add( CreditAuditEventParameters.STRING11 ); //Index 34
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_MINIMUM_TENOR, CreditAuditEventParameters.STRING11 );

        // New minimum tenor.
        properties.add( CreditAuditEventParameters.STRING12 ); //Index 35
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_MINIMUM_TENOR, CreditAuditEventParameters.STRING12 );

        // Existing credit status.
        properties.add( CreditAuditEventParameters.STRING13 ); //Index 36
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_CREDIT_STATUS, CreditAuditEventParameters.STRING13 );

        // New credit status.
        properties.add( CreditAuditEventParameters.STRING14 ); //Index 37
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_CREDIT_STATUS, CreditAuditEventParameters.STRING14 );

        // Existing currency pair exemption
        properties.add( CreditAuditEventParameters.STRING15 ); //Index 38
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_CURRENCYPAIR_EXEMPTION, CreditAuditEventParameters.STRING15 );

        // New currency pair exemption
        properties.add( CreditAuditEventParameters.STRING16 ); //Index 39
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_CURRENCYPAIR_EXEMPTION, CreditAuditEventParameters.STRING16 );

        // Existing tenor profile
        properties.add( CreditAuditEventParameters.STRING17 ); //Index 40
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_TENORPROFILE, CreditAuditEventParameters.STRING17 );

        // New tenor profile
        properties.add( CreditAuditEventParameters.STRING18 ); //Index 41
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_TENORPROFILE, CreditAuditEventParameters.STRING18 );

        // existing credit mode.
        properties.add( CreditAuditEventParameters.STRING19 ); //Index 42
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_TENORPROFILE, CreditAuditEventParameters.STRING19 );

        // New credit mode.
        properties.add( CreditAuditEventParameters.STRING20 ); //Index 43
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_MODE, CreditAuditEventParameters.STRING20 );


        // Existing LE level override for tenor restrictions
        properties.add( CreditAuditEventParameters.STRING19 ); //Index 44
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_LEOVERRIDE, CreditAuditEventParameters.STRING19 );

        // New LE level override for tenor restrictions
        properties.add( CreditAuditEventParameters.STRING20 ); //Index 45
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_LEOVERRIDE, CreditAuditEventParameters.STRING20 );


        // tenor restrictions at LE level. existing org default
        properties.add( CreditAuditEventParameters.STRING19 ); //Index 46
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_ORGDEFAULT, CreditAuditEventParameters.STRING19 );

        // tenor restrictions at LE level. new org default
        properties.add( CreditAuditEventParameters.STRING20 ); //Index 47
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.ORGDEFAULT, CreditAuditEventParameters.STRING20 );

        // deposit/withdraw currency
        properties.add( CreditAuditEventParameters.ENTITY10SHORTNAME ); //Index 48
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.DEPOSIT_WITHDRAW_CURRENCY, CreditAuditEventParameters.ENTITY10SHORTNAME );

        // deposit/withdraw amount
        properties.add( CreditAuditEventParameters.DOUBLE14 ); //Index 49
        formats.add( CreditAuditEventParameters.DECIMAL_FORMAT_48 );
        fieldMappings.put( CreditAuditEventParameters.DEPOSIT_WITHDRAW_AMOUNT, CreditAuditEventParameters.DOUBLE14 );

        // Existing stop-out percentage.
        properties.add( CreditAuditEventParameters.DOUBLE15 ); //Index 50
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_STOPOUT_PERCENTAGE, CreditAuditEventParameters.DOUBLE15 );

        // New stop-out percentage.
        properties.add( CreditAuditEventParameters.DOUBLE16 ); //Index 51
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_STOPOUT_PERCENTAGE, CreditAuditEventParameters.DOUBLE16 );

        // Existing gross spread margin.
        properties.add( CreditAuditEventParameters.DOUBLE17 ); //Index 52
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.EXISTING_GROSS_SPREAD_MARGIN, CreditAuditEventParameters.DOUBLE17 );

        // New gross spread margin.
        properties.add( CreditAuditEventParameters.DOUBLE18 ); //Index 53
        formats.add( null );
        fieldMappings.put( CreditAuditEventParameters.NEW_GROSS_SPREAD_MARGIN, CreditAuditEventParameters.DOUBLE18 );
    }

    public String getResourceBundleName()
    {
        return "com.integral.finance.creditLimit.audit.CreditAuditMessages";
    }

    public String getDescription( ResourceBundle rb, User user, Locale locale )
    {
        String message = super.getDescription( rb, user, locale );
        message = message.replaceAll( "null", "N/A" );
        return message;
    }

    //overriden the method from AuditEventFacadeC with additional check of type length
    public String getMessageKey()
    {
        if ( msgKey == null )
        {
            StringBuilder msgKeyBuffer = new StringBuilder( 100 );
            msgKeyBuffer.append( "AUDIT_" );
            msgKeyBuffer.append( getAuditEvent().getComponent() );
            msgKeyBuffer.append( '_' );
            msgKeyBuffer.append( getAuditEvent().getAction() );
            if ( getAuditEvent().getType() != null && getAuditEvent().getType().length() > 0 )
            {
                msgKeyBuffer.append( '_' );
                msgKeyBuffer.append( getAuditEvent().getType() );
            }
            msgKey = msgKeyBuffer.toString();
        }
        return msgKey;
    }

    public Organization getCreditProviderOrganization()
    {
        return ( Organization ) getAuditEvent().getEntity1();
    }

    public void setCreditProviderOrganization( Organization creditProviderOrg )
    {
        getAuditEvent().setEntity1( creditProviderOrg );
    }

    public CreditLimitRuleSet getCreditLimitRuleSet()
    {
        return ( CreditLimitRuleSet ) getAuditEvent().getEntity2();
    }

    public void setCreditLimitRuleSet( CreditLimitRuleSet clrs )
    {
        getAuditEvent().setEntity2( clrs );
    }

    public Organization getCreditCounterpartyOrganization()
    {
        return ( Organization ) getAuditEvent().getEntity3();
    }

    public void setCreditCounterpartyOrganization( Organization creditCptyOrg )
    {
        getAuditEvent().setEntity3(creditCptyOrg);
    }

    public LegalEntity getCreditProviderLegalEntity()
    {
        return ( LegalEntity ) getAuditEvent().getEntity4();
    }

    public void setCreditProviderLegalEntity( LegalEntity creditProviderLe )
    {
        getAuditEvent().setEntity4(creditProviderLe);
    }

    public TradingParty getCreditCounterparty()
    {
        return ( TradingParty ) getAuditEvent().getEntity5();
    }

    public void setCreditCounterparty( TradingParty creditCpty )
    {
        getAuditEvent().setEntity5(creditCpty);
    }

    public Currency getLimitCurrency()
    {
        return ( Currency ) getAuditEvent().getEntity6();
    }

    public void setLimitCurrency( Currency ccy )
    {
        getAuditEvent().setEntity6(ccy);
    }

    public Currency getExistingLimitCurrency()
    {
        return ( Currency ) getAuditEvent().getEntity9();
    }

    public void setExistingLimitCurrency( Currency ccy )
    {
        getAuditEvent().setEntity9(ccy);
    }

    public CreditUtilizationCalculator getExistingNettingMethodology()
    {
        return ( CreditUtilizationCalculator ) getAuditEvent().getEntity7();
    }

    public void setExistingNettingMethodology( CreditUtilizationCalculator calc )
    {
        getAuditEvent().setEntity7(calc);
    }

    public CreditUtilizationCalculator getNewNettingMethodology()
    {
        return ( CreditUtilizationCalculator ) getAuditEvent().getEntity8();
    }

    public void setNewNettingMethodology( CreditUtilizationCalculator calc )
    {
        getAuditEvent().setEntity8(calc);
    }

    public IdcDate getSettlementDate()
    {
        IdcDateTime dateTime = getAuditEvent().getDateTimeArg1();
        return dateTime != null ? dateTime.getDate() : null;
    }

    public void setSettlementDate( IdcDate date )
    {
        getAuditEvent().setDateTimeArg1(date != null ? date.toIdcDateTime() : null);
    }

    public Double getExistingLimitAmount()
    {
        return getAuditEvent().getDoubleArg1();
    }

    public void setExistingLimitAmount( Double amt )
    {
        getAuditEvent().setDoubleArg1(amt);
    }

    public Double getNewLimitAmount()
    {
        return getAuditEvent().getDoubleArg2();
    }

    public void setNewLimitAmount( Double amt )
    {
        getAuditEvent().setDoubleArg2(amt);
    }

    public Double getExistingNotificationPercentage()
    {
        return getAuditEvent().getDoubleArg3();
    }

    public void setExistingNotificationPercentage( Double percent )
    {
        getAuditEvent().setDoubleArg3(percent);
    }

    public Double getNewNotificationPercentage()
    {
        return getAuditEvent().getDoubleArg4();
    }

    public void setNewNotificationPercentage( Double percent )
    {
        getAuditEvent().setDoubleArg4(percent);
    }

    public Double getExistingWarningPercentage()
    {
        return getAuditEvent().getDoubleArg5();
    }

    public void setExistingWarningPercentage( Double percent )
    {
        getAuditEvent().setDoubleArg5(percent);
    }

    public Double getNewWarningPercentage()
    {
        return getAuditEvent().getDoubleArg6();
    }

    public void setNewWarningPercentage( Double percent )
    {
        getAuditEvent().setDoubleArg6(percent);
    }

    public Double getExistingSuspensionPercentage()
    {
        return getAuditEvent().getDoubleArg7();
    }

    public void setExistingSuspensionPercentage( Double percent )
    {
        getAuditEvent().setDoubleArg7(percent);
    }

    public Double getNewSuspensionPercentage()
    {
        return getAuditEvent().getDoubleArg8();
    }

    public void setNewSuspensionPercentage( Double percent )
    {
        getAuditEvent().setDoubleArg8(percent);
    }

    public Double getExistingStopOutPercentage()
    {
        return getAuditEvent().getDoubleArg15();
    }

    public void setExistingStopOutPercentage( Double percent )
    {
        getAuditEvent().setDoubleArg15(percent);
    }

    public Double getNewStopOutPercentage()
    {
        return getAuditEvent().getDoubleArg16();
    }

    public void setNewStopOutPercentage( Double percent )
    {
        getAuditEvent().setDoubleArg16(percent);
    }

    public Double getUtilizationPercentage()
    {
        return getAuditEvent().getDoubleArg9();
    }

    public void setUtilizationPercentage( Double percent )
    {
        getAuditEvent().setDoubleArg9(percent);
    }

    public String getExistingEmailAddress()
    {
        return getAuditEvent().getStringArg1();
    }

    public void setExistingEmailAddress( String email )
    {
        getAuditEvent().setStringArg1(email);
    }

    public String getNewEmailAddress()
    {
        return getAuditEvent().getStringArg2();
    }

    public void setNewEmailAddress( String email )
    {
        getAuditEvent().setStringArg2(email);
    }

    public String getExistingSenderEmailAddress()
    {
        return getAuditEvent().getStringArg3();
    }

    public void setExistingSenderEmailAddress( String email )
    {
        getAuditEvent().setStringArg3(email);
    }

    public String getNewSenderEmailAddress()
    {
        return getAuditEvent().getStringArg4();
    }

    public void setNewSenderEmailAddress( String email )
    {
        getAuditEvent().setStringArg4(email);
    }

    public double getExistingDailyExposureHorizon()
    {
        return getAuditEvent().getDoubleArg10();
    }

    public void setExistingDailyExposureHorizon( double horizon )
    {
        getAuditEvent().setDoubleArg10(horizon);
    }

    public double getNewDailyExposureHorizon()
    {
        return getAuditEvent().getDoubleArg11();
    }

    public void setNewDailyExposureHorizon( double horizon )
    {
        getAuditEvent().setDoubleArg11(horizon);
    }

    public String getTradeDescription()
    {
        return getAuditEvent().getStringArg5();
    }

    public void setTradeDescription( String tradeDescription )
    {
        getAuditEvent().setStringArg5(tradeDescription);
    }

    public String getExistingCreditExposureLevel()
    {
        return getAuditEvent().getStringArg3();
    }

    public void setExistingCreditExposureLevel( String exposure )
    {
        getAuditEvent().setStringArg3(exposure);
    }

    public String getNewCreditExposureLevel()
    {
        return getAuditEvent().getStringArg4();
    }

    public void setNewCreditExposureLevel( String exposure )
    {
        getAuditEvent().setStringArg4(exposure);
    }

    public String getActionLevel()
    {
        return getAuditEvent().getStringArg5();
    }

    public void setActionLevel( String action )
    {
        getAuditEvent().setStringArg5(action);
    }

    public String getCreditCounterpartyShortName()
    {
        return getAuditEvent().getStringArg6();
    }

    public void setCreditCounterpartyShortName( String cptyName )
    {
        getAuditEvent().setStringArg6(cptyName);
    }

    public Double getExistingLeverageFactor()
    {
        return getAuditEvent().getDoubleArg12();
    }

    public void setExistingLeverageFactor( Double factor )
    {
        getAuditEvent().setDoubleArg12(factor);
    }

    public Double getNewLeverageFactor()
    {
        return getAuditEvent().getDoubleArg13();
    }

    public void setNewLeverageFactor( Double factor )
    {
        getAuditEvent().setDoubleArg13(factor);
    }

    public String getExistingMaximumTenor()
    {
        return getAuditEvent().getStringArg7();
    }

    public void setExistingMaximumTenor( String maxTenor )
    {
        getAuditEvent().setStringArg7(maxTenor);
    }

    public String getNewMaximumTenor()
    {
        return getAuditEvent().getStringArg8();
    }

    public void setNewMaximumTenor( String maxTenor )
    {
        getAuditEvent().setStringArg8(maxTenor);
    }

    public String getExistingMinimumTenor()
    {
        return getAuditEvent().getStringArg11();
    }

    public void setExistingMinimumTenor( String minTenor )
    {
        getAuditEvent().setStringArg11(minTenor);
    }

    public String getNewMinimumTenor()
    {
        return getAuditEvent().getStringArg12();
    }

    public void setNewMinimumTenor( String minTenor )
    {
        getAuditEvent().setStringArg12(minTenor);
    }

    public String getExistingCreditStatus()
    {
        return getAuditEvent().getStringArg13();
    }

    public void setExistingCreditStatus( String existingCreditStatus )
    {
        getAuditEvent().setStringArg13(existingCreditStatus);
    }

    public String getNewCreditStatus()
    {
        return getAuditEvent().getStringArg14();
    }

    public void setNewCreditStatus( String newCreditStatus )
    {
        getAuditEvent().setStringArg14(newCreditStatus);
    }

    public ArrayList getFormats( User user )
    {
        return formats;
    }

    public ArrayList getMessageProperties()
    {
        return properties;
    }

    public HashMap getFieldMappings()
    {
        return fieldMappings;
    }

    public String getExistingCurrencyPairGroupExemption()
    {
        return getAuditEvent().getStringArg15();
    }

    public void setExistingCurrencyPairGroupExemption( String currencyPairExemption )
    {
        getAuditEvent().setStringArg15( currencyPairExemption );
    }

    public String getNewCurrencyPairExemption()
    {
        return getAuditEvent().getStringArg16();
    }

    public void setNewCurrencyPairGroupExemption( String currencyPairExemption )
    {
        getAuditEvent().setStringArg16( currencyPairExemption );
    }

    public String getExistingTenorProfile()
    {
        return getAuditEvent().getStringArg17();
    }

    public void setExistingTenorProfile( String tenorProfile )
    {
        getAuditEvent().setStringArg17(tenorProfile);
    }

    public String getNewTenorProfile()
    {
        return getAuditEvent().getStringArg18();
    }

    public void setNewTenorProfile( String tenorProfile )
    {
        getAuditEvent().setStringArg18(tenorProfile);
    }

    public String getExistingMode()
    {
        return getAuditEvent().getStringArg19();
    }

    public void setExistingMode( String mode )
    {
        getAuditEvent().setStringArg19(mode);
    }

    public String getNewMode()
    {
        return getAuditEvent().getStringArg20();
    }

    public void setNewMode( String mode )
    {
        getAuditEvent().setStringArg20(mode);
    }

    @Override
    public void setExistingPFEExcludeMode(String pfeExcludeMode)
    {
        getAuditEvent().setStringArg19( pfeExcludeMode );

    }

    @Override
    public String getExistingPFEExcludeMode()
    {
        return getAuditEvent().getStringArg19();
    }

    @Override
    public void setPFEExcludeMode(String pfeExcludeMode) {
        getAuditEvent().setStringArg20(pfeExcludeMode);
    }

    @Override
    public String getPFEExcludeMode() {
        return getAuditEvent().getStringArg20();
    }

    //----------- Need to update the below for Audit

    /**
     *
     * @param currentUsePFE
     */
    public void setExistingUsePFEConfig(String currentUsePFE )
    {
        getAuditEvent().setStringArg19(currentUsePFE);
    }
    /**
     *
     * @return
     */
    public String getExistingUsePFEConfig()
    {
        return getAuditEvent().getStringArg19();
    }

    /**
     *
     * @param newUsePFE
     */
    public void setNewUsePFEConfig(String newUsePFE )
    {
        getAuditEvent().setStringArg20(newUsePFE);
    }
    /**
     *
     * @return
     */
    public String getNewUsePFEConfig()
    {
        return  getAuditEvent().getStringArg20();
    }

    /**
     * Sets the short name of the newly set PFEConfiguration.
     *
     * @param pfeConfiguration PFEConfiguration
     */
    public void setNewPFEConfiguration( String pfeConfiguration )
    {
        getAuditEvent().setStringArg18(pfeConfiguration);
    }

    /**
     * Sets the existing PFEConfiguration.
     *
     * @param pfeConfiguration PFEConfiguration
     */
    public void setExistingPFEConfiguration( String pfeConfiguration )
    {
        getAuditEvent().setStringArg17(pfeConfiguration);
    }


    public String getNewPFEConfiguration()
    {
       return getAuditEvent().getStringArg18(  );
    }


    public String getExistingPFEConfiguration()
    {
       return getAuditEvent().getStringArg17();
    }

    public void setExistingLEOverride(String leOverride){
        getAuditEvent().setStringArg19(leOverride);
    }

    public String getExistingLEOverride(){return getAuditEvent().getStringArg19();}


    public void setLEOverride(String leOverride){
        getAuditEvent().setStringArg20(leOverride);
    }

    public String getLEOverride(){return getAuditEvent().getStringArg20();}

    public void setExistingOrgDefault(String existingOrgDefault){getAuditEvent().setStringArg19(existingOrgDefault);}

    public String getExistingOrgDefault(){return getAuditEvent().getStringArg19();}

    public void setOrgDefault(String orgDefault){getAuditEvent().setStringArg20(orgDefault);}

    public String getOrgDefault(){return getAuditEvent().getStringArg20();}

    public String getExistingTenorInBusinessDays()
    {
        return getAuditEvent().getStringArg9();
    }

    public void setExistingTenorInBusinessDays( String existingTenorInBusinessDays )
    {
        getAuditEvent().setStringArg9( existingTenorInBusinessDays );
    }

    public String getTenorInBusinessDays()
    {
        return getAuditEvent().getStringArg10();
    }

    public void setTenorInBusinessDays( String tenorInBusinessDays )
    {
        getAuditEvent().setStringArg10( tenorInBusinessDays );
    }

    public Currency getDepositWithdrawCurrency()
    {
        return ( Currency ) getAuditEvent().getEntity10();
    }

    public void setDepositWithdrawCurrency( Currency ccy )
    {
        getAuditEvent ().setEntity10 ( ccy );
    }

    public double getDepositWithdrawAmount( )
    {
        return getAuditEvent ().getDoubleArg14 ();
    }

    public void setDepositWithdrawAmount( double amount )
    {
        getAuditEvent ().setDoubleArg14 ( amount );
    }

    public User getCreditOverrideUser()
    {
        Entity anEntity = getAuditEvent ().getEntity10 ();//reusing entity 10
        return anEntity instanceof User ? ( User ) anEntity : null;
    }

    public void setCreditOverrideUser( User user )
    {
        getAuditEvent ().setEntity10 ( user );
    }

    public void setExistingUseCreditLimitRuleLevelTenorCoefficients(String currentCLRLevelTenorCoefficients )
    {
        getAuditEvent().setStringArg19( currentCLRLevelTenorCoefficients );
    }

    public String getUseCreditLimitRuleLevelTenorCoefficients()
    {
        return getAuditEvent().getStringArg19();
    }

    public void setNewUseCreditLimitRuleLevelTenorCoefficients(String newCLRLevelTenorCoefficients )
    {
        getAuditEvent().setStringArg20( newCLRLevelTenorCoefficients );
    }

    public String getNewUseCreditLimitRuleLevelTenorCoefficients()
    {
        return getAuditEvent().getStringArg20();
    }

    public String getCreditType()
    {
        return getAuditEvent ().getStringArg1 ();
    }

    public void setCreditType( String type )
    {
        getAuditEvent ().setStringArg1 ( type );
    }

    public Double getExistingGrossSpreadMargin()
    {
        return getAuditEvent ().getDoubleArg17 ();
    }

    public void setExistingGrossSpreadMargin( Double margin )
    {
        getAuditEvent ().setDoubleArg17( margin );
    }

    public Double getNewGrossSpreadMargin()
    {
        return getAuditEvent ().getDoubleArg18 ();
    }

    public void setNewGrossSpreadMargin( Double margin )
    {
        getAuditEvent ().setDoubleArg18( margin );
    }
}
