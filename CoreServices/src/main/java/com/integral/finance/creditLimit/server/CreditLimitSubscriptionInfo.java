package com.integral.finance.creditLimit.server;

// Copyright (c) 2015 Integral Development Corp.  All rights reserved.

import com.integral.exception.IdcIllegalArgumentException;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.creditLimit.notification.CreditLimitInfo;
import com.integral.finance.currency.Currency;
import com.integral.time.IdcDate;

import java.util.Arrays;
import java.util.List;

public class CreditLimitSubscriptionInfo
{
	private LegalEntity fiLe, lpLe;

	private Currency limitCcy;

	private IdcDate valueDate;

	private CreditLimitInfo cli;

    private int hashcode;

    private List<Integer> venues;

    private CreditLimitSubscriptionInfo()
    {
    }

    public CreditLimitSubscriptionInfo ( final LegalEntity fiLe, final LegalEntity lpLe, final IdcDate valDate )
    {
        this();
        if ( fiLe == null || lpLe == null || valDate == null )
        {
            throw new IdcIllegalArgumentException( "Invalid params. fiLE=" + fiLe + ",lpLE=" + lpLe + ",valueDate=" + valDate );
        }
        this.fiLe = fiLe;
        this.lpLe = lpLe;
        this.valueDate = valDate;
        this.hashcode = generateHashcode();
    }

	public LegalEntity getFiLe()
	{
		return fiLe;
	}

	public LegalEntity getLpLe()
	{
	  return lpLe;
	}

	public Currency getLimitCcy()
	{
		return limitCcy;
	}

	public void setLimitCcy(Currency limitCcy)
	{
		this.limitCcy = limitCcy;
	}

	public IdcDate getValueDate()
	{
		return valueDate;
	}

	public CreditLimitInfo getCreditLimitInfo()
	{
		return cli;
	}

	public void setCreditLimitInfo( CreditLimitInfo aCli )
	{
		this.cli = aCli;
	}

    public List<Integer> getVenues() {
        return venues;
    }

    public void setVenues(List<Integer> venues) {
        this.venues = venues;
    }

	public String toString()
	{
		return new StringBuilder( 200).append("fiLe=").append(fiLe.getFullyQualifiedName() ).append( ",lpLeId=")
                .append(lpLe.getFullyQualifiedName() ).append( ",valDate=")
                .append(valueDate.getFormattedDate(IdcDate.DD_MMM_YYYY_HYPHEN ) ).append( ",limitCcy=")
                .append(limitCcy ).append( ",cli=" ).append( cli ).toString();
	}

    public boolean equals ( Object object )
    {
        if ( object instanceof CreditLimitSubscriptionInfo )
        {
            CreditLimitSubscriptionInfo other = (CreditLimitSubscriptionInfo) object;
            return other.getFiLe().isSameAs( fiLe )&& other.getLpLe().isSameAs( lpLe ) && other.getValueDate().isSameAs( valueDate );
        }
        return false;
    }

    public int hashCode()
    {
        return hashcode;
    }

    private int generateHashcode()
    {
        long[] arr = new long[] { fiLe.getObjectID(), lpLe.getObjectID(), (long) valueDate.asDays() };
        return Arrays.hashCode(arr);
    }
}
