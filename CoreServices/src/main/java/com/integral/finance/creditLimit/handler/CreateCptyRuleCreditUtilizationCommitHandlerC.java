package com.integral.finance.creditLimit.handler;

// Copyright (c) 2013 Integral Development Corp.  All rights reserved.

import com.integral.finance.creditLimit.CounterpartyCreditLimitRule;
import com.integral.finance.creditLimit.CreditLimitRule;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.finance.creditLimit.admin.CreditAdminServiceLoggerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.rule.Rule;
import com.integral.time.DatePeriod;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;

import java.util.Collection;

/**
 * This class is used as a transaction commit handler which will be executed after transaction is successfully committed.
 * This is used to create credit utilizations for future dates for the counterparty credit limit rule.
 *
 * <AUTHOR> Development Corp.
 */
public class CreateCptyRuleCreditUtilizationCommitHandlerC implements MessageHandler
{
    protected Log log = LogFactory.getLog( CreditAdminServiceLoggerC.class );

    /**
     * Instance variable for counterparty credit limit rule
     */
    private CounterpartyCreditLimitRule cptyRule;

    private Organization creditProviderOrg;


    public CreateCptyRuleCreditUtilizationCommitHandlerC( Organization cpo, CounterpartyCreditLimitRule cclr )
    {
        this();
        this.creditProviderOrg = cpo;
        this.cptyRule = cclr;
    }

    /**
     * Private constructor to avoid creation without credit limit rule.
     */
    private CreateCptyRuleCreditUtilizationCommitHandlerC()
    {
    }

    public Message handle( Message message )
    {
        try
        {
            log.warn( new StringBuilder( 200 ).append( "CCUC.handle.INFO : Creating credit utilizations for cclr=" )
                    .append( cptyRule ).toString() );

            // create credit utilizations for the new organization level counterparty credit limit rule.
            IdcDate startDate = DateTimeFactory.newDate();
            DatePeriod dp = CreditUtilC.getCreditUtilizationPeriod( creditProviderOrg );
            IdcDate endDate = startDate.addDays( dp.asDays() );
            Collection<Rule> rules = cptyRule.getChildrenRules();
            for ( Rule rule : rules )
            {
                CreditUtilizationManagerC.getInstance().createCreditUtilizations( ( CreditLimitRule ) rule, startDate, endDate, creditProviderOrg, true, true );
            }
        }
        catch ( Exception e )
        {
            log.error( new StringBuilder( 200 ).append( "CCUC.handle : Error creating credit utilizations for cclr=" )
                    .append( cptyRule ).append( ",cco=" ).append( cptyRule.getTradingPartyOrganization() ).append( ",cc=" )
                    .append( cptyRule.getTradingParty() ).toString(), e );
        }
        return message;
    }
}

