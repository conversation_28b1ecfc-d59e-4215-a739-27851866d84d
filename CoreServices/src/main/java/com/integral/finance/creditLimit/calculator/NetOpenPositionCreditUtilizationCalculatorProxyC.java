package com.integral.finance.creditLimit.calculator;

// Copyright (c) 2001-2005 Integral Development Corp. All rights reserved.

import com.integral.finance.creditLimit.*;
import com.integral.finance.currency.Currency;
import com.integral.finance.instrument.AmountOfInstrument;
import com.integral.finance.marketData.MarketDataSet;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.price.fx.FXPrice;
import com.integral.time.IdcDate;
import com.integral.workflow.dealing.DealingLimit;

import java.util.HashMap;
import java.util.Map;

/**
 * This calculator is based on the Net Open Position credit netting methodology. In this methodology, the net used
 * amount is calculated as the absolute value of net receivable or net payable whichever is greater.
 *
 * <AUTHOR> Development Corp.
 */
public class NetOpenPositionCreditUtilizationCalculatorProxyC extends CreditUtilizationCalculatorProxyC
{
    NetOpenPositionCreditUtilizationCalculatorProxyC( String name )
    {
        super();
        this.setShortName ( name );
    }

    @Override
    public boolean applyAmount( CreditUtilization cu, CreditUtilizationEvent cue, MarketDataSet mds, boolean excessUtilizationAllowed, Map<String, FXPrice> rateMap )
    {
        return applyAmount( cu, cue, GREATER_NET, mds, excessUtilizationAllowed, rateMap );
    }

    @Override
    public boolean removeAmount( CreditUtilization cu, CreditUtilizationEvent cue, MarketDataSet mds, Map<String, FXPrice> rateMap )
    {
        return removeAmount( cu, cue, GREATER_NET, mds, rateMap );
    }

    @Override
    public boolean updateUtilizedAmount( CreditUtilization cu, CreditUtilizationEvent cue, AmountOfInstrument principalAoi, AmountOfInstrument priceAoi, MarketDataSet mds, boolean excessUtilizationAllowed, Map<String, FXPrice> rateMap )
    {
        return updateUtilizedAmount( cu, cue, GREATER_NET, principalAoi, priceAoi, mds, excessUtilizationAllowed, rateMap );
    }

    @Override
    public DealingLimit getDealingLimit( CreditUtilization cu, Currency baseCcy, Currency varCcy, IdcDate date, Map<String, FXPrice> rateMap, boolean isMaker )
    {
        return getDealingLimit( cu, baseCcy, varCcy, date, GREATER_NET, rateMap, isMaker );
    }

    @Override
    public void updateRealtimeCreditUtilization( RealtimeCreditUtilization rcu )
    {
        updateRealtimeCreditUtilization( rcu, GREATER_NET );
    }

    @Override
    public boolean isCreditAvailable( CurrencyPositionCollection cps, CreditUtilization cu )
    {
        return isCreditAvailable ( cps, cu, GREATER_NET );
    }

    /**
     * Returns the utilization amount from the currency positions of credit utilization. In this case, greater value of net
     * receivable and net payable is used.
     *
     * @param cu credit utilization
     * @return used amount
     */
    @Override
    protected double getUtilizationAmount( CreditUtilization cu )
    {
        return getUtilizationAmount( cu, new HashMap<String, FXPrice>() );
    }

    /**
     * Returns the realtime utilization amount.
     *
     * @param cu      credit utilization
     * @param rateMap rate map
     * @return used amount
     */
    @Override
    protected double getUtilizationAmount( CreditUtilization cu, Map rateMap )
    {
        return cu.getCurrencyPositions().getNetAmount( cu.getCurrency(), ( FXMarketDataSet ) getMarketDataSet( cu ), new HashMap<String, FXPrice>(), true, cu.isApplyPandL() );
    }
}
