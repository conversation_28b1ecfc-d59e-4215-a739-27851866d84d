package com.integral.finance.creditLimit.calculator;

// Copyright (c) 2001-2005 Integral Development Corp. All rights reserved.

/**
 * This calculator is based on the daily settlement credit limit netting methodology which calculates the net used
 * amount based on the net receivable on the value date.
 *
 * <AUTHOR> Development Corp.
 */
public class DailySettlementLimitCreditUtilizationCalculatorProxyC extends CreditUtilizationCalculatorProxyC
{
    DailySettlementLimitCreditUtilizationCalculatorProxyC( String name )
    {
        super();
        this.setShortName ( name );
    }
}
