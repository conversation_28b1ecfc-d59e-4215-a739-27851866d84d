package com.integral.finance.creditLimit.handler;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CounterpartyCreditLimitRule;
import com.integral.finance.creditLimit.CreditLimitConstants;
import com.integral.finance.creditLimit.CreditLimitRuleSet;
import com.integral.finance.creditLimit.CreditLimitRuleSetC;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.CreditUtilizationEvent;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.finance.creditLimit.admin.CreditAdminServiceLoggerC;
import com.integral.finance.creditLimit.admin.CreditLimitAdminService;
import com.integral.finance.creditLimit.admin.CreditLimitAdminServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageHandler;
import com.integral.persistence.PersistenceFactory;
import com.integral.user.Organization;
import org.eclipse.persistence.queries.CursoredStream;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.Collection;

/**
 * This class is reinitialize the credit data structure for a credit provider organization. It primarily deals with
 * removing existing credit utilization events and credit limit rules and credit utilizations associated with them.
 * After removing the existing data, it uses credit limit admin service to re-establish the credit relationship.
 *
 * <AUTHOR> Development Corp.
 */
public abstract class ReinitializeCreditHandlerC implements MessageHandler
{
    protected int commitBatchSize = 100;
    protected Log log = LogFactory.getLog( CreditAdminServiceLoggerC.class );
    protected CreditLimitAdminService creditAdminService = CreditLimitAdminServiceFactory.getCreditLimitAdminService();

    /**
     * Re-initializes the credit data structure for the credit relationship between the credit provider organization and
     * credit counterparty organization.
     *
     * @param creditProviderOrg
     * @param creditCptyOrg
     */
    protected void reinitializeCreditCounterpartyOrganization( Organization creditProviderOrg, Organization creditCptyOrg )
    {
        try
        {
            log.warn( new StringBuilder( 200 ).append( "ReinitializeCreditHandlerC.reinitializeCreditCounterpartyOrganization.INFO : Re-initializing the credit provider and cpty organization. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCptyOrg=" ).append( creditCptyOrg ).toString() );

            boolean success = removeExistingCreditUtilizationEvents( creditProviderOrg, creditCptyOrg );
            if ( !success )
            {
                int retries = 1;
                while ( !success && retries <= CreditLimitConstants.MAXIMUM_RETRY )
                {
                    retries++;
                    success = removeExistingCreditUtilizationEvents( creditProviderOrg, creditCptyOrg );
                }
            }

            // inactivate the credit utilization events in spaces.
            CreditUtilizationManagerC.getInstance().inactivateSpacesCreditUtilizationEventsForCounterpartyOrg( creditProviderOrg, creditCptyOrg );

            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeCounterpartyOrganizationCreditUtilizations( creditProviderOrg, creditCptyOrg );
            Collection<CounterpartyCreditLimitRule> cptyRules = CreditUtilC.queryAllCounterpartyCreditLimitRulesForCounterpartyOrganization( creditProviderOrg, creditCptyOrg, false );
            if ( cptyRules != null && !cptyRules.isEmpty() )
            {
                removeCounterpartyCreditLimitRules( CreditUtilC.getCreditLimitRuleSet( creditProviderOrg ), cptyRules );
            }

            Collection<TradingParty> tps = CounterpartyUtilC.getRelatedOrgTradingParties( creditProviderOrg, creditCptyOrg );

            // now set the credit relationship again based on the credit limit org function configuration.
            for ( TradingParty tp : tps )
            {
                if ( tp.isActive() )
                {
                    PersistenceFactory.newSession().refreshObject( CreditUtilC.getCreditLimitRuleSet( creditProviderOrg ) );
                    creditAdminService.establishCreditRelationship( creditProviderOrg, tp );
                }
                else
                {
                    log.warn( new StringBuilder( 200 ).append( "ReinitializeCreditHandlerC.reinitializeCreditCounterpartyOrganization.INFO : Skipping inactive trading party. tp=" )
                            .append( tp ).append( ",creditProviderOrg=" ).append( creditProviderOrg ).toString() );
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "ReinitializeCreditHandlerC.reinitializeCreditCounterpartyOrganization.ERROR : Error re-initializing creditProviderOrg=" + creditProviderOrg + ",creditCptyOrg=" + creditCptyOrg, e );
        }
    }

    /**
     * Removes all the existing credit utilization events created by the credit relationship between credit provider organization and
     * credit counterparty organization.
     *
     * @param creditProviderOrg
     * @param creditCptyOrg
     * @return result
     */
    protected boolean removeExistingCreditUtilizationEvents( Organization creditProviderOrg, Organization creditCptyOrg )
    {
        CursoredStream cursor = null;
        try
        {
            ReadAllQuery raq = new ReadAllQuery( CreditUtilizationEvent.class );
            raq.useCursoredStream( commitBatchSize, commitBatchSize );
            raq.setSelectionCriteria( CreditUtilC.getCreditUtilizationEventExpression( creditProviderOrg, creditCptyOrg ) );

            int num = 0;
            cursor = ( CursoredStream ) PersistenceFactory.newSession().executeQuery( raq );
            UnitOfWork uow = null;
            while ( !cursor.atEnd() )
            {
                if ( uow == null )
                {
                    uow = PersistenceFactory.newSession().acquireUnitOfWork();
                }
                CreditUtilizationEvent cue = ( CreditUtilizationEvent ) cursor.read();
                uow.deleteObject( uow.registerObject( cue ) );
                num++;
                if ( num == commitBatchSize )
                {
                    num = 0;
                    uow.commit();
                    uow = null;
                    cursor.releasePrevious();
                }
            }
            if ( uow != null && num > 0 )
            {
                uow.commit();
            }
            cursor.close();
        }
        catch ( Exception e )
        {
            log.error( "ReinitializeCreditHandlerC.removeExistingCreditUtilizationEvents.ERROR.", e );
            if ( cursor != null )
            {
                cursor.close();
            }
            return false;
        }
        return true;
    }

    /**
     * Deletes the counterparty credit limit rules.
     *
     * @param clrs
     * @param rules
     */
    protected void removeCounterpartyCreditLimitRules( CreditLimitRuleSet clrs, Collection<CounterpartyCreditLimitRule> rules )
    {
        try
        {
            if ( clrs == null )
            {
                log.warn( "ReinitializeCreditHandlerC.removeCounterpartyCreditLimitRules.WARN : Null credit limit rule set." );
                return;
            }
            UnitOfWork uow = PersistenceFactory.newSession().acquireUnitOfWork();
            uow.addReadOnlyClass( CreditLimitRuleSetC.class );
            for ( CounterpartyCreditLimitRule cclr : rules )
            {
                cclr = ( CounterpartyCreditLimitRule ) uow.refreshObject( cclr );
                CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) uow.registerObject( cclr );
                registeredCclr.setRuleSet( null );
                registeredCclr.getChildrenRules().clear();
                uow.deleteObject( registeredCclr );
            }
            uow.commit();
        }
        catch ( Exception e )
        {
            log.error( "ReinitializeCreditHandlerC.removeCounterpartyCreditLimitRules.ERROR : rules=" + rules, e );
        }
    }
}

