package com.integral.finance.creditLimit.admin;

// Copyright (c) 2019 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.account.AccountEnums;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationFactory;
import com.integral.finance.creditLimit.db.CreditDataQueryService;
import com.integral.finance.creditLimit.db.CreditDataQueryServiceFactory;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyComparatorC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageFactory;
import com.integral.message.MessageHandler;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.rule.SendEmailAction;
import com.integral.rule.SendEmailActionC;
import com.integral.system.mail.SendEmailC;
import com.integral.system.notification.SendEmailThread;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateTime;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.IdcUtilC;
import com.integral.util.PropertyComparatorC;
import com.integral.util.StringUtilC;
import com.integral.util.Tuple;

import java.math.BigDecimal;
import java.sql.Time;
import java.util.*;


public class AccountTransactionHistoryTimeBasedReportFunctor extends AccountTransactionHistoryReportFunctor
{
    private static Log log = LogFactory.getLog( AccountTransactionHistoryTimeBasedReportFunctor.class );
    private static final String INVALID_START_TIME = "FAIL: Invalid start time";
    private static final String INVALID_END_TIME = "FAIL: Invalid end time";

    private static final String ACCOUNT_PROVIDER_ORG = "accountProviderOrg";
    private static final String ACCOUNT_CPTY_ORG = "accountCptyOrg";
    private static final String ACCOUNT_CPTY = "accountCpty";
    private static final String START_DATE = "startDate";
    private static final String END_DATE = "endDate";
    private static final String FROM_EMAIL_ADDRESS = "fromEmailAddress";
    private static final String TO_EMAIL_ADDRESSES = "toEmailAddresses";
    private static final String START_TIME = "startTime";
    private static final String END_TIME = "endTime";

    private static final String ACCOUNT_PROVIDER_PARAM = "AccountProviderOrg";
    private static final String ACCOUNT_CPTY_PARAM = "AccountCpty";
    private static final String ACCOUNT_CPTY_ORG_PARAM = "AccountCptyOrg";
    private static final String START_DATE_PARAM = "StartDate";
    private static final String END_DATE_PARAM = "EndDate";
    private static final String START_TIME_PARAM = "StartTime";
    private static final String END_TIME_PARAM = "EndTime";

    private String accountProviderOrg;
    private String accountCptyOrg;
    private String accountCpty;
    private String fromEmailAddress;
    private String toEmailAddresses;
    private String startDate;
    private String endDate;
    private String startTime;
    private String endTime;

    private static MessageHandler notificationHandler = null;
    private static CreditDataQueryService _cqs = CreditDataQueryServiceFactory.getCreditDataQueryService ();

    public static void setNotificationHandler ( MessageHandler mh )
    {
        notificationHandler = mh;
    }

    @Override
    public String getDescription()
    {
        return "Functor to report the list account transactions for the specified date time period.";
    }

    @Override
    public List<String> getFunctorProperties()
    {
        List<String> properties = new ArrayList<String> ();
        properties.add( ACCOUNT_PROVIDER_ORG );
        properties.add ( ACCOUNT_CPTY_ORG );
        properties.add ( ACCOUNT_CPTY );
        properties.add ( FROM_EMAIL_ADDRESS );
        properties.add( TO_EMAIL_ADDRESSES );
        properties.add ( START_DATE );
        properties.add ( END_DATE );
        properties.add ( START_TIME );
        properties.add ( END_TIME );
        return properties;
    }

    public String getAccountProviderOrg()
    {
        return accountProviderOrg;
    }

    public void setAccountProviderOrg( String org )
    {
        this.accountProviderOrg = org;
    }

    public String getAccountCptyOrg()
    {
        return accountCptyOrg;
    }

    public void setAccountCptyOrg( String org )
    {
        this.accountCptyOrg = org;
    }

    public String getAccountCpty()
    {
        return accountCpty;
    }

    public void setAccountCpty( String cpty )
    {
        this.accountCpty = cpty;
    }

    public String getFromEmailAddress()
    {
        return fromEmailAddress;
    }

    public void setFromEmailAddress( String emailAddress )
    {
        this.fromEmailAddress = emailAddress;
    }

    public String getToEmailAddresses()
    {
        return toEmailAddresses;
    }

    public void setToEmailAddresses( String emailAddresses )
    {
        this.toEmailAddresses = emailAddresses;
    }

    public String getStartDate()
    {
        return startDate;
    }

    public void setStartDate ( String date )
    {
        this.startDate = date;
    }

    public String getEndDate()
    {
        return endDate;
    }

    public void setEndDate( String date )
    {
        this.endDate = date;
    }

    public String getStartTime()
    {
        return startTime;
    }

    public void setStartTime( String timeStr )
    {
        this.startTime = timeStr;
    }

    public String getEndTime()
    {
        return endTime;
    }

    public void setEndTime( String timeStr )
    {
        this.endTime = timeStr;
    }

    public String validate()
    {
        String result = super.validate ();
        if ( !SUCCESS.equals ( result ) )
        {
            return result;
        }

        if ( validateTime ( getStartTime () ) != null )
        {
            return INVALID_START_TIME;
        }

        if ( validateTime (  getEndTime () ) != null )
        {
            return INVALID_END_TIME;
        }

        Tuple<IdcDate, IdcDate> dates = getFromToDate ( getStartDate (), getEndDate (), IdcUtilC.getSessionContextUser (), false );
        IdcDate date0 = dates.first;
        IdcDate date1 = dates.second;
        IdcDateTime dateTime0 = startTime != null ? DateTimeFactory.newDateTime ( date0, getTime ( startTime ), TimeZone.getTimeZone ( "GMT" ) ) : DateTimeFactory.newDateTime ( date0.asJdkDate ().getTime () );
        IdcDateTime dateTime1 = endTime != null ? DateTimeFactory.newDateTime ( date1, getTime ( endTime ), TimeZone.getTimeZone ( "GMT" ) ) : DateTimeFactory.newDateTime ( date1.asJdkDate ().getTime () );
        if ( dateTime0.isLaterThan ( dateTime1 ))
        {
            return INVALID_END_TIME;
        }

        return SUCCESS;
    }

    protected void generateReport( WorkflowMessage msg )
    {
        try
        {
            User loginUser = IdcUtilC.getSessionContextUser ();
            Organization apo = ReferenceDataCacheC.getInstance ().getOrganization ( accountProviderOrg );
            Organization aco = ReferenceDataCacheC.getInstance ().getOrganization ( accountCptyOrg );
            LegalEntity acoLE = CounterpartyUtilC.getLegalEntity ( aco, accountCpty );
            TradingParty atp = acoLE.getTradingParty ( apo );
            if ( atp == null )
            {
                log.warn ( "AHTRF.generateReport : No trading party found in org=" + apo + " for legal entity=" + acoLE.getFullyQualifiedName () );
                return;
            }

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( apo, atp );
            if ( cclr == null )
            {
                log.warn ( "AHTRF.generateReport : No active credit line found with org=" + apo + " for legal entity=" + acoLE.getFullyQualifiedName () );
                return;
            }

            CreditLimitRule clr = ( CreditLimitRule ) cclr.getChildRule ( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
            if ( clr == null || !clr.isEnabled () || !CreditUtilC.isCashSettlement ( clr ) )
            {
                log.warn ( "AHTRF.generateReport : credit or account methodology is not enabled with provider org=" + apo + " for legal entity=" + acoLE.getFullyQualifiedName () + ",clr=" + clr );
                return;
            }
            Tuple<IdcDate, IdcDate> dates = getFromToDate ( getStartDate (), getEndDate (), loginUser, false );
            IdcDate date0 = dates.first;
            IdcDate date1 = dates.second;
            IdcDateTime dateTime0 = startTime != null ? DateTimeFactory.newDateTime ( date0, getTime ( startTime ), TimeZone.getTimeZone ( "GMT" ) ) : DateTimeFactory.newDateTime ( date0.asJdkDate ().getTime () );
            IdcDateTime dateTime1 = endTime != null ? DateTimeFactory.newDateTime ( date1, getTime ( endTime ), TimeZone.getTimeZone ( "GMT" ) ) : DateTimeFactory.newDateTime ( date1.asJdkDate ().getTime () );

            List<CreditUtilizationEvent> cueList = new ArrayList<CreditUtilizationEvent> ();
            Collection<CreditUtilizationEvent> nonSpacesEvents = _cqs.getNonSpacesCreditUtilizationEventsByCreatedDateTime ( apo, cclr, dateTime0, dateTime1 );
            if ( nonSpacesEvents != null )
            {
                for ( CreditUtilizationEvent cue : nonSpacesEvents )
                {
                    CreditUtilization creditUtil = cue.getCreditUtilization ();
                    if ( ! (creditUtil instanceof DailyCreditUtilization) )
                    {
                        cueList.add ( cue );
                    }
                }
            }
            Iterator<CreditUtilizationEvent> spacesEvents = _cqs.getSpacesCreditUtilizationEventsByCreatedDateTime ( apo, cclr, dateTime0, dateTime1 );
            if ( spacesEvents != null )
            {
                while ( spacesEvents.hasNext () )
                {
                    CreditUtilizationEvent cue = spacesEvents.next ();
                    CreditUtilization creditUtil = cue.getCreditUtilization ();
                    if ( ! (creditUtil instanceof DailyCreditUtilization) )
                    {
                        cueList.add ( cue );
                    }
                }
            }

            Collections.sort ( cueList, new PropertyComparatorC ( "createdDateTime", false ) );
            SendEmailAction emailAction = buildAccountTransactionHistoryReportEmail ( cueList, apo );

            if ( notificationHandler != null )
            {
                WorkflowMessage workflowMessage = MessageFactory.newWorkflowMessage ();
                workflowMessage.setParameterValue ( "SendEmailAction", emailAction );
                notificationHandler.handle ( workflowMessage );
            }

            SendEmailC.getEventsPool ().execute ( new SendEmailThread ( emailAction ) );
        }
        catch ( Exception e )
        {
            log.error ( "AHTRF.generateReport : Exception while generating the account transaction history report for account provider="
                    + accountProviderOrg + ",cptyOrg=" + accountCptyOrg + ",cpty=" + accountCpty, e );
        }
    }

    private SendEmailAction buildAccountTransactionHistoryReportEmail( final Collection<CreditUtilizationEvent> cues, final Organization apo )
    {
        SendEmailAction emailAction = null;
        try
        {
            String subject = CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean ().getCreditTransactionHistoryTimeBasedReportEmailSubject ( apo );

            String ccyBalancesTable = createCurrencyBalancesTable( cues );
            String body = new StringBuilder( 1000 ).append ( ccyBalancesTable ).append ( "\n" ).append ( "\n" )
                    .append ( "\n" ).append (  createEventsTable( cues  ) ).toString () ;

            if ( subject != null && body != null )
            {
                emailAction = new SendEmailActionC ();
                Collection<String> tos = IdcUtilC.arrayAsArrayList( IdcUtilC.getSubstring( getToEmailAddresses (), "," ) );
                emailAction.setTos( tos );
                emailAction.setFrom( getFromEmailAddress () );
                emailAction.setSubject( subject );
                emailAction.setBody( body );

                // set parameters in the send mail action.
                emailAction.putCustomField( ACCOUNT_PROVIDER_PARAM, getAccountProviderOrg () );
                emailAction.putCustomField( ACCOUNT_CPTY_ORG_PARAM, getAccountCptyOrg () );
                emailAction.putCustomField( ACCOUNT_CPTY_PARAM, getAccountCpty () );
                emailAction.putCustomField ( START_DATE_PARAM, getStartDate () );
                emailAction.putCustomField ( END_DATE_PARAM, !StringUtilC.isNullOrEmpty ( getEndDate () ) ? " - " + getEndDate () : " " );
                emailAction.putCustomField ( START_TIME_PARAM, getStartTime () );
                emailAction.putCustomField ( END_TIME_PARAM, !StringUtilC.isNullOrEmpty ( getEndTime () ) ? " - " + getEndTime () : " " );
                log.info( new StringBuilder( 300 ).append( "AHTRF.buildAccountTransactionHistoryReportEmail : email details. subject=" )
                        .append( subject ).append( ",body=" ).append( body ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "AHTRF.buildAccountTransactionHistoryReportEmail : Exception.", e );
        }
        return emailAction;
    }

    private String validateTime( String timeStr )
    {
        if ( StringUtilC.isNullOrEmpty ( timeStr  ) || "N/A".equalsIgnoreCase( timeStr ) || "null".equalsIgnoreCase( timeStr ) )
        {
            return "InvalidTime";
        }
        try
        {
            Time.valueOf( timeStr.trim () ) ;
        }
        catch( Exception e )
        {
            log.info( "AHTRF.validateTime : invalid time specified. time=" + timeStr  + ",ex=" + e.getMessage() );
            return "InvalidTime";
        }
        return null;
    }

    private Time getTime( String timeStr )
    {
        try
        {
            return Time.valueOf ( timeStr );
        }
        catch ( Exception e )
        {
            log.info ( "AHTRF.getTime : invalid time string. time=" + timeStr );
        }
        return null;
    }

    private Map<Currency, BigDecimal> getCurrencyEndingBalances( final Collection<CreditUtilizationEvent> cues )
    {
        Map<Currency, BigDecimal> ccyMap = new TreeMap<Currency, BigDecimal>( new CurrencyComparatorC () );
        Map<Currency, Tuple<Long, BigDecimal>> ccyBalanceMap = new HashMap<Currency, Tuple<Long, BigDecimal>>();
        for ( CreditUtilizationEvent cue: cues )
        {
            Currency principalCcy = cue.getPrincipalCurrency ();
            if ( principalCcy != null )
            {
                Tuple<Long, BigDecimal> ccyBal = ccyBalanceMap.get ( principalCcy );
                if ( ccyBal == null || cue.getCreatedTimestamp ().getTime () > ccyBal.first )
                {
                    Tuple<Long, BigDecimal> tuple = new Tuple<Long, BigDecimal> (  );
                    tuple.first = cue.getCreatedTimestamp ().getTime ();
                    tuple.second = cue.getPrincipalBalance ();
                    ccyBalanceMap.put ( principalCcy, tuple );
                }
            }
            Currency priceCcy = cue.getPriceCurrency ();
            if ( priceCcy != null && AccountEnums.AccountEventType.TRADE.equals ( cue.getEventType () ) )
            {
                Tuple<Long, BigDecimal> ccyBal = ccyBalanceMap.get ( priceCcy );
                if ( ccyBal == null || cue.getCreatedTimestamp ().getTime () > ccyBal.first )
                {
                    Tuple<Long, BigDecimal> tuple = new Tuple<Long, BigDecimal> (  );
                    tuple.first = cue.getCreatedTimestamp ().getTime ();
                    tuple.second = cue.getPriceBalance ();
                    ccyBalanceMap.put ( priceCcy, tuple );
                }
            }
        }

        for ( Currency ccy: ccyBalanceMap.keySet () )
        {
            ccyMap.put ( ccy, ccyBalanceMap.get ( ccy ).second );
        }
        return ccyMap;
    }

    private String createCurrencyBalancesTable( final Collection<CreditUtilizationEvent> cues )
    {
        Map<Currency, BigDecimal> ccyMap = getCurrencyEndingBalances ( cues );

        StringBuilder buf = new StringBuilder( 300 );

        // add the header
        int i = 0;
        for ( Currency ccy: ccyMap.keySet () )
        {
            if ( i > 0 )
            {
                buf.append ( ','  );
            }
            buf.append ( ccy.getShortName () );
            i++;
        }

        buf.append ( "\n" );

        // add the table data
        i = 0;
        for ( Currency ccy: ccyMap.keySet () )
        {
            if ( i > 0 )
            {
                buf.append ( ','  );
            }
            BigDecimal ccyAmt = ccyMap.get ( ccy );
            buf.append ( ccy.getDecimalFormat ( AMOUNT_FORMAT_STRING ).format ( ccyAmt.negate () ) );
            i++;
        }
        return buf.toString ();
    }
}
