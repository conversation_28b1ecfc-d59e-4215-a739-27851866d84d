package com.integral.finance.creditLimit;

// Copyright (c) 2022 Integral Development Corp.  All rights reserved.

import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.configuration.CreditConfigurationFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;
import com.integral.util.StringUtilC;

/**
 * This class is used to manage the auto position liquidation process upon close positions percentage breach.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditMarginLiquidationServiceC implements CreditMarginLiquidationService
{
    protected Log log = LogFactory.getLog ( this.getClass () );

    private MarginCallService marginCallService;

    /**
     * Singleton instance
     */
    private static CreditMarginLiquidationServiceC _creditMarginLiquidationService;

    /*
     * Static initialization.
     */
    static
    {
        _creditMarginLiquidationService = new CreditMarginLiquidationServiceC ();
    }

    /**
     * Private empty constructor.
     */
    protected CreditMarginLiquidationServiceC ( )
    {
    }

    /**
     * Returns the singleton instance of CreditMarginLiquidationService.
     *
     * @return credit margin liquidation service.
     */
    public static CreditMarginLiquidationServiceC getInstance ( )
    {
        return _creditMarginLiquidationService;
    }

    /**
     * Use only for testing
     *
     * @param service margin liquidation service
     */
    public static void _set_instance ( CreditMarginLiquidationServiceC service )
    {
        _creditMarginLiquidationService = service;
    }

    public void registerMarginCallService ( MarginCallService service )
    {
        marginCallService = service;
    }

    public void closePositions ( CreditWorkflowMessage cwm )
    {
        try
        {
            MarginCallService service = getMarginCallService ();
            if ( service != null )
            {
                service.closePositions ( cwm );
            }
            else
            {
                log.info ( "CMLS.closePositions - no margin call service configured." );
            }
        }
        catch ( Exception e )
        {
            log.error ( "CMLS.closePositions - Exception while sending the close out position notification to YM for cptyRule=" + cwm.getCounterpartyCreditLimitRule (), e );
        }
    }

    public void onClosePositionsStatusUpdate ( CreditWorkflowMessage cwm )
    {
        try
        {
            log.info ( "CMLS.onClosePositionsStatusUpdate - start stop out status update. cwm=" + cwm );
            Organization cpo = cwm.getOrganization ();
            Organization cco = cwm.getTradingPartyOrganization ();
            TradingParty cc = cco.getDefaultDealingEntity ().getTradingParty ( cpo );
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( cpo, cc );
            IdcUtilC.setSessionContextUser ( cpo.getDefaultDealingUser () );
            CreditNotificationManagerC.getInstance ().updateStopOutState ( cclr );
            log.info ( "CMLS.onClosePositionsStatusUpdate - finished stop out status update. cwm=" + cwm );
        }
        catch ( Exception e )
        {
            log.error ( "CMLS.onClosePositionsStatusUpdate - Exception while updating the positions close request response. cwm=" + cwm, e );
        }
    }

    private synchronized MarginCallService getMarginCallService ( )
    {
        try
        {
            if ( marginCallService == null )
            {
                String className = CreditConfigurationFactory.getCreditConfigurationMBean ().getCreditMarginCallServiceClassName ();
                if ( !StringUtilC.isNullOrEmpty ( className ) )
                {
                    marginCallService = ( MarginCallService ) Class.forName ( className ).newInstance ();
                    marginCallService.initialize ();
                }
            }
        }
        catch ( Exception e )
        {
            log.error ( "CMLS.getMarginCallService - Exception in initializing the margin call service.", e );
        }
        return marginCallService;
    }
}

