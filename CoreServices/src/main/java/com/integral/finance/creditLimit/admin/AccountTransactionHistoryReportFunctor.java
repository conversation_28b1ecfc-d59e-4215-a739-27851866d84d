package com.integral.finance.creditLimit.admin;

// Copyright (c) 2019 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.account.AccountEnums;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationFactory;
import com.integral.finance.creditLimit.db.CreditDataQueryService;
import com.integral.finance.creditLimit.db.CreditDataQueryServiceFactory;
import com.integral.finance.currency.Currency;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageFactory;
import com.integral.message.MessageHandler;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.rule.SendEmailAction;
import com.integral.rule.SendEmailActionC;
import com.integral.scheduler.ScheduleFunctorC;
import com.integral.system.mail.SendEmailC;
import com.integral.system.notification.SendEmailThread;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.IdcUtilC;
import com.integral.util.PropertyComparatorC;
import com.integral.util.StringUtilC;
import com.integral.util.Tuple;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


public class AccountTransactionHistoryReportFunctor extends ScheduleFunctorC
{
    private static Log log = LogFactory.getLog( AccountTransactionHistoryReportFunctor.class );
    private static final String INVALID_EMAIL_ADDRESS = "FAIL: Invalid email address.";
    private static final String INVALID_ACCOUNT_PROVIDER_ORG = "FAIL: Invalid account provider org.";
    private static final String INVALID_ACCOUNT_CPTY_ORG = "FAIL: Invalid account counterparty org.";
    private static final String INVALID_ACCOUNT_CPTY = "FAIL: Invalid account counterparty.";
    private static final String INVALID_ACCOUNT_TRADING_PARTY = "FAIL: Invalid account tradingparty.";
    private static final String INVALID_START_DATE = "FAIL: Invalid start date.";
    private static final String INVALID_END_DATE = "FAIL: Invalid end date.";
    private static final String INVALID_USE_TRADE_DATE = "FAIL: Invalid use trade date flag.";

    private static final String ACCOUNT_PROVIDER_ORG = "accountProviderOrg";
    private static final String ACCOUNT_CPTY_ORG = "accountCptyOrg";
    private static final String ACCOUNT_CPTY = "accountCpty";
    private static final String START_DATE = "startDate";
    private static final String END_DATE = "endDate";
    private static final String FROM_EMAIL_ADDRESS = "fromEmailAddress";
    private static final String TO_EMAIL_ADDRESSES = "toEmailAddresses";
    private static final String USE_TRADE_DATE = "useTradeDate";

    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    protected static final String AMOUNT_FORMAT_STRING = "################.############";
    private static final String ACCOUNT_PROVIDER_PARAM = "AccountProviderOrg";
    private static final String ACCOUNT_CPTY_PARAM = "AccountCpty";
    private static final String ACCOUNT_CPTY_ORG_PARAM = "AccountCptyOrg";
    private static final String START_DATE_PARAM = "StartDate";
    private static final String END_DATE_PARAM = "EndDate";

    public static final String TODAY = "Today";
    public static final String YESTERDAY = "Yesterday";
    public static final String MTD = "MTD";
    public static final String WTD = "WTD";
    public static final String YTD = "YTD";

    private String accountProviderOrg;
    private String accountCptyOrg;
    private String accountCpty;
    private String fromEmailAddress;
    private String toEmailAddresses;
    private String startDate;
    private String endDate;
    private String useTradeDate;

    private static MessageHandler notificationHandler = null;
    private static CreditDataQueryService _cqs = CreditDataQueryServiceFactory.getCreditDataQueryService ();

    public static void setNotificationHandler ( MessageHandler mh )
    {
        notificationHandler = mh;
    }

    public void execute( WorkflowMessage msg )
    {
        long t0 = System.currentTimeMillis();
        log.warn( "ATHRF.execute : Begin execute the transaction" );
        try
        {
            generateReport( msg );
        }
        catch ( Exception e )
        {
            log.error( "ATHRF.execute : Error while generating the report of list of account transactions.", e );
        }
        log.info( "ATHRF.execute : Finished generating account transaction history report functor. took ms= " + ( System.currentTimeMillis() - t0 ) );
    }

    @Override
    public String getDescription()
    {
        return "Functor to report the list account transactions for the specified period.";
    }

    @Override
    public List<String> getFunctorProperties()
    {
        List<String> properties = super.getFunctorProperties ();
        properties.add( ACCOUNT_PROVIDER_ORG );
        properties.add ( ACCOUNT_CPTY_ORG );
        properties.add ( ACCOUNT_CPTY );
        properties.add ( FROM_EMAIL_ADDRESS );
        properties.add( TO_EMAIL_ADDRESSES );
        properties.add ( START_DATE );
        properties.add ( END_DATE );
        properties.add ( USE_TRADE_DATE );
        return properties;
    }

    public String getAccountProviderOrg()
    {
        return accountProviderOrg;
    }

    public void setAccountProviderOrg( String org )
    {
        this.accountProviderOrg = org;
    }

    public String getAccountCptyOrg()
    {
        return accountCptyOrg;
    }

    public void setAccountCptyOrg( String org )
    {
        this.accountCptyOrg = org;
    }

    public String getAccountCpty()
    {
        return accountCpty;
    }

    public void setAccountCpty( String cpty )
    {
        this.accountCpty = cpty;
    }

    public String getFromEmailAddress()
    {
        return fromEmailAddress;
    }

    public void setFromEmailAddress( String emailAddress )
    {
        this.fromEmailAddress = emailAddress;
    }

    public String getToEmailAddresses()
    {
        return toEmailAddresses;
    }

    public void setToEmailAddresses( String emailAddresses )
    {
        this.toEmailAddresses = emailAddresses;
    }

    public String getStartDate()
    {
        return startDate;
    }

    public void setStartDate ( String date )
    {
        this.startDate = date;
    }

    public String getEndDate()
    {
        return endDate;
    }

    public void setEndDate( String date )
    {
        this.endDate = date;
    }

    public String getUseTradeDate()
    {
        return useTradeDate;
    }

    public void setUseTradeDate( String flag )
    {
        this.useTradeDate = flag;
    }

    public String validate()
    {
        String result = super.validate ();
        if ( !SUCCESS.equals ( result ) )
        {
            return result;
        }

        if ( StringUtilC.isNullOrEmpty ( getToEmailAddresses () ) || StringUtilC.isNullOrEmpty ( getFromEmailAddress () ) )
        {
            return INVALID_EMAIL_ADDRESS;
        }

        if ( !CreditUtilC.validateEmailId ( getFromEmailAddress () ) )
        {
            return INVALID_EMAIL_ADDRESS;
        }

        String[] emailIds = getToEmailAddresses ().split ( "," );
        for ( String emailId: emailIds )
        {
            if ( !CreditUtilC.validateEmailId ( emailId ) )
            {
                return INVALID_EMAIL_ADDRESS;
            }
        }

        if ( StringUtilC.isNullOrEmpty ( getAccountProviderOrg () ) )
        {
            return INVALID_ACCOUNT_PROVIDER_ORG;
        }
        if ( StringUtilC.isNullOrEmpty ( getAccountCptyOrg () ) )
        {
            return INVALID_ACCOUNT_CPTY_ORG;
        }
        if ( StringUtilC.isNullOrEmpty ( getAccountCpty () ) )
        {
            return INVALID_ACCOUNT_CPTY;
        }

        Organization apo = ReferenceDataCacheC.getInstance ().getOrganization ( getAccountProviderOrg () );
        if ( apo == null )
        {
            return INVALID_ACCOUNT_PROVIDER_ORG;
        }
        Organization aco = ReferenceDataCacheC.getInstance ().getOrganization ( getAccountCptyOrg () );
        if ( aco == null )
        {
            return INVALID_ACCOUNT_CPTY_ORG;
        }

        LegalEntity acoLE = CounterpartyUtilC.getLegalEntity ( aco, getAccountCpty () );
        if ( acoLE == null )
        {
            return INVALID_ACCOUNT_CPTY;
        }

        TradingParty tp = acoLE.getTradingParty ( apo );
        if ( tp == null )
        {
            return INVALID_ACCOUNT_TRADING_PARTY;
        }

        String startDateStr = getStartDate ();
        if ( StringUtilC.isNullOrEmpty ( startDateStr) )
        {
            return INVALID_START_DATE;
        }

        boolean specialDateRanges = MTD.equalsIgnoreCase ( startDateStr ) || WTD.equalsIgnoreCase ( startDateStr )
                || YTD.equalsIgnoreCase ( startDateStr ) || TODAY.equalsIgnoreCase ( startDateStr ) || YESTERDAY.equalsIgnoreCase ( startDateStr );

        IdcDate startDateObj = null, endDateObj = null;
        if ( !specialDateRanges )
        {
            startDateObj = getDate ( startDateStr, IdcUtilC.getSessionContextUser ()  );
            if ( startDateObj == null )
            {
                return INVALID_START_DATE;
            }
        }

        String endDateStr = getEndDate ();
        boolean specialEndDateRanges = TODAY.equalsIgnoreCase ( endDateStr );

        if ( !StringUtilC.isNullOrEmpty ( endDateStr ) )
        {
            if ( !specialEndDateRanges )
            {
                endDateObj = getDate ( getEndDate (), IdcUtilC.getSessionContextUser () );
                if ( endDateObj == null )
                {
                    return INVALID_END_DATE;
                }
            }
        }

        if ( endDateObj != null && startDateObj != null && endDateObj.isEarlierThan ( startDateObj ) )
        {
            return INVALID_END_DATE;
        }

        String useTradeDateFlag = getUseTradeDate ();
        if ( !StringUtilC.isNullOrEmpty ( useTradeDateFlag ) )
        {
            boolean validValues = "Y".equalsIgnoreCase ( useTradeDateFlag ) || "N".equalsIgnoreCase ( useTradeDateFlag );
            if ( !validValues )
            {
                return INVALID_USE_TRADE_DATE;
            }
        }

        return SUCCESS;
    }

    protected void generateReport( WorkflowMessage msg )
    {
        try
        {
            User loginUser = IdcUtilC.getSessionContextUser ();
            Organization apo = ReferenceDataCacheC.getInstance ().getOrganization ( accountProviderOrg );
            Organization aco = ReferenceDataCacheC.getInstance ().getOrganization ( accountCptyOrg );
            LegalEntity acoLE = CounterpartyUtilC.getLegalEntity ( aco, accountCpty );
            TradingParty atp = acoLE.getTradingParty ( apo );
            if ( atp == null )
            {
                log.warn ( "ATHRF.generateReport : No trading party found in org=" + apo + " for legal entity=" + acoLE.getFullyQualifiedName () );
                return;
            }

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( apo, atp );
            if ( cclr == null )
            {
                log.warn ( "ATHRF.generateReport : No active credit line found with org=" + apo + " for legal entity=" + acoLE.getFullyQualifiedName () );
                return;
            }

            CreditLimitRule clr = ( CreditLimitRule ) cclr.getChildRule ( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
            if ( clr == null || !clr.isEnabled () || !CreditUtilC.isCashSettlement ( clr ) )
            {
                log.warn ( "ATHRF.generateReport : credit or account methodology is not enabled with provider org=" + apo + " for legal entity=" + acoLE.getFullyQualifiedName () + ",clr=" + clr );
                return;
            }
            boolean useTradeDate = StringUtilC.isNullOrEmpty ( getUseTradeDate () ) || "Y".equalsIgnoreCase ( getUseTradeDate () );
            Tuple<IdcDate, IdcDate> dates = getFromToDate ( getStartDate (), getEndDate (), loginUser, useTradeDate );
            IdcDate date0 = dates.first;
            IdcDate date1 = dates.second;
            List<CreditUtilizationEvent> cueList = new ArrayList<CreditUtilizationEvent> ();
            Collection<CreditUtilizationEvent> nonSpacesEvents = useTradeDate ? _cqs.getNonSpacesCreditUtilizationEventsByTradeDate ( apo, cclr, date0, date1 ) :  _cqs.getNonSpacesCreditUtilizationEventsByCreatedDate ( apo, cclr, date0, date1 );
            if ( nonSpacesEvents != null )
            {
                for ( CreditUtilizationEvent cue : nonSpacesEvents )
                {
                    CreditUtilization creditUtil = cue.getCreditUtilization ();
                    if ( ! (creditUtil instanceof DailyCreditUtilization) )
                    {
                        cueList.add ( cue );
                    }
                }
            }
            Iterator<CreditUtilizationEvent> spacesEvents = useTradeDate ? _cqs.getSpacesCreditUtilizationEventsByTradeDate( apo, cclr, date0, date1 ) : _cqs.getSpacesCreditUtilizationEventsByCreatedDate ( apo, cclr, date0, date1 );
            if ( spacesEvents != null )
            {
                while ( spacesEvents.hasNext () )
                {
                    CreditUtilizationEvent cue = spacesEvents.next ();
                    CreditUtilization creditUtil = cue.getCreditUtilization ();
                    if ( ! (creditUtil instanceof DailyCreditUtilization) )
                    {
                        cueList.add ( cue );
                    }
                }
            }

            Collections.sort ( cueList, new PropertyComparatorC ( "createdDateTime", false ) );
            SendEmailAction emailAction = buildAccountTransactionHistoryReportEmail ( cueList, apo );

            if ( notificationHandler != null )
            {
                WorkflowMessage workflowMessage = MessageFactory.newWorkflowMessage ();
                workflowMessage.setParameterValue ( "SendEmailAction", emailAction );
                notificationHandler.handle ( workflowMessage );
            }

            SendEmailC.getEventsPool ().execute ( new SendEmailThread ( emailAction ) );
        }
        catch ( Exception e )
        {
            log.error ( "ATHRF.generateReport : Exception while generating the account transaction history report for account provider="
                    + accountProviderOrg + ",cptyOrg=" + accountCptyOrg + ",cpty=" + accountCpty, e );
        }
    }

    private SendEmailAction buildAccountTransactionHistoryReportEmail( final Collection<CreditUtilizationEvent> cues, final Organization apo )
    {
        SendEmailAction emailAction = null;
        try
        {
            String subject = CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean ().getCreditTransactionHistoryReportEmailSubject ( apo );

            String body = createEventsTable( cues );

            if ( subject != null && body != null )
            {
                emailAction = new SendEmailActionC ();
                Collection<String> tos = IdcUtilC.arrayAsArrayList( IdcUtilC.getSubstring( getToEmailAddresses (), "," ) );
                emailAction.setTos( tos );
                emailAction.setFrom( getFromEmailAddress () );
                emailAction.setSubject( subject );
                emailAction.setBody( body );

                // set parameters in the send mail action.
                emailAction.putCustomField( ACCOUNT_PROVIDER_PARAM, getAccountProviderOrg () );
                emailAction.putCustomField( ACCOUNT_CPTY_ORG_PARAM, getAccountCptyOrg () );
                emailAction.putCustomField( ACCOUNT_CPTY_PARAM, getAccountCpty () );
                emailAction.putCustomField ( START_DATE_PARAM, getStartDate () );
                emailAction.putCustomField ( END_DATE_PARAM, getEndDate () != null ? " - " + getEndDate () : " " );
                log.info( new StringBuilder( 300 ).append( "ATHRF.buildAccountTransactionHistoryReportEmail : email details. subject=" )
                        .append( subject ).append( ",body=" ).append( body ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "ATHRF.buildAccountTransactionHistoryReportEmail : Exception.", e );
        }
        return emailAction;
    }

    protected String createEventsTable( Collection<CreditUtilizationEvent> cues )
    {
        StringBuilder buf = new StringBuilder( cues.size () * 100 );
        // add the header
        buf.append ( "Trade Date,Asset,Event,Order ID,ID,Timestamp,Amount,Balance,Ticket ID,Notes" );
        for ( CreditUtilizationEvent cue: cues )
        {
            boolean isDepositWithdrawEvent = AccountEnums.AccountEventType.DEPOSIT.equals ( cue.getEventType () ) || AccountEnums.AccountEventType.WITHDRAW.equals ( cue.getEventType () );
            boolean isPrincipalCcyBuy = cue.getBuySell() == CreditLimit.BUY;
            Currency ccy = cue.getPrincipalCurrency ();
            buf.append ( "\n" );
            buf.append ( cue.getTradeDate ().getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) ).append ( ',' );
            buf.append ( ccy.getShortName () ).append ( ',' );
            buf.append ( AccountEnums.AccountEventType.getEventTypeDescription( cue.getEventType () ) ).append ( ',' );
            buf.append ( cue.getOrderId () != null ? cue.getOrderId () : "" ).append ( ',' );
            buf.append ( cue.getTransactionId () ).append ( ',' );
            buf.append ( getFormattedCreatedTime ( cue.getCreatedTimestamp (), DATE_TIME_FORMAT ) ).append ( ',' );
            if ( isDepositWithdrawEvent )
            {
                buf.append( ccy.getDecimalFormat ( AMOUNT_FORMAT_STRING ).format ( cue.getPrincipal() != 0.0 ?
                        cue.getPrincipalCurrency().round( cue.getPrincipal() * ( isPrincipalCcyBuy ? -1 : 1 ) ) : 0.0 ) ).append ( ',' );
            }
            else
            {
                buf.append ( ccy.getDecimalFormat ( AMOUNT_FORMAT_STRING ).format( cue.getPrincipal() != 0.0 ?
                        cue.getPrincipalCurrency().round( cue.getPrincipal() * ( isPrincipalCcyBuy ? -1 : 1 ) ) : 0.0 ) ).append ( ',' );
            }
            buf.append ( cue.getFormattedPrincipalBalanceAmount ( null, false ) ).append ( ',' );
            buf.append ( cue.getTicket () != null ? cue.getTicket () : "" ).append ( ',' );
            buf.append ( cue.getNotes () != null ? cue.getNotes () : "" );

            if ( !isDepositWithdrawEvent )
            {
                buf.append ( "\n" );
                Currency priceCcy = cue.getPriceCurrency ();
                buf.append ( cue.getTradeDate ().getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) ).append ( ',' );
                buf.append ( priceCcy.getShortName () ).append ( ',' );
                buf.append ( AccountEnums.AccountEventType.getEventTypeDescription( cue.getEventType () ) ).append ( ',' );
                buf.append ( cue.getOrderId () != null ? cue.getOrderId () : "" ).append ( ',' );
                buf.append ( cue.getTransactionId () ).append ( ',' );
                buf.append ( getFormattedCreatedTime ( cue.getCreatedTimestamp (), DATE_TIME_FORMAT ) ).append ( ',' );
                buf.append ( priceCcy.getDecimalFormat ( AMOUNT_FORMAT_STRING ).format ( cue.getPrice() != 0.0 ?
                        cue.getPriceCurrency().round( cue.getPrice() * ( isPrincipalCcyBuy ? 1 : -1 ) ) : 0.0 ) ).append ( ',' );
                buf.append ( cue.getFormattedPriceBalanceAmount ( null, false ) ).append ( ',' );
                buf.append ( cue.getTicket () != null ? cue.getTicket () : "" ).append ( ',' );
                buf.append ( cue.getNotes () != null ? cue.getNotes () : "" );
            }
        }
        return buf.toString ();
    }

    private String getFormattedCreatedTime ( Timestamp timestamp, String format )
    {
        String dateTimeStr;
        try
        {
            DateFormat df = new SimpleDateFormat ( format );
            dateTimeStr = df.format( timestamp );
        }
        catch ( Exception e )
        {
            dateTimeStr = timestamp.toString();
        }
        return dateTimeStr;
    }

    protected Tuple<IdcDate, IdcDate> getFromToDate( String startDateStr, String endDateStr, User user, boolean useTradeDate )
    {
        IdcDate fromDate;
        IdcDate toDate = null;
        int daysToBeSubForFromDate;
        TimeZone tz = TimeZone.getTimeZone( "GMT" );
        IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ();
        IdcDate systemDate = DateTimeFactory.newDate ( tz );
        Calendar calFrom = Calendar.getInstance();
        calFrom.setTime( new Date() );
        calFrom.setTimeZone( tz );
        calFrom.set( Calendar.HOUR_OF_DAY, 0 );
        calFrom.set( Calendar.MINUTE, 0 );
        calFrom.set( Calendar.SECOND, 1 );
        boolean isTodayEndDateStr = TODAY.equalsIgnoreCase ( endDateStr );
        if( MTD.equalsIgnoreCase ( startDateStr ) )
        {
            IdcDate from = DateTimeFactory.newDate( calFrom.getTime() );
            daysToBeSubForFromDate = from.getDayOfMonth() - 1;
            fromDate = from.subtractDays ( daysToBeSubForFromDate );
            toDate = useTradeDate ? tradeDate : systemDate;
        }
        else if( WTD.equalsIgnoreCase ( startDateStr ) )
        {
            IdcDate from = DateTimeFactory.newDate( calFrom.getTime() );
            daysToBeSubForFromDate = from.getDayOfWeek();
            fromDate = from.subtractDays ( daysToBeSubForFromDate );
            toDate = useTradeDate ? tradeDate : systemDate;
        }
        else if ( YTD.equalsIgnoreCase( startDateStr ) )
        {
            Calendar cal = Calendar.getInstance();
            cal.set( Calendar.MONTH, 0 ); // 1 Jan
            cal.set( Calendar.DATE, 1 );
            cal.setTimeZone( tz );
            cal.set( Calendar.HOUR_OF_DAY, 0 );
            cal.set( Calendar.MINUTE, 0 );
            cal.set( Calendar.SECOND, 1 );
            fromDate = DateTimeFactory.newDate( cal.getTime() );
            toDate = useTradeDate ? tradeDate : systemDate;
        }
        else if ( TODAY.equalsIgnoreCase ( startDateStr ))
        {
            fromDate =  useTradeDate ? tradeDate : systemDate;
            toDate = fromDate;
        }
        else if ( YESTERDAY.equalsIgnoreCase ( startDateStr ) )
        {
            fromDate = useTradeDate ? tradeDate.subtractDays ( 1 ) : systemDate.subtractDays ( 1 );
            toDate = fromDate;
        }
        else
        {
            fromDate =  getDate ( startDateStr, user );
            if ( !isTodayEndDateStr )
            {
                toDate = getDate ( endDateStr, user );
            }
        }

        if ( isTodayEndDateStr )
        {
            toDate = useTradeDate ? tradeDate : systemDate;
        }

        return new Tuple<IdcDate, IdcDate> ( fromDate, toDate );
    }

    private IdcDate getDate( String dateStr, User user )
    {
        try
        {
            return DateTimeFactory.newDate( dateStr, user.getDisplayPreference ().getDateFormat () );
        }
        catch ( ParseException e )
        {
            log.info ( "ATHRF.getDate : invalid date string. dateStr=" + dateStr + ",user=" + user );
        }
        return null;
    }
}
