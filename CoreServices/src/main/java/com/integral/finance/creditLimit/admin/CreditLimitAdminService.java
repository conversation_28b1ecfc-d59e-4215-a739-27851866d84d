package com.integral.finance.creditLimit.admin;

// Copyright (c) 2005 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.model.PfeConfiguration;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.marketData.MarketDataSet;
import com.integral.finance.trade.Tenor;
import com.integral.message.WorkflowMessage;
import com.integral.time.IdcDate;
import com.integral.user.Organization;

import java.util.Collection;

/**
 * CreditLimitAdminService is the interface for credit data creation and management for the credit provider organization.
 *
 * <AUTHOR> Development Corp.
 */
public interface CreditLimitAdminService
{
    /**
     * Enables/disables the credit for the credit provider organization.
     *
     * @param creditProviderOrg credit provider organization
     * @param enable            enable
     */
    void setCreditEnabled( Organization creditProviderOrg, boolean enable );

    /**
     * Enables/disables the credit for the credit counterparty from the credit provider.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param enable            enable
     */
    void setCreditEnabled( Organization creditProviderOrg, TradingParty creditCpty, boolean enable );

    /**
     * Returns the netting methodology used for the credit utilization calculation for the specified credit limit type.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param existingClsf      existing classification
     * @return netting methodology
     */
    CreditUtilizationCalculator getNettingMethodology( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification existingClsf );

    /**
     * Sets the netting methodology for the credit utilization calculations for credit provider and credit counterparty. If an existing
     * classification is specified, then an existing credit rule of that classification if found, will be set with a new credit
     * utilization calculator. If a null is specified for existing classification, then, all the credit limit rules will
     * be modified with the credit utilization calculator specified.
     *
     * @param creditProvider credit provider organization
     * @param creditCpty     credit counterparty
     * @param existingClsf   existing classification
     * @param calc           calc
     */
    void setNettingMethodology( Organization creditProvider, TradingParty creditCpty, CreditLimitClassification existingClsf, CreditUtilizationCalculator calc );

    /**
     * Returns the default aggregate netting methodology used for the credit provider organization.
     *
     * @param creditProviderOrg credit provider organization
     * @return default netting methodology
     */
    CreditUtilizationCalculator getDefaultAggregateNettingMethodology( Organization creditProviderOrg );

    /**
     * Returns the default daily netting methodology used for the credit provider organization.
     *
     * @param creditProviderOrg credit provider organization
     * @return default netting methodology
     */
    CreditUtilizationCalculator getDefaultDailyNettingMethodology( Organization creditProviderOrg );

    /**
     * Sets the default aggregate netting methodology used for the credit provider organization.  If set, it will be applicable for
     * single credit limit rule type.  When it is set, it will be applicable for the credit relationships established afterwards.
     *
     * @param creditProviderOrg credit provider organization
     * @param nettingCalc       netting calculator
     */
    void setDefaultAggregateNettingMethodology( Organization creditProviderOrg, CreditUtilizationCalculator nettingCalc );

    /**
     * Sets the default daily netting methodology used for the credit provider organization.  If set, it will be applicable for
     * daily credit limit rule type.  When it is set, it will be applicable for the credit relationships established afterwards.
     *
     * @param creditProviderOrg credit provider organization
     * @param nettingCalc       netting calculator
     */
    void setDefaultDailyNettingMethodology( Organization creditProviderOrg, CreditUtilizationCalculator nettingCalc );

    /**
     * Returns the default credit limit currency for the credit provider organization. This currency will be used for the new
     * credit relationships.
     *
     * @param creditProviderOrg credit provider organization
     * @return currency
     */
    Currency getDefaultLimitCurrency( Organization creditProviderOrg );

    /**
     * Sets the default credit limit currency for the credit provider organization. This currency will be used for new
     * credit relationship.
     *
     * @param creditProviderOrg credit provider organization
     * @param ccy               currency
     */
    void setDefaultLimitCurrency( Organization creditProviderOrg, Currency ccy );

    /**
     * Sets the percentage of utilization at which the notification email message will be sent out. This information is
     * at the credit provider level.
     *
     * @param creditProviderOrg credit provider organization
     * @param percentage        percentage
     */
    void setNotificationPercentage( Organization creditProviderOrg, double percentage );

    /**
     * Sets the percentage of utilization at which the notification email message will be sent out. This information is
     * at the credit counterparty level.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param percentage        percentage
     */
    void setNotificationPercentage( Organization creditProviderOrg, TradingParty creditCpty, double percentage );

    /**
     * Sets the percentage of utilization at which the warning email notification will be sent out. This information is
     * at the credit provider level.
     *
     * @param creditProviderOrg credit provider organization
     * @param percentage        percentage
     */
    void setWarningPercentage( Organization creditProviderOrg, double percentage );

    /**
     * Sets the percentage of utilization at which the warning email notification will be sent out. This information is
     * at the credit counterparty level.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param percentage        percentage
     */
    void setWarningPercentage( Organization creditProviderOrg, TradingParty creditCpty, double percentage );

    /**
     * Sets the percentage of utilization at which the trading suspension email notification will be sent out. This information is at
     * the credit provider level.
     *
     * @param creditProviderOrg credit provider organization
     * @param percentage        percentage
     */
    void setSuspensionPercentage( Organization creditProviderOrg, double percentage );

    /**
     * Sets the percentage of utilization at which the trading suspension email notification will be sent out. This information is at
     * the credit counterparty level.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param percentage        percentage
     */
    void setSuspensionPercentage( Organization creditProviderOrg, TradingParty creditCpty, double percentage );

    /**
     * Sets the percentage of utilization at which the closing of positions will be initiated. This information is at
     * the credit provider level.
     *
     * @param creditProviderOrg credit provider organization
     * @param percentage        percentage
     */
    void setStopOutPercentage( Organization creditProviderOrg, Double percentage );

    /**
     * Sets the percentage of utilization which the closing of positions will be initiated. This information is at
     * the credit counterparty level.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param percentage        percentage
     */
    void setStopOutPercentage( Organization creditProviderOrg, TradingParty creditCpty, Double percentage );

    /**
     * Sets the list of email addresses at the credit provider organization level. Credit utilization percentage notification
     * email will be sent to these addresses.
     *
     * @param creditProviderOrg credit provider organization
     * @param email             email
     */
    void setNotificationEmailAddress( Organization creditProviderOrg, String email );

    /**
     * Sets the list of email addresses where the credit counterparty utilization percentage notifications will be sent to.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param emailAddress      email address
     */
    void setNotificationEmailAddress( Organization creditProviderOrg, TradingParty creditCpty, String emailAddress );

    /**
     * Returns the credit limit amount for the credit relationship between credit provider organization and credit cpty
     * for a specified credit type.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param clsf              classification
     * @return credit limit amount
     */
    double getCreditLimit( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf );

    /**
     * Returns the credit limit amount for the credit relationship between credit provider organization and credit cpty
     * for a specified credit type and date.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param clsf              classification
     * @param date              date
     * @return credit limit amount
     */
    double getCreditLimit( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, IdcDate date );

    /**
     * Returns the currency in which credit limit amount is specified.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param clsf              classification
     * @return limit currency
     */
    Currency getCreditLimitCurrency( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf );

    /**
     * Sets the credit limit amount for the credit relationship formed by credit provider organization and credit counterparty.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param clsf              classification
     * @param limit             limit
     * @param ccy               currency
     */
    void setCreditLimit( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, double limit, Currency ccy );

    /**
     * Sets the credit limit amount for the credit relationship formed by credit provider organization and credit counterparty. If the force update limit is true,
     * in the integration mode, the tracking of used amount will be reset.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param clsf              classification
     * @param limit             limit
     * @param ccy               currency
     * @param forceUpdateLimit  update the limit even if it is same as the existing value.
     */
    void setCreditLimit( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, double limit, Currency ccy, boolean forceUpdateLimit );

    /**
     * Sets the credit limit amount for the specified date. This limit is for the credit relationship formed by credit provider organization
     * and credit counterparty. This limit on this date will override the regular limit specified for all dates.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param clsf              classification
     * @param limit             limit
     * @param ccy               currency
     * @param date              date
     */
    void setCreditLimit( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, Double limit, Currency ccy, IdcDate date );

    /**
     * Sets the credit limit amount for the specified date. This limit is for the credit relationship formed by credit provider organization
     * and credit counterparty. This limit on this date will override the regular limit specified for all dates. If the force update limit is true,
     * in the integration mode, the tracking of used amount will be reset.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param clsf              classification
     * @param limit             limit
     * @param ccy               currency
     * @param date              date
     * @param forceUpdateLimit  update the limit even if it is same as the existing value.
     */
    void setCreditLimit( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, Double limit, Currency ccy, IdcDate date, boolean forceUpdateLimit );

    /**
     * Sets the default credit exposure at the credit provider level. This will be used when new credit relationships are set up.
     *
     * @param creditProviderOrg credit provider organization
     * @param isOrgLevel        org level
     */
    void setDefaultExposureLevel( Organization creditProviderOrg, boolean isOrgLevel );

    /**
     * Sets the credit exposure at the organization level for the credit counterparty organization. If credit exposure is
     * at legal entity level when this method is called, then the credit data will be modified to make it at the organization
     * level.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCptyOrg     credit counterparty organization
     */
    void setOrganizationExposureLevel( Organization creditProviderOrg, Organization creditCptyOrg );

    /**
     * Sets the credit exposure at the legal entity level for the credit counterparty organization. If credit exposure is
     * at organization level when this method is called, then existing credit data will be modified accordingly.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCptyOrg     credit counterparty organization
     */
    void setLegalEntityExposureLevel( Organization creditProviderOrg, Organization creditCptyOrg );

    /**
     * Establishes the credit relationship with the credit counterparty by adding credit limit rules.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     */
    void establishCreditRelationship( Organization creditProviderOrg, TradingParty creditCpty );

    /**
     * Establishes the credit relationship with the credit counterparty by adding credit limit rules.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param isAuditEnabled    is audit enabled
     */
    void establishCreditRelationship( Organization creditProviderOrg, TradingParty creditCpty, boolean isAuditEnabled );

    /**
     * Removes the credit relationship with the credit counterparty or counterparty organization by inactivating the credit
     * limit rules.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     */
    void removeCreditRelationship( Organization creditProviderOrg, TradingParty creditCpty );

    /**
     * Sets up the organization as a credit provider organization by creating the credit limit rule set and credit limit
     * org function. Used in organization creation process.
     *
     * @param creditProviderOrg credit provider organization
     * @param mds               market data set
     */
    void setupCreditProvider( Organization creditProviderOrg, MarketDataSet mds );

    /**
     * Reinitializes the credit data structure for the credit provider org based on the credit limit org function
     * configuration. This means the existing utilization events and utilizations will be discarded.
     *
     * @param creditProviderOrg credit provider organization
     */
    void reinitialize( Organization creditProviderOrg );

    /**
     * Reinitializes the credit data structures for the credit provider organization and credit counterparty organization
     * combination by removing existing data and installing new data. Existing credit utilization events and credit utilizations
     * will be removed.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCptyOrg     credit counterparty organization
     */
    void reinitializeCreditCounterpartyOrganization( Organization creditProviderOrg, Organization creditCptyOrg );

    /**
     * Sets the number of days for which daily credit utilizations will be considered the credit exposure.
     *
     * @param creditProviderOrg credit provider organization
     * @param days              days
     */
    void setDailyExposureHorizon( Organization creditProviderOrg, int days );

    /**
     * Sets the enabling of email sending when a trade is rejected due to credit failure.
     *
     * @param creditProviderOrg credit provider organization
     * @param enable            enable
     */
    void setEnableRejectionEmail( Organization creditProviderOrg, boolean enable );

    /**
     * Sets the sender email address for the emails generated from the credit provider organization.
     *
     * @param creditProviderOrg credit provider organization
     * @param email             email
     */
    void setSenderEmailAddress( Organization creditProviderOrg, String email );

    /**
     * Processes the workflow message based on the topic and event and parameters. Parameter names should be the ones which
     * are defined in {@link com.integral.finance.creditLimit.CreditLimit}. Workflow message event names are defined in
     * {@link com.integral.finance.creditLimit.CreditMessageEvent}.
     *
     * @param wm workflow message
     * @return workflow message
     */
    WorkflowMessage process( WorkflowMessage wm );

    /**
     * Returns the organization level default margin leverage factor for the credit provider organization.
     *
     * @param creditProviderOrg credit provider organization
     * @return leverage factor
     */
    Double getDefaultLeverageFactor( Organization creditProviderOrg );

    /**
     * Sets the organization level default margin leverage factor for the credit provider organization.
     *
     * @param creditProviderOrg credit provider organization
     * @param leverageFactor    leverage factor
     */
    void setDefaultLeverageFactor( Organization creditProviderOrg, Double leverageFactor );

    /**
     * Returns the organization level default flag for including profit and loss in the calculation of available limit.
     *
     * @param creditProviderOrg credit provider organization
     * @return include profit and loss
     */
    Boolean isDefaultApplyPandL( Organization creditProviderOrg );

    /**
     * Sets the organization level default flag for including the profit and loss in the calculation of available limit.
     *
     * @param creditProviderOrg credit provider org
     * @param applyPandL        apply profit and loss
     */
    void setDefaultApplyPandL( Organization creditProviderOrg, Boolean applyPandL );

    /**
     * Returns the margin leverage factor for the credit counterparty.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit counterparty
     * @param clsf              classification
     * @return margin leverage factor
     */
    Double getLeverageFactor( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf );

    /**
     * Sets the margin leverage factor for the credit counterparty.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit counterparty
     * @param clsf              classification
     * @param leverageFactor    leverage factor
     */
    void setLeverageFactor( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, Double leverageFactor );

    /**
     * Returns whether the profit and loss should be included in the calculation of available limits for the counterparty organization.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit cpty
     * @param clsf              classification
     * @return leverage factor
     */
    Boolean isApplyPandL( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf );

    /**
     * Sets whether the profit and loss should be included in the calculation of available limits for the counterparty organization.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param clsf              classification
     * @param applyPandL        apply profit and loss
     */
    void setApplyPandL( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, Boolean applyPandL );

    /**
     * Returns the organization level default flag for whether the current date positions should be ignored in the calculation of available limit.
     *
     * @param creditProviderOrg credit provider organization
     * @return boolean representing whether to ignore current date positions
     */
    Boolean isDefaultIgnoreCurrDatePositions( Organization creditProviderOrg );

    /**
     * Sets the organization level default flag for whether the current date positions should be ignored in the calculation of available limit.
     *
     * @param creditProviderOrg       credit provider org
     * @param ignoreCurrDatePositions ignore Current Date Positions
     */
    void setDefaultIgnoreCurrDatePositions( Organization creditProviderOrg, Boolean ignoreCurrDatePositions );

    /**
     * Returns whether the current date positions should be ignored in the calculation of available limits for the counterparty organization.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit cpty
     * @param clsf              classification
     * @return boolean representing whether to ignore current date positions
     */
    Boolean isIgnoreCurrDatePositions( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf );

    /**
     * Sets whether the current date positions should be ignored in the calculation of available limits for the counterparty organization.
     *
     * @param creditProviderOrg       credit provider organization
     * @param creditCpty              credit counterparty
     * @param clsf                    classification
     * @param ignoreCurrDatePositions ignore Current Date Positions
     */
    void setIgnoreCurrDatePositions( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, Boolean ignoreCurrDatePositions );

    /**
     * Returns the organization level default maximum tenor for the credit provider organization.
     *
     * @param creditProviderOrg credit provider organization
     * @return maximum tenor
     */
    Tenor getDefaultMaximumTenor( Organization creditProviderOrg );

    /**
     * Sets the maximum tenor used to restrict the trading if the value date exceeds max tenor value. This information is
     * at the credit provider level.
     *
     * @param creditProviderOrg credit provider organization
     * @param maximumTenor      maximum Tenor
     */
    void setDefaultMaximumTenor( Organization creditProviderOrg, Tenor maximumTenor );

    /**
     * Returns the maximum tenor for the credit counterparty.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit counterparty
     * @return maximum tenor
     */
    Tenor getMaximumTenor( Organization creditProviderOrg, TradingParty creditCpty );

    /**
     * Sets the maximum tenor used to restrict the trading if the value date exceeds max tenor value. This information is
     * at the credit counterparty level.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param maximumTenor      maximum Tenor
     */
    void setMaximumTenor( Organization creditProviderOrg, TradingParty creditCpty, Tenor maximumTenor );

    /**
     * Sets the maximum tenor used to restrict the trading if the value date exceeds max tenor value. This information is
     * at the credit counterparty level.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param maximumTenor      maximum Tenor
     */
    void setMaximumTenor( Organization creditProviderOrg, TradingParty creditCpty, Tenor maximumTenor , boolean forceCptyUpdate);
    /**
     * Sets the credit counterparty rules to active or inactive state if credit is configured at Organization level for the counterparty.
     *
     * @param creditProviderOrg credit provider org
     * @param counterPartyOrg   counterparty org
     * @param status            status indicating whether rule is set to active ot inactive
     */
    void setCounterpartyCreditLimitRuleActive( Organization creditProviderOrg, Organization counterPartyOrg, Character status );

    /**
     * Sets the credit counterparty rules to disable or enabled credit if credit is configured at Organization level for the counterparty.
     *
     * @param creditProviderOrg credit provider org
     * @param counterPartyOrg   counterparty org
     * @param isEnabled         Enabling or disabling the Counter party credit Rule
     */
    void setCounterpartyCreditLimitRuleEnable( Organization creditProviderOrg, Organization counterPartyOrg, boolean isEnabled );

    /**
     * Sets the credit counterparty rules to active or inactive state -- this applies to the rules which has no trading relations
     *
     * @param cclrs  collection of credit counterparty rules
     * @param status status indicating whether rule is set to active ot inactive
     */
    void setCounterpartyCreditLimitRulesActive( Collection<CounterpartyCreditLimitRule> cclrs, Character status );

    /**
     * Returns the organization level default minimum tenor for the credit provider organization.
     *
     * @param creditProviderOrg credit provider org
     * @return minimum tenor
     */
    Tenor getDefaultMinimumTenor( Organization creditProviderOrg );

    /**
     * Returns the minimum tenor for the credit counterparty.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit counterparty
     * @return minimum tenor
     */
    Tenor getMinimumTenor( Organization creditProviderOrg, TradingParty creditCpty );

    /**
     * Sets the minimum tenor used to restrict the trading if the value date is lesser than min tenor value. This information is
     * at the credit provider level.
     *
     * @param creditProviderOrg credit provider org
     * @param minimumTenor      minimum Tenor
     */
    void setDefaultMinimumTenor( Organization creditProviderOrg, Tenor minimumTenor );

    /**
     * Sets the minimum tenor used to restrict the trading if the value date is lesser than min tenor value. This information is
     * at the credit counterparty level.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit counterparty
     * @param minimumTenor      minimum Tenor
     */
    void setMinimumTenor( Organization creditProviderOrg, TradingParty creditCpty, Tenor minimumTenor );

    /**
     *
     * @param creditProviderOrg
     * @param creditCptyOrg
     * @param minimumTenor
     */
    void setMinimumTenor( Organization creditProviderOrg, Organization creditCptyOrg, Tenor minimumTenor );

    /**
     *
     * @param creditProviderOrg
     * @param creditCptyOrg
     * @param maxTenor
     */
    void setMaximumTenor( Organization creditProviderOrg, Organization creditCptyOrg, Tenor maxTenor );


    /**
     * Sets the minimum tenor used to restrict the trading if the value date is lesser than min tenor value. This information is
     * at the credit counterparty level.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit counterparty
     * @param minimumTenor      minimum Tenor
     */
    void setMinimumTenor( Organization creditProviderOrg, TradingParty creditCpty, Tenor minimumTenor, boolean updateCptyLE );
    /**
     * Returns the organization level default flag for whether the credit balance need to be updated with P&L.
     *
     * @param creditProviderOrg credit provider organization
     * @return boolean representing whether to update the credit balance with P&L
     */
    Boolean isDefaultUpdateBalanceWithPL( Organization creditProviderOrg );

    /**
     * Sets the organization level default flag determining whether the credit balance need to be update with P&L.
     *
     * @param creditProviderOrg   credit provider org
     * @param updateBalanceWithPL flag determining whether credit balance need to be updated with P&L.
     */
    void setDefaultUpdateBalanceWithPL( Organization creditProviderOrg, Boolean updateBalanceWithPL );

    /**
     * Returns the credit counterparty level default flag for whether the credit balance need to be updated with P&L.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param clsf              credit limit classification
     * @return boolean representing whether to update the credit balance with P&L
     */
    Boolean isUpdateBalanceWithPL( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf );

    /**
     * Sets the credit counterparty level default flag determining whether the credit balance need to be update with P&L.
     *
     * @param creditProviderOrg   credit provider org
     * @param creditCpty          credit cpty
     * @param clsf                classification
     * @param updateBalanceWithPL flag determining whether credit balance need to be updated with P&L.
     */
    void setUpdateBalanceWithPL( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, Boolean updateBalanceWithPL );

    /**
     * @param creditProviderOrg       credit provider org
     * @param creditCpty              credit cpty
     * @param clsf                    credit limit classification
     * @param updateAmt               update amount
     * @param settledPositionBaseDate settled position base date
     */
    void updateCreditBalanceWithProfitAndLoss( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, double updateAmt, IdcDate settledPositionBaseDate );

    /**
     * Returns the organization level default flag for whether the credit utilization and P/L is based on the trade date instead of value date.
     *
     * @param creditProviderOrg credit provider organization
     * @return boolean trade date based P/L
     */
    Boolean isDefaultDailyPL( Organization creditProviderOrg );

    /**
     * Sets the organization level default flag determining whether credit utilization and P/L is based on the trade date.
     *
     * @param creditProviderOrg credit provider org
     * @param dailyPL           flag determining whether credit balance need to be updated with P&L.
     */
    void setDefaultDailyPL( Organization creditProviderOrg, Boolean dailyPL );

    /**
     * Returns the credit counterparty level default flag for whether the credit utilization and P/L is based on the trade date.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @param clsf              credit limit classification
     * @return boolean representing whether trade date based positions
     */
    Boolean isDailyPL( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf );

    /**
     * Sets the credit counterparty level default flag determining whether the credit balance need to be update with P&L.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit cpty
     * @param clsf              classification
     * @param dailyPL           flag determining whether positions are based on the trade date.
     */
    void setDailyPL( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, Boolean dailyPL );

    /**
     * Sets the credit status for the credit provider organization.
     *
     * @param creditProviderOrg credit provider org
     * @param creditStatus      credit status
     */
    void setCreditStatus( Organization creditProviderOrg, Integer creditStatus );

    /**
     * Sets the credit status for credit counterparty.
     *
     * @param creditProviderOrg credit provider
     * @param creditCpty        credit counterparty
     * @param creditStatus      credit status (CreditLimitConstants.CREDIT_NO_CHECK, CreditLimitConstants.CREDIT_ACTIVE, CreditLimitConstants.CREDIT_SUSPEND)
     */
    void setCreditStatus( Organization creditProviderOrg, TradingParty creditCpty, Integer creditStatus );

    /**
     * Sets Currency Pair Group Exemption for Credit Provider
     *
     * @param creditProviderOrg credit provider
     * @param currencyPairGroup Currency Pair Group that is to be exempted from Credit check
     */
    void setCurrencyPairGroupExemption( Organization creditProviderOrg, CurrencyPairGroup currencyPairGroup );

    /**
     * Sets Currency Pair Group Exemption for Credit Provider --> Counterparty
     *
     * @param creditProviderOrg credit provider
     * @param creditCpty        credit counterparty
     * @param currencyPairGroup Currency Pair Group that is to be exempted from Credit check
     */
    void setCurrencyPairGroupExemption( Organization creditProviderOrg, TradingParty creditCpty, CurrencyPairGroup currencyPairGroup );

    void setCurrencyPairGroupExemption( Organization creditProviderOrg, TradingParty creditCpty, CurrencyPairGroup currencyPairGroup, boolean useDefault );

    /**
     * Sets the credit provider org level tenor profile.
     *
     * @param cpo credit provider org
     * @param ctp credit tenor profile
     */
    void setDefaultCreditTenorProfile( Organization cpo, CreditTenorProfile ctp );

    /**
     * Sets the credit provider org level tenor profile.
     *
     * @param cpo credit provider org
     * @param cc  credit counterparty
     * @param ctp credit tenor profile
     */
    void setCreditTenorProfile( Organization cpo, TradingParty cc, CreditTenorProfile ctp );

    /**
     * @deprecated
     * @param cpo credit provider org
     * @param cc credit cpty
     * @param ctp credit tenor profile
     * @param useDefault use default
     */
    void setCreditTenorProfile( Organization cpo, TradingParty cc, CreditTenorProfile ctp, boolean useDefault );

    /**
     * Sets the credit provider org level tenor profile.
     *
     * @param cpo credit provider org
     * @param cc  credit counterparty
     * @param clsf credit type
     * @param ctp credit tenor profile
     */
    void setCreditTenorProfile( Organization cpo, TradingParty cc, CreditLimitClassification clsf, CreditTenorProfile ctp );

    /**
     * Sets whether the default exempt currency pair list is used or not.
     *
     * @param cpo        credit provider org
     * @param cc         credit counterparty
     * @param useDefault use default exempt
     */
    void setUseDefaultExemptCurrencyPairGroup( Organization cpo, TradingParty cc, boolean useDefault );

    void addCreditTenorProfile( Organization cpo, PFEConfigurationProfile pfeConfigurationProfile,CreditTenorProfile ctp );

    void addCreditTenorProfile( Organization creditProviderOrg, CreditTenorProfile creditTenorProfile );

    void addCreditTenorParameters( Organization creditProviderOrg, CreditTenorProfile creditTenorProfile, Collection<CreditTenorParameters> creditTenorParameters );

    void removeCreditTenorParameters( Organization creditProviderOrg, CreditTenorProfile creditTenorProfile, Collection<CreditTenorParameters> creditTenorParameters );

    void removeCreditTenorProfile( Organization creditProviderOrg, CreditTenorProfile creditTenorProfile );

    void updateCreditTenorProfile( Organization cpo, PFEConfigurationProfile pfeConfigurationProfile,CreditTenorProfile ctp );

    void updateCreditTenorProfile( Organization creditProviderOrg, CreditTenorProfile creditTenorProfile );

    void setExternalCreditLimitProvider( Organization creditProviderOrg, TradingParty creditCpty, String externalCreditLimitProvider );
    void setExternalCreditEnabled( Organization creditProviderOrg, TradingParty creditCpty, boolean externalCreditEnabled );

    /**
     *
     * @param creditProviderOrg
     * @param creditCptyOrg
     * @param leOverride
     */
    void setLEOverride( Organization creditProviderOrg, Organization creditCptyOrg, boolean leOverride );

    /**
     *
     * @param creditProviderOrg
     * @param cco
     * @param creditTp
     * @param orgDefault
     */
    void setOrgDefault( Organization creditProviderOrg, Organization cco, TradingParty creditTp, boolean orgDefault );

    /**
     *
     * @param creditProviderOrg
     * @param creditCpty
     * @return
     */
    String getExternalCreditLimitProvider( Organization creditProviderOrg, TradingParty creditCpty );
    
    /**
    *
    * @param creditProviderOrg
    * @param creditCpty
    * @return
    */
    boolean isExternalCreditEnabled( Organization creditProviderOrg, TradingParty creditCpty );

    /**
     * Sets whether the default tenor profile is used or not.
     *
     * @param cpo        credit provider org
     * @param cc         credit counterparty
     * @param useDefault use default exempt
     */
    void setUseDefaultTenorProfile( Organization cpo, TradingParty cc, boolean useDefault );

    /**
     * Enable to use PFE Configuration instead of Credit Tenor Profile
     * for the combination for Credit Provider / Cpty Org
     * @param cpo credit provider org
     * @param usePFEConfig use pfe config
     */
    void setUsePFEConfiguration( Organization cpo,  boolean usePFEConfig );


    /**
     * Enable to use PFE Configuration instead of Credit Tenor Profile
     * for the combination for Credit Provider / Cpty Org
     * @param cpo credit provider org
     * @param cc credit cpty
     * @param usePFEConfig use pfe config
     */
    void setUsePFEConfiguration( Organization cpo, TradingParty cc, boolean usePFEConfig );

    /**
     * Enable to use PFE Configuration instead of Credit Tenor Profile
     * for the combination for Credit Provider / Cpty Org
     * @param cpo credit provider org
     * @param cc credit cpty
     * @param clsf credit type
     * @param usePFEConfig use pfe config
     */
    void setUsePFEConfiguration( Organization cpo, TradingParty cc, CreditLimitClassification clsf, boolean usePFEConfig );

    /**
     * Sets the credit provider level mode in which credit will be operating.
     *
     * @param cpo  credit provider org
     * @param mode mode
     */
    void setDefaultMode( Organization cpo, int mode );

    /**
     * Sets the mode in which the credit relationship between credit provider and credit counterparty will be operating.
     *
     * @param cpo  credit provider org
     * @param cc   credit counterparty
     * @param mode mode
     */
    void setMode( Organization cpo, TradingParty cc, Integer mode );

    void setMode( Organization cpo, TradingParty cc, Integer mode, boolean useDefaultMode );

    /**
     * Sets whether the default credit mode is used or not.
     *
     * @param cpo        credit provider org
     * @param cc         credit counterparty
     * @param useDefault use default exempt
     */
    void setUseDefaultMode( Organization cpo, TradingParty cc, boolean useDefault );

    // PFE Configuration related services
    void addPfeConfiguration( Organization creditProvider, PFEConfiguration pfeConfiguration );

    CreditTenorProfile getCreditTenorProfile( Organization creditProviderOrg,String profileName );

    PFEConfiguration getPfeConfiguration( Organization creditProviderOrg,String pfeName );

    void updatePfeConfiguration( Organization creditProvider, PFEConfiguration pfeConfiguration );

    void removePfeConfiguration( Organization creditProvider, PFEConfiguration pfeConfiguration );

    void addPfeConfigurationProfile( Organization creditProviderOrg,
                                     PFEConfiguration pfeConfiguration,
                                     Collection<PFEConfigurationProfile> pfeConfigurationProfiles );
    void removePfeConfigurationProfiles( Organization creditProviderOrg,
                                         PFEConfiguration pfeConfiguration,
                                         Collection<PFEConfigurationProfile> pfeConfigurationProfiles );

    void setPFEConfiguration ( Organization cpo, PFEConfiguration ctp );

    void setPFEConfiguration( Organization cpo, TradingParty cc, PFEConfiguration pfeConfiguration );

    void setPFEConfiguration( Organization cpo, TradingParty cc, CreditLimitClassification clsf, PFEConfiguration pfeConfiguration );

    /**
	 * @param cpo credit provider org
	 * @param pfeMode pfe mode
	 */
	void setPFEExcludeForDailyExposure(Organization cpo, int pfeMode);

    /**
     *
     * @param cpo credit provider org
     * @param cc credit cpty
     * @param pfeMode pfe mode
     */
	void setPFEExcludeForDailyExposure( Organization cpo, TradingParty cc, int pfeMode );

    /**
     *
     * @param cpo credit provider org
     * @param cc credit cpty
     * @param profileOrConfigName name
     * @param usePFE use pfe
     * @return tenor profile
     */
	CreditTenorProfileDetails getCreditTenorProfiles( Organization cpo, TradingParty cc, String profileOrConfigName, boolean usePFE);

    /**
     *
     * @param organization org
     * @param model model
     */
    void persist (Organization organization, PfeConfiguration model );

    /**
     * Returns whether tenor restrictions at the credit counterparty level if specificed in days, would be business days. If false, it will
     * be calendar days.
     *
     * @param cpo credit provider org
     * @param cc        credit counterparty
     * @return boolean representing whether trade date based positions
     */
    boolean isTenorRestrictionInBusinessDays( Organization cpo, TradingParty cc );

    /**
     * Sets the credit counterparty exposure ( org or LE ) level default flag determining whether the credit balance need to be update with P&L.
     *
     * @param cpo credit provider org
     * @param cc credit cpty
     * @param businessDays tenor restrictions in business days
     */
    void setTenorRestrictionInBusinessDays( Organization cpo, TradingParty cc, boolean businessDays );

    /**
     * Returns whether tenor restrictions at the credit counterparty Legal entity level irrespective of org/le level exposure,
     * if specificed in days, would be business days. If false, it will
     * be calendar days.
     *
     * @param cpo credit provider org
     * @param cc        credit counterparty
     * @return boolean representing whether trade date based positions
     */
    boolean isCptyLETenorRestrictionInBusinessDays( Organization cpo, TradingParty cc );

    /**
     * Sets the credit counterparty LE level default flag determining whether the credit balance need to be update with P&L.
     *
     * @param cpo credit provider org
     * @param cc credit cpty
     * @param businessDays tenor restrictions in business days
     */
    void setCptyLETenorRestrictionInBusinessDays( Organization cpo, TradingParty cc, boolean businessDays );

    CreditWorkflowMessage deposit( Organization cpo, TradingParty cc, Currency ccy, double amount, String transactionId, String notes, String ticket, String extTxId );

    CreditWorkflowMessage withdraw ( Organization cpo, TradingParty cc, Currency ccy, double amount, String transactionId, String notes, String ticket, String extTxId );

    void setUseCreditLimitRuleLevelTenorCoefficients( Organization cpo, TradingParty cc, boolean flag );

    void setGrossPositionSpreadMargin( Organization cpo, CreditTenorProfile ctp, double margin );
}