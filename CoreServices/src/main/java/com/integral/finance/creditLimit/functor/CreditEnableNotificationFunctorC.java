package com.integral.finance.creditLimit.functor;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.*;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.HashMap;

/**
 * This class is used to reset cache for the credit provider organization and credit cpty or cpty organization when credit is
 * enabled/disabled.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditEnableNotificationFunctorC extends CreditRemoteNotificationFunctorC
{

    public void onCommit( HashMap props )
    {
        // do not process the message if remote notification processing is not enabled.
        if ( !creditAdminConfig.isCreditRemoteNotificationProcessingEnabled() )
        {
            return;
        }

        Organization creditProviderOrg = null;
        Organization creditCptyOrg = null;

        log.info( "CENF.onCommit : Resetting credit utilizations cache on credit enable/disable. props=" + props );

        try
        {
            String eventName = ( String ) props.get( CreditLimitConstants.EVENT_PROPERTY );
            String creditProviderOrgGuid = ( String ) props.get( CreditLimit.CREDIT_PROVIDER_ORGANIZATION );
            if ( creditProviderOrgGuid != null )
            {
                creditProviderOrg = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditProviderOrgGuid, Organization.class );

                String creditCptyOrgGuid = ( String ) props.get( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION );
                if ( creditCptyOrgGuid != null )
                {
                    creditCptyOrg = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditCptyOrgGuid, Organization.class );
                }

                if ( log.isDebugEnabled() )
                {
                    log.debug( "CENF.onCommit : Reset cache for creditProviderOrg=" + creditProviderOrg + ",creditCptyOrg=" + creditCptyOrg );
                }
                if ( creditProviderOrg != null )
                {
                    if ( creditCptyOrg != null )
                    {
                        Collection<LegalEntity> leCol = creditCptyOrg.getLegalEntities();
                        for ( LegalEntity le : leCol )
                        {
                            TradingParty tp = le.getTradingParty( creditProviderOrg );
                            if ( tp != null )
                            {
                                CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().resetCreditEnabled( creditProviderOrg, tp );
                            }
                        }
                    }
                    else
                    {
                        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().resetCreditEnabled( creditProviderOrg, null );
                    }

                    // update the aggregate cache with the updated entries.
                    CreditUtilizationManagerC.getInstance().updateAggregateCreditUtilizationCacheEntry( creditProviderOrg, eventName );

                    boolean isEstablishCreditRelationship = CreditMessageEvent.SETCREDITRELATIONSHIP.getName().equals ( eventName );

                    if ( !isEstablishCreditRelationship )// for credit relationship setup process, another functor does below already.
                    {
                        // fetch the credit utilizations for the cache based on subscriptions.
                        new CreditUtilizationCacheFetchFromSubscriptionsC ().fetchCreditUtilizations ( creditProviderOrg, creditCptyOrg );
                    }
                }
                else
                {
                    log.warn( "CENF.onCommit : Null credit provider org. props=" + props + ",guid=" + creditProviderOrgGuid );
                }
            }
            else
            {
                log.warn( "CENF.onCommit : Null credit provider property. props=" + props );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CENF.onCommit : Error resetting the cache for org=" + creditProviderOrg, e );
        }
    }
}

