package com.integral.finance.creditLimit;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.exception.IdcOptimisticLockException;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.audit.CreditLimitAdminAuditManagerC;
import com.integral.finance.creditLimit.configuration.CreditConfigurationFactory;
import com.integral.finance.creditLimit.configuration.CreditConfigurationMBean;
import com.integral.finance.creditLimit.enums.StopOutState;
import com.integral.finance.creditLimit.handler.CreditOverrideAuditCommitHandler;
import com.integral.finance.creditLimit.handler.CreditRejectionAuditReleaseHandler;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.Request;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSwap;
import com.integral.finance.trade.Trade;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.math.MathUtil;
import com.integral.message.ErrorMessage;
import com.integral.message.MessageStatus;
import com.integral.persistence.Entity;
import com.integral.rule.SendEmailAction;
import com.integral.rule.SendEmailActionC;
import com.integral.session.IdcTransaction;
import com.integral.system.notification.SendEmailThread;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.FormatUtilC;
import com.integral.util.IdcUtilC;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * This class is used to manage notifications related to credit workflow. These notifications may be via email or jms.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditNotificationManagerC
{
    public static final int NORMAL_UTILIZATION_LEVEL = 0;
    public static final int NOTIFICATION_LEVEL = 100;
    public static final int WARNING_LEVEL = 200;
    public static final int SUSPENSION_LEVEL = 300;
    public static final int STOPOUT_LEVEL = 400;

    private static final String PARAMETER_PREFIX = "\\$";
    private static final String CREDIT_TYPE = PARAMETER_PREFIX + CreditLimit.CREDIT_LIMIT_RULE_NAME;
    private static final String THRESHOLD_TYPE = PARAMETER_PREFIX + CreditLimit.CREDIT_UTILIZATION_THRESHOLD_NAME;
    private static final String THRESHOLD_VALUE = PARAMETER_PREFIX + CreditLimit.CREDIT_UTILIZATION_THRESHOLD_VALUE;
    private static final String UTILIZATION_DATE = PARAMETER_PREFIX + CreditLimit.CREDIT_UTILIZATION_DATE;
    private static final String UTILIZATION_PERCENTAGE = PARAMETER_PREFIX + CreditLimit.CREDIT_UTILIZATION_PERCENTAGE;
    private static final String UTILIZATION_AMOUNT = PARAMETER_PREFIX + CreditLimit.CREDIT_UTILIZATION_AMOUNT;
    private static final String LIMIT_CURRENCY = PARAMETER_PREFIX + CreditLimit.CREDIT_LIMIT_CURRENCY;
    private static final String AVAILABLE_AMOUNT = PARAMETER_PREFIX + CreditLimit.CREDIT_AVAILABLE_AMOUNT;
    private static final String LIMIT_AMOUNT = PARAMETER_PREFIX + CreditLimit.CREDIT_LIMIT_AMOUNT;

    protected Log log = LogFactory.getLog ( this.getClass () );
    private static final CreditConfigurationMBean creditConfigMBean = CreditConfigurationFactory.getCreditConfigurationMBean ();
    private static final Vector<Class> readOnlyClasses = new Vector<Class> ( 20 );
    private static final Map<Integer, String> thresholdMap = new HashMap<Integer, String> ( 3 );
    private ThreadPoolExecutor notificationExecutor = null;

    private static final String MIN_TENOR_PARAM = "MinTenor";
    private static final String MAX_TENOR_PARAM = "MaxTenor";
    private static final String TRADE_DESCRIPTION_PARAM = "TradeDescription";

    private static final Map<String, Long> alertMap = new ConcurrentHashMap<String, Long> ();
    private static final String FAIL_CLOSE_OUT_POSITIONS_EVENT = "FailCloseOutPositions";
    private static final String AWAITING_CLOSE_OUT_POSITIONS_RESPONSE_EVENT = "AwaitingCloseOutPositionsResponse";

    /**
     * Singleton instance
     */
    private static CreditNotificationManagerC _creditNotificationManager;

    /*
     * Static initialization.
     */
    static
    {
        _creditNotificationManager = new CreditNotificationManagerC ();
        setReadOnlyClasses ();
        thresholdMap.put ( NOTIFICATION_LEVEL, creditConfigMBean.getCreditUtilizationThresholdName ( CreditLimit.CREDIT_NOTIFICATION_PERCENTAGE ) );
        thresholdMap.put ( WARNING_LEVEL, creditConfigMBean.getCreditUtilizationThresholdName ( CreditLimit.CREDIT_WARNING_PERCENTAGE ) );
        thresholdMap.put ( SUSPENSION_LEVEL, creditConfigMBean.getCreditUtilizationThresholdName ( CreditLimit.CREDIT_SUSPENSION_PERCENTAGE ) );
        thresholdMap.put ( STOPOUT_LEVEL, creditConfigMBean.getCreditUtilizationThresholdName ( CreditLimit.CREDIT_STOPOUT_PERCENTAGE ) );
    }

    public static Map getThresholdMap ( )
    {
        return Collections.unmodifiableMap ( thresholdMap );
    }

    /**
     * Private empty constructor.
     */
    protected CreditNotificationManagerC ( )
    {
    }

    /**
     * Returns the singleton instance of CreditNotificationManagerC.
     *
     * @return credit notification manager.
     */
    public static CreditNotificationManagerC getInstance ( )
    {
        return _creditNotificationManager;
    }

    /**
     * Use only for testing
     *
     * @param mgr notification manager
     */
    public static void _set_instance ( CreditNotificationManagerC mgr )
    {
        _creditNotificationManager = mgr;
    }

    /**
     * Returns the ThreadPoolExecutor to maintain thread pool for email sending threads
     *
     * @return Thread Pool Executor
     */
    public Executor getPooledExecutor ( )
    {
        if ( notificationExecutor == null )
        {
            notificationExecutor = initNotificationExcecutor ();
        }
        return notificationExecutor;
    }

    public void sendCreditRejectEmail ( CreditWorkflowMessage cwm )
    {
        long t0 = System.currentTimeMillis ();
        if ( log.isDebugEnabled () )
        {
            log.debug ( new StringBuilder ( 200 ).append ( "CNM.sendCreditRejectEmail : creditProviderOrg=" )
                    .append ( cwm.getOrganization () ).append ( ",creditCpty=" ).append ( cwm.getTradingParty () ).append ( ",txId=" )
                    .append ( CreditUtilC.getTransactionId ( ( Entity ) cwm.getObject () ) ).toString () );
        }
        try
        {
            Organization creditProvider = cwm.getOrganization ();
            if ( MessageStatus.SUCCESS.getName ().equals ( cwm.getEventName () ) || cwm.getErrors () == null || cwm.getErrors ().isEmpty () )
            {
                log.warn ( new StringBuilder ( 150 ).append ( "CNM.sendCreditRejectEmail : Either message status is success or errors are empty. org=" )
                        .append ( creditProvider ).append ( ",creditCpty=" ).append ( cwm.getTradingParty () ).toString () );
                return;
            }

            // filter the error codes for which notification mails should not be sent.
            if ( CreditLimit.ERROR_CREDIT_SUSPENDED.equals ( cwm.getErrorCode () ) )
            {
                return;
            }

            CounterpartyCreditLimitRule cclr = cwm.getCounterpartyCreditLimitRule ();
            if ( cclr == null )
            {
                cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( creditProvider, cwm.getTradingParty (), true );
            }
            if ( cclr == null )
            {
                log.error ( new StringBuilder ( 150 ).append ( "CNM.sendCreditRejectEmail : No active cpty rule for provider org=" )
                        .append ( creditProvider ).append ( ",creditCpty=" ).append ( cwm.getTradingParty () ).toString () );
                return;
            }

            // audit for credit reject
            boolean warmupMode = IdcUtilC.isWarmUpObject ( cwm.getObject () ) || RuntimeFactory.getServerRuntimeMBean ().isServerWarmingUp ();

            auditCreditRejection ( creditProvider, cwm.getTradingPartyOrganization (), cwm.getTradingParty (), getTradeDescription ( cwm, true, false ), warmupMode );

            Collection<String> emailAddress = CreditUtilC.getNotificationEmailAddress ( cclr );
            boolean enableRejectionEmail = CreditUtilC.getCreditLimitOrgFunction ( creditProvider ).isEnableRejectionEmail ();
            if ( enableRejectionEmail && emailAddress != null )
            {
                SendEmailAction emailAction = buildRejectNotificationEmail ( creditProvider, cwm, cclr, new ArrayList<String> ( emailAddress ) );
                long elapsed = System.currentTimeMillis () - t0;
                if ( emailAction != null )
                {
                    if ( !warmupMode )
                    {
                        CreditLimit.creditEventsLog.info ( new StringBuilder ( 200 ).append ( "CNM.sendCreditRejectEmail : sending rejection email. cclr=" )
                                .append ( cclr ).append ( ",tos=" ).append ( emailAction.getTos () ).append ( ",timeTaken=" )
                                .append ( elapsed ).toString () );
                        getPooledExecutor ().execute ( new SendEmailThread ( emailAction ) );
                    }
                    else
                    {
                        log.info ( new StringBuilder ( 150 ).append ( "CNM.sendCreditRejectEmail : Skipping email sending due to server warming up. Provider org=" )
                                .append ( creditProvider ).append ( ",creditCpty=" ).append ( cwm.getTradingParty () )
                                .append ( ",emailAddress=" ).append ( emailAddress ).append ( ",enableRejection=" )
                                .append ( true ).append ( ",timeTaken=" ).append ( elapsed ).toString () );
                    }
                }
            }
            else
            {
                long elapsed = System.currentTimeMillis () - t0;
                log.info ( new StringBuilder ( 150 ).append ( "CNM.sendCreditRejectEmail : Either email is not enabled or Email not specified for provider org=" )
                        .append ( creditProvider ).append ( ",creditCpty=" ).append ( cwm.getTradingParty () )
                        .append ( ",emailAddress=" ).append ( emailAddress ).append ( ",enableRejection=" )
                        .append ( enableRejectionEmail ).append ( ",timeTaken=" ).append ( elapsed ).toString () );
            }
        }
        catch ( Exception e )
        {
            log.error ( "CNM.sendCreditRejectEmail : Error sending the credit rejection mail.", e );
        }
    }

    public void sendCreditOverrideNotification ( CreditWorkflowMessage cwm, User overrideUser )
    {
        long t0 = System.currentTimeMillis ();
        String tid = CreditUtilC.getTransactionId ( ( Entity ) cwm.getObject () );
        if ( log.isDebugEnabled () )
        {
            log.debug ( new StringBuilder ( 200 ).append ( "CNM.sendCreditOverrideNotification : creditProviderOrg=" )
                    .append ( cwm.getOrganization () ).append ( ",creditCpty=" ).append ( cwm.getTradingParty () ).append ( ",txId=" )
                    .append ( tid ).append ( ",user=" ).append ( overrideUser != null ? overrideUser.getFullyQualifiedName () : "" ).toString () );
        }
        try
        {
            Organization creditProvider = cwm.getOrganization ();
            CounterpartyCreditLimitRule cclr = cwm.getCounterpartyCreditLimitRule ();
            if ( cclr == null )
            {
                cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( creditProvider, cwm.getTradingParty (), true );
            }
            if ( cclr == null )
            {
                log.error ( new StringBuilder ( 150 ).append ( "CNM.sendCreditOverrideNotification : No active cpty rule for provider org=" )
                        .append ( creditProvider ).append ( ",creditCpty=" ).append ( cwm.getTradingParty () ).toString () );
                return;
            }

            Collection<CreditLimitUtilization> utilInfo = new ArrayList<CreditLimitUtilization> ( 2 );
            boolean aggregateHandled = false;
            for ( CreditUtilizationEvent cue : cwm.getCreditUtilizationEvents () )
            {
                CreditUtilization cu = cue.getCreditUtilization ();
                boolean isAggregateCU = !(cu instanceof DailyCreditUtilization);
                if ( isAggregateCU )
                {
                    if ( aggregateHandled )
                    {
                        continue;
                    }
                    aggregateHandled = true;
                }
                double utilizedAmt = cu.getUsedAmount () + cu.getReservedAmount ();
                double availableAmt = cu.getAvailableReserveAmount ();
                double utilizationPercent = cu.getUtilizedPercentage ();
                utilInfo.add ( new CreditLimitUtilization ( utilizedAmt, availableAmt, utilizationPercent, 0.0, NORMAL_UTILIZATION_LEVEL, cu ) );
            }


            // audit for credit reject
            boolean warmupMode = IdcUtilC.isWarmUpObject ( cwm.getObject () ) || RuntimeFactory.getServerRuntimeMBean ().isServerWarmingUp ();

            String tradeDesc = getTradeDescription ( cwm, false, true );

            auditCreditOverride ( creditProvider, cwm.getTradingPartyOrganization (), cwm.getTradingParty (), overrideUser, tradeDesc, warmupMode );

            Collection<String> emailAddress = CreditUtilC.getNotificationEmailAddress ( cclr );
            if ( emailAddress != null )
            {
                SendEmailAction emailAction = buildCreditOverrideNotificationEmail ( cwm, creditProvider, overrideUser, tid, cclr, utilInfo, new ArrayList<String> ( emailAddress ), tradeDesc );
                long elapsed = System.currentTimeMillis () - t0;
                if ( emailAction != null )
                {
                    if ( !warmupMode )
                    {
                        CreditLimit.creditEventsLog.info ( new StringBuilder ( 200 ).append ( "CNM.sendCreditOverrideNotification : sending credit override over-utilization email. cclr=" )
                                .append ( cclr ).append ( ",tos=" ).append ( emailAction.getTos () ).append ( ",tid=" )
                                .append ( tid ).append ( ",overrideUser=" ).append ( overrideUser != null ? overrideUser.getFullyQualifiedName () : "" )
                                .append ( ",timeTaken=" ).append ( elapsed ).toString () );
                        getPooledExecutor ().execute ( new SendEmailThread ( emailAction ) );
                    }
                    else
                    {
                        log.info ( new StringBuilder ( 150 ).append ( "CNM.sendCreditOverrideNotification : Skipping email sending due to server warming up. Provider org=" )
                                .append ( creditProvider ).append ( ",creditCpty=" ).append ( cwm.getTradingParty () )
                                .append ( ",emailAddress=" ).append ( emailAddress ).append ( ",timeTaken=" ).append ( elapsed ).toString () );
                    }
                }
            }
            else
            {
                long elapsed = System.currentTimeMillis () - t0;
                log.info ( new StringBuilder ( 150 ).append ( "CNM.sendCreditOverrideNotification : Either email is not enabled or Email not specified for provider org=" )
                        .append ( creditProvider ).append ( ",creditCpty=" ).append ( cwm.getTradingParty () )
                        .append ( ",timeTaken=" ).append ( elapsed ).toString () );
            }
        }
        catch ( Exception e )
        {
            log.error ( "CNM.sendCreditOverrideNotification : Error sending the credit override mail.", e );
        }
    }

    /**
     * Sends credit utilization notification mails based on the credit utilization and configuration settings.
     *
     * @param creditUtils utilizations
     */
    public void sendUtilizationLevelNotification ( Collection<CreditUtilization> creditUtils, boolean marginReval )
    {
        try
        {
            Collection<CreditLimitUtilization> utilInfo = null;
            Collection<String> email = null;
            for ( CreditUtilization cu : creditUtils )
            {
                // for integration mode, there should not be any notifications.
                if ( cu.isIntegrationMode () )
                {
                    return;
                }
                if ( email == null )
                {
                    email = CreditUtilC.getNotificationEmailAddress ( cu );
                }
                if ( email == null && !marginReval )
                {
                    return;
                }
                Organization cpo = CreditUtilC.getCreditProviderOrganization ( cu );
                boolean retailModeEnabled = creditConfigMBean.isCreditRetailModeEnabled ( cpo.getShortName () );
                double usedAmt = retailModeEnabled && cu.getCreditLimitRule ().getCreditUtilizationCalculator () != null
                        ? cu.getCreditLimitRule ().getCreditUtilizationCalculator ().getRealtimeUtilizationAmount ( cu )
                        : cu.getUsedAmount ();
                double utilizedAmt = usedAmt + cu.getReservedAmount ();
                double availableAmt = retailModeEnabled ? cu.getRealtimeCreditUtilization ().getAvailableLimit () : cu.getAvailableReserveAmount ();
                double utilizationPercent = retailModeEnabled ? cu.getRealtimeCreditUtilization ().getUtilizedPercent () : cu.getUtilizedPercentage ();
                if ( log.isDebugEnabled () )
                {
                    log.debug ( "CNM.sendUtilizationLevelNotification : utilization percentage=" + utilizationPercent + ",cu=" + cu + ",marginReval=" + marginReval );
                }
                if ( !marginReval )
                {
                    Double suspension = CreditUtilC.getSuspensionPercentage ( cu );
                    // round the utilized percent to 2 decimal places.
                    utilizationPercent = MathUtil.round ( cu.getUtilizedPercentage (), 0.01, BigDecimal.ROUND_HALF_UP );
                    if ( suspension != null && utilizationPercent >= suspension )
                    {
                        if ( utilInfo == null )
                        {
                            utilInfo = new TreeSet<CreditLimitUtilization> ();
                        }
                        utilInfo.add ( new CreditLimitUtilization ( utilizedAmt, availableAmt, utilizationPercent, suspension, SUSPENSION_LEVEL, cu ) );
                        continue;
                    }

                    Double warning = CreditUtilC.getWarningPercentage ( cu );
                    if ( warning != null && utilizationPercent >= warning )
                    {
                        if ( utilInfo == null )
                        {
                            utilInfo = new TreeSet<CreditLimitUtilization> ();
                        }
                        utilInfo.add ( new CreditLimitUtilization ( utilizedAmt, availableAmt, utilizationPercent, warning, WARNING_LEVEL, cu ) );
                        continue;
                    }
                }

                if ( !retailModeEnabled )
                {
                    if ( !marginReval )
                    {
                        Double notification = CreditUtilC.getNotificationPercentage ( cu );
                        if ( notification != null && utilizationPercent >= notification )
                        {
                            if ( utilInfo == null )
                            {
                                utilInfo = new TreeSet<CreditLimitUtilization> ();
                            }
                            utilInfo.add ( new CreditLimitUtilization ( utilizedAmt, availableAmt, utilizationPercent, notification, NOTIFICATION_LEVEL, cu ) );
                        }
                    }
                }
                else
                {
                    if ( marginReval )
                    {
                        Double stopOutPercentage = CreditUtilC.getStopOutPercentage ( cu );
                        if ( stopOutPercentage != null && utilizationPercent >= stopOutPercentage )
                        {
                            if ( utilInfo == null )
                            {
                                utilInfo = new TreeSet<CreditLimitUtilization> ();
                            }
                            utilInfo.add ( new CreditLimitUtilization ( utilizedAmt, availableAmt, utilizationPercent, stopOutPercentage, STOPOUT_LEVEL, cu ) );
                        }
                    }
                }
            }

            if ( log.isDebugEnabled () )
            {
                log.debug ( new StringBuilder ( 200 ).append ( "CNM.sendUtilizationLevelNotification : utilization info=" )
                        .append ( utilInfo ).append ( ",creditUtils=" ).append ( creditUtils ).toString () );
            }

            // now send the notification.
            if ( utilInfo != null )
            {
                sendUtilizationNotification ( email, utilInfo, 0, marginReval );
            }
        }
        catch ( Exception e )
        {
            log.error ( "CNM.sendUtilizationLevelNotification : Exception. creditUtils=" + creditUtils, e );
        }
    }

    public void updateStopOutState ( CounterpartyCreditLimitRule cclr )
    {
        updateStopOutState ( cclr, 0 );
    }

    private void updateStopOutState ( CounterpartyCreditLimitRule cclr, int retryNum )
    {
        IdcTransaction tx = null;
        try
        {
            tx = CreditUtilC.startTransaction ( getReadOnlyClasses () );
            if ( tx != null )
            {
                tx.setNamespaceValidationEnabled ( false );
            }
            if ( retryNum > 0 )
            {
                log.info ( "CNM.updateStopOutState - refreshing the cpty rule=" + cclr );
                cclr = ( CounterpartyCreditLimitRule ) IdcUtilC.refreshObject ( cclr );
            }

            if ( cclr != null )
            {
                CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cclr.getRegisteredObject ();
                registeredCclr.setStopOutDate ( null );
                registeredCclr.setStopOutState ( StopOutState.EXECUTED );
                CreditUtilC.endTransaction ( tx );
                log.info ( "CNM.updateStopOutState - set the stop out status to executed for cpty rule=" + cclr );
            }
            else
            {
                log.info ( "CNM.updateStopOutState - cpty rule is null. retryNum=" + retryNum );
            }
        }
        catch ( IdcOptimisticLockException e )
        {
            CreditUtilC.releaseTransaction ( tx );
            updateStopOutState ( cclr, ++retryNum );
        }
        catch ( Exception e )
        {
            log.error ( "CNM.updateStopOutState : Exception while updating stop out state for cclr=" + cclr, e );
            CreditUtilC.releaseTransaction ( tx );
        }
    }

    /**
     * Builds the send email action based on the credit utilization parameters.
     *
     * @param cpo        credit provider org
     * @param cclr       counterparty credit limit rule
     * @param limitUtils cu
     * @param email      email
     * @return email action
     */
    private SendEmailAction buildUtilizationNotificationEmail ( Organization cpo, CounterpartyCreditLimitRule cclr, Collection<CreditLimitUtilization> limitUtils, Collection<String> email )
    {
        SendEmailAction emailAction = null;
        try
        {
            String subject;
            String body;

            final boolean showSettlementCode = creditConfigMBean.isShowSettlementCode ( cpo.getShortName () );
            subject = showSettlementCode ? creditConfigMBean.getCreditNotificationEmailSubjectWithSettlementCode () : creditConfigMBean.getCreditNotificationEmailSubject ();
            body = showSettlementCode ? creditConfigMBean.getCreditNotificationEmailContentWithSettlementCode () : creditConfigMBean.getCreditNotificationEmailContent ();

            if ( subject != null && body != null )
            {
                emailAction = new SendEmailActionC ();
                emailAction.setTos ( email );
                Collection<String> ccs = creditConfigMBean.getCreditNotificationCCEmailAddress ();
                if ( ccs != null )
                {
                    emailAction.setCCs ( ccs );
                }
                emailAction.setFrom ( CreditUtilC.getSenderEmailAddress ( cclr ) );
                emailAction.setSubject ( subject );

                // set parameters in the send mail action.
                emailAction.putCustomField ( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, (( CreditLimitRuleSet ) cclr.getRuleSet ()).getOrganization ().getShortName () );
                emailAction.putCustomField ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cclr.getTradingPartyOrganization ().getShortName () );
                emailAction.putCustomField ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION_LONG_NAME, cclr.getTradingPartyOrganization ().getLongName () );
                TradingParty creditCpty = cclr.getTradingParty ();
                emailAction.putCustomField ( CreditLimit.CREDIT_COUNTERPARTY, creditCpty != null ? creditCpty.getShortName () : " " );
                emailAction.putCustomField ( CreditLimit.DELIMITER, creditCpty != null ? ":" : " " );
                if ( showSettlementCode )
                {
                    String settlementCode = cclr.getSettlementCode ();
                    emailAction.putCustomField ( CreditLimit.SETTLEMENT_CODE_PARAM, settlementCode != null ? settlementCode : "" );
                }

                ArrayList<String> doNotFormatCustomFields = new ArrayList<String> ();
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_PROVIDER_ORGANIZATION );
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION );
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION_LONG_NAME );
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_COUNTERPARTY );
                emailAction.setNonDisplayFormaterKeyList ( doNotFormatCustomFields );

                StringBuilder bodySb = new StringBuilder ( 1000 ).append ( body );
                for ( CreditLimitUtilization clu : limitUtils )
                {
                    bodySb.append ( "\n\n" ).append ( buildCreditLimitRuleLevelContent ( clu ) );
                }
                body = bodySb.toString ();
                emailAction.setBody ( body );
                if ( log.isDebugEnabled () )
                {
                    log.debug ( new StringBuilder ( 300 ).append ( "CNM.CreditNotificationManagerC : email details. subject=" )
                            .append ( subject ).append ( ",body=" ).append ( body ).toString () );
                }
            }
        }
        catch ( Exception e )
        {
            log.error ( "CNM.buildUtilizationNotificationEmail : Exception. limitUtils=" + limitUtils, e );
        }
        return emailAction;
    }

    /**
     * Builds the send email action for the credit workflow rejection.
     *
     * @param cpo   credit provider org
     * @param cwm   credit workflow message
     * @param cclr  counterparty credit limit rule
     * @param email email
     * @return email action
     */
    private SendEmailAction buildRejectNotificationEmail ( Organization cpo, CreditWorkflowMessage cwm, CounterpartyCreditLimitRule cclr, Collection<String> email )
    {
        SendEmailAction emailAction = null;
        try
        {
            String subject;
            String body;

            final boolean showSettlementCode = creditConfigMBean.isShowSettlementCode ( cpo.getShortName () );
            subject = showSettlementCode ? creditConfigMBean.getCreditRejectNotificationEmailSubjectWithSettlementCode () : creditConfigMBean.getCreditRejectNotificationEmailSubject ();
            body = creditConfigMBean.getCreditRejectNotificationEmailContent ();

            if ( subject != null && body != null )
            {
                emailAction = new SendEmailActionC ();
                emailAction.setTos ( email );
                Collection<String> ccs = creditConfigMBean.getCreditNotificationCCEmailAddress ();
                if ( ccs != null )
                {
                    emailAction.setCCs ( ccs );
                }
                emailAction.setFrom ( CreditUtilC.getSenderEmailAddress ( cclr ) );
                emailAction.setSubject ( subject );
                emailAction.setBody ( body );

                // set parameters in the send mail action.
                emailAction.putCustomField ( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cwm.getOrganization ().getShortName () );
                emailAction.putCustomField ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cwm.getTradingPartyOrganization ().getShortName () );
                emailAction.putCustomField ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION_LONG_NAME, cwm.getTradingPartyOrganization ().getLongName () );
                emailAction.putCustomField ( CreditLimit.CREDIT_COUNTERPARTY, cwm.getTradingParty ().getShortName () );
                emailAction.putCustomField ( CreditLimit.TRADE_DESCRIPTION, getTradeDescription ( cwm, true, false ) );
                emailAction.putCustomField ( CreditLimit.TRADE_DATE, getTradeDate ( cwm ) );
                emailAction.putCustomField ( CreditLimit.TRADE_EXECUTION_DATE, getTradeExecutionDate ( cwm ) );
                emailAction.putCustomField ( CreditLimit.REJECT_REASON, getRejectReason ( cwm ) );
                emailAction.putCustomField ( CreditLimit.VALUE_DATES, getValueDates ( cwm ) );
                if ( showSettlementCode )
                {
                    String settlementCode = cclr.getSettlementCode ();
                    emailAction.putCustomField ( CreditLimit.SETTLEMENT_CODE_PARAM, settlementCode != null ? settlementCode : "" );
                }

                ArrayList<String> doNotFormatCustomFields = new ArrayList<String> ();
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_PROVIDER_ORGANIZATION );
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION );
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION_LONG_NAME );
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_COUNTERPARTY );
                emailAction.setNonDisplayFormaterKeyList ( doNotFormatCustomFields );

                if ( log.isDebugEnabled () )
                {
                    log.debug ( new StringBuilder ( 300 ).append ( "CNM.buildRejectNotificationEmail : email details. subject=" )
                            .append ( subject ).append ( ",body=" ).append ( body ).toString () );
                }
            }
        }
        catch ( Exception e )
        {
            log.error ( "CNM.buildRejectNotificationEmail : Exception. cclr=" + cclr, e );
        }
        return emailAction;
    }

    /**
     * Builds the send email action for the credit over utilization due to override.
     *
     * @param cpo          credit provider org
     * @param overrideUser user
     * @param cclr         counterparty credit limit rule
     * @param limitUtils   cu
     * @param email        email
     * @param tradeDesc    trade description
     * @return email action
     */
    private SendEmailAction buildCreditOverrideNotificationEmail ( CreditWorkflowMessage cwm, Organization cpo, User overrideUser, String tid, CounterpartyCreditLimitRule cclr, Collection<CreditLimitUtilization> limitUtils, Collection<String> email, String tradeDesc )
    {
        SendEmailAction emailAction = null;
        try
        {
            String subject;
            String body;

            final boolean showSettlementCode = creditConfigMBean.isShowSettlementCode ( cpo.getShortName () );
            subject = showSettlementCode ? creditConfigMBean.getCreditOverrideNotificationEmailSubjectWithSettlementCode () : creditConfigMBean.getCreditOverrideNotificationEmailSubject ();
            body = showSettlementCode ? creditConfigMBean.getCreditOverrideNotificationEmailContentWithSettlementCode () : creditConfigMBean.getCreditOverrideNotificationEmailContent ();

            if ( subject != null && body != null )
            {
                emailAction = new SendEmailActionC ();
                emailAction.setTos ( email );
                Collection<String> ccs = creditConfigMBean.getCreditNotificationCCEmailAddress ();
                if ( ccs != null )
                {
                    emailAction.setCCs ( ccs );
                }
                emailAction.setFrom ( CreditUtilC.getSenderEmailAddress ( cclr ) );
                emailAction.setSubject ( subject );

                // set parameters in the send mail action.
                emailAction.putCustomField ( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, (( CreditLimitRuleSet ) cclr.getRuleSet ()).getOrganization ().getShortName () );
                emailAction.putCustomField ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cclr.getTradingPartyOrganization ().getShortName () );
                emailAction.putCustomField ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION_LONG_NAME, cclr.getTradingPartyOrganization ().getLongName () );
                TradingParty creditCpty = cclr.getTradingParty ();
                emailAction.putCustomField ( CreditLimit.CREDIT_COUNTERPARTY, creditCpty != null ? creditCpty.getShortName () : " " );
                emailAction.putCustomField ( CreditLimit.DELIMITER, creditCpty != null ? ":" : " " );
                emailAction.putCustomField ( CreditLimit.CREDIT_OVERRIDE_USER_PARAM, overrideUser != null ? overrideUser.getShortName () : "" );
                emailAction.putCustomField ( CreditLimit.TRANSACTION_ID_PARAM, tid != null ? tid : "" );
                if ( showSettlementCode )
                {
                    String settlementCode = cclr.getSettlementCode ();
                    emailAction.putCustomField ( CreditLimit.SETTLEMENT_CODE_PARAM, settlementCode != null ? settlementCode : "" );
                }

                String minTenorString = " ";
                CreditTenorRestriction minTenor = CreditUtilizationManagerC.getInstance ().getCreditUtilizationCache ().getMinimumTenor ( cpo, cwm.getTradingParty () );
                if ( minTenor != null && minTenor.getTenor () != null )
                {
                    minTenorString = minTenor.getTenor ().getName ();
                }

                String maxTenorString = " ";
                CreditTenorRestriction maxTenor = CreditUtilizationManagerC.getInstance ().getCreditUtilizationCache ().getMaximumTenor ( cpo, cwm.getTradingParty () );
                if ( maxTenor != null && maxTenor.getTenor () != null )
                {
                    maxTenorString = maxTenor.getTenor ().getName ();
                }
                emailAction.putCustomField ( MIN_TENOR_PARAM, minTenorString );
                emailAction.putCustomField ( MAX_TENOR_PARAM, maxTenorString );
                emailAction.putCustomField ( TRADE_DESCRIPTION_PARAM, tradeDesc );

                ArrayList<String> doNotFormatCustomFields = new ArrayList<String> ();
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_PROVIDER_ORGANIZATION );
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION );
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION_LONG_NAME );
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_COUNTERPARTY );
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_OVERRIDE_USER_PARAM );
                doNotFormatCustomFields.add ( CreditLimit.TRANSACTION_ID_PARAM );
                doNotFormatCustomFields.add ( MIN_TENOR_PARAM );
                doNotFormatCustomFields.add ( MAX_TENOR_PARAM );
                doNotFormatCustomFields.add ( TRADE_DESCRIPTION_PARAM );
                emailAction.setNonDisplayFormaterKeyList ( doNotFormatCustomFields );

                StringBuilder bodySb = new StringBuilder ( 1000 ).append ( body );
                for ( CreditLimitUtilization clu : limitUtils )
                {
                    bodySb.append ( "\n\n" ).append ( buildCreditOverrideRuleLevelContent ( clu ) );
                }
                body = bodySb.toString ();
                emailAction.setBody ( body );
                if ( log.isDebugEnabled () )
                {
                    log.debug ( new StringBuilder ( 300 ).append ( "CNM.buildCreditOverrideNotificationEmail : email details. subject=" )
                            .append ( subject ).append ( ",body=" ).append ( body ).toString () );
                }
            }
        }
        catch ( Exception e )
        {
            log.error ( "CNM.buildCreditOverrideNotificationEmail : Exception. limitUtils=" + limitUtils, e );
        }
        return emailAction;
    }


    /**
     * Sends notification email and updates the notification date time in counterparty credit limit rule.
     *
     * @param email       email
     * @param limitUtils  cu
     * @param retryNum    retry number
     * @param marginReval margin revaluation
     */
    protected void sendUtilizationNotification ( Collection<String> email, Collection<CreditLimitUtilization> limitUtils, int retryNum, boolean marginReval )
    {
        IdcDate currentTradeDate = EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ();
        if ( (email == null && !marginReval) || retryNum >= CreditLimitConstants.MAXIMUM_RETRY )
        {
            return;
        }
        IdcTransaction tx = null;
        boolean sendNotification = false;
        boolean stopOutTriggered = false;
        try
        {
            CounterpartyCreditLimitRule cclr = null;
            Organization cpo = null;
            tx = CreditUtilC.startTransaction ( getReadOnlyClasses () );
            if ( tx != null )
            {
                tx.setNamespaceValidationEnabled ( false );
            }
            for ( CreditLimitUtilization clu : limitUtils )
            {
                CreditUtilization cu = clu.getCreditUtilization ();
                CreditLimitRule clr = cu.getCreditLimitRule ();
                boolean daily = clr instanceof DailyCreditLimitRule;
                cclr = ( CounterpartyCreditLimitRule ) clr.getParentRule ();
                cpo = CreditUtilC.getCreditProviderOrganization ( ( CreditLimitRuleSet ) cclr.getRuleSet () );
                if ( retryNum > 0 )
                {
                    CreditUtilC.getTransaction ().getUOW ().refreshObject ( cclr );
                }

                if ( log.isDebugEnabled () )
                {
                    log.debug ( "CNM.sendUtilizationNotification : credit utilization details=" + clu );
                }
                Date notifiedDate = getNotifiedDate ( cclr, clu.getUtilizationLevel (), daily );
                if ( marginReval )
                {
                    if ( daily || STOPOUT_LEVEL != clu.getUtilizationLevel () )
                    {
                        continue;
                    }
                }
                boolean stopOutLevel = !daily && STOPOUT_LEVEL == clu.getUtilizationLevel () && marginReval;
                String stopoutRefId = null;
                if ( log.isDebugEnabled () )
                {
                    log.debug ( new StringBuilder ( 200 ).append ( "CNM.sendUtilizationNotification :  clu=" )
                            .append ( clu ).append ( ",notifiedDate=" ).append ( notifiedDate )
                            .append ( ",stopOutState=" ).append ( cclr.getStopOutState () ).append ( ",marginReval=" )
                            .append ( marginReval ).append ( ",stopoutLevel=" ).append ( stopOutLevel )
                            .append ( ",cptyRule=" ).append ( cclr ).append ( ",cu.usedAmt=" ).append ( cu.getUsedAmount () )
                            .append ( ",cu.utilPercent=" ).append ( cu.getUtilizedPercentage () )
                            .append ( ",cu.cpc=" ).append ( cu.getCurrencyPositions ( false ) ).toString () );
                }
                if ( !stopOutLevel && notifiedDate != null && DateTimeFactory.newDate ( notifiedDate ).isLaterThanOrEqualTo ( currentTradeDate ) )
                {
                    log.info ( new StringBuilder ( 200 ).append ( "CNM.sendUtilizationNotification : Notification already sent for clu=" )
                            .append ( clu ).append ( ",notifiedDate=" ).append ( notifiedDate )
                            .append ( ",cptyRule=" ).append ( cclr ).toString () );
                    continue;
                }

                if ( stopOutLevel )
                {
                    StopOutState stopOutState = cclr.getStopOutState ();
                    if ( StopOutState.TRIGGERED.equals ( stopOutState ) )
                    {
                        log.info ( new StringBuilder ( 200 ).append ( "CNM.sendUtilizationNotification : closing out positions notifications already sent for clu=" )
                                .append ( clu ).append ( ",cu.usedAmt=" ).append ( cu.getUsedAmount () )
                                .append ( ",cu.utilPercent=" ).append ( cu.getUtilizedPercentage () )
                                .append ( ",notifiedDate=" ).append ( notifiedDate ).append ( ",stopOutState=" )
                                .append ( stopOutState ).append ( ",cptyRule=" ).append ( cclr ).toString () );

                        String alertKey = AWAITING_CLOSE_OUT_POSITIONS_RESPONSE_EVENT + "_" + cclr.getCreditLineSignature ();
                        long now = System.currentTimeMillis ();
                        Long lastAlertTime = alertMap.get ( alertKey );
                        if ( lastAlertTime == null || lastAlertTime < now - IdcDate.SECONDS_PER_HOUR * 1000 )
                        {
                            alertMap.put ( alertKey, now );
                            String alertMsg = new StringBuilder ( 200 ).append ( "Awaiting response on close out positions with broker risk management module for credit provider=" )
                                    .append ( cpo.getShortName () ).append ( ",counterparty=" ).append ( cclr.getTradingPartyOrganization ().getShortName () ).toString ();
                            log.info ( alertMsg );
                            MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CLOSE_OUT_POSITIONS_AWAITING_RESPONSE, this.getClass ().getName (), alertMsg, null );
                        }
                        continue;
                    }

                    //send the close positions notification
                    CreditWorkflowMessage cwm = CreditLimitPOJOFactory.newCreditWorkflowMessage ();
                    cwm.setCounterpartyCreditLimitRule ( cclr );
                    cwm.setEvent ( CreditMessageEvent.STOPOUTPOSITIONS );
                    CreditMarginLiquidationServiceC.getInstance ().closePositions ( cwm );
                    stopoutRefId = cwm.getMessageID ();
                    if ( MessageStatus.SUCCESS.equals ( cwm.getStatus () ) )
                    {
                        stopOutTriggered = true;
                        log.info ( "CNM.sendUtilizationNotification - initiated close out positions for cptyRule="
                                + cclr + ",status=" + cwm.getStatus () + ",clu=" + clu + ",cu.usedAmt="
                                + cu.getUsedAmount () + ",cu.utilPercent=" + cu.getUtilizedPercentage () + ",cu.cpc="
                                + cu.getCurrencyPositions ( false ) );
                    }
                    else
                    {
                        log.info ( "CNM.sendUtilizationNotification - failed initiating close out positions for cptyRule="
                                + cclr + ",status=" + cwm.getStatus () + ",clu=" + clu + ",cu.usedAmt="
                                + cu.getUsedAmount () + ",cu.utilPercent=" + cu.getUtilizedPercentage ()
                                + ",cu.cpc=" + cu.getCurrencyPositions ( false ) );
                        String alertKey = FAIL_CLOSE_OUT_POSITIONS_EVENT + "_" + cclr.getCreditLineSignature ();
                        long now = System.currentTimeMillis ();
                        Long lastAlertTime = alertMap.get ( alertKey );
                        if ( lastAlertTime == null || lastAlertTime < now - IdcDate.SECONDS_PER_HOUR * 1000 )
                        {
                            alertMap.put ( alertKey, now );
                            String alertMsg = new StringBuilder ( 200 ).append ( "Failed to close out positions with broker risk management module for credit provider=" )
                                    .append ( cpo.getShortName () ).append ( ",counterparty=" ).append ( cclr.getTradingPartyOrganization ().getShortName () ).toString ();
                            log.info ( alertMsg );
                            MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CLOSE_OUT_POSITIONS_FAIL, this.getClass ().getName (), alertMsg, null );
                        }
                    }
                }

                // set the notified time and commit.
                sendNotification = true;
                CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cclr.getRegisteredObject ();
                if ( stopOutTriggered )
                {
                    registeredCclr.setStopOutState ( StopOutState.TRIGGERED );
                    registeredCclr.setStopOutId ( stopoutRefId );
                }
                setNotifiedDate ( registeredCclr, clu.getUtilizationLevel (), currentTradeDate.asJdkDate () );

                // audit
                CreditLimitAdminAuditManagerC.getInstance ().auditUtilizationPercentageBreach ( cpo, registeredCclr.getTradingParty (),
                        registeredCclr.getTradingPartyOrganization (), clu.getUtilizedPercentage (), clu.getUtilizationLevel (), clu.getUtilizationThreshold () );
            }
            if ( sendNotification )
            {
                CreditUtilC.endTransaction ( tx );

                // send email if subject and body are present.
                if ( email != null && !email.isEmpty () )
                {
                    SendEmailAction emailAction = buildUtilizationNotificationEmail ( cpo, cclr, limitUtils, new ArrayList<String> ( email ) );
                    if ( emailAction != null )
                    {
                        CreditLimit.creditEventsLog.info ( new StringBuilder ( 200 ).append ( "CNM.sendUtilizationNotification : sending utilization email. cptyRule=" )
                                .append ( cclr ).append ( ",tos=" ).append ( emailAction.getTos () ).toString () );
                        getPooledExecutor ().execute ( new SendEmailThread ( emailAction ) );
                    }
                }
            }
            else
            {
                CreditUtilC.releaseTransaction ( tx );
            }
        }
        catch ( IdcOptimisticLockException e )
        {
            CreditUtilC.releaseTransaction ( tx );
            sendUtilizationNotification ( email, limitUtils, ++retryNum, marginReval );
        }
        catch ( Exception e )
        {
            log.error ( "CNM.sendUtilizationNotification : exception while sending credit notification email. utils=" + limitUtils, e );
            CreditUtilC.releaseTransaction ( tx );
        }
    }

    /**
     * Returns the notification date and time from the counterparty credit limit rule based on the credit utilization level.
     *
     * @param cclr             counterparty credit limit rule
     * @param utilizationLevel utilization level
     * @param daily            daily type credit
     * @return date
     */
    protected Date getNotifiedDate ( CounterpartyCreditLimitRule cclr, int utilizationLevel, boolean daily )
    {
        if ( !daily && utilizationLevel >= STOPOUT_LEVEL )
        {
            return cclr.getStopOutDate ();
        }
        else if ( utilizationLevel >= SUSPENSION_LEVEL )
        {
            return cclr.getSuspensionDate ();
        }
        else if ( utilizationLevel >= WARNING_LEVEL )
        {
            return cclr.getWarningDate ();
        }
        else if ( utilizationLevel >= NOTIFICATION_LEVEL )
        {
            return cclr.getNotificationDate ();
        }
        return null;
    }

    /**
     * Sets the current date and time as the notification date in the counterparty credit limit rule based on the
     * utilization level.
     *
     * @param cclr             counterparty credit limit rule
     * @param utilizationLevel level
     * @param date             date
     */
    protected void setNotifiedDate ( CounterpartyCreditLimitRule cclr, int utilizationLevel, Date date )
    {
        if ( utilizationLevel >= STOPOUT_LEVEL )
        {
            cclr.setStopOutDate ( date );
        }
        else if ( utilizationLevel >= SUSPENSION_LEVEL )
        {
            cclr.setSuspensionDate ( date );
        }
        else if ( utilizationLevel >= WARNING_LEVEL )
        {
            cclr.setWarningDate ( date );
        }
        else if ( utilizationLevel >= NOTIFICATION_LEVEL )
        {
            cclr.setNotificationDate ( date );
        }
    }

    /**
     * Sets the readonly classes.
     */
    private static void setReadOnlyClasses ( )
    {
        readOnlyClasses.add ( com.integral.finance.creditLimit.CreditLimitRuleSetC.class );
        readOnlyClasses.add ( com.integral.finance.creditLimit.CreditLimitClassificationC.class );
        readOnlyClasses.add ( com.integral.finance.creditLimit.CreditLimitWorkflowStateC.class );
        readOnlyClasses.add ( com.integral.user.UserC.class );
        readOnlyClasses.add ( com.integral.user.OrganizationC.class );
        readOnlyClasses.add ( com.integral.persistence.NamespaceC.class );
        readOnlyClasses.add ( com.integral.workflow.StateC.class );
        readOnlyClasses.add ( com.integral.finance.counterparty.TradingPartyC.class );
        readOnlyClasses.add ( com.integral.finance.counterparty.LegalEntityC.class );
        readOnlyClasses.add ( com.integral.finance.fx.FXPaymentParametersC.class );
        readOnlyClasses.add ( com.integral.finance.fx.FXLegC.class );
        readOnlyClasses.add ( com.integral.workflow.WorkflowStateMapC.class );
        readOnlyClasses.add ( com.integral.finance.marketData.fx.FXMarketDataSetC.class );
        readOnlyClasses.add ( com.integral.rule.RuleActionC.class );
        readOnlyClasses.add ( com.integral.rule.SendEmailActionC.class );
    }

    /**
     * Return a list of read only classes.
     *
     * @return vector of read only classes for transaction.
     */
    private static Vector getReadOnlyClasses ( )
    {
        return readOnlyClasses;
    }

    /**
     * Builds the rule level email content based on the utilization details.
     *
     * @param clu credit limit utilization
     * @return content
     * @
     */
    private String buildCreditLimitRuleLevelContent ( CreditLimitUtilization clu )
    {
        CreditUtilization cu = clu.getCreditUtilization ();
        boolean marginCall = !(cu instanceof DailyCreditUtilization) && creditConfigMBean.isCreditRetailModeEnabled ( cu.getNamespace ().getShortName () ) && clu.getUtilizationLevel () == SUSPENSION_LEVEL;
        String content = creditConfigMBean.getCreditNotificationRuleEmailContent ();
        if ( content != null )
        {
            DecimalFormat numberFormat = new DecimalFormat ( CreditLimitConstants.AMOUNT_FORMAT_STRING );
            numberFormat.setMaximumFractionDigits ( 2 );
            numberFormat.setMinimumFractionDigits ( 2 );
            CreditUtilizationCalculator calc = cu.getCreditLimitRule ().getCreditUtilizationCalculator ();
            content = content.replaceAll ( CREDIT_TYPE, calc != null ? calc.getLongName () : "" );
            content = content.replaceAll ( THRESHOLD_TYPE, marginCall ? creditConfigMBean.getCreditUtilizationThresholdName ( CreditLimit.CREDIT_MARGIN_CALL_PERCENTAGE ) : thresholdMap.get ( clu.getUtilizationLevel () ) );
            content = content.replaceAll ( THRESHOLD_VALUE, numberFormat.format ( clu.getUtilizationThreshold () ) );
            content = content.replaceAll ( UTILIZATION_DATE, getCreditUtilizationDateString ( cu ) );
            content = content.replaceAll ( UTILIZATION_PERCENTAGE, numberFormat.format ( clu.getUtilizedPercentage () ) );
            content = content.replaceAll ( UTILIZATION_AMOUNT, numberFormat.format ( cu.getCurrency ().round ( clu.getUtilizedAmount () ) ) );
            content = content.replaceAll ( LIMIT_CURRENCY, cu.getCurrency ().getShortName () );
            content = content.replaceAll ( AVAILABLE_AMOUNT, numberFormat.format ( cu.getCurrency ().round ( clu.getAvailableAmount () ) ) );
            content = content.replaceAll ( LIMIT_AMOUNT, numberFormat.format ( cu.getCurrency ().round ( cu.getLimit () ) ) );
        }
        return content;
    }

    /**
     * Builds the rule level email content based on the utilization details in case of credit override.
     *
     * @param clu credit limit utilization
     * @return content
     */
    private String buildCreditOverrideRuleLevelContent ( CreditLimitUtilization clu )
    {
        CreditUtilization cu = clu.getCreditUtilization ();
        String content = creditConfigMBean.getCreditOverrideNotificationRuleEmailContent ();
        if ( content != null )
        {
            DecimalFormat numberFormat = new DecimalFormat ( CreditLimitConstants.AMOUNT_FORMAT_STRING );
            numberFormat.setMaximumFractionDigits ( 2 );
            numberFormat.setMinimumFractionDigits ( 2 );
            CreditUtilizationCalculator calc = cu.getCreditLimitRule ().getCreditUtilizationCalculator ();
            content = content.replaceAll ( CREDIT_TYPE, calc != null ? calc.getLongName () : "" );
            content = content.replaceAll ( UTILIZATION_DATE, getCreditUtilizationDateString ( cu ) );
            content = content.replaceAll ( UTILIZATION_PERCENTAGE, numberFormat.format ( clu.getUtilizedPercentage () ) );
            content = content.replaceAll ( UTILIZATION_AMOUNT, numberFormat.format ( cu.getCurrency ().round ( clu.getUtilizedAmount () ) ) );
            content = content.replaceAll ( LIMIT_CURRENCY, cu.getCurrency ().getShortName () );
            content = content.replaceAll ( AVAILABLE_AMOUNT, numberFormat.format ( cu.getCurrency ().round ( clu.getAvailableAmount () ) ) );
            content = content.replaceAll ( LIMIT_AMOUNT, numberFormat.format ( cu.getCurrency ().round ( cu.getLimit () ) ) );
        }
        return content;
    }


    /**
     * Returns the credit utilization date formatted as dd-MMM-YYYY string.
     *
     * @param cu credit utilization
     * @return credit utilization date
     */
    private String getCreditUtilizationDateString ( CreditUtilization cu )
    {
        IdcDate cuDate = CreditUtilC.getCreditUtilizationDate ( cu );
        return cuDate != null ? cuDate.getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) : " ";
    }

    /**
     * Returns the trade execution date formatted as date and time.
     *
     * @param cwm credit workflow message
     * @return execution date time
     */
    private String getTradeExecutionDate ( CreditWorkflowMessage cwm )
    {
        Trade trade = getTrade ( cwm );
        Date executionDate = trade.getEntryDate ();
        return executionDate != null ? new SimpleDateFormat ( "dd-MMM-yyyy HH:mm:ss:SSS zzz" ).format ( executionDate ) : " ";
    }

    /**
     * Returns the trade date of the trade.
     *
     * @param cwm credit workflow message
     * @return trade date
     */
    private String getTradeDate ( CreditWorkflowMessage cwm )
    {
        Trade trade = getTrade ( cwm );
        return trade.getTradeDate () != null ? trade.getTradeDate ().getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) : " ";
    }

    /**
     * Returns the trade entity from the credit workflow message.
     *
     * @param cwm credit workflow message
     * @return trade
     */
    private Trade getTrade ( CreditWorkflowMessage cwm )
    {
        Trade trade = null;
        Entity creditEntity = ( Entity ) cwm.getObject ();
        if ( creditEntity instanceof Trade )
        {
            trade = ( Trade ) creditEntity;
        }
        else if ( creditEntity instanceof Quote )
        {
            trade = (( Quote ) creditEntity).getRequest ().getTrade ();
        }
        else if ( creditEntity instanceof Request )
        {
            trade = (( Request ) creditEntity).getTrade ();
        }
        return trade;
    }

    /**
     * Returns the trade description of the subject trade of the credit workflow.
     *
     * @param cwm                   credit workflow message
     * @param includeWorkflowErrors include workflow errors
     * @return trade description
     */
    private String getTradeDescription ( CreditWorkflowMessage cwm, boolean includeWorkflowErrors, boolean includeValueDates )
    {
        String desc = " ";
        try
        {
            desc = getTradeDescription ( cwm, getTrade ( cwm ), includeWorkflowErrors, includeValueDates );
        }
        catch ( Exception e )
        {
            log.error ( "CNM.getTradeDescription : Error generating trade description. entity=" + cwm.getObject (), e );
        }
        return desc;
    }

    /**
     * Returns the trade description for the trade.
     *
     * @param trade trade
     * @return description
     */
    private String getTradeDescription ( CreditWorkflowMessage creditWorkflowMessage, Trade trade, boolean includeWorkflowErrors, boolean includeValueDates )
    {
        StringBuilder sb = new StringBuilder ( 80 );
        String cptyA = trade.getCounterpartyA () != null ? trade.getCounterpartyA ().getShortName () : "";
        String cptyB = trade.getCounterpartyB () != null ? trade.getCounterpartyB ().getShortName () : "";
        if ( trade instanceof FXSingleLeg )
        {
            FXSingleLeg fxSingleLeg = ( FXSingleLeg ) trade;
            FXPaymentParameters fxPmt = fxSingleLeg.getFXLeg ().getFXPayment ();
            String ccyPair = fxPmt.getFXRate ().getCurrencyPair ().getName ();
            Currency dealtCcy = fxPmt.isDealtCurrency1 () ? fxPmt.getCurrency1 () : fxPmt.getCurrency2 ();
            double dealtAmt = fxPmt.isDealtCurrency1 () ? fxPmt.getCurrency1Amount () : fxPmt.getCurrency2Amount ();
            boolean isBuyDealt = dealtCcy.isSameAs ( fxPmt.getBuyingCurrency ( Trade.COUNTERPARTYA ) );
            String dealtAmtStr = dealtCcy.getDecimalFormat ( CreditLimitConstants.AMOUNT_FORMAT_STRING ).format ( dealtAmt );
            sb.append ( trade.getTransactionID () ).append ( ' ' ).append ( cptyA ).append ( isBuyDealt ? " BUY " : " SELL " )
                    .append ( dealtAmtStr ).append ( ' ' ).append ( dealtCcy ).append ( ' ' ).append ( ccyPair ).append ( ' ' ).append ( cptyB ).append ( ' ' );
            if ( includeWorkflowErrors )
            {
                sb.append ( creditWorkflowMessage.getErrorDescription () );
            }
            if ( includeValueDates )
            {
                sb.append ( ' ' ).append ( fxPmt.getValueDate ().getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) );
            }
        }
        else if ( trade instanceof FXSwap )
        {
            FXSwap fxSwap = ( FXSwap ) trade;
            FXPaymentParameters nearFXPmt = fxSwap.getNearLeg ().getFXPayment ();
            FXPaymentParameters farFXPmt = fxSwap.getFarLeg ().getFXPayment ();
            String ccyPair = nearFXPmt.getFXRate ().getCurrencyPair ().getName ();
            Currency nearDealtCcy = nearFXPmt.isDealtCurrency1 () ? nearFXPmt.getCurrency1 () : nearFXPmt.getCurrency2 ();
            boolean isNearBuyDealt = nearDealtCcy.isSameAs ( nearFXPmt.getBuyingCurrency ( Trade.COUNTERPARTYA ) );
            double nearDealtAmt = nearFXPmt.isDealtCurrency1 () ? nearFXPmt.getCurrency1Amount () : nearFXPmt.getCurrency2Amount ();
            String nearDealtAmtStr = nearDealtCcy.getDecimalFormat ( CreditLimitConstants.AMOUNT_FORMAT_STRING ).format ( nearDealtAmt );
            Currency farDealtCcy = farFXPmt.isDealtCurrency1 () ? farFXPmt.getCurrency1 () : farFXPmt.getCurrency2 ();
            boolean isFarBuyDealt = farDealtCcy.isSameAs ( farFXPmt.getBuyingCurrency ( Trade.COUNTERPARTYA ) );
            double farDealtAmt = farFXPmt.isDealtCurrency1 () ? farFXPmt.getCurrency1Amount () : farFXPmt.getCurrency2Amount ();
            String farDealtAmtStr = farDealtCcy.getDecimalFormat ( CreditLimitConstants.AMOUNT_FORMAT_STRING ).format ( farDealtAmt );
            sb.append ( trade.getTransactionID () ).append ( ' ' ).append ( cptyA ).append ( isNearBuyDealt ? " BUY/" : " SELL/" )
                    .append ( isFarBuyDealt ? "BUY " : "SELL " ).append ( nearDealtAmtStr ).append ( ' ' ).append ( nearDealtCcy )
                    .append ( '/' ).append ( farDealtAmtStr ).append ( ' ' ).append ( farDealtCcy ).append ( ' ' ).append ( ccyPair )
                    .append ( ' ' ).append ( cptyB ).append ( ' ' );
            if ( includeWorkflowErrors )
            {
                sb.append ( creditWorkflowMessage.getErrorDescription () );
            }
            if ( includeValueDates )
            {
                sb.append ( ' ' ).append ( nearFXPmt.getValueDate ().getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) )
                        .append ( '/' ).append ( farFXPmt.getValueDate ().getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) );
            }
        }
        return sb.toString ();
    }

    /**
     * Returns the value dates of the trade formatted and separated by comma.
     *
     * @param cwm credit workflow message
     * @return value dates
     */
    private String getValueDates ( CreditWorkflowMessage cwm )
    {
        String desc = " ";
        Trade trade = getTrade ( cwm );
        if ( trade instanceof FXSingleLeg )
        {
            FXSingleLeg fxSingleLeg = ( FXSingleLeg ) trade;
            desc = fxSingleLeg.getFXLeg ().getFXPayment ().getValueDate ().getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN );
        }
        if ( trade instanceof FXSwap )
        {
            FXSwap fxSwap = ( FXSwap ) trade;
            desc = new StringBuilder ( 50 ).append ( fxSwap.getNearLeg ().getFXPayment ().getValueDate ().getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) )
                    .append ( ", " ).append ( fxSwap.getFarLeg ().getFXPayment ().getValueDate ().getFormattedDate ( IdcDate.DD_MMM_YYYY_HYPHEN ) ).toString ();
        }
        return desc;
    }

    /**
     * Returns the reject reason from the properties file based on the credit workflow error code.
     *
     * @param cwm credit workflow message
     * @return reject reason
     */
    private String getRejectReason ( CreditWorkflowMessage cwm )
    {
        StringBuilder sb = new StringBuilder ( 50 );
        if ( cwm.getErrors () != null )
        {
            for ( Object o : cwm.getErrors () )
            {
                ErrorMessage errorMsg = ( ErrorMessage ) o;
                String errorCode = errorMsg.getCode ();
                if ( errorCode != null )
                {
                    String msg = creditConfigMBean.getCreditFailureMessage ( errorCode );
                    if ( msg == null )
                    {
                        msg = creditConfigMBean.getCreditFailureMessage ( CreditLimit.ERROR_SYSTEM_RUNTIME );
                    }
                    if ( msg != null )
                    {
                        if ( sb.indexOf ( msg ) == -1 )
                        {
                            if ( sb.length () > 0 )
                            {
                                sb.append ( ',' );
                            }
                            sb.append ( msg );
                        }
                    }
                }
            }
        }
        else
        {
            sb.append ( ' ' );
        }
        return sb.toString ();
    }

    private static class CreditNotificationThreadFactory implements ThreadFactory
    {
        final AtomicInteger threadNumber = new AtomicInteger ( 1 );
        ThreadGroup tg;

        public CreditNotificationThreadFactory ( String name )
        {
            tg = new ThreadGroup ( name );
        }

        public Thread newThread ( Runnable runnable )
        {
            return new Thread ( tg, runnable, "CreditNotifierThread-" + threadNumber.getAndIncrement () );
        }
    }

    /**
     * Initializes the thread pool executor for notifications if not already initialized.
     */
    private synchronized ThreadPoolExecutor initNotificationExcecutor ( )
    {
        if ( notificationExecutor == null )
        {
            ThreadPoolExecutor executor = new ThreadPoolExecutor ( creditConfigMBean.getCreditEmailPoolsize (),
                    creditConfigMBean.getCreditEmailPoolsize (), 120, TimeUnit.SECONDS, new LinkedBlockingQueue (), new CreditNotificationThreadFactory ( "CreditNotificationThreadGroup" ) );
            executor.prestartAllCoreThreads ();
            return executor;
        }
        return notificationExecutor;
    }

    protected void auditCreditRejection ( final Organization cpo, final Organization cco, final TradingParty cc, String tradeDescription, boolean warmupMode )
    {
        getPooledExecutor ().execute ( new CreditRejectionAuditReleaseHandler ( cpo, cc, cco, tradeDescription, IdcUtilC.getSessionContextUser (), warmupMode ) );
    }

    protected void auditCreditOverride ( final Organization cpo, final Organization cco, final TradingParty cc, User overrideUser, String tradeDescription, boolean warmupMode )
    {
        new CreditOverrideAuditCommitHandler ( cpo, cc, cco, tradeDescription, IdcUtilC.getSessionContextUser (), overrideUser, warmupMode ).run ();
    }

    public void sendCreditDepositWithdrawEmail ( CreditWorkflowMessage cwm )
    {
        long t0 = System.currentTimeMillis ();
        if ( log.isDebugEnabled () )
        {
            log.debug ( new StringBuilder ( 200 ).append ( "CNM.sendCreditDepositWithdrawEmail : cpo=" )
                    .append ( cwm.getOrganization () ).append ( ",cc=" ).append ( cwm.getTradingParty () ).append ( ",txId=" )
                    .append ( cwm.getTransactionId () ).toString () );
        }
        try
        {
            Organization cpo = cwm.getOrganization ();
            CounterpartyCreditLimitRule cclr = cwm.getCounterpartyCreditLimitRule ();
            if ( cclr == null )
            {
                cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( cpo, cwm.getTradingParty (), true );
            }
            if ( cclr == null )
            {
                log.error ( new StringBuilder ( 150 ).append ( "CNM.sendCreditDepositWithdrawEmail : No active cpty rule for provider org=" )
                        .append ( cpo ).append ( ",creditCpty=" ).append ( cwm.getTradingParty () ).toString () );
                return;
            }

            Collection<String> emailAddress = CreditUtilC.getNotificationEmailAddress ( cclr );
            if ( emailAddress != null )
            {
                SendEmailAction emailAction = buildDepositWithdrawNotificationEmail ( cpo, cwm, cclr, new ArrayList<String> ( emailAddress ) );
                long elapsed = System.currentTimeMillis () - t0;
                if ( emailAction != null )
                {
                    CreditLimit.creditEventsLog.info ( new StringBuilder ( 200 ).append ( "CNM.sendCreditDepositWithdrawEmail : sending deposit/withdraw notification email. cclr=" )
                            .append ( cclr ).append ( ",tos=" ).append ( emailAction.getTos () ).append ( ",timeTaken=" )
                            .append ( elapsed ).toString () );
                    getPooledExecutor ().execute ( new SendEmailThread ( emailAction ) );
                }
            }
            else
            {
                long elapsed = System.currentTimeMillis () - t0;
                log.info ( new StringBuilder ( 150 ).append ( "CNM.sendCreditDepositWithdrawEmail : Email address is not configured for provider org=" )
                        .append ( cpo ).append ( ",creditCpty=" ).append ( cwm.getTradingParty () ).append ( ",timeTaken=" ).append ( elapsed ).toString () );
            }
        }
        catch ( Exception e )
        {
            log.error ( "CNM.sendCreditDepositWithdrawEmail : Error sending the deposit/withdraw mail.", e );
        }
    }

    /**
     * Builds the send email action for the deposit/withdraw
     *
     * @param cpo   credit provider org
     * @param cwm   credit workflow message
     * @param cclr  counterparty credit limit rule
     * @param email email
     * @return email action
     */
    private SendEmailAction buildDepositWithdrawNotificationEmail ( Organization cpo, CreditWorkflowMessage cwm, CounterpartyCreditLimitRule cclr, Collection<String> email )
    {
        SendEmailAction emailAction = null;
        try
        {
            if ( cwm == null || !(CreditMessageEvent.DEPOSIT.getName ().equals ( cwm.getEventName () ) || CreditMessageEvent.WITHDRAW.getName ().equals ( cwm.getEventName () )) )
            {
                log.info ( new StringBuilder ( 150 ).append ( "CNM.buildDepositWithdrawNotificationEmail : Unable to build email notification for provider org=" )
                        .append ( cpo ).append ( ",cclr=" ).append ( cclr ).append ( ",email=" ).append ( email ).toString () );
                return null;
            }

            boolean isWithdraw = CreditMessageEvent.WITHDRAW.getName ().equals ( cwm.getEventName () );

            String subject = isWithdraw ? creditConfigMBean.getCreditWithdrawNotificationEmailSubject () : creditConfigMBean.getCreditDepositNotificationEmailSubject ();
            String body = isWithdraw ? creditConfigMBean.getCreditWithdrawNotificationEmailContent () : creditConfigMBean.getCreditDepositNotificationEmailContent ();

            if ( subject != null && body != null )
            {
                emailAction = new SendEmailActionC ();
                emailAction.setTos ( email );
                Collection<String> ccs = creditConfigMBean.getCreditNotificationCCEmailAddress ();
                if ( ccs != null )
                {
                    emailAction.setCCs ( ccs );
                }
                emailAction.setFrom ( CreditUtilC.getSenderEmailAddress ( cclr ) );
                emailAction.setSubject ( subject );
                emailAction.setBody ( body );

                // set parameters in the send mail action.
                emailAction.putCustomField ( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cwm.getOrganization ().getShortName () );
                emailAction.putCustomField ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cwm.getTradingPartyOrganization ().getShortName () );
                emailAction.putCustomField ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION_LONG_NAME, cwm.getTradingPartyOrganization ().getLongName () );
                TradingParty creditCpty = cclr.getTradingParty ();
                emailAction.putCustomField ( CreditLimit.CREDIT_COUNTERPARTY, creditCpty != null ? creditCpty.getShortName () : " " );
                emailAction.putCustomField ( CreditLimit.DELIMITER, creditCpty != null ? ":" : " " );

                String ccyStr = ( String ) cwm.getParameterValue ( CreditLimit.DEPOSIT_WITHDRAW_CURRENCY_PARAM );
                Currency ccy = CurrencyFactory.getCurrency ( ccyStr );
                Double amount = ( Double ) cwm.getParameterValue ( CreditLimit.DEPOSIT_WITHDRAW_AMOUNT_PARAM );
                BigDecimal newBalance = ( BigDecimal ) cwm.getParameterValue ( CreditLimit.DEPOSIT_WITHDRAW_BALANCE_AMOUNT_PARAM );

                DecimalFormat df = ccy.getDecimalFormat ( FormatUtilC.AMOUNT_FORMAT );
                df.setMinimumFractionDigits ( 0 );
                String newBalanceAmtStr = df.format ( newBalance );
                String amountStr = df.format ( amount );

                emailAction.putCustomField ( CreditLimit.DEPOSIT_WITHDRAW_AMOUNT_PARAM, amountStr );
                emailAction.putCustomField ( CreditLimit.DEPOSIT_WITHDRAW_CURRENCY_PARAM, ccyStr );
                emailAction.putCustomField ( CreditLimit.DEPOSIT_WITHDRAW_BALANCE_AMOUNT_PARAM, newBalanceAmtStr );
                emailAction.putCustomField ( CreditLimit.DEPOSIT_WITHDRAW_USER_PARAM, cwm.getParameterValue ( CreditLimit.DEPOSIT_WITHDRAW_USER_PARAM ) );

                ArrayList<String> doNotFormatCustomFields = new ArrayList<String> ();
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_PROVIDER_ORGANIZATION );
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION );
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION_LONG_NAME );
                doNotFormatCustomFields.add ( CreditLimit.CREDIT_COUNTERPARTY );
                doNotFormatCustomFields.add ( CreditLimit.DEPOSIT_WITHDRAW_AMOUNT_PARAM );
                doNotFormatCustomFields.add ( CreditLimit.DEPOSIT_WITHDRAW_CURRENCY_PARAM );
                doNotFormatCustomFields.add ( CreditLimit.DEPOSIT_WITHDRAW_BALANCE_AMOUNT_PARAM );
                doNotFormatCustomFields.add ( CreditLimit.DEPOSIT_WITHDRAW_USER_PARAM );
                emailAction.setNonDisplayFormaterKeyList ( doNotFormatCustomFields );

                log.info ( new StringBuilder ( 300 ).append ( "CNM.buildDepositWithdrawNotificationEmail : email details. subject=" )
                        .append ( subject ).append ( ",body=" ).append ( body ).toString () );
            }
        }
        catch ( Exception e )
        {
            log.error ( "CNM.buildDepositWithdrawNotificationEmail : Exception. cclr=" + cclr + ",cpo=" + cpo, e );
        }
        return emailAction;
    }

    public Map<String, Long> getAlertMap ( )
    {
        return alertMap;
    }
}

