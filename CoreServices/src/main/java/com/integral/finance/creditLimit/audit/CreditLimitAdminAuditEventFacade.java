package com.integral.finance.creditLimit.audit;

// Copyright (c) 2001-2006 Integral Development Corp. All rights reserved.

import com.integral.audit.AuditEventFacade;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditLimitRuleSet;
import com.integral.finance.creditLimit.CreditUtilizationCalculator;
import com.integral.finance.currency.Currency;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;

/**
 * CreditLimitAdminAuditEventFacade provides basic facade support for credit admin related audit events.
 * This facade class implementation will encapsulate the knowledge how specifid fields
 * of audit event are used. For example, if the credit limit currency information is stored in entity5 field of the class <code>AuditEventC</code>,
 * it would expose a method called <code>setLimitCurrency</code> that will be called without knowing where the limit currency is stored.
 *
 * <AUTHOR> Development Corp.
 */

public interface CreditLimitAdminAuditEventFacade extends AuditEventFacade
{
    /**
     * Facade name which is used to register the facade.
     */
    String FACADE_NAME = "CREDIT_LIMIT_ADMIN_AUDIT_EVENT_FACADE";

    /**
     * Return the credit provider organization.
     *
     * @return credit provider organization
     */
    Organization getCreditProviderOrganization();

    /**
     * Set the credit provider organization.
     *
     * @param creditProviderOrg credit provider organization
     */
    void setCreditProviderOrganization( Organization creditProviderOrg );

    /**
     * Return the credit limit ruleset to process the credit request.
     *
     * @return credit limit ruleset of the credit holder organization
     */
    CreditLimitRuleSet getCreditLimitRuleSet();

    /**
     * Set the credit limit ruleset to process the credit request.
     *
     * @param clrs credit limit ruleset of the credit holder organization
     */
    void setCreditLimitRuleSet( CreditLimitRuleSet clrs );

    /**
     * Return the credit counterparty organization for the credit provider organization.
     *
     * @return credit counterparty organization
     */
    Organization getCreditCounterpartyOrganization();

    /**
     * Set the credit counterparty organization for the credit provider organization.
     *
     * @param creditCptyOrg credit counterparty organization
     */
    void setCreditCounterpartyOrganization( Organization creditCptyOrg );

    /**
     * Return the legal entity for the credit provider organization.
     *
     * @return credit provider legal entity
     */
    LegalEntity getCreditProviderLegalEntity();

    /**
     * Set the legal entity of the credit provider organization.
     *
     * @param creditProviderLe credit provider legal entity
     */
    void setCreditProviderLegalEntity( LegalEntity creditProviderLe );

    /**
     * Return the credit counterparty for the credit provider organization.
     *
     * @return credit counterparty
     */
    TradingParty getCreditCounterparty();

    /**
     * Set the credit counterparty for the credit provider organization.
     *
     * @param creditCpty credit requester trading party
     */
    void setCreditCounterparty( TradingParty creditCpty );

    /**
     * Return the currency of the limit amounts.
     *
     * @return limit currency
     */
    Currency getLimitCurrency();

    /**
     * Set the currency of the limit amounts.
     *
     * @param ccy currency
     */
    void setLimitCurrency( Currency ccy );

    /**
     * Return the existing currency of the limit amounts.
     *
     * @return limit currency
     */
    Currency getExistingLimitCurrency();

    /**
     * Set the currency of the limit amounts.
     *
     * @param ccy currency
     */
    void setExistingLimitCurrency( Currency ccy );

    /**
     * Returns the existing netting methodology.
     *
     * @return netting methodology calc
     */
    CreditUtilizationCalculator getExistingNettingMethodology();

    /**
     * Sets the existing org level netting methodology for the credit provider organization.
     *
     * @param calc calculator
     */
    void setExistingNettingMethodology( CreditUtilizationCalculator calc );

    /**
     * Returns the new org level netting methodology for the credit provider organization.
     *
     * @return netting methodology calc
     */
    CreditUtilizationCalculator getNewNettingMethodology();

    /**
     * Sets the new org level netting methodology for the credit provider organization.
     *
     * @param calc calculator
     */
    void setNewNettingMethodology( CreditUtilizationCalculator calc );

    /**
     * Return the settlement date for the credit utilization. Settlement date will be applicable to certain types of
     * credit limit rules.
     *
     * @return settlement date
     */
    IdcDate getSettlementDate();

    /**
     * Set the settlement date of the credit utilization. Settlement date will be applicable to only certain types
     * of credit limit rules.
     *
     * @param date date
     */
    void setSettlementDate( IdcDate date );

    /**
     * Return the existing credit limit amount as defined by the credit limit rule.
     *
     * @return credit limit amount
     */
    Double getExistingLimitAmount();

    /**
     * Set the existing credit limit amount as defined by the credit limit rule.
     *
     * @param amt credit limit amount
     */
    void setExistingLimitAmount( Double amt );

    /**
     * Return the new credit limit amount as defined by the credit limit rule.
     *
     * @return credit limit amount
     */
    Double getNewLimitAmount();

    /**
     * Set the new credit limit amount as defined by the credit limit rule.
     *
     * @param amt credit limit amount
     */
    void setNewLimitAmount( Double amt );

    /**
     * Returns the existing notification percentage.
     *
     * @return notification percentage
     */
    Double getExistingNotificationPercentage();

    /**
     * Sets the existing notification percentage.
     *
     * @param percent percentage
     */
    void setExistingNotificationPercentage( Double percent );

    /**
     * Returns the new notification percentage.
     *
     * @return notification percentage
     */
    Double getNewNotificationPercentage();

    /**
     * Sets the new notification percentage.
     *
     * @param percent percentage
     */
    void setNewNotificationPercentage( Double percent );

    /**
     * Returns the existing warning percentage.
     *
     * @return warning percentage
     */
    Double getExistingWarningPercentage();

    /**
     * Sets the existing warning percentage.
     *
     * @param percent percentage
     */
    void setExistingWarningPercentage( Double percent );

    /**
     * Returns the new warning percentage.
     *
     * @return warning percentage
     */
    Double getNewWarningPercentage();

    /**
     * Sets the new warning percentage.
     *
     * @param percent percentage
     */
    void setNewWarningPercentage( Double percent );

    /**
     * Returns the existing suspension percentage.
     *
     * @return suspension percentage
     */
    Double getExistingSuspensionPercentage();

    /**
     * Sets the existing suspension percentage.
     *
     * @param percent percentage
     */
    void setExistingSuspensionPercentage( Double percent );

    /**
     * Returns the new suspension percentage.
     *
     * @return suspension percentage
     */
    Double getNewSuspensionPercentage();

    /**
     * Sets the new suspension percentage.
     *
     * @param percent percentage
     */
    void setNewSuspensionPercentage( Double percent );

    /**
     * Returns the existing stop-out percentage.
     *
     * @return suspension percentage
     */
    Double getExistingStopOutPercentage();

    /**
     * Sets the existing stop-out percentage.
     *
     * @param percent percentage
     */
    void setExistingStopOutPercentage( Double percent );

    /**
     * Returns the new suspension percentage.
     *
     * @return stop-out percentage
     */
    Double getNewStopOutPercentage();

    /**
     * Sets the new stop-out percentage.
     *
     * @param percent percentage
     */
    void setNewStopOutPercentage( Double percent );

    /**
     * Returns the percentage of utilization.
     *
     * @return percent
     */
    Double getUtilizationPercentage();

    /**
     * Sets the percentage of utilization.
     *
     * @param percent percentage
     */
    void setUtilizationPercentage( Double percent );

    /**
     * Returns the new email addresses for utilization notification.
     *
     * @return email address
     */
    String getExistingEmailAddress();

    /**
     * Sets the new email addresses for utilization notification.
     *
     * @param email email address
     */
    void setExistingEmailAddress( String email );

    /**
     * Returns the new email addresses for utilization notification.
     *
     * @return email address
     */
    String getNewEmailAddress();

    /**
     * Sets the new email addresses for utilization notification.
     *
     * @param email email address
     */
    void setNewEmailAddress( String email );

    /**
     * Returns the existing sender email addresses for utilization notification.
     *
     * @return sender email address
     */
    String getExistingSenderEmailAddress();

    /**
     * Sets the existing sender email addresses for utilization notification.
     *
     * @param email email address
     */
    void setExistingSenderEmailAddress( String email );

    /**
     * Returns the new sender email addresses for utilization notification.
     *
     * @return sender email address
     */
    String getNewSenderEmailAddress();

    /**
     * Sets the new sender email addresses for utilization notification.
     *
     * @param email email address
     */
    void setNewSenderEmailAddress( String email );

    /**
     * Returns the existing daily exposure horizon for the credit provider organization.
     *
     * @return daily exposure horizon
     */
    double getExistingDailyExposureHorizon();

    /**
     * Sets the existing daily exposure horizon for the credit provider organization.
     *
     * @param horizon horizon
     */
    void setExistingDailyExposureHorizon( double horizon );

    /**
     * Returns the new daily exposure horizon for the credit provider organization.
     *
     * @return sender email address
     */
    double getNewDailyExposureHorizon();

    /**
     * Sets the new daily exposure horizon for the credit provider organization.
     *
     * @param horizon horizon
     */
    void setNewDailyExposureHorizon( double horizon );

    /**
     * Returns the trade description for trade rejected due to credit
     *
     * @return trade description
     */
    String getTradeDescription();

    /**
     * Sets the trade description for trade rejected due to credit.
     *
     * @param tradeDescription trade description
     */
    void setTradeDescription( String tradeDescription );


    /**
     * Returns the existing credit exposure level. Exposure level can be either at org level or at legal entity level.
     *
     * @return exposure level as specified CreditLimit interface
     */
    String getExistingCreditExposureLevel();

    /**
     * Sets the existing credit exposure level. Exposure level can be either at org level or legal entity level.
     *
     * @param exposure level as specified CreditLimit interface
     */
    void setExistingCreditExposureLevel( String exposure );

    /**
     * Returns the new credit exposure level. Exposure level can be either at org level or at legal entity level.
     *
     * @return exposure level as specified CreditLimit interface
     */
    String getNewCreditExposureLevel();

    /**
     * Sets the new credit exposure level. Exposure level can be either at org level or legal entity level.
     *
     * @param exposure level as specified CreditLimit interface
     */
    void setNewCreditExposureLevel( String exposure );

    /**
     * Returns the level at which this admin action is done. It can be either at the credit provider organization level
     * or at credit counterparty/organization level.
     *
     * @return action as specified in CreditAuditEventParameters interface
     */
    String getActionLevel();

    /**
     * Sets he level at which this admin action is done. It can be either at the credit provider organization level
     * or at credit counterparty/organization level.
     *
     * @param action as specified in CreditAuditEventParameters interface
     */
    void setActionLevel( String action );

    /**
     * Returns the credit counterparty shortname. If credit is at the organization level, then credit counterparty organization
     * shortname is returned. If credit is at legal entity level, then trading party shortname is returned.
     *
     * @return counterparty short name
     */
    String getCreditCounterpartyShortName();

    /**
     * Sets the credit counterparty shortname. If credit is at the organization level, then credit counterparty organization
     * shortname is returned. If credit is at legal entity level, then trading party shortname is returned.
     *
     * @param cptyName counterparty name
     */
    void setCreditCounterpartyShortName( String cptyName );

    /**
     * Returns the existing margin leverage factor.
     *
     * @return leverage factor
     */
    Double getExistingLeverageFactor();

    /**
     * Sets the existing margin leverage factor.
     *
     * @param factor factor
     */
    void setExistingLeverageFactor( Double factor );

    /**
     * Returns the new margin leverage factor.
     *
     * @return new leverage factor
     */
    Double getNewLeverageFactor();

    /**
     * Sets the new margin leverage factor.
     *
     * @param factor factor
     */
    void setNewLeverageFactor( Double factor );

    /**
     * Returns the existing maximum tenor.
     *
     * @return maximum tenor
     */
    String getExistingMaximumTenor();

    /**
     * Sets the existing maximum tenor.
     *
     * @param maxTenor maximum tenor
     */
    void setExistingMaximumTenor( String maxTenor );

    /**
     * Returns the new maximum tenor.
     *
     * @return maximum tenor
     */
    String getNewMaximumTenor();

    /**
     * Sets the new maximum tenor.
     *
     * @param maxTenor maximum tenor
     */
    void setNewMaximumTenor( String maxTenor );

    /**
     * Returns the existing minimum tenor.
     *
     * @return minimum tenor
     */
    String getExistingMinimumTenor();

    /**
     * Sets the existing minimum tenor.
     *
     * @param minTenor minimum tenor
     */
    void setExistingMinimumTenor( String minTenor );

    /**
     * Returns the new minimum tenor.
     *
     * @return minimum tenor
     */
    String getNewMinimumTenor();

    /**
     * Sets the new minimum tenor.
     *
     * @param minTenor minimum tenor
     */
    void setNewMinimumTenor( String minTenor );

    /**
     * Returns the existing credit status.
     *
     * @return credit status
     */
    String getExistingCreditStatus();

    /**
     * Sets the existing credit status.
     *
     * @param existingCreditStatus credit status
     */
    void setExistingCreditStatus( String existingCreditStatus );

    /**
     * Returns the new credit status.
     *
     * @return credit status
     */
    String getNewCreditStatus();

    /**
     * Sets the new credit status.
     *
     * @param newCreditStatus credit status
     */
    void setNewCreditStatus( String newCreditStatus );

    /**
     * Returns the existing CurrencyPairGroup exemption.
     *
     * @return CurrencyPairGroup shortname
     */
    String getExistingCurrencyPairGroupExemption();

    /**
     * Sets the existing exempted CurrencyPairGroup shortname.
     *
     * @param currencyPairExemption
     */
    void setExistingCurrencyPairGroupExemption( String currencyPairExemption );

    /**
     * Returns the new exempted CurrencyPairGroup shortname.
     *
     * @return CurrencyPairGroup shortname
     */
    String getNewCurrencyPairExemption();

    /**
     * Sets the new exempted CurrencyPairGroup shortname.
     *
     * @param currencyPairExemption shortname
     */
    void setNewCurrencyPairGroupExemption( String currencyPairExemption );

    /**
     * Returns the existing credit tenor profile.
     *
     * @return tenor profile short name
     */
    String getExistingTenorProfile();

    /**
     * Sets the existing tenor profile name.
     *
     * @param tenorProfile tenor profile
     */
    void setExistingTenorProfile( String tenorProfile );


    /**
     * Sets the existing PFEConfiguration.
     *
     * @param pfeConfiguration PFEConfiguration
     */
    void setExistingPFEConfiguration( String pfeConfiguration );

    /**
     *
     * @return
     */
    String getNewPFEConfiguration();

    /**
     *
      * @return
     */
    String getExistingPFEConfiguration();

    /**
     * Returns the new credit tenor profile short name.
     *
     * @return credit tenor profile
     */
    String getNewTenorProfile();

    /**
     * Sets the short name of the newly set credit tenor profile.
     *
     * @param tenorProfile credit tenor profile
     */
    void setNewTenorProfile( String tenorProfile );

    /**
     * Sets the short name of the newly set PFEConfiguration.
     *
     * @param pfeConfiguration PFEConfiguration
     */
    void setNewPFEConfiguration( String pfeConfiguration );

    /**
     *
     * @param currentUsePFE
     */
    void setExistingUsePFEConfig(String currentUsePFE );

    /**
     *
     * @return
     */
    String getExistingUsePFEConfig();

    /**
     *
     * @param newUsePFE
     */
    void setNewUsePFEConfig(String newUsePFE );

    /**
     *
     * @return
     */
    String getNewUsePFEConfig();
    /**
     * Returns the existing credit mode.
     *
     * @return credit mode
     */
    String getExistingMode();

    /**
     * Sets the existing credit mode.
     *
     * @param mode credit mode
     */
    void setExistingMode( String mode );

    /**
     * Returns the new credit mode.
     *
     * @return credit mode
     */
    String getNewMode();

    /**
     * Sets the credit mode.
     *
     * @param mode credit mode
     */
    void setNewMode( String mode );

    /**
     *
     * @param pfeExcludeMode  No  ( Include Daily and Aggregate for Exposure evaluation),
     *                        Yes ( Exclude PFE from daily exposure evaluation),
     *                        Default ( use Providers settings)
     */
    void  setExistingPFEExcludeMode(String pfeExcludeMode);

    /**
     * Return the PFE Exclude mode , No , Yes ( Exclude PFE from daily exposure evaluation), Default ( use Providers settings)
     * @return existing PFE mode
     */
    String  getExistingPFEExcludeMode();

    /**
     *
     * @param pfeExcludeMode  No  ( Include Daily and Aggregate for Exposure evaluation),
     *                        Yes ( Exclude PFE from daily exposure evaluation),
     *                        Default ( use Providers settings)
     */
    void  setPFEExcludeMode(String pfeExcludeMode);

    /**
     * Return the PFE Exclude mode , No , Yes ( Exclude PFE from daily exposure evaluation), Default ( use Providers settings)
     * @return exclude PFE
     */
    String  getPFEExcludeMode();

    void setExistingLEOverride(String leOverride);

    String getExistingLEOverride();

    void setLEOverride(String leOverride);

    String getLEOverride();

    void setExistingOrgDefault(String existingOrgDefault);

    String getExistingOrgDefault();

    void setOrgDefault(String orgDefault);

    String getOrgDefault();

    String getExistingTenorInBusinessDays();

    void setExistingTenorInBusinessDays(String existingTenorInBusinessDays );

    String getTenorInBusinessDays();

    void setTenorInBusinessDays(String tenorInBusinessDays );

    Currency getDepositWithdrawCurrency();

    void setDepositWithdrawCurrency( Currency ccy );

    double getDepositWithdrawAmount( );

    void setDepositWithdrawAmount( double amount );

    User getCreditOverrideUser();

    void setCreditOverrideUser( User user );

    void setExistingUseCreditLimitRuleLevelTenorCoefficients(String currentCLRLevelTenorCoefficients );

    String getUseCreditLimitRuleLevelTenorCoefficients();

    void setNewUseCreditLimitRuleLevelTenorCoefficients(String newCLRLevelTenorCoefficients );

    String getNewUseCreditLimitRuleLevelTenorCoefficients();

    String getCreditType();

    void setCreditType( String type );

    /**
     * Return the existing gross spread margin set for the credit tenor profile.
     *
     * @return gross spread margin
     */
    Double getExistingGrossSpreadMargin();

    /**
     * Set the existing gross spread margin set for the credit tenor profile.
     *
     * @param margin gross spread margin
     */
    void setExistingGrossSpreadMargin( Double margin );

    /**
     * Return the new gross spread margin set for the credit tenor profile.
     *
     * @return gross spread margin
     */
    Double getNewGrossSpreadMargin();

    /**
     * Set the new gross spread margin set for the credit tenor profile.
     *
     * @param margin gross spread margin
     */
    void setNewGrossSpreadMargin( Double margin );
}
