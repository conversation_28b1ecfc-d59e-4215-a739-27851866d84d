package com.integral.finance.creditLimit.calculator;

// Copyright (c) 2001-2005 Integral Development Corp. All rights reserved.

import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationFactory;
import com.integral.finance.creditLimit.db.CreditDataQueryServiceFactory;
import com.integral.finance.currency.Currency;
import com.integral.finance.instrument.AmountOfInstrument;
import com.integral.finance.marketData.MarketDataSet;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.price.fx.FXPrice;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.workflow.dealing.DealingLimit;

import java.util.HashMap;
import java.util.Map;

/**
 * CreditUtilizationCalculatorProxyC implements the basic operations on
 * credit utilizations.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditUtilizationCalculatorProxyC implements CreditUtilizationCalculatorProxy
{
    final static Character FAIL_ACTION = CreditLimit.ACTION_FAIL;
    final static Character APPLY_ACTION = CreditLimit.ACTION_APPLY;
    private final static Character EARMARK_ACTION = CreditLimit.ACTION_EARMARK;
    final static Character REMOVE_ACTION = CreditLimit.ACTION_REMOVE;
    private transient CurrencyPositionCollectionBuilder positionBuilder = null;

    protected Log log = LogFactory.getLog( this.getClass() );

    private String shortName;

    CreditUtilizationCalculatorProxyC()
    {
    }

    CreditUtilizationCalculatorProxyC( String name )
    {
        this();
        setShortName ( name );
    }

    public String getShortName()
    {
        return shortName;
    }

    public void setShortName( String name )
    {
        this.shortName = name;
    }

    /**
     * Check the utilization used or reserved amount against the utilization.
     */
    public boolean checkAmount( CreditUtilization cu, CreditUtilizationEvent ce )
    {
        if ( ce.getUsedAmount() != 0.0 )
        {
            return ( cu.getAvailableUsedAmount() >= ce.getUsedAmount() );
        }
        else if ( ce.getReservedAmount() != 0.0 )
        {
            return ( cu.getAvailableMarginReserveAmount() >= ce.getReservedAmount() );
        }
        return true;
    }

    public boolean applyAmount( CreditUtilization cu, CreditUtilizationEvent cue, MarketDataSet mds, boolean excessUtilizationAllowed, Map<String, FXPrice> rateMap )
    {
        return applyAmount( cu, cue, NET_RECEIVABLE, mds, excessUtilizationAllowed, rateMap );
    }

    public boolean removeAmount( CreditUtilization cu, CreditUtilizationEvent cue, MarketDataSet mds, Map<String, FXPrice> rateMap )
    {
        return removeAmount( cu, cue, NET_RECEIVABLE, mds, rateMap);
    }

    public boolean updateAmount( CreditUtilization cu, CreditUtilizationEvent ce, double newAmount )
    {
        double deltaAmount;
        deltaAmount = newAmount - ce.getUsedAmount();
        if ( log.isDebugEnabled() )
        {
            log.debug( "Change credit amount from " + ce.getUsedAmount() + " to " + newAmount );
        }
        ce.setUsedAmount( newAmount );
        cu.updateUsedAmount( deltaAmount );
        return true;
    }

    public CurrencyPositionCollection buildCurrencyPositions( CreditUtilization cu )
    {
        return getCurrencyPositions( cu, getMarketDataSet( cu ).getCurrentBaseDate() );
    }

    public CurrencyPositionCollection buildCurrencyPositionsFromSnapshot( CreditUtilization cu )
    {
        return getCurrencyPositionCollectionBuilder().buildCurrencyPositionsFromSnapshot( cu, getMarketDataSet( cu ).getCurrentBaseDate(),
                cu.getCreditLimitRule().isAllowNetting(), isDailyAggregate (), isIgnoreLimitCurrency (), cu.ignoreCurrentDatePositions(), cu.isApplyPandL(), cu.isDailyPL() );
    }

    public CurrencyPositionCollection buildCurrencyPositionsFromNextDateSnapshot( CreditUtilization cu )
    {
        return getCurrencyPositionCollectionBuilder().buildCurrencyPositionsFromNextDateSnapshot( cu, getMarketDataSet( cu ).getCurrentBaseDate(),
                cu.getCreditLimitRule().isAllowNetting(), isDailyAggregate (), isIgnoreLimitCurrency (), cu.ignoreCurrentDatePositions(), cu.isApplyPandL(), cu.isDailyPL() );
    }

    public CurrencyPositionCollection buildCurrencyPositionsShell( CreditUtilization cu )
    {
        return getCurrencyPositionCollectionBuilder().buildCurrencyPositionsShell( cu, getMarketDataSet( cu ).getCurrentBaseDate(),
                cu.getCreditLimitRule().isAllowNetting(), isDailyAggregate (), isIgnoreLimitCurrency(), cu.ignoreCurrentDatePositions(), cu.isApplyPandL(), cu.isDailyPL() );
    }

    public CurrencyPositionCollection getCurrencyPositions( CreditUtilization cu, IdcDate baseDate )
    {
        return CreditDataQueryServiceFactory.getCreditDataQueryService().buildCurrencyPositions( cu, getCurrencyPositionQueryParameters( cu, baseDate ) );
    }

    public void recalculateUtilization( CreditUtilization cu )
    {
        cu.setUsedAmount( 0.0 );
        cu.rebuildCurrencyPositions( CreditLimitConstants.RESET_RECALCULATE_UTILIZATION, true );
        Map<String, FXPrice> rateMap = new HashMap<String, FXPrice>();
        double newUsedAmt = getUtilizationAmount( cu, rateMap );
        cu.setUsedAmount( newUsedAmt );
        CurrencyPositionCollection cpc = cu.getCurrencyPositions();
        String positions = cpc.getPositions();
        cu.setPositions( positions );
        cu.setMarketRates(cpc.getMarketRates(rateMap));
        cu.setCurrencyPositionChecksum( cpc.calculateChecksum() );
        cu.setPositionLastUpdatedTime ( System.currentTimeMillis() );
        cu.setNextDatePositions ( null );
        cu.setNextDatePositionDate ( null );
        cu.setNextDatePositionConfigChecksum ( null );

        // special handling for indicating that there are underlying positions.
        if ( newUsedAmt == 0.0 && positions != null && !positions.equals( "" ) )
        {
            cu.updateUsedAmount( -CreditLimit.MINIMUM );
        }

        if ( CreditLimit.creditCalcLog.isDebugEnabled() )
        {
            CreditLimit.creditCalcLog.debug( new StringBuilder( 200 ).append( "CUC.recalculateUtilization : new cu used amt=" )
                    .append( cu.getUsedAmount() ).append( ",cu=" ).append( cu ).append( ",this=" ).append( this )
                    .append( ",ccyPos=" ).append( cu.getCurrencyPositions() ).toString() );
        }
    }

    public double getRealtimeUtilizationAmount( CreditUtilization cu )
    {
        return getUtilizationAmount( cu );
    }

    public double getRealtimeAvailableAmount( CreditUtilization cu )
    {
        return cu.isIntegrationMode() ? cu.getAvailableReserveAmount() : cu.getAdjustedLimit() - ( getUtilizationAmount( cu ) + cu.getReservedAmount() );
    }

    public DealingLimit getDealingLimit( CreditUtilization cu, Currency baseCcy, Currency varCcy, IdcDate date, Map<String, FXPrice> rateMap, boolean isMaker )
    {
        return getDealingLimit( cu, baseCcy, varCcy, date, NET_RECEIVABLE, rateMap, isMaker );
    }

    public void updateRealtimeCreditUtilization( RealtimeCreditUtilization rcu )
    {
        updateRealtimeCreditUtilization( rcu, NET_RECEIVABLE );
    }

    public String toString()
    {
        return new StringBuilder( 80 ).append( "CreditCalc." ).append( getShortName () ).append( ",hashCode=" )
                .append( hashCode() ).toString();
    }

    /**
     * Updates the real-time credit utilization based on the calculator type.
     *
     * @param rcu      real-time credit utilization
     * @param calcType calculation type
     */
    protected void updateRealtimeCreditUtilization( RealtimeCreditUtilization rcu, int calcType )
    {
        CreditUtilization cu = rcu.getCreditUtilization();
        if ( cu != null )
        {
            CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule().getParentRule();
            double limitAmt = cu.getLimit();
            double usedAmt = 0.0;
            double utilizedAmt = 0.0;
            double profitAndLoss = 0.0;
            double netReceivable, netPayable, pandLNetReceivable, pandLNetPayable;
            CurrencyPositionCollection positions = cu.getCurrencyPositions();
            try
            {
                cclr.getLock().lock();
                double[] netAmounts = positions.getNetAmounts( cu.getCurrency(), ( FXMarketDataSet ) getMarketDataSet( cu ), new HashMap<String, FXPrice>() );
                netReceivable = netAmounts[CurrencyPositionCollection.NET_RECEIVABLE];
                netPayable = netAmounts[CurrencyPositionCollection.NET_PAYABLE];
                pandLNetReceivable = netAmounts[CurrencyPositionCollection.PANDL_NET_RECEIVABLE];
                pandLNetPayable = netAmounts[CurrencyPositionCollection.PANDL_NET_PAYABLE];
                if ( cu.isApplyPandL() )
                {
                    profitAndLoss = positions.getProfitAndLoss( pandLNetReceivable, pandLNetPayable, true );
                }
                switch ( calcType )
                {
                    case NET_RECEIVABLE:
                        usedAmt = positions.getNetReceivableAmount( netAmounts, true, false );
                        utilizedAmt = positions.getNetReceivableAmount( netAmounts, true, true );
                        break;
                    case GREATER_NET:
                        usedAmt = positions.getNetAmount( netAmounts, true, false );
                        utilizedAmt = positions.getNetAmount( netAmounts, true, true );
                        break;
                    case AGGREGATE_NET:
                        usedAmt = positions.getAggregateNetAmount( netAmounts, true, false );
                        utilizedAmt = positions.getAggregateNetAmount( netAmounts, true, true );
                        break;
                    case AGGREGATE_NET_CASH:
                        usedAmt = positions.getNetPayableAmount( netAmounts, true, false );
                        utilizedAmt = positions.getNetPayableAmount( netAmounts, true, true );
                        break;
                }
            }
            finally
            {
                cclr.getLock().unlock();
            }

            // updates the values in the real-time credit utilization
            rcu.setLimitAmount( limitAmt );
            rcu.setPandL( profitAndLoss );
            rcu.setUsedAmount( usedAmt );
            rcu.setAvailableLimit( cu.isIntegrationMode() ? cu.getAvailableReserveAmount() : cu.getAdjustedLimit() - utilizedAmt - cu.getReservedAmount() );
            rcu.setUtilizedPercent( limitAmt != 0.0 ? ( utilizedAmt + cu.getReservedAmount() ) / limitAmt * 100 : 0.0 );
            if ( CreditLimit.creditCalcLog.isDebugEnabled() )
            {
                CreditLimit.creditCalcLog.debug( new StringBuilder( 200 ).append( "CUC.updateRealtimeCreditUtilization : Updated real-time credit utilization. calc=" )
                        .append( this.getShortName() ).append( ",netReceivable=" ).append( netReceivable ).append( ",netPayable=" )
                        .append( netPayable ).append( ",realtimeCu=" ).append( rcu ).toString() );
            }
        }
    }

    /**
     * Returns the dealing limit for the currency pair on the date from the credit utilization.
     *
     * @param cu       credit utilization
     * @param baseCcy  base currency
     * @param varCcy   variable currency
     * @param date     date
     * @param calcType calc type
     * @param rateMap  rate map
     * @param isMaker  maker
     * @return dealing limit
     */
    protected DealingLimit getDealingLimit( CreditUtilization cu, Currency baseCcy, Currency varCcy, IdcDate date, int calcType, Map<String, FXPrice> rateMap, boolean isMaker )
    {
        CurrencyPositionCollection ccyPos = cu.getCurrencyPositions();
        FXMarketDataSet fxMds = ( FXMarketDataSet ) getMarketDataSet( cu );
        if ( cu.isIntegrationMode() )
        {
            return ccyPos.getIntegrationModeDealingLimit( cu, fxMds, baseCcy, varCcy, date, rateMap, isMaker );
        }
        switch ( calcType )
        {
            case NET_RECEIVABLE:
            {
                double[] netAmounts = ccyPos.getNetAmounts( cu.getCurrency(), fxMds, rateMap );
                return ccyPos.getNetReceivableDealingLimit( getAvailableLimitExcludingUsedAmount( cu ), netAmounts, cu.getCurrency(), fxMds, date, baseCcy, varCcy, rateMap, isMaker );
            }
            case GREATER_NET:
                return ccyPos.getNetAmountDealingLimit( getAvailableLimitExcludingUsedAmount( cu ), cu.getCurrency(), fxMds, date, baseCcy, varCcy, rateMap, isMaker );
            case AGGREGATE_NET:
                return ccyPos.getAggregateNetAmountDealingLimit( getAvailableLimitExcludingUsedAmount( cu ), cu.getCurrency(), fxMds, date, baseCcy, varCcy, rateMap, isMaker );
            case AGGREGATE_NET_CASH:
                return ccyPos.getAggregateNetCashAmountDealingLimit( fxMds, date, baseCcy, varCcy, rateMap, isMaker );
        }
        return null;
    }

    /**
     * Returns the utilization amount from the currency positions of credit utilization. In this case, net receivable is used.
     *
     * @param cu credit utilization
     * @return used amount
     */
    protected double getUtilizationAmount( CreditUtilization cu )
    {
        return getUtilizationAmount( cu, new HashMap<String, FXPrice>() );
    }

    /**
     * Returns the utilization amount
     *
     * @param cu      credit utilization
     * @param rateMap rate map
     * @return used amount
     */
    protected double getUtilizationAmount( CreditUtilization cu, Map<String, FXPrice> rateMap )
    {
        return cu.getCurrencyPositions().getNetReceivableAmount( cu.getCurrency(), ( FXMarketDataSet ) getMarketDataSet( cu ), rateMap, true, cu.isApplyPandL() );
    }

    /**
     * Applies credit utilization event amount to the credit utilization.
     *
     * @param cu                       credit utilization
     * @param cue                      credit utilization event
     * @param calcType                 calc type
     * @param mds                      market data set
     * @param excessUtilizationAllowed excess credit allowed
     * @param rateMap                  rate map
     * @return result
     */
    protected boolean applyAmount( CreditUtilization cu, CreditUtilizationEvent cue, int calcType, MarketDataSet mds, boolean excessUtilizationAllowed, Map<String, FXPrice> rateMap )
    {
        if ( cue.getReservedAmount() != 0.0 )
        {
            return earmarkAmount( cu, cue, calcType, mds, rateMap );
        }
        else    // set amount
        {
            boolean result;
            FXMarketDataSet fxMds = ( FXMarketDataSet ) mds;

            CurrencyPositionCollection positions = cu.getCurrencyPositions();
            if ( log.isDebugEnabled() )
            {
                log.info( new StringBuilder( 200 ).append( "CUC.applyAmount : Before applying amount, positions=" )
                        .append( positions ).append( ",cuUsedAmt=" ).append( cu.getUsedAmount() )
                        .append( ",cuePrincipal=" ).append( cue.getPrincipal() ).append( "," ).append( cue.getPrincipalCurrency() )
                        .append( ",cuePrice=" ).append( cue.getPrice() ).append( "," ).append( cue.getPriceCurrency() )
                        .append( ",tradeDate=" ).append( cue.getTradeDate() ).append( ",settlementDate=" ).append( cue.getSettlementDate() )
                        .append( ",cuLimit=" ).append( cu.getLimit() ).append( ",cuAdjLimit=" ).append( cu.getAdjustedLimit() ).toString() );
            }
            positions.addCreditUtilizationEvent( cue );
            double[] netAmounts = positions.getNetAmounts( cu.getCurrency(), fxMds, rateMap );
            double netUsed = 0.0;

            switch ( calcType )
            {
                case NET_RECEIVABLE:
                    netUsed = positions.getNetReceivableAmount( netAmounts, true, cu.isApplyPandL() );
                    break;
                case GREATER_NET:
                    netUsed = positions.getNetAmount( netAmounts, true, cu.isApplyPandL() );
                    break;
                case AGGREGATE_NET:
                    netUsed = positions.getAggregateNetAmount( netAmounts, true, cu.isApplyPandL() );
                    break;
            }

            if ( CreditLimit.creditCalcLog.isDebugEnabled() )
            {
                CreditLimit.creditCalcLog.info( new StringBuilder( 200 ).append( "CUC.applyAmount : positions after applying amount. pos=" )
                        .append( positions ).append( ",new usedAmt=" ).append( netUsed ).append( ",cuLimit=" ).append( cu.getLimit() )
                        .append( ",cuAdjLimit=" ).append( cu.getAdjustedLimit() ).toString() );
            }
            result = cu.addUsedLimitAmount( cue, netUsed, true, excessUtilizationAllowed );

            if ( !result )
            {
                // recalculate the utilization without the new positions and see if the new position reduces credit utilization.
                double netUsedWithNewPositions = netUsed;
                positions.removeCreditUtilizationEvent( cue );
                netAmounts = positions.getNetAmounts( cu.getCurrency(), fxMds, rateMap );
                switch ( calcType )
                {
                    case NET_RECEIVABLE:
                        netUsed = positions.getNetReceivableAmount( netAmounts, true, cu.isApplyPandL() );
                        break;
                    case GREATER_NET:
                        netUsed = positions.getNetAmount( netAmounts, true, cu.isApplyPandL() );
                        break;
                    case AGGREGATE_NET:
                        netUsed = positions.getAggregateNetAmount( netAmounts, true, cu.isApplyPandL() );
                        break;
                }
                if ( netUsedWithNewPositions >= netUsed && !isAllowCreditBreachOnTodayTradeWithZeroUtilization( cu, cue, positions.getBaseDate() ))
                {
                    cu.setUsedAmount( netUsed );
                    cue.setLastAction( FAIL_ACTION );
                    cue.setErrorCode( CreditLimit.ERROR_INSUFFICIENT_CREDIT );
                    log.info( new StringBuilder( 200 ).append( "CUC.applyAmount : Insufficient credit. calc=" )
                            .append( this.getShortName() ).append( ",pos=" ).append( positions )
                            .append( ",cuePrincipal=" ).append( cue.getPrincipal() ).append( cue.getPrincipalCurrency() )
                            .append( ",cuePrice=" ).append( cue.getPrice() ).append( cue.getPriceCurrency() )
                            .append( ",cue.usedAmt=").append( cue.getUsedAmount() ).append( ",cue.tc=" ).append( cue.getTenorCoefficient() )
                            .append( ",netUsedAmt=" ).append( netUsed ).append( ",cu.usedAmt=" ).append( cu.getUsedAmount() )
                            .append( ",netUsedWithNewPositions=" ).append( netUsedWithNewPositions )
                            .append( ",cu.reserveAmt=" ).append( cu.getReservedAmount() ).append( ",cuLimit=" ).append( cu.getLimit() )
                            .append( ",cuAdjustedLimit=" ).append( cu.getAdjustedLimit() ).append( ",marketRate=" )
                            .append( positions.getMarketRates( rateMap ) ).append( ",earmarkedUsedAmt=" ).append( cu.getEarmarkedUsedAmount() )
                            .append( ",cue.trdDate=").append( cue.getTradeDate() ).toString() );
                }
                else
                {
                    log.info( new StringBuilder( 200 ).append( "CUC.applyAmount : Allow credit operation due to improvement in utilization. calc=" )
                            .append( this.getShortName() ).append( ",pos=" ).append( positions )
                            .append( ",cuePrincipal=" ).append( cue.getPrincipal() ).append( cue.getPrincipalCurrency() )
                            .append( ",cuePrice=" ).append( cue.getPrice() ).append( cue.getPriceCurrency() )
                            .append( ",cue.usedAmt=").append( cue.getUsedAmount() ).append( ",cue.tc=" ).append( cue.getTenorCoefficient() )
                            .append( ",netUsedWithNewPositions=" ).append( netUsedWithNewPositions ).append( ",netUsedWithoutNewPositions=" ).append( netUsed ).append( ",cu.usedAmt=" ).append( cu.getUsedAmount() )
                            .append( ",cu.reserveAmt=" ).append( cu.getReservedAmount() ).append( ",cuLimit=" ).append( cu.getLimit() )
                            .append( ",cuAdjustedLimit=" ).append( cu.getAdjustedLimit() ).append( ",marketRate=" ).append( positions.getMarketRates( rateMap ) )
                            .append( ",earmarkedUsedAmt=" ).append( cu.getEarmarkedUsedAmount() ).toString() );
                    positions.addCreditUtilizationEvent( cue );
                    result = cu.addUsedLimitAmount( cue, netUsedWithNewPositions, true, true );
                    cue.setLastAction( APPLY_ACTION );
                }
            }
            else
            {
                cue.setLastAction( APPLY_ACTION );
            }

            // update the event with the limit and reserved and used amount
            cue.setLimitAmount( cu.getLimit() );
            cue.setTotalReservedAmount( cu.getReservedAmount() );
            cue.setTotalUsedAmount( cu.getUsedAmount() );
            //WL - this value is displayed on the deal ticket regarding credit available
            cue.setAvailableReserveAmount( cu.getAvailableMarginReserveAmount() );
            cue.setCreditUtilizationCalculator( cu.getCreditLimitRule().getCreditUtilizationCalculator() );
            cu.setMarketRates( positions.getMarketRates( rateMap ) );

            if ( CreditLimit.creditCalcLog.isDebugEnabled() )
            {
                CreditLimit.creditCalcLog.debug( new StringBuilder( 200 ).append("CUC.applyAmount : After applying amount, positions=")
                        .append(positions).append( ",cuUsedAmt=" ).append(cu.getUsedAmount()).append( ",netUsed=" )
                        .append(netUsed).toString() );
            }
            return result;
        }
    }

    public boolean updateUtilizedAmount( CreditUtilization cu, CreditUtilizationEvent cue, AmountOfInstrument principalAoi, AmountOfInstrument priceAoi, MarketDataSet mds, boolean excessUtilizationAllowed, Map<String, FXPrice> rateMap )
    {
        return updateUtilizedAmount( cu, cue, NET_RECEIVABLE, principalAoi, priceAoi, mds, excessUtilizationAllowed, rateMap );
    }

    public boolean updateUtilizedAmount( CreditUtilization cu, CreditUtilizationEvent cue, int calcType, AmountOfInstrument principalAoi, AmountOfInstrument priceAoi, MarketDataSet mds, boolean excessUtilizationAllowed, Map<String, FXPrice> rateMap )
    {
        boolean result;
        FXMarketDataSet fxMds = ( FXMarketDataSet ) mds;
        CurrencyPositionCollection positions = cu.getCurrencyPositions();
        if ( log.isDebugEnabled() )
        {
            log.debug(new StringBuilder(200).append("CUC.updateUtilizedAmount : Before updating utilized amount, positions=")
                    .append(positions).append(",cuUsedAmt=").append(cu.getUsedAmount())
                    .append(",cuePrincipal=").append(cue.getPrincipal()).append(",").append(cue.getPrincipalCurrency())
                    .append(",cuePrice=").append(cue.getPrice()).append(",").append(cue.getPriceCurrency())
                    .append(",cuLimit=").append(cu.getLimit()).append(",cuAdjLimit=").append(cu.getAdjustedLimit()).toString());
        }

        //1. Remove the old currency pair position and add back new positions to the cue in the position collection
        positions.removeCreditUtilizationEvent( cue );
        cue.setOriginalPrincipalAmount( cue.getPrincipal() );
        cue.setOriginalPriceAmount( cue.getPrice() );
        cue.setOriginalTradePrincipalAmount( cue.getTradePrincipalAmount() );
        cue.setOriginalTradePriceAmount( cue.getTradePriceAmount() );
        cue.setOriginalUsedAmount( cue.getUsedAmount() );

        if ( cu.isIntegrationMode() )
        {
            cu.updateEarmarkedUsedAmount( -cue.getUsedAmount() );
        }

        //cue set new positions
        cue.setPrincipalCurrency( ( Currency ) principalAoi.getInstrument() );
        cue.setPriceCurrency( ( Currency ) priceAoi.getInstrument() );
        double tc = cue.getTenorCoefficient();
        if ( tc >= 0.0 && tc != CreditLimit.DEFAULT_TENOR_COEFFICIENT )
        {
            cue.setPrincipal( cue.getPrincipalCurrency().round( principalAoi.getAmount() * tc ) );
            cue.setPrice( cue.getPriceCurrency().round( priceAoi.getAmount() * tc ) );
        }
        else
        {
            cue.setPrincipal( principalAoi.getAmount() );
            cue.setPrice( priceAoi.getAmount() );
        }

        // update trade amounts fields
        cue.setTradePrincipalAmount( principalAoi.getAmount() );
        cue.setTradePriceAmount( priceAoi.getAmount() );

        if ( cue.getOriginalPrincipalAmount() != 0.0 )
        {
            cue.setUsedAmount( cue.getOriginalUsedAmount() * ( cue.getPrincipal() / cue.getOriginalPrincipalAmount() ) );
        }
        positions.addCreditUtilizationEvent( cue );

        if ( log.isDebugEnabled() )
        {
            log.info(new StringBuilder(200).append("CUC.updateUtilizedAmount : After updating utilized amount. positions=")
                    .append(positions).append(",cuUsedAmt=").append(cu.getUsedAmount())
                    .append(",cuePrincipal=").append(cue.getPrincipal()).append(",").append(cue.getPrincipalCurrency())
                    .append(",cuePrice=").append(cue.getPrice()).append(",").append(cue.getPriceCurrency())
                    .append(",cuLimit=").append(cu.getLimit()).append(",cuAdjLimit=").append(cu.getAdjustedLimit()).toString());
        }

        double[] netAmounts = positions.getNetAmounts( cu.getCurrency(), fxMds, rateMap );
        double netUsed = 0.0;

        switch ( calcType )
        {
            case NET_RECEIVABLE:
                netUsed = positions.getNetReceivableAmount( netAmounts, true, cu.isApplyPandL() );
                break;
            case GREATER_NET:
                netUsed = positions.getNetAmount( netAmounts, true, cu.isApplyPandL() );
                break;
            case AGGREGATE_NET:
                netUsed = positions.getAggregateNetAmount( netAmounts, true, cu.isApplyPandL() );
                break;
        }

        if ( CreditLimit.creditCalcLog.isDebugEnabled() )
        {
            CreditLimit.creditCalcLog.debug( new StringBuilder( 200 ).append( "CUC.updateUtilizedAmount : positions after updating amount. pos=" )
                    .append( positions ).append( ",new usedAmt=" ).append( netUsed ).append( ",cuLimit=" ).append( cu.getLimit() )
                    .append( ",cuAdjLimit=" ).append( cu.getAdjustedLimit() ).toString() );
        }

        result = cu.addUsedLimitAmount( cue, netUsed, true, excessUtilizationAllowed );

        // update the event with the limit and reserved and used amount
        cue.setLimitAmount( cu.getLimit() );
        cue.setTotalReservedAmount( cu.getReservedAmount() );
        cue.setTotalUsedAmount( cu.getUsedAmount() );
        cue.setAvailableReserveAmount( cu.getAvailableMarginReserveAmount() );
        cue.setCreditUtilizationCalculator( cu.getCreditLimitRule().getCreditUtilizationCalculator() );
        cu.setMarketRates( positions.getMarketRates( rateMap ) );

        if ( CreditLimit.creditCalcLog.isDebugEnabled() )
        {
            CreditLimit.creditCalcLog.debug( new StringBuilder( 200 ).append( "CUC.updateUtilizedAmount : After updating amount, positions=")
                    .append(positions).append(",cuUsedAmt=").append(cu.getUsedAmount()).append(",netUsed=")
                    .append(netUsed).toString());
        }

        return result;
    }

    /**
     * Removes the credit utilization event amount from the credit utilization.
     *
     * @param cu       credit utilization
     * @param cue      credit utilization event
     * @param calcType calc type
     * @param mds      market data set
     * @param rateMap  rate map
     * @return result
     */
    protected boolean removeAmount( CreditUtilization cu, CreditUtilizationEvent cue, int calcType, MarketDataSet mds, Map<String, FXPrice> rateMap )
    {
        if ( cue.getUsedAmount() != 0.0 )
        {
            FXMarketDataSet fxMds = ( FXMarketDataSet ) mds;
            CurrencyPositionCollection positions = cu.getCurrencyPositions();
            if ( CreditLimit.creditCalcLog.isDebugEnabled() )
            {
                CreditLimit.creditCalcLog.debug( new StringBuilder( 200 ).append( "CUC.removeAmount : Before removing amount, positions=" )
                        .append( positions ).append( ",cuUsedAmt=" ).append( cu.getUsedAmount() ).append( ",cueUsedAmt=" )
                        .append( cue.getUsedAmount() ).toString() );
            }

            positions.removeCreditUtilizationEvent( cue );
            double[] netAmounts = positions.getNetAmounts( cu.getCurrency(), fxMds, rateMap );
            double netUsedAmount = 0.0;

            switch ( calcType )
            {
                case NET_RECEIVABLE:
                    netUsedAmount = positions.getNetReceivableAmount( netAmounts, true, cu.isApplyPandL() );
                    break;
                case GREATER_NET:
                    netUsedAmount = positions.getNetAmount( netAmounts, true, cu.isApplyPandL() );
                    break;
                case AGGREGATE_NET:
                    netUsedAmount = positions.getAggregateNetAmount( netAmounts, true, cu.isApplyPandL() );
                    break;
            }
//            cu.setUsedAmount( netUsedAmount > 0 ? netUsedAmount : 0.0 );
            cu.setUsedAmount( netUsedAmount );
            if ( cu.isIntegrationMode() )
            {
                cu.updateEarmarkedUsedAmount( -cue.getUsedAmount() );
            }

            if ( CreditLimit.creditCalcLog.isDebugEnabled() )
            {
                CreditLimit.creditCalcLog.debug( new StringBuilder( 200 ).append( "CUC.removeAmount : After removing amount, positions=" )
                        .append( positions ).append( ",cuUsedAmt=" ).append( cu.getUsedAmount() ).append( ",cueUsedAmt=" )
                        .append( cue.getUsedAmount() ).append( ",netUsed" ).append( netUsedAmount ).append( ",limits=" ).toString() );
            }
            cu.setPositions( positions.getPositions() );
            cu.setMarketRates( positions.getMarketRates( rateMap ) );
        }
        else if ( cue.getReservedAmount() != 0.0 )
        {
            cu.addUsedReserveAmount( -cue.getReservedAmount() );
        }
        cue.setLastAction( REMOVE_ACTION );
        return true;
    }

    /**
     * Returns the market dataset associated with credit limit ruleset.
     *
     * @param cu credit utilization
     * @return market data set
     */
    protected MarketDataSet getMarketDataSet( CreditUtilization cu )
    {
        MarketDataSet mds = ( ( CreditLimitRuleSet ) cu.getCreditLimitRule().getParentRule().getRuleSet() ).getMarketDataSet();
        if ( mds == null )
        {
            log.info( "CUC.getMarketDataSet : no market data set found for cu=" + cu );
            Organization org = ReferenceDataCacheC.getInstance().getOrganization( cu.getNamespace() );
            if ( org != null )
            {
                CreditLimitOrgFunction orgFunction = org.getCreditLimitOrgFunction();
                if ( orgFunction != null )
                {
                    mds = orgFunction.getMarketDataSet();
                }
                log.info( "CUC.getMarketDataSet : mds from org function=" + orgFunction + ",org=" + org + ",mds=" + mds + ",cu=" + cu );
            }
        }
        return mds;
    }

    /**
     * Returns true if the amount in the credit utilization event is successfully earmarked in the credit utilization.
     *
     * @param cu       credit utilization
     * @param cue      credit utilization event
     * @param calcType calc type
     * @param mds      market data set
     * @param rateMap  rateMap rate map
     * @return result of operation
     */
    private boolean earmarkAmount( CreditUtilization cu, CreditUtilizationEvent cue, int calcType, MarketDataSet mds, Map<String, FXPrice> rateMap )
    {
        boolean result = false;
        cue.setErrorCode( null );
        //todo: WL - used for earmarking available credit for RFS -  wont work when leverage factor is set as available amt does not account for the leverage factor
        if ( cu.getAvailableMarginReserveAmount() - cue.getReservedAmount() < 0.0 )
        {
            cue.setLastAction( FAIL_ACTION );
            cue.setErrorCode( CreditLimit.ERROR_INSUFFICIENT_RESERVE );
        }
        else
        {
            cu.updateReservedAmount( cue.getReservedAmount() );
            cue.setLastAction( EARMARK_ACTION );
            result = true;
        }

        // update the event with the limit and reserved and used amount
        cue.setLimitAmount( cu.getLimit() );
        cue.setTotalReservedAmount( cu.getReservedAmount() );
        cue.setTotalUsedAmount( cu.getUsedAmount() );
        cue.setCreditUtilizationCalculator( cu.getCreditLimitRule().getCreditUtilizationCalculator() );
        return result;
    }

    /**
     * Returns the currency position collecton builder calculator.
     *
     * @return currency position builder
     */
    private CurrencyPositionCollectionBuilder getCurrencyPositionCollectionBuilder()
    {
        if ( this.positionBuilder == null )
        {
            this.positionBuilder = CreditLimitFactory.newCurrencyPositionCollectionBuilder();
        }
        return positionBuilder;
    }

    /**
     * Returns the available limit excluding the used amount.
     *
     * @param cu credit utilization
     * @return limit
     */
    private double getAvailableLimitExcludingUsedAmount( CreditUtilization cu )
    {
        return cu.getAdjustedLimit() - cu.getReservedAmount();
    }

    /**
     * Returns the query parameters and values used to query and build the currency positions.
     *
     * @param cu       credit utilization
     * @param baseDate base date
     * @return query parameters
     */
    private CurrencyPositionQueryParameters getCurrencyPositionQueryParameters( final CreditUtilization cu, IdcDate baseDate )
    {
        CurrencyPositionQueryParameters queryParams = new CurrencyPositionQueryParametersC();
        queryParams.setBaseDate( baseDate );
        queryParams.setNetting( cu.getCreditLimitRule().isAllowNetting() );
        queryParams.setDailyAggregate( isDailyAggregate () );
        queryParams.setIgnoreLimitCcyPositions( isIgnoreLimitCurrency () );
        queryParams.setIgnoreCurrentBusinessDate( cu.ignoreCurrentDatePositions() );
        queryParams.setApplyPL( cu.isApplyPandL() );
        boolean isCashSettlement = CreditUtilC.isCashSettlement ( cu.getCreditLimitRule () );
        queryParams.setDailyPL( cu.isDailyPL() && !isCashSettlement );
        return queryParams;
    }

    private boolean isAllowCreditBreachOnTodayTradeWithZeroUtilization( CreditUtilization cu, CreditUtilizationEvent cue, IdcDate baseDate )
    {
        if ( cue.getPrincipal() == 0.0 && cue.getPrice() == 0.0 && baseDate.isSameAs( cue.getTradeDate() ) )
        {
            final Organization cpo = ReferenceDataCacheC.getInstance().getOrganization( cu.getNamespace().getShortName() );
            return CreditLimitConfigurationFactory.getCreditConfigurationMBean().isTodayTradesWithZeroUtilizationAllowedOnBreach( cpo );
        }
        return false;
    }

    @Override
    public boolean isCreditAvailable( CurrencyPositionCollection cps, CreditUtilization cu )
    {
        return isCreditAvailable ( cps, cu, NET_RECEIVABLE );
    }

    protected boolean isCreditAvailable ( CurrencyPositionCollection cps, CreditUtilization cu, int calcType )
    {
        FXMarketDataSet fxMds = ( FXMarketDataSet ) getMarketDataSet ( cu );
        Map<String, FXPrice> rateMap = new HashMap<String, FXPrice>();
        double[] netAmounts = cps.getNetAmounts( cu.getCurrency(), fxMds, rateMap );
        double netUsed = 0.0;

        switch ( calcType )
        {
            case NET_RECEIVABLE:
                netUsed = cps.getNetReceivableAmount( netAmounts, true, cu.isApplyPandL() );
                break;
            case GREATER_NET:
                netUsed = cps.getNetAmount( netAmounts, true, cu.isApplyPandL() );
                break;
            case AGGREGATE_NET:
                netUsed = cps.getAggregateNetAmount( netAmounts, true, cu.isApplyPandL() );
                break;

            case AGGREGATE_NET_CASH:
                if ( cu.getCreditLimitRule ().isAccountLimitCheckPolicyEnabled () )
                {
                    netUsed = cps.getNetReceivableAmount( netAmounts, true, cu.isApplyPandL() );
                }
                break;
        }

        if ( CreditLimit.creditCalcLog.isDebugEnabled() )
        {
            CreditLimit.creditCalcLog.info( new StringBuilder( 200 ).append( "CUC.isCreditAvailable : positions=" )
                    .append( cps ).append( ",new usedAmt=" ).append( netUsed ).append( ",cuLimit=" ).append( cu.getLimit() )
                    .append( ",cuAdjLimit=" ).append( cu.getAdjustedLimit() ).toString() );
        }
        return cu.getAdjustedLimit () - netUsed >= 0.0;
    }

    protected boolean isDailyAggregate()
    {
        return false;
    }

    protected boolean isIgnoreLimitCurrency()
    {
        return false;
    }
}
