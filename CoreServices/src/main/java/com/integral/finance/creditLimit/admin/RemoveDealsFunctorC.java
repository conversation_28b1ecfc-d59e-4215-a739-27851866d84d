package com.integral.finance.creditLimit.admin;

// Copyright (c) 2011 Integral Development Corporation.  All Rights Reserved.

import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationFactory;
import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationMBean;
import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.DealC;
import com.integral.finance.dealing.DealRequest;
import com.integral.finance.dealing.Order;
import com.integral.finance.dealing.OrderC;
import com.integral.finance.dealing.facade.DealStateFacade;
import com.integral.finance.dealing.facade.OrderStateFacade;
import com.integral.finance.dealing.fx.FXDealLeg;
import com.integral.finance.dealing.fx.FXSingleLegDeal;
import com.integral.finance.dealing.fx.FXSwapDeal;
import com.integral.persistence.ClusterCommitEventAdapterC;
import com.integral.persistence.Entity;
import com.integral.persistence.PersistenceFactory;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.server.VirtualServerType;
import com.integral.time.DatePeriod;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateConstants;
import com.integral.workflow.State;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.CursoredStream;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * This functor class is used to remove the deal/order data model.
 *
 * <AUTHOR> Development Corp.
 */
public class RemoveDealsFunctorC extends CreditFunctorC
{
    protected DealingDataPurgeConfigurationMBean purgeConfigMBean = DealingDataPurgeConfigurationFactory.getDealingDataPurgeConfigurationMBean();
    private AtomicInteger ordersBatchCount = new AtomicInteger();
    private AtomicInteger totalOrdersCount = new AtomicInteger();
    private AtomicInteger deletedOrdersCount = new AtomicInteger();
    private long ordersStartTime;
    private Date lastOrderCreatedDate;

    public RemoveDealsFunctorC( String name )
    {
        setName( name );
    }

    public void execute()
    {
        try
        {
            // skip the purge if the current virtual server type is not fxi dealing server.(IS/BA/OA)
            String s = ConfigurationFactory.getServerMBean().getVirtualServerType();
            if ( s == null || !( s.equals( VirtualServerType.BrokerAdaptor ) || s.equals( VirtualServerType.OrderAdaptor ) || s.equals( VirtualServerType.FXIServer ) || s.equals( VirtualServerType.AdminServer ) ) )
            {
                log.info( "RDF.execute : Functor not executed as it is not a FXI dealing server or admin server. serverType=" + s );
                return;
            }

            if ( isEnabled() && purgeConfigMBean.isDealingDataPurgeEnabled() )
            {
                start();

                if ( purgeConfigMBean.isDealingDataClassPurgeEnabled( OrderC.class.getSimpleName() ) )
                {
                    performOrderDealModelPurge();
                }

                // delete all the RFS deals. RFS deals are created without any order and without any parent and children deals.
                if ( purgeConfigMBean.isDealingDataClassPurgeEnabled( DealC.class.getSimpleName() ) )
                {
                    deleteOldRFSDeals();
                }
            }
            else
            {
                log.info( "RDF.execute : Functor not executed. this=" + this + ",isPurgeEnabled=" + purgeConfigMBean.isDealingDataPurgeEnabled() );
            }
        }
        catch ( Exception e )
        {
            log.error( "RDF.execute : Exception", e );
        }
        finally
        {
            finish();
        }

        // mark suspension as completed if suspend was initiated.
        if ( suspensionInitiated )
        {
            this.suspended = true;
        }
    }

    private void performOrderDealModelPurge()
    {
        for ( int i = 0; i < 100; i++ )
        {
            try
            {
                deleteOldOrders();
                break;
            }
            catch ( Exception e )
            {
                log.error( "RDF.performOrderDealModelPurge : Exception while doing order/deal model purge. Attempt=" + i, e );
            }
        }
    }

    private void deleteOldOrders() throws Exception
    {
        ordersStartTime = System.currentTimeMillis();
        long t0 = System.currentTimeMillis();
        CursoredStream cursor = null;
        int ordersCount = 0;
        int batchCount = 0;
        int deletedOrdersTotalCount = 0;
        try
        {
            IdcDate cutoffDate = getCutoffDate();
            IdcDate adjustedCutOffDate = cutoffDate.subtractDays( 2 );// this offset is adjust 2days before cutoff date to avoid going through all spot trades.
            Session session = PersistenceFactory.newSession();
            int batchSize = purgeConfigMBean.getCommitBatchSize( OrderC.class.getSimpleName() );
            ExpressionBuilder eb = new ExpressionBuilder();

            ReadAllQuery raq = new ReadAllQuery( eb );
            raq.setReferenceClass( OrderC.class );
            raq.useCursoredStream( batchSize, batchSize );
            raq.addOrdering( eb.get( Entity.ObjectID ) );

            int num = 0;
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.setProperty( ClusterCommitEventAdapterC.MULTI_APP, ClusterCommitEventAdapterC.MULTI_APP_DISABLED );

            cursor = ( CursoredStream ) session.executeQuery( raq );
            log.info( "RDF.deleteOldOrders : Query took " + ( System.currentTimeMillis() - t0 ) + ",batchSize=" + batchSize );
            Collection<Order> orderBatch = new ArrayList<Order>( batchSize );
            while ( !cursor.atEnd() )
            {
                if ( !isEnabled() || !purgeConfigMBean.isDealingDataPurgeEnabled() || !purgeConfigMBean.isDealingDataClassPurgeEnabled( OrderC.class.getSimpleName() ) || !purgeConfigMBean.isDealOrderAsyncDeletionEnabled() )
                {
                    log.info( "RDF.deleteOldOrders : Skipping further orders deletion due to configuration change. enabled=" + purgeConfigMBean.isDealingDataPurgeEnabled() );
                    break;
                }

                Order anOrder = ( Order ) cursor.read();
                try
                {
                    ordersCount++;
                    // skip further deletion process if orders beyond cut-off date is encountered.
                    if ( anOrder != null && anOrder.getCreatedDate() != null && anOrder.getCreatedDate().getTime() > adjustedCutOffDate.asJdkDate().getTime() )
                    {
                        log.info( "RDF.deleteOldOrders : Skipping further orders upon reaching adjusted cutoff date="
                                + adjustedCutOffDate + ",order.createdDate=" + anOrder.getCreatedDate() + ",order=" + anOrder + ",cutoffDate=" + cutoffDate );
                        break;
                    }
                    orderBatch.add( anOrder );
                }
                catch ( Exception e )
                {
                    log.error( "RDF.deleteOldOrders : Error deleting " + anOrder + ",createdDate=" + ( anOrder != null ? anOrder.getCreatedDate() : null ), e );
                }

                num++;

                // close transaction
                if ( num == batchSize )
                {
                    batchCount++;
                    num = 0;
                    // delegate the handling of the requests to purge worker threads.
                    if ( purgeConfigMBean.isMultiThreadDealingDataPurgeEnabled() )
                    {
                        CreditFunctorServerC.getInstance().getPooledExecutor().execute( new OrderPurgeWorker( new ArrayList<Order>( orderBatch ), cutoffDate ) );
                    }
                    else
                    {
                        new OrderPurgeWorker( new ArrayList<Order>( orderBatch ), cutoffDate ).run();
                    }
                    orderBatch = new ArrayList<Order>( batchSize );
                    cursor.releasePrevious();
                }
            }

            if ( num > 0 )
            {
                // delegate the handling of the requests to purge worker threads.
                if ( purgeConfigMBean.isMultiThreadDealingDataPurgeEnabled() )
                {
                    CreditFunctorServerC.getInstance().getPooledExecutor().execute( new OrderPurgeWorker( new ArrayList<Order>( orderBatch ), cutoffDate ) );
                }
                else
                {
                    new OrderPurgeWorker( new ArrayList<Order>( orderBatch ), cutoffDate ).run();
                }
                cursor.releasePrevious();
            }

            cursor.close();
            cursor = null;

        }
        catch ( Exception e )
        {
            log.error( "RDF.deleteOldOrders : Exception while removing the old data", e );
            throw e;
        }
        finally
        {
            if ( cursor != null )
            {
                cursor.close();
            }
        }

        long elapsed = System.currentTimeMillis() - t0;
        double avgBatchTime = batchCount > 0 ? elapsed / batchCount : -1;
        log.info( "RDF.deleteOldOrders : Removed total order  " + deletedOrdersTotalCount + " of a total of "
                + ordersCount + " time taken=" + elapsed + ",batchNum=" + batchCount + ",avgBatchTime=" + avgBatchTime );
    }

    private void deleteOldRFSDeals()
    {
        long t0 = System.currentTimeMillis();
        CursoredStream cursor = null;
        int RFSDealsCount = 0;
        int batchCount = 0;
        int deletedRFSDealsTotalCount = 0;
        try
        {
            IdcDate cutoffDate = getCutoffDate();
            IdcDate adjustedCutOffDate = cutoffDate.subtractDays( 2 );// this offset is adjust 2days before cutoff date to avoid going through all spot trades.
            Session session = PersistenceFactory.newSession();
            int batchSize = purgeConfigMBean.getCommitBatchSize( DealC.class.getSimpleName() );
            ExpressionBuilder eb = new ExpressionBuilder();
            ReadAllQuery raq = new ReadAllQuery( eb );
            Expression expr = eb.get( "parent" ).isNull().and( eb.get( "order" ).isNull() );// for RFS deals, parent and order info will be null.
            raq.setSelectionCriteria( expr );
            raq.setReferenceClass( DealC.class );
            raq.useCursoredStream( batchSize, batchSize );
            raq.addOrdering( eb.get( Entity.ObjectID ) );

            int num = 0;
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.setProperty( ClusterCommitEventAdapterC.MULTI_APP, ClusterCommitEventAdapterC.MULTI_APP_DISABLED );
            long batchTime = System.currentTimeMillis();

            cursor = ( CursoredStream ) session.executeQuery( raq );
            log.info( "RDF.deleteOldRFSDeals : Query took " + ( System.currentTimeMillis() - t0 ) + ",batchSize=" + batchSize );
            while ( !cursor.atEnd() )
            {
                if ( !isEnabled() || !purgeConfigMBean.isDealingDataPurgeEnabled() || !purgeConfigMBean.isDealingDataClassPurgeEnabled( DealC.class.getSimpleName() ) )
                {
                    log.info( "RDF.deleteOldRFSDeals : Skipping further RFS deals deletion due to configuration change. enabled=" + purgeConfigMBean.isDealingDataPurgeEnabled() );
                    break;
                }

                Deal aDeal = ( Deal ) cursor.read();
                try
                {
                    RFSDealsCount++;
                    // skip further deletion process if deals beyond cut-off date is encountered.
                    if ( aDeal != null && aDeal.getCreatedDate() != null && aDeal.getCreatedDate().getTime() > adjustedCutOffDate.asJdkDate().getTime() )
                    {
                        log.info( "RDF.deleteOldRFSDeals : Skipping further deals upon reaching adjusted cutoff date="
                                + adjustedCutOffDate + ",order.createdDate=" + aDeal.getCreatedDate() + ",deal=" + aDeal + ",cutoffDate=" + cutoffDate );
                        break;
                    }
                    if ( shouldDeleteDeal( aDeal, cutoffDate ) )
                    {
                        deleteDeal( aDeal, uow );
                        deletedRFSDealsTotalCount++;
                        log.info( new StringBuilder( 300 ).append( "RDF.deleteOldRFSDeals : Deleting deal state=" ).append( aDeal.getState() )
                                .append( ",tid=" ).append( aDeal.getTransactionId() ).append( ",createdDate=" )
                                .append( aDeal.getCreatedDate() ).append( ",clsf=" ).append( aDeal.getTradeClassification() ).toString() );

                    }
                    else
                    {
                        log.info( new StringBuilder( 300 ).append( "RDF.deleteOldRFSDeals : Skipping deal with state=" ).append( aDeal.getState() )
                                .append( ",tid=" ).append( aDeal.getTransactionId() ).append( ",createdDate=" )
                                .append( aDeal.getCreatedDate() ).append( ",clsf=" ).append( aDeal.getTradeClassification() ).toString() );
                    }
                }
                catch ( Exception e )
                {
                    log.error( "RDF.deleteOldRFSDeals : Error deleting " + aDeal + ",createdDate=" + ( aDeal != null ? aDeal.getCreatedDate() : null ), e );
                }

                num++;

                // close transaction
                if ( num == batchSize )
                {
                    batchCount++;
                    num = 0;
                    long ct = commitTransaction( uow, "deleteOldRFSDeals" );
                    long now = System.currentTimeMillis();
                    log.info( new StringBuilder( 400 ).append( "RDF.deleteOldRFSDeals : Stats for batchNum=" ).append( batchCount )
                            .append( ",batchTime=" ).append( now - batchTime ).append( ",totalCount=" )
                            .append( RFSDealsCount ).append( ",handledTotal=" ).append( deletedRFSDealsTotalCount ).append( ",commitTime=" )
                            .append( ct ).append( ",avgBatchTime=" ).append( ( now - t0 ) / batchCount ).toString() );
                    batchTime = System.currentTimeMillis();
                    cursor.releasePrevious();
                    uow = session.acquireUnitOfWork();
                    uow.setProperty( ClusterCommitEventAdapterC.MULTI_APP, ClusterCommitEventAdapterC.MULTI_APP_DISABLED );
                }
            }

            if ( num > 0 )
            {
                long ct = commitTransaction( uow, "deleteOldRFSDeals" );
                batchCount++;
                long now = System.currentTimeMillis();
                log.info( new StringBuilder( 300 ).append( "RDF.deleteOldRFSDeals : Stats for batchNum=" ).append( batchCount )
                        .append( ",batchTime=" ).append( now - batchTime ).append( ",totalCount=" )
                        .append( RFSDealsCount ).append( ",handledTotal=" ).append( deletedRFSDealsTotalCount ).append( ",commitTime=" )
                        .append( ct ).append( ",avgBatchTime=" ).append( ( now - t0 ) / batchCount ).toString() );
            }

            cursor.close();
            cursor = null;

        }
        catch ( Exception e )
        {
            log.error( "RDF.deleteOldRFSDeals : Exception while removing the old data", e );
        }
        finally
        {
            if ( cursor != null )
            {
                cursor.close();
            }
        }
        log.info( "RDF.deleteOldRFSDeals : Removed total RFS deal  " + deletedRFSDealsTotalCount + " of a total of "
                + RFSDealsCount + " time taken=" + ( System.currentTimeMillis() - t0 ) + ",batchNum=" + batchCount );
    }

    private boolean shouldDeleteOrder( Order order, IdcDate cutoffDate )
    {
        try
        {
            OrderStateFacade ocd = ( OrderStateFacade ) order.getFacade( OrderStateFacade.FACADE_NAME );
            if ( ocd.isCancelled() || ocd.isFailed() || ocd.isFilled() )
            {
                if ( ocd.isCancelled() || ocd.isFilled() )
                {
                    if ( isOrderValueDateEarlierThanCutoffDate( order, cutoffDate ) )
                    {
                        return true;
                    }
                }
                else
                {
                    return true;
                }
            }

            // special handling of older orders in Created state.
            if ( ocd.isCreated() )
            {
                IdcDate fartherCutOffDate = cutoffDate.subtractYears( 2 );
                if ( order.getCreatedDate() != null && order.getCreatedDate().before( fartherCutOffDate.asJdkDate() ) )
                {
                    if ( isOrderValueDateEarlierThanCutoffDate( order, cutoffDate ) )
                    {
                        return true;
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "RDF.shouldDeleteOrder : Exception on order=" + order, e );
        }
        return false;
    }

    private boolean shouldDeleteDeal( Deal deal, IdcDate cutoffDate )
    {
        try
        {
            if ( deal.getState() == null || deal.getTransactionId() == null )
            {
                return true;
            }
            DealStateFacade dcd = ( DealStateFacade ) deal.getFacade( DealStateFacade.FACADE_NAME );
            if ( dcd.isVerified() || dcd.isRejected() || dcd.isCancelled() || dcd.isFilled() || dcd.isDelayedVerification() )
            {
                if ( isDealValueDateEarlierThanCutoffDate( deal, cutoffDate ) )
                {
                    return true;
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "RDF.shouldDeleteDeal : Exception on deal=" + deal, e );
        }
        return false;
    }

    private IdcDate getCutoffDate()
    {
        return getCutoffDate( purgeConfigMBean.getDealingDataPurgeKeepPeriod() );
    }

    private IdcDate getCutoffDate( DatePeriod dp )
    {
        IdcDate currentDate = DateTimeFactory.newDate();
        int daysToKeep = dp != null && dp.asDays() > 0 ? dp.asDays() : IdcDateConstants.DAYS_PER_WEEK;
        log.info( "RDF.getCutoffDate : configured date period=" + dp + ",selected days=" + daysToKeep );
        return currentDate.subtractDays( daysToKeep );
    }

    private boolean isOrderValueDateEarlierThanCutoffDate( Order order, IdcDate cutoffDate )
    {
        // if children deals are present
        Collection<Deal> cDeals = order.getDeals();
        if ( cDeals != null && !cDeals.isEmpty() )
        {
            for ( Deal cDeal : cDeals )
            {
                Collection<Deal> ccDeals = cDeal.getDeals();
                if ( ccDeals != null && !ccDeals.isEmpty() )
                {
                    for ( Deal deal : ccDeals )
                    {
                        if ( !isDealValueDateEarlierThanCutoffDate( deal, cutoffDate ) )
                        {
                            return false;
                        }
                    }
                }

                // direct deals
                if ( !isDealValueDateEarlierThanCutoffDate( cDeal, cutoffDate ) )
                {
                    return false;
                }
            }
        }

        return true;
    }

    private boolean isDealValueDateEarlierThanCutoffDate( Deal deal, IdcDate cutoffDate )
    {
        if ( deal instanceof FXSingleLegDeal )
        {
            FXDealLeg fxDealLeg = ( ( FXSingleLegDeal ) deal ).getFXDealLeg();
            if ( fxDealLeg.getValueDate() != null && fxDealLeg.getValueDate().isLaterThanOrEqualTo( cutoffDate ) )
            {
                return false;
            }
        }
        else if ( deal instanceof FXSwapDeal )
        {
            FXDealLeg nearDealLeg = ( ( FXSwapDeal ) deal ).getNearDealLeg();
            if ( nearDealLeg.getValueDate() != null && nearDealLeg.getValueDate().isLaterThanOrEqualTo( cutoffDate ) )
            {
                return false;
            }

            FXDealLeg farDealLeg = ( ( FXSwapDeal ) deal ).getFarDealLeg();
            if ( farDealLeg.getValueDate() != null && farDealLeg.getValueDate().isLaterThanOrEqualTo( cutoffDate ) )
            {
                return false;
            }
        }
        else
        {
            log.warn( "RDF.isDealValueDateEarlierThanCutoffDate : unsupported trade type. deal=" + deal );
            return false;
        }
        return true;
    }

    private void deleteDeal( Deal aDeal, UnitOfWork uow )
    {
        try
        {
            Deal registeredDeal = ( Deal ) uow.registerObject( aDeal );
            Collection<Deal> cDeals = registeredDeal.getDeals();
            if ( cDeals != null && !cDeals.isEmpty() )
            {
                for ( Deal cDeal : cDeals )
                {
                    Deal registeredCDeal = ( Deal ) uow.registerObject( cDeal );
                    DealRequest dealRequest = registeredCDeal.getDealRequest();
                    if ( dealRequest != null )
                    {
                        DealRequest registeredDealRequest = ( DealRequest ) uow.registerObject( dealRequest );
                        registeredDealRequest.setDeal( null );
                        registeredCDeal.setDealRequest( null );
                        uow.deleteObject( registeredDealRequest );
                    }
                }
            }

            // delete the deal request associated with this.
            DealRequest dealRequest = registeredDeal.getDealRequest();
            if ( dealRequest != null )
            {
                DealRequest registeredDealRequest = ( DealRequest ) uow.registerObject( dealRequest );
                registeredDealRequest.setDeal( null );
                registeredDeal.setDealRequest( null );
                uow.deleteObject( registeredDealRequest );
            }

            uow.deleteObject( registeredDeal );
        }
        catch ( Exception e )
        {
            log.error( "RDF.deleteDeal : Exception. deal=" + aDeal, e );
        }
    }

    private void deleteOrder( Order order, UnitOfWork uow )
    {
        try
        {
            Order registeredOrder = ( Order ) uow.registerObject( order );
            Collection<Deal> deals = registeredOrder.getDeals();
            if ( deals != null && !deals.isEmpty() )
            {
                for ( Deal deal : deals )
                {
                    Deal registeredDeal = ( Deal ) uow.registerObject( deal );
                    DealRequest dealRequest = registeredDeal.getDealRequest();
                    if ( dealRequest != null )
                    {
                        DealRequest registeredDealRequest = ( DealRequest ) uow.registerObject( dealRequest );
                        registeredDealRequest.setDeal( null );
                        registeredDeal.setDealRequest( null );
                        uow.deleteObject( registeredDealRequest );
                    }

                    Collection<Deal> childrenDeals = registeredDeal.getDeals();
                    if ( childrenDeals != null && !childrenDeals.isEmpty() )
                    {
                        for ( Deal cDeal : childrenDeals )
                        {
                            Deal registeredCDeal = ( Deal ) uow.registerObject( cDeal );
                            DealRequest cDealRequest = registeredCDeal.getDealRequest();
                            if ( cDealRequest != null )
                            {
                                DealRequest registeredCDealRequest = ( DealRequest ) uow.registerObject( cDealRequest );
                                registeredCDealRequest.setDeal( null );
                                registeredCDeal.setDealRequest( null );
                                uow.deleteObject( registeredCDealRequest );
                            }

                            uow.deleteObject( registeredCDeal );
                        }
                    }
                    uow.deleteObject( registeredDeal );
                }
            }
            uow.deleteObject( registeredOrder );
        }
        catch ( Exception e )
        {
            log.error( "RDF.deleteOrder : Exception. order=" + order, e );
        }
    }

    private long commitTransaction( UnitOfWork uow, String context )
    {
        long t0 = System.currentTimeMillis();
        long elapsed = 0;
        try
        {
            uow.commit();
            elapsed = System.currentTimeMillis() - t0;
            long sleepInterval = purgeConfigMBean.getBatchSleepInterval();
            if ( sleepInterval > 0 )
            {
                Thread.sleep( sleepInterval );
            }
        }
        catch ( Exception e )
        {
            log.error( "RDF.commitTransaction : Exception on " + context, e );
        }
        return elapsed;
    }

    private class OrderPurgeWorker implements Runnable
    {
        Collection<Order> orders;
        IdcDate cutoffDate;

        public OrderPurgeWorker( Collection<Order> coll, IdcDate date )
        {
            this.orders = coll;
            this.cutoffDate = date;
        }

        public void run()
        {
            try
            {
                long batchStartTime = System.currentTimeMillis();
                UnitOfWork uow = PersistenceFactory.newSession().acquireUnitOfWork();
                uow.setProperty( ClusterCommitEventAdapterC.MULTI_APP, ClusterCommitEventAdapterC.MULTI_APP_DISABLED );
                int deletedCount = 0;
                for ( Order anOrder : orders )
                {
                    totalOrdersCount.incrementAndGet();
                    if ( shouldDeleteOrder( anOrder, cutoffDate ) )
                    {
                        deletedCount++;
                        deletedOrdersCount.incrementAndGet();
                        deleteOrder( anOrder, uow );
                        State state = anOrder.getState();
                        String stateName = state != null ? state.getShortName() : null;
                        String clsfName = anOrder.getOrderClassification() != null ? anOrder.getOrderClassification().getShortName() : null;
                        log.info( new StringBuilder( 300 ).append( "RDF.deleteOldOrders : Deleting order state=" ).append( stateName )
                                .append( ",tid/orderId=" ).append( anOrder.getTransactionId() ).append( '/' ).append( anOrder.getOrderId() )
                                .append( ",createdDate=" ).append( anOrder.getCreatedDate() ).append( ",clsf=" ).append( clsfName ).toString() );

                    }
                    lastOrderCreatedDate = anOrder.getCreatedDate();
                }

                if ( deletedCount > 0 )
                {
                    int batchNum = ordersBatchCount.incrementAndGet();
                    long dt = commitTransaction( uow, "deleteOldOrders" );
                    long now = System.currentTimeMillis();
                    log.info( new StringBuilder( 400 ).append( "RDF.deleteOldOrders : Stats for batchNum=" ).append( batchNum )
                            .append( ",batchTime=" ).append( now - batchStartTime ).append( ",totalCount=" )
                            .append( totalOrdersCount.get() ).append( ",handledTotal=" ).append( deletedOrdersCount.get() )
                            .append( ",commitTime=" ).append( dt ).append( ",deletionCount=" ).append( deletedCount )
                            .append( ",avgBatchTime=" ).append( ( now - ordersStartTime ) / batchNum )
                            .append( ",lastOrderCreatedDate=" ).append( lastOrderCreatedDate ).toString() );
                }
            }
            catch ( Exception e )
            {
                log.error( "OPW.run : Exception while running the order purge worker=", e );
            }
        }
    }


}
