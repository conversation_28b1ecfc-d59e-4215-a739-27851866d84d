package com.integral.finance.creditLimit.handler;

// Copyright (c) 2020 Integral Development Corp.  All rights reserved.

import com.integral.exception.IdcException;
import com.integral.finance.creditLimit.CreditNotificationManagerC;
import com.integral.finance.creditLimit.CreditWorkflowMessage;
import com.integral.finance.creditLimit.admin.CreditAdminServiceLoggerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.IdcUtilC;

public class CreditOverrideEmailCommitHandler implements MessageHandler, Runnable
{
    protected static Log log = LogFactory.getLog( CreditAdminServiceLoggerC.class );


    /**
     * Instance variable for credit override audit facade creation
     */
    private CreditWorkflowMessage cwm;
    private Organization creditProviderOrg;
    private User sessionUser, overrideUser;
    private boolean warmup;

    private CreditOverrideEmailCommitHandler()
    {
    }

    public CreditOverrideEmailCommitHandler( CreditWorkflowMessage cwm, Organization creditProviderOrg, User sessionUser, User overrideUser, boolean isWarmup )
    {
        this();
        this.cwm = cwm;
        this.creditProviderOrg = creditProviderOrg;
        this.sessionUser = sessionUser;
        this.overrideUser = overrideUser;
        this.warmup = isWarmup;
    }

    /*
        Starts a transaction and persists the credit rejection audit event.
    */
    public Message handle( Message message ) throws IdcException
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( "COEH.handle.INFO : Audit credit override event. " + this );
        }
        try
        {
            if ( !warmup )
            {
                User contextUser = sessionUser != null ? sessionUser : creditProviderOrg.getDefaultDealingUser ();
                if ( contextUser != null )
                {
                    IdcUtilC.setSessionContextUser ( contextUser );
                }
                CreditNotificationManagerC.getInstance().sendCreditOverrideNotification( cwm, overrideUser );
            }
        }
        catch ( Exception e )
        {
            log.error( "COEH.handle ; Audit credit override event. " + this, e );
        }
        return message;
    }

    public void run()
    {
        try
        {
            handle( null );
        }
        catch ( Exception e )
        {
            log.error( "COEH.run : Exception. cpo=" + creditProviderOrg + ",overrideUser=" + overrideUser, e );
        }
    }

    public String toString()
    {
        return new StringBuilder( 100 ).append( "creditProvider=" ).append( creditProviderOrg )
                .append( "overrideUser=" ).append( overrideUser ).toString();
    }
}
