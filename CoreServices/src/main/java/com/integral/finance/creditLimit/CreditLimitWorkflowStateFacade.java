package com.integral.finance.creditLimit;

// Copyright (c) 2001-2005 Integral Development Corp. All rights reserved.

import com.integral.facade.EntityFacade;

import java.util.Collection;

/**
 * CreditLimitWorkflowStateFacade provides basic support for wrapping the credit limit workflow state of an credit entity.
 * It also provides helper methods to filter credit utilization events and credit audit events.
 *
 * <AUTHOR> Development Corp.
 */

public interface CreditLimitWorkflowStateFacade extends EntityFacade
{
    String CREDIT_INITIAL_STATE = "Initial";
    String CREDIT_FAILED_STATE = "CSFailed";
    String CREDIT_RESERVED_STATE = "CSReserved";
    String CREDIT_USED_STATE = "CSUsed";
    String CREDIT_UNDONE_STATE = "CSUndone";


    /**
     * Return the underlying credit workflow state.
     *
     * @return credit limit workflow state wrapped by the facade.
     */
    CreditLimitWorkflowState getCreditLimitWorkflowState();

    /**
     * Returns the list of credit utilization events associated with the credit limit workflow state due to
     * credit actions done on the credit entity.
     *
     * @return the list of credit utilization events related to various credit actions of credit entity or null
     */
    Collection<CreditUtilizationEvent> getCreditUtilizationEvents();

    /**
     * Sets the credit utilization events to the credit limit workflow state.
     */
    void setCreditUtilizationEvents( Collection<CreditUtilizationEvent> creditUtilEvents );

    /**
     * Returns true if credit is reserved on the credit entity.
     *
     * @return boolean indicating whether credit is reserved or not.
     */
    boolean isCreditReserved();

    /**
     * Sets the credit entity state to indicate that credit is reserved.
     */
    void setCreditReserved();

    /**
     * Returns true if credit is used on the credit entity.
     *
     * @return boolean indicating whether the credit is used or not.
     */
    boolean isCreditUsed();

    /**
     * Sets the credit entity state to indicate that credit is utilized.
     */
    void setCreditUsed();

    /**
     * Returns true if credit is removed on the credit entity. This can be either reserving the credit or using the credit.
     *
     * @return boolean indicating whether credit is removed or not.
     */
    boolean isCreditUndone();

    /**
     * Sets the credit entity state to indicate that either used credit or reserved credit is removed.
     */
    void setCreditUndone();
}
