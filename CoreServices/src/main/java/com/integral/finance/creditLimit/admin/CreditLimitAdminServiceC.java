package com.integral.finance.creditLimit.admin;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.exception.IdcDatabaseException;
import com.integral.exception.IdcIllegalArgumentException;
import com.integral.exception.IdcRuntimeException;
import com.integral.finance.account.AccountEnums;
import com.integral.finance.account.AccountManagementConstants;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.*;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationFactory;
import com.integral.finance.creditLimit.audit.CreditLimitAdminAuditManagerC;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationFactory;
import com.integral.finance.creditLimit.db.CreditDataQueryServiceFactory;
import com.integral.finance.creditLimit.functor.*;
import com.integral.finance.creditLimit.handler.CreateCreditUtilizationCommitHandlerC;
import com.integral.finance.creditLimit.handler.CreditUtilizationRecalculationHandlerC;
import com.integral.finance.creditLimit.model.PfeConfiguration;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.currency.CurrencyPairGroupC;
import com.integral.finance.marketData.MarketDataSet;
import com.integral.finance.trade.Tenor;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageEvent;
import com.integral.message.MessageHandler;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.NamespaceC;
import com.integral.session.IdcTransaction;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.user.UserC;
import com.integral.util.IdcUtilC;
import com.integral.util.StringUtilC;
import com.integral.workflow.StateC;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Vector;

/**
 * Implementation of CreditLimitAdminService which is used to create and maintain the credit related data for
 * the credit provider organization. This service works within the existing transaction and if it is not available, creates
 * new transaction and commit the transaction. Some of the methods such as setting of netting methodology and changing
 * the credit exposure etc, removes the credit data structure and create new credit limit rules and migrates the existing
 * credit utilization events.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitAdminServiceC implements CreditLimitAdminService
{
    protected Log log = LogFactory.getLog( CreditAdminServiceLoggerC.class );
    protected static Vector<Class> readOnlyClasses = new Vector<Class>( 16 );
    protected static Vector<Class> ruleSetTransactionReadOnlyClasses = new Vector<Class>( 25 );
    protected static Vector<Class> orgFunctionTransactionReadOnlyClasses = new Vector<Class>( 15 );
    protected static Vector<Class> cptyRuleTransactionReadOnlyClasses = new Vector<Class>( 25 );
    protected final static String INSUFFICIENT_PARAMETERS_ERROR = "Insufficient parameters";
    protected final static CreditLimitPFEAdminServiceC pfeService = new CreditLimitPFEAdminServiceC();
    protected final static CreditRelationshipAdminServiceC relationshipService = new CreditRelationshipAdminServiceC();

    /**
     * The maximum length of notification email address fields for validation purposes.
     */
    private static final int NOTIFICATION_EMAIL_ADDRESS_MAX_LENGTH = 256;

    static
    {
        setReadOnlyClasses();
        setOrgFunctionTransactionReadOnlyClasses();
        setRuleSetTransactionReadOnlyClasses();
        setCptyRuleTransactionReadOnlyClasses();
    }

    public void setCreditEnabled( Organization creditProviderOrg, boolean enable )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setCreditEnabled : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",enable=" ).append( enable ).toString() );
        }
        if ( !validateCreditProvider( creditProviderOrg, "setCreditEnabled" ) )
        {
            return;
        }
        IdcTransaction tx = null;

        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( creditProviderOrg );
            if ( clrs.isEnabled() == enable )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setCreditEnabled : Specified value is same as existing. org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",enable=" ).append( enable )
                        .append( ",existing=" ).append( clrs.isEnabled() ).toString() );
                return;
            }

            tx = startTransaction( ruleSetTransactionReadOnlyClasses );

            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setCreditEnabled : org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",enable=" ).append( enable )
                        .append( ",existing=" ).append( registeredClrs.isEnabled() ).append( ",newTx=" ).append( tx != null ).toString() );
            }

            registeredClrs.setEnabled( enable );

            // audit
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelCreditEnable( creditProviderOrg, enable );

            // send jms message updates.
            MessageEvent messageEvent = enable ? CreditMessageEvent.ENABLECREDIT : CreditMessageEvent.DISABLECREDIT;

            // add notification functors for clearing the credit enable/disable flag cache.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 1 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, messageEvent.getName() );
            getTransaction().addRemoteFunctor( CreditEnableNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( messageEvent.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setCreditEnabled : No remote notification functor registered. event=" + messageEvent.getName() );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setCreditEnabled : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setCreditEnabled : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    public void setCounterpartyCreditLimitRuleEnable( Organization cpo, Organization cco, boolean isEnabled )
    {
        getCreditRelationshipService().setCounterpartyCreditLimitRuleEnable(cpo, cco, isEnabled);
    }

    public void setCounterpartyCreditLimitRuleActive( Organization cpo, Organization cco, Character status )
    {
        getCreditRelationshipService().setCounterpartyCreditLimitRuleActive(cpo, cco, status);
    }

    public void setCounterpartyCreditLimitRulesActive( Collection<CounterpartyCreditLimitRule> cclrs, Character status )
    {
        getCreditRelationshipService().setCounterpartyCreditLimitRulesActive(cclrs, status);
    }

    public void setCreditEnabled( Organization creditProviderOrg, TradingParty creditCpty, boolean enable )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setCreditEnabled : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",enable=" ).append( enable ).toString() );
        }

        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "setCreditEnabled" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            Collection<CounterpartyCreditLimitRule> cclrs = CreditUtilC.getAllCounterpartyCreditLimitRulesForTradingParty( creditProviderOrg, creditTp, false );
            if ( cclrs == null || cclrs.size() != 2 )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setCreditEnabled : No counterparty credit limit rule for org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",tp=" ).append( creditCpty.getShortName() ).append( ",cclrs=" ).append( cclrs ).toString() );
                return;
            }
            CounterpartyCreditLimitRule orgRule = CreditUtilC.getOrgLevelCounterpartyCreditLimitRule( cclrs );
            CounterpartyCreditLimitRule tpRule = CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule( cclrs );
            if ( orgRule.isEnabled() == enable && tpRule.isEnabled() == enable )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setCreditEnabled : Specified value is same as existing. org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",tp=" ).append( creditTp.getShortName() )
                        .append( ",enable=" ).append( enable ).append( ",existing=" ).append( orgRule.isEnabled() ).toString() );
                return;
            }
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );

            CounterpartyCreditLimitRule registeredOrgCclr = ( CounterpartyCreditLimitRule ) orgRule.getRegisteredObject();
            CounterpartyCreditLimitRule registeredTpCclr = ( CounterpartyCreditLimitRule ) tpRule.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setCreditEnabled : org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",tp=" ).append( creditTp.getShortName() )
                        .append( ",enable=" ).append( enable ).append( ",existing=" ).append( orgRule.isEnabled() )
                        .append( ",new tx=" ).append( tx != null ).toString() );
            }

            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty );
            boolean isCptyOrgLevel = false;
            if ( cclr != null )
            {
                isCptyOrgLevel = ( cclr.getTradingParty() == null );
            }
            else
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setCreditEnabled : No cpty rule. creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).toString() );
            }

            registeredOrgCclr.setEnabled( enable );
            registeredTpCclr.setEnabled( enable );

            // audit
            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelCreditEnable( creditProviderOrg, isCptyOrgLevel, creditTp, creditTp.getLegalEntityOrganization(), enable );

            // send jms message updates.
            MessageEvent messageEvent = enable ? CreditMessageEvent.ENABLECREDIT : CreditMessageEvent.DISABLECREDIT;

            // add notification functors for clearing the credit enable/disable flag cache.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 2 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, creditTp.getLegalEntityOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, messageEvent.getName() );
            getTransaction().addRemoteFunctor( CreditEnableNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( messageEvent.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setCreditEnabled : No remote notification functor registered. event=" + messageEvent.getName() );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setCreditEnabled : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( " took ms=" )
                        .append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setCreditEnabled : org=" + creditProviderOrg.getShortName() + ",creditCpty=" + creditCpty, e );
            CreditUtilC.releaseTransaction(tx);
        }
    }


    public String getExternalCreditLimitProvider( Organization creditProviderOrg, TradingParty creditCpty )
    {
        try
        {
            if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "getExternalCreditLimitProvider" ) )
            {
                return null;
            }
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty, true );
            if ( cptyRule == null )
            {
                log.info( "CLAS.getExternalCreditLimitProvider : No cpty rule for org=" + creditProviderOrg );
                return null;
            }
            return cptyRule.getExternalCreditLimitProvider();
        }
        catch ( Exception e )
        {
            log.warn("CLAS.getExternalCreditLimitProvider : Error getting external credit limit provider for creditProviderOrg=" + creditProviderOrg + ",creditCpty=" + creditCpty, e);
        }
        return null;
    }
    
    public boolean isExternalCreditEnabled( Organization creditProviderOrg, TradingParty creditCpty )
    {
        try
        {
            if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "isExternalCreditEnabled" ) )
            {
                return false;
            }
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty, true );
            if ( cptyRule == null )
            {
                log.info( "CLAS.isExternalCreditEnabled : No cpty rule for org=" + creditProviderOrg );
                return false;
            }
            return cptyRule.isExternalCreditEnabled();
        }
        catch ( Exception e )
        {
            log.warn("CLAS.isExternalCreditEnabled : Error getting external credit enabled for creditProviderOrg=" + creditProviderOrg + ",creditCpty=" + creditCpty, e);
        }
        return false;
    }

    public CreditUtilizationCalculator getNettingMethodology( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification existingClsf )
    {
        if ( existingClsf == null )
        {
            log.info( new StringBuilder( 200 ).append( "CLAS.getNettingMethodology : Parameters not specified. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",existingClsf=" ).append( existingClsf ).toString() );
            throw new IdcIllegalArgumentException( "Parameters not specified" );
        }

        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "getNettingMethodology" ) )
        {
            return null;
        }
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty );
            if ( cptyRule == null )
            {
                log.info( "CLAS.getNettingMethodology : No cpty rule for org=" + creditProviderOrg );
                return null;
            }

            Collection<CreditLimitRule> rules = new ArrayList<CreditLimitRule>( cptyRule.getChildrenRules() );
            for ( CreditLimitRule clr : rules )
            {
                if ( existingClsf.isSameAs( clr.getClassification() ) )
                {
                    return clr.getCreditUtilizationCalculator();
                }
            }
            log.info( "CLAS.getNettingMethodology : No credit limit rule with the clsf=" + existingClsf );
        }
        catch ( Exception e )
        {
            log.warn("CLAS.getNettingMethodology : Error getting netting methodology for creditProviderOrg=" + creditProviderOrg + ",creditCpty=" + creditCpty, e);
        }
        return null;
    }

    public void setNettingMethodology( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification existingClsf, CreditUtilizationCalculator calc )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setNettingMethodology : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",calc=" ).append( calc ).toString() );
        }
        if ( existingClsf == null && calc == null )
        {
            log.warn( "CLAS.setNettingMethodology : Parameters not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( existingClsf != null && calc != null && !CreditUtilC.validateCreditUtilizationCalculator( calc, existingClsf ) )
        {
            log.warn( "CLAS.setNettingMethodology : Not a matching calculator. calc=" + calc + ",existingClsf=" + existingClsf );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_NETTING_CALCULATOR_ERROR_CODE, calc );
        }

        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "setNettingMethodology" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setNettingMethodology : No cpty rule. calc=" )
                        .append( calc ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",existingClsf=" ).append( existingClsf.getShortName() ).toString() );
                return;
            }

            boolean isDailyNetting = false;
            if ( existingClsf.isSameAs( CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION ) )
            {
                isDailyNetting = true;
            }
            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            if ( calc != null )
            {
                existingClsf = CreditUtilC.getCreditLimitClassification( calc );
            }
            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cptyRule, existingClsf );
            if ( clr == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setNettingMethodology : No credit limit rule found in cpty rule=" )
                        .append( cptyRule ).append( ",creditProviderOrg=" ).append( creditProviderOrg ).append( ",existingClsf=" ).append( existingClsf ).toString() );
                return;
            }

            CreditUtilizationCalculator existingCalc = clr.getCreditUtilizationCalculator();
            if ( ( calc == null && existingCalc == null ) || calc != null && calc.isSameAs( existingCalc ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setNettingMethodology : Same as existing calculator. calc=" )
                        .append( calc ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",existingClsf=" ).append( existingClsf.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitOrgFunctionC.class );
            addReadOnlyClass( tx, CreditLimitRuleSetC.class );
            CreditLimitRule registeredClr = ( CreditLimitRule ) clr.getRegisteredObject();
            if ( calc != null )
            {
                calc = ( CreditUtilizationCalculator ) calc.getRegisteredObject();
            }

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setNettingMethodology : cptyRule=" )
                        .append( cptyRule ).append( ",creditLimitRule=" ).append( clr ).append( ",rules=" ).append( cptyRule.getChildrenRules() )
                        .append( ",new tx=" ).append( tx != null ).toString() );
            }

            // set the calculator
            CreditUtilizationCalculator oldCalc = registeredClr.getCreditUtilizationCalculator();
            registeredClr.setCreditUtilizationCalculator( calc );
            if ( calc != null )
            {
                registeredClr.setAllowNetting( CreditUtilC.isNettingCalculator( calc ) );
            }

            if ( isDailyNetting )
            {
                CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelDailyNettingMethodology( creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization(), existingCalc, calc );
            }
            else
            {
                CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelAggregateNettingMethodology( creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization(), existingCalc, calc );
            }

            // add a commit handler for recalculation of credit utilization.
            addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( clr ) );

            // add notification functors for clearing the credit utilization cache.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETCREDITUTILIZATIONCALCULATOR.getName() );
            propertiesMap.put( CreditLimitConstants.OLD_VALUE, oldCalc != null ? oldCalc.getShortName() : CreditLimitConstants.VALUE_NONE );
            propertiesMap.put( CreditLimitConstants.NEW_VALUE, calc != null ? calc.getShortName() : CreditLimitConstants.VALUE_NONE );
            if ( cptyRule.getTradingParty() != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cptyRule.getTradingParty().getGUID() );
            }
            getTransaction().addRemoteFunctor( CreditUtilizationCacheResetNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETCREDITUTILIZATIONCALCULATOR.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setNettingMethodology : No remote notification functor registered. event=" + CreditMessageEvent.SETCREDITUTILIZATIONCALCULATOR.getName() );
            }
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setNettingMethodology : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",existingClsf=" )
                        .append( existingClsf.getShortName() ).append( ",calc=" ).append( calc ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setNettingMethodology : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public CreditUtilizationCalculator getDefaultAggregateNettingMethodology( Organization creditProviderOrg )
    {
        if ( !validateCreditProvider( creditProviderOrg, "getDefaultAggregateNettingMethodology" ) )
        {
            return null;
        }
        try
        {
            return CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg ).getDefaultAggregateCreditUtilizationCalculator();
        }
        catch ( Exception e )
        {
            log.warn("CLAS.getDefaultAggregateNettingMethodology : Error getting default aggregate netting methodology for creditProviderOrg=" + creditProviderOrg, e);
        }
        return null;
    }

    public void setDefaultAggregateNettingMethodology( Organization creditProviderOrg, CreditUtilizationCalculator nettingCalc )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultAggregateNettingMethodology : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",nettingCalc=" ).append( nettingCalc ).toString() );
        }
        if ( !validateCreditProvider( creditProviderOrg, "setDefaultAggregateNettingMethodology" ) )
        {
            return;
        }
        if ( nettingCalc != null && !CreditUtilC.validateCreditUtilizationCalculator( nettingCalc, CreditLimitConstants.GROSS_NOTIONAL_CLASSIFICATION ) )
        {
            log.warn( "CLAS.setDefaultAggregateNettingMethodology : Not a matching calculator. calc=" + nettingCalc );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_NETTING_CALCULATOR_ERROR_CODE, nettingCalc );
        }

        IdcTransaction tx = null;
        try
        {
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg );
            CreditUtilizationCalculator existingAggregateCalc = orgFunc.getDefaultAggregateCreditUtilizationCalculator();
            if ( ( nettingCalc == null && existingAggregateCalc == null ) || ( existingAggregateCalc != null && existingAggregateCalc.isSameAs( nettingCalc ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setDefaultAggregateNettingMethodology : Same as existing calculator. calc=" )
                        .append( nettingCalc ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",existingAggregateCalc=" ).append( existingAggregateCalc ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitRuleSetC.class );

            CreditLimitOrgFunction registeredOrgFunc = ( CreditLimitOrgFunction ) orgFunc.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultAggregateNettingMethodology : org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",nettingCalc=" ).append( nettingCalc )
                        .append( ",existingAggregate" ).append( existingAggregateCalc )
                        .append( ",newTx=" ).append( tx != null ).toString() );
            }

            registeredOrgFunc.setDefaultAggregateCreditUtilizationCalculator( nettingCalc != null ? ( CreditUtilizationCalculator ) nettingCalc.getRegisteredObject() : null );

            // audit
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelAggregateNettingMethodology( creditProviderOrg, existingAggregateCalc, nettingCalc );

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultAggregateNettingMethodology : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setDefaultAggregateNettingMethodology : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public CreditUtilizationCalculator getDefaultDailyNettingMethodology( Organization creditProviderOrg )
    {
        if ( !validateCreditProvider( creditProviderOrg, "getDefaultDailyNettingMethodology" ) )
        {
            return null;
        }
        try
        {
            return CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg ).getDefaultDailyCreditUtilizationCalculator();
        }
        catch ( Exception e )
        {
            log.warn("CLAS.getDefaultDailyNettingMethodology : Error getting default daily netting methodology for creditProviderOrg=" + creditProviderOrg, e);
        }
        return null;
    }

    public void setDefaultDailyNettingMethodology( Organization creditProviderOrg, CreditUtilizationCalculator nettingCalc )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultDailyNettingMethodology : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",nettingCalc=" ).append( nettingCalc ).toString() );
        }
        if ( !validateCreditProvider( creditProviderOrg, "setDefaultDailyNettingMethodology" ) )
        {
            return;
        }
        if ( nettingCalc != null && !CreditUtilC.validateCreditUtilizationCalculator( nettingCalc, CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION ) )
        {
            log.warn( "CLAS.setDefaultDailyNettingMethodology : Not a matching calculator. calc=" + nettingCalc );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_NETTING_CALCULATOR_ERROR_CODE, nettingCalc );
        }

        IdcTransaction tx = null;
        try
        {
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg );
            CreditUtilizationCalculator existingDailyCalc = orgFunc.getDefaultDailyCreditUtilizationCalculator();
            if ( ( nettingCalc == null && existingDailyCalc == null ) || ( existingDailyCalc != null && existingDailyCalc.isSameAs( nettingCalc ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setDefaultDailyNettingMethodology : Same as existing calculator. calc=" )
                        .append( nettingCalc ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",existingDailyCalc=" ).append( existingDailyCalc ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitRuleSetC.class );

            CreditLimitOrgFunction registeredOrgFunc = ( CreditLimitOrgFunction ) orgFunc.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultDailyNettingMethodology : org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",nettingCalc=" ).append( nettingCalc )
                        .append( ",existingDailyCalc" ).append( existingDailyCalc )
                        .append( ",newTx=" ).append( tx != null ).toString() );
            }

            registeredOrgFunc.setDefaultDailyCreditUtilizationCalculator( nettingCalc != null ? ( CreditUtilizationCalculator ) nettingCalc.getRegisteredObject() : null );

            // audit
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelDailyNettingMethodology( creditProviderOrg, existingDailyCalc, nettingCalc );

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultDailyNettingMethodology : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setDefaultDailyNettingMethodology : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public Currency getDefaultLimitCurrency( Organization creditProviderOrg )
    {
        if ( !validateCreditProvider( creditProviderOrg, "getDefaultLimitCurrency" ) )
        {
            return null;
        }
        try
        {
            return CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg ).getCreditLimitCurrency();
        }
        catch ( Exception e )
        {
            log.warn("CLAS.getDefaultLimitCurrency : Error getting default limit currency for creditProviderOrg=" + creditProviderOrg, e);
        }
        return null;
    }

    public void setDefaultLimitCurrency( Organization creditProviderOrg, Currency ccy )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append("CLAS.setDefaultLimitCurrency : Begin. creditProviderOrg=")
                    .append(creditProviderOrg).append( ",ccy=" ).append( ccy ).toString() );
        }
        if ( !validateCreditProvider(creditProviderOrg, "setDefaultLimitCurrency") )
        {
            return;
        }
        if ( ccy == null )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultLimitCurrency : Invalid currency=" )
                    .append( ccy ).toString() );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_LIMIT_CURRENCY_ERROR_CODE, ccy );
        }

        IdcTransaction tx = null;
        try
        {
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg );
            Currency existingLimitCcy = orgFunc.getCreditLimitCurrency();
            if ( ccy.isSameAs( existingLimitCcy ) )
            {
                log.info( new StringBuilder( 200 ).append("CLAS.setDefaultLimitCurrency : Same as existing currency. ccy=")
                        .append(ccy).append(",creditProviderOrg=").append( creditProviderOrg.getShortName() )
                        .append(",existingLimitCcy=").append( existingLimitCcy ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitRuleSetC.class );

            CreditLimitOrgFunction registeredOrgFunc = ( CreditLimitOrgFunction ) orgFunc.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append("CLAS.setDefaultLimitCurrency : org=")
                        .append(creditProviderOrg.getShortName()).append(",ccy=").append( ccy )
                        .append(",existingCcy=").append(existingLimitCcy).append( ",newTx=" ).append( tx != null ).toString() );
            }

            Currency registeredCcy = ( Currency ) ccy.getRegisteredObject();
            registeredOrgFunc.setCreditLimitCurrency(registeredCcy);

            //audit
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelLimitCurrency(creditProviderOrg, existingLimitCcy, ccy);
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append("CLAS.setDefaultLimitCurrency : End. creditProviderOrg=")
                        .append(creditProviderOrg.getShortName()).append( " took ms=" ).append(System.currentTimeMillis() - t0).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setDefaultLimitCurrency : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public void setNotificationPercentage( Organization creditProviderOrg, double percentage )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append("CLAS.setNotificationPercentage : Begin. creditProviderOrg=")
                    .append(creditProviderOrg).append( ",percentage=" ).append( percentage ).toString() );
        }
        if ( !validateCreditProvider(creditProviderOrg, "setNotificationPercentage") )
        {
            return;
        }
        if ( !CreditUtilC.validateUtilizationPercentage( percentage ) )
        {
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_NOTIFICATION_PERCENTAGE_ERROR_CODE, percentage );
        }

        CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet(creditProviderOrg);

        IdcTransaction tx = null;
        try
        {
            Double existingPercentage = clrs.getNotificationPercentage();
            if ( existingPercentage != null && existingPercentage.equals ( percentage ) )
            {
                log.info(new StringBuilder(200).append("CLAS.setNotificationPercentage : New value is same as existing value. org=")
                        .append(creditProviderOrg.getShortName()).append(",percentage=").append(percentage)
                        .append(",existing=").append(existingPercentage).toString());
                return;
            }

            tx = startTransaction( ruleSetTransactionReadOnlyClasses );
            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append("CLAS.setNotificationPercentage : org=")
                        .append(creditProviderOrg.getShortName()).append(",percentage=").append( percentage )
                        .append(",existing=").append(existingPercentage).append( ",new tx=" ).append( tx != null ).toString() );
            }
            registeredClrs.setNotificationPercentage(percentage);
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelNotificationPercentage(creditProviderOrg, existingPercentage, registeredClrs.getNotificationPercentage());

            CreditUtilC.endTransaction( tx );
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append("CLAS.setNotificationPercentage : End. creditProviderOrg=")
                        .append(creditProviderOrg.getShortName()).append(",percentage=").append(percentage).append( " took ms=" )
                        .append(System.currentTimeMillis() - t0).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setNotificationPercentage : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    public void setNotificationPercentage( Organization creditProviderOrg, TradingParty creditCpty, double percentage )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setNotificationPercentage : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append(",creditCpty=").append(creditCpty).append( ",percentage=" ).append( percentage ).toString() );
        }
        if ( !validateCreditCounterparty(creditProviderOrg, creditCpty, "setNotificationPercentage") )
        {
            return;
        }
        if ( !CreditUtilC.validateUtilizationPercentage( percentage ) )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setNotificationPercentage : Invalid notification percentage=" )
                    .append( percentage ).toString() );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_NOTIFICATION_PERCENTAGE_ERROR_CODE, percentage );
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty(creditCpty, creditProviderOrg);
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(creditProviderOrg, creditTp);
            if ( cclr == null )
            {
                log.warn( "CLAS.setNotificationPercentage : No counterparty credit limit rule for org=" + creditProviderOrg.getShortName() + ",tp=" + creditCpty.getShortName() );
                return;
            }
            Double existingPercentage = cclr.getNotificationPercentage();
            if ( existingPercentage != null && existingPercentage.equals ( percentage ) )
            {
                log.info(new StringBuilder(200).append("CLAS.setNotificationPercentage : New value is same as existing value. org=")
                        .append(creditProviderOrg.getShortName()).append(",tp=").append(creditTp.getShortName()).append(",percentage=").append(percentage)
                        .append(",existing=").append(existingPercentage).append(",new tx=").append(tx != null).toString());
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );

            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cclr.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setNotificationPercentage : org=" )
                        .append( creditProviderOrg.getShortName() ).append(",tp=").append(creditTp.getShortName())
                        .append(",percentage=").append( percentage ).append(",existing=")
                        .append(existingPercentage).append( ",new tx=" ).append( tx != null ).toString() );
            }

            registeredCclr.setNotificationPercentage(percentage);
            boolean isCptyOrgLevel = cclr.getTradingParty() == null;
            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelNotificationPercentage(creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization(), existingPercentage, registeredCclr.getNotificationPercentage());

            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setNotificationPercentage : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append(",creditCpty=").append(creditCpty.getShortName()).append( ",percentage=" )
                        .append(percentage).append( " took ms=" ).append(System.currentTimeMillis() - t0).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setNotificationPercentage : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    public void setWarningPercentage( Organization creditProviderOrg, double percentage )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append("CLAS.setWarningPercentage : Begin. creditProviderOrg=")
                    .append(creditProviderOrg).append( ",percentage=" ).append( percentage ).toString() );
        }
        if ( !validateCreditProvider(creditProviderOrg, "setWarningPercentage") )
        {
            return;
        }
        if ( !CreditUtilC.validateUtilizationPercentage( percentage ) )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setWarningPercentage : Invalid warning percentage=" )
                    .append( percentage ).toString() );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_WARNING_PERCENTAGE_ERROR_CODE, percentage );
        }
        IdcTransaction tx = null;
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet(creditProviderOrg);
            Double existingPercentage = clrs.getWarningPercentage();
            if ( existingPercentage != null && existingPercentage == percentage )
            {
                log.info(new StringBuilder(200).append("CLAS.setWarningPercentage : New value is same as existing value. org=")
                        .append(creditProviderOrg.getShortName()).append(",percentage=").append(percentage)
                        .append(",existing=").append(existingPercentage).append(",new tx=").append(tx != null).toString());
                return;
            }

            tx = startTransaction( ruleSetTransactionReadOnlyClasses );

            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append("CLAS.setWarningPercentage : org=")
                        .append(creditProviderOrg.getShortName()).append(",percentage=").append( percentage )
                        .append(",existing=").append(registeredClrs.getWarningPercentage()).append( ",new tx=" ).append( tx != null ).toString() );
            }
            registeredClrs.setWarningPercentage(percentage);
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelWarningPercentage(creditProviderOrg, existingPercentage, registeredClrs.getWarningPercentage());

            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setWarningPercentage : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( ",percentage=" ).append( percentage ).append( " took ms=" )
                        .append( System.currentTimeMillis() - t0 ).toString() );
            }

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append("CLAS.setWarningPercentage : End. creditProviderOrg=")
                        .append(creditProviderOrg.getShortName()).append( ",percentage=" ).append(percentage).append( " took ms=" )
                        .append(System.currentTimeMillis() - t0).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setWarningPercentage : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    public void setWarningPercentage( Organization creditProviderOrg, TradingParty creditCpty, double percentage )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setWarningPercentage : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append(",creditCpty=").append(creditCpty).append( ",percentage=" ).append( percentage ).toString() );
        }
        if ( !validateCreditCounterparty(creditProviderOrg, creditCpty, "setWarningPercentage") )
        {
            return;
        }
        if ( !CreditUtilC.validateUtilizationPercentage( percentage ) )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setWarningPercentage : Invalid warning percentage=" )
                    .append( percentage ).toString() );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_WARNING_PERCENTAGE_ERROR_CODE, percentage );
        }
        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(creditProviderOrg, creditTp);
            if ( cclr == null )
            {
                log.warn("CLAS.setWarningPercentage : No counterparty credit limit rule for org=" + creditProviderOrg.getShortName() + ",tp=" + creditCpty.getShortName());
                return;
            }
            Double existingPercentage = cclr.getWarningPercentage();
            if ( existingPercentage != null && existingPercentage == percentage )
            {
                log.info(new StringBuilder(200).append("CLAS.setWarningPercentage : New value is same as existing value. org=")
                        .append(creditProviderOrg.getShortName()).append(",tp=").append(creditTp.getShortName()).append(",percentage=").append(percentage)
                        .append(",existing=").append(existingPercentage).append(",new tx=").append(tx != null).toString());
                return;
            }
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );

            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cclr.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setWarningPercentage : org=" )
                        .append( creditProviderOrg.getShortName() ).append(",tp=").append(creditTp.getShortName())
                        .append(",percentage=").append( percentage ).append(",existing=")
                        .append(existingPercentage).append( ",new tx=" ).append( tx != null ).toString() );
            }

            registeredCclr.setWarningPercentage(percentage);
            boolean isCptyOrgLevel = cclr.getTradingParty() == null;
            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelWarningPercentage(creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization(), existingPercentage, registeredCclr.getWarningPercentage());

            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setWarningPercentage : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append(",creditCpty=").append(creditCpty.getShortName()).append( ",percentage=" )
                        .append(percentage).append( " took ms=" ).append(System.currentTimeMillis() - t0).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setWarningPercentage : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    public void setSuspensionPercentage( Organization creditProviderOrg, double percentage )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append("CLAS.setSuspensionPercentage : Begin. creditProviderOrg=")
                    .append(creditProviderOrg).append( ",percentage=" ).append( percentage ).toString() );
        }
        if ( !validateCreditProvider(creditProviderOrg, "setSuspensionPercentage") )
        {
            return;
        }
        if ( !CreditUtilC.validateUtilizationPercentage( percentage ) )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setSuspensionPercentage : Invalid suspension percentage=" )
                    .append( percentage ).toString() );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_SUSPENSION_PERCENTAGE_ERROR_CODE, percentage );
        }
        IdcTransaction tx = null;
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet(creditProviderOrg);
            Double existingPercentage = clrs.getSuspensionPercentage();
            if ( existingPercentage != null && existingPercentage == percentage )
            {
                log.info(new StringBuilder(200).append("CLAS.setSuspensionPercentage : New value is same as existing value. org=")
                        .append(creditProviderOrg.getShortName()).append(",percentage=").append(percentage)
                        .append(",existing=").append(existingPercentage).toString());
                return;
            }

            tx = startTransaction( ruleSetTransactionReadOnlyClasses );
            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug(new StringBuilder(200).append("CLAS.setSuspensionPercentage : org=")
                        .append(creditProviderOrg.getShortName()).append(",percentage=").append(percentage)
                        .append(",existing=").append(existingPercentage).append(",new tx=").append(tx != null).toString());
            }
            registeredClrs.setSuspensionPercentage( percentage );
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelSuspensionPercentage(creditProviderOrg, existingPercentage, registeredClrs.getSuspensionPercentage());

            // add notification functors for clearing the credit enable/disable flag cache.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 1 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put(CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETSUSPENSIONPERCENTAGE.getName());

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETSUSPENSIONPERCENTAGE.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setSuspensionPercentage : No remote notification functor registered. event=" + CreditMessageEvent.SETSUSPENSIONPERCENTAGE.getName() );
            }

            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append("CLAS.setSuspensionPercentage : End. creditProviderOrg=")
                        .append(creditProviderOrg.getShortName()).append( ",percentage=" ).append(percentage).append( " took ms=" )
                        .append(System.currentTimeMillis() - t0).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setSuspensionPercentage : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    public void setSuspensionPercentage( Organization creditProviderOrg, TradingParty creditCpty, double percentage )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setSuspensionPercentage : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append(",creditCpty=").append(creditCpty).append( ",percentage=" ).append( percentage ).toString() );
        }
        if ( !validateCreditCounterparty(creditProviderOrg, creditCpty, "setSuspensionPercentage") )
        {
            return;
        }
        if ( !CreditUtilC.validateUtilizationPercentage( percentage ) )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setSuspensionPercentage : Invalid suspension percentage=" )
                    .append( percentage ).toString() );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_SUSPENSION_PERCENTAGE_ERROR_CODE, percentage );
        }
        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(creditProviderOrg, creditTp);
            if ( cclr == null )
            {
                log.warn( "CLAS.setSuspensionPercentage : No counterparty credit limit rule for org=" + creditProviderOrg.getShortName() + ",tp=" + creditCpty.getShortName() );
                return;
            }
            Double existingPercentage = cclr.getSuspensionPercentage();
            if ( existingPercentage != null && existingPercentage == percentage )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setSuspensionPercentage : New value is same as existing value. org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",tp=" ).append( creditTp.getShortName() ).append(",percentage=").append(percentage)
                        .append(",existing=").append( existingPercentage ).append(",new tx=").append( tx != null ).toString() );
                return;
            }
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );

            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cclr.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setSuspensionPercentage : org=" )
                        .append( creditProviderOrg.getShortName() ).append(",tp=").append(creditTp.getShortName())
                        .append(",percentage=").append( percentage ).append(",existing=")
                        .append(existingPercentage).append( ",new tx=" ).append( tx != null ).toString() );
            }

            registeredCclr.setSuspensionPercentage( percentage );
            boolean isCptyOrgLevel = cclr.getTradingParty() == null;
            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelSuspensionPercentage(creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization(), existingPercentage, registeredCclr.getSuspensionPercentage());

            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cclr.getTradingPartyOrganization().getGUID() );
            if ( cclr.getTradingParty() != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cclr.getTradingParty().getGUID() );
            }
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETSUSPENSIONPERCENTAGE.getName() );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETSUSPENSIONPERCENTAGE.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setSuspensionPercentage : No remote notification functor registered. event=" + CreditMessageEvent.SETSUSPENSIONPERCENTAGE.getName() );
            }

            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setSuspensionPercentage : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append(",creditCpty=").append(creditCpty.getShortName()).append( ",percentage=" )
                        .append(percentage).append( " took ms=" ).append(System.currentTimeMillis() - t0).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setSuspensionPercentage : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public void setStopOutPercentage( Organization creditProviderOrg, Double percentage )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append("CLAS.setStopOutPercentage : Begin. creditProviderOrg=")
                    .append(creditProviderOrg).append( ",percentage=" ).append( percentage ).toString() );
        }
        if ( !validateCreditProvider(creditProviderOrg, "setStopOutPercentage") )
        {
            return;
        }
        if ( percentage != null && !CreditUtilC.validateUtilizationPercentage( percentage ) )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setStopOutPercentage : Invalid stop-out percentage=" )
                    .append( percentage ).toString() );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_STOPOUT_PERCENTAGE_ERROR_CODE, percentage );
        }
        IdcTransaction tx = null;
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet(creditProviderOrg);
            Double existingPercentage = clrs.getStopOutPercentage ();
            if ( existingPercentage != null && existingPercentage == percentage )
            {
                log.info(new StringBuilder(200).append("CLAS.setStopOutPercentage : New value is same as existing value. org=")
                        .append(creditProviderOrg.getShortName()).append(",percentage=").append(percentage)
                        .append(",existing=").append(existingPercentage).toString());
                return;
            }

            tx = startTransaction( ruleSetTransactionReadOnlyClasses );
            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug(new StringBuilder(200).append("CLAS.setStopOutPercentage : org=")
                        .append(creditProviderOrg.getShortName()).append(",percentage=").append(percentage)
                        .append(",existing=").append(existingPercentage).append(",new tx=").append(tx != null).toString());
            }
            registeredClrs.setStopOutPercentage ( percentage );
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelStopOutPercentage(creditProviderOrg, existingPercentage, registeredClrs.getStopOutPercentage ());

            // add notification functors for clearing the credit enable/disable flag cache.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 1 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put(CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETSTOPOUTPERCENTAGE.getName());
            getTransaction().addRemoteFunctor( CreditStopOutNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETSTOPOUTPERCENTAGE.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setStopOutPercentage : No remote notification functor registered. event=" + CreditMessageEvent.SETSTOPOUTPERCENTAGE.getName() );
            }

            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append("CLAS.setStopOutPercentage : End. creditProviderOrg=")
                        .append(creditProviderOrg.getShortName()).append( ",percentage=" ).append(percentage).append( " took ms=" )
                        .append(System.currentTimeMillis() - t0).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setStopOutPercentage : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    public void setStopOutPercentage( Organization creditProviderOrg, TradingParty creditCpty, Double percentage )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setStopOutPercentage : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append(",creditCpty=").append(creditCpty).append( ",percentage=" ).append( percentage ).toString() );
        }
        if ( !validateCreditCounterparty(creditProviderOrg, creditCpty, "setStopOutPercentage") )
        {
            return;
        }
        if ( percentage != null && !CreditUtilC.validateUtilizationPercentage( percentage ) )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setStopOutPercentage : Invalid stop-out percentage=" )
                    .append( percentage ).toString() );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_STOPOUT_PERCENTAGE_ERROR_CODE, percentage );
        }
        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(creditProviderOrg, creditTp);
            if ( cclr == null )
            {
                log.warn( "CLAS.setStopOutPercentage : No counterparty credit limit rule for org=" + creditProviderOrg.getShortName() + ",tp=" + creditCpty.getShortName() );
                return;
            }
            Double existingPercentage = cclr.getStopOutPercentage ();
            if ( existingPercentage != null && existingPercentage == percentage )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setStopOutPercentage : New value is same as existing value. org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",tp=" ).append( creditTp.getShortName() ).append(",percentage=").append(percentage)
                        .append(",existing=").append( existingPercentage ).append(",new tx=").append( tx != null ).toString() );
                return;
            }
            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );

            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cclr.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setStopOutPercentage : org=" )
                        .append( creditProviderOrg.getShortName() ).append(",tp=").append(creditTp.getShortName())
                        .append(",percentage=").append( percentage ).append(",existing=")
                        .append(existingPercentage).append( ",new tx=" ).append( tx != null ).toString() );
            }

            registeredCclr.setStopOutPercentage ( percentage );
            boolean isCptyOrgLevel = cclr.getTradingParty() == null;
            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelStopOutPercentage(creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization(), existingPercentage, registeredCclr.getStopOutPercentage ());

            // add notification functors for updating stop-out percentage.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cclr.getTradingPartyOrganization().getGUID() );
            if ( cclr.getTradingParty() != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cclr.getTradingParty().getGUID() );
            }
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETSTOPOUTPERCENTAGE.getName() );
            getTransaction().addRemoteFunctor( CreditStopOutNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETSTOPOUTPERCENTAGE.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setStopOutPercentage : No remote notification functor registered. event=" + CreditMessageEvent.SETSTOPOUTPERCENTAGE.getName() );
            }

            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setStopOutPercentage : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append(",creditCpty=").append(creditCpty.getShortName()).append( ",percentage=" )
                        .append(percentage).append( " took ms=" ).append(System.currentTimeMillis() - t0).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setStopOutPercentage : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public void setNotificationEmailAddress( Organization creditProviderOrg, String email )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append("CLAS.setNotificationEmailAddress : Begin. creditProviderOrg=")
                    .append(creditProviderOrg).append(",email=").append( email ).toString() );
        }
        if ( !validateCreditProvider(creditProviderOrg, "setNotificationEmailAddress") )
        {
            return;
        }
        if ( email != null &&  ( !CreditUtilC.validateEmailIds( email ) || email.length () > NOTIFICATION_EMAIL_ADDRESS_MAX_LENGTH ) )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setNotificationEmailAddress : Invalid email address=" )
                    .append( email ).toString() );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_NOTIFICATION_EMAIL_ERROR_CODE, email );
        }
        IdcTransaction tx = null;
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet(creditProviderOrg);
            String existingEmail = clrs.getNotificationEmailAddress();
            if ( ( existingEmail == null && email == null ) || ( existingEmail != null && email != null && existingEmail.equals( email ) ) )
            {
                log.info( new StringBuilder( 200 ).append("CLAS.setNotificationEmailAddress : Same as existing value. org=")
                        .append(creditProviderOrg.getShortName()).append(",email=").append( email )
                        .append(",existing=").append(existingEmail).append( ",new tx=" ).append( tx != null ).toString() );
                return;
            }

            tx = startTransaction( ruleSetTransactionReadOnlyClasses );
            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug(new StringBuilder(200).append("CLAS.setNotificationEmailAddress : org=")
                        .append(creditProviderOrg.getShortName()).append(",email=").append(email)
                        .append(",existing=").append(existingEmail).append(",new tx=").append(tx != null).toString());
            }
            registeredClrs.setNotificationEmailAddress( email );
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelNotificationEmailAddress(creditProviderOrg, existingEmail, email);

            CreditUtilC.endTransaction(tx);

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append("CLAS.setNotificationEmailAddress : End. creditProviderOrg=")
                        .append(creditProviderOrg.getShortName()).append( ",email=" ).append(email).append( " took ms=" )
                        .append(System.currentTimeMillis() - t0).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setNotificationEmailAddress : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public void setNotificationEmailAddress( Organization creditProviderOrg, TradingParty creditCpty, String email )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append("CLAS.setNotificationEmailAddress : Begin. creditProviderOrg=")
                    .append(creditProviderOrg).append(",creditCpty=").append(creditCpty).append(",email=").append( email ).toString() );
        }
        if ( !validateCreditCounterparty(creditProviderOrg, creditCpty, "setNotificationEmailAddress") )
        {
            return;
        }
        if ( email != null && ( !CreditUtilC.validateEmailIds( email ) || email.length () > NOTIFICATION_EMAIL_ADDRESS_MAX_LENGTH ) )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setNotificationEmailAddress : Invalid email address=" )
                    .append( email ).toString() );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_NOTIFICATION_EMAIL_ERROR_CODE, email );
        }
        IdcTransaction tx = null;
        try
        {
            TradingParty creditTp = CounterpartyUtilC.getTradingParty(creditCpty, creditProviderOrg);
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(creditProviderOrg, creditTp);

            if ( cclr == null )
            {
                log.warn( "CLAS.setNotificationEmailAddress : No counterparty credit limit rule for org=" + creditProviderOrg.getShortName() + ",tp=" + creditCpty.getShortName() );
                return;
            }
            String existingEmail = cclr.getNotificationEmailAddress();
            if ( ( existingEmail == null && email == null ) || ( existingEmail != null && email != null && existingEmail.equals( email ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setNotificationEmailAddress : Same as existing value. org=" )
                        .append( creditProviderOrg.getShortName() ).append(",tp=").append(creditTp.getShortName())
                        .append(",email=").append( email ).append(",existing=")
                        .append(existingEmail).append( ",new tx=" ).append( tx != null ).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );

            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cclr.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug(new StringBuilder(200).append("CLAS.setNotificationEmailAddress : org=")
                        .append(creditProviderOrg.getShortName()).append(",tp=").append(creditTp.getShortName())
                        .append(",email=").append(email).append(",existing=")
                        .append(existingEmail).append(",new tx=").append(tx != null).toString());
            }

            registeredCclr.setNotificationEmailAddress( email );
            boolean isCptyOrgLevel = cclr.getTradingParty() == null;
            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelNotificationEmailAddress(creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization(), existingEmail, email);

            CreditUtilC.endTransaction(tx);

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setNotificationEmailAddress : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append(",creditCpty=").append(creditCpty.getShortName()).append( ",email=" )
                        .append(email).append( " took ms=" ).append(System.currentTimeMillis() - t0).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setNotificationEmailAddress : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public double getCreditLimit( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append("CLAS.getCreditLimit : Begin. creditProviderOrg=")
                    .append(creditProviderOrg).append( ",creditCpty=" ).append( creditCpty ).append(",clsf=").append( clsf ).toString() );
        }
        if ( clsf == null )
        {
            log.warn( "CLAS.getCreditLimit : Parameters not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }
        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "getCreditLimit" ) )
        {
            return 0.0;
        }
        try
        {
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(creditProviderOrg, creditCpty);
            if ( cclr == null )
            {
                log.warn("CLAS.getCreditLimit : No cpty rule found for org=" + creditProviderOrg.getShortName() + ",creditCpty=" + creditCpty.getShortName());
                return 0.0;
            }
            Collection<CreditLimitRule> rules = CreditUtilC.getCreditLimitRules( cclr );
            for ( CreditLimitRule clr : rules )
            {
                if ( clsf.isSameAs( clr.getClassification() ) )
                {
                    return clr.getLimitAmount();
                }
            }
            log.info(new StringBuilder(200).append("CLAS.getCreditLimit : No credit limit rule with clsf=")
                    .append(clsf).append(",creditProviderOrg=").append(creditProviderOrg.getShortName())
                    .append(",creditCpty=").append(creditCpty.getShortName()).toString());
        }
        catch ( Exception e )
        {
            log.warn("CLAS.getCreditLimit : org=" + creditProviderOrg.getShortName() + ",creditCpty=" + creditCpty.getShortName(), e);
        }
        return 0.0;
    }

    public double getCreditLimit( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, IdcDate date )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.getCreditLimit : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append(",creditCpty=").append(creditCpty).append( ",clsf=" ).append( clsf )
                    .append(",date=").append( date ).toString() );
        }
        if ( clsf == null || date == null )
        {
            log.warn( "CLAS.getCreditLimit : Parameters not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }
        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "getCreditLimit" ) )
        {
            return 0.0;
        }
        try
        {
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty );
            if ( cclr == null )
            {
                log.warn( "CLAS.getCreditLimit : No counterparty credit limit rule found for org=" + creditProviderOrg.getShortName() + ",creditCpty=" + creditCpty.getShortName() );
                return 0.0;
            }
            Collection<CreditLimitRule> rules = CreditUtilC.getCreditLimitRules( cclr );
            for ( CreditLimitRule clr : rules )
            {
                if ( clsf.isSameAs( clr.getClassification() ) )
                {
                    if ( clr instanceof DailyCreditLimitRule )
                    {
                        DailyCreditLimitRule dclr = ( DailyCreditLimitRule ) clr;
                        CreditUtilization cu = CreditUtilizationManagerC.getInstance().getCreditUtilizationForDate( dclr, date );
                        if ( cu != null )
                        {
                            return cu.getLimit();
                        }
                        else
                        {
                            log.warn( "CLAS.getCreditLimit : No credit utilization found for org=" + creditProviderOrg.getShortName() + ",creditCpty=" + creditCpty.getShortName() );
                        }
                    }
                    else
                    {
                        return clr.getLimitAmount();
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.getCreditLimit : org=" + creditProviderOrg.getShortName(), e );
        }
        return 0.0;
    }

    public Currency getCreditLimitCurrency( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append("CLAS.getCreditLimitCurrency : Begin. creditProviderOrg=")
                    .append(creditProviderOrg).append( ",creditCpty=" ).append( creditCpty ).append(",clsf=").append( clsf ).toString() );
        }
        if ( clsf == null )
        {
            log.warn( "CLAS.getCreditLimitCurrency : Parameters not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }
        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "getCreditLimitCurrency" ) )
        {
            return null;
        }
        try
        {
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty );
            if ( cclr == null )
            {
                log.warn( "CLAS.getCreditLimitCurrency : No cpty rule found for org=" + creditProviderOrg.getShortName() + ",creditCpty=" + creditCpty.getShortName() );
                return CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg ).getCreditLimitCurrency();
            }

            Collection<CreditLimitRule> rules = CreditUtilC.getCreditLimitRules( cclr );
            for ( CreditLimitRule clr : rules )
            {
                if ( clsf.isSameAs( clr.getClassification() ) )
                {
                    return clr.getCurrency();
                }
            }
            log.info( new StringBuilder( 200 ).append( "CLAS.getCreditLimitCurrency : No credit limit rule with clsf=" )
                    .append( clsf.getShortName() ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                    .append( ",creditCpty=" ).append( creditCpty.getShortName() ).toString() );
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.getCreditLimitCurrency : org=" + creditProviderOrg.getShortName() + ",creditCpty=" + creditCpty.getShortName(), e );
        }
        return null;
    }

    public void setCreditLimit( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, double limit, Currency ccy )
    {
        setCreditLimit( creditProviderOrg, creditCpty, clsf, limit, ccy, true );
    }

    public void setCreditLimit( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, double limit, Currency ccy, boolean forceUpdateLimit )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setCreditLimit : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",clsf=" )
                    .append( clsf ).append( ",ccy=" ).append( ccy ).toString() );
        }
        if ( clsf == null || ccy == null )
        {
            log.warn( "CLAS.setCreditLimit : Parameters not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }
        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "setCreditLimit" ) )
        {
            return;
        }
        if ( !CreditUtilC.validateCreditLimit( limit ) )
        {
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_LIMIT_AMOUNT_ERROR_CODE, limit );
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(creditProviderOrg, creditTp);
            if ( cclr == null )
            {
                log.warn( "CLAS.setCreditLimit : No counterparty rule found for org=" + creditProviderOrg.getShortName() + ",creditCpty=" + creditCpty.getShortName() );
                return;
            }
            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cclr, clsf );
            if ( clr == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setCreditLimit : No credit limit rule for creditProviderOrg=" )
                        .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",cclr=" )
                        .append( cclr ).append( ",creditRules=" ).append( cclr.getChildrenRules() ).toString() );
                return;
            }

            final boolean integrationMode = CreditUtilC.getCreditMode( cclr ) == CreditLimit.CREDIT_INTEGRATION_MODE;
            double existingLimit = clr.getLimitAmount();
            boolean registerLimit = integrationMode && forceUpdateLimit;
            boolean sameLimit = existingLimit == limit;
            if ( sameLimit && !registerLimit )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setCreditLimit : Same as existing value. org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",tp=" ).append( creditCpty.getShortName() )
                        .append( ",newAmt=" ).append( limit ).append( ",existing=" ).append( existingLimit )
                        .append( ",ccy=" ).append( ccy ).append( ",existing=" ).append( clr.getCurrency() ).append( ",clsf=" )
                        .append( clsf.getShortName() ).append( ",new tx=" ).append( tx != null ).toString() );
                return;
            }
            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitOrgFunctionC.class );
            CreditLimitRule registeredClr = ( CreditLimitRule ) clr.getRegisteredObject();
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setCreditLimit : org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",tp=" ).append( creditCpty.getShortName() )
                        .append( ",newAmt=" ).append( limit ).append( ",existing=" ).append( existingLimit )
                        .append( ",ccy=" ).append( ccy ).append( ",existing=" ).append( registeredClr.getCurrency() ).append( ",clsf=" )
                        .append( clsf.getShortName() ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            Currency limitCcy = registeredClr.getCurrency();
            if ( limitCcy == null || limitCcy.isSameAs( ccy ) )
            {
                registeredClr.setLimitAmount( limitCcy != null ? limitCcy.round ( limit ) : limit );
                registeredClr.update();
                // if integration mode, reset the earmarked used amount.
                if ( integrationMode )
                {
                    Collection<CreditLimitRule> creditLimitRules = cclr.getChildrenRules();
                    for (CreditLimitRule creditLimitRule : creditLimitRules)
                    {
                        Collection<CreditUtilization> cus = creditLimitRule.getCreditUtilizations();
                        for ( CreditUtilization cu : cus )
                        {
                            if ( cu.getEarmarkedUsedAmount() != 0.0 )
                            {
                                if( CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationLookupSpacesEnabled(creditProviderOrg.getShortName()))
                                {
                                    cu.resetEarmarkedUsedAmount();
                                    CreditDataQueryServiceFactory.getCreditReferenceDataService().createOrUpdateCreditUtilization( cu );
                                }
                                else
                                {
                                    CreditUtilization registeredCu = (CreditUtilization) cu.getRegisteredObject();
                                    registeredCu.resetEarmarkedUsedAmount();
                                    registeredCu.update();
                                }
                            }
                        }
                    }
                }
            }
            else
            {
                // check the market data for conversion between current limit currency and new limit currency.
                CreditUtilC.releaseTransaction( tx );
                throw new IdcRuntimeException( new UnsupportedOperationException( "Credit currency modification is not supported." ) );
            }

            // audit
            boolean suppressAudit = registerLimit && sameLimit && CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean ().isUnchangedLimitUpdateAuditSuppressEnabled ( creditProviderOrg );
            if ( !suppressAudit )
            {
                boolean isCptyOrgLevel = cclr.getTradingParty () == null;
                CreditLimitAdminAuditManagerC.getInstance ().auditCounterpartyLevelCreditLimit ( creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization (), existingLimit, limit, registeredClr.getCreditUtilizationCalculator () );
            }

            if ( limit > existingLimit )
            {
                if ( log.isDebugEnabled() )
                {
                    log.debug( "CLAS.setCreditLimit.DEBUG : reset the notification date time since new limit is greater than existing limit. creditProviderOrg=" + creditProviderOrg.getShortName() );
                }
                CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cclr.getRegisteredObject();
                registeredCclr.setNotificationDate( null );
                registeredCclr.setWarningDate( null );
                registeredCclr.setSuspensionDate( null );
            }

            // add notification functors for clearing the credit enable/disable flag cache.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 1 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cclr.getTradingPartyOrganization().getGUID() );
            if ( cclr.getTradingParty() != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cclr.getTradingParty().getGUID() );
            }
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.UPDATELIMIT.getName() );
            if ( CreditLimit.CREDIT_INTEGRATION_MODE == CreditUtilC.getCreditMode( cclr ) )
            {
                getTransaction().addRemoteFunctor( CreditModeNotificationFunctorC.class.getName(), propertiesMap );
            }

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.UPDATELIMIT.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setCreditLimit : No remote notification functor registered. event=" + CreditMessageEvent.UPDATELIMIT.getName() );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setCreditLimit : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" )
                        .append( clsf.getShortName() ).append( ",ccy=" ).append( ccy ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setCreditLimit : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    public void setCreditLimit( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, Double limit, Currency ccy, IdcDate date )
    {
        setCreditLimit( creditProviderOrg, creditCpty, clsf, limit, ccy, date, true );
    }

    public void setCreditLimit( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, Double limit, Currency ccy, IdcDate date, boolean forceUpdateLimit )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setCreditLimit : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",clsf=" )
                    .append( clsf ).append( ",ccy=" ).append( ccy ).append( ",date=" ).append( date ).append( ",limit=" ).append( limit ).toString() );
        }
        if ( clsf == null || ccy == null || date == null || ( limit != null && limit < 0.0 ) )
        {
            log.warn( "CLAS.setCreditLimit : Parameters not specified. clsf=" + clsf + ",ccy=" + ccy + ",date=" + date + ",limit=" + limit );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }
        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "setCreditLimit" ) )
        {
            return;
        }
        if ( limit != null && !CreditUtilC.validateCreditLimit( limit ) )
        {
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_LIMIT_AMOUNT_ERROR_CODE, limit );
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditTp );
            if ( cclr == null )
            {
                log.warn( "CLAS.setCreditLimit : No credit limit ruleset found for org=" + creditProviderOrg.getShortName() );
                return;
            }
            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cclr, clsf );
            if ( clr == null )
            {
                log.warn( "CLAS.setCreditLimit : No credit limit rule found for org=" + creditProviderOrg.getShortName() + ",clsf=" + clsf );
                return;
            }
            if ( clr instanceof DailyCreditLimitRule )
            {
                DailyCreditLimitRule dclr = ( DailyCreditLimitRule ) clr;
                CreditUtilization cu = CreditUtilizationManagerC.getInstance ().getCreditUtilizationForDate ( dclr, date );
                if ( cu == null )
                {
                    if ( CreditLimitConfigurationFactory.getCreditConfigurationMBean ().isCreditUtilizationPersistenceSpacesEnabled ( creditProviderOrg.getShortName () ) )
                    {
                        CreditDataQueryServiceFactory.getCreditReferenceDataService ().createOrUpdateCreditLimit ( clr, date, limit );
                        cu = CreditUtilizationManagerC.getInstance ().getCreditUtilizationForDate ( dclr, date );
                    }
                    else
                    {
                        log.warn ( new StringBuilder ( 200 ).append ( "CLAS.setCreditLimit : No credit utilization for date. org=" )
                                .append ( creditProviderOrg.getShortName () ).append ( ",tp=" ).append ( creditCpty.getShortName () )
                                .append ( ",newAmt=" ).append ( limit ).append ( ",existing=" ).append ( clr.getLimitAmount () )
                                .append ( ",ccy=" ).append ( ccy ).append ( ",existing=" ).append ( clr.getCurrency () )
                                .append ( ",date=" ).append ( date ).append ( ",clsf=" ).append ( clsf.getShortName () ).append ( ",new tx=" ).append ( tx != null ).toString () );
                        return;
                    }

                }

                Double existingLimit = cu.getLimitAmount ();
                boolean registerLimit = CreditUtilC.getCreditMode ( cclr ) == CreditLimit.CREDIT_INTEGRATION_MODE && forceUpdateLimit;
                boolean sameLimit = (existingLimit == null && limit == null) || (existingLimit != null && limit != null && existingLimit.doubleValue () == limit.doubleValue ());
                if ( sameLimit )
                {

                    if ( ! registerLimit )
                    {
                        log.info ( new StringBuilder ( 200 ).append ( "CLAS.setCreditLimit : Same as existing value. Daily credit limit rule. org=" )
                                .append ( creditProviderOrg.getShortName () ).append ( ",tp=" ).append ( creditCpty.getShortName () )
                                .append ( ",newAmt=" ).append ( limit ).append ( ",existing=" ).append ( existingLimit )
                                .append ( ",date=" ).append ( date ).append ( ",ccy=" ).append ( ccy ).append ( ",existing=" )
                                .append ( cu.getCurrency () ).append ( ",clsf=" ).append ( clsf.getShortName () ).append ( ",new tx=" ).append ( tx != null ).toString () );
                        return;
                    }
                }

                tx = startTransaction ( readOnlyClasses );
                addReadOnlyClass ( tx, CreditLimitOrgFunctionC.class );
                CreditUtilization registeredCu = cu;
                CreditLimitRule registeredClr = ( CreditLimitRule ) clr.getRegisteredObject ();
                registeredClr.update ();
                if ( CreditLimitConfigurationFactory.getCreditConfigurationMBean ().isCreditUtilizationPersistenceOracleEnabled ( creditProviderOrg.getShortName () ) )
                {
                    if ( CreditLimitConfigurationFactory.getCreditConfigurationMBean ().isCreditUtilizationLookupSpacesEnabled ( creditProviderOrg.getShortName () ) )
                    {
                        CreditUtilization ocu = CreditDataQueryServiceFactory.getCreditDataQueryService ().getCreditUtilizationFromOracle ( cu );
                        if ( ocu != null )
                        {
                            registeredCu = ( CreditUtilization ) ocu.getRegisteredObject ();
                        }
                    }
                    else
                    {
                        registeredCu = ( CreditUtilization ) cu.getRegisteredObject ();
                    }
                }

                if ( log.isDebugEnabled () )
                {
                    log.debug ( new StringBuilder ( 200 ).append ( "CLAS.setCreditLimit : Daily credit limit rule. org=" )
                            .append ( creditProviderOrg.getShortName () ).append ( ",tp=" ).append ( creditCpty.getShortName () )
                            .append ( ",newAmt=" ).append ( limit ).append ( ",existing=" ).append ( existingLimit ).append ( ",date=" ).append ( date )
                            .append ( ",ccy=" ).append ( ccy ).append ( ",existing=" ).append ( registeredCu.getCurrency () )
                            .append ( ",clsf=" ).append ( clsf.getShortName () ).append ( ",new tx=" ).append ( tx != null ).toString () );
                }

                if ( registeredCu.getCurrency () == null || registeredCu.getCurrency ().isSameAs ( ccy ) )
                {
                    registeredCu.setLimitAmount ( limit );
                    registeredCu.resetEarmarkedUsedAmount ();
                    registeredCu.update ();
                }
                else
                {
                    CreditUtilC.releaseTransaction ( tx );
                    throw new IdcRuntimeException ( new UnsupportedOperationException ( "Credit currency modification is not supported." ) );
                }

                // audit
                boolean isCptyOrgLevel = cclr.getTradingParty () == null;
                boolean suppressAudit = registerLimit && sameLimit && CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean ().isUnchangedLimitUpdateAuditSuppressEnabled ( creditProviderOrg );
                if ( ! suppressAudit )
                {
                    CreditLimitAdminAuditManagerC.getInstance ().auditCounterpartyLevelCreditLimitOnDate ( creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization (), existingLimit, limit, date );
                }

                HashMap<String, String> propertiesMap = new HashMap<String, String>( 1 );
                propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cclr.getTradingPartyOrganization().getGUID() );
                if ( !isCptyOrgLevel )
                {
                    propertiesMap.put ( CreditLimit.CREDIT_COUNTERPARTY, creditCpty.getGUID () );
                }
                propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.UPDATELIMIT.getName() );

                // Adds the remote notification functor registered for the event.
                Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.UPDATELIMIT.getName() );
                if ( remoteFunctor != null )
                {
                    getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
                }
                else
                {
                    log.info( "CLAS.setCreditLimit : No remote notification functor registered. event=" + CreditMessageEvent.UPDATELIMIT.getName() );
                }

                // commits transaction if created by the service.
                if(CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationPersistenceSpacesEnabled(creditProviderOrg.getShortName()))
                {
                    CreditDataQueryServiceFactory.getCreditReferenceDataService().createOrUpdateCreditUtilization( registeredCu );
                }
                CreditUtilC.endTransaction( tx );

            }
            else
            {
                if ( log.isDebugEnabled() )
                {
                    log.debug( new StringBuilder( 200 ).append( "CLAS.setCreditLimit : Not a Daily credit limit rule. org=" )
                            .append( creditProviderOrg.getShortName() ).append( ",tp=" ).append( creditCpty.getShortName() )
                            .append( ",newAmt=" ).append( limit ).append( ",existing=" ).append( clr.getLimitAmount() )
                            .append( ",ccy=" ).append( ccy ).append( ",existing=" ).append( clr.getCurrency() )
                            .append( ",clsf=" ).append( clsf.getShortName() ).append( ",new tx=" ).append( tx != null ).toString() );
                }
            }

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setCreditLimit : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" )
                        .append( clsf.getShortName() ).append( ",ccy=" ).append( ccy ).append( ",date=" ).append( date )
                        .append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setCreditLimit : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    public void setDefaultExposureLevel( Organization cpo, boolean isOrgLevel )
    {
        getCreditRelationshipService().setDefaultExposureLevel(cpo, isOrgLevel);
    }

    public void setOrganizationExposureLevel( Organization cpo, Organization cco )
    {
        getCreditRelationshipService().setOrganizationExposureLevel(cpo, cco);
    }

    public void setLegalEntityExposureLevel( Organization cpo, Organization cco )
    {
        getCreditRelationshipService().setLegalEntityExposureLevel(cpo, cco);
    }

    public void establishCreditRelationship( Organization cpo, TradingParty cc )
    {
        getCreditRelationshipService().establishCreditRelationship(cpo, cc);
    }

    public void establishCreditRelationship( Organization cpo, TradingParty cc, boolean isAuditEnabled )
    {
        getCreditRelationshipService().establishCreditRelationship(cpo, cc, isAuditEnabled);
    }

    public void removeCreditRelationship( Organization cpo, TradingParty cc )
    {
        getCreditRelationshipService().removeCreditRelationship(cpo, cc);
    }

    public void setupCreditProvider( Organization cpo, MarketDataSet mds )
    {
        getCreditRelationshipService().setupCreditProvider(cpo, mds);
    }

    public void reinitialize( Organization cpo )
    {
        getCreditRelationshipService().reinitialize(cpo);
    }

    public void reinitializeCreditCounterpartyOrganization( Organization cpo, Organization cco )
    {
        getCreditRelationshipService().reinitializeCreditCounterpartyOrganization(cpo, cco);
    }

    public WorkflowMessage process( WorkflowMessage wm )
    {
        try
        {
            if ( CreditLimit.CREDIT_ADMIN.equals(wm.getTopic()) )
            {
                Organization creditProviderOrg = ( Organization ) wm.getParameterValue( CreditLimit.CREDIT_PROVIDER_ORGANIZATION );
                Organization creditCptyOrg = ( Organization ) wm.getParameterValue( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION );
                TradingParty creditCpty = ( TradingParty ) wm.getParameterValue( CreditLimit.CREDIT_COUNTERPARTY );
                CreditLimitClassification clsf = ( CreditLimitClassification ) wm.getParameterValue( CreditLimit.CREDIT_LIMIT_CLASSIFICATION );
                MarketDataSet mds = ( MarketDataSet ) wm.getParameterValue( CreditLimit.MARKET_DATASET );
                if ( CreditMessageEvent.ENABLECREDIT.getName().equals( wm.getEventName() ) )
                {
                    if ( creditCpty != null )
                    {
                        setCreditEnabled( creditProviderOrg, creditCpty, true );
                    }
                    else
                    {
                        setCreditEnabled( creditProviderOrg, true );
                    }
                }
                else if ( CreditMessageEvent.DISABLECREDIT.getName().equals( wm.getEventName() ) )
                {
                    if ( creditCpty != null )
                    {
                        setCreditEnabled( creditProviderOrg, creditCpty, false );
                    }
                    else
                    {
                        setCreditEnabled( creditProviderOrg, false );
                    }
                }
                else if ( CreditMessageEvent.UPDATELIMIT.getName().equals( wm.getEventName() ) )
                {
                    Currency ccy = ( Currency ) wm.getParameterValue( CreditLimit.CREDIT_LIMIT_CURRENCY );
                    Double limit = ( Double ) wm.getParameterValue( CreditLimit.CREDIT_LIMIT_AMOUNT );
                    IdcDate date = ( IdcDate ) wm.getParameterValue( CreditLimit.CREDIT_UTILIZATION_DATE );
                    if ( date != null )
                    {
                        setCreditLimit( creditProviderOrg, creditCpty, clsf, limit, ccy, date, false );
                    }
                    else
                    {
                        setCreditLimit( creditProviderOrg, creditCpty, clsf, limit.doubleValue(), ccy );
                    }
                }
                else if ( CreditMessageEvent.SETCOUNTERPARTYEXPOSURE.getName().equals( wm.getEventName() ) )
                {
                    setLegalEntityExposureLevel( creditProviderOrg, creditCptyOrg );
                }
                else if ( CreditMessageEvent.SETCREDITRELATIONSHIP.getName().equals( wm.getEventName() ) )
                {
                    establishCreditRelationship( creditProviderOrg, creditCpty );
                }
                else if ( CreditMessageEvent.SETCREDITUTILIZATIONCALCULATOR.getName().equals( wm.getEventName() ) )
                {
                    CreditUtilizationCalculator calc = ( CreditUtilizationCalculator ) wm.getParameterValue( CreditLimit.CREDIT_UTILIZATION_CALCULATOR );
                    setNettingMethodology( creditProviderOrg, creditCpty, clsf, calc );
                }
                else if ( CreditMessageEvent.SETORGEXPOSURE.getName().equals( wm.getEventName() ) )
                {
                    setOrganizationExposureLevel( creditProviderOrg, creditCptyOrg );
                }
                else if ( CreditMessageEvent.REMOVECREDITRELATIONSHIP.getName().equals( wm.getEventName() ) )
                {
                    removeCreditRelationship( creditProviderOrg, creditCpty );
                }
                else if ( CreditMessageEvent.SETUPCREDITPROVIDER.getName().equals( wm.getEventName() ) )
                {
                    setupCreditProvider( creditProviderOrg, mds );
                }
                else if ( CreditMessageEvent.REINITIALIZECREDITPROVIDER.getName().equals( wm.getEventName() ) )
                {
                    reinitialize( creditProviderOrg );
                }
                else if ( CreditMessageEvent.REINITIALIZECREDITCOUNTERPARTYORG.getName().equals( wm.getEventName() ) )
                {
                    reinitializeCreditCounterpartyOrganization( creditProviderOrg, creditCptyOrg );
                }
                else
                {
                    log.warn( "CLAS.process : Unsupported topic=" + wm.getTopic() );
                }
            }
            else
            {
                log.warn("CLAS.process : Invalid topic=" + wm.getTopic());
            }
        }
        catch ( Exception e )
        {
            log.warn("CLAS.process : topic=" + wm.getTopic() + ",event=" + wm.getEventName());
        }
        return wm;
    }

    public void setDailyExposureHorizon( Organization creditProviderOrg, int days )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setDailyExposureHorizon : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",days=" ).append( days ).toString() );
        }
        if ( !validateCreditProvider( creditProviderOrg, "setCounterpartyCreditExposureViewDays" ) )
        {
            return;
        }
        if ( !CreditUtilC.validateDailyExposureHorizon(days) )
        {
            log.info( new StringBuilder( 200 ).append( "CLAS.setDailyExposureHorizon : Invalid value. org=" )
                    .append( creditProviderOrg.getShortName() ).append( ",days=" ).append( days ).toString() );
            return;
        }
        IdcTransaction tx = null;

        try
        {
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg );
            if ( orgFunc.getDailyExposureHorizon() == days )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setDailyExposureHorizon : Specified value is same as existing. org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",days=" ).append( days )
                        .append( ",existing=" ).append( orgFunc.getDailyExposureHorizon() ).toString() );
                return;
            }

            tx = startTransaction( orgFunctionTransactionReadOnlyClasses );

            CreditLimitOrgFunction registeredOrgFunc = ( CreditLimitOrgFunction ) orgFunc.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDailyExposureHorizon : org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",days=" ).append( days )
                        .append( ",existing=" ).append( registeredOrgFunc.getDailyExposureHorizon() ).append( ",newTx=" )
                        .append(tx != null).toString() );
            }

            registeredOrgFunc.setDailyExposureHorizon(days);
            //audit for change in daily exposure horizon
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelDailyExposureHorizon(creditProviderOrg, orgFunc.getDailyExposureHorizon(), days);
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );
            if ( log.isDebugEnabled() )
            {
                log.debug(new StringBuilder(200).append("CLAS.setDailyExposureHorizon : End. creditProviderOrg=")
                        .append(creditProviderOrg.getShortName()).append(" took ms=").append(System.currentTimeMillis() - t0).toString());
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setDailyExposureHorizon : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public void setEnableRejectionEmail( Organization creditProviderOrg, boolean enable )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setEnableRejectionEmail : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",enable=" ).append( enable ).toString() );
        }
        if ( !validateCreditProvider(creditProviderOrg, "setEnableRejectionEmail") )
        {
            return;
        }
        IdcTransaction tx = null;

        try
        {
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg );
            if ( orgFunc.isEnableRejectionEmail() == enable )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setEnableRejectionEmail : Specified value is same as existing. org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",enable=" ).append( enable )
                        .append( ",existing=" ).append( orgFunc.isEnableRejectionEmail() ).toString() );
                return;
            }

            tx = startTransaction( orgFunctionTransactionReadOnlyClasses );

            CreditLimitOrgFunction registeredOrgFunc = ( CreditLimitOrgFunction ) orgFunc.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setEnableRejectionEmail : org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",enable=" ).append( enable )
                        .append( ",existing=" ).append( orgFunc.isEnableRejectionEmail() ).append( ",newTx=" ).append(tx != null).toString() );
            }

            registeredOrgFunc.setEnableRejectionEmail(enable);

            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelRejectionEmailEnable(creditProviderOrg, enable);

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setEnableRejectionEmail : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setEnableRejectionEmail : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    public void setSenderEmailAddress( Organization creditProviderOrg, String email )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setSenderEmailAddress : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",email=" ).append( email ).toString() );
        }
        if ( !validateCreditProvider( creditProviderOrg, "setSenderEmailAddress" ) )
        {
            return;
        }

        if ( email != null && !CreditUtilC.validateEmailIds( email ) )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setSenderEmailAddress : Invalid email address=" )
                    .append( email ).append( ",creditProviderOrg=" ).append( creditProviderOrg ).toString() );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_NOTIFICATION_EMAIL_ERROR_CODE, email );
        }

        IdcTransaction tx = null;

        try
        {
            CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg );
            if ( ( orgFunc.getSenderEmailAddress() == null && email == null ) || ( email != null && email.equals( orgFunc.getSenderEmailAddress() ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setSenderEmailAddress : Specified value is same as existing. org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",email=" ).append( email )
                        .append( ",existing=" ).append( orgFunc.getSenderEmailAddress() ).toString() );
                return;
            }

            tx = startTransaction( orgFunctionTransactionReadOnlyClasses );

            CreditLimitOrgFunction registeredOrgFunc = ( CreditLimitOrgFunction ) orgFunc.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setSenderEmailAddress : org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",email=" ).append( email )
                        .append( ",existing=" ).append( registeredOrgFunc.getSenderEmailAddress() ).append( ",newTx=" )
                        .append(tx != null).toString() );
            }

            registeredOrgFunc.setSenderEmailAddress(email);
            //audit
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelSenderEmailAddress(creditProviderOrg, orgFunc.getSenderEmailAddress(), email);
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setSenderEmailAddress : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setSenderEmailAddress : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    public Double getDefaultLeverageFactor( Organization cpo )
    {
        return getCreditPFEAdminService().getDefaultLeverageFactor( cpo );
    }

    public void setDefaultLeverageFactor( Organization cpo, Double leverageFactor )
    {
        getCreditPFEAdminService().setDefaultLeverageFactor( cpo, leverageFactor );
    }

    public Boolean isDefaultApplyPandL( Organization creditProviderOrg )
    {
        if ( !validateCreditProvider( creditProviderOrg, "isDefaultApplyPandL" ) )
        {
            return null;
        }
        try
        {
            return CreditUtilC.getCreditLimitRuleSet( creditProviderOrg ).isApplyPandL();
        }
        catch ( Exception e )
        {
            log.warn("CLAS.isDefaultApplyPandL : Error getting default apply P&L flag for creditProviderOrg=" + creditProviderOrg, e);
        }
        return null;
    }

    public void setDefaultApplyPandL( Organization creditProviderOrg, Boolean applyPandL )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultApplyPandL : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",applyPandL=" ).append( applyPandL ).toString() );
        }
        if ( !validateCreditProvider( creditProviderOrg, "setDefaultApplyPandL" ) )
        {
            return;
        }
        IdcTransaction tx = null;
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( creditProviderOrg );
            Boolean existingApplyPandL = clrs.isApplyPandL();
            if ( ( applyPandL == null && existingApplyPandL == null ) || ( existingApplyPandL != null && applyPandL != null && existingApplyPandL.equals( applyPandL ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setDefaultApplyPandL : Same as existing apply PandL. applyPandL=" )
                        .append( applyPandL ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",existingApplyPandL=" ).append( existingApplyPandL ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitOrgFunctionC.class );

            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultApplyPandL : org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",applyPandL=" ).append( applyPandL )
                        .append( ",existingApplyPandL" ).append( existingApplyPandL )
                        .append( ",newTx=" ).append( tx != null ).toString() );
            }
            String messageEvent = applyPandL != null && applyPandL ? CreditMessageEvent.ENABLEPANDL.getName() : CreditMessageEvent.DISABLEPANDL.getName();
            registeredClrs.setApplyPandL( applyPandL );

            // add a commit handler for recalculation of credit utilization.
            addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( ( Collection<CounterpartyCreditLimitRule> ) clrs.getRules(), false ) );

            // audit
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelApplyPandL( creditProviderOrg, applyPandL );

            // clear the credit utilization cache after commit
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, messageEvent );
            getTransaction().addRemoteFunctor( CreditUtilizationCacheResetNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( messageEvent );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setDefaultApplyPandL : No remote notification functor registered. event=" + messageEvent );
            }
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultApplyPandL : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setDefaultApplyPandL : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public Double getLeverageFactor( Organization cpo, TradingParty cc, CreditLimitClassification clsf )
    {
        return getCreditPFEAdminService().getLeverageFactor( cpo, cc, clsf );
    }

    public void setLeverageFactor( Organization cpo, TradingParty cc, CreditLimitClassification clsf, Double leverageFactor )
    {
        getCreditPFEAdminService().setLeverageFactor( cpo, cc, clsf, leverageFactor );
    }

    public Boolean isApplyPandL( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf )
    {
        if ( clsf == null || !validateCreditCounterparty( creditProviderOrg, creditCpty, "isApplyPandL" ) )
        {
            return null;
        }
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty );
            if ( cptyRule == null )
            {
                log.info( "CLAS.isApplyPandL : No cpty rule for org=" + creditProviderOrg );
            }

            Collection<CreditLimitRule> rules = new ArrayList<CreditLimitRule>( cptyRule.getChildrenRules() );
            for ( CreditLimitRule clr : rules )
            {
                if ( clsf.isSameAs( clr.getClassification() ) )
                {
                    return clr.isApplyPandL();
                }
            }
            log.info( "CLAS.isApplyPandL : No credit limit rule with the clsf=" + clsf );
        }
        catch ( Exception e )
        {
            log.warn(new StringBuilder(200).append("CLAS.isApplyPandL : Error getting apply PandL for creditProviderOrg=")
                    .append(creditProviderOrg).append(",creditCpty=").append(creditCpty).append(",clsf=").append(clsf).toString(), e);
        }
        return null;
    }

    public void setApplyPandL( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, Boolean applyPandL )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setApplyPandL : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",clsf=" ).append( clsf ).toString() );
        }
        if ( clsf == null )
        {
            log.warn( "CLAS.setApplyPandL : Parameters not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "setApplyPandL" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setApplyPandL : No cpty rule. applyPandL=" )
                        .append( applyPandL ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" ).append( clsf.getShortName() ).toString() );
                return;
            }

            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cptyRule, clsf );
            if ( clr == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setApplyPandL : No credit limit rule found in cpty rule=" )
                        .append( cptyRule ).append( ",creditProviderOrg=" ).append( creditProviderOrg ).append( ",clsf=" ).append( clsf ).toString() );
                return;
            }

            Boolean existingApplyPandL = clr.isApplyPandL();
            if ( ( applyPandL == null && existingApplyPandL == null ) || applyPandL != null && existingApplyPandL != null && applyPandL.equals( existingApplyPandL ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setApplyPandL : Same as existing applyPandL=" )
                        .append( applyPandL ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" ).append( clsf.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitOrgFunctionC.class );
            addReadOnlyClass( tx, CreditLimitRuleSetC.class );
            CreditLimitRule registeredClr = ( CreditLimitRule ) clr.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setApplyPandL : cptyRule=" )
                        .append( cptyRule ).append( ",creditLimitRule=" ).append( clr ).append( ",rules=" ).append( cptyRule.getChildrenRules() )
                        .append( ",new tx=" ).append( tx != null ).toString() );
            }

            // set the calculator
            registeredClr.setApplyPandL( applyPandL );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;
            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelApplyPandL( creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization(), applyPandL );

            // send jms message updates.
            MessageEvent messageEvent = applyPandL ? CreditMessageEvent.ENABLEPANDL : CreditMessageEvent.DISABLEPANDL;

            // add a commit handler for recalculation of credit utilization.
            addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( clr ) );

            // add notification functors for clearing the credit utilization cache.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, messageEvent.getName() );
            if ( cptyRule.getTradingParty() != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cptyRule.getTradingParty().getGUID() );
            }
            getTransaction().addRemoteFunctor( CreditUtilizationCacheResetNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( messageEvent.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setApplyPandL : No remote notification functor registered. event=" + messageEvent.getName() );
            }
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setApplyPandL : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" )
                        .append( clsf.getShortName() ).append( ",applyPandL=" ).append( applyPandL ).append( " took ms=" )
                        .append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setApplyPandL : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }


    public Boolean isDefaultIgnoreCurrDatePositions( Organization creditProviderOrg )
    {
        if ( !validateCreditProvider( creditProviderOrg, "isDefaultIgnoreCurrDatePositions" ) )
        {
            return null;
        }
        try
        {
            return CreditUtilC.getCreditLimitRuleSet( creditProviderOrg ).ignoreCurrentDatePositions();
        }
        catch ( Exception e )
        {
            log.warn("CLAS.isDefaultIgnoreCurrDatePositions : Error getting default IgnoreCurrDatePositions flag for creditProviderOrg=" + creditProviderOrg, e);
        }
        return null;
    }

    public void setDefaultIgnoreCurrDatePositions( Organization creditProviderOrg, Boolean ignoreCurrDatePositions )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultIgnoreCurrDatePositions : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",ignoreCurrDatePositions=" ).append( ignoreCurrDatePositions ).toString() );
        }
        if ( !validateCreditProvider( creditProviderOrg, "setDefaultIgnoreCurrDatePositions" ) )
        {
            return;
        }
        IdcTransaction tx = null;
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( creditProviderOrg );
            Boolean existingIgnoreCurrDatePositions = clrs.ignoreCurrentDatePositions();
            if ( ( ignoreCurrDatePositions == null && existingIgnoreCurrDatePositions == null ) || ( existingIgnoreCurrDatePositions != null && ignoreCurrDatePositions != null && existingIgnoreCurrDatePositions.equals( ignoreCurrDatePositions ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setDefaultIgnoreCurrDatePositions : Same as existing IgnoreCurrentDatePositions. ignoreCurrDatePositions=" )
                        .append( ignoreCurrDatePositions ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",existingIgnoreCurrDatePositions=" ).append( existingIgnoreCurrDatePositions ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitOrgFunctionC.class );

            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultIgnoreCurrDatePositions : org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",ignoreCurrDatePositions=" ).append( ignoreCurrDatePositions )
                        .append( ",existingIgnoreCurrDatePositions" ).append( existingIgnoreCurrDatePositions )
                        .append( ",newTx=" ).append( tx != null ).toString() );
            }

            registeredClrs.setIgnoreCurrentDatePositions( ignoreCurrDatePositions );

            // add a commit handler for recalculation of credit utilization.
            addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( ( Collection<CounterpartyCreditLimitRule> ) clrs.getRules(), false ) );

            // audit
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelIgnoreCurrentDatePositions( creditProviderOrg, ignoreCurrDatePositions );

            // clear the credit utilization cache after commit
            MessageEvent messageEvent = ignoreCurrDatePositions ? CreditMessageEvent.ENABLEIGNORECURRENTDATEPOSITIONS : CreditMessageEvent.DISABLEIGNORECURRENTDATEPOSITIONS;
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, messageEvent.getName() );
            getTransaction().addRemoteFunctor( CreditUtilizationCacheResetNotificationFunctorC.class.getName(), propertiesMap );

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultIgnoreCurrDatePositions : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setDefaultIgnoreCurrDatePositions : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public Boolean isIgnoreCurrDatePositions( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf )
    {
        if ( clsf == null || !validateCreditCounterparty( creditProviderOrg, creditCpty, "isIgnoreCurrDatePositions" ) )
        {
            return null;
        }
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty );
            if ( cptyRule == null )
            {
                log.info( "CLAS.isIgnoreCurrDatePositions : No cpty rule for org=" + creditProviderOrg );
            }

            Collection<CreditLimitRule> rules = new ArrayList<CreditLimitRule>( cptyRule.getChildrenRules() );
            for ( CreditLimitRule clr : rules )
            {
                if ( clsf.isSameAs( clr.getClassification() ) )
                {
                    return clr.ignoreCurrentDatePositions();
                }
            }
            log.info( "CLAS.isIgnoreCurrDatePositions : No credit limit rule with the clsf=" + clsf );
        }
        catch ( Exception e )
        {
            log.warn(new StringBuilder(200).append("CLAS.isIgnoreCurrDatePositions : Error getting igonore currentdate positions for creditProviderOrg=")
                    .append(creditProviderOrg).append(",creditCpty=").append(creditCpty).append(",clsf=").append(clsf).toString(), e);
        }
        return null;
    }

    public void setIgnoreCurrDatePositions( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, Boolean ignoreCurrDatePositions )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setIgnoreCurrDatePositions : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",clsf=" ).append( clsf ).toString() );
        }
        if ( clsf == null )
        {
            log.warn( "CLAS.setIgnoreCurrDatePositions : Parameters not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "setIgnoreCurrDatePositions" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setIgnoreCurrDatePositions : No cpty rule. ignoreCurrDatePositions=" )
                        .append( ignoreCurrDatePositions ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" ).append( clsf.getShortName() ).toString() );
                return;
            }

            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cptyRule, clsf );
            if ( clr == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setIgnoreCurrDatePositions : No credit limit rule found in cpty rule=" )
                        .append( cptyRule ).append( ",creditProviderOrg=" ).append( creditProviderOrg ).append( ",clsf=" ).append( clsf ).toString() );
                return;
            }

            Boolean existingIgnoreCurrDatePositions = clr.ignoreCurrentDatePositions();
            if ( ( ignoreCurrDatePositions == null && existingIgnoreCurrDatePositions == null ) || ignoreCurrDatePositions != null && existingIgnoreCurrDatePositions != null && ignoreCurrDatePositions.equals( existingIgnoreCurrDatePositions ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setIgnoreCurrDatePositions : Same as existing ignoreCurrDatePositions=" )
                        .append( ignoreCurrDatePositions ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" ).append( clsf.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitOrgFunctionC.class );
            addReadOnlyClass( tx, CreditLimitRuleSetC.class );
            CreditLimitRule registeredClr = ( CreditLimitRule ) clr.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setIgnoreCurrDatePositions : cptyRule=" )
                        .append( cptyRule ).append( ",creditLimitRule=" ).append( clr ).append( ",rules=" ).append( cptyRule.getChildrenRules() )
                        .append( ",new tx=" ).append( tx != null ).toString() );
            }

            // set the calculator
            registeredClr.setIgnoreCurrentDatePositions( ignoreCurrDatePositions );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;
            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelIgnoreCurrentDatePositions( creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization(), ignoreCurrDatePositions );

            // send jms message updates.
            MessageEvent messageEvent = ignoreCurrDatePositions ? CreditMessageEvent.ENABLEIGNORECURRENTDATEPOSITIONS : CreditMessageEvent.DISABLEIGNORECURRENTDATEPOSITIONS;

            // add a commit handler for recalculation of credit utilization.
            addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( clr ) );

            // add notification functors for clearing the credit utilization cache.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, messageEvent.getName() );
            if ( cptyRule.getTradingParty() != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cptyRule.getTradingParty().getGUID() );
            }
            getTransaction().addRemoteFunctor( CreditUtilizationCacheResetNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( messageEvent.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setIgnoreCurrDatePositions : No remote notification functor registered. event=" + messageEvent.getName() );
            }
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setIgnoreCurrDatePositions : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" )
                        .append( clsf.getShortName() ).append( ",ignoreCurrDatePositions=" ).append( ignoreCurrDatePositions ).append( " took ms=" )
                        .append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setIgnoreCurrDatePositions : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public Tenor getDefaultMinimumTenor( Organization creditProviderOrg )
    {
        if ( !validateCreditProvider(creditProviderOrg, "getDefaultMinimumTenor") )
        {
            return null;
        }
        try
        {
            return CreditUtilC.getCreditLimitRuleSet( creditProviderOrg ).getMinimumTenor();
        }
        catch ( Exception e )
        {
            log.warn("CLAS.getDefaultMinimumTenor : Error getting default minimum tenor for creditProviderOrg=" + creditProviderOrg, e);
        }
        return null;
    }

    public Tenor getMinimumTenor( Organization creditProviderOrg, TradingParty creditCpty )
    {
        if ( !validateCreditCounterparty(creditProviderOrg, creditCpty, "getMinimumTenor") )
        {
            return null;
        }
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty );
            if ( cptyRule == null )
            {
                log.info( "CLAS.getMinimumTenor : No cpty rule for org=" + creditProviderOrg );
                return null;
            }
            return cptyRule.getMinimumTenor();
        }
        catch ( Exception e )
        {
            log.warn(new StringBuilder(200).append("CLAS.getMinimumTenor : Error getting minimum tenor for creditProviderOrg=")
                    .append(creditProviderOrg).append(",creditCpty=").append(creditCpty).toString(), e);
        }
        return null;
    }

    public void setDefaultMinimumTenor( Organization creditProviderOrg, Tenor minimumTenor )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultMinimumTenor : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",minimumTenor=" ).append( minimumTenor ).toString() );
        }
        if ( !validateCreditProvider( creditProviderOrg, "setDefaultMinimumTenor" ) )
        {
            return;
        }
        //todo: check if there is any validation required for minTenor field - IMM, etc.
        /*if ( minimumTenor == null )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultMinimumTenor : Invalid minimumTenor=" )
                    .append( minimumTenor ).toString() );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_MINIMUM_TENOR_ERROR_CODE, MinimumTenor );
        }*/

        IdcTransaction tx = null;
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( creditProviderOrg );
            Tenor existingMinTenor = clrs.getMinimumTenor();
            if ( ( minimumTenor == null && existingMinTenor == null ) || ( existingMinTenor != null && minimumTenor != null && existingMinTenor.compareTo( minimumTenor ) == 0 ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setDefaultMinimumTenor : Same as existing tenor. minimumTenor=" )
                        .append( minimumTenor ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",existingMinTenor=" ).append( existingMinTenor ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitOrgFunctionC.class );

            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultMinimumTenor : org=" ).append( creditProviderOrg.getShortName() )
                        .append( ",minimumTenor=" ).append( minimumTenor ).append( ",existingMinTenor=" ).append( existingMinTenor ).append( ",newTx=" ).append( tx != null ).toString() );
            }
            registeredClrs.setMinimumTenor( minimumTenor );

            //audit
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelMinimumTenor( creditProviderOrg, existingMinTenor, minimumTenor );

            // Adds the remote notification functor registered for the event.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 1 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETMINIMUMTENOR.getName() );

            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETMINIMUMTENOR.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setDefaultMinimumTenor : No remote notification functor registered. event=" + CreditMessageEvent.SETMINIMUMTENOR.getName() );
            }
            getTransaction().addRemoteFunctor(CreditTenorConfigChanges.class.getName(), propertiesMap);

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultMinimumTenor : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setDefaultMinimumTenor : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    /**
     *
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit counterparty
     * @param minimumTenor      minimum Tenor
     */
    public void setMinimumTenor( Organization creditProviderOrg, TradingParty creditCpty, Tenor minimumTenor )
    {
        setMinimumTenor(creditProviderOrg,creditCpty,minimumTenor,false);
    }


    public void setMinimumTenor( Organization cpo, Organization cco, Tenor minimumTenor )
    {
        LegalEntity creditCptyLe = CreditUtilC.getDefaultlegalEntity(cco);
        TradingParty creditTp = CounterpartyUtilC.getTradingParty(creditCptyLe, cpo);
        if ( creditTp == null )
        {
            log.warn(new StringBuilder(200).append("CLAS.setMinimumTenor : No credit cpty. creditProviderOrg=")
                    .append(cpo.getShortName()).append(",creditCptyOrg=").append(cco.getShortName()).toString());
            return;
        }
        setMinimumTenor(cpo,creditTp,minimumTenor);
    }

    public void setMinimumTenor( Organization creditProviderOrg, TradingParty creditCpty, Tenor minimumTenor, boolean  updateCpty )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setMinimumTenor : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append(",creditCpty=").append(creditCpty).append( ",minimumTenor=" ).append( minimumTenor ).toString() );
        }

        //todo: check if there is any validation required for minTenor field - IMM, etc.
        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "setMinimumTenor" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = null;
            if(updateCpty)
            {
                cptyRule =  CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule(creditProviderOrg, creditTp);
            }
            else
            {
                cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(creditProviderOrg, creditTp);
            }
            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setMinimumTenor : No cpty rule. minimumTenor=" )
                        .append( minimumTenor ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).toString() );
                return;
            }
            Tenor existingMinTenor = cptyRule.getMinimumTenor();

            if ( ( minimumTenor == null && existingMinTenor == null ) || ( existingMinTenor != null && minimumTenor != null && existingMinTenor.compareTo( minimumTenor ) == 0 ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setMinimumTenor : Same as existing minimum tenor=" )
                        .append( minimumTenor ).append(",existing=").append(existingMinTenor).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setMinimumTenor : cptyRule=" ).append( cptyRule ).append(",minimumTenor=")
                        .append(minimumTenor).append( ",existing=" ).append( existingMinTenor ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            // set the minimum tenor
            registeredCclr.setMinimumTenor( minimumTenor );

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelMinimumTenor(creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization(), existingMinTenor, minimumTenor);

            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            if ( cptyRule.getTradingParty () != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, creditCpty.getGUID() );
            }
            propertiesMap.put(CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETMINIMUMTENOR.getName());
            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETMINIMUMTENOR.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setMinimumTenor : No remote notification functor registered. event=" + CreditMessageEvent.SETMINIMUMTENOR.getName() );
            }
            getTransaction().addRemoteFunctor(CreditTenorConfigChanges.class.getName(), propertiesMap);
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setMinimumTenor : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( ",creditCpty=" ).append( creditCpty.getShortName() )
                        .append(",minimumTenor=").append(minimumTenor).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setMinimumTenor : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public Tenor getDefaultMaximumTenor( Organization creditProviderOrg )
    {
        if ( !validateCreditProvider(creditProviderOrg, "getDefaultMaximumTenor") )
        {
            return null;
        }
        try
        {
            return CreditUtilC.getCreditLimitRuleSet( creditProviderOrg ).getMaximumTenor();
        }
        catch ( Exception e )
        {
            log.warn("CLAS.getDefaultMaximumTenor : Error getting default maximum tenor for creditProviderOrg=" + creditProviderOrg, e);
        }
        return null;
    }

    public Tenor getMaximumTenor( Organization creditProviderOrg, TradingParty creditCpty )
    {
        if ( !validateCreditCounterparty(creditProviderOrg, creditCpty, "getMaximumTenor") )
        {
            return null;
        }
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty );
            if ( cptyRule == null )
            {
                log.info( "CLAS.getMaximumTenor : No cpty rule for org=" + creditProviderOrg );
                return null;
            }
            return cptyRule.getMaximumTenor();
        }
        catch ( Exception e )
        {
            log.warn(new StringBuilder(200).append("CLAS.getMaximumTenor : Error getting maximum tenor for creditProviderOrg=")
                    .append(creditProviderOrg).append(",creditCpty=").append(creditCpty).toString(), e);
        }
        return null;
    }

    public void setDefaultMaximumTenor( Organization creditProviderOrg, Tenor maximumTenor )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultMaximumTenor : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",maximumTenor=" ).append( maximumTenor ).toString() );
        }
        if ( !validateCreditProvider( creditProviderOrg, "setDefaultMaximumTenor" ) )
        {
            return;
        }
        //todo: check if there is any validation required for maxTenor field - IMM, etc.
        /*if ( maximumTenor == null )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultMaximumTenor : Invalid maximumTenor=" )
                    .append( maximumTenor ).toString() );
            throw new CreditLimitException( CreditLimitConstants.CREDIT_ADMIN_MAXIMUM_TENOR_ERROR_CODE, maximumTenor );
        }*/

        IdcTransaction tx = null;
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( creditProviderOrg );
            Tenor existingMaxTenor = clrs.getMaximumTenor();
            if ( ( maximumTenor == null && existingMaxTenor == null ) || ( existingMaxTenor != null && maximumTenor != null && existingMaxTenor.compareTo( maximumTenor ) == 0 ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setDefaultMaximumTenor : Same as existing tenor. maximumTenor=" )
                        .append( maximumTenor ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",existingMaxTenor=" ).append( existingMaxTenor ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitOrgFunctionC.class );

            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultMaximumTenor : org=" ).append( creditProviderOrg.getShortName() )
                        .append( ",maximumTenor=" ).append( maximumTenor ).append( ",existingMaxTenor=" ).append( existingMaxTenor ).append( ",newTx=" ).append( tx != null ).toString() );
            }
            registeredClrs.setMaximumTenor( maximumTenor );

            //audit
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelMaximumTenor( creditProviderOrg, existingMaxTenor, maximumTenor );

            // Adds the remote notification functor registered for the event.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 1 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETMAXIMUMTENOR.getName() );

            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETMAXIMUMTENOR.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setDefaultMaximumTenor : No remote notification functor registered. event=" + CreditMessageEvent.SETMAXIMUMTENOR.getName() );
            }
            getTransaction().addRemoteFunctor(CreditTenorConfigChanges.class.getName(), propertiesMap);

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultMaximumTenor : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setDefaultMaximumTenor : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }
    public void setMaximumTenor( Organization cpo, Organization cco, Tenor minimumTenor )
    {
        LegalEntity creditCptyLe = CreditUtilC.getDefaultlegalEntity(cco);
        TradingParty creditTp = CounterpartyUtilC.getTradingParty(creditCptyLe, cpo);
        if ( creditTp == null )
        {
            log.warn(new StringBuilder(200).append("CLAS.setMinimumTenor : No credit cpty. creditProviderOrg=")
                    .append(cpo.getShortName()).append(",creditCptyOrg=").append(cco.getShortName()).toString());
            return;
        }
        setMaximumTenor(cpo,creditTp,minimumTenor);
    }

    public void setMaximumTenor( Organization creditProviderOrg, TradingParty creditCpty, Tenor maximumTenor )
    {
        setMaximumTenor(creditProviderOrg,creditCpty,maximumTenor,false);
    }

    public void setMaximumTenor( Organization creditProviderOrg, TradingParty creditCpty, Tenor maximumTenor , boolean updateCptyLE)
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setMaximumTenor : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append(",creditCpty=").append(creditCpty).append( ",maximumTenor=" ).append( maximumTenor ).toString() );
        }

        //todo: check if there is any validation required for maxTenor field - IMM, etc.
        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "setMaximumTenor" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = null;
            if(updateCptyLE)
            {
                cptyRule = CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule(creditProviderOrg, creditTp);
            }
            else
            {
                cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(creditProviderOrg, creditTp);
            }
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setMaximumTenor : No cpty rule. maximumTenor=" )
                        .append( maximumTenor ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).toString() );
                return;
            }
            Tenor existingMaxTenor = cptyRule.getMaximumTenor();

            if ( ( maximumTenor == null && existingMaxTenor == null ) || ( existingMaxTenor != null && maximumTenor != null && existingMaxTenor.compareTo( maximumTenor ) == 0 ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setMaximumTenor : Same as existing maximum tenor=" )
                        .append( maximumTenor ).append(",existing=").append(existingMaxTenor).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setMaximumTenor : cptyRule=" ).append( cptyRule ).append(",maximumTenor=")
                        .append(maximumTenor).append( ",existing=" ).append( existingMaxTenor ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            // set the maximum tenor
            registeredCclr.setMaximumTenor( maximumTenor );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelMaximumTenor(creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization(), existingMaxTenor, maximumTenor);

            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            if ( !isCptyOrgLevel )
            {
                propertiesMap.put ( CreditLimit.CREDIT_COUNTERPARTY, creditCpty.getGUID () );
            }
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETMAXIMUMTENOR.getName() );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETMAXIMUMTENOR.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setMaximumTenor : No remote notification functor registered. event=" + CreditMessageEvent.SETMAXIMUMTENOR.getName() );
            }
            getTransaction().addRemoteFunctor(CreditTenorConfigChanges.class.getName(), propertiesMap);
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setMaximumTenor : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( ",creditCpty=" ).append( creditCpty.getShortName() )
                        .append(",maximumTenor=").append(maximumTenor).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setMaximumTenor : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public Boolean isDefaultUpdateBalanceWithPL( Organization creditProviderOrg )
    {
        if ( !validateCreditProvider( creditProviderOrg, "isDefaultUpdateBalanceWithPL" ) )
        {
            return null;
        }
        try
        {
            return CreditUtilC.getCreditLimitRuleSet( creditProviderOrg ).isUpdateBalanceWithPL();
        }
        catch ( Exception e )
        {
            log.warn("CLAS.isDefaultUpdateBalanceWithPL : Error getting default updateBalanceWithPL flag for creditProviderOrg=" + creditProviderOrg, e);
        }
        return null;
    }

    public void setDefaultUpdateBalanceWithPL( Organization creditProviderOrg, Boolean updatePL )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultUpdateBalanceWithPL : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",updatePL=" ).append( updatePL ).toString() );
        }
        if ( !validateCreditProvider( creditProviderOrg, "setDefaultUpdateBalanceWithPL" ) )
        {
            return;
        }
        IdcTransaction tx = null;
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet(creditProviderOrg);
            Boolean existingUpdatePL = clrs.isUpdateBalanceWithPL();
            if ( ( updatePL == null && existingUpdatePL == null ) || ( existingUpdatePL != null && updatePL != null && existingUpdatePL.equals( updatePL ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setDefaultUpdateBalanceWithPL : Same as existing updatePL. updatePL=" )
                        .append( updatePL ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",existingUpdatePL=" ).append( existingUpdatePL ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitOrgFunctionC.class );

            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultUpdateBalanceWithPL : org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",updatePL=" ).append( updatePL )
                        .append( ",existingUpdatePL" ).append( existingUpdatePL )
                        .append( ",newTx=" ).append( tx != null ).toString() );
            }

            registeredClrs.setUpdateBalanceWithPL( updatePL );

            // audit
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelSetUpdateBalanceWithPL( creditProviderOrg, updatePL );

            // commits transaction if created by the service.
            CreditUtilC.endTransaction(tx);

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultUpdateBalanceWithPL : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setDefaultUpdateBalanceWithPL : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public Boolean isUpdateBalanceWithPL( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf )
    {
        if ( clsf == null || !validateCreditCounterparty( creditProviderOrg, creditCpty, "isUpdateBalanceWithPL" ) )
        {
            return null;
        }
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(creditProviderOrg, creditCpty);
            if ( cptyRule == null )
            {
                log.info( "CLAS.isUpdateBalanceWithPL : No cpty rule for org=" + creditProviderOrg );
            }

            Collection<CreditLimitRule> rules = new ArrayList<CreditLimitRule>( cptyRule.getChildrenRules() );
            for ( CreditLimitRule clr : rules )
            {
                if ( clsf.isSameAs( clr.getClassification() ) )
                {
                    return clr.isUpdateBalanceWithPL();
                }
            }
            log.info("CLAS.isUpdateBalanceWithPL : No credit limit rule with the clsf=" + clsf);
        }
        catch ( Exception e )
        {
            log.warn(new StringBuilder(200).append("CLAS.isUpdateBalanceWithPL : Error getting update balance with P&L flag for creditProviderOrg=")
                    .append(creditProviderOrg).append(",creditCpty=").append(creditCpty).append(",clsf=").append(clsf).toString(), e);
        }
        return null;
    }

    public void setUpdateBalanceWithPL( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, Boolean updateBalancePL )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append("CLAS.setUpdateBalanceWithPL : Begin. creditProviderOrg=")
                    .append(creditProviderOrg).append( ",creditCpty=" ).append(creditCpty).append( ",clsf=" ).append( clsf ).toString() );
        }
        if ( clsf == null )
        {
            log.warn( "CLAS.setUpdateBalanceWithPL : Parameters not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "setUpdateBalanceWithPL" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setUpdateBalanceWithPL : No cpty rule. setUpdateBalanceWithPL=" )
                        .append( updateBalancePL ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" ).append( clsf.getShortName() ).toString() );
                return;
            }

            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cptyRule, clsf );
            if ( clr == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setUpdateBalanceWithPL : No credit limit rule found in cpty rule=" )
                        .append( cptyRule ).append( ",creditProviderOrg=" ).append( creditProviderOrg ).append( ",clsf=" ).append( clsf ).toString() );
                return;
            }

            Boolean existingUpdateBalanceWithPL = clr.isUpdateBalanceWithPL();
            if ( ( updateBalancePL == null && existingUpdateBalanceWithPL == null ) || updateBalancePL != null && existingUpdateBalanceWithPL != null && updateBalancePL.equals( existingUpdateBalanceWithPL ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setUpdateBalanceWithPL : Same as existing updateBalancePL=" )
                        .append( updateBalancePL ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" ).append( clsf.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitOrgFunctionC.class );
            addReadOnlyClass( tx, CreditLimitRuleSetC.class );
            CreditLimitRule registeredClr = ( CreditLimitRule ) clr.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setUpdateBalanceWithPL : cptyRule=" )
                        .append( cptyRule ).append( ",creditLimitRule=" ).append( clr ).append( ",rules=" ).append( cptyRule.getChildrenRules() )
                        .append( ",new tx=" ).append( tx != null ).toString() );
            }

            // set the calculator
            registeredClr.setUpdateBalanceWithPL( updateBalancePL );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;
            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelSetUpdateBalanceWithPL( creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization(), updateBalancePL );

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setUpdateBalanceWithPL : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" )
                        .append( clsf.getShortName() ).append( ",updateBalancePL=" ).append( updateBalancePL ).append( " took ms=" )
                        .append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setUpdateBalanceWithPL : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public void updateCreditBalanceWithProfitAndLoss( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, double updateAmt, IdcDate settledPositionBaseDate )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.updateCreditBalanceWithProfitAndLoss : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append(",creditCpty=").append(creditCpty).append( ",clsf=" )
                    .append(clsf).append( ",updateLimitAmount=" ).append( updateAmt ).toString() );
        }
        if ( clsf == null || creditProviderOrg == null )
        {
            log.warn( "CLAS.updateCreditBalanceWithProfitAndLoss : Parameters not specified." );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }
        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "updateCreditBalanceWithProfitAndLoss" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditTp );
            if ( cclr == null )
            {
                log.warn( "CLAS.updateCreditBalanceWithProfitAndLoss : No counterparty rule found for org=" + creditProviderOrg.getShortName() + ",creditCpty=" + creditCpty.getShortName() );
                return;
            }
            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cclr, clsf );
            if ( clr == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.updateCreditBalanceWithProfitAndLoss : No credit limit rule for creditProviderOrg=" )
                        .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",cclr=" )
                        .append( cclr ).append( ",creditRules=" ).append( cclr.getChildrenRules() ).toString() );
                return;
            }

            double existingLimit = clr.getLimitAmount();
            IdcDate currentBusinessDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            IdcDate lastUpdatedDate = clr.getLastBalanceUpdateWithPLDate();
            IdcDate lastPositionDate = clr.getPLPositionDate();
            boolean endOfDayAlreadyUpdated = lastUpdatedDate != null && lastUpdatedDate.isLaterThanOrEqualTo( currentBusinessDate );
            boolean positionDateAlreadyConsidered = lastPositionDate != null && lastPositionDate.isLaterThanOrEqualTo( settledPositionBaseDate );
            if ( endOfDayAlreadyUpdated || positionDateAlreadyConsidered )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.updateCreditBalanceWithProfitAndLoss : End of day update already done or position date already considered. org=" )
                        .append( creditProviderOrg ).append( ",tp=" ).append( creditCpty.getShortName() )
                        .append( ",updateAmt=" ).append( updateAmt ).append( ",currentBusinessDate=" ).append( currentBusinessDate )
                        .append( ",lastUpdatedDate=" ).append( lastUpdatedDate ).append( ",existing=" ).append( clr.getCurrency() ).append( ",clsf=" )
                        .append( clsf.getShortName() ).append( ",new tx=" ).append( tx != null ).toString() );
                return;
            }
            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitOrgFunctionC.class );
            CreditLimitRule registeredClr = ( CreditLimitRule ) clr.getRegisteredObject();
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.updateCreditBalanceWithProfitAndLoss : org=" )
                        .append( creditProviderOrg ).append( ",tp=" ).append( creditCpty.getShortName() )
                        .append( ",updateAmt=" ).append( updateAmt ).append( ",currentLimit=" ).append( existingLimit )
                        .append( ",ccy=" ).append( clr.getCurrency() ).append( ",clsf=" )
                        .append( clsf.getShortName() ).append( ",lastUpdated=" ).append( lastUpdatedDate ).append( ",PLPosDate=" )
                        .append( clr.getPLPositionDate() ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            double newLimit = registeredClr.getCurrency ().round (existingLimit + updateAmt );
            registeredClr.setLimitAmount( newLimit > 0.0 ? newLimit : 0.0 );
            registeredClr.setPLPositionDate( settledPositionBaseDate );
            registeredClr.setLastBalanceUpdateWithPLDate( currentBusinessDate );

            // audit
            boolean isCptyOrgLevel = cclr.getTradingParty() == null;
            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelCreditBalanceUpdate( creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization(), existingLimit, registeredClr.getLimitAmount(), registeredClr.getCreditUtilizationCalculator() );

            // add notification functors for clearing the credit enable/disable flag cache.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 1 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cclr.getTradingPartyOrganization().getGUID());
            if ( !isCptyOrgLevel )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, creditCpty.getGUID() );
            }
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.UPDATEBALANCEWITHPL.getName() );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.UPDATEBALANCEWITHPL.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.updateCreditBalanceWithProfitAndLoss : No remote notification functor registered. event=" + CreditMessageEvent.UPDATEBALANCEWITHPL.getName() );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.updateCreditBalanceWithProfitAndLoss : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" )
                        .append( clsf.getShortName() ).append( ",updateAmt=" ).append( updateAmt ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.updateCreditBalanceWithProfitAndLoss : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public Boolean isDefaultDailyPL( Organization creditProviderOrg )
    {
        if ( !validateCreditProvider( creditProviderOrg, "isDefaultDailyPL" ) )
        {
            return null;
        }
        try
        {
            return CreditUtilC.getCreditLimitRuleSet( creditProviderOrg ).isDailyPL();
        }
        catch ( Exception e )
        {
            log.warn("CLAS.isDefaultDailyPL : Error getting default dailyPL flag for creditProviderOrg=" + creditProviderOrg, e);
        }
        return null;
    }

    public void setDefaultDailyPL( Organization creditProviderOrg, Boolean dailyPL )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultDailyPL : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",dailyPL=" ).append( dailyPL ).toString() );
        }
        if ( !validateCreditProvider( creditProviderOrg, "setDefaultDailyPL" ) )
        {
            return;
        }
        IdcTransaction tx = null;
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( creditProviderOrg );
            Boolean existingDailyPL = clrs.isDailyPL();
            if ( ( dailyPL == null && existingDailyPL == null ) || ( existingDailyPL != null && dailyPL != null && existingDailyPL.equals( dailyPL ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setDefaultDailyPL : Same as existing dailyPL. dailyPL=" )
                        .append( dailyPL ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",existingDailyPL=" ).append( existingDailyPL ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitOrgFunctionC.class );

            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultDailyPL : org=" )
                        .append( creditProviderOrg.getShortName() ).append( ",dailyPL=" ).append( dailyPL )
                        .append( ",existingDailyPL" ).append( existingDailyPL )
                        .append( ",newTx=" ).append( tx != null ).toString() );
            }
            String messageEvent = dailyPL != null && dailyPL ? CreditMessageEvent.ENABLEDAILYPL.getName() : CreditMessageEvent.DISABLEDAILYPL.getName();
            registeredClrs.setDailyPL( dailyPL );

            // add a commit handler for recalculation of credit utilization.
            addCreditUtilizationRecalculationCommitHandler( new CreditUtilizationRecalculationHandlerC( ( Collection<CounterpartyCreditLimitRule> ) clrs.getRules(), false ) );

            // audit
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelSetDailyPL( creditProviderOrg, dailyPL );

            // clear the credit utilization cache after commit
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, messageEvent );
            getTransaction().addRemoteFunctor( CreditUtilizationCacheResetNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( messageEvent );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setDefaultDailyPL : No remote notification functor registered. event=" + messageEvent );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDefaultDailyPL : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setDefaultDailyPL : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public Boolean isDailyPL( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf )
    {
        if ( clsf == null || !validateCreditCounterparty( creditProviderOrg, creditCpty, "isDailyPL" ) )
        {
            return null;
        }
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditCpty );
            if ( cptyRule == null )
            {
                log.info( "CLAS.isDailyPL : No cpty rule for org=" + creditProviderOrg );
            }

            Collection<CreditLimitRule> rules = new ArrayList<CreditLimitRule>( cptyRule.getChildrenRules() );
            for ( CreditLimitRule clr : rules )
            {
                if ( clsf.isSameAs( clr.getClassification() ) )
                {
                    return clr.isDailyPL();
                }
            }
            log.info( "CLAS.isDailyPL : No credit limit rule with the clsf=" + clsf );
        }
        catch ( Exception e )
        {
            log.warn(new StringBuilder(200).append("CLAS.isDailyPL : Error getting dailyPL flag for creditProviderOrg=")
                    .append(creditProviderOrg).append(",creditCpty=").append(creditCpty).append(",clsf=").append(clsf).toString(), e);
        }
        return null;
    }

    public void setDailyPL( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitClassification clsf, Boolean dailyPL )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setDailyPL : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",clsf=" ).append( clsf ).toString() );
        }
        if ( clsf == null )
        {
            log.warn("CLAS.setDailyPL : Parameters not specified.");
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }

        if ( !validateCreditCounterparty(creditProviderOrg, creditCpty, "setDailyPL") )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(creditProviderOrg, creditTp);
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setDailyPL : No cpty rule. dailyPL=" )
                        .append( dailyPL ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" ).append( clsf.getShortName() ).toString() );
                return;
            }

            CreditLimitRule clr = CreditUtilC.getCreditLimitRule( cptyRule, clsf );
            if ( clr == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setDailyPL : No credit limit rule found in cpty rule=" )
                        .append( cptyRule ).append( ",creditProviderOrg=" ).append( creditProviderOrg ).append( ",clsf=" ).append( clsf ).toString() );
                return;
            }

            Boolean existingDailyPL = clr.isDailyPL();
            if ( ( dailyPL == null && existingDailyPL == null ) || dailyPL != null && existingDailyPL != null && dailyPL.equals( existingDailyPL ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setDailyPL : Same as existing dailyPL=" )
                        .append( dailyPL ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).append( ",clsf=" ).append( clsf.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( readOnlyClasses );
            addReadOnlyClass( tx, CreditLimitOrgFunctionC.class );
            addReadOnlyClass( tx, CreditLimitRuleSetC.class );
            CreditLimitRule registeredClr = ( CreditLimitRule ) clr.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug(new StringBuilder(200).append("CLAS.setDailyPL : cptyRule=")
                        .append(cptyRule).append(",creditLimitRule=").append(clr).append(",rules=").append(cptyRule.getChildrenRules())
                        .append(",new tx=").append(tx != null).toString());
            }

            // set the calculator
            registeredClr.setDailyPL(dailyPL);

            MessageEvent messageEvent = dailyPL != null && dailyPL ? CreditMessageEvent.ENABLEDAILYPL : CreditMessageEvent.DISABLEDAILYPL;
            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;
            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelSetDailyPL( creditProviderOrg, isCptyOrgLevel, creditCpty, creditCpty.getLegalEntityOrganization(), dailyPL );

            // add a commit handler for recalculation of credit utilization.
            addCreditUtilizationRecalculationCommitHandler(new CreditUtilizationRecalculationHandlerC(clr));

            // add notification functors for clearing the credit utilization cache.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, messageEvent.getName() );
            if ( cptyRule.getTradingParty() != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cptyRule.getTradingParty().getGUID() );
            }
            getTransaction().addRemoteFunctor( CreditUtilizationCacheResetNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( messageEvent.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setDailyPL : No remote notification functor registered. event=" + messageEvent.getName() );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setDailyPL : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( ",creditCpty=" ).append( creditCpty.getShortName() ).append(",clsf=")
                        .append( clsf.getShortName() ).append( ",dailyPL=" ).append( dailyPL ).append(" took ms=")
                        .append(System.currentTimeMillis() - t0).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setDailyPL : org=" + creditProviderOrg.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public void setCreditStatus( Organization creditProviderOrg, Integer creditStatus )
    {
        long t0 = System.currentTimeMillis();

        String creditStatusStr = "";
        if ( creditStatus == CreditLimitConstants.CREDIT_NO_CHECK )
        {
            creditStatusStr = CreditLimitConstants.CREDIT_STATUS_NO_CHECK;
        }
        else if ( creditStatus == CreditLimitConstants.CREDIT_ACTIVE )
        {
            creditStatusStr = CreditLimitConstants.CREDIT_STATUS_ACTIVE;
        }
        else if ( creditStatus == CreditLimitConstants.CREDIT_SUSPEND )
        {
            creditStatusStr = CreditLimitConstants.CREDIT_STATUS_SUSPEND;
        }

        if ( log.isDebugEnabled() )
        {
            log.debug(new StringBuilder(200).append("CLAS.setCreditStatus : Begin. creditProviderOrg=")
                    .append(creditProviderOrg).append(",creditStatus=").append(creditStatusStr).toString());
        }

        if ( !validateCreditProvider(creditProviderOrg, "setCreditStatus") )
        {
            return;
        }

        IdcTransaction tx = null;
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet(creditProviderOrg);
            boolean suspend = creditStatus == CreditLimitConstants.CREDIT_SUSPEND;
            boolean creditEnabled = creditStatus != CreditLimitConstants.CREDIT_NO_CHECK;
            int crCreditStatus = CreditUtilC.getCreditStatus( clrs );
            if ( creditStatus.equals(crCreditStatus) )
            {
                log.info(new StringBuilder(200).append("CLAS.setCreditStatus : Same as specified value =")
                        .append(creditStatusStr).append(",existing=").append(creditStatusStr).append(",cpo=")
                        .append(creditProviderOrg.getShortName()).toString());
                return;
            }

            tx = startTransaction( ruleSetTransactionReadOnlyClasses );

            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();

            registeredClrs.setEnabled( creditEnabled );

            String crCreditStatusStr = crCreditStatus == CreditLimitConstants.CREDIT_NO_CHECK ? CreditLimitConstants.CREDIT_STATUS_NO_CHECK : ( crCreditStatus == CreditLimitConstants.CREDIT_SUSPEND ? CreditLimitConstants.CREDIT_STATUS_SUSPEND : CreditLimitConstants.CREDIT_STATUS_ACTIVE );
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setCreditStatus : clrs=" ).append( clrs )
                        .append( ",creditStatus=" ).append( creditStatusStr ).append( ",existing=" )
                        .append( crCreditStatusStr ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            if ( creditEnabled )
            {
                // set trading suspended/active
                registeredClrs.setTradingSuspended( suspend );
            }

            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelCreditStatus(creditProviderOrg, crCreditStatusStr, creditStatusStr);
            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            String eventName = creditEnabled ? ( suspend ? CreditMessageEvent.SUSPENDTRADING.getName() : CreditMessageEvent.ACTIVETRADING.getName() ) : CreditMessageEvent.NOCHECK.getName();
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, eventName );
            getTransaction().addRemoteFunctor( CreditEnableNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( eventName );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setCreditStatus : No remote notification functor registered. event=" + eventName );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append("CLAS.setCreditStatus : End. creditProviderOrg=")
                        .append(creditProviderOrg.getShortName()).append(",creditStatus=").append( creditStatusStr )
                        .append(" took ms=").append(System.currentTimeMillis() - t0).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setCreditStatus : org=" + creditProviderOrg.getShortName() + ", creditStatus=" + creditStatusStr, e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    public void setCreditStatus( Organization creditProviderOrg, TradingParty creditCpty, Integer creditStatus )
    {
        long t0 = System.currentTimeMillis();

        String creditStatusStr = "";
        if ( creditStatus == CreditLimitConstants.CREDIT_NO_CHECK )
        {
            creditStatusStr = CreditLimitConstants.CREDIT_STATUS_NO_CHECK;
        }
        else if ( creditStatus == CreditLimitConstants.CREDIT_ACTIVE )
        {
            creditStatusStr = CreditLimitConstants.CREDIT_STATUS_ACTIVE;
        }
        else if ( creditStatus == CreditLimitConstants.CREDIT_SUSPEND )
        {
            creditStatusStr = CreditLimitConstants.CREDIT_STATUS_SUSPEND;
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setCreditStatus : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append(",creditCpty=").append(creditCpty).append( ",creditStatus=" ).append( creditStatusStr ).toString() );
        }

        if ( !validateCreditCounterparty(creditProviderOrg, creditCpty, "setCreditStatus") )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(creditProviderOrg, creditTp);
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setCreditStatus : No cpty rule. creditStatus=" )
                        .append( creditStatusStr ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).toString() );
                return;
            }
            boolean suspend = creditStatus == CreditLimitConstants.CREDIT_SUSPEND;
            boolean creditEnabled = creditStatus != CreditLimitConstants.CREDIT_NO_CHECK;
            int crCreditStatus = CreditUtilC.getCreditStatus( cptyRule );
            if ( creditStatus.equals( crCreditStatus ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setCreditStatus : Same as specified value =" )
                        .append( creditStatusStr ).append(",existing=").append(creditStatusStr).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append(",creditCpty=").append(creditCpty.getShortName()).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );

            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();

            registeredCclr.setEnabled( creditEnabled );

            String crCreditStatusStr = crCreditStatus == CreditLimitConstants.CREDIT_NO_CHECK ? CreditLimitConstants.CREDIT_STATUS_NO_CHECK : ( crCreditStatus == CreditLimitConstants.CREDIT_SUSPEND ? CreditLimitConstants.CREDIT_STATUS_SUSPEND : CreditLimitConstants.CREDIT_STATUS_ACTIVE );
            if ( log.isDebugEnabled() )
            {
                log.debug(new StringBuilder(200).append("CLAS.setCreditStatus : cptyRule=").append(cptyRule).append(",creditStatus=")
                        .append(creditStatusStr).append(",existing=").append(crCreditStatusStr).append(",new tx=").append(tx != null).toString());
            }

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;
            if ( creditEnabled )
            {
                // set trading suspended/active
                registeredCclr.setTradingSuspended( suspend );
            }

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelCreditStatus(creditProviderOrg, isCptyOrgLevel, creditCpty,
                    creditCpty.getLegalEntityOrganization(), crCreditStatusStr, creditStatusStr);
            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            if ( !isCptyOrgLevel )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, creditCpty.getGUID() );
            }
            String eventName = creditEnabled ? ( suspend ? CreditMessageEvent.SUSPENDTRADING.getName() : CreditMessageEvent.ACTIVETRADING.getName() ) : CreditMessageEvent.NOCHECK.getName();
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, eventName );
            getTransaction().addRemoteFunctor( CreditEnableNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( eventName );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setCreditStatus : No remote notification functor registered. event=" + eventName );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setCreditStatus : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append(",creditCpty=").append(creditCpty.getShortName())
                        .append(",creditStatus=").append( creditStatusStr ).append(" took ms=").append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setCreditStatus : org=" + creditProviderOrg.getShortName() + ", creditStatus=" + creditStatusStr, e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    /**
     * Sets Currency Pair Group Exemption for Credit Provider
     *
     * @param cpo, credit provider
     * @param cpg  Currency Pair Group that is to be exempted from Credit check
     */
    public void setCurrencyPairGroupExemption( Organization cpo, CurrencyPairGroup cpg )
    {
        IdcTransaction tx = null;
        if ( !validateCreditProvider( cpo, "setCurrencyPairGroupExemption" ) )
        {
            return;
        }
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet(cpo);
            CurrencyPairGroup existingCpg = clrs.getExemptCurrencyPairGroup();
            if ( ( cpg == null && existingCpg == null ) || ( existingCpg != null && cpg != null && existingCpg.isSameAs( cpg ) ) )
            {
                log.info(new StringBuilder(200).append("CLAS.setCurrencyPairGroupExemption : Same as existing ccy pair group. newCpg=")
                        .append(cpg).append(",existing=").append(existingCpg).append(",cpo=").append(cpo.getShortName()).toString());
                return;
            }
            tx = startTransaction( ruleSetTransactionReadOnlyClasses );
            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();
            registeredClrs.setExemptCurrencyPairGroup( cpg );
            CreditLimitAdminAuditManagerC.getInstance().auditCurrencyPairGroupExemption(cpo, existingCpg, cpg);

            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            String eventName = "CurrencyPairExempt";
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, eventName );
            getTransaction().addRemoteFunctor( CreditCurrencyPairGroupNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( eventName );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setCreditStatus : No remote notification functor registered. event=" + eventName );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setCurrencyPairGroupExemption : org=" + cpo.getShortName() + ", CurrencyPairGroup=" + ( null != cpg ? cpg.getShortName() : "NA" ), e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    /**
     * Sets Currency Pair Group Exemption for Credit Provider --> Counterparty
     *
     * @param cpo credit provider
     * @param cc  credit counterparty
     * @param cpg Currency Pair Group that is to be exempted from Credit check
     */
    public void setCurrencyPairGroupExemption( Organization cpo, TradingParty cc, CurrencyPairGroup cpg )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setCurrencyPairGroupExemption : Begin. cpo=" )
                    .append( cpo ).append( ",cc=" ).append( cc ).append( ",cpg=" ).append( cpg ).toString() );
        }

        if ( !validateCreditCounterparty( cpo, cc, "setCurrencyPairGroupExemption" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( cc, cpo );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setCurrencyPairGroupExemption : No cpty rule. cpg=" )
                        .append( cpg ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }
            CurrencyPairGroup existingCpg = cptyRule.getExemptCurrencyPairGroup();

            if ( ( cpg == null && existingCpg == null ) || ( existingCpg != null && cpg != null && existingCpg.isSameAs( cpg ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setCurrencyPairGroupExemption : Same as existing ccy pair group=" )
                        .append( cpg ).append( ",existing=" ).append( existingCpg ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setCurrencyPairGroupExemption : cptyRule=" ).append( cptyRule ).append( ",cpg=" )
                        .append( cpg ).append( ",existing=" ).append( existingCpg ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            // set the exempt currency pair group
            CurrencyPairGroup regCpg = cpg != null ? ( CurrencyPairGroup ) cpg.getRegisteredObject() : null;
            registeredCclr.setExemptCurrencyPairGroup( regCpg );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelCurrencyPairGroupExemption(cpo, isCptyOrgLevel, cc, cc.getLegalEntityOrganization(), existingCpg, cpg);

            // add notification functors for updating exempt currency pairs.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.EXEMPTCURRENCYPAIRGROUP.getName() );
            if ( cptyRule.getTradingParty() != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cptyRule.getTradingParty().getGUID() );
            }
            getTransaction().addRemoteFunctor( CreditCurrencyPairGroupNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.EXEMPTCURRENCYPAIRGROUP.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setCurrencyPairGroupExemption : No remote notification functor registered. event=" + CreditMessageEvent.EXEMPTCURRENCYPAIRGROUP.getName() );
            }
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setCurrencyPairGroupExemption : End. cpo=" )
                        .append( cpo.getShortName() ).append( ",cc=" ).append( cc.getShortName() )
                        .append( ",cpg=" ).append( cpg ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setCurrencyPairGroupExemption : org=" + cpo.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    /**
     * Sets whether to use the default exempt currency pair group specified in the credit provider level.
     *
     * @param cpo              credit provider
     * @param cc               credit counterparty
     * @param useDefaultExempt use default exempt ccy pair group flag
     */
    public void setUseDefaultExemptCurrencyPairGroup( Organization cpo, TradingParty cc, boolean useDefaultExempt )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setUseDefaultExemptCurrencyPairGroup : Begin. cpo=" )
                    .append( cpo ).append( ",cc=" ).append( cc ).append( ",useDefaultExempt=" ).append( useDefaultExempt ).toString() );
        }

        if ( !validateCreditCounterparty( cpo, cc, "setUseDefaultExemptCurrencyPairGroup" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( cc, cpo );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setUseDefaultExemptCurrencyPairGroup : No cpty rule. useDefaultExempt=" )
                        .append( useDefaultExempt ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            final boolean existingUseDefault = cptyRule.isUseDefaultExemptCurrencyPairGroup();

            if ( existingUseDefault == useDefaultExempt )
            {
                log.info(new StringBuilder(200).append("CLAS.setUseDefaultExemptCurrencyPairGroup : Same as existing use default exempt flag=")
                        .append(existingUseDefault).append(",new=").append(useDefaultExempt).append(",cpo=").append(cpo.getShortName())
                        .append(",cc=").append(cc.getShortName()).toString());
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setUseDefaultExemptCurrencyPairGroup : cptyRule=" ).append( cptyRule ).append( ",useDefaultExempt=" )
                        .append( useDefaultExempt ).append( ",existing=" ).append( existingUseDefault ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            // set the new flag
            registeredCclr.setUseDefaultExemptCurrencyPairGroup(useDefaultExempt);

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;
            String event = useDefaultExempt ? CreditMessageEvent.USEDEFAULTEXEMPTCURRENCYPAIRGROUP.getName() : CreditMessageEvent.USECPTYLEVELEXEMPTCURRENCYPAIRGROUP.getName();

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelUseDefaultExemptCurrencyPairGroup(cpo, isCptyOrgLevel, cc, cc.getLegalEntityOrganization(), useDefaultExempt);

            // add notification functors for updating exempt currency pairs.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, event );
            if ( cptyRule.getTradingParty() != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cptyRule.getTradingParty().getGUID() );
            }
            getTransaction().addRemoteFunctor( CreditCurrencyPairGroupNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( event );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setUseDefaultExemptCurrencyPairGroup : No remote notification functor registered. event=" + event );
            }
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append("CLAS.setUseDefaultExemptCurrencyPairGroup : End. cpo=")
                        .append(cpo.getShortName()).append(",cc=").append( cc.getShortName() )
                        .append(",useDefaultExempt=").append( useDefaultExempt ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setUseDefaultExemptCurrencyPairGroup : org=" + cpo.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    /**
     * Sets Currency Pair Group Exemption for Credit Provider --> Counterparty
     *
     * @param cpo credit provider
     * @param cc  credit counterparty
     * @param cpg Currency Pair Group that is to be exempted from Credit check
     */
    public void setCurrencyPairGroupExemption( Organization cpo, TradingParty cc, CurrencyPairGroup cpg, boolean useDefaultExempt )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setCurrencyPairGroupExemption : Begin. cpo=" )
                    .append( cpo ).append( ",cc=" ).append( cc ).append( ",cpg=" ).append( cpg ).toString() );
        }

        if ( !validateCreditCounterparty( cpo, cc, "setCurrencyPairGroupExemption" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( cc, cpo );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setCurrencyPairGroupExemption : No cpty rule. cpg=" )
                        .append( cpg ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }
            CurrencyPairGroup existingCpg = cptyRule.getExemptCurrencyPairGroup();
            final boolean existingUseDefault = cptyRule.isUseDefaultExemptCurrencyPairGroup();
            final boolean defaultSame = useDefaultExempt == existingUseDefault;
            final boolean exemptSame = ( cpg == null && existingCpg == null ) || ( existingCpg != null && cpg != null && existingCpg.isSameAs( cpg ) );

            if ( defaultSame && exemptSame )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setCurrencyPairGroupExemption : Same as existing values. ExistingUseDefault=" )
                        .append( existingUseDefault ).append( ",new=" ).append( useDefaultExempt )
                        .append(",existingCpg=").append(existingCpg).append( ",newCpg=" ).append( cpg ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setCurrencyPairGroupExemption : Begin. cptyRule=" ).append( cptyRule ).append( ",ExistingUseDefault=" )
                        .append( existingUseDefault ).append(",new=").append( useDefaultExempt )
                        .append(",existingCpg=").append(existingCpg).append( ",newCpg=" ).append( cpg ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            // set the exempt currency pair group
            registeredCclr.setUseDefaultExemptCurrencyPairGroup( useDefaultExempt );
            CurrencyPairGroup regCpg = cpg != null ? ( CurrencyPairGroup ) cpg.getRegisteredObject() : null;
            registeredCclr.setExemptCurrencyPairGroup( regCpg );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelCurrencyPairGroupExemption(cpo, isCptyOrgLevel, cc, cc.getLegalEntityOrganization(), existingCpg, cpg, existingUseDefault, useDefaultExempt);

            // add notification functors for updating exempt currency pairs.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.EXEMPTCURRENCYPAIRGROUP.getName() );
            if ( cptyRule.getTradingParty() != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cptyRule.getTradingParty().getGUID() );
            }
            getTransaction().addRemoteFunctor( CreditCurrencyPairGroupNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.EXEMPTCURRENCYPAIRGROUP.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setCurrencyPairGroupExemption : No remote notification functor registered. event=" + CreditMessageEvent.EXEMPTCURRENCYPAIRGROUP.getName() );
            }
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setCurrencyPairGroupExemption : End. cptyRule=" ).append( cptyRule ).append( ",ExistingUseDefault=" )
                        .append( existingUseDefault ).append(",new=").append(useDefaultExempt)
                        .append(",existingCpg=").append( existingCpg ).append(",newCpg=").append( cpg ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setCurrencyPairGroupExemption : org=" + cpo.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void setCreditTenorProfile( Organization cpo, TradingParty cc, CreditTenorProfile ctp, boolean useDefaultTenorProfile )
    {
        getCreditPFEAdminService().setCreditTenorProfile(cpo, cc, ctp, useDefaultTenorProfile);
    }

    public void setDefaultCreditTenorProfile( Organization cpo, CreditTenorProfile ctp )
    {
        getCreditPFEAdminService().setDefaultCreditTenorProfile(cpo, ctp);
    }

    public void setCreditTenorProfile( Organization cpo, TradingParty cc, CreditTenorProfile ctp )
    {
        getCreditPFEAdminService().setCreditTenorProfile(cpo, cc, ctp);
    }

    public void setCreditTenorProfile( Organization cpo, TradingParty cc, CreditLimitClassification clsf, CreditTenorProfile ctp )
    {
        getCreditPFEAdminService().setCreditTenorProfile(cpo, cc, clsf, ctp);
    }

    public void setExternalCreditEnabled( Organization creditProviderOrg, TradingParty creditCpty, boolean externalCreditEnabled )
    {
        long t0 = System.currentTimeMillis();

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setExternalCreditEnabled : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",externalCreditEnabled=" ).append( externalCreditEnabled ).toString() );
        }

        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "setExternalCreditEnabled" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setExternalCreditEnabled : No cpty rule. externalCreditEnabled=" )
                        .append( externalCreditEnabled ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).toString() );
                return;
            }

            boolean existingExternalCreditEnabled = cptyRule.isExternalCreditEnabled();

            if ( externalCreditEnabled == existingExternalCreditEnabled )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setExternalCreditEnabled : Same as specified value =" )
                        .append( existingExternalCreditEnabled ).append( ",existing=" ).append( existingExternalCreditEnabled ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );

            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();
            registeredCclr.setExternalCreditEnabled( externalCreditEnabled );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setExternalCreditEnabled : cptyRule=" ).append( cptyRule ).append( ",externalCreditEnabled=" )
                        .append( externalCreditEnabled ).append( ",existing=" ).append( existingExternalCreditEnabled ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelExternalCreditEnabled( creditProviderOrg, isCptyOrgLevel, creditCpty,
                    creditCpty.getLegalEntityOrganization(), externalCreditEnabled );

            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            if ( !isCptyOrgLevel )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, creditCpty.getGUID() );
            }
            String eventName = CreditMessageEvent.UPDATE_EXTERNAL_CREDITLIMIT_PROVIDER.getName();
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, eventName );
            getTransaction().addRemoteFunctor( CreditEnableNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( eventName );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setExternalCreditLimitProvider : No remote notification functor registered. event=" + eventName );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setExternalCreditLimitProvider : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( ",creditCpty=" ).append( creditCpty.getShortName() )
                        .append( ",externalCreditEnabled=" ).append( externalCreditEnabled ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setExternalCreditEnabled : org=" + creditProviderOrg.getShortName() + ", externalCreditEnabled=" + externalCreditEnabled, e );
            CreditUtilC.releaseTransaction( tx );
        }
    }

    public void setExternalCreditLimitProvider( Organization creditProviderOrg, TradingParty creditCpty, String externalCreditLimitProvider )
    {
        long t0 = System.currentTimeMillis();

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setExternalCreditLimitProvider : Begin. creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCpty=" ).append( creditCpty ).append( ",externalCreditLimitProvider=" ).append( externalCreditLimitProvider ).toString() );
        }

        if ( !validateCreditCounterparty( creditProviderOrg, creditCpty, "externalCreditLimitProvider" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderOrg, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setExternalCreditLimitProvider : No cpty rule. externalCreditLimitProvider=" )
                        .append( externalCreditLimitProvider ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).toString() );
                return;
            }

            String existingLimitProvider = cptyRule.getExternalCreditLimitProvider();

            if ( ( existingLimitProvider == null && externalCreditLimitProvider == null ) ||
                    ( existingLimitProvider != null && externalCreditLimitProvider != null && externalCreditLimitProvider.equals( existingLimitProvider ) ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setExternalCreditLimitProvider : Same as specified value =" )
                        .append( externalCreditLimitProvider ).append( ",existing=" ).append( existingLimitProvider ).append( ",creditProviderOrg=" ).append( creditProviderOrg.getShortName() )
                        .append( ",creditCpty=" ).append( creditCpty.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );

            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();
            registeredCclr.setExternalCreditLimitProvider( externalCreditLimitProvider );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setExternalCreditLimitProvider : cptyRule=" ).append( cptyRule ).append( ",externalCreditLimitProvider=" )
                        .append( externalCreditLimitProvider ).append( ",existing=" ).append( existingLimitProvider ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelExternalCreditLimitProvider( creditProviderOrg, isCptyOrgLevel, creditCpty,
                    creditCpty.getLegalEntityOrganization(), existingLimitProvider, externalCreditLimitProvider );

            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            if ( !isCptyOrgLevel )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, creditCpty.getGUID() );
            }
            String eventName = CreditMessageEvent.UPDATE_EXTERNAL_CREDITLIMIT_PROVIDER.getName();
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, eventName );
            getTransaction().addRemoteFunctor( CreditEnableNotificationFunctorC.class.getName(), propertiesMap );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( eventName );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setExternalCreditLimitProvider : No remote notification functor registered. event=" + eventName );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setExternalCreditLimitProvider : End. creditProviderOrg=" )
                        .append( creditProviderOrg.getShortName() ).append( ",creditCpty=" ).append( creditCpty.getShortName() )
                        .append( ",externalCreditLimitProvider=" ).append( externalCreditLimitProvider ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setExternalCreditLimitProvider : org=" + creditProviderOrg.getShortName() + ", externalCreditLimitProvider=" + externalCreditLimitProvider, e );
            CreditUtilC.releaseTransaction( tx );
        }
    }
    public void setOrgDefault( Organization cpo,  Organization cco, TradingParty creditTp, boolean orgDefault )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setOrgDefault : Begin. cpo=" )
                    .append( cpo.getShortName() ).append( ",cc=" ).append( cco.getShortName() ).append( ",cpg=" ).append( orgDefault ).toString() );
        }
        if ( !CreditUtilC.validateCreditCounterpartyOrg(cpo, cco, "setOrgDefault") )
        {
            return;
        }

        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule tpCclr = CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule(cpo, creditTp);
            if ( tpCclr != null && !tpCclr.isOrgLevel() )
            {
                log.info(new StringBuilder(200).append("CLAS.setOrgDefault :  Credit Configuration org level. creditProviderOrg=")
                        .append(cpo.getShortName()).append(",creditCptyOrg=").append(cco).toString());
                return;
            }
            boolean existingOrgDefault = tpCclr.isOrgDefault();
            final boolean sameAsExisting = orgDefault == existingOrgDefault;
            if ( sameAsExisting )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setOrgDefault : Same as existing values").append(",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cco.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) tpCclr.getRegisteredObject();

            if (log.isDebugEnabled()) {
                log.debug(new StringBuilder(200).append("CLAS.setOrgDefault : updating orgDefault").append(",cpo=").
                        append(cpo.getShortName()).append(",ExistingOrgDefault=")
                        .append(existingOrgDefault).append(",new=").append(orgDefault)
                        .append(",cc=").append(cco.getShortName()).append(",new tx=").append(tx != null).toString());
            }

            // set the exempt currency pair group
            registeredCclr.setOrgDefault(orgDefault);

            CreditLimitAdminAuditManagerC.getInstance().auditOrgDefault(cpo, cco, creditTp, existingOrgDefault, orgDefault);
            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, tpCclr.getTradingPartyOrganization().getGUID() );
            String eventName = CreditMessageEvent.SETORGDEFAULT.getName();
            propertiesMap.put(CreditLimitConstants.EVENT_PROPERTY, eventName);
            propertiesMap.put(CreditLimit.CREDIT_COUNTERPARTY, creditTp.getGUID());
            getTransaction().addRemoteFunctor(CreditTenorConfigChanges.class.getName(), propertiesMap);

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append("CLAS.setOrgDefault : End. cptyRule=").append(tpCclr ).append( ",ExistingOrgDefault=")
                        .append( existingOrgDefault).append(",new=").append(orgDefault).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setOrgDefault : org=" + cpo.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void setLEOverride( Organization cpo, Organization cco, boolean leOverride )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setLEOverride : Begin. cpo=" )
                    .append( cpo.getShortName() ).append( ",cc=" ).append( cco.getShortName() ).append( ",cpg=" ).append( leOverride ).toString() );
        }
        if ( !CreditUtilC.validateCreditCounterpartyOrg(cpo, cco, "setLEOverride") )
        {
            return;
        }

        IdcTransaction tx = null;
        try
        {
            LegalEntity creditCptyLe = CreditUtilC.getDefaultlegalEntity(cco);
            TradingParty creditTp = CounterpartyUtilC.getTradingParty(creditCptyLe, cpo);
            if ( creditTp == null )
            {
                log.warn(new StringBuilder(200).append("CLAS.setLEOverride : No credit cpty. creditProviderOrg=")
                        .append(cpo.getShortName()).append(",creditCptyOrg=").append(cco.getShortName()).toString());
                return;
            }

            CounterpartyCreditLimitRule activeCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty(cpo, creditTp);
            if ( activeCclr != null && !activeCclr.isActive() && activeCclr.isOrgLevel() )
            {
                log.info(new StringBuilder(200).append("CLAS.setLEOverride : Inactive Credit Configuration org level. creditProviderOrg=")
                        .append(cpo.getShortName()).append(",creditCptyOrg=").append(cco).toString());
                return;
            }
            boolean existingLeOverride = activeCclr.isLeOverride();
            final boolean sameLEOverride = leOverride == existingLeOverride;
            if ( sameLEOverride )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setLEOverride : Same as existing values").append(",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cco.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) activeCclr.getRegisteredObject();

            if (log.isDebugEnabled()) {
                log.debug(new StringBuilder(200).append("CLAS.setLEOverride : updating leOverride").append(",cpo=").
                        append(cpo.getShortName()).append(",Existing LeOverride=")
                        .append(existingLeOverride).append(",new=").append(leOverride)
                        .append(",cc=").append(cco.getShortName()).append(",new tx=").append(tx != null).toString());
            }

            // set the exempt currency pair group
            registeredCclr.setLeOverride(leOverride);


            CreditLimitAdminAuditManagerC.getInstance().auditLEOverride(cpo, cco, creditTp, existingLeOverride, leOverride);
            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, activeCclr.getTradingPartyOrganization().getGUID() );
            String eventName = CreditMessageEvent.SETLEOVERRIDE.getName();
            propertiesMap.put(CreditLimitConstants.EVENT_PROPERTY, eventName);
            getTransaction().addRemoteFunctor(CreditTenorConfigChanges.class.getName(), propertiesMap);
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append("CLAS.setLEOverride : End. cptyRule=").append(activeCclr ).append( ",ExistingUseDefault=")
                        .append( existingLeOverride).append(",new=").append(leOverride).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setLEOverride : org=" + cpo.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    /**
     * Sets whether to use the default tenor profile specified in the credit provider level.
     *
     * @param cpo                    credit provider
     * @param cc                     credit counterparty
     * @param useDefaultTenorProfile use default tenor profile flag
     */
    public void setUseDefaultTenorProfile( Organization cpo, TradingParty cc, boolean useDefaultTenorProfile )
    {
        getCreditPFEAdminService().setUseDefaultTenorProfile( cpo, cc, useDefaultTenorProfile );
    }

    public void addCreditTenorProfile( Organization cpo, CreditTenorProfile ctp )
    {
        getCreditPFEAdminService().addCreditTenorProfile( cpo, ctp );
    }

    public void addPfeConfiguration( Organization cpo, PFEConfiguration pfeConfiguration )
    {
        getCreditPFEAdminService().addPfeConfiguration( cpo, pfeConfiguration );
    }

    public CreditTenorProfile getCreditTenorProfile( Organization creditProviderOrg,String profileName )
    {
        return getCreditPFEAdminService().getCreditTenorProfile(creditProviderOrg,profileName);

    }
    public PFEConfiguration getPfeConfiguration( Organization creditProviderOrg,String pfeName )
    {
       return getCreditPFEAdminService().getPfeConfiguration(creditProviderOrg,pfeName);
    }

    public void updatePfeConfiguration( Organization cpo, PFEConfiguration pfeConfiguration )
    {
        getCreditPFEAdminService().updatePfeConfiguration( cpo, pfeConfiguration );
    }

    public void removePfeConfiguration( Organization cpo, PFEConfiguration pfeConfiguration )
    {
        getCreditPFEAdminService().removePfeConfiguration( cpo, pfeConfiguration );
    }

    public void addPfeConfigurationProfile( Organization cpo, PFEConfiguration pfeConfiguration, Collection<PFEConfigurationProfile> pfeConfigurationProfiles )
    {
        getCreditPFEAdminService().addPfeConfigurationProfile( cpo, pfeConfiguration, pfeConfigurationProfiles );
    }

    public void removePfeConfigurationProfiles( Organization cpo, PFEConfiguration pfeConfiguration, Collection<PFEConfigurationProfile> pfeConfigurationProfiles )
    {
        getCreditPFEAdminService().removePfeConfigurationProfiles( cpo, pfeConfiguration, pfeConfigurationProfiles );
    }

    public void setPFEConfiguration( Organization cpo, PFEConfiguration pfeConfiguration )
    {
        getCreditPFEAdminService().setPFEConfiguration( cpo, pfeConfiguration );
    }

    public void setPFEConfiguration( Organization cpo, TradingParty cc, PFEConfiguration pfeConfiguration )
    {
        getCreditPFEAdminService().setPFEConfiguration( cpo, cc, pfeConfiguration );
    }

    public void setPFEConfiguration( Organization cpo, TradingParty cc, CreditLimitClassification clsf, PFEConfiguration pfeConfiguration )
    {
        getCreditPFEAdminService().setPFEConfiguration( cpo, cc, clsf, pfeConfiguration );
    }

    public void removeCreditTenorProfile( Organization cpo, CreditTenorProfile ctp )
    {
        getCreditPFEAdminService().removeCreditTenorProfile( cpo, ctp );
    }

    public void addCreditTenorParameters( Organization cpo, CreditTenorProfile ctp, Collection<CreditTenorParameters> creditTenorParameters )
    {
        getCreditPFEAdminService().addCreditTenorParameters( cpo, ctp, creditTenorParameters );
    }

    public void removeCreditTenorParameters( Organization cpo, CreditTenorProfile ctp, Collection<CreditTenorParameters> creditTenorParameters )
    {
        getCreditPFEAdminService().removeCreditTenorParameters( cpo, ctp, creditTenorParameters );
    }

    /**
     * Enable to use PFE Configuration instead of Credit Tenor Profile
     * for the combination for Credit Provider / Cpty Org
     * @param cpo
     * @param usePFEConfig
     */
    public void setUsePFEConfiguration( Organization cpo,  boolean usePFEConfig )
    {
        getCreditPFEAdminService().setUsePFEConfiguration( cpo,  usePFEConfig );
    }

    /**
     * Enable to use PFE Configuration instead of Credit Tenor Profile
     * for the combination for Credit Provider / Cpty Org
     * @param cpo
     * @param cc
     * @param usePFEConfig
     */
    public void setUsePFEConfiguration( Organization cpo, TradingParty cc, boolean usePFEConfig )
    {
        getCreditPFEAdminService().setUsePFEConfiguration( cpo, cc, usePFEConfig );
    }

    public void setUsePFEConfiguration( Organization cpo, TradingParty cc, CreditLimitClassification clsf, boolean usePFEConfig )
    {
        getCreditPFEAdminService().setUsePFEConfiguration( cpo, cc, clsf, usePFEConfig );
    }

    public CreditTenorProfileDetails getCreditTenorProfiles( Organization cpo, TradingParty creditCpty, String profileOrConfigName, boolean usePFE)
    {
    	return getCreditPFEAdminService().getCreditTenorProfiles(cpo, creditCpty, profileOrConfigName, usePFE);
    }

    public void persist (Organization organization, PfeConfiguration model )
    {
        getCreditPFEAdminService().persist(organization,model);
    }

    public void setDefaultMode( Organization cpo, int mode )
    {
        IdcTransaction tx = null;
        if ( !validateCreditProvider( cpo, "setDefaultMode" ) )
        {
            return;
        }
        try
        {
            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( cpo );
            int existingMode = clrs.getMode();
            if ( mode == existingMode )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setDefaultMode : Same as existing mode=" )
                        .append( existingMode ).append( ",new=" ).append( mode ).append( ",cpo=" ).append( cpo.getShortName() ).toString() );
                return;
            }
            tx = startTransaction( ruleSetTransactionReadOnlyClasses );
            CreditLimitRuleSet registeredClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();
            registeredClrs.setMode( mode );
            String action = CreditMessageEvent.SETCREDITMODE.getName();
            CreditLimitAdminAuditManagerC.getInstance().auditOrgLevelCreditMode( cpo, existingMode, mode, action );

            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            String eventName = CreditMessageEvent.SETCREDITMODE.getName();
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, eventName );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( eventName );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setDefaultMode : No remote notification functor registered. event=" + eventName );
            }

            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setDefaultMode : org=" + cpo.getShortName() + ",mode=" + mode, e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void setPFEExcludeForDailyExposure( Organization cpo, int pFEExcludeForDailyExposure )
    {
        getCreditPFEAdminService().setPFEExcludeForDailyExposure( cpo, pFEExcludeForDailyExposure );
    }

    public void setPFEExcludeForDailyExposure( Organization cpo, TradingParty cc, int pFEExcludeForDailyExposure )
    {
        getCreditPFEAdminService().setPFEExcludeForDailyExposure( cpo, cc, pFEExcludeForDailyExposure );
    }

    public void setMode( Organization cpo, TradingParty cc, Integer mode )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setMode : Begin. cpo=" )
                    .append( cpo ).append( ",cc=" ).append( cc ).append( ",mode=" ).append( mode ).toString() );
        }

        if ( !validateCreditCounterparty( cpo, cc, "setMode" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( cc, cpo );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setMode : No cpty rule. mode=" )
                        .append( mode ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }
            Integer existingMode = cptyRule.getMode();

            if ( ( mode == null && existingMode == null ) || ( existingMode != null && mode != null && existingMode == mode ) )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setMode : Same as existing mode=" )
                        .append( existingMode ).append( ",newMode=" ).append( mode ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setMode : cptyRule=" ).append( cptyRule ).append( ",mode=" )
                        .append( mode ).append( ",existingMode=" ).append( existingMode ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            registeredCclr.setMode( mode );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelCreditMode( cpo, isCptyOrgLevel, cc, cc.getLegalEntityOrganization(), existingMode, mode );

            // add notification functors for setting the mode
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            if ( !isCptyOrgLevel )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cc.getGUID() );
            }
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETCREDITMODE.getName() );

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETCREDITMODE.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setMode : No remote notification functor registered. event=" + CreditMessageEvent.SETCREDITMODE.getName() );
            }
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setMode : End. cpo=" )
                        .append( cpo.getShortName() ).append( ",cc=" ).append( cc.getShortName() )
                        .append( ",mode=" ).append( mode ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setMode : org=" + cpo.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }


    /**
     * Sets whether to use the default credit mode configured in the credit provider level for the credit counterparty specified.
     *
     * @param cpo            credit provider
     * @param cc             credit counterparty
     * @param useDefaultMode use default credit mode flag
     */
    public void setUseDefaultMode( Organization cpo, TradingParty cc, boolean useDefaultMode )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setUseDefaultMode : Begin. cpo=" )
                    .append( cpo ).append( ",cc=" ).append( cc ).append( ",useDefaultMode=" ).append( useDefaultMode ).toString() );
        }

        if ( !validateCreditCounterparty( cpo, cc, "setUseDefaultMode" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( cc, cpo );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setUseDefaultMode : No cpty rule. useDefaultMode=" )
                        .append( useDefaultMode ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            final boolean existingUseDefault = cptyRule.isUseDefaultMode();

            if ( existingUseDefault == useDefaultMode )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setUseDefaultMode : Same as existing use default credit mode flag=" )
                        .append( existingUseDefault ).append( ",new=" ).append( useDefaultMode ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setUseDefaultMode : cptyRule=" ).append( cptyRule ).append( ",useDefaultMode=" )
                        .append( useDefaultMode ).append( ",existing=" ).append( existingUseDefault ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            // set the new flag
            registeredCclr.setUseDefaultMode( useDefaultMode );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;
            String event = useDefaultMode ? CreditMessageEvent.USEDEFAULTCREDITMODE.getName() : CreditMessageEvent.USECPTYLEVELCREDITMODE.getName();

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelUseDefaultMode( cpo, isCptyOrgLevel, cc, cc.getLegalEntityOrganization(), useDefaultMode );

            // add notification functors for updating exempt currency pairs.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, event );
            if ( cptyRule.getTradingParty() != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cptyRule.getTradingParty().getGUID() );
            }

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( event );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setUseDefaultMode : No remote notification functor registered. event=" + event );
            }
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setUseDefaultMode : End. cpo=" )
                        .append( cpo.getShortName() ).append( ",cc=" ).append( cc.getShortName() )
                        .append( ",useDefaultMode=" ).append( useDefaultMode ).append( " took ms=" )
                        .append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setUseDefaultMode : org=" + cpo.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }

    public void setMode( Organization cpo, TradingParty cc, Integer mode, boolean useDefaultMode )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.setMode : Begin. cpo=" )
                    .append( cpo ).append( ",cc=" ).append( cc ).append( ",mode=" ).append( mode )
                    .append( ",useDefaultMode=" ).append( useDefaultMode ).toString() );
        }

        if ( !validateCreditCounterparty( cpo, cc, "setMode" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( cc, cpo );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );
            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.setMode : No cpty rule. mode=" )
                        .append( mode ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            Integer existingMode = cptyRule.getMode();
            final boolean existingUseDefault = cptyRule.isUseDefaultMode();
            final boolean defaultSame = useDefaultMode == existingUseDefault;
            final boolean modeSame = ( mode == null && existingMode == null ) || ( existingMode != null && mode != null && existingMode == mode );

            if ( defaultSame && modeSame )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.setMode : Same as existing values. use default credit mode flag=" )
                        .append( existingUseDefault ).append( ",new=" ).append( useDefaultMode )
                        .append( ", exsitingMode=" ).append( existingMode ).append( ",newMode=" ).append( mode ).append( ",cpo=" ).append( cpo.getShortName() )
                        .append( ",cc=" ).append( cc.getShortName() ).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setMode : cptyRule=" ).append( cptyRule )
                        .append( ",existingUseDefault=" ).append( existingUseDefault ).append( ",new=" ).append( useDefaultMode )
                        .append( ", exsitingMode=" ).append( existingMode ).append( ",newMode=" ).append( mode ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            registeredCclr.setMode( mode );
            registeredCclr.setUseDefaultMode( useDefaultMode );

            boolean isCptyOrgLevel = cptyRule.getTradingParty() == null;

            CreditLimitAdminAuditManagerC.getInstance().auditCounterpartyLevelCreditMode( cpo, isCptyOrgLevel, cc, cc.getLegalEntityOrganization(), existingMode, mode );

            // add notification functors for setting the mode
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETCREDITMODE.getName() );
            if ( cptyRule.getTradingParty() != null )
            {
                propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cptyRule.getTradingParty().getGUID() );
            }

            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETCREDITMODE.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.setMode : No remote notification functor registered. event=" + CreditMessageEvent.SETCREDITMODE.getName() );
            }
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.setMode : End. cpo=" )
                        .append( cpo.getShortName() ).append( ",cc=" ).append( cc.getShortName() )
                        .append( ",mode=" ).append( mode ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.setMode : org=" + cpo.getShortName(), e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
        }
    }
    public void addCreditTenorProfile( Organization cpo, PFEConfigurationProfile pfeConfigurationProfile,CreditTenorProfile ctp )
    {
        getCreditPFEAdminService().addCreditTenorProfile(cpo, pfeConfigurationProfile, ctp);
    }

    public void updateCreditTenorProfile( Organization cpo, PFEConfigurationProfile pfeConfigurationProfile,CreditTenorProfile ctp )
    {
        getCreditPFEAdminService().updateCreditTenorProfile( cpo, pfeConfigurationProfile,ctp );
    }
    public void updateCreditTenorProfile( Organization cpo, CreditTenorProfile ctp )
    {
        getCreditPFEAdminService().updateCreditTenorProfile( cpo, ctp );
    }

    public boolean isTenorRestrictionInBusinessDays( Organization cpo, TradingParty cc )
    {
        return isTenorInBusinessDays( cpo, cc, false );
    }

    public void setTenorRestrictionInBusinessDays( Organization cpo, TradingParty cc, boolean businessDays )
    {
        updateTenorRestrictionsInBusinessDays( cpo, cc, false, businessDays );
    }

    public boolean isCptyLETenorRestrictionInBusinessDays( Organization cpo, TradingParty cc )
    {
        return isTenorInBusinessDays( cpo, cc, true );
    }

    public void setCptyLETenorRestrictionInBusinessDays( Organization cpo, TradingParty cc, boolean businessDays )
    {
        updateTenorRestrictionsInBusinessDays( cpo, cc, true, businessDays );
    }

    public CreditWorkflowMessage deposit( Organization cpo, TradingParty cc, Currency ccy, double amount, String transactionId, String notes, String ticket, String extTxId )
    {
        Organization cco = null;
        CreditWorkflowMessage cwm = CreditUtilC.initialiseCreditWorkflowMessage ( cc, cc.getLegalEntityOrganization (), cpo.getDefaultDealingEntity () );
        cwm.setEvent ( CreditMessageEvent.DEPOSIT );
        CreditUtilization cu = null;
        boolean ccyPositionUpdated = false;
        CreditUtilizationEvent aggCue = null;
        try
        {
            if ( !validateCreditCounterparty ( cpo, cc, "deposit" ) )
            {
                log.warn ( "CLAS.deposit : Invalid credit relationship. cpo=" + cpo.getShortName () + ",cc=" + cc  + ",transactionId=" + transactionId );
                cwm = CreditUtilC.getCreditWorkflowMessageWithError ( cwm, AccountManagementConstants.NOT_ACCOUNT_ENABLED_ERROR );
                return cwm;
            }
            if ( ! CreditUtilC.validateCreditLimit ( amount ) )
            {
                log.warn ( "CLAS.deposit : Invalid deposit amount. cpo=" + cpo.getShortName () + ",cc=" + cc  + ",amount=" + amount );
                cwm = CreditUtilC.getCreditWorkflowMessageWithError ( cwm, AccountManagementConstants.INVALID_AMOUNT_ERROR );
                return cwm;
            }

            if ( StringUtilC.isNullOrEmpty ( transactionId ) )
            {
                log.warn ( "CLAS.deposit : Invalid transaction Id. cpo=" + cpo.getShortName () + ",cc=" + cc  + ",transactionId=" + transactionId );
                cwm = CreditUtilC.getCreditWorkflowMessageWithError ( cwm, AccountManagementConstants.INVALID_DEPOSIT_WITHDRAW_TRANSACTION_ID_ERROR );
                return cwm;
            }

            if ( StringUtilC.isNullOrEmpty ( extTxId ) )
            {
                log.warn ( "CLAS.deposit : Invalid external transaction Id. cpo=" + cpo.getShortName () + ",cc=" + cc  + ",extTxId=" + extTxId );
                cwm = CreditUtilC.getCreditWorkflowMessageWithError ( cwm, AccountManagementConstants.INVALID_EXTERNAL_TRANSACTIONID_ERROR );
                return cwm;
            }

            TradingParty creditTp = CounterpartyUtilC.getTradingParty ( cc, cpo );
            cco = creditTp.getLegalEntityOrganization ();
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( cpo, creditTp );
            if ( cclr == null )
            {
                log.warn ( "CLAS.deposit : No counterparty rule found for org=" + cpo.getShortName () + ",creditCpty=" + creditTp  + ",cco=" + cco );
                cwm = CreditUtilC.getCreditWorkflowMessageWithError ( cwm, AccountManagementConstants.NOT_ACCOUNT_ENABLED_ERROR );
                return cwm;
            }
            SingleCreditLimitRule clr = ( SingleCreditLimitRule ) cclr.getChildRule ( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
            if ( clr == null || ! CreditLimitConstants.AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR.isSameAs ( clr.getCreditUtilizationCalculator () ) )
            {
                log.warn ( new StringBuilder ( 200 ).append ( "CLAS.deposit : No credit limit rule or not cash settlement methodology for cpo=" )
                        .append ( cpo ).append ( ",creditCpty=" ).append ( cc ).append ( ",cclr=" )
                        .append ( cclr ).append ( ",cco=" ).append ( cco ).toString () );
                cwm = CreditUtilC.getCreditWorkflowMessageWithError ( cwm, AccountManagementConstants.NOT_ACCOUNT_ENABLED_ERROR );
                return cwm;
            }

            double roundedAmt = ccy.round ( amount );

            log.info ( new StringBuilder ( 200 ).append ( "CLAS.deposit : deposit initiated for cpo=" )
                    .append ( cpo ).append ( ",creditCpty=" ).append ( cc ).append ( ",cco=" ).append ( cco )
                    .append ( ",ccy=" ).append ( ccy ).append ( ",amount=" ).append ( amount )
                    .append ( ",roundedAmount=" ).append ( roundedAmt ).append( ",transactionId=" )
                    .append ( transactionId  ).append ( ",extTxId=" ).append ( extTxId ).toString () );

            try
            {
                cclr.getLock ().lock ();
                IdcTransaction tx = startTransaction ( readOnlyClasses );
                addReadOnlyClass ( tx, CreditLimitOrgFunctionC.class );
                addReadOnlyClass ( tx, CounterpartyCreditLimitRuleC.class );
                addReadOnlyClass ( tx, CreditLimitRuleSetC.class );
                addReadOnlyClass ( tx, CreditUtilizationCalculatorC.class );
                addReadOnlyClass ( tx, UserC.class );
                CreditLimitRule registeredClr = ( CreditLimitRule ) clr.getRegisteredObject ();
                registeredClr.update ();

                cu = clr.getCreditUtilization ();
                CreditUtilization registeredCu = ( CreditUtilization ) cu.getRegisteredObject ();

                aggCue = CreditLimitFactory.newCreditUtilizationEvent ();
                aggCue = ( CreditUtilizationEvent ) aggCue.getRegisteredObject ();
                aggCue.setNamespace ( cpo.getNamespace () );
                aggCue.setCreditUtilization ( registeredCu );
                aggCue.setTradingParty ( creditTp );
                aggCue.setTradingPartyOrganization ( creditTp.getLegalEntityOrganization () );
                aggCue.setInstrument ( clr.getCurrency () );

                aggCue.setPrincipalCurrency ( ccy );
                aggCue.setPrincipal ( roundedAmt );
                aggCue.setTradePrincipalAmount ( roundedAmt );

                aggCue.setPriceCurrency ( ccy );
                aggCue.setPrice ( 0.0 );
                aggCue.setTradePriceAmount ( 0.0 );
                aggCue.setLastAction ( CreditLimit.ACTION_APPLY );
                aggCue.setTenorCoefficient ( 1.0 );

                aggCue.setBuySell ( CreditLimit.SELL );
                aggCue.setSettlementDate ( CreditLimitConstants.MAX_VALUE_DATE );
                aggCue.setTradeDate ( EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate () );
                aggCue.setCounterpartyCreditLimitRule ( cclr );
                aggCue.setTransactionId ( transactionId );

                registeredCu.getCurrencyPositions ( true ).addCreditUtilizationEvent ( aggCue, false, false );// do not update last updated time
                ccyPositionUpdated = true;
                registeredCu.setPositions ( registeredCu.getCurrencyPositions ().getPositions () );
                registeredCu.setUsedAmount ( registeredClr.getCreditUtilizationCalculator ().getRealtimeUtilizationAmount ( registeredCu ) );
                CurrencyPosition prinCcyPos = registeredCu.getCurrencyPositions ().getCurrencyPosition ( ccy );
                aggCue.setPrincipalBalance ( prinCcyPos != null ? prinCcyPos.getNetAmountBigDecimal () : BigDecimal.ZERO );
                aggCue.setEventType ( AccountEnums.AccountEventType.DEPOSIT );
                aggCue.setUser ( IdcUtilC.getSessionContextUser () );
                aggCue.setNotes ( notes );
                aggCue.setTicket ( ticket );
                aggCue.setExternalTransactionId ( extTxId );

                // add a commit handler for recalculation of credit utilization.
                addCreditUtilizationRecalculationCommitHandler ( new CreditUtilizationRecalculationHandlerC ( clr ) );

                // add notification functors for adding the deposits.
                HashMap<String, String> propertiesMap = new HashMap<String, String> ( 3 );
                propertiesMap.put ( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID () );
                propertiesMap.put ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cclr.getTradingPartyOrganization ().getGUID () );
                if ( cclr.getTradingParty() != null )
                {
                    propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cclr.getTradingParty().getGUID () );
                }
                String eventName = cwm.getEventName ();
                propertiesMap.put ( CreditLimitConstants.EVENT_PROPERTY, cwm.getEventName () );
                addDepositWithdrawRemoteFunctor( propertiesMap );

                // Adds the remote notification functor registered for the event.
                Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance ().getCreditRemoteTransactionFunctor ( eventName );
                if ( remoteFunctor != null )
                {
                    getTransaction ().addRemoteFunctor ( remoteFunctor.getName (), propertiesMap );
                }
                else
                {
                    log.info ( "CLAS.deposit : No remote notification functor registered. event=" + eventName );
                }

                endTransaction ( tx, true, false );

                boolean isCptyOrgLevel = cclr.getTradingParty() == null;
                CreditLimitAdminAuditManagerC.getInstance().auditDeposit( cpo, isCptyOrgLevel, cc, cc.getLegalEntityOrganization(), ccy, amount );

                //send email notification
                cwm.setTransactionId ( transactionId );
                BigDecimal newBalance = cu.getCurrencyPositions ().getCurrencyPosition ( ccy ).getNetAccountAmountBigDecimal ();
                cwm.setParameterValue ( CreditLimit.DEPOSIT_WITHDRAW_AMOUNT_PARAM, amount );
                cwm.setParameterValue ( CreditLimit.DEPOSIT_WITHDRAW_CURRENCY_PARAM, ccy.getShortName () );
                cwm.setParameterValue ( CreditLimit.DEPOSIT_WITHDRAW_BALANCE_AMOUNT_PARAM, newBalance );
                cwm.setParameterValue ( CreditLimit.DEPOSIT_WITHDRAW_AVAILABLE_AMOUNT_PARAM, newBalance );
                cwm.setParameterValue ( CreditLimit.DEPOSIT_WITHDRAW_USER_PARAM, IdcUtilC.getSessionContextUser ().getFullyQualifiedName () );
                CreditNotificationManagerC.getInstance ().sendCreditDepositWithdrawEmail ( cwm );

                log.info ( new StringBuilder ( 200 ).append ( "CLAS.deposit : deposit successful for cpo=" )
                        .append ( cpo ).append ( ",creditCpty=" ).append ( cc ).append ( ",cco=" ).append ( cco )
                        .append ( ",ccy=" ).append ( ccy ).append ( ",amount=" ).append ( amount )
                        .append ( ",roundedAmount=" ).append( ",transactionId=" ).append ( transactionId  )
                        .append ( roundedAmt ).append ( ",extTxId=" ).append ( extTxId ).toString () );
            }
            finally
            {
                cclr.getLock ().unlock ();
            }

        }
        catch ( Exception e )
        {
            boolean dbException = e instanceof IdcDatabaseException;
            boolean extTxIdConstraint = false;
            if ( dbException )
            {
                IdcDatabaseException dbe = ( IdcDatabaseException ) e;
                if ( dbe.getNestedException () != null && dbe.getNestedException ().getMessage () != null
                        && dbe.getNestedException ().getMessage ().contains ( AccountManagementConstants.DEPOSIT_WITHDRAW_EXTERNAL_TXID_UNIQUE_CONSTRAINT_NAME ) )
                {
                    extTxIdConstraint = true;
                }
            }
            String errorLogStr = new StringBuilder ( 200 ).append ( "CLAS.deposit : deposit failed for cpo=" )
                    .append ( cpo ).append ( ",creditCpty=" ).append ( cc ).append ( ",cco=" ).append ( cco ).append ( ",ccy=" )
                    .append ( ccy ).append ( ",amount=" ).append ( amount ).append( ",transactionId=" )
                    .append ( transactionId  ).append ( ",extTxId=" ).append ( extTxId ).toString ();
            if ( extTxIdConstraint )
            {
                log.info ( errorLogStr + ",reason=" + AccountManagementConstants.DUPLICATE_EXTERNAL_TRANSACTIONID_ERROR );
            }
            else
            {
                log.error ( errorLogStr, e );
            }

            //check whether aggCue is not persisted, but currency position got updated.
            CreditUtilizationEvent refreshedCue = ( CreditUtilizationEvent ) IdcUtilC.refreshObject ( aggCue );
            if ( refreshedCue == null && ccyPositionUpdated )
            {
                log.warn ( "CLAS.deposit : credit utilization event not persisted and currency position got updated. rebuild currency positions for cu=" + cu + ",aggCue=" + aggCue );
                cu.rebuildCurrencyPositions ( CreditLimitConstants.RESET_CACHE_UPDATE_STALE, true );
            }

            cwm = CreditUtilC.getCreditWorkflowMessageWithError ( cwm, extTxIdConstraint ? AccountManagementConstants.DUPLICATE_EXTERNAL_TRANSACTIONID_ERROR : AccountManagementConstants.RUNTIME_EXCEPTION );
        }
        return cwm;
    }

    public CreditWorkflowMessage withdraw ( Organization cpo, TradingParty cc, Currency ccy, double amt, String transactionId, String notes, String ticket, String extTxId )
    {
        double amount = Math.abs ( amt );
        CreditWorkflowMessage cwm = CreditUtilC.initialiseCreditWorkflowMessage ( cc, cc.getLegalEntityOrganization (), cpo.getDefaultDealingEntity () );
        cwm.setEvent ( CreditMessageEvent.WITHDRAW );
        Organization cco = null;
        CreditUtilization cu = null;
        boolean ccyPositionUpdated = false;
        CreditUtilizationEvent aggCue = null;
        try
        {
            if ( !validateCreditCounterparty ( cpo, cc, "withdraw" ) )
            {
                log.warn ( "CLAS.withdraw : Invalid credit relationship. cpo=" + cpo.getShortName () + ",cc=" + cc  + ",transactionId=" + transactionId );
                cwm = CreditUtilC.getCreditWorkflowMessageWithError ( cwm, AccountManagementConstants.NOT_ACCOUNT_ENABLED_ERROR );
                return cwm;
            }

            if ( StringUtilC.isNullOrEmpty ( transactionId ) )
            {
                log.warn ( "CLAS.withdraw : Invalid transaction Id. cpo=" + cpo.getShortName () + ",cc=" + cc  + ",transactionId=" + transactionId );
                cwm = CreditUtilC.getCreditWorkflowMessageWithError ( cwm, AccountManagementConstants.INVALID_DEPOSIT_WITHDRAW_TRANSACTION_ID_ERROR );
                return cwm;
            }

            if ( StringUtilC.isNullOrEmpty ( extTxId ) )
            {
                log.warn ( "CLAS.withdraw : Invalid external transaction Id. cpo=" + cpo.getShortName () + ",cc=" + cc  + ",extTxId=" + extTxId );
                cwm = CreditUtilC.getCreditWorkflowMessageWithError ( cwm, AccountManagementConstants.INVALID_EXTERNAL_TRANSACTIONID_ERROR );
                return cwm;
            }

            TradingParty creditTp = CounterpartyUtilC.getTradingParty ( cc, cpo );
            cco = creditTp.getLegalEntityOrganization ();
            CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( cpo, creditTp );
            if ( cclr == null )
            {
                log.warn ( "CLAS.withdraw : No counterparty rule found for org=" + cpo.getShortName () + ",creditCpty=" + creditTp + ",cco=" + cco );
                cwm = CreditUtilC.getCreditWorkflowMessageWithError ( cwm, AccountManagementConstants.NOT_ACCOUNT_ENABLED_ERROR );
                return cwm;
            }
            SingleCreditLimitRule clr = ( SingleCreditLimitRule ) cclr.getChildRule ( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
            if ( clr == null || ! CreditLimitConstants.AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR.isSameAs ( clr.getCreditUtilizationCalculator () ) )
            {
                log.warn ( new StringBuilder ( 200 ).append ( "CLAS.withdraw : No credit limit rule or not cash settlement methodology for cpo=" )
                        .append ( cpo ).append ( ",creditCpty=" ).append ( cc ).append ( ",cclr=" )
                        .append ( cclr ).append ( ",cco=" ).append ( cco ).toString () );
                cwm = CreditUtilC.getCreditWorkflowMessageWithError ( cwm, AccountManagementConstants.NOT_ACCOUNT_ENABLED_ERROR );
                return cwm;
            }

            cu = clr.getCreditUtilization ();
            CurrencyPositionCollection cpc = cu.getCurrencyPositions ( true );
            double roundedAmount = ccy.round ( amount );

            log.info ( new StringBuilder ( 200 ).append ( "CLAS.withdraw : withdraw initiated for cpo=" )
                    .append ( cpo ).append ( ",creditCpty=" ).append ( cc ).append ( ",cco=" ).append ( cco )
                    .append ( ",ccy=" ).append ( ccy ).append ( ",amount=" ).append ( amount )
                    .append ( ",roundedAmount=" ).append ( roundedAmount ).append( ",transactionId=" )
                    .append ( transactionId  ).append ( ",extTxId=" ).append ( extTxId ).toString () );

            try
            {
                cclr.getLock().lock();
                IdcTransaction tx = startTransaction ( readOnlyClasses );
                addReadOnlyClass ( tx, CreditLimitOrgFunctionC.class );
                addReadOnlyClass ( tx, CounterpartyCreditLimitRuleC.class );
                addReadOnlyClass ( tx, CreditLimitRuleSetC.class );
                addReadOnlyClass ( tx, CreditUtilizationCalculatorC.class );
                addReadOnlyClass ( tx, UserC.class );
                CreditLimitRule registeredClr = ( CreditLimitRule ) clr.getRegisteredObject ();
                registeredClr.update ();

                CreditUtilization registeredCu = ( CreditUtilization ) cu.getRegisteredObject ();

                aggCue = CreditLimitFactory.newCreditUtilizationEvent ();
                aggCue = ( CreditUtilizationEvent ) aggCue.getRegisteredObject ();
                aggCue.setNamespace ( cpo.getNamespace () );
                aggCue.setCreditUtilization ( registeredCu );
                aggCue.setTradingParty ( creditTp );
                aggCue.setTradingPartyOrganization ( creditTp.getLegalEntityOrganization () );
                aggCue.setInstrument ( clr.getCurrency () );

                aggCue.setPrincipalCurrency ( ccy );
                aggCue.setPrincipal ( roundedAmount );
                aggCue.setTradePrincipalAmount ( roundedAmount );

                aggCue.setPriceCurrency ( ccy );
                aggCue.setPrice ( 0.0 );
                aggCue.setTradePriceAmount ( 0.0 );
                aggCue.setLastAction ( CreditLimit.ACTION_APPLY );
                aggCue.setTenorCoefficient ( 1.0 );

                aggCue.setBuySell ( CreditLimit.BUY );
                aggCue.setSettlementDate ( CreditLimitConstants.MAX_VALUE_DATE );
                aggCue.setTradeDate ( EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate () );
                aggCue.setCounterpartyCreditLimitRule ( cclr );
                aggCue.setTransactionId ( transactionId );

                registeredCu.getCurrencyPositions ( true ).addCreditUtilizationEvent ( aggCue, false, false );// do not update last updated time
                ccyPositionUpdated = true;
                registeredCu.setPositions ( registeredCu.getCurrencyPositions ().getPositions () );
                registeredCu.setUsedAmount ( registeredClr.getCreditUtilizationCalculator ().getRealtimeUtilizationAmount ( registeredCu ) );
                CurrencyPosition prinCcyPos = registeredCu.getCurrencyPositions ().getCurrencyPosition ( ccy );
                aggCue.setPrincipalBalance ( prinCcyPos != null ? prinCcyPos.getNetAmountBigDecimal () : BigDecimal.ZERO );
                aggCue.setEventType ( AccountEnums.AccountEventType.WITHDRAW );
                aggCue.setUser ( IdcUtilC.getSessionContextUser () );
                aggCue.setNotes ( notes );
                aggCue.setTicket ( ticket );
                aggCue.setExternalTransactionId ( extTxId );

                // add a commit handler for recalculation of credit utilization.
                addCreditUtilizationRecalculationCommitHandler ( new CreditUtilizationRecalculationHandlerC ( clr ) );

                // add notification functors for adding the withdrawals.
                HashMap<String, String> propertiesMap = new HashMap<String, String> ( 3 );
                propertiesMap.put ( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID () );
                propertiesMap.put ( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cclr.getTradingPartyOrganization ().getGUID () );
                if ( cclr.getTradingParty() != null )
                {
                    propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cclr.getTradingParty().getGUID () );
                }
                String eventName = cwm.getEventName ();
                propertiesMap.put ( CreditLimitConstants.EVENT_PROPERTY, eventName );
                addDepositWithdrawRemoteFunctor( propertiesMap );

                // Adds the remote notification functor registered for the event.
                Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance ().getCreditRemoteTransactionFunctor ( eventName );
                if ( remoteFunctor != null )
                {
                    getTransaction ().addRemoteFunctor ( remoteFunctor.getName (), propertiesMap );
                }
                else
                {
                    log.info ( "CLAS.withdraw : No remote notification functor registered. event=" + eventName );
                }

                endTransaction ( tx, true, false );

                boolean isCptyOrgLevel = cclr.getTradingParty() == null;
                CreditLimitAdminAuditManagerC.getInstance().auditWithdraw( cpo, isCptyOrgLevel, cc, cc.getLegalEntityOrganization(), ccy, amount );

                //send email notification
                cwm.setTransactionId ( transactionId );
                BigDecimal newBalance = cu.getCurrencyPositions ().getCurrencyPosition ( ccy ).getNetAccountAmountBigDecimal ();
                cwm.setParameterValue ( CreditLimit.DEPOSIT_WITHDRAW_AMOUNT_PARAM, amount );
                cwm.setParameterValue ( CreditLimit.DEPOSIT_WITHDRAW_CURRENCY_PARAM, ccy.getShortName () );
                cwm.setParameterValue ( CreditLimit.DEPOSIT_WITHDRAW_BALANCE_AMOUNT_PARAM, newBalance );
                cwm.setParameterValue ( CreditLimit.DEPOSIT_WITHDRAW_AVAILABLE_AMOUNT_PARAM, newBalance );
                cwm.setParameterValue ( CreditLimit.DEPOSIT_WITHDRAW_USER_PARAM, IdcUtilC.getSessionContextUser ().getFullyQualifiedName () );
                CreditNotificationManagerC.getInstance ().sendCreditDepositWithdrawEmail ( cwm );

                log.info ( new StringBuilder ( 200 ).append ( "CLAS.withdraw : withdraw successful for cpo=" )
                        .append ( cpo ).append ( ",creditCpty=" ).append ( cc ).append ( ",cco=" ).append ( cco )
                        .append ( ",ccy=" ).append ( ccy ).append ( ",amount=" ).append ( amount )
                        .append ( ",roundedAmount=" ).append ( roundedAmount ).append( ",transactionId=" )
                        .append ( transactionId  ).append ( ",extTxId=" ).append ( extTxId ).toString () );
            }
            finally
            {
                cclr.getLock ().unlock ();
            }
        }
        catch ( Exception e )
        {
            boolean dbException = e instanceof IdcDatabaseException;
            boolean extTxIdConstraint = false;
            if ( dbException )
            {
                IdcDatabaseException dbe = ( IdcDatabaseException ) e;
                if ( dbe.getNestedException () != null && dbe.getNestedException ().getMessage () != null
                        && dbe.getNestedException ().getMessage ().contains ( AccountManagementConstants.DEPOSIT_WITHDRAW_EXTERNAL_TXID_UNIQUE_CONSTRAINT_NAME ) )
                {
                    extTxIdConstraint = true;
                }
            }
            String errorLogStr = new StringBuilder ( 200 ).append ( "CLAS.withdraw : withdraw failed for cpo=" )
                    .append ( cpo ).append ( ",creditCpty=" ).append ( cc ).append ( ",cco=" ).append ( cco ).append ( ",ccy=" )
                    .append ( ccy ).append ( ",amount=" ).append ( amount ).append( ",transactionId=" )
                    .append ( transactionId  ).append ( ",extTxId=" ).append ( extTxId ).toString ();
            if ( extTxIdConstraint )
            {
                log.info ( errorLogStr + ",reason=" + AccountManagementConstants.DUPLICATE_EXTERNAL_TRANSACTIONID_ERROR );
            }
            else
            {
                log.error ( errorLogStr, e );
            }

            //check whether aggCue is not persisted, but currency position got updated.
            CreditUtilizationEvent refreshedCue = ( CreditUtilizationEvent ) IdcUtilC.refreshObject ( aggCue );
            if ( refreshedCue == null && ccyPositionUpdated )
            {
                log.warn ( "CLAS.withdraw : credit utilization event not persisted and currency position got updated. rebuild currency positions for cu=" + cu + ",aggCue=" + aggCue );
                cu.rebuildCurrencyPositions ( CreditLimitConstants.RESET_CACHE_UPDATE_STALE, true );
            }

            cwm = CreditUtilC.getCreditWorkflowMessageWithError ( cwm, extTxIdConstraint ? AccountManagementConstants.DUPLICATE_EXTERNAL_TRANSACTIONID_ERROR : AccountManagementConstants.RUNTIME_EXCEPTION );
        }
        return cwm;
    }

    private boolean isTenorInBusinessDays( Organization cpo, TradingParty cc, boolean cptyLELevel  )
    {
        try
        {
            if ( validateCreditCounterparty( cpo, cc, "isTenorInBusinessDays" ) )
            {
                CounterpartyCreditLimitRule cptyRule = cptyLELevel ? CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule( cpo, cc )
                        : CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, cc );
                if ( cptyRule != null )
                {
                    return cptyRule.isTenorRestrictionInBusinessDays();
                }
                else
                {
                    log.info( "CLAS.isTenorInBusinessDays : No cpty rule for org=" + cpo + ",cc=" + cc + ",cptyLELevel=" + cptyLELevel );
                }
            }
        }
        catch ( Exception e )
        {
            log.warn( new StringBuilder( 200 ).append( "CLAS.isTenorInBusinessDays : Error getting tenor in BusinessDays for cpo=" )
                    .append( cpo ).append( ",cc=" ).append( cc ).toString(), e );
        }
        return false;
    }

    private void updateTenorRestrictionsInBusinessDays ( final Organization cpo, final TradingParty cc, boolean updateCptyLE, boolean businessDays )
    {
        long t0 = System.currentTimeMillis();
        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "CLAS.updateTenorRestrictionsInBusinessDays : Begin. cpo=" )
                    .append( cpo ).append( ",cc=" ).append( cc ).append( ",businessDays=" ).append( businessDays ).toString() );
        }

        if ( !validateCreditCounterparty( cpo, cc, "updateTenorRestrictionsInBusinessDays" ) )
        {
            return;
        }

        TradingParty creditTp = CounterpartyUtilC.getTradingParty( cc, cpo );
        IdcTransaction tx = null;
        try
        {
            CounterpartyCreditLimitRule cptyRule;
            if( updateCptyLE )
            {
                cptyRule =  CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule( cpo, creditTp );
            }
            else
            {
                cptyRule = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, creditTp );
            }

            if ( cptyRule == null )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.updateTenorRestrictionsInBusinessDays : No cpty rule. cpo=" )
                        .append( cpo ).append( ",cc=" ).append( creditTp ).toString() );
                return;
            }
            boolean existingFlag = cptyRule.isTenorRestrictionInBusinessDays();

            if ( existingFlag == businessDays )
            {
                log.info( new StringBuilder( 200 ).append( "CLAS.updateTenorRestrictionsInBusinessDays : Same as existing business days flag=" )
                        .append( businessDays ).append(",existing=").append( existingFlag ).append( ",cpo=" ).append( cpo )
                        .append( ",cc=" ).append( creditTp ).toString() );
                return;
            }

            tx = startTransaction( cptyRuleTransactionReadOnlyClasses );
            CounterpartyCreditLimitRule registeredCclr = ( CounterpartyCreditLimitRule ) cptyRule.getRegisteredObject();

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.updateTenorRestrictionsInBusinessDays : cptyRule=" )
                        .append( cptyRule ).append( ",businessDays=" ).append( businessDays ).append( ",existing=" )
                        .append( existingFlag ).append( ",new tx=" ).append( tx != null ).toString() );
            }

            registeredCclr.setTenorRestrictionInBusinessDays( businessDays );

            CreditLimitAdminAuditManagerC.getInstance().auditTenorRestrictionsInBusinessDays( cpo, cptyRule.getTradingParty() == null, cptyRule.getTradingPartyOrganization(), creditTp, existingFlag, businessDays );

            // add notification functors for updating credit limits.
            HashMap<String, String> propertiesMap = new HashMap<String, String>( 3 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, cpo.getGUID() );
            if ( cptyRule.getTradingParty () != null )
            {
                propertiesMap.put ( CreditLimit.CREDIT_COUNTERPARTY, creditTp.getGUID () );
            }
            propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, cptyRule.getTradingPartyOrganization().getGUID() );
            propertiesMap.put(CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETTENORBUSINESSDAYS.getName());
            // Adds the remote notification functor registered for the event.
            Class remoteFunctor = CreditRemoteNotificationFunctorServerC.getInstance().getCreditRemoteTransactionFunctor( CreditMessageEvent.SETTENORBUSINESSDAYS.getName() );
            if ( remoteFunctor != null )
            {
                getTransaction().addRemoteFunctor( remoteFunctor.getName(), propertiesMap );
            }
            else
            {
                log.info( "CLAS.updateTenorRestrictionsInBusinessDays : No remote notification functor registered. event=" + CreditMessageEvent.SETTENORBUSINESSDAYS.getName() );
            }
            getTransaction().addRemoteFunctor( CreditTenorConfigChanges.class.getName(), propertiesMap );
            // commits transaction if created by the service.
            CreditUtilC.endTransaction( tx );

            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 200 ).append( "CLAS.updateTenorRestrictionsInBusinessDays : End. cpo=" )
                        .append( cpo.getShortName() ).append( ",cc=" ).append( creditTp )
                        .append( ",businessDays=" ).append( businessDays ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.updateTenorRestrictionsInBusinessDays : cpo=" + cpo.getShortName(), e );
            CreditUtilC.releaseTransaction(tx);
        }
    }

    public void setUseCreditLimitRuleLevelTenorCoefficients( Organization cpo, TradingParty cc, boolean flag )
    {
        getCreditPFEAdminService().setUseCreditLimitRuleLevelTenorCoefficients( cpo, cc, flag );
    }

    public void setGrossPositionSpreadMargin( Organization cpo, CreditTenorProfile ctp, double margin )
    {
        getCreditPFEAdminService().setGrossPositionSpreadMargin( cpo, ctp, margin );
    }

    /**
     * Sets the read only classes which are basic for credit admin workflow.
     */
    private static void setReadOnlyClasses()
    {
        readOnlyClasses.add( CreditLimitClassificationC.class );
        readOnlyClasses.add( UserC.class );
        readOnlyClasses.add( OrganizationC.class );
        readOnlyClasses.add( NamespaceC.class );
        readOnlyClasses.add( StateC.class );
        readOnlyClasses.add( TradingPartyC.class );
        readOnlyClasses.add( LegalEntityC.class );
        readOnlyClasses.add( CreditUtilizationCalculatorC.class );
        readOnlyClasses.add( CurrencyC.class );
    }

    /**
     * Sets read only classes for a transaction where only credit limit org function is modified.
     */
    private static void setOrgFunctionTransactionReadOnlyClasses()
    {
        orgFunctionTransactionReadOnlyClasses.addAll( readOnlyClasses );
        orgFunctionTransactionReadOnlyClasses.add( CounterpartyCreditLimitRuleC.class );
        orgFunctionTransactionReadOnlyClasses.add( CreditLimitRuleC.class );
        orgFunctionTransactionReadOnlyClasses.add( DailyCreditLimitRuleC.class );
        orgFunctionTransactionReadOnlyClasses.add( CreditUtilizationC.class );
        orgFunctionTransactionReadOnlyClasses.add( DailyCreditUtilizationC.class );
        orgFunctionTransactionReadOnlyClasses.add( CreditUtilizationEventC.class );
    }

    /**
     * Sets read only classes for a transaction where only credit limit ruleset  is modified.
     */
    private static void setRuleSetTransactionReadOnlyClasses()
    {
        ruleSetTransactionReadOnlyClasses.addAll( readOnlyClasses );
        ruleSetTransactionReadOnlyClasses.add( CreditLimitOrgFunctionC.class );
        ruleSetTransactionReadOnlyClasses.add( CounterpartyCreditLimitRuleC.class );
        ruleSetTransactionReadOnlyClasses.add( CreditLimitRuleC.class );
        ruleSetTransactionReadOnlyClasses.add( DailyCreditLimitRuleC.class );
        ruleSetTransactionReadOnlyClasses.add( CreditUtilizationC.class );
        ruleSetTransactionReadOnlyClasses.add( DailyCreditUtilizationC.class );
        ruleSetTransactionReadOnlyClasses.add( CreditUtilizationEventC.class );
        ruleSetTransactionReadOnlyClasses.add( CurrencyPairGroupC.class );
    }

    /**
     * Sets read only classes for a transaction where only counterparty credit limit rule is modified.
     */
    private static void setCptyRuleTransactionReadOnlyClasses()
    {
        cptyRuleTransactionReadOnlyClasses.addAll( readOnlyClasses );
        cptyRuleTransactionReadOnlyClasses.add( CreditLimitOrgFunctionC.class );
        cptyRuleTransactionReadOnlyClasses.add( CreditLimitRuleSetC.class );
        cptyRuleTransactionReadOnlyClasses.add( CreditLimitRuleC.class );
        cptyRuleTransactionReadOnlyClasses.add( DailyCreditLimitRuleC.class );
        cptyRuleTransactionReadOnlyClasses.add( CreditUtilizationC.class );
        cptyRuleTransactionReadOnlyClasses.add( DailyCreditUtilizationC.class );
        cptyRuleTransactionReadOnlyClasses.add( CreditUtilizationEventC.class );
        cptyRuleTransactionReadOnlyClasses.add( CurrencyPairGroup.class );
    }

    /**
     * Adds read only classes to the transaction if transaction is not null.
     *
     * @param tx      transaction
     * @param roClass readonly class
     */
    protected void addReadOnlyClass( IdcTransaction tx, Class roClass )
    {
        try
        {
            if ( tx != null )
            {
                tx.getUOW().addReadOnlyClass( roClass );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.addReadOnlyClass : read only class=" + roClass, e );
        }
    }

    /**
     * Removes read only classes to the transaction if transaction is not null.
     *
     * @param tx             transaction
     * @param writeableClass writeable class
     */
    protected void removeReadOnlyClass( IdcTransaction tx, Class writeableClass )
    {
        try
        {
            if ( tx != null )
            {
                tx.getUOW().removeReadOnlyClass( writeableClass );
            }
        }
        catch ( Exception e )
        {
            log.warn( "CLAS.removeReadOnlyClass : class=" + writeableClass, e );
        }
    }

    /**
     * Validates the credit provider organization by checking the availability of credit limit org function and
     * credit limit rule set.
     *
     * @param creditProviderOrg credit provider org
     * @param contextInfo       context information
     * @return validation result
     */
    protected boolean validateCreditProvider( Organization creditProviderOrg, String contextInfo )
    {
        if ( creditProviderOrg == null )
        {
            log.warn( new StringBuilder( 100 ).append( "CLAS." ).append( contextInfo ).append( " : Parameters not specified." ).toString() );
            throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
        }
        CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( creditProviderOrg );
        if ( clrs == null || CreditUtilC.getCreditLimitOrgFunction( creditProviderOrg ) == null )
        {
            log.info( new StringBuilder( 200 ).append( "CreditLimitAdminServiceC" ).append( contextInfo ).append( " : No credit limit ruleset or org function found for org=" )
                    .append( creditProviderOrg.getShortName() ).toString() );
            return false;
        }
        return true;
    }

    /**
     * Validates the credit provider and counterparty by checking the availability of trading party.
     *
     * @param creditProviderOrg credit provider org
     * @param creditCpty        credit counterparty
     * @param contextInfo       context information
     * @return validation result
     */
    protected boolean validateCreditCounterparty( Organization creditProviderOrg, TradingParty creditCpty, String contextInfo )
    {
        boolean valid = validateCreditProvider( creditProviderOrg, contextInfo );
        if ( valid )
        {
            if ( creditCpty == null || ( CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg ) ) == null || !CreditUtilC.validateCreditCounterparty( creditProviderOrg, creditCpty ) )
            {
                log.warn( new StringBuilder( 200 ).append( "CLAS.validateCreditCounterparty - " ).append( contextInfo )
                        .append( " : Parameters not specified. cpo=" ).append ( creditProviderOrg ).append ( ",creditCpty=" )
                        .append ( creditCpty ).toString() );
                throw new IdcRuntimeException( new IllegalArgumentException( INSUFFICIENT_PARAMETERS_ERROR ) );
            }
        }
        return valid;
    }

    protected IdcTransaction getTransaction()
    {
        return CreditUtilC.getTransaction();
    }

    protected IdcTransaction startTransaction( Vector readOnlyClasses )
    {
        return CreditUtilC.startTransaction( readOnlyClasses );
    }

    protected void endTransaction( IdcTransaction tx, boolean throwException )
    {
        CreditUtilC.endTransaction( tx, throwException );
    }

    protected void endTransaction( IdcTransaction tx, boolean throwException, boolean logDBException )
    {
        CreditUtilC.endTransaction( tx, throwException, logDBException );
    }

    protected void releaseTransaction( IdcTransaction tx )
    {
        CreditUtilC.releaseTransaction( tx );
    }

    protected CounterpartyCreditLimitRule getActiveCounterpartyCreditLimitRuleForTradingParty( final Organization cpo, final TradingParty cc )
    {
        return CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, cc );
    }

    protected void addCreditUtilizationCreationCommitHandler( final Organization cpo, final Organization cco, final TradingParty cc )
    {
        // create credit utilizations after the transaction is successful.
        getTransaction().addCommitHandler( new CreateCreditUtilizationCommitHandlerC( cpo, cco, cc ) );
    }

    protected void addCreditUtilizationRecalculationCommitHandler( MessageHandler handler )
    {
        // add a commit handler for recalculation of credit utilization.
        getTransaction().addCommitHandler( handler );
    }

    protected Currency getReportingCurrency( final Organization cpo )
    {
        return CreditUtilC.getReportingCurrency( cpo );
    }

    protected CreditLimitPFEAdminServiceC getCreditPFEAdminService()
    {
        return pfeService;
    }

    protected CreditRelationshipAdminServiceC getCreditRelationshipService()
    {
        return relationshipService;
    }

    protected void addDepositWithdrawRemoteFunctor( HashMap<String, String> propertiesMap )
    {
        getTransaction ().addRemoteFunctor ( CreditUtilizationCacheResetNotificationFunctorC.class.getName (), propertiesMap );
    }
}
