package com.integral.finance.creditLimit.admin;

// Copyright (c) 2022 Integral Development Corporation.  All Rights Reserved.

import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationFactory;
import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationMBean;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.fx.FXRateBasisC;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.marketData.fx.FXMarketDataSetC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.NamespaceC;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.time.DatePeriod;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.OrganizationC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.CursoredStream;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.ArrayList;
import java.util.Collection;

public class RemoveOldFixedPeriodMarketDataSetsFunctor extends CreditFunctorC
{
    protected static Log log = LogFactory.getLog ( RemoveOldFixedPeriodMarketDataSetsFunctor.class );
    private static final DealingDataPurgeConfigurationMBean _purgeConfigMBean = DealingDataPurgeConfigurationFactory.getDealingDataPurgeConfigurationMBean ();
    private static final int COMMIT_BATCH_SIZE = 50;

    RemoveOldFixedPeriodMarketDataSetsFunctor ( String name )
    {
        setName ( name );
    }

    private IdcDate getDatePeriodDate ( DatePeriod dp )
    {
        IdcDate currentDate = DateTimeFactory.newDate ();
        return dp.subtract ( currentDate );
    }

    @Override
    public void execute ( )
    {
        try
        {
            // delete fixed period market data sets
            DatePeriod dp = _purgeConfigMBean.getFixedPeriodMarketDataSetPurgeKeepPeriod ();
            if ( dp != null )
            {
                IdcDate date = getDatePeriodDate ( dp );
                deleteFixedPeriodMarketDataSets ( date );
            }
            else
            {
                log.info ( "RFPMF.execute - no purge date period configured for fixed period market data sets." );
            }

            // delete EODMDS based on the configuration
            DatePeriod endOfDayMDSPurgePeriod = _purgeConfigMBean.getEndOfDayMarketDataSetPurgeKeepPeriod ();
            if ( endOfDayMDSPurgePeriod != null )
            {
                IdcDate date = getDatePeriodDate ( endOfDayMDSPurgePeriod );
                deleteEndOfDayMarketDataSets ( date );
            }
            else
            {
                log.info ( "RFPMF.execute - no purge date period configured for end of day market data sets." );
            }
        }
        catch ( Exception e )
        {
            log.error ( "RFPMF.execute - Exception while deleting the old fixed period market data sets.", e );
        }
    }

    public void deleteFixedPeriodMarketDataSets( IdcDate baseDate )
    {
        deleteMarketDataSets( baseDate, getFixedPeriodMarketDataSetByDateExpression ( baseDate ) );
    }

    public void deleteEndOfDayMarketDataSets( IdcDate baseDate )
    {
        deleteMarketDataSets( baseDate, getEndOfDayMDSMarketDataSetByDateExpression ( baseDate ) );
    }

    private void deleteMarketDataSets( IdcDate baseDate, Expression expression )
    {
        long t0 = System.currentTimeMillis();
        log.info( new StringBuilder( 200 ).append( "RFPMF.deleteMarketDataSets : start deleting fixed period market data sets older than date=" )
                .append( baseDate ).toString() );
        int num = 0;
        int totalRemoved = 0;
        int completedBatchCount = 0;
        CursoredStream cursor = null;

        try
        {
            Session session = PersistenceFactory.newSession();
            ReadAllQuery raq = new ReadAllQuery();
            raq.setReferenceClass( FXMarketDataSetC.class );
            raq.useCursoredStream( COMMIT_BATCH_SIZE, COMMIT_BATCH_SIZE );
            raq.setSelectionCriteria( expression );
            cursor = ( CursoredStream ) session.executeQuery( raq );

            Collection<FXMarketDataSet> marketDataSets = null;
            while ( !cursor.atEnd() )
            {
                if ( num == 0 )
                {
                    marketDataSets = new ArrayList<FXMarketDataSet> ( 10 );
                }
                marketDataSets.add( ( FXMarketDataSet ) cursor.read() );
                num++;
                totalRemoved++;
                if ( num >= COMMIT_BATCH_SIZE )
                {
                    deleteMarketDataSetCollection( marketDataSets );
                    num = 0;
                    cursor.releasePrevious();
                    ++completedBatchCount;
                    log.info( new StringBuilder( 200 ).append( "RFPMF.deleteMarketDataSets : totalRemoved=" )
                            .append( totalRemoved ).append( ",batchNum=" ).append( completedBatchCount ).toString() );
                }
            }
            if ( num > 0 )
            {
                deleteMarketDataSetCollection( marketDataSets );
            }

            log.info( new StringBuilder( 200 ).append( "RFPMF.deleteMarketDataSets : Removed count=" )
                    .append( totalRemoved ).append( ",baseDate=" ).append( baseDate ).toString() );
            cursor.close();
        }
        catch ( Exception e )
        {
            log.error( new StringBuilder( 200 ).append( "RFPMF.deleteMarketDataSets : baseDate=" )
                    .append( baseDate ).toString(), e );
        }
        finally
        {
            if ( cursor != null )
            {
                cursor.close();
            }
        }
        log.info( new StringBuilder( 200 ).append( "RFPMF.deleteMarketDataSets : finished deleting old market data sets. Date=" )
                .append( baseDate ).append( ",totalRemoved=" ).append( totalRemoved ).append( " took ms=" )
                .append( System.currentTimeMillis() - t0 ).toString() );
    }

    private Expression getFixedPeriodMarketDataSetByDateExpression ( IdcDate date )
    {
        ExpressionBuilder eb = new ExpressionBuilder ();
        Expression fixedPeriodExpr = eb.get ( "fixedPeriod" ).equal ( true );
        Expression baseDateExpr = eb.get ( "baseDate" ).lessThan ( date );
        return fixedPeriodExpr.and ( baseDateExpr );
    }

    private Expression getEndOfDayMDSMarketDataSetByDateExpression ( IdcDate date )
    {
        ExpressionBuilder eb = new ExpressionBuilder ();
        Expression eodMDSExpr = eb.get ( NamedEntity.ShortName ).equal ( ReferenceDataCacheC.END_OF_DAY_MDS );
        Expression baseDateExpr = eb.get ( "baseDate" ).lessThan ( date );
        return eodMDSExpr.and ( baseDateExpr );
    }

    private void deleteMarketDataSetCollection( Collection<FXMarketDataSet> marketDataSets )
    {
        try
        {
            UnitOfWork uow = PersistenceFactory.newSession ().acquireUnitOfWork ();
            uow.removeAllReadOnlyClasses ();
            uow.addReadOnlyClass ( OrganizationC.class );
            uow.addReadOnlyClass ( NamespaceC.class );
            uow.addReadOnlyClass ( FXRateConventionC.class );
            uow.addReadOnlyClass ( FXRateBasisC.class );
            uow.addReadOnlyClass ( CurrencyC.class );

            for ( FXMarketDataSet fxMarketDataSet: marketDataSets )
            {
                FXMarketDataSet registeredFXMds = ( FXMarketDataSet ) uow.registerObject ( fxMarketDataSet );
                uow.deleteObject ( registeredFXMds );
            }

            uow.commit ();
            log.info ( "RFPMF.deleteMarketDataSetCollection : deleting the old market data sets=" + marketDataSets );
            long sleepInterval = _purgeConfigMBean.getBatchSleepInterval();
            if ( sleepInterval > 0 )
            {
                Thread.sleep( sleepInterval );
            }
        }
        catch ( Exception e )
        {
            log.error ( "RFPMF.deleteMarketDataSetCollection : Exception while deleting the old market datasets=" + marketDataSets, e );
        }
    }
}
