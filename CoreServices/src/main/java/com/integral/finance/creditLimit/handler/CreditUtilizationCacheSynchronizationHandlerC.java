package com.integral.finance.creditLimit.handler;

// Copyright (c) 2013 Integral Development Corp.  All rights reserved.

import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationFactory;
import com.integral.finance.creditLimit.quickcheck.CreditLineCollection;
import com.integral.finance.creditLimit.quickcheck.CreditLineManagerC;
import com.integral.finance.creditLimit.quickcheck.CurrencyLevelCreditLine;
import com.integral.finance.creditLimit.quickcheck.CurrencyPairLevelCreditLineKey;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.price.fx.FXPrice;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcSimpleDateFormat;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.IdcUtilC;
import com.integral.util.StringUtilC;

import org.eclipse.persistence.sessions.Session;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map.Entry;

/**
 * This class is used to synchronize cache between multiple servers when same credit lines are used in two or more servers.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditUtilizationCacheSynchronizationHandlerC
{
    private static final Log log = LogFactory.getLog( CreditUtilizationCacheSynchronizationHandlerC.class );

    public static final String UTIL_EVENT_DELIMITER = "#";
    public static final String UTIL_EVENT_FIELD_DELIMITER = "~";
    public static final int CREDIT_UTIL_OBJECTID_POS = 0;
    public static final int PRINCIPAL_CCY_POS = 1;
    public static final int PRICE_CCY_POS = 2;
    public static final int PRINCIPAL_CCYAMT_POS = 3;
    public static final int PRICE_CCYAMT_POS = 4;
    public static final int BUYSELL_POS = 5;
    public static final int ORIGINAL_PRINCIPAL_CCYAMT_POS = 6;
    public static final int ORIGINAL_PRICE_CCYAMT_POS = 7;
    public static final int LAST_ACTION_POS = 8;
    public static final int POSITION_DATE_POS = 9;
    public static final int LIMIT_CCY_AMOUNT_POS = 10;
    public static final int ORIGINAL_LIMIT_CCY_AMOUNT_POS = 11;
    public static final int TRADE_PRINCIPAL_CCYAMT_POS = 12;
    public static final int TRADE_PRICE_CCYAMT_POS = 13;
    public static final int ORIGINAL_TRADE_PRINCIPAL_CCYAMT_POS = 14;
    public static final int ORIGINAL_TRADE_PRICE_CCYAMT_POS = 15;
    public static final int CPO_CC_CCYPAIR_KEY_POS = 16;
    public static final int CPO_CC_DATE_KEY_POS = 17;
    public static final int CPO_POS = 18;
    public static final int IS_DAILY_CU = 19;
    public static final int TP_OBJ_ID_POS = 20;
    public static final int CCPAIR_INDX_POS = 21;
    public static final int BASE_CCY_POS = 22;
    
    public static final String ACTION_APPLY = CreditLimit.ACTION_APPLY + "";
    public static final String ACTION_REMOVE = CreditLimit.ACTION_REMOVE + "";
    public static final String BUY = CreditLimit.BUY + "";

    public static Collection<CounterpartyCreditLimitRule> applyUtilizationEvents( String event, String eventSnapshot, String tid, boolean updateCcyPositions, IdcDate tradeDate )
    {
        return applyUtilizationEvents( event, eventSnapshot, tid, updateCcyPositions, tradeDate, false );
    }

    public static void applyUtilizationEvents( String event, String eventSnapshot, String tid, IdcDate tradeDate )
    {
        applyUtilizationEvents( event, eventSnapshot, tid, true, tradeDate, false );
    }

    public static void applyUtilizationEvents( String event, String eventSnapshot, String tid, IdcDate tradeDate, boolean readCUFromCache )
    {
        applyUtilizationEvents( event, eventSnapshot, tid, true, tradeDate, readCUFromCache );
    }

    public static Collection<CounterpartyCreditLimitRule> applyUtilizationEvents( String event, String eventSnapshot, String tid, boolean updateCcyPositions, IdcDate tradeDate, boolean readCUFromCache )
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( "CUSH.applyUtilizationEvents : updating credit currency positions cache. eventSnapshot=" + eventSnapshot + ",tid=" + tid );
        }

        try
        {
        	boolean persistenceEnabled = false;
        	if ( ConfigurationFactory.getServerMBean().shouldPersistCreditNotification() ) {
        		log.info("persistCreditUtilizations : Credit utilization persistence is enabled on this Notification server");
        		persistenceEnabled = true;
        	}
    		
            Session session = null;
            //If we are not going to updateCcyPositions, why take the pain of reading from db. Use the one in cache.
            readCUFromCache = !updateCcyPositions?true:readCUFromCache;
            if ( !readCUFromCache )
            {
                session = getPersistenceSession();
            }
            String[] events = eventSnapshot.split( UTIL_EVENT_DELIMITER );

            short logMsg = 0;
            Collection<CurrencyPairLevelCreditLineKey> creditLineKeys = null;
            Collection<CounterpartyCreditLimitRule> cclrs = new HashSet<CounterpartyCreditLimitRule>();
            Collection<CreditLineCollection> affectedSubscriptions = new HashSet<CreditLineCollection>();
            Collection<CurrencyLevelCreditLine> affectedOrphanCreditLines = new HashSet<CurrencyLevelCreditLine>( );
            HashMap<CounterpartyCreditLimitRule,HashSet<CreditUtilization>> cclrToCreditUtilsMap = new HashMap<CounterpartyCreditLimitRule, HashSet<CreditUtilization>>();
            StringBuilder sb = new StringBuilder ( 400 );
            for ( String utilEvent : events )
            {
                if ( StringUtilC.isNullOrEmpty( utilEvent ) )
                {
                    continue;
                }
                String[] utilEventFields = utilEvent.split( UTIL_EVENT_FIELD_DELIMITER );
                String cuId = utilEventFields[CREDIT_UTIL_OBJECTID_POS];
                String cpoStr = utilEventFields[CPO_POS];
                String cpoCcDate = utilEventFields[CPO_CC_DATE_KEY_POS];
                IdcDate valueDate = null;
                String posDateStr = utilEventFields[POSITION_DATE_POS];
                boolean isDailyCU = Boolean.parseBoolean(utilEventFields[IS_DAILY_CU]);
                Organization cpo = ReferenceDataCacheC.getInstance().getOrganization( cpoStr);
                long tpObjId = Long.parseLong(utilEventFields[TP_OBJ_ID_POS]);
                int ccyPairIndx = Integer.parseInt(utilEventFields[CCPAIR_INDX_POS]);
                if (!StringUtilC.isNullOrEmpty(posDateStr))
                {
                    valueDate = DateTimeFactory.newDate( posDateStr, IdcSimpleDateFormat.FORMAT_DATE_ISO );
                }

                CurrencyPairLevelCreditLineKey creditLineKey = new CurrencyPairLevelCreditLineKey(cpo.getIndex(), tpObjId, ccyPairIndx, valueDate.getDays(), true);
                if ( creditLineKeys == null ){
                    creditLineKeys = new HashSet<CurrencyPairLevelCreditLineKey>();
                }
                creditLineKeys.add( creditLineKey );

                collectAffectedOrphanCreditLines( affectedOrphanCreditLines, creditLineKey );

                if ( log.isDebugEnabled() )
                {
                    log.debug( "CUSH.applyUtilizationEvents : processing cu object id=" + cuId );
                }
                CreditUtilization cu = getCreditUtilizationByObjectId( Long.valueOf( cuId ), cpoCcDate, cpoStr, !isDailyCU, session, readCUFromCache );
                if ( log.isDebugEnabled() )
                {
                    log.debug( new StringBuilder( 200 ).append( "CUSH.handle : cu from session cache=" )
                            .append( cu ).append( ",utilEvent=" ).append( utilEvent ).toString() );
                }
				if ( cu == null ) {
					if ( persistenceEnabled ) {
						if ( log.isDebugEnabled() ) {
							log.debug("CUSH.applyUtilizationEvents : Persistence enabled. Quering Credit Utilization from D ");
						}
						cu = getCreditUtilizationFromDB(cuId);

					}
					if ( cu == null ) {
						continue;
					}
				}

                long dt = 0;
                CurrencyPositionCollection ccyPos = cu.getCurrencyPositions( persistenceEnabled );
                HashSet<CreditUtilization> creditUtilsToPersist = null;
                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule().getParentRule();
                creditUtilsToPersist = cclrToCreditUtilsMap.get(cclr);
                if(creditUtilsToPersist == null) {
                	creditUtilsToPersist = new HashSet<CreditUtilization>();
                	cclrToCreditUtilsMap.put(cclr, creditUtilsToPersist);
                }
                if ( ccyPos != null )
                {
                    cclrs.add(cclr);
                    creditUtilsToPersist.add( cu );
                	Collection<CreditLineCollection> creditLineCollections = CreditLineManagerC.getInstance().getFILPSubscriptionsForCreditLine(cpo, cclr.getTradingParty(), cclr.getTradingPartyOrganization());
                	if(creditLineCollections!=null)
                	{
                		affectedSubscriptions.addAll(creditLineCollections);
                	}
        			
                    if ( !updateCcyPositions )
                    {
            			if ( log.isDebugEnabled() ) {
							log.debug("CUSH.applyUtilizationEvents : Skipping Credit Utilization ccy positions update");
						}
                        continue;
                    }
                    try
                    {
                        Currency principalCcy = CurrencyFactory.getCachedCurrency( utilEventFields[PRINCIPAL_CCY_POS] );
                        Currency priceCcy = CurrencyFactory.getCachedCurrency( utilEventFields[PRICE_CCY_POS] );
                        double principalCcyAmt = Double.parseDouble( utilEventFields[PRINCIPAL_CCYAMT_POS] );
                        double priceCcyAmt = Double.parseDouble( utilEventFields[PRICE_CCYAMT_POS] );                        
                        double tradePrincipalCcyAmt = Double.parseDouble( utilEventFields[TRADE_PRINCIPAL_CCYAMT_POS] );
                        double tradePriceCcyAmt = Double.parseDouble( utilEventFields[TRADE_PRICE_CCYAMT_POS] );                        
                        double origPrincipalCcyAmt = StringUtilC.isNullOrEmpty( utilEventFields[ORIGINAL_PRINCIPAL_CCYAMT_POS] ) ? 0.0 : Double.parseDouble( utilEventFields[ORIGINAL_PRINCIPAL_CCYAMT_POS] );
                        double origPriceCcyAmt = StringUtilC.isNullOrEmpty( utilEventFields[ORIGINAL_PRICE_CCYAMT_POS] ) ? 0.0 : Double.parseDouble( utilEventFields[ORIGINAL_PRICE_CCYAMT_POS] );                        
                        double origTradePrincipalCcyAmt = StringUtilC.isNullOrEmpty( utilEventFields[ORIGINAL_TRADE_PRINCIPAL_CCYAMT_POS] ) ? 0.0 : Double.parseDouble( utilEventFields[ORIGINAL_TRADE_PRINCIPAL_CCYAMT_POS] );
                        double origTradePriceCcyAmt = StringUtilC.isNullOrEmpty( utilEventFields[ORIGINAL_TRADE_PRICE_CCYAMT_POS] ) ? 0.0 : Double.parseDouble( utilEventFields[ORIGINAL_TRADE_PRICE_CCYAMT_POS] );
                        boolean buySell = BUY.equals( utilEventFields[BUYSELL_POS] );
                        char action = ACTION_APPLY.equals( utilEventFields[LAST_ACTION_POS] ) ? CreditLimit.ACTION_APPLY : ACTION_REMOVE.equals( utilEventFields[LAST_ACTION_POS] ) ?
                                CreditLimit.ACTION_REMOVE : CreditLimit.ACTION_NONE;
                        double limitCcyAmt = Double.parseDouble( utilEventFields[LIMIT_CCY_AMOUNT_POS] );
                        double origLimitCcyAmt = StringUtilC.isNullOrEmpty( utilEventFields[ORIGINAL_LIMIT_CCY_AMOUNT_POS] ) ? 0.0 : Double.parseDouble( utilEventFields[ORIGINAL_LIMIT_CCY_AMOUNT_POS] );
                        if ( CreditMessageEvent.USE.getName().equals( event ) && CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUseEventMessageHandlingDelaySimulationEnabled () )
                        {
                            log.info ( "CUSH.applyUtilizationEvents : Before sleeping on USE event. cclr=" + cclr );
                            Thread.sleep ( 10000 );
                            log.info ( "CUSH.applyUtilizationEvents : Finished sleeping on USE event. " + cclr );
                        }
                        cclr.getLock().lock();
                        long t0 = System.currentTimeMillis();
                        if ( log.isDebugEnabled() )
                        {
                            log.debug( new StringBuilder( 200 ).append( "CUSH.applyUtilizationEvents : before updating the ccyPos=" )
                                    .append( ccyPos ).append( ",cu=" ).append( cuId ).toString() );
                        }

                        Currency baseCcy = CurrencyFactory.getCachedCurrency( utilEventFields[BASE_CCY_POS] );
                        ccyPos.addUtilizationEventPosition( event, principalCcy, principalCcyAmt, priceCcy, priceCcyAmt,
                                tradeDate, valueDate, origPrincipalCcyAmt, origPriceCcyAmt, buySell, action, limitCcyAmt, 
                                origLimitCcyAmt, tradePrincipalCcyAmt, tradePriceCcyAmt, origTradePrincipalCcyAmt, origTradePriceCcyAmt, baseCcy );

                        if ( log.isDebugEnabled() )
                        {
                            log.debug( new StringBuilder( 200 ).append( "CUSH.applyUtilizationEvents : after updating the ccyPos=" ).append( ccyPos ).toString() );
                        }

                        dt = System.currentTimeMillis() - t0;
                    }
                    finally
                    {
                        cclr.getLock().unlock();
                    }

                    if ( dt > 1000 )
                    {
                        log.warn( new StringBuilder( 200 ).append( "CUSH.applyUtilizationEvents : time taken=" )
                                .append( dt ).append( ",cclr=" ).append( cclr ).toString() );
                    }
                    // calculate and set new used amounts.
                    CreditUtilizationCalculator calc = cu.getCreditLimitRule().getCreditUtilizationCalculator();
                    if(calc!=null){
                    	cu.setUsedAmount( calc.getRealtimeUtilizationAmount( cu ) );
                    }
					
                    if ( logMsg == 0 )
                    {
                        sb.append( "CUSH.applyUtilizationEvents : handling credit events for tid=" )
                                .append( tid ).append( ",event=" ).append( event ).append( ",eventSnapshot=" ).append( eventSnapshot );
                        logMsg = 1;
                    }
                    
                }
            }
            
			if ( persistenceEnabled ) {
				persistCreditUtilizations(cclrToCreditUtilsMap);
			}
            
            // release and refresh the credit lines                        
            if ( affectedSubscriptions != null )
            {
                // update the credit line manager available limits and relationships real-time.
                CreditLineManagerC.getInstance().reloadAndRefreshCreditLineCollections( affectedSubscriptions );
            }

            //Refresh Orphan creditLines
            for ( CurrencyLevelCreditLine creditLine: affectedOrphanCreditLines )
            {
                CreditLimitSubscriptionManagerC.getInstance().getCreditLineFromCreditEntity(
                        creditLine.getCurrencyPair(), creditLine.getValueDate(), new HashMap<String, FXPrice>(),
                        creditLine.getCreditProviderLe(), creditLine.getCreditTradingParty(), creditLine.isCPOMaker());
            }

            if ( creditLineKeys != null && !creditLineKeys.isEmpty() )
            {
                final String origTid = getOriginalTradeId( tid );
                sb.append ( ",origTid=" ).append ( origTid );
                for ( CurrencyPairLevelCreditLineKey creditLineKey : creditLineKeys )
                {
                    String releaseResult = CreditLineManagerC.getInstance().releaseLineLevel(creditLineKey, origTid);
                    if ( releaseResult != null )
                    {
                        sb.append ( "," ).append ( releaseResult );
                    }
                }
            }

            if ( logMsg > 0 )
            {
                CreditLimit.creditEventsLog.info ( sb.toString () );
            }

            return cclrs;
        }
        catch ( Exception e )
        {
            log.warn( "CUSH.applyUtilizationEvents : Error synchronizing the currency positions. eventSnapshot=" + eventSnapshot + ",tid=" + tid, e );
        }
        return null;
    }

    private static void collectAffectedOrphanCreditLines( Collection<CurrencyLevelCreditLine> affectedOrphanCreditLines, CurrencyPairLevelCreditLineKey creditLineKey )
    {
        CurrencyLevelCreditLine currencyLevelCreditLineAsMaker = CreditLineManagerC.getInstance().getCurrencyLevelCreditLine(creditLineKey, true);
        if(currencyLevelCreditLineAsMaker!=null && currencyLevelCreditLineAsMaker.isOrphan()){
            affectedOrphanCreditLines.add( currencyLevelCreditLineAsMaker );
        }
        CurrencyLevelCreditLine currencyLevelCreditLineAsTaker = CreditLineManagerC.getInstance().getCurrencyLevelCreditLine(creditLineKey, false);
        if(currencyLevelCreditLineAsTaker!=null && currencyLevelCreditLineAsTaker.isOrphan()){
            affectedOrphanCreditLines.add( currencyLevelCreditLineAsTaker );
        }
    }

    public static String createUtilEventSnapshot( CreditWorkflowMessage cwm )
    {
        Collection<CreditUtilizationEvent> events = cwm.getCreditUtilizationEventsForNotification();
        if ( events.isEmpty() )
        {
            log.warn( "CUSH.createUtilEventSnapshot : No events found for credit workflow message=" + cwm );
            return null;
        }
        StringBuilder snapshot = new StringBuilder( 200 );
        boolean initial = true;
        for ( CreditUtilizationEvent cue : events )
        {
            if ( !initial )
            {
                snapshot.append( UTIL_EVENT_DELIMITER );
            }
            String cpoShortName = cue.getTradingParty().getOrganization().getShortName();
            CreditUtilization cu = cue.getCreditUtilization();
            CreditLimitRule clr = cu.getCreditLimitRule();
            boolean isCashAccount = CreditUtilC.isCashSettlement ( clr );
            CounterpartyCreditLimitRule cclr = (CounterpartyCreditLimitRule) clr.getParentRule();

            snapshot.append( cu.getObjectID() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getPrincipalCurrency().getShortName() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getPriceCurrency().getShortName() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getPrincipal() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getPrice() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getBuySell() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getOriginalPrincipalAmount() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getOriginalPriceAmount() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getLastAction() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            //TODO: Can this settlementDate possibly be null ??
            final IdcDate posDate = isCashAccount ? cue.getTradeValueDate () : cue.getSettlementDate();
            snapshot.append( posDate != null ? posDate.getFormattedDate( IdcDate.DD_MMM_YYYY_HYPHEN ) : null );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getUsedAmount() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getOriginalUsedAmount() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getTradePrincipalAmount() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getTradePriceAmount() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getOriginalTradePrincipalAmount() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getOriginalTradePriceAmount() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( getCpoCcCcyPairKey( cue ) );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            boolean isDailyCu = cu instanceof DailyCreditUtilization;
            String cuCacheKey = null;
            if(isDailyCu){

                cuCacheKey = CreditUtilizationCacheC.getCacheKey(cpoShortName, cclr.getTradingPartyOrganization().getShortName(),
                        cclr.getTradingParty()!=null?cclr.getTradingParty().getObjectID():null, posDate);
            }else{
                cuCacheKey = CreditUtilizationCacheC.getAggregateCreditUtilizationCacheKey( cpoShortName, cclr.getTradingPartyOrganization().getShortName(),
                        cclr.getTradingParty() != null ? cclr.getTradingParty().getObjectID() : null );
            }
            snapshot.append(cuCacheKey);
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cpoShortName);
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( isDailyCu);
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getTradingParty().getObjectID() );
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( CurrencyFactory.getCurrencyPair( cue.getBaseCurrency(), cue.getVariableCurrency()).getIndex());
            snapshot.append( UTIL_EVENT_FIELD_DELIMITER );
            snapshot.append( cue.getBaseCurrency ().getShortName() );

            initial = false;
        }
        return snapshot.toString();
    }

    protected static Session getPersistenceSession() throws PersistenceException
    {
        return PersistenceFactory.newSession();
    }

    protected static CreditUtilization getCreditUtilizationByObjectId( long objectId, String key, String cpoStr, boolean isAggregateCU, Session session, boolean getCreditUtilFromCache )
    {
        boolean cuLookupSpacesEnabled = CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationLookupSpacesEnabled(cpoStr);
        if ( getCreditUtilFromCache || cuLookupSpacesEnabled)
        {
            CreditUtilization cu = CreditUtilizationManagerC.getInstance().
                    getCreditUtilizationCache().getCreditUtilizationFromCache( key, isAggregateCU );
            if(cu == null && isAggregateCU && !cuLookupSpacesEnabled)
            {
                Collection<CreditUtilization> creditUtils = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getAllCreditUtilizations();
                if ( creditUtils != null )
                {
                    for ( CreditUtilization cutil : creditUtils )
                    {
                        if ( cutil.getObjectID() == objectId )
                        {
                            return cutil;
                        }
                    }
                }
            }
            return cu;
        }
        else
        {
            return ( CreditUtilization ) session.getIdentityMapAccessor().getFromIdentityMap( objectId, CreditUtilizationC.class );
        }
    }

    /**
     * Creates a unique identifier for the credit line + currency pair combination. The format is 'Credit Provider Org short name':'Credit Cpty object Id':'Currency Pair':'valueDate'.
     *
     * @param cue credit utilization event.
     * @return credit line + ccy pair + valueDate key
     */
    private static String getCpoCcCcyPairKey( CreditUtilizationEvent cue )
    {
        return new StringBuilder( 64 ).append( cue.getNamespace().getShortName() ).append( ':' )
                .append( cue.getTradingParty().getObjectID() ).append( ':' ).
                append( CurrencyFactory.getCurrencyPairName( cue.getBaseCurrency(), cue.getVariableCurrency() ) ).
                append(':').append(cue.getSettlementDate().getFormattedDate(IdcDate.DD_MM_YYYY_HYPHEN)).toString();
    }
    
	private static String getOriginalTradeId(String tradeId)
    {
        if ( tradeId != null )
        {
            int index = tradeId.indexOf('C');
            return index == -1 ? tradeId : tradeId.substring(0, index);
        }
        return null;
	}
	
	private static void persistCreditUtilizations(HashMap<CounterpartyCreditLimitRule,HashSet<CreditUtilization>> cclrToCreditUtilsMap)
	{
		for ( Entry<CounterpartyCreditLimitRule, HashSet<CreditUtilization>> cclrSet : cclrToCreditUtilsMap.entrySet() ) {
			CounterpartyCreditLimitRule cclr = cclrSet.getKey();
			try {
				HashSet<CreditUtilization> creditUtils = cclrSet.getValue();
				if ( creditUtils == null || creditUtils.size() == 0 ) {
					continue;
				}
				Organization cpo = CreditUtilC.getCreditProviderOrganization((CreditLimitRuleSet) cclr.getRuleSet());
				log.info("persistCreditUtilizations : Persisting Credit Utilizations for cpo= " + cpo + " , CreditLimitRule=" + cclr);
				User contextUser = cpo.getDefaultDealingUser();
				IdcUtilC.setSessionContextUser(contextUser);
	            CreditUtilizationManagerC.getInstance().getCreditUtilizationPersistenceTask().addCreditUtilizations( cclr, creditUtils, false );
	            CreditUtilizationManagerC.getInstance().getCreditUtilizationNotificationTask().addCreditUtilizations( creditUtils, IdcUtilC.getSessionContextUser(), false );
	        
			}
			catch ( Exception e ) {
				log.error("persistCreditUtilizations : Error while persisting Credit Utilizations for CreditLimitRule=" + cclr, e);
			}
		}
	}
	
	private static CreditUtilization getCreditUtilizationFromDB( String cUId )
	{
		try {
			long cUtilId = Long.valueOf(cUId);
			return (CreditUtilization) ReferenceDataCacheC.getInstance().getEntityByObjectId(cUtilId, CreditUtilization.class);
		}
		catch ( Exception e ) {
			log.error("getCreditUtilizationFromDB : CU not found in db for id=" + cUId, e);
		}
		return null;
	}
}

