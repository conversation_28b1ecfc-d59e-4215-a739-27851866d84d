package com.integral.finance.creditLimit;

// Copyright (c) 2001-2005 Integral Development Corp. All rights reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.marketData.MarketDataSet;
import com.integral.message.WorkflowMessage;
import com.integral.time.IdcDate;
import com.integral.user.Organization;

import java.util.Collection;

/**
 * CreditWorkflowMessage is used to drive the execution of the credit process through
 * a credit limit rule set. To initiate a credit process, a credit workflow message
 * is created. The object is set to the object for which credit should be processed, if
 * this is a new credit operation. The rule set should be set to a credit limit rule set.
 * The event should be set to a value from the CreditLimit interface. There are two basic
 * types of credit operations. One is to allocate credit for a new trade and the other is
 * to update credit for an existing trade. The former requires that the object of this
 * workflow message be set to the trade. The later requires that this message be set
 * with the appropriate credit utilization event(s) that are to
 * be processed.
 *
 * <AUTHOR> Development Corp.
 */
public interface CreditWorkflowMessage extends WorkflowMessage
{
    /**
     * Credit workflow event codes associated with take credit.
     */
    int BEGIN_TAKE_CREDIT_EVENT = 0;
    int RETRIEVED_RULESET_EVENT = 1;
    int END_EXECUTE_RULESET_EVENT = 2;
    int BUILT_EVENTS_EVENT = 3;
    int BEGIN_PERFORM_EVENT = 4;
    int END_PERFORM_EVENT = 5;
    int SET_STATE_EVENT = 6;
    int END_TAKE_CREDIT_EVENT = 7;

    /**
     * Credit workflow events associated with take credit.
     */
    String BEGIN_TAKE_CREDIT = "BeginTake";
    String RETRIEVED_RULESET = "RetrievedRuleSet";
    String END_EXECUTE_RULESET = "ExecRuleSet";
    String BUILT_EVENTS = "BuiltUtilEvents";
    String BEGIN_PERFORM = "BeginPerform";
    String END_PERFORM = "EndPerform";
    String SET_STATE = "SetState";
    String END_TAKE_CREDIT = "EndTake";


    /**
     * Return the credit limit rule for this message. This is set when working
     * on a specific credit limit rule, otherwise it is null.
     *
     * @return credit limit rule
     */
    CreditLimitRule getCreditLimitRule();

    /**
     * Set the credit limit rule for this message. This is set when working
     * on a specific credit limit rule, otherwise it is null.
     *
     * @param clr credit limit rule
     */
    void setCreditLimitRule( CreditLimitRule clr );

    /**
     * Return the of matched credit limit rules.
     *
     * @return credit limit rule collection
     */
    Collection<CreditLimitRule> getCreditLimitRules();

    /**
     * Set the collection of matched credit limit rules.
     *
     * @param c collection of credit limit rules.
     */
    void setCreditLimitRules( Collection<CreditLimitRule> c );

    /**
     * Return the organization for which this message is intended.
     *
     * @return organization
     */
    Organization getOrganization();

    /**
     * Set the organization for the message.
     *
     * @param org organization
     */
    void setOrganization( Organization org );

    /**
     * Return the legal entity for the credit check. Can be null for an
     * update.
     *
     * @return legal entity
     */
    LegalEntity getLegalEntity();

    /**
     * Set the legal entity for the credit check.
     *
     * @param le legal entity
     */
    void setLegalEntity( LegalEntity le );

    /**
     * Return the trading party for the credit check.
     *
     * @return trading party
     */
    TradingParty getTradingParty();

    /**
     * Set the trading party for the credit check.
     *
     * @param tp trading party
     */
    void setTradingParty( TradingParty tp );

    /**
     * Return the trading party's organization.
     *
     * @return trading party organization
     */
    Organization getTradingPartyOrganization();

    /**
     * Sets the trading party organization.
     *
     * @param org organization
     */
    void setTradingPartyOrganization( Organization org );

    /**
     * Return a collection of credit utilization events to transact.
     *
     * @return credit utilization events
     */
    Collection<CreditUtilizationEvent> getCreditUtilizationEvents();

    /**
     * Set the collection of credit utilization events.
     *
     * @param events credit utilization events
     */
    void setCreditUtilizationEvents( Collection<CreditUtilizationEvent> events );

    /**
     * Return a collection of credit utilization events for notification
     *
     * @return credit utilization events for notification
     */
    Collection<CreditUtilizationEvent> getCreditUtilizationEventsForNotification();

    /**
     * Set the collection of credit utilization events for notification
     *
     * @param events credit utilization events
     */
    void setCreditUtilizationEventsForNotification( Collection<CreditUtilizationEvent> events );

    /**
     * Return the business date for the transaction.
     *
     * @return business date
     */
    IdcDate getBusinessDate();

    /**
     * Set the business date of the transaction.
     *
     * @param date date
     */
    void setBusinessDate( IdcDate date );

    /**
     * Return the market data set to use for determining prices
     *
     * @return market data set
     */
    MarketDataSet getMarketDataSet();

    /**
     * Set the market data set to use for determining prices
     *
     * @param mds market data set
     */
    void setMarketDataSet( MarketDataSet mds );

    /**
     * Return a Character value for the amount type, either use, reserve
     * or add to the utilization.
     *
     * @return amount type
     */
    Character getAmountType();

    /**
     * Set a Character value for for the amount type, either use, reserve
     * or add to the utilization.
     *
     * @param type type
     */
    void setAmountType( Character type );

    /**
     * Returns the counterparty credit limit rule which represents the credit relationship in the credit workflow.
     *
     * @return counterparty credit limit rule
     */
    CounterpartyCreditLimitRule getCounterpartyCreditLimitRule();

    /**
     * Sets the counterparty credit limit rule which represents the credit relationship in the credit workflow.
     *
     * @param cclr counterparty credit limit rule
     */
    void setCounterpartyCreditLimitRule( CounterpartyCreditLimitRule cclr );

    /**
     * Returns the event time based on the index.
     *
     * @param eventIndex eventIndex
     * @return event time in milliseconds
     */
    long getEventTime( int eventIndex );

    /**
     * Sets the event time in milliseconds for the event index specified.
     *
     * @param eventIndex event index
     * @param eventTime  event time
     */
    void setEventTime( int eventIndex, long eventTime );

    /**
     * Returns the error code in the credit workflow
     *
     * @return error code
     */
    String getErrorCode();

    /**
     * Sets the error code in the credit workflow.
     *
     * @param code code
     */
    void setErrorCode( String code );

    /**
     * Returns the description of the credit failure reason.
     *
     * @return description
     */
    String getErrorDescription();

    /**
     * Sets the description of the credit failure reason.
     *
     * @param errorDescription error description
     */
    void setErrorDescription( String errorDescription );

    /**
     * Returns the transaction id associated with the credit entity.
     *
     * @return transaction id
     */
    String getTransactionId();

    /**
     * Sets the transaction id associated with the credit entity.
     *
     * @param tid transaction id
     */
    void setTransactionId( String tid );

    /**
     * Returns the trade date from the credit entity.
     *
     * @return trade date
     */
    IdcDate getTradeDate();

    /**
     * Sets the trade date of the credit entity.
     *
     * @param tradeDate trade date
     */
    void setTradeDate( IdcDate tradeDate );

    /**
     * Returns whether credit workflow is exempted.
     *
     * @return exempted
     */
    boolean isCreditExempt();

    /**
     * Sets whether credit workflow is exempted or not.
     *
     * @param exempt exempt
     */
    void setCreditExempt( boolean exempt );

    boolean isTenorRestrictionsBypassed();

    void setTenorRestrictionsBypassed ( boolean flag );

    boolean isOverUtilized();

    void setOverUtilized ( boolean overUtilized );

    String getCreditLineDescription();
}

