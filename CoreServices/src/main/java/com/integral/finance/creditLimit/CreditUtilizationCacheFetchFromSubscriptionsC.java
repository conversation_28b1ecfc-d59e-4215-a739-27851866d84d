package com.integral.finance.creditLimit;

import com.integral.admin.ha.PrimaryAdminIdentifier;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.configuration.CreditConfigurationFactory;
import com.integral.finance.price.fx.FXPrice;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.persistence.util.LogUtil;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.IdcUtilC;
import com.integral.workflow.dealing.DealingLimit;
import com.integral.workflow.dealing.fx.FXDealingLimit;
import com.integral.workflow.dealing.fx.FXDealingLimitCollection;

import java.util.*;

/**
 * <AUTHOR> Development Corporation.
 */
public class CreditUtilizationCacheFetchFromSubscriptionsC implements Runnable
{
    public final static int FETCH = 1;
    public final static int REBUILD = 2;
    public final static int REVALUATE_MARGIN = 3;

    public static final String CREDIT_RELATION_DELIMITER = "_";

    private static final Log log = LogFactory.getLog( CreditUtilizationCacheFetchFromSubscriptionsC.class );
    private int eventCode = FETCH;
    private static final Object LOCK = new Object ();

    private Set<String> creditRelations = null;

    public CreditUtilizationCacheFetchFromSubscriptionsC()
    {
    }

    public CreditUtilizationCacheFetchFromSubscriptionsC( int anEventCode )
    {
        this.eventCode = anEventCode;
    }

    /**
     * Executes event based on the code. Fetch event periodically fetches the credit utilization to memory based on all the subscriptions
     */
    public void run()
    {
        switch ( eventCode )
        {
            case FETCH:
            {
                if ( creditRelations != null )
                {
                    for ( String creditRelation: creditRelations )
                    {
                        String[] components = creditRelation.split ( CREDIT_RELATION_DELIMITER );
                        Organization takerOrg = ReferenceDataCacheC.getInstance ().getOrganization ( components[0] );
                        Organization makerOrg = ReferenceDataCacheC.getInstance ().getOrganization ( components[1] );
                        boolean handleAllLegalEntities = CreditConfigurationFactory.getCreditConfigurationMBean ().isCreditWarmupAllLegalEntitiesOnSubscriptionEnabled ( takerOrg );
                        if ( !handleAllLegalEntities )
                        {
                            fetchAllLegalEntitiesCreditUtilizations ( takerOrg, makerOrg );
                        }
                    }
                }
                else
                {
                    fetchCreditUtilizations ( null, null );
                }
                break;
            }
            case REBUILD:
            {
                rebuildStaleCreditUtilizationCacheEntries();
                break;
            }
            case REVALUATE_MARGIN:
            {
                revaluateMargin();
                break;
            }
        }
    }

    public void fetchCreditUtilizations( final Organization cpo, final Organization cptyOrg )
    {
        try
        {
            LogUtil.setDisableDBQueryLogSwitch( true );
            long t0 = System.currentTimeMillis();
            if ( log.isDebugEnabled() )
            {
                log.debug( "CUFS.fetchCreditUtilizations : Time invoked at " + new Date() );
            }

            Map<String, CreditRelationSubscriptionInfo> subscriptionMap = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptionMap();
            Collection<IdcDate> majorCcySpotDates = CreditUtilC.getDefaultSpotDates();
            majorCcySpotDates.addAll ( CreditUtilC.calculateDefaultSpotDates ( EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ().addDays( 1 ) ) );

            for ( CreditRelationSubscriptionInfo si : subscriptionMap.values() )
            {
                try
                {
                    LegalEntity cpl = si.getCreditProviderLegalEntity();
                    TradingParty cc = si.getCreditCounterparty();
                    if ( cpl == null || cc == null )
                    {
                        log.warn( "CUFS.fetchCreditUtilizations : creditProviderLe or credit cpty is null. cpl=" + cpl + ",cc=" + cc );
                        continue;
                    }

                    Organization cco = cc.getLegalEntityOrganization();

                    // if credit provider org is specified, and it is not the same as subscription, then skip this subscription
                    if ( cpo != null && !cpo.isSameAs( cpl.getOrganization() ) )
                    {
                        continue;
                    }

                    // if the credit counterparty organization is specified, and it is not the same as in the subscription info, then skip this subscription
                    if ( cptyOrg != null && !cptyOrg.isSameAs( cco ) )
                    {
                        continue;
                    }

                    final FXDealingLimitCollection dlc = ( FXDealingLimitCollection ) si.getDealingLimitCollection();
                    final Set<IdcDate> spotDates = new HashSet<IdcDate> ( majorCcySpotDates );

                    if ( dlc != null && dlc.getDealingLimits() != null )
                    {
                        Collection<DealingLimit> dealingLimits = dlc.getDealingLimits();
                        for ( DealingLimit dl : dealingLimits )
                        {
                            FXDealingLimit fxDl = ( FXDealingLimit ) dl;
                            IdcDate spotDate = CreditUtilC.getSpotDate( fxDl.getBaseCurrency(), fxDl.getVariableCurrency() );
                            if ( spotDate != null )
                            {
                                spotDates.add( spotDate );
                            }
                        }
                    }

                    boolean customerOrgsFetchEnabled = CreditConfigurationFactory.getCreditConfigurationMBean().isCustomerOrgsCreditUtilizationFetchEnabled();
                    CreditLimitSubscriptionManagerC.getInstance().addCreditUtilizationsForAllLEs( cpl.getOrganization(), cco, spotDates, false, customerOrgsFetchEnabled, CreditLimitConstants.REVAL_CREDIT_EVENT, null );
                }
                catch ( Exception e )
                {
                    log.warn( "CUFS.fetchCreditUtilizations : Exception fetching CU's. key=" + si, e );
                }

            }

            String msg = cpo != null ? " cpo=" + cpo + ",cco=" + cptyOrg : "";
            log.info( new StringBuilder( 200 ).append( "CUFS.fetchCreditUtilizations : Fetch of credit utilizations took ms=" )
                    .append( System.currentTimeMillis() - t0 ).append( msg ).toString() );
        }
        catch ( Exception e )
        {
            log.warn( "CUFS.fetchCreditUtilizations : Exception while fetching credit utilizations", e );
        }
        finally
        {
            LogUtil.removeDisableDBQueryLogSwitch();
        }
    }

    public void fetchAllLegalEntitiesCreditUtilizations( final Organization takerOrg, final Organization makerOrg )
    {
        try
        {
            LogUtil.setDisableDBQueryLogSwitch( true );
            long t0 = System.currentTimeMillis();
            if ( log.isDebugEnabled() )
            {
                log.debug( "CUFS.fetchAllLegalEntitiesCreditUtilizations : Time invoked at " + new Date() );
            }

            Set<IdcDate> majorCcySpotDates = CreditUtilC.getDefaultSpotDates();
            majorCcySpotDates.addAll ( CreditUtilC.calculateDefaultSpotDates ( EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ().addDays( 1 ) ) );

            boolean customerOrgsFetchEnabled = CreditConfigurationFactory.getCreditConfigurationMBean().isCustomerOrgsCreditUtilizationFetchEnabled();
            CreditLimitSubscriptionManagerC.getInstance().addCreditUtilizationsForAllLEs( takerOrg, makerOrg, majorCcySpotDates, false, customerOrgsFetchEnabled, CreditLimitConstants.REVAL_CREDIT_EVENT, null );
            String msg = ". takerOrg=" + takerOrg + ",makerOrg=" + makerOrg;
            log.info( new StringBuilder( 200 ).append( "CUFS.fetchAllLegalEntitiesCreditUtilizations : Fetch of all legal entities credit utilization took ms=" )
                    .append( System.currentTimeMillis() - t0 ).append( msg ).toString() );
        }
        catch ( Exception e )
        {
            log.warn( "CUFS.fetchAllLegalEntitiesCreditUtilizations : Exception while fetching credit utilizations. takerOrg=" + takerOrg + ",makerOrg=" + makerOrg, e );
        }
        finally
        {
            LogUtil.removeDisableDBQueryLogSwitch();
        }
    }

    public void fetchTradingPartyCreditUtilizations( final Organization cpo, final Organization cptyOrg, final TradingParty creditTp )
    {
        try
        {
            LogUtil.setDisableDBQueryLogSwitch( true );
            long t0 = System.currentTimeMillis();
            if ( log.isDebugEnabled() )
            {
                log.debug( "CUFS.fetchTradingPartyCreditUtilizations : Time invoked at " + new Date() );
            }

            Map<String, CreditRelationSubscriptionInfo> subscriptionMap = CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().getCreditLimitSubscriptionMap();
            Collection<IdcDate> majorCcySpotDates = CreditUtilC.getDefaultSpotDates();
            majorCcySpotDates.addAll ( CreditUtilC.calculateDefaultSpotDates ( EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ().addDays( 1 ) ) );

            for ( CreditRelationSubscriptionInfo si : subscriptionMap.values() )
            {
                try
                {
                    LegalEntity cpl = si.getCreditProviderLegalEntity();
                    TradingParty cc = si.getCreditCounterparty();
                    if ( cpl == null || cc == null )
                    {
                        log.warn( "CUFS.fetchTradingPartyCreditUtilizations : creditProviderLe or credit cpty is null. cpl=" + cpl + ",cc=" + cc );
                        continue;
                    }

                    Organization cco = cc.getLegalEntityOrganization();

                    // if credit provider org is specified, and it is not the same as subscription, then skip this subscription
                    if ( cpo != null && !cpo.isSameAs( cpl.getOrganization() ) )
                    {
                        continue;
                    }

                    // if the credit counterparty organization is specified, and it is not the same as in the subscription info, then skip this subscription
                    if ( cptyOrg != null && !cptyOrg.isSameAs( cco ) )
                    {
                        continue;
                    }

                    final FXDealingLimitCollection dlc = ( FXDealingLimitCollection ) si.getDealingLimitCollection();
                    final Set<IdcDate> spotDates = new HashSet<IdcDate> ( majorCcySpotDates );

                    if ( dlc != null && dlc.getDealingLimits() != null )
                    {
                        Collection<DealingLimit> dealingLimits = dlc.getDealingLimits();
                        for ( DealingLimit dl : dealingLimits )
                        {
                            FXDealingLimit fxDl = ( FXDealingLimit ) dl;
                            IdcDate spotDate = CreditUtilC.getSpotDate( fxDl.getBaseCurrency(), fxDl.getVariableCurrency() );
                            if ( spotDate != null )
                            {
                                spotDates.add( spotDate );
                            }
                        }
                    }

                    CreditLimitSubscriptionManagerC.getInstance ().addCreditUtilizationsForLEs ( cpl, creditTp.getLegalEntity (), spotDates, CreditLimitConstants.REVAL_CREDIT_EVENT, null );
                }
                catch ( Exception e )
                {
                    log.warn( "CUFS.fetchTradingPartyCreditUtilizations : Exception fetching CU's. key=" + si, e );
                }
            }

            log.info( new StringBuilder( 200 ).append( "CUFS.fetchTradingPartyCreditUtilizations : Fetch of credit utilizations took ms=" )
                    .append( System.currentTimeMillis() - t0 ).append( ",cpo=" ).append ( cpo ).append ( ",cco=" )
                    .append ( cptyOrg ).append ( ",tp=" ).append ( creditTp ).toString() );
        }
        catch ( Exception e )
        {
            log.warn( "CUFS.fetchTradingPartyCreditUtilizations : Exception while fetching credit utilizations", e );
        }
        finally
        {
            LogUtil.removeDisableDBQueryLogSwitch();
        }
    }

    public void rebuildStaleCreditUtilizationCacheEntries()
    {
        try
        {
            //If it is not a CreditworkflowServer, why bother rebuildingCUCache ?
            if (!ConfigurationFactory.getServerMBean().isCreditWorkflowServer()) {
                return;
            }

            LogUtil.setDisableDBQueryLogSwitch( true );
            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().rebuildAllStaleEntries();
        }
        catch ( Exception e )
        {
            log.warn( "CUFS.rebuildStaleCreditUtilizationCacheEntries : rebuild all stale entries", e );
        }
        finally
        {
            LogUtil.removeDisableDBQueryLogSwitch();
        }
    }

    public void revaluateMargin( )
    {
        synchronized ( LOCK )
        {
            try
            {
                if ( !ConfigurationFactory.getServerMBean().isApplicationStarted() )
                {
                    return;
                }
                if ( CreditUtilC.isMarginRevaluationEnabled () )
                {
                    Collection<CounterpartyCreditLimitRule> autoStopEnabledRules = CreditUtilizationManagerC.getInstance ().getCreditUtilizationCache ().getAutoStopOutEnabledRules ();
                    if ( log.isDebugEnabled () )
                    {
                        log.info( "CUFS.revaluateMargin - retrieved autoStopEnabledRules=" + autoStopEnabledRules );
                    }
                    Map<String, FXPrice> rateMap = new HashMap<String, FXPrice>( 20 );
                    for ( CounterpartyCreditLimitRule cclr : autoStopEnabledRules )
                    {
                        if ( cclr.isActive () && CreditUtilC.isCreditActive ( cclr ) )
                        {
                            CreditLimitRuleSet clrs = ( CreditLimitRuleSet ) cclr.getRuleSet ();
                            Organization cpo = CreditUtilC.getCreditProviderOrganization ( clrs );
                            CreditUtilizationManagerC.getInstance ().getAggregateCreditUtililzations( cpo );//ensures currency positions are loaded.
                            Double stopOutPercent = cclr.getStopOutPercentage ();
                            if ( stopOutPercent == null || stopOutPercent == 0.0 )
                            {
                                stopOutPercent = clrs.getStopOutPercentage ();
                            }
                            if ( stopOutPercent != null && stopOutPercent > 0.0 )
                            {
                                Collection<CreditUtilization> creditUtils = new ArrayList<CreditUtilization> ();
                                SingleCreditLimitRule clr = ( SingleCreditLimitRule ) cclr.getChildRule ( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
                                if ( clr != null && clr.getCreditUtilizationCalculator () != null && clr.isAllowNetting () )
                                {
                                    CreditUtilization cu = clr.getCreditUtilization ();
                                    if ( cu == null )
                                    {
                                        log.warn ( "CUFS.revaluateMargin - No credit utilization found for clr=" + clr + ",cclr=" + cclr );
                                        continue;
                                    }
                                    CreditUtilC.updateCurrencyPositionConversionRates ( cu, rateMap, false );
                                    cu.recalculateRealtimeCreditUtilization ();
                                    creditUtils.add ( cu );
                                    User user = cpo.getDefaultDealingUser ();
                                    if ( user == null )
                                    {
                                        user = !cpo.getUsers ().isEmpty () ? ( User ) cpo.getUsers().toArray ()[0] : null;
                                    }
                                    if ( user != null )
                                    {
                                        IdcUtilC.setSessionContextUser ( user );
                                        CreditNotificationManagerC.getInstance ().sendUtilizationLevelNotification ( creditUtils, true );
                                    }
                                    else
                                    {
                                        log.info( "CUFS.revaluateMargin - No user found in the credit provider org=" + cpo + ",defaultUser=" + cpo.getDefaultDealingUser () );
                                    }
                                }
                            }
                        }
                        else if ( log.isDebugEnabled () )
                        {
                            log.debug( "CUFS.revaluateMargin - skipping cpty rule being no check or inactive. cptyRule=" + cclr );
                        }
                    }
                }
                else if ( log.isDebugEnabled () )
                {
                    boolean isAdminServer = ConfigurationFactory.getServerMBean ().isAdminServer ();
                    boolean isPrimaryAdmin = PrimaryAdminIdentifier.getInstance().isPrimary();
                    log.debug( "CUFS.revaluateMargin - margin evaluation is not enabled. adminServer=" + isAdminServer + ",primaryAdmin="  + isPrimaryAdmin );
                }
            }
            catch ( Exception e )
            {
                log.error ( "CUFS.revaluateMargin - Exception while revaluation of margin usage.", e );
            }
        }
    }

    public Set<String> getCreditRelations ( )
    {
        return creditRelations;
    }

    public void setCreditRelations ( Set<String> creditRelations )
    {
        this.creditRelations = creditRelations;
    }


}
