package com.integral.finance.creditLimit;

// Copyright (c) 2016 Integral Development Corp.  All rights reserved.

import com.integral.finance.trade.Tenor;

/**
 * This class is used to define a tenor restriction along with whether it is in business days or calendar days
 */
public class CreditTenorRestriction
{
    private Tenor tenor;
    private boolean businessDays;

    private CreditTenorRestriction()
    {
	}

    public CreditTenorRestriction( Tenor aTenor, boolean businessDaysLag )
    {
        this();
        this.tenor = aTenor;
        this.businessDays = businessDaysLag;
    }

    public Tenor getTenor()
    {
        return tenor;
    }

    public boolean isBusinessDays()
    {
        return businessDays;
    }
}
