package com.integral.finance.creditLimit.calculator;

// Copyright (c) 2001-2005 Integral Development Corp. All rights reserved.

import com.integral.finance.creditLimit.*;
import com.integral.finance.currency.Currency;
import com.integral.finance.instrument.AmountOfInstrument;
import com.integral.finance.marketData.MarketDataSet;
import com.integral.finance.price.fx.FXPrice;
import com.integral.time.IdcDate;
import com.integral.workflow.dealing.DealingLimit;

import java.util.Map;

/**
 * CreditUtilizationCalculatorProxy is used to determine the impact on credit
 * utilization due to a credit utilization event.
 *
 * <AUTHOR> Development Corp.
 */
public interface CreditUtilizationCalculatorProxy
{
    /**
     * These represent basic algorithms on currency positions.
     * net receivable - In this algorithm only net receivable currency positions are converted to credit limit currency and added to get total utilization.
     * greater net - In this algorithm, the greater absolute value of net payable positions and net receivable positions are used to arrive at total utilization.
     * aggregate net - In this algorithm, the total payables and total receivables credit currency equivalent are added together to arrive at total utilization.
     */
    int NET_RECEIVABLE = 0;
    int GREATER_NET = 1;
    int AGGREGATE_NET = 2;
    int AGGREGATE_NET_CASH = 3;

    String getShortName();

    void setShortName( String name );

    /**
     * Return true if the credir utilization event will not
     * go over the credit limit.
     *
     * @param cu  credit utilization
     * @param cue credit utilization event
     * @return check result
     */
    boolean checkAmount( CreditUtilization cu, CreditUtilizationEvent cue );

    /**
     * Add the credit utilization event to the credit utilization and updates the dealing limit collection.
     *
     * @param cu                        credit utilization
     * @param cue                       credit utilization event
     * @param mds                       market data set
     * @param exceessUtilizationAllowed excess allowed
     * @param rateMap                   rate map
     * @return result of operation
     */
    boolean applyAmount( CreditUtilization cu, CreditUtilizationEvent cue, MarketDataSet mds, boolean exceessUtilizationAllowed, Map<String, FXPrice> rateMap );

    /**
     * Remove the credit utilization event from the credit utilization and updates the dealing limit collection.
     *
     * @param cu      credit utiliztion
     * @param cue     credit utilization event
     * @param mds     market data set
     * @param rateMap rate map
     * @return result of operation
     */
    boolean removeAmount( CreditUtilization cu, CreditUtilizationEvent cue, MarketDataSet mds, Map<String, FXPrice> rateMap );

    /**
     * Update the credit utilization for a credit utilization event with
     * the new amount and updates the limit collection. The new amount is usually obtained from the credit
     * utilization event using calculateAmount.
     *
     * @param cu        credit utilization
     * @param cue       credit utilization event
     * @param newAmount new amount
     * @return result of operation
     */
    boolean updateAmount( CreditUtilization cu, CreditUtilizationEvent cue, double newAmount );

    /**
     * Update the credit utilization for a credit utilization event with
     * the new updated amounts for currency pair sent by provider and updates the limit collection.
     *
     * @param cu                       credit utilization
     * @param cue                      credit utilization event
     * @param principalAoi             principal aoi
     * @param priceAoi                 price aoi
     * @param mds                      market data set
     * @param excessUtilizationAllowed excess
     * @param rateMap                  rate map
     * @return result of operation
     */
    boolean updateUtilizedAmount( CreditUtilization cu, CreditUtilizationEvent cue, AmountOfInstrument principalAoi, AmountOfInstrument priceAoi, MarketDataSet mds, boolean excessUtilizationAllowed, Map<String, FXPrice> rateMap );

    /**
     * Builds the net credit utilization events from the existing credit utilization events.
     *
     * @param cu credit utilization
     * @return currency position collection
     */
    CurrencyPositionCollection buildCurrencyPositions( CreditUtilization cu );

    /**
     * Builds the net currency positions from the currency position snapshot.
     *
     * @param cu credit utilization
     * @return currency position collection
     */
    CurrencyPositionCollection buildCurrencyPositionsFromSnapshot( CreditUtilization cu );

    /**
     * Builds the net currency positions from the currency position snapshot.
     *
     * @param cu credit utilization
     * @return currency position collection
     */
    CurrencyPositionCollection buildCurrencyPositionsFromNextDateSnapshot( CreditUtilization cu );

    /**
     * Builds the empty currency positions as a shell.
     *
     * @param cu credit utilization
     * @return currency position collection
     */
    CurrencyPositionCollection buildCurrencyPositionsShell( CreditUtilization cu );

    /**
     * Returns the currency position collection for the specified credit utilization with the specified base date.
     *
     * @param cu       credit utilization
     * @param baseDate base date
     * @return currency position collecton object
     */
    CurrencyPositionCollection getCurrencyPositions( CreditUtilization cu, IdcDate baseDate );

    /**
     * Recalculates the used and reserved amount based on the list of credit utilization events.
     *
     * @param cu credit utilization
     */
    void recalculateUtilization( CreditUtilization cu );

    /**
     * Returns the real time utlization amount by revaluating the currency positions using the latest market data. In this
     * case, the amount is indicative and does not update the credit utilization with the new amount.
     *
     * @param cu credit utilization
     * @return realtime used amount
     */
    double getRealtimeUtilizationAmount( CreditUtilization cu );

    /**
     * Returns the real time available credit amount by revaluating the currency positions using the latest market data. In this
     * case, the amount is indicative and does not update the credit utilization with the new amount.
     *
     * @param cu credit utilization
     * @return realtime available amount.
     */
    double getRealtimeAvailableAmount( CreditUtilization cu );

    /**
     * Returns the dealing limit of the credit utilization for the currency pair formed by base currency and variable currency.
     *
     * @param cu      credit utilization
     * @param baseCcy base currency
     * @param varCcy  variable currency
     * @param date    date
     * @param rateMap rate map
     * @param isMaker maker
     * @return dealing limit
     */
    DealingLimit getDealingLimit( CreditUtilization cu, Currency baseCcy, Currency varCcy, IdcDate date, Map<String, FXPrice> rateMap, boolean isMaker );

    /**
     * Updates the real time credit utilization based on the current currency positions and market data.
     *
     * @param rcu real-time credit utilization
     */
    void updateRealtimeCreditUtilization( RealtimeCreditUtilization rcu );

    /**
     * Returns whether any available limit is there for the specified set of currency positions for the credit utilization.
     * @param cps currency position collection
     * @param cu credit utilization
     * @return credit available
     */
    boolean isCreditAvailable( CurrencyPositionCollection cps, CreditUtilization cu );
}

