package com.integral.finance.creditLimit.handler;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.admin.CreditFunctorServerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;

/**
 * This class is used as an end of day handler to revaluate all the aggregate type credit utilizations.
 *
 * <AUTHOR> Development Corp.
 */
public class RevaluateAggregateCreditUtilizationHandler implements MessageHandler
{
    protected Log log = LogFactory.getLog( getClass() );
    private static final Object lockObject = new Object();

    public Message handle( Message msg )
    {
        try
        {
            synchronized ( lockObject )
            {
                CreditFunctorServerC.getInstance().getStandAloneCreditFunctor( CreditFunctorServerC.REVALUATE_AGGREGATE_CREDIT_UTILIZATION ).execute();
            }
        }
        catch ( Exception e )
        {
            log.error( "RevaluateAggregateCreditUtilizationHandler.handle.ERROR : exception", e );
        }
        return null;
    }
}
