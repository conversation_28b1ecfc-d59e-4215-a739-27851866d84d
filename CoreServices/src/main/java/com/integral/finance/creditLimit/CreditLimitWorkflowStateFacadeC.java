package com.integral.finance.creditLimit;

// Copyright (c) 2001-2005 Integral Development Corp. All rights reserved.

import com.integral.facade.EntityFacadeC;
import com.integral.facade.FacadeFactory;

import java.util.Collection;

/**
 * CreditLimitWorkflowStateFacadeC is used to wrap the credit limit workflow state.
 * It provides helper methods to determine credit states and filter the credit utilization events and audit events.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitWorkflowStateFacadeC extends EntityFacadeC implements CreditLimitWorkflowStateFacade
{
    static
    {
        FacadeFactory.setFacade( CreditLimitWorkflowStateFacadeC.class.getName(), CreditLimitWorkflowState.class, CreditLimitWorkflowStateFacadeC.class );
    }

    public CreditLimitWorkflowState getCreditLimitWorkflowState()
    {
        return ( CreditLimitWorkflowState ) getEntity();
    }

    public Collection<CreditUtilizationEvent> getCreditUtilizationEvents()
    {
        return getCreditLimitWorkflowState().getCreditUtilizationEvents();
    }

    public void setCreditUtilizationEvents( Collection<CreditUtilizationEvent> creditUtilEvents )
    {
        getCreditLimitWorkflowState().setCreditUtilizationEvents( creditUtilEvents );
    }

    public boolean isCreditReserved()
    {
        return CreditLimitWorkflowStateUtilC.CREDIT_RESERVED.equals( getCreditLimitWorkflowState().getState() );
    }

    public void setCreditReserved()
    {
        getCreditLimitWorkflowState().transitionState( CreditLimitWorkflowStateUtilC.CREDIT_RESERVED, null );
    }

    public boolean isCreditUsed()
    {
        return CreditLimitWorkflowStateUtilC.CREDIT_USED.equals( getCreditLimitWorkflowState().getState() );
    }

    public void setCreditUsed()
    {
        getCreditLimitWorkflowState().transitionState( CreditLimitWorkflowStateUtilC.CREDIT_USED, null );
    }

    public boolean isCreditUndone()
    {
        return CreditLimitWorkflowStateUtilC.CREDIT_UNDONE.equals( getCreditLimitWorkflowState().getState() );
    }

    public void setCreditUndone()
    {
        getCreditLimitWorkflowState().transitionState( CreditLimitWorkflowStateUtilC.CREDIT_UNDONE, null );
    }

}
