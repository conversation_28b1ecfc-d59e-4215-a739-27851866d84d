package com.integral.finance.creditLimit.quickcheck;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.creditLimit.CreditEntity;
import com.integral.finance.creditLimit.CreditLimit;
import com.integral.finance.currency.CurrencyPair;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.time.IdcDate;
import com.integral.user.OrganizationC;
import com.integral.util.StringUtilC;
import com.integral.workflow.dealing.fx.FXDealingLimit;
import com.integral.workflow.dealing.fx.FXWorkflowFactory;

/**
 * This is the bucket holding creditlines information for a give FI-LP-CcyPair combination. 
 * It has operations to take and undo credit
 * <AUTHOR>
 *
 */
public class CreditLineCollectionC implements CreditLineCollection {
	
	private LegalEntity fiLe;
	private LegalEntity lpLe;
	private CurrencyPair ccyPair;
	private IdcDate valueDate;
	
	private AtomicBoolean creditSuspended = new AtomicBoolean(false);
	
	private CurrencyLevelCreditLine[] currencyLevelCreditLines;
	
	private AtomicBoolean isDirty;
	private AtomicBoolean brokerCreditLines = new AtomicBoolean(false);

	private volatile FXDealingLimit dealingLimit;
	private FXDealingLimit zeroDealingLimit;
	
	private Log log = LogFactory.getLog(getClass());

	private LP_FI lpFi;

	public CreditLineCollectionC(LegalEntity fiLe, LegalEntity lpLe, CurrencyPair ccyPair, IdcDate valueDate)
	{
		this.fiLe = fiLe;
		this.lpLe = lpLe;
		this.ccyPair = ccyPair;
		this.valueDate = valueDate;
		isDirty = new AtomicBoolean(false);
		dealingLimit = FXWorkflowFactory.newFXSingleDealingLimit();
		dealingLimit.setBaseCurrency( ccyPair.getBaseCurrency() );
		dealingLimit.setVariableCurrency( ccyPair.getVariableCurrency() );
		
		zeroDealingLimit = FXWorkflowFactory.newFXSingleDealingLimit();
		zeroDealingLimit.setBaseCurrency( ccyPair.getBaseCurrency() );
		zeroDealingLimit.setVariableCurrency( ccyPair.getVariableCurrency() );
		zeroDealingLimit.setBidLimit( 0 );	
		zeroDealingLimit.setOfferLimit( 0 );

		lpFi = new LP_FI( lpLe.getOrganization(), fiLe.getOrganization() );
	}

	public double getAvailableCredit(boolean isBid, boolean forOrderMatch)
	{
		if(creditSuspended.get() && forOrderMatch)
		{
			return 0;
		}
		return isBid ? dealingLimit.getBidLimit() : dealingLimit.getOfferLimit();
	}

	public FXDealingLimit getAvailableDealingLimit(boolean forOrderMatch)
	{
		if(creditSuspended.get() && forOrderMatch)
		{
			return zeroDealingLimit;
		}
		return dealingLimit;
	}

	/**
	 * 	Check if there is enough credit. 
	 *  <ul><li>If yes, then consume credit and return true</li>
	 *	<li>If no, then return false</li></ul>
	 */
	public ReserveCreditResult reserveCredit(double requestedAmount, boolean isBid, String transactionId, List<CreditEntity> creditEntities)
	{
		ReserveCreditResult result = ReserveCreditResult.SUCCESS;		
		StringBuilder sb = new StringBuilder(200).append("reserveCredit: ReservingCredit for ").append(transactionId)
                .append(" | RequestedAmt=").append(requestedAmount).append(" | bid=" ).append( isBid );

        if(creditEntities==null || creditEntities.isEmpty())
		{						
			result = ReserveCreditResult.NOOP;
			sb.append(" | NOOP : because no valid credit relationships found to evaluate for " + transactionId);
		}
		else
		{
			boolean reserveCreditSuccessful = true;
			long t1 = System.currentTimeMillis();
			for(CreditEntity creditEntity:creditEntities)
			{
				if( creditEntity.getTradingParty() == null ){
					reserveCreditSuccessful = false;
					log.warn("reserveCredit : TradingParty is NULL on CreditEntity. creditEntity= "+creditEntity);
					break;
				}
				if( creditEntity.getOrganization().isSameAs(creditEntity.getLegalEntity().getOrganization())){
					log.info("reserveCredit : Same credit provider & customer for tId="+transactionId+", creditEntity="+creditEntity);
					continue;
				}
                CurrencyLevelCreditLine creditLine = CreditLineManagerC.getInstance().getCurrencyLevelCreditLineLoadIfNotExists(creditEntity, ccyPair, valueDate);
				if(creditLine!=null)
                {
                    if(!creditLine.isDummy())
                    {
                        sb.append(" | CreditLine=").append(creditLine.toString());
                        if(!creditLine.reserveCredit(requestedAmount, transactionId, isBid))
                        {
                            // Oops! there isn't enough credit to reserve ! Operation failed !!
                            reserveCreditSuccessful = false;
                            break;
                        }
                    }
                }
                else if ( OrganizationC.class.equals ( creditEntity.getOrganization ().getClass () ) ) // skipping credit for mock organizations.
                {
                    reserveCreditSuccessful = false;
                    log.error("reserveCredit: No CreditLine corresponding to creditentity: " + creditEntity);
                    break;
                }
                else
				{
					log.info("reserveCredit: No CreditLine corresponding to creditentity: " + creditEntity);
				}
			}
			if(log.isDebugEnabled())
			{
				long t2 = System.currentTimeMillis()-t1;
				log.debug("CreditLineCollection.reserveCredit : TIMETAKEN = " + t2);
			}
			if(!reserveCreditSuccessful)
			{	
				// Reserve unsuccessful. So Revert the amount we reserved till now and return false.
				// Note:: if we don't revert, then the lines show lesser credit than they are supposed to. 
				// This may impact other trades where these shared lines are used
                for(CreditEntity creditEntity:creditEntities)
				{
					if( creditEntity.getTradingParty() == null ){
						continue;
					}
                    CurrencyLevelCreditLine creditLine = CreditLineManagerC.getInstance().getCurrencyLevelCreditLineLoadIfNotExists(creditEntity, ccyPair, valueDate);
					if(creditLine!=null && !creditLine.isDummy()){
						creditLine.release(transactionId);
					}
				}				
				result = ReserveCreditResult.FAIL;
				sb.append(" | FAIL: Insufficient credit");
			}
			else
			{
				sb.append(" | SUCCESS");
			}
		}
		//if(!ReserveCreditResult.NOOP.equals(result)){
		CreditLimit.creditEventsLog.info( sb.toString() );
		//}
		return result;
	}
	
	public void releaseAndrefresh(String transactionId, List<CreditEntity> creditEntities)
	{
		String result = "";
        for ( CreditEntity creditEntity:creditEntities )
        {
			if( creditEntity.getTradingParty() == null ){
				log.info("releaseAndrefresh : TradingParty is NULL on CreditEntity. tid="+transactionId+", creditEntity="+creditEntity);
				continue;
			}
            CurrencyLevelCreditLine creditLine = CreditLineManagerC.getInstance().getCurrencyLevelCreditLineLoadIfNotExists( creditEntity, ccyPair, valueDate );
			if( creditLine != null )
            {
				result += creditLine.releaseAndRefresh( transactionId );
			}
            else
            {
                log.error( "releaseAndrefresh: No CreditLine corresponding to creditentity: " + creditEntity );
            }
		}
		if ( !StringUtilC.isNullOrEmpty (result ) )
		{
			log.info( "CLC.releaseAndrefresh: releasing reserve for tid=" + transactionId  + ",result=" + result );
		}
	}
		
	public void resetAllCreditLines(CurrencyLevelCreditLine[] aCurrencyLevelCreditLines)
	{		
		this.currencyLevelCreditLines = aCurrencyLevelCreditLines;
		for(CurrencyLevelCreditLine currencyLevelCreditLine:currencyLevelCreditLines)
		{
			if(currencyLevelCreditLine!=null){
				currencyLevelCreditLine.addInterestedCreditLineCollection(this);
			}
		}
	}

	public boolean isEmtpy()
	{
		return this.currencyLevelCreditLines.length==0;
	}
	
	public void calculateMinAvailableAmounts()
	{		
		if(isDirty.get())
		{
			forceCalculateMinAvailableAmounts ();
			isDirty.set(false);
		}
	}

	public void forceCalculateMinAvailableAmounts()
	{
		double minOffer=Double.MAX_VALUE, minBid=Double.MAX_VALUE;
		if( hasBrokerCreditLines() ){
			minBid = 0.0D;
			minOffer = 0.0D;
		}
		else {
			for (CurrencyLevelCreditLine creditLine : currencyLevelCreditLines) {
				//If DummyLine, don't bother considering it in calculating MinAvailable amounts. Only consider if its a genuine line.
				if (creditLine != null && !creditLine.isDummy()) {
					double minBidFromCreditLine = creditLine.getMinAvailableBid();
					if (Double.compare(minBidFromCreditLine, minBid) < 0) {
						minBid = minBidFromCreditLine;
					}

					double minOfferFromCreditLine = creditLine.getMinAvailableOffer();
					if (Double.compare(minOfferFromCreditLine, minOffer) < 0) {
						minOffer = minOfferFromCreditLine;
					}
				}
			}
		}
		dealingLimit.setBidLimit( minBid );
		dealingLimit.setOfferLimit( minOffer );
	}

	public void reEvaluateSuspensionStatus(){
		for(CurrencyLevelCreditLine currencyLevelCreditLine:currencyLevelCreditLines){			
			if(currencyLevelCreditLine!=null && currencyLevelCreditLine.isCreditSuspended())
			{
				creditSuspended.set(true);
				return;
			}
		}		
		creditSuspended.set(false);
	}
	
	public LegalEntity getFILE(){
		return fiLe;
	}
	
	public LegalEntity getLPLE(){
		return lpLe;
	}
	
	public void setLPLE(LegalEntity lpLe){
		this.lpLe = lpLe;
	}
	
	public CurrencyPair getCurrencyPair(){
		return ccyPair;
	}
	
	public IdcDate getValueDate(){
		return valueDate;
	}
	
	public void setValueDate(IdcDate valueDate){
		this.valueDate = valueDate;
	}
	
	public boolean forSameValueDate(IdcDate aValueDate){
		return valueDate.isSameAs(aValueDate);
	}
	
	public void markDirty(){
		this.isDirty.set(true);
	}

	public boolean hasBrokerCreditLines(){
		return this.brokerCreditLines.get();
	}

	public void setBrokenCreditLines(boolean hasBrokenCreditLines){
		this.brokerCreditLines.set(hasBrokenCreditLines);
	}

	public LP_FI getLP_FI()
	{
		return lpFi;
	}

	public String toString()
    {
        StringBuilder sb = new StringBuilder( 100 );
        sb.append( "fiLe=" ).append( fiLe.getFullyQualifiedName() );
        sb.append( ", lpLe=" ).append( lpLe.getFullyQualifiedName() );
        sb.append( ", ccyPair=" ).append( ccyPair.getName() );
        sb.append( ", valueDate=" ).append( valueDate.getFormattedDate( IdcDate.DD_MM_YYYY_HYPHEN ) );
        sb.append( ", dl=").append( dealingLimit );
        sb.append( ", Lines=[");
        if ( currencyLevelCreditLines != null )
		{
			for ( CurrencyLevelCreditLine line : currencyLevelCreditLines )
			{
				if ( line != null )
				{
					sb.append ( line.toString () ).append ( ',' );
				}
			}
		}
		else
		{
			sb.append ( "null" );
		}
        sb.append(']');
        return sb.toString();
    }

    //For Diagnostics page
	public CurrencyLevelCreditLine[] getCreditLines(){
		return currencyLevelCreditLines;
	}
	
	//For Diagnostics page
	public double getMinAvailableBid(){
		return dealingLimit.getBidLimit();
	}
	
	//For Diagnostics page
	public double getMinAvailableOffer(){
		return dealingLimit.getOfferLimit();
	}
}