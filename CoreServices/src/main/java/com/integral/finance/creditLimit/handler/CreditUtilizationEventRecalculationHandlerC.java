package com.integral.finance.creditLimit.handler;

// Copyright (c) 2013 Integral Development Corp.  All rights reserved.

import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationFactory;
import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationMBean;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.time.IdcDate;
import com.integral.user.Organization;

import java.util.Collection;

/**
 * This class is used as an end of day handler to recalculate credit utilization events based on the tenor coefficients.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditUtilizationEventRecalculationHandlerC implements MessageHandler
{
    protected Log log = LogFactory.getLog( CreditUtilizationEventRecalculationHandlerC.class );

    public Message handle( Message message )
    {
        try
        {
            long t0 = System.currentTimeMillis();
            final IdcDate businessDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            CreditAdminConfigurationMBean _creditAdminMBean = CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean();
            Collection<Organization> orgs = _creditAdminMBean.isEndOfDayCreditUtilizationEventRevaluationEnabled() ? CreditUtilC.getOrganizationsWithPFESet() : _creditAdminMBean.getEndOfDayCreditUtilizationEventRevaluationEnabledOrgs();
            if ( orgs != null && !orgs.isEmpty() )
            {
                CreditUtilizationManagerC.getInstance().recalculateAllCreditUtilizationEvents( businessDate, null, orgs );
                log.info( new StringBuilder( 200 ).append( "CUERH.handle : Finished recalculating credit utilization events. orgs=" )
                        .append( orgs ).append( ",timeTaken=" ).append( System.currentTimeMillis() - t0 ).toString() );

            }
        }
        catch ( Exception e )
        {
            log.error( "CUERH.handle : Exception while recalculating credit utilization events.", e );
        }
        return message;
    }
}

