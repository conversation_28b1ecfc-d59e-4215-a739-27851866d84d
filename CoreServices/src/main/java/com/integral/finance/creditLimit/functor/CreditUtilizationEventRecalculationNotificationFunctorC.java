package com.integral.finance.creditLimit.functor;

import com.integral.finance.creditLimit.CreditLimit;
import com.integral.finance.creditLimit.CreditLimitConstants;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;

import java.util.HashMap;
import java.util.List;

public class CreditUtilizationEventRecalculationNotificationFunctorC extends CreditRemoteNotificationFunctorC
{
    private static final Log log = LogFactory.getLog( CreditUtilizationEventRecalculationNotificationFunctorC.class );

    public void onCommit( HashMap props )
    {
        // do not process the message if remote notification processing is not enabled.
        if ( !creditAdminConfig.isCreditRemoteNotificationProcessingEnabled() )
        {
            return;
        }

        long t0 = System.currentTimeMillis();
        Organization cpo;
        try
        {
            String event = ( String ) props.get( CreditLimitConstants.EVENT_PROPERTY );
            Object obj = props.get( CreditLimit.CREDIT_PROVIDER_ORGS );
            log.info( "CERNF.onCommit : cpo orgs param=" + obj + ",class=" + obj.getClass() );
            String[] creditProviderOrgGuids = null;
            if ( obj instanceof String[] )
            {
                creditProviderOrgGuids = ( String[] ) obj;
            }
            else if ( obj instanceof List ) // with rabbitmq mesasging, string array is getting serialized as ArrayList
            {
                List guidList = ( List ) obj;
                creditProviderOrgGuids = new String[ guidList.size() ];
                int i = 0;
                for ( Object guidObj: guidList )
                {
                    creditProviderOrgGuids[i] = (String) guidObj;
                    i++;
                }
            }
            else
            {
                log.info( "CERNF.onCommit : Unsupported parameter type. cpo orgs param=" + obj + ",class=" + obj.getClass() );
            }

            if ( creditProviderOrgGuids != null )
            {
                for ( String creditProviderOrgGuid : creditProviderOrgGuids )
                {
                    cpo = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditProviderOrgGuid, Organization.class );

                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "CERNF.onCommit : updating cache for cpo=" + cpo );
                    }

                    if ( cpo != null )
                    {
                        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().rebuildProviderOrganizationCreditUtilizations( cpo, event );
                    }
                    else
                    {
                        log.warn( "CERNF.onCommit : credit provider org is not found for guid=" + creditProviderOrgGuid );
                    }
                }
            }

            log.info( new StringBuilder( 200 ).append( "CERNF.onCommit : updated the cache for all orgs whose events are updated. props=" )
                    .append( props ).append( ",timeTaken=" ).append( System.currentTimeMillis() - t0 ).toString() );
        }
        catch ( Exception e )
        {
            log.error( "CERNF.onCommit : Exception while handling the admin notification on credit utilization event revaluation. props=" + props, e );
        }
    }
}