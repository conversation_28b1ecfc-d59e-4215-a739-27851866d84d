package com.integral.finance.creditLimit.calculator;

// Copyright (c) 2001-2005 Integral Development Corp.  All rights reserved.

import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationFactory;
import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationMBean;
import com.integral.finance.account.AccountEnums;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CounterpartyCreditLimitRule;
import com.integral.finance.creditLimit.CreditLimit;
import com.integral.finance.creditLimit.CreditLimitFactory;
import com.integral.finance.creditLimit.CreditLimitRule;
import com.integral.finance.creditLimit.CreditUtilizationEvent;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.FXLeg;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXRate;
import com.integral.finance.marketData.MarketDataSet;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.trade.Trade;
import com.integral.persistence.Entity;
import com.integral.time.IdcDate;
import com.integral.user.Organization;


/**
 * FXDealingPriceNotionalCreditUtilizationEventBuilderC is used to create the credit utilization events based on the
 * information from the dealing price and amount type.
 *
 * <AUTHOR> Development Corp.
 */
public class FXDealingPriceNotionalCreditUtilizationEventBuilderC extends CreditUtilizationEventBuilderC
{
    private static DealingDataPurgeConfigurationMBean _configMBean = DealingDataPurgeConfigurationFactory.getDealingDataPurgeConfigurationMBean();

    public CreditUtilizationEvent createCreditUtilizationEvent( Organization creditProviderOrg, TradingParty creditCpty, CreditLimitRule creditRule, char amountType, Entity aLeg, boolean calculateLimitCurrencyAmount, boolean isCreditProviderMaker, IdcDate currentTradeDate, double tenorCoefficient, boolean skipUpdateAccountReceivable, boolean isAcceptance )
    {
        if ( !( aLeg instanceof FXLegDealingPrice ) )
        {
            log.error( "FXDealingPriceNotionalCreditUtilizationEventBuilderC.createCreditUtilizationEvent.ERROR - Wrong credit entity type" );
            return null;
        }
        if ( amountType != CreditLimit.AMOUNT_RESERVE )
        {
            log.warn( "FXDealingPriceNotionalCreditUtilizationEventBuilderC.createCreditUtilizationEvent.WARN - Wrong credit amount type" );
            return null;
        }
        CreditUtilizationEvent cue = CreditLimitFactory.newCreditUtilizationEvent();
        CreditUtilizationEvent registeredCue = ( CreditUtilizationEvent ) cue.getRegisteredObject();
        registeredCue.setNamespace( creditRule.getParentRule().getRuleSet().getNamespace() );
        FXLegDealingPrice fxLegDealingPrice = ( FXLegDealingPrice ) aLeg;
        FXMarketDataSet fxMds = ( FXMarketDataSet ) getMarketDataSet( creditProviderOrg );
        populateFXPosition( registeredCue, fxLegDealingPrice, creditRule, fxMds, creditCpty, calculateLimitCurrencyAmount, tenorCoefficient );
        if ( !checkTradeLegMaturity( fxLegDealingPrice, currentTradeDate ) )
        {
            registeredCue.setErrorCode( CreditLimit.ERROR_INVALID_VALUE_DATE );
        }
        registeredCue.setEventType ( AccountEnums.AccountEventType.TRADE );
        return registeredCue;
    }

    @Override
    public CreditUtilizationEvent createCreditUtilizationEvent(Organization creditProviderOrg, TradingParty creditCpty, CreditLimitRule creditRule, char amountType, Entity aLeg, boolean calculateLimitCurrencyAmount, boolean isCreditProviderMaker, IdcDate currentTradeDate, double tenorCoefficient, boolean skipUpdateAccountReceivable) {
        return createCreditUtilizationEvent(  creditProviderOrg,  creditCpty,  creditRule,  amountType,  aLeg,  calculateLimitCurrencyAmount,  isCreditProviderMaker,  currentTradeDate,  tenorCoefficient,  skipUpdateAccountReceivable, false );
    }

    /**
     * Populates the fxPosition with various trade related information.
     *
     * @param fxCreditUtilEvent            credit utilization event
     * @param fxLegDealingPrice            fx leg dealing price
     * @param creditRule                   credit limit rule
     * @param fxMds                        market data set
     * @param creditCpty                   credit counterparty
     * @param calculateLimitCurrencyAmount boolean representing whether to calculate limit amount
     * @param tenorCoefficient             tenor cofficient
     */
    private void populateFXPosition( CreditUtilizationEvent fxCreditUtilEvent, FXLegDealingPrice fxLegDealingPrice, CreditLimitRule creditRule, FXMarketDataSet fxMds, TradingParty creditCpty, boolean calculateLimitCurrencyAmount, double tenorCoefficient )
    {
        FXLeg fxLeg = ( FXLeg ) fxLegDealingPrice.getFXLeg().getRegisteredObject();
        FXPaymentParameters fxPayment = fxLegDealingPrice.getFXPayment();

        Trade trade = ( Trade ) fxLeg.getTrade().getRegisteredObject();
        CounterpartyCreditLimitRule cptyRule = ( CounterpartyCreditLimitRule ) creditRule.getParentRule();
        fxCreditUtilEvent.setTransactionId( trade.getTransactionID() ); // Trade is no longer set on CUE. Make sure tid is set.
        fxCreditUtilEvent.setOrderId ( trade.getOrderId () );
        fxCreditUtilEvent.setTradingParty( creditCpty );
        fxCreditUtilEvent.setTradingPartyOrganization( creditCpty.getLegalEntityOrganization() );
        fxCreditUtilEvent.setLegalEntity( cptyRule.getLegalEntity() );
        final IdcDate valDate = fxPayment.getValueDate();
        fxCreditUtilEvent.setSettlementDate( valDate );
        fxCreditUtilEvent.setTradeValueDate ( valDate );
        fxCreditUtilEvent.setTimeToLive( _configMBean.getTTL( fxCreditUtilEvent.getClass(), valDate ) );
        fxCreditUtilEvent.setTradeDate( trade.getTradeDate() );
        Currency creditLimitCcy = creditRule.getCurrency();
        double principalAmt = fxLegDealingPrice.getDealt().getAmount();
        Currency principalCcy = ( Currency ) fxLegDealingPrice.getDealt().getInstrument();
        fxCreditUtilEvent.setPrincipal( principalAmt );
        fxCreditUtilEvent.setPrincipalCurrency( principalCcy );
        fxCreditUtilEvent.setPrice( 0.0 );
        fxCreditUtilEvent.setPriceCurrency( fxLegDealingPrice.getSettledCurrency() );
        fxCreditUtilEvent.setInstrument( creditLimitCcy );
        fxCreditUtilEvent.setTradePrincipalAmount( principalAmt );
        fxCreditUtilEvent.setTradePriceAmount( 0.0 );
        fxCreditUtilEvent.setTenorCoefficient( tenorCoefficient );

        if ( tenorCoefficient == CreditLimit.TENOR_COEFFICIENT_NA )
        {
            fxCreditUtilEvent.setErrorCode( CreditLimit.ERROR_TENOR_COEFFICIENT_NOT_SET_FOR_TENOR );
        }
        else if ( tenorCoefficient != CreditLimit.DEFAULT_TENOR_COEFFICIENT )
        {
            fxCreditUtilEvent.setPrincipal( principalCcy.round( principalAmt * tenorCoefficient ) );
            fxCreditUtilEvent.setPrice( 0.0 );
        }


        if ( log.isDebugEnabled() )
        {
            String tradeNs = trade.getNamespace() != null ? trade.getNamespace().getShortName() : null;
            log.debug( new StringBuilder( 400 ).append( "FXDealingPriceNotionalCreditUtilizationEventBuilderC.populateFXPosition.DEBUG - namespace=" )
                    .append( tradeNs ).append( ",txId=" ).append( trade.getTransactionID() ).append( ",dealtCcy=" )
                    .append( fxLegDealingPrice.getDealtCurrency() ).append( ",settledCcy=" )
                    .append( fxLegDealingPrice.getSettledCurrency() ).append( ",limitCcy=" ).append( creditLimitCcy )
                    .append( ",fxMds=" ).append( fxMds ).toString() );
        }

        if ( !calculateLimitCurrencyAmount )
        {
            return;
        }

        boolean checkPrinCcyRate = true;
        boolean checkPrcCcyRate = true;
        double reservedAmt = 0.0;
        if ( creditLimitCcy.isSameAs( fxLegDealingPrice.getDealtCurrency().getRealCurrency () ) )
        {
            checkPrinCcyRate = false;
            reservedAmt = fxLegDealingPrice.getDealtAmount();
        }
        else if ( creditLimitCcy.isSameAs( fxLegDealingPrice.getSettledCurrency().getRealCurrency () ) )
        {
            checkPrcCcyRate = false;
        }

        Currency prinCcy = fxCreditUtilEvent.getPrincipalCurrency();
        Currency prcCcy = fxCreditUtilEvent.getPriceCurrency();

        if ( checkPrinCcyRate )
        {
            FXMarketDataElement fxMde = fxMds.findSpotConversionMarketDataElement( creditLimitCcy, prinCcy, true );
            if ( fxMde != null && fxMde.getFXRate() != null && fxMde.getFXRate().getRate() > 0.0 )
            {
                FXRate fxRate = fxMde.getFXRate();
                reservedAmt = fxRate.getAmount( fxCreditUtilEvent.getPrincipal(), prinCcy );
                if ( CreditLimit.creditCalcLog.isInfoEnabled() )
                {
                    CreditLimit.creditCalcLog.info( new StringBuilder( 400 ).append( "FXDealingPriceNotionalCreditUtilizationEventBuilderC.setLimitCurrencyAmount.INFO - FXRateSource=" )
                            .append( fxMds.getShortName() ).append( ",realtimeMds=" ).append( fxMds.isRealtime() ).append( ",mdeOwner=" )
                            .append( fxMde.getOwner() ).append( ",mdeType=" ).append( fxMde.getDescription() ).append( ",ccyPair=" )
                            .append( fxRate.getCurrencyPair().toString() ).append( ",spotRate=" ).append( fxRate.getSpotRate() )
                            .append( ",fwdPoints=" ).append( fxRate.getForwardPoints() ).append( ",rate=" ).append( fxRate.getRate() )
                            .append( ",mdeDate=" ).append( fxMde.getValueDate() ).append( ",mdeTenor=" ).append( fxMde.getTenor() ).append( ",txId=" )
                            .append( fxCreditUtilEvent.getTransactionId() ).toString() );

                    // check the mde owner is extended market data set.
                    if ( fxMde.getOwner() instanceof MarketDataSet && ( ( MarketDataSet ) fxMde.getOwner() ).getExtendedMarketDataSet() == null )
                    {
                        MarketDataSet clrsMds = fxMds.isRealtime() ? fxMds.getSessionInstance() : fxMds;
                        MarketDataSet extendedMds = clrsMds.getExtendedMarketDataSet();
                        if ( extendedMds != null )
                        {
                            extendedMds = extendedMds.isRealtime() ? extendedMds.getSessionInstance() : extendedMds;
                        }
                        CreditLimit.creditCalcLog.info( new StringBuilder( 200 ).append( "FXDealingPriceNotionalCreditUtilizationEventBuilderC.setLimitCurrencyAmount.INFO - fxMds=" )
                                .append( clrsMds ).append( ",clrsMds.size=" ).append( clrsMds.getMarketDataElements().size() ).append( ",extendedMds=" )
                                .append( extendedMds ).append( ",extendedMds.size=" ).append( extendedMds != null ? extendedMds.getMarketDataElements().size() : -1 ).toString() );
                    }
                }
            }
            else
            {
                log.error( new StringBuilder( 200 ).append( "FXDealingPriceNotionalCreditUtilizationEventBuilderC.setLimitCurrencyAmount.ERROR - No spot FXRate found for ccyPair=" )
                        .append( CurrencyFactory.getCurrencyPairName( creditLimitCcy, prinCcy ) ).append( ",mds=" ).append( fxMds.getShortName() ).append( ",txId=" )
                        .append( fxCreditUtilEvent.getTransactionId() ).toString() );

                fxCreditUtilEvent.setErrorCode( CreditLimit.ERROR_CONVERSION_RATE_UNAVAILABLE );
                fxCreditUtilEvent.setReservedAmount( creditLimitCcy.round( reservedAmt ) );
                return;
            }
        }

        if ( checkPrcCcyRate )
        {
            FXMarketDataElement fxMde = fxMds.findSpotConversionMarketDataElement( creditLimitCcy, prcCcy, true );
            if ( fxMde != null && fxMde.getFXRate() != null && fxMde.getFXRate().getRate() > 0.0 )
            {
                FXRate fxRate = fxMde.getFXRate();
                if ( reservedAmt == 0.0 )
                {
                    reservedAmt = fxRate.getAmount( fxCreditUtilEvent.getPrincipal(), prinCcy );
                }
                if ( CreditLimit.creditCalcLog.isInfoEnabled() )
                {
                    CreditLimit.creditCalcLog.info( new StringBuilder( 400 ).append( "FXDealingPriceNotionalCreditUtilizationEventBuilderC.setLimitCurrencyAmount.INFO - FXRateSource=" )
                            .append( fxMds.getShortName() ).append( ",realtimeMds=" ).append( fxMds.isRealtime() ).append( ",mdeOwner=" )
                            .append( fxMde.getOwner() ).append( ",mdeType=" ).append( fxMde.getDescription() ).append( ",ccyPair=" )
                            .append( fxRate.getCurrencyPair() ).append( ",spotRate=" ).append( fxRate.getSpotRate() )
                            .append( ",fwdPoints=" ).append( fxRate.getForwardPoints() ).append( ",rate=" )
                            .append( fxRate.getRate() ).append( ",mdeDate=" ).append( fxMde.getValueDate() ).append( ",mdeTenor=" )
                            .append( fxMde.getTenor() ).append( ",txId=" )
                            .append( fxCreditUtilEvent.getTransactionId() ).toString() );

                    // check the mde owner is extended market data set.
                    if ( fxMde.getOwner() instanceof MarketDataSet && ( ( MarketDataSet ) fxMde.getOwner() ).getExtendedMarketDataSet() == null )
                    {
                        MarketDataSet clrsMds = fxMds.isRealtime() ? fxMds.getSessionInstance() : fxMds;
                        MarketDataSet extendedMds = clrsMds.getExtendedMarketDataSet();
                        if ( extendedMds != null )
                        {
                            extendedMds = extendedMds.isRealtime() ? extendedMds.getSessionInstance() : extendedMds;
                        }
                        CreditLimit.creditCalcLog.info( new StringBuilder( 200 ).append( "FXDealingPriceNotionalCreditUtilizationEventBuilderC.setLimitCurrencyAmount.INFO - fxMds=" )
                                .append( clrsMds ).append( ",clrsMds.size=" ).append( clrsMds.getMarketDataElements().size() ).append( ",extendedMds=" )
                                .append( extendedMds ).append( ",extendedMds.size=" ).append( extendedMds != null ? extendedMds.getMarketDataElements().size() : -1 ).toString() );
                    }
                }
            }
            else
            {
                log.error( new StringBuilder( 200 ).append( "FXDealingPriceNotionalCreditUtilizationEventBuilderC.setLimitCurrencyAmount.ERROR - No spot FXRate found for ccyPair=" )
                        .append( CurrencyFactory.getCurrencyPairName( creditLimitCcy, prcCcy ) ).append( ",mds=" ).append( fxMds.getShortName() ).append( ",txId=" )
                        .append( fxCreditUtilEvent.getTransactionId() ).toString() );

                fxCreditUtilEvent.setErrorCode( CreditLimit.ERROR_CONVERSION_RATE_UNAVAILABLE );
                fxCreditUtilEvent.setReservedAmount( creditLimitCcy.round( reservedAmt ) );
                return;
            }
        }
        fxCreditUtilEvent.setReservedAmount( creditLimitCcy.round( reservedAmt ) );
    }

    /**
     * Checks whether the trade leg is matured by comparing the trade leg's value date
     * with the current business date in the market dataset associated with the credit limit ruleset.
     *
     * @param fxDp             fx leg dealing price
     * @param currentTradeDate current trade date
     * @return true if trade leg maturity date is earlier than the current business date
     */
    private boolean checkTradeLegMaturity( FXLegDealingPrice fxDp, IdcDate currentTradeDate )
    {
        if ( fxDp.getValueDate().isEarlierThan( currentTradeDate ) )
        {
            log.error( new StringBuilder( 200 ).append( "FXDealingPriceNotionalCreditUtilizationEventBuilderC.checkTradeLegMaturity.ERROR - Invalid Value Date=" )
                    .append( fxDp.getValueDate() ).append( ",businessDate=" ).append( currentTradeDate ).append( ",txId=" )
                    .append( fxDp.getFXLeg().getTrade().getTransactionID() ).toString() );
            return false;
        }
        return true;
    }

}
