package com.integral.finance.creditLimit;

// Copyright (c) 2022 Integral Development Corp.  All rights reserved.

/**
 * This class is used to execute the margin call and auto close out of positions.
 *
 * <AUTHOR> Development Corp.
 */
public interface MarginCallService
{
    void initialize();

    void closePositions( CreditWorkflowMessage wm );

    void onClosePositionsStatusUpdate( CreditWorkflowMessage cwm );
}