package com.integral.finance.creditLimit.calculator;

// Copyright (c) 2021 Integral Development Corp. All rights reserved.

/**
 * This calculator is based on the aggregate net settlement that calculates the utilization amount using net receivable amount only.
 *
 * <AUTHOR> Development Corp.
 */
public class AggregateNetSettlementReceivableCreditUtilizationCalculatorProxyC extends CreditUtilizationCalculatorProxyC
{
    AggregateNetSettlementReceivableCreditUtilizationCalculatorProxyC( String name )
    {
        super();
        this.setShortName ( name );
    }
}
