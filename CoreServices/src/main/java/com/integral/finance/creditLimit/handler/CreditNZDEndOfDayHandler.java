package com.integral.finance.creditLimit.handler;

// Copyright (c) 2015 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;

/**
 * This class is used to handle the all the credit related end of day activities related to NZD roll time.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditNZDEndOfDayHandler implements MessageHandler
{
    protected Log log = LogFactory.getLog( getClass() );

    public Message handle( Message msg )
    {
        try
        {
            // re-initialize all the tenor coefficients.
            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().updateAllCreditTenorProfiles();
        }
        catch ( Exception e )
        {
            log.error( "NEDH.handle : error while end of day NZD roll over", e );
        }
        return null;
    }
}
