package com.integral.finance.creditLimit;

import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationFactory;
import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationMBean;
import com.integral.finance.creditLimit.configuration.CreditConfigurationFactory;
import com.integral.finance.creditLimit.configuration.CreditConfigurationMBean;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.message.MessageEvent;
import com.integral.message.MessageFactory;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateConstants;

import java.util.TimeZone;
import java.util.regex.Pattern;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

/**
 * This interface is used to provide constants which are invariably used in credit limit service and credit limit
 * admin service.
 *
 * <AUTHOR> Development Corp.
 */
public interface CreditLimitConstants
{
    // credit limit rule set names and description
    String CREDIT_LIMIT_RULE_SET_SHORT_NAME = "CreditLimitRuleSet";
    String CREDIT_LIMIT_RULE_SET_LONG_NAME = "FI Credit limit Rule Set";
    String CREDIT_LIMIT_RULE_SET_DESCRIPTION = "Financial Institution Credit Limit Rule Set";
    String COUNTERPARTY_CREDIT_LIMIT_RULE_SHORT_NAME = "CounterpartyCreditLimitRule";
    String CREDIT_LIMIT_RULE_SET_INDEX_PROPERTY_TRADING_PARTY = "tradingParty.shortName";
    String CREDIT_LIMIT_RULE_SET_INDEX_PROPERTY_TRADING_PARTY_ORGANIZATION = "tradingPartyOrganization.shortName";

    // types of credit limit rules and their short names.
    String GROSS_NOTIONAL_CLASSIFICATION_SHORT_NAME = "GROSSNOTIONAL";
    String DAILY_SETTLEMENT_CLASSIFICATION_SHORT_NAME = "DAILYSETTLEMENT";
    String GROSS_NOTIONAL_RULE_SHORT_NAME = "GrossNotional";
    String DAILY_SETTLEMENT_RULE_SHORT_NAME = "DailySettlement";

    // credit limit classifications.
    CreditLimitClassification GROSS_NOTIONAL_CLASSIFICATION = CreditUtilC.getCreditLimitClassification( GROSS_NOTIONAL_CLASSIFICATION_SHORT_NAME );
    CreditLimitClassification DAILY_SETTLEMENT_CLASSIFICATION = CreditUtilC.getCreditLimitClassification( DAILY_SETTLEMENT_CLASSIFICATION_SHORT_NAME );

    // types of credit netting methodology
    String DAILY_SETTLEMENT_LIMIT_CALCULATOR_SHORT_NAME = "DailyNetReceivable";
    String AGGREGATE_LIMIT_CALCULATOR_SHORT_NAME = "AggregateNetReceivable";
    String NET_OPEN_POSITION_CALCULATOR_SHORT_NAME = "AggregateNetSettlement";
    String GROSS_DAILY_LIMIT_CALCULATOR_SHORT_NAME = "DailyGrossSettlement";
    String GROSS_AGGREGATE_LIMIT_CALCULATOR_SHORT_NAME = "AggregateGrossSettlement";
    String AGGREGATE_NETPR_SETTLEMENT_CALCULATOR_SHORT_NAME = "AggregateNetPRSettlement";
    String AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR_SHORT_NAME = "AggregateNetCashSettlement";
    String AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR_SHORT_NAME = "AggregateNetSettlementReceivable";
    String AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR_SHORT_NAME = "AggregateMultiFactorSettlement";
    String AGGREGATE_NET_CREDITNET_SETTLEMENT_CALCULATOR_SHORT_NAME = "AggregateNetCreditNetSettlement";
    String DAILY_NONE_CALCULATOR_SHORT_NAME = "DN";
    String AGGREGATE_NONE_CALCULATOR_SHORT_NAME = "AN";
    String DAILY_SETTLEMENT_LIMIT_CALCULATOR_SHORT_CODE = "DNR";
    String AGGREGATE_LIMIT_CALCULATOR_SHORT_CODE = "ANR";
    String NET_OPEN_POSITION_CALCULATOR_SHORT_CODE = "ANS";
    String GROSS_DAILY_LIMIT_CALCULATOR_SHORT_CODE = "DGS";
    String GROSS_AGGREGATE_LIMIT_CALCULATOR_SHORT_CODE = "AGS";
    String AGGREGATE_NETPR_SETTLEMENT_CALCULATOR_SHORT_CODE = "ANSPR";
    String AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR_SHORT_CODE = "ANCS";
    String AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR_SHORT_CODE = "ANSR";
    String AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR_SHORT_CODE = "AMS";
    String AGGREGATE_NET_CREDITNET_SETTLEMENT_CALCULATOR_SHORT_CODE = "ACNS";

    // credit utilization calculators
    CreditUtilizationCalculator DAILY_SETTLEMENT_LIMIT_CALCULATOR = CreditUtilC.getCreditUtilizationCalculator( DAILY_SETTLEMENT_LIMIT_CALCULATOR_SHORT_NAME );
    CreditUtilizationCalculator AGGREGATE_LIMIT_CALCULATOR = CreditUtilC.getCreditUtilizationCalculator( AGGREGATE_LIMIT_CALCULATOR_SHORT_NAME );
    CreditUtilizationCalculator NET_OPEN_POSITION_CALCULATOR = CreditUtilC.getCreditUtilizationCalculator( NET_OPEN_POSITION_CALCULATOR_SHORT_NAME );
    CreditUtilizationCalculator GROSS_DAILY_LIMIT_CALCULATOR = CreditUtilC.getCreditUtilizationCalculator( GROSS_DAILY_LIMIT_CALCULATOR_SHORT_NAME );
    CreditUtilizationCalculator GROSS_AGGREGATE_LIMIT_CALCULATOR = CreditUtilC.getCreditUtilizationCalculator( GROSS_AGGREGATE_LIMIT_CALCULATOR_SHORT_NAME );
    CreditUtilizationCalculator AGGREGATE_NPR_SETTLEMENT_CALCULATOR = CreditUtilC.getCreditUtilizationCalculator( AGGREGATE_NETPR_SETTLEMENT_CALCULATOR_SHORT_NAME );
    CreditUtilizationCalculator AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR = CreditUtilC.getCreditUtilizationCalculator( AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR_SHORT_NAME );
    CreditUtilizationCalculator AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR = CreditUtilC.getCreditUtilizationCalculator( AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR_SHORT_NAME );
    CreditUtilizationCalculator AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR = CreditUtilC.getCreditUtilizationCalculator( AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR_SHORT_NAME );
    CreditUtilizationCalculator AGGREGATE_NET_CREDITNET_SETTLEMENT_CALCULATOR = CreditUtilC.getCreditUtilizationCalculator( AGGREGATE_NET_CREDITNET_SETTLEMENT_CALCULATOR_SHORT_NAME );
    CreditUtilizationCalculator DAILY_NONE_CALCULATOR = null;
    CreditUtilizationCalculator AGGREGATE_NONE_CALCULATOR = null;

    // constants which are currently used but will be removed in future releases when new domain attributes are used.
    String CREDIT_LIMIT_RULESET_CUSTOM_FIELD_NAME = "DirectFX_CreditLimitRuleSet";
    String DEFAULT_DEALING_ENTITY_CUSTOM_FIELD_NAME = "DirectFX_DefaultDealingEntity";
    String EXTERNAL_LP_CUSTOM_FIELD_NAME = "DirectFX_ExternalLP";

    //constants defining events which based on configuration uses snapshot based positions
    int TRADE_EVENT = 1;
    int RATE_QUALIFICATION_EVENT = 2;
    int SUBSCRIBE_EVENT = 3;
    int REVAL_CREDIT_EVENT = 4;

    /**
     * Maximum number of retries that are allowed in the case of an optimistic lock exception.
     */
    int MAXIMUM_RETRY = 5;

    /*
    * The amount format to be used for the credit limit amounts.
    */
    String AMOUNT_FORMAT_STRING = "###,###,###,###.########";

    /**
     * Credit utilization period for which credit utilization needs to be kept when outright trades
     * are also supported.
     */
    String OUTRIGHT_CREDIT_UTILIZATION_PERIOD = "2Y";

    /**
     * Credit utilization period for which credit utilization needs to be kept when only spot trades
     * are supported.
     */
    String SPOT_CREDIT_UTILIZATION_PERIOD = "1M";

    /**
     * Namespace description value for FI organizations.
     */
    String FI_NAMESPACE_DESCRIPTION = "FI";

    /**
     * This is the maximum limit for the notification/warning/suspension utilization percentages.
     */
    int UTILIZATION_NOTIFICATION_PERCENTAGE_LIMIT = 100;

    /**
     * This is the maximum limit for the number of days that can be considered for daily credit exposure. 10 years is default.
     */
    int DAILY_EXPOSURE_HORIZON_LIMIT = IdcDateConstants.DAYS_IN_LEAP_YEAR * 10;

    /**
     * The name of the timer which is used to trigger revaluation of credit utilization cache periodically.
     */
    String CREDIT_UTILIZATION_CACHE_REVALUATION_TIMER = "CreditUtilizationCacheRevaluationTimer";

    // default values for utilization percentages.
    double DEFAULT_NOTIFICATION_PERCENTAGE = 80;
    double DEFAULT_WARNING_PERCENTAGE = 90;
    double DEFAULT_SUSPENSION_PERCENTAGE = 98;

    int DEFAULT_EMAIL_POOLSIZE = 10;

    /**
     * credit update related parameters.
     */
    String EVENT_PROPERTY = "event";
    String TOPIC_PROPERTY = "topic";
    String OLD_VALUE = "OldValue";
    String NEW_VALUE = "NewValue";
    String VALUE_NONE = "NONE";
    String CREDIT_LIMIT_MODIFIED_EVENT_NAME = "Modified";
    String CREDIT_LIMIT_MODIFIED_TOPIC_NAME = "Credit";
    String EVENT_PROPERTY_ENTITY_SHORTNAME = "shortname";
    MessageEvent MSG_EVENT_MODIFIED = MessageFactory.newMessageEvent( CREDIT_LIMIT_MODIFIED_EVENT_NAME );

    // error codes for credit limit admin service
    String CREDIT_ADMIN_NOTIFICATION_PERCENTAGE_ERROR_CODE = "Credit.Admin.Notification.Percentage.Validation.Error";
    String CREDIT_ADMIN_WARNING_PERCENTAGE_ERROR_CODE = "Credit.Admin.Warning.Percentage.Validation.Error";
    String CREDIT_ADMIN_SUSPENSION_PERCENTAGE_ERROR_CODE = "Credit.Admin.Suspension.Percentage.Validation.Error";
    String CREDIT_ADMIN_STOPOUT_PERCENTAGE_ERROR_CODE = "Credit.Admin.StopOut.Percentage.Validation.Error";
    String CREDIT_ADMIN_NOTIFICATION_EMAIL_ERROR_CODE = "Credit.Admin.Notification.Email.Validation.Error";
    String CREDIT_ADMIN_LIMIT_CURRENCY_ERROR_CODE = "Credit.Admin.Limit.Currency.Validation.Error";
    String CREDIT_ADMIN_LIMIT_AMOUNT_ERROR_CODE = "Credit.Admin.Limit.Amount.Validation.Error";
    String CREDIT_ADMIN_NETTING_CALCULATOR_ERROR_CODE = "Credit.Admin.Netting.Calculator.Validation.Error";
    String CREDIT_ADMIN_MARGIN_TEMPLATE_ERROR_CODE = "Credit.Admin.Margin.Template.Validation.Error";
    String CREDIT_ADMIN_LEVERAGE_FACTOR_ERROR_CODE = "Credit.Admin.Leverage.Factor.Validation.Error";
    String CREDIT_ADMIN_DAILY_EXPOSURE_HORIZON_ERROR_CODE = "Credit.Admin.Daily.Exposure.Horizon.Error";
    String CREDIT_ADMIN_MAXIMUM_TENOR_ERROR_CODE = "Credit.Admin.Maximum.Tenor.Error";
    String CREDIT_ADMIN_MINIMUM_TENOR_ERROR_CODE = "Credit.Admin.Minimum.Tenor.Error";
    String CREDIT_ADMIN_MINIMUM_TENOR_EXCEED_MAXIMIMUM_TENOR_ERROR_CODE = "Credit.Admin.Minimum.Tenor.Exceed.Maximum.Tenor.Error";
    String CREDIT_ADMIN_EXTERNAL_LIMIT_PROVIDER_ERROR_CODE = "Credit.Admin.External.Limit.Provider.Validation.Error";
    String CREDIT_ADMIN_INVALID_PFE_ERROR_CODE = "Credit.Admin.Invalid.PFE.Error";
    String CREDIT_ADMIN_INVALID_TENOR_PROFILE_ERROR_CODE = "Credit.Admin.Invalid.TenorProfile.Error";
    String CREDIT_ADMIN_INVALID_CURRENCYPAIRGROUP_ERROR_CODE = "Credit.Admin.Invalid.CurrencyPairGroup.Error";
    String CREDIT_ADMIN_GROSS_POSITION_SPREAD_MARGIN_ERROR_CODE = "Credit.Admin.Gross.Position.Spread.Margin.Validation.Error";

    Pattern EMAIL_VALIDATION_PATTERN = Pattern.compile( "[a-zA-Z-_\\.0-9]+@[a-zA-Z-_\\.0-9]+\\.[a-z]+" );
    double MINIMUM = 0.00001;
    double CHECKSUM_TOLERANCE = 0.1;
    CreditConfigurationMBean configMBean = CreditConfigurationFactory.getCreditConfigurationMBean();
    CreditAdminConfigurationMBean adminConfigMBean = CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean();
    int MAIN_NAMESPACE_ID = 1;
    String MAIN_NAMESPACE = "MAIN";
    TimeZone GMT_TIMEZONE = TimeZone.getTimeZone( "GMT" );
    TradeConfigurationMBean tradeConfigMBean = TradeConfigurationFactory.getTradeConfigurationMBean();
    String CREDIT_LIMIT_PEER_VIRTUAL_SERVER = "PeerVirtualServer";

    // credit utilization currency position reset intiated by
    String RESET_CCYPOSITIONS_ENDOFDAY = "EndOfDayReset";
    String RESET_CACHE_SYNC_UPDATE_AMOUNT = "CacheSyncOnUpdateAmountReset";
    String RESET_CACHE_PROVIDER_LEVEL = "ProviderLevelCacheReset";
    String RESET_CACHE_CPTY_ORG_LEVEL = "CptyOrgLevelCacheReset";
    String RESET_CACHE_TP_LEVEL = "TPLevelCacheReset";
    String RESET_RECALCULATE_UTILIZATION = "UtilizationRecalculationReset";
    String RESET_CACHE_SYNC_MISC = "CacheSyncMiscReset";
    String RESET_SANITY_CHECK = "SanityCheckReset";
    String RESET_CACHE_SYNC_CHECKSUM = "CacheSyncCheckSumReset";
    String RESET_CACHE_ENTRY = "CacheEntryReset";
    String RESET_CACHE_ENTRY_AGGREGATE = "CacheEntryResetAggregate";
    String RESET_CACHE_UPDATE_STALE = "CacheEntryUpdateStale";
    String REBUILD_CCYPOSITIONS_FROM_SNAPSHOT_ENDOFDAY = "EndOfDayRebuildFromSnapshot";

    String REBUILD_CACHE_PROVIDER_LEVEL = "ProviderLevelCacheRebuild";
    String REBUILD_CACHE_CPTY_ORG_LEVEL = "CptyOrgLevelCacheRebuild";
    String REBUILD_CACHE_TP_LEVEL = "TPLevelCacheRebuild";

    String CREDIT_STATUS_SUSPEND = "Suspend";
    String CREDIT_STATUS_ACTIVE = "Active";
    String CREDIT_STATUS_NO_CHECK = "No Check";

    int CREDIT_NO_CHECK = 0;
    int CREDIT_ACTIVE = 1;
    int CREDIT_SUSPEND = 2;

    // permissions
    String CREDIT_METHODOLOGY_OVERRIDE_PERMISSION = "CreditMethodologyOverride";
    
    // External Credit Check Constants
    String EXTERNAL_CREDIT_PROVIDER_TRAIANA_PING = "TraianaPing";
    String EXTERNAL_CREDIT_PROVIDER_TRAIANA_PUSH = "TraianaPush";
    String EXTERNAL_CREDIT_PROVIDER_RBIV = "External";

    IdcDate MAX_VALUE_DATE = DateTimeFactory.newDate ( 1, 1, 2117 );

    // jms updates related properties and parameters.
    String CREDIT_RULE_TYPE_PROPERTY = "creditType";
    String CREDIT_HOLDER_ORGANIZATION_PROPERTY = "cpo";
    String CREDIT_HOLDER_LEGALENTITY_PROPERTY = "cpl";
    String CREDIT_REQUESTER_TRADINGPARTY_PROPERRTY = "cc";
    String CREDIT_REQUESTER_ORGANIZATION_PROPERTY = "cco";
    String CREDIT_LIMIT_AVAILABLE_AMOUNT_PROPERTY = "avlAmt";
    String CREDIT_LIMIT_CURRENCY_PROPERTY = "clc";
    String CREDIT_UTILIZATION_DATE_PROPERTY = "cuDate";
    String CREDIT_ENTITY_TRANSACTIONID_PROPERTY = "tid";
    String CREDIT_EVENT_PROPERTY = "event";
    String CREDIT_UTIL_EVENT_SNAPSHOT_PROPERTY = "es";
    String TRADE_DATE_PROPERTY = "td";
    String MODIFIED_EVENT = "Modified";
    String VIRTUAL_SERVER_PROPERTY = "vs";

    // credit types
    String CREDIT_TYPE_AGGREGATE = "Aggregate";
    String CREDIT_TYPE_DAILY = "Daily";
}
