package com.integral.finance.creditLimit.calculator;

// Copyright (c) 2001-2003 Integral Development Corp.  All rights reserved.

import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationFactory;
import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationMBean;
import com.integral.finance.account.AccountEnums;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditLimit;
import com.integral.finance.creditLimit.CreditLimitFactory;
import com.integral.finance.creditLimit.CreditLimitRule;
import com.integral.finance.creditLimit.CreditLimitRuleSet;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.CreditUtilizationEvent;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Request;
import com.integral.finance.fx.FXLeg;
import com.integral.finance.fx.FXPaymentParameters;
import com.integral.finance.fx.FXRate;
import com.integral.finance.marketData.MarketDataSet;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.trade.Trade;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.persistence.Entity;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.util.MathUtilC;

/**
 * FXLegCreditUtilizationEventBuilderC is used to build the credit utilization events based on the information from
 * the trade leg and amount type.
 *
 * <AUTHOR> Development Corp.
 */
public class FXLegNotionalCreditUtilizationEventBuilderC extends CreditUtilizationEventBuilderC
{
    private static DealingDataPurgeConfigurationMBean _configMBean = DealingDataPurgeConfigurationFactory.getDealingDataPurgeConfigurationMBean();

    @Override
    public CreditUtilizationEvent createCreditUtilizationEvent(Organization creditProviderOrg, TradingParty creditCpty, CreditLimitRule creditRule, char amountType, Entity aLeg, boolean calculateLimitCurrencyAmount, boolean isCreditProviderMaker, IdcDate currentTradeDate, double tenorCoefficient, boolean skipUpdateAccountReceivable) {
        return createCreditUtilizationEvent( creditProviderOrg,  creditCpty,  creditRule,  amountType,  aLeg,  calculateLimitCurrencyAmount,  isCreditProviderMaker,  currentTradeDate,  tenorCoefficient,  skipUpdateAccountReceivable, false );
    }

    public CreditUtilizationEvent createCreditUtilizationEvent(Organization creditProviderOrg, TradingParty creditCpty, CreditLimitRule creditRule, char amountType, Entity aLeg, boolean calculateLimitCurrencyAmount, boolean isCreditProviderMaker, IdcDate currentTradeDate, double tenorCoefficient, boolean skipUpdateAccountReceivable, boolean isAcceptance )
    {
        if ( !( aLeg instanceof FXLeg ) )
        {
            log.warn( "FCUB.createCreditUtilizationEvent : Wrong credit entity type.leg=" + aLeg );
            return null;
        }
        if ( amountType != CreditLimit.AMOUNT_USE )
        {
            log.warn( "FCUB.createCreditUtilizationEvent : Wrong credit amount type." );
            return null;
        }
        FXLeg fxLeg = ( FXLeg ) aLeg;
        FXMarketDataSet fxMds = ( FXMarketDataSet ) getMarketDataSet( creditProviderOrg );
        CreditUtilizationEvent registeredCue;
        if ( ConfigurationFactory.getServerMBean().isIntegralSpacesEnabled() && !isRFSRequest( fxLeg ) && !fxLeg.getTrade().isManualTrade() && fxLeg.getTrade().isSpaces() )
        {
            registeredCue = CreditLimitFactory.newSpaceCreditUtilizationEvent();
        }
        else
        {
            CreditUtilizationEvent cue = CreditLimitFactory.newCreditUtilizationEvent();
            registeredCue = ( CreditUtilizationEvent ) cue.getRegisteredObject();
        }

        registeredCue.setNamespace( creditRule.getParentRule().getRuleSet().getNamespace() );
        populateFXPosition( registeredCue, fxLeg, creditRule, fxMds, creditCpty, calculateLimitCurrencyAmount, isCreditProviderMaker, tenorCoefficient, CreditUtilC.isCashSettlement ( creditRule ), skipUpdateAccountReceivable, isAcceptance );
        if ( !checkTradeLegMaturity( fxLeg, currentTradeDate ) )
        {
            registeredCue.setErrorCode( CreditLimit.ERROR_INVALID_VALUE_DATE );
        }
        registeredCue.setEventType ( AccountEnums.AccountEventType.TRADE );
        return registeredCue;
    }

    /**
     * Populates the fxPosition with various trade related information.
     *
     * @param fxCreditUtilEvent            credit utilization event
     * @param fxLeg                        fx leg of the trade
     * @param creditRule                   credit limit rule
     * @param fxMds                        market data set
     * @param creditCpty                   credit counterparty
     * @param calculateLimitCurrencyAmount boolean representing whether to calculate limit amount
     * @param isCreditProviderMaker        boolean representing whether credit provider is maker
     * @param tenorCoefficient             tenor coefficient
     * @param skipUpdateAccountReceivable skip update account receivable
     */
    private void populateFXPosition( CreditUtilizationEvent fxCreditUtilEvent, FXLeg fxLeg, CreditLimitRule creditRule, FXMarketDataSet fxMds, TradingParty creditCpty, boolean calculateLimitCurrencyAmount, boolean isCreditProviderMaker, double tenorCoefficient, boolean isCashSettlement, boolean skipUpdateAccountReceivable, boolean isAcceptance)
    {
        Currency creditLimitCcy = creditRule.getCurrency();
        FXPaymentParameters fxPayment = fxLeg.getFXPayment();

        Trade registeredTrade = ( Trade ) fxLeg.getTrade().getRegisteredObject();
        fxCreditUtilEvent.setTransactionId( registeredTrade.getTransactionID() );
        fxCreditUtilEvent.setOrderId ( registeredTrade.getOrderId () );
        fxCreditUtilEvent.setTradingParty( creditCpty );
        fxCreditUtilEvent.setTradingPartyOrganization( creditCpty.getLegalEntityOrganization() );
        fxCreditUtilEvent.setInstrument( creditLimitCcy );
        final IdcDate valDate = fxPayment.getValueDate();
        fxCreditUtilEvent.setSettlementDate( valDate );
        fxCreditUtilEvent.setTradeValueDate ( valDate );
        if ( !isCashSettlement )
        {
            fxCreditUtilEvent.setTimeToLive ( _configMBean.getTTL ( fxCreditUtilEvent.getClass (), valDate ) );
        }

        fxCreditUtilEvent.setTradeDate( registeredTrade.getTradeDate() );

        TradingParty cptyA = ( TradingParty ) registeredTrade.getCounterpartyA();
        if ( cptyA == null )
        {
            fxCreditUtilEvent.setErrorCode( CreditLimit.ERROR_COUNTERPARTY_UNAVAILABLE );
            return;
        }
        TradingParty cptyB = ( TradingParty ) registeredTrade.getCounterpartyB();
        if ( cptyB == null )
        {
            fxCreditUtilEvent.setErrorCode( CreditLimit.ERROR_COUNTERPARTY_UNAVAILABLE );
            return;
        }

        fxCreditUtilEvent.setLegalEntity( isCreditProviderMaker ? CounterpartyUtilC.getLegalEntity( cptyB ) : CounterpartyUtilC.getLegalEntity( cptyA ) );

        // the trading perspective is from the credit holder organization.
        String cptyRef = isCreditProviderMaker ? Trade.COUNTERPARTYB : Trade.COUNTERPARTYA;
        Currency buyCcy = fxPayment.getBuyingCurrency( cptyRef );
        double buyAmt = fxPayment.getBuyingAmount( cptyRef );
        Currency sellCcy = fxPayment.getSellingCurrency( cptyRef );
        double sellAmt = fxPayment.getSellingAmount( cptyRef );

        char buyOrSell = CreditLimit.BUY;
        Currency principalCcy = buyCcy;
        double principalAmt = buyAmt;
        Currency priceCcy = sellCcy;
        double priceAmt = sellAmt;

        if ( buyAmt <= 0.0 || sellAmt <= 0.0 )
        {
            log.info( new StringBuilder( 400 ).append( "FCUB.populateFXPosition : buy or sell amount cannot be 0.0, buyAmt=" )
                    .append( buyAmt ).append( " ,sellAmt=" ).append( sellAmt ).toString() );
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 400 ).append( "FCUB.populateFXPosition : namespace=" )
                    .append( registeredTrade.getNamespace() != null ? registeredTrade.getNamespace().getShortName() : null )
                    .append( ",txId=" ).append( registeredTrade.getTransactionID() ).append( ",buyCcy=" ).append( buyCcy )
                    .append( ",sellCcy=" ).append( sellCcy ).append( ",limitCcy=" ).append( creditLimitCcy ).toString() );
        }

        if ( creditLimitCcy.isSameAs( sellCcy.getRealCurrency () ) )
        {
            buyOrSell = CreditLimit.SELL;
            principalCcy = sellCcy;
            principalAmt = sellAmt;
            priceCcy = buyCcy;
            priceAmt = buyAmt;
        }

        FXRate tradeFXRate = fxLeg.getFXPayment().getFXRate();
        fxCreditUtilEvent.setTradeRate( tradeFXRate.getRate() );
        fxCreditUtilEvent.setTradeSpotRate( tradeFXRate.getSpotRate() );
        fxCreditUtilEvent.setTradeFwdPoints( tradeFXRate.getForwardPoints() );
        fxCreditUtilEvent.setBaseCurrency( tradeFXRate.getBaseCurrency() );
        fxCreditUtilEvent.setVariableCurrency( tradeFXRate.getVariableCurrency() );
        fxCreditUtilEvent.setFxRateConvention( tradeFXRate.getFXRateConvention() );

        fxCreditUtilEvent.setTradeLegName( fxLeg.getName() );
        fxCreditUtilEvent.setPrincipal( principalAmt );
        fxCreditUtilEvent.setPrincipalCurrency( principalCcy );
        fxCreditUtilEvent.setPrice( priceAmt );
        fxCreditUtilEvent.setPriceCurrency( priceCcy );
        fxCreditUtilEvent.setBuySell( buyOrSell );

        fxCreditUtilEvent.setTradePrincipalAmount( principalAmt );
        fxCreditUtilEvent.setTradePriceAmount( priceAmt );
        fxCreditUtilEvent.setTenorCoefficient( tenorCoefficient );

        if ( tenorCoefficient == CreditLimit.TENOR_COEFFICIENT_NA )
        {
            fxCreditUtilEvent.setErrorCode( CreditLimit.ERROR_TENOR_COEFFICIENT_NOT_SET_FOR_TENOR );
        }
        else if ( tenorCoefficient != CreditLimit.DEFAULT_TENOR_COEFFICIENT )
        {
            fxCreditUtilEvent.setPrincipal( principalCcy.round( principalAmt * tenorCoefficient ) );
            fxCreditUtilEvent.setPrice( priceCcy.round( priceAmt * tenorCoefficient ) );
        }

        CurrencyPair currencyPair = CurrencyFactory.getCurrencyPair(fxCreditUtilEvent.getBaseCurrency(), fxCreditUtilEvent.getVariableCurrency());
        Double contractMultiplier = QuoteConventionUtilC.getInstance().getContractMultiplier(currencyPair);
        if( isAcceptance && contractMultiplier != null){
            if( priceCcy.isAnIndex()){
                fxCreditUtilEvent.setPrincipal( fxCreditUtilEvent.getPrincipal() * contractMultiplier);
            }else if( principalCcy.isAnIndex()){
                fxCreditUtilEvent.setPrice( fxCreditUtilEvent.getPrice() * contractMultiplier);
            }
        }

        if ( calculateLimitCurrencyAmount )
        {
            // set the used amount
            setLimitCurrencyAmount( fxCreditUtilEvent, creditLimitCcy, fxMds, fxCreditUtilEvent, contractMultiplier );
        }

        if(log.isDebugEnabled()) {
            log.debug(String.format("createCreditUtilizationEvent::populateFXPosition: ccypair:%s, contractMultiplier:%s, Principal:%s, Price:%s, usedAmt: %s",
                    currencyPair.getName(),
                    contractMultiplier,
                    fxCreditUtilEvent.getPrincipal(),
                    fxCreditUtilEvent.getPrice(),
                    fxCreditUtilEvent.getUsedAmount()));
        }



        if ( isCashSettlement && skipUpdateAccountReceivable )
        {
            if ( principalCcy.isSameAs ( sellCcy ) )
            {
                fxCreditUtilEvent.setPrincipal ( 0.0 );
                fxCreditUtilEvent.setTradePrincipalAmount ( 0.0 );
            }
            else
            {
                fxCreditUtilEvent.setPrice ( 0.0 );
                fxCreditUtilEvent.setTradePriceAmount ( 0.0 );
            }
        }
    }

    /**
     * Return the credit limit ruleset for the specified credit limit rule.
     *
     * @param clr credit limit rule
     * @return credit limit rule set
     */
    protected CreditLimitRuleSet getCreditLimitRuleSet( CreditLimitRule clr )
    {
        return ( CreditLimitRuleSet ) clr.getParentRule().getRuleSet();
    }

    /**
     * Return the credit limit currency amount for setting into the fx position. If rates are available from
     * the side rates, it is used. Otherwise rates are taken from the market data set.
     *  @param fxCreditUtilEvent cue
     * @param creditLimitCcy    credit limit currency
     * @param fxMds             fx market dataset associated with the credit limit ruleset.
     * @param cue               credit utilization event
     * @param contractMultiplier
     */
    private void setLimitCurrencyAmount(CreditUtilizationEvent fxCreditUtilEvent, Currency creditLimitCcy, FXMarketDataSet fxMds, CreditUtilizationEvent cue, Double contractMultiplier)
    {
        boolean checkPrcCcyRate = true;
        boolean checkPrinCcyRate = true;
        double limitCcyAmt = 0.0;
        // if credit limit currency same as principal or price currency, return the respective amounts.
        Currency prinCcy = fxCreditUtilEvent.getPrincipalCurrency();
        Currency priceCcy = fxCreditUtilEvent.getPriceCurrency();
        double prinAmt = fxCreditUtilEvent.getPrincipal();
        if ( prinCcy.getRealCurrency ().isSameAs( creditLimitCcy ) )
        {
            checkPrinCcyRate = false;
            if ( log.isDebugEnabled() )
            {
                log.debug( "FCUB.setLimitCurrencyAmount : CreditLimitCurrency is same as one of the currencies. ccy=" + prinCcy );
            }
            limitCcyAmt = prinAmt;
        }
        double prcAmt = fxCreditUtilEvent.getPrice();
        if ( priceCcy.getRealCurrency ().isSameAs( creditLimitCcy ) )
        {
            checkPrcCcyRate = false;
            if ( log.isDebugEnabled() )
            {
                log.debug( "FCUB.setLimitCurrencyAmount : CreditLimitCurrency is same as one of the currencies. ccy" + priceCcy );
            }
            limitCcyAmt = prcAmt;
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( new StringBuilder( 200 ).append( "FCUB.setLimitCurrencyAmount : Calculating limit ccy amount from market data set=" )
                    .append( fxMds ).append( " check with " ).append( prinCcy ).append( '/' ).append( creditLimitCcy ).append( " and with " )
                    .append( priceCcy ).append( '/' ).append( creditLimitCcy ).toString() );
        }

        if ( checkPrinCcyRate )
        {
            FXMarketDataElement fxMde = fxMds.findSpotConversionMarketDataElement( creditLimitCcy, prinCcy, true );
            if ( fxMde != null && fxMde.getFXPrice() != null && fxMde.getFXPrice().isValidPrice() )
            {
                FXRate fxRate = fxMde.getFXPrice().getBidFXRate() != null ? fxMde.getFXPrice().getBidFXRate() : ( fxMde.getFXPrice().getOfferFXRate() != null ? fxMde.getFXPrice().getOfferFXRate() : fxMde.getFXRate() );
                if ( limitCcyAmt == 0.0 )
                {
                    limitCcyAmt = fxRate.getAmount( contractMultiplier != null? MathUtilC.multiply(fxCreditUtilEvent.getPrincipal(), contractMultiplier):
                                                                                fxCreditUtilEvent.getPrincipal(),
                                                                                prinCcy );
                }
                if ( CreditLimit.creditCalcLog.isDebugEnabled() )
                {
                    CreditLimit.creditCalcLog.debug( new StringBuilder( 400 ).append( "FCUB.setLimitCurrencyAmount : FXRateSource=" )
                            .append( fxMds.getShortName() ).append( ",realtimeMds=" ).append( fxMds.isRealtime() ).append( ",mdeOwner=" )
                            .append( fxMde.getOwner() ).append( ",mdeType=" ).append( fxMde.getDescription() ).append( ",ccyPair=" )
                            .append( fxRate.getCurrencyPair().toString() ).append( ",spotRate=" ).append( fxRate.getSpotRate() )
                            .append( ",fwdPoints=" ).append( fxRate.getForwardPoints() ).append( ",rate=" ).append( fxRate.getRate() )
                            .append( ",mdeDate=" ).append( fxMde.getValueDate() ).append( ",mdeTenor=" ).append( fxMde.getTenor() ).append( ",txId=" )
                            .append( fxCreditUtilEvent.getTransactionId() ).toString() );

                    // check the mde owner is extended market data set.
                    if ( fxMde.getOwner() instanceof MarketDataSet && ( ( MarketDataSet ) fxMde.getOwner() ).getExtendedMarketDataSet() == null )
                    {
                        MarketDataSet clrsMds = fxMds.isRealtime() ? fxMds.getSessionInstance() : fxMds;
                        MarketDataSet extendedMds = clrsMds.getExtendedMarketDataSet();
                        if ( extendedMds != null )
                        {
                            extendedMds = extendedMds.isRealtime() ? extendedMds.getSessionInstance() : extendedMds;
                        }
                        CreditLimit.creditCalcLog.debug( new StringBuilder( 200 ).append( "FCUB.setLimitCurrencyAmount.INFO - fxMds=" )
                                .append( clrsMds ).append( ",clrsMds.size=" ).append( clrsMds.getMarketDataElements().size() ).append( ",extendedMds=" )
                                .append( extendedMds ).append( ",extendedMds.size=" ).append( extendedMds != null ? extendedMds.getMarketDataElements().size() : -1 ).toString() );
                    }
                }
            }
            else
            {
                log.warn( new StringBuilder( 200 ).append( "FCUB.setLimitCurrencyAmount : No spot FXRate found for ccy pair=" )
                        .append( CurrencyFactory.getCurrencyPairName( creditLimitCcy, prinCcy ) ).append( ",mds=" ).append( fxMds.getShortName() ).append( ",txId=" )
                        .append( fxCreditUtilEvent.getTransactionId() ).toString() );
                cue.setErrorCode( CreditLimit.ERROR_CONVERSION_RATE_UNAVAILABLE );
                cue.setUsedAmount( limitCcyAmt );
                return;
            }
        }

        if ( checkPrcCcyRate )
        {
            FXMarketDataElement fxMde = fxMds.findSpotConversionMarketDataElement( creditLimitCcy, priceCcy, true );
            if ( fxMde != null && fxMde.getFXPrice() != null && fxMde.getFXPrice().isValidPrice() )
            {
                FXRate fxRate = fxMde.getFXPrice().getBidFXRate() != null ? fxMde.getFXPrice().getBidFXRate() : ( fxMde.getFXPrice().getOfferFXRate() != null ? fxMde.getFXPrice().getOfferFXRate() : fxMde.getFXRate() );
                if ( limitCcyAmt == 0.0 )
                {
                    limitCcyAmt = fxRate.getAmount(  contractMultiplier != null?  MathUtilC.multiply(fxCreditUtilEvent.getPrincipal(),contractMultiplier):
                                                                                  fxCreditUtilEvent.getPrincipal(),
                                                                                  prinCcy );
                }
                if ( CreditLimit.creditCalcLog.isDebugEnabled() )
                {
                    CreditLimit.creditCalcLog.debug( new StringBuilder( 400 ).append( "FCUB.setLimitCurrencyAmount : FXRateSource=" )
                            .append( fxMds.getShortName() ).append( ",realtimeMds=" ).append( fxMds.isRealtime() ).append( ",mdeOwner=" )
                            .append( fxMde.getOwner() ).append( ",mdeType=" ).append( fxMde.getDescription() ).append( ",ccyPair=" )
                            .append( fxRate.getCurrencyPair().toString() ).append( ",spotRate=" ).append( fxRate.getSpotRate() )
                            .append( ",fwdPoints=" ).append( fxRate.getForwardPoints() ).append( ",rate=" ).append( fxRate.getRate() )
                            .append( ",mdeDate=" ).append( fxMde.getValueDate() ).append( ",mdeTenor=" ).append( fxMde.getTenor() ).append( ",txId=" )
                            .append( fxCreditUtilEvent.getTransactionId() ).toString() );

                    // check the mde owner is extended market data set.
                    if ( fxMde.getOwner() instanceof MarketDataSet && ( ( MarketDataSet ) fxMde.getOwner() ).getExtendedMarketDataSet() == null )
                    {
                        MarketDataSet clrsMds = fxMds.isRealtime() ? fxMds.getSessionInstance() : fxMds;
                        MarketDataSet extendedMds = clrsMds.getExtendedMarketDataSet();
                        if ( extendedMds != null )
                        {
                            extendedMds = extendedMds.isRealtime() ? extendedMds.getSessionInstance() : extendedMds;
                        }
                        CreditLimit.creditCalcLog.debug( new StringBuilder( 200 ).append( "FCUB.setLimitCurrencyAmount  : fxMds=" )
                                .append( clrsMds ).append( ",clrsMds.size=" ).append( clrsMds.getMarketDataElements().size() ).append( ",extendedMds=" )
                                .append( extendedMds ).append( ",extendedMds.size=" ).append( extendedMds != null ? extendedMds.getMarketDataElements().size() : -1 ).toString() );
                    }
                }
            }
            else
            {
                log.warn( new StringBuilder( 200 ).append( "FCUB.setLimitCurrencyAmount : No spot FXRate found for ccy pair=" )
                        .append( CurrencyFactory.getCurrencyPairName( creditLimitCcy, priceCcy ) ).append( ",mds=" ).append( fxMds.getShortName() ).append( ",txId=" ).append( fxCreditUtilEvent.getTransactionId() ).toString() );
                cue.setErrorCode( CreditLimit.ERROR_CONVERSION_RATE_UNAVAILABLE );
                cue.setUsedAmount( limitCcyAmt );
                return;
            }
        }
        cue.setUsedAmount( limitCcyAmt );
    }

    /**
     * Checks whether the trade leg is matured by comparing the trade leg's value date
     * with the current business date in the market dataset associated with the credit limit ruleset.
     *
     * @param fxLeg            fx leg
     * @param currentTradeDate current trade date
     * @return true if trade leg maturity date is earlier than the current business date
     */
    private boolean checkTradeLegMaturity( FXLeg fxLeg, IdcDate currentTradeDate )
    {
        if ( fxLeg.getFXPayment().getValueDate().isEarlierThan( currentTradeDate ) )
        {
            log.warn( new StringBuilder( 200 ).append( "FCUB.checkTradeLegMaturity : Invalid Value Date=" )
                    .append( fxLeg.getFXPayment().getValueDate() ).append( ",businessDate=" ).append( currentTradeDate ).append( ",txId=" )
                    .append( fxLeg.getTrade().getTransactionID() ).toString() );
            return false;
        }
        return true;
    }

    private boolean isRFSRequest( FXLeg fxLeg )
    {
        Trade trade = fxLeg.getTrade();
        Request request = trade.getRequest();
        if ( request != null ) // Will have to check manual trades.
        {
            String reqClsf = request.getRequestClassification().getShortName();
            if ( reqClsf.equalsIgnoreCase( ISCommonConstants.RFQ_CREATE_TYPE ) )
            {
                return true;
            }
        }
        return false;
    }
}
