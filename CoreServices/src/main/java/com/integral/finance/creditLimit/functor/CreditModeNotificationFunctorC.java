package com.integral.finance.creditLimit.functor;

import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditLimit;
import com.integral.finance.creditLimit.CreditLimitConstants;
import com.integral.finance.creditLimit.CreditMessageEvent;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;

import java.util.HashMap;

public class CreditModeNotificationFunctorC extends CreditRemoteNotificationFunctorC
{
    private static final Log log = LogFactory.getLog( CreditModeNotificationFunctorC.class );

    public void onCommit( HashMap props )
    {
        // do not process the message if remote notification processing is not enabled.
        if ( !creditAdminConfig.isCreditRemoteNotificationProcessingEnabled() )
        {
            return;
        }

        long t0 = System.currentTimeMillis();
        Organization cpo;
        Organization cco = null;
        TradingParty cc = null;
        try
        {
            String event = ( String ) props.get( CreditLimitConstants.EVENT_PROPERTY );
            if ( CreditMessageEvent.UPDATELIMIT.getName().equals( event ) || CreditMessageEvent.UPDATELIMITDAILY.getName().equals( event ) )
            {
                String creditProviderOrgGuid = ( String ) props.get( CreditLimit.CREDIT_PROVIDER_ORGANIZATION );
                if ( creditProviderOrgGuid != null )
                {
                    cpo = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditProviderOrgGuid, Organization.class );

                    String creditCptyOrgGuid = ( String ) props.get( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION );
                    if ( creditCptyOrgGuid != null )
                    {
                        cco = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditCptyOrgGuid, OrganizationC.class );
                    }

                    String creditCptyGuid = ( String ) props.get( CreditLimit.CREDIT_COUNTERPARTY );
                    if ( creditCptyGuid != null )
                    {
                        cc = ( TradingParty ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditCptyGuid, TradingParty.class );
                    }

                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "CMNF.onCommit : updating cache for cpo=" + cpo + ",cco=" + cco + ",cc=" + cc );
                    }

                    if ( cpo != null )
                    {
                        if ( cc != null )
                        {
                            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().updateTradingPartyCreditMode( cpo, cc );
                        }
                        else if ( cco != null )
                        {
                            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().updateCounterpartyOrganizationCreditMode( cpo, cco );
                        }
                        else
                        {
                            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().updateProviderOrganizationCreditMode( cpo );
                        }
                    }
                    else
                    {
                        log.warn( "CMNF.onCommit : credit provider org is not found for guid=" + creditProviderOrgGuid );
                    }
                }

                log.info( new StringBuilder( 200 ).append( "CTPNF.onCommit : updated the cache for credit mode. props=" )
                        .append( props ).append( ",timeTaken=" ).append( System.currentTimeMillis() - t0 ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CMNF.onCommit : Exception while handling the admin notification related to credit mode. props=" + props, e );
        }
    }
}