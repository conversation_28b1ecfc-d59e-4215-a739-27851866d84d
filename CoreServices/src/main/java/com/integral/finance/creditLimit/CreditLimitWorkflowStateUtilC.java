package com.integral.finance.creditLimit;

// Copyright (c) 2011 Integral Development Corp. All rights reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Entity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.session.IdcTransaction;
import com.integral.user.Organization;
import com.integral.workflow.State;
import com.integral.workflow.StateC;
import com.integral.workflow.WorkflowState;
import com.integral.workflow.WorkflowStateMap;

import java.util.ArrayList;
import java.util.Collection;

/**
 * CreditLimitWorkflowStateUtilC is used to set the state in the credit limit workflow state.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitWorkflowStateUtilC
{
    public static final State CREDIT_FAILED = getStateObject( CreditLimitWorkflowStateFacade.CREDIT_FAILED_STATE );
    public static final State CREDIT_RESERVED = getStateObject( CreditLimitWorkflowStateFacade.CREDIT_RESERVED_STATE );
    public static final State CREDIT_USED = getStateObject( CreditLimitWorkflowStateFacade.CREDIT_USED_STATE );
    public static final State CREDIT_UNDONE = getStateObject( CreditLimitWorkflowStateFacade.CREDIT_UNDONE_STATE );

    public static final String CREDIT_WORKFLOW_STATE_NAME_PREFIX = "Credit";

    private static Log log = LogFactory.getLog( CreditLimitWorkflowStateUtilC.class );

    public static boolean isCreditReserved( CreditLimitWorkflowState clws )
    {
        return CREDIT_RESERVED.isSameAs( clws.getState() );
    }

    public static void setCreditReserved( CreditLimitWorkflowState clws )
    {
        clws.transitionState( CREDIT_RESERVED, null );
    }

    public static boolean isCreditUsed( CreditLimitWorkflowState clws )
    {
        return CREDIT_USED.isSameAs( clws.getState() );
    }

    public static void setCreditUsed( CreditLimitWorkflowState clws )
    {
        clws.transitionState( CREDIT_USED, null );
    }

    public static boolean isCreditUndone( CreditLimitWorkflowState clws )
    {
        return CREDIT_UNDONE.isSameAs( clws.getState() );
    }

    public static boolean isCreditUsed( Entity creditEntity )
    {
        Collection<CreditLimitWorkflowState> creditLimitWorkflowStates = getCreditLimitWorkflowStates ( creditEntity );
        if ( creditLimitWorkflowStates != null && !creditLimitWorkflowStates.isEmpty () )
        {
            for ( CreditLimitWorkflowState clws: creditLimitWorkflowStates )
            {
                if ( CREDIT_USED.isSameAs( clws.getState() ) )
                {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean isCreditUndone( Entity creditEntity )
    {
        Collection<CreditLimitWorkflowState> creditLimitWorkflowStates = getCreditLimitWorkflowStates ( creditEntity );
        if ( creditLimitWorkflowStates != null && !creditLimitWorkflowStates.isEmpty () )
        {
            for ( CreditLimitWorkflowState clws: creditLimitWorkflowStates )
            {
                if ( CREDIT_UNDONE.isSameAs( clws.getState() ) )
                {
                    return true;
                }
            }
        }
        return false;
    }

    public static void setCreditUndone( CreditLimitWorkflowState clws )
    {
        clws.transitionState( CREDIT_UNDONE, null );
    }

    public static boolean isCreditCheckFailed( CreditLimitWorkflowState clws )
    {
        return CREDIT_FAILED.isSameAs( clws.getState() );
    }

    public static void setCreditCheckFailed( CreditLimitWorkflowState clws )
    {
        clws.transitionState ( CREDIT_FAILED, null );
    }

    public static CreditLimitWorkflowState getCreditLimitWorkflowState( Entity creditEntity, LegalEntity le, TradingParty tp, boolean addIfNull, IdcTransaction tx, Boolean isCreditProviderMaker )
    {
        CreditLimitWorkflowState clws = getCreditLimitWorkflowState( creditEntity, tp );
        if ( clws == null && addIfNull )
        {
            clws = createCreditLimitWorkflowState( le, tp, null );
        }
        if ( clws != null )
        {
            clws = ( CreditLimitWorkflowState ) registerObject( clws, tx );
            WorkflowStateMap wsm = creditEntity.getWorkflowStateMap();
            if ( wsm != null )
            {
                wsm = ( WorkflowStateMap ) registerObject( wsm, tx );
                creditEntity.setWorkflowStateMap( wsm );
                wsm.setWorkflowState( clws.getName(), clws );
                if ( wsm.getNamespace() == null )
                {
                    clws.setNamespace( creditEntity.getNamespace() );
                }
            }
            else
            {
                clws.setNamespace( creditEntity.getNamespace() );
                log.error( "CWSU.getCreditLimitWorkflowState.ERROR - No workflow state map for the credit entity of type : "
                        + creditEntity.getClass().getName() + " and ObjectId : " + creditEntity.getObjectID() );
            }

            clws.setMaker ( isCreditProviderMaker );
        }
        return clws;
    }

    public static CreditLimitWorkflowState createCreditLimitWorkflowState( LegalEntity le, TradingParty tp, State state )
    {
        if ( tp == null )
        {
            log.warn( "CWSU.createCreditLimitWorkflowState- credit requester trading party is not specified." );
            return null;

        }
        CreditLimitWorkflowState clws = CreditLimitFactory.newCreditLimitWorkflowState();

        //Credit workflow state is a named workflow state, hence name should be unique.
        clws.setName( new StringBuilder( 50 ).append( CREDIT_WORKFLOW_STATE_NAME_PREFIX ).append( tp.getGUID() ).toString() );
        clws.setLegalEntity( le );
        clws.setTradingParty( tp );
        clws.setState( state );
        return clws;
    }

    public static Collection<CreditLimitWorkflowState> getCreditLimitWorkflowStates( Entity creditEntity )
    {
        Collection<CreditLimitWorkflowState> creditWorkflowStates = null;
        if ( creditEntity != null && creditEntity.getWorkflowStateMap() != null )
        {
            Collection<WorkflowState> workflowStates = creditEntity.getWorkflowStateMap().getWorkflowStates();
            if ( workflowStates != null )
            {
                for ( WorkflowState ws : workflowStates )
                {
                    if ( ws instanceof CreditLimitWorkflowState )
                    {
                        if ( creditWorkflowStates == null )
                        {
                            creditWorkflowStates = new ArrayList<CreditLimitWorkflowState>( 2 );
                        }
                        creditWorkflowStates.add( ( CreditLimitWorkflowState ) ws );
                    }
                }
            }
        }
        return creditWorkflowStates;
    }

    public static CreditLimitWorkflowState getCreditLimitWorkflowState( Entity creditEntity, TradingParty tp )
    {
        Collection<CreditLimitWorkflowState> creditWorkflowStates = getCreditLimitWorkflowStates( creditEntity );
        if ( creditWorkflowStates != null )
        {
            for ( CreditLimitWorkflowState clws : creditWorkflowStates )
            {
                if ( tp.getObjectId() == clws.getTradingParty().getObjectId() )
                {
                    return clws;
                }
            }
        }
        return null;
    }

    public static CreditLimitWorkflowState getCreditLimitWorkflowState( Entity creditEntity, Organization cpo, Organization cco )
    {
        Collection<CreditLimitWorkflowState> creditWorkflowStates = getCreditLimitWorkflowStates( creditEntity );
        if ( creditWorkflowStates != null )
        {
            for ( CreditLimitWorkflowState clws : creditWorkflowStates )
            {
                final TradingParty clwsTP = clws.getTradingParty ();
                if ( clwsTP != null && cpo.isSameAs ( clwsTP.getOrganization () ) && cco.isSameAs ( clwsTP.getLegalEntityOrganization () ) )
                {
                    return clws;
                }
            }
        }
        return null;
    }

    /**
     * This returns the state object based on the shortname. Used to set the credit limit workfflow with different states.
     *
     * @param shortName of the state object.
     * @return state representing credit workflow state.
     */
    private static State getStateObject( String shortName )
    {
        return ( State ) ReferenceDataCacheC.getInstance().getEntityByShortName( shortName, StateC.class, null, null );
    }

    /**
     * Registers the object in the transaction specified
     *
     * @param entity entity to be registered
     * @param tx     transaction to register entity
     * @return registered entity
     */
    private static Entity registerObject( Entity entity, IdcTransaction tx )
    {
        if ( tx == null || entity == null )
        {
            return entity;
        }
        entity = tx.getRegisteredObject( entity );
        return entity;
    }

}
