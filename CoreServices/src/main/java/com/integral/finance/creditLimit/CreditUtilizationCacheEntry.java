package com.integral.finance.creditLimit;

import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.time.IdcDate;
import com.integral.user.Organization;

import java.util.Collection;

// Copyright (c) 2001-2006 Integral Development Corp. All rights reserved.

/**
 * This interface is used for the credit utilization cache content which holds credit utilizations for a credit counterparty
 * and settlement date combination. The cache holds the collection of credit utilizations and underlying counterparty
 * credit limit rule.
 *
 * <AUTHOR> Development Corp.
 */
public interface CreditUtilizationCacheEntry
{
    /**
     * Returns the date for the credit utilizations.
     *
     * @return date
     */
    IdcDate getDate();

    /**
     * Returns the collection of credit utilizations
     *
     * @return collection
     */
    Collection<CreditUtilization> getCreditUtilizations();

    /**
     * replaces the credit utilizations of this entry with the passed collection parameter
     * @param aCreditUtilizations
     */
    void setCreditUtilizations(Collection<CreditUtilization> aCreditUtilizations);

    /**
     * Returns counterparty credit limit rule to which credit utilizations belong.
     *
     * @return counterparty credit limit rule
     */
    CounterpartyCreditLimitRule getCounterpartyCreditLimitRule();

    /**
     * Validates the credit utilizations by checking the supported credit limit rules.
     *
     * @return result
     */
    boolean validateCreditUtilizations();

    /**
     * Returns the created time of this credit utilization cache entry.
     *
     * @return created time
     */
    long getCreatedTime();

    /**
     * Returns the creditprovider organization
     *
     * @return organization
     */
    Organization getCreditProviderOrganization();

    /**
     * Returns whether it is an aggregate credit utilizations type cache
     *
     * @return aggregate type
     */
    boolean isAggregateCreditUtilizationCollectionCacheEntry();

    /**
     * Returns whether the cache entry is stale.
     *
     * @return latest
     */
    boolean isStale();

    /**
     * Sets the entry as stale.
     *
     * @param stale stale
     */
    void setStale( boolean stale );

    /**
     * Returns the updated time of this credit utilization cache entry.
     *
     * @return modified time
     */
    long getModifiedTime();

    /**
     * Mark the entry as updated with the event.
     *
     * @param event event
     */
    void update( String event );

    /**
     * Returns the last event updated the entry.
     *
     * @return event
     */
    String getLastEvent();

    /**
     * Sets the last event updated the entry.
     *
     * @param event event
     */
    void setLastEvent( String event );

    /**
     * Returns the exempted currency pair group
     *
     * @return cpg
     */
    CurrencyPairGroup getExemptedCurrencyPairGroup();

    /**
     * Sets the exempted currency pair group
     *
     * @param cpg currency pair group
     */
    void setExemptedCurrencyPairGroup( CurrencyPairGroup cpg );

    /**
     * Returns whether specified currency pair is exempted in the list.
     *
     * @param ccyPair currency pair
     * @return exempted
     */
    boolean isCurrencyPairExempted( CurrencyPair ccyPair );

    /**
     * Returns the tenor coefficient for the settlement date
     *
     * @param clr credit limit rule
     * @param ccyPair currency pair
     * @return tenor coefficient
     */
    double getTenorCoefficient( CreditLimitRule clr, CurrencyPair ccyPair );

    /**
     * Initializes the tenor coefficients for the currency pair.
     *
     * @param ccyPairs currency pair list
     */
    void initTenorCoefficient( Collection<CurrencyPair> ccyPairs );

    /**
     * Initializes the tenor coefficients for all the currency pairs subscribed.
     */
    void initTenorCoefficientsForAllCurrencyPairs();
}
