package com.integral.finance.creditLimit.calculator;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.exception.IdcException;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.handler.CreditUtilizationRecalculationHandlerC;
import com.integral.finance.creditLimit.handler.ReplaceCreditLimitRuleCommitHandlerC;
import com.integral.time.IdcDate;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.Map;

/**
 * This class is used to create daily credit limit rule and add it to the counterparty credit limit rule. It also
 * provides methods to convert an existing credit limit rule into daily credit limit rule.
 *
 * <AUTHOR> Development Corp.
 */
public class DailyCreditLimitRuleCreationCalculatorC extends CreditLimitRuleCreationCalculatorC
{
    public CreditLimitRule createCreditLimitRule( Organization creditProviderOrg, CounterpartyCreditLimitRule cclr, CreditLimitClassification clsf, CreditUtilizationCalculator calc, CreditLimitRule existingClr )
    {
        try
        {
            log.warn( new StringBuilder( 200 ).append( "DailyCreditLimitRuleCreationCalculatorC.createCreditLimitRule.INFO : creditProviderOrg=" )
                    .append( ",calc=" ).append( calc ).append( ",clsf=" ).append( clsf ).append( ",existingClr=" ).append( existingClr ).toString() );

            if ( existingClr == null )
            {
                return createDailyCreditLimitRule( creditProviderOrg, cclr, calc );
            }
            else if ( existingClr instanceof SingleCreditLimitRule )
            {
                CreditLimitRule registeredExistingClr = ( CreditLimitRule ) existingClr.getRegisteredObject();
                registeredExistingClr.setActive( false );

                DailyCreditLimitRule newClr = createDailyCreditLimitRule( creditProviderOrg, cclr, calc );
                convertToDailyCreditLimitRule( creditProviderOrg, registeredExistingClr, newClr );
                return newClr;
            }
            else
            {
                CreditLimitRule registeredDclr = ( CreditLimitRule ) existingClr.getRegisteredObject();
                // existing credit limit rule needs to be set with the calculator.
                registeredDclr.setCreditUtilizationCalculator( calc );
                registeredDclr.setAllowNetting( CreditUtilC.isNettingCalculator( calc ) );
                CreditUtilC.getTransaction().addCommitHandler( new CreditUtilizationRecalculationHandlerC( existingClr ) );
                return registeredDclr;
            }
        }
        catch ( Exception e )
        {
            log.error( "DailyCreditLimitRuleCreationCalculatorC.createCreditLimitRule.ERROR : Error creating daily credit limit rule. creditProviderOrg=" + creditProviderOrg + ",cclr=" + cclr, e );
        }
        return null;
    }

    /**
     * Creates daily credit limit rule for the credit provider organization and add it to the counterparty credit
     * limit rule.
     *
     * @param creditProviderOrg
     * @param cclr
     * @param calc
     * @return daily credit limit rule
     */
    private DailyCreditLimitRule createDailyCreditLimitRule( Organization creditProviderOrg, CounterpartyCreditLimitRule cclr, CreditUtilizationCalculator calc )
    {
        DailyCreditLimitRule registeredClr = ( DailyCreditLimitRule ) CreditLimitFactory.newDailyCreditLimitRule().getRegisteredObject();
        registeredClr.setNamespace( creditProviderOrg.getNamespace() );
        registeredClr.setShortName( CreditLimitConstants.DAILY_SETTLEMENT_RULE_SHORT_NAME );
        registeredClr.setClassification( ( CreditLimitClassification ) CreditLimitConstants.DAILY_SETTLEMENT_CLASSIFICATION.getRegisteredObject() );
        registeredClr.setCreditUtilizationCalculator( calc );
        registeredClr.setAllowNetting( CreditUtilC.isNettingCalculator( calc ) );
        registeredClr.setCurrency( CreditUtilC.getCounterpartyCreditLimitCurrency( creditProviderOrg, cclr.getTradingPartyOrganization(), cclr.getTradingParty() ) );
        registeredClr.setParentRule( cclr );
        cclr.addChildRule( registeredClr );
        return registeredClr;
    }

    /**
     * Converts the existing credit limit rule to the daily credit limit rule. Credit utilization events if attached to
     * the credit utilization, are added to the respective credit utilizations.
     *
     * @param creditProviderOrg
     * @param existingClr
     * @param newDclr
     */
    private void convertToDailyCreditLimitRule( Organization creditProviderOrg, CreditLimitRule existingClr, DailyCreditLimitRule newDclr ) throws IdcException
    {
        newDclr.setLimitAmount( existingClr.getLimitAmount() );
        newDclr.setCurrency( existingClr.getCurrency() );
        CreditUtilization existingCu = CreditUtilizationManagerC.getInstance().getCreditUtilizationForDate( existingClr, null );
        if ( existingCu == null )
        {
            log.warn( "DailyCreditLimitRuleCreationCalculatorC.convertToDailyCreditLimitRule.INFO : Null credit utilization for a single credit limit rule. clr=" + existingClr );
            return;
        }

        IdcDate businessDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
        Map<IdcDate, CreditUtilization> utilsMap = newDclr.getCreditUtilizationMap();
        Collection<CreditUtilizationEvent> cues = CreditUtilC.getCreditUtilizationEvents( existingClr );
        log.warn( "DailyCreditLimitRuleCreationCalculatorC.convertToDailyCreditLimitRule.INFO : events size=" + cues.size() + " for cu=" + existingCu );
        for ( CreditUtilizationEvent cue : cues )
        {
            CreditUtilizationEvent registeredCue = ( CreditUtilizationEvent ) cue.getRegisteredObject();
            if ( !CreditUtilC.isValidCreditUtilizationEvent( registeredCue, businessDate ) )
            {
                CreditUtilC.getTransaction().getUOW().deleteObject( registeredCue );
                continue;
            }
            IdcDate posDate = registeredCue.getSettlementDate();
            if ( posDate != null )
            {
                DailyCreditUtilization dateCu = ( DailyCreditUtilization ) utilsMap.get( posDate );
                if ( dateCu == null )
                {
                    dateCu = ( DailyCreditUtilization ) CreditLimitFactory.newDailyCreditUtilization().getRegisteredObject();
                    dateCu.setNamespace( creditProviderOrg.getNamespace() );
                    dateCu.setCreditLimitRule( newDclr );
                    utilsMap.put( posDate, dateCu );
                }
                registeredCue.setCreditUtilization( dateCu );
            }
        }
        newDclr.setCreditUtilizationMap( utilsMap );

        CreditUtilC.getTransaction().addCommitHandler( new ReplaceCreditLimitRuleCommitHandlerC( creditProviderOrg, existingClr, newDclr ) );
    }
}

