package com.integral.finance.creditLimit.calculator;

// Copyright (c) 2001-2005 Integral Development Corp. All rights reserved.

import com.integral.finance.calculator.CalculatorC;
import com.integral.finance.creditLimit.CreditLimitOrgFunction;
import com.integral.finance.creditLimit.CreditLimitRuleSet;
import com.integral.finance.marketData.MarketDataSet;
import com.integral.user.Organization;

/**
 * CreditUtilizationEventBuilderC is an abstract super class for credit utilization event builders.
 * Subclasses should implement the method createCreditUtilizationEvents() to actually create
 * the events.
 *
 * <AUTHOR> Development Corp.
 */
public abstract class CreditUtilizationEventBuilderC
        extends CalculatorC
        implements CreditUtilizationEventBuilder
{
    /**
     * Returns the market data set for the organization. If market data set is specified at the org function level, then it
     * is returned. Otherwise, market data set associated with the credit limit rule set is returned.
     *
     * @param org org
     * @return market data set
     */
    protected MarketDataSet getMarketDataSet( Organization org )
    {
        CreditLimitOrgFunction orgFunction = getCreditLimitOrgFunction( org );
        if ( orgFunction != null && orgFunction.getMarketDataSet() != null )
        {
            return orgFunction.getMarketDataSet();
        }
        CreditLimitRuleSet clrs = getCreditLimitRuleSet( org );
        return clrs != null ? clrs.getMarketDataSet() : null;
    }

    /**
     * Returns the credit limit org function for the credit provider org.
     *
     * @param creditProviderOrg credit provider org
     * @return credit limit org function
     */
    protected CreditLimitOrgFunction getCreditLimitOrgFunction( Organization creditProviderOrg )
    {
        try
        {
            return creditProviderOrg.getCreditLimitOrgFunction();
        }
        catch ( Exception e )
        {
            log.error( "CreditUtilC.getCreditLimitOrgFunction.ERROR. org=" + creditProviderOrg, e );
        }
        return null;
    }

    /**
     * Returns the credit limit ruleset for the organization.
     *
     * @param creditProviderOrg credit provider org
     * @return credit limit rule set
     */
    protected CreditLimitRuleSet getCreditLimitRuleSet( Organization creditProviderOrg )
    {
        CreditLimitOrgFunction creditOrgFunc = creditProviderOrg.getCreditLimitOrgFunction();
        if ( creditOrgFunc != null )
        {
            return creditOrgFunc.getCreditLimitRuleSet();
        }
        else
        {
            log.error( "CreditUtilC.getCreditLimitRuleSet.ERROR : No credit limit org function for org=" + creditProviderOrg.getShortName() );
        }
        return ( CreditLimitRuleSet ) creditProviderOrg.getCustomFieldValue( "DirectFX_CreditLimitRuleSet" ); //todo provided for backward compatibility. to be removed later.
    }
}
