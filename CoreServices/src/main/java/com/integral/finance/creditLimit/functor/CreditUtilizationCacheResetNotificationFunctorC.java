package com.integral.finance.creditLimit.functor;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditLimit;
import com.integral.finance.creditLimit.CreditLimitConstants;
import com.integral.finance.creditLimit.CreditMessageEvent;
import com.integral.finance.creditLimit.CreditUtilizationCacheFetchFromSubscriptionsC;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.persistence.util.LogUtil;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;

/**
 * This class is used an end of day service handler which will be executed at the end of the business day.
 * This is used to reset all the credit utilizations which are present in the credit utilization cache.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditUtilizationCacheResetNotificationFunctorC extends CreditRemoteNotificationFunctorC
{
    public void onCommit( HashMap props )
    {
        // do not process the message if remote notification processing is not enabled.
        if ( !creditAdminConfig.isCreditRemoteNotificationProcessingEnabled() )
        {
            return;
        }

        long t0 = System.currentTimeMillis();
        Organization creditProviderOrg = null;
        Organization creditCptyOrg = null;
        TradingParty creditCpty = null;

        log.info( "CURF.onCommit : Begin updating credit utilizations cache. props=" + props );

        try
        {
            LogUtil.setDisableDBQueryLogSwitch( true );
            String eventName = ( String ) props.get( CreditLimitConstants.EVENT_PROPERTY );
            String creditProviderOrgGuid = ( String ) props.get( CreditLimit.CREDIT_PROVIDER_ORGANIZATION );
            if ( creditProviderOrgGuid != null )
            {
                creditProviderOrg = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditProviderOrgGuid, Organization.class );

                String creditCptyOrgGuid = ( String ) props.get( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION );
                if ( creditCptyOrgGuid != null )
                {
                    creditCptyOrg = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditCptyOrgGuid, Organization.class );
                }

                String creditCptyGuid = ( String ) props.get( CreditLimit.CREDIT_COUNTERPARTY );
                if ( creditCptyGuid != null )
                {
                    creditCpty = ( TradingParty ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditCptyGuid, TradingParty.class );
                }

                String creditCptyGuids = ( String ) props.get( CreditLimit.CREDIT_CPTY_ORGS );
                Collection<Organization> cptyOrgs = null;

                if ( log.isDebugEnabled() )
                {
                    log.debug( "CURF.onCommit : Reset cache for creditProviderOrg=" + creditProviderOrg + ",creditCptyOrg=" + creditCptyOrg + ",creditCpty=" + creditCpty );
                }
                if ( creditProviderOrg != null )
                {
                    // if it is re-initialize, then remove the cache entries.
                    if ( CreditMessageEvent.REINITIALIZECREDITPROVIDER.getName().equals( eventName ) || CreditMessageEvent.REINITIALIZECREDITCOUNTERPARTYORG.getName().equals( eventName ) )
                    {
                        if ( creditCptyOrg != null )
                        {
                            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeCounterpartyOrganizationCreditUtilizations(creditProviderOrg, creditCptyOrg);
                        }
                        else
                        {
                            CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeProviderOrganizationCreditUtilizations(creditProviderOrg);
                        }
                    }

                    if ( creditCpty != null )
                    {
                        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().rebuildTradingPartyCreditUtilizations(creditProviderOrg, creditCpty, eventName);
                    }
                    else if ( creditCptyOrg != null )
                    {
                        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().rebuildCounterpartyOrganizationCreditUtilizations( creditProviderOrg, creditCptyOrg, eventName );
                    }
                    else if ( creditCptyGuids != null )
                    {
                        cptyOrgs = new HashSet<Organization>();
                        String[] guids = creditCptyGuids.split( "," );
                        for ( String cptyOrgGuid : guids )
                        {
                            Organization org = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByGuid( cptyOrgGuid, OrganizationC.class );
                            if ( org != null )
                            {
                                CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().rebuildCounterpartyOrganizationCreditUtilizations( creditProviderOrg, org, eventName );
                                cptyOrgs.add( org );
                            }
                        }
                    }
                    else
                    {
                        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().rebuildProviderOrganizationCreditUtilizations( creditProviderOrg, eventName );
                    }

                    // special handling for org/le exposure level changes.
                    if ( eventName != null )
                    {
                        boolean removeNonAggCacheEntriesOnMethodologyChange = false;
                        if ( CreditMessageEvent.SETCREDITUTILIZATIONCALCULATOR.getName().equals( eventName ) )
                        {
                            String oldVal = ( String ) props.get( CreditLimitConstants.OLD_VALUE );
                            String newVal = ( String ) props.get( CreditLimitConstants.NEW_VALUE );
                            // if either old value or new value is NONE, then we need to remove the non-aggregate cache entries.
                            if ( ( oldVal != null && CreditLimitConstants.VALUE_NONE.equals( oldVal ) ) || ( newVal != null && CreditLimitConstants.VALUE_NONE.equals( newVal ) ) )
                            {
                                removeNonAggCacheEntriesOnMethodologyChange = true;
                            }
                        }
                        boolean updateAggregateCacheEntryOnly = CreditMessageEvent.SETCREDITRELATIONSHIP.getName().equals( eventName )
                                || CreditMessageEvent.REMOVECREDITRELATIONSHIP.getName().equals( eventName )
                                || CreditMessageEvent.SETCREDITUTILIZATIONCALCULATOR.getName().equals( eventName );

                        if ( CreditMessageEvent.SETORGEXPOSURE.getName().equals( eventName ) || CreditMessageEvent.SETCOUNTERPARTYEXPOSURE.getName().equals( eventName )
                                || CreditMessageEvent.ACTIVATECREDIT.getName().equals( eventName ) || CreditMessageEvent.INACTIVATECREDIT.getName().equals( eventName )
                                || CreditMessageEvent.REINITIALIZECREDITPROVIDER.getName().equals( eventName ) || CreditMessageEvent.REINITIALIZECREDITCOUNTERPARTYORG.getName().equals( eventName )
                                || updateAggregateCacheEntryOnly )
                        {
                            // update the aggregate cache with the updated entries.
                            CreditUtilizationManagerC.getInstance().updateAggregateCreditUtilizationCacheEntry( creditProviderOrg, eventName );
                            if ( !updateAggregateCacheEntryOnly || removeNonAggCacheEntriesOnMethodologyChange )
                            {
                                if ( creditCptyOrg != null )
                                {
                                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeCounterpartyOrganizationNonAggregateCreditUtilizations( creditProviderOrg, creditCptyOrg );
                                }

                                if ( cptyOrgs != null )
                                {
                                    for ( Organization cptyOrg : cptyOrgs )
                                    {
                                        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().removeCounterpartyOrganizationNonAggregateCreditUtilizations( creditProviderOrg, cptyOrg );
                                    }
                                }
                            }

                            // reset the cpty rules.
                            if ( creditCpty != null )
                            {
                                CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().resetCreditCounterpartyCptyRules( creditProviderOrg, creditCpty );
                            }
                            else if ( creditCptyOrg != null )
                            {
                                CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().resetCreditCounterpartyOrgCptyRules( creditProviderOrg, creditCptyOrg );
                            }
                            else if ( cptyOrgs != null )
                            {
                                for ( Organization cptyOrg : cptyOrgs )
                                {
                                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().resetCreditCounterpartyOrgCptyRules( creditProviderOrg, cptyOrg );
                                }
                            }
                            else
                            {
                                CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().resetCreditProviderCptyRules( creditProviderOrg );
                            }

                            // update auto-stop rules enabled map
                            CreditUtilizationManagerC.getInstance ().getCreditUtilizationCache ().rebuildAutoStopOutEnabledRules ();
                        }
                    }

                    if ( creditCpty != null )
                    {
                        new CreditUtilizationCacheFetchFromSubscriptionsC ().fetchTradingPartyCreditUtilizations ( creditProviderOrg, creditCptyOrg, creditCpty );
                    }
                    else
                    {
                        new CreditUtilizationCacheFetchFromSubscriptionsC ().fetchCreditUtilizations ( creditProviderOrg, creditCptyOrg );
                    }
                }
                else
                {
                    log.warn( "CURF.onCommit : Null credit provider org. props=" + props + ",guid=" + creditProviderOrgGuid );
                }
            }
            else
            {
                log.warn("CURF.onCommit : Null credit provider property. props=" + props);
            }

            log.info( new StringBuilder( 200 ).append( "CURF.onCommit : Finished updating credit utilizations cache. props=" )
                    .append( props ).append( ",provider=" ).append( creditProviderOrg ).append( ",cptyOrg=" ).append( creditCptyOrg )
                    .append( ",tp=" ).append( creditCpty ).append( ",event=" ).append( eventName )
                    .append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
        }
        catch ( Exception e )
        {
            log.warn( "CURF.onCommit : Error resetting the cache for org=" + creditProviderOrg, e );
        }
        finally
        {
            LogUtil.removeDisableDBQueryLogSwitch();
        }
    }
}

