package com.integral.finance.creditLimit.calculator;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.calculator.CalculatorFactory;
import com.integral.finance.creditLimit.CounterpartyCreditLimitRuleC;
import com.integral.finance.creditLimit.CreditLimitConstants;
import com.integral.finance.creditLimit.CreditUtilizationCalculator;
import com.integral.util.Factory;

/**
 * Provides factory methods for instantiating credit limit rule creation calculator objects. The determination of which
 * calculator used is based on the netting methodology.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitRuleCreationCalculatorFactory extends Factory
{
    // Singleton members.
    protected static CreditLimitRuleCreationCalculatorFactory current;

    /**
     * The suffix used for the credit limit rule creation calculators. The prefix is the short name
     * of the credit netting methodology.
     */
    protected static final String CREDIT_LIMIT_RULE_CREATION_CALCULATOR = "CreditLimitRuleCreationCalculator";

    // Static initialization.
    static
    {
        current = new CreditLimitRuleCreationCalculatorFactory();

        CalculatorFactory.putCalculator( getCalculatorName( CreditLimitConstants.GROSS_AGGREGATE_LIMIT_CALCULATOR ), CounterpartyCreditLimitRuleC.class, SingleCreditLimitRuleCreationCalculatorC.class );
        CalculatorFactory.putCalculator( getCalculatorName( CreditLimitConstants.AGGREGATE_LIMIT_CALCULATOR ), CounterpartyCreditLimitRuleC.class, SingleCreditLimitRuleCreationCalculatorC.class );
        CalculatorFactory.putCalculator( getCalculatorName( CreditLimitConstants.NET_OPEN_POSITION_CALCULATOR ), CounterpartyCreditLimitRuleC.class, SingleCreditLimitRuleCreationCalculatorC.class );
        CalculatorFactory.putCalculator( getCalculatorName( CreditLimitConstants.AGGREGATE_NPR_SETTLEMENT_CALCULATOR ), CounterpartyCreditLimitRuleC.class, SingleCreditLimitRuleCreationCalculatorC.class );
        CalculatorFactory.putCalculator( getCalculatorName( CreditLimitConstants.AGGREGATE_NET_CASH_SETTLEMENT_CALCULATOR ), CounterpartyCreditLimitRuleC.class, SingleCreditLimitRuleCreationCalculatorC.class );
        CalculatorFactory.putCalculator( getCalculatorName( CreditLimitConstants.AGGREGATE_NET_SETTLEMENT_RECEIVABLE_CALCULATOR ), CounterpartyCreditLimitRuleC.class, SingleCreditLimitRuleCreationCalculatorC.class );
        CalculatorFactory.putCalculator( getCalculatorName( CreditLimitConstants.AGGREGATE_MULTIFACTOR_SETTLEMENT_CALCULATOR ), CounterpartyCreditLimitRuleC.class, SingleCreditLimitRuleCreationCalculatorC.class );
        CalculatorFactory.putCalculator( getCalculatorName( CreditLimitConstants.AGGREGATE_NET_CREDITNET_SETTLEMENT_CALCULATOR ), CounterpartyCreditLimitRuleC.class, SingleCreditLimitRuleCreationCalculatorC.class );

        CalculatorFactory.putCalculator( getCalculatorName( CreditLimitConstants.GROSS_DAILY_LIMIT_CALCULATOR ), CounterpartyCreditLimitRuleC.class, DailyCreditLimitRuleCreationCalculatorC.class );
        CalculatorFactory.putCalculator( getCalculatorName( CreditLimitConstants.DAILY_SETTLEMENT_LIMIT_CALCULATOR ), CounterpartyCreditLimitRuleC.class, DailyCreditLimitRuleCreationCalculatorC.class );
    }

    public static CreditLimitRuleCreationCalculator getCreditLimitRuleCreationCalculator( CreditUtilizationCalculator calc )
    {
        return current._getCreditLimitRuleCreationCalculator( calc );
    }

    /**
     * Returns the credit limit rule creation calculator based on the credit utilization calculator which represents
     * the netting methodology.
     *
     * @param calc
     * @return credit limit rule creation calculator
     */
    protected CreditLimitRuleCreationCalculator _getCreditLimitRuleCreationCalculator( CreditUtilizationCalculator calc )
    {
        return ( CreditLimitRuleCreationCalculator ) CalculatorFactory.getCalculator( getCalculatorName( calc ), CounterpartyCreditLimitRuleC.class );
    }

    /**
     * Return the calculator name credit utilization calculator combination. The type of credit limit rule
     * is primarily decided by the type of netting.
     *
     * @param calc
     * @return calculator
     */
    private static String getCalculatorName( CreditUtilizationCalculator calc )
    {
        String calcPrefix = "";
        if ( calc != null )
        {
            calcPrefix = calc.getShortName();
        }
        return calcPrefix + CREDIT_LIMIT_RULE_CREATION_CALCULATOR;
    }
}

