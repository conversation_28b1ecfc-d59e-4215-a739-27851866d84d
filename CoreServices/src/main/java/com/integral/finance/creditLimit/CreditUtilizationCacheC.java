package com.integral.finance.creditLimit;

// Copyright (c) 2001-2005 Integral Development Corp.  All rights reserved.

import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.configuration.CreditConfigurationFactory;
import com.integral.finance.creditLimit.configuration.CreditConfigurationMBean;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationFactory;
import com.integral.finance.creditLimit.db.CreditDataQueryServiceFactory;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.workflow.dealing.DealingLimit;
import com.integral.workflow.dealing.DealingLimitCollection;
import com.integral.workflow.dealing.fx.FXDealingLimit;
import com.integral.workflow.dealing.fx.FXDealingLimitCollection;
import com.integral.workflow.dealing.fx.FXWorkflowFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.concurrent.locks.ReentrantLock;


/**
 * This class is used to keep the credit utilizations which are frequently used. Least used credit utilizations
 * are removed from the cache periodically.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditUtilizationCacheC implements CreditUtilizationCache
{
    protected Log log = LogFactory.getLog( this.getClass() );

    CreditUtilizationCacheSystem cache;
    private static final Map<String, CreditRelationSubscriptionInfo> subscriptionMap = new ConcurrentHashMap<String, CreditRelationSubscriptionInfo>();
    public static char LIMIT_SUBSCRIPTION_KEY_DELIMITER = '|';//used in utility jsp
    public static char CACHE_KEY_DELIMITER = '_';
    public static char CACHE_KEY_TPID_DELIMITER = '-';
    private static final CreditConfigurationMBean _configMBean = CreditConfigurationFactory.getCreditConfigurationMBean();

    private static final CreditUtilizationCache instance = new CreditUtilizationCacheC();

    /**
     * Instance variable for the map which stores the credit enable/disable flag.
     */
    private static final Map<String, Map<String, Integer>> creditEnableMap = new ConcurrentHashMap<String, Map<String, Integer>>();

    private final static Map<Organization, Map<TradingParty, CreditRelationship>> cclrMap = new ConcurrentHashMap<Organization, Map<TradingParty, CreditRelationship>>( 100 );

    /**
     * The map is used to store locks for subscription keys.
     */
    private static final ConcurrentMap<String, ReentrantLock> subscriptionLockMap = new ConcurrentHashMap<String, ReentrantLock>( 100 );

    private static ConcurrentMap<Organization, Collection<CounterpartyCreditLimitRule>> stopOutEnabledCclrMap;
    private static final Object autoStopOutRulesEnabledMapLock = new Object ();

    public static CreditUtilizationCache getInstance(){
        return instance;
    }

    private CreditUtilizationCacheC()
    {
        cache = new CreditUtilizationCacheSystem();
    }

    public void addAggregateCreditUtilizationCacheEntry( String cpoStr, String ccoStr, Long creditTpObjectId, CreditUtilization cu )
    {

    }

    public void addDailyCreditUtilizationCacheEntry( String cpoStr, String ccoStr, Long creditTPObjectId, IdcDate cuDate, Collection<CreditUtilization> cus )
    {

    }

    private Map<TradingParty, CreditRelationship> initTradingPartyCreditRelationshipMap( Organization cpo, TradingParty cc )
    {
        Map<TradingParty, CreditRelationship> tpMap = cclrMap.get( cpo );
        if ( tpMap == null )
        {
            tpMap = new ConcurrentHashMap<TradingParty, CreditRelationship>();
            LegalEntity le = CreditUtilC.getCreditProviderLegalEntity( cpo );
            if ( le != null )
            {
                CreditRelationship cr = new CreditRelationship( le, cc );
                cr.initTransients();
                tpMap.put( cc, cr );
                cclrMap.put( cpo, tpMap );
            }
        }
        return tpMap;
    }

    public CreditTenorRestriction getMinimumTenor(  Organization cpo, TradingParty cc)
    {
        Map<TradingParty, CreditRelationship> tpMap = cclrMap.get( cpo );
        if ( tpMap == null )
        {
            tpMap = initTradingPartyCreditRelationshipMap(cpo,cc);
        }
        CreditRelationship cr = tpMap.get(cc);
        CreditTenorRestriction tenor = null;
        if(null != cr)
        {
            tenor = cr.getMinimumTenor();
        }
        return tenor;
    }

    public CreditTenorRestriction getMaximumTenor(  Organization cpo, TradingParty cc)
    {
        Map<TradingParty, CreditRelationship> tpMap = cclrMap.get( cpo );
        if ( tpMap == null )
        {
            tpMap = initTradingPartyCreditRelationshipMap(cpo,cc);
        }
        CreditRelationship cr = tpMap.get(cc);
        CreditTenorRestriction tenor = null;
        if(null != cr)
        {
            tenor = cr.getMaximumTenor();
        }
        return tenor;
    }

    public DealingLimitCollection getCreditLimitSubscriptions( LegalEntity cpl, TradingParty cc )
    {
        String key = getCreditLimitSubscriptionCacheKey(cpl, cc);
        if ( key != null )
        {
            CreditRelationSubscriptionInfo si = subscriptionMap.get( key );
            if ( si != null )
            {
                return si.getDealingLimitCollection();
            }
        }
        return null;
    }

    public void addCreditLimitSubscriptions( LegalEntity cpl, TradingParty cc, Organization cco, Collection<String> ccyPairs )
    {
        // adding of limit subscription is synchronized to avoid corrupting limit data during credit workflow.
        String key = getCreditLimitSubscriptionCacheKey(cpl, cc);
        if ( key == null )
        {
            return;
        }
        ReentrantLock lock = getSubscriptionLock( key );
        try
        {
            lock.lock();
            if ( ccyPairs != null && !ccyPairs.isEmpty() )
            {
                CreditRelationSubscriptionInfo si = subscriptionMap.get( key );
                FXDealingLimitCollection dlc;
                if ( si == null )
                {
                    si = new CreditRelationSubscriptionInfoC( cpl, cc );
                    dlc = FXWorkflowFactory.newFXDealingLimitCollection();
                    si.setDealingLimitCollection( dlc );
                    subscriptionMap.put( key, si );
                }
                else
                {
                    dlc = ( FXDealingLimitCollection ) si.getDealingLimitCollection();
                }

                // make a copy of dealing limit collection.
                FXDealingLimitCollection dlcCopy = ( FXDealingLimitCollection ) dlc.copy();

                for ( String ccyPair : ccyPairs )
                {
                    CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString(ccyPair);
                    Currency baseCcy = currencyPair.getBaseCurrency();
                    Currency varCcy = currencyPair.getVariableCurrency();
                    FXDealingLimit dealingLimit = dlcCopy.getDealingLimit( baseCcy, varCcy );
                    if ( dealingLimit == null )
                    {
                        dealingLimit = FXWorkflowFactory.newFXDealingLimit();
                        dealingLimit.setBaseCurrency( baseCcy );
                        dealingLimit.setVariableCurrency( varCcy );
                        dealingLimit.setBidLimits( new double[]{DealingLimit.DEFAULT_LIMIT} );
                        dealingLimit.setOfferLimits( new double[]{DealingLimit.DEFAULT_LIMIT} );
                        dlcCopy.addDealingLimit( dealingLimit );
                    }
                }

                // finally set the dealing limit collection copy in the subscription info.
                si.setDealingLimitCollection( dlcCopy );
            }
        }
        finally
        {
            lock.unlock();
        }
    }

    public void addCreditLimitSubscription( LegalEntity cpl, TradingParty cc, Organization cco, Currency baseCcy, Currency varCcy )
    {
        // adding of limit subscription is synchronized to avoid corrupting limit data during credit workflow.
        String key = getCreditLimitSubscriptionCacheKey( cpl, cc );
        if ( key == null )
        {
            return;
        }
        ReentrantLock lock = getSubscriptionLock( key );
        try
        {
            lock.lock();
            CreditRelationSubscriptionInfo si = subscriptionMap.get( key );
            FXDealingLimitCollection dlc;
            if ( si == null )
            {
                si = new CreditRelationSubscriptionInfoC( cpl, cc );
                dlc = FXWorkflowFactory.newFXDealingLimitCollection();
                si.setDealingLimitCollection( dlc );
                subscriptionMap.put( key, si );
            }
            else
            {
                dlc = ( FXDealingLimitCollection ) si.getDealingLimitCollection();
            }

            // make a copy of dealing limit collection.
            FXDealingLimitCollection dlcCopy = ( FXDealingLimitCollection ) dlc.copy();

            FXDealingLimit dealingLimit = dlcCopy.getDealingLimit( baseCcy, varCcy );
            if ( dealingLimit == null )
            {
                dealingLimit = FXWorkflowFactory.newFXDealingLimit();
                dealingLimit.setBaseCurrency( baseCcy );
                dealingLimit.setVariableCurrency( varCcy );
                dealingLimit.setBidLimits( new double[]{DealingLimit.DEFAULT_LIMIT} );
                dealingLimit.setOfferLimits( new double[]{DealingLimit.DEFAULT_LIMIT} );
                dlcCopy.addDealingLimit( dealingLimit );
            }

            // finally set the dealing limit collection copy in the subscription info.
            si.setDealingLimitCollection(dlcCopy);
        }
        finally
        {
            lock.unlock();
        }
    }

    public void removeCreditLimitSubscription( LegalEntity cpl, TradingParty cc, Organization cco, Currency baseCcy, Currency varCcy )
    {
        // adding of limit subscription is synchronized to avoid corrupting limit data during credit workflow.
        String key = getCreditLimitSubscriptionCacheKey( cpl, cc );
        if ( key == null )
        {
            return;
        }
        ReentrantLock lock = getSubscriptionLock( key );

        try
        {
            lock.lock();
            CreditRelationSubscriptionInfo si = subscriptionMap.get(key);
            if ( si != null )
            {
                FXDealingLimitCollection col = ( FXDealingLimitCollection ) si.getDealingLimitCollection();
                if ( col != null )
                {
                    FXDealingLimitCollection dlcCopy = ( FXDealingLimitCollection ) col.copy();
                    dlcCopy.removeDealingLimit( CurrencyFactory.getCurrencyPairName(baseCcy, varCcy) );
                    si.setDealingLimitCollection( dlcCopy );
                }
            }
        }
        finally
        {
            lock.unlock();
        }
    }

    public void removeCreditLimitSubscriptions( LegalEntity cpl, TradingParty cc, Organization cco, Collection<String> ccyPairs )
    {
        // adding of limit subscription is synchronized to avoid corrupting limit data during credit workflow.
        String key = getCreditLimitSubscriptionCacheKey( cpl, cc );
        if ( key == null )
        {
            return;
        }
        ReentrantLock lock = getSubscriptionLock( key );
        try
        {
            lock.lock();
            if ( ccyPairs == null )
            {
                subscriptionMap.remove( key );
                return;
            }
            FXDealingLimitCollection col = getDealingLimitCollectionFromSubscriptionMap(key);
            if ( col != null )
            {
                FXDealingLimitCollection dlcCopy = ( FXDealingLimitCollection ) col.copy();
                for ( String ccyPair : ccyPairs )
                {
                    dlcCopy.removeDealingLimit( ccyPair );
                }
                CreditRelationSubscriptionInfo si = subscriptionMap.get( key );
                if ( si != null )
                {
                    si.setDealingLimitCollection( dlcCopy );
                }
            }
        }
        finally
        {
            lock.unlock();
        }
    }

    public void addCreditUtilizationCacheEntry( LegalEntity cpl, Organization ccOrg, TradingParty creditTp, IdcDate date, CreditUtilizationCacheEntry cce )
    {
        cache.put( getCacheKey( cpl, ccOrg, creditTp, date ), cce );
    }

    public void addCreditUtilizationCacheEntry( Organization cpo, CounterpartyCreditLimitRule cclr, IdcDate date, Collection<CreditUtilization> creditUtils)
    {
        String cco = cclr.getTradingPartyOrganization().getShortName();
        TradingParty cc = cclr.getTradingParty();
        Long ccId = cc!=null?cc.getObjectID():null;
        CreditUtilizationCacheEntry cce = new CreditUtilizationCacheEntryC( date, creditUtils, cclr, cpo, false );
        cache.put( getCacheKey( cpo.getShortName(), cco, ccId, date ), cce );
    }

    @Override
    public CreditUtilization getCreditUtilizationFromCache(String key, boolean isAggCu)
    {
        CreditUtilizationCacheEntry cuce = cache.get( key );
        if(cuce!=null)
        {
            Collection<CreditUtilization> cus = cuce.getCreditUtilizations();
            for(CreditUtilization cu : cus)
            {
                if(!isAggCu){
                    if(cu instanceof DailyCreditUtilization){
                        return cu;
                    }
                }else{
                    if(!(cu instanceof DailyCreditUtilization)){
                        return cu;
                    }
                }
            }
        }
        return null;
    }


    @Override
    public CreditUtilization getCreditUtilizationFromCache(String cpoStr, String ccoStr, Long creditTpObjectId, IdcDate cuDate, boolean isAggCu)
    {
        String key;
        if(isAggCu){
            key = getAggregateCreditUtilizationCacheKey( cpoStr, ccoStr, creditTpObjectId );
        }else{
            key = getCacheKey( cpoStr, ccoStr, creditTpObjectId, cuDate );
        }
        return getCreditUtilizationFromCache( key, isAggCu );
    }

    public CreditUtilizationCacheEntry getCreditUtilizationCacheEntry( LegalEntity cpl, Organization ccOrg, TradingParty cc, IdcDate date )
    {
        return getCreditUtilizationCacheEntry( cpl.getOrganization().getShortName(), ccOrg.getShortName(), cc!=null?cc.getObjectID():null, date );
    }

    public CreditUtilizationCacheEntry getCreditUtilizationCacheEntry( String cpo, String cco, Long creditTpObjectId, IdcDate date )
    {
        return cache.get( getCacheKey( cpo, cco, creditTpObjectId, date ) );
    }

    public void removeTradingPartyCreditUtilizations( Organization cpo, TradingParty cc )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.removeCounterpartyEntries(cpo, cc);
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append( "CUC.removeTradingPartyCreditUtilizations : cache size=" )
                        .append( cache.size() ).append( ",originalSize=" ).append( originalSize ).append( ",cpo=" ).append( cpo )
                        .append( ",cc=" ).append( cc ).append( " took ms=" )
                        .append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.removeTradingPartyCreditUtilizations : Exception. cpo=" + cpo + ",cc=" + cc, e );
        }
    }

    public void removeCounterpartyOrganizationCreditUtilizations( Organization cpo, Organization cco )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.removeCounterpartyOrganizationEntries(cpo, cco);
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append( "CUC.removeCounterpartyOrganizationCreditUtilizations : cache size=" )
                        .append( cache.size() ).append(",origSize=").append(originalSize).append( ",cpo=" ).append( cpo )
                        .append(",cco=").append( cco ).append(" took ms=").
                                append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.removeCounterpartyOrganizationCreditUtilizations : Exception. cpo=" + cpo + ",cco=" + cco, e );
        }
    }

    public void removeProviderOrganizationCreditUtilizations( Organization cpo )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.removeProviderOrganizationEntries(cpo);
            if ( log.isDebugEnabled() )
            {
                log.debug(new StringBuilder(100).append("CUC.removeProviderOrganizationCreditUtilizations : cache size=")
                        .append(cache.size()).append(",origSize=").append(originalSize).append(",cpo=").append(cpo)
                        .append(" took ms=").append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString());
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.removeProviderOrganizationCreditUtilizations : Exception. cpo=" + cpo, e );
        }
    }

    public void removeAllCreditUtilizations()
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.removeAll();
            if ( log.isInfoEnabled() )
            {
                log.info(new StringBuilder(100).append("CUC.removeAllCreditUtilizations : cache size=")
                        .append(cache.size()).append(",origSize=").append(originalSize).append(" took ms=")
                        .append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString());
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.removeAllCreditUtilizations : Exception", e );
        }
    }

    @Override
    public void flushAndReloadAllUtilizations(String cpoShortName)
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.flushAndReloadAllUtilizations( cpoShortName );
            log.info(new StringBuilder(100).append("CUC.flushAndReloadAllUtilizations : cache size=")
                    .append(cache.size()).append(",origSize=").append(originalSize).append(" took ms=")
                    .append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000))
                    .append(" for Org=").append(cpoShortName).toString());
        }
        catch ( Exception e )
        {
            log.error( "CUC.flushAndReloadAllUtilizations : Exception", e );
        }
    }

    public void removeAllObsoleteCacheEntries()
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.removeAllObsoleteCacheEntries();
            log.info(new StringBuilder(100).append("CUC.removeAllObsoleteCacheEntries : cache size=")
                    .append(cache.size()).append(",origSize=").append(originalSize).append(" took ms=")
                    .append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString());
        }
        catch ( Exception e )
        {
            log.error( "CUC.removeAllObsoleteCacheEntries : Exception", e );
        }
    }

    public void removeCounterpartyOrganizationNonAggregateCreditUtilizations( Organization cpo, Organization cco )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.removeCounterpartyOrganizationNonAggregateEntries(cpo, cco);
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append( "CUC.removeCounterpartyOrganizationNonAggregateCreditUtilizations : cache size=" )
                        .append( cache.size() ).append( ",origSize=" ).append( originalSize ).append( ",cpo=" ).append( cpo )
                        .append( ",cco=" ).append( cco ).append( " took ms=" ).
                                append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.removeCounterpartyOrganizationNonAggregateCreditUtilizations : Exception. cpo=" + cpo + ",cco=" + cco, e );
        }
    }


    public void resetTradingPartyCreditUtilizations( Organization cpo, TradingParty cc )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.resetCounterpartyEntries(cpo, cc);
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append( "CUC.resetTradingPartyCreditUtilizations : cache size=" )
                        .append( cache.size() ).append( ",origSize=" ).append( originalSize ).append( ",cpo=" ).append( cpo )
                        .append( ",cc=" ).append( cc ).append( " took ms=" )
                        .append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.resetTradingPartyCreditUtilizations : Exception. cpo=" + cpo + "cc=" + cc, e );
        }
    }

    public void resetCounterpartyOrganizationCreditUtilizations( Organization cpo, Organization cco )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.resetCounterpartyOrganizationEntries(cpo, cco);
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append( "CUC.resetCounterpartyOrganizationCreditUtilizations : cache size=" )
                        .append( cache.size() ).append(",origSize=").append(originalSize).append( ",cpo=" ).append( cpo )
                        .append(",cco=").append( cco ).append(" took ms=").
                                append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.resetCounterpartyOrganizationCreditUtilizations : Exception. cpo=" + cpo + ",cco=" + cco, e );
        }
    }

    public void resetProviderOrganizationCreditUtilizations( Organization cpo )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.resetProviderOrganizationEntries(cpo);
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append("CUC.resetProviderOrganizationCreditUtilizations : cache size=")
                        .append(cache.size()).append(",origSize=").append(originalSize).append(",cpo=").append( cpo )
                        .append(" took ms=").append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.resetProviderOrganizationCreditUtilizations : Exception. cpo=" + cpo, e );
        }
    }

    public void resetAllCreditUtilizations()
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.resetAll();
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append("CUC.resetAllCreditUtilizations : cache size=")
                        .append(cache.size()).append(",origSize=").append( originalSize ).append(" took ms=")
                        .append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.resetAllCreditUtilizations : Exception", e );
        }
    }

    public void rebuildTradingPartyCreditUtilizations( Organization cpo, TradingParty cc, String event )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.rebuildCounterpartyEntries(cpo, cc, event);
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append( "CUC.rebuildTradingPartyCreditUtilizations : cache size=" )
                        .append( cache.size() ).append( ",origSize=" ).append( originalSize ).append(",cpo=").append(cpo)
                        .append(",cc=").append( cc ).append( " took ms=" )
                        .append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.rebuildTradingPartyCreditUtilizations : Exception. cpo=" + cpo + "cc=" + cc, e );
        }
    }

    public void rebuildCounterpartyOrganizationCreditUtilizations( Organization cpo, Organization cco, String event )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.rebuildCounterpartyOrganizationEntries(cpo, cco, event);
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append( "CUC.rebuildCounterpartyOrganizationCreditUtilizations : cache size=" )
                        .append( cache.size() ).append(",origSize=").append(originalSize).append( ",cpo=" ).append( cpo )
                        .append(",cco=").append( cco ).append(" took ms=").
                                append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.rebuildCounterpartyOrganizationCreditUtilizations : Exception. cpo=" + cpo + ",cco=" + cco, e );
        }
    }

    public void rebuildProviderOrganizationCreditUtilizations( Organization cpo, String event )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.rebuildProviderOrganizationEntries(cpo, event);
            if ( log.isDebugEnabled() )
            {
                log.debug(new StringBuilder(100).append("CUC.rebuildProviderOrganizationCreditUtilizations : cache size=")
                        .append(cache.size()).append(",origSize=").append(originalSize).append(",cpo=").append(cpo)
                        .append(" took ms=").append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString());
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.rebuildProviderOrganizationCreditUtilizations : Exception. cpo=" + cpo, e );
        }
    }

    public void rebuildAllStaleEntries()
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            long updatedCount = cache.rebuildAllStaleEntries();
            if ( updatedCount > 0 )
            {
                log.info(new StringBuilder(100).append("CUC.rebuildAllStaleEntries : cache size=")
                        .append(originalSize).append(",updated=").append(updatedCount).append(" took ms=")
                        .append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString());
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.rebuildAllCreditUtilizations. Exception", e );
        }
    }

    public void rebuildAllCreditUtilizations()
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.rebuildAll();
            log.info( new StringBuilder( 100 ).append( "CUC.rebuildAllCreditUtilizations : cache size=" )
                    .append( cache.size() ).append( ",originalSize=" ).append( originalSize ).append( " took ms=" )
                    .append( String.format( "%1.4f", ( ( double ) ( System.nanoTime() - t0 ) ) / 1000000 ) ).toString() );
        }
        catch ( Exception e )
        {
            log.error( "CUC.rebuildAllCreditUtilizations. Exception", e );
        }
    }

    public void rebuildAllCreditUtilizationsOptimized()
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.rebuildAllOptimized();
            log.info( new StringBuilder( 100 ).append( "CUC.rebuildAllCreditUtilizationsOptimized : cache size=" )
                    .append( cache.size() ).append( ",originalSize=" ).append( originalSize ).append( " took ms=" )
                    .append( String.format( "%1.4f", ( ( double ) ( System.nanoTime() - t0 ) ) / 1000000 ) ).toString() );
        }
        catch ( Exception e )
        {
            log.error( "CUC.rebuildAllCreditUtilizationsOptimized. Exception", e );
        }
    }


    public void rebuildAllCreditUtilizationsWithDelay( long delaySeed )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.rebuildAllWithDelay(delaySeed);
            log.info( new StringBuilder( 100 ).append( "CUC.rebuildAllCreditUtilizationsWithDelay : Rebuilt cache entries with delay. cache size=" )
                    .append( cache.size() ).append( ",originalSize=" ).append( originalSize ).append( " took ms=" )
                    .append( String.format( "%1.4f", ( ( double ) ( System.nanoTime() - t0 ) ) / 1000000 ) ).toString() );
        }
        catch ( Exception e )
        {
            log.error( "CUC.rebuildAllCreditUtilizationsWithDelay : Exception", e );
        }
    }


    public void setAllCreditUtilizationsStale()
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.setAllStale();
            log.info( new StringBuilder( 100 ).append( "CUC.setAllCreditUtilizationsStale : Set all cache entries stale. cache size=" )
                    .append( cache.size() ).append( ",originalSize=" ).append( originalSize ).append( " took ms=" )
                    .append( String.format( "%1.4f", ( ( double ) ( System.nanoTime() - t0 ) ) / 1000000 ) ).toString() );
        }
        catch ( Exception e )
        {
            log.error( "CUC.setAllCreditUtilizationsStale : Exception", e );
        }
    }

    public Collection<CreditUtilization> getAllCreditUtilizations()
    {
        return cache.getAllCreditUtilizations();
    }

    public Collection<CreditUtilization> getAllAggregateCreditUtilizations( boolean onlyProviderCache )
    {
        return cache.getAllAggregateCreditUtilizations(onlyProviderCache);
    }

    public int size()
    {
        return cache.size();
    }

    public boolean isCreditEnabled(int creditStatus)
    {
    	return creditStatus > CreditLimitConstants.CREDIT_NO_CHECK;
    }
    
    public boolean isCreditEnabled( Organization cpo, TradingParty creditTp )
    {
        if ( cpo != null )
        {
        	int creditStatus = getCreditStatus(cpo, creditTp);
            return isCreditEnabled(creditStatus);
        }
        return false;
    }

    public int getCreditStatus( Organization cpo, TradingParty cc )
    {
        if ( cpo != null && cc != null )
        {
            /*
                In Firm Based credit a TradingParty is created in memory if the trading firms of credit provider & credit customer are the
                same. In such a case credit status is "No Check"
             */
            if( cc.getLegalEntity().getOrganization().isSameAs(cpo)){
                return CreditLimitConstants.CREDIT_NO_CHECK;
            }
            TradingParty tp = CounterpartyUtilC.getTradingParty(cc, cpo);
            if ( tp != null )
            {
                Map<String, Integer> tpMap = creditEnableMap.get( cpo.getGUID() );
                if ( tpMap == null )
                {
                    tpMap = new ConcurrentHashMap<String, Integer>();
                    creditEnableMap.put( cpo.getGUID(), tpMap );
                }
                Integer tpStatus = tpMap.get( tp.getGUID() );
                if ( tpStatus == null )
                {
                    tpStatus = CreditUtilC.getCreditStatus( cpo, tp );
                    tpMap.put( tp.getGUID(), tpStatus );
                }
                return tpStatus;
            }
        }
        if( cc == null ){
            /*
                In firm based credit the trading party can be null if required relationships are missing.
                In such a case return SUSPENDED status
             */
            return CreditLimitConstants.CREDIT_SUSPEND;
        }
        return CreditLimitConstants.CREDIT_NO_CHECK;
    }


    public void resetCreditEnabled( Organization cpo, TradingParty cc )
    {
        log.info( "CUC.resetCreditEnabled : Resetting credit enable cache. cpo=" + cpo + ",cc=" + cc );
        synchronized ( creditEnableMap )
        {
            if ( cpo != null )
            {
                Map<String, Integer> tpMap = creditEnableMap.get( cpo.getGUID() );
                if ( tpMap != null && !tpMap.isEmpty() )
                {
                    // if tp is not specified, reset for all trading parties.
                    if ( cc == null )
                    {
                        tpMap.clear();
                    }
                    else
                    {
                        TradingParty tp = CounterpartyUtilC.getTradingParty( cc, cpo );
                        if ( tp != null )
                        {
                            tpMap.remove( tp.getGUID() );
                        }
                    }
                }
            }
            else
            {
                creditEnableMap.clear();
            }
        }
    }

    public Map<String, CreditRelationSubscriptionInfo> getCreditLimitSubscriptionMap()
    {
        return Collections.unmodifiableMap( subscriptionMap );
    }

    public Map getCreditEnableMap()
    {
        return Collections.unmodifiableMap( creditEnableMap );
    }

    public CreditUtilizationCacheEntry getAggregateCreditUtilizationCollectionCacheEntry( Organization cpo )
    {
        return cache.get( getAggregateCreditUtilizationCollectionCacheKey( cpo ) );
    }

    public void addAggregateCreditUtilizationCollectionCacheEntry( CreditUtilizationCacheEntry cacheEntry, Organization cpo )
    {
        cache.put( getAggregateCreditUtilizationCollectionCacheKey( cpo ), cacheEntry);
    }

    public void addAggregateCreditUtilizationCacheEntry( IdcDate aDate, Collection<CreditUtilization> creditUtils, CounterpartyCreditLimitRule cclr, Organization creditProviderOrg)
    {
        CreditUtilizationCacheEntry  cacheEntry = new CreditUtilizationCacheEntryC( null, creditUtils, cclr, creditProviderOrg, false );
        String ccoShortName = cclr.getTradingPartyOrganization().getShortName();
        TradingParty creditTp = cclr.getTradingParty();
        cache.put( getAggregateCreditUtilizationCacheKey( creditProviderOrg.getShortName(), ccoShortName, creditTp!=null?creditTp.getObjectId():null ), cacheEntry);
    }

    public CreditUtilizationCacheEntry getAggregateCreditUtilizationCacheEntry( Organization cpo, Organization cco, TradingParty cc)
    {
        return cache.get( getAggregateCreditUtilizationCacheKey( cpo.getShortName(), cco.getShortName(), cc!=null?cc.getObjectId():null ) );
    }

    public Collection<CreditUtilizationCacheEntry> getAllCreditUtilizationCacheEntries()
    {
        return cache.getAllCreditUtilizationCacheEntries();
    }

    public CounterpartyCreditLimitRule getActiveCounterpartyCreditLimitRule( Organization cpo, TradingParty cc )
    {
        CreditRelationship cr = getCreditRelationship ( cpo, cc );
        if(cr != null) 
        {
        	return cr.getActiveCptyRule();
        }
        return null;
    }

    public CreditRelationship getCreditRelationship( Organization cpo, TradingParty cc )
    {
        boolean initialize = false;
        Map<TradingParty, CreditRelationship> tpMap = cclrMap.get( cpo );
        if ( tpMap == null )
        {
            if(log.isDebugEnabled())
            {
                log.debug("CUCache: Didnt find tpMap for " + cpo + " so creating a new map.");
            }
            tpMap = new ConcurrentHashMap<TradingParty, CreditRelationship>();
            initialize = true;
        }
        else if(!tpMap.containsKey( cc ))
        {
            initialize = true;
        }
        if(initialize)
        {
            LegalEntity le = cpo.getDefaultDealingEntity();
            if ( le == null )
            {
                Collection<LegalEntity> les = cpo.getLegalEntities();
                if ( les != null && !les.isEmpty() )
                {
                    le = les.iterator().next();
                }
            }
            if ( le != null )
            {
                CreditRelationship cr = new CreditRelationship( le, cc );
                cr.initTransients();
                tpMap.put( cc, cr );
                if(log.isDebugEnabled())
                {
                    log.debug("CUCache: Adding entry to tpMap. Key=" + cc + ", Value="+cr);
                }
                cclrMap.put( cpo, tpMap );
            }
        }
        return tpMap.get( cc );
    }


    public CounterpartyCreditLimitRule getOrgLevelCounterpartyCreditLimitRule( Organization cpo, TradingParty cc )
    {
        Map<TradingParty, CreditRelationship> tpMap = cclrMap.get( cpo );
        if ( tpMap == null )
        {
            tpMap = new ConcurrentHashMap<TradingParty, CreditRelationship>();
            LegalEntity le = cpo.getDefaultDealingEntity();
            if ( le == null )
            {
                Collection<LegalEntity> les = cpo.getLegalEntities();
                if ( les != null && !les.isEmpty() )
                {
                    le = les.iterator().next();
                }
            }
            if ( le != null )
            {
                CreditRelationship cr = new CreditRelationship( le, cc );
                cr.initTransients();
                tpMap.put( cc, cr );
                cclrMap.put( cpo, tpMap );
            }
        }
        CreditRelationship cr = tpMap.get( cc );
        return cr != null ? cr.getOrgLevelCptyRule() : CreditUtilC.getOrgLevelCounterpartyCreditLimitRule( cpo, cc );
    }


    public void resetCreditProviderCptyRules( Organization cpo )
    {
        int cclrCount = -1;
        Map<TradingParty, CreditRelationship> tpMap = cclrMap.get( cpo );
        if ( tpMap != null )
        {
            cclrCount = tpMap.size ();
            Collection<CreditRelationship> crs = new ArrayList<CreditRelationship>( tpMap.values() );
            for ( CreditRelationship cr : crs )
            {
                cr.initTransients();
            }
        }
        log.info( "CUC.resetCreditProviderCptyRules - re-initializing cclr map for cpo=" + cpo + ",cclrs=" + cclrCount );
    }

    public void resetCreditCounterpartyOrgCptyRules( Organization cpo, Organization cco )
    {
        int cclrCount = -1;
        int initCount = 0;
        Map<TradingParty, CreditRelationship> tpMap = cclrMap.get( cpo );
        if ( tpMap != null )
        {
            cclrCount = tpMap.size ();
            Collection<TradingParty> tps = new ArrayList<TradingParty>( tpMap.keySet() );
            for ( TradingParty tp : tps )
            {
                if ( cco.isSameAs( tp.getLegalEntityOrganization() ) )
                {
                    resetCreditEnabled( cpo, tp );
                    CreditRelationship cr = tpMap.get( tp );
                    if ( cr != null )
                    {
                        cr.initTransients();
                        initCount++;
                    }
                }
            }
        }
        log.info( "CUC.resetCreditCounterpartyOrgCptyRules - re-initializing cpty cclr map for cpo=" + cpo + ",cco=" + cco + ",cclrs=" + cclrCount + ",initCount=" + initCount );
    }

    public void resetCreditCounterpartyCptyRules( Organization cpo, TradingParty cc )
    {
        Map<TradingParty, CreditRelationship> tpMap = cclrMap.get( cpo );
        if ( tpMap != null )
        {
            CreditRelationship cr = tpMap.get( cc );
            if ( cr != null )
            {
                cr.initTransients();
            }
        }
    }

    public void resetCreditCounterpartyCptyTenor( Organization cpo )
    {
        Map<TradingParty, CreditRelationship> tpMap = cclrMap.get( cpo );
        if ( tpMap != null )
        {
            Collection<CreditRelationship> crs = new ArrayList<CreditRelationship>( tpMap.values() );
            for ( CreditRelationship cr : crs )
            {
                cr.initTenors();
            }
        }
    }

    public void resetCreditCounterpartyCptyTenor( Organization cpo, Organization cco )
    {
        Map<TradingParty, CreditRelationship> tpMap = cclrMap.get( cpo );
        if ( tpMap != null )
        {
            Collection<TradingParty> tps = new ArrayList<TradingParty>( tpMap.keySet() );
            for ( TradingParty tp : tps )
            {
                if ( cco.isSameAs( tp.getLegalEntityOrganization() ) )
                {
                    CreditRelationship cr = tpMap.get( tp );
                    if ( cr != null )
                    {
                        cr.initTenors();
                    }
                }
            }
        }
    }
    public void resetCreditCounterpartyCptyTenor( Organization cpo, TradingParty cc )
    {
        Map<TradingParty, CreditRelationship> tpMap = cclrMap.get( cpo );
        if ( tpMap != null )
        {
            CreditRelationship cr = tpMap.get( cc );
            if ( cr != null )
            {
                cr.initTenors();
            }
        }
    }
    public void updateTradingPartyCreditExemptionList( Organization cpo, TradingParty cc )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.updateCounterpartyEntriesForExemptionList( cpo, cc );
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append( "CUC.updateTradingPartyCreditExemptionList : cache size=" )
                        .append( cache.size() ).append( ",origSize=" ).append( originalSize ).append( ",cpo=" ).append( cpo )
                        .append( " took ms=" ).append( String.format( "%1.4f", ( ( double ) ( System.nanoTime() - t0 ) ) / 1000000 ) ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.updateTradingPartyCreditExemptionList : Exception. cpo=" + cpo, e );
        }
    }

    public void updateCounterpartyOrganizationCreditExemptionList( Organization cpo, Organization cco )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.updateCounterpartyOrganizationEntriesForExemptionList(cpo, cco);
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append( "CUC.updateCounterpartyOrganizationCreditExemptionList : cache size=" )
                        .append( cache.size() ).append(",origSize=").append(originalSize).append( ",cpo=" ).append( cpo )
                        .append(",cco=").append( cco ).append(" took ms=").
                                append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.updateCounterpartyOrganizationCreditExemptionList : Exception. cpo=" + cpo + ",cco=" + cco, e );
        }
    }

    public void updateProviderOrganizationCreditExemptionList( Organization cpo )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.updateProviderOrganizationEntriesForExemptionList(cpo);
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append("CUC.updateProviderOrganizationCreditExemptionList : cache size=")
                        .append(cache.size()).append( ",origSize=" ).append( originalSize ).append(",cpo=").append( cpo )
                        .append(" took ms=").append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.updateProviderOrganizationCreditExemptionList : Exception. cpo=" + cpo, e );
        }
    }

    public void updateCreditExemptionList( CurrencyPairGroup cpg )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.updateAllForExemptionList(cpg);
            if ( log.isInfoEnabled() )
            {
                log.info(new StringBuilder(100).append("CUC.updateCreditExemptionList : cache size=")
                        .append(cache.size()).append(",origSize=").append(originalSize).append(" took ms=")
                        .append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString());
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.updateCreditExemptionList : Exception", e );
        }
    }

    public void updateTradingPartyCreditTenorProfile( Organization cpo, TradingParty cc )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.updateCounterpartyEntriesForTenorProfile(cpo, cc);
            if ( log.isDebugEnabled() )
            {
                log.debug(new StringBuilder(100).append("CUC.updateTradingPartyCreditTenorProfile : cache size=")
                        .append(cache.size()).append(",origSize=").append(originalSize).append(",cpo=").append(cpo)
                        .append(" took ms=").append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString());
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.updateTradingPartyCreditTenorProfile : Exception. cpo=" + cpo, e );
        }
    }

    public void updateCounterpartyOrganizationCreditTenorProfile( Organization cpo, Organization cco )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.updateCounterpartyOrganizationEntriesForTenorProfile(cpo, cco);
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append( "CUC.updateCounterpartyOrganizationCreditTenorProfile : cache size=" )
                        .append( cache.size() ).append(",origSize=").append(originalSize).append( ",cpo=" ).append( cpo )
                        .append(",cco=").append( cco ).append(" took ms=").
                                append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.updateCounterpartyOrganizationCreditTenorProfile : Exception. cpo=" + cpo + ",cco=" + cco, e );
        }
    }

    public void updateProviderOrganizationCreditTenorProfile( Organization cpo )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.updateProviderOrganizationEntriesForTenorProfile(cpo);
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append("CUC.updateProviderOrganizationCreditTenorProfile : cache size=")
                        .append(cache.size()).append( ",origSize=" ).append( originalSize ).append(",cpo=").append( cpo )
                        .append(" took ms=").append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.updateProviderOrganizationCreditTenorProfile : Exception. cpo=" + cpo, e );
        }
    }

    public void updateAllCreditTenorProfiles()
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.updateAllCreditTenorProfiles();
            log.info(new StringBuilder(100).append("CUC.updateAllCreditTenorProfiles : cache size=")
                    .append(cache.size()).append(",origSize=").append(originalSize)
                    .append(" took ms=").append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString());
        }
        catch ( Exception e )
        {
            log.error( "CUC.updateAllCreditTenorProfiles : Exception while updating all tenor coefficients.", e );
        }
    }

    public void updateTradingPartyCreditMode( Organization cpo, TradingParty cc )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.updateCounterpartyEntriesForCreditMode(cpo, cc);
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append( "CUC.updateTradingPartyCreditMode : cache size=" )
                        .append( cache.size() ).append( ",origSize=" ).append( originalSize ).append( ",cpo=" ).append( cpo )
                        .append( " took ms=" ).append( String.format( "%1.4f", ( ( double ) ( System.nanoTime() - t0 ) ) / 1000000 ) ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.updateTradingPartyCreditMode : Exception. cpo=" + cpo, e );
        }
    }

    public void updateCounterpartyOrganizationCreditMode( Organization cpo, Organization cco )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.updateCounterpartyOrganizationEntriesForCreditMode(cpo, cco);
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append( "CUC.updateCounterpartyOrganizationCreditMode : cache size=" )
                        .append( cache.size() ).append(",origSize=").append(originalSize).append( ",cpo=" ).append( cpo )
                        .append(",cco=").append( cco ).append(" took ms=").
                                append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.updateCounterpartyOrganizationCreditMode : Exception. cpo=" + cpo + ",cco=" + cco, e );
        }
    }

    public void updateProviderOrganizationCreditMode( Organization cpo )
    {
        try
        {
            long t0 = System.nanoTime();
            int originalSize = cache.size();
            cache.updateProviderOrganizationEntriesForCreditMode(cpo);
            if ( log.isDebugEnabled() )
            {
                log.debug( new StringBuilder( 100 ).append("CUC.updateProviderOrganizationCreditMode : cache size=")
                        .append(cache.size()).append( ",origSize=" ).append( originalSize ).append(",cpo=").append( cpo )
                        .append(" took ms=").append(String.format("%1.4f", ((double) (System.nanoTime() - t0)) / 1000000)).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUC.updateProviderOrganizationCreditMode : Exception. cpo=" + cpo, e );
        }
    }

    public Set<CurrencyPair> getSubscribedCurrencyPairs( Organization cpo, TradingParty cc )
    {
        Set<CurrencyPair> ccyPairs = null;
        String subscriptionKey = getCreditLimitSubscriptionCacheKey( CreditUtilC.getCreditProviderLegalEntity( cpo ), cc );
        CreditRelationSubscriptionInfo si = subscriptionMap.get( subscriptionKey );
        if ( si != null )
        {
            FXDealingLimitCollection fxDlc = ( FXDealingLimitCollection ) si.getDealingLimitCollection();
            if ( fxDlc != null )
            {
                Collection<DealingLimit> dlc = fxDlc.getDealingLimits();
                if ( dlc != null )
                {
                    ccyPairs = new HashSet<CurrencyPair>();
                    for ( DealingLimit dl : dlc )
                    {
                        FXDealingLimit fxDl = (FXDealingLimit) dl;
                        ccyPairs.add(CurrencyFactory.getCurrencyPair(fxDl.getBaseCurrency(), fxDl.getVariableCurrency()));
                    }
                }
            }
        }
        return ccyPairs;
    }

    /**
     * Returns the dealing limit collection for the relation subscription.
     *
     * @param key cache key
     * @return dlc
     */
    private FXDealingLimitCollection getDealingLimitCollectionFromSubscriptionMap( String key )
    {
        if ( key != null )
        {
            CreditRelationSubscriptionInfo si = subscriptionMap.get( key );
            if ( si != null )
            {
                return ( FXDealingLimitCollection ) si.getDealingLimitCollection();
            }
        }
        return null;
    }

    /**
     * Returns the cache key used to store the credit utilization cache entry.
     *
     * @param cpl      credit provider legal entity
     * @param creditTp credit trading party
     * @param date     date
     * @return cache key
     */
    public static String getCacheKey( LegalEntity cpl, Organization ccOrg, TradingParty creditTp, IdcDate date )
    {
        return getCacheKey( cpl.getOrganization().getShortName(), ccOrg.getShortName(), creditTp!=null?creditTp.getObjectID():null, date);
    }

    public static String getCacheKey( String cpo, String cco, Long creditTpObjectId, IdcDate date)
    {
        String dateStr = date!=null?date.getFormattedDate(IdcDate.DD_MMM_YYYY_HYPHEN):null;
        boolean isTPId = creditTpObjectId != null;
        // key is formed from the credit provider org shortname, settlement date and (Credit TPObjectId or Credit Counterparty org shortname)
        return new StringBuilder( 50 ).append( cpo ).append ( CACHE_KEY_DELIMITER ).append( dateStr )
                .append( isTPId ? CACHE_KEY_TPID_DELIMITER : CACHE_KEY_DELIMITER )
                .append( isTPId ? creditTpObjectId : cco ).toString();
    }

    /**
     * Returns the cache key for aggregate credit utilization cache.
     *
     * @param cpo credit provider organization
     * @return cache key
     */
    private String getAggregateCreditUtilizationCollectionCacheKey( Organization cpo )
    {
        return cpo.getShortName() + cpo.getObjectID();
    }

    public static String getAggregateCreditUtilizationCacheKey( String cpoShortName, String ccoShortName, Long ccObjectId )
    {
        StringBuilder sb = new StringBuilder( 50 );
        sb.append( cpoShortName ).append( LIMIT_SUBSCRIPTION_KEY_DELIMITER ).append(ccoShortName);
        if(ccObjectId != null){
            sb.append( LIMIT_SUBSCRIPTION_KEY_DELIMITER ).append( ccObjectId );
        }
        return sb.toString();
    }
    /**
     * Returns the cache key used to store the credit limit subscription.
     *
     * @param cpl credit provider legal entity
     * @param cc  credit cpty
     * @return cache key
     */
    private String getCreditLimitSubscriptionCacheKey( LegalEntity cpl, TradingParty cc )
    {
        return cc != null ? new StringBuilder( 100 ).append( cpl.getShortName() ).append( LIMIT_SUBSCRIPTION_KEY_DELIMITER ).append( cc.getObjectId() ).toString() : null;
    }

    private class CreditUtilizationCacheSystem
    {
        private final ConcurrentMap<String, CreditUtilizationCacheEntry> cceMap = new ConcurrentHashMap<String, CreditUtilizationCacheEntry> ( );

        public void put( String key, CreditUtilizationCacheEntry cce )
        {
            if ( key != null && cce != null )
            {
                cceMap.put ( key, cce );
            }
            else
            {
                log.info ("CUC.put : either key or cce is null. key=" + key + ",cce=" + cce );
            }
        }

        public CreditUtilizationCacheEntry get( String key )
        {
            return key != null ? cceMap.get ( key ) : null;
        }

        public void removeAll()
        {
            cceMap.clear ();
        }

        void removeAllObsoleteCacheEntries()
        {
            final IdcDate currentDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            Collection<String> removedKeys = new HashSet<String>();
            Collection<String> keys = cceMap.keySet ();
            for ( String key: keys )
            {
                CreditUtilizationCacheEntry cce = cceMap.get ( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null && cce.getDate() != null && cce.getDate().isEarlierThan( currentDate ) )
                    {
                        cceMap.remove( key );
                        removedKeys.add( key );
                    }
                }
            }
            log.info( "CUC.removeAllObsoleteCacheEntries : Removed cache entry keys=" + removedKeys + ",currentTradeDate=" + currentDate );
        }


        void flushAndReloadAllUtilizations(String cpoShortName)
        {
            boolean cuLookupSpacesEnabled = CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationLookupSpacesEnabled(cpoShortName);
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get ( key );
                if ( cce != null )
                {
                    Organization cpo = cce.getCreditProviderOrganization();
                    if(cpoShortName!=null && !cpoShortName.equals( cpo.getShortName()))
                    {
                        continue;
                    }
                    Collection<CreditUtilization> oldCus = cce.getCreditUtilizations();
                    if(oldCus!=null && !oldCus.isEmpty())
                    {
                        Collection<CreditUtilization> freshCus = new ArrayList<CreditUtilization>(oldCus.size());
                        for(CreditUtilization cu: oldCus)
                        {
                            try{
                                CreditUtilization freshCu;
                                if(cuLookupSpacesEnabled)
                                {
                                    IdcDate date = null;
                                    if(cu instanceof DailyCreditUtilization){
                                        date = ((DailyCreditUtilization)cu).getDate();
                                    }
                                    freshCu = CreditDataQueryServiceFactory.getCreditReferenceDataService().getCreditUtilizationForDate(cu.getCreditLimitRule(), date);
                                }
                                else
                                {
                                    freshCu = CreditDataQueryServiceFactory.getCreditDataQueryService().getCreditUtilizationFromOracle( cu );
                                }
                                if(freshCu!=null){
                                    freshCus.add( freshCu );
                                }else{
                                    log.error( "CUC.flushAndReloadAllUtilizations : error alternate CU not found for CU="+cu);
                                }
                            }catch(Exception ex){
                                log.error( "CUC.flushAndReloadAllUtilizations : error reloading CU="+cu, ex );
                            }
                        }
                        cce.setCreditUtilizations(freshCus);
                    }
                }
                else
                {
                    log.error( "CUC.flushAndReloadAllUtilizations : CCE is null for key="+key);
                }
            }
        }


        void updateAllForExemptionList ( CurrencyPairGroup cpg )
        {
            if ( cpg == null )
            {
                log.info( "CUC.updateAllForExemptionList : null currency pair group" );
                return;
            }
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get ( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        CurrencyPairGroup currencyPairGroup = cce.getExemptedCurrencyPairGroup();
                        if ( currencyPairGroup != null && cpg.isSameAs( currencyPairGroup ) )
                        {
                            cce.setExemptedCurrencyPairGroup( CreditUtilC.getExemptCurrencyPairGroup( cclr ) );
                        }
                    }
                }
            }
        }

        void removeProviderOrganizationEntries ( Organization cpo )
        {
            int count = 0;
            Collection<String> keys = cceMap.keySet ();
            for ( String key: keys )
            {
                CreditUtilizationCacheEntry cce = cceMap.get ( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        if ( cclr.getRuleSet().getNamespace().isSameAs( cpo.getNamespace() ) )
                        {
                            cceMap.remove( key );
                            count++;
                            continue;
                        }
                    }
                    else if ( cce.getCreditProviderOrganization() != null && cpo.isSameAs( cce.getCreditProviderOrganization() ) )
                    {
                        cceMap.remove( key );
                        count++;
                        continue;
                    }

                    if ( key.startsWith ( cpo.getShortName () + CACHE_KEY_DELIMITER ))
                    {
                        cceMap.remove( key );
                        count++;
                    }
                }
            }

            cceMap.remove ( getAggregateCreditUtilizationCollectionCacheKey ( cpo ) );
            log.info ( "CUC.removeProviderOrganizationEntries - removed entries for cpo=" + cpo + ",cceSize=" + count );
        }

        void updateProviderOrganizationEntriesForExemptionList ( Organization cpo )
        {
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        if ( cclr.getRuleSet().getNamespace().isSameAs( cpo.getNamespace() ) )
                        {
                            cce.setExemptedCurrencyPairGroup( CreditUtilC.getExemptCurrencyPairGroup( cclr ) );
                        }
                    }
                }
            }
        }

        private void removeCounterpartyOrganizationEntries ( Organization cpo, Organization cco )
        {
            int count = 0;
            Collection<String> tradingPartyObjectIds = new HashSet<String> ();
            Collection<TradingParty> tradingParties = cpo.getTradingParties ();
            for ( TradingParty tp: tradingParties )
            {
                if ( cco.isSameAs ( tp.getLegalEntityOrganization () ) )
                {
                    tradingPartyObjectIds.add ( String.valueOf ( tp.getObjectID () ) );
                }
            }

            Collection<String> keys = cceMap.keySet ();
            for ( String key: keys )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        if ( cclr.getRuleSet().getNamespace().isSameAs( cpo.getNamespace() ) && cco.isSameAs( cclr.getTradingPartyOrganization() ) )
                        {
                            log.info( "CUC.removeCounterpartyOrganizationEntries - removing cache key=" + key + ",cpo=" + cpo + ",cco=" + cco );
                            cceMap.remove( key );
                            count++;
                            continue;
                        }
                    }
                    else if ( cce.getCreditProviderOrganization() != null && cpo.isSameAs( cce.getCreditProviderOrganization() ) )
                    {
                        log.info( "CUC.removeCounterpartyOrganizationEntries - removing cache key=" + key + ",cpo=" + cpo + ",cco=" + cco );
                        cceMap.remove( key );
                        count++;
                        continue;
                    }

                    // handle special case of deleted cclrs in case of re-initialize workflow.
                    log.info( "CUC.removeCounterpartyOrganizationEntries - cache key=" + key + ",cpo=" + cpo + ",cco=" + cco );
                    if ( key.startsWith ( cpo.getShortName () + CACHE_KEY_DELIMITER ) )
                    {
                        if ( key.endsWith ( CACHE_KEY_DELIMITER + cco.getShortName () ) )
                        {
                            log.info ( "CUC.removeCounterpartyOrganizationEntries -  removed cpty org level entries for cpo=" + cpo + ",cco=" + cco + ",cclr=" + cclr + ",key=" + key );
                            cceMap.remove ( key );
                            count++;
                        }
                        else
                        {
                            for ( String tpId: tradingPartyObjectIds )
                            {
                                if ( key.endsWith ( CACHE_KEY_TPID_DELIMITER + tpId ) )
                                {
                                    log.info ( "CUC.removeCounterpartyOrganizationEntries - removed tp level entries for cpo=" + cpo + ",cco=" + cco + ",cclr=" + cclr + ",key=" + key );
                                    cceMap.remove ( key );
                                    count++;
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            log.info ( "CUC.removeCounterpartyOrganizationEntries - removed entries for cpo=" + cpo + ",cco=" + cco + ",cceSize=" + count );
        }

        void updateCounterpartyOrganizationEntriesForExemptionList ( Organization cpo, Organization cco )
        {
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        if ( cclr.getRuleSet().getNamespace().isSameAs( cpo.getNamespace() ) && cco.isSameAs( cclr.getTradingPartyOrganization() ) )
                        {
                            cce.setExemptedCurrencyPairGroup( CreditUtilC.getExemptCurrencyPairGroup( cclr ) );
                        }
                    }
                }
            }
        }

        private void removeCounterpartyEntries ( Organization cpo, TradingParty cc )
        {
            Collection<String> keys = cceMap.keySet ();
            for ( String key: keys )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        if ( cclr.getRuleSet().getNamespace().isSameAs( cpo.getNamespace() ) && cc.isSameAs( cclr.getTradingParty() ) )
                        {
                            cceMap.remove( key );
                        }
                    }
                    else if ( cce.getCreditProviderOrganization() != null && cpo.isSameAs( cce.getCreditProviderOrganization() ) )
                    {
                        cceMap.remove( key );
                    }
                }
            }
        }

        void updateCounterpartyEntriesForExemptionList ( Organization cpo, TradingParty cc )
        {
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        if ( cclr.getRuleSet().getNamespace().isSameAs( cpo.getNamespace() ) && cc.isSameAs( cclr.getTradingParty() ) )
                        {
                            cce.setExemptedCurrencyPairGroup( CreditUtilC.getExemptCurrencyPairGroup( cclr ) );
                        }
                    }
                }
            }
        }

        void updateCounterpartyEntriesForTenorProfile ( Organization cpo, TradingParty cc )
        {
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        if ( cclr.getRuleSet().getNamespace().isSameAs( cpo.getNamespace() ) && cc.isSameAs( cclr.getTradingParty() ) )
                        {
                            cce.initTenorCoefficientsForAllCurrencyPairs();
                        }
                    }
                }
            }
        }

        void updateCounterpartyOrganizationEntriesForTenorProfile ( Organization cpo, Organization cco )
        {
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        if ( cclr.getRuleSet().getNamespace().isSameAs( cpo.getNamespace() ) && cco.isSameAs( cclr.getTradingPartyOrganization() ) )
                        {
                            cce.initTenorCoefficientsForAllCurrencyPairs();
                        }
                    }
                }
            }
        }

        void updateProviderOrganizationEntriesForTenorProfile ( Organization cpo )
        {
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        if ( cclr.getRuleSet().getNamespace().isSameAs( cpo.getNamespace() ) )
                        {
                            cce.initTenorCoefficientsForAllCurrencyPairs();
                        }
                    }
                }
            }
        }

        void updateAllCreditTenorProfiles ()
        {
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    cce.initTenorCoefficientsForAllCurrencyPairs();
                }
            }
        }

        void updateCounterpartyEntriesForCreditMode ( Organization cpo, TradingParty cc )
        {
            Collection<CreditUtilization> creditUtils = new HashSet<CreditUtilization>();
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null && CreditLimit.CREDIT_INTEGRATION_MODE == CreditUtilC.getCreditMode( cclr ) )
                    {
                        if ( cclr.getRuleSet().getNamespace().isSameAs( cpo.getNamespace() ) && cc.isSameAs( cclr.getTradingParty() ) )
                        {
                            creditUtils.addAll( cce.getCreditUtilizations() );
                        }
                    }
                }
            }

            // go through all the credit utilizations and mark the earmarked amount as zero.
            for ( CreditUtilization cu : creditUtils )
            {
                cu.resetEarmarkedUsedAmount();
            }
        }

        void updateCounterpartyOrganizationEntriesForCreditMode ( Organization cpo, Organization cco )
        {
            Collection<CreditUtilization> creditUtils = new HashSet<CreditUtilization>();
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null && CreditLimit.CREDIT_INTEGRATION_MODE == CreditUtilC.getCreditMode( cclr ) )
                    {
                        if ( cclr.getRuleSet().getNamespace().isSameAs( cpo.getNamespace() ) && cco.isSameAs( cclr.getTradingPartyOrganization() ) )
                        {
                            creditUtils.addAll( cce.getCreditUtilizations() );
                        }
                    }
                }
            }

            // go through all the credit utilizations and mark the earmarked amount as zero.
            for ( CreditUtilization cu : creditUtils )
            {
                cu.resetEarmarkedUsedAmount();
            }
        }

        void updateProviderOrganizationEntriesForCreditMode ( Organization cpo )
        {
            Collection<CreditUtilization> creditUtils = new HashSet<CreditUtilization>();
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null && CreditLimit.CREDIT_INTEGRATION_MODE == CreditUtilC.getCreditMode( cclr ) )
                    {
                        if ( cclr.getRuleSet().getNamespace().isSameAs( cpo.getNamespace() ) )
                        {
                            creditUtils.addAll( cce.getCreditUtilizations() );
                        }
                    }
                }
            }

            // go through all the credit utilizations and mark the earmarked amount as zero.
            for ( CreditUtilization cu : creditUtils )
            {
                cu.resetEarmarkedUsedAmount();
            }
        }

        void removeCounterpartyOrganizationNonAggregateEntries ( Organization cpo, Organization cco )
        {
            Collection<String> keys = cceMap.keySet ();
            for ( String key: keys )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        if ( cclr.getRuleSet().getNamespace().isSameAs( cpo.getNamespace() ) && cco.isSameAs( cclr.getTradingPartyOrganization() ) )
                        {
                            cceMap.remove( key );
                        }
                    }
                }
            }
        }

        void resetAll ()
        {
            Set<CreditUtilization> creditUtils = new HashSet<CreditUtilization>( size() );
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get ( key );
                Collection<CreditUtilization> col = cce.getCreditUtilizations();
                if ( col != null )
                {
                    creditUtils.addAll( cce.getCreditUtilizations() );
                }
            }

            // reset the credit utilizations in the set.
            for ( CreditUtilization creditUtil : creditUtils )
            {
                // resetting is needed only for aggregate utilizations
                if ( !( creditUtil instanceof DailyCreditUtilizationC ) )
                {
                    creditUtil.rebuildCurrencyPositions( CreditLimitConstants.RESET_CCYPOSITIONS_ENDOFDAY, false );
                }
            }
        }

        void rebuildAll ()
        {
            Set<CreditUtilization> creditUtils = new HashSet<CreditUtilization>( size() );
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get ( key );
                Collection<CreditUtilization> col = cce.getCreditUtilizations();
                if ( col != null )
                {
                    creditUtils.addAll( cce.getCreditUtilizations() );
                }
            }

            // reset the credit utilizations in the set.
            for ( CreditUtilization cu : creditUtils )
            {
                // resetting is needed only for aggregate utilizations
                if ( ! (cu instanceof DailyCreditUtilizationC) )
                {
                    cu.rebuildCurrencyPositions ( CreditLimitConstants.RESET_CCYPOSITIONS_ENDOFDAY, true );
                }
            }
        }

        void rebuildAllOptimized ()
        {
            Set<CreditUtilization> creditUtils = new HashSet<CreditUtilization>( size() );
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get ( key );
                Collection<CreditUtilization> col = cce.getCreditUtilizations();
                if ( col != null )
                {
                    creditUtils.addAll( cce.getCreditUtilizations() );
                }
            }

            // reset the credit utilizations in the set.
            final IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ();
            for ( CreditUtilization cu : creditUtils )
            {
                // resetting is needed only for aggregate utilizations
                if ( ! ( cu instanceof DailyCreditUtilizationC ) && !CreditUtilC.isCashSettlement ( cu.getCreditLimitRule () ) )
                {
                    if ( cu.isCurrencyPairPositionBased () ) // next date based optimized workflow does not work for currency pair positions.
                    {
                        cu.rebuildCurrencyPositions ( CreditLimitConstants.RESET_CCYPOSITIONS_ENDOFDAY, true );
                    }
                    else
                    {
                        boolean rebuilt = cu.rebuildCurrencyPositionsFromNextDateSnapshot ( tradeDate, CreditLimitConstants.REBUILD_CCYPOSITIONS_FROM_SNAPSHOT_ENDOFDAY, true );
                        if ( !rebuilt )
                        {
                            cu.getCurrencyPositions ().setStale ( true );
                        }
                    }
                }
            }
        }

        long rebuildAllStaleEntries ()
        {
            Set<CreditUtilization> creditUtils = new HashSet<CreditUtilization>( size() );
            Set<CreditUtilization> positionMismatchCreditUtils = new HashSet<CreditUtilization>( size() );
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get ( key );

                // handle only non-aggregate cache entries which are stale.
                if ( cce.isStale() && !cce.isAggregateCreditUtilizationCollectionCacheEntry() )
                {
                    Collection<CreditUtilization> col = cce.getCreditUtilizations();
                    if ( col != null )
                    {
                        creditUtils.addAll ( col );
                    }
                    //once it is added for rebuilding, mark the entry as non-stale.
                    cce.setStale( false );
                }
                else
                {
                    Collection<CreditUtilization> col = cce.getCreditUtilizations();
                    if ( col != null )
                    {
                        for ( CreditUtilization cu : col )
                        {
                            if ( cu.getCurrencyPositions().isStale() )
                            {
                                creditUtils.add( cu );
                            }
                            else if ( cu.getCurrencyPositions().calculateChecksum() != cu.getCurrencyPositionChecksum()  ) // rebuild credit utils that has a currency position mismatch.
                            {
                                long now = System.currentTimeMillis();
                                long stalePeriod = _configMBean.getCurrencyPositionMismatchStalePeriod();
                                boolean validPositionUpdateTime = stalePeriod > 0 && cu.getPositionLastUpdatedTime() > 0;
                                if ( validPositionUpdateTime && now - cu.getCurrencyPositions().getLastUpdatedTime() > stalePeriod && now - cu.getPositionLastUpdatedTime() > stalePeriod )
                                {
                                    log.info( new StringBuilder( 200 ).append( "CUC.rebuildAllStaleEntries : currency position mismatch for cu=" )
                                            .append ( cu ).append( ",cu.lastPosUpdate=" ).append( cu.getPositionLastUpdatedTime() )
                                            .append( ",cu.checksum=" ).append( cu.getCurrencyPositionChecksum() ).append( ",cps.checksum=" )
                                            .append( cu.getCurrencyPositions().calculateChecksum() ).append( ",cps.lastUpdated=" )
                                            .append( cu.getCurrencyPositions().getLastUpdatedTime() ).toString());
                                    creditUtils.add(cu);
                                    positionMismatchCreditUtils.add ( cu );
                                }
                            }
                        }
                    }
                }
            }

            // now outside of sync block we need rebuild the credit utils.
            for ( CreditUtilization cu : creditUtils )
            {
                if ( !CreditUtilC.isCashSettlement ( cu.getCreditLimitRule () ))
                {
                    cu.rebuildCurrencyPositions ( CreditLimitConstants.RESET_CACHE_UPDATE_STALE, true );
                }
            }

            try
            {
                if ( ! positionMismatchCreditUtils.isEmpty () && EndOfDayServiceFactory.getEndOfDayService ().getLocalEndOfDayServer ().isEndOfBusinessDayEnabled () )
                {
                    Map<CounterpartyCreditLimitRule, Collection<CreditUtilization>> map = new HashMap<CounterpartyCreditLimitRule, Collection<CreditUtilization>> ( positionMismatchCreditUtils.size () );
                    for ( CreditUtilization cu : positionMismatchCreditUtils )
                    {
                        CounterpartyCreditLimitRule cclr = cu.getCreditLimitRule () != null ? ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule ().getParentRule () : null;
                        if ( cclr != null )
                        {
                            Collection<CreditUtilization> set = map.get ( cclr );
                            if ( set == null )
                            {
                                set = new HashSet<CreditUtilization> ();
                                map.put ( cclr, set );
                            }
                            set.add ( cu );
                        }
                    }
                    for ( CounterpartyCreditLimitRule cclr : map.keySet () )
                    {
                        Collection<CreditUtilization> cuCol = map.get ( cclr );
                        log.info ( "CUC.rebuildAllStaleEntries : Adding to persistence task the position mismatched credit utils. cclr=" + cclr + ",utils=" + cuCol );
                        CreditUtilizationManagerC.getInstance ().getCreditUtilizationPersistenceTask ().addCreditUtilizations ( cclr, cuCol, false );
                    }
                }
            }
            catch ( Exception e )
            {
                log.error ( "CUC.rebuildAllStaleEntries : exception while persisting recalculated ", e );
            }

            return creditUtils.size();
        }

        void rebuildAllWithDelay ( long delaySeed )
        {
            Set<CreditUtilization> creditUtils = new HashSet<CreditUtilization>( size() );
            Set<CreditUtilizationCacheEntry> cces = new HashSet<CreditUtilizationCacheEntry>( size() );
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get ( key );
                if ( cce != null )
                {
                    cces.add( cce );
                }
            }

            // iterate through the entries and rebuild the aggregate credit utilization
            for ( CreditUtilizationCacheEntry cce : cces )
            {
                try
                {
                    Collection<CreditUtilization> cus = cce.getCreditUtilizations();
                    for ( CreditUtilization cu : cus )
                    {
                        if ( !( cu instanceof DailyCreditUtilization ) && !creditUtils.contains( cu ) && !CreditUtilC.isCashSettlement ( cu.getCreditLimitRule () ) )
                        {
                            // add random delay
                            long delay = 0;
                            if ( delaySeed > 0 )
                            {
                                delay = ( long ) ( Math.random() * delaySeed );
                                if ( delay > 0 )
                                {
                                    Thread.sleep( delay );
                                }
                            }

                            long t0 = System.currentTimeMillis();
                            cu.rebuildCurrencyPositions( CreditLimitConstants.RESET_CCYPOSITIONS_ENDOFDAY, false );
                            log.warn( new StringBuilder( 200 ).append( "CUC.rebuildAllWithDelay : rebuilt positions with delay=" )
                                    .append( delay ).append( ",rebuildTime=" ).append( System.currentTimeMillis() - t0 )
                                    .append( ",cu=" ).append( cu ).toString() );
                            creditUtils.add( cu );
                        }
                    }
                    cce.setStale( false );
                }
                catch ( Exception e )
                {
                    log.error( "CUC.rebuildAllWithDelay : Exception while rebuilding cache with delay", e );
                }
            }
        }

        void setAllStale ()
        {
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get ( key );
                if ( cce != null )
                {
                    cce.setStale( true );
                }
            }
        }

        void resetProviderOrganizationEntries ( Organization cpo )
        {
            Set<CreditUtilization> creditUtils = new HashSet<CreditUtilization>( size() );
            Collection<String> keys = cceMap.keySet ();
            for ( String key: keys )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        if ( cclr.getRuleSet().getNamespace().isSameAs( cpo.getNamespace() ) )
                        {
                            Collection<CreditUtilization> col = cce.getCreditUtilizations();
                            if ( col != null )
                            {
                                creditUtils.addAll( cce.getCreditUtilizations() );
                            }
                        }
                    }
                    else if ( cce.getCreditProviderOrganization() != null && cpo.isSameAs( cce.getCreditProviderOrganization() ) )
                    {
                        cceMap.remove( key );
                    }
                }
            }

            // reset the credit utilization in the set.
            for ( CreditUtilization creditUtil : creditUtils )
            {
                creditUtil.rebuildCurrencyPositions( CreditLimitConstants.RESET_CACHE_PROVIDER_LEVEL, true );
            }
        }

        void resetCounterpartyOrganizationEntries ( Organization cpo, Organization cco )
        {
            Set<CreditUtilization> creditUtils = new HashSet<CreditUtilization>( size() );
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        CreditLimitRuleSet clrs = ( CreditLimitRuleSet ) cclr.getRuleSet();
                        if ( cpo.isSameAs( clrs.getOrganization() ) && cco.isSameAs( cclr.getTradingPartyOrganization() ) )
                        {
                            Collection<CreditUtilization> col = cce.getCreditUtilizations();
                            if ( col != null )
                            {
                                creditUtils.addAll( cce.getCreditUtilizations() );
                            }
                        }
                    }
                }
            }

            // reset the credit utilizations in the set.
            for ( CreditUtilization creditUtil : creditUtils )
            {
                creditUtil.rebuildCurrencyPositions( CreditLimitConstants.RESET_CACHE_CPTY_ORG_LEVEL, true );
            }

        }

        void resetCounterpartyEntries ( Organization cpo, TradingParty cc )
        {
            Set<CreditUtilization> creditUtils = new HashSet<CreditUtilization>( size() );
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        CreditLimitRuleSet clrs = ( CreditLimitRuleSet ) cclr.getRuleSet();
                        if ( cpo.isSameAs( clrs.getOrganization() ) && cc.isSameAs( cclr.getTradingParty() ) )
                        {
                            Collection<CreditUtilization> col = cce.getCreditUtilizations();
                            if ( col != null )
                            {
                                creditUtils.addAll( cce.getCreditUtilizations() );
                            }
                        }
                    }
                }
            }

            // reset the credit utilizations in the set.
            for ( CreditUtilization creditUtil : creditUtils )
            {
                creditUtil.rebuildCurrencyPositions( CreditLimitConstants.RESET_CACHE_TP_LEVEL, true );
            }

        }

        void rebuildProviderOrganizationEntries ( Organization cpo, String event )
        {
            Set<CreditUtilization> creditUtils = new HashSet<CreditUtilization>( size() );
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        if ( cclr.getRuleSet().getNamespace().isSameAs( cpo.getNamespace() ) )
                        {
                            Collection<CreditUtilization> col = cce.getCreditUtilizations();
                            if ( col != null )
                            {
                                creditUtils.addAll( cce.getCreditUtilizations() );
                            }
                            cce.update( event );
                        }
                    }
                    else if ( cce.getCreditProviderOrganization() != null && cpo.isSameAs( cce.getCreditProviderOrganization() ) )
                    {
                        Collection<CreditUtilization> col = cce.getCreditUtilizations();
                        if ( col != null )
                        {
                            creditUtils.addAll( cce.getCreditUtilizations() );
                        }
                        cce.update( event );
                    }
                }
            }

            // reset the credit utilizations in the set.
            for ( CreditUtilization creditUtil : creditUtils )
            {
                creditUtil.rebuildCurrencyPositions( CreditLimitConstants.REBUILD_CACHE_PROVIDER_LEVEL + event, true );
            }
        }

        void rebuildCounterpartyOrganizationEntries ( Organization cpo, Organization cco, String event )
        {
            Set<CreditUtilization> creditUtils = new HashSet<CreditUtilization>( size() );
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule();
                    if ( cclr != null )
                    {
                        CreditLimitRuleSet clrs = ( CreditLimitRuleSet ) cclr.getRuleSet();
                        if ( cpo.isSameAs( clrs.getOrganization() ) && cco.isSameAs( cclr.getTradingPartyOrganization() ) )
                        {
                            Collection<CreditUtilization> col = cce.getCreditUtilizations();
                            if ( col != null )
                            {
                                creditUtils.addAll( cce.getCreditUtilizations() );
                            }
                            cce.update( event );
                        }
                    }
                    else if ( cce.getCreditProviderOrganization() != null && cpo.isSameAs( cce.getCreditProviderOrganization() ) )
                    {
                        Collection<CreditUtilization> col = cce.getCreditUtilizations();
                        if ( col != null )
                        {
                            for ( CreditUtilization cu : col )
                            {
                                CounterpartyCreditLimitRule cptyRule = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule().getParentRule();
                                if ( cptyRule != null && cco.isSameAs( cptyRule.getTradingPartyOrganization() ) )
                                {
                                    creditUtils.add( cu );
                                }
                            }
                        }
                        cce.update( event );
                    }
                }
            }

            // reset the credit utilizations in the set.
            for ( CreditUtilization creditUtil : creditUtils )
            {
                creditUtil.rebuildCurrencyPositions( CreditLimitConstants.REBUILD_CACHE_CPTY_ORG_LEVEL + event, true );
            }
        }

        void rebuildCounterpartyEntries ( Organization cpo, TradingParty cc, String event )
        {
            Set<CreditUtilization> creditUtils = new HashSet<CreditUtilization>( size() );
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get ( key );
                if ( cce != null )
                {
                    CounterpartyCreditLimitRule cclr = cce.getCounterpartyCreditLimitRule ();
                    if ( cclr != null )
                    {
                        CreditLimitRuleSet clrs = ( CreditLimitRuleSet ) cclr.getRuleSet ();
                        if ( cpo.isSameAs ( clrs.getOrganization () ) && cc.isSameAs ( cclr.getTradingParty () ) )
                        {
                            Collection<CreditUtilization> col = cce.getCreditUtilizations ();
                            if ( col != null )
                            {
                                creditUtils.addAll ( cce.getCreditUtilizations () );
                            }
                            cce.update ( event );
                        }
                    }
                    else if ( cce.getCreditProviderOrganization () != null && cpo.isSameAs ( cce.getCreditProviderOrganization () ) )
                    {
                        Collection<CreditUtilization> col = cce.getCreditUtilizations ();
                        if ( col != null )
                        {
                            for ( CreditUtilization cu : col )
                            {
                                CounterpartyCreditLimitRule cptyRule = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule ().getParentRule ();
                                if ( cptyRule != null && cc.isSameAs ( cptyRule.getTradingParty () ) )
                                {
                                    creditUtils.add ( cu );
                                }
                            }
                        }
                        cce.update ( event );
                    }
                }
            }

            // reset the credit utilizations in the set.
            for ( CreditUtilization creditUtil : creditUtils )
            {
                creditUtil.rebuildCurrencyPositions( CreditLimitConstants.REBUILD_CACHE_TP_LEVEL + event, true );
            }
        }

        public Collection<CreditUtilization> getAllCreditUtilizations()
        {
            Set<CreditUtilization> creditUtils = new HashSet<CreditUtilization>( );
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get ( key );
                Collection<CreditUtilization> col = cce.getCreditUtilizations();
                if ( col != null )
                {
                    creditUtils.addAll( cce.getCreditUtilizations() );
                }
            }
            return creditUtils;
        }

        Collection<CreditUtilization> getAllAggregateCreditUtilizations ( boolean onlyProviderCacheEntries )
        {
            Set<CreditUtilization> creditUtils = new HashSet<CreditUtilization>( size() );
            for ( String key: cceMap.keySet () )
            {
                CreditUtilizationCacheEntry cce = cceMap.get ( key );
                if ( cce.isAggregateCreditUtilizationCollectionCacheEntry() )
                {
                    Collection<CreditUtilization> col = cce.getCreditUtilizations();
                    if ( col != null )
                    {
                        creditUtils.addAll( cce.getCreditUtilizations() );
                    }
                }
                else if ( !onlyProviderCacheEntries )
                {
                    for ( CreditUtilization cu : cce.getCreditUtilizations() )
                    {
                        if ( cu.getCreditLimitRule() instanceof SingleCreditLimitRule )
                        {
                            creditUtils.add( cu );
                        }
                    }
                }
            }
            return creditUtils;
        }

        public int size()
        {
            return cceMap.size ();
        }

        Collection<CreditUtilizationCacheEntry> getAllCreditUtilizationCacheEntries ()
        {
            return cceMap.values ();
        }
    }

    public void clearAllSubscriptionData()
    {
        subscriptionMap.clear();
        creditEnableMap.clear();
    }

    public Collection<CounterpartyCreditLimitRule> getAutoStopOutEnabledRules()
    {
        Collection<CounterpartyCreditLimitRule> cclrs = null;
        synchronized ( autoStopOutRulesEnabledMapLock )
        {
            if ( stopOutEnabledCclrMap == null )
            {
                stopOutEnabledCclrMap = initAutoStopOutEnabledRules ();
            }
            if ( stopOutEnabledCclrMap != null )
            {
                cclrs = new ArrayList<CounterpartyCreditLimitRule> ();
                for ( Collection<CounterpartyCreditLimitRule> coll : stopOutEnabledCclrMap.values () )
                {
                    if ( coll != null && !coll.isEmpty () )
                    {
                        cclrs.addAll ( coll );
                    }
                }
            }
        }
        return cclrs;
    }

    public void updateAutoStopOutEnabledRules( CounterpartyCreditLimitRule cclr )
    {
        if ( cclr.isActive () && CreditUtilC.isAutoStopOutEnabled ( cclr ) )
        {
            synchronized ( autoStopOutRulesEnabledMapLock )
            {
                if ( stopOutEnabledCclrMap == null )
                {
                    stopOutEnabledCclrMap = initAutoStopOutEnabledRules ();
                }
                if ( stopOutEnabledCclrMap != null )
                {
                    Organization cpo = CreditUtilC.getCreditProviderOrganization ( ( CreditLimitRuleSet ) cclr.getRuleSet () );
                    Collection<CounterpartyCreditLimitRule> rules = stopOutEnabledCclrMap.get ( cpo );
                    if ( rules == null )
                    {
                        rules = new ConcurrentSkipListSet<CounterpartyCreditLimitRule> ();
                        stopOutEnabledCclrMap.put ( cpo, rules );
                    }
                    rules.add ( cclr );
                }
            }
        }
    }

    private ReentrantLock getSubscriptionLock( String key )
    {
        ReentrantLock lock = subscriptionLockMap.get( key );
        if ( lock == null )
        {
            lock = new ReentrantLock();
            ReentrantLock existingLock = subscriptionLockMap.putIfAbsent( key, lock );
            if ( existingLock != null )
            {
                lock = existingLock;
            }
        }
        return lock;
    }

    public void rebuildAutoStopOutEnabledRules()
    {
        synchronized ( autoStopOutRulesEnabledMapLock )
        {
            // rebuild only if the cache is already populated.
            if ( stopOutEnabledCclrMap != null )
            {
                stopOutEnabledCclrMap = initAutoStopOutEnabledRules ();
            }
        }
    }

    /**
     * for dev-app utilities
     * @return map of credit provider and their credit relationships.
     */
    public Map<Organization, Map<TradingParty, CreditRelationship>> getCounterpartyCreditLimitRuleMap()
    {
        return cclrMap;
    }

    private ConcurrentMap<Organization, Collection<CounterpartyCreditLimitRule>> initAutoStopOutEnabledRules()
    {
        if ( !CreditUtilC.isMarginRevaluationEnabled () )
        {
            return null;
        }
        ConcurrentMap<Organization, Collection<CounterpartyCreditLimitRule>> map = new ConcurrentHashMap<Organization, Collection<CounterpartyCreditLimitRule>> ();
        Collection<CounterpartyCreditLimitRule> cclrs = CreditUtilC.getAutoStopOutEnabledRules ( null );
        if ( cclrs != null && !cclrs.isEmpty () )
        {
            for ( CounterpartyCreditLimitRule cclr: cclrs )
            {
                if ( cclr.isActive () && CreditUtilC.isAutoStopOutEnabled ( cclr ) )
                {
                    Organization cpo = CreditUtilC.getCreditProviderOrganization ( ( CreditLimitRuleSet ) cclr.getRuleSet () );
                    Collection<CounterpartyCreditLimitRule> rules = map.get ( cpo );
                    if ( rules == null )
                    {
                        rules = new ConcurrentSkipListSet<CounterpartyCreditLimitRule> ();
                        map.put ( cpo, rules );
                    }
                    rules.add( cclr );
                }
            }
        }
        return map;
    }
}

