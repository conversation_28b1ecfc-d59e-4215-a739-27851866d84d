package com.integral.finance.creditLimit.handler;

import com.integral.exception.IdcException;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.creditLimit.admin.CreditAdminServiceLoggerC;
import com.integral.finance.creditLimit.audit.CreditLimitAdminAuditManagerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.session.IdcTransaction;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.IdcUtilC;

import java.util.Vector;

/**
 * <AUTHOR> Development Corporation.
 */

public class CreditRejectionAuditReleaseHandler implements MessageHandler, Runnable
{
    protected static Log log = LogFactory.getLog( CreditAdminServiceLoggerC.class );
    private static Vector readOnlyClasses = new Vector( 20 );


    /**
     * Instance variable for credit rejection audit facade creation
     */
    Organization creditProvider;
    TradingParty creditCpty;
    Organization creditCptyOrg;
    String tradeDescription;
    User user;
    boolean warmup;

    static
    {
        setReadOnlyClasses();
    }

    private CreditRejectionAuditReleaseHandler()
    {
    }

    public CreditRejectionAuditReleaseHandler( Organization provider, TradingParty creditCpty, Organization creditCptyOrg, String tradeDescription, User usr, boolean isWarmup )
    {
        this();
        this.creditProvider = provider;
        this.creditCpty = creditCpty;
        this.creditCptyOrg = creditCptyOrg;
        this.tradeDescription = tradeDescription;
        this.user = usr;
        this.warmup = isWarmup;
    }

    /*
        Starts a transaction and persists the credit rejection audit event.
    */
    public Message handle( Message message ) throws IdcException
    {
        IdcTransaction tx = null;
        if ( log.isDebugEnabled() )
        {
            log.debug( "CRAR.handle.INFO : Audit credit rejection event. " + this );
        }
        try
        {
            if ( !warmup )
            {
                User contextUser = user != null ? user : creditProvider.getDefaultDealingUser();
                if ( contextUser != null )
                {
                    IdcUtilC.setSessionContextUser( contextUser );
                    tx = CreditUtilC.startTransaction( readOnlyClasses );
                    CreditLimitAdminAuditManagerC.getInstance().auditCreditRejection( creditProvider, creditCpty, creditCptyOrg, tradeDescription );
                    CreditUtilC.endTransaction( tx );
                    tx = null;
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "CRAR.handle ; Audit credit rejection event. " + this );
        }
        finally
        {
            if ( tx != null )
            {
                tx.release();
            }
        }
        return message;
    }

    public void run()
    {
        try
        {
            handle( null );
        }
        catch ( Exception e )
        {
            log.error( "CRAR.run : Exception. cpo=" + creditProvider + ",cco=" + creditCptyOrg + ",cc=" + creditCpty, e );
        }
    }

    public String toString()
    {
        return new StringBuilder( 100 ).append( "creditProvider=" ).append( creditProvider ).append( "cpty=" ).append( creditCpty ).
                append( ",cptyOrg=" ).append( creditCptyOrg ).append( ",tradeDescription=" ).append( tradeDescription ).toString();
    }

    /**
     * Sets the readonly classes.
     */
    private static void setReadOnlyClasses()
    {
        readOnlyClasses.add( com.integral.finance.creditLimit.CreditLimitRuleSetC.class );
        readOnlyClasses.add( com.integral.finance.creditLimit.CreditLimitClassificationC.class );
        readOnlyClasses.add( com.integral.finance.creditLimit.CreditLimitWorkflowStateC.class );
        readOnlyClasses.add( com.integral.user.UserC.class );
        readOnlyClasses.add( com.integral.user.OrganizationC.class );
        readOnlyClasses.add( com.integral.persistence.NamespaceC.class );
        readOnlyClasses.add( com.integral.workflow.StateC.class );
        readOnlyClasses.add( com.integral.finance.counterparty.TradingPartyC.class );
        readOnlyClasses.add( com.integral.finance.counterparty.LegalEntityC.class );
        readOnlyClasses.add( com.integral.finance.fx.FXPaymentParametersC.class );
        readOnlyClasses.add( com.integral.finance.fx.FXLegC.class );
        readOnlyClasses.add( com.integral.workflow.WorkflowStateMapC.class );
        readOnlyClasses.add( com.integral.finance.marketData.fx.FXMarketDataSetC.class );
        readOnlyClasses.add( com.integral.rule.RuleActionC.class );
        readOnlyClasses.add( com.integral.rule.SendEmailActionC.class );
    }
}
