package com.integral.finance.creditLimit.admin;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationFactory;
import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationMBean;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.creditLimit.CreditUtilizationC;
import com.integral.finance.creditLimit.CreditUtilizationEventC;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.persistence.PersistenceFactory;
import com.integral.time.IdcDate;
import org.eclipse.persistence.sessions.Record;
import org.eclipse.persistence.sessions.Session;

import java.math.BigDecimal;
import java.util.Vector;

/**
 * This functor class is used to remove the obsolete credit utilizations and events. Organizations are queried based on the admin configuration.
 *
 * <AUTHOR> Development Corp.
 */
public class RemoveObsoleteCreditFunctorC extends CreditFunctorC
{
    protected DealingDataPurgeConfigurationMBean purgeConfigMBean = DealingDataPurgeConfigurationFactory.getDealingDataPurgeConfigurationMBean();

    public RemoveObsoleteCreditFunctorC( String name )
    {
        setName( name );
    }

    public void execute()
    {
        try
        {
            if ( isEnabled() && creditAdminConfigMBean.isRemoveCreditEnabled() )
            {
                start();

                // do the straight sql deletions
                if ( isEnabled() && creditAdminConfigMBean.isRemoveCreditEnabled() )
                {
                    deleteOldCreditUtilEventsByDirectSql();
                }
                if ( isEnabled() && creditAdminConfigMBean.isRemoveCreditEnabled() )
                {
                    deleteOldCreditUtilsByDirectSql();
                }

                IdcDate businessDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
                IdcDate cutOffDate = businessDate.subtractDays( 1 );
                CreditUtilizationManagerC.getInstance().deleteSettledCreditUtilizationEvents( cutOffDate, Integer.MAX_VALUE, true, this );
                if ( isEnabled() && creditAdminConfigMBean.isRemoveCreditEnabled() )
                {
                    CreditUtilizationManagerC.getInstance().deleteSettledDailyCreditUtilizations( cutOffDate, Integer.MAX_VALUE, true, this );
                }
            }
            else
            {
                log.warn( "RemoveObsoleteCreditFunctorC.execute.INFO : Functor not executed. this=" + this + ",isRemoveCreditEnabled=" + creditAdminConfigMBean.isRemoveCreditEnabled() );
            }
        }
        catch ( Exception e )
        {
            log.error( "RemoveObsoleteCreditFunctorC.execute.ERROR.", e );
        }
        finally
        {
            finish();
        }

        // mark suspension as completed if suspend was initiated.
        if ( suspensionInitiated )
        {
            this.suspended = true;
        }
    }

    private void deleteOldCreditUtilEventsByDirectSql()
    {
        long t0 = System.currentTimeMillis();
        int batchCount = 0;
        int deletedCueTotalCount = 0;
        long oldRecordCount = 0;
        try
        {
            int batchSize = purgeConfigMBean.getCommitBatchSize( CreditUtilizationEventC.class.getSimpleName() );
            batchSize = batchSize > 0 ? batchSize : 10000;
            oldRecordCount = getOldCreditUtilEventCount();
            if ( oldRecordCount == 0 )
            {
                log.info( "ROCF.deleteOldCreditUtilEventsByDirectSql : There are not credit util events to remove." );
                return;
            }
            batchCount = oldRecordCount > batchSize ? ( ( int ) oldRecordCount / batchSize ) + 1 : 1;
            for ( int i = 0; i <= batchCount; i++ )
            {
                long dt = System.currentTimeMillis();
                deleteOldCreditUtilEvents( batchSize );
                deletedCueTotalCount += batchSize;
                log.info( new StringBuilder( 200 ).append( "ROCF.deleteOldCreditUtilEventsByDirectSql : Stats for batchNum=" )
                        .append( i + 1 ).append( ",deletedCount=" ).append( deletedCueTotalCount ).append( ",timeTaken=" )
                        .append( System.currentTimeMillis() - dt ).append( ",oldRecordCount=" ).append( oldRecordCount ).toString() );

                if ( !isEnabled() || !creditAdminConfigMBean.isRemoveCreditEnabled() )
                {
                    break;
                }

                long sleepDelay = purgeConfigMBean.getBatchSleepInterval();
                if ( sleepDelay > 0 )
                {
                    Thread.sleep( sleepDelay );
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "ROCF.deleteOldCreditUtilEventsByDirectSql : Exception while removing the old data", e );
        }
        log.info( "ROCF.deleteOldCreditUtilEventsByDirectSql : Removed a total of " + deletedCueTotalCount + " time taken="
                + ( System.currentTimeMillis() - t0 ) + ",batchNum=" + batchCount + ",oldRecordCount=" + oldRecordCount );
    }

    private void deleteOldCreditUtilsByDirectSql()
    {
        long t0 = System.currentTimeMillis();
        int batchCount = 0;
        int deletedCuTotalCount = 0;
        long oldRecordCount = 0;
        try
        {
            int batchSize = purgeConfigMBean.getCommitBatchSize( CreditUtilizationC.class.getSimpleName() );
            batchSize = batchSize > 0 ? batchSize : 10000;
            oldRecordCount = getOldCreditUtilCount();
            if ( oldRecordCount == 0 )
            {
                log.info( "ROCF.deleteOldCreditUtilsByDirectSql : There are not credit utils to remove." );
                return;
            }
            batchCount = oldRecordCount > batchSize ? ( ( int ) oldRecordCount / batchSize ) + 1 : 1;
            for ( int i = 0; i <= batchCount; i++ )
            {
                long dt = System.currentTimeMillis();
                deleteOldCreditUtils( batchSize );
                deletedCuTotalCount += batchSize;
                log.info( new StringBuilder( 200 ).append( "ROCF.deleteOldCreditUtilsByDirectSql : Stats for batchNum=" )
                        .append( i + 1 ).append( ",deletedCount=" ).append( deletedCuTotalCount ).append( ",timeTaken=" )
                        .append( System.currentTimeMillis() - dt ).append( ",oldRecordCount=" ).append( oldRecordCount ).toString() );

                if ( !isEnabled() || !creditAdminConfigMBean.isRemoveCreditEnabled() )
                {
                    break;
                }

                long sleepDelay = purgeConfigMBean.getBatchSleepInterval();
                if ( sleepDelay > 0 )
                {
                    Thread.sleep( sleepDelay );
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "ROCF.deleteOldCreditUtilsByDirectSql : Exception while removing the old data", e );
        }
        log.info( "ROCF.deleteOldCreditUtilsByDirectSql : Removed a total of " + deletedCuTotalCount + " time taken="
                + ( System.currentTimeMillis() - t0 ) + ",batchNum=" + batchCount + ",oldRecordCount=" + oldRecordCount );
    }

    public long getOldCreditUtilEventCount()
    {
        long recordCount = 0;
        try
        {
            long t0 = System.currentTimeMillis();
            Session session = PersistenceFactory.newSession();
            Vector orphanCount = session.executeSQL( "SELECT COUNT(*) FROM IDCCREDITUTILEVT WHERE POSSTLMTDATE < SYSDATE -1" );
            if ( orphanCount != null && !orphanCount.isEmpty() )
            {
                Record dbRecord = ( Record ) orphanCount.get( 0 );
                if ( dbRecord != null )
                {
                    BigDecimal count = ( BigDecimal ) dbRecord.get( "COUNT(*)" );
                    recordCount = count.longValue();
                    log.info( new StringBuilder( 200 ).append( "ROCF.getOldCreditUtilEventCount : Query for count took " )
                            .append( System.currentTimeMillis() - t0 ).append( ",recordCount=" ).append( recordCount ).toString() );
                    return recordCount;
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "ROCF.getOldCreditUtilEventCount : exception while getting the credit util event record count.", e );
        }
        return recordCount;
    }

    public long getOldCreditUtilCount()
    {
        long recordCount = 0;
        try
        {
            long t0 = System.currentTimeMillis();
            Session session = PersistenceFactory.newSession();
            Vector orphanCount = session.executeSQL( "SELECT COUNT(*) FROM IDCCREDITUTIL WHERE CUDATE  < SYSDATE -1 AND TYPE='IdcDailyCredUtilC'" );
            if ( orphanCount != null && !orphanCount.isEmpty() )
            {
                Record dbRecord = ( Record ) orphanCount.get( 0 );
                if ( dbRecord != null )
                {
                    BigDecimal count = ( BigDecimal ) dbRecord.get( "COUNT(*)" );
                    recordCount = count.longValue();
                    log.info( new StringBuilder( 200 ).append( "ROCF.getOldCreditUtilCount : Query for count took " )
                            .append( System.currentTimeMillis() - t0 ).append( ",recordCount=" ).append( recordCount ).toString() );
                    return recordCount;
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "ROCF.getOldCreditUtilCount : exception while getting the credit util record count.", e );
        }
        return recordCount;
    }

    public boolean deleteOldCreditUtilEvents( int batchSize )
    {
        try
        {
            Session session = PersistenceFactory.newSession();
            String deleteSql = "DELETE FROM IDCCREDITUTILEVT WHERE POSSTLMTDATE < SYSDATE -1 AND ROWNUM < " + ( batchSize + 1 );
            session.executeNonSelectingSQL( deleteSql );
        }
        catch ( Exception e )
        {
            log.error( "ROCF.deleteOldCreditUtilEvents : exception while deleting credit util events.", e );
            return false;
        }
        return true;
    }

    public boolean deleteOldCreditUtils( int batchSize )
    {
        try
        {
            Session session = PersistenceFactory.newSession();
            String deleteSql = "DELETE FROM IDCCREDITUTIL WHERE CUDATE  < SYSDATE -1 AND TYPE='IdcDailyCredUtilC' AND ROWNUM < " + ( batchSize + 1 );
            session.executeNonSelectingSQL( deleteSql );
        }
        catch ( Exception e )
        {
            log.error( "ROCF.deleteOldCreditUtils : exception while deleting credit utils.", e );
            return false;
        }
        return true;
    }
}
