package com.integral.finance.creditLimit;

// Copyright (c) 2001-2005 Integral Development Corp. All rights reserved.

import com.integral.util.Factory;

/**
 * CreditLimitFactory provides methods to create credit limit related objects.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitPOJOFactory extends Factory
{

    protected static CreditLimitPOJOFactory current;

    static
    {
        CreditLimitPOJOFactory.current = new CreditLimitPOJOFactory();
    }

    /**
     * Return new instance of CreditWorkflowMessage.
     *
     * @return credit workflow message
     */
    public static CreditWorkflowMessage newCreditWorkflowMessage()
    {
        return current._newCreditWorkflowMessage();
    }

    /**
     * Return instance of CreditWorkflowMessageC.
     *
     * @return credit workflow message
     */
    protected CreditWorkflowMessage _newCreditWorkflowMessage()
    {
        return new CreditWorkflowMessageC();
    }
}
