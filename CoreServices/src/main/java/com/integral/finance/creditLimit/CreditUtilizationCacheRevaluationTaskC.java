package com.integral.finance.creditLimit;

// Copyright (c) 2001-2007 Integral Development Corp.  All rights reserved.

import com.integral.finance.businessCenter.EndOfDayService;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.price.fx.FXPrice;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.util.LogUtil;
import com.integral.user.Organization;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * This class is used periodically revaluate the credit utilization available in the cache.
 */
public class CreditUtilizationCacheRevaluationTaskC implements Runnable
{
    private static final Log log = LogFactory.getLog ( CreditUtilizationCacheRevaluationTaskC.class );
    private final AtomicInteger count = new AtomicInteger ();
    private volatile boolean stopped = false;
    private final Object lock = new Object ();
    private static final int HALF_HOUR = 30 * 60 * 1000;
    private static final EndOfDayService endOfDayService = EndOfDayServiceFactory.getEndOfDayService ();
    public static volatile boolean forceFetch;
    public static volatile long lastStaleUpdatedTime = System.currentTimeMillis ();
    public static volatile long lastStopOutEvaluatedTime = System.currentTimeMillis ();

    private static final Set<String> creditRelations = new HashSet<String> ();

    private final Object CREDIT_RELATIONS_LOCK = new Object ();

    public void run ( )
    {
        while ( !stopped )
        {
            try
            {
                LogUtil.setDisableDBQueryLogSwitch ( true );
                synchronized ( lock )
                {
                    // updates the current trade date. This should be moved to a more appropriate thread.
                    endOfDayService.updateCurrentTradeDate ();
                    endOfDayService.updateCurrentNZDTradeDate ();

                    long period = CreditLimitConstants.adminConfigMBean.getRealtimeCreditUtilizationCacheRevaluationPeriod ();
                    count.incrementAndGet ();
                    if ( CreditLimitConstants.adminConfigMBean.isPeriodicRealtimeCreditUtilizationCacheRevaluationEnabled () )
                    {
                        long t0 = System.currentTimeMillis ();
                        Collection<CreditUtilization> creditUtils = CreditUtilizationManagerC.getInstance ().getCreditUtilizationCache ().getAllCreditUtilizations ();
                        Map<String, FXPrice> rateMap = new HashMap<String, FXPrice> ( 20 );
                        for ( CreditUtilization cu : creditUtils )
                        {
                            if ( !(cu instanceof DailyCreditUtilization) )
                            {
                                CreditUtilC.updateCurrencyPositionConversionRates ( cu, rateMap, false );
                                cu.getRealtimeCreditUtilization ().recalculate ();
                                if ( log.isDebugEnabled () )
                                {
                                    log.debug ( new StringBuilder ( 200 ).append ( "CUCRT.update.DEBUG : Recalculated realTimeCU for CU:" ).append ( cu )
                                            .append ( " | With Cpc=" ).append ( cu.getCurrencyPositions () ).append ( ". Latest RCU=" ).append ( cu.getRealtimeCreditUtilization () ).toString () );
                                }
                            }
                        }
                        if ( log.isDebugEnabled () )
                        {
                            log.debug ( new StringBuilder ( 200 ).append ( "CUCRT.update.DEBUG : Time taken for credit util calcs=" )
                                    .append ( System.currentTimeMillis () - t0 ).append ( ",creditUtils.size=" ).append ( creditUtils.size () ).toString () );
                        }
                        t0 = System.currentTimeMillis () - t0;
                        if ( log.isDebugEnabled () )
                        {
                            log.debug ( new StringBuilder ( 200 ).append ( "CUCRT.update.DEBUG : Revaluation count=" )
                                    .append ( count ).append ( ",total time took ms=" ).append ( t0 ).toString () );
                        }
                        if ( t0 < period )
                        {
                            period -= t0;
                        }
                    }

                    //Fetch all the credit utilization to the cache for all the subscriptions every 30 minutes
                    if ( (count.get () == (HALF_HOUR / CreditLimitConstants.adminConfigMBean.getRealtimeCreditUtilizationCacheRevaluationPeriod ()))
                            && CreditLimitConstants.configMBean.isCreditUtilizationPeriodicMemoryFetchEnabled () )
                    {
                        count.set ( 0 );
                        CreditNotificationManagerC.getInstance ().getPooledExecutor ().execute ( new CreditUtilizationCacheFetchFromSubscriptionsC () );
                    }
                    else if ( forceFetch && !creditRelations.isEmpty () && count.get () % 10 == 0 )
                    {
                        synchronized ( CREDIT_RELATIONS_LOCK )
                        {
                            forceFetch = false;
                            CreditUtilizationCacheFetchFromSubscriptionsC task = new CreditUtilizationCacheFetchFromSubscriptionsC ();
                            task.setCreditRelations ( new HashSet<String> ( creditRelations ) );
                            log.info ( "CUCRT.run - async loading all legal entities credit relations=" + creditRelations );
                            creditRelations.clear ();
                            CreditNotificationManagerC.getInstance ().getPooledExecutor ().execute ( task );
                        }
                    }
                    // rebuilds the stale entries
                    if ( System.currentTimeMillis () - lastStaleUpdatedTime > CreditLimitConstants.adminConfigMBean.getCreditUtilizationCacheEntryUpdateStalePeriod () )
                    {
                        CreditNotificationManagerC.getInstance ().getPooledExecutor ().execute ( new CreditUtilizationCacheFetchFromSubscriptionsC ( CreditUtilizationCacheFetchFromSubscriptionsC.REBUILD ) );
                        lastStaleUpdatedTime = System.currentTimeMillis ();
                    }
                    if ( CreditUtilC.isMarginRevaluationEnabled () && System.currentTimeMillis () - lastStopOutEvaluatedTime > CreditLimitConstants.adminConfigMBean.getCreditUtilizationStopOutRevaluationPeriod () )
                    {
                        CreditNotificationManagerC.getInstance ().getPooledExecutor ().execute ( new CreditUtilizationCacheFetchFromSubscriptionsC ( CreditUtilizationCacheFetchFromSubscriptionsC.REVALUATE_MARGIN ) );
                        lastStopOutEvaluatedTime = System.currentTimeMillis ();
                    }
                    lock.wait ( period );
                }
            }
            catch ( Exception e )
            {
                log.error ( "CUCRT.run - Exception.", e );
            }
            finally
            {
                LogUtil.removeDisableDBQueryLogSwitch ();
            }
        }
    }

    public void stop ( )
    {
        stopped = true;
    }

    public void forceFetch ( final Organization takerOrg, Organization makerOrg )
    {
        synchronized ( CREDIT_RELATIONS_LOCK )
        {
            String relation = new StringBuilder ( 100 ).append ( takerOrg.getShortName () )
                    .append ( CreditUtilizationCacheFetchFromSubscriptionsC.CREDIT_RELATION_DELIMITER )
                    .append ( makerOrg.getShortName () ).toString ();
            creditRelations.add ( relation );
            forceFetch = true;
        }
    }
}
