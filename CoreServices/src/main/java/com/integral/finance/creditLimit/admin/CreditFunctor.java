package com.integral.finance.creditLimit.admin;

// Copyright (c) 2001-2006 Integral Development Corp. All rights reserved.

import com.integral.time.IdcDateTime;
import com.integral.user.User;

/**
 * This basic interface for credit admin functors which is used to execute credit regeneration activities.
 *
 * <AUTHOR> Development Corp.
 */
public interface CreditFunctor
{
    /**
     * Returns the name of the functor.
     *
     * @return name
     */
    public String getName();

    /**
     * Sets the name of the functor.
     *
     * @param name
     */
    public void setName( String name );

    /**
     * Executes the functors which performs an activity related to credit regeneration.
     */
    public void execute();

    /**
     * Returns whether this functor is enabled.
     *
     * @return enabled.
     */
    public boolean isEnabled();

    /**
     * Sets whether the functor is enabled or disabled.
     *
     * @param enable
     */
    public void setEnabled( boolean enable );

    /**
     * Returns the date and time at which functor started executing.
     *
     * @return datetime
     */
    public IdcDateTime getStartTime();

    /**
     * Returns the date and time at which functor finished executing.
     *
     * @return datetime
     */
    public IdcDateTime getEndTime();

    /**
     * Returns the user executing the functor.
     *
     * @return user
     */
    public User getUser();

    /**
     * Returns whether the functor is getting executed.
     *
     * @return running
     */
    public boolean isRunning();

    /**
     * Returns true if the functor execution is suspended.
     *
     * @return isSuspended
     */
    public boolean isSuspended();

    /**
     * Suspends the execution of the functor.
     */
    public void suspend();

    /**
     * Resets the functor for future execution. This is used after suspending the functor.
     */
    public void reset();
}
