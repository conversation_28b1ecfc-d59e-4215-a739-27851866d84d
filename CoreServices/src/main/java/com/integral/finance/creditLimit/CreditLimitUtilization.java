package com.integral.finance.creditLimit;

// Copyright (c) 2015 Integral Development Corp.  All rights reserved.

/**
 * This class is used to hold details about the credit utilization.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitUtilization implements Comparable
{
        private double utilizedPercentage;
        private double utilizationThreshold;
        private int utilizationLevel;
        private CreditUtilization creditUtil;
        private double utilizedAmount;
        private double availableAmount;

        public CreditLimitUtilization( double usedAmt, double available, double percent, double threshold, int level, CreditUtilization cu )
        {
            this.utilizedPercentage = percent;
            this.utilizationThreshold = threshold;
            this.utilizationLevel = level;
            this.creditUtil = cu;
            this.utilizedAmount = usedAmt;
            this.availableAmount = available;
        }

        public double getUtilizedAmount()
        {
            return utilizedAmount;
        }

        public double getAvailableAmount()
        {
            return availableAmount;
        }

        public void setAvailableAmount( double amt )
        {
            this.availableAmount = amt;
        }

        public double getUtilizedPercentage()
        {
            return utilizedPercentage;
        }

        public double getUtilizationThreshold()
        {
            return utilizationThreshold;
        }

        public int getUtilizationLevel()
        {
            return utilizationLevel;
        }

        public CreditUtilization getCreditUtilization()
        {
            return creditUtil;
        }

        public void setCreditUtilization( CreditUtilization cu )
        {
            this.creditUtil = cu;
        }

        public int compareTo( Object other )
        {
            if ( other instanceof CreditLimitUtilization )
            {
                CreditLimitUtilization utilInfo = ( CreditLimitUtilization ) other;

                if ( this.equals( utilInfo ) )
                {
                    return 0;
                }
                if ( utilInfo.getUtilizationLevel() != this.getUtilizationLevel() )
                {
                    return utilInfo.getUtilizationLevel() - this.getUtilizationLevel();
                }
                else
                {
                    return utilInfo.getUtilizedPercentage() > this.getUtilizedPercentage() ? -1 : 1;
                }
            }
            return 0;
        }

        public String toString()
        {
            return new StringBuilder( 200 ).append( "cu=" ).append( creditUtil ).append( ",level=" ).append( utilizationLevel )
                    .append( ",threshold=" ).append( utilizationThreshold ).append( ",utilizedAmt=" ).append( utilizedAmount )
                    .append( ",utilizedPercent=" ).append( utilizedPercentage ).append( ",availableAmt=" ).append( availableAmount ).toString();
        }
}

