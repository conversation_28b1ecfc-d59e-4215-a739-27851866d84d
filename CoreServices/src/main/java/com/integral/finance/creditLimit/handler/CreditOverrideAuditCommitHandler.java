package com.integral.finance.creditLimit.handler;

// Copyright (c) 2020 Integral Development Corp.  All rights reserved.

import com.integral.exception.IdcException;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.admin.CreditAdminServiceLoggerC;
import com.integral.finance.creditLimit.audit.CreditLimitAdminAuditManagerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.IdcUtilC;

public class CreditOverrideAuditCommitHandler implements MessageHandler, Runnable
{
    protected static Log log = LogFactory.getLog( CreditAdminServiceLoggerC.class );


    /**
     * Instance variable for credit override audit facade creation
     */
    private Organization creditProvider;
    private TradingParty creditCpty;
    private Organization creditCptyOrg;
    private String tradeDescription;
    private User sessionUser, overrideUser;
    private boolean warmup;

    private CreditOverrideAuditCommitHandler()
    {
    }

    public CreditOverrideAuditCommitHandler( Organization provider, TradingParty creditCpty, Organization creditCptyOrg, String tradeDescription, User sessionUser, User overrideUser, boolean isWarmup )
    {
        this();
        this.creditProvider = provider;
        this.creditCpty = creditCpty;
        this.creditCptyOrg = creditCptyOrg;
        this.tradeDescription = tradeDescription;
        this.sessionUser = sessionUser;
        this.overrideUser = overrideUser;
        this.warmup = isWarmup;
    }

    /*
        Starts a transaction and persists the credit rejection audit event.
    */
    public Message handle( Message message ) throws IdcException
    {
        if ( log.isDebugEnabled() )
        {
            log.debug( "COAH.handle.INFO : Audit credit override event. " + this );
        }
        try
        {
            if ( !warmup )
            {
                User contextUser = sessionUser != null ? sessionUser : creditProvider.getDefaultDealingUser ();
                if ( contextUser != null )
                {
                    IdcUtilC.setSessionContextUser ( contextUser );
                }
                CreditLimitAdminAuditManagerC.getInstance().auditCreditOverride ( creditProvider, creditCpty, creditCptyOrg, overrideUser, tradeDescription );
            }
        }
        catch ( Exception e )
        {
            log.error( "COAH.handle ; Audit credit override event. " + this, e );
        }
        return message;
    }

    public void run()
    {
        try
        {
            handle( null );
        }
        catch ( Exception e )
        {
            log.error( "COAH.run : Exception. cpo=" + creditProvider + ",cco=" + creditCptyOrg + ",cc=" + creditCpty, e );
        }
    }

    public String toString()
    {
        return new StringBuilder( 100 ).append( "creditProvider=" ).append( creditProvider ).append( "cpty=" ).append( creditCpty ).
                append( ",cptyOrg=" ).append( creditCptyOrg ).append( ",tradeDescription=" ).append( tradeDescription ).toString();
    }
}
