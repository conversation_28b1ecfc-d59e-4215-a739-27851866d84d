package com.integral.finance.creditLimit.functor;

import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.db.CreditDataQueryServiceFactory;
import com.integral.finance.creditLimit.db.CreditMemoryCacheUpdate;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.is.ISCommonConstants;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.Namespace;
import com.integral.persistence.cache.ReferenceDataCache;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;
import com.integral.user.OrganizationC;
import com.integral.util.CollectionUtil;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

public class CreditCurrencyPairGroupNotificationFunctorC extends CreditRemoteNotificationFunctorC
{
    private static final Log log = LogFactory.getLog( CreditCurrencyPairGroupNotificationFunctorC.class );

    public void onCommit( HashMap props )
    {
        // do not process the message if remote notification processing is not enabled.
        if ( !creditAdminConfig.isCreditRemoteNotificationProcessingEnabled() )
        {
            return;
        }

        long t0 = System.currentTimeMillis();
        Organization cpo;
        Organization cco = null;
        TradingParty cc = null;
        try
        {
            // Use case#1 of credit admin service calls to assign the exempted currency pair group at provider level or counterparty level.
            String creditProviderOrgGuid = ( String ) props.get( CreditLimit.CREDIT_PROVIDER_ORGANIZATION );
            if ( creditProviderOrgGuid != null )
            {
                cpo = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditProviderOrgGuid, Organization.class );

                String creditCptyOrgGuid = ( String ) props.get( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION );
                if ( creditCptyOrgGuid != null )
                {
                    cco = ( Organization ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditCptyOrgGuid, OrganizationC.class );
                }

                String creditCptyGuid = ( String ) props.get( CreditLimit.CREDIT_COUNTERPARTY );
                if ( creditCptyGuid != null )
                {
                    cc = ( TradingParty ) ReferenceDataCacheC.getInstance().getEntityByGuid( creditCptyGuid, TradingParty.class );
                }

                if ( log.isDebugEnabled() )
                {
                    log.debug( "CPNF.onCommit : updating cache for cpo=" + cpo + ",cco=" + cco + ",cc=" + cc );
                }

                if ( cpo != null )
                {
                    if ( cc != null )
                    {
                        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().updateTradingPartyCreditExemptionList( cpo, cc );
                    }
                    else if ( cco != null )
                    {
                        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().updateCounterpartyOrganizationCreditExemptionList( cpo, cco );
                    }
                    else
                    {
                        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().updateProviderOrganizationCreditExemptionList( cpo );
                    }
                }
            }

            // Use case #2 Currency pair group update with change of underlying currency pairs.
            String cpgGuid = ( String ) props.get( ISCommonConstants.CURRENCYPAIRGROUP_GUID );
            if ( cpgGuid != null )
            {
                CurrencyPairGroup cpg = ( CurrencyPairGroup ) ReferenceDataCacheC.getInstance().getEntityByGuid( cpgGuid, CurrencyPairGroup.class );
                if ( cpg != null )
                {
                    CreditMemoryCacheUpdate creditMemoryCacheUpdate =  CreditDataQueryServiceFactory.getCreditMemoryCachUpdate();
                    creditMemoryCacheUpdate.reintializeLoadedPFEConfiguration();

                    CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().updateCreditExemptionList(cpg);
                    Namespace namespace = cpg.getNamespace();
                    ReferenceDataCache referenceDataCache = ReferenceDataCacheC.getInstance();
                    boolean isMainNS = namespace.isSameAs(referenceDataCache.getMAINNamespace());
                    Set<Organization> creditProviderOrgs = new HashSet<Organization>();

                    Collection<PFEConfiguration> pfeConfigurations = null;
                    if(isMainNS)
                    {
                        pfeConfigurations = CreditDataQueryServiceFactory.getCreditDataQueryService().getPFEAssociatedToCurrencyPairGroup(cpg);
                    }
                    else
                    {
                        Organization org = referenceDataCache.getOrganization(namespace);
                        CreditLimitOrgFunction orgFunc = CreditUtilC.getCreditLimitOrgFunction(org);
                        if( null!= org && orgFunc != null)
                        {
                            pfeConfigurations = CreditUtilC.getPFEConfiguration(org);
                            creditProviderOrgs.add(org);
                        }
                    }
                    if(!CollectionUtil.isEmptyOrNull(pfeConfigurations))
                    {
                        for (PFEConfiguration pfeConfiguration : pfeConfigurations)
                        {
                            if(isMainNS)
                            {
                                Namespace creditProviderNS = (Namespace) pfeConfiguration.getNamespace();
                                Organization creditProviderOrg = referenceDataCache.getOrganization(creditProviderNS);
                                if( null!= creditProviderOrg )
                                {
                                    log.warn("CPNF.onCommit -  PFE Configuration  " + pfeConfiguration.getFullyQualifiedName() + " ] Changes, hence will reinitialize " + creditProviderOrg.getShortName() + " credit cache");
                                    creditProviderOrgs.add(creditProviderOrg);
                                }
                            }

                            Collection<PFEConfigurationProfile> pfeConfigurationProfiles = pfeConfiguration.getPfeConfigurationProfiles();
                            for (PFEConfigurationProfile pfeConfigurationProfile : pfeConfigurationProfiles)
                            {
                                ((PFEConfigurationProfileC) pfeConfigurationProfile).resetTransients();
                            }
                            ((PFEConfigurationC) pfeConfiguration).resetTransients();
                            log.warn("CPNF.onCommit - Reinitializing PFE Configuration [ " + pfeConfiguration.getFullyQualifiedName() + " ] on Currency Pair Group  [ " + cpg.getFullyQualifiedName() + " ] change ");
                        }

                        for (Organization creditProviderOrg : creditProviderOrgs)
                        {
                            initializeTenorProfileCache(creditProviderOrg);
                        }
                    }
                }
                else
                {
                    log.warn("CPNF.onCommit : No currency pair group found with GUID=" + cpgGuid);
                }

            }


            log.info( new StringBuilder( 200 ).append( "CPNF.onCommit : updated the cache for credit exemption list. props=" )
                    .append( props ).append( ",timeTaken=" ).append( System.currentTimeMillis() - t0 ).toString() );
        }
        catch ( Exception e )
        {
            log.error( "CPNF.onCommit : Exception while handling the admin notification on exempted currency pair. props=" + props, e );
        }
    }

    private void initializeTenorProfileCache(Organization creditProviderOrg)
    {
        log.warn("CPNF.onCommit Started Tenor Profile/PFE initialization for Currency Pair Group change for Org [ " + creditProviderOrg.getShortName() + " ]");
        CreditUtilizationManagerC.getInstance().getCreditUtilizationCache().updateProviderOrganizationCreditTenorProfile(creditProviderOrg);
        log.warn("CPNF.onCommit Completed Tenor Profile/PFE initialization for Currency Pair Group change for Org [ " + creditProviderOrg.getShortName() + " ]");

    }
}