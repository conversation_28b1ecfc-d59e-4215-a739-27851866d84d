package com.integral.finance.creditLimit.admin;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.util.Factory;

/**
 * Provides factory methods for getting credit limit admin service.
 *
 * <AUTHOR> Development Corp.
 */
public class CreditLimitAdminServiceFactory extends Factory
{
    // Singleton members.
    protected static CreditLimitAdminServiceFactory current;


    // Static initialization.
    static
    {
        current = new CreditLimitAdminServiceFactory();
    }

    /**
     * Returns the credit limit admin service.
     *
     * @return credit limit admin service.
     */
    public static CreditLimitAdminService getCreditLimitAdminService()
    {
        return current._getCreditLimitAdminService();
    }

    /**
     * Returns the credit limit admin service.
     *
     * @return credit limit admin service
     */
    protected CreditLimitAdminService _getCreditLimitAdminService()
    {
        return new CreditLimitAdminServiceC();
    }
}

