package com.integral.finance.creditLimit.admin;

// Copyright (c) 2017 Integral Development Corporation.  All Rights Reserved.

import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationFactory;
import com.integral.dbms.maintenance.purgedb.configuration.DealingDataPurgeConfigurationMBean;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.dateGeneration.mbean.HolidayCalendarMBeanC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.rule.SendEmailAction;
import com.integral.rule.SendEmailActionC;
import com.integral.scheduler.ScheduleFunctorC;
import com.integral.system.mail.SendEmailC;
import com.integral.system.notification.SendEmailThread;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;
import com.integral.util.StringUtilC;
import com.integral.util.Tuple;

import java.util.*;

public class OrgFullInactivationReportFunctor extends ScheduleFunctorC
{
    private static Log log = LogFactory.getLog( OrgFullInactivationReportFunctor.class );
    private DealingDataPurgeConfigurationMBean _purgeConfigMBean = DealingDataPurgeConfigurationFactory.getDealingDataPurgeConfigurationMBean();
    private static final String INVALID_EMAIL_ADDRESS = "FAIL: Invalid email address.";
    public static final String ORGS_LIST = "OrgsList";
    private static final String NO_BROKER_ORG = "N/A";

    private String fromEmailAddress, toEmailAddresses;

    public void execute( WorkflowMessage msg )
    {
        long t0 = System.currentTimeMillis();
        log.warn( "OFIRF.execute : Begin execute full inactivation orgs report functor." );
        try
        {
            generateReport( msg );
        }
        catch ( Exception e )
        {
            log.error( "OFIRF.execute : Error while generating the report of list of orgs that are marked for full inactivation.", e );
        }
        log.info( "OFIRF.execute : Finished executing full inactivation report functor. took ms= " + ( System.currentTimeMillis() - t0 ) );
    }

    @Override
    public String getDescription()
    {
        return "Functor to report the list of orgs that are marked for full inactivation in the weekend.";
    }

    @Override
    public List<String> getFunctorProperties()
    {
        List<String> properties = super.getFunctorProperties();
        properties.add( "fromEmailAddress" );
        properties.add( "toEmailAddresses" );
        return properties;
    }

    public String getFromEmailAddress()
    {
        return fromEmailAddress;
    }

    public void setFromEmailAddress( String emailId )
    {
        this.fromEmailAddress = emailId;
    }

    public String getToEmailAddresses()
    {
        return toEmailAddresses;
    }

    public void setToEmailAddresses( String emailIds )
    {
        this.toEmailAddresses = emailIds;
    }

    public String validate()
    {
        String result = super.validate ();
        if ( !SUCCESS.equals ( result ) )
        {
            return result;
        }

        if ( StringUtilC.isNullOrEmpty ( fromEmailAddress ) || StringUtilC.isNullOrEmpty ( toEmailAddresses ) )
        {
            return INVALID_EMAIL_ADDRESS;
        }

        if ( !CreditUtilC.validateEmailId ( fromEmailAddress ) )
        {
            return INVALID_EMAIL_ADDRESS;
        }

        String[] emailIds = toEmailAddresses.split ( "," );
        for ( String emailId: emailIds )
        {
            if ( !CreditUtilC.validateEmailId ( emailId ) )
            {
                return INVALID_EMAIL_ADDRESS;
            }
        }
        return SUCCESS;
    }

    private void generateReport( WorkflowMessage msg )
    {
        Collection<Organization> orgs = getFullInactivationOrgs();
        if ( orgs != null && !orgs.isEmpty () )
        {
            String orgsDetail = getOrgsListDetail( getOrgsSet ( orgs ));
            if ( msg != null )
            {
                msg.setParameterValue ( ORGS_LIST, orgsDetail );
            }
            SendEmailAction emailAction = buildFullInactivationOrgsReportEmail ( orgs, orgsDetail );
            SendEmailC.getEventsPool ().execute ( new SendEmailThread ( emailAction ) );
        }
        else
        {
            log.info ( "OFIRF.generateReport : There are no orgs that are marked with full inactivation. Skipping report email generation." );
        }
    }

    private Collection<Organization> getFullInactivationOrgs()
    {
        Collection<Organization> fullInactivationOrgs = new HashSet<Organization> ();
        Organization[] orgs = ReferenceDataCacheC.getInstance ().getOrgs ();
        for ( Organization org: orgs )
        {
            if ( org.isPassive () && org.isFullInactivation () )
            {
                fullInactivationOrgs.add ( org );
            }
        }
        return fullInactivationOrgs;
    }

    private String getFullInactivationOrgsReportEmailSubject()
    {
        return HolidayCalendarMBeanC.getInstance ().getEmailSubjectPrefix () + " " +  _purgeConfigMBean.getFullInactivationOrgsListEmailSubject ();
    }

    private SendEmailAction buildFullInactivationOrgsReportEmail( Collection<Organization> orgs, String orgsList )
    {
        SendEmailAction emailAction = null;
        try
        {
            String subject;
            String body;

            subject = getFullInactivationOrgsReportEmailSubject ();
            body = _purgeConfigMBean.getFullInactivationOrgsListEmailContent ();

            if ( subject != null && body != null )
            {
                emailAction = new SendEmailActionC ();
                Collection<String> tos = IdcUtilC.arrayAsArrayList( IdcUtilC.getSubstring( getToEmailAddresses (), "," ) );
                emailAction.setTos( tos );
                emailAction.setFrom( getFromEmailAddress () );
                emailAction.setSubject( subject );
                emailAction.setBody( body );

                // set parameters in the send mail action.
                emailAction.putCustomField( ORGS_LIST, orgsList );
                log.info( new StringBuilder( 300 ).append( "OFIRF.buildFullInactivationOrgsReportEmail : email details. subject=" )
                        .append( subject ).append( ",body=" ).append( body ).toString() );
            }
        }
        catch ( Exception e )
        {
            log.error( "OFIRF.buildFullInactivationOrgsReportEmail : Exception.", e );
        }
        return emailAction;
    }

    private Set<Tuple<String, String>> getOrgsSet( Collection<Organization> orgs )
    {
        Set<Tuple<String, String>> orgsSet = new TreeSet<Tuple<String, String>> ( new OrgTupleComparator() );
        for ( Organization org: orgs )
        {
            orgsSet.add ( new Tuple<String, String> ( org.getBrokerOrganization () != null ? org.getBrokerOrganization ().getShortName () : NO_BROKER_ORG, org.getShortName () ) );
        }
        return orgsSet;
    }

    private String getOrgsListDetail ( Set<Tuple<String, String>>  orgsSet )
    {
        StringBuilder buf = new StringBuilder( 500 );
        buf.append( "Org" ).append ( ',' ).append ( "Broker" ).append ( "\n" );
        for ( Tuple<String, String> tpl: orgsSet )
        {
            buf.append ( tpl.second ).append ( ',' ).append ( tpl.first );
            buf.append ( "\n" );
        }
        return buf.toString ();
    }

    private class OrgTupleComparator implements Comparator, java.io.Serializable
    {
        public int compare( Object first, Object second )
        {
            Tuple<String,String> tuple1 = ( Tuple<String,String>  ) first;
            Tuple<String,String> tuple2 = ( Tuple<String,String>  ) second;
            return tuple1.first.equals ( tuple2.first ) ? tuple1.second.compareTo ( tuple2.second ) : tuple1.first.compareTo( tuple2.first );
        }
    }
}
