package com.integral.finance.creditLimit;

// Copyright (c) 2001-2005 Integral Development Corp.  All rights reserved.

import com.integral.exception.IdcException;
import com.integral.exception.IdcOptimisticLockException;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.admin.CreditFunctor;
import com.integral.finance.creditLimit.admin.CreditFunctorServerC;
import com.integral.finance.creditLimit.admin.CreditLimitAdminServiceFactory;
import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationFactory;
import com.integral.finance.creditLimit.admin.configuration.CreditAdminConfigurationMBean;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationFactory;
import com.integral.finance.creditLimit.configuration.CreditLimitConfigurationMBean;
import com.integral.finance.creditLimit.db.CreditDataQueryServiceFactory;
import com.integral.finance.creditLimit.functor.CreditEnableNotificationFunctorC;
import com.integral.finance.creditLimit.functor.CreditUtilizationEventRecalculationNotificationFunctorC;
import com.integral.finance.currency.Currency;
import com.integral.finance.dealing.Request;
import com.integral.finance.fx.FXTrade;
import com.integral.finance.instrument.AmountOfInstrument;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.trade.Trade;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.log.MessageLogger;
import com.integral.is.spaces.fx.persistence.ISSpacesPersistenceService;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.netting.model.NettingPortfolio;
import com.integral.netting.model.NettingTradeRequest;
import com.integral.persistence.ClusterCommitEventAdapterC;
import com.integral.persistence.Entity;
import com.integral.persistence.PersistenceFactory;
import com.integral.rule.Rule;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.time.DatePeriod;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.IdcUtilC;
import com.integral.util.StopWatchC;
import com.integral.workflow.dealing.DealingLimit;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.CursoredStream;
import org.eclipse.persistence.queries.ReadAllQuery;
import org.eclipse.persistence.sessions.Session;
import org.eclipse.persistence.sessions.UnitOfWork;

import java.util.*;

/**
 * This class is used to manage the creation, revaluation and deletion of credit utilizations. It also provides the look-up
 * of credit utilizations.
 */
public class CreditUtilizationManagerC
{
    private static final int COMMIT_BATCH_SIZE = 100;
    private static final int JUMBO_COMMIT_BATCH_SIZE = 500;

    protected Log log = LogFactory.getLog( this.getClass() );
    private static Vector<Class> readOnlyClasses = new Vector<Class>( 20 );
    private static Vector<Class> creditLimitBalanceUpdateReadOnlyClasses = new Vector<Class>( 20 );

    /**
     * lock for creation of credit utilizations. This is required to avoid db deadlocks.
     */
    private static final Object cuCreationLock = new Object();

    private CreditUtilizationCache _creditUtilizationCache = null;
    private CreditUtilizationPersistenceTaskC creditUtilUpdateTask = null;
    private CreditUtilizationNotificationTaskC creditUtilNotificationTask = null;
    private CreditUtilizationCacheRevaluationTaskC creditUtilizationCacheRevaluationTask = new CreditUtilizationCacheRevaluationTaskC();
    private CreditAdminConfigurationMBean creditAdminConfigMBean = CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean();
    private ISSpacesPersistenceService spacesPersistenceService = null;

    /**
     * Singleton instance
     */
    private static CreditUtilizationManagerC _creditUtilizationManager;

    /*
     * Static initialization.
     */
    static
    {
        _creditUtilizationManager = new CreditUtilizationManagerC();
        setReadOnlyClasses();
        setCreditLimitBalanceUpdateReadOnlyClasses();
    }

    /**
     * Private constructor.
     */
    private CreditUtilizationManagerC()
    {
        _creditUtilizationCache = CreditUtilizationCacheC.getInstance();
        CreditUtilizationCacheManager.getInstance ().setCreditUtilizationCache ( _creditUtilizationCache );
        creditUtilUpdateTask = new CreditUtilizationPersistenceTaskC();
        creditUtilNotificationTask = new CreditUtilizationNotificationTaskC();

        if(ConfigurationFactory.getServerMBean().isCreditWorkflowServer())
        {
            Thread creditUtilUpdateThread = new Thread( creditUtilUpdateTask );
            creditUtilUpdateThread.setDaemon( true );
            creditUtilUpdateThread.start();

            // set the credit utilization revaluation timer.
            if ( CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean().isPeriodicRealtimeCreditUtilizationCacheRevaluationEnabled() )
            {
                Thread creditRevalThread = new Thread( creditUtilizationCacheRevaluationTask );
                creditRevalThread.setDaemon( true );
                creditRevalThread.start();
            }

            // add the notification task.
            Thread creditUtilNotifierThread = new Thread( creditUtilNotificationTask );
            creditUtilNotifierThread.setDaemon( true );
            creditUtilNotifierThread.start();
        }
    }

    /**
     * Returns the singleton instance of CreditUtilizationManager.
     *
     * @return credit utilization manager.
     */
    public static CreditUtilizationManagerC getInstance()
    {
        return _creditUtilizationManager;
    }

    public CreditUtilizationPersistenceTaskC getCreditUtilizationPersistenceTask()
    {
        return creditUtilUpdateTask;
    }

    public CreditUtilizationNotificationTaskC getCreditUtilizationNotificationTask()
    {
        return creditUtilNotificationTask;
    }

    public CreditUtilizationCacheRevaluationTaskC getCreditUtilizationCacheRevaluationTask()
    {
        return creditUtilizationCacheRevaluationTask;
    }

    /**
     * Returns whether the credit is enabled for the credit provider organization and credit counterparty combination.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @return enabled
     */
    public boolean isCreditEnabled( Organization creditProviderOrg, TradingParty creditCpty )
    {
        return _creditUtilizationCache.isCreditEnabled( creditProviderOrg, creditCpty );
    }

    /**
     * Returns the credit status of the relationship between the credit provider org and credit cpty.
     *
     * @param creditProviderOrg credit provider organization
     * @param creditCpty        credit counterparty
     * @return status
     */
    public int getCreditStatus( Organization creditProviderOrg, TradingParty creditCpty )
    {
        return _creditUtilizationCache.getCreditStatus( creditProviderOrg, creditCpty );
    }


    /**
     * Returns whether credit is enabled in any of the credit lines between first legal entity and second legal entity whether
     * it is through a prime broker trading party or directly.
     *
     * @param le1 legal entity1
     * @param le2 legal entity2
     * @return credit enabled
     */
    public boolean isCreditEnabled( LegalEntity le1, LegalEntity le2 )
    {
        return CreditLimitSubscriptionManagerC.getInstance().isCreditEnabled( le1, le2 );
    }

    /**
     * Returns the credit utilization cache which keeps the credit utilizations for credit workflow.
     *
     * @return credit utilization cache
     */
    public CreditUtilizationCache getCreditUtilizationCache()
    {
        return _creditUtilizationCache;
    }

    /**
     * Subscribes the currency pair for the credit limit updates for the credit line provided by the credit provider organization
     * for the credit counterparty represented by the trading party or organization.
     *
     * @param creditProviderLe credit provider legal entity
     * @param creditCpty       credit counterparty
     * @param creditCptyOrg    credit counterparty organization
     * @param baseCcy          base currency
     * @param varCcy           variable currency
     */
    public void subscribe( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, Currency baseCcy, Currency varCcy )
    {
        CreditLimitSubscriptionManagerC.getInstance().subscribe( creditProviderLe, creditCpty, creditCptyOrg, baseCcy, varCcy );
    }

    /**
     * Subscribes a list of currency pairs for the credit limit updates for the credit line provided by the credit provider organization
     * for the credit counterparty trading party or organization.
     *
     * @param creditProviderLe credit provider legal entity
     * @param creditCpty       credit counterparty
     * @param creditCptyOrg    credit counterparty organization
     * @param ccyPairColl      currency pair collection
     */
    public void subscribe( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, Collection<String> ccyPairColl )
    {
        CreditLimitSubscriptionManagerC.getInstance().subscribe( creditProviderLe, creditCpty, creditCptyOrg, ccyPairColl );
    }

    /**
     * Unsubscribe the currency pair formed by base currency and variable currency for the credit limit updates for the credit line provided by
     * the credit provider organization for the credit counterparty trading party or organization.
     *
     * @param creditProviderLe credit provider legal entity
     * @param creditCpty       credit counterparty
     * @param creditCptyOrg    credit counterparty organization
     * @param baseCcy          base currency
     * @param varCcy           variable currency
     */
    public void unsubscribe( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, Currency baseCcy, Currency varCcy )
    {
        CreditLimitSubscriptionManagerC.getInstance().unsubscribe( creditProviderLe, creditCpty, creditCptyOrg, baseCcy, varCcy );
    }

    /**
     * Unsubscribe the list of currency pairs for credit limit updates for the credit line provided by the credit provider organization
     * for the credit counterparty trading party or organization. If currency pair collection is null, then all currency pair subscriptions are
     * removed.
     *
     * @param creditProviderLe credit provider legal entity
     * @param creditCpty       credit counterparty
     * @param creditCptyOrg    credit counterparty organization
     * @param ccyPairColl      currency pair collection
     */
    public void unsubscribe( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, Collection<String> ccyPairColl )
    {
        CreditLimitSubscriptionManagerC.getInstance().unsubscribe( creditProviderLe, creditCpty, creditCptyOrg, ccyPairColl );
    }

    /**
     * Subscribes the currency pair for the credit limit updates for the credit lines between the specified legal entities.
     *
     * @param le1     legal entity1
     * @param le2     legal entity2
     * @param baseCcy base currency
     * @param varCcy  variable currency
     */
    public void subscribeBilateralCreditLimitUpdate( LegalEntity le1, LegalEntity le2, Currency baseCcy, Currency varCcy )
    {
        CreditLimitSubscriptionManagerC.getInstance().subscribeBilateralCreditLimitUpdate( le1, le2, baseCcy, varCcy );
    }

    /**
     * Subscribes a list of currency pairs for the credit limit updates for the credit lines between the specified
     * legal entities.
     *
     * @param le1         legal entity1
     * @param le2         legal entity2
     * @param ccyPairColl currency pair collection
     */
    public void subscribeBilateralCreditLimitUpdate( LegalEntity le1, LegalEntity le2, Collection<String> ccyPairColl )
    {
        CreditLimitSubscriptionManagerC.getInstance().subscribeBilateralCreditLimitUpdate( le1, le2, ccyPairColl );
    }

    /**
     * Subscribes the currency pair for the credit limit updates for the credit lines between the specified legal entities
     * Adds given subscription to cache for periodic recalulations
     *
     * @param le1                      legal entity1
     * @param le2                      legal entity2
     * @param baseCcy                  base currency
     * @param varCcy                   variable currency
     * @param isLe1Maker               boolean representing whether le1 is maker
     * @param isServerSideProvisioning boolean returning whether its server-side provisioning
     */
    public void subscribeBilateralCreditLimitUpdate( LegalEntity le1, LegalEntity le2, Currency baseCcy, Currency varCcy, boolean isLe1Maker, boolean isServerSideProvisioning, IdcDate spotValueDate )
    {
        CreditLimitSubscriptionManagerC.getInstance().subscribeBilateralCreditLimitUpdate( le1, le2, baseCcy, varCcy, isLe1Maker, isServerSideProvisioning, spotValueDate );
    }

    /**
     * Subscribes the currency pair for the credit limit updates for the credit lines between the specified legal entities
     * Adds given subscription to cache for periodic recalulations
     *
     * @param le1                      legal entity1
     * @param le2                      legal entity2
     * @param baseCcy                  base currency
     * @param varCcy                   variable currency
     * @param isLe1Maker               boolean representing whether le1 is maker
     * @param isServerSideProvisioning boolean returning whether its server-side provisioning
     */
    public void subscribeBilateralCreditLimitUpdate( LegalEntity le1, LegalEntity le2, Currency baseCcy, Currency varCcy, boolean isLe1Maker, boolean isServerSideProvisioning, IdcDate spotValueDate, boolean handleAllLegalEntities )
    {
        CreditLimitSubscriptionManagerC.getInstance().subscribeBilateralCreditLimitUpdate( le1, le2, baseCcy, varCcy, isLe1Maker, isServerSideProvisioning, spotValueDate, handleAllLegalEntities );
    }

    /**
     * Unsubscribe the currency pair formed by base currency and variable currency for the credit limit updates for the credit lines
     * between specified .
     *
     * @param le1     legal entity1
     * @param le2     legal entity2
     * @param baseCcy base currency
     * @param varCcy  variable currency
     */
    public void unsubscribeBilateralCreditLimitUpdate( LegalEntity le1, LegalEntity le2, Currency baseCcy, Currency varCcy )
    {
        CreditLimitSubscriptionManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( le1, le2, baseCcy, varCcy );
    }

    /**
     * Unsubscribe the list of currency pairs for credit limit updates for the credit lines between specified legal entities.
     *
     * @param le1         legal entity1
     * @param le2         legal entity2
     * @param ccyPairColl currency pair collection
     */
    public void unsubscribeBilateralCreditLimitUpdate( LegalEntity le1, LegalEntity le2, Collection<String> ccyPairColl )
    {
        CreditLimitSubscriptionManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( le1, le2, ccyPairColl );
    }

    /**
     * Unsubscribe the currency pair formed by base currency and variable currency for the credit limit updates for the credit lines
     * between specified le's.
     *
     * @param le1                      legal entity 1
     * @param le2                      legal entity 2
     * @param isLe1Maker               boolean representing whether le1 is maker
     * @param baseCcy                  base currency
     * @param varCcy                   variable currency
     * @param isServerSideProvisioning boolean representing whether it is server-sdie provisioning
     */
    public void unsubscribeBilateralCreditLimitUpdate( LegalEntity le1, LegalEntity le2, Currency baseCcy, Currency varCcy, boolean isLe1Maker, boolean isServerSideProvisioning )
    {
        CreditLimitSubscriptionManagerC.getInstance().unsubscribeBilateralCreditLimitUpdate( le1, le2, baseCcy, varCcy, isLe1Maker, isServerSideProvisioning );
    }

    /**
     * Returns the available credit limit by finding lowest credit limit available in all credit types. This is a limit amount in
     * credit limit currency and represents unilateral credit by the credit provider legal enity for the credit cpty. A null value
     * indicates that credit is not enabled for this credit provider and counterparty combination. This limit represents an limit
     * amount which can be used for any currency pairs and currency pair can have greater available credit limit based on the
     * netting methodology and current currency positions.
     *
     * @param creditProviderLe credit provider legal entity
     * @param creditCpty       credit counterparty
     * @param creditCptyOrg    credit counterparty organization
     * @param settlementDate   settlement date
     * @return limit amount
     */
    public Double getAvailableCreditLimit( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, IdcDate settlementDate )
    {
        return CreditLimitSubscriptionManagerC.getInstance().getAvailableCreditLimit( creditProviderLe, creditCpty, creditCptyOrg, settlementDate );
    }

    /**
     * Returns the available credit limit by finding lowest credit limit available in all credit types. This is a limit amount in
     * credit limit currency and represents bilateral credit between these legal entities. A null value
     * indicates that credit is not enabled for these legal entities. This limit represents a limit
     * amount which can be used for any currency pairs and currency pair can have greater available credit limit based on the
     * netting methodology and current currency positions. This limit amount is in the limit currency of the first specified
     * legal entity. This is because each credit provider organization can have a limit currency which can be different from the
     * other credit provider organization.
     *
     * @param le1  legal entity1
     * @param le2  legal entity2
     * @param date date
     * @return limit amount
     */
    public Double getAvailableBilateralCreditLimit( LegalEntity le1, LegalEntity le2, IdcDate date )
    {
        return CreditLimitSubscriptionManagerC.getInstance().getAvailableBilateralCreditLimit( le1, le2, date );
    }

    /**
     * Returns the available credit limit by finding lowest credit limit available in all credit types. This is a limit amount in
     * credit limit currency and represents unilateral credit by the credit provider legal enity for the credit cpty. A null value
     * indicates that credit is not enabled for this credit provider and counterparty combination. This limit represents a limit
     * amount which can be used for any currency pairs and currency pair can have greater available credit limit based on the
     * netting methodology and current currency positions.
     *
     * @param creditProviderLe credit provider legal entity
     * @param creditCpty       credit counterparty
     * @param creditCptyOrg    credit counterparty organization
     * @param settlementDate   settlement date
     * @return limit amount and currency as an amount of instrument
     */
    public AmountOfInstrument getAvailableLimitAmountOfInstrument( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, IdcDate settlementDate )
    {
        return CreditLimitSubscriptionManagerC.getInstance().getAvailableLimitAmountOfInstrument( creditProviderLe, creditCpty, creditCptyOrg, settlementDate );
    }

    /**
     * Returns the available credit limit by finding lowest credit limit available in all credit types. This is a limit amount in
     * credit limit currency and represents bilateral credit between the specified legal entities. A null value
     * indicates that credit is not enabled between these legal entities. This limit represents a limit
     * amount which can be used for any currency pairs and currency pair can have greater available credit limit based on the
     * netting methodology and current currency positions. The amount of instrument would be having the amount specified in the
     * limit currency of the first specified legal entity.
     *
     * @param le1  legal entity1
     * @param le2  legal entity2
     * @param date date
     * @return limit amount and currency as an amount of instrument
     */
    public AmountOfInstrument getAvailableBilateralCreditLimitAmountOfInstrument( LegalEntity le1, LegalEntity le2, IdcDate date )
    {
        return CreditLimitSubscriptionManagerC.getInstance().getAvailableBilateralCreditLimitAmountOfInstrument( le1, le2, date );
    }

    /**
     * Returns the available credit limit in the base currency for the currency pair formed by base currency and variable currency.
     * This is calculated by finding lowest credit limit available in all credit types for the currency pair. This is a limit amount in
     * base currency and represents unilateral credit by the credit provider legal entity for the credit cpty. A null value
     * indicates that credit is not enabled for this credit provider and counterparty combination. This limit represents an limit
     * amount which can be used for the currency pair and currency pair can have greater available credit limit based on the
     * netting methodology and current currency positions.
     *
     * @param creditProviderLe        credit provider legal entity
     * @param creditCpty              credit counterparty
     * @param creditCptyOrg           credit counterparty organization
     * @param settlementDate          settlement date
     * @param baseCcy                 base currency
     * @param varCcy                  variable currency
     * @param isCreditProviderLeMaker credit provider maker
     * @return limit amount in base currency
     */
    public DealingLimit getAvailableBaseCurrencyCreditLimit( LegalEntity creditProviderLe, TradingParty creditCpty, Organization creditCptyOrg, IdcDate settlementDate, Currency baseCcy, Currency varCcy, boolean isCreditProviderLeMaker )
    {
        return CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyCreditLimit( creditProviderLe, creditCpty, creditCptyOrg, settlementDate, baseCcy, varCcy, isCreditProviderLeMaker, new HashMap() );
    }

    /**
     * Returns the available credit limit in the base currency for the currency pair formed by base currency and variable currency.
     * This is calculated by finding lowest credit limit available in all credit types for the currency pair. This is a limit amount in
     * base currency and represents bilateral credit between the specified legal entities. A null value
     * indicates that credit is not enabled between these legal entities. This limit represents an limit
     * amount which can be used for the currency pair and currency pair can have greater available credit limit based on the
     * netting methodology and current currency positions.
     *
     * @param le1        legal entity1
     * @param le2        legal entity2
     * @param date       date
     * @param baseCcy    base currency
     * @param varCcy     variable currency
     * @param isLe1Maker isLe1Maker
     * @return limit amount in base currency
     */
    public DealingLimit getAvailableBaseCurrencyBilateralCreditLimit( LegalEntity le1, LegalEntity le2, IdcDate date, Currency baseCcy, Currency varCcy, boolean isLe1Maker )
    {
        return CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( le1, le2, date, baseCcy, varCcy, isLe1Maker );
    }

    public DealingLimit getAvailableBaseCurrencyBilateralCreditLimit( LegalEntity le1, LegalEntity le2, IdcDate date, Currency baseCcy, Currency varCcy, boolean isLe1Maker, boolean isApplyTenorCoefficient )
    {
        return CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( le1, le2, date, baseCcy, varCcy, isLe1Maker, isApplyTenorCoefficient );
    }

    public DealingLimit getAvailableBaseCurrencyBilateralCreditLimit( LegalEntity le1, LegalEntity le2, IdcDate date, Currency baseCcy, Currency varCcy, boolean isLe1Maker, boolean isApplyTenorCoefficient, CreditWorkflowRiders riders )
    {
        return CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( le1, le2, date, baseCcy, varCcy, isLe1Maker, isApplyTenorCoefficient, riders );
    }

    public boolean getAvailableBaseCurrencyBilateralCreditLimit( LegalEntity le1, LegalEntity le2, List<CreditEntity> creditEntities, IdcDate date, Currency baseCcy, Currency varCcy, boolean isLe1Maker, double[] bidLimit, double[] offerLimit, boolean isMultiTier )
    {
        return CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( le1, le2, creditEntities, date, baseCcy, varCcy, isLe1Maker, bidLimit, offerLimit, isMultiTier );
    }
	
    /**
     * This method qualifies the incoming quote based on the credit limits. If the credit limits are higher than quote limit than the
     * quote limits (double array) is returned as-is. Else the arrays are updated with available credit limit value
     *
     * @param le1         legal entity1
     * @param le2         legal entity2
     * @param date        date
     * @param baseCcy     base currency
     * @param varCcy      variable currency
     * @param isLe1Maker  isLe1Maker
     * @param bidLimit    incoming quote bid limit array
     * @param offerLimit  incoming quote offer limit array
     * @param isMultiTier boolean representing whether it is multi-tier or multi-quote
     * @return boolean returns false if credit disabled and true if credit is enabled
     */
    public boolean getAvailableBaseCurrencyBilateralCreditLimit( LegalEntity le1, LegalEntity le2, IdcDate date, Currency baseCcy, Currency varCcy, boolean isLe1Maker, double[] bidLimit, double[] offerLimit, boolean isMultiTier )
    {
        return CreditLimitSubscriptionManagerC.getInstance().getAvailableBaseCurrencyBilateralCreditLimit( le1, le2, date, baseCcy, varCcy, isLe1Maker, bidLimit, offerLimit, isMultiTier );
    }

    public Collection<CreditUtilization> getCreditUtilizations( LegalEntity creditProviderLe, Organization creditCptyOrg, TradingParty creditCpty, IdcDate settlementDate, int eventCode )
    {
        return CreditLimitSubscriptionManagerC.getInstance().getCreditUtilizations( creditProviderLe, creditCptyOrg, creditCpty, settlementDate, eventCode );
    }

    public Collection<CreditUtilizationCacheEntry> getCreditUtilizationCacheEntries( LegalEntity creditProviderLe, Organization creditCptyOrg, TradingParty creditCpty, Entity creditEntity )
    {
        return CreditLimitSubscriptionManagerC.getInstance().getCreditUtilizationCacheEntries( creditProviderLe, creditCptyOrg, creditCpty, creditEntity );
    }

    public CreditUtilizationCacheEntry getCreditUtilizationCacheEntry( LegalEntity creditProviderLe, Organization creditCptyOrg, TradingParty creditCpty, IdcDate settlementDate, int eventCode )
    {
        return CreditLimitSubscriptionManagerC.getInstance().getCreditUtilizationCacheEntry( creditProviderLe, creditCptyOrg, creditCpty, eventCode, settlementDate);
    }

    public CreditWorkflowMessage createCreditUtilizations( LegalEntity creditProviderLe, Organization creditCptyOrg, TradingParty creditCpty, IdcDate startSettlementDate, IdcDate endSettlementDate)
    {
        return createCreditUtilizations( creditProviderLe, creditCptyOrg, creditCpty, startSettlementDate, endSettlementDate, false );
    }

    public CreditWorkflowMessage createCreditUtilizations( LegalEntity creditProviderLe, Organization creditCptyOrg, TradingParty creditCpty, IdcDate startSettlementDate, IdcDate endSettlementDate, boolean createAggregateCuOnly )
    {
        return createCreditUtilizations( creditProviderLe, creditCptyOrg, creditCpty, startSettlementDate, endSettlementDate, false, createAggregateCuOnly);
    }

    public CreditWorkflowMessage createCreditUtilizations( LegalEntity creditProviderLe, Organization creditCptyOrg, TradingParty creditCpty, IdcDate startSettlementDate, IdcDate endSettlementDate, boolean endDateCheckOnly, boolean createAggregateCUOnly )
    {
        log.info( new StringBuilder( 200 ).append( "CUM.createCreditUtilizations - Creating utilizations for tp=" )
                .append( creditCpty ).append( ", le=" ).append( creditProviderLe ).append( ", tpOrg=" ).append( creditCptyOrg ).append( ", startDate=" )
                .append( startSettlementDate ).append( ", endDate=" ).append( endSettlementDate )
                .append(", createAggregateCUOnly=").append(createAggregateCUOnly).toString() );

        StopWatchC watch = null;
        if ( LogFactory.isTimingEnabled( this.getClass() ) )
        {
            watch = LogFactory.getStopWatch( this.getClass(), "CUM.createCreditUtilizations" );
        }

        Organization creditProviderOrg = creditProviderLe.getOrganization();
        TradingParty tp = CounterpartyUtilC.getTradingParty( creditCpty, creditProviderOrg );
        Collection<CounterpartyCreditLimitRule> cptyRules = CreditUtilC.getAllCounterpartyCreditLimitRulesForTradingParty( creditProviderOrg, tp, false );
        if ( cptyRules == null || cptyRules.isEmpty() )
        {
            log.info( new StringBuilder( 200 ).append( "CUM.createCreditUtilizations - No cpty rules for tp=" )
                    .append( creditCpty ).append( ",le=" ).append( creditProviderLe ).append( ",tpOrg=" ).append( creditCptyOrg ).toString() );
            return null;
        }

        Collection<CreditLimitRule> creditLimitRules = new ArrayList<CreditLimitRule>( 4 );
        for ( CounterpartyCreditLimitRule cclr : cptyRules )
        {
            creditLimitRules.addAll( cclr.getChildrenRules() );
        }

        if ( creditLimitRules.isEmpty() )
        {
            log.info( new StringBuilder( 200 ).append( "CUM.createCreditUtilizations : No credit limit rules matched for creditProviderOrg=" )
                    .append( creditProviderOrg ).append( ",creditCptyOrg=" ).append( creditCptyOrg ).append( "creditCpty=" ).append( creditCpty ).toString() );
            return null;
        }
        for ( Rule rule : creditLimitRules )
        {
            CreditLimitRule creditLimitRule = ( CreditLimitRule ) rule;
            if(createAggregateCUOnly)
            {
                if(creditLimitRule instanceof SingleCreditLimitRule)
                {
                    createCreditUtilizations( creditLimitRule, startSettlementDate, endSettlementDate, creditProviderOrg, endDateCheckOnly );
                }
            }
            else
            {
                createCreditUtilizations( creditLimitRule, startSettlementDate, endSettlementDate, creditProviderOrg, endDateCheckOnly );
            }
        }

        if ( watch != null )
        {
            watch.stopAndLog();
        }
        if ( log.isDebugEnabled() )
        {
            log.debug( "CUM.createCreditUtilizations.DEBUG - end. tp=" + tp );
        }

        // Call the Remote functors again here just to work around any race condition and may arise between the CU creation task and the RemoteFunctor execution
        Vector<Class> cclrUpdateReadOnlyClasses = new Vector<Class>(getReadOnlyClasses());
        cclrUpdateReadOnlyClasses.remove( com.integral.finance.creditLimit.CounterpartyCreditLimitRuleC.class );
        IdcTransaction tx = CreditUtilC.startTransaction( cclrUpdateReadOnlyClasses );
        //The following loop is just to dirty one of the cclr so that transaction commits.
        for ( CounterpartyCreditLimitRule cclr : cptyRules )
        {
            CounterpartyCreditLimitRule registeredCclr = (CounterpartyCreditLimitRule)cclr.getRegisteredObject();
            registeredCclr.update();
            break;
        }
        HashMap<String, String> propMap = new HashMap<String, String>( 3 );
        propMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, creditProviderOrg.getGUID() );
        propMap.put( CreditLimit.CREDIT_COUNTERPARTY_ORGANIZATION, creditCptyOrg.getGUID() );
        propMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.SETCREDITRELATIONSHIP.getName() );
        tx.addRemoteFunctor(CreditEnableNotificationFunctorC.class.getName(), propMap);
        CreditUtilC.endTransaction( tx );

        return null;
    }

    /**
     * Returns the credit utilization for the specified date. If the credit limit rule is of type daily credit
     * limit rule, this method returns the daily credit utilization for that date. If the rule is of type Single
     * credit limit, this returns the only one credit utilization for the rule.
     *
     * @param clr  credit limit rule
     * @param date settlement date
     * @return credit utilization
     */
    public CreditUtilization getCreditUtilizationForDate( CreditLimitRule clr, IdcDate date)
    {
        return getCreditUtilizationForDate ( false, clr, date, null );
    }

    /**
     * Returns the credit utilization for the specified date. If the credit limit rule is of type daily credit
     * limit rule, this method returns the daily credit utilization for that date. If the rule is of type Single
     * credit limit, this returns the only one credit utilization for the rule.
     * @param logFetch log fetching from database
     * @param clr  credit limit rule
     * @param date settlement date
     * @return credit utilization
     */
    public CreditUtilization getCreditUtilizationForDate( boolean logFetch, CreditLimitRule clr, IdcDate date, String context )
    {
        CounterpartyCreditLimitRule cclr = (CounterpartyCreditLimitRule)clr.getParentRule();
        String ccoShortName = cclr.getTradingPartyOrganization().getShortName();
        String cpoShortName = clr.getNamespace().getShortName();
        TradingParty creditCpty = cclr.getTradingParty();

        //Lookup cache first. If not found, then query for CU.
        CreditUtilization cu =  getCreditUtilizationCache().getCreditUtilizationFromCache(
                cpoShortName, ccoShortName, creditCpty!=null?creditCpty.getObjectId():null, date, !(clr instanceof DailyCreditLimitRule));

        if(cu==null)
        {
            cu = CreditDataQueryServiceFactory.getCreditDataQueryService().getCreditUtilizationForDate( clr, date );
            if ( logFetch && clr instanceof DailyCreditLimitRuleC )
            {
                log.info( new StringBuilder( 200 ).append( "CUM.getCreditUtilizationForDate : Fetch CU from database for date=" )
                        .append( date ).append( ",cpo=" ).append( cpoShortName ).append ( ",cco" ).append (  ccoShortName )
                        .append ( ",cc=" ).append (  creditCpty ).append ( ",clr=" ).append ( clr ).append ( ",cu=" )
                        .append ( cu ).append ( ",context=" ).append ( context ).toString() );
            }
        }

        return cu;
    }


    public CreditWorkflowMessage recalculateAllAggregateCreditUtilizations( IdcDate businessDate )
    {
        return recalculateAllAggregateCreditUtilizations( businessDate, CreditFunctorServerC.getInstance().getStandAloneCreditFunctor( CreditFunctorServerC.REVALUATE_AGGREGATE_CREDIT_UTILIZATION ) );
    }

    public CreditWorkflowMessage recalculateAllAggregateCreditUtilizationsForNextDate()
    {
        IdcDate tradeDate = EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ();
        return recalculateAllAggregateCreditUtilizationsForNextDate( tradeDate.addDays ( 1 ), CreditFunctorServerC.getInstance().getStandAloneCreditFunctor( CreditFunctorServerC.PRECALCULATE_AGGREGATE_CREDIT_UTILIZATION ) );
    }

    /**
     * Recalculates all the aggregate type credit utilizations belonging to single credit limit rule rule viz GrossNotional type rules.
     *
     * @param businessDate  business date
     * @param creditFunctor credit functor
     * @return credit workflow message
     */
    public CreditWorkflowMessage recalculateAllAggregateCreditUtilizationsForNextDate( IdcDate businessDate, CreditFunctor creditFunctor )
    {
        if ( creditFunctor != null && !creditFunctor.isEnabled() )
        {
            log.info( "CUM.recalculateAllAggregateCreditUtilizationsForNextDate : Disabled Functor=" + creditFunctor );
            return null;
        }

        log.info( new StringBuilder( 200 ).append( "CUM.recalculateAllAggregateCreditUtilizationsForNextDate : Begin. businessDate=" )
                .append( businessDate ).append( ",functor=" ).append( creditFunctor ).toString() );


        long t0 = System.currentTimeMillis();
        int num = 0;
        int totalSingleCreditUtilsUpdated = 0;
        CursoredStream cursor = null;

        try
        {
            Session session = PersistenceFactory.newSession();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression singleCLRExpr = eb.get( "date" ).isNull();
            Expression eventsExistsExpr = eb.get( "usedAmount" ).notEqual( 0 );
            Expression expr = singleCLRExpr.and( eventsExistsExpr );

            ReadAllQuery raq = new ReadAllQuery( eb );
            raq.setReferenceClass( CreditUtilization.class );
            raq.useCursoredStream( COMMIT_BATCH_SIZE, COMMIT_BATCH_SIZE );
            raq.setSelectionCriteria( expr );
            cursor = ( CursoredStream ) session.executeQuery( raq );
            Map<String, Collection<CreditUtilization>> orgName2creditUtilList = new HashMap<String, Collection<CreditUtilization>>();

            while ( !cursor.atEnd() )
            {
                if ( creditFunctor != null && !creditFunctor.isEnabled() )
                {
                    log.info( "CUM.recalculateAllAggregateCreditUtilizationsForNextDate : Disabled Functor=" + creditFunctor );
                    cursor.close();
                    return null;
                }
                CreditUtilization cu = ( CreditUtilization ) cursor.read();
                if ( cu == null || !cu.isActive() || cu.getCreditLimitRule() == null )
                {
                    log.warn( "CUM.recalculateAllAggregateCreditUtilizationsForNextDate : credit util is either null or does not have a rule." + cu );
                    continue;
                }
                if ( cu.getCreditLimitRule().getCreditUtilizationCalculator() == null )
                {
                    continue;
                }

                // if counterparty credit limit rule is inactive, then skip the re-calculation.
                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule().getParentRule();
                if ( cclr == null || !cclr.isActive() )
                {
                    log.info("CUM.recalculateAllAggregateCreditUtilizationsForNextDate : skipping as cpty credit limit rule is either null or inactive for cu=" + cu + ",cclr=" + cclr );
                    continue;
                }

                if ( CreditUtilC.isCashSettlementWithoutLimitCheckEnabled ( cu.getCreditLimitRule () ) )
                {
                    log.info("CUM.recalculateAllAggregateCreditUtilizationsForNextDate : skipping as cash settlement type. cu=" + cu + ",cclr=" + cclr );
                    continue;
                }

                String cuNamespace = cu.getNamespace().getShortName();
                Collection<CreditUtilization> cuList = orgName2creditUtilList.get( cuNamespace );
                if(cuList==null){
                    cuList = new ArrayList<CreditUtilization>();
                    orgName2creditUtilList.put( cuNamespace, cuList );
                }
                cuList.add( cu );

                num++;
                totalSingleCreditUtilsUpdated++;

                if ( num >= COMMIT_BATCH_SIZE )
                {
                    recalculateCreditUtilizationCollectionForNextDate( session, orgName2creditUtilList, businessDate);
                    num = 0;
                    orgName2creditUtilList.clear();
                    cursor.releasePrevious();
                }
            }

            if ( num > 0 )
            {
                recalculateCreditUtilizationCollectionForNextDate( session, orgName2creditUtilList,  businessDate );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUM.recalculateAllAggregateCreditUtilizationsForNextDate - Exception while recalculating all single credit utilizations. businessDate=" + businessDate, e );
        }
        finally
        {
            if ( cursor != null )
            {
                cursor.close();
            }
        }

        log.info( new StringBuilder( 200 ).append( "CUM.recalculateAllAggregateCreditUtilizationsForNextDate : Finished. businessDate=" )
                .append( businessDate ).append( ",count=" ).append( totalSingleCreditUtilsUpdated ).append( " took ms=" )
                .append( System.currentTimeMillis() - t0 ).toString() );
        return null;
    }


    /**
     * Recalculates all the aggregate type credit utilizations belonging to single credit limit rule rule viz GrossNotional type rules.
     *
     * @param businessDate  business date
     * @param creditFunctor credit functor
     * @return credit workflow message
     */
    public CreditWorkflowMessage recalculateAllAggregateCreditUtilizations( IdcDate businessDate, CreditFunctor creditFunctor )
    {
        if ( creditFunctor != null && !creditFunctor.isEnabled() )
        {
            log.info( "CUM.recalculateAllAggregateCreditUtilizations : Disabled Functor=" + creditFunctor );
            return null;
        }

        log.info( new StringBuilder( 200 ).append( "CUM.recalculateAllAggregateCreditUtilizations : Begin. businessDate=" )
                .append( businessDate ).append( ",functor=" ).append( creditFunctor ).toString() );


        long t0 = System.currentTimeMillis();
        int num = 0;
        int totalSingleCreditUtilsUpdated = 0;
        CursoredStream cursor = null;
        Map<String, Collection<CreditUtilization>> orgName2PLUpdateList = new HashMap<String, Collection<CreditUtilization>>();

        try
        {
            Session session = PersistenceFactory.newSession();
            ExpressionBuilder eb = new ExpressionBuilder();
            Expression singleCLRExpr = eb.get( "date" ).isNull();
            Expression eventsExistsExpr = eb.get( "usedAmount" ).notEqual( 0 );
            Expression expr = singleCLRExpr.and( eventsExistsExpr );

            ReadAllQuery raq = new ReadAllQuery( eb );
            raq.setReferenceClass( CreditUtilization.class );
            raq.useCursoredStream( COMMIT_BATCH_SIZE, COMMIT_BATCH_SIZE );
            raq.setSelectionCriteria( expr );
            cursor = ( CursoredStream ) session.executeQuery( raq );
            Map<String, Collection<CreditUtilization>> orgName2creditUtilList = new HashMap<String, Collection<CreditUtilization>>();

            while ( !cursor.atEnd() )
            {
                if ( creditFunctor != null && !creditFunctor.isEnabled() )
                {
                    log.info( "CUM.recalculateAllAggregateCreditUtilizations : Disabled Functor=" + creditFunctor );
                    cursor.close();
                    return null;
                }
                CreditUtilization cu = ( CreditUtilization ) cursor.read();
                if ( cu == null || !cu.isActive() || cu.getCreditLimitRule() == null )
                {
                    log.warn( "CUM.recalculateAllAggregateCreditUtilizations : credit util is either null or does not have a rule." + cu );
                    continue;
                }
                if ( cu.getCreditLimitRule().getCreditUtilizationCalculator() == null )
                {
                    continue;
                }

                // if counterparty credit limit rule is inactive, then skip the re-calculation.
                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule().getParentRule();
                if ( cclr == null || !cclr.isActive() )
                {
                    log.info("CUM.recalculateAllAggregateCreditUtilizations : skipping as cpty credit limit rule is either null or inactive for cu=" + cu + ",cclr=" + cclr );
                    continue;
                }

                if ( CreditUtilC.isCashSettlementWithoutLimitCheckEnabled ( cu.getCreditLimitRule () ) )
                {
                    log.info("CUM.recalculateAllAggregateCreditUtilizations : skipping as cash settlement type. cu=" + cu + ",cclr=" + cclr );
                    continue;
                }

                String cuNamespace = cu.getNamespace().getShortName();
                Collection<CreditUtilization> cuList = orgName2creditUtilList.get( cuNamespace );
                if(cuList==null){
                    cuList = new ArrayList<CreditUtilization>();
                    orgName2creditUtilList.put( cuNamespace, cuList );
                }
                cuList.add( cu );

                num++;
                totalSingleCreditUtilsUpdated++;

                // update the list of PL balance update if apply P/L and Update EOD P/L is enabled.
                if ( cu.isApplyPandL() && cu.isUpdateBalanceWithPL() )
                {
                    Collection<CreditUtilization> plUpdateList = orgName2PLUpdateList.get( cuNamespace );
                    if(plUpdateList==null)
                    {
                        plUpdateList = new HashSet<CreditUtilization>(  );
                        orgName2PLUpdateList.put( cuNamespace, plUpdateList );
                    }
                    plUpdateList.add( cu );
                }

                if ( num >= COMMIT_BATCH_SIZE )
                {
                    recalculateCreditUtilizationCollection( session, orgName2creditUtilList, true, false );
                    num = 0;
                    orgName2creditUtilList.clear();
                    cursor.releasePrevious();
                }
            }

            if ( num > 0 )
            {
                recalculateCreditUtilizationCollection( session, orgName2creditUtilList, true, false );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUM.recalculateAllAggregateCreditUtilizations - Exception while recalculating all single credit utilizations. businessDate=" + businessDate, e );
        }
        finally
        {
            if ( cursor != null )
            {
                cursor.close();
            }
        }

        try
        {
            // update the P&L in the credit balance.
            if ( !orgName2PLUpdateList.isEmpty() )
            {
                updateCreditBalanceAggregateCreditUtilizations( orgName2PLUpdateList, businessDate );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUM.recalculateAllAggregateCreditUtilizations - Exception while updating P/L at end of day. businessDate=" + businessDate, e );
        }

        if ( log.isDebugEnabled() )
        {
            log.debug( "CUM.recalculateAllAggregateCreditUtilizations.DEBUG - end. Total updated : " + totalSingleCreditUtilsUpdated );
        }

        log.info( new StringBuilder( 200 ).append( "CUM.recalculateAllAggregateCreditUtilizations : Finished. businessDate=" )
                .append( businessDate ).append( ",count=" ).append( totalSingleCreditUtilsUpdated ).append( " took ms=" )
                .append( System.currentTimeMillis() - t0 ).toString() );
        return null;
    }

    /**
     * Recalculates the credit utilizations. used by devapp pages
     *
     * @param creditUtils credit utils
     */
    public void recalculateCreditUtilizations( Collection<CreditUtilization> creditUtils )
    {
        log.info( new StringBuilder( 200 ).append( "CUM.recalculateCreditUtilizations : recalculating credit utils=" )
                .append( creditUtils ).toString() );


        long t0 = System.currentTimeMillis();

        try
        {
            Map<String, Collection<CreditUtilization>> orgName2creditUtilList = new HashMap<String, Collection<CreditUtilization>>();

            for ( CreditUtilization cu: creditUtils )
            {
                if ( cu == null || !cu.isActive() || cu.getCreditLimitRule() == null )
                {
                    log.warn( "CUM.recalculateCreditUtilizations : credit util is either null or does not have a rule." + cu );
                    continue;
                }
                if ( cu.getCreditLimitRule().getCreditUtilizationCalculator() == null )
                {
                    continue;
                }

                // if counterparty credit limit rule is inactive, then skip the re-calculation.
                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule().getParentRule();
                if ( cclr == null || !cclr.isActive() )
                {
                    log.info("CUM.recalculateCreditUtilizations : skipping as cpty credit limit rule is either null or inactive for cu=" + cu + ",cclr=" + cclr );
                    continue;
                }

                String cuNamespace = cu.getNamespace().getShortName();
                Collection<CreditUtilization> cuList = orgName2creditUtilList.get( cuNamespace );
                if( cuList == null )
                {
                    cuList = new ArrayList<CreditUtilization>();
                    orgName2creditUtilList.put( cuNamespace, cuList );
                }
                cuList.add( cu );
            }

            if ( creditUtils.size () > 0 )
            {
                recalculateCreditUtilizationCollection( PersistenceFactory.newSession (), orgName2creditUtilList, true, false );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUM.recalculateCreditUtilizations - Exception while recalculating the credit utils=" + creditUtils, e );
        }

        log.info( new StringBuilder( 200 ).append( "CUM.recalculateCreditUtilizations : Finished recalculating creditUtils=" )
                .append( creditUtils ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
    }

    /**
     * precalculates the next date positions for the credit utilizations. used by devapp pages
     *
     * @param creditUtils credit utils
     */
    public void precalculateNextDatePositionsForCreditUtilizations( Collection<CreditUtilization> creditUtils )
    {
        log.info( new StringBuilder( 200 ).append( "CUM.precalculateNextDatePositionsForCreditUtilizations : pre-calculating the next date positions for credit utils=" )
                .append( creditUtils ).toString() );


        long t0 = System.currentTimeMillis();
        IdcDate businessDate = EndOfDayServiceFactory.getEndOfDayService ().getCurrentTradeDate ();
        IdcDate targetDate = businessDate.addDays ( 1 );
        try
        {
            Map<String, Collection<CreditUtilization>> orgName2creditUtilList = new HashMap<String, Collection<CreditUtilization>>();

            for ( CreditUtilization cu: creditUtils )
            {
                if ( cu == null || !cu.isActive() || cu.getCreditLimitRule() == null )
                {
                    log.warn( "CUM.precalculateNextDatePositionsForCreditUtilizations : credit util is either null or does not have a rule." + cu );
                    continue;
                }
                if ( cu.getCreditLimitRule().getCreditUtilizationCalculator() == null )
                {
                    continue;
                }

                // if counterparty credit limit rule is inactive, then skip the re-calculation.
                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule().getParentRule();
                if ( cclr == null || !cclr.isActive() )
                {
                    log.info("CUM.precalculateNextDatePositionsForCreditUtilizations : skipping as cpty credit limit rule is either null or inactive for cu=" + cu + ",cclr=" + cclr );
                    continue;
                }

                String cuNamespace = cu.getNamespace().getShortName();
                Collection<CreditUtilization> cuList = orgName2creditUtilList.get( cuNamespace );
                if( cuList == null )
                {
                    cuList = new ArrayList<CreditUtilization>();
                    orgName2creditUtilList.put( cuNamespace, cuList );
                }
                cuList.add( cu );
            }

            if ( creditUtils.size () > 0 )
            {
                recalculateCreditUtilizationCollectionForNextDate ( PersistenceFactory.newSession (), orgName2creditUtilList,targetDate );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUM.precalculateNextDatePositionsForCreditUtilizations - Exception while pre-calculating next date positions for the credit utils=" + creditUtils, e );
        }

        log.info( new StringBuilder( 200 ).append( "CUM.precalculateNextDatePositionsForCreditUtilizations : Finished precalculating next date positions creditUtils=" )
                .append( creditUtils ).append( " took ms=" ).append( System.currentTimeMillis() - t0 ).toString() );
    }

    /**
     * Recalculates all the aggregate type credit utilizations belonging to single credit limit rule rule viz GrossNotional type rules.
     *
     * @param orgName2CreditUtils  credit utils by namespace
     * @param businessDate business date
     * @return credit workflow message
     */
    private void updateCreditBalanceAggregateCreditUtilizations( Map<String, Collection<CreditUtilization>> orgName2CreditUtils, IdcDate businessDate )
    {
        log.info( new StringBuilder( 200 ).append( "CUM.updateCreditBalanceAggregateCreditUtilizations : Begin. businessDate=" )
                .append( businessDate ).toString() );

        for(Map.Entry<String, Collection<CreditUtilization>> entry : orgName2CreditUtils.entrySet())
        {
            if( CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationPersistenceOracleEnabled( entry.getKey() ))
            {
                updateCreditBalanceAggregateCreditUtilizationsIntoOracle( entry.getValue(), businessDate);
            }
            if( CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationPersistenceSpacesEnabled( entry.getKey() ))
            {
                updateCreditBalanceAggregateCreditUtilizationsIntoRDS( entry.getValue(), businessDate);
            }
        }
    }

    private CreditWorkflowMessage updateCreditBalanceAggregateCreditUtilizationsIntoRDS( Collection<CreditUtilization> creditUtils, IdcDate businessDate)
    {
        try
        {
            if ( creditUtils == null || creditUtils.isEmpty() )
            {
                return null;
            }
            for ( CreditUtilization aggregateCu : creditUtils )
            {
                updateCreditBalance( aggregateCu, businessDate );
                CreditDataQueryServiceFactory.getCreditReferenceDataService().createOrUpdateCreditUtilization( aggregateCu );
            }
        }
        catch ( Exception e )
        {
            log.error( new StringBuilder( 200 ).append( "CUM.updateCreditBalanceAggregateCreditUtilizationsIntoRDS : Exception while updating balance. businessDate=" )
                    .append( businessDate ).append( ",cus=" ).append( creditUtils ).toString(), e );
        }
        return null;
    }

    private CreditWorkflowMessage updateCreditBalanceAggregateCreditUtilizationsIntoOracle( Collection<CreditUtilization> creditUtils, IdcDate businessDate)
    {
        long t0 = System.currentTimeMillis();
        IdcSessionManager.getInstance().setTransaction( null );

        updateCreditBalance( creditUtils, businessDate );

        if ( log.isDebugEnabled() )
        {
            log.debug( "CUM.updateCreditBalanceAggregateCreditUtilizationsIntoOracle.- end. Total updated : " + creditUtils.size() );
        }

        log.info( new StringBuilder( 200 ).append( "CUM.updateCreditBalanceAggregateCreditUtilizationsIntoOracle : Finished. businessDate=" )
                .append( businessDate ).append( ",count=" ).append( creditUtils.size() ).append( " took ms=" )
                .append( System.currentTimeMillis() - t0 ).toString() );
        return null;
    }

    private void updateCreditBalance( Collection<CreditUtilization> creditUtils, IdcDate businessDate )
    {
        try
        {
            if ( creditUtils == null || creditUtils.isEmpty() )
            {
                return;
            }
            for ( CreditUtilization aggregateCu : creditUtils )
            {
                try
                {
                    IdcTransaction tx = CreditUtilC.startTransaction( getCreditLimitBalanceUpdateReadOnlyClasses() );
                    updateCreditBalance( aggregateCu, businessDate );
                    tx.commit();
                }
                catch ( IdcOptimisticLockException ole )
                {
                    log.info( "CUM.updateCreditBalance : OptimisticLockException while updating P/L." );
                    updateCreditBalance( aggregateCu, businessDate );
                }
            }
        }
        catch ( Exception e )
        {
            log.error( new StringBuilder( 200 ).append( "CUM.updateCreditBalance : Exception while updating balance. businessDate=" )
                    .append( businessDate ).append( ",cus=" ).append( creditUtils ).toString(), e );
        }
    }

    private void updateCreditBalance( CreditUtilization aggregateCu, IdcDate businessDate )
    {
        try
        {
            if ( aggregateCu != null )
            {
                // extract all the information needed for P/L calculation from the aggregate type credit utilization.
                Map<String, FXPrice> rateMap = new HashMap<String, FXPrice>();
                CreditLimitRule clr = aggregateCu.getCreditLimitRule();
                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) clr.getParentRule();
                CreditLimitRuleSet clrs = ( CreditLimitRuleSet ) cclr.getRuleSet();
                Organization creditProviderOrg = clrs.getOrganization();
                FXMarketDataSet fxMds = ( FXMarketDataSet ) CreditUtilC.getMarketDataSet( creditProviderOrg );
                TradingParty creditTp = cclr.getTradingParty();
                if ( creditTp == null )
                {
                    creditTp = CounterpartyUtilC.getTradingParty( CreditUtilC.getDefaultlegalEntity( cclr.getTradingPartyOrganization() ), creditProviderOrg );
                }

                // determine the settled positions date for calculating the P/L for the just settled positions.
                IdcDate lastPositionDate = aggregateCu.getCreditLimitRule().getPLPositionDate();
                IdcDate settledPosEndDate = aggregateCu.ignoreCurrentDatePositions() && !aggregateCu.isDailyPL() ? businessDate : businessDate.subtractDays( 1 );
                IdcDate settledPosStartDate = settledPosEndDate;
                IdcDate lastUpdatedDate = aggregateCu.getCreditLimitRule().getLastBalanceUpdateWithPLDate();

                //If the same settled position date is already updated, then skip the update.
                boolean endOfDayAlreadyUpdated = lastUpdatedDate != null && lastUpdatedDate.isLaterThanOrEqualTo( businessDate );
                boolean positionDateAlreadyConsidered = lastPositionDate != null && lastPositionDate.isLaterThanOrEqualTo( settledPosStartDate );
                if ( endOfDayAlreadyUpdated || positionDateAlreadyConsidered )
                {
                    log.info( new StringBuilder( 200 ).append( "CUM.updateCreditBalance : Skip updating credit limit balance with P&L for cu=" )
                            .append( aggregateCu ).append( ",businessDate=" ).append( businessDate ).append( ",lastPosDate=" )
                            .append( lastPositionDate ).append( ",settledPosStartDate=" ).append( settledPosStartDate )
                            .append( ",settledPosEndDate=" ).append( settledPosEndDate ).append( ",creditTp=" )
                            .append( creditTp ).append( ",lastUpdatedDate=" ).append( lastUpdatedDate ).toString() );
                    return;
                }

                // calculate the latest P/L based on the current positions. validate the current position's base date. If base date is previous to current business date, reset and rebuild currency positions.
                CurrencyPositionCollection currentCps = aggregateCu.getCurrencyPositions();
                if ( currentCps != null && currentCps.getBaseDate().isEarlierThan( businessDate ) )
                {
                    log.info( new StringBuilder( 200 ).append( "CUM.updateCreditBalance : Current position's base date is older than current business date for cu=" )
                            .append( aggregateCu ).append( ",businessDate=" ).append( businessDate ).append( ",currentCps.baseDate=" )
                            .append( currentCps.getBaseDate() ).toString() );
                    aggregateCu.rebuildCurrencyPositions( CreditLimitConstants.RESET_CACHE_SYNC_MISC, true );
                    currentCps = aggregateCu.getCurrencyPositions();
                }
                double[] currentNetAmounts = currentCps.getNetAmounts( aggregateCu.getCurrency(), fxMds, rateMap );
                double currentPL = currentCps.getProfitAndLoss( currentNetAmounts[CurrencyPositionCollection.PANDL_NET_RECEIVABLE], currentNetAmounts[CurrencyPositionCollection.PANDL_NET_PAYABLE], true );


                if ( lastPositionDate != null && lastPositionDate.isEarlierThan( settledPosStartDate.subtractDays( 1 ) ) )
                {
                    settledPosStartDate = lastPositionDate.addDays( 1 );
                }

                // calculate the P/L for the settled as well as open positions. P/L for settled positions would be difference between these P/L values.
                // The same rate map is passed to the new calculation so that rates are consistent.
                CurrencyPositionCollection cpsWithSettledPositions = aggregateCu.getCreditLimitRule().getCreditUtilizationCalculator().getCurrencyPositions( aggregateCu, aggregateCu.ignoreCurrentDatePositions() && !aggregateCu.isDailyPL() ? settledPosStartDate.subtractDays( 1 ) : settledPosStartDate );
                double[] netAmounts = cpsWithSettledPositions.getNetAmounts( aggregateCu.getCurrency(), fxMds, rateMap );
                double pLWithSettledPos = cpsWithSettledPositions.getProfitAndLoss( netAmounts[CurrencyPositionCollection.PANDL_NET_RECEIVABLE], netAmounts[CurrencyPositionCollection.PANDL_NET_PAYABLE], true );
                double settledPosPL = pLWithSettledPos - currentPL;
                log.info( new StringBuilder( 200 ).append( "CUM.updateCreditBalance : Updating credit limit balance with P&L for cu=" )
                        .append( aggregateCu ).append( ",businessDate=" ).append( businessDate ).append( ",currentP&L=" ).append( currentPL )
                        .append( ",lastPosDate=" ).append( lastPositionDate ).append( ",settledPosStartDate=" ).append( settledPosStartDate )
                        .append( ",pLWithSettledPos=" ).append( pLWithSettledPos ).append( ",settledPosPL=" ).append( settledPosPL )
                        .append( ",creditTp=" ).append( creditTp ).append( ",currentCps=" ).append( currentCps ).append( ",cpsWithSettled=" )
                        .append( cpsWithSettledPositions ).append( ",settledPosEndDate=" ).append( settledPosEndDate ).append( ",rateMap=" )
                        .append( cpsWithSettledPositions.getMarketRates( rateMap ) ).toString() );

                // call the admin service to update the credit limit and auditing and updates.
                CreditLimitAdminServiceFactory.getCreditLimitAdminService().updateCreditBalanceWithProfitAndLoss( creditProviderOrg, creditTp, clr.getClassification(), settledPosPL, settledPosEndDate );
            }
        }
        catch ( Exception e )
        {
            log.error( new StringBuilder( 200 ).append( "CUM.updateCreditBalance : Exception while updating balance. businessDate=" )
                    .append( businessDate ).append( ",cu=" ).append( aggregateCu ).toString(), e );
        }
    }

    public CreditUtilizationCacheEntry getAggregateCreditUtililzations( Organization creditProviderOrg )
    {
        CreditUtilizationCacheEntry cce = getCreditUtilizationCache().getAggregateCreditUtilizationCollectionCacheEntry( creditProviderOrg );
        if ( cce == null )
        {
            Collection<CreditUtilization> cus = CreditUtilC.getAggregateCreditUtilizations( creditProviderOrg );
            Collection<CreditUtilization> cusInCache = getCreditUtilizationCache().getAllCreditUtilizations();
            Collection<CreditUtilization> cuList = new ArrayList<CreditUtilization>( cus );
            for ( CreditUtilization cu : cus )
            {
                // handle re-initialize situations
                if ( cu.getCreditLimitRule () == null || cu.getCreditLimitRule ().getParentRule () == null )
                {
                    log.info ( "CUC.getAggregateCreditUtililzations : no credit limit rule or cpty rule found for cu=" + cu + ",clr=" + cu.getCreditLimitRule () );
                    cuList.remove ( cu );
                    continue;
                }

                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule().getParentRule();
                CreditLimitRuleSet clrs = ( CreditLimitRuleSet ) cclr.getRuleSet();
                if ( !clrs.isEnabled() || !cclr.isEnabled() )
                {
                    cuList.remove( cu );
                    continue;
                }
                if ( cusInCache.contains( cu ) )
                {
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "CUM.getAggregateCreditUtililzations : cu exists in the cache. cu=" + cu );
                    }
                    cu.getCurrencyPositions( true );
                }
                else
                {
                    cu.rebuildCurrencyPositions( CreditLimitConstants.RESET_CACHE_ENTRY_AGGREGATE, true );
                }
                cu.recalculateRealtimeCreditUtilization();
            }
            cce = new CreditUtilizationCacheEntryC( null, cuList, null, creditProviderOrg, true );
            getCreditUtilizationCache().addAggregateCreditUtilizationCollectionCacheEntry( cce, creditProviderOrg );
        }
        return cce;
    }

    public void updateAggregateCreditUtilizationCacheEntry( Organization creditProviderOrg, String eventName )
    {
        CreditUtilizationCacheEntry cce = getCreditUtilizationCache().getAggregateCreditUtilizationCollectionCacheEntry( creditProviderOrg );
        if ( cce != null )
        {
            Collection<CreditUtilization> cus = CreditUtilC.getAggregateCreditUtilizations( creditProviderOrg );
            Collection<CreditUtilization> cusInCache = cce.getCreditUtilizations();
            Collection<CreditUtilization> cuList = new ArrayList<CreditUtilization>( cus );
            for ( CreditUtilization cu : cus )
            {
                // handle re-initialize use cases
                if ( cu == null || cu.getCreditLimitRule () == null || cu.getCreditLimitRule ().getParentRule () == null )
                {
                    log.info ( "CUC.updateAggregateCreditUtilizationCacheEntry : no credit limit rule or cpty rule found for cu=" + cu + ",clr=" +  ( cu != null ? cu.getCreditLimitRule () : "NULL CU" ) );
                    if ( cu != null )
                    {
                        cuList.remove ( cu );
                    }
                    continue;
                }

                CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule().getParentRule();
                CreditLimitRuleSet clrs = ( CreditLimitRuleSet ) cclr.getRuleSet();
                if ( !clrs.isEnabled() || !cclr.isEnabled() )
                {
                    cuList.remove( cu );
                    continue;
                }
                if ( cusInCache != null && cusInCache.contains( cu ) )
                {
                    if ( log.isDebugEnabled() )
                    {
                        log.debug( "CUM.updateAggregateCreditUtilizationCacheEntry : cu exists in the cache. cu=" + cu + ",eventName=" + eventName );
                    }
                    cu.getCurrencyPositions( true );
                }
                else
                {
                    cu.rebuildCurrencyPositions( CreditLimitConstants.RESET_CACHE_ENTRY_AGGREGATE, true );
                }
                cu.recalculateRealtimeCreditUtilization();
            }
            cce = new CreditUtilizationCacheEntryC( null, cuList, null, creditProviderOrg, true );
            getCreditUtilizationCache().addAggregateCreditUtilizationCollectionCacheEntry( cce, creditProviderOrg );
        }
    }

    /**
     * Recalculates all the credit utilization events where credit tenor profile is used.
     *
     * @param businessDate  business date
     * @param creditFunctor credit functor
     * @param orgsList      org list
     * @return credit workflow message
     */
    public CreditWorkflowMessage recalculateAllCreditUtilizationEvents( IdcDate businessDate, CreditFunctor creditFunctor, Collection<Organization> orgsList )
    {
        if ( creditFunctor != null && !creditFunctor.isEnabled() )
        {
            log.info( "CUM.recalculateAllCreditUtilizationEvents : Disabled Functor=" + creditFunctor );
            return null;
        }

        if ( orgsList == null || orgsList.isEmpty() )
        {
            log.info( "CUM.recalculateAllCreditUtilizationEvents : No org is configured with credit tenor profile." );
            return null;
        }

        log.info( new StringBuilder( 200 ).append( "CUM.recalculateAllCreditUtilizationEvents : Begin. businessDate=" )
                .append( businessDate ).append( ",functor=" ).append( creditFunctor ).append( ",orgsList=" ).append( orgsList ).toString() );

        long t0 = System.currentTimeMillis();
        int totalCueCount = 0;
        try
        {
            Session session = PersistenceFactory.newSession();
            for ( Organization org : orgsList )
            {
                ExpressionBuilder eb = new ExpressionBuilder();
                Expression expr = eb.get( Entity.Status ).equal( Entity.ACTIVE_STATUS );
                expr = expr.and( eb.get( Entity.Namespace ).equal( org.getNamespace()));

                ReadAllQuery raq = new ReadAllQuery( eb );
                raq.setReferenceClass( CreditUtilizationEvent.class );
                raq.setSelectionCriteria( expr );
                Collection<CreditUtilizationEvent> creditUtilEventList = ( Collection<CreditUtilizationEvent> ) session.executeQuery( raq );
                totalCueCount+=creditUtilEventList.size();
                recalculateCreditUtilizationEventCollection( session, creditUtilEventList);
            }
            recalculateSpacesCreditUtilizationEvents( session, orgsList);

            if ( !orgsList.isEmpty() )
            {
                Set<Organization> orgs = new HashSet<Organization>();
                orgs.addAll( orgsList );
                sendCacheResetRemoteNotification( orgs );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUM.recalculateAllCreditUtilizationEvents - Exception while recalculating all credit utilization events. businessDate=" + businessDate, e );
        }
        log.info( new StringBuilder( 200 ).append( "CUM.recalculateAllCreditUtilizationEvents : Finished. businessDate=" )
                .append( businessDate ).append( ",count=" ).append( totalCueCount ).append( " took ms=" )
                .append( System.currentTimeMillis() - t0 ).toString() );
        return null;
    }

    /**
     * Returns the credit utilization for the specified date. If the credit utilization is not found, then an error is printed if
     * specified.
     *
     * @param clr      credit limit rule
     * @param date     date
     * @param logError log error
     * @return credit utilization
     */
    private CreditUtilization getCreditUtilizationForDate( CreditLimitRule clr, IdcDate date, boolean logError )
    {
        CreditUtilization cu = CreditDataQueryServiceFactory.getCreditDataQueryService().getCreditUtilizationForDate( clr, date );
        if ( cu == null && logError )
        {
            if ( date != null && date.isLaterThanOrEqualTo( DateTimeFactory.newDate() ) )
            {
                log.error( "CUM.getCreditUtilizationForDate No credit utilization found in the credit rule=" + clr + ",date=" + date );
            }
            else // reduce the log level for cases such as demo bank adaptor giving old dates.
            {
                log.warn( "CUM.getCreditUtilizationForDate No credit utilization found in the credit rule=" + clr + ",date=" + date );
            }
        }
        return cu;
    }

    /**
     * Recalculates the collection of active credit utilization events and credit utilizations based on the market rate
     * on the business date from the market dataset specified in the ruleset on the credit limit ruleset.
     *
     * @param orgName2CreditUtilList collection of credit utilizations by namespace.
     * @param resetReserved  reset reserved amount
     * @return credit workflow message indicating the status of operation and errors.
     * @throws IdcException IdcException
     */
    private void recalculateCreditUtilizationCollection( Session session, Map<String, Collection<CreditUtilization>> orgName2CreditUtilList, boolean resetReserved, boolean markForPLUpdate) throws IdcException
    {
        for(Map.Entry<String, Collection<CreditUtilization>> entry: orgName2CreditUtilList.entrySet())
        {
            if( CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationPersistenceOracleEnabled( entry.getKey() ))
            {
                recalculateCreditUtilizationCollectionIntoOracle(session, entry.getValue(), resetReserved, markForPLUpdate);
            }
            if( CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationPersistenceSpacesEnabled( entry.getKey() ))
            {
                recalculateCreditUtilizationCollectionIntoRDS( entry.getValue(), resetReserved, markForPLUpdate);
            }
        }
    }

    private CreditWorkflowMessage recalculateCreditUtilizationCollectionIntoOracle( Session session, Collection<CreditUtilization> creditUtilList, boolean resetReserved, boolean markForPLUpdate ) throws IdcException
    {
        if(session==null){
            session = PersistenceFactory.newSession();
        }

        try
        {
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClasses( getReadOnlyClasses() );

            Collection<CreditUtilization> registeredCus = new ArrayList<CreditUtilization>( );
            for(CreditUtilization cu : creditUtilList)
            {
                if( CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationLookupSpacesEnabled(cu.getNamespace().getShortName())){
                    //The object at hand is an RDS object. Retrieve the corresponding oracle object and recalculate
                    cu = CreditDataQueryServiceFactory.getCreditDataQueryService().getCreditUtilizationFromOracle(cu);
                }
                cu.markPositionsDirty ();
                cu.markNextDatePositionsDirty ();
                CreditUtilization registeredCu = ( CreditUtilization ) uow.registerObject( cu );
                registeredCus.add( registeredCu );
            }

            reallyRecalculateCreditUtilization( registeredCus, resetReserved, markForPLUpdate);
            uow.commit();
        }
        catch ( IdcOptimisticLockException ole )
        {
            log.info( "CUM.recalculateCreditUtilizationCollectionIntoOracle : OLE while recalculating credit util." );

            //handle the optimistic lock exception by refreshing and recalculating each credit utilization one at a time.
            for ( CreditUtilization cu : creditUtilList )
            {
                refreshAndRecalculateCreditUtilization( session, cu, resetReserved );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUM.recalculateCreditUtilizationCollectionIntoOracle - Exception while recalculating credit util.", e );
        }
        return null;
    }

    private CreditWorkflowMessage recalculateCreditUtilizationCollectionIntoRDS( Collection<CreditUtilization> creditUtilList, boolean resetReserved, boolean markForPLUpdate ) throws IdcException
    {
        try
        {
            reallyRecalculateCreditUtilization( creditUtilList, resetReserved, markForPLUpdate);

            for ( CreditUtilization cu : creditUtilList )
            {
                CreditDataQueryServiceFactory.getCreditReferenceDataService().createOrUpdateCreditUtilization( cu );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUM.recalculateCreditUtilizationCollectionIntoRDS - Exception while recalculating credit util.", e );
        }
        return null;
    }

    private void reallyRecalculateCreditUtilization( Collection<CreditUtilization> creditUtilList, boolean resetReserved, boolean markForPLUpdate )
    {
        for ( CreditUtilization cu : creditUtilList )
        {
            if ( cu != null )
            {
                String pos = cu.getPositions();
                cu.setPositions( pos != null ? pos + ' ' : "" );

                if ( cu.getCreditLimitRule() != null && cu.getCreditLimitRule().getCreditUtilizationCalculator() != null )
                {
                    double oldUsedAmt = cu.getUsedAmount();
                    IdcDate oldPositionDate = cu.getPositionsDate();
                    String posBefore = cu.getPositions();
                    cu.getCreditLimitRule().getCreditUtilizationCalculator().recalculateUtilization( cu );
                    IdcDate newPositionDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
                    cu.setPositionsDate( newPositionDate );
                    String posAfter = cu.getPositions();
                    double newUsedAmt = cu.getUsedAmount();

                    // since cue recalculation can lead to zero used amount, it can skip end of day P/L update. Forcing it to do with a non-zero used amount.
                    if ( markForPLUpdate && newUsedAmt == 0.0 && cu.isUpdateBalanceWithPL() )
                    {
                        cu.updateUsedAmount( -CreditLimit.MINIMUM );
                    }

                    log.info( new StringBuilder( 600 ).append( "CUM.reallyRecalculateCreditUtilization : recalculated utilization for cu=" )
                            .append( cu ).append( ",clr=" ).append( cu.getCreditLimitRule() ).append( ",cu desc=")
                            .append ( CreditUtilC.getCreditUtilizationDescription ( cu ) )
                            .append( ",beforePositions=" ).append( posBefore ).append( ",afterPositions=" )
                            .append( posAfter ).append( ",oldPositionDate=" ).append( oldPositionDate )
                            .append( ",newPositionDate=" ).append( newPositionDate ).append( ",oldUsedAmt=" )
                            .append( oldUsedAmt ).append( ",newUsedAmt=" ).append( newUsedAmt ).append( ",cu.limit=")
                            .append ( cu.getLimit () ).toString() );

                    if ( oldUsedAmt <= cu.getLimit () && newUsedAmt > cu.getLimit () )
                    {
                        try
                        {
                            CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet ( cu );
                            Organization cpo = CreditUtilC.getCreditProviderOrganization ( cu );
                            CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) cu.getCreditLimitRule ().getParentRule ();
                            boolean creditEnabled = clrs != null && clrs.isEnabled () && cclr.isEnabled ();
                            if ( CreditAdminConfigurationFactory.getCreditAdminConfigurationMBean ().isEndOfDayRevaluationBreachAlertEnabled ( cpo ) && creditEnabled )
                            {
                                String logStr = new StringBuilder ( 300 ).append ( "Credit breached during end of day/week revaluation for credit line=" )
                                        .append ( CreditUtilC.getCreditUtilizationDescription ( cu ) ).append ( ".old used amount=" )
                                        .append ( oldUsedAmt ).append ( ",new used amount=" ).append ( newUsedAmt )
                                        .append ( ",limit amount=" ).append ( cu.getLimit () ).toString ();
                                MessageLogger.getInstance ().log ( ISAlertMBean.ALERT_CREDIT_BREACH_ENDOFDAY_PROCESS, "CreditUtilizationManagerC.reallyRecalculateCreditUtilization()", logStr, null );
                                log.info ( "CUM.reallyRecalculateCreditUtilization : " + logStr );
                            }
                        }
                        catch ( Exception e )
                        {
                            log.error ( "CUM.reallyRecalculateCreditUtilization : Exception while alert logging of credit breach. cu=" + cu, e );
                        }
                    }
                }
                else
                {
                    log.info( "CUM.reallyRecalculateCreditUtilization : no credit limit rule or calculator for cu=" + cu + ",clr=" + cu.getCreditLimitRule() );
                }
                if ( resetReserved )
                {
                    cu.setReservedAmount( 0.0 );
                }
            }
        }
    }

    /**
     * Refresh the credit utilization and recalculate the utilization after resetting and rebuilding the currency positions. Since each credit
     * utilization is refreshed and retried 3 times, any optimistic lock exception after that is aborted.
     *
     * @param session       session
     * @param cu            credit utilization
     * @param resetReserved reset reserved
     * @return credit workflow message
     */
    private CreditWorkflowMessage refreshAndRecalculateCreditUtilization( Session session, CreditUtilization cu, boolean resetReserved )
    {
        int retryNum = 0;

        while ( true )
        {
            try
            {
                UnitOfWork uow = session.acquireUnitOfWork();
                uow.addReadOnlyClasses( getReadOnlyClasses() );
                cu = ( CreditUtilization ) uow.refreshObject( cu );
                if ( cu != null )
                {
                    String pos = cu.getPositions();
                    cu.setPositions( pos != null ? pos + ' ' : "" );

                    CreditUtilization registeredCu = ( CreditUtilization ) uow.registerObject( cu );
                    if ( registeredCu.getCreditLimitRule() != null && registeredCu.getCreditLimitRule().getCreditUtilizationCalculator() != null )
                    {
                        double oldUsedAmt = registeredCu.getUsedAmount();
                        IdcDate oldPositionDate = registeredCu.getPositionsDate();
                        String posBefore = registeredCu.getPositions();
                        registeredCu.getCreditLimitRule().getCreditUtilizationCalculator().recalculateUtilization( registeredCu );
                        IdcDate newPositionDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
                        registeredCu.setPositionsDate( EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate() );
                        String posAfter = registeredCu.getPositions();
                        double newUsedAmt = registeredCu.getUsedAmount();
                        log.info( new StringBuilder( 600 ).append( "CUM.refreshAndRecalculateCreditUtilization : recalculated utilization for cu=" )
                                .append( registeredCu ).append( ",clr=" ).append( registeredCu.getCreditLimitRule() )
                                .append( ",beforePositions=" ).append( posBefore ).append( ",afterPositions=" )
                                .append( posAfter ).append( ",oldPositionDate=" ).append( oldPositionDate )
                                .append( ",newPositionDate=" ).append( newPositionDate ).append( ",oldUsedAmt=" )
                                .append( oldUsedAmt ).append( ",newUsedAmt=" ).append( newUsedAmt ).toString() );
                    }
                    else
                    {
                        log.info( "CUM.refreshAndRecalculateCreditUtilization : no credit limit rule or calculator for cu=" + cu + ",clr=" + cu.getCreditLimitRule() );
                    }
                    if ( resetReserved )
                    {
                        registeredCu.setReservedAmount( 0.0 );
                    }
                }

                uow.commit();
                break;
            }
            catch ( IdcOptimisticLockException ole )
            {
                log.info( "CUM.refreshAndRecalculateCreditUtilization : OLE while recalculating credit util. Retry : " + retryNum );
                retryNum++;

                if ( retryNum >= CreditLimitConstants.MAXIMUM_RETRY )
                {
                    log.warn( "CUM.refreshAndRecalculateCreditUtilization : OptimisticLockException maximum retry exceeded for cu=" + cu );
                    break;
                }
            }
            catch ( Exception e )
            {
                log.error( "CUM.refreshAndRecalculateCreditUtilization - Exception while recalculating credit util.", e );
                break;
            }
        }
        return null;
    }

    /**
     * Creates the credit utilizations based on the type of the credit limit rule for the date band specified as
     * start and end dates. Credit utilizations are created in batches of database transactions.
     *
     * @param creditLimitRule     credit limit rule for which the credit utilizations need to be created.
     * @param startSettlementDate credit utilizations need to be created from this start date.
     * @param endSettlementDate   credit utilizations need to be created up to this end date.
     * @param creditProviderOrg   credit provider organization.
     * @param endDateCheckOnly    checks the end date credit utilization availability only.
     * @return The credit workflow message containing the credit util events, status, errors etc.
     */
    public CreditWorkflowMessage createCreditUtilizations( CreditLimitRule creditLimitRule, IdcDate startSettlementDate, IdcDate endSettlementDate, Organization creditProviderOrg, boolean endDateCheckOnly )
    {
        return createCreditUtilizations( creditLimitRule, startSettlementDate, endSettlementDate, creditProviderOrg, endDateCheckOnly, false );
    }

    /**
     * Creates the credit utilizations based on the type of the credit limit rule for the date band specified as
     * start and end dates. Credit utilizations are created in batches of database transactions.
     *
     * @param creditLimitRule     credit limit rule for which the credit utilizations need to be created.
     * @param startSettlementDate credit utilizations need to be created from this start date.
     * @param endSettlementDate   credit utilizations need to be created upto this end date.
     * @param creditProviderOrg   credit provider organization.
     * @param endDateCheckOnly    checks the end date credit utilization availability only.
     * @param disableJMSSend      disable multi-app message sending
     * @return The credit workflow message containing the credit util events, status, errors etc.
     */
    public CreditWorkflowMessage createCreditUtilizations( CreditLimitRule creditLimitRule, IdcDate startSettlementDate, IdcDate endSettlementDate, Organization creditProviderOrg, boolean endDateCheckOnly, boolean disableJMSSend )
    {
        StopWatchC watch = null;
        if ( LogFactory.isTimingEnabled( this.getClass() ) )
        {
            watch = LogFactory.getStopWatch( this.getClass(), "CUM.createCreditUtilizations for a credit limit rule" );
        }

        try
        {
            Session session = PersistenceFactory.newSession();
            UnitOfWork uow;
            synchronized ( cuCreationLock )
            {
                if ( creditLimitRule instanceof DailyCreditLimitRule )
                {
                    DailyCreditLimitRule dailyClr = ( DailyCreditLimitRule ) creditLimitRule;
                    int newOrUpdatedCreditUtilizationsNum = 0;
                    IdcDate startDateInUOW = startSettlementDate;
                    if ( endDateCheckOnly )
                    {
                        // end day check is normally used for automatic credit regeneration. Adjust the end settlement date staggered for efficiency.
                        int offset = Math.random() > 0.5 ? 0 : DatePeriod.DAYS_PER_WEEK;
                        endSettlementDate = endSettlementDate.addDays( offset );
                        CreditUtilization endDateCreditUtil = getCreditUtilizationForDate( dailyClr, endSettlementDate, false );
                        if ( endDateCreditUtil != null )
                        {
                            if ( log.isDebugEnabled() )
                            {
                                TradingParty tp = ( ( CounterpartyCreditLimitRule ) creditLimitRule.getParentRule() ).getTradingParty();
                                log.debug( new StringBuilder( 200 ).append( "CUM.createCreditUtilizations : End date has credit util for tp=" )
                                        .append( tp ).append( "endSettlementDate=" ).append( endSettlementDate ).append( ",offset=" ).append( offset ).toString() );
                            }
                            return null;
                        }
                    }
                    Map<IdcDate, CreditUtilization> map = dailyClr.getCreditUtilizationMap();
                    int retryNum = 0;
                    // pad the endDate randomly for efficiency.
                    int padWeek = Math.random() > 0.5 ? 2 : 1;
                    endSettlementDate = endSettlementDate.addDays( DatePeriod.DAYS_PER_WEEK * padWeek );
                    while ( startDateInUOW.isEarlierThanOrEqualTo( endSettlementDate ) )
                    {
                        while ( true )
                        {
                            IdcDate date = startDateInUOW;
                            try
                            {
                                uow = session.acquireUnitOfWork();
                                uow.addReadOnlyClasses( getReadOnlyClasses() );
                                uow.addReadOnlyClass( com.integral.finance.creditLimit.CreditUtilizationEventC.class );
                                uow.addReadOnlyClass( com.integral.finance.creditLimit.CreditLimitRuleC.class );
                                uow.addReadOnlyClass( com.integral.finance.creditLimit.DailyCreditLimitRuleC.class );
                                uow.addReadOnlyClass( com.integral.finance.creditLimit.SingleCreditLimitRuleC.class );
                                if ( disableJMSSend )
                                {
                                    uow.setProperty( ClusterCommitEventAdapterC.MULTI_APP, ClusterCommitEventAdapterC.MULTI_APP_DISABLED );
                                }

                                int numOfCommittable = 0;
                                while ( date.isEarlierThanOrEqualTo( endSettlementDate ) )
                                {
                                    CreditUtilization creditUtil = map.get( date );
                                    if ( creditUtil != null && creditUtil.isPassive() )
                                    {
                                        CreditUtilization cu = ( CreditUtilization ) uow.registerObject( creditUtil );
                                        cu.setNamespace( creditProviderOrg.getNamespace() );
                                        cu.setStatus( 'A' );
                                        numOfCommittable++;
                                        newOrUpdatedCreditUtilizationsNum++;
                                    }
                                    else if ( creditUtil == null )
                                    {
                                        DailyCreditUtilization cu = CreditLimitFactory.newDailyCreditUtilization();
                                        cu.setNamespace( creditProviderOrg.getNamespace() );
                                        cu.setCreditLimitRule( dailyClr );
                                        cu.setDate( date );
                                        DailyCreditUtilization ocu = (DailyCreditUtilization)CreditDataQueryServiceFactory.getCreditDataQueryService().getCreditUtilizationFromOracle(cu);
                                        if(ocu!=null)
                                        {
                                            cu = ocu;
                                        }
                                        cu = ( DailyCreditUtilization ) uow.registerObject( cu );
                                        map.put( date, cu );
                                        numOfCommittable++;
                                        newOrUpdatedCreditUtilizationsNum++;
                                    }
                                    date = date.addDays( 1 );
                                    if ( numOfCommittable >= COMMIT_BATCH_SIZE )
                                    {
                                        break;
                                    }
                                }
                                uow.commit();
                                startDateInUOW = date;
                                break;
                            }
                            catch ( IdcOptimisticLockException ole )
                            {
                                log.info( "CUM.createCreditUtilizations - OLE while creating credit utils. Retry : " + retryNum );
                                retryNum++;
                                if ( retryNum >= CreditLimitConstants.MAXIMUM_RETRY )
                                {
                                    log.info( "CUM.createCreditUtilizations : OptimisticLockException maximum retry exceeded." );
                                    throw new Exception();
                                }
                            }
                            catch ( Exception e )
                            {
                                log.error( "CUM.createCreditUtilizations - Exception while creating credit utils.", e );
                                break;
                            }
                        }
                    }
                    // if there are credit utilizations updated or added, refresh the credit limit rule.
                    if ( newOrUpdatedCreditUtilizationsNum > 0 )
                    {
                        creditLimitRule = ( CreditLimitRule ) session.refreshObject( creditLimitRule );
                        uow = session.acquireUnitOfWork();
                        uow.addReadOnlyClasses( getReadOnlyClasses() );
                        creditLimitRule = ( CreditLimitRule ) uow.registerObject( creditLimitRule );
                        CreditLimitRuleSetC clrs = ( CreditLimitRuleSetC ) CreditUtilC.getCreditLimitRuleSet( creditProviderOrg );
                        clrs.resetTransients();
                        creditLimitRule.update();
                        uow.commit();
                    }
                }
                else if ( creditLimitRule instanceof SingleCreditLimitRule )
                {
                    SingleCreditLimitRule singleClr = ( SingleCreditLimitRule ) creditLimitRule;
                    CreditLimitConfigurationMBean creditConfigMBean = CreditLimitConfigurationFactory.getCreditConfigurationMBean();
                    if ( singleClr.getCreditUtilization() == null )
                    {
                        String cpo = creditLimitRule.getNamespace().getShortName();
                        log.info("CUM.createCreditUtilizations - creating AggregateCU for CLR " + creditLimitRule);
                        CreditUtilization cu = CreditLimitFactory.newCreditUtilization();
                        if (creditConfigMBean.isCreditUtilizationPersistenceOracleEnabled(cpo ))
                        {
                            uow = session.acquireUnitOfWork();
                            uow.addReadOnlyClasses( getReadOnlyClasses() );
                            SingleCreditLimitRule registeredSingleClr = ( SingleCreditLimitRule ) uow.registerObject( singleClr );
                            CreditUtilization registeredCU = ( CreditUtilization ) uow.registerObject( cu );
                            initializeNewAggCU(registeredSingleClr, registeredCU );
                            uow.commit();
                        }
                        if (creditConfigMBean.isCreditUtilizationPersistenceSpacesEnabled(cpo))
                        {
                            initializeNewAggCU(singleClr, cu );
                            CreditDataQueryServiceFactory.getCreditReferenceDataService().createOrUpdateCreditUtilization(cu);
                        }

                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "CUM.createCreditUtilizations.Error -  while creating credit utilizations.", e );
        }

        if ( watch != null )
        {
            watch.stopAndLog();
        }
        return null;
    }

    private void initializeNewAggCU( SingleCreditLimitRule singleClr, CreditUtilization cu )
    {
        cu.setNamespace( singleClr.getNamespace() );
        cu.setCreditLimitRule( singleClr );
        singleClr.setCreditUtilization(cu );
    }

    /**
     * Return a list of read only classes which are basic to credit operations. Other operation specific read only classes
     * need to be added in specific cases
     *
     * @return vector of read only classes for transaction.
     */
    private static Vector getReadOnlyClasses()
    {
        return readOnlyClasses;
    }

    /**
     * Sets the readonly classes.
     */
    private static void setReadOnlyClasses()
    {
        readOnlyClasses.add( com.integral.finance.creditLimit.CreditLimitRuleSetC.class );
        readOnlyClasses.add( com.integral.finance.creditLimit.CreditLimitClassificationC.class );
        readOnlyClasses.add( com.integral.finance.creditLimit.CreditLimitWorkflowStateC.class );
        readOnlyClasses.add( com.integral.finance.creditLimit.CounterpartyCreditLimitRuleC.class );
        readOnlyClasses.add( com.integral.user.UserC.class );
        readOnlyClasses.add( com.integral.user.OrganizationC.class );
        readOnlyClasses.add( com.integral.persistence.NamespaceC.class );
        readOnlyClasses.add( com.integral.workflow.StateC.class );
        readOnlyClasses.add( com.integral.finance.counterparty.TradingPartyC.class );
        readOnlyClasses.add( com.integral.finance.counterparty.LegalEntityC.class );
        readOnlyClasses.add( com.integral.finance.fx.FXPaymentParametersC.class );
        readOnlyClasses.add( com.integral.finance.fx.FXLegC.class );
        readOnlyClasses.add( com.integral.workflow.WorkflowStateMapC.class );
        readOnlyClasses.add( com.integral.finance.marketData.fx.FXMarketDataSetC.class );
    }

    /**
     * Returns the list of read-only classes needed for end of day credit limit balance update.
     *
     * @return list of read-only classes
     */
    private static Vector getCreditLimitBalanceUpdateReadOnlyClasses()
    {
        return creditLimitBalanceUpdateReadOnlyClasses;
    }

    /**
     * Sets the read-only classes for the end of day credit limit balance update with settled positions profit and loss.
     */
    private static void setCreditLimitBalanceUpdateReadOnlyClasses()
    {
        creditLimitBalanceUpdateReadOnlyClasses.addAll( getReadOnlyClasses() );
        creditLimitBalanceUpdateReadOnlyClasses.add( CreditUtilizationC.class );
    }

    /**
     * Deletes all the credit utilization events which has position settlement date prior to the business date supplied.
     *
     * @param businessDate   business date
     * @param batchCount     maximum batches of commit allowed
     * @param disableJMSSend disable JMS
     * @param functor        functor
     * @return credit workflow message
     */
    public CreditWorkflowMessage deleteSettledCreditUtilizationEvents( IdcDate businessDate, int batchCount, boolean disableJMSSend, CreditFunctor functor )
    {
        long t0 = System.currentTimeMillis();
        log.info( new StringBuilder( 200 ).append( "CUM.deleteSettledCreditUtilizationEvents : Start deleting old credit events. Date=" )
                .append( businessDate ).toString() );
        int num = 0;
        int totalRemoved = 0;
        int completedBatchCount = 0;
        CursoredStream cursor = null;

        try
        {
            Session session = PersistenceFactory.newSession();
            ReadAllQuery raq = new ReadAllQuery();
            raq.setReferenceClass( CreditUtilizationEventC.class );
            raq.useCursoredStream( JUMBO_COMMIT_BATCH_SIZE, JUMBO_COMMIT_BATCH_SIZE );
            raq.setSelectionCriteria( getSettledCreditUtilizationEventsExpression( businessDate ) );
            cursor = ( CursoredStream ) session.executeQuery( raq );

            Collection<CreditUtilizationEvent> cues = null;
            while ( !cursor.atEnd() )
            {
                if ( num == 0 )
                {
                    cues = new ArrayList<CreditUtilizationEvent>( JUMBO_COMMIT_BATCH_SIZE );

                    // run-time check for configuration so that it can be interrupted if needed once the batch is completed.
                    if ( ( functor != null && !functor.isEnabled() ) || !creditAdminConfigMBean.isRemoveCreditEnabled() )
                    {
                        log.info( new StringBuilder( 200 ).append( "CUM.deleteSettledCreditUtilizationEvents : Skipping further deletion due to config change. functor=" )
                                .append( functor ).append( ",creditAdminConfigMBean.isRemoveCreditEnabled=" )
                                .append( creditAdminConfigMBean.isRemoveCreditEnabled() ).toString() );
                        break;
                    }
                }
                cues.add( ( CreditUtilizationEvent ) cursor.read() );
                num++;
                totalRemoved++;
                if ( num >= JUMBO_COMMIT_BATCH_SIZE )
                {
                    deleteSettledCreditUtilizationEventsCollection( session, cues, disableJMSSend );
                    num = 0;
                    cursor.releasePrevious();
                    ++completedBatchCount;
                    log.info( new StringBuilder( 200 ).append( "CUM.deleteSettledCreditUtilizationEvents : totalRemoved=" )
                            .append( totalRemoved ).append( ",batchNum=" ).append( completedBatchCount ).toString() );
                }

                if ( completedBatchCount >= batchCount )
                {
                    log.info( "CUM.deleteSettledCreditUtilizationEvents : Specified batches of execution completed." );
                    break;
                }
            }
            if ( num > 0 )
            {
                deleteSettledCreditUtilizationEventsCollection( session, cues, disableJMSSend );
            }

            if ( log.isInfoEnabled() )
            {
                log.info( new StringBuilder( 200 ).append( "CUM.deleteSettledCreditUtilizationEvents : Removed count=" )
                        .append( totalRemoved ).append( ",businessDate=" ).append( businessDate ).toString() );
            }
            cursor.close();
        }
        catch ( Exception e )
        {
            log.error( new StringBuilder( 200 ).append( "CUM.deleteSettledCreditUtilizationEvents : businessDate=" )
                    .append( businessDate ).toString(), e );
        }
        finally
        {
            if ( cursor != null )
            {
                cursor.close();
            }
        }
        log.info( new StringBuilder( 200 ).append( "CUM.deleteSettledCreditUtilizationEvents : End deleting old credit events. Date=" )
                .append( businessDate ).append( ",totalRemoved=" ).append( totalRemoved ).append( " took ms=" )
                .append( System.currentTimeMillis() - t0 ).toString() );

        return null;
    }


    /**
     * Return the expression for retrieving all the credit utilization events which are prior to the settlement date specified.
     *
     * @param settlementDate settlement date
     * @return expression to be used in the query
     */
    private Expression getSettledCreditUtilizationEventsExpression( IdcDate settlementDate )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        return eb.get( "settlementDate" ).lessThan( settlementDate.asSqlDate() );
    }

    /**
     * Removes the credit utilization event collection by deleting them. Before that the credit utilization events need
     * be deleted.
     *
     * @param session        to manage the deletion
     * @param cues           credit utilization event list.
     * @param disableJMSSend disable multi-app sending
     * @return credit workflow message indicating status of operation and errors.
     */
    private CreditWorkflowMessage deleteSettledCreditUtilizationEventsCollection( Session session, Collection<CreditUtilizationEvent> cues, boolean disableJMSSend )
    {
        int retryNum = 0;
        while ( true )
        {
            try
            {
                UnitOfWork uow = session.acquireUnitOfWork();
                uow.addReadOnlyClasses( getReadOnlyClasses() );
                uow.addReadOnlyClass( CreditUtilizationC.class );
                if ( disableJMSSend )
                {
                    uow.setProperty( ClusterCommitEventAdapterC.MULTI_APP, ClusterCommitEventAdapterC.MULTI_APP_DISABLED );
                }

                for ( CreditUtilizationEvent cue : cues )
                {
                    if ( retryNum > 0 )
                    {
                        cue = ( CreditUtilizationEvent ) uow.refreshObject( cue );
                    }
                    if ( cue != null )
                    {
                        cue = ( CreditUtilizationEvent ) uow.registerObject( cue );
                        uow.deleteObject( cue );
                    }
                }
                uow.commit();
                break;
            }
            catch ( IdcOptimisticLockException e )
            {
                retryNum++;
                if ( retryNum >= CreditLimitConstants.MAXIMUM_RETRY )
                {
                    log.info( "CUM.deleteSettledCreditUtilizationEventsCollection : OptimisticLockException maximum retry exceeded." );
                    break;
                }
            }
        }
        return null;
    }

    /**
     * Deletes all the daily credit utilizations which has date prior to the business date supplied.
     *
     * @param businessDate   business date
     * @param batchCount     maximum batches of commit allowed
     * @param disableJMSSend disable multi-app message sending
     * @param functor        functor
     * @return credit workflow message
     */
    public CreditWorkflowMessage deleteSettledDailyCreditUtilizations( IdcDate businessDate, int batchCount, boolean disableJMSSend, CreditFunctor functor )
    {
        long t0 = System.currentTimeMillis();
        log.info( new StringBuilder( 200 ).append( "CUM.deleteSettledDailyCreditUtilizations : Start deleting old credit events. Date=" )
                .append( businessDate ).toString() );
        int num = 0;
        int totalRemoved = 0;
        int completedBatchCount = 0;
        CursoredStream cursor = null;

        try
        {
            Session session = PersistenceFactory.newSession();
            ReadAllQuery raq = new ReadAllQuery();
            raq.setReferenceClass( DailyCreditUtilizationC.class );
            raq.useCursoredStream( JUMBO_COMMIT_BATCH_SIZE, JUMBO_COMMIT_BATCH_SIZE );
            raq.setSelectionCriteria( getSettledDailyCreditUtilizationExpression( businessDate ) );
            cursor = ( CursoredStream ) session.executeQuery( raq );

            Collection<DailyCreditUtilization> cus = null;
            while ( !cursor.atEnd() )
            {
                if ( num == 0 )
                {
                    cus = new ArrayList<DailyCreditUtilization>( JUMBO_COMMIT_BATCH_SIZE );

                    // run-time check for configuration so that it can be interrupted if needed once the batch is completed.
                    if ( ( functor != null && !functor.isEnabled() ) || !creditAdminConfigMBean.isRemoveCreditEnabled() )
                    {
                        log.info( new StringBuilder( 200 ).append( "CUM.deleteSettledDailyCreditUtilizations : Skipping further deletion due to config change. functor=" )
                                .append( functor ).append( ",creditAdminConfigMBean.isRemoveCreditEnabled=" ).append( creditAdminConfigMBean.isRemoveCreditEnabled() ).toString() );
                        break;
                    }
                }
                cus.add( ( DailyCreditUtilization ) cursor.read() );
                num++;
                totalRemoved++;
                if ( num >= JUMBO_COMMIT_BATCH_SIZE )
                {
                    deleteSettledDailyCreditUtilizationCollection( session, cus, disableJMSSend );
                    num = 0;
                    cursor.releasePrevious();
                    ++completedBatchCount;
                    log.info( new StringBuilder( 200 ).append( "CUM.deleteSettledDailyCreditUtilizations : totalRemoved=" )
                            .append( totalRemoved ).append( ",batchNum=" ).append( completedBatchCount ).toString() );
                }

                if ( completedBatchCount > batchCount )
                {
                    log.info( "CUM.deleteSettledDailyCreditUtilizations : Specified batches of execution completed." );
                    break;
                }
            }
            if ( num > 0 )
            {
                deleteSettledDailyCreditUtilizationCollection( session, cus, disableJMSSend );
            }

            if ( log.isInfoEnabled() )
            {
                log.info( new StringBuilder( 200 ).append( "CUM.deleteSettledDailyCreditUtilizations : Removed count=" )
                        .append( totalRemoved ).append( ",businessDate=" ).append( businessDate ).toString() );
            }
            cursor.close();
        }
        catch ( Exception e )
        {
            log.error( new StringBuilder( 200 ).append( "CUM.deleteSettledDailyCreditUtilizations : businessDate=" )
                    .append( businessDate ).toString(), e );
        }
        finally
        {
            if ( cursor != null )
            {
                cursor.close();
            }
        }
        log.info( new StringBuilder( 200 ).append( "CUM.deleteSettledDailyCreditUtilizations : End deleting old credit utils. Date=" )
                .append( businessDate ).append( ",totalRemoved=" ).append( totalRemoved ).append( " took ms=" )
                .append( System.currentTimeMillis() - t0 ).toString() );

        return null;
    }

    public CreditWorkflowMessage checkPortfolioCredit( NettingPortfolio np )
    {
        return CreditLimitSubscriptionManagerC.getInstance().checkPortfolioCredit( np );
    }

    public CreditWorkflowMessage checkPortfolioCredit( NettingPortfolio np, boolean useAccepedRequests, boolean isAcceptance )
    {
        return CreditLimitSubscriptionManagerC.getInstance().checkPortfolioCredit( np, useAccepedRequests, isAcceptance );
    }

    public CreditWorkflowMessage checkMultiLegFXTradeCredit( FXTrade trade, final LegalEntity takerLe, final LegalEntity makerLe )
    {
        return CreditLimitSubscriptionManagerC.getInstance().checkMultiLegFXTradeCredit( trade, takerLe, makerLe );
    }

    public CreditWorkflowMessage checkMultiLegFXTradeCredit( Request request, final LegalEntity takerLe, final LegalEntity makerLe )
    {
        return CreditLimitSubscriptionManagerC.getInstance().checkMultiLegFXTradeCredit( request, takerLe, makerLe );
    }

    public CreditWorkflowMessage checkTradeAllocationsCredit( Trade origTrade, Collection<NettingTradeRequest> allocationTradeRequests )
    {
        return CreditLimitSubscriptionManagerC.getInstance().checkTradeAllocationsCredit( origTrade, allocationTradeRequests );
    }

    /**
     * Return the expression for retrieving all the daily credit utilizations which are prior to the settlement date specified.
     *
     * @param settlementDate settlement date
     * @return expression to be used in the query
     */
    private Expression getSettledDailyCreditUtilizationExpression( IdcDate settlementDate )
    {
        ExpressionBuilder eb = new ExpressionBuilder();
        return eb.get( "date" ).lessThan( settlementDate.asSqlDate() );
    }

    /**
     * Removes the daily credit utilization  collection by deleting them. Before that the credit utilization events need
     * be deleted.
     *
     * @param session        to manage the deletion
     * @param cus            credit utilization event list.
     * @param disableJMSSend disable multi-app JMS message sending
     * @return credit workflow message indicating status of operation and errors.
     */
    private CreditWorkflowMessage deleteSettledDailyCreditUtilizationCollection(
            Session session, Collection<DailyCreditUtilization> cus, boolean disableJMSSend )
    {
        int retryNum = 0;
        while ( true )
        {
            try
            {
                UnitOfWork uow = session.acquireUnitOfWork();
                uow.addReadOnlyClasses( getReadOnlyClasses() );
                if ( disableJMSSend )
                {
                    uow.setProperty( ClusterCommitEventAdapterC.MULTI_APP, ClusterCommitEventAdapterC.MULTI_APP_DISABLED );
                }

                for ( DailyCreditUtilization cu : cus )
                {
                    if ( retryNum > 0 )
                    {
                        cu = ( DailyCreditUtilization ) uow.refreshObject( cu );
                    }
                    if ( cu != null )
                    {
                        cu = ( DailyCreditUtilization ) uow.registerObject( cu );
                        uow.deleteObject( cu );
                    }
                }
                uow.commit();
                break;
            }
            catch ( IdcOptimisticLockException e )
            {
                retryNum++;
                if ( retryNum >= CreditLimitConstants.MAXIMUM_RETRY )
                {
                    log.info( "CUM.deleteSettledDailyCreditUtilizationCollection : OptimisticLockException maximum retry exceeded." );
                    break;
                }
            }
        }
        return null;
    }

    /**
     * Recalculates the collection of active credit utilization events based on the tenor coefficients and the date period
     * between current trade date and value date.
     *
     * @param session             to manage the database transaction
     * @param creditUtilEventList collection of credit utilization events.
     * @return credit workflow message indicating the status of operation and errors.
     * @throws IdcException IdcException
     */
    private CreditWorkflowMessage recalculateCreditUtilizationEventCollection(
           Session session, Collection<CreditUtilizationEvent> creditUtilEventList) throws IdcException
    {
        try
        {
            Map<String, Collection<CreditUtilization>> orgName2CUList = new HashMap<String, Collection<CreditUtilization>>();
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClasses( getReadOnlyClasses() );
            uow.addReadOnlyClass( CreditUtilizationC.class );
            for ( CreditUtilizationEvent cue : creditUtilEventList )
            {
                if ( cue != null )
                {
                    CreditUtilizationEvent registeredCue = ( CreditUtilizationEvent ) uow.registerObject( cue );
                    CreditUtilization cu = recalculateCreditUtilizationEvent( registeredCue );
                    if ( cu != null )
                    {
                        Collection cuList = orgName2CUList.get( cu.getNamespace().getShortName() );
                        if(cuList == null)
                        {
                            cuList = new HashSet<CreditUtilization>();
                            orgName2CUList.put( cu.getNamespace().getShortName(), cuList );
                        }
                        cuList.add( cu );
                        if ( cu.getCreditLimitRule() == null || cu.getCreditLimitRule().getParentRule() == null )
                        {
                            log.warn( "CUM.recalculateCreditUtilizationEventCollection : either credit limit rule or cpty credit limit rule is null. clr=" + cu.getCreditLimitRule() );
                        }
                    }
                }
            }
            uow.commit();

            // recalculate all the credit utilizations.
            recalculateCreditUtilizationCollection(session, orgName2CUList, false, true);
        }
        catch ( IdcOptimisticLockException ole )
        {
            log.info( "CUM.recalculateCreditUtilizationEventCollection : OptimisticLockException while recalculating credit util." );

            //handle the optimistic lock exception by refreshing and recalculating each credit utilization event one at a time.
            for ( CreditUtilizationEvent cue : creditUtilEventList )
            {
                refreshAndRecalculateCreditUtilizationEvent( session, cue );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUM.recalculateCreditUtilizationEventCollection - Exception while recalculating credit util events.", e );
        }
        return null;
    }

    /**
     * Refresh the credit utilization event and recalculate the utilization event.
     *
     * @param session session
     * @param cue     credit utilization event
     * @return credit workflow message
     */
    private CreditWorkflowMessage refreshAndRecalculateCreditUtilizationEvent( Session session, CreditUtilizationEvent cue )
    {
        int retryNum = 0;

        while ( true )
        {
            try
            {
                Map<String, Collection<CreditUtilization>> orgName2CUList = new HashMap<String, Collection<CreditUtilization>>( 1 );
                UnitOfWork uow = session.acquireUnitOfWork();
                uow.addReadOnlyClasses( getReadOnlyClasses() );
                cue = ( CreditUtilizationEvent ) uow.refreshObject( cue );
                if ( cue != null )
                {
                    CreditUtilizationEvent registeredCue = ( CreditUtilizationEvent ) uow.registerObject( cue );
                    CreditUtilization cu = recalculateCreditUtilizationEvent( registeredCue );
                    if ( cu != null )
                    {
                        Collection cuList = new HashSet<CreditUtilization>(1);
                        orgName2CUList.put( cu.getNamespace().getShortName(), cuList );
                        cuList.add( cu );
                    }
                }
                uow.commit();

                recalculateCreditUtilizationCollection( session, orgName2CUList, false, true);
                break;
            }
            catch ( IdcOptimisticLockException ole )
            {
                log.info( "CUM.refreshAndRecalculateCreditUtilizationEvent : OptimisticLockException while recalculating credit util event. Retry : " + retryNum );
                retryNum++;

                if ( retryNum >= CreditLimitConstants.MAXIMUM_RETRY )
                {
                    log.info( "CUM.refreshAndRecalculateCreditUtilizationEvent : OptimisticLockException maximum retry exceeded for cue=" + cue );
                    break;
                }
            }
            catch ( Exception e )
            {
                log.error( "CUM.refreshAndRecalculateCreditUtilizationEvent - Exception while recalculating credit util event.", e );
                break;
            }
        }
        return null;
    }

    private CreditUtilization recalculateCreditUtilizationEvent( CreditUtilizationEvent cue )
    {
        CreditUtilization cu = null;
        try
        {
            cu = cue.getCreditUtilization();
            if ( cu != null )
            {
                CreditLimitRule clr = cu.getCreditLimitRule();
                if ( clr != null )
                {
                    CounterpartyCreditLimitRule cclr = ( CounterpartyCreditLimitRule ) clr.getParentRule();
                    if ( cclr != null )
                    {
                        if ( !cclr.isActive() )
                        {
                            // figure out if the cclr is not active due to the exposure change.
                            final boolean exposureChanged = ( cclr.isOrgLevel() && cclr.getTradingParty() != null )
                                    || ( !cclr.isOrgLevel() && cclr.getTradingParty() == null );
                            if ( exposureChanged )
                            {
                                CreditLimitRuleSet clrs = ( CreditLimitRuleSet ) cclr.getRuleSet();
                                Organization cpo = CreditUtilC.getCreditProviderOrganization( clrs );
                                CounterpartyCreditLimitRule activeCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( cpo, cue.getTradingParty(), true );
                                log.info( "CUM.recalculateCreditUtilizationEvent : handling exposure change. currentCclr="
                                        + activeCclr + ",cue.cclr=" + cclr + ",cue.tp=" + cue.getTradingParty() + ",cclr.tp=" + cclr.getTradingParty() );
                                if ( activeCclr != null )
                                {
                                    cclr = activeCclr;
                                    // retrieve appropriate credit utilization for recalculation.
                                    if ( cu instanceof DailyCreditUtilizationC )
                                    {
                                        DailyCreditLimitRule dclr = ( DailyCreditLimitRule ) cclr.getChildRule( CreditLimitConstants.DAILY_SETTLEMENT_RULE_SHORT_NAME );
                                        if ( dclr != null )
                                        {
                                            IdcDate date = ( ( DailyCreditUtilization ) cu ).getDate();
                                            cu = getCreditUtilizationForDate(dclr, date);
                                        }
                                    }
                                    else
                                    {
                                        SingleCreditLimitRule singleClr = ( SingleCreditLimitRule ) cclr.getChildRule( CreditLimitConstants.GROSS_NOTIONAL_RULE_SHORT_NAME );
                                        if ( singleClr != null )
                                        {
                                            cu = getCreditUtilizationForDate(singleClr, null);
                                        }
                                    }
                                }
                            }
                        }

                        if ( cclr.isActive() )
                        {
                            CreditTenorProfile ctp = CreditUtilC.getCreditTenorProfile( cclr, clr, cue.getPrincipalCurrency(), cue.getPriceCurrency() );
                            if ( ctp == null )
                            {
                                log.info( "CUM.recalculateCreditUtilizationEvent : skipping event as there is no tenor profile. tid=" + cue.getTransactionId() + ",cu=" + cu + ",cclr=" + cclr );
                                return null;
                            }

                            double tc = CreditUtilC.getTenorCoefficient( cclr, clr, cue.getSettlementDate(), cue.getPrincipalCurrency(), cue.getPriceCurrency() );
                            if ( tc != CreditLimit.TENOR_COEFFICIENT_NA )
                            {
                                cu = cue.getCreditUtilization();

                                if ( cue.getTradePrincipalAmount() == 0.0 && cue.getTradePriceAmount() == 0.0 )
                                {
                                    cue.setTradePrincipalAmount( cue.getPrincipal() );
                                    cue.setTradePriceAmount( cue.getPrice() );
                                }

                                if ( cu instanceof DailyCreditUtilization && !CreditUtilC.isApplyPFEOnDailyCredit( cclr ) )
                                {
                                    tc = CreditLimit.DEFAULT_TENOR_COEFFICIENT;
                                }

                                double oldTc = cue.getTenorCoefficient();
                                double existingPrincipalAmt = cue.getPrincipal();
                                double existingPriceAmt = cue.getPrice();
                                cue.setPrincipal( cue.getPrincipalCurrency().round( cue.getTradePrincipalAmount() * tc ) );
                                cue.setPrice( cue.getPriceCurrency().round( cue.getTradePriceAmount() * tc ) );
                                cue.setTenorCoefficient( tc );
                                log.info( new StringBuilder( 300 ).append( "CUM.recalculateCreditUtilizationEvent : recalculated event=" )
                                        .append( cue ).append( ",cclr=" ).append( cclr ).append( ",existingPrincipalAmt=" )
                                        .append( existingPrincipalAmt ).append( ",existingPriceAmt=" ).append( existingPriceAmt ).append( ",tc=" )
                                        .append( tc ).append( ",origTrdPrincipal=" ).append( cue.getTradePrincipalAmount() )
                                        .append( ",origTrdPrice=" ).append( cue.getTradePriceAmount() ).append( ",newPrincipalAmt=" )
                                        .append( cue.getPrincipal() ).append( ",newPriceAmt=" ).append( cue.getPrice() ).append( ",oldTc=" )
                                        .append( oldTc ).append( ",cu=" ).append( cu ).append( ",tid=" ).append( cue.getTransactionId() ).toString() );

                            }
                            else
                            {
                                log.info( "CUM.recalculateCreditUtilizationEvent : skipping event as there is no tenor coefficient. tid=" + cue.getTransactionId() + ",cu=" + cu );
                            }
                        }
                    }
                    else
                    {
                        log.info( "CUM.recalculateCreditUtilizationEvent : No cpty credit limit rule found for cue=" + cue );
                    }
                }
                else
                {
                    log.info( "CUM.recalculateCreditUtilizationEvent : No credit limit rule found for cue=" + cue );
                }
            }
            else
            {
                log.info( "CUM.recalculateCreditUtilizationEvent : No credit utilization found for cue=" + cue );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUM.recalculateCreditUtilizationEvent : Exception while recalculating the credit utilization event=" + cue, e );
        }
        return cu;
    }

    /**
     * This method sends a remote notification so that credit utilization currency positions can be re-fetched since utilization event amounts are
     * updated with tenor coefficients.
     *
     * @param orgs organizations with modified events
     */
    private void sendCacheResetRemoteNotification( Set<Organization> orgs )
    {
        IdcTransaction tx = null;
        User loggedInUser = IdcUtilC.getSessionContextUser ();
        try
        {
            if ( orgs == null || orgs.isEmpty() )
            {
                return;
            }
            IdcSessionManager.getInstance().setTransaction( null );
            for ( Organization org : orgs )
            {
                CreditLimitRuleSet clrs = CreditUtilC.getCreditLimitRuleSet( org );
                if ( clrs != null )
                {
                    User usr = org.getDefaultDealingUser();
                    IdcUtilC.setSessionContextUser( usr );
                    tx = CreditUtilC.startTransaction( getReadOnlyClasses() );
                    tx.getUOW().removeReadOnlyClass( CreditLimitRuleSetC.class );
                    log.info( "CUM.sendCacheResetRemoteNotification : credit limit rule set used for update=" + clrs );
                    CreditLimitRuleSet regClrs = ( CreditLimitRuleSet ) clrs.getRegisteredObject();
                    regClrs.update();
                    break;
                }
            }

            String[] orgGuids = new String[orgs.size()];
            int i = 0;
            for ( Organization org : orgs )
            {
                orgGuids[i] = org.getGUID();
                i++;
            }

            // add notification functors for clearing the credit enable/disable flag cache.
            HashMap  propertiesMap = new HashMap( 2 );
            propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGS, orgGuids );
            propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, CreditMessageEvent.RECALCULATE_UTILIZATION_EVENTS.getName() );
            CreditUtilC.getTransaction().addRemoteFunctor( CreditUtilizationEventRecalculationNotificationFunctorC.class.getName(), propertiesMap );

            CreditUtilC.endTransaction( tx );
        }
        catch ( Exception e )
        {
            log.error( "CUM.sendCacheResetRemoteNotification : Exception while sending the remote notification to rebuild currency positions after evaluation.", e );
            CreditUtilC.releaseTransaction( tx );
        }
        finally
        {
            CreditUtilC.closeTransaction( tx );
            IdcUtilC.setSessionContextUser ( loggedInUser );
        }
    }

    private void recalculateSpacesCreditUtilizationEvents(Session session, Collection<Organization> configuredOrgs)
    {
        try
        {
            Map<String, Collection<CreditUtilization>> orgName2CUList = new HashMap<String, Collection<CreditUtilization>>(  );
            for ( Organization org : configuredOrgs )
            {
                Iterator<CreditUtilizationEvent> resultIterator = CreditDataQueryServiceFactory.getCreditDataQueryService().getSpacesCreditUtilizationEvents( org.getNamespace() );
                if ( resultIterator != null )
                {
                    while ( resultIterator.hasNext() )
                    {
                        CreditUtilizationEvent object = resultIterator.next();

                        SpaceCreditUtilizationEventC cue = ( SpaceCreditUtilizationEventC ) object;
                        CreditUtilization cu = recalculateCreditUtilizationEvent( cue );
                        if ( cu != null )
                        {
                            ErrorMessage errorMessage = getISCreditPersistenceService().persist( cue, ApplicationEventCodes.EVENT_CREDIT_UTILIZATION_REVALUATION );
                            if ( errorMessage != null )
                            {
                                log.error( "CUM.recalculateSpacesCreditUtilizationEvents : error while persisting recalculated credit utilization event=" + cue + ",errorMsg=" + errorMessage );
                            }
                            Collection<CreditUtilization> cuList = orgName2CUList.get( cu.getNamespace().getShortName() );
                            if(cuList == null){
                                cuList = new HashSet<CreditUtilization>();
                                orgName2CUList.put( cu.getNamespace().getShortName(), cuList );
                            }
                            cuList.add( cu );
                        }
                    }
                }
                // recalculate all the credit utilizations.
                recalculateCreditUtilizationCollection( session, orgName2CUList, false, true);
            }
        }
        catch ( Exception e )
        {
            log.error( "CUM.recalculateSpacesCreditUtilizationEvents : Exception while recalculating spaces credit util events. orgs=" + configuredOrgs, e );
        }
    }

    public void inactivateSpacesCreditUtilizationEventsForCounterpartyOrg( final Organization cpo, final Organization cco )
    {
        try
        {
            if ( cpo == null || cco == null )
            {
                log.info( "CUM.inactivateSpacesCreditUtilizationEventsForCounterpartyOrg : Either cpo or cco is null. cpo=" + cpo + ",cco=" + cco );
                return;
            }

            if ( !CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditMetaSpaceQueryEnabled() )
            {
                log.info( "CUM.inactivateSpacesCreditUtilizationEventsForCounterpartyOrg : metaspace credit query is disabled. cpo=" + cpo + ",cco=" + cco );
                return;
            }

            Iterator<CreditUtilizationEvent> resultIterator = CreditDataQueryServiceFactory.getCreditDataQueryService().getSpacesCreditUtilizationEventsForCounterpartyOrg( cpo, cco );
            if ( resultIterator != null )
            {
                while ( resultIterator.hasNext() )
                {
                    CreditUtilizationEvent object = resultIterator.next();

                    SpaceCreditUtilizationEventC cue = ( SpaceCreditUtilizationEventC ) object;
                    if ( cue != null )
                    {
                        final Organization cptyOrg = cue.getTradingPartyOrganization();
                        if ( cptyOrg != null && cco.isSameAs( cptyOrg ) )
                        {
                            cue.setActive( false );
                            ErrorMessage errorMessage = getISCreditPersistenceService().persist( cue, ApplicationEventCodes.EVENT_CREDIT_UTILIZATION_EVENT_INACTIVATION );
                            if ( errorMessage != null )
                            {
                                log.error( "CUM.inactivateSpacesCreditUtilizationEventsForCounterpartyOrg : error while persisting inactivated credit utilization event=" + cue + ",errorMsg=" + errorMessage );
                            }
                        }
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "CUM.inactivateSpacesCreditUtilizationEventsForCounterpartyOrg : Exception while inactivating the credit utilization event for cpo=" + cpo + ",cco=" + cco, e );
        }
    }

    public Collection<CreditUtilizationEvent> getSpacesCreditUtilizationEventsForCounterpartyOrg( final Organization cpo, final Organization cco )
    {
        Collection<CreditUtilizationEvent> cues = new HashSet<CreditUtilizationEvent>();
        try
        {
            if ( cpo == null || cco == null )
            {
                log.info( "CUM.getSpacesCreditUtilizationEventsForCounterpartyOrg : Either cpo or cco is null. cpo=" + cpo + ",cco=" + cco );
                return cues;
            }

            if ( !CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditMetaSpaceQueryEnabled() )
            {
                log.info( "CUM.getSpacesCreditUtilizationEventsForCounterpartyOrg : metaspace credit query is disabled. cpo=" + cpo + ",cco=" + cco );
                return cues;
            }

            Iterator<CreditUtilizationEvent> resultIterator = CreditDataQueryServiceFactory.getCreditDataQueryService().getSpacesCreditUtilizationEventsForCounterpartyOrg( cpo, cco );
            if ( resultIterator != null )
            {
                while ( resultIterator.hasNext() )
                {
                    CreditUtilizationEvent object = resultIterator.next();

                    SpaceCreditUtilizationEventC cue = ( SpaceCreditUtilizationEventC ) object;
                    if ( cue != null && cue.isActive() )
                    {
                        cues.add( cue );
                    }
                }
            }
        }
        catch ( Exception e )
        {
            log.error( "CUM.getSpacesCreditUtilizationEventsForCounterpartyOrg : Exception while retrieving the credit utilization event for cpo=" + cpo + ",cco=" + cco, e );
        }
        return cues;
    }

    private ISSpacesPersistenceService getISCreditPersistenceService()
    {
        if ( spacesPersistenceService == null )
        {
            spacesPersistenceService = PersistenceServiceFactory.getCreditPersistenceService();
            if ( spacesPersistenceService == null )
            {
                log.error( "CUM.getISCreditPersistenceService : Could not get spaces credit persistence service." );
            }
        }
        return spacesPersistenceService;
    }

    /**
     * Recalculates the collection of active credit utilization events and credit utilizations based on the market rate
     * on the business date from the market dataset specified in the ruleset on the credit limit ruleset.
     *
     * @param orgName2CreditUtilList collection of credit utilizations by namespace.
     * @throws IdcException IdcException
     */
    private void recalculateCreditUtilizationCollectionForNextDate( Session session, Map<String, Collection<CreditUtilization>> orgName2CreditUtilList, IdcDate date ) throws IdcException
    {
        for( Map.Entry<String, Collection<CreditUtilization>> entry: orgName2CreditUtilList.entrySet() )
        {
            if( CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationPersistenceOracleEnabled( entry.getKey() ) )
            {
                recalculateCreditUtilizationCollectionIntoOracleForNextDate( session, entry.getValue(), date );
            }
        }
    }

    private void recalculateCreditUtilizationCollectionIntoOracleForNextDate( Session session, Collection<CreditUtilization> creditUtilList, IdcDate date ) throws IdcException
    {
        if( session==null )
        {
            session = PersistenceFactory.newSession();
        }

        try
        {
            UnitOfWork uow = session.acquireUnitOfWork();
            uow.addReadOnlyClasses( getReadOnlyClasses() );

            Collection<CreditUtilization> registeredCus = new ArrayList<CreditUtilization>( );
            for( CreditUtilization cu : creditUtilList )
            {
                if( CreditLimitConfigurationFactory.getCreditConfigurationMBean().isCreditUtilizationLookupSpacesEnabled(cu.getNamespace().getShortName()))
                {
                    //The object at hand is an RDS object. Retrieve the corresponding oracle object and recalculate
                    cu = CreditDataQueryServiceFactory.getCreditDataQueryService().getCreditUtilizationFromOracle(cu);
                }

                cu.markNextDatePositionsDirty ();
                CreditUtilization registeredCu = ( CreditUtilization ) uow.registerObject( cu );
                registeredCus.add( registeredCu );
            }

            reallyRecalculateCreditUtilizationForNextDate( registeredCus, date );
            uow.commit();
        }
        catch ( IdcOptimisticLockException ole )
        {
            log.info( "CUM.recalculateCreditUtilizationCollectionIntoOracleForNextDate : OLE while recalculating credit util." );

            //handle the optimistic lock exception by refreshing and recalculating each credit utilization one at a time.
            for ( CreditUtilization cu : creditUtilList )
            {
                refreshAndRecalculateCreditUtilizationForNextDate( session, cu, date );
            }
        }
        catch ( Exception e )
        {
            log.error( "CUM.recalculateCreditUtilizationCollectionIntoOracleForNextDate - Exception while recalculating credit util.", e );
        }
    }

    private void reallyRecalculateCreditUtilizationForNextDate( Collection<CreditUtilization> creditUtilList, IdcDate date )
    {
        for ( CreditUtilization cu : creditUtilList )
        {
            if ( cu != null )
            {
                if ( cu.getCreditLimitRule() != null && cu.getCreditLimitRule().getCreditUtilizationCalculator() != null )
                {
                    cu.setNextDatePositionDate ( date );
                    CurrencyPositionCollection cpc = cu.getCreditLimitRule ().getCreditUtilizationCalculator ().getCurrencyPositions ( cu, date );
                    String nextDatePositions = null;
                    if ( cpc != null && cpc.hasPositions () )
                    {
                        nextDatePositions = cpc.generatePositionSnapshot ();
                    }
                    cu.setNextDatePositions ( nextDatePositions );
                    cu.setNextDatePositionLastUpdatedTime ( System.currentTimeMillis () );

                    log.info( new StringBuilder( 600 ).append( "CUM.reallyRecalculateCreditUtilizationForNextDate : recalculated utilization for cu=" )
                            .append( cu ).append( ",clr=" ).append( cu.getCreditLimitRule() )
                            .append( ",nextDatePositions=" ).append( nextDatePositions ).append( ",nextDate=" ).append( date ).toString () );
                }
                else
                {
                    cu.setNextDatePositions ( null );
                    cu.setNextDatePositionDate ( null );
                    cu.setNextDatePositionConfigChecksum ( null );
                    log.info( "CUM.reallyRecalculateCreditUtilizationForNextDate : no credit limit rule or calculator for cu=" + cu + ",clr=" + cu.getCreditLimitRule() );
                }
            }
        }
    }

    /**
     * Refresh the credit utilization and recalculate the utilization after resetting and rebuilding the currency positions. Since each credit
     * utilization is refreshed and retried 3 times, any optimistic lock exception after that is aborted.
     *
     * @param session       session
     * @param cu            credit utilization
     */
    private void refreshAndRecalculateCreditUtilizationForNextDate( Session session, CreditUtilization cu, IdcDate date )
    {
        int retryNum = 0;

        while ( true )
        {
            try
            {
                UnitOfWork uow = session.acquireUnitOfWork();
                uow.addReadOnlyClasses( getReadOnlyClasses() );
                cu = ( CreditUtilization ) uow.refreshObject( cu );
                if ( cu != null )
                {
                    cu.markNextDatePositionsDirty ();
                    CreditUtilization registeredCu = ( CreditUtilization ) uow.registerObject( cu );
                    if ( registeredCu.getCreditLimitRule() != null && registeredCu.getCreditLimitRule().getCreditUtilizationCalculator() != null )
                    {
                        registeredCu.setNextDatePositionDate ( date );
                        CurrencyPositionCollection cpc = registeredCu.getCreditLimitRule ().getCreditUtilizationCalculator ().getCurrencyPositions ( registeredCu, date );
                        String nextDatePositions = null;
                        if ( cpc != null && cpc.hasPositions () )
                        {
                            nextDatePositions = cpc.generatePositionSnapshot ();
                        }
                        registeredCu.setNextDatePositions ( nextDatePositions );
                        registeredCu.setNextDatePositionLastUpdatedTime ( System.currentTimeMillis () );

                        log.info( new StringBuilder( 600 ).append( "CUM.refreshAndRecalculateCreditUtilizationForNextDate : recalculated utilization for cu=" )
                                .append( registeredCu ).append( ",clr=" ).append( registeredCu.getCreditLimitRule() )
                                .append( ",nextDatePositions=" ).append( nextDatePositions ).append( ",nextDate=" ).append( date ).toString () );
                    }
                    else
                    {
                        registeredCu.setNextDatePositions ( null );
                        registeredCu.setNextDatePositionDate ( null );
                        registeredCu.setNextDatePositionConfigChecksum ( null );
                        log.info( "CUM.refreshAndRecalculateCreditUtilizationForNextDate : no credit limit rule or calculator for cu=" + cu + ",clr=" + cu.getCreditLimitRule() );
                    }
                }

                uow.commit();
                break;
            }
            catch ( IdcOptimisticLockException ole )
            {
                log.info( "CUM.refreshAndRecalculateCreditUtilizationForNextDate : OLE while recalculating credit util. Retry : " + retryNum );
                retryNum++;

                if ( retryNum >= CreditLimitConstants.MAXIMUM_RETRY )
                {
                    log.warn( "CUM.refreshAndRecalculateCreditUtilizationForNextDate : OptimisticLockException maximum retry exceeded for cu=" + cu );
                    break;
                }
            }
            catch ( Exception e )
            {
                log.error( "CUM.refreshAndRecalculateCreditUtilizationForNextDate - Exception while calculating next date position.", e );
                break;
            }
        }
    }
}

