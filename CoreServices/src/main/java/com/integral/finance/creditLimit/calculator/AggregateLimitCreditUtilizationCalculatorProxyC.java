package com.integral.finance.creditLimit.calculator;


// Copyright (c) 2001-2005 Integral Development Corp. All rights reserved.


/**
 * Aggregate Credit Utilization calculator class is used for aggregate limit credit netting methodology which calculates
 * the net amount by summing up the net receivables on each value date.
 *
 * <AUTHOR> Development Corp.
 */
public class AggregateLimitCreditUtilizationCalculatorProxyC extends CreditUtilizationCalculatorProxyC
{
    AggregateLimitCreditUtilizationCalculatorProxyC( String name )
    {
        super();
        this.setShortName ( name );
    }

    @Override
    protected boolean isDailyAggregate()
    {
        return true;
    }
}
