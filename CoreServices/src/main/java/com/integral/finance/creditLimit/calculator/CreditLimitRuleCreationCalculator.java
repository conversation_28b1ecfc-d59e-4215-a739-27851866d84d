package com.integral.finance.creditLimit.calculator;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.finance.calculator.Calculator;
import com.integral.finance.creditLimit.CounterpartyCreditLimitRule;
import com.integral.finance.creditLimit.CreditLimitClassification;
import com.integral.finance.creditLimit.CreditLimitRule;
import com.integral.finance.creditLimit.CreditUtilizationCalculator;
import com.integral.user.Organization;

/**
 * This interface is used to create the credit limit rule based on the credit limit classification and
 * netting methodology.
 *
 * <AUTHOR> Development Corp.
 */
public interface CreditLimitRuleCreationCalculator extends Calculator
{
    /**
     * Creates credit limit rule and adds it to the counterparty credit limit rule. The type of credit limit rule is determined by the
     * credit limit classification and netting methodology.
     *
     * @param creditProviderOrg
     * @param cclr
     * @param clsf
     * @param calc
     * @param existingClr
     * @return credit limit rule
     */
    public CreditLimitRule createCreditLimitRule( Organization creditProviderOrg, CounterpartyCreditLimitRule cclr, CreditLimitClassification clsf, CreditUtilizationCalculator calc, CreditLimitRule existingClr );
}
