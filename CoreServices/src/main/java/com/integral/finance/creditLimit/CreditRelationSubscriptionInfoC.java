package com.integral.finance.creditLimit;

// Copyright (c) 2012 Integral Development Corp.  All rights reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.workflow.dealing.DealingLimitCollection;

/**
 * This class is used to store the attributes of a credit limit subscription for a credit relationship between
 * credit provider and credit counterparty.
 *
 * <AUTHOR> Development Corporation.
 */
public class CreditRelationSubscriptionInfoC implements CreditRelationSubscriptionInfo
{
    private LegalEntity creditProviderLegalEntity;
    private TradingParty creditCpty;
    private DealingLimitCollection dealingLimits;

    private CreditRelationSubscriptionInfoC()
    {
    }

    public CreditRelationSubscriptionInfoC( LegalEntity le, TradingParty tp )
    {
        this();
        this.creditProviderLegalEntity = le;
        this.creditCpty = tp;
    }

    public LegalEntity getCreditProviderLegalEntity()
    {
        return creditProviderLegalEntity;
    }

    public TradingParty getCreditCounterparty()
    {
        return creditCpty;
    }

    public DealingLimitCollection getDealingLimitCollection()
    {
        return dealingLimits;
    }

    public void setDealingLimitCollection( DealingLimitCollection dlc )
    {
        this.dealingLimits = dlc;
    }

    public String toString()
    {
        return new StringBuilder( 300 ).append( "cpl=" )
                .append( creditProviderLegalEntity ).append( ",cc=" ).append( creditCpty )
                .append( ",dlc=" ).append( dealingLimits ).toString();
    }

}
