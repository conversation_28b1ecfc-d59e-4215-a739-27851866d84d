package com.integral.finance.creditLimit.admin;

// Copyright (c) 2001-2006 Integral Development Corporation.  All Rights Reserved.

import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.creditLimit.CreditUtilizationManagerC;
import com.integral.session.IdcSessionManager;
import com.integral.time.IdcDate;

/**
 * This class is used to revaluate all the aggregate type of credit utilizations based on the valid credit utilization events and latest market data set.
 *
 * <AUTHOR> Development Corp.
 */
public class RevaluateAggregateCreditUtilizationFunctorC extends CreditFunctorC
{
    public RevaluateAggregateCreditUtilizationFunctorC( String name )
    {
        setName( name );
    }

    public void execute()
    {
        try
        {
            if ( isEnabled() && creditAdminConfigMBean.isEndOfDayAggregateCreditUtilizationRevaluationEnabled() )
            {
                start();
                IdcDate businessDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
                IdcSessionManager.getInstance().setTransaction( null );
                CreditUtilizationManagerC.getInstance().recalculateAllAggregateCreditUtilizations( businessDate, this );
                IdcSessionManager.getInstance().setTransaction( null );
                finish();
            }
            else
            {
                log.warn( "RevaluateAggregateCreditUtilizationFunctorC.execute.INFO : Functor not executed. this=" + this + ",isEODCreditUtilizationRevaluationEnabled=" + creditAdminConfigMBean.isEndOfDayAggregateCreditUtilizationRevaluationEnabled() );
            }
        }
        catch ( Exception e )
        {
            log.error( "RevaluateAggregateCreditUtilizationFunctorC.execute.ERROR.", e );
            finish();
        }
    }
}
