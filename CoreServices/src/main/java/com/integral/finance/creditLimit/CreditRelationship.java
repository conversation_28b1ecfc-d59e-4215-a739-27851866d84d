package com.integral.finance.creditLimit;

// Copyright (c) 2001-2005 Integral Development Corp.  All rights reserved.

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.trade.Tenor;

/**
 * This class is used to define a credit relationship whether it is at org level or legal entity level
 */
public class CreditRelationship
{
    private LegalEntity creditProviderLe;
    private TradingParty creditCounterparty;
    private String displayKey = null;
    private CounterpartyCreditLimitRule activeCclr, orgCclr, tpCclr;
    private CreditTenorRestriction minTenor;
    private CreditTenorRestriction maxTenor;

    public CreditRelationship( LegalEntity creditProviderLe, TradingParty creditCounterparty )
    {
        this.creditProviderLe = creditProviderLe;
        this.creditCounterparty = creditCounterparty;
    }

    public LegalEntity getCreditProviderLe()
    {
        return creditProviderLe;
    }

    public TradingParty getCreditCounterparty()
    {
        return creditCounterparty;
    }

    public boolean equals( CreditRelationship relation )
    {
        return creditProviderLe.isSameAs( relation.getCreditProviderLe() ) && creditCounterparty.isSameAs( relation.getCreditCounterparty() );
    }

    public int hashCode()
    {
        return toString().hashCode();
    }

    public CounterpartyCreditLimitRule getActiveCptyRule()
    {
        return activeCclr;
    }

    public CounterpartyCreditLimitRule getOrgLevelCptyRule()
    {
        return orgCclr;
    }

    public CounterpartyCreditLimitRule getTPLevelCptyRule()
    {
        return tpCclr;
    }

    public boolean isInactivated()
    {
        return orgCclr != null && !orgCclr.isActive () && tpCclr != null && !tpCclr.isActive ();
    }

    public String getDisplayKey()
    {
        if ( displayKey == null )
        {
            LegalEntity legalEntity = creditCounterparty != null ? creditCounterparty.getLegalEntity () : null;
            displayKey = new StringBuilder( 128 ).append( creditProviderLe.getOrganization().getShortName() ).append( " -> " )
                    .append( legalEntity != null ? legalEntity.getFullyQualifiedName () : "N/A" ).toString();
        }
        return displayKey;
    }

    public void initTransients()
    {
        activeCclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty( creditProviderLe.getOrganization(), creditCounterparty, false );
        orgCclr = CreditUtilC.getOrgLevelCounterpartyCreditLimitRule(creditProviderLe.getOrganization(), creditCounterparty);
        tpCclr = CreditUtilC.getTradingPartyLevelCounterpartyCreditLimitRule(creditProviderLe.getOrganization(), creditCounterparty);

        if ( activeCclr != null )
        {
            activeCclr.getRuleSet ();// added to initialize the value holder to avoid query.
        }

        initTenors();
    }

    public void initTenors()
    {
        boolean look4LEConfig = ( null!=activeCclr && activeCclr.isOrgLevel() && activeCclr.isLeOverride());
        look4LEConfig = look4LEConfig && (null!=tpCclr && !tpCclr.isOrgDefault());
        if (look4LEConfig)
        {
            minTenor = CreditUtilC.getMinimumTenor(tpCclr, false);
            maxTenor = CreditUtilC.getMaximumTenor(tpCclr, false );
        }
        else
        {
            minTenor = CreditUtilC.getMinimumTenor(activeCclr, true );
            maxTenor = CreditUtilC.getMaximumTenor(activeCclr, true );
        }
    }

    public CreditTenorRestriction getMinimumTenor()
    {
        return minTenor;
    }

    public CreditTenorRestriction getMaximumTenor()
    {
        return maxTenor;
    }
    public String toString()
    {
        return getDisplayKey();
    }
}
