package com.integral.finance.creditLimit.quickcheck;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CounterpartyCreditLimitRule;
import com.integral.finance.creditLimit.CreditEntity;
import com.integral.finance.creditLimit.CreditLimitSubscriptionManagerC;
import com.integral.finance.creditLimit.CreditUtilC;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.price.fx.FXPrice;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.BidOfferMode;
import com.integral.model.dealing.DealingModelRef;
import com.integral.model.dealing.MatchEvent;
import com.integral.model.dealing.OrderMatchRequest;
import com.integral.model.dealing.SingleLegOrderMatch;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.model.dealing.descriptor.CoveredTradeDescriptor;
import com.integral.model.rw.RWChannels;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.persistence.util.LogUtil;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.util.StringUtilC;
import com.integral.util.collections.ConcurrentHashSet;
import com.integral.workflow.dealing.fx.FXDealingLimit;
import com.integral.workflow.dealing.fx.FXWorkflowFactory;


/**
 * This class serves as a cache for CreditLimit wrappers based on the FI, LP and currency pair combination.
 * <AUTHOR>
 */

public class CreditLineManagerC
{

    private final Log log = LogFactory.getLog( getClass() );
    
    //Singleton instance
    private static CreditLineManagerC _creditLineManager = new CreditLineManagerC();
    
    private final ConcurrentMap<CreditLineCollectionKey, CreditLineCollection> fiLpCcyPair2CreditLimitWrapper = new ConcurrentHashMap<CreditLineCollectionKey, CreditLineCollection>();
	private final ConcurrentMap<CurrencyPairLevelCreditLineKey, CurrencyLevelCreditLine> cpoCcCcyValueDate2CreditLine = new ConcurrentHashMap<CurrencyPairLevelCreditLineKey, CurrencyLevelCreditLine>();
	private final ConcurrentMap<CurrencyPairLevelCreditLineKey, CurrencyLevelCreditLine> cpoCcOrigCcyValueDate2CreditLine = new ConcurrentHashMap<CurrencyPairLevelCreditLineKey, CurrencyLevelCreditLine>();
    private final Set<CurrencyLevelCreditLine> orphanCreditLines = new ConcurrentHashSet<CurrencyLevelCreditLine>( );
    private final ConcurrentMap<String, CreditLine> cpoCc2CreditLine = new ConcurrentHashMap<String, CreditLine>();
    private final ConcurrentMap<String, Set<CreditLine>> cpoCco2CreditLine = new ConcurrentHashMap<String, Set<CreditLine>>();

    static final Set<String> noRatesCurrencyPairs = new ConcurrentHashSet<String>();
    private long noRatesCurrencyPairsLastLoggedTime = System.currentTimeMillis();
	
	private final FXDealingLimit dummyEmptyDealingLimit = FXWorkflowFactory.newFXDealingLimit();

	private volatile boolean disableReloadTasks;
	private static final AtomicLong recalculationCounter = new AtomicLong ();


	//Private constructor.
    protected CreditLineManagerC()
    {   
    	log.info("Starting ReloadAllCreditLinesTask. executes every 1 minute");
    	ReloadAllCreditLinesTask reloadAllCreditLinesTask = new ReloadAllCreditLinesTask();
		ScheduledExecutorService reloadScheduler = Executors.newScheduledThreadPool(1, new ThreadFactory() 
		{			
			@Override
			public Thread newThread(Runnable r) {
				return new Thread( new ThreadGroup("ReloadAllCreditLinesGroup"), r, "ReloadAllCreditLinesTask");
			}
		});
		long reloadDelayInMills = RuntimeFactory.getServerRuntimeMBean().getQuickCreditCheckReloadTaskDelayInMills();
		reloadScheduler.scheduleWithFixedDelay(reloadAllCreditLinesTask, reloadDelayInMills, reloadDelayInMills, TimeUnit.MILLISECONDS);
    	
    	log.info("Starting RecalculateLimitsForAllSubscriptionsTask. Executes every 10 Mills");
    	RecalculateLimitsForAllSubscriptionsTask creditLimitRecalculationTask = new RecalculateLimitsForAllSubscriptionsTask();
		ScheduledExecutorService reEvaluateScheduler = Executors.newScheduledThreadPool(1, new ThreadFactory() 
		{			
			@Override
			public Thread newThread(Runnable r) {
				return new Thread( new ThreadGroup("RecalculateLimitsForAllSubscriptionsGroup"), r, "RecalculateLimitsForAllSubscriptionsTask");
			}
		});
		long recalcDelayInMills = RuntimeFactory.getServerRuntimeMBean().getQuickCreditCheckLimitsRecalcTaskDelayInMills();
		reEvaluateScheduler.scheduleWithFixedDelay(creditLimitRecalculationTask, recalcDelayInMills, recalcDelayInMills, TimeUnit.MILLISECONDS);
    }
    
    
    /** Returns the singleton instance of CreditLineManager.*/
    public static CreditLineManagerC getInstance()
    {
        return _creditLineManager;
    }


    public static void setInstance(CreditLineManagerC clmc)
    {
        _creditLineManager = clmc ;
    }
    
    public CreditLineCollectionKey getKey(LegalEntity lpLe, LegalEntity fiLe, CurrencyPair ccyPair){
    	return new CreditLineCollectionKey(lpLe.getIndex(), fiLe.getIndex(), ccyPair.getIndex());
    }

    public String getCreditLineKey( Organization cpo, TradingParty cc )
	{
    	StringBuilder sb = new StringBuilder(40);
    	sb.append( cpo.getShortName() ).append( ':' ).append( cc.getObjectID() );
    	return sb.toString();
    }

	public String getCreditLineKey( Organization cpo, Organization cco )
	{
		StringBuilder sb = new StringBuilder(40);
		sb.append( cpo.getShortName() ).append( ':' ).append( cco.getShortName() );
		return sb.toString();
	}

	/**
	 * Deprecated. This method will be removed eventually. Use getAvailableDealingLimit instead
	 */
	@Deprecated
	public double getAvailableCredit(LegalEntity fiLe, LegalEntity lpLe, String ccyPairStr, IdcDate valueDate, boolean isBid, boolean forOrderMatch)
    {    
		CurrencyPair ccyPair = CurrencyFactory.getCurrencyPairFromString(ccyPairStr);
		CreditLineCollectionKey creditLineCollectionLookupKey = new CreditLineCollectionKey( lpLe.getIndex(), fiLe.getIndex(), ccyPair.getIndex() );
    	CreditLineCollection creditLineCollection = fiLpCcyPair2CreditLimitWrapper.get( creditLineCollectionLookupKey );
    	if(creditLineCollection==null || !creditLineCollection.forSameValueDate(valueDate))
    	{
			IdcDate existingValueDate = creditLineCollection != null ? creditLineCollection.getValueDate () : null;
    		creditLineCollection = lazyLoadBilateralCreditLimits(fiLe, lpLe, ccyPair, valueDate);
			fiLpCcyPair2CreditLimitWrapper.put( creditLineCollectionLookupKey, creditLineCollection );
			log.info ( "CLM.getAvailableCredit - put the lazily loaded credit line collection in the cache for LP="
					+  lpLe.getFullyQualifiedName () + ",fiLE=" + fiLe.getFullyQualifiedName () + ",existingValueDate="
					+ existingValueDate + ",valueDate=" + valueDate + ",creditLineCollection=" + creditLineCollection );
		}
        if(log.isDebugEnabled()){
            log.debug( "CLM.getAvailableCredit from collection: " + creditLineCollection );
        }
    	return creditLineCollection.getAvailableCredit(isBid, forOrderMatch);
    }

	public FXDealingLimit getAvailableDealingLimit ( CreditLineCollectionKey subscriptionKey, boolean forOrderMatch)
	{
		CreditLineCollection creditLineCollection = fiLpCcyPair2CreditLimitWrapper.get( subscriptionKey );
		if ( creditLineCollection == null )
		{
			log.info( "CLM.getAvailableDealingLimit : no credit subscription for key=" + subscriptionKey  );
			return dummyEmptyDealingLimit;
		}
        if(log.isDebugEnabled()){
            log.debug( "CLM.getAvailableDealingLimit from collection: " + creditLineCollection );
        }
		return creditLineCollection.getAvailableDealingLimit(forOrderMatch);
	}

	private CreditLineCollection lazyLoadBilateralCreditLimits(LegalEntity fiLe,
			LegalEntity lpLe, CurrencyPair ccyPair, IdcDate valueDate) 
	{
		StringBuilder sb = new StringBuilder(100);
		sb.append("Could not find a quick check credit subscription for fiLe=").append(fiLe);
		sb.append("|lpLe=").append(lpLe);
		sb.append("|ccyPair=").append(ccyPair.getName());
		sb.append("|date=").append(valueDate).append(". So loading it now lazily !");
		log.info(sb.toString());
		// Create CreditLineCollection directly with whatever the LP is (it could be a mask or a real one!). 		
		CreditLineCollection creditLineCollection = createCreditLineCollection(fiLe, lpLe, ccyPair, valueDate);	
		CreditLimitSubscriptionManagerC.getInstance().loadBilateralCreditLimitsForOCX( fiLe, lpLe, ccyPair, valueDate, false, true, creditLineCollection);					
		return creditLineCollection;
	}

    public ReserveCreditResult reserveCredit(LegalEntity fiLe, LegalEntity lpLe, CurrencyPair ccyPair,
                                             IdcDate valueDate, double amount, SingleLegTrade trade)
    {
		String autoCloseChannel = RWChannels.getChannelConfig ().getAutoCloseHedgeOpenPositionChannel ();
		if ( autoCloseChannel != null && autoCloseChannel.equals ( trade.getChannel () ) )
		{
			log.info ( "CLM.reserveCredit - skipping credit check on margin close calls. tradeId=" + trade.get_id ()
					+ ",channel=" + trade.getChannel () );
			return ReserveCreditResult.SUCCESS;
		}
        List<CreditEntity> creditEntities = getAllCreditEntities( trade, true );
        return reserveCredit(fiLe, lpLe, ccyPair, valueDate, amount, isBid(trade),
                getOriginalTradeId(trade.get_id()), creditEntities);
    }

	public ReserveCreditResult reserveCreditSingleLevel(LegalEntity fiLe, LegalEntity lpLe, CurrencyPair ccyPair,
											 IdcDate valueDate, double amount, SingleLegTrade trade)
	{
		List<CreditEntity> creditEntities = getAllCreditEntities( trade, false );
		return reserveCredit(fiLe, lpLe, ccyPair, valueDate, amount, isBid(trade),
				getOriginalTradeId(trade.get_id()), creditEntities);
	}


	private List<CreditEntity> getAllCreditEntities( SingleLegTrade trade, boolean doTradeChain )
    {
        List<CreditEntity> creditEntities = new ArrayList<CreditEntity>();
        if ( !doTradeChain )
		{
			creditEntities.addAll ( trade.getCreditEntities () );
		}
		else
		{
			SingleLegTrade tradeInConsideration = trade;
			while(tradeInConsideration!=null)
			{
				creditEntities.addAll ( tradeInConsideration.getCreditEntities () );
				CoveredTradeDescriptor coveredTradeDesc = tradeInConsideration.getCoveredTrade ();
				if ( coveredTradeDesc != null )
				{
					DealingModelRef<SingleLegTrade> coveredTradeRef = coveredTradeDesc.getTradeRef ();
					if ( coveredTradeRef != null )
					{
						tradeInConsideration = coveredTradeRef.getObject ();
					}
				}
			}
        }
        return creditEntities;
    }

    public ReserveCreditResult reserveCredit(LegalEntity fiLe, LegalEntity lpLe, CurrencyPair ccyPair,
                                             IdcDate valueDate, double amount, OrderMatchRequest venueMatchRequest)
    {
        return reserveCredit(fiLe, lpLe, ccyPair, valueDate, amount, isBid(venueMatchRequest),
                venueMatchRequest.get_id(), venueMatchRequest.getCreditEntities());
    }

    private ReserveCreditResult reserveCredit(LegalEntity fiLe, LegalEntity lpLe, CurrencyPair ccyPair,
    		IdcDate valueDate, double amount, boolean isBid, String transactionId, List<CreditEntity> creditEntities)
    {
    	long t1 = System.currentTimeMillis();
		CreditLineCollectionKey creditLineCollectionLookupKey = new CreditLineCollectionKey( lpLe.getIndex(), fiLe.getIndex(), ccyPair.getIndex() );
    	CreditLineCollection creditLineCollection = fiLpCcyPair2CreditLimitWrapper.get( creditLineCollectionLookupKey );
    	if(creditLineCollection==null  || !creditLineCollection.forSameValueDate(valueDate))
    	{
			IdcDate existingValueDate = creditLineCollection != null ? creditLineCollection.getValueDate () : null;
    		creditLineCollection = lazyLoadBilateralCreditLimits(fiLe, lpLe, ccyPair, valueDate);
			fiLpCcyPair2CreditLimitWrapper.put ( creditLineCollectionLookupKey, creditLineCollection );
			log.info ( "CLM.reserveCredit - put the lazily loaded credit line collection in the cache for LP="
					+  lpLe.getFullyQualifiedName () + ",fiLE=" + fiLe.getFullyQualifiedName () + ",existingValueDate="
					+ existingValueDate + ",valueDate=" + valueDate + ",creditLineCollection=" + creditLineCollection );
    	}    	
    	ReserveCreditResult result = creditLineCollection.reserveCredit(amount, isBid, transactionId, creditEntities);
    	if(log.isDebugEnabled())
    	{
    		long t2 = System.currentTimeMillis()-t1;
    		log.debug("CLM.reserveCredit : TIMETAKEN = " + t2);
    	}
    	return result;    	
    }

    private String getOriginalTradeId(String tradeId){
        int index = tradeId.indexOf('C');
        if(index==-1){
            return tradeId;
        }
        return tradeId.substring(0, index);
    }


    private boolean isBid(OrderMatchRequest venueMatchRequest) {
        MatchEvent.MatchEventLeg matchEventLeg = venueMatchRequest.getMatchEventLeg();
        return matchEventLeg.getBuySellMode() == MatchEvent.MatchEventLeg.BuySellMode.SELL;
    }


    private boolean isBid(SingleLegTrade trade) {
        SingleLegOrderMatch match = trade.getMatchEvent();
        if(null != match.getMatchedQuote()){
            return match.getMatchedQuote().getBidOfferMode() == BidOfferMode.BID;
        }
        else{
            return match.getMatchEventLeg().getBuySellMode() == MatchEvent.MatchEventLeg.BuySellMode.SELL;
        }
    }

    public void releaseAndrefreshSubscriptionLevel(LegalEntity lpLe, LegalEntity fiLe, CurrencyPair ccyPair, SingleLegTrade trade)
    {
		releaseAndRefreshSubscription( lpLe, fiLe, ccyPair, trade, true );
    }

	public void releaseAndrefreshSubscriptionSingleLevel(LegalEntity lpLe, LegalEntity fiLe, CurrencyPair ccyPair, SingleLegTrade trade)
	{
		releaseAndRefreshSubscription( lpLe, fiLe, ccyPair, trade, false );
	}

	private void releaseAndRefreshSubscription( LegalEntity lpLe, LegalEntity fiLe, CurrencyPair ccyPair, SingleLegTrade trade, boolean doTradeChain )
	{
		CreditLineCollectionKey creditLineCollectionLookupKey = new CreditLineCollectionKey( lpLe.getIndex(), fiLe.getIndex(), ccyPair.getIndex() );
		CreditLineCollection creditLineCollection = fiLpCcyPair2CreditLimitWrapper.get( creditLineCollectionLookupKey );
		if( creditLineCollection!=null )
		{
			List<CreditEntity> creditEntities = getAllCreditEntities( trade, doTradeChain );
			creditLineCollection.releaseAndrefresh(getOriginalTradeId( trade.get_id()), creditEntities );
		}
	}

	public String releaseLineLevel(CurrencyPairLevelCreditLineKey lookupKey, String transactionId)
    {
    	String result = null;
        lookupKey.setCpoMaker( true );
    	CurrencyLevelCreditLine currencyLevelCreditLineAsMaker = cpoCcCcyValueDate2CreditLine.get(lookupKey);
    	if(currencyLevelCreditLineAsMaker!=null)
    	{
            result = currencyLevelCreditLineAsMaker.release(transactionId);
    	}

		CurrencyLevelCreditLine origCurrencyLevelCreditLineAsMaker = cpoCcOrigCcyValueDate2CreditLine.get ( lookupKey );
    	if ( origCurrencyLevelCreditLineAsMaker != null )
		{
			result += origCurrencyLevelCreditLineAsMaker.release ( transactionId );
		}

        lookupKey.setCpoMaker( false );
        CurrencyLevelCreditLine currencyLevelCreditLineAsTaker = cpoCcCcyValueDate2CreditLine.get(lookupKey);
        if(currencyLevelCreditLineAsTaker!=null)
        {
            result += currencyLevelCreditLineAsTaker.release(transactionId);
        }

		CurrencyLevelCreditLine origCurrencyLevelCreditLineAsTaker = cpoCcOrigCcyValueDate2CreditLine.get ( lookupKey );
		if ( origCurrencyLevelCreditLineAsTaker != null )
		{
			result += origCurrencyLevelCreditLineAsTaker.release ( transactionId );
		}
        return result;
    }

    public CurrencyLevelCreditLine getCurrencyLevelCreditLine(CurrencyPairLevelCreditLineKey lookupKey, boolean isCpoMaker)
    {
        lookupKey.setCpoMaker( isCpoMaker);
        return cpoCcCcyValueDate2CreditLine.get(lookupKey);
    }
    
    public CreditLineCollection getCreditLineCollectionAddIfNotExists(LegalEntity fiLe, LegalEntity lpLe, CurrencyPair ccyPair, IdcDate valueDate)
    {
		CreditLineCollectionKey creditLineCollectionLookupKey = new CreditLineCollectionKey( lpLe.getIndex(), fiLe.getIndex(), ccyPair.getIndex() );
    	CreditLineCollection creditLineCollection = fiLpCcyPair2CreditLimitWrapper.get( creditLineCollectionLookupKey );
    	if(creditLineCollection==null)
    	{    	
    		creditLineCollection = createCreditLineCollection(fiLe, lpLe,ccyPair, valueDate);
    	}else{
    		creditLineCollection.setValueDate(valueDate);
    	}
    	return creditLineCollection;
    }


	private CreditLineCollection createCreditLineCollection(LegalEntity fiLe,
			LegalEntity lpLe, CurrencyPair ccyPair, IdcDate valueDate) 
	{
		CreditLineCollectionKey key = new CreditLineCollectionKey(lpLe.getIndex(), fiLe.getIndex(), ccyPair.getIndex());
		CreditLineCollection creditLineCollection = new CreditLineCollectionC(fiLe, lpLe, ccyPair, valueDate);
		fiLpCcyPair2CreditLimitWrapper.put(key, creditLineCollection);
		return creditLineCollection;
	}


    public CurrencyLevelCreditLine getCurrencyLevelCreditLineLoadIfNotExists(CreditEntity creditEntity, CurrencyPair ccyPair, IdcDate valueDate)
    {
        LegalEntity fiLe = creditEntity.getLegalEntity();
        Organization cpo = fiLe.getOrganization();
        TradingParty tp = creditEntity.getTradingParty();
        if( creditEntity.getLegalEntity().getOrganization().isSameAs(creditEntity.getOrganization())){
        	return null;
		}
        CurrencyPairLevelCreditLineKey cpLevelCreditLineLookupKey = new CurrencyPairLevelCreditLineKey(
                                                                    cpo.getIndex(), tp.getObjectId(),ccyPair.getIndex(),
                                                                    valueDate.getDays(), creditEntity.isMaker());

        CurrencyLevelCreditLine line = cpoCcCcyValueDate2CreditLine.get(cpLevelCreditLineLookupKey);
        if(line == null)
        {
            log.info("Credit line not found so loading lazily for creditEntity=" + creditEntity);
            Map<String, FXPrice> rateMap = new HashMap<String, FXPrice>();
            line = CreditLimitSubscriptionManagerC.getInstance().getCreditLineFromCreditEntity(ccyPair, valueDate, rateMap, fiLe, tp, creditEntity.isMaker());
            if(line!=null)
            {
                line.setOrphan( true );
            }
        }
        return line;
    }

    public CurrencyLevelCreditLine updateCcyPairLevelCreditLineOrCreateIfNotExisting(
            LegalEntity cpl, TradingParty tp, boolean isCpoMaker,
			CurrencyPair ccyPair, IdcDate valueDate, double bidLimit,
			double offerLimit, boolean suspended, Currency limitCcy,
			boolean isOrgLevelCredit, boolean isDummy, boolean isCashSettlement )
	{
        Organization cpo = cpl.getOrganization();
		CurrencyPairLevelCreditLineKey cpLevelCreditLineLookupKey = new CurrencyPairLevelCreditLineKey( cpo.getIndex(), tp.getObjectId(), ccyPair.getIndex(), valueDate.getDays(), isCpoMaker );
		CurrencyLevelCreditLine currencyLevelCreditLine = cpoCcCcyValueDate2CreditLine.get(cpLevelCreditLineLookupKey);
		CreditLine creditLine = updateCreditLineOrCreateIfNotExisting(cpo, tp, isOrgLevelCredit, limitCcy, suspended, isCashSettlement );
		if(currencyLevelCreditLine==null)
        {
            CurrencyPairLevelCreditLineKey key = new CurrencyPairLevelCreditLineKey(cpo.getIndex(), tp.getObjectId(), ccyPair.getIndex(), valueDate.getDays(), isCpoMaker );
            currencyLevelCreditLine = new CurrencyLevelCreditLine(cpl, tp, isCpoMaker, ccyPair, valueDate, bidLimit, offerLimit, creditLine, isDummy, isCashSettlement );
			cpoCcCcyValueDate2CreditLine.put(key, currencyLevelCreditLine);

			// add parent currency pair in case of forward currencies.
			if ( ccyPair.isNonSpotSettlementType () )
			{
				CurrencyPair origCcyPair = CurrencyFactory.getOriginalCurrencyPair ( ccyPair );
				CurrencyPairLevelCreditLineKey key1 = new CurrencyPairLevelCreditLineKey(cpo.getIndex(), tp.getObjectId(), origCcyPair.getIndex(), valueDate.getDays(), isCpoMaker );
				cpoCcOrigCcyValueDate2CreditLine.put(key1, currencyLevelCreditLine);
			}
		}
        else
        {
			currencyLevelCreditLine.setDummy(isDummy);
			currencyLevelCreditLine.resetCredit(bidLimit, offerLimit);
			currencyLevelCreditLine.setSuspend(suspended);
			currencyLevelCreditLine.setCreditLine(creditLine);
			currencyLevelCreditLine.initLimitCcyBaseCcyPrice();
			currencyLevelCreditLine.setCashSettlement ( isCashSettlement );
		}    		
		return currencyLevelCreditLine;
    }
    
    private CreditLine updateCreditLineOrCreateIfNotExisting(Organization cpo, TradingParty tp, boolean isOrgLevelCredit, Currency limitCcy, boolean suspended, boolean cash )
    {
		String key =  isOrgLevelCredit ? getCreditLineKey( cpo, tp.getLegalEntityOrganization() ) : getCreditLineKey( cpo, tp );
    	if( key != null )
    	{
    		CreditLine creditLine = cpoCc2CreditLine.get( key );
    		if( creditLine==null )
    		{
    			creditLine = new CreditLine(limitCcy, suspended, isOrgLevelCredit, cash );
    			cpoCc2CreditLine.put( key, creditLine );
    			addCpoCco2CreditLine(cpo, tp.getLegalEntityOrganization(), creditLine);
    		}
    		else
    		{
    			creditLine.setSuspend( suspended );
				creditLine.setLimitCurrency( limitCcy );
				creditLine.setOrgLevel( isOrgLevelCredit );
    		}    		
    		return creditLine;
    	}
    	return null;    	
    }
    
    private void addCpoCco2CreditLine(Organization cpo, Organization cco, CreditLine creditLine)
    {
    	String key = getCreditLineKey( cpo, cco);
    	Set<CreditLine> creditLineSet = cpoCco2CreditLine.get(key);
    	if(creditLineSet==null)
		{
    		creditLineSet = new ConcurrentHashSet<CreditLine>();
			if ( key != null )
			{
				cpoCco2CreditLine.putIfAbsent ( key, creditLineSet );
			}
    	}
    	creditLineSet.add(creditLine);
    }

    public boolean isCreditSubscribedBetweenOrgs ( final Organization cpo, final Organization cco )
    {
    	String orgExposureKey = getCreditLineKey( cpo, cco );
    	Set<CreditLine> creditLineSet = cpoCco2CreditLine.get(orgExposureKey);
		return creditLineSet != null && !creditLineSet.isEmpty ();
	}
    
    
	public boolean isCreditLineSubscribed ( final Organization cpo, final TradingParty cc, final Organization cco )
	{
		String orgExposureKey = getCreditLineKey( cpo, cco );
		CreditLine creditLine = cpoCc2CreditLine.get( orgExposureKey );
		if ( creditLine != null )
		{
			return true;
		}

		if ( cc != null )
		{
			String leExposureKey = getCreditLineKey( cpo, cc );
			creditLine = cpoCc2CreditLine.get( leExposureKey );
			return creditLine != null;
		}
		return false;
	}

	
	public Collection<CreditLineCollection> getSubscriptionsAffectedByCreditBetweenOrgs( final Organization cpo, final Organization cco )
	{
		String orgExposureKey = getCreditLineKey(cpo, cco);
		Set<CreditLineCollection> interestedSubscriptions = new ConcurrentHashSet<CreditLineCollection>();
		Set<CreditLine> creditLines = cpoCco2CreditLine.get(orgExposureKey);
		if ( creditLines != null )
		{
			for ( CreditLine creditLine : creditLines )
			{
				if ( creditLine.getInterestedCreditLineCollections () != null && !creditLine.getInterestedCreditLineCollections ().isEmpty () )
				{
					interestedSubscriptions.addAll ( creditLine.getInterestedCreditLineCollections () );
				}
			}
		}
		return interestedSubscriptions;
	}
	
	
	public Collection<CreditLineCollection> getFILPSubscriptionsForCreditLine( final Organization cpo, final TradingParty cc, final Organization cco )
	{
		if ( cco != null )
		{
			String orgExposureKey = getCreditLineKey(cpo, cco);
			CreditLine creditLine = cpoCc2CreditLine.get(orgExposureKey);
			if (creditLine == null && cc != null)
			{
				String leExposureKey = getCreditLineKey(cpo, cc);
				creditLine = cpoCc2CreditLine.get(leExposureKey);
			}
			return creditLine != null ? creditLine.getInterestedCreditLineCollections() : null;
		}
		else
		{
			Collection<CreditLineCollection> clcSet = new HashSet<CreditLineCollection>();
			for ( CurrencyLevelCreditLine ccl: cpoCcCcyValueDate2CreditLine.values() )
			{
				if ( ccl.getCreditProvider().equals( cpo.getShortName() ) )
				{
					clcSet.addAll( ccl.getInterestedCreditLineCollections() );
				}
			}
			return clcSet;
		}
	}

    public void removeOrphanLine(CurrencyLevelCreditLine creditLine)
    {
        if(creditLine!=null)
        {
            orphanCreditLines.remove( creditLine );
        }
    }

    public void addOrphanLine(CurrencyLevelCreditLine creditLine)
    {
        if(creditLine!=null)
        {
            orphanCreditLines.add( creditLine );
        }
    }

    public Collection<CurrencyLevelCreditLine> getAffectedOrphanCreditLines(final Organization cpo, final TradingParty cc, final Organization cco)
    {
        List<CurrencyLevelCreditLine> affectedOrphanLines = new ArrayList<CurrencyLevelCreditLine>();
        for(CurrencyLevelCreditLine creditLine : orphanCreditLines)
        {
            if(creditLine.getCreditProviderOrg().getShortName().equals( cpo.getShortName() ))
            {
                if(cco==null && cc==null)
                {
                    affectedOrphanLines.add( creditLine );
                }
                else if(cco!=null)
                {
                    if(cco.getShortName().equals( creditLine.getCreditTradingParty().getLegalEntityOrganization().getShortName()))
                    {
                        affectedOrphanLines.add( creditLine );
                    }
                }
                else
				{
					if(cc.getObjectID() == creditLine.getCreditTradingParty().getObjectID())
					{
						affectedOrphanLines.add( creditLine );
					}
				}
            }
        }
        return affectedOrphanLines;
    }


    public Collection<LP_FI> getInterestedSubscriptions(final Organization cpo, final TradingParty cc, final Organization cco)
    {
    	HashSet<LP_FI> collection = new HashSet<LP_FI>();
    	Collection<CreditLineCollection> creditLineCollections = getFILPSubscriptionsForCreditLine(cpo, cc, cco);
    	if(creditLineCollections!=null)
    	{
			for(CreditLineCollection clc:creditLineCollections)
			{
				collection.add(clc.getLP_FI());
			}
    	}
        return collection;
    }
        

	/**
     * For Diagnostics page
     */
    public CreditLineCollection getCreditLineCollection(String fullyQualifiedFILEName, String fullyQualifiedLPLEName, String ccyPairStr)
    {
    	Organization fi, lp;
    	LegalEntity fiLe, lpLe;
    	if ( !StringUtilC.isNullOrEmpty( fullyQualifiedFILEName ) || !StringUtilC.isNullOrEmpty( fullyQualifiedLPLEName ) )
    	{
    		if ( !fullyQualifiedFILEName.contains ( "@" ) || !fullyQualifiedLPLEName.contains ( "@" ) )
    		{
    			log.error("Please enter valid fi or lp in the legalentity@org format.");
    			return null;
    		}
    		String[] fiParams = fullyQualifiedFILEName.split("@");
    		String[] lpParams = fullyQualifiedLPLEName.split("@");
    		fi = ReferenceDataCacheC.getInstance().getOrganization( fiParams[1] );
    		fiLe = (LegalEntity) ReferenceDataCacheC.getInstance().getEntityByShortName(fiParams[0], LegalEntity.class, fi.getNamespace(), 'A');
    		lp = ReferenceDataCacheC.getInstance().getOrganization ( lpParams[1] );
    		lpLe = (LegalEntity) ReferenceDataCacheC.getInstance().getEntityByShortName(lpParams[0], LegalEntity.class, lp.getNamespace(), 'A');
    		
    		CurrencyPair ccyPair = CurrencyFactory.getCurrencyPairFromString(ccyPairStr);
			CreditLineCollectionKey creditLineCollectionLookupKey = new CreditLineCollectionKey( lpLe.getIndex(), fiLe.getIndex(), ccyPair.getIndex() );
        	return fiLpCcyPair2CreditLimitWrapper.get( creditLineCollectionLookupKey );
    	}	
    	return null;
    }
    
    public Map<CreditLineCollectionKey, CreditLineCollection> getCreditLimitWrapper(){
    	return Collections.unmodifiableMap(fiLpCcyPair2CreditLimitWrapper);
    }
    
    public Collection<CurrencyLevelCreditLine> getAllCreditLines(){
    	return cpoCcCcyValueDate2CreditLine.values();
    }

    public void removeAll()
    {
        log.info( "CLM.removeAll : removing all the credit subscription data." );
        fiLpCcyPair2CreditLimitWrapper.clear();
        cpoCcCcyValueDate2CreditLine.clear();
        cpoCcOrigCcyValueDate2CreditLine.clear ();
        cpoCc2CreditLine.clear();
    }

	public void reloadAndRefreshCreditLineCollections( Collection<CreditLineCollection> creditLineCollections )
	{
		try
		{
			for( CreditLineCollection creditLineCollection: creditLineCollections )
			{
				try
				{
					CreditLimitSubscriptionManagerC.getInstance().
							loadBilateralCreditLimitsForOCX( creditLineCollection.getFILE(), creditLineCollection.getLPLE(),
									creditLineCollection.getCurrencyPair(),creditLineCollection.getValueDate(), false, true, creditLineCollection );
				}
				catch(Exception e)
				{
					log.error( "CLM.reloadAndRefreshAll : Exception while reloading credit lines=" + creditLineCollection, e );
				}
			}
		}
		catch( Exception ex )
		{
			log.error("Error in reloadAndRefreshAll", ex);
		}
	}

    public void purgeRolledOverSubscriptionsAtEOD()
    {
        try
        {
            List<CreditLineCollectionKey> rolledOverSubscriptions = new ArrayList<CreditLineCollectionKey>();

            IdcDate currentBusinessDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
            IdcDate previousBusinessDate = currentBusinessDate.previousDate();

            for(Map.Entry<CreditLineCollectionKey, CreditLineCollection> entry : fiLpCcyPair2CreditLimitWrapper.entrySet() )
            {
                CreditLineCollection creditLineCollection = entry.getValue();
                if(previousBusinessDate.isLaterThan( creditLineCollection.getValueDate() ))
                {
                    rolledOverSubscriptions.add( entry.getKey());
                }
            }

            for(CreditLineCollectionKey key : rolledOverSubscriptions)
            {
                fiLpCcyPair2CreditLimitWrapper.remove( key );
                log.info( "CLM.purgeRolledOverSubscriptions : purged rolled-over subscription: " + key );
            }
        }
        catch(Exception ex)
        {
            log.error( "CLM.purgeRolledOverSubscriptions : ", ex );
        }
    }

	public void reloadAndRefreshAll()
	{
		try
		{
			LogUtil.setDisableDBQueryLogSwitch( true );
			long t1 = System.currentTimeMillis();
			reloadAndRefreshCreditLineCollections( fiLpCcyPair2CreditLimitWrapper.values() );
			if ( log.isDebugEnabled() )
			{
				long t2 = System.currentTimeMillis() - t1;
				log.debug( "CLM.reloadAndRefreshAll : TIMETAKEN = " + t2 + " | Number of Lines=" + getAllCreditLines().size() );
			}
		}
		finally
		{
			LogUtil.removeDisableDBQueryLogSwitch();
		}
	}

	/**
	 * Only used for test cases to stop the periodic reload tasks.
     */
	public void disableReloadTasks( boolean disable )
	{
		disableReloadTasks = disable;
	}

    /**
     * This task is executed periodically to refresh all the subscriptions. Any admin changes to credit relations/limits get covered in this sweep
     */
    class ReloadAllCreditLinesTask implements Runnable
    {    
		public void run()
		{
			if ( !disableReloadTasks )
			{
				reloadAndRefreshAll();
			}
			try
			{
				long now = System.currentTimeMillis();
				if ( now - noRatesCurrencyPairsLastLoggedTime > MetricsManager.instance().getTimerInterval() )
				{
					noRatesCurrencyPairsLastLoggedTime = now;
					Set<String> currencyPairs = new HashSet<String>();
					if ( !noRatesCurrencyPairs.isEmpty() )
					{
						currencyPairs.addAll( noRatesCurrencyPairs );
						log.info( "CLM.ReloadAllCreditLinesTask : No conversion rates found for currency pairs=" + currencyPairs );
						noRatesCurrencyPairs.clear();
					}
				}
			}
			catch ( Exception e )
			{
				log.error( "CLM.ReloadAllCreditLinesTask.run : Exception." , e );
			}
		}
    }
    
    /**
     * This task goes through all the subscriptions and re-evaluates the subscription level bid and offer credit limits for all dirty subscriptions.
     * Currency position sync notifications affecting a subscription (i.e. affecting one or more credit lines that the subscription is interested in),
     * will cause the subscription to be marked dirty. So that it is picked up for credit limit re-evaluation by this task.
     */
    class RecalculateLimitsForAllSubscriptionsTask implements Runnable
    {    
    	private final Log log = LogFactory.getLog( getClass() );
		public void run()
		{
			try
			{
				if ( disableReloadTasks )
				{
					return;
				}
				recalculationCounter.incrementAndGet ();
				for(CreditLineCollection creditLineCollection: fiLpCcyPair2CreditLimitWrapper.values())
				{
					try
					{
						creditLineCollection.calculateMinAvailableAmounts();
					}
					catch( Exception e )
					{
						log.error( "CLM.RecalculateLimits : Exception while recalculating credit lines=" + creditLineCollection, e );
					}
				}
				// refresh orphan lines as well every 10th execution
				if ( recalculationCounter.get () % 10 == 0 && !orphanCreditLines.isEmpty () )
				{
					Set<CurrencyLevelCreditLine> orphanLines = new HashSet<CurrencyLevelCreditLine> ( orphanCreditLines );
					if ( log.isDebugEnabled () )
					{
						log.debug ( "CLM.RecalculateLimits : calculating available limits of orphan credit lines=" + orphanLines );
					}
					for ( CurrencyLevelCreditLine creditLine : orphanLines )
					{
						LegalEntity cple = creditLine.getCreditProviderLe ();
						TradingParty tp = creditLine.getCreditTradingParty ();
						CounterpartyCreditLimitRule cclr = CreditUtilC.getActiveCounterpartyCreditLimitRuleForTradingParty ( cple.getOrganization (), tp, true );
						if ( cclr != null )
						{
							CreditLimitSubscriptionManagerC.getInstance ().refreshCreditLine ( cple, tp,
									creditLine.getCurrencyPair (), creditLine.getValueDate (), creditLine.isCPOMaker (),
									creditLine.getLimitCurrency (), cclr.getTradingParty () == null );
						}
					}
				}
			}
			catch(Exception ex)
			{
				log.error("Error in RecalculateLimitsForAllSubscriptionsTask",ex);
			}
		}
    }
}
