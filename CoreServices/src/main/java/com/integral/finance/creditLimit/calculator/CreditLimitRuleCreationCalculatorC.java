package com.integral.finance.creditLimit.calculator;

// Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.finance.calculator.CalculatorC;
import com.integral.finance.creditLimit.admin.CreditAdminServiceLoggerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * This class is the abstract class for various credit limit rule creation calculators.
 *
 * <AUTHOR> Development Corp.
 */
public abstract class CreditLimitRuleCreationCalculatorC extends CalculatorC implements CreditLimitRuleCreationCalculator
{
    protected Log log = LogFactory.getLog( CreditAdminServiceLoggerC.class );
}

