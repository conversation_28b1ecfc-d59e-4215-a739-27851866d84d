<?xml version="1.0"?>
<definitionMap>

<!-- 

This file represents class and interface mappings for form classes. This file is used by Integral
to dynamically determine the proper form class to use for a domain class.  Mappings for both classes
and interfaces are provided since method signatures may be interface based and not classes. Only base
interfaces and base class need to be put here since the lookup mechanism will search the interface
hierarchy and class hierarchy for a matching entry. 

Customers should extend this file to for new domain classes, for which specific form classes
are needed. If the new domain classes can use a form for their ancestor, provided by Integral,
no modification is necessary.

 end of header -->


<!-- Core Interfaces -->

  <definition>
    <objectType>com.integral.query.Query</objectType>
    <formType>com.integral.jsp.framework.ObjectForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.persistence.Entity</objectType>
    <formType>com.integral.jsp.framework.EntityForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.persistence.NamedEntity</objectType>
    <formType>com.integral.jsp.framework.NamedEntityForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.facade.EntityFacade</objectType>
    <formType>com.integral.jsp.framework.EntityFacadeForm</formType>
  </definition>
  
  <definition>
    <objectType>com.integral.message.WorkflowMessage</objectType>
    <formType>com.integral.jsp.framework.WorkflowMessageForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.persistence.Dependent</objectType>
    <formType>com.integral.jsp.framework.DependentForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.persistence.CustomField</objectType>
    <formType>com.integral.jsp.framework.CustomFieldForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.time.DatePeriod</objectType>
    <formType>com.integral.time.jsp.DatePeriodForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.rule.RuleSet</objectType>
    <formType>com.integral.rule.jsp.RuleSetForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.rule.Rule</objectType>
    <formType>com.integral.rule.jsp.RuleForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.jsp.JSPForwardRuleC</objectType>
    <formType>com.integral.rule.jsp.JSPForwardRuleForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.rule.PredicateCondition</objectType>
    <formType>com.integral.rule.jsp.PredicateConditionForm</formType>
  </definition>
  
  <definition>
      <objectType>com.integral.rule.WorkflowAction</objectType>
      <formType>com.integral.rule.jsp.WorkflowActionForm</formType>
  </definition>
  
  <definition>
      <objectType>com.integral.audit.rule.AuditActionC</objectType>
      <formType>com.integral.rule.jsp.AuditActionForm</formType>
  </definition>  
  
  <definition>
        <objectType>com.integral.rule.SetPropertiesActionC</objectType>
        <formType>com.integral.rule.jsp.SetPropertiesActionForm</formType>
  </definition>  
  
  <definition>
      <objectType>com.integral.rule.MethodAction</objectType>
      <formType>com.integral.rule.jsp.MethodActionForm</formType>
  </definition>

    <definition>
        <objectType>com.integral.rule.SetPropertyAction</objectType>
        <formType>com.integral.rule.jsp.SetPropertyActionForm</formType>
    </definition>

    <definition>
        <objectType>com.integral.rule.SendMessageAction</objectType>
        <formType>com.integral.rule.jsp.SendMessageActionForm</formType>
    </definition>

    <definition>
        <objectType>com.integral.rule.ObjectCondition</objectType>
        <formType>com.integral.rule.jsp.ObjectConditionForm</formType>
    </definition>
    
    <definition>
        <objectType>com.integral.rule.ConditionAttribute</objectType>
        <formType>com.integral.rule.jsp.ConditionAttributeForm</formType>
    </definition>    
    
    <definition>
        <objectType>com.integral.finance.instrument.AmountOfInstrument</objectType>
        <formType>com.integral.jsp.framework.DependentForm</formType>
    </definition>          

  <definition>
    <objectType>com.integral.user.DisplayPreference</objectType>
    <formType>com.integral.user.jsp.DisplayPreferenceForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.workflow.ChangeSet</objectType>
    <formType>com.integral.workflow.jsp.ChangeSetForm</formType>
  </definition>  


<!-- Core Classes -->

  <definition>
    <objectType>com.integral.query.QueryC</objectType>
    <formType>com.integral.jsp.framework.ObjectForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.persistence.EntityC</objectType>
    <formType>com.integral.jsp.framework.EntityForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.persistence.NamedEntityC</objectType>
    <formType>com.integral.jsp.framework.NamedEntityForm</formType>
  </definition>
 
   <definition>
    <objectType>com.integral.facade.EntityFacadeC</objectType>
    <formType>com.integral.jsp.framework.EntityFacadeForm</formType>
  </definition>
 
  <definition>
    <objectType>com.integral.message.WorkflowMessageC</objectType>
    <formType>com.integral.jsp.framework.WorkflowMessageForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.persistence.DependentC</objectType>
    <formType>com.integral.jsp.framework.DependentForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.persistence.OwnedEntityCollectionC</objectType>
    <formType>com.integral.jsp.framework.OwnedEntityCollectionForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.persistence.CustomFieldC</objectType>
    <formType>com.integral.jsp.framework.CustomFieldForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.time.DatePeriodC</objectType>
    <formType>com.integral.time.jsp.DatePeriodForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.rule.RuleSetC</objectType>
    <formType>com.integral.rule.jsp.RuleSetForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.rule.RuleC</objectType>
    <formType>com.integral.rule.jsp.RuleForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.rule.PredicateConditionC</objectType>
    <formType>com.integral.rule.jsp.PredicateConditionForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.user.DisplayPreferenceC</objectType>
    <formType>com.integral.user.jsp.DisplayPreferenceForm</formType>
  </definition>

  <definition>
    <objectType>com.integral.workflow.ChangeSetC</objectType>
    <formType>com.integral.workflow.jsp.ChangeSetForm</formType>
  </definition>  

<definition>
    <objectType>com.integral.persistence.ExternalSystemId</objectType>
    <formType>com.integral.jsp.framework.ExternalSystemIdForm</formType>
</definition>



<!-- Finance Interfaces -->

<definition>
    <objectType>com.integral.finance.fx.FXPaymentParameters</objectType>
    <formType>com.integral.finance.fx.jsp.FXPaymentParametersForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.dealing.Quote</objectType>
    <formType>com.integral.finance.dealing.jsp.QuoteForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.dealing.Request</objectType>
    <formType>com.integral.finance.dealing.jsp.RequestForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.dealing.DealingPriceElement</objectType>
    <formType>com.integral.finance.dealing.jsp.DealingPriceElementForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.dealing.DealingPrice</objectType>
    <formType>com.integral.finance.dealing.jsp.DealingPriceForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.fx.FXSwap</objectType>
    <formType>com.integral.finance.fx.jsp.FXSwapForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.price.DoublePrice</objectType>
    <formType>com.integral.finance.price.jsp.DoublePriceForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.price.fx.FXPrice</objectType>
    <formType>com.integral.finance.price.fx.jsp.FXPriceForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.fx.FXRate</objectType>
    <formType>com.integral.finance.fx.jsp.FXRateForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.instrument.Instrument</objectType>
    <formType>com.integral.finance.instrument.jsp.InstrumentForm</formType>
</definition>

<!-- Finance Classes -->

<definition>
    <objectType>com.integral.finance.fx.FXPaymentParametersC</objectType>
    <formType>com.integral.finance.fx.jsp.FXPaymentParametersForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.dealing.QuoteC</objectType>
    <formType>com.integral.finance.dealing.jsp.QuoteForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.dealing.RequestC</objectType>
    <formType>com.integral.finance.dealing.jsp.RequestForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.dealing.DealingPriceElementC</objectType>
    <formType>com.integral.finance.dealing.jsp.DealingPriceElementForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.dealing.DealingPriceC</objectType>
    <formType>com.integral.finance.dealing.jsp.DealingPriceForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.fx.FXSwapC</objectType>
    <formType>com.integral.finance.fx.jsp.FXSwapForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.price.DoublePriceC</objectType>
    <formType>com.integral.finance.price.jsp.DoublePriceForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.price.fx.FXPriceC</objectType>
    <formType>com.integral.finance.price.fx.jsp.FXPriceForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.fx.FXRateC</objectType>
    <formType>com.integral.finance.fx.jsp.FXRateForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.instrument.InstrumentC</objectType>
    <formType>com.integral.finance.instrument.jsp.InstrumentForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.trade.Tenor</objectType>
    <formType>com.integral.finance.trade.jsp.TenorForm</formType>
</definition>

<!-- Credit Classes -->

<definition>
    <objectType>com.integral.finance.creditLimit.CreditLimitRuleSet</objectType>
    <formType>com.integral.finance.creditLimit.jsp.CreditLimitRuleSetForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.creditLimit.CounterpartyCreditLimitRule</objectType>
    <formType>com.integral.finance.creditLimit.jsp.CounterpartyCreditLimitRuleForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.creditLimit.CreditLimitRule</objectType>
    <formType>com.integral.finance.creditLimit.jsp.CreditLimitRuleForm</formType>
</definition>

<definition>
    <objectType>com.integral.finance.creditLimit.CreditUtilization</objectType>
    <formType>com.integral.finance.creditLimit.jsp.CreditUtilizationForm</formType>
</definition>


<definition>
    <objectType>com.integral.broker.model.StreamC</objectType>
    <formType>com.integral.admin.jsp.broker.PriceMakingStreamForm</formType>
</definition>

<!-- Closing tag -->

</definitionMap>
