<%@page import="com.integral.finance.trade.configuration.TradeConfigurationFactory"%>
<%@ page import="java.util.Iterator"%> <%@ taglib uri="/tag/idc-html.tld" prefix="idcHtml" %>
<%@ taglib uri="/tag/idc-bean.tld" prefix="idcBean" %>
<%@ taglib uri="/tag/struts-html.tld" prefix="html" %>
<%@ taglib uri="/tag/idc-logic.tld" prefix="idcLogic" %>
<%@ page import="com.integral.jsp.framework.ObjectActionForm" %>
<%@ page import="com.integral.businessCenter.BusinessCenterC" %>
<%@ page import="com.integral.finance.country.CountryFactory" %>
<%@ page import="org.apache.struts.action.*" %>
<%@ page import="java.util.*" %>
<%@ page import="com.integral.finance.counterparty.LegalEntity"%>
<%@ page import="com.integral.admin.utils.organization.*,com.integral.user.*"%>
<%@ page import="com.integral.provider.*"%>
<%@page import="com.integral.system.server.*"%>
<%@page import="com.integral.admin.utils.*"%>
<%@page import="com.integral.persistence.EntityFactory"%>
<%@page import="com.integral.admin.utils.organization.*"%>
<%@page import="com.integral.mifid.MiFIDMBeanC"%>
<%@page import="com.integral.is.ISCommonConstants"%>
<%@ page import="com.integral.finance.country.CountryFactory,
                 java.util.Collections,java.util.Collection,java.util.ArrayList,java.util.HashMap,java.util.Iterator,java.util.List"%>
<%@page import="com.integral.subscription.rules.SubscriptionRules"%>
<script src="/admin/integral/admin/js/jquery/jquery.min.js" type="text/javascript"></script>
<%!
    HashMap mapForQuoteLogics=new HashMap();
    {
        mapForQuoteLogics.put(ProviderOrgFunction.SAME_QUOTE,"Organization.Same.Quote");
        mapForQuoteLogics.put(ProviderOrgFunction.NEW_QUOTE_SAME_PRICE,"Organization.New.Quote.Same.Price");
        mapForQuoteLogics.put(ProviderOrgFunction.NEW_QUOTE_BETTER_PRICE,"Organization.New.Quote.Better.Price");
    }
%>
<input type="hidden" name="workflowMessage.objectChanged" value="false">
<idcHtml:hidden name="ObjectActionForm" property="objectForm.status" />
<idcHtml:hidden name="ObjectActionForm" property="objectForm.encryptedId" />
<idcBean:setProperty name="ObjectActionForm" property="alias.inputForm" value="objectForm.workflowStateMap.changeSet.changes"/>
<idcHtml:hidden name="ObjectActionForm" property="alias.inputForm" value="objectForm.workflowStateMap.changeSet.changes"/>
<idcHtml:hidden name="ObjectActionForm" property="inputForm.objectType" value="com.integral.user.Organization" valueToBean="true"/>
<idcBean:define id="organization" name="ObjectActionForm" property="objectForm.object" type="com.integral.user.Organization" />
<idcBean:setProperty name="ObjectActionForm" property="inputForm.original" valueObj="<%=organization%>"/>
<idcBean:define id="loginUser" name="ObjectActionForm" property="user" type="com.integral.user.UserC"/>
<idcBean:setProperty name="ObjectActionForm" property="alias.contactForm" value="objectForm"/>
<idcHtml:hidden name="ObjectActionForm" property="alias.contactForm" value="objectForm"/>
<idcHtml:hidden name="ObjectActionForm" property="contactForm.atomic" value="false"/>
<%
	String encID = organization.getEncryptedObjectID();
 	int numMasked = organization.getMaskedLP().size();
 	boolean isExternalProvider = false, isECN = false, isBroker = false;
 	boolean isMaskedLP = organization.isMasked();
 	if(isMaskedLP){
 		Organization realLP = organization.getRealLP();
 		if(realLP != null){
 			isExternalProvider = realLP.isExternalProvider();
 			isECN = realLP.isECN();
 			isBroker = realLP.isBroker();
 		}
 	}else{
 		isExternalProvider = organization.isExternalProvider();
 		isECN = organization.isECN();
 		isBroker = organization.isBroker();
 	}
 	boolean isLiquidityGroup = organization.isLiquidityGroup();
 	String isLGDisplay = isLiquidityGroup ? "" : "none";
    HashMap mapForSubscriptionRuleDesc=new HashMap();
    {
    	mapForSubscriptionRuleDesc.put(SubscriptionRules.MULTIPLE_STREAM_MULTIPLE_PATH, "Subscription.Rules.MULTIPLE_STREAM_MULTIPLE_PATH.Desc");
    	mapForSubscriptionRuleDesc.put(SubscriptionRules.SINGLE_STREAM,"Subscription.Rules.SINGLE_STREAM.Desc");
    	mapForSubscriptionRuleDesc.put(SubscriptionRules.MULTIPLE_STREAM_SINGLE_PATH,"Subscription.Rules.MULTIPLE_STREAM_SINGLE_PATH.Desc");
    	mapForSubscriptionRuleDesc.put(SubscriptionRules.NONE,"Subscription.Rules.NONE");
    }

    boolean manageCcyPairConfig = organization.getBrokerOrganization()!=null ? organization.getBrokerOrganization().isManageCcyPairConfigEnabled() : organization.isManageCcyPairConfigEnabled();
%>
<script>
idcWindow.registerWindowObject(objectChangeListener);
objectChangeListener.register("inputForm.longName");
function checkBoxes (chkBox)
{
      var theCheckBox="chkBox"+chkBox.id;
      var refChk = document.getElementById(chkBox);
      if( null== refChk ) refChk = document.getElementsByName(chkBox)[0];
      if(refChk.value == "")
    	  refChk.value='false';
      if(refChk.value == "false") {
    	  refChk.value='true';
      }else{
    	  refChk.value='false';
      }
}
function updateExternalAuth(chkBox , objectID)
{
    if(chkBox.checked == false)
         document.getElementById(objectID).value='false';
    else
         document.getElementById(objectID).value='true';
}

var FMALP_c = null;
$(document).ready(
        function()
        {
        	$("#maskedLPProv").click(function(){
        		$(this).val($(this).prop("checked"));
        	});
        	$("#PreTadeAllocation").click(function(){
        		$(this).val($(this).prop("checked"));
        	});
            $("#PostTradeAllocation").click(function(){
                $(this).val($(this).prop("checked"));
            });
        	$("#maskedLPCcy").click(function(){
        		$(this).val($(this).prop("checked"));
        	});
        	$("#manageCcyPairConfig").click(function(){
        		$(this).val($(this).prop("checked"));
        	});

        	$("#mdfServerEnabled").click(function(){
        		$(this).val($(this).prop("checked"));
        	});

        	$("#superBankEnabled").click(function(){
        		$(this).val($(this).prop("checked"));
        	});

            $("#masked").click(function()
            		{
                         $(this).val($(this).prop("checked"));
                         if($(this).val() == "true")
                         {
                             $("#realLP").prop("disabled","");
                         }
                         else
                         {
                        	 $("#realLP").prop("disabled","disabled");
                         }
            		});
            $("#cancelOrdersOnUserInactivation").click(function()
                    {
                         $(this).val($(this).prop("checked"));
                         changeButton();
                    });

            $("#prorataForwardSpecialClient").click(function()
                    {
                         $(this).val($(this).prop("checked"));
                         changeButton();
                    });

	            $("#Save").click(function()
	            {
	            	  $("#event").val('Save');
	            	  if(validateOrgSave()==true)
	            	  {
	            		    perform();
	            	  }
	            });
	            $("#DefaultDealingUser").change(function(){changeButton();});
	            $("#DefaultLE").change(function(){changeButton();});
	            initialize();
        });

function initialize()
{
	if($("#masked").val() == "true")
    {
        $("#realLP").prop("disabled","");
    }
    else
    {
        $("#realLP").prop("disabled","disabled");
    }
    if($.trim($("#DefaultLE").val()) =="None" || <%=(numMasked>0)%>)
    {
        $("#masked").prop("disabled","disabled");
    }
}
function validateOrgSave()
{
    if($("#masked").prop("checked")&& $.trim($("#realLP").val())=="")
    {
    	alert("Please Select a Organization to mask the LP - '" + $("#longName").val()+"'");
        return false;
    }
    return true;
}
</script>
<table width="100%" cellpadding="0" cellspacing="0">
	<idcHtml:errors/>
</table>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
	<tr>
		<td class="stl2" nowrap><idcBean:message key="Main.Details" />
<%
	boolean isSubportalView = loginUser.hasPermission("SubPortalViewPerm")&&loginUser.getOrganization().isBroker();
	if(organization.getBrokerOrganization()!=null&&isSubportalView)
	{
%>
		&nbsp;&nbsp;<a class="lh" name="refLink" href="javascript:helpPopup('Cpty/Subportal/Details/CptyEditDetails.html#_top','Admin Portal Help')">
			<img src="/admin/theme/images/help.png" alt='<idcBean:message key="utility.help"/>' border="0"></img>
		</a>
<%
	}
%>
		</td>
		<td class="stl3" nowrap>&nbsp;</td>
		<td class="stl4" nowrap>&nbsp;</td>
	</tr>
</table>
<%
    boolean isSysAdmin = UserUtil.isSysAdmin(UserUtil.getSessionUser());
	boolean hasLiquidityMasterControl = loginUser.hasPermission("LiquidityMasterControl");

	ActionErrors errors = (ActionErrors) request.getAttribute (Action.ERROR_KEY );
	if ( errors != null )
	{
		Iterator errorsIter = errors.get();
		while (errorsIter.hasNext())
		{
			ActionError error = (ActionError)errorsIter.next();
		}
   	}
    boolean isMainOrg = loginUser.getOrganization().getShortName().equals("MAIN");

%>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
	<tr>
		<td class="f3" colspan="2" nowrap>Fields marked with an asterisk (<span class="fr">*</span>) are required</td>
	</tr>
	<tr>
		<td class="f3" nowrap><idcBean:message key="ShortName" /></td>
		<td class="f" nowrap><idcHtml:text name="ObjectActionForm" property="objectForm.shortName" widgetStyle="read"/></td>
	</tr>
	<tr>
		<td class="f3" nowrap><span class="fr">*</span>&nbsp;<idcBean:message key="LongName" /></td>
		<td class="f" nowrap><idcHtml:text name="ObjectActionForm" styleId="longName" property="inputForm.longName" size="50" maxlength="50" styleClass="ft"/></td>
	</tr>
    <tr>
        <td class="f3" nowrap>&nbsp;<idcBean:message key="Description" /></td>
        <td class="f" nowrap><idcHtml:textarea name="ObjectActionForm" property="objectForm.description" cols="50" rows="5" styleClass="ft" /></td>
    </tr>
    <tr>
        <td class="f3" nowrap>&nbsp;OCX Participant Type</td>
        <td class="f" nowrap><%=(organization.getOcxParticipantType() == null ? "" : organization.getOcxParticipantType())%></td>
    </tr>
	<tr>
        <td class="f3" nowrap>&nbsp;<idcBean:message key="Address1" /></td>
        <td class="f" nowrap>
        <idcHtml:text name="ObjectActionForm" property="contactForm.addressLine1" size="50" maxlength="50" styleClass="ft"/></td>
    </tr>
    <tr>
        <td class="f3" nowrap>&nbsp;<idcBean:message key="Address2" /></td>
        <td class="f" nowrap>
        <idcHtml:text name="ObjectActionForm" property="contactForm.addressLine2" size="50" maxlength="50" styleClass="ft"/></td>
    </tr>
    <tr>
        <td class="f3" nowrap>&nbsp;<idcBean:message key="Address3" /></td>
        <td class="f" nowrap>
        <idcHtml:text name="ObjectActionForm" property="contactForm.addressLine3" size="50" maxlength="50" styleClass="ft"/></td>
    </tr>
    <tr>
        <td class="f3" nowrap>&nbsp;<idcBean:message key="Address4" /></td>
        <td class="f" nowrap>
        <idcHtml:text name="ObjectActionForm" property="contactForm.addressLine4" size="50" maxlength="50" styleClass="ft"/></td>
    </tr>
    <tr>
        <td class="f3" nowrap>&nbsp;<idcBean:message key="PostalCode" /></td>
        <td class="f" nowrap>
        <idcHtml:text name="ObjectActionForm" property="contactForm.postalCode" size="50" maxlength="50" styleClass="ft"/></td>
    </tr>
    <tr>
        <td class="f3" nowrap><idcBean:message key="Country" /> </td>
        <td class="f"  nowrap>
            <%
                java.util.Map countryMap = com.integral.admin.utils.GenericAdminUtil.getCountryMap();
            %>
            <idcHtml:select name="ObjectActionForm" property="contactForm.country">
                <idcHtml:option value=""><idcBean:message key="SelectOption" /></idcHtml:option>
                <idcLogic:iterate id="country" collection="<%=  com.integral.admin.utils.GenericAdminUtil.getCountryMap().keySet()%>">
                    <idcHtml:option value="<%=(String) countryMap.get((String) country)%>"><%=(String) country%></idcHtml:option>
                </idcLogic:iterate>
            </idcHtml:select>
        </td>
    </tr>

    <tr>
		<td CLASS="f3" nowrap>&nbsp;<idcBean:message key="OrganizationGroup" /></td>
		<td class="f" nowrap><idcHtml:text name="ObjectActionForm" property="objectForm.organizationGroup" size="50" maxlength="50" styleClass="ft"/></td>
	</tr>
<%
    if(isMainOrg)
    {
%>
    <tr>
		<td CLASS="f3" nowrap>&nbsp;<idcBean:message key="GroupNum" /></td>
		<td class="f" nowrap><idcHtml:text name="ObjectActionForm" property="objectForm.groupNumber" size="25" maxlength="20" styleClass="ft" readonly="true"/></td>
	</tr>
    <tr>
		<td CLASS="f3" nowrap>&nbsp;<idcBean:message key="SecGroupNum" /></td>
		<td class="f" nowrap><idcHtml:text name="ObjectActionForm" property="objectForm.secondaryGroupNumber" size="25" maxlength="20" styleClass="ft" readonly="true"/></td>
	</tr>
<%
    }
%>
    <tr>
		<td CLASS="f3" nowrap>&nbsp;<idcBean:message key="Region" /></td>
		<td class="f" nowrap>
        <%
                 Collection regionCodeColl = new ArrayList();
                 regionCodeColl.add("1");
                 regionCodeColl.add("2");
                 regionCodeColl.add("3");
                 ArrayList regionColl = new ArrayList(regionCodeColl.size());
                 HashMap regionMap = new HashMap(regionCodeColl.size());
                 Iterator regionCodeIter =  regionCodeColl.iterator();
                 while(regionCodeIter.hasNext()) {
                       String regionCode = (String) regionCodeIter.next();
                      if(regionCode.equals("1")) {
                          regionMap.put("Asia-Pacific","1");
                          regionColl.add("Asia-Pacific");
                      }
                     if(regionCode.equals("2")) {
                          regionMap.put("Europe", "2");
                          regionColl.add("Europe");
                      }
                     if(regionCode.equals("3")) {
                          regionMap.put("America","3");
                          regionColl.add("America");
                      }

                 }
                 Collections.sort((List) regionColl);
        %>
            <idcHtml:select name="ObjectActionForm" property="objectForm.region">
				<idcHtml:option value=""><idcBean:message key="SelectOption" /></idcHtml:option>
				<idcLogic:iterate id="region" collection="<%= regionColl %>">
					<idcHtml:option value="<%=(String) regionMap.get((String) region)%>"><%=(String) region%></idcHtml:option>
				</idcLogic:iterate>
			</idcHtml:select>
        </td>
	</tr>
</table>

<%

if (isMainOrg) { // show Subscription rules only to main users
	// Show Subscription rules only if the org is ECN/Broker/ExternalProvider and is quoting to
	// atleast one org
	if ((isECN || isBroker || isExternalProvider) && (OrganizationUtil.isLiquidityProvider(organization))) {
%>
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
	<tr>
		<td class="stl2" nowrap><idcBean:message key="Subscription.Rules"/></td>
		<td class="stl3" nowrap>&nbsp;</td>
		<td class="stl4" nowrap>&nbsp;</td>
	</tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
	<tr>
		<%
			if (isMaskedLP) {
		%>
		<td class="f3" width="25%" nowrap>&nbsp;<idcBean:message key="Override"/></td>
		<td class="f" nowrap><input type="checkbox" id="maskedLPOverride"
			name="workflowMessage.maskedLPOverride" value="true"
			<%=organization.getProviderOrgFunction()
							.isOverrideSetAtMaskedLP() ? "checked" : ""%>></td>
		<%
			} else {
		%>
		<td class="f3" width="25%" nowrap>&nbsp;<idcBean:message key="Subscription.Rules"/></td>
		<td class="f" nowrap><select
			name="workflowMessage.SubscriptionRule" id="SubscriptionRule"
			class="ft" onchange="changeButton();">
				<%
					SubscriptionRules selectedRule = organization
									.getProviderOrgFunction().getSubscriptionRule();
							for (SubscriptionRules rule : SubscriptionRules.values()) {
				%>
				<option value="<%=rule.toString()%>"
					<%=(selectedRule == rule) ? "selected" : ""%>><idcBean:message key="<%=(String)mapForSubscriptionRuleDesc.get(rule)%>"/></option>
				<%
					}
				%>
		</select></td>
		<%
			}
		%>

	</tr>
</table>
<%
	} // end if
} // end sysAdmin user
%>

<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap><idcBean:message key="Regulatory.Details" /></td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
    <tr>
        <td class="f3" nowrap>&nbsp;<idcBean:message key="label.LEI.Details" /></td>
        <td class="f3" nowrap>
        <idcHtml:text name="ObjectActionForm" property="objectForm.LEI" size="20" maxlength="20" styleClass="ft"/>
        </td>
    </tr>
    <% if(MiFIDMBeanC.getInstance().isMiFIDEnabled()){ %>
        <tr>
            <td class="f3" nowrap>&nbsp;<idcBean:message key="label.MICCode" /></td>
            <td class="f3" nowrap>
                <idcHtml:text name="ObjectActionForm" property="objectForm.MICCode" size="50" maxlength="50" styleClass="ft" disabled="<%=isSysAdmin? false:true %>"/>
            </td>
        </tr>
        <tr>
            <td class="f3" nowrap>&nbsp;<idcBean:message key="label.ShortCodeSource" /></td>
            <td class="f3" nowrap>
                <idcHtml:text name="ObjectActionForm" property="objectForm.shortCodeSource" size="50" maxlength="50" styleClass="ft" disabled="<%=isSysAdmin? false:true %>"/>
            </td>
        </tr>
    <%}%>
</table>

<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap><idcBean:message key="Organization.Trading.Allowed" /></td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
    <tr>
        <td class="f3" nowrap width="25%"><idcBean:message key="Organization.Trading.Allowed" /></td>
        <td class="f" nowrap>
         <input type="checkbox" id="TradingAllowed" name="TradingAllowed" <%=organization.isTradingAllowed()?"checked":""%> disabled>
         <%if(hasLiquidityMasterControl){%>
         &nbsp;Click <a href="/admin/Forward.do?forwardURI=Taker.Liquidity.Rules&objectForm.objectType=com.integral.user.OrganizationC&objectForm.encryptedId=<%=encID%>&encryptedObjectId=<%=encID%>">here</a> to modify this field.
         <%}%>
        </td>
    </tr>
</table>
<%
if(organization.getBrokerOrganization()!=null&&isSubportalView)
{
%>
<input type="hidden" name="workflowMessage.brokerForOrg" value="<%=organization.getBrokerOrganization().getShortName()%>">
<%
}
%>
<%
if(UserUtil.isSysAdmin(UserUtil.getSessionUser()))
{
    List<VirtualServerC> colVirtual = (List<VirtualServerC>)GenericAdminUtil.getAllEntities(com.integral.system.server.VirtualServerC.class,'A');
    Collections.sort(colVirtual,EntityFactory.newShortNameComparator());
%>
<div class="space"></div>

<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap><idcBean:message key="VirtualServer.Heading"/></td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
    <tr>
        <td class="f3" width="25%" nowrap><idcBean:message key="VirtualServer.list"/></td>
        <td class="f" nowrap>
         <select name="workflowMessage.VirtualServer" id="VirtualServer" class="ft" onchange="changeButton();" >
         <%
         	VirtualServer vs = organization.getVirtualServer();
	         if( null == vs || isExternalProvider)
	         {
	         %>
	         <option value=""><idcBean:message key="SelectOption1"/></option>
	         <%
	         }
            long selectedVSID = 0L;
            String vsId = "";
            if( null!= vs )
            {
                selectedVSID = vs.getObjectId();
                vsId = vs.getEncryptedObjectID();
            }
            for(VirtualServerC serverC:colVirtual)
            {
                if(serverC.getVirtualServerType().getShortName().equals(VirtualServerType.BrokerAdaptor) || serverC.getVirtualServerType().getShortName().equals(VirtualServerType.OrderAdaptor)|| serverC.getVirtualServerType().getShortName().equals(VirtualServerType.OMSServer)
                || serverC.getVirtualServerType().getShortName().equals(VirtualServerType.EMSServer) || serverC.getVirtualServerType().getShortName().equals(VirtualServerType.UIGatewayServer))
                {
         %>
            <option value="<%=serverC.getEncryptedObjectId()%>" <%=(selectedVSID==serverC.getObjectId())?"selected":""%>><%=serverC.getShortName()%></option>
         <%
                }
            }
         %>
         </select>
        </td>
    </tr>
</table>
<input type="hidden" name="workflowMessage.PreviousVirtualServer" value="<%=vsId%>">
<%
}
%>
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap><idcBean:message key="organization.default.DealingUser"/>
<%
	if(organization.getBrokerOrganization()!=null&&isSubportalView)
	{
%>
		&nbsp;&nbsp;<a class="lh" name="refLink" href="javascript:helpPopup('Cpty/Subportal/Details/CptyDefaultUserLE.html#_top','Admin Portal Help')">
			<img src="/admin/theme/images/help.png" alt='<idcBean:message key="utility.help"/>' border="0"></img>
		</a>
<%
	}
%>
		</td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
    <tr>
        <td class="f3" width="25%" nowrap><idcBean:message key="organization.default.DealingUser"/></td>
        <td class="f" nowrap>
         <select name="workflowMessage.DefaultDealingUser" id="DefaultDealingUser" class="ft">
            <%
            User orgDefaultUser = OrganizationUtil.getDefaultDealingUser(organization);

             List<User> users=new ArrayList<User>(organization.getUsers());
             Collections.sort(users,EntityFactory.newShortNameComparator());
             if(users.size()==0 || null == orgDefaultUser){
            %>
            <option value=""><idcBean:message key="SelectOption1"/></option>
          <%
             }
             AdminWebServicesMBean adminWebServicesMBean = AdminWebServicesMBeanC.getInstance();
             boolean showOrgManageCcyPairConfig = adminWebServicesMBean.isShowOrgManageCcyPairConfig(organization.getShortName());
             String[] userToken = isSysAdmin?new String[0]:adminWebServicesMBean.getHiddenUserTokens(organization);
             boolean ignore = false;

             for(User user:users)
             {
                ignore = false;
                for(String token : userToken)
                {
                    String userNameLowerCase = user.getShortName().toLowerCase();
                    if( userNameLowerCase.contains(token.toLowerCase()))
                    {
                        ignore = true;break;
                    }
                 }
                 if(!ignore)
                 {
	                 boolean isDefUser = ((null ==orgDefaultUser )? false:user.isSameAs(orgDefaultUser));
	                 if(user.isActive() || isDefUser )
	                 {
	            %>
	                   <option value="<%=user.getShortName()%>" <%=isDefUser?"selected":"" %>>
	                        <%=user.getShortName()%>
	                   </option>
	            <%
	                 }
                 }
             }
             %>
             </select>
        </td>
    </tr>
</table>
<div class="space"></div>

<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap><idcBean:message key="Admin.default.legalentity"/>
<%
	if(organization.getBrokerOrganization()!=null&&isSubportalView)
	{
%>
		&nbsp;&nbsp;<a class="lh" name="refLink" href="javascript:helpPopup('Cpty/Subportal/Details/CptyDefaultUserLE.html#_top','Admin Portal Help')">
			<img src="/admin/theme/images/help.png" alt='<idcBean:message key="utility.help"/>' border="0"></img>
		</a>
<%
	}
%>
		</td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
    <tr>
        <td class="f3" width="25%" nowrap><idcBean:message key="Admin.default.legalentity"/></td>
        <td class="f" nowrap>
         <select name="workflowMessage.DefaultLE" id="DefaultLE" class="ft">
         <%
         LegalEntity defaultLE = organization.getDefaultDealingEntity();

         List<LegalEntity> legalentityCol=new ArrayList<LegalEntity>(organization.getLegalEntities());
         if( legalentityCol.size()==0 || null == defaultLE ){ %>
         <option value="None"><idcBean:message key="SelectOption1"/></option>
          <%
         }
             Collections.sort(legalentityCol,EntityFactory.newShortNameComparator());
             for(LegalEntity le:legalentityCol)
             {
                 if(le.isActive() )
                 {
           %>
                <option value="<%=le.getShortName()%>" <%=((null != defaultLE && defaultLE.isSameAs(le)))?"selected":"" %>><%=le.getShortName()%>
         <%}}%>
        </td>
    </tr>
</table>
<%
boolean isNotMAIN = !(organization.getShortName().equals("MAIN"));
if((organization.getOrganizationFunction("PROVIDER")!=null) && isNotMAIN )
{
%>
  <jsp:include page="../user/OrgSupportedChannels.jsp" />
<%
}
%>
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
	<tr>
		<td class="stl2" nowrap>Client Channel</td>
		<td class="stl3" nowrap>&nbsp;</td>
		<td class="stl4" nowrap>&nbsp;</td>
		<td class="stl8" nowrap>&nbsp;</td>
	</tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
</tr>
    <%
            java.util.Collection<com.integral.persistence.ExternalSystem> colExternal=com.integral.admin.utils.externalsystem.ExternalSystemIDUtil.getClientChannel();
    %>
    <tr>
        <td class="f3v" nowrap width="25%">Client Channel</td>
        <td  class="f3" nowrap>
            <select id="clientchannel" class="ft" name="workflowMessage.ClientChannel">
                <option value="None"><idcBean:message key="SelectOption1"/></option>
                <% for(com.integral.persistence.ExternalSystem external:colExternal)
                {
                %>
                <option value="<%=external.getShortName()%>" <%=organization.getClientChannel()!=null&&organization.getClientChannel().getShortName().equals(external.getShortName())?"selected":""%>><%=external.getLongName()%></option>
                <%
                    }
                %>
            </select>

        </td>
    </tr>
</table>
<%
if( TradeConfigurationFactory.getTradeConfigurationMBean().isBatchTradeSupported(organization))
{
%>
<idcBean:define id="isPreTadeAllocationEnabled" name="organization" property="preTradeAllocation" type="Boolean" />
<idcBean:define id="isPostTradeAllocationEnabled" name="organization" property="postTradeAllocation" type="Boolean" />
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
	<tr>
		<td class="stl2" nowrap><idcBean:message key="PreTradeAllocation" /></td>
		<td class="stl3" nowrap>&nbsp;</td>
		<td class="stl4" nowrap>&nbsp;</td>
		<td class="stl8" nowrap>&nbsp;</td>
	</tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
<tr>
		<td class="f3v" nowrap width="25%"><idcBean:message key="PreTradeAllocation" /></td>
        <td class="f3" nowrap>
            <input type="checkbox" id="PreTadeAllocation" name="workflowMessage.PreTadeAllocation" <%=isPreTadeAllocationEnabled.booleanValue()?"checked":""%> value="<%=isPreTadeAllocationEnabled.booleanValue()%>"/>
        </td>
</tr>
</table>
<%--
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap><idcBean:message key="PostTradeAllocation" /></td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
        <td class="stl8" nowrap>&nbsp;</td>
    </tr>
</table>

<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
    <tr>
        <td class="f3v" nowrap width="25%"><idcBean:message key="PostTradeAllocation" /></td>
        <td class="f3" nowrap>
            <input type="checkbox" id="PostTradeAllocation" name="workflowMessage.PostTradeAllocation" <%=isPostTradeAllocationEnabled.booleanValue()?"checked":""%> value="<%=isPostTradeAllocationEnabled.booleanValue()%>"/>
        </td>
    </tr>
</table>
 --%>
<%
}
if(loginUser.hasPermission("SetBroker") && isNotMAIN )
{
    Organization brokerOrg =organization.getBrokerOrganization();
    Collection organizationRelationships=((Organization)organization).getOrganizationRelationships();
    java.util.List<String> listOrgs = new ArrayList<String>();
    Iterator organizationRelationshipsIterator=organizationRelationships.iterator();
    while(organizationRelationshipsIterator.hasNext())
    {
      OrganizationRelationship orgRel=(OrganizationRelationship)organizationRelationshipsIterator.next();
      Organization relatedOrg=orgRel.getRelatedOrganization();
      String relatedOrgShortName=relatedOrg.getShortName() ;
      if( relatedOrg.isActive())
      {
          if ((relatedOrgShortName != null) && (relatedOrgShortName.length() > 0))
          {
              if(!(listOrgs.contains(relatedOrgShortName)))
              {

                    listOrgs.add(relatedOrgShortName);
             }
          }
      }
    }
   Collections.sort(listOrgs);
%>
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap>Broker</td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
        <td class="stl8" nowrap>&nbsp;</td>
    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
<tr>
    <td class="f3v" nowrap width="25%">Broker for Customer</td>
    <td class="f3" nowrap>
        <select id="broker" class="ft" name="workflowMessage.brokerForOrg">
            <option value="" <%=brokerOrg==null?"selected":""%> ><idcBean:message key="SelectOption1" /></option>
            <%
              for(Iterator<String> itrOrg = listOrgs.iterator() ; itrOrg.hasNext();)
              {
                  String brokerForOrganization = itrOrg.next();
                  if("FXI".equals(brokerForOrganization))
                  {
                      continue;
                  }

                  if(brokerOrg !=null && brokerOrg.getShortName().equals(brokerForOrganization))
                  {
                    %>
                    <option value="<%=brokerForOrganization%>" selected="selected"><%=brokerForOrganization%> </option>
                    <%
                    }
                    else
                    {
                    %>
                    <option value="<%=brokerForOrganization%>" ><%=brokerForOrganization%> </option>
                    <%
                    }
                }
            %>
        </select>
     </td>
 </tr>
</table>

<%
if(organization.getPrimeBrokerOrganizationForMV()!=null&&isSubportalView)
{
%>
<input type="hidden" name="workflowMessage.primeBrokerForOrg" value="<%=organization.getPrimeBrokerOrganizationForMV().getShortName()%>">
<%
}
%>
<%
	Organization primeBrokerOrg = organization
				.getPrimeBrokerOrganizationForMV();
		Collection organizationPBRelationships = ((Organization) organization)
				.getOrganizationRelationships();
		java.util.List<String> pbListOrgs = new ArrayList<String>();
		Iterator organizationPBRelationshipsIterator = organizationPBRelationships
				.iterator();
		while (organizationPBRelationshipsIterator.hasNext()) {
			OrganizationRelationship orgRel = (OrganizationRelationship) organizationPBRelationshipsIterator
					.next();
			Organization relatedOrg = orgRel.getRelatedOrganization();
			if (relatedOrg.isActive() && relatedOrg.isPrimeBroker()) {
				String relatedOrgShortName = relatedOrg.getShortName();
				if ((relatedOrgShortName != null)
						&& (relatedOrgShortName.length() > 0)) {
					if (!(pbListOrgs.contains(relatedOrgShortName))) {

						pbListOrgs.add(relatedOrgShortName);
					}
				}
			}
		}
		Collections.sort(pbListOrgs);
%>
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap>Prime Broker for Matching Venue</td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
        <td class="stl8" nowrap>&nbsp;</td>
    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
<tr>
    <td class="f3v" nowrap width="25%">Prime Broker for Customer</td>
    <td class="f3" nowrap>
        <select id="primeBroker" class="ft" name="workflowMessage.primeBrokerForOrg">
            <option value="" <%=primeBrokerOrg==null?"selected":""%> ><idcBean:message key="SelectOption1" /></option>
            <%
              for(Iterator<String> itrOrg = pbListOrgs.iterator() ; itrOrg.hasNext();)
              {
                  String pbForOrganization = itrOrg.next();
                  if("FXI".equals(pbForOrganization))
                  {
                      continue;
                  }

                  if(primeBrokerOrg !=null && primeBrokerOrg.getShortName().equals(pbForOrganization))
                  {
                    %>
                    <option value="<%=pbForOrganization%>" selected="selected"><%=pbForOrganization%> </option>
                    <%
                    }
                    else
                    {
                    %>
                    <option value="<%=pbForOrganization%>" ><%=pbForOrganization%> </option>
                    <%
                    }
                }
            %>
        </select>
     </td>
 </tr>
 </table>

<%}
if(loginUser.hasPermission("ProviderTypeConfig") && isNotMAIN )
{
List<Organization> orgFI = (List<Organization>)OrganizationUtil.getAllFIOrganizations();
Collections.sort(orgFI,EntityFactory.newShortNameComparator());
%>
<div class="space"></div>

<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap><idcBean:message key="OrganizationType" /></td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
        <td class="stl8" nowrap>&nbsp;</td>
    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
<tr>
        <td class="f3v" nowrap width="25%"><idcBean:message key="OrganizationType" /></td>
        <td class="f3" nowrap>
          <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.externalProvider" />
            <idcHtml:checkbox name="ObjectActionForm" property="objectForm.externalProvider"  onclick="checkBoxes('objectForm.externalProvider')" disabled="<%=isLiquidityGroup?true:false%>"/>&nbsp;<idcBean:message key="ExternalProvider" />

        <br>

        <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.ECN" />
            <idcHtml:checkbox name="ObjectActionForm" property="objectForm.ECN"  onclick="checkBoxes('objectForm.ECN')" disabled="<%=isLiquidityGroup?true:false%>"/>&nbsp;<idcBean:message key="ECN" />
        <br>

           <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.broker" />
            <idcHtml:checkbox name="ObjectActionForm" property="objectForm.broker"  onclick="checkBoxes('objectForm.broker')" disabled="<%=isLiquidityGroup?true:false%>"/>&nbsp;<idcBean:message key="Broker" />

       <br>

          <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.primeBroker" />
            <idcHtml:checkbox name="ObjectActionForm" property="objectForm.primeBroker"  onclick="checkBoxes('objectForm.primeBroker')" disabled="<%=isLiquidityGroup?true:false%>"/>&nbsp;<idcBean:message key="PrimeBroker" />

        <br>
          <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.matrixOrg" />
           <idcHtml:checkbox name="ObjectActionForm" property="objectForm.matrixOrg"  onclick="checkBoxes('objectForm.matrixOrg')" disabled="<%=isLiquidityGroup?true:false%>"/>&nbsp;<idcBean:message key="Matrix" />

        <br>
          <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.clearingHouse" />
            <idcHtml:checkbox name="ObjectActionForm" property="objectForm.clearingHouse"  onclick="checkBoxes('objectForm.clearingHouse')" disabled="<%=isLiquidityGroup?true:false%>"/>&nbsp;<idcBean:message key="ClearingHouse" />

        <br>
          <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.SEF" />
            <idcHtml:checkbox name="ObjectActionForm" property="objectForm.SEF"  onclick="checkBoxes('objectForm.SEF')" disabled="<%=isLiquidityGroup?true:false%>"/>&nbsp;<idcBean:message key="SEF" />

        <% if(MiFIDMBeanC.getInstance().isMiFIDEnabled()){ %>
            <br>
              <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.miFID" />
                <idcHtml:checkbox name="ObjectActionForm" property="objectForm.miFID"  onclick="checkBoxes('objectForm.miFID')" disabled="<%=isLiquidityGroup?true:false%>"/>&nbsp;<idcBean:message key="MiFID" />

            <br>
              <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.MTFVenue" />
                <idcHtml:checkbox name="ObjectActionForm" property="objectForm.MTFVenue"  onclick="checkBoxes('objectForm.MTFVenue')" disabled="<%=isLiquidityGroup?true:false%>"/>&nbsp;<idcBean:message key="MTFVenue" />
        <%}%>
        <br>
          <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.SDR" />
            <idcHtml:checkbox name="ObjectActionForm" property="objectForm.SDR"  onclick="checkBoxes('objectForm.SDR')" disabled="<%=isLiquidityGroup?true:false%>"/>&nbsp;<idcBean:message key="SDR" />

        <br>
         <input type="checkbox" id="masked" name="workflowMessage.masked" name="masked" value="<%=organization.isMasked()?true:false%>" <%=isLiquidityGroup?"disabled":""%>" <%=organization.isMasked()?"checked":""%>>&nbsp;<idcBean:message key="organization.mask.LP.For" />
         <select id="realLP" name="workflowMessage.realLP" class="ft" disabled>
            <option value=""><idcBean:message key="SelectOption" /></option>
            <%
            Organization orgRealLP = organization.getRealLP();

            for(Organization org:orgFI)
            {
                String selected ="";
                if( (org.getObjectID() == organization.getObjectID())||org.isMasked()) continue;
                if( !ObjectUtils.isNull(orgRealLP) )
                {
                    if( organization.isMasked()&& orgRealLP.getObjectID() == org.getObjectID()) selected = "selected";
                }
            %>
             <option value="<%=org.getShortName()%>" <%=selected%>><%=org.getShortName()%></option>
            <%
            }
            %>
         </select>
       <!--    value=" < % = organization.isMasked()?true:false % >" < %=organization.isMasked()?"checked":""% >>&nbsp;<idcBean:message key="organization.mask.LP.For" /> -->
          <br>
         <div id="FMALP_div" >

          <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.FMALP" />
            <idcHtml:checkbox name="ObjectActionForm" property="objectForm.FMALP" styleId="objectForm_FMALP"  onclick="checkBoxes2('objectForm.FMALP')" disabled="<%=isLiquidityGroup?true:false%>"/>&nbsp;<idcBean:message key="FMALP" />

         </div>

          <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.FXIDirectSetup" />
            <idcHtml:checkbox name="ObjectActionForm" property="objectForm.FXIDirectSetup"  onclick="checkBoxes('objectForm.FXIDirectSetup')" disabled="<%=isLiquidityGroup?true:false%>"/>&nbsp;<idcBean:message key="FXIDirect" />


         <br>
            <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.makerVenue" />
            <idcHtml:checkbox name="ObjectActionForm" property="objectForm.makerVenue"  onclick="checkBoxes('objectForm.makerVenue')" disabled="<%=isLiquidityGroup?true:false%>"/>&nbsp;<idcBean:message key="MakerVenue" />
         <br>
            <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.organizationTypeBridge" />
            <idcHtml:checkbox name="ObjectActionForm" property="objectForm.organizationTypeBridge"  onclick="checkBoxes('objectForm.organizationTypeBridge')" disabled="<%=isLiquidityGroup?true:false%>"/>&nbsp;<idcBean:message key="OrganizationTypeBridge" />

         <br>
             <input type="checkbox" id="superBankEnabled" name="workflowMessage.superBankEnabled" value="<%=organization.isSuperBank()?true:false%>" <%=isLiquidityGroup?"disabled":""%> <%=organization.isSuperBank()?"checked":""%>>&nbsp;<idcBean:message key="SuperBank" />
         <br>
            <div style="display: <%=isLGDisplay%>">
             <input type="checkbox" id="lgEnabled" name="workflowMessage.lgEnabled" value="<%=isLiquidityGroup?true:false%>" <%=isLiquidityGroup?"disabled":""%>>&nbsp;<idcBean:message key="LiquidityGroup" />
            </div>

         <script>

         function checkBoxes2 (chkBox)
         {


        	 var theCheckBox="chkBox"+chkBox.id;
               var refChk = document.getElementById(chkBox);
               if( null== refChk ) refChk = document.getElementsByName(chkBox)[0];
               if(refChk.value == "")
             	  refChk.value='false';
               if(refChk.value == "false") {
             	  refChk.value='true';
               }else{
             	  refChk.value='false';
               }
         }
         </script>

        </td>
</tr>

</table>
<%
}
if (isNotMAIN && loginUser.getOrganization().isMiFID() && MiFIDMBeanC.getInstance().isMiFIDEnabled()){
%>
    <div class="space"></div>
    <table width="100%" cellpadding="0" cellspacing="0" class="outl">
        <tr>
            <td class="stl2" nowrap><idcBean:message key="OrganizationType" /></td>
            <td class="stl3" nowrap>&nbsp;</td>
            <td class="stl4" nowrap>&nbsp;</td>
            <td class="stl8" nowrap>&nbsp;</td>
        </tr>
    </table>
    <table width="100%" cellpadding="0" cellspacing="1" class="outl2">
    <tr>
            <td class="f3v" nowrap width="25%"><idcBean:message key="OrganizationType" /></td>
            <td class="f3" nowrap>
                <br>
                  <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.miFID" />
                    <idcHtml:checkbox name="ObjectActionForm" property="objectForm.miFID"  onclick="checkBoxes('objectForm.miFID')" />&nbsp;<idcBean:message key="MiFID" />
            </td>
    </tr>
    </table>
<%
}
if( organization.getRealLP() != null && isSysAdmin )
{
%>
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap>Mask LP Provisioning</td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
        <td class="stl8" nowrap>&nbsp;</td>
    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
<tr>
    <td class="f3" nowrap width="25%"><idcBean:message key="Organization.MaskLP.PriceProvisioning" /></td>
    <td class="f" nowrap>
    	<input type="checkbox" id="maskedLPProv" name="workflowMessage.maskedLPPro" name="maskedLPPro" value="<%=organization.isMaskLPProvisioning()?true:false%>" <%=organization.isMaskLPProvisioning()?"checked":""%>>
    </td>

</tr>
<tr>
    <td class="f3" nowrap width="25%"><idcBean:message key="Organization.MaskLP.CurrencyPairs" /></td>
    <td class="f" nowrap>
    	<input type="checkbox" id="maskedLPCcy" name="workflowMessage.maskedLPCcy" name="maskedLPCcy" value="<%=organization.isMaskLPCurrencyPairs()?true:false%>" <%=organization.isMaskLPCurrencyPairs()?"checked":""%>>
    </td>
</tr>
</table>
<% }

if (showOrgManageCcyPairConfig) { %>
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap>Manage CurrencyPair Configuration</td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
        <td class="stl8" nowrap>&nbsp;</td>
    </tr>
</table>

<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
<tr>
    <td class="f3" nowrap width="25%"><idcBean:message key="Organization.Manage.CurrencyPair.Config" /></td>
    <td class="f" nowrap>
    	<input type="checkbox" id="manageCcyPairConfig" name="workflowMessage.manageCcyPairConfig" name="manageCcyPairConfig" value="<%=manageCcyPairConfig?true:false%>" <%=manageCcyPairConfig?"checked":""%> <%=organization.getBrokerOrganization() != null?"disabled":""%>>
    </td>
</tr>
</table>
<% } %>
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap>Netting Preferences</td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
    </tr>
</table>

<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
    <tr>
        <td class="f3" nowrap width="25%">Group by Legal Entity</td>
        <td class="f" nowrap>
            <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.nettingGroupByLE" />
            <idcHtml:checkbox name="ObjectActionForm" property="objectForm.nettingGroupByLE"  onclick="checkBoxes('objectForm.nettingGroupByLE')" />&nbsp;
         </td>
    </tr>
    <tr>
        <td class="f3" nowrap width="25%">Group by Trading Party</td>
        <td class="f" nowrap>
            <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.nettingGroupByTP" />
            <idcHtml:checkbox name="ObjectActionForm" property="objectForm.nettingGroupByTP"  onclick="checkBoxes('objectForm.nettingGroupByTP')" />&nbsp;
        </td>
    </tr>
</table>
<idcHtml:hidden name="ObjectActionForm" property="objectForm.encryptedId" />
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap><idcBean:message key="Password.ExpairyExemption.Preference" />
<%
	if(organization.getBrokerOrganization()!=null&&isSubportalView)
	{
%>
		&nbsp;&nbsp;<a class="lh" name="refLink" href="javascript:helpPopup('Cpty/Subportal/Details/CptyEditPasswordExempt.html#_top','Admin Portal Help')">
			<img src="/admin/theme/images/help.png" alt='<idcBean:message key="utility.help"/>' border="0"></img>
		</a>
<%
	}
%>
		</td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
    </tr>
</table>

<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
    <tr>
        <td class="f3" nowrap width="25%"><idcBean:message key="Organization.Password.ExpairyExemption" /></td>
        <td class="f" nowrap>
            <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.passwordExpiryExempt" value='<%=Boolean.toString(organization.isPasswordExpiryExempt())%>' />
        <idcHtml:checkbox name="ObjectActionForm" property="objectForm.passwordExpiryExempt"  onclick="checkBoxes('objectForm.passwordExpiryExempt')" />
               <script>elementBinding.registerCheckboxChange("objectForm.passwordExpiryExempt","passwordExpiryExempt");</script> </td>
        </td>
    </tr>
    <tr>
        <td class="f3" nowrap width="25%"><idcBean:message key="Organization.password.ExemptPasswordLocking" /></td>
        <td class="f" nowrap>
        <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.maxFailedLoginAttemptsExempt" value="<%=Boolean.toString(organization.isMaxFailedLoginAttemptsExempt())%>"/>
              <idcHtml:checkbox name="ObjectActionForm" property="objectForm.maxFailedLoginAttemptsExempt"  onclick="checkBoxes('objectForm.maxFailedLoginAttemptsExempt')" />
               <script>elementBinding.registerCheckboxChange("objectForm.maxFailedLoginAttemptsExempt","maxFailedLoginAttemptsExempt");</script> </td>
        </td>
    </tr>
</table>
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap><idcBean:message key="Organization.ExternalAuthentication" /></td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>

    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl">
    <tr>
        <td class="f3" nowrap width="25%"><idcBean:message key="Organization.ExternalAuthentication.Required" /></td>
		        <td class="f" nowrap>
          <idcHtml:select name="ObjectActionForm" property="objectForm.externalAuth" >
                         <idcHtml:option value='0'>
		                     <idcBean:message key="Organization.ExternalAuthentication.StaticPassword" />
	                     </idcHtml:option>
                         <idcHtml:option value='1'>
		                     <idcBean:message key="Organization.ExternalAuthentication.OTP" />
	                     </idcHtml:option>
                         <idcHtml:option value='2'>
		                     <idcBean:message key="Organization.ExternalAuthentication.2FA" />
	                     </idcHtml:option>
                          <idcHtml:option value='3'>
                              <idcBean:message key="Organization.ExternalAuthentication.GoogleAuth" />
                          </idcHtml:option>
             </idcHtml:select>
        </td>
    </tr>
</table>
<idcBean:define id="showLimitAsMatchedPrice" name="ObjectActionForm" property="objectForm.object.showLimitAsMatchedPrice" type="java.lang.Boolean"/>
<idcBean:define id="showLimitAsMatchedPriceBroker" name="ObjectActionForm" property="objectForm.object.showLimitAsMatchedPriceBroker" type="java.lang.Boolean"/>
<idcBean:define id="loginUser" name="ObjectActionForm" property="user" type="com.integral.user.UserC"/>
<%
if(loginUser.hasPermission("LimitOrderMsgDisp")){%>
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap><idcBean:message key="Organization.LimitOrderMessageDisplay" /></td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
        <td class="stl8" nowrap>&nbsp;</td>
    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
    <tr>
        <td class="f3" width="25%" nowrap><idcBean:message key="Organization.ShowMatchedAsLimit" /></td>
        <td class="f3" nowrap>
          <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.showLimitAsMatchedPrice" value="<%=String.valueOf(showLimitAsMatchedPrice)%>"/>
          <idcHtml:checkbox name="ObjectActionForm" property="objectForm.showLimitAsMatchedPrice"  onclick="checkBoxes('objectForm.showLimitAsMatchedPrice')" />
           <script>elementBinding.registerCheckboxChange("objectForm.showLimitAsMatchedPrice","showLimitAsMatchedPrice");
           </script>
        </td>
    </tr>
</table>
<div class="space"></div>
<%}%>
<%
if(loginUser.hasPermission("LimitOrderMsgDispBrk")){%>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap><idcBean:message key="Organization.LimitOrderMessageDisplayBroker" /></td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
        <td class="stl8" nowrap>&nbsp;</td>
    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
    <tr>
        <td class="f3" width="25%" nowrap><idcBean:message key="Organization.ShowMatchedAsLimit" /></td>
        <td class="f3" nowrap>
          <idcHtml:hidden name = "ObjectActionForm" property = "objectForm.showLimitAsMatchedPriceBroker" value="<%=String.valueOf(showLimitAsMatchedPriceBroker)%>"/>
          <idcHtml:checkbox name="ObjectActionForm" property="objectForm.showLimitAsMatchedPriceBroker"  onclick="checkBoxes('objectForm.showLimitAsMatchedPriceBroker')" />
           <script>elementBinding.registerCheckboxChange("objectForm.showLimitAsMatchedPriceBroker","showLimitAsMatchedPriceBroker");
           </script>
        </td>
    </tr>
</table>
<%}%>
<idcBean:define id="cancelOrdersOnUserInactivation" name="ObjectActionForm" property="objectForm.object.cancelOrdersOnUserInactivation" type="java.lang.Boolean"/>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap><idcBean:message key="MakerPortal.Menu.Report.Order"/></td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
        <td class="stl8" nowrap>&nbsp;</td>
    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
    <tr>
        <td class="f3" width="25%" nowrap><idcBean:message key="ORG.DETAIL.CANCELORDER"/></td>
        <td class="f3" nowrap>
          <input type="checkbox" name="workflowMessage.cancelOrdersOnUserInactivation" id="cancelOrdersOnUserInactivation" value="<%=String.valueOf(cancelOrdersOnUserInactivation)%>" <%=(cancelOrdersOnUserInactivation?"checked":"") %>/>
        </td>
    </tr>
</table>
<%
if(isSysAdmin )
{
%>
<idcBean:define id="fwdURI"  name="ObjectActionForm" property="forwardURI" type="java.lang.String"/>
<input type="hidden" name="workflowMessage.objectChanged" value="false">
<idcBean:define id="loginUser" name="ObjectActionForm" property="user" type="com.integral.user.UserC" />
<idcBean:define id="nsType1" name="ObjectActionForm" property="objectForm.object.namespace.description"/>
<idcBean:define id="organization" name="ObjectActionForm" property="objectForm.object" type="com.integral.user.Organization" />
<idcHtml:hidden name="ObjectActionForm" property="objectForm.encryptedId" />
<%
if(fwdURI.equals("Admin.Organization.Edit"))
{
%>
<idcBean:setProperty name="ObjectActionForm" property="inputForm.original" valueObj="<%=organization%>"/>
<%}%>
<idcBean:setProperty name="ObjectActionForm" property="alias.contactForm" value="objectForm"/>
<idcHtml:hidden name="ObjectActionForm" property="alias.contactForm" value="objectForm"/>
<idcHtml:hidden name="ObjectActionForm" property="contactForm.atomic" value="false"/>
<idcBean:define id="providerOrgFunction" name="ObjectActionForm" property="objectForm.object.organizationFunction(PROVIDER)" type="ProviderOrgFunctionC" />
<%
    ProviderOrgFunctionC brkOrgFunct=(ProviderOrgFunctionC)organization.getOrganizationFunction( ProviderOrgFunction.ORG_FUNC_NAME );
    if (providerOrgFunction!=null)
    {
%>
<idcBean:define id="bestQuoteStrategy" name="ObjectActionForm" property="objectForm.object.organizationFunction(PROVIDER).bestQuoteStrategy" type="java.lang.Integer" />
<%
    Collection keysForQuoteLogics=mapForQuoteLogics.keySet();
    List keysListForQuoteLogics=new ArrayList(keysForQuoteLogics);
    Collections.sort (keysListForQuoteLogics, new Comparator()
    {
        public int compare (Object o1, Object o2)
        {
            return ((Integer)o1).compareTo((Integer)o2);
        }
    });
%>
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap><idcBean:message key="Organization.Best.Quote.Logic.Flag" /></td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
    <tr>
        <td class="f3" nowrap width="25%"><idcBean:message key="Organization.Best.Quote.Logic" /></td>
        <td class="f" nowrap>
          <idcHtml:select name="ObjectActionForm" property="workflowMessage.bestQuoteStrategy" value='<%=bestQuoteStrategy==null?(""+ProviderOrgFunction.SAME_QUOTE):""+bestQuoteStrategy %>' >
                  <idcLogic:iterate id="quoteLogic" collection="<%=keysListForQuoteLogics%>">
                         <idcHtml:option value='<%=""+quoteLogic%>'>
                         <idcBean:message key="<%=(String)mapForQuoteLogics.get(quoteLogic)%>" />
                     </idcHtml:option>
                 </idcLogic:iterate>
             </idcHtml:select>
        </td>
    </tr>
</table>
<%
    }
%>
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap>Market Feed Server Configuration</td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
        <td class="stl8" nowrap>&nbsp;</td>
    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
<tr>
    <td class="f3" nowrap width="25%">Market Feed Server Enabled</td>
    <td class="f" nowrap>
    	<input type="checkbox" id="mdfServerEnabled" name="workflowMessage.mdfServerEnabled" value="<%=organization.isMarketDataFeedServerEnabled()?true:false%>" <%=organization.isMarketDataFeedServerEnabled()?"checked":""%>>
    </td>

</tr>
</table>
<%
 }
%>
<%
if(TradeConfigurationFactory.getTradeConfigurationMBean().isProrataForwardSupportEnabled(organization))
{
	Object cfValue = organization.getCustomFieldValue( ISCommonConstants.PRORATA_FORWARD_SPECIAL_CLIENT );
	boolean prorataForwardSpecialClient = cfValue != null ? ((Boolean) cfValue) : false;
%>
<div class="space"></div>
<table width="100%" cellpadding="0" cellspacing="0" class="outl">
    <tr>
        <td class="stl2" nowrap><idcBean:message key="Organization.ProrataForward.Configuration" /></td>
        <td class="stl3" nowrap>&nbsp;</td>
        <td class="stl4" nowrap>&nbsp;</td>
        <td class="stl8" nowrap>&nbsp;</td>
    </tr>
</table>
<table width="100%" cellpadding="0" cellspacing="1" class="outl2">
    <tr>
        <td class="f3" width="25%" nowrap><idcBean:message key="Organization.ProrataForward.Special.Client" /></td>
        <td class="f3" nowrap>
          <input type="checkbox" name="workflowMessage.prorataForwardSpecialClient" id="prorataForwardSpecialClient" value="<%=String.valueOf(prorataForwardSpecialClient)%>" <%=(prorataForwardSpecialClient?"checked":"") %>/>
        </td>
    </tr>
</table>
<%}%>
