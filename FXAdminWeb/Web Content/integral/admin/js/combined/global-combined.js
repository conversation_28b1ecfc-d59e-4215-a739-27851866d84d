var DELIMITER0 = "~,";
var DELIMITER1 = "!,";

// *****************************************************************************
// AbstractCollection.js
// This is an abstract JavaScript class which implements the basic functions
// for collections. 
// See ArrayList.js
//     HashMap.js
//     HashSet.js
// For concrete implmentations.
//
// Copyright (c) 1999-2002 Integral Development Corp. All rights reserved.
// *****************************************************************************


//*****************
// Constructor
//*****************


function AbstractCollection(elements) {
	this.elementData = new Array();
	if (elements) {
		for (var i = 0; i < elements.length; i++) {
			this.elementData[i] = elements[i];
		}
	}

	this.isCollection = true;
	this.size = size;
	this.isEmpty = isEmpty;
	this.add = add;
	this.addAll = addAll;
	this.remove = remove;
	this.removeAll = removeAll;
	this.clear = clear;
	this.contains = contains;
	this.toArray =  toArray;

	//********************
	// @return the Size of the Collection
	//********************

	function size() {
		return this.elementData.length;
	}

	
	//*******************
	// @return true if the collection is empty [contains no data]
	//*******************

	function isEmpty() {
		return this.size() == 0;
	}

	//*******************
	// Add an element to anInstance. If Instance is not defined add to this.
	// @return true if added
	// @param anElement object to be added
	// @param anInstance - a Collection or null
	//*******************

	function add(anElement, anInstance) {
		if ((typeof anElement) == "undefined") {
			return false;
		}

		var theInstance = anInstance || this;
		theInstance.elementData[theInstance.elementData.length] = anElement;
		return true;
	}

	//*******************
	// Add all of aCollection to this.
	//@param aCollection
	//******************	

	function addAll(aCollection) {
		for(var i=0; i<aCollection.size(); i++)
			this.add(aCollection.elementData[i]);

		return true;
	}
	
	//*******************
	// Remove anElement from this
	// @param anElement an Object
	// @return true if removed
	//*******************

	function remove(anElement) {
		var index = ArrayUtils.getElementIndex(this.elementData, anElement);
		if(index == -1) {
			return false;
		}
			
		this.elementData = ArrayUtils.removeElementByIndex(this.elementData, index);
		return true;
	}

	
	//*******************
	// Remove all of aCollection from this.
	//@param aCollection
	//******************
	
	function removeAll(aCollection)	{
		for(var i=0; i<aCollection.size(); i++) {
			this.remove(aCollection.elementData[i]);
		}

		return true;
	}

	//*******************
	// Remove all elements from the Collection
	//******************
	
	function clear() {
		this.elementData = new Array();
	}

	
	//***************
	// @param anElement an Object
	// @return true if the collection contains the anElement
	//*******************

	function contains(anElement) {
		return ArrayUtils.getElementIndex(this.elementData, anElement) != -1;
	}

	//*****************
	// Convert to an Array
	// @return anArray
	//*****************

	function toArray() {
		return this.elementData;
	}
}

// *****************************************************************************
// ArrayList.js
// 
// This is a JavaScript ArrayList class.
//
// Copyright (c) 1999-2002 Integral Development Corp. All rights reserved.
// *****************************************************************************

ArrayList.prototype = new AbstractCollection();

function ArrayList(elements) {
	this.base = AbstractCollection;
	this.base(elements);

	this.set = set;
	this.get = get;

	//******************************
	// Set anElement at an Index
	// @param anIndex
	// @param anElement
	//******************************	

	function set(anIndex, anElement) {
		this.elementData[anIndex] = anElement;
	}

	//******************************
	// Retreive an Ojbect at an Index
	// @return anObject
	// @param anIndex
	//******************************

	function get(anIndex) {
		return this.elementData[anIndex];
	}
}

// *****************************************************************************
// ArrayUtils.js
// This JavaScript class holds array utility methods.  These methods are
// accessible through the ArrayUtils variable.
//
// Copyright (c) 1999-2002 Integral Development Corp. All rights reserved.
// *****************************************************************************


//****************
// Constructor
//****************

function ArrayUtilsC() {
	//Function Definition
	this.isArray = isArray;
	this.validateArray = validateArray;
	this.getTokens = getTokens;
	this.getElementIndex = getElementIndex;
	this.removeElementByIndex = removeElementByIndex;
	this.removeElements = removeElements;
	this.addElements = addElements;

	function isArray(anObj, checkLength) {
		if ((typeof anObj.length) != "undefined" && !isNaN(anObj.length)) {
			if (checkLength) {
				if (anObj[0]) {
					return true;
				} else {
					return false;
				}
			}

			return true;
		}

		return false;
	}

	//*****************
	// Ensure aParameter is an Array
	// @return anArray
	// @param aParameter
	//*****************

	function validateArray(aParameter) {
		if ((typeof aParameter) == "undefined") {
			return aParameter;
		}

		var anArray = aParameter;
		if((typeof aParameter) == "string") {
			anArray = new Array();
			anArray[0] = aParameter;
		}

		return anArray;
	}


	//**************
	// Parse a string breaking into tokens by delimiter
	// @return anArray tokens 
	// @param aString string to be parsed
	// @param delimiter delimiter for tokens
	// @param times null or number of times to iterate
	//**************

	function getTokens(aString, delimiter, times) {
		var index = -1;
		var tokens = new Array();
		var times = times || -1;

		var i = 0;
		for(i=0; (index = aString.indexOf(delimiter)) != -1; i++) {
			tokens[i] = aString.substring(0, index);

			if(index >= length(aString) - 1) {
				break;
			}

			aString = aString.substring(index + delimiter.length);

			if(times != -1 && i >= times - 1) {
				i++;
				break;
			}
		}

		aString = StringUtils.trim(aString, delimiter);

		if(aString) {
			tokens[i] = aString;
		}

		return tokens;
	}
	
	
	//**************
	// Return the Index of an Element
	// @return int index
	// @param anArray to be searched
	// @param anElement index to be found for
	// @param ignoreCase true to ignore
	//**************

	function getElementIndex(anArray, anElement, ignoreCase) {
		for(var i=0; i<anArray.length; i++) {
			var element = anArray[i];

			if(element == anElement || (ignoreCase && element.toLowerCase() == anElement.toLowerCase())) {
				return i;
			}
		}

		return -1;
	}

	//**************
	// Remove an Element from an array
	// @return newArray copy of array without index
	// @param anArray 
	// @param index to remove
	//**************


	function removeElementByIndex(anArray, index) {
		var newArray = new Array();
		for(var i=0, j=0; i<anArray.length; i++) {
			if(index != i) {
				newArray[j++] = anArray[i];
			}
		}

		return newArray;
	}

	//**************
	// Remove an Elements from an array
	// @return newArray copy of array without index range
	// @param anArray 
	// @param index1 to remove from
	// @param index2 to remove to
	//**************

	function removeElements(anArray, index1, index2) {
		var newArray = new Array();
		var length = anArray.length;

		for (var i = 0, j = 0; i < length; i++) {
			if (i < index1 || i >= index2) {
				newArray[j++] = anArray[i];
			}
		}

		return newArray;
	}

	
	//**************
	// Add Elements to an array
	// @param anArray to be added to
	// @param elements to be added
	//**************
	
	function addElements(anArray, elements) {
		for (var i = 0; i < elements.length; i++) {
			anArray[anArray.length] = elements[i];
		}
	}
}

var ArrayUtils = new ArrayUtilsC();

/**
 * The following methods are deprecated, use ArrayUtils.aMethod instead
 */

function getTokens(aString, delimiter, times) {return ArrayUtils.getTokens(aString, delimiter, times);}
function getElementIndex(anArray, anElement) {return ArrayUtils.getElementIndex(anArray, anElement);}
function removeElements(anArray, index1, index2) {return ArrayUtils.removeElements(anArray, index1, index2);}
function addElements(anArray, elements) {ArrayUtils.addElements(anArray, elements);}

// *****************************************************************************
// HashMap.js
// This is JavaScript class which implements a HashMap.
//
// Copyright (c) 1999-2002 Integral Development Corp. All rights reserved.
// *****************************************************************************

//*****************
// Constructor
//*****************

function HashMap(keys, values) {

	// Function definitions

	this.keys = keys || new Array();
	this.values = values || new Array();

	this.keySet = keySet;
	this.containsKey = containsKey;
	this.get = get;
	this.getByIndex = getByIndex;
	this.put = put;
	this.size = size;
	this.remove = remove;
	this.clear = clear;
	this.insert = insert;

	
	//***********************************
	//@return the keys of the HashMap
	//***********************************

	function keySet() {
		return this.keys;
	}

	
	//************************************
	// @return true if the HashMap contains aKey
	// @param aKey
	//************************************

	function containsKey(aKey) {
		return (this.get(aKey) != null);
	}

	//************************************
	// @return the Value for a key
	// @param key aKey	
	//************************************

	function get(key) {
		for(var i=0; i<this.keys.length; i++) {
			if(key == this.keys[i])
				return this.values[i];
		}

		return null;
	}

	
	//**************************************
	// @return an Object at an Index
	// @param index
	//**************************************

	function getByIndex(index) {
		if(this.values.length - 1 < index)
			return null;
		
		return this.values[index];
	}

	
	//*************************************
	// Insert Key/Value pair at an Index
	// @param index
	// @param key
	// @param value
	//*************************************

	function insert(index, key, value) {
		var oldLength = this.keys.length;

		for(var i=oldLength; i>index; i--)
		{
			this.keys[i] = this.keys[i-1];
			this.values[i] = this.values[i-1];
		}

		this.keys[index] = key;
		this.values[index] = value;
	}

	
	//***************************************
	// Insert key/Value pair
	// @param key - a key
	// @param value - a Value
	//***************************************

	function put(key, value) {
		var index = ArrayUtils.getElementIndex(this.keys, key);
		if(index != -1)
		{
			var oldValue = this.values[index];
			this.values[index] = value;
			return oldValue;
		}

		this.keys[this.keys.length] = key;
		this.values[this.values.length] = value;
		return null;
	}

	//*****************************************
	// @return size of hashMap
	//*****************************************

	function size() {
		return this.keys.length;
	}

	//*******************************************
	// Remove a Key
	// @param key - akey
	//*******************************************

	function remove(key) {
		var index = ArrayUtils.getElementIndex(this.keys, key);
		if(index == -1)
			return null;

		var value = this.values[index];
		this.keys = ArrayUtils.removeElementByIndex(this.keys, index);
		this.values = ArrayUtils.removeElementByIndex(this.values, index);
	}

	//*********************************************
	// Clear all data from the hashmap
	//*********************************************

	function clear() {
		this.keys = new Array();
		this.values = new Array();
	}
}

// *****************************************************************************
// HashSet.js
// This is JavaScript class which implements a HashSet.
//
// Copyright (c) 1999-2002 Integral Development Corp. All rights reserved.
// *****************************************************************************

HashSet.prototype = new AbstractCollection();

//*************
// Constructor
//*************

function HashSet(elements) {
	// Function Definition

	this.base = AbstractCollection;
	this.base(elements);

	this.add = add;
	this.clone = clone;

	//*********************************
	// Add an Element
	// @param anElement to be added
	//*********************************

	function add(anElement) {
		if(this.contains(anElement)) {
			return false;
		}

		return HashSet.prototype.add(anElement, this);
	}

	//**********************************
	// Copy the HashSet
	//**********************************

	function clone() {
		var aSet = new HashSet();
		aSet.addAll(this);
		return aSet;
	}
}

// *****************************************************************************
// StringUtils.js
// This JavaScript class holds String utility methods.  These methods are
// accessible through the StringUtils variable.
//
// Copyright (c) 1999-2002 Integral Development Corp. All rights reserved.
// *****************************************************************************



function StringUtilsC() {
	this.getSubstring = getSubstring;
	this.evalString = evalString;
	this.setUrlProperty = setUrlProperty;
	this.getProperty = getProperty;
	this.trim = trim;
	this.length = length;

	//************************
	// @return subString
	// @param aString
	// @param flag1
	// @param flag2
	//************************
	function getSubstring(aString, flag1, flag2) {
		var index1 = aString.indexOf(flag1);
		if (index1 == -1) {
			return null;
		}

		var index2 = aString.indexOf(flag2, index1 + flag1.length);
		if (index2 == -1) {
			index2 = aString.length;
		}

		return aString.substring(index1 + flag1.length, index2);
	}

	//**************************
	// @return aString or if null ""
	// @param aString
	//**************************
	function evalString(aString) {
		if (!aString) {
			return "";
		}

		return (aString == "null" ? "" : aString);
	}

	//***********************
	// @return aURL
	// @param aString
	// @param Key
	// @param value
	// @param force - true override existing parameter
	//***********************

	function setUrlProperty(aString, key, value, force) {
		var keyStr = key + "=";

		var startIndex = aString.indexOf(keyStr);
		if(!aString || (startIndex == -1 && !force)) {
			return aString;
		}

		if(aString.indexOf("?") == -1) {
			return aString + "?" + key + "=" + value;
		}
		if(startIndex == -1) {
			return aString + "&" + key + "=" + value;
		}

		var endIndex = aString.indexOf("&", startIndex);
		var result = aString.substring(0, startIndex) + keyStr + value;

		if(endIndex != -1) {
			result += aString.substring(endIndex);
		}
	
		return result;
	}

	//***********************
	// @return a Propertry of a string
	// @param aString
	// @param aKey
	// @param aFlag
	// @param partialMatch
	//***********************

	function getProperty(aString, aKey, aFlag, partialMatch) {
		var flag = aFlag || "&";
		var keyStr = aKey + "=";
		var index1 = aString.indexOf(keyStr);
		if (index1 == -1) {
			return null;
		}

		if (index1 > 0 && !partialMatch && aString.substring(index1 - 1, index1).match(/[a-zA-Z]/)) {
			return null;
		}

		var index2 = aString.indexOf(flag, index1);
		if (index2 == -1) {
			index2 = aString.length;
		}

		return aString.substring(index1 + keyStr.length, index2);
	}

	//***********************
	// @return trimmed string
	// @param aString
	// @param tail 
	//***********************
	function trim(aString, tail, aTrimLeadingFlag, aTrimTrailingFlag) {
		var trimLeadingFlag = (typeof(aTrimLeadingFlag) == "undefined" ? true : aTrimLeadingFlag);
		var trimTrailingFlag = (typeof(aTrimTrailingFlag) == "undefined" ? true : aTrimTrailingFlag);

		if(length(aString) == 0) {
			return "";
		}

		if (!tail) {
			return new String(aString).replace(/^\s*|\s*$/g, "");
		}

		var index = -1;

		while(trimLeadingFlag && aString.indexOf(tail) == 0) {
			if(aString == tail) {
				return "";
			}

			aString = aString.substring(tail.length);
		}

		while(trimTrailingFlag && (index = aString.lastIndexOf(tail)) == length(aString) - tail.length && index != -1) {
			aString = aString.substring(0, index);
		}

		return aString;
	}

	//***********************
	// @return length of string safely
	// @param aString
	//***********************

	function length(aString) {
		if(aString && aString.length) {
			return aString.length;
		} else {
			return 0;
		}
	}
}

var StringUtils = new StringUtilsC();

/**
 * The following methods are deprecated, use StringUtils.aMethod() instead
 */

function getSubstring(aString, flag1, flag2) {return StringUtils.getSubstring(aString, flag1, flag2);}
function evalString(aString) {return StringUtils.evalString(aString);}
function setUrlProperty(aString, key, value, force) {return StringUtils.setUrlProperty(aString, key, value, force);}
function getProperty(aString, aKey, aFlag, partialMatch) {return StringUtils.getProperty(aString, aKey, aFlag, partialMatch);}
function trim(aString, tail) {return StringUtils.trim(aString, tail);}
function length(aString) {return StringUtils.length(aString);}

// ****************************************************************************
// HtmlUtilsC.js
// This is a collection of HTML utilities.
// The Utilities are made available through the variable HtmlUtils.
//
// For example:
// HtmlUtils.getElementById("anId");
//
// Copyright (c) 1999-2002 Integral Development Corp. All rights reserved.
// ****************************************************************************


function HtmlUtilsC() {
	//Function definitions
	this.isIE = isIE;
        this.getValue = getValue;
	this.setValue = setValue;
	this.getElementById = getElementById;
	this.getElementsByTagName = getElementsByTagName;
	this.getElementByValue = getElementByValue;
	this.findWindowObject = findWindowObject;
	this.findAllWindowObjects = findAllWindowObjects;
	this.findAllFrameObjects = findAllFrameObjects;
	this.findSelectedRadioVal= findSelectedRadioVal;
	this.addValidWindowObject = addValidWindowObject;
	this.isAlive = isAlive;
	this.setAllValues = setAllValues;
	this.enableAllInputs = enableAllInputs;
	this.setDisplayEnabled = setDisplayEnabled;

	
	//***************************************************************************************************
	//Determine if the current browser is IE or not
    //@return A flag indicating the browser is IE or not
    //***************************************************************************************************

	function isIE() {
		return navigator.appName == "Microsoft Internet Explorer";
	}

//***************************************************************************************************
	//@return the value of  radio Obj.
    //@param radio obj name 
    //***************************************************************************************************

	
	function findSelectedRadioVal(name){
	    var fields = document.getElementsByName(name);
		var selRadValue;
		for(var i = 0; i < fields.length; i++){
		    if(fields[i].checked){
		        selRadValue = fields[i].value;
		        return selRadValue;
		    }
		}
		return "";
	}

	//***************************************************************************************************
	//@return the value of anObj.
    //@param anOjb an HTML object
    //@param propName the name used to extract the property value for example text. If null value is used.
    //***************************************************************************************************

	function getValue(anObj, propName) {
		if ((typeof anObj) == "string") {
			anObj = this.getElementById(anObj);
		}

		if (!anObj) {
			return null;
		}

		if (!anObj.type) {
			return this.isIE() ? anObj.innerHTML : "";
		}

		if (anObj.type.indexOf("select") == 0) {
			if (anObj.selectedIndex >= 0) {
				var option = anObj.options[anObj.selectedIndex];
				return propName == "text" ? option.text : option.value;
			} else {
				return "";
			}
		} else if (anObj.type == "radio") {
			var radios = this.getElementsByTagName("input", "radio");
			for (var i = 0 ; i < radios.length; i++) {
				var radio = radios[i];
				if (radio.checked) {
					//verify that this is the correct radio button before returning its value
					if (radio.name == anObj.name) {
						return radio.value;
					}
				}
			}
		} else if (anObj.type.indexOf("checkbox") == 0) {
			return anObj.checked;
		} else if ((typeof anObj.value) != "undefined") {
			return anObj.value;
		}
	}

	//*************************************************
	// Turn the Display on or off for an object
	// @param anObj to enable/disable display [a String value will be converted to an object]
	// @param enable - boolean true enable.
	// @param window - window containing the element
	//*************************************************
	function setDisplayEnabled(anObj, isEnabled, aWnd){
		var wnd = aWnd || window;
	   	if ((typeof anObj) == "string") {
			anObj = this.getElementById(anObj,wnd);
		}
               if (anObj){
		  anObj.style.display = isEnabled ? "" : "none";		
	       }
	}

	//*****************************************************************************************************
	// Set the value of anObj to be aValue. Function will use the appropriate method for the objects type.
        //@param anObj  -anObj to set the value on
        //@param aValue - value to set on the object
        //*****************************************************************************************************
	
	function setValue(anObj, aValue) {
		if ((typeof anObj) == "string") {
			anObj = this.getElementById(anObj);
		}

		if ((typeof anObj) == "undefined") {
			return;
		}

		if (anObj.type && (anObj.type.indexOf("select") == 0)) {
			for (var i = 0 ; i < anObj.options.length; i++) {
				if (anObj.options[i].value == aValue) {
					anObj.selectedIndex = i;
				}
			}
		} else if (anObj.type && (anObj.type.indexOf("checkbox") == 0 || anObj.type == "radio")) {
			anObj.checked = eval(aValue);
		} else if ((typeof anObj.value) != "undefined") {
			anObj.value = aValue;
		} else if (this.isIE()) {
			anObj.innerHTML = aValue;
		} else if (anObj.document && anObj.document.write) {
			anObj.document.write(aValue);
			anObj.document.close();
		}
	}

	//******************************************************************************************************
        // Find an object within a document by its id.
	// Function will use the most appropriate method based on the browser type.
        // @return an Object
        // @param id - id of the object to find
        //******************************************************************************************************

        function getElementById(id, aWnd, aIsIeFlag) {
		var wnd = aWnd || window;
		var element = null;
		var isIeFlag = (typeof aIsIeFlag) == "undefined" ? this.isIE() : aIsIeFlag;

		if (isIeFlag) {
			element = wnd.document.getElementById(id);
			if (!element) {
				element = wnd.document.frames[id];
			}
		} else {
			element = wnd.document.forms[0].elements[id];
			if (!element && wnd.document.layers) {
				element = wnd.document.layers[id];
			}
		}

		return element;
	}

	//******************************************************************************************************
    // Finds an objects array within a document by the tag name.
    // @return an objects array
    // @param aTagName - The tag name to be used for search
    // @param aType - Type of this tag
    // @param aWnd - Specified window
    //******************************************************************************************************
	function getElementsByTagName(tagName, aType, aWnd) {
		var wnd = aWnd || window;

		var rawResult = new Array();

		if (this.isIE()) {
			rawResult = wnd.document.getElementsByTagName(tagName);
		} else if (tagName == "input") {
			rawResult = wnd.document.forms[0].elements;
		}

		var result = new HashSet();

		for (var i = 0; i < rawResult.length; i++) {
			var element = rawResult[i];

			if (!aType || element.type == aType) {
				result.add(element);
			}
		}

		return result.toArray();
	}

	//******************************************************************************************************
        // Find the first input object within a document by value.
	// Function will use the most appropriate method based on the browser type.
        // @return an Object - null if not found
        // @param id - id of the object to find
        //******************************************************************************************************
	
	function getElementByValue(aValue) {
		var elements = window.document.getElementsByTagName("input");
		for (var i = 0; i < elements.length; i++) {
			var element = elements[i];
			if (element.value == aValue) {
				return element;
			}
		}

		return null;
	}

	//******************************************************************************************************
        // Set all objects with equalling id to a value.
	// Function will use the most appropriate method based on the browser type.
        // @param aValue - value to be set
        // @param anId - name of the object contained within the form to be set
        //******************************************************************************************************

	function setAllValues(anId, aValue) {
		var elements = document.forms[0].elements;
		for (var i = 0 ; i < elements.length; i++) {
			var obj = elements[i];
			if (obj.name == anId) {
				setValue(obj, aValue);
			}
		}
	}

	//******************************************************************************************************
        // Enable or disable all input objects on a window.
        // @param anEnabledFlag - true to enable; false to disable
        // @param aWnd -the window object to search for input elemeents
        //******************************************************************************************************
	
	function enableAllInputs(anEnabledFlag, aWnd, inputTypes) {
		var disabled = anEnabledFlag ? false : true;
		var wnd = aWnd || window;

		if ((typeof inputTypes) == "string") {
			inputTypes = new ArrayList([inputTypes]);
		}

		var elements = wnd.document.forms[0].elements;

		for (var i = 0; i < elements.length; i++) {
			var element = elements[i];

			if (!inputTypes || inputTypes.contains(element.type)) {
				element.disabled = disabled;
			}
		}

		if (!inputTypes) {
			wnd.disabled = disabled;
		}
	}

	//******************************************************************************************************
        // Find all objects within a specified window. If the window is not specified then search the entire
        // document hierarchy.
	// @return an ArrayList of Objects
        // @param objName - name of objects to be found
        // @param aWnd -the window to be searched. If not specified search the entire document hierarchy.
        //******************************************************************************************************

	function findAllWindowObjects(objName, aWnd) {
		var objs = new ArrayList();
		var wnd = aWnd || window.top;
		try{
			objs.addAll(this.findAllFrameObjects(objName, wnd));
		} catch(e){}

		if (wnd.opener) {
			try{
                objs.addAll(this.findAllFrameObjects(objName, wnd.opener.top));
            } catch(e){}
		}

		return objs;
	}

	//******************************************************************************************************
	// Find all objects within a specified windows frame. If the window is not specified then an empty list
    // will be returned.
	// @return an ArrayList of Objects
    // @param objName - name of objects to be found
    // @param aWnd -the window to be searched. 
    //******************************************************************************************************

	function findAllFrameObjects(objName, aWnd) {
		var objs = new ArrayList();
		if (!aWnd) {
			return objs;
		}

		this.addValidWindowObject(objs, aWnd, objName);

		for (var i = 0; i < aWnd.frames.length; i++) {
			try{
				objs.addAll(this.findAllFrameObjects(objName, aWnd.frames[i]));
			}catch(e){}
		}
		
		if(aWnd.document.frames){
			for (var i = 0; i < aWnd.document.frames.length; i++) {
				try{	
					objs.addAll(this.findAllFrameObjects(objName, aWnd.document.frames[i]));
				}catch(e){}
			}
	    }

		return objs;
	}

	//******************************************************************************************************
	// Find the specified object within a specified window. Returns null is not found.
	// @return anObject - the object with the specified name. Null if not found.
    // @param objName - name of object to be found
    // @param aWnd -the window to be searched. If not specified search the entire document hierarchy.
    //******************************************************************************************************

	function findWindowObject(objName, aWnd) {
		var objs = this.findAllWindowObjects(objName, aWnd);
		return objs.size() > 0 ? objs.get(0) : null;
	}

	//******************************************************************************************************
	// Add an object to a valid window (not closed or disabled).
	// @param objs - Object list.
    // @param objName - name of object to be found
    // @param wnd -the window added to. 
    //******************************************************************************************************

	function addValidWindowObject(objs, wnd, objName) {
		if (!wnd || wnd.closed || wnd.disabled) {
			return;
		}

		var obj = eval("wnd." + objName);

		if (obj) {
			objs.add(obj);
		}
	}

	
	//******************************************************************************************************
	// Check to see if an obj is available for use. Determined by checking wether the objects window is
	// not disabled | closed | null.
	// @return true if object is alive (available). False otherwise 
	// @param anObj - name of object to be checked
	//******************************************************************************************************

	function isAlive(anObj) {
		if (!anObj.window || anObj.window.closed || anObj.window.disabled) {
			return false;
		}

		return anObj == eval("anObj.window." + anObj.name);
	}
}

var HtmlUtils = new HtmlUtilsC();

// ****************************************************************************
// IdcWindow.js
// This class holds a collection of window objects and a collection of on load
// scripts to be run. It is usefull for collecting all scripts which are required
// to be run at load or unload time.
//
//
// Copyright (c) 1999-2002 Integral Development Corp. All rights reserved.
// ****************************************************************************



function IdcWindow(name) {
	this.base = IdcObject;
	this.base(name || "idcWindow");

	this.windowObjects = new HashSet();
	this.onLoadScripts = new HashSet();
	this.onUnloadScripts = new HashSet();
	this.onSubmitScripts = new HashSet();

	this.registerWindowObject = registerWindowObject;
	this.registerOnLoadScript = registerOnLoadScript;
	this.registerOnUnloadScript = registerOnUnloadScript;
	this.registerOnSubmitScript = registerOnSubmitScript;
	this.executeRegisteredScripts = executeRegisteredScripts;
	this.onLoad = onLoad;
	this.onUnload = onUnload;
	this.onSubmit = onSubmit;
	this.updateWindowObjects = updateWindowObjects;

	//******************************************
	// Register a Window Object
	// @param obj a window object
	//******************************************

	function registerWindowObject(obj) {
		if (obj) {
			this.windowObjects.add(obj);
		}
	}

	//******************************************
	// Register an on load script name
	// @param aScript - a script name
	//******************************************

	function registerOnLoadScript(aScript) {
		this.onLoadScripts.add(aScript);
	}

	//******************************************
	// Register an on unload script name
	// @param aScript - a script name
	//******************************************

	function registerOnUnloadScript(aScript) {
		this.onUnloadScripts.add(aScript);
	}

	//******************************************
	// Register an on submit script name
	// @param aScript - a script name
	//******************************************

	function registerOnSubmitScript(aScript) {
		this.onSubmitScripts.add(aScript);
	}

	//********************************************
	// Trigger all registerd on load scripts
	//*********************************************
	function onLoad() {
		this.updateWindowObjects("onLoad");
		this.executeRegisteredScripts(this.onLoadScripts);
		this.loaded = true;
	}
	
	//********************************************
	// Trigger a on unload event
	//*********************************************

	function onUnload() {
		this.updateWindowObjects("onUnload");
		this.executeRegisteredScripts(this.onUnloadScripts);
		return true;
	}

	//********************************************
	// Trigger a on formsubmit event
	//*********************************************

	function onSubmit() {
		this.updateWindowObjects("onSubmit");
		this.executeRegisteredScripts(this.onSubmitScripts);
		return true;
	}

	//********************************************
	// Run the registered scripts set
	//*********************************************

	function executeRegisteredScripts(scriptsSet) {
		var scripts = scriptsSet.toArray();
		for (var i = 0; i < scripts.length; i++) {
			var script = scripts[i];
			eval(script);
		}
	}

	//********************************************
	// Update Window Objects using a function
	// @param functionName
	//********************************************

	function updateWindowObjects(functionName) {
		var windowObjs = this.windowObjects.toArray();
		for (var i = 0; i < windowObjs.length; i++) {
			var windowObj = windowObjs[i];
			if (eval("windowObj." + functionName)) {
				eval("windowObj." + functionName + "()");
			}
		}
	}
}

var idcWindow = new IdcWindow();

// ****************************************************************************
// IdcObject.js
// This is the abstract superclass for all Integral created Javascript classes.
// It is similar to Object in java.
//
// Copyright (c) 1999-2002 Integral Development Corp. All rights reserved.
// ****************************************************************************

function IdcObject(name) {
    // name is the name of the variable being assigned in the window.
	this.name = name;

    // window the window this object was created in
	this.window = window;

	// unique instance id to identify this object
	this.instanceId = new Date().getTime();
}

// ****************************************************************************
// ElementBinding.js
// 
// This javaScript class is responsible for cascading changes to
// interested objects when there observed object changes.
// This class is accessible through the variable elementBinding.
// Typical use would be when a drop down box value changes
// an associated field displaying a value linked with the drop
// down value would also be updated.
//
// Usage:
// elementBinding.addBinding("sourceId", "destId || bindingParameters");
// elementBinding.change("id");
//
// Copyright (c) 1999-2002 Integral Development Corp. All rights reserved.
// ****************************************************************************


function ElementBindingC() {
	this.bindings = new HashMap();
	this.registeredChanges = new ArrayList();

	this.addBinding = addBinding;
	this.change = change;
	this.doChange = doChange;
	this.updateDestObj = updateDestObj;
	this.registerChange = registerChange;
	this.registerCheckboxChange = registerCheckboxChange;
	this.updateObservers = updateObservers;
	this.onLoad = onLoad;

	
	//************************************
	// Register a change notification
	// @param sourceId - Id of the observed object
	// @param destIds - Ids of the observing objects
	// @param customMethods - property of sourceId to extract or the customized function to be invoked
	// @param formats - Formatting definitions for destId object
	//*************************************

	function addBinding(sourceId, aBindingParameters) {
		var bindingParameters = (typeof aBindingParameters) == "string" ? new BindingParameters(aBindingParameters) : aBindingParameters;

		var bindingParametersList = this.bindings.get(sourceId);
		if (!bindingParametersList) {
			bindingParametersList = new ArrayList();
		}

		bindingParametersList.add(bindingParameters);

		this.bindings.put(sourceId, bindingParametersList);
		this.registerChange(sourceId);
	}

	
	//************************************
	// Trigger change notification
	// Using dynamic arguments [array of source Ids]
	//************************************
	function change(aSourceIdObj) {
		var sourceObj = aSourceIdObj;

		if ((typeof aSourceIdObj) == "string") {
			var bindingParametersList = this.bindings.get(aSourceIdObj);
			if (bindingParametersList && bindingParametersList.size() > 0) {
				var bindingParameters = bindingParametersList.get(0);
				sourceObj = bindingParameters.validateObj(aSourceIdObj, bindingParameters.sourceWindow);
			}
		}

		this.doChange(sourceObj);
	}

	//**************************************
	// Register an Object Change without triggering the actual change notification.
	// This is useful when initializing a page (onLoading).
	//**************************************

	function registerChange(aSourceId) {
		this.registeredChanges.add(aSourceId);
	}

	//*****************************************
	// Register a Checkbox change without triggering the actual change notification.
	//*****************************************
	
	function registerCheckboxChange(hiddenInputName, checkboxName) {
		this.addBinding(hiddenInputName, checkboxName);
		this.addBinding(checkboxName, new BindingParameters(hiddenInputName, window, window, "checked"));
		this.registerChange(hiddenInputName);
	}

	
	//*********************************************
	// Perform change notification for an observed object.
	// @param soureObj - Changed object.
	//*********************************************

	function doChange(sourceObj, aStartSourceId) {
		if (!sourceObj)	{
			return;
		}	

		var sourceId = (sourceObj.id || sourceObj.name);

		// avoid dead loop
		if (sourceId == aStartSourceId) {
			return;
		}
		var startSourceId = aStartSourceId || sourceId;

		this.updateObservers(sourceObj);

		var bindingParametersList = this.bindings.get(sourceId);

		for (var i = 0; bindingParametersList && i < bindingParametersList.size(); i++) {
			var bindingParameters = bindingParametersList.get(i);
			this.updateDestObj(sourceObj, bindingParameters);

			// chained binding change
			this.doChange(bindingParameters.destObj, startSourceId);
		}
	}

	//**********************************************
	// <Private Method> Perform change notification
	// @param sourceObj - changed object
	// @param destHtmlObj - observing object
	// @param destObjParams - additional formatting for destHtmlObj.
	//**********************************************

	function updateDestObj(sourceObj, bindingParameters) {
		var destObj = bindingParameters.destObj;
		var customMethod = bindingParameters.customMethod;
		
		if (customMethod == "detailChild" 
			&& bindingParameters.sourceWindow != bindingParameters.destWindow
			&& !sourceObj.initialized) {
			HtmlUtils.setValue(sourceObj, HtmlUtils.getValue(destObj));
			sourceObj.initialized = true;
			return;
		}

		if (customMethod.indexOf("checked") == 0) {
			var enabled = sourceObj.type == "radio"? sourceObj.value == customMethod.substring(8) : sourceObj.checked;

			if (customMethod.indexOf("checked_") == 0) {
				destObj.disabled = !enabled;
			} else {
				destObj.value = new String(enabled);
			}
		} else {
			var value = HtmlUtils.getValue(sourceObj, customMethod);
			HtmlUtils.setValue(destObj, value);
		}

		if (customMethod.charAt(customMethod.length - 1) == ")") {
			eval(customMethod);
		}
	}

	//********************************************
	// Update all the observers
	// Typically triggered by an onLoad event of NotionalAmountFormat
	//********************************************
	function updateObservers(anElement) {
		var observers = anElement.observers ? anElement.observers.toArray() : null;
		if (observers == null) {
			return;
		}

		for (var i = 0; i < observers.length; i++) {
			var observer = observers[i];
			observer.focus();
			observer.blur();
		}
	}

	//********************************************
	// Perform all registered Changes.
	// Typically triggered by an onLoad event
	//********************************************

	function onLoad() {
		// Initialize the BindingParameters on page load
		for (var i = 0; i < this.registeredChanges.size(); i++) {
			var bindingParametersList = this.bindings.get(this.registeredChanges.get(i));
			
			for (var j = 0; bindingParametersList && j < bindingParametersList.size(); j++) {
				var bindingParameters = bindingParametersList.get(j);
				bindingParameters.init();
			}
		}
		
		// Do the initial elements changes
		for (var i = 0; i < this.registeredChanges.size(); i++) {
			this.change(this.registeredChanges.get(i));
		}
	}
}

var elementBinding = new ElementBindingC();

function BindingParameters(aDestId, aSourceWindow, aDestWindow, aCustomMethod) {
	this.base = IdcObject;
	this.base();

	this.isBindingParameter = true;
	this.sourceWindow = aSourceWindow || window;
	this.destWindow = aDestWindow || window;
	this.customMethod = aCustomMethod || "value";
	this.destId = aDestId;
	this.destObj = null;

	this.init = init;
	this.validateObj = validateObj;

	function init() {
		this.destObj = this.validateObj(this.destId, this.destWindow);
	}

	function validateObj(anIdObj, aWindow) {
		var anObj = anIdObj;

		if ((typeof anIdObj) == "string") {
			anObj = HtmlUtils.getElementById(anIdObj, aWindow);
			if (anObj) {
				anObj.id = anIdObj;
			}
		}

		return anObj;
	}
}

function ObjectChangeListener(aName, anObjectChangeHiddenName) {
	this.base = IdcObject;
	this.base(aName);

	this.checkList = new HashSet();
	this.register = register;
	this.validate = validate;
	this.setOriginalValue = setOriginalValue;
	this.checkValueChange = checkValueChange;
	this.isArray = isArray;
	this.onLoad = onLoad;
	this.onSubmit = onSubmit;
	this.objectChangeHiddenName = anObjectChangeHiddenName || "workflowMessage.objectChanged";

	function register(anIdObj) {
		this.checkList.add(anIdObj);
	}

	function onLoad() {
		this.validate(true);
	}

	function onSubmit() {
		this.validate(false);
	}

	function validate(isOnLoad) {
		var ids = this.checkList.toArray();

		for (var i = 0; i < ids.length; i++) {
			var htmlObj = HtmlUtils.getElementById(ids[i], window, false);
			if (htmlObj) {
				if (isOnLoad) {
					this.setOriginalValue(htmlObj);
				} else {
					if (this.checkValueChange(htmlObj)) {
						HtmlUtils.setValue(this.objectChangeHiddenName, "true");
						break;
					}
				}
			}
		}
	}

	function setOriginalValue(anObj) {	
		if ((anObj.type) && (anObj.type == "select-multiple"))
		{
			for (var i = 0; i < anObj.options.length; i++) {
				anObj.options[i].originalValue = anObj.options[i].value;
			}
		}
		else if (this.isArray(anObj)) { //for checkbox multiple selection
			for (var i = 0; i < anObj.length; i++) {
				// Jack
				if (anObj[i].checked == undefined)				
				{
					anObj[i].originalValue = anObj[i].value;
				} else
				{
					anObj[i].originalValue = anObj[i].checked;
				}
			}	  
		} else { // for other types
			anObj.originalValue = HtmlUtils.getValue(anObj);
		}
	}

	function isArray(anObj) {
		return (anObj.type && anObj.type.indexOf("select") == 0) ?
			false
			: ArrayUtils.isArray(anObj, true);
	}

	function checkValueChange(anObj) {
		if (anObj.type == "select-multiple")
		{
			for (var i = 0; i < anObj.options.length; i++) {
				if (anObj.options[i].originalValue != anObj.options[i].value)
				{
					return true;
				}
			}
		}
		else if (this.isArray(anObj)) {
			for (var i = 0; i < anObj.length; i++) {
				if (anObj[i].checked == undefined)
				{
					if (anObj[i].originalValue != anObj[i].value) {
						return true;
					}
				} else
				{
					if (anObj[i].originalValue != anObj[i].checked) {
						return true;
					}
				}
		    	}
		} else {
			return anObj.originalValue != HtmlUtils.getValue(anObj);
		}

		return false;
	}
}

objectChangeListener = new ObjectChangeListener("objectChangeListener");

// ****************************************************************************
// TableActions.js
// These JavaScript functions are used for the <idcTable:table> tables.
//
// Copyright (c) 1999-2002 Integral Development Corp. All rights reserved.
// ****************************************************************************


function TableActions() {
	this.doCheckOrUncheckAll = doCheckOrUncheckAll;
	this.pageAtRow = pageAtRow;
	this.sortOnColumn = sortOnColumn;
	this.search = search;
	this.searchClear = searchClear;
	this.searchOn = searchOn;


	function getTableForm()
	{
		var tableForm = document.forms['TableForm'] || document.getElementById('TableForm');
		if(undefined == tableForm || null==tableForm){
			tableForm = document.forms[0];
		}
		return tableForm;
	}
	// Function doCheckOrUncheckAll()
	//
	// This function will check/uncheck all checkboxes on the current page.

	function doCheckOrUncheckAll()
	{
		var tableForm = getTableForm();
		for (var j = 1; j < tableForm.elements.length; j++)
		{
			if ( tableForm.elements[j].name.indexOf("selectedItems") == 0 ) {
				tableForm.elements[j].checked = tableForm.elements["checkAll"].checked;
			}
		}
	}


	// Function pageAtRow( startRowId, newRowStart )
	//
	// Pass in the 'startRow' attribute for this table. Sets the 'startRow' attribute
	// on the IdcTableFormwith the newRowStart row count.

	function pageAtRow( startRowId, newRowStart ) {
		var startRow = document.getElementById( startRowId );
		if( null== startRow ) startRow = document.getElementsByName(startRowId)[0];
		startRow.value = newRowStart;
		getTableForm().submit();
	}


	// Function sortOnColumn(startRowId, colummSortedId, sortOrderId, column)
	//
	// Pass in the 'startRow', 'columnSorted', 'sortOrder' attributes for this table.
	// Sets the 'columnSorted' attribute from the passed in column variable.  Also,
	// reverses the 'sortOrder' value, to allow for sorting 'A-Z' or 'Z-A'.

	function sortOnColumn(startRowId, colummSortedId, sortOrderId, column,columnAllowingNullVal) {
		var startRow = document.getElementById( startRowId );
		if( null== startRow ) startRow = document.getElementsByName(startRowId)[0];
		var columnSorted = document.getElementById( colummSortedId );
		if( null== columnSorted ) columnSorted = document.getElementsByName(columnSorted)[0];
		var sortOrder = document.getElementById( sortOrderId );
		if( null== sortOrderId ) sortOrderId = document.getElementsByName(sortOrderId)[0];
		try
		{
			var columnAllowingNull = document.getElementsByName( "TableForm_columnSortedAllowingNull")[0];
			columnAllowingNull.value = columnAllowingNullVal;
		}catch(e){}

		try
		{
			if( validate()==false)return;
		}
		catch(e)
		{
			// validate method is not defined in all UI hence a workaround to handle the error

		}
   		startRow.value = 1;
		if (columnSorted.value == column){
			if (sortOrder.value == "ASC"){
				sortOrder.value = "DSC";
			} else {
				sortOrder.value = "ASC";
			}
		} else {
			columnSorted.value = column;
			sortOrder.value = "DSC";
		}
		getTableForm().submit();
	}


	// Function search( startRowId )
	//
	// Pass in the 'startRow' attribute for this table.  Sets the 'startRow'
	// attribute to 1.

	function search( startRowId , containsId, searchValue) {
		var startRow = document.getElementById( startRowId );
		if( null== startRow ) startRow = document.getElementsByName(startRowId)[0];
		var contains = document.getElementById( containsId );
		if( null== contains ) contains = document.getElementsByName(containsId)[0];
		if(searchValue == undefined){
			if(contains != undefined && contains.value == "")
				contains.disabled = true;
		}
		else{
			contains.value = searchValue;
		}
		startRow.value = 1;
		getTableForm().submit();
	}


	// Function searchClear( startRowId, containsId )
	//
	// Pass in the 'startRow' and 'queryForm.searchForm.contains' attribute for
	// this table.  Sets the 'startRow' attribute to 1.  Set the 'contains' attribute
	// to "".

	function searchClear( startRowId, containsId ) {
		var startRow = document.getElementById( startRowId );
		if( null== startRow ) startRow = document.getElementsByName(startRowId)[0];
		var contains = document.getElementById( containsId );
		if( null== contains ) contains = document.getElementsByName(containsId)[0];
		contains.disabled = true;
		startRow.value = 1;
		getTableForm().submit();
	}


	// Function searchOn( startRowId, searchPropertyId, containsId, searchValue )
	//
	// Sets the 'startRow' attribute to 1.  The 'contains' attribute is set to the
	// 'searchOnString' attribute (which comes from the JSP search input box).  The
	// 'searchAttribute' is set to the values of the JSP search type drop down box.

	function searchOn( startRowId, searchPropertyId, containsId, searchValue ) {
		var startRow = document.getElementById( startRowId );
		if( null== startRow ) startRow = document.getElementsByName(startRowId)[0];
		var contains = document.getElementById( containsId );
		if( null== contains ) contains = document.getElementsByName(containsId)[0];
		var searchProperty = document.getElementById( searchPropertyId );
		if( null== searchProperty ) searchProperty = document.getElementsByName(searchPropertyId)[0];

		startRow.value = 1;
		contains.value = searchValue;
		getTableForm().submit();
	}
}

var tableActions = new TableActions();

// Deprecated, use tableActions.method instead
function doCheckOrUncheckAll() {tableActions.doCheckOrUncheckAll();}
function pageAtRow( startRowId, newRowStart ) {tableActions.pageAtRow(startRowId, newRowStart);}
function sortOnColumn(startRowId, colummSortedId, sortOrderId, column,columnAllowingNull) {tableActions.sortOnColumn(startRowId, colummSortedId, sortOrderId, column,columnAllowingNull);}
function search( startRowId ,containsId,searchValue) {tableActions.search( startRowId,containsId,searchValue );}
function searchClear( startRowId, containsId ) {tableActions.searchClear( startRowId, containsId );}
function searchOn( startRowId, searchPropertyId, containsId, searchValue ) {tableActions.searchOn( startRowId, searchPropertyId, containsId, searchValue );}

// ****************************************************************************
// activate.js
// These JavaScript functions are used for activate/deactivate items in a table.
//
// Copyright (c) 1999-2002 Integral Development Corp. All rights reserved.
// ****************************************************************************


// Function doShowActive( showActive )
//
// Sets the 'startRow' attribute to 1.  The 'displayActive' attribute is set to 
// the 'showActive' attribute. 

function doShowActive(showActive){
	document.forms[0].startRow.value = 1;
	document.forms[0].displayActive.value=showActive;
	document.forms[0].submit();
}


// Function doActivateDeactivate( action, postToUrl )
//
// Sets the 'userAction' attribute to the 'action' variable passed in.  Sets 
// the 'action' attribute to the 'postToUrl' variable. The 'displayActive' 
// attribute is set to the 'showActive' attribute. 

function doActivateDeactivate(anAction,postToURL){
	document.forms[0].userAction.value=anAction;
	document.forms[0].action=postToURL;
	document.forms[0].submit();
}


// Function doInactivate()
//
// Inactivates the selected items.  Pass in the tableForm that you want to 
// submit on and pass in the status you want change.  The table form's
// action attribute will be set to '/admin/Workflow.do'.

function doInactivate( tableForm, statusId ) {
	var status = document.getElementById( statusId );
    if (status === null) {
        status = 'active';
    }else{
        status.value = "active";
    }
	document.forms[tableForm].action = "/admin/table/Workflow.do";
	document.forms[tableForm].event.value = "Inactivate";
	document.forms[tableForm].submit();
}	



// Function doActivate()
//
// Inactivates the selected items.  Pass in the tableForm that you want to 
// submit on and pass in the status you want change.  The table form's
// action attribute will be set to '/admin/Workflow.do'.

function doActivate( tableForm, statusId ) {
	var status = document.getElementById( statusId );
    if (status === null) {
        status = 'inactive';
    }else{
        status.value = "inactive";
    }
	document.forms[tableForm].action = "/admin/table/Workflow.do";
	document.forms[tableForm].event.value = "Activate";
	document.forms[tableForm].submit();
}
	
// Function setupForm()
//
// Setup the table form for the event.  Pass in the tableForm that you want to 
// submit on, the status you want change, and the event name.  The table form's
// action attribute will be set to '/admin/Workflow.do'. 
//	
function setupForm( tableForm, statusId, eventValue ) {
	var status = document.getElementById( statusId );
	status.value = "new";
	document.forms[tableForm].action = "/admin/table/Workflow.do";
	document.forms[tableForm].event.value = eventValue;
}
//*********************************
// Get time zone
// @param aString
//*********************************
function TimeZone(aString) {
	var fields = ArrayUtils.getTokens(aString, DELIMITER0);
	this.id = fields[0];
	this.offset = eval(fields[1]);
}

var userTimeZone = window.USER_TIME_ZONE_STRING ? new TimeZone(USER_TIME_ZONE_STRING) : null;

var LOCAL_TIME_OFFSET = new Date().getTimezoneOffset() * 60000;
var HOUR_MILLIS = 3600000;
var MONTH_NAMES=new Array('January','February','March','April','May','June','July','August','September','October','November','December','Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec');
var DAY_NAMES=new Array('Sunday','Monday','Tuesday','Wednesday','Thursday','Friday','Saturday','Sun','Mon','Tue','Wed','Thu','Fri','Sat');

function DateFormat(aString) {
	this.format = format;
	this.parse = parse;
	this.parseField = parseField;
	this.validateString = validateString;
	this.isValidCharacter =isValidCharacter;
	this.getDateFrmFormat = getDateFrmFormat;
	this.isDate = isDate;

	this.formatDate = formatDate;
	this.getTimeOffset = getTimeOffset;

	var fields = ArrayUtils.getTokens(aString, DELIMITER0);
	this.dateFormat = fields[0];
	this.timeFormat = fields[1];
	this.pattern = this.dateFormat + " " + this.timeFormat;

	function formatDate(aDate, aDateFormat) {
		var dateFormat = aDateFormat || this.dateFormat;
		return this.format(aDate, dateFormat, true);
	}

	function getTimeOffset() {
		if ((typeof this.timeOffset) == "undefined") {
			this.timeOffset = (this.timeZone ? this.timeZone.offset : 0) + LOCAL_TIME_OFFSET;
		}

		return this.timeOffset;
	}

	function format(aDateTime, aDateTimeFormat, isDateOnly) {
		var htmlObj = null;
		var result = "";

		if ((typeof aDateTime) == "object") {
			htmlObj = aDateTime;
			if(htmlObj !=null) {
				aDateTime = this.parse(StringUtils.trim(htmlObj.value).toLowerCase(), isDateOnly);
			}

			if (aDateTime == null) {
				return;
			}
		}

		if (aDateTime) {
			var dateTimeFormat = aDateTimeFormat || this.dateFormat + " " + this.timeFormat;
			if (!isDateOnly) {
				aDateTime += this.getTimeOffset();
			}

			result = FormatUtils.formatDateTime(dateTimeFormat, new Date(aDateTime), this.timeZone.id);
		}

		if (htmlObj != null) {
			htmlObj.value = result;
		}

		return result;
	}

	function parse(aString, isDateOnly) {
		if (!aString) {
			return null;
		}

		var result;

		if(aString.match(/[dmy]{1}$/)) {
			var number = eval(aString.substring(0, aString.length - 1));
			if (number < 0) {
				number = 0;
			}

			var shortcut = aString.substring(aString.length - 1);

			var now = new Date();
			var newDate = now;
			if (shortcut == "d") {
				newDate = new Date(now.getTime() + number * 24 * HOUR_MILLIS);
			} else if (shortcut == "m") {
				newDate = new Date(FormatUtils.getYear(now) + number / 12, now.getMonth() + number % 12, now.getDate());
			} else if (shortcut == "y") {
				newDate = new Date(FormatUtils.getYear(now) + number, now.getMonth(), now.getDate());
			}

			result = newDate.getTime();
		} else {
			aString = this.validateString(aString, "/");
			aString = this.validateString(aString, "-");

			var pattern = this.pattern;
			if (aString.match(/\b\d{8}\b/)) {
				pattern = "yyyyMMdd";
			}
		
			var year = this.parseField(aString, "y", pattern);
			var month = this.parseField(aString, "M", pattern);
			var day = this.parseField(aString, "d", pattern);

			var hour = this.parseField(aString, "H", pattern);
			var minute = this.parseField(aString, "m", pattern);
			var second = this.parseField(aString, "s", pattern);

			if (year == null || month == null || day == null || hour == null || minute == null || second == null) {
				return null;
			}

			result = new Date(year, month, day, hour, minute, second).getTime();
		}

		if (!isDateOnly) {
			var estOffset=600000*300+7200000;
			//Bug fix 19446 . Setting  the  EST TimeZoneOffset
			if(window.EST_TIME_ZONE_STRING)
				estOffset=window.EST_TIME_ZONE_STRING;
			
			result+=estOffset;
			var userTZ=window.USER_TIME_ZONE_STRING;
			var fields = ArrayUtils.getTokens(userTZ, DELIMITER0);
	        var useroffset = eval(fields[1]);
			result+=useroffset;
			//result -= this.getTimeOffset();
		}

		return result;
	}

	function validateString(aString, separator) {
		aString = StringUtils.trim(aString);
		var result = "";

		var fields = ArrayUtils.getTokens(aString, separator);
		for (var i = 0; i < fields.length; i++) {
			var field = fields[i];
			if (field.length == 1 || field.indexOf(" ") == 1) {
				field = "0" + field;
			}

			if (i > 0) {
				result += separator;
			}

			result += field;
		}

		if (result == "") {
			result = aString;
		}

		return result;
	}

	function parseField(aString, symbol, aPattern) {
		var field = "";
		var result = 0;
		var pattern = aPattern || this.pattern;

		for (var i = 0; i < pattern.length; i++) {
			var ch = pattern.charAt(i);
			if (ch == symbol) {
				ch = aString.charAt(i);
				if (!this.isValidCharacter(ch)) {
					return null;
				}

				field += ch;
			}
		}

		if (symbol == "M") {
			if (field.length == 3) {
				result = ArrayUtils.getElementIndex(FormatUtils.MONTH_NAMES, field, true);
			} else {
				result = MathUtils.parseInt(field) - 1;
			}
		} else {
			result = MathUtils.parseInt(field);
		}

		return isNaN(result) ? 0 : result;
	}

	function isValidCharacter(ch, symbol, aPattern) {
		if (symbol == "M" && aPattern.indexOf("MMM") != -1) {
			if (!isNaN(ch)) {
				return false;
			}
		}

		return !isNaN(ch);
	}

	function isDate(val,format) {
	var date=getDateFromFormat(val,format);
	if (date==0) { return false; }
	return true;
	}

// ------------------------------------------------------------------
// Utility functions for parsing in getDateFromFormat()
// ------------------------------------------------------------------
function _isInteger(val) {
	var digits="1234567890";
	for (var i=0; i < val.length; i++) {
		if (digits.indexOf(val.charAt(i))==-1) { return false; }
		}
	return true;
	}
function _getInt(str,i,minlength,maxlength) {
	for (var x=maxlength; x>=minlength; x--) {
		var token=str.substring(i,i+x);
		if (token.length < minlength) { return null; }
		if (_isInteger(token)) { return token; }
		}
	return null;
	}
	
// ------------------------------------------------------------------
// getDateFromFormat( date_string , format_string )
//
// This function takes a date string and a format string. It matches
// If the date string matches the format string, it returns the 
// getTime() of the date. If it does not match, it returns 0.
// ------------------------------------------------------------------
function getDateFrmFormat(val) {
	val=val+"";
	format=this.dateFormat+"";
	var i_val=0;
	var i_format=0;
	var c="";
	var token="";
	var token2="";
	var x,y;
	var now=new Date();
	var year=now.getYear();
	var month=now.getMonth()+1;
	var date=1;
	var hh=now.getHours();
	var mm=now.getMinutes();
	var ss=now.getSeconds();
	var ampm="";
	
	while (i_format < format.length) {
		// Get next token from format string
		c=format.charAt(i_format);
		token="";
		while ((format.charAt(i_format)==c) && (i_format < format.length)) {
			token += format.charAt(i_format++);
			}
		// Extract contents of value based on format token
		if (token=="yyyy" || token=="yy" || token=="y") {
			if (token=="yyyy") { x=4;y=4; }
			if (token=="yy")   { x=2;y=2; }
			if (token=="y")    { x=2;y=4; }
			year=_getInt(val,i_val,x,y);
			if (year==null) { return 0; }
			i_val += year.length;
			if (year.length==2) {
				if (year > 70) { year=1900+(year-0); }
				else { year=2000+(year-0); }
				}
			}
		else if (token=="MMM"||token=="NNN"){
			month=0;
			for (var i=0; i<MONTH_NAMES.length; i++) {
				var month_name=MONTH_NAMES[i];
				if (val.substring(i_val,i_val+month_name.length).toLowerCase()==month_name.toLowerCase()) {
					if (token=="MMM"||(token=="NNN"&&i>11)) {
						month=i+1;
						if (month>12) { month -= 12; }
						i_val += month_name.length;
						break;
						}
					}
				}
			if ((month < 1)||(month>12)){return 0;}
			}
		else if (token=="EE"||token=="E"){
			for (var i=0; i<DAY_NAMES.length; i++) {
				var day_name=DAY_NAMES[i];
				if (val.substring(i_val,i_val+day_name.length).toLowerCase()==day_name.toLowerCase()) {
					i_val += day_name.length;
					break;
					}
				}
			}
		else if (token=="MM"||token=="M") {
			month=_getInt(val,i_val,token.length,2);
			if(month==null||(month<1)||(month>12)){return 0;}
			i_val+=month.length;}
		else if (token=="dd"||token=="d") {
			date=_getInt(val,i_val,token.length,2);
			if(date==null||(date<1)||(date>31)){return 0;}
			i_val+=date.length;}
		else if (token=="hh"||token=="h") {
			hh=_getInt(val,i_val,token.length,2);
			if(hh==null||(hh<1)||(hh>12)){return 0;}
			i_val+=hh.length;}
		else if (token=="HH"||token=="H") {
			hh=_getInt(val,i_val,token.length,2);
			if(hh==null||(hh<0)||(hh>23)){return 0;}
			i_val+=hh.length;}
		else if (token=="KK"||token=="K") {
			hh=_getInt(val,i_val,token.length,2);
			if(hh==null||(hh<0)||(hh>11)){return 0;}
			i_val+=hh.length;}
		else if (token=="kk"||token=="k") {
			hh=_getInt(val,i_val,token.length,2);
			if(hh==null||(hh<1)||(hh>24)){return 0;}
			i_val+=hh.length;hh--;}
		else if (token=="mm"||token=="m") {
			mm=_getInt(val,i_val,token.length,2);
			if(mm==null||(mm<0)||(mm>59)){return 0;}
			i_val+=mm.length;}
		else if (token=="ss"||token=="s") {
			ss=_getInt(val,i_val,token.length,2);
			if(ss==null||(ss<0)||(ss>59)){return 0;}
			i_val+=ss.length;}
		else if (token=="a") {
			if (val.substring(i_val,i_val+2).toLowerCase()=="am") {ampm="AM";}
			else if (val.substring(i_val,i_val+2).toLowerCase()=="pm") {ampm="PM";}
			else {return 0;}
			i_val+=2;}
		else {
			if (val.substring(i_val,i_val+token.length)!=token) {return 0;}
			else {i_val+=token.length;}
			}
		}
	// If there are any trailing characters left in the value, it doesn't match
	if (i_val != val.length) { return 0; }
	// Is date valid for month?
	if (month==2) {
		// Check for leap year
		if ( ( (year%4==0)&&(year%100 != 0) ) || (year%400==0) ) { // leap year
			if (date > 29){ return 0; }
			}
		else { if (date > 28) { return 0; } }
		}
	if ((month==4)||(month==6)||(month==9)||(month==11)) {
		if (date > 30) { return 0; }
		}
	// Correct hours value
	if (hh<12 && ampm=="PM") { hh=hh-0+12; }
	else if (hh>11 && ampm=="AM") { hh-=12; }
	var newdate=new Date(year,month-1,date,hh,mm,ss);
	return newdate.getTime();
	}

}

var userDateFormat = window.USER_DATE_FORMAT_STRING ? new DateFormat(USER_DATE_FORMAT_STRING) : null;

if (window.userTimeZone) {
	userDateFormat.timeZone = userTimeZone;
}
