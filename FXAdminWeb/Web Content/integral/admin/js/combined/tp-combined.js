function changeTPLSaveButton(flag){
   if(!flag){
     $j('#datasubmit').removeAttr('disabled');
     $j('#datasubmit').removeClass('b1').addClass('b1r');
   }
   else{
     $j('#datasubmit').prop('disabled', true);
     $j('#datasubmit').removeClass('b1r').addClass('b1');
     }
 }
/**
 * Reset the input elements back to original value on error
 *
 */
function resetToOriginalState(encryptedID)
{
	if( null != encryptedID )
	{
	   var tpOriginal = modifiedCollection.get(encryptedID);
	   $j("#longname_"+encryptedID).val(tpOriginal.getLongName());
       resetSelect('#DDE_'+encryptedID,tpOriginal.getDefaultDealingEntity());
	   resetSelect('#PB_'+encryptedID,tpOriginal.getPrimeBrokerOrganization());
	   resetSelectPB('#PBTP_'+encryptedID,tpOriginal,false);
       resetSelect('#PBC_'+encryptedID,tpOriginal.getPrimeBrokerCreditOrganization());
       resetSelectPB('#PBCTP_'+encryptedID,tpOriginal,true);
       resetSelect('#STREAM_'+encryptedID,tpOriginal.getStream());
       var isEnabled = tpOriginal.isPrimeBrokerCreditEnabled();
       if( isEnabled)
       {
           $j("#CreditUsed_"+encryptedID).prop('checked','checked');
       }
       else
       {
    	   $j("#CreditUsed_"+encryptedID).prop('checked','');  
       }
       if( tpOriginal.isPrimeBrokerSTPEnabled())
       {
           $j("#PBSTPEnabled_"+encryptedID).prop('checked','checked');
       }
       else
       {
           $j("#PBSTPEnabled_"+encryptedID).prop('checked','');  
       }
       if( tpOriginal.isPrimeBrokerCoverEnabled())
       {
           $j("#PBCoverEnabled_"+encryptedID).prop('checked','checked');
       }
       else
       {
           $j("#PBCoverEnabled_"+encryptedID).prop('checked','');  
       }
       
       resetExternalSystemIDFields(tpOriginal);
       resetSelect('#STPEnabled_'+encryptedID,tpOriginal.getSTPEnabled());
       resetSelect('#GenUSI_'+encryptedID,tpOriginal.getGenerateUSI());

	   modifiedCollection.remove(encryptedID,tpOriginal.getSTPEnabled());
	   mapTP.remove(encryptedID);
	   mapTP.put(encryptedID,tpOriginal);
	   modifiedCollection.remove(encryptedID);
	}
}
function resetExternalSystemIDFields(tradingParty)
{
	var keys = tradingParty.getExternalSystemIDKeys();
	$j.each(keys,function(index)
			{
		       var tmpKey = keys[index];
		       var tmpVal = tradingParty.getExternalSystemID(tmpKey);
		       $j("#"+tmpKey+"_"+tradingParty.getEncryptedID()).val(tmpVal);
			}); 
}
function takeBackup(encryptedID)
{
	if( modifiedCollection.get(encryptedID) == null )
	{
		var tp = mapTP.get(encryptedID).clone();
		//tp = Object.extend(tp, mapTP.get(encryptedID))
		modifiedCollection.put(encryptedID,tp);
	}
}

function resetSelectPB(selectID,tpOriginal,isCredit)
{
	   var value="";
	   var creditOriginal = "";
	   if(isCredit == false )
	   {
		   creditOriginal = tpOriginal.getPrimeBrokerTradingParty();
		   value = tpOriginal.getPrimeBrokerOrganization();
	   }
	   else
	   {
		   creditOriginal = tpOriginal.getPrimeBrokerCreditTradingParty();
		   value = tpOriginal.getPrimeBrokerCreditOrganization();
	   }
	   if( creditOriginal == "" )
	   {
        $j(selectID).val(creditOriginal);
        $j(selectID).selectOptions(creditOriginal);
	   }
	   else
	   {
		   updatePrimeBrokerOrganization(null,tpOriginal.getEncryptedID(),isCredit);
		   $j(selectID).val(creditOriginal);
	       $j(selectID).selectOptions(creditOriginal);
	   }
}
function updateStream(encryptedID,value)
{
	takeBackup(encryptedID);
    var tp = mapTP.get(encryptedID);
    tp.setStream(value);
    tp.update();
    changeTPLSaveButton();
    
}
function updateLongName(encryptedID,value)
{
    takeBackup(encryptedID);
    var tp = mapTP.get(encryptedID);
    tp.setLongName(value);
    tp.update();
    changeTPLSaveButton();
}
function updateDefaultDealingEntity(encryptedID)
{
    var selectedDDEntity = $j('#DDE_'+encryptedID).val();
    var tp = mapTP.get(encryptedID);
    if(selectedDDEntity.length==0 )
    {
        alert($j('#DDE_'+encryptedID +" :selected").text() + " is an invalid value");
        $j('#DDE_'+encryptedID ).val(tp.getDefaultDealingEntity());
    }
    else
    {
    	takeBackup(encryptedID);
    	tp.setDefaultDealingEntity(selectedDDEntity);
    	tp.update();
    }
    changeTPLSaveButton();
}
function updateExternalSystemID(externalSystemId, externalSystemIdValue, encryptedID)
{
    takeBackup(encryptedID);
    var tp = mapTP.get(encryptedID);
    if( tp.getExternalSystemID(externalSystemId) != externalSystemIdValue )
    {
        tp.setExternalSystemID(externalSystemId,externalSystemIdValue);
        tp.update();
    }
}

function updateSTPEnabled(encryptedID)
{
	takeBackup(encryptedID);
    var tp = mapTP.get(encryptedID);
	
	var selectOption = '#STPEnabled_'+encryptedID;
	tp.setSTPEnabled($j(selectOption).val());
	tp.update();
	changeTPLSaveButton();
}
function updatePrimeBrokerTradingParty(encryptedID,isCredit)
{
	 takeBackup(encryptedID);
	 var tp = mapTP.get(encryptedID);
	 var selectOption = '#PBTP_'+encryptedID;
	 if(isCredit)
	 {
		 selectOption = '#PBCTP_'+encryptedID
	 }
	 var selectedPrimeBrokerTP = $j(selectOption).val();
	 if( selectedPrimeBrokerTP.length > 0 )
	 {
		 if(isCredit)
		 {
			  tp.setPrimeBrokerCreditTradingParty(selectedPrimeBrokerTP);
              tp.update();
		 }
		 else
		 {
			  tp.setPrimeBrokerTradingParty(selectedPrimeBrokerTP);
              tp.update();
		 }
	 }
	 else
	 {
		 var currentValue = tp.getPrimeBrokerTradingParty();
		 if(isCredit)
		 {
			 currentValue = tp.getPrimeBrokerCreditTradingParty();
		 }
		 if( currentValue.length > 0 )
         {
              alert($j(selectOption +" :selected").text() + " is an invalid value");
              $j(selectOption).prop("selectedIndex",1);
              //TODO: set the first value on tp object
              // also need to handle the invaliad value selected for Pb
         }
	 }
	 changeTPLSaveButton();
}
function updatePBCreditEnabled(encryptedID)
{
    takeBackup(encryptedID);
    var tp = mapTP.get(encryptedID);
    var isChecked = $j("#CreditUsed_"+encryptedID).prop("checked");
    if(isChecked)
    {
    	tp.setPrimeBrokerCreditEnabled(true);
    }
    else
    {
    	tp.setPrimeBrokerCreditEnabled(false);
    }
    tp.update();
    changeTPLSaveButton();
}
function updatePBSTPEnabled(encryptedID)
{
    takeBackup(encryptedID);
    var tp = mapTP.get(encryptedID);
    var isChecked = $j("#PBSTPEnabled_"+encryptedID).prop("checked");
    if(isChecked)
    {
        tp.setPrimeBrokerSTPEnabled(true);
    }
    else
    {
        tp.setPrimeBrokerSTPEnabled(false);
    }
    tp.update();
    changeTPLSaveButton();
}
function updatePBCoverEnabled(encryptedID)
{
    takeBackup(encryptedID);
    var tp = mapTP.get(encryptedID);
    var isChecked = $j("#PBCoverEnabled_"+encryptedID).prop("checked");
    if(isChecked)
    {
        tp.setPrimeBrokerCoverEnabled(true);
    }
    else
    {
        tp.setPrimeBrokerCoverEnabled(false);
    }
    tp.update();
    changeTPLSaveButton();
}

function updatePBTradingParty(responseData)
{
       var tpEncryptedId = responseData.TradingParty.EncryptedObjectID;
       var selectionOption = "#PBTP_"+tpEncryptedId;
       var isCredit = responseData.CreditTP;
       if( isCredit)
       {
           selectionOption ="#PBCTP_"+tpEncryptedId;
       }
       var tp = mapTP.get(tpEncryptedId);
       $j(selectionOption).removeOption(/./);
       var data = responseData.PrimeBrokerTradingParties;
       $j(selectionOption).addOption("", "-----------------");
       $j.each(data,function(index)
        {
             $j(selectionOption).addOption(data[index], data[index]);
        });
        if(data.length<1) return;
        if(isCredit)
        {
            tp.setPrimeBrokerCreditTradingParty(data[0]);
        }
        else
        {
            tp.setPrimeBrokerTradingParty(data[0]);
        } 
        $j(selectionOption).prop("selectedIndex",1);
        changeTPLSaveButton();
}
function showErrorDialog(errorMessage)
{
    $j("#dialog").dialog('open');
    $j("#dialog").html(errorMessage);
    //resetToOriginalState();
}
function reportSuccess(data)
{
    if( data.error != null )
    {
    	 var tpFailureDATA = modifiedCollection.values;
         if( null != tpFailureDATA )
         {
             $j.each(tpFailureDATA, function (index)
                     {
                           resetToOriginalState(tpFailureDATA[index].getEncryptedID());
                     });
         }
         showErrorDialog( data.error);
         changeTPLSaveButton();
    }
    else
    {  changeTPLSaveButton(true);
		var status = "SUCCESS";
		if( data.STATUS != null )
		{
			status = data.STATUS;
		}
		if( status == "PARTIAL")
		{
			var tpFailureDATA = data.FAILURE;
			if( null != tpFailureDATA )
			{
				 $j.each(tpFailureDATA, function (index)
				 		{
				              resetToOriginalState(tpFailureDATA[index]);
						});
			}
            if (data.ERRORSTRING != null) {
                var errStr = "\n";
                var counter = 1;
                $j.each(data.ERRORSTRING,function(index,value){
                    errStr = errStr + "\n" + counter + ". " + value;
                    counter = counter + 1;
                });
                showErrorDialog("Failed to process the request partially, resetting to previous state: " + errStr);
            }
            else {
                showErrorDialog("Failed to process the request, resetting to previous state");
            }
		}
		if( status == "ROLLBACK")
        {
            var tpFailureDATA = modifiedCollection.values;
            if( null != tpFailureDATA )
            {
	            $j.each(tpFailureDATA, function (index)
	                    {
	                          resetToOriginalState(tpFailureDATA[index].getEncryptedID());
	                    });
            }
            showErrorDialog("Failed to process the request, resetting to previous state");
        }
		if( status == "SUCCESS")
		{
			modifiedCollection.clear();
			var arrData = mapTP.values;
			$j.each(arrData, function ()
               {
					if(this.isModified())
	                {
						this.setInitialized();
	                }
               });
		}
    }
}
function updatePrimeBrokerOrganization(url,encryptedID,isCredit)
{
    changeTPLSaveButton();
	takeBackup(encryptedID);
	var tp = mapTP.get(encryptedID);
	var selectID = '#PB_'+encryptedID;
	var selectIDPBT = '#PBTP_'+encryptedID;
	if(isCredit)
	{
		selectID = '#PBC_'+encryptedID;
		selectIDPBT = '#PBCTP_'+encryptedID;
	}
	var selectedPrimeBroker = $j(selectID).val();
	var oldPB =   tp.getPrimeBrokerOrganization();
	if(isCredit)
	{
		oldPB = tp.getPrimeBrokerCreditOrganization();
	}
	if( oldPB != selectedPrimeBroker ) 
	{
		if(isCredit)
	    {
			tp.setPrimeBrokerCreditOrganization(selectedPrimeBroker);
	    }
		else
		{
			tp.setPrimeBrokerOrganization(selectedPrimeBroker);
		}
	    if(selectedPrimeBroker.length>0)
	    {
	        $j.post(url,{service:"PBConfig",PrimeBrokerOrganization:selectedPrimeBroker,TradingParty_EncryptedObjectID:tp.getEncryptedID(),CreditPB:isCredit},updatePBTradingParty, "json");
	    }
	    else
	    {
	    	if(isCredit)
	        {
	    		tp.setPrimeBrokerCreditOrganization("");
	    		tp.setPrimeBrokerCreditTradingParty("");
	    		
	        }
	    	else
	    	{
                tp.setPrimeBrokerOrganization("");
	    		tp.setPrimeBrokerTradingParty("");
	    	}
	    	$j(selectIDPBT).prop("selectedIndex",0);
	    }
	    
	    tp.update();
	}
}
function updateReportingParty(encryptedID)
{
    takeBackup(encryptedID);
    var tp = mapTP.get(encryptedID);
    var reportingParty = $j("#ReportingParty_"+encryptedID).val();
    tp.setReportingParty(reportingParty);
    tp.update();
    changeTPLSaveButton();
}
function updateGenerateUSI(encryptedID)
{
    takeBackup(encryptedID);
    var tp = mapTP.get(encryptedID);
    var generateUSI = $j("#GenUSI_"+encryptedID).val();
    tp.setGenerateUSI(generateUSI);
    tp.update();
    changeTPLSaveButton();
}
function convertEntityArrayToJSONTradingParty(arrData)
{
    var numElements =  arrData.size();
    var data="{"
    for(var i=0;i<numElements; i++)
    {
        var entity  = arrData[i];
        if(entity.isModified())
        {
            data += "\"" + entity.getShortName()+"@" + entity.getNamespace()+"-"+entity.getLegalEntityOrganization()+"\":";
            data +=entity.toJSON();
        }
       
    }
    data+="}";
    return data;
}
function convertEntityArrayToJSON(arrData)
{
    var numElements =  arrData.size();
    var data="{"
    for(var i=0;i<numElements; i++)
    {
        var entity  = arrData[i];
        if(entity.isModified())
        {
            data += "\"" + entity.getShortName()+"@" + entity.getNamespace()+"\":";
            data +=entity.toJSON();
        }
       
    }
    data+="}";
    return data;
}
function initializeTradingPartyList()
{
	$j("#dialogLoading").hide().ajaxStart(function(){$j(this).dialog('open');}).ajaxComplete(function(){$j(this).dialog('close');});
    $j('#log').hide().ajaxError(function(e, xhr, settings, exception) 
    {
    	$j(this).show();
		$j(this).text(reportError(e, xhr, settings, exception));
    }).ajaxStart(function() 
      {
          $j(this).hide();
          $j(this).text('');
      });
    $j("#datasubmit").click(function(){
                var url = '/admin/BulkDataModificationService.do';
                var data = convertEntityArrayToJSONTradingParty(mapTP.values);
                $j.post(url,{service:"TradingParty",data:data},reportSuccess, "json");
                $j(this).prop("disabled", true);
            });
            /*.ajaxStart(function() 
        		   {
                       $j(this).prop("disabled", true);
                   }
                 ).ajaxError(
                 		  function() 
                          {
                 			   $j(this).prop("disabled", false);
                 		  }
                 ).ajaxSuccess(
                        function() 
	                    {
	                        $j(this).prop("disabled", true);
                            $j('#datasubmit').removeClass('b1r').addClass('b1');
	                    }
		              );*/

    $j('#dialog').dialog({
        modal: true,
        autoOpen: false,
        resizable: true,
        draggable: false,
        width: 400,
        height: 300,
        zIndex: 5000,
        title: 'FXInside: Error',
        buttons: { "Ok": function() { $j(this).dialog("close"); }},
    	dialogClass: 'fxi-error'
    });
    $j('#dialogLoading').dialog({
        modal: true,
        autoOpen: false,
        resizable: false,
        draggable: false,
        zIndex: 7000,
        dialogClass: 'fxi-message'
    });

    $j("#inactivate").bind( "click", 
            function()
            {
                if($j("input[id=selectedItems]:checked").length>0)
                {
                     //$j(":input:not(input[type=hidden])").prop("disabled","true");
                     //$j("select[name=tableSelection]").prop("disabled","");
                     //$j("input[id=selectedItems]:checked").prop("disabled","");
                     $j("#TableForm").prop("action","/admin/table/Workflow.do");
                     $j("input[name='event']").val("Inactivate");
                     $j("#TableForm").trigger("submit");
                }
                else
                {
                    alert("Please select a Trading Party to Inactivate");
                }
            });  
       
            $j("#checkAll").bind("click", function()
             {
                  $j("input[id=selectedItems]").prop('checked',$j("#checkAll").is(':checked'));
             });       
}


function radioClick(value)
{
  $j("input[name='showInactiveRelationship']").val(value);
  $j(":input:not(input[type=hidden])").prop("disabled","true");
  $j("select[name='tableSelection']").prop("disabled","");
  $j("#TableForm").prop("action","/admin/table/Search.do");
  $j("#TableForm").prop("method","post");
  $j("#TableForm").trigger("submit");
  return true;
}