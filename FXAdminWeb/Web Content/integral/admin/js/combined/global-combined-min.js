var DELIMITER0="~,";var DELIMITER1="!,";function AbstractCollection(elements){this.elementData=new Array();if(elements){for(var i=0;i<elements.length;i++){this.elementData[i]=elements[i];}}this.isCollection=true;this.size=size;this.isEmpty=isEmpty;this.add=add;this.addAll=addAll;this.remove=remove;this.removeAll=removeAll;this.clear=clear;this.contains=contains;this.toArray=toArray;function size(){return this.elementData.length;}function isEmpty(){return this.size()==0;}function add(anElement,anInstance){if((typeof anElement)=="undefined"){return false;}var theInstance=anInstance||this;theInstance.elementData[theInstance.elementData.length]=anElement;return true;}function addAll(aCollection){for(var i=0;i<aCollection.size();i++){this.add(aCollection.elementData[i]);}return true;}function remove(anElement){var index=ArrayUtils.getElementIndex(this.elementData,anElement);if(index==-1){return false;}this.elementData=ArrayUtils.removeElementByIndex(this.elementData,index);return true;}function removeAll(aCollection){for(var i=0;i<aCollection.size();i++){this.remove(aCollection.elementData[i]);}return true;}function clear(){this.elementData=new Array();}function contains(anElement){return ArrayUtils.getElementIndex(this.elementData,anElement)!=-1;}function toArray(){return this.elementData;}}ArrayList.prototype=new AbstractCollection();function ArrayList(elements){this.base=AbstractCollection;this.base(elements);this.set=set;this.get=get;function set(anIndex,anElement){this.elementData[anIndex]=anElement;}function get(anIndex){return this.elementData[anIndex];}}function ArrayUtilsC(){this.isArray=isArray;this.validateArray=validateArray;this.getTokens=getTokens;this.getElementIndex=getElementIndex;this.removeElementByIndex=removeElementByIndex;this.removeElements=removeElements;this.addElements=addElements;function isArray(anObj,checkLength){if((typeof anObj.length)!="undefined"&&!isNaN(anObj.length)){if(checkLength){if(anObj[0]){return true;}else{return false;}}return true;}return false;}function validateArray(aParameter){if((typeof aParameter)=="undefined"){return aParameter;}var anArray=aParameter;if((typeof aParameter)=="string"){anArray=new Array();anArray[0]=aParameter;}return anArray;}function getTokens(aString,delimiter,times){var index=-1;var tokens=new Array();var times=times||-1;var i=0;for(i=0;(index=aString.indexOf(delimiter))!=-1;i++){tokens[i]=aString.substring(0,index);if(index>=length(aString)-1){break;}aString=aString.substring(index+delimiter.length);if(times!=-1&&i>=times-1){i++;break;}}aString=StringUtils.trim(aString,delimiter);if(aString){tokens[i]=aString;}return tokens;}function getElementIndex(anArray,anElement,ignoreCase){for(var i=0;i<anArray.length;i++){var element=anArray[i];if(element==anElement||(ignoreCase&&element.toLowerCase()==anElement.toLowerCase())){return i;}}return -1;}function removeElementByIndex(anArray,index){var newArray=new Array();for(var i=0,j=0;i<anArray.length;i++){if(index!=i){newArray[j++]=anArray[i];}}return newArray;}function removeElements(anArray,index1,index2){var newArray=new Array();var length=anArray.length;for(var i=0,j=0;i<length;i++){if(i<index1||i>=index2){newArray[j++]=anArray[i];}}return newArray;}function addElements(anArray,elements){for(var i=0;i<elements.length;i++){anArray[anArray.length]=elements[i];}}}var ArrayUtils=new ArrayUtilsC();function getTokens(aString,delimiter,times){return ArrayUtils.getTokens(aString,delimiter,times);}function getElementIndex(anArray,anElement){return ArrayUtils.getElementIndex(anArray,anElement);}function removeElements(anArray,index1,index2){return ArrayUtils.removeElements(anArray,index1,index2);}function addElements(anArray,elements){ArrayUtils.addElements(anArray,elements);}function HashMap(keys,values){this.keys=keys||new Array();this.values=values||new Array();this.keySet=keySet;this.containsKey=containsKey;this.get=get;this.getByIndex=getByIndex;this.put=put;this.size=size;this.remove=remove;this.clear=clear;this.insert=insert;function keySet(){return this.keys;}function containsKey(aKey){return(this.get(aKey)!=null);}function get(key){for(var i=0;i<this.keys.length;i++){if(key==this.keys[i]){return this.values[i];}}return null;}function getByIndex(index){if(this.values.length-1<index){return null;}return this.values[index];}function insert(index,key,value){var oldLength=this.keys.length;for(var i=oldLength;i>index;i--){this.keys[i]=this.keys[i-1];this.values[i]=this.values[i-1];}this.keys[index]=key;this.values[index]=value;}function put(key,value){var index=ArrayUtils.getElementIndex(this.keys,key);if(index!=-1){var oldValue=this.values[index];this.values[index]=value;return oldValue;}this.keys[this.keys.length]=key;this.values[this.values.length]=value;return null;}function size(){return this.keys.length;}function remove(key){var index=ArrayUtils.getElementIndex(this.keys,key);if(index==-1){return null;}var value=this.values[index];this.keys=ArrayUtils.removeElementByIndex(this.keys,index);this.values=ArrayUtils.removeElementByIndex(this.values,index);}function clear(){this.keys=new Array();this.values=new Array();}}HashSet.prototype=new AbstractCollection();function HashSet(elements){this.base=AbstractCollection;this.base(elements);this.add=add;this.clone=clone;function add(anElement){if(this.contains(anElement)){return false;}return HashSet.prototype.add(anElement,this);}function clone(){var aSet=new HashSet();aSet.addAll(this);return aSet;}}function StringUtilsC(){this.getSubstring=getSubstring;this.evalString=evalString;this.setUrlProperty=setUrlProperty;this.getProperty=getProperty;this.trim=trim;this.length=length;function getSubstring(aString,flag1,flag2){var index1=aString.indexOf(flag1);if(index1==-1){return null;}var index2=aString.indexOf(flag2,index1+flag1.length);if(index2==-1){index2=aString.length;}return aString.substring(index1+flag1.length,index2);}function evalString(aString){if(!aString){return"";}return(aString=="null"?"":aString);}function setUrlProperty(aString,key,value,force){var keyStr=key+"=";var startIndex=aString.indexOf(keyStr);if(!aString||(startIndex==-1&&!force)){return aString;}if(aString.indexOf("?")==-1){return aString+"?"+key+"="+value;}if(startIndex==-1){return aString+"&"+key+"="+value;}var endIndex=aString.indexOf("&",startIndex);var result=aString.substring(0,startIndex)+keyStr+value;if(endIndex!=-1){result+=aString.substring(endIndex);}return result;}function getProperty(aString,aKey,aFlag,partialMatch){var flag=aFlag||"&";var keyStr=aKey+"=";var index1=aString.indexOf(keyStr);if(index1==-1){return null;}if(index1>0&&!partialMatch&&aString.substring(index1-1,index1).match(/[a-zA-Z]/)){return null;}var index2=aString.indexOf(flag,index1);if(index2==-1){index2=aString.length;}return aString.substring(index1+keyStr.length,index2);}function trim(aString,tail,aTrimLeadingFlag,aTrimTrailingFlag){var trimLeadingFlag=(typeof(aTrimLeadingFlag)=="undefined"?true:aTrimLeadingFlag);var trimTrailingFlag=(typeof(aTrimTrailingFlag)=="undefined"?true:aTrimTrailingFlag);if(length(aString)==0){return"";}if(!tail){return new String(aString).replace(/^\s*|\s*$/g,"");}var index=-1;while(trimLeadingFlag&&aString.indexOf(tail)==0){if(aString==tail){return"";}aString=aString.substring(tail.length);}while(trimTrailingFlag&&(index=aString.lastIndexOf(tail))==length(aString)-tail.length&&index!=-1){aString=aString.substring(0,index);}return aString;}function length(aString){if(aString&&aString.length){return aString.length;}else{return 0;}}}var StringUtils=new StringUtilsC();function getSubstring(aString,flag1,flag2){return StringUtils.getSubstring(aString,flag1,flag2);}function evalString(aString){return StringUtils.evalString(aString);}function setUrlProperty(aString,key,value,force){return StringUtils.setUrlProperty(aString,key,value,force);}function getProperty(aString,aKey,aFlag,partialMatch){return StringUtils.getProperty(aString,aKey,aFlag,partialMatch);}function trim(aString,tail){return StringUtils.trim(aString,tail);}function length(aString){return StringUtils.length(aString);}function HtmlUtilsC(){this.isIE=isIE;this.getValue=getValue;this.setValue=setValue;this.getElementById=getElementById;this.getElementsByTagName=getElementsByTagName;this.getElementByValue=getElementByValue;this.findWindowObject=findWindowObject;this.findAllWindowObjects=findAllWindowObjects;this.findAllFrameObjects=findAllFrameObjects;this.findSelectedRadioVal=findSelectedRadioVal;this.addValidWindowObject=addValidWindowObject;this.isAlive=isAlive;this.setAllValues=setAllValues;this.enableAllInputs=enableAllInputs;this.setDisplayEnabled=setDisplayEnabled;function isIE(){return navigator.appName=="Microsoft Internet Explorer";}function findSelectedRadioVal(name){var fields=document.getElementsByName(name);var selRadValue;for(var i=0;i<fields.length;i++){if(fields[i].checked){selRadValue=fields[i].value;return selRadValue;}}return"";}function getValue(anObj,propName){if((typeof anObj)=="string"){anObj=this.getElementById(anObj);}if(!anObj){return null;}if(!anObj.type){return this.isIE()?anObj.innerHTML:"";}if(anObj.type.indexOf("select")==0){if(anObj.selectedIndex>=0){var option=anObj.options[anObj.selectedIndex];return propName=="text"?option.text:option.value;}else{return"";}}else{if(anObj.type=="radio"){var radios=this.getElementsByTagName("input","radio");for(var i=0;i<radios.length;i++){var radio=radios[i];if(radio.checked){if(radio.name==anObj.name){return radio.value;}}}}else{if(anObj.type.indexOf("checkbox")==0){return anObj.checked;}else{if((typeof anObj.value)!="undefined"){return anObj.value;}}}}}function setDisplayEnabled(anObj,isEnabled,aWnd){var wnd=aWnd||window;if((typeof anObj)=="string"){anObj=this.getElementById(anObj,wnd);}if(anObj){anObj.style.display=isEnabled?"":"none";}}function setValue(anObj,aValue){if((typeof anObj)=="string"){anObj=this.getElementById(anObj);}if((typeof anObj)=="undefined"){return;}if(anObj.type&&(anObj.type.indexOf("select")==0)){for(var i=0;i<anObj.options.length;i++){if(anObj.options[i].value==aValue){anObj.selectedIndex=i;}}}else{if(anObj.type&&(anObj.type.indexOf("checkbox")==0||anObj.type=="radio")){anObj.checked=eval(aValue);}else{if((typeof anObj.value)!="undefined"){anObj.value=aValue;}else{if(this.isIE()){anObj.innerHTML=aValue;}else{if(anObj.document&&anObj.document.write){anObj.document.write(aValue);anObj.document.close();}}}}}}function getElementById(id,aWnd,aIsIeFlag){var wnd=aWnd||window;var element=null;var isIeFlag=(typeof aIsIeFlag)=="undefined"?this.isIE():aIsIeFlag;if(isIeFlag){element=wnd.document.getElementById(id);if(!element){element=wnd.document.frames[id];}}else{element=wnd.document.forms[0].elements[id];if(!element&&wnd.document.layers){element=wnd.document.layers[id];}}return element;}function getElementsByTagName(tagName,aType,aWnd){var wnd=aWnd||window;var rawResult=new Array();if(this.isIE()){rawResult=wnd.document.getElementsByTagName(tagName);}else{if(tagName=="input"){rawResult=wnd.document.forms[0].elements;}}var result=new HashSet();for(var i=0;i<rawResult.length;i++){var element=rawResult[i];if(!aType||element.type==aType){result.add(element);}}return result.toArray();}function getElementByValue(aValue){var elements=window.document.getElementsByTagName("input");for(var i=0;i<elements.length;i++){var element=elements[i];if(element.value==aValue){return element;}}return null;}function setAllValues(anId,aValue){var elements=document.forms[0].elements;for(var i=0;i<elements.length;i++){var obj=elements[i];if(obj.name==anId){setValue(obj,aValue);}}}function enableAllInputs(anEnabledFlag,aWnd,inputTypes){var disabled=anEnabledFlag?false:true;var wnd=aWnd||window;if((typeof inputTypes)=="string"){inputTypes=new ArrayList([inputTypes]);}var elements=wnd.document.forms[0].elements;for(var i=0;i<elements.length;i++){var element=elements[i];if(!inputTypes||inputTypes.contains(element.type)){element.disabled=disabled;}}if(!inputTypes){wnd.disabled=disabled;}}function findAllWindowObjects(objName,aWnd){var objs=new ArrayList();var wnd=aWnd||window.top;try{objs.addAll(this.findAllFrameObjects(objName,wnd));}catch(e){}if(wnd.opener){try{objs.addAll(this.findAllFrameObjects(objName,wnd.opener.top));}catch(e){}}return objs;}function findAllFrameObjects(objName,aWnd){var objs=new ArrayList();if(!aWnd){return objs;}this.addValidWindowObject(objs,aWnd,objName);for(var i=0;i<aWnd.frames.length;i++){try{objs.addAll(this.findAllFrameObjects(objName,aWnd.frames[i]));}catch(e){}}if(aWnd.document.frames){for(var i=0;i<aWnd.document.frames.length;i++){try{objs.addAll(this.findAllFrameObjects(objName,aWnd.document.frames[i]));}catch(e){}}}return objs;}function findWindowObject(objName,aWnd){var objs=this.findAllWindowObjects(objName,aWnd);return objs.size()>0?objs.get(0):null;}function addValidWindowObject(objs,wnd,objName){if(!wnd||wnd.closed||wnd.disabled){return;}var obj=eval("wnd."+objName);if(obj){objs.add(obj);}}function isAlive(anObj){if(!anObj.window||anObj.window.closed||anObj.window.disabled){return false;}return anObj==eval("anObj.window."+anObj.name);}}var HtmlUtils=new HtmlUtilsC();function IdcWindow(name){this.base=IdcObject;this.base(name||"idcWindow");this.windowObjects=new HashSet();this.onLoadScripts=new HashSet();this.onUnloadScripts=new HashSet();this.onSubmitScripts=new HashSet();this.registerWindowObject=registerWindowObject;this.registerOnLoadScript=registerOnLoadScript;this.registerOnUnloadScript=registerOnUnloadScript;this.registerOnSubmitScript=registerOnSubmitScript;this.executeRegisteredScripts=executeRegisteredScripts;this.onLoad=onLoad;this.onUnload=onUnload;this.onSubmit=onSubmit;this.updateWindowObjects=updateWindowObjects;function registerWindowObject(obj){if(obj){this.windowObjects.add(obj);}}function registerOnLoadScript(aScript){this.onLoadScripts.add(aScript);}function registerOnUnloadScript(aScript){this.onUnloadScripts.add(aScript);}function registerOnSubmitScript(aScript){this.onSubmitScripts.add(aScript);}function onLoad(){this.updateWindowObjects("onLoad");this.executeRegisteredScripts(this.onLoadScripts);this.loaded=true;}function onUnload(){this.updateWindowObjects("onUnload");this.executeRegisteredScripts(this.onUnloadScripts);return true;}function onSubmit(){this.updateWindowObjects("onSubmit");this.executeRegisteredScripts(this.onSubmitScripts);return true;}function executeRegisteredScripts(scriptsSet){var scripts=scriptsSet.toArray();for(var i=0;i<scripts.length;i++){var script=scripts[i];eval(script);}}function updateWindowObjects(functionName){var windowObjs=this.windowObjects.toArray();for(var i=0;i<windowObjs.length;i++){var windowObj=windowObjs[i];if(eval("windowObj."+functionName)){eval("windowObj."+functionName+"()");}}}}var idcWindow=new IdcWindow();function IdcObject(name){this.name=name;this.window=window;this.instanceId=new Date().getTime();}function ElementBindingC(){this.bindings=new HashMap();this.registeredChanges=new ArrayList();this.addBinding=addBinding;this.change=change;this.doChange=doChange;this.updateDestObj=updateDestObj;this.registerChange=registerChange;this.registerCheckboxChange=registerCheckboxChange;this.updateObservers=updateObservers;this.onLoad=onLoad;function addBinding(sourceId,aBindingParameters){var bindingParameters=(typeof aBindingParameters)=="string"?new BindingParameters(aBindingParameters):aBindingParameters;var bindingParametersList=this.bindings.get(sourceId);if(!bindingParametersList){bindingParametersList=new ArrayList();}bindingParametersList.add(bindingParameters);this.bindings.put(sourceId,bindingParametersList);this.registerChange(sourceId);}function change(aSourceIdObj){var sourceObj=aSourceIdObj;if((typeof aSourceIdObj)=="string"){var bindingParametersList=this.bindings.get(aSourceIdObj);if(bindingParametersList&&bindingParametersList.size()>0){var bindingParameters=bindingParametersList.get(0);sourceObj=bindingParameters.validateObj(aSourceIdObj,bindingParameters.sourceWindow);}}this.doChange(sourceObj);}function registerChange(aSourceId){this.registeredChanges.add(aSourceId);}function registerCheckboxChange(hiddenInputName,checkboxName){this.addBinding(hiddenInputName,checkboxName);this.addBinding(checkboxName,new BindingParameters(hiddenInputName,window,window,"checked"));this.registerChange(hiddenInputName);}function doChange(sourceObj,aStartSourceId){if(!sourceObj){return;}var sourceId=(sourceObj.id||sourceObj.name);if(sourceId==aStartSourceId){return;}var startSourceId=aStartSourceId||sourceId;this.updateObservers(sourceObj);var bindingParametersList=this.bindings.get(sourceId);for(var i=0;bindingParametersList&&i<bindingParametersList.size();i++){var bindingParameters=bindingParametersList.get(i);this.updateDestObj(sourceObj,bindingParameters);this.doChange(bindingParameters.destObj,startSourceId);}}function updateDestObj(sourceObj,bindingParameters){var destObj=bindingParameters.destObj;var customMethod=bindingParameters.customMethod;if(customMethod=="detailChild"&&bindingParameters.sourceWindow!=bindingParameters.destWindow&&!sourceObj.initialized){HtmlUtils.setValue(sourceObj,HtmlUtils.getValue(destObj));sourceObj.initialized=true;return;}if(customMethod.indexOf("checked")==0){var enabled=sourceObj.type=="radio"?sourceObj.value==customMethod.substring(8):sourceObj.checked;if(customMethod.indexOf("checked_")==0){destObj.disabled=!enabled;}else{destObj.value=new String(enabled);}}else{var value=HtmlUtils.getValue(sourceObj,customMethod);HtmlUtils.setValue(destObj,value);}if(customMethod.charAt(customMethod.length-1)==")"){eval(customMethod);}}function updateObservers(anElement){var observers=anElement.observers?anElement.observers.toArray():null;if(observers==null){return;}for(var i=0;i<observers.length;i++){var observer=observers[i];observer.focus();observer.blur();}}function onLoad(){for(var i=0;i<this.registeredChanges.size();i++){var bindingParametersList=this.bindings.get(this.registeredChanges.get(i));for(var j=0;bindingParametersList&&j<bindingParametersList.size();j++){var bindingParameters=bindingParametersList.get(j);bindingParameters.init();}}for(var i=0;i<this.registeredChanges.size();i++){this.change(this.registeredChanges.get(i));}}}var elementBinding=new ElementBindingC();function BindingParameters(aDestId,aSourceWindow,aDestWindow,aCustomMethod){this.base=IdcObject;this.base();this.isBindingParameter=true;this.sourceWindow=aSourceWindow||window;this.destWindow=aDestWindow||window;this.customMethod=aCustomMethod||"value";this.destId=aDestId;this.destObj=null;this.init=init;this.validateObj=validateObj;function init(){this.destObj=this.validateObj(this.destId,this.destWindow);}function validateObj(anIdObj,aWindow){var anObj=anIdObj;if((typeof anIdObj)=="string"){anObj=HtmlUtils.getElementById(anIdObj,aWindow);if(anObj){anObj.id=anIdObj;}}return anObj;}}function ObjectChangeListener(aName,anObjectChangeHiddenName){this.base=IdcObject;this.base(aName);this.checkList=new HashSet();this.register=register;this.validate=validate;this.setOriginalValue=setOriginalValue;this.checkValueChange=checkValueChange;this.isArray=isArray;this.onLoad=onLoad;this.onSubmit=onSubmit;this.objectChangeHiddenName=anObjectChangeHiddenName||"workflowMessage.objectChanged";function register(anIdObj){this.checkList.add(anIdObj);}function onLoad(){this.validate(true);}function onSubmit(){this.validate(false);}function validate(isOnLoad){var ids=this.checkList.toArray();for(var i=0;i<ids.length;i++){var htmlObj=HtmlUtils.getElementById(ids[i],window,false);if(htmlObj){if(isOnLoad){this.setOriginalValue(htmlObj);}else{if(this.checkValueChange(htmlObj)){HtmlUtils.setValue(this.objectChangeHiddenName,"true");break;}}}}}function setOriginalValue(anObj){if((anObj.type)&&(anObj.type=="select-multiple")){for(var i=0;i<anObj.options.length;i++){anObj.options[i].originalValue=anObj.options[i].value;}}else{if(this.isArray(anObj)){for(var i=0;i<anObj.length;i++){if(anObj[i].checked==undefined){anObj[i].originalValue=anObj[i].value;}else{anObj[i].originalValue=anObj[i].checked;}}}else{anObj.originalValue=HtmlUtils.getValue(anObj);}}}function isArray(anObj){return(anObj.type&&anObj.type.indexOf("select")==0)?false:ArrayUtils.isArray(anObj,true);}function checkValueChange(anObj){if(anObj.type=="select-multiple"){for(var i=0;i<anObj.options.length;i++){if(anObj.options[i].originalValue!=anObj.options[i].value){return true;}}}else{if(this.isArray(anObj)){for(var i=0;i<anObj.length;i++){if(anObj[i].checked==undefined){if(anObj[i].originalValue!=anObj[i].value){return true;}}else{if(anObj[i].originalValue!=anObj[i].checked){return true;}}}}else{return anObj.originalValue!=HtmlUtils.getValue(anObj);}}return false;}}objectChangeListener=new ObjectChangeListener("objectChangeListener");function TableActions(){this.doCheckOrUncheckAll=doCheckOrUncheckAll;this.pageAtRow=pageAtRow;this.sortOnColumn=sortOnColumn;this.search=search;this.searchClear=searchClear;this.searchOn=searchOn;function getTableForm(){var tableForm=document.forms.TableForm||document.getElementById("TableForm");if(undefined==tableForm||null==tableForm){tableForm=document.forms[0];}return tableForm;}function doCheckOrUncheckAll(){var tableForm=getTableForm();for(var j=1;j<tableForm.elements.length;j++){if(tableForm.elements[j].name.indexOf("selectedItems")==0){tableForm.elements[j].checked=tableForm.elements.checkAll.checked;}}}function pageAtRow(startRowId,newRowStart){var startRow=document.getElementById(startRowId);if(null==startRow){startRow=document.getElementsByName(startRowId)[0];}startRow.value=newRowStart;getTableForm().submit();}function sortOnColumn(startRowId,colummSortedId,sortOrderId,column,columnAllowingNullVal){var startRow=document.getElementById(startRowId);if(null==startRow){startRow=document.getElementsByName(startRowId)[0];}var columnSorted=document.getElementById(colummSortedId);if(null==columnSorted){columnSorted=document.getElementsByName(columnSorted)[0];}var sortOrder=document.getElementById(sortOrderId);if(null==sortOrderId){sortOrderId=document.getElementsByName(sortOrderId)[0];}try{var columnAllowingNull=document.getElementsByName("TableForm_columnSortedAllowingNull")[0];columnAllowingNull.value=columnAllowingNullVal;}catch(e){}try{if(validate()==false){return;}}catch(e){}startRow.value=1;if(columnSorted.value==column){if(sortOrder.value=="ASC"){sortOrder.value="DSC";}else{sortOrder.value="ASC";}}else{columnSorted.value=column;sortOrder.value="DSC";}getTableForm().submit();}function search(startRowId,containsId,searchValue){var startRow=document.getElementById(startRowId);if(null==startRow){startRow=document.getElementsByName(startRowId)[0];}var contains=document.getElementById(containsId);if(null==contains){contains=document.getElementsByName(containsId)[0];}if(searchValue==undefined){if(contains!=undefined&&contains.value==""){contains.disabled=true;}}else{contains.value=searchValue;}startRow.value=1;getTableForm().submit();}function searchClear(startRowId,containsId){var startRow=document.getElementById(startRowId);if(null==startRow){startRow=document.getElementsByName(startRowId)[0];}var contains=document.getElementById(containsId);if(null==contains){contains=document.getElementsByName(containsId)[0];}contains.disabled=true;startRow.value=1;getTableForm().submit();}function searchOn(startRowId,searchPropertyId,containsId,searchValue){var startRow=document.getElementById(startRowId);if(null==startRow){startRow=document.getElementsByName(startRowId)[0];}var contains=document.getElementById(containsId);if(null==contains){contains=document.getElementsByName(containsId)[0];}var searchProperty=document.getElementById(searchPropertyId);if(null==searchProperty){searchProperty=document.getElementsByName(searchPropertyId)[0];}startRow.value=1;contains.value=searchValue;getTableForm().submit();}}var tableActions=new TableActions();function doCheckOrUncheckAll(){tableActions.doCheckOrUncheckAll();}function pageAtRow(startRowId,newRowStart){tableActions.pageAtRow(startRowId,newRowStart);}function sortOnColumn(startRowId,colummSortedId,sortOrderId,column,columnAllowingNull){tableActions.sortOnColumn(startRowId,colummSortedId,sortOrderId,column,columnAllowingNull);}function search(startRowId,containsId,searchValue){tableActions.search(startRowId,containsId,searchValue);}function searchClear(startRowId,containsId){tableActions.searchClear(startRowId,containsId);}function searchOn(startRowId,searchPropertyId,containsId,searchValue){tableActions.searchOn(startRowId,searchPropertyId,containsId,searchValue);}function doShowActive(showActive){document.forms[0].startRow.value=1;document.forms[0].displayActive.value=showActive;document.forms[0].submit();}function doActivateDeactivate(anAction,postToURL){document.forms[0].userAction.value=anAction;document.forms[0].action=postToURL;document.forms[0].submit();}function doInactivate(tableForm,statusId){var status=document.getElementById(statusId);if(status===null){status="active";}else{status.value="active";}document.forms[tableForm].action="/admin/table/Workflow.do";document.forms[tableForm].event.value="Inactivate";document.forms[tableForm].submit();}function doActivate(tableForm,statusId){var status=document.getElementById(statusId);if(status===null){status="inactive";}else{status.value="inactive";}document.forms[tableForm].action="/admin/table/Workflow.do";document.forms[tableForm].event.value="Activate";document.forms[tableForm].submit();}function setupForm(tableForm,statusId,eventValue){var status=document.getElementById(statusId);status.value="new";document.forms[tableForm].action="/admin/table/Workflow.do";document.forms[tableForm].event.value=eventValue;}function TimeZone(aString){var fields=ArrayUtils.getTokens(aString,DELIMITER0);this.id=fields[0];this.offset=eval(fields[1]);}var userTimeZone=window.USER_TIME_ZONE_STRING?new TimeZone(USER_TIME_ZONE_STRING):null;var LOCAL_TIME_OFFSET=new Date().getTimezoneOffset()*60000;var HOUR_MILLIS=3600000;var MONTH_NAMES=new Array("January","February","March","April","May","June","July","August","September","October","November","December","Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec");var DAY_NAMES=new Array("Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sun","Mon","Tue","Wed","Thu","Fri","Sat");function DateFormat(aString){this.format=format;this.parse=parse;this.parseField=parseField;this.validateString=validateString;this.isValidCharacter=isValidCharacter;this.getDateFrmFormat=getDateFrmFormat;this.isDate=isDate;this.formatDate=formatDate;this.getTimeOffset=getTimeOffset;var fields=ArrayUtils.getTokens(aString,DELIMITER0);this.dateFormat=fields[0];this.timeFormat=fields[1];this.pattern=this.dateFormat+" "+this.timeFormat;function formatDate(aDate,aDateFormat){var dateFormat=aDateFormat||this.dateFormat;return this.format(aDate,dateFormat,true);}function getTimeOffset(){if((typeof this.timeOffset)=="undefined"){this.timeOffset=(this.timeZone?this.timeZone.offset:0)+LOCAL_TIME_OFFSET;}return this.timeOffset;}function format(aDateTime,aDateTimeFormat,isDateOnly){var htmlObj=null;var result="";if((typeof aDateTime)=="object"){htmlObj=aDateTime;if(htmlObj!=null){aDateTime=this.parse(StringUtils.trim(htmlObj.value).toLowerCase(),isDateOnly);}if(aDateTime==null){return;}}if(aDateTime){var dateTimeFormat=aDateTimeFormat||this.dateFormat+" "+this.timeFormat;if(!isDateOnly){aDateTime+=this.getTimeOffset();}result=FormatUtils.formatDateTime(dateTimeFormat,new Date(aDateTime),this.timeZone.id);}if(htmlObj!=null){htmlObj.value=result;}return result;}function parse(aString,isDateOnly){if(!aString){return null;}var result;if(aString.match(/[dmy]{1}$/)){var number=eval(aString.substring(0,aString.length-1));if(number<0){number=0;}var shortcut=aString.substring(aString.length-1);var now=new Date();var newDate=now;if(shortcut=="d"){newDate=new Date(now.getTime()+number*24*HOUR_MILLIS);}else{if(shortcut=="m"){newDate=new Date(FormatUtils.getYear(now)+number/12,now.getMonth()+number%12,now.getDate());}else{if(shortcut=="y"){newDate=new Date(FormatUtils.getYear(now)+number,now.getMonth(),now.getDate());}}}result=newDate.getTime();}else{aString=this.validateString(aString,"/");aString=this.validateString(aString,"-");var pattern=this.pattern;if(aString.match(/\b\d{8}\b/)){pattern="yyyyMMdd";}var year=this.parseField(aString,"y",pattern);var month=this.parseField(aString,"M",pattern);var day=this.parseField(aString,"d",pattern);var hour=this.parseField(aString,"H",pattern);var minute=this.parseField(aString,"m",pattern);var second=this.parseField(aString,"s",pattern);if(year==null||month==null||day==null||hour==null||minute==null||second==null){return null;}result=new Date(year,month,day,hour,minute,second).getTime();}if(!isDateOnly){var estOffset=600000*300+7200000;if(window.EST_TIME_ZONE_STRING){estOffset=window.EST_TIME_ZONE_STRING;}result+=estOffset;var userTZ=window.USER_TIME_ZONE_STRING;var fields=ArrayUtils.getTokens(userTZ,DELIMITER0);var useroffset=eval(fields[1]);result+=useroffset;}return result;}function validateString(aString,separator){aString=StringUtils.trim(aString);var result="";var fields=ArrayUtils.getTokens(aString,separator);for(var i=0;i<fields.length;i++){var field=fields[i];if(field.length==1||field.indexOf(" ")==1){field="0"+field;}if(i>0){result+=separator;}result+=field;}if(result==""){result=aString;}return result;}function parseField(aString,symbol,aPattern){var field="";var result=0;var pattern=aPattern||this.pattern;for(var i=0;i<pattern.length;i++){var ch=pattern.charAt(i);if(ch==symbol){ch=aString.charAt(i);if(!this.isValidCharacter(ch)){return null;}field+=ch;}}if(symbol=="M"){if(field.length==3){result=ArrayUtils.getElementIndex(FormatUtils.MONTH_NAMES,field,true);}else{result=MathUtils.parseInt(field)-1;}}else{result=MathUtils.parseInt(field);}return isNaN(result)?0:result;}function isValidCharacter(ch,symbol,aPattern){if(symbol=="M"&&aPattern.indexOf("MMM")!=-1){if(!isNaN(ch)){return false;}}return !isNaN(ch);}function isDate(val,format){var date=getDateFromFormat(val,format);if(date==0){return false;}return true;}function _isInteger(val){var digits="1234567890";for(var i=0;i<val.length;i++){if(digits.indexOf(val.charAt(i))==-1){return false;}}return true;}function _getInt(str,i,minlength,maxlength){for(var x=maxlength;x>=minlength;x--){var token=str.substring(i,i+x);if(token.length<minlength){return null;}if(_isInteger(token)){return token;}}return null;}function getDateFrmFormat(val){val=val+"";format=this.dateFormat+"";var i_val=0;var i_format=0;var c="";var token="";var token2="";var x,y;var now=new Date();var year=now.getYear();var month=now.getMonth()+1;var date=1;var hh=now.getHours();var mm=now.getMinutes();var ss=now.getSeconds();var ampm="";while(i_format<format.length){c=format.charAt(i_format);token="";while((format.charAt(i_format)==c)&&(i_format<format.length)){token+=format.charAt(i_format++);}if(token=="yyyy"||token=="yy"||token=="y"){if(token=="yyyy"){x=4;y=4;}if(token=="yy"){x=2;y=2;}if(token=="y"){x=2;y=4;}year=_getInt(val,i_val,x,y);if(year==null){return 0;}i_val+=year.length;if(year.length==2){if(year>70){year=1900+(year-0);}else{year=2000+(year-0);}}}else{if(token=="MMM"||token=="NNN"){month=0;for(var i=0;i<MONTH_NAMES.length;i++){var month_name=MONTH_NAMES[i];if(val.substring(i_val,i_val+month_name.length).toLowerCase()==month_name.toLowerCase()){if(token=="MMM"||(token=="NNN"&&i>11)){month=i+1;if(month>12){month-=12;}i_val+=month_name.length;break;}}}if((month<1)||(month>12)){return 0;}}else{if(token=="EE"||token=="E"){for(var i=0;i<DAY_NAMES.length;i++){var day_name=DAY_NAMES[i];if(val.substring(i_val,i_val+day_name.length).toLowerCase()==day_name.toLowerCase()){i_val+=day_name.length;break;}}}else{if(token=="MM"||token=="M"){month=_getInt(val,i_val,token.length,2);if(month==null||(month<1)||(month>12)){return 0;}i_val+=month.length;}else{if(token=="dd"||token=="d"){date=_getInt(val,i_val,token.length,2);if(date==null||(date<1)||(date>31)){return 0;}i_val+=date.length;}else{if(token=="hh"||token=="h"){hh=_getInt(val,i_val,token.length,2);if(hh==null||(hh<1)||(hh>12)){return 0;}i_val+=hh.length;}else{if(token=="HH"||token=="H"){hh=_getInt(val,i_val,token.length,2);if(hh==null||(hh<0)||(hh>23)){return 0;}i_val+=hh.length;}else{if(token=="KK"||token=="K"){hh=_getInt(val,i_val,token.length,2);if(hh==null||(hh<0)||(hh>11)){return 0;}i_val+=hh.length;}else{if(token=="kk"||token=="k"){hh=_getInt(val,i_val,token.length,2);if(hh==null||(hh<1)||(hh>24)){return 0;}i_val+=hh.length;hh--;}else{if(token=="mm"||token=="m"){mm=_getInt(val,i_val,token.length,2);if(mm==null||(mm<0)||(mm>59)){return 0;}i_val+=mm.length;}else{if(token=="ss"||token=="s"){ss=_getInt(val,i_val,token.length,2);if(ss==null||(ss<0)||(ss>59)){return 0;}i_val+=ss.length;}else{if(token=="a"){if(val.substring(i_val,i_val+2).toLowerCase()=="am"){ampm="AM";}else{if(val.substring(i_val,i_val+2).toLowerCase()=="pm"){ampm="PM";}else{return 0;}}i_val+=2;}else{if(val.substring(i_val,i_val+token.length)!=token){return 0;}else{i_val+=token.length;}}}}}}}}}}}}}}if(i_val!=val.length){return 0;}if(month==2){if(((year%4==0)&&(year%100!=0))||(year%400==0)){if(date>29){return 0;}}else{if(date>28){return 0;}}}if((month==4)||(month==6)||(month==9)||(month==11)){if(date>30){return 0;}}if(hh<12&&ampm=="PM"){hh=hh-0+12;}else{if(hh>11&&ampm=="AM"){hh-=12;}}var newdate=new Date(year,month-1,date,hh,mm,ss);return newdate.getTime();}}var userDateFormat=window.USER_DATE_FORMAT_STRING?new DateFormat(USER_DATE_FORMAT_STRING):null;if(window.userTimeZone){userDateFormat.timeZone=userTimeZone;}