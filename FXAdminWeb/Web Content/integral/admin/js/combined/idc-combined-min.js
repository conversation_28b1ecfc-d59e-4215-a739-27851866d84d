var Entity=Class.create({initialize:function(encryptedID,namespace,status){this.encryptedID=encryptedID;this.namespace=namespace;this.status=status;this.setInitialized();},getNamespace:function(){return this.namespace;},getEncryptedID:function(){return this.encryptedID;},getStatus:function(){return this.status;},isModified:function(){return this.modified;},update:function(){this.modified=true;},setInitialized:function(){this.modified=false;},clone:function(){var ent=new Entity(this.encryptedID,this.namespace,this.status);return ent;},toString:function(){return'"EncryptedID":"'+this.getEncryptedID()+'","Namespace":"'+this.getNamespace()+'","Status":"'+this.getStatus();},toJSON:function(){return"{"+this.toString()+"}";}});var NamedEntity=Class.create(Entity,{initialize:function($super,encryptedID,namespace,status,shortName){$super(encryptedID,namespace,status);this.shortName=shortName;this.longName="";this.externalSystemIDS=new HashMap();},getShortName:function(){return this.shortName;},setLongName:function(longName){this.longName=longName;},getLongName:function(){return this.longName;},addExternalSystemID:function(key,value){if(this.externalSystemIDS.get(key)===null){this.externalSystemIDS.put(key,value);}},setExternalSystemID:function(key,value){this.externalSystemIDS.put(key,value);},getExternalSystemIDs:function(){return this.externalSystemIDS;},getExternalSystemIDKeys:function(){return this.externalSystemIDS.keys;},getExternalSystemIDValues:function(){return this.externalSystemIDS.values;},getExternalSystemID:function(key){return this.externalSystemIDS.get(key);},clone:function(){var ne=new NamedEntity(this.getEncryptedID(),this.getNamespace(),this.getStatus(),this.getShortName());var keys=this.getExternalSystemIDKeys();for(var i=0;i<keys.size;i++){ne.setExternalSystemID(keys[i],this.getExternalSystemID(keys[i]));}return ne;},toStringExternalSystemID:function(){var size=this.externalSystemIDS.size();var tmpVal="";if(size>0){var keys=this.externalSystemIDS.keySet();for(var i=0;i<keys.length;i++){var key=keys[i];var value=this.externalSystemIDS.get(key);tmpVal=tmpVal+'"'+key+'":"'+value+'"';if(i<keys.length-1){tmpVal=tmpVal+",";}}}return tmpVal;},toString:function($super){return $super()+'","ShortName":"'+this.getShortName()+'","LongName":"'+this.getLongName();}});var Organization=Class.create(NamedEntity,{initialize:function($super,encryptedID,namespace,status,shortName){$super(encryptedID,namespace,status,shortName);this.brokerOrganization="";this.isbroker=false;this.isecn=false;this.isexternalprovider=false;this.isprimebroker=false;},getBrokerOrganization:function(){return this.brokerOrganization;},setBrokerOrganization:function(brokerOrganization){this.brokerOrganization=brokerOrganization;},isBroker:function(){return this.isbroker;},isExternalProvider:function(){return this.isexternalprovider;},isPrimeBroker:function(){return this.isprimebroker;},isECN:function(){return this.isecn;},setBroker:function(isbroker){this.isbroker=isbroker;},setExternalProvider:function(isexternalprovider){this.isexternalprovider=isexternalprovider;},setPrimeBroker:function(isprimebroker){this.isprimebroker=isprimebroker;},setECN:function(isecn){this.isecn=isecn;},clone:function(){var org=new Organization(this.getEncryptedID(),this.getNamespace(),this.getStatus(),this.getShortName());org.setLongName(this.getLongName());org.setExternalProvider(this.isExternalProvider());org.setBroker(this.isBroker());org.setPrimeBroker(this.isPrimeBroker());org.setECN(this.isECN());org.setBrokerOrganization(this.getBrokerOrganization());var keys=this.getExternalSystemIDKeys();for(var i=0;i<keys.length;i++){org.setExternalSystemID(keys[i],this.getExternalSystemID(keys[i]));}return org;},toString:function($super){return $super()+'","BrokerOrganization":"'+this.getBrokerOrganization()+'","Broker":"'+this.isBroker()+'","PrimeBroker":"'+this.isPrimeBroker()+'","ExternalProvider":"'+this.isExternalProvider()+'","ECN":"'+this.isECN()+'","ExternalSystemID":{'+this.toStringExternalSystemID()+"}";}});var Counterparty=Class.create(NamedEntity,{initialize:function($super,encryptedID,namespace,status,shortName,organization){$super(encryptedID,namespace,status,shortName);this.organization=organization;},getOrganization:function(){return this.organization;},clone:function(){return new Counterparty(this.getEncryptedID(),this.getNamespace(),this.getStatus(),this.getShortName(),this.getOrganization());},toString:function($super){return $super()+'","Organization":"'+this.getOrganization();}});var TradingParty=Class.create(Counterparty,{initialize:function($super,encryptedID,namespace,status,shortName,organization){$super(encryptedID,namespace,status,shortName,organization);this.legalEntity="";this.legalEntityOrganization="";this.primeBrokerOrganization="";this.primeBrokerTradingParty="";this.primeBrokerCreditOrganization="";this.primeBrokerCreditTradingParty="";this.defaultDealingEntity="";this.stream="";this.isPBCreditEnable=false;this.associatedLegalEntities=new ArrayList();this.stpEnabled=0;this.primeBrokerSTPEnabled=false;this.primeBrokerCoverEnabled=false;this.reportingParty=0;this.generateUSI="";},setLegalEntity:function(legalEntity){this.legalEntity=legalEntity;},setLegalEntityOrganization:function(legalEntityOrganization){this.legalEntityOrganization=legalEntityOrganization;},setPrimeBrokerOrganization:function(primeBrokerOrganization){this.primeBrokerOrganization=primeBrokerOrganization;},setPrimeBrokerTradingParty:function(primeBrokerTradingParty){this.primeBrokerTradingParty=primeBrokerTradingParty;},getPrimeBrokerOrganization:function(){return this.primeBrokerOrganization;},getPrimeBrokerTradingParty:function(){return this.primeBrokerTradingParty;},getSTPEnabled:function(){return this.stpEnabled;},setSTPEnabled:function(stpenabled){this.stpEnabled=stpenabled;},isPrimeBrokerSTPEnabled:function(){return this.primeBrokerSTPEnabled;},setPrimeBrokerSTPEnabled:function(pbSTPenabled){this.primeBrokerSTPEnabled=pbSTPenabled;},setPrimeBrokerCoverEnabled:function(pbCover){this.primeBrokerCoverEnabled=pbCover;},isPrimeBrokerCoverEnabled:function(){return this.primeBrokerCoverEnabled;},isPrimeBrokerCreditEnabled:function(){return this.isPBCreditEnable;},getStream:function(){return this.stream;},setPrimeBrokerCreditEnabled:function(isPBCreditEnable){this.isPBCreditEnable=isPBCreditEnable;},setStream:function(stream){this.stream=stream;},setPrimeBrokerCreditOrganization:function(primeBrokerCreditOrganization){this.primeBrokerCreditOrganization=primeBrokerCreditOrganization;},setPrimeBrokerCreditTradingParty:function(primeBrokerCreditTradingParty){this.primeBrokerCreditTradingParty=primeBrokerCreditTradingParty;},getPrimeBrokerCreditOrganization:function(){return this.primeBrokerCreditOrganization;},getPrimeBrokerCreditTradingParty:function(){return this.primeBrokerCreditTradingParty;},getLegalEntityOrganization:function(){return this.legalEntityOrganization;},getLegalEntity:function(){return this.legalEntity;},setDefaultDealingEntity:function(defaultDealingEntity){this.defaultDealingEntity=defaultDealingEntity;},getDefaultDealingEntity:function(){return this.defaultDealingEntity;},setAssociatedLegalEntities:function(associatedLegalEntities){if(null!=associatedLegalEntities&&associatedLegalEntities.size>0){for(var i=0;i<associatedLegalEntities.size;i++){this.associatedLegalEntitiesa.add(associatedLegalEntities.get(i));}}},addAssociatedLegalEntities:function(associateLegalEntity){this.associatedLegalEntities.add(associateLegalEntity);},getAssociatedLegalEntities:function(){return this.associatedLegalEntities;},getReportingParty:function(){return this.reportingParty;},setReportingParty:function(reportingParty){this.reportingParty=reportingParty;},getGenerateUSI:function(){return this.generateUSI;},setGenerateUSI:function(generateUSI){this.generateUSI=generateUSI;},clone:function(){var tp=new TradingParty(this.getEncryptedID(),this.getNamespace(),this.getStatus(),this.getShortName(),this.getOrganization());tp.setDefaultDealingEntity(this.getDefaultDealingEntity());tp.setLegalEntity(this.getLegalEntity());tp.setLegalEntityOrganization(this.getLegalEntityOrganization());tp.setStream(this.getStream());tp.setLongName(this.getLongName());tp.setSTPEnabled(this.getSTPEnabled());tp.setPrimeBrokerCreditEnabled(this.isPrimeBrokerCreditEnabled());tp.setPrimeBrokerCreditOrganization(this.getPrimeBrokerCreditOrganization());tp.setPrimeBrokerCreditTradingParty(this.getPrimeBrokerCreditTradingParty());tp.setPrimeBrokerOrganization(this.getPrimeBrokerOrganization());tp.setPrimeBrokerTradingParty(this.getPrimeBrokerTradingParty());tp.setAssociatedLegalEntities(this.getAssociatedLegalEntities());tp.setPrimeBrokerSTPEnabled(this.isPrimeBrokerSTPEnabled());tp.setPrimeBrokerCoverEnabled(this.isPrimeBrokerCoverEnabled());tp.setReportingParty(this.getReportingParty());tp.setGenerateUSI(this.getGenerateUSI());var keys=this.getExternalSystemIDKeys();for(var i=0;i<keys.length;i++){tp.setExternalSystemID(keys[i],this.getExternalSystemID(keys[i]));}return tp;},toString:function($super){var associatedLEs="";var arrAssociatedLE=this.getAssociatedLegalEntities();if(null!=arrAssociatedLE&&arrAssociatedLE.size()>0){var tmp=arrAssociatedLE.toArray();for(var i=0;i<tmp.length-1;i++){associatedLEs+=tmp[i]+",";}associatedLEs+=tmp[tmp.length-1];}return $super()+'","LegalEntity":"'+this.getLegalEntity()+'","LegalEntityOrganization":"'+this.getLegalEntityOrganization()+'","PrimeBrokerOrganization":"'+this.getPrimeBrokerOrganization()+'","PrimeBrokerTradingParty":"'+this.getPrimeBrokerTradingParty()+'","PrimeBrokerCreditOrganization":"'+this.getPrimeBrokerCreditOrganization()+'","PrimeBrokerCreditTradingParty":"'+this.getPrimeBrokerCreditTradingParty()+'","Stream":"'+this.getStream()+'","PBCreditEnabled":"'+this.isPrimeBrokerCreditEnabled()+'","PBSTPEnabled":"'+this.isPrimeBrokerSTPEnabled()+'","PBCoverEnabled":"'+this.isPrimeBrokerCoverEnabled()+'","STPEnabled":"'+this.getSTPEnabled()+'","DefaultDealingEntity":"'+this.getDefaultDealingEntity()+'","AssociatedLegalEntities":"{'+associatedLEs+'}","ExternalSystemID":{'+this.toStringExternalSystemID()+'},"ReportingParty":"'+this.getReportingParty()+'","GenerateUSI":"'+this.getGenerateUSI()+'"';}});var MarketDataElement=Class.create(NamedEntity,{initialize:function($super,encryptedID,namespace,shortName){$super(encryptedID,namespace,"A",shortName);this.currencyPair=null;this.tenor=null;this.bid=0;this.mid=0;this.offer=0;this.pipsFactor=0;this.pointsPrecision=0;},getBid:function(){return this.bid;},setBid:function(aBid){this.bid=aBid;},getOffer:function(){return this.offer;},setOffer:function(aOffer){this.offer=aOffer;},getMid:function(){return this.mid;},setMid:function(aMid){this.mid=aMid;},getCurrencyPair:function(){return this.currencyPair;},setCurrencyPair:function(aCurrencyPair){this.currencyPair=aCurrencyPair;},getTenor:function(){return this.tenor;},setTenor:function(aTenor){this.tenor=aTenor;},setPipsFactor:function(aPipsFactor){this.pipsFactor=aPipsFactor;},setPoinsPrecision:function(aPointsPrecision){this.pointsPrecision=aPointsPrecision;},getPipsFactor:function(){return this.pipsFactor;},getPoinsPrecision:function(){return this.pointsPrecision;},clone:function(){var mdeElement=new MarketDataElement(this.getEncryptedID(),this.getNamespace(),this.getStatus(),this.getShortName());mdeElement.setCurrencyPair(this.getCurrencyPair());mdeElement.setTenor(this.getTenor());mdeElement.setCurrencyPair(this.getMid());mdeElement.setBid(this.getBid());mdeElement.setOffer(this.getOffer());mdeElement.setPipsFactor(this.getPipsFactor());mdeElement.setPoinsPrecision(this.getPoinsPrecision());return mdeElement;},toString:function(){return $super()+'","CP":"'+this.getCurrencyPair()+'","Tenor":"'+this.getTenor()+'","Bid":"'+this.getBid()+'","Mid":"'+this.getMid()+'","Offer":"'+this.getOffer();},toJSON:function(){return"{"+this.toString()+"}";}});var Channel=Class.create({initialize:function(){this.esp=true;this.rfsspot=false;this.rfsot=false;this.rfsswap=false;},isESPEnabled:function(){return this.esp;},isRFSSpotEnabled:function(){return this.rfsspot;},isRFSOutrightEnabled:function(){return this.rfsot;},isRFSSwapEnabled:function(){return this.rfsswap;},toString:function(){return'"ESP":"'+this.isESPEnabled()+'","RFSSpot":"'+this.isRFSSpotEnabled()+'","RFSOutright":"'+this.isRFSOutrightEnabled()+'","RFSSwap":"'+this.isRFSSwapEnabled();},toJSON:function(){return"{"+this.toString()+"}";}});