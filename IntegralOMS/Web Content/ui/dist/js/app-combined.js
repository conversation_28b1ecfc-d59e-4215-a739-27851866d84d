'use strict';

// declare top-level module which depends on filters,and services
var OMSApp = angular.module('OMSApp',
    [   'OMSApp.filters',
        'OMSApp.directives', // custom directives
        'ngRoute',//Angular Route
        'ngCookies',
        'ngAnimate',
        'ngSanitize', // for html-bind in ckeditor
        //'ui.ace', // ace code editor
        'ui.bootstrap', // jquery ui bootstrap
        'ui.grid',// version 3 of ng-grid, use it in prod if it gets stable
        'ui.grid.edit',
        'ui.grid.rowEdit',
        'ui.grid.selection',
        'ui.grid.exporter',
        'ui.grid.pinning',
        'ui.grid.moveColumns',
        'ui.grid.saveState',
        'ui.grid.autoResize',
        'ui.select',
        'ui.bootstrap.datetimepicker',
        'frapontillo.bootstrap-switch',
        'lr.upload'
    ]);


var filters = angular.module('OMSApp.filters', []);
var directives = angular.module('OMSApp.directives', []);
var services = angular.module('OMSApp.services', []);

// bootstrap angular
OMSApp.config(['$routeProvider', '$locationProvider', '$httpProvider', function ($routeProvider, $locationProvider, $httpProvider) {

    // TODO use html5 *no hash) where possible
    // $locationProvider.html5Mode(true);

	$httpProvider.defaults.cache = false;
	   if (!$httpProvider.defaults.headers.get) {
	     $httpProvider.defaults.headers.get = {};
	}
	    // disable IE ajax request caching
   $httpProvider.defaults.headers.get['If-Modified-Since'] = '0';
	    
    $routeProvider.when('/', {
        templateUrl:'partials/home.html'
    });
    $routeProvider.when('/contact', {
        templateUrl:'partials/contact.html'
    });
    $routeProvider.when('/orderEntry', {
        templateUrl:'partials/orderEntry.html'
    });
    $routeProvider.when('/orderList', {
        templateUrl:'partials/orderList.html'
    });
    $routeProvider.when('/about', {
        templateUrl:'partials/about.html'
    });
    $routeProvider.when('/audit', {
        templateUrl:'partials/orderAuditModal.html'
    });
    $routeProvider.when('/faq', {
        templateUrl:'partials/faq.html'
    });
    $routeProvider.when('/preferences', {
        templateUrl:'partials/preferences.html'
    });
    $routeProvider.when('/login', {
        templateUrl:'partials/login.html'
    });

    // by default, redirect to site root
    $routeProvider.otherwise({
        redirectTo:'/'
    });

}]);

// this is run after angular is instantiated and bootstrapped
OMSApp.run(function ($rootScope, $location,$window, $http, $timeout, $templateCache, AuthService, RESTService, OrderService ,RateService) {

    $rootScope.$on('$routeChangeStart', function(event, next, current) {
        if (typeof(current) !== 'undefined'){
            $templateCache.remove(current.templateUrl);
        }
    });
    
    // *****
    // Eager load some data using simple REST client
    // *****
    $rootScope.format ='MM-dd-yy hh:mm:ss a';

    $rootScope.restService = RESTService;
    
    $rootScope.showOnlyCustOrg = true;

    // async load constants
    $rootScope.constants = [];
    $rootScope.restService.get('data/constants.json', function (data) {
            $rootScope.constants = data[0];
        }
    );

    // async load data do be used in table (playgound grid widget)
    $rootScope.listData = [];

    //Get User preerences Here
    $rootScope.restService.get('data/userPref.json', function (data) {
            $rootScope.preferences = data;
        }
    );
    // *****
    // Initialize authentication
    // *****
    $rootScope.authService = AuthService;
    $rootScope.orderService = OrderService;
    $rootScope.rateService = RateService;
    $rootScope.notifier = new NotificationManager($rootScope);

    $rootScope.$watch('authService.authorized()', function () {

        // if never logged in, do nothing (otherwise bookmarks fail)
        if ($rootScope.authService.initialState()) {
            // we are public browsing
            return;
        }

        $rootScope.authNotifier = new NotificationManager($rootScope);

        // when user logs in, redirect to home
        if ($rootScope.authService.authorized()) {
            $location.path("/");
            $rootScope.authNotifier.notify('information', 'Welcome ' + $rootScope.authService.currentUser() + "!");
        }

        // when user logs out, redirect to home
        if (!$rootScope.authService.authorized()) {
            $location.path("/");
        }

    }, true);



});
'use strict';
OMSApp.controller('LoginCtrl', ['$scope', '$rootScope', 'AuthService', function LoginCtrl($scope, $rootScope, AuthService) {
    $rootScope.$watch('authService.authorized()', function () {
        if ($rootScope.authService.authorized()) {

        } else {
            $scope.passwordInput = null;
        }
    });
    
    $scope.loginResponseHandler = function(response ,data) {
    	if(response.status == "OK") {
    		$rootScope.errorMsg ="";
    	} else {
	    	if(response.responseJSON && response.responseJSON.errorCode) {
				$rootScope.errorMsg =response.responseJSON.errorCode;
			} else if(response.responseString) {
				$rootScope.errorMsg = response.responseString;
			} else {
				$rootScope.errorMsg ="Login Failed";
			}
    	}
    };
    
    $rootScope.errorMsg ="";
    $scope.login = function () {
    	$rootScope.errorMsg ="";
        if ($scope.orgInput == null) {
            AuthService.login('DBNA', 'Sameer', 'Test@123' ,$scope.loginResponseHandler);
        } else {
            AuthService.login($scope.orgInput, $scope.userInput, $scope.passwordInput ,$scope.loginResponseHandler);
        }
    };

}]);

		
'use strict';

OMSApp.controller('OrderAuditCtrl',['$scope', '$modal', '$modalInstance','data','$interval','uiGridConstants', function ($scope, $modal, $modalInstance, data, $interval ,uiGridConstants) {
	
    $scope.gridOptions = {
            enableSorting: false,
            enableFiltering: false,
            enableHighlighting: true,
            enableColumnResize: false,
            enableColumnReordering: true,
            enableGridMenu: true,
            enableRowHeaderSelection: true,
            enableColumnMenus:false,
            enableHorizontalScrollbar:uiGridConstants.scrollbars.NEVER,
            enableVerticalScrollbar:true,
            enableRowSelection: false,
            columnDefs: [                        
                         {
                        	 displayName: "User",
                             field: "actionUser",
                             enableCellEdit: false,
                             maxWidth: 140
                          },
                          {
                        	  displayName: "Event",
                              field: "eventDisplayName",
                              enableCellEdit: false,
                              maxWidth: 140
                          },
                          {
                        	   displayName: "Timestamp",
                               field: "timeStamp",
                           	   cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: center;'>{{(MODEL_COL_FIELD| epochMilliseconds)}}</div>", 
                               enableCellEdit: false,
                               maxWidth: 140,
                               sort: {
                                   direction: "desc",
                                   priority: 1
                               }
                          },
                          {
                        	    displayName: "Message",
                                field: "eventMsg",
                                enableCellEdit: false,
                                minWidth: 300,
                                cellTemplate: "<div class='ui-grid-cell-contents elipsis' style='height:100%;text-align: left;'>{{(MODEL_COL_FIELD)}}</div>"
                          }
                  ],
    	    onRegisterApi: function (gridApi) {
    	      $scope.gridApi = gridApi;
    	 
    	      $interval( function() {
    	        $scope.gridApi.core.handleWindowResize();
    	      }, 500, 10);
    	      }
    };
    

    data.omsAuditEvents.forEach(function(event) {
    	if( event.actionOrg != null ){
    		event.actionUser = event.actionOrg;
    	}
    	if(event.actionUser == null || event.actionUser == ""){
    		event.actionUser = "System";
    	}
    	if(event.eventDisplayName == "STATE_CHANGED"){
    		event.eventDisplayName = event.newState;
    	}
	});
    $scope.gridOptions.data = data.omsAuditEvents;
    $scope.orderId = data.orderId;
   
    
    $scope.ok = function () {
        $scope.data = null;
        $modalInstance.close();
    };
    
    $scope.cancel = function () {
        $scope.data = null;
        $modalInstance.dismiss('cancel');
    };
}]);
/*
* Created by sameer on 4/21/15.
 */
'use strict';
OMSApp.controller('OrderEntryCtrl', ['$scope', '$interval', '$routeParams', '$http', '$rootScope', '$document', 'Utils', '$filter', '$window', 'RESTService',
    function OrderEntryCtrl($scope, $interval, $routeParams, $http, $rootScope, $document, Utils, $filter, $window, RESTService) {
        $scope.notifier = new NotificationManager($rootScope);
        $scope.action =  "";
        $scope.orders = [];
        $scope.order = {};
        $scope.order.customer = {};
        $scope.order.custOrg = {};
        $scope.order.customer.required = true;
        $scope.order.tif = {};
        $scope.order.ccyPair = {};
        $scope.order.orderType = {};
        $scope.order.ratePlaceHolder = " Rate";
        $scope.order.ccyPair.selected = 'EUR/USD';
        $scope.order.baseCCY = 'EUR';
        $scope.order.termCCY = 'USD';
        $scope.order.dealtCCY = true;
        $scope.order.side = true;
        $scope.order.link = false;
        var childOrders = null;
        var amountValidated = false;
        var tpslOrder = false;
        var percAwayFromMarket = 0.0;
        var validateNumber = function(number , fieldName) {
        	var response = {};
        	if (number != null) {
        		response.field = parseFloat(number.replace(/,/g, ''));
                 if(response.field <= 0.00) {
                 	response.errorMsg = fieldName +" should be greater than 0";
                 	return response;
                 }
             } else {
                 response.errorMsg = fieldName +" missing";
                 return response;
             }
        	if(fieldName == "Amount"){
        		amountValidated = true;
        	}
        	return response;
        }
        
        var validateOrder = function (order, count) {
            var response = {};
            var newOrder = {};
            var fixingOrder = false;
            if ($scope.order.ccyPair != null) {
                newOrder.ccyPair = $scope.order.ccyPair.selected;
                
            } else {
                response.errorMsg = "Currency Pair missing";
                return response;
            }
            if($rootScope.ccyPairs.indexOf(newOrder.ccyPair)<0) {
            	response.errorMsg =  "Currency Pair " + newOrder.ccyPair + " is not Supported";
                return response;
            }
            
            newOrder.dealtCcy = $scope.order.dealtCCY ? $scope.order.baseCCY : $scope.order.termCCY;
            newOrder.side = order.side ? 'BUY' : 'SELL';
            
            var response = {};
            if(!amountValidated){
            	response = validateNumber($scope.order.amount , "Amount");
            }
            if(response.errorMsg) {
            	return response;
            } else {
            	if(response.field){
            		newOrder.dealtAmt = response.field;
            	}
            	else{
            		newOrder.dealtAmt = $scope.order.amount;
            	}
            	if((count == 0 && childOrders.length == 0) || (count == 1 && childOrders.length == 1) || (count == 2 && childOrders.length == 2)){
            		if(response.field){
            			$scope.order.amount = $filter('amountFilter')(response.field, true);
            		}
            		else{
            			$scope.order.amount = $filter('amountFilter')($scope.order.amount, true);
            		}
            		amountValidated = false;
            	}
            	else{
            		if(response.field){
            			$scope.order.amount = response.field;
            		}
            	}
            }
           if (order.orderType != null && order.orderType.selected.value == "FIXING") {
                order.rate = null;
                fixingOrder = true;
           }else{
            response = validateNumber(order.rate , "Price"); 
            if(response.errorMsg) {
            	return response;
            } else {
            	newOrder.orderPrice = response.field;
            	if(count == 0){
            		$scope.order.rate = $filter('amountFilter')(response.field, false);
            	}
            	else if(count == 1){
            		$scope.orders[0].rate = $filter('amountFilter')(response.field, false);
            	}
            	else if(count == 2){
            		$scope.orders[1].rate = $filter('amountFilter')(response.field, false);
            	}
            }
           }

            if (order.orderType != null) {
                newOrder.orderType = order.orderType.selected.value;
            }

            if (response.errorMsg) {
                return response;
            }
            if($rootScope.customer) {
            	newOrder.cptyAOrg = $rootScope.customerOrg;
            	newOrder.cptyBOrg = $rootScope.brokerOrg;
            } else {
                if ($rootScope.showOnlyCustOrg) {
                    if ($scope.order.custOrg.selected) {
                        newOrder.cptyAOrg = $scope.order.custOrg.selected;
                        newOrder.cptyA = $scope.customers[newOrder.cptyAOrg].le;
                    }
                }
            }


            newOrder.orderState = 'DRAFT';
            newOrder.notes = $scope.order.notes;
            newOrder.custNotes = $scope.order.custNotes;
            newOrder.contigencyParameters = order.contigencyParameters;
            
            if ($scope.order.expiryTime) {
            	newOrder.expiryTime = $scope.order.expiryTime;//This is in browsers timestamp 
                var currentTimeWithUserPref = new Date(moment.utc());//This is in browsers timestamp 
                if($scope.order.expiryTime <= currentTimeWithUserPref.valueOf()) {
                       response.errorMsg = "Order Expiry time has already passed";
                      return response;
                }
                newOrder.tif = 'GTD';
            } else {
                newOrder.tif = 'GTC';
            }

            if ($scope.order.fixingTime && fixingOrder) {
                 var year = $filter('date')(new Date(moment.utc()), "yyyy");
                 var month = $filter('date')(new Date(moment.utc()), "MM");
                 var day = $filter('date')(new Date(moment.utc()), "dd");
           	     var hourMin = $scope.order.fixingTime.split(":");
           	     var hour = hourMin[0];
           	     var min = hourMin[1];
           	     var fixingDateTime = new Date(Date.UTC(year, month-1, day, hour, min));
            	 newOrder.fixingTime = fixingDateTime.getTime();//This is GMT time
                 var currentTimeWithUserPref = new Date();//This is in browsers timestamp
                 var ct = currentTimeWithUserPref.getTime();
                 if(fixingDateTime.getTime() <= ct) {
                      response.errorMsg = "Order Fixing time has already passed";
                      return response;
                 }
            }

            if ($scope.order.fixingReference && fixingOrder) {
                newOrder.fixingReference = $scope.order.fixingReference;
            }

            if (order.orderType != null && order.orderType.selected.value == "FIXING") {
                if (newOrder.fixingTime == null) {
                    response.errorMsg = "Fixing time cannot be empty for fixing order ";
                    return response;
                }
                if (newOrder.fixingReference == null) {
                    response.errorMsg = "Fixing Reference cannot be empty for fixing order ";
                    return response;
                }
                if(newOrder.cptyAOrg == null) {
                    response.errorMsg = "Customer cannot be empty for fixing order ";
                    return response;
                }
                if(newOrder.tif == 'GTD' && $scope.order.expiryTime < $scope.order.fixingTime){
                    response.errorMsg = "Fixing Time cannot be greater than expiry Time for GTD Order";
                    return response;
                }
            }

            if(order.primaryOrder) {
            	newOrder.primaryOrder = true;
            }
            response.newOrder = newOrder;
            $rootScope.filterAndRefreshData('');            
            if ($rootScope.rates != null) {
              if(tpslOrder || count == 0){
            	order.orderPrice = order.rate;
              	order.ccyPair = $scope.order.ccyPair.selected;
              	order.dealtCcy = newOrder.dealtCcy;
              	order.side = newOrder.side;
            	var dealtInBase = (order.ccyPair.substring(0, 3) === order.dealtCcy);
                var isBuy = dealtInBase ? (order.side == 'BUY') : (order.side != 'BUY');         	
            	var orderType = null;
           	 	if(order.orderType.selected == null)
           	 	{
           	 		orderType = order.orderType;
           	 	}
           	 	else{
           	 		orderType = order.orderType.selected.value;
           	 	}
           	 	var marketRate = null;
                if ((isBuy && orderType != 'STOP') || (!isBuy && orderType == 'STOP')) {
                	marketRate = $rootScope.rates[newOrder.ccyPair + '/' + 'OFFER'];
                } else if((!isBuy && orderType != 'STOP') || (isBuy && orderType == 'STOP'))  {
                	marketRate = $rootScope.rates[newOrder.ccyPair + '/' +  'BID'];                     
                }
                var alertMessage = "";
            	if(isOrderWithInMarket( Utils.calculatePercentage(order), isBuy, orderType)){
            		alertMessage = "Order is "+ percAwayFromMarket+" % away from the market, do you want to continue.";
            		if (!getConfirmation(alertMessage)) {
                        return;
                    }
            	}
            	else if(((isBuy && orderType != 'STOP') || (!isBuy && orderType == 'STOP'))  && newOrder.orderPrice > marketRate){
            		if(isBuy && orderType != 'STOP'){
            			alertMessage = "BUY "+$scope.order.baseCCY+" LIMIT order is above the market and will trigger immediately, confirm to continue.";
            		}else{
            			alertMessage = "SELL "+$scope.order.baseCCY+" STOP order is above the market and will trigger immediately, confirm to continue.";
            		}
            		if (!getConfirmation(alertMessage)) {
                        return;
                    }
            	}
            	else if(((!isBuy && orderType != 'STOP') || (isBuy && orderType == 'STOP')) && newOrder.orderPrice < marketRate){
            		if(!isBuy && orderType != 'STOP'){
            			alertMessage = "SELL "+$scope.order.baseCCY+" LIMIT order is below the market and will trigger immediately, confirm to continue.";
            		}else{
            			alertMessage = "BUY "+$scope.order.baseCCY+" STOP order is below the market and will trigger immediately, confirm to continue.";
            		}
            		if (!getConfirmation(alertMessage)) {
                        return;
                    }
            	}
	         }
            }
            order.ccyPair = $scope.order.ccyPair;
            delete order.orderPrice;
            delete order.dealtCcy;
            delete order.side;
            return response;
        };
        var isOrderWithInMarket = function(order, isBuy, orderType){
        	if ((isBuy && orderType != 'STOP') || (!isBuy && orderType == 'STOP') ) {
            	var buyPercThreshold = $rootScope.buyPercThreshold ? $rootScope.buyPercThreshold : 99.7500;
            	if( order.percentage > buyPercThreshold && order.percentage < 100.0000) {
            		percAwayFromMarket = $filter('number')(100 - order.percentage,2);
            		return true;
            	}
            } else if ((!isBuy && orderType != 'STOP') || (isBuy && orderType == 'STOP')){
            	var sellPercThreshold = $rootScope.sellPercThreshold ? $rootScope.sellPercThreshold : 100.2500;
            	if (order.percentage < sellPercThreshold && order.percentage > 100.0000) {
            		percAwayFromMarket = $filter('number')(order.percentage - 100,2);
            		return true;
            	}
            }
        	return false;
        }
        
        var getConfirmation = function(message){
            var start = new Date().getTime();
            var result = $window.confirm(message);
            var dt = new Date().getTime() - start;           
            if(result || dt < 50)
               return true;
            return result;
        }
        
        $rootScope.saveOrderEntry = function () {
        	$rootScope.expandOrderEntry = !$rootScope.expandOrderEntry;
        	 var prefStr = {};
             prefStr.omsPref = {};
             prefStr.omsPref.gridStateSetting = {};
             prefStr.omsPref.gridStateSetting["OrderEntry"] = JSON.stringify({expanded:$rootScope.expandOrderEntry});
             RESTService.saveUIPreference(prefStr, function (data, errorMsg) {
 	    		 if(errorMsg!=null) {
 	    			 console.log("Failed save state, Status" + data.status);
 	    		 } else {
 	    			 console.log("State save Successful.");
 	    		 }
 	    	 });
         }; 
        
        $scope.onAction = function(action) {
        	if(!action) {
        		return;
        	}
        	if(action === 'make_draft') {
        		action = null;
        	}
        	if(action === 'reset') {
        		$scope.reset($scope.order);
        		return;
        	}
        	var orders = {};
        	orders.orderListParams = [];
        	
	    	 var primaryOrder = angular.copy($scope.order);
	    	 childOrders = angular.copy($scope.orders);
	    	 primaryOrder.primaryOrder = true;
    		if(primaryOrder.orderType.selected.value == "TPSL") {
    			tpslOrder = true;
        		primaryOrder.contigencyParameters = [{"type" :1 ,"groupId":"Group1"}];
        		primaryOrder.orderType.selected = $scope.orderTypes[0];
        		
        		childOrders[0].contigencyParameters = [{"type" :1 ,"groupId":"Group1"}];
        		childOrders[0].primaryOrder = true;
        	} else if(primaryOrder.link) {
        		//Link is enabled
        		
        		if(childOrders && childOrders.length >0) {
            		if(childOrders[0].isSelected && !childOrders[1].isSelected) {
            			//If Done TP
                		primaryOrder.contigencyParameters =[{"type" :2 ,"groupId":"Group1"}];
            			childOrders.splice(1,1);
            		} 
            		else if(childOrders[1].isSelected && !childOrders[0].isSelected) {
            			//If Done SL
            			primaryOrder.contigencyParameters = [{"type" :2 ,"groupId":"Group1"}];
            			childOrders.splice(0,1);
            		}
             		else if(childOrders[1].isSelected && childOrders[0].isSelected) {
             			//If Done TP-SL
             			childOrders[0].contigencyParameters = [{"type" :1 ,"groupId":"Group1"}];
             			childOrders[1].contigencyParameters = [{"type" :1 ,"groupId":"Group1"}];
            			primaryOrder.contigencyParameters = [{"type" :2 ,"groupId":"Group1"}];
            		}
            		else if(!childOrders[1].isSelected && !childOrders[0].isSelected) {
            			childOrders = [];
            		}
       
        		}

        	} 
        	var count = 0;
        	var response = validateOrder(primaryOrder, count);
        	if (response == null ){
        		return;
        	}
            if (response.errorMsg != null) {
                $scope.notifier.notify('error', "Order submission failed, Reason : " + response.errorMsg);
                tpslOrder = false;
                return;
            } else {
            	orders.orderListParams.push(response.newOrder);
            }
        	
            childOrders.forEach(function (order) {
            	 if (!response.errorMsg) {
	                response = validateOrder(order, ++count);
	                if (response.errorMsg == null) {
	                	orders.orderListParams.push(response.newOrder);
	                }
            	 }
                
        	});
        	if($rootScope.customer) {
        		orders.customer =true;
        		orders.brokerOrg = $rootScope.brokerOrg;
        	}
            if( response.errorMsg ) {
            	 $scope.notifier.notify('error', "Order Submission failed. " + response.errorMsg);
            	 tpslOrder = false;
            	 return;
            }
        	//orders.orderListParams.push({"ccyPair":"EUR/USD","dealtCcy":"EUR","side":"BUY","dealtAmt":1000,"orderPrice":1.3,"orderType":"LIMIT","orderState":"DRAFT","tif":"GTC"});
            tpslOrder = false;
            $rootScope.orderService.createBatchOrders(orders, action , function (data, errorMsg) {
                if (!errorMsg) {
                	if(!$rootScope.webSocket) {
                		$scope.notifier.notify('success', 'Submitted ' + data.responseTuples[0].value);
                	}
                } else {
                    $scope.notifier.notify('error', errorMsg);
                }
            });
        };


        $scope.reset = function (order) {
        	$.noty.closeAll();
            if (order.customers) {
                order.customer.selected = null;
            }
            if (order.custOrg) {
                order.custOrg.selected = null;
            }
            if (order.tifs) {
                order.tif.selected = $scope.tifs[0];
            }
            if (order.ccyPairs) {
                order.ccyPair.selected = $scope.ccyPairs[0];
            }
            if (order.orderTypes) {
                order.orderType.selected = $scope.orderTypes[0];
            }
            order.amount = null;
            order.expiryTime = null;
            order.notes = null;
            order.custNotes = null;
            order.dealtCCY = true;
            order.side = true;
            if (order.ftt) {
                order.ftt = null;
            }
            if (order.fixtt) {
                order.fixtt = null;
            }
            order.fixingTime = null;
            order.fixingReference = null;
            $scope.orders = [];
            $scope.order.link = false;
            $scope.populateDealtCCY(order);
            $scope.populateRate(order);
            //onCCYPairChange();
        };
        
        
        $scope.populateDealtCCY = function (order) {
            if (order.ccyPair && order.ccyPair.selected) {
                order.baseCCY = order.ccyPair.selected.substring(0, 3);
                order.termCCY = order.ccyPair.selected.substring(4, 7);
                order.dealtCCY = true;
            }
        };


        $scope.populateRate = function (order) {
            if (order.ccyPair && order.ccyPair.selected) {
                var ccyPair = order.ccyPair.selected;
                var isBuying =((order.side && order.dealtCCY) || (!order.side && !order.dealtCCY));
                var side = isBuying ? 'OFFER' : 'BID';
                var key = ccyPair + '/' + side;
                if ($rootScope.rates) {
                    var rate = $rootScope.rates[key];
                    rate = Utils.formatRate(rate, ccyPair);
                    order.rate = rate;
                }
            }
        };
        
        
        $scope.toggleLink = function () {
        		$scope.order.link = ! $scope.order.link;
        		if($scope.order.link) {
        			$scope.orders = [{orderType: {selected:$scope.order.orderTypes[0]}, side:!$scope.order.side ,orderTypeDisplayText : "Take Profit" ,ratePlaceHolder : " Rate"},
        			                {orderType: {selected:$scope.order.orderTypes[1]}, side:!$scope.order.side ,orderTypeDisplayText : "Stop Loss" ,ratePlaceHolder : " Stop Rate"}];
        			$scope.copyCommonValuesToChild();
        			$scope.orders.forEach(function(order) {
        				$scope.setRate(order);
        			});
        			$scope.setRate($scope.order);
        		} else {
        			$scope.orders = [];
        		}
        }
        $scope.groupByOrg = function (customer) {
            return customer.org;
        };

        $scope.populateDealtCCY = function () {
            if ($scope.order.ccyPair.selected) {
                $scope.order.baseCCY = $scope.order.ccyPair.selected.substring(0, 3);
                $scope.order.termCCY = $scope.order.ccyPair.selected.substring(4, 7);
                $scope.order.dealtCCY = true;
            }
        };


        $scope.populateRate = function () {
            if ($scope.order.ccyPair.selected) {
                var ccyPair = $scope.order.ccyPair.selected;
                var isBuying =(($scope.order.side && $scope.order.dealtCCY) || (!$scope.order.side && !$scope.order.dealtCCY));
                var side = isBuying ? 'OFFER' : 'BID';
                var key = ccyPair + '/' + side;
                if ($rootScope.rates) {
                    var rate = $rootScope.rates[key];
                    rate = Utils.formatRate(rate, ccyPair);
                    $scope.order.rate = rate;
                }
            }
        };
        
        $scope.setRate = function (order) {
            if (order.ccyPair.selected) {
                var ccyPair = order.ccyPair.selected;
                var isBuying =((order.side && order.dealtCCY) || (!order.side && !order.dealtCCY));
                var side = isBuying ? 'OFFER' : 'BID';
                var key = ccyPair + '/' + side;
                if ($rootScope.rates) {
                    var rate = $rootScope.rates[key];
                    rate = Utils.formatRate(rate, ccyPair);
                    order.rate = rate;
                }
            }
        };
        
        $scope.copyCommonValuesToChild = function() {
        	$scope.orders.forEach(function (childOrder) {
        		childOrder.customer = {};
        		childOrder.custOrg = {};
        		childOrder.tif = {};
        		childOrder.ccyPair = {};
        		childOrder.tif.selected = $scope.order.tif.selected;
        		childOrder.ccyPair.selected = $scope.order.ccyPair.selected;
        		childOrder.dealtCCY = $scope.order.dealtCCY;
        		childOrder.amount = $scope.order.amount;
        	});
        }
        
        /**
         * ========================================================================================
         * 	On key Change listeners
         * ========================================================================================
         */
        
        $scope.formatRateOnBlur = function () {
            if ($scope.order.rate) {
                $scope.order.rate = Utils.formatRate($scope.order.rate, $scope.order.ccyPair.selected);
            }
        };

        $scope.formatAmountOnBlur = function (obj) {
            var amt = notionalAmountFormat.format(document.querySelector('#amount'), $scope.order.ccyPair.selected);
            $scope.order.amount = amt;
        };
        
        	
    	$scope.$watch('order.amount', function () {
         	//This is parent order
        	$scope.orders.forEach(function (childOrder) {
        		if($scope.order) {
        			childOrder.amount = $scope.order.amount;
        		}
        	});
        });

        
        $scope.onSideChange = function () {
        	if($scope.orders) {
        		if($scope.order.orderType.selected.value == "TPSL") {
        			$scope.orders[0].side = $scope.order.side;
        			$scope.setRate($scope.orders[0]);
        		} else {
              		$scope.orders.forEach(function(order) {
            			order.side = !$scope.order.side;
            			$scope.setRate(order);
            		});
        		}
        	}
            $scope.setRate($scope.order);

        };
        
        $scope.onDealtCCYChange = function () {
           	$scope.orders.forEach(function(order) {
           		order.dealtCCY = $scope.order.dealtCCY;
        		$scope.setRate(order);
        	});
        	$scope.setRate($scope.order);
        };

        $scope.onCCYPairChange = function () {
            $scope.populateDealtCCY();
            $scope.copyCommonValuesToChild();
           	$scope.orders.forEach(function(order) {
        		$scope.setRate(order);
        	});
        	
        	$scope.setRate($scope.order);
        };
        
        
        $scope.onOrderTypeChange = function () {
            	
    		if($scope.order.orderType.selected.value == "TPSL") {
        		$scope.orders = [{orderType:{selected:$scope.order.orderTypes[1]}, side:$scope.order.side ,ratePlaceHolder : " Stop Rate"}];
        	
        	} /*
        	else if($scope.order.orderType.selected.value == "TP") {
        		$scope.orders = [{orderType:{selected:$scope.order.orderTypes[0]}, side:!$scope.order.side}];
        	
        	} else if ($scope.order.orderType.selected.value == "SL") {
        		$scope.orders = [{orderType: {selected:$scope.order.orderTypes[1]}, side:!$scope.order.side}];
        		
        	} else if ($scope.order.orderType.selected.value == "TPSL") {
        		$scope.orders = [{orderType: {selected:$scope.order.orderTypes[0]}, side:!$scope.order.side},{orderType: {selected:$scope.order.orderTypes[1]}, side:!$scope.order.side}];
        		
        	}*/ else {
        		$scope.orders = [];
        		$scope.order.link = false;
        	}
        	
        	$scope.copyCommonValuesToChild();
        	$scope.orders.forEach(function(order) {
        		$scope.setRate(order);
        	});
            
        	 $scope.setRate($scope.order);
        };

        $scope.onFixingReferenceChange = function (fixingReference) {
            $scope.order.fixingTime = null;
            $rootScope.fixingTime = $rootScope.fixingTimeMap[fixingReference];
        }
        
        
        /**
         *  ========================================================================================
         *  Watchers
         *  ========================================================================================
         */
        
        $rootScope.$watch('customers', function () {
            $scope.order.customers = $rootScope.customers;
            $scope.customers = $rootScope.customers;
            if ($scope.order.customers) {
                // $scope.order.customer.selected = $scope.order.customers[0];
            }
         	
        });

        $rootScope.$watch('tif', function () {
            $scope.order.tifs = $rootScope.tif;
            $scope.tifs = $rootScope.tif;
            if ($scope.order.tifs) {
                $scope.order.tif.selected = $scope.order.tifs[0];
            }
        });

        $rootScope.$watch('ccyPairs', function () {
            $scope.order.ccyPairs = $rootScope.ccyPairs;
            $scope.ccyPairs = $rootScope.ccyPairs;

            if ($scope.order.ccyPairs) {
                $scope.order.ccyPair.selected = $scope.order.ccyPairs[0];
                $scope.onCCYPairChange();
            }
        });

        $rootScope.$watch('orderTypes', function () {
            $scope.order.orderTypes = $rootScope.orderTypes;
            $scope.orderTypes = $rootScope.orderTypes;
            if ($scope.order.orderTypes) {
                $scope.order.orderType.selected = $scope.order.orderTypes[0];
            }
        	//$scope.setRate($scope.order);
        });

        $scope.$watch('order.dateDropDownInput', function () {
        	  if ($scope.order.dateDropDownInput) {
                  var date = $scope.order.dateDropDownInput;//This is in browsers timestamp 
                  $scope.order.ftt = $filter('date')(date, $rootScope.dtAndTimeFmt); //Users preference timezone  
                  $scope.order.expiryTime = date.valueOf() + $rootScope.browserAndUserTimeZoneDiff;
              }
        });

        $scope.$watch('order.dateDropDownInput1', function () {
        	  if ($scope.order.dateDropDownInput1) {
                  var date = $scope.order.dateDropDownInput1;//This is in browsers timestamp
                  $scope.order.fixtt = $filter('date')(date, $rootScope.dtAndTimeFmt); //Users preference timezone
                  $scope.order.fixingTime = date.valueOf() + $rootScope.browserAndUserTimeZoneDiff;
              }
        });


        $rootScope.$watch('authService.authorized()', function () {
        	if (!$rootScope.authService.authorized()) {
        		$scope.orders = [];
        		$scope.reset($scope.order);
        	}
        });

        
        
    }]);


function guid() {
	  function s4() {
	    return Math.floor((1 + Math.random()) * 0x10000)
	      .toString(16)
	      .substring(1);
	  }
	  return s4() + s4() + '-' + s4() + '-' + s4() + '-' +
	    s4() + '-' + s4() + s4() + s4();
	}

$(document).unbind('keydown').bind('keydown', function (event) {
    var doPrevent = false;
    if (event.keyCode === 8) {
        var d = event.srcElement || event.target;
        if ((d.tagName.toUpperCase() === 'INPUT' &&
                (
                d.type.toUpperCase() === 'TEXT' ||
                d.type.toUpperCase() === 'PASSWORD' ||
                d.type.toUpperCase() === 'FILE' ||
                d.type.toUpperCase() === 'EMAIL' ||
                d.type.toUpperCase() === 'SEARCH' ||
                d.type.toUpperCase() === 'DATE' )
            ) ||
            d.tagName.toUpperCase() === 'TEXTAREA') {
            doPrevent = d.readOnly || d.disabled;
        }
        else {
            doPrevent = true;
        }
    }
    else if (event.keyCode === 66) {
        var source = event.target || event.srcElement;
        if (source.id == 'side') {
            $scope.side = true;
        }
    } else if (event.keyCode === 83) {
        var source = event.target || event.srcElement;
        if (source.id == 'side') {
            $scope.side = false;
        }
    }
    if (doPrevent) {
        event.preventDefault();
    }
});


'use strict';

OMSApp.controller('OrderListCtrl', ['$scope', '$window', '$q', '$filter', '$timeout', '$interval', '$routeParams', 'websocket' ,'AuthService', 'OrderService','RESTService' ,'ReferenceDataService', 'RateService', 'Utils','GridUtils', 'OrderPanelFactory', 'AggregateOrderPanelFactory', '$http', '$rootScope', 'uiGridConstants', '$cookies','$modal','$location', '$modalStack',
    function OrderListCtrl($scope, $window, $q, $filter, $timeout, $interval, $routeParams, websocket ,AuthService, OrderService, RESTService,  ReferenceDataService, RateService, Utils,GridUtils, OrderPanelFactory, AggregateOrderPanelFactory, $http, $rootScope, uiGridConstants, $cookies , $modal ,$location, $modalStack) {
      	$scope.activeState = [];
		$rootScope.STATES ={DRAFT :"DRAFT",	
							ACTIVE :"ACTIVE",
							FILLED:"FILLED",
							NOTIFIED:"NOTIFIED",
							CANCELLED:"CANCELLED",
							REJECTED:"REJECTED",
							EXPIRED:"EXPIRED",
							ARCHIVED:"ARCHIVED",
							EXPORTED:"PASSED",
							IMPORTED:"RECEIVED",
							WITHDRAWN:"TAKEN_BACK",
							ACCEPTED:"ACCEPTED",
							TRIGGERED:"TRIGGERED",
							CANCEL_REQUESTED:"CANCEL_REQUESTED",
							AMEND_REQUESTED:"AMEND_REQUESTED",
							CREDIT_FAIL:"CREDIT_FAIL",
							AUTO:"AUTO"};
		
        
        //================================== Initialize Grids =======================================================================
        $scope.FixingAggregate = AggregateOrderPanelFactory.createGrid("FixingAggregate" ,  [$rootScope.STATES.WITHDRAWN,
                                                                         $rootScope.STATES.REJECTED,
                                                                         $rootScope.STATES.ACCEPTED,
                                                                         $rootScope.STATES.EXPORTED,
                                                                         $rootScope.STATES.DRAFT,
                                                                         $rootScope.STATES.ACTIVE,
                                                                         $rootScope.STATES.TRIGGERED,
                                                                         $rootScope.STATES.AUTO ],	["FixingAggregate"]);
        $scope.Draft = OrderPanelFactory.createGrid("Draft" , [$rootScope.STATES.DRAFT ] ,["Draft"]); 
        $scope.Cancelled = OrderPanelFactory.createGrid("Cancelled" , [$rootScope.STATES.CANCELLED ] ,["Cancelled"]);   
        $scope.Active = OrderPanelFactory.createGrid("Active" ,  [$rootScope.STATES.ACTIVE, $rootScope.STATES.TRIGGERED,$rootScope.STATES.FILLED,$rootScope.STATES.EXPORTED,$rootScope.STATES.WITHDRAWN,$rootScope.STATES.REJECTED,$rootScope.STATES.CREDIT_FAIL] ,["ActiveTop" ,"ActiveBottom" ]);
        $scope.Active1 = OrderPanelFactory.createGrid("Active1" ,  [$rootScope.STATES.ACTIVE, $rootScope.STATES.TRIGGERED,$rootScope.STATES.FILLED,$rootScope.STATES.EXPORTED,$rootScope.STATES.WITHDRAWN,$rootScope.STATES.REJECTED,$rootScope.STATES.CREDIT_FAIL] ,["ActiveTop1" ,"ActiveBottom1" ]);
        $scope.Active2 = OrderPanelFactory.createGrid("Active2" ,  [$rootScope.STATES.ACTIVE, $rootScope.STATES.TRIGGERED,$rootScope.STATES.FILLED,$rootScope.STATES.EXPORTED,$rootScope.STATES.WITHDRAWN,$rootScope.STATES.REJECTED,$rootScope.STATES.CREDIT_FAIL] ,["ActiveTop2" ,"ActiveBottom2" ]);
        $scope.Matched = OrderPanelFactory.createGrid("Matched" , [$rootScope.STATES.FILLED],	["Matched"]);
        $scope.Executed = OrderPanelFactory.createGrid("Executed" , [$rootScope.STATES.NOTIFIED],	["Executed"]);    
        $scope.Auto = OrderPanelFactory.createGrid("Auto" , [$rootScope.STATES.AUTO],	["Auto"]);    
        $scope.Completed = OrderPanelFactory.createGrid("Completed" ,  [$rootScope.STATES.WITHDRAWN,
                                                                         $rootScope.STATES.REJECTED,
                                                                         $rootScope.STATES.ACCEPTED,
                                                                         $rootScope.STATES.IMPORTED,
                                                                         $rootScope.STATES.CANCEL_REQUESTED,
                                                                         $rootScope.STATES.AMEND_REQUESTED,
                                                                         $rootScope.STATES.CREDIT_FAIL,
                                                                         $rootScope.STATES.EXPORTED,
                                                                         $rootScope.STATES.DRAFT, 
                                                                         $rootScope.STATES.ACTIVE, 
                                                                         $rootScope.STATES.TRIGGERED,
                                                                         $rootScope.STATES.FILLED,
                                                                         $rootScope.STATES.NOTIFIED, 
                                                                         $rootScope.STATES.AUTO,
                                                                         $rootScope.STATES.EXPIRED, 
                                                                         $rootScope.STATES.ARCHIVED ],	["Completed"]);    
        $scope.CustomerGrid = OrderPanelFactory.createGrid("Customer" ,  [$rootScope.STATES.WITHDRAWN,
                                                                          $rootScope.STATES.REJECTED,
                                                                          $rootScope.STATES.ACCEPTED,
                                                                          $rootScope.STATES.IMPORTED,
                                                                          $rootScope.STATES.EXPORTED,
                                                                          $rootScope.STATES.DRAFT, 
                                                                          $rootScope.STATES.ACTIVE, 
                                                                          $rootScope.STATES.TRIGGERED,
                                                                          $rootScope.STATES.FILLED,
                                                                          $rootScope.STATES.NOTIFIED, 
                                                                          $rootScope.STATES.AUTO,
                                                                          $rootScope.STATES.EXPIRED],	["Customer"]);    
        $scope.Fixing = OrderPanelFactory.createGrid("Fixing" ,  [$rootScope.STATES.WITHDRAWN,
                                                                         $rootScope.STATES.REJECTED,
                                                                         $rootScope.STATES.ACCEPTED,
                                                                         $rootScope.STATES.IMPORTED,
                                                                         $rootScope.STATES.CANCEL_REQUESTED,
                                                                         $rootScope.STATES.AMEND_REQUESTED,
                                                                         $rootScope.STATES.CREDIT_FAIL,
                                                                         $rootScope.STATES.EXPORTED,
                                                                         $rootScope.STATES.DRAFT,
                                                                         $rootScope.STATES.ACTIVE,
                                                                         $rootScope.STATES.TRIGGERED,
                                                                         $rootScope.STATES.FILLED,
                                                                         $rootScope.STATES.NOTIFIED,
                                                                         $rootScope.STATES.AUTO ],	["Fixing"]);

        $scope.exportStates = [$rootScope.STATES.ACTIVE, $rootScope.STATES.TRIGGERED, $rootScope.STATES.FILLED, $rootScope.STATES.EXPORTED, $rootScope.STATES.WITHDRAWN, $rootScope.STATES.REJECTED, $rootScope.STATES.AUTO, $rootScope.STATES.DRAFT ]; 
        var removeColumnsFromCustomerBlotter = function() {
        	var removeIndexes = [28,27,26,25,24,23,22,20,18,16,9,7,0];
        	 
        	 for(var i = 0; i < removeIndexes.length; i++){
        		 var index = removeIndexes[i];
        		 $scope.CustomerGrid.grids[0].columnDefs.splice(index, 1);
        	 }
        	$scope.CustomerGrid.grids[0].multiSelect = false;
        	$scope.CustomerGrid.grids[0].enableSelectAll = false;
        	$scope.CustomerGrid.grids[0].enableFullRowSelection = false;
        	$scope.CustomerGrid.grids[0].enableRowHeaderSelection = false;
        	
        	$scope.CustomerGrid.grids[0].columnDefs[0].minWidth = 60;
        	$scope.CustomerGrid.grids[0].columnDefs[1].minWidth = 35;
        	$scope.CustomerGrid.grids[0].columnDefs[2].minWidth = 60;
        	$scope.CustomerGrid.grids[0].columnDefs[3].minWidth = 35;
        	$scope.CustomerGrid.grids[0].columnDefs[7].minWidth = 60;
        	$scope.CustomerGrid.grids[0].columnDefs[9].minWidth = 40;
        	
        	var visibleMarketMonitorColums = [0,1, 2, 3, 8,10 ,12,14,15];
        	$scope.CustomerGrid.grids[0].columnDefs.forEach(function (value, i) {
        		$scope.CustomerGrid.grids[0].columnDefs[i].visible = (visibleMarketMonitorColums.indexOf(i) > -1);
             });       	
        	
        }
        
        var removeColumnsFromMonitorBlotter = function() {        	      	
        	//$scope.Active.grids[0].columnDefs.splice(30, 1);
        }
        removeColumnsFromCustomerBlotter();
        removeColumnsFromMonitorBlotter();
        $scope.Inbox = OrderPanelFactory.createGrid("Inbox" ,  [$rootScope.STATES.IMPORTED,$rootScope.STATES.ACTIVE,,$rootScope.STATES.AUTO, $rootScope.STATES.TRIGGERED,$rootScope.STATES.FILLED, $rootScope.STATES.CANCEL_REQUESTED, $rootScope.STATES.AMEND_REQUESTED],	["Inbox"]);
        $scope.Outbox = OrderPanelFactory.createGrid("Outbox" ,  [$rootScope.STATES.EXPORTED,$rootScope.STATES.WITHDRAWN,$rootScope.STATES.REJECTED ,$rootScope.STATES.FILLED ,$rootScope.STATES.ACCEPTED],["Outbox"]);    
        $rootScope.showNotifiedTab = true;
        $rootScope.showFilledTab = true;
        $rootScope.showReceivedTab = true;
        $rootScope.showSentTab = true;
        $rootScope.showMonitor1Tab = true;
        $rootScope.showMonitor2Tab = true;
        $rootScope.showMonitorTab = true;
		$rootScope.showDraftTab = true;
		$rootScope.showCancelledTab = true;
		$rootScope.showFixingTab = true;
		$rootScope.showFixingAggregateTab = true;
		$rootScope.showAutoTab = true;
        $rootScope.passAccounts = [];
        $rootScope.highlightOrdersList = [];
        $scope.passAccount = {};
        $scope.selected = {};
        $scope.selected.states = [$rootScope.STATES.ACTIVE, $rootScope.STATES.TRIGGERED,$rootScope.STATES.FILLED,$rootScope.STATES.EXPORTED,$rootScope.STATES.WITHDRAWN];
        $scope.resetBulkButtons = function() {
        	$scope.selectedStates =  [];
            $scope.showDraftBtnInMtr = false;
            $scope.showActivateBtnInMtr = false;
            $scope.showNotifyBtnInMtr = false;
            $scope.showAutoBtnInMtr = false;
            $scope.showWithdrawBtnInMtr = false;
            $scope.showPassBtnInMtr = false;
            $scope.showMatchBtnInMtr = false;
            $scope.showCancelBtn = false;
            $scope.showRejecteBtnInMtr = false;
            $scope.showArchiveBtnInMtr = false;
            $scope.showAcceptBtnInMtr = false;
            $scope.showManualBtnInMtr = false;
            $scope.showCancelRejecteBtnInMtr = false;
			$scope.showCancelAcceptBtnInMtr = false;
			$scope.showAmendRejecteBtnInMtr = false;
            $scope.showAmendAcceptBtnInMtr = false;
			$scope.showCreditOverrideRejectBtnInMtr = false;
            $scope.showCreditOverrideAcceptBtnInMtr = false;
            $scope.showSkipCreditCheckBtnInMtr = false;
        }
        $scope.resetBulkButtons();
        
    	$scope.showAuditDialog = function (orderId) {
            OrderService.getAuditEvents(orderId ,function (data, errorMsg) {
            	$scope.auditModalInstance = $modal.open({
                    controller: 'OrderAuditCtrl',
                    templateUrl: 'partials/orderAuditModal.html',
                    windowClass: 'audit-modal-window',
                    resolve: {
                      data: function () {                    
                          return data;
                      }
                    }
               });
            });
          };
          
                    
          $scope.exportData = function () {
        	  var brokerName;
        	  if($rootScope.org && $rootScope.org=='MAIN'){
        		  brokerName = $window.prompt("Please enter broker name", "");
        	  }else{
        		  var brokerName = $rootScope.org;
        	  }
        	  if(brokerName != null && brokerName != ""){          	  
        		  var queryParam = {"states": $scope.exportStates};
        		  OrderService.pollOrdersOfOrg( brokerName, queryParam ,function (data, errorMsg) {
        			  $scope.Completed.orders = getOrdersForStates(data ,$scope.exportStates);
               	      $scope.refreshGrid($scope.Completed);
               	      $timeout(function () {
               	    	  $scope.Completed.grids[0].exporterCsvFilename = brokerName+".csv";
               	    	  $scope.Completed.grids[0].gridApi.exporter.csvExport( 'all', 'all' );
             	      }, 100);
               	      if($rootScope.org && $rootScope.org=='MAIN'){
               	    	  $timeout(function () {
               	    		  $scope.getOrders(); 
               	    	  }, 350);
               	      }
          		});
        	  }
        	  else if(brokerName == ""){
        		  $window.confirm("Exporting can't be done as you did not enter broker name");  
        	  }
          };
        //================ Save State =================================
          $scope.state = {};
          
          $scope.saveState = function(gridApi) {
            $scope.state = gridApi.saveState.save();
            
            var prefStr = {};
            prefStr.omsPref = {};
            prefStr.omsPref.gridStateSetting = {};
            prefStr.omsPref.gridStateSetting[gridApi.grid.options.name] = JSON.stringify($scope.state);
            RESTService.saveUIPreference(prefStr, function (data, errorMsg) {
	    		 if(errorMsg!=null) {
	    			 console.log("Failed save state, Status" + data.status);
	    		 } else {
	    			 console.log("State save Successful.");
	    		 }
	    	 });
          };
          
          $rootScope.saveAutoOrderFlag = function () {
        	 $rootScope.showAutoAndManual = !$rootScope.showAutoAndManual;
        	 if( $rootScope.showAutoAndManual )
 	         {
 	        	$scope.Active.states.push($rootScope.STATES.AUTO);
 	        	$scope.Active1.states.push($rootScope.STATES.AUTO);
 	        	$scope.Active2.states.push($rootScope.STATES.AUTO);
 	        	$scope.refreshGrid($scope.Active);
 	        	$scope.refreshGrid($scope.Active1);
 	        	$scope.refreshGrid($scope.Active2);
 	         }else {
 	        	var index = -1;
                for(var i=0;i<$scope.Active.states.length;i++) {
                	if($scope.Active.states[i] == $rootScope.STATES.AUTO){
                		 index = i;
                		 break;
                	}
                }
                if(index != -1){
                	$scope.Active.states.splice(index, 1);
                	$scope.Active1.states.splice(index, 1);
                	$scope.Active2.states.splice(index, 1);
 	        	}
 	        	$scope.refreshGrid($scope.Active);
 	        	$scope.refreshGrid($scope.Active1);
                $scope.refreshGrid($scope.Active2);
 	         }
        	 var prefStr = {};
             prefStr.omsPref = {};
             prefStr.omsPref.gridStateSetting = {};
             prefStr.omsPref.gridStateSetting["AutoAndManual"] = JSON.stringify({expanded:$rootScope.showAutoAndManual});
             RESTService.saveUIPreference(prefStr, function (data, errorMsg) {
 	    		 if(errorMsg!=null) {
 	    			 console.log("Failed save state, Status" + data.status);
 	    		 } else {
 	    			 console.log("State save Successful.");
 	    		 }
 	    	 });
         };
         
          $scope.initializeUISettingListeners = function(grid) {
        		if(grid.name!= "ActiveTop" && grid.name != "ActiveBotom" && grid.gridApi) {
         			grid.gridApi.core.on.sortChanged = function (grid, sort) {
                    		$scope.saveState(grid.gridApi);	
                    };
     			}
        		if(grid.gridApi){
				grid.gridApi.colMovable.on.columnPositionChanged($scope,function(colDef, originalPosition, newPosition){
					 if($scope.isActiveTable()) {
						var state = $scope.Active.grids[0].gridApi.saveState.save();
						$scope.Active.grids[1].gridApi.saveState.restore( $scope, state );
						$scope.saveState($scope.Active.grids[0].gridApi);
					 }
                     else if($scope.isActive1Table()) {
						var state = $scope.Active1.grids[0].gridApi.saveState.save();
						$scope.Active1.grids[1].gridApi.saveState.restore( $scope, state );
						$scope.saveState($scope.Active1.grids[0].gridApi);
					 }
                     else if($scope.isActive2Table()) {
						var state = $scope.Active2.grids[0].gridApi.saveState.save();
						$scope.Active2.grids[1].gridApi.saveState.restore( $scope, state );
						$scope.saveState($scope.Active2.grids[0].gridApi);
					 } else if($scope.currentGridOptions){
						$scope.saveState($scope.currentGridOptions.gridApi);
					}
				}); 
     			
                grid.gridApi.core.on.columnVisibilityChanged( $scope, function (column) {
                	if(column.grid.api.grid.options.name == "ActiveTop") {
                		$scope.Active.grids[1].columnDefs.forEach(function(columnDef){
                			if(columnDef.field === column.field) {
                				columnDef.visible = column.visible;
                				$scope.Active.grids[1].gridApi.core.notifyDataChange(uiGridConstants.dataChange.COLUMN);
                			}
                		});
                		$scope.saveState(column.grid.api);
                   	} else if(column.grid.api.grid.options.name == "ActiveTop1") {
                		$scope.Active1.grids[1].columnDefs.forEach(function(columnDef){
                			if(columnDef.field === column.field) {
                				columnDef.visible = column.visible;
                				$scope.Active1.grids[1].gridApi.core.notifyDataChange(uiGridConstants.dataChange.COLUMN);
                			}
                		});
                		$scope.saveState(column.grid.api);
                   	} else if(column.grid.api.grid.options.name == "ActiveTop2") {
                		$scope.Active2.grids[1].columnDefs.forEach(function(columnDef){
                			if(columnDef.field === column.field) {
                				columnDef.visible = column.visible;
                				$scope.Active2.grids[1].gridApi.core.notifyDataChange(uiGridConstants.dataChange.COLUMN);
                			}
                		});
                		$scope.saveState(column.grid.api);
                   	} else if (column.grid.api.grid.options.name == "ActiveBotom" || column.grid.api.grid.options.name == "ActiveBotom1" || column.grid.api.grid.options.name == "ActiveBotom2") {
                		
                	} else {
                		$scope.saveState(column.grid.api);
                	}
                } );}
          } 
          
          $scope.restoreStateAndInitListeners = function(grid , data) {
  			if(data && data.omsPref && data.omsPref.gridStateSetting && data.omsPref.gridStateSetting[grid.name]) {
 				var savedState = JSON.parse(data.omsPref.gridStateSetting[grid.name]);
 				grid.gridApi.saveState.restore( $scope, savedState );
 				if(grid.name == "ActiveTop") {
 					$scope.Active.grids[1].gridApi.saveState.restore( $scope, savedState ); 					
 				} else if(grid.name == "ActiveTop1") {
                    $scope.Active1.grids[1].gridApi.saveState.restore( $scope, savedState );
                } else if(grid.name == "ActiveTop2") {
                    $scope.Active2.grids[1].gridApi.saveState.restore( $scope, savedState );
                }
 			}
  			$scope.initializeUISettingListeners(grid);
          }
          
          $scope.loadPreferences = function() {
              RESTService.loadUIPreference(function (data, errorMsg) {
         			$scope.restoreStateAndInitListeners($scope.Active.grids[0],data);
         			$scope.restoreStateAndInitListeners($scope.Active1.grids[0],data);
         			$scope.restoreStateAndInitListeners($scope.Active2.grids[0],data);
         			$scope.restoreStateAndInitListeners($scope.Draft.grids[0],data);
         			$scope.restoreStateAndInitListeners($scope.Cancelled.grids[0],data);
         			$scope.restoreStateAndInitListeners($scope.Matched.grids[0],data);
         			$scope.restoreStateAndInitListeners($scope.Executed.grids[0],data);
         			$scope.restoreStateAndInitListeners($scope.Completed.grids[0],data);
         			$scope.restoreStateAndInitListeners($scope.Outbox.grids[0],data);
         			$scope.restoreStateAndInitListeners($scope.Inbox.grids[0],data);
         			$scope.restoreStateAndInitListeners($scope.Auto.grids[0],data);	
         			$scope.restoreStateAndInitListeners($scope.CustomerGrid.grids[0],data);
         			$scope.restoreStateAndInitListeners($scope.Fixing.grids[0],data);
         			$scope.restoreStateAndInitListeners($scope.FixingAggregate.grids[0],data);
         			if(data && data.omsPref && data.omsPref.gridStateSetting && data.omsPref.gridStateSetting["OrderEntry"]) {
         				$rootScope.expandOrderEntry = JSON.parse(data.omsPref.gridStateSetting["OrderEntry"]).expanded;
         			}
         			if(data && data.omsPref && data.omsPref.gridStateSetting && data.omsPref.gridStateSetting["AutoAndManual"]) {
         				$rootScope.showAutoAndManual = JSON.parse(data.omsPref.gridStateSetting["AutoAndManual"]).expanded;
         			}
                    if(data && data.omsPref && data.omsPref.gridStateSetting && data.omsPref.gridStateSetting["Monitor1Search"]) {
         				$scope.search1 = JSON.parse(data.omsPref.gridStateSetting["Monitor1Search"]).expanded;
         			}
                    if(data && data.omsPref && data.omsPref.gridStateSetting && data.omsPref.gridStateSetting["Monitor2Search"]) {
         				$scope.search2 = JSON.parse(data.omsPref.gridStateSetting["Monitor2Search"]).expanded;
         			}
                    if(data && data.omsPref && data.omsPref.gridStateSetting && data.omsPref.gridStateSetting["FixingAggreSearch"]) {
         				$scope.searchFA = JSON.parse(data.omsPref.gridStateSetting["FixingAggreSearch"]).expanded;
         			}
         			if( $rootScope.showAutoAndManual ) {
 	        			$scope.Active.states.push($rootScope.STATES.AUTO);
 	        			$scope.Active1.states.push($rootScope.STATES.AUTO);
 	        			$scope.Active2.states.push($rootScope.STATES.AUTO);
 	        		}
         		 });             
              
          }
        //================ Order Edit ==================================
        $scope.setRowUpdateStarted = function(rowEntity , colDef) {
        	 rowEntity.updatedFields = {};
             rowEntity.updatedFields[colDef.field] = rowEntity[colDef.field];
             $rootScope.isLockedForUpdate = true;
        };
        
        $scope.setRowUpdateEnded = function(rowEntity , colDef) {
        	delete rowEntity.updatedFields;
            $rootScope.isLockedForUpdate = false;
       };
       
       function saveRow(gridApi, rowEntity) {
    	   var promise = $q.defer();
    	   gridApi.rowEdit.setSavePromise( rowEntity, promise.promise );
    	   promise.resolve();
    	 };
     //================================================== 
    	 
    	 
        $scope.registerGridAPIs = function(grid) {
        	
        	grid.onRegisterApi  = function (gridApi) {
     			grid.gridApi = gridApi;
     			grid.gridApi.grid.gridWidth = $scope.width;
                gridApi.core.handleWindowResize();
                gridApi.rowEdit.on.saveRow($scope, saveRow.bind(gridApi.grid, gridApi));
                gridApi.grid.registerRowsProcessor($scope.singleFilter, 100);
                gridApi.grid.registerRowBuilder(function (row, gridOptions) {
                    if (row.entity.newOrderFlag && !row.isNew) {
                        row.isNew = true;
                    } else if (row.entity.triggered && !row.isTriggered) {
                        row.isTriggered = true;
                    }
                });
                gridApi.edit.on.beginCellEdit($scope, function (rowEntity, colDef) {
                    if (rowEntity.orderId == "") {
                        return;
                    }
                    $scope.setRowUpdateStarted(rowEntity,colDef);
                });
                gridApi.edit.on.cancelCellEdit($scope, function (rowEntity, colDef) {
                	if (rowEntity.orderId == "") {
                		return;
                	}
                	 $scope.setRowUpdateEnded(rowEntity,colDef);
                });
                gridApi.edit.on.afterCellEdit($scope, function (rowEntity, colDef) {
                	if (rowEntity.orderId == "") {
                		return;
                	}
                	var dataChanged = false;
                	var newValue = null;
                	if(rowEntity.updatedFields) {
                		
                		var oldValue = rowEntity.updatedFields[colDef.field];
                		newValue = rowEntity[colDef.field];
                		if(oldValue!=newValue) {
                			//Data has changed let saveRow update the order
                			dataChanged = true;
                			if((colDef.field =="dealtAmt" || colDef.field =="baseAmt" || colDef.field =="fillPrice" || colDef.field =="orderPrice" || colDef.field =="fixingRate")
                					&& (newValue == "null" || newValue <= 0.0)) {
                				if(colDef.field =="fixingRate"){
                				    $scope.notifier.notify('error', (colDef.fieldName?colDef.fieldName :colDef.name) + " update failed, Reason:Invalid value");
                				}else{
                				    $scope.notifier.notify('error', "Order ( " + rowEntity.orderId + " ) " + (colDef.fieldName?colDef.fieldName :colDef.name) + " update failed, Reason:Invalid value");
                				}
                				dataChanged = false;
                			}
                			if(!newValue && colDef.field =="fillPrice") {
                				newValue = -1.0;
                			}
                		} 
                	}
                	
                	if(!dataChanged) {
                		$scope.setRowUpdateEnded(rowEntity,colDef);
                	} else {
                	  if(!$scope.isFixingAggregateTable()){
                        $rootScope.orderService.updateOrder(JSON.stringify(Utils.getUpdatedOrder(rowEntity,colDef ,newValue )), function (data, errorMsg) {
                            $timeout(function () {
                            	$scope.setRowUpdateEnded(rowEntity,colDef);
                            }, 1500);
                        	
                            if(!$rootScope.webSocket) {
	                            if (!errorMsg) {
	                                $scope.notifier.notify('success', "Order ( " + rowEntity.orderId + " ) " + (colDef.fieldName?colDef.fieldName :colDef.name) + " updated to " + newValue);
	                            } else {
	                                $scope.notifier.notify('error', "Order ( " + rowEntity.orderId + " ) " + (colDef.fieldName?colDef.fieldName :colDef.name) + " update failed");
	                            }
                            }
                        });
                      }else{
                        var currentTimeWithUserPref = new Date(moment.utc());//This is in browsers timestamp
                        if(currentTimeWithUserPref.valueOf() < rowEntity.fixingTime) {
                                $scope.setRowUpdateEnded(rowEntity,colDef);
                                $scope.notifier.notify('error', "Fixing Rate update not allowed before Fixing Time.");

                        }
                        else
                        {
                            $rootScope.orderService.updateAggregateOrder(JSON.stringify(Utils.getUpdatedOrder(rowEntity,colDef ,newValue )), function (data, errorMsg) {
                                $timeout(function () {
                                    $scope.setRowUpdateEnded(rowEntity,colDef);
                                }, 1500);

                                if(!$rootScope.webSocket) {
                                    if (!errorMsg) {
                                        $scope.notifier.notify('success', "Orders ( " + rowEntity.orderIds + " ) " + (colDef.fieldName?colDef.fieldName :colDef.name) + " updated to " + newValue);
                                    } else {
                                        $scope.notifier.notify('error', "Orders ( " + rowEntity.orderIds + " ) " + (colDef.fieldName?colDef.fieldName :colDef.name) + " update failed");
                                    }
                                }
                            });
                        }
                      }
                	}
                	delete rowEntity.updatedFields;
                });
                
                if(grid.name == "ActiveTop") {
                    $scope.currentGridOptions = $scope.Active.grids[0];
                	$rootScope.activeTopInitialized = true;
                	gridApi.selection.on.rowSelectionChanged($scope,function(row){
                		$scope.rowSelected();
                      });
               	 	gridApi.selection.on.rowSelectionChangedBatch($scope, function(rows){
               		 	if(rows && rows.length >0 && rows[0].isSelected) {
               		 		rows.forEach(function(row) {
               		 			if(row.entity.orderId=="") {
               		 			 row.setSelected(false);
               		 			}
               		 		})
               		 		$scope.Active.grids[1].gridApi.grid.rows.forEach(function (row) {
                                if ( !row.isSelected && row.enableSelection !== false &&row.entity.orderId!=""){
                                  row.setSelected(true);
                                }
                              });
               		 	}else {
               		 		$scope.Active.grids[1].gridApi.selection.clearSelectedRows();
               		 	}
               		 	$scope.rowSelected();
                      });
                } else if(grid.name == "ActiveBottom" || grid.name == "ActiveBottom1" || grid.name == "ActiveBottom2") {
                	$rootScope.activeBottomInitialized = true;
                	gridApi.selection.on.rowSelectionChanged($scope,function(row){
                		$scope.rowSelected();
                      });
                } else if(grid.name == "ActiveTop1") {
                    $scope.currentGridOptions = $scope.Active1.grids[0];
                	$rootScope.activeTopInitialized = true;
                	gridApi.selection.on.rowSelectionChanged($scope,function(row){
                		$scope.rowSelected();
                      });
               	 	gridApi.selection.on.rowSelectionChangedBatch($scope, function(rows){
               		 	if(rows && rows.length >0 && rows[0].isSelected) {
               		 		rows.forEach(function(row) {
               		 			if(row.entity.orderId=="") {
               		 			 row.setSelected(false);
               		 			}
               		 		})
               		 		$scope.Active1.grids[1].gridApi.grid.rows.forEach(function (row) {
                                if ( !row.isSelected && row.enableSelection !== false &&row.entity.orderId!=""){
                                  row.setSelected(true);
                                }
                              });
               		 	}else {
               		 		$scope.Active1.grids[1].gridApi.selection.clearSelectedRows();
               		 	}
               		 	$scope.rowSelected();
                      });
                } else if(grid.name == "ActiveTop2") {
                    $scope.currentGridOptions = $scope.Active.grids[0];
                	$rootScope.activeTopInitialized = true;
                	gridApi.selection.on.rowSelectionChanged($scope,function(row){
                		$scope.rowSelected();
                      });
               	 	gridApi.selection.on.rowSelectionChangedBatch($scope, function(rows){
               		 	if(rows && rows.length >0 && rows[0].isSelected) {
               		 		rows.forEach(function(row) {
               		 			if(row.entity.orderId=="") {
               		 			 row.setSelected(false);
               		 			}
               		 		})
               		 		$scope.Active2.grids[1].gridApi.grid.rows.forEach(function (row) {
                                if ( !row.isSelected && row.enableSelection !== false &&row.entity.orderId!=""){
                                  row.setSelected(true);
                                }
                              });
               		 	}else {
               		 		$scope.Active2.grids[1].gridApi.selection.clearSelectedRows();
               		 	}
               		 	$scope.rowSelected();
                      });
                } else {
                	gridApi.selection.on.rowSelectionChangedBatch($scope, function(rows){
                		$scope.rowSelected();
                	});
                	gridApi.selection.on.rowSelectionChanged($scope,function(row){
                		$scope.rowSelected();
                	});
                }
             };
             

        }
        
        $scope.registerGridAPIs($scope.Draft.grids[0]);
        $scope.registerGridAPIs($scope.Cancelled.grids[0]);
        $scope.registerGridAPIs($scope.Active.grids[0]);
        $scope.registerGridAPIs($scope.Active.grids[1]);
        $scope.registerGridAPIs($scope.Active1.grids[0]);
        $scope.registerGridAPIs($scope.Active1.grids[1]);
        $scope.registerGridAPIs($scope.Active2.grids[0]);
        $scope.registerGridAPIs($scope.Active2.grids[1]);
        $scope.registerGridAPIs($scope.Matched.grids[0]);
        $scope.registerGridAPIs($scope.Executed.grids[0]);
        $scope.registerGridAPIs($scope.Auto.grids[0]);
        $scope.registerGridAPIs($scope.Completed.grids[0]);
        $scope.registerGridAPIs($scope.Inbox.grids[0]);
        $scope.registerGridAPIs($scope.Outbox.grids[0]);
        $scope.registerGridAPIs($scope.CustomerGrid.grids[0]);
        $scope.registerGridAPIs($scope.Fixing.grids[0]);
        $scope.registerGridAPIs($scope.FixingAggregate.grids[0]);
        
        
        
        $scope.Executed.grids[0].columnDefs[23].visible = true;
        $scope.Completed.grids[0].columnDefs[23].visible = true;
        $scope.Outbox.grids[0].columnDefs[26].visible = true;
        $scope.Inbox.grids[0].columnDefs[25].visible = true;
        $scope.Inbox.grids[0].columnDefs[27].visible = true;
        $scope.Completed.grids[0].columnDefs[28].visible = true;
        $scope.Auto.grids[0].columnDefs[28].visible = true;
        $scope.Executed.grids[0].columnDefs[28].visible = true;
        $scope.Completed.grids[0].columnDefs[12].width = 110;
        $scope.Inbox.grids[0].columnDefs[12].width = 110;
        $scope.Fixing.grids[0].columnDefs[30].visible = true;
        $scope.Fixing.grids[0].columnDefs[31].visible = true;
        $scope.Fixing.grids[0].columnDefs[32].visible = true;
        $scope.Fixing.grids[0].columnDefs[34].visible = true;

        //======================================Global Variables ===================================================================
        
        $scope.currentGridOptions = $scope.Active.grids[0];
        $scope.alertSound = new Audio('data/alert.mp3');
        $scope.incomingSound = new Audio('data/incoming.mp3');
        $scope.newSound = new Audio('data/draft.mp3');
        $scope.notifier = new NotificationManager($rootScope);
        $scope.width = 1700;
        $scope.scollToMarket = true;
        $rootScope.isLockedForUpdate = false;
        $rootScope.stopUpdates = false;
        $rootScope.isAnimationInProgress = false;
        $rootScope.rates = {};
        $scope.inboxUnreadCount = 0;
        $scope.newUnreadCount = 0;
        $scope.notifiedUnreadCount = 0;
        
       //======================== Jquery methods =============================================================================== 
        $(document).ready(function () {
        	var currWidth = $(window).width();
        	if(currWidth < $rootScope.minimumUIWidth) {
        		currWidth = Number($rootScope.minimumUIWidth) + 25
        	}
            $scope.width = currWidth - 30;
            $scope.height = $(window).height() - 290;
            var rowsHeight = $scope.height / 24.25
            $scope.rowsToShow = Math.floor(rowsHeight/2)*2;
        });
        var w = angular.element($window);

        w.bind('resize', function () {
        	windowResize();            
        });
        
        $scope.unlock = function() {
        	$rootScope.stopUpdates=!$rootScope.stopUpdates;
        };
   
        var windowResize = function() { 
        	var currWidth = $(window).width();
        	if(currWidth < $rootScope.minimumUIWidth) {
        		currWidth = Number($rootScope.minimumUIWidth) + 25
        	}
            $scope.width = currWidth - 30;
            $scope.height = $(window).height() - 290;
            var rowsHeight = $scope.height / 24.25
            $scope.rowsToShow = Math.floor(rowsHeight/2)*2;
            $scope.handleWindowResize();
        }
        
        $scope.tabChanged = function (gridOptions) {
        	$rootScope.stopUpdates = false;
        	$rootScope.isLockedForUpdate = false;
        	$scope.resetBulkButtons();
        	var currWidth = $(window).width();
        	if(currWidth < $rootScope.minimumUIWidth) {
        		currWidth = Number($rootScope.minimumUIWidth) + 25
        	}
            $scope.width = currWidth - 30;
            $scope.height = $(window).height() - 290;
            $scope.currentGridOptions = gridOptions;
            $scope.rowSelected();
            $scope.refreshView();
            $scope.handleWindowResize();
            if($scope.isInboxTable()) {
            	$scope.inboxUnreadCount = 0;
            }
            if($scope.isDraftTable()) {
            	$scope.newUnreadCount = 0;
            }
            if($scope.isExecutedTable()) {
            	$scope.notifiedUnreadCount = 0;
            }
            $scope.Active.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;           
        	$scope.Active.grids[1].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;
            $scope.Active1.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;
        	$scope.Active1.grids[1].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;
            $scope.Active2.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;
        	$scope.Active2.grids[1].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;
        	$scope.Matched.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;
        	$scope.Executed.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;
        	$scope.Auto.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;
        	$scope.Completed.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;
        	$scope.Outbox.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;
        	$scope.Draft.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;
        	$scope.Cancelled.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;
        	$scope.Inbox.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;
        	$scope.Fixing.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;
        	//$scope.FixingAggregate.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 10;
        };

        $scope.handleWindowResize = function () {
                if($scope.isActiveTable()) {
                    $scope.scollToMarket = true;
                    $scope.Active.grids[0].gridApi.core.handleWindowResize();
                    $scope.Active.grids[1].gridApi.core.handleWindowResize();
                } else if($scope.isActive1Table()) {
                    $scope.scollToMarket = true;
                    $scope.Active1.grids[0].gridApi.core.handleWindowResize();
                    $scope.Active1.grids[1].gridApi.core.handleWindowResize();
                } else if($scope.isActive2Table()) {
                    $scope.scollToMarket = true;
                    $scope.Active2.grids[0].gridApi.core.handleWindowResize();
                    $scope.Active2.grids[1].gridApi.core.handleWindowResize();
                } else if ($scope.currentGridOptions && !$scope.isFixingAggregateTable()) {
                    $scope.currentGridOptions.gridApi.grid.handleWindowResize();
                }
        }
        
     
        $scope.bringToMarket = function () {
            if (!$rootScope.isLockedForUpdate && !$rootScope.stopUpdates) {
               if($scope.isActiveTable()) {
            	if ($scope.Active.grids[0].data.length > 0) {
            		   $scope.Active.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = ($scope.Active.grids[0].data.length - 1) * 25;
                 }
                if ($scope.Active.grids[1].data.length > 0) {
                    $scope.Active.grids[1].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 0;
                }
               }
            else if($scope.isActive1Table()) {
            	if ($scope.Active1.grids[0].data.length > 0) {
            		   $scope.Active1.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = ($scope.Active1.grids[0].data.length - 1) * 25;
                 }
                if ($scope.Active1.grids[1].data.length > 0) {
                    $scope.Active1.grids[1].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 0;
                }
               }
            else if($scope.isActive2Table()) {
            	if ($scope.Active2.grids[0].data.length > 0) {
            		   $scope.Active2.grids[0].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = ($scope.Active2.grids[0].data.length - 1) * 25;
                 }
                if ($scope.Active2.grids[1].data.length > 0) {
                    $scope.Active2.grids[1].gridApi.grid.element[0].getElementsByClassName("ui-grid-viewport")[0].scrollTop = 0;
                }
               }
            }
        };
        var rateTimerEvent, orderTimerEvent , heartbeatTimerEvent, bringToMarketEvent, customerOrderTimerEvent;

        var bringToMarketTimer = function () {
           bringToMarketEvent = $timeout(function () {
                $scope.bringToMarket();
                bringToMarketTimer();
            }, 10000);
        };



//========================== All Grid Model , View Handling =======================================
       /**
        * Refresh the data model for grids orders
        */
	   $scope.refreshDataInGrids = function(newData) {
	    	
	    	if(!newData || $rootScope.isLockedForUpdate) {
	    		return;
	    	}
	       if($rootScope.customer) {
	    	   $scope.CustomerGrid.orders = getOrdersForStates(newData ,$scope.CustomerGrid.states);
	    	   $scope.CustomerGrid.orders.forEach(function(order) {
	    		   if(order.orderState!="NOTIFIED") {
	    			   delete order.fillPrice;
	    			   delete order.filledAmount;
	    		   }
	      	  	});
        	   $scope.refreshGrid($scope.CustomerGrid);
	    	   return;
	       }
           if($scope.isDraftTable()) {
        	   $scope.Draft.orders = getOrdersForStates(newData ,$scope.Draft.states);
        	   $scope.refreshGrid($scope.Draft);
           } else if($scope.isCancelledTable()) {
        	   $scope.Cancelled.orders = getOrdersForStates(newData ,$scope.Cancelled.states);
        	   $scope.refreshGrid($scope.Cancelled);
           } else if($scope.isFixingTable()) {
        	   $scope.Fixing.orders = getOrdersForStates(newData ,$scope.Fixing.states);
        	   $scope.refreshGrid($scope.Fixing);
           } else if($scope.isActiveTable()) {
        	   $scope.Active.orders = getOrdersForStates(newData ,$scope.Active.states);
        	   $scope.refreshGrid($scope.Active);
           } else if($scope.isActive1Table()) {
        	   $scope.Active1.orders = getOrdersForStates(newData ,$scope.Active1.states);
        	   $scope.refreshGrid($scope.Active1);
           } else if($scope.isActive2Table()) {
        	   $scope.Active2.orders = getOrdersForStates(newData ,$scope.Active2.states);
        	   $scope.refreshGrid($scope.Active2);
           } else if($scope.isMatchedTable()) {
        	   var matchedOrders  = getOrdersForStates(newData ,$scope.Matched.states);
   	    	   angular.copy(matchedOrders, $scope.Matched.orders);
   	    	   $scope.refreshGrid($scope.Matched);
           } else if($scope.isExecutedTable()) {
        	   $scope.Executed.orders = getOrdersForStates(newData ,$scope.Executed.states);
        	   $scope.refreshGrid($scope.Executed);
           } else if($scope.isAutoTable()) {
        	   $scope.Auto.orders = getOrdersForStates(newData ,$scope.Auto.states);
        	   $scope.refreshGrid($scope.Auto);
           } else if($scope.isCompletedTable()) {
        	   $scope.Completed.orders = getOrdersForStates(newData ,$scope.Completed.states);
        	   $scope.refreshGrid($scope.Completed);
           } else if($scope.isInboxTable()) {
        	   $scope.Inbox.orders = getOrdersForStates(newData ,$scope.Inbox.states);
        	   $scope.refreshGrid($scope.Inbox);
           } else if($scope.isOutboxTable()) {
        	   $scope.Outbox.orders = getOrdersForStates(newData ,$scope.Outbox.states);
        	   $scope.refreshGrid($scope.Outbox);
           } else {
        	   $scope.Active.orders = getOrdersForStates(newData ,$scope.Active.states);
        	   $scope.refreshGrid($scope.Active);
           }
	    	
	    	//$scope.populateRatesInOrders($scope.Active.orders);
	    	
	    };

/**
        * Refresh the data model for aggregate grid orders
        */
	   $scope.refreshDataInAggregateGrid = function(newData) {

	    	if(!newData || $rootScope.isLockedForUpdate) {
	    		return;
	    	}
            if($scope.isFixingAggregateTable()) {
        	   $scope.FixingAggregate.orders = newData;
        	   $scope.refreshGrid($scope.FixingAggregate);
           }
	    };
        
	    $scope.checkAndUpdateGridData = function(grid , data, refreshGridData) {
	    	if($rootScope.isLockedForUpdate || $rootScope.isAnimationInProgress) {
	    		$timeout(function () {
	    			$scope.checkAndUpdateGridData(grid, data, refreshGridData);
	            }, 1000);
	    	} else {
	    		refreshGridData(grid ,data);
	    	}
	   } 
	    
	  
	    
	   $scope.getCurrentStates = function() {
		   if($rootScope.org =='MAIN') {
			   return $scope.selected.states;
		   }
           if($scope.isDraftTable()) {
        	   return $scope.Draft.states;
           } else if($scope.isCancelledTable()) {
        	   return $scope.Cancelled.states;
           } else if($scope.isFixingTable()) {
        	   return $scope.Fixing.states;
           } else if($scope.isFixingAggregateTable()) {
        	   return $scope.FixingAggregate.states;
           } else if($scope.isActiveTable()) {
        	   return $scope.Active.states;
           } else if($scope.isActive1Table()) {
        	   return $scope.Active1.states;
           } else if($scope.isActive2Table()) {
        	   return $scope.Active2.states;
           } else if($scope.isMatchedTable()) {
        	   return $scope.Matched.states;
           } else if($scope.isExecutedTable()) {
        	   return $scope.Executed.states;
           } else if($scope.isAutoTable()) {
        	   return $scope.Auto.states;
           } else if($scope.isCompletedTable()) {
        	   return $scope.Completed.states;
           } else if($scope.isInboxTable()) {
        	   return $scope.Inbox.states;
           } else if($scope.isOutboxTable()) {
        	   return $scope.Outbox.states;
           } else if($scope.isCustomerTable()) {
        	   return $scope.CustomerGrid.states;
           }else {
        	   return $scope.Active.states;
           }
	   }
       /**
        * Refresh the view for grids. Optimize to repaint only current tab
        */ 
	   $scope.refreshView = function() {
		   
           	if ($rootScope.isLockedForUpdate || $rootScope.isAnimationInProgress) {
               return;
           	}
            if($scope.isDraftTable()) {
            	$scope.refreshGrid($scope.Draft);
            } else if($scope.isCancelledTable()) {
            	$scope.refreshGrid($scope.Cancelled);
            } else if($scope.isFixingTable()) {
            	$scope.refreshGrid($scope.Fixing);
            } else if($scope.isFixingAggregateTable()) {
            	$scope.refreshGrid($scope.FixingAggregate);
            } else if($scope.isActiveTable()) {
            	 $scope.refreshGrid($scope.Active);
            } else if($scope.isActive1Table()) {
            	 $scope.refreshGrid($scope.Active1);
            } else if($scope.isActive2Table()) {
            	 $scope.refreshGrid($scope.Active2);
            } else if($scope.isMatchedTable()) {
            	 $scope.refreshGrid($scope.Matched);
            } else if($scope.isExecutedTable()) {
            	$scope.refreshGrid($scope.Executed);
            } else if($scope.isAutoTable()) {
            	 $scope.refreshGrid($scope.Auto);
            } else if($scope.isCompletedTable()) {
            	 $scope.refreshGrid($scope.Completed);
            } else if($scope.isInboxTable()) {
            	 $scope.refreshGrid($scope.Inbox);
            } else if($scope.isOutboxTable()) {
            	 $scope.refreshGrid($scope.Outbox);
            } 
   	    };
      
	    
	    /***
	     *  Calculate sort priority of market monitor. Calculate Percentage from Market
	     */
        $scope.populateRatesInOrders = function (orders) {
            for (var i in orders) {
                var order = orders[i];
                order = Utils.calculatePercentage(order);
            }
        };
        
	    /***
	     *  Calculate sort priority of market monitor. Calculate Percentage from Market
	     */
        $scope.populateRatesInMarketMonitor = function () {
        	if( $rootScope.isLockedForUpdate) {
        		return;
        	}
        	//var tempActiveOrders = [];
            var aboveMarket = [];
            var belowMarket = [];
           if($scope.isActiveTable()) {
            if( $scope.search ){
               var searchTerms = $scope.search.split(" ");
               for (var i in $scope.Active.orders) {
                 var order = $scope.Active.orders[i];
                 if($scope.matchFound(searchTerms, order, $scope.searchColumns)){
                     Utils.calculatePercentage(order);
                     if (order.percentage < 100.00) {
                         belowMarket.push(order);
                     } else {
                         aboveMarket.push(order);
                     }

                     for(var i = $rootScope.highlightOrdersList.length -1; i >= 0 ; i--){
                        var orderToBeHighlighted = $rootScope.highlightOrdersList[i];
                        if(orderToBeHighlighted.orderId == order.orderId) {
                            if(orderToBeHighlighted.state == 'TRIGGERED') {
                                order.triggered = true;
                            } else if (orderToBeHighlighted.state == 'NEW') {
                                order.newOrderFlag = true;
                            }
                            $rootScope.highlightOrdersList.splice(i,1);
                        }
                     }
                 }
               }
            }
            else
            {
                for (var i in $scope.Active.orders) {
                    var order = $scope.Active.orders[i];
                    Utils.calculatePercentage(order);
                    if (order.percentage < 100.00) {
                        belowMarket.push(order);
                    } else {
                        aboveMarket.push(order);
                    }

                    for(var i = $rootScope.highlightOrdersList.length -1; i >= 0 ; i--){
                        var orderToBeHighlighted = $rootScope.highlightOrdersList[i];
                        if(orderToBeHighlighted.orderId == order.orderId) {
                            if(orderToBeHighlighted.state == 'TRIGGERED') {
                                order.triggered = true;
                            } else if (orderToBeHighlighted.state == 'NEW') {
                                order.newOrderFlag = true;
                            }
                            $rootScope.highlightOrdersList.splice(i,1);
                        }
                    }
                }
            }
            var aboveMarketWithoutFixingOrder =[];
        	for (var j in aboveMarket) {
        		if(aboveMarket[j].orderType!= "FIXING"){
        			aboveMarketWithoutFixingOrder.push(aboveMarket[j]);
        		}
        	}
            var belowMarketWithoutFixingOrder =[];
        	for (var j in belowMarket) {
        		if(belowMarket[j].orderType != "FIXING"){
        			belowMarketWithoutFixingOrder.push(belowMarket[j]);
        		}
        	}
            //Add dummy rows if orders are less than grid row count
            Utils.addPadding(aboveMarketWithoutFixingOrder, true , $scope.rowsToShow/2);
            Utils.addPadding(belowMarketWithoutFixingOrder, false,$scope.rowsToShow/2);

            $scope.Active.grids[0].data = aboveMarketWithoutFixingOrder;
            $scope.Active.grids[1].data = belowMarketWithoutFixingOrder;
            $scope.Active.grids[0].gridApi.core.notifyDataChange(uiGridConstants.dataChange.ROW);
            $scope.Active.grids[1].gridApi.core.notifyDataChange(uiGridConstants.dataChange.ROW);

           } else if($scope.isActive1Table()) {
            if( $scope.search1 ){
               var searchTerms = $scope.search1.split(" ");
               for (var i in $scope.Active1.orders) {
                 var order = $scope.Active1.orders[i];
                 if($scope.matchFound(searchTerms, order, $scope.searchColumns)){
                     Utils.calculatePercentage(order);
                     if (order.percentage < 100.00) {
                         belowMarket.push(order);
                     } else {
                         aboveMarket.push(order);
                     }

                     for(var i = $rootScope.highlightOrdersList.length -1; i >= 0 ; i--){
                        var orderToBeHighlighted = $rootScope.highlightOrdersList[i];
                        if(orderToBeHighlighted.orderId == order.orderId) {
                            if(orderToBeHighlighted.state == 'TRIGGERED') {
                                order.triggered = true;
                            } else if (orderToBeHighlighted.state == 'NEW') {
                                order.newOrderFlag = true;
                            }
                            $rootScope.highlightOrdersList.splice(i,1);
                        }
                     }
                 }
               }
            }
            else
            {
                for (var i in $scope.Active1.orders) {
                    var order = $scope.Active1.orders[i];
                    Utils.calculatePercentage(order);
                    if (order.percentage < 100.00) {
                        belowMarket.push(order);
                    } else {
                        aboveMarket.push(order);
                    }

                    for(var i = $rootScope.highlightOrdersList.length -1; i >= 0 ; i--){
                        var orderToBeHighlighted = $rootScope.highlightOrdersList[i];
                        if(orderToBeHighlighted.orderId == order.orderId) {
                            if(orderToBeHighlighted.state == 'TRIGGERED') {
                                order.triggered = true;
                            } else if (orderToBeHighlighted.state == 'NEW') {
                                order.newOrderFlag = true;
                            }
                            $rootScope.highlightOrdersList.splice(i,1);
                        }
                    }
                }
            }
            var aboveMarketWithoutFixingOrder =[];
        	for (var j in aboveMarket) {
        		if(aboveMarket[j].orderType!= "FIXING"){
        			aboveMarketWithoutFixingOrder.push(aboveMarket[j]);
        		}
        	}
            var belowMarketWithoutFixingOrder =[];
        	for (var j in belowMarket) {
        		if(belowMarket[j].orderType != "FIXING"){
        			belowMarketWithoutFixingOrder.push(belowMarket[j]);
        		}
        	}
            //Add dummy rows if orders are less than grid row count
            Utils.addPadding(aboveMarketWithoutFixingOrder, true , $scope.rowsToShow/2);
            Utils.addPadding(belowMarketWithoutFixingOrder, false,$scope.rowsToShow/2);

            $scope.Active1.grids[0].data = aboveMarketWithoutFixingOrder;
            $scope.Active1.grids[1].data = belowMarketWithoutFixingOrder;
            $scope.Active1.grids[0].gridApi.core.notifyDataChange(uiGridConstants.dataChange.ROW);
            $scope.Active1.grids[1].gridApi.core.notifyDataChange(uiGridConstants.dataChange.ROW);

           } else if($scope.isActive2Table()) {
            if( $scope.search2 ){
               var searchTerms = $scope.search2.split(" ");
               for (var i in $scope.Active2.orders) {
                 var order = $scope.Active2.orders[i];
                 if($scope.matchFound(searchTerms, order, $scope.searchColumns)){
                     Utils.calculatePercentage(order);
                     if (order.percentage < 100.00) {
                         belowMarket.push(order);
                     } else {
                         aboveMarket.push(order);
                     }

                     for(var i = $rootScope.highlightOrdersList.length -1; i >= 0 ; i--){
                        var orderToBeHighlighted = $rootScope.highlightOrdersList[i];
                        if(orderToBeHighlighted.orderId == order.orderId) {
                            if(orderToBeHighlighted.state == 'TRIGGERED') {
                                order.triggered = true;
                            } else if (orderToBeHighlighted.state == 'NEW') {
                                order.newOrderFlag = true;
                            }
                            $rootScope.highlightOrdersList.splice(i,1);
                        }
                     }
                 }
               }
            }
            else
            {
                for (var i in $scope.Active2.orders) {
                    var order = $scope.Active2.orders[i];
                    Utils.calculatePercentage(order);
                    if (order.percentage < 100.00) {
                        belowMarket.push(order);
                    } else {
                        aboveMarket.push(order);
                    }

                    for(var i = $rootScope.highlightOrdersList.length -1; i >= 0 ; i--){
                        var orderToBeHighlighted = $rootScope.highlightOrdersList[i];
                        if(orderToBeHighlighted.orderId == order.orderId) {
                            if(orderToBeHighlighted.state == 'TRIGGERED') {
                                order.triggered = true;
                            } else if (orderToBeHighlighted.state == 'NEW') {
                                order.newOrderFlag = true;
                            }
                            $rootScope.highlightOrdersList.splice(i,1);
                        }
                    }
                }
            }
            var aboveMarketWithoutFixingOrder =[];
        	for (var j in aboveMarket) {
        		if(aboveMarket[j].orderType!= "FIXING"){
        			aboveMarketWithoutFixingOrder.push(aboveMarket[j]);
        		}
        	}
            var belowMarketWithoutFixingOrder =[];
        	for (var j in belowMarket) {
        		if(belowMarket[j].orderType != "FIXING"){
        			belowMarketWithoutFixingOrder.push(belowMarket[j]);
        		}
        	}
            //Add dummy rows if orders are less than grid row count
            Utils.addPadding(aboveMarketWithoutFixingOrder, true , $scope.rowsToShow/2);
            Utils.addPadding(belowMarketWithoutFixingOrder, false,$scope.rowsToShow/2);

            $scope.Active2.grids[0].data = aboveMarketWithoutFixingOrder;
            $scope.Active2.grids[1].data = belowMarketWithoutFixingOrder;
            $scope.Active2.grids[0].gridApi.core.notifyDataChange(uiGridConstants.dataChange.ROW);
            $scope.Active2.grids[1].gridApi.core.notifyDataChange(uiGridConstants.dataChange.ROW);

           }
        };
        
     
        
        $scope.refreshGrid = function(grid) {
        	//console.log(".refreshGrid: Refreshing grid-> " + grid.name );
        	if(grid.name == $scope.Active.name) {
        		$scope.populateRatesInMarketMonitor();
        	} else if(grid.name == $scope.Active1.name) {
                $scope.populateRatesInMarketMonitor();
            } else if(grid.name == $scope.Active2.name) {
                $scope.populateRatesInMarketMonitor();
            } else {
        		for(var i in grid.grids) {
        			if($scope.isOutboxTable()) {
        				var tempOrders =[];
        				grid.orders.forEach(function(order) {
        					if(order.orderState == $rootScope.STATES.FILLED) {
        						if(order.toOrg) {
        							tempOrders.push(order);
        						}
        					} else {
        						tempOrders.push(order);
        					}
        				});
        				grid.grids[i].data = tempOrders;
        			}else if($scope.isInboxTable()) {
        				var tempOrders =[];
        				grid.orders.forEach(function(order) {
        					if(order.orderState == $rootScope.STATES.FILLED || order.orderState == $rootScope.STATES.ACTIVE ||order.orderState == $rootScope.STATES.TRIGGERED) {
        						if(order.fromOrg) {
        							tempOrders.push(order);
        						}
        					} else {
        						tempOrders.push(order);
        					}
        				});
        				grid.grids[i].data = tempOrders
        			} else if($scope.isFixingTable()) {
        				 for (var j in grid.orders) {
        		                var order = grid.orders[j];
        		                if(order.fillPrice == null){
        		                	delete order.fillPrice;
        		                }
        		            }
        		         var tempOrders =[];
                         grid.orders.forEach(function(order) {
                            if(order.orderType == "FIXING"){
                                tempOrders.push(order);
                            }
                         });
        				grid.grids[i].data = tempOrders;
        			} else if($scope.isFixingAggregateTable()) {
        		         var tempOrders =[];
                         grid.orders.forEach(function(order) {
                            tempOrders.push(order);
                         });
        				grid.grids[i].data = tempOrders;
                    } else if($scope.isCancelledTable() || $scope.isCompletedTable() || $scope.isExecutedTable()) {
        				 for (var j in grid.orders) {
        		                var order = grid.orders[j];
        		                if(order.fillPrice == null){
        		                	delete order.fillPrice;
        		                }
        		            }
        				grid.grids[i].data = grid.orders;
        			}  else {
        				 for (var j in grid.orders) {
        		                var order = grid.orders[j];
        		                if(order.fillPrice == null){
        		                	delete order.fillPrice;
        		                }
        		            }
                         var tempOrders =[];
                         grid.orders.forEach(function(order) {
                            if(order.orderType != "FIXING"){
                                tempOrders.push(order);
                            }
                         });
        				grid.grids[i].data = tempOrders;
        			}
        			
    				if($scope.currentGridOptions && $scope.currentGridOptions.gridApi) {
    					$scope.currentGridOptions.gridApi.core.notifyDataChange(uiGridConstants.dataChange.ROW);
    					$scope.currentGridOptions.gridApi.core.queueGridRefresh();
    				}
        		
        		}
        	}
        }
        var contains = function(array , key , value)  {
        	for(var i in array) {
        		if(array[i][key] == value) {
        			return true;
        		}
        	}
        	return false;
        }
        /**
         *  Returns orders for states
         */
        var getOrdersForStates = function(newData ,states) {
          	 var allOrders = [];
          	 for (var index in states) {
          		 var state =  states[index];
          		 allOrders = newData[state] ? allOrders.concat(newData[state]) : allOrders;
          	 }
          	return allOrders;
          };
        
         
  //=====================================================================================================      
              
        if (window.self !== window.top) {
            // App embedded as iframe , so just start polling
            initServices();

        }
        else {
            $rootScope.$watch('authService.authorized()', function () {
                
            	if ($rootScope.authService.authorized()) {
                    $rootScope.org = $rootScope.authService.currentOrg();                   
                                        
                    if($rootScope.org!="MAIN") {
                    	initServices();
                        $scope.loadPreferences();

                    } else {
                    	$rootScope.showNotifiedTab = false;
                    	$rootScope.showFilledTab = false;
                    	$rootScope.showReceivedTab = false;
                    	$rootScope.showSentTab = false;
                    	$rootScope.showMonitor1Tab = false;
                        $rootScope.showMonitor2Tab = false;
                    	$rootScope.showMonitorTab = false;
                    	$rootScope.showAutoTab = false;
                    	$rootScope.showDraftTab = false;
                    	$rootScope.showCancelledTab = false;
                    	$rootScope.showFixingTab = false;
                    	$rootScope.showFixingAggregateTab = false;
                		$scope.activeState = [];
                		$scope.activeState[5] = true;
                		$scope.Completed.grids[0].columnDefs[0].visible = true;
                		$scope.tabChanged($scope.Completed.grids[0]);
                    }
                } else {
                	if( $rootScope.showAutoAndManual )
 	        		{
                		var index = -1;
                		for(var i=0;i<$scope.Active.states.length;i++) {
                			  if($scope.Active.states[i] == $rootScope.STATES.AUTO){
                				  index = i;
                				  break;
                			  }
                		}
                		if(index != -1){
                			$scope.Active.states.splice(index, 1);
                			$scope.Active1.states.splice(index, 1);
                			$scope.Active2.states.splice(index, 1);
 	        			}
 	        		}
                    stopServices();
                    clearAllData();
                    $scope.reset();
                    $modalStack.close($scope.auditModalInstance);
                }
            });
        }

        $rootScope.$watch('webSocketInitialized', function () { 
        	if($rootScope.webSocketInitialized) {
        		console.log("Websocket Initialized");
        		$scope.registerWebsocketHandlers(websocket);
        	}
        	
        });

        $scope.registerWebsocketHandlers = function(websocket) {

        	websocket.on("connect", function (data) {
	    	        console.log("Connected to web Socket");
	    	        var ssoToken = $.cookie('SSO_TOKEN');
	    	        if($rootScope.ssoToken != ""){
	    	            ssoToken = $rootScope.ssoToken;
	    	        }
	    	    	var request = {userName: $rootScope.authService.currentUser(), userOrg:$rootScope.authService.currentOrg() , authToken:ssoToken};
	    	        websocket.emit("auth" , request, function(data) {
	                });
	    			
	    	    });
        	
        	/*var request = {userName: $rootScope.authService.currentUser(), userOrg:$rootScope.authService.currentOrg() , authToken:$.cookie('SSO_TOKEN')};
			websocket.removeListener('auth');
			websocket.emit("auth" , request, function(data) {
            });*/
			
        	websocket.on("auth" , function(response) {
            	if( !response || response.status == "ERROR") {
            		console.log(response.responseString);
            	} else {
            		if(!$rootScope.customer) {
            			$rootScope.webSocketProtocol = true;
            		}
            		$rootScope.webSocket = websocket;
            		console.log("User Authenticated on Websocket");
            	}
            });
        	
            websocket.on("disconnect", function (data) {
                console.log("Disconnected from web Socket");
                //$scope.notifier.notify('error', "Connection Lost");
            });
            
        	websocket.on("allOrders", function (data) {
            	$scope.refreshDataInGrids(data); 
            	//$scope.refreshView();
            });

        	websocket.on("aggregateOrders", function (data) {
            	$scope.refreshDataInAggregateGrid(data);
            	//$scope.refreshView();
            });
            
        	websocket.on("marketRatesInc", function (rates) {
                $rootScope.rates = rates;
                $scope.populateRatesInMarketMonitor();
             	var latency = new Date().getTime() - startTime;
        		latencyArr[latencyArr.length] = latency>0 ? latency : 0;
            });
        	websocket.on("marketRatesFR", function (rates) {
            	$rootScope.rates = rates;

            });
        	
        	websocket.on("forceLogout", function (rates) {
        		console.log("Force Logout Received");
        		$scope.logout();

            });
        	
        	websocket.on("alert", function (data) {
        		if( data.properties.priority == "low") {
        			$scope.notifier.notify('alertInformation', data.properties.message);  
        		}else
        		{
        			$scope.notifier.notify('error', data.properties.message); 
        		}
        	});
            
        	websocket.on("notification", function (data) {
        		if( !data || data.status == "ERROR") {
        			$scope.notifier.notify('error', data.responseString);
            	} else {
            		$scope.notifier.notify('success', data.responseString);
            		if(data.event && ( data.event=="PASS_RECEIVED" || data.event=="PASS_CANCEL_RECEIVED" )) {
            		  if(data.orderParams && data.orderParams.primaryOrder == true){
            			$scope.incomingSound.play();
            			if(!$scope.isInboxtTable()) {
            				$scope.inboxUnreadCount = $scope.inboxUnreadCount + 1;
            			}
            		  }
            		}
            		else if(data.event && data.event=="CREATED")  {
            			if(data.orderParams.custOrder)
            			{
            				$scope.newSound.play();
            				if(!$scope.isDraftTable()) {
            					$scope.newUnreadCount = $scope.newUnreadCount + 1;
            				}
            			}
                	}
                	else if(data.orderParams && data.orderParams.orderState=="NOTIFIED")
            		{
            			$scope.newSound.play();
            			if(!$scope.isExecutedTable()) {
            				$scope.notifiedUnreadCount = $scope.notifiedUnreadCount + 1;
            			}
            		}	
            		var oldState, newState;
                    if(data.properties) {
                    	oldState = data.properties.OS;
                    	newState = data.properties.NS;
                      	if(oldState!='TRIGGERED' && newState == 'TRIGGERED' ) {
                    		$rootScope.highlightOrdersList.push({'orderId' : data.orderId , 'state': 'TRIGGERED'});
                    		$scope.alertSound.play();
                    	}
                      	
                     	if((oldState!='ACTIVE' && newState == 'ACTIVE')) {
                     		$rootScope.highlightOrdersList.push({'orderId' : data.orderId , 'state': 'NEW'});
                    	}
                    }
            	}
            });
        }
        
        $scope.logout = function() {
        	$scope.notifier.notify('error', "Session Logged out");
        	stopServices();
      		$rootScope.authService.resetSession();
        }     
        
        var clearAllData = function () {
        	$rootScope.stopUpdates = false;
        	$scope.currentGridOptions = null;
        	$rootScope.showNotifiedTab = true;
        	$rootScope.showFilledTab = true;
        	$rootScope.showReceivedTab = true;
        	$rootScope.showSentTab = true;
        	$rootScope.showMonitor1Tab = true;
            $rootScope.showMonitor2Tab = true;
        	$rootScope.highlightOrdersList = [];
        	$rootScope.passAccounts= [];
        	$scope.inboxUnreadCount = 0;
        	$scope.newUnreadCount = 0;
        	$scope.notifiedUnreadCount = 0;
        	$scope.passAccount = {};
        	$scope.resetBulkButtons();
            $scope.Draft.grids[0].data = [];
            $scope.Cancelled.grids[0].data = [];
            $scope.Matched.grids[0].data = [];
            $scope.Executed.grids[0].data = [];
            $scope.Auto.grids[0].data = [];
            $scope.CustomerGrid.grids[0].data = [];
            $scope.Completed.grids[0].data = [];
            $scope.Active.grids[0].data = [];
            $scope.Active.grids[1].data = [];
            $scope.Active1.grids[0].data = [];
            $scope.Active1.grids[1].data = [];
            $scope.Active2.grids[0].data = [];
            $scope.Active2.grids[1].data = [];
            $scope.Inbox.grids[0].data = [];
            $scope.Outbox.grids[0].data = [];
            $scope.Fixing.grids[0].data = [];
            $scope.FixingAggregate.grids[0].data = [];
        };

        var initServices = function () {
        	referenceDataQueries();
        };


        var stopServices = function () {
        	$timeout.cancel(rateTimerEvent);
        	$timeout.cancel(orderTimerEvent);
        	$timeout.cancel(customerOrderTimerEvent);
        	$timeout.cancel(bringToMarketEvent);
        	$timeout.cancel(heartbeatTimerEvent);
            lastHeartbeat = null;
            connectionStatus = null;
            latencyArr = [10];
            $rootScope.heartBeatInterval = 10000;
            hbfailure = 0;
        };

        $scope.$on('$destroy', function () {
            stopServices();
        });
        

        $scope.reset = function() {
        	$rootScope.highlightOrdersList = [];
        	$scope.currentGridOptions = null;
        	$scope.search = '';
        	$scope.search1 = '';
        	$scope.search2 = '';
        	$scope.searchFA = '';
        	$scope.activeState[0] = true;
        	$('#searchInput').val("");
        };
        
        $scope.executeBatch = function (action) {
        	var context = null;
        	if(action =="pass" && !$scope.passAccount.selected) {
        		 $scope.notifier.notify('error', "Please select an account to pass order");
        		 return;
        	} else {
        		context = {"passAccount" : $scope.passAccount.selected };
        	}

            var selectedRows = [];
            if($scope.isActiveTable()){
                selectedRows = $scope.Active.grids[0].gridApi.selection.getSelectedGridRows();
                selectedRows = selectedRows.concat($scope.Active.grids[1].gridApi.selection.getSelectedGridRows());
            } else if($scope.isActive1Table()){
                selectedRows = $scope.Active1.grids[0].gridApi.selection.getSelectedGridRows();
                selectedRows = selectedRows.concat($scope.Active1.grids[1].gridApi.selection.getSelectedGridRows());
            } else if($scope.isActive2Table()){
                selectedRows = $scope.Active2.grids[0].gridApi.selection.getSelectedGridRows();
                selectedRows = selectedRows.concat($scope.Active2.grids[1].gridApi.selection.getSelectedGridRows());
            } else if ($scope.currentGridOptions) {
                 selectedRows = $scope.currentGridOptions.gridApi.selection.getSelectedGridRows();
            }
           
            for (var i in selectedRows) {
            	if(selectedRows[i].entity.orderId != "") {
            		$scope.executeAction(selectedRows[i], selectedRows[i].entity, action , context);
            	}
            }
      	  selectedRows.forEach(function(row) {
     		 row.setSelected(false); 
     	  });
     	  $rootScope.isLockedForUpdate = false;
     	  $scope.resetBulkButtons();
            
        };


        $scope.executeAction = function (row, rowEntity, action, context, callback) {
            if (!rowEntity && callback) {
                callback(false, "No Orders to act on");
            }
            else {
            	if(action==="showAuditDialog") {
            		if(!($rootScope.customer || ($rootScope.org && $rootScope.org=='MAIN'))){
            			$scope.showAuditDialog(rowEntity.orderId);
            		}
            		return;
            	}
                var order = {};
                angular.copy(rowEntity, order);
                if (action === "match") {
                    if (!order.fillPrice) {
                        order.fillPrice = order.orderPrice;
                        if(context) {
                        	context.fillPrice = order.orderPrice.toString();
                        } else {
                        	context = {"fillPrice" : order.orderPrice.toString()}
                        }
                    }
                    if (!order.matchPrice) {
                        order.matchPrice = order.marketRate;
                    }
                }
            	
            	if(action == "executeAndBBook") {
            		context = {"BBookName" : "B" };
            		action = "execute";
            	}
            	if($scope.isFixingAggregateTable()) {
                 OrderService.sendAction(order.orderIds, action, order, context, function (data, errorMsg) {
                    if (errorMsg) {
                        $scope.notifier.notify('error', "Orders:" + order.orderIds + " " + errorMsg);
                    } else {
                    	$scope.notifier.notify('success', "Orders:" + order.orderIds + " Booked successfully");
                    }

                 });
            	}else{
                 OrderService.sendAction(order.orderId, action, order, context, function (data, errorMsg) {
                    if (errorMsg) {
                        $scope.notifier.notify('error', "Order:" + order.orderId + " " + errorMsg);
                        if (callback) {
                            callback(false, "Order:" + order.orderId + " " + errorMsg);
                        }
                    } else {
                    	if(!$rootScope.webSocket) {
                    		var notificationMsg = Utils.notificationMsg(data.orders[0], data.orders[0].orderState, action);
                    		$scope.notifier.notify('success', notificationMsg);
                    	}
                        if (callback) {
                            callback(true, "Order:" + order.orderId + " " + errorMsg);
                        }
                    }

                 });
                }
            }
        };
        

        $scope.onComplete = function (response) {
            if (!response || response.data.Status == 'Error') {
                var errors = '';
                /*for (var i = 0; i < response.data.errors.length; i++) {
                    errors = errors + response.data.errors[i] + '<br>'
                }*/
                if(response.data.errors.length >0) {
                	errors = " Reason ->" + response.data.errors[0];
                	console.log("Order Upload Failed,"+errors)
                }
                $scope.notifier.notify('error', 'Order Upload Failed' );
            } else {
                $scope.notifier.notify('success', 'Order Upload Successful');
            }
        };

     //======================= Grid API related Methods ===========================================
        
        
        
      $scope.getOrders = function() {    	
    		var queryParam = {"states": $scope.getCurrentStates()};
    		if($rootScope.customer) {
    			queryParam.customerOrg = $rootScope.customerOrg;
    			queryParam.brokerOrg = $rootScope.brokerOrg;
    		}
    		OrderService.pollOrders( queryParam ,function (data, errorMsg) {
    			$scope.refreshDataInGrids(data); 
    		});
	        OrderService.pollAggregateOrders( queryParam ,function (data, errorMsg) {
                 $scope.refreshDataInAggregateGrid(data);
            });
    } 
      
      var callGetOrders = function() { 
      	if(!$rootScope.fetchCustomerOrders){  
      		customerOrderTimerEvent = $timeout(function () {
      			callGetOrders();	
      		}, (1000));
      	}
      	else 
      	{
      		$scope.getOrders();  
      		$rootScope.fetchCustomerOrders = false;
      		callGetOrders();
      	}
      }
      
     /**
      * This is a poller for Orders. It fetches full orders  data
      */ 
     var pollAndProcessOrders = function () {
            orderTimerEvent = $timeout(function () {
            	if($rootScope.webSocketProtocol) {
            		$scope.pollOrdersOnWebSocket();
            	}	else {
                	var queryParam = {"states": $scope.getCurrentStates()};
                	if($rootScope.customer) {
                		queryParam.customerOrg = $rootScope.customerOrg;
                		queryParam.brokerOrg = $rootScope.brokerOrg;
                	}
	                OrderService.pollOrders(queryParam ,function (data, errorMsg) {
	                		$scope.refreshDataInGrids(data); 
	                	});
	                OrderService.pollAggregateOrders( queryParam ,function (data, errorMsg) {
                        $scope.refreshDataInAggregateGrid(data);
                    });
            	}
            	pollAndProcessOrders();
            }, ($rootScope.orderPollInterval?$rootScope.orderPollInterval:750));
     };
     var lastHeartbeat;
     var connectionStatus;
     var latencyArr = [10];
     $rootScope.heartBeatInterval = 10000;
     var missHbRetries = 4
     var hbfailure = 0;

     var requestRenewToken = function() {
    	 RESTService.requestRenewToken( $rootScope.authService.currentUser(),$rootScope.authService.currentOrg() , function (data, errorMsg) {
    		 if(errorMsg!=null) {
    			 console.log("Error occured in Renew Token request :" + data.responseText + ", Status" + data.status+" reason:"+t+" .error:"+err);
                 if(data.status == 0 && t != "abort"){
                	 requestRenewToken();
                 }
    		 } else {
    				console.log("Renew Token Successful.");
					RESTService.getSsoToken();
    		 }
    	 });
     }
     
     function getValueForKey(key, arr) {
         for (var i = 0; i < arr.length; i++) {
             if (key == arr[i].key) {
                 return arr[i].value;
             }
         }
         return null;
     }
     
     /**
      * Heartbeat service
      */ 
     var startHeartbeatService = function () {
    	  heartbeatTimerEvent = $timeout(function () {
	        var minLt = '', maxLt = '', sumLt = 0, avgLt = '';
	        var copyLtArr = $.extend(new Array(), latencyArr);
	        latencyArr = new Array();
	        for (var i = 0; i < copyLtArr.length; i++) {
	            var lt = copyLtArr[i];
	            minLt = (minLt == '' || minLt > lt) ? lt : minLt;
	            maxLt = (maxLt == '' || maxLt < lt) ? lt : maxLt;
	            sumLt = sumLt + lt;
	        }
	        if (sumLt != 0) {
	            avgLt = Math.round(sumLt / copyLtArr.length);
	        }
    		 RESTService.heartbeat(minLt,maxLt,sumLt,avgLt , function (data, errorMsg) {
    			try {
	    			 if(errorMsg==null) {
	    				 lastHeartbeat = new Date().getTime();
	                     if (data && data.responseTuples) {
	                         var response = data.responseTuples;
	                         if (getValueForKey("SSO_TOKEN_RENEW", response) != undefined)
	                        	 requestRenewToken();
	                     }
	                     hbfailure = 0;
	    			 }
	                 else {
	                	 if(data) {
	                		 console.log("Error occured in heartbeat :"+data.responseText+", Status"+data.status+" failed : "+(hbfailure+1)+" times. err:"+errorMsg);
	                	 }
	                     hbfailure ++;
	                 }
	
	                 if(missHbRetries != undefined && hbfailure >= missHbRetries){
	                     console.log("Logging out as heartbeat to server failed for "+missHbRetries+" times.");
	                     $scope.logout();
	                     return;
	                 }
                 }catch(ex){
                	 console.log("Javascript exception in heartbeat workflow "+ex);
                 }
    			 startHeartbeatService();
    			 });
    	 }, $rootScope.heartBeatInterval);
     };
     
     var startTime =0;
     /**
      * This is a poller for Rates. It fetches market rates
      */ 
     var pollAndProcessRates = function () {
         rateTimerEvent = $timeout(function () {
        	if(!$rootScope.stopUpdates) {
             	if($rootScope.webSocketProtocol) {
             		$scope.pollMarketRatesOnWebSocket();
             	}
             	else {
             		   startTime = new Date().getTime();
    	                RateService.pollRates($rootScope.ccyPairs , function (rates, errorMsg) {
    	                		$rootScope.rates = rates;
    	                		var latency = new Date().getTime() - startTime;
    	                		latencyArr[latencyArr.length] = latency>0 ? latency : 0;
    	                });
             	}
        	} 
         	pollAndProcessRates();
         }, ($rootScope.marketPollInterval?$rootScope.marketPollInterval:750));
     };

     $scope.pollOrdersOnWebSocket = function() {
	    var request = {userName: $rootScope.authService.currentUser(), userOrg: $rootScope.authService.currentOrg() };
        request.queryParam = {"states": $scope.getCurrentStates()};
        $rootScope.webSocket.emit("allOrders" ,request );
        if($scope.isFixingAggregateTable()) {
            $rootScope.webSocket.emit("aggregateOrders" ,request );
        }
 }
     
     
     $scope.pollMarketRatesOnWebSocket = function() {
    	 startTime = new Date().getTime()
  	   var request = {userName: $rootScope.authService.currentUser(), userOrg: $rootScope.authService.currentOrg() };
        request.queryParam = {"currencyPairs":$rootScope.ccyPairs};
        $rootScope.webSocket.emit("marketRatesFR" ,request );
     };

     /**
      * This is to set homeCcy column display name
      */ 
     var setHomeCcyColumnDisplayName = function () {
    	 if( $rootScope.homeCcy != null){
    		 var displayName = $rootScope.homeCcy+ " Amt";
    		 $scope.Draft.grids[0].columnDefs[7].displayName = displayName;
    		 $scope.Cancelled.grids[0].columnDefs[7].displayName = displayName;
    		 $scope.Active.grids[0].columnDefs[7].displayName = displayName;
    		 $scope.Active1.grids[0].columnDefs[7].displayName = displayName;
    		 $scope.Active2.grids[0].columnDefs[7].displayName = displayName;
    		 $scope.Matched.grids[0].columnDefs[7].displayName = displayName;
    		 $scope.Executed.grids[0].columnDefs[7].displayName = displayName;
    		 $scope.Auto.grids[0].columnDefs[7].displayName = displayName;
    		 $scope.Completed.grids[0].columnDefs[7].displayName = displayName;
    		 $scope.Inbox.grids[0].columnDefs[7].displayName = displayName;
    		 $scope.Outbox.grids[0].columnDefs[7].displayName = displayName;
    		 $scope.Fixing.grids[0].columnDefs[7].displayName = displayName;
    	 }
     };
     var setCustomerNotesColumnDisplayName = function () {
    	 $scope.CustomerGrid.grids[0].columnDefs[16].displayName = "Notes";
     }
     
     var referenceDataQueries = function () {    	 
    	 
    			 if(!$rootScope.customer) {
    				setHomeCcyColumnDisplayName();
    				websocket.connect(); 
    	           	startHeartbeatService(); 	        		
 	                pollAndProcessOrders();
 	                bringToMarketTimer();
 	                pollAndProcessRates();
 	                windowResize(); 	               
 	        	} else {
 	        		setCustomerNotesColumnDisplayName();
 	        		websocket.connect(); 
 	        		startHeartbeatService();
 	        		//$rootScope.webSocketInitialized = false;
	    			$rootScope.webSocketProtocol = false;
	    			//$rootScope.webSocket = null;
	    			$scope.currentGridOptions = $scope.CustomerGrid.grids[0];
 	        		RateService.pollRates($rootScope.ccyPairs , function (rates, errorMsg) {
	                		$rootScope.rates = rates;
	                });  	        		
 	        		callGetOrders();
	                $rootScope.marketPollInterval = 10000;
 	        		$rootScope.orderPollInterval = 10000;
 	        		pollAndProcessRates();
 	        		pollAndProcessOrders();
 	        		
 	        	}    		 
    	 
         ReferenceDataService.userPreferences(function (data, errorMsg) {
             $rootScope.tif = data.tif;
             $rootScope.orderTypes = data.orderTypes;
            
         });

         ReferenceDataService.fixingVenueAndTimeList(function (data, errorMsg) {
                	    	 var fixingReference = [];
                   	    	 var fixingTime = [];
                   			 for (var i = 0; i < data.length; i++) {
                   	             var venue = data[i].venue;
                   	             fixingReference.push(venue);
                   	             fixingTime[venue] = data[i].times;;
                   	         }
                   	         $rootScope.fixingReference = fixingReference;
                   	         $rootScope.fixingTimeMap = fixingTime;
         });

         var org = $rootScope.org;
         if (!org) {
             org = $cookies['storedorg'];
         }
         ReferenceDataService.customerAccountList(function (data, errorMsg) {
             if (data && data.length > 0) {
                 var customers = {};
                 var orgs = [];
                 var orgMap = [];
                 var orgList = [];

                 for (var i = 0; i < data.length; i++) {
                     var cust = {'org': data[i].orgShortName, 'le': data[i].leShortName};
                     var index = orgs.indexOf(cust.org);

                     if (!$rootScope.showOnlyCustOrg || index < 0) {
                         customers[cust.org] = cust;
                         orgList.push(cust.org);
                         orgs.push(cust.org);
                         orgMap.push({'id': cust.org, 'accounts': [cust.le]});
                     }
                     if (index >= 0) {
                         orgMap[index].accounts.push(cust.le);
                     }
                 }
                 if(orgList) {
                	 orgList = orgList.sort();
                	 orgList.unshift("");
                 }
                 $rootScope.orgs = orgMap;
                 $rootScope.customers = customers;
                 $rootScope.orgList = orgList;

                 $scope.Draft.grids[0].columnDefs[14].editDropdownOptionsArray = orgList;
                 $scope.Matched.grids[0].columnDefs[14].editDropdownOptionsArray = orgList;
                 $scope.Executed.grids[0].columnDefs[14].editDropdownOptionsArray = orgList;
                 $scope.Completed.grids[0].columnDefs[14].editDropdownOptionsArray = orgList;
                 $scope.Active.grids[0].columnDefs[14].editDropdownOptionsArray = orgList;
                 $scope.Active.grids[1].columnDefs[14].editDropdownOptionsArray = orgList;
                 $scope.Active1.grids[0].columnDefs[14].editDropdownOptionsArray = orgList;
                 $scope.Active1.grids[1].columnDefs[14].editDropdownOptionsArray = orgList;
                 $scope.Active2.grids[0].columnDefs[14].editDropdownOptionsArray = orgList;
                 $scope.Active2.grids[1].columnDefs[14].editDropdownOptionsArray = orgList;
             }
         }, org);

     };  
        //===================== UI Grid Releated methods ==========================================
        $scope.isCloseToMarket = function (row) {
        	if(row.entity.ccyPair) {
            	var dealtInBase = (row.entity.ccyPair.substring(0, 3) === row.entity.dealtCcy);
            	var isBuy = dealtInBase ? (row.entity.side == 'BUY') : (row.entity.side != 'BUY');
            	var orderType = row.entity.orderType;
                if ((isBuy && orderType != 'STOP') || (!isBuy && orderType == 'STOP')  ) {
                	var buyPercThreshold = $rootScope.buyPercThreshold ? $rootScope.buyPercThreshold : 99.7500;
                	if( row.entity.percentage > buyPercThreshold && row.entity.percentage < 100.0000) {
                		return true;
                	}
                } else if ((!isBuy && orderType != 'STOP') || (isBuy && orderType == 'STOP')){
                	var sellPercThreshold = $rootScope.sellPercThreshold ? $rootScope.sellPercThreshold : 100.2500;
                	if (row.entity.percentage < sellPercThreshold && row.entity.percentage > 100.0000) {
                		return true;
                	}
                }
        	}

        }

        $scope.isTriggered = function (row) {
            return row.entity.orderState == 'TRIGGERED';
        };
        
        $scope.isAutoOrderAndNotCloseToMarket = function (row) {
            return row.entity.orderState == 'AUTO' && !$scope.isCloseToMarket(row);
        };
        
        $scope.refreshCurrentGrid = function () {
                if($scope.isActiveTable()){
                    $scope.scollToMarket = true;
                    $scope.Active.grids[0].gridApi.grid.refresh();
                    $scope.Active.grids[1].gridApi.grid.refresh();
                }else if($scope.isActive1Table()){
                    $scope.scollToMarket = true;
                    $scope.Active1.grids[0].gridApi.grid.refresh();
                    $scope.Active1.grids[1].gridApi.grid.refresh();
                }else if($scope.isActive2Table()){
                    $scope.scollToMarket = true;
                    $scope.Active2.grids[0].gridApi.grid.refresh();
                    $scope.Active2.grids[1].gridApi.grid.refresh();
                } else if ($scope.currentGridOptions) {
                    $scope.currentGridOptions.gridApi.grid.refresh();
                }
        };
        
        //====================================== Search Filter ===============================================
        $scope.searchColumns = ["orderId" ,"ccyPair" , "side" , "dealtAmt" , "dealtCcy" , "orderType" ,"orderPrice" , "fillPrice" , "orderState" ,  "cptyAOrg" , "tif" , "notes", "contigencyParameters" ,"tradeId" ,"fromOrg" ,"toOrg" , "clientOrderId" ,"emsOrderId", "cptyBOrg"];
        $scope.searchFAColumns = ["fixingAsset" ,"ccyPair" , "side" , "dealtAmt" , "dealtCcy" , "fixingReference" ,"balance" , "orderCount" , "fixingRate"];
        $scope.singleFilter = function (renderableRows) {
            if ($scope.search &&!$scope.isActiveTable() &&!$scope.isActive1Table() &&!$scope.isActive2Table() && !$scope.isFixingAggregateTable()) {
             $scope.rowSelected();             
            	 var searchTerms = $scope.search.split(" ");            	                 
            	 renderableRows.forEach(function(row) {
            		 var match = $scope.matchFound(searchTerms, row.entity, $scope.searchColumns);
            	   if(!match) {
              	       row.visible = false;
              	   }
                });           
            }
            else if($scope.searchFA && $scope.isFixingAggregateTable()){
             $scope.rowSelected();
            	 var searchTerms = $scope.searchFA.split(" ");
            	 renderableRows.forEach(function(row) {
            		 var match = $scope.matchFound(searchTerms, row.entity, $scope.searchFAColumns);
            	   if(!match) {
              	       row.visible = false;
              	   }
                });
            }
            return renderableRows;
        };

        $scope.matchFound = function (searchTerms, entity, searchCols) {
            var match = false;
            for(var i=0; i <searchTerms.length; i++) {
             	       var regex = new RegExp(searchTerms[i], 'i');
             	       for (var j=0;j< searchCols.length;j++) {
             	         var value = entity[searchCols[j]];
             	         var valueStr;
             	         if (value) {
             	            if(Array.isArray(value)) {
             	                valueStr = JSON.stringify(value);
             	            } else {
             	                 valueStr = String(value);
             	            }
             	            if (valueStr && valueStr.match(regex)) {
             	                 match = true;
             	                 break;
             	            }
             	         }
             	       }
             	       if(match) {
             	    	   break;
             	       }
            }
            return match;
        };
        
        $rootScope.filterAndRefreshData = function (search) {
            $scope.search = search;
            $scope.refreshCurrentGrid();
        };

        $rootScope.filterAndRefreshData1 = function (search) {
            $scope.search1 = search;
            $scope.refreshCurrentGrid();
            var prefStr = {};
             prefStr.omsPref = {};
             prefStr.omsPref.gridStateSetting = {};
             prefStr.omsPref.gridStateSetting["Monitor1Search"] = JSON.stringify({expanded:$scope.search1});
             RESTService.saveUIPreference(prefStr, function (data, errorMsg) {
 	    		 if(errorMsg!=null) {
 	    			 console.log("Failed save state, Status" + data.status);
 	    		 } else {
 	    			 console.log("State save Successful.");
 	    		 }
 	    	 });
        };

        $rootScope.filterAndRefreshData2 = function (search) {
            $scope.search2 = search;
            $scope.refreshCurrentGrid();
            var prefStr = {};
             prefStr.omsPref = {};
             prefStr.omsPref.gridStateSetting = {};
             prefStr.omsPref.gridStateSetting["Monitor2Search"] = JSON.stringify({expanded:$scope.search2});
             RESTService.saveUIPreference(prefStr, function (data, errorMsg) {
 	    		 if(errorMsg!=null) {
 	    			 console.log("Failed save state, Status" + data.status);
 	    		 } else {
 	    			 console.log("State save Successful.");
 	    		 }
 	    	 });
        };

        $rootScope.filterAndRefreshDataInFixiAggre = function (search) {
            $scope.searchFA = search;
            $scope.refreshCurrentGrid();
            var prefStr = {};
             prefStr.omsPref = {};
             prefStr.omsPref.gridStateSetting = {};
             prefStr.omsPref.gridStateSetting["FixingAggreSearch"] = JSON.stringify({expanded:$scope.searchFA});
             RESTService.saveUIPreference(prefStr, function (data, errorMsg) {
 	    		 if(errorMsg!=null) {
 	    			 console.log("Failed save state, Status" + data.status);
 	    		 } else {
 	    			 console.log("State save Successful.");
 	    		 }
 	    	 });
        };

        
        //================================ Bulk action handling on row Selection ====================================================
        
    
           $scope.isDraftTable = function () {
           	return $scope.currentGridOptions && $scope.currentGridOptions.name == "Draft";
           };
           
           $scope.isCancelledTable = function () {
           	return $scope.currentGridOptions && $scope.currentGridOptions.name == "Cancelled";
           };

           $scope.isFixingTable = function () {
           	return $scope.currentGridOptions && $scope.currentGridOptions.name == "Fixing";
           };

           $scope.isFixingAggregateTable = function () {
           	return $scope.currentGridOptions && $scope.currentGridOptions.name == "FixingAggregate";
           };

           $scope.isActiveTable = function () {
           	return !$scope.currentGridOptions || ($scope.currentGridOptions && $scope.currentGridOptions.name == "ActiveTop");
           };

           $scope.isActive1Table = function () {
           	return $scope.currentGridOptions && $scope.currentGridOptions.name == "ActiveTop1";
           };

           $scope.isActive2Table = function () {
           	return $scope.currentGridOptions && $scope.currentGridOptions.name == "ActiveTop2";
           };

           $scope.isMatchedTable = function () {
           	return $scope.currentGridOptions && $scope.currentGridOptions.name == "Matched";
           };
           
           $scope.isExecutedTable = function () {
           	return $scope.currentGridOptions && $scope.currentGridOptions.name == "Executed";
           };
           
           $scope.isAutoTable = function () {
           	return $scope.currentGridOptions && $scope.currentGridOptions.name == "Auto";
           };
           
           $scope.isCompletedTable = function () {
           	return $scope.currentGridOptions && $scope.currentGridOptions.name == "Completed";
           };
           
           $scope.isOutboxTable = function () {
           	return $scope.currentGridOptions && $scope.currentGridOptions.name == "Outbox";
           };
           $scope.isInboxtTable = function () {
           	return $scope.currentGridOptions && $scope.currentGridOptions.name == "Inbox";
           };
           
           $scope.isCustomerTable = function () {
              	return $scope.currentGridOptions && $scope.currentGridOptions.name == "Customer";
           };
           
           $scope.showActivateButton = function () {
           	return $scope.showActivateBtnInMtr;
           };
           
           $scope.showDraftButton = function () {
           	return $scope.showDraftBtnInMtr;
           };
           
           $scope.showAutoButton = function () {
           	return $scope.showAutoBtnInMtr;
           };
           
           $scope.showPassButton = function () {
           	return $scope.showPassBtnInMtr;
           };
           
           $scope.showWithdrawButton = function () {
           	return $scope.showWithdrawBtnInMtr;
           };
           
           $scope.showCancelButton = function () {
           	return $scope.showCancelBtn;
           };

           $scope.showMatchButton = function () {
           	return $scope.showMatchBtnInMtr;
           };
           
           $scope.showNotifyButton = function () {
           	return $scope.showNotifyBtnInMtr;
           };
           
           $scope.showArchiveButton = function () {
           	return $scope.showArchiveBtnInMtr;;
           };
           
           $scope.showRejectButton = function () {
           	return $scope.showRejecteBtnInMtr;;
           };
           
           $scope.showAcceptButton = function () {
           	return $scope.showAcceptBtnInMtr;;
           };
           
           $scope.showCancelRejectButton = function () {
           	return $scope.showCancelRejecteBtnInMtr;
           };
           
           $scope.showCancelAcceptButton = function () {
           	return $scope.showCancelAcceptBtnInMtr;
           };

           $scope.showAmendRejectButton = function () {
           	return $scope.showAmendRejecteBtnInMtr;
           };

           $scope.showAmendAcceptButton = function () {
           	return $scope.showAmendAcceptBtnInMtr;
           };

           $scope.showCreditOverrideRejectButton = function () {
           	return $scope.showCreditOverrideRejectBtnInMtr;
           };

           $scope.showCreditOverrideAcceptButton = function () {
           	return $scope.showCreditOverrideAcceptBtnInMtr;
           };

           $scope.showSkipCreditCheckButton = function () {
            return $scope.showSkipCreditCheckBtnInMtr;
           };

           $scope.showManualButton = function () {
              	return $scope.showManualBtnInMtr;;
            };
           
           $scope.isInboxTable = function () {
           	return $scope.currentGridOptions && $scope.currentGridOptions.name == "Inbox";
           };
          
           $scope.isOutboxTable = function () {
           	return $scope.currentGridOptions && $scope.currentGridOptions.name == "Outbox";
           };
           
        $scope.rowSelected = function() {
        	var rows; 
           	if($scope.isActiveTable()) {
           		rows = $scope.Active.grids[1].gridApi.selection.getSelectedRows();
            	rows = rows.concat($scope.Active.grids[0].gridApi.selection.getSelectedRows());
            } else if($scope.isActive1Table()) {
           		rows = $scope.Active1.grids[1].gridApi.selection.getSelectedRows();
            	rows = rows.concat($scope.Active1.grids[0].gridApi.selection.getSelectedRows());
        	} else if($scope.isActive2Table()) {
           		rows = $scope.Active2.grids[1].gridApi.selection.getSelectedRows();
            	rows = rows.concat($scope.Active2.grids[0].gridApi.selection.getSelectedRows());
        	} else if($scope.currentGridOptions && !$scope.isFixingAggregateTable()){
        		 rows = $scope.currentGridOptions.gridApi.selection.getSelectedRows();
        	}
        	$scope.handleBulkActionIcons(rows);
        
        };
        
        $scope.handleBulkActionIcons = function(rows) {
        	$scope.resetBulkButtons();
        	if(rows && rows.length >0) {
        		$rootScope.isLockedForUpdate = true;
        		var emsOrderSelected = false;
        		var fixingOrderSelected = false;
        		var limitOrStopOrderSelected = false;
        		angular.forEach(rows, function(row) {
        			if(row.orderId != "") {
	        			var index = $scope.selectedStates.indexOf(row.orderState);
	        			if(row.emsOrder) {
	        				emsOrderSelected = true;
	        			}
	        			if(index <0) {
	        				$scope.selectedStates.push(row.orderState);
	        			}
                        if(row.orderType == "FIXING") {
	        				fixingOrderSelected = true;
	        			}else{
	        			    limitOrStopOrderSelected = true;
	        			}
        			}
        	    });
        		
	        	var draftSelected = $scope.selectedStates.indexOf($rootScope.STATES.DRAFT)>=0;
	        	var activeSelected = $scope.selectedStates.indexOf($rootScope.STATES.ACTIVE)>=0;
	        	var triggeredSelected = $scope.selectedStates.indexOf($rootScope.STATES.TRIGGERED)>=0;
	        	var matchedSelected = $scope.selectedStates.indexOf($rootScope.STATES.FILLED)>=0;
	        	var passedSelected = $scope.selectedStates.indexOf($rootScope.STATES.EXPORTED)>=0;
	        	var acceptedSelected = $scope.selectedStates.indexOf($rootScope.STATES.ACCEPTED)>=0;
	        	var receivedSelected = $scope.selectedStates.indexOf($rootScope.STATES.IMPORTED)>=0;
	        	var notifiedSelected = $scope.selectedStates.indexOf($rootScope.STATES.NOTIFIED)>=0;
	        	var rejectedSelected = $scope.selectedStates.indexOf($rootScope.STATES.REJECTED)>=0;
	        	var wihdrawnSelected = $scope.selectedStates.indexOf($rootScope.STATES.WITHDRAWN)>=0;
	        	var autoSelected = $scope.selectedStates.indexOf($rootScope.STATES.AUTO)>=0;
	        	var archivedSelected = $scope.selectedStates.indexOf($rootScope.STATES.ARCHIVED)>=0;
	        	var canceledSelected = $scope.selectedStates.indexOf($rootScope.STATES.CANCELLED)>=0;
	        	var cancelRequestedSelected = $scope.selectedStates.indexOf($rootScope.STATES.CANCEL_REQUESTED)>=0;
	        	var amendRequestedSelected = $scope.selectedStates.indexOf($rootScope.STATES.AMEND_REQUESTED)>=0;
	        	var creditOverrideRequestedSelected = $scope.selectedStates.indexOf($rootScope.STATES.CREDIT_FAIL)>=0
	        	
	        	if(draftSelected && !activeSelected && !triggeredSelected && !matchedSelected && !passedSelected && !acceptedSelected && !receivedSelected && !notifiedSelected && !rejectedSelected && !wihdrawnSelected && !autoSelected && !canceledSelected && !archivedSelected && !cancelRequestedSelected && !amendRequestedSelected) {
	        		//Only Draft orders selected
	        		if(!fixingOrderSelected){
	        		    $scope.showActivateBtnInMtr = true;
	        		}
	        		$scope.showPassBtnInMtr = true;
	        	} else if(!draftSelected && activeSelected && !triggeredSelected && !matchedSelected && !passedSelected && !acceptedSelected && !receivedSelected && !notifiedSelected && !rejectedSelected && !wihdrawnSelected && !autoSelected && !canceledSelected && !archivedSelected && !cancelRequestedSelected && !amendRequestedSelected) {
	        		//Only Active orders selected
	        		$scope.showDraftBtnInMtr = true;
	        		if($rootScope.enableAutoForOCXOrders){
	        			$scope.showAutoBtnInMtr = true;
	        		}
	        		else{
	        			$scope.showAutoBtnInMtr = !emsOrderSelected;
	        		}
	        		$scope.showPassBtnInMtr = true;
	        		$scope.showMatchBtnInMtr = true;
	        	} else if(!draftSelected && !activeSelected && triggeredSelected && !matchedSelected && !passedSelected && !acceptedSelected && !receivedSelected && !notifiedSelected && !rejectedSelected && !wihdrawnSelected && !autoSelected && !canceledSelected && !archivedSelected && !cancelRequestedSelected && !amendRequestedSelected && !creditOverrideRequestedSelected) {
	        		//Only Triggered orders selected
	        		$scope.showActivateBtnInMtr = true;
	        		$scope.showMatchBtnInMtr = true;
	        	} else if(!draftSelected && activeSelected && triggeredSelected && !matchedSelected && !passedSelected && !acceptedSelected && !receivedSelected && !notifiedSelected && !rejectedSelected && !wihdrawnSelected && !autoSelected && !canceledSelected && !archivedSelected && !cancelRequestedSelected && !amendRequestedSelected && !creditOverrideRequestedSelected) {
	        		//Only Triggered & Active orders selected
	        		$scope.showMatchBtnInMtr = true;
	        	} else if(!draftSelected && !activeSelected && !triggeredSelected && matchedSelected && !passedSelected && !acceptedSelected && !receivedSelected && !notifiedSelected && !rejectedSelected && !wihdrawnSelected && !autoSelected && !canceledSelected && !archivedSelected && !cancelRequestedSelected && !amendRequestedSelected && !creditOverrideRequestedSelected) {
	        		//Only matched orders selected
	        		$scope.showNotifyBtnInMtr = true;
	        	} else if(!draftSelected && !activeSelected && !triggeredSelected && !matchedSelected && (passedSelected || acceptedSelected )&& !receivedSelected && !notifiedSelected && !rejectedSelected && !wihdrawnSelected && !autoSelected && !canceledSelected && !archivedSelected && !cancelRequestedSelected && !amendRequestedSelected && !creditOverrideRequestedSelected) {
	        		//Only Passed or Accepted orders selected
	        		$scope.showWithdrawBtnInMtr = true;
	        	} else if(!draftSelected && !activeSelected && !triggeredSelected && !matchedSelected && !passedSelected && !acceptedSelected && receivedSelected && !notifiedSelected && !rejectedSelected && !wihdrawnSelected && !autoSelected && !canceledSelected && !archivedSelected && !cancelRequestedSelected && !amendRequestedSelected && !creditOverrideRequestedSelected) {
	        		//Only Recieved orders selected
	        		$scope.showAcceptBtnInMtr = true;
	        		$scope.showRejecteBtnInMtr = true;
	        	} else if(!draftSelected && !activeSelected && !triggeredSelected && !matchedSelected && !passedSelected && !acceptedSelected && !receivedSelected && !notifiedSelected && !rejectedSelected && !wihdrawnSelected && !autoSelected && !canceledSelected && !archivedSelected && cancelRequestedSelected) {
	        		//Only Cancel Requested orders selected
	        		$scope.showCancelRejecteBtnInMtr = true;
	        		$scope.showCancelAcceptBtnInMtr = true;
                } else if(!draftSelected && !activeSelected && !triggeredSelected && !matchedSelected && !passedSelected && !acceptedSelected && !receivedSelected && !notifiedSelected && !rejectedSelected && !wihdrawnSelected && !autoSelected && !canceledSelected && !archivedSelected && amendRequestedSelected && !creditOverrideRequestedSelected) {
	        		//Only Amend Requested orders selected
	        		$scope.showAmendRejecteBtnInMtr = true;
	        		$scope.showAmendAcceptBtnInMtr = true;
                } else if(!draftSelected && !activeSelected && !triggeredSelected && !matchedSelected && !passedSelected && !acceptedSelected && !receivedSelected && !notifiedSelected && !rejectedSelected && !wihdrawnSelected && !autoSelected && !canceledSelected && !archivedSelected && !amendRequestedSelected && creditOverrideRequestedSelected) {
	        		//Only Credit Override Requested orders selected
	        		$scope.showCreditOverrideRejectBtnInMtr = true;
	        		$scope.showCreditOverrideAcceptBtnInMtr = true;
	        		$scope.showSkipCreditCheckBtnInMtr = true;
	        	} else if(!draftSelected && !activeSelected && !triggeredSelected && !matchedSelected && !passedSelected && !acceptedSelected && !receivedSelected && notifiedSelected && !rejectedSelected && !wihdrawnSelected && !autoSelected && !canceledSelected && !archivedSelected && !cancelRequestedSelected && !amendRequestedSelected && !creditOverrideRequestedSelected) {
	        		//Only Notified orders selected
	        		$scope.showArchiveBtnInMtr = true;
	        	}  else if(!draftSelected && !activeSelected && !triggeredSelected && !matchedSelected && !passedSelected && !acceptedSelected && !receivedSelected && !notifiedSelected && (rejectedSelected || wihdrawnSelected) && !autoSelected && !canceledSelected && !archivedSelected && !cancelRequestedSelected && !amendRequestedSelected && !creditOverrideRequestedSelected) {
	        		//Only Rejected or Withdrawn orders selected
	        		if(!fixingOrderSelected){
	        		    $scope.showActivateBtnInMtr = true;
	        		}
	        		if(fixingOrderSelected && !limitOrStopOrderSelected){
	        		    $scope.showPassBtnInMtr = true;
	        		}
	        	} else if(!draftSelected && !activeSelected && !triggeredSelected && !matchedSelected && !passedSelected && !acceptedSelected && !receivedSelected && !notifiedSelected && !rejectedSelected && !wihdrawnSelected && autoSelected && !canceledSelected && !archivedSelected && !cancelRequestedSelected && !amendRequestedSelected && !creditOverrideRequestedSelected) {
	        		//Only Auto orders selected
	        		$scope.showManualBtnInMtr = true;
	        	}  
	        	
	        	if ((draftSelected || activeSelected || triggeredSelected || matchedSelected || (fixingOrderSelected && rejectedSelected && !limitOrStopOrderSelected)) && (!passedSelected && !acceptedSelected && !receivedSelected && !notifiedSelected && (fixingOrderSelected || !rejectedSelected) && !wihdrawnSelected && !autoSelected && !canceledSelected && !archivedSelected && !cancelRequestedSelected)) {
	        		$scope.showCancelBtn = true;
	        	}
        	}  else {
	        	$rootScope.isLockedForUpdate = false;
	        }
        	
        	
        };
    	
        
    }
]);






  

'use strict';
var toUTCDate = function(date){
    var _utc = new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(),  date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds() ,date.getUTCMilliseconds());
    return _utc;
  };
/* Filters */

filters.filter('interpolate', ['version', function (version) {
    return function (text) {
        return String(text).replace(/\%VERSION\%/mg, version);
    }
}]);

filters.filter('rateFilter', ['Utils', '$filter', function (Utils, $filter) {
    return function (rate, ccyPair) {
        return $filter('amountFilter')(Utils.formatRate(rate, ccyPair), false);
    };
}]);

filters.filter('orderStateMapping', ['$rootScope',function($rootScope) {

    var customerStateMap = {"ACTIVE" :"ACCEPTED", "FILLED" :"ACCEPTED" , "TRIGGERED":"ACCEPTED", "AUTO":"ACCEPTED", "PASSED" :"ACCEPTED",
    						"RECEIVED" :"SUBMITTED", "TAKEN_BACK" :"ACCEPTED" , "REJECTED":"ACCEPTED", "NOTIFIED":"DONE", "DRAFT":"SUBMITTED"};

    return function(originalState) {
   
    	if(!angular.isDefined(originalState)) {
    		return originalState;
    	}
    	if($rootScope.customer) {
    	   	var mappedState = customerStateMap[originalState];
            if (angular.isDefined(mappedState)) {
                return mappedState;
            } 
    	}
    	
    	return originalState;
 
    };
}]);
filters.filter('aboveMarket', function() {
    return function(input) {
      var aboveMarket = [];
      angular.forEach(input, function(item) {
        if (item.percentage >= 100.00)
        	aboveMarket.push(item);
      });
      return aboveMarket.length > 0 ? aboveMarket : input
    };
  });
      
  filters.filter('belowMarket', function() {
    return function(input) {
      var belowMarket = [];
      angular.forEach(input, function(item) {
        if (item.percentage < 100.00)
        		belowMarket.push(item);
      });
      return belowMarket.length > 0 ? belowMarket : []
    };
  });
    	      
filters.filter('amountFilter', ['$filter', '$rootScope', function ($filter, $rootScope) {
    return function (amount, numberFilter) {
		 if(angular.isUndefined(amount) || amount === null) {
			 return undefined;
		 }
    	var thousandAmtSep = $rootScope.thousandAmtSep;
    	var thousandFilteredAmt = amount+"";
    	if(angular.isUndefined(numberFilter)){
    		thousandFilteredAmt = $filter('number')(amount,0);
    	}else if(numberFilter){
    		thousandFilteredAmt = $filter('number')(amount);
    	}
    	if(angular.equals(thousandAmtSep, ".")){
    		thousandFilteredAmt = thousandFilteredAmt+"";
    		thousandFilteredAmt = thousandFilteredAmt.replace(".",":");
    		thousandFilteredAmt = thousandFilteredAmt.replace(/,/g,".");
    		thousandFilteredAmt = thousandFilteredAmt.replace(":",",");
    	}
    	var formattedAmt = thousandFilteredAmt;
    	var format = $rootScope.amtFmt;
    	if (amount < 0){
    		if(format.indexOf("(") != -1){
    			formattedAmt = "(" + formattedAmt.substring( 1 ) +")";
    		}
    		else if(format.indexOf("trailing") != -1){
    			formattedAmt = formattedAmt.substring( 1 ) +"-";
    		}
    	}
        return formattedAmt;
    };
}]);


filters.filter('epochMilliseconds', ['$filter', '$rootScope',function($filter, $rootScope) {
    return function (input) {
        if (input == null) {
            return null;
        }
        var d = new Date(0);
        d.setTime(input+$rootScope.browserGMTTimeDiff);
        return $filter('date')(toUTCDate(d),$rootScope.dtAndTimeFmt,$rootScope.timeZoneOffset);
    };
}]);

filters.filter('epochMillisecondsToDate', ['$filter', '$rootScope',function($filter, $rootScope) {
    return function (input) {
        if (input == null) {
            return null;
        }
        var d = new Date(0);
        d.setTime(input+$rootScope.browserGMTTimeDiff);
        return $filter('date')(toUTCDate(d),$rootScope.dtFmt,$rootScope.timeZoneOffset);
    };
}]);
filters.filter('epochMillisecondsToTime', ['$filter', '$rootScope',function($filter, $rootScope) {
    return function (input) {
        if (input == null) {
            return null;
        }
        var d = new Date(0);
        d.setTime(input+$rootScope.browserGMTTimeDiff);
        return $filter('date')(toUTCDate(d),$rootScope.timeFmt,$rootScope.timeZoneOffset);
    };
}]);

filters.filter('conitigencyParser', function () {
	return function (contigencies) {
		if (contigencies == null || contigencies.length ==0) {
			return null;
		}
		  var displayText = ""
		  angular.forEach(contigencies, function(contigency) {
			  var displayVal = "";
/*			  if(contigency.type == 1) {
				  displayVal = "OCO ";
			  } else if(contigency.type == 2) {
				  displayVal = "OTO ";
			  } else {
				  displayVal = "Links ";
			  }*/
			  
			  if(contigency.linkedOrderIds) {
				  displayVal =  contigency.linkedOrderIds.toString();
			  }
			  displayText = displayText + displayVal;
		      });
		return displayText;
	};
});

filters.filter('propsFilter', function () {
    return function (items, props) {
        var out = [];

        if (angular.isArray(items)) {
            items.forEach(function (item) {
                var itemMatches = false;

                var keys = Object.keys(props);
                for (var i = 0; i < keys.length; i++) {
                    var prop = keys[i];
                    var text = props[prop].toLowerCase();
                    if (item[prop].toString().toLowerCase().indexOf(text) !== -1) {
                        itemMatches = true;
                        break;
                    }
                }

                if (itemMatches) {
                    out.push(item);
                }
            });
        } else {
            // Let the output be the input untouched
            out = items;
        }

        return out;
    }
});
OMSApp.factory('AggregateOrderPanelFactory', ['$http','uiGridConstants' , '$rootScope',function ($http ,uiGridConstants , $rootScope ) {
	var self = this;
	var editableTables = ["Draft"];
	self.rowIdentity = function (row) {
	    return row.aggregateKey ? row.aggregateKey : '';
	};
    
    self.editAllowed = function($scope) {
    	return !$rootScope.isLockedForUpdate && !$rootScope.customer;
    };
	self.rowEquality= function (rowEntityA, rowEntityB) {
        //First check for row Identity
        if (self.rowIdentity(rowEntityA) != self.rowIdentity(rowEntityB)) {
            return false;
        }

        return angular.equals(rowEntityA, rowEntityB);
    };
    
    self.newGrid = function (name , visibleColumns){
		var tmp = {};
		angular.copy(self.commonGridOptions, tmp);
		tmp.name = name;
		return tmp;
	};
    
    self.commonGridOptions = {
		saveWidths: false,
		saveOrder: true,
		saveScroll: false,
		saveFocus: false,
		saveVisible: true,
		saveSort: true,
		saveFilter: false,
		savePinning: false,
		saveGrouping: false,
		saveGroupingExpandedStates: false,
		saveTreeView: false,
		saveSelection: false,
        enableRowHashing: false,
        enableSorting: true,
        enableFiltering: false,
        multiSelect: true,
        enableSelectAll: true,
        enableColumnResize: false,
        enableColumnReordering: true,
        enableGridMenu: true,
        enableRowHeaderSelection: true,
        enableFullRowSelection: true,
        enableColumnMenus:false,
        enableHorizontalScrollbar:uiGridConstants.scrollbars.NEVER,
        enableRowSelection: false,
        rowHeight:24,
        rowEditWaitInterval: 600,
        flatEntityAccess: true,
        rowTemplate: 'partials/templates/rowTemplate.html',
        exporterSuppressColumns: ['dealtCcy' ],
        exporterMenuPdf: false,
        exporterMenuCsv: false,
        exporterHeaderFilter: function( displayName ) { 
            if( displayName === 'Fixing Time' ) {
              return 'FixingTime';
            } 
            else if( displayName === 'Fixing Reference' ) {
              return 'FixingVenue';
            }else { 
              return displayName;
            } 
          },
        rowIdentity: function (row) {
            return self.rowIdentity(row);
        },
        rowEquality: function (rowEntityA, rowEntityB) {
        	return self.rowEquality(rowEntityA, rowEntityB);
        },
        columnDefs: [
            {//0
                field: "date",
                displayName: "Date",
                enableCellEdit: false,
                headerCellClass: 'alignCenter',
                minWidth: 140,
                maxWidth: 140,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{(MODEL_COL_FIELD| epochMillisecondsToDate)}}</div>",
                visible: true
            }
            ,
            {//1
                field: "fixingAsset",
                displayName: "Fixing Asset",
                minWidth: 120,
                maxWidth: 120,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{MODEL_COL_FIELD}}</div>",
                visible: true,
                enableCellEdit: false
            }
            ,
            {//2
                field: "fixingReference",
                displayName: "Fixing Venue",
                minWidth: 120,
                maxWidth: 120,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{MODEL_COL_FIELD}}</div>",
                visible: true,
                enableCellEdit: false
            }
            ,
            {//3
                field: "fixingTime",
                displayName: "Fixing Time",
                enableCellEdit: false,
                headerCellClass: 'alignCenter',
                minWidth: 140,
                maxWidth: 140,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{(MODEL_COL_FIELD| epochMillisecondsToTime)}}</div>",
                visible: true,
                sort: {
                    direction: "asc",
                    priority: 0
                }
            },
            {//4
                field: "ccyPair",
                displayName: "Currency Pair",
                enableCellEdit: false,
                enableFiltering: true,
                
                minWidth: 80,
                maxWidth: 120,
                headerCellClass:'alignCenter',
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: center;'>{{MODEL_COL_FIELD}}</div>",
				visible: true
            },
            {//5
                field: "side",
                displayName: "Net BUY/SELL",
                enableCellEdit: false,
                cellTemplate: "<div align='center' style='height:100%;'><span ng-class='{\"buy\": row.entity.side==\"BUY\" ,\"sell\": row.entity.side==\"SELL\"}'>{{row.entity.side}}</span></div>",

                headerCellClass: 'alignCenter',
                minWidth: 70,
                maxWidth: 120,
                visible: true
            },
            {//6
                field: "balance",
                displayName: "Balance",
                fieldName: "Balance",
                enableCellEdit: true,
                cellEditableCondition : function($scope) {
                	return  !$scope.row.entity.fromOrg && self.editAllowed($scope);
                },
                minWidth: 85,
                maxWidth: 100,
                headerCellClass: 'alignCenter',
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: right;'>{{MODEL_COL_FIELD| amountFilter}}</div>",
				visible: true
            },
            {//7
                field: "dealtCcy",
                displayName: "Dealt",
                enableCellEdit: false,
                enableSorting: false,
                minWidth: 60,
                maxWidth: 100,
                headerCellClass: 'alignRight',
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: center;'>{{MODEL_COL_FIELD}}</div>",
				visible: true
            },
            {//8
                field: "orderCount",
                displayName: "Order count",
                enableCellEdit: false,
                minWidth: 80,
                maxWidth: 120,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'><a href='#'>{{MODEL_COL_FIELD}}</a></div>",
                visible: true,
                headerCellClass: function(grid, row, col, rowRenderIndex, colRenderIndex) {
                     return 'alignLeft';
                }
            }
            ,
             {//9
                 field: "fixingRate",
                 displayName: "Fixing Rate",
                 fieldName: "Fixing Rate",
                 enableCellEdit: true,
                 cellEditableCondition : function($scope) {
                 	return self.editAllowed($scope);
                 },
                 headerCellClass: 'alignRight',

 				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: right;'>{{MODEL_COL_FIELD | rateFilter:row.entity.ccyPair}}</div>",
                minWidth: 70,
                maxWidth: 100,
                visible: true
             },
            {//10
                field: "action",
                displayName: "Action",
                cellTemplate: "partials/templates/bookTrades.html",
                allowCellFocus: false,
                enableSorting: false,
                enableCellEdit: false,
                headerCellClass: 'alignCenter',
                minWidth :60,
                maxWidth: 80,
                visible: true
            }

        ]
    };

    
	return {
		newGrid : function (name , visibleColumns){
				return self.newGrid(name ,visibleColumns)
			},
        createGrid : function ( name , states ,gridNames) {
        	var newGrid = {};
        	newGrid.name = name;
        	newGrid.orders = [];
        	newGrid.states = states;
        	newGrid.gridNames = gridNames;
        	newGrid.grids = [];
        	for(var i in gridNames) {
        		var gridName = gridNames[i];
        		var grid = self.newGrid(gridName);
                newGrid.grids.push(grid);
        	}
        	return newGrid;
        }
	};
	
}]);

OMSApp.factory('OrderPanelFactory', ['$http','uiGridConstants' ,'OrderService', '$rootScope',function ($http ,uiGridConstants , OrderService, $rootScope ) {
	var self = this;
	var editableTables = ["Draft" ,"ActiveTop","ActiveBottom","ActiveTop1","ActiveBottom1","ActiveTop2","ActiveBottom2", "Matched"];
	self.rowIdentity = function (row) {
	    return row.orderId ? row.orderId : '';
	};
	
	self.overrideMarketMonitorGrid = function(grid) {
		grid.columnDefs.push(
            {//28
                
                field: "percentage",
                displayName: "% from Market",
                enableCellEdit: false,
                type: "number",
                sort: {
                    direction: "desc",
                    priority: 0
                },
                suppressRemoveSort: false,
                minWidth: 100,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: right;'>{{MODEL_COL_FIELD}}</div>", 
                visible: false
            });
		
		
    	grid.rowTemplate = 'partials/templates/activeMonitorRowTemplate.html';
    	grid.enableSorting = false;
    	grid.columnDefs[12].minWidth = 110;
    	
    	if(grid.name == "ActiveBottom" || grid.name == "ActiveBottom1" || grid.name == "ActiveBottom2") {
    		grid.showHeader = false;
    		grid.enableGridMenu = false;
    	}
    	var visibleMarketMonitorColums = [1, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 17, 18, 19, 22 ,24];
    	grid.columnDefs.forEach(function (value, i) {
    		grid.columnDefs[i].visible = (visibleMarketMonitorColums.indexOf(i) > -1);
        });
    	grid.columnDefs[20].suppressRemoveSort= true;
        
    };
    
    self.editAllowed = function($scope) {
    	return !$rootScope.isLockedForUpdate && 
    			!$rootScope.customer && 
    			(editableTables.indexOf($scope.grid.options.name)>=0) &&
    			($scope.row && $scope.row.entity && $scope.row.entity.orderId != "")  &&  
				($scope.row.entity.orderState==$rootScope.STATES.ACTIVE || $scope.row.entity.orderState==$rootScope.STATES.FILLED ||
				 $scope.row.entity.orderState==$rootScope.STATES.DRAFT || $scope.row.entity.orderState==$rootScope.STATES.TRIGGERED ) ;
    };
	self.rowEquality= function (rowEntityA, rowEntityB) {
        //First check for row Identity
        if (self.rowIdentity(rowEntityA) != self.rowIdentity(rowEntityB)) {
            return false;
        }

        return angular.equals(rowEntityA, rowEntityB);
    };
    
    self.newGrid = function (name , visibleColumns){
		var tmp = {};
		angular.copy(self.commonGridOptions, tmp);
		tmp.name = name;
		return tmp;
	};
    
    self.commonGridOptions = {
		saveWidths: false,
		saveOrder: true,
		saveScroll: false,
		saveFocus: false,
		saveVisible: true,
		saveSort: true,
		saveFilter: false,
		savePinning: false,
		saveGrouping: false,
		saveGroupingExpandedStates: false,
		saveTreeView: false,
		saveSelection: false,
        enableRowHashing: false,
        enableSorting: true,
        enableFiltering: false,
        multiSelect: true,
        enableSelectAll: true,
        enableColumnResize: false,
        enableColumnReordering: true,
        enableGridMenu: true,
        enableRowHeaderSelection: true,
        enableFullRowSelection: true,
        enableColumnMenus:false,
        enableHorizontalScrollbar:uiGridConstants.scrollbars.NEVER,
        enableRowSelection: false,
        rowHeight:24,
        rowEditWaitInterval: 600,
        flatEntityAccess: true,
        rowTemplate: 'partials/templates/rowTemplate.html',
        exporterSuppressColumns: [ 'cptyBOrg', 'dealtCcy','baseSide', 'baseAmt', 'homeCcyAmt', 'marketRate', 'fillPrice', 'action', 'lockedByUser', 'expiryTime', 'notes', 'portfolioId', 'filledAmount', 'triggerTime', 'tradeId', 'fromOrg', 'toOrg', 'clientOrderId', 'emsOrderId', 'custNotes', 'percentage' ],
        exporterCsvFilename: 'Orders.csv', 
        exporterMenuPdf: false,
        exporterMenuCsv: false,
        exporterHeaderFilter: function( displayName ) { 
            if( displayName === 'Order' ) { 
              return 'Rate'; 
            } 
            else if( displayName === 'Type' ) { 
              return 'OrderType'; 
            }else { 
              return displayName;
            } 
          },
        rowIdentity: function (row) {
            return self.rowIdentity(row);
        },
        rowEquality: function (rowEntityA, rowEntityB) {
        	return self.rowEquality(rowEntityA, rowEntityB);
        },
        columnDefs: [
            {//0
            	 field: "cptyBOrg",
                 displayName: "Org",
                 enableCellEdit: false,
                 enableFiltering: true,
                 visible: false,
                 minWidth: 80,
                 maxWidth: 200,
                 headerCellClass:'alignCenter',
 				 cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: center;'>{{MODEL_COL_FIELD}}</div>"
    
            
                /*field: "Lock",
                displayName: "Lock",
                enableCellEdit: false,
                enableFiltering: false,
                enableSorting: false,
                cellTemplate: "<div ng-if='row.entity.rowType!=\"MARKET\"' align='center'><span ng-if='row.entity.lockedByUser&&row.entity.lockedByUser==grid.appScope.loggedInUser()'><i class='fa fa-lock fa-lg'  style='color:  #74C365;'></i></span><span ng-if='row.entity.lockedByUser&&row.entity.lockedByUser!=grid.appScope.loggedInUser()'><i class='fa fa-lock fa-lg'  style='color: #FF6357;'></i></span><span ng-if='!row.entity.lockedByUser'><i class='fa fa-unlock fa-lg'  style='color: #73C2FB;'></i></span></div>",
                minWidth: 50,
                //maxWidth: 50,
                visible: false,
                editableCellTemplate: 'partials/templates/gridDropDown.html',
                editDropdownValueLabel :'id',
                editDropdownOptionsArray: ['BUY','SELL']*/
            },
            {//1
                field: "ccyPair",
                displayName: "CCY Pair",
                enableCellEdit: false,
                enableFiltering: true,
                
                minWidth: 80,
                maxWidth: 80,
                headerCellClass:'alignCenter',
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: center;'>{{MODEL_COL_FIELD}}</div>"
            },
            {//2
                field: "side",
                displayName: "Buy/Sell",
                enableCellEdit: false,
                cellTemplate: "<div align='center' style='height:100%;'><span ng-class='{\"buy\": row.entity.side==\"BUY\" ,\"sell\": row.entity.side==\"SELL\"}'>{{row.entity.side}}</span></div>",
                
                headerCellClass: 'alignCenter',
                minWidth: 70,
                maxWidth: 70
            },
            {//3
                field: "dealtAmt",
                displayName: "Size",
                fieldName: "Dealt Amount",
                enableCellEdit: true,
                cellEditableCondition : function($scope) {
                	return  !$scope.row.entity.fromOrg && self.editAllowed($scope);
                },
                minWidth: 85,
                maxWidth: 100,
                headerCellClass: 'alignCenter',
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: right;'>{{MODEL_COL_FIELD| amountFilter}}</div>"

            },
            {//4
                field: "dealtCcy",
                displayName: "CCY",
                enableCellEdit: false,
                enableSorting: false,
                minWidth: 60,
                maxWidth: 60,
                headerCellClass: 'alignRight',
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: center;'>{{MODEL_COL_FIELD}}</div>"
            },
            {//5
            	field: "baseSide",
                displayName: "Base Side",
                enableCellEdit: false,
                cellTemplate: "<div align='center' style='height:100%;'><span ng-class='{\"buy\": row.entity.baseSide==\"BUY\" ,\"sell\": row.entity.baseSide==\"SELL\"}'>{{row.entity.baseSide}}</span></div>",
                headerCellClass: 'alignCenter',
                minWidth: 70,
                maxWidth: 70,
                visible: false
                
                /*field: "matchPrice",
                displayName: "Cover Rate",
                enableCellEdit: false,
                type: "numberStr",
                
                minWidth: 70,
                headerCellClass: 'alignCenter',
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: right;'>{{MODEL_COL_FIELD | rateFilter:row.entity.ccyPair}}</div>", 
                visible: false*/
            },
            {//6
        	   field: "baseAmt",
               displayName: "Base Amt",
               fieldName: "Base Amount",
               cellEditableCondition : function($scope) {
               	return !$scope.row.entity.fromOrg && self.editAllowed($scope);
               },
               minWidth: 85,
               maxWidth: 100,
               headerCellClass: 'alignCenter',
			   cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: right;'>{{MODEL_COL_FIELD| amountFilter}}</div>",
			   visible: false

               /* field: "cptyA",
                displayName: "Account",
                
                enableCellEdit: true,
                cellEditableCondition : function($scope) {
                	return !$scope.row.entity.fromOrg && self.editAllowed($scope);
                },
                headerCellClass: 'alignLeft',
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;padding:5px;text-align: left;'>{{MODEL_COL_FIELD}}</div>",
                minWidth: 100,
                visible: false*/
            },
            {//7
	            field: "homeCcyAmt",
	            displayName: "Local Amt",
	            enableCellEdit: false,
	            enableSorting: false,
                minWidth: 85,
                maxWidth: 100,
	            headerCellClass: 'alignCenter',
				    cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: right;'>{{MODEL_COL_FIELD| amountFilter}}</div>",
				    visible: false
            },
            {//8
                field: "orderType",
                displayName: "Type",
                
                enableCellEdit: false,
                minWidth: 70,
                maxWidth: 70,
                visible: true,
                cellEditableCondition : function($scope) {
                	return !$scope.row.entity.fromOrg && self.editAllowed($scope);
                },
                //pinnedLeft: true,
                headerCellClass: 'alignRight',
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: center;'>{{MODEL_COL_FIELD}}</div>",
                editableCellTemplate: 'partials/templates/gridDropDown.html',
                editDropdownValueLabel :'id',
                editDropdownOptionsArray: [ 'LIMIT','STOP']

            },        	  
            {//9
                 field: "marketRate",
                 displayName: "Market",
                 enableCellEdit: false,
                 type: "numberStr",
                 enableSorting: false,
                 minWidth: 70,
                 maxWidth: 80,
                // headerCellTemplate: 'partials/templates/marketHeader.html',
                 headerCellClass: 'alignRight',
                 cellTemplate: "<div class='ui-grid-cell-contents marketRate' style='height:100%;text-align: right;'>{{MODEL_COL_FIELD | rateFilter:row.entity.ccyPair}}</div>",
                 visible: false
             },
            {//10
                field: "orderPrice",
                displayName: "Order",
                fieldName: "Order Price",
                enableCellEdit: true,
                cellEditableCondition : function($scope) {
                	return !$scope.row.entity.fromOrg && self.editAllowed($scope);
                },
                minWidth: 70,
                maxWidth: 80,
                headerCellClass:'alignRight',
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: right;'>{{MODEL_COL_FIELD| rateFilter:row.entity.ccyPair}}</div>"
            },
             {//11
                 field: "fillPrice",
                 displayName: "Fill",
                 fieldName: "Fill Price",
                 enableCellEdit: true,
                 cellEditableCondition : function($scope) {
                 	return self.editAllowed($scope);
                 },
                 headerCellClass: 'alignRight',
                 
 				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: right;'>{{MODEL_COL_FIELD | rateFilter:row.entity.ccyPair}}</div>",
                minWidth: 70,
                maxWidth: 80
             },
            {//12
                field: "action",
                displayName: "Action",
                cellTemplate: "partials/templates/actionColumn.html",
                allowCellFocus: false,
                enableSorting: false,
                enableCellEdit: false,
                headerCellClass: 'alignCenter',
                minWidth :60
                
            },
            {//13
                field: "orderState",
                displayName: "Status",
                enableCellEdit: false,
                minWidth: 100,
                maxWidth: 100,
                headerCellClass: 'centerLeft',
				cellTemplate: "<div class='ui-grid-cell-contents elipsis' style='height:100%;padding:5px;text-align:center;'>{{MODEL_COL_FIELD | orderStateMapping:row.entity.orderState }}</div>",
                visible: true
            },

            {//14
                field: "cptyAOrg",
                displayName: "Customer",
                fieldName: "Customer",
                
                enableCellEdit: true,
                cellEditableCondition : function($scope) {
                	return !$scope.row.entity.fromOrg && self.editAllowed($scope);
                },
                minWidth: 80,
                maxWidth: 200,
                headerCellClass: 'alignLeft',
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;padding:5px;text-align:Left;'>{{MODEL_COL_FIELD}}</div>",
                editableCellTemplate: 'partials/templates/gridDropDown.html',
                editDropdownValueLabel :'id'
            },
            {//15
                field: "tif",
                displayName: "TIF",
                enableCellEdit: false,
                minWidth: 50,
                maxWidth: 50,
                visible: false,
                cellEditableCondition : function($scope) {
                	return !$scope.row.entity.fromOrg && self.editAllowed($scope);
                },
                headerCellClass: 'alignCenter',
                cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: center;'>{{MODEL_COL_FIELD}}</div>",
                editableCellTemplate: 'partials/templates/gridDropDown.html',
                editDropdownValueLabel :'id',
                editDropdownOptionsArray: [ 'GTC' ,'GTD' ]
            },
            {//16
                field: "lockedByUser",
                displayName: "Locked User",
                
                enableCellEdit: false,
                minWidth: 100,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{MODEL_COL_FIELD}}</div>", 
                visible: false
            },
            {//17
                field: "expiryTime",
                displayName: "Expiry Time",
                enableCellEdit: false,
                /*cellEditableCondition : function($scope) {
                	return self.editAllowed($scope) && $scope.row.entity.tif == "GTD";
                },*/
                cellEditableCondition : function($scope) {
                	return !$scope.row.entity.fromOrg && self.editAllowed($scope);
                },
                
                headerCellClass: 'alignCenter',
                minWidth: 140,
                maxWidth: 140,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{row.entity.tif? (row.entity.tif == 'GTD' ? (MODEL_COL_FIELD| epochMilliseconds) : 'GTC') : ''}}</div>", 
                editableCellTemplate: 'partials/templates/tifSelector.html',
                visible: true
            },
            {//18
                field: "notes",
                displayName: "Notes",
                enableCellEdit: true,
                cellEditableCondition : function($scope) {
                	return self.editAllowed($scope);
                },  
                minWidth: 150,
                headerCellClass: 'alignCenter',
                cellTemplate : "<div class='grid-tooltip' tooltip='{{ row.entity.notes }}' tooltip-placement='top' tooltip-append-to-body='true'><div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{MODEL_COL_FIELD}}</div></div>",
             	visible: true

            },
            {//19
                field: "orderId",
                displayName: "Order ID",
                enableCellEdit: false,
                minWidth: 80,
                maxWidth: 120,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'><a href='#' ng-click='execAction(row ,\"showAuditDialog\")'>{{MODEL_COL_FIELD}}</a></div>", 
                visible: true,
                sort: {
                    direction: "desc",
                    priority: 1
                },
                headerCellClass: function(grid, row, col, rowRenderIndex, colRenderIndex) {
                     return 'alignLeft';
                }
            },
            {//20
                field: "portfolioId",
                displayName: "Portfolio ID",
                minWidth: 100,
                maxWidth: 100,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{MODEL_COL_FIELD}}</div>", 
                visible: false
            },

            {//21                
                field: "filledAmount",
                displayName: "Fill Qty",
                enableCellEdit: false,
                type: "numberStr",
                minWidth: 85,
                maxWidth: 100,
                cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: right;'>{{MODEL_COL_FIELD| amountFilter}}</div>",
                visible: false
            },
            {//22
                
                field: "triggerTime",
                displayName: "Trigger Time",
                enableCellEdit: false,
                type: 'date',
                minWidth: 140,
                maxWidth: 140,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: center;'>{{MODEL_COL_FIELD| epochMilliseconds}}</div>", 
                visible: true
            },
            {//23
                field: "tradeId",
                displayName: "Trade ID",
                minWidth: 80,
                maxWidth: 120,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{MODEL_COL_FIELD}}</div>", 
                visible: false,
                enableCellEdit: false
            }
            ,
            {//24
                field: "contigencyParameters",
                displayName: "Linked Order",
                minWidth: 110,
                maxWidth: 140,
                cellTemplate : "<div class='grid-tooltip' tooltip='{{ row.entity.contigencyParameters | conitigencyParser}}' tooltip-placement='top' tooltip-append-to-body='true'><div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{MODEL_COL_FIELD | conitigencyParser}}</div></div>",
                visible: true,
                enableCellEdit: false,
                cellToolTip : function(row, col) {
                    return  row.entity.contigencyParameters | conitigencyParser;
                }
            }
            ,
            {//25
                field: "fromOrg",
                displayName: "Received from",
                minWidth: 80,
                maxWidth: 100,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{MODEL_COL_FIELD}}</div>", 
                visible: false,
                enableCellEdit: false
            }
            ,
            {//26
                field: "toOrg",
                displayName: "Sent To",
                minWidth: 80,
                maxWidth: 100,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{MODEL_COL_FIELD}}</div>", 
                visible: false,
                enableCellEdit: false
            }          
            ,
            {//27
                field: "clientOrderId",
                displayName: "Client Order ID",
                minWidth: 80,
                maxWidth: 120,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{MODEL_COL_FIELD}}</div>", 
                visible: false,
                enableCellEdit: false
            }
            ,
            {//28
                field: "emsOrderId",
                displayName: "EMS Order ID",
                minWidth: 80,
                maxWidth: 120,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{MODEL_COL_FIELD}}</div>", 
                visible: false,
                enableCellEdit: false
            },
            {//29
                field: "custNotes",
                displayName: "Customer Notes",
                enableCellEdit: false,
                minWidth: 150,
                headerCellClass: 'alignCenter',
                cellTemplate : "<div class='grid-tooltip' tooltip='{{ row.entity.custNotes }}' tooltip-placement='top' tooltip-append-to-body='true'><div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{MODEL_COL_FIELD}}</div></div>",
				visible: true

            }
            ,
            {//30
                field: "fixingReference",
                displayName: "Fixing Venue",
                minWidth: 120,
                maxWidth: 120,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{MODEL_COL_FIELD}}</div>",
                visible: false,
                enableCellEdit: false
            }
            ,
            {//31
                field: "fixingDate",
                displayName: "Fixing Date",
                enableCellEdit: false,
                headerCellClass: 'alignCenter',
                minWidth: 140,
                maxWidth: 140,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{(MODEL_COL_FIELD| epochMillisecondsToDate)}}</div>",
                visible: true
            }
            ,
            {//32
                field: "fixingTime",
                displayName: "Fixing Time",
                enableCellEdit: false,
                headerCellClass: 'alignCenter',
                minWidth: 140,
                maxWidth: 140,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{(MODEL_COL_FIELD| epochMillisecondsToTime)}}</div>",
                visible: false
            }
            ,
            {//33
                field: "transactionId",
                displayName: "Cover Trade ID",
                minWidth: 110,
                maxWidth: 120,
				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: left;'>{{MODEL_COL_FIELD}}</div>",
                visible: false,
                enableCellEdit: false
            },
            {//34
                field: "venueBMR",
                displayName: "VenueBMR",
                headerCellClass: 'alignRight',
 				cellTemplate: "<div class='ui-grid-cell-contents' style='height:100%;text-align: right;'>{{MODEL_COL_FIELD | rateFilter:row.entity.ccyPair}}</div>",
                minWidth: 70,
                maxWidth: 80,
                visible: false,
                enableCellEdit: false
             }
        ]
    };

    
	return {
		newGrid : function (name , visibleColumns){
				return self.newGrid(name ,visibleColumns)
			},
        createGrid : function ( name , states ,gridNames) {
        	var newGrid = {};
        	newGrid.name = name;
        	newGrid.orders = [];
        	newGrid.states = states;
        	newGrid.gridNames = gridNames;
        	newGrid.grids = [];
        	for(var i in gridNames) {
        		var gridName = gridNames[i];
        		var grid = self.newGrid(gridName);
        		if(name == "Active" || name == "Active1" || name == "Active2") {
        			self.overrideMarketMonitorGrid(grid);
        		}
                newGrid.grids.push(grid);
        	}
        	return newGrid;
        }
	};
	
}]);

OMSApp.factory('Preferences',
    function Preferences (RESTService) {
        var userPreferences;
        RESTService.get('data/userPref.json', function (data) {
                userPreferences = data;
            }
        );

    }
);
'use strict';

OMSApp.directive('modaldraggable', function ($document) {
	  return function (scope, element) {
	    var startX = 0,
	      startY = 0,
	      x = 300,
	      y = 0;
	     element= angular.element(document.getElementsByClassName("modal-dialog"));
	     console.log("added directive");
	     element.css({
	      position: 'fixed',
	      cursor: 'move'
	    });
	    
	    element.on('mousedown', function (event) {
	      // Prevent default dragging of selected content
	      event.preventDefault();
	      startX = event.screenX - x;
	      startY = event.screenY - y;
	      $document.on('mousemove', mousemove);
	      $document.on('mouseup', mouseup);
	    });

	    function mousemove(event) {
	      y = event.screenY - startY;
	      x = event.screenX - startX;
	      element.css({
	        top: y + 'px',
	        left: x + 'px'
	      });
	    }

	    function mouseup() {
	      $document.unbind('mousemove', mousemove);
	      $document.unbind('mouseup', mouseup);
	    }
	  };
});
OMSApp.factory('RESTService', ['$http', '$rootScope',
    function ($http, $rootScope) {
		var self = this;
        return {
            get: function (url, callback) {
                return $http({method: 'GET', url: url}).
                    success(function (data, status, headers, config) {
                        callback(data);
                    }).
                    error(function (data, status, headers, config) {
                        console.log("failed to retrieve data");
                    });
            },
            post: function (url, body, callback) {
                return $http.post(url, body).
                    success(function (data, status, headers, config) {
                        callback(data, status);
                    }).
                    error(function (data, status, headers, config) {
                        // called asynchronously if an error occurs
                        // or server returns response with an error status.
                        callback(data, status);
                    });
            },
            getSsoDomain : function(){
                $.ajax({
                    url: '/fxi/fxiapi/sso/ssoDomain' ,
                    type: 'GET',
                    async:false,
                    success: function(data) {
                    	$rootScope.ssoDomain = data;
                    }
                });
            },
            getSsoToken : function(){
                $.ajax({
                    url: '/fxi/fxiapi/sso/ssoToken' ,
                    type: 'GET',
                    async:false,
                    success: function(data) {
                    	$rootScope.ssoToken = data;
                    }
                });
            },
            getTokenInfo : function(){
                $.ajax({
                    url: '/fxi/fxiapi/sso/token/getInfo/v2' ,
                    type: 'GET',
                    async:false,
                    success: function(data) {
                        $rootScope.tokenInfo = data;
                    }
                });
            },
            addUserSession : function(){
                $.ajax({
                    url: '/fxi/fxiapi/unity/addUserSession' ,
                    type: 'POST',
                    dataType: "json",
                    cache: false,
                    async:false,
                    contentType: "application/json; charset=UTF-8",
                    success: function(data) {
                    	console.log("User session got added");
                    },
                    error: function(data, t, err) {
                    	console.log("Unable to add User session .error:"+err);
                    }
                });
            },
            
            /**--------------------Login to Server ------------------**/
            login: function (org, user, pw, callbackFnc) {

                var oPost = {user: user, pass: pw, org: org};
                $.ajax({
                    url: '/fxi/fxiapi/sso/login',
                    type: 'POST',
                    data: JSON.stringify(oPost),
                    dataType: "json",
                    contentType: "application/json; charset=UTF-8",
                    processData: false,
                    cache: false,
                    async: false,
                    beforeSend: function (x) {
                        if (x && x.overrideMimeType) {
                            x.overrideMimeType("application/json;charset=UTF-8");
                        }
                    },
                    success: function (data) {
                        if (typeof callbackFnc == 'function') {
                            callbackFnc.call(this, data);
                        }
                    },

                    error: function (data) {
                        if (typeof callbackFnc == 'function') {
                            callbackFnc.call(this, data);
                        }
                    }
                });
            },
            requestRenewToken : function(userName ,orgName ,callback) {
                var timeNow = new Date().getTime();
                console.log("Sending renew token request. ts="+timeNow);
                var userPlusOrg = userName + orgName;
                $.ajax({
                    url: '/fxi/fxiapi/sso/token/renew?un="'+userPlusOrg+'"&ts="'+timeNow+'"' ,
                    type: 'GET',
                    dataType: "json",
                    cache: false,
                    timeout: 5000,
                    contentType: "application/json; charset=UTF-8",
                    success: function(data) {
                   	 	callback(data, null);
                    },
                    error: function(data, t, err) {
                    	callback(data, "failed to send token renew");
                    }
                });
            },
            heartbeat: function (minLt,maxLt,sumLt,avgLt , callback) {
                return $http({
                    method: 'GET',
                    url: '/fxi/fxiapi/heartbeat',
                    params: {
                    	l : minLt,
                    	a : maxLt,
                    	h : sumLt,
                    	c : avgLt
                    }
                }).
                    success(function (data, status, headers, config) {
                        callback(data, null);
                    }).
                    error(function (data, status, headers, config) {
                        callback(data, "failed to send heartbeat");
                    });
            },
            logout : function(callbackFnc, reason) {
                var logoutUrl = '/fxi/fxiapi/sso/logout';
                $.ajax({
                    type: 'POST',
                    url: logoutUrl,
                    dataType: "json",
                    data : JSON.stringify({"reason":reason}),
                    async: false,
                    cache: false,
                    processData: false,
                    success: function(data) {
                        if (typeof callbackFnc == 'function') {
                            callbackFnc.call(this, data);
                        }
                    },

                    error: function(data) {
                        if (typeof callbackFnc == 'function') {
                            callbackFnc.call(this, data);
                        }
                    }
                });
            },
            loadUIPreference: function (callback) {
                console.log("Loading pref  ");
                $.ajax({
                    url: '/fxi/fxiapi/oms/pref/load/usersettings',
                    type: 'GET',
                    dataType: "json",
                    cache: false,
                    contentType: "application/json;charset=UTF-8",
                    success: function(data) {
                   	 	callback(data, null);
                    },
                    error: function(data, t, err) {
                    	callback(data, "failed to load preferences");
                    }
                });
            },
            saveUIPreference: function (prefStr ,callback) {
                console.log("Saving  " + prefStr);
                $.ajax({
                    url: '/fxi/fxiapi/oms/pref/save/usersettings',
                    type: 'POST',
                    data: JSON.stringify(prefStr),
                    dataType: "json",
                    cache: false,
                    contentType: "application/json;charset=UTF-8",
                    success: function(data) {
                   	 	callback(data, null);
                    },
                    error: function(data, t, err) {
                    	callback(data, "failed to save preferences");
                    }
                });
            }
        };
    }
]);
	
OMSApp.factory('ReferenceDataService', ['$http',
    function ($http) {
        var self = this;
        return {
            userPreferences: function (callback) {
                return $http({method: 'GET', url: 'data/userPref.json'}).
                    success(function (data, status, headers, config) {
                        callback(data, null);
                    }).
                    error(function (data, status, headers, config) {
                        callback(data, "failed to retrieve data");
                    });
            },
            refData: function (callback) {
                return $http({method: 'GET', url: '/fxi/fxiapi/oms/getReferenceData'}).
                    success(function (data, status, headers, config) {
                        callback(data, null);
                    }).
                    error(function (data, status, headers, config) {
                        callback(data, "failed to retrieve data");
                    });
            },
            userPrefData: function (callback) {
                return $http({method: 'GET', url: '/fxi/fxiapi/pref/load/dispPref'}).
                    success(function (data, status, headers, config) {
                        callback(data, null);
                    }).
                    error(function (data, status, headers, config) {
                        callback(data, "failed to retrieve data");
                    });
            },
            ccyPairList: function (callback) {
                return $http({method: 'GET', url: '/fxi/fxiapi/refdata/supportedCcypairs'}).
                    success(function (data, status, headers, config) {
                        callback(data, null);
                    }).
                    error(function (data, status, headers, config) {
                        callback(data, "failed to retrieve data");
                    });
            },
            fixingVenueAndTimeList: function (callback) {
                return $http({method: 'GET', url: '/fxi/fxiapi/refdata/fixingVenueAndTime'}).
                    success(function (data, status, headers, config) {
                        callback(data, null);
                    }).
                    error(function (data, status, headers, config) {
                        callback(data, "failed to retrieve data");
                    });
            },
            customerAccountList: function (callback, org) {
                return $http({method: 'GET', url: '/fxi/fxiapi/oms/getCustomer?orgName=' + org }).
                    success(function (data, status, headers, config) {
                        callback(data, null);
                    }).
                    error(function (data, status, headers, config) {
                        callback(data, "failed to retrieve data");
                    });
            }
        }
    }]);

OMSApp.factory('OrderService', ['$http',
    function ($http) {
        var self = this;
        return {
            pollAggregateOrders: function (queryParam ,callback) {
                return $http({
                    method: 'POST',
                    url: '/fxi/fxiapi/oms/getAggregateOrders',
                    data: queryParam
                }).
                    success(function (data, status, headers, config) {
                        callback(data, null);
                    }).
                    error(function (data, status, headers, config) {
                        callback(data, "failed to retrieve data");
                    });
            },
            pollOrders: function (queryParam ,callback) {
                return $http({
                    method: 'POST',
                    url: '/fxi/fxiapi/oms/getOrders',
                    data: queryParam
                }).
                    success(function (data, status, headers, config) {
                        callback(data, null);
                    }).
                    error(function (data, status, headers, config) {
                        callback(data, "failed to retrieve data");
                    });
            },
            pollOrdersOfOrg: function (brokerOrg, queryParam ,callback) {
                return $http({
                    method: 'POST',
                    url: '/fxi/fxiapi/oms/getOrdersOfOrg?brokerOrg=' + brokerOrg,
                    data: queryParam
                }).
                    success(function (data, status, headers, config) {
                        callback(data, null);
                    }).
                    error(function (data, status, headers, config) {
                        callback(data, "failed to retrieve data");
                    });
            },
            getAuditEvents: function (orderId , callback) {
                return $http({
                    method: 'GET',
                    params: {
                    	orderId :orderId
                    },
                    url: '/fxi/fxiapi/oms/getOMSAuditEvents'
                }).
                    success(function (data, status, headers, config) {
                        callback(data, null);
                    }).
                    error(function (data, status, headers, config) {
                        callback(data, "Order Audit query Failed : Server not reachable");
                    });
            },
            createOrder: function (order, callback, action) {
                return $http.post('/fxi/fxiapi/oms/createOrder?action=' + (action ? action : ""), JSON.stringify(order)).
                    success(function (data, status, headers, config) {
                        if (data.status == 'ERROR') {
                            callback(data, "Order submission failed, Reason: " + data.errorCode);
                        } else {
                            callback(data, null);
                        }
                    }).
                    error(function (data, status, headers, config) {
                        callback(data, "failed to retrieve data")
                    });
            },
            updateOrder: function (order, callback) {
                return $http({method: 'POST', url: '/fxi/fxiapi/oms/updateOrder', data: order}).
                    success(function (data, status, headers, config) {
                        if (data.status == 'ERROR') {
                            callback(data, "Failed to update order, Reason: " + data.errorCode);
                        } else {
                            callback(data, null);
                        }
                    }).
                    error(function (data, status, headers, config) {
                        callback(data, "failed to retrieve data");
                    });
            },
            updateAggregateOrder: function (order, callback) {
                return $http({method: 'POST', url: '/fxi/fxiapi/oms/updateAggregateOrder', data: order}).
                    success(function (data, status, headers, config) {
                        if (data.status == 'ERROR') {
                            callback(data, "Failed to update aggregate order, Reason: " + data.errorCode);
                        } else {
                            callback(data, null);
                        }
                    }).
                    error(function (data, status, headers, config) {
                        callback(data, "failed to retrieve data");
                    });
            },
            sendAction: function (orderId, action, order, context, callback) {
                return $http({
                    method: 'POST',
                    url: '/fxi/fxiapi/oms/action?orderId=' + orderId + '&action=' + action,
                    data: context!=null? context : {}
                }).
                    success(function (data, status, headers, config) {
                        if (data != null && data.status == 'ERROR') {
                        	var errorMsg  = (data.errorCode ? data.errorCode : "Order Action Failed");
                			callback(data, errorMsg);
                        } else {
                            callback(data, null);
                        }
                    }).
                    error(function (data, status, headers, config) {
                        callback(data, "Order Action Failed : Server not reachable");
                    });
            },
            sendBatchAction: function (data, action, callback) {
            	return $http({
            		method: 'POST',
            		url: '/fxi/fxiapi/oms/batchAction?action=' + action,
            		data: JSON.stringify(data)
            	}).
            	success(function (data, status, headers, config) {
            		if (data != null && data.status == 'ERROR') {
            			var errorMsg  = (data.errorCode ? data.errorCode : "Order Batch Action Failed");
            			callback(data, errorMsg);
            		} else {
            			callback(data, null);
            		}
            	}).
            	error(function (data, status, headers, config) {
            		callback(data, "Order Batch Action Failed : Server not reachable");
            	});
            },
            createBatchOrders: function (data, action, callback) {
            	return $http({
            		method: 'POST',
            		url: '/fxi/fxiapi/oms/createBatchOrders?action=' + ((action && action!="null")? action : ""),
            		data: JSON.stringify(data)
            	}).
            	success(function (data, status, headers, config) {
            		if (data != null && data.status == 'ERROR') {
            			var errorMsg  = (data.errorCode ? data.errorCode : "Order submission failed ");
            			callback(data, errorMsg);
            		} else {
            			callback(data, null);
            		}
            	}).
            	error(function (data, status, headers, config) {
            		callback(data, "Order Submission Failed : Server not reachable");
            	});
            }
        };

    }]);


OMSApp.factory('RateService', ['$http',
    function ($http) {
        var self = this;
        return {
            pollRates: function (ccyPairs ,processRates) {
                return $http({
                    method: 'GET',
                    params: {
                    	currencyPairs :ccyPairs
                    },
                    url: '/fxi/fxiapi/oms/getMarketRates'
                }).
                    success(function (data, status, headers, config) {
                        processRates(data, null);
                    }).
                    error(function (data, status, headers, config) {
                        processRates(data, "failed to retrieve data")
                    });
            }
        };

    }]);

OMSApp.factory('GridUtils', ['$http',
                               function ($http) {
                                   var self = this;
                                   self.statesMap = {};
                                   self.statesMap.DRAFT = ["DRAFT" ,"INTIAL"];
                                   self.statesMap.ACTIVE = ["ACTIVE", "TRIGGERED", "INUSE", "MATCHED"];
                                   self.statesMap.MATCHED = ["MATCHED"];
                                   self.statesMap.EXECUTED = ["EXECUTED"];
                                   self.statesMap.AUTO = ["AUTO"];
                                   self.statesMap.COMPLETED = ["EXPIRED" ,"CANCELLED" ,"ARCHIVED" ,"REJECTED" ,"EXPORTED"];
                                   
                                   return {
                                	    isActiveGrid : function(state) {
                                			 return self.statesMap.ACTIVE.indexOf(state) !=-1 ;
                                		},
                                		isDraftGrid : function(state) {
                                			return self.statesMap.DRAFT.indexOf(state) !=-1 ;
                                		},
                                		isMatchedGrid : function(state) {
                                			return self.statesMap.MATCHED.indexOf(state) !=-1 ;
                                		},
                                		isExecutedGrid : function(state) {
                                			return self.statesMap.EXECUTED.indexOf(state) !=-1 ;
                                		},
                                		isAutoGrid : function(state) {
                                			return self.statesMap.AUTO.indexOf(state) !=-1 ;
                                		},
                                		isCompletedGrid : function(state) {
                                			return self.statesMap.COMPLETED.indexOf(state) !=-1 ;
                                		}
                                   };
      }]);





OMSApp.factory('Utils', ['$http','$rootScope' ,'$filter' , function ($http ,$rootScope ,$filter) {
	var self = this;
	var MARKET_TABLE_LENGTH = 12;
	
		self.getRate = function (ccyPair, bidOffer) {
	        if ($rootScope.rates != null) {
	            return $rootScope.rates[ccyPair + '/' + bidOffer];
	        }
	        return null;
	    };
		return {
		 formatRate :  function(rate ,ccyPair) {
			if(angular.isUndefined(rate) || rate === null) {
				return undefined;
			}
	    	var precision = 5;
	    	if(ccyPair && $rootScope.ccyPairConfig) {
	    		var ccyConfig = $rootScope.ccyPairConfig[ccyPair];
	    		if(ccyConfig) {
	    			precision = $rootScope.ccyPairConfig[ccyPair].spotPrecision;
	    		}
	    	}
	    	rate = parseFloat(rate).toFixed(precision);
    	    return rate;
    	 },
    	 /**
    	  *  Remove entity from grid
    	  */
    	 removeEntityFromArray : function(array , entity) {
    	 	var newData = [];
    	 	for(var index in array) {
    	 		var row = array[index];
    	 		if(row.orderId != entity.orderId) {
    	 			newData.push(row);
    	 		}
    	 	}
    	 	return newData;
    	 	
    	 },
         calculatePercentage : function (order) {
             if (order.orderPrice != null) {
            	 var dealtInBase = (order.ccyPair.substring(0, 3) === order.dealtCcy);
            	 var isBuy = dealtInBase ? (order.side == 'BUY') : (order.side != 'BUY');
            	 var orderType = null;
            	 if(order.orderType.selected == null)
            	 {
            		 orderType = order.orderType;
            	 }
            	 else{
            		 orderType = order.orderType.selected.value;
            	 }
            	 var marketRate = null;
                 if ((isBuy && orderType != 'STOP') || (!isBuy && orderType == 'STOP')) {
                	 marketRate = self.getRate(order.ccyPair, 'OFFER');
                 } else if((!isBuy && orderType != 'STOP') || (isBuy && orderType == 'STOP'))  {
                	 marketRate = self.getRate(order.ccyPair, 'BID');                     
                 }
                 if (marketRate != null) {
                     order.marketRate = marketRate;
                     order.percentage = ((order.orderPrice / marketRate) * 100).toFixed(6);
                 }
             }
             return order;
         },
         
         getRate : function (ccyPair, bidOffer) {
        	 return self.getRate(ccyPair, bidOffer);
        	},
        	
    	 getUpdatedOrder : function (order, colDef , newValue) {
    	     var updatedOrder = {};
    	     updatedOrder.orderId = order.orderId;
    	     updatedOrder.orderIds = order.orderIds;
    	     updatedOrder[colDef.field] = newValue;
    	     return updatedOrder;
    	 },
    	 
    	 /**
    	  *  Insert new Entity to Table , If already exists then update it
    	  */
    	 upsertEntityInArray : function(array , entity) {
    	 	var newData = [];
    	 	for(var index in array) {
    	 		var row = array[index];
    	 		if(row.orderId != "" && row.orderId != entity.orderId) {
    	 			newData.push(row);
    	 		}
    	 	}
    	 	newData.push(entity);
    	 	
    	 	return newData;
    	 	
    	 },
         addPadding : function(aboveMarket , isAbove , marketRows) {
         	if(aboveMarket.length < marketRows) {
             	var count = marketRows - aboveMarket.length;
                 for(var i = 0; i <count ;i++)
              	   aboveMarket.push( {'percentage': isAbove?Number.MAX_VALUE:Number.MIN_VALUE , 'orderId':'' , 'tmpId':isAbove+i});
                 }
           },
		 notificationMsg : function(order, newState, action, oldState) {
			 	var notificationMsg = "";
			    var baseCcyStr = order.ccyPair.substring(0,3 );
			    var varCcyStr = order.ccyPair.substring(4,7 );
			    var printCcy='';
			    if(order.dealtCcy == baseCcyStr) {
			        printCcy = varCcyStr;
			    } else {
			        printCcy = baseCcyStr;
			    }
			    
			    var fillRate = this.formatRate(order.fillPrice ,order.ccyPair);
			    var orderRate = this.formatRate(order.orderPrice ,order.ccyPair);
			    var dealtAmount = $filter('number')(order.dealtAmt);
			    var orgName = order.cptyAOrg!=null ? order.cptyAOrg + "'s " : "Draft "
			    var orderDetailStr = "Order to "+order.side +" "+ dealtAmount +" "+ order.dealtCcy +" vs "+ printCcy;
			    if (newState == 'SUBMITTED') {
			        notificationMsg = orgName + orderDetailStr  + " at " + orderRate + " is SUBMITTED";
				} else if (action == 'autoexecute') {
			         notificationMsg = orgName + orderDetailStr +" sent to EMS for Auto Execution";

				} else if (oldState == 'AUTO' && newState == 'ACTIVE' ) {
					 notificationMsg = orgName + orderDetailStr +" cancelled from EMS for manual management";
				} else if (newState == 'ACTIVE') {
					notificationMsg = orgName + orderDetailStr  + " is ACTIVE now";
				} else if (newState == 'DRAFT') {
			         notificationMsg = orgName + orderDetailStr  + " moved to DRAFT";
				} else if (newState == 'ARCHIVED') {
			         notificationMsg = orgName + orderDetailStr  + " ARCHIVED";
				} else if (newState == 'MATCHED') {
			        notificationMsg = orgName + orderDetailStr +" was FILLED";
					if(order.fillPrice) {
						notificationMsg = notificationMsg + " at " + fillRate;
					}
				} else 	if (newState == 'EXECUTED') {
			         notificationMsg =  order.cptyAOrg + " is NOTIFIED that " + orderDetailStr +" was Filled";

			     } else if (newState == 'TRIGGERED') {
			         notificationMsg = orgName + orderDetailStr +" TRIGGERED";
			         
			     } else if (newState == 'CANCELLED') {
			         notificationMsg = orgName + orderDetailStr +" CANCELLED";

				}else {
					notificationMsg = orgName + orderDetailStr +" changed state";
				}
				return notificationMsg;
			}
	};
	
}]);

OMSApp.factory('AuthService', ['$rootScope','RESTService','ReferenceDataService','websocket','$cookies','$window', '$filter', function ($rootScope, RESTService, ReferenceDataService, websocket, $cookies ,$window, $filter ) {
    var currentUser = null;
    var authorized = false;
    var currentOrg = null; 
    var refDataCheck = false;
    var ssoLoginOrg = "";
    var ssoLoginUser = "";
    var portalLogin = false;
    $rootScope.timeZoneOffset = "+0000";
    $rootScope.timeZoneId = "GMT";
    $rootScope.dtAndTimeFmt = "yyyy/MM/dd HH:mm:ss"
    $rootScope.browserAndUserTimeZoneDiff = 0;
    $rootScope.expandOrderEntry = false;
    $rootScope.enableAutoForOCXOrders = false;
    $rootScope.showAutoAndManual = false;
    var  self = this;
    $rootScope.minimumUIWidth = 1600;
    $rootScope.ssoDomain = "";
    $rootScope.ssoToken  = "";
    $rootScope.tokenInfo  = "";
    $rootScope.dtFmt = "yyyy/MM/dd"
    $rootScope.timeFmt = "HH:mm"
    
    self.removeCookies = function(){
    	delete $cookies["AUTH_TOKEN"];
		delete $cookies["SSO_TOKEN"];
		delete $cookies["jsmssid"];
		delete $cookies["JSESSIONID"];
		delete $cookies["EXTERNAL_SSO_AUTH"];
    };
    
    var initialState = true;

    return {
        initialState: function () {
            return initialState;
        },
        login: function (org, name, password, callback) {
            RESTService.login(org, name, password, function (response, data) {
            	if (response.status == 'OK') {
            		ReferenceDataService.refData(function (data, errorMsg) {
               		 if(data && data.status == 'OK') {
               			 if(data.ccyPairList) {
                	    	 var ccyPairs = [];
                   	    	 var ccyPairConfig = [];
                   			 for (var i = 0; i < data.ccyPairList.length; i++) {
                   	             var ccyPair = data.ccyPairList[i].instrument;
                   	             ccyPairs.push(ccyPair);
                   	             ccyPairConfig[ccyPair] = data.ccyPairList[i];
                   	         }
                   	         $rootScope.ccyPairs = ccyPairs;
                   	         $rootScope.ccyPairConfig = ccyPairConfig;
               			 }
   
               			 $rootScope.homeCcy = data.homeCcy;
               			 $rootScope.passAccounts = data.passAccounts;
               			 $rootScope.customer = data.customer;
               			 $rootScope.customerOrg = data.customerOrg;
               			 $rootScope.brokerOrg = data.brokerOrg;
               			 currentUser = name;
    	                 currentOrg = org;
    	                 authorized = true;
    	                 initialState = false;
    	                 $rootScope.createOrder = true;
    	                 $rootScope.fetchCustomerOrders = true;
    	                 var currentDate = new Date(moment.utc());//This is in browsers timestamp 
               			 var gmtDate = new Date($filter('date')(currentDate, "yyyy/MM/dd HH:mm:ss", "+0000"));
               			 $rootScope.browserGMTTimeDiff = currentDate.getTime()- gmtDate.getTime();               			 
               			 if(data.orgUISettings) { 
               				if(data.orgUISettings.riskWarehouseEnabled && data.orgUISettings.riskWarehouseEnabled=="true") {
              					 $rootScope.riskWarehouseEnabled = true;
              				 }
               				 if(data.orgUISettings.marketPollInterval) {
               					 $rootScope.marketPollInterval = data.orgUISettings.marketPollInterval;
               				 }
               				 if(data.orgUISettings.marketPollInterval) {
               					 $rootScope.orderPollInterval = data.orgUISettings.orderPollInterval;
               				 }
               				 if(data.orgUISettings.buyPercThreshold) {
              					 $rootScope.buyPercThreshold = data.orgUISettings.buyPercThreshold;
              				 }
              				 if(data.orgUISettings.sellPercThreshold) {
              					 $rootScope.sellPercThreshold = data.orgUISettings.sellPercThreshold;
              				 }
              				 if(data.orgUISettings.showAutoOrdersInMonitorTab) {
             					 $rootScope.showAutoOrdersInMonitorTab = data.orgUISettings.showAutoOrdersInMonitorTab;
             				 }
              				 if(data.orgUISettings.exportOrders && data.orgUISettings.exportOrders.toUpperCase() == "TRUE") {
            					 $rootScope.exportOrders = true;
            				 }
              				 if(data.orgUISettings.enableAutoForOCXOrders && data.orgUISettings.enableAutoForOCXOrders.toUpperCase() == "TRUE"){
              					$rootScope.enableAutoForOCXOrders = true;
              				 }
              				 if(data.orgUISettings.minimumUIWidth) {
            					 $rootScope.minimumUIWidth = data.orgUISettings.minimumUIWidth;
            				 }
               				 if(data.orgUISettings.heartbeatPollInterval) {
               					 $rootScope.heartBeatInterval = data.orgUISettings.heartbeatPollInterval;
               				 }
               				 if(data.orgUISettings.showNotifiedTab && data.orgUISettings.showNotifiedTab =="false") {
               			         $rootScope.showNotifiedTab = false;
               				 }
               				 if(data.orgUISettings.showFilledTab && data.orgUISettings.showFilledTab =="false") {
               					 $rootScope.showFilledTab = false;
               				 }
               				 if(data.orgUISettings.showSentTab && data.orgUISettings.showSentTab =="false") {
               					 $rootScope.showSentTab = false;
               				 }
               				 if(data.orgUISettings.showReceivedTab && data.orgUISettings.showReceivedTab =="false") {
               					 $rootScope.showReceivedTab = false;
               				 }
                             if(data.orgUISettings.monitor1Name) {
               					 $rootScope.showMonitor1Tab = true;
               					 $rootScope.monitor1Name = data.orgUISettings.monitor1Name;
               				 }else{
               				    $rootScope.showMonitor1Tab = false;
               				 }
               				 if(data.orgUISettings.monitor2Name) {
               					 $rootScope.showMonitor2Tab = true;
               					 $rootScope.monitor2Name = data.orgUISettings.monitor2Name;
               				 }else{
               				    $rootScope.showMonitor2Tab = false;
               				 }
               				
               			 }
               		 } else {
               			$rootScope.errorMsg = "Permission Denied";
               		 }
            		});
            		ReferenceDataService.userPrefData(function (data, errorMsg) {
                  		 if(data && data.status == 'OK' && data.userDP) {
                  			 $rootScope.dtAndTimeFmt = data.userDP.dtFmt+" "+data.userDP.timeFmt;
                  			 $rootScope.dtAndTimeFmt = $rootScope.dtAndTimeFmt.replace("zzz","");
                  			 $rootScope.dtAndTimeFmt = $rootScope.dtAndTimeFmt.replace("aa","a");
                  			 $rootScope.dtFmt = data.userDP.dtFmt;
                   			 $rootScope.amtFmt = data.userDP.amtFmt;
                   			 $rootScope.thousandAmtSep = data.userDP.thousandAmtSep;              			               			 
                   			 $rootScope.timeZoneOffset = data.userDP.timeZoneOffset;
                   			 $rootScope.timeZoneId = data.userDP.timeZoneId;
                   			 var currentDate = new Date(moment.utc());//This is in browsers timestamp 
                   			 var userTimeZoneDate = new Date($filter('date')(currentDate, "yyyy/MM/dd HH:mm:ss", $rootScope.timeZoneOffset));
                  			 $rootScope.browserAndUserTimeZoneDiff = currentDate.getTime()- userTimeZoneDate.getTime();
                  		 } });	    	    	
                } else {
                	callback(response ,data);
                    currentUser = null;
                    authorized = false;
                }
            });
            RESTService.addUserSession();
            RESTService.getSsoToken();
        },
        ssoLogin: function () {
        	if(!refDataCheck && $rootScope.activeTopInitialized && $rootScope.activeBottomInitialized){       			    	        							
            		ReferenceDataService.refData(function (data, errorMsg) {
               		 if(data && data.status == 'OK') {
               			 if(data.ccyPairList) {
                	    	 var ccyPairs = [];
                   	    	 var ccyPairConfig = [];
                   			 for (var i = 0; i < data.ccyPairList.length; i++) {
                   	             var ccyPair = data.ccyPairList[i].instrument;
                   	             ccyPairs.push(ccyPair);
                   	             ccyPairConfig[ccyPair] = data.ccyPairList[i];
                   	         }
                   	         $rootScope.ccyPairs = ccyPairs;
                   	         $rootScope.ccyPairConfig = ccyPairConfig;
               			 }
   
               			 $rootScope.homeCcy = data.homeCcy;
               			 $rootScope.passAccounts = data.passAccounts;
               			 $rootScope.customer = data.customer;
               			 $rootScope.customerOrg = data.customerOrg;
               			 $rootScope.brokerOrg = data.brokerOrg;
               			 currentUser = ssoLoginUser;
    	                 currentOrg = ssoLoginOrg;
    	                 if(portalLogin){
    	                    appsWidget('#app-list-widget');
    	                 }
    	                 authorized = true;
    	                 initialState = false;
    	                 $rootScope.createOrder = true;
    	                 $rootScope.fetchCustomerOrders = true;
    	                 var currentDate = new Date(moment.utc());//This is in browsers timestamp 
               			 var gmtDate = new Date($filter('date')(currentDate, "yyyy/MM/dd HH:mm:ss", "+0000"));
               			 $rootScope.browserGMTTimeDiff = currentDate.getTime()- gmtDate.getTime();               			 
               			 if(data.orgUISettings) { 
               				if(data.orgUISettings.riskWarehouseEnabled && data.orgUISettings.riskWarehouseEnabled=="true") {
              					 $rootScope.riskWarehouseEnabled = true;
              				 }
               				 if(data.orgUISettings.marketPollInterval) {
               					 $rootScope.marketPollInterval = data.orgUISettings.marketPollInterval;
               				 }
               				 if(data.orgUISettings.marketPollInterval) {
               					 $rootScope.orderPollInterval = data.orgUISettings.orderPollInterval;
               				 }
               				 if(data.orgUISettings.buyPercThreshold) {
              					 $rootScope.buyPercThreshold = data.orgUISettings.buyPercThreshold;
              				 }
              				 if(data.orgUISettings.sellPercThreshold) {
              					 $rootScope.sellPercThreshold = data.orgUISettings.sellPercThreshold;
              				 }
              				 if(data.orgUISettings.showAutoOrdersInMonitorTab) {
             					 $rootScope.showAutoOrdersInMonitorTab = data.orgUISettings.showAutoOrdersInMonitorTab;
             				 }
              				 if(data.orgUISettings.exportOrders && data.orgUISettings.exportOrders.toUpperCase() == "TRUE") {
            					 $rootScope.exportOrders = true;
            				 }
              				 if(data.orgUISettings.enableAutoForOCXOrders && data.orgUISettings.enableAutoForOCXOrders.toUpperCase() == "TRUE"){
              					$rootScope.enableAutoForOCXOrders = true;
              				 }
              				 if(data.orgUISettings.minimumUIWidth) {
            					 $rootScope.minimumUIWidth = data.orgUISettings.minimumUIWidth;
            				 }
               				 if(data.orgUISettings.heartbeatPollInterval) {
               					 $rootScope.heartBeatInterval = data.orgUISettings.heartbeatPollInterval;
               				 }
               				 if(data.orgUISettings.showNotifiedTab && data.orgUISettings.showNotifiedTab =="false") {
               			         $rootScope.showNotifiedTab = false;
               				 }
               				 if(data.orgUISettings.showFilledTab && data.orgUISettings.showFilledTab =="false") {
               					 $rootScope.showFilledTab = false;
               				 }
               				 if(data.orgUISettings.showSentTab && data.orgUISettings.showSentTab =="false") {
               					 $rootScope.showSentTab = false;
               				 }
               				 if(data.orgUISettings.showReceivedTab && data.orgUISettings.showReceivedTab =="false") {
               					 $rootScope.showReceivedTab = false;
               				 }
                             if(data.orgUISettings.monitor1Name) {
               					 $rootScope.showMonitor1Tab = true;
               					 $rootScope.monitor1Name = data.orgUISettings.monitor1Name;
               				 }else{
               				    $rootScope.showMonitor1Tab = false;
               				 }
               				 if(data.orgUISettings.monitor2Name) {
               					 $rootScope.showMonitor2Tab = true;
               					 $rootScope.monitor2Name = data.orgUISettings.monitor2Name;
               				 }else{
               				    $rootScope.showMonitor2Tab = false;
               				 }
               				
               			 }
               		 } else {
               			$rootScope.errorMsg = "Permission Denied";
               		 }
            		});
            		ReferenceDataService.userPrefData(function (data, errorMsg) {
                  		 if(data && data.status == 'OK' && data.userDP) {
                  			 $rootScope.dtAndTimeFmt = data.userDP.dtFmt+" "+data.userDP.timeFmt;
                  			 $rootScope.dtAndTimeFmt = $rootScope.dtAndTimeFmt.replace("zzz","");
                  			 $rootScope.dtAndTimeFmt = $rootScope.dtAndTimeFmt.replace("aa","a");
                  			 $rootScope.dtFmt = data.userDP.dtFmt;
                   			 $rootScope.amtFmt = data.userDP.amtFmt;
                   			 $rootScope.thousandAmtSep = data.userDP.thousandAmtSep;              			               			 
                   			 $rootScope.timeZoneOffset = data.userDP.timeZoneOffset;
                   			 $rootScope.timeZoneId = data.userDP.timeZoneId;
                   			 var currentDate = new Date(moment.utc());//This is in browsers timestamp 
                   			 var userTimeZoneDate = new Date($filter('date')(currentDate, "yyyy/MM/dd HH:mm:ss", $rootScope.timeZoneOffset));
                  			 $rootScope.browserAndUserTimeZoneDiff = currentDate.getTime()- userTimeZoneDate.getTime();
                  		 } });   	    	           
            		RESTService.addUserSession();
            		RESTService.getSsoToken();
            		refDataCheck = true;              	 
            }
        },
        logout: function () {
        	RESTService.logout(function (response, data) {
                if (response.status == 'OK') {
                	console.log("Logout Successful");
                }
                websocket.disconnect();
                self.removeCookies();
                currentUser = null;
                authorized = false;
                if(ssoLoginUser.length > 0 && ssoLoginOrg.length > 0)
           	 	{
               		window.location.href = "/fxi/integral/sso/external/errorpage.jsp";
             	}
               
            },"User Logged out");
        },
        isExternalSSOAuthCookieValid : function () {
            var temp =  $cookies.get('EXTERNAL_SSO_AUTH');
            if(temp != null)
            {
             console.log("Inside isExternalSSOAuthCookieValid: "+temp);
             if(temp == "LoggedIn@")
             {
             	return false;
             }
           	 var user = temp.split("@");
           	 ssoLoginUser = user[0];
           	 ssoLoginOrg = user[1];
           	 RESTService.getSsoDomain();
           	 if(ssoLoginUser.length > 0 && ssoLoginOrg.length > 0)
           	 {
           	    var domainValue = null;
           	    if($rootScope.ssoDomain != ""){
           	        domainValue = $rootScope.ssoDomain;
           	    }
           	    console.log("Inside isExternalSSOAuthCookieValid domainValue:  "+domainValue+", $rootScope.ssoDomain: "+$rootScope.ssoDomain);
           	 	$cookies.put("EXTERNAL_SSO_AUTH", "LoggedIn@", {path: '/', domain:domainValue});
            	return true;
             }
             else
             {
             return false;
             }
            }
            else
            {
            	return false;
            }
        },
        isPortalLogin : function () {
            var loginType =  $cookies.get('LOGIN_TYPE');
            if(portalLogin){
                return true;
            }
            if(loginType != null && loginType == "PORTAL")
            {
                  console.log("Inside isPortalLogin: "+loginType);
	              RESTService.getTokenInfo();
                  ssoLoginUser = $rootScope.tokenInfo.userName;
                  ssoLoginOrg = $rootScope.tokenInfo.userOrgName;
                  if(ssoLoginUser.length > 0 && ssoLoginOrg.length > 0)
                  {
                     portalLogin = true;
                     return true;
                  }
                  else
                  {
	                return false;
                  }
             }
             else
             {
                  return false;
             }
        },

        isSSOLoggedIn: function () {
        	if(ssoLoginUser.length > 0 && ssoLoginOrg.length > 0)
           	{
            	return true;
            }
            return false;
        },
        isLoggedIn: function () {
            return authorized;
        },
        currentUser: function () {
            return currentUser;
        },
        currentOrg: function () {
            return currentOrg;
        },
        tradeDate: function () {
            return $filter('date')(new Date(moment.utc()), "MM/dd/yyyy");
        },
        timeZoneId: function () {
            return $rootScope.timeZoneId;
        },
        dayOfWeek: function () {
            return $filter('date')(new Date(moment.utc()), "EEE");
        },
        userTimeZoneDate: function () {
            return $filter('date')(new Date(moment.utc()), "dd MMM yyyy HH:mm:ss", $rootScope.timeZoneOffset);
        },
        authorized: function () {
            return authorized;
        },
        createOrder: function(){
        	if($('.navbar-toggle').is(":visible")) {
        		$('.navbar-toggle').click();
        	} 
        	$rootScope.createOrder = true;
        },
        viewOrder: function(){
          	if($('.navbar-toggle').is(":visible")) {
        		$('.navbar-toggle').click();
        	} 
        	$rootScope.createOrder = false;   
        	$rootScope.fetchCustomerOrders = true;
        },
        resetSession: function () {
        	 websocket.disconnect();
        	 currentUser = null;
             authorized = false;
             if(ssoLoginUser.length > 0 && ssoLoginOrg.length > 0)
           	 {
               		window.location.href = "/fxi/integral/sso/external/errorpage.jsp";
             }
        }
    };
}
]);
/**
 * Created by sameer on 4/20/15.
 */
'use strict';
OMSApp.factory('websocket', function ($location , $rootScope ,$timeout) {
	    var socket = {}
	    var promise = null;

	    function connect() {
	        if (!promise) {
	            console.log('Initializing Websocket');
	            promise = $timeout(function () {
	            	 
	    			 var socketUrl = $location.protocol()+ "://" + $location.host();
	    			 if($location.port() != "443" && $location.port() != "80") {
	    				 socketUrl = socketUrl + ":" + "9092"
	    			 }
	    			 socket  = io.connect(socketUrl , {path:"/OMSSocket"});
	    			 $rootScope.webSocketInitialized = true;
	                 return socket;
	            }, 3000);
	        }

	        return promise;
	    };
	    
	    function on (eventName, callback) {
	  		  socket.on(eventName, function () {  
	  		        var args = arguments;
	  		        $rootScope.$apply(function () {
	  		          callback.apply(socket, args);
	  		        });
	  		      });
	  		    };
	  		    
	  	function emit (eventName, data, callback) {
		  	      socket.emit(eventName, data, function () {
		  		        var args = arguments;
		  		        $rootScope.$apply(function () {
		  		          if (callback) {
		  		            callback.apply(socket, args);
		  		          }
		  		        });
		  		      })
		  		    };
	    return {
	    	connect: connect,
	    	disconnect: function() {
	    		if(promise) {
	    			$timeout(function(){
	    				socket.disconnect();
		    			socket = null;
		    			promise = null;
		    			$rootScope.webSocketInitialized = false;
		    			$rootScope.webSocketProtocol = false;
		    			$rootScope.webSocket = null;
	    			});
	    		}
	    	},
	    	removeListener:function(eventName) {
				socket.removeListener(eventName);
			},
	  	  	on 	: on,
	  	    emit : emit
	    };
});


'use strict';

directives.directive('fadeIn', function () {
    return {
        compile: function (elm) {
            $(elm).css('opacity', 0.0);
            return function (scope, elm, attrs) {
                $(elm).animate({opacity: 1.0}, 1500);
            };
        }
    };
});


directives.directive('uiGridRow', function ($animate, $timeout, $rootScope, uiGridConstants) {
    return {
        priority: -1,
        link: function ($scope, $elm, $attrs) {
            $scope.$watch('row.entity', function (n, o) {
                if ($scope.row.isNew || $scope.row.isTriggered) {

                    var cssClass = $scope.row.isNew ? 'new-row' : 'trigger';
                    var animationTime = $scope.row.isNew ? 700 : 3000;
                    $rootScope.isAnimationInProgress = true;
                    $elm.addClass(cssClass);

                    $timeout(function () {
                        $animate.removeClass($elm, cssClass);
                        $scope.row.isNew = false;
                        $scope.row.isTriggered = false;
                        $rootScope.isAnimationInProgress = false;
                    }, animationTime);

                   
                }
            });

            $scope.$on('action', function (evt, row, action) {
                var cssClass = 'blinkOnce';

                $scope.grid.appScope.executeAction(row, row.entity, action, null, function (success, errorMsg) {
                    $rootScope.isAnimationInProgress = true;
                    $animate.addClass($elm, cssClass)
                        // The animation is done.
                        .then(function () {
                            $animate.removeClass($elm, cssClass);
                            $rootScope.isAnimationInProgress = false;
                        });
                });
            });
        }
    }
})

    .directive('uiGridCell', function () {
        return {
            priority: -1,
            link: function ($scope, $elm, $attrs) {
                // Expose the deleteRow function to the cell scope so our custom template
                //  can call it
                $scope.execAction = execAction;
                // When the delete button is clicked, emit a "delete-row" event and pass
                //   the row as an argument
                function execAction(row, action) {
                    $scope.$emit('action', row, action);
                }
            }
        }
    })

    .directive('uiSelectWrap', ['$document', 'uiGridEditConstants', function ($document, uiGridEditConstants) {
        return {
            link: function ($scope, $elm, $attr) {

                $document.on('click', docClick);

                function docClick(evt) {
                    if ($(evt.target).closest('.ui-select-container').size() === 0) {
                        $scope.$emit(uiGridEditConstants.events.END_CELL_EDIT);
                        $document.off('click', docClick);
                    }
                }
            }
        }
    }])

    .directive('dateTimeSelectorWrap', ['$document', 'uiGridEditConstants', function ($document, uiGridEditConstants) {
        return {
            link: function ($scope, $elm, $attr) {

                $document.on('click', docClick);

                function docClick(evt) {
                    if ($(evt.target).closest('.dropdown1').size() === 0) {
                        $scope.$emit(uiGridEditConstants.events.END_CELL_EDIT);
                        $document.off('click', docClick);
                    }
                }
            }
        }
    }]);



/*
 This directive allows us to pass a function in on an enter key to do what we want.
 */
'use strict';

directives.directive('ngEnter', function () {
    return function (scope, element, attrs) {
        element.bind("keydown keypress", function (event) {
            if (event.which === 13) {
                scope.$apply(function () {
                    scope.$eval(attrs.ngEnter);
                });

                event.preventDefault();
            }
        });
    };
});
'use strict';

// this is the angular way to stop even propagation
directives.directive('stopEvent', function () {
    return {
        restrict: 'A',
        link: function (scope, element, attr) {
            element.bind(attr.stopEvent, function (e) {
                e.stopPropagation();
            });
        }
    }
});

directives.directive('a', function () {
    return {
        restrict: 'E',
        link: function (scope, elem, attrs) {
            if (attrs.ngClick || attrs.href === '' || attrs.href === '#') {
                elem.on('click', function (e) {
                    e.preventDefault();
                });
            }
        }
    };
});
'use strict';

directives.directive('noty', function () {

    return {
        restrict:'A',

        link: function (scope, element, attr) {

            // set notification (noty) defaults on global scope
            var opts = {
                layout: 'top',
                theme: 'nucleusTheme',
                dismissQueue: true, // If you want to use queue feature set this true
                template: '<div class="noty_message"><span class="noty_text"></span><div class="noty_close"></div></div>',
                animation: {
                    open: {height: 'toggle'},
                    close: {height: 'toggle'},
                    easing: 'swing',
                    speed: 200 // opening & closing animation speed
                },
                timeout: 5000, // delay for closing event. Set false for sticky notifications
                force: false, // adds notification to the beginning of queue when set to true
                modal: false,
                maxVisible: 1, // you can set max visible notification for dismissQueue true option
                closeWith: ['click'], // ['click', 'button', 'hover']
                callback: {
                    onShow: function() {},
                    afterShow: function() {},
                    onClose: function() {},
                    afterClose: function() {}
                },
                buttons: false // an array of buttons
            };

            var index = scope.$index;
            var notification = scope.notifications[index];
            var text = notification['text'];
            var type = notification['type'];

            opts.text = text;
            opts.type = type;

            // errors persist on screen longer
            if (type == 'error' || type == 'alertInformation') {
                opts.timeout = false;
            }

            notification['processed'] = true;

            noty(opts);
        }
    }
});
