package com.integral.util.ratelimiting;

import com.integral.system.configuration.IdcMBean;

/**
 * MBean interface for rate limiting configuration.
 * Provides configuration parameters for rate limiting functionality
 * across the Integral platform.
 * 
 * <AUTHOR> Development Corporation.
 */
public interface RateLimitingMBean extends IdcMBean {
    
    // Global Rate Limiting Configuration Constants
    String RATE_LIMITING_ENABLED = "IDC.RATE_LIMITING.ENABLED";
    String RATE_LIMITING_HEADER_NAME = "IDC.RATE_LIMITING.HEADER_NAME";
    String RATE_LIMITING_MAX_REQUESTS = "IDC.RATE_LIMITING.MAX_REQUESTS";
    String RATE_LIMITING_TIME_WINDOW_SECONDS = "IDC.RATE_LIMITING.TIME_WINDOW_SECONDS";
    String RATE_LIMITING_CLEANUP_INTERVAL_SECONDS = "IDC.RATE_LIMITING.CLEANUP_INTERVAL_SECONDS";
    
    // FXIAPI-specific rate limiting configuration
    String FXIAPI_RATE_LIMITING_ENABLED = "IDC.FXIAPI.RATE_LIMITING.ENABLED";
    String FXIAPI_RATE_LIMITING_HEADER_NAME = "IDC.FXIAPI.RATE_LIMITING.HEADER_NAME";
    String FXIAPI_RATE_LIMITING_MAX_REQUESTS = "IDC.FXIAPI.RATE_LIMITING.MAX_REQUESTS";
    String FXIAPI_RATE_LIMITING_TIME_WINDOW_SECONDS = "IDC.FXIAPI.RATE_LIMITING.TIME_WINDOW_SECONDS";
    String FXIAPI_RATE_LIMITING_CLEANUP_INTERVAL_SECONDS = "IDC.FXIAPI.RATE_LIMITING.CLEANUP_INTERVAL_SECONDS";
    
    // Admin-specific rate limiting configuration
    String ADMIN_RATE_LIMITING_ENABLED = "IDC.ADMIN.RATE_LIMITING.ENABLED";
    String ADMIN_RATE_LIMITING_HEADER_NAME = "IDC.ADMIN.RATE_LIMITING.HEADER_NAME";
    String ADMIN_RATE_LIMITING_MAX_REQUESTS = "IDC.ADMIN.RATE_LIMITING.MAX_REQUESTS";
    String ADMIN_RATE_LIMITING_TIME_WINDOW_SECONDS = "IDC.ADMIN.RATE_LIMITING.TIME_WINDOW_SECONDS";
    String ADMIN_RATE_LIMITING_CLEANUP_INTERVAL_SECONDS = "IDC.ADMIN.RATE_LIMITING.CLEANUP_INTERVAL_SECONDS";

    /**
     * Returns whether global rate limiting is enabled
     * @return true if global rate limiting is enabled
     */
    boolean isRateLimitingEnabled();

    /**
     * Returns the global HTTP header name to use for rate limiting identification
     * @return header name for rate limiting
     */
    String getRateLimitingHeaderName();

    /**
     * Returns the global maximum number of requests allowed within the time window
     * @return maximum requests per time window
     */
    int getRateLimitingMaxRequests();

    /**
     * Returns the global time window in seconds for rate limiting
     * @return time window in seconds
     */
    int getRateLimitingTimeWindowSeconds();

    /**
     * Returns the global cleanup interval in seconds for expired rate limiting entries
     * @return cleanup interval in seconds
     */
    int getRateLimitingCleanupIntervalSeconds();
    
    // FXIAPI-specific rate limiting methods
    
    /**
     * Returns whether FXIAPI rate limiting is enabled
     * @return true if FXIAPI rate limiting is enabled
     */
    boolean isFxiapiRateLimitingEnabled();

    /**
     * Returns the FXIAPI HTTP header name to use for rate limiting identification
     * @return header name for rate limiting
     */
    String getFxiapiRateLimitingHeaderName();

    /**
     * Returns the FXIAPI maximum number of requests allowed within the time window
     * @return maximum requests per time window
     */
    int getFxiapiRateLimitingMaxRequests();

    /**
     * Returns the FXIAPI time window in seconds for rate limiting
     * @return time window in seconds
     */
    int getFxiapiRateLimitingTimeWindowSeconds();

    /**
     * Returns the FXIAPI cleanup interval in seconds for expired rate limiting entries
     * @return cleanup interval in seconds
     */
    int getFxiapiRateLimitingCleanupIntervalSeconds();
    
    // Admin-specific rate limiting methods
    
    /**
     * Returns whether Admin rate limiting is enabled
     * @return true if Admin rate limiting is enabled
     */
    boolean isAdminRateLimitingEnabled();

    /**
     * Returns the Admin HTTP header name to use for rate limiting identification
     * @return header name for rate limiting
     */
    String getAdminRateLimitingHeaderName();

    /**
     * Returns the Admin maximum number of requests allowed within the time window
     * @return maximum requests per time window
     */
    int getAdminRateLimitingMaxRequests();

    /**
     * Returns the Admin time window in seconds for rate limiting
     * @return time window in seconds
     */
    int getAdminRateLimitingTimeWindowSeconds();

    /**
     * Returns the Admin cleanup interval in seconds for expired rate limiting entries
     * @return cleanup interval in seconds
     */
    int getAdminRateLimitingCleanupIntervalSeconds();
}
