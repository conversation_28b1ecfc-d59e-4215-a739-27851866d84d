package com.integral.fxiapi.util;

import com.integral.log.Log;
import com.integral.log.LogFactory;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Rate limiting manager that implements a sliding window algorithm
 * to control request rates based on configurable identifiers.
 * 
 * <AUTHOR> Development Corporation.
 */
public class RateLimitingManager {
    
    private static final Log log = LogFactory.getLog(RateLimitingManager.class);
    
    private final ConcurrentMap<String, RateLimitEntry> rateLimitMap;
    private final ScheduledExecutorService cleanupExecutor;
    private final int maxRequests;
    private final long timeWindowMillis;
    private final long cleanupIntervalMillis;
    
    /**
     * Constructor for RateLimitingManager
     * 
     * @param maxRequests Maximum number of requests allowed in the time window
     * @param timeWindowSeconds Time window in seconds
     * @param cleanupIntervalSeconds Cleanup interval in seconds for expired entries
     */
    public RateLimitingManager(int maxRequests, int timeWindowSeconds, int cleanupIntervalSeconds) {
        this.maxRequests = maxRequests;
        this.timeWindowMillis = timeWindowSeconds * 1000L;
        this.cleanupIntervalMillis = cleanupIntervalSeconds * 1000L;
        this.rateLimitMap = new ConcurrentHashMap<String, RateLimitEntry>();
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r, "RateLimitingManager-Cleanup");
                t.setDaemon(true);
                return t;
            }
        });


        // Schedule periodic cleanup of expired entries
        this.cleanupExecutor.scheduleAtFixedRate(
            new Runnable() {
                public void run() {
                    cleanupExpiredEntries();
                }
            },
            cleanupIntervalSeconds,
            cleanupIntervalSeconds,
            TimeUnit.SECONDS
        );
        
        log.info("RateLimitingManager initialized with maxRequests=" + maxRequests + 
                ", timeWindowSeconds=" + timeWindowSeconds + 
                ", cleanupIntervalSeconds=" + cleanupIntervalSeconds);
    }
    
    /**
     * Check if a request is allowed for the given identifier
     * 
     * @param identifier The identifier to check rate limiting for
     * @return true if request is allowed, false if rate limit exceeded
     */
    public boolean isRequestAllowed(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            if (log.isDebugEnabled()) {
                log.debug("RateLimitingManager: Empty identifier, allowing request");
            }
            return true;
        }
        
        long currentTime = System.currentTimeMillis();
        RateLimitEntry entry = rateLimitMap.get(identifier);
        if (entry == null) {
            entry = new RateLimitEntry();
            RateLimitEntry existing = rateLimitMap.putIfAbsent(identifier, entry);
            if (existing != null) {
                entry = existing;
            }
        }

        return entry.isRequestAllowed(currentTime, timeWindowMillis, maxRequests);
    }
    
    /**
     * Get current request count for an identifier within the time window
     * 
     * @param identifier The identifier to check
     * @return current request count
     */
    public int getCurrentRequestCount(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return 0;
        }
        
        RateLimitEntry entry = rateLimitMap.get(identifier);
        if (entry == null) {
            return 0;
        }
        
        long currentTime = System.currentTimeMillis();
        return entry.getCurrentCount(currentTime, timeWindowMillis);
    }
    
    /**
     * Clean up expired entries from the rate limit map
     */
    private void cleanupExpiredEntries() {
        try {
            long currentTime = System.currentTimeMillis();
            int removedCount = 0;

            java.util.Iterator<java.util.Map.Entry<String, RateLimitEntry>> iterator = rateLimitMap.entrySet().iterator();
            while (iterator.hasNext()) {
                java.util.Map.Entry<String, RateLimitEntry> entry = iterator.next();
                if (entry.getValue().isExpired(currentTime, timeWindowMillis)) {
                    iterator.remove();
                    removedCount++;
                }
            }

            if (log.isDebugEnabled() && removedCount > 0) {
                log.debug("RateLimitingManager: Cleaned up " + removedCount + " expired entries");
            }
        } catch (Exception e) {
            log.error("Error during rate limiting cleanup", e);
        }
    }
    
    /**
     * Get the current size of the rate limit map
     * 
     * @return number of active rate limit entries
     */
    public int getActiveEntriesCount() {
        return rateLimitMap.size();
    }
    
    /**
     * Shutdown the rate limiting manager
     */
    public void shutdown() {
        cleanupExecutor.shutdown();
        try {
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            cleanupExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        rateLimitMap.clear();
        log.info("RateLimitingManager shutdown completed");
    }
    
    /**
     * Inner class to track rate limiting for a specific identifier
     */
    private static class RateLimitEntry {
        private final AtomicLong windowStart = new AtomicLong(0);
        private final AtomicInteger requestCount = new AtomicInteger(0);
        private final AtomicLong lastAccessTime = new AtomicLong(System.currentTimeMillis());
        
        /**
         * Check if a request is allowed and increment counter if so
         * 
         * @param currentTime Current timestamp
         * @param timeWindowMillis Time window in milliseconds
         * @param maxRequests Maximum requests allowed
         * @return true if request is allowed
         */
        synchronized boolean isRequestAllowed(long currentTime, long timeWindowMillis, int maxRequests) {
            lastAccessTime.set(currentTime);
            
            long currentWindowStart = windowStart.get();
            
            // Check if we need to start a new window
            if (currentTime - currentWindowStart >= timeWindowMillis) {
                windowStart.set(currentTime);
                requestCount.set(1);
                return true;
            }
            
            // Check if we're within the rate limit
            int currentCount = requestCount.get();
            if (currentCount < maxRequests) {
                requestCount.incrementAndGet();
                return true;
            }
            
            return false;
        }
        
        /**
         * Get current request count within the time window
         * 
         * @param currentTime Current timestamp
         * @param timeWindowMillis Time window in milliseconds
         * @return current request count
         */
        synchronized int getCurrentCount(long currentTime, long timeWindowMillis) {
            long currentWindowStart = windowStart.get();
            
            // If we're outside the current window, count is 0
            if (currentTime - currentWindowStart >= timeWindowMillis) {
                return 0;
            }
            
            return requestCount.get();
        }
        
        /**
         * Check if this entry is expired and can be cleaned up
         * 
         * @param currentTime Current timestamp
         * @param timeWindowMillis Time window in milliseconds
         * @return true if entry is expired
         */
        boolean isExpired(long currentTime, long timeWindowMillis) {
            // Consider entry expired if it hasn't been accessed for 2x the time window
            return currentTime - lastAccessTime.get() > (timeWindowMillis * 2);
        }
    }
}
