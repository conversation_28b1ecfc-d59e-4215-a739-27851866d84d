<?xml version="1.0" encoding="utf-8"?>
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:tocPath="" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Glossary" MadCap:TargetType="WebHelp" lang="en-us" xml:lang="en-us" MadCap:PathToHelpSystem="../" MadCap:HelpSystemFileName="Help.xml">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <link href="SkinSupport/Slideshow.css" rel="stylesheet" type="text/css" MadCap:generated="True" />
        <link href="SkinSupport/MadCap.css" rel="stylesheet" type="text/css" MadCap:generated="True" />
        <title>Glossary</title>
        <link href="Resources/Stylesheets/help.css" rel="stylesheet" type="text/css" />
        <script src="SkinSupport/jquery.min.js" type="text/javascript">
        </script>
        <script src="SkinSupport/MadCapAll.js" type="text/javascript">
        </script>
        <script src="SkinSupport/MadCapTopicAll.js" type="text/javascript">
        </script>
    </head>
    <body style="background-color: #fafafa;">
        <div id="GlossaryBody">
            <div class="GlossaryPageEntry">
                <div class="GlossaryPageTerm">
                    <a href="javascript:void(0);" class="GlossaryPageTerm" id="MCDropDownHotSpot_580200857_0" onclick="FMCDropDown( this ); FMCScrollToVisible( window, this.parentNode.parentNode ); return false;">My Term</a>
                    <a name="580200857_anchor1">
                    </a>
                </div>
                <div class="GlossaryPageDefinition" id="MCDropDownBody_580200857_0" style="display: none;">My definition</div>
            </div>
        </div>
        <p>&#160;</p>
        <script type="text/javascript" src="SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>