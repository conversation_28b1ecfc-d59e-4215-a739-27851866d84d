<?xml version="1.0" encoding="utf-8"?>
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:tocPath="Execute Spots (Non-Fixing)|Order Types for Spot Execution" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="WebHelp" lang="en-us" xml:lang="en-us" MadCap:conditions="IDC.Type_NonFixing" class="" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="Help.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" /><title>TWAP Orders</title>
        <link href="../../SkinSupport/Slideshow.css" rel="stylesheet" />
        <link href="../../SkinSupport/MadCap.css" rel="stylesheet" />
        <link href="../../Resources/TableStyles/ColumnTable.css" rel="stylesheet" />
        <link href="../../Resources/TableStyles/PWTitleHead.css" rel="stylesheet" />
        <link href="../../Resources/Stylesheets/help.css" rel="stylesheet" />
        <script src="../../SkinSupport/jquery.min.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapAll.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapTopicAll.js" type="text/javascript">
        </script>
    </head>
    <body>
        <h2 class="Heading"><a name="_top"></a><a name="kanchor11"></a>TWAP Orders</h2>
        <p class="BreadcrumbLink"><span class="BreadcrumbLink1" MadCap:autonum="1. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">1. </span></span><a href="../1_Import/1ImportIntro.html#_top" class="MCXref_Title_0">Import Trade List</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="2. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">2. </span></span><a href="../2_Plan/Plan.html#_top" class="MCXref_Title_0">Advanced Netting</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="3. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">3. </span></span><a href="../3_NetForwards/NetForwards.html#_top" class="MCXref_Title_0">Netted Forwards</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="4. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">4. </span></span>Execute Spots</span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="5. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">5. </span></span><a href="../5_ExecuteFixedSpotRoll/ExecuteFixedSpotRoll.html#_top" class="MCXref_Title_0">Execute Fixed Spot Roll</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="6. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">6. </span></span><a href="../6_TrueUpBaseTerm/TrueUpBaseTerm.html#_top" class="MCXref_Title_0">True-Up Base/Term</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="7. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">7. </span></span><a href="../7_TrueUpXCcy/TrueUpXCcy.html#_top" class="MCXref_Title_0">True-Up Cross Currency</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="8. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">8. </span></span><a href="../8_Export/Export.html#_top" class="MCXref_Title_0">Allocate</a></span>
        </p>
        <p class="Body1">A Time-Weighted Average Price (TWAP) order is an order-slicing algo order that divides up an order into clips and submits each clip for order matching at intervals over a period of time.</p>
        <p class="Body1">You configure the size of each clip and the interval between clips. Both the size and the interval can be either fixed or random between a minimum and maximum value.</p>
        <p class="Body1">Each clip is either matched with top-of-book prices as a market order or with the current top-of-book prices within a range of pips that you specify. In addition, you can specify a &#8220;no worse than&#8221; limit for the entire order so that no clip is matched with a price worse than the limit.</p>
        <p class="Body1">You can further specify the action when your TWAP order expires, either cancelling any remaining balance or filling the balance as a market order that sweeps the market for 30 seconds.</p>
        <p class="Body1">You specify a total execution time for your TWAP order when you submit it. The maximum execution time is one hour.</p>
        <p class="Figure">
            <img src="../../img/OrderDetails_TWAP.png" />
        </p>
        <p class="FigureTitle" MadCap:autonum="Figure 1 "><span class="autonumber"><span class="Figures">Figure 1 </span></span>Order Details Panel: TWAP Order</p>
        <table class="TableStyle-PWTitleHead" style="margin-left: 0;margin-right: auto;mc-table-style: url('../../Resources/TableStyles/PWTitleHead.css');" cellspacing="0">
            <caption class="TableTitle" MadCap:autonum="Table 1 "><span class="autonumber"><span class="Figures">Table 1 </span></span>Orders Details Panel: TWAP Order Components</caption>
            <col class="TableStyle-PWTitleHead-Column-Column1" />
            <col class="TableStyle-PWTitleHead-Column-Column1" />
            <thead>
                <tr class="TableStyle-PWTitleHead-Head-Header1">
                    <th class="TableStyle-PWTitleHead-HeadE-Column1-Header1">
                        <p class="CellHeading">Item</p>
                    </th>
                    <th class="TableStyle-PWTitleHead-HeadD-Column1-Header1">
                        <p class="CellHeading">Description</p>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">1</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The currency pair of the trade.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">2</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Order type drop-down list. The order entry tools change depending on the order type you choose.</p>
                        <table class="TableStyle-ColumnTable" style="margin-left: 0;margin-right: auto;mc-table-style: url('../../Resources/TableStyles/ColumnTable.css');" cellspacing="0">
                            <col class="TableStyle-ColumnTable-Column-Column1" MadCap:conditions="IDC.Type_NonFixing" />
                            <col class="TableStyle-ColumnTable-Column-Column1" MadCap:conditions="IDC.Type_Fixing" />
                            <tbody>
                                <tr class="TableStyle-ColumnTable-Body-Body1">
                                    <td class="TableStyle-ColumnTable-BodyB-Column1-Body1">
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">LMT: <a href="Order_Limit.html#_top" class="MCXref_SectionAndPage_0">Limit Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">MKT: <a href="Order_Market.html#_top" class="MCXref_SectionAndPage_0">Market Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">MKTRng: <a href="Order_MarketRange.html#_top" class="MCXref_SectionAndPage_0">Market Range Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">TWAP: <a href="#_top" class="MCXref_SectionAndPage_0 selected">TWAP Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">TWAP Plus</td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">RiskNet</td>
                                            </tr>
                                        </table>
                                    </td>
                                    <td class="TableStyle-ColumnTable-BodyA-Column1-Body1">
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">C2C+WM Mkt: <a href="Order_FixMarket.html#_top" class="MCXref_SectionAndPage_0">C2C+WM Mkt Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">C2C+WM TWAP: <a href="Order_FixTWAP.html#_top" class="MCXref_SectionAndPage_0">C2C+WM TWAP Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">BAML WL</td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <p class="CellInfoNote" MadCap:autonum="NOTE: "><span class="autonumber"><span class="InfoSymbol">NOTE: </span></span>The types of orders that you can submit depend on your user permissions.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">3</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Top-of-book ESP bid price for the selected currency pair. Automatically updates from streaming market prices.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">4</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Top-of-book ESP offer price for the selected currency pair. Automatically updates from streaming market prices.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">5</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The value date of the top-of-book prices</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">6</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Trade direction</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText"><a name="TWAP_size"></a>7</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Size field: The size of your order</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">8</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Dealt currency</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText"><a name="TWAP_totalexecutiontime"></a>9</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText"><span class="Bold">Total Execution Time</span> field: The total time that your TWAP order is working. For example, &#8220;00:01:00&#8221; specifies one minute of clip slicing and price matching. This time elapses even when the order is suspended. When the time has elapsed, the order is expired and the action at expiry is triggered (see <a href="#TWAP_actionatexpiry" class="MCXref_ItemNumber_0 selected">item 13</a>). The maximum execution time is one hour.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText"><a name="TWAP_noworsethan"></a>10</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText"><span class="Bold">No Worse Than</span> checkbox: Select to specify an absolute price limit for your TWAP order. No individual clip can be matched with a price worse than this price. In addition, this limit also applies to the action at expiry if you choose <span class="Bold">Fill Balance at Market</span> (see <a href="#TWAP_actionatexpiry" class="MCXref_ItemNumber_0 selected">item 13</a>). If cleared, your TWAP order has no limit price and individual clips can be matched at any top-of-book price.</p>
                        <p class="CellText"><span class="Bold">No Worse Than</span> field: The price limit enforced for all clips and at expiry if you choose <span class="Bold">Fill Balance at Market</span> (see <a href="#TWAP_actionatexpiry" class="MCXref_ItemNumber_0 selected">item 13</a>). Enter the rate or use the arrows to adjust the rate. The price that appears is taken from the top-of-book prices.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">11</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Clip size and rate tools: Configure how clip sizes and rates are determined.</p>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top"><span class="Bold">Clip Size</span> field: The base size of each clip. The default value in this field is calculated from the total size of the order (see <a href="#TWAP_size" class="MCXref_ItemNumber_0 selected">item 7</a>). The largest clip size you enter must be less than the total size of the order. The minimum clip size is 100K.</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top"><span class="Bold">Randomize Size (+/-)</span> checkbox and field: Select the checkbox to randomize the clip size by the percentage value that you enter in the field. Clear to keep the clip size fixed. The clip size range is displayed next to the field.</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top"><span class="Bold">Range</span> checkbox and field: Select the checkbox to specify slippage for each clip.  If you enter a value of zero, each clip is matched as a limit order. Clear to match each clip at all top-of-market prices until filled or expired.</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">12</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Clip interval tools: Set the clip interval. </p>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top"><span class="Bold">Clip Interval</span> field: The base interval between each clip. The default value in this field is calculated from the total execution time of the order (see <a href="#TWAP_totalexecutiontime" class="MCXref_ItemNumber_0 selected">item 9</a>).</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top"><span class="Bold">Randomize Interval (+/-)</span> checkbox and field: Select the checkbox to randomize the clip interval by the percentage value that you enter in the field. Clear to keep the clip interval fixed. The clip interval range is displayed next to the field.</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText"><a name="TWAP_actionatexpiry"></a>13</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText"><span class="Bold">Action At Expiry</span> radio buttons: Choose whether any unfilled amount at expiry is cancelled or filled at market:</p>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top"><span class="Bold">Cancel Balance</span> radio button: Cancel any unfilled amount when the order expires. This is the default setting.</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top"><span class="Bold">Fill Balance at Market</span> radio button: Fill the remaining balance with top-of-book prices as a market order when the order expires. The <span class="Bold">No Worse Than</span> price is honored if you have set one (see <a href="#TWAP_noworsethan" class="MCXref_ItemNumber_0 selected">item 10</a>).</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">14</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p><span class="Bold">Notes</span> checkbox: Select the checkbox to show a field for entering notes on the order. These notes are only shown to you (the submitting user) and your organization. Your notes are saved in the field as you enter them and stay when you close the panel, submit and order, or log out. Order notes can contain a maximum of 256 characters. Angle bracket characters &#8220;&lt;&#8221; and &#8220;&gt;&#8221; are not allowed.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">15</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText"><span class="Bold">Order Summary</span>: A textual description so that you can quickly confirm the behavior of your order. Updated dynamically as you change the order&#8217;s parameters.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyB-Column1-Body1">
                        <p class="CellText">16</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyA-Column1-Body1">
                        <p class="CellText"><span class="Bold">Save Order Details</span> button: Click to save the order details to use when you execute spots.</p>
                    </td>
                </tr>
            </tbody>
        </table>
        <div MadCap:conditions="IDC.online">
            <p class="RelatedTopicsHeader">Related Topics</p>
            <p class="RelatedTopics"><a href="OrderTypes.html#_top" class="MCXref_SectionAndPage_0">Order Types for Spot Execution</a>
            </p>
        </div>
        <div class="Copyright">
            <hr class="Copyright" />
            <table class="Copyright">
                <tr>
                    <td class="CopyrightText">
                        <p class="CellCopyright">&#169; <span class="IDCCopyrightDate">2018</span> Integral Development Corp. All rights reserved. Integral technology is protected under U.S. Patent Nos. 6,347,307; 7,882,011; 8,417,622; 8,862,507; 9,412,134; 9,836,789 and patent pending applications and related intellectual property. This product and related documentation are protected by copyright and distributed under licenses restricting, without limitation, its use, reproduction, copying, distribution, and decompilation.</p>
                    </td>
                    <td class="CopyrightVersion">
                        <p class="CellCopyright"><span class="IDCClientVersion">6.7.7</span>v<span class="IDCDocVersionMinor">2</span></p>
                    </td>
                </tr>
            </table>
        </div>
        <script type="text/javascript" src="../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>