<?xml version="1.0" encoding="utf-8"?>
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:tocPath="" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="WebHelp" lang="en-us" xml:lang="en-us" class="" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="Help.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" /><title>Netted Forwards</title>
        <link href="../../SkinSupport/Slideshow.css" rel="stylesheet" />
        <link href="../../SkinSupport/MadCap.css" rel="stylesheet" />
        <link href="../../Resources/TableStyles/CWTitleHead.css" rel="stylesheet" />
        <link href="../../Resources/Stylesheets/help.css" rel="stylesheet" />
        <script src="../../SkinSupport/jquery.min.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapAll.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapTopicAll.js" type="text/javascript">
        </script>
    </head>
    <body>
        <h1 class="Heading" MadCap:conditions="IDC.online"><a name="_top"></a><a name="kanchor5"></a>Netted Forwards</h1>
        <p class="BreadcrumbLink"><span class="BreadcrumbLink1" MadCap:autonum="1. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">1. </span></span><a href="../1_Import/1ImportIntro.html#_top" class="MCXref_Title_0">Import Trade List</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="2. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">2. </span></span><a href="../2_Plan/Plan.html#_top" class="MCXref_Title_0">Advanced Netting</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="3. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">3. </span></span>Netted Forwards</span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="4. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">4. </span></span><a href="../4_ExecuteSpots/4ExecuteSpotsIntro.html#_top" class="MCXref_Title_0">Execute Spots</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="5. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">5. </span></span><a href="../5_ExecuteFixedSpotRoll/ExecuteFixedSpotRoll.html#_top" class="MCXref_Title_0">Execute Fixed Spot Roll</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="6. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">6. </span></span><a href="../6_TrueUpBaseTerm/TrueUpBaseTerm.html#_top" class="MCXref_Title_0">True-Up Base/Term</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="7. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">7. </span></span><a href="../7_TrueUpXCcy/TrueUpXCcy.html#_top" class="MCXref_Title_0">True-Up Cross Currency</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="8. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">8. </span></span><a href="../8_Export/Export.html#_top" class="MCXref_Title_0">Allocate</a></span>
        </p>
        <p class="Body1">The Netted Forwards screen shows you the netted forward amounts in your portfolio, if any, grouped by currency pair.</p>
        <p class="Body1">You can configure <span class="IDCClientProduct">InvestorFX</span> to skip this step. See <a href="../../Settings/SettingsSkipping.html#_top" class="MCXref_SectionAndPage_0">Choosing Workflow Steps To Skip</a>.</p>
        <p class="ProcedureHead">Net Forwards:</p>
        <p class="Numbered1" MadCap:autonum="1."><span class="autonumber"><span class="NumberSymbol">1.</span></span>Review the net forward amounts.</p>
        <p class="Numbered" MadCap:autonum="2."><span class="autonumber"><span class="NumberSymbol">2.</span></span>Click the <img src="../../img/IconArrowBlueNext.png" class="InLineImage" /> Next button.</p>
        <p class="Anchor">&#160;</p>
        <table style="margin-left: 0;margin-right: auto;caption-side: top;mc-table-style: url('../../Resources/TableStyles/CWTitleHead.css');" class="TableStyle-CWTitleHead" cellspacing="0">
            <caption class="TableTitle" MadCap:autonum="Table 1 "><span class="autonumber"><span class="Figures">Table 1 </span></span>Net Forwards Columns</caption>
            <col class="TableStyle-CWTitleHead-Column-Column1" />
            <col class="TableStyle-CWTitleHead-Column-Column1" />
            <thead>
                <tr class="TableStyle-CWTitleHead-Head-Header1">
                    <th class="TableStyle-CWTitleHead-HeadE-Column1-Header1">
                        <p class="CellHeading">Column</p>
                    </th>
                    <th class="TableStyle-CWTitleHead-HeadD-Column1-Header1">
                        <p class="CellHeading">Description</p>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">CCY Pair</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The currency pair of the trade</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Value Date</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The value date of the trade</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Dealt B/S</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Whether the dealt currency is bought or sold from the perspective of the trade&#8217;s account</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Dealt CCY</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The dealt currency of the trade</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyB-Column1-Body1">
                        <p class="CellText">Dealt Amt</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyA-Column1-Body1">
                        <p class="CellText">The amount of the dealt currency</p>
                    </td>
                </tr>
            </tbody>
        </table>
        <div MadCap:conditions="IDC.online">
            <p class="RelatedTopicsHeader">Related Topics</p>
            <p class="RelatedTopics"><a href="../5_ExecuteFixedSpotRoll/ExecuteFixedSpotRoll.html#_top" class="MCXref_SectionAndPage_0">Execute Fixed Spot Roll</a>
            </p>
        </div>
        <div class="Copyright">
            <hr class="Copyright" />
            <table class="Copyright">
                <tr>
                    <td class="CopyrightText">
                        <p class="CellCopyright">&#169; <span class="IDCCopyrightDate">2018</span> Integral Development Corp. All rights reserved. Integral technology is protected under U.S. Patent Nos. 6,347,307; 7,882,011; 8,417,622; 8,862,507; 9,412,134; 9,836,789 and patent pending applications and related intellectual property. This product and related documentation are protected by copyright and distributed under licenses restricting, without limitation, its use, reproduction, copying, distribution, and decompilation.</p>
                    </td>
                    <td class="CopyrightVersion">
                        <p class="CellCopyright"><span class="IDCClientVersion">6.7.7</span>v<span class="IDCDocVersionMinor">2</span></p>
                    </td>
                </tr>
            </table>
        </div>
        <script type="text/javascript" src="../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>