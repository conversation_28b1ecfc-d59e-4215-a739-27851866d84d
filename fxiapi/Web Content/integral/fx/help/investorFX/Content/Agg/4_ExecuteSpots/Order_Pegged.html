<?xml version="1.0" encoding="utf-8"?>
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:tocPath="" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="WebHelp" lang="en-us" xml:lang="en-us" MadCap:conditions="IDC.omit" class="" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="Help.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" /><title>Pegged Orders</title>
        <link href="../../SkinSupport/Slideshow.css" rel="stylesheet" />
        <link href="../../SkinSupport/MadCap.css" rel="stylesheet" />
        <link href="../../Resources/TableStyles/ColumnTable.css" rel="stylesheet" />
        <link href="../../Resources/TableStyles/PWTitleHead.css" rel="stylesheet" />
        <link href="../../Resources/Stylesheets/help.css" rel="stylesheet" />
        <script src="../../SkinSupport/jquery.min.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapAll.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapTopicAll.js" type="text/javascript">
        </script>
    </head>
    <body>
        <h1 class="Heading"><a name="_top"></a><a name="kanchor26"></a>Pegged Orders</h1>
        <p class="BreadcrumbLink"><span class="BreadcrumbLink1" MadCap:autonum="1. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">1. </span></span><a href="../1_Import/1ImportIntro.html#_top" class="MCXref_Title_0">Import Trade List</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="2. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">2. </span></span><a href="../2_Plan/Plan.html#_top" class="MCXref_Title_0">Advanced Netting</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="3. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">3. </span></span><a href="../3_NetForwards/NetForwards.html#_top" class="MCXref_Title_0">Netted Forwards</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="4. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">4. </span></span>Execute Spots</span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="5. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">5. </span></span><a href="../5_ExecuteFixedSpotRoll/ExecuteFixedSpotRoll.html#_top" class="MCXref_Title_0">Execute Fixed Spot Roll</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="6. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">6. </span></span><a href="../6_TrueUpBaseTerm/TrueUpBaseTerm.html#_top" class="MCXref_Title_0">True-Up Base/Term</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="7. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">7. </span></span><a href="../7_TrueUpXCcy/TrueUpXCcy.html#_top" class="MCXref_Title_0">True-Up Cross Currency</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="8. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">8. </span></span><a href="../8_Export/Export.html#_top" class="MCXref_Title_0">Allocate</a></span>
        </p>
        <p class="Body1">A pegged order is an algo order that allows you to seek a price near the best bid, offer, or mid price in the market. The order rate automatically adjusts as the market moves. When a price in the market that the order is pegged to moves through the offset amount, then your order begins matching with prices.</p>
        <h2 class="Heading">Peg Rate</h2>
        <p class="Body1">You can make your order more or less aggressive according to the price that you peg to and by setting an offset amount.</p>
        <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span>Peg the price to the best bid if buying or the best offer if selling (known as a &#8220;primary peg&#8221;). The motivation is to join the market and be passive. The peg offset can be used to improve the price to the market or to worsen the price so that the order may be lifted as part of a market sweep.</p>
        <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span>Peg the price to the midpoint between the bid and offer. The system will constantly monitor the best bid and offer and set the price to the midpoint.</p>
        <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span>Peg the price to the best offer if buying or the best bid if selling (known as a &#8220;market peg&#8221;). A market peg with peg offset of zero is same as aggressing on the market. However, a market peg with a positive offset will set the price just inside the market and essentially compress the market to the specified offset. This technique is be used to take an aggressive position without actually crossing a provided bid or offer.</p>
        <h2 class="Heading">Peg Offset</h2>
        <p class="Body1">The offset amount can be a positive or negative value.</p>
        <p class="Figure">
            <img src="../../img/OrderDetails_PEG.png" />
        </p>
        <p class="FigureTitle" MadCap:autonum="Figure 1 "><span class="autonumber"><span class="Figures">Figure 1 </span></span>Order Details Panel: Pegged Order</p>
        <table class="TableStyle-PWTitleHead" style="margin-left: 0;margin-right: auto;mc-table-style: url('../../Resources/TableStyles/PWTitleHead.css');" cellspacing="0">
            <caption class="TableTitle" MadCap:autonum="Table 1 "><span class="autonumber"><span class="Figures">Table 1 </span></span>Orders Details Panel: Pegged Order Components</caption>
            <col class="TableStyle-PWTitleHead-Column-Column1" />
            <col class="TableStyle-PWTitleHead-Column-Column1" />
            <thead>
                <tr class="TableStyle-PWTitleHead-Head-Header1">
                    <th class="TableStyle-PWTitleHead-HeadE-Column1-Header1">
                        <p class="CellHeading">Item</p>
                    </th>
                    <th class="TableStyle-PWTitleHead-HeadD-Column1-Header1">
                        <p class="CellHeading">Description</p>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">1</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The currency pair of the trade.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">2</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Order type drop-down list. The order entry tools change depending on the order type you choose.</p>
                        <table class="TableStyle-ColumnTable" style="margin-left: 0;margin-right: auto;mc-table-style: url('../../Resources/TableStyles/ColumnTable.css');" cellspacing="0">
                            <col class="TableStyle-ColumnTable-Column-Column1" MadCap:conditions="IDC.Type_NonFixing" />
                            <col class="TableStyle-ColumnTable-Column-Column1" MadCap:conditions="IDC.Type_Fixing" />
                            <tbody>
                                <tr class="TableStyle-ColumnTable-Body-Body1">
                                    <td class="TableStyle-ColumnTable-BodyB-Column1-Body1">
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">LMT: <a href="Order_Limit.html#_top" class="MCXref_SectionAndPage_0">Limit Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">MKT: <a href="Order_Market.html#_top" class="MCXref_SectionAndPage_0">Market Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">MKTRng: <a href="Order_MarketRange.html#_top" class="MCXref_SectionAndPage_0">Market Range Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">TWAP: <a href="Order_TWAP.html#_top" class="MCXref_SectionAndPage_0">TWAP Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">TWAP Plus</td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">RiskNet</td>
                                            </tr>
                                        </table>
                                    </td>
                                    <td class="TableStyle-ColumnTable-BodyA-Column1-Body1">
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">C2C+WM Mkt: <a href="Order_FixMarket.html#_top" class="MCXref_SectionAndPage_0">C2C+WM Mkt Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">C2C+WM TWAP: <a href="Order_FixTWAP.html#_top" class="MCXref_SectionAndPage_0">C2C+WM TWAP Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">BAML WL</td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <p class="CellInfoNote" MadCap:autonum="NOTE: "><span class="autonumber"><span class="InfoSymbol">NOTE: </span></span>The types of orders that you can submit depend on your user permissions.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">3</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Top-of-book ESP bid price for the selected currency pair. Automatically updates from streaming market prices.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">4</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Top-of-book ESP offer price for the selected currency pair. Automatically updates from streaming market prices.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">5</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The value date of the top-of-book prices</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">6</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Trade direction</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">7</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Size field: The size of your order</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">8</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Dealt currency</p>
                    </td>
                </tr>
                <tr style="height: 5px;" class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">9</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText"><span class="Bold">Time in Force</span> field: The period of time during which your order is active:</p>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">Enter a value of zero for a GTC (Good till Cancelled) order. Your order is matched with prices until filled.</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">Enter a time for a GTT (Good Till Time) order. Your order is matched with prices until it expires or is filled. Any remaining unfilled balance can be submitted again for execution.</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">10</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText"><span class="Bold">No Worse Than</span> checkbox: Select to specify an absolute price limit. Your order is not matched with a price worse than this price. If cleared, your order has no limit price and can be matched at any top-of-book price.</p>
                        <p class="CellText"><span class="Bold">No Worse Than</span> field: The price limit. Enter the rate or use the arrows to adjust the rate. The price that appears is taken from the top-of-book prices.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">11</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText"><span class="Bold">Show Amount</span> field: Enter an amount that you want users to see. If you leave this field blank, the entire size of your order is revealed. For hidden orders, enter 0 (zero).</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">12</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText"><span class="Bold">Peg to</span> drop-down list: The price to which your order is pegged:</p>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">Bid: Peg the price to the best bid</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">Mid: Peg the price to the midpoint between the bid and offer</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">Offer: Peg the price to the best offer</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">13</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText"><span class="Bold">Offset</span> field: The number of pips that the trigger rate is offset from the best price. Enter a positive value to make your order more attractive in the market (more aggressive). Enter a negative value to make your order less attractive in the market (less aggressive).</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">14</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText"><span class="Bold">Action At Expiry</span> radio buttons: Choose whether any unfilled amount at expiry is cancelled or filled at market:</p>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top"><span class="Bold">Cancel Balance</span> radio button: Cancel any unfilled amount when the order expires. This is the default setting.</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top"><span class="Bold">Fill Balance at Market</span> radio button: Fill the remaining balance with top-of-book prices as a market order when the order expires.</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">15</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Order Summary: A textual description so that you can quickly confirm the behavior of your order. Updated dynamically as you change the order&#8217;s parameters.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyB-Column1-Body1">
                        <p class="CellText">16</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyA-Column1-Body1">
                        <p class="CellText"><span class="Bold">Save Order Details</span> button: Click to save the order details when you execute spots.</p>
                    </td>
                </tr>
            </tbody>
        </table>
        <div MadCap:conditions="IDC.online">
            <p class="RelatedTopicsHeader">Related Topics</p>
            <p class="RelatedTopics"><a href="OrderTypes.html#_top" class="MCXref_SectionAndPage_0">Order Types for Spot Execution</a>
            </p>
        </div>
        <div class="Copyright">
            <hr class="Copyright" />
            <table class="Copyright">
                <tr>
                    <td class="CopyrightText">
                        <p class="CellCopyright">&#169; <span class="IDCCopyrightDate">2018</span> Integral Development Corp. All rights reserved. Integral technology is protected under U.S. Patent Nos. 6,347,307; 7,882,011; 8,417,622; 8,862,507; 9,412,134; 9,836,789 and patent pending applications and related intellectual property. This product and related documentation are protected by copyright and distributed under licenses restricting, without limitation, its use, reproduction, copying, distribution, and decompilation.</p>
                    </td>
                    <td class="CopyrightVersion">
                        <p class="CellCopyright"><span class="IDCClientVersion">6.7.7</span>v<span class="IDCDocVersionMinor">2</span></p>
                    </td>
                </tr>
            </table>
        </div>
        <script type="text/javascript" src="../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>