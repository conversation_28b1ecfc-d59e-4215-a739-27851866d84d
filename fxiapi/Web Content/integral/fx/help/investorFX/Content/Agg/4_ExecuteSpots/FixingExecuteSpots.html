<?xml version="1.0" encoding="utf-8"?>
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:tocPath="" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="WebHelp" lang="en-us" xml:lang="en-us" MadCap:conditions="IDC.Type_Fixing" class="" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="Help.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" /><title>Execute Spots (Fixing Workflow)</title>
        <link href="../../SkinSupport/Slideshow.css" rel="stylesheet" />
        <link href="../../SkinSupport/MadCap.css" rel="stylesheet" />
        <link href="../../Resources/TableStyles/CWTitleHead.css" rel="stylesheet" />
        <link href="../../Resources/Stylesheets/help.css" rel="stylesheet" />
        <script src="../../SkinSupport/jquery.min.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapAll.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapTopicAll.js" type="text/javascript">
        </script>
    </head>
    <body>
        <h1 class="Heading"><a name="_top"></a><a name="kanchor25"></a>Execute Spots<MadCap:conditionalText MadCap:conditions="IDC.Type_Fixing,IDC.Type_NonFixing"> (Fixing Workflow)</MadCap:conditionalText></h1>
        <p class="BreadcrumbLink"><span class="BreadcrumbLink1" MadCap:autonum="1. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">1. </span></span><a href="../1_Import/1ImportIntro.html#_top" class="MCXref_Title_0">Import Trade List</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="2. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">2. </span></span><a href="../2_Plan/Plan.html#_top" class="MCXref_Title_0">Advanced Netting</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="3. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">3. </span></span><a href="../3_NetForwards/NetForwards.html#_top" class="MCXref_Title_0">Netted Forwards</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="4. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">4. </span></span>Execute Spots</span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="5. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">5. </span></span><a href="../5_ExecuteFixedSpotRoll/ExecuteFixedSpotRoll.html#_top" class="MCXref_Title_0">Execute Fixed Spot Roll</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="6. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">6. </span></span><a href="../6_TrueUpBaseTerm/TrueUpBaseTerm.html#_top" class="MCXref_Title_0">True-Up Base/Term</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="7. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">7. </span></span><a href="../7_TrueUpXCcy/TrueUpXCcy.html#_top" class="MCXref_Title_0">True-Up Cross Currency</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="8. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">8. </span></span><a href="../8_Export/Export.html#_top" class="MCXref_Title_0">Allocate</a></span>
        </p>
        <p class="Body1">The Execute Spots screen shows the list of spot orders that <span class="IDCClientProduct">InvestorFX</span> has readied to net your portfolio.</p>
        <p class="Numbered1" MadCap:autonum="1."><span class="autonumber"><span class="NumberSymbol">1.</span></span>Edit the orders as necessary.</p>
        <p class="Bullet2" MadCap:autonum="&#9702; "><span class="autonumber"><span class="BulletSymbol">&#9702; </span></span>Choose an order type from the drop-down lists in the Order Type column (<img src="../../img/DropDownList_OrderType_Fixing.png" class="InLineImage" MadCap:conditions="IDC.Type_Fixing" />).</p>
        <p class="Bullet2" MadCap:autonum="&#9702; "><span class="autonumber"><span class="BulletSymbol">&#9702; </span></span>Edit your orders using the field in the Limit Rate / Range column or click the <span class="Bold">Details</span> button to open the Order Details Panel.</p>
        <p class="Numbered" MadCap:autonum="2."><span class="autonumber"><span class="NumberSymbol">2.</span></span>(<span class="Emphasis">Optional</span>) Click <span class="Bold">Check RiskNet</span>.</p>
        <p class="Numbered" MadCap:autonum="3."><span class="autonumber"><span class="NumberSymbol">3.</span></span>(<span class="Emphasis">Optional</span>) Use the checkboxes to select the orders that you want to execute now. All checkboxes are checked by default. Use the checkbox in the column heading to check and uncheck all orders.</p>
        <p class="InfoNote" MadCap:autonum="NOTE: "><span class="autonumber"><span class="InfoSymbol">NOTE: </span></span>All spot orders must be executed before you can continue processing your portfolio.</p>
        <p class="Numbered" MadCap:autonum="4."><span class="autonumber"><span class="NumberSymbol">4.</span></span>When you have finished configuring the orders, click the <span class="Bold">Execute</span> button at the top of the screen to submit selected orders for execution.</p>
        <p class="Body2"><span class="IDCClientProduct">InvestorFX</span> submits your orders and updates the table with order IDs, order status, and trade information as trades are executed. To see individual trade information, click the linked order ID to open the Order Details window.</p>
        <p class="Numbered" MadCap:autonum="5."><span class="autonumber"><span class="NumberSymbol">5.</span></span>When spot execution is complete, click the <img src="../../img/IconArrowBlueNext.png" class="InLineImage" /> Next button.</p>
        <table style="margin-left: 0;margin-right: auto;caption-side: top;mc-table-style: url('../../Resources/TableStyles/CWTitleHead.css');width: 100%;" class="TableStyle-CWTitleHead" cellspacing="0">
            <caption class="TableTitle" MadCap:autonum="Table 1 "><span class="autonumber"><span class="Figures">Table 1 </span></span>Execute Spots Columns</caption>
            <col class="TableStyle-CWTitleHead-Column-Column1" />
            <col class="TableStyle-CWTitleHead-Column-Column1" />
            <thead>
                <tr class="TableStyle-CWTitleHead-Head-Header1">
                    <th class="TableStyle-CWTitleHead-HeadE-Column1-Header1">
                        <p class="CellHeading">Column</p>
                    </th>
                    <th class="TableStyle-CWTitleHead-HeadD-Column1-Header1">
                        <p class="CellHeading">Description</p>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Checkboxes</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Select the spot trades to execute. Use the checkbox in the column heading to check and uncheck all orders.</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Order ID</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The ID of the order that <span class="IDCClientProduct">InvestorFX</span> places for the spot trade. Empty until you click <span class="Bold">Execute</span> and trades are executed. Click the Expand button (<img src="../../img/IconButtonExpandOrder.png" />) next to the order ID to see the details of the executed trades.</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">CCY Pair</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The currency pair of the trade</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Spot Date</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The spot date of the trade. Empty until you click <span class="Bold">Execute</span> and trades are executed.</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Dealt B/S</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Whether the dealt currency is bought or sold from the perspective of the trade&#8217;s account</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Dealt CCY</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The dealt currency of the trade</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Order Amt</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The amount of the submitted order</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1" MadCap:conditions="IDC.Type_Fixing">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Fixing Matched Amt</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The amount matched for fixing. Zero until you click <span class="Bold">Execute</span> and trades are matched.</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1" MadCap:conditions="IDC.Type_Fixing">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Resid Filled Amt</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The amount not matched for fixing and instead matched with the market. Zero until you click <span class="Bold">Execute</span> and matching for fixing is complete. If your entire amount is matched for fixing, this value remains zero.</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Unfilled Amt</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The order amount left unfilled by the trade, if any. Zero until you click <span class="Bold">Execute</span> and trades are executed.</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Market Bid</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The bid price from the market for reference. Click the <span class="Bold">Refresh</span> button to update unexecuted orders with the latest market rates.</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Market Offer</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The offer price from the market for reference. Click the <span class="Bold">Refresh</span> button to update unexecuted orders with the latest market rates.</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1" MadCap:conditions="IDC.Type_Fixing">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Fixing Rate</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The rate at which the fixing amount was fixed. Zero until after fixing is done.</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1" MadCap:conditions="IDC.Type_Fixing">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Resid Executed Rate</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The rate at which the residual amount, if any, was executed. Zero until after fixing is done and the residual amount is submitted for execution.</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1" MadCap:conditions="IDC.Type_Fixing">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Residual Order Type</p>
                        <p class="CellText">
                            <img src="../../img/DropDownList_OrderType_Fixing.png" class="InLineImage" />
                        </p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Use the drop-down lists to change the order type for individual orders.</p>
                        <p class="CellText">Pre-fix:</p>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">C2C+WM Mkt: Market orders ensure immediate execution but do not guarantee price. Trades are executed against the order at market rates at the top of the book until the order is filled. See <a href="Order_FixMarket.html#_top" class="MCXref_SectionAndPage_0">C2C+WM Mkt Orders</a>.</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">C2C+WM TWAP: A Time-Weighted Average Price (TWAP) order is an order-slicing algo order that divides up an order into clips and submits each clip for order matching at intervals over a period of time. Use the <span class="Bold">Details</span> button to open the Order Details panel and edit your order. See <a href="Order_FixTWAP.html#_top" class="MCXref_SectionAndPage_0">C2C+WM TWAP Orders</a>.</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">BAML&#160;WM</td>
                            </tr>
                        </table>
                        <p class="CellText">Post-fix:</p>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">TWAP: A Time-Weighted Average Price (TWAP) order is an order-slicing algo order that divides up an order into clips and submits each clip for order matching at intervals over a period of time. Use the <span class="Bold">Details</span> button to open the Order Details panel and edit your order.</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">MKT: Market orders ensure immediate execution but do not guarantee price. Trades are executed against the order at market rates at the top of the book until the order is filled.</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">WMTWAP</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">WMMKT</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Limit Rate / Range</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The limit rate for trade execution if you choose the &#8220;LMT&#8221; order type or the range of pips allowed for trade execution if you choose the &#8220;MKTRng&#8221; order type</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Order Details</p>
                        <p class="CellText">
                            <img src="../../img/ButtonOrderDetails.png" class="InLineImage" />
                        </p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Click the <span class="Bold">Details</span> button to open the Order Details panel, review and edit the order details before you submit it.</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyB-Column1-Body1">
                        <p class="CellText">Status</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyA-Column1-Body1">
                        <p class="CellText">The status of the order. Updated after you click <span class="Bold">Execute</span> and as your order is filled. Possible values are:</p>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">Empty: The spot order has not been submitted for execution. You have not yet checked the order&#8217;s checkbox and clicked <span class="Bold">Execute</span>.</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">Submitted: The order has been submitted and <span class="IDCClientProduct">InvestorFX</span> is waiting for trade verification.</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">Partial: Depending on the order&#8217;s time in force, the order may be matched with multiple prices. The order is shown with this status until it is completely filled or until it expires.</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">Cancelled: When the order expires before it is completely filled, the original order is cancelled and the remaining balance is available for resubmission as a new order.</td>
                            </tr>
                        </table>
                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                            <col style="width: -14.4px;" />
                            <col style="width: 14.4px;" />
                            <col style="width: auto;" />
                            <tr>
                                <td valign="top" />
                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                </td>
                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">Filled: The entire order amount is done.</td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="Copyright">
            <hr class="Copyright" />
            <table class="Copyright">
                <tr>
                    <td class="CopyrightText">
                        <p class="CellCopyright">&#169; <span class="IDCCopyrightDate">2018</span> Integral Development Corp. All rights reserved. Integral technology is protected under U.S. Patent Nos. 6,347,307; 7,882,011; 8,417,622; 8,862,507; 9,412,134; 9,836,789 and patent pending applications and related intellectual property. This product and related documentation are protected by copyright and distributed under licenses restricting, without limitation, its use, reproduction, copying, distribution, and decompilation.</p>
                    </td>
                    <td class="CopyrightVersion">
                        <p class="CellCopyright"><span class="IDCClientVersion">6.7.7</span>v<span class="IDCDocVersionMinor">2</span></p>
                    </td>
                </tr>
            </table>
        </div>
        <script type="text/javascript" src="../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>