<?xml version="1.0" encoding="utf-8"?>
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:tocPath="" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="WebHelp" lang="en-us" xml:lang="en-us" MadCap:conditions="IDC.Wkflw_Agg" class="" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="Help.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" /><title>Import Trade List</title>
        <link href="../../SkinSupport/Slideshow.css" rel="stylesheet" />
        <link href="../../SkinSupport/MadCap.css" rel="stylesheet" />
        <link href="../../Resources/Stylesheets/help.css" rel="stylesheet" />
        <script src="../../SkinSupport/jquery.min.js">
        </script>
        <script src="../../SkinSupport/MadCapAll.js">
        </script>
        <script src="../../SkinSupport/MadCapTopicAll.js">
        </script>
    </head>
    <body>
        <h1 class="Heading" MadCap:conditions="IDC.online"><a name="_top"></a>Import Trade List</h1>
        <p class="BreadcrumbLink"><span class="BreadcrumbLink1" MadCap:autonum="1. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">1. </span></span>Import Trade List</span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="2. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">2. </span></span><a href="../2_Plan/Plan.html#_top" class="MCXref_Title_0" xrefformat="{paratext}">Advanced Netting</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="3. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">3. </span></span><a href="../3_NetForwards/NetForwards.html#_top" class="MCXref_Title_0" xrefformat="{paratext}">Netted Forwards</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="4. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">4. </span></span><a href="../4_ExecuteSpots/4ExecuteSpotsIntro.html#_top" class="MCXref_Title_0" xrefformat="{paratext}">Execute Spots</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="5. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">5. </span></span><a href="../5_ExecuteFixedSpotRoll/ExecuteFixedSpotRoll.html#_top" class="MCXref_Title_0" xrefformat="{paratext}">Execute Fixed Spot Roll</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="6. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">6. </span></span><a href="../6_TrueUpBaseTerm/TrueUpBaseTerm.html#_top" class="MCXref_Title_0" xrefformat="{paratext}">True-Up Base/Term</a></span><span class="BreadcrumbLinkNoNumber"> &gt;</span> <span class="BreadcrumbLink" MadCap:autonum="7. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">7. </span></span><a href="../7_TrueUpXCcy/TrueUpXCcy.html#_top" class="MCXref_Title_0" xrefformat="{paratext}">True-Up Cross Currency</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="8. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">8. </span></span><a href="../8_Export/Export.html#_top" class="MCXref_Title_0" xrefformat="{paratext}">Allocate</a></span></p>
        <p class="Body1">First you import the customer trades that comprise the portfolio that you want to execute and net.</p>
        <p class="Body1">You can import trades from two sources:</p>
        <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span><span class="MCPopup"><a href="javascript:void(0);" MadCap:src="../../Glossary/CSV.html#_top" onclick="FMCPopup( event, this ); return false;" class="MCXref_Glossary_0" alt="" title="" xrefformat="{paratext}" xrefhref="../../Glossary/CSV.html#_top">CSV</a></span> file (<a href="ImportFile.html#_top" class="MCXref_SectionAndPage_0" xrefformat="{paratext}">Import Trade List from a File</a>)</p>
        <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span>Your OMS via the <span class="IDCClientProduct">InvestorFX</span> Staging Area (<a href="ImportStaging.html#_top" class="MCXref_SectionAndPage_0" xrefformat="{paratext}">Import with the Staging Area</a>)</p>
        <div MadCap:conditions="IDC.Type_Fixing,IDC.Type_NonFixing">
            <p class="Body1">The netting process and the order types available differ based on whether you chose the Non-Fixing or the Fixing workflow:</p>
            <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span><span class="Bold">Non-Fixing</span>: <span class="IDCClientProduct">InvestorFX</span> nets your portfolio in real time with mid-rates in RiskNet. You receive trade and price confirmation as soon as your orders are matched.</p>
            <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span><span class="Bold">Fixing</span>: You upload and submit your portfolio before the fixing deadline. You receive confirmation that your orders have been matched, but you only receive trade and price confirmation after the fixing rate has been published.</p>
        </div>
        <div class="Copyright">
            <hr class="Copyright" />
            <table class="Copyright">
                <tr>
                    <td class="CopyrightText">
                        <p class="CellCopyright">&#169; <span class="IDCCopyrightDate">2018</span> Integral Development Corp. All rights reserved. Integral technology is protected under U.S. Patent Nos. 6,347,307; 7,882,011; 8,417,622; 8,862,507; 9,412,134; 9,836,789 and patent pending applications and related intellectual property. This product and related documentation are protected by copyright and distributed under licenses restricting, without limitation, its use, reproduction, copying, distribution, and decompilation.</p>
                    </td>
                    <td class="CopyrightVersion">
                        <p class="CellCopyright"><span class="IDCClientVersion">6.7.7</span>v<span class="IDCDocVersionMinor">2</span></p>
                    </td>
                </tr>
            </table>
        </div>
        <script type="text/javascript" src="../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>