<?xml version="1.0" encoding="utf-8"?>
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:tocPath="Execute Spots (Fixing)" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="WebHelp" lang="en-us" xml:lang="en-us" class="" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="Help.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" /><title>Order Types for Spot Execution</title>
        <link href="../../SkinSupport/Slideshow.css" rel="stylesheet" />
        <link href="../../SkinSupport/MadCap.css" rel="stylesheet" />
        <link href="../../Resources/Stylesheets/help.css" rel="stylesheet" />
        <script src="../../SkinSupport/jquery.min.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapAll.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapTopicAll.js" type="text/javascript">
        </script>
    </head>
    <body>
        <h1 class="Heading"><a name="_top"></a><a name="kanchor7"></a>Order Types for Spot Execution</h1>
        <p class="Body1">Depending on your permissions, you can submit the following order types to execute spots when netting your portfolio:</p>
        <div MadCap:conditions="IDC.Type_NonFixing">
            <p class="Body1">Non-Fixing workflow:</p>
            <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span><a href="Order_Market.html#_top" class="MCXref_SectionAndPage_0" xrefformat="{paratext}">Market Orders</a>
            </p>
            <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span><a href="Order_MarketRange.html#_top" class="MCXref_SectionAndPage_0" xrefformat="{paratext}">Market Range Orders</a>
            </p>
            <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span><a href="Order_Limit.html#_top" class="MCXref_SectionAndPage_0" xrefformat="{paratext}">Limit Orders</a>
            </p>
            <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span><a href="Order_TWAP.html#_top" class="MCXref_SectionAndPage_0" xrefformat="{paratext}">TWAP Orders</a>
            </p>
            <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span>TWAP Plus</p>
            <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span>RiskNet</p>
        </div>
        <div MadCap:conditions="IDC.Type_Fixing">
            <p class="Body1" MadCap:conditions="IDC.Type_NonFixing,IDC.Type_Fixing">Fixing workflow (pre-fix):</p>
            <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span><a href="Order_FixMarket.html#_top" class="MCXref_SectionAndPage_0" xrefformat="{paratext}">C2C+WM Mkt Orders</a>
            </p>
            <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span><a href="Order_FixTWAP.html#_top" class="MCXref_SectionAndPage_0" xrefformat="{paratext}">C2C+WM TWAP Orders</a>
            </p>
            <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span>BAML&#160;WM</p>
            <p class="Body1">Fixing workflow (post-fix):</p>
            <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span>TWAP</p>
            <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span>MKT</p>
            <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span>WMTWAP</p>
            <p class="Bullet1" MadCap:autonum="&#8226; "><span class="autonumber"><span class="BulletSymbol">&#8226; </span></span>WMMKT</p>
        </div>
        <div MadCap:conditions="IDC.online">
            <p class="RelatedTopicsHeader">Related Topics</p>
            <p class="RelatedTopics"><a href="ExecuteSpots.html#_top" class="MCXref_SectionAndPage_0" xrefformat="{paratext}">Execute Spots (Non-Fixing Workflow)</a>
            </p>
            <p class="RelatedTopics"><a href="../../Welcome.html#_top" class="MCXref_SectionAndPage_0" xrefformat="{paratext}">Managing Your Portfolios with InvestorFX</a>
            </p>
        </div>
        <div class="Copyright">
            <hr class="Copyright" />
            <table class="Copyright">
                <tr>
                    <td class="CopyrightText">
                        <p class="CellCopyright">&#169; <span class="IDCCopyrightDate">2018</span> Integral Development Corp. All rights reserved. Integral technology is protected under U.S. Patent Nos. 6,347,307; 7,882,011; 8,417,622; 8,862,507; 9,412,134; 9,836,789 and patent pending applications and related intellectual property. This product and related documentation are protected by copyright and distributed under licenses restricting, without limitation, its use, reproduction, copying, distribution, and decompilation.</p>
                    </td>
                    <td class="CopyrightVersion">
                        <p class="CellCopyright"><span class="IDCClientVersion">6.7.7</span>v<span class="IDCDocVersionMinor">2</span></p>
                    </td>
                </tr>
            </table>
        </div>
        <script type="text/javascript" src="../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>