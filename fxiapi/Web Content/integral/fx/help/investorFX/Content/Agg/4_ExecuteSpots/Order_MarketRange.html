<?xml version="1.0" encoding="utf-8"?>
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:tocPath="Execute Spots (Non-Fixing)|Order Types for Spot Execution" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="WebHelp" lang="en-us" xml:lang="en-us" MadCap:conditions="IDC.Type_NonFixing" class="" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="Help.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" /><title>Market Range Orders</title>
        <link href="../../SkinSupport/Slideshow.css" rel="stylesheet" />
        <link href="../../SkinSupport/MadCap.css" rel="stylesheet" />
        <link href="../../Resources/TableStyles/ColumnTable.css" rel="stylesheet" />
        <link href="../../Resources/TableStyles/PWTitleHead.css" rel="stylesheet" />
        <link href="../../Resources/Stylesheets/help.css" rel="stylesheet" />
        <script src="../../SkinSupport/jquery.min.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapAll.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapTopicAll.js" type="text/javascript">
        </script>
    </head>
    <body>
        <h2 class="Heading"><a name="_top"></a><a name="kanchor10"></a>Market Range Orders</h2>
        <p class="BreadcrumbLink"><span class="BreadcrumbLink1" MadCap:autonum="1. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">1. </span></span><a href="../1_Import/1ImportIntro.html#_top" class="MCXref_Title_0">Import Trade List</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="2. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">2. </span></span><a href="../2_Plan/Plan.html#_top" class="MCXref_Title_0">Advanced Netting</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="3. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">3. </span></span><a href="../3_NetForwards/NetForwards.html#_top" class="MCXref_Title_0">Netted Forwards</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="4. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">4. </span></span>Execute Spots</span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="5. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">5. </span></span><a href="../5_ExecuteFixedSpotRoll/ExecuteFixedSpotRoll.html#_top" class="MCXref_Title_0">Execute Fixed Spot Roll</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="6. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">6. </span></span><a href="../6_TrueUpBaseTerm/TrueUpBaseTerm.html#_top" class="MCXref_Title_0">True-Up Base/Term</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="7. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">7. </span></span><a href="../7_TrueUpXCcy/TrueUpXCcy.html#_top" class="MCXref_Title_0">True-Up Cross Currency</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="8. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">8. </span></span><a href="../8_Export/Export.html#_top" class="MCXref_Title_0">Allocate</a></span>
        </p>
        <p class="Body1">Market range orders include an execution range of pips that you define. Your order is matched with all market prices that achieve an average execution price better than or equal to the execution range until the order is filled or cancelled.</p>
        <p>
            <img src="../../img/OrderDetails_MKTRng.png" />
        </p>
        <p class="FigureTitle" MadCap:autonum="Figure 1 "><span class="autonumber"><span class="Figures">Figure 1 </span></span>Order Details Panel: Market Range Order</p>
        <table class="TableStyle-PWTitleHead" style="margin-left: 0;margin-right: auto;mc-table-style: url('../../Resources/TableStyles/PWTitleHead.css');" cellspacing="0">
            <caption class="TableTitle" MadCap:autonum="Table 1 "><span class="autonumber"><span class="Figures">Table 1 </span></span>Orders Details Panel: Market Range Order Components</caption>
            <col class="TableStyle-PWTitleHead-Column-Column1" />
            <col class="TableStyle-PWTitleHead-Column-Column1" />
            <thead>
                <tr class="TableStyle-PWTitleHead-Head-Header1">
                    <th class="TableStyle-PWTitleHead-HeadE-Column1-Header1">
                        <p class="CellHeading">Item</p>
                    </th>
                    <th class="TableStyle-PWTitleHead-HeadD-Column1-Header1">
                        <p class="CellHeading">Description</p>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">1</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The currency pair of the trade.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">2</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Order type drop-down list. The order entry tools change depending on the order type you choose.</p>
                        <table class="TableStyle-ColumnTable" style="margin-left: 0;margin-right: auto;mc-table-style: url('../../Resources/TableStyles/ColumnTable.css');" cellspacing="0">
                            <col class="TableStyle-ColumnTable-Column-Column1" MadCap:conditions="IDC.Type_NonFixing" />
                            <col class="TableStyle-ColumnTable-Column-Column1" MadCap:conditions="IDC.Type_Fixing" />
                            <tbody>
                                <tr class="TableStyle-ColumnTable-Body-Body1">
                                    <td class="TableStyle-ColumnTable-BodyB-Column1-Body1">
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">LMT: <a href="Order_Limit.html#_top" class="MCXref_SectionAndPage_0">Limit Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">MKT: <a href="Order_Market.html#_top" class="MCXref_SectionAndPage_0">Market Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">MKTRng: <a href="#_top" class="MCXref_SectionAndPage_0 selected">Market Range Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">TWAP: <a href="Order_TWAP.html#_top" class="MCXref_SectionAndPage_0">TWAP Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">TWAP Plus</td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">RiskNet</td>
                                            </tr>
                                        </table>
                                    </td>
                                    <td class="TableStyle-ColumnTable-BodyA-Column1-Body1">
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">C2C+WM Mkt: <a href="Order_FixMarket.html#_top" class="MCXref_SectionAndPage_0">C2C+WM Mkt Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">C2C+WM TWAP: <a href="Order_FixTWAP.html#_top" class="MCXref_SectionAndPage_0">C2C+WM TWAP Orders</a></td>
                                            </tr>
                                        </table>
                                        <table class="AutoNumber_p_CellBullet" style="width: 100%; margin-left: 0;" cellspacing="0" cellpadding="0">
                                            <col style="width: -14.4px;" />
                                            <col style="width: 14.4px;" />
                                            <col style="width: auto;" />
                                            <tr>
                                                <td valign="top" />
                                                <td class="AutoNumber_p_CellBullet" valign="top"><span class="CellBulletSymbol">&#8226; </span>
                                                </td>
                                                <td class="AutoNumber_p_CellBullet" MadCap:autonum="&#8226; " valign="top">BAML WL</td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <p class="CellInfoNote" MadCap:autonum="NOTE: "><span class="autonumber"><span class="InfoSymbol">NOTE: </span></span>The types of orders that you can submit depend on your user permissions.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">3</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Top-of-book ESP bid price for the selected currency pair. Automatically updates from streaming market prices.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">4</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Top-of-book ESP offer price for the selected currency pair. Automatically updates from streaming market prices.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">5</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The value date of the top-of-book prices</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">6</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Trade direction</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">7</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Size field: The size of your order</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">8</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Dealt currency</p>
                    </td>
                </tr>
                <tr style="height: 5px;" class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">9</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText"><span class="Bold">Good Till Duration</span> field: Enter the period of time during which your order is active. Your order is matched with prices until it expires or is filled. Any remaining unfilled balance can be submitted again for execution. The maximum duration is one hour.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">10</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText"><span class="Bold">Range</span> field: Enter the slippage in pips.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">11</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p><span class="Bold">Notes</span> checkbox: Select the checkbox to show a field for entering notes on the order. These notes are only shown to you (the submitting user) and your organization. Your notes are saved in the field as you enter them and stay when you close the panel, submit and order, or log out. Order notes can contain a maximum of 256 characters. Angle bracket characters &#8220;&lt;&#8221; and &#8220;&gt;&#8221; are not allowed.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">12</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText"><span class="Bold">Order Summary</span>: A textual description so that you can quickly confirm the behavior of your order. Updated dynamically as you change the order&#8217;s parameters.</p>
                    </td>
                </tr>
                <tr class="TableStyle-PWTitleHead-Body-Body1">
                    <td class="TableStyle-PWTitleHead-BodyB-Column1-Body1">
                        <p class="CellText">13</p>
                    </td>
                    <td class="TableStyle-PWTitleHead-BodyA-Column1-Body1">
                        <p class="CellText"><span class="Bold">Save Order Details</span> button: Click to save the order details to use when you execute spots.</p>
                    </td>
                </tr>
            </tbody>
        </table>
        <div MadCap:conditions="IDC.online">
            <p class="RelatedTopicsHeader">Related Topics</p>
            <p class="RelatedTopics"><a href="Order_Market.html#_top" class="MCXref_SectionAndPage_0">Market Orders</a>
            </p>
            <p class="RelatedTopics"><a href="OrderTypes.html#_top" class="MCXref_SectionAndPage_0">Order Types for Spot Execution</a>
            </p>
        </div>
        <div class="Copyright">
            <hr class="Copyright" />
            <table class="Copyright">
                <tr>
                    <td class="CopyrightText">
                        <p class="CellCopyright">&#169; <span class="IDCCopyrightDate">2018</span> Integral Development Corp. All rights reserved. Integral technology is protected under U.S. Patent Nos. 6,347,307; 7,882,011; 8,417,622; 8,862,507; 9,412,134; 9,836,789 and patent pending applications and related intellectual property. This product and related documentation are protected by copyright and distributed under licenses restricting, without limitation, its use, reproduction, copying, distribution, and decompilation.</p>
                    </td>
                    <td class="CopyrightVersion">
                        <p class="CellCopyright"><span class="IDCClientVersion">6.7.7</span>v<span class="IDCDocVersionMinor">2</span></p>
                    </td>
                </tr>
            </table>
        </div>
        <script type="text/javascript" src="../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>