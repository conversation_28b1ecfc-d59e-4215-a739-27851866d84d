<?xml version="1.0" encoding="utf-8"?>
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" MadCap:tocPath="" MadCap:InPreviewMode="false" MadCap:PreloadImages="false" MadCap:RuntimeFileType="Topic" MadCap:TargetType="WebHelp" lang="en-us" xml:lang="en-us" class="" MadCap:PathToHelpSystem="../../../" MadCap:HelpSystemFileName="Help.xml" MadCap:SearchType="Stem">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" /><title>Advanced Netting</title>
        <link href="../../SkinSupport/Slideshow.css" rel="stylesheet" />
        <link href="../../SkinSupport/MadCap.css" rel="stylesheet" />
        <link href="../../Resources/TableStyles/CWTitleHead.css" rel="stylesheet" />
        <link href="../../Resources/Stylesheets/help.css" rel="stylesheet" />
        <script src="../../SkinSupport/jquery.min.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapAll.js" type="text/javascript">
        </script>
        <script src="../../SkinSupport/MadCapTopicAll.js" type="text/javascript">
        </script>
    </head>
    <body>
        <h1 class="Heading" MadCap:conditions="IDC.online"><a name="_top"></a><a name="kanchor3"></a>Advanced Netting</h1>
        <p class="BreadcrumbLink"><span class="BreadcrumbLink1" MadCap:autonum="1. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">1. </span></span><a href="../1_Import/1ImportIntro.html#_top" class="MCXref_Title_0">Import Trade List</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="2. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">2. </span></span>Advanced Netting</span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="3. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">3. </span></span><a href="../3_NetForwards/NetForwards.html#_top" class="MCXref_Title_0">Netted Forwards</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="4. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">4. </span></span><a href="../4_ExecuteSpots/4ExecuteSpotsIntro.html#_top" class="MCXref_Title_0">Execute Spots</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="5. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">5. </span></span><a href="../5_ExecuteFixedSpotRoll/ExecuteFixedSpotRoll.html#_top" class="MCXref_Title_0">Execute Fixed Spot Roll</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="6. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">6. </span></span><a href="../6_TrueUpBaseTerm/TrueUpBaseTerm.html#_top" class="MCXref_Title_0">True-Up Base/Term</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="7. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">7. </span></span><a href="../7_TrueUpXCcy/TrueUpXCcy.html#_top" class="MCXref_Title_0">True-Up Cross Currency</a></span><span class="BreadcrumbLinkNoNumber"> &gt; </span><span class="BreadcrumbLink" MadCap:autonum="8. "><span class="autonumber"><span class="BreadcrumbNumberSymbol">8. </span></span><a href="../8_Export/Export.html#_top" class="MCXref_Title_0">Allocate</a></span>
        </p>
        <p class="Body1">The Advanced Netting screen shows you summaries of transaction cost bases for each netting method so that you can choose the approach that best meets your business needs. Your choice of netting plan determines how trades are executed and how netting is accomplished.</p>
        <p class="Body1">You can configure <span class="IDCClientProduct">InvestorFX</span> to apply a default netting plan and skip this step. See <a href="../../Settings/SettingsPlan.html#_top" class="MCXref_SectionAndPage_0">Choosing the Default Netting Plan</a> and <a href="../../Settings/SettingsSkipping.html#_top" class="MCXref_SectionAndPage_0">Choosing Workflow Steps To Skip</a>.</p>
        <p class="Numbered1" MadCap:autonum="1."><span class="autonumber"><span class="NumberSymbol">1.</span></span>Choose a netting method by clicking a method&#8217;s column heading.</p>
        <p class="Body2">When you choose a netting plan, the following happens:</p>
        <p class="Bullet2" MadCap:autonum="&#9702; "><span class="autonumber"><span class="BulletSymbol">&#9702; </span></span>Your chosen netting plan is indicated with a green check (<img src="../../img/IconAccept.png" class="InLineImage" />).</p>
        <p class="Bullet2" MadCap:autonum="&#9702; "><span class="autonumber"><span class="BulletSymbol">&#9702; </span></span>The portfolio status indicators at the top of the screen update with your chosen portfolio plan.</p>
        <p class="Bullet2" MadCap:autonum="&#9702; "><span class="autonumber"><span class="BulletSymbol">&#9702; </span></span>The portfolio status changes to &#8220;Planned&#8221;.</p>
        <p class="Numbered" MadCap:autonum="2."><span class="autonumber"><span class="NumberSymbol">2.</span></span>(<span class="Emphasis">Optional</span>) Click <span class="Bold">Refresh Market Data</span> to update the table with the latest market rates.</p>
        <p class="Numbered" MadCap:autonum="3."><span class="autonumber"><span class="NumberSymbol">3.</span></span>Click the <img src="../../img/IconArrowBlueNext.png" class="InLineImage" /> Next button.</p>
        <h2 class="Heading">Plan Netting Calculations</h2>
        <table style="margin-left: 0;margin-right: auto;caption-side: top;mc-table-style: url('../../Resources/TableStyles/CWTitleHead.css');" class="TableStyle-CWTitleHead" cellspacing="0">
            <caption class="TableTitle" MadCap:autonum="Table 1 "><span class="autonumber"><span class="Figures">Table 1 </span></span>Plan Calculations</caption>
            <col class="TableStyle-CWTitleHead-Column-Column1" />
            <col class="TableStyle-CWTitleHead-Column-Column1" />
            <thead>
                <tr class="TableStyle-CWTitleHead-Head-Header1">
                    <th class="TableStyle-CWTitleHead-HeadE-Column1-Header1">
                        <p class="CellHeading">Calculation</p>
                    </th>
                    <th class="TableStyle-CWTitleHead-HeadD-Column1-Header1">
                        <p class="CellHeading">Description</p>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">% Netting</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The percentage of netted amount in relation to the total portfolio amount</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">TX Cost (USD)</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Your estimated cost to execute the portfolio with the chosen netting plan in USD</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">TX Cost(bps)</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Your estimated cost to execute the portfolio with the chosen netting plan in basis points</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyB-Column1-Body1">
                        <p class="CellText">Cost Savings(bps)</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyA-Column1-Body1">
                        <p class="CellText">Your estimated savings with the chosen netting plan versus no netting in basis points</p>
                    </td>
                </tr>
            </tbody>
        </table>
        <h2 class="Heading"><a name="kanchor4"></a>Plan Netting Scenarios and Columns</h2>
        <p class="Body1">You can configure <span class="IDCClientProduct">InvestorFX</span> to apply a default netting plan and skip this step. See <a href="../../Settings/SettingsSkipping.html#_top" class="MCXref_SectionAndPage_0">Choosing Workflow Steps To Skip</a>.</p>
        <table style="margin-left: 0;margin-right: auto;caption-side: top;mc-table-style: url('../../Resources/TableStyles/CWTitleHead.css');" class="TableStyle-CWTitleHead" cellspacing="0">
            <caption class="TableTitle" MadCap:autonum="Table 2 "><span class="autonumber"><span class="Figures">Table 2 </span></span>Netting Scenarios</caption>
            <col class="TableStyle-CWTitleHead-Column-Column1" />
            <col class="TableStyle-CWTitleHead-Column-Column1" />
            <thead>
                <tr class="TableStyle-CWTitleHead-Head-Header1">
                    <th class="TableStyle-CWTitleHead-HeadE-Column1-Header1">
                        <p class="CellHeading">Calculation</p>
                    </th>
                    <th class="TableStyle-CWTitleHead-HeadD-Column1-Header1">
                        <p class="CellHeading">Description</p>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">No Netting</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">All trades in the portfolio executed individually</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Simple Netting</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Netting base amounts and term amounts separately</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1" MadCap:conditions="">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Base/Term Netting</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Netting base and term amounts together</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1" MadCap:conditions="">
                    <td class="TableStyle-CWTitleHead-BodyB-Column1-Body1">
                        <p class="CellText">Base/Term + Cross CCY Netting</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyA-Column1-Body1">
                        <p class="CellText">Netting base, term, and cross currency amounts together using the vehicle currencies specified for the cross currencies in the user settings (see <a href="../../Settings/SettingsCrossCurrency.html#_top" class="MCXref_SectionAndPage_0">Configuring Cross Currency Netting</a>)</p>
                    </td>
                </tr>
            </tbody>
        </table>
        <p class="Anchor">&#160;</p>
        <table style="margin-left: 0;margin-right: auto;caption-side: top;mc-table-style: url('../../Resources/TableStyles/CWTitleHead.css');" class="TableStyle-CWTitleHead" cellspacing="0">
            <caption class="TableTitle" MadCap:autonum="Table 3 "><span class="autonumber"><span class="Figures">Table 3 </span></span>Plan Columns</caption>
            <col class="TableStyle-CWTitleHead-Column-Column1" />
            <col class="TableStyle-CWTitleHead-Column-Column1" />
            <thead>
                <tr class="TableStyle-CWTitleHead-Head-Header1">
                    <th class="TableStyle-CWTitleHead-HeadE-Column1-Header1">
                        <p class="CellHeading">Column</p>
                    </th>
                    <th class="TableStyle-CWTitleHead-HeadD-Column1-Header1">
                        <p class="CellHeading">Description</p>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">CCY Pair</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The currency pair of the trade</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Value Date</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The value date of the trade</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Dealt B/S</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">Whether the dealt currency is bought or sold from the perspective of the trade&#8217;s account</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Dealt CCY</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The dealt currency of the trade</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Net Base</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The net dealt amount of the base currency</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Net Term</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The net dealt amount of the term currency</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Bid Rate</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The bid rate of the price</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyE-Column1-Body1">
                        <p class="CellText">Mid Rate</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyD-Column1-Body1">
                        <p class="CellText">The top-of-the-book mid rate</p>
                    </td>
                </tr>
                <tr class="TableStyle-CWTitleHead-Body-Body1">
                    <td class="TableStyle-CWTitleHead-BodyB-Column1-Body1">
                        <p class="CellText">Offer Rate</p>
                    </td>
                    <td class="TableStyle-CWTitleHead-BodyA-Column1-Body1">
                        <p class="CellText">The offer rate of the price</p>
                    </td>
                </tr>
            </tbody>
        </table>
        <div MadCap:conditions="IDC.online">
            <p class="RelatedTopicsHeader">Related Topics</p>
            <p class="RelatedTopics"><a href="../../Settings/SettingsPlan.html#_top" class="MCXref_SectionAndPage_0">Choosing the Default Netting Plan</a>
            </p>
            <p class="RelatedTopics"><a href="../../Settings/SettingsSkipping.html#_top" class="MCXref_SectionAndPage_0">Choosing Workflow Steps To Skip</a>
            </p>
            <p class="RelatedTopics"><a href="../../Settings/SettingsCrossCurrency.html#_top" class="MCXref_SectionAndPage_0">Configuring Cross Currency Netting</a>
            </p>
        </div>
        <div class="Copyright">
            <hr class="Copyright" />
            <table class="Copyright">
                <tr>
                    <td class="CopyrightText">
                        <p class="CellCopyright">&#169; <span class="IDCCopyrightDate">2018</span> Integral Development Corp. All rights reserved. Integral technology is protected under U.S. Patent Nos. 6,347,307; 7,882,011; 8,417,622; 8,862,507; 9,412,134; 9,836,789 and patent pending applications and related intellectual property. This product and related documentation are protected by copyright and distributed under licenses restricting, without limitation, its use, reproduction, copying, distribution, and decompilation.</p>
                    </td>
                    <td class="CopyrightVersion">
                        <p class="CellCopyright"><span class="IDCClientVersion">6.7.7</span>v<span class="IDCDocVersionMinor">2</span></p>
                    </td>
                </tr>
            </table>
        </div>
        <script type="text/javascript" src="../../SkinSupport/MadCapBodyEnd.js">
        </script>
    </body>
</html>