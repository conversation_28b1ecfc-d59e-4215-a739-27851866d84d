var xmlGlossaryData = "";
xmlGlossaryData += '<?xml version=\"1.0\" encoding=\"utf-8\"?>';
xmlGlossaryData += '<html xmlns:MadCap=\"http://www.madcapsoftware.com/Schemas/MadCap.xsd\" MadCap:tocPath=\"\" MadCap:InPreviewMode=\"false\" MadCap:PreloadImages=\"false\" MadCap:RuntimeFileType=\"Glossary\" MadCap:TargetType=\"WebHelp\" lang=\"en-us\" xml:lang=\"en-us\" MadCap:PathToHelpSystem=\"../\" MadCap:HelpSystemFileName=\"Help.xml\">';
xmlGlossaryData += '    <head>';
xmlGlossaryData += '        <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />';
xmlGlossaryData += '        <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />';
xmlGlossaryData += '        <link href=\"SkinSupport/Slideshow.css\" rel=\"stylesheet\" type=\"text/css\" MadCap:generated=\"True\" />';
xmlGlossaryData += '        <link href=\"SkinSupport/MadCap.css\" rel=\"stylesheet\" type=\"text/css\" MadCap:generated=\"True\" />';
xmlGlossaryData += '        <title>Glossary</title>';
xmlGlossaryData += '        <link href=\"Resources/Stylesheets/help.css\" rel=\"stylesheet\" type=\"text/css\" />';
xmlGlossaryData += '        <script src=\"SkinSupport/jquery.min.js\" type=\"text/javascript\">';
xmlGlossaryData += '        </script>';
xmlGlossaryData += '        <script src=\"SkinSupport/MadCapAll.js\" type=\"text/javascript\">';
xmlGlossaryData += '        </script>';
xmlGlossaryData += '        <script src=\"SkinSupport/MadCapTopicAll.js\" type=\"text/javascript\">';
xmlGlossaryData += '        </script>';
xmlGlossaryData += '    </head>';
xmlGlossaryData += '    <body style=\"background-color: #fafafa;\">';
xmlGlossaryData += '        <div id=\"GlossaryBody\">';
xmlGlossaryData += '            <div class=\"GlossaryPageEntry\">';
xmlGlossaryData += '                <div class=\"GlossaryPageTerm\">';
xmlGlossaryData += '                    <a href=\"javascript:void(0);\" class=\"GlossaryPageTerm\" id=\"MCDropDownHotSpot_580200857_0\" onclick=\"FMCDropDown( this ); FMCScrollToVisible( window, this.parentNode.parentNode ); return false;\">My Term</a>';
xmlGlossaryData += '                    <a name=\"580200857_anchor1\">';
xmlGlossaryData += '                    </a>';
xmlGlossaryData += '                </div>';
xmlGlossaryData += '                <div class=\"GlossaryPageDefinition\" id=\"MCDropDownBody_580200857_0\" style=\"display: none;\">My definition</div>';
xmlGlossaryData += '            </div>';
xmlGlossaryData += '        </div>';
xmlGlossaryData += '        <p>&#160;</p>';
xmlGlossaryData += '        <script type=\"text/javascript\" src=\"SkinSupport/MadCapBodyEnd.js\">';
xmlGlossaryData += '        </script>';
xmlGlossaryData += '    </body>';
xmlGlossaryData += '</html>';
CMCXmlParser._FilePathToXmlStringMap.Add('Glossary', xmlGlossaryData);
