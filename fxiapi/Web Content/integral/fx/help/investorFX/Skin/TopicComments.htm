<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" lang="en-us" xml:lang="en-us" MadCap:TargetType="WebHelp" MadCap:PathToHelpSystem="../" MadCap:RuntimeFileType="TopicComments" MadCap:HelpSystemFileName="Help.xml" MadCap:PathToSkinFolder="../Data/SkinInvestorFXSkin/" MadCap:InPreviewMode="False">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <title>Topic Comments</title>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <script type="text/javascript" src="../Content/SkinSupport/MadCapAll.js">
        </script>
        <script type="text/javascript" src="../Content/SkinSupport/MadCapBodyEnd.js">
        </script>
        <style type="text/css">
a:active
{
	color: #000000;
	text-decoration: none;
	background-color: transparent;
}
a:link
{
	color: #000000;
	text-decoration: none;
}
a:visited
{
	color: #000000;
	text-decoration: none;
}
a:hover
{
	color: #000000;
	text-decoration: underline;
}
body
{
	background-color: #fafafa;
	font-family: Verdana, Sans-Serif;
	font-size: 10px;
	margin: 0px;
	padding: 3px;
}
img
{
	border: none;
	vertical-align: bottom;
}
#ButtonsWrapper
{
    margin-right: 2px; /* Need this for Firefox since for some reason it adds space between the button elements and the img elements inside them. Otherwise, the topic comments frame contains scrollbars in Firefox. */
}
#Buttons
{
    border: none;
    border-collapse: collapse;
    margin: 0px;
    padding: 0px;
    width: 100%;
}
button
{
    background-color: transparent;
    border: none;
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
}
</style>
    </head>
    <body>
        <div id="ButtonsWrapper">
            <table id="Buttons">
                <tr>
                    <td>&#160;</td>
                </tr>
            </table>
        </div>
        <br />
    </body>
</html>