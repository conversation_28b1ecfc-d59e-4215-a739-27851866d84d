<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:MadCap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" lang="en-us" xml:lang="en-us" MadCap:TargetType="WebHelp" MadCap:PathToHelpSystem="../" MadCap:RuntimeFileType="Navigation" MadCap:HelpSystemFileName="Help.xml" MadCap:PathToSkinFolder="../Data/SkinInvestorFXSkin/" MadCap:InPreviewMode="False">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <title>Navigation</title>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <script type="text/javascript" src="../Content/SkinSupport/MadCapAll.js">
        </script>
        <style type="text/css">
a:active
{
	color: #0055ff;
	text-decoration: none;
	background-color: transparent;
}
a:link
{
	color: #0055ff;
	text-decoration: none;
}
a:visited
{
	color: #0055ff;
	text-decoration: none;
}
a:hover
{
	color: #ff0000;
	text-decoration: none;
}
div.AccordionItem a:active
{
	color: #000000;
	text-decoration: none;
	background-color: transparent;
}
div.AccordionItem a:link
{
	color: #000000;
	text-decoration: none;
}
div.AccordionItem a:visited
{
	color: #000000;
	text-decoration: none;
}
div.AccordionItem a:hover
{
	color: #000000;
	text-decoration: none;
}
body
{
	font-family: Arial, Sans-Serif;
	font-size: 12px;
	margin: 0px;
	padding: 0px;
}
iframe
{
    display: none;
	width: 100%;
	position: absolute;
}
img
{
	width: 16px;
	height: 16px;
	border: none;
	vertical-align: bottom;
    margin-right: 5px;
}
table
{
	border: none;
	margin: 0px;
	padding: 0px;
}
td
{
	margin: 0px;
	padding: 0px;
}
td.Icon
{
	text-indent: 10px;
	width: 30px;
    white-space: nowrap;
}
.AccordionItem
{
	cursor: pointer;
	cursor: hand;
	height: 28px;
	margin: 0px;
	padding: 0px;
	border: none;
	position: absolute;
	width: 100%;
}
.AccordionItem table
{
	width: 100%;
	height: 28px;
}
.AccordionItem table table
{
	border-collapse: collapse;
}
.AccordionItem a
{
	color: #000000;
	font-size: 12px;
	font-weight: bold;
	white-space: nowrap;
}
#AccordionIcons
{
	cursor: pointer;
	cursor: hand;
	height: 28px;
	margin: 0px;
	padding: 0px;
	border: none;
	position: absolute;
	width: 100%;
	text-align: right;
}
#AccordionIcons table
{
	width: 100%;
	height: 28px;
}
#AccordionIcons table table
{
	border-collapse: collapse;
	margin-left: auto;
	width: auto;
}
#AccordionIcons table table td
{
	display: none;
	visibility: hidden;
}
#AccordionExpander
{
	color: #ffffff;
	font-size: 1px;
	cursor: n-resize;
	width: 100%;
	height: 7px;
	margin: 0px;
	padding: 0px;
	border: none;
	position: absolute;
}
#NavigationTop
{
	font-size: 0px;
	height: 8px;
	margin: 0px;
	padding: 0px;
	border: none;
}
</style>
    </head>
    <body>
        <div id="NavigationTop" style="background-image: url('../Data/SkinInvestorFXSkin/NavigationTopGradient.jpg');">
        </div>
        <iframe name="toc" id="toc" src="Toc.htm" frameborder="0" title="Table of Contents">
        </iframe>
        <iframe name="index" id="index" src="Index.htm" frameborder="0" scrolling="no" title="Index">
        </iframe>
        <iframe name="search" id="search" src="Search.htm" frameborder="0" scrolling="no" title="Search">
        </iframe>
        <iframe name="glossary" id="glossary" src="../Content/Glossary.htm" frameborder="0" title="Glossary" style="display: none;">
        </iframe>
        <iframe name="favorites" id="favorites" src="Favorites.htm" frameborder="0" title="Favorites" style="display: none;">
        </iframe>
        <iframe name="browsesequences" id="browsesequences" src="BrowseSequences.htm" frameborder="0" title="Browse Sequences" style="display: none;">
        </iframe>
        <iframe name="community" id="community" frameborder="0" title="Community" style="display: none;">
        </iframe>
        <div id="AccordionExpander" style="background-image: url('../Data/SkinInvestorFXSkin/NavigationBottomGradient.jpg');" onmousedown="ExpandAccordionStart(); return false;">&#160;</div>
        <div class="AccordionItem" id="tocAccordion" style="display: none;" onclick="AccordionItemClick( this );">
            <table cellspacing="0">
                <tr>
                    <td id="tocAccordionBackgroundCell" style="background-image: url('../Data/SkinInvestorFXSkin/TocAccordionBackground.jpg');">
                        <table>
                            <tr>
                                <td class="Icon">
                                    <a href="javascript:void( 0 );">
                                        <img id="tocAccordionItemIcon" src="../Data/SkinInvestorFXSkin/WebHelpTOCButtonIcon.gif" alt="" style="width: 16px;height: 16px;" />&#160;<span>Table of Contents</span></a>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
        <div class="AccordionItem" id="indexAccordion" style="display: none;" onclick="AccordionItemClick( this );">
            <table cellspacing="0">
                <tr>
                    <td id="indexAccordionBackgroundCell" style="background-image: url('../Data/SkinInvestorFXSkin/IndexAccordionBackground.jpg');">
                        <table>
                            <tr>
                                <td class="Icon">
                                    <a href="javascript:void( 0 );">
                                        <img id="indexAccordionItemIcon" src="../Data/SkinInvestorFXSkin/WebHelpIndexButtonIcon.gif" alt="" style="width: 16px;height: 16px;" />&#160;<span>Index</span></a>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
        <div class="AccordionItem" id="searchAccordion" style="display: none;" onclick="AccordionItemClick( this );">
            <table cellspacing="0">
                <tr>
                    <td id="searchAccordionBackgroundCell" style="background-image: url('../Data/SkinInvestorFXSkin/SearchAccordionBackground.jpg');">
                        <table>
                            <tr>
                                <td class="Icon">
                                    <a href="javascript:void( 0 );">
                                        <img id="searchAccordionItemIcon" src="../Data/SkinInvestorFXSkin/WebHelpSearchButtonIcon.gif" alt="" style="width: 16px;height: 16px;" />&#160;<span>Search</span></a>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
        <div class="AccordionItem" id="glossaryAccordion" style="display: none;" onclick="AccordionItemClick( this );">
            <table cellspacing="0">
                <tr>
                    <td id="glossaryAccordionBackgroundCell" style="background-image: url('../Data/SkinInvestorFXSkin/GlossaryAccordionBackground.jpg');">
                        <table>
                            <tr>
                                <td class="Icon">
                                    <a href="javascript:void( 0 );">
                                        <img id="glossaryAccordionItemIcon" src="../Data/SkinInvestorFXSkin/GlossaryIcon.gif" alt="" style="width: 16px;height: 16px;" />&#160;<span>Glossary</span></a>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
        <div class="AccordionItem" id="favoritesAccordion" style="display: none;" onclick="AccordionItemClick( this );">
            <table cellspacing="0">
                <tr>
                    <td id="favoritesAccordionBackgroundCell" style="background-image: url('../Data/SkinInvestorFXSkin/FavoritesAccordionBackground.jpg');">
                        <table>
                            <tr>
                                <td class="Icon">
                                    <a href="javascript:void( 0 );">
                                        <img id="favoritesAccordionItemIcon" src="../Data/SkinInvestorFXSkin/FavoritesIcon.gif" alt="" style="width: 16px;height: 16px;" />&#160;<span>Favorites</span></a>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
        <div class="AccordionItem" id="browsesequencesAccordion" style="display: none;" onclick="AccordionItemClick( this );">
            <table cellspacing="0">
                <tr>
                    <td id="browsesequencesAccordionBackgroundCell" style="background-image: url('../Data/SkinInvestorFXSkin/BrowsesequencesAccordionBackground.jpg');">
                        <table>
                            <tr>
                                <td class="Icon">
                                    <a href="javascript:void( 0 );">
                                        <img id="browsesequencesAccordionItemIcon" src="../Data/SkinInvestorFXSkin/BrowsesequencesIcon.gif" alt="" style="width: 16px;height: 16px;" />&#160;<span>Browse Sequences</span></a>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
        <div class="AccordionItem" id="communityAccordion" style="display: none;" onclick="AccordionItemClick( this );">
            <table cellspacing="0">
                <tr>
                    <td id="communityAccordionBackgroundCell" style="background-image: url('../Data/SkinInvestorFXSkin/CommunityAccordionBackground.jpg');">
                        <table>
                            <tr>
                                <td class="Icon">
                                    <a href="javascript:void( 0 );">
                                        <img id="communityAccordionItemIcon" src="../Data/SkinInvestorFXSkin/CommunityIcon.gif" alt="" style="width: 16px;height: 16px;" />&#160;<span>Community</span></a>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
        <div id="AccordionIcons">
            <table cellspacing="0">
                <tr>
                    <td id="AccordionTrayBackgroundCell" style="background-image: url('../Data/SkinInvestorFXSkin/AccordionIconsBackground.jpg');">
                        <table>
                            <tr>
                                <td id="tocIcon" title="Table of Contents" style="background-image: url('../Data/SkinInvestorFXSkin/TocAccordionBackground.jpg');" onclick="AccordionIconClick( this );" onkeyup="Navigation_ItemOnkeyup( event );">
                                    <img id="tocAccordionTrayIcon" src="../Data/SkinInvestorFXSkin/WebHelpTOCButtonIcon.gif" alt="Table of Contents" style="width: 16px;height: 16px;" />
                                </td>
                                <td id="indexIcon" title="Index" style="background-image: url('../Data/SkinInvestorFXSkin/IndexAccordionBackground.jpg');" onclick="AccordionIconClick( this );" onkeyup="Navigation_ItemOnkeyup( event );">
                                    <img id="indexAccordionTrayIcon" src="../Data/SkinInvestorFXSkin/WebHelpIndexButtonIcon.gif" alt="Index" style="width: 16px;height: 16px;" />
                                </td>
                                <td id="searchIcon" title="Search" style="background-image: url('../Data/SkinInvestorFXSkin/SearchAccordionBackground.jpg');" onclick="AccordionIconClick( this );" onkeyup="Navigation_ItemOnkeyup( event );">
                                    <img id="searchAccordionTrayIcon" src="../Data/SkinInvestorFXSkin/WebHelpSearchButtonIcon.gif" alt="Search" style="width: 16px;height: 16px;" />
                                </td>
                                <td id="glossaryIcon" title="Glossary" style="background-image: url('../Data/SkinInvestorFXSkin/GlossaryAccordionBackground.jpg');" onclick="AccordionIconClick( this );" onkeyup="Navigation_ItemOnkeyup( event );">
                                    <img id="glossaryAccordionTrayIcon" src="../Data/SkinInvestorFXSkin/GlossaryIcon.gif" alt="Glossary" style="width: 16px;height: 16px;" />
                                </td>
                                <td id="favoritesIcon" title="Favorites" style="background-image: url('../Data/SkinInvestorFXSkin/FavoritesAccordionBackground.jpg');" onclick="AccordionIconClick( this );" onkeyup="Navigation_ItemOnkeyup( event );">
                                    <img id="favoritesAccordionTrayIcon" src="../Data/SkinInvestorFXSkin/FavoritesIcon.gif" alt="Favorites" style="width: 16px;height: 16px;" />
                                </td>
                                <td id="browsesequencesIcon" title="Browse Sequences" style="background-image: url('../Data/SkinInvestorFXSkin/BrowsesequencesAccordionBackground.jpg');" onclick="AccordionIconClick( this );" onkeyup="Navigation_ItemOnkeyup( event );">
                                    <img id="browsesequencesAccordionTrayIcon" src="../Data/SkinInvestorFXSkin/BrowsesequencesIcon.gif" alt="Browse Sequences" style="width: 16px;height: 16px;" />
                                </td>
                                <td id="communityIcon" title="Community" style="background-image: url('../Data/SkinInvestorFXSkin/CommunityAccordionBackground.jpg');" onclick="AccordionIconClick( this );" onkeyup="Navigation_ItemOnkeyup( event );">
                                    <img id="communityAccordionTrayIcon" src="../Data/SkinInvestorFXSkin/CommunityIcon.gif" alt="Community" style="width: 16px;height: 16px;" />
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
    </body>
</html>