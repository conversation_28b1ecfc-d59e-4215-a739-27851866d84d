var xmlSkinData = "";
xmlSkinData += '<?xml version=\"1.0\" encoding=\"utf-8\"?>';
xmlSkinData += '<CatapultSkin Version=\"1\" Title=\"Integral InvestorFX Help\" AutoSyncTOC=\"false\" Tabs=\"TOC,Index,Search\" Top=\"0px\" Left=\"0px\" Width=\"700px\" Height=\"700px\" DefaultTab=\"TOC\" UseDefaultBrowserSetup=\"true\" Bottom=\"0px\" UseBrowserDefaultSize=\"true\">';
xmlSkinData += '    <HtmlHelpOptions ShowMenuBar=\"False\" TopmostWindowStyle=\"False\" AutoShowNavigationPane=\"False\" EnableButtonCaptions=\"False\" NavigationPaneWidth=\"0\" Buttons=\"Hide,Back,Forward,Print\" HideNavigationOnStartup=\"False\" />';
xmlSkinData += '    <Toc LinesBetweenItems=\"True\" LinesFromRoot=\"False\" SingleClick=\"False\" PlusMinusSquares=\"False\" AlwaysShowSelection=\"False\" UseFolderIcons=\"False\" ImageListWidth=\"16\" BinaryStorage=\"False\" />';
xmlSkinData += '    <Stylesheet Link=\"Stylesheet.xml\">';
xmlSkinData += '    </Stylesheet>';
xmlSkinData += '    <WebHelpOptions HideNavigationOnStartup=\"False\" VisibleAccordionItemCount=\"3\" NavigationPanePosition=\"Left\" NavigationPaneWidth=\"300\" />';
xmlSkinData += '    <Toolbar EnableCustomLayout=\"True\" Buttons=\"Separator|Back|Forward|Separator|Home|Separator|Print|Separator\" />';
xmlSkinData += '</CatapultSkin>';
CMCXmlParser._FilePathToXmlStringMap.Add('Skin', xmlSkinData);
