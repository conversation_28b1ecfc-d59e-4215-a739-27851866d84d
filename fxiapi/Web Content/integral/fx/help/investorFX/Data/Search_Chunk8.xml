<?xml version="1.0" encoding="utf-8"?><index><stem n="unfil"><phr n="unfilled"><ent r="7" t="6" w="228" /><ent r="7" t="6" w="233" /><ent r="7" t="6" w="358" /><ent r="7" t="8" w="193" /><ent r="7" t="9" w="173" /><ent r="7" t="10" w="186" /><ent r="7" t="11" w="490" /><ent r="7" t="11" w="502" /><ent r="7" t="12" w="175" /><ent r="7" t="13" w="491" /><ent r="7" t="13" w="503" /><ent r="7" t="28" w="252" /><ent r="7" t="28" w="257" /><ent r="7" t="29" w="317" /><ent r="7" t="29" w="442" /><ent r="7" t="29" w="454" /></phr></stem><stem n="left"><phr n="left"><ent r="7" t="6" w="232" /><ent r="7" t="25" w="73" /><ent r="7" t="28" w="256" /></phr></stem><stem n="refer"><phr n="reference"><ent r="7" t="6" w="247" /><ent r="7" t="6" w="262" /><ent r="7" t="28" w="271" /><ent r="7" t="28" w="286" /></phr></stem><stem n="unexecut"><phr n="unexecuted"><ent r="7" t="6" w="252" /><ent r="7" t="6" w="267" /><ent r="9" t="18" w="96" /><ent r="7" t="25" w="296" /><ent r="7" t="25" w="378" /><ent r="7" t="28" w="276" /><ent r="7" t="28" w="291" /></phr></stem><stem n="lmt"><phr n="lmt"><ent r="7" t="6" w="293" /><ent r="7" t="6" w="341" /><ent r="11" t="8" w="73" /><ent r="11" t="9" w="77" /><ent r="11" t="10" w="90" /><ent r="11" t="11" w="156" /><ent r="11" t="12" w="79" /><ent r="11" t="13" w="157" /><ent r="7" t="28" w="453" /><ent r="11" t="29" w="204" /></phr></stem><stem n="pip"><phr n="pips"><ent r="7" t="6" w="297" /><ent r="7" t="6" w="364" /><ent r="3" t="10" w="40" /><ent r="7" t="10" w="200" /><ent r="3" t="11" w="80" /><ent r="3" t="13" w="81" /><ent r="7" t="28" w="457" /><ent r="7" t="29" w="410" /></phr></stem><stem n="mktrng"><phr n="mktrng"><ent r="7" t="6" w="303" /><ent r="7" t="6" w="359" /><ent r="11" t="8" w="79" /><ent r="11" t="9" w="83" /><ent r="11" t="10" w="96" /><ent r="11" t="11" w="162" /><ent r="11" t="12" w="85" /><ent r="11" t="13" w="163" /><ent r="7" t="28" w="463" /><ent r="11" t="29" w="210" /></phr></stem><stem n="mkt"><phr n="mkt"><ent r="7" t="6" w="323" /><ent r="5" t="7" w="43" /><ent r="4" t="7" w="55" /><ent r="11" t="8" w="76" /><ent r="11" t="8" w="90" /><ent r="12" t="8" w="92" /><ent r="11" t="9" w="80" /><ent r="11" t="9" w="94" /><ent r="12" t="9" w="96" /><ent r="11" t="10" w="93" /><ent r="11" t="10" w="107" /><ent r="12" t="10" w="109" /><ent r="11" t="11" w="159" /><ent r="11" t="11" w="173" /><ent r="12" t="11" w="175" /><ent r="268435456" t="12" w="2" /><ent r="1048578" t="12" w="9" /><ent r="11" t="12" w="82" /><ent r="11" t="12" w="96" /><ent r="12" t="12" w="98" /><ent r="11" t="13" w="160" /><ent r="11" t="13" w="174" /><ent r="12" t="13" w="176" /><ent r="7" t="25" w="38" /><ent r="7" t="25" w="57" /><ent r="7" t="25" w="59" /><ent r="7" t="28" w="335" /><ent r="8" t="28" w="354" /><ent r="7" t="28" w="425" /><ent r="11" t="29" w="207" /><ent r="11" t="29" w="221" /><ent r="12" t="29" w="223" /></phr></stem><stem n="ensur"><phr n="ensure"><ent r="7" t="6" w="327" /><ent r="3" t="9" w="43" /><ent r="3" t="12" w="45" /><ent r="7" t="28" w="338" /><ent r="7" t="28" w="428" /></phr></stem><stem n="immedi"><phr n="immediate"><ent r="7" t="6" w="328" /><ent r="3" t="9" w="44" /><ent r="3" t="12" w="46" /><ent r="7" t="28" w="339" /><ent r="7" t="28" w="429" /></phr></stem><stem n="guarante"><phr n="guarantee"><ent r="7" t="6" w="330" /><ent r="7" t="28" w="341" /><ent r="7" t="28" w="431" /></phr></stem><stem n="better"><phr n="better"><ent r="7" t="6" w="348" /><ent r="7" t="6" w="353" /><ent r="3" t="8" w="39" /><ent r="3" t="10" w="50" /></phr></stem><stem n="doe"><phr n="does"><ent r="7" t="6" w="351" /></phr></stem><stem n="remain"><phr n="remains"><ent r="7" t="6" w="357" /><ent r="7" t="28" w="250" /></phr><phr n="remaining"><ent r="7" t="6" w="479" /><ent r="7" t="8" w="192" /><ent r="7" t="9" w="172" /><ent r="7" t="10" w="185" /><ent r="3" t="11" w="103" /><ent r="7" t="11" w="514" /><ent r="7" t="12" w="174" /><ent r="3" t="13" w="104" /><ent r="7" t="13" w="515" /><ent r="7" t="28" w="535" /><ent r="7" t="29" w="316" /><ent r="7" t="29" w="466" /></phr></stem><stem n="defin"><phr n="define"><ent r="7" t="6" w="362" /><ent r="3" t="10" w="41" /></phr><phr n="defined"><ent r="4096" t="30" w="3" /><ent r="4096" t="31" w="4" /></phr></stem><stem n="within"><phr n="within"><ent r="7" t="6" w="372" /><ent r="3" t="11" w="78" /><ent r="3" t="13" w="79" /><ent r="9" t="14" w="238" /><ent r="3" t="31" w="12" /><ent r="3" t="31" w="31" /></phr></stem><stem n="twap"><phr n="twap"><ent r="7" t="6" w="377" /><ent r="7" t="6" w="382" /><ent r="7" t="6" w="407" /><ent r="5" t="7" w="33" /><ent r="4" t="7" w="35" /><ent r="5" t="7" w="46" /><ent r="4" t="7" w="54" /><ent r="11" t="8" w="83" /><ent r="12" t="8" w="84" /><ent r="11" t="8" w="86" /><ent r="11" t="8" w="95" /><ent r="12" t="8" w="97" /><ent r="11" t="9" w="87" /><ent r="12" t="9" w="88" /><ent r="11" t="9" w="90" /><ent r="11" t="9" w="99" /><ent r="12" t="9" w="101" /><ent r="11" t="10" w="100" /><ent r="12" t="10" w="101" /><ent r="11" t="10" w="103" /><ent r="11" t="10" w="112" /><ent r="12" t="10" w="114" /><ent r="268435456" t="11" w="1" /><ent r="4096" t="11" w="4" /><ent r="1048578" t="11" w="7" /><ent r="3" t="11" w="35" /><ent r="3" t="11" w="97" /><ent r="3" t="11" w="117" /><ent r="3" t="11" w="128" /><ent r="4" t="11" w="133" /><ent r="11" t="11" w="166" /><ent r="12" t="11" w="167" /><ent r="11" t="11" w="169" /><ent r="11" t="11" w="178" /><ent r="12" t="11" w="180" /><ent r="7" t="11" w="242" /><ent r="7" t="11" w="284" /><ent r="7" t="11" w="307" /><ent r="11" t="12" w="89" /><ent r="12" t="12" w="90" /><ent r="11" t="12" w="92" /><ent r="11" t="12" w="101" /><ent r="12" t="12" w="103" /><ent r="268435456" t="13" w="2" /><ent r="4096" t="13" w="5" /><ent r="1048578" t="13" w="9" /><ent r="3" t="13" w="37" /><ent r="3" t="13" w="98" /><ent r="3" t="13" w="118" /><ent r="3" t="13" w="129" /><ent r="4" t="13" w="134" /><ent r="11" t="13" w="167" /><ent r="12" t="13" w="168" /><ent r="11" t="13" w="170" /><ent r="11" t="13" w="179" /><ent r="12" t="13" w="181" /><ent r="7" t="13" w="243" /><ent r="7" t="13" w="285" /><ent r="7" t="13" w="308" /><ent r="7" t="25" w="28" /><ent r="7" t="25" w="41" /><ent r="7" t="25" w="58" /><ent r="7" t="25" w="101" /><ent r="8" t="25" w="106" /><ent r="7" t="28" w="357" /><ent r="7" t="28" w="362" /><ent r="8" t="28" w="389" /><ent r="7" t="28" w="395" /><ent r="7" t="28" w="400" /><ent r="11" t="29" w="214" /><ent r="12" t="29" w="215" /><ent r="11" t="29" w="217" /><ent r="11" t="29" w="226" /><ent r="12" t="29" w="228" /><ent r="3" t="32" w="42" /><ent r="4" t="32" w="54" /></phr></stem><stem n="weight"><phr n="weighted"><ent r="7" t="6" w="379" /><ent r="3" t="11" w="32" /><ent r="3" t="13" w="34" /><ent r="7" t="28" w="359" /><ent r="7" t="28" w="397" /></phr></stem><stem n="averag"><phr n="average"><ent r="7" t="6" w="380" /><ent r="3" t="8" w="36" /><ent r="3" t="10" w="47" /><ent r="3" t="11" w="33" /><ent r="3" t="13" w="35" /><ent r="7" t="28" w="360" /><ent r="7" t="28" w="398" /></phr></stem><stem n="slice"><phr n="slicing"><ent r="7" t="6" w="385" /><ent r="3" t="11" w="38" /><ent r="7" t="11" w="253" /><ent r="3" t="13" w="40" /><ent r="7" t="13" w="254" /><ent r="7" t="28" w="365" /><ent r="7" t="28" w="403" /></phr><phr n="slices"><ent r="3" t="32" w="57" /></phr></stem><stem n="algo"><phr n="algo"><ent r="7" t="6" w="386" /><ent r="3" t="11" w="39" /><ent r="3" t="13" w="41" /><ent r="7" t="28" w="366" /><ent r="7" t="28" w="404" /><ent r="3" t="29" w="33" /></phr></stem><stem n="divid"><phr n="divides"><ent r="7" t="6" w="388" /><ent r="3" t="11" w="41" /><ent r="3" t="13" w="43" /><ent r="7" t="28" w="368" /><ent r="7" t="28" w="406" /></phr></stem><stem n="clip"><phr n="clips"><ent r="7" t="6" w="391" /><ent r="3" t="11" w="44" /><ent r="3" t="11" w="56" /><ent r="7" t="11" w="312" /><ent r="7" t="11" w="323" /><ent r="3" t="13" w="45" /><ent r="3" t="13" w="57" /><ent r="7" t="13" w="313" /><ent r="7" t="13" w="324" /><ent r="7" t="28" w="371" /><ent r="7" t="28" w="409" /><ent r="3" t="32" w="60" /><ent r="3" t="32" w="77" /></phr><phr n="clip"><ent r="7" t="6" w="393" /><ent r="3" t="11" w="46" /><ent r="3" t="11" w="54" /><ent r="3" t="11" w="66" /><ent r="3" t="11" w="89" /><ent r="7" t="11" w="252" /><ent r="7" t="11" w="287" /><ent r="7" t="11" w="345" /><ent r="7" t="11" w="351" /><ent r="8" t="11" w="355" /><ent r="7" t="11" w="360" /><ent r="7" t="11" w="372" /><ent r="7" t="11" w="381" /><ent r="7" t="11" w="392" /><ent r="7" t="11" w="400" /><ent r="7" t="11" w="403" /><ent r="7" t="11" w="416" /><ent r="7" t="11" w="421" /><ent r="7" t="11" w="427" /><ent r="7" t="11" w="434" /><ent r="7" t="11" w="438" /><ent r="8" t="11" w="440" /><ent r="7" t="11" w="445" /><ent r="7" t="11" w="465" /><ent r="7" t="11" w="473" /><ent r="7" t="11" w="476" /><ent r="3" t="13" w="47" /><ent r="3" t="13" w="55" /><ent r="3" t="13" w="67" /><ent r="3" t="13" w="90" /><ent r="7" t="13" w="253" /><ent r="7" t="13" w="288" /><ent r="7" t="13" w="346" /><ent r="7" t="13" w="352" /><ent r="8" t="13" w="356" /><ent r="7" t="13" w="361" /><ent r="7" t="13" w="373" /><ent r="7" t="13" w="382" /><ent r="7" t="13" w="393" /><ent r="7" t="13" w="401" /><ent r="7" t="13" w="404" /><ent r="7" t="13" w="417" /><ent r="7" t="13" w="422" /><ent r="7" t="13" w="428" /><ent r="7" t="13" w="435" /><ent r="7" t="13" w="439" /><ent r="8" t="13" w="441" /><ent r="7" t="13" w="446" /><ent r="7" t="13" w="466" /><ent r="7" t="13" w="474" /><ent r="7" t="13" w="477" /><ent r="7" t="28" w="373" /><ent r="7" t="28" w="411" /><ent r="3" t="32" w="62" /><ent r="3" t="32" w="75" /><ent r="3" t="32" w="78" /><ent r="3" t="32" w="92" /><ent r="4" t="33" w="63" /><ent r="4" t="33" w="70" /></phr></stem><stem n="interv"><phr n="intervals"><ent r="7" t="6" w="396" /><ent r="3" t="11" w="49" /><ent r="3" t="13" w="50" /><ent r="7" t="28" w="376" /><ent r="7" t="28" w="414" /><ent r="3" t="32" w="64" /></phr><phr n="interval"><ent r="3" t="11" w="55" /><ent r="3" t="11" w="59" /><ent r="7" t="11" w="435" /><ent r="7" t="11" w="439" /><ent r="8" t="11" w="441" /><ent r="7" t="11" w="444" /><ent r="8" t="11" w="458" /><ent r="7" t="11" w="466" /><ent r="7" t="11" w="474" /><ent r="7" t="11" w="477" /><ent r="3" t="13" w="56" /><ent r="3" t="13" w="60" /><ent r="7" t="13" w="436" /><ent r="7" t="13" w="440" /><ent r="8" t="13" w="442" /><ent r="7" t="13" w="445" /><ent r="8" t="13" w="459" /><ent r="7" t="13" w="467" /><ent r="7" t="13" w="475" /><ent r="7" t="13" w="478" /><ent r="3" t="32" w="76" /><ent r="3" t="32" w="80" /></phr></stem><stem n="plu"><phr n="plus"><ent r="7" t="6" w="408" /><ent r="4" t="7" w="36" /><ent r="11" t="8" w="87" /><ent r="11" t="9" w="91" /><ent r="11" t="10" w="104" /><ent r="11" t="11" w="170" /><ent r="11" t="12" w="93" /><ent r="11" t="13" w="171" /><ent r="7" t="25" w="29" /><ent r="11" t="29" w="218" /></phr></stem><stem n="possibl"><phr n="possible"><ent r="7" t="6" w="433" /><ent r="9" t="14" w="211" /><ent r="9" t="15" w="219" /><ent r="9" t="16" w="226" /><ent r="7" t="16" w="369" /><ent r="7" t="28" w="489" /></phr></stem><stem n="yet"><phr n="yet"><ent r="7" t="6" w="440" /><ent r="9" t="14" w="218" /><ent r="9" t="15" w="226" /><ent r="9" t="16" w="233" /><ent r="7" t="16" w="376" /><ent r="7" t="28" w="496" /></phr></stem><stem n="wait"><phr n="waiting"><ent r="7" t="6" w="451" /><ent r="9" t="14" w="226" /><ent r="9" t="15" w="235" /><ent r="9" t="16" w="244" /><ent r="7" t="16" w="387" /><ent r="7" t="28" w="507" /></phr></stem><stem n="verif"><phr n="verification"><ent r="7" t="6" w="453" /><ent r="9" t="14" w="228" /><ent r="9" t="15" w="237" /><ent r="9" t="16" w="246" /><ent r="7" t="16" w="389" /><ent r="7" t="28" w="509" /></phr></stem><stem n="partial"><phr n="partial"><ent r="7" t="6" w="454" /><ent r="7" t="28" w="510" /></phr></stem><stem n="depend"><phr n="depending"><ent r="7" t="6" w="455" /><ent r="3" t="7" w="14" /><ent r="7" t="8" w="69" /><ent r="7" t="8" w="167" /><ent r="7" t="9" w="73" /><ent r="7" t="10" w="86" /><ent r="7" t="11" w="152" /><ent r="7" t="12" w="75" /><ent r="7" t="13" w="153" /><ent r="7" t="28" w="511" /><ent r="7" t="29" w="200" /></phr><phr n="depend"><ent r="7" t="8" w="104" /><ent r="7" t="9" w="108" /><ent r="7" t="10" w="121" /><ent r="7" t="11" w="187" /><ent r="7" t="12" w="110" /><ent r="7" t="13" w="188" /><ent r="7" t="29" w="235" /></phr></stem><stem n="forc"><phr n="force"><ent r="7" t="6" w="459" /><ent r="7" t="25" w="370" /><ent r="7" t="25" w="425" /><ent r="7" t="28" w="515" /><ent r="8" t="29" w="285" /></phr></stem><stem n="multipl"><phr n="multiple"><ent r="7" t="6" w="462" /><ent r="4096" t="26" w="4" /><ent r="4096" t="26" w="6" /><ent r="7" t="28" w="518" /></phr></stem><stem n="shown"><phr n="shown"><ent r="7" t="6" w="465" /><ent r="7" t="8" w="213" /><ent r="7" t="9" w="193" /><ent r="7" t="10" w="212" /><ent r="7" t="11" w="543" /><ent r="7" t="12" w="195" /><ent r="7" t="13" w="544" /><ent r="7" t="28" w="521" /></phr></stem><stem n="expir"><phr n="expires"><ent r="7" t="6" w="469" /><ent r="7" t="6" w="472" /><ent r="7" t="8" w="189" /><ent r="7" t="9" w="169" /><ent r="7" t="10" w="182" /><ent r="3" t="11" w="99" /><ent r="7" t="11" w="505" /><ent r="7" t="11" w="522" /><ent r="7" t="12" w="171" /><ent r="3" t="13" w="100" /><ent r="7" t="13" w="506" /><ent r="7" t="13" w="523" /><ent r="7" t="28" w="525" /><ent r="7" t="28" w="528" /><ent r="7" t="29" w="313" /><ent r="7" t="29" w="457" /><ent r="7" t="29" w="474" /></phr><phr n="expired"><ent r="7" t="11" w="264" /><ent r="7" t="11" w="432" /><ent r="7" t="13" w="265" /><ent r="7" t="13" w="433" /><ent r="9" t="14" w="234" /><ent r="9" t="14" w="244" /></phr></stem><stem n="cancel"><phr n="cancelled"><ent r="7" t="6" w="470" /><ent r="7" t="6" w="478" /><ent r="3" t="9" w="40" /><ent r="3" t="10" w="56" /><ent r="7" t="11" w="493" /><ent r="3" t="12" w="42" /><ent r="7" t="13" w="494" /><ent r="7" t="28" w="526" /><ent r="7" t="28" w="534" /><ent r="7" t="29" w="297" /><ent r="7" t="29" w="445" /></phr><phr n="cancelling"><ent r="3" t="11" w="101" /><ent r="3" t="13" w="102" /></phr><phr n="cancel"><ent r="8" t="11" w="496" /><ent r="7" t="11" w="500" /><ent r="8" t="13" w="497" /><ent r="7" t="13" w="501" /><ent r="8" t="29" w="448" /><ent r="7" t="29" w="452" /></phr></stem><stem n="origin"><phr n="original"><ent r="7" t="6" w="476" /><ent r="9" t="16" w="174" /><ent r="7" t="16" w="317" /><ent r="7" t="28" w="532" /></phr></stem><stem n="balanc"><phr n="balance"><ent r="7" t="6" w="480" /><ent r="7" t="8" w="194" /><ent r="7" t="9" w="174" /><ent r="7" t="10" w="187" /><ent r="3" t="11" w="104" /><ent r="3" t="11" w="106" /><ent r="8" t="11" w="300" /><ent r="8" t="11" w="328" /><ent r="8" t="11" w="497" /><ent r="8" t="11" w="509" /><ent r="7" t="11" w="515" /><ent r="7" t="12" w="176" /><ent r="3" t="13" w="105" /><ent r="3" t="13" w="107" /><ent r="8" t="13" w="301" /><ent r="8" t="13" w="329" /><ent r="8" t="13" w="498" /><ent r="8" t="13" w="510" /><ent r="7" t="13" w="516" /><ent r="7" t="28" w="536" /><ent r="7" t="29" w="318" /><ent r="8" t="29" w="449" /><ent r="8" t="29" w="461" /><ent r="7" t="29" w="467" /><ent r="3" t="31" w="60" /></phr></stem><stem n="resubmiss"><phr n="resubmission"><ent r="7" t="6" w="482" /><ent r="7" t="28" w="538" /></phr></stem><stem n="entir"><phr n="entire"><ent r="7" t="6" w="486" /><ent r="3" t="11" w="86" /><ent r="3" t="13" w="87" /><ent r="9" t="14" w="230" /><ent r="9" t="15" w="239" /><ent r="9" t="16" w="248" /><ent r="7" t="16" w="391" /><ent r="7" t="28" w="245" /><ent r="7" t="28" w="542" /><ent r="7" t="29" w="373" /></phr></stem><stem n="permiss"><phr n="permissions"><ent r="3" t="7" w="15" /><ent r="7" t="8" w="106" /><ent r="7" t="9" w="110" /><ent r="7" t="10" w="123" /><ent r="7" t="11" w="189" /><ent r="7" t="12" w="112" /><ent r="7" t="13" w="190" /><ent r="7" t="29" w="237" /></phr></stem><stem n="pre"><phr n="pre"><ent r="4" t="7" w="40" /><ent r="7" t="25" w="31" /><ent r="7" t="25" w="43" /><ent r="7" t="28" w="332" /></phr></stem><stem n="c2c+wm"><phr n="c2c+wm"><ent r="5" t="7" w="42" /><ent r="5" t="7" w="45" /><ent r="11" t="8" w="89" /><ent r="12" t="8" w="91" /><ent r="11" t="8" w="94" /><ent r="12" t="8" w="96" /><ent r="11" t="9" w="93" /><ent r="12" t="9" w="95" /><ent r="11" t="9" w="98" /><ent r="12" t="9" w="100" /><ent r="11" t="10" w="106" /><ent r="12" t="10" w="108" /><ent r="11" t="10" w="111" /><ent r="12" t="10" w="113" /><ent r="11" t="11" w="172" /><ent r="12" t="11" w="174" /><ent r="11" t="11" w="177" /><ent r="12" t="11" w="179" /><ent r="268435456" t="12" w="1" /><ent r="1048578" t="12" w="8" /><ent r="11" t="12" w="95" /><ent r="12" t="12" w="97" /><ent r="11" t="12" w="100" /><ent r="12" t="12" w="102" /><ent r="268435456" t="13" w="1" /><ent r="1048578" t="13" w="8" /><ent r="11" t="13" w="173" /><ent r="12" t="13" w="175" /><ent r="11" t="13" w="178" /><ent r="12" t="13" w="180" /><ent r="7" t="25" w="37" /><ent r="7" t="25" w="40" /><ent r="7" t="28" w="334" /><ent r="8" t="28" w="353" /><ent r="7" t="28" w="356" /><ent r="8" t="28" w="388" /><ent r="11" t="29" w="220" /><ent r="12" t="29" w="222" /><ent r="11" t="29" w="225" /><ent r="12" t="29" w="227" /></phr></stem><stem n="baml"><phr n="baml"><ent r="4" t="7" w="48" /><ent r="11" t="8" w="99" /><ent r="11" t="9" w="103" /><ent r="11" t="10" w="116" /><ent r="11" t="11" w="182" /><ent r="11" t="12" w="105" /><ent r="11" t="13" w="183" /><ent r="7" t="25" w="47" /><ent r="7" t="28" w="391" /><ent r="11" t="29" w="230" /></phr></stem><stem n="wm"><phr n="wm"><ent r="4" t="7" w="49" /><ent r="7" t="25" w="48" /><ent r="7" t="25" w="56" /><ent r="7" t="28" w="392" /></phr></stem><stem n="post"><phr n="post"><ent r="4" t="7" w="52" /><ent r="7" t="25" w="50" /><ent r="7" t="28" w="393" /></phr></stem><stem n="wmtwap"><phr n="wmtwap"><ent r="4" t="7" w="56" /><ent r="7" t="25" w="55" /><ent r="7" t="28" w="442" /></phr></stem><stem n="wmmkt"><phr n="wmmkt"><ent r="4" t="7" w="57" /><ent r="7" t="28" w="443" /></phr></stem><stem n="achiev"><phr n="achieve"><ent r="3" t="8" w="35" /><ent r="3" t="10" w="46" /></phr></stem><stem n="equal"><phr n="equal"><ent r="3" t="8" w="40" /><ent r="3" t="10" w="51" /></phr></stem><stem n="compon"><phr n="components"><ent r="4" t="8" w="52" /><ent r="4" t="9" w="56" /><ent r="4" t="10" w="69" /><ent r="4" t="11" w="135" /><ent r="4" t="12" w="58" /><ent r="4" t="13" w="136" /><ent r="4" t="29" w="183" /></phr></stem><stem n="item"><phr n="item"><ent r="7" t="8" w="53" /><ent r="7" t="9" w="57" /><ent r="7" t="10" w="70" /><ent r="7" t="11" w="136" /><ent r="8" t="11" w="269" /><ent r="8" t="11" w="303" /><ent r="8" t="11" w="331" /><ent r="8" t="11" w="369" /><ent r="8" t="11" w="455" /><ent r="8" t="11" w="530" /><ent r="7" t="12" w="59" /><ent r="7" t="13" w="137" /><ent r="8" t="13" w="270" /><ent r="8" t="13" w="304" /><ent r="8" t="13" w="332" /><ent r="8" t="13" w="370" /><ent r="8" t="13" w="456" /><ent r="8" t="13" w="531" /><ent r="7" t="29" w="184" /></phr></stem><stem n="1"><phr n="1"><ent r="7" t="8" w="55" /><ent r="7" t="9" w="59" /><ent r="7" t="10" w="72" /><ent r="7" t="11" w="138" /><ent r="7" t="12" w="61" /><ent r="7" t="13" w="139" /><ent r="7" t="29" w="186" /></phr></stem><stem n="2"><phr n="2"><ent r="7" t="8" w="59" /><ent r="7" t="9" w="63" /><ent r="7" t="10" w="76" /><ent r="7" t="11" w="142" /><ent r="7" t="12" w="65" /><ent r="7" t="13" w="143" /><ent r="7" t="29" w="190" /></phr></stem><stem n="entri"><phr n="entry"><ent r="7" t="8" w="66" /><ent r="7" t="9" w="70" /><ent r="7" t="10" w="83" /><ent r="7" t="11" w="149" /><ent r="7" t="12" w="72" /><ent r="7" t="13" w="150" /><ent r="7" t="29" w="197" /><ent r="3" t="30" w="20" /></phr></stem><stem n="wl"><phr n="wl"><ent r="11" t="8" w="100" /><ent r="11" t="9" w="104" /><ent r="11" t="10" w="117" /><ent r="11" t="11" w="183" /><ent r="11" t="12" w="106" /><ent r="11" t="13" w="184" /><ent r="11" t="29" w="231" /></phr></stem><stem n="3"><phr n="3"><ent r="7" t="8" w="107" /><ent r="7" t="9" w="111" /><ent r="7" t="10" w="124" /><ent r="7" t="11" w="190" /><ent r="7" t="12" w="113" /><ent r="7" t="13" w="191" /><ent r="7" t="29" w="238" /></phr></stem><stem n="esp"><phr n="esp"><ent r="7" t="8" w="110" /><ent r="7" t="8" w="124" /><ent r="7" t="9" w="114" /><ent r="7" t="9" w="128" /><ent r="7" t="10" w="127" /><ent r="7" t="10" w="141" /><ent r="7" t="11" w="193" /><ent r="7" t="11" w="207" /><ent r="7" t="12" w="116" /><ent r="7" t="12" w="130" /><ent r="7" t="13" w="194" /><ent r="7" t="13" w="208" /><ent r="7" t="29" w="241" /><ent r="7" t="29" w="255" /></phr></stem><stem n="automat"><phr n="automatically"><ent r="7" t="8" w="116" /><ent r="7" t="8" w="130" /><ent r="7" t="9" w="120" /><ent r="7" t="9" w="134" /><ent r="7" t="10" w="133" /><ent r="7" t="10" w="147" /><ent r="7" t="11" w="199" /><ent r="7" t="11" w="213" /><ent r="7" t="12" w="122" /><ent r="7" t="12" w="136" /><ent r="7" t="13" w="200" /><ent r="7" t="13" w="214" /><ent r="3" t="26" w="33" /><ent r="3" t="29" w="46" /><ent r="7" t="29" w="247" /><ent r="7" t="29" w="261" /></phr></stem><stem n="stream"><phr n="streaming"><ent r="7" t="8" w="118" /><ent r="7" t="8" w="132" /><ent r="7" t="9" w="122" /><ent r="7" t="9" w="136" /><ent r="7" t="10" w="135" /><ent r="7" t="10" w="149" /><ent r="7" t="11" w="201" /><ent r="7" t="11" w="215" /><ent r="7" t="12" w="124" /><ent r="7" t="12" w="138" /><ent r="7" t="13" w="202" /><ent r="7" t="13" w="216" /><ent r="7" t="29" w="249" /><ent r="7" t="29" w="263" /></phr></stem><stem n="4"><phr n="4"><ent r="7" t="8" w="121" /><ent r="7" t="9" w="125" /><ent r="7" t="10" w="138" /><ent r="7" t="11" w="204" /><ent r="7" t="12" w="127" /><ent r="7" t="13" w="205" /><ent r="7" t="25" w="156" /><ent r="7" t="25" w="305" /><ent r="7" t="25" w="387" /><ent r="7" t="25" w="430" /><ent r="7" t="29" w="252" /><ent r="268435456" t="32" w="1" /></phr></stem><stem n="5"><phr n="5"><ent r="7" t="8" w="135" /><ent r="7" t="9" w="139" /><ent r="7" t="10" w="152" /><ent r="7" t="11" w="218" /><ent r="7" t="12" w="141" /><ent r="7" t="13" w="219" /><ent r="7" t="25" w="97" /><ent r="7" t="25" w="150" /><ent r="7" t="29" w="266" /><ent r="3" t="32" w="129" /></phr></stem><stem n="6"><phr n="6"><ent r="7" t="8" w="141" /><ent r="7" t="9" w="145" /><ent r="7" t="10" w="158" /><ent r="7" t="11" w="224" /><ent r="7" t="12" w="147" /><ent r="7" t="13" w="225" /><ent r="7" t="25" w="22" /><ent r="7" t="25" w="62" /><ent r="7" t="29" w="272" /></phr></stem><stem n="7"><phr n="7"><ent r="7" t="8" w="144" /><ent r="7" t="9" w="148" /><ent r="7" t="10" w="161" /><ent r="7" t="11" w="227" /><ent r="8" t="11" w="370" /><ent r="7" t="12" w="150" /><ent r="7" t="13" w="228" /><ent r="8" t="13" w="371" /><ent r="7" t="25" w="23" /><ent r="7" t="25" w="63" /><ent r="7" t="29" w="275" /></phr></stem><stem n="size"><phr n="size"><ent r="7" t="8" w="145" /><ent r="7" t="8" w="147" /><ent r="7" t="9" w="149" /><ent r="7" t="9" w="151" /><ent r="7" t="10" w="162" /><ent r="7" t="10" w="164" /><ent r="3" t="11" w="53" /><ent r="3" t="11" w="58" /><ent r="7" t="11" w="228" /><ent r="7" t="11" w="230" /><ent r="7" t="11" w="346" /><ent r="8" t="11" w="356" /><ent r="7" t="11" w="359" /><ent r="7" t="11" w="366" /><ent r="7" t="11" w="373" /><ent r="7" t="11" w="378" /><ent r="7" t="11" w="382" /><ent r="8" t="11" w="385" /><ent r="7" t="11" w="393" /><ent r="7" t="11" w="401" /><ent r="7" t="11" w="404" /><ent r="7" t="12" w="151" /><ent r="7" t="12" w="153" /><ent r="3" t="13" w="54" /><ent r="3" t="13" w="59" /><ent r="7" t="13" w="229" /><ent r="7" t="13" w="231" /><ent r="7" t="13" w="347" /><ent r="8" t="13" w="357" /><ent r="7" t="13" w="360" /><ent r="7" t="13" w="367" /><ent r="7" t="13" w="374" /><ent r="7" t="13" w="379" /><ent r="7" t="13" w="383" /><ent r="8" t="13" w="386" /><ent r="7" t="13" w="394" /><ent r="7" t="13" w="402" /><ent r="7" t="13" w="405" /><ent r="7" t="29" w="276" /><ent r="7" t="29" w="278" /><ent r="7" t="29" w="374" /><ent r="3" t="32" w="74" /><ent r="3" t="32" w="79" /><ent r="3" t="33" w="61" /><ent r="4" t="33" w="64" /><ent r="3" t="33" w="68" /><ent r="4" t="33" w="71" /></phr><phr n="sizes"><ent r="7" t="11" w="352" /><ent r="7" t="13" w="353" /></phr></stem><stem n="8"><phr n="8"><ent r="7" t="8" w="149" /><ent r="7" t="9" w="153" /><ent r="7" t="10" w="166" /><ent r="7" t="11" w="232" /><ent r="7" t="12" w="155" /><ent r="7" t="13" w="233" /><ent r="7" t="29" w="280" /></phr></stem><stem n="9"><phr n="9"><ent r="7" t="8" w="152" /><ent r="7" t="9" w="156" /><ent r="7" t="10" w="169" /><ent r="7" t="11" w="235" /><ent r="8" t="11" w="456" /><ent r="7" t="12" w="158" /><ent r="7" t="13" w="236" /><ent r="8" t="13" w="457" /><ent r="7" t="29" w="283" /></phr></stem><stem n="taken"><phr n="taken"><ent r="7" t="8" w="161" /><ent r="7" t="11" w="340" /><ent r="7" t="13" w="341" /><ent r="7" t="29" w="356" /></phr></stem><stem n="enter"><phr n="enter"><ent r="7" t="8" w="170" /><ent r="7" t="8" w="181" /><ent r="7" t="8" w="220" /><ent r="7" t="9" w="161" /><ent r="7" t="9" w="200" /><ent r="7" t="10" w="174" /><ent r="7" t="10" w="198" /><ent r="7" t="10" w="219" /><ent r="7" t="11" w="333" /><ent r="7" t="11" w="374" /><ent r="7" t="11" w="396" /><ent r="7" t="11" w="418" /><ent r="7" t="11" w="469" /><ent r="7" t="11" w="550" /><ent r="7" t="12" w="163" /><ent r="7" t="12" w="202" /><ent r="7" t="13" w="334" /><ent r="7" t="13" w="375" /><ent r="7" t="13" w="397" /><ent r="7" t="13" w="419" /><ent r="7" t="13" w="470" /><ent r="7" t="13" w="551" /><ent r="3" t="21" w="82" /><ent r="7" t="29" w="291" /><ent r="7" t="29" w="303" /><ent r="7" t="29" w="349" /><ent r="7" t="29" w="364" /><ent r="7" t="29" w="379" /><ent r="7" t="29" w="416" /><ent r="7" t="29" w="424" /><ent r="3" t="33" w="51" /><ent r="3" t="33" w="59" /><ent r="3" t="33" w="66" /></phr><phr n="entering"><ent r="7" t="8" w="209" /><ent r="7" t="9" w="189" /><ent r="7" t="10" w="208" /><ent r="7" t="11" w="539" /><ent r="7" t="12" w="191" /><ent r="7" t="13" w="540" /></phr></stem><stem n="arrow"><phr n="arrows"><ent r="7" t="8" w="173" /><ent r="7" t="11" w="335" /><ent r="7" t="13" w="336" /><ent r="7" t="29" w="351" /></phr></stem><stem n="adjust"><phr n="adjust"><ent r="7" t="8" w="174" /><ent r="7" t="11" w="336" /><ent r="7" t="13" w="337" /><ent r="7" t="29" w="352" /></phr><phr n="adjusts"><ent r="3" t="29" w="47" /></phr></stem><stem n="10"><phr n="10"><ent r="7" t="8" w="176" /><ent r="7" t="9" w="182" /><ent r="7" t="10" w="195" /><ent r="7" t="11" w="276" /><ent r="8" t="11" w="531" /><ent r="7" t="12" w="184" /><ent r="7" t="13" w="277" /><ent r="8" t="13" w="532" /><ent r="7" t="29" w="322" /></phr></stem></index>