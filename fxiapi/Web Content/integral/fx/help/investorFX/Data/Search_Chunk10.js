var xmlSearch_Chunk10Data = "";
xmlSearch_Chunk10Data += '<?xml version=\"1.0\" encoding=\"utf-8\"?><index><stem n=\"foreign\"><phr n=\"foreign\"><ent r=\"3\" t=\"31\" w=\"29\" /></phr></stem><stem n=\"branch\"><phr n=\"branches\"><ent r=\"3\" t=\"31\" w=\"30\" /></phr></stem><stem n=\"consid\"><phr n=\"considered\"><ent r=\"3\" t=\"31\" w=\"33\" /></phr></stem><stem n=\"call\"><phr n=\"called\"><ent r=\"3\" t=\"31\" w=\"40\" /><ent r=\"3\" t=\"31\" w=\"46\" /></phr></stem><stem n=\"bank\"><phr n=\"bank\"><ent r=\"3\" t=\"31\" w=\"42\" /></phr></stem><stem n=\"might\"><phr n=\"might\"><ent r=\"3\" t=\"31\" w=\"43\" /></phr></stem><stem n=\"integralni\"><phr n=\"integralny\"><ent r=\"3\" t=\"31\" w=\"47\" /></phr></stem><stem n=\"integrallon\"><phr n=\"integrallon\"><ent r=\"3\" t=\"31\" w=\"48\" /></phr></stem><stem n=\"forth\"><phr n=\"forth\"><ent r=\"3\" t=\"31\" w=\"50\" /></phr></stem><stem n=\"normal\"><phr n=\"normally\"><ent r=\"3\" t=\"31\" w=\"53\" /></phr></stem><stem n=\"regard\"><phr n=\"regarded\"><ent r=\"3\" t=\"31\" w=\"54\" /></phr></stem><stem n=\"have\"><phr n=\"having\"><ent r=\"3\" t=\"31\" w=\"55\" /></phr></stem><stem n=\"uniqu\"><phr n=\"unique\"><ent r=\"3\" t=\"31\" w=\"56\" /></phr></stem><stem n=\"produc\"><phr n=\"produce\"><ent r=\"3\" t=\"31\" w=\"59\" /></phr></stem><stem n=\"sheet\"><phr n=\"sheets\"><ent r=\"3\" t=\"31\" w=\"61\" /></phr></stem><stem n=\"report\"><phr n=\"report\"><ent r=\"3\" t=\"31\" w=\"62\" /></phr></stem><stem n=\"central\"><phr n=\"central\"><ent r=\"3\" t=\"31\" w=\"63\" /></phr></stem><stem n=\"author\"><phr n=\"authorities\"><ent r=\"3\" t=\"31\" w=\"64\" /></phr></stem><stem n=\"establish\"><phr n=\"established\"><ent r=\"3\" t=\"31\" w=\"67\" /></phr></stem><stem n=\"reflect\"><phr n=\"reflect\"><ent r=\"3\" t=\"32\" w=\"17\" /></phr></stem><stem n=\"previou\"><phr n=\"previous\"><ent r=\"3\" t=\"32\" w=\"21\" /></phr></stem><stem n=\"prior\"><phr n=\"prior\"><ent r=\"3\" t=\"32\" w=\"28\" /></phr></stem><stem n=\"smaller\"><phr n=\"smaller\"><ent r=\"3\" t=\"32\" w=\"59\" /></phr></stem><stem n=\"well\"><phr n=\"well\"><ent r=\"3\" t=\"32\" w=\"73\" /></phr></stem><stem n=\"rest\"><phr n=\"rests\"><ent r=\"3\" t=\"32\" w=\"101\" /></phr></stem><stem n=\"midrat\"><phr n=\"midrate\"><ent r=\"3\" t=\"32\" w=\"104\" /><ent r=\"3\" t=\"32\" w=\"126\" /></phr></stem><stem n=\"ocx\"><phr n=\"ocx\"><ent r=\"3\" t=\"32\" w=\"108\" /></phr></stem><stem n=\"opportun\"><phr n=\"opportunities\"><ent r=\"3\" t=\"32\" w=\"128\" /></phr></stem><stem n=\"combo\"><phr n=\"combo\"><ent r=\"3\" t=\"33\" w=\"57\" /></phr></stem><stem n=\"box\"><phr n=\"box\"><ent r=\"3\" t=\"33\" w=\"58\" /></phr></stem><stem n=\"min\"><phr n=\"min\"><ent r=\"4\" t=\"33\" w=\"62\" /></phr></stem><stem n=\"max\"><phr n=\"max\"><ent r=\"4\" t=\"33\" w=\"69\" /></phr></stem></index>';
CMCXmlParser._FilePathToXmlStringMap.Add('Search_Chunk10', xmlSearch_Chunk10Data);
