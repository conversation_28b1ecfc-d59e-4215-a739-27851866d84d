if(!Array.prototype.indexOf){Array.prototype.indexOf=function(c,d){var b;if(this==null){throw new TypeError('"this" is null or not defined')}var e=Object(this);var a=e.length>>>0;if(a===0){return -1}var f=+d||0;if(Math.abs(f)===Infinity){f=0}if(f>=a){return -1}b=Math.max(f>=0?f:a-Math.abs(f),0);while(b<a){if(b in e&&e[b]===c){return b}b++}return -1}}if(typeof String.prototype.startsWith!="function"){String.prototype.startsWith=function(a){return this.indexOf(a)==0}}if(typeof String.prototype.endsWith!=="function"){String.prototype.endsWith=function(a){return this.indexOf(a,this.length-a.length)!==-1}}if(typeof integral=="undefined"){var integral={}}integral=(function(au){var H=null;var D=false;var V;var aw;var A;var B;var E;var U;var aB;var ay;var m;var c;var am;var an;var W;var aE;var ak;var ab;var S;var az={};var u="";var Y="";var q=0;var av=0;var r=0;var P;var ah="";var v="";var G="";var y=new Array();var b;var ax;var h;var o=2000;var aq=0;var ae="";var aC=0;var i=false;var M=false;var X=null;var ad=false;var t;var a;var ac;var e=0;var j=0;var ao=0;var J=false;var ag=new Array();var x=new Array();var ai=0;var ar=false;var aA=false;var R={};var aj=false;var k;var w=0;var Q;var N;var n;var g=0;var K={O:0,T:0,B:0};var af=false;var p=false;var I;function al(){var aG=new Date().getTime();try{if(!aj){if(!J&&(aG-q)>N){aD()}if(i&&((aG-av)>n)){d()}}else{if((aG-w)>10000){l()}}if((aG-e)>b){aa()}if(((aG-ai)>integral.ui.getOrderSyncInterval())&&integral.ui.isOrderSyncEnabled()){ai=aG;integral.ui.reconnectedNetwork()}}catch(aH){}setTimeout(al,200)}function aD(){var aG=au.readCookie("LOGIN_TIME");if(G!=aG){var aH="getMessages:Redirecting to login page as the server login time is not same as client login time";integral.log(aH,au.consts.LOG_ERROR);C(aH);return}if(!aj){if(J){z()}else{ap()}}}function aF(){integral.log("Login to Unity Server.");Q=io.connect("/");integral.setUnity(Q);var aG={userName:u,userOrg:Y,authToken:au.readCookie("SSO_TOKEN")};Q.emit("authrequest",aG);Q.on("authresponse",function(aH){if(aH=="SUCCESS"){integral.log("Unity server authrequest is successful.");af=true;if(!p&&(integral.ui.enableTickers()||integral.ui.isFXInsideRT())){integral.tickers.startupTicker()}}else{integral.log("Unity server authrequest failed.")}});Q.on("invalidtoken",function(){integral.log("Invalid token received by Unity server.");integral.ui.logout("Logging out as invalid token received by Unity server.")});Q.on("unity_error",function(){integral.log("Unity error occurred");integral.ui.logout("Logging out as there was an error on the Unity server")});Q.on("disconnect",function(){if(!ad){integral.log("Socket info: disconnect event received when user still logged in.");integral.ui.logout("Logging out as disconnect event was received from Unity server")}});Q.on("reconnecting",function(){integral.log("Socket info: reconnecting.")});Q.on("connect_error",function(){integral.log("Socket error: connect_failed.");integral.ui.logout("Logging out as connecting to Unity server failed ")});Q.on("error",function(aH){integral.log("Socket error: "+aH);integral.ui.logout("Logging out as there was an error in Unity server")});Q.on("reconnect_failed",function(){integral.log("Socket error: reconnect_failed.");integral.ui.logout("Logging out as there was an error in re-connecting to Unity server")});Q.on("invalidparams",function(){integral.log("Invalid login parameters received by Unity server.");integral.ui.logout("Logging out as invalid login parameters received by Unity server. User: "+u+". Org: "+Y)});Q.on("fxBenchmarkSubscribeResponse",function(){integral.log("fxBenchmarkSubscribeResponse received by Unity server.")});Q.on("fxBenchmarkUnSubscribeResponse",function(){integral.log("fxBenchmarkSubscribeResponse received by Unity server.")});Q.on("tickerSubscribeResponse",function(){integral.log("tickerSubscribeResponse received by Unity server.")});Q.on("tickerUnSubscribeResponse",function(){integral.log("tickerSubscribeResponse received by Unity server.")});Q.on("message",function(aI){try{integral.p(JSON.parse(aI),"socket")}catch(aH){integral.log("Error in parsing the message from socket server "+aI)}});Q.on("deal",function(aJ){try{var aH=aJ.split("\u0001");integral.p(JSON.parse(aH[1]),"socket");if(af){Q.emit("ackDeal",aH[0])}}catch(aI){integral.log("Error in parsing the message from socket server "+aJ)}});Q.on("ackDeal",function(aJ){try{var aH=aJ.split("\u0001");integral.p(JSON.parse(aH[1]),"socket");if(af){Q.emit("ackDeal",aH[0])}}catch(aI){integral.log("Error in parsing the message from socket server "+aJ)}});Q.on("testresponse",function(aI){if(aI!=undefined){var aH=new Date().getTime()-aI.time;y[y.length]=(aH>0?aH:0)}})}function O(aH){var aG={userName:u,userOrg:Y,authToken:au.readCookie("SSO_TOKEN")};af=false;Q.emit("logout",aG,aH)}function d(){var aG=au.readCookie("LOGIN_TIME");if(G!=aG){var aH="getSecondaryMessages:Redirecting to login page as the server login time is not same as client login time";integral.log(aH,au.consts.LOG_ERROR);C(aH);return}f()}function z(){jQuery.support.cors=true;var aG=window.location.href;var aI=aG.split(":");var aH="";if(aG.indexOf("8080")!=-1){aH=aI[0]+":"+aI[1]+":8878/jmsproxy?cmd=U&f=4&ts="+new Date().getTime()}else{aH="/jmsproxy?cmd=U&f=4&ts="+new Date().getTime()}au("#streamingframe").attr("src",aH)}function ap(){var aI=new Date().getTime();if(t!=undefined&&t.readyState!=4){if((aI-q)<3000){return}else{t.abort()}}jQuery.support.cors=true;var aL=window.location.href;var aN=aL.split(":");var aM="";var aK="H"+(j++);if(j>=100000){j=0}if(ag.length>0){var aH="";for(var aG=0;aG<ag.length;aG++){if(aH.length>0){aH+=","}aH+=ag[aG]}ag=new Array()}if(aL.indexOf("8080")!=-1){aM=aN[0]+":"+aN[1]+":8878/jmsproxy?cmd=P&f=4&wMs=1000&ts="+aI+"&lmc="+r+"&un="+ae+"&reqId="+aK+"&ackIdList="+(aH!=undefined?aH:"")}else{aM="/jmsproxy?cmd=P&f=4&wMs=1000&ts="+aI+"&lmc="+r+"&un="+ae+"&reqId="+aK+"&ackIdList="+(aH!=undefined?aH:"")}r=0;var aJ;q=aI;t=au.ajax({url:aM,type:"GET",dataType:"json",cache:false,timeout:3000,reqId:aK,contentType:"application/json; charset=UTF-8",success:function(aQ){r=aQ.length;var aP=new Date().getTime()-this.sentTime;var aO=integral.p(aQ);aP=aP-(aO>0?aO:0);y[y.length]=(aP>0?aP:0);Z();if(ah=="broken"){ah="resumed";integral.ui.reconnectedNetwork();integral.ui.replaceSystemAlert(integral.ui.getLabel(au.consts.CONNECTION_RESUMED))}},beforeSend:function(){this.sentTime=new Date().getTime()},error:function(aQ,aO,aP){integral.log("Error occured in Primary polling request : "+aK+" : "+aQ.responseText+", Status = "+aQ.status+" .reason:"+aO+" .error:"+aP+". sent time : "+this.sentTime+". time taken:"+(new Date().getTime()-this.sentTime));if(aO=="timeout"){aD();return}else{if(aO=="abort"){return}}if((aQ.responseText!=null)){if((aQ.responseText.indexOf("Not Logged in")!=-1||aQ.responseText.indexOf("Error=401")!=-1)){var aR="getPollingMessages:Redirecting to login page, Error: "+aQ.responseText+", Status = "+aQ.status;integral.log(aR,au.consts.LOG_ERROR);C(aR)}}if(aQ.statusText=="error"){if(!ad){ah="broken";if(aQ.responseText!=null&&aQ.responseText.indexOf("503 Service Temporarily Unavailable")!=-1){integral.ui.replaceSystemAlert(integral.ui.getLabel("service_down"))}else{integral.ui.replaceSystemAlert(integral.ui.getLabel("lost_network_connection"))}}}}});if(!i&&au.readCookie("jsmssid")!=undefined&&au.readCookie("jsmssid")!=""){integral.enableSecondaryServer()}}function at(){var aG=new Date().getTime();integral.log("Sending renew token request. ts="+aG);if(ac!=undefined&&ac.readyState!=4){ac.abort();integral.log("Renew token request already active ")}ac=au.ajax({url:'/fxi/fxiapi/sso/token/renew?un="'+ae+'"&ts="'+aG+'"',type:"GET",dataType:"json",cache:false,timeout:5000,contentType:"application/json; charset=UTF-8",success:function(aH){integral.log("Renew Token Successful.")},error:function(aJ,aH,aI){integral.log("Error occured in Renew Token request :"+aJ.responseText+", Status"+aJ.status+" reason:"+aH+" .error:"+aI);if(aJ.status==0&&aH!="abort"){at()}}})}function f(){var aI=new Date().getTime();if(a!=undefined&&a.readyState!=4){if((aI-av)<5000){return}else{a.abort()}}jQuery.support.cors=true;var aL=window.location.href;var aN=aL.split(":");var aM="";var aK="H"+(ao++);if(ao>=100000){ao=0}if(x.length>0){var aH="";for(var aG=0;aG<x.length;aG++){if(aH.length>0){aH+=","}aH+=x[aG]}x=new Array()}if(aL.indexOf("8080")!=-1){aM=aN[0]+":"+aN[1]+":8878/ss/jmsproxy?cmd=P&f=4&wMs=1000&ts="+aI+"&lmc="+r+"&un="+ae+"&reqId="+aK+"&ackIdList="+(aH!=undefined?aH:"")}else{aM="/ss/jmsproxy?cmd=P&f=4&wMs=1000&ts="+aI+"&lmc="+r+"&un="+ae+"&reqId="+aK+"&ackIdList="+(aH!=undefined?aH:"")}r=0;var aJ;av=aI;a=au.ajax({url:aM,type:"GET",dataType:"json",cache:false,timeout:5000,reqId:aK,contentType:"application/json; charset=UTF-8",sentTime:aI,success:function(aO){integral.p(aO,"secondary");if(v=="broken"){v="resumed";integral.log("Connection to Secondary Server is resumed")}aC=0},error:function(aQ,aO,aP){integral.log("Error occured in Secondary polling request : "+aK+" : "+aQ.responseText+", Status = "+aQ.status+" failed :"+(aC+1)+" times. reason:"+aO+" .error:"+aP+". sent time : "+this.sentTime+". time taken:"+(new Date().getTime()-this.sentTime));if(aO=="timeout"){d();return}else{if(aO=="abort"){return}}aC++;if((aQ.responseText!=null)){integral.log("Secondary Server Polling Failed, reason:"+aQ.status);if(aQ.status!=404&&aQ.status!=401){if(aC<=3){setTimeout(d,50)}}}if(aQ.statusText=="error"){v="broken";if(aQ.responseText!=null&&aQ.responseText.indexOf("503 Service Temporarily Unavailable")!=-1){integral.log("Secondary Server Polling Failed, Reason: Service down errorcode"+aQ.responseText)}else{integral.log("Secondary Server Polling Failed, ,Reason:  Lost network connection"+aQ)}}}})}function F(aG){if(s(aG)=="T"){if(!integral.ui.isTraderView()){integral.ui.enableOrgViewDropdown()}}if(s(aG)=="O"){if(!integral.ui.isOrderTraderView()){integral.ui.enableOrderOrgViewDropdown()}}if(s(aG)=="B"){if(!integral.ui.isOrderTraderView()){integral.ui.enableOrderOrgViewDropdown()}}}function L(aI,aG){for(var aH=0;aH<aG.length;aH++){if(aI==aG[aH].key){return aG[aH].value}}return null}function l(){w=new Date().getTime();Q.emit("testrequest",{time:w})}function aa(){e=new Date().getTime();try{var aL="",aP="",aO=0,aN="";var aG=au.extend(new Array(),y);y=new Array();for(var aI=0;aI<aG.length;aI++){var aH=aG[aI];aL=(aL==""||aL>aH)?aH:aL;aP=(aP==""||aP<aH)?aH:aP;aO=aO+aH}if(aO!=0){aN=Math.round(aO/aG.length)}var aJ="/fxi/fxiapi/heartbeat?l="+aL+"&a="+aN+"&h="+aP+"&c="+aG.length+"&un="+ae;au.ajax({url:aJ,type:"GET",dataType:"json",cache:false,timeout:h,contentType:"application/json; charset=UTF-8",success:function(aS){if(aS&&aS.responseTuples){var aR=aS.responseTuples;if(L("SSO_TOKEN_RENEW",aR)!=undefined){at()}X=L("serverUTCTime",aR);if(X!=undefined){integral.ui.setServerTime(X)}}if(aj&&ah=="broken"){ah="resumed";if(!ad){integral.log("Socket info: Network reconnected. Attempting re-auth with node.");Q=null;aF()}integral.ui.reconnectedNetwork();integral.ui.replaceSystemAlert(integral.ui.getLabel(au.consts.CONNECTION_RESUMED))}Z()},error:function(aT,aR,aS){integral.log("Error occured in heartbeat :"+aT.responseText+", Status"+aT.status+" failed : "+(aq+1)+" times. reason "+aR+". err:"+aS);aq++;if(aR!="timeout"&&aj){ah="broken";if(aj){O("Heartbeat with OA failed")}if(aT.responseText!=null&&aT.responseText.indexOf("503 Service Temporarily Unavailable")!=-1){integral.ui.replaceSystemAlert(integral.ui.getLabel("service_down"))}else{integral.ui.replaceSystemAlert(integral.ui.getLabel("lost_network_connection"))}}}});if(!aj&&i){var aK="/fxi/fxiapi/ss/heartbeat?l="+aL+"&a="+aN+"&h="+aP+"&c="+aG.length+"&un="+ae;au.ajax({url:aK,type:"GET",dataType:"json",cache:false,timeout:h,contentType:"application/json; charset=UTF-8",success:function(aS){if(av>0&&((new Date().getTime()-av)>b)){if(au("#fxticker").length>=1){integral.log("Secondary service is restored. trying reconnection");var aR=integral.tickers.getTickerCurrencyPair();integral.tickers.subscribeTicker(aR)}}},error:function(aT,aR,aS){integral.log("Error occured in Secondary heartbeat :"+aT.responseText+", Status"+aT.status+". reason "+aR+". err:"+aS)}})}if(q>0&&((new Date().getTime()-q)>b)){integral.log("Primary polling is restored.")}if(ax!=undefined&&aq>=ax){integral.log("Logging out as heartbeat to server failed for "+ax+" times.");integral.ui.logout("Logging out as heartbeat to server failed for "+ax+" times.");return}}catch(aM){integral.log("Javascript exception in heartbeat workflow "+aM)}if(integral.ui.isAutoLogoutEnabled()){var aQ=integral.ui.getLastActivityTime();if((new Date().getTime()-aQ)>integral.ui.getAutoLogoutInactivitySec()*1000&&au('[id="logoutDialog"]').length<=0){integral.ui.displayLogoutDialog()}}}function C(aG){if(aj){O(aG)}au("#fxiprime").data("loggedin",false);location.href=P}function Z(){aq=0}function T(){for(var aG in R){A.call(this,R[aG])}R={}}function s(aG){if(K.O==aG){return"O"}else{if(K.T==aG){return"T"}else{if(K.B==aG){return"B"}}}return null}return{setCookie:function(aH,aK,aI,aJ){var aL="";if(aI){var aG=new Date();aG.setTime(aG.getTime()+(365*24*60*60*1000));aL="expires="+aG.toUTCString()}if(aJ){document.cookie=aH+"="+aK+";path=/;;domain="+aJ+";"+aL}else{document.cookie=aH+"="+aK+";path=/;"+aL}},removeCookie:function(aG){au.setCookie(aG,"",{path:"/",duration:-1})},setLoginTime:function(aG){G=aG},getConnectionStatus:function(aG){return ah},getReferenceData:function(aG){return az[aG]},getUserName:function(){return u},getUserOrg:function(){return Y},loginUnityServer:function(){aj=true;aF()},disableUnityServer:function(){au.ajax({url:"/fxi/fxiapi/unity/disableWSStreaming",type:"POST",dataType:"json",cache:false,async:false,timeout:5000,contentType:"application/json; charset=UTF-8",success:function(aG){integral.log("Client switched over to jmsproxy polling")},error:function(aI,aG,aH){integral.log("Error switching to jmsproxy :"+aI.responseText+", Status"+aI.status+" reason:"+aG+" .error:"+aH)}});aj=false},isUnityEnabled:function(){return aj},setUnityFlag:function(aG){aj=aG},setPrimaryPollingInterval:function(aG){N=aG},setSecondaryPollingInterval:function(aG){n=aG},setUnity:function(aG){k=aG},getUnity:function(){return k},setServerUpdateCallbacks:function(aJ,aG,aL,aH,aK,aI){if(A==undefined){A=aJ}if(E==undefined){E=aG}if(V==undefined){V=aL}if(am==undefined){am=aH}if(ay==undefined){ay=aK}if(aE==undefined){aE=aI}},handleServerError:function(aJ,aI){if(aI.status==401){var aH="handleServerError:Redirecting to login page as an error occured : "+aJ+", Status :"+aI.status;integral.log(aH,au.consts.LOG_ERROR);C(aH)}else{if(aI.status==400||aI.status==403){var aG=au.parseJSON(aI.responseText);if(aG.status!=undefined){S.call(aI,aJ+". Status="+aG.status+" Message="+aG.errorCode)}}else{if(aI.status=="ERROR"){S.call(aI,aJ+". Status="+aI.status+" Message="+aI.errorCode)}else{if(aI.status!=undefined){S.call(aI,aJ+". Status="+aI.status+" Message="+aI.statusText)}}}}},tryExternalSSOLogin:function(aN){var aH=au.cookie("EXTERNAL_SSO_AUTH");if(aH){try{if(aH=="LoggedIn@"){integral.log("ExternalSSO : Duplicate Login received or user refreshed the browser.");aM()}else{var aI=aH.split("@");integral.log("Attempt to initialize dealing service by external SSO");integral.initDealingService(aI[0],"",aI[1],aN,aL);integral.removeCookie("EXTERNAL_SSO_AUTH");var aK=au.cookie("SSO_DOMAIN");integral.setCookie("EXTERNAL_SSO_AUTH","LoggedIn@",null,aK)}}catch(aJ){aG("Login to dealing Service Failed")}return true}return false;function aG(aO){var aP=au.cookie("SAML_BRAND");if(aP){window.location.href="sso/external/error/"+aP+"?error="+aO}else{window.location.href="sso/external/error?error="+aO}}function aM(){var aO=au.cookie("SAML_BRAND");if(aO){window.location.href="sso/external/logout/"+aO}else{window.location.href="sso/external/logout"}}function aL(aP){if(aP.status==401||aP.status==500){window.location.href="sso/external/error?error=User Not authorized To Access the App"}else{if(aP.status=="OK"){var aO=au.parseJSON(this.data);au.props={redirectUrl:"",brandedPath:""};au.props.user=aO.user;au.props.org=aO.org;if(aP.legalAgreementStatus==false){integral.ui.showLicenseAgreement(aP)}else{aP.brandFor=aN;aP.externalSSO=true;integral.log("External SSO.dealingServiceCallBack:Loading main panel.");loadMainPanel(aP)}}}}},login2fa:function(aH,aG,aN,aK,aJ,aM){var aI="username="+aH+"&password="+aG+"&org="+aN;integral.removeCookie("JSESSIONID");integral.removeCookie("primJSESSIONID");integral.removeCookie("secJSESSIONID");integral.removeCookie("AUTH_TOKEN");integral.removeCookie("userInfo");var aL={user:aH,fixedFormatting:false,org:aN,apiVersion:au.consts.API_VERSION,channel:"FXI_HTML",clientType:"WEBCLIENT",clientName:"HTML",clientVersion:"HTML",loginParams:{brandVersion:"HTML",brand:(aM==""?"FXI":aM)}};if(aK!=""){aL.otp=aK}if(aG!=""){aL.pass=aG}au.ajax({url:"/fxi/fxiapi/auth/login",type:"POST",data:JSON.stringify(aL),dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",processData:false,async:false,beforeSend:function(aO){if(aO&&aO.overrideMimeType){aO.overrideMimeType("application/json;charset=UTF-8")}},success:function(aO){b=parseInt(aO.additionalInfo.heartBeatInterval);ax=parseInt(aO.additionalInfo.missedHeartbeatsRetries);h=parseInt(aO.additionalInfo.heartbeatRequestTimeout);if(b==null||b<o){b=o}J=aO.asynChannel=="stream";integral.setUserInfo(aH,aN);au(window).attr("name","fromlogin_"+aM);G=au.readCookie("LOGIN_TIME");if(typeof aJ=="function"){aJ.call(this,aO)}integral.log("login call Success")},error:function(aO){if(typeof aJ=="function"){aJ.call(this,aO)}}})},loginSSO:function(aH,aG,aN,aK,aJ,aM){integral.removeCookies();var aI="username="+aH+"&password="+aG+"&org="+aN;var aL;aL={user:aH,org:aN,apiVersion:au.consts.API_VERSION};if(aK!=""){aL.otp=aK}if(aG!=""){aL.pass=aG}au.ajax({url:"/fxi/fxiapi/sso/login",type:"POST",data:JSON.stringify(aL),dataType:"json",contentType:"application/json; charset=UTF-8",processData:false,cache:false,async:false,beforeSend:function(aO){if(aO&&aO.overrideMimeType){aO.overrideMimeType("application/json;charset=UTF-8")}},success:function(aO){if(typeof aJ=="function"){aO.brandedPath=aM;aJ.call(this,aO)}au(window).attr("ssologin",true);integral.log("loginSSO call Success")},error:function(aO){if(typeof aJ=="function"){aJ.call(this,aO)}au(window).attr("ssologin",true)}})},initDealingService:function(aH,aG,aL,aK,aI){integral.log("Calling initDealingService");var aJ={user:aH,org:aL,fixedFormatting:false,channel:"FXI_HTML",clientType:"WEBCLIENT",clientName:"HTML",apiVersion:au.consts.API_VERSION,clientVersion:"HTML",loginParams:{brandVersion:"HTML",brand:(aK==""?"FXI":aK)}};aJ.loginParams.webSocketEnabled=(integral.ui.getProperty("websocket",false)=="enabled"&&!integral.ui.isBrowserIE())?true:false;au.ajax({url:"/fxi/fxiapi/dealingService/initialize",type:"POST",data:JSON.stringify(aJ),dataType:"json",contentType:"application/json; charset=UTF-8",processData:false,async:false,cache:false,beforeSend:function(aM){if(aM&&aM.overrideMimeType){aM.overrideMimeType("application/json;charset=UTF-8")}},success:function(aM){b=parseInt(aM.additionalInfo.heartBeatInterval);ax=parseInt(aM.additionalInfo.missedHeartbeatsRetries);h=parseInt(aM.additionalInfo.heartbeatRequestTimeout);if(b==null||b<o){b=o}J=aM.asynChannel=="stream";integral.setUserInfo(aH,aL);au(window).attr("name","fromlogin_"+aK);G=au.readCookie("LOGIN_TIME");if(typeof aI=="function"){aI.call(this,aM)}integral.log("initDealingService call Success")},error:function(aM){if(typeof aI=="function"){aI.call(this,aM)}}})},initSecondaryService:function(){integral.log("Calling secondaryService initialize");au.ajax({url:"/fxi/fxiapi/secondaryService/initialize",type:"POST",dataType:"json",contentType:"application/json; charset=UTF-8",processData:false,async:false,cache:false,beforeSend:function(aG){if(aG&&aG.overrideMimeType){aG.overrideMimeType("application/json;charset=UTF-8")}},success:function(aG){integral.log("SecondaryService initialize call Success")},error:function(aG){integral.log("SecondaryService initialize call failed")}})},getHistoricalData:function(aG,aI){if(!aI.autoUpdate){integral.log("User updated the historical chart. Instrument="+aI.instrument+"&Period="+aI.period)}var aH;au.ajax({url:"/fxi/fxiapi/historicaldata/quoteHistory?instrument="+aI.instrument+"&period="+aI.period+"&count=200&auto="+(aI.autoUpdate?"true":"false"),type:"GET",beforeSend:function(aJ){if(aJ&&aJ.overrideMimeType){aJ.overrideMimeType("application/json;charset=UTF-8")}},params:aI,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",async:true,success:function(aJ){if(aJ.status!="ERROR"){aH=aJ.records;aG.call(this,aH,aI)}else{integral.log("Error in getting historical data");aJ.systemError=true;integral.handleServerError("Error in getting historical data",aJ)}},error:function(aJ){aJ.systemError=true;integral.log("Error in getting historical data");integral.handleServerError("Error in getting historical data",aJ)}})},setCookieForRedirection:function(aG){integral.removeCookie("orgServiceGroup");au.ajax({url:"/fxi/fxiapi/auth/redirect/"+aG,type:"GET",cache:false,async:false,contentType:"application/json; charset=UTF-8",processData:false,beforeSend:function(aH){if(aH&&aH.overrideMimeType){aH.overrideMimeType("application/json;charset=UTF-8")}},success:function(aH){},error:function(aH){}})},logout:function(aH,aI){var aG="/fxi/fxiapi/sso/logout";au.ajax({type:"POST",url:"/fxi/fxiapi/dealingService/terminate",dataType:"json",async:false,cache:false,processData:false});au.ajax({type:"POST",url:aG,dataType:"json",data:JSON.stringify({reason:aI}),async:false,cache:false,processData:false,success:function(aJ){ad=true;if(aj){O(aI)}integral.removeCookies();integral.removeCookiesOnLogout();if(typeof aH=="function"){aH.call(this,aJ)}},error:function(aJ){integral.removeCookies();integral.removeCookiesOnLogout();if(typeof aH=="function"){aH.call(this,aJ)}}})},removeCookies:function(){integral.removeCookie("MDFServerEnabled");integral.removeCookie("AUTH_TOKEN");integral.removeCookie("SSO_TOKEN");integral.removeCookie("jmspsid");integral.removeCookie("jsmssid");integral.removeCookie("JSESSIONID");integral.removeCookie("JMS_PROXY_HOST_PORT");integral.removeCookie("primJSESSIONID");integral.removeCookie("secJSESSIONID");integral.removeCookie("userInfo");integral.removeCookie("hbInterval");integral.removeCookie("misshbretries");integral.removeCookie("hbReqTimeout");integral.removeCookie("orgServiceGroup");integral.removeCookie("businessDate");integral.removeCookie("metURL")},removeCookiesOnLogout:function(){var aH=document.cookie.split(/=[^;]*(?:;\s*|$)/);for(var aG=0;aG<aH.length;aG++){if(au.trim(aH[aG]).indexOf("SSO_Sticky_Session")===0){integral.removeCookie(aH[aG])}}},forgotPassword:function(aG,aJ,aI){var aH=JSON.stringify({user:aG,org:aJ,passwordAnswer:"N/A"});au.ajax({url:"/fxi/fxiapi/auth/forgotPassword",type:"POST",cache:false,data:aH,dataType:"json",contentType:"application/json; charset=UTF-8",processData:false,beforeSend:function(aK){if(aK&&aK.overrideMimeType){aK.overrideMimeType("application/json;charset=UTF-8")}},success:function(aK){if(typeof aI=="function"){aI.call(this,aK)}},error:function(aK){if(typeof aI=="function"){aI.call(this,aK)}}})},resetPassword:function(aI,aK,aH){var aG=JSON.stringify({oldPassword:aI,newPassword:aK});var aJ;au.ajax({url:"/fxi/fxiapi/auth/resetPassword",type:"POST",data:aG,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",processData:false,async:false,beforeSend:function(aL){if(aL&&aL.overrideMimeType){aL.overrideMimeType("application/json;charset=UTF-8")}},success:function(aL){aJ=aL},error:function(aL){aJ=aL}});return aJ},getLegalAgreement:function(){var aG;au.ajax({type:"GET",url:"/fxi/fxiapi/auth/legalAgreement",async:false,cache:false,dataType:"json",success:function(aH){aG=aH},error:function(aH){aH.systemError=true;integral.handleServerError("getLegalAgreement",aH)}});return aG},acceptLegalAgreement:function(aG){au.ajax({type:"GET",url:"/fxi/fxiapi/auth/legalAgreement/accept",async:false,cache:false,dataType:"json",success:function(aH){aG.call()},error:function(aH){aH.systemError=true;integral.handleServerError("acceptLegalAgreement",aH)}})},getPermissions:function(aH){var aG;au.ajax({type:"GET",url:"/fxi/fxiapi/auth/permissions",async:false,cache:false,dataType:"json",success:function(aI){aG=aI},error:function(aI){aI.systemError=true;integral.handleServerError("getPermissions",aI)}});return aG},getRiskNetReferenceData:function(){var aG=null;au.ajax({type:"GET",url:"/fxi/fxiapi/refdata/rexrefdata",async:false,cache:false,dataType:"json",success:function(aH){aG=aH},error:function(aH){aH.systemError=true;integral.handleServerError("getRiskNetRferenceData",aH)}});return aG},getSupportedCurrencyPairs:function(){var aG=new Array();var aH="{ ";au.ajax({type:"GET",url:"/fxi/fxiapi/refdata/supportedCcypairs",async:false,cache:false,dataType:"json",success:function(aL){var aK=aL;for(var aJ=0,aI=0;aJ<aK.length;aJ++){if(aK[aJ]==null||aK[aJ]==undefined){integral.log("Supported Currency pair received is :"+aK[aJ]);continue}aG[aI]=aK[aJ].instrument;az[aG[aI]]=aK[aJ];aI++;aH+=aK[aJ].instrument+":"+aK[aJ].spotValueDate+", "}aH+=" }";integral.log("Spot Values Dates : "+aH)},error:function(aI){aI.systemError=true;integral.handleServerError("getSupportedCurrencyPairs",aI)}});return aG},getRTProviders:function(aG){var aH=[];au.ajax({type:"GET",url:"/fxi/fxiapi/refdata/rtAggregationProviders",async:false,cache:false,dataType:"json",success:function(aI){aH=aI},error:function(aI){aI.systemError=true;integral.handleServerError("Error in rtAggregationProviders "+aG,aI)}});return aH},getSupportedCcys:function(){var aG=new Array();au.ajax({type:"GET",url:"/fxi/fxiapi/refdata/supportedCcys",async:false,cache:false,dataType:"json",success:function(aH){aG=aH},error:function(aH){aH.systemError=true;integral.handleServerError("getSupportedCurrencyPairs",aH)}});return aG},getVehicleCcys:function(){var aG=new Array();au.ajax({type:"GET",url:"/fxi/fxiapi/refdata/vehicleCcys",async:false,cache:false,dataType:"json",success:function(aH){aG=aH},error:function(aH){aH.systemError=true;integral.handleServerError("getVehicleCcys",aH)}});return aG},getDefaultTenorsList:function(){var aG=new Array();au.ajax({type:"GET",url:"/fxi/fxiapi/rfs/supportedTenors",async:false,cache:false,dataType:"json",success:function(aH){if(aH==null||aH.length==0){aG=["JAN","FEB","MAR","APR","MAY","JUN","JUL","AUG","SEP","OCT","NOV","DEC","TOM","TOD","ON","TN","SN","TM"]}else{aG=aH}},error:function(aH){aG=["JAN","FEB","MAR","APR","MAY","JUN","JUL","AUG","SEP","OCT","NOV","DEC","TOM","TOD","ON","TN","SN","TM"];aH.systemError=true;integral.handleServerError("getDefaultTenorsList",aH)}});return aG},getProviderStreams:function(aG){var aH={};au.ajax({type:"GET",url:"/fxi/fxiapi/refdata/providers/anonymous/"+aG,async:false,cache:false,dataType:"json",success:function(aI){aH=aI},error:function(aI){aI.systemError=true;integral.handleServerError("getProviderStreams "+aG,aI)}});return aH.sort()},subscribeRFS:function(aJ,aS,aO,aQ,aG,aM,aY,aP,aH,aN,a1,aT,aW,aR,aV,aU,aL,a0){a0=a0!=undefined?a0:0;if(c==undefined){c=aJ}if(am==undefined){am=aO}var aK={instrument:aG,amount:aM,farDealtAmount:aH,dealtInstrument:aY,farValueDate:aN,channel:a1,sideType:parseInt(aT),priceType:parseInt(aW),expiryInSecs:aV,stmtInst:aL};if(integral.ui.isSalesDealerTradingEnabled()){var aZ=aQ.find("#salesdealerorgs").val();var aX=aQ.find("#salesdealerles").val();aK.customerOrg=aZ;aK.customerAccount=aX}if(aU){aK.rfsPriceViewType=1;aK.apiVersion=1.1;if(aQ.data("panelType")=="fb"){aK.depth=0}else{aK.depth=1}}if(aR=="fixingdate"){aK.fixingDate=aP}else{aK.nearValueDate=aP}var aI=JSON.stringify(aK);au.ajax({url:"/fxi/fxiapi/rfs/subscribe",type:"POST",beforeSend:function(a2){if(a2&&a2.overrideMimeType){a2.overrideMimeType("application/json;charset=UTF-8")}},data:aI,dataType:"json",async:true,cache:false,contentType:"application/json; charset=UTF-8",success:function(a2){if(a2.status!="ERROR"){integral.log("Subscribed RFS "+aG+" "+aM+", client expiry "+aV+", requestId "+a2.requestId);if(D==undefined||!D){au('<iframe id="streamingframe" width="0" height="0" frameborder="0" ></iframe>').appendTo("body");aD();D=true}c.call(a2,aQ);aS.call(a2,true,aQ)}else{aS.call(a2,false,aQ);a2.systemError=true;integral.handleServerError("Subscribe RFS "+aG,a2)}},error:function(a2){if(a0==3){a2.systemError=true;integral.handleServerError("Error in RFS "+aG,a2);aS.call(a2,false,aQ)}else{integral.log("Retrying RFS Subscription "+aI+" Error:"+a2.status+", Count:"+(a0+1));integral.subscribeRFS(aJ,aS,aO,aQ,aG,aM,aY,aP,aH,aN,a1,aT,aW,aR,aV,aU,aL,a0+1)}}})},withdrawRFS:function(aI,aG,aH){au.ajax({url:"/fxi/fxiapi/rfs/withdraw/"+aH,type:"GET",beforeSend:function(aJ){if(aJ&&aJ.overrideMimeType){aJ.overrideMimeType("application/json;charset=UTF-8")}},dataType:"json",async:true,cache:false,contentType:"application/json; charset=UTF-8",success:function(aJ){if(aJ.status!="ERROR"){aI.call(aJ,aG)}else{aJ.systemError=true;integral.handleServerError("Withdraw RFS "+aH,aJ)}},error:function(aJ){aJ.systemError=true;integral.handleServerError("Withdraw RFS "+aH,aJ)}})},subscribeBatch:function(aK,aH,aL){if(aH.length==0){return}aL=aL!=undefined?aL:0;if(V==undefined){V=aK}var aJ=JSON.stringify({requestList:aH});var aG="{";for(var aI=0;aI<aH.length;aI++){aG+="[ Currency :"+aH[aI].instrument+", Dealt CCy :"+aH[aI].dealtInstrument+", Market Depth :"+aH[aI].depth+", amount :"+aH[aI].sizes+", Request Type :"+aH[aI].reqType+", View Type :"+aH[aI].view+", Key :"+aH[aI].reqID+"],"}aG+="}.";integral.log("Sending batch Subscription request for "+aG);au.ajax({url:"/fxi/fxiapi/marketdata/subscribeBatch",type:"POST",beforeSend:function(aM){if(aM&&aM.overrideMimeType){aM.overrideMimeType("application/json;charset=UTF-8")}},data:aJ,dataType:"json",contentType:"application/json; charset=UTF-8",cache:false,success:function(aT){if(aT.status!="ERROR"){var aX=aT.ccyPairRefDataList;var aQ="";if(aX!=null){for(var aU=0;aU<aX.length;aU++){if(aX[aU]==undefined||aX[aU]==null){integral.log("Reference data recieved is :"+aX[aU]);continue}az[aX[aU].instrument]=aX[aU];if(integral.getReferenceData(aX[aU].instrument).spotValueDate!=aX[aU].spotValueDate){aQ+=aX[aU].instrument+" set to :"+aX[aU].spotValueDate+", "}}if(aQ!=""){integral.log("In SubscribeBatch, Spot value date for "+aQ)}}}else{var aZ="";var aR="";var aW,aN,aY,aO;var aV=integral.ui.getKeyMap();var aS={};for(var aP in aV){aS[aV[aP]]=aP}if(aT.instrumentResponseDescriptorList!=null){var aM=aT.instrumentResponseDescriptorList;for(var aU=0;aU<aM.length;aU++){if(aM[aU].status=="ERROR"){aO=aS[aM[aU].id];if(aO){aW=aO.substr(0,3)+"/"+aO.substr(3,3);aN=(aO.substr(6,aO.length-8));aY=aO.charAt(aO.length-2)=="B"?aW.substr(0,3):aW.substr(4,3);aZ+=aZ.indexOf(aW)==-1?((aU!=0?",":"")+aW):"";aR+=aM[aU].errorCode+":"+aW+":"+aN+":"+aY+","}else{aR+=aM[aU].id+":"+aM[aU].errorCode+", "}}}aS=null;aV=null;if(integral.ui.isFXInsideRT()){aT.errorCode="FMA Subscription Failed : "+aZ}else{aT.errorCode="Subscription Failed : "+aZ}}else{aR=aT.errorCode}integral.log("Error in SubscribeBatch :"+aR);aT.systemError=true;if(aR.indexOf("chart")===-1){integral.handleServerError("SubscribeBatch ",aT)}}if(D==undefined||!D){au('<iframe id="streamingframe" width="0" height="0" frameborder="0" ></iframe>').appendTo("body");aD();D=true}},error:function(aM){if(aL==3){aM.systemError=true;if(requestType==1){integral.handleServerError("Error in getting rates for board ",aM)}else{integral.log("Could not unsubscribe board "+currencyPairArr)}}else{integral.log("Retrying Batch "+(requestType==1?"Subscription ":"Unsubscription ")+" "+aJ+" Error:"+aM.status+", Count:"+(aL+1));integral.subscribeBatch(aK,aH,aL+1)}}})},subscribe:function(aJ,aK,aO,aM,aI,aS,aQ,aT,aR){aR=aR!=undefined?aR:0;if(V==undefined){V=aJ}var aP=1000;if(integral.ui.isMetal(aK)){aP=1}var aH={reqID:aT,view:aQ,reqType:aO,depth:aI,updType:0,org:Y,entryType:2,instrument:aK,dealtInstrument:aS,minSize:aP};if(aM!=""&&aM!=null&&aM!=undefined){aH.sizes=[aM];aH.requestedSize=aM}var aG=aO==1?"Subscription":"Un-subscription";var aN=JSON.stringify(aH);var aL="[Currency :"+aK+", Dealt CCy :"+aS+", amount :"+aM+", Request Type :"+aO+", Market Depth :"+aI+", View Type :"+aQ+", Key :"+aT+"]";integral.log("Sending "+aG+" for "+aL);au.ajax({url:"/fxi/fxiapi/marketdata/subscribe",type:"POST",beforeSend:function(aU){if(aU&&aU.overrideMimeType){aU.overrideMimeType("application/json;charset=UTF-8")}},data:aN,dataType:"json",requestType:aO,cache:false,contentType:"application/json; charset=UTF-8",success:function(aW){var aX=false;var aV=" ";for(var aU=0;aU<aW.length;aU++){if(aW[aU].status!="ERROR"){integral.log(aG+" Successful "+aK+" "+aM+" "+((aS==undefined)?"TOB":aS)+", Market Depth :"+aI+", View Type :"+aQ+", Key :"+aT);if(aW[aU].status=="OK"&&aW[aU].refData!=null){az[aK]=aW[aU].refData;if(integral.getReferenceData(aK).spotValueDate!=aW[aU].refData.spotValueDate){integral.log("In Subscribe, Spot value date for "+aK+" set to :"+aW[aU].refData.spotValueDate)}aX=true}}else{aV+=aW[aU].errorCode+"|"}}if(!aX){if(aO==1&&aV.indexOf("SUBSCRIPTION_EXISTS")==-1){aW.errorCode=" Subscription Failed : "+aK;aW.status="ERROR";aW.systemError=true;if(aT.indexOf("_chart")==-1){integral.handleServerError("Subscribe ",aW)}}}if(aV!=" "){integral.log("Error in "+aG+": "+aT+":"+aK+":"+aM+":"+((aS==undefined)?"TOB":aS)+" "+aV)}if(D==undefined||!D){au('<iframe id="streamingframe" width="0" height="0" frameborder="0" ></iframe>').appendTo("body");aD();D=true}},error:function(aU){if(aR==3){aU.systemError=true;if(aO==1){integral.handleServerError("Error in getting rates for "+aK,aU)}else{integral.log("Could not unsubscribe "+aK+" "+aM)}}else{integral.log("Retrying "+aG+" "+aN+" Error:"+aU.status+", Count:"+(aR+1));integral.subscribe(aJ,aK,aO,aM,aI,aS,aQ,aT,aR+1)}}})},subscribeBenchmarkRates:function(aL,aJ){var aI=aJ.currencyPair;var aG=aJ.clientReferenceId;if(aj){if(integral.getUnity()!=undefined){integral.subscribeUnityBenchmarkRates(aL,aI)}return}var aH={};aH.requestList=[{clientReferenceId:aG+"_"+aI,currencyPair:aI}];aH.ps="FXB";var aK=JSON.stringify(aH);au.ajax({url:"/fxi/fxiapi/fxbenchmark/subscribe",type:"POST",beforeSend:function(aM){if(aM&&aM.overrideMimeType){aM.overrideMimeType("application/json;charset=UTF-8")}},data:aK,provider:aJ.provider,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(aM){if(!i&&au.readCookie("jsmssid")!=undefined&&au.readCookie("jsmssid")!=""){integral.enableSecondaryServer()}aL.call(this,aM)},error:function(aM){}})},subscribeUnityBenchmarkRates:function(aH,aG){var aI={userName:integral.getUserName(),userOrg:integral.getUserOrg(),requestList:[{currencyPair:aG,clientReferenceId:"html"}],sendLatestSnapshot:true};integral.getUnity().emit("fxBenchmarkSubscribe",aI)},unsubscribeUnityBenchmarkRates:function(aH,aG){var aI={userName:integral.getUserName(),userOrg:integral.getUserOrg(),requestList:[{currencyPair:aG,clientReferenceId:"html"}]};integral.getUnity().emit("fxBenchmarkUnSubscribe",aI)},unsubscribeBenchmarkRates:function(aL,aJ){var aI=aJ.subscriptionRequestId;if(aj){if(integral.getUnity()!=undefined){integral.unsubscribeUnityBenchmarkRates(aL,aJ)}return}var aG=aJ.clientReferenceId;var aH={};aH.subscriptionRequestIdList=[aI];aH.clientReferenceId=aG;aH.ps="FXB";var aK=JSON.stringify(aH);au.ajax({url:"/fxi/fxiapi/fxbenchmark/unsubscribe",type:"POST",beforeSend:function(aM){if(aM&&aM.overrideMimeType){aM.overrideMimeType("application/json;charset=UTF-8")}},data:aK,provider:aJ.provider,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(aM){aL.call(this,aM)},error:function(aM){}})},subscribeRT:function(aK,aJ,aS){var aL=aJ.currencyPair;var aP=aJ.requestType;var aN=aJ.amount;var aI=aJ.marketDepth;var aU=aJ.dealtCcy;var aR=aJ.view;var aT=aJ.key;aS=aS!=undefined?aS:0;if(V==undefined){V=aK}var aQ=1000;if(integral.ui.isMetal(aL)){aQ=1}var aH={reqID:aT,view:aR,reqType:aP,depth:aI,updType:0,org:Y,entryType:2,instrument:aL,dealtInstrument:aU,minSize:aQ};if(aN!=""&&aN!=null&&aN!=undefined){aH.sizes=[aN];aH.requestedSize=aN}if(aJ.provider!=null){aH.providers=[aJ.provider]}var aG=aP==1?"Subscription":"Un-subscription";var aO=JSON.stringify(aH);var aM="[Currency :"+aL+", Dealt CCy :"+aU+", amount :"+aN+", Request Type :"+aP+", Market Depth :"+aI+", View Type :"+aR+", Key :"+aT+"]";integral.log("Sending "+aG+" for "+aM);au.ajax({url:"/fxi/fxiapi/marketdata/subscribe",type:"POST",beforeSend:function(aV){if(aV&&aV.overrideMimeType){aV.overrideMimeType("application/json;charset=UTF-8")}},data:aO,provider:aJ.provider,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(aX){var aY=false;var aW=" ";for(var aV=0;aV<aX.length;aV++){if(aX[aV].status!="ERROR"){integral.log(aG+" Successful "+aL+" "+aN+" "+((aU==undefined)?"TOB":aU)+", Market Depth :"+aI+", View Type :"+aR+", Key :"+aT);if(aX[aV].status=="OK"&&aX[aV].refData!=null){az[aL]=aX[aV].refData;if(integral.getReferenceData(aL).spotValueDate!=aX[aV].refData.spotValueDate){integral.log("In RT Subscribe, Spot value date for "+aL+" set to :"+aX[aV].refData.spotValueDate)}aY=true}}else{aW+=aX[aV].errorCode+"|"}}if(!aY){if(aP==1&&aW.indexOf("SUBSCRIPTION_EXISTS")==-1){aX.errorCode=this.provider+" Subscription Failed : "+aL;aX.status="ERROR";aX.systemError=true;if(aT.indexOf("_chart")==-1){integral.handleServerError("Subscribe ",aX)}}}if(aW!=" "){integral.log("Error in "+aG+": "+aT+":"+aL+":"+aN+":"+((aU==undefined)?"TOB":aU)+" "+aW)}if(D==undefined||!D){au('<iframe id="streamingframe" width="0" height="0" frameborder="0" ></iframe>').appendTo("body");aD();D=true}},error:function(aV){if(aS==3){aV.systemError=true;if(aP==1){integral.handleServerError("Error in getting rates for "+aL,aV)}else{integral.log("Could not unsubscribe RT "+aL+" "+aN)}}else{integral.log("Retrying "+aG+" "+aO+" Error:"+aV.status+", Count:"+(aS+1));integral.subscribeRT(aK,aJ,aS+1)}}})},subscribeOrderNotifications:function(aG){aG=aG!=undefined?aG:0;au.ajax({url:"/fxi/fxiapi/notification/order/subscribe",type:"POST",beforeSend:function(aH){if(aH&&aH.overrideMimeType){aH.overrideMimeType("application/json;charset=UTF-8")}},dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(aH){integral.log("Order Notifications Subscription successful")},error:function(aH){if(aG==3){integral.handleServerError("Error in subscribing order notification",aH)}else{integral.log("Retrying Order notification Subscription. Error:"+aH.status+", Count:"+(aG+1));integral.subscribeOrderNotifications(aG+1)}}})},subscribeChiefDealerNotifications:function(aG){aG=aG!=undefined?aG:0;au.ajax({url:"/fxi/fxiapi/chiefdealer/notifications/subscribe",type:"POST",beforeSend:function(aH){if(aH&&aH.overrideMimeType){aH.overrideMimeType("application/json;charset=UTF-8")}},dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",async:false,timeout:2000,success:function(aH){integral.log("chiefdealer Notifications Subscription successful")},error:function(aH){if(aG==3){integral.handleServerError("Error in subscribing chiefdealer notification",aH)}else{integral.log("Retrying chiefdealer notification Subscription. Error:"+aH.status+", Count:"+(aG+1));integral.subscribeOrderNotifications(aG+1)}}})},unsubscribeChiefDealerNotifications:function(aG){au.ajax({url:"/fxi/fxiapi/chiefdealer/notifications/unsubscribe",type:"POST",beforeSend:function(aH){if(aH&&aH.overrideMimeType){aH.overrideMimeType("application/json;charset=UTF-8")}},dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(aH){integral.log("chiefdealer Notifications Un-Subscription successful")},error:function(aH){if(aG){integral.log("Error in un-subscribing chiefdealer notification")}else{integral.handleServerError("Error in un-subscribing chiefdealer notification",aH)}}})},subscribeOrderBook:function(aG){aG=aG!=undefined?aG:0;au.ajax({url:"/fxi/fxiapi/orderbook/view/subscribe",type:"POST",beforeSend:function(aH){if(aH&&aH.overrideMimeType){aH.overrideMimeType("application/json;charset=UTF-8")}},dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(aH){integral.log("Order book Subscription successful")},error:function(aH){aH.systemError=true;if(aG==3){integral.handleServerError("Error in subscribing order book",aH)}else{integral.log("Retrying Order book Subscription. Error:"+aH.status+", Count:"+(aG+1));integral.subscribeOrderBook(aG+1)}}})},unsubscribeOrderBook:function(aG){au.ajax({url:"/fxi/fxiapi/orderbook/view/unsubscribe",type:"POST",beforeSend:function(aH){if(aH&&aH.overrideMimeType){aH.overrideMimeType("application/json;charset=UTF-8")}},dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(aH){integral.log("Order book Un-Subscription successful")},error:function(aH){if(aG){integral.log("Error in unsubscribe order book")}else{aH.systemError=true;integral.handleServerError("Error in unsubscribe order book",aH)}}})},subscribeTicker:function(aI,aG,aJ){if(aj){if(af){integral.subscribeUnityTicker(aI,aG,aJ)}return}aJ=aJ!=undefined?aJ:0;if(aB==undefined){aB=aI}var aH=JSON.stringify({requestList:[{currencyPair:aG,clientReferenceId:"html"}],sendLatestSnapshot:true});au.ajax({url:"/fxi/fxiapi/trade/ticker/subscribe",type:"POST",beforeSend:function(aK){if(aK&&aK.overrideMimeType){aK.overrideMimeType("application/json;charset=UTF-8")}},data:aH,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(aK){aB.call(this,aK,"snapshot");if(!i&&au.readCookie("jsmssid")!=undefined&&au.readCookie("jsmssid")!=""){integral.enableSecondaryServer()}},error:function(aK){aK.systemError=true;if(aJ==3){integral.handleServerError("Error in subscribing ticker "+aG,aK)}else{integral.log("Retrying Ticker Subscription "+aH+" Error:"+aK.status+", Count:"+(aJ+1));integral.subscribeTicker(aI,aG,aJ+1)}}})},subscribeUnityTicker:function(aH,aG,aJ){aJ=aJ!=undefined?aJ:0;if(aB==undefined){aB=aH}var aI={userName:integral.getUserName(),userOrg:integral.getUserOrg(),requestList:[{currencyPair:aG,clientReferenceId:"html"}],sendLatestSnapshot:true};integral.getUnity().emit("tickerSubscribe",aI);p=true},unsubscribeUnityTicker:function(aH,aG,aJ){if(integral.getUnity()!=undefined){var aI={userName:integral.getUserName(),userOrg:integral.getUserOrg(),requestList:[{currencyPair:aG,clientReferenceId:"html"}]};integral.getUnity().emit("tickerUnSubscribe",aI)}},unsubscribeTicker:function(aI,aJ,aG){if(aj){integral.unsubscribeUnityTicker(aI,aJ,aG);return}var aH=JSON.stringify({subscriptionRequestIdList:[aJ]});au.ajax({url:"/fxi/fxiapi/trade/ticker/unsubscribe",type:"POST",beforeSend:function(aK){if(aK&&aK.overrideMimeType){aK.overrideMimeType("application/json;charset=UTF-8")}},data:aH,ccyPair:aG,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(aK){aI.call(aK,this.ccyPair)},error:function(aK){}})},subscribePositions:function(aJ,aK){aK=aK!=undefined?aK:0;if(E==undefined){E=aJ}var aI=integral.ui.getUserSettings().getPnlCurrency();var aH=["Instrument","ValueDate"];if(integral.ui.isSalesDealerTradingEnabled()){aH.push("CustomerOrg")}var aG=JSON.stringify({clientReferenceId:"html",aggregateView:true,customerId:u,customerOrg:Y,fromValueDate:integral.ui.getBusinessDate(),pnlCurrency:aI,pnlUpdateIntervalInMills:5000,positionType:"All",groupByColumns:aH});au.ajax({url:"/fxi/fxiapi/position/subscribe",type:"POST",beforeSend:function(aL){if(aL&&aL.overrideMimeType){aL.overrideMimeType("application/json;charset=UTF-8")}},data:aG,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(aL){if(D==undefined||!D){au('<iframe id="streamingframe" width="0" height="0" frameborder="0" ></iframe>').appendTo("body");aD();D=true}},error:function(aL){aL.systemError=true;if(aK==3){integral.handleServerError("Error in getting positions",aL)}else{integral.log("Retrying Position Subscription "+aG+" Error:"+aL.status+", Count:"+(aK+1));integral.subscribePositions(aJ,aK+1)}}})},subscribeNewPositions:function(aH,aN){var aL=u;var aM;aN=aN!=undefined?aN:0;if(E==undefined){E=aH}var aO=integral.ui.getUserSettings().getPnlCurrency();var aK=false;var aG=integral.ui.getPosGroupByValue();var aI=integral.ui.isPosTraderView()?"USER":integral.ui.getPositionLevelMap(aG);if((aI==="ORG"||aI==="LE")&&integral.ui.isSalesDealerTradingEnabled()){aK=integral.ui.getICPMap(aG)}if(!integral.ui.isPosTraderView()&&aI==="USER"){aL="*"}aM=aI==="LE"?"*":"";var aJ=JSON.stringify({clientReferenceId:"html",aggregateView:true,customerId:aL,customerOrg:Y,customerAccount:aM,fromValueDate:integral.ui.getBusinessDate(),pnlCurrency:aO,pnlUpdateIntervalInMills:5000,positionType:"All",positionLevel:aI,inclCustomerPositions:aK});au.ajax({url:"/fxi/fxiapi/position/subscribe",type:"POST",beforeSend:function(aP){if(aP&&aP.overrideMimeType){aP.overrideMimeType("application/json;charset=UTF-8")}},data:aJ,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(aP){if(integral.ui.isPosHierarchyViewEnabled()){integral.ui.setposRequestId(aP.positionRequestId)}if(D==undefined||!D){au('<iframe id="streamingframe" width="0" height="0" frameborder="0" ></iframe>').appendTo("body");aD();D=true}},error:function(aP){aP.systemError=true;if(aN==3){integral.handleServerError("Error in getting positions",aP)}else{integral.log("Retrying Position Subscription "+aJ+" Error:"+aP.status+", Count:"+(aN+1));integral.subscribeNewPositions(aH,aN+1)}}})},unsubscribePositions:function(aG){au.ajax({url:"/fxi/fxiapi/position/unsubscribe",type:"GET",beforeSend:function(aH){if(aH&&aH.overrideMimeType){aH.overrideMimeType("application/json;charset=UTF-8")}},cache:false,dataType:"json",async:false,contentType:"application/json; charset=UTF-8",success:function(aH){if(aG){aG.call()}},error:function(){}})},getOpenPositions:function(){var aG;au.ajax({url:"/fxi/fxiapi/position/q?customer="+u+"&groupBy=CP",type:"GET",beforeSend:function(aH){if(aH&&aH.overrideMimeType){aH.overrideMimeType("application/json;charset=UTF-8")}},async:false,cache:false,contentType:"application/json; charset=UTF-8",success:function(aH){aG=aH},error:function(aH){aH.systemError=true;integral.handleServerError("getOpenPositions",aH)}});return aG},p:function(aV,a4){if(J&&(a4==null)){q=new Date().getTime()}var aI=null;var aK=null;var aW;var aQ;var a2;var aR;var aU;var aS;var aO={};var aG=0;for(var aX in aV){try{var aJ=aV[aX];if(aJ[0]==="WT"){aG=parseInt(aJ[1]);continue}if(aJ[0]=="TH"){var aN=aJ[1];if(aN!=null&&aN.data!=null){if(aN.data.indexOf("<event>LOGOUT</event>")!=-1){var aY="Logout message received from the server :"+aN.data;integral.log(aY);C(aY);break}}continue}if(aJ[1]=="hb"){continue}if(aJ[0]=="MDSF"){var a1=aJ[1].split("|");var aP;if(a1.length>=4){aO.slMsgCcy=a1[3].replace("[","");aO.slReqId=a1[0].replace("[","");aO.slTenor=a1[4];aP=integral.ui.convertSLPointsFormat(aO.slMsgCcy,a1[5],a1[6].replace("]]",""));aO.slBidPoint=aP.formattedBidPoint;aO.slOfferPoint=aP.formattedOfferPoint;if(aO.slReqId==integral.ui.getslReqId(aO.slMsgCcy)){integral.ui.setslDataObj(aO.slMsgCcy,aO.slTenor,aO.slBidPoint,aO.slOfferPoint);integral.ui.updateSwapLadder(aO)}}continue}if(aJ[1]=="sos"){continue}if(aJ[1]=="eos"){aD();continue}if(aJ[0]=="OM"){var aT=au.parseJSON(aJ[1]);if(aJ[2]=="1"){if(a4==null){ag.push(aJ[3])}else{if(a4=="secondary"){x.push(aJ[3])}}}if(aI==null){aI="Orders received - "}if(aT.acceptedTrade!=null){if(aK==null){aK="Trades Done - "}aK=aK+aT.acceptedTrade.orderId+":"+aT.acceptedTrade.tradeId+","}aI=aI+aT.orderId+":"+aT.orderStatus+",";if(ar&&aA){A.call(this,aT)}else{R[aJ[3]]=aT}continue}if(aJ[0]=="PM"){var aL=au.parseJSON(aJ[1]);if(integral.ui.isPosHierarchyViewEnabled()){if(aL.positionRequestId===integral.ui.getposRequestId()){E.call(this,aL,false)}else{integral.log("Droped PM.Req ID-"+aL.positionRequestId+" not matching with Latest Req ID-"+integral.ui.getposRequestId())}}else{E.call(this,aL,false)}continue}if(aJ[0]=="RM"){var a3=aJ[1];V.call(this,a3);continue}if(aJ[0]=="NM"){var a3=au.parseJSON(aJ[1]);if(aJ[2]=="1"){if(a4==null){ag.push(aJ[3])}else{if(a4=="secondary"){x.push(aJ[3])}}}ay.call(this,a3);continue}if(aJ[0]=="RFSE"||aJ[0]=="RFST"||aJ[0]=="RFSR"||aJ[0]=="RFSF"||aJ[0]=="PORTFOLIOR"){if(aJ[0]=="RFST"&&!integral.ui.isTraderView()){continue}var aM=aJ[1];am.call(this,aM,aJ[0]);continue}if(aJ[0]==="TT"){aB.call(this,aJ[1],"trade")}if(aJ[0]==="OBV"){integral.ui.handleOrderBookUpdate(aJ[1])}if(aJ[0]==="OSNM"){var aH=au.parseJSON(aJ[1]);aH.event="OrderNotification";ay.call(this,aH)}if(aJ[0]==="NH"&&(!integral.ui.isTraderView()||!integral.ui.isOrderTraderView())){if(aJ[2]=="1"){if(a4==null){ag.push(aJ[3])}else{if(a4=="secondary"){x.push(aJ[3])}}}if(aR==null){aR=new Array()}aR.push(aJ[1])}if(aJ[0]==="FXB"){var a0=au.parseJSON(aJ[1]);V.call(this,a0,aJ[0])}if((aJ[0]==="CDQ")){if(aJ[2]=="1"){if(a4==null){ag.push(aJ[3])}else{if(a4=="secondary"){x.push(aJ[3])}}}var aT=au.parseJSON(aJ[1]);if(aT.FileName!=null){}else{if(s(aT.refId)=="B"){if(aT.CDQData!=null){if(a2==null){a2=new Array()}Array.prototype.push.apply(a2,aT.CDQData)}else{if(aT.EOR==true){integral.ui.enableOrgViewDropdown();integral.ui.enableOrderOrgViewDropdown();integral.setOrderBlotterLoaded(true)}}}else{if(s(aT.refId)=="T"){if(aT.CDQData!=null){if(aW==null){aW=new Array()}Array.prototype.push.apply(aW,aT.CDQData)}else{if(aT.EOR==true){integral.ui.enableOrgViewDropdown()}}}else{if(s(aT.refId)=="O"){if(aT.CDQData!=null){if(aQ==null){aQ=new Array()}Array.prototype.push.apply(aQ,aT.CDQData)}else{if(aT.EOR==true){integral.ui.enableOrderOrgViewDropdown()}}}}}}}}catch(aZ){integral.log("Exception in processing the proxy response "+aZ+" : "+aJ)}}if(aW!=null){integral.ui.processOrgTradesJmsProxy(aW)}if(aQ!=null){integral.ui.processOrgOrdersJmsProxy(aQ)}if(a2!=null){integral.ui.processCDQObjectsJmsProxy(a2)}if(aR!=null){integral.ui.processCDQNotifications(aR)}if(aI!=null){integral.log(aI)}if(aK!=null){integral.log(aK)}return aG},log:function(aI,aH){var aK=new Date();var aJ=aK.toGMTString();var aG=JSON.stringify({message:aI,level:aH});au.ajax({url:"/fxi/fxiapi/log",type:"POST",beforeSend:function(aL){if(aL&&aL.overrideMimeType){aL.overrideMimeType("application/json;charset=UTF-8")}},data:aG,dataType:"json",async:aH==au.consts.LOG_ERROR?false:true,cache:false,contentType:"application/json;charset=UTF-8",success:function(aL){Z()},error:function(aL){}})},placeOrderRT:function(aL,aI){try{var aG=au.readCookie("LOGIN_TIME");if(G!=aG){var aM="placeOrderRT:Redirecting to login page as client login time is not same as server login time.";integral.log(aM,au.consts.LOG_ERROR);C(aM);return}var aH=integral.createOrderParams(aI);if(integral.ui.isFXInsideRT()){if(aI.provider!=null){aH.preferredProviders=[aI.provider]}else{aH.preferredProviders=["FMA"]}}var aK=JSON.stringify(aH);au.ajax({url:"/fxi/fxiapi/order/place",type:"POST",beforeSend:function(aN){if(aN&&aN.overrideMimeType){aN.overrideMimeType("application/json;charset=UTF-8")}},data:aK,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(aN){if(aN.status!="ERROR"){integral.log("Order place success : "+aN.order.orderId+" : "+aK);if(typeof aL=="function"){aL.call(this,aN.order,{channel:aI.channel,ccyPair:aI.ccyPair})}}else{integral.log("Order placed failed : "+aK);integral.handleServerError("place order",aN)}},error:function(aO){integral.log("Order place failed : "+aK);integral.handleServerError("place order",aO);if(typeof aL=="function"&&aI.channel=="HTML/ESP/PB"){var aN=au.parseJSON(aK);aN.orderStatus="Failed";aL.call(this,aN)}}})}catch(aJ){integral.log("Error in placing the order : "+aI.ccyPair+" : "+aI.rate+" : "+aI.amount+" : "+aI.oType+" : "+aI.channel)}},placeOrder:function(aJ,aO,aS,aL,aW,aR,a0,aT,aM,aY,aP,aK,a2,aI,aZ){try{var aQ=au.readCookie("LOGIN_TIME");if(G!=aQ){var aV="placeOrder:Redirecting to login page as client login time is not same as server login time.";integral.log(aV,au.consts.LOG_ERROR);C(aV);return}aL=aL.toString();var aG=[0];if(aK!=null&&aK!=undefined){aG=aG.concat(aK)}var aN={amount:aL,minQty:(aI==undefined?"0.00":aI),maxShow:aP,instrument:aS,dealtInstrument:aW,clientOrderId:new Date().getTime(),execFlags:aG,orderSide:aR,orderType:aO,customerOrg:Y,customerId:u,expiryType:aT,expiryTime:aM,marketRange:aY,tradeChannel:a2,orderNotes:aZ};if(aO==2){aN.stopPrice=a0}else{aN.limitPrice=a0}if(integral.ui.isSalesDealerTradingEnabled()){var a1=au("#salesdealerorgs").val();var aX=au("#salesdealerles").val();aN.customerOrg=a1;aN.customerAccount=aX}if(integral.ui.isFXInsideRT()){aN.preferredProviders=["FMA"]}var aH=JSON.stringify(aN);if((a2=="HTML/ESP/FXB"||a2=="HTML/ESP/FXL"||a2=="HTML/ESP/FXID/DLP")&&(a0==undefined||a0==null||a0<=0)){integral.log("Placing order failed : "+aH);S.call(this,"Order can not be placed with invalid price.");return}au.ajax({url:"/fxi/fxiapi/order/place",type:"POST",beforeSend:function(a3){if(a3&&a3.overrideMimeType){a3.overrideMimeType("application/json;charset=UTF-8")}},data:aH,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(a3){if(a3.status!="ERROR"){integral.log("Order place success : "+a3.order.orderId+" : "+aH);if(typeof aJ=="function"){aJ.call(this,a3.order,{channel:a2,ccyPair:aS})}}else{integral.log("Order placed failed : "+aH);integral.handleServerError("place order",a3)}},error:function(a4){integral.log("Order place failed : "+aH);integral.handleServerError("place order",a4);if(typeof aJ=="function"&&a2=="HTML/ESP/PB"){var a3=au.parseJSON(aH);a3.orderStatus="Failed";aJ.call(this,a3)}}})}catch(aU){integral.log("Error in placing the order : "+aS+" : "+a0+" : "+aL+" : "+aO+" : "+a2)}},placeOrders:function(aK,aL){try{var aI=new Array();for(var aH=0;aH<aL.length;aH++){aI.push(integral.createOrderParams(aL[aH]))}var aJ=JSON.stringify({orderList:aI});au.ajax({url:"/fxi/fxiapi/order/placeorders",type:"POST",beforeSend:function(aM){if(aM&&aM.overrideMimeType){aM.overrideMimeType("application/json;charset=UTF-8")}},data:aJ,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(aO){if(aO.orderResponses!=null){for(var aN=0;aN<aO.orderResponses.length;aN++){if(aO.orderResponses[aN].status!="ERROR"){var aM=aO.orderResponses[aN].order;integral.log("Order place success : "+aM.orderId+" : ");aK.call(this,aM,{channel:aM.channel,ccyPair:aM.ccyPair})}else{integral.log("Order placed failed : "+aJ);integral.handleServerError("place order",aO.orderResponses[aN])}}}},error:function(aM){integral.log("Order place failed : "+aJ);integral.handleServerError("place order",aM)}})}catch(aG){integral.log("Error in placing the orders : "+aL)}},resumeOrder:function(aH,aI,aG){au.ajax({url:"/fxi/fxiapi/order/resume?oid="+aH,type:"GET",beforeSend:function(aJ){if(aJ&&aJ.overrideMimeType){aJ.overrideMimeType("application/json;charset=UTF-8")}},dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(aJ){if(aJ.status!="ERROR"){integral.log("Order resume success : "+aH)}else{integral.log("Order resume failed : "+aH)}if(aG!=null){aG.call(aJ,aI)}},error:function(aJ){integral.log("Order resume failed : "+aH)}})},suspendOrder:function(aH,aI,aG){au.ajax({url:"/fxi/fxiapi/order/suspend?oid="+aH,type:"GET",beforeSend:function(aJ){if(aJ&&aJ.overrideMimeType){aJ.overrideMimeType("application/json;charset=UTF-8")}},dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(aJ){if(aJ.status!="ERROR"){integral.log("Order suspend success : "+aH)}else{integral.log("Order suspend failed : "+aH)}if(aG!=null){aG.call(aJ,aI)}},error:function(aJ){integral.log("Order suspend failed : "+aH)}})},amendIFDoneOrderAPI:function(aG){au.ajax({url:"/fxi/fxiapi/order/amend/batch",type:"POST",beforeSend:function(aH){if(aH&&aH.overrideMimeType){aH.overrideMimeType("application/json;charset=UTF-8")}},data:aG,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(aH){if(aH.status!="ERROR"){integral.log("Order amend success : "+aG)}else{integral.log("Order amend failed : "+aG);integral.handleServerError("amend order",aH)}},error:function(aH){integral.log("Order amend failed : "+aG);integral.handleServerError("amend order",aH)}})},amendOrder:function(aH){var aG={orderId:aH.orderId};if(aH.newOrderAmount!=null){aG.newOrderAmount=aH.newOrderAmount}if(aH.newOrderRate!=null){aG.newOrderRate=aH.newOrderRate}if(aH.newTriggerRate!=null){aG.newTriggerRate=aH.newTriggerRate}var aI=JSON.stringify(aG);au.ajax({url:"/fxi/fxiapi/order/amend",type:"POST",beforeSend:function(aJ){if(aJ&&aJ.overrideMimeType){aJ.overrideMimeType("application/json;charset=UTF-8")}},data:aI,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(aJ){if(aJ.status!="ERROR"){integral.log("Order amend success : "+aI)}else{integral.log("Order amend failed : "+aI);integral.handleServerError("amend order",aJ)}},error:function(aJ){integral.log("Order amend failed : "+aI);integral.handleServerError("amend order",aJ)}})},createOrderParams:function(aR){var aO=aR.oType;var aT=aR.ccyPair;var aJ=aR.amount;var aW=aR.dealtInstr;var aS=aR.buySell;var a0=aR.rate;var aU=aR.timeInFrc;var aK=aR.expTime;var aZ=aR.mktRange;var aP=aR.showAmt;var aI=aR.execStrg;var a2=aR.channel;var aN=aR.contingencyParameters;var aY=aR.clientOrderId;var aH=aR.executionStartTime;var aL=aR.executionEndTime;var aG=aR.minQty;var aQ=au.readCookie("LOGIN_TIME");if(G!=aQ){var aV="createOrderParams:Redirecting to login page as client login time is not same as server login time.";integral.log(aV,au.consts.LOG_ERROR);C(aV);return}if(aP!=undefined&&aP!=0){aP=integral.ui.getExpandedAmt(aP)>integral.ui.getExpandedAmt(aJ)?aJ:aP}aJ=aJ.toString();var aM={amount:aJ,minQty:aG==undefined?"0.00":aG,maxShow:aP,instrument:aT,dealtInstrument:aW,clientOrderId:new Date().getTime(),orderSide:aS,orderType:aO,customerOrg:Y,customerId:u,expiryType:aU,expiryTime:aK,marketRange:aZ,tradeChannel:a2,contingencyParameters:aN,clientOrderId:aY,executionStartTime:aH,executionEndTime:aL};if(aI!=null&&aI!=undefined){aM.execFlags=aI}if(aO==2){aM.stopPrice=a0;aM.limitPrice=a0}else{aM.limitPrice=a0}if(aR.executionStrategyParams!=null){aM.executionStrategyParams=aR.executionStrategyParams}if(aR.executionStrategyName!=null){aM.executionStrategyName=aR.executionStrategyName}if(aR.strategyType!=null){aM.strategyType=aR.strategyType}if(integral.ui.isSalesDealerTradingEnabled()){var a1=au("#salesdealerorgs").val();var aX=au("#salesdealerles").val();aM.customerOrg=a1;aM.customerAccount=aX}if(aR.metadata&&aR.metadata.indexOf("ifDone")!=-1){aM.orderMetadata=aR.metadata+",placedByOrg~"+integral.getUserOrg()}else{if(aR.metadata!=null){aM.orderMetadata=(aR.metadata.indexOf("placedByOrg")==-1)?aR.metadata+" placedByOrg~"+integral.getUserOrg():aR.metadata}else{if(!aR.metadata){aM.orderMetadata="placedByOrg~"+integral.getUserOrg()}}}aM.orderNotes=aR.orderNotes;if(aR.preferredProviders!=undefined){aM.preferredProviders=aR.preferredProviders}if(aR.fixingName!=null){aM.fixingName=aR.fixingName}if((a2=="HTML/ESP/FXB"||a2=="HTML/ESP/FXL"||a2=="HTML/ESP/FXID/DLP")&&(a0==undefined||a0==null||a0<=0)){integral.log("Placing order failed : "+aM);S.call(this,"Order can not be placed with invalid price.");return}if(aR.directedOrderParams!=null){aM.spotSpread=aR.spotSpread;aM.preferredProviders=aR.preferredProviders;aM.directedOrderParams=aR.directedOrderParams;aM.tenor=aR.tenor}aR=null;return aM},placeRfs:function(aG,aH,aJ,aO,aK,aI,aM,aN){if(ab==undefined){ab=aG}var aL=JSON.stringify({quoteId:aJ,sideType:parseInt(aO),instrument:aK,dealtInstrument:aI,tradeChannel:aM,stmtInst:aN});au.ajax({url:"/fxi/fxiapi/rfs/accept",type:"POST",beforeSend:function(aP){if(aP&&aP.overrideMimeType){aP.overrideMimeType("application/json;charset=UTF-8")}},data:aL,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(aP){if(aP.status!="ERROR"){integral.log("Rfs trade success : "+aJ);if(typeof ab=="function"){ab.call(aP,aH)}}else{integral.handleServerError("Place rfs "+aK,aP)}},error:function(aP){integral.handleServerError("Place rfs "+aK,aP)}})},getOrdersAfterReconnect:function(aG,aH){au.ajax({url:"/fxi/fxiapi/order/getOrdersByOrderId?orderIds="+aH,type:"GET",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:true,cache:false,success:function(aI){if(aI.status!="ERROR"){if(ar&&aA){aG.call(aI)}}},error:function(aI){aI.systemError=true;integral.handleServerError("getOrdersAfterReconnect "+aH,aI)}})},cancelOrder:function(aH,aG){au.ajax({url:"/fxi/fxiapi/order/cancel?oid="+aG,type:"GET",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:true,cache:false,success:function(aI){if(aI.status!="ERROR"){aH.call(aI,aG);integral.log("Order cancelled successfully : "+aG)}else{integral.handleServerError("CancelOrder "+aG,aI)}},error:function(aI){integral.handleServerError("CancelOrder "+aG,aI)}})},confirmTrade:function(aG,aH){aH=(aH!=null?parseInt(aH)+1:1);au.ajax({url:"/fxi/fxiapi/trade/confirmTrade?transactionId="+aG,type:"GET",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:true,cache:false,timeout:30000,success:function(aI){if(aI.status=="ERROR"){integral.log("Could not confirm trade "+aG+" "+aI.statusText+" Error count : "+aH);if(aH<12){setTimeout("integral.confirmTrade('"+aG+"','"+aH+")",5000)}}},error:function(aI){integral.log("Error in confirm trade "+aG+" Error count : "+aH+" "+aI.statusText);if(aH<12){setTimeout("integral.confirmTrade('"+aG+"','"+aH+"')",5000)}}})},fillAtMarket:function(aI,aG){var aH=JSON.stringify({orderId:aG});au.ajax({type:"POST",url:"/fxi/fxiapi/order/cancelReplace",beforeSend:function(aJ){if(aJ&&aJ.overrideMimeType){aJ.overrideMimeType("application/json;charset=UTF-8")}},data:aH,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(aJ){},error:function(aJ){integral.handleServerError("Fill @ market "+aG,aJ)}})},saveUIComponent:function(aJ,aH,aI,aG){integral.log("Saving "+aJ+" : "+aH);au.ajax({url:"/fxi/fxiapi/pref/save/"+aJ,type:"POST",beforeSend:function(aK){if(aK&&aK.overrideMimeType){aK.overrideMimeType("application/json;charset=UTF-8")}},data:'{"pref":'+aH+"}",dataType:"json",async:false,cache:false,contentType:"application/json;charset=UTF-8",success:function(aK){if(aK.status=="OK"&&aI){aI.call(aK,aG)}},error:function(aK){integral.log("Save failed for "+aJ+": "+aH+" "+aK.status+" - "+aK.statusText)}})},loadUIComponent:function(aG){var aH;au.ajax({url:"/fxi/fxiapi/pref/load/"+aG,type:"GET",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(aI){aH=aI.pref;integral.log("Loading "+aG+" : "+aH)},error:function(aI){}});return aH},saveGeneralPrefs:function(aH,aG){integral.log("Saving "+aH+" : "+aG);au.ajax({url:"/fxi/fxiapi/pref/save/"+aH,type:"POST",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},data:'{"pref":'+aG+"}",dataType:"json",async:false,cache:false,contentType:"application/json;charset=UTF-8",success:function(aI){},error:function(aI){integral.log("Save failed for "+aH+": "+aG+" "+aI.status+" - "+aI.statusText)}})},loadGeneralPrefs:function(aH){var aG;au.ajax({url:"/fxi/fxiapi/pref/load/"+aH,type:"GET",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(aI){aG=aI;integral.log("Loading "+aH+" : "+aG.pref)},error:function(aI){}});return aG},loadMultipleComponents:function(aH,aG){au.ajax({url:"/fxi/fxiapi/pref/multiLoad?components="+aH,type:"GET",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(aI){aG.call(aI)},error:function(aI){this.systemError=true;S.call(this,"Load of settings failed for "+name+": "+aI.status+" "+aI.statusText)}})},loadSettings:function(aG,aH){au.ajax({url:"/fxi/fxiapi/pref/load/"+aG,type:"GET",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(aI){aH.call(aI);integral.log("Loading "+aG+" : "+aI.pref)},error:function(aI){this.systemError=true;S.call(this,"Load of settings failed for "+aG+": "+aI.status+" "+aI.statusText)}})},saveSettings:function(aH,aG,aI,aK){var aJ={pref:aG};au.ajax({url:"/fxi/fxiapi/pref/save/"+aH,type:"POST",cache:false,beforeSend:function(aL){if(aL&&aL.overrideMimeType){aL.overrideMimeType("application/json;charset=UTF-8")}},data:JSON.stringify(aJ),dataType:"json",contentType:"application/json;charset=UTF-8",success:function(aL){if(aK){integral.log("Setting changed "+aK)}if(aI!=undefined&&aI!=null){aI.call(aL)}},error:function(aL){integral.log("Setting change failed "+aK)}})},loadRMMSettings:function(aG,aH){au.ajax({url:"/fxi/fxiapi/rmmpref/load/"+aG,type:"GET",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(aI){aH.call(aI);integral.log("Loading "+aG+" : "+aI.pref)},error:function(aI){S.call(this,"Load of settings failed for "+aG+": "+aI.status+" "+aI.statusText)}})},loadRMMAllowedPlans:function(aG){au.ajax({url:"/fxi/fxiapi/rmmpref/plans/",type:"GET",beforeSend:function(aH){if(aH&&aH.overrideMimeType){aH.overrideMimeType("text/plain;charset=UTF-8")}},contentType:"text/plain;charset=UTF-8",async:false,cache:false,success:function(aH){aG.call(aH);integral.log("Allowed User Plans "+aH)},error:function(aH){S.call(this,"Load of allowed plans failed: "+aH.status+" "+aH.statusText)}})},rmmBlockRateUpdateOnHide:function(){},rmmBlockOrderBlotterUpdateOnHide:function(){},rmmBlockPositionBlotterUpdateOnHide:function(){},rmmBlockNotificationUpdateOnHide:function(){},rmmBlockRfsUpdateOnHide:function(){},rmmBlockRfsFBRateUpdateOnHide:function(){},useRmmOnOrderBlotterUpdateCallbackFn:function(){if(A!=undefined&&A!=this.rmmBlockOrderBlotterUpdateOnHide){B=A;A=this.rmmBlockOrderBlotterUpdateOnHide}},useRmmOnPositionBlotterUpdateCallbackFn:function(){if(E!=undefined&&E!=this.rmmBlockPositionBlotterUpdateOnHide){U=E;E=this.rmmBlockPositionBlotterUpdateOnHide}},useRmmOnRateUpdateCallbackFn:function(){if(V!=undefined&&V!=this.rmmBlockRateUpdateOnHide){aw=V;V=this.rmmBlockRateUpdateOnHide}},useRmmOnNotificationUpdateCallbackFn:function(){if(ay!=undefined&&ay!=this.rmmBlockNotificationUpdateOnHide){m=ay;ay=this.rmmBlockNotificationUpdateOnHide}},useRmmOnRfsUpdateCallbackFn:function(){if(am!=undefined&&am!=this.rmmBlockRfsUpdateOnHide){an=am;am=this.rmmBlockRfsUpdateOnHide}},useRmmOnRfsFBRateUpdateCallbackFn:function(){if(aE!=undefined&&aE!=this.rmmBlockRfsFBRateUpdateOnHide){ak=aE;aE=this.rmmBlockRfsFBRateUpdateOnHide}},useOriginalOnOrderBlotterUpdateCallbackFn:function(){if(A===this.rmmBlockOrderBlotterUpdateOnHide){A=B}},useOriginalOnPositionBlotterUpdateCallbackFn:function(){if(E===this.rmmBlockPositionBlotterUpdateOnHide){E=U}},useOriginalOnRateUpdateCallbackFn:function(){if(V===this.rmmBlockRateUpdateOnHide){V=aw}},useOriginalOnNotificationUpdateCallbackFn:function(){if(ay===this.rmmBlockNotificationUpdateOnHide){ay=m}},useOriginalOnRfsUpdateCallbackFn:function(){if(am===this.rmmBlockRfsUpdateOnHide){am=an}},useOriginalOnRfsFBRateUpdateCallbackFn:function(){if(aE===this.rmmBlockRfsFBRateUpdateOnHide){aE=ak}},setOnRateUpdateCallbackFn:function(aG){V=aG},loadRfsWaitTime:function(aH,aG){au.ajax({url:"/fxi/fxiapi/rmmpref/rfsWaitTime/",type:"GET",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("text/plain;charset=UTF-8")}},contentType:"text/plain;charset=UTF-8",async:false,cache:false,success:function(aI){aG.call("",aH,aI);integral.log("RFS Wait Time "+aI)},error:function(aI){S.call(this,"Load of RFS Wait Time failed: "+aI.status+" "+aI.statusText)}})},saveRMMSettings:function(aH,aG,aI,aK){var aJ={pref:aG};au.ajax({url:"/fxi/fxiapi/rmmpref/save/"+aH,type:"POST",cache:false,beforeSend:function(aL){if(aL&&aL.overrideMimeType){aL.overrideMimeType("application/json;charset=UTF-8")}},data:JSON.stringify(aJ),dataType:"json",contentType:"application/json;charset=UTF-8",success:function(aL){if(aK){integral.log("Setting changed "+aK)}if(aI!=undefined&&aI!=null){aI.call(aL)}},error:function(aL){integral.log("Setting change failed "+aK)}})},saveClientUIComponent:function(aH,aG){integral.log("Saving ClientUI "+aH+" : "+aG);au.ajax({url:"/fxi/fxiapi/clientpref/save/"+aH,type:"POST",cache:false,beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},data:'{"pref":'+aG+"}",dataType:"json",contentType:"application/json;charset=UTF-8",success:function(aI){},error:function(aI){}})},loadClientUIComponent:function(aG){var aH;au.ajax({url:"/fxi/fxiapi/clientpref/load/"+aG,type:"GET",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(aI){if(aI.status!="ERROR"){aH=aI.pref;integral.log("Loading ClientUI "+aG+" : "+aH)}},error:function(aI){}});return aH},saveUserDisplayPrefs:function(aH,aG){au.ajax({url:"/fxi/fxiapi/pref/save/dispPref",type:"POST",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},async:false,data:aH,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(aI){aG.call(au.parseJSON(aH))},error:function(aI){}})},loadUserDisplayPrefs:function(aG){au.ajax({url:"/fxi/fxiapi/pref/load/dispPref",type:"GET",beforeSend:function(aH){if(aH&&aH.overrideMimeType){aH.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",cache:false,success:function(aH){if(aH.status=="OK"){aG.call(aH.userDP)}else{S.call(this,"Load user display prefs failed :"+aH.status+" "+aH.errorCode)}},error:function(aH){this.systemError=true;S.call(this,"Load user display prefs failed :"+aH.status+" "+aH.statusText)}})},getProvidersList:function(){var aG={};au.ajax({type:"GET",url:"/fxi/fxiapi/refdata/providers/real",async:false,cache:false,dataType:"json",success:function(aH){aG=aH},error:function(aH){aH.systemError=true;integral.handleServerError("getProvidersList",aH)}});return aG.sort()},enableSecondaryServer:function(){i=true;if(!M){M=true;integral.initSecondaryService()}},setUserInfo:function(aG,aH){u=aG;Y=aH;ae=aG+"_"+aH},setErrorHandler:function(aG){S=aG},checkAuthToken:function(aH){var aI,aG;P=aH},getOrders:function(aH){var aG;au.ajax({url:"/fxi/fxiapi/order/activeAndToday",cache:false,type:"GET",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:true,success:function(aI){aG=aI;aH.call(aG);ar=true;if(ar&&aA){T()}},error:function(aI){aI.systemError=true;integral.handleServerError("activeAndToday",aI)}})},setOrderBlotterLoaded:function(aG){ar=aG},getActiveOrders:function(aI,aK,aH){var aG;var aJ="/fxi/fxiapi/order/active";if(aK){aJ="/fxi/fxiapi/order/active?orgLevel=true&enrichTrades=true"}au.ajax({url:aJ,cache:false,type:"GET",beforeSend:function(aL){if(aL&&aL.overrideMimeType){aL.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:true,success:function(aL){aG=aL;aI.call(aG)},error:function(aL){aL.systemError=true;integral.handleServerError("getActiveOrders",aL)}})},getOrderById:function(aG,aH){au.ajax({url:"/fxi/fxiapi/order/q?orderId="+aG,type:"GET",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:true,cache:false,success:function(aJ){if(aJ.length>0){var aI=aJ[0];aH.call(aI)}else{integral.log("No order retrieved for orderId: "+aG)}},error:function(aI){aI.systemError=true;integral.handleServerError("getOrderById "+aG,aI)}})},getTradesForOrder:function(aG,aI){var aH=au.ajax({url:"/fxi/fxiapi/trade/q?orderId="+aG,type:"GET",beforeSend:function(aJ){if(aJ&&aJ.overrideMimeType){aJ.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:true,cache:false,timeout:60000,success:function(aJ){aI.call(aJ)},error:function(aK,aJ){aK.systemError=true;if(aJ=="timeout"){aI.call(aJ);integral.log("Open order ticket - Trade query timedout for order: "+aG)}else{if(aJ=="abort"){integral.log("getTradesForOrder aborted : User might have closed the order ticket "+aG)}else{integral.handleServerError("getTradesForOrder "+aG,aK)}}}});return aH},getTradesForTradeIds:function(aH,aG){au.ajax({url:"/fxi/fxiapi/trade/getTradesByTradeId?tradeIds="+aH,type:"GET",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:true,cache:false,success:function(aI){aG.call(aI)},error:function(aJ,aI){integral.log("Could not get Trades for : "+aH)}})},downloadCDQResponseFile:function(aG,aH){jQuery.ajax({async:true,url:"/fxi/fxiapi/config/download?fileName="+aH+"&time="+(new Date().getTime()),type:"GET",dataType:"text",data:null,success:function(aI){if(aG!=null){aG.call(this,aI)}}})},getCDQResponseTemplate:function(aG){au.ajax({url:"/fxi/fxiapi/util/responseTemplateInJSON",type:"GET",beforeSend:function(aH){if(aH&&aH.overrideMimeType){aH.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(aH){if(aG!=null){aG.call(this,aH)}},error:function(aH){integral.handleServerError("getCDQResponseTemplate ",aH)}})},getOrgTradesForDate:function(aI,aG,aH){au.ajax({url:"/fxi/fxiapi/trade/tradeDetails?fromDate="+aH+"&toDate="+aH+"&queryDataType="+aG+"&writeToFile=false&fixedFormatting=false&compaction=false&btchSize=10&maxCount="+integral.ui.getMaxCdqCount(),type:"GET",beforeSend:function(aJ){if(aJ&&aJ.overrideMimeType){aJ.overrideMimeType("application/json;charset=UTF-8")}},queryType:aG,contentType:"application/json;charset=UTF-8",async:true,cache:false,timeout:5000,success:function(aK){if(I){clearTimeout(I)}I=setTimeout(function(){F(aK.responseTuples[0].value)},60000);g=aK.responseTuples[0].value;var aJ=aK.responseTuples[0].value;K[aG]=(aJ>K[aG])?aJ:K[aG];if(aI!=null){aI.call(this,aG,g)}},error:function(aL,aJ,aK){if(aJ=="timeout"){integral.log("tradeDetails query for Org view timedout")}setTimeout(function(){F(null)},60000);integral.handleServerError("getOrgTradesForCurrentDate ",aK)}})},getTradesForCurrentDate:function(aG){var aH;au.ajax({url:"/fxi/fxiapi/trade/q?dateRange=Today",type:"GET",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:true,cache:false,success:function(aI){aG.call(this,aI);aA=true;integral.ui.enableOrgViewDropdown();if(ar&&aA){T()}},error:function(aI){aI.systemError=true;integral.handleServerError("getTradesForCurrentDate ",aI)}})},getTradeById:function(aH,aG){au.ajax({url:"/fxi/fxiapi/trade/q?tradeId="+aH,type:"GET",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:true,cache:false,success:function(aI){if(aI.length>0){var aJ=aI[0];aG.call(aJ)}else{integral.log("No trade retrieved for tradeId: "+aH)}},error:function(aI){aI.systemError=true;integral.handleServerError("getTradeById "+aH,aI)}})},getBaseAmount:function(aI,aG,aH,aJ){if(aG==aI){return aH}else{return aJ}},getVarAmount:function(aI,aG,aH,aJ){if(aG==aI){return aJ}else{return aH}},getBrandInfo:function(aI){var aH=JSON.stringify({clientType:"WEBCLIENT",clientVersion:"1.0",org:aI});var aG;au.ajax({url:"/fxi/fxiapi/brand/brandInfo",data:aH,type:"POST",beforeSend:function(aJ){if(aJ&&aJ.overrideMimeType){aJ.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(aJ){if(aJ.status=="ERROR"){integral.log("No branded info found for: "+aI+" "+aJ.errorCode)}aG=aJ.brandData},error:function(aJ){integral.log("No branded info found for: "+aI+aJ.status)}});return aG},startHeartbeat:function(){al();integral.log("Started polling timer.")},subscribeSwapPoints:function(aH){var aG=JSON.stringify(aH);au.ajax({url:"/fxi/fxiapi/marketdata/subscribeSwapPoint",data:aG,type:"POST",beforeSend:function(aI){if(aI&&aI.overrideMimeType){aI.overrideMimeType("application/json;charset=UTF-8")}},contentType:"application/json;charset=UTF-8",cache:false,dataType:"json",success:function(aI){if(aI.status=="ERROR"){integral.log("Subscribtion failed for Swap Points")}},error:function(aI){integral.log("Subscribtion failed for Swap Points")}})}}})(jQuery);(function(e){var c="",j="",i="";e.fn.createFxLogin=function(l,o,s){e.props={redirectUrl:l,brandedPath:o};var n=this;var u=e('<div id="logincontainer"><div class="loginbranding"></div><div id="errormessagelogin"></div><div id="loginelements"><form method="post" id="login_form" onsubmit="return false;" autocomplete="off" action=""><div class="logininput"><label id="orglable" for="organization" class="loginlabel2">'+integral.common.getLabel(e.consts.ORGANIZATION)+'</label><input id="organization" name="organization" value="" title="'+integral.common.getLabel(e.consts.ORGANIZATION).toLowerCase()+'" type="text" class="logininputfields"></div><div style="clear:both;"></div><div class="logininput"><label id="unlable" for="username" class="loginlabel2">'+integral.common.getLabel(e.consts.USER_NAME)+'</label><input id="username" name="username" value="" title="'+integral.common.getLabel(e.consts.USER_NAME).toLowerCase()+'" type="text" class="logininputfields"></div><div style="clear:both;"></div><div class="logininput"><label id="pwlable" for="password" class="loginlabel2">'+integral.common.getLabel(e.consts.PASSWORD)+'</label><input id="password" name="password" value="" title="'+integral.common.getLabel(e.consts.PASSWORD).toLowerCase()+'" type="password" class="logininputfields"></div><div style="clear:both;"></div><div id="otpdiv" class="logininput"><label id="otplable" for="password" class="loginlabel2">OTP</label><input id="otp" name="otp" value="" title="OTP" type="password" class="logininputfields"></div><div style="clear:both;"></div><div class="loginbuttonbar"><span class="loginbuttonalign"><input id="login_submit" value='+integral.common.getLabel(e.consts.LOGIN)+' type="submit" class="loginbutton"></span><span id="forgot" class="forgot"><span>'+integral.common.getLabel(e.consts.FORGOT_PASSWORD)+'</span></span></div><span id="2faspan" class="forgot"><label class="">Static</label><input checked type=radio name=pwchoice value="0">&nbsp;&nbsp;<label class="">OTP</label><input type=radio name=pwchoice value="1">&nbsp;&nbsp;<label class="">2FA</label><input type=radio id="2fa" name=pwchoice value="2"></span></form></div><div style="clear:both;"></div><div id="logincopyright">'+integral.common.getLabel(e.consts.IDC_COPYRIGHT)+"</div></div>");this.append(u);if(e.readCookie("LogoutError")!=null){e("#errormessagelogin").html(jQuery.i18n.prop(e.readCookie("LogoutError")));integral.removeCookie("LogoutError")}var q=e.readCookie(o+"storedname");var m=e.readCookie(o+"storedorg");if(q!=undefined){e("#unlable").hide();e("#username").val(q)}if(m!=undefined){e("#orglable").hide();e("#organization").val(m)}e("#otpdiv").hide();if(!integral.common.has2FAPermission()){e("#2faspan").hide()}else{e('input[id="2fa"]:radio').prop("checked",true);p("2")}function p(v){switch(v){case"0":e("#otp").val("");e("#otplable").show();e("#otpdiv").hide();e("#password").removeAttr("disabled");e("#forgot").removeClass("forgotDisabled");e("#forgot").addClass("forgot");break;case"1":e("#otpdiv").show();e("#password").val("");e("#pwlable").show();e("#password").attr("disabled","disabled");e("#forgot").removeClass("forgot");e("#forgot").addClass("forgotDisabled");break;case"2":e("#otpdiv").show();e("#password").removeAttr("disabled");e("#forgot").removeClass("forgotDisabled");e("#forgot").addClass("forgot");break}}e("input[name=pwchoice]:radio").change(function(){p(this.value)});e("#username").on("focus keydown",function(){e("#unlable").hide()});e("#username").on("blur keyup",function(){if(this.value==undefined||e.trim(this.value)==""){e("#unlable").show()}else{e("#unlable").hide()}});e("#otp").on("focus keydown",function(){e("#otplable").hide()});e("#otp").on("blur keyup",function(){if(this.value==undefined||e.trim(this.value)==""){e("#otplable").show()}else{e("#otplable").hide()}});e("#password").on("focus keydown",function(){e("#pwlable").hide()});e("#password").on("blur keyup",function(){if(this.value==undefined||e.trim(this.value)==""){e("#pwlable").show()}else{e("#pwlable").hide()}});e("#organization").on("focus keydown",function(){e("#orglable").hide()});e("#organization").on("blur keyup",function(){if(this.value==undefined||e.trim(this.value)==""){e("#orglable").show()}else{e("#orglable").hide()}});e("#forgot").click(function(){if(e("#forgot").attr("class")=="forgot"){integral.forgotPassword(e("#username").val(),e("#organization").val(),a)}});e("#login_submit").click(function(){e("#errormessagelogin").html("");c=e("#username").val();j=e("#organization").val();if(!t(e("#username").val())){return}var v=parseInt(e("input[name=pwchoice]:radio:checked").val());i=hex_md5(e("#password").val());var w=e("#otp").val();if(v==0){w=""}else{if(v==1){i=""}}integral.setCookie(o+"storedname",e("#username").val(),true);integral.setCookie(o+"storedorg",e("#organization").val(),true);if(s==="false"||s===false){integral.setCookieForRedirection(e("#organization").val())}var x=r(c,e("#password").val(),j,w,v);if(!x){e("#errormessagelogin").html(integral.common.getLabel(e.consts.PLEASE_ENTER_VALID_CREDENTIALS));return}if(s==="true"||s===true){integral.loginSSO(c,i,j,w,f,e.props.brandedPath)}else{integral.login2fa(c,i,j,w,d,e.props.brandedPath)}});function r(y,w,A,v,x){var z=integral.common.getProperty("client_side_login_validation","true");if(z=="skip"){return true}switch(x){case 0:if(y==undefined||y==""||w==undefined||w==""||A==undefined||A==""){return false}break;case 1:if(y==undefined||y==""||v==undefined||v==""||A==undefined||A==""){return false}break;case 2:if(y==undefined||y==""||w==undefined||w==""||v==undefined||v==""||A==undefined||A==""){return false}break}return true}function t(v){if(e.props.redirectUrl.indexOf("slpweb")!=-1){return(jQuery.i18n.prop("dev_users").indexOf(v)!=-1||jQuery.i18n.prop("dev_users").indexOf("ALL")!=-1)}else{return true}}};function b(l){var m=e('<div id="logincontainer"><div class="loginbranding"></div><div id="errormessagelogin">'+integral.common.getLabel(e.consts.CREATE_NEW_PASSWORD)+'</div><div id="loginelements"><form method="post" id="login_form" onsubmit="return false;" action=""><div class="logininput"><label id="oplable" for="password" class="loginlabel2">'+integral.common.getLabel(e.consts.ENTER_PASSWORD)+'</label><input id="oldpass" name="password" title="password" type="password" class="logininputfields" autocomplete="off"></div><div style="clear:both;"></div><div class="logininput"><label id="nplable" for="newpassword" class="loginlabel2">'+integral.common.getLabel(e.consts.NEW_PASSWORD)+'</label><input id="newpass" name="newpassword" value="" title="newpassword" type="password" class="logininputfields"></div><div style="clear:both;"></div><div class="logininput"><label id="cplable" for="confirmpassword" class="loginlabel2">'+integral.common.getLabel(e.consts.CONFIRM_PASSWORD)+'</label><input id="confirmpass" name="confirmpassword" value="" title="confirmpassword" type="password" class="logininputfields"></div><div style="clear:both;"></div><div class="loginbuttonbar"><span class="loginbuttonalign"><input id="save" value="Save" type="submit" class="loginbutton"></span></div></form></div><div style="clear:both;"></div><div id="logincopyright">'+integral.common.getLabel(e.consts.IDC_COPYRIGHT)+"</div></div>");e("#fxlogin").find("#logincontainer").remove();e("#fxlogin").prepend(m);e("#oldpass").on("focus keydown",function(){e("#oplable").hide()});e("#oplable").click(function(){e("#oplable").hide();e("#oldpass").focus()});e("#oldpass").on("blur keyup",function(){if(this.value==undefined||e.trim(this.value)==""){e("#oplable").show()}else{e("#oplable").hide()}});e("#newpass").on("focus keydown",function(){e("#nplable").hide()});e("#nplable").click(function(){e("#nplable").hide();e("#newpass").focus()});e("#newpass").on("blur keyup",function(){if(this.value==undefined||e.trim(this.value)==""){e("#nplable").show()}else{e("#nplable").hide()}});e("#confirmpass").on("focus keydown",function(){e("#cplable").hide()});e("#cplable").click(function(){e("#cplable").hide();e("#confirmpass").focus()});e("#confirmpass").on("blur keyup",function(){if(this.value==undefined||e.trim(this.value)==""){e("#cplable").show()}else{e("#cplable").hide()}});e("#save").click(function(){if(e("#newpass").val()==e("#confirmpass").val()){var n=integral.resetPassword(e("#oldpass").val(),e("#newpass").val(),undefined);if(n.status=="OK"){if(l.legalAgreementStatus==false){integral.ui.showLicenseAgreement(l)}else{integral.log("Reset password.dealingServiceCallBack:Loading main panel.");loadMainPanel(l)}}else{d(n)}}else{e("#errormessagelogin").html(integral.common.getLabel(e.consts.NEW_CONFIRMPASSWD_DONT_MATCH))}})}function a(l){if(l.status=="OK"){e("#errormessagelogin").html(integral.common.getLabel(e.consts.NEW_PASSWORD_SENT))}else{e("#errormessagelogin").html(l.errorCode)}}function k(l){integral.log("Login Error : "+l);if(l.indexOf("ERR_OTP_INTERNAL_SERVER_ERROR_")==0){return l.replace("ERR_OTP_INTERNAL_SERVER_ERROR_",integral.common.getLabel(e.consts.CONTACT_SUPPORT_FORLOGIN)+" ")}return integral.common.getLabel(integral.common.getErrorMsg(l))}function d(m){if(e.parseJSON(m.responseText)!=null&&e.parseJSON(m.responseText).status=="ERROR"){e("#errormessagelogin").html(k(e.parseJSON(m.responseText).errorCode))}else{if(m.status!=null&&m.status=="ERROR"){e("#errormessagelogin").html(k(m.errorCode))}else{if(m.status==500){e("#errormessagelogin").html(k(m.statusText))}else{if(m.status=="OK"){var l=e.parseJSON(this.data);e.props.user=l.user;e.props.org=l.org;if(m.changePassword==true){b()}else{if(m.legalAgreementStatus==false){integral.ui.showLicenseAgreement(m)}else{integral.log("mycallback:Loading main panel.");loadMainPanel(m)}}}}}}}function h(m){if(m.status==401){e("#errormessagelogin").html(k(m.statusText))}else{if(m.status=="OK"){var l=e.parseJSON(this.data);e.props.user=l.user;e.props.org=l.org;if(m.changePassword==true){b(m)}else{if(m.legalAgreementStatus==false){integral.ui.showLicenseAgreement(m)}else{integral.log("dealingServiceCallBack:Loading main panel.");loadMainPanel(m)}}}}}function f(l){if(l.status=="OK"){integral.initDealingService(c,i,j,l.brandedPath,h)}else{d(l)}}function g(m,l){if(l.status=="OK"){integral.log("newPasswordCallBack:Loading main panel.");loadMainPanel(l)}else{d(l)}}})(jQuery);integral.common=(function(a){a.consts={API_VERSION:"1.21",MAIN_APP_TITLE:"main_app_title",HEADER_LOGOUTBUTTON:"header_logoutbutton",MAIN_HDR_STNGS:"main_hdr_stngs",MAIN_HDR_TRDNG:"main_hdr_trdng",TRADING_PANELS:"trading_panels",HDR_TOGGLETRDNG_OFF:"hdr_toggletrdng_off",HDR_TOGGLETRDNG_ON:"hdr_toggletrdng_on",MAIN_HDR_CURRENT_USER:"main_hdr_current_user",MAIN_HDR_THEMES:"main_hdr_themes",MAIN_HDR_ORG:"main_hdr_org",MAIN_HDR_TRADEDATE:"main_hdr_tradedate",MAIN_HDR_LOCALTIME:"main_hdr_localtime",BOARD_TITLE:"board_title",LADDER_TITLE:"ladder_title",FXFBTITLE:"fxfbtitle",OP_TITLE:"OP_title",OP_ORDERTYPE:"OP_ordertype",OP_LIMITPRICE:"OP_limitprice",OP_STOPPRICE:"OP_stopprice",OP_TRIGGER:"OP_trigger",OP_TIMEINFORCE:"OP_timeinforce",OP_SECONDS:"OP_seconds",OP_SUBMITORDER_BUTTON:"OP_Submitorder_button",OP_CLOSE_BUTTON:"OP_close_button",OP_INVALID_AMOUNT_ENTERED:"OP_invalid_amount_entered",OP_INVALID_AMOUNT:"OP_invalid_amount",OP_TIMEINFORCE_INVALID:"OP_timeinforce_invalid",OP_ENTER_VALID_PRICE:"OP_enter_valid_price",OP_INVALID_RATES:"OP_invalid_rates",OP_INVALID_AMOUNT:"OP_invalid_amount",OP_BID_GREATERTHAN_BESTOFFER:"OP_bid_greaterthan_bestoffer",OP_STOPBID_MUSTBE_ABOVE_BESTOFFER:"OP_stopbid_mustbe_above_bestoffer",OP_STOPOFFER_MUSTBE_BELOW_BESTBID:"OP_stopoffer_mustbe_below_bestbid",OP_OFFER_LESSTHAN_BESTBID:"OP_offer_lessthan_bestbid",OP_RATEFIELD_SHOULDNOTBEEMPTY:"OP_ratefield_shouldnotbeempty",OP_AMOUNTFILED_SHOULDNOTBEEMPTY:"OP_amountfiled_shouldnotbeempty",OP_INVALIDRATE_MAXPRECISION_EXCEEDED:"OP_invalidrate_maxprecision_exceeded",OP_RATES_NOTAVAILABLE:"OP_rates_notavailable",OP_GREATER_TPPRICE:"OP_tplmtprc_grtrthan_parentorderprc",OP_LESSER_TPPRICE:"OP_tplmtprc_lessthan_parentorderprc",OP_GREATER_SLPRICE:"OP_sltrgrprc_grtrthan_parentorderprc",OP_LESSER_SLPRICE:"OP_sltrgrprc_lessthan_parentorderprc",OP_IFDONE_DIALOG:"OP_dialog_ifdone_fill@mkt_unfilledorder",OP_IFDONE_DIALOGTITLE:"OP_dialogtitle_ifdone_fill@mkt_unfilledorder",OP_IFDONEAMEND_DIALOGTITLE:"OP_dialogtitle_ifdoneamend_validate",OP_IFDONELBL:"OP_ifdonelabel",OP_IFDONETPLBL:"OP_ifdonetplabel",OP_IFDONESLLBL:"OP_ifdonesllabel",OP_IFDONETPTYPE:"OP_ifDonetptype",OP_IFDONETPORDTYPE:"OP_ifDonetpordertype",OP_IFDONESLTYPE:"OP_ifDonesltype",OP_IFDONESLORDTYPE:"OP_ifDoneslordertype",OSP_CANCELLING:"OSP_cancelling",OSP_CLRALL_BTN:"OSP_clrall_btn",OSP_OFFALL_BTN:"OSP_offall_btn",OSROW_OFFBTN:"OSROW_offbtn_value",OB_TITLE:"OB_title",PB_TITLE:"PB_title",TB_TITLE:"TB_title",MSGBLTR_TITLE:"MB_title",OB_STATUS:"OB_status",OB_CCYPAIR:"OB_ccypair",OB_DEALTCURRENCY:"OB_dealtcurrency",OB_FILLAMOUNT:"OB_fillamount",OB_ORDERRATE:"OB_orderrate",OB_FILLRATE:"OB_fillrate",PB_LONGSHORT:"PB_longshort",PB_NETAMOUNT:"PB_netamount",PB_POSITIONRATE:"PB_positionrate",PB_PANDL:"PB_pandl",TB_TENOR:"TB_tenor",TB_FIXINGDATE:"TB_fixingdate",TB_EXECUTIONTIME:"TB_executiontime",TB_BASEAMOUNT:"TB_baseamount",TB_TERMAMOUNT:"TB_termamount",TB_STREAM:"Stream",PLANNER_UI_LABEL:"planner_ui_label",STNGS_TITLE:"stngs_title",STNGS_GENERAL:"stngs_general",STNGS_GENERAL_DISPLAY:"stngs_general_display",STNGS_GENERAL_DATEFORMAT:"stngs_general_dateformat",STNGS_GENERAL_TIMEFORMAT:"stngs_general_timeformat",STNGS_GENERAL_NEGINDICATOR:"stngs_general_negindicator",STNGS_GENERAL_DECSEPERATOR:"stngs_general_decseperator",STNGS_GENERAL_AMOUNTFORMAT:"stngs_general_amountformat",STNGS_GENERAL_PNLCURRENCY:"stngs_general_pnlcurrency",STNGS_GENERAL_REVERTTODEFAULT:"stngs_general_reverttodefault",STNGS_GENERAL_DISABLEAUTOUNIT:"stngs_general_disableautounit",STNGS_GENERAL_OPENONORDERSUBMIT:"stngs_general_openonordersubmit",CONFIRM_OSP_CLOSE:"confirm_osp_close",STNGS_TRADESTNGS_TITLE:"stngs_tradestngs_title",STNGS_TRDNG_TRDNGSTYLE:"stngs_trdng_trdngstyle",STNGS_TRDNG_SINGLECLICK:"stngs_trdng__singleclick",STNGS_TRDNG_DOUBLECLICK:"stngs_trdng_doubleclick",STNGS_TRDNG_CCYPAIRS:"stngs_trdng_ccypairs",STNGS_TRDNG_FBROWS:"stngs_trdng_fbrows",STNGS_TRDNG_TRADESIZES:"stngs_trdng_tradesizes",STNGS_TRDNG_LADDERSIZES:"stngs_trdng_laddersizes",STNGS_TRDNG_MAXORDERSIZE:"stngs_trdng_maxordersize",STNGS_TRDNG_MAXNETSPOTAMT:"stngs_trdng_maxnetspotamt",STNGS_TRDNG_COPYALLCCYSBTN:"stngs_trdng_copyallccysbtn",STNGS_TRDNG_EXECRANGE:"stngs_trdng_execrange",STNGS_TRDNG_EXECRANGE_CCYPAIR:"stngs_trdng_execrange_ccypair",STNGS_TRDNG_EXECRANGE_RANGE:"stngs_trdng_execrange_range",STNGS_TRDNG_EXECRANGE_RANGEINPUT:"stngs_trdng_execrange_rangeinput",STNGS_TRDNG_EXECRANGE_APPLYBUTTON:"stngs_trdng_execrange_applybutton",STNGS_TRDNG_RESTOREDEFAULTSBUTTON:"stngs_trdng_restoredefaultsbutton",STNGS_RFSSTNGS_TITLE:"stngs_rfsstngs_title",STNGS_RFS_DEFAULTTENOROUTRIGHT:"stngs_rfs_defaulttenoroutright",STNGS_RFS_DEFAULTTENOR_SWAP:"stngs_rfs_defaulttenor_swap",STNGS_RFS_EXPIRYTIME:"stngs_rfs_expirytime",STNGS_RFS_SECONDS:"stngs_rfs_seconds",STNGS_RFS_COPYTOALLCCYBTN:"stngs_rfs_copytoallccybtn",STNGS_RFS_RESTOREDEFAULTS:"stngs_rfs_restoredefaults",STNGS_INVALID_ORDERSIZES:"stngs_invalid_ordersizes",STNGS_INVALID_MARKETRANGE:"stngs_invalid_marketrange",STNGS_EXPIRYTIME_VALIDATION:"stngs_expirytime_validation",STNGS_INVALID_SIZES:"stngs_invalid_sizes",STNGS_INVALIDSIZES_EXCEEDMAXLENGTH:"stngs_invalidsizes_exceedmaxlength",STNGS_INVALIDSIZES_DUPLICATECHARACTERS:"stngs_invalidsizes_duplicatecharacters",STNGS_INVALIDSIZES_EMPTYFILED:"stngs_invalidsizes_emptyfiled",STNGS_INVALIDFBROWS_EMPTYFIELD:"stngs_invalidfbrows_emptyfield",STNGS_INVALIDFBROWS_INVALIDNUM:"stngs_invalidfbrows_invalidnum",STNGS_INVALIDFBROWS_OUTOFRANGE:"stngs_invalidfbrows_outofrange",STNGS_INVALID_OUTRIGHTTENOR:"stngs_invalid_outrighttenor",STNGS_DUPLICATE_OUTRIGHTTENOR:"stngs_duplicate_outrighttenor",STNGS_DUPLICATE_SWAPTENOR:"stngs_duplicate_swaptenor",STNGS_OUTRIGHTTENORS_CANTBE_EMPTY:"stngs_outrighttenors_cantbe_empty",STNGS_SWAPTENORS_CANTBE_EMPTY:"stngs_swaptenors_cantbe_empty",STNGS_INVALID_SWAPTENORS:"stngs_invalid_swaptenors",STNGS_SAME_NRANDFAR_SWAPTENORS:"stngs_same_nrandfar_swaptenors",STNGS_TRADESIZES:"stngs_tradesizes",STNGS_LADDERSIZES:"stngs_laddersizes",STNGS_MARKETRANGES:"stngs_marketranges",STNGS_MAXORDERSIZE:"stngs_maxordersize",STNGS_RFSEXPIRY:"stngs_rfsexpiry",STNGS_FULLBOOKROWS:"stngs_fullbookrows",STNGS_OKBUTTON:"stngs_okbutton",STNGS_CANCELBUTTON:"stngs_cancelbutton",STNGS_TRDNG_CONFIRM_TXT:"stngs_trdng_confirm_text",STNGS_TRDNG_CHECK_TEXT:"stngs_trdng_check_text",STNGS_TRDNG_TITLE_TEXT:"stngs_trdng_title_text",OCOOUO_ORD1_LABEL:"ocoouo_ord1_label",OCOOUO_ORD2_LABEL:"ocoouo_ord2_label",OP_STARTTIME:"op_starttime",OP_FULLFILL:"op_fullfill",OP_ABSOLUTE:"op_absolute",OP_PROPORTIONAL:"op_proportional",OP_ATPRICE:"op_atprice",OP_ORDER:"op_order",OP_TOTAL_EXEC_TIME:"op_total_exec_time",OP_NOWORSETHAN:"op_noworsethan",OP_PARAMETERS:"op_parameters",OP_CLIPINTERVAL:"op_clipinterval",OP_CLIPDETAILS:"op_clipdetails",FIXED:"fixed",RANDOM:"random",RANDOMSIZE:"op_randomizesize",RANDOMINT:"op_randomizeint",BETWEEN:"between",AND:"and",OP_CLIPSIZE:"op_clipsize",OP_TOTALCLIP:"op_totalclip",OP_SHOWSIZE:"op_showsize",RANGE:"range",OP_ACTIONEXPIRY:"op_actionexpiry",OP_CANCELBALANCE:"op_cancelbalance",FILL_BAL_AT_MKT:"fill_bal_at_mkt",PEGOFFSET:"pegoffset",PARAMETERS:"parameters",PEGDETAILS:"pegdetails",CLIPDETAILS:"clipdetails",PEGTO:"pegto",OFFSET:"offset",ERR_START_IN_ZERO:"err_start_in_zero",ERR_START_AT_SMALLER:"err_start_at_smaller",ERR_END_AT_SMALLER:"err_end_at_smaller",RFS_AMOUNT:"RFS_amount",RFS_NEARDATE:"RFS_neardate",RFS_NEARAMOUNT:"RFS_nearamount",RFS_FARDATE:"RFS_fardate",RFS_FARAMOUNT:"RFS_faramount",RFS_BUY:"RFS_buy",RFS_SELL:"RFS_sell",RFS_BIDSPOTRATE:"RFS_bidspotrate",RFS_OFFERSPOTRATE:"RFS_offerspotrate",RFS_EXPIRESIN:"RFS_expiresin",RFS_SECONDS:"RFS_seconds",RFS_ADDITIONALINSTRUCTIONS:"RFS_additionalinstructions",RFS_REMEMBERCHECK:"RFS_remembercheck",RFS_OUTRIGHTRATE:"RFS_outrightrate",RFS_OUTRIGHTPOINTS:"RFS_outrightpoints",RFS_NEARPOINTS:"RFS_nearpoints",RFS_FARPOINTS:"RFS_farpoints",RFS_NEARVLUEDATE:"RFS_nearvluedate",RFS_FARVALUEDATE:"RFS_farvaluedate",RFS_FIXINGDATE:"RFS_fixingdate",RFS_SWAPPOINTS:"RFS_swappoints",RFS_ADDITIONALSPREADS:"RFS_additionalspreads",RFS_GETPRICEBUTTON:"RFS_getpricebutton",RFS_CANCELBUTTON:"RFS_cancelbutton",RFS_ACCEPTPRICEBUTTON:"RFS_acceptpricebutton",RFS_SHOWLDRLINK:"RFS_showldrlink",RFS_HIDELDRLINK:"RFS_hideldrlink",RFS_INVALID_DATEFORMAT:"RFS_invalid_dateformat",RFS_INVALID_LEG_DATE:"RFS_invalid_leg_date",RFS_DATE_CANTBELESSTHAN_CURDATE:"RFS_date_cantbelessthan_curdate",RFS_NRDATE_CANTBE_GREATERTHAN_FARDATE:"RFS_nrdate_cantbe_greaterthan_fardate",RFS_NRDATE_CANTBE_SAMEAS_FARDATE:"RFS_nrdate_cantbe_sameas_fardate",TICKERENABLED:"tickerEnabled",FULLBOOKDISPLAYCOUNT:"FullbookDisplayCount",ORDER_CANTBECANCELLED:"order_cantbecancelled",TEST:"test",ORDERAMOUNT_EXCEED_WARNING:"orderamount_exceed_warning",VWAP:2,BEST_PRICE:3,FB_AGGR:0,FB_NON_AGGR:1,ORDER_PLACE_LIMIT:"order.place.Limit",ORDER_PLACE_STOP:"order.place.Stop",ORDER_PLACE_MARKET:"order.place.Market",ORDER_PLACE_STOPLIMIT:"order.place.StopLimit",RFS_SUBSCRIBE_SPOT:"rfs.Spot",RFS_SUBSCRIBE_OUTRIGHT:"rfs.Outright",RFS_SUBSCRIBE_SWAP:"rfs.Swap",RFS_SUBSCRIBE_NDF:"rfs.NDF",RFS_SUBSCRIBE_FWDFWD:"rfs.subscribe.FwdFwd",RFS_ACCEPT_OUTRIGHT:"rfs.accept.Outright",RFS_ACCEPT_SWAP:"rfs.accept.Swap",RFS_ACCEPT_SPOT:"rfs.accept.Spot",RFS_ACCEPT_FWDFWD:"rfs.accept.FwdFwd",RFS_NRTENOR_CANTBE_EMPTY:"RFS_nrtenor_cantbe_empty",RFS_FARTENOR_CANTBE_EMPTY:"RFS_fartenor_cantbe_empty",RFS_FARVALUEDATE_EQUALORBEFORE_NEAR:"RFS_farvaluedate_equalorbefore_near",RFS_NO_NDF_CURRENCY:"RFS_no_ndf_currency",RFS_REQFAILED:"RFS_reqfailed",RFS_1WAY:"RFS_1way",RFS_2WAY:"RFS_2way",CONTACT_SUPPORT_FORLOGIN:"contact_support_forlogin",LEGALAGREEMENT_DECLINED:"legalagreement_declined",BROWSERSUPPORT_SYSTEM_REQS:"browsersupport_system_reqs",MIN_BROWSER_REQS:"min_browser_reqs",BROWSERSUPPORT_TITLE_COMPONENT:"browsersupport_title_component",BROWSERSUPPORT_MIN_REQUIREMENTS:"browsersupport_min_requirements",BROWSERSUPPORT_PREFERED_REQ:"browsersupport_prefered_req",BROWSERSUPPORT_SCRRESOLUTION:"browsersupport_scrresolution",INTERNET_BROWSERS:"internet_browsers",I_E:"i_e",FIREFOX:"firefox",SAFARI:"safari",CHROME:"chrome",INTERNET_CONNECTION:"internet_connection",INTERNET_CON_DESCRIPTION:"internet_con_description",IDC_COPYRIGHT:"idc_copyright",CCY_NOT_SUPPORTED:"ccy_not_supported",CONNECTION_RESUMED:"connection_resumed",ORDER_ID:"order_id",TRADE_ID:"trade_id",BUYSELL:"buysell",SUBMISSION_TIME:"submission_time",ORDER_STATUS:"order_status",DEALT_CCY:"dealt_ccy",TRADER:"trader",LIMIT_RATE:"limit_rate",TYPE:"type",AVG_RATE:"avg_rate",STRATEGY:"strategy",ORDER_AMOUNT:"order_amount",DEALT_AMOUNT:"dealt_amount",MKT_RANGE:"mkt_range",FILLED_AMOUNT:"filled_amount",HIDDEN_DISPLAY:"hidden_display",CONTINGENCY:"contingency",LINKID:"linkid",TIME_IN_FORCE:"time_in_force",EXPIRY:"expiry",TRADE_TICKET:"trade_ticket",TRADE_TYPE:"trade_type",ORG:"org",ACCOUNT:"account",USER:"user",TRADE_DATE:"trade_date",VALUE_DATE:"value_date",EXECUTION_DATE_TIME:"execution_date_time",MAKER_TAKER:"maker_taker",TAKER:"taker",BUYS:"buys",SELLS:"sells",CCY_PAIR:"ccy_pair",SPOT_RATE:"spot_rate",COUNTERPARTY:"counterparty",COUNTERPARTY_ACCOUNT:"counterparty_account",COUNTERPARTY_ID:"counterparty_id",TENOR:"tenor",FORWARD_POINTS:"forward_points",RATE:"rate",NEAR_LEG:"near_leg",FAR_LEG:"far_leg",NEAR_FORWARD_POINTS:"near_forward_points",FAR_FORWARD_POINTS:"far_forward_points",NEAR_RATE:"near_rate",FAR_RATE:"far_rate",SWAP_POINTS:"swap_points",FIXING_DATE:"fixing_date",DT_GENERATED:"DT_generated",TIME_ZONE:"time_zone",DT_PATENTINFO:"DT_patentinfo",PRINT:"print_btn_val",CLOSE:"close_btn_val",COULDNT_RETRIEVE_TRADES:"couldnt_retrieve_trades",USER_NAME:"user_name",PASSWORD:"password",ORGANIZATION:"organization",LOGIN:"login",FORGOT_PASSWORD:"forgot_password",CREATE_NEW_PASSWORD:"create_new_password",ENTER_PASSWORD:"enter_password",NEW_PASSWORD:"new_password",CONFIRM_PASSWORD:"confirm_password",NEW_CONFIRMPASSWD_DONT_MATCH:"new_confirmpasswd_dont_match",NEW_PASSWORD_SENT:"new_password_sent",PLEASE_ENTER_VALID_CREDENTIALS:"please_enter_valid_credentials",SPROCESSING:"sProcessing",SLENGTHMENU:"sLengthMenu",SZERORECORDS:"sZeroRecords",SINFO:"sInfo",SINFOEMPTY:"sInfoEmpty",SINFOFILTERED:"sInfoFiltered",SINFOPOSTFIX:"sInfoPostFix",SEMPTYTABLE:"sEmptyTable",SSEARCH:"sSearch",SFIRST:"sFirst",SLAST:"sLast",SNEXT:"sNext",SINFOTHOUSANDS:"sInfoThousands",SLOADINGRECORDS:"sLoadingRecords",SPREVIOUS:"sPrevious",LOG_ERROR:40000};a.errorMsgs={"Request.Validation.Trade.ValueDate.NotBusinessDate":"Request.Validation.Trade.ValueDate.NotBusinessDate","Request.Validation.Trade.FixingDate.NotBusinessDate":"Request.Validation.Trade.FixingDate.NotBusinessDate","Request.Validation.Trade.FarLeg.ValueDate.NotBusinessDate":"Request.Validation.Trade.FarLeg.ValueDate.NotBusinessDate","Request.Validation.Trade.ValueDate.BeforeTradeDate":"Request.Validation.Trade.ValueDate.BeforeTradeDate","Request.Validation.Trade.FixingDate.BeforeTradeDate":"Request.Validation.Trade.FixingDate.BeforeTradeDate","Request.Validation.Trade.FarLeg.ValueDate.BeforeTradeDate":"Request.Validation.Trade.FarLeg.ValueDate.BeforeTradeDate","Request.Validation.Trade.ValueDate.LaterThanMaxTenor":"Request.Validation.Trade.ValueDate.LaterThanMaxTenor","Request.Validation.Trade.FixingDate.LaterThanMaxTenor":"Request.Validation.Trade.FixingDate.LaterThanMaxTenor","Request.Validation.Trade.FarLeg.ValueDate.LaterThanMaxTenor":"Request.Validation.Trade.FarLeg.ValueDate.LaterThanMaxTenor","Request.Validation.Trade.SettlementDateRule.NotDefined":"Request.Validation.Trade.SettlementDateRule.NotDefined","Request.Validation.Trade.ValueDate.Mismatch":"Request.Validation.Trade.ValueDate.Mismatch","Request.Validation.Tenor/ValueDate.Missing":"Request.Validation.Tenor_ValueDate.Missing","Request.Validation.Tenor/FixingDate.Missing":"Request.Validation.Tenor_FixingDate.Missing","Request.Validation.FarLeg.Tenor/ValueDate.Missing":"Request.Validation.FarLeg.Tenor_ValueDate.Missing","Request.Validation.Trade.No.FixingDate.Or.ValueDate":"Request.Validation.Trade.No.FixingDate.Or.ValueDate",INCORRECT_REQUEST_PARAMS:"INCORRECT_REQUEST_PARAMS","OrderAmount.LessThanMinTradeSize":"OrderAmount.LessThanMinTradeSize","Request.Validation.TradingDisabled":"Request.Validation.TradingDisabled",ERR_OTP_DISABLED:"ERR_OTP_DISABLED",ERR_OTP_NOT_RECEIVED:"ERR_OTP_NOT_RECEIVED",ERR_OTP_INTERNAL_SERVER_ERROR_:"ERR_OTP_INTERNAL_SERVER_ERROR_",ERR_2FA_CREDENTIALS_NOT_RECEIVED:"ERR_2FA_CREDENTIALS_NOT_RECEIVED",ERR_INVALID_OTP_FOR_2FA:"ERR_INVALID_OTP_FOR_2FA",ERR_INVALID_OTP:"ERR_INVALID_OTP",ERR_INVALID_PWD_FOR_2FA:"ERR_INVALID_PWD_FOR_2FA",ERR_INVALID_SESSION:"ERR_INVALID_SESSION",ERR_2FA_DISABLED:"ERR_2FA_DISABLED","ValidationError.Invalid.Password.NewPasswordsNotLongEnough":"ValidationError.Invalid.Password.NewPasswordsNotLongEnough","ValidationError.Invalid.Password.NewPasswordHasBeenUsedBefore":"ValidationError.Invalid.Password.NewPasswordHasBeenUsedBefore","ValidationError.Invalid.Password.NewPasswordsEqualsUsername":"ValidationError.Invalid.Password.NewPasswordsEqualsUsername","Invalid user/organization supplied":"invalid_user_or_org","Invalid password":"invalid_password","ValidationError.Invalid.Password.NewPasswordsContainsThreeConsIdenticalChars":"ValidationError.Invalid.Password.NewPasswordsContainsThreeConsIdenticalChars","ValidationError.Invalid.Password.NewPasswordsContainsDisAllowedPatterns":"ValidationError.Invalid.Password.NewPasswordsContainsDisAllowedPatterns","user.login.invalidIP":"user_login_invalidIP","Idc.Org.Logins.Disabled":"Idc_Org_Logins_Disabled"};return{getLabel:function(b,g,f,e,c){var d=jQuery.i18n.prop(b,g,f,e,c);return d},getProperty:function(c,b){var d=jQuery.i18n.prop(c);return(d==c)?b:d},has2FAPermission:function(){return jQuery.i18n.prop("html2fa")=="true"},getErrorMsg:function(b){return a.errorMsgs[b]==undefined?b:a.errorMsgs[b]}}})(jQuery);(function(a){a.fn.createLicenseAgreement=function(c){var d=a('<p style="text-align: center;font-weight: bold;">User Agreement</p><p style="text-align: center;font-weight: bold; color:#ff0000">IMPORTANT &#150; READ CAREFULLY</p><p><b>PLEASE READ THIS AGREEMENT IN ITS ENTIRETY BEFORE ACCESSING THE SYSTEM AND USING THE SERVICES THEREON.  BY CLICKING ON THE "Click to eSign" BUTTON BELOW, YOU, AND THE ENTITY WHICH YOU REPRESENT ("YOU") AGREE, AS OF THE DATE AND TIME YOU CLICK ON THE  "Click to eSign" BUTTON ("EFFECTIVE DATE") TO BE BOUND BY ALL OF THE TERMS AND CONDITIONS OF THIS AGREEMENT. IF YOU DO NOT AGREE TO ALL THE TERMS AND CONDITIONS OF THIS AGREEMENT, THEN DO NOT CLICK ON THE "Click to eSign" BUTTON AND YOU WILL NOT BE ABLE TO USE THE SERVICES. THIS AGREEMENT REPRESENTS THE ENTIRE AGREEMENT RELATING TO YOUR USE OF THE SYSTEM. </b></p><p>In consideration of the mutual promises herein, the receipt and sufficiency of which is hereby acknowledged by the Parties, the Parties agree as follows:</p><ol type="1"><li><b>Definitions</b><ol type="A"><li>"Credit Facilitator" means an entity that is approved by Integral and provides credit to You for the purpose of facilitating Your transactions on the System.</li><li>"FX" means foreign exchange and/or foreign currency.</li><li>"System" means the online platform containing FX Inside Prime and FX Inside Professional, access to which may enable users to execute FX transactions with counterparties using an Credit Facilitator.</li><li>"Integral Operational Materials" means System related guidelines published by Integral from time to time which are incorporated herein by reference and which may be amended by Integral in its sole discretion.</li></ol></li><li><b>Access Grant.</b> Subject to the terms of this Agreement, Integral grants to You a non exclusive, revocable, non transferable, non-sublicensable and limited license for You to access the System solely for Your own internal business purpose of conducting FX transactions on the System with counterparties using an Credit Facilitator.  In the event of improper activity and/or requirement of a counterparty or Credit Facilitator, Integral may suspend, restrict or place limits on Your ability to trade with any counterparty through the System without notice, in its sole discretion.  Further, any counterparty may, in its sole discretion, refuse to act on any of your requests, instructions or transactions.</li><li><b>Use of the System.</b>You represent and warrant on a continuing basis as long as You are authorized to access and use the System that:<ol type="a"><li>	You shall access and use the System only in conformity with all applicable statutes, rules, and regulations, and the interpretations of any regulatory agency with jurisdiction ("Applicable Law") and Integral Operational Materials.  You are responsible for all instructions or other communications (including bids and offers) made through the System associated with Your identity and/or password, and such instructions shall be binding on You. You transmit instructions to counterparties through the System at your own risk. </li><li>	 You have in place all security, systems and compliance procedures required to prevent violation of Applicable Law and unauthorized access, use or misuse of the System.  You will not alter, delete, disable or otherwise circumvent any security device. You will notify Integral immediately if you become aware of any unauthorized access to or use of the System.  </li><li> You will provide any counterparty with any information or documentation such counterparty requires in order to complete transactions with You through FX Inside Prime.  </li><li>You will not attempt to access information or applications that You have not been authorized to use by Integral, and, if You inadvertently gain such access, You agree not to use or disseminate, reproduce, redistribute or decompile any such information or applications.</li><li>You shall not use or access the System for any reason other than to trade with counterparties through the System.  Without limiting the foregoing, You shall not transmit, publish or otherwise disseminate any prices or other content of the System.</li><li>You will not use the System or any feature of the System to post or transmit inappropriate information.</li></ol></li><li><b>Representations.</b><br />Each party represents and warrants to the other that it has the right and full corporate power to enter into this Agreement and that this Agreement creates legal, valid and binding obligations on it which are enforceable against it in accordance with its terms.  Further, Integral represents that the System does not infringe on any patent, trademark, or copyright of a third party.</li><li><b>Reservation of Rights.</b> Integral has exclusive ownership of and rights to FX Inside Prime and the System, its use and the content of the System, as well as all related copyrights, trademarks, service marks, patent rights, and trade secrets and any other intellectual property rights therein (registered or unregistered) including any applications, anywhere in the world.  You will not (i) sell, lease, transfer, make derivative works from, reproduce, redistribute or otherwise disseminate all or any part of FX Inside Prime, the System, its components or its content, or (ii) copy, alter, decompile or reverse engineer FX Inside Prime, the System or any of its components.  Further, You will not remove, obscure or change any copyright or other notices or legends contained in FX Inside Prime, the System or any of its components.</li><li><b>DISCLAIMER OF WARRANTY AND LIMITATION OF LIABILITY. </b>FX Inside Prime AND THE SYSTEM AND ALL CONTENT ARE PROVIDED "AS IS." NEITHER INTEGRAL NOR ANY CREDIT FACILITATOR MAKES ANY REPRESENTATION OR WARRANTY, EXPRESS OR IMPLIED.  INTEGRAL DISCLAIMS ANY OBLIGATION TO KEEP FX Inside Prime AND/OR THE SYSTEM SECURE OR FREE OF ERRORS OR VIRUSES OR TO MAINTAIN UNINTERRUPTED ACCESS. INTEGRAL MAY STOP PRODUCING OR UPDATING ALL OR ANY PART OF FX Inside Prime AND/OR THE SYSTEM.  INTEGRAL SHALL NOT HAVE ANY LIABILITY TO YOU OR ANY THIRD PARTY FOR ANY LOSS OF PROFITS, LOSS OF DATA, INDIRECT, SPECIAL OR CONSEQUENTIAL LOSS OR OTHER DAMAGE OR LIABILITY ARISING OUT OF, OR IN CONNECTION WITH, THE PROVISION OR USE OF (OR ANY INABILITY TO USE) FX Inside Prime OR THE SYSTEM, WHETHER IN CONTRACT, TORT (INCLUDING NEGLIGENCE), STATUTE OR OTHERWISE, EVEN IF INTEGRAL HAS BEEN ADVISED OF THE POSSIBILITY THEREOF.  EXCEPT WITH RESPECT TO MISUSE OF INTEGRAL\'S INTELLECTUAL PROPERTY, INDEMNIFICATION LIABILITY OR BREACH OF CONFIDENTIALITY BY CUSTOMER, THE  TOTAL MAXIMUM  LIABILITY FOR  ANY LOSS OR DAMAGES HOWSOEVER CAUSED  AND IN RELATION TO ANY CLAIM OR SERIES OF CLAIMS RELATING TO THIS AGREEMENT SHALL BE LIMITED TO THE FEES PAID DURING THE 12 MONTHS IMMEDIATELY PRECEDING THE CLAIM.</li><li><b>Non-Infringement and Indemnification.</b>  Integral shall defend You against claims that the System, when used as permitted by this Agreement, infringes any patent or copyright, or other registered intellectual property right of any third party, provided that you promptly notify Integral of such claim, allow Integral to have sole control of the defense and settlement thereof and fully cooperate with Integral in such defense at your own expense. No other indemnity of any kind is provided by Integral with respect to any other matter. You shall indemnify, defend and hold harmless Integral from any and all claims, liabilities, damages, costs and expenses (including attorneys\' fees) arising from Your intellectual property infringement and/or breach of this Agreement and/or Your use of the System and/or any part thereof including, without limitation, trading losses, the accuracy or completeness of any quote, failure to deliver or complete a transaction and any failure to comply with any Applicable Law or regulation.</li><li><b>Confidentiality and Use of Data.</b>All (oral or written) business, technical, financial and other information provided by a party ("disclosing party") to the other party ("receiving party") under this Agreement shall be deemed "Confidential Information". The confidentiality obligations set forth herein shall not apply to information disclosed by the disclosing party that the receiving party can prove by admissible evidence (i) is or has become generally publicly known through no fault of the receiving party, (ii) was in its possession or known by it, without restriction, prior to receipt from the disclosing party, (iii) was rightfully disclosed to it by a third party without restriction, (iv) was independently developed without use of or access to any Confidential Information of the disclosing party, or (v) was required to be disclosed by court order, provided that the receiving party has promptly notified the disclosing party about such requirement, has attempted to limit such disclosure and to obtain confidential treatment or a protective order, and has allowed the disclosing party to participate in any such undertakings and proceedings.  All Confidential Information shall be held in confidence by the receiving party and not disclosed or used by the receiving party except as permitted by this Agreement or as expressly authorized in writing by the other party.  Each party shall use at least the same degree of care to protect the disclosing party\'s Confidential Information as it uses to protect its own confidential information of like nature, but in no circumstances less than reasonable care.  Notwithstanding the foregoing, You acknowledge and agree that any content transmitted through or with the assistance of FX Inside Prime and/or the System may be used by Integral so long as personal identifiers of individuals are removed from such content.  Also, Integral may publicly announce and use in its marketing materials the existence (but not the terms) of this Agreement and the Your role with respect to the System.   Further, Integral shall not be liable for the privacy of e-mail addresses, registration and identification information, communications or any other content stored on Integral\'s equipment, transmitted over networks accessed by the System, or otherwise connected with Your use of FX Inside Prime or the System.  Integral shall not be liable for the loss, corruption of, or incompleteness, of data, content, or any other information provided to Integral or downloaded to or from the System by You.</li><li><b>Data Protection.  </b> If any personal data (including sensitive personal data) belonging to Your users or Your customers, or other individuals, is provided to Integral by or through You, You represent and warrant that such persons are aware of and explicitly consent to the use of such personal data by Integral and You agree to indemnify Integral and any counterparty receiving such information against any loss or damage either may incur arising out of a breach by You of this representation and warranty; and You further acknowledge and agree that Integral may monitor Your use of the System and Your use of e-mail and/or instant messaging in connection with the System and may monitor and tape record telephone conversations with You concerning the System.</li><li><b>Term and Termination.</b><ol type="A"><li>Term:   This Agreement shall commence on the Effective Date and shall continue in effect for one year  ("the Initial Term") unless terminated sooner in accordance with the terms herein.  Thereafter, the Agreement shall automatically renew for one or more further one year consecutive terms ("Renewal Term") unless terminated by either party giving written notice not less than sixty (60) days ("Notice Period") prior to the end of the Initial or Renewal Term.</li><li>Termination:  This Agreement may be terminated for cause immediately by a party in the event that the other party:  1) materially breaches this Agreement (e.g. failure to pay) and such breach remains uncured for thirty (30) days after receiving written notice, or 2) is the subject of a voluntary petition in bankruptcy or any voluntary proceeding relating to insolvency, receivership, liquidation, or composition for the benefit of creditors (each a "Bankruptcy").  Upon termination of this Agreement (i) Your right to access and/or use FX Inside Prime and the System as described herein shall cease; (ii) You shall cease to use all copies of any Integral product, software or documentation; and (iii) You shall return to Integral all copies of said material in Your possession, custody or control.</li></ol></li><li><b>Arbitration. </b> The Parties agree that any dispute or controversy arising out of or relating to any interpretation, construction, performance or breach of this Agreement or arising out of or relating to this Agreement shall be settled by arbitration in San Francisco, California in accordance with the rules then in effect of the American Arbitration Association. The decision of the arbitrator shall be final, conclusive and binding on the parties to the arbitration and shall include an award of costs and fee including, without limitation, attorneys fees and costs to the prevailing party.  Judgment, including costs and fees, may be entered on nothing more than a copy of the arbitrator\'s decision in any court having jurisdiction over the party. Notwithstanding the foregoing, it is hereby understood and agreed that damages may be an inadequate remedy in the event of a breach by either party of any of said covenants, including without limitation those with respect to confidentiality, and that any such breach will cause great and irreparable injury and damage, and thus accordingly, each party agrees that the other party shall be entitled, without waiving any additional rights or remedies otherwise available at law or in equity or by statute, and without need of posting a bond, to injunctive and other equitable relief from a court of competent jurisdiction in the event of a breach or intended or threatened breach by the other of any of said covenants.</li><li><b>Miscellaneous. </b> Integral shall not have any liability for any failure to perform or delay in performing its obligations under this Agreement due to any Act of God, act of governmental authority, change in law or regulation, war, criminal act, fire, explosion, earthquake, flood, weather condition, power failure, transportation or other accident beyond its reasonable control. This Agreement along with the Dealing Rules constitutes the entire agreement between the parties for access to FX Inside Prime and the System and supersedes all proposals, negotiations and discussions, oral or written, relating to access to and use of FX Inside Prime and the System. Neither Party may assign this Agreement without the express written consent of the other party which consent shall not be unreasonably withheld or delayed.  The failure of either Party to exercise in any respect any right provided for herein shall not be deemed a waiver of any further rights hereunder. If any provision of this Agreement shall be adjudged by any court of competent jurisdiction to be unenforceable or invalid, that provision shall be limited or eliminated to the minimum extent necessary so that this Agreement shall otherwise remain in full force and effect and enforceable. Each and every notice and consent required or otherwise given or made under this Agreement shall be in writing, and shall be deemed given or made when personally delivered, when sent by confirmed fax, or three days after being sent by prepaid certified or registered mail to the last known address or fax number of the party.   The headings and captions used in this Agreement are used for convenience only and are not to be considered in construing or interpreting this Agreement. This Agreement shall be governed by and construed in accordance with the laws of the state of California, without regard to California\'s conflict of laws or principles. Any suit brought under this Agreement shall be brought in the state or federal court sitting in San Francisco, California. Neither party shall be deemed to be an employee, agent, partner or legal representative of the other for any purpose, and neither shall have any right, power, or authority to create any obligation or responsibility on behalf of the other. The disclaimers and limitations of liabilities made by Integral in sections 7 and 13 are also made for the benefit of Your counterparties on the System and You acknowledge and agree that such counterparties are intended third party beneficiaries of these provisions and shall have the ability to enforce their rights accordingly as if it were a party to this Agreement in place of Integral.  EACH PARTY RECOGNIZES AND AGREES THAT THE WARRANTY AND LIABILITY DISCLAIMERS AND REMEDY LIMITATIONS IN THIS AGREEMENT ARE A MATERIAL BARGAINED FOR BASIS OF THIS AGREEMENT AND THAT THEY HAVE BEEN TAKEN INTO ACCOUNT AND REFLECTED IN DETERMINING THE CONSIDERATION TO BE GIVEN BY EACH PARTY UNDER THIS AGREEMENT AND IN THE DECISION BY EACH PARTY TO ENTER INTO THIS AGREEMENT. </li></ol><p><b>By clicking the "Click to eSign" button you acknowledge that you have read, understand and agree to be bound by the terms above.  Further, the person clicking the "Click to eSign" button represents that he/she is an authorized representative with the power and authority to accept and bind the entity entering into this Agreement to the terms of this Agreement.</b></p>');var b=a('<div class="agreementContainer"><div class="agreementLogo"></div><div id="lacontent" class="normal"></div><div class="agreementAction"><input id="read" type="checkbox" class="agreementCheck" />I have read the above<br /><div class="agreement"><button class="laagree" id="accept">I Agree</button><button class="ladecline" id="decline">I Decline</button><button class="laprint" id="print">Print</button></div></div></div>');this.append(b);var e=integral.getLegalAgreement();b.find("#lacontent").html(e.legalAgreement);b.jqm({modal:true,overlay:0});b.jqmShow();b.draggable({cancel:"div[id^=lacontent]"});b.find("#accept").attr("disabled","disabled");b.find("#read").click(function(){if(a(this).is(":checked")){b.find("#accept").prop("disabled",false)}else{b.find("#accept").prop("disabled",true)}});b.find("#accept").click(function(){integral.acceptLegalAgreement(function(){integral.log("acceptLegalAgreement:Loading main panel.");loadMainPanel(c)});b.jqmHide();b.remove()});b.find("#decline").click(function(){integral.log("License agreement declined. Logging out.");integral.logout(undefined,"License agreement declined");b.jqmHide();b.remove();a("#fxlogin").find("#logincontainer").remove();a("#fxlogin").createFxLogin(a.props.redirectUrl,a.props.brandedPath,true);a("#errormessagelogin").html(integral.common.getLabel(a.consts.LEGALAGREEMENT_DECLINED))});b.find("#print").click(function(){if(integral.ui.isBrowserIE()){a("#lacontent").printElement({printMode:"popup",leaveOpen:false})}else{a("#lacontent").printElement()}})}})(jQuery);(function(a){a.fn.showSupportedBrowsers=function(){var b=a('<div class="requirementsbox"><div id="logincontainer"><div class="reqheading">'+integral.common.getLabel(a.consts.BROWSERSUPPORT_SYSTEM_REQS)+'</div><div style="reqtable"><div class="reqdescription">'+integral.common.getLabel(a.consts.MIN_BROWSER_REQS)+'</div><table class="reqtable"><thead><tr><th title="'+integral.common.getLabel(a.consts.BROWSERSUPPORT_TITLE_COMPONENT)+'" class="thbg">'+integral.common.getLabel(a.consts.BROWSERSUPPORT_TITLE_COMPONENT)+'</th><th title="'+integral.common.getLabel(a.consts.BROWSERSUPPORT_MIN_REQUIREMENTS)+'" class="thbg">'+integral.common.getLabel(a.consts.BROWSERSUPPORT_MIN_REQUIREMENTS)+'</th><th title="'+integral.common.getLabel(a.consts.BROWSERSUPPORT_PREFERED_REQ)+'" class="thbg">'+integral.common.getLabel(a.consts.BROWSERSUPPORT_PREFERED_REQ)+'</th></tr></thead><tbody><tr class="reqtrodd"><td class="reqcomponent">'+integral.common.getLabel(a.consts.BROWSERSUPPORT_SCRRESOLUTION)+'</td><td>1600 x 1200</td><td>1600 x 1200+</td></tr><tr><td class="reqcomponent">'+integral.common.getLabel(a.consts.INTERNET_BROWSERS)+'</td><td><ul style="margin-left:15px"><li style="margin-left:0px">'+integral.common.getLabel(a.consts.I_E)+" 7</li><li>"+integral.common.getLabel(a.consts.FIREFOX)+" 4</li><li>"+integral.common.getLabel(a.consts.SAFARI)+" 4</li><li>"+integral.common.getLabel(a.consts.CHROME)+'</li></ul></td><td><ul style="margin-left:15px"><li>'+integral.common.getLabel(a.consts.I_E)+" 9</li><li>"+integral.common.getLabel(a.consts.FIREFOX)+" 9.0</li><li>"+integral.common.getLabel(a.consts.SAFARI)+" 5</li><li>"+integral.common.getLabel(a.consts.CHROME)+' 16</li></ul></td></tr><tr class="reqtrodd"><td class="reqcomponent">'+integral.common.getLabel(a.consts.INTERNET_CONNECTION)+'</td><td colspan="2">'+integral.common.getLabel(a.consts.INTERNET_CON_DESCRIPTION)+'</td></tr></tbody></table></div><div id="logincopyright">'+integral.common.getLabel(a.consts.IDC_COPYRIGHT)+"</div></div></div>");this.append(b)}})(jQuery);