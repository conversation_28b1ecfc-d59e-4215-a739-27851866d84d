if(!Array.prototype.indexOf){Array.prototype.indexOf=function(searchElement,fromIndex){var k;if(this==null){throw new TypeError('"this" is null or not defined');}var O=Object(this);var len=O.length>>>0;if(len===0){return -1;}var n=+fromIndex||0;if(Math.abs(n)===Infinity){n=0;}if(n>=len){return -1;}k=Math.max(n>=0?n:len-Math.abs(n),0);while(k<len){if(k in O&&O[k]===searchElement){return k;}k++;}return -1;};}if(typeof String.prototype.startsWith!="function"){String.prototype.startsWith=function(str){return this.indexOf(str)==0;};}if(typeof String.prototype.endsWith!=="function"){String.prototype.endsWith=function(suffix){return this.indexOf(suffix,this.length-suffix.length)!==-1;};}if(typeof integral=="undefined"){var integral={};}integral=(function($){var authToken=null;var iframeInitialized=false;var callBackOnRateUpdate;var rmmCallBackOnRateUpdate;var callBackOnOrderUpdate;var rmmCallBackOnOrderBlotterUpdate;var callBackOnPositionUpdate;var rmmCallBackOnPositionBlotterUpdate;var callBackOnTickerUpdate;var callBackOnNotification;var rmmCallBackOnNotification;var callBackOnGetPrice;var callBackOnRfsUpdate;var rmmCallBackOnRfsUpdate;var callBackOnRFSFBGetPrice;var callBackOnRfsFBRateUpdate;var rmmCallBackOnRfsFBRateUpdate;var callBackOnRfsAccept;var callBackOnError;var ccyPairRefData={};var userName="";var userOrg="";var lastMsgRcvd=0;var lastSecPoll=0;var lastMsgsCount=0;var loginPage;var connectionStatus="";var secConnectionStatus="";var loginTime="";var latencyArr=new Array();var heartBeatInterval;var missHbRetries;var hbRequestTimeout;var minHbInterval=2000;var hbfailure=0;var userPlusOrg="";var ssFailureCnt=0;var ssExists=false;var secondaryServiceInit=false;var time=null;var loggedOut=false;var primaryPollRequest;var secondaryPollRequest;var renewTokenRequest;var lastPrimHBTime=0;var pollCount=0;var secPollCount=0;var streaming=false;var ackIdList=new Array();var secAckIdList=new Array();var lastActiveOrderPoll=0;var orderBlotterLoaded=false;var tradeBlotterLoaded=false;var replayOrderCache={};var unityEnabled=false;var unity;var unityPingTime=0;var iosocket;var priPollInt;var secPollInt;var cdqInProgress=0;var cdQueryRefMap={O:0,T:0,B:0};var unityLoggedIn=false;var unityTickerInitialized=false;var orgTradesForDateTmOut;function startPoller(){var timeNow=new Date().getTime();try{if(!unityEnabled){if(!streaming&&(timeNow-lastMsgRcvd)>priPollInt){getMessages();}if(ssExists&&((timeNow-lastSecPoll)>secPollInt)){getSecondaryMessages();}}else{if((timeNow-unityPingTime)>10000){pingUnity();}}if((timeNow-lastPrimHBTime)>heartBeatInterval){sendHeartbeat();}if(((timeNow-lastActiveOrderPoll)>integral.ui.getOrderSyncInterval())&&integral.ui.isOrderSyncEnabled()){lastActiveOrderPoll=timeNow;integral.ui.reconnectedNetwork();}}catch(ex){}setTimeout(startPoller,200);}function getMessages(){var serverLoginTime=$.readCookie("LOGIN_TIME");if(loginTime!=serverLoginTime){var reason="getMessages:Redirecting to login page as the server login time is not same as client login time";integral.log(reason,$.consts.LOG_ERROR);redirectToLoginPage(reason);return;}if(!unityEnabled){if(streaming){getStreamingMessages();}else{getPollingMessages();}}}function loginUnityServer(){integral.log("Login to Unity Server.");iosocket=io.connect("/");integral.setUnity(iosocket);var authParams={userName:userName,userOrg:userOrg,authToken:$.readCookie("SSO_TOKEN")};iosocket.emit("authrequest",authParams);iosocket.on("authresponse",function(msg){if(msg=="SUCCESS"){integral.log("Unity server authrequest is successful.");unityLoggedIn=true;if(!unityTickerInitialized&&(integral.ui.enableTickers()||integral.ui.isFXInsideRT())){integral.tickers.startupTicker();}}else{integral.log("Unity server authrequest failed.");}});iosocket.on("invalidtoken",function(){integral.log("Invalid token received by Unity server.");integral.ui.logout("Logging out as invalid token received by Unity server.");});iosocket.on("unity_error",function(){integral.log("Unity error occurred");integral.ui.logout("Logging out as there was an error on the Unity server");});iosocket.on("disconnect",function(){if(!loggedOut){integral.log("Socket info: disconnect event received when user still logged in.");integral.ui.logout("Logging out as disconnect event was received from Unity server");}});iosocket.on("reconnecting",function(){integral.log("Socket info: reconnecting.");});iosocket.on("connect_error",function(){integral.log("Socket error: connect_failed.");integral.ui.logout("Logging out as connecting to Unity server failed ");});iosocket.on("error",function(err){integral.log("Socket error: "+err);integral.ui.logout("Logging out as there was an error in Unity server");});iosocket.on("reconnect_failed",function(){integral.log("Socket error: reconnect_failed.");integral.ui.logout("Logging out as there was an error in re-connecting to Unity server");});iosocket.on("invalidparams",function(){integral.log("Invalid login parameters received by Unity server.");integral.ui.logout("Logging out as invalid login parameters received by Unity server. User: "+userName+". Org: "+userOrg);});iosocket.on("fxBenchmarkSubscribeResponse",function(){integral.log("fxBenchmarkSubscribeResponse received by Unity server.");});iosocket.on("fxBenchmarkUnSubscribeResponse",function(){integral.log("fxBenchmarkSubscribeResponse received by Unity server.");});iosocket.on("tickerSubscribeResponse",function(){integral.log("tickerSubscribeResponse received by Unity server.");});iosocket.on("tickerUnSubscribeResponse",function(){integral.log("tickerSubscribeResponse received by Unity server.");});iosocket.on("message",function(msg){try{integral.p(JSON.parse(msg),"socket");}catch(ex){integral.log("Error in parsing the message from socket server "+msg);}});iosocket.on("deal",function(msg){try{var socketmsg=msg.split("\u0001");integral.p(JSON.parse(socketmsg[1]),"socket");if(unityLoggedIn){iosocket.emit("ackDeal",socketmsg[0]);}}catch(ex){integral.log("Error in parsing the message from socket server "+msg);}});iosocket.on("ackDeal",function(msg){try{var socketmsg=msg.split("\u0001");integral.p(JSON.parse(socketmsg[1]),"socket");if(unityLoggedIn){iosocket.emit("ackDeal",socketmsg[0]);}}catch(ex){integral.log("Error in parsing the message from socket server "+msg);}});iosocket.on("testresponse",function(msg){if(msg!=undefined){var latency=new Date().getTime()-msg.time;latencyArr[latencyArr.length]=(latency>0?latency:0);}});}function logoutUnityServer(msg){var authParams={userName:userName,userOrg:userOrg,authToken:$.readCookie("SSO_TOKEN")};unityLoggedIn=false;iosocket.emit("logout",authParams,msg);}function getSecondaryMessages(){var serverLoginTime=$.readCookie("LOGIN_TIME");if(loginTime!=serverLoginTime){var reason="getSecondaryMessages:Redirecting to login page as the server login time is not same as client login time";integral.log(reason,$.consts.LOG_ERROR);redirectToLoginPage(reason);return;}doSecondaryPoll();}function getStreamingMessages(){jQuery.support.cors=true;var srvrUrl=window.location.href;var urlArr=srvrUrl.split(":");var apiMsgUrl="";if(srvrUrl.indexOf("8080")!=-1){apiMsgUrl=urlArr[0]+":"+urlArr[1]+":8878/jmsproxy?cmd=U&f=4&ts="+new Date().getTime();}else{apiMsgUrl="/jmsproxy?cmd=U&f=4&ts="+new Date().getTime();}$("#streamingframe").attr("src",apiMsgUrl);}function getPollingMessages(){var timeNow=new Date().getTime();if(primaryPollRequest!=undefined&&primaryPollRequest.readyState!=4){if((timeNow-lastMsgRcvd)<3000){return;}else{primaryPollRequest.abort();}}jQuery.support.cors=true;var srvrUrl=window.location.href;var urlArr=srvrUrl.split(":");var apiMsgUrl="";var reqId="H"+(pollCount++);if(pollCount>=100000){pollCount=0;}if(ackIdList.length>0){var ackIds="";for(var j=0;j<ackIdList.length;j++){if(ackIds.length>0){ackIds+=",";}ackIds+=ackIdList[j];}ackIdList=new Array();}if(srvrUrl.indexOf("8080")!=-1){apiMsgUrl=urlArr[0]+":"+urlArr[1]+":8878/jmsproxy?cmd=P&f=4&wMs=1000&ts="+timeNow+"&lmc="+lastMsgsCount+"&un="+userPlusOrg+"&reqId="+reqId+"&ackIdList="+(ackIds!=undefined?ackIds:"");}else{apiMsgUrl="/jmsproxy?cmd=P&f=4&wMs=1000&ts="+timeNow+"&lmc="+lastMsgsCount+"&un="+userPlusOrg+"&reqId="+reqId+"&ackIdList="+(ackIds!=undefined?ackIds:"");}lastMsgsCount=0;var latency;lastMsgRcvd=timeNow;primaryPollRequest=$.ajax({url:apiMsgUrl,type:"GET",dataType:"json",cache:false,timeout:3000,reqId:reqId,contentType:"application/json; charset=UTF-8",success:function(data){lastMsgsCount=data.length;var latency=new Date().getTime()-this.sentTime;var wt=integral.p(data);latency=latency-(wt>0?wt:0);latencyArr[latencyArr.length]=(latency>0?latency:0);resetHBFailureCnt();if(connectionStatus=="broken"){connectionStatus="resumed";integral.ui.reconnectedNetwork();integral.ui.replaceSystemAlert(integral.ui.getLabel($.consts.CONNECTION_RESUMED));}},beforeSend:function(){this.sentTime=new Date().getTime();},error:function(data,t,err){integral.log("Error occured in Primary polling request : "+reqId+" : "+data.responseText+", Status = "+data.status+" .reason:"+t+" .error:"+err+". sent time : "+this.sentTime+". time taken:"+(new Date().getTime()-this.sentTime));if(t=="timeout"){getMessages();return;}else{if(t=="abort"){return;}}if((data.responseText!=null)){if((data.responseText.indexOf("Not Logged in")!=-1||data.responseText.indexOf("Error=401")!=-1)){var reason="getPollingMessages:Redirecting to login page, Error: "+data.responseText+", Status = "+data.status;integral.log(reason,$.consts.LOG_ERROR);redirectToLoginPage(reason);}}if(data.statusText=="error"){if(!loggedOut){connectionStatus="broken";if(data.responseText!=null&&data.responseText.indexOf("503 Service Temporarily Unavailable")!=-1){integral.ui.replaceSystemAlert(integral.ui.getLabel("service_down"));}else{integral.ui.replaceSystemAlert(integral.ui.getLabel("lost_network_connection"));}}}}});if(!ssExists&&$.readCookie("jsmssid")!=undefined&&$.readCookie("jsmssid")!=""){integral.enableSecondaryServer();}}function requestRenewToken(){var timeNow=new Date().getTime();integral.log("Sending renew token request. ts="+timeNow);if(renewTokenRequest!=undefined&&renewTokenRequest.readyState!=4){renewTokenRequest.abort();integral.log("Renew token request already active ");}renewTokenRequest=$.ajax({url:'/fxi/fxiapi/sso/token/renew?un="'+userPlusOrg+'"&ts="'+timeNow+'"',type:"GET",dataType:"json",cache:false,timeout:5000,contentType:"application/json; charset=UTF-8",success:function(data){integral.log("Renew Token Successful.");},error:function(data,t,err){integral.log("Error occured in Renew Token request :"+data.responseText+", Status"+data.status+" reason:"+t+" .error:"+err);if(data.status==0&&t!="abort"){requestRenewToken();}}});}function doSecondaryPoll(){var timeNow=new Date().getTime();if(secondaryPollRequest!=undefined&&secondaryPollRequest.readyState!=4){if((timeNow-lastSecPoll)<5000){return;}else{secondaryPollRequest.abort();}}jQuery.support.cors=true;var srvrUrl=window.location.href;var urlArr=srvrUrl.split(":");var apiMsgUrl="";var reqId="H"+(secPollCount++);if(secPollCount>=100000){secPollCount=0;}if(secAckIdList.length>0){var ackIds="";for(var j=0;j<secAckIdList.length;j++){if(ackIds.length>0){ackIds+=",";}ackIds+=secAckIdList[j];}secAckIdList=new Array();}if(srvrUrl.indexOf("8080")!=-1){apiMsgUrl=urlArr[0]+":"+urlArr[1]+":8878/ss/jmsproxy?cmd=P&f=4&wMs=1000&ts="+timeNow+"&lmc="+lastMsgsCount+"&un="+userPlusOrg+"&reqId="+reqId+"&ackIdList="+(ackIds!=undefined?ackIds:"");}else{apiMsgUrl="/ss/jmsproxy?cmd=P&f=4&wMs=1000&ts="+timeNow+"&lmc="+lastMsgsCount+"&un="+userPlusOrg+"&reqId="+reqId+"&ackIdList="+(ackIds!=undefined?ackIds:"");}lastMsgsCount=0;var latency;lastSecPoll=timeNow;secondaryPollRequest=$.ajax({url:apiMsgUrl,type:"GET",dataType:"json",cache:false,timeout:5000,reqId:reqId,contentType:"application/json; charset=UTF-8",sentTime:timeNow,success:function(data){integral.p(data,"secondary");if(secConnectionStatus=="broken"){secConnectionStatus="resumed";integral.log("Connection to Secondary Server is resumed");}ssFailureCnt=0;},error:function(data,t,err){integral.log("Error occured in Secondary polling request : "+reqId+" : "+data.responseText+", Status = "+data.status+" failed :"+(ssFailureCnt+1)+" times. reason:"+t+" .error:"+err+". sent time : "+this.sentTime+". time taken:"+(new Date().getTime()-this.sentTime));if(t=="timeout"){getSecondaryMessages();return;}else{if(t=="abort"){return;}}ssFailureCnt++;if((data.responseText!=null)){integral.log("Secondary Server Polling Failed, reason:"+data.status);if(data.status!=404&&data.status!=401){if(ssFailureCnt<=3){setTimeout(getSecondaryMessages,50);}}}if(data.statusText=="error"){secConnectionStatus="broken";if(data.responseText!=null&&data.responseText.indexOf("503 Service Temporarily Unavailable")!=-1){integral.log("Secondary Server Polling Failed, Reason: Service down errorcode"+data.responseText);}else{integral.log("Secondary Server Polling Failed, ,Reason:  Lost network connection"+data);}}}});}function enableOrgViewDropdown(cdqRefId){if(cdqResponseType(cdqRefId)=="T"){if(!integral.ui.isTraderView()){integral.ui.enableOrgViewDropdown();}}if(cdqResponseType(cdqRefId)=="O"){if(!integral.ui.isOrderTraderView()){integral.ui.enableOrderOrgViewDropdown();}}if(cdqResponseType(cdqRefId)=="B"){if(!integral.ui.isOrderTraderView()){integral.ui.enableOrderOrgViewDropdown();}}}function getValueForKey(key,arr){for(var i=0;i<arr.length;i++){if(key==arr[i].key){return arr[i].value;}}return null;}function pingUnity(){unityPingTime=new Date().getTime();iosocket.emit("testrequest",{time:unityPingTime});}function sendHeartbeat(){lastPrimHBTime=new Date().getTime();try{var minLt="",maxLt="",sumLt=0,avgLt="";var copyLtArr=$.extend(new Array(),latencyArr);latencyArr=new Array();for(var i=0;i<copyLtArr.length;i++){var lt=copyLtArr[i];minLt=(minLt==""||minLt>lt)?lt:minLt;maxLt=(maxLt==""||maxLt<lt)?lt:maxLt;sumLt=sumLt+lt;}if(sumLt!=0){avgLt=Math.round(sumLt/copyLtArr.length);}var hbUrl="/fxi/fxiapi/heartbeat?l="+minLt+"&a="+avgLt+"&h="+maxLt+"&c="+copyLtArr.length+"&un="+userPlusOrg;$.ajax({url:hbUrl,type:"GET",dataType:"json",cache:false,timeout:hbRequestTimeout,contentType:"application/json; charset=UTF-8",success:function(data){if(data&&data.responseTuples){var response=data.responseTuples;if(getValueForKey("SSO_TOKEN_RENEW",response)!=undefined){requestRenewToken();}time=getValueForKey("serverUTCTime",response);if(time!=undefined){integral.ui.setServerTime(time);}}if(unityEnabled&&connectionStatus=="broken"){connectionStatus="resumed";if(!loggedOut){integral.log("Socket info: Network reconnected. Attempting re-auth with node.");iosocket=null;loginUnityServer();}integral.ui.reconnectedNetwork();integral.ui.replaceSystemAlert(integral.ui.getLabel($.consts.CONNECTION_RESUMED));}resetHBFailureCnt();},error:function(data,t,err){integral.log("Error occured in heartbeat :"+data.responseText+", Status"+data.status+" failed : "+(hbfailure+1)+" times. reason "+t+". err:"+err);hbfailure++;if(t!="timeout"&&unityEnabled){connectionStatus="broken";if(unityEnabled){logoutUnityServer("Heartbeat with OA failed");}if(data.responseText!=null&&data.responseText.indexOf("503 Service Temporarily Unavailable")!=-1){integral.ui.replaceSystemAlert(integral.ui.getLabel("service_down"));}else{integral.ui.replaceSystemAlert(integral.ui.getLabel("lost_network_connection"));}}}});if(!unityEnabled&&ssExists){var secondaryHbUrl="/fxi/fxiapi/ss/heartbeat?l="+minLt+"&a="+avgLt+"&h="+maxLt+"&c="+copyLtArr.length+"&un="+userPlusOrg;$.ajax({url:secondaryHbUrl,type:"GET",dataType:"json",cache:false,timeout:hbRequestTimeout,contentType:"application/json; charset=UTF-8",success:function(data){if(lastSecPoll>0&&((new Date().getTime()-lastSecPoll)>heartBeatInterval)){if($("#fxticker").length>=1){integral.log("Secondary service is restored. trying reconnection");var tickerCcy=integral.tickers.getTickerCurrencyPair();integral.tickers.subscribeTicker(tickerCcy);}}},error:function(data,t,err){integral.log("Error occured in Secondary heartbeat :"+data.responseText+", Status"+data.status+". reason "+t+". err:"+err);}});}if(lastMsgRcvd>0&&((new Date().getTime()-lastMsgRcvd)>heartBeatInterval)){integral.log("Primary polling is restored.");}if(missHbRetries!=undefined&&hbfailure>=missHbRetries){integral.log("Logging out as heartbeat to server failed for "+missHbRetries+" times.");integral.ui.logout("Logging out as heartbeat to server failed for "+missHbRetries+" times.");return;}}catch(ex){integral.log("Javascript exception in heartbeat workflow "+ex);}if(integral.ui.isAutoLogoutEnabled()){var lastClicked=integral.ui.getLastActivityTime();if((new Date().getTime()-lastClicked)>integral.ui.getAutoLogoutInactivitySec()*1000&&$('[id="logoutDialog"]').length<=0){integral.ui.displayLogoutDialog();}}}function redirectToLoginPage(reason){if(unityEnabled){logoutUnityServer(reason);}$("#fxiprime").data("loggedin",false);location.href=loginPage;}function resetHBFailureCnt(){hbfailure=0;}function updateReplayMessages(){for(var m in replayOrderCache){callBackOnOrderUpdate.call(this,replayOrderCache[m]);}replayOrderCache={};}function cdqResponseType(refId){if(cdQueryRefMap.O==refId){return"O";}else{if(cdQueryRefMap.T==refId){return"T";}else{if(cdQueryRefMap.B==refId){return"B";}}}return null;}return{setCookie:function(name,value,expiry,domainval){var expiryStr="";if(expiry){var date=new Date();date.setTime(date.getTime()+(365*24*60*60*1000));expiryStr="expires="+date.toUTCString();}if(domainval){document.cookie=name+"="+value+";path=/;;domain="+domainval+";"+expiryStr;}else{document.cookie=name+"="+value+";path=/;"+expiryStr;}},removeCookie:function(name){$.setCookie(name,"",{path:"/",duration:-1});},setLoginTime:function(time){loginTime=time;},getConnectionStatus:function(ccyPair){return connectionStatus;},getReferenceData:function(ccyPair){return ccyPairRefData[ccyPair];},getUserName:function(){return userName;},getUserOrg:function(){return userOrg;},loginUnityServer:function(){unityEnabled=true;loginUnityServer();},disableUnityServer:function(){$.ajax({url:"/fxi/fxiapi/unity/disableWSStreaming",type:"POST",dataType:"json",cache:false,async:false,timeout:5000,contentType:"application/json; charset=UTF-8",success:function(data){integral.log("Client switched over to jmsproxy polling");},error:function(data,t,err){integral.log("Error switching to jmsproxy :"+data.responseText+", Status"+data.status+" reason:"+t+" .error:"+err);}});unityEnabled=false;},isUnityEnabled:function(){return unityEnabled;},setUnityFlag:function(flag){unityEnabled=flag;},setPrimaryPollingInterval:function(interval){priPollInt=interval;},setSecondaryPollingInterval:function(interval){secPollInt=interval;},setUnity:function(socket){unity=socket;},getUnity:function(){return unity;},setServerUpdateCallbacks:function(updateOrderBlotter,updatePositionBlotter,onRateUpdate,onRfsUpdate,onNotificationUpdate,onRfsFBUpdate){if(callBackOnOrderUpdate==undefined){callBackOnOrderUpdate=updateOrderBlotter;}if(callBackOnPositionUpdate==undefined){callBackOnPositionUpdate=updatePositionBlotter;}if(callBackOnRateUpdate==undefined){callBackOnRateUpdate=onRateUpdate;}if(callBackOnRfsUpdate==undefined){callBackOnRfsUpdate=onRfsUpdate;}if(callBackOnNotification==undefined){callBackOnNotification=onNotificationUpdate;}if(callBackOnRfsFBRateUpdate==undefined){callBackOnRfsFBRateUpdate=onRfsFBUpdate;}},handleServerError:function(text,e){if(e.status==401){var reason="handleServerError:Redirecting to login page as an error occured : "+text+", Status :"+e.status;integral.log(reason,$.consts.LOG_ERROR);redirectToLoginPage(reason);}else{if(e.status==400||e.status==403){var errorMsg=$.parseJSON(e.responseText);if(errorMsg.status!=undefined){callBackOnError.call(e,text+". Status="+errorMsg.status+" Message="+errorMsg.errorCode);}}else{if(e.status=="ERROR"){callBackOnError.call(e,text+". Status="+e.status+" Message="+e.errorCode);}else{if(e.status!=undefined){callBackOnError.call(e,text+". Status="+e.status+" Message="+e.statusText);}}}}},tryExternalSSOLogin:function(brand){var EXT_SSO_TOKEN_v1=$.cookie("EXTERNAL_SSO_AUTH");if(EXT_SSO_TOKEN_v1){try{if(EXT_SSO_TOKEN_v1=="LoggedIn@"){integral.log("ExternalSSO : Duplicate Login received or user refreshed the browser.");redirectExternalSSOLogout();}else{var user=EXT_SSO_TOKEN_v1.split("@");integral.log("Attempt to initialize dealing service by external SSO");integral.initDealingService(user[0],"",user[1],brand,dealingServiceCallBack);integral.removeCookie("EXTERNAL_SSO_AUTH");var domainValue=$.cookie("SSO_DOMAIN");integral.setCookie("EXTERNAL_SSO_AUTH","LoggedIn@",null,domainValue);}}catch(e){redirectExternalSSOError("Login to dealing Service Failed");}return true;}return false;function redirectExternalSSOError(error){var EXT_SSO_SAML_BRAND=$.cookie("SAML_BRAND");if(EXT_SSO_SAML_BRAND){window.location.href="sso/external/error/"+EXT_SSO_SAML_BRAND+"?error="+error;}else{window.location.href="sso/external/error?error="+error;}}function redirectExternalSSOLogout(){var EXT_SSO_SAML_BRAND=$.cookie("SAML_BRAND");if(EXT_SSO_SAML_BRAND){window.location.href="sso/external/logout/"+EXT_SSO_SAML_BRAND;}else{window.location.href="sso/external/logout";}}function dealingServiceCallBack(data){if(data.status==401||data.status==500){window.location.href="sso/external/error?error=User Not authorized To Access the App";}else{if(data.status=="OK"){var uinfo=$.parseJSON(this.data);$.props={redirectUrl:"",brandedPath:""};$.props.user=uinfo.user;$.props.org=uinfo.org;if(data.legalAgreementStatus==false){integral.ui.showLicenseAgreement(data);}else{data.brandFor=brand;data.externalSSO=true;integral.log("External SSO.dealingServiceCallBack:Loading main panel.");loadMainPanel(data);}}}}},login2fa:function(uname,pswd,org,otp,callbackFnc,brandedPath){var dataString="username="+uname+"&password="+pswd+"&org="+org;integral.removeCookie("JSESSIONID");integral.removeCookie("primJSESSIONID");integral.removeCookie("secJSESSIONID");integral.removeCookie("AUTH_TOKEN");integral.removeCookie("userInfo");var loginParams={user:uname,fixedFormatting:false,org:org,apiVersion:$.consts.API_VERSION,channel:"FXI_HTML",clientType:"WEBCLIENT",clientName:"HTML",clientVersion:"HTML",loginParams:{brandVersion:"HTML",brand:(brandedPath==""?"FXI":brandedPath)}};if(otp!=""){loginParams.otp=otp;}if(pswd!=""){loginParams.pass=pswd;}$.ajax({url:"/fxi/fxiapi/auth/login",type:"POST",data:JSON.stringify(loginParams),dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",processData:false,async:false,beforeSend:function(x){if(x&&x.overrideMimeType){x.overrideMimeType("application/json;charset=UTF-8");}},success:function(data){heartBeatInterval=parseInt(data.additionalInfo.heartBeatInterval);missHbRetries=parseInt(data.additionalInfo.missedHeartbeatsRetries);hbRequestTimeout=parseInt(data.additionalInfo.heartbeatRequestTimeout);if(heartBeatInterval==null||heartBeatInterval<minHbInterval){heartBeatInterval=minHbInterval;}streaming=data.asynChannel=="stream";integral.setUserInfo(uname,org);$(window).attr("name","fromlogin_"+brandedPath);loginTime=$.readCookie("LOGIN_TIME");if(typeof callbackFnc=="function"){callbackFnc.call(this,data);}integral.log("login call Success");},error:function(data){if(typeof callbackFnc=="function"){callbackFnc.call(this,data);}}});},loginSSO:function(uname,pswd,org,otp,callbackFnc,brandedPath){integral.removeCookies();var dataString="username="+uname+"&password="+pswd+"&org="+org;var loginParams;loginParams={user:uname,org:org,apiVersion:$.consts.API_VERSION};if(otp!=""){loginParams.otp=otp;}if(pswd!=""){loginParams.pass=pswd;}$.ajax({url:"/fxi/fxiapi/sso/login",type:"POST",data:JSON.stringify(loginParams),dataType:"json",contentType:"application/json; charset=UTF-8",processData:false,cache:false,async:false,beforeSend:function(x){if(x&&x.overrideMimeType){x.overrideMimeType("application/json;charset=UTF-8");}},success:function(data){if(typeof callbackFnc=="function"){data.brandedPath=brandedPath;callbackFnc.call(this,data);}$(window).attr("ssologin",true);integral.log("loginSSO call Success");},error:function(data){if(typeof callbackFnc=="function"){callbackFnc.call(this,data);}$(window).attr("ssologin",true);}});},initDealingService:function(uname,pswd,org,brandedPath,callbackFnc){integral.log("Calling initDealingService");var loginParams={user:uname,org:org,fixedFormatting:false,channel:"FXI_HTML",clientType:"WEBCLIENT",clientName:"HTML",apiVersion:$.consts.API_VERSION,clientVersion:"HTML",loginParams:{brandVersion:"HTML",brand:(brandedPath==""?"FXI":brandedPath)}};loginParams.loginParams.webSocketEnabled=(integral.ui.getProperty("websocket",false)=="enabled"&&!integral.ui.isBrowserIE())?true:false;$.ajax({url:"/fxi/fxiapi/dealingService/initialize",type:"POST",data:JSON.stringify(loginParams),dataType:"json",contentType:"application/json; charset=UTF-8",processData:false,async:false,cache:false,beforeSend:function(x){if(x&&x.overrideMimeType){x.overrideMimeType("application/json;charset=UTF-8");}},success:function(data){heartBeatInterval=parseInt(data.additionalInfo.heartBeatInterval);missHbRetries=parseInt(data.additionalInfo.missedHeartbeatsRetries);hbRequestTimeout=parseInt(data.additionalInfo.heartbeatRequestTimeout);if(heartBeatInterval==null||heartBeatInterval<minHbInterval){heartBeatInterval=minHbInterval;}streaming=data.asynChannel=="stream";integral.setUserInfo(uname,org);$(window).attr("name","fromlogin_"+brandedPath);loginTime=$.readCookie("LOGIN_TIME");if(typeof callbackFnc=="function"){callbackFnc.call(this,data);}integral.log("initDealingService call Success");},error:function(data){if(typeof callbackFnc=="function"){callbackFnc.call(this,data);}}});},initSecondaryService:function(){integral.log("Calling secondaryService initialize");$.ajax({url:"/fxi/fxiapi/secondaryService/initialize",type:"POST",dataType:"json",contentType:"application/json; charset=UTF-8",processData:false,async:false,cache:false,beforeSend:function(x){if(x&&x.overrideMimeType){x.overrideMimeType("application/json;charset=UTF-8");}},success:function(data){integral.log("SecondaryService initialize call Success");},error:function(data){integral.log("SecondaryService initialize call failed");}});},getHistoricalData:function(callBackFn,params){if(!params.autoUpdate){integral.log("User updated the historical chart. Instrument="+params.instrument+"&Period="+params.period);}var records;$.ajax({url:"/fxi/fxiapi/historicaldata/quoteHistory?instrument="+params.instrument+"&period="+params.period+"&count=200&auto="+(params.autoUpdate?"true":"false"),type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},params:params,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",async:true,success:function(data){if(data.status!="ERROR"){records=data.records;callBackFn.call(this,records,params);}else{integral.log("Error in getting historical data");data.systemError=true;integral.handleServerError("Error in getting historical data",data);}},error:function(e){e.systemError=true;integral.log("Error in getting historical data");integral.handleServerError("Error in getting historical data",e);}});},setCookieForRedirection:function(org){integral.removeCookie("orgServiceGroup");$.ajax({url:"/fxi/fxiapi/auth/redirect/"+org,type:"GET",cache:false,async:false,contentType:"application/json; charset=UTF-8",processData:false,beforeSend:function(x){if(x&&x.overrideMimeType){x.overrideMimeType("application/json;charset=UTF-8");}},success:function(data){},error:function(data){}});},logout:function(callbackFnc,reason){var logoutUrl="/fxi/fxiapi/sso/logout";$.ajax({type:"POST",url:"/fxi/fxiapi/dealingService/terminate",dataType:"json",async:false,cache:false,processData:false});$.ajax({type:"POST",url:logoutUrl,dataType:"json",data:JSON.stringify({reason:reason}),async:false,cache:false,processData:false,success:function(data){loggedOut=true;if(unityEnabled){logoutUnityServer(reason);}integral.removeCookies();integral.removeCookiesOnLogout();if(typeof callbackFnc=="function"){callbackFnc.call(this,data);}},error:function(data){integral.removeCookies();integral.removeCookiesOnLogout();if(typeof callbackFnc=="function"){callbackFnc.call(this,data);}}});},removeCookies:function(){integral.removeCookie("MDFServerEnabled");integral.removeCookie("AUTH_TOKEN");integral.removeCookie("SSO_TOKEN");integral.removeCookie("jmspsid");integral.removeCookie("jsmssid");integral.removeCookie("JSESSIONID");integral.removeCookie("JMS_PROXY_HOST_PORT");integral.removeCookie("primJSESSIONID");integral.removeCookie("secJSESSIONID");integral.removeCookie("userInfo");integral.removeCookie("hbInterval");integral.removeCookie("misshbretries");integral.removeCookie("hbReqTimeout");integral.removeCookie("orgServiceGroup");integral.removeCookie("businessDate");integral.removeCookie("metURL");},removeCookiesOnLogout:function(){var cookieNames=document.cookie.split(/=[^;]*(?:;\s*|$)/);for(var i=0;i<cookieNames.length;i++){if($.trim(cookieNames[i]).indexOf("SSO_Sticky_Session")===0){integral.removeCookie(cookieNames[i]);}}},forgotPassword:function(uname,org,callbackFnc){var dataString=JSON.stringify({user:uname,org:org,passwordAnswer:"N/A"});$.ajax({url:"/fxi/fxiapi/auth/forgotPassword",type:"POST",cache:false,data:dataString,dataType:"json",contentType:"application/json; charset=UTF-8",processData:false,beforeSend:function(x){if(x&&x.overrideMimeType){x.overrideMimeType("application/json;charset=UTF-8");}},success:function(data){if(typeof callbackFnc=="function"){callbackFnc.call(this,data);}},error:function(data){if(typeof callbackFnc=="function"){callbackFnc.call(this,data);}}});},resetPassword:function(oldPassword,newPassword,callbackFnc){var dataString=JSON.stringify({oldPassword:oldPassword,newPassword:newPassword});var resp;$.ajax({url:"/fxi/fxiapi/auth/resetPassword",type:"POST",data:dataString,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",processData:false,async:false,beforeSend:function(x){if(x&&x.overrideMimeType){x.overrideMimeType("application/json;charset=UTF-8");}},success:function(data){resp=data;},error:function(data){resp=data;}});return resp;},getLegalAgreement:function(){var la;$.ajax({type:"GET",url:"/fxi/fxiapi/auth/legalAgreement",async:false,cache:false,dataType:"json",success:function(data){la=data;},error:function(e){e.systemError=true;integral.handleServerError("getLegalAgreement",e);}});return la;},acceptLegalAgreement:function(callbackFnc){$.ajax({type:"GET",url:"/fxi/fxiapi/auth/legalAgreement/accept",async:false,cache:false,dataType:"json",success:function(data){callbackFnc.call();},error:function(e){e.systemError=true;integral.handleServerError("acceptLegalAgreement",e);}});},getPermissions:function(callbackFnc){var permissions;$.ajax({type:"GET",url:"/fxi/fxiapi/auth/permissions",async:false,cache:false,dataType:"json",success:function(data){permissions=data;},error:function(e){e.systemError=true;integral.handleServerError("getPermissions",e);}});return permissions;},getRiskNetReferenceData:function(){var refData=null;$.ajax({type:"GET",url:"/fxi/fxiapi/refdata/rexrefdata",async:false,cache:false,dataType:"json",success:function(data){refData=data;},error:function(e){e.systemError=true;integral.handleServerError("getRiskNetRferenceData",e);}});return refData;},getSupportedCurrencyPairs:function(){var ccyPairArr=new Array();var vdlogStr="{ ";$.ajax({type:"GET",url:"/fxi/fxiapi/refdata/supportedCcypairs",async:false,cache:false,dataType:"json",success:function(data){var supportedCurRefDataArray=data;for(var i=0,j=0;i<supportedCurRefDataArray.length;i++){if(supportedCurRefDataArray[i]==null||supportedCurRefDataArray[i]==undefined){integral.log("Supported Currency pair received is :"+supportedCurRefDataArray[i]);continue;}ccyPairArr[j]=supportedCurRefDataArray[i].instrument;ccyPairRefData[ccyPairArr[j]]=supportedCurRefDataArray[i];j++;vdlogStr+=supportedCurRefDataArray[i].instrument+":"+supportedCurRefDataArray[i].spotValueDate+", ";}vdlogStr+=" }";integral.log("Spot Values Dates : "+vdlogStr);},error:function(e){e.systemError=true;integral.handleServerError("getSupportedCurrencyPairs",e);}});return ccyPairArr;},getRTProviders:function(ccyPair){var providers=[];$.ajax({type:"GET",url:"/fxi/fxiapi/refdata/rtAggregationProviders",async:false,cache:false,dataType:"json",success:function(data){providers=data;},error:function(e){e.systemError=true;integral.handleServerError("Error in rtAggregationProviders "+ccyPair,e);}});return providers;},getSupportedCcys:function(){var supportedCcyArray=new Array();$.ajax({type:"GET",url:"/fxi/fxiapi/refdata/supportedCcys",async:false,cache:false,dataType:"json",success:function(data){supportedCcyArray=data;},error:function(e){e.systemError=true;integral.handleServerError("getSupportedCurrencyPairs",e);}});return supportedCcyArray;},getVehicleCcys:function(){var vehicleCcys=new Array();$.ajax({type:"GET",url:"/fxi/fxiapi/refdata/vehicleCcys",async:false,cache:false,dataType:"json",success:function(data){vehicleCcys=data;},error:function(e){e.systemError=true;integral.handleServerError("getVehicleCcys",e);}});return vehicleCcys;},getDefaultTenorsList:function(){var defaultTenorsArray=new Array();$.ajax({type:"GET",url:"/fxi/fxiapi/rfs/supportedTenors",async:false,cache:false,dataType:"json",success:function(data){if(data==null||data.length==0){defaultTenorsArray=["JAN","FEB","MAR","APR","MAY","JUN","JUL","AUG","SEP","OCT","NOV","DEC","TOM","TOD","ON","TN","SN","TM"];}else{defaultTenorsArray=data;}},error:function(e){defaultTenorsArray=["JAN","FEB","MAR","APR","MAY","JUN","JUL","AUG","SEP","OCT","NOV","DEC","TOM","TOD","ON","TN","SN","TM"];e.systemError=true;integral.handleServerError("getDefaultTenorsList",e);}});return defaultTenorsArray;},getProviderStreams:function(provider){var streamArr={};$.ajax({type:"GET",url:"/fxi/fxiapi/refdata/providers/anonymous/"+provider,async:false,cache:false,dataType:"json",success:function(data){streamArr=data;},error:function(e){e.systemError=true;integral.handleServerError("getProviderStreams "+provider,e);}});return streamArr.sort();},subscribeRFS:function(callbackFnc,callBackOnSubscrUpdate,callBackOnRfsUpd,rfspanel,instrument,amount,dealtInstrument,nearLegValueDate,farlegDealtAmount,farLegValueDate,channel,sideType,priceType,fixOrValue,rfsexpiry,multiLPEnabled,stmtInst,errorCount){errorCount=errorCount!=undefined?errorCount:0;if(callBackOnGetPrice==undefined){callBackOnGetPrice=callbackFnc;}if(callBackOnRfsUpdate==undefined){callBackOnRfsUpdate=callBackOnRfsUpd;}var rfsMap={instrument:instrument,amount:amount,farDealtAmount:farlegDealtAmount,dealtInstrument:dealtInstrument,farValueDate:farLegValueDate,channel:channel,sideType:parseInt(sideType),priceType:parseInt(priceType),expiryInSecs:rfsexpiry,stmtInst:stmtInst};if(integral.ui.isSalesDealerTradingEnabled()){var customerOrg=rfspanel.find("#salesdealerorgs").val();var customerAccount=rfspanel.find("#salesdealerles").val();rfsMap.customerOrg=customerOrg;rfsMap.customerAccount=customerAccount;}if(multiLPEnabled){rfsMap.rfsPriceViewType=1;rfsMap.apiVersion=1.1;if(rfspanel.data("panelType")=="fb"){rfsMap.depth=0;}else{rfsMap.depth=1;}}if(fixOrValue=="fixingdate"){rfsMap.fixingDate=nearLegValueDate;}else{rfsMap.nearValueDate=nearLegValueDate;}var dataString=JSON.stringify(rfsMap);$.ajax({url:"/fxi/fxiapi/rfs/subscribe",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,dataType:"json",async:true,cache:false,contentType:"application/json; charset=UTF-8",success:function(data){if(data.status!="ERROR"){integral.log("Subscribed RFS "+instrument+" "+amount+", client expiry "+rfsexpiry+", requestId "+data.requestId);if(iframeInitialized==undefined||!iframeInitialized){$('<iframe id="streamingframe" width="0" height="0" frameborder="0" ></iframe>').appendTo("body");getMessages();iframeInitialized=true;}callBackOnGetPrice.call(data,rfspanel);callBackOnSubscrUpdate.call(data,true,rfspanel);}else{callBackOnSubscrUpdate.call(data,false,rfspanel);data.systemError=true;integral.handleServerError("Subscribe RFS "+instrument,data);}},error:function(e){if(errorCount==3){e.systemError=true;integral.handleServerError("Error in RFS "+instrument,e);callBackOnSubscrUpdate.call(e,false,rfspanel);}else{integral.log("Retrying RFS Subscription "+dataString+" Error:"+e.status+", Count:"+(errorCount+1));integral.subscribeRFS(callbackFnc,callBackOnSubscrUpdate,callBackOnRfsUpd,rfspanel,instrument,amount,dealtInstrument,nearLegValueDate,farlegDealtAmount,farLegValueDate,channel,sideType,priceType,fixOrValue,rfsexpiry,multiLPEnabled,stmtInst,errorCount+1);}}});},withdrawRFS:function(callback,rfspanel,requestId){$.ajax({url:"/fxi/fxiapi/rfs/withdraw/"+requestId,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},dataType:"json",async:true,cache:false,contentType:"application/json; charset=UTF-8",success:function(data){if(data.status!="ERROR"){callback.call(data,rfspanel);}else{data.systemError=true;integral.handleServerError("Withdraw RFS "+requestId,data);}},error:function(e){e.systemError=true;integral.handleServerError("Withdraw RFS "+requestId,e);}});},subscribeBatch:function(callbackFnc,batchRequestsArr,errorCount){if(batchRequestsArr.length==0){return;}errorCount=errorCount!=undefined?errorCount:0;if(callBackOnRateUpdate==undefined){callBackOnRateUpdate=callbackFnc;}var dataString=JSON.stringify({requestList:batchRequestsArr});var subLogStr="{";for(var i=0;i<batchRequestsArr.length;i++){subLogStr+="[ Currency :"+batchRequestsArr[i].instrument+", Dealt CCy :"+batchRequestsArr[i].dealtInstrument+", Market Depth :"+batchRequestsArr[i].depth+", amount :"+batchRequestsArr[i].sizes+", Request Type :"+batchRequestsArr[i].reqType+", View Type :"+batchRequestsArr[i].view+", Key :"+batchRequestsArr[i].reqID+"],";}subLogStr+="}.";integral.log("Sending batch Subscription request for "+subLogStr);$.ajax({url:"/fxi/fxiapi/marketdata/subscribeBatch",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,dataType:"json",contentType:"application/json; charset=UTF-8",cache:false,success:function(data){if(data.status!="ERROR"){var ccyRDList=data.ccyPairRefDataList;var vdlogstr="";if(ccyRDList!=null){for(var i=0;i<ccyRDList.length;i++){if(ccyRDList[i]==undefined||ccyRDList[i]==null){integral.log("Reference data recieved is :"+ccyRDList[i]);continue;}ccyPairRefData[ccyRDList[i].instrument]=ccyRDList[i];if(integral.getReferenceData(ccyRDList[i].instrument).spotValueDate!=ccyRDList[i].spotValueDate){vdlogstr+=ccyRDList[i].instrument+" set to :"+ccyRDList[i].spotValueDate+", ";}}if(vdlogstr!=""){integral.log("In SubscribeBatch, Spot value date for "+vdlogstr);}}}else{var errorStr="";var logStr="";var ccy,amt,dealtCCy,config;var keyMap=integral.ui.getKeyMap();var tempMap={};for(var k in keyMap){tempMap[keyMap[k]]=k;}if(data.instrumentResponseDescriptorList!=null){var instrumentList=data.instrumentResponseDescriptorList;for(var i=0;i<instrumentList.length;i++){if(instrumentList[i].status=="ERROR"){config=tempMap[instrumentList[i].id];if(config){ccy=config.substr(0,3)+"/"+config.substr(3,3);amt=(config.substr(6,config.length-8));dealtCCy=config.charAt(config.length-2)=="B"?ccy.substr(0,3):ccy.substr(4,3);errorStr+=errorStr.indexOf(ccy)==-1?((i!=0?",":"")+ccy):"";logStr+=instrumentList[i].errorCode+":"+ccy+":"+amt+":"+dealtCCy+",";}else{logStr+=instrumentList[i].id+":"+instrumentList[i].errorCode+", ";}}}tempMap=null;keyMap=null;if(integral.ui.isFXInsideRT()){data.errorCode="FMA Subscription Failed : "+errorStr;}else{data.errorCode="Subscription Failed : "+errorStr;}}else{logStr=data.errorCode;}integral.log("Error in SubscribeBatch :"+logStr);data.systemError=true;if(logStr.indexOf("chart")===-1){integral.handleServerError("SubscribeBatch ",data);}}if(iframeInitialized==undefined||!iframeInitialized){$('<iframe id="streamingframe" width="0" height="0" frameborder="0" ></iframe>').appendTo("body");getMessages();iframeInitialized=true;}},error:function(e){if(errorCount==3){e.systemError=true;if(requestType==1){integral.handleServerError("Error in getting rates for board ",e);}else{integral.log("Could not unsubscribe board "+currencyPairArr);}}else{integral.log("Retrying Batch "+(requestType==1?"Subscription ":"Unsubscription ")+" "+dataString+" Error:"+e.status+", Count:"+(errorCount+1));integral.subscribeBatch(callbackFnc,batchRequestsArr,errorCount+1);}}});},subscribe:function(callbackFnc,currencyPair,requestType,amount,marketDepth,dealtCcy,view,key,errorCount){errorCount=errorCount!=undefined?errorCount:0;if(callBackOnRateUpdate==undefined){callBackOnRateUpdate=callbackFnc;}var minQty=1000;if(integral.ui.isMetal(currencyPair)){minQty=1;}var subcrParam={reqID:key,view:view,reqType:requestType,depth:marketDepth,updType:0,org:userOrg,entryType:2,instrument:currencyPair,dealtInstrument:dealtCcy,minSize:minQty};if(amount!=""&&amount!=null&&amount!=undefined){subcrParam.sizes=[amount];subcrParam.requestedSize=amount;}var requestTypeStr=requestType==1?"Subscription":"Un-subscription";var dataString=JSON.stringify(subcrParam);var subLogStr="[Currency :"+currencyPair+", Dealt CCy :"+dealtCcy+", amount :"+amount+", Request Type :"+requestType+", Market Depth :"+marketDepth+", View Type :"+view+", Key :"+key+"]";integral.log("Sending "+requestTypeStr+" for "+subLogStr);$.ajax({url:"/fxi/fxiapi/marketdata/subscribe",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,dataType:"json",requestType:requestType,cache:false,contentType:"application/json; charset=UTF-8",success:function(data){var success=false;var errorlogStr=" ";for(var i=0;i<data.length;i++){if(data[i].status!="ERROR"){integral.log(requestTypeStr+" Successful "+currencyPair+" "+amount+" "+((dealtCcy==undefined)?"TOB":dealtCcy)+", Market Depth :"+marketDepth+", View Type :"+view+", Key :"+key);if(data[i].status=="OK"&&data[i].refData!=null){ccyPairRefData[currencyPair]=data[i].refData;if(integral.getReferenceData(currencyPair).spotValueDate!=data[i].refData.spotValueDate){integral.log("In Subscribe, Spot value date for "+currencyPair+" set to :"+data[i].refData.spotValueDate);}success=true;}}else{errorlogStr+=data[i].errorCode+"|";}}if(!success){if(requestType==1&&errorlogStr.indexOf("SUBSCRIPTION_EXISTS")==-1){data.errorCode=" Subscription Failed : "+currencyPair;data.status="ERROR";data.systemError=true;if(key.indexOf("_chart")==-1){integral.handleServerError("Subscribe ",data);}}}if(errorlogStr!=" "){integral.log("Error in "+requestTypeStr+": "+key+":"+currencyPair+":"+amount+":"+((dealtCcy==undefined)?"TOB":dealtCcy)+" "+errorlogStr);}if(iframeInitialized==undefined||!iframeInitialized){$('<iframe id="streamingframe" width="0" height="0" frameborder="0" ></iframe>').appendTo("body");getMessages();iframeInitialized=true;}},error:function(e){if(errorCount==3){e.systemError=true;if(requestType==1){integral.handleServerError("Error in getting rates for "+currencyPair,e);}else{integral.log("Could not unsubscribe "+currencyPair+" "+amount);}}else{integral.log("Retrying "+requestTypeStr+" "+dataString+" Error:"+e.status+", Count:"+(errorCount+1));integral.subscribe(callbackFnc,currencyPair,requestType,amount,marketDepth,dealtCcy,view,key,errorCount+1);}}});},subscribeBenchmarkRates:function(callbackFnc,subscrOptions){var currencyPair=subscrOptions.currencyPair;var clientRefId=subscrOptions.clientReferenceId;if(unityEnabled){if(integral.getUnity()!=undefined){integral.subscribeUnityBenchmarkRates(callbackFnc,currencyPair);}return;}var reqParams={};reqParams.requestList=[{clientReferenceId:clientRefId+"_"+currencyPair,currencyPair:currencyPair}];reqParams.ps="FXB";var dataString=JSON.stringify(reqParams);$.ajax({url:"/fxi/fxiapi/fxbenchmark/subscribe",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,provider:subscrOptions.provider,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(data){if(!ssExists&&$.readCookie("jsmssid")!=undefined&&$.readCookie("jsmssid")!=""){integral.enableSecondaryServer();}callbackFnc.call(this,data);},error:function(e){}});},subscribeUnityBenchmarkRates:function(callbackFnc,ccyPair){var params={userName:integral.getUserName(),userOrg:integral.getUserOrg(),requestList:[{currencyPair:ccyPair,clientReferenceId:"html"}],sendLatestSnapshot:true};integral.getUnity().emit("fxBenchmarkSubscribe",params);},unsubscribeUnityBenchmarkRates:function(callbackFnc,ccyPair){var params={userName:integral.getUserName(),userOrg:integral.getUserOrg(),requestList:[{currencyPair:ccyPair,clientReferenceId:"html"}]};integral.getUnity().emit("fxBenchmarkUnSubscribe",params);},unsubscribeBenchmarkRates:function(callbackFnc,subscrOptions){var subscriptionRequestId=subscrOptions.subscriptionRequestId;if(unityEnabled){if(integral.getUnity()!=undefined){integral.unsubscribeUnityBenchmarkRates(callbackFnc,subscrOptions);}return;}var clientRefId=subscrOptions.clientReferenceId;var reqParams={};reqParams.subscriptionRequestIdList=[subscriptionRequestId];reqParams.clientReferenceId=clientRefId;reqParams.ps="FXB";var dataString=JSON.stringify(reqParams);$.ajax({url:"/fxi/fxiapi/fxbenchmark/unsubscribe",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,provider:subscrOptions.provider,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(data){callbackFnc.call(this,data);},error:function(e){}});},subscribeRT:function(callbackFnc,subscrOptions,errorCount){var currencyPair=subscrOptions.currencyPair;var requestType=subscrOptions.requestType;var amount=subscrOptions.amount;var marketDepth=subscrOptions.marketDepth;var dealtCcy=subscrOptions.dealtCcy;var view=subscrOptions.view;var key=subscrOptions.key;errorCount=errorCount!=undefined?errorCount:0;if(callBackOnRateUpdate==undefined){callBackOnRateUpdate=callbackFnc;}var minQty=1000;if(integral.ui.isMetal(currencyPair)){minQty=1;}var subcrParam={reqID:key,view:view,reqType:requestType,depth:marketDepth,updType:0,org:userOrg,entryType:2,instrument:currencyPair,dealtInstrument:dealtCcy,minSize:minQty};if(amount!=""&&amount!=null&&amount!=undefined){subcrParam.sizes=[amount];subcrParam.requestedSize=amount;}if(subscrOptions.provider!=null){subcrParam.providers=[subscrOptions.provider];}var requestTypeStr=requestType==1?"Subscription":"Un-subscription";var dataString=JSON.stringify(subcrParam);var subLogStr="[Currency :"+currencyPair+", Dealt CCy :"+dealtCcy+", amount :"+amount+", Request Type :"+requestType+", Market Depth :"+marketDepth+", View Type :"+view+", Key :"+key+"]";integral.log("Sending "+requestTypeStr+" for "+subLogStr);$.ajax({url:"/fxi/fxiapi/marketdata/subscribe",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,provider:subscrOptions.provider,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(data){var success=false;var errorlogStr=" ";for(var i=0;i<data.length;i++){if(data[i].status!="ERROR"){integral.log(requestTypeStr+" Successful "+currencyPair+" "+amount+" "+((dealtCcy==undefined)?"TOB":dealtCcy)+", Market Depth :"+marketDepth+", View Type :"+view+", Key :"+key);if(data[i].status=="OK"&&data[i].refData!=null){ccyPairRefData[currencyPair]=data[i].refData;if(integral.getReferenceData(currencyPair).spotValueDate!=data[i].refData.spotValueDate){integral.log("In RT Subscribe, Spot value date for "+currencyPair+" set to :"+data[i].refData.spotValueDate);}success=true;}}else{errorlogStr+=data[i].errorCode+"|";}}if(!success){if(requestType==1&&errorlogStr.indexOf("SUBSCRIPTION_EXISTS")==-1){data.errorCode=this.provider+" Subscription Failed : "+currencyPair;data.status="ERROR";data.systemError=true;if(key.indexOf("_chart")==-1){integral.handleServerError("Subscribe ",data);}}}if(errorlogStr!=" "){integral.log("Error in "+requestTypeStr+": "+key+":"+currencyPair+":"+amount+":"+((dealtCcy==undefined)?"TOB":dealtCcy)+" "+errorlogStr);}if(iframeInitialized==undefined||!iframeInitialized){$('<iframe id="streamingframe" width="0" height="0" frameborder="0" ></iframe>').appendTo("body");getMessages();iframeInitialized=true;}},error:function(e){if(errorCount==3){e.systemError=true;if(requestType==1){integral.handleServerError("Error in getting rates for "+currencyPair,e);}else{integral.log("Could not unsubscribe RT "+currencyPair+" "+amount);}}else{integral.log("Retrying "+requestTypeStr+" "+dataString+" Error:"+e.status+", Count:"+(errorCount+1));integral.subscribeRT(callbackFnc,subscrOptions,errorCount+1);}}});},subscribeOrderNotifications:function(errorCount){errorCount=errorCount!=undefined?errorCount:0;$.ajax({url:"/fxi/fxiapi/notification/order/subscribe",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(data){integral.log("Order Notifications Subscription successful");},error:function(e){if(errorCount==3){integral.handleServerError("Error in subscribing order notification",e);}else{integral.log("Retrying Order notification Subscription. Error:"+e.status+", Count:"+(errorCount+1));integral.subscribeOrderNotifications(errorCount+1);}}});},subscribeChiefDealerNotifications:function(errorCount){errorCount=errorCount!=undefined?errorCount:0;$.ajax({url:"/fxi/fxiapi/chiefdealer/notifications/subscribe",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",async:false,timeout:2000,success:function(data){integral.log("chiefdealer Notifications Subscription successful");},error:function(e){if(errorCount==3){integral.handleServerError("Error in subscribing chiefdealer notification",e);}else{integral.log("Retrying chiefdealer notification Subscription. Error:"+e.status+", Count:"+(errorCount+1));integral.subscribeOrderNotifications(errorCount+1);}}});},unsubscribeChiefDealerNotifications:function(logOnError){$.ajax({url:"/fxi/fxiapi/chiefdealer/notifications/unsubscribe",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(data){integral.log("chiefdealer Notifications Un-Subscription successful");},error:function(e){if(logOnError){integral.log("Error in un-subscribing chiefdealer notification");}else{integral.handleServerError("Error in un-subscribing chiefdealer notification",e);}}});},subscribeOrderBook:function(errorCount){errorCount=errorCount!=undefined?errorCount:0;$.ajax({url:"/fxi/fxiapi/orderbook/view/subscribe",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(data){integral.log("Order book Subscription successful");},error:function(e){e.systemError=true;if(errorCount==3){integral.handleServerError("Error in subscribing order book",e);}else{integral.log("Retrying Order book Subscription. Error:"+e.status+", Count:"+(errorCount+1));integral.subscribeOrderBook(errorCount+1);}}});},unsubscribeOrderBook:function(logOnError){$.ajax({url:"/fxi/fxiapi/orderbook/view/unsubscribe",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(data){integral.log("Order book Un-Subscription successful");},error:function(e){if(logOnError){integral.log("Error in unsubscribe order book");}else{e.systemError=true;integral.handleServerError("Error in unsubscribe order book",e);}}});},subscribeTicker:function(callbackFnc,ccyPair,errorCount){if(unityEnabled){if(unityLoggedIn){integral.subscribeUnityTicker(callbackFnc,ccyPair,errorCount);}return;}errorCount=errorCount!=undefined?errorCount:0;if(callBackOnTickerUpdate==undefined){callBackOnTickerUpdate=callbackFnc;}var dataString=JSON.stringify({requestList:[{currencyPair:ccyPair,clientReferenceId:"html"}],sendLatestSnapshot:true});$.ajax({url:"/fxi/fxiapi/trade/ticker/subscribe",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(data){callBackOnTickerUpdate.call(this,data,"snapshot");if(!ssExists&&$.readCookie("jsmssid")!=undefined&&$.readCookie("jsmssid")!=""){integral.enableSecondaryServer();}},error:function(e){e.systemError=true;if(errorCount==3){integral.handleServerError("Error in subscribing ticker "+ccyPair,e);}else{integral.log("Retrying Ticker Subscription "+dataString+" Error:"+e.status+", Count:"+(errorCount+1));integral.subscribeTicker(callbackFnc,ccyPair,errorCount+1);}}});},subscribeUnityTicker:function(callbackFnc,ccyPair,errorCount){errorCount=errorCount!=undefined?errorCount:0;if(callBackOnTickerUpdate==undefined){callBackOnTickerUpdate=callbackFnc;}var params={userName:integral.getUserName(),userOrg:integral.getUserOrg(),requestList:[{currencyPair:ccyPair,clientReferenceId:"html"}],sendLatestSnapshot:true};integral.getUnity().emit("tickerSubscribe",params);unityTickerInitialized=true;},unsubscribeUnityTicker:function(callbackFnc,ccyPair,errorCount){if(integral.getUnity()!=undefined){var params={userName:integral.getUserName(),userOrg:integral.getUserOrg(),requestList:[{currencyPair:ccyPair,clientReferenceId:"html"}]};integral.getUnity().emit("tickerUnSubscribe",params);}},unsubscribeTicker:function(callbackFnc,subReqId,ccyPair){if(unityEnabled){integral.unsubscribeUnityTicker(callbackFnc,subReqId,ccyPair);return;}var dataString=JSON.stringify({subscriptionRequestIdList:[subReqId]});$.ajax({url:"/fxi/fxiapi/trade/ticker/unsubscribe",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,ccyPair:ccyPair,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(data){callbackFnc.call(data,this.ccyPair);},error:function(e){}});},subscribePositions:function(callbackFnc,errorCount){errorCount=errorCount!=undefined?errorCount:0;if(callBackOnPositionUpdate==undefined){callBackOnPositionUpdate=callbackFnc;}var pnlCurrency=integral.ui.getUserSettings().getPnlCurrency();var groupByArr=["Instrument","ValueDate"];if(integral.ui.isSalesDealerTradingEnabled()){groupByArr.push("CustomerOrg");}var dataString=JSON.stringify({clientReferenceId:"html",aggregateView:true,customerId:userName,customerOrg:userOrg,fromValueDate:integral.ui.getBusinessDate(),pnlCurrency:pnlCurrency,pnlUpdateIntervalInMills:5000,positionType:"All",groupByColumns:groupByArr});$.ajax({url:"/fxi/fxiapi/position/subscribe",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(data){if(iframeInitialized==undefined||!iframeInitialized){$('<iframe id="streamingframe" width="0" height="0" frameborder="0" ></iframe>').appendTo("body");getMessages();iframeInitialized=true;}},error:function(e){e.systemError=true;if(errorCount==3){integral.handleServerError("Error in getting positions",e);}else{integral.log("Retrying Position Subscription "+dataString+" Error:"+e.status+", Count:"+(errorCount+1));integral.subscribePositions(callbackFnc,errorCount+1);}}});},subscribeNewPositions:function(callbackFnc,errorCount){var custId=userName;var custAcc;errorCount=errorCount!=undefined?errorCount:0;if(callBackOnPositionUpdate==undefined){callBackOnPositionUpdate=callbackFnc;}var pnlCurrency=integral.ui.getUserSettings().getPnlCurrency();var custPositionsFlag=false;var grpByField=integral.ui.getPosGroupByValue();var posLevel=integral.ui.isPosTraderView()?"USER":integral.ui.getPositionLevelMap(grpByField);if((posLevel==="ORG"||posLevel==="LE")&&integral.ui.isSalesDealerTradingEnabled()){custPositionsFlag=integral.ui.getICPMap(grpByField);}if(!integral.ui.isPosTraderView()&&posLevel==="USER"){custId="*";}custAcc=posLevel==="LE"?"*":"";var dataString=JSON.stringify({clientReferenceId:"html",aggregateView:true,customerId:custId,customerOrg:userOrg,customerAccount:custAcc,fromValueDate:integral.ui.getBusinessDate(),pnlCurrency:pnlCurrency,pnlUpdateIntervalInMills:5000,positionType:"All",positionLevel:posLevel,inclCustomerPositions:custPositionsFlag});$.ajax({url:"/fxi/fxiapi/position/subscribe",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(data){if(integral.ui.isPosHierarchyViewEnabled()){integral.ui.setposRequestId(data.positionRequestId);}if(iframeInitialized==undefined||!iframeInitialized){$('<iframe id="streamingframe" width="0" height="0" frameborder="0" ></iframe>').appendTo("body");getMessages();iframeInitialized=true;}},error:function(e){e.systemError=true;if(errorCount==3){integral.handleServerError("Error in getting positions",e);}else{integral.log("Retrying Position Subscription "+dataString+" Error:"+e.status+", Count:"+(errorCount+1));integral.subscribeNewPositions(callbackFnc,errorCount+1);}}});},unsubscribePositions:function(callBackFnc){$.ajax({url:"/fxi/fxiapi/position/unsubscribe",type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},cache:false,dataType:"json",async:false,contentType:"application/json; charset=UTF-8",success:function(data){if(callBackFnc){callBackFnc.call();}},error:function(){}});},getOpenPositions:function(){var positions;$.ajax({url:"/fxi/fxiapi/position/q?customer="+userName+"&groupBy=CP",type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},async:false,cache:false,contentType:"application/json; charset=UTF-8",success:function(data){positions=data;},error:function(e){e.systemError=true;integral.handleServerError("getOpenPositions",e);}});return positions;},p:function(content,channel){if(streaming&&(channel==null)){lastMsgRcvd=new Date().getTime();}var orders=null;var trades=null;var cdqueryTrades;var cdqueryOrders;var cdqueryObjects;var cdNotification;var slMsgCcy;var slReqId;var slDataObj={};var wt=0;for(var i in content){try{var msg=content[i];if(msg[0]==="WT"){wt=parseInt(msg[1]);continue;}if(msg[0]=="TH"){var message=msg[1];if(message!=null&&message.data!=null){if(message.data.indexOf("<event>LOGOUT</event>")!=-1){var reason="Logout message received from the server :"+message.data;integral.log(reason);redirectToLoginPage(reason);break;}}continue;}if(msg[1]=="hb"){continue;}if(msg[0]=="MDSF"){var slMsgArr=msg[1].split("|");var formtdPointObj;if(slMsgArr.length>=4){slDataObj.slMsgCcy=slMsgArr[3].replace("[","");slDataObj.slReqId=slMsgArr[0].replace("[","");slDataObj.slTenor=slMsgArr[4];formtdPointObj=integral.ui.convertSLPointsFormat(slDataObj.slMsgCcy,slMsgArr[5],slMsgArr[6].replace("]]",""));slDataObj.slBidPoint=formtdPointObj.formattedBidPoint;slDataObj.slOfferPoint=formtdPointObj.formattedOfferPoint;if(slDataObj.slReqId==integral.ui.getslReqId(slDataObj.slMsgCcy)){integral.ui.setslDataObj(slDataObj.slMsgCcy,slDataObj.slTenor,slDataObj.slBidPoint,slDataObj.slOfferPoint);integral.ui.updateSwapLadder(slDataObj);}}continue;}if(msg[1]=="sos"){continue;}if(msg[1]=="eos"){getMessages();continue;}if(msg[0]=="OM"){var oData=$.parseJSON(msg[1]);if(msg[2]=="1"){if(channel==null){ackIdList.push(msg[3]);}else{if(channel=="secondary"){secAckIdList.push(msg[3]);}}}if(orders==null){orders="Orders received - ";}if(oData.acceptedTrade!=null){if(trades==null){trades="Trades Done - ";}trades=trades+oData.acceptedTrade.orderId+":"+oData.acceptedTrade.tradeId+",";}orders=orders+oData.orderId+":"+oData.orderStatus+",";if(orderBlotterLoaded&&tradeBlotterLoaded){callBackOnOrderUpdate.call(this,oData);}else{replayOrderCache[msg[3]]=oData;}continue;}if(msg[0]=="PM"){var pData=$.parseJSON(msg[1]);if(integral.ui.isPosHierarchyViewEnabled()){if(pData.positionRequestId===integral.ui.getposRequestId()){callBackOnPositionUpdate.call(this,pData,false);}else{integral.log("Droped PM.Req ID-"+pData.positionRequestId+" not matching with Latest Req ID-"+integral.ui.getposRequestId());}}else{callBackOnPositionUpdate.call(this,pData,false);}continue;}if(msg[0]=="RM"){var data=msg[1];callBackOnRateUpdate.call(this,data);continue;}if(msg[0]=="NM"){var data=$.parseJSON(msg[1]);if(msg[2]=="1"){if(channel==null){ackIdList.push(msg[3]);}else{if(channel=="secondary"){secAckIdList.push(msg[3]);}}}callBackOnNotification.call(this,data);continue;}if(msg[0]=="RFSE"||msg[0]=="RFST"||msg[0]=="RFSR"||msg[0]=="RFSF"||msg[0]=="PORTFOLIOR"){if(msg[0]=="RFST"&&!integral.ui.isTraderView()){continue;}var rfs=msg[1];callBackOnRfsUpdate.call(this,rfs,msg[0]);continue;}if(msg[0]==="TT"){callBackOnTickerUpdate.call(this,msg[1],"trade");}if(msg[0]==="OBV"){integral.ui.handleOrderBookUpdate(msg[1]);}if(msg[0]==="OSNM"){var ord=$.parseJSON(msg[1]);ord.event="OrderNotification";callBackOnNotification.call(this,ord);}if(msg[0]==="NH"&&(!integral.ui.isTraderView()||!integral.ui.isOrderTraderView())){if(msg[2]=="1"){if(channel==null){ackIdList.push(msg[3]);}else{if(channel=="secondary"){secAckIdList.push(msg[3]);}}}if(cdNotification==null){cdNotification=new Array();}cdNotification.push(msg[1]);}if(msg[0]==="FXB"){var fxbRate=$.parseJSON(msg[1]);callBackOnRateUpdate.call(this,fxbRate,msg[0]);}if((msg[0]==="CDQ")){if(msg[2]=="1"){if(channel==null){ackIdList.push(msg[3]);}else{if(channel=="secondary"){secAckIdList.push(msg[3]);}}}var oData=$.parseJSON(msg[1]);if(oData.FileName!=null){}else{if(cdqResponseType(oData.refId)=="B"){if(oData.CDQData!=null){if(cdqueryObjects==null){cdqueryObjects=new Array();}Array.prototype.push.apply(cdqueryObjects,oData.CDQData);}else{if(oData.EOR==true){integral.ui.enableOrgViewDropdown();integral.ui.enableOrderOrgViewDropdown();integral.setOrderBlotterLoaded(true);}}}else{if(cdqResponseType(oData.refId)=="T"){if(oData.CDQData!=null){if(cdqueryTrades==null){cdqueryTrades=new Array();}Array.prototype.push.apply(cdqueryTrades,oData.CDQData);}else{if(oData.EOR==true){integral.ui.enableOrgViewDropdown();}}}else{if(cdqResponseType(oData.refId)=="O"){if(oData.CDQData!=null){if(cdqueryOrders==null){cdqueryOrders=new Array();}Array.prototype.push.apply(cdqueryOrders,oData.CDQData);}else{if(oData.EOR==true){integral.ui.enableOrderOrgViewDropdown();}}}}}}}}catch(e){integral.log("Exception in processing the proxy response "+e+" : "+msg);}}if(cdqueryTrades!=null){integral.ui.processOrgTradesJmsProxy(cdqueryTrades);}if(cdqueryOrders!=null){integral.ui.processOrgOrdersJmsProxy(cdqueryOrders);}if(cdqueryObjects!=null){integral.ui.processCDQObjectsJmsProxy(cdqueryObjects);}if(cdNotification!=null){integral.ui.processCDQNotifications(cdNotification);}if(orders!=null){integral.log(orders);}if(trades!=null){integral.log(trades);}return wt;},log:function(message,category){var currDate=new Date();var time=currDate.toGMTString();var dataString=JSON.stringify({message:message,level:category});$.ajax({url:"/fxi/fxiapi/log",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,dataType:"json",async:category==$.consts.LOG_ERROR?false:true,cache:false,contentType:"application/json;charset=UTF-8",success:function(data){resetHBFailureCnt();},error:function(e){}});},placeOrderRT:function(callbackFnc,orderOptions){try{var serverLoginTime=$.readCookie("LOGIN_TIME");if(loginTime!=serverLoginTime){var reason="placeOrderRT:Redirecting to login page as client login time is not same as server login time.";integral.log(reason,$.consts.LOG_ERROR);redirectToLoginPage(reason);return;}var orderParams=integral.createOrderParams(orderOptions);if(integral.ui.isFXInsideRT()){if(orderOptions.provider!=null){orderParams.preferredProviders=[orderOptions.provider];}else{orderParams.preferredProviders=["FMA"];}}var dataString=JSON.stringify(orderParams);$.ajax({url:"/fxi/fxiapi/order/place",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(data){if(data.status!="ERROR"){integral.log("Order place success : "+data.order.orderId+" : "+dataString);if(typeof callbackFnc=="function"){callbackFnc.call(this,data.order,{channel:orderOptions.channel,ccyPair:orderOptions.ccyPair});}}else{integral.log("Order placed failed : "+dataString);integral.handleServerError("place order",data);}},error:function(e){integral.log("Order place failed : "+dataString);integral.handleServerError("place order",e);if(typeof callbackFnc=="function"&&orderOptions.channel=="HTML/ESP/PB"){var order=$.parseJSON(dataString);order.orderStatus="Failed";callbackFnc.call(this,order);}}});}catch(ex){integral.log("Error in placing the order : "+orderOptions.ccyPair+" : "+orderOptions.rate+" : "+orderOptions.amount+" : "+orderOptions.oType+" : "+orderOptions.channel);}},placeOrder:function(callbackFnc,orderType,ccyPair,amount,dealtInstr,bS,rate,tif,expiryTime,marketRange,maxShow,execStrg,channel,minQty,orderNotes){try{var serverLoginTime=$.readCookie("LOGIN_TIME");if(loginTime!=serverLoginTime){var reason="placeOrder:Redirecting to login page as client login time is not same as server login time.";integral.log(reason,$.consts.LOG_ERROR);redirectToLoginPage(reason);return;}amount=amount.toString();var execFlags=[0];if(execStrg!=null&&execStrg!=undefined){execFlags=execFlags.concat(execStrg);}var orderParams={amount:amount,minQty:(minQty==undefined?"0.00":minQty),maxShow:maxShow,instrument:ccyPair,dealtInstrument:dealtInstr,clientOrderId:new Date().getTime(),execFlags:execFlags,orderSide:bS,orderType:orderType,customerOrg:userOrg,customerId:userName,expiryType:tif,expiryTime:expiryTime,marketRange:marketRange,tradeChannel:channel,orderNotes:orderNotes};if(orderType==2){orderParams.stopPrice=rate;}else{orderParams.limitPrice=rate;}if(integral.ui.isSalesDealerTradingEnabled()){var customerOrg=$("#salesdealerorgs").val();var customerAccount=$("#salesdealerles").val();orderParams.customerOrg=customerOrg;orderParams.customerAccount=customerAccount;}if(integral.ui.isFXInsideRT()){orderParams.preferredProviders=["FMA"];}var dataString=JSON.stringify(orderParams);if((channel=="HTML/ESP/FXB"||channel=="HTML/ESP/FXL"||channel=="HTML/ESP/FXID/DLP")&&(rate==undefined||rate==null||rate<=0)){integral.log("Placing order failed : "+dataString);callBackOnError.call(this,"Order can not be placed with invalid price.");return;}$.ajax({url:"/fxi/fxiapi/order/place",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(data){if(data.status!="ERROR"){integral.log("Order place success : "+data.order.orderId+" : "+dataString);if(typeof callbackFnc=="function"){callbackFnc.call(this,data.order,{channel:channel,ccyPair:ccyPair});}}else{integral.log("Order placed failed : "+dataString);integral.handleServerError("place order",data);}},error:function(e){integral.log("Order place failed : "+dataString);integral.handleServerError("place order",e);if(typeof callbackFnc=="function"&&channel=="HTML/ESP/PB"){var order=$.parseJSON(dataString);order.orderStatus="Failed";callbackFnc.call(this,order);}}});}catch(ex){integral.log("Error in placing the order : "+ccyPair+" : "+rate+" : "+amount+" : "+orderType+" : "+channel);}},placeOrders:function(callbackFnc,ordersArray){try{var orderList=new Array();for(var i=0;i<ordersArray.length;i++){orderList.push(integral.createOrderParams(ordersArray[i]));}var dataString=JSON.stringify({orderList:orderList});$.ajax({url:"/fxi/fxiapi/order/placeorders",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(data){if(data.orderResponses!=null){for(var i=0;i<data.orderResponses.length;i++){if(data.orderResponses[i].status!="ERROR"){var order=data.orderResponses[i].order;integral.log("Order place success : "+order.orderId+" : ");callbackFnc.call(this,order,{channel:order.channel,ccyPair:order.ccyPair});}else{integral.log("Order placed failed : "+dataString);integral.handleServerError("place order",data.orderResponses[i]);}}}},error:function(e){integral.log("Order place failed : "+dataString);integral.handleServerError("place order",e);}});}catch(ex){integral.log("Error in placing the orders : "+ordersArray);}},resumeOrder:function(orderId,statusEntry,callbackfn){$.ajax({url:"/fxi/fxiapi/order/resume?oid="+orderId,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(data){if(data.status!="ERROR"){integral.log("Order resume success : "+orderId);}else{integral.log("Order resume failed : "+orderId);}if(callbackfn!=null){callbackfn.call(data,statusEntry);}},error:function(e){integral.log("Order resume failed : "+orderId);}});},suspendOrder:function(orderId,statusEntry,callbackfn){$.ajax({url:"/fxi/fxiapi/order/suspend?oid="+orderId,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(data){if(data.status!="ERROR"){integral.log("Order suspend success : "+orderId);}else{integral.log("Order suspend failed : "+orderId);}if(callbackfn!=null){callbackfn.call(data,statusEntry);}},error:function(e){integral.log("Order suspend failed : "+orderId);}});},amendIFDoneOrderAPI:function(ifDoneAmendReqJson){$.ajax({url:"/fxi/fxiapi/order/amend/batch",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:ifDoneAmendReqJson,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(data){if(data.status!="ERROR"){integral.log("Order amend success : "+ifDoneAmendReqJson);}else{integral.log("Order amend failed : "+ifDoneAmendReqJson);integral.handleServerError("amend order",data);}},error:function(e){integral.log("Order amend failed : "+ifDoneAmendReqJson);integral.handleServerError("amend order",e);}});},amendOrder:function(orderOptions){var orderParams={orderId:orderOptions.orderId};if(orderOptions.newOrderAmount!=null){orderParams.newOrderAmount=orderOptions.newOrderAmount;}if(orderOptions.newOrderRate!=null){orderParams.newOrderRate=orderOptions.newOrderRate;}if(orderOptions.newTriggerRate!=null){orderParams.newTriggerRate=orderOptions.newTriggerRate;}var dataString=JSON.stringify(orderParams);$.ajax({url:"/fxi/fxiapi/order/amend",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(data){if(data.status!="ERROR"){integral.log("Order amend success : "+dataString);}else{integral.log("Order amend failed : "+dataString);integral.handleServerError("amend order",data);}},error:function(e){integral.log("Order amend failed : "+dataString);integral.handleServerError("amend order",e);}});},createOrderParams:function(orderOptions){var orderType=orderOptions.oType;var ccyPair=orderOptions.ccyPair;var amount=orderOptions.amount;var dealtInstr=orderOptions.dealtInstr;var bS=orderOptions.buySell;var rate=orderOptions.rate;var tif=orderOptions.timeInFrc;var expiryTime=orderOptions.expTime;var marketRange=orderOptions.mktRange;var maxShow=orderOptions.showAmt;var execStrg=orderOptions.execStrg;var channel=orderOptions.channel;var contingencyParameters=orderOptions.contingencyParameters;var clientOrderId=orderOptions.clientOrderId;var executionStartTime=orderOptions.executionStartTime;var executionEndTime=orderOptions.executionEndTime;var minQty=orderOptions.minQty;var serverLoginTime=$.readCookie("LOGIN_TIME");if(loginTime!=serverLoginTime){var reason="createOrderParams:Redirecting to login page as client login time is not same as server login time.";integral.log(reason,$.consts.LOG_ERROR);redirectToLoginPage(reason);return;}if(maxShow!=undefined&&maxShow!=0){maxShow=integral.ui.getExpandedAmt(maxShow)>integral.ui.getExpandedAmt(amount)?amount:maxShow;}amount=amount.toString();var orderParams={amount:amount,minQty:minQty==undefined?"0.00":minQty,maxShow:maxShow,instrument:ccyPair,dealtInstrument:dealtInstr,clientOrderId:new Date().getTime(),orderSide:bS,orderType:orderType,customerOrg:userOrg,customerId:userName,expiryType:tif,expiryTime:expiryTime,marketRange:marketRange,tradeChannel:channel,contingencyParameters:contingencyParameters,clientOrderId:clientOrderId,executionStartTime:executionStartTime,executionEndTime:executionEndTime};if(execStrg!=null&&execStrg!=undefined){orderParams.execFlags=execStrg;}if(orderType==2){orderParams.stopPrice=rate;orderParams.limitPrice=rate;}else{orderParams.limitPrice=rate;}if(orderOptions.executionStrategyParams!=null){orderParams.executionStrategyParams=orderOptions.executionStrategyParams;}if(orderOptions.executionStrategyName!=null){orderParams.executionStrategyName=orderOptions.executionStrategyName;}if(orderOptions.strategyType!=null){orderParams.strategyType=orderOptions.strategyType;}if(integral.ui.isSalesDealerTradingEnabled()){var customerOrg=$("#salesdealerorgs").val();var customerAccount=$("#salesdealerles").val();orderParams.customerOrg=customerOrg;orderParams.customerAccount=customerAccount;}if(orderOptions.metadata&&orderOptions.metadata.indexOf("ifDone")!=-1){orderParams.orderMetadata=orderOptions.metadata+",placedByOrg~"+integral.getUserOrg();}else{if(orderOptions.metadata!=null){orderParams.orderMetadata=(orderOptions.metadata.indexOf("placedByOrg")==-1)?orderOptions.metadata+" placedByOrg~"+integral.getUserOrg():orderOptions.metadata;}else{if(!orderOptions.metadata){orderParams.orderMetadata="placedByOrg~"+integral.getUserOrg();}}}orderParams.orderNotes=orderOptions.orderNotes;if(orderOptions.preferredProviders!=undefined){orderParams.preferredProviders=orderOptions.preferredProviders;}if(orderOptions.fixingName!=null){orderParams.fixingName=orderOptions.fixingName;}if((channel=="HTML/ESP/FXB"||channel=="HTML/ESP/FXL"||channel=="HTML/ESP/FXID/DLP")&&(rate==undefined||rate==null||rate<=0)){integral.log("Placing order failed : "+orderParams);callBackOnError.call(this,"Order can not be placed with invalid price.");return;}if(orderOptions.directedOrderParams!=null){orderParams.spotSpread=orderOptions.spotSpread;orderParams.preferredProviders=orderOptions.preferredProviders;orderParams.directedOrderParams=orderOptions.directedOrderParams;orderParams.tenor=orderOptions.tenor;}orderOptions=null;return orderParams;},placeRfs:function(callbackFnc,rfspanel,quoteid,sidetype,currenypair,dealtinstr,channel,addnlInstr){if(callBackOnRfsAccept==undefined){callBackOnRfsAccept=callbackFnc;}var dataString=JSON.stringify({quoteId:quoteid,sideType:parseInt(sidetype),instrument:currenypair,dealtInstrument:dealtinstr,tradeChannel:channel,stmtInst:addnlInstr});$.ajax({url:"/fxi/fxiapi/rfs/accept",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(data){if(data.status!="ERROR"){integral.log("Rfs trade success : "+quoteid);if(typeof callBackOnRfsAccept=="function"){callBackOnRfsAccept.call(data,rfspanel);}}else{integral.handleServerError("Place rfs "+currenypair,data);}},error:function(e){integral.handleServerError("Place rfs "+currenypair,e);}});},getOrdersAfterReconnect:function(callbackFnc,orderIds){$.ajax({url:"/fxi/fxiapi/order/getOrdersByOrderId?orderIds="+orderIds,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:true,cache:false,success:function(data){if(data.status!="ERROR"){if(orderBlotterLoaded&&tradeBlotterLoaded){callbackFnc.call(data);}}},error:function(e){e.systemError=true;integral.handleServerError("getOrdersAfterReconnect "+orderIds,e);}});},cancelOrder:function(callbackFnc,orderId){$.ajax({url:"/fxi/fxiapi/order/cancel?oid="+orderId,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:true,cache:false,success:function(data){if(data.status!="ERROR"){callbackFnc.call(data,orderId);integral.log("Order cancelled successfully : "+orderId);}else{integral.handleServerError("CancelOrder "+orderId,data);}},error:function(e){integral.handleServerError("CancelOrder "+orderId,e);}});},confirmTrade:function(tradeId,errCount){errCount=(errCount!=null?parseInt(errCount)+1:1);$.ajax({url:"/fxi/fxiapi/trade/confirmTrade?transactionId="+tradeId,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:true,cache:false,timeout:30000,success:function(data){if(data.status=="ERROR"){integral.log("Could not confirm trade "+tradeId+" "+data.statusText+" Error count : "+errCount);if(errCount<12){setTimeout("integral.confirmTrade('"+tradeId+"','"+errCount+")",5000);}}},error:function(e){integral.log("Error in confirm trade "+tradeId+" Error count : "+errCount+" "+e.statusText);if(errCount<12){setTimeout("integral.confirmTrade('"+tradeId+"','"+errCount+"')",5000);}}});},fillAtMarket:function(callbackFnc,orderId){var dataString=JSON.stringify({orderId:orderId});$.ajax({type:"POST",url:"/fxi/fxiapi/order/cancelReplace",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:dataString,dataType:"json",cache:false,contentType:"application/json; charset=UTF-8",success:function(data){},error:function(e){integral.handleServerError("Fill @ market "+orderId,e);}});},saveUIComponent:function(name,uiData,callBackFunc,callBackObj){integral.log("Saving "+name+" : "+uiData);$.ajax({url:"/fxi/fxiapi/pref/save/"+name,type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:'{"pref":'+uiData+"}",dataType:"json",async:false,cache:false,contentType:"application/json;charset=UTF-8",success:function(data){if(data.status=="OK"&&callBackFunc){callBackFunc.call(data,callBackObj);}},error:function(data){integral.log("Save failed for "+name+": "+uiData+" "+data.status+" - "+data.statusText);}});},loadUIComponent:function(name){var uiState;$.ajax({url:"/fxi/fxiapi/pref/load/"+name,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(data){uiState=data.pref;integral.log("Loading "+name+" : "+uiState);},error:function(e){}});return uiState;},saveGeneralPrefs:function(name,uiData){integral.log("Saving "+name+" : "+uiData);$.ajax({url:"/fxi/fxiapi/pref/save/"+name,type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:'{"pref":'+uiData+"}",dataType:"json",async:false,cache:false,contentType:"application/json;charset=UTF-8",success:function(data){},error:function(data){integral.log("Save failed for "+name+": "+uiData+" "+data.status+" - "+data.statusText);}});},loadGeneralPrefs:function(name){var tradingPrefs;$.ajax({url:"/fxi/fxiapi/pref/load/"+name,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(data){tradingPrefs=data;integral.log("Loading "+name+" : "+tradingPrefs.pref);},error:function(e){}});return tradingPrefs;},loadMultipleComponents:function(comps,callbackFnc){$.ajax({url:"/fxi/fxiapi/pref/multiLoad?components="+comps,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(data){callbackFnc.call(data);},error:function(e){this.systemError=true;callBackOnError.call(this,"Load of settings failed for "+name+": "+e.status+" "+e.statusText);}});},loadSettings:function(name,callbackFnc){$.ajax({url:"/fxi/fxiapi/pref/load/"+name,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(data){callbackFnc.call(data);integral.log("Loading "+name+" : "+data.pref);},error:function(e){this.systemError=true;callBackOnError.call(this,"Load of settings failed for "+name+": "+e.status+" "+e.statusText);}});},saveSettings:function(name,uiData,callbackFnc,changedDelta){var map={pref:uiData};$.ajax({url:"/fxi/fxiapi/pref/save/"+name,type:"POST",cache:false,beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:JSON.stringify(map),dataType:"json",contentType:"application/json;charset=UTF-8",success:function(data){if(changedDelta){integral.log("Setting changed "+changedDelta);}if(callbackFnc!=undefined&&callbackFnc!=null){callbackFnc.call(data);}},error:function(data){integral.log("Setting change failed "+changedDelta);}});},loadRMMSettings:function(name,callbackFnc){$.ajax({url:"/fxi/fxiapi/rmmpref/load/"+name,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(data){callbackFnc.call(data);integral.log("Loading "+name+" : "+data.pref);},error:function(e){callBackOnError.call(this,"Load of settings failed for "+name+": "+e.status+" "+e.statusText);}});},loadRMMAllowedPlans:function(callbackFnc){$.ajax({url:"/fxi/fxiapi/rmmpref/plans/",type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("text/plain;charset=UTF-8");}},contentType:"text/plain;charset=UTF-8",async:false,cache:false,success:function(data){callbackFnc.call(data);integral.log("Allowed User Plans "+data);},error:function(e){callBackOnError.call(this,"Load of allowed plans failed: "+e.status+" "+e.statusText);}});},rmmBlockRateUpdateOnHide:function(){},rmmBlockOrderBlotterUpdateOnHide:function(){},rmmBlockPositionBlotterUpdateOnHide:function(){},rmmBlockNotificationUpdateOnHide:function(){},rmmBlockRfsUpdateOnHide:function(){},rmmBlockRfsFBRateUpdateOnHide:function(){},useRmmOnOrderBlotterUpdateCallbackFn:function(){if(callBackOnOrderUpdate!=undefined&&callBackOnOrderUpdate!=this.rmmBlockOrderBlotterUpdateOnHide){rmmCallBackOnOrderBlotterUpdate=callBackOnOrderUpdate;callBackOnOrderUpdate=this.rmmBlockOrderBlotterUpdateOnHide;}},useRmmOnPositionBlotterUpdateCallbackFn:function(){if(callBackOnPositionUpdate!=undefined&&callBackOnPositionUpdate!=this.rmmBlockPositionBlotterUpdateOnHide){rmmCallBackOnPositionBlotterUpdate=callBackOnPositionUpdate;callBackOnPositionUpdate=this.rmmBlockPositionBlotterUpdateOnHide;}},useRmmOnRateUpdateCallbackFn:function(){if(callBackOnRateUpdate!=undefined&&callBackOnRateUpdate!=this.rmmBlockRateUpdateOnHide){rmmCallBackOnRateUpdate=callBackOnRateUpdate;callBackOnRateUpdate=this.rmmBlockRateUpdateOnHide;}},useRmmOnNotificationUpdateCallbackFn:function(){if(callBackOnNotification!=undefined&&callBackOnNotification!=this.rmmBlockNotificationUpdateOnHide){rmmCallBackOnNotification=callBackOnNotification;callBackOnNotification=this.rmmBlockNotificationUpdateOnHide;}},useRmmOnRfsUpdateCallbackFn:function(){if(callBackOnRfsUpdate!=undefined&&callBackOnRfsUpdate!=this.rmmBlockRfsUpdateOnHide){rmmCallBackOnRfsUpdate=callBackOnRfsUpdate;callBackOnRfsUpdate=this.rmmBlockRfsUpdateOnHide;}},useRmmOnRfsFBRateUpdateCallbackFn:function(){if(callBackOnRfsFBRateUpdate!=undefined&&callBackOnRfsFBRateUpdate!=this.rmmBlockRfsFBRateUpdateOnHide){rmmCallBackOnRfsFBRateUpdate=callBackOnRfsFBRateUpdate;callBackOnRfsFBRateUpdate=this.rmmBlockRfsFBRateUpdateOnHide;}},useOriginalOnOrderBlotterUpdateCallbackFn:function(){if(callBackOnOrderUpdate===this.rmmBlockOrderBlotterUpdateOnHide){callBackOnOrderUpdate=rmmCallBackOnOrderBlotterUpdate;}},useOriginalOnPositionBlotterUpdateCallbackFn:function(){if(callBackOnPositionUpdate===this.rmmBlockPositionBlotterUpdateOnHide){callBackOnPositionUpdate=rmmCallBackOnPositionBlotterUpdate;}},useOriginalOnRateUpdateCallbackFn:function(){if(callBackOnRateUpdate===this.rmmBlockRateUpdateOnHide){callBackOnRateUpdate=rmmCallBackOnRateUpdate;}},useOriginalOnNotificationUpdateCallbackFn:function(){if(callBackOnNotification===this.rmmBlockNotificationUpdateOnHide){callBackOnNotification=rmmCallBackOnNotification;}},useOriginalOnRfsUpdateCallbackFn:function(){if(callBackOnRfsUpdate===this.rmmBlockRfsUpdateOnHide){callBackOnRfsUpdate=rmmCallBackOnRfsUpdate;}},useOriginalOnRfsFBRateUpdateCallbackFn:function(){if(callBackOnRfsFBRateUpdate===this.rmmBlockRfsFBRateUpdateOnHide){callBackOnRfsFBRateUpdate=rmmCallBackOnRfsFBRateUpdate;}},setOnRateUpdateCallbackFn:function(callbackFnc){callBackOnRateUpdate=callbackFnc;},loadRfsWaitTime:function(id,callbackFnc){$.ajax({url:"/fxi/fxiapi/rmmpref/rfsWaitTime/",type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("text/plain;charset=UTF-8");}},contentType:"text/plain;charset=UTF-8",async:false,cache:false,success:function(data){callbackFnc.call("",id,data);integral.log("RFS Wait Time "+data);},error:function(e){callBackOnError.call(this,"Load of RFS Wait Time failed: "+e.status+" "+e.statusText);}});},saveRMMSettings:function(name,uiData,callbackFnc,changedDelta){var map={pref:uiData};$.ajax({url:"/fxi/fxiapi/rmmpref/save/"+name,type:"POST",cache:false,beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:JSON.stringify(map),dataType:"json",contentType:"application/json;charset=UTF-8",success:function(data){if(changedDelta){integral.log("Setting changed "+changedDelta);}if(callbackFnc!=undefined&&callbackFnc!=null){callbackFnc.call(data);}},error:function(data){integral.log("Setting change failed "+changedDelta);}});},saveClientUIComponent:function(name,uiData){integral.log("Saving ClientUI "+name+" : "+uiData);$.ajax({url:"/fxi/fxiapi/clientpref/save/"+name,type:"POST",cache:false,beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},data:'{"pref":'+uiData+"}",dataType:"json",contentType:"application/json;charset=UTF-8",success:function(data){},error:function(e){}});},loadClientUIComponent:function(name){var uiState;$.ajax({url:"/fxi/fxiapi/clientpref/load/"+name,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(data){if(data.status!="ERROR"){uiState=data.pref;integral.log("Loading ClientUI "+name+" : "+uiState);}},error:function(e){}});return uiState;},saveUserDisplayPrefs:function(displayFmt,callbackFnc){$.ajax({url:"/fxi/fxiapi/pref/save/dispPref",type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},async:false,data:displayFmt,dataType:"json",cache:false,contentType:"application/json;charset=UTF-8",success:function(data){callbackFnc.call($.parseJSON(displayFmt));},error:function(e){}});},loadUserDisplayPrefs:function(callbackFnc){$.ajax({url:"/fxi/fxiapi/pref/load/dispPref",type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",cache:false,success:function(data){if(data.status=="OK"){callbackFnc.call(data.userDP);}else{callBackOnError.call(this,"Load user display prefs failed :"+data.status+" "+data.errorCode);}},error:function(e){this.systemError=true;callBackOnError.call(this,"Load user display prefs failed :"+e.status+" "+e.statusText);}});},getProvidersList:function(){var provArr={};$.ajax({type:"GET",url:"/fxi/fxiapi/refdata/providers/real",async:false,cache:false,dataType:"json",success:function(data){provArr=data;},error:function(e){e.systemError=true;integral.handleServerError("getProvidersList",e);}});return provArr.sort();},enableSecondaryServer:function(){ssExists=true;if(!secondaryServiceInit){secondaryServiceInit=true;integral.initSecondaryService();}},setUserInfo:function(user,org){userName=user;userOrg=org;userPlusOrg=user+"_"+org;},setErrorHandler:function(handleError){callBackOnError=handleError;},checkAuthToken:function(redirectTo){var userName,userOrg;loginPage=redirectTo;},getOrders:function(callbackFnc){var aOrders;$.ajax({url:"/fxi/fxiapi/order/activeAndToday",cache:false,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:true,success:function(data){aOrders=data;callbackFnc.call(aOrders);orderBlotterLoaded=true;if(orderBlotterLoaded&&tradeBlotterLoaded){updateReplayMessages();}},error:function(e){e.systemError=true;integral.handleServerError("activeAndToday",e);}});},setOrderBlotterLoaded:function(flag){orderBlotterLoaded=flag;},getActiveOrders:function(callbackFnc,orgLevel,forDate){var aOrders;var urlStr="/fxi/fxiapi/order/active";if(orgLevel){urlStr="/fxi/fxiapi/order/active?orgLevel=true&enrichTrades=true";}$.ajax({url:urlStr,cache:false,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:true,success:function(data){aOrders=data;callbackFnc.call(aOrders);},error:function(e){e.systemError=true;integral.handleServerError("getActiveOrders",e);}});},getOrderById:function(orderId,callbackFnc){$.ajax({url:"/fxi/fxiapi/order/q?orderId="+orderId,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:true,cache:false,success:function(data){if(data.length>0){var order=data[0];callbackFnc.call(order);}else{integral.log("No order retrieved for orderId: "+orderId);}},error:function(e){e.systemError=true;integral.handleServerError("getOrderById "+orderId,e);}});},getTradesForOrder:function(orderId,callbackFnc){var thr=$.ajax({url:"/fxi/fxiapi/trade/q?orderId="+orderId,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:true,cache:false,timeout:60000,success:function(data){callbackFnc.call(data);},error:function(e,t){e.systemError=true;if(t=="timeout"){callbackFnc.call(t);integral.log("Open order ticket - Trade query timedout for order: "+orderId);}else{if(t=="abort"){integral.log("getTradesForOrder aborted : User might have closed the order ticket "+orderId);}else{integral.handleServerError("getTradesForOrder "+orderId,e);}}}});return thr;},getTradesForTradeIds:function(tradeIds,callbackFnc){$.ajax({url:"/fxi/fxiapi/trade/getTradesByTradeId?tradeIds="+tradeIds,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:true,cache:false,success:function(data){callbackFnc.call(data);},error:function(e,t){integral.log("Could not get Trades for : "+tradeIds);}});},downloadCDQResponseFile:function(callbackFnc,fileName){jQuery.ajax({async:true,url:"/fxi/fxiapi/config/download?fileName="+fileName+"&time="+(new Date().getTime()),type:"GET",dataType:"text",data:null,success:function(xmlStr){if(callbackFnc!=null){callbackFnc.call(this,xmlStr);}}});},getCDQResponseTemplate:function(callbackFnc){$.ajax({url:"/fxi/fxiapi/util/responseTemplateInJSON",type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(data){if(callbackFnc!=null){callbackFnc.call(this,data);}},error:function(e){integral.handleServerError("getCDQResponseTemplate ",e);}});},getOrgTradesForDate:function(callbackFnc,queryType,forDate){$.ajax({url:"/fxi/fxiapi/trade/tradeDetails?fromDate="+forDate+"&toDate="+forDate+"&queryDataType="+queryType+"&writeToFile=false&fixedFormatting=false&compaction=false&btchSize=10&maxCount="+integral.ui.getMaxCdqCount(),type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},queryType:queryType,contentType:"application/json;charset=UTF-8",async:true,cache:false,timeout:5000,success:function(data){if(orgTradesForDateTmOut){clearTimeout(orgTradesForDateTmOut);}orgTradesForDateTmOut=setTimeout(function(){enableOrgViewDropdown(data.responseTuples[0].value);},60000);cdqInProgress=data.responseTuples[0].value;var refId=data.responseTuples[0].value;cdQueryRefMap[queryType]=(refId>cdQueryRefMap[queryType])?refId:cdQueryRefMap[queryType];if(callbackFnc!=null){callbackFnc.call(this,queryType,cdqInProgress);}},error:function(data,t,err){if(t=="timeout"){integral.log("tradeDetails query for Org view timedout");}setTimeout(function(){enableOrgViewDropdown(null);},60000);integral.handleServerError("getOrgTradesForCurrentDate ",err);}});},getTradesForCurrentDate:function(callbackFnc){var trades;$.ajax({url:"/fxi/fxiapi/trade/q?dateRange=Today",type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:true,cache:false,success:function(data){callbackFnc.call(this,data);tradeBlotterLoaded=true;integral.ui.enableOrgViewDropdown();if(orderBlotterLoaded&&tradeBlotterLoaded){updateReplayMessages();}},error:function(e){e.systemError=true;integral.handleServerError("getTradesForCurrentDate ",e);}});},getTradeById:function(tradeId,callbackFnc){$.ajax({url:"/fxi/fxiapi/trade/q?tradeId="+tradeId,type:"GET",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:true,cache:false,success:function(data){if(data.length>0){var trade=data[0];callbackFnc.call(trade);}else{integral.log("No trade retrieved for tradeId: "+tradeId);}},error:function(e){e.systemError=true;integral.handleServerError("getTradeById "+tradeId,e);}});},getBaseAmount:function(dealtCcy,baseCcy,dealAmt,settledAmt){if(baseCcy==dealtCcy){return dealAmt;}else{return settledAmt;}},getVarAmount:function(dealtCcy,baseCcy,dealAmt,settledAmt){if(baseCcy==dealtCcy){return settledAmt;}else{return dealAmt;}},getBrandInfo:function(orgName){var dataString=JSON.stringify({clientType:"WEBCLIENT",clientVersion:"1.0",org:orgName});var brandInfo;$.ajax({url:"/fxi/fxiapi/brand/brandInfo",data:dataString,type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",async:false,cache:false,success:function(data){if(data.status=="ERROR"){integral.log("No branded info found for: "+orgName+" "+data.errorCode);}brandInfo=data.brandData;},error:function(e){integral.log("No branded info found for: "+orgName+e.status);}});return brandInfo;},startHeartbeat:function(){startPoller();integral.log("Started polling timer.");},subscribeSwapPoints:function(req){var dataString=JSON.stringify(req);$.ajax({url:"/fxi/fxiapi/marketdata/subscribeSwapPoint",data:dataString,type:"POST",beforeSend:function(req){if(req&&req.overrideMimeType){req.overrideMimeType("application/json;charset=UTF-8");}},contentType:"application/json;charset=UTF-8",cache:false,dataType:"json",success:function(data){if(data.status=="ERROR"){integral.log("Subscribtion failed for Swap Points");}},error:function(e){integral.log("Subscribtion failed for Swap Points");}});}};})(jQuery);(function($){var uName="",org="",pw="";$.fn.createFxLogin=function(url,brand,isSSOEnabled){$.props={redirectUrl:url,brandedPath:brand};var maindiv=this;var loginhtml=$('<div id="logincontainer"><div class="loginbranding"></div><div id="errormessagelogin"></div><div id="loginelements"><form method="post" id="login_form" onsubmit="return false;" autocomplete="off" action=""><div class="logininput"><label id="orglable" for="organization" class="loginlabel2">'+integral.common.getLabel($.consts.ORGANIZATION)+'</label><input id="organization" name="organization" value="" title="'+integral.common.getLabel($.consts.ORGANIZATION).toLowerCase()+'" type="text" class="logininputfields"></div><div style="clear:both;"></div><div class="logininput"><label id="unlable" for="username" class="loginlabel2">'+integral.common.getLabel($.consts.USER_NAME)+'</label><input id="username" name="username" value="" title="'+integral.common.getLabel($.consts.USER_NAME).toLowerCase()+'" type="text" class="logininputfields"></div><div style="clear:both;"></div><div class="logininput"><label id="pwlable" for="password" class="loginlabel2">'+integral.common.getLabel($.consts.PASSWORD)+'</label><input id="password" name="password" value="" title="'+integral.common.getLabel($.consts.PASSWORD).toLowerCase()+'" type="password" class="logininputfields"></div><div style="clear:both;"></div><div id="otpdiv" class="logininput"><label id="otplable" for="password" class="loginlabel2">OTP</label><input id="otp" name="otp" value="" title="OTP" type="password" class="logininputfields"></div><div style="clear:both;"></div><div class="loginbuttonbar"><span class="loginbuttonalign"><input id="login_submit" value='+integral.common.getLabel($.consts.LOGIN)+' type="submit" class="loginbutton"></span><span id="forgot" class="forgot"><span>'+integral.common.getLabel($.consts.FORGOT_PASSWORD)+'</span></span></div><span id="2faspan" class="forgot"><label class="">Static</label><input checked type=radio name=pwchoice value="0">&nbsp;&nbsp;<label class="">OTP</label><input type=radio name=pwchoice value="1">&nbsp;&nbsp;<label class="">2FA</label><input type=radio id="2fa" name=pwchoice value="2"></span></form></div><div style="clear:both;"></div><div id="logincopyright">'+integral.common.getLabel($.consts.IDC_COPYRIGHT)+"</div></div>");this.append(loginhtml);if($.readCookie("LogoutError")!=null){$("#errormessagelogin").html(jQuery.i18n.prop($.readCookie("LogoutError")));integral.removeCookie("LogoutError");}var storedname=$.readCookie(brand+"storedname");var storedorg=$.readCookie(brand+"storedorg");if(storedname!=undefined){$("#unlable").hide();$("#username").val(storedname);}if(storedorg!=undefined){$("#orglable").hide();$("#organization").val(storedorg);}$("#otpdiv").hide();if(!integral.common.has2FAPermission()){$("#2faspan").hide();}else{$('input[id="2fa"]:radio').prop("checked",true);setLoginMethodFields("2");}function setLoginMethodFields(loginMethod){switch(loginMethod){case"0":$("#otp").val("");$("#otplable").show();$("#otpdiv").hide();$("#password").removeAttr("disabled");$("#forgot").removeClass("forgotDisabled");$("#forgot").addClass("forgot");break;case"1":$("#otpdiv").show();$("#password").val("");$("#pwlable").show();$("#password").attr("disabled","disabled");$("#forgot").removeClass("forgot");$("#forgot").addClass("forgotDisabled");break;case"2":$("#otpdiv").show();$("#password").removeAttr("disabled");$("#forgot").removeClass("forgotDisabled");$("#forgot").addClass("forgot");break;}}$("input[name=pwchoice]:radio").change(function(){setLoginMethodFields(this.value);});$("#username").on("focus keydown",function(){$("#unlable").hide();});$("#username").on("blur keyup",function(){if(this.value==undefined||$.trim(this.value)==""){$("#unlable").show();}else{$("#unlable").hide();}});$("#otp").on("focus keydown",function(){$("#otplable").hide();});$("#otp").on("blur keyup",function(){if(this.value==undefined||$.trim(this.value)==""){$("#otplable").show();}else{$("#otplable").hide();}});$("#password").on("focus keydown",function(){$("#pwlable").hide();});$("#password").on("blur keyup",function(){if(this.value==undefined||$.trim(this.value)==""){$("#pwlable").show();}else{$("#pwlable").hide();}});$("#organization").on("focus keydown",function(){$("#orglable").hide();});$("#organization").on("blur keyup",function(){if(this.value==undefined||$.trim(this.value)==""){$("#orglable").show();}else{$("#orglable").hide();}});$("#forgot").click(function(){if($("#forgot").attr("class")=="forgot"){integral.forgotPassword($("#username").val(),$("#organization").val(),forgotPassCallback);}});$("#login_submit").click(function(){$("#errormessagelogin").html("");uName=$("#username").val();org=$("#organization").val();if(!checkDevUser($("#username").val())){return;}var logintype=parseInt($("input[name=pwchoice]:radio:checked").val());pw=hex_md5($("#password").val());var otp=$("#otp").val();if(logintype==0){otp="";}else{if(logintype==1){pw="";}}integral.setCookie(brand+"storedname",$("#username").val(),true);integral.setCookie(brand+"storedorg",$("#organization").val(),true);if(isSSOEnabled==="false"||isSSOEnabled===false){integral.setCookieForRedirection($("#organization").val());}var valid=validateCredentials(uName,$("#password").val(),org,otp,logintype);if(!valid){$("#errormessagelogin").html(integral.common.getLabel($.consts.PLEASE_ENTER_VALID_CREDENTIALS));return;}if(isSSOEnabled==="true"||isSSOEnabled===true){integral.loginSSO(uName,pw,org,otp,handleSSOResponse,$.props.brandedPath);}else{integral.login2fa(uName,pw,org,otp,mycallback,$.props.brandedPath);}});function validateCredentials(uName,pw,org,otp,loginType){var clientValidation=integral.common.getProperty("client_side_login_validation","true");if(clientValidation=="skip"){return true;}switch(loginType){case 0:if(uName==undefined||uName==""||pw==undefined||pw==""||org==undefined||org==""){return false;}break;case 1:if(uName==undefined||uName==""||otp==undefined||otp==""||org==undefined||org==""){return false;}break;case 2:if(uName==undefined||uName==""||pw==undefined||pw==""||otp==undefined||otp==""||org==undefined||org==""){return false;}break;}return true;}function checkDevUser(username){if($.props.redirectUrl.indexOf("slpweb")!=-1){return(jQuery.i18n.prop("dev_users").indexOf(username)!=-1||jQuery.i18n.prop("dev_users").indexOf("ALL")!=-1);}else{return true;}}};function switchToPasswordResetScreen(loginResponse){var resethtml=$('<div id="logincontainer"><div class="loginbranding"></div><div id="errormessagelogin">'+integral.common.getLabel($.consts.CREATE_NEW_PASSWORD)+'</div><div id="loginelements"><form method="post" id="login_form" onsubmit="return false;" action=""><div class="logininput"><label id="oplable" for="password" class="loginlabel2">'+integral.common.getLabel($.consts.ENTER_PASSWORD)+'</label><input id="oldpass" name="password" title="password" type="password" class="logininputfields" autocomplete="off"></div><div style="clear:both;"></div><div class="logininput"><label id="nplable" for="newpassword" class="loginlabel2">'+integral.common.getLabel($.consts.NEW_PASSWORD)+'</label><input id="newpass" name="newpassword" value="" title="newpassword" type="password" class="logininputfields"></div><div style="clear:both;"></div><div class="logininput"><label id="cplable" for="confirmpassword" class="loginlabel2">'+integral.common.getLabel($.consts.CONFIRM_PASSWORD)+'</label><input id="confirmpass" name="confirmpassword" value="" title="confirmpassword" type="password" class="logininputfields"></div><div style="clear:both;"></div><div class="loginbuttonbar"><span class="loginbuttonalign"><input id="save" value="Save" type="submit" class="loginbutton"></span></div></form></div><div style="clear:both;"></div><div id="logincopyright">'+integral.common.getLabel($.consts.IDC_COPYRIGHT)+"</div></div>");$("#fxlogin").find("#logincontainer").remove();$("#fxlogin").prepend(resethtml);$("#oldpass").on("focus keydown",function(){$("#oplable").hide();});$("#oplable").click(function(){$("#oplable").hide();$("#oldpass").focus();});$("#oldpass").on("blur keyup",function(){if(this.value==undefined||$.trim(this.value)==""){$("#oplable").show();}else{$("#oplable").hide();}});$("#newpass").on("focus keydown",function(){$("#nplable").hide();});$("#nplable").click(function(){$("#nplable").hide();$("#newpass").focus();});$("#newpass").on("blur keyup",function(){if(this.value==undefined||$.trim(this.value)==""){$("#nplable").show();}else{$("#nplable").hide();}});$("#confirmpass").on("focus keydown",function(){$("#cplable").hide();});$("#cplable").click(function(){$("#cplable").hide();$("#confirmpass").focus();});$("#confirmpass").on("blur keyup",function(){if(this.value==undefined||$.trim(this.value)==""){$("#cplable").show();}else{$("#cplable").hide();}});$("#save").click(function(){if($("#newpass").val()==$("#confirmpass").val()){var data=integral.resetPassword($("#oldpass").val(),$("#newpass").val(),undefined);if(data.status=="OK"){if(loginResponse.legalAgreementStatus==false){integral.ui.showLicenseAgreement(loginResponse);}else{integral.log("Reset password.dealingServiceCallBack:Loading main panel.");loadMainPanel(loginResponse);}}else{mycallback(data);}}else{$("#errormessagelogin").html(integral.common.getLabel($.consts.NEW_CONFIRMPASSWD_DONT_MATCH));}});}function forgotPassCallback(data){if(data.status=="OK"){$("#errormessagelogin").html(integral.common.getLabel($.consts.NEW_PASSWORD_SENT));}else{$("#errormessagelogin").html(data.errorCode);}}function getErrorMsg(errorKey){integral.log("Login Error : "+errorKey);if(errorKey.indexOf("ERR_OTP_INTERNAL_SERVER_ERROR_")==0){return errorKey.replace("ERR_OTP_INTERNAL_SERVER_ERROR_",integral.common.getLabel($.consts.CONTACT_SUPPORT_FORLOGIN)+" ");}return integral.common.getLabel(integral.common.getErrorMsg(errorKey));}function mycallback(data){if($.parseJSON(data.responseText)!=null&&$.parseJSON(data.responseText).status=="ERROR"){$("#errormessagelogin").html(getErrorMsg($.parseJSON(data.responseText).errorCode));}else{if(data.status!=null&&data.status=="ERROR"){$("#errormessagelogin").html(getErrorMsg(data.errorCode));}else{if(data.status==500){$("#errormessagelogin").html(getErrorMsg(data.statusText));}else{if(data.status=="OK"){var uinfo=$.parseJSON(this.data);$.props.user=uinfo.user;$.props.org=uinfo.org;if(data.changePassword==true){switchToPasswordResetScreen();}else{if(data.legalAgreementStatus==false){integral.ui.showLicenseAgreement(data);}else{integral.log("mycallback:Loading main panel.");loadMainPanel(data);}}}}}}}function dealingServiceCallBack(data){if(data.status==401){$("#errormessagelogin").html(getErrorMsg(data.statusText));}else{if(data.status=="OK"){var uinfo=$.parseJSON(this.data);$.props.user=uinfo.user;$.props.org=uinfo.org;if(data.changePassword==true){switchToPasswordResetScreen(data);}else{if(data.legalAgreementStatus==false){integral.ui.showLicenseAgreement(data);}else{integral.log("dealingServiceCallBack:Loading main panel.");loadMainPanel(data);}}}}}function handleSSOResponse(data){if(data.status=="OK"){integral.initDealingService(uName,pw,org,data.brandedPath,dealingServiceCallBack);}else{mycallback(data);}}function newPasswordCallBack(aa,data){if(data.status=="OK"){integral.log("newPasswordCallBack:Loading main panel.");loadMainPanel(data);}else{mycallback(data);}}})(jQuery);integral.common=(function($){$.consts={API_VERSION:"1.21",MAIN_APP_TITLE:"main_app_title",HEADER_LOGOUTBUTTON:"header_logoutbutton",MAIN_HDR_STNGS:"main_hdr_stngs",MAIN_HDR_TRDNG:"main_hdr_trdng",TRADING_PANELS:"trading_panels",HDR_TOGGLETRDNG_OFF:"hdr_toggletrdng_off",HDR_TOGGLETRDNG_ON:"hdr_toggletrdng_on",MAIN_HDR_CURRENT_USER:"main_hdr_current_user",MAIN_HDR_THEMES:"main_hdr_themes",MAIN_HDR_ORG:"main_hdr_org",MAIN_HDR_TRADEDATE:"main_hdr_tradedate",MAIN_HDR_LOCALTIME:"main_hdr_localtime",BOARD_TITLE:"board_title",LADDER_TITLE:"ladder_title",FXFBTITLE:"fxfbtitle",OP_TITLE:"OP_title",OP_ORDERTYPE:"OP_ordertype",OP_LIMITPRICE:"OP_limitprice",OP_STOPPRICE:"OP_stopprice",OP_TRIGGER:"OP_trigger",OP_TIMEINFORCE:"OP_timeinforce",OP_SECONDS:"OP_seconds",OP_SUBMITORDER_BUTTON:"OP_Submitorder_button",OP_CLOSE_BUTTON:"OP_close_button",OP_INVALID_AMOUNT_ENTERED:"OP_invalid_amount_entered",OP_INVALID_AMOUNT:"OP_invalid_amount",OP_TIMEINFORCE_INVALID:"OP_timeinforce_invalid",OP_ENTER_VALID_PRICE:"OP_enter_valid_price",OP_INVALID_RATES:"OP_invalid_rates",OP_INVALID_AMOUNT:"OP_invalid_amount",OP_BID_GREATERTHAN_BESTOFFER:"OP_bid_greaterthan_bestoffer",OP_STOPBID_MUSTBE_ABOVE_BESTOFFER:"OP_stopbid_mustbe_above_bestoffer",OP_STOPOFFER_MUSTBE_BELOW_BESTBID:"OP_stopoffer_mustbe_below_bestbid",OP_OFFER_LESSTHAN_BESTBID:"OP_offer_lessthan_bestbid",OP_RATEFIELD_SHOULDNOTBEEMPTY:"OP_ratefield_shouldnotbeempty",OP_AMOUNTFILED_SHOULDNOTBEEMPTY:"OP_amountfiled_shouldnotbeempty",OP_INVALIDRATE_MAXPRECISION_EXCEEDED:"OP_invalidrate_maxprecision_exceeded",OP_RATES_NOTAVAILABLE:"OP_rates_notavailable",OP_GREATER_TPPRICE:"OP_tplmtprc_grtrthan_parentorderprc",OP_LESSER_TPPRICE:"OP_tplmtprc_lessthan_parentorderprc",OP_GREATER_SLPRICE:"OP_sltrgrprc_grtrthan_parentorderprc",OP_LESSER_SLPRICE:"OP_sltrgrprc_lessthan_parentorderprc",OP_IFDONE_DIALOG:"OP_dialog_ifdone_fill@mkt_unfilledorder",OP_IFDONE_DIALOGTITLE:"OP_dialogtitle_ifdone_fill@mkt_unfilledorder",OP_IFDONEAMEND_DIALOGTITLE:"OP_dialogtitle_ifdoneamend_validate",OP_IFDONELBL:"OP_ifdonelabel",OP_IFDONETPLBL:"OP_ifdonetplabel",OP_IFDONESLLBL:"OP_ifdonesllabel",OP_IFDONETPTYPE:"OP_ifDonetptype",OP_IFDONETPORDTYPE:"OP_ifDonetpordertype",OP_IFDONESLTYPE:"OP_ifDonesltype",OP_IFDONESLORDTYPE:"OP_ifDoneslordertype",OSP_CANCELLING:"OSP_cancelling",OSP_CLRALL_BTN:"OSP_clrall_btn",OSP_OFFALL_BTN:"OSP_offall_btn",OSROW_OFFBTN:"OSROW_offbtn_value",OB_TITLE:"OB_title",PB_TITLE:"PB_title",TB_TITLE:"TB_title",MSGBLTR_TITLE:"MB_title",OB_STATUS:"OB_status",OB_CCYPAIR:"OB_ccypair",OB_DEALTCURRENCY:"OB_dealtcurrency",OB_FILLAMOUNT:"OB_fillamount",OB_ORDERRATE:"OB_orderrate",OB_FILLRATE:"OB_fillrate",PB_LONGSHORT:"PB_longshort",PB_NETAMOUNT:"PB_netamount",PB_POSITIONRATE:"PB_positionrate",PB_PANDL:"PB_pandl",TB_TENOR:"TB_tenor",TB_FIXINGDATE:"TB_fixingdate",TB_EXECUTIONTIME:"TB_executiontime",TB_BASEAMOUNT:"TB_baseamount",TB_TERMAMOUNT:"TB_termamount",TB_STREAM:"Stream",PLANNER_UI_LABEL:"planner_ui_label",STNGS_TITLE:"stngs_title",STNGS_GENERAL:"stngs_general",STNGS_GENERAL_DISPLAY:"stngs_general_display",STNGS_GENERAL_DATEFORMAT:"stngs_general_dateformat",STNGS_GENERAL_TIMEFORMAT:"stngs_general_timeformat",STNGS_GENERAL_NEGINDICATOR:"stngs_general_negindicator",STNGS_GENERAL_DECSEPERATOR:"stngs_general_decseperator",STNGS_GENERAL_AMOUNTFORMAT:"stngs_general_amountformat",STNGS_GENERAL_PNLCURRENCY:"stngs_general_pnlcurrency",STNGS_GENERAL_REVERTTODEFAULT:"stngs_general_reverttodefault",STNGS_GENERAL_DISABLEAUTOUNIT:"stngs_general_disableautounit",STNGS_GENERAL_OPENONORDERSUBMIT:"stngs_general_openonordersubmit",CONFIRM_OSP_CLOSE:"confirm_osp_close",STNGS_TRADESTNGS_TITLE:"stngs_tradestngs_title",STNGS_TRDNG_TRDNGSTYLE:"stngs_trdng_trdngstyle",STNGS_TRDNG_SINGLECLICK:"stngs_trdng__singleclick",STNGS_TRDNG_DOUBLECLICK:"stngs_trdng_doubleclick",STNGS_TRDNG_CCYPAIRS:"stngs_trdng_ccypairs",STNGS_TRDNG_FBROWS:"stngs_trdng_fbrows",STNGS_TRDNG_TRADESIZES:"stngs_trdng_tradesizes",STNGS_TRDNG_LADDERSIZES:"stngs_trdng_laddersizes",STNGS_TRDNG_MAXORDERSIZE:"stngs_trdng_maxordersize",STNGS_TRDNG_MAXNETSPOTAMT:"stngs_trdng_maxnetspotamt",STNGS_TRDNG_COPYALLCCYSBTN:"stngs_trdng_copyallccysbtn",STNGS_TRDNG_EXECRANGE:"stngs_trdng_execrange",STNGS_TRDNG_EXECRANGE_CCYPAIR:"stngs_trdng_execrange_ccypair",STNGS_TRDNG_EXECRANGE_RANGE:"stngs_trdng_execrange_range",STNGS_TRDNG_EXECRANGE_RANGEINPUT:"stngs_trdng_execrange_rangeinput",STNGS_TRDNG_EXECRANGE_APPLYBUTTON:"stngs_trdng_execrange_applybutton",STNGS_TRDNG_RESTOREDEFAULTSBUTTON:"stngs_trdng_restoredefaultsbutton",STNGS_RFSSTNGS_TITLE:"stngs_rfsstngs_title",STNGS_RFS_DEFAULTTENOROUTRIGHT:"stngs_rfs_defaulttenoroutright",STNGS_RFS_DEFAULTTENOR_SWAP:"stngs_rfs_defaulttenor_swap",STNGS_RFS_EXPIRYTIME:"stngs_rfs_expirytime",STNGS_RFS_SECONDS:"stngs_rfs_seconds",STNGS_RFS_COPYTOALLCCYBTN:"stngs_rfs_copytoallccybtn",STNGS_RFS_RESTOREDEFAULTS:"stngs_rfs_restoredefaults",STNGS_INVALID_ORDERSIZES:"stngs_invalid_ordersizes",STNGS_INVALID_MARKETRANGE:"stngs_invalid_marketrange",STNGS_EXPIRYTIME_VALIDATION:"stngs_expirytime_validation",STNGS_INVALID_SIZES:"stngs_invalid_sizes",STNGS_INVALIDSIZES_EXCEEDMAXLENGTH:"stngs_invalidsizes_exceedmaxlength",STNGS_INVALIDSIZES_DUPLICATECHARACTERS:"stngs_invalidsizes_duplicatecharacters",STNGS_INVALIDSIZES_EMPTYFILED:"stngs_invalidsizes_emptyfiled",STNGS_INVALIDFBROWS_EMPTYFIELD:"stngs_invalidfbrows_emptyfield",STNGS_INVALIDFBROWS_INVALIDNUM:"stngs_invalidfbrows_invalidnum",STNGS_INVALIDFBROWS_OUTOFRANGE:"stngs_invalidfbrows_outofrange",STNGS_INVALID_OUTRIGHTTENOR:"stngs_invalid_outrighttenor",STNGS_DUPLICATE_OUTRIGHTTENOR:"stngs_duplicate_outrighttenor",STNGS_DUPLICATE_SWAPTENOR:"stngs_duplicate_swaptenor",STNGS_OUTRIGHTTENORS_CANTBE_EMPTY:"stngs_outrighttenors_cantbe_empty",STNGS_SWAPTENORS_CANTBE_EMPTY:"stngs_swaptenors_cantbe_empty",STNGS_INVALID_SWAPTENORS:"stngs_invalid_swaptenors",STNGS_SAME_NRANDFAR_SWAPTENORS:"stngs_same_nrandfar_swaptenors",STNGS_TRADESIZES:"stngs_tradesizes",STNGS_LADDERSIZES:"stngs_laddersizes",STNGS_MARKETRANGES:"stngs_marketranges",STNGS_MAXORDERSIZE:"stngs_maxordersize",STNGS_RFSEXPIRY:"stngs_rfsexpiry",STNGS_FULLBOOKROWS:"stngs_fullbookrows",STNGS_OKBUTTON:"stngs_okbutton",STNGS_CANCELBUTTON:"stngs_cancelbutton",STNGS_TRDNG_CONFIRM_TXT:"stngs_trdng_confirm_text",STNGS_TRDNG_CHECK_TEXT:"stngs_trdng_check_text",STNGS_TRDNG_TITLE_TEXT:"stngs_trdng_title_text",OCOOUO_ORD1_LABEL:"ocoouo_ord1_label",OCOOUO_ORD2_LABEL:"ocoouo_ord2_label",OP_STARTTIME:"op_starttime",OP_FULLFILL:"op_fullfill",OP_ABSOLUTE:"op_absolute",OP_PROPORTIONAL:"op_proportional",OP_ATPRICE:"op_atprice",OP_ORDER:"op_order",OP_TOTAL_EXEC_TIME:"op_total_exec_time",OP_NOWORSETHAN:"op_noworsethan",OP_PARAMETERS:"op_parameters",OP_CLIPINTERVAL:"op_clipinterval",OP_CLIPDETAILS:"op_clipdetails",FIXED:"fixed",RANDOM:"random",RANDOMSIZE:"op_randomizesize",RANDOMINT:"op_randomizeint",BETWEEN:"between",AND:"and",OP_CLIPSIZE:"op_clipsize",OP_TOTALCLIP:"op_totalclip",OP_SHOWSIZE:"op_showsize",RANGE:"range",OP_ACTIONEXPIRY:"op_actionexpiry",OP_CANCELBALANCE:"op_cancelbalance",FILL_BAL_AT_MKT:"fill_bal_at_mkt",PEGOFFSET:"pegoffset",PARAMETERS:"parameters",PEGDETAILS:"pegdetails",CLIPDETAILS:"clipdetails",PEGTO:"pegto",OFFSET:"offset",ERR_START_IN_ZERO:"err_start_in_zero",ERR_START_AT_SMALLER:"err_start_at_smaller",ERR_END_AT_SMALLER:"err_end_at_smaller",RFS_AMOUNT:"RFS_amount",RFS_NEARDATE:"RFS_neardate",RFS_NEARAMOUNT:"RFS_nearamount",RFS_FARDATE:"RFS_fardate",RFS_FARAMOUNT:"RFS_faramount",RFS_BUY:"RFS_buy",RFS_SELL:"RFS_sell",RFS_BIDSPOTRATE:"RFS_bidspotrate",RFS_OFFERSPOTRATE:"RFS_offerspotrate",RFS_EXPIRESIN:"RFS_expiresin",RFS_SECONDS:"RFS_seconds",RFS_ADDITIONALINSTRUCTIONS:"RFS_additionalinstructions",RFS_REMEMBERCHECK:"RFS_remembercheck",RFS_OUTRIGHTRATE:"RFS_outrightrate",RFS_OUTRIGHTPOINTS:"RFS_outrightpoints",RFS_NEARPOINTS:"RFS_nearpoints",RFS_FARPOINTS:"RFS_farpoints",RFS_NEARVLUEDATE:"RFS_nearvluedate",RFS_FARVALUEDATE:"RFS_farvaluedate",RFS_FIXINGDATE:"RFS_fixingdate",RFS_SWAPPOINTS:"RFS_swappoints",RFS_ADDITIONALSPREADS:"RFS_additionalspreads",RFS_GETPRICEBUTTON:"RFS_getpricebutton",RFS_CANCELBUTTON:"RFS_cancelbutton",RFS_ACCEPTPRICEBUTTON:"RFS_acceptpricebutton",RFS_SHOWLDRLINK:"RFS_showldrlink",RFS_HIDELDRLINK:"RFS_hideldrlink",RFS_INVALID_DATEFORMAT:"RFS_invalid_dateformat",RFS_INVALID_LEG_DATE:"RFS_invalid_leg_date",RFS_DATE_CANTBELESSTHAN_CURDATE:"RFS_date_cantbelessthan_curdate",RFS_NRDATE_CANTBE_GREATERTHAN_FARDATE:"RFS_nrdate_cantbe_greaterthan_fardate",RFS_NRDATE_CANTBE_SAMEAS_FARDATE:"RFS_nrdate_cantbe_sameas_fardate",TICKERENABLED:"tickerEnabled",FULLBOOKDISPLAYCOUNT:"FullbookDisplayCount",ORDER_CANTBECANCELLED:"order_cantbecancelled",TEST:"test",ORDERAMOUNT_EXCEED_WARNING:"orderamount_exceed_warning",VWAP:2,BEST_PRICE:3,FB_AGGR:0,FB_NON_AGGR:1,ORDER_PLACE_LIMIT:"order.place.Limit",ORDER_PLACE_STOP:"order.place.Stop",ORDER_PLACE_MARKET:"order.place.Market",ORDER_PLACE_STOPLIMIT:"order.place.StopLimit",RFS_SUBSCRIBE_SPOT:"rfs.Spot",RFS_SUBSCRIBE_OUTRIGHT:"rfs.Outright",RFS_SUBSCRIBE_SWAP:"rfs.Swap",RFS_SUBSCRIBE_NDF:"rfs.NDF",RFS_SUBSCRIBE_FWDFWD:"rfs.subscribe.FwdFwd",RFS_ACCEPT_OUTRIGHT:"rfs.accept.Outright",RFS_ACCEPT_SWAP:"rfs.accept.Swap",RFS_ACCEPT_SPOT:"rfs.accept.Spot",RFS_ACCEPT_FWDFWD:"rfs.accept.FwdFwd",RFS_NRTENOR_CANTBE_EMPTY:"RFS_nrtenor_cantbe_empty",RFS_FARTENOR_CANTBE_EMPTY:"RFS_fartenor_cantbe_empty",RFS_FARVALUEDATE_EQUALORBEFORE_NEAR:"RFS_farvaluedate_equalorbefore_near",RFS_NO_NDF_CURRENCY:"RFS_no_ndf_currency",RFS_REQFAILED:"RFS_reqfailed",RFS_1WAY:"RFS_1way",RFS_2WAY:"RFS_2way",CONTACT_SUPPORT_FORLOGIN:"contact_support_forlogin",LEGALAGREEMENT_DECLINED:"legalagreement_declined",BROWSERSUPPORT_SYSTEM_REQS:"browsersupport_system_reqs",MIN_BROWSER_REQS:"min_browser_reqs",BROWSERSUPPORT_TITLE_COMPONENT:"browsersupport_title_component",BROWSERSUPPORT_MIN_REQUIREMENTS:"browsersupport_min_requirements",BROWSERSUPPORT_PREFERED_REQ:"browsersupport_prefered_req",BROWSERSUPPORT_SCRRESOLUTION:"browsersupport_scrresolution",INTERNET_BROWSERS:"internet_browsers",I_E:"i_e",FIREFOX:"firefox",SAFARI:"safari",CHROME:"chrome",INTERNET_CONNECTION:"internet_connection",INTERNET_CON_DESCRIPTION:"internet_con_description",IDC_COPYRIGHT:"idc_copyright",CCY_NOT_SUPPORTED:"ccy_not_supported",CONNECTION_RESUMED:"connection_resumed",ORDER_ID:"order_id",TRADE_ID:"trade_id",BUYSELL:"buysell",SUBMISSION_TIME:"submission_time",ORDER_STATUS:"order_status",DEALT_CCY:"dealt_ccy",TRADER:"trader",LIMIT_RATE:"limit_rate",TYPE:"type",AVG_RATE:"avg_rate",STRATEGY:"strategy",ORDER_AMOUNT:"order_amount",DEALT_AMOUNT:"dealt_amount",MKT_RANGE:"mkt_range",FILLED_AMOUNT:"filled_amount",HIDDEN_DISPLAY:"hidden_display",CONTINGENCY:"contingency",LINKID:"linkid",TIME_IN_FORCE:"time_in_force",EXPIRY:"expiry",TRADE_TICKET:"trade_ticket",TRADE_TYPE:"trade_type",ORG:"org",ACCOUNT:"account",USER:"user",TRADE_DATE:"trade_date",VALUE_DATE:"value_date",EXECUTION_DATE_TIME:"execution_date_time",MAKER_TAKER:"maker_taker",TAKER:"taker",BUYS:"buys",SELLS:"sells",CCY_PAIR:"ccy_pair",SPOT_RATE:"spot_rate",COUNTERPARTY:"counterparty",COUNTERPARTY_ACCOUNT:"counterparty_account",COUNTERPARTY_ID:"counterparty_id",TENOR:"tenor",FORWARD_POINTS:"forward_points",RATE:"rate",NEAR_LEG:"near_leg",FAR_LEG:"far_leg",NEAR_FORWARD_POINTS:"near_forward_points",FAR_FORWARD_POINTS:"far_forward_points",NEAR_RATE:"near_rate",FAR_RATE:"far_rate",SWAP_POINTS:"swap_points",FIXING_DATE:"fixing_date",DT_GENERATED:"DT_generated",TIME_ZONE:"time_zone",DT_PATENTINFO:"DT_patentinfo",PRINT:"print_btn_val",CLOSE:"close_btn_val",COULDNT_RETRIEVE_TRADES:"couldnt_retrieve_trades",USER_NAME:"user_name",PASSWORD:"password",ORGANIZATION:"organization",LOGIN:"login",FORGOT_PASSWORD:"forgot_password",CREATE_NEW_PASSWORD:"create_new_password",ENTER_PASSWORD:"enter_password",NEW_PASSWORD:"new_password",CONFIRM_PASSWORD:"confirm_password",NEW_CONFIRMPASSWD_DONT_MATCH:"new_confirmpasswd_dont_match",NEW_PASSWORD_SENT:"new_password_sent",PLEASE_ENTER_VALID_CREDENTIALS:"please_enter_valid_credentials",SPROCESSING:"sProcessing",SLENGTHMENU:"sLengthMenu",SZERORECORDS:"sZeroRecords",SINFO:"sInfo",SINFOEMPTY:"sInfoEmpty",SINFOFILTERED:"sInfoFiltered",SINFOPOSTFIX:"sInfoPostFix",SEMPTYTABLE:"sEmptyTable",SSEARCH:"sSearch",SFIRST:"sFirst",SLAST:"sLast",SNEXT:"sNext",SINFOTHOUSANDS:"sInfoThousands",SLOADINGRECORDS:"sLoadingRecords",SPREVIOUS:"sPrevious",LOG_ERROR:40000};$.errorMsgs={"Request.Validation.Trade.ValueDate.NotBusinessDate":"Request.Validation.Trade.ValueDate.NotBusinessDate","Request.Validation.Trade.FixingDate.NotBusinessDate":"Request.Validation.Trade.FixingDate.NotBusinessDate","Request.Validation.Trade.FarLeg.ValueDate.NotBusinessDate":"Request.Validation.Trade.FarLeg.ValueDate.NotBusinessDate","Request.Validation.Trade.ValueDate.BeforeTradeDate":"Request.Validation.Trade.ValueDate.BeforeTradeDate","Request.Validation.Trade.FixingDate.BeforeTradeDate":"Request.Validation.Trade.FixingDate.BeforeTradeDate","Request.Validation.Trade.FarLeg.ValueDate.BeforeTradeDate":"Request.Validation.Trade.FarLeg.ValueDate.BeforeTradeDate","Request.Validation.Trade.ValueDate.LaterThanMaxTenor":"Request.Validation.Trade.ValueDate.LaterThanMaxTenor","Request.Validation.Trade.FixingDate.LaterThanMaxTenor":"Request.Validation.Trade.FixingDate.LaterThanMaxTenor","Request.Validation.Trade.FarLeg.ValueDate.LaterThanMaxTenor":"Request.Validation.Trade.FarLeg.ValueDate.LaterThanMaxTenor","Request.Validation.Trade.SettlementDateRule.NotDefined":"Request.Validation.Trade.SettlementDateRule.NotDefined","Request.Validation.Trade.ValueDate.Mismatch":"Request.Validation.Trade.ValueDate.Mismatch","Request.Validation.Tenor/ValueDate.Missing":"Request.Validation.Tenor_ValueDate.Missing","Request.Validation.Tenor/FixingDate.Missing":"Request.Validation.Tenor_FixingDate.Missing","Request.Validation.FarLeg.Tenor/ValueDate.Missing":"Request.Validation.FarLeg.Tenor_ValueDate.Missing","Request.Validation.Trade.No.FixingDate.Or.ValueDate":"Request.Validation.Trade.No.FixingDate.Or.ValueDate",INCORRECT_REQUEST_PARAMS:"INCORRECT_REQUEST_PARAMS","OrderAmount.LessThanMinTradeSize":"OrderAmount.LessThanMinTradeSize","Request.Validation.TradingDisabled":"Request.Validation.TradingDisabled",ERR_OTP_DISABLED:"ERR_OTP_DISABLED",ERR_OTP_NOT_RECEIVED:"ERR_OTP_NOT_RECEIVED",ERR_OTP_INTERNAL_SERVER_ERROR_:"ERR_OTP_INTERNAL_SERVER_ERROR_",ERR_2FA_CREDENTIALS_NOT_RECEIVED:"ERR_2FA_CREDENTIALS_NOT_RECEIVED",ERR_INVALID_OTP_FOR_2FA:"ERR_INVALID_OTP_FOR_2FA",ERR_INVALID_OTP:"ERR_INVALID_OTP",ERR_INVALID_PWD_FOR_2FA:"ERR_INVALID_PWD_FOR_2FA",ERR_INVALID_SESSION:"ERR_INVALID_SESSION",ERR_2FA_DISABLED:"ERR_2FA_DISABLED","ValidationError.Invalid.Password.NewPasswordsNotLongEnough":"ValidationError.Invalid.Password.NewPasswordsNotLongEnough","ValidationError.Invalid.Password.NewPasswordHasBeenUsedBefore":"ValidationError.Invalid.Password.NewPasswordHasBeenUsedBefore","ValidationError.Invalid.Password.NewPasswordsEqualsUsername":"ValidationError.Invalid.Password.NewPasswordsEqualsUsername","Invalid user/organization supplied":"invalid_user_or_org","Invalid password":"invalid_password","ValidationError.Invalid.Password.NewPasswordsContainsThreeConsIdenticalChars":"ValidationError.Invalid.Password.NewPasswordsContainsThreeConsIdenticalChars","ValidationError.Invalid.Password.NewPasswordsContainsDisAllowedPatterns":"ValidationError.Invalid.Password.NewPasswordsContainsDisAllowedPatterns","user.login.invalidIP":"user_login_invalidIP","Idc.Org.Logins.Disabled":"Idc_Org_Logins_Disabled"};return{getLabel:function(labelKey,param1,param2,param3,param4){var value=jQuery.i18n.prop(labelKey,param1,param2,param3,param4);return value;},getProperty:function(key,defaultValue){var value=jQuery.i18n.prop(key);return(value==key)?defaultValue:value;},has2FAPermission:function(){return jQuery.i18n.prop("html2fa")=="true";},getErrorMsg:function(key){return $.errorMsgs[key]==undefined?key:$.errorMsgs[key];}};})(jQuery);(function($){$.fn.createLicenseAgreement=function(loginResponse){var content=$('<p style="text-align: center;font-weight: bold;">User Agreement</p><p style="text-align: center;font-weight: bold; color:#ff0000">IMPORTANT &#150; READ CAREFULLY</p><p><b>PLEASE READ THIS AGREEMENT IN ITS ENTIRETY BEFORE ACCESSING THE SYSTEM AND USING THE SERVICES THEREON.  BY CLICKING ON THE "Click to eSign" BUTTON BELOW, YOU, AND THE ENTITY WHICH YOU REPRESENT ("YOU") AGREE, AS OF THE DATE AND TIME YOU CLICK ON THE  "Click to eSign" BUTTON ("EFFECTIVE DATE") TO BE BOUND BY ALL OF THE TERMS AND CONDITIONS OF THIS AGREEMENT. IF YOU DO NOT AGREE TO ALL THE TERMS AND CONDITIONS OF THIS AGREEMENT, THEN DO NOT CLICK ON THE "Click to eSign" BUTTON AND YOU WILL NOT BE ABLE TO USE THE SERVICES. THIS AGREEMENT REPRESENTS THE ENTIRE AGREEMENT RELATING TO YOUR USE OF THE SYSTEM. </b></p><p>In consideration of the mutual promises herein, the receipt and sufficiency of which is hereby acknowledged by the Parties, the Parties agree as follows:</p><ol type="1"><li><b>Definitions</b><ol type="A"><li>"Credit Facilitator" means an entity that is approved by Integral and provides credit to You for the purpose of facilitating Your transactions on the System.</li><li>"FX" means foreign exchange and/or foreign currency.</li><li>"System" means the online platform containing FX Inside Prime and FX Inside Professional, access to which may enable users to execute FX transactions with counterparties using an Credit Facilitator.</li><li>"Integral Operational Materials" means System related guidelines published by Integral from time to time which are incorporated herein by reference and which may be amended by Integral in its sole discretion.</li></ol></li><li><b>Access Grant.</b> Subject to the terms of this Agreement, Integral grants to You a non exclusive, revocable, non transferable, non-sublicensable and limited license for You to access the System solely for Your own internal business purpose of conducting FX transactions on the System with counterparties using an Credit Facilitator.  In the event of improper activity and/or requirement of a counterparty or Credit Facilitator, Integral may suspend, restrict or place limits on Your ability to trade with any counterparty through the System without notice, in its sole discretion.  Further, any counterparty may, in its sole discretion, refuse to act on any of your requests, instructions or transactions.</li><li><b>Use of the System.</b>You represent and warrant on a continuing basis as long as You are authorized to access and use the System that:<ol type="a"><li>	You shall access and use the System only in conformity with all applicable statutes, rules, and regulations, and the interpretations of any regulatory agency with jurisdiction ("Applicable Law") and Integral Operational Materials.  You are responsible for all instructions or other communications (including bids and offers) made through the System associated with Your identity and/or password, and such instructions shall be binding on You. You transmit instructions to counterparties through the System at your own risk. </li><li>	 You have in place all security, systems and compliance procedures required to prevent violation of Applicable Law and unauthorized access, use or misuse of the System.  You will not alter, delete, disable or otherwise circumvent any security device. You will notify Integral immediately if you become aware of any unauthorized access to or use of the System.  </li><li> You will provide any counterparty with any information or documentation such counterparty requires in order to complete transactions with You through FX Inside Prime.  </li><li>You will not attempt to access information or applications that You have not been authorized to use by Integral, and, if You inadvertently gain such access, You agree not to use or disseminate, reproduce, redistribute or decompile any such information or applications.</li><li>You shall not use or access the System for any reason other than to trade with counterparties through the System.  Without limiting the foregoing, You shall not transmit, publish or otherwise disseminate any prices or other content of the System.</li><li>You will not use the System or any feature of the System to post or transmit inappropriate information.</li></ol></li><li><b>Representations.</b><br />Each party represents and warrants to the other that it has the right and full corporate power to enter into this Agreement and that this Agreement creates legal, valid and binding obligations on it which are enforceable against it in accordance with its terms.  Further, Integral represents that the System does not infringe on any patent, trademark, or copyright of a third party.</li><li><b>Reservation of Rights.</b> Integral has exclusive ownership of and rights to FX Inside Prime and the System, its use and the content of the System, as well as all related copyrights, trademarks, service marks, patent rights, and trade secrets and any other intellectual property rights therein (registered or unregistered) including any applications, anywhere in the world.  You will not (i) sell, lease, transfer, make derivative works from, reproduce, redistribute or otherwise disseminate all or any part of FX Inside Prime, the System, its components or its content, or (ii) copy, alter, decompile or reverse engineer FX Inside Prime, the System or any of its components.  Further, You will not remove, obscure or change any copyright or other notices or legends contained in FX Inside Prime, the System or any of its components.</li><li><b>DISCLAIMER OF WARRANTY AND LIMITATION OF LIABILITY. </b>FX Inside Prime AND THE SYSTEM AND ALL CONTENT ARE PROVIDED "AS IS." NEITHER INTEGRAL NOR ANY CREDIT FACILITATOR MAKES ANY REPRESENTATION OR WARRANTY, EXPRESS OR IMPLIED.  INTEGRAL DISCLAIMS ANY OBLIGATION TO KEEP FX Inside Prime AND/OR THE SYSTEM SECURE OR FREE OF ERRORS OR VIRUSES OR TO MAINTAIN UNINTERRUPTED ACCESS. INTEGRAL MAY STOP PRODUCING OR UPDATING ALL OR ANY PART OF FX Inside Prime AND/OR THE SYSTEM.  INTEGRAL SHALL NOT HAVE ANY LIABILITY TO YOU OR ANY THIRD PARTY FOR ANY LOSS OF PROFITS, LOSS OF DATA, INDIRECT, SPECIAL OR CONSEQUENTIAL LOSS OR OTHER DAMAGE OR LIABILITY ARISING OUT OF, OR IN CONNECTION WITH, THE PROVISION OR USE OF (OR ANY INABILITY TO USE) FX Inside Prime OR THE SYSTEM, WHETHER IN CONTRACT, TORT (INCLUDING NEGLIGENCE), STATUTE OR OTHERWISE, EVEN IF INTEGRAL HAS BEEN ADVISED OF THE POSSIBILITY THEREOF.  EXCEPT WITH RESPECT TO MISUSE OF INTEGRAL\'S INTELLECTUAL PROPERTY, INDEMNIFICATION LIABILITY OR BREACH OF CONFIDENTIALITY BY CUSTOMER, THE  TOTAL MAXIMUM  LIABILITY FOR  ANY LOSS OR DAMAGES HOWSOEVER CAUSED  AND IN RELATION TO ANY CLAIM OR SERIES OF CLAIMS RELATING TO THIS AGREEMENT SHALL BE LIMITED TO THE FEES PAID DURING THE 12 MONTHS IMMEDIATELY PRECEDING THE CLAIM.</li><li><b>Non-Infringement and Indemnification.</b>  Integral shall defend You against claims that the System, when used as permitted by this Agreement, infringes any patent or copyright, or other registered intellectual property right of any third party, provided that you promptly notify Integral of such claim, allow Integral to have sole control of the defense and settlement thereof and fully cooperate with Integral in such defense at your own expense. No other indemnity of any kind is provided by Integral with respect to any other matter. You shall indemnify, defend and hold harmless Integral from any and all claims, liabilities, damages, costs and expenses (including attorneys\' fees) arising from Your intellectual property infringement and/or breach of this Agreement and/or Your use of the System and/or any part thereof including, without limitation, trading losses, the accuracy or completeness of any quote, failure to deliver or complete a transaction and any failure to comply with any Applicable Law or regulation.</li><li><b>Confidentiality and Use of Data.</b>All (oral or written) business, technical, financial and other information provided by a party ("disclosing party") to the other party ("receiving party") under this Agreement shall be deemed "Confidential Information". The confidentiality obligations set forth herein shall not apply to information disclosed by the disclosing party that the receiving party can prove by admissible evidence (i) is or has become generally publicly known through no fault of the receiving party, (ii) was in its possession or known by it, without restriction, prior to receipt from the disclosing party, (iii) was rightfully disclosed to it by a third party without restriction, (iv) was independently developed without use of or access to any Confidential Information of the disclosing party, or (v) was required to be disclosed by court order, provided that the receiving party has promptly notified the disclosing party about such requirement, has attempted to limit such disclosure and to obtain confidential treatment or a protective order, and has allowed the disclosing party to participate in any such undertakings and proceedings.  All Confidential Information shall be held in confidence by the receiving party and not disclosed or used by the receiving party except as permitted by this Agreement or as expressly authorized in writing by the other party.  Each party shall use at least the same degree of care to protect the disclosing party\'s Confidential Information as it uses to protect its own confidential information of like nature, but in no circumstances less than reasonable care.  Notwithstanding the foregoing, You acknowledge and agree that any content transmitted through or with the assistance of FX Inside Prime and/or the System may be used by Integral so long as personal identifiers of individuals are removed from such content.  Also, Integral may publicly announce and use in its marketing materials the existence (but not the terms) of this Agreement and the Your role with respect to the System.   Further, Integral shall not be liable for the privacy of e-mail addresses, registration and identification information, communications or any other content stored on Integral\'s equipment, transmitted over networks accessed by the System, or otherwise connected with Your use of FX Inside Prime or the System.  Integral shall not be liable for the loss, corruption of, or incompleteness, of data, content, or any other information provided to Integral or downloaded to or from the System by You.</li><li><b>Data Protection.  </b> If any personal data (including sensitive personal data) belonging to Your users or Your customers, or other individuals, is provided to Integral by or through You, You represent and warrant that such persons are aware of and explicitly consent to the use of such personal data by Integral and You agree to indemnify Integral and any counterparty receiving such information against any loss or damage either may incur arising out of a breach by You of this representation and warranty; and You further acknowledge and agree that Integral may monitor Your use of the System and Your use of e-mail and/or instant messaging in connection with the System and may monitor and tape record telephone conversations with You concerning the System.</li><li><b>Term and Termination.</b><ol type="A"><li>Term:   This Agreement shall commence on the Effective Date and shall continue in effect for one year  ("the Initial Term") unless terminated sooner in accordance with the terms herein.  Thereafter, the Agreement shall automatically renew for one or more further one year consecutive terms ("Renewal Term") unless terminated by either party giving written notice not less than sixty (60) days ("Notice Period") prior to the end of the Initial or Renewal Term.</li><li>Termination:  This Agreement may be terminated for cause immediately by a party in the event that the other party:  1) materially breaches this Agreement (e.g. failure to pay) and such breach remains uncured for thirty (30) days after receiving written notice, or 2) is the subject of a voluntary petition in bankruptcy or any voluntary proceeding relating to insolvency, receivership, liquidation, or composition for the benefit of creditors (each a "Bankruptcy").  Upon termination of this Agreement (i) Your right to access and/or use FX Inside Prime and the System as described herein shall cease; (ii) You shall cease to use all copies of any Integral product, software or documentation; and (iii) You shall return to Integral all copies of said material in Your possession, custody or control.</li></ol></li><li><b>Arbitration. </b> The Parties agree that any dispute or controversy arising out of or relating to any interpretation, construction, performance or breach of this Agreement or arising out of or relating to this Agreement shall be settled by arbitration in San Francisco, California in accordance with the rules then in effect of the American Arbitration Association. The decision of the arbitrator shall be final, conclusive and binding on the parties to the arbitration and shall include an award of costs and fee including, without limitation, attorneys fees and costs to the prevailing party.  Judgment, including costs and fees, may be entered on nothing more than a copy of the arbitrator\'s decision in any court having jurisdiction over the party. Notwithstanding the foregoing, it is hereby understood and agreed that damages may be an inadequate remedy in the event of a breach by either party of any of said covenants, including without limitation those with respect to confidentiality, and that any such breach will cause great and irreparable injury and damage, and thus accordingly, each party agrees that the other party shall be entitled, without waiving any additional rights or remedies otherwise available at law or in equity or by statute, and without need of posting a bond, to injunctive and other equitable relief from a court of competent jurisdiction in the event of a breach or intended or threatened breach by the other of any of said covenants.</li><li><b>Miscellaneous. </b> Integral shall not have any liability for any failure to perform or delay in performing its obligations under this Agreement due to any Act of God, act of governmental authority, change in law or regulation, war, criminal act, fire, explosion, earthquake, flood, weather condition, power failure, transportation or other accident beyond its reasonable control. This Agreement along with the Dealing Rules constitutes the entire agreement between the parties for access to FX Inside Prime and the System and supersedes all proposals, negotiations and discussions, oral or written, relating to access to and use of FX Inside Prime and the System. Neither Party may assign this Agreement without the express written consent of the other party which consent shall not be unreasonably withheld or delayed.  The failure of either Party to exercise in any respect any right provided for herein shall not be deemed a waiver of any further rights hereunder. If any provision of this Agreement shall be adjudged by any court of competent jurisdiction to be unenforceable or invalid, that provision shall be limited or eliminated to the minimum extent necessary so that this Agreement shall otherwise remain in full force and effect and enforceable. Each and every notice and consent required or otherwise given or made under this Agreement shall be in writing, and shall be deemed given or made when personally delivered, when sent by confirmed fax, or three days after being sent by prepaid certified or registered mail to the last known address or fax number of the party.   The headings and captions used in this Agreement are used for convenience only and are not to be considered in construing or interpreting this Agreement. This Agreement shall be governed by and construed in accordance with the laws of the state of California, without regard to California\'s conflict of laws or principles. Any suit brought under this Agreement shall be brought in the state or federal court sitting in San Francisco, California. Neither party shall be deemed to be an employee, agent, partner or legal representative of the other for any purpose, and neither shall have any right, power, or authority to create any obligation or responsibility on behalf of the other. The disclaimers and limitations of liabilities made by Integral in sections 7 and 13 are also made for the benefit of Your counterparties on the System and You acknowledge and agree that such counterparties are intended third party beneficiaries of these provisions and shall have the ability to enforce their rights accordingly as if it were a party to this Agreement in place of Integral.  EACH PARTY RECOGNIZES AND AGREES THAT THE WARRANTY AND LIABILITY DISCLAIMERS AND REMEDY LIMITATIONS IN THIS AGREEMENT ARE A MATERIAL BARGAINED FOR BASIS OF THIS AGREEMENT AND THAT THEY HAVE BEEN TAKEN INTO ACCOUNT AND REFLECTED IN DETERMINING THE CONSIDERATION TO BE GIVEN BY EACH PARTY UNDER THIS AGREEMENT AND IN THE DECISION BY EACH PARTY TO ENTER INTO THIS AGREEMENT. </li></ol><p><b>By clicking the "Click to eSign" button you acknowledge that you have read, understand and agree to be bound by the terms above.  Further, the person clicking the "Click to eSign" button represents that he/she is an authorized representative with the power and authority to accept and bind the entity entering into this Agreement to the terms of this Agreement.</b></p>');var container=$('<div class="agreementContainer"><div class="agreementLogo"></div><div id="lacontent" class="normal"></div><div class="agreementAction"><input id="read" type="checkbox" class="agreementCheck" />I have read the above<br /><div class="agreement"><button class="laagree" id="accept">I Agree</button><button class="ladecline" id="decline">I Decline</button><button class="laprint" id="print">Print</button></div></div></div>');this.append(container);var la=integral.getLegalAgreement();container.find("#lacontent").html(la.legalAgreement);container.jqm({modal:true,overlay:0});container.jqmShow();container.draggable({cancel:"div[id^=lacontent]"});container.find("#accept").attr("disabled","disabled");container.find("#read").click(function(){if($(this).is(":checked")){container.find("#accept").prop("disabled",false);}else{container.find("#accept").prop("disabled",true);}});container.find("#accept").click(function(){integral.acceptLegalAgreement(function(){integral.log("acceptLegalAgreement:Loading main panel.");loadMainPanel(loginResponse);});container.jqmHide();container.remove();});container.find("#decline").click(function(){integral.log("License agreement declined. Logging out.");integral.logout(undefined,"License agreement declined");container.jqmHide();container.remove();$("#fxlogin").find("#logincontainer").remove();$("#fxlogin").createFxLogin($.props.redirectUrl,$.props.brandedPath,true);$("#errormessagelogin").html(integral.common.getLabel($.consts.LEGALAGREEMENT_DECLINED));});container.find("#print").click(function(){if(integral.ui.isBrowserIE()){$("#lacontent").printElement({printMode:"popup",leaveOpen:false});}else{$("#lacontent").printElement();}});};})(jQuery);(function($){$.fn.showSupportedBrowsers=function(){var content=$('<div class="requirementsbox"><div id="logincontainer"><div class="reqheading">'+integral.common.getLabel($.consts.BROWSERSUPPORT_SYSTEM_REQS)+'</div><div style="reqtable"><div class="reqdescription">'+integral.common.getLabel($.consts.MIN_BROWSER_REQS)+'</div><table class="reqtable"><thead><tr><th title="'+integral.common.getLabel($.consts.BROWSERSUPPORT_TITLE_COMPONENT)+'" class="thbg">'+integral.common.getLabel($.consts.BROWSERSUPPORT_TITLE_COMPONENT)+'</th><th title="'+integral.common.getLabel($.consts.BROWSERSUPPORT_MIN_REQUIREMENTS)+'" class="thbg">'+integral.common.getLabel($.consts.BROWSERSUPPORT_MIN_REQUIREMENTS)+'</th><th title="'+integral.common.getLabel($.consts.BROWSERSUPPORT_PREFERED_REQ)+'" class="thbg">'+integral.common.getLabel($.consts.BROWSERSUPPORT_PREFERED_REQ)+'</th></tr></thead><tbody><tr class="reqtrodd"><td class="reqcomponent">'+integral.common.getLabel($.consts.BROWSERSUPPORT_SCRRESOLUTION)+'</td><td>1600 x 1200</td><td>1600 x 1200+</td></tr><tr><td class="reqcomponent">'+integral.common.getLabel($.consts.INTERNET_BROWSERS)+'</td><td><ul style="margin-left:15px"><li style="margin-left:0px">'+integral.common.getLabel($.consts.I_E)+" 7</li><li>"+integral.common.getLabel($.consts.FIREFOX)+" 4</li><li>"+integral.common.getLabel($.consts.SAFARI)+" 4</li><li>"+integral.common.getLabel($.consts.CHROME)+'</li></ul></td><td><ul style="margin-left:15px"><li>'+integral.common.getLabel($.consts.I_E)+" 9</li><li>"+integral.common.getLabel($.consts.FIREFOX)+" 9.0</li><li>"+integral.common.getLabel($.consts.SAFARI)+" 5</li><li>"+integral.common.getLabel($.consts.CHROME)+' 16</li></ul></td></tr><tr class="reqtrodd"><td class="reqcomponent">'+integral.common.getLabel($.consts.INTERNET_CONNECTION)+'</td><td colspan="2">'+integral.common.getLabel($.consts.INTERNET_CON_DESCRIPTION)+'</td></tr></tbody></table></div><div id="logincopyright">'+integral.common.getLabel($.consts.IDC_COPYRIGHT)+"</div></div></div>");this.append(content);};})(jQuery);