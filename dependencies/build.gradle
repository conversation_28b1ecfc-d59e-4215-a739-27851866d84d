def repoPath = "$repoSrc"
def runtimePath = "$repoSrc/../runtime/usrv/conf"

description = "this is dependencies project"

version = "1.0.0"

sourceSets {
    main{
       java {
         srcDirs 'src'
       }
	}
}

dependencies {
        compile project (":IntegralConfigWeb")
        compile project (":notification")
        compile project (":RiskManagement")
        compile project (":FXAdminWeb")
        compile project (":unity")
        compile project (":fxiapi")
        compile project (":StartupWeb")
        compile project (":ISClientWeb")
        compile project (":spacesmgmt")
        compile project (":devapp")


        //These are runtime libs
         runtime "de.undercouch:bson4jackson:2.4.0"
         runtime "com.ibm:dhbcore:7.0"

         runtime "org.apache.zookeeper:zookeeper:3.4.6"
         runtime "org.apache.curator:curator-client:2.5.0"
         runtime "cglib:cglib-nodep:2.2"
         runtime "org.jdom:jdom:1.1.3"
         runtime "org.jdom:jdom2:2.0.5"
         runtime "org.codehaus.jettison:jettison:1.2"
         runtime "net.sf.kxml:kxml2:2.3.0"
         runtime "stax:stax:1.2.0"
         runtime "org.codehaus.woodstox:wstx-asl:3.2.7"
         runtime "xom:xom:1.1"
         runtime "com.thoughtworks.xstream:xstream-hibernate:1.4.5"
         runtime "org.apache.maven:maven-artifact:3.0.3"

         runtime "org.apache.curator:curator-framework:2.5.0"
         runtime "org.apache.curator:curator-recipes:2.5.0"
         runtime "org.apache.curator:curator-x-discovery:2.5.0"
	 runtime "org.javassist:javassist:3.18.1-GA"
         runtime "org.hibernate:hibernate-validator:4.1.0.Final"
         runtime "org.hibernate:hibernate-annotations:3.0.0"
         runtime "org.apache.velocity:velocity:1.5"
         runtime "xerces:xml-apis:1.0.0"
         runtime "org.hsqldb:hsqldb:1.0.0"
         runtime "commons-digester:commons-digester:1.0.0"
        // runtime "org.springframework:spring-asm:3.1.1.RELEASE"
        // runtime "org.springframework:spring-expression:3.1.1.RELEASE"
        // runtime "org.springframework.security:spring-security-config:3.1.2.RELEASE"
        // runtime "org.springframework.security:spring-security-core:3.1.2.RELEASE"
        // runtime "org.springframework.security:spring-security-web:3.1.2.RELEASE"

         runtime "org.bouncycastle:bcprov-jdk15:1.46"
         runtime "org.owasp.esapi:esapi:2.0.1"
         runtime "org.apache.santuario:xmlsec:1.4.5"

         runtime "commons-cli:commons-cli:1.2"
        
         runtime "org.eclipse.jetty.aggregate:jetty-all-server:8.1.15.v20140411"
         runtime "xalan:xalan:1.0.0"
         runtime "com.sonicmq:sonic_crypto:7.6.2"
         runtime "org.restlet.jee:org.restlet.ext.servlet:1.0.0"
         runtime "com.integral:connector:1.0.0"

}

//create a single Jar with all dependencies
task fatJar(type: Jar) {
     zip64=true
     baseName = 'usrv-dependencies'
     destinationDir = file("$repoPath")

     from {
          configurations.runtime.filter( {!(it.name =~/ISDomain.*\.jar/||it.name=~/RiskManagement\.jar/||it.name=~/Multicast\.jar/||it.name=~/jmsproxy\.jar/||it.name=~/FXAdminWebServices\.jar/||it.name=~/monitorserver\.jar/||it.name=~/notification\.jar/||it.name=~/rds\.jar/||it.name=~/unity\.jar/||it.name=~/DirectFX\.jar/||it.name=~/StartupWeb.*\.jar/||it.name=~/devapp.*\.jar/||it.name=~/spacesmgmt.*\.jar/||it.name=~/ISClientWeb.*\.jar/||it.name=~/spring.*\.jar/)}).collect {
             it.isDirectory() ? it : zipTree(it)
         }
     } {
      exclude "META-INF/*.DSA", "META-INF/*.RSA", "META-INF/*.SF"
      exclude "com/integral/"
      exclude "**/pom.xml"
     }
     with jar
     manifest {
          attributes 'Implementation-Title': 'Integral dependencies',
                     'Implementation-Version': version,
                     'Implementation-Description': project.description,
                     'Built-By': System.getProperty('user.name'),
                     'Built-JDK': System.getProperty('java.version'),
                     'Built-Hostname': hostname(),
                     'Build-Time': buildTime()
     }

}


clean {
     delete "build"
     delete "../usrv-dependencies-1.0.0.jar"
}

task compileTests{
}
