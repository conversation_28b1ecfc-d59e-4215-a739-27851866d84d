<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>notification</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.jdt.core.javabuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.buildship.core.gradleprojectbuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>org.eclipse.jdt.core.javanature</nature>
		<nature>org.eclipse.buildship.core.gradleprojectnature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>conf</name>
			<type>2</type>
			<location>/Users/<USER>/svnviews/mainfxi/runtime/usrv/conf</location>
		</link>
		<link>
			<name>properties</name>
			<type>2</type>
			<location>/Users/<USER>/svnviews/mainfxi/integral5/properties</location>
		</link>
	</linkedResources>
	<filteredResources>
		<filter>
			<id>1746845772331</id>
			<name></name>
			<type>30</type>
			<matcher>
				<id>org.eclipse.core.resources.regexFilterMatcher</id>
				<arguments>node_modules|\.git|__CREATED_BY_JAVA_LANGUAGE_SERVER__</arguments>
			</matcher>
		</filter>
	</filteredResources>
</projectDescription>
