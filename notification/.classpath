<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" output="bin/main" path="src">
		<attributes>
			<attribute name="gradle_scope" value="main"/>
			<attribute name="gradle_used_by_scope" value="main,test,unitTest"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="var" path="INTEGRAL5HOME/IntegralCoreDomain.jar" sourcepath="/INTEGRAL5HOME/IntegralCoreDomain/Source"/>
	<classpathentry kind="var" path="INTEGRAL_LIB/apache/commons/commons-logging.jar"/>
	<classpathentry kind="var" path="INTEGRAL5HOME/IntegralCoreServices.jar" sourcepath="/INTEGRAL5HOME/IntegralCoreServices/Java Source"/>
	<classpathentry kind="var" path="INTEGRAL_LIB/SonicSoftware/sonicMQ762/sonic_Client.jar"/>
	<classpathentry combineaccessrules="false" kind="src" path="/ISDomain"/>
	<classpathentry kind="var" path="INTEGRAL5HOME/spaces.jar" sourcepath="/INTEGRAL5HOME/spaces-source.jar"/>
	<classpathentry kind="var" path="INTEGRAL_LIB/TOPLink.jar"/>
	<classpathentry kind="var" path="INTEGRAL_LIB/logback/logback-classic-1.0.6.jar" sourcepath="/logbackclssrc"/>
	<classpathentry kind="var" path="INTEGRAL_LIB/logback/logback-core-1.0.6.jar" sourcepath="/logbacksrc"/>
	<classpathentry kind="var" path="INTEGRAL_LIB/IFSThirdParty.jar"/>
	<classpathentry kind="var" path="INTEGRAL_LIB/IBM/MQSeries6.0/com.ibm.mq.jar"/>
	<classpathentry kind="var" path="INTEGRAL_LIB/IBM/MQSeries6.0/com.ibm.mq.pcf.jar"/>
	<classpathentry kind="var" path="INTEGRAL_LIB/IBM/MQSeries6.0/com.ibm.mqjms.jar"/>
	<classpathentry kind="var" path="INTEGRAL_LIB/guava/guava-15.0.jar"/>
	<classpathentry kind="var" path="INTEGRAL_LIB/jboss/server/usrv/lib/javax.servlet.jar"/>
	<classpathentry kind="var" path="INTEGRAL_LIB/jboss/server/usrv/lib/javax.servlet.jsp.jar"/>
	<classpathentry kind="var" path="INTEGRAL_LIB/apache/commons/commons-lang-2.2.jar"/>
	<classpathentry kind="src" output="bin/unitTest" path="unit">
		<attributes>
			<attribute name="gradle_scope" value="unitTest"/>
			<attribute name="gradle_used_by_scope" value="unitTest"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="bin/unitTest" path="conf">
		<attributes>
			<attribute name="gradle_scope" value="unitTest"/>
			<attribute name="gradle_used_by_scope" value="unitTest"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="bin/unitTest" path="properties">
		<attributes>
			<attribute name="gradle_scope" value="unitTest"/>
			<attribute name="gradle_used_by_scope" value="unitTest"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-1.8/"/>
	<classpathentry kind="con" path="org.eclipse.buildship.core.gradleclasspathcontainer"/>
	<classpathentry kind="output" path="bin/default"/>
</classpath>
