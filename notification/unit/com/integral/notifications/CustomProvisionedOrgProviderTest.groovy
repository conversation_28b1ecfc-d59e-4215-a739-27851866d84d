package com.integral.notifications

import com.integral.persistence.cache.ReferenceDataCacheC

/**
 * Created by raghunathans on 5/22/17.
 */
class CustomProvisionedOrgProviderTest extends GroovyTestCase {

    CustomProvisionedOrgProviderC provider = new CustomProvisionedOrgProviderC();

    public void testUpdateShouldSetEmptyOrgIfRetrievalFromRDSFails() {

        ReferenceDataCacheC referenceDataCacheC = ReferenceDataCacheC.getInstance();

        assertNull(referenceDataCacheC.getProvisionedOrgs());

        //simulating failure due to RDS
        provider.updateProvisionedOrgs();

        assertNotNull(referenceDataCacheC.getProvisionedOrgs());

        assertEquals(0,referenceDataCacheC.getProvisionedOrgs().size());
    }

}
