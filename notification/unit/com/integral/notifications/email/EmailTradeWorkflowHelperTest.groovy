package com.integral.notifications.email

import com.integral.testframework.api.API

class EmailTradeWorkflowHelperTest extends GroovyTestCase{
    @Override
    protected void setUp() throws Exception {
        super.setUp()
        API.setupInMemoryTestInfrastructure()
    }

    public void test(){
        def helper = new EmailTradeWorkflowHelper(null)
        def BTC = API.getCcy "BTC"
        BTC.setTickValue(0.00000001D)
        assert BTC.getTickValue() == 0.00000001D
        assert helper.formatAmount("BTC",0.00000002D) == "0.00000002"
        assert helper.formatAmount("BTC",0.1209D) == "0.1209"
        assert helper.formatAmount("BTC",20.10011D) == "20.10011"
        assert helper.formatAmount("BTC",20.0D) == "20"
        assert helper.formatAmount("BTC",1234567890.0D) == "1,234,567,890"
    }
}
