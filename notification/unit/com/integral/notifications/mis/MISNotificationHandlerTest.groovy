package com.integral.notifications.mis

import com.integral.finance.dealing.RequestC
import com.integral.finance.fx.FXSingleLeg
import com.integral.finance.fx.FXSingleLegC
import com.integral.finance.trade.facade.TradeStateFacade
import com.integral.finance.trade.facade.TradeStateFacadeC
import com.integral.is.common.ApplicationEventCodes
import com.integral.model.dealing.MatchEvent
import com.integral.model.dealing.SingleLegOrder
import com.integral.model.dealing.SingleLegTrade
import com.integral.spaces.notification.NHResponse
import com.integral.spaces.notification.NHResponseCode
import com.integral.spaces.notification.Notification
import com.integral.testframework.api.API

import static com.integral.is.common.ApplicationEventCodes.*
import static org.mockito.Mockito.*
import static org.mockito.Mockito.doThrow;

/**
 * Created by verma on 4/10/14.
 */
class MISNotificationHandlerTest extends GroovyTestCase {

    @Override
    protected void setUp() throws Exception {
        API.setupInMemoryTestInfrastructure()
        super.setUp()
    }

    public void testHandleRedelivery_OrderNotification() {
        def handler = new MISNotificationHandler()
        handler.initialize()
        handler = spy(handler)
        doReturn(new NHResponse(NHResponseCode.SUCCESS)).when(handler).processRedeliveredOrderNotification((any(Notification.class)))

        def notification = new Notification<SingleLegOrder>()
        notification.dataObjectType = SingleLegOrder.class
        def nhResponse = handler.handleRedelivery(notification,null)

        assert nhResponse != null
        assert nhResponse.responseCode == NHResponseCode.SUCCESS
    }

    public void testHandleRedelivery_TradeNotification() {
        def handler = new MISNotificationHandler()
        handler.initialize()
        handler = spy(handler)
        doReturn(new NHResponse(NHResponseCode.SUCCESS)).when(handler).processRedeliveredTradeNotification((any(Notification.class)))

        def notification = new Notification<SingleLegTrade>()
        notification.dataObjectType = SingleLegTrade.class
        def nhResponse = handler.handleRedelivery(notification,null)

        assert nhResponse != null
        assert nhResponse.responseCode == NHResponseCode.SUCCESS
    }

    public void testHandleRedelivery_Exception() {
        def handler = new MISNotificationHandler()
        handler.initialize()
        handler = spy(handler)
        doReturn(new NHResponse(NHResponseCode.SUCCESS)).when(handler).processRedeliveredTradeNotification((any(Notification.class)))
        doReturn(new NHResponse(NHResponseCode.SUCCESS)).when(handler).processRedeliveredTradeNotification((any(Notification.class)))

        def notification = new Notification<SingleLegOrder>()
        notification.dataObjectType = null
        def nhResponse = handler.handleRedelivery(notification,null)

        assert nhResponse != null
        assert nhResponse.responseCode == NHResponseCode.FAILURE
    }

    public void testHandleRedelivery_UnsupportedObject() {
        def handler = new MISNotificationHandler()
        handler = spy(handler)
        doReturn(new NHResponse(NHResponseCode.SUCCESS)).when(handler).processRedeliveredTradeNotification(any(Notification.class))
        doReturn(new NHResponse(NHResponseCode.SUCCESS)).when(handler).processRedeliveredTradeNotification(any(Notification.class))

        def notification = new Notification<MatchEvent>()
        notification.dataObjectType = MatchEvent.class
        def nhResponse = handler.handleRedelivery(notification,null)

        assert nhResponse != null
        assert nhResponse.responseCode == NHResponseCode.FAILURE
    }

    public void testProcessRedeliveredOrderNotification_InsertEvent_RequestNotFound() {
        def handler = new MISNotificationHandler()
        handler = spy(handler)
        doReturn(null).when(handler).queryRequest(any(MISPerformanceMetrics.class), anyString(), anyString())
        doReturn(new NHResponse(NHResponseCode.SUCCESS)).when(handler).handle(any(Notification.class))
        doThrow(new RuntimeException()).when(handler).dropRedeliveredNotification(any(Notification.class))

        def notification = new Notification<SingleLegOrder>()
        notification.namespaceName = "NSX"
        notification.appEventCode = EVENT_ESP_ORDER_SUBMIT.code

        def order = new SingleLegOrder()
        order._id = "OID12345"

        notification.completeObject = order
        def nhResponse = handler.processRedeliveredOrderNotification(notification)
        assert nhResponse != null
        assert nhResponse.responseCode == NHResponseCode.SUCCESS
    }

    public void testProcessRedeliveredOrderNotification_InsertEvent_RequestFound() {
        def handler = new MISNotificationHandler()
        handler = spy(handler)
        doReturn(new RequestC()).when(handler).queryRequest(any(MISPerformanceMetrics.class), anyString(), anyString())
        doReturn(new NHResponse(NHResponseCode.SUCCESS)).when(handler).dropRedeliveredNotification(any(Notification.class))
        doThrow(new RuntimeException()).when(handler).handle(any(Notification.class))

        def notification = new Notification<SingleLegOrder>()
        notification.namespaceName = "NSX"
        notification.appEventCode = EVENT_ESP_ORDER_SUBMIT.code

        def order = new SingleLegOrder()
        order._id = "OID12345"

        notification.completeObject = order
        def nhResponse = handler.processRedeliveredOrderNotification(notification)
        assert nhResponse != null
        assert nhResponse.responseCode == NHResponseCode.SUCCESS
    }

    public void testProcessRedeliveredOrderNotification_UpdateEvent() {
        def handler = new MISNotificationHandler()
        handler = spy(handler)

        doThrow(new RuntimeException()).when(handler).dropRedeliveredNotification(any(Notification.class))
        doThrow(new RuntimeException()).when(handler).queryRequest(any(MISPerformanceMetrics.class), anyString(), anyString())
        doReturn(new NHResponse(NHResponseCode.SUCCESS)).when(handler).handle(any(Notification.class))

        def notification = new Notification<SingleLegOrder>()
        notification.namespaceName = "NSX"
        notification.appEventCode = EVENT_ESP_ORDER_EXPIRE.code

        def nhResponse = handler.processRedeliveredOrderNotification(notification)
        assert nhResponse != null
        assert nhResponse.responseCode == NHResponseCode.SUCCESS
    }

    public void testProcessRedeliveredTradeNotification_InsertEvent_TradeNotFound() {
        def handler = new MISNotificationHandler()
        handler = spy(handler)
        doReturn(null).when(handler).queryTrade(any(MISPerformanceMetrics.class), anyString(), anyString())
        doReturn(new NHResponse(NHResponseCode.SUCCESS)).when(handler).handle(any(Notification.class))
        doThrow(new RuntimeException()).when(handler).dropRedeliveredNotification(any(Notification.class))
        doThrow(new RuntimeException()).when(handler).isRedeliveredNotificationProcessingRequired(any(ApplicationEventCodes.class), any(FXSingleLeg.class))
        def notification = new Notification<SingleLegTrade>()
        notification.namespaceName = "NSX"
        notification.appEventCode = EVENT_ESP_TRADE_ACCEPT.code

        def trade = new SingleLegTrade()
        trade._id = "TID12345"

        notification.completeObject = trade
        def nhResponse = handler.processRedeliveredTradeNotification(notification)
        assert nhResponse != null
        assert nhResponse.responseCode == NHResponseCode.SUCCESS
    }

    public void testProcessRedeliveredTradeNotification_InsertEvent_TradeFound() {
        def handler = new MISNotificationHandler()
        handler = spy(handler)
        doReturn(new FXSingleLegC()).when(handler).queryTrade(any(MISPerformanceMetrics.class), anyString(), anyString())
        doThrow(new RuntimeException()).when(handler).handle(any(Notification.class))
        doThrow(new RuntimeException()).when(handler).dropRedeliveredNotification(any(Notification.class))
        doThrow(new RuntimeException()).when(handler).isRedeliveredNotificationProcessingRequired(any(ApplicationEventCodes.class), any(FXSingleLeg.class))

        def notification = new Notification<SingleLegTrade>()
        notification.namespaceName = "NSX"
        notification.appEventCode = EVENT_ESP_TRADE_ACCEPT.code

        def trade = new SingleLegTrade()
        trade._id = "TID12345"

        notification.completeObject = trade
        def nhResponse = handler.processRedeliveredTradeNotification(notification)
        assert nhResponse != null
        assert nhResponse.responseCode == NHResponseCode.SUCCESS

    }

    public void testProcessRedeliveredTradeNotification_UpdateEvent_TradeNotFound() {
        def handler = new MISNotificationHandler()
        handler = spy(handler)
        doReturn(null).when(handler).queryTrade(any(MISPerformanceMetrics.class), anyString(), anyString())
        doReturn(new NHResponse(NHResponseCode.SUCCESS)).when(handler).handle(any(Notification.class))
        doThrow(new RuntimeException()).when(handler).dropRedeliveredNotification(any(Notification.class))
        doThrow(new RuntimeException()).when(handler).isRedeliveredNotificationProcessingRequired(any(ApplicationEventCodes.class), any(FXSingleLeg.class))

        def notification = new Notification<SingleLegTrade>()
        notification.namespaceName = "NSX"
        notification.appEventCode = EVENT_ESP_TRADE_VERIFIED.code

        def trade = new SingleLegTrade()
        trade._id = "TID12345"

        notification.completeObject = trade
        def nhResponse = handler.processRedeliveredTradeNotification(notification)
        assert nhResponse != null
        assert nhResponse.responseCode == NHResponseCode.FAILURE
    }

    public void testProcessRedeliveredTradeNotification_UpdateEvent_TradeFound_ProcessingNotRequired() {
        def handler = new MISNotificationHandler()
        handler = spy(handler)
        doReturn(new FXSingleLegC()).when(handler).queryTrade(any(MISPerformanceMetrics.class), anyString(), anyString())
        doThrow(new RuntimeException()).when(handler).handle(any(Notification.class))
        doReturn(new NHResponse(NHResponseCode.SUCCESS)).when(handler).dropRedeliveredNotification(any(Notification.class))
        doReturn(false).when(handler).isRedeliveredNotificationProcessingRequired(any(ApplicationEventCodes.class), any(FXSingleLeg.class))

        def notification = new Notification<SingleLegTrade>()
        notification.namespaceName = "NSX"
        notification.appEventCode = EVENT_ESP_TRADE_VERIFIED.code

        def trade = new SingleLegTrade()
        trade._id = "TID12345"

        notification.completeObject = trade
        def nhResponse = handler.processRedeliveredTradeNotification(notification)
        assert nhResponse != null
        assert nhResponse.responseCode == NHResponseCode.SUCCESS
    }

    public void testProcessRedeliveredTradeNotification_UpdateEvent_TradeFound_ProcessingRequired() {
        def handler = new MISNotificationHandler()
        handler = spy(handler)
        doReturn(new FXSingleLegC()).when(handler).queryTrade(any(MISPerformanceMetrics.class), anyString(), anyString())
        doReturn(new NHResponse(NHResponseCode.SUCCESS)).when(handler).handle(any(Notification.class))
        doThrow(new RuntimeException()).when(handler).dropRedeliveredNotification(any(Notification.class))
        doReturn(true).when(handler).isRedeliveredNotificationProcessingRequired(any(ApplicationEventCodes.class), any(FXSingleLeg.class))

        def notification = new Notification<SingleLegTrade>()
        notification.namespaceName = "NSX"
        notification.appEventCode = EVENT_ESP_TRADE_VERIFIED.code

        def trade = new SingleLegTrade()
        trade._id = "TID12345"

        notification.completeObject = trade
        def nhResponse = handler.processRedeliveredTradeNotification(notification)
        assert nhResponse != null
        assert nhResponse.responseCode == NHResponseCode.SUCCESS
    }

    public void testIsRedeliveredNotificationProcessingRequired_EVENT_ESP_TRADE_CANCEL(){
        def handler = new MISNotificationHandler()
        def trade = new FXSingleLegC()
        trade = spy(trade)
        def tradeStateFacade = mock(TradeStateFacadeC.class)
        doReturn(tradeStateFacade).when(trade).getFacade(TradeStateFacade.TRADE_STATE_FACADE)

        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_CANCEL,trade)

        doReturn(true).when(tradeStateFacade).isCancelled()
        assert !handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_CANCEL,trade)
    }

    public void testIsRedeliveredNotificationProcessingRequired_EVENT_ESP_NET_TRADE_CANCEL(){
        def handler = new MISNotificationHandler()
        def trade = new FXSingleLegC()
        trade = spy(trade)
        def tradeStateFacade = mock(TradeStateFacadeC.class)
        doReturn(tradeStateFacade).when(trade).getFacade(TradeStateFacade.TRADE_STATE_FACADE)

        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_NET_TRADE_CANCEL,trade)

        doReturn(true).when(tradeStateFacade).isCancelled()
        assert !handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_NET_TRADE_CANCEL,trade)
    }

    public void testIsRedeliveredNotificationProcessingRequired_EVENT_ESP_TRADE_CONFIRMED(){
        def handler = new MISNotificationHandler()
        def trade = new FXSingleLegC()
        trade = spy(trade)
        def tradeStateFacade = mock(TradeStateFacadeC.class)
        doReturn(tradeStateFacade).when(trade).getFacade(TradeStateFacade.TRADE_STATE_FACADE)

        assert !handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_CONFIRMED,trade)

        doReturn(true).when(tradeStateFacade).isVerified()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_CONFIRMED,trade)
    }

    public void testIsRedeliveredNotificationProcessingRequired_EVENT_ESP_TRADE_REJECTED(){
        def handler = new MISNotificationHandler()
        def trade = new FXSingleLegC()
        trade = spy(trade)
        def tradeStateFacade = mock(TradeStateFacadeC.class)
        doReturn(tradeStateFacade).when(trade).getFacade(TradeStateFacade.TRADE_STATE_FACADE)

        assert !handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_REJECTED,trade)

        doReturn(true).when(tradeStateFacade).isPending()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_REJECTED,trade)
        doReturn(true).when(tradeStateFacade).isNew()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_REJECTED,trade)
        doReturn(true).when(tradeStateFacade).isCreated()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_REJECTED,trade)
        doReturn(true).when(tradeStateFacade).isAccepted()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_REJECTED,trade)
    }

    public void testIsRedeliveredNotificationProcessingRequired_EVENT_ESP_TRADE_PRE_VERIFIED(){
        def handler = new MISNotificationHandler()
        def trade = new FXSingleLegC()
        trade = spy(trade)
        def tradeStateFacade = mock(TradeStateFacadeC.class)
        doReturn(tradeStateFacade).when(trade).getFacade(TradeStateFacade.TRADE_STATE_FACADE)

        assert !handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_PRE_VERIFIED,trade)

        doReturn(true).when(tradeStateFacade).isPending()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_PRE_VERIFIED,trade)
        doReturn(true).when(tradeStateFacade).isNew()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_PRE_VERIFIED,trade)
        doReturn(true).when(tradeStateFacade).isCreated()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_PRE_VERIFIED,trade)
        doReturn(true).when(tradeStateFacade).isAccepted()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_PRE_VERIFIED,trade)
    }

    public void testIsRedeliveredNotificationProcessingRequired_EVENT_ESP_TRADE_VERIFIED(){
        def handler = new MISNotificationHandler()
        def trade = new FXSingleLegC()
        trade = spy(trade)
        def tradeStateFacade = mock(TradeStateFacadeC.class)
        doReturn(tradeStateFacade).when(trade).getFacade(TradeStateFacade.TRADE_STATE_FACADE)

        assert !handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_VERIFIED,trade)

        doReturn(true).when(tradeStateFacade).isPending()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_VERIFIED,trade)
        doReturn(true).when(tradeStateFacade).isNew()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_VERIFIED,trade)
        doReturn(true).when(tradeStateFacade).isCreated()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_VERIFIED,trade)
        doReturn(true).when(tradeStateFacade).isAccepted()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_VERIFIED,trade)
    }

    public void testIsRedeliveredNotificationProcessingRequired_EVENT_ESP_TRADE_AMEND(){
        def handler = new MISNotificationHandler()
        def trade = new FXSingleLegC()
        trade = spy(trade)
        def tradeStateFacade = mock(TradeStateFacadeC.class)
        doReturn(tradeStateFacade).when(trade).getFacade(TradeStateFacade.TRADE_STATE_FACADE)

        assert !handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_AMEND,trade)

        doReturn(true).when(tradeStateFacade).isVerified()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_AMEND,trade)
        doReturn(true).when(tradeStateFacade).isConfirmed()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_AMEND,trade)
    }

    public void testIsRedeliveredNotificationProcessingRequired_EVENT_ESP_NET_TRADE_AMEND(){
        def handler = new MISNotificationHandler()
        def trade = new FXSingleLegC()
        trade = spy(trade)
        def tradeStateFacade = mock(TradeStateFacadeC.class)
        doReturn(tradeStateFacade).when(trade).getFacade(TradeStateFacade.TRADE_STATE_FACADE)

        assert !handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_NET_TRADE_AMEND,trade)

        doReturn(true).when(tradeStateFacade).isNet()
        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_NET_TRADE_AMEND,trade)
    }

    public void testIsRedeliveredNotificationProcessingRequired_EVENT_ESP_TRADE_NETTED(){
        def handler = new MISNotificationHandler()
        def trade = new FXSingleLegC()
        trade = spy(trade)
        def tradeStateFacade = mock(TradeStateFacadeC.class)
        doReturn(tradeStateFacade).when(trade).getFacade(TradeStateFacade.TRADE_STATE_FACADE)

        assert handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_NETTED,trade)

        doReturn(true).when(tradeStateFacade).isNetted()
        assert !handler.isRedeliveredNotificationProcessingRequired(EVENT_ESP_TRADE_NETTED,trade)
    }

    public void testIsRedeliveredNotificationProcessingRequired_DefaultCase(){
        def handler = new MISNotificationHandler()
        def trade = new FXSingleLegC()
        trade = spy(trade)
        def tradeStateFacade = mock(TradeStateFacadeC.class)
        doReturn(tradeStateFacade).when(trade).getFacade(TradeStateFacade.TRADE_STATE_FACADE)

        def handledCodes = [
                EVENT_ESP_TRADE_CANCEL,
                EVENT_ESP_NET_TRADE_CANCEL,
                EVENT_ESP_TRADE_CONFIRMED,
                EVENT_ESP_TRADE_REJECTED,
                EVENT_ESP_TRADE_PRE_VERIFIED,
                EVENT_ESP_TRADE_VERIFIED,
                EVENT_ESP_TRADE_AMEND,
                EVENT_ESP_NET_TRADE_AMEND,
                EVENT_ESP_TRADE_NETTED,
                EVENT_ESP_MS_LOOKUP
        ]

        for(ApplicationEventCodes aec : values()){
            if( !handledCodes.contains(aec))
                assert !handler.isRedeliveredNotificationProcessingRequired(aec,trade) : "Failed for "+aec.name()
        }

    }
}
