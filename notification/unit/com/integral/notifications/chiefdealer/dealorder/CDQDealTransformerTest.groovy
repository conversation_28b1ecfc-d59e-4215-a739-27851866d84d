package com.integral.notifications.chiefdealer.dealorder

import com.integral.finance.dealing.Deal
import com.integral.finance.dealing.facade.DealStateFacadeC
import com.integral.finance.dealing.fx.FXSingleLegDeal
import com.integral.finance.dealing.fx.FXSingleLegDealC
import com.integral.finance.dealing.fx.FXSingleLegOrderC
import com.integral.model.dealing.SingleLegOrder
import com.integral.model.dealing.SingleLegTrade
import com.integral.model.dealing.State
import com.integral.testframework.api.API
import com.integral.testframework.api.MATCHEVENT
import com.integral.testframework.api.SINGLELEGORDER
import com.integral.testframework.api.SINGLELEGTRADE
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import com.integral.testframework.api.QUOTE

import static com.integral.model.dealing.OrderRequest.RequestLeg.BuySellMode.BUY
import static org.mockito.Mockito.*

/**
 * Created by verma on 4/15/14.
 */
class CDQDealTransformerTest extends AbstractCDQTransformerTestCase {
    @Override
    protected void setUp() throws Exception {
        super.setUp()
        API.setupInMemoryTestInfrastructure()
    }

    @Override
    protected void tearDown() throws Exception {
        super.tearDown()
        deals.clear()
    }

    def deals = new ArrayList<FXSingleLegDealC>()

    public void testHandleTradeAccept_FIBA_TSINIT() {
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                                        , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSINIT, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty]).trade
        transformer.handleTradeAccept(trade)

        assert deals.size() == 1
        def deal = deals.get(0)
        assert deal.namespaceName == tc.FI1.org.shortName
        assert deal.taker
    }

    public void testHandleTradeAccept_FIBA_TSVERIFIED() {
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                                        , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match

        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSVERIFIED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty]).trade
        transformer.handleTradeAccept(trade)

        assert deals.size() == 1
        def deal = deals.get(0)
        assert deal.namespaceName == tc.FI1.org.shortName
        assert deal.taker
    }
	
	

    public void testHandleTradeAccept_FIBA_TSCONFIRMED() {
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                                        , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSVERIFIED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty]).trade
        transformer.handleTradeAccept(trade)

        assert deals.size() == 1
        def deal = deals.get(0)
        assert deal.namespaceName == tc.FI1.org.shortName
        assert deal.taker
    }

    public void testHandleTradeAccept_FIBA_TSREJECTED() {
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                                        , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty]).trade
        transformer.handleTradeAccept(trade)

        assert deals.size() == 1
        def deal = deals.get(0)
        assert deal.namespaceName == tc.FI1.org.shortName
        assert deal.taker
    }

    public void testHandleTradeAccept_FIBA_TSFAILED() {
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                                        , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSFAILED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty]).trade
        transformer.handleTradeAccept(trade)

        assert deals.size() == 0
    }

    public void testHandleTradeVerified_FIBA_TSVERIFIED() {
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                                        , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSVERIFIED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty]).trade

        CDViewService.init()
        CDViewService.createAndGetCDViewManager(order)

        transformer.handleTradeAccept(trade)
        transformer.handleTradeVerified(trade)

        assert deals.size() == 2
        def deal = getDeal("TID1", "FI1")
        assert deal.namespaceName == tc.FI1.org.shortName
        assert deal.taker

        deal = getDeal("TID1", "BRK1")
        assert deal.namespaceName == tc.BRK1.org.shortName
        assert !deal.taker
    }

    public void testHandleTradeVerified_FIBA_TSCONFIRMED() {
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                                        , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSVERIFIED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty]).trade

        CDViewService.init()
        CDViewService.createAndGetCDViewManager(order)

        transformer.handleTradeAccept(trade)
        transformer.handleTradeVerified(trade)

        assert deals.size() == 2
        def deal = getDeal("TID1", "FI1")
        assert deal.namespaceName == tc.FI1.org.shortName
        assert deal.taker

        deal = getDeal("TID1", "BRK1")
        assert deal.namespaceName == tc.BRK1.org.shortName
        assert !deal.taker
    }

    public void testHandleTradeRejected_FIBA_TSREJECTED() {
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                                        , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty]).trade

        transformer.handleTradeAccept(trade)
        transformer.handleTradeRejected(trade)

        assert deals.size() == 1
        def deal = getDeal("TID1", "FI1")
        assert deal.namespaceName == tc.FI1.org.shortName
        assert deal.taker
    }

    private FXSingleLegDealC getDeal(String tradeId, String namespace) {
        for (FXSingleLegDealC deal : deals) {
            if ((tradeId + "_" + namespace).equals(deal._id) && namespace.equals(deal.namespaceName)) {
                return deal
            }
        }
        return null
    }

    protected CDQDealTransformer createCDQDealTransformer() {
        CDQDealExpirableCache dealCache = mock(CDQDealExpirableCache.class)
        when(dealCache.get(anyString(), anyString(), anyString())).thenAnswer(new Answer<Object>() {
            @Override
            Object answer(InvocationOnMock invocation) throws Throwable {
                def String tradeId = invocation.arguments[0]
                def String namespace = invocation.arguments[2]
                return getDeal(tradeId, namespace)
            }
        })

        CDQOrderExpirableCache orderCache = mock(CDQOrderExpirableCache.class)

        def dealTransformer = new CDQDealTransformer(dealCache, orderCache, new ChiefDealerNotifier())
        SpacesDealFacade dealFacade = mock(SpacesDealFacadeC.class)
        dealTransformer.df = dealFacade
        when(dealFacade.dealCreate(any(SingleLegTrade.class), anyString())).thenAnswer(new Answer<Object>() {
            @Override
            Object answer(InvocationOnMock invocation) throws Throwable {
                FXSingleLegDeal deal = createFXSingleLegDeal()
                deals.add(deal)
                return deal
            }
        })
        when(dealFacade.dealVerified(any(SingleLegTrade.class), any(Deal.class), anyString())).thenAnswer(new Answer<Object>() {
            @Override
            Object answer(InvocationOnMock invocation) throws Throwable {
                Deal deal = deals.get(0);
                deal.setState(DealStateFacadeC.VERIFIED_STATE);
                return invocation.arguments[1]
            }
        })
        SpacesOrderFacadeC orderFacade = mock(SpacesOrderFacadeC.class)
        dealTransformer.of = orderFacade
        doReturn(new FXSingleLegOrderC()).when(orderFacade).orderCreate(any(SingleLegOrder.class))

        ChiefDealerNotifier chiefDealerNotifier = mock(ChiefDealerNotifier.class)
        dealTransformer.cdNotifier = chiefDealerNotifier

        return dealTransformer
    }

    private static FXSingleLegDealC createFXSingleLegDeal() {
        FXSingleLegDeal deal = new FXSingleLegDealC()
        return deal
    }


    public void testShouldProcessDealReplay_DEAL_CREATE_TRUE(){
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty ,"externalReferenceId":"testExternalReferenceId"]).trade

        CDViewManager.CDView cdView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER,tc.FI1.org);

        boolean shouldReplay = transformer.shouldProcessDealReplay(trade,CDQDealTransformer.DealTransformAction.DEAL_CREATE_EVENT,cdView) ;
        assertTrue(shouldReplay);


    }

    public void testShouldProcessDealReplay_DEAL_CREATE_FALSE(){
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty ,"externalReferenceId":"testExternalReferenceId"]).trade

        CDViewManager.CDView cdView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER,tc.FI1.org);

        transformer.handleTradeAccept(trade);

        boolean shouldReplay = transformer.shouldProcessDealReplay(trade,CDQDealTransformer.DealTransformAction.DEAL_CREATE_EVENT,cdView) ;
        assertFalse(shouldReplay);

    }


    public void testShouldProcessDealReplay_DEAL_NET_TRUE(){
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty ,"externalReferenceId":"testExternalReferenceId"]).trade

        CDViewManager.CDView cdView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER,tc.FI1.org);

        boolean shouldReplay = transformer.shouldProcessDealReplay(trade,CDQDealTransformer.DealTransformAction.DEAL_NET_EVENT,cdView) ;
        assertTrue(shouldReplay);

    }

    public void testShouldProcessDealReplay_DEAL_NET_FALSE(){
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty ,"externalReferenceId":"testExternalReferenceId"]).trade

        CDViewManager.CDView cdView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER,tc.FI1.org);

        transformer.handleTradeNet(trade);

        boolean shouldReplay = transformer.shouldProcessDealReplay(trade,CDQDealTransformer.DealTransformAction.DEAL_NET_EVENT,cdView) ;
        assertFalse(shouldReplay);
    }

    public void testShouldProcessDealReplay_DEAL_AMENDED_NULL(){
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty ,"externalReferenceId":"testExternalReferenceId"]).trade

        CDViewManager.CDView cdView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER,tc.FI1.org);

        try {
            boolean shouldReplay = transformer.shouldProcessDealReplay(trade, CDQDealTransformer.DealTransformAction.DEAL_AMENDED_EVENT, cdView);
            assertFalse(shouldReplay);
        } catch (Exception e) {
            fail()
        }
    }

    public void testShouldProcessDealReplay_DEAL_AMENDED_FALSE(){
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty ,"externalReferenceId":"testExternalReferenceId"]).trade


        CDViewService.init()
        CDViewService.createAndGetCDViewManager(order)

        CDViewManager.CDView cdView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER,tc.FI1.org);

        transformer.handleTradeAccept(trade);
        transformer.handleTradeVerified(trade);

        boolean shouldReplay = transformer.shouldProcessDealReplay(trade,CDQDealTransformer.DealTransformAction.DEAL_AMENDED_EVENT,cdView) ;
        assertTrue(shouldReplay);

        /***
         * Deal is updated to a state other than VERIFIED
         */
        deals.get(0).setState(DealStateFacadeC.CREATED_STATE);

        shouldReplay = transformer.shouldProcessDealReplay(trade,CDQDealTransformer.DealTransformAction.DEAL_AMENDED_EVENT,cdView) ;
        assertFalse(shouldReplay);
    }


    public void testShouldProcessDealReplay_DEAL_CANCELLED_NULL(){
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty ,"externalReferenceId":"testExternalReferenceId"]).trade

        CDViewManager.CDView cdView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER,tc.FI1.org);

        try {
            boolean shouldReplay = transformer.shouldProcessDealReplay(trade, CDQDealTransformer.DealTransformAction.DEAL_CANCELLED_EVENT, cdView);
            assertFalse(shouldReplay);
        } catch (Exception e) {
            fail()
        }
    }

    public void testShouldProcessDealReplay_DEAL_CANCELLED(){
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty ,"externalReferenceId":"testExternalReferenceId"]).trade


        CDViewService.init()
        CDViewService.createAndGetCDViewManager(order)

        CDViewManager.CDView cdView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER,tc.FI1.org);

        transformer.handleTradeAccept(trade);
        transformer.handleTradeVerified(trade);

        boolean shouldReplay = transformer.shouldProcessDealReplay(trade,CDQDealTransformer.DealTransformAction.DEAL_CANCELLED_EVENT,cdView) ;
        assertTrue(shouldReplay);

        /***
         * Deal is updated to a state other than VERIFIED
         */
        deals.get(0).setState(DealStateFacadeC.CREATED_STATE);

        shouldReplay = transformer.shouldProcessDealReplay(trade,CDQDealTransformer.DealTransformAction.DEAL_CANCELLED_EVENT,cdView) ;
        assertFalse(shouldReplay);
    }


    public void testShouldProcessDealReplay_DEAL_NETTED_NULL(){
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty ,"externalReferenceId":"testExternalReferenceId"]).trade

        CDViewManager.CDView cdView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER,tc.FI1.org);

        try {
            boolean shouldReplay = transformer.shouldProcessDealReplay(trade, CDQDealTransformer.DealTransformAction.DEAL_NETTED_EVENT, cdView);
            assertFalse(shouldReplay);
        } catch (Exception e) {
            fail()
        }
    }

    public void testShouldProcessDealReplay_DEAL_NETTED(){
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty ,"externalReferenceId":"testExternalReferenceId"]).trade


        CDViewService.init()
        CDViewService.createAndGetCDViewManager(order)

        CDViewManager.CDView cdView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER,tc.FI1.org);

        transformer.handleTradeAccept(trade);
        transformer.handleTradeVerified(trade);

        boolean shouldReplay = transformer.shouldProcessDealReplay(trade,CDQDealTransformer.DealTransformAction.DEAL_NETTED_EVENT,cdView) ;
        assertTrue(shouldReplay);

        /***
         * Deal is updated to a state other than VERIFIED
         */
        deals.get(0).setState(DealStateFacadeC.CREATED_STATE);

        shouldReplay = transformer.shouldProcessDealReplay(trade,CDQDealTransformer.DealTransformAction.DEAL_NETTED_EVENT,cdView) ;
        assertFalse(shouldReplay);
    }


    public void testShouldProcessDealReplay_DEAL_VERIFIED_NULL(){
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty ,"externalReferenceId":"testExternalReferenceId"]).trade

        CDViewManager.CDView cdView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER,tc.FI1.org);

        try {
            boolean shouldReplay = transformer.shouldProcessDealReplay(trade, CDQDealTransformer.DealTransformAction.DEAL_VERIFIED_EVENT, cdView);
            assertFalse(shouldReplay);
        } catch (Exception e) {
            fail()
        }
    }

    public void testShouldProcessDealReplay_DEAL_VERIFIED(){
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty ,"externalReferenceId":"testExternalReferenceId"]).trade


        CDViewService.init()
        CDViewService.createAndGetCDViewManager(order)

        CDViewManager.CDView cdView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER,tc.FI1.org);

        transformer.handleTradeAccept(trade);


        deals.get(0).setState(DealStateFacadeC.CREATED_STATE);
        boolean shouldReplay = transformer.shouldProcessDealReplay(trade,CDQDealTransformer.DealTransformAction.DEAL_VERIFIED_EVENT,cdView) ;
        assertTrue(shouldReplay);

        /***
         * Deal is updated to a state other than CREATED
         */


        deals.get(0).setState(DealStateFacadeC.VERIFIED_STATE);

        shouldReplay = transformer.shouldProcessDealReplay(trade,CDQDealTransformer.DealTransformAction.DEAL_VERIFIED_EVENT,cdView) ;
        assertFalse(shouldReplay);
    }


    public void testShouldProcessDealReplay_DEAL_REJECTED_NULL(){
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty ,"externalReferenceId":"testExternalReferenceId"]).trade

        CDViewManager.CDView cdView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER,tc.FI1.org);

        try {
            boolean shouldReplay = transformer.shouldProcessDealReplay(trade, CDQDealTransformer.DealTransformAction.DEAL_REJECTED_EVENT, cdView);
            assertFalse(shouldReplay);
        } catch (Exception e) {
            fail()
        }
    }

    public void testShouldProcessDealReplay_DEAL_REJECTED(){
        def transformer = createCDQDealTransformer()
        def tc = new CDQTestConfig()
        def order = new SINGLELEGORDER([_id     : "OID1", correlationId: "1", stateName: State.Name.RSPARTIAL, organization: tc.FI1, user: tc.FI1USER1, virtualServer: "test-vs"
                , amount: 1000.00D, ccyPair: tc.EURUSD, dealtCcy: tc.EUR, orderRate: 1.3, buySell: BUY]).order
        def match = new MATCHEVENT([_id: "MID1", order: order]).match
        def trade = new SINGLELEGTRADE([_id: "TID1", match: match, stateName: State.Name.TSREJECTED, dealtAmount: 500.00D, legalEntity: tc.FI1LE1, tradingParty: tc.BRK1LE1_FI1.tradingParty ,"externalReferenceId":"testExternalReferenceId"]).trade


        CDViewService.init()
        CDViewService.createAndGetCDViewManager(order)

        CDViewManager.CDView cdView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER,tc.FI1.org);

        transformer.handleTradeAccept(trade);


        deals.get(0).setState(DealStateFacadeC.CREATED_STATE);
        boolean shouldReplay = transformer.shouldProcessDealReplay(trade,CDQDealTransformer.DealTransformAction.DEAL_REJECTED_EVENT,cdView) ;
        assertTrue(shouldReplay);

        /***
         * Deal is updated to a state other than CREATED
         */


        deals.get(0).setState(DealStateFacadeC.VERIFIED_STATE);

        shouldReplay = transformer.shouldProcessDealReplay(trade,CDQDealTransformer.DealTransformAction.DEAL_REJECTED_EVENT,cdView) ;
        assertFalse(shouldReplay);
    }










}
