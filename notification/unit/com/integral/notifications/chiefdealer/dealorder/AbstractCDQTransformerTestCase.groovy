package com.integral.notifications.chiefdealer.dealorder

import com.integral.testframework.api.API
import com.integral.testframework.api.BROKER
import com.integral.testframework.api.LE
import com.integral.testframework.api.ORG
import com.integral.testframework.api.TP
import com.integral.testframework.api.USER

/**
 * Created with IntelliJ IDEA.
 * User: shahr
 * Date: 4/21/14
 * Time: 3:22 PM
 */
abstract class AbstractCDQTransformerTestCase extends GroovyTestCase{





    protected class CDQTestConfig {
        def ORG FI1,PB1
        def USER FI1USER1,BRK1USER1
        def BROKER BRK1
        def LE FI1LE1, BRK1LE1
        def TP FI1LE1_BRK1
        def TP BRK1LE1_FI1
        def EURUSD
        def EUR

        {
            FI1 = API.createORG "FI1"
            PB1 = API.createORG "PB1"

            FI1LE1 = FI1.legalEntity "FI1LE1"
            FI1USER1 = FI1.user "FI1USER1"

            BRK1 = API.createBroker "BRK1"
            BRK1LE1 = BRK1.legalEntity "BRK1LE1"
            BRK1USER1 = BRK1.user "BRK1USER1"

            FI1.addLP BRK1
            BRK1.addFI FI1

            API.createTP(FI1, BRK1)
            API.createTP(BRK1, FI1)

            FI1LE1_BRK1 = API.getTP(BRK1, FI1LE1)
            BRK1LE1_FI1 = API.getTP(FI1, BRK1LE1)

            EURUSD = API.getCcyPair "EUR/USD"
            EUR = EURUSD.baseCurrency
        }
    }

}
