package com.integral.notifications.chiefdealer.dealorder

import com.integral.spaces.notification.NHResponse
import com.integral.spaces.notification.NHResponseCode

/**
 * Created with IntelliJ IDEA.
 * User: shahr
 * Date: 4/22/14
 * Time: 11:24 AM
 */
class CDQPersistenceNotificationHandlerTest extends GroovyTestCase{

    public void testGetFinalResponse(){
        CDQPersistenceNotificationHandler handler = new CDQPersistenceNotificationHandler();

        NHResponse response1 = new NHResponse(NHResponseCode.SUCCESS);
        NHResponse response2 = new NHResponse(NHResponseCode.SUCCESS);
        NHResponse response3 = new NHResponse(NHResponseCode.SUCCESS);

        NHResponse response = handler.getFinalResponse(response1,response2,response3);

        assertEquals(response.getResponseCode(),NHResponseCode.SUCCESS);

        NHResponse response4 = new NHResponse(NHResponseCode.SUCCESS);
        NHResponse response5 = new NHResponse(NHResponseCode.SUCCESS);
        NHResponse response6 = new NHResponse(NHResponseCode.FAILURE);

        response = handler.getFinalResponse(response4,response5,response6);

        assertEquals(response.getResponseCode(),NHResponseCode.FAILURE);

        NHResponse response7 = new NHResponse(NHResponseCode.SUCCESS);
        def errorString1 = "testDescp1=test1,test2,test3"
        NHResponse response8 = new NHResponse(NHResponseCode.FAILURE, errorString1);
        def errorString2 = "testDescp1=test4,test5,test6"
        NHResponse response9 = new NHResponse(NHResponseCode.FAILURE,errorString2);

        response = handler.getFinalResponse(response7,response8,response9)

        assertEquals(response.getResponseCode(),NHResponseCode.FAILURE);

        assertEquals(response.getErrorDescription(),errorString1 + CDQPersistenceNotificationHandler.LINE_BREAK + errorString2+CDQPersistenceNotificationHandler.LINE_BREAK)

    }

    public void testRegenerateProperties(){

        CDQPersistenceNotificationHandler handler = new CDQPersistenceNotificationHandler();
        def errorString1 = "testDescp1=test1,test2,test3"
        def errorString2 = "testDescp2=test4,test5,test6"

        def errorDesc = errorString1 + CDQPersistenceNotificationHandler.LINE_BREAK + errorString2+CDQPersistenceNotificationHandler.LINE_BREAK

        Properties properties = handler.load(errorDesc)
        assertEquals(properties.size(),2)
        assertEquals(properties.getProperty("testDescp1"),"test1,test2,test3");
        assertEquals(properties.getProperty("testDescp2"),"test4,test5,test6");
    }

}
