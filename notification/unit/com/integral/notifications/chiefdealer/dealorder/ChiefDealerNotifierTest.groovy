package com.integral.notifications.chiefdealer.dealorder

import com.integral.finance.dealing.Order
import com.integral.finance.dealing.fx.FXSingleLegOrderC
import com.integral.testframework.api.API

/**
 * Created with IntelliJ IDEA.
 * User: shahr
 * Date: 5/8/14
 * Time: 12:38 PM
 */
class ChiefDealerNotifierTest extends AbstractCDQTransformerTestCase{

    @Override
    protected void setUp() throws Exception {
        super.setUp()
        API.setupInMemoryTestInfrastructure()
    }

    public void testIgnoreNotification_custorg_null_placedByOrg_null(){
        def tc = new CDQTestConfig()

        ChiefDealerNotifier notifier = new ChiefDealerNotifier();

        Order order = new FXSingleLegOrderC();
        boolean ignore = notifier.ignoreNotification(order, tc.PB1.org);
        assertFalse(ignore)

    }

    public void testIgnoreNotification_custorg_fi_placedByOrg_fi(){
        def tc = new CDQTestConfig()

        ChiefDealerNotifier notifier = new ChiefDealerNotifier();

        Order order = new FXSingleLegOrderC();

        order.setCustomerOrg(tc.FI1.org);
        order.setPlacedByOrg(tc.FI1.org);

        boolean ignore = notifier.ignoreNotification(order, tc.PB1.org);
        assertTrue(ignore)

        ignore = notifier.ignoreNotification(order, tc.FI1.org);
        assertFalse(ignore)
    }

    public void testIgnoreNotification_custorg_fi_placedByOrg_pb(){
        def tc = new CDQTestConfig()

        ChiefDealerNotifier notifier = new ChiefDealerNotifier();

        Order order = new FXSingleLegOrderC();

        order.setCustomerOrg(tc.FI1.org);
        order.setPlacedByOrg(tc.PB1.org);

        boolean ignore = notifier.ignoreNotification(order, tc.PB1.org);
        assertFalse(ignore)

        ignore = notifier.ignoreNotification(order, tc.FI1.org);
        assertFalse(ignore)
    }


    public void testIgnoreNotification_custorg_fi_placedByOrg_fi_withBroker(){
        def tc = new CDQTestConfig()

        ChiefDealerNotifier notifier = new ChiefDealerNotifier();

        Order order = new FXSingleLegOrderC();

        order.setCustomerOrg(tc.FI1.org);
        order.setPlacedByOrg(tc.FI1.org);
        order.getCustomerOrg().setBrokerOrganization(tc.BRK1.org)


        boolean ignore = notifier.ignoreNotification(order, tc.PB1.org);
        assertTrue(ignore)

        ignore = notifier.ignoreNotification(order, tc.FI1.org);
        assertFalse(ignore)

        ignore = notifier.ignoreNotification(order, tc.BRK1.org);
        assertFalse(ignore)

    }


    public void testIgnoreNotification_custorg_fi_placedByOrg_pb_withBroker(){
        def tc = new CDQTestConfig()

        ChiefDealerNotifier notifier = new ChiefDealerNotifier();

        Order order = new FXSingleLegOrderC();

        order.setCustomerOrg(tc.FI1.org);
        order.setPlacedByOrg(tc.PB1.org);
        order.getCustomerOrg().setBrokerOrganization(tc.BRK1.org)


        boolean ignore = notifier.ignoreNotification(order, tc.PB1.org);
        assertFalse(ignore)

        ignore = notifier.ignoreNotification(order, tc.FI1.org);
        assertFalse(ignore)

        ignore = notifier.ignoreNotification(order, tc.BRK1.org);
        assertFalse(ignore)

    }
}
