package com.integral.is.common;

import java.util.Hashtable;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.spaces.notification.NotificationConfiguration;
import com.integral.spaces.notification.NotificationManager;
import com.integral.spaces.notification.NotificationManagerFactory;
import com.integral.spaces.services.SpacesIDServiceFactory;
import com.integral.system.runtime.ShutdownTask;

/**
 * Shutdown only the NotificationServer
 */
public class NotificationsShutdownTask implements ShutdownTask {
    protected Log log = LogFactory.getLog(NotificationsShutdownTask.class);

    public String shutdown(String aName, Hashtable args) {
        log.info("Shutting down NotificationManager");
        NotificationManagerFactory.shutdownAllNotificationManagers();
        NotificationManager.getInstance().shutdown();
        if(NotificationConfiguration.getInstance().isNotificationPersistenceEnabled()){
            log.info("Stopping SpacesIDServiceFactory");
            SpacesIDServiceFactory.getInstance().stop();
        }
        log.info("NotificationManager shutdown complete");
        return "Notifications Shutdown";
    }
}