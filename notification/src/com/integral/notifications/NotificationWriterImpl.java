package com.integral.notifications;

import java.nio.ByteBuffer;

import com.integral.dbservice.notifications.NotificationPersistenceService;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.NotificationEntity;
import com.integral.spaces.notification.Notification;
import com.integral.spaces.notification.persistence.NotificationWriter;

public class NotificationWriterImpl implements NotificationWriter {

	private static Log log = LogFactory.getLog(NotificationWriterImpl.class);

	@Override
	public void writeNotification(String queueName, String handlerGroup,
			Notification<ByteBuffer> notification) {

		log.info("Saving notification received on queue:" + queueName + ":handlerGroup:" + handlerGroup  + ":"
				+ notification + ":using DBService");
		try {
			NotificationEntity entity = new NotificationEntity(queueName, handlerGroup, notification);
			NotificationPersistenceService.instance().writeNotification(
					entity);
		} catch (Exception e) {
			log.error("Error in saving notification on queue:" + queueName + ":handlerGroup:" + handlerGroup  + ":"
					+ notification + ":using DBService", e);
		}

	}
}
