package com.integral.notifications.chiefdealer.dealorder;

import static com.integral.is.common.query.ClientDataQueryConstants.marketFwdPts;
import static com.integral.is.common.query.ClientDataQueryConstants.marketRate;
import static com.integral.is.common.query.ClientDataQueryConstants.marketSpotRate;

import java.util.HashMap;
import java.util.Map;

import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.Order;
import com.integral.finance.dealing.fx.FXSingleLegOrderC;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.query.ClientDataQueryHelper;
import com.integral.is.spaces.fx.client.alert.ClientAlertPersistenceUtil;
import com.integral.is.spaces.fx.client.fxi.ClientResponseBuilder;
import com.integral.is.system.notification.NotificationMessageSender;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import java.util.List;
import java.util.ArrayList;

/**
 * User: anup
 */
public class ChiefDealerNotifier implements IChiefDealerNotifier {

    private static Log log = LogFactory.getLog( ChiefDealerNotifier.class);
    private static ThreadLocal<Map<String, Object>> TLdataMap = new ThreadLocal<Map<String, Object>>(){

        @Override
        public Map<String, Object> initialValue() {
            return  new HashMap<String, Object>(128);
        }
    };

    private Map<String, String> jmsProps = new HashMap<String, String>(2);

    private static String[] TRADE_ADDITIONAL_PARAMS={ISConstantsC.AVG_FILLED_PRICE,ISConstantsC.ORDER_AVG_FILL_PRICE,
            ISConstantsC.ORDER_FILLED_AMT,ISConstantsC.ORDER_UNFILLED_AMT,
            ISConstantsC.FILLED_AMOUNT, "errorDescription","ResponseRecievedTime","LPCrossing",ISConstantsC.REQ_REF_ID};

    private static String[] TRADE_PB_ADDITIONAL_PARAMS={
            "errorDescription",ISConstantsC.REQ_REF_ID,
            marketRate,marketSpotRate,ISConstantsC.REQUEST_TYPE,marketFwdPts, ISCommonConstants.REQUEST_TYPE};

    private static String[] TRADE_CANCEL_ADDITIONAL_PARAMS={marketSpotRate, marketFwdPts};
    private static String[] TRADE_PB_CANCEL_ADDITIONAL_PARAMS={marketSpotRate, marketFwdPts};

    private static String[] ORDER_ADDITIONAL_PARAMS={ISConstantsC.IS_ORDER_ID, ISCommonConstants.ORDER_EXECUTION_STRATEGY};

    private static String[] ORDER_EXPIRY_ADDITIONAL_PARAMS={ISConstantsC.IS_ORDER_ID, ISCommonConstants.ORDER_EXECUTION_STRATEGY};

    private static String[] ORDER_CANCEL_ADDITIONAL_PARAMS={ISConstantsC.IS_ORDER_ID,
        ISCommonConstants.ORDER_EXECUTION_STRATEGY, ISCommonConstants.Key_ExpiryByScheduler};

    private static String[] ORDER_RESTATED_ADDITIONAL_PARAMS ={ISConstantsC.IS_ORDER_ID,
        ISCommonConstants.ORDER_EXECUTION_STRATEGY,ISCommonConstants.EVENT_SEQ_ID,ISCommonConstants.AVAILABLE_AMOUNT};

    private static String[] ORDER_AMEND_ADDITIONAL_PARAMS = {ISConstantsC.IS_ORDER_ID, ISCommonConstants.EVENT_SEQ_ID,
        ISCommonConstants.ORDER_EXECUTION_STRATEGY, ISConstantsC.WF_PARAM_NEW_ORDER_RATE,
        ISConstantsC.WF_PARAM_NEW_ORDER_AMOUNT, ISCommonConstants.LAST_ORDER_AMOUNT,ISCommonConstants.WF_PARAM_NEW_ORDER_NOTES};

    public ChiefDealerNotifier() {
        jmsProps.put( ISConstantsC.JMS_PROP_ACK_REQUIRED, "true" );
        jmsProps.put( "MessageType", "ResponseESP_1" );
    }


    public void notifyChiefDealer( SingleLegOrder orderRequest, Order order, Organization organization, ApplicationEventCodes ase)
    {
        notifyChiefDealer(orderRequest, order, organization, ase, null);
    }

    public void notifyChiefDealer( SingleLegOrder orderRequest, Order order, Organization organization,
                                   ApplicationEventCodes ase, Map<String, String> notificationProperties)
    {
        Map dataMap = TLdataMap.get();
        dataMap.clear();
        dataMap.put("workflowMessageId", System.currentTimeMillis());
        if (orderRequest != null) {
            dataMap.put("referenceId", order.getClientReferenceId());
            if (dataMap.get(ISCommonConstants.EVENT_SEQ_ID) == null && orderRequest.getEventSeqId() > 0) {
                dataMap.put(ISCommonConstants.EVENT_SEQ_ID, String.valueOf(orderRequest.getEventSeqId()));
            }
        }

        ClientResponseBuilder.getInstance().updateData((FXSingleLegOrderC) order, dataMap);
        String message = null;
        boolean notifyDealer =false;
        switch( ase ) {
            case EVENT_CD_ORDER_SUBMIT:
                ISMBean ismBean = ISFactory.getInstance().getISMBean();
                boolean customerOrderExcluded = ismBean.isCustomerOrderExcluded(organization);
                if (customerOrderExcluded && ignoreNotification(order, organization)) {
                    return;
                }
                message = ClientDataQueryHelper.getInstance().getOrderSubmittedMessage( dataMap, new StringBuilder( 1000 ),ORDER_ADDITIONAL_PARAMS );
                break;
            case EVENT_ESP_CD_ORDER_UPDATE:
            	message = ClientDataQueryHelper.getInstance().getOrderUpdateMessage(dataMap, new StringBuilder(1000), ORDER_ADDITIONAL_PARAMS);
                notifyDealer = shouldNotifyDealer(order);
                break;
            case EVENT_ESP_CD_ORDER_CANCEL:
                message = ClientDataQueryHelper.getInstance().getOrderCancelledMessage(dataMap, new StringBuilder(1000), ORDER_CANCEL_ADDITIONAL_PARAMS);
                notifyDealer = shouldNotifyDealer(order);
                break;
            case EVENT_ESP_CD_ORDER_EXPIRED:
                message = ClientDataQueryHelper.getInstance().getOrderCancelledMessage(dataMap, new StringBuilder(1000), ORDER_EXPIRY_ADDITIONAL_PARAMS);
                break;
            case EVENT_ESP_CD_ORDER_RESTATED:
                message = ClientDataQueryHelper.getInstance().getOrderRestatedMessage(dataMap, new StringBuilder(1000), ORDER_RESTATED_ADDITIONAL_PARAMS );
                persistClientAlertMessage(orderRequest, order, message);
                break;
            case EVENT_ESP_CD_ORDER_SUSPEND_RESUME:
                message = ClientDataQueryHelper.getInstance().getOrderSuspendResumeMessage(dataMap, new StringBuilder(1000), ORDER_ADDITIONAL_PARAMS, order.isStrategyExecutionSuspended());
                notifyDealer = shouldNotifyDealer(order);
                break;
            case EVENT_ESP_CD_ORDER_AMEND:
                dataMap.remove(ISCommonConstants.BROADCASTORDER); // MTX-1531 Client reverses order side if broadcast order flag is true for amend orders.
                message = ClientDataQueryHelper.getInstance().getOrderAmendMessage(dataMap, new StringBuilder(1000), ORDER_AMEND_ADDITIONAL_PARAMS);
                persistClientAlertMessage(orderRequest, order, message);
                notifyDealer = shouldNotifyDealer(order);
                break;
            case EVENT_ESP_CD_ORDER_FAILED:
            case EVENT_ESP_CD_ORDER_CREATE_FAILED:
                message = ClientDataQueryHelper.getInstance().getOrderSubmissionFailedMessage(dataMap, new StringBuilder(1000));
                break;
            case EVENT_ESP_CD_ORDER_OTOTRIGGERED:
            	message = ClientDataQueryHelper.getInstance().getOrderSubmittedMessage(dataMap, new StringBuilder( 1000 ), ORDER_ADDITIONAL_PARAMS );
            	persistClientAlertMessage(orderRequest, order, message);
                break;
            case EVENT_ESP_CD_ORDER_PENDING:
                populateNotificationProperties(notificationProperties, dataMap);
                message = ClientDataQueryHelper.getInstance().getOrderPendingMessage(dataMap, new StringBuilder(1000));
                persistClientAlertMessage(orderRequest, order, message);
                break;
            case EVENT_ESP_CD_ORDER_PRE_RATE_CANCEL:
                message = ClientDataQueryHelper.getInstance().getOrderCancelledMessage(dataMap, new StringBuilder(1000), ORDER_CANCEL_ADDITIONAL_PARAMS);
                break;
            case EVENT_ESP_CD_ORDER_PRE_RATE_EXPIRED:
                message = ClientDataQueryHelper.getInstance().getOrderCancelledMessage(dataMap, new StringBuilder(1000), ORDER_EXPIRY_ADDITIONAL_PARAMS);
                break;
        }
        if(notifyDealer)
        {
            log.info("notifyChiefDealer : Notifying dealer order message=" + message + ", ase=" + ase + ", user=" +
                    order.getUser().getShortName() + ", organization=" + organization.getShortName());
            sendMessageToDealer(message,jmsProps,order.getUser());
        }
        log.info("notifyChiefDealer : Notifying chief dealer order message=" + message + ", ase=" + ase +", organization="+organization.getShortName());
        sendMessageToUserPermission( message, jmsProps, organization, ISConstantsC.CHIEFDEALER_MPVIEW_PERM );
    }

    protected boolean ignoreNotification(Order order, Organization organization) {
        if (order.getCustomerOrg() != null && order.getPlacedByOrg() != null) {
            // Processing orders for only customer orgs or broker for customers or for sales dealer

            if (organization.isSameAs(order.getCustomerOrg())) {
                return false;
            }

            if (organization.isSameAs(order.getPlacedByOrg())) {
                return false;
            }

            if (order.getCustomerOrg().getBrokerOrganization() != null && order.getCustomerOrg().getBrokerOrganization().isSameAs(organization)) {
                return false;
            }

            if (log.isDebugEnabled()) {
                log.debug("notifyChiefDealer : skipping processing makerorders. customerOrg = " + order.getCustomerOrg().getShortName() + " : placedByOrg = " + order.getPlacedByOrg().getShortName() + " : userOrgName = " + organization.getShortName()
                        + " : customerOrg.getBrokerOrganization() = " + order.getCustomerOrg().getBrokerOrganization() + " for tid = " + order.get_id());
            }
            return true;
        }
        return false;
    }

    private void populateNotificationProperties(Map<String, String> notificationProperties, Map dataMap)
    {
        if (notificationProperties != null) {
            dataMap.putAll(notificationProperties);
        }
    }

    protected void persistClientAlertMessage(SingleLegOrder orderRequest, Order order, String message) {
        _persistClientAlertMessage(orderRequest, order, message);
    }

	private void _persistClientAlertMessage(SingleLegOrder orderRequest, Order order, String message) {
		if((orderRequest != null) && (order != null) && (message != null)) {
			String dateString = order.getTradeDate().getFormattedDate(IdcDate.YYYY_MM_DD_HYPHEN);
			ClientAlertPersistenceUtil.getInstance().processClientMessageAlert(dateString, message, orderRequest.getUser());
		}
	}

    public void notifyChiefDealer( Deal deal, Organization organization, ApplicationEventCodes ase )
    {
        Map dataMap = TLdataMap.get();
        dataMap.clear();
        dataMap.put("workflowMessageId", System.currentTimeMillis());
        ClientResponseBuilder.getInstance().updateData(deal, dataMap, true);
        String message = null;
        Map jmsProps = this.jmsProps;
        boolean notifyDealer =false;
        switch ( ase ) {
            case EVENT_CD_TRADE_VERIFY:
            case EVENT_ESP_CD_MAKER_TRADE_VERIFY:
        	    jmsProps = new HashMap<String,String>(this.jmsProps);
        	    jmsProps.put(ISCommonConstants.KEY_CD_MESSAGE_TYPE, "dt");
        	    jmsProps.put(ISCommonConstants.KEY_CD_FI_INDEX,String.valueOf(deal.getCounterpartyAOrg().getIndex()));
        	    jmsProps.put(ISCommonConstants.KEY_CD_LP_INDEX,String.valueOf(deal.getCounterpartyBOrg().getIndex()));
        	    message = ClientDataQueryHelper.getInstance().getVerificationMessage( dataMap, new StringBuilder( 1000 ) );
        	    break;
            case EVENT_CD_TRADE_AMEND:
        	    message = ClientDataQueryHelper.getInstance().getAmendTradeMessage(dataMap, new StringBuilder( 1000 ) , null);
                notifyDealer = shouldNotifyDealer(deal);
                break;
            case EVENT_CD_TRADE_CANCEL:
                message = ClientDataQueryHelper.getInstance().getCancellationMessage(dataMap, new StringBuilder( 1000 ) );
                notifyDealer = shouldNotifyDealer(deal);
                break;
            case EVENT_CD_TRADE_NETTED:
                message = ClientDataQueryHelper.getInstance().getCancellationMessage(dataMap, new StringBuilder( 1000 ) );
                notifyDealer = shouldNotifyDealer(deal);
                break;
            case EVENT_CD_TRADE_NET:
                dataMap.put(ISConstantsC.DEAL_NET,ISConstantsC.IS_DEAL_NET);
                message = ClientDataQueryHelper.getInstance().getVerificationMessage(dataMap, new StringBuilder( 1000 ) );
                notifyDealer = shouldNotifyDealer(deal);
                break;
            case EVENT_CD_TRADE_REJECT:
                message = ClientDataQueryHelper.getInstance().getRejectionMessage(dataMap, new StringBuilder( 1000 ) );
                break;
            case EVENT_CD_POST_TRADE_UPDATE:
        	    message = ClientDataQueryHelper.getInstance().getPostTradeUpdateMessage(dataMap, new StringBuilder( 1000 ) , null);
                notifyDealer = shouldNotifyDealer(deal);
                break;
        }

        if(notifyDealer)
        {
            log.info("notifyChiefDealer : Notifying dealer trade message=" + message + ", ase=" + ase + ",user=" +
                        deal.getUser().getShortName() + " , organization=" + organization.getShortName());
            sendMessageToDealer(message,jmsProps,deal.getUser());

        }
        log.info("notifyChiefDealer : Notifying chief dealer trade message=" + message + ", ase=" + ase+" , organization=" + organization.getShortName());
        sendMessageToUserPermission(message, jmsProps, organization, ISConstantsC.CHIEFDEALER_MPVIEW_PERM);
        sendMessageToUserPermission(message, jmsProps, organization, ISConstantsC.CHIEFDEALER_DBVIEW_PERM);
    }

    private boolean shouldNotifyDealer(Deal deal) {
        boolean isChiefDealer= deal.getUser().hasPermission(ISConstantsC.CHIEFDEALER_MPVIEW_PERM) ||
                                deal.getUser().hasPermission(ISConstantsC.CHIEFDEALER_MPVIEW_PERM);
        return !isChiefDealer ;
    }

    private boolean shouldNotifyDealer(Order order) {
        boolean isChiefDealer= order.getUser().hasPermission(ISConstantsC.CHIEFDEALER_MPVIEW_PERM) ||
                order.getUser().hasPermission(ISConstantsC.CHIEFDEALER_MPVIEW_PERM);
        return !isChiefDealer ;
    }

    protected void sendMessageToDealer( String messageContents, Map messageProps, User user ) {
        _sendMessageToDealer(messageContents, messageProps, user);
    }

    private void _sendMessageToDealer( String messageContents, Map messageProps, User user ) {
        try {
            List<String> userList = new ArrayList<String>();
            userList.add(user.getFullName());
            NotificationMessageSender.sendJMSMessageToSpecificUsers(messageContents, messageProps, userList);
        }
        catch ( Exception e ) {
            log.error( "sendMessageToUserPermission : Error in sending notification " + e );
            e.printStackTrace();
        }
    }


    protected void sendMessageToUserPermission( String messageContents, Map messageProps, Organization org, String permission){
        _sendMessageToUserPermission( messageContents, messageProps, org, permission );
    }

    private void _sendMessageToUserPermission( String messageContents, Map messageProps, Organization org, String permission ) {
        try {
            NotificationMessageSender.sendMessageToUserPermission(messageContents, messageProps, org, permission);
        }
        catch ( Exception e ) {
            log.error( "sendMessageToUserPermission : Error in sending notification " + e );
            e.printStackTrace();
        }
    }

}
