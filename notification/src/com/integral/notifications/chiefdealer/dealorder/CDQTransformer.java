package com.integral.notifications.chiefdealer.dealorder;

import com.integral.is.common.util.TradeUtil;
import com.integral.model.dealing.MatchEventPQ;
import com.integral.model.dealing.SingleLegOrderMatch;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.HashSet;

/**
 * User: verma
 * Date: 10/15/13
 * Time: 2:57 PM
 */
public abstract class CDQTransformer {

    protected Collection<CDViewManager.CDView> createDealViews(SingleLegTrade trade){
        switch (trade.getState().getName()) {
            case TSFAILED:
                return null;
        }

        //Taker Views

        HashSet<CDViewManager.CDView> cdViews = new HashSet<CDViewManager.CDView>(4);
        if (trade.getLegalEntity() != null) {
            cdViews.add(new CDViewManager.CDView(CDViewManager.CDView.Type.TAKER, trade.getLegalEntity().getOrganization()));
            if (!trade.getLegalEntity().getNamespace().isSameAs(trade.getUser().getNamespace())) {
                //Add Deal for SalesDealer
                cdViews.add(new CDViewManager.CDView(CDViewManager.CDView.Type.TAKER, trade.getUser().getOrganization()));
            }
        }


        //Maker Views
        if ( trade.isMaker() ) {
            return cdViews;
        }
        else {
            switch (trade.getState().getName()) {
                case TSREJECTED:
                case TSINIT:
                    return cdViews;
                //SPAC-930 : Do not process any REJECT trades or trades in INIT stage for MAKER view
            }
            //TradingParty will be null for Intra-Floor Trades. For then the MAKER Trade generates the MAKER view.
            if ( trade.getTradingParty() != null ) {
                SingleLegOrderMatch match = trade.getMatchEvent();
                Organization makerOrg = trade.getTradingParty().getLegalEntityOrganization();
                if(match.isDirectedOrder()){
                    if( TradeUtil.isDirectedOrderTradeNonBilateralNonBetweenCMs( trade )){
                        cdViews.add(new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER, makerOrg));
                    }
                }else if ( match.isMultiPrimeBrokerCoverEnabled() || match.isPrimeBrokerCoverEnabled() ) {
                     cdViews.add(new CDViewManager.CDView( CDViewManager.CDView.Type.MAKER, makerOrg ) );
                 }
                else if ( makerOrg.isBroker()) {
                	//EN-932 [PROBLEM] - Displayed order deals appearing twice
                	//maker view is already created for broker for DO
                    //SPAC-1607  matchedQuote can be null in netting
                    if(match.getMatchedQuote()!=null &&  match.getMatchedQuote().getType() == MatchEventPQ.QuoteDescriptor.Type.ORDER) {
                        //do nothing
                    }
                    else{
                        cdViews.add(new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER, makerOrg));
                    }

                 }
            }
        }
        return cdViews;
    }
}
