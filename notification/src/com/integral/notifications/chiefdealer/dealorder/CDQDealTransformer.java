package com.integral.notifications.chiefdealer.dealorder;


import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.Currency;
import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.facade.DealStateFacade;
import com.integral.finance.dealing.fx.FXDealLeg;
import com.integral.finance.dealing.fx.FXSingleLegDeal;
import com.integral.finance.dealing.fx.FXSingleLegOrder;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.mbean.ClientConfMBean;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.TradeUtil;
import com.integral.is.finance.currency.CurrencyPairUtil;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.*;
import com.integral.notifications.util.SpacesOrderFacadeUtil;
import com.integral.persistence.Namespace;
import com.integral.spaces.ApplicationSpaceEvent;
import com.integral.spaces.notification.NHResponse;
import com.integral.spaces.notification.NHResponseCode;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.Collection;
/**
 * User: biswa
 * Date: 6/26/13
 * Time: 3:07 PM
 */

/**
 * This class is responsible for following actions -
 * Given a SingleLegTrade object -
 * 1. transforms to corresponding FXSingleLegDeal or queries it.
 * 2. performs corresponding state transition on FXSingleLegDeal.
 * 3. persists deal in spaces.
 * 4. sends chief dealer notifications
 */

public class CDQDealTransformer extends CDQTransformer{
    private static final Log log = LogFactory.getLog( CDQDealTransformer.class );
    protected static SpacesOrderFacade of = new SpacesOrderFacadeC();

    protected static SpacesDealFacade df = new SpacesDealFacadeC();
    public static final String DEAL_CREATE_EVENT_NAME = "dealCreate";
    public static final String DEAL_PRE_RATE_VERIFIED_EVENT_NAME = "dealPreRateVerified";
    public static final String DEAL_VERIFIED_EVENT_NAME = "dealVerified";
    public static final String DEAL_REJECTED_EVENT_NAME = "dealRejected";
    private static final String DEAL_CANCELLED_EVENT_NAME = "dealCancelled";
    private static final String DEAL_NETTED_EVENT_NAME = "dealNetted";
    private static final String DEAL_NET_EVENT_NAME = "dealNet";
    private static final String DEAL_AMENDED_EVENT_NAME = "dealAmended";
    public static final String DEAL_UPDATE_EVENT_NAME = "dealUpdate";
    private ClientConfMBean clientMBean = ISFactory.getInstance().getClientConfMBean();
    private CDQDealExpirableCache CDQDealExpirableCache;
    private CDQOrderExpirableCache CDQOrderExpirableCache;
    protected IChiefDealerNotifier cdNotifier = null;


    public static enum DealTransformAction {
        DEAL_CREATE_EVENT,
        DEAL_VERIFIED_EVENT,
        DEAL_REJECTED_EVENT,
        DEAL_CANCELLED_EVENT,
        DEAL_NETTED_EVENT,
        DEAL_NET_EVENT,
        DEAL_AMENDED_EVENT,
        DEAL_UPDATE_EVENT
        }
    
    public CDQDealTransformer( CDQDealExpirableCache CDQDealExpirableCache, CDQOrderExpirableCache CDQOrderExpirableCache, IChiefDealerNotifier chiefDealerNotifier) {
        this.CDQDealExpirableCache = CDQDealExpirableCache;
        this.CDQOrderExpirableCache = CDQOrderExpirableCache;
        this.cdNotifier = chiefDealerNotifier;
    }

    /**
     * create FXSingleLegDeal on trade acceptence and sends notification message
     *
     * @param trade Associated SingleLegTrade
     */
    public NHResponse handleTradeAccept( SingleLegTrade trade) {
        return handleTradeAccept(trade,false);
    }

    /**
     * create FXSingleLegDeal on trade acceptence and sends notification message
     *
     * @param trade Associated SingleLegTrade
     * @param replayed if this notification has been replayed
     */
    public NHResponse handleTradePreRateVerified( SingleLegTrade trade,boolean replayed ) {
        log.info( DEAL_PRE_RATE_VERIFIED_EVENT_NAME + " TransactionID:" + trade.get_id() );
        SingleLegOrder orderRequest = trade.getOrderRequest();
        Collection<CDViewManager.CDView> views = createDealViews(trade);
        boolean error = false;
        if ( views != null && !views.isEmpty() ) {
            for ( CDViewManager.CDView view : views ) {
                try {
                    log.info( "handleTradePreRateVerified : Processing Trade=" + trade.get_id() + ", view=" + view );
                    FXSingleLegDeal deal = null;
                    if (replayed) {
                        boolean shouldReplayNotification = shouldProcessDealReplay(trade, DealTransformAction.DEAL_CREATE_EVENT, view);
                        if (!shouldReplayNotification) {
                            continue;
                        }
                    }
                    switch ( view.getType() ) {
                        case TAKER:
                            deal = df.dealCreatePreRate( trade, DEAL_CREATE_EVENT_NAME );
                            deal.set_id( generateDealId(trade,view));
                            deal.setNamespace( view.getOrganization().getNamespace() );
                            deal.setNamespaceName( view.getOrganization().getNamespace().getShortName() );
                            //This needs to be done only for Taker view
                            deal.setOrderBasedNettingEnabled( trade.getMatchEvent().isNettingRequired() );
                            deal.setNettingOnCalculatedResponseEnabled(orderRequest.isNettingOnCalculatedResponseEnabled());
                            deal.setShowPreNettingFills( orderRequest.isShowFills() );
                            df.updateDealAttributesForView(deal,trade,view);
                            df.updateDealAttributesForOcx2(deal,trade,view);
                            break;
                        case MAKER:
                            if(log.isDebugEnabled()){
                                log.info("handleTradeAccept : Skipping Trade=" + trade.get_id() + ", view=" + view);
                            }
                            continue;
                    }
                    if ( deal != null ) {
                        ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_TRADE_ACCEPT );
                        PersistenceServiceFactory.getCDQPersistenceService().synchronousPersist(ase, trade.getCorrelationId(), trade.isWarmUpObject(), DEAL_PRE_RATE_VERIFIED_EVENT_NAME);
                        CDQDealExpirableCache.add( deal );
                    }
                }
                catch ( Exception ex ) {
                    log.info( "handleTradeAccept : CDViewProcess Could not process View=" + view + " for Trade=" + trade.get_id(),ex );
                    error = true;
                }
            }
        }
        else {
            if(!trade.getState().getName().equals(State.Name.TSFAILED)){
                error = true;
                log.info( "handleTradeAccept : CDViewProcess No CDView found for Trade=" + trade.get_id() + ". Deal not created/updated in CDQ" );
            }
        }

        if(error){
            return new NHResponse(NHResponseCode.FAILURE);
        }else{
            return new NHResponse(NHResponseCode.SUCCESS);
        }
    }

    /**
     * create FXSingleLegDeal on trade acceptence and sends notification message
     *
     * @param trade Associated SingleLegTrade
     * @param replayed if this notification has been replayed
     */
    public NHResponse handleTradeAccept( SingleLegTrade trade,boolean replayed ) {
        log.info( DEAL_CREATE_EVENT_NAME + " TransactionID:" + trade.get_id() );
        SingleLegOrder orderRequest = trade.getOrderRequest();
        Collection<CDViewManager.CDView> views = createDealViews(trade);
        boolean error = false;
        if ( views != null && !views.isEmpty() ) {
            for ( CDViewManager.CDView view : views ) {
                try {
                    log.info( "handleTradeAccept : Processing Trade=" + trade.get_id() + ", view=" + view );
                    FXSingleLegDeal deal = null;
                    if (replayed) {
                        boolean shouldReplayNotification = shouldProcessDealReplay(trade, DealTransformAction.DEAL_CREATE_EVENT, view);
                        if (!shouldReplayNotification) {
                            continue;
                        }
                    }
                    switch ( view.getType() ) {
                        case TAKER:
                            deal = df.dealCreate( trade, DEAL_CREATE_EVENT_NAME );
                            deal.set_id( generateDealId(trade,view));
                            deal.setNamespace( view.getOrganization().getNamespace() );
                            deal.setNamespaceName( view.getOrganization().getNamespace().getShortName() );
                            //This needs to be done only for Taker view
                            deal.setOrderBasedNettingEnabled( trade.getMatchEvent().isNettingRequired() );
                            deal.setNettingOnCalculatedResponseEnabled(orderRequest.isNettingOnCalculatedResponseEnabled());
                            deal.setShowPreNettingFills( orderRequest.isShowFills() );
                            df.updateDealAttributesForOcx2(deal,trade,view);
                            break;
                        case MAKER:
                            if(log.isDebugEnabled()){
                                log.info("handleTradeAccept : Skipping Trade=" + trade.get_id() + ", view=" + view);
                            }
                            continue;
                        }
                    if ( deal != null ) {
                        ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_TRADE_ACCEPT );
                        PersistenceServiceFactory.getCDQPersistenceService().synchronousPersist(ase, trade.getCorrelationId(), trade.isWarmUpObject(), DEAL_CREATE_EVENT_NAME);
                        CDQDealExpirableCache.add( deal );
                    }
                }
                catch ( Exception ex ) {
                    log.info( "handleTradeAccept : CDViewProcess Could not process View=" + view + " for Trade=" + trade.get_id(),ex );
                    error = true;
                }
            }
        }
        else {
            if(!trade.getState().getName().equals(State.Name.TSFAILED)){
                error = true;
                log.info( "handleTradeAccept : CDViewProcess No CDView found for Trade=" + trade.get_id() + ". Deal not created/updated in CDQ" );
            }
        }

        if(error){
            return new NHResponse(NHResponseCode.FAILURE);
        }else{
            return new NHResponse(NHResponseCode.SUCCESS);
        }
    }

    private String generateDealId( SingleLegTrade trade, CDViewManager.CDView view ) {
        StringBuilder sb = new StringBuilder( 100 );
        sb.append( trade.get_id() ).append( '_' ).append( view.getOrganization().getShortName() );
        return sb.toString();
    }

    /**
     * updates FXSingleLegDeal on trade amend and sends notification message
     *
     * @param trade Associated SingleLegTrade
     */
    public NHResponse handleTradeAmended( SingleLegTrade trade) {
        return handleTradeAmended(trade,false);
    }


        /**
         * updates FXSingleLegDeal on trade amend and sends notification message
         *
         * @param trade Associated SingleLegTrade
         * @param replayed if this notification has been replayed
         */
    public NHResponse handleTradeAmended( SingleLegTrade trade,boolean replayed ) {

        log.info( DEAL_AMENDED_EVENT_NAME + " TransactionID:" + trade.get_id() );
        SingleLegOrder orderRequest = trade.getOrderRequest();
        Collection<CDViewManager.CDView> views = createDealViews(trade);
        String dealTransactionId = getDealTransactionId(trade);
        boolean error = false;
        if ( views != null && !views.isEmpty() ) {
            for ( CDViewManager.CDView view : views ) {
                try {
                    log.info( "handleTradeAmended : Processing Trade=" + trade.get_id() + ", view=" + view );
                    if (replayed) {
                        boolean shouldReplayNotification = shouldProcessDealReplay(trade, DealTransformAction.DEAL_AMENDED_EVENT, view);
                        if (!shouldReplayNotification) {
                            continue;
                        }
                    }
                    Deal deal = null;
                    switch ( view.getType() ) {
                        case TAKER:
                             deal = CDQDealExpirableCache.get( dealTransactionId, orderRequest.get_id(), view.getOrganization().getNamespace().getShortName() );
                            if ( deal != null ) {
                                df.dealAmended(trade, deal, DEAL_AMENDED_EVENT_NAME );
                                df.updateDealAttributesForOcx2(deal,trade,view);
                                deal.setNamespace( view.getOrganization().getNamespace() );
                                deal.setNamespaceName( view.getOrganization().getNamespace().getShortName() );
                            }
                            break;
                        case MAKER:
                            deal = CDQDealExpirableCache.get( dealTransactionId, orderRequest.get_id(), view.getOrganization().getNamespace().getShortName() );
                            if ( deal != null ) {
                                df.dealAmended( trade, deal, DEAL_AMENDED_EVENT_NAME );
                                df.updateDealAttributesForOcx2(deal,trade,view);
                                /*
                                    Flip for Maker
                                 */
                                LegalEntity cptyA = deal.getCounterpartyA();
                                LegalEntity cptyB = deal.getCounterpartyB();
                                deal.setCounterpartyB( cptyA );
                                deal.setCounterpartyA( cptyB );
                            }
                            break;
                    }

                    if ( deal != null ) {
                        ApplicationSpaceEvent ase2 = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_TRADE_AMEND );
                        PersistenceServiceFactory.getCDQPersistenceService().synchronousPersist(ase2, trade.getCorrelationId(), trade.isWarmUpObject(), DEAL_AMENDED_EVENT_NAME);
                          switch ( view.getType() ) {
                            case TAKER:
                                if ( orderRequest.isNettingEnabled() ) {
                                    if ( orderRequest.isShowFills() ) {
                                        cdNotifier.notifyChiefDealer( deal,view.getOrganization(), ApplicationEventCodes.EVENT_CD_TRADE_AMEND );
                                    }
                                }
                                else {
                                    cdNotifier.notifyChiefDealer( deal,view.getOrganization(), ApplicationEventCodes.EVENT_CD_TRADE_AMEND );
                                }
                                break;
                            case MAKER:
                                cdNotifier.notifyChiefDealer( deal,view.getOrganization(), ApplicationEventCodes.EVENT_CD_TRADE_AMEND );
                                break;
                        }
                    }
               
                }
                catch ( Exception ex ) {
                    error = true;
                    log.info( "handleTradeAmended : CDViewProcess Could not process View=" + view + " for Trade=" + trade.get_id() );
                }
            }
        }
        else {
            error = true;
            log.info( "handleTradeAmended : CDViewProcess No CDView found for Trade=" + trade.get_id() + ". Deal not created/updated in CDQ" );
        }

        if(error){
            return new NHResponse(NHResponseCode.FAILURE);
        }else{
            return new NHResponse(NHResponseCode.SUCCESS);
        }
    }


    /**
     * updates FXSingleLegDeal on trade verification and sends notification message
     *
     * @param trade Associated SingleLegTrade
     */
    public NHResponse handleTradeVerified( SingleLegTrade trade) {
        return handleTradeVerified(trade,false);
    }

        /**
         * updates FXSingleLegDeal on trade verification and sends notification message
         *
         * @param trade Associated SingleLegTrade
         * @param replayed if this notification has been replayed
         */
    public NHResponse handleTradeVerified( SingleLegTrade trade ,boolean replayed) {
        log.info( DEAL_VERIFIED_EVENT_NAME + " TransactionID:" + trade.get_id() );
        SingleLegOrder orderRequest = trade.getOrderRequest();
        CDViewManager cdViewManager = CDViewService.getCDViewManager( orderRequest );
        processMakerOrderViews(cdViewManager,trade);
        Collection<CDViewManager.CDView> views = createDealViews(trade);
        String dealTransactionId = getDealTransactionId(trade);
        boolean error = false ;
        if ( views != null && !views.isEmpty() ) {
            for ( CDViewManager.CDView view : views ) {
                try {
                    log.info( "handleTradeVerified : Processing Trade=" + trade.get_id() + ", view=" + view );
                    Deal deal = null;
                    if (replayed) {
                        boolean shouldReplayNotification = shouldProcessDealReplay(trade, DealTransformAction.DEAL_VERIFIED_EVENT, view);
                        if (!shouldReplayNotification) {
                            continue;
                        }
                    }
                    switch ( view.getType() ) {
                        case TAKER:
                            deal = CDQDealExpirableCache.get( dealTransactionId, orderRequest.get_id(), view.getOrganization().getNamespace().getShortName() );
                            if ( deal != null ) {
                                df.dealVerified( trade, deal, DEAL_VERIFIED_EVENT_NAME );
                                df.updateDealAttributesForView(deal,trade,view);
                                df.updateDealAttributesForOcx2(deal,trade,view);
                                deal.setNamespace( view.getOrganization().getNamespace() );
                                deal.setNamespaceName( view.getOrganization().getNamespace().getShortName() );

                                ApplicationSpaceEvent ase2 = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_TRADE_VERIFY );
                                PersistenceServiceFactory.getCDQPersistenceService().synchronousPersist(ase2, trade.getCorrelationId(), trade.isWarmUpObject(), DEAL_VERIFIED_EVENT_NAME);
                            }
                            break;
                        case MAKER:
                                deal = df.dealCreate( trade, DEAL_CREATE_EVENT_NAME );
                                deal.set_id( generateDealId(trade,view));
                                deal = df.dealVerified(trade, deal, DEAL_VERIFIED_EVENT_NAME);
                                df.updateDealAttributesForView(deal,trade,view);
                                df.updateDealAttributesForOcx2(deal,trade,view);
                                flipMakerAttributes( (FXSingleLegDeal)deal, view.getOrganization(), trade.getMakerUser(), trade );


                            ApplicationSpaceEvent ase2 = PersistenceServiceFactory.getCDQPersistenceService().createEvent(deal, ApplicationEventCodes.EVENT_ESP_CD_MAKER_TRADE_VERIFY);
                            PersistenceServiceFactory.getCDQPersistenceService().synchronousPersist(ase2, trade.getCorrelationId(), trade.isWarmUpObject(), DEAL_VERIFIED_EVENT_NAME);


                            break;
                    }
                    
                    if( trade.isBbookTrade() )
                    {
                    	continue;
                    }

                    if ( deal != null ) {
                        switch ( view.getType() ) {
                            case TAKER:
                                if ( orderRequest.isNettingEnabled() ) {
                                    if ( !trade.getMatchEvent().isNettingRequired() ) {
                                        cdNotifier.notifyChiefDealer( deal,view.getOrganization(), ApplicationEventCodes.EVENT_CD_TRADE_VERIFY );
                                    }else if(orderRequest.isNettingOnCalculatedResponseEnabled() && orderRequest.isShowFills()){
                                        cdNotifier.notifyChiefDealer( deal,view.getOrganization(), ApplicationEventCodes.EVENT_CD_TRADE_VERIFY );
                                    } else {
                                        log.info("handleTradeVerified : Verified trade that will be netted. Taker ChiefDealer not notified for Trade=" + trade.get_id());
                                    }
                                }
                                else {
                                    cdNotifier.notifyChiefDealer( deal,view.getOrganization(), ApplicationEventCodes.EVENT_CD_TRADE_VERIFY );
                                }
                                break;
                            case MAKER:
                                cdNotifier.notifyChiefDealer( deal,view.getOrganization(), ApplicationEventCodes.EVENT_ESP_CD_MAKER_TRADE_VERIFY);
                                break;
                        }
                    }
                }
                catch ( Exception ex ) {
                    log.info( "handleTradeVerified : CDViewProcess could not process View=" + view + " for Trade=" + trade.get_id(),ex );
                    error = true;
                }
            }
        }
        else {
            error = true;
            log.info( "handleTradeVerified : CDViewProcess No CDView found for Trade=" + trade.get_id() + ". Deal not created/updated in CDQ" );
        }

        if(error){
            return new NHResponse(NHResponseCode.FAILURE);
        }else{
            return new NHResponse(NHResponseCode.SUCCESS);
        }
    }
    
    private String getDealTransactionId( SingleLegTrade trade ) {
        if( trade.isMaker() ){
            return trade.getExternalReferenceId();
        }
        else{
            return trade.get_id();
        }
    }
    
    
    public NHResponse handlePostTradeUpdate( SingleLegTrade trade,boolean replayed ) {
        log.info( DEAL_UPDATE_EVENT_NAME + " TransactionID:" + trade.get_id() );
        SingleLegOrder orderRequest = trade.getOrderRequest();
        Collection<CDViewManager.CDView> views = createDealViews(trade);
        String dealTransactionId = getDealTransactionId(trade);
        boolean error = false;
        if ( views != null && !views.isEmpty() ) {
            for ( CDViewManager.CDView view : views ) {
                try {
                    log.info( "handlePostTradeUpdate : Processing Trade=" + trade.get_id() + ", view=" + view );
                    if (replayed) {
                        boolean shouldReplayNotification = shouldProcessDealReplay(trade, DealTransformAction.DEAL_UPDATE_EVENT, view);
                        if (!shouldReplayNotification) {
                            continue;
                        }
                    }
                    Deal deal = null;
                    switch ( view.getType() ) {
                        case TAKER:
                             deal = CDQDealExpirableCache.get( dealTransactionId, orderRequest.get_id(), view.getOrganization().getNamespace().getShortName() );
                            if ( deal != null ) {
                            	deal.setBackOfficeID(trade.getBackOfficeID());
                                deal.setNamespace( view.getOrganization().getNamespace() );
                                deal.setNamespaceName( view.getOrganization().getNamespace().getShortName() );
                            }
                            break;
                        case MAKER:
                            deal = CDQDealExpirableCache.get( dealTransactionId, orderRequest.get_id(), view.getOrganization().getNamespace().getShortName() );
                            if ( deal != null ) {
                            	deal.setBackOfficeID(trade.getBackOfficeID());
                            }
                            break;
                    }

                    if ( deal != null ) {
                        ApplicationSpaceEvent ase2 = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_POST_TRADE_UPDATE );
                        PersistenceServiceFactory.getCDQPersistenceService().synchronousPersist(ase2, trade.getCorrelationId(), trade.isWarmUpObject(), DEAL_UPDATE_EVENT_NAME);
                          switch ( view.getType() ) {
                            case TAKER:
                                if ( orderRequest.isNettingEnabled() ) {
                                    if ( orderRequest.isShowFills() ) {
                                        cdNotifier.notifyChiefDealer( deal,view.getOrganization(), ApplicationEventCodes.EVENT_CD_POST_TRADE_UPDATE );
                                    }
                                }
                                else {
                                    cdNotifier.notifyChiefDealer( deal,view.getOrganization(), ApplicationEventCodes.EVENT_CD_POST_TRADE_UPDATE );
                                }
                                break;
                            case MAKER:
                                cdNotifier.notifyChiefDealer( deal,view.getOrganization(), ApplicationEventCodes.EVENT_CD_POST_TRADE_UPDATE );
                                break;
                        }
                    }
               
                }
                catch ( Exception ex ) {
                    error = true;
                    log.info( "handlePostTradeUpdate : CDViewProcess Could not process View=" + view + " for Trade=" + trade.get_id() );
                }
            }
        }
        else {
            error = true;
            log.info( "handlePostTradeUpdate : CDViewProcess No CDView found for Trade=" + trade.get_id() + ". Deal not created/updated in CDQ" );
        }

        if(error){
            return new NHResponse(NHResponseCode.FAILURE);
        }else{
            return new NHResponse(NHResponseCode.SUCCESS);
        }
    }

    //dealRejected

    /**
     * updates FXSingleLegDeal on trade verification and sends notification message
     *
     * @param trade Associated SingleLegTrade
     */
    public NHResponse handleTradeRejected( SingleLegTrade trade) {
        return handleTradeRejected(trade,false);
    }
        /**
         * updates FXSingleLegDeal on trade verification and sends notification message
         *
         * @param trade Associated SingleLegTrade
         * @param replayed if this notification has been replayed
         */
    public NHResponse handleTradeRejected( SingleLegTrade trade ,boolean replayed) {
        log.info( DEAL_REJECTED_EVENT_NAME + " TransactionID:" + trade.get_id() );
        SingleLegOrder orderRequest = trade.getOrderRequest();
        Collection<CDViewManager.CDView> views = createDealViews(trade);
        String dealTransactionId = getDealTransactionId(trade);
        boolean error = false;
        if ( views != null && !views.isEmpty() ) {
            for ( CDViewManager.CDView view : views ) {
                try {
                    log.info( "handleTradeRejected : Processing Trade=" + trade.get_id() + ", view=" + view );
                    Deal deal = null;

                    if (replayed) {
                        boolean shouldReplayNotification = shouldProcessDealReplay(trade, DealTransformAction.DEAL_REJECTED_EVENT, view);
                        if (!shouldReplayNotification) {
                            continue;
                        }
                    }

                    switch ( view.getType() ) {
                        case TAKER:
                            deal = CDQDealExpirableCache.get( dealTransactionId, orderRequest.get_id(), view.getOrganization().getNamespace().getShortName() );
                            if ( deal != null ) {
                                df.dealRejected( trade, deal, DEAL_REJECTED_EVENT_NAME );
                                df.updateDealAttributesForView(deal,trade,view);
                                df.updateDealAttributesForOcx2(deal,trade,view);
                                deal.setNamespace( view.getOrganization().getNamespace() );
                                deal.setNamespaceName( view.getOrganization().getNamespace().getShortName() );
                            }
                            break;
                        case MAKER:
                            deal = CDQDealExpirableCache.get( dealTransactionId, orderRequest.get_id(), view.getOrganization().getNamespace().getShortName() );
                            if ( deal != null ) {
                                df.dealRejected( trade, deal, DEAL_REJECTED_EVENT_NAME );
                                df.updateDealAttributesForView(deal,trade,view);
                                df.updateDealAttributesForOcx2(deal,trade,view);
                                LegalEntity cptyA = deal.getCounterpartyA();
                                LegalEntity cptyB = deal.getCounterpartyB();
                                deal.setCounterpartyB( cptyA);
                                deal.setCounterpartyA( cptyB );
                                deal.setCounterpartyATradeId( trade.getExternalReferenceId() );
                                deal.setCounterpartyBTradeId( trade.get_id() );
                                deal.setUser( trade.getMakerUser() );
                            }
                            break;
                    }
                    if ( deal != null ) {
                        ApplicationSpaceEvent ase2 = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_TRADE_REJECT );
                        PersistenceServiceFactory.getCDQPersistenceService().synchronousPersist(ase2, trade.getCorrelationId(), trade.isWarmUpObject(), DEAL_REJECTED_EVENT_NAME);
                        if ( clientMBean.showRejections( deal.getNamespaceName() ) ) {
                            cdNotifier.notifyChiefDealer( deal, view.getOrganization(), ApplicationEventCodes.EVENT_CD_TRADE_REJECT );
                        }
                    }
                }
                catch ( Exception ex ) {
                    log.info( "handleTradeRejected : CDViewProcess could not process View=" + view + " for Trade=" + trade.get_id() );
                    error = true;
                }
            }
        }
        else {
            if(!trade.getState().getName().equals(State.Name.TSFAILED)){
                error = true;
                log.info( "handleTradeRejected : CDViewProcess  No CDView found for Trade=" + trade.get_id() + ". Deal not created/updated in CDQ" );
            }
        }
        if(error){
            return new NHResponse(NHResponseCode.FAILURE);
        }else{
            return new NHResponse(NHResponseCode.SUCCESS);
        }
    }

    /**
     * updates FXSingleLegDeal on trade cancellation and sends notification message
     *
     * @param trade Associated SingleLegTrade
     */
    public NHResponse handleTradeCancelled(SingleLegTrade trade){
       return handleTradeCancelled(trade,false);
    }

    /**
     * updates FXSingleLegDeal on trade cancellation and sends notification message
     *
     * @param trade Associated SingleLegTrade
     * @param replayed if this notification has been replayed
     */
    public NHResponse handleTradeCancelled( SingleLegTrade trade,boolean replayed ) {
        log.info( DEAL_CANCELLED_EVENT_NAME + " TransactionID:" + trade.get_id() );
        SingleLegOrder orderRequest = trade.getOrderRequest();
        Collection<CDViewManager.CDView> views = createDealViews(trade);
        String dealTransactionId = getDealTransactionId(trade);
        boolean error = false;
        if ( views != null && !views.isEmpty() ) {
            for (CDViewManager.CDView view : views) {
                try {
                    log.info( "handleTradeCancelled : Processing Trade=" + trade.get_id() + ", view=" + view );
                    if (replayed) {
                        boolean shouldReplayNotification = shouldProcessDealReplay(trade, DealTransformAction.DEAL_CANCELLED_EVENT, view);
                        if (!shouldReplayNotification) {
                            continue;
                        }
                    }
                    Deal deal = CDQDealExpirableCache.get( dealTransactionId, orderRequest.get_id(), view.getOrganization().getNamespace().getShortName() );
                    if ( deal != null ) {
                        df.dealCancelled( trade, deal, DEAL_CANCELLED_EVENT_NAME );
                        df.updateDealAttributesForOcx2(deal,trade,view);
                        deal.setNamespace( view.getOrganization().getNamespace() );
                        deal.setNamespaceName( view.getOrganization().getNamespace().getShortName() );
                        ApplicationSpaceEvent ase2 = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_TRADE_CANCEL );
                        PersistenceServiceFactory.getCDQPersistenceService().synchronousPersist(ase2, trade.getCorrelationId(), trade.isWarmUpObject(), DEAL_CANCELLED_EVENT_NAME);
                        cdNotifier.notifyChiefDealer( deal, view.getOrganization(), ApplicationEventCodes.EVENT_CD_TRADE_CANCEL );
                    }
                }
                catch ( Exception ex ) {
                    error = true;
                    log.info( "handleTradeCancelled : CDViewProcess could not in processing View=" + view + " for Trade=" + trade.get_id() );
                }
            }
        }
        else {
            error = true;
            log.info( "handleTradeCancelled : CDViewProcess No CDView found for Trade=" + trade.get_id() + ". Deal not created/updated in CDQ" );
        }

        if(error){
            return new NHResponse(NHResponseCode.FAILURE);
        }else{
            return new NHResponse(NHResponseCode.SUCCESS);
        }
    }

    /**
     * updates FXSingleLegDeal on trade netted and sends notification message
     *
     * @param trade Associated SingleLegTrade
     */
    public NHResponse handleTradeNetted( SingleLegTrade trade) {
        return handleTradeNetted(trade,false);
    }

    /**
     * updates FXSingleLegDeal on trade netted and sends notification message
     *
     * @param trade Associated SingleLegTrade
     */
    public NHResponse handleTradeNetted( SingleLegTrade trade,boolean replayed ) {
        log.info( DEAL_NETTED_EVENT_NAME + " TransactionID:" + trade.get_id() );
        SingleLegOrder orderRequest = trade.getOrderRequest();
        Collection<CDViewManager.CDView> views = createDealViews(trade);
        String dealTransactionId = getDealTransactionId(trade);
        boolean error = false;
        if ( views != null && !views.isEmpty() ) {
            for ( CDViewManager.CDView view : views ) {
                try {
                    log.info( "handleTradeNetted : Processing Trade=" + trade.get_id() + ", view=" + view );
                    if (replayed) {
                        boolean shouldReplayNotification = shouldProcessDealReplay(trade, DealTransformAction.DEAL_NETTED_EVENT, view);
                        if (!shouldReplayNotification) {
                            continue;
                        }
                    }

                    Deal deal = CDQDealExpirableCache.get( dealTransactionId, orderRequest.get_id(), view.getOrganization().getNamespace().getShortName() );
                    if ( deal != null ) {
                        df.dealNetted( trade, deal, DEAL_NETTED_EVENT_NAME );
                        df.updateDealAttributesForOcx2(deal,trade,view);
                        deal.setNamespace( view.getOrganization().getNamespace() );
                        deal.setNamespaceName( view.getOrganization().getNamespace().getShortName() );
                        ApplicationSpaceEvent ase2 = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_TRADE_NETTED );
                        PersistenceServiceFactory.getCDQPersistenceService().synchronousPersist(ase2, trade.getCorrelationId(), trade.isWarmUpObject(), DEAL_NETTED_EVENT_NAME);
                        switch ( view.getType() ) {
                            case TAKER:
                                if ( orderRequest.isNettingEnabled() ) {
                                    if ( !trade.getMatchEvent().isNettingRequired() ) {
                                        cdNotifier.notifyChiefDealer( deal, view.getOrganization(), ApplicationEventCodes.EVENT_CD_TRADE_NETTED );
                                    }else if(orderRequest.isNettingOnCalculatedResponseEnabled() && orderRequest.isShowFills()){
                                        cdNotifier.notifyChiefDealer( deal, view.getOrganization(), ApplicationEventCodes.EVENT_CD_TRADE_NETTED );
                                    }else {
                                        log.info("handleTradeNetted : Taker ChiefDealer should not be notified for Netted Trade=" + trade.get_id());
                                    }
                                }
                                else {
                                    cdNotifier.notifyChiefDealer( deal, view.getOrganization(), ApplicationEventCodes.EVENT_CD_TRADE_NETTED );
                                }
                                break;
                            case MAKER:
                                cdNotifier.notifyChiefDealer( deal, view.getOrganization(), ApplicationEventCodes.EVENT_CD_TRADE_NETTED );
                                break;
                        }
                    }
                }
                catch ( Exception ex ) {
                    error = true;
                    log.info( "handleTradeNetted : CDViewProcess could not in processing View=" + view + " for Trade=" + trade.get_id() );
                }
            }
        }
        else {
            error = true;
            log.info( "handleTradeNetted : CDViewProcess No CDView found for Trade=" + trade.get_id() + ". Deal not created/updated in CDQ" );
        }

        if(error){
            return new NHResponse(NHResponseCode.FAILURE);
        }else{
            return new NHResponse(NHResponseCode.SUCCESS);
        }
    }

    /**
     * updates FXSingleLegDeal on trade net and sends notification message
     *
     * @param trade Associated SingleLegTrade
     */
    public NHResponse handleTradeNet( SingleLegTrade trade) {
        return handleTradeNet(trade,false);
    }

    /**
     * updates FXSingleLegDeal on trade net and sends notification message
     *
     * @param trade Associated SingleLegTrade
     * @param replayed
     */
    public NHResponse handleTradeNet( SingleLegTrade trade , boolean replayed) {
        log.info( DEAL_NET_EVENT_NAME + " TransactionID:" + trade.get_id() );
        Collection<CDViewManager.CDView> views = createDealViews(trade);
        boolean error = false;
        if ( views != null && !views.isEmpty() ) {
            for ( CDViewManager.CDView view : views ) {
                try {
                    log.info( "handleTradeNet : Processing Trade=" + trade.get_id() + ", view=" + view );
                    FXSingleLegDeal deal = null;
                    if (replayed) {
                        boolean shouldReplayNotification = shouldProcessDealReplay(trade, DealTransformAction.DEAL_NET_EVENT, view);
                        if (!shouldReplayNotification) {
                            continue;
                        }
                    }
                    switch ( view.getType() ) {
                        case TAKER:
                            deal = df.dealCreate( trade, DEAL_CREATE_EVENT_NAME );
                            df.dealNet( trade, deal, DEAL_NET_EVENT_NAME );
                            df.updateDealAttributesForOcx2(deal,trade,view);
                            deal.set_id( generateDealId( trade,view ) );
                            deal.setNamespace( view.getOrganization().getNamespace() );
                            deal.setNamespaceName( view.getOrganization().getNamespace().getShortName() );
                            break;
                        case MAKER:
                            deal = df.dealCreate( trade, DEAL_CREATE_EVENT_NAME );
                            df.dealNet( trade, deal, DEAL_NET_EVENT_NAME );
                            df.updateDealAttributesForOcx2(deal,trade,view);
                            deal.set_id( generateDealId( trade,view ) );
                            flipMakerAttributes( deal, view.getOrganization(), trade.getMakerUser(), trade );
                            break;
                    }
                    if ( deal != null ) {
                        ApplicationSpaceEvent ase = PersistenceServiceFactory.getCDQPersistenceService().createEvent( deal, ApplicationEventCodes.EVENT_CD_TRADE_NET );
                        PersistenceServiceFactory.getCDQPersistenceService().synchronousPersist(ase, trade.getCorrelationId(), trade.isWarmUpObject(), DEAL_NET_EVENT_NAME);
                        cdNotifier.notifyChiefDealer( deal, view.getOrganization(), ApplicationEventCodes.EVENT_CD_TRADE_NET );
                    }
                }
                catch ( Exception ex ) {
                    error = true;
                    log.info( "handleTradeNet : CDViewProcess could not processes View=" + view + " for Trade=" + trade.get_id() );
                }
            }
        }
        else {
            error = true;
            log.info( "handleTradeNet : CDViewProcess No CDView found for Trade=" + trade.get_id() + ". Deal not created/updated in CDQ" );
        }

        if(error){
            return new NHResponse(NHResponseCode.FAILURE);
        }else{
            return new NHResponse(NHResponseCode.SUCCESS);
        }
    }

    private void flipMakerAttributes( FXSingleLegDeal deal, Organization makerOrg, User makerUser, final SingleLegTrade takerTrade ) {
        Namespace ns = makerOrg.getNamespace();
        deal.setNamespace( ns );
        deal.setNamespaceName( ns.getShortName() );
        deal.setUser( makerUser );
        deal.setTaker( false );

        FXDealLeg dealLeg = deal.getFXDealLeg();
        int bidOfferMode = dealLeg.getAcceptedBidOfferMode();
        if ( bidOfferMode == DealingPrice.BID ) {
            dealLeg.setAcceptedBidOfferMode( DealingPrice.OFFER );
        }
        else if ( bidOfferMode == DealingPrice.OFFER ) {
            dealLeg.setAcceptedBidOfferMode( DealingPrice.BID );
        }
        String ALEI = deal.getCounterpartyALEI();
        String BLEI = deal.getCounterpartyBLEI();
        deal.setCounterpartyALEI( BLEI );
        deal.setCounterpartyBLEI( ALEI );

        LegalEntity cptyA = deal.getCounterpartyA();
        LegalEntity cptyB = deal.getCounterpartyB();
        if (deal.getNamespace().isSameAs(cptyA.getNamespace())) {
            deal.setBookName(takerTrade.getCptyABookName());
        } else if (deal.getNamespace().isSameAs(cptyB.getNamespace())) {
            deal.setBookName(takerTrade.getCptyBBookName());
            /*
                Synthetic cross information added only for CounterpartyB of the trade
             */
            if( takerTrade.isSyntheticCross() ){
                deal.setSyntheticCross(true);
                deal.setPrimaryBookName(takerTrade.getCptyBPrimaryBookName());
                deal.setSecondaryBookName(takerTrade.getCptyBSecondaryBookName());
                String localCcyPair = takerTrade.getLocalCurrencyPair();
                String foreignCcyPair = takerTrade.getForeignCurrencyPair();
                Currency vehicleCcy = getVehicleCurrency(localCcyPair, foreignCcyPair);
                if( vehicleCcy != null ) {
                    dealLeg.setVehicleCurrency(vehicleCcy.getName());
                }
                dealLeg.setLocalCurrencyPair(localCcyPair);
                dealLeg.setForeignCurrencyPair(foreignCcyPair);
                dealLeg.setVehicleCurrencyAmount(takerTrade.getVehicleCCYAmount());
            }
            deal.setPrimaryBookName(takerTrade.getCptyBPrimaryBookName());
            deal.setSecondaryBookName(takerTrade.getCptyBSecondaryBookName());
        }
        deal.setCounterpartyB( cptyA );
        deal.setCounterpartyA( cptyB );

        // cptyATradeId is set with deal.transactionID and cptyBTradeId is set
        String existingCptyBTradeId = deal.getCounterpartyBTradeId();
        String cptyBTradeId = takerTrade.getExternalReferenceId();
        if ( cptyBTradeId == null )
        {
            log.info( "CDT.flipMakerAttributes :: CounterpartyBTradeId is NULL for Deal " + takerTrade.get_id() );
        }
        else
        {
            deal.setCounterpartyBTradeId( cptyBTradeId );
        }
        if ( log.isDebugEnabled() )
        {
            log.debug(new StringBuilder(200).append("CDQ.flipMakerAttributes : existing cptyBtradeId=")
                    .append(existingCptyBTradeId).append(",cptyATradeId=").append(deal.getCounterpartyATradeId())
                    .append(",newCptyBTradeId=").append(cptyBTradeId).toString());
        }
    }

    protected Currency getVehicleCurrency(String localCcyPair, String foreignCcyPair) {
        if( localCcyPair == null || foreignCcyPair == null ){
            return null;
        }
        try {
            Currency foreignBaseCcy = CurrencyPairUtil.getBaseCcy(foreignCcyPair);
            Currency foreignTermCcy = CurrencyPairUtil.getTermCcy(foreignCcyPair);

            Currency localBaseCcy = CurrencyPairUtil.getBaseCcy(localCcyPair);
            Currency localTermCcy = CurrencyPairUtil.getTermCcy(localCcyPair);

            if (foreignBaseCcy.isSameAs(localBaseCcy) || foreignBaseCcy.isSameAs(localTermCcy)) {
                return foreignBaseCcy;
            } else if (foreignTermCcy.isSameAs(localBaseCcy) || foreignTermCcy.isSameAs(localTermCcy)) {
                return foreignTermCcy;
            }
        }
        catch (Exception ex){
            log.warn("getVehicleCurrency : Failed to get vehicle currency for localCcyPair="+localCcyPair+", foreignCcyPair="+foreignCcyPair);
        }
        return null;
    }

    private void processMakerOrderViews( CDViewManager cdViewManager, SingleLegTrade trade ) {
        if( trade.isMaker() ){
            return;
        }
        SingleLegOrderMatch match = trade.getMatchEvent();
        if(match.isDirectedOrder()){
            if( TradeUtil.isDirectedOrderTradeNonBilateralNonBetweenCMs( trade )){
                Organization makerOrg = null;
                if(trade.getTradingParty()!=null){
                     makerOrg = trade.getTradingParty().getLegalEntityOrganization();
                }else{
                     makerOrg = trade.getCounterpartyLegalEntity().getOrganization();
                }

                CDViewManager.CDView pbView = new CDViewManager.CDView( CDViewManager.CDView.Type.MAKER, makerOrg );
                if ( !cdViewManager.getOrderViews().contains( pbView ) ) {
                    log.info("processMakerOrderViews :Maker: CDViewManager " + cdViewManager.getOrderId() + " does not contain pbView " + pbView.toString());
                    cdViewManager.addOrderView( pbView );
                    if (trade.getTradingParty() != null) {
                        createMakerOrder(trade.getOrderRequest(), makerOrg, trade.getTradingParty().getLegalEntity(), trade.getMakerUser());
                    } else if (trade.isOCX2()) {
                        createMakerOrder(trade.getOrderRequest(), makerOrg, trade.getCounterpartyLegalEntity(), trade.getMakerUser());
                    }
                }else{
                    String namespace = pbView.getOrganization().getNamespace().getShortName();
                    FXSingleLegOrder order = ( FXSingleLegOrder ) CDQOrderExpirableCache.get(namespace, trade.getOrderRequestId() );
                    if( order == null ) {
                        log.info("processMakerOrderViews :Maker: CDQOrderExpirableCache does not contain order for namespace : " + namespace + " orderRequestId : " + trade.getOrderRequestId());
                        if (trade.getTradingParty() != null) {
                            createMakerOrder(trade.getOrderRequest(), makerOrg, trade.getTradingParty().getLegalEntity(), trade.getMakerUser());
                        } else if (trade.isOCX2()) {
                            createMakerOrder(trade.getOrderRequest(), makerOrg, trade.getCounterpartyLegalEntity(), trade.getMakerUser());
                        }
                    }
                }
            }
        }else if ( trade.getTradingParty() != null) {
            Organization org = trade.getTradingParty().getLegalEntityOrganization();
            if ( match.isMultiPrimeBrokerCoverEnabled() ) {
                CDViewManager.CDView pbView = new CDViewManager.CDView( CDViewManager.CDView.Type.MAKER, org );
                if ( !cdViewManager.getOrderViews().contains( pbView ) ) {
                    log.info("processMakerOrderViews :PBEnabled: CDViewManager " + cdViewManager.getOrderId() + " does not contain pbView " + pbView.toString());
                    cdViewManager.addOrderView( pbView );
                    createMakerOrder( trade.getOrderRequest(), org, trade.getTradingParty().getLegalEntity(), trade.getMakerUser() );
                }
                else{
                    String namespace = pbView.getOrganization().getNamespace().getShortName();
                    FXSingleLegOrder order = ( FXSingleLegOrder ) CDQOrderExpirableCache.get(namespace, trade.getOrderRequestId() );
                    if( order == null ){
                        log.info("processMakerOrderViews :PBEnabled: CDQOrderExpirableCache does not contain order for namespace : " + namespace + " orderRequestId : " + trade.getOrderRequestId());
                        createMakerOrder(trade.getOrderRequest(), org, trade.getTradingParty().getLegalEntity(), trade.getMakerUser());
                    }
                }
            }else if ( org.isBroker() ) {
                CDViewManager.CDView brokerView = new CDViewManager.CDView( CDViewManager.CDView.Type.MAKER, org );
                if ( !cdViewManager.getOrderViews().contains( brokerView ) ) {
                    cdViewManager.addOrderView( brokerView );
                    log.info("processMakerOrderViews :Broker : CDViewManager" + cdViewManager.getOrderId() + "does not contain brokerView " + brokerView.toString());
                    createMakerOrder( trade.getOrderRequest(), org, trade.getTradingParty().getLegalEntity(), trade.getMakerUser() );
                }
                else{
                    String namespace = brokerView.getOrganization().getNamespace().getShortName();
                    FXSingleLegOrder order = ( FXSingleLegOrder ) CDQOrderExpirableCache.get(namespace, trade.getOrderRequestId() );
                    if( order == null ){
                        log.info("processMakerOrderViews :Broker: CDQOrderExpirableCache does not contain order for namespace : " + namespace + " orderRequestId : " + trade.getOrderRequestId());
                        createMakerOrder( trade.getOrderRequest(), org, trade.getTradingParty().getLegalEntity(), trade.getMakerUser() );
                    }
                }
            }
        }
    }
	

    protected void createMakerOrder( SingleLegOrder orderRequest, Organization makerOrg, LegalEntity legalEntity, User makerUser ) {
        log.info( "createMakerOrder : Creating Maker Order for Order=" + orderRequest.get_id() + ", makerOrg=" + makerOrg.getShortName() );
        try {
            FXSingleLegOrder order = of.orderCreate( orderRequest );
            order.set_id(SpacesOrderFacadeUtil.get_id(orderRequest.get_id(), makerOrg.getNamespace().getShortName(), CDViewManager.CDView.Type.MAKER) );
            flipMakerAttributes( order, makerOrg, legalEntity, makerUser );
            ApplicationSpaceEvent ase3 = PersistenceServiceFactory.getCDQPersistenceService().createEvent( order, ApplicationEventCodes.EVENT_CD_ORDER_SUBMIT );
            PersistenceServiceFactory.getCDQPersistenceService().synchronousPersist(ase3, orderRequest.getCorrelationId(), orderRequest.isWarmUpObject(), DEAL_CREATE_EVENT_NAME);
            CDQOrderExpirableCache.add( order );
            cdNotifier.notifyChiefDealer( null, order, order.getCounterpartyAOrg(), ApplicationEventCodes.EVENT_CD_ORDER_SUBMIT );
        }
        catch ( Exception ex ) {
            log.warn( "createMakerOrder : CDViewProcess in creating Maker Order for Order=" + orderRequest.get_id() + ", makerOrg=" + makerOrg.getShortName(), ex );
        }
    }

    private void flipMakerAttributes( FXSingleLegOrder order, Organization org, LegalEntity makerLE, User makerUser ) {
        order.setNamespace( org.getNamespace() );
        order.setCounterpartyAOrg( org );
        int bidOffer = order.getFXDealLeg().getAcceptedBidOfferMode();
        switch ( bidOffer ) {
            case DealingPrice.BID:
                order.getFXDealLeg().setAcceptedBidOfferMode( DealingPrice.OFFER );
                break;
            case DealingPrice.OFFER:
                order.getFXDealLeg().setAcceptedBidOfferMode( DealingPrice.BID );
                break;
        }
        order.setTaker( false );
        //order.setlegalEntityCptyA( makerLE );
        //order.setUser( makerUser ); -- AP-8243 - Broker chief dealer should see customer user as Trader
    }

    protected boolean shouldProcessDealReplay(SingleLegTrade trade, DealTransformAction action, CDViewManager.CDView view) throws UnsupportedOperationException{

        String dealTransactionId = getDealTransactionId(trade);
        OrderRequest orderRequest = trade.getOrderRequest();

        switch (action) {
            case DEAL_CREATE_EVENT:
            case DEAL_NET_EVENT:
                Deal deal = CDQDealExpirableCache.get(dealTransactionId, orderRequest.get_id(), view.getOrganization().getNamespace().getShortName());
                if (deal == null) {
                    log.info("SingleLegTrade " + trade + " has not been processed for view " + view + "hence will replay this notification for action:" + action);
                    return true;
                } else {
                    log.info("SingleLegTrade " + trade + " has been processed for view " + view + "hence will NOT replay this notification for action:" + action);
                    return false;
                }

            case DEAL_AMENDED_EVENT:
            case DEAL_CANCELLED_EVENT:
            case DEAL_NETTED_EVENT:
                deal = CDQDealExpirableCache.get(dealTransactionId, orderRequest.get_id(), view.getOrganization().getNamespace().getShortName());
                if(deal==null){
                    log.info("No deal found for " + trade + " orderRequestId : "+ orderRequest.get_id() + "and view" + view);
                    return false;
                }

                DealStateFacade dealStateFacade = (DealStateFacade) deal.getFacade(DealStateFacade.FACADE_NAME);
                if (dealStateFacade.isVerified()) {
                    log.info("SingleLegTrade " + trade + " has not been processed for view " + view + "hence will replay this notification for action:" + action);
                    return true;
                } else {
                    log.info("SingleLegTrade " + trade + " has been processed for view " + view + "hence will NOT replay this notification for action:" + action);
                    return false;
                }

            case DEAL_VERIFIED_EVENT:
            case DEAL_REJECTED_EVENT:
                deal = CDQDealExpirableCache.get(dealTransactionId, orderRequest.get_id(), view.getOrganization().getNamespace().getShortName());
                if(deal==null){
                    log.info("No deal found for " + trade + " orderRequestId : "+ orderRequest.get_id() + "and view" + view);
                    return false;
                }
                dealStateFacade = (DealStateFacade) deal.getFacade(DealStateFacade.FACADE_NAME);
                if (dealStateFacade.isCreated()) {
                    log.info("SingleLegTrade " + trade + " has not been processed for view " + view + "hence will replay this notification for action:" + action);
                    return true;
                } else {
                    log.info("SingleLegTrade " + trade + " has been processed for view " + view + "hence will NOT replay this notification for action:" + action);
                    return false;
                }
            case DEAL_UPDATE_EVENT:
            	deal = CDQDealExpirableCache.get(dealTransactionId, orderRequest.get_id(), view.getOrganization().getNamespace().getShortName());
                if(deal == null){
                    log.info("No deal found for " + trade + " orderRequestId : "+ orderRequest.get_id() + "and view" + view);
                    return false;
                }
                return true;
           default:
               String errorMessage = "Unsupported action for replay notification :" + action + " hence not replaying this notification";
               log.info(errorMessage);
               throw new UnsupportedOperationException(errorMessage);
        }
    }
}
