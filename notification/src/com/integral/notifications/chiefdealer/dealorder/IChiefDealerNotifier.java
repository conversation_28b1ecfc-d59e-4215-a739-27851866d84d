package com.integral.notifications.chiefdealer.dealorder;

import java.util.Map;

import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.Order;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.user.Organization;

public interface IChiefDealerNotifier {

	public void notifyChiefDealer( SingleLegOrder orderRequest, Order order, Organization organization, ApplicationEventCodes ase);

	 public void notifyChiefDealer( SingleLegOrder orderRequest, Order order, Organization organization,
             ApplicationEventCodes ase, Map<String, String> notificationProperties);

	 public void notifyChiefDealer( Deal deal, Organization organization, ApplicationEventCodes ase );

	
	
}
