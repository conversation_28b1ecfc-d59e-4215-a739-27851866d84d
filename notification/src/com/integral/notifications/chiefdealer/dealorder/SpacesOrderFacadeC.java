package com.integral.notifications.chiefdealer.dealorder;

import com.integral.finance.dealing.*;
import com.integral.finance.dealing.facade.DealingFacadeFactory;
import com.integral.finance.dealing.facade.OrderStateFacade;
import com.integral.finance.dealing.facade.config.RequestStateMBean;
import com.integral.finance.dealing.fx.FXDealLeg;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXSingleLegOrder;
import com.integral.finance.dealing.fx.FXSingleLegOrderC;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.TradeClassification;
import com.integral.fix.client.FixConstants;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.facade.ISFacade;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.model.dealing.*;
import com.integral.model.dealing.TimeInForce;
import com.integral.model.dealing.OrderRequest.RequestLeg;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.query.spaces.fx.esp.query.OrderQueryService;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.util.MathUtilC;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * User: biswa
 * Date: 6/26/13
 * Time: 3:09 PM
 */

/***
 *  This class encapsulates business logic to trigger state change of
 *  order (FXSingleLegOrder)
 */

public class SpacesOrderFacadeC extends ISFacade implements SpacesOrderFacade  {

    protected static RequestStateMBean requestStateMBean = DealingFacadeFactory.getRequestStateMBean();
    protected static String ACCEPT_VERIFIED_STATE_NAME = requestStateMBean.getAcceptedVerifiedStateName();
    private static AtomicLong sequenceForMongo = new AtomicLong();


    /**
     * Order creation for order request
     * @param orderRequest  SingleLegOrder
     * @return              FXSingleLegOrder for corresponding order request
     */
    @Override
    public FXSingleLegOrder orderCreate(SingleLegOrder orderRequest) {
        FXSingleLegOrder order = FXDealingFactory.newFXSingleLegOrder();
        FXDealLeg dealLeg = FXDealingFactory.newFXDealLeg();
        order.setOrderId( orderRequest.get_id() );
        order.setNamespace(orderRequest.getOrganization().getNamespace());
        List<OrderRequest.RequestLeg> legs = orderRequest.getRequestLegs();
        OrderRequest.RequestLeg requestLeg= legs.get(0);
        
        double amount = requestLeg.getAmount();
        order.setAmount( amount );
        order.setCreatedDate( new Date(orderRequest.getOrderRequestEventTimes().getSubmissionTime()));
        IdcDate valueDate = DateTimeFactory.newDate(new Date(orderRequest.getCreatedBusinessDate()));
        order.setTradeDate(valueDate);
        order.setSubmissionTimestamp(new Timestamp(orderRequest.getOrderRequestEventTimes().getSubmissionTime()));
        order.setExpiryTimestamp(new Timestamp(orderRequest.getExpireTime() ));
        order.setCounterpartyAOrg( orderRequest.getOrganization() );
        order.setNotes(orderRequest.getNotes());
        order.setPlacedByOrg(orderRequest.getUser().getOrganization());
        order.setCustomerOrg(orderRequest.getOrganization());
        order.setPQOrder(orderRequest.isPQOrder());
        if(orderRequest.getClientDescriptor() != null) {
			String quoteId = orderRequest.getClientDescriptor().getReferenceQuoteId();
			order.setQuoteId(quoteId);
		}
        if ( TimeInForce.GTC == orderRequest.getTimeInForce() )
        {
            order.setExpirationClassification( ISConstantsC.ORDER_CLASSIFICATION_GTC );
        }
        else if ( TimeInForce.GTD == orderRequest.getTimeInForce() )
        {
            order.setExpirationClassification( ISConstantsC.ORDER_CLASSIFICATION_GTD );
        }
        else if ( TimeInForce.GFS == orderRequest.getTimeInForce() )
        {
            order.setExpirationClassification( ISConstantsC.ORDER_CLASSIFICATION_GFS );
        }
        else if ( TimeInForce.FOK == orderRequest.getTimeInForce() )
        {
            order.setExpirationClassification( ISConstantsC.ORDER_CLASSIFICATION_FOK );
        }
        else if ( TimeInForce.DAY == orderRequest.getTimeInForce() )
        {
            order.setExpirationClassification( ISConstantsC.ORDER_CLASSIFICATION_DAY );
        }
        else if ( TimeInForce.IOC == orderRequest.getTimeInForce() )
        {
            order.setExpirationClassification( ISConstantsC.ORDER_CLASSIFICATION_IOC );
        }
        else if ( TimeInForce.GTF == orderRequest.getTimeInForce() )
        {
            order.setExpirationClassification( ISConstantsC.ORDER_CLASSIFICATION_GTF);
        }
        
        order.setNotes(orderRequest.getNotes());

        RequestClassification reqClsf = getRequestClassification( orderRequest);
        order.setOrderClassification( reqClsf );
        order.setUser( orderRequest.getUser() );
        order.setlegalEntityCptyA(orderRequest.getLegalEntity() );
        order.setContextInfo( orderRequest.getMarketSnapshot() );
        order.setMessageId( orderRequest.getSourceMessageId() );
        order.setTransactionId( orderRequest.getTransactionId() );
        order.setTaker(!orderRequest.isMaker() );
        getStateFacade( order ).setCreated();
        order.setOrderMetaData(orderRequest.getOrderMetaData());

        order.setOrderRequestGuid( orderRequest.get_id());

        boolean  isCcy1Dealt = orderRequest.getDealtCurrency().equals(orderRequest.getBaseCurrency());
        double orderRate  =requestLeg.getSpotRate();
        double amount2 = amount * orderRate;
        boolean isBuy = requestLeg.getBuySellMode()== OrderRequest.RequestLeg.BuySellMode.BUY;

        dealLeg.setAcceptedBidOfferMode( isBuy?DealingPrice.BID:DealingPrice.OFFER );
        dealLeg.setCurrencyPair(orderRequest.getCurrencyPair().toString());
        dealLeg.setCurrency1(orderRequest.getBaseCurrency());
        dealLeg.setCurrency2(orderRequest.getTermCurrency());
        dealLeg.setCurrency1Amount(isCcy1Dealt ? amount : amount2);
        dealLeg.setCurrency2Amount(isCcy1Dealt ? amount2 : amount);
        dealLeg.setBuyingCurrency1(isBuy);
        dealLeg.setDealtCurrency1(isCcy1Dealt);
        dealLeg.setTenor(requestLeg.getTenor());

        dealLeg.setRate( orderRate );
        dealLeg.setOriginalOrderRate(orderRate);
        dealLeg.setOriginalOrderAmount(amount);
        dealLeg.setSpotRate( requestLeg.getSpotRate());
        dealLeg.setFxRateConvention(QuoteConventionUtilC.getInstance().getDefault());
        //dealLeg.setLegClassification( getTradeLegClassification( trade ) );
		log.info("TransactionId: "+orderRequest.getTransactionId()+"CurrencyPair: "+orderRequest.getCurrencyPair().toString()+
        		" QuoteConvention:"+QuoteConventionUtilC.getInstance().getDefault());
        String userOrg = orderRequest.getUser().getOrganization().getShortName();
        String reqOrg = orderRequest.getOrganization().getShortName();
        if ( userOrg != null && !userOrg.equalsIgnoreCase( reqOrg ) )
        {
            order.setNamespace( orderRequest.getNamespace() );
        }
        order.setFXDealLeg( dealLeg );
        double disAmt = orderRequest.getMaxShowAmount();
        if ( disAmt > 0 )
        {
            order.setDisplayLimit( disAmt);
        }
        else
        {
            order.setDisplayLimit( 0.0 );
        }

        order.setOrderType( getOrderType(orderRequest) );
        order.setExecutionStrategy( getOrderExecutionStategy(orderRequest.getExecutionFlags()));
        

        if(orderRequest.isMarketRangeOrder()){
            double mktRange = orderRequest.getMarketRange();
            if( mktRange  != -1 ) // OA sets it while sending to BA. Do not show it to clients.
            {
                double marketRangeInPips = orderRequest.getMarketRange() * orderRequest.getFxRateBasis().getPipsFactor();
                order.setMarketRange( String.valueOf( marketRangeInPips ) );
            }
        }


        String dealingChannel = orderRequest.getChannel();
        if ( dealingChannel != null )
        {
            order.setDealingChannel( dealingChannel );
        }


        //order.setsLTriggerType(ISUtilImpl.getInstance().getSLTriggerType(orderRequest));
        order.setExecutionDate( new Date(orderRequest.getState().getTimestamp()));
        order.setServerManagedOrder(orderRequest.isServerManaged());
        order.setStopPrice( orderRequest.getOrderTrigger().getTriggerRate());
        if( (orderRequest.getExecutionFlags() & ExecutionFlags.TRAILING )== ExecutionFlags.TRAILING){
            order.setTrailingStopBestPrice( orderRequest.getOrderTrigger().getTrailingStopBestPrice());
            order.setTrailingDistance( orderRequest.getOrderTrigger().getTrailingDistance());
            FXRateBasis rb = orderRequest.getFxRateBasis();
            double trailingDistanceInPips = MathUtilC.multiply(orderRequest.getOrderTrigger().getTrailingDistance(),rb.getPipsFactor());
            order.setTrailingDistanceInPips( trailingDistanceInPips);
        }
        OrderStrategy strategy = orderRequest.getOrderStrategy();
        // OrderStrategy strategy = null;
        if(strategy != null){
            long execStartTime = strategy.getOrderExecutionStartTime();
            if( execStartTime == 0){
                execStartTime = orderRequest.getSubmissionTime();
            }
            order.setOrderExecutionStartTime(new Timestamp( execStartTime) );
            order.setOrderExecutionEndTime(new Timestamp( strategy.getOrderExecutionEndTime() ));
            order.setTwapSliceInterval( strategy.getSliceInterval());
            order.setRandomizeTwapSliceInterval( strategy.isRandomizeSliceInterval());
            order.setTwapSliceSize( strategy.getSliceSize());
            order.setRandomizeTwapSliceSize(strategy.isRandomizeSliceSize());
            OrderStrategy.ActionOnExpirationType actionOnExp = strategy.getActionOnExpiration();
            if (actionOnExp != null)
                order.setActionOnOrderExpitation(actionOnExp.ordinal());
            order.setTwapSliceRegularSize( strategy.getSliceRegularSize());
            order.setOrderExecutionStrategyName(strategy.getStrategyName());
            order.setTwapMinimumSliceInterval( strategy.getMinSliceInterval());
            order.setTwapSliceTopOfBookPercent(strategy.getSliceTOBPercent());
            order.setTwapFOKSlice( strategy.isSliceFOK());
            order.setTwapMinSliceSize(strategy.getMinSliceSize());
            order.setTwapSliceTopOfBookRange(strategy.getSliceTOBRange());
            order.setPegOffset( strategy.getPegOffsetValue());
            order.setPegOffsetIncrement( strategy.getPegOffsetIncrement());
            order.setPegOffsetIncrementInterval(strategy.getPegOffsetIncrementInterval());
            order.setTwapSliceIntervalRandomizationFactor(strategy.getSliceIntervalRandomizationFactor());
            order.setTwapSliceSizeRandomizationFactor(strategy.getSliceSizeRandomizationFactor());
        }

        
        OrderContingency contingency = orderRequest.getOrderContingency();
        if( contingency != null && contingency.getType() == OrderContingency.Type.OCO)
        {
            order.setLinkedOrderId( contingency.getLinkId());
        }
        
        if( orderRequest.getOrderContingencies() != null )
        {
        	order.setContingencyParameters(orderRequest.getOrderContingencies());
        	//This is for backward compatibility.
        	for( ContingencyParameter cParam : orderRequest.getOrderContingencies() )
        	{
        		//Parse only OCO or OUO type.
        		if ( cParam.getType() == ContingencyType.OCO || cParam.getType() == ContingencyType.OUO_ABSOLUTE ||
        				cParam.getType() == ContingencyType.OUO_PROPORTIONAL )
				{
            		if( cParam.getLinkedOrderIds() != null && cParam.getLinkedOrderIds().iterator().hasNext() )
            		{
            			//Put one of them as linkedOrderId
            			order.setLinkedOrderId(cParam.getLinkedOrderIds().iterator().next());
            		}
            		else if( cParam.getLinkedOrderIdsStr() != null )
            		{
            			order.setLinkedOrderId( cParam.getLinkedOrderIdsStr().split(",")[0] );
            		}
            		order.setContingentType(cParam.getType());
                	order.setGroupId( cParam.getGroupId() );
            		break;
				}
        	}
        }
        

        if ( (short) (orderRequest.getExecutionFlags() & ExecutionFlags.HIDDEN_ORDER) != ExecutionFlags.HIDDEN_ORDER )
        {
            order.setBroadcastOrder(true);
        }
        order.setOriginalMaxShowAmount(orderRequest.getOriginalMaxShowAmount());
        order.setDisplayLimit(orderRequest.getMaxShowAmount());
        order.setMinFillAmount(requestLeg.getMinFillAmount());
        order.setExecutionFlags(orderRequest.getExecutionFlags());
        order.setClientReferenceId(orderRequest.getClientReferenceId());
        order.setSecondaryPricePriority(orderRequest.getExecutionInstructions().getSecondaryPricePriority());
        if (orderRequest.getOrderTrigger() != null) {
            order.setTriggerType(orderRequest.getOrderTrigger().getTriggerType());
            if (order.getTriggerType() != null) {
                order.setsLTriggerType(order.getTriggerType().name());
            }
        }
        // populate fixing date and fixing tenor for NDF trades
        long fixingDate = requestLeg.getFixingDate();
        if (fixingDate > 0) {
        	dealLeg.setFixingDate(DateTimeFactory.newDate(new Date(fixingDate)));
        }
        String fixingTenor = requestLeg.getFixingTenor();
        if (fixingTenor != null) {
        	dealLeg.setFixingTenor(new Tenor(fixingTenor));
        }
        boolean isOTOInactiveOrder = orderRequest.isOTOInactiveOrder();
        order.setOTOInactiveOrder(isOTOInactiveOrder);
        if(ProductType.FXSPOT != orderRequest.getProductType()){
            TradeClassification clsf = ISUtilImpl.getInstance().getTradeClassification(orderRequest.getProductType());
            order.setTradeClassification(clsf);
        }
        order.setAlgoDescriptor(orderRequest.getAlgoDescriptor());
        order.setAlgoOrderAtBest(orderRequest.isAlgoOrderAtBest());
        order.setOmsOrder( orderRequest.isOMSOrder() );
        ExecutionInstructions executionInstructions = orderRequest.getExecutionInstructions();
        if (executionInstructions != null) {
            order.setRoutingInstruction(executionInstructions.getRoutingInstruction());
        }
        return order;
    }

    private RequestClassification getRequestClassification(SingleLegOrder orderRequest) {
        RequestClassification clsfn = null;
        if(orderRequest != null){
            clsfn = ISUtilImpl.getInstance().getRequestClassification( orderRequest.getType().name() );
        }
         return clsfn;
    }


    /**
     * Cancel an Order for given order request
     * @param order         Order to be cancelled
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    @Override
    public void orderCancel(Order order, SingleLegOrder orderRequest, String event_name) {
        if ( order == null || orderRequest == null)
        {
            return;
        }
        getStateFacade( order ).setCancelled();
        order.setFilledAmount( orderRequest.getOrderFilledAmount());
        order.setAverageRate( orderRequest.getOrderAverageFillRate() );

        //.Net client recognizes only "FXI" as value. Server also communicate 'FXI' to client at run time.
        //Since there is no user named 'FXI' , hard-coding value FXI right now.
        if( orderRequest.isUnsolicitedCancel() )
        {
            order.setUnsolicitedCancelBy( ISCommonConstants.SYSTEM_USER );
        }

        long cancellationTime = orderRequest.getOrderRequestEventTimes().getCancellationTime();
        order.setExecutionDate(new Date(cancellationTime));
        order.setExecutionTimestamp( new Timestamp(cancellationTime));
        if( orderRequest.getCancelledBy() == null ){
            order.setCancelledBy(orderRequest.getCancelledBy());
        }
        else{
            order.setCancelledBy( ISCommonConstants.SYSTEM_USER );
        }
        order.setOrderCancelReason(orderRequest.getCancellationReason());

    }

    @Override
    public void orderPreRateCancel(Order order, SingleLegOrder orderRequest, String event_name) {
        if ( order == null || orderRequest == null)
        {
            return;
        }
        getStateFacade(order).setPreRateCancelled();
    }

    /**
     * Fill an Order for given order request
     * @param order         Order to be Filled
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    @Override
    public void orderFilled(Order order, SingleLegOrder orderRequest, String event_name) {
        if ( order == null || orderRequest == null)
        {
            return;
        }
        getStateFacade( order ).setFilled();
        long filledTime = orderRequest.getModifiedTime();
        order.setExecutionDate(new Date(filledTime));
        order.setExecutionTimestamp( new Timestamp( filledTime ) );
        order.setFilledAmount( orderRequest.getOrderFilledAmount());
        order.setAverageRate(orderRequest.getOrderAverageFillRate());

        // populate fixing date and fixing tenor for NDF trades
        if (order instanceof FXSingleLegOrder) {
        	FXSingleLegOrder singleLegOrder = (FXSingleLegOrder)order;
        	FXDealLeg dealLeg =	singleLegOrder.getFXDealLeg();
        	RequestLeg requestLeg = orderRequest.getRequestLeg();
            long fixingDate = requestLeg.getFixingDate();
            if (fixingDate > 0) {
            	dealLeg.setFixingDate(DateTimeFactory.newDate(new Date(fixingDate)));
    }
            String fixingTenor = requestLeg.getFixingTenor();
            if (fixingTenor != null) {
            	dealLeg.setFixingTenor(new Tenor(fixingTenor));
            }
            long valueDate = requestLeg.getValueDate();            
            if (valueDate > 0) {
            	dealLeg.setValueDate(DateTimeFactory.newDate(new Date(valueDate)));
            }
        }

    }

    /**
     *  Query Maker Order for a given trade.
     * @param trade     SingleLegTrade for which maker order is to be found
     * @return          Maker Order
     */
    public Order getMakerOrderForRequest(SingleLegTrade trade) {
        Order order= null;
        SpacesQueryService.QueryResult<Order> result = OrderQueryService.queryForOrderById(DealingModelUtil.getCounterPartyLegalEntity( trade).getOrganization().getNamespace().getShortName(), trade.getOrderRequest().get_id());
        if(result.getStatus() == SpacesQueryService.QueryResult.Status.SUCCESS) {
            order = result.getResult();
        }
        if(order==null)
            log.info("Order not found in database : queryForOrderById(nameSpaceName=" + DealingModelUtil.getCounterPartyLegalEntity( trade).getOrganization().getNamespace().getShortName() + ", orderID=" + trade.getOrderRequest().get_id() + " ). Query status=" + result.getStatus().name());
        return order;
    }


    /**
     * Expire an Order for given order request
     * @param order         Order to be Expired
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    @Override
    public void orderExpired(Order order, SingleLegOrder orderRequest, String event_name) {
        if ( order == null || orderRequest == null)
        {
            return;
        }
        getStateFacade( order ).setExpired();
        order.setFilledAmount( orderRequest.getOrderFilledAmount());
        order.setAverageRate( orderRequest.getOrderAverageFillRate() );
        order.setCancelledBy( orderRequest.getCancelledBy() );
        long expirationTime = orderRequest.getModifiedTime();
        order.setExecutionDate(new Date(expirationTime));
        order.setExecutionTimestamp( new Timestamp(expirationTime));

    }

    @Override
    public void orderPreRateExpired(Order order, SingleLegOrder orderRequest, String event_name) {
        if ( order == null || orderRequest == null)
        {
            return;
        }
        getStateFacade(order).setPreRateExpired();
    }
    @Override
    public void orderCancelPending(Order order, SingleLegOrder orderRequest, String event_name) {
        if ( order == null || orderRequest == null)
        {
            return;
        }
        getStateFacade(order).setCancelPending();
    }
    @Override
    public void orderAmendPending(Order order, SingleLegOrder orderRequest, String event_name) {
        if ( order == null || orderRequest == null)
        {
            return;
        }
        getStateFacade(order).setAmendPending();
    }

    /**
     * Restate an Order for given order request
     * @param order         Order to be Restated
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    @Override
    public void orderRestated(Order order, SingleLegOrder orderRequest, String event_name) {
        if ( order == null || orderRequest == null)
        {
            return;
        }
        
        order.setAmount(orderRequest.getOrderAmount());
        ((FXSingleLegOrder)order).getFXDealLeg().setCurrency1Amount(orderRequest.getOrderAmount());
        long expirationTime = orderRequest.getModifiedTime();
        order.setExecutionDate(new Date(expirationTime));
        order.setExecutionTimestamp(new Timestamp(expirationTime));
    }
    
    /**
     * Activate the Order state for given order request
     * @param order         Order to be Activated
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    @Override    
    public void orderActive(Order order, SingleLegOrder orderRequest, String event_name){
		if (order == null || orderRequest == null) {
			return;
		}
		getStateFacade( order ).setActive();
    }

    /**
     * Fill of an Order for given order request
     * @param order         Order to be Expired
     * @param trade  associated {@link SingleLegTrade}
     * @param event_name    event which triggered this method
     */
    @Override
    public void orderFill(FXSingleLegOrder order, SingleLegTrade trade, String event_name) {
        double rawAvgFillRate = (( FXSingleLegOrderC)order).getRawAvgFillRate();
        if( rawAvgFillRate < ISCommonConstants.MIN_RATE ){
            rawAvgFillRate = order.getAverageRate();
        }
        FXDealLeg dealLeg = order.getFXDealLeg();
        boolean isTermCcyOrder = !dealLeg.isDealtCurrency1();
        double initialFillAmt = order.getFilledAmount();
        if( isTermCcyOrder ){
            double totalFilledAmount = order.getFilledAmount() + trade.getDealtAmount();
            double totalSettledAmount;
            if( rawAvgFillRate > ISCommonConstants.MIN_RATE ){
                totalSettledAmount = order.getFilledAmount()/rawAvgFillRate + trade.getDealtAmount() / trade.getSpotRate();
            }
            else{
                totalSettledAmount = trade.getDealtAmount() / trade.getSpotRate();
            }
            rawAvgFillRate = totalFilledAmount/totalSettledAmount;
        }
        else{
            double totalSettledAmount = (rawAvgFillRate * order.getFilledAmount() + trade.getDealtAmount() * trade.getSpotRate());
            double totalFilledAmount = order.getFilledAmount() + trade.getDealtAmount();
            rawAvgFillRate = totalSettledAmount/totalFilledAmount;
        }
        (( FXSingleLegOrderC)order).setRawAvgFillRate(rawAvgFillRate);
        double filledAmt = order.getFilledAmount();
        filledAmt = MathUtilC.add( filledAmt, trade.getDealtAmount() );
        order.setFilledAmount( filledAmt );
        double averageRate = ISUtilImpl.getInstance().roundAverageOrderFillRate( rawAvgFillRate, trade.getOrderRequest(), initialFillAmt );
        //double averageRate = RateRoundingService.roundAverageOrderFillRate( rawAvgFillRate, trade.getOrderRequest() );
        order.setAverageRate( averageRate );
        order.setExecutionDate(new Date(trade.getExecutionTime()));
        order.setExecutionTimestamp( new Timestamp(trade.getExecutionTime()));
    }

    public void preRateOrderFill(FXSingleLegOrder order, SingleLegTrade trade){
        double filledAmt = order.getFilledAmount();
        filledAmt = MathUtilC.add( filledAmt, trade.getDealtAmount() );
        order.setFilledAmount( filledAmt );

        double orderAmount = order.getAmount();
        if(MathUtilC.subtract(orderAmount, filledAmt) < MathUtilC.getMinAmount(trade.getDealtCurrency())) {
            getStateFacade(order).setPreRateFilled();
        } else {
            getStateFacade(order).setPreRatePartial();
        }
    }

    public void postRateOrderFill(FXSingleLegOrder order, SingleLegTrade trade){
        double rawAvgFillRate = (( FXSingleLegOrderC)order).getRawAvgFillRate();
        if( rawAvgFillRate < ISCommonConstants.MIN_RATE ){
            rawAvgFillRate = order.getAverageRate();
        }
        FXDealLeg dealLeg = order.getFXDealLeg();
        boolean isTermCcyOrder = !dealLeg.isDealtCurrency1();
        double initialFillAmt = order.getFilledAmount();
        if( isTermCcyOrder ){
            double totalFilledAmount = order.getFilledAmount() + trade.getDealtAmount();
            double totalSettledAmount;
            if( rawAvgFillRate > ISCommonConstants.MIN_RATE ){
                totalSettledAmount = order.getFilledAmount()/rawAvgFillRate + trade.getDealtAmount() / trade.getSpotRate();
            }
            else{
                totalSettledAmount = trade.getDealtAmount() / trade.getSpotRate();
            }
            rawAvgFillRate = totalFilledAmount/totalSettledAmount;
        }
        else{
            double totalSettledAmount = (rawAvgFillRate * order.getFilledAmount() + trade.getDealtAmount() * trade.getSpotRate());
            double totalFilledAmount = order.getFilledAmount() + trade.getDealtAmount();
            rawAvgFillRate = totalSettledAmount/totalFilledAmount;
        }
        (( FXSingleLegOrderC)order).setRawAvgFillRate(rawAvgFillRate);
        double averageRate = ISUtilImpl.getInstance().roundAverageOrderFillRate( rawAvgFillRate, trade.getOrderRequest(), initialFillAmt );
        //double averageRate = RateRoundingService.roundAverageOrderFillRate( rawAvgFillRate, trade.getOrderRequest() );
        order.setAverageRate( averageRate );
        order.setExecutionDate(new Date(trade.getExecutionTime()));
        order.setExecutionTimestamp( new Timestamp(trade.getExecutionTime()));
    }


    /**
     * Fail an Order for given order request
     * @param order         Order to be marked as Failed
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    @Override
    public void orderFailed(Order order, SingleLegOrder orderRequest, String event_name) {
        if ( order == null || orderRequest == null)
        {
            return;
        }
        order.setOrderRejectionReason(orderRequest.getRejectionReason());
        order.setOrderRejectionCode(orderRequest.getRejectionCode());
        getStateFacade( order ).setFailed();
    }


    /**
     * Amend an Order for given order request
     * @param order         Order to be Amended
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    @Override
    public void orderAmend(Order order, SingleLegOrder orderRequest, String event_name) {
        if (order == null || orderRequest == null) {
            return;
        }

        order.setAmended(true);
        List<OrderRequest.RequestLeg> legs = orderRequest.getRequestLegs();
        OrderRequest.RequestLeg requestLeg= legs.get(0);

        FXDealLeg dealLeg = ((FXSingleLegOrder) order).getFXDealLeg();
        double amount = requestLeg.getAmount();
        double orderRate = requestLeg.getSpotRate();
        double amount2 = amount * orderRate;
        order.setAmount( amount );
        boolean isCcy1Dealt = orderRequest.getDealtCurrency().equals(orderRequest.getBaseCurrency());
        dealLeg.setCurrency1Amount(isCcy1Dealt ? amount : amount2);
        dealLeg.setCurrency2Amount(isCcy1Dealt ? amount2 : amount);
        order.setOrderRate(orderRate );
        dealLeg.setRate(orderRate);
        dealLeg.setSpotRate(orderRate);
        order.setExecutionTimestamp(new Timestamp(orderRequest.getOrderRequestEventTimes().getAmendTime()));

        double disAmt = orderRequest.getMaxShowAmount();
        if (disAmt > 0){
            order.setDisplayLimit( disAmt);
        }
        else {
            order.setDisplayLimit( 0.0 );
        }

        order.setStopPrice( orderRequest.getOrderTrigger().getTriggerRate());
        order.setClientReferenceId(orderRequest.getClientReferenceId());
    }

    /**
     * Resume/Suspend an Order for given order request
     * @param order         Order to be Resume/Suspend
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    @Override
    public void orderResumeSuspend(Order order, SingleLegOrder orderRequest, String event_name) {
        if (order == null || orderRequest == null) {
            return;
        }

        order.setStrategyExecutionSuspended(orderRequest.isStrategyExecutionSuspended());
    }


    private String getOrderExecutionStategy(int executionFlags) {
        if ( ( executionFlags & ExecutionFlags.BEST_PRICE ) > 0 )
        {
            return "BP";
        }
        else if ( ( executionFlags & ExecutionFlags.VWAP ) > 0 )
        {
            return "VWAP";
        }
        else if ( ( executionFlags & ExecutionFlags.SWEEP ) > 0 )
        {
            return "SWP";
        }
        return "";
    }

    private String getOrderType(SingleLegOrder orderRequest) {
        OrderRequest.Type type = orderRequest.getType();
        switch (type){
            case LIMIT:
                return FixConstants.INTEGRAL_ORD_TYPE_LIMIT;
            case MARKET:
                return FixConstants.INTEGRAL_ORD_TYPE_MARKET;
            case STOP:
                if( (orderRequest.getExecutionFlags() & ExecutionFlags.TRAILING) == ExecutionFlags.TRAILING ){
                    return FixConstants.INTEGRAL_ORD_TYPE_TLSTOP;
                }
                return FixConstants.INTEGRAL_ORD_TYPE_STOP;
            case STOPLIMIT:
                return FixConstants.INTEGRAL_ORD_TYPE_STOP_LIMIT;
            default:
                return "";
        }
    }

    /**
     * get order state facade
     * @param order
     * @return
     */
    //TODO: in-line the method.
    private OrderStateFacade getStateFacade( Order order )
    {
        return ( OrderStateFacade ) order.getFacade( OrderStateFacade.FACADE_NAME );
    }
}
