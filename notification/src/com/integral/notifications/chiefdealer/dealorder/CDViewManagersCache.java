package com.integral.notifications.chiefdealer.dealorder;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.notifications.cache.NotificationCache;

/**
 * User: verma
 * Date: 10/10/13
 * Time: 3:14 PM
 */
public class CDViewManagersCache extends NotificationCache<CDViewManager> {
    private Log log = LogFactory.getLog(CDViewManagersCache.class);
    public CDViewManagersCache(int cacheSize,long expirationTimeInMS,int concurrencyLevel,boolean cacheEnabled) {
        super(cacheSize,expirationTimeInMS,concurrencyLevel);
    }

    @Override
    public Class getType() {
        return CDViewManager.class;
    }
}
