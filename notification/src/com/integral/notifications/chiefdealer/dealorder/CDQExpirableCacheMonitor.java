package com.integral.notifications.chiefdealer.dealorder;

import com.integral.notifications.cache.Cache;

import java.util.HashMap;
import java.util.Map;

/**
 * <PERSON><PERSON> used to monitor the CDQ related expirable cache details.
 *
 */

public class CDQExpirableCacheMonitor {
    private boolean cacheDisabled = false;
    private Map<String, Cache> cacheMap = new HashMap<String, Cache>();

    private CDQExpirableCacheMonitor() {
    }

    private static class CDQExpirableCacheMonitorHolder {
        private final static CDQExpirableCacheMonitor INSTANCE = new CDQExpirableCacheMonitor();
    }

    public static CDQExpirableCacheMonitor getInstance() {
        return CDQExpirableCacheMonitorHolder.INSTANCE;
    }

    public boolean isCacheDisabled() {
        return cacheDisabled;
    }

    public void setCacheDisabled(boolean cacheDisabled) {
        this.cacheDisabled = cacheDisabled;
    }

    public void add(String name, Cache expirableCache) {
        cacheMap.put(name, expirableCache);
    }

    public String recordCount() {
        if (cacheDisabled) {
            return " 0 (caching disabled)";
        }

        if (cacheMap.size() == 0) {
            return " None";
        }

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Cache> c : cacheMap.entrySet()) {
            sb.append(" ").append(c.getKey()).append(" = ").append(c.getValue().size());
        }

        return sb.toString();
    }
}
