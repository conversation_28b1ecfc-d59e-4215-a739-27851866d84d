package com.integral.notifications.chiefdealer.dealorder;

import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.fx.FXSingleLegDeal;
import com.integral.model.dealing.SingleLegTrade;

/**
 * User: biswa
 * Date: 6/26/13
 * Time: 3:08 PM
 */
public interface SpacesDealFacade {

    /**
     * Deal creation for given trade
     * @param trade     Associated SingleLegTrade
     * @param event     event which triggered this method
     * @return          FXSingleLegDeal for corresponding SingleLegTrade
     */
    FXSingleLegDeal dealCreatePreRate(SingleLegTrade trade, String event);


    /**
     * Deal creation for given trade
     * @param trade     Associated SingleLegTrade
     * @param event     event which triggered this method
     * @return          FXSingleLegDeal for corresponding SingleLegTrade
     */
    FXSingleLegDeal dealCreate(SingleLegTrade trade, String event);

    /**
     * Deal verification for given trade
     * @param trade     Associated SingleLegTrade
     * @param deal      Original deal
     * @param event     event which triggered this method
     */
    Deal dealVerified(SingleLegTrade trade,Deal deal, String event);


    /**
     * Deal Cancellation for given trade
     * @param trade     Associated SingleLegTrade
     * @param deal      Original deal
     * @param event     event which triggered this method
     */
    void dealCancelled(SingleLegTrade trade, Deal deal, String event);



    /**
     *  Performs a deep copy of given deal
     * @param deal      Original Deal
     * @return          Cloned Deal
     */
    FXSingleLegDeal cloneDeal(FXSingleLegDeal deal);

    /**
     * flip attributes a taker Deal to make it a Maker Deal.
     * @param takerDeal     Original Customer Deal.
     */
    void flipMakerAttributes(FXSingleLegDeal takerDeal);


    /**
     *  Sets the counterparty details on a given deal.
     * @param makerDeal Original Deal
     * @param trade     associated trade object
     */
    void setMakerCpty(Deal makerDeal,SingleLegTrade trade);

    /**
     * Deal Netted for given trade
     * @param trade     Associated SingleLegTrade
     * @param deal      Original deal
     * @param event     event which triggered this method
     */
    void dealNetted(SingleLegTrade trade, Deal deal, String event);


    /**
     * Deal Netted for given trade
     * @param trade     Associated SingleLegTrade
     * @param deal      Original deal
     * @param event     event which triggered this method
     */
    Deal dealNet(SingleLegTrade trade, Deal deal, String event);


    /**
     * Deal Rejected for given trade
     * @param trade     Associated SingleLegTrade
     * @param deal      Original deal
     * @param event     event which triggered this method
     */
    void dealRejected(SingleLegTrade trade, Deal deal, String event);
    
    /**
     * Deal Amended for given trade
     * @param trade     Associated SingleLegTrade
     * @param deal      Original deal
     * @param event     event which triggered this method
     */
    public void dealAmended(SingleLegTrade trade, Deal deal, String event) ;

    void updateDealAttributesForOcx2(Deal deal, SingleLegTrade trade, CDViewManager.CDView view);

    void updateDealAttributesForView(Deal deal, SingleLegTrade trade, CDViewManager.CDView view);
}
