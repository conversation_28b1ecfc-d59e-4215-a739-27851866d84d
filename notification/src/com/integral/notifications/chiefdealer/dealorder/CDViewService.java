package com.integral.notifications.chiefdealer.dealorder;

import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.TradeUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.DealingModelRef;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegOrderMatch;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.model.dealing.State;
import com.integral.notifications.cache.NotificationCache;
import com.integral.notifications.cache.NotificationCacheFactory;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.query.spaces.fx.esp.query.SingleLegOrderMatchQueryService;
import com.integral.query.spaces.fx.esp.query.SingleLegTradeQueryService;
import com.integral.spaces.SpaceIterator;
import com.integral.user.Organization;

/**
 * User: verma
 * Date: 10/9/13
 * Time: 3:14 PM
 */
public class CDViewService {
    private static final Log log = LogFactory.getLog( CDViewService.class );

    private static NotificationCache<CDViewManager> viewManagers;
    protected static CDViewService current;

    static {
        current = new CDViewService();
    }

    public static void init(){
        current._init();
    }

    protected void _init() {
        viewManagers = NotificationCacheFactory.getNotificationCache(CDViewManager.class);
    }

    public static CDViewManager getCDViewManager( SingleLegOrder order ) {
        return current._getCDViewManager( order );
    }

    public static CDViewManager createAndGetCDViewManager( SingleLegOrder order ) {
        return current._createAndGetCDViewManager( order );
    }

    private CDViewManager _createAndGetCDViewManager( SingleLegOrder order ) {
        CDViewManager vm = viewManagers.get(order.get_id());
        if ( vm == null ) {
            vm = new CDViewManager( order.get_id() );
            viewManagers.add(order.get_id(), vm);
        }
        return vm;
    }

    private CDViewManager _getCDViewManager( SingleLegOrder order ) {
        CDViewManager vm = viewManagers.get(order.get_id());
        if ( vm == null ) {
            vm = restoreCDViewManager( order );
            if( vm != null ){
                viewManagers.add(order.get_id(), vm);
            }
        }
        return vm;
    }

    public static CDViewManager getViewManager(SingleLegOrder order){
        return current.retrieveViewManager(order);
    }

    public static CDViewManagersCache getViewManagers(){
        return current.retrieveViewManagers();
    }

    protected CDViewManagersCache retrieveViewManagers(){
        return (CDViewManagersCache)viewManagers;
    }


    /***
     * This method is used for simply retrieving the viewManager from the Cache. If the viewManager does not find
     * the viewManager, it simply returns NULL. It does not restore the viewManager/modify the viewManager
     * @param order
     * @return
     */
    protected CDViewManager retrieveViewManager(SingleLegOrder order){
        return viewManagers.get( order.get_id() );

    }

    private CDViewManager restoreCDViewManager(SingleLegOrder order) {
        log.warn("restoreCDViewManager : Restoring the CDViewManager for Order=" + order.get_id());
        CDViewManager vm = new CDViewManager(order.get_id());
        vm.addOrderView(new CDViewManager.CDView(CDViewManager.CDView.Type.TAKER, order.getOrganization()));
        if (!order.getOrganization().getNamespace().isSameAs(order.getUser().getNamespace())) {
            //Add SD view
            vm.addOrderView(new CDViewManager.CDView(CDViewManager.CDView.Type.TAKER, order.getUser().getOrganization()));
        }
        Organization broker = order.getOrganization().getBrokerOrganization();
        if(broker != null && ISFactory.getInstance().getISMBean().createCDQMakerOrderOnSubmission(broker.getShortName())){
            vm.addOrderView(new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER, broker));
        }
        SpaceIterator<SingleLegOrderMatch> orderMatchIterator = getOrderMatchesForOrder(order);
        if (orderMatchIterator != null) {
            while (orderMatchIterator.hasNext()) {
                SingleLegOrderMatch orderMatch = orderMatchIterator.next();
                SpaceIterator<SingleLegTrade> tradeIterator = getTradesForOrderMatch(orderMatch);
                if (tradeIterator != null) {
                    while (tradeIterator.hasNext()) {
                        SingleLegTrade trade = tradeIterator.next();
                        populateTradeWithOrderMatch(orderMatch, trade);
                        State state = trade.getState();
                        if (state == null) {
                            log.error("restoreCDViewManager : Null State set on Trade=" + trade.get_id());
                            continue;
                        }
                        switch (state.getName()) {
                            case TSFAILED:
                                //Failed Trades are not processed for CDQ
                                continue;
                        }
                        //No Additional View is required for MAKER Trades as they are created for just one view in MAKER's Namespace.
                        if (trade.isMaker()) {
                            continue;
                        }

                        //SPAC-930 : Processing for maker views, ignore rejected and init stages
                        switch (state.getName()) {
                            case TSREJECTED:
                            case TSINIT:
                                continue;

                        }

                        if (trade.getTradingParty() != null) {

                            Organization org = trade.getTradingParty().getLegalEntityOrganization();
                            SingleLegOrderMatch match = trade.getMatchEvent();
                            if(match.isDirectedOrder()){
                                if( TradeUtil.isDirectedOrderTradeNonBilateralNonBetweenCMs( trade )){
                                    CDViewManager.CDView mView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER, org);
                                    if (!trade.isInternalRejection()) {
                                        vm.addOrderView(mView);
                                    }
                                }
                            }
                            if (org.isBroker() || match.isMultiPrimeBrokerCoverEnabled()) {
                                CDViewManager.CDView mView = new CDViewManager.CDView(CDViewManager.CDView.Type.MAKER, org);
                                if (!trade.isInternalRejection()) {
                                    vm.addOrderView(mView);
                                }
                            }
                        }
                    }
                }

            }
        }
        return vm;
    }

    private SpaceIterator<SingleLegTrade> getTradesForOrderMatch(SingleLegOrderMatch orderMatch){
        SpacesQueryService.QueryResult<SpaceIterator<SingleLegTrade>> tradeQueryResult = SingleLegTradeQueryService.getTradesIteratorForOrderMatch( orderMatch.getNamespace().getShortName(), orderMatch.get_id(), true );

        switch (tradeQueryResult.getStatus()){
            case FAILURE:
                log.error( "getTradesForOrderMatch : Failed to query Trades for OrderMatch " + orderMatch.get_id() );
                return null;
            case SUCCESS:
                return tradeQueryResult.getResult();
        }
        log.error("getTradesForOrderMatch : No valid status found, returning NULL");
        return null;

    }

    private SpaceIterator<SingleLegOrderMatch> getOrderMatchesForOrder(SingleLegOrder order){
        SpacesQueryService.QueryResult<SpaceIterator<SingleLegOrderMatch>> orderMatchQueryResult = SingleLegOrderMatchQueryService.getOrderMatchesIteratorForOrder( order.getNamespace().getShortName(), order.get_id(), true );
        switch (orderMatchQueryResult.getStatus()){
            case FAILURE:
                log.error( "getOrderMatchesForOrder : Failed to query OrderMatch for Order " + order.get_id() );
                return null;
            case SUCCESS:
                return orderMatchQueryResult.getResult();
        }
        log.error("getOrderMatchesForOrder : No valid status found, returning NULL");
        return null;
    }

    private void populateTradeWithOrderMatch(SingleLegOrderMatch orderMatch, SingleLegTrade trade) {
        DealingModelRef<SingleLegOrderMatch> mer = trade.getMatchEventRef();
        mer.setNamespace( orderMatch.getNamespace() );
        mer.setObject( orderMatch );
        mer.setUid( orderMatch.get_id() );
        trade.setMatchEventRef( mer );
    }
}
