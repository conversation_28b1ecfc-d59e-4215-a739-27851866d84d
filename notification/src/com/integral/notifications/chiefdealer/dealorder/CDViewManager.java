package com.integral.notifications.chiefdealer.dealorder;

import com.integral.user.Organization;

import java.util.Collection;
import java.util.HashSet;
import java.util.concurrent.ConcurrentHashMap;

/**
 * User: verma
 * Date: 10/9/13
 * Time: 2:30 PM
 *
 * Maintains the views for an Order.
 * This class is not thread safe. Only one thread should be invoking methods on an instance of this class.
 */
public class CDViewManager {
    private final String orderId;
    private final HashSet<CDView> orderViews = new HashSet<CDView>(  );

    public CDViewManager( String orderId ) {
        this.orderId = orderId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void addOrderView(CDView view){
        orderViews.add( view );
    }

    public Collection<CDView> getOrderViews(){
        return orderViews;
    }

    public static class CDView{
        public enum Type {MAKER,TAKER}
        private final Type type;
        private final Organization organization;
        private final String hashCodeStr;

        public CDView( Type type, Organization organization ) {
            this.type = type;
            this.organization = organization;
            this.hashCodeStr = new StringBuilder( 50 ).append( type ).append( ':' ).append( organization.getShortName() ).toString();
        }

        public Type getType() {
            return type;
        }

        public Organization getOrganization() {
            return organization;
        }

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder( "CDView{" );
            sb.append( "type=" ).append( type );
            sb.append( ", organization=" ).append( organization.getShortName() );
            sb.append( '}' );
            return sb.toString();
        }

        @Override
        public int hashCode() {
            return hashCodeStr.hashCode();
        }

        @Override
        public boolean equals( Object o ) {
            if( o != null && CDView.class.equals( o.getClass() )){
                return (( CDView )o).hashCodeStr.equals( this.hashCodeStr );
            }
            return false;
        }
    }
}
