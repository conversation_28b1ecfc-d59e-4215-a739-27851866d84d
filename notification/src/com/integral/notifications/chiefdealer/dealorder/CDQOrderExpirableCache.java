package com.integral.notifications.chiefdealer.dealorder;

import com.integral.finance.dealing.Order;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.notifications.cache.NotificationCache;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.query.spaces.fx.esp.query.OrderQueryService;

/**
* This class is used to keep the Deal objects which are currently used. Least used Deal objects
* are removed from the cache periodically.
*
*/
public class CDQOrderExpirableCache extends NotificationCache<Order> {

    private boolean cacheDisabled = false;
    private Log log = LogFactory.getLog(CDQOrderExpirableCache.class);

    public CDQOrderExpirableCache(int cacheSize,long expirationTimeInMS,int concurrencyLevel,boolean cacheEnabled) {
        super(cacheSize,expirationTimeInMS,concurrencyLevel);
        this.cacheDisabled = !cacheEnabled;
    }

    /**
     * Returns the key to the cache based on the given namespace and id.
     * NB. The id is guaranteed to be unique within the namespace only.
     *
     * @param namespace of the order request
     * @param id of the order request
     * @return key to the cache
     */
    private String getKey(final String namespace, final String id) {
        return namespace + "." + id;
    }

    /**
     * Return the order from cache is present.
     * If order is not found in cache query is done. Query is done on primary.
     * @param namespace
     * @param id
     * @return {@link com.integral.finance.dealing.Order}
     */
    public Order get(String namespace,String id) {
        Order order=null;
        if (cacheDisabled){
            order = queryOrderForRequest(namespace,id);
        }
        else{
            if(namespace != null && id != null){
                String key = getKey(namespace,id);
                order = get(key);
                if (order == null) {
                    log.info("Cache miss, key=" + key + " - querying database");
                    order = queryOrderForRequest(namespace,id);
                }
                else{
                    log.info("Cache hit, key=" + key);
                }
            }

        }
        return  order;
    }

    /**
     * Query spaces to find the Order object.
     * Query is done on primary.
     * @param nameSpaceName of the SingleLegOrder object
     * @param orderID of the SingleLegOrder object
     * @return Order found Order object on success or null upon failure
     */
    private Order queryOrderForRequest(String nameSpaceName,String orderID) {
        Order order= null;
        SpacesQueryService.QueryResult<Order> result = OrderQueryService.queryForOrderById(nameSpaceName,orderID,true);
        if(result.getStatus() == SpacesQueryService.QueryResult.Status.SUCCESS) {
            order = result.getResult();
        }
       if(order != null)
            add(order);
        else{
           log.info("Order not found in database : queryForOrderById(nameSpaceName=" + nameSpaceName + ", orderID=" + orderID + " ). Query status=" + result.getStatus().name());
       }
        return order;
    }

    /**
     * Adds the object to the cache by associating the object in the cache with its derived key.
     * If the key already exists in the cache the new object will replace the old one.
     *
     * @param order object to be associated in the cache
     */
    public void add(Order order) {
        if(order == null){
            log.info("add: received null Order");
            return;
        }
        if (cacheDisabled)
            return;

        String key = getKey(order);
        add(key, order);
        log.info("Added order to cache. key=" + key);
    }

    private String getKey(Order order) {
        return order.getNamespaceName() + "."   + order.getOrderId()  ;
    }

    @Override
    public Class getType() {
        return Order.class;
    }
}
