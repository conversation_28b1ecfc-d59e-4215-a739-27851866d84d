package com.integral.notifications.chiefdealer.dealorder;

import com.integral.finance.dealing.Order;
import com.integral.finance.dealing.fx.FXSingleLegOrder;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegTrade;

/**
 * User: biswa
 * Date: 6/26/13
 * Time: 3:09 PM
 */
public interface SpacesOrderFacade {

    /**
     * Order creation for order request
     * @param orderRequest  SingleLegOrder
     * @return              FXSingleLegOrder for corresponding order request
     */
    FXSingleLegOrder orderCreate(SingleLegOrder orderRequest);

    /**
     * Cancel an Order for given order request
     * @param order         Order to be cancelled
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    void orderCancel(Order order, SingleLegOrder orderRequest, String event_name);


    /**
     * Fill an Order for given order request
     * @param order         Order to be Filled
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    void orderFilled(Order order, SingleLegOrder orderRequest, String event_name);


    /**
     * Expire an Order for given order request
     * @param order         Order to be Expired
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    void orderExpired(Order order, SingleLegOrder orderRequest, String event_name);

    /**
     * Expire an fixing order
     * @param order
     * @param orderRequest
     * @param event_name
     */
    void orderPreRateExpired(Order order, SingleLegOrder orderRequest, String event_name);

    /**
     * Cancel pending order
     * @param order
     * @param orderRequest
     * @param event_name
     */
    void orderCancelPending(Order order, SingleLegOrder orderRequest, String event_name);

    /**
     * Amend pending order
     * @param order
     * @param orderRequest
     * @param event_name
     */
    void orderAmendPending(Order order, SingleLegOrder orderRequest, String event_name);

    /**
     * Cancel of fixing order
     * @param order
     * @param orderRequest
     * @param event_name
     */
    void orderPreRateCancel(Order order, SingleLegOrder orderRequest, String event_name);


    /**
     * Fill of an Order for given order request
     * @param order         Order to be Expired
     * @param trade  associated {@link SingleLegTrade}
     * @param event_name    event which triggered this method
     */
    public void orderFill(FXSingleLegOrder order, SingleLegTrade trade, String event_name);


    /**
     *
     * @param order order to be filled
     * @param trade pre rate trade verification
     */
    public void preRateOrderFill(FXSingleLegOrder order, SingleLegTrade trade);

    /**
     * Post rate update for deal order
     * @param order
     * @param trade
     */
    public void postRateOrderFill(FXSingleLegOrder order, SingleLegTrade trade);

    /**
     * Fail an Order for given order request
     * @param order         Order to be marked as Failed
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    void orderFailed(Order order, SingleLegOrder orderRequest, String event_name);


    /**
     * Restate an Order for given order request
     * @param order         Order to be restated
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    void orderRestated(Order order, SingleLegOrder orderRequest, String event_name);
    
    /**
     * Activate the Order state for given order request
     * @param order         Order to be Activated
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    void orderActive(Order order, SingleLegOrder orderRequest, String event_name);

    /**
     * Amend an Order for given order request.
     *
     * @param order         Order to be Amended
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    void orderAmend(Order order, SingleLegOrder orderRequest, String event_name);


    /**
     * Resume/Suspend an Order for given order request
     * @param order         Order to be Resume/Suspend
     * @param orderRequest  associated SingleLegOrder
     * @param event_name    event which triggered this method
     */
    void orderResumeSuspend(Order order, SingleLegOrder orderRequest, String event_name);
}
