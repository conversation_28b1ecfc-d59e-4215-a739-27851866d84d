package com.integral.notifications.mis;

import java.util.Vector;

import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;

import com.integral.is.common.util.ISUtilImpl;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.netting.model.NettingTradeRequestC;
import com.integral.notifications.cache.NotificationCache;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import com.integral.query.QueryCriteria;
import com.integral.query.QueryCriteriaBuilder;
import com.integral.query.QueryCriteriaBuilderC;

public class MISNettingTradeRequestExpirableCache  extends NotificationCache<NettingTradeRequestC>  {

	
    private final static Log LOG = LogFactory.getLog(MISNettingTradeRequestExpirableCache.class);

    private boolean cacheDisabled = false;

    public MISNettingTradeRequestExpirableCache(int cacheSize,long expirationTimeInMS,int concurrencyLevel,boolean cacheEnabled) {
        super(cacheSize,expirationTimeInMS,concurrencyLevel);
        this.cacheDisabled = !cacheEnabled;
    }

    /**
     * Returns the key to the cache based on the given namespace and id.
     * NB. The id is guaranteed to be unique within the namespace only.
     *
     * @param namespace of the MIS notification object
     * @param id of the object
     * @return key to the cache
     */
    private String getKey(final String namespace, final String pfId, final String sequenceNo) {
        return namespace + "." + pfId + "." + sequenceNo;
    }

    /**
     * @param req to be cached
     * @return key to the cache
     */
    private String getKey(NettingTradeRequestC ntr) {
        return getKey(ntr.getNamespaceName(), ntr.getNettingPortfolio().getPortfolioID(), ntr.getSequenceNumber());
    }

    /**
     * Query the database to find the Request object.
     *
     * @param orderId of the Request object
     * @param performanceMetrics null or MIS performance metrics
     * @return found Request object on success or null upon failure
     * @throws PersistenceException
     */
    private NettingTradeRequestC query(final String pfId, final String sequenceNo, MISPerformanceMetrics performanceMetrics) throws PersistenceException 
    {
        QueryCriteriaBuilder builder = new QueryCriteriaBuilderC();
        QueryCriteria criteria = builder.get("");
        Expression exp = new ExpressionBuilder();
        Expression pfIdExp = exp.get("nettingPortfolio").get("_id").equal(pfId);
        Expression seqNoExp = exp.get("sequenceNumber").equal(sequenceNo);
        criteria.setExpression(pfIdExp.and(seqNoExp));
        
        ReadAllQuery query = new ReadAllQuery(NettingTradeRequestC.class, criteria.getExpression());

        long startQueryTime = (performanceMetrics != null) ? System.currentTimeMillis() : 0L;
        Vector allObjects = (Vector)PersistenceFactory.newSession().executeQuery(query);
        if (performanceMetrics != null) {
            performanceMetrics.setTargetDbQueryTime(System.currentTimeMillis() - startQueryTime);
        }

        if ((allObjects != null) && (allObjects.size() > 0)) {
        	NettingTradeRequestC req = (NettingTradeRequestC)allObjects.get(0);
            add(req);
            return req;
        }

        return null;
    }

    /**
     * Adds the object to the cache by associating the object in the cache with its derived key.
     * If the key already exists in the cache the new object will replace the old one.
     *
     * @param TradeRequest object to be associated in the cache
     */
    public void add(NettingTradeRequestC ntr) {
        if (cacheDisabled)
            return;

        String key = getKey(ntr);
        add(key, ntr);
        LOG.info("Associated NettingTradeRequestC object with key " + key);
    }

    /**
     * Find the NettingTradeRequest object from cache. Only if not present in cache, query the database.
     *
     * @param namespace of the NettingTradeRequest object
     * @param portfolioId of the NettingTradeRequest object
     * @param performanceMetrics null or MIS performance metrics
     * @return found NettingTradeRequest object on success or null upon failure
     * @throws PersistenceException
     */
    public NettingTradeRequestC get(final String namespace, final String portfolioId, final String sequenceNo,
    		MISPerformanceMetrics performanceMetrics) throws PersistenceException 
    {
        if (cacheDisabled)
            return query(portfolioId, sequenceNo, performanceMetrics);

        String key = getKey(namespace, portfolioId, sequenceNo);
        NettingTradeRequestC ntr = (NettingTradeRequestC)get(key);
        if (ntr == null) {
            LOG.info("Cache miss, key=" + key + " - querying database");
            return query(portfolioId, sequenceNo, performanceMetrics);      // also adds to cache
        }

        if (performanceMetrics != null) {
            performanceMetrics.setTargetDbQueryTime(0L);
        }

        LOG.info("Cache hit, key=" + key);
        return ntr;
    }

    @Override
    public Class getType() {
        return NettingTradeRequestC.class;
    }

}
