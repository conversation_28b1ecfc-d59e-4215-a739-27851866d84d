package com.integral.notifications.mis;

import com.integral.broker.model.Stream;
import com.integral.businessCenter.BusinessCenterC;
import com.integral.finance.counterparty.CounterpartyC;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.creditLimit.*;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dateGeneration.SettlementDateRuleC;
import com.integral.finance.dealing.*;
import com.integral.finance.dealing.facade.DealingFacadeFactory;
import com.integral.finance.dealing.facade.config.RequestStateMBean;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXDealingPriceElement;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.*;
import com.integral.finance.marketData.fx.FXMarketDataElementC;
import com.integral.finance.marketData.fx.FXMarketDataSetC;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.TradeClassificationC;
import com.integral.finance.trade.TradeLegClassificationC;
import com.integral.fix.client.FixConstants;
import com.integral.is.ISCommonConstants;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.MarketSnapshotUtil;
import com.integral.is.finance.businessCenter.EndOfDayService;
import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.TimeInForce;
import com.integral.model.dealing.*;
import com.integral.model.dealing.descriptor.OriginatingOrderRequestDescriptor;
import com.integral.model.ems.EMSExecutionRule;
import com.integral.persistence.*;
import com.integral.rule.RuleC;
import com.integral.rule.RuleSetC;
import com.integral.rule.SetPropertyActionC;
import com.integral.session.IdcTransaction;
import com.integral.spaces.notification.NotificationSerializationException;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateC;
import com.integral.time.IdcDateTime;
import com.integral.user.*;
import com.integral.workflow.ChangeSetC;
import com.integral.workflow.RequestWorkflowStateMapDependentC;
import com.integral.workflow.StateC;
import com.integral.workflow.WorkflowFactory;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: behzadib
 * Date: 10/4/12
 * Time: 10:14 AM
 *
 * This singleton is used to transform an object of type
 * com.integral.model.dealing.SingleLegOrder to com.integral.finance.dealing.RequestC
 * to be further serialized.  Therefore, the static and transient fields are ignored.
 */
public class MISSingleLegOrderTransformer {
    private final static Log LOG = LogFactory.getLog(MISSingleLegOrderTransformer.class);
    private final MISRequestExpirableCache requestExpirableCache;

    public MISSingleLegOrderTransformer(MISRequestExpirableCache requestExpirableCache) {
        this.requestExpirableCache = requestExpirableCache;
    }

    /**
     * Query the database table with the given orderId only if it is an UPDATE case.
     * If exists, it'll be returned, otherwise it is an INSERT case and it instantiates a new one.
     *
     * @param namespace of the Request object
     * @param orderId to be used in the query
     * @param appEventCode application event code at the time
     * @param performanceMetrics MIS performance metrics
     * @return a new or the existing record
     * @throws PersistenceException
     */
    public RequestC getPersistentRequestRecord(final String namespace, final String orderId, final ApplicationEventCodes appEventCode, MISPerformanceMetrics performanceMetrics) throws PersistenceException {
        if (MISHandlerUtil.isOrderUpdateEvent(appEventCode)) {
            RequestC req = queryRequest(namespace, orderId, performanceMetrics);
            if (req != null)
                return req;

            LOG.error("No prior record in database, orderId=" + orderId + " update appEventCode=" + appEventCode.toString());
			MessageLogger.getInstance()
					.log(ISAlertMBean.ORDER_NOT_FOUND_IN_DB,
							this.getClass().getName(),
							"No prior record in database, orderId=" + orderId
									+ " update appEventCode="
									+ appEventCode.toString(), null);
            throw new PersistenceException();
        }

        return new RequestC();      // ThreadLocal is overkill as these are cached
    }

    public RequestC queryRequest(String namespace, String orderId, MISPerformanceMetrics performanceMetrics) throws PersistenceException {
        return requestExpirableCache.get(namespace, orderId, performanceMetrics);
    }

    /**
     * Add readonly classes required for persisting the transaction.
     * These are the classes which will not change by insert/update.
     * That includes all the reference data.
     *
     * @param tx transaction id
     */
    public void addReadOnlyClasses(final IdcTransaction tx) {
        tx.addReadOnlyClass(BusinessCenterC.class);
        tx.addReadOnlyClass(ChangeSetC.class);
        tx.addReadOnlyClass(CounterpartyC.class);
        tx.addReadOnlyClass(CounterpartyCreditLimitRuleC.class);
        tx.addReadOnlyClass(CreditLimitClassificationC.class);
        tx.addReadOnlyClass(CreditLimitRuleC.class);
        tx.addReadOnlyClass(CreditLimitRuleSetC.class);
        tx.addReadOnlyClass(CreditUtilizationC.class);
        tx.addReadOnlyClass(CreditUtilizationCalculatorC.class);
        tx.addReadOnlyClass(CreditUtilizationEventC.class);
        tx.addReadOnlyClass(CurrencyC.class);
        tx.addReadOnlyClass(DailyCreditLimitRuleC.class);
        tx.addReadOnlyClass(DailyCreditUtilizationC.class);
        tx.addReadOnlyClass(Date.class);
        tx.addReadOnlyClass(DisplayPreferenceC.class);
        tx.addReadOnlyClass(ExternalSystemC.class);
        tx.addReadOnlyClass(FXBusinessCalendarC.class);
        tx.addReadOnlyClass(FXMarketDataElementC.class);
        tx.addReadOnlyClass(FXMarketDataSetC.class);
        tx.addReadOnlyClass(FXRateBasisC.class);
        tx.addReadOnlyClass(FXRateConventionC.class);
        tx.addReadOnlyClass(FXSettlementDateRuleC.class);
        tx.addReadOnlyClass(IdcDateTime.class);
        tx.addReadOnlyClass(LegalEntityC.class);
        tx.addReadOnlyClass(NamespaceC.class);
        tx.addReadOnlyClass(NamespaceGroupC.class);
        tx.addReadOnlyClass(OrderC.class);
        tx.addReadOnlyClass(OrganizationC.class);
        tx.addReadOnlyClass(OrganizationRelationshipClassificationC.class);
        tx.addReadOnlyClass(QuoteC.class);
        tx.addReadOnlyClass(QuoteClassificationC.class);
        tx.addReadOnlyClass(RequestClassificationC.class);
        tx.addReadOnlyClass(RuleC.class);
        tx.addReadOnlyClass(RuleSetC.class);
        tx.addReadOnlyClass(SetPropertyActionC.class);
        tx.addReadOnlyClass(SettlementDateRuleC.class);
        tx.addReadOnlyClass(SingleCreditLimitRuleC.class);
        tx.addReadOnlyClass(StateC.class);
        tx.addReadOnlyClass(Timestamp.class);
        tx.addReadOnlyClass(TradeClassificationC.class);
        tx.addReadOnlyClass(TradeLegClassificationC.class);
        tx.addReadOnlyClass(TradingPartyC.class);
        tx.addReadOnlyClass(UserC.class);
    }

    /**
     * Transforms an object of type com.integral.model.dealing.SingleLegOrder to com.integral.finance.dealing.RequestC
     * ignoring the transient and static fields of both objects are ignored.
     *
     * @param singleLegOrder object to be transformed
     * @throws Exception
     */
    public void transform(final RequestC req, final SingleLegOrder singleLegOrder, final ApplicationEventCodes appEventCode) throws Exception {
        LOG.info(singleLegOrder.getClass().getName() + " ==> " + req.getClass().getName() + " orderId=" + singleLegOrder.get_id() + " appEventCode=" + appEventCode.toString());

        final boolean update = MISHandlerUtil.isOrderUpdateEvent(appEventCode);
        if (!update) {
            // start from a clean slate.
            req.resetTransients();
            req.resetFacadeTransients();
            req.resetTransientProperties();
            req.resetStickyProperties();

            req.setOrderId(singleLegOrder.get_id());
        }

        /*
         * Transforming the inherited fields from DealingModel first.
         */
        req.setNamespace(singleLegOrder.getNamespace());
        req.setStatus(Entity.ACTIVE_STATUS);            // TODO need getStatus() [just like SingleLegTrade] not to hard code it
        req.setVirtualServer(singleLegOrder.getVirtualServer());
        req.setPortfolioRefId( singleLegOrder.getPortfolioId() );
        req.setOriginatingPortfolioId(singleLegOrder.getOriginatingPortfolioId());
        req.setCreatedTimestamp(new Timestamp(singleLegOrder.getCreatedTime()));
        req.setCreatedBusinessDate(DateTimeFactory.newDate(new Date(singleLegOrder.getCreatedBusinessDate())));
        req.setModifiedDate(new Date(singleLegOrder.getModifiedTime()));

        /*
         * SingleLegOrder non-inherited fields to RequestC
         */
        req.setCoveredOrderId(singleLegOrder.getCoveredOrderId());
        req.setOriginatingOrderId( singleLegOrder.getOriginatingOrderId() );
        OriginatingOrderRequestDescriptor oord = singleLegOrder.getOriginatingOrderRequest();
        if( oord != null ){
            if( oord.getLegalEntity() != null ){
                req.setOriginatingCptyId( oord.getLegalEntity().getObjectID() );
            }
            if( oord.getUser() != null ){
                req.setOriginatingUserId( oord.getUser().getObjectID() );
            }
        }
        req.setCorelationId(singleLegOrder.getCorrelationId());
        req.setCurrencyPair(singleLegOrder.getCurrencyPair());
        req.setUser(singleLegOrder.getUser());
        req.setLegalEntity(singleLegOrder.getLegalEntity());
        req.setExecutionFlags((short)singleLegOrder.getExecutionFlags());
        req.setMarketSnapshot(MarketSnapshotUtil.convertToOldSnapshot(singleLegOrder.getMarketSnapshot()));
        req.setGridMidRate(singleLegOrder.getGridMidRate());
        req.setExpiryTime(new Timestamp(singleLegOrder.getExpireTime()));
        req.setTradeChannel(singleLegOrder.getChannel());
        req.setPersistentOrder(singleLegOrder.isPersist());
        req.setCancelledBy(singleLegOrder.getCancelledBy());
        req.setTransactionID(singleLegOrder.getTransactionId());
        req.setExternalRequestId(singleLegOrder.getClientReferenceId());     // ClientOrderId
        req.setIsOrderPassive(singleLegOrder.getIsOrderPassive());
        req.setNettingEnabled(singleLegOrder.isNettingEnabled());
        req.setOrderNotes(singleLegOrder.getNotes());
        req.setExternalAccount(singleLegOrder.getExternalAccount());
        Organization org = singleLegOrder.getOrganization();
        if (org == null)
            throw new NullPointerException("org");

        req.setOrganization(org);
        req.setOrganizationFirm(singleLegOrder.getFirm());
//        if (org.getBrokerOrganization() != null) {
//            req.setBrokerOrgId(org.getBrokerOrganization().getObjectID());
//        }

        OrderCancellationCode cancellationCode = singleLegOrder.getCancellationCode();
        if (cancellationCode != null) {
            req.setCancellationCode(cancellationCode.getCode());
        }

        // set the stream on the request.
        Stream stream = singleLegOrder.getStream();
        if ( stream != null )
        {
            req.setCustomerStreamId( stream.getShortName() );
        }

        transformState(singleLegOrder, req, update);
        transformPrice(singleLegOrder, req, update);
        transformOrderFlags(singleLegOrder, req);
        transformEventTimes(singleLegOrder, req, update);
        transformTimeInForce(singleLegOrder, req);
        transformClassification(singleLegOrder, req);
        transformExecutionInstructions(singleLegOrder, req);
        transformOrderStrategy(singleLegOrder, req, update);
        transformOrderContingency(singleLegOrder, req);
        transformPreferredProviders(singleLegOrder, req);
        EMSExecutionRule emsExecutionRule = singleLegOrder.getExecutionRule();
        if( emsExecutionRule != null ){
            req.setEmsExecutionType(emsExecutionRule.getExecutionType());
        }
        req.setCustomParameters ( singleLegOrder.getCustomParameters () );
    }

    /*
     * Should not use the RequestStateFacade.set*() here as it'll add extra unnecessary database inserts.
     */
    private void transformState(final SingleLegOrder singleLegOrder, final RequestC req, final boolean update) throws NullPointerException, PersistenceException {
        final RequestStateMBean requestStateMBean = DealingFacadeFactory.getRequestStateMBean();
        final RequestWorkflowStateMapDependentC reqState = WorkflowFactory.newRequestWorkflowStateMapDependent();

        if (!update) {
            reqState.setState(requestStateMBean.getInitialState());
            req.setWorkflowStateMap(reqState);
            return;
        }

        reqState.setLastState(reqState.getState());

        State singleLegOrderState = singleLegOrder.getState();
        if (singleLegOrderState == null)
            throw new NullPointerException("singleLegOrderState");

        switch (singleLegOrderState.getName()) {
            case  RSINIT:
                reqState.setState(requestStateMBean.getInitialState());
                break;
            case RSACTIVE:
                reqState.setState(requestStateMBean.getActiveState());
                break;
            case RSDECLINED:
                reqState.setState(requestStateMBean.getDeclinedState());
                break;
            case RSCANCELLED:
                reqState.setState(requestStateMBean.getCancelledState());
                break;
            case RSEXECUTED:
                reqState.setState(requestStateMBean.getAcceptedVerifiedState());
                break;
            case RSPARTIAL:
                reqState.setState(requestStateMBean.getPartialState());
                break;
            case RSEXCEPTION:
                reqState.setState(requestStateMBean.getExceptionState());
                break;
            case RSSUSPENDED:
                reqState.setState(requestStateMBean.getSuspendedState());
                break;
            case RSEXPIRED:
                reqState.setState(requestStateMBean.getExpiredState());
                break;
            case RSPRERATEPARTIAL:
                reqState.setState(requestStateMBean.getPreRatePartialState());
                break;
            case RSPRERATECOMPLETE:
                reqState.setState(requestStateMBean.getPreRateFilledState());
                break;
            case RSPRERATEEXPIRED:
                reqState.setState(requestStateMBean.getPreRateExpiredState());
                break;
            case RSPRERATECANCELLED:
                reqState.setState(requestStateMBean.getPreRateCancelledState());
                break;
            default:
                LOG.error("Invalid state " + singleLegOrderState.getName() + ", orderId " + singleLegOrder.get_id());
                return;
        }

        req.setWorkflowStateMap(reqState);
    }

    private void transformPrice(final SingleLegOrder singleLegOrder, final RequestC req, final boolean update) throws NullPointerException, PersistenceException {
        final String priceName = "singleLeg";
        FXLegDealingPrice origPrice;
        if (update) {
            origPrice = (FXLegDealingPrice)req.getRequestPrice(priceName);
            if (origPrice == null) {
                throw new NullPointerException("origPrice");
            }
        } else {
            origPrice = FXDealingFactory.newFXLegDealingPrice();
        }
        FXLegDealingPrice price = (FXLegDealingPrice)origPrice.getRegisteredObject();
        if (price == null)
            throw new NullPointerException("price");

        if(singleLegOrder.getFxRateBasis().isForwardSettlementType()){
            price.setTenor(new Tenor(singleLegOrder.getRequestLeg().getTenor()));
        }

        if( singleLegOrder.isPQOrder() ) {
            price.setMaxShowAmount(0d);
            price.setTenor(Tenor.SPOT_TENOR);
        } else {
            price.setMaxShowAmount(singleLegOrder.getMaxShowAmount());
        }

        CurrencyPair currencyPair = singleLegOrder.getCurrencyPair();
        if (currencyPair == null)
            throw new NullPointerException("currencyPair");

        Currency dealtCurrency = singleLegOrder.getDealtCurrency();
        if (dealtCurrency == null)
            throw new NullPointerException("dealtCurrency");

        price.setDealtCurrency(dealtCurrency);

        Currency baseCurrency = currencyPair.getBaseCurrency();
        if (baseCurrency == null)
            throw new NullPointerException("baseCurrency");

        if (baseCurrency.getShortName().equals(dealtCurrency.getShortName()))
            price.setSettledCurrency(currencyPair.getVariableCurrency());
        else
            price.setSettledCurrency(currencyPair.getBaseCurrency());

        OrderRequest.RequestLeg requestLeg = singleLegOrder.getRequestLeg();
        if (requestLeg == null)
            throw new NullPointerException("requestLeg");

        OrderRequest.RequestLeg.BuySellMode buySellMode = requestLeg.getBuySellMode();
        if (buySellMode == null)
            throw new NullPointerException("buySellMode");

        FXDealingPriceElement fxPriceElement;
        if (update) {
            fxPriceElement = price.getFXPriceElement();
            if (fxPriceElement == null)
                throw new NullPointerException("fxPriceElement");
        } else {
            fxPriceElement = FXDealingFactory.newFXDealingPriceElementDependent();
            price.setFXPriceElement(fxPriceElement);
        }
        FXPrice fxPrice = fxPriceElement.getFXPrice();
        if (fxPrice == null)
            throw new NullPointerException("fxPrice");

        fxPrice.setBaseCurrency(singleLegOrder.getBaseCurrency());
        fxPrice.setVariableCurrency(singleLegOrder.getTermCurrency());

        switch (buySellMode) {
            case SELL:
                price.setBidOfferMode(DealingPrice.OFFER);
                if (!update) {
                    fxPrice.setOfferFXRate(new FXRateC());
                }

                FXRate offerFXRage = fxPrice.getOfferFXRate();
                if (offerFXRage == null)
                    throw new NullPointerException("offerFXRage");

                offerFXRage.setSpotRate(singleLegOrder.getOrderSpotRate());
                if (singleLegOrder.isOutrightLimitOrder()) {                   
                    if (requestLeg != null) {
                    	offerFXRage.setForwardPoints(requestLeg.getForwardPoint());
                    	offerFXRage.setRate(requestLeg.getRate());
                    }
                } 
                
                break;
            case BUY:
                price.setBidOfferMode(DealingPrice.BID);
                if (!update) {
                    fxPrice.setBidFXRate(new FXRateC());
                }

                FXRate bidFXRate = fxPrice.getBidFXRate();
                if (bidFXRate == null)
                    throw new NullPointerException("bidFXRate");

                bidFXRate.setSpotRate(singleLegOrder.getOrderSpotRate());
                if (singleLegOrder.isOutrightLimitOrder()) {                   
                    if (requestLeg != null) {
                    	bidFXRate.setForwardPoints(requestLeg.getForwardPoint());
                    	bidFXRate.setRate(requestLeg.getRate());
                    }
                } 
                break;
            case TWO_WAY:
                // single leg order is not expected to get TWO_WAY, still we set the mode
                price.setBidOfferMode(DealingPrice.TWO_WAY);
                break;
            default:
                LOG.error("Invalid buySellMode=" + buySellMode + ", orderId " + singleLegOrder.get_id());
                break;
        }

        req.setVolumeWeightedQuotePrice(requestLeg.getVwapPrice());
        price.setDealtAmount(requestLeg.getAmount());
        price.setTOBBidRateAtTerminalState(requestLeg.getTobBidRateAtCompletion());
        price.setTOBOfferRateAtTerminalState(requestLeg.getTobOfferRateAtCompletion());
        OrderStrategy orderReqExecInstrStrategy = singleLegOrder.getOrderStrategy();
        if (orderReqExecInstrStrategy != null) {
        	price.setTOBRange(orderReqExecInstrStrategy.getSliceTOBRange());
        }
        FXRateBasis fxRateBasis = singleLegOrder.getFxRateBasis();
        if (fxRateBasis == null)
            throw new NullPointerException("fxRateBasis");
        if (singleLegOrder.isOutrightLimitOrder()) {
        	// It's outright , limit order
        	long longValue = requestLeg.getValueDate();
        	if (longValue > 0) {
        		price.setValueDate(IdcDateC.newDate(new Date(longValue)));
        	} else {
                IdcDate tradeDate = DateTimeFactory.newDate(new Date(singleLegOrder.getCreatedBusinessDate()));
                price.setValueDate(EndOfDayService.getValueDate(fxRateBasis, tradeDate));
        	}
            long fixingDate = requestLeg.getFixingDate();
            if (fixingDate > 0) {
            	price.setFixingDate(IdcDateC.newDate(new Date(fixingDate)));
            }    
        } else {
            IdcDate tradeDate = DateTimeFactory.newDate(new Date(singleLegOrder.getCreatedBusinessDate()));
            price.setValueDate(EndOfDayService.getValueDate(fxRateBasis, tradeDate));
        }    
        price.setStopLossTriggered(singleLegOrder.isStopLossTriggered());
        OrderTrigger orderTrigger = singleLegOrder.getOrderTrigger();       // currently orders are triggered only for stop loss
        if (orderTrigger != null){
            OrderTrigger.Type triggerType = orderTrigger.getTriggerType();
            if (triggerType != null) {
                switch (triggerType) {
                    case BID:
                        price.setTriggerPriceType(DealingPrice.BID);
                        break;
                    case OFFER:
                        price.setTriggerPriceType(DealingPrice.OFFER);
                        break;
                    case MID:
                        price.setTriggerPriceType(DealingPrice.MID);
                        break;
                    default:
                        LOG.error("Invalid trigger type " + triggerType);
                        break;
                }
            }
            price.setStopLossInitialTriggerRate(orderTrigger.getOriginalTriggerRate());
            price.setStopLossTriggerRate(orderTrigger.getTriggeredByRate());
            price.setStopPrice(orderTrigger.getTriggerRate());
            price.setStopLossTriggerPoints(orderTrigger.getTriggerPoints());
        }
        if (update) {
            price.setFilledAmount(requestLeg.getFilledAmount());
            price.setAverageRate(requestLeg.getAverageRate());
            price.setPriceImprovement(requestLeg.getPriceImprovement());
        }
        
        req.setRequestPrice(priceName, price);
    }

    private void transformOrderFlags(final SingleLegOrder singleLegOrder, final RequestC req) {
        if (singleLegOrder.getOrderFlags() == 0)
            return;
        req.setPersistentOrder(singleLegOrder.isPersist());
        req.setCancelReceived(singleLegOrder.isCancelReceived());
        req.setServerManagedOrder(singleLegOrder.isServerManaged());
        req.setOrderTimedout(singleLegOrder.isTimedOut());
    }

    private void transformEventTimes(final SingleLegOrder singleLegOrder, final RequestC req, final boolean update) {
        req.setExecutionDateTime(new Date(singleLegOrder.getModifiedTime()));
        OrderRequestEventTimes orderReqEventTimes = singleLegOrder.getOrderRequestEventTimes();
        if (orderReqEventTimes == null)
            return;

        if (update) {
            if (orderReqEventTimes.getSentByConsumer() != 0)
                req.setClientSentDateTime(new Date(orderReqEventTimes.getSentByConsumer()));
            if (orderReqEventTimes.getSubmissionTime() != 0)
                req.setSubmissionTime(new Timestamp(orderReqEventTimes.getSubmissionTime()));
            if (orderReqEventTimes.getTriggerReachedAtTime() != 0)
                req.setTriggerReachedAt(new Timestamp(orderReqEventTimes.getTriggerReachedAtTime()));
            if (orderReqEventTimes.getAvailableForMatchingTime() != 0)
                req.setAcceptedDateTime(DateTimeFactory.newDateTime(orderReqEventTimes.getAvailableForMatchingTime()));
            if (orderReqEventTimes.getCancellationTime() != 0)
                req.setCancellationTime(new Timestamp(orderReqEventTimes.getCancellationTime()));
            if (orderReqEventTimes.getExpirationTime() != 0)
                req.setExpirationTime(new Timestamp(orderReqEventTimes.getExpirationTime()));
            if (orderReqEventTimes.getCompletedTime() != 0)
                req.setExecutionDateTime(new Date(orderReqEventTimes.getCompletedTime()));
        } else {
            req.setClientSentDateTime(new Date(orderReqEventTimes.getSentByConsumer()));
            req.setSubmissionTime(new Timestamp(orderReqEventTimes.getSubmissionTime()));
            req.setTriggerReachedAt(new Timestamp(orderReqEventTimes.getTriggerReachedAtTime()));
            req.setAcceptedDateTime(DateTimeFactory.newDateTime(orderReqEventTimes.getAvailableForMatchingTime()));
            req.setCancellationTime(new Timestamp(orderReqEventTimes.getCancellationTime()));
            req.setExpirationTime(new Timestamp(orderReqEventTimes.getExpirationTime()));
        }
    }

    private void transformTimeInForce(final SingleLegOrder singleLegOrder, final RequestC req) {
        TimeInForce tif = singleLegOrder.getTimeInForce();
        if (tif == null)
            return;

        switch (tif) {
            case GTC:
                req.setTimeInForce(com.integral.finance.dealing.TimeInForce.GTC);
                break;
            case GTD:
                req.setTimeInForce(com.integral.finance.dealing.TimeInForce.GTD);
                break;
            case GFS:
                req.setTimeInForce(com.integral.finance.dealing.TimeInForce.GFS);
                break;
            case FOK:
                req.setTimeInForce(com.integral.finance.dealing.TimeInForce.FOK);
                break;
            case DAY:
                req.setTimeInForce(com.integral.finance.dealing.TimeInForce.DAY);
                break;
            case IOC:
                req.setTimeInForce(com.integral.finance.dealing.TimeInForce.IOC);
                break;
            case GTF:
                req.setTimeInForce(com.integral.finance.dealing.TimeInForce.GTF);
                break;

        }
    }

    private void transformClassification(final SingleLegOrder singleLegOrder, final RequestC req) {
    	ProductType productType = singleLegOrder.getProductType();
    	if (ProductType.FXNDF == productType) {
    		req.setTradeClassification(ISUtilImpl.TRD_NDF_CLSF);
    	} else if (ProductType.FXFWD == productType) {
    		req.setTradeClassification(ISUtilImpl.TRD_OUTRIGHT_CLSF);
    	} else if (ProductType.FXSWAP == productType) {
    		req.setTradeClassification(ISUtilImpl.TRD_SWAP_CLSF);
    	} else {
    		req.setTradeClassification(ISUtilImpl.TRD_SPOT_CLSF); 
    	}
          // TODO need a getter not to hard code it
        OrderRequest.Type type = singleLegOrder.getType();
        if (type == null)
            return;

        switch (type){
            case STOP:
                if( (singleLegOrder.getExecutionFlags() & ExecutionFlags.TRAILING) == ExecutionFlags.TRAILING){
                    type = OrderRequest.Type.TLSTOP;
                }
                break;
        }
        RequestClassification singleLetOrderRequestClassification = ISUtilImpl.getInstance().getRequestClassification(type.name());
        LOG.info("OrderRequestClassification " + type.name() + " cached as " + singleLetOrderRequestClassification.getShortName() + ", orderId " + singleLegOrder.get_id());
        req.setRequestClassification(singleLetOrderRequestClassification);       
        // if request is limit and tenor is forward or NDF , set outrightLimitOrder flag as true
        req.setOutrightLimitOrder(singleLegOrder.isOutrightLimitOrder()); 
        req.setSEF(singleLegOrder.isSEF());
    }

    private void transformExecutionInstructions(final SingleLegOrder singleLegOrder, final RequestC req) throws NotificationSerializationException {
        ExecutionInstructions.SecondaryPricePriority secondaryPricePriority = singleLegOrder.getSecondaryPricePriority();
        if (secondaryPricePriority != null) {
            switch (secondaryPricePriority) {
                case SPP:
                    req.setSecondarySortPriority(FixConstants.PROVIDER_PRIORITY);
                    break;
                case SPS:
                    req.setSecondarySortPriority(FixConstants.SIZE_PRIORITY);
                    break;
                case SPT:
                    req.setSecondarySortPriority(FixConstants.TIME_PRIORITY);
                    break;
                case SPTL:
                    req.setSecondarySortPriority(FixConstants.TIME_PRIORITY);
                    break;
                default:
                    throw new NotificationSerializationException("Invalid order strategy SecondaryPricePriority " + secondaryPricePriority, null);
            }
        }

        ExecutionInstructions.CoverExecutionDescriptor orderReqExecInstrCoverExecDesc = singleLegOrder.getCoverExecutionDescriptor();
        if (orderReqExecInstrCoverExecDesc != null) {
            req.setCoverExecutionMethod(orderReqExecInstrCoverExecDesc.getCoverExecutionMethod());
        }
    }

    private void transformOrderStrategy(final SingleLegOrder singleLegOrder, final RequestC req, final boolean update) {
        OrderStrategy orderReqExecInstrStrategy = singleLegOrder.getOrderStrategy();
        if (orderReqExecInstrStrategy == null)
            return;

        req.setOrderExecutionStrategyName(orderReqExecInstrStrategy.getStrategyName());
        req.setTwapSliceInterval(orderReqExecInstrStrategy.getSliceInterval());
        req.setRandomizeTwapSliceInterval(orderReqExecInstrStrategy.isRandomizeSliceInterval());
        req.setTwapMinimumSliceInterval(orderReqExecInstrStrategy.getMinSliceInterval());
        req.setTwapMinimumSliceSize(orderReqExecInstrStrategy.getMinSliceSize());
        req.setTwapSliceSize(orderReqExecInstrStrategy.getSliceSize());
        req.setTwapSliceRegularSize(orderReqExecInstrStrategy.getSliceRegularSize());
        req.setRandomizeTwapSliceSize(orderReqExecInstrStrategy.isRandomizeSliceSize());
        req.setTwapSliceTopOfBookPercent(orderReqExecInstrStrategy.getSliceTOBPercent());
        req.setTwapFOKSlice(orderReqExecInstrStrategy.isSliceFOK());
        req.setPegOffset(orderReqExecInstrStrategy.getPegOffsetValue());
        req.setPegOffsetIncrement(orderReqExecInstrStrategy.getPegOffsetIncrement());
        req.setPegOffsetIncrementInterval(orderReqExecInstrStrategy.getPegOffsetIncrementInterval());
        req.setRandomizePegOffsetIncrement(orderReqExecInstrStrategy.isRandomizePegOffsetIncrement());
        
		if ( orderReqExecInstrStrategy.getOrderExecutionStartTime() > 0 ) {
        	req.setOrderExecutionStartTime(new Timestamp(orderReqExecInstrStrategy.getOrderExecutionStartTime())); 
        }
		if ( orderReqExecInstrStrategy.getOrderExecutionEndTime() > 0 ) {
        	req.setOrderExecutionEndTime(new Timestamp(orderReqExecInstrStrategy.getOrderExecutionEndTime()));     
        } else if(singleLegOrder.getExpireTime() > 0) {
        	req.setOrderExecutionEndTime(new Timestamp(singleLegOrder.getExpireTime()));     
        }

        OrderStrategy.ActionOnExpirationType actionOnExp = orderReqExecInstrStrategy.getActionOnExpiration();
        if (actionOnExp != null) {
            switch (actionOnExp) {
                case FILL_AT_MARKET:
                    req.setActionOnOrderExpitation(ISCommonConstants.TWAP_ACTIONORDEREXPIRY_FillAtMarket);
                    break;
                case CANCEL:
                    req.setActionOnOrderExpitation(ISCommonConstants.TWAP_ACTIONORDEREXPIRY_CancelOrder);
                    break;
                default:
                    LOG.error("Invalid actionOnExp " + actionOnExp + ", orderId " + singleLegOrder.get_id());
                    break;
            }
        }

        OrderStrategy.PegOffsetType pegOffsetType = orderReqExecInstrStrategy.getPegOffsetType();
        if (pegOffsetType != null) {
            switch (pegOffsetType) {
                case MARKET:
                    req.setPegType(PegTypes.MARKET);
                    break;
                case MID:
                    req.setPegType(PegTypes.MID);
                    break;
                case PRIMARY:
                    req.setPegType(PegTypes.PRIMARY);
                    break;
                case TRANSITION:
                    req.setPegType(PegTypes.PRIMARY_TO_MARKET);
                    break;
                default:
                    break;
            }
        }
    }

    private void transformOrderContingency(final SingleLegOrder singleLegOrder, final RequestC req) {
        OrderContingency  orderContingency = singleLegOrder.getOrderContingency();
        if (orderContingency == null)
            return;

        req.setLinkedOrderId(orderContingency.getLinkId());
        OrderContingency.Type ocType = orderContingency.getType();
        if (ocType == null)
            return;

        switch (ocType) {
            case OCO:
                // req.set?(ContingencyType.OCO);               // currently not supported
                break;
            case OTO:
                // req.set?(ContingencyType.OTO);               // currently not supported
                break;
            case OUO:
                // req.set?(ContingencyType.OUO_ABSOLUTE);      // currently not supported
                break;
            case AUTOCOVER:
                // req.set?(ContingencyType.AUTO_COVER);        // currently not supported
                break;
            case COPY:
                // req.set?(ContingencyType.COPY);        // currently not supported
                break;
            case COPY_AUTOCOVER:
            	// req.set?(ContingencyType.COPY_AUTOCOVER);        // currently not supported
            	break;
            case WAREHOUSE_COVER_RISK:
                break;
            default:
                LOG.error("Invalid order contingency type " + ocType + ", orderId " + singleLegOrder.get_id());
                break;
        }
    }

    private void transformPreferredProviders(final SingleLegOrder singleLegOrder, final RequestC req) {
        List<String> preferredProviders = singleLegOrder.getPreferredProviders();
        if ((preferredProviders != null) && !preferredProviders.isEmpty()) {
            String prefProviders = preferredProviders.toString();

            // drop the beginning '[' and ending ']'
            int lastCharIndex = prefProviders.length() - 1;
            req.setPreferredProviders(prefProviders.substring(1, lastCharIndex));
        }
    }
}
