package com.integral.notifications.mis;

import com.integral.finance.dealing.RequestC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.notifications.cache.NotificationCache;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import com.integral.query.QueryCriteria;
import com.integral.query.QueryCriteriaBuilder;
import com.integral.query.QueryCriteriaBuilderC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;

import java.util.Vector;

/**
 * This class is used to keep the RequestC objects which are currently used. Least used RequestC objects
 * are removed from the cache periodically.
 *
 * <AUTHOR>
 */
public class MISRequestExpirableCache extends NotificationCache<RequestC> {
    private final static Log LOG = LogFactory.getLog(MISRequestExpirableCache.class);

    private boolean cacheDisabled = false;

    public MISRequestExpirableCache(int cacheSize,long expirationTimeInMS,int concurrencyLevel,boolean cacheEnabled) {
        super(cacheSize,expirationTimeInMS,concurrencyLevel);
        this.cacheDisabled = !cacheEnabled;
    }

    /**
     * Returns the key to the cache based on the given namespace and id.
     * NB. The id is guaranteed to be unique within the namespace only.
     *
     * @param namespace of the MIS notification object
     * @param id of the object
     * @return key to the cache
     */
    private String getKey(final String namespace, final String id) {
        return namespace + "." + id;
    }

    /**
     * @param req to be cached
     * @return key to the cache
     */
    private String getKey(RequestC req) {
        return getKey(req.getNamespace().getShortName(), req.getOrderId());
    }

    /**
     * Query the database to find the Request object.
     *
     * @param orderId of the Request object
     * @param performanceMetrics null or MIS performance metrics
     * @return found Request object on success or null upon failure
     * @throws PersistenceException
     */
    private RequestC query(final String orderId, MISPerformanceMetrics performanceMetrics) throws PersistenceException {
        QueryCriteriaBuilder builder = new QueryCriteriaBuilderC();
        QueryCriteria criteria = builder.get("");
        Expression exp = new ExpressionBuilder();
        Expression tranExp = exp.get("orderId").equal(orderId);
        Expression quotedExp = exp.get("requestClassification").notEqual(ISUtilImpl.QTQ_ACCET_CLSF);
        criteria.setExpression(tranExp.and(quotedExp));
        ReadAllQuery query = new ReadAllQuery(RequestC.class, criteria.getExpression());

        long startQueryTime = (performanceMetrics != null) ? System.currentTimeMillis() : 0L;
        Vector allObjects = (Vector)PersistenceFactory.newSession().executeQuery(query);
        if (performanceMetrics != null) {
            performanceMetrics.setTargetDbQueryTime(System.currentTimeMillis() - startQueryTime);
        }

        if ((allObjects != null) && (allObjects.size() > 0)) {
            RequestC req = (RequestC)allObjects.get(0);
            add(req);
            return req;
        }

        return null;
    }

    /**
     * Adds the object to the cache by associating the object in the cache with its derived key.
     * If the key already exists in the cache the new object will replace the old one.
     *
     * @param req object to be associated in the cache
     */
    public void add(RequestC req) {
        if (cacheDisabled)
            return;

        String key = getKey(req);
        add(key, req);
        LOG.info("Associated RequestC object with key " + key);
    }

    /**
     * Find the Request object from cache. Only if not present in cache, query the database.
     *
     * @param namespace of the Request object
     * @param orderId of the Request object
     * @param performanceMetrics null or MIS performance metrics
     * @return found Request object on success or null upon failure
     * @throws PersistenceException
     */
    public RequestC get(final String namespace, final String orderId, MISPerformanceMetrics performanceMetrics) throws PersistenceException {
        if (cacheDisabled)
            return query(orderId, performanceMetrics);

        String key = getKey(namespace, orderId);
        RequestC req = (RequestC)get(key);
        if (req == null) {
            LOG.info("Cache miss, key=" + key + " - querying database");
            return query(orderId, performanceMetrics);      // also adds to cache
        }

        if (performanceMetrics != null) {
            performanceMetrics.setTargetDbQueryTime(0L);
        }

        LOG.info("Cache hit, key=" + key);
        return req;
    }

    @Override
    public Class getType() {
        return RequestC.class;
    }
}
