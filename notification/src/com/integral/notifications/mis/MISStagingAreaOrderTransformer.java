package com.integral.notifications.mis;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.eclipse.persistence.indirection.ValueHolderInterface;

import com.integral.time.IdcDateTime;
import com.integral.businessCenter.BusinessCenterC;
import com.integral.finance.counterparty.CounterpartyC;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.creditLimit.CounterpartyCreditLimitRuleC;
import com.integral.finance.creditLimit.CreditLimitClassificationC;
import com.integral.finance.creditLimit.CreditLimitRuleC;
import com.integral.finance.creditLimit.CreditLimitRuleSetC;
import com.integral.finance.creditLimit.CreditUtilizationC;
import com.integral.finance.creditLimit.CreditUtilizationCalculatorC;
import com.integral.finance.creditLimit.CreditUtilizationEventC;
import com.integral.finance.creditLimit.DailyCreditLimitRuleC;
import com.integral.finance.creditLimit.DailyCreditUtilizationC;
import com.integral.finance.creditLimit.SingleCreditLimitRuleC;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.dateGeneration.SettlementDateRuleC;
import com.integral.finance.dealing.OrderC;
import com.integral.finance.dealing.QuoteC;
import com.integral.finance.dealing.QuoteClassificationC;
import com.integral.finance.dealing.RequestClassificationC;
import com.integral.finance.dealing.StagingRequestC;
import com.integral.finance.dealing.StagingRequestLeg;
import com.integral.finance.dealing.StagingRequestLegC;
import com.integral.finance.fx.FXBusinessCalendarC;
import com.integral.finance.fx.FXRateBasisC;
import com.integral.finance.fx.FXRateConventionC;
import com.integral.finance.fx.FXSettlementDateRuleC;
import com.integral.finance.marketData.fx.FXMarketDataElementC;
import com.integral.finance.marketData.fx.FXMarketDataSetC;
import com.integral.finance.trade.TradeClassificationC;
import com.integral.finance.trade.TradeLegClassificationC;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.ExternalSystemC;
import com.integral.persistence.NamespaceC;
import com.integral.persistence.NamespaceGroupC;
import com.integral.persistence.PersistenceException;
import com.integral.rule.RuleC;
import com.integral.rule.RuleSetC;
import com.integral.rule.SetPropertyActionC;
import com.integral.session.IdcTransaction;
import com.integral.staging.Leg;
import com.integral.staging.Order;
import com.integral.user.DisplayPreferenceC;
import com.integral.user.OrganizationC;
import com.integral.user.OrganizationRelationshipClassificationC;
import com.integral.user.UserC;
import com.integral.workflow.ChangeSetC;
import com.integral.workflow.StateC;

/**
 * Created with IntelliJ IDEA.
 * User: vinods
 * Date: 8/4/2016
 * Time: 10:14 AM
 *
 * This singleton is used to transform an object of type
 * com.integral.staging to com.integral.finance.dealing.StagingRequestC
 * to be further serialized.  Therefore, the static and transient fields are ignored.
 */
public class MISStagingAreaOrderTransformer {
	 private final static Log LOG = LogFactory.getLog(MISSingleLegOrderTransformer.class);
	 private final MISStagingAreaExpirableCache stagingAreaExpirableCache;
	 
	 public MISStagingAreaOrderTransformer(MISStagingAreaExpirableCache stagingAreaExpirableCache){
		 this.stagingAreaExpirableCache = stagingAreaExpirableCache;
	 }
	 
	 /**
     * Query the database table with the given orderId only if it is an UPDATE case.
     * If exists, it'll be returned, otherwise it is an INSERT case and it instantiates a new one.
     *
     * @param namespace of the StagingRequest object
     * @param orderId to be used in the query
     * @param appEventCode application event code at the time
     * @param performanceMetrics MIS performance metrics
     * @return a new or the existing record
     * @throws PersistenceException
     */
    public StagingRequestC getPersistentRequestRecord(final String namespace, final String orderId, final ApplicationEventCodes appEventCode, 
    		MISPerformanceMetrics performanceMetrics) throws PersistenceException {
        if (MISHandlerUtil.isStagingOrderUpdateEvent(appEventCode)) {
            StagingRequestC req = queryRequest(namespace, orderId, performanceMetrics);
            if (req != null)
                return req;

            LOG.error("No prior record in database, orderId=" + orderId + " update appEventCode=" + appEventCode.toString());
			MessageLogger.getInstance()
					.log(ISAlertMBean.ORDER_NOT_FOUND_IN_DB,
							this.getClass().getName(),
							"No prior record in database, orderId=" + orderId
									+ " update appEventCode="
									+ appEventCode.toString(), null);
            throw new PersistenceException();
        }

        return new StagingRequestC();      // ThreadLocal is overkill as these are cached
    }
    
    public StagingRequestC queryRequest(String namespace, String orderId, MISPerformanceMetrics performanceMetrics) throws PersistenceException {
        return stagingAreaExpirableCache.get(namespace, orderId, performanceMetrics);
    }
    
    /**
     * Add readonly classes required for persisting the transaction.
     * These are the classes which will not change by insert/update.
     * That includes all the reference data.
     *
     * @param tx transaction id
     */
    public void addReadOnlyClasses(final IdcTransaction tx) {
        tx.addReadOnlyClass(BusinessCenterC.class);
        tx.addReadOnlyClass(ChangeSetC.class);
        tx.addReadOnlyClass(CounterpartyC.class);
        tx.addReadOnlyClass(CounterpartyCreditLimitRuleC.class);
        tx.addReadOnlyClass(CreditLimitClassificationC.class);
        tx.addReadOnlyClass(CreditLimitRuleC.class);
        tx.addReadOnlyClass(CreditLimitRuleSetC.class);
        tx.addReadOnlyClass(CreditUtilizationC.class);
        tx.addReadOnlyClass(CreditUtilizationCalculatorC.class);
        tx.addReadOnlyClass(CreditUtilizationEventC.class);
        tx.addReadOnlyClass(CurrencyC.class);
        tx.addReadOnlyClass(DailyCreditLimitRuleC.class);
        tx.addReadOnlyClass(DailyCreditUtilizationC.class);
        tx.addReadOnlyClass(Date.class);
        tx.addReadOnlyClass(DisplayPreferenceC.class);
        tx.addReadOnlyClass(ExternalSystemC.class);
        tx.addReadOnlyClass(FXBusinessCalendarC.class);
        tx.addReadOnlyClass(FXMarketDataElementC.class);
        tx.addReadOnlyClass(FXMarketDataSetC.class);
        tx.addReadOnlyClass(FXRateBasisC.class);
        tx.addReadOnlyClass(FXRateConventionC.class);
        tx.addReadOnlyClass(FXSettlementDateRuleC.class);
        tx.addReadOnlyClass(IdcDateTime.class);
        tx.addReadOnlyClass(LegalEntityC.class);
        tx.addReadOnlyClass(NamespaceC.class);
        tx.addReadOnlyClass(NamespaceGroupC.class);
        tx.addReadOnlyClass(OrderC.class);
        tx.addReadOnlyClass(OrganizationC.class);
        tx.addReadOnlyClass(OrganizationRelationshipClassificationC.class);
        tx.addReadOnlyClass(QuoteC.class);
        tx.addReadOnlyClass(QuoteClassificationC.class);
        tx.addReadOnlyClass(RequestClassificationC.class);
        tx.addReadOnlyClass(RuleC.class);
        tx.addReadOnlyClass(RuleSetC.class);
        tx.addReadOnlyClass(SetPropertyActionC.class);
        tx.addReadOnlyClass(SettlementDateRuleC.class);
        tx.addReadOnlyClass(SingleCreditLimitRuleC.class);
        tx.addReadOnlyClass(StateC.class);
        tx.addReadOnlyClass(Timestamp.class);
        tx.addReadOnlyClass(TradeClassificationC.class);
        tx.addReadOnlyClass(TradeLegClassificationC.class);
        tx.addReadOnlyClass(TradingPartyC.class);
        tx.addReadOnlyClass(UserC.class);
    }
    
    
    /**
     * Transforms an object of type com.integral.staging.Order to com.integral.finance.dealing.StagingRequestC
     * the transient and static fields of both objects are ignored.
     *
     * @param order object to be transformed
     * @throws Exception
     */
    public void transform(final StagingRequestC sReq, final Order order, final ApplicationEventCodes appEventCode) throws Exception {
        LOG.info(order.getClass().getName() + " ==> " + sReq.getClass().getName() + " orderId=" + order.get_id() + " appEventCode=" + appEventCode.toString());

        final boolean update = MISHandlerUtil.isStagingOrderUpdateEvent(appEventCode);
        if (!update) {
            // start from a clean slate.
            sReq.resetTransients();
            sReq.resetFacadeTransients();
            sReq.resetTransientProperties();
            sReq.resetStickyProperties();
        }
        
        sReq.setNamespace(order.getNamespace());
        sReq.setOrdId(order.get_id()) ;
        sReq.setClOrdId(order.getClientOrderId()) ;				
    	sReq.setAccount(order.getAccount()) ;					
    	sReq.setSource(order.getSource()) ;
    	sReq.setMtfTrade( order.getMifidParameters() != null && order.getMifidParameters().size() > 0 );
    	sReq.setCcyPair(order.getCcyPair()) ;	
    	sReq.setSide(order.isBuy()) ;	
    	sReq.setDealtCcy(order.getDealtCcy()) ;
    	sReq.setDealtAmt(order.getDealtAmt()) ;	
    	sReq.setTradeDate(order.getTradeDate() == 0 ? null : new Timestamp(order.getTradeDate())) ;	
    	sReq.setValueDate((order.getValueDate() == null || order.getValueDate() == 0) ? null : new Timestamp(order.getValueDate())) ;
    	sReq.setState(order.getState()) ;
    	sReq.setUserName(order.getUserName()) ;
    	sReq.setInputCC(order.getInputCC()) ;
    	sReq.setPfId(order.getPfId()) ;
    	sReq.setOmsOrder(order.isOmsOrder()) ;
    	sReq.settId(order.getTransactionId()) ; 	
    	sReq.setBenchMarkRate1(Double.isNaN(order.getOrdSubmissionBMRate()) ? 0.0d  : order.getOrdSubmissionBMRate()) ;
    	sReq.setBenchMarkRate2(Double.isNaN(order.getOrdImportBMRate()) ? 0.0d  : order.getOrdImportBMRate()) ;
    	sReq.setBenchMarkRate3(Double.isNaN(order.getGtmOrdSubmissionBMRate()) ? 0.0d  : order.getGtmOrdSubmissionBMRate()) ;
    	sReq.setBidSpotRate(Double.isNaN(order.getWtAvgTOBBidSpotRate())  ? 0.0d : order.getWtAvgTOBBidSpotRate()) ;
    	sReq.setPreFeeAllInRate(Double.isNaN(order.getTradeRateBeforeFee()) ? 0.0d : order.getTradeRateBeforeFee());
    	sReq.setOfferSpotRate(Double.isNaN(order.getWtAvgTOBOfferSpotRate()) ? 0.0d : order.getWtAvgTOBOfferSpotRate()) ;
    	sReq.setImportTime(order.getImportTime() == 0 ? null : new Timestamp(order.getImportTime()));
    	sReq.setOrdCreationTime(order.getCreatedTime() == 0 ? null : new Timestamp(order.getCreatedTime()));
    	sReq.setExecutedCC(order.getExecutedCC());
    	sReq.setOrg(order.getNamespaceName());
    	String reason = order.getReason();
    	if (reason != null && reason.length() > 255) {
    		// truncating beyond 255 characters
    		reason = reason.substring(0, 255);
    	}
    	sReq.setReason(reason);
    	// populate legs data on the registered legs 
    	Collection<StagingRequestLeg> registeredLegs = sReq.getLegs();
    	if (registeredLegs != null && registeredLegs.size() > 0)
    	{
    		for (StagingRequestLeg leg : registeredLegs)
    		{
  				StagingRequestLeg registeredLeg = (StagingRequestLeg)leg.getRegisteredObject();
    			Timestamp valueDate = registeredLeg.getValueDate();
    			Leg orderLeg = getLegFromOrder(valueDate.getTime(), order);
    			if (orderLeg != null)
    			{
    				Double settleAmtd = orderLeg.getSettlAmt();
    				String legPrice = orderLeg.getLegPrice();
    				String legForwardPoint = orderLeg.getLegForwardPoint();    				
    				registeredLeg.setSettlAmt(settleAmtd);
    				registeredLeg.setLegPrice(legPrice);
    				registeredLeg.setLegForwardPoint(legForwardPoint);
    			}
    		}
    	}
    	else
    	{
    		// first insert.
    		List<Leg> orderLegs = order.getLegs();
    		if (orderLegs != null && orderLegs.size() > 0)
    		{
    			List<StagingRequestLeg> legs = new ArrayList<StagingRequestLeg>(orderLegs.size());
    			for (Leg orderLeg : orderLegs)
    			{    				
    				StagingRequestLeg stagingRequestLeg = new StagingRequestLegC();  
    				stagingRequestLeg.set_id(orderLeg.get_id());
    				stagingRequestLeg.setNamespace(order.getNamespace());
    				stagingRequestLeg.setOrderId(orderLeg.getOrderId());
    				stagingRequestLeg.setDealtAmt(orderLeg.getDealtAmt());
    				stagingRequestLeg.setSettlAmt(orderLeg.getSettlAmt());
					Timestamp valueDate = new Timestamp(orderLeg.getValueDate());
    				stagingRequestLeg.setValueDate(valueDate);
    				stagingRequestLeg.setTenor(orderLeg.getTenor());
    				stagingRequestLeg.setBuy(orderLeg.getBuy());
    				stagingRequestLeg.setAccount(orderLeg.getAccount());
    				stagingRequestLeg.setLegPrice(orderLeg.getLegPrice());
    				stagingRequestLeg.setLegForwardPoint(orderLeg.getLegForwardPoint());
    				legs.add(stagingRequestLeg);
    			}
    			sReq.setLegs(legs);
    		}
    	}
    }
    private Leg getLegFromOrder(final long valuedate, final Order order)
    {
    	List<Leg> legs = order.getLegs();
    	if (legs == null || legs.size() == 0)
    	{
    		return null;
    	}
    	for (Leg leg : legs)
    	{
    		if (valuedate == leg.getValueDate())
    		{
    			return leg;
    		}
    	}
    	return null;
    }
}
