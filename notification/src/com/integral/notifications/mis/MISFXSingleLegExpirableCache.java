package com.integral.notifications.mis;

import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSingleLegC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.notifications.cache.NotificationCache;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import com.integral.query.QueryCriteria;
import com.integral.query.QueryCriteriaBuilder;
import com.integral.query.QueryCriteriaBuilderC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;

import java.util.Vector;

/**
 * This class is used to keep the FXSingleLeg objects which are currently used. Least used FXSingleLeg objects
 * are removed from the cache periodically.
 *
 * <AUTHOR>
 */
public class MISFXSingleLegExpirableCache extends NotificationCache<FXSingleLeg> {
    private final static Log LOG = LogFactory.getLog(MISFXSingleLegExpirableCache.class);

    private boolean cacheDisabled = false;

    public MISFXSingleLegExpirableCache(int cacheSize,long expirationTimeInMS,int concurrencyLevel,boolean cacheEnabled) {
        super( cacheSize,expirationTimeInMS,concurrencyLevel);
        this.cacheDisabled = !cacheEnabled;
    }

    /**
     * Returns the key to the cache based on the given namespace and id.
     * NB. The id is guaranteed to be unique within the namespace only.
     *
     * @param namespace of the MIS notification object
     * @param id of the object
     * @return key to the cache
     */
    private String getKey(final String namespace, final String id) {
        return namespace + "." + id;
    }

    /**
     * @param trd to be cached
     * @return key to the cache
     */
    private String getKey(final FXSingleLeg trd) {
        return getKey(trd.getNamespace().getShortName(), trd.getTransactionID());
    }

    /**
     * Query the database to find the FXSingleLeg object.
     *
     * @param transactionID of the FXSingleLeg object
     * @param performanceMetrics null or MIS performance metrics
     * @return found FXSingleLeg object on success or null upon failure
     * @throws PersistenceException
     */
    private FXSingleLeg query(final String transactionID, MISPerformanceMetrics performanceMetrics) throws PersistenceException {
        QueryCriteriaBuilder builder = new QueryCriteriaBuilderC();
        QueryCriteria criteria = builder.get("");
        Expression exp = new ExpressionBuilder();
        Expression tranExp = exp.get("transactionID").equal(transactionID);
        criteria.setExpression(tranExp);
        ReadAllQuery query = new ReadAllQuery(FXSingleLegC.class, criteria.getExpression());

        long startQueryTime = (performanceMetrics != null) ? System.currentTimeMillis() : 0L;
        Vector allObjects = (Vector) PersistenceFactory.newSession().executeQuery(query);
        if (performanceMetrics != null) {
            performanceMetrics.setTargetDbQueryTime(System.currentTimeMillis() - startQueryTime);
        }

        if ((allObjects != null) && (allObjects.size() > 0)) {
            FXSingleLeg fxSingleLeg = (FXSingleLeg)allObjects.get(0);
            add(fxSingleLeg);
            return fxSingleLeg;
        }

        return null;
    }

    /**
     * Adds the object to the cache by associating the object in the cache with its derived key.
     * If the key already exists in the cache the new object will replace the old one.
     *
     * @param trd object to be associated in the cache
     */
    public void add(FXSingleLeg trd) {
        if (cacheDisabled)
            return;

        String key = getKey(trd);
        add(key, trd);
        LOG.info("Associated FXSingleLeg object with key " + key);
    }

    /**
     * Find the FXSingleLeg object from cache. Only if not present in cache, query the database.
     *
     * @param namespace of the FXSingleLeg object
     * @param transactionID of the FXSingleLeg object
     * @param performanceMetrics null or MIS performance metrics
     * @return found FXSingleLeg object on success or null upon failure
     * @throws PersistenceException
     */
    public FXSingleLeg get(final String namespace, final String transactionID, MISPerformanceMetrics performanceMetrics) throws PersistenceException {
        if (cacheDisabled)
            return query(transactionID, performanceMetrics);

        String key = getKey(namespace, transactionID);
        FXSingleLeg trd = get(key);
        if (trd == null) {
            LOG.info("Cache miss, key=" + key + " - querying database");
            return query(transactionID, performanceMetrics);        // also adds to cache
        }

        if (performanceMetrics != null) {
            performanceMetrics.setTargetDbQueryTime(0L);
        }

        LOG.info("Cache hit, key=" + key);
        return trd;
    }

    @Override
    public Class getType() {
        return FXSingleLeg.class;
    }
}

