package com.integral.notifications.mis;

import com.integral.notifications.cache.Cache;
import com.integral.notifications.cache.NotificationCache;

import java.util.HashMap;
import java.util.Map;

/**
 * <PERSON><PERSON> used to monitor the expirable cache details.
 *
 * <AUTHOR>
 */
public class MISExpirableCacheMonitor {
    private boolean cacheDisabled = false;
    private Map<String, Cache> cacheMap = new HashMap<String, Cache>();

    private MISExpirableCacheMonitor() {
    }

    private static class MISExpirableCacheMonitorHolder {
        private final static MISExpirableCacheMonitor INSTANCE = new MISExpirableCacheMonitor();
    }

    public static MISExpirableCacheMonitor getInstance() {
        return MISExpirableCacheMonitorHolder.INSTANCE;
    }

    public boolean isCacheDisabled() {
        return cacheDisabled;
    }

    public void setCacheDisabled(boolean cacheDisabled) {
        this.cacheDisabled = cacheDisabled;
    }

    public void add(String name, NotificationCache expirableCache) {
        cacheMap.put(name, expirableCache);
    }

    public String recordCount() {
        if (cacheDisabled) {
            return " 0 (caching disabled)";
        }

        if (cacheMap.size() == 0) {
            return " None";
        }

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Cache> c : cacheMap.entrySet()) {
            sb.append(" ").append(c.getKey()).append(" = ").append(c.getValue().size());
        }

        return sb.toString();
    }
}
