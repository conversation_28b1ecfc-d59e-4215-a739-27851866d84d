package com.integral.notifications.mis;

import com.integral.is.common.ApplicationEventCodes;

/**
 * Utility class for MIS notification handlers.
 */
public class MISHandlerUtil {
    /**
     *
     * @param appEventCode {@link com.integral.is.common.ApplicationEventCodes}
     * @return true if the appEventCode is an update event for {@link com.integral.model.dealing.OrderRequest} object.
     */
    public static boolean isOrderUpdateEvent(final ApplicationEventCodes appEventCode) {
        return appEventCode != ApplicationEventCodes.EVENT_ESP_ORDER_SUBMIT;
    }
    
    public static boolean isStagingOrderUpdateEvent(final ApplicationEventCodes appEventCode) {
    	return appEventCode != ApplicationEventCodes.EVENT_SA_ORDER_SUBMIT;
    }

    /**
     *
     * @param appEventCode {@link com.integral.is.common.ApplicationEventCodes}
     * @return true if the appEventCode is an update event for {@link com.integral.model.dealing.Trade} object.
     */
    public static boolean isTradeUpdateEvent(final ApplicationEventCodes appEventCode) {
        switch ( appEventCode ){
            case EVENT_ESP_TRADE_ACCEPT:
            case EVENT_ESP_TRADE_CREATED_VERIFIED:
            case EVENT_ESP_TRADE_CREATED_PRE_VERIFIED:
            case EVENT_ESP_TRADE_NET:
            case EVENT_ESP_TRADE_PRE_RATE_VERIFIED:
            case EVENT_ESP_TRADE_POST_RATE_VERIFIED:
                return false;
            default:
                return true;
        }
    }
}
