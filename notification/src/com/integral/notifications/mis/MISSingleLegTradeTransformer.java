package com.integral.notifications.mis;

import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.Stream;
import com.integral.businessCenter.BusinessCenterC;
import com.integral.exception.IdcNoSuchObjectException;
import com.integral.finance.counterparty.CounterpartyC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.finance.counterparty.TradingPartyC;
import com.integral.finance.creditLimit.*;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyC;
import com.integral.finance.dateGeneration.SettlementDateRuleC;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.*;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.dealing.mifid.MiFIDUtils;
import com.integral.finance.fx.*;
import com.integral.finance.marketData.fx.FXMarketDataElementC;
import com.integral.finance.marketData.fx.FXMarketDataSetC;
import com.integral.finance.trade.*;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.MarketSnapshotUtil;
import com.integral.is.common.util.TradeUtil;
import com.integral.is.configuration.ISCommonConfigFactory;
import com.integral.is.configuration.ISCommonMBean;
import com.integral.is.spaces.fx.esp.service.CounterPartyTradeService;
import com.integral.is.spaces.fx.esp.service.FXESPServiceFactory;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.is.spaces.fx.persistence.DealingModelSpacesCacheManager;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.multihost.MultihostMBean;
import com.integral.model.AccountID;
import com.integral.model.dealing.TradeLeg;
import com.integral.model.dealing.*;
import com.integral.model.dealing.descriptor.CoveredTradeDescriptor;
import com.integral.model.dealing.descriptor.OriginatingOrderRequestDescriptor;
import com.integral.model.dealing.descriptor.OriginatingTradeDescriptor;
import com.integral.persistence.*;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.persistence.spaces.PersistenceConstants;
import com.integral.rule.RuleC;
import com.integral.rule.RuleSetC;
import com.integral.rule.SetPropertyActionC;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.time.IdcDateTime;
import com.integral.user.*;
import com.integral.util.IdcUtilC;
import com.integral.util.MathUtilC;
import com.integral.util.StringUtilC;
import com.integral.workflow.ChangeSetC;
import com.integral.workflow.StateC;
import com.integral.workflow.WorkflowStateMap;
import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 *
 * This singleton is used to transform an object of type
 * com.integral.model.dealing.SingleLegTrade to com.integral.finance.fx.FXSingleLegC
 * to be further serialized.  Therefore, the static and transient fields are ignored.
 */
public class MISSingleLegTradeTransformer {
    private final static Log LOG = LogFactory.getLog(MISSingleLegTradeTransformer.class);
    private final MISRequestExpirableCache requestExpirableCache;
    private final MISFXSingleLegExpirableCache singleLegExpirableCache;
    private MultihostMBean mhMBean = ISFactory.getInstance().getMultihostMBean();
    private static final ISCommonMBean isCommonMbean = ISCommonConfigFactory.getISCommonMBean();


    public MISSingleLegTradeTransformer(MISRequestExpirableCache requestExpirableCache, MISFXSingleLegExpirableCache singleLegExpirableCache) {
        this.requestExpirableCache = requestExpirableCache;
        this.singleLegExpirableCache = singleLegExpirableCache;
    }

    /**
     * Query the database table with the given orderId only if it is an UPDATE case.
     * If exists, it'll be returned, otherwise it is an INSERT case and it instantiates a new one.
     *
     * @param namespace of the FxSingleLeg object
     * @param tid to be used in the query
     * @param appEventCode application event code
     * @return a new or the existing record
     * @throws PersistenceException
     */
    public FXSingleLeg getPersistentTradeRecord(final String namespace, final String tid, final ApplicationEventCodes appEventCode, MISPerformanceMetrics performanceMetrics) throws PersistenceException {
        if (MISHandlerUtil.isTradeUpdateEvent(appEventCode)) {
            FXSingleLeg trd = queryTrade(namespace, tid, performanceMetrics);
            if (trd != null)
                return trd;

            LOG.error("No prior record in database, tid=" + tid + " update appEventCode=" + appEventCode.toString());
            throw new PersistenceException();
        }

        return new FXFactory().newFXSingleLeg();      // ThreadLocal is overkill as these are cached
    }

    public FXSingleLeg queryTrade(String namespace, String tid, MISPerformanceMetrics performanceMetrics) throws PersistenceException {
        return singleLegExpirableCache.get(namespace, tid, performanceMetrics);
    }

    /**
     * Add readonly classes required for persisting the transaction.
     * These are the classes which will not change by insert/update.
     * That includes all the reference data.
     *
     * @param tx transaction id
     */
    public void addReadOnlyClasses(final IdcTransaction tx) {
        tx.addReadOnlyClass(BusinessCenterC.class);
        tx.addReadOnlyClass(ChangeSetC.class);
        tx.addReadOnlyClass(CounterpartyC.class);
        tx.addReadOnlyClass(CounterpartyCreditLimitRuleC.class);
        tx.addReadOnlyClass(CreditLimitClassificationC.class);
        tx.addReadOnlyClass(CreditLimitRuleC.class);
        tx.addReadOnlyClass(CreditLimitRuleSetC.class);
        tx.addReadOnlyClass(CreditUtilizationC.class);
        tx.addReadOnlyClass(CreditUtilizationCalculatorC.class);
        tx.addReadOnlyClass(CreditUtilizationEventC.class);
        tx.addReadOnlyClass(CurrencyC.class);
        tx.addReadOnlyClass(DailyCreditLimitRuleC.class);
        tx.addReadOnlyClass(DailyCreditUtilizationC.class);
        tx.addReadOnlyClass(Date.class);
        tx.addReadOnlyClass(DisplayPreferenceC.class);
        tx.addReadOnlyClass(ExternalSystemC.class);
        tx.addReadOnlyClass(FXBusinessCalendarC.class);
        tx.addReadOnlyClass(FXMarketDataElementC.class);
        tx.addReadOnlyClass(FXMarketDataSetC.class);
        tx.addReadOnlyClass(FXRateBasisC.class);
        tx.addReadOnlyClass(FXRateConventionC.class);
        tx.addReadOnlyClass(FXSettlementDateRuleC.class);
        tx.addReadOnlyClass(IdcDateTime.class);
        tx.addReadOnlyClass(LegalEntityC.class);
        tx.addReadOnlyClass(NamespaceC.class);
        tx.addReadOnlyClass(NamespaceGroupC.class);
        tx.addReadOnlyClass(OrderC.class);
        tx.addReadOnlyClass(OrganizationC.class);
        tx.addReadOnlyClass(OrganizationRelationshipClassificationC.class);
        tx.addReadOnlyClass(QuoteC.class);
        tx.addReadOnlyClass(QuoteClassificationC.class);
        tx.addReadOnlyClass(RequestClassificationC.class);
        tx.addReadOnlyClass(RuleC.class);
        tx.addReadOnlyClass(RuleSetC.class);
        tx.addReadOnlyClass(SetPropertyActionC.class);
        tx.addReadOnlyClass(SettlementDateRuleC.class);
        tx.addReadOnlyClass(SingleCreditLimitRuleC.class);
        tx.addReadOnlyClass(StateC.class);
        tx.addReadOnlyClass(Timestamp.class);
        tx.addReadOnlyClass(TradeClassificationC.class);
        tx.addReadOnlyClass(TradeLegClassificationC.class);
        tx.addReadOnlyClass(TradingPartyC.class);
        tx.addReadOnlyClass(UserC.class);
    }

    public  void transform(final String namespace,final FXSingleLegC trd, final SingleLegTrade singleLegTrade, final ApplicationEventCodes appEventCode) throws Exception {
        LOG.info(singleLegTrade.getClass().getName() + " ==> " + trd.getClass().getName() + " tid=" + singleLegTrade.get_id() + " appEventCode=" + appEventCode.toString());

        SingleLegOrderMatch matchEvent = singleLegTrade.getMatchEvent();
        if (matchEvent == null)
            throw  new NullPointerException("matchEvent");

        final boolean update = MISHandlerUtil.isTradeUpdateEvent(appEventCode);
        transform( namespace, trd, singleLegTrade, appEventCode, update);
    }

    public  void transform(final String namespace,final FXSingleLegC trd, final SingleLegTrade singleLegTrade, final ApplicationEventCodes appEventCode, final boolean update) throws Exception {
        LOG.info(singleLegTrade.getClass().getName() + " ==> " + trd.getClass().getName() + " tid=" + singleLegTrade.get_id() + " appEventCode=" + appEventCode.toString() + "   trade : " + singleLegTrade );

        SingleLegOrderMatch matchEvent = singleLegTrade.getMatchEvent();
        if (matchEvent == null)
            throw  new NullPointerException("matchEvent");

        if (!update) {
            // start from a clean slate.
            trd.resetTransients();
            trd.resetTransientProperties();
            trd.resetFacadeTransients();
            trd.resetStickyProperties();

            trd.setTransactionID(singleLegTrade.get_id());
        }

        /*
        * Transforming the inherited fields from DealingModel first.
        */
        trd.setNamespace(singleLegTrade.getNamespace());
        trd.setStatus(singleLegTrade.getStatus());
        trd.setVirtualServer(singleLegTrade.getVirtualServer());
        trd.setPortfolioRefId( singleLegTrade.getPortfolioId() );
        trd.setOriginatingPortfolioId(singleLegTrade.getOriginatingPortfolioId());
        trd.setModifiedDate(new Date(singleLegTrade.getModifiedTime()));
        trd.setOcx2(singleLegTrade.isOCX2());
        Timestamp createdTime = new Timestamp(singleLegTrade.getCreatedTime());
        trd.setCreatedTimestamp(createdTime);
        trd.setEntryDateTime(DateTimeFactory.newDateTime(createdTime));
        trd.setOrderNotes(singleLegTrade.getOrderNotes());
        /*
         * singleLegTrade non-inherited fields to FXSingleLegC
         */
        trd.setTradeDate(DateTimeFactory.newDate(new Date(singleLegTrade.getTradeDate())));
        trd.setExecutionDateTime(new Date(singleLegTrade.getExecutionTime()));

        long orderMatchedByServer = matchEvent.getMatchEventTimes().getOrderMatchedByServer();
        if( orderMatchedByServer == 0L ){
            orderMatchedByServer = singleLegTrade.getCreatedTime();
        }
        trd.setBusinessExecutionDate( new Timestamp( orderMatchedByServer ) );
        trd.setConfirmationDateTime(new Date(singleLegTrade.getConfirmationTime()));
        trd.setMakerUser(singleLegTrade.getMakerUser());
        trd.setTakerReferenceId(singleLegTrade.getOrderRequest().get_id());
        trd.setManualTrade(singleLegTrade.isManual());
        trd.setMakerReferenceId(singleLegTrade.getMakerRefId());
        trd.setUPI(singleLegTrade.getUPI());
        trd.setUSI(singleLegTrade.getUSI());
        trd.setCounterpartyALEI(singleLegTrade.getCounterpartyALEI());
        trd.setCounterpartyBLEI(singleLegTrade.getCounterpartyBLEI());
        trd.setChannel(singleLegTrade.getChannel());
        trd.setFreshQuoteUsed(singleLegTrade.isBestQuoteUsed());
        trd.setEntryUser(singleLegTrade.getUser());
        trd.setMakerMarketSnapshot(MarketSnapshotUtil.convertToOldSnapshot(singleLegTrade.getMakerMarketSnapShot()));
        if(!StringUtilC.isNullOrEmpty(singleLegTrade.getExternalReferenceId()))
        {
            populateExternalSystemId(trd, ISConstantsC.EXT_REQUEST_ID, singleLegTrade.getExternalReferenceId());
        }

        if(!StringUtilC.isNullOrEmpty(singleLegTrade.get_id()))
        {
            populateExternalSystemId(trd, ISConstantsC.EXT_IS_TRANSACTION_ID, singleLegTrade.get_id());
        }

        String netTradeId = singleLegTrade.getNetTradeId();
        if(netTradeId != null && appEventCode == ApplicationEventCodes.EVENT_ESP_TRADE_NETTED){
            FXSingleLeg netTrade = getPersistentTradeRecord(namespace, netTradeId, ApplicationEventCodes.EVENT_ESP_TRADE_NETTED, new MISPerformanceMetrics());
            FXSingleLeg registeredNetTrade = (FXSingleLeg)netTrade.getRegisteredObject();
            trd.setNetTrade(registeredNetTrade);
        }

        // Copy the maker order id from SingleLegTrade to non-spaces trade.
        if(!singleLegTrade.getMatchEvent().isDirectedOrder()){
            trd.setMakerOrderId(singleLegTrade.getMakerOrderId());
            trd.setMakerOrderUserChannel( singleLegTrade.getMakerOrderChannel() );
            if( singleLegTrade.getMakerOrderDealtCurrency() != null ){
                trd.setMakerOrderDealtCurrency( singleLegTrade.getMakerOrderDealtCurrency() );
            }
        }else{
            updateMakerDetailsForDirectedOrder(trd,singleLegTrade);
        }
//        trd.setNote(singleLegTrade.get?());                            // TBD workflow not implemented
//        trd.setNetTrade(singleLegTrade.get?());                       // TBD workflow not implemented
//        trd.setMakerMarketSnapshot(singleLegTrade.get?());             // TBD workflow not implemented
//        trd.setClearingHouse(singleLegTrade.get?());                   // TBD workflow not implemented
//        trd.setClearingTime(singleLegTrade.get?());                    // TBD workflow not implemented
//        trd.setSDR(singleLegTrade.get?());                             // TBD workflow not implemented
        boolean isTakerSalesDealer = !singleLegTrade.getNamespace().isSameAs(singleLegTrade.getUser().getNamespace());
        if (isTakerSalesDealer) {
            trd.setSalesDealerCounterparty(singleLegTrade.getUser().getDefaultDealingEntity());
            trd.setSalesDealerUser(singleLegTrade.getUser());
        }
        LegalEntity counterpartyLE = DealingModelUtil.getCounterPartyLegalEntity( singleLegTrade);
        boolean isMakerSalesDealer = !counterpartyLE.getNamespace().isSameAs(singleLegTrade.getMakerUser().getNamespace());
        if (isMakerSalesDealer) {
            trd.setMakerSalesDealerCounterparty(singleLegTrade.getMakerUser().getDefaultDealingEntity());
            trd.setMakerSalesDealerUser(singleLegTrade.getMakerUser());
        }

        trd.setSweepNumber(matchEvent.getSweepNumber());

        transformState(singleLegTrade, trd, appEventCode);
        transformTradeLeg(singleLegTrade, trd, matchEvent, appEventCode, update);
        trd.setCounterpartyAFirm(singleLegTrade.getCounterpartyAFirm());
        trd.setCounterpartyBFirm(singleLegTrade.getCounterpartyBFirm());
        trd.setCounterpartyCFirm(singleLegTrade.getCounterpartyCFirm());
        trd.setCounterpartyDFirm(singleLegTrade.getCounterpartyDFirm());
        // there should not be any modifications to cptyTrades during confirmation workflow.
        if ( ApplicationEventCodes.EVENT_ESP_TRADE_CONFIRMED != appEventCode )
        {
            transformCptyTrades( singleLegTrade, trd);
        }

        transformTradeClassification(singleLegTrade, trd);
        transformOriginatingTrade(singleLegTrade, trd);
        if(matchEvent.getMatchEventPQ() != null){
            transformQuotedRequest(singleLegTrade, trd, matchEvent, update, counterpartyLE );
        }else if(matchEvent.isDirectedOrder()){
            trd.setVenueName( matchEvent.getMatchingVenue().getShortName() );
            trd.setAggresorInVenue(singleLegTrade.isVenueAgressorTrade());
            transformQuotedRequestDirectedOrder(singleLegTrade, trd, matchEvent, update, counterpartyLE );
        }
        transformEventTimes(singleLegTrade, trd, matchEvent);
        transformClientTag(trd, matchEvent);
        transformNextQuoteDetails(trd,matchEvent);
        if(trd.getRequest() != null && matchEvent != null)
        {
        	if(singleLegTrade.getVenueBMR() != 0.0)
        	{
        		trd.getRequest().setGridMidRate(singleLegTrade.getVenueBMR());
            	trd.setMidBenchmarkRate1(singleLegTrade.getVenueBMR());
            	trd.setMidBenchmarkRate2(singleLegTrade.getVenueBMR());
        	}
            else {
                trd.getRequest().setGridMidRate( matchEvent.getGridMidRate() );
                trd.setMidBenchmarkRate1( matchEvent.getGridMidRate() );
                trd.setMidBenchmarkRate2( matchEvent.getGridMidRate() );
            }
            MiFIDUtils.setMidBenchmarkAllinRate( trd );
        }
        if (trd.getRequest() != null && singleLegTrade.getOrderRequest() != null && singleLegTrade.getOrderRequest().getRequestChannel() != null) {
            trd.getRequest().setChannel(ISUtilImpl.getInstance().getExtSys(singleLegTrade.getOrderRequest().getRequestChannel()));

            if(!StringUtilC.isNullOrEmpty(singleLegTrade.getOrderRequestClientReferenceId()))
            {
                populateExternalSystemId(trd, singleLegTrade.getOrderRequest().getRequestChannel(), singleLegTrade.getOrderRequestClientReferenceId());
            }
        }
        String toOrg = counterpartyLE.getOrganization().getShortName();
        if(!StringUtilC.isNullOrEmpty(toOrg))
        {
            populateExternalSystemId(trd, toOrg, toOrg);
        }
        if (trd.getRequest() != null && singleLegTrade.getOrderRequest() != null) {
            trd.getRequest().setNotes(singleLegTrade.getOrderRequest().getSourceMessageId() + "#");
        }
        if(singleLegTrade.isSyntheticCross())
        {
            trd.setSyntheticCross(true);
            trd.setVehicleCCY(singleLegTrade.getVehicleCurrency());
            trd.setVehicleCCYAmount(singleLegTrade.getVehicleCCYAmount());
            trd.setForeignCurrencyPair(singleLegTrade.getForeignCurrencyPair());
            trd.setLocalCurrencyPair(singleLegTrade.getLocalCurrencyPair());
            trd.setPrimaryDealtCcyId(singleLegTrade.getPrimaryDealtCcyId());
            trd.setSecondaryDealtCcyId(singleLegTrade.getSecondaryDealtCcyId());
            if (LOG.isDebugEnabled()) {
            	LOG.debug("trade.isSyntheticCross()=true " +
            			", trd.getVehicleCCY()=" + trd.getVehicleCCY() +
            			", trd.getVehicleCCYAmount()=" + trd.getVehicleCCYAmount() +
            			", trd.getForeignCurrencyPair()=" + trd.getForeignCurrencyPair() +
            			", trd.getLocalCurrencyPair()=" + trd.getLocalCurrencyPair());
            }
        }
        trd.setSyntheticCrossComponent(singleLegTrade.getSyntheticCrossComponent());
        trd.setBBookTrade(singleLegTrade.isBbookTrade());
        trd.setFullAmountTrade(singleLegTrade.isFullAmountTrade());
        trd.setCptyABookName(singleLegTrade.getCptyABookName());
        trd.setCptyBBookName(singleLegTrade.getCptyBBookName());
        trd.setCptyBPrimaryBookName(singleLegTrade.getCptyBPrimaryBookName());
        trd.setCptyBSecondaryBookName(singleLegTrade.getCptyBSecondaryBookName());
        trd.setMT300Field72(singleLegTrade.getMt300Field72());
        LOG.info("trade.id=" + singleLegTrade.get_id() +
                ", trd.getRequest().getTakerReferenceId=" + trd.getRequest().getTakerReferenceId() +
                ", trd.getCoverTrdMkrIds=" + trd.getCoverTrdMkrIds() +
                ", trd.getMakerReferenceId=" + trd.getMakerReferenceId() +
                ", trd.getTakerReferenceId=" + trd.getTakerReferenceId() +
                ", singleLegTrade.getTakerRefId=" + singleLegTrade.getTakerRefId() +
                ", singleLegTrade.getMakerRefId=" + singleLegTrade.getMakerRefId()+
                ", singleLegTrade.getMt300Field72=" + singleLegTrade.getMt300Field72()
            );

        modifyCoverTradeForSTP(trd, singleLegTrade);
        transformAccountId(singleLegTrade, trd);
        setAlgoType(trd, singleLegTrade);
        trd.setFeatureFlags(singleLegTrade.getFeatureFlags());
        trd.setCptyVisibilityType(singleLegTrade.getCptyVisibilityType());
        if(singleLegTrade.getOrderRequest() != null && singleLegTrade.getOrderRequest().isPQDirectedOrder() && singleLegTrade.getOrderRequest().getClientDescriptor() != null){
			String rateId = singleLegTrade.getOrderRequest().getClientDescriptor().getReferenceQuoteId();
			trd.setRateId(rateId);
		}
        if( singleLegTrade.isSyntheticCross() ){
            trd.setPrimaryExecutionType(singleLegTrade.getBrokerPrimaryExecutionType());
            trd.setSecondaryExecutionType(singleLegTrade.getBrokerSecondaryExecutionType());
        }
        else{
            trd.setBrokerExecutionType(singleLegTrade.getBrokerExecutionType());
        }
        trd.setCustomParameters ( singleLegTrade.getCustomParameters () );
    }

	private void setAlgoType( final FXSingleLeg trd, final SingleLegTrade singleLegTrade )
	{
		Set<Integer> pricingAlgos = singleLegTrade.getPricingAlgos();
		if ( pricingAlgos == null ) {
			pricingAlgos = new HashSet<Integer>();
		}

		if ( singleLegTrade.isFinalVenueTrade() ) {
			SingleLegOrderMatch matchEvent = singleLegTrade.getMatchEvent();
			if ( matchEvent != null && matchEvent.getMatchingVenue() != null && (matchEvent.getMatchingVenue().isNonFixingRisknetVenue() || matchEvent.getMatchingVenue().isFixingRiskNetVenue()) ) {
				pricingAlgos.add(PricingAlgo.RISK_NET.getId());
			}
		}

		if ( pricingAlgos != null && pricingAlgos.size() > 0 ) {
			trd.setAlgoType(StringUtils.join(pricingAlgos.toArray(), ","));
		}
	}

    private void transformAccountId(SingleLegTrade singleLegTrade, FXSingleLegC trd)
	{
        final SingleLegOrder order = singleLegTrade.getOrderRequest ();
        if ( LOG.isDebugEnabled () )
        {
            LOG.debug ( "MSTT.transformAccountId - set account id in the trade for trade=" + singleLegTrade.get_id ()
                    + ",requestChannel=" + order.getRequestChannel () + ",takerOrg=" + order.getOrganization ()
                    + ",channel=" + order.getChannel () );
        }
        if( order.getAccountRef() != null )
    	{
            final Organization takerOrg = order.getOrganization ();
            if ( takerOrg != null && takerOrg.isBroker () || ISCommonConstants.TRADECHANNEL_BA.equals ( order.getChannel () ) )
            {
                LOG.info ( "MSTT.transformAccountId - skip set accountId as broker is taker or BA cover channel="
                        + order.getRequestChannel () + ",takerOrg=" + takerOrg + ",tid=" + singleLegTrade.get_id() );
            }
            else
            {
                trd.setAccountId ( singleLegTrade.getOrderRequest ().getAccountRef ().getUid () );
                trd.setAccountShortname ( singleLegTrade.getOrderRequest ().getAccountRef ().getShortName () );
                if ( singleLegTrade.getOrderRequest().getAccountGroupId() != null ){
                    trd.setAccountGroupId(singleLegTrade.getOrderRequest().getAccountGroupId());
                }
            }
    	}
	}

	private void updateMakerDetailsForDirectedOrder(FXSingleLeg fxSingleLegTrade,SingleLegTrade singleLegTrade){
        SingleLegOrderMatch matchEvent = singleLegTrade.getMatchEvent();
        if(matchEvent.isDirectedOrder() && (matchEvent.isPrimeBrokerInitiated() || matchEvent.isBilateral())){
            String makerNameSpace = null;
            if(singleLegTrade.isIntraFloor()){
                makerNameSpace = singleLegTrade.getCounterpartyLegalEntity().getOrganization().getNamespace().getShortName();
            }else{
                if(singleLegTrade.getTradingParty()!=null) {
                    makerNameSpace = singleLegTrade.getTradingParty().getLegalEntityOrganization().getNamespace().getShortName();
                }else{
                    if(singleLegTrade.isOCX2()){
                        makerNameSpace = singleLegTrade.getCounterpartyLegalEntity().getOrganization().getNamespace().getShortName();
                    }

                }
            }
            DealingModel makerMatchModel = DealingModelSpacesCacheManager.getInstance().getCachedObject( PersistenceServiceFactory.getTradingMetaspaceName(),
                    makerNameSpace, PersistenceConstants.MATCHEVENT, singleLegTrade.getMakerOrderId(), MatchEvent.class );
            if(makerMatchModel != null){
                MatchEvent makerMatch = (MatchEvent)makerMatchModel;
                DealingModel orderReqModel = DealingModelSpacesCacheManager.getInstance().getCachedObject( PersistenceServiceFactory.getTradingMetaspaceName(),
                        makerNameSpace, PersistenceConstants.ORDERREQUEST, makerMatch.getOrderRequestRef().getUid(), SingleLegOrder.class );
                SingleLegOrder singleLegOrder = (SingleLegOrder)orderReqModel;
                fxSingleLegTrade.setMakerOrderId(makerMatch.getOrderRequestId());
                singleLegTrade.setMakerOrderId( makerMatch.getOrderRequestId() );
                if( singleLegTrade.getMakerOrderDealtCurrency() != null ){
                    fxSingleLegTrade.setMakerOrderDealtCurrency( singleLegOrder.getDealtCurrency().getShortName() );
                }
            }
        }
    }

    private void modifyCoverTradeForSTP(final FXSingleLeg trd, final SingleLegTrade singleLegTrade) {
        // Set NetTradeId as CoveredTradeId in Cover trade STP.
        CoveredTradeDescriptor coveredTradeDescriptor = singleLegTrade.getCoveredTrade();
        if (coveredTradeDescriptor != null && coveredTradeDescriptor.getTradeRef() != null) {
            if(singleLegTrade.getOrderRequest().getCoveredNetTradeId() != null ) {
                trd.setCoveredTradesNetTradeId(singleLegTrade.getOrderRequest().getCoveredNetTradeId());
            }
        }
    }

    private void populateExternalSystemId(FXSingleLeg trd, String extSys, String id)
    {
        ExternalSystem externalSystem = ISUtilImpl.getInstance().getExternalSys(extSys);
        if (externalSystem != null) {
            trd.addExternalSystemId(externalSystem, id);
        }
    }

    private void transformNextQuoteDetails(FXSingleLegC trd, SingleLegOrderMatch matchEvent) {
        MatchEventPQ matchEventPQ = matchEvent.getMatchEventPQ();
        if (matchEventPQ == null)
            return;

        MatchEventPQ.QuoteDescriptor.NextQuoteDetails nextQuoteDetails = matchEventPQ.getMatchedQuote().getNextQuoteDetails();
        if(nextQuoteDetails == null){
            nextQuoteDetails = matchEventPQ.getQuoteForTradeRequest().getNextQuoteDetails();
        }
        if(nextQuoteDetails != null){
            trd.setNextQuoteCompareToMatch(nextQuoteDetails.getComparedToPrev());
            trd.setNextQuoteEvent(nextQuoteDetails.getReceivedEvent());
        }
    }

    private void transformState(final SingleLegTrade singleLegTrade, final FXSingleLeg trd, final ApplicationEventCodes appEventCode) throws NullPointerException, PersistenceException {
        if (appEventCode == ApplicationEventCodes.EVENT_ESP_TRADE_PENDING) {
            // should already be in TSPENDING as set by tradeStateFacade.setNew()
            return;
        }

        User singleLegTradeUser = singleLegTrade.getUser();
        if (singleLegTradeUser == null)
            throw new NullPointerException("singleLegTradeUser");

        User lastUser = null;

        IdcUtilC.setSessionContextUser(singleLegTradeUser);
        final TradeStateFacade tradeStateFacade = (TradeStateFacade)trd.getFacade(TradeStateFacade.TRADE_STATE_FACADE);

        /*
         * Relying only on the appEventCode from the notification message and disregarding
         * singleLegTrade.getState() from the persisted record which maybe ahead of the state
         * transition implied by the sequence of the notification messages.
         *
         * The tradeStateFacade setters will set the last state as well.
         */
        switch (appEventCode) {
            case EVENT_ESP_TRADE_ACCEPT:
                tradeStateFacade.setNew();
                break;
            case EVENT_ESP_TRADE_CANCEL:
                tradeStateFacade.setCancelled();
                trd.setEtlTimestamp(new Timestamp(System.currentTimeMillis()));
                lastUser = singleLegTrade.getLastUser ();
                trd.setNote ( singleLegTrade.getNote () );
                break;
            case EVENT_ESP_NET_TRADE_CANCEL:
                tradeStateFacade.setCancelled();
                trd.setEtlTimestamp(new Timestamp(System.currentTimeMillis()));
                lastUser = singleLegTrade.getLastUser ();
                trd.setNote ( singleLegTrade.getNote () );
                break;
            case EVENT_ESP_TRADE_COMPLETED:
                LOG.info("Ignoring appEventCode EVENT_ESP_TRADE_COMPLETED, tid=" + singleLegTrade.get_id());
                break;
            case EVENT_ESP_TRADE_CONFIRMED:
                tradeStateFacade.setConfirmed();
                break;
            case EVENT_ESP_TRADE_FAILED:
                tradeStateFacade.setFailed();
                break;
            case EVENT_ESP_TRADE_PENDING:
                // tradeStateFacade.setNew(); no need as it is already in that state
                break;
            case EVENT_ESP_TRADE_REJECTED:
                if (singleLegTrade.isInternalRejection()) {
                    tradeStateFacade.setFailed();
                } else {
                    tradeStateFacade.setRejected();
                }
                break;
            case EVENT_ESP_TRADE_CREATED_VERIFIED:      // not update
            case EVENT_ESP_TRADE_POST_RATE_VERIFIED:
            case EVENT_ESP_TRADE_VERIFIED:
                tradeStateFacade.setVerified();
                break;
            case EVENT_ESP_TRADE_AMEND:
            case EVENT_ESP_NET_TRADE_AMEND:
            	if((tradeStateFacade.isNet() || tradeStateFacade.isNetted()) || trd.isOrderNetTrade())
            	{
            		//For netted trades do not call setAmended as it changes the state to confirmed.Just set the execution time
            		tradeStateFacade.getTrade().setExecutionDateTime( new Date() );
            	}
            	else
            	{
            		tradeStateFacade.setAmended();
            	}
                trd.setEtlTimestamp(new Timestamp(System.currentTimeMillis()));
                break;
            case EVENT_ESP_TRADE_NETTED:
            	if(isCommonMbean.isNetTradeStateTransformEnabled()) {
            		tradeStateFacade.setCancelled();
                    trd.setEtlTimestamp(new Timestamp(System.currentTimeMillis()));
            	} else {
            		tradeStateFacade.setNetted();
            	}
                trd.setOrderNetTrade(true);
                break;
            case EVENT_ESP_TRADE_NET:
            	if(isCommonMbean.isNetTradeStateTransformEnabled()) {
            		tradeStateFacade.setVerified();
            	} else {
            		tradeStateFacade.setNet();
            	}
                trd.setOrderNetTrade(true);
                break;
            case EVENT_ESP_TRADE_PRE_VERIFIED:
                //It will be followed by EVENT_ESP_TRADE_VERIFIED
                //It will already be in new state
                break;
            case EVENT_ESP_TRADE_CREATED_PRE_VERIFIED:
               tradeStateFacade.setNew(); //It will be followed by EVENT_ESP_TRADE_VERIFIED
                break;
            case EVENT_ESP_MS_LOOKUP:
                break;
            case EVENT_ESP_TRADE_PRE_RATE_VERIFIED:
                tradeStateFacade.setPreRateVerified();
                break;
            case EVENT_ESP_TRADE_ALLOCATION_COMPLETED:
                tradeStateFacade.setCancelled ();
                break;
            case EVENT_ESP_POST_TRADE_UPDATE:
            	trd.setBackOfficeID(singleLegTrade.getBackOfficeID());
            	break;
            default:
                LOG.info("Invalid update appEventCode " + appEventCode.toString() + ", tid=" + singleLegTrade.get_id());
                break;
        }

        WorkflowStateMap workflowStateMap = trd.getWorkflowStateMap();
        if (workflowStateMap == null)
            throw new NullPointerException("workflowStateMap");

        workflowStateMap.setWorkflowCodeArgument(singleLegTrade.getRejectionReason());
        trd.setWorkflowCodeArgument( singleLegTrade.getRejectionReason());
        trd.setWorkflowCode( workflowStateMap.getWorkflowCode());
        LOG.info ( "MSTT.transformState - appEventCode=" + appEventCode + ",lastUser=" + lastUser
                + ",singleLegTrade.lastUser=" + singleLegTrade.getLastUser () + ",tid=" + trd.getTransactionID () );
        if ( lastUser != null )
        {
            workflowStateMap.setLastActor ( lastUser );
        }
    }

    private void transformTradeLeg(final SingleLegTrade singleLegTrade, final FXSingleLeg trd, final SingleLegOrderMatch matchEvent, final ApplicationEventCodes appEventCode, final boolean update) throws NullPointerException, PersistenceException {
        TradeLeg tradeLeg = singleLegTrade.getTradeLeg();
        if (tradeLeg == null)
            throw new NullPointerException("tradeLeg");

        MatchEvent.MatchEventLeg matchEventLeg = matchEvent.getMatchEventLeg();
        if (matchEventLeg == null)
            throw new NullPointerException("matchEventLeg");

        FXRateBasis fxRateBasis = singleLegTrade.getFxRateBasis();
        if (fxRateBasis == null) {
            fxRateBasis = matchEvent.getFxRateBasis();
            if (fxRateBasis == null)
                throw new NullPointerException("fxRateBasis");
        }

        trd.setSymbol(fxRateBasis.getCurrencyPairString());

        FXLeg fxLeg = trd.getFXLeg();
        if (fxLeg == null)
            throw new NullPointerException("fxLeg");

        fxLeg.setUSI(singleLegTrade.getUSI());

        fxLeg.setUTI(tradeLeg.getUTI());
        fxLeg.setUTINamespace(tradeLeg.getUTINamespace());
        fxLeg.setUTIIdentifier(tradeLeg.getUTIIdentifier());

		FXPaymentParameters fxPaymentParams;
        FXRate fxRate;
        if (update) {
            fxPaymentParams = fxLeg.getFXPayment();
            if (fxPaymentParams == null)
                throw new NullPointerException("fxPaymentParams");

            fxRate = fxPaymentParams.getFXRate();
            if (fxRate == null)
                throw new NullPointerException("fxRate");
        } else {
            fxPaymentParams = FXFactory.newFXPaymentParameters();
            fxLeg.setFXPayment(fxPaymentParams);

            fxRate = FXFactory.newFXRate();
            fxPaymentParams.setFXRate(fxRate);
        }

        fxPaymentParams.setCurrency1(singleLegTrade.getBaseCurrency());
        fxPaymentParams.setCurrency2(singleLegTrade.getTermCurrency());
        Currency settleCurrency;
        if (singleLegTrade.getDealtCurrency().equals(singleLegTrade.getBaseCurrency())) {
            fxPaymentParams.setDealtCurrency1(true);
            fxPaymentParams.setCurrency1Amount(singleLegTrade.getDealtAmount());
            fxPaymentParams.setCurrency2Amount(singleLegTrade.getSettledAmount());
            settleCurrency = singleLegTrade.getTermCurrency();
        } else {
            fxPaymentParams.setDealtCurrency1(false);
            fxPaymentParams.setCurrency1Amount(singleLegTrade.getSettledAmount());
            fxPaymentParams.setCurrency2Amount(singleLegTrade.getDealtAmount());
            settleCurrency = singleLegTrade.getBaseCurrency();
        }

        fxPaymentParams.setBuyingCurrency1(tradeLeg.isBuyingBaseCurrency());
        if( matchEventLeg.getTenor() != null ) {
            fxPaymentParams.setTenor(new Tenor(matchEventLeg.getTenor()));
        }

        if (fxPaymentParams.getFXCoverRate() == null) {
            fxPaymentParams.setFXCoverRate(createFXCoverRate(singleLegTrade, tradeLeg, appEventCode));
        }

        fxPaymentParams.setAcceptedProviderPrice(matchEventLeg.getFinalAcceptanceSpotRate());
        IdcDate valueDate = DateTimeFactory.newDate(new Date(tradeLeg.getValueDate()));
        fxPaymentParams.setValueDate(valueDate);
        if(ISCommonConstants.TRD_CLSF_FXNDF.equals ( singleLegTrade.getClassification () ) && tradeLeg.getFixingDate() != 0L) {
            IdcDate fixingDate = DateTimeFactory.newDate(new Date(tradeLeg.getFixingDate()));
            fxPaymentParams.setFixingDate(fixingDate);
        }
        Tenor tenor = tradeLeg.getTenor() != null ? new Tenor(tradeLeg.getTenor()) : null;
        fxRate.setFXRateBasis(fxRateBasis);
        fxRate.setBaseCurrency(fxRateBasis.getBaseCurrency());
        fxRate.setFXRateConvention(fxRateBasis.getFXRateConvention());
        boolean isSpot = ISUtilImpl.TRD_SPOT_CLSF.getShortName().equals(singleLegTrade.getClassification());
        /*
            If spotDate is set use that. Else use value date for backward compatibility with ESP Spot workflow
         */
        IdcDate spotDate = tradeLeg.getSpotValueDate() != 0L ? DateTimeFactory.newDate(new Date(tradeLeg.getSpotValueDate())) : null;
        if(!isSpot){
            if( spotDate == null ) {
                IdcDate tradeDate = DateTimeFactory.newDate(new Date(singleLegTrade.getTradeDate()));
                spotDate = fxRateBasis.getSpotDate(tradeDate);
            }
            fxPaymentParams.setTenor(tenor);
            fxPaymentParams.setSpotDate(spotDate);
            trd.setMidBenchmarkFwdPoints ( tradeLeg.getMidBenchmarkFwdPoints () );
			trd.setStreamingNonSpot ( singleLegTrade.isStreamingNonSpot() );
            trd.setSpotValueDate ( spotDate );
        }
        else{
            if( tenor == null ){
                fxPaymentParams.setTenor(Tenor.SPOT_TENOR);
            }
            else{
                fxPaymentParams.setTenor(tenor);
            }
            if( spotDate == null ) {
                fxPaymentParams.setSpotDate(valueDate);
            }
            else{
                fxPaymentParams.setSpotDate(spotDate);
            }
        }
        fxRate.setVariableCurrency(fxRateBasis.getVariableCurrency());
        double spotRate = tradeLeg.getSpotRate();
        double forwardPoints = tradeLeg.getForwardPoints();
        fxRate.setSpotRate(spotRate);

        if(singleLegTrade.getOrderRequest().isNettingOnCalculatedResponseEnabled()){
            fxRate.setRate(tradeLeg.getRate());
        }
        fxRate.setForwardPoints(forwardPoints);
        if(!isSpot){
            fxRate.setRate(tradeLeg.getRate());
        }
        fxPaymentParams.setFees(tradeLeg.getFees());
        fxPaymentParams.setFeesInUSD(tradeLeg.getFeesInUSD());
        fxPaymentParams.setInitialSettledAmount(tradeLeg.getInitialSettledAmount());
        boolean roundingError = spotRate != fxRate.getSpotRate() || forwardPoints != fxRate.getForwardPoints() || tradeLeg.getRate() != fxRate.getRate();
        if(roundingError) {
			LOG.error(".transformTradeLeg: " + singleLegTrade.get_id() + ' ' + fxRateBasis.getCurrencyPair().getName() + ' ' + fxRateBasis.getFXRateConvention().getShortName() + ' ' 
			+ spotRate + ' ' + forwardPoints + ' ' + fxRate.getForwardPoints() + ' ' + fxRate.getSpotRate() + ' ' + fxRate.getRate());
		}
    }

    private void transformCptyTrades(final SingleLegTrade singleLegTrade, final FXSingleLeg trd) throws Exception {
        trd.setCounterpartyA(singleLegTrade.getLegalEntity());
        trd.setCounterpartyB( DealingModelUtil.getCounterPartyLegalEntity( singleLegTrade));
        if( singleLegTrade.isTakerSelfClearing() ){
            trd.setCounterpartyCSelfClearing(true);
            if( singleLegTrade.getTakerLegalEntityForSelfClearing() == null ) {
                LOG.warn("transformCptyTrades : tid=" + singleLegTrade.get_id() + ", takerPrimeBrokerSelfClearing=true but selfClearingTakerPrimeBrokerTradingParty is NULL");
            }
            trd.setCounterpartyCForSelfClearing(singleLegTrade.getTakerLegalEntityForSelfClearing());
        }
        else {
            trd.setCounterpartyC(singleLegTrade.getTakerPrimeBrokerTradingParty());
        }
        if( singleLegTrade.isMakerSelfClearing() ){
            trd.setCounterpartyDSelfClearing(true);
            if( singleLegTrade.getMakerLegalEntityForSelfClearing() == null ){
                LOG.warn("transformCptyTrades : tid="+singleLegTrade.get_id()+", makerPrimeBrokerSelfClearing=true but selfClearingMakerPrimeBrokerTradingParty is NULL");
            }
            trd.setCounterpartyDForSelfClearing(singleLegTrade.getMakerLegalEntityForSelfClearing());
        }
        else{
            trd.setCounterpartyD(singleLegTrade.getMakerPrimeBrokerTradingParty());
        }
        trd.setTakerCounterparty(trd.getCounterpartyA());
        trd.setMakerCounterparty(trd.getCounterpartyB());
        if (trd.getRequest() != null && singleLegTrade.getOrderRequest() != null && singleLegTrade.getOrderRequest().getRequestChannel() != null) {
            trd.getRequest().setChannel(ISUtilImpl.getInstance().getExtSys(singleLegTrade.getOrderRequest().getRequestChannel()));
            if(!StringUtilC.isNullOrEmpty(singleLegTrade.getOrderRequestClientReferenceId()))
            {
                populateExternalSystemId(trd, singleLegTrade.getOrderRequest().getRequestChannel(), singleLegTrade.getOrderRequestClientReferenceId());
            }
        }
        TradeStateFacade tradeStateFacade = (TradeStateFacade)trd.getFacade(TradeStateFacade.TRADE_STATE_FACADE);

        String coveredTradeTxId = null;
        CoveredTradeDescriptor coveredTradeDescriptor = singleLegTrade.getCoveredTrade();
        /*if(singleLegTrade.isShouldLinkExternalReqIdForTrade()){
            coveredTradeTxId = singleLegTrade.getExternalReferenceId();
        }else*/
        if(singleLegTrade.isShouldLinkExternalReqIdForTrade()){
            trd.setVenueTradeTxId( singleLegTrade.getVenueTradeTxId() );
            trd.setVenueCptyTradeTxId( singleLegTrade.getVenueCptyTradeTxId() );
        }
        if (coveredTradeDescriptor != null) {
            if (coveredTradeDescriptor.getTradeRef() != null) {
                coveredTradeTxId = coveredTradeDescriptor.getTradeRef().getUid();
            }
            trd.setCoveredTradeCounterparty( coveredTradeDescriptor.getLegalEntity() );
            trd.setCoveredTradeUser( coveredTradeDescriptor.getUser() );
        }
        // setting net trade id as covered trade id for all cover trades
        if (coveredTradeDescriptor != null && coveredTradeDescriptor.getTradeRef() != null) {
            if(singleLegTrade.getOrderRequest().getCoveredNetTradeId() != null ) {
                coveredTradeTxId = singleLegTrade.getOrderRequest().getCoveredNetTradeId();
            }
        }
        if(!StringUtilC.isNullOrEmpty(coveredTradeTxId)){
            trd.setCoveredTradeTxId(coveredTradeTxId);
            // set External System for CoveredTrade
            ExternalSystem coveredTradeIdExtSys = ISUtilImpl.getInstance().getExternalSys(ISConstantsC.EXT_COVER_TRADE);
            if ( coveredTradeIdExtSys != null )
            {
                trd.addExternalSystemId( coveredTradeIdExtSys, coveredTradeTxId );
            }
            // set CounterpartyRequestID
            ExternalSystem extSys = ISUtilImpl.getInstance().getExternalSys(ISConstantsC.EXT_CPTY_REQUEST_ID);
            if ( extSys != null)
            {
                trd.addExternalSystemId( extSys, coveredTradeTxId );
            }
        }
        Collection<CptyTrade> FXSingleLegCounterParties = trd.getCptyTrades();
        if (FXSingleLegCounterParties == null)
            throw new NullPointerException("FXSingleLegCounterParties");

        CounterPartyTradeService counterPartyTradeService = FXESPServiceFactory.getInstance().getCounterPartyTradeService();
        List<CounterpartyTrade> singleLegTradeCounterParties = new ArrayList<CounterpartyTrade>( 4 );
        if(!singleLegTrade.isDOMakerTrade()){
            counterPartyTradeService.createTakerCptyTrade( singleLegTrade,singleLegTradeCounterParties );
            counterPartyTradeService.createMakerCptyTrade( singleLegTrade,singleLegTradeCounterParties );
        }
        String coverTradeIds = singleLegTrade.getCoverTradeIds();
        trd.setCoverTradeTxIds(coverTradeIds);
        for (CounterpartyTrade counterpartyTrade : singleLegTradeCounterParties) {
            CounterpartyTrade.CounterpartyType counterpartyType = counterpartyTrade.getCounterpartyType();
            STPDownloadAttributes downloadAttributes = null;
            char owningCptyRef;
            switch (counterpartyTrade.getView()){
                case MAKER_SYNTHETIC_PRIMARY:
                case MAKER_SYNTHETIC_SECONDARY:
                    continue;
            }
            switch (counterpartyType) {
                case A:     // FI, taker
                    owningCptyRef = 'A';
                    downloadAttributes = singleLegTrade.getTakerDownloadAttributes();
                    break;
                case B:     // LP, maker
                    owningCptyRef = 'B';
                    downloadAttributes = singleLegTrade.getMakerDownloadAttributes();
                    break;
                case C:     // FI PB
                    owningCptyRef = 'C';
                    downloadAttributes = singleLegTrade.getTakerDownloadAttributes();
                    break;
                case D:     // LP PB
                    owningCptyRef = 'D';
                    downloadAttributes = singleLegTrade.getMakerDownloadAttributes();
                    break;
                case S:     // Taker Sales Dealer
                    owningCptyRef = 'S';
                    downloadAttributes = singleLegTrade.getTakerDownloadAttributes();
                    break;
                case M:     // Maker Sales Dealer
                    owningCptyRef = 'M';
                    downloadAttributes = singleLegTrade.getMakerDownloadAttributes();
                    break;
                default:
                    throw new Exception("Invalid counterpartyType " + counterpartyType.toString());
            }

            CptyTrade cptyTrade = trd.getCptyTrade(owningCptyRef);
            if (cptyTrade == null) {
                cptyTrade = TradeFactory.newCptyTrade();
                FXSingleLegCounterParties.add(cptyTrade);
                cptyTrade.setOwningCptyRef(owningCptyRef);
            }

            if( downloadAttributes != null && ( tradeStateFacade.isVerified() || tradeStateFacade.isConfirmed())) {
                String reportingParty = downloadAttributes.getTakerReferenceId() + "-" + downloadAttributes.getMakerReferenceId();
                cptyTrade.setReportingParty( reportingParty );
                LOG.info("Reporting for " + singleLegTrade.get_id() + ", cpty=" + counterpartyTrade.getNamespaceName() + ", reportingParty=" + reportingParty);
            }

            transformCptyTrade(trd, counterpartyTrade, cptyTrade, coveredTradeTxId, coverTradeIds, singleLegTrade.isDOTakerTrade());
        }

        if (singleLegTradeCounterParties.size() != FXSingleLegCounterParties.size()) {
            LOG.warn("Mismatch number of counterparties, source: " + singleLegTradeCounterParties.size() +
                     " dest: " + FXSingleLegCounterParties.size() +
                     " cId=" + singleLegTrade.getCorrelationId() +
                     " tid=" + singleLegTrade.get_id());
        }
    }

    private void transformCptyTrade(final FXSingleLeg trd, final CounterpartyTrade spacesCptyTrade, final CptyTrade cptyTrade, final String coveredTradeTxId, final String coverTradeIds, boolean isDOTakerTrade) {
        cptyTrade.setTrade(trd);
        cptyTrade.setTradeDate(DateTimeFactory.newDate(new Date(spacesCptyTrade.getTradeDate())));
        cptyTrade.setNamespace(spacesCptyTrade.getNamespace());
        cptyTrade.setLegalEntity(spacesCptyTrade.getLegalEntity());
        cptyTrade.setUser(spacesCptyTrade.getUser());
        cptyTrade.setTradingParty(spacesCptyTrade.getTradingParty());
        cptyTrade.setTradingPartyUser(spacesCptyTrade.getCounterpartyUser());
        cptyTrade.setStatus(spacesCptyTrade.getStatus());

        switch ( spacesCptyTrade.getView() ){
            case TAKER:
                // Set the covered trade transaction id only for taker since we don't show it in STP of maker download.
                cptyTrade.setCoveredTradeTxId(coveredTradeTxId);
                cptyTrade.setTaker(true);
                if(isDOTakerTrade){
                    cptyTrade.setVenueTradeTxId( trd.getVenueTradeTxId() );
                    cptyTrade.setVenueCptyTradeTxId( trd.getVenueCptyTradeTxId() );
                }
                break;
            case MAKER:
                // Set the cover trade transaction ids only for maker.
                cptyTrade.setCoverTradeTxIds(coverTradeIds);
                cptyTrade.setTaker(false);
                if(!isDOTakerTrade) {
                    cptyTrade.setVenueTradeTxId( trd.getVenueTradeTxId() );
                    cptyTrade.setVenueCptyTradeTxId( trd.getVenueCptyTradeTxId() );
                }
                break;
        }
        cptyTrade.setBookName(spacesCptyTrade.getBookName());

       // cptyTrade.setNetPositionCreated(true);       // TODO already accounted for in the position services for now

//        TODO only if needed
//        cptyTrade.setTradingPartyLegalEntity(?);
//        cptyTrade.setCoverCptyTrades(?);
//        cptyTrade.setCoveredTradeTxIdSTP(?);
//        cptyTrade.setCoveredCptyTrade(?);
//        cptyTrade.setCoverTradeTxIdCollection(?);
//
//        cptyTrade.setActive(?);
//        cptyTrade.setAllocatedCptyTrades(?);
//        cptyTrade.setAllocationCptyTrade(?);
//        cptyTrade.setContainedCptyTrades(?);
//        cptyTrade.setContainingCptyTrade(?);
//        cptyTrade.setDownloadedMessage(?);
    }

    private void transformTradeClassification(final SingleLegTrade singleLegTrade, final FXSingleLeg trd) throws IdcNoSuchObjectException {
        TradeClassification singleLegTradeClassification = (TradeClassification)ReferenceDataCacheC.getInstance().getEntityByShortName(singleLegTrade.getClassification(), TradeClassificationC.class, null, Entity.ACTIVE_STATUS);
        boolean isNDF = ISCommonConstants.TRD_CLSF_FXNDF.equals ( singleLegTrade.getClassification () ) || ISCommonConstants.TRD_CLSF_FXNDF_SWAP.equals ( singleLegTrade.getClassification () );
        if ( isNDF && !singleLegTradeClassification.getShortName().equals(ISUtilImpl.TRD_SPOT_CLSF.getShortName())) {
            trd.getFXLeg().setTradeLegClassification(ISUtilImpl.TRDLEG_FWD_CLSF );
        }
        else if (singleLegTradeClassification.getShortName().equals(ISUtilImpl.TRD_SPOT_CLSF.getShortName())) {
            trd.getFXLeg().setTradeLegClassification(ISUtilImpl.TRDLEG_SPOT_CLSF );
        }
        else {
            trd.getFXLeg().setTradeLegClassification(ISUtilImpl.TRDLEG_FWD_CLSF );
        }

        trd.setTradeClassification(singleLegTradeClassification);
    }

    private void transformOriginatingTrade(final SingleLegTrade singleLegTrade, final FXSingleLeg trd) throws NullPointerException {
        OriginatingTradeDescriptor origTradeDesc = singleLegTrade.getOriginatingTrade();
        if (origTradeDesc == null)
            throw new NullPointerException("origTradeDesc");

        DealingModelRef<SingleLegTrade> tradeRef = origTradeDesc.getTradeRef();
        if (tradeRef == null)
            throw new NullPointerException("tradeRef");

        SingleLegOrder orderRequest = singleLegTrade.getOrderRequest();
        if( orderRequest == null ){
            throw new NullPointerException("orderRequest");
        }

        OriginatingOrderRequestDescriptor origOrderDesc = orderRequest.getOriginatingOrderRequest();
        if( origOrderDesc == null ){
            throw new NullPointerException("origOrderDesc");
        }
        DealingModelRef<OrderRequest> orderRef = origOrderDesc.getOrderRequestRef();
        if( orderRef == null ){
            throw new NullPointerException("orderRef");
        }

        trd.setOriginatingOrderId(orderRef.getUid());
        trd.setOriginatingAmount(origTradeDesc.getDealtAmount());
        if( origTradeDesc.getLegalEntity() != null ){
            trd.setOriginatingCptyId(origTradeDesc.getLegalEntity().getObjectID());
        }
        if( origTradeDesc.getUser() != null ){
            trd.setOriginatingUserId(origTradeDesc.getUser().getObjectID());
        }
    }

    private void transformQuotedRequest(final SingleLegTrade singleLegTrade, final FXSingleLegC trd, final SingleLegOrderMatch matchEvent, final boolean update, final LegalEntity cptyBLe) throws NullPointerException, PersistenceException {
        MatchEventPQ matchEventPQ = matchEvent.getMatchEventPQ();
        if (matchEventPQ == null)
            return;
        MatchEventPQ.QuoteDescriptor matchedQuote = matchEventPQ.getMatchedQuote();
        if (matchedQuote == null)
            throw new NullPointerException("matchedQuote");

        trd.setMaskedLP(matchedQuote.getMaskedName());

        MatchEventPQ.QuoteDescriptor.QuoteEventTimes quoteEventTimes = matchedQuote.getQuoteEventTimes();
        if (quoteEventTimes == null)
            throw new NullPointerException("quoteEventTimes");
        if (update) {
            if (quoteEventTimes.getRateEffective() != 0)
                trd.setRateEffective(new Timestamp(quoteEventTimes.getRateEffective()));
            if (quoteEventTimes.getRateReceivedByAdapter() != 0)
                trd.setRateRecvdByAdpt(new Timestamp(quoteEventTimes.getRateReceivedByAdapter()));
            if (quoteEventTimes.getRateSentByAdapter() != 0)
                trd.setRateSentByAdpt(new Timestamp(quoteEventTimes.getRateSentByAdapter()));
            if (quoteEventTimes.getRateReceivedByIS() != 0)
                trd.setRateRecvdByApp(new Timestamp(quoteEventTimes.getRateReceivedByIS()));
            if (quoteEventTimes.getQuoteCreated() != 0)
                trd.setQuoteCreatedByApp(new Timestamp(quoteEventTimes.getQuoteCreated()));
            if (quoteEventTimes.getRateSentByIS() != 0)
                trd.setRateSentByApp(new Timestamp(quoteEventTimes.getRateSentByIS()));
            if( trd.getRequest().getMarketSnapshot() == null || trd.getRequest().getMarketSnapshot().isEmpty())
                trd.getRequest().setMarketSnapshot(MarketSnapshotUtil.convertToOldSnapshot(matchEvent.getMarketSnapshot()));
            if( trd.getRequest().getGridMidRate() == 0.0)
            {
				trd.getRequest().setGridMidRate(matchEvent.getGridMidRate());
				trd.setMidBenchmarkRate1(matchEvent.getGridMidRate());
				trd.setMidBenchmarkRate2(matchEvent.getGridMidRate());
                MiFIDUtils.setMidBenchmarkAllinRate( trd );
			}
        } else {
            trd.setRateEffective(new Timestamp(quoteEventTimes.getRateEffective()));
            trd.setRateRecvdByAdpt(new Timestamp(quoteEventTimes.getRateReceivedByAdapter()));
            trd.setRateSentByAdpt(new Timestamp(quoteEventTimes.getRateSentByAdapter()));
            trd.setRateRecvdByApp(new Timestamp(quoteEventTimes.getRateReceivedByIS()));
            trd.setQuoteCreatedByApp(new Timestamp(quoteEventTimes.getQuoteCreated()));
            trd.setRateSentByApp(new Timestamp(quoteEventTimes.getRateSentByIS()));

            MatchEventPQ.QuoteDescriptor quoteDescriptor = matchEventPQ.getQuoteForTradeRequest();

            if (quoteDescriptor == null)
                throw new NullPointerException("quoteDescriptor");
            String streamId = quoteDescriptor.getStreamId();
            trd.setStream(streamId);
            setVenueName(quoteDescriptor,singleLegTrade,trd,matchEvent,streamId);
            RequestC quotedRequest = new RequestC();      // ThreadLocal is overkill as these are cached
            trd.setRequest(quotedRequest);
            quotedRequest.setSpaces(true);
            quotedRequest.setTransactionID(singleLegTrade.get_id());
            quotedRequest.setExternalRequestId(singleLegTrade.getOrderRequestClientReferenceId());
            quotedRequest.setExternalTradeId(singleLegTrade.getExternalReferenceId());
            quotedRequest.setLegalEntity(singleLegTrade.getLegalEntity());
            quotedRequest.setVirtualServer(singleLegTrade.getVirtualServer());
            quotedRequest.setTradeChannel(singleLegTrade.getChannel());
            quotedRequest.setUser(singleLegTrade.getUser());
            quotedRequest.setCreatedBusinessDate(DateTimeFactory.newDate(new Date(quoteEventTimes.getQuoteCreated())));
            quotedRequest.setMarketSnapshot(MarketSnapshotUtil.convertToOldSnapshot(matchEvent.getMarketSnapshot()));
            quotedRequest.setGridMidRate(matchEvent.getGridMidRate());
            quotedRequest.setHistoricalMatch(matchEvent.isHistoricMatch());
            switch (quoteDescriptor.getType()) {
                case MULTI_QUOTE:
                    quotedRequest.setPriceType(Quote.PRICE_TYPE_QUOTES);
                    break;
                case MULTI_TIER:
                    quotedRequest.setPriceType(Quote.PRICE_TYPE_MULTI_TIER);
                    break;
                case ORDER:
                    quotedRequest.setPriceType(Quote.PRICE_TYPE_ORDERS);
                    break;
                case VENUE:
                    quotedRequest.setPriceType( Quote.PRICE_TYPE_VENUE );
                    break;
                default:
                    LOG.error("Invalid match event quote for trade request type " + quoteDescriptor.getType() + ", tid=" + singleLegTrade.get_id());
                    break;
            }

            RequestClassification singleLetTradeRequestClassification = ISUtilImpl.getInstance().getRequestClassification(ISCommonConstants.QTQ_ACCEPT_TYPE);
            LOG.info("TradeRequestClassification " + ISCommonConstants.QTQ_ACCEPT_TYPE + " cached as " + singleLetTradeRequestClassification.getShortName() + ", tid=" + singleLegTrade.get_id() + ", snapshot captured=" + (quotedRequest.getMarketSnapshot().length() != 0));
            quotedRequest.setRequestClassification(singleLetTradeRequestClassification);

            SingleLegOrder singleLegOrder = singleLegTrade.getOrderRequest();
            if (singleLegOrder == null)
                throw new NullPointerException("singleLegOrder");

            String orderRequestId = singleLegOrder.get_id();
            quotedRequest.setOrderId(orderRequestId);

            IdcTransaction tx = IdcSessionManager.getInstance().getTransaction();
            // Query and set Order request only if transaction is enabled.
            if( tx != null && !tx.isFakeTransaction())
            {
            RequestC parentRequest = requestExpirableCache.get(singleLegOrder.getNamespaceName(), orderRequestId, null);
            if (parentRequest == null)
                throw new NullPointerException("parentRequest");

            RequestC registeredParentRequest = (RequestC)parentRequest.getRegisteredObject();
            quotedRequest.setParentRequest(registeredParentRequest);
		    }

            if ( cptyBLe != null )
            {
                quotedRequest.getToOrganizations().add( cptyBLe.getOrganization() );
            }
            else
            {
                LOG.error( "MSTT.transformQuotedRequest : cptyB Legal entity is null for order request id=" + orderRequestId );
            }
            quotedRequest.setSentDate( singleLegTrade.getExecutionTime() > 0 ? new Date( singleLegTrade.getExecutionTime() ) : new Date() );

            transformQuotedRequestPrice(singleLegOrder, quotedRequest, matchEvent);
            quotedRequest.setCustomParameters ( singleLegOrder.getCustomParameters () );
        }
    }


    /*Nishit Verma
     * This function will be removed after Domain Model Changes
     */
    private void setVenueName(MatchEventPQ.QuoteDescriptor quoteDescriptor,final SingleLegTrade singleLegTrade, final FXSingleLegC trd, final SingleLegOrderMatch matchEvent , final String streamId)
    {
        Organization matchingVenueOrg = quoteDescriptor.getOrganization();
        if(matchingVenueOrg != null)
        {
        	String mvName = matchingVenueOrg.getShortName();
			boolean setVenueName = !matchEvent.isPrimeBrokerInitiated();
			if (singleLegTrade.getTradingParty() != null) {
				String cptyOrgShortName = singleLegTrade.getTradingParty()
						.getLegalEntityOrganization().getShortName();
				setVenueName = setVenueName
						|| cptyOrgShortName.equals(mvName);
			}

		if (matchingVenueOrg.isMakerVenue() && setVenueName) {
				trd.setVenueName(matchingVenueOrg.getShortName());
	            trd.setAggresorInVenue(singleLegTrade.isVenueAgressorTrade());
		}
        else if( TradeUtil.isDirectedOrderCustomerMakerTradeWithClearingMember(singleLegTrade)){
            trd.setVenueName(matchingVenueOrg.getShortName());
            trd.setAggresorInVenue(singleLegTrade.isVenueAgressorTrade());
        }
        else if (setVenueName){
            trd.setAggresorInVenue( singleLegTrade.isVenueAgressorTrade() );
            BrokerOrganizationFunction brokerOrganizationFunction = matchingVenueOrg.getBrokerOrganizationFunction();
            if(brokerOrganizationFunction != null){
                Stream stream = brokerOrganizationFunction.getStream( streamId );
                if(stream != null && stream.isNoLastLook()){
                	String myVenue = mhMBean.getHostedOrgForStream(stream.getLegalEntity().getOrganization().getShortName(), streamId);
                	trd.setVenueName(myVenue);
                }
            }
        }
        }
    }

    private void transformQuotedRequestDirectedOrder(final SingleLegTrade singleLegTrade, final FXSingleLeg trd, final SingleLegOrderMatch matchEvent, final boolean update, final LegalEntity cptyBLe) throws NullPointerException, PersistenceException {
        if(singleLegTrade.isIntraFloor()){
            trd.setMaskedLP(singleLegTrade.getCounterpartyLegalEntity().getOrganization().getShortName());
        }else{
            if(singleLegTrade.getTradingParty()!=null){
                trd.setMaskedLP(singleLegTrade.getTradingParty().getLegalEntityOrganization().getShortName());
            }else{
                trd.setMaskedLP(singleLegTrade.getCounterpartyLegalEntity().getOrganization().getShortName());
            }

        }
        if (!update) {
            RequestC quotedRequest = new RequestC();      // ThreadLocal is overkill as these are cached
            trd.setRequest(quotedRequest);
            quotedRequest.setSpaces(true);
            quotedRequest.setTransactionID(singleLegTrade.get_id());
            quotedRequest.setExternalRequestId(singleLegTrade.getOrderRequestClientReferenceId());
            quotedRequest.setExternalTradeId(singleLegTrade.getExternalReferenceId());
            quotedRequest.setLegalEntity(singleLegTrade.getLegalEntity());
            quotedRequest.setVirtualServer(singleLegTrade.getVirtualServer());
            quotedRequest.setTradeChannel(singleLegTrade.getChannel());
            quotedRequest.setUser(singleLegTrade.getUser());
            quotedRequest.setMarketSnapshot(MarketSnapshotUtil.convertToOldSnapshot(matchEvent.getMarketSnapshot()));
            if(singleLegTrade.getVenueBMR() != 0.0)
            quotedRequest.setGridMidRate(singleLegTrade.getVenueBMR());
            else
            quotedRequest.setGridMidRate(matchEvent.getGridMidRate());
            quotedRequest.setHistoricalMatch(matchEvent.isHistoricMatch());
            RequestClassification singleLetTradeRequestClassification = ISUtilImpl.getInstance().getRequestClassification(ISCommonConstants.QTQ_ACCEPT_TYPE);
            LOG.info("TradeRequestClassification " + ISCommonConstants.QTQ_ACCEPT_TYPE + " cached as " + singleLetTradeRequestClassification.getShortName() + ", tid=" + singleLegTrade.get_id());
            quotedRequest.setRequestClassification(singleLetTradeRequestClassification);
            quotedRequest.setPriceType( Quote.PRICE_TYPE_VENUE );
            SingleLegOrder singleLegOrder = singleLegTrade.getOrderRequest();
            if (singleLegOrder == null)
                throw new NullPointerException("singleLegOrder");

            String orderRequestId = singleLegOrder.get_id();
            quotedRequest.setOrderId(orderRequestId);

            IdcTransaction tx = IdcSessionManager.getInstance().getTransaction();
            // Query and set Order request only if transaction is enabled.
            if( tx != null && !tx.isFakeTransaction())
            {
                RequestC parentRequest = requestExpirableCache.get(singleLegOrder.getNamespaceName(), orderRequestId, null);
                if (parentRequest == null)
                    throw new NullPointerException("parentRequest");

                RequestC registeredParentRequest = (RequestC)parentRequest.getRegisteredObject();
                quotedRequest.setParentRequest(registeredParentRequest);
            }

            if ( cptyBLe != null )
            {
                quotedRequest.getToOrganizations().add( cptyBLe.getOrganization() );
            }
            else
            {
                LOG.error( "MSTT.transformQuotedRequest : cptyB Legal entity is null for order request id=" + orderRequestId );
            }
            quotedRequest.setSentDate( singleLegTrade.getExecutionTime() > 0 ? new Date( singleLegTrade.getExecutionTime() ) : new Date() );

            transformQuotedRequestPrice(singleLegOrder, quotedRequest, matchEvent);
        }
    }


    private void transformQuotedRequestPrice(final SingleLegOrder singleLegOrder, final RequestC quotedRequest, final SingleLegOrderMatch matchEvent) {
        MatchEvent.MatchEventLeg matchEventLeg = matchEvent.getMatchEventLeg();
        if (matchEventLeg == null)
            throw new NullPointerException("matchEventLeg");

        final String priceName = "singleLeg";
        FXLegDealingPrice origPrice = FXDealingFactory.newFXLegDealingPrice();
        FXLegDealingPrice quotedRequestPrice = (FXLegDealingPrice)origPrice.getRegisteredObject();
        if (quotedRequestPrice == null)
            throw new NullPointerException("price");

        quotedRequest.setRequestPrice(priceName, quotedRequestPrice);

        Currency dealtCurrency = singleLegOrder.getDealtCurrency();
        if (dealtCurrency == null)
            throw new NullPointerException("dealtCurrency");

        quotedRequestPrice.setDealtCurrency(dealtCurrency);
        quotedRequestPrice.setDealtAmount(matchEventLeg.getFinalAcceptanceAmount());
        if( matchEventLeg.getTenor() != null ) {
            quotedRequestPrice.setTenor(new Tenor(matchEventLeg.getTenor()));
        }
        quotedRequestPrice.setValueDate( DateTimeFactory.newDate( new Date( matchEventLeg.getValueDate() ) ) );
        quotedRequestPrice.setSettledCurrency( singleLegOrder.getBaseCurrency().isSameAs( dealtCurrency ) ? singleLegOrder.getTermCurrency() : singleLegOrder.getBaseCurrency() );

        OrderRequest.RequestLeg requestLeg = singleLegOrder.getRequestLeg();
        if (requestLeg == null)
            throw new NullPointerException("requestLeg");

        OrderRequest.RequestLeg.BuySellMode buySellMode = requestLeg.getBuySellMode();
        if (buySellMode == null)
            throw new NullPointerException("buySellMode");

        switch (buySellMode) {
            case SELL:
                quotedRequestPrice.setBidOfferMode(DealingPrice.BID);
                quotedRequestPrice.setAcceptedPriceBidOfferMode(DealingPrice.BID);
                break;
            case BUY:
                quotedRequestPrice.setBidOfferMode(DealingPrice.OFFER);
                quotedRequestPrice.setAcceptedPriceBidOfferMode(DealingPrice.OFFER);
                break;
            case TWO_WAY:
                // single leg order is not expected to get TWO_WAY, still we set the mode
                quotedRequestPrice.setBidOfferMode(DealingPrice.TWO_WAY);
                quotedRequestPrice.setAcceptedPriceBidOfferMode(DealingPrice.TWO_WAY);
                break;
            default:
                LOG.error("Invalid buySellMode=" + buySellMode + ", orderId " + singleLegOrder.get_id());
                break;
        }
    }

    private void transformEventTimes(final SingleLegTrade singleLegTrade, final FXSingleLeg trd, final SingleLegOrderMatch matchEvent) throws NullPointerException {
        MatchEventTimes matchEventTimes = matchEvent.getMatchEventTimes();
        if (matchEventTimes == null)
            throw new NullPointerException("matchEventTimes");

        TradeEventTimes tradeEventTimes = singleLegTrade.getEventTimes();
        if (tradeEventTimes == null)
            throw new NullPointerException("tradeEventTimes");

        SingleLegOrder singleLegOrder = singleLegTrade.getOrderRequest();
        if (singleLegOrder == null)
            throw new NullPointerException("singleLegOrder");

        OrderRequestEventTimes orderReqEventTimes = singleLegOrder.getOrderRequestEventTimes();
        if (orderReqEventTimes == null)
            throw new NullPointerException("orderReqEventTimes");

        if (matchEventTimes.getJmsProxyReceivedRate() != 0)
            trd.setRateRecvdByPrxy(new Timestamp(matchEventTimes.getJmsProxyReceivedRate()));
        if (matchEventTimes.getClientQueriedRate() != 0)
            trd.setRateQueriedByUser(new Timestamp(matchEventTimes.getClientQueriedRate()));
        if (matchEventTimes.getJmsProxySentRate() != 0)
            trd.setRateSentByPrxy(new Timestamp(matchEventTimes.getJmsProxySentRate()));
        if (matchEventTimes.getClientReceivedRate() != 0)
            trd.setRateRecvdByUser(new Timestamp(matchEventTimes.getClientReceivedRate()));
        if (matchEventTimes.getClientDisplayRate() != 0)
            trd.setRateDsplyByUser(new Timestamp(matchEventTimes.getClientDisplayRate()));
        if (matchEventTimes.getClientUserAcceptedRate() != 0)
            trd.setRateAcptByUser(new Timestamp(matchEventTimes.getClientUserAcceptedRate()));
        if (matchEventTimes.getClientSentAcceptance() != 0)
            trd.setAcptSentByUser(new Timestamp(matchEventTimes.getClientSentAcceptance()));
        if (matchEventTimes.getAcceptanceReceivedByIS() != 0)
            trd.setAcptRecvdByApp(new Timestamp(matchEventTimes.getAcceptanceReceivedByIS()));
        if (matchEventTimes.getRateAggregatedByServer() != 0)
            trd.setRateAggrtByApp(new Timestamp(matchEventTimes.getRateAggregatedByServer()));
        if (orderReqEventTimes.getReceivedTime() != 0)
            trd.setOrdRecvdByApp(new Timestamp(orderReqEventTimes.getReceivedTime()));
        if (matchEventTimes.getOrderMatchedByServer() != 0)
            trd.setOrdMtchdByApp(new Timestamp(matchEventTimes.getOrderMatchedByServer()));
        if (singleLegTrade.getConfirmationTime() != 0)
            trd.setCnfrmByUser(new Timestamp(singleLegTrade.getConfirmationTime()));

        if (tradeEventTimes.getNextRateReceivedByAppTime() != 0)
            trd.setNxtRateRecvdByApp(new Timestamp(tradeEventTimes.getNextRateReceivedByAppTime()));
        if (tradeEventTimes.getAcceptanceSentByAppTime() != 0)
            trd.setAcptSentByApp(new Timestamp(tradeEventTimes.getAcceptanceSentByAppTime()));
        if (tradeEventTimes.getAcceptanceReceivedByAdaptorTime() != 0)
            trd.setAcptRecvdByAdpt(new Timestamp(tradeEventTimes.getAcceptanceReceivedByAdaptorTime()));
        if (tradeEventTimes.getAcceptanceSentByAdaptorTime() != 0)
            trd.setAcptSentByAdpt(new Timestamp(tradeEventTimes.getAcceptanceSentByAdaptorTime()));

        switch (singleLegTrade.getState().getName()) {
            case TSFAILED:
            case TSREJECTED:
                if (tradeEventTimes.getResponseReceivedByAdaptorTime() != 0)
                    trd.setRjctRecvdFrmPrvdr(new Timestamp(tradeEventTimes.getResponseReceivedByAdaptorTime()));
                if (tradeEventTimes.getResponseReceivedByAppTime() != 0)
                    trd.setRjctRecvdByApp(new Timestamp(tradeEventTimes.getResponseReceivedByAppTime()));
                if (tradeEventTimes.getResponseSentByAppTime() != 0)
                    trd.setRjctSentByApp(new Timestamp(tradeEventTimes.getResponseSentByAppTime()));

                break;

            default:
                if (tradeEventTimes.getResponseReceivedByAdaptorTime() != 0)
                    trd.setVrfyRecvdFrmPrvdr(new Timestamp(tradeEventTimes.getResponseReceivedByAdaptorTime()));
                if (tradeEventTimes.getResponseReceivedByAppTime() != 0)
                    trd.setVrfyRecvdByApp(new Timestamp(tradeEventTimes.getResponseReceivedByAppTime()));
                if (tradeEventTimes.getResponseSentByAppTime() != 0)
                    trd.setVrfySentByApp(new Timestamp(tradeEventTimes.getResponseSentByAppTime()));

                break;
        }
    }

    private void transformClientTag(final FXSingleLeg trd, final MatchEvent matchEvent) {
        if (matchEvent.getAccountIds() != null) {
            for (AccountID accountID : matchEvent.getAccountIds()) {
                if ("ClientTagID".equals(accountID.getAccountId())) {
                    trd.setClientTag(accountID.getAccountIdValue());
                }
            }
        }
    }

    private FXCoverRate createFXCoverRate(final SingleLegTrade singleLegTrade, final TradeLeg tradeLeg, final ApplicationEventCodes appEventCode) throws NullPointerException
    {
        // Create FXCoverRate for all the verified trades.
        switch (appEventCode) {
            case EVENT_ESP_TRADE_REJECTED:
            case EVENT_ESP_TRADE_VERIFIED:
            case EVENT_ESP_TRADE_POST_RATE_VERIFIED:
            case EVENT_ESP_TRADE_CREATED_VERIFIED:
                break;
            default:
                return null;
        }

        Organization legalEntityOrg = DealingModelUtil.getCounterPartyLegalEntity( singleLegTrade).getOrganization();
        if (legalEntityOrg == null)
            throw new NullPointerException("legalEntityOrg");

        Namespace namespace = legalEntityOrg.getNamespace();
        if (namespace == null)
            throw new NullPointerException("namespace");

        FXCoverRate fxCoverRate = new FXCoverRateC();
        fxCoverRate.setNamespace(namespace);

        FXRate fxRate = fxCoverRate.getFXRate();
        if (fxRate == null)
            throw new NullPointerException("fxRate");

        fxRate.setBaseCurrency(singleLegTrade.getBaseCurrency());
        fxRate.setVariableCurrency(singleLegTrade.getTermCurrency());
        fxRate.setFXRateBasis(singleLegTrade.getMatchEvent().getFxRateBasis());
        fxRate.setFXRateConvention(fxRate.getFXRateBasis().getFXRateConvention());

        FXRate marketFxRate = FXFactory.newFXRate();
        marketFxRate.setBaseCurrency(singleLegTrade.getBaseCurrency());
        marketFxRate.setVariableCurrency(singleLegTrade.getTermCurrency());
        marketFxRate.setFXRateBasis(fxRate.getFXRateBasis());
        marketFxRate.setFXRateConvention(fxRate.getFXRateConvention());

        OrderRequest orderRequest = singleLegTrade.getOrderRequest();
        if (orderRequest == null)
            throw new NullPointerException("orderRequest");

        TradeLeg.CoverRateDescriptor coverRateDesc = tradeLeg.getCoverRateDescriptor();
        if (coverRateDesc == null)
            throw new NullPointerException("coverRateDesc");

        double forwardPoints = tradeLeg.getForwardPoints();
        double baseForwardPoints = forwardPoints;
        double coverRate,executionRate;
        if(singleLegTrade.isStreamingNonSpot()){
        	baseForwardPoints = MathUtilC.subtract(forwardPoints,
	        		MathUtilC.add(coverRateDesc.getPMPreTradeFwdSpread(),coverRateDesc.getPMPostTradeFwdSpread()));
        }
        if(ISUtilImpl.TRD_SPOT_CLSF.getShortName().equals(singleLegTrade.getClassification())){
            coverRate = coverRateDesc.getCoverRate();
            executionRate = coverRateDesc.getCoverExecutionRate();
        }else {
            coverRate = coverRateDesc.getCoverRate() - forwardPoints;
            executionRate = coverRateDesc.getCoverExecutionRate() - baseForwardPoints;
            if(coverRateDesc.isSorUsingSpotAndMDS()){
            	executionRate = coverRateDesc.getCoverExecutionRate();
            }
            if(coverRateDesc.getCoverExecutionSpotRate() != null){
                executionRate = coverRateDesc.getCoverExecutionSpotRate();
            }
        }
        if (tradeLeg.isBuyingBaseCurrency()) {
            fxRate.setOfferSpotRate(coverRate);
            fxRate.setOfferForwardPoints(forwardPoints);
            marketFxRate.setOfferSpotRate(executionRate);
            marketFxRate.setOfferForwardPoints(baseForwardPoints);
        }
        else {
            fxRate.setBidSpotRate(coverRate);
            fxRate.setBidForwardPoints(forwardPoints);
            marketFxRate.setBidSpotRate(executionRate);
            marketFxRate.setBidForwardPoints(baseForwardPoints);
        }
        LOG.info("coverRateDesc.getCoverExecutionRate(): "+coverRateDesc.getCoverExecutionRate()+", executionRate: "+executionRate+", baseForwardPoints: "+baseForwardPoints+", Rate: " + marketFxRate.getRate ()+
        ", Spot rate: "+ marketFxRate.getSpotRate()+", forward points: "+marketFxRate.getForwardPoints()+", singleLegTrade.isStreamingNonSpot(): "+singleLegTrade.isStreamingNonSpot()
        +", coverRateDesc.isSorUsingSpotAndMDS(): "+coverRateDesc.isSorUsingSpotAndMDS()+", coverRateDesc.getCoverExecutionSpotRate(): "+coverRateDesc.getCoverExecutionSpotRate());
        // Set the market FXRate.
        fxCoverRate.setMarketFxRate(marketFxRate);

        //Spread can be negative
        if (Math.abs(coverRateDesc.getPPSpotSpread()) > ISCommonConstants.MIN_RATE) {
            double ppSpotSpread = MathUtilC.correctFloatingPointsCalculationPrecision(coverRateDesc.getPPSpotSpread());
            fxCoverRate.addSpread(new SpreadElementC(FXCoverRate.PP_SPOT_SPREAD, ppSpotSpread));
            fxCoverRate.addSpread(new SpreadElementC(FXCoverRate.PP_CUST_SPREAD, ppSpotSpread));
        }

        if (Math.abs(coverRateDesc.getPMSpotSpread()) > ISCommonConstants.MIN_RATE) {
            double pmSpotSpread = MathUtilC.correctFloatingPointsCalculationPrecision(coverRateDesc.getPMSpotSpread());
            fxCoverRate.addSpread(new SpreadElementC(FXCoverRate.PM_SPOT_SPREAD, pmSpotSpread));
        }

        if (Math.abs(coverRateDesc.getPMPreTradeSpread()) > ISCommonConstants.MIN_RATE) {
            double pmPreTradeSpread = MathUtilC.correctFloatingPointsCalculationPrecision(coverRateDesc.getPMPreTradeSpread());
            fxCoverRate.addSpread(new SpreadElementC(FXCoverRate.PM_PRE_SPOT_SPREAD, pmPreTradeSpread));
        }

        if (Math.abs(coverRateDesc.getPMPostTradeSpread()) > ISCommonConstants.MIN_RATE) {
            double pmPostTradeSpread = MathUtilC.correctFloatingPointsCalculationPrecision(coverRateDesc.getPMPostTradeSpread());
            fxCoverRate.addSpread(new SpreadElementC(FXCoverRate.PM_POST_SPOT_SPREAD, pmPostTradeSpread));
        }

        if (coverRateDesc.isMinSpreadEnabled()) {
            double minSpread = MathUtilC.correctFloatingPointsCalculationPrecision(coverRateDesc.getPMMinSpread());
            fxCoverRate.addSpread(new SpreadElementC(FXCoverRate.PM_MIN_SPREAD, minSpread));
        }

        if (coverRateDesc.isMaxSpreadEnabled()) {
            double maxSpread = MathUtilC.correctFloatingPointsCalculationPrecision(coverRateDesc.getPMMaxSpread());
            fxCoverRate.addSpread(new SpreadElementC(FXCoverRate.PM_MAX_SPREAD, maxSpread));
        }

        if(Math.abs(coverRateDesc.getSkew()) > ISCommonConstants.MIN_RATE){
            double skew = MathUtilC.correctFloatingPointsCalculationPrecision(coverRateDesc.getSkew());
            fxCoverRate.addSpread(new SpreadElementC(FXCoverRate.PM_SKEW_SPREAD, skew));
        }
		
		if(singleLegTrade.isStreamingNonSpot()){
	        if (Math.abs(coverRateDesc.getPMFwdSpread()) > ISCommonConstants.MIN_RATE) {
	            double pmFwdSpread = MathUtilC.correctFloatingPointsCalculationPrecision(coverRateDesc.getPMFwdSpread());
	            fxCoverRate.addSpread(new SpreadElementC(FXCoverRate.PM_FWD_SPREAD, Math.abs(pmFwdSpread)));
	        }
	
	        
	        if (Math.abs(coverRateDesc.getPMPreTradeFwdSpread()) > ISCommonConstants.MIN_RATE) {
	            double pmPreTradeFwdSpread = MathUtilC.correctFloatingPointsCalculationPrecision(coverRateDesc.getPMPreTradeFwdSpread());
	            fxCoverRate.addSpread(new SpreadElementC(FXCoverRate.PM_PRE_FWD_SPREAD, Math.abs(pmPreTradeFwdSpread)));
	        }
	
	        if (Math.abs(coverRateDesc.getPMPostTradeFwdSpread()) > ISCommonConstants.MIN_RATE) {
	            double pmPostTradeFwdSpread = MathUtilC.correctFloatingPointsCalculationPrecision(coverRateDesc.getPMPostTradeFwdSpread());
	            fxCoverRate.addSpread(new SpreadElementC(FXCoverRate.PM_POST_FWD_SPREAD, Math.abs(pmPostTradeFwdSpread)));
	        }
        }

        return fxCoverRate;
    }
}
