package com.integral.notifications.mis;

import com.integral.exception.IdcNoSuchObjectException;
import com.integral.exception.IdcOptimisticLockException;
import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.dealing.RequestC;
import com.integral.finance.dealing.StagingRequestC;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.fx.FXSingleLegC;
import com.integral.finance.trade.CptyTrade;
import com.integral.finance.trade.CptyTradeC;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.finance.trade.facade.TradeStateFacade;
import com.integral.finance.trade.functor.TradeServiceTransactionFunctor;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.model.dealing.descriptor.CoveredTradeDescriptor;
import com.integral.netting.model.MarketSpotPrice;
import com.integral.netting.model.MarketSpotPriceDealingModelC;
import com.integral.netting.model.NettingPortfolio;
import com.integral.netting.model.NettingPortfolioC;
import com.integral.netting.model.NettingPortfolioDealingModelC;
import com.integral.netting.model.NettingTradeRequest;
import com.integral.netting.model.NettingTradeRequestC;
import com.integral.netting.model.NettingTradeRequestDealingModelC;
import com.integral.notifications.NotificationConfiguration;
import com.integral.notifications.cache.NotificationCacheFactory;
import com.integral.notifications.cache.NotificationCacheMBean;
import com.integral.notifications.cache.NotificationCacheMBeanImpl;
import com.integral.persistence.ClusterCommitEventAdapterC;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.spaces.config.MetaspacesConfigMBean;
import com.integral.spaces.config.MetaspacesConfigMBeanImpl;
import com.integral.spaces.notification.*;
import com.integral.staging.Order;

import com.integral.user.User;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Vector;

public class MISNotificationHandler implements NotificationHandler {
    private final static Log LOG = LogFactory.getLog(MISNotificationHandler.class);
    private final static String METRICS_CATEGORY = "com.integral.metrics";
    private final static Log METRICS_LOG = LogFactory.getLog(METRICS_CATEGORY);
    private final static MISExpirableCacheMonitor EXPIRABLE_CACHE_MONITOR = MISExpirableCacheMonitor.getInstance();
    private static final ISMBean isMBean = ISFactory.getInstance().getISMBean();
    private static MISStagingAreaExpirableCache stagingAreaExpirableCache;
    private static MISRequestExpirableCache requestExpirableCache;
    private static MISFXSingleLegExpirableCache singleLegExpirableCache;
    private static MISNettingPortfolioExpirableCache nettingPortfolioExpirableCache;
    private static MISNettingTradeRequestExpirableCache nettingTradeRequestExpirableCache;

    private int optimisticLockMaxRetry = 5;
    private long commitTimeMaxMilliSeconds = 90000;
    private MISStagingAreaOrderTransformer StagingAreaOrderTransformer;
    private MISSingleLegOrderTransformer SingleLegOrderTransformer;
    private MISSingleLegTradeTransformer SingleLegTradeTransformer;
    private MISNettingPortfolioTransformer NettingPortfolioTransformer;
    private MISNettingTradeRequestTransformer NettingTradeRequestTransformer;

    private final static String MS_REDILEVERY_COUNT = "MS_DELIVERY";

    @Override
    public long getLongIdentifier() {
        return NotificationConfiguration.NOTIFICATION_HANDLER_MIS.getLongIdentifier();
    }

    /**
     * Initialize MIS notification handling operations.
     * Must be called before the handler is used.
     */
    public void initialize() {
        MetaspacesConfigMBean mBean = MetaspacesConfigMBeanImpl.getInstance();
        Properties props = mBean.getMISNotificationHandlerProperty();
        NotificationCacheMBean cacheMBean = NotificationCacheMBeanImpl.getInstance();

        boolean cacheDisabled = !cacheMBean.isCacheEnabled();
        optimisticLockMaxRetry = Integer.parseInt(props.getProperty(MISNotificationConstants.OPTIMISTIC_LOCK_MAX_RETRY, "5"));
        commitTimeMaxMilliSeconds = Long.parseLong(props.getProperty(MISNotificationConstants.COMMIT_TIME_MAX_MILLISEC, "90000"));
        LOG.info("configured cacheDisabled=" + cacheDisabled );

        stagingAreaExpirableCache = (MISStagingAreaExpirableCache) NotificationCacheFactory.getNotificationCache(StagingRequestC.class);
        requestExpirableCache = (MISRequestExpirableCache) NotificationCacheFactory.getNotificationCache(RequestC.class);
        singleLegExpirableCache = (MISFXSingleLegExpirableCache) NotificationCacheFactory.getNotificationCache(FXSingleLeg.class);
        nettingPortfolioExpirableCache = (MISNettingPortfolioExpirableCache)NotificationCacheFactory.getNotificationCache(NettingPortfolioC.class);
        nettingTradeRequestExpirableCache = (MISNettingTradeRequestExpirableCache)NotificationCacheFactory.getNotificationCache(NettingTradeRequestC.class);

        EXPIRABLE_CACHE_MONITOR.setCacheDisabled(cacheDisabled);
        if (!cacheDisabled) {
            EXPIRABLE_CACHE_MONITOR.add("RequestC", requestExpirableCache);
            EXPIRABLE_CACHE_MONITOR.add("FXSingleLegC", singleLegExpirableCache);
        }
        
        StagingAreaOrderTransformer = new MISStagingAreaOrderTransformer((MISStagingAreaExpirableCache) stagingAreaExpirableCache);
        SingleLegOrderTransformer = new MISSingleLegOrderTransformer((MISRequestExpirableCache)requestExpirableCache);
        SingleLegTradeTransformer = new MISSingleLegTradeTransformer((MISRequestExpirableCache)requestExpirableCache, (MISFXSingleLegExpirableCache)singleLegExpirableCache);
        NettingPortfolioTransformer = new MISNettingPortfolioTransformer((MISNettingPortfolioExpirableCache)nettingPortfolioExpirableCache);
        NettingTradeRequestTransformer = new MISNettingTradeRequestTransformer((MISNettingTradeRequestExpirableCache)nettingTradeRequestExpirableCache);
    }

    @Override
    public NHResponse handle(final Notification notification) {
        long startTime = System.currentTimeMillis();
        MISPerformanceMetrics performanceMetrics = new MISPerformanceMetrics();
        notification.setHandlerPerformanceMetrics(performanceMetrics);

        Object notificationCompleteObj = notification.getCompleteObject();
        int optimisticLockRetryCount = 0;
        while (optimisticLockRetryCount < optimisticLockMaxRetry) {
            try {
                if (SingleLegOrder.class.isAssignableFrom(notification.getDataObjectType())){
                    transformAndPersist(notification, (SingleLegOrder)notificationCompleteObj);
                    return new NHResponse(NHResponseCode.SUCCESS);
                }
                else if (SingleLegTrade.class.isAssignableFrom(notification.getDataObjectType())){
                    transformAndPersist(notification, (SingleLegTrade)notificationCompleteObj);
                    return new NHResponse(NHResponseCode.SUCCESS);
                }
                else if (NettingPortfolioDealingModelC.class.isAssignableFrom(notification.getDataObjectType())){
                	NettingPortfolioDealingModelC dealingModel = (NettingPortfolioDealingModelC)notificationCompleteObj;
                    transformAndPersist(notification, (NettingPortfolio)dealingModel.getNettingPortfolioEntity());
                    return new NHResponse(NHResponseCode.SUCCESS);
                }
                else if (NettingTradeRequestDealingModelC.class.isAssignableFrom(notification.getDataObjectType())){
                	NettingTradeRequestDealingModelC dealingModel = (NettingTradeRequestDealingModelC)notificationCompleteObj;
                    transformAndPersist(notification, (NettingTradeRequest)dealingModel.getNettingTradeRequestEntity());
                    return new NHResponse(NHResponseCode.SUCCESS);
                }
                else if (MarketSpotPriceDealingModelC.class.isAssignableFrom(notification.getDataObjectType())){
                	MarketSpotPriceDealingModelC dealingModel = (MarketSpotPriceDealingModelC)notificationCompleteObj;
                    transformAndPersist(notification, (MarketSpotPrice)dealingModel.getMarketSpotPriceEntity());
                    return new NHResponse(NHResponseCode.SUCCESS);
                }
                else if (Order.class.isAssignableFrom( notification.getDataObjectType() )){
                	Order stagingOrder = (Order)notificationCompleteObj;
                	transformAndPersist(notification, stagingOrder);
                	return new NHResponse(NHResponseCode.SUCCESS);
                }
                else{
                    LOG.error("Unsupported MIS notification object: " + notificationCompleteObj.getClass().getName() + " " + notification.toString());
                }
                performanceMetrics.setTotalTime(System.currentTimeMillis() - startTime);
                METRICS_LOG.info(performanceMetrics.getMetrics(notification));
                break;
            } catch (IdcOptimisticLockException ole) {
                /*
                * Some actual db record update has happened concurrently creating a version id mismatch.
                * Or that record is corrupted.
                */
                String details = ole.getDetailMessage();
                if (org.eclipse.persistence.exceptions.OptimisticLockException.class.isAssignableFrom(ole.getClass())) {
                    org.eclipse.persistence.exceptions.OptimisticLockException ope = (org.eclipse.persistence.exceptions.OptimisticLockException)((Object)ole);
                    details += "' query='" + ope.getQuery().toString();
                }

                optimisticLockRetryCount++;
                LOG.warn("IdcOptimisticLockException occurred: '" + details + "' retry count=" + optimisticLockRetryCount + " " + notification.toString());
                startTime = System.currentTimeMillis();
            } catch (Exception e) {
                LOG.error(notification.toString(), e);
                break;
            } finally {
                // Cleanup transaction irrespective of whether success/failure.
                // This cleanup helps for subsequent transaction incase current transaction is failed.
                IdcSessionManager.getInstance().setTransaction(null);
            }
        }
        return new NHResponse(NHResponseCode.FAILURE);
    }

	@Override
    public NHResponse handleRedelivery(Notification notification,String errorDesc) {
        try {
            Class objectType = notification.getDataObjectType();
            if (SingleLegOrder.class.isAssignableFrom(objectType)) {
                return processRedeliveredOrderNotification(notification);
            } else if (SingleLegTrade.class.isAssignableFrom(objectType)) {
                return processRedeliveredTradeNotification(notification);
            } else {
                LOG.warn("handleRedelivery : Unsupported objectType=" + objectType + ",notification=" + notification);
            }
        } catch (Exception ex) {
            LOG.error("handleRedelivery : Exception in processing notification=" + notification);
        }
        return new NHResponse(NHResponseCode.FAILURE);
    }

    protected NHResponse processRedeliveredTradeNotification(Notification notification) throws PersistenceException {
        MISPerformanceMetrics performanceMetrics = new MISPerformanceMetrics();
        notification.setHandlerPerformanceMetrics(performanceMetrics);
        String namespace = notification.getNamespaceName();
        SingleLegTrade singleLegTrade = (SingleLegTrade) notification.getCompleteObject();
        String tradeId = singleLegTrade.get_id();
        ApplicationEventCodes appEventCode = ApplicationEventCodes.fromCode(notification.getAppEventCode());
        FXSingleLeg trade;
        if (!MISHandlerUtil.isTradeUpdateEvent(appEventCode)) {
            trade = queryTrade(performanceMetrics, namespace, tradeId);
            if (trade != null) {
                LOG.info("processRedeliveredTradeNotification : Trade for tradeId=" + tradeId + " already present in database. Not processing redelivered notification=" + notification);
                return new NHResponse(NHResponseCode.SUCCESS);
            } else {
                LOG.info("processRedeliveredTradeNotification : Trade for tradeId=" + tradeId + " not present in database. Processing redelivered notification=" + notification);
                return handle(notification);
            }
        } else {
            trade = queryTrade(performanceMetrics, namespace, tradeId);
            if (trade != null) {
                boolean processingRequired = isRedeliveredNotificationProcessingRequired(appEventCode, trade);
                String state = "N/A";
                if (trade.getState() != null) {
                    state = trade.getState().getShortName();
                }
                LOG.info("processRedeliveredTradeNotification : aec="+appEventCode.name()+", tradeId="+tradeId+", state="+state+", processingRequired="+processingRequired );
                if (processingRequired) {
                    return handle(notification);
                } else {
                    return dropRedeliveredNotification(notification);
                }
            }
            else{
                LOG.warn("processRedeliveredTradeNotification : Trade for tradeId="+tradeId+" not found. Notification processing failed.");
                return new NHResponse(NHResponseCode.FAILURE);
            }
        }
    }

    protected FXSingleLeg queryTrade(MISPerformanceMetrics performanceMetrics, String namespace, String tradeId) throws PersistenceException {
        return SingleLegTradeTransformer.queryTrade(namespace, tradeId, performanceMetrics);
    }

    protected boolean isRedeliveredNotificationProcessingRequired(ApplicationEventCodes appEventCode, FXSingleLeg trade) {
        boolean processingRequired = false;
        TradeStateFacade tsf = (TradeStateFacade) trade.getFacade(TradeStateFacade.TRADE_STATE_FACADE);
        switch (appEventCode) {
            case EVENT_ESP_TRADE_CANCEL:
            case EVENT_ESP_NET_TRADE_CANCEL:
                if (!tsf.isCancelled() ) {
                    processingRequired = true;
                }
                break;

            case EVENT_ESP_TRADE_CONFIRMED:
                //Only verified trades can be confirmed.
                if (tsf.isVerified()) {
                    processingRequired = true;
                }
                break;

            case EVENT_ESP_TRADE_REJECTED:
                if (tsf.isPending() || tsf.isNew() || tsf.isCreated() || tsf.isAccepted()) {
                    processingRequired = true;
                }
                break;
            case EVENT_ESP_TRADE_PRE_VERIFIED:
            case EVENT_ESP_TRADE_POST_RATE_VERIFIED:
            case EVENT_ESP_TRADE_VERIFIED:
                if (tsf.isPending() || tsf.isNew() || tsf.isCreated() || tsf.isAccepted()) {
                    processingRequired = true;
                }
                break;

            case EVENT_ESP_TRADE_AMEND:
                if (tsf.isVerified() || tsf.isConfirmed()) {
                    processingRequired = true;
                }
                break;

            case EVENT_ESP_NET_TRADE_AMEND:
                if( tsf.isNet() ){
                    processingRequired = true;
                }
                break;

            case EVENT_ESP_TRADE_NETTED:
                if (!tsf.isNetted()) {
                    processingRequired = true;
                }
                break;
            case EVENT_ESP_MS_LOOKUP:
                    processingRequired = true;
                break;
            case EVENT_ESP_POST_TRADE_UPDATE:
                	processingRequired = true;
            	break;
        }
        return processingRequired;
    }

    protected NHResponse processRedeliveredOrderNotification(Notification notification) throws PersistenceException {
        MISPerformanceMetrics performanceMetrics = new MISPerformanceMetrics();
        notification.setHandlerPerformanceMetrics(performanceMetrics);
        String namespace = notification.getNamespaceName();
        ApplicationEventCodes appEventCode = ApplicationEventCodes.fromCode(notification.getAppEventCode());
        if (!MISHandlerUtil.isOrderUpdateEvent(appEventCode)) {
            SingleLegOrder singleLegOrder = (SingleLegOrder) notification.getCompleteObject();
            String orderId = singleLegOrder.get_id();
            RequestC req = queryRequest(performanceMetrics, namespace, orderId);
            if( req != null ){
                LOG.info("processRedeliveredOrderNotification : Request for orderId="+orderId+" already present in database. Not processing redelivered notification="+notification);
                return dropRedeliveredNotification(notification);
            }
            else{
                LOG.info("processRedeliveredOrderNotification : Request for orderId="+orderId+" not present in database. Processing redelivered notification="+notification);
                return handle(notification);
            }
        }
        else {
            return handle(notification);
        }
    }

    protected NHResponse dropRedeliveredNotification(Notification notification) {
        LOG.info("dropRedeliveredNotification : Not processing redelivered notification="+notification);
        return new NHResponse(NHResponseCode.SUCCESS);
    }

    protected RequestC queryRequest(MISPerformanceMetrics performanceMetrics, String namespace, String orderId) throws PersistenceException {
        return SingleLegOrderTransformer.queryRequest(namespace, orderId, performanceMetrics);
    }

    private ApplicationEventCodes getApplicationEventCodes(final Notification notification) throws NullPointerException {
        ApplicationEventCodes appEventCode = ApplicationEventCodes.fromCode(notification.getAppEventCode());
        if (appEventCode == null)
            throw new NullPointerException("appEventCode");

        return appEventCode;
    }

    private void commitTransaction(final IdcTransaction tx, final Notification notification, final String recordClassName) {
        long commitStartTime = System.currentTimeMillis();
        tx.commit();
        long commitTime = System.currentTimeMillis() - commitStartTime;

        MISPerformanceMetrics performanceMetrics = (MISPerformanceMetrics)notification.getHandlerPerformanceMetrics();
        performanceMetrics.setTargetDbCommitTime(commitTime);

        String msg = "Committed " + recordClassName + " (took " + commitTime + " millisec) " + notification.toString();
        if (commitTime > commitTimeMaxMilliSeconds) {
            LOG.warn("ALERT: Database commit took abnormally long. " + msg);
        } else {
            LOG.info(msg);
        }
    }

    private void transformAndPersist(final Notification notification, final SingleLegOrder singleLegOrder) throws Exception {
        ApplicationEventCodes appEventCode = getApplicationEventCodes(notification);
        if (appEventCode == ApplicationEventCodes.EVENT_ESP_ORDER_FAILED) {
            LOG.info("Discarding failed singleLegOrder " + notification.toString());
            return;
        }

        MISPerformanceMetrics performanceMetrics = (MISPerformanceMetrics)notification.getHandlerPerformanceMetrics();
        String namespace = notification.getNamespaceName();
        String orderId = singleLegOrder.get_id();
        if (orderId == null)
            throw new NullPointerException("orderId");

        RequestC req = SingleLegOrderTransformer.getPersistentRequestRecord(namespace, orderId, appEventCode, performanceMetrics);

        IdcSessionContext context = IdcSessionManager.getInstance().getSessionContext( singleLegOrder.getUser());
        IdcSessionManager.getInstance().setSessionContext( context );
        IdcSessionManager.getInstance().setTransaction(null);
        IdcTransaction tx = newTransaction(singleLegOrder.getUser(), "persistOrder");
        SingleLegOrderTransformer.addReadOnlyClasses(tx);
        setMultiAppMessageConfig( tx, appEventCode );

        RequestC registeredRequest = (RequestC)req.getRegisteredObject();
        registeredRequest.setSpaces(true);

        long startTransformTime = System.currentTimeMillis();
        SingleLegOrderTransformer.transform(registeredRequest, singleLegOrder, appEventCode);
        performanceMetrics.setTransformTime(System.currentTimeMillis() - startTransformTime);
        // The quotedRequest registration causes issues while persisting trade.
        // Related to issue ESF-71. The original issue was spac-1411.
        /*
        if(singleLegOrder.getMarketSnapshot() == null){
            LOG.info("Need to lookup MarketSnapshot for order:"+singleLegOrder.get_id());
            String marketSnapshot = DealingModelTradeHelper.getFormattedMarketSnapshot(singleLegOrder);
            if(marketSnapshot != null){
                LOG.info("Got MarketSnapshot on query, will set it on the request");
                registeredRequest.setMarketSnapshot(MarketSnapshotUtil.convertToOldSnapshot(singleLegOrder.getMarketSnapshot()));
            }else{
                LOG.info("Could not find MarketSnapshot, sending out a notification for re-attempt");
                publishNotification(notification,ApplicationEventCodes.EVENT_ESP_MS_LOOKUP);
            }
        }
        */
        commitTransaction(tx, notification, "RequestC");
        addToRequestExpirableCache(req);  // even for update cases so that cache always has the latest
    }

    private void publishNotification(Notification notification, ApplicationEventCodes eventEspMsLookup) {
        Notification clonedNotification = null;
        try {
            clonedNotification = notification.clone();
            clonedNotification.set_id(null);
            clonedNotification.setAppEventCode(eventEspMsLookup.getCode());
            if (clonedNotification.getNotificationProperties() != null && clonedNotification.getNotificationProperties().get(MS_REDILEVERY_COUNT) != null) {
                LOG.info("Notification message was delivered for id:-" + clonedNotification.getObjectId() + " current delivery count:-" + clonedNotification.getNotificationProperties().get(MS_REDILEVERY_COUNT));
                try {
                    int count = Integer.parseInt((String) clonedNotification.getNotificationProperties().get(MS_REDILEVERY_COUNT));
                    if (count <= isMBean.getMsRetryCount()) {
                        clonedNotification.getNotificationProperties().put(MS_REDILEVERY_COUNT, (count + 1));
                    } else {
                        LOG.warn("MISSING-MARKETSNAPSHOT, maximum number of attempts exceeded, unable to retrieve MarketSnapshot for notification:-" + clonedNotification);
                        return;
                    }
                } catch (Exception ex) {
                    // If for any reason we are not able to parse, set the maximum count to break the loop
                    clonedNotification.getNotificationProperties().put(MS_REDILEVERY_COUNT, isMBean.getMsRetryCount());
                    return;
                }
            } else {
                Map<String, String> notificationProps = new HashMap<String, String>();
                notificationProps.put(MS_REDILEVERY_COUNT, "1");
                clonedNotification.setNotificationProperties(notificationProps);
            }

            NotificationManager.getInstance().notify(clonedNotification);
        } catch (CloneNotSupportedException cnse) {
            LOG.error("Unable to clone notification Object", cnse);
        } catch (NotificationException nex) {
            LOG.error("Unable to send Notification:- " + notification, nex);
        }
    }


    private void addToRequestExpirableCache(RequestC req){
        requestExpirableCache.add( req );
    }

    private void transformAndPersist(final Notification notification, final SingleLegTrade singleLegTrade) throws Exception {
        ApplicationEventCodes appEventCode = getApplicationEventCodes(notification);
        MISPerformanceMetrics performanceMetrics = (MISPerformanceMetrics)notification.getHandlerPerformanceMetrics();
        boolean processingRequired = isProcessingRequired(appEventCode,singleLegTrade);
        if( !processingRequired ){
            if( LOG.isDebugEnabled() ){
                LOG.debug( "Not processing aec="+appEventCode.name()+",trade="+singleLegTrade.get_id()+",notification="+notification.toString() );
            }
            return;
        }
        String namespace = notification.getNamespaceName();
        String tid = singleLegTrade.get_id();
        if (tid == null)
            throw new NullPointerException("tid");

        FXSingleLeg fxSingleLeg = SingleLegTradeTransformer.getPersistentTradeRecord(namespace, tid, appEventCode, performanceMetrics);
        Counterparty oldCpty = null;
        boolean isCptyA = false;
        if ( ApplicationEventCodes.EVENT_ESP_TRADE_AMEND == appEventCode || ApplicationEventCodes.EVENT_ESP_NET_TRADE_AMEND == appEventCode )
        {
            if ( !singleLegTrade.getLegalEntity().isSameAs( fxSingleLeg.getCounterpartyA() ) )
            {
                oldCpty = fxSingleLeg.getCounterpartyA();
                isCptyA = true;
            }
            else if ( !DealingModelUtil.getCounterPartyLegalEntity(singleLegTrade).isSameAs( fxSingleLeg.getCounterpartyB() ) )
            {
                oldCpty = fxSingleLeg.getCounterpartyB();
                isCptyA = false;
            }
        }

        IdcSessionContext context = IdcSessionManager.getInstance().getSessionContext( singleLegTrade.getUser());
        IdcSessionManager.getInstance().setSessionContext( context );
        IdcSessionManager.getInstance().setTransaction(null);
        IdcTransaction tx = newTransaction(singleLegTrade.getUser(), "persistTrade");
        SingleLegTradeTransformer.addReadOnlyClasses(tx);

        // for confirmation workflow, add CptyTradeC as read-only class. This is to avoid optimistic lock exceptions.
        if ( ApplicationEventCodes.EVENT_ESP_TRADE_CONFIRMED == appEventCode )
        {
            tx.addReadOnlyClass( CptyTradeC.class );
        }

        setMultiAppMessageConfig( tx, appEventCode );

        FXSingleLeg registeredFXSingleLeg = (FXSingleLeg)fxSingleLeg.getRegisteredObject();
        registeredFXSingleLeg.setSpaces(true);

        long startTransformTime = System.currentTimeMillis();
        SingleLegTradeTransformer.transform( namespace, ( FXSingleLegC ) registeredFXSingleLeg, singleLegTrade, appEventCode );
        performanceMetrics.setTransformTime( System.currentTimeMillis() - startTransformTime );
        modifyCoverTradeForSTP( fxSingleLeg, singleLegTrade ); //oa-netting
        // Adding it for SPAC-1411, where in an order can end up with an empty snapshot.
        // Specifically checking for MarketSnapshot on SingleLegOrder as on RequestC we set a blank string

        // The quotedRequest registration causes issues while persisting trade.
        // Related to issue ESF-71
        /*if(singleLegTrade.getMatchEvent() != null && singleLegTrade.getMatchEvent().getMarketSnapshot() == null){
            LOG.info("Need to lookup MarketSnapshot for Trade:"+singleLegTrade.get_id());
            String marketSnapshot = DealingModelTradeHelper.getFormattedMarketSnapshot(singleLegTrade);
            if(marketSnapshot != null){
                LOG.info("Got MarketSnapshot on query, will set it on the request");
                RequestC quotedRequest = (RequestC)registeredFXSingleLeg.getRequest().getRegisteredObject();
                quotedRequest.setMarketSnapshot(MarketSnapshotUtil.convertToOldSnapshot(singleLegTrade.getMatchEvent().getMarketSnapshot()));
            }else{
                LOG.info("Couldnot find MarketSnapshot, sending out a notification for re-attempt");
                publishNotification(notification,ApplicationEventCodes.EVENT_ESP_MS_LOOKUP);
            }
        }*/
        if(!singleLegTrade.getMatchEvent().isNettingRequired()) {
            switch (appEventCode) {
                case EVENT_ESP_TRADE_CREATED_VERIFIED:
                case EVENT_ESP_TRADE_VERIFIED:
                case EVENT_ESP_TRADE_NET:
                case EVENT_ESP_TRADE_POST_RATE_VERIFIED:
                    TradeServiceTransactionFunctor.verifyTrade(registeredFXSingleLeg);
                    break;
                case EVENT_ESP_TRADE_CANCEL:
                    TradeServiceTransactionFunctor.cancelTrade(registeredFXSingleLeg);
                    if(registeredFXSingleLeg.isSyntheticCross()){
                        CptyTrade cptyTradeB = registeredFXSingleLeg.getCptyTrade(TradeServiceConstants.CPTY_TRADE_OWNING_CPTY_REF_B);
                        ExpressionBuilder eb = new ExpressionBuilder();
                        Expression expr = eb.getField( "IDCCPTYTRD.SYNXBACKREF").equal( cptyTradeB.getObjectID() );
                        Vector<CptyTrade> syntheticCptyTrades = PersistenceFactory.newSession().readAllObjects(CptyTradeC.class, expr);
                        for(CptyTrade cptyTrade : syntheticCptyTrades){
                            if(cptyTrade.getOwningCptyRef() == TradeServiceConstants.CPTY_TRADE_OWNING_CPTY_REF_B){
                                Trade trade = cptyTrade.getTrade();
                                TradeServiceTransactionFunctor.cancelTrade(trade);
                            }
                        }
                    }
                    break;
                case EVENT_ESP_NET_TRADE_CANCEL:
                    TradeServiceTransactionFunctor.cancelTrade(registeredFXSingleLeg);
                    break;
                case EVENT_ESP_TRADE_AMEND:
                    TradeServiceTransactionFunctor.amendTrade(registeredFXSingleLeg, getAmendWFMsg(fxSingleLeg, registeredFXSingleLeg));
                    break;
                case EVENT_ESP_NET_TRADE_AMEND:
                    TradeServiceTransactionFunctor.amendTrade(registeredFXSingleLeg, getAmendWFMsg(fxSingleLeg, registeredFXSingleLeg));
                	break;
                default:
                    break;
            }
        }

        commitTransaction(tx, notification, "FXSingleLegC");
        addToSingleLegExpirableCache(fxSingleLeg);  // even for update cases so that cache always has the latest
    }
    
    private void transformAndPersist(final Notification notification, final NettingPortfolio nettingPortfolio) throws Exception
    {
        ApplicationEventCodes appEventCode = getApplicationEventCodes(notification);
        MISPerformanceMetrics performanceMetrics = (MISPerformanceMetrics)notification.getHandlerPerformanceMetrics();
        String namespace = notification.getNamespaceName();

    	String portfolioId = nettingPortfolio.getPortfolioID();
        if (portfolioId == null)
            throw new NullPointerException("portfolioId");

        IdcSessionContext context = IdcSessionManager.getInstance().getSessionContext( nettingPortfolio.getUser());
        IdcSessionManager.getInstance().setSessionContext( context );
        IdcSessionManager.getInstance().setTransaction(null);
        IdcTransaction tx = newTransaction(nettingPortfolio.getUser(), "persistNettingPortfolio");
        SingleLegTradeTransformer.addReadOnlyClasses(tx);

        setMultiAppMessageConfig( tx, appEventCode );

        NettingPortfolio persistentPortfolio = NettingPortfolioTransformer.getPersistentPortfolioRecord(namespace, portfolioId, appEventCode, performanceMetrics);
        NettingPortfolioC registeredNettingPortfolio = (NettingPortfolioC)persistentPortfolio.getRegisteredObject();

        long startTransformTime = System.currentTimeMillis();
        NettingPortfolioTransformer.transform(registeredNettingPortfolio, nettingPortfolio, appEventCode);
        performanceMetrics.setTransformTime(System.currentTimeMillis() - startTransformTime);
        
        commitTransaction(tx, notification, "NettingPortfolioC");        
    }
    
    private void transformAndPersist(final Notification notification, final NettingTradeRequest nettingTradeRequest) throws Exception
    {
        ApplicationEventCodes appEventCode = getApplicationEventCodes(notification);
        MISPerformanceMetrics performanceMetrics = (MISPerformanceMetrics)notification.getHandlerPerformanceMetrics();
        String namespace = notification.getNamespaceName();
        nettingTradeRequest.setOwningPortfolio(nettingTradeRequest.getNettingPortfolio());
        
        String sequenceNo = nettingTradeRequest.getSequenceNumber();
        String pfId = nettingTradeRequest.getNettingPortfolio().getPortfolioID();
        if (pfId == null)
            throw new NullPointerException("portfolioId");
        if (sequenceNo == null)
            throw new NullPointerException("sequenceNumber");
                
        IdcSessionContext context = IdcSessionManager.getInstance().getSessionContext( nettingTradeRequest.getNettingPortfolio().getUser());
        IdcSessionManager.getInstance().setSessionContext( context );
        IdcSessionManager.getInstance().setTransaction(null);
        IdcTransaction tx = newTransaction(nettingTradeRequest.getNettingPortfolio().getUser(), "persistNettingTradeRequest");
        SingleLegTradeTransformer.addReadOnlyClasses(tx);

        setMultiAppMessageConfig( tx, appEventCode );
                
        NettingTradeRequestC persistentNtr = NettingTradeRequestTransformer.getPersistentNettingTradeRequestRecord(namespace, pfId, sequenceNo, appEventCode, performanceMetrics);
        NettingTradeRequestC registeredNettingTradeRequest = (NettingTradeRequestC)persistentNtr.getRegisteredObject();
        long startTransformTime = System.currentTimeMillis();
        NettingTradeRequestTransformer.transform(registeredNettingTradeRequest, nettingTradeRequest, appEventCode, performanceMetrics);
        performanceMetrics.setTransformTime(System.currentTimeMillis() - startTransformTime);
        commitTransaction(tx, notification, "NettingTradeRequestC");        
    }
    
    private void transformAndPersist(final Notification notification, final MarketSpotPrice marketSpotPrice) throws Exception
    {
        ApplicationEventCodes appEventCode = getApplicationEventCodes(notification);
        MISPerformanceMetrics performanceMetrics = (MISPerformanceMetrics)notification.getHandlerPerformanceMetrics();
        String namespace = notification.getNamespaceName();       
        IdcSessionContext context = IdcSessionManager.getInstance().getSessionContext( marketSpotPrice.getPortfolio().getUser());
        IdcSessionManager.getInstance().setSessionContext( context );
        IdcSessionManager.getInstance().setTransaction(null);
        IdcTransaction tx = newTransaction(marketSpotPrice.getPortfolio().getUser(), "persistMarketSpotPrice");
        SingleLegTradeTransformer.addReadOnlyClasses(tx);

        setMultiAppMessageConfig( tx, appEventCode );

        MarketSpotPrice registeredMarketSpotPrice = (MarketSpotPrice)marketSpotPrice.getRegisteredObject();
        registeredMarketSpotPrice.setModifiedDate(new Date(System.currentTimeMillis()));        
        long startTransformTime = System.currentTimeMillis();
        performanceMetrics.setTransformTime( System.currentTimeMillis() - startTransformTime );
        commitTransaction(tx, notification, "MarketSpotPriceC");        
    }
    
    private void transformAndPersist(Notification notification,
			Order stagingOrder) throws Exception {
    	ApplicationEventCodes appEventCode = getApplicationEventCodes(notification);
        MISPerformanceMetrics performanceMetrics = (MISPerformanceMetrics)notification.getHandlerPerformanceMetrics();
        String namespace = notification.getNamespaceName();    
        String orderId = stagingOrder.get_id();
        if (orderId == null)
            throw new NullPointerException("orderId");
        
       
        IdcSessionContext context = IdcSessionManager.getInstance().getSessionContext( stagingOrder.getUserName() + "@MAIN." + stagingOrder.getNamespaceName());
        IdcSessionManager.getInstance().setSessionContext( context );
        IdcSessionManager.getInstance().setTransaction(null);
        IdcTransaction tx = newTransaction(stagingOrder.getUserName() + "@MAIN." + stagingOrder.getNamespaceName(), "persistStagingOrder");
        StagingAreaOrderTransformer.addReadOnlyClasses(tx);

        setMultiAppMessageConfig( tx, appEventCode );
        
        StagingRequestC sReq= StagingAreaOrderTransformer.getPersistentRequestRecord(namespace, orderId, appEventCode, performanceMetrics);
        StagingRequestC registeredStagingRequest = (StagingRequestC)sReq.getRegisteredObject();
        registeredStagingRequest.setModifiedDate(new Date(System.currentTimeMillis()));   
        long startTransformTime = System.currentTimeMillis();
        StagingAreaOrderTransformer.transform(registeredStagingRequest, stagingOrder, appEventCode);
        performanceMetrics.setTransformTime( System.currentTimeMillis() - startTransformTime );
        commitTransaction(tx, notification, "StagingRequestC");   
        addToStagingAreaExpirableCache(sReq);
    }
    
    
    protected boolean isProcessingRequired(ApplicationEventCodes appEventCode, SingleLegTrade singleLegTrade) {
        switch (appEventCode) {
            case EVENT_ESP_TRADE_FAILED:
            case EVENT_ESP_LIFT_CREATE_MAKER_TRADE:
            case EVENT_DO_CREATE_MAKER_TRADE:
            case EVENT_ESP_MAKER_TRADE_PRE_RATE_VERIFIED:
            case EVENT_ESP_TRADE_PRE_RATE_VERIFIED:
            case EVENT_ESP_MAKER_TRADE_POST_RATE_VERIFIED:
                return false;
        }
        if (singleLegTrade.isMaker()) {
            return false;
        }
        return true;
    }

    private void addToSingleLegExpirableCache(FXSingleLeg fxSingleLeg){
        singleLegExpirableCache.add( fxSingleLeg );
    }
    
    private void addToStagingAreaExpirableCache(StagingRequestC stagingRequest){
        stagingAreaExpirableCache.add( stagingRequest );
    }

    private WorkflowMessage getAmendWFMsg(FXSingleLeg oldTrade , FXSingleLeg newtrade)
	{
		boolean isCptyA = true;
		LegalEntity oldLeA = CounterpartyUtilC.getLegalEntity(oldTrade.getCounterpartyA());
		LegalEntity oldLeB = CounterpartyUtilC.getLegalEntity(oldTrade.getCounterpartyB());
		LegalEntity newCptyyA = CounterpartyUtilC.getLegalEntity(newtrade.getCounterpartyA());
		WorkflowMessage trdWfMsg = MessageFactory.newWorkflowMessage();
		if ( oldLeA.getObjectId() == newCptyyA.getObjectId() )
		{
			isCptyA = false;
		}
		trdWfMsg.setParameterValue("OriginalCounterparty", isCptyA ? oldLeA : oldLeB);
		trdWfMsg.setParameterValue("isCounterpartyA", isCptyA);
		return trdWfMsg;
	}
    
    private void modifyCoverTradeForSTP(final FXSingleLeg trd, final SingleLegTrade singleLegTrade) {
        // Set NetTradeId as CoveredTradeId in Cover trade STP.
        CoveredTradeDescriptor coveredTradeDescriptor = singleLegTrade.getCoveredTrade();
        if (coveredTradeDescriptor != null && coveredTradeDescriptor.getTradeRef() != null) {
            if(singleLegTrade.getOrderRequest().getCoveredNetTradeId() != null ) {
                trd.setCoveredTradesNetTradeId(singleLegTrade.getOrderRequest().getCoveredNetTradeId());
            }
        }
    }

    public static MISRequestExpirableCache getRequestExpirableCache() {
        return requestExpirableCache;
    }

    public static MISFXSingleLegExpirableCache getSingleLegExpirableCache() {
        return singleLegExpirableCache;
    }

    public void setMultiAppMessageConfig( final IdcTransaction tx, ApplicationEventCodes appEventCode ) throws IdcNoSuchObjectException
    {            // make multi-app message sending configurable.
        if ( !ISFactory.getInstance().getISMBean().isDealingTransactionMultiAppUpdateEnabled() )
        {
            final String eventCode = String.valueOf( appEventCode.getCode() );
            final String multiAppProp = ( String ) tx.getUOW().getProperty( ClusterCommitEventAdapterC.MULTI_APP );
            if ( multiAppProp == null || ClusterCommitEventAdapterC.MULTI_APP_DISABLED.equals( multiAppProp ) ) // if an event handler already marked it as enabled then, don't disable it.
            {
                final boolean eventEnabledForMultiApp = ISFactory.getInstance().getISMBean().isDealingTransactionMultiAppUpdateEnabled( eventCode );
                tx.getUOW().setProperty( ClusterCommitEventAdapterC.MULTI_APP, eventEnabledForMultiApp ? ClusterCommitEventAdapterC.MULTI_APP_ENABLED : ClusterCommitEventAdapterC.MULTI_APP_DISABLED );
                LOG.info( new StringBuilder( 200 ).append( "MNH.setMultiAppMessageConfig : set multi-app message flag for tx=" ).append( tx )
                        .append( ",eventCode=" ).append( eventCode ).append( ",multiAppFlag=" ).append( multiAppProp )
                        .append( ",eventEnabledForMultiApp=" ).append( eventEnabledForMultiApp ).toString() );
            }
            else
            {
                LOG.info( new StringBuilder( 200 ).append( "MNH.setMultiAppMessageConfig : multi-app flag is already set for tx=" ).append( tx )
                        .append( ",eventCode=" ).append( eventCode ).append( ",multiAppFlag=" ).append( multiAppProp ).toString() );
            }
        }
    }

    protected IdcTransaction newTransaction(User user,String eventName){
        return IdcSessionManager.getInstance().newTransaction(user, eventName);
    }

    protected IdcTransaction newTransaction(String user,String eventName) throws IdcNoSuchObjectException {
        return IdcSessionManager.getInstance().newTransaction(user, eventName);
    }
}
