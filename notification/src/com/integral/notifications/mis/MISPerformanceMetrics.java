package com.integral.notifications.mis;

import com.integral.spaces.notification.Notification;
import com.integral.spaces.notification.NotificationHandlerPerformanceMetrics;

/**
 * BRIEF and only the needed metrics for the MIS notification handling.
 *
 * <AUTHOR>
 */
public class MISPerformanceMetrics implements NotificationHandlerPerformanceMetrics {
    private StringBuilder header = new StringBuilder(64);
    private long targetDbQueryTime;
    private long transformTime;
    private long targetDbCommitTime;
    private long totalTime;

    public MISPerformanceMetrics() {
    }

    // be mindful of added performance cost when you are adding more info
    private void setHeader(Notification notification) {
        if (notification == null)
            return;

        header.append(" oId=").append(notification.getObjectId());
        header.append(" cId=").append(notification.getCorrelationId());
        header.append(" s=").append(notification.getSpace());
        header.append(" nssh=").append(notification.getNamespaceName());
        header.append(" ev=").append(notification.getEvent());
    }

    public void setTargetDbQueryTime(long milliSeconds) {
        targetDbQueryTime = milliSeconds;
    }

    public void setTransformTime(long milliSeconds) {
        transformTime = milliSeconds;
    }

    public void setTargetDbCommitTime(long milliSeconds) {
        targetDbCommitTime = milliSeconds;
    }

    @Override
    public long getTotalTime() {
        return totalTime;
    }

    @Override
    public void setTotalTime(long milliSeconds) {
        this.totalTime = milliSeconds;
    }

    @Override
    public String getMetrics(Notification notification) {
        setHeader(notification);
        return getMetrics();
    }

    @Override
    public String getMetrics() {
        StringBuilder sb = new StringBuilder(64);
        sb.append("MIS:").append(header);
        sb.append(" total=").append(totalTime);
        sb.append(" qry=").append(targetDbQueryTime);
        sb.append(" xform=").append(transformTime);
        sb.append(" cmt=").append(targetDbCommitTime);
        return sb.toString();
    }
}
