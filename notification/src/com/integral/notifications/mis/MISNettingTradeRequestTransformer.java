package com.integral.notifications.mis;

import com.integral.is.common.ApplicationEventCodes;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.netting.model.NettingPortfolio;
import com.integral.netting.model.NettingPortfolioC;
import com.integral.netting.model.NettingTradeRequest;
import com.integral.netting.model.NettingTradeRequestC;
import com.integral.notifications.cache.NotificationCacheFactory;
import com.integral.persistence.PersistenceException;

public class MISNettingTradeRequestTransformer {

    private final static Log LOG = LogFactory.getLog(MISNettingTradeRequestTransformer.class);
    private final MISNettingTradeRequestExpirableCache nettingTradeRequestExpirableCache;
    
    public MISNettingTradeRequestTransformer(MISNettingTradeRequestExpirableCache nettingTradeRequestExpirableCache) {
        this.nettingTradeRequestExpirableCache = nettingTradeRequestExpirableCache;
    }


	public NettingTradeRequestC getPersistentNettingTradeRequestRecord(String namespace, String portfolioId, String sequenceNo,
			ApplicationEventCodes appEventCode, MISPerformanceMetrics performanceMetrics) throws PersistenceException
	{
        if (isUpdateEvent(appEventCode)) {
            NettingTradeRequestC ntr = queryPortfolio(namespace, portfolioId, sequenceNo, performanceMetrics);
            if (ntr != null)
                return ntr;

            LOG.error("No prior record in database NTRId=" + portfolioId + " update appEventCode=" + appEventCode.toString());
            throw new PersistenceException();
        }
        return new NettingTradeRequestC();      // ThreadLocal is overkill as these are cached
	}
	
	
    public NettingTradeRequestC queryPortfolio(String namespace, String portfolioId, String sequenceNo, MISPerformanceMetrics performanceMetrics) throws PersistenceException {
        return nettingTradeRequestExpirableCache.get(namespace, portfolioId, sequenceNo, performanceMetrics);
    }
    
    public static boolean isUpdateEvent(final ApplicationEventCodes appEventCode) 
    {
    	switch(appEventCode)
    	{
	    	case EVENT_NETTING_NTR_UPDATE:
	    		return true;
	        default:
	        	return false;
        }
    }
    
    
	public void transform(NettingTradeRequestC persistentNTR, NettingTradeRequest otherNTR, ApplicationEventCodes appEventCode, MISPerformanceMetrics performanceMetrics) throws PersistenceException 
	{
		StringBuilder sb = new StringBuilder(100).append(otherNTR.getClass().getName()).append(" ==> ").
				append(persistentNTR.getClass().getName() ).append(" pfId=").append(otherNTR.getNettingPortfolio().getPortfolioID())
				.append(" sqNo=").append(otherNTR.getSequenceNumber()).append(" appEventCode=").append(appEventCode.toString());	
        LOG.info(sb.toString()); 
	 
        final boolean update = isUpdateEvent(appEventCode);
        if (!update) {
            // start from a clean slate.
        	persistentNTR.resetTransients();
        	persistentNTR.resetFacadeTransients();
        	persistentNTR.resetTransientProperties();
        	persistentNTR.resetStickyProperties();

        	persistentNTR.set_id(otherNTR.get_id());
        }    
        persistentNTR.update(otherNTR);      
        MISNettingPortfolioExpirableCache portfolioCache = (MISNettingPortfolioExpirableCache)NotificationCacheFactory.getNotificationCache(NettingPortfolioC.class);
        NettingPortfolio portfolio = portfolioCache.get(otherNTR.getNamespaceName(), otherNTR.getNettingPortfolio().getPortfolioID(), performanceMetrics);
        if(portfolio!=null){
        	NettingPortfolio registeredPortfolio = (NettingPortfolio)portfolio.getRegisteredObject();
        	persistentNTR.setNettingPortfolio(registeredPortfolio);
        	persistentNTR.setOwningPortfolio(registeredPortfolio);
        }
	}
}
