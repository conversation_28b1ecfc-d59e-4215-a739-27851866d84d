package com.integral.notifications.mis;

import java.util.Vector;

import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.netting.model.NettingPortfolioC;
import com.integral.notifications.cache.NotificationCache;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import com.integral.query.QueryCriteria;
import com.integral.query.QueryCriteriaBuilder;
import com.integral.query.QueryCriteriaBuilderC;

public class MISNettingPortfolioExpirableCache extends NotificationCache<NettingPortfolioC> {
	
    private final static Log LOG = LogFactory.getLog(MISNettingPortfolioExpirableCache.class);

    private boolean cacheDisabled = false;

    public MISNettingPortfolioExpirableCache(int cacheSize,long expirationTimeInMS,int concurrencyLevel,boolean cacheEnabled) {
        super(cacheSize,expirationTimeInMS,concurrencyLevel);
        this.cacheDisabled = !cacheEnabled;
    }

    /**
     * Returns the key to the cache based on the given namespace and id.
     * NB. The id is guaranteed to be unique within the namespace only.
     *
     * @param namespace of the MIS notification object
     * @param id of the object
     * @return key to the cache
     */
    private String getKey(final String namespace, final String id) {
        return namespace + "." + id;
    }

    /**
     * @param req to be cached
     * @return key to the cache
     */
    private String getKey(NettingPortfolioC nettingPortfolio) {
        return getKey(nettingPortfolio.getNamespaceName(), nettingPortfolio.getPortfolioID());
    }

    /**
     * Query the database to find the Request object.
     *
     * @param orderId of the Request object
     * @param performanceMetrics null or MIS performance metrics
     * @return found Request object on success or null upon failure
     * @throws PersistenceException
     */
    private NettingPortfolioC query(final String pfId, MISPerformanceMetrics performanceMetrics) throws PersistenceException {
        QueryCriteriaBuilder builder = new QueryCriteriaBuilderC();
        QueryCriteria criteria = builder.get("");
        Expression exp = new ExpressionBuilder();
        Expression tranExp = exp.get("portfolioID").equal(pfId);
        criteria.setExpression(tranExp);
        ReadAllQuery query = new ReadAllQuery(NettingPortfolioC.class, criteria.getExpression());

        long startQueryTime = (performanceMetrics != null) ? System.currentTimeMillis() : 0L;
        Vector allObjects = (Vector)PersistenceFactory.newSession().executeQuery(query);
        if (performanceMetrics != null) {
            performanceMetrics.setTargetDbQueryTime(System.currentTimeMillis() - startQueryTime);
        }

        if ((allObjects != null) && (allObjects.size() > 0)) {
            NettingPortfolioC req = (NettingPortfolioC)allObjects.get(0);
            add(req);
            return req;
        }

        return null;
    }

    /**
     * Adds the object to the cache by associating the object in the cache with its derived key.
     * If the key already exists in the cache the new object will replace the old one.
     *
     * @param portfolio object to be associated in the cache
     */
    public void add(NettingPortfolioC portfolio) {
        if (cacheDisabled)
            return;

        String key = getKey(portfolio);
        add(key, portfolio);
        LOG.info("Associated NettingPortfolioC object with key " + key);
    }

    /**
     * Find the NettingPortfolio object from cache. Only if not present in cache, query the database.
     *
     * @param namespace of the NettingPortfolio object
     * @param portfolioId of the NettingPortfolio object
     * @param performanceMetrics null or MIS performance metrics
     * @return found NettingPortfolio object on success or null upon failure
     * @throws PersistenceException
     */
    public NettingPortfolioC get(final String namespace, final String portfolioId, MISPerformanceMetrics performanceMetrics) throws PersistenceException {
        if (cacheDisabled)
            return query(portfolioId, performanceMetrics);

        String key = getKey(namespace, portfolioId);
        NettingPortfolioC portfolio = (NettingPortfolioC)get(key);
        if (portfolio == null) {
            LOG.info("Cache miss, key=" + key + " - querying database");
            return query(portfolioId, performanceMetrics);      // also adds to cache
        }

        if (performanceMetrics != null) {
            performanceMetrics.setTargetDbQueryTime(0L);
        }

        LOG.info("Cache hit, key=" + key);
        return portfolio;
    }

    @Override
    public Class getType() {
        return NettingPortfolioC.class;
    }

}
