package com.integral.notifications.mis;

/**
 * <AUTHOR>
 */
public class MISNotificationConstants {
    public final static String MIS_NOTIFICATION_PROPERTY = "MIS.Notifications.";
    public final static String CACHE_DISABLED = MIS_NOTIFICATION_PROPERTY + "cacheDisabled";
    public final static String CACHE_NUMBER_OF_CONTAINERS = MIS_NOTIFICATION_PROPERTY + "cacheNumOfContainers";
    public final static String CACHE_ROTATION_INTERVAL_IN_MILLISECS = MIS_NOTIFICATION_PROPERTY + "cacheRotationIntervalInMilliSeconds";
    public final static String CACHE_MOVE_TO_FIRST_CONTAINER_ON_LOOKUP = MIS_NOTIFICATION_PROPERTY + "cacheMoveToFirstContainerOnLookup";
    public final static String OPTIMISTIC_LOCK_MAX_RETRY = MIS_NOTIFICATION_PROPERTY + "optimisticLockMaxRetry";
    public final static String COMMIT_TIME_MAX_MILLISEC = MIS_NOTIFICATION_PROPERTY + "commitTimeMaxMilliSeconds";
}
