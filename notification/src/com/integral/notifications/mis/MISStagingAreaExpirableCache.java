package com.integral.notifications.mis;

import java.util.Vector;

import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReadAllQuery;

import com.integral.finance.dealing.RequestC;
import com.integral.finance.dealing.StagingRequestC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.notifications.cache.NotificationCache;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import com.integral.query.QueryCriteria;
import com.integral.query.QueryCriteriaBuilder;
import com.integral.query.QueryCriteriaBuilderC;
import com.integral.staging.Order;

/**
 * This class is used to keep the Order objects which are currently used. Least used Order objects
 * are removed from the cache periodically.
 *
 * <AUTHOR>
 */
public class MISStagingAreaExpirableCache extends NotificationCache<StagingRequestC>{
	
	 private final static Log LOG = LogFactory.getLog(MISStagingAreaExpirableCache.class);

	 private boolean cacheDisabled = false;
	 
	 
	 public MISStagingAreaExpirableCache(int cacheSize,long expirationTimeInMS,int concurrencyLevel,boolean cacheEnabled) {
        super(cacheSize,expirationTimeInMS,concurrencyLevel);
        this.cacheDisabled = !cacheEnabled;
	 }
	 
	/**
     * Returns the key to the cache based on the given namespace and id.
     * NB. The id is guaranteed to be unique within the namespace only.
     *
     * @param namespace of the MIS notification object
     * @param id of the object
     * @return key to the cache
     */
    private String getKey(final String namespace, final String id) {
        return namespace + "." + id;
    }
    
    /**
     * @param sReq to be cached
     * @return key to the cache
     */
    private String getKey(StagingRequestC sReq) {
        return getKey(sReq.getNamespace().getShortName(), sReq.getOrdId());
    }
    
    /**
     * Query the database to find the StagingRequest object.
     *
     * @param orderId of the StagingRequest object
     * @param performanceMetrics null or MIS performance metrics
     * @return found StagingRequest object on success or null upon failure
     * @throws PersistenceException
     */
    private StagingRequestC query(final String orderId, MISPerformanceMetrics performanceMetrics) throws PersistenceException {
        QueryCriteriaBuilder builder = new QueryCriteriaBuilderC();
        QueryCriteria criteria = builder.get("");
        Expression exp = new ExpressionBuilder();
        Expression tranExp = exp.get("ordId").equal(orderId);
        criteria.setExpression(tranExp);
        ReadAllQuery query = new ReadAllQuery(StagingRequestC.class, criteria.getExpression());

        long startQueryTime = (performanceMetrics != null) ? System.currentTimeMillis() : 0L;
        Vector allObjects = (Vector)PersistenceFactory.newSession().executeQuery(query);
        if (performanceMetrics != null) {
            performanceMetrics.setTargetDbQueryTime(System.currentTimeMillis() - startQueryTime);
        }

        if ((allObjects != null) && (allObjects.size() > 0)) {
            StagingRequestC sReq = (StagingRequestC)allObjects.get(0);
            add(sReq);
            return sReq;
        }

        return null;
    }
    
    
    /**
     * Adds the object to the cache by associating the object in the cache with its derived key.
     * If the key already exists in the cache the new object will replace the old one.
     *
     * @param sReq object to be associated in the cache
     */
    public void add(StagingRequestC sReq) {
        if (cacheDisabled)
            return;

        String key = getKey(sReq);
        add(key, sReq);
        LOG.info("Associated StagingRequestC object with key " + key);
    }
    
    
    /**
     * Find the StagingRequest object from cache. Only if not present in cache, query the database.
     *
     * @param namespace of the StagingRequest object
     * @param orderId of the StagingRequest object
     * @param performanceMetrics null or MIS performance metrics
     * @return found StagingRequest object on success or null upon failure
     * @throws PersistenceException
     */
    public StagingRequestC get(final String namespace, final String orderId, MISPerformanceMetrics performanceMetrics) throws PersistenceException {
        if (cacheDisabled)
            return query(orderId, performanceMetrics);

        String key = getKey(namespace, orderId);
        StagingRequestC sReq = (StagingRequestC)get(key);
        if (sReq == null) {
            LOG.info("Cache miss, key=" + key + " - querying database");
            return query(orderId, performanceMetrics);      // also adds to cache
        }

        if (performanceMetrics != null) {
            performanceMetrics.setTargetDbQueryTime(0L);
        }

        LOG.info("Cache hit, key=" + key);
        return sReq;
    }
    
    @Override
    public Class getType() {
        return StagingRequestC.class;
    }

}
