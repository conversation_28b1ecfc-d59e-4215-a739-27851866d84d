package com.integral.notifications.authtoken;

import com.integral.cas.AuthenticationToken;
import com.integral.cas.CASClient;
import com.integral.cas.config.CASMBean;
import com.integral.cas.config.CASMBeanC;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.spaces.fx.persistence.ISSpacesPersistenceService;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.notifications.NotificationConfiguration;
import com.integral.persistence.spaces.PersistenceConstants;
import com.integral.spaces.Metaspace;
import com.integral.spaces.QueryBuilder;
import com.integral.spaces.notification.NHResponse;
import com.integral.spaces.notification.NHResponseCode;
import com.integral.spaces.notification.Notification;
import com.integral.spaces.notification.NotificationHandler;
import com.integral.spaces.spi.CriteriaSet;

/**
 * Created with IntelliJ IDEA.
 * User: tapan.vijay
 * Date: 15/7/13
 * Time: 7:10 PM
 * To change this template use File | Settings | File Templates.
 */
public class AuthTokenNotificationHandler implements NotificationHandler
{
    private static final Log log = LogFactory.getLog(AuthTokenNotificationHandler.class);
    private final ISSpacesPersistenceService persistenceService = PersistenceServiceFactory.getUserSessionPersistenceService();
    private final CASMBean authTokenConfig;

    public AuthTokenNotificationHandler()
    {
        authTokenConfig = CASMBeanC.getInstance();
    }


    @Override
    public long getLongIdentifier() {
        return NotificationConfiguration.NOTIFICATION_HANDLER_AUTHTOKEN.getLongIdentifier();
    }

    @Override
    public NHResponse handleRedelivery(Notification notification,String errorDesc) {
        return handle(notification);
    }

    public NHResponse handle(Notification notification)
    {
        if (authTokenConfig.isCasClientSSOEnabled()) {
            try {
                ApplicationEventCodes aec = ApplicationEventCodes.fromCode(notification.getAppEventCode());
                if (aec == ApplicationEventCodes.EVENT_AUTHENTICATION_TOKEN) {
                    String authToken = notification.getObjectId();
                    if (log.isDebugEnabled()) {
                        log.debug("handle: authToken from notification="+authToken);
                    }
                    AuthenticationToken authenticationToken = getAuthenticationFromSpaces(authToken);
                    if (log.isDebugEnabled()) {
                        if (authenticationToken == null) {
                            log.debug("handle: authenticationToken is null");
                        } else {
                            log.debug("handle: authenticationToken from spaces="+authenticationToken);
                            log.debug("handle: authenticationToken isJWtToken from spaces="+authenticationToken.isJwtToken());
                            log.debug("handle: authenticationToken tokenType from spaces="+authenticationToken.getTokenType());
                        }
                    }

                    if (authenticationToken != null) {
                        switch (authenticationToken.getTokenType()) {
                            case ORIGIN_ID:
                                if (log.isDebugEnabled()) {
                                    log.debug("handle: tokenValue="+authenticationToken.getTokenValue());
                                }
                                CASClient.getInstance().invalidateOriginIds(authenticationToken.getTokenValue(), false);
                                break;
                            case JWT_TOKEN:
                                if (log.isDebugEnabled()) {
                                    log.debug("handle: tokenValue="+authenticationToken.getTokenValue());
                                }
                                CASClient.getInstance().invalidateJwtToken(authenticationToken.getTokenValue());
                                break;
                            default:
                                CASClient.getInstance().invalidateToken(authToken);
                        }
                    }
                }
            }
            catch (Exception nse) {
                log.error("Error handling notification:" + notification, nse);
                return new NHResponse(NHResponseCode.FAILURE);
            }
        }
        return new NHResponse(NHResponseCode.SUCCESS);
    }


    private AuthenticationToken getAuthenticationFromSpaces(String token){
        Metaspace metaspace =  persistenceService.getMetaspace();
        if (metaspace == null || metaspace.isStopped()) {
            log.warn("either meta space is null or stopped");
            return null;
        }
        //namespace is user's organization
        QueryBuilder<AuthenticationToken> query = metaspace.createNewQueryBuilder("", PersistenceConstants.AUTHTOKEN);
        CriteriaSet criteriaSet = metaspace.defaultCriteriaSet();
        //Verify that given token is not present in db. If exists in db, then it is invalid token.
        query.add(criteriaSet.is("_id", token));
        AuthenticationToken tokenInDB = query.build().getSingleResult(AuthenticationToken.class);
        return tokenInDB;
    }
}
