package com.integral.notifications.stp;

import com.integral.finance.dealing.fx.Allocation;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXSingleLegC;
import com.integral.finance.trade.AllocationList;
import com.integral.finance.trade.TradeService;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.finance.trade.configuration.TradeServiceConstants;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.is.spaces.fx.persistence.DealingModelTradeHelper;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.*;
import com.integral.notifications.NotificationConfiguration;
import com.integral.notifications.mis.*;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.query.spaces.fx.esp.query.SingleLegTradeQueryService;
import com.integral.session.IdcSessionManager;
import com.integral.session.IdcTransaction;
import com.integral.spaces.SpaceIterator;
import com.integral.spaces.notification.NHResponse;
import com.integral.spaces.notification.NHResponseCode;
import com.integral.spaces.notification.Notification;
import com.integral.spaces.notification.NotificationHandler;
import com.integral.time.DateTimeFactory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;

/**
 * Created by katariya on 5/13/14.
 */
public class FinXMLNotificationHandler implements NotificationHandler
{
    private final static Log log = LogFactory.getLog(FinXMLNotificationHandler.class);
    private MISSingleLegTradeTransformer SingleLegTradeTransformer;
    private TradeConfigurationMBean tradeConfig = TradeConfigurationFactory.getTradeConfigurationMBean();

    /**
     * Initialize MIS notification handling operations.
     * Must be called before the handler is used.
     */
    public void initialize()
    {
        // TODO Passing null cache instance..requires refactoring of MISSingleLegTransformer
        SingleLegTradeTransformer = new MISSingleLegTradeTransformer(null, null);
    }

    @Override
    public NHResponse handle(Notification notification)
    {
        try
        {
            if (SingleLegTrade.class.isAssignableFrom(notification.getDataObjectType()))
            {
                log.info("Processing notification " + notification);
                ApplicationEventCodes appEventCode = ApplicationEventCodes.fromCode(notification.getAppEventCode());
                WorkflowMessage workflowMessage = MessageFactory.newWorkflowMessage();
                switch ( appEventCode )
                {
                    case EVENT_ESP_NET_TRADE_AMEND:
                    case EVENT_ESP_NET_TRADE_CANCEL:
                        workflowMessage.getMap().put("NetTrade",true);
                    case EVENT_ESP_TRADE_CREATED_VERIFIED:
                    case EVENT_ESP_TRADE_VERIFIED:
                    case EVENT_ESP_TRADE_POST_RATE_VERIFIED:
                    case EVENT_ESP_TRADE_NET:
                    case EVENT_ESP_TRADE_CANCEL:
                    case EVENT_ESP_TRADE_AMEND:
                    case EVENT_ESP_TRADE_ALLOCATION_COMPLETED:
                        performSTP( workflowMessage, notification, appEventCode );
                        break;
                    default:
                        break;
                }
            }
        }
        catch (Exception e)
        {
            log.error("Exception while processing notification " + notification, e);
            IdcSessionManager.getInstance().setTransaction(null);
            return new NHResponse(NHResponseCode.FAILURE);
        }
        return new NHResponse(NHResponseCode.SUCCESS);
    }

    @Override
    public long getLongIdentifier() {
        return NotificationConfiguration.NOTIFICATION_HANDLER_STP.getLongIdentifier();
    }

    @Override
    public NHResponse handleRedelivery(Notification notification, String errorDescription) {
        // Reattempt sending STP.
        return handle( notification );
    }

    private void populateWFParams(WorkflowMessage workflowMessage ,ApplicationEventCodes appEventCode ,final SingleLegTrade singleLegTrade)
    {
        if ( ApplicationEventCodes.EVENT_ESP_TRADE_AMEND == appEventCode || ApplicationEventCodes.EVENT_ESP_NET_TRADE_AMEND == appEventCode )
        {
            workflowMessage.getMap().put(TradeService.CPTY_A_AMENDED, singleLegTrade.isTakerAmended());
            if(singleLegTrade.getOldLegalEntity()!=null)
            {
            	workflowMessage.getMap().put(TradeService.ORIG_CPTY, singleLegTrade.getOldLegalEntity());
            	workflowMessage.getMap().put( TradeService.CPTY_A, singleLegTrade.getOldLegalEntity().getShortName() );
            }
        }
        if(singleLegTrade.getMatchEvent().isDirectedOrder()){
            workflowMessage.getMap().put( TradeServiceConstants.DIRECTED_ORDER, true );
        }else{
            workflowMessage.getMap().put( TradeServiceConstants.DIRECTED_ORDER, false );
        }
    }
    /**
     * Perform STP by transforming spaces trade to oracle trade
     * @param workflowMessage workflow message
     * @param notification notification
     * @param appEventCode event code
     * @throws Exception exception
     */
    public void performSTP( WorkflowMessage workflowMessage, Notification notification, ApplicationEventCodes appEventCode ) throws Exception
    {
        SingleLegTrade singleLegTrade = (SingleLegTrade)notification.getCompleteObject();
        if(singleLegTrade.getMatchEvent().isNettingRequired()){
            return;
        }

        final boolean allocationCompletedEvent = ApplicationEventCodes.EVENT_ESP_TRADE_ALLOCATION_COMPLETED == appEventCode;
        OrderRequest orderRequest = singleLegTrade.getOrderRequest();
        if ( orderRequest != null && orderRequest.isPreTradeAllocation() && !allocationCompletedEvent && singleLegTrade.isAllocationNetTrade () )
        {
            log.info("FinXMLNotificationHandler.performSTP - skipping block net trade STP for trade id= " + singleLegTrade.get_id() + ",eventCode=" + appEventCode );
            return;
        }

        IdcTransaction tx = IdcSessionManager.getInstance().newFakeTransaction(singleLegTrade.getUser(), "tradeFakeTransaction");
        String namespace = notification.getNamespaceName();
        FXSingleLegC singleLeg = (FXSingleLegC) FXFactory.newFXSingleLeg();
        SingleLegTradeTransformer.transform(namespace, singleLeg, singleLegTrade, appEventCode, false);
        populateWFParams(workflowMessage, appEventCode,singleLegTrade);
        // block trade workflow - set the allocation details in the trade for generating STP message with all allocated trade details.
        if ( allocationCompletedEvent )
        {
            updateAllocationList( singleLegTrade, singleLeg );
            workflowMessage.setParameterValue(TradeService.ALLOCATION_COMPLETED, true);
        }

        FinXMLHandler handler = new FinXMLHandler( singleLeg );
        handler.handle(workflowMessage);
        tx.commit();
    }

    private void updateAllocationList(SingleLegTrade singleLegTrade, FXSingleLegC singleLeg) {
        OrderRequest orderRequest = singleLegTrade.getOrderRequest();
        log.info("FinXMLNotificationHandler.updateAllocationList::orderRequest.isPreTradeAllocation()= " + orderRequest.isPreTradeAllocation() + " ,singleLegTrade.isAllocationNetTrade()=" + singleLegTrade.isAllocationNetTrade() + " for trade id= " + singleLegTrade.get_id());
        if (orderRequest.isPreTradeAllocation() && singleLegTrade.isAllocationNetTrade()) {
            MatchEvent matchEvent = singleLegTrade.getMatchEvent();
            Collection<SingleLegTrade> allocationTrades = new ArrayList();
            SpacesQueryService.QueryResult<SpaceIterator<SingleLegTrade>> tradeQueryResult = SingleLegTradeQueryService.getTradesIteratorForOrderMatch(matchEvent.getNamespaceName(), matchEvent.get_id(), true);
            switch (tradeQueryResult.getStatus()) {
                case FAILURE: {
                    log.error("FinXMLNotificationHandler.updateAllocationList - failed to get the trades for match event=" + matchEvent.get_id() + ",orderId=" + orderRequest.get_id());
                    break;
                }
                case SUCCESS: {
                    SpaceIterator<SingleLegTrade> tradeIterator = tradeQueryResult.getResult();
                    if (tradeIterator != null) {
                        while (tradeIterator.hasNext()) {
                            SingleLegTrade trade = tradeIterator.next();
                            DealingModelTradeHelper.updateWithTransients(trade);
                            DealingModelRef<? extends Trade> netTradeRef = trade.getAllocationNetTradeRef();
                            if (netTradeRef != null) {
                                log.info("FinXMLNotificationHandler.updateAllocationList - net trade ref for trade=" + trade.get_id() + ",netTradeId=" + netTradeRef.getObject().get_id() + ",singleLegTrade.get_id()=" + singleLegTrade.get_id());
                                if (singleLegTrade.get_id().equals(netTradeRef.getObject().get_id())) {
                                    allocationTrades.add(trade);
                                }
                            } else if(!trade.get_id().equals(singleLegTrade.get_id())) {
                                log.warn("FNH.updateAllocationList - no net trade ref found for trade=" + trade.get_id() + ",netTradeId=" + singleLegTrade.get_id() + ",netTradeRef=" + netTradeRef );
                            }
                        }
                    }
                }
            }
            if (!allocationTrades.isEmpty()) {
                if (tradeConfig.isAllocationRequiredInSTPForTraiana(singleLegTrade.getOrderRequest().getOrganization().getShortName())) {
                    Collection<AllocationList.Allocation> allocations = new ArrayList(allocationTrades.size());
                    for (SingleLegTrade allocationTrade : allocationTrades) {
                        AllocationList.Allocation allocation = convertToAllocation(allocationTrade);
                        allocations.add(allocation);
                        log.info("FinXMLNotificationHandler.updateAllocationList:: added to allocationList for netTradeId= " + singleLegTrade.get_id() + " allocation Trade id=" + allocationTrade.get_id() );
                    }
                    AllocationList allocationList = new AllocationList(allocations);
                    singleLeg.setAllocationList(allocationList);
                    log.info("FinXMLNotificationHandler.updateAllocationList:: allocationList has been set to trade object for netTradeId= " + singleLegTrade.get_id() + " with size="+ allocations.size() );
                }
            }
        }
    }


    /**
     * This function will create an ALlocation Object for each Allocation Trade
     *
     * @param trade
     * @return
     */
    private AllocationList.Allocation convertToAllocation(SingleLegTrade trade) {
        TradeLeg tradeLeg = trade.getTradeLeg();
        AllocationList.Allocation allocation = new AllocationList.Allocation();
        allocation.setTid(trade.get_id());
        if(trade.getLegalEntity()!=null)
            allocation.setFundId(trade.getLegalEntity().getShortName());
        allocation.setPortfolioId(trade.getPortfolioId());
        allocation.setCptyA(trade.getLegalEntity().getShortName());
        allocation.setCptyB(DealingModelUtil.getCounterPartyLegalEntity(trade).getShortName());
        allocation.setTradeDate(DateTimeFactory.newDate(new Date(trade.getTradeDate())));
        allocation.setValueDate(DateTimeFactory.newDate(new Date(tradeLeg.getValueDate())));
        allocation.setCurrency1(trade.getBaseCurrency());
        allocation.setCurrency2(trade.getTermCurrency());
        if (trade.getDealtCurrency().equals(trade.getBaseCurrency())) {
            allocation.setDealtCurrency1(true);
            allocation.setCurrency1Amount(trade.getDealtAmount());
            allocation.setCurrency2Amount(trade.getSettledAmount());
        } else {
            allocation.setDealtCurrency1(false);
            allocation.setCurrency1Amount(trade.getSettledAmount());
            allocation.setCurrency2Amount(trade.getDealtAmount());
        }
        boolean isSpot = ISUtilImpl.TRD_SPOT_CLSF.getShortName().equals(trade.getClassification());
        double spotRate = tradeLeg.getSpotRate();
        double forwardPoints = tradeLeg.getForwardPoints();
        allocation.setSpotRate(spotRate);
        allocation.setForwardPoints(forwardPoints);
        if (!isSpot) {
            allocation.setRate(tradeLeg.getRate());
        } else {
            allocation.setRate(spotRate);
        }
        return allocation;
    }
}
