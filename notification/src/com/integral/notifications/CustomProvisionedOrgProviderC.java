package com.integral.notifications;

import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.is.warmuptrade.ProvisionedOrgProvider;
import com.integral.is.warmuptrade.WarmUpTradeUtilC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.ReferenceEntity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.rds.client.ClientFactory;
import com.integral.rds.client.QueryIterator;
import com.integral.rds.client.ReferenceDataService;
import com.integral.rds.message.QueryBuilder;
import com.integral.scheduler.ScheduleEventC;
import com.integral.trading.VSTradingMetrics;
import com.integral.user.Organization;
import com.integral.user.OrganizationRelationship;

import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * Created by raghunathans on 12/13/16.
 */
public class CustomProvisionedOrgProviderC implements ProvisionedOrgProvider {

    public static final String NSSH = "nssh";
    private static Log log = LogFactory.getLog(CustomProvisionedOrgProviderC.class);

    public static final String MAIN = "MAIN";

    public static final String FXI = "FXI";

    private static final long SIXTEEN_DAYS_IN_MILLISECONDS = 16 * 24 * 60 * 60 * 1000l;

    @Override
    public void updateProvisionedOrgs() {
        ReferenceDataCacheC referenceDataCacheC = ReferenceDataCacheC.getInstance();
        try {
            if (null == referenceDataCacheC.getProvisionedOrgs()) {
                Set<Organization> orgs = new HashSet<Organization>();
                //Get reference data
                long startTime = System.currentTimeMillis();
                Set<String> orgNames = getOrganizationsForWarmup();
                if (!orgNames.isEmpty()) {
                    if(log.isDebugEnabled()){
                        log.debug("Warming up the trading organization(s):"+orgNames);
                    }
                    Set<Long> orgNameSpaceIds = new HashSet<Long>();
                    WarmUpTradeUtilC warmUpTradeUtilC = WarmUpTradeUtilC.getInstance();
                    //Warm up for the reference data
                    log.info("CustomProvisionedOrgProviderC.update:Warming up the trading organization(s) reference data, Number of organizations:" + orgNames.size());
                    for (String orgName : orgNames) {
                        Organization organization = referenceDataCacheC.getOrganization(orgName);
                        if ( null != organization )
                        {
                            if ( !organization.isActive () )
                            {
                                continue;
                            }
                            Collection<Organization> counterPartyOrgs = organization.getRelatedActiveOrganizations (CounterpartyUtilC.FI_LP_RELATIONSHIP);
                            for (Organization counterParty : counterPartyOrgs) {
                                if (counterParty.isMasked()) {
                                    if (orgNameSpaceIds.add(counterParty.getRealLP().getNamespace().getObjectID()))
                                    {
                                        if ( counterParty.getRealLP () != null && counterParty.getRealLP ().isActive () )
                                        {
                                            orgs.add ( counterParty.getRealLP () );
                                            warmUpTradeUtilC.performOrganizationWarmup ( counterParty.getRealLP () );
                                        }
                                    }
                                } else {
                                    if (orgNameSpaceIds.add(counterParty.getNamespace().getObjectID())) {
                                        orgs.add(counterParty);
                                        warmUpTradeUtilC.performOrganizationWarmup(counterParty);
                                    }
                                    if(counterParty.isBroker()){
                                        Collection<Organization> lgOrgs = counterParty.getRelatedActiveOrganizations (OrganizationRelationship.B_LG_RELATIONSHIP);
                                        for (Organization lg : lgOrgs) {
                                            if (orgNameSpaceIds.add(lg.getNamespace().getObjectID())) {
                                                orgs.add(lg);
                                                warmUpTradeUtilC.performOrganizationWarmup(lg);
                                            }
                                        }
                                    }
                                }
                            }
                            if(organization.isBroker()){
                                Collection<Organization> lgOrgs = organization.getRelatedActiveOrganizations (OrganizationRelationship.B_LG_RELATIONSHIP);
                                for (Organization lg : lgOrgs) {
                                    if (orgNameSpaceIds.add(lg.getNamespace().getObjectID())) {
                                        orgs.add(lg);
                                        warmUpTradeUtilC.performOrganizationWarmup(lg);
                                    }
                                }
                            }
                            orgs.add(organization);
                            orgNameSpaceIds.add(organization.getNamespace().getObjectID());
                            warmUpTradeUtilC.performOrganizationWarmup(organization);
                        } else {
                            log.info("Failed to get organization from reference data cache:" + orgName);
                        }
                    }
                    log.info("CustomProvisionedOrgProviderC.update:Warmed up trading organization(s),no of organizations:" + orgs.size()+",time taken:"+(System.currentTimeMillis()-startTime));

                    startTime = System.currentTimeMillis();
                    String[] defaultOrgs = new String[]{FXI, MAIN};
                    Organization fxiOrganization;
                    for (String defaultOrg : defaultOrgs) {
                        fxiOrganization = referenceDataCacheC.getOrganization(defaultOrg);
                        if (null != fxiOrganization && orgNameSpaceIds.add(fxiOrganization.getNamespace().getObjectID())) {
                            orgs.add(fxiOrganization);
                            warmUpTradeUtilC.performOrganizationWarmup(fxiOrganization);
                        }
                    }

                    //Update the reference data
                    referenceDataCacheC.setProvisionOrgs(orgs);
                    referenceDataCacheC.setProvisionedNamespaceIds(orgNameSpaceIds);
                    log.info("CustomProvisionedOrgProviderC.update:Warmed up default organization(s):" + Arrays.asList(defaultOrgs)+",time taken:"+(System.currentTimeMillis()-startTime));
                } else {
                    log.info("CustomProvisionedOrgProviderC.update:Failed to get trading organization(s) reference data, Reference data warm up failed.");
                    //No organization is provisioned
                    referenceDataCacheC.setProvisionOrgs(new HashSet<Organization>());
                }
            }
        } catch (Exception e) {
            log.error("CustomProvisionedOrgProviderC.update:Failed to warm up the trading organization(s) reference data", e);
            //No organization is provisioned
            referenceDataCacheC.setProvisionOrgs(new HashSet<Organization>());
        }
    }

    private Set<VSTradingMetrics> getAllVirtualServerTradingMetric() throws Exception {
        ReferenceDataService referenceDataService = ClientFactory.getFactory().getReferenceDataService();
        QueryBuilder builder = new QueryBuilder(VSTradingMetrics.class);
        builder.addStringParam(NSSH, CustomProvisionedOrgProviderC.MAIN);
        QueryIterator<? extends ReferenceEntity> iterator = referenceDataService.getIterator(VSTradingMetrics.class, CustomProvisionedOrgProviderC.MAIN, builder.build());

        long currentTime = System.currentTimeMillis();
        long sixteenDaysBack = currentTime - SIXTEEN_DAYS_IN_MILLISECONDS;
        Map<String, List<VSTradingMetrics>> vsNamespaces = new HashMap<String, List<VSTradingMetrics>>();
        while (iterator.hasNext()) {
            VSTradingMetrics tradingMetrics = (VSTradingMetrics) iterator.next();
            //Discarding metadata older than two weeks.
            if(tradingMetrics.getCreatedTimeStamp() < sixteenDaysBack){
                log.warn("Discarding the trading organization(s) reference data since its older than sixteen days.CurrentTime:"+currentTime+",data:"+tradingMetrics+",Time to clean up.");
                continue;
            }

            List<VSTradingMetrics> vsTradingMetrics = vsNamespaces.get(tradingMetrics.getShortName());
            if (null == vsTradingMetrics) {
                vsTradingMetrics = new ArrayList<VSTradingMetrics>();
                vsNamespaces.put(tradingMetrics.getShortName(), vsTradingMetrics);
            }
            vsTradingMetrics.add(tradingMetrics);
        }

        Set<VSTradingMetrics> vsTradingMetricses = new HashSet<VSTradingMetrics>();
        for (Map.Entry<String, List<VSTradingMetrics>> entry : vsNamespaces.entrySet()) {
            vsTradingMetricses.add(getLatestReferenceData(entry.getValue()));
        }
        return vsTradingMetricses;
    }


    private VSTradingMetrics getPostTradingMetaData() throws Exception {
        ReferenceDataService referenceDataService = ClientFactory.getFactory().getReferenceDataService();
        Future<List<? extends ReferenceEntity>> retrieveByShortName = referenceDataService.retrieveByShortName(VSTradingMetrics.class, CustomProvisionedOrgProviderC.MAIN, CustomProvisionedOrgProviderC.MAIN, null);
        List<? extends ReferenceEntity> referenceEntities = retrieveByShortName.get(30, TimeUnit.SECONDS);
        return getLatestReferenceData(referenceEntities);
    }

    public static VSTradingMetrics getLatestReferenceData(List<? extends ReferenceEntity> referenceEntities) {
        if (referenceEntities == null || referenceEntities.isEmpty()) {
            return null;
        }

        VSTradingMetrics firstReferenceEntity = (VSTradingMetrics) referenceEntities.get(0);
        if (referenceEntities.size() == 1) {
            return firstReferenceEntity;
        } else {
            log.info("Found multiple trading organization(s) reference data,will consider latest as per creation time" +
                    ".Time to clean up, size:" + referenceEntities.size());
            VSTradingMetrics latestMetaData = firstReferenceEntity;
            VSTradingMetrics vsTradingMetrics;
            for (int i = 1; i < referenceEntities.size(); i++) {
                vsTradingMetrics = (VSTradingMetrics) referenceEntities.get(i);
                if (vsTradingMetrics.getCreatedTimeStamp() > latestMetaData.getCreatedTimeStamp()) {
                    //swap if latest.
                    latestMetaData = vsTradingMetrics;
                }
            }
            return latestMetaData;
        }
    }

    public Set<String> getOrganizationsForWarmup() throws Exception {

        Set<String> orgNames = new HashSet<String>();
        for (VSTradingMetrics vsTradingMetrics : getAllVirtualServerTradingMetric()) {
            Set<String> tradingOrganizations = vsTradingMetrics.getTradingOrganizations();
            if(null!=tradingOrganizations ){
                orgNames.addAll(tradingOrganizations);
            }
        }

        return orgNames;
    }


//    public Set<String> getOrganizationsForWarmup() throws Exception {
//        VSTradingMetrics virtualServerTradingMetrics = getPostTradingMetaData();
//        if (null != virtualServerTradingMetrics) {
//            return virtualServerTradingMetrics.getTradingOrganizations();
//        }
//        return Collections.emptySet();
//    }
}

