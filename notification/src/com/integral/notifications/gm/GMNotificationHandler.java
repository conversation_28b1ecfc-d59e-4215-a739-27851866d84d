package com.integral.notifications.gm;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.util.ISCommonUtilC;
import com.integral.is.configuration.ISCommonConfigFactory;
import com.integral.is.configuration.ISCommonMBean;
import com.integral.is.log.MessageLogger;
import com.integral.is.management.ManagementConstants;
import com.integral.is.management.monitor.StagingOrderMonitorMessageBuilder;
import com.integral.is.management.monitor.spaces.SpacesOrderMonitorMessageBuilder;
import com.integral.is.management.monitor.spaces.SpacesStagingOrderMonitorMessageBuilder;
import com.integral.is.management.monitor.spaces.SpacesTradeMonitorMessageBuilder;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.management.NotificationTypes;
import com.integral.management.order.OrderManagementObject;
import com.integral.management.order.OrderManagementObjectC;
import com.integral.management.stagingOrder.StagingOrderManagementObject;
import com.integral.management.stagingOrder.StagingOrderManagementObjectC;
import com.integral.management.trade.TradeManagementObject;
import com.integral.management.trade.TradeManagementObjectC;
import com.integral.model.dealing.OrderMatchRequest;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.notifications.NotificationConfiguration;
import com.integral.spaces.notification.NHResponse;
import com.integral.spaces.notification.NHResponseCode;
import com.integral.spaces.notification.Notification;
import com.integral.spaces.notification.NotificationHandler;
import com.integral.staging.Order;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.user.Organization;

import java.util.EnumMap;
import java.util.HashMap;
import java.util.Map;

import static com.integral.is.management.monitor.MonitorMessageBuilderUtil.getFirmOrgName;

/**
 * Created by IntelliJ IDEA.
 * User: bhanotp
 * Date: 8/2/12
 */
public class GMNotificationHandler implements NotificationHandler {

    private Log log = LogFactory.getLog(GMNotificationHandler.class);
    
    private static Log gridMonitorMessage = LogFactory.getLog("com.integral.is.monitor.xml");
    private static final ISCommonMBean iscm = ISCommonConfigFactory.getISCommonMBean();
    private static final  String defaultTradeJmsTopic = iscm.getTradeJmsTopic();
    protected static ServerMBean serverMBean = ConfigurationFactory.getServerMBean();
    
    protected static SpacesStagingOrderMonitorMessageBuilder stagingAreaOrderMessageBuilder = new SpacesStagingOrderMonitorMessageBuilder();
    protected static SpacesTradeMonitorMessageBuilder tradeMessageBuilder = new SpacesTradeMonitorMessageBuilder();
    protected static SpacesOrderMonitorMessageBuilder orderMessageBuilder = new SpacesOrderMonitorMessageBuilder();
    private IGMMessageNotifier notifier;
    private boolean pool = true;

    private static final String[] Trade_Submitted = new String[]{ManagementConstants.TRADE_SUBMITTED};
    private static final String[] Trade_Exception = new String[]{ManagementConstants.TRADE_EXCEPTION};
    private static final String[] Trade_Pending = new String[]{ManagementConstants.PENDING};
    private static final String[] Trade_PreRate_Verified = new String[]{ManagementConstants.TRADE_PRERATE_VERIFIED};
    private static final String[] Trade_PostRate_Verified = new String[]{ManagementConstants.TRADE_POSTRATE_VERIFIED};
    private static final String[] Trade_Verified = new String[]{ManagementConstants.TRADE_VERIFIED};
    private static final String[] Trade_Verified_Confirmed = new String[]{ManagementConstants.TRADE_VERIFIED,ManagementConstants.TRADE_CONFIRMED};
    private static final String[] Trade_PostRate_Verified_Confirmed = new String[]{ManagementConstants.TRADE_POSTRATE_VERIFIED,ManagementConstants.TRADE_CONFIRMED};
    private static final String[] Trade_Rejected = new String[]{ManagementConstants.TRADE_REJECTED};
    private static final String[] Trade_Confirmed = new String[]{ManagementConstants.TRADE_CONFIRMED};
    private static final String[] Trade_Confirmed_Risk = new String[]{ManagementConstants.TRADE_CONFIRMED,ManagementConstants.TRADE_RISK_POSITION};
    private static final String[] Trade_Cancelled = new String[]{ManagementConstants.TRADE_CANCELLED};
    private static final String[] Trade_DownloadSent = new String[]{ManagementConstants.DEAL_DOWNLOAD_SENT};
    private static final String[] Trade_Submitted_Verified_Confirmed = new String[]{ManagementConstants.TRADE_SUBMITTED,ManagementConstants.TRADE_VERIFIED,ManagementConstants.TRADE_CONFIRMED};
    private static final String[] Order_PartialMatch = new String[]{ISCommonConstants.EVENT_ORDER_PARTIAL_MATCH};
    private static final String[] Trade_Amended = new String[]{ManagementConstants.TRADE_AMENDED};
    private static final String[] Trade_DontKnow = new String[]{ManagementConstants.VERIFIED_NOT_ACKNOWLEDGED};
    private static final String[] Match_Submitted = new String[]{ManagementConstants.MATCH_SUBMITTED};
    private static final String[] Match_Expired = new String[]{ManagementConstants.MATCH_EXPIRED};
    private static final String[] Match_Cancelled = new String[]{ManagementConstants.MATCH_CANCELLED};
    private static final String[] Match_Rejected = new String[]{ManagementConstants.MATCH_REJECTED};
    private static final String[] Match_PreRate_Expired = new String[]{ManagementConstants.MATCH_PRERATE_EXPIRED};
    private static final String[] Match_PreRate_Cancelled = new String[]{ManagementConstants.MATCH_PRERATE_CANCELLED};
    private static final String[] Match_Amend = new String[]{ManagementConstants.MATCH_AMEND};

    private static final EnumMap<ApplicationEventCodes, String> orderEventTranslation = new EnumMap<ApplicationEventCodes, String>(ApplicationEventCodes.class);
    private static final EnumMap<ApplicationEventCodes, String> stagingOrderEventTranslation = new EnumMap<ApplicationEventCodes, String>(ApplicationEventCodes.class);

    static {
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_SUBMIT, ISCommonConstants.EVENT_ORDER_SUBMITTED);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_MATCH, ISCommonConstants.EVENT_ORDER_MATCHED);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_SUBMIT, ISCommonConstants.EVENT_ORDER_MATCHED);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_EXPIRE, ISCommonConstants.EVENT_ORDER_MATCH_EXPIRED);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_CANCEL, ISCommonConstants.EVENT_ORDER_MATCH_CANCELLED);
        orderEventTranslation.put( ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_AMEND,ISCommonConstants.EVENT_ORDER_MATCH_AMEND );
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_CANCEL, ISCommonConstants.EVENT_ORDER_CANCELLED);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_EXPIRE, ISCommonConstants.EVENT_ORDER_EXPIRED);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_FAILED, ISCommonConstants.EVENT_ORDER_FAILED);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_FILL, ISCommonConstants.EVENT_ORDER_PARTIAL_MATCH);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_FILLED, ISCommonConstants.EVENT_ORDER_FILLED);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_RELOADED,ISCommonConstants.EVENT_ORDER_RELOADED);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_AMEND,ISCommonConstants.EVENT_ORDER_AMENDED);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_TRIGGERED,ISCommonConstants.EVENT_ORDER_WORKING);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_STATE_UPDATE, ISCommonConstants.EVENT_ORDER_FAILED);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_RESTATED, ISCommonConstants.EVENT_ORDER_RESTATED);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_SUSPENDED, ISCommonConstants.EVENT_ORDER_SUSPENDED);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_RESUMED, ISCommonConstants.EVENT_ORDER_RESUMED);
        orderEventTranslation.put(ApplicationEventCodes.EVENT_ESP_ORDER_UPDATE_ON_TRADE_NET, ISCommonConstants.EVENT_ORDER_UPDATE_ON_NETTING);
        stagingOrderEventTranslation.put(ApplicationEventCodes.EVENT_SA_ORDER_SUBMIT, ISCommonConstants.EVENT_SA_ORDER_SUBMITTED);
        stagingOrderEventTranslation.put(ApplicationEventCodes.EVENT_SA_ORDER_UPDATE, ISCommonConstants.EVENT_SA_ORDER_UPDATED);
        stagingOrderEventTranslation.put(ApplicationEventCodes.EVENT_SA_ORDER_CANCEL, ISCommonConstants.EVENT_SA_ORDER_CANCELLED);
        stagingOrderEventTranslation.put(ApplicationEventCodes.EVENT_SA_ORDER_EXPIRY, ISCommonConstants.EVENT_SA_ORDER_EXPIRED);
        // need to handle this seperately
    }

    ThreadLocal<TradeManagementObject> threadLocalTMO = new ThreadLocal<TradeManagementObject>() {
        @Override
        protected TradeManagementObject initialValue() {
            return new TradeManagementObjectC("", serverMBean.getVirtualServerName());
        }
    };

    ThreadLocal<OrderManagementObject> threadLocalOMO = new ThreadLocal<OrderManagementObject>() {
        @Override
        protected OrderManagementObject initialValue() {
            return new OrderManagementObjectC("", serverMBean.getVirtualServerName());
        }
    };
    
    ThreadLocal<StagingOrderManagementObject> threadLocalSOMO = new ThreadLocal<StagingOrderManagementObject>() {
        @Override
        protected StagingOrderManagementObject initialValue() {
            return new StagingOrderManagementObjectC("", serverMBean.getVirtualServerName());
        }
    };

    /*public GMNotificationHandler() {
        notifier  = new GMMessageNotifier();
    }*/

    public GMNotificationHandler( IGMMessageNotifier notifier ) {
        this.notifier = notifier;
        this.pool = false; // Testing purpose.
    }

    private static class BrokerJmsNameUtilHolder{
         public static BrokerJmsNameUtil brokerJmsNameUtil = BrokerJmsNameUtil.getInstance();
    }

    //Thread safe.
    protected static BrokerJmsNameUtil getBrokerJmsNameUtil(){
        return BrokerJmsNameUtilHolder.brokerJmsNameUtil;
    }

    private void logNotification(Notification notification) {
    	if(gridMonitorMessage.isInfoEnabled()) {
	    	StringBuilder logMessage = new StringBuilder(100);
	    	logMessage.append(" NOTIF_FOR_GM");
	    	logMessage.append(" EVENT=").append(notification.getEvent());
	    	logMessage.append(" ,APP_EVENT=").append(ApplicationEventCodes.fromCode(notification.getAppEventCode()));
	    	logMessage.append(" ,CORR_ID=").append(notification.getCorrelationId());
	    	logMessage.append(" ,NAMESPACE=").append(notification.getNamespaceName());
	    	logMessage.append(" ,METASPACE=").append(notification.getMetaspace());	    	
	    	logMessage.append(" ,SPACE=").append(notification.getSpace());	    	
	    	gridMonitorMessage.info(logMessage.toString());
    	}
    }

    public NHResponse handle(Notification notification) {
    	logNotification(notification);
        try {
            if (SingleLegTrade.class.isAssignableFrom(notification.getDataObjectType())) {
                handleTradeNotification(notification);
            } else if (SingleLegOrder.class.isAssignableFrom(notification.getDataObjectType())) {
                handleOrderNotification(notification);
            } else if (OrderMatchRequest.class.isAssignableFrom(notification.getDataObjectType())) {
                handleOrderMatchRequestNotification(notification);
            } else if (Order.class.isAssignableFrom(notification.getDataObjectType())) {
            	handleStagingOrderNotification(notification);
            }
        } catch (Exception nse) {
            log.error("handle : Exception in handling notification->" + notification, nse);
            return new NHResponse(NHResponseCode.FAILURE);
        }

        return new NHResponse(NHResponseCode.SUCCESS);
    }

	@Override
    public NHResponse handleRedelivery(Notification notification,String errorDesc) {
        return handle(notification);
    }

    @Override
    public long getLongIdentifier() {
        return NotificationConfiguration.NOTIFICATION_HANDLER_GM.getLongIdentifier();
    }

    private void handleOrderNotification(Notification notification) {     
            SingleLegOrder request = (SingleLegOrder ) notification.getCompleteObject();
            if (request == null) {
                log.error("handleOrderNotification : SingleLegOrder is null.");
                return;
            }
            ApplicationEventCodes aec = ApplicationEventCodes.fromCode(notification.getAppEventCode());
            String event = getOMOEvent( aec,request );

            if(event == null || event.isEmpty()){                
                if(log.isDebugEnabled()){
                    log.debug( "handleOrderNotification : No Mapped Event for ApplicationEventCode "+aec.name() );
                }
                return;
            }

        processOrderNotification(notification, request, event);
    }
    
    private void handleStagingOrderNotification(Notification notification) {
			Order stagingOrder = (Order) notification.getCompleteObject();
			if(stagingOrder == null) {
				log.error("handleStagingOrderNotification : Staging Order is null.");
				return;
			}
			ApplicationEventCodes aec = ApplicationEventCodes.fromCode(notification.getAppEventCode());
			String event = stagingOrderEventTranslation.get(aec);
			
			if(event == null || event.isEmpty()){
				if(log.isDebugEnabled()){
					log.debug("handleStagingOrderNotification : No Mapped Event for ApplicationEventcode " + aec.name());
				}
				return;
			}
			processStagingOrderNotification(notification, stagingOrder, event);
		
	}

    private void processOrderNotification(Notification notification, SingleLegOrder request, String event) {
        try {            
            OrderManagementObject omo = pool ? threadLocalOMO.get() : new OrderManagementObjectC("", serverMBean.getVirtualServerName());
            omo.setName(request.get_id());
            omo.setVirtualServerName(request.getVirtualServer());
            omo.setServer(notification.getServerName());
            omo.reInit();
            orderMessageBuilder.createOrderMonitorMessage(omo, request, event, null, null);
            omo.setCorelationId(notification.getCorrelationId());

            if (ISCommonUtilC.isWarmUpObject(request)) {
                ISCommonUtilC.setAsWarmUpObject(omo);
            }
            LegalEntity le = request.getLegalEntity();
            Organization cptyAOrg = le.getOrganization();
            Organization cptyABrokerOrg = le.getOrganization().getBrokerOrganization();

            omo.setCptyAOrg(request.getLegalEntity().getOrganization().getShortName());
            omo.setFirmForCptyA(getFirmOrgName(cptyAOrg));
            omo.setBrokerForCptyA(cptyABrokerOrg != null ? cptyABrokerOrg.getShortName() : null);
            // create notification.
            Map<String, String> properties = new HashMap<String, String>();
            properties.put(ManagementConstants.ORGANIZATION_PROPERTY, cptyAOrg != null ? cptyAOrg.getShortName() : null);
            properties.put(ManagementConstants.BROKER_ORGANIZATION_PROPERTY, cptyABrokerOrg != null ? cptyABrokerOrg.getShortName() : null);
            notifyOrder( omo, properties );
            if( ISCommonConstants.EVENT_ORDER_FILLED.equals( event )){
                omo.setStatus( ISCommonConstants.EVENT_ORDER_FULL_MATCH );
                notifyOrder( omo, properties );
            }
        } catch (Exception e) {
            log.error("processOrderNotification : Exception in processing notification->" + notification, e);
        } finally {
            if( pool ) threadLocalOMO.get().reset();
        }
    }
    
    
    private void processStagingOrderNotification(Notification notification, Order stagingOrder, String event) {
    	try {
    		StagingOrderManagementObject somo = pool ? threadLocalSOMO.get() : new StagingOrderManagementObjectC("", serverMBean.getVirtualServerName());
        	somo.setName(stagingOrder.get_id());
        	somo.setVirtualServerName(stagingOrder.getVirtualServer());
        	somo.setServer(notification.getServerName());
        	somo.reInit();
        	stagingAreaOrderMessageBuilder.createStagingOrderManagementObject(somo, stagingOrder, event, null, null);
        	somo.setCorrelationId(notification.getCorrelationId());
        	Map<String, String> properties = new HashMap<String, String>();
        	properties.put(ManagementConstants.ORGANIZATION_PROPERTY, stagingOrder.getNamespaceName());
        	properties.put(ManagementConstants.BROKER_ORGANIZATION_PROPERTY, stagingOrder.getInputCC() != null ? stagingOrder.getInputCC() : stagingOrder.getExecutedCC());
        	notifyStagingOrder(somo, properties);
    	} catch (Exception e) {
    		log.error("processStagingOrderNotification : Exception in processing notification->" + notification, e);
    	} finally {
    		if(pool) threadLocalSOMO.get().reset();
    	}
    }

    private String getOMOEvent( ApplicationEventCodes aec, SingleLegOrder request ) {
        switch ( aec ){
            case EVENT_ESP_ORDER_EXPIRE:
                if( request.getTimeInForce() != null ){
                    switch ( request.getTimeInForce() ){
                        case GTD:
                        case DAY:
                        case GFS:
                        case GTF:
                            return ISCommonConstants.EVENT_ORDER_EXPIRED;
                        default:
                            return ISCommonConstants.EVENT_ORDER_CANCELLED;
                    }
                }
        }
        return orderEventTranslation.get(aec);
    }

    private void handleTradeNotification(Notification notification) {
        try {
            SingleLegTrade trade = (SingleLegTrade) notification.getCompleteObject();
            if (trade == null) {
                log.error("handleTradeNotification : Trade is null.");
                return;
            }

            if (log.isDebugEnabled()) {
                log.debug(notification.toString());
                log.debug(trade.toString());
            }
            ApplicationEventCodes aec = ApplicationEventCodes.fromCode(notification.getAppEventCode());
            String[] orderEvents = getGMOrderEvents( aec );
            if( orderEvents != null && orderEvents.length != 0 ){
                for( String event : orderEvents ){
                    processOrderNotification(notification, trade.getOrderRequest(), event);
                }
            }
            else{
                if( log.isDebugEnabled() ){
                    log.debug( "handleTradeNotification : No GM Order Events mapped to Trade aec="+aec.name() );
                }
            }
            String[] tradeEvents = getGMTradeEvents( trade, aec );
            if (tradeEvents != null && tradeEvents.length !=0) {
                for ( String event : tradeEvents ) {
                    TradeManagementObject tmo = threadLocalTMO.get();
                    try {
                    	if(trade.isDontKnowRejection() && ManagementConstants.TRADE_REJECTED.equals(event)){
                        	event = ManagementConstants.REJECTED_BY_INTEGRAL_DONTKNOW;
                        } else if(trade.isAutoCancelled() && ManagementConstants.TRADE_REJECTED.equals(event)){
                            event = ManagementConstants.REJECTED_BY_INTEGRAL_TIMEDOUT;
                        }
                        tmo.setVirtualServerName( trade.getVirtualServer() );
                        tmo.setServer( notification.getServerName() );
                        tmo.setName( getTransactionId( trade ) );
                        tmo.reInit();
                        processTradeNotification( tmo, trade, event );
                        tmo.setCorelationId( notification.getCorrelationId() );
                    } catch ( Exception ex ) {
                        log.error( "handleTradeNotification : Exception in sending GM notification for event="+event+",tradeId="+ getTransactionId( trade ) +",aec="+aec.name(), ex );
                    } finally {
                        tmo.reset();
                    }
                }
            }
            else{
                if(log.isDebugEnabled() ){
                    if( log.isDebugEnabled() ){
                        log.debug( "handleTradeNotification : No GM Trade Events mapped to Trade aec="+aec.name() );
                    }
                }
            }
        } catch (Exception ex) {
            log.error("handleTradeNotification : Exception in handling notification->" + notification, ex);
        }
    }

    private void handleOrderMatchRequestNotification(Notification notification)
    {
        OrderMatchRequest matchRequest = (OrderMatchRequest) notification.getCompleteObject();
        if (matchRequest == null) {
            log.error("handleOrderMatchRequestNotification : OrderMatchRequest is null.");
            return;
        }

        ApplicationEventCodes aec = ApplicationEventCodes.fromCode(notification.getAppEventCode());
        String[] matchEvents = getGMMatchEvents(matchRequest, aec);
        if(matchEvents != null){
            for (String event : matchEvents) {
                TradeManagementObject tmo = threadLocalTMO.get();
                try {
                    tmo.setVirtualServerName(matchRequest.getVirtualServer());
                    tmo.setServer(notification.getServerName());
                    tmo.setName(matchRequest.get_id());
                    tmo.reInit();
                    processMatchNotification(tmo, matchRequest, event);
                }
                catch (Exception e) {
                    log.error("handleOrderMatchRequestNotification: Exception in handling GM notification for event = " +
                            event + " ,matchId = " + matchRequest.get_id() + " ,aec = " + aec.name(), e);
                }
                finally {
                    tmo.reset();
                }
            }
        }
    }

    private String getTransactionId( SingleLegTrade trade ) {
        if( trade.isMaker() ){
            return trade.getExternalReferenceId();
        }
        return trade.get_id();
    }

    private String[] getGMMatchEvents(OrderMatchRequest matchRequest, ApplicationEventCodes aec) {

        if(!matchRequest.isPrimeBrokerInitiated()){
            switch (aec) {
                case EVENT_ESP_ORDER_MATCH_SUBMIT:
                    return Match_Submitted;
                case EVENT_ESP_ORDER_MATCH_EXPIRE:
                    return Match_Expired;
                case EVENT_ESP_ORDER_MATCH_CANCEL:
                    return Match_Cancelled;
                case EVENT_ESP_ORDER_MATCH_REJECT:
                    return Match_Rejected;
                case EVENT_ESP_ORDER_MATCH_PRERATE_CANCEL:
                    return Match_PreRate_Cancelled;
                case EVENT_ESP_ORDER_MATCH_PRERATE_EXPIRE:
                    return Match_PreRate_Expired;
                case EVENT_ESP_ORDER_MATCH_AMEND:
                    return Match_Amend;
                case EVENT_ESP_ORDER_MATCH_SUBMIT_SUCCESS:
                    log.info( "NO-OP Match Submit Event " + matchRequest.get_id() );
                    return null;
                case EVENT_ESP_ORDER_MATCH:
                    log.info( "NO-OP Match Event " + matchRequest.get_id() );
                    return null;
                default:
                    log.error("Couldn't find associated GM event for order match reques : " + matchRequest.get_id());
                    return null;
            }
        }
        return null;
    }

    private String[] getGMTradeEvents( SingleLegTrade trade, ApplicationEventCodes aec ) {
        switch ( aec ){
            case EVENT_ESP_TRADE_VERIFIED:
            case EVENT_ESP_TRADE_CREATED_VERIFIED:
                return Trade_Verified;
            case EVENT_ESP_TRADE_REJECTED:
                return Trade_Rejected;
            case EVENT_ESP_TRADE_CONFIRMED:
                if( trade.isRisk() ){
                    return Trade_Confirmed_Risk;
                }
                return Trade_Confirmed;
            case EVENT_ESP_TRADE_ACCEPT:
                return Trade_Submitted;
            case EVENT_ESP_TRADE_FAILED:
                return Trade_Exception;
            case EVENT_ESP_TRADE_PENDING:
                return Trade_Pending;
            case EVENT_ESP_TRADE_CANCEL:
            case EVENT_ESP_NET_TRADE_CANCEL:
                return Trade_Cancelled;
            case EVENT_ESP_TRADE_STP:
                return Trade_DownloadSent;
            case EVENT_ESP_TRADE_NET:
                return Trade_Verified_Confirmed;
            case EVENT_ESP_LIFT_CREATE_MAKER_TRADE:
            case EVENT_DO_CREATE_MAKER_TRADE:
                return Trade_Submitted_Verified_Confirmed;
            case EVENT_ESP_TRADE_AMEND:
            case EVENT_ESP_NET_TRADE_AMEND:
            	return Trade_Amended;
            case EVENT_ESP_TRADE_DONTKNOW:
                return Trade_DontKnow;
            case EVENT_ESP_TRADE_PRE_RATE_VERIFIED:
            case EVENT_ESP_MAKER_TRADE_PRE_RATE_VERIFIED:
                return Trade_PreRate_Verified;
            case EVENT_ESP_TRADE_POST_RATE_VERIFIED:
                return Trade_PostRate_Verified;
            case EVENT_ESP_MAKER_TRADE_POST_RATE_VERIFIED:
                return Trade_PostRate_Verified_Confirmed;
        }
        return null;
    }

    private String[] getGMOrderEvents( ApplicationEventCodes aec ) {
        switch ( aec ){
            case EVENT_ESP_TRADE_VERIFIED:
            case EVENT_ESP_TRADE_PRE_RATE_VERIFIED:
            case EVENT_ESP_TRADE_CREATED_VERIFIED:
            case EVENT_ESP_LIFT_CREATE_MAKER_TRADE:
            case EVENT_DO_CREATE_MAKER_TRADE:
            case EVENT_ESP_MAKER_TRADE_PRE_RATE_VERIFIED:
                return Order_PartialMatch;
        }
        return null;
    }

    private void processMatchNotification(TradeManagementObject tmo, OrderMatchRequest matchRequest, String event) throws Exception {
        tradeMessageBuilder.createTradeMessage(tmo, matchRequest, event, null);
        try {
            if (tmo != null) {
                LegalEntity le = matchRequest.getLegalEntity();
                Organization cptyAOrg = le.getOrganization();
                Organization cptyABrokerOrg = le.getOrganization().getBrokerOrganization();

                tmo.setCptyAOrg(matchRequest.getLegalEntity().getOrganization().getShortName());
                tmo.setBrokerForCptyA(cptyABrokerOrg != null ? cptyABrokerOrg.getShortName() : null);
                Map<String, String> properties = new HashMap<String, String>();
                properties.put( ManagementConstants.ORGANIZATION_PROPERTY, cptyAOrg != null ? cptyAOrg.getShortName() : null);
                properties.put(ManagementConstants.BROKER_ORGANIZATION_PROPERTY, cptyABrokerOrg != null ? cptyABrokerOrg.getShortName() : null);
                notifyTrade(tmo, properties);
            }
        } catch (Exception e) {
            MessageLogger.getInstance().log("MONITORING", "GMNotificationHandler.processMatchNotification",
                new StringBuffer().append("Message was not send to Monitoring Server. MatchId [").append(matchRequest.get_id())
                    .append( "] " ).append(e).append(" [").append(e.getMessage()).append("]").toString(), null);
            throw e;
        }
    }

    private void processTradeNotification( TradeManagementObject tmo, SingleLegTrade trade, String event ) throws Exception {
        tradeMessageBuilder.createTradeMessage( tmo, trade, event, null );
        try {
            if (tmo != null) {
                LegalEntity le = trade.getLegalEntity();
                Organization cptyAOrg = le.getOrganization();
                Organization cptyABrokerOrg = le.getOrganization().getBrokerOrganization();

                tmo.setCptyAOrg(trade.getLegalEntity().getOrganization().getShortName());
                tmo.setBrokerForCptyA(cptyABrokerOrg != null ? cptyABrokerOrg.getShortName() : null);
                Map<String, String> properties = new HashMap<String, String>();
                properties.put( ManagementConstants.ORGANIZATION_PROPERTY, cptyAOrg != null ? cptyAOrg.getShortName() : null);
                properties.put(ManagementConstants.BROKER_ORGANIZATION_PROPERTY, cptyABrokerOrg != null ? cptyABrokerOrg.getShortName() : null);
                if(tmo.getStatus().equals(ManagementConstants.TRADE_SUBMITTED)){
                    tmo.setStatus(ManagementConstants.TRADE_PRE_SUBMIT);
                    String marketSnapshot = tmo.getMarketSnapshot();
                    tmo.setMarketSnapshot(null);
                    notifyTrade( tmo, properties );

                    tmo.setStatus(ManagementConstants.TRADE_SUBMITTED);
                    tmo.setMarketSnapshot(marketSnapshot);
                }
                notifyTrade( tmo, properties );
            }
        } catch (Exception e) {
            MessageLogger.getInstance().log("MONITORING", "GMNotificationHandler.handleTradeNotification",
                    new StringBuffer().append("Message was not send to Monitoring Server. TXID [").append( getTransactionId( trade ) )
                            .append( "] " ).append(e).append(" [").append(e.getMessage()).append("]").toString(), null);
            throw e;
        }
    }

    public void notifyOrder( OrderManagementObject omo, Map properties )
    {
        com.integral.management.Notification notif = new com.integral.management.Notification(NotificationTypes.STORE, omo, System.currentTimeMillis());
        notif.setUserData(omo);
        //if split is enabled use util to get the proper to topic name to which we need to publish the message
        if(iscm.isEnableGridMonSplitBrokerTradeTopics()) {
            String tradeJmsTopic = getBrokerJmsNameUtil().getTopicName( omo.getVirtualServerName(), omo.getCorelationId(), omo.getOrderID() );
            properties.put(ManagementConstants.BROKER_JMS_TOPIC, tradeJmsTopic);
        }
        //fall back to old logic if splitting is disabled
        else{
            properties.put(ManagementConstants.BROKER_JMS_TOPIC, defaultTradeJmsTopic);
        }
        notifier.notify(notif, properties, omo.getStatus());
    }
    
    
    public void notifyStagingOrder( StagingOrderManagementObject somo, Map properties )
    {
        com.integral.management.Notification notif = new com.integral.management.Notification(NotificationTypes.STORE, somo, System.currentTimeMillis());
        notif.setUserData(somo);
        //if split is enabled use util to get the proper to topic name to which we need to publish the message
        if(iscm.isEnableGridMonSplitBrokerTradeTopics()) {
            String stagingOrderJmsTopic = getBrokerJmsNameUtil().getTopicName( somo.getVirtualServerName(), somo.getCorrelationId(), somo.getOrderId() );
            properties.put(ManagementConstants.BROKER_JMS_TOPIC, stagingOrderJmsTopic);
        }
        //fall back to old logic if splitting is disabled
        else{
            properties.put(ManagementConstants.BROKER_JMS_TOPIC, defaultTradeJmsTopic);
        }
        notifier.notify(notif, properties, somo.getStatus());
    }

    public void notifyTrade( TradeManagementObject tmo, Map properties )
    {
        com.integral.management.Notification notif = new com.integral.management.Notification(NotificationTypes.STORE, tmo, System.currentTimeMillis());
        notif.setUserData(tmo);
        //if split is enabled use util to get the proper to topic name to which we need to publish the message
        if(iscm.isEnableGridMonSplitBrokerTradeTopics()) {
            String tradeJmsTopic = getBrokerJmsNameUtil().getTopicName( tmo.getVirtualServerName(), tmo.getCorelationId(), tmo.getOrderID() );
            properties.put(ManagementConstants.BROKER_JMS_TOPIC, tradeJmsTopic);
        }
        //fall back to old logic if splitting is disabled
        else{
            properties.put(ManagementConstants.BROKER_JMS_TOPIC, defaultTradeJmsTopic);
        }
        notifier.notify(notif, properties, tmo.getStatus());
    }
}
