package com.integral.notifications.gm;

import com.integral.is.configuration.ISCommonConfigFactory;
import com.integral.is.configuration.ISCommonMBean;
import com.integral.is.management.ManagementConstants;
import com.integral.jmsx.JMSDBUtilsC;
import com.integral.jmsx.JNDIEntry;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.configuration.JNDIEntryC;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by pendyalan on 6/30/2014.
 * this Utility class helps in getting the proper JMS name on which we need to publish the notification to GM
 * it is a Singleton , use it with the help of getInstance method.
 */
public class BrokerJmsNameUtil {
    private static final Log log = LogFactory.getLog(BrokerJmsNameUtil.class);
    private static final  ISCommonMBean iscm = ISCommonConfigFactory.getISCommonMBean();
    private static final  String defaultTradeJmsTopic = iscm.getTradeJmsTopic();
    //This should be in sync with GM FXIDirectAgentC.numberOfTopics
    private static final  int numberOfTopics = iscm.getNumberOfGridMonOaTradeTopics();
    private static final  JMSDBUtilsC util = JMSDBUtilsC.getInstance();
    private Map<String, String> jmsCache = null;

    private static volatile BrokerJmsNameUtil instance = null;

    /**
     * Singleton so constructor is private
     */
    private BrokerJmsNameUtil()
    {
    }

    public static BrokerJmsNameUtil getInstance ()
    {
      if(instance==null)
      {
          instance = new BrokerJmsNameUtil();
          instance.init();
      }
        return instance;
    }
    /**
     * init the class
     */
    private void init()
    {
        jmsCache = getJMSCollection();
    }

    /**
     *the following API is exposed
     *and it helps in getting the proper JMS name on which we need to publish the notification to GM
   */

    public String getTopicName(String vs, String correlationID,String orderID) {
        if(jmsCache==null)
        {
            log.warn("BrokerJmsNameUtil.getTopicName jmsCache is null ");
            init();
        }
            String topicName = jmsCache.get(vs);
            //if run time new vs addtion needs to be supported we need to actually call getJMSColection instead
            if (topicName == null) {
                log.warn("BrokerJmsNameUtil.getTopicName Jms Topic Name not found for vs " + vs + " so using topic " + defaultTradeJmsTopic + " in commutation ");
                topicName = defaultTradeJmsTopic;
            }
            return _getTopicName(topicName, correlationID, orderID);
    }
    /**
     *   the following helper function  helps in populating the cache , the cache contains VS as key and JMS name as value
     */
    private static Map<String, String> getJMSCollection() {
        Map<String, String> col = new HashMap<String, String>();
        Collection<JNDIEntry> destC = util.getJNDIEntries(ManagementConstants.MANAGEMENT_JMS_TOPIC);
		//added null chk to fix test cases.
		if(destC!=null)
		{
			for(JNDIEntry dest : destC) {
				if (dest != null) {
					if (dest instanceof JNDIEntryC) {
						String vs = ((JNDIEntryC) dest).getVirtualServer().getShortName();
						col.put(vs, getJmsNameFromJNDIEntry(dest));
					}
				}
			}
		}
        log.info(" JMS topic name cache " +col);
        return col;
    }
    /**
     *the following helper function  helps in getting JMS name from JNDI entry
    */
    private static String getJmsNameFromJNDIEntry(JNDIEntry dest) {
        String tradeJmsTopic = dest.getJmsName();
        String newDest = ManagementConstants.MANAGEMENT_JMS_TOPIC + ".TRADES";
        tradeJmsTopic = tradeJmsTopic.replace( ManagementConstants.MANAGEMENT_JMS_TOPIC, newDest );
        return tradeJmsTopic;
    }

    /**
     *the following helper function  returns the final topic on which we need to publish the message
   */
    private static String _getTopicName(String tradeJmsTopic,String corelationID,String orderID)
    {
        if(iscm.isUseMultipleTradeTopic()&& isSplitNeeded(tradeJmsTopic))
        {
            return tradeJmsTopic + getSuffixValue(corelationID, orderID);
        }
        else{
            return tradeJmsTopic ;
        }
    }

    /**
     * the following helper function  helps in identifying if default topic name should be split for load balancing
   */
    private static boolean isSplitNeeded(String tradeJmsTopic) {
        return defaultTradeJmsTopic.equals(tradeJmsTopic);
    }


   /**
    * the following helper function  returns the suffix value for the topic
    */
    private static int getSuffixValue(String coId, String orderId)
    {
        coId = getCorrelationId(coId, orderId);
        int i = 0;
        try{
            i = Math.abs(coId.hashCode())%numberOfTopics;
        }
        catch (Exception e) {
            log.error("BrokerJmsNameUtil.getMeId exception in TradeMonitorAgentC.getMeId",e);
        }
        return i;
    }

    /**
     *the following helper function  returns the CorrelationId
    */
    private static String getCorrelationId(String coId, String orderId) {
        if (coId != null && !"".equals(coId))
            return coId;
        else {
            if (orderId !=null && orderId.lastIndexOf('C') != -1) {
                orderId = orderId.substring(0, orderId.lastIndexOf('C'));
            }
            return orderId;
        }
    }
}
