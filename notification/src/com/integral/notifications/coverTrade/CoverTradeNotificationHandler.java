package com.integral.notifications.coverTrade;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.spaces.fx.esp.factory.DealingModelFactory;
import com.integral.is.spaces.fx.esp.primebroker.PrimeBrokerUtil;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.is.spaces.fx.persistence.ISSpacesPersistenceService;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.model.dealing.*;
import com.integral.model.dealing.descriptor.*;
import com.integral.notifications.NotificationConfiguration;
import com.integral.spaces.ApplicationSpaceEvent;
import com.integral.spaces.notification.NHResponse;
import com.integral.spaces.notification.NHResponseCode;
import com.integral.spaces.notification.Notification;
import com.integral.spaces.notification.NotificationHandler;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by tapan.vijay on 4/4/14.
 */
public class CoverTradeNotificationHandler implements NotificationHandler
{
    private static final Log log = LogFactory.getLog(CoverTradeNotificationHandler.class);
    private static final ISMBean isMbean = ISFactory.getInstance().getISMBean();
    private static final DealingModelFactory dealingModelFactory = DealingModelFactory.getInstance();
    private static final ISSpacesPersistenceService isSpacesPersistenceService = PersistenceServiceFactory.getISSpacesPersistenceService();

    @Override
    public NHResponse handleRedelivery(Notification notification, String string) {
        return handle(notification);
    }

    @Override
    public NHResponse handle(Notification notification) {
        try {
            if (!isMbean.isMultiPbSerializationEnabled()) {
                // Multi pb serialization is not enabled, so nothing to be done on this handler
                return new NHResponse(NHResponseCode.SUCCESS);
            }

            ApplicationEventCodes aec = ApplicationEventCodes.fromCode(notification.getAppEventCode());
            if (SingleLegTrade.class.isAssignableFrom(notification.getDataObjectType())) {
                SingleLegTrade trade = (SingleLegTrade) notification.getCompleteObject();
                if (shouldHandleTradeEvent(trade, aec)) {
                    handleTradeNotification(trade, aec);
                }
            }
            else if (SingleLegOrderMatch.class.isAssignableFrom(notification.getDataObjectType())) {
                SingleLegOrderMatch  matchEvent = (SingleLegOrderMatch) notification.getCompleteObject();
                if (shouldHandleMatchEvent(matchEvent, aec)) {
                    handleMatchEventNotification(matchEvent, aec);
                }
            }
            else if (SingleLegOrder.class.isAssignableFrom(notification.getDataObjectType())) {
                SingleLegOrder order = (SingleLegOrder) notification.getCompleteObject();
                if (shouldHandleOrderEvent(order, aec)) {
                    handleOrderNotification(order, aec);
                }
            }
        }
        catch ( Exception ex ) {
            log.error( "Exception in handling Notification " + notification, ex );
            return new NHResponse(NHResponseCode.FAILURE);
        }

        return new NHResponse(NHResponseCode.SUCCESS);
    }

    private boolean shouldHandleTradeEvent(SingleLegTrade trade, ApplicationEventCodes aec)
    {
        State.Name tradeStateName = trade.getState().getName();
        if (tradeStateName == State.Name.TSNET || tradeStateName == State.Name.TSNETTED) {
            return false;
        }

        SingleLegOrderMatch matchEvent = trade.getMatchEventRef().getObject();
        if (!matchEvent.isPrimeBrokerCoverEnabled()) {
            // Only handle customer match event here
            return false;
        }

        if (trade.getCoverTradeAttributeDescriptors().isEmpty()) {
            return false;
        }

        switch (aec) {
            case EVENT_ESP_TRADE_ACCEPT:
            case EVENT_ESP_TRADE_VERIFIED:
            case EVENT_ESP_TRADE_PRE_VERIFIED:
            case EVENT_ESP_TRADE_CREATED_PRE_VERIFIED:
            case EVENT_ESP_TRADE_CREATED_VERIFIED:
            case EVENT_ESP_TRADE_PENDING:
            case EVENT_ESP_TRADE_COMPLETED:
            case EVENT_ESP_TRADE_REJECTED:
            case EVENT_ESP_TRADE_CONFIRMED:
                return true;
            default:
                return false;
        }
    }

    private boolean shouldHandleMatchEvent(SingleLegOrderMatch matchEvent, ApplicationEventCodes aec)
    {
        if (!matchEvent.isPrimeBrokerCoverEnabled()) {
            // Only handle customer match event here
            return false;
        }

        if (matchEvent.getCoverMatchAttributeDescriptors().isEmpty()) {
            return false;
        }

        switch (aec) {
            case EVENT_ESP_ORDER_MATCH:
            case EVENT_ESP_TRADE_REQUEST_EXPIRE:
                return true;
            default:
                return false;
        }
    }

    private boolean shouldHandleOrderEvent(SingleLegOrder order, ApplicationEventCodes aec)
    {
        if (order.getCoveredOrderRequest() != null) {
            // Only handle customer order
            return false;
        }

        if (order.getCoverOrderAttributeDescriptors().isEmpty()) {
            return false;
        }

        switch (aec) {
            case EVENT_ESP_ORDER_CANCEL:
            case EVENT_ESP_ORDER_FILLED:
            case EVENT_ESP_ORDER_EXPIRE:
                return true;
            default:
                return false;
        }
    }

    private void handleOrderNotification(SingleLegOrder order, ApplicationEventCodes aec)
    {
        log.info("handleOrderNotification: Received notification for order=" + order.get_id() + ", ApplicationEvent=" + aec.name());

        SingleLegOrder coveredOrder = order;
        List<CoverOrderAttributeDescriptor> descriptorList = order.getCoverOrderAttributeDescriptors();
        for (CoverOrderAttributeDescriptor descriptor : descriptorList)
        {
            SingleLegOrder coverOrder = updateCoverOrder(coveredOrder, descriptor);
            coveredOrder = coverOrder;
        }
    }

    private void handleMatchEventNotification(SingleLegOrderMatch matchEvent, ApplicationEventCodes aec)
    {
        log.info("handleMatchEventNotification: Received notification for match=" + matchEvent.get_id() + ", ApplicationEvent=" + aec.name());

        SingleLegOrderMatch coveredMatchEvent = matchEvent;
        List<CoverMatchAttributeDescriptor> descriptorList = matchEvent.getCoverMatchAttributeDescriptors();
        List<ApplicationSpaceEvent> events = new ArrayList<ApplicationSpaceEvent> (descriptorList.size());
        for (CoverMatchAttributeDescriptor descriptor : descriptorList)
        {
            SingleLegOrderMatch coverMatchEvent = createCoverMatchEvent(coveredMatchEvent, descriptor, aec);
            events.add(isSpacesPersistenceService.createEvent(coverMatchEvent, aec));
            coveredMatchEvent = coverMatchEvent;
        }

        persistEvents(events, matchEvent.getCorrelationId(), matchEvent.isWarmUpObject(), aec.name());
    }

    private void handleTradeNotification( SingleLegTrade trade, ApplicationEventCodes aec )
    {
        log.info("handleTradeNotification : Received notification for Trade=" + trade.get_id() + ", ApplicationEvent=" + aec.name());

        SingleLegTrade coveredTrade = trade;
        List<CoverTradeAttributeDescriptor> descriptorList = trade.getCoverTradeAttributeDescriptors();
        List<ApplicationSpaceEvent> events = new ArrayList<ApplicationSpaceEvent> (descriptorList.size() * 1);

        for (CoverTradeAttributeDescriptor descriptor : descriptorList) {
            SingleLegTrade coverTrade = createCoverTrade(coveredTrade, trade, descriptor, aec);
            events.add(isSpacesPersistenceService.createEvent(coverTrade, aec));
            coveredTrade = coverTrade;
        }

        persistEvents(events, trade.getCorrelationId(), trade.isWarmUpObject(), aec.name());
    }

    private void persistEvents(List<ApplicationSpaceEvent> events, String correlationId, boolean isWarmUp, String event)
    {
        ErrorMessage errorMessage = isSpacesPersistenceService.persist(events, correlationId, isWarmUp, event);
        if (errorMessage != null) {
            log.error("Error Message while saving to database: " + errorMessage.getErrorCode());
        }
    }

    @Override
    public long getLongIdentifier() {
        return NotificationConfiguration.NOTIFICATION_HANDLER_COVER_TRADE.getLongIdentifier();
    }

    private SingleLegOrder updateCoverOrder(SingleLegOrder coveredOrder, CoverOrderAttributeDescriptor descriptor)
    {
        String namespace = descriptor.getLegalEntity().getNamespace().getShortName();
        SingleLegOrder coverOrder = CoverTradeNotificationUtil.getCachedOrderRequest(namespace, descriptor.getCoverOrderId());

        DealingModelUtil.copyCoverOrderAttributeDescriptor(descriptor, coverOrder);
        DealingModelUtil.doStateTransition(coverOrder, coveredOrder.getState().getName());
        ApplicationEventCodes aec = ApplicationEventCodes.EVENT_ESP_ORDER_STATE_UPDATE;
        switch (coverOrder.getState().getName()) {
            case RSCANCELLED:
                aec = ApplicationEventCodes.EVENT_ESP_ORDER_CANCEL;
                break;
            case RSEXECUTED:
                if (coverOrder.getOrderFilledAmount() < coverOrder.getOrderAmount()) {
                    aec = ApplicationEventCodes.EVENT_ESP_ORDER_CANCEL;
                }
                else {
                    aec = ApplicationEventCodes.EVENT_ESP_ORDER_FILLED;
                }
                break;
            case RSEXPIRED:
                aec = ApplicationEventCodes.EVENT_ESP_ORDER_EXPIRE;
                break;
            default:
                log.warn( "orderCompleted : Cover Order " + coverOrder.get_id() + " is in invalid final state " + coverOrder.getState().getName() );
                break;
        }

        coverOrder.getRequestLeg().setPendingSettledCurrencyAmount(0.0D); // Order reaches it's terminal state
        isSpacesPersistenceService.persist(coverOrder, aec, aec.name());

        return coverOrder;
    }

    private SingleLegOrderMatch createCoverMatchEvent(SingleLegOrderMatch coveredMatchEvent,
                                                      CoverMatchAttributeDescriptor descriptor,
                                                      ApplicationEventCodes aec)
    {
        SingleLegOrderMatch coverMatchEvent = null;
        String namespace = descriptor.getLegalEntity().getNamespace().getShortName();
        if (aec == ApplicationEventCodes.EVENT_ESP_ORDER_MATCH) {
            // New match event so create new cover match event here
            SingleLegOrder coverOrder = CoverTradeNotificationUtil.getCachedOrderRequest(namespace, descriptor.getCoverOrderId());
            coverMatchEvent = dealingModelFactory.newMatchEventPQ(coverOrder);
        }
        else {
            coverMatchEvent = CoverTradeNotificationUtil.getCachedMatchEvent(namespace, descriptor.getCoverMatchEventId());
        }

        PrimeBrokerUtil.copy(coveredMatchEvent, coverMatchEvent);
        coverMatchEvent.setVirtualServer(coveredMatchEvent.getVirtualServer());
        DealingModelUtil.copyCoverMatchAttributeDescriptor(descriptor, coverMatchEvent);
        return coverMatchEvent;
    }

    private SingleLegTrade createCoverTrade(SingleLegTrade coveredTrade,
                                            SingleLegTrade customerTrade,
                                            CoverTradeAttributeDescriptor descriptor,
                                            ApplicationEventCodes aec)
    {
        LegalEntity primeBrokerEntity = descriptor.getLegalEntity();
        String namespace = primeBrokerEntity.getNamespace().getShortName();
        SingleLegTrade coverTrade = null;

        switch (aec) {
            case EVENT_ESP_TRADE_ACCEPT:
            case EVENT_ESP_TRADE_CREATED_PRE_VERIFIED:
            case EVENT_ESP_TRADE_CREATED_VERIFIED:
                // New trade event, create cover trade
                SingleLegOrderMatch coverMatchEvent = CoverTradeNotificationUtil.getCachedMatchEvent(namespace, descriptor.getCoverMatchEventId());
                SingleLegOrder coverOrder = CoverTradeNotificationUtil.getCachedOrderRequest(namespace, descriptor.getCoverOrderId());
                coverMatchEvent.setOrderRequestRef(new DealingModelRef<OrderRequest>());
                coverMatchEvent.getOrderRequestRef().setObject(coverOrder);
                coverTrade = DealingModelFactory.getInstance().newTrade(coverMatchEvent);
                break;
            default:
                coverTrade = CoverTradeNotificationUtil.getCachedTrade(namespace, descriptor.getCoverTradeId());
        }

        coverTrade.setVirtualServer(coveredTrade.getVirtualServer());

        coverTrade.setCreditTradingParty(coveredTrade.getCreditTradingParty());
        coverTrade.setTradeDate(coveredTrade.getTradeDate());
        PrimeBrokerUtil.copy(coveredTrade, coverTrade);
        PrimeBrokerUtil.copy(coveredTrade.getState(), coverTrade.getState());

        coverTrade.setExecutionTime(coveredTrade.getExecutionTime());
        coverTrade.setConfirmationTime(coveredTrade.getConfirmationTime());
        PrimeBrokerUtil.copyTradeEventTimes(coveredTrade.getEventTimes(), coverTrade.getEventTimes());

        TradeLeg coverTradeLeg = coverTrade.getTradeLeg();
        TradeLeg coveredTradeLeg = coveredTrade.getTradeLeg();
        coverTradeLeg.setDealtAmount(coveredTradeLeg.getDealtAmount());

        DealingModelUtil.copyCoverTradeAttributeDescriptor(descriptor, coverTrade);
        coverTrade.setUser(primeBrokerEntity.getOrganization().getDefaultDealingUser());

        OriginatingTradeDescriptor originatingTradeDescriptor = new OriginatingTradeDescriptor();
        originatingTradeDescriptor.setTradeRef( new DealingModelRef<SingleLegTrade>() );
        coverTrade.setOriginatingTrade( originatingTradeDescriptor );
        DealingModelUtil.populateOriginatingTradeDescriptor(coveredTrade.getOriginatingTrade(), coverTrade.getOriginatingTrade());

        CoveredTradeDescriptor coveredTradeDescriptor = new CoveredTradeDescriptor();
        coveredTradeDescriptor.setTradeRef( new DealingModelRef<SingleLegTrade>() );
        coverTrade.setCoveredTrade( coveredTradeDescriptor );
        DealingModelUtil.populateCoveredTradeDescriptor(coveredTradeDescriptor, coveredTrade);
        DealingModelUtil.populateExternalReqTradeDescriptor(coverTrade.getExternalReqTrade(), customerTrade);

        DealingModelRef<SingleLegTrade> coverTradeRef = new DealingModelRef<SingleLegTrade>();
        coverTradeRef.setNamespace(coverTrade.getNamespace());
        coverTradeRef.setObject(coverTrade);
        coveredTrade.setCoverTradeRef(coverTradeRef);

        return coverTrade;
    }
}
