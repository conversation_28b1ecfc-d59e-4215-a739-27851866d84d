package com.integral.notifications.coverTrade;

import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegOrderMatch;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.persistence.spaces.PersistenceConstants;
import com.integral.spaces.Metaspaces;

/**
 * Created by tapan.vijay on 4/10/14.
 */
public class CoverTradeNotificationUtil
{
    private static final Log log = LogFactory.getLog(CoverTradeNotificationUtil.class);
    private static final int NUM_ATTEMPTS = 3;
    private static final com.integral.is.spaces.fx.persistence.DealingModelSpacesCacheManager spacesCacheManager =
            com.integral.is.spaces.fx.persistence.DealingModelSpacesCacheManager.getInstance();

    public static Object getCachedObject( String metaspace, String space, String namespace, String id, Class cls) {

        Object obj = spacesCacheManager.getCachedObject(metaspace, namespace, space, id, cls);
        if (obj == null) {
            for (int i = 1; i <= NUM_ATTEMPTS; i++)
            {
                Metaspaces.getInstance().refreshClusterMetadata();
                try {
                    log.info("Record from database is null, reattempting: " + i);
                    Thread.sleep(i * 1000);
                    obj = spacesCacheManager.getCachedObject(metaspace, namespace, space, id, cls);
                    if (obj != null) {
                        break;
                    }
                } catch (InterruptedException ie) {
                    log.error("DealingModelPreProcessor.preProcess() interrupted while waiting for quering :" + metaspace + ":" + namespace + ":" + space + ":" + id + ":" + cls.getName());
                }
            }
        }

        return obj;
    }

    public static SingleLegOrderMatch getCachedMatchEvent(String namespace, String id) {
        Object obj = getCachedObject(PersistenceServiceFactory.getTradingMetaspaceName(), PersistenceConstants.MATCHEVENT,
            namespace, id, SingleLegOrderMatch.class);
        return obj != null ? (SingleLegOrderMatch) obj : null;
    }

    public static SingleLegOrder getCachedOrderRequest(String namespace, String id) {
        Object obj = getCachedObject(PersistenceServiceFactory.getTradingMetaspaceName(), PersistenceConstants.ORDERREQUEST,
            namespace, id, SingleLegOrder.class);
        return obj != null ? (SingleLegOrder ) obj : null;
    }

    public static SingleLegTrade getCachedTrade(String namespace, String id) {
        Object obj = getCachedObject(PersistenceServiceFactory.getTradingMetaspaceName(), PersistenceConstants.TRADE,
            namespace, id, SingleLegTrade.class);
        return obj != null ? (SingleLegTrade) obj : null;
    }
}
