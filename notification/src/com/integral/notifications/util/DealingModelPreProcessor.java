package com.integral.notifications.util;

import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.fx.FXRateBasis;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.spaces.fx.persistence.DealingModelTradeHelper;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.*;
import com.integral.spaces.Metaspaces;
import com.integral.spaces.cache.SpacesCacheManager;
import com.integral.spaces.notification.Notification;
import com.integral.spaces.notification.NotificationPreProcessor;
import com.integral.spaces.serialize.SerializationView;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;

import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Created by IntelliJ IDEA.
 * User: bhanotp
 * Date: 8/10/12
 */
public class DealingModelPreProcessor implements NotificationPreProcessor {
    private ConcurrentMap<String, Class> allDealingModelClasses;
    private ConcurrentMap<String, Class<? extends SerializationView>> viewClasses;
    private Log logger = LogFactory.getLog(DealingModelPreProcessor.class);
    private SpacesCacheManager<DealingModel> spacesCacheManager;
    private final int NUM_ATTEMPTS = 3;

    public DealingModelPreProcessor() {
        allDealingModelClasses = new ConcurrentHashMap<String, Class>();
        viewClasses = new ConcurrentHashMap<String, Class<? extends SerializationView>>();
        spacesCacheManager = com.integral.is.spaces.fx.persistence.DealingModelSpacesCacheManager.getInstance();
    }

    public void preProcess(Notification notification) {
        if (shouldHandle(notification)) {
            /**
             * Here we will make an attempt to lookup the object cache to see if this object exists in an instance of SpacesCacheManager.
             * If not it will add it onto it.
             */
            if (logger.isDebugEnabled())
                logger.debug("DMPPC.preProcess=> " + notification);
            Class cls = allDealingModelClasses.get(notification.getDataObjectType().getName());
//            Class<? extends SerializationView> viewClass = viewClasses.get(notification.getAllPropsMap().get(NotificationConstants.NOTIFICATION_OBJECT_SERIALIZATION_VIEW));
            long versionId = notification.getObjectVersion();

            Object obj = spacesCacheManager.getCachedObject(notification.getMetaspace(), notification.getNamespaceName(), notification.getSpace(), notification.getObjectId(), cls, versionId);

            /**
             * Check if the object was retrieved, else retry incrementally unless an object is retrieved.
             */
            if (obj == null) {
                for (int i = 1; i <= NUM_ATTEMPTS; i++) {
                    Metaspaces.getInstance().refreshClusterMetadata();
                    try {
                        /**
                         * Sleeep for the a few ms
                         */
                        logger.info("Record from database is null, reattempting: "+i);
                        Thread.sleep(i * 1000);
                        obj = spacesCacheManager.getCachedObject(notification.getMetaspace(), notification.getNamespaceName(), notification.getSpace(), notification.getObjectId(), cls,versionId);
                        if (obj != null) {
                            break;
                        }
                    } catch (InterruptedException ie) {
                        logger.error("DealingModelPreProcessor.preProcess() interrupted while waiting for quering :" + notification.getMetaspace() + ":" + notification.getNamespaceName() + ":" + notification.getSpace() + ":" + notification.getObjectId() + ":" + cls.getName());
                    }
                }
            }

            if (obj instanceof DealingModel) {
                DealingModel dealingModel = (DealingModel) obj;
                if (dealingModel.getVersionId() < versionId) {
                    logger.error("Version mismatch, version in Notification: " + versionId + " DB version: " + (dealingModel.getVersionId() == 0 ? "BLANK": dealingModel.getVersionId()));
                } else if (dealingModel.getVersionId() != versionId) {
                    logger.info("Version mismatch, version in Notification: " + versionId + " DB version: " + dealingModel.getVersionId());
                }
            }
            DealingModelTradeHelper.updateWithTransients(obj);
            boolean forwardTenor = isForwardTenor(obj);
            if(forwardTenor){
                obj = DealingModelTradeHelper.queryObject(notification.getMetaspace(), notification.getNamespaceName(), notification.getSpace(), notification.getObjectId(), cls, versionId);
                DealingModelTradeHelper.updateWithTransients(obj, true);
                modifyForwardTradeDetails(obj);
            }

            if (logger.isDebugEnabled())
                logger.debug("DMPPC.preProcess==> from Cache: " + obj);
            if (logger.isDebugEnabled())
                logger.debug("DMPPC.preProcess==> after merge: " + obj);
            notification.setCompleteObject(obj);
        }
    }

    private boolean isForwardTenor(Object obj){
        if(obj instanceof SingleLegOrder && isForwardCcyPair(((SingleLegOrder) obj).getCurrencyPair())){
            return true;
        }else if(obj instanceof SingleLegOrderMatch && isForwardCcyPair(((SingleLegOrderMatch) obj).getCurrencyPair())){
            return true;
        }else if(obj instanceof SingleLegTrade && isForwardCcyPair(((SingleLegTrade) obj).getCurrencyPair())){
            return true;
        }
        return false;
    }

    private void modifyForwardTradeDetails(Object object){
        if(object instanceof SingleLegOrder){
            modifyForwardTradeDetails((SingleLegOrder)object);
        }else if(object instanceof SingleLegOrderMatch){
            modifyForwardTradeDetails((SingleLegOrderMatch)object);
        }else if(object instanceof SingleLegTrade){
            modifyForwardTradeDetails((SingleLegTrade)object);
        }else {
            return;
        }
    }

    private void modifyForwardTradeDetails(SingleLegOrder order)
    {
        String tenor = null;
        IdcDate brokenDate = null;
        CurrencyPair ccyPair = order.getCurrencyPair ();
        if ( ccyPair.getVariableCurrency ().getSettlementTenor () != null )
        {
            tenor = getTenor ( ccyPair );
        }
        if ( ccyPair.isVirtual () && ccyPair.getVariableCurrency ().getBrokenDate () != null )
        {
            brokenDate = ccyPair.getVariableCurrency ().getBrokenDate ();
        }
        CurrencyPair underlyingCurrencyPair = getUnderlyingCcyPair(order.getCurrencyPair());
        OrderRequest.RequestLeg leg = order.getRequestLeg();
        boolean termCcyOrder = order.getCurrencyPair().getVariableCurrency().isSameAs(order.getDealtCurrency());
        FXRateBasis fxRateBasis = order.getFxRateBasis();
        if( ccyPair.isNonSpotSettlementType () )
        {
            fxRateBasis = fxRateBasis.getFXRateConvention().getFXRateBasis(underlyingCurrencyPair);
            order.setFxRateBasis(fxRateBasis);
        }
        boolean isNDF = fxRateBasis != null && fxRateBasis.isNonDeliverable();
        if ( ISCommonConstants.DELIVERABLE_NDF_PARAMETER_DEFAULT != order.getDeliverableNDFParameter () )
        {
            isNDF = ISCommonConstants.DELIVERABLE_NDF_PARAMETER_NDF == order.getDeliverableNDFParameter ();
        }

        if(termCcyOrder){
            order.setDealtCurrency(underlyingCurrencyPair.getVariableCurrency());
        }
        order.setCurrencyPair(underlyingCurrencyPair);
        order.setProductType(isNDF ? ProductType.FXNDF : ProductType.FXFWD);
        leg.setTenor(tenor);
        if ( brokenDate != null )
        {
            leg.setValueDate ( brokenDate.asJdkDate ().getTime () );
        }
    }

    private void modifyForwardTradeDetails(SingleLegOrderMatch matchEvent){
        matchEvent.getMatchEventLeg().setTenor(getTenor(matchEvent.getCurrencyPair()));
        modifyForwardTradeDetails(matchEvent.getOrderRequest());
    }

    private void modifyForwardTradeDetails(SingleLegTrade trade){
        TradeLeg tradeLeg = trade.getTradeLeg();
        String tenor = null;
        IdcDate brokenDate = null;
        CurrencyPair ccyPair = trade.getCurrencyPair();
        if ( ccyPair.getVariableCurrency ().getSettlementTenor () != null )
        {
            tenor = getTenor ( ccyPair );
        }
        if ( ccyPair.isVirtual () && ccyPair.getVariableCurrency ().getBrokenDate () != null )
        {
            brokenDate = ccyPair.getVariableCurrency ().getBrokenDate ();
        }
        tradeLeg.setTenor( tenor );
        CurrencyPair underlyingCurrencyPair = getUnderlyingCcyPair(trade.getCurrencyPair());
        FXRateBasis fxRateBasis = trade.getFxRateBasis();
        if(fxRateBasis == null && trade.getOrderRequest() != null){
            fxRateBasis = trade.getOrderRequest().getFxRateBasis();
        }
        if(fxRateBasis == null){
            logger.error(".modifyForwardTradeDetails: FXRateBasis not available. Unable to update Forward Instrument details");
        }else {
            fxRateBasis = fxRateBasis.getFXRateConvention().getFXRateBasis(underlyingCurrencyPair);
            trade.setFxRateBasis(fxRateBasis);
        }
        boolean isNDF = fxRateBasis != null && fxRateBasis.isNonDeliverable();
        trade.setClassification(isNDF ? ISUtilImpl.TRD_NDF_CLSF.getShortName() : ISUtilImpl.TRD_OUTRIGHT_CLSF.getShortName());
        tradeLeg.setClassification(ISUtilImpl.TRDLEG_FWD_CLSF.getShortName());
        if(isNDF){
            IdcDate valueDate = DateTimeFactory.newDate(new Date(tradeLeg.getValueDate()));
            long fixingDateLong = tradeLeg.getFixingDate();
            IdcDate fixingDate;
            if(fixingDateLong <= 0L)
            {
                fixingDate = fxRateBasis.getFixingDate(valueDate);
            }
            else
            {
                fixingDate = DateTimeFactory.newDate(new Date(fixingDateLong));
            }
            tradeLeg.setFixingDate(fixingDate.asJdkDate().getTime());
        }
        if(trade.getTermCurrency().isSameAs(trade.getDealtCurrency())){
            trade.setDealtCurrency(underlyingCurrencyPair.getVariableCurrency());
        }
        trade.setCurrencyPair(underlyingCurrencyPair);

        modifyForwardTradeDetails(trade.getMatchEvent());
        if ( brokenDate != null )
        {
            tradeLeg.setValueDate ( brokenDate.asJdkDate ().getTime () );
        }
    }

    private CurrencyPair getUnderlyingCcyPair(CurrencyPair currencyPair){
        return CurrencyFactory.getOriginalCurrencyPair(currencyPair);
    }

    private boolean isForwardCcyPair(CurrencyPair currencyPair){
        return currencyPair.getVariableCurrency().isForwardSettlementType();
    }

    private String getTenor(CurrencyPair currencyPair){
        return currencyPair.getVariableCurrency().getSettlementTenor() != null ? currencyPair.getVariableCurrency().getSettlementTenor().getName() : null;
    }

    private boolean shouldHandle(Notification notification) {
        if (allDealingModelClasses.containsKey(notification.getDataObjectType().getName())) {
            addSerializationView(notification);
            return true;
        } else {
            /**
             * If the string has not already been checked, check to see if any of the parent class was DealingModel
             */
            try {
                Class cls = notification.getDataObjectType();
                if (DealingModel.class.isAssignableFrom(cls)) {
                    allDealingModelClasses.putIfAbsent(notification.getDataObjectType().getName(), cls);
                    addSerializationView(notification);
                    return true;
                }

            } catch (Exception e) {
                if (logger.isDebugEnabled()) {
                    logger.debug("DealingModelPreProcessor=> not processing notification" + notification);
                }
            }
            return false;
        }
    }

    private void addSerializationView(Notification notification) {
        String strClass = notification.getSerializationView().getName();
        if (strClass == null) {
            logger.debug("NO Serialization view Class found for record" + notification);
        }
        if (viewClasses.containsKey(strClass)) {
            return;
        }
        if (strClass != null) {
            try {
                Class viewClass = notification.getSerializationView();
                viewClasses.putIfAbsent(strClass, viewClass);
            } catch (Exception ex) {
                logger.error("Unable to find a serialization view for the ObjectSerializationViewclass: " + strClass, ex);
            }
        }
    }
}
