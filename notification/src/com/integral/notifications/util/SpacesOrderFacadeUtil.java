package com.integral.notifications.util;

import com.integral.notifications.chiefdealer.dealorder.CDViewManager;

/**
 * Created with IntelliJ IDEA.
 * User: shahr
 * Date: 1/29/14
 * Time: 11:54 AM
 */
public class SpacesOrderFacadeUtil {

    /**
     *  generate _id of a FXSingleLegOrder
     * @param orderId     Associated OrderID of order
     * @param namespace   Associated namespace of order
     * @return
     */
    public static String get_id(String orderId, String namespace ,CDViewManager.CDView.Type type){
        StringBuilder sb = new StringBuilder(orderId).append("_").append(namespace);
        switch (type){
            case TAKER:
                sb.append("_").append("T");
                break;
            case MAKER:
                sb.append("_").append("M");
                break;
        }
        return sb.toString();
    }
}
