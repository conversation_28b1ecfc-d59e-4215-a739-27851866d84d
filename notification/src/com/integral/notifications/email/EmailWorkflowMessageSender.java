package com.integral.notifications.email;

import com.integral.exception.IdcException;
import com.integral.is.common.email.EmailConstants;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mail.MailService;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.EntityService;
import com.integral.persistence.EntityServiceFactory;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * Email message sender.
 *
 * <AUTHOR>
 */
public class EmailWorkflowMessageSender {
    private final static Log LOG = LogFactory.getLog(EmailWorkflowMessageSender.class);
    private final static EntityService ENTITY_SERVICE = EntityServiceFactory.getEntityService();
    private final static Map<String, String> EMAIL_SENDER_PROPERTIES = new HashMap<String, String>();

    public EmailWorkflowMessageSender() {
    }

    public EmailWorkflowMessageSender(String jmsTopicSelector) {
        EMAIL_SENDER_PROPERTIES.put("selector", jmsTopicSelector);
    }

    /**
     * Sends the email message.
     *
     * @param emailWflMsg email workflow message
     * @throws Exception
     */
    protected void sendEmail(WorkflowMessage emailWflMsg)
    {

        Map emailContents = (Map)emailWflMsg.getParameterValue(EmailConstants.TRADE_MAIL_CONTENTS);
        if( emailContents != null ) {
            LOG.info("Send email for orderId=" + emailContents.get(EmailConstants.TRADE_ORDER_ID) + " ,tradeId=" + emailContents.get(EmailConstants.TRADE_TRANSACTION_ID));
            if (LOG.isDebugEnabled())
                LOG.debug("Send email for " + emailWflMsgtoString(emailWflMsg));
        }
        try {
        	MailService.getInstance().sendEmail(emailWflMsg, EMAIL_SENDER_PROPERTIES);
        } catch (IdcException e) {
            LOG.error("Email workflow message: " + emailWflMsgtoString(emailWflMsg), e);
        }
    }

    private String emailWflMsgtoString(WorkflowMessage emailWflMsg) {
        StringBuffer sb = new StringBuffer();
        sb.append(EmailConstants.TRADE_EVENT_TYPE).append("=").append(emailWflMsg.getParameterValue(EmailConstants.TRADE_EVENT_TYPE)).append(" ");
        sb.append(EmailConstants.TRADE_MAIL_CONTENTS).append("={ ");
        Map values = (Map)emailWflMsg.getParameterValue(EmailConstants.TRADE_MAIL_CONTENTS);
        Iterator keyItr = values.keySet().iterator();
        while (keyItr.hasNext()) {
            String key = (String)keyItr.next();
            sb.append(key).append("=").append(values.get(key)).append(" ");
        }
        sb.append("} ");
        sb.append(EmailConstants.TRADE_CLIENT_MAIL_ID).append("=").append(emailWflMsg.getParameterValue(EmailConstants.TRADE_CLIENT_MAIL_ID));
        return sb.toString();
    }
}
