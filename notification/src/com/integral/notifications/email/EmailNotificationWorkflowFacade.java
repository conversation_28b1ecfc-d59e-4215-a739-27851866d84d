package com.integral.notifications.email;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.email.EmailConstants;
import com.integral.is.common.email.ISEmailUtil;
import com.integral.is.common.email.TradeEmailMBean;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.management.trade.TradeStatus;
import com.integral.message.MessageEvent;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.*;
import com.integral.persistence.ExternalSystemId;
import com.integral.spaces.notification.Notification;
import com.integral.user.Organization;
import com.integral.workflow.WorkflowState;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

import static com.integral.is.common.ApplicationEventCodes.EVENT_ESP_TRADE_PENDING;

/**
 * Package specific email notification message facade. The main entry point to this facade is
 * createWorkflowMessage().
 *
 * <AUTHOR>
 */
class EmailNotificationWorkflowFacade {
    private static final Log LOG = LogFactory.getLog(EmailNotificationWorkflowFacade.class);
    private static final TradeEmailMBean TRADE_EMAIL_MBEAN = ISFactory.getInstance().getTradeEmailMBean();
    private static final ISMBean ISMBEAN = ISFactory.getInstance().getISMBean();

    private static final ThreadLocal<EmailTradeWorkflowHelper> EMAIL_TRADE_WORKFLOW_HELPER_THREAD_LOCAL = new ThreadLocal<EmailTradeWorkflowHelper>() {
        @Override
        protected EmailTradeWorkflowHelper initialValue() {
            return new EmailTradeWorkflowHelper(TRADE_EMAIL_MBEAN);
        }
    };

    private static EmailWorkflowMessageSender emailWorkflowMessageSender;

    // Suppress default constructor for noninstantiability
    private EmailNotificationWorkflowFacade() {
        throw new InstantiationError();           // preventing instantiation from within
    }

    static void setEmailWorkflowMessageSender(EmailWorkflowMessageSender emailWkflMsgSender) {
        emailWorkflowMessageSender = emailWkflMsgSender;
    }

    /**
     * Given the Notification message creates and sends the EmailMessage(s) based on the SingleLegTrade.
     *
     * @param notification Notification message received
     * @param singleLegTrade SingleLegTrade corresponding to the received Notification
     * @throws Exception
     */
    static void sendWorkflowMessage(final Notification notification, final SingleLegTrade singleLegTrade) throws Exception {
        if (singleLegTrade.getMatchEvent().isNettingRequired()) {
            LOG.info("ENWF.sendWorkflowMessage(). Skipping Email Notification for trade as netting required was set on match event. TrdId:" + singleLegTrade.get_id());
            return;
        }
        ApplicationEventCodes appEventCode = getApplicationEventCodes(notification);
        switch (appEventCode) {
            case EVENT_ESP_TRADE_ACCEPT:
                // matchevent is done, after this the trade may get rejected, verified or ...
                sendAcceptedTradeWorkflowMsg(singleLegTrade);
                break;

            case EVENT_ESP_TRADE_PENDING:
                sendPendingTradeWorkflowMsg(singleLegTrade);
                break;

            case EVENT_ESP_TRADE_VERIFIED:
            case EVENT_ESP_TRADE_POST_RATE_VERIFIED:
            case EVENT_ESP_TRADE_CREATED_VERIFIED:
            case EVENT_ESP_LIFT_CREATE_MAKER_TRADE:
            case EVENT_DO_CREATE_MAKER_TRADE:
            case EVENT_ESP_TRADE_NET:
            case EVENT_ESP_MAKER_TRADE_POST_RATE_VERIFIED:
                sendVerifiedTradeWorkflowMsg(singleLegTrade);
                break;

            case EVENT_ESP_TRADE_REJECTED:
                sendRejectedTradeWorkflowMsg(singleLegTrade);
                break;

            case EVENT_ESP_TRADE_PRE_VERIFIED:
            case EVENT_ESP_TRADE_CREATED_PRE_VERIFIED:
            case EVENT_ESP_TRADE_FAILED:
            case EVENT_ESP_TRADE_CANCEL:
            case EVENT_ESP_NET_TRADE_CANCEL:
            case EVENT_ESP_TRADE_COMPLETED:
            case EVENT_ESP_TRADE_CONFIRMED:
            case EVENT_ESP_TRADE_REQUEST_EXPIRE:
            case EVENT_ESP_TRADE_STP:
            case EVENT_ESP_TRADE_NETTED:
            case EVENT_ESP_TRADE_AMEND:
            case EVENT_ESP_NET_TRADE_AMEND:
            case EVENT_ESP_MS_LOOKUP:
            case EVENT_ESP_TRADE_ALLOCATION_COMPLETED:
            case EVENT_ESP_POST_TRADE_UPDATE:
                break;

            case EVENT_ESP_TRADE_DONTKNOW:
                ISEmailUtil.getInstance().sendOrderTimeoutTradeNotification( singleLegTrade, true );
                break;
            case EVENT_ESP_TRADE_PRE_RATE_VERIFIED:
            case EVENT_ESP_MAKER_TRADE_PRE_RATE_VERIFIED:
                break;
            default:
                throw new Exception("Invalid appEventCode " + appEventCode.toString());
        }
    }

    private static void sendPendingTradeWorkflowMsg(final SingleLegTrade singleLegTrade) throws Exception {
        SingleLegOrder singleLegOrder = singleLegTrade.getOrderRequest();
        if (singleLegOrder == null)
            throw new NullPointerException("singleLegOrder");

        Organization fiOrg = singleLegOrder.getOrganization();
        if (fiOrg == null)
            throw new NullPointerException("fiOrg");

        SingleLegOrderMatch matchEvent = singleLegTrade.getMatchEvent();
        if (matchEvent == null)
            throw  new NullPointerException("matchEvent");

        Organization tradingPartyOrg = getTradingPartyOrg(singleLegTrade);
        String emailId = getExtSysEmailId(tradingPartyOrg);
        if (emailId != null) {
            sendPendingTradeWorkflowMsgToTradingParty(singleLegTrade, singleLegOrder, matchEvent, tradingPartyOrg, emailId);
        }

        emailId = getExtSysEmailId(fiOrg);
        if (emailId != null) {
            sendPendingTradeWorkflowMsgToFi(singleLegTrade, singleLegOrder, matchEvent, fiOrg, tradingPartyOrg, emailId);
        }
    }

    private static void sendPendingTradeWorkflowMsgToTradingParty(final SingleLegTrade singleLegTrade, final SingleLegOrder singleLegOrder, final SingleLegOrderMatch matchEvent, final Organization tradingPartyOrg, final String emailId) throws Exception {
        if (matchEvent.isPrimeBrokerCoverEnabled()) {
            if (LOG.isDebugEnabled()) {
                LOG.debug("Not sending email notification to PB for FI-PB pending covered trade " + singleLegTrade.get_id());
            }
            return;
        }

        Map values = new HashMap();
        values.put(EmailConstants.TRADE_TRADE_STATE, TradeStatus.PENDING);

        WorkflowMessage emailWflMsg = MessageFactory.newWorkflowMessage();
        emailWflMsg.setParameterValue(ISConstantsC.TRADE_STATE, TradeStatus.PENDING);
        emailWflMsg.setParameterValue(EmailConstants.TRADE_EVENT_TYPE, EmailConstants.TRADE_PENDING);
        emailWflMsg.setParameterValue(ISConstantsC.REQUEST_TRANSACTION_ID, singleLegOrder.get_id());

        sendTradeWorkflowMsgToTradingParty(singleLegTrade, tradingPartyOrg, emailWflMsg, values, ISConstantsC.MSG_EVT_PENDING, true, emailId);
    }

    private static void sendPendingTradeWorkflowMsgToFi(final SingleLegTrade singleLegTrade, final SingleLegOrder singleLegOrder, final SingleLegOrderMatch matchEvent, final Organization fiOrg, final Organization tradingPartyOrg, final String emailId) throws Exception {
        if (matchEvent.isPrimeBrokerInitiated()) {
            // definitely a cover trade
            if (LOG.isDebugEnabled()) {
                LOG.debug("Not sending email notification to PB for PB-LP pending cover trade " + singleLegTrade.get_id());
            }
            return;
        }

        EmailTradeWorkflowHelper helper = EMAIL_TRADE_WORKFLOW_HELPER_THREAD_LOCAL.get();
        helper.populate(singleLegTrade, singleLegOrder, fiOrg,EVENT_ESP_TRADE_PENDING);

        Map values = new HashMap();
        values.put(EmailConstants.TRADE_PROVIDER_NAME, helper.getProviderName());
        values.put(EmailConstants.TRADE_EXECUTION_DATE, helper.getTradeExecutionDate());
        values.put(EmailConstants.TRADE_TRADE_DATE, helper.getTradeDate());
        values.put(EmailConstants.TRADE_EXECUTION_TIME, helper.getTradeExecutionTime());
        values.put(EmailConstants.TRADE_USER, helper.getTradeUserName());
        values.put(EmailConstants.TRADE_DEALT_AMOUNT, helper.getTradeDealtAmount());
        values.put(EmailConstants.TRADE_CCY_PAIR, helper.getCcyPairName());
        values.put(EmailConstants.TRADE_RATE, helper.getTradeRate());
        values.put(EmailConstants.TRADE_BUY_CCY, helper.getBuyCcyName());
        values.put(EmailConstants.TRADE_DEALT_CCY, helper.getDealtCcyName());
        values.put(EmailConstants.TRADE_SETTLED_CCY, helper.getSettledCcyName());
        values.put(EmailConstants.TRADE_TYPE, helper.getTradeType());
        values.put(EmailConstants.TRADE_LEGAL_ENTITY, helper.getTradeLegalEntity());
        values.put(EmailConstants.TRADE_SELL_CCY, helper.getSellCcyName());
        values.put(EmailConstants.TRADE_BUY_SELL, helper.getFiBuySellMode());
        values.put(EmailConstants.TRADE_BUY_AMT, helper.getTradeBuyAmount());
        values.put(EmailConstants.TRADE_SELL_AMT, helper.getTradeSellAmount());
        values.put(EmailConstants.TRADE_VALUE_DATE, helper.getTradeValueDate());
        populateNDFDetails(values, helper);
        values.put(EmailConstants.TRADE_TRANSACTION_ID, singleLegTrade.get_id());
        values.put(EmailConstants.TRADE_ORGANIZATION, helper.getTradeOrganization());
        values.put(EmailConstants.TRADE_ORDER_ID, helper.getOrderId());
        values.put(EmailConstants.TRADE_TRADE_STATE, TradeStatus.PENDING);
        values.put(EmailConstants.EMAIL_BRAND_NAME, helper.getEmailBrandName());
        values.put(EmailConstants.EMAIL_SUPPORT_MSG, helper.getEmailSupportMsg());
        values.put(EmailConstants.EMAIL_COPYRIGHT, helper.getEmailCopyRight());
        if(helper.isNonSpotTrade()){
            values.put(EmailConstants.TRADE_SPOT_RATE, "");
            values.put(EmailConstants.FORWARD_POINTS, "");
        }

        WorkflowMessage emailWflMsg = MessageFactory.newWorkflowMessage();
        emailWflMsg.setTopic(ISConstantsC.MSG_TOPIC_LPTradeMailMessage);
        emailWflMsg.setEvent(ISConstantsC.MSG_EVT_CREATE);
        emailWflMsg.setStatus(MessageStatus.SUCCESS);
        emailWflMsg.setParameterValue(ISConstantsC.REQUEST_TRANSACTION_ID, singleLegOrder.get_id());
        emailWflMsg.setParameterValue(ISConstantsC.TRADE_STATE, TradeStatus.PENDING);
        emailWflMsg.setParameterValue(EmailConstants.TRADE_EVENT_TYPE, EmailConstants.TRADE_PENDING_FOR_USER);
        emailWflMsg.setParameterValue(EmailConstants.TRADE_MAIL_CONTENTS, values);

        helper.setBrandSpecificParameters(emailWflMsg);

        emailWflMsg.setParameterValue(EmailConstants.EmailIDForDelayedVerification, emailId);

        ISEmailUtil.getInstance().addLPContactInformation(tradingPartyOrg, values);

        emailWorkflowMessageSender.sendEmail(emailWflMsg);
    }

    private static void sendAcceptedTradeWorkflowMsg(final SingleLegTrade singleLegTrade) throws Exception {
        SingleLegOrderMatch matchEvent = singleLegTrade.getMatchEvent();
        if (matchEvent == null)
            throw  new NullPointerException("matchEvent");

        if (matchEvent.isPrimeBrokerInitiated()) {
            // definitely a cover trade, no need to check
            if (LOG.isDebugEnabled()) {
                LOG.debug("Not sending email notification to PB for PB-LP accepted cover trade " + singleLegTrade.get_id());
            }
            return;
        }

        SingleLegOrder singleLegOrder = singleLegTrade.getOrderRequest();
        if (singleLegOrder == null)
            throw new NullPointerException("singleLegOrder");

        Organization fiOrg = singleLegOrder.getOrganization();
        if (fiOrg == null)
            throw new NullPointerException("fiOrg");

        String fiOrgName = fiOrg.getShortName();
        if (TRADE_EMAIL_MBEAN.isRequestEmailSuppressedForFI(fiOrgName)) {
            if (TRADE_EMAIL_MBEAN.getSuppressTradeEmailForFIOrg(fiOrgName)) {
                if (LOG.isDebugEnabled()) {
                    LOG.debug("Not sending email notification for accepted trade " + singleLegTrade.get_id() + " as " + TRADE_EMAIL_MBEAN.IDC_SUPPRESS_TRADE_EMAIL_REQUEST + " = true for " + fiOrgName + " organization");
                }
                return;
            }
        } else if (TRADE_EMAIL_MBEAN.getSuppressTradeEmailDefault()) {
            if (LOG.isDebugEnabled()) {
                LOG.debug("Not sending email notification for accepted trade " + singleLegTrade.get_id() + " as " + TRADE_EMAIL_MBEAN.IDC_SUPPRESS_TRADE_EMAIL_REQUEST + " = true");
            }
            return;
        }

        EmailTradeWorkflowHelper helper = EMAIL_TRADE_WORKFLOW_HELPER_THREAD_LOCAL.get();
        helper.populate(singleLegTrade, singleLegOrder, fiOrg, ApplicationEventCodes.EVENT_ESP_TRADE_ACCEPT);

        Map values = new HashMap();
        values.put(EmailConstants.TRADE_ORDER_ID, helper.getOrderId());
        values.put(EmailConstants.TRADE_TRANSACTION_ID, singleLegTrade.get_id());
        values.put(EmailConstants.TRADE_ORGANIZATION, helper.getTradeOrganization());
        values.put(EmailConstants.TRADE_LEGAL_ENTITY, helper.getTradeLegalEntity());
        values.put(EmailConstants.TRADE_USER, helper.getTradeUserName());
        values.put(EmailConstants.TRADE_TRADE_DATE, helper.getTradeDate());
        values.put(EmailConstants.TRADE_VALUE_DATE, helper.getTradeValueDate());
        populateNDFDetails(values, helper);
        values.put(EmailConstants.TRADE_EXECUTION_DATE, helper.getTradeExecutionDate());
        values.put(EmailConstants.TRADE_TYPE, helper.getTradeType());
        values.put(EmailConstants.TRADE_CCY_PAIR, helper.getCcyPairName());
        values.put(EmailConstants.TRADE_BUY_CCY, helper.getBuyCcyName());
        values.put(EmailConstants.TRADE_BUY_AMT, helper.getTradeBuyAmount());
        values.put(EmailConstants.TRADE_SELL_AMT, helper.getTradeSellAmount());
        values.put(EmailConstants.TRADE_DEALT_CCY, helper.getDealtCcyName());
        values.put(EmailConstants.TRADE_SETTLED_CCY, helper.getSettledCcyName());
        values.put(EmailConstants.TRADE_SELL_CCY, helper.getSellCcyName());
        values.put(EmailConstants.TRADE_DEALT_AMOUNT, helper.getTradeDealtAmount());
        values.put(EmailConstants.TRADE_RATE, helper.getAcceptanceRate());
        values.put(EmailConstants.TRADE_PROVIDER_NAME, helper.getProviderName());
        values.put(EmailConstants.TRADE_BUY_SELL, helper.getFiBuySellMode());
        values.put(EmailConstants.EMAIL_BRAND_NAME, helper.getEmailBrandName());
        values.put(EmailConstants.EMAIL_SUPPORT_MSG, helper.getEmailSupportMsg());
        values.put(EmailConstants.EMAIL_COPYRIGHT, helper.getEmailCopyRight());
        if(helper.isNonSpotTrade()){
            values.put(EmailConstants.TRADE_SPOT_RATE, "");
            values.put(EmailConstants.FORWARD_POINTS, "");
        }

        WorkflowMessage emailWflMsg = MessageFactory.newWorkflowMessage();
        emailWflMsg.setTopic(ISConstantsC.MSG_TOPIC_TRADE);
        emailWflMsg.setEvent(ISConstantsC.MSG_EVT_CREATE);
        emailWflMsg.setStatus(MessageStatus.SUCCESS);
        emailWflMsg.setParameterValue(EmailConstants.TRADE_EVENT_TYPE, EmailConstants.TRADE_PRICE_TAKING);
        emailWflMsg.setParameterValue(EmailConstants.TRADE_MAIL_CONTENTS, values);

        setRestingOrderFlags(singleLegTrade,emailWflMsg);

        helper.setBrandSpecificParameters(emailWflMsg);
        helper.setTradeClientEmailIdForOrg(emailWflMsg);
        helper.setTradeClientEmailIdForUser(emailWflMsg, EmailConstants.TRADE_PRICE_TAKING);
        emailWorkflowMessageSender.sendEmail(emailWflMsg);
    }

    private static void sendVerifiedTradeWorkflowMsg(final SingleLegTrade singleLegTrade) throws Exception {
        if (singleLegTrade.isManualInterventionDone()) {
            Organization tradingPartyOrg = getTradingPartyOrg(singleLegTrade);
            String emailId = getExtSysEmailId(tradingPartyOrg);
            if (emailId != null) {
                sendVerifiedTradeWorkflowMsgToTradingParty(singleLegTrade, tradingPartyOrg, emailId);
            }
        }

        sendVerifiedTradeWorkflowMsgToFi(singleLegTrade);
    }

    private static void sendVerifiedTradeWorkflowMsgToTradingParty(final SingleLegTrade singleLegTrade, final Organization tradingPartyOrg, String emailId) throws Exception {
        Map values = new HashMap();
        WorkflowMessage emailWflMsg = MessageFactory.newWorkflowMessage();
        emailWflMsg.setParameterValue(EmailConstants.TRADE_EVENT_TYPE, EmailConstants.MANUAL_TRADE_VERIFIED);
        sendTradeWorkflowMsgToTradingParty(singleLegTrade, tradingPartyOrg, emailWflMsg, values, ISConstantsC.MSG_EVT_CREATE, false, emailId);
    }

    private static void sendVerifiedTradeWorkflowMsgToFi(final SingleLegTrade singleLegTrade) throws Exception {
        EmailTradeWorkflowHelper helper = EMAIL_TRADE_WORKFLOW_HELPER_THREAD_LOCAL.get();
        helper.populate(singleLegTrade);

        Map values = new HashMap();
        values.put(EmailConstants.TRADE_TRANSACTION_ID, singleLegTrade.get_id());
        values.put(EmailConstants.TRADE_ORDER_ID, helper.getOrderId());
        values.put(EmailConstants.TRADE_TRADE_DATE, helper.getTradeDate());
        values.put(EmailConstants.TRADE_EXECUTION_DATE, helper.getTradeExecutionDate());
        values.put(EmailConstants.TRADE_EXECUTION_TIME, helper.getTradeExecutionTime());
        values.put(EmailConstants.TRADE_VALUE_DATE, helper.getTradeValueDate());
        populateNDFDetails(values, helper);
        values.put(EmailConstants.TRADE_ORGANIZATION, helper.getTradeOrganization());
        values.put(EmailConstants.TRADE_LEGAL_ENTITY, helper.getTradeLegalEntity());
        values.put(EmailConstants.TRADE_ORGANIZATION_ROLE, helper.getTradeOrganizationRole());
        values.put(EmailConstants.TRADE_NAME, helper.getTradeName());
        values.put(EmailConstants.TRADE_TYPE, helper.getTradeType());
        values.put(EmailConstants.TYPE, helper.getType());
        values.put(EmailConstants.TRADE_CCY_PAIR, helper.getCcyPairName());
        values.put(EmailConstants.TRADE_BUY_CCY, helper.getBuyCcyName());
        values.put(EmailConstants.TRADE_SELL_CCY, helper.getSellCcyName());
        values.put(EmailConstants.TRADE_DEALT_AMOUNT, helper.getTradeDealtAmount());
        values.put(EmailConstants.TRADE_BUY_AMT, helper.getTradeBuyAmount());
        values.put(EmailConstants.TRADE_SELL_AMT, helper.getTradeSellAmount());
        values.put(EmailConstants.TRADE_RATE, helper.getTradeRate());
        values.put(EmailConstants.TRADE_SETTLED_AMOUNT, helper.getTradeSettledAmount());
        values.put(EmailConstants.TRADE_SETTLED_AMOUNT_INUSD, helper.getTradeSettledAmountInUSD());
        values.put(EmailConstants.TRADE_PROVIDER_NAME, helper.getTradePartyName());
        values.put(EmailConstants.TRADE_PROVIDER_TRADE_ID, helper.getTradePartyId());
        values.put(EmailConstants.TRADE_BUY_SELL, helper.getFiBuySellMode());
        values.put(EmailConstants.TRADE_USER, helper.getTradeUserName());
        values.put(EmailConstants.TRADE_EXTERNAL_TRADE_ID, helper.getTradeExternalTradeId());
        values.put(EmailConstants.TRADE_DEALT_CCY, helper.getDealtCcyName());
        values.put(EmailConstants.TRADE_SETTLED_CCY, helper.getSettledCcyName());
        values.put(EmailConstants.TRADE_BASE_CCY, helper.getBaseCcyName());
        values.put(EmailConstants.TRADE_TERM_CCY, helper.getVariableCcyName());
        values.put(EmailConstants.TRADE_COUNTERPARTY, helper.getTradeCounterparty());

        values.put(EmailConstants.EBS_DEFAULT_LE, helper.getEbsDefaultLegalEntity());
        values.put(EmailConstants.EMAIL_BRAND_NAME, helper.getEmailBrandName());
        values.put(EmailConstants.EMAIL_SUPPORT_MSG, helper.getEmailSupportMsg());
        values.put(EmailConstants.EMAIL_COPYRIGHT, helper.getEmailCopyRight());

        //------------- Check for Order Notes -----
        boolean isOrderNotesExists = false;
        if(!StringUtils.isBlank(helper.getOrderNotes()))
        {
            isOrderNotesExists = true;
        }
        WorkflowMessage emailWflMsg = MessageFactory.newWorkflowMessage();
        emailWflMsg.setTopic(ISConstantsC.MSG_TOPIC_TRADE);
        emailWflMsg.setEvent(ISConstantsC.MSG_EVT_CREATE);
        emailWflMsg.setStatus(MessageStatus.SUCCESS);
        emailWflMsg.setParameterValue(EmailConstants.TRADE_EVENT_TYPE, EmailConstants.TRADE_DONE);
        helper.setBrandSpecificParameters(emailWflMsg);
        setRestingOrderFlags(singleLegTrade,emailWflMsg);
        boolean sdWorkflow = !singleLegTrade.getOrderRequest().getUser().getOrganization().isSameAs(singleLegTrade.getOrderRequest().getOrganization());

        values.put(EmailConstants.TRADE_ORDER_NOTES, isOrderNotesExists?helper.getOrderNotes():"");

        emailWflMsg.setParameterValue(EmailConstants.TRADE_MAIL_CONTENTS, values);

        if (helper.isUserSentTradeVerificationEmail())
        {
        	if(sdWorkflow)
        	{
                values.put(EmailConstants.TRADE_ORDER_NOTES,"");
                helper.setTradeClientEmailIdForOrg(emailWflMsg);
                emailWorkflowMessageSender.sendEmail(emailWflMsg);

                if(isOrderNotesExists )
                {
                    values.put(EmailConstants.TRADE_ORDER_NOTES, helper.getOrderNotes());
                }
                //----------------------------------------------------------------------
                // remove fi email set before setting SD email id
                //----------------------------------------------------------------------
                emailWflMsg.setParameterValue(EmailConstants.TRADE_CLIENT_MAIL_ID, "");
                helper.setTradeClientEmailIdForUser(emailWflMsg, EmailConstants.TRADE_DONE);
            }
            else
            {
                helper.setTradeClientEmailIdForOrg(emailWflMsg);
                helper.setTradeClientEmailIdForUser(emailWflMsg, EmailConstants.TRADE_DONE);
            }
        }
        if(helper.isNonSpotTrade()){
            values.put(EmailConstants.TRADE_SPOT_RATE, helper.getTradeSpotRate());
            values.put(EmailConstants.FORWARD_POINTS, helper.getTradeForwardPoints());
        }
        emailWorkflowMessageSender.sendEmail(emailWflMsg);
    }

    private static void sendRejectedTradeWorkflowMsg(final SingleLegTrade singleLegTrade) throws Exception {
        String status = "";
        boolean sendToLp = false;
        if (singleLegTrade.isAutoCancelled()) {
            status = TradeStatus.REJECTED_BY_INTEGRAL;
            sendToLp = true;
        } else if (singleLegTrade.isManualInterventionDone()) {
            status = EmailConstants.MANUAL_TRADE_REJECTED;
            sendToLp = true;
        }

        if (sendToLp) {
            Organization tradingPartyOrg = getTradingPartyOrg(singleLegTrade);
            String emailId = getExtSysEmailId(tradingPartyOrg);
            if (emailId != null) {
                sendRejectedTradeWorkflowMsgToTradingParty(singleLegTrade, tradingPartyOrg, status, emailId);
            }
        }

        sendRejectedTradeWorkflowMsgToFi(singleLegTrade);
    }

    private static void sendRejectedTradeWorkflowMsgToTradingParty(final SingleLegTrade singleLegTrade, final Organization tradingPartyOrg, final String state, String emailId) throws Exception {
        Map values = new HashMap();
        WorkflowMessage emailWflMsg = MessageFactory.newWorkflowMessage();
        boolean setLPSupportInfo  = false;
        if (state.equals(TradeStatus.REJECTED_BY_INTEGRAL)) {
            setLPSupportInfo = true;
            emailWflMsg.setParameterValue(ISConstantsC.TRADE_STATE, state);
            values.put(EmailConstants.TRADE_TRADE_STATE, state);
            emailWflMsg.setParameterValue(EmailConstants.TRADE_EVENT_TYPE, EmailConstants.TRADE_REJECTED_BY_INTEGRAL);
        } else if (state.equals(EmailConstants.MANUAL_TRADE_REJECTED)) {
            emailWflMsg.setParameterValue(EmailConstants.TRADE_EVENT_TYPE, EmailConstants.MANUAL_TRADE_REJECTED);
        } else {
            LOG.info("Invalid state=" + state + " for " + singleLegTrade.get_id() + " tradingPartyOrg=" + tradingPartyOrg.getShortName() + " emailId=" + emailId);
        }

        sendTradeWorkflowMsgToTradingParty(singleLegTrade, tradingPartyOrg, emailWflMsg, values, ISConstantsC.MSG_EVT_CREATE, setLPSupportInfo, emailId);
    }

    private static void sendTradeWorkflowMsgToTradingParty(
            final SingleLegTrade singleLegTrade,
            final Organization tradingPartyOrg,
            final WorkflowMessage emailWflMsg,
            final Map values,
            MessageEvent emailWflMsgEvent,
            boolean setLPSupportInfo,
            String emailId)
                throws Exception {

        EmailTradeWorkflowHelper helper = EMAIL_TRADE_WORKFLOW_HELPER_THREAD_LOCAL.get();
        ApplicationEventCodes applicationEventCodes = null;
        if( values.containsKey( EmailConstants.TRADE_TRADE_STATE) && TradeStatus.PENDING.equals( values.get( EmailConstants.TRADE_TRADE_STATE))){
            applicationEventCodes = EVENT_ESP_TRADE_PENDING;
        }
        helper.populate(singleLegTrade, null, null, applicationEventCodes);

        values.put(EmailConstants.TRADE_PROVIDER_NAME, tradingPartyOrg.getShortName());
        values.put(EmailConstants.TRADE_TRADE_DATE, helper.getTradeDate());
        values.put(EmailConstants.TRADE_EXECUTION_DATE, helper.getTradeExecutionDate());
        values.put(EmailConstants.TRADE_EXECUTION_TIME, helper.getTradeExecutionTime());
        values.put(EmailConstants.TRADE_USER, helper.getTradeUserName());
        values.put(EmailConstants.TRADE_DEALT_AMOUNT, helper.getTradeDealtAmount());
        values.put(EmailConstants.TRADE_CCY_PAIR, helper.getCcyPairName());
        values.put(EmailConstants.TRADE_RATE, helper.getTradeRate());
        values.put(EmailConstants.TRADE_BUY_CCY, helper.getBuyCcyName());
        values.put(EmailConstants.TRADE_DEALT_CCY, helper.getDealtCcyName());
        values.put(EmailConstants.TRADE_SETTLED_CCY, helper.getSettledCcyName());
        values.put(EmailConstants.TRADE_SELL_CCY, helper.getSellCcyName());
        values.put(EmailConstants.TRADE_BUY_SELL, helper.getFiBuySellMode());
        values.put(EmailConstants.TRADE_BUY_AMT, helper.getTradeBuyAmount());
        values.put(EmailConstants.TRADE_SELL_AMT, helper.getTradeSellAmount());
        values.put(EmailConstants.TRADE_VALUE_DATE, helper.getTradeValueDate());
        populateNDFDetails(values, helper);
        values.put(EmailConstants.TRADE_TRANSACTION_ID, singleLegTrade.get_id());
        values.put(EmailConstants.TRADE_ORGANIZATION, helper.getTradeOrganization());
        values.put(EmailConstants.TRADE_ORDER_ID, helper.getOrderId());
        values.put(EmailConstants.EMAIL_BRAND_NAME, helper.getEmailBrandName(tradingPartyOrg));
        values.put(EmailConstants.EMAIL_SUPPORT_MSG, helper.getEmailSupportMsg(tradingPartyOrg));
        values.put(EmailConstants.EMAIL_COPYRIGHT, helper.getEmailCopyRight(tradingPartyOrg));

        emailWflMsg.setTopic(ISConstantsC.MSG_TOPIC_LPTradeMailMessage);
        emailWflMsg.setEvent(emailWflMsgEvent);
        emailWflMsg.setStatus(MessageStatus.SUCCESS);
        emailWflMsg.setParameterValue(EmailConstants.TRADE_MAIL_CONTENTS, values);

        helper.setBrandSpecificParameters(emailWflMsg, tradingPartyOrg);

        emailId = ISUtilImpl.getInstance().replaceSemicolonDelimiter(emailId);
        emailWflMsg.setParameterValue(EmailConstants.EmailIDForDelayedVerification, emailId);

        if (setLPSupportInfo) {
            ISEmailUtil.getInstance().addLPContactInformation(tradingPartyOrg, values);
        }
        if(helper.isNonSpotTrade()){
            values.put(EmailConstants.TRADE_SPOT_RATE, helper.getTradeSpotRate());
            values.put(EmailConstants.FORWARD_POINTS, helper.getTradeForwardPoints());
        }

        emailWorkflowMessageSender.sendEmail(emailWflMsg);
    }

    private static void sendRejectedTradeWorkflowMsgToFi(final SingleLegTrade singleLegTrade) throws Exception {
        SingleLegOrder singleLegOrder = null;
        Organization fiOrg = null;

        switch (singleLegTrade.getInternalRejectionCode()) {
            case WorkflowState.CREDIT_LIMIT_EXCEEDED:
            case WorkflowState.CREDIT_CHECK_FAILURE:
                break;

            default:
                singleLegOrder = singleLegTrade.getOrderRequest();
                if (singleLegOrder == null)
                    throw new NullPointerException("singleLegOrder");

                fiOrg = singleLegOrder.getOrganization();
                if (fiOrg == null)
                    throw new NullPointerException("fiOrg");

                if (TRADE_EMAIL_MBEAN.isRejectEmailSuppressedForFI(fiOrg.getShortName())) {
                    if (LOG.isDebugEnabled()) {
                        LOG.debug("Not sending email notification for rejected trade " + singleLegTrade.get_id() + ", rejection emails are suppressed for " + fiOrg.getShortName() + " organization");
                    }
                    return;
                }
                break;
        }

        EmailTradeWorkflowHelper helper = EMAIL_TRADE_WORKFLOW_HELPER_THREAD_LOCAL.get();
        helper.populate(singleLegTrade, singleLegOrder, fiOrg);

        Map values = new HashMap();
        values.put(EmailConstants.TRADE_TRANSACTION_ID, singleLegTrade.get_id());
        values.put(EmailConstants.TRADE_ORDER_ID, helper.getOrderId());
        values.put(EmailConstants.TRADE_TRADE_DATE, helper.getTradeDate());
        values.put(EmailConstants.TRADE_EXECUTION_DATE, helper.getTradeExecutionDate());
        values.put(EmailConstants.TRADE_EXECUTION_TIME, helper.getTradeExecutionTime());
        values.put(EmailConstants.TRADE_ORGANIZATION, helper.getTradeOrganization());
        values.put(EmailConstants.TRADE_LEGAL_ENTITY, helper.getTradeLegalEntity());
        values.put(EmailConstants.TRADE_ORGANIZATION_ROLE, helper.getTradeOrganizationRole());
        values.put(EmailConstants.TRADE_NAME, helper.getTradeName());
        values.put(EmailConstants.TRADE_TYPE, helper.getTradeType());
        values.put(EmailConstants.TRADE_CCY_PAIR, helper.getCcyPairName());
        values.put(EmailConstants.TRADE_BUY_CCY, helper.getBuyCcyName());
        values.put(EmailConstants.TRADE_SELL_CCY, helper.getSellCcyName());
        values.put(EmailConstants.TYPE, helper.getType());
        values.put(EmailConstants.TRADE_DEALT_CCY, helper.getDealtCcyName());
        values.put(EmailConstants.TRADE_SETTLED_CCY, helper.getSettledCcyName());
        values.put(EmailConstants.TRADE_VALUE_DATE, helper.getTradeValueDate());
        populateNDFDetails(values, helper);
        values.put(EmailConstants.TRADE_DEALT_AMOUNT, helper.getTradeDealtAmount());
        values.put(EmailConstants.TRADE_BUY_AMT, helper.getTradeBuyAmount());
        values.put(EmailConstants.TRADE_SELL_AMT, helper.getTradeSellAmount());
        values.put(EmailConstants.TRADE_RATE, helper.getTradeRate());
        values.put(EmailConstants.TRADE_SETTLED_AMOUNT, helper.getTradeSettledAmount());
        values.put(EmailConstants.TRADE_PROVIDER_NAME, helper.getTradePartyName());
        values.put(EmailConstants.TRADE_FAILURE_REASON, singleLegTrade.getRejectionReason());
        values.put(EmailConstants.TRADE_BUY_SELL, helper.getFiBuySellMode());
        values.put(EmailConstants.TRADE_USER, helper.getTradeUserName());
        values.put(EmailConstants.EMAIL_BRAND_NAME, helper.getEmailBrandName());
        values.put(EmailConstants.EMAIL_SUPPORT_MSG, helper.getEmailSupportMsg());
        values.put(EmailConstants.EMAIL_COPYRIGHT, helper.getEmailCopyRight());
        if(helper.isNonSpotTrade()){
            values.put(EmailConstants.TRADE_SPOT_RATE, "");
            values.put(EmailConstants.FORWARD_POINTS, "");
        }

        WorkflowMessage emailWflMsg = MessageFactory.newWorkflowMessage();
        emailWflMsg.setTopic(ISConstantsC.MSG_TOPIC_TRADE);
        emailWflMsg.setEvent(ISConstantsC.MSG_EVT_CREATE);
        emailWflMsg.setStatus(MessageStatus.SUCCESS);
        emailWflMsg.setParameterValue(EmailConstants.TRADE_EVENT_TYPE, EmailConstants.TRADE_DECLINED);
        emailWflMsg.setParameterValue(EmailConstants.TRADE_MAIL_CONTENTS, values);

        helper.setBrandSpecificParameters(emailWflMsg);
        setRestingOrderFlags(singleLegTrade,emailWflMsg);

        if (helper.isUserSentTradeRejectionEmail()) {
            helper.setTradeClientEmailIdForOrg(emailWflMsg);
            helper.setTradeClientEmailIdForUser(emailWflMsg, EmailConstants.TRADE_DECLINED);
        }
        emailWorkflowMessageSender.sendEmail(emailWflMsg);
    }



    private static ApplicationEventCodes getApplicationEventCodes(final Notification notification) throws NullPointerException {
        ApplicationEventCodes appEventCode = ApplicationEventCodes.fromCode(notification.getAppEventCode());
        if (appEventCode == null)
            throw new NullPointerException("appEventCode");

        return appEventCode;
    }

    private static Organization getTradingPartyOrg(final SingleLegTrade singleLegTrade) throws NullPointerException {
        LegalEntity counterPartyLE = DealingModelUtil.getCounterPartyLegalEntity( singleLegTrade);
        if (counterPartyLE == null)
            throw new NullPointerException("counterPartyLE");

        Organization counterPartyOrg = counterPartyLE.getOrganization();
        if (counterPartyOrg == null)
            throw new NullPointerException("counterPartyOrg");

        return counterPartyOrg;
    }

    private static String getExtSysEmailId(final Organization org) {
        ExternalSystemId extSysEmailNotification = org.getExternalSystemId(EmailConstants.EmailNotificationForDelayedVerification);
        if (extSysEmailNotification == null || !extSysEmailNotification.getSystemId().trim().equalsIgnoreCase("y")) {
            if (LOG.isDebugEnabled()) {
                LOG.debug("No external system email notifications for organization " + org.getShortName());
            }

            return null;
        }

        ExternalSystemId extSysEmailId = org.getExternalSystemId(EmailConstants.EmailIDForDelayedVerification);
        if (extSysEmailId == null) {
            if (LOG.isDebugEnabled()) {
                LOG.debug("No external system email for organization " + org.getShortName());
            }

            return null;
        }

        String emailId = extSysEmailId.getSystemId();
        if (emailId == null || emailId.equals("")) {
            if (LOG.isDebugEnabled()) {
                LOG.debug("No email id for organization external system " + extSysEmailId.getName());
            }

            return null;
        }

        return emailId;
    }
    
    private static void setRestingOrderFlags(final SingleLegTrade singleLegTrade , WorkflowMessage emailWflMsg )
    {
        SingleLegOrder orderRequest =  singleLegTrade.getOrderRequest();
        if ( orderRequest == null )
        {
            LOG.info ( "ENWF.setRestingOrderFlags : no order request found for trade=" + singleLegTrade.getCorrelationId () );
            return;
        }
        String tifString = orderRequest.getOrderExpiry ().getTimeInForce ().toString ();
    	String[] exclusionList = ISMBEAN.getOrderEmailTIFExclusion( orderRequest.getOrganization () );
    	boolean exluded = false;
    	if ( exclusionList != null )
        {
            for ( String exclusion : exclusionList )
            {
                if ( tifString.equals ( exclusion ) )
                {
                    exluded = true;
                    break;
                }
            }
        }

		//special logic to exclude GTD orders with small expiry times if GTD is not part of the exclusion list.
        if ( !exluded && TimeInForce.GTD.toString ().equals ( tifString ) )
        {
            long expiryTime = getRelativeExpiryTimeInMilliseconds ( orderRequest );
            long threshold = ISMBEAN.getOrderEmailGTDExpirationTimeThreshold ( orderRequest.getOrganization () );
            if ( expiryTime <= threshold )
            {
                if (LOG.isDebugEnabled())
                {
                    LOG.debug( "ENWF.setRestingOrderFlags : exclude GTD Order " + orderRequest.get_id() + ",expiryTime=" +  expiryTime + ",expiryThreshold=" + threshold );
                }
                exluded = true;
            }
        }


    	if(!exluded)
    	{
    		emailWflMsg.setParameterValue(EmailConstants.RESTINGORDER,true);
    		if (LOG.isDebugEnabled()) 
    		{
                LOG.debug("Is Resting Order " + orderRequest.get_id() + " Order TIF type : " +  orderRequest.getOrderExpiry().getTimeInForce().toString());
            }
    	}
    }

    private static long getRelativeExpiryTimeInMilliseconds( SingleLegOrder orderRequest )
    {
        OrderRequestEventTimes eventTimes = orderRequest.getOrderRequestEventTimes();
        long refTime = eventTimes.getSubmissionTime();
        return orderRequest.getExpireTime () - refTime;
    }

    private static void populateNDFDetails(Map values, EmailTradeWorkflowHelper helper)
    {
        if(helper.isNDF()) {
            values.put(EmailConstants.TRADE_FIXING_DATE, helper.getTradeFixingDate());
            values.put(EmailConstants.TRADE_INCLUDE_TEMPLATE_TYPE_ONE_SUB, EmailConstants.TRADE_FIXING_DATE_INCLUDE_SUB);
            values.put(EmailConstants.TRADE_INCLUDE_TEMPLATE_TYPE_ONE_BODY, EmailConstants.TRADE_FIXING_DATE_INCLUDE_BODY);
        }
    }
}
