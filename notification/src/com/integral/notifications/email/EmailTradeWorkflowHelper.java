package com.integral.notifications.email;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.fx.FXRateBasis;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.email.EmailConstants;
import com.integral.is.common.email.TradeEmailMBean;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.MatchEvent;
import com.integral.model.dealing.MatchEventPQ;
import com.integral.model.dealing.OrderRequest;
import com.integral.model.dealing.ProductType;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegOrderMatch;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.model.dealing.TradeLeg;
import com.integral.persistence.Namespace;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.user.Contact;
import com.integral.user.DisplayPreference;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserContact;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * Package specific helper for email trade workflow.
 *
 * <AUTHOR>
 */
class EmailTradeWorkflowHelper {
    private final static Log LOG = LogFactory.getLog(EmailTradeWorkflowHelper.class);
    private final static String DOUBLE_NUMBER_FORMAT = "#,###,##0";
    private final static String SPOT_RATE_FORMAT = "0.0000####";
    private final static String DATE_FORMAT = "MMM dd,yyyy";
    private final static String DATE_TIME_FORMAT = "MMM dd,yyyy HH:mm:ss zzz";

    private final TradeEmailMBean tradeEmailMBean;

    private SingleLegTrade singleLegTrade;
    private SingleLegOrder singleLegOrder;
    private SingleLegOrderMatch matchEvent;
    private MatchEvent.MatchEventLeg matchEventLeg;
    private FXRateBasis fxRateBasis;
    private Namespace namespace;
    private Organization fiOrg;
    private String fiOrgName;
    private String maskedLpName;
    private String realLpName;
    private LegalEntity legalEntity;
    private LegalEntity counterPartyLE;
    private TradeLeg tradeLeg;
    private long executionTime;
    private User user;
    private DisplayPreference displayPreference;
    private String ccyPairName;
    private String baseCcyName;
    private String variableCcyName;
    private String fiBuySellMode;
    private String buyCcyName;
    private String sellCcyName;
    private double buyAmount;
    private double sellAmount;
    private String dealtCcyName;
    private double dealtAmount;
    private String settledCcyName;
    private double settledAmount;
    private double settledAmountInUSD;
    private String orderNotes;

    EmailTradeWorkflowHelper(final TradeEmailMBean tradeEmailMBean) {
        this.tradeEmailMBean = tradeEmailMBean;
    }

    void populate(SingleLegTrade trd) throws Exception {
        populate(trd, null, null, null);
    }
    void populate(SingleLegTrade trd, SingleLegOrder order, Organization org) throws Exception {
        populate(trd, order, org, null);
    }
    void populate(SingleLegTrade trd, SingleLegOrder order, Organization org, ApplicationEventCodes appEventCode) throws NullPointerException, IllegalArgumentException {
        reset();                        // protect against ThreadLocal misuses

        this.singleLegTrade = trd;
        namespace = trd.getNamespace();
        if (namespace == null)
            throw new NullPointerException("namespace");

        executionTime = trd.getExecutionTime();

        if (order == null) {
            this.singleLegOrder = trd.getOrderRequest();
            if (this.singleLegOrder == null)
                throw new NullPointerException("singleLegOrder");
        } else {
            this.singleLegOrder = order;
        }

        if (org == null) {
            this.fiOrg = singleLegOrder.getOrganization();
            if (this.fiOrg == null)
                throw new NullPointerException("fiOrg");
        } else {
            this.fiOrg = org;
        }
        fiOrgName = fiOrg.getShortName();

        matchEvent = trd.getMatchEvent();
        if (matchEvent == null)
            throw  new NullPointerException("matchEvent");

        matchEventLeg = matchEvent.getMatchEventLeg();
        if (matchEventLeg == null)
            throw new NullPointerException("matchEventLeg");

        MatchEventPQ matchEventPQ = matchEvent.getMatchEventPQ();
        if (matchEvent.isDirectedOrder()) {
            if (singleLegTrade.isIntraFloor() ) {
                realLpName = singleLegTrade.getCounterpartyLegalEntity().getOrganization().getShortName();
                maskedLpName = singleLegTrade.getCounterpartyLegalEntity().getOrganization().getShortName();
            }
            else {
                if(singleLegTrade.getTradingParty()!=null) {
                    realLpName = singleLegTrade.getTradingParty().getOrganization().getShortName();
                    maskedLpName = singleLegTrade.getTradingParty().getOrganization().getShortName();
                }else if(singleLegTrade.isOCX2()){
                    realLpName = singleLegTrade.getCounterpartyLegalEntity().getOrganization().getShortName();
                    maskedLpName = singleLegTrade.getCounterpartyLegalEntity().getOrganization().getShortName();
                }
            }
        }
        else {
            MatchEventPQ.QuoteDescriptor quoteDescriptor = matchEventPQ.getQuoteForTradeRequest();
            if (quoteDescriptor == null)
                throw new NullPointerException("quoteDescriptor");

            realLpName = quoteDescriptor.getOrgName();
            maskedLpName = quoteDescriptor.getMaskedName();     // if no masked LP used it'll be the same as realLpName
        }

        FXRateBasis tradeFxRateBasis = trd.getFxRateBasis();
        if (tradeFxRateBasis == null) {
            tradeFxRateBasis = matchEvent.getFxRateBasis();
            if (tradeFxRateBasis == null)
                throw new NullPointerException("fxRateBasis");
        }
        fxRateBasis = tradeFxRateBasis;

        user = singleLegOrder.getUser();
        if (user == null)
            throw new NullPointerException("user");

        legalEntity = trd.getLegalEntity();
        if (legalEntity == null)
            throw new NullPointerException("legalEntity");

        counterPartyLE = DealingModelUtil.getCounterPartyLegalEntity( trd);
        if ( counterPartyLE == null)
            throw new NullPointerException("tradingPartyLegalEntity");

        displayPreference = user.getDisplayPreference();
        tradeLeg = trd.getTradeLeg();
        if (tradeLeg == null)
            throw new NullPointerException("tradeLeg");

        CurrencyPair ccyPair = singleLegOrder.getCurrencyPair();
        ccyPair = CurrencyFactory.getOriginalCurrencyPair(ccyPair);
        if (ccyPair == null)
            throw new NullPointerException("ccyPair");

        ccyPairName = ccyPair.getName();
        baseCcyName = ccyPair.getBaseCurrency().getShortName();
        variableCcyName = ccyPair.getVariableCurrency().getShortName();

        dealtCcyName = trd.getDealtCurrencyName();
        dealtCcyName = getUnderlyingCcyName(dealtCcyName);
        settledCcyName = dealtCcyName.equals(baseCcyName) ? variableCcyName : baseCcyName;
        settledAmountInUSD = tradeLeg.getSettledAmountInUSD();

        orderNotes = singleLegOrder.getNotes();

        if (appEventCode != null) {
            switch (appEventCode) {
                case EVENT_ESP_TRADE_ACCEPT:
                case EVENT_ESP_TRADE_PENDING:
                    dealtAmount = tradeLeg.getAcceptedDealtCurrencyAmount();
                    settledAmount = tradeLeg.getAcceptedSettledCurrencyAmount();
                    break;
                default:
                    dealtAmount = tradeLeg.getDealtAmount();
                    settledAmount = tradeLeg.getSettledAmount();
            }
        } else {
            dealtAmount = tradeLeg.getDealtAmount();
            settledAmount = tradeLeg.getSettledAmount();
        }
        populateBuySellAttributes(dealtAmount, settledAmount);
    }



    void reset() {
        singleLegTrade = null;
        singleLegOrder = null;
        matchEvent = null;
        matchEventLeg = null;
        fxRateBasis = null;
        namespace = null;
        fiOrg = null;
        fiOrgName = null;
        maskedLpName = null;
        realLpName = null;
        legalEntity = null;
        counterPartyLE = null;
        tradeLeg = null;
        executionTime = 0L;
        user = null;
        displayPreference = null;
        ccyPairName = null;
        baseCcyName = null;
        variableCcyName = null;
        fiBuySellMode = null;
        buyCcyName = null;
        sellCcyName = null;
        buyAmount = 0.0;
        sellAmount = 0.0;
        dealtCcyName = null;
        dealtAmount = 0.0;
        settledCcyName = null;
        settledAmount = 0.0;
        settledAmountInUSD = 0.0;
        orderNotes = null;
    }

    void populateBuySellAttributes(double dealtAmt, double settledAmt)  {
        OrderRequest.RequestLeg requestLeg = singleLegOrder.getRequestLeg();
        if (requestLeg == null)
            throw new NullPointerException("requestLeg");

        OrderRequest.RequestLeg.BuySellMode buySellMode = requestLeg.getBuySellMode();
        if (buySellMode == null)
            throw new NullPointerException("buySellMode");

        boolean isFiBuyingBaseCurrency = tradeLeg.isBuyingBaseCurrency();
        if (singleLegTrade.getDealtCurrency().equals(singleLegTrade.getBaseCurrency())) {

            // buySellMode is in terms of base currency
            switch (buySellMode) {
                case SELL:
                    fiBuySellMode = "Sell";
                    break;
                case BUY:
                    fiBuySellMode = "Buy";
                    break;
                case TWO_WAY:
                    fiBuySellMode = "unknown";
                    break;
                default:
                    fiBuySellMode = "unknown";
                    LOG.error("Invalid fiBuySellMode=" + buySellMode + ", orderId " + singleLegOrder.get_id());
                    break;
            }

            if (isFiBuyingBaseCurrency) {
                buyCcyName = dealtCcyName;
                buyAmount = dealtAmt;
                sellCcyName = settledCcyName;
                sellAmount = settledAmt;
            } else {
                buyCcyName = settledCcyName;
                buyAmount = settledAmt;
                sellCcyName = dealtCcyName;
                sellAmount = dealtAmt;
            }
        } else {

            // buySellMode is in terms of base currency
            switch (buySellMode) {
                case SELL:
                    fiBuySellMode = "Buy";
                    break;
                case BUY:
                    fiBuySellMode = "Sell";
                    break;
                case TWO_WAY:
                    fiBuySellMode = "unknown";
                    break;
                default:
                    fiBuySellMode = "unknown";
                    LOG.error("Invalid fiBuySellMode=" + buySellMode + ", orderId " + singleLegOrder.get_id());
                    break;
            }

            if (isFiBuyingBaseCurrency) {
                buyCcyName = settledCcyName;
                buyAmount = settledAmt;
                sellCcyName = dealtCcyName;
                sellAmount = dealtAmt;
            } else {
                buyCcyName = dealtCcyName;
                buyAmount = dealtAmt;
                sellCcyName = settledCcyName;
                sellAmount = settledAmt;
            }
        }
    }

    String getTradeOrganization() {
        return namespace.getShortName();
    }

    String getTradeOrganizationRole() {
        return namespace.getDescription();
    }

    String getTradeLegalEntity() {
        return legalEntity.getShortName();
    }

    String getEbsDefaultLegalEntity() {
        return fiOrg.getDefaultDealingEntity().getShortName();
    }

    String getTradeCounterparty() {
        return counterPartyLE.getShortName();
    }

    String getTradePartyId() {
        return getTradePartyName();
    }

    String getTradePartyName() {
        return counterPartyLE.getOrganization().getShortName();
    }

    String getProviderName() {
        if (realLpName != null && realLpName.equals(maskedLpName)) // incase of net trade, realLPName will be _Null_
            return realLpName;      // LP is not masked

        // LP is masked
        if (matchEvent.isPrimeBrokerCoverEnabled())
            return maskedLpName;    // FI-PB-LPAA/LP workflow

        return realLpName;          // FI-LPAA/LP workflow
    }

    String getTradeExternalTradeId() {
        return singleLegTrade.getExternalReferenceId();
    }

    String getCcyPairName() {
        return ccyPairName;
    }

    public String getBaseCcyName() {
        return baseCcyName;
    }

    public String getVariableCcyName() {
        return variableCcyName;
    }

    String getFiBuySellMode() {
        return fiBuySellMode;
    }

    String getBuyCcyName() {
        return buyCcyName;
    }

    String getSellCcyName() {
        return sellCcyName;
    }

    String getTradeBuyAmount() {
        return formatAmount(buyCcyName, buyAmount);
    }

    String getTradeSellAmount() {
        return formatAmount(sellCcyName, sellAmount);
    }

    String getDealtCcyName() {
        return dealtCcyName;
    }

    String getTradeDealtAmount() {
        return formatAmount(dealtCcyName, dealtAmount);
    }

    String getSettledCcyName() {
        return settledCcyName;
    }

    String getTradeSettledAmount() {
        return formatAmount(settledCcyName, settledAmount);
    }

    String getTradeSettledAmountInUSD() {
        return formatAmount(settledCcyName, settledAmountInUSD);
    }

    String getOrderId() {
        return singleLegOrder.get_id();
    }

    String getType() {
        return getTradeName();
    }

    String getTradeName() {
        return "FXSPOT";
    }

    String getTradeType() {
        return isNonSpotTrade() ? (isNDF() ? "FXNDF" : "FXOUTRIGHT") : "FXSPOT";
    }

    String getTradeUserName() {
        return user.getShortName();
    }

    String getTradeDate() {
        IdcDate tradeDate = DateTimeFactory.newDate(new Date(singleLegTrade.getTradeDate()));
        return formatDate(tradeDate.asJdkDate());
    }

    String getTradeExecutionDate() {
        return formatDate(new Date(executionTime));
    }

    String getTradeExecutionTime() {
        return formatDateTime(new Date(executionTime));
    }

    String getTradeValueDate() {
        IdcDate valueDate = DateTimeFactory.newDate(new Date(tradeLeg.getValueDate()));
        return formatDate(valueDate.asJdkDate());
    }

    String getTradeFixingDate(){
        long fixingDate = tradeLeg.getFixingDate();
        if(fixingDate == 0L){
            return "";
        }
        IdcDate valueDate = DateTimeFactory.newDate(new Date(fixingDate));
        return formatDate(valueDate.asJdkDate());
    }

    String getTradeRate() {
        if(!isNonSpotTrade()) {
            return formatSpotRate(tradeLeg.getRate());
        }else {
            return formatRate(tradeLeg.getRate());
        }
    }

    String getTradeSpotRate(){
        return formatSpotRate(tradeLeg.getSpotRate());
    }

    String getTradeForwardPoints(){
        return formatRate(tradeLeg.getForwardPoints());
    }

    String getAcceptanceRate() {
        if(!isNonSpotTrade()) {
            return formatSpotRate(matchEventLeg.getFinalAcceptanceSpotRate());
        }else {
            return formatRate(matchEventLeg.getFinalAcceptanceSpotRate());
        }
    }

    private String formatSpotRate(final double rate) {
        DecimalFormat df = new DecimalFormat(SPOT_RATE_FORMAT);
        if (DealingModelUtil.isRateInverted(fxRateBasis, variableCcyName)) {
            df.setMaximumFractionDigits(fxRateBasis.getInverseSpotPrecision());
            df.setMinimumFractionDigits(fxRateBasis.getInverseSpotPrecision());
        } else {
            df.setMaximumFractionDigits(fxRateBasis.getSpotPrecision());
            df.setMinimumFractionDigits(fxRateBasis.getSpotPrecision());
        }

        return df.format(rate);
    }

    private String formatRate(final double rate) {
        DecimalFormat df = new DecimalFormat(SPOT_RATE_FORMAT);
        if (DealingModelUtil.isRateInverted(fxRateBasis, variableCcyName)) {
            df.setMaximumFractionDigits(fxRateBasis.getInverseForwardRatePrecision());
            df.setMinimumFractionDigits(fxRateBasis.getInverseForwardRatePrecision());
        } else {
            df.setMaximumFractionDigits(fxRateBasis.getForwardRatePrecision());
            df.setMinimumFractionDigits(fxRateBasis.getForwardRatePrecision());
        }

        return df.format(rate);
    }

    boolean isUserSentTradeVerificationEmail() {
        String userSentTradeVerificationEmail = (String)user.getProperty(ISConstantsC.SEND_TRADE_EMAIL_VERIFICATION);
        return (userSentTradeVerificationEmail == null) || userSentTradeVerificationEmail.trim().equalsIgnoreCase("y");
    }

    boolean isUserSentTradeRejectionEmail() {
        String userSentTradeRejectionEmail = (String)user.getProperty(ISConstantsC.SEND_TRADE_EMAIL_REJECTION);
        return (userSentTradeRejectionEmail == null) || userSentTradeRejectionEmail.trim().equalsIgnoreCase("y");
    }

    void setTradeClientEmailIdForOrg(final WorkflowMessage emailWflMsg) throws NullPointerException {
        String emailId;

        LegalEntity legalEntity = singleLegTrade.getLegalEntity();
        boolean sendTradingSupportEmail = null!=legalEntity && legalEntity.isSendEmail();
        String leName = legalEntity != null ? legalEntity.getShortName () : "N/A";
        for (Object obj : fiOrg.getContacts()) {
            if (obj == null)
                continue;

            Contact orgContact = (Contact)obj;
            if (!orgContact.getShortName().equals(ISConstantsC.ORGANIZATION_EMAIL_CONTACT))
                continue;

            String emailAddress = orgContact.getEmailAddress();
            if (emailAddress == null)
                continue;

            if( !sendTradingSupportEmail )
            {
                LOG.info( new StringBuilder ( "ETWH.setTradeClientEmailIdForOrg - Email is NOT sent for tid=")
                        .append ( singleLegTrade.get_id() ).append ( ",toOrg=" ).append ( fiOrg.getShortName() )
                        .append ( ",legalEntity=" ).append ( leName ).append ( ",tradingSupportEmailAddress=" )
                        .append ( emailAddress ).append ( ",legalEntity.sendEmailEnabled=" )
                        .append ( false ).toString () ) ;
                continue;
            }

            emailId = ISUtilImpl.getInstance().replaceSemicolonDelimiter(emailAddress);
            emailWflMsg.setParameterValue(EmailConstants.TRADE_CLIENT_MAIL_ID, emailId);
            if (LOG.isDebugEnabled()) {
                LOG.debug("Sending Email to Organization " + fiOrgName + " at contact " + emailId);
            }
            LOG.info( new StringBuilder ( "ETWH.setTradeClientEmailIdForOrg - sending email for tid=")
                    .append ( singleLegTrade.get_id() ).append ( ",toOrg=" ).append ( fiOrg.getShortName() )
                    .append ( ",legalEntity=" ).append ( leName ).append ( ",tradingSupportEmailAddress=" )
                    .append ( emailAddress ).append ( ",legalEntity.sendEmailEnabled=" )
                    .append ( true ).toString () ) ;
        }
        // SD flow
        Organization userOrg = user.getOrganization();
        if (userOrg == null)
            throw new NullPointerException("userOrg");

        if (userOrg.isSameAs(fiOrg))
            return;

        emailId = (String)emailWflMsg.getParameterValue(EmailConstants.TRADE_CLIENT_MAIL_ID);
        for (Object obj : userOrg.getContacts()) {
            if (obj == null)
                continue;

            Contact sdContact = (Contact)obj;
            if (!sdContact.getShortName().equals(ISConstantsC.ORGANIZATION_EMAIL_CONTACT))
                continue;

            String emailAddress = sdContact.getEmailAddress();
            if (emailAddress == null )
                continue;

            if( !user.getDefaultDealingEntity().isSendEmail() )
            {
                leName = user.getDefaultDealingEntity().getFullyQualifiedName ();
                LOG.info( new StringBuilder ( "ETWH.setTradeClientEmailIdForOrg - SD Workflow. Email is NOT sent for tid=")
                        .append ( singleLegTrade.get_id() ).append ( ",toOrg=" ).append ( userOrg.getShortName() )
                        .append ( ",fiOrg=" ).append ( fiOrg.getShortName () ).append ( ",legalEntity=" )
                        .append ( leName ).append ( ",tradingSupportEmailAddress=" ).append ( emailAddress )
                        .append ( ",legalEntity.sendEmailEnabled=" ).append ( user.getDefaultDealingEntity().isSendEmail () ).toString () ) ;
                continue;
            }

            String sdEmailId = ISUtilImpl.getInstance().replaceSemicolonDelimiter(sdContact.getEmailAddress());
            emailId = (emailId == null || emailId.equalsIgnoreCase("")) ? sdEmailId : emailId + "," + sdEmailId;
            emailWflMsg.setParameterValue(EmailConstants.TRADE_CLIENT_MAIL_ID, emailId);
            if (LOG.isDebugEnabled()) {
                LOG.debug("Also sending Email to SD organization " + userOrg.getShortName() + " at contact " + emailId);
            }

            LOG.info( new StringBuilder ( "ETWH.setTradeClientEmailIdForOrg - sending email to SD org for tid=" )
                    .append ( singleLegTrade.get_id() ).append ( ",toOrg=" ).append ( userOrg.getShortName() )
                    .append ( ",fiOrg=" ).append ( fiOrg.getShortName () ).append ( ",tradingSupportEmailAddress=" )
                    .append ( emailId ).toString () ) ;
        }
    }

    void setTradeClientEmailIdForUser(final WorkflowMessage emailWflMsg, final String eventType) {
        if (!user.isTradingEmailEnabled())
            return;

        UserContact userContact = user.getContact();
        if (userContact == null) {
            if (LOG.isDebugEnabled()) {
                LOG.debug("No user contacts available for " + getTradeUserName());
            }
            return;
        }

        String userEmailAddress = userContact.getEmailAddress();
        if (userEmailAddress == null || userEmailAddress.equals("")) {
            if (LOG.isDebugEnabled()) {
                LOG.debug("No email address available for " + getTradeUserName());
            }
            return;
        }

        String emailId = (String)emailWflMsg.getParameterValue(EmailConstants.TRADE_CLIENT_MAIL_ID);
        boolean isSendEmail = isSendEmail(user, eventType);
        if(isSendEmail)
        {
            boolean isSendForRestingOrder= isSendForRestingOrder(user,emailWflMsg);
            boolean isROEnableSend = user.isROTriggeringEmailEnabled();
            if(isSendForRestingOrder && isROEnableSend )
            {
                if (LOG.isDebugEnabled())
                {
                    Map vales = (Map)emailWflMsg.getParameterValue(EmailConstants.TRADE_MAIL_CONTENTS);
                    String orderID = (String)vales.get(EmailConstants.TRADE_ORDER_ID);
                    LOG.debug("Is Resting Order Email Enabled For User " + user.getFullyQualifiedName() + " for Order " +orderID );
                }
                emailId = (emailId == null) ? userEmailAddress : emailId + "," + userEmailAddress;
            }
            else
            if ( !isROEnableSend)
            {
                if (LOG.isDebugEnabled())
                {
                    Map vales = (Map)emailWflMsg.getParameterValue(EmailConstants.TRADE_MAIL_CONTENTS);
                    String orderID = (String)vales.get(EmailConstants.TRADE_ORDER_ID);
                    LOG.debug("Is Resting Order Not Email Enabled For User " + user.getFullyQualifiedName() + " for Order " +orderID );
                }
                emailId = (emailId == null) ? userEmailAddress : emailId + "," + userEmailAddress;
            }
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("Sending email to user " + getTradeUserName() + " at contact " + userEmailAddress);
        }

        LOG.info( new StringBuilder ( "ETWH.setTradeClientEmailIdForUser - user email info for tid=" )
                .append ( singleLegTrade.get_id() ).append ( ",user=" ).append ( user.getFullyQualifiedName () )
                .append ( ",isSendEmail=" ).append ( isSendEmail ).append ( ",emailId=" ).append ( emailId )
                .append ( ",eventType=" ).append ( eventType ).append ( ",userEmail=" ).append ( userEmailAddress ).toString () ) ;

        emailWflMsg.setParameterValue(EmailConstants.TRADE_CLIENT_MAIL_ID, emailId);
    }

    String getEmailBrandName() {
        return tradeEmailMBean.getBrandName(fiOrg);
    }
    String getEmailBrandName(final Organization org) {
        return tradeEmailMBean.getBrandName(org);

    }

    String getEmailSupportMsg() {
        return tradeEmailMBean.getSupportMsg(fiOrg);
    }
    String getEmailSupportMsg(final Organization org) {
        return tradeEmailMBean.getSupportMsg(org);
    }

    String getEmailCopyRight() {
        return tradeEmailMBean.getCopyright(fiOrg);
    }
    String getEmailCopyRight(final Organization org) {
        return tradeEmailMBean.getCopyright(org);
    }

    void setBrandSpecificParameters(final WorkflowMessage emailWflMsg) {
        setBrandSpecificParameters(emailWflMsg, fiOrg);
    }
    void setBrandSpecificParameters(final WorkflowMessage emailWflMsg, final Organization org) {
        emailWflMsg.setParameterValue(EmailConstants.EMAIL_BACKGROUND_IMAGE_ENABLED, tradeEmailMBean.isBackgroundImageEnabled(org));
        emailWflMsg.setParameterValue(EmailConstants.EMAIL_BACKGROUND_IMAGE, tradeEmailMBean.getBackgroundImageName(org));
        emailWflMsg.setParameterValue(EmailConstants.EMAIL_LOGO_IMAGE_ENABLED, tradeEmailMBean.isLogoImageEnabled(org));
        emailWflMsg.setParameterValue(EmailConstants.EMAIL_LOGO_IMAGE, tradeEmailMBean.getLogoImageName(org));
    }

    private static boolean isSendForRestingOrder(final User user, final WorkflowMessage emailWflMsg)
    {
        Boolean isrestingOrder = (Boolean)emailWflMsg.getParameterValue(EmailConstants.RESTINGORDER);
        if( null == isrestingOrder ) return false;
        return isrestingOrder.booleanValue();
    }

    private static boolean isSendEmail(final User user,String eventType)
    {
        if ((eventType.equals(EmailConstants.TRADE_DONE) && user.isEspVerificationEmailEnabled()) ||
                (eventType.equals(EmailConstants.TRADE_DECLINED) && user.isEspRejectionEmailEnabled()) ||
                (eventType.equals(EmailConstants.TRADE_PRICE_TAKING) && user.isEspTradeRequestEmailEnabled()))
        {
            return true;
        }
        return false;
    }
    /**
     * Format is based on currency pair tick value.
     *
     * @param ccy currency
     * @param amount amount
     * @return formatted amount based on currency's tick value
     */
    String formatAmount(final String ccy, final double amount){
        Currency currency = CurrencyFactory.getCurrency(ccy);
        DecimalFormat decimalFormat = currency.getDecimalFormat(DOUBLE_NUMBER_FORMAT);
        /*
            No trailing zeros
         */
        decimalFormat.setMinimumFractionDigits(0);
        return decimalFormat.format(amount);
    }

    private String formatDate(final Date date) {
        SimpleDateFormat format = new SimpleDateFormat(DATE_FORMAT);
        if (displayPreference != null) {
            format.setTimeZone(displayPreference.getTimeZone());
        }
        return format.format(date);
    }

    private String formatDateTime(final Date dateTime) {
        SimpleDateFormat format = new SimpleDateFormat(DATE_TIME_FORMAT);
        if (displayPreference != null) {
            format.setTimeZone(displayPreference.getTimeZone());
        }
        return format.format(dateTime);
    }

    public String getOrderNotes() {
        return orderNotes;
    }

    public void setOrderNotes(String orderNotes) {
        this.orderNotes = orderNotes;
    }

    private String getUnderlyingCcyName(String name){
        Currency currency = CurrencyFactory.getOriginalCurrency(name);
        return currency != null ? currency.getName() : null;
    }

    boolean isNonSpotTrade(){
        if(singleLegTrade != null && singleLegTrade.getClassification() != null){
            return !singleLegTrade.getClassification().equals(ISUtilImpl.TRD_SPOT_CLSF.getShortName());
        }else if(singleLegOrder != null && singleLegOrder.getProductType() != null){
            return !singleLegOrder.getProductType().equals(ProductType.FXSPOT);
        }else {
            return false;
        }
    }

    boolean isNDF()
    {
        if( singleLegTrade != null )
        {
            return ISCommonConstants.TRD_CLSF_FXNDF.equals ( singleLegTrade.getClassification () ) || ISCommonConstants.TRD_CLSF_FXNDF_SWAP.equals ( singleLegTrade.getClassification () );
        }
        else if(singleLegOrder != null && singleLegOrder.getFxRateBasis() != null)
        {
            return singleLegOrder.getFxRateBasis().isNonDeliverable();
        }
        else
        {
            return false;
        }
    }
}
