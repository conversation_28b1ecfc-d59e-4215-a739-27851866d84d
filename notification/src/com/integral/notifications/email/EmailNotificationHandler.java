package com.integral.notifications.email;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.notifications.NotificationConfiguration;
import com.integral.spaces.notification.NHResponse;
import com.integral.spaces.notification.NHResponseCode;
import com.integral.spaces.notification.Notification;
import com.integral.spaces.notification.NotificationHandler;

/**
 * Handles email notifications.
 *
 * <AUTHOR>
 */
public class EmailNotificationHandler implements NotificationHandler {
    private final static Log LOG = LogFactory.getLog(EmailNotificationHandler.class);
    private EmailWorkflowMessageSender emailWorkflowMessageSender;

    public EmailNotificationHandler() {
        emailWorkflowMessageSender = new EmailWorkflowMessageSender("TradingEmail");
        EmailNotificationWorkflowFacade.setEmailWorkflowMessageSender(emailWorkflowMessageSender);
    }

    public EmailNotificationHandler(EmailWorkflowMessageSender emailWorkflowMessageSender) {
        this.emailWorkflowMessageSender = emailWorkflowMessageSender;
        EmailNotificationWorkflowFacade.setEmailWorkflowMessageSender(emailWorkflowMessageSender);
    }


    @Override
    public long getLongIdentifier() {
        return NotificationConfiguration.NOTIFICATION_HANDLER_EMAIL.getLongIdentifier();
    }

    @Override
    public NHResponse handle(Notification notification) {
        try {
            Object notificationCompleteObj = notification.getCompleteObject();
            if (SingleLegTrade.class.isAssignableFrom(notification.getDataObjectType())) {
                EmailNotificationWorkflowFacade.sendWorkflowMessage(notification, (SingleLegTrade) notificationCompleteObj);
            } else {
                LOG.info("Unsupported Email notification object: " + notificationCompleteObj.getClass().getName() + " " + notification.toString());
            }
        } catch (Exception e) {
            LOG.error(notification.toString(), e);
            return new NHResponse(NHResponseCode.FAILURE);
        }
        return new NHResponse(NHResponseCode.SUCCESS);
    }

    @Override
    public NHResponse handleRedelivery(Notification notification,String errorDesc) {
        return handle(notification);
    }
}
