package com.integral.notifications.cptytrd;

import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.spaces.fx.esp.service.CounterPartyTradeService;
import com.integral.is.spaces.fx.esp.service.FXESPServiceFactory;
import com.integral.is.spaces.fx.persistence.ISSpacesPersistenceService;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.CounterpartyTrade;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.notifications.NotificationConfiguration;
import com.integral.spaces.ApplicationSpaceEvent;
import com.integral.spaces.notification.NHResponse;
import com.integral.spaces.notification.NHResponseCode;
import com.integral.spaces.notification.Notification;
import com.integral.spaces.notification.NotificationHandler;

import java.util.ArrayList;
import java.util.List;

public class CptyTradePersistenceHandler implements NotificationHandler {

	private static Log log = LogFactory.getLog( CptyTradePersistenceHandler.class);
	
    protected CounterPartyTradeService counterPartyTradeService = FXESPServiceFactory.getInstance().getCounterPartyTradeService();
    protected ISSpacesPersistenceService isSpacesPersistenceService = PersistenceServiceFactory.getISSpacesPersistenceService();

	public CptyTradePersistenceHandler() {
	}

    @Override
    public long getLongIdentifier() {
        return NotificationConfiguration.NOTIFICATION_HANDLER_CPTY_TRD.getLongIdentifier();
    }

    @Override
    public NHResponse handleRedelivery(Notification notification,String errorDesc) {
        return handle(notification);
    }

    public NHResponse handle(Notification notification) {
        if( log.isDebugEnabled() )
            log.info(notification.toString());
        try {
            if( SingleLegTrade.class.isAssignableFrom(notification.getDataObjectType()))
            {
                ApplicationEventCodes aec = ApplicationEventCodes.fromCode(notification.getAppEventCode());
                SingleLegTrade trd = (SingleLegTrade) notification.getCompleteObject();
                switch (aec){
                    case EVENT_ESP_TRADE_VERIFIED:
                    case EVENT_ESP_TRADE_REJECTED:
                    case EVENT_ESP_TRADE_CREATED_VERIFIED:
                    case EVENT_ESP_TRADE_NET:
                    case EVENT_ESP_TRADE_POST_RATE_VERIFIED:
                    case EVENT_DO_CREATE_MAKER_TRADE:
                        log.info("Creating counterparty trades for trade " + trd.get_id());
                        createCounterpartyTrades(trd);
                        break;

                    case EVENT_ESP_TRADE_AMEND:
                    case EVENT_ESP_NET_TRADE_AMEND:
                    case EVENT_ESP_TRADE_CANCEL:
                    case EVENT_ESP_NET_TRADE_CANCEL:
                        log.info("Updating counterparty trades for trade " + trd.get_id());
                        updateCounterpartyTrades(trd);
                }
            }
        }
        catch ( Exception ex ) {
            log.error("handle : Exception in creating/updating counterparty trades",ex);
            return new NHResponse(NHResponseCode.FAILURE);
        }
        return new NHResponse(NHResponseCode.SUCCESS);
    }

    private List<CounterpartyTrade> createCounterpartyTrades( SingleLegTrade trade ) {
        try {
            List<CounterpartyTrade> cptyTrades = counterPartyTradeService.createCounterPartyTrades( trade );
            if ( cptyTrades != null ) {
                for ( CounterpartyTrade cptyTrd : cptyTrades ) {
                    ApplicationSpaceEvent ase = isSpacesPersistenceService.createEvent(
                        cptyTrd, ApplicationEventCodes.EVENT_ESP_CPTY_TRD_CREATE);
                    ase.setSendNotification(false); // Do not send notification for cpty trade on notification server

                    isSpacesPersistenceService.persist(ase, cptyTrd.getCorrelationId(), cptyTrd.isWarmUpObject(),
                                                       ApplicationEventCodes.EVENT_ESP_CPTY_TRD_CREATE.name());
                }
            }
            return cptyTrades;
        }
        catch ( Exception ex ) {
            log.error( "createCounterpartyTrades : Exception in creating counterparty trades for Trade " + trade.get_id(), ex );
        }
        return null;
    }

    private List<CounterpartyTrade> updateCounterpartyTrades( SingleLegTrade trade ) {
        try {
            List<CounterpartyTrade> updatedCptyTrades = counterPartyTradeService.updateCounterpartyTradesOnStateChange(trade);
            if( updatedCptyTrades != null ){
                for( CounterpartyTrade cptyTrd : updatedCptyTrades ){
                    ApplicationSpaceEvent ase = isSpacesPersistenceService.createEvent(
                        cptyTrd, ApplicationEventCodes.EVENT_ESP_CPTY_TRD_UPDATE);
                    ase.setSendNotification(false); // Do not send notification for cpty trade on notification server

                    isSpacesPersistenceService.persist(ase, cptyTrd.getCorrelationId(), cptyTrd.isWarmUpObject(),
                                                       ApplicationEventCodes.EVENT_ESP_CPTY_TRD_UPDATE.name());
                }
            }
            return updatedCptyTrades;
        }
        catch ( Exception ex ) {
            log.error( "updateCounterpartyTrades : Exception in updating counterparty trades for Trade " + trade.get_id(), ex );
        }
        return null;
    }
}
