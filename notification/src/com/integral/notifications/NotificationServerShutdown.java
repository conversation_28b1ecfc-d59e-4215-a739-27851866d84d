package com.integral.notifications;

import java.util.Hashtable;

import com.integral.cluster.ClusterManagerHelper;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.services.cluster.ClusterManager;
import com.integral.spaces.config.MetaspacesConfigMBean;
import com.integral.spaces.config.MetaspacesConfigMBeanImpl;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.runtime.ShutdownTask;

public class NotificationServerShutdown implements ShutdownTask {

	private static Log log = LogFactory.getLog(NotificationServerShutdown.class.getName());
			
	@Override
	public String shutdown(String aName, Hashtable args) {
		if (MetaspacesConfigMBeanImpl.getInstance()
				.isClusterModeEnabledForNotificationServer()) {
			log.info(ConfigurationFactory.getServerMBean()
					.getVirtualServerName()
					+ " leaving the notification server cluster:" + MetaspacesConfigMBean.NS_CLUSTER_NAME);
			ClusterManagerHelper helper = ClusterManagerHelper.getInstance();
			try {
                helper.stopInstance(MetaspacesConfigMBean.NS_CLUSTER_NAME);
                helper.shutdown();
			} catch (Exception ex) {
				log.error(
						"Exception while leaving the notification server cluster",
						ex);
				return "Exception while leaving the notification server cluster";
			}			
		}
		log.info("Successfully shutdown notification server");
		return "Successfully shutdown notification server";
	}

}
