package com.integral.notifications.clob;

import java.util.ArrayList;
import java.util.List;

import com.integral.SEF.UTIService;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.is.common.GlobalMetrics;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.YMUtil;
import com.integral.is.finance.businessCenter.EndOfDayService;
import com.integral.is.finance.quote.calculator.CreditServiceFacade;
import com.integral.is.finance.quote.calculator.CreditServiceFacadeFactory;
import com.integral.is.finance.quote.calculator.CreditServiceFacadeFactory.CreditServiceType;
import com.integral.is.netting.NetTradeIdGeneratorC;
import com.integral.is.spaces.fx.esp.factory.DealingModelFactory;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.is.spaces.fx.service.ServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.TradeInfo;
import com.integral.model.dealing.DealingModel;
import com.integral.model.dealing.DealingModelRef;
import com.integral.model.dealing.MatchEvent;
import com.integral.model.dealing.MatchEventTimes;
import com.integral.model.dealing.OrderMatchRequest;
import com.integral.model.dealing.OrderRequest;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.model.dealing.State;
import com.integral.model.dealing.TradeEventTimes;
import com.integral.model.dealing.TradeLeg;
import com.integral.model.dealing.descriptor.CoveredTradeDescriptor;
import com.integral.model.dealing.descriptor.OriginatingTradeDescriptor;
import com.integral.notifications.clob.persistence.ClobCache;
import com.integral.notifications.clob.serializer.ClobOrder;
import com.integral.notifications.clob.serializer.ProviderRejectReason;
import com.integral.notifications.clob.util.ClobPersistUtil;
import com.integral.notifications.clob.util.TradeVerifier;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.trade.notification.TradeNotificationUtil;
import com.integral.user.User;

public class SingleLegTradeHandler {

	private final Log log = LogFactory.getLog(SingleLegTradeHandler.class);
	private static final DealingModelFactory factory = DealingModelFactory
			.getInstance();
	private final CoverTradeHandler coverTradeHandler;
	protected Log metricsLog = LogFactory
			.getLog("com.integral.metrics.ClobMetrics");
	protected static String virtualServerName = ConfigurationFactory
			.getServerMBean().getVirtualServerName();

	private final CreditServiceFacade creditServiceFacade;
	private ClobCache cache;

	public SingleLegTradeHandler(ClobCache aCache) {
	    cache = aCache;
		coverTradeHandler = new CoverTradeHandler(aCache);
		creditServiceFacade = CreditServiceFacadeFactory.getInstance(CreditServiceType.MVP);
	}


   public SingleLegTrade createAndUpdateProviderTrade(ClobOrder response,
			OrderMatchRequest singleLegOrderMatch, SingleLegOrder orderRequest){
        MatchEvent.MatchEventLeg matchEventLeg = singleLegOrderMatch.getMatchEventLeg();
        SingleLegOrder order = singleLegOrderMatch.getOrderRequest();

        SingleLegTrade trade = DealingModelFactory.getInstance().newTrade( singleLegOrderMatch );
        TradeLeg tradeLeg = trade.getTradeLeg();
	    trade.set_id(response.getFillTransId());
        trade.setFirstTrade( true );
        //trade.setFirstTradeId(trade.get_id());
        trade.setLegalEntity( response.getCustomerLE() );
        trade.setCounterpartyLegalEntity(response.getCounterPartyLE());
        trade.setTradingParty(singleLegOrderMatch.getTradingParty());
        tradeLeg.setDealtAmount(response.getOrderQty() - response.getFillQty() );
        tradeLeg.setAcceptedDealtCurrencyAmount(tradeLeg.getDealtAmount());
        final double myRate = response.getFillQty() > 0 && response.getFillRate() != 0.0 ? response.getFillRate() : response.getRate();
		double settledAmount = order.getBaseCurrency().round(tradeLeg.getDealtAmount() * myRate);
        tradeLeg.setAcceptedSettledCurrencyAmount( settledAmount );
        tradeLeg.setSettledAmount( settledAmount );
        tradeLeg.setSpotRate ( myRate );
        tradeLeg.setRate(myRate);
        tradeLeg.setSettledAmount(settledAmount);
        tradeLeg.setValueDate( matchEventLeg.getValueDate() );
        switch ( matchEventLeg.getBuySellMode() ) {
            case BUY:
                tradeLeg.setBuyingBaseCurrency( true );
                break;
            case SELL:
                tradeLeg.setBuyingBaseCurrency( false );
                break;
        }
        // download attributes for maker
        trade.getMakerDownloadAttributes().setTakerReferenceId(order.get_id());//:todo figure what should be the value here

        // download attributes for taker
        if ( order.getClientReferenceId() == null ) {
            trade.setTakerRefId( order.get_id() );
            trade.getTakerDownloadAttributes().setTakerReferenceId( order.get_id());
        }
        else {
            trade.setTakerRefId( order.getClientReferenceId() );
            trade.getTakerDownloadAttributes().setTakerReferenceId(order.getClientReferenceId());
        }
        // In case of BA server use coveredOrderId.
        if( order.getCoveredOrderId() != null )
            trade.getTakerDownloadAttributes().setTakerReferenceId( order.getCoveredOrderId());


	   if(trade.getOriginatingTrade() == null){
		   OriginatingTradeDescriptor origTradeDesc = new OriginatingTradeDescriptor();
		   DealingModelRef<SingleLegTrade> origTradeRef = new DealingModelRef<SingleLegTrade>();
		   origTradeDesc.setTradeRef(origTradeRef);
		   trade.setOriginatingTrade( origTradeDesc );
		}

				if(response.getOriginatingLE() != null){
			log.info(new StringBuilder("STH.crtUpdProvTrd: setting origLE passed by ME=").append(response.getOriginatingLE())
					.append(" on trd=").append(trade.get_id()).toString());
					LegalEntity origLE = response.getOriginatingLE();
			OriginatingTradeDescriptor origTradeDesc = trade.getOriginatingTrade();
			DealingModelRef<SingleLegTrade> origTradeRef = origTradeDesc.getTradeRef();
					origTradeRef.setNamespace( origLE.getNamespace() );
			if(response.getOrigOrderId() != null && !response.getOrigOrderId().equals("0")){
				origTradeRef.setUid(response.getOrigOrderId());
			}
			else{
				origTradeRef.setUid(trade.get_id());
			}
					origTradeRef.setUid( response.getOrigOrderId() );
					origTradeDesc.setUser( origLE.getOrganization().getDefaultDealingUser() );
					origTradeDesc.setLegalEntity( origLE );

					CoveredTradeDescriptor coveredTradeDescriptor = new CoveredTradeDescriptor();
					coveredTradeDescriptor.setTradeRef( new DealingModelRef<SingleLegTrade>() );
					DealingModelRef<SingleLegTrade> coveredTradeRef = coveredTradeDescriptor.getTradeRef();
					coveredTradeRef.setNamespace( origLE.getNamespace() );
					coveredTradeRef.setUid( response.getOrigOrderId() );
					coveredTradeDescriptor.setUser( origLE.getOrganization().getDefaultDealingUser() );
					coveredTradeDescriptor.setLegalEntity( origLE );
					trade.setCoveredTrade( coveredTradeDescriptor );
				}
				else{
					DealingModelUtil.populateOriginatingTradeDescriptor( trade.getOriginatingTrade(), trade );
				}

        DealingModelUtil.populateExternalReqTradeDescriptor(trade.getExternalReqTrade(),trade);


        DealingModelUtil.doStateTransition( trade, State.Name.TSINIT, trade.getUser().getFullyQualifiedName() );
        ServiceFactory.getTradeService().newTrade( trade );
        trade.setTradingParty(singleLegOrderMatch.getTradingParty());
        //UTI service validates txn format. Warm-up trades' txnid format is not
        trade.setClassification(ISUtilImpl.TRD_SPOT_CLSF.getShortName());
        if( !trade.isWarmUpObject() )
        	UTIService.intercept(trade);
        trade.setMakerUser(singleLegOrderMatch.getTradingParty()
				.getLegalEntityOrganization().getDefaultDealingUser());
		ClobPersistUtil.updateEventTimes(response, singleLegOrderMatch, trade);
		trade.setRejectionReason(ProviderRejectReason.getRejectReason(response.getProviderRejectReason()));
		trade.setExternalReferenceId( response.getProviderExecId()  != null  && !response.getProviderExecId().isEmpty() ?
				response.getProviderExecId() : response.getFillTransId());

        return trade;
    }
	/*
	public void setOriginatingCoveredDetails_Reject(SingleLegTrade makerTrade, SingleLegOrder takerOrder, ClobOrder takerFill){
		if(takerOrder != null && takerFill != null){
			OriginatingTradeDescriptor tradeDescriptor = new OriginatingTradeDescriptor();
			DealingModelRef<SingleLegTrade> tradeRef = new DealingModelRef<SingleLegTrade>();
			//tradeRef.setUid( takerOrder.get_id() );
			if(takerFill.getCustomerLE() != null){
				tradeRef.setNamespace(takerFill.getCustomerLE().getOrganization().getNamespace().getName());
				tradeDescriptor.setUser(takerFill.getCustomerLE().getOrganization().getDefaultDealingUser());
				tradeDescriptor.setLegalEntity(takerFill.getCustomerLE());
			}
			tradeDescriptor.setDealtAmount(takerFill.getFillQty());
			if(takerFill.getVenueCode() != null) tradeDescriptor.setMaskedName(takerFill.getVenueCode().getName());
			tradeDescriptor.setTradeRef(tradeRef);
			makerTrade.setOriginatingTrade(tradeDescriptor);

			CoveredTradeDescriptor coveredTradeDescriptor = new CoveredTradeDescriptor();
			DealingModelRef<SingleLegTrade> coveredTradeDescriptorTradeRef = new DealingModelRef<SingleLegTrade>();
			coveredTradeDescriptorTradeRef.setNamespace(takerFill.getCustomerLE().getOrganization().getNamespace().getName());
			//coveredTradeDescriptorTradeRef.setUid( takerOrder.get_id() );
			coveredTradeDescriptor.setUser(takerFill.getCustomerLE().getOrganization().getDefaultDealingUser());
			coveredTradeDescriptor.setLegalEntity(takerFill.getCustomerLE());
			SingleLegOrderMatch match = makerTrade.getMatchEvent();
			if( match != null && match.getQuoteForTradeRequest() != null ){
				coveredTradeDescriptor.setStreamId( match.getQuoteForTradeRequest().getStreamId() );
			}
			coveredTradeDescriptor.setTradeRef(coveredTradeDescriptorTradeRef);
			makerTrade.setCoveredTrade(coveredTradeDescriptor);
			StringBuilder sb = new StringBuilder( "STH.setOrigCoveredDetails: setting orig/covered le on maker trade as " )
					.append(takerFill.getCustomerLE().getShortName()).append(" from takerOrder=").append(takerFill.getOrderId())
					.append(" for makerTrade=").append(makerTrade.get_id());
			log.info(sb.toString());
		}
	}*/

	/*
	 * Bi-llateral workflow - if selfMatchPreventionID was not set by OA while
	 * submitting order, CLOB will match order having same customerLE or from
	 * same CustomerOrg
	 *
	 * Case I : Cptyle on response is same as Customerle, or Cptyle org is same
	 * as Customerle org, create intra-floor trade (Order 1 : FI(le1)->CLOB,
	 * Order 2 : FI(le1)->CLOB), (Order 1 : FI(le1)->CLOB, Order 2 :
	 * FI(le2)->CLOB) One Trade between FI and FI(Intra-floor trade).
	 *
	 * Case II : Cptyle on response is different than Customerle, create a trade
	 * between both. (Order 1 : FI1->CLOB, Order 2 : FI2->CLOB) One Trade
	 * between FI1 and FI2.
	 *
	 * Clearing Member workflow Case I : Cptyle on response is same as
	 * Customerle, or Cptyle org is same as Customerle org, CM is same as
	 * CptyCM, create trade between Customer and CM (Order 1 :
	 * FI(le1)->CM->CLOB, Order 2 : FI(le1)->CM->CLOB), (Order 1 :
	 * FI(le1)->CM->CLOB, Order 2 : FI(le2)->CM->CLOB) One Trade between
	 * FI(taker) and CM(maker) and another trade between FI(maker) and
	 * CM(taker).
	 *
	 * Case II : Cptyle on response is different than Customerle, CM is same as
	 * CptyCM, create trade between Customer and CM (Order 1 : FI1->CM->CLOB,
	 * Order 2 : FI2->CM->CLOB) One Trade between FI1(taker) and CM(maker) and
	 * another trade between FI2(maker) and CM(taker).
	 *
	 * Case III : Cptyle on response is different than Customerle, CM is
	 * different than CptyCM, create trade between Customer and CM (Order 1 :
	 * FI1->CM1->CLOB, Order 2 : FI2->CM2->CLOB) One Trade between FI1(taker)
	 * and CM1(maker) and another trade between FI2(maker) and CM2(taker). 3rd
	 * trade between CM1(maker) and CM2(taker)
	 *
	 * No mix and match between Billateral and ClearingMember workflow.
	 */
	public List<SingleLegTrade> createTrades(ClobOrder response,
			OrderMatchRequest matchRequest, SingleLegOrder orderRequest) {

		boolean isBillateral = matchRequest.getTradingParty() == null;
		boolean createTradeBetweenCMs = !isBillateral
				&& isCMTrade(response.getCustomerCM(),
						response.getCptyClearingMemberLE());
		log.info(new StringBuilder("SingleLegTrade.processOrderFill : rid = ")
				.append(response.getFillTransId()).append(", cmTrade = ")
				.append(createTradeBetweenCMs).append(",agg=")
				.append(response.isAggressor()).append(", isPQ=").append(response.isPQOrder()).toString());
		List<SingleLegTrade> trades = new ArrayList<SingleLegTrade>();
		SingleLegTrade customerTrade = createNewCustomerTrade(response, matchRequest);
		updateCustTrade(customerTrade, response, matchRequest, createTradeBetweenCMs, isBillateral, response.getFillTransId(), response.getCpFillTransId());
		// Trade created here is for the last leg. FI1->CM1, FI2->CM2 trade will
		// be between CM1 and CM2. For all other cases its the coveredTrade.
		cache.addTrade(response.getFillId(), customerTrade);
		trades.add(customerTrade);
		if (createTradeBetweenCMs && !response.isOrderNLLPOrigin() && !response.isPQOrder()) {
			response.getMetrics().setCovercreationStart(System.nanoTime());
			SingleLegTrade cover = coverTradeHandler.createTradeWithCover( customerTrade, response, matchRequest, createTradeBetweenCMs, isBillateral);
			response.getMetrics().setCovercreationEnd(System.nanoTime());
			trades.add(cover);
		}

		if (response.isTerminal()) {
			ClobPersistUtil.handleTerminalFillResponse(matchRequest, response, orderRequest);
		}
		log.info("Trades" + trades.size());
		return trades;
	}

	public void setDealingModelFields(final DealingModel dmo) {
		dmo.setCreatedTime(System.currentTimeMillis());
		dmo.setVirtualServer(virtualServerName);
		long currentTradeDate = EndOfDayService.getCurrentTradeDateMillis();
		dmo.setCreatedBusinessDate(currentTradeDate);
	}

	public SingleLegTrade createRejectTrade(ClobOrder response,
			OrderMatchRequest matchRequest)
			{
		SingleLegTrade customerTrade = createNewCustomerTrade(response,
				matchRequest);
		updateCustTrade(customerTrade, response, matchRequest,
				false, false, DealingModelUtil.generateDOTransactionId(),
				response.getCpFillTransId());
		return customerTrade;
			}

	private SingleLegTrade createNewCustomerTrade(ClobOrder response,
			OrderMatchRequest matchEvent) {
		SingleLegTrade myTrade = factory.newTrade(matchEvent);
		myTrade.setLegalEntity(response.getCustomerLE());
		if(response.isPQOrder()) {
			myTrade.setCounterpartyLegalEntity(response.getCounterPartyLE());
		}
		else if (response.getCustomerCM() != null) {
			myTrade.setCounterpartyLegalEntity(response.getCustomerCM());
		} else {
			myTrade.setCounterpartyLegalEntity(response.getCounterPartyLE());
		}
		DealingModelUtil.doStateTransition(myTrade, State.Name.TSVERIFIED,
				myTrade.getUser().getFullyQualifiedName());
		return myTrade;
	}

	public boolean isCMTrade(LegalEntity cm1, LegalEntity cm2) {
		if (cm1 == null || cm2 == null)
			return false;
		return !cm1.isSameAs(cm2);
	}

	public boolean verifyAndUpdatePosition(SingleLegTrade trade,
			OrderMatchRequest matchRequest, boolean createTradeBetweenCMs,
			boolean isBilateral) {
		try {

			new TradeVerifier().verifyTrade(trade);
			log.info("createTradeBetweenCMs:" + createTradeBetweenCMs
					+ ",isBilateral:" + isBilateral + ",trade.isMaker():"
					+ trade.isMaker());
			User myUser = trade.getUser();
			if (myUser != null) {
				IdcSessionContext ctx = IdcSessionManager.getInstance()
						.getSessionContext(myUser);
				IdcSessionManager.getInstance().setSessionContext(ctx);
			} else {
				log.error("Deafult Dealing User of the Customer LE organization not set");
			}
			if (createTradeBetweenCMs) {
				matchRequest.setResponsePending(false);
				if (!trade.isMaker()) {
					TradeNotificationUtil.publishTradeNotification(trade,
							TradeInfo.TradeEvent.VERIFY);
					creditServiceFacade
							.takeCreditSingleLevel(trade);
				}
			} else if (isBilateral) {
				if (!trade.isMaker()) {
					YMUtil.setYMBookName(trade);
					TradeNotificationUtil.publishTradeNotification(trade,
							TradeInfo.TradeEvent.VERIFY);
					creditServiceFacade
					.takeCreditSingleLevel(trade);
				} else {
					YMUtil.setYMBookName(trade);
					TradeNotificationUtil.publishTradeNotification(trade,
							TradeInfo.TradeEvent.VERIFY);
				}
			} else {
				YMUtil.setYMBookName(trade);
				TradeNotificationUtil.publishTradeNotification(trade,
						TradeInfo.TradeEvent.VERIFY);
				creditServiceFacade.takeCreditSingleLevel(
						trade);
			}

		} catch (Exception e) {
			log.error("Exception in processing match verification for match "
					+ matchRequest, e);
		}

		return false;
	}

	private SingleLegTrade updateCustTrade(final SingleLegTrade customerTrade,
			ClobOrder response, OrderMatchRequest customerRequest,
			boolean createTradeBetweenCMs, boolean isBilateral,
			String transactionIdWithfillId, String counterPartyFillId) {

        customerTrade.setVenueAgressorTrade(response.isAggressor());
		setLEsCPsAndIds(customerTrade, response, customerRequest, createTradeBetweenCMs, isBilateral, transactionIdWithfillId, counterPartyFillId);
		customerTrade.setClassification(ISUtilImpl.TRD_SPOT_CLSF.getShortName());
		TradeLeg customerTradeLeg = customerTrade.getTradeLeg();
		customerTradeLeg.setSpotRate ( response.getFillRate() );
		customerTradeLeg.setRate(response.getFillRate());
		customerTradeLeg.setDealtAmount(customerTrade.getDealtCurrency().round( response.getFillQty()));
		MatchEvent.MatchEventLeg customerMatchLeg = customerRequest.getMatchEventLeg();
		customerTradeLeg.setClassification(ISUtilImpl.TRDLEG_SPOT_CLSF.getShortName());
		customerTradeLeg.setValueDate(customerMatchLeg.getValueDate());
		switch (customerMatchLeg.getBuySellMode()) {
		case BUY:
			customerTradeLeg.setBuyingBaseCurrency(true);
			break;
		case SELL:
			customerTradeLeg.setBuyingBaseCurrency(false);
			break;
		case TWO_WAY:
			customerTradeLeg.setBuyingBaseCurrency(false);
			break;
		}

		if(!response.isPQOrder())
		{
			customerTrade.getTakerDownloadAttributes().setMakerReferenceId(
				customerTrade.getExternalReferenceId());
		} else
		{
			customerTrade.getTakerDownloadAttributes().setMakerReferenceId(
					response.getCpFillTransId());
		}

		customerTrade.setFirstTradeId(customerRequest.get_id());
		customerTrade.setLegalEntity(response.getCustomerLE());
		customerTrade.setCreditEntities(customerRequest.getCreditEntities());
		DealingModelUtil.doStateTransition(customerTrade, State.Name.TSINIT, customerTrade.getUser().getFullyQualifiedName());
		ServiceFactory.getTradeService().newTrade(customerTrade);
		customerTrade.setDirectedOrderAggressor(response.isAggressor());
		GlobalMetrics.getInstance().addTrade( customerTrade.getNamespace().getShortName());
		ClobPersistUtil.updateEventTimes(response, customerRequest, customerTrade);
		customerTrade.setRefRateId(response.getRateId());
		DealingModelRef<OrderRequest> orderRequestRef = new DealingModelRef<OrderRequest>();
		customerTrade.setOrderRequestRef(orderRequestRef);
		customerTrade.setClassification(ISUtilImpl.TRD_SPOT_CLSF.getShortName());
		DealingModelUtil.populateDealingModelRef(orderRequestRef, customerRequest.getOrderRequest());
		DealingModelRef<MatchEvent> matchEventRef = new DealingModelRef<MatchEvent>();
		DealingModelUtil.populateDealingModelRef(matchEventRef, customerRequest);

		SingleLegTrade cptyTrade = cache.getTrade( response.getCounterPartyFillId() );
		ClobOrder cptyClobFill = cache.getClobOrderInfo(response.getCptyOrderId());
		if(cptyTrade != null && cptyClobFill == null){
			log.info(new StringBuilder("STH.updateCustTrade: ClobOrderInfo not found for cptyFillId=").append(response.getCounterPartyFillId()).toString());
		}
		if(cptyTrade != null && response.isPQOrder()){
			DealingModelUtil.populateOriginatingTradeDescriptor(customerTrade.getOriginatingTrade(), cptyTrade);
			log.info(new StringBuilder("STH.updateCustTrade: isMakerOrder_LastLook setting custTrade=").append(cptyTrade.get_id())
					.append(" as origTrade for makerTrade=").append(customerTrade.get_id()).toString());
			if(!createTradeBetweenCMs){
				log.info(new StringBuilder("STH.updateCustTrade: isMakerOrder_LastLook setting trade=").append(cptyTrade.get_id())
						.append(" as coveredTrade for makerTrade=").append(customerTrade.get_id()).toString());
				DealingModelUtil.populateCoveredTradeDescriptor(customerTrade.getCoveredTrade(), cptyTrade);
			}
			else{
				// CM2-CM1 trade is not created anymore. So setting CM1-CM2 as covered trade for CM2-LP
				if(cptyTrade.getCoverTradeRef() != null && cptyTrade.getCoverTradeRef().getObject() != null){
					DealingModelUtil.populateCoveredTradeDescriptor(customerTrade.getCoveredTrade(), (SingleLegTrade)cptyTrade.getCoverTradeRef().getObject());
					log.info(new StringBuilder("STH.updateCustTrade: isMakerOrder_LastLook setting trade=")
							.append(((SingleLegTrade)cptyTrade.getCoverTradeRef().getObject()).get_id())
							.append(" as coveredTrade for makerTrade=").append(customerTrade.get_id()).toString());
				}
			}
		}
		else if(cptyTrade != null && cptyClobFill != null && cptyClobFill.isPQOrder()){
			DealingModelUtil.populateOriginatingTradeDescriptor(cptyTrade.getOriginatingTrade(), customerTrade);
			log.info(new StringBuilder("STH.updateCustTrade: isTakerOrder_LastLook setting custTrade=").append(customerTrade.get_id())
					.append(" as origTrade for makerTrade=").append(cptyTrade.get_id()).toString());
			if(!createTradeBetweenCMs){
				log.info(new StringBuilder("STH.updateCustTrade: isTakerOrder_LastLook setting trade=").append(customerTrade.get_id())
						.append(" as coveredTrade for makerTrade=").append(cptyTrade.get_id()).toString());
				DealingModelUtil.populateCoveredTradeDescriptor(cptyTrade.getCoveredTrade(), customerTrade);
			}
		}
		else{
			if(response.getOriginatingLE() != null){
				LegalEntity origLE = response.getOriginatingLE();
				log.info(new StringBuilder("STH.setOrigCvrdDtls: setting origLE=").append(origLE)
						.append(" on Order=").append(customerTrade.get_id()).toString());
				OriginatingTradeDescriptor origTradeDesc = customerTrade.getOriginatingTrade();
				DealingModelRef<SingleLegTrade> origTradeRef = origTradeDesc.getTradeRef();
				origTradeRef.setNamespace( origLE.getOrganization().getNamespace() );
				if(response.getCpFillTransId() != null && !response.getCpFillTransId().equals("0")){
					origTradeRef.setUid(response.getCpFillTransId());
				}
				else{
					origTradeRef.setUid(customerTrade.get_id());
				}
				origTradeDesc.setUser( origLE.getOrganization().getDefaultDealingUser() );
				origTradeDesc.setLegalEntity( origLE );
				customerTrade.setOriginatingTrade( origTradeDesc );
				/*if(response.isPQOrder()){
				CoveredTradeDescriptor coveredTradeDescriptor = new CoveredTradeDescriptor();
				coveredTradeDescriptor.setTradeRef( new DealingModelRef<SingleLegTrade>() );
				DealingModelRef<SingleLegTrade> coveredTradeRef = coveredTradeDescriptor.getTradeRef();
					coveredTradeRef.setNamespace( origLE.getOrganization().getNamespace() );
					//coveredTradeRef.setUid( origOrderDesc.getOrderRequestRef().getUid() );
					coveredTradeDescriptor.setUser( origLE.getOrganization().getDefaultDealingUser() );
					coveredTradeDescriptor.setLegalEntity( origLE );
				customerTrade.setCoveredTrade( coveredTradeDescriptor );
				}*/
			}
		}
		/*else{
		DealingModelUtil.populateOriginatingTradeDescriptor(customerTrade.getOriginatingTrade(), customerTrade);
			log.info(new StringBuilder("STH.updateCustTrade: setting trade as is it's own orig trade=").append(customerTrade.get_id())
					.append(", cptyFillId=").append(response.getCounterPartyFillId()).toString());
			DealingModelUtil.populateCoveredTradeDescriptor(customerTrade.getCoveredTrade(), customerTrade);
			log.info(new StringBuilder("STH.updateCustTrade: setting trade as is it's own covered trade=").append(customerTrade.get_id())
					.append(", cptyFillId=").append(response.getCounterPartyFillId()).toString());
		}*/

		customerTrade.setMatchEventRef(matchEventRef);
		customerTrade.setUser(customerRequest.getOrderRequest().getUser());
		customerTrade.setEmsInitiated(false);
		return customerTrade;
	}

	public void logTradeMetrics(SingleLegTrade trade,
			SingleLegOrder orderRequest) {
		if (DealingModelUtil.isWarmUpObject(trade)
				|| trade.isInternalRejection()) {
			return;
		}
		try {
			TradeEventTimes tradeEventTimes = trade.getEventTimes();
			MatchEventTimes matchEventTimes = trade.getMatchEvent()
					.getMatchEventTimes();
			long rP = matchEventTimes.getAcceptanceSentToIS()
					- matchEventTimes.getAcceptanceReceivedByIS();
			long accISLeg = tradeEventTimes.getAcceptanceSentByAppTime()
					- matchEventTimes.getAcceptanceReceivedByIS();
			long accAdpLeg = tradeEventTimes.getAcceptanceSentByAdaptorTime()
					- tradeEventTimes.getAcceptanceReceivedByAdaptorTime();
			StringBuilder sb = new StringBuilder(100);
			sb.append("cId=").append(trade.getCorrelationId());
			sb.append(",oId=").append(orderRequest.get_id());
			sb.append(",tId=").append(trade.get_id());
			switch (trade.getState().getName()) {
			case TSVERIFIED:
			case TSCONFIRMED:
			case TSPREVERIFIED:
				sb.append(",s=V");
				break;
			case TSREJECTED:
				sb.append(",s=R");
				break;
			}
			sb.append(",rP=").append(rP);
			sb.append(",acc=").append(accISLeg).append(",accAd=")
					.append(accAdpLeg);
			long respAdpLeg = tradeEventTimes.getResponseSentByAdaptorTime()
					- tradeEventTimes.getResponseReceivedByAdaptorTime();
			long respISLeg = tradeEventTimes.getResponseSentByAppTime()
					- tradeEventTimes.getResponseReceivedByAppTime();
			sb.append(",resAd=").append(respAdpLeg).append(",res=")
					.append(respISLeg);
			sb.append(",cs=").append(matchEventTimes.creditTime);
			sb.append(",vt=").append(matchEventTimes.validationTime);
			sb.append(",pp=").append(matchEventTimes.priceProvisionTime);
			metricsLog.info(sb.toString());
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}


	private SingleLegTrade setLEsCPsAndIds(SingleLegTrade customerTrade,
			ClobOrder response, OrderMatchRequest customerRequest,
			boolean createTradeBetweenCMs, boolean isBilateral,
			String transactionIdWithfillId, String counterPartyFillId) {
		if (isBilateral) {
			log.info("Bilateral: " + response.getCustomerLE().getShortName()
					+ " : " + response.getCounterPartyLE().getShortName());
			customerTrade.set_id(transactionIdWithfillId);
			customerTrade.setExternalReferenceId(counterPartyFillId);
            customerTrade.setVenueCptyTradeTxId(counterPartyFillId);
			customerTrade.setMakerOrderId(response
					.getCptyOrderId());
			customerTrade.setMakerTransactionId(counterPartyFillId);
            customerTrade.setVenueTradeTxId(counterPartyFillId);
			DealingModelRef<SingleLegTrade> coverTradeRef = new DealingModelRef<SingleLegTrade>();
			coverTradeRef.setUid(counterPartyFillId);
			customerTrade.setCoverTradeRef(coverTradeRef);
			customerTrade.setLegalEntity(response.getCustomerLE());
			customerTrade.setCounterpartyLegalEntity(response
					.getCounterPartyLE());
			if (!response.isAggressor()) {
				customerTrade.setMaker(true);
				customerTrade.setDOMakerTrade(true);
			} else {
				customerTrade.setDOTakerTrade(true);
			}
			if (response
					.getCounterPartyLE()
					.getOrganization()
					.isSameAs(
							customerRequest.getLegalEntity().getOrganization())) {
				customerTrade.setIntraFloor(true);
				customerTrade.setCounterpartyLegalEntity(response
						.getCounterPartyLE());
			} else {
				log.info("customer Trade Trading Party : "
						+ customerTrade.getTradingParty());
				customerTrade.setTradingParty(response.getCounterPartyLE()
						.getTradingParty(
								customerRequest.getLegalEntity()
										.getOrganization()));
			}
			customerTrade.setMakerUser(response.getCounterPartyLE()
					.getOrganization().getDefaultDealingUser());
		} else if (createTradeBetweenCMs) {
			log.info("CMS: " + response.getCustomerLE().getShortName() + " : "
					+ response.getCustomerCM().getShortName());
			customerTrade.set_id(DealingModelUtil.getDOTransactionIdPrefix()
					+ response.getFillId()
					+ DealingModelUtil.getSOGTransactionIdSuffix());
			customerTrade.setLegalEntity(response.getCustomerLE());
			log.info("Setting Legal Entity Party: "
					+ response.getCustomerLE().getObjectId() + " OID "
					+ response.getOrderId());



			if(response.isPQOrder())
			{
				customerTrade.set_id(DealingModelUtil.getDOTransactionIdPrefix() + response.getFillId() );
				customerTrade.setCounterpartyLegalEntity(response.getCounterPartyLE()); // response.getCounterPartyLE());

			}
			else {
				customerTrade.set_id(DealingModelUtil.getDOTransactionIdPrefix() + response.getFillId() + DealingModelUtil.getSOGTransactionIdSuffix());

				if (response.getCustomerCM() != null)
					customerTrade.setCounterpartyLegalEntity(response
							.getCustomerCM());
			}
			customerTrade.setTradingParty(customerRequest.getTradingParty());
			customerTrade.setMakerUser(customerRequest.getTradingParty()
					.getLegalEntityOrganization().getDefaultDealingUser());
		} else {
			customerTrade.setLegalEntity(response.getCustomerLE());
			if(response.isPQOrder())
			{
				customerTrade.setCounterpartyLegalEntity(response.getCounterPartyLE());
			}
			else if (response.getCustomerCM() != null)
				customerTrade.setCounterpartyLegalEntity(response
						.getCustomerCM());
			customerTrade.setTradingParty(customerRequest.getTradingParty());
			customerTrade.set_id(transactionIdWithfillId);
			customerTrade.setMakerUser(customerRequest.getTradingParty()
					.getLegalEntityOrganization().getDefaultDealingUser());
			if (customerRequest.isNettingRequired()) {
				String netTradeId = NetTradeIdGeneratorC.INSTANCE
						.getOrCreateNetTradeId(
								customerRequest.getOrderRequest(),
								customerTrade);
				customerTrade.setNetTradeId(netTradeId);
			}
		}
		customerTrade.setCounterpartyALEI(CounterpartyUtilC
				.getLEI(customerTrade.getLegalEntity()));
		updateLinking(customerTrade,createTradeBetweenCMs,response,
				transactionIdWithfillId,counterPartyFillId,isBilateral);
		return customerTrade;
	}

	private void updateLinking(SingleLegTrade customerTrade,
			boolean createTradeBetweenCMs,ClobOrder response,
			String transactionIdWithfillId, String counterPartyFillId,
			boolean isBilateral)
			{
        // single CM case, both maker and taker trade to CM will be set
        // diff CM case, only maker side trade to CM will be set
		customerTrade.setFinalVenueTrade(true);
        if(!createTradeBetweenCMs || !response.isAggressor()){
            //setVenueIds( customerTrade, transactionIdWithfillId, response.isPQOrder() ? response.getProviderExecId() :
            	//counterPartyFillId);
            setVenueIds( customerTrade, transactionIdWithfillId, counterPartyFillId,response);
        }
			}

    /**
     * Currently, makerTransactionId is the fillId and externalReferenceId is the cptyFillId.
     * For oracle trade, they transform into: venueTxId=makerTxId, venueCptyTxId=externalRefId
     */
    private void setVenueIds(SingleLegTrade trade, String transactionIdWithfillId, String counterPartyFillId , ClobOrder response){
        trade.setShouldLinkExternalReqIdForTrade( true );
        trade.setVenueTradeTxId(transactionIdWithfillId);
        trade.setVenueCptyTradeTxId(counterPartyFillId);
        trade.setMakerTransactionId( transactionIdWithfillId );
        if(response.isPQOrder())
        trade.setExternalReferenceId(response.getProviderExecId());
        else
        trade.setExternalReferenceId( counterPartyFillId );
    }

}
