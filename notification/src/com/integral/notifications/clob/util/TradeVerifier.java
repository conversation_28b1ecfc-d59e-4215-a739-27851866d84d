package com.integral.notifications.clob.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.trade.configuration.TradeConfigurationFactory;
import com.integral.finance.trade.configuration.TradeConfigurationMBean;
import com.integral.is.ISCommonConstants;
import com.integral.is.finance.businessCenter.EndOfDayService;
import com.integral.is.spaces.fx.esp.service.TradeServiceC;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.*;
import com.integral.session.IdcSessionManager;
import com.integral.time.IdcDate;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.MathUtilC;

public class TradeVerifier {

	private TradeConfigurationMBean config = TradeConfigurationFactory.getTradeConfigurationMBean();
	private static Log log = LogFactory.getLog(TradeServiceC.class);

	protected boolean sessionContextUserHasPermission( String p )
	{
		try
		{
			return ((User) IdcSessionManager.getInstance().getSessionContext().getUser()).hasPermission(p);
		}
		catch ( Exception e )
		{
			LogFactory.getLog(this.getClass()).error("Exception => sessionContextUserHasPermission(" + p + ") returns false.", e);
		}
		return false;
	}

	public void newTrade( SingleLegTrade trade )
	{
		setTradeDate(trade);
		setUPI(trade);
		setLEIs(trade);
	}

	private void setTradeDate( SingleLegTrade trade )
	{
		SingleLegOrderMatch singleLegOrderMatch = trade.getMatchEvent();
		IdcDate tradeDate = EndOfDayService.getCurrentTradeDate();
		FXRateBasis fxRateBasis = singleLegOrderMatch.getFxRateBasis();
		if ( fxRateBasis != null )
		{
			if ( config.isNZDRollTimeRollTradeDateEnabled() )
			{
				IdcDate nzdDate = fxRateBasis.getNZDTradeDate(tradeDate);
				if ( nzdDate != null )
				{
					tradeDate = nzdDate;
				}
			}
		}
		trade.setTradeDate(tradeDate.asJdkDate().getTime());
	}

	public void failTrade( SingleLegTrade trade )
	{
		DealingModelUtil.doStateTransition(trade, State.Name.TSFAILED);
		SingleLegOrderMatch matchEvent = trade.getMatchEvent();
		MatchEvent.MatchEventLeg matchEventLeg = matchEvent.getMatchEventLeg();
		matchEventLeg.setPendingAmount(0);
		updateCoverRateDescriptor(trade);
	}

	public void rejectTrade( SingleLegTrade trade )
	{
		DealingModelUtil.doStateTransition(trade, State.Name.TSREJECTED);
		SingleLegOrderMatch matchEvent = trade.getMatchEvent();
		MatchEvent.MatchEventLeg matchEventLeg = matchEvent.getMatchEventLeg();
		matchEventLeg.setPendingAmount(0);
		updateCoverRateDescriptor(trade);
	}

	public void netTrade( SingleLegTrade nettedTrade, SingleLegTrade parentNetTrade )
	{
		DealingModelRef<SingleLegTrade> parentNetTradeRef = new DealingModelRef<SingleLegTrade>();
		DealingModelUtil.populateDealingModelRef(parentNetTradeRef, parentNetTrade);
		netTrade(nettedTrade, parentNetTrade, parentNetTradeRef);
	}

	public void netTrade( SingleLegTrade nettedTrade, SingleLegTrade parentNetTrade, DealingModelRef<SingleLegTrade> parentNetTradeRef )
	{
		//Add reference to netted trade
		NettedTradeRef tradeRef = new NettedTradeRef();
		DealingModelUtil.populateDealingModelRef(tradeRef, nettedTrade);
		List<NettedTradeRef> nettedTrades = parentNetTrade.getNettedTradeReferences();
		if ( nettedTrades == null )
		{
			nettedTrades = new ArrayList<NettedTradeRef>();
			parentNetTrade.setNettedTradeReferences(nettedTrades);
		}
		nettedTrades.add(tradeRef);
		nettedTrade.setNetTradeRef(parentNetTradeRef);
		//change the state of the netted trade.
		DealingModelUtil.doStateTransition(nettedTrade, State.Name.TSNETTED);
	}

	public void confirmTrade( SingleLegTrade trade )
	{
		// TODO Auto-generated method stub

	}



	public void verifyTrade( SingleLegTrade trade )
	{
		setUPI(trade);
		setLEIs(trade);
		SingleLegOrderMatch matchEvent = trade.getMatchEvent();
		if ( matchEvent.isPreVerificationEnabled() )
		{
			DealingModelUtil.doStateTransition(trade, State.Name.TSPREVERIFIED);
		}
		else
		{
			DealingModelUtil.doStateTransition(trade, State.Name.TSVERIFIED);
		}
        MatchEvent.MatchEventLeg matchEventLeg = matchEvent.getMatchEventLeg();
        TradeLeg tradeLeg = trade.getTradeLeg();
        double tradeAmt = tradeLeg.getDealtAmount();
        double pendingAmount = MathUtilC.subtract( matchEventLeg.getPendingAmount(),tradeAmt );
        matchEventLeg.setPendingAmount(pendingAmount);
        double verifiedAmount = MathUtilC.add(matchEventLeg.getVerifiedDealtCurrencyAmount(),tradeAmt);
        matchEventLeg.setVerifiedDealtCurrencyAmount(verifiedAmount);
        tradeLeg.setAcceptedDealtCurrencyAmount(matchEventLeg.getFinalAcceptanceAmount());
        updateCoverRateDescriptor(trade);
	}


	private void setUPI( SingleLegTrade trade )
	{
		Counterparty counterparty = trade.getLegalEntity();
		Organization organization = counterparty.getOrganization();
		String product = config.getUPIProduct(organization, trade.getClassification());

		String baseCurrencyName = trade.getOrderRequest().getBaseCurrency().getShortName();
		String termCurrencyName = trade.getOrderRequest().getTermCurrency().getShortName();

		StringBuilder builder = new StringBuilder(20).append(baseCurrencyName).append('_').append(termCurrencyName).append('_').append(product);
		trade.setUPI(builder.toString());
	}

	private void setLEIs( SingleLegTrade trade )
	{
		trade.setCounterpartyALEI(CounterpartyUtilC.getLEI(trade.getLegalEntity()));
		LegalEntity cptyB = DealingModelUtil.getCounterPartyLegalEntity(trade);
		if ( cptyB != null )
		{
			trade.setCounterpartyBLEI(CounterpartyUtilC.getLEI(cptyB));
		}
	}


	public void updateCoverRateDescriptor( SingleLegTrade trade )
	{
		//Cover descriptor will be set by the prime broker if prime broker cover trade is enabled
		if ( trade.getMatchEvent().isMultiPrimeBrokerCoverEnabled())
		{
			return;
		}
		LegalEntity counterpartyLE = DealingModelUtil.getCounterPartyLegalEntity(trade);
		//TradingParty tp = trade.getTradingParty();
		if ( counterpartyLE != null )
		{
			TradeLeg tradeLeg = trade.getTradeLeg();
			TradeLeg.CoverRateDescriptor coverRateDescriptor = tradeLeg.getCoverRateDescriptor();
			coverRateDescriptor.setOwner(counterpartyLE.getOrganization());

			// Set the cover rate and ppspot spread, if it's not already set.
			if ( !coverRateDescriptor.isCoverRateUpdated() )
			{
				SingleLegOrderMatch orderMatch = trade.getMatchEvent();
				List<SpreadInfo> spreads = orderMatch.getMatchEventLeg().getSpreadsOnFinalAcceptancePrice();
				double cumulativeSpread = 0.0D;
				if ( spreads != null && !spreads.isEmpty() )
				{
					String tpOrgName = counterpartyLE.getOrganization().getShortName();
					for ( SpreadInfo si : spreads )
					{
						if ( tpOrgName.equals(si.getFromOrgName()) )
						{
							switch ( si.getType() )
							{
							case PP :
								if ( si.isSpreadEnabled() && si.isPreTradeSpread() )
								{
//									cumulativeSpread = cumulativeSpread + si.getSpread();
//									if ( si.isGiveBackAllowed() )
//									{
//										cumulativeSpread = cumulativeSpread - si.getSpreadImprovement();
//									}
									cumulativeSpread =  ( si.isGiveBackAllowed() )? cumulativeSpread + si.getSpreadWithImprovement():cumulativeSpread + si.getSpread();
								}
								break;
							case EXT :
								if ( si.isSpreadEnabled() )
								{
									cumulativeSpread = cumulativeSpread + si.getSpread();
								}
								break;
							}
						}
					}
				}

				cumulativeSpread = MathUtilC.correctFloatingPointsCalculationPrecision(cumulativeSpread);
				coverRateDescriptor.setPPSpotSpread(cumulativeSpread);
				double coverRate = TradeVerifier.removeSpread(tradeLeg.getRate(), cumulativeSpread, !tradeLeg.isBuyingBaseCurrency());
				coverRate = MathUtilC.correctFloatingPointsCalculationPrecision(coverRate);
				coverRateDescriptor.setCoverRate(coverRate);
			}

			// Round the cover execution rate with the precision between the FI and BA.
			if ( coverRateDescriptor.getCoverExecutionRate() > ISCommonConstants.MIN_RATE )
			{
				double coverExecRate = coverRateDescriptor.getCoverExecutionRate();
				String termCurrency = trade.getTermCurrency().getShortName();
				FXRateBasis rateBasis = trade.getFxRateBasis();

				if ( tradeLeg.isBuyingBaseCurrency() )
				{
					coverExecRate = DealingModelUtil.roundSpotRate(rateBasis, termCurrency, coverExecRate, BigDecimal.ROUND_CEILING);
				}
				else
				{
					coverExecRate = DealingModelUtil.roundSpotRate(rateBasis, termCurrency, coverExecRate, BigDecimal.ROUND_FLOOR);
				}

				coverRateDescriptor.setCoverExecutionRate(coverExecRate);
				Double coverExecSpotRate = coverRateDescriptor.getCoverExecutionSpotRate();
				if(coverExecSpotRate != null){
					if ( tradeLeg.isBuyingBaseCurrency() )
					{
						coverExecSpotRate = DealingModelUtil.roundSpotRate(rateBasis, termCurrency, coverExecSpotRate, BigDecimal.ROUND_CEILING);
					}
					else
					{
						coverExecSpotRate = DealingModelUtil.roundSpotRate(rateBasis, termCurrency, coverExecSpotRate, BigDecimal.ROUND_FLOOR);
					}
					coverRateDescriptor.setCoverExecutionSpotRate(coverExecSpotRate);
				}
			}
		}
	}


	  public static double removeSpread( double rate, double spread, boolean bid ) {
	        if ( bid ) {
	            return MathUtilC.add(rate, spread);
	        }
	        else {
	            return  MathUtilC.subtract(rate ,spread);
	        }
	    }


}
