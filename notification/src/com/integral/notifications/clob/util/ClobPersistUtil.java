package com.integral.notifications.clob.util;

import java.math.BigDecimal;

import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.finance.price.fx.FXPrice;
import com.integral.fix.client.FixConstants;
import com.integral.is.ISCommonConstants;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.ISConstantsC;
import com.integral.is.log.MessageLogger;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.MatchEvent;
import com.integral.model.dealing.MatchEventTimes;
import com.integral.model.dealing.OrderMatchRequest;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegOrderMatch;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.model.dealing.State;
import com.integral.model.dealing.OrderRequest.RequestLeg;
import com.integral.model.dealing.TradeEventTimes;
import com.integral.notifications.clob.rmq.ClobListenerMBean;
import com.integral.notifications.clob.rmq.ClobListenerMBeanC;
import com.integral.notifications.clob.serializer.ClobOrder;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.util.MathUtilC;

public class ClobPersistUtil {
	private static final Log log = LogFactory
			.getLog(ClobPersistUtil.class);
	
	public static void updateOrderLeg(RequestLeg requestLeg, ClobOrder clobOrder) {
		if (clobOrder.getAvgRate() != 0)
		requestLeg.setAverageRate(clobOrder.getAvgRate());
		requestLeg.setOriginalSpotRate(clobOrder.getRate());
		requestLeg.setFilledAmount(clobOrder.getFillQty());
		requestLeg.setTotalfilledAmount(clobOrder.getCumQty());
		requestLeg.setRate(clobOrder.getFillRate());
		requestLeg.setBuySellMode(clobOrder.getSide());
		requestLeg.setTenor(FixConstants.SPOT_TENOR.getName());
		requestLeg.setAmount(clobOrder.getOrderQty());
		requestLeg.setSpotRate(clobOrder.getRate());
		log.info("Request Leg filled Amount settled amount total amt "
				+ requestLeg.getFilledAmount() + ":"
				+ requestLeg.getTotalfilledAmount() + ":"
				+ clobOrder.getOrderQty());
	}
	
	
	public static void cancelPartialCoverOrder(OrderMatchRequest matchRequest,
			ClobOrder response, SingleLegOrder orderRequest) {
		MatchEvent.MatchEventLeg matchEventLeg = matchRequest
				.getMatchEventLeg();
		matchEventLeg.setCancelledAmount(response.getOrderQty() - matchEventLeg.getMatchedAmount());
		matchEventLeg.setFinalAcceptanceAmount(matchEventLeg.getMatchedAmount());
		if (validateTerminalAmount(matchRequest)) {
			matchEventLeg.setPendingAmount(0);
			matchRequest.getState().setName(State.Name.RSEXPIRED);
			matchRequest.setExpiratationDone(true);
		}
	}
	
	
	public static void handleTerminalFillResponse(OrderMatchRequest matchRequest,
			ClobOrder response, SingleLegOrder orderRequest) {
		MatchEvent.MatchEventLeg matchEventLeg = matchRequest
				.getMatchEventLeg();
		matchEventLeg.setMatchedAmount(response.getCumQty());
		matchEventLeg.setCancelledAmount(response.getOrderQty() - response.getCumQty());
		matchEventLeg.setFinalAcceptanceAmount(matchEventLeg.getMatchedAmount());
		matchEventLeg.setVerifiedDealtCurrencyAmount(matchEventLeg.getFinalAcceptanceAmount() -
				matchEventLeg.getCancelledAmount());
		orderRequest.getRequestLeg().setFilledAmount(response.getCumQty());
		if (validateTerminalAmount(matchRequest)) {
			matchEventLeg.setPendingAmount(0);
			matchRequest.getState().setName(State.Name.RSEXPIRED);
			matchRequest.setExpiratationDone(true);
		}
	}
	
	public static void handleTerminalFillResponseForCover(OrderMatchRequest matchRequest,
			ClobOrder response, SingleLegOrder orderRequest) {
		MatchEvent.MatchEventLeg matchEventLeg = matchRequest
				.getMatchEventLeg();
		matchEventLeg.setMatchedAmount(response.getFillQty());
		matchEventLeg.setCancelledAmount(response.getOrderQty() - response.getCumQty());
		matchEventLeg
				.setFinalAcceptanceAmount(matchEventLeg.getMatchedAmount());
		matchEventLeg.setVerifiedDealtCurrencyAmount(matchEventLeg.getFinalAcceptanceAmount() -
				matchEventLeg.getCancelledAmount());
		orderRequest.getRequestLeg().setFilledAmount(response.getCumQty());
		matchEventLeg.setPendingAmount(0);
		matchRequest.getState().setName(State.Name.RSEXPIRED);
		matchRequest.setExpiratationDone(true);
	}
	
	public static void updateMatchEvent(SingleLegOrderMatch matchEvent,ClobOrder response) {
		matchEvent.setMultiFill(true);
		FXRateBasis fxRateBasis = matchEvent.getFxRateBasis();
		MatchEvent.MatchEventLeg matchEventLeg = matchEvent.getMatchEventLeg();
		switch (matchEventLeg.getBuySellMode()) {
		case SELL: {
			double spotRate = response.getFillRate();
			if (spotRate > 0.0) {
				spotRate = DealingModelUtil.roundSpotRate(fxRateBasis,
						matchEvent.getTermCurrency().getShortName(), spotRate,
						BigDecimal.ROUND_FLOOR);
				matchEventLeg.setMatchedSpotRate(spotRate);
			} else {
				FXPrice liveMDSRate = getLiveMDSRate(fxRateBasis
						.getCurrencyPair());
				if (liveMDSRate != null)
					matchEventLeg.setMatchedSpotRate(liveMDSRate.getBidFXRate()
							.getSpotRate());
			}
			break;
		}
		case BUY: {
			double spotRate = response.getFillRate();
			if (spotRate > 0.0) {
				spotRate = DealingModelUtil.roundSpotRate(fxRateBasis,
						matchEvent.getTermCurrency().getShortName(), spotRate,
						BigDecimal.ROUND_CEILING);
				matchEventLeg.setMatchedSpotRate(spotRate);
			} else {
				FXPrice liveMDSRate = getLiveMDSRate(fxRateBasis
						.getCurrencyPair());
				if (liveMDSRate != null)
					matchEventLeg.setMatchedSpotRate(liveMDSRate
							.getOfferFXRate().getSpotRate());
			}
			break;
		}
		}
	}
	
	private static FXPrice getLiveMDSRate(CurrencyPair currencyPair) {
		FXMarketDataElement dataElement = ReferenceDataCacheC
				.getInstance()
				.getLiveFXMds()
				.findSpotConversionMarketDataElement(
						currencyPair.getBaseCurrency(),
						currencyPair.getVariableCurrency(), false);
		if (isNotNull(dataElement)) {
			return dataElement.getFXPrice();
		} else {
			dataElement = ReferenceDataCacheC
					.getInstance()
					.getLiveFXMds()
					.findSpotConversionMarketDataElement(
							currencyPair.getBaseCurrency(),
							currencyPair.getVariableCurrency(), true);
			if (isNotNull(dataElement)) {
				// ----------------------------------------------------------------------------------------------------------------------------------------
				// looks like we have inverted rate
				// ----------------------------------------------------------------------------------------------------------------------------------------
				if (log.isDebugEnabled()) {
					log.debug("[PP-LiveFXMDS ] Inverted Price for Currency Pair "
							+ currencyPair.getName()
							+ " found in Live FXMDS , Using inverted Price");
				}
				FXPrice fxPrice = dataElement.getFXPrice();
				if (isNotNull(fxPrice)) {
					return fxPrice.getInverted();
				}
			}
		}
		return null;
	}

	public static boolean isNotNull(Object obj) {
		if (null != obj)
			return true;
		return false;
	}

	
	public static boolean validateTerminalAmount(OrderMatchRequest orderMatchRequest) {
		MatchEvent.MatchEventLeg matchEventLeg = orderMatchRequest
				.getMatchEventLeg();
		if (Math.abs(MathUtilC.subtract(matchEventLeg.getFinalAcceptanceAmount(),
				 MathUtilC.add(matchEventLeg.getVerifiedDealtCurrencyAmount(),
						 MathUtilC.add(matchEventLeg.getPendingRateAmount(), matchEventLeg
							.getCancelledAmount())))) >= MathUtilC.getMinAmount(orderMatchRequest.getDealtCurrency())) {
			MessageLogger.getInstance().log(
					ISAlertMBean.DO_ALERT_EVENT_FILL_MISSING,
					"MVPS",
					new StringBuilder("cumulative mismatch")
							.append(",a=")
							.append(matchEventLeg.getFinalAcceptanceAmount())
							.append("v=")
							.append(matchEventLeg
									.getVerifiedDealtCurrencyAmount())
							.append("pre=")
							.append(matchEventLeg.getPendingRateAmount())
							.append("c=")
							.append(matchEventLeg.getCancelledAmount())
							.toString(), orderMatchRequest.get_id());
			return false;
		}
		return true;
	}
	
	public static void updateEventTimes(ClobOrder response,
			OrderMatchRequest matchRequest, SingleLegTrade trade) {
		long[] timings = response.getTimings();
		TradeEventTimes tradeEventTimes = trade.getEventTimes();
		MatchEventTimes matchEventTimes = matchRequest.getMatchEventTimes();
		matchEventTimes.setOrderMatchedByServer(timings[ISCommonConstants.ME_MATCHING_TIME]);
		tradeEventTimes.setAcceptanceSentByAdaptorTime(timings[ISCommonConstants.ME_ADAP_ORD_SENT_TIME]);
		tradeEventTimes.setResponseReceivedByAdaptorTime(timings[ISCommonConstants.ME_ADAP_RESP_RCVD_TIME]);
		tradeEventTimes.setResponseReceivedByAppTime(timings[ISCommonConstants.ME_RESP_RCVD_TIME]);
	}

	public static String getOrderId(ClobOrder clobOrder){
		String orderId = clobOrder.getOrderId();
		if(!clobOrder.isPQOrder()) return orderId;
		String suffix = ClobListenerMBeanC.getInstance().getProviderOrderIdSuffix();
		if(suffix != null && !suffix.isEmpty() && orderId.endsWith( suffix )){
			return orderId.substring( 0, orderId.length() - suffix.length() );
	}
		return orderId;
	}

	public static boolean isLastLookGatewayOrder(byte orig, byte dest){
		if(orig == 2 && dest == 3) return true;
		return false;
	}

	public static boolean matchedAgainstEMSOrder(byte orig, byte dest){
		if(orig == 0 || dest == 0) return true;
		return false;
	}
}
