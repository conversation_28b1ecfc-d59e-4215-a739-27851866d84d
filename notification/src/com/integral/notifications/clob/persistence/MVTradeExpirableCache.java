package com.integral.notifications.clob.persistence;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.notifications.cache.NotificationCache;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.query.spaces.fx.esp.query.SingleLegTradeQueryService;

/**
 * Created by dasp on 8/15/2017.
 * This class is used to keep the SingleLegTrade objects which are currently used. Least used objects
 * are removed from the cache periodically.
 */
public class MVTradeExpirableCache extends NotificationCache<SingleLegTrade>
{

    private Log log = LogFactory.getLog(MVTradeExpirableCache.class);

    public MVTradeExpirableCache( int cacheSize, long expirationTimeInMS, int concurrencyLevel) {
        super(cacheSize, expirationTimeInMS, concurrencyLevel);
    }

    /**
     * Query spaces to find the SingleLegTrade object.
     * This does a query on primary
     *
     * @param nameSpace of the SingleLegTrade object
     * @param fillId of the SingleLegTrade
     * @param tradeId of the SingleLegTrade
     * @return SingleLegTrade found on success or null upon failure
     */
    private SingleLegTrade queryTrade(String nameSpace, String fillId, String tradeId) {
        SingleLegTrade trade = null;
        SpacesQueryService.QueryResult<SingleLegTrade> result = SingleLegTradeQueryService.getTrade(nameSpace, tradeId);
        if(result.getStatus() == SpacesQueryService.QueryResult.Status.SUCCESS) {
            trade = result.getResult();
        }
        if(trade != null)
            add(fillId, trade);
        else{
            log.info("SingleLegTrade not found in database : getTrade(nameSpace=" + nameSpace + " ,transactionId=" + tradeId + " ). query status=" + result.getStatus().name());
        }
        return trade;
    }

    /**
     * Adds the object to the cache by associating the object in the cache with the incoming key.
     * If the key already exists in the cache the new object will replace the old one.
     *
     * @param trade object to be associated in the cache
     */
    public void addTrade(String fillId, SingleLegTrade trade) {
        if(trade == null){
            log.info("add: received null Trade");
            return;
        }
        add(fillId, trade);
        log.info("Added trade to cache. fillId=" + fillId);
    }

    /**
     *
     * This always queries from Primary
     * @param tradeId
     * @param fillId
     * @param namespaceName
     * @return
     */
    public SingleLegTrade get(String tradeId, String fillId, String namespaceName, boolean onlyCacheLookUp){
        SingleLegTrade trade = get(fillId);
        if (trade == null && !onlyCacheLookUp) {
            log.info("Cache miss, fillId=" + fillId + " - querying database");
            trade = queryTrade(namespaceName, fillId, tradeId);
        }
        else{
            log.info("Cache hit, fillId=" + fillId);
        }
        return trade;
    }

    @Override
    public Class getType() {
        return SingleLegTrade.class;
    }
}
