package com.integral.notifications.clob.persistence;

import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.model.dealing.OrderMatchRequest;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.notifications.clob.SingleLegOrderConvertor;
import com.integral.spaces.ApplicationSpaceEvent;
import com.integral.spaces.DefaultSpaceEvent;

public enum ClobPersistenceFactory {

	INSTANCE;
	boolean testMode = false;
	
	
    public void setTestMode(boolean testMode) {
		this.testMode = testMode;
	}

	public ApplicationSpaceEvent createOrderReqEvent( SingleLegOrder dealingModel, ApplicationEventCodes eventCode )
	  {
	      dealingModel.setModifiedTime(System.currentTimeMillis());
	      dealingModel.incVersionId();
	      ApplicationSpaceEvent ase = new DefaultSpaceEvent();
	      ase.setAppEventCode(eventCode.getCode());
	      ase.setEntity(dealingModel);
	      ase.setObjectVersion(dealingModel.getVersionId());
	      ase.setSerializationView(eventCode.getSerializationView());
	      ase.setSpaceName(eventCode.getSpaceName());
	      ase.setSendNotification(true);
	      return ase;
	  }
	  
	  public ApplicationSpaceEvent createOrderMatchEvent( OrderMatchRequest dealingModel, ApplicationEventCodes eventCode )
	  {
	      dealingModel.setModifiedTime(System.currentTimeMillis());
	      dealingModel.incVersionId();
	      ApplicationSpaceEvent ase = new DefaultSpaceEvent();
	      ase.setAppEventCode(eventCode.getCode());
	      ase.setEntity(dealingModel);
	      ase.setObjectVersion(dealingModel.getVersionId());
	      ase.setSerializationView(eventCode.getSerializationView());
	      ase.setSpaceName(eventCode.getSpaceName());
	      ase.setSendNotification(true);
	      return ase;
	  }
	  
	  public ApplicationSpaceEvent createTradeEvent( SingleLegTrade dealingModel, ApplicationEventCodes eventCode )
	  {
	      dealingModel.setModifiedTime(System.currentTimeMillis());
	      dealingModel.incVersionId();
	      ApplicationSpaceEvent ase = new DefaultSpaceEvent();
	      ase.setAppEventCode(eventCode.getCode());
	      ase.setEntity(dealingModel);
	      ase.setObjectVersion(dealingModel.getVersionId());
	      ase.setSerializationView(eventCode.getSerializationView());
	      ase.setSpaceName(eventCode.getSpaceName());
	      ase.setSendNotification(true);
	      return ase;
	  }
	  
	  
	  public void synchronousPersist(ApplicationSpaceEvent event, String correlationId, boolean isWarmup, String eventString) {
		  if(testMode){
			  
		  }
		  else {
		  ErrorMessage msg1 = PersistenceServiceFactory.getISSpacesPersistenceService()
			.synchronousPersist(event,
					correlationId,
					isWarmup,
					eventString);
		  if(msg1 != null)
		  {
			  log.error(msg1.getErrorMessage() + ":" + msg1.getCode()
					  + ":" + msg1.getErrorCode());
		  }
			  }
			  
		  }
	  
		private static final Log log = LogFactory
				.getLog(ClobPersistenceFactory.class);
}
