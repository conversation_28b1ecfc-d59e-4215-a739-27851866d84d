package com.integral.notifications.clob.persistence;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageHandler;
import com.integral.model.dealing.DealingModel;
import com.integral.model.dealing.DealingModelRef;
import com.integral.model.dealing.OrderMatchRequest;
import com.integral.model.dealing.OrderRequest;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.model.dealing.State;
import com.integral.notifications.clob.rmq.ClobListenerMBeanC;
import com.integral.notifications.clob.serializer.ClobOrder;
import com.integral.persistence.spaces.PersistenceConstants;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.query.spaces.SpacesQueryService.QueryResult;
import com.integral.query.spaces.fx.esp.query.SingleLegOrderQueryService;
import com.integral.query.spaces.fx.esp.query.SingleLegTradeQueryService;
import com.integral.spaces.Metaspace;
import com.integral.spaces.Metaspaces;
import com.integral.spaces.QueryBuilder;
import com.integral.spaces.SpaceIterator;
import com.integral.spaces.serialize.JsonSerializationHandler;
import com.integral.spaces.serialize.SerializationResult;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.util.collections.ConcurrentHashSet;

public class ClobExpirableCache implements ClobCache {

	private static final MVOrderExpirableCache orderCache = (MVOrderExpirableCache) MVCacheFactory.getOrderNotificationCache();
	private static final MVTradeExpirableCache tradeCache = (MVTradeExpirableCache) MVCacheFactory.getTradeNotificationCache();
	private static final MVMatchRequestExpirableCache matchRequestCache = (MVMatchRequestExpirableCache) MVCacheFactory.getMatchRequestNotificationCache();
	private static final MVCoverOrderExpirableCache coverOrderCache = (MVCoverOrderExpirableCache) MVCacheFactory.getCoverOrderNotificationCache();
	private static final MVClobOrderExpirableCache clobOrderCache = (MVClobOrderExpirableCache) MVCacheFactory.getClobOrderNotificationCache();

	private static final ConcurrentHashMap<String, Double> coverTotalFilledAmount = new ConcurrentHashMap<String, Double>(400);
	protected static String virtualServerName = ConfigurationFactory.getServerMBean().getVirtualServerName();

	private static Log logger = LogFactory.getLog(ClobExpirableCache.class);

	private static final ConcurrentHashSet<String> loadedOrdersOnStartup = new ConcurrentHashSet<String>( 400);

	public long getOrderCount() { return orderCache.size(); }

	public long getTradeCount() { return tradeCache.size(); }

	public long getMatchRequestCount() {
		return matchRequestCache.size();
	}

	public void addOrderRequest(SingleLegOrder orderRequest) {
		orderCache.addOrder(orderRequest);
	}

	public void updateOrderRequestCache(SingleLegOrder orderRequest, String originalClientReferenceId) {}

	public SingleLegOrder getOrderRequest(String orderId, String nameSpace) {
		return orderCache.getOrder(orderId, nameSpace, true);
	}

	public boolean containsOrder(String anId, String aNameSpace) {
		logger.info("Contains Order Id " + anId + ":" + orderCache.containsOrder(anId, aNameSpace, true));
		if ( orderCache.containsOrder(anId, aNameSpace, true)) return true;

		Object myObj = getCachedObject( PersistenceServiceFactory.getTradingMetaspaceName(), PersistenceConstants.ORDERREQUEST, aNameSpace, anId, SingleLegOrder.class);
		if (myObj == null) return false;
		SingleLegOrder myOrder = (SingleLegOrder) myObj;
		orderCache.addOrder(myOrder);
		return true;
	}

	public SingleLegOrder getOrderRequestByClientReferenceId( String clientReferenceId, String userShortName, String userOrgShortName) {
		return null;
	}

	public MessageHandler getOrderRequestHandler(String orderId) {
		SingleLegOrder order = orderCache.get(orderId);
		if (order != null) {
			return order.getHandler();
		}
		return null;
	}
	
	public SingleLegTrade getTradeForMatchEvent(OrderMatchRequest orderMatch)
	{
		SpacesQueryService.QueryResult<SpaceIterator<SingleLegTrade>> tradeQueryResult = SingleLegTradeQueryService.getTradesIteratorForOrderMatch(orderMatch.getNamespace().getShortName(), orderMatch.get_id(), true);
        switch (tradeQueryResult.getStatus()) {
            case FAILURE:
                logger.error("getTradeForMatchEvent : Failed to query Trades for Order Match  " + orderMatch.get_id());
                return null;
            case SUCCESS:
                if (tradeQueryResult != null) {
                    SpaceIterator<SingleLegTrade> tradeIterator = tradeQueryResult.getResult();
                    while (tradeIterator.hasNext()) {
                        SingleLegTrade trade = tradeIterator.next();
                        return trade;
                    }
                }
            default:
            return null;
        }
	}

	public void removeOrderRequest(String orderId) {
		orderCache.invalidate( orderId );
	}

	public void addTrade(String fillId, SingleLegTrade trade) {
		tradeCache.addTrade(fillId, trade);
	}

	public SingleLegTrade getTrade(String fillId) {
		return tradeCache.get(fillId);
	}

	public void removeTrade(String fillId) {
		tradeCache.invalidate(fillId);
	}

	public void addMatchRequest(String orderId, OrderMatchRequest matchRequest) {
		matchRequestCache.addMatchRequest(orderId, matchRequest);
	}

	public OrderMatchRequest getMatchRequest(SingleLegOrder order) {
		return matchRequestCache.getMatchRequest(order.get_id(), order, false);
			}
	
	public OrderMatchRequest getMatchRequest(String orderId) {
		return matchRequestCache.getMatchRequest(orderId,null, true);
			}

	public void removeMatchRequest(String orderId) {
		matchRequestCache.invalidate(orderId);
	}

	public void add(String key, SingleLegOrder order) {
		logger.info("Added Order Id " + key + ":" + order.toString());
		orderCache.addOrder( order );
	}

	public SingleLegOrder get(String key) {
		return orderCache.get(key);
	}

	public static Object getCachedObject(String metaspace, String space,
			String namespace, String id, Class cls) {
		com.integral.is.spaces.fx.persistence.DealingModelSpacesCacheManager dmcm = com.integral.is.spaces.fx.persistence.DealingModelSpacesCacheManager
				.getInstance();
		return dmcm.getCachedObject(metaspace, namespace, space, id, cls);
	}

	@Override
	public void addCoverOrderRequest(String customerOrderId, SingleLegOrder coverOrder) {
		coverOrderCache.addOrder(customerOrderId, coverOrder);
	}

	@Override
	public SingleLegOrder getCoverOrderRequestForOrder(SingleLegOrder custOrder, LegalEntity legalEntity) {
		if (legalEntity != null && !loadedOrdersOnStartup.contains(legalEntity.getOrganization().getNamespace().getName())) {
			try {
				loadActiveOrders(legalEntity.getOrganization().getNamespace().getName());
				loadedOrdersOnStartup.add(legalEntity.getOrganization().getNamespace().getName());
			} catch (Exception e) {
				logger.error("Could Not Load Previous Active orders for Prime Broker" + e);
			}
		}
		SingleLegOrder myOrder = coverOrderCache.getCoverOrder(custOrder.get_id());
		if(myOrder == null && legalEntity != null) {
			SingleLegOrder parent = orderCache.getOrder(custOrder.get_id(), legalEntity.getOrganization().getNamespace().getName(), false);
			if(parent != null) {
				QueryResult<SingleLegOrder> queryResult = SingleLegOrderQueryService.queryOrderRequestByCoveredOrderId(custOrder.get_id(), legalEntity.getOrganization().getNamespace().getName());
				 return queryResult.getResult();
			}
		} 
		return myOrder;
	}

	public DealingModel queryDealingModelForMatchRequest(String metaspace, String namespace, Object correlationId, Class<?> cls) {
		String space = PersistenceConstants.MATCHEVENT;
		if (logger.isDebugEnabled()) {
			logger.debug("Quering for Object with details" + metaspace + namespace + space + correlationId + cls);
		}
		try {

			Metaspace ms = Metaspaces.getInstance().getMetaspace(namespace, metaspace);
			QueryBuilder qb = ms.createNewQueryBuilder(namespace, space);
			qb.add(ms.defaultCriteriaSet().is("cid", correlationId));
			DealingModel model = (DealingModel) qb.build().getSingleResult(cls);
			if (logger.isDebugEnabled()) {
				JsonSerializationHandler jsh = new JsonSerializationHandler();
				SerializationResult<ByteBuffer> result = jsh.serializeObject( model, ByteBuffer.allocate(8192));
				logger.debug("Object retrieved" + new String(result.getData().array()));
			}
			return model;
		} catch (Exception ex) {
			logger.error("Error: while retrieving information for ms: "
					+ metaspace + " ns: " + namespace + " spc: " + space
					+ " obj :" + correlationId + " cls: " + cls.getName(), ex);
		}
		return null;
	}

	private void loadActiveOrders(String namespace) throws Exception {
		List<State.Name> states = new ArrayList<State.Name>();
		states.add(State.Name.RSINIT);
		states.add(State.Name.RSPARTIAL);
		SpacesQueryService.QueryResult<SpaceIterator<SingleLegOrder>> queryResult = SingleLegOrderQueryService.getOrderRequestsByState(namespace, states, virtualServerName, true);
		switch (queryResult.getStatus()) {
		case SUCCESS:
			SpaceIterator<SingleLegOrder> spaceIterator = queryResult.getResult();
			if (spaceIterator != null) {
				StringBuilder sb = new StringBuilder(500);
				sb.append("LOADED ACTIVE ORDERS FOR NAMESPACE ").append(namespace).append(" [ ");
				try {
					while (spaceIterator.hasNext()) {
						SingleLegOrder singleLegOrder = spaceIterator.next();
						String coveredOrderId = singleLegOrder.getCoveredOrderId();
						if(coveredOrderId != null)
						{
							coverOrderCache.addOrder(coveredOrderId, singleLegOrder);
							addCoverTotalFilledAmount(singleLegOrder.get_id(), singleLegOrder.getOrderFilledAmount());
						}
						orderCache.addOrder(singleLegOrder);
						
						sb.append(singleLegOrder.get_id());
						sb.append(",");
					}
				} finally {
					spaceIterator.close();
				}
				sb.append("]");
				logger.info(sb.toString());
			}
			return;
		}
		throw new Exception("Failed to Load Active Orders for Namespace "
				+ namespace);
	}

	@Override
	public void addCoverTotalFilledAmount(String anId, Double anAmount) {
		// TODO Auto-generated method stub
		Double totalAmount = anAmount;
		if (coverTotalFilledAmount.containsKey(anId)) {
			totalAmount = coverTotalFilledAmount.get(anId) + anAmount;
		}
		coverTotalFilledAmount.put(anId, totalAmount);
	}
	
	
	@Override
	public Double getCoverTotalFilledAmount(String anId) {
		// TODO Auto-generated method stub
		Double totalAmount = 0.0;
		if (coverTotalFilledAmount.containsKey(anId)) {
			totalAmount = coverTotalFilledAmount.get(anId);
		}
		return totalAmount;
	}

	public void addClobOrderInfo( String orderId, ClobOrder clobOrderInfo){
	 	clobOrderCache.addClobOrderInfo(orderId, clobOrderInfo);
	}
	public ClobOrder getClobOrderInfo(String orderId){
		return clobOrderCache.getClobOrderInfo( orderId );
	}
}
