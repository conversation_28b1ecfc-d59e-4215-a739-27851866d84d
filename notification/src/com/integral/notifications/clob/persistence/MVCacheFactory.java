package com.integral.notifications.clob.persistence;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.notifications.cache.NotificationCache;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by dasp on 8/16/2017.
 */
public class MVCacheFactory {

    private static final Log log = LogFactory.getLog(MVCacheFactory.class);

    private static ConcurrentHashMap<String, NotificationCache> allCaches = new ConcurrentHashMap<String, NotificationCache>();

    public static final String MV_CACHE_KEY_ORDER = "ORDER";
    public static final String MV_CACHE_KEY_TRADE = "TRADE";
    public static final String MV_CACHE_KEY_MATCH_REQUEST = "MATCHREQUEST";
    public static final String MV_CACHE_KEY_COVER_ORDER = "COVERORDER";
    public static final String MV_CACHE_KEY_CLOB_ORDER = "CLOBORDER";
    
    private final static long cacheExpirationTime = MVCacheMBeanImpl.getInstance().getExpirationTimeInMS();
    private final static int cacheSize = MVCacheMBeanImpl.getInstance().getCacheSize();
    private final static int cacheConcurrency = MVCacheMBeanImpl.getInstance().getCacheConcurrency();

    public static NotificationCache getOrderNotificationCache() {
        if (allCaches.get(MV_CACHE_KEY_ORDER) == null) {
            synchronized (MVCacheFactory.class) {
                NotificationCache cache = allCaches.get(MV_CACHE_KEY_ORDER);
                if(cache == null){
                    cache = new MVOrderExpirableCache(cacheSize, cacheExpirationTime, cacheConcurrency);
                    allCaches.put(MV_CACHE_KEY_ORDER, cache);
                }
            }
        }
        return  allCaches.get(MV_CACHE_KEY_ORDER);
    }

    public static NotificationCache getTradeNotificationCache() {
        if (allCaches.get(MV_CACHE_KEY_TRADE) == null) {
            synchronized (MVCacheFactory.class) {
                NotificationCache cache = allCaches.get(MV_CACHE_KEY_TRADE);
                if(cache == null){
                    cache = new MVTradeExpirableCache(cacheSize, cacheExpirationTime, cacheConcurrency);
                    allCaches.put(MV_CACHE_KEY_TRADE, cache);
                }
            }
        }
        return  allCaches.get(MV_CACHE_KEY_TRADE);
    }

    public static NotificationCache getMatchRequestNotificationCache() {
        if (allCaches.get(MV_CACHE_KEY_MATCH_REQUEST) == null) {
            synchronized (MVCacheFactory.class) {
                NotificationCache cache = allCaches.get(MV_CACHE_KEY_MATCH_REQUEST);
                if(cache == null){
                    cache = new MVMatchRequestExpirableCache(cacheSize, cacheExpirationTime, cacheConcurrency);
                    allCaches.put(MV_CACHE_KEY_MATCH_REQUEST, cache);
                }
            }
        }
        return  allCaches.get(MV_CACHE_KEY_MATCH_REQUEST);
    }

    public static NotificationCache getCoverOrderNotificationCache() {
        if (allCaches.get(MV_CACHE_KEY_COVER_ORDER) == null) {
            synchronized (MVCacheFactory.class) {
                NotificationCache cache = allCaches.get(MV_CACHE_KEY_COVER_ORDER);
                if(cache == null){
                    cache = new MVCoverOrderExpirableCache(cacheSize, cacheExpirationTime, cacheConcurrency);
                    allCaches.put(MV_CACHE_KEY_COVER_ORDER, cache);
                }
            }
        }
        return  allCaches.get(MV_CACHE_KEY_COVER_ORDER);
    }

    public static NotificationCache getClobOrderNotificationCache() {
        if (allCaches.get(MV_CACHE_KEY_CLOB_ORDER) == null) {
            synchronized (MVCacheFactory.class) {
                NotificationCache cache = allCaches.get(MV_CACHE_KEY_CLOB_ORDER);
                if(cache == null){
                    cache = new MVClobOrderExpirableCache(cacheSize, cacheExpirationTime, cacheConcurrency);
                    allCaches.put(MV_CACHE_KEY_CLOB_ORDER, cache);
                }
            }
        }
        return  allCaches.get(MV_CACHE_KEY_CLOB_ORDER);
    }
}
