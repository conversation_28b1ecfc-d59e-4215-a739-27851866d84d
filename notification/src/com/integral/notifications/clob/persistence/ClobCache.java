package com.integral.notifications.clob.persistence;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.model.dealing.OrderMatchRequest;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.notifications.clob.serializer.ClobOrder;

public interface ClobCache {
	    public  long getOrderCount();
	    public  long getTradeCount();
	    public  long getMatchRequestCount();
	    public  void addOrderRequest( SingleLegOrder orderRequest) ;
	    public  void updateOrderRequestCache( SingleLegOrder orderRequest, String originalClientReferenceId );
	    public  SingleLegOrder getOrderRequest( String orderId, String nameSpace ) ;
	    public  boolean containsOrder( String anId , String aNameSpace );
	    public  void removeOrderRequest( String orderId );
	    public  void addTrade( String fillId, SingleLegTrade trade );
	    public  SingleLegTrade getTrade( String fillId );
	    public  void removeTrade( String fillId );
	    public  void addMatchRequest(String anId , OrderMatchRequest matchRequest);
	    public  OrderMatchRequest getMatchRequest(SingleLegOrder order);
	    public OrderMatchRequest getMatchRequest(String orderId);
	    public  void removeMatchRequest(String orderId) ;
	    public void add(String key, SingleLegOrder object);
		public SingleLegOrder get(String key) ;
		public void addCoverOrderRequest(String anId,SingleLegOrder orderRequest);
		public void addCoverTotalFilledAmount(String anId,Double anAmount);
		public Double getCoverTotalFilledAmount(String anId);
		public SingleLegTrade getTradeForMatchEvent(OrderMatchRequest event);
		public  SingleLegOrder getCoverOrderRequestForOrder(SingleLegOrder custOrder, LegalEntity aPBNameSpace);
		public void addClobOrderInfo( String orderId, ClobOrder clobOrderInfo);
		public ClobOrder getClobOrderInfo(String orderId);
}
