package com.integral.notifications.clob.persistence;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.notifications.cache.NotificationCache;
import com.integral.notifications.clob.serializer.ClobOrder;

/**
 * Created by dasp on 8/25/2017.
 */
public class MVClobOrderExpirableCache extends NotificationCache<ClobOrder>
{
    private Log log = LogFactory.getLog(MVClobOrderExpirableCache.class);

    public MVClobOrderExpirableCache( int cacheSize, long expirationTimeInMS, int concurrencyLevel) {
        super(cacheSize, expirationTimeInMS, concurrencyLevel);
    }

    /**
     * Adds the object to the cache by associating the object in the cache with the incoming key.
     * If the key already exists in the cache the new object will replace the old one.
     *
     * @param order object to be associated in the cache
     */
    public void addClobOrderInfo(String orderId, ClobOrder order) {
        if(order == null){
            log.info("add: received null Order");
            return;
        }
        add(orderId, order);
        log.info("Added ClobOrderInfo to cache. orderId=" + orderId);
    }

    /**
     *
     * This always queries from Primary
     * @param orderId
     * @return
     */
    public ClobOrder getClobOrderInfo(String orderId){
        ClobOrder order = get(orderId);
        if (order == null) {
            log.info("ClobOrder Cache miss, orderId=" + orderId);
        }
        else{
            log.info("ClobOrder Cache hit, orderId=" + orderId);
        }
        return order;
    }

    @Override
    public Class getType() {
        return ClobOrder.class;
    }
}
