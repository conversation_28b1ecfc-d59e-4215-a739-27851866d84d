package com.integral.notifications.clob.persistence;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.notifications.cache.NotificationCache;

/**
 * Created by dasp on 8/16/2017.
 */
public class MVCoverOrderExpirableCache extends NotificationCache<SingleLegOrder>{

    private Log log = LogFactory.getLog(MVCoverOrderExpirableCache.class);

    public MVCoverOrderExpirableCache( int cacheSize, long expirationTimeInMS, int concurrencyLevel) {
        super(cacheSize, expirationTimeInMS, concurrencyLevel);
    }

    /**
     * Adds the object to the cache by associating the object in the cache with the incoming key.
     * If the key already exists in the cache the new object will replace the old one.
     *
     * @param coverOrder object to be associated in the cache
     */
    public void addOrder(String custOrderId, SingleLegOrder coverOrder) {
        if(coverOrder == null){
            log.info("add: received null Order");
            return;
        }
        add(custOrderId, coverOrder);
        log.info("Added order to cache. custOId=" + custOrderId);
    }

    /**
     *
     * This always queries from Primary
     * @param custOrderId
     * @return
     */
    public SingleLegOrder getCoverOrder( String custOrderId){
        SingleLegOrder order = get(custOrderId);
        if (order == null) {
            log.info("Cache miss, orderId=" + custOrderId);
        }
        else{
            log.info("Cache hit, orderId=" + custOrderId);
        }
        return order;
    }

    @Override
    public Class getType() {
        return SingleLegOrder.class;
    }
}
