package com.integral.notifications.clob.persistence;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.notifications.cache.NotificationCache;
import com.integral.query.spaces.SpacesQueryService;
import com.integral.query.spaces.fx.esp.query.SingleLegOrderQueryService;

/**
 * Created by dasp on 8/16/2017.
 */
public class MVOrderExpirableCache extends NotificationCache<SingleLegOrder> {
    private Log log = LogFactory.getLog(MVOrderExpirableCache.class);

    public MVOrderExpirableCache( int cacheSize, long expirationTimeInMS, int concurrencyLevel) {
        super(cacheSize, expirationTimeInMS, concurrencyLevel);
    }

    /**
     * Query spaces to find the SingleLegOrder object.
     * This does a query on primary
     *
     * @param nameSpace of the SingleLegOrder object
     * @param orderId of the SingleLegOrder
     * @return SingleLegOrder found on success or null upon failure
     */
    private SingleLegOrder queryOrder(String nameSpace, String orderId) {
        SingleLegOrder order = null;
        SpacesQueryService.QueryResult<SingleLegOrder> result = SingleLegOrderQueryService.queryOrderRequestById(nameSpace, orderId, true);
        if(result.getStatus() == SpacesQueryService.QueryResult.Status.SUCCESS) {
            order = result.getResult();
        }
        if(order != null)
            addOrder(order);
        else{
            log.info("SingleLegOrder not found in database : get(nameSpace=" + nameSpace + " ,orderId=" + orderId + " ). query status=" + result.getStatus().name());
        }
        return order;
    }

    /**
     * Adds the object to the cache by associating the object in the cache with the incoming key.
     * If the key already exists in the cache the new object will replace the old one.
     *
     * @param order object to be associated in the cache
     */
    public void addOrder(SingleLegOrder order) {
        if(order == null){
            log.info("add: received null Order");
            return;
        }
        add(order.get_id(), order);
        log.info("Added order to cache. oId=" + order.get_id());
    }

    /**
     *
     * This always queries from Primary
     * @param orderId
     * @param namespace
     * @return
     */
    public SingleLegOrder getOrder(String orderId, String namespace, boolean onlyCacheLookUp){
        SingleLegOrder order = get(orderId);
        if (order == null) {
            log.info("Cache miss, orderId=" + orderId);
            if(!onlyCacheLookUp){
                log.info( "Querying database for oderId=" + orderId );
            if(namespace != null && !namespace.isEmpty()){
            order = queryOrder(namespace, orderId);
                }
            }
        }
        else{
            log.info("Cache hit, orderId=" + orderId);
        }
        return order;
    }

    public boolean containsOrder(String orderId, String nameSpace, boolean onlyCacheLookUp){
        return getOrder(orderId, nameSpace, onlyCacheLookUp) != null;
    }

    @Override
    public Class getType() {
        return SingleLegOrder.class;
    }
}
