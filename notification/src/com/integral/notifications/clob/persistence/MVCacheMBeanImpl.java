package com.integral.notifications.clob.persistence;

import com.integral.system.configuration.IdcMBeanC;

import java.util.concurrent.atomic.AtomicBoolean;
import static com.integral.notifications.clob.persistence.MVCacheConstants.*;

/**
 * Created by dasp on 8/16/2017.
 */
public class MVCacheMBeanImpl  extends IdcMBeanC implements MVCacheMBean {

    private AtomicBoolean initialized;
    private Long expirationTimeInMS;
    private Integer cacheSize;
    private Integer cacheConcurrency;

    private static class ConfigHolder {
        private static MVCacheMBeanImpl INSTANCE = new MVCacheMBeanImpl();
    }

    public static MVCacheMBeanImpl getInstance() {
        return MVCacheMBeanImpl.ConfigHolder.INSTANCE;
    }

    private MVCacheMBeanImpl() {
        super("MVCache");
    }
    @Override
    public void initialize() {
        super.initialize();
        if (initialized == null) {
            initialized = new AtomicBoolean(false);
        }
        if (!initialized.get()) {
            expirationTimeInMS= getLongProperty(MV_CACHE_EXPIRATIONTIME,300000);
            cacheSize= getIntProperty(MV_CACHE_SIZE,1000);
            cacheConcurrency= getIntProperty(MV_CACHE_CONCURRENCY,16);
        }
        initialized.set(true);
    }

    @Override
    public int getCacheSize() {
        return cacheSize;
    }

    @Override
    public long getExpirationTimeInMS() {
        return expirationTimeInMS;
    }

    @Override
    public int getCacheConcurrency() {
        return cacheConcurrency;
    }
}
