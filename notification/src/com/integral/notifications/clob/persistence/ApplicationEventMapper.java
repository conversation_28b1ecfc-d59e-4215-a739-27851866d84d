package com.integral.notifications.clob.persistence;

import java.util.HashMap;
import java.util.Map;

import com.integral.is.common.ApplicationEventCodes;
import com.integral.model.dealing.State;

public class ApplicationEventMapper {

	public static final Map<State.Name,ApplicationEventCodes> orderEventMap
	 = new HashMap<State.Name,ApplicationEventCodes>();
	
	public static final Map<State.Name,ApplicationEventCodes> matchEventMap
	 = new HashMap<State.Name,ApplicationEventCodes>();
	
	static{
		orderEventMap.put(State.Name.RSEXECUTED, ApplicationEventCodes.EVENT_ESP_ORDER_FILLED);
		orderEventMap.put(State.Name.RSPARTIAL, ApplicationEventCodes.EVENT_ESP_ORDER_UPDATE);
		orderEventMap.put(State.Name.RSINIT, ApplicationEventCodes.EVENT_ESP_ORDER_SUBMIT);
		orderEventMap.put(State.Name.RSEXPIRED, ApplicationEventCodes.EVENT_ESP_ORDER_EXPIRE);
		orderEventMap.put(State.Name.RSCANCELLED, ApplicationEventCodes.EVENT_ESP_ORDER_CANCEL);
		orderEventMap.put(State.Name.TSCONFIRMED, ApplicationEventCodes.EVENT_ESP_TRADE_CONFIRMED);
		
		
		matchEventMap.put(State.Name.RSINIT, ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_SUBMIT);
		matchEventMap.put(State.Name.RSEXPIRED, ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_EXPIRE);
		matchEventMap.put(State.Name.RSCANCELLED, ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_CANCEL);
		matchEventMap.put(State.Name.RSDECLINED, ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_REJECT);
		matchEventMap.put(State.Name.RSPARTIAL, ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_SUBMIT);
	}
	
	
	public static ApplicationEventCodes getCodeForOrder(State.Name aName)
	{
		return orderEventMap.get(aName);
	}
	
	public static ApplicationEventCodes getCodeForMatch(State.Name aName)
	{
		return matchEventMap.get(aName);
	}
	
}
