package com.integral.notifications.clob;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.ExecutionFlags;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.fix.client.FixConstants;
import com.integral.is.ISCommonConstants;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.MarketSnapshotUtil;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.finance.businessCenter.EndOfDayService;
import com.integral.is.log.MessageLogger;
import com.integral.is.management.ManagementConstants;
import com.integral.is.management.monitor.spaces.SpacesTradeMonitorMessageBuilder;
import com.integral.is.oms.directedOrders.OrderMatchConfiguration;
import com.integral.is.oms.directedOrders.OrderMatchConfiguration.MatchAttributes;
import com.integral.is.oms.directedOrders.OrderMatchConfigurationFactory;
import com.integral.is.spaces.fx.esp.factory.DealingModelFactory;
import com.integral.is.spaces.fx.esp.matchevent.MatchEventFactory;
import com.integral.is.spaces.fx.esp.priceprovision.FXESPPriceProvisionUtil;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.is.spaces.fx.esp.util.GMNotifierUtil;
import com.integral.is.spaces.fx.service.ServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.management.trade.TradeManagementObject;
import com.integral.management.trade.TradeManagementObjectC;
import com.integral.message.ErrorMessage;
import com.integral.model.dealing.DealingModelRef;
import com.integral.model.dealing.MatchEvent;
import com.integral.model.dealing.MatchEventPQ;
import com.integral.model.dealing.MatchEventPQ.QuoteDescriptor;
import com.integral.model.dealing.OrderMatchRequest;
import com.integral.model.dealing.OrderRequest;
import com.integral.model.dealing.OrderRequest.RequestLeg;
import com.integral.model.dealing.OrderRequest.RequestLeg.BuySellMode;
import com.integral.model.dealing.OrderVisibility;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.model.dealing.State;
import com.integral.model.dealing.descriptor.CoveredOrderRequestDescriptor;
import com.integral.model.dealing.descriptor.CoveredTradeDescriptor;
import com.integral.model.dealing.descriptor.OriginatingOrderRequestDescriptor;
import com.integral.notifications.clob.persistence.ApplicationEventMapper;
import com.integral.notifications.clob.persistence.ClobCache;
import com.integral.notifications.clob.persistence.ClobExpirableCache;
import com.integral.notifications.clob.persistence.ClobPersistenceFactory;
import com.integral.notifications.clob.rmq.ClobListenerMBean;
import com.integral.notifications.clob.rmq.ClobListenerMBeanC;
import com.integral.notifications.clob.serializer.ClobOrder;
import com.integral.notifications.clob.serializer.ProviderRejectReason;
import com.integral.notifications.clob.util.ClobPersistUtil;
import com.integral.notifications.clob.util.ClobProvision;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.spaces.ApplicationSpaceEvent;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.user.Organization;
import com.integral.user.User;

public class SingleLegOrderConvertor {

	public static final int INDEX_NA_INT = -1;
	public static final int INDEX_NA_LONG = -1;
	protected static String virtualServerName = ConfigurationFactory
			.getServerMBean().getVirtualServerName();
	private static final String ORDER_PENDING_VERIFICATION_NAME = "orderPending1";
	private static final String ORDER_UPDATED_NAME = "orderUpdated1";
	private static final String MATCH_NAME = "orderMatchEvent1";
	private static final String MATCH_UPDATED_NAME = "orderMatchEventUpdated1";
	public static final String DEAL_CREATE_EVENT_NAME = "dealCreate1";
	public static final String DEAL_PRE_RATE_VERIFIED_EVENT_NAME = "dealPreRateVerified1";
	public static final String DEAL_VERIFIED_EVENT_NAME = "dealVerified1";
	public static final String DEAL_REJECTED_EVENT_NAME = "dealRejected1";
	public static final String ORDER_EXPIRED_IOC_FOK = "orderExpiredIOCorFOK";

	private ClobCache cache;
	private final SingleLegTradeHandler handler;
	private final ClobPersistenceFactory persistenceFactory;
	private final ClobListenerMBean mBean = ClobListenerMBeanC.getInstance();

	public SingleLegOrderConvertor() {
		cache = new ClobExpirableCache();
		handler = new SingleLegTradeHandler(cache);
		persistenceFactory = ClobPersistenceFactory.INSTANCE;
	}

	public void setCacheForTesting(ClobCache aCache) {
		cache = aCache;
	}

	public void handleOrder(State.Name stateName, ClobOrder anOrder) {
		try{
			if (stateName.equals(State.Name.RSINIT))
				handleInitialOrder(anOrder);
		else if (!cache.containsOrder(anOrder.getOrderId(), getNameSpace(anOrder))) {
			if (stateName.equals(State.Name.RSPARTIAL))
				handleNewCentralOrder(stateName, anOrder);
			else if(stateName.equals(State.Name.RSEXPIRED) || stateName.equals(State.Name.RSCANCELLED)
					&& anOrder.getChannelCode() != ClobOrder.GATEWAY_ORIGIN_MAKER)
				handleTakerExpireReject(stateName, anOrder);
			else if(stateName.equals(State.Name.RSEXCEPTION))
			{
				handleDontKnow(stateName,anOrder);
			}
		} else {
			Organization org = anOrder.getCustomerLE().getOrganization();
			User myUser = org.getDefaultDealingUser();
			if (myUser != null) {
			IdcSessionContext ctx = IdcSessionManager.getInstance()
							.getSessionContext(myUser);
					IdcSessionManager.getInstance().setSessionContext(ctx);
			}
			if(stateName.equals(State.Name.RSEXCEPTION))
			{
				handleDontKnow(stateName,anOrder);
			}
			else {
				handleExistingCentralOrder(stateName, anOrder);
				if((stateName.equals(State.Name.RSEXPIRED) || stateName.equals(State.Name.RSCANCELLED))
						&& anOrder.isPQOrder())
					handleTakerMultiFillReject(stateName, anOrder);
			}
		}
		}
        catch(Exception ex)
		{
			log.error("Failed to handle order",ex);
        	MessageLogger.getInstance().log( ISAlertMBean.ALERT_CLOB_ORDER_HANDLE_FAIL,
					 "SingleLegOrderConvertor.handleOrder()", ex.getMessage(), null);
		}

	}


	public void handleDontKnow(State.Name stateName, ClobOrder anOrder) {
		log.info("Dont Know Trade" + anOrder.getOrderId());
		SingleLegOrder orderRequest = cache.getOrderRequest(anOrder.getOrderId(), getNameSpace(anOrder));
		ClobPersistUtil.updateOrderLeg(orderRequest.getRequestLeg(), anOrder);
		DealingModelUtil.doStateTransition(orderRequest, stateName);
		OrderMatchRequest myReq = cache.getMatchRequest(orderRequest);
		myReq.setResponsePending(true);
		myReq.setRejectReason("DELAYED_DEAL");
		SingleLegTrade oldTrade = cache.getTradeForMatchEvent(myReq);
        DealingModelRef<MatchEvent> matchEventRef = new DealingModelRef<MatchEvent>();
        DealingModelUtil.populateDealingModelRef( matchEventRef, myReq );
        oldTrade.setMatchEventRef( matchEventRef );
		DealingModelRef<OrderRequest> orderRequestRef = new DealingModelRef<OrderRequest>();
		oldTrade.setOrderRequestRef( orderRequestRef );
		DealingModelUtil.populateDealingModelRef( orderRequestRef, orderRequest );
	    GMNotifierUtil gmNotifierUtil = new GMNotifierUtil();
		TradeManagementObject tmo = new TradeManagementObjectC("",virtualServerName);
		tmo.setName(oldTrade.get_id());
		tmo.reInit();
		SpacesTradeMonitorMessageBuilder tradeMessageBuilder = new SpacesTradeMonitorMessageBuilder();
		Map<String, Object> additionalParams = new HashMap<String, Object>(4);
		String transactionId = DealingModelUtil.generateTransactionId(myReq
				.getChannel());
		additionalParams.put(ISConstantsC.DUMMY_TRADE_ID, transactionId);
		String providerTradeId = oldTrade.get_id();
		additionalParams.put(ISConstantsC.PROVIDER_TRADE_ID, providerTradeId);
		double riskAmount = anOrder.getFillQty();
		if (riskAmount != 0.0) {
			additionalParams.put(ISConstantsC.RISK_AMOUNT, riskAmount);
		}
		double acceptedRate = oldTrade.getTradeLeg().getRate();
		if (acceptedRate != 0.0) {
			additionalParams.put(ISConstantsC.ACCEPTED_RATE, acceptedRate);
		}
		try {
			String rejReason = (anOrder.getProviderRejectReason() > 0)
					? ManagementConstants.REJECTED_BY_INTEGRAL_REJECTED : ManagementConstants.REJECTED_BY_INTEGRAL_VERIFIED;
			tradeMessageBuilder.createTradeMessage(tmo, oldTrade,
					rejReason,
					additionalParams);
		} catch (Exception e) {
			log.error(e.getMessage());
			e.printStackTrace();
		}
		// Send risk notification to GM
		log.info("SLO.handleDontKnow: Sending notification to GM for oId=" + orderRequest.get_id());
		gmNotifierUtil.notifyTrade(tmo, new HashMap<String, Object>());

	}

	public void handleTakerMultiFillReject(State.Name stateName, ClobOrder anOrder) {
		if (anOrder.isPQOrder() && anOrder.getProviderRejectReason() == ProviderRejectReason.AUTOCANCEL_TIMED_OUT )
		{
		SingleLegOrder orderRequest = cache.getOrderRequest(anOrder.getOrderId(), getNameSpace(anOrder));
		OrderMatchRequest myReq = cache.getMatchRequest(orderRequest);
			SingleLegTrade	pqTrade = handler.createAndUpdateProviderTrade(anOrder, myReq,orderRequest);
			cache.addMatchRequest(anOrder.getOrderId(), myReq);
			ApplicationSpaceEvent ase8 = persistenceFactory.createTradeEvent( pqTrade, ApplicationEventCodes.EVENT_ESP_TRADE_ACCEPT);
			persistenceFactory.synchronousPersist(ase8, pqTrade.getCorrelationId(), pqTrade.isWarmUpObject(), DEAL_REJECTED_EVENT_NAME);
	        ErrorMessage errorMessage = ServiceFactory.getOrderService().onTradeRejection( orderRequest, pqTrade );
	        ServiceFactory.getTradeService().rejectTrade( pqTrade );

	        	pqTrade.setAutoCancelled(true);
				{
                    GMNotifierUtil gmNotifierUtil = new GMNotifierUtil();

                    TradeManagementObject tmo = new TradeManagementObjectC("",virtualServerName);
                    tmo.setName(pqTrade.get_id());
                    tmo.reInit();

                    SpacesTradeMonitorMessageBuilder builder = new SpacesTradeMonitorMessageBuilder();
                    try {
						builder.createTradeMessage(tmo, pqTrade, ManagementConstants.REJECTED_BY_INTEGRAL_REJECTED, null);
					} catch (Exception e) {
						log.error(e.getMessage());
					}

                    // Send risk notification to GM.
                    gmNotifierUtil.notifyTrade(tmo, null);
				}
				ApplicationSpaceEvent ase9 = persistenceFactory.createTradeEvent( pqTrade, ApplicationEventCodes.EVENT_ESP_TRADE_REJECTED);
				persistenceFactory.synchronousPersist(ase9, pqTrade.getCorrelationId(), pqTrade.isWarmUpObject(), DEAL_REJECTED_EVENT_NAME);
				myReq.getState().setName(stateName);ApplicationSpaceEvent ase6 = persistenceFactory.createOrderMatchEvent(myReq,
								ApplicationEventMapper.getCodeForMatch(stateName));

				persistenceFactory.synchronousPersist(ase6, myReq.getCorrelationId(), myReq.isWarmUpObject(), MATCH_UPDATED_NAME);
				log.info(" Handled multiFill taker Expire"  + orderRequest.get_id());
      	}

	        	}

	public void handleTakerExpireReject(State.Name stateName, ClobOrder anOrder) {
		SingleLegOrder orderRequest = convert(anOrder);
		log.info("handleIOC_FOK_Expire Persisting new Cancel Order " + orderRequest.get_id());
		CoveredOrderRequestDescriptor cord = new CoveredOrderRequestDescriptor();
		DealingModelRef<OrderRequest> myRef1 = new DealingModelRef<OrderRequest>();
		myRef1.setUid(String.valueOf(anOrder.getClOrdId()));
		cord.setOrderRequestRef(myRef1);
		orderRequest.setCoveredOrderRequest(cord);
		OriginatingOrderRequestDescriptor myDesc = orderRequest
				.getOriginatingOrderRequest();
		DealingModelRef<OrderRequest> myRef = new DealingModelRef<OrderRequest>();
		myRef.setUid(anOrder.getClOrdId());
		myDesc.setOrderRequestRef(myRef);

		ApplicationSpaceEvent ase0 = persistenceFactory.createOrderReqEvent(
				orderRequest, ApplicationEventCodes.EVENT_ESP_ORDER_SUBMIT);
		persistenceFactory.synchronousPersist(ase0,
				orderRequest.getCorrelationId(), orderRequest.isWarmUpObject(),
				ORDER_PENDING_VERIFICATION_NAME);
		OrderMatchRequest myReq = newOrderMatchRequest(orderRequest, anOrder);
		log.info("Persisting new MatchEvent " + myReq.get_id());
		ApplicationSpaceEvent ase4 = persistenceFactory.createOrderMatchEvent(
				myReq, ApplicationEventCodes.EVENT_ESP_ORDER_MATCH);
		persistenceFactory.synchronousPersist(ase4, myReq.getCorrelationId(),
				myReq.isWarmUpObject(), MATCH_NAME);

		SingleLegTrade pqTrade = null;


		if (anOrder.isPQOrder())
		{
			pqTrade = handler.createAndUpdateProviderTrade(anOrder,
					myReq,orderRequest);
			cache.addMatchRequest(anOrder.getOrderId(), myReq);
			ApplicationSpaceEvent ase8 = persistenceFactory.createTradeEvent(
					pqTrade, ApplicationEventCodes.EVENT_ESP_TRADE_ACCEPT);
			persistenceFactory.synchronousPersist(ase8, pqTrade.getCorrelationId(),
					pqTrade.isWarmUpObject(), DEAL_REJECTED_EVENT_NAME);
			ErrorMessage errorMessage = ServiceFactory.getOrderService().onTradeRejection( orderRequest, pqTrade );
			ServiceFactory.getTradeService().rejectTrade( pqTrade );
			if(anOrder.getProviderRejectReason() == ProviderRejectReason.AUTOCANCEL_TIMED_OUT)
			{
				pqTrade.setAutoCancelled(true);
				{
					GMNotifierUtil gmNotifierUtil = new GMNotifierUtil();

					TradeManagementObject tmo = new TradeManagementObjectC("",virtualServerName);
					tmo.setName(pqTrade.get_id());
					tmo.reInit();

					SpacesTradeMonitorMessageBuilder builder = new SpacesTradeMonitorMessageBuilder();
					try {
						builder.createTradeMessage(tmo, pqTrade, ManagementConstants.REJECTED_BY_INTEGRAL_REJECTED, null);
					} catch (Exception e) {
						log.error(e.getMessage());
					}

					// Send risk notification to GM.
					gmNotifierUtil.notifyTrade(tmo, null);
				}
			}
			ApplicationSpaceEvent ase9 = persistenceFactory.createTradeEvent(
					pqTrade, ApplicationEventCodes.EVENT_ESP_TRADE_REJECTED);
			persistenceFactory.synchronousPersist(ase9, pqTrade.getCorrelationId(),
					pqTrade.isWarmUpObject(), DEAL_REJECTED_EVENT_NAME);

		}
		else {
			orderRequest.getState().setName(State.Name.RSEXPIRED);
			cache.addOrderRequest(orderRequest);

			myReq.getState().setName(stateName);
			ClobPersistUtil.handleTerminalFillResponse(myReq, anOrder,orderRequest);

			ApplicationSpaceEvent ase6 = persistenceFactory
					.createOrderMatchEvent(myReq,
							ApplicationEventMapper.getCodeForMatch(stateName));
			persistenceFactory.synchronousPersist(ase6,
					myReq.getCorrelationId(), myReq.isWarmUpObject(),
					MATCH_UPDATED_NAME);

		}
		orderRequest.getState().setName(State.Name.RSEXPIRED);
		cache.addOrderRequest(orderRequest);

		ApplicationSpaceEvent ase = persistenceFactory.createOrderReqEvent(
				orderRequest, ApplicationEventCodes.EVENT_ESP_ORDER_EXPIRE);
		persistenceFactory.synchronousPersist(ase,
				orderRequest.getCorrelationId(), orderRequest.isWarmUpObject(),
				ORDER_UPDATED_NAME);

		log.info("handledIOC_FOK_Ex"
				+ "pire Persisting new Cancel Order " + orderRequest.get_id());
	}
	/*
	public void handleTakerExpireRejectLastLook(State.Name stateName, ClobOrder anOrder) {
		SingleLegOrder orderRequest = convert(anOrder);
		log.info("ExpRej_LastLook handling new Cancel Order " + orderRequest.get_id());

		cache.addOrderRequest(orderRequest);
		cache.addClobOrderInfo(ClobPersistUtil.getOrderId( anOrder ), anOrder);

		// idea is to persist lp's fill later than customer's fill. So currently we get lp's fill 1st and then customers. But below code handles both sequences.
		if(!anOrder.isPQOrder()) {

			DealingModelUtil.populateOriginatingOrderDescriptor( orderRequest.getOriginatingOrderRequest(), orderRequest );
			log.info(new StringBuilder("Expire_LastLook.setOrigCoveredDetails: setting order as is it's own orig order=").append(orderRequest.get_id())
					.append(", cptyOrderId=").append(anOrder.getCptyOrderId()).toString());

			ApplicationSpaceEvent ase0 = persistenceFactory.createOrderReqEvent( orderRequest, ApplicationEventCodes.EVENT_ESP_ORDER_SUBMIT );
			persistenceFactory.synchronousPersist( ase0, orderRequest.getCorrelationId(), orderRequest.isWarmUpObject(), ORDER_PENDING_VERIFICATION_NAME );
			OrderMatchRequest myReq = newOrderMatchRequest(orderRequest, anOrder);
			log.info("Persisting new MatchEvent nonPQOrder " + myReq.get_id());
			ApplicationSpaceEvent ase4 = persistenceFactory.createOrderMatchEvent( myReq, ApplicationEventCodes.EVENT_ESP_ORDER_MATCH );
			persistenceFactory.synchronousPersist( ase4, myReq.getCorrelationId(), myReq.isWarmUpObject(), MATCH_NAME );

			orderRequest.getState().setName(State.Name.RSEXPIRED);
			cache.addOrderRequest(orderRequest);
			cache.addClobOrderInfo(anOrder.getFillId(), anOrder);
			myReq.getState().setName(stateName);
			ClobPersistUtil.handleTerminalFillResponse(myReq, anOrder,orderRequest);

			ApplicationSpaceEvent ase6 = persistenceFactory.createOrderMatchEvent(myReq, ApplicationEventMapper.getCodeForMatch(stateName));
			persistenceFactory.synchronousPersist(ase6, myReq.getCorrelationId(), myReq.isWarmUpObject(), MATCH_UPDATED_NAME);
			orderRequest.getState().setName(State.Name.RSEXPIRED);
			ApplicationSpaceEvent ase = persistenceFactory.createOrderReqEvent( orderRequest, ApplicationEventCodes.EVENT_ESP_ORDER_EXPIRE);
			persistenceFactory.synchronousPersist(ase, orderRequest.getCorrelationId(), orderRequest.isWarmUpObject(), ORDER_UPDATED_NAME);
			log.info("ExpRej_LastLook Persisting new Cancel Order " + orderRequest.get_id());
		}

		ClobOrder cptyClobOrder = cache.getClobOrderInfo(anOrder.getCptyOrderId());

		if(cptyClobOrder != null){
			ClobOrder makerClobOrder = cptyClobOrder.isPQOrder() ? cptyClobOrder : anOrder;
			ClobOrder takerClobOrder = cptyClobOrder.isPQOrder() ? anOrder : cptyClobOrder;
			SingleLegOrder takerOrderRequest = cptyClobOrder.isPQOrder() ? orderRequest : cache.getOrderRequest(anOrder.getCptyOrderId(), null);
			SingleLegOrder makerOrderRequest = cptyClobOrder.isPQOrder() ?
					cache.getOrderRequest(makerClobOrder.getOrderId(), null) : orderRequest;

			if(cache.getTrade(makerClobOrder.getFillId()) != null){
				log.info("SLO.ExpRej_LastLook rej trd already persisted for id=" + makerClobOrder.getFillId());
				return;
			}
			if(makerOrderRequest != null && takerOrderRequest != null){
				ApplicationSpaceEvent ase1 = persistenceFactory.createOrderReqEvent( makerOrderRequest, ApplicationEventCodes.EVENT_ESP_ORDER_SUBMIT);
				persistenceFactory.synchronousPersist(ase1, makerOrderRequest.getCorrelationId(), makerOrderRequest.isWarmUpObject(), ORDER_PENDING_VERIFICATION_NAME);
				OrderMatchRequest makerMatchRequest = newOrderMatchRequest(makerOrderRequest, makerClobOrder);
				log.info("Persisting new MatchEvent " + makerMatchRequest.get_id());
				ApplicationSpaceEvent ase2 = persistenceFactory.createOrderMatchEvent(makerMatchRequest, ApplicationEventCodes.EVENT_ESP_ORDER_MATCH);
				persistenceFactory.synchronousPersist(ase2, makerMatchRequest.getCorrelationId(), makerMatchRequest.isWarmUpObject(), MATCH_NAME);

				SingleLegTrade pqTrade = handler.createAndUpdateProviderTrade(makerClobOrder, makerMatchRequest, makerOrderRequest);
				//handler.setOriginatingCoveredDetails_Reject(pqTrade, takerOrderRequest, takerClobOrder);
				cache.addTrade(makerClobOrder.getFillId(), pqTrade);
				cache.addMatchRequest(makerClobOrder.getOrderId(), makerMatchRequest);
				ApplicationSpaceEvent ase8 = persistenceFactory.createTradeEvent( pqTrade, ApplicationEventCodes.EVENT_ESP_TRADE_ACCEPT);
				persistenceFactory.synchronousPersist(ase8, pqTrade.getCorrelationId(), pqTrade.isWarmUpObject(), DEAL_REJECTED_EVENT_NAME);
				ServiceFactory.getOrderService().onTradeRejection( makerOrderRequest, pqTrade );
	        	ServiceFactory.getTradeService().rejectTrade( pqTrade );
				if(makerClobOrder.getProviderRejectReason() == ProviderRejectReason.AUTOCANCEL_TIMED_OUT)
	        	{
	        		pqTrade.setAutoCancelled(true);
                    GMNotifierUtil gmNotifierUtil = new GMNotifierUtil();

                    TradeManagementObject tmo = new TradeManagementObjectC("");
                    tmo.setName(pqTrade.get_id());
                    tmo.reInit();

                    SpacesTradeMonitorMessageBuilder builder = new SpacesTradeMonitorMessageBuilder();
                    try {
						builder.createTradeMessage(tmo, pqTrade, ManagementConstants.REJECTED_BY_INTEGRAL_REJECTED, null);
					} catch (Exception e) {
						log.error(e.getMessage());
					}

                    // Send risk notification to GM.
                    gmNotifierUtil.notifyTrade(tmo, null);
	        	}
				ApplicationSpaceEvent ase9 = persistenceFactory.createTradeEvent( pqTrade, ApplicationEventCodes.EVENT_ESP_TRADE_REJECTED);
				persistenceFactory.synchronousPersist(ase9, pqTrade.getCorrelationId(), pqTrade.isWarmUpObject(), DEAL_REJECTED_EVENT_NAME);
				makerOrderRequest.getState().setName(State.Name.RSEXPIRED);

				ApplicationSpaceEvent ase = persistenceFactory.createOrderReqEvent( makerOrderRequest, ApplicationEventCodes.EVENT_ESP_ORDER_EXPIRE);
				persistenceFactory.synchronousPersist(ase, makerOrderRequest.getCorrelationId(), makerOrderRequest.isWarmUpObject(), ORDER_UPDATED_NAME);
				log.info("handledIOC_FOK_Expire Persisting new Cancel Order " + makerOrderRequest.get_id());
			}
		}
	}*/

	public void handleInitialOrder( ClobOrder anOrder)
	{
		    SingleLegOrder orderRequest = convert(anOrder);
			cache.addOrderRequest(orderRequest);
			cache.addClobOrderInfo(ClobPersistUtil.getOrderId( anOrder ), anOrder);
			log.info("Persisting new Order " + orderRequest.get_id());
			ApplicationSpaceEvent ase = persistenceFactory.createOrderReqEvent(
					orderRequest, ApplicationEventCodes.EVENT_ESP_ORDER_SUBMIT);
			persistenceFactory.synchronousPersist(ase,
					orderRequest.getCorrelationId(), orderRequest.isWarmUpObject(),
					ORDER_PENDING_VERIFICATION_NAME);
			anOrder.getMetrics().setOrderEventSubmitTime(System.nanoTime());
			OrderMatchRequest myReq = newOrderMatchRequest(orderRequest, anOrder);
			log.info("Persisting new MatchEvent " + myReq.get_id());
			ApplicationSpaceEvent ase4 = persistenceFactory.createOrderMatchEvent(
					myReq, ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_SUBMIT);
			anOrder.getMetrics().setOrderMatchEventStart(System.nanoTime());
			persistenceFactory.synchronousPersist(ase4, myReq.getCorrelationId(),
					myReq.isWarmUpObject(), MATCH_NAME);
			cache.addMatchRequest(anOrder.getOrderId(), myReq);
			anOrder.getMetrics().setOrderMatchEventEnd(System.nanoTime());
	}

	public void handleNewCentralOrder(State.Name stateName, ClobOrder anOrder) {
		SingleLegOrder orderRequest = convert(anOrder);
		cache.addOrderRequest(orderRequest);
		cache.addClobOrderInfo(ClobPersistUtil.getOrderId( anOrder ), anOrder);
		log.info("Persisting new Order " + orderRequest.get_id());
		ApplicationSpaceEvent ase = persistenceFactory.createOrderReqEvent(
				orderRequest, ApplicationEventCodes.EVENT_ESP_ORDER_SUBMIT);
		persistenceFactory.synchronousPersist(ase,
				orderRequest.getCorrelationId(), orderRequest.isWarmUpObject(),
				ORDER_PENDING_VERIFICATION_NAME);
		anOrder.getMetrics().setOrderEventSubmitTime(System.nanoTime());
		OrderMatchRequest myReq = newOrderMatchRequest(orderRequest, anOrder);
		log.info("Persisting new MatchEvent " + myReq.get_id());
		ApplicationSpaceEvent ase4 = persistenceFactory.createOrderMatchEvent(
				myReq, ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_SUBMIT);
		anOrder.getMetrics().setOrderMatchEventStart(System.nanoTime());
		persistenceFactory.synchronousPersist(ase4, myReq.getCorrelationId(),
				myReq.isWarmUpObject(), MATCH_NAME);
		cache.addMatchRequest(anOrder.getOrderId(), myReq);
		anOrder.getMetrics().setOrderMatchEventEnd(System.nanoTime());
		for (SingleLegTrade myTrade : newTrades(anOrder, myReq)) {
			boolean isBillateral = myReq.getTradingParty() == null;
			myReq.getState().setName(State.Name.RSEXECUTED);
			verifyConfirmAndPersistTrade(myTrade, isBillateral, anOrder);
			if (anOrder.isTerminal()) {
				log.info("Order Id Terminal " + orderRequest.get_id());
				DealingModelUtil.doStateTransition(orderRequest,
						State.Name.RSEXECUTED);
				ApplicationEventCodes myCode = ApplicationEventCodes.EVENT_ESP_ORDER_FILLED;
				ApplicationSpaceEvent ase5 = persistenceFactory
						.createOrderReqEvent(orderRequest, myCode);
				persistenceFactory.synchronousPersist(ase5,
						orderRequest.getCorrelationId(),
						orderRequest.isWarmUpObject(), ORDER_UPDATED_NAME);
				myReq.getState().setName(State.Name.RSEXPIRED);
				ApplicationSpaceEvent ase6 = persistenceFactory
						.createOrderMatchEvent(
								myReq,
								ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_EXPIRE);

				persistenceFactory.synchronousPersist(ase6,
						myReq.getCorrelationId(), myReq.isWarmUpObject(),
						MATCH_NAME);

			}

		}
	}

	private void setOriginatingCoveredDetails( SingleLegOrder orderRequest, ClobOrder clobOrder){
		if ( orderRequest.getCoveredOrderRequest() == null ) {
			CoveredOrderRequestDescriptor cor = new CoveredOrderRequestDescriptor();
			DealingModelRef<OrderRequest> orref = new DealingModelRef<OrderRequest>();
			cor.setOrderRequestRef(orref);
			orderRequest.setCoveredOrderRequest(cor);
		}
		if ( orderRequest.getOriginatingOrderRequest() == null ) {
			OriginatingOrderRequestDescriptor oor = new OriginatingOrderRequestDescriptor();
			DealingModelRef<OrderRequest> orref = new DealingModelRef<OrderRequest>();
			oor.setOrderRequestRef(orref);
			orderRequest.setOriginatingOrderRequest(oor);
		}
		SingleLegOrder cptyOrder = cache.getOrderRequest(clobOrder.getCptyOrderId(), null );
		if(cptyOrder == null && !clobOrder.isPQOrder()){
			cptyOrder = cache.getOrderRequest(clobOrder.getCptyOrderId()+ClobListenerMBeanC.getInstance().getProviderOrderIdSuffix(), null);
		}
		if(cptyOrder != null){
			if ( cptyOrder.getCoveredOrderRequest() == null ) {
				CoveredOrderRequestDescriptor cor = new CoveredOrderRequestDescriptor();
				DealingModelRef<OrderRequest> orref = new DealingModelRef<OrderRequest>();
				cor.setOrderRequestRef(orref);
				cptyOrder.setCoveredOrderRequest(cor);
			}
			if ( cptyOrder.getOriginatingOrderRequest() == null ) {
				OriginatingOrderRequestDescriptor oor = new OriginatingOrderRequestDescriptor();
				DealingModelRef<OrderRequest> orref = new DealingModelRef<OrderRequest>();
				oor.setOrderRequestRef(orref);
				cptyOrder.setOriginatingOrderRequest(oor);
			}
		}
		ClobOrder cptyClobFill = cache.getClobOrderInfo(clobOrder.getCptyOrderId());
		if(cptyOrder != null && cptyClobFill == null){
			log.info(new StringBuilder("SLO.setOrigCvrdDtls: ClobOrderInfo not found for cptyFillId=").append(clobOrder.getCounterPartyFillId()).toString());
		}
		if(cptyOrder != null && clobOrder.isPQOrder()) {
			DealingModelUtil.populateOriginatingOrderDescriptor( orderRequest.getOriginatingOrderRequest(), cptyOrder );
			log.info(new StringBuilder("SLO.setOrigCvrdDtls: isMakerOrder_LastLook setting custOrder=")
					.append(cptyOrder.get_id()).append(" as origOrder for makerOrder=").append(orderRequest.get_id()).toString());
			DealingModelUtil.populateCoveredOrderDescriptor(orderRequest.getCoveredOrderRequest(), cptyOrder);
			log.info(new StringBuilder("SLO.setOrigCvrdDtls: isMakerOrder_LastLook setting order=").append(cptyOrder.get_id())
					.append(" as coveredOrder for makerOrder=").append(orderRequest.get_id()).toString());
			if(cptyClobFill != null && cptyClobFill.getFillId() != null) {
				SingleLegTrade takerTrade = cache.getTrade(cptyClobFill.getFillId());
				if(takerTrade != null){
					if(orderRequest.getCoveredTrade() == null){
						CoveredTradeDescriptor ct = new CoveredTradeDescriptor();
						DealingModelRef<SingleLegTrade> tradeRef = new DealingModelRef<SingleLegTrade>();
						ct.setTradeRef(tradeRef);
						orderRequest.setCoveredTrade(ct);
					}
					if(clobOrder.getCustomerCM() != null && clobOrder.getCptyClearingMemberLE() != null && !clobOrder.getCustomerCM().isSameAs(clobOrder.getCounterPartyLE())){
						//Todo: service trade btw cm's and set it as coveredtrade on cm-lp order
					}
					else{
						DealingModelUtil.populateCoveredTradeDescriptor(orderRequest.getCoveredTrade(), takerTrade);
					}
				}
			}
		}
		else if(cptyOrder != null && cptyClobFill != null && cptyClobFill.isPQOrder()){
			DealingModelUtil.populateOriginatingOrderDescriptor( cptyOrder.getOriginatingOrderRequest(), orderRequest );
			log.info(new StringBuilder("SLO.setOrigCvrdDtls: isTakerOrder_LastLook setting custOrder=").append(orderRequest.get_id())
					.append(" as origOrder for makerOrder=").append(cptyOrder.get_id()).toString());
			DealingModelUtil.populateCoveredOrderDescriptor(cptyOrder.getCoveredOrderRequest(), orderRequest);
			log.info(new StringBuilder("SLO.setOrigCvrdDtls: isTakerOrder_LastLook setting order=").append(orderRequest.get_id())
					.append(" as coveredOrder for makerOrder=").append(cptyOrder.get_id()).toString());
			DealingModelUtil.populateOriginatingOrderDescriptor( orderRequest.getOriginatingOrderRequest(), orderRequest );
			log.info(new StringBuilder("SLO.setOrigCvrdDtls: setting order as is it's own orig order=").append(orderRequest.get_id())
					.append(", cptyOrderId=").append(clobOrder.getCptyOrderId()).toString());
		}
		else if(clobOrder.getOriginatingLE() != null){
            log.info(new StringBuilder("SLO.setOrigCvrdDtls: setting origLE=").append(clobOrder.getOriginatingLE())
                    .append(" on Order=").append(orderRequest.get_id()).toString());
			OriginatingOrderRequestDescriptor origOrdReqDesc = orderRequest.getOriginatingOrderRequest();
			LegalEntity originatingLE = clobOrder.getOriginatingLE();
			origOrdReqDesc.setLegalEntity(originatingLE);
			origOrdReqDesc.setUser(originatingLE.getOrganization().getDefaultDealingUser());
			DealingModelRef<OrderRequest> myRef = new DealingModelRef<OrderRequest>();
			if(clobOrder.getOrigOrderId() != null && !clobOrder.getOrigOrderId().equals("0")){
				myRef.setUid(clobOrder.getOrigOrderId());
			}
			else{
				myRef.setUid(orderRequest.get_id());
			}
			myRef.setNamespace(originatingLE.getOrganization().getNamespace());
			origOrdReqDesc.setOrderRequestRef(myRef);
		}
		else{
			DealingModelUtil.populateOriginatingOrderDescriptor( orderRequest.getOriginatingOrderRequest(), orderRequest );
			log.info(new StringBuilder("STO.setOrigCvrdDtls: setting order as is it's own orig order=").append(orderRequest.get_id())
					.append(", cptyOrderId=").append(clobOrder.getCptyOrderId()).toString());
		}
	}

	public void handleExistingCentralOrder(State.Name stateName,
			ClobOrder anOrder) {
		log.info("Partial Fill" + anOrder.getOrderId());
		SingleLegOrder orderRequest = cache.getOrderRequest(anOrder.getOrderId(), getNameSpace(anOrder));

		setOriginatingCoveredDetails( orderRequest, anOrder );

		ClobPersistUtil.updateOrderLeg(orderRequest.getRequestLeg(), anOrder);
		DealingModelUtil.doStateTransition(orderRequest, stateName);
		ApplicationEventCodes myCode = ApplicationEventMapper.getCodeForOrder(stateName);
		ApplicationSpaceEvent ase = persistenceFactory.createOrderReqEvent(
				orderRequest, myCode);
		persistenceFactory.synchronousPersist(ase,
				orderRequest.getCorrelationId(), orderRequest.isWarmUpObject(),
				ORDER_UPDATED_NAME);
		OrderMatchRequest myReq = cache.getMatchRequest(orderRequest);

		if (stateName.equals(State.Name.RSPARTIAL)) {
			for (SingleLegTrade myTrade : newTrades(anOrder, myReq)) {
				boolean isBillateral = myReq.getTradingParty() == null;
				verifyConfirmAndPersistTrade(myTrade, isBillateral, anOrder);

			}
			if (anOrder.isTerminal()) {
				log.info("Order Id Terminal Partial " + orderRequest.get_id());
				DealingModelUtil.doStateTransition(orderRequest,
						State.Name.RSEXECUTED);
				ApplicationEventCodes myCode1 = ApplicationEventCodes.EVENT_ESP_ORDER_FILLED;
				ApplicationSpaceEvent ase5 = persistenceFactory
						.createOrderReqEvent(orderRequest, myCode1);
				persistenceFactory.synchronousPersist(ase5,
						orderRequest.getCorrelationId(),
						orderRequest.isWarmUpObject(), ORDER_UPDATED_NAME);
				myReq.getState().setName(State.Name.RSEXPIRED);
				ApplicationSpaceEvent ase6 = persistenceFactory
						.createOrderMatchEvent(
								myReq,
								ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_EXPIRE);

				persistenceFactory.synchronousPersist(ase6,
						myReq.getCorrelationId(), myReq.isWarmUpObject(),
						MATCH_UPDATED_NAME);
				if(cache.getCoverOrderRequestForOrder( orderRequest, anOrder.getCustomerCM()) != null)
				{
					SingleLegOrder cover = cache.getCoverOrderRequestForOrder( orderRequest, anOrder.getCustomerCM());
					double myAmount = cache.getCoverTotalFilledAmount(cover.get_id());
					myAmount = myAmount == 0 ? cover.getRequestLeg().getFilledAmount() : myAmount;
					if(myAmount < orderRequest.getRequestLeg().getAmount())
					{
						DealingModelUtil.doStateTransition(cover, State.Name.RSCANCELLED);
						OrderMatchRequest coverRequest = cache.getMatchRequest(cover);
						ClobPersistUtil.cancelPartialCoverOrder(coverRequest, anOrder,cover);
						cancelCoverOrder(State.Name.RSCANCELLED,orderRequest,anOrder,cover,coverRequest);
					}
				}
			}

		} else {
			log.info("Order Id Cancel/Expire" + orderRequest.get_id());
			final boolean expireMatch = !(anOrder.isPQOrder() && anOrder.getProviderRejectReason() == ProviderRejectReason.AUTOCANCEL_TIMED_OUT);
			DealingModelUtil.doStateTransition(orderRequest, stateName);
			ClobPersistUtil.updateOrderLeg(orderRequest.getRequestLeg(), anOrder);
			ClobPersistUtil.handleTerminalFillResponse(myReq, anOrder,orderRequest);
			ApplicationSpaceEvent ase5 = persistenceFactory
					.createOrderReqEvent(orderRequest, ApplicationEventMapper.getCodeForOrder(stateName));

			persistenceFactory.synchronousPersist(ase5,
					orderRequest.getCorrelationId(),
					orderRequest.isWarmUpObject(), ORDER_UPDATED_NAME);

			if(expireMatch){
			myReq.getState().setName(stateName);
			ApplicationSpaceEvent ase6 = persistenceFactory
					.createOrderMatchEvent(myReq,
							ApplicationEventMapper.getCodeForMatch(stateName));

			persistenceFactory.synchronousPersist(ase6,
					myReq.getCorrelationId(), myReq.isWarmUpObject(),
					MATCH_UPDATED_NAME);
			}
			//Also Cancel any Cover Orders asscoiated with these Orders
			if(cache.getCoverOrderRequestForOrder( orderRequest, anOrder.getCustomerCM()) != null)
			{
				SingleLegOrder cover = cache.getCoverOrderRequestForOrder( orderRequest, anOrder.getCustomerCM());
				DealingModelUtil.doStateTransition(cover, stateName);
				ClobPersistUtil.updateOrderLeg(cover.getRequestLeg(), anOrder);
				OrderMatchRequest coverRequest = cache.getMatchRequest(cover);
				ClobPersistUtil.handleTerminalFillResponse(coverRequest, anOrder,cover);
				cancelCoverOrder(stateName,orderRequest,anOrder,cover,coverRequest);
			}
		}

	}


	private void cancelCoverOrder(State.Name stateName , SingleLegOrder orderRequest , ClobOrder anOrder , SingleLegOrder cover ,
			OrderMatchRequest coverRequest) {


			ApplicationSpaceEvent ase7 = persistenceFactory
					.createOrderReqEvent(cover, ApplicationEventMapper.getCodeForOrder(stateName));

			persistenceFactory.synchronousPersist(ase7,
					cover.getCorrelationId(),
					cover.isWarmUpObject(), ORDER_UPDATED_NAME);

			coverRequest.getState().setName(stateName);
			ApplicationSpaceEvent ase8 = persistenceFactory
					.createOrderMatchEvent(coverRequest,
							ApplicationEventMapper.getCodeForMatch(stateName));

			persistenceFactory.synchronousPersist(ase8,
					coverRequest.getCorrelationId(), coverRequest.isWarmUpObject(),
					MATCH_UPDATED_NAME);
	}

	private void verifyConfirmAndPersistTrade(SingleLegTrade aTrade,
			boolean isBilateral, ClobOrder response) {
		log.info("Persisted Trade Id" + aTrade.get_id());
		boolean createTradeBetweenCMs = !isBilateral
				&& handler.isCMTrade(response.getCustomerCM(),
						response.getCptyClearingMemberLE());
		OrderMatchRequest matchRequestForTrade = (OrderMatchRequest) aTrade
				.getMatchEvent();
		LegalEntity myPBLe = aTrade.getTradingParty() != null ? aTrade
				.getTradingParty().getLegalEntity() : null;
		FXESPPriceProvisionUtil.calculateTradeVerificationRateWithFee(aTrade,
				matchRequestForTrade, response.getFillRate(), myPBLe,
				aTrade.getLegalEntity());
		DealingModelUtil.updateTrade(aTrade, response.getFillQty(),
				response.getValueDate());
		aTrade.getEventTimes().setResponseSentByAppTime(
				System.currentTimeMillis());
		handler.logTradeMetrics(aTrade, matchRequestForTrade.getOrderRequest());
		boolean doEvent = aTrade.getMatchEvent().isPrimeBrokerInitiated()
				|| isBilateral;
		response.getMetrics().setCreditStart(System.nanoTime());
		handler.verifyAndUpdatePosition(aTrade, matchRequestForTrade,
				createTradeBetweenCMs, isBilateral);
		response.getMetrics().setCreditEnd(System.nanoTime());
		response.getMetrics().setTradecreationStart(System.nanoTime());
		if (aTrade.isMaker() && doEvent) {

			ApplicationSpaceEvent ase2 = persistenceFactory.createTradeEvent(
					aTrade,
					ApplicationEventCodes.EVENT_ESP_LIFT_CREATE_MAKER_TRADE);
			aTrade.setVenueAgressorTrade(response.isAggressor());
			persistenceFactory.synchronousPersist(ase2,
					aTrade.getCorrelationId(), aTrade.isWarmUpObject(),
					DEAL_CREATE_EVENT_NAME);

		} else if (!response.isOrderNLLPOrigin()) {
			ApplicationSpaceEvent ase2 = persistenceFactory.createTradeEvent(
					aTrade,
					ApplicationEventCodes.EVENT_ESP_TRADE_CREATED_VERIFIED);
			persistenceFactory.synchronousPersist(ase2,
					aTrade.getCorrelationId(), aTrade.isWarmUpObject(),
					DEAL_CREATE_EVENT_NAME);
			ApplicationSpaceEvent ase3 = persistenceFactory.createTradeEvent(
					aTrade, ApplicationEventCodes.EVENT_ESP_TRADE_CONFIRMED);
			persistenceFactory.synchronousPersist(ase3,
					aTrade.getCorrelationId(), aTrade.isWarmUpObject(),
					DEAL_CREATE_EVENT_NAME);
			log.info("Trade Id Confirmed " + aTrade.get_id());
		}
		response.getMetrics().setTradecreationEnd(System.nanoTime());

	}

	public void updateCache(ClobOrder anOrder) {
		if (anOrder.isTerminal()) {
			cache.removeOrderRequest(anOrder.getOrderId());
			cache.removeMatchRequest(anOrder.getOrderId());
		}
	}

	public String getNameSpace(ClobOrder anOrder) {
		Organization org = anOrder.getCustomerLE() != null ? anOrder.getCustomerLE().getOrganization() : null;
		return org != null ? org.getNamespace().getName() : null;
	}

	public SingleLegOrder convert(ClobOrder clobOrder) {
		SingleLegOrder orderRequest = DealingModelFactory.getInstance()
				.newOrderRequest();
		Organization org = clobOrder.getCustomerLE().getOrganization();
		User myUser = org.getDefaultDealingUser();
		if (myUser != null) {
		IdcSessionContext ctx = IdcSessionManager.getInstance()
						.getSessionContext(myUser);
				IdcSessionManager.getInstance().setSessionContext(ctx);
		}
		orderRequest.setLegalEntity(clobOrder.getCustomerLE());
		orderRequest.set_id(clobOrder.getOrderId());
		orderRequest.setNamespace(org.getNamespace());
		orderRequest.setUser(myUser);
		orderRequest.setCurrencyPair(clobOrder.getCcyPair());
		orderRequest.setDealtCurrency(clobOrder.getCcyPair()
				.getBaseCurrency());
		orderRequest.setType(clobOrder.getOrderType());
		orderRequest.setChannel(mBean.getChannelName(clobOrder.getChannelCode()));
		orderRequest.setRequestChannel(mBean.getChannelName(clobOrder.getChannelCode()));
		orderRequest.setOrganization(org);
		orderRequest.setTimeInForce(clobOrder.getTif());
		FXRateConvention fxRateConvention = QuoteConventionUtilC.getInstance()
				.getDefault();
		FXRateBasis rateBasis = fxRateConvention.getFXRateBasis(orderRequest
				.getCurrencyPair());
		orderRequest.setFxRateBasis(rateBasis);
		RequestLeg requestLeg = orderRequest.getRequestLeg();
		ClobPersistUtil.updateOrderLeg(requestLeg, clobOrder);
		requestLeg.setValueDate(EndOfDayService
				.getValueDate(rateBasis, EndOfDayService.getCurrentTradeDate())
				.asJdkDate().getTime());

		setOriginatingCoveredDetails( orderRequest, clobOrder );

		OrderVisibility myVis = new OrderVisibility();
		OrderVisibility.Type myVisType = OrderVisibility.Type.DISPLAY;
		if (clobOrder.getMaxShow() == 0)
			myVisType = OrderVisibility.Type.HIDDEN;
		if (clobOrder.getMaxShow() < clobOrder.getOrderQty())
			myVisType = OrderVisibility.Type.ICEBERG;
		myVis.setType(myVisType);
		orderRequest.setOrderVisibilityType(myVisType);
		orderRequest.setCorrelationId(clobOrder.getOrderId());
		orderRequest.setTransactionId(clobOrder.getFillTransId());
		orderRequest.setEmsInitiated(false);
		orderRequest.setClientReferenceId(clobOrder.getClOrdId());
		orderRequest.getOrderRequestEventTimes().setReceivedTime(clobOrder.getArrivalTime());
		orderRequest.getOrderRequestEventTimes().setSubmissionTime(clobOrder.getArrivalTime());
		orderRequest.setExecutionFlags(orderRequest.getExecutionFlags()
				| ExecutionFlags.RISKNET_DIRECTED_ORDER);

		return orderRequest;
	}

	public OrderMatchRequest newOrderMatchRequest(SingleLegOrder orderRequest,
			ClobOrder anOrder) {
		OrderMatchRequest matchEvent = MatchEventFactory
				.newOrderMatchRequest(true); // assume it's directed order
		if (DealingModelUtil.isWarmUpObject(orderRequest)) {
			DealingModelUtil.setAsWarmUpObject(matchEvent);
		}

		handler.setDealingModelFields(matchEvent);
		matchEvent.getDirectedOrderInfo().setTradingVenue(
				anOrder.getVenue());
		matchEvent.setCorrelationId(orderRequest.getCorrelationId());
		matchEvent.setNamespace(orderRequest.getNamespace());
		matchEvent.setEmsInitiated(orderRequest.isEmsInitiated());
		matchEvent.setTimeToLive(orderRequest.getTimeToLive());
		matchEvent.setLegalEntity(orderRequest.getLegalEntity());
		matchEvent.setGridMidRate(anOrder.getRefPrice());
		matchEvent.setMarketSnapshot(createNewMarketSnapshot( orderRequest,anOrder, BuySellMode.SELL==anOrder.getSide()?true:false));
		DealingModelUtil.populateDealingModelRef(
				matchEvent.getOrderRequestRef(), orderRequest);
		///matchEvent.set_id(orderRequest.get_id());
		matchEvent.setFxRateBasis(orderRequest.getFxRateBasis());
		List<MatchEvent.MatchEventLeg> matchEventLegList = new ArrayList<MatchEvent.MatchEventLeg>();
		MatchEvent.MatchEventLeg myLeg = new MatchEvent.MatchEventLeg();
		long myTime = anOrder.getValueDate().getTime();
		if(myTime != 0)
		myLeg.setValueDate(myTime);
		else
	    myLeg.setValueDate(EndOfDayService
				.getValueDate(orderRequest.getFxRateBasis(),
						EndOfDayService.getCurrentTradeDate()).asJdkDate()
				.getTime());
		myLeg.setTenor(FixConstants.SPOT_TENOR.getName());
		MatchEvent.MatchEventLeg.BuySellMode myMode = orderRequest
				.getRequestLeg().getBuySellMode() != null ? orderRequest
				.getRequestLeg().getBuySellMode()
				.equals(OrderRequest.RequestLeg.BuySellMode.BUY) ? MatchEvent.MatchEventLeg.BuySellMode.BUY
				: MatchEvent.MatchEventLeg.BuySellMode.SELL
				: MatchEvent.MatchEventLeg.BuySellMode.TWO_WAY;
		myLeg.setBuySellMode(myMode);
		myLeg.setMatchedAmount(orderRequest.getOrderAmount());
		myLeg.setCancelledAmount(0);
		myLeg.setFinalAcceptanceAmount(orderRequest.getOrderAmount() );
		myLeg.setMatchedSpotRate(anOrder.getFillRate());
		myLeg.setMatchRate(anOrder.getRate());
		matchEventLegList.add(myLeg);
		matchEvent.setMatchEventLegs(matchEventLegList);
		DealingModelRef<OrderRequest> orderRequestRef = new DealingModelRef<OrderRequest>();
		orderRequestRef.setObject(orderRequest);
		orderRequestRef.setUid(orderRequest.get_id());
		orderRequestRef.setNamespace(orderRequest.getNamespace());
		matchEvent.setOrderRequestRef(orderRequestRef);
		if (anOrder.isPQOrder()) {
			final MatchEventPQ mPq = new MatchEventPQ();
			final QuoteDescriptor.Type qType = anOrder.getQuoteType() == ClobOrder.MULTI_QUOTE ?
					QuoteDescriptor.Type.MULTI_QUOTE : QuoteDescriptor.Type.MULTI_TIER;
			matchEvent.setMatchEventPQ(mPq);
			final QuoteDescriptor qd = mPq.getMatchedQuote();
			if (anOrder.getStreamLE() != null)
				qd.setStreamId(anOrder.getStreamLE().getShortName());
			qd.setLegalEntity(anOrder.getCounterPartyLE());
			qd.setOrganization(anOrder.getCounterPartyLE().getOrganization());
			qd.setType(qType);
			qd.setQuoteId(Long.toString(anOrder.getQuoteId()));
			final QuoteDescriptor qd1 = mPq.getQuoteForTradeRequest();
			if (anOrder.getStreamLE() != null)
				qd1.setStreamId(anOrder.getStreamLE().getShortName());
			qd1.setLegalEntity(anOrder.getCounterPartyLE());
			qd1.setOrganization(anOrder.getCounterPartyLE().getOrganization());
			qd1.setType(qType);
			qd.setQuoteId(Long.toString(anOrder.getQuoteId()));

		}
		log.info("Org Venue" + anOrder.getVenue().getShortName());
		ClobProvision prov = new ClobProvision(orderRequest.getLegalEntity(),
				anOrder.getVenue(), orderRequest.getCurrencyPair());
		OrderMatchConfiguration omConfig = OrderMatchConfigurationFactory
				.borrowOrderMatchConfiguration(orderRequest);
		prov.build(anOrder);
		prov.populateAndValidate(omConfig);
		updateMatchRequest(omConfig, matchEvent, anOrder.getVenue());
		ClobPersistUtil.updateMatchEvent(matchEvent,anOrder);
		log.info("matchRequest : tradingParty1" + matchEvent.getTradingParty()
				+ " bilateral : " + matchEvent.isBilateral());
		return matchEvent;
	}

	public OrderMatchRequest updateMatchRequest(
			OrderMatchConfiguration matchConfiguration,
			OrderMatchRequest customerMatchRequest, Organization tradingVenue) {
		SingleLegOrder customerOrderRequest = matchConfiguration
				.getSingleLegOrder();

		if (matchConfiguration.getMatchAttributes() == null) {
			log.error("MRWFH.createUpdateMatchRequest : missing match attributes.");
			return null;
		}
		MatchAttributes matchAttrs = matchConfiguration.getMatchAttributes();
		customerMatchRequest.getState().setName(State.Name.RSPARTIAL);
		DealingModelUtil.populateOrderMatchRequest(customerMatchRequest,
				customerOrderRequest);
		customerMatchRequest.getDirectedOrderInfo().setTradingVenue(
				tradingVenue);
		DealingModelRef<OrderMatchRequest> coveredMatchRequestRef = new DealingModelRef<OrderMatchRequest>();
		coveredMatchRequestRef
				.setNamespace(customerMatchRequest.getNamespace());
		coveredMatchRequestRef.setObject(customerMatchRequest);
		customerMatchRequest.setTradingParty(matchAttrs.getTradingParty());
		customerMatchRequest.setBilateral(matchConfiguration.isBilateral());
		customerMatchRequest.setCreditEntities(matchAttrs
				.getCreditTradingParties());
		customerMatchRequest.setResponsePending(false);
		return customerMatchRequest;

	}

    private static final char FIELD_SEPARATOR  = MarketSnapshotUtil.FIELD_SEPARATOR;
    private static final char RECORD_SEPARATOR = MarketSnapshotUtil.RECORD_SEPARATOR;
    private static final char TYPE_SEPARATOR   = MarketSnapshotUtil.TYPE_SEPARATOR;
    private static final char EMPTY_RECORD     = MarketSnapshotUtil.EMPTY_RECORD;

    private static final char TRUE = MarketSnapshotUtil.TRUE;
    private static final char FALSE = MarketSnapshotUtil.FALSE;
	 public static String createNewMarketSnapshot(SingleLegOrder orderRequest,ClobOrder anOrder,boolean isBid )
{
		 double topOfBidBook =anOrder.getTopOfBidBookPrice();
		 double  topOfOfferBook =anOrder.getTopOfOfferBookPrice();
		 StringBuilder snapshot = new StringBuilder(50);
		 snapshot.append(MarketSnapshotUtil.SNAPSHOT_VERSION);
	     snapshot.append(TYPE_SEPARATOR);
	     snapshot.append(TRUE );
	     snapshot.append(FALSE);
	     snapshot.append(isBid ? TRUE : FALSE);
	     snapshot.append(FALSE);
	     snapshot.append(FALSE);
	     snapshot.append(FALSE);
	     snapshot.append(TYPE_SEPARATOR);
	     if(topOfBidBook!=0.0) {
	    	 appendTopBook(orderRequest, anOrder, snapshot, topOfBidBook,anOrder.getTopOfBidBookProvider());
	     }else {
	    	 snapshot.append(EMPTY_RECORD);
	     }
	     snapshot.append(TYPE_SEPARATOR);
	     if(topOfOfferBook!=0.0) {
	    	 appendTopBook(orderRequest, anOrder, snapshot,topOfOfferBook, anOrder.getTopOfOfferBookProvider());
	     }else {
	    	 snapshot.append(EMPTY_RECORD);
	     }

return snapshot.toString();
}






	 public static void appendTopBook(SingleLegOrder orderRequest,ClobOrder anOrder, StringBuilder snapshot,double topOfBook,Organization topOfBookProvider) {

		 snapshot.append(anOrder.getTimings()[ISCommonConstants.ME_MATCHING_TIME]);
		 snapshot.append(FIELD_SEPARATOR).append(topOfBookProvider.getShortName());
		 snapshot.append(FIELD_SEPARATOR).append(0);
		 snapshot.append(FIELD_SEPARATOR).append(1);
		 snapshot.append(FIELD_SEPARATOR).append(topOfBook);
		 snapshot.append(FIELD_SEPARATOR).append(0);
		 snapshot.append(FIELD_SEPARATOR).append(FALSE);
		 snapshot.append(FIELD_SEPARATOR).append(FALSE);
		 snapshot.append(RECORD_SEPARATOR);
	 }

	public List<SingleLegTrade> newTrades(ClobOrder response,
			OrderMatchRequest matchEvent) {
		return handler.createTrades(response, matchEvent,
				matchEvent.getOrderRequest());
	}

	private static final Log log = LogFactory
			.getLog(SingleLegOrderConvertor.class);

	public static CurrencyPair getPair(String a, String b) {
		return CurrencyFactory.getCurrencyPair(a, b);
	}

}
