package com.integral.notifications.clob.credit;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;

import com.integral.config.util.ConfigUtil;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.LegalEntityC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.property.SystemPropertyC;
import com.integral.system.server.VirtualServer;
import com.integral.tradingvenue.TradingVenueRelationShip;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;

public class GatewayConfigUtil {
  private static Log log = LogFactory.getLog(GatewayConfigUtil.class);

  private static final String MAKER_GATWAY_PROVISIONED_ORGS_PROPERTY = "Idc.GateWay.Container.Supported.Tenants";
  private static final String MAKER_GATWAY_PROVISIONED_TENANT_ORG_PROPERTY = "Idc.GateWay.ReferenceData.Tenant.Organization.";
  private static final String DEFAULT_VENUE_PROPERTY_PREFIX = "Idc.GateWay.ReferenceData.Default.Venue.";
  private static final String MAKER_ORG_LEGAL_ENTITIES_PREFIX = "Idc.GateWay.Maker.";
  private static final String MAKER_ORG_LEGAL_ENTITIES_SUFFIX = ".Supported.SubTenants";


  public static Collection<String> getProvisionedTenants(VirtualServer vs) {

    SystemPropertyC prop = SystemPropertyC.getSystemPropertyForVirtualServer(vs.getShortName(), MAKER_GATWAY_PROVISIONED_ORGS_PROPERTY);
    if (prop != null) {
      Collection<String> tenantNames = IdcUtilC.arrayAsArrayList(IdcUtilC.getSubstring(prop.getStringValue(), ","));
      return tenantNames;
    }
    return Collections.EMPTY_SET;
  }

  public static Organization getOrgForTenant(String tenantName, VirtualServer vs) {


    String orgName = null;
    Collection<SystemPropertyC> systemPropertiesWithName = ConfigUtil.getSystemPropertiesWithName(MAKER_GATWAY_PROVISIONED_TENANT_ORG_PROPERTY + tenantName);
    if (systemPropertiesWithName != null && systemPropertiesWithName.size() > 0) {
      for (SystemPropertyC propOrg : systemPropertiesWithName) {
        if (SystemPropertyC.SCOPE_TYPE_LOCAL.equals(propOrg.getScope())) {
          if (vs.isSameAs(propOrg.getVirtualServer())) {
            orgName = propOrg.getStringValue();
            break;
          }
        }
      }
      if (orgName == null) {
        for (SystemPropertyC propOrg : systemPropertiesWithName) {
          if (SystemPropertyC.SCOPE_TYPE_GROUP.equals(propOrg.getScope())) {
            if (vs.getServerGroup().equals(propOrg.getServerGroup())) {
              orgName = propOrg.getStringValue();
              break;
            }
          }
        }
      }
      if (orgName == null) {
        for (SystemPropertyC propOrg : systemPropertiesWithName) {
          if (SystemPropertyC.SCOPE_TYPE_GLOBAL.equals(propOrg.getScope())) {
            if (vs.getServerGroup().equals(propOrg.getServerGroup())) {
              orgName = propOrg.getStringValue();
              break;
            }
          }
        }
      }
    }
    if (orgName == null) {
      orgName = tenantName;
    }
    final Organization org = ReferenceDataCacheC.getInstance().getOrganization(orgName);
    if (org != null) {
      return org;
    } else {
      log.warn("CU.getProvisionedOrgs : no org found with short name=" + orgName);
      return null;
    }
  }

  public static Organization getTradingVenue(String tenantName, Organization org, VirtualServer vs) {
    try {
      String propName = DEFAULT_VENUE_PROPERTY_PREFIX + tenantName;
      Collection<SystemPropertyC> props = ConfigUtil.getSystemPropertiesWithName(propName);
      if (props != null) {
        String tradingVenueName = null;
        int scope = 0;
        for (SystemPropertyC sp : props) {
          if (SystemPropertyC.SCOPE_TYPE_LOCAL.equals(sp.getScope())) {
            if (vs.isSameAs(sp.getVirtualServer())) {
              tradingVenueName = sp.getStringValue();
              scope = 2;
            } else {
              continue;
            }
          } else if (SystemPropertyC.SCOPE_TYPE_GROUP.equals(sp.getScope())) {
            if (vs.getServerGroup().equals(sp.getServerGroup())) {
              tradingVenueName = sp.getStringValue();
              scope = 1;
            }
          }

          if (SystemPropertyC.SCOPE_TYPE_GLOBAL.equals(sp.getScope()) && scope == 0) {
            tradingVenueName = sp.getStringValue();
          }
        }

        if (tradingVenueName != null) {
          Organization tradingVenueOrg = ReferenceDataCacheC.getInstance().getOrganization(tradingVenueName);
          if(tradingVenueOrg != null)
            return tradingVenueOrg;
          else
            log.info("CU.getTradingVenue : no trading venue org found with name=" + tradingVenueName + " for org=" + org);
        } else {
          log.info("CU.getTradingVenue : No trading venue found for the clearing member le for org=" + org + ",vs=" + vs + ",tenantName:" + tenantName);
        }
      }
    } catch (Exception e) {
      log.error("CU.getTradingVenue : exception while retrieving the clearing member le for org=" + org + ",vs=" + vs, e);
    }
    return null;
  }

  public static LegalEntity getTradingVenueClearingMemberLE(String tenantName, Organization org, VirtualServer vs) {
    try {

      Organization tradingVenueOrg = getTradingVenue(tenantName, org, vs);
      if (tradingVenueOrg != null) {
        TradingVenueRelationShip tvRel = org.getTradingVenueRelationShip(tradingVenueOrg);
        if (tvRel != null) {
          return tvRel.getClearingMember();
        } else {
          log.info("CU.getTradingVenueClearingMemberLE : no trading venue relation found with tv org=" + tradingVenueOrg.getName() + " for org=" + org);
        }
      }
    } catch (Exception e) {
      log.error("CU.getTradingVenueClearingMemberLE : exception while retrieving the clearing member le for org=" + org + ",vs=" + vs, e);
    }
    return null;
  }

  public static Collection<LegalEntity> getMakerGatewayConfiguredLegalEntities(final String tenantName, final Organization org, VirtualServer vs) {
    try {
      
        String propName = MAKER_ORG_LEGAL_ENTITIES_PREFIX + tenantName + MAKER_ORG_LEGAL_ENTITIES_SUFFIX;
        Collection<SystemPropertyC> props = ConfigUtil.getSystemPropertiesWithName(propName);
        if (props != null) {
          String propValue = null;
          int scope = 0;
          for (SystemPropertyC sp : props) {
            if (SystemPropertyC.SCOPE_TYPE_LOCAL.equals(sp.getScope())) {
              if (vs.isSameAs(sp.getVirtualServer())) {
                propValue = sp.getStringValue();
                scope = 2;
              } else {
                continue;
              }
            } else if (SystemPropertyC.SCOPE_TYPE_GROUP.equals(sp.getScope())) {
              if (vs.getServerGroup().equals(sp.getServerGroup())) {
                propValue = sp.getStringValue();
                scope = 1;
              }
            }

            if (SystemPropertyC.SCOPE_TYPE_GLOBAL.equals(sp.getScope()) && scope == 0) {
              propValue = sp.getStringValue();
            }
          }

          if (propValue != null) {
            Collection<String> leNames = IdcUtilC.arrayAsArrayList(IdcUtilC.getSubstring(propValue, ","));
            if (leNames != null) {
              Collection<LegalEntity> les = new HashSet<LegalEntity>(leNames.size());
              for (String leName : leNames) {
                final LegalEntity le =
                    (LegalEntity) ReferenceDataCacheC.getInstance().getEntityByShortName(leName, LegalEntityC.class, org.getNamespace(), null);
                if (le != null) {
                  les.add(le);
                } else {
                  log.warn("CU.getConfiguredLegalEntities : no le found with short name=" + leName + " in org=" + org);
                }
              }
              return les;
            }
          }
        }
    } catch (Exception e) {
      log.error("CU.getConfiguredLegalEntities : exception while retrieving the configured legal entities for org=" + org + ",vs=" + vs, e);
    }
    return Collections.EMPTY_SET;
  }

}
