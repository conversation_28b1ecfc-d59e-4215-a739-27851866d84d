package com.integral.notifications.clob.credit;

import com.integral.finance.creditLimit.server.CreditServerManagerC;
import com.integral.system.configuration.IdcMBeanC;
import com.integral.util.IdcUtilC;

import java.util.Collection;

public class MVCreditConfigMBeanC extends IdcMBeanC implements MVCreditConfigMBean {

    private static MVCreditConfigMBean instance = createInstanceAndLoadProperties();

    private String multicastAddress;
    private int multicastPort;
    private long multicastPublishPeriod;
    private boolean creditServerEnabled;
    private boolean ccyPositionNotificationEnabled;
    Collection<String> limitNotificationEnabledVSTypes;
    private long creditLimitsPublishPeriod, creditLinesReloadPeriod;
    Collection<String> limitEnabledGatewayServerVSTypes;
    private boolean accountBalancePublishEnabled;
    private boolean creditPublisherClusterEnabled;
    private int clobCreditCutOffPercent;
    //deprecated
    private int creditLineObserverSyncPeriod;

    private int creditLineMaxPublishCount;

	private boolean isBilateralStreamUpdatePublishEnabled;
	private boolean isStreamUpdatePublishEnabled;


    private long intervalToPublishCredit;

    private int maximumRatesPerWindow;
    private int windowSizeInSecs;
    private boolean isFlowControlEnabled;
    private int flowControlTimeToWaitInMillis;

    protected MVCreditConfigMBeanC() {
        super("com.integral.notifications.clob.credit.MVCreditConfigMBeanC");
    }


    private static MVCreditConfigMBean createInstanceAndLoadProperties() {
        final MVCreditConfigMBean result = new MVCreditConfigMBeanC();
        return result;
    }

    /**
     * Returns a singleton instance.
     *
     * @return a singleton instance of <code>OrderConfigurationMBean</code>
     */
    public static MVCreditConfigMBean getInstance() {
        return instance;
    }

    @Override
    public void initialize() {
        super.initialize();
        loadProperties();
    }


    void loadProperties() {
        multicastAddress = getStringProperty(CREDIT_MULTICAST_ADDRESS, null);
        multicastPort = getIntProperty(CREDIT_MULTICAST_PORT, 0);
        multicastPublishPeriod = getLongProperty(CREDIT_MULTICAST_PUBLISH_PERIOD, 10000);
        creditServerEnabled = getBooleanProperty(CREDIT_SERVER_ENABLED, true);
        ccyPositionNotificationEnabled = getBooleanProperty(CCY_POSITION_NOTIFICATION_ENABLED, false);
        limitNotificationEnabledVSTypes = initLimitNotificationEnabledVirtualServers();
        limitEnabledGatewayServerVSTypes = initLimitEnabledGatewayVirtualServers();
        creditLimitsPublishPeriod = getLongProperty(CREDIT_LIMITS_PUBLISH_PERIOD, CREDIT_LIMITS_PUBLISH_PERIOD_DEFAULT);
        creditLinesReloadPeriod = getLongProperty(CREDIT_LINES_RELOAD_PERIOD, CREDIT_LINES_RELOAD_PERIOD_DEFAULT);
        accountBalancePublishEnabled = getBooleanProperty(ACCOUNT_BALANCE_PUBLISH_ENABLED, false);
        creditPublisherClusterEnabled = getBooleanProperty(CREDIT_PUBLISHING_CLUSTER_ENABLED, false);
        clobCreditCutOffPercent = getIntProperty(CLOB_CREDIT_CUT_OFF_PERCENT, 10);
        creditLineObserverSyncPeriod = getIntProperty(CREDIT_LINE_OBSERVER_SYNC_PERIOD, 100);
        creditLineMaxPublishCount = getIntProperty(CREDIT_MAX_PUBLISH_COUNT, 3);
		isBilateralStreamUpdatePublishEnabled = getBooleanProperty(BILATERAL_STREAMUPDATE_PUBLISH_ENABLED, false);
		isStreamUpdatePublishEnabled = getBooleanProperty(STREAMUPDATE_PUBLISH_ENABLED, false);
        intervalToPublishCredit = getIntProperty(INTERVAL_TO_PUBLISH_STREAMUPDATE_IN_MILLIS, 5000);
        maximumRatesPerWindow = getIntProperty(STREAMUPDATES_FLOWCONTROL_MAXIMUMUPDATES_PER_WINDOW, 1000);
        windowSizeInSecs = getIntProperty(STREAMUPDATE_FLOWCONTROL_WINDOWSIZE_IN_SECS, 1000);
        isFlowControlEnabled = getBooleanProperty(FLOWCONTROL_ENABLED_FOR_STREAMUPDATE, false);
        flowControlTimeToWaitInMillis = getIntProperty(STREAMUPDATE_FLOWCONTROL_TIMETOWAIT_INMILLIS, 500);
        log.info(" flowControlTimeToWaitInMillis " + flowControlTimeToWaitInMillis + " isFlowControlEnabled " + isFlowControlEnabled + " windowSizeInSecs  " + windowSizeInSecs + " maximumRatesPerWindow " + maximumRatesPerWindow + " maximumRatesPerWindow " + maximumRatesPerWindow + " intervalToPublishCredit " + intervalToPublishCredit + " creditLineObserverSyncPeriod:" + creditLineObserverSyncPeriod);
    }


    public int getClobCreditCutOffPercent() {
        return clobCreditCutOffPercent;
    }

    public String getMulticastAddress() {
        return multicastAddress;
    }

    public int getMulticastPort() {
        return multicastPort;
    }

    public long getMulticastPublishPeriod() {
        return multicastPublishPeriod;
    }

    @Override
    public boolean isCreditEnabled() {
        // TODO Auto-generated method stub
        return creditServerEnabled;
    }

    @Override
    public boolean isAccountBalancePublishingEnabled() {
        return accountBalancePublishEnabled;
    }

    @Override
    public boolean isCreditPublisherClusterEnabled() {
        return creditPublisherClusterEnabled;
    }

    public Collection<String> getLimitEnabledGatewayVSTypes() {
        return limitEnabledGatewayServerVSTypes;
    }


    public boolean isCreditServerEnabled() {
        return creditServerEnabled;
    }

    public boolean isCcyPositionNotificationEnabled() {
        return ccyPositionNotificationEnabled;
    }

    public Collection<String> getLimitNotificationEnabledVSTypes() {
        return limitNotificationEnabledVSTypes;
    }

    public long getCreditLimitsPublishPeriod() {
        return creditLimitsPublishPeriod;
    }

    public long getCreditLinesReloadPeriod() {
        return creditLinesReloadPeriod;
    }

    public void setProperty(String key, String value, int scope, String oldValue) {
        try {
            super.setProperty(key, value, scope, oldValue);
            if (key.equals(CREDIT_SERVER_ENABLED) || key.equals(CREDIT_LIMITS_PUBLISH_PERIOD) || key.equals(CREDIT_LINES_RELOAD_PERIOD)) {
                log.info("CSC.setProperty : credit server property change. key=" + key + ",newValue=" + value + ",oldValue=" + oldValue);
                CreditServerManagerC.getInstance().initCreditServerTasks();
            }
        } catch (Exception e) {
            log.error("CSC.setProperty : error while setting the property " + key + " to " + value + " scope" + scope, e);
        }
    }

    public void removeProperty(String key, int scope) {
        super.removeProperty(key, scope);
        if (key.equals(CREDIT_SERVER_ENABLED) || key.equals(CREDIT_LIMITS_PUBLISH_PERIOD) || key.equals(CREDIT_LINES_RELOAD_PERIOD)) {
            log.info("QCM.removeProperty : remove property=" + key + ". remove credit server tasks.");
            CreditServerManagerC.getInstance().initCreditServerTasks();
        }
    }

    private Collection<String> initLimitNotificationEnabledVirtualServers() {
        String types = getStringProperty(LIMIT_NOTIFICATION_ENABLED_VS_TYPES, null);
        if (types != null) {
            return IdcUtilC.arrayAsArrayList(IdcUtilC.getSubstring(types, ","));
        }
        return null;
    }

    private Collection<String> initLimitEnabledGatewayVirtualServers() {
        String types = getStringProperty(LIMIT_NOTIFICATION_ENABLED_GATEWAY_VS_TYPES, "MakerGateWay" + "," + UNIVERSAL_ADAPTOR_VIRTUAL_SERVER_TYPE);
        if (types != null) {
            return IdcUtilC.arrayAsArrayList(IdcUtilC.getSubstring(types, ","));
        }
        return null;
    }

    public int getCreditLineMaxPublishCount() {
        return creditLineMaxPublishCount;
    }

    public boolean isBilateralStreamUpdatePublishEnabled() {
        return isBilateralStreamUpdatePublishEnabled;
    }

    public boolean isStreamUpdatePublishEnabled() {
        return isStreamUpdatePublishEnabled;
    }


    public long getIntervalToPublishCredit() {
        return intervalToPublishCredit;
    }

    public void setIntervalToPublishCredit(long intervalToPublishCredit) {
        this.intervalToPublishCredit = intervalToPublishCredit;
    }

    public int getMaximumRatesPerWindow() {
        return this.maximumRatesPerWindow;
    }

    public int getWindowSizeInSecs() {
        return this.windowSizeInSecs;

    }

    public boolean isFlowControlEnabled() {
        return this.isFlowControlEnabled;
    }

    public int getFlowControlTimeToWait() {
        return this.flowControlTimeToWaitInMillis;
    }

    public int getCreditLineObserverSyncPeriod() {
        return creditLineObserverSyncPeriod;
    }

}
