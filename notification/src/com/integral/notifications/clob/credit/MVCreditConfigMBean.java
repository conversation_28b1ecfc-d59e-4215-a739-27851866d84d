package com.integral.notifications.clob.credit;

import com.integral.system.configuration.IdcMBean;

import java.util.Collection;

public interface MVCreditConfigMBean extends IdcMBean {

    String CREDIT_MULTICAST_ADDRESS = "Idc.Mvp.Credit.Multicast.Address";
    String CREDIT_MULTICAST_PORT = "Idc.Mvp.Credit.Multicast.Port";
    String CREDIT_MULTICAST_PUBLISH_PERIOD = "Idc.Mvp.Credit.Multicast.Publish.Period";
    String CREDIT_SERVER_ENABLED = "Idc.Mvp.Credit.Server.Enabled";
    String CCY_POSITION_NOTIFICATION_ENABLED = "Idc.Credit.Ccy.Position.Notification.Enabled";
    String LIMIT_NOTIFICATION_ENABLED_VS_TYPES = "Idc.Credit.Limit.Notification.Enabled.VS.Types";
    String CREDIT_LIMITS_PUBLISH_PERIOD = "Idc.Credit.Limits.Publish.Period";
    String CREDIT_LINES_RELOAD_PERIOD = "Idc.Credit.Lines.Reload.Period";
    String LIMIT_NOTIFICATION_ENABLED_GATEWAY_VS_TYPES = "Idc.Credit.Lines.Gateway.VS.Types";
    String UNIVERSAL_ADAPTOR_VIRTUAL_SERVER_TYPE = "UniversalAdaptor";
    String ACCOUNT_BALANCE_PUBLISH_ENABLED = "Idc.Mvp.AccountBalance.Publish.Enabled";
    String CREDIT_PUBLISHING_CLUSTER_ENABLED = "Idc.Mvp.CreditPublisher.Cluster.Enabled";
    String CLOB_CREDIT_CUT_OFF_PERCENT = "Idc.Mvp.CreditLineObserver.Clob.Credit.Cutoff.Percent";
    String CREDIT_LINE_OBSERVER_SYNC_PERIOD = "Idc.Mvp.CreditLineObserver.Sync.Period";
    String CREDIT_MAX_PUBLISH_COUNT = "Idc.Mvp.StreamUpdate.MaxUpdate.Publish.Count";

    String INTERVAL_TO_PUBLISH_STREAMUPDATE_IN_MILLIS = "Idc.Mvp.StreamUpdate.Interval.ToPublish.NextUpdateInMillis";

    String BILATERAL_STREAMUPDATE_PUBLISH_ENABLED = "Idc.Mvp.StreamUpdate.Bilateral.Publish.Enabled";

    String STREAMUPDATE_PUBLISH_ENABLED = "Idc.Mvp.StreamUpdate.Publish.Enabled";

    String STREAMUPDATES_FLOWCONTROL_MAXIMUMUPDATES_PER_WINDOW = "Idc.Mvp.StreamUpdate.FlowControl.MaximumUpdates.PerWindow";

    String STREAMUPDATE_FLOWCONTROL_WINDOWSIZE_IN_SECS = "Idc.Mvp.StreamUpdate.FlowControl.WindowSize";

    String FLOWCONTROL_ENABLED_FOR_STREAMUPDATE = "Idc.Mvp.StreamUpdate.FlowControl.Enabled";

    String STREAMUPDATE_FLOWCONTROL_TIMETOWAIT_INMILLIS = "Idc.Mvp.StreamUpdate.FlowControl.TimeToWaitInMillis";


    int CREDIT_LIMITS_PUBLISH_PERIOD_DEFAULT = 5 * 1000;
    int CREDIT_LINES_RELOAD_PERIOD_DEFAULT = 120 * 1000;

    boolean isCreditServerEnabled();

    boolean isCcyPositionNotificationEnabled();

    Collection<String> getLimitNotificationEnabledVSTypes();

    /**
     * Returns the period in milliseconds by which credit notifications are published in multicast.
     *
     * @return period in milliseconds
     */
    long getCreditLimitsPublishPeriod();

    /**
     * Returns the period in milliseconds by which credit lines are refreshed based on the admin configuration.
     *
     * @return period in milliseconds
     */
    long getCreditLinesReloadPeriod();


    Collection<String> getLimitEnabledGatewayVSTypes();

    /**
     * Returns the multicast address on which credit limit updates are published.
     *
     * @return multicast address
     */
    String getMulticastAddress();

    /**
     * Returns the multicast port on which credit limit updates are published.
     *
     * @return multicast port
     */
    int getMulticastPort();

    /**
     * Returns the period in milliseconds by which credit notifications are published in multicast.
     *
     * @return period in milliseconds
     */
    long getMulticastPublishPeriod();


    int getClobCreditCutOffPercent();

    int getCreditLineObserverSyncPeriod();

    boolean isCreditEnabled();

    boolean isAccountBalancePublishingEnabled();

    boolean isCreditPublisherClusterEnabled();

    int getCreditLineMaxPublishCount();

    boolean isBilateralStreamUpdatePublishEnabled();

    boolean isStreamUpdatePublishEnabled();

    long getIntervalToPublishCredit();

    int getMaximumRatesPerWindow();

    int getWindowSizeInSecs();

    boolean isFlowControlEnabled();

    int getFlowControlTimeToWait();
}
