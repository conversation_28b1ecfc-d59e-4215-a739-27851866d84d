package com.integral.notifications.clob.credit;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.services.cluster.notification.MasterSlaveTransitionHandler;

import java.util.concurrent.atomic.AtomicBoolean;

public class MVPCreditManagerMasterSlaveTransitionHandler implements MasterSlaveTransitionHandler {
    private Log log = LogFactory.getLog(this.getClass());
    private AtomicBoolean master = new AtomicBoolean(false);

    @Override
    public void onBecomeSlaveFromOffline(String resourceName, String partitionId) {
        log.info("onBecomeSlaveFromOffline : resourceName="+resourceName+", partitionId="+partitionId);
        master.set(false);
    }

    @Override
    public void onBecomeMasterFromSlave(String resourceName, String partitionId) {
        log.info("onBecomeMasterFromSlave : resourceName="+resourceName+", partitionId="+partitionId);
        master.set(true);
    }

    @Override
    public void onBecomeSlaveFromMaster(String resourceName, String partitionId) {
        log.info("onBecomeSlaveFromMaster : resourceName="+resourceName+", partitionId="+partitionId);
        master.set(false);
    }

    @Override
    public void onBecomeOffLineFromSlave(String resourceName, String partitionId) {
        log.info("onBecomeOffLineFromSlave : resourceName="+resourceName+", partitionId="+partitionId);
        master.set(false);
    }

    @Override
    public void onBecomeOffLine(String resourceName, String partitionId) {
        log.info("onBecomeOffLine : resourceName="+resourceName+", partitionId="+partitionId);
        master.set(false);
    }

    public boolean isMaster() {
        return master.get();
    }
}
