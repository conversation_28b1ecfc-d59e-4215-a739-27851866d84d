package com.integral.notifications.clob.credit.flowcontrol;

public class FlowInfo {
  public String type;
  public double currentRate;
  public long curTime;

  public String unit;
  public int windowSize;
  public int windowSlideLength;

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public double getCurrentRate() {
    return currentRate;
  }

  public void setCurrentRate(double currentRate) {
    this.currentRate = currentRate;
  }

  public long getCurTime() {
    return curTime;
  }

  public void setCurTime(long curTime) {
    this.curTime = curTime;
  }

  public String getUnit() {
    return unit;
  }

  public void setUnit(String unit) {
    this.unit = unit;
  }

  public int getWindowSize() {
    return windowSize;
  }

  public void setWindowSize(int windowSize) {
    this.windowSize = windowSize;
  }

  public int getWindowSlideLength() {
    return windowSlideLength;
  }

  public void setWindowSlideLength(int windowSlideLength) {
    this.windowSlideLength = windowSlideLength;
  }

  @Override
  public String toString() {
    StringBuilder builder = new StringBuilder();
    builder.append("FlowInfo [type=");
    builder.append(type);
    builder.append(", currentRate=");
    builder.append(currentRate);
    builder.append(", curTime=");
    builder.append(curTime);
    builder.append(", unit=");
    builder.append(unit);
    builder.append(", windowSize=");
    builder.append(windowSize);
    builder.append(", windowSlideLength=");
    builder.append(windowSlideLength);
    builder.append("]");
    return builder.toString();
  }
  
}
