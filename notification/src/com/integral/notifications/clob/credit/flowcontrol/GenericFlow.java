package com.integral.notifications.clob.credit.flowcontrol;



import java.util.Arrays;



public class GenericFlow {

  private static final long NANOS_IN_MS = 1000000l;
 
 // private final long nanosInWindowUnit;
  private final int numIntervalsSec;
  private final long windowSize;
  private final short MAX_MESSAGES;
  private final long SLOT_SIZE;
  private final long WINDOWSIZE_MINUSONE;
  private final int[] messages;
  private short currentSum = 0;
  private long windowStart;
  private long windowEnd;
  private int currentSlot;
  
  
  private boolean closePort = false;
  private FlowInfo data  = new FlowInfo();
  
  public GenericFlow(int maxFlow , int windowSizeSec) {
    numIntervalsSec = windowSizeSec;
    MAX_MESSAGES = (short) maxFlow;
    windowSize = numIntervalsSec*NANOS_IN_MS;
    SLOT_SIZE = windowSize / numIntervalsSec;
    WINDOWSIZE_MINUSONE = windowSize - NANOS_IN_MS;
    messages = new int[numIntervalsSec];
    Arrays.fill(messages, 0);
    data.setType("RATE MESSAGE");
    data.setUnit("SECONDS");
    data.setWindowSize(numIntervalsSec);
    data.setWindowSlideLength(1);
    data.setCurrentRate(0);
    windowStart = System.nanoTime();
    windowEnd = windowStart + windowSize;
  }
  
  public boolean checkFlow(long aTimeNanos) {
    if(insideWindow(aTimeNanos))
    {
      currentSlot = getSlot(aTimeNanos);
      messages[currentSlot]++;
      currentSum++;
    } else slideWindow(aTimeNanos);
    closePort = currentSum > MAX_MESSAGES;
    data.setCurrentRate(currentSum);
    data.setCurTime(aTimeNanos);
    return closePort;
  }
  
  private boolean insideWindow(long aTime)
  {
    return windowStart <= aTime && windowEnd >= aTime;
  }
  
  private int getSlot(long aTime)
  {
    return  (int) (((aTime - windowStart) / SLOT_SIZE));
  }

  private void slideWindow(long aTime)
  {
    if(aTime - windowEnd > WINDOWSIZE_MINUSONE)
      {
      resetWindow(aTime);
      currentSlot = 0;
      messages[currentSlot]++;
      currentSum++;
      }
    else
      {
      int oldWindowEnd  = (int) (windowEnd/NANOS_IN_MS);
      currentSlot = numIntervalsSec -1;
      windowEnd =  (((aTime)/NANOS_IN_MS)*NANOS_IN_MS + NANOS_IN_MS);
      windowStart = windowEnd - windowSize;
      int slotDiff = (int)((windowEnd) / NANOS_IN_MS) - oldWindowEnd;
      for(int i = 0 ; i < slotDiff ;i++)
      {
      currentSum -= messages[i];      
      }
      System.arraycopy(messages,slotDiff, messages, 0, numIntervalsSec-slotDiff);
      Arrays.fill(messages, numIntervalsSec-slotDiff, numIntervalsSec,0);
      messages[currentSlot]++;
      currentSum++;
      }
  }
  
  private void resetWindow(long aTime)
  {
       Arrays.fill(messages, 0);
       currentSlot = 0;
       currentSum = 0;
       windowStart = aTime;
       windowEnd = aTime+windowSize;
  }
  
  public void printMessage()
  {
    String k =  "";
    for(int j = 0 ; j < numIntervalsSec;j++)
    {
      k = k + messages[j] + " ";
    }
     k = k + " sum: " + currentSum;
     System.out.println(k);
  }
  
  public boolean isClosePort() {
    return closePort;
  }
  
  public FlowInfo getData(){
    return data;
  }
  
}
