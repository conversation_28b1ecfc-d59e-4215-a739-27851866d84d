package com.integral.notifications.tradeTicker;

import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.OrderRequest;
import com.integral.model.dealing.SingleLegOrderMatch;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.model.dealing.TradeLeg;
import com.integral.notifications.NotificationConfiguration;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.query.spaces.fx.esp.query.SingleLegOrderMatchQueryService;
import com.integral.spaces.notification.NHResponse;
import com.integral.spaces.notification.NHResponseCode;
import com.integral.spaces.notification.Notification;
import com.integral.spaces.notification.NotificationHandler;
import com.integral.ticker.TickerFactory;
import com.integral.ticker.publish.TickEventPublisher;
import com.integral.tradeticker.TradeTickEvent;
import com.integral.tradeticker.config.TradeTickerConfig;
import com.integral.user.Organization;

import java.io.IOException;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 5/17/13
 * Time: 11:41 AM
 * Sends out Trade Ticker Notifications on Trade Verified event.
 */
public class TradeTickerNotificationHandler implements NotificationHandler {

    private static final Log log = LogFactory.getLog(TradeTickerNotificationHandler.class);

    private final TradeTickerConfig tickerConfig;
    private final TickEventPublisher<TradeTickEvent> tickerPublisher;

    public TradeTickerNotificationHandler() {
        tickerConfig = (TradeTickerConfig) TickerFactory.getInstance().getTickerService(TradeTickEvent.class).getConfiguration();
        tickerPublisher = TickerFactory.getInstance().getTickEventPublisher(TradeTickEvent.class);
        log.info("TradeTickerNotificationHandler initialized.");
    }

    @Override
    public long getLongIdentifier() {
        return NotificationConfiguration.NOTIFICATION_HANDLER_TRADE_TICKER.getLongIdentifier();
    }

    public NHResponse handle(Notification notification) {
        if (log.isDebugEnabled()) {
            log.debug(notification.toString());
        }
        if (!tickerConfig.isTickEventPublishingEnabled()) {
            log.info("Tick event publishing is disabled");
            return new NHResponse(NHResponseCode.SUCCESS);
        }
        try {
            if (SingleLegTrade.class.isAssignableFrom(notification.getDataObjectType())) {
                ApplicationEventCodes aec = ApplicationEventCodes.fromCode(notification.getAppEventCode());
                if (aec.equals(ApplicationEventCodes.EVENT_ESP_TRADE_VERIFIED) ||
                        aec.equals(ApplicationEventCodes.EVENT_ESP_TRADE_CREATED_VERIFIED) ||
                        aec.equals(ApplicationEventCodes.EVENT_ESP_TRADE_NET) || aec.equals( ApplicationEventCodes.EVENT_ESP_TRADE_POST_RATE_VERIFIED )) {
                    SingleLegTrade trade = (SingleLegTrade) notification.getCompleteObject();
                    if (trade == null) {
                        log.error("TradeTickerNotificationHandler.handle() : Trade is null.");
                        return new NHResponse(NHResponseCode.SUCCESS);
                    }
                    boolean doPublish = false;
                    TradeTickerConfig.DeploymentType deploymentType = null;
                    TradeTickerConfig.TradeType publishTickType = null;
                    TradeTickerConfig.TradeType tradeType = null;
                    final boolean nettingRequired = trade.getMatchEvent().isNettingRequired();
                    if (!nettingRequired) { //Making sure Tick Update is published only for net trade.
                        deploymentType = findDeploymentType(trade);
                        publishTickType = tickerConfig.getPublishTickType(deploymentType);
                        tradeType = findTradeType(trade);
                        switch (publishTickType) {
                            case ALL:
                                doPublish = true;
                                break;
                            case CUSTOMER:
                                if (tradeType == TradeTickerConfig.TradeType.CUSTOMER || tradeType == TradeTickerConfig.TradeType.CUSTOMER_MARKET) {
                                    doPublish = true;
                                }
                                break;
                            case MARKET:
                                if (tradeType == TradeTickerConfig.TradeType.MARKET || tradeType == TradeTickerConfig.TradeType.CUSTOMER_MARKET) {
                                    doPublish = true;
                                }
                                break;
                        }
                        if (doPublish) {
                            publishTradeNotification(trade, deploymentType, tradeType);
                            if (log.isDebugEnabled()) {
                                log.debug("TTNH.publish trade:" + trade.toString());
                            }
                        }
                    }
                    if (log.isDebugEnabled()) {
                        log.debug("TTNH.handle trade:" + trade.toString() + ". NettingRequired:" + nettingRequired
                                + ", deploymentType:" + deploymentType + ", doPublish:" + doPublish +
                                ", publishTickType:" + publishTickType + ", tradeType:" + tradeType);
                    }
                }
            }
        } catch (Exception nse) {
            log.error("Error handling notification:" + notification, nse);
            return new NHResponse(NHResponseCode.FAILURE);
        }
        return new NHResponse(NHResponseCode.SUCCESS);
    }

    @Override
    public NHResponse handleRedelivery(Notification notification,String errorDesc) {
        return new NHResponse(NHResponseCode.SUCCESS);
    }

    private TradeTickerConfig.TradeType findTradeType(SingleLegTrade trade) {
        TradeTickerConfig.TradeType result = TradeTickerConfig.TradeType.OTHER;
        if (trade.getCoveredTrade() == null || trade.getCoveredTrade().getLegalEntity() == null) {
            result = TradeTickerConfig.TradeType.CUSTOMER;
        }
        Organization cptyBOrg = DealingModelUtil.getCounterPartyLegalEntity(trade).getOrganization();
        if (cptyBOrg.isExternalProvider() || cptyBOrg.isECN()) {
            if (result == TradeTickerConfig.TradeType.CUSTOMER) {
                result = TradeTickerConfig.TradeType.CUSTOMER_MARKET;
            } else {
                result = TradeTickerConfig.TradeType.MARKET;
            }
        }
        return result;
    }

    private TradeTickerConfig.DeploymentType findDeploymentType(SingleLegTrade trade) {
        TradeTickerConfig.DeploymentType result = TradeTickerConfig.DeploymentType.OTHERS;
        Organization lpOrg = null;
        if (trade.getOriginatingTrade() != null && trade.getOriginatingTrade().getMaskedName() != null) {
            lpOrg = ReferenceDataCacheC.getInstance().getOrganization(trade.getOriginatingTrade().getMaskedName());
        } else {
            log.warn("Originating Trade not found for trade:" + trade.get_id());
            if(trade.getMatchEvent().getMatchedQuote() != null){
                lpOrg = trade.getMatchEvent().getMatchedQuote().getMaskedOrganization();
            }
            if (lpOrg == null) {
                //may happen for Net Trade
                if (trade.getNettedTradeReferences() != null && trade.getNettedTradeReferences().get(0) != null) {
                    SingleLegTrade nettedTrade = (SingleLegTrade) trade.getNettedTradeReferences().get(0).getObject();
                    SingleLegOrderMatch match = SingleLegOrderMatchQueryService.getOrderMatchWithId(nettedTrade.getMatchEventRef().getNamespace().getShortName(), nettedTrade.getMatchEventRef().getUid()).getResult();
                    lpOrg = match.getMatchedQuote().getMaskedOrganization();
                } else {
                    if(trade.getTradingParty()!=null){
                        lpOrg = trade.getTradingParty().getLegalEntityOrganization();
                    }else if(trade.isOCX2()){
                        lpOrg = trade.getCounterpartyLegalEntity().getOrganization();
                    }

                }
            }
        }

        Organization realLpOrg = lpOrg;
        if (lpOrg.getRealLP() != null) {
            realLpOrg = lpOrg.getRealLP();
        }
        final boolean broker = realLpOrg.isBroker() && !realLpOrg.isFMALP();
        if (!broker && lpOrg.isFMALP()) {
            result = TradeTickerConfig.DeploymentType.FMA;
        } else if (broker) {
            result = TradeTickerConfig.DeploymentType.BROKER;
        }
        return result;
    }

    private void publishTradeNotification(SingleLegTrade trade, TradeTickerConfig.DeploymentType deploymentType,
                                          TradeTickerConfig.TradeType tradeType) throws IOException {
        String ccyPair = trade.getCurrencyPairName();
        //Only supported currency pairs are published on multicast channel -- revisit this later
        if (tickerConfig.getBufferKeyIndex(ccyPair) != -1) {
            TradeLeg singleLeg = trade.getTradeLeg();

            boolean isBuy = false;
            OrderRequest.RequestLeg.BuySellMode buySellMode = trade.getOrderRequest().getRequestLeg().getBuySellMode();
            if (buySellMode != null) {
                switch (buySellMode) {
                    case SELL:
                        isBuy = false;
                        break;
                    case BUY:
                        isBuy = true;
                        break;
                }
            } else {
                log.warn("TTNH.publishTradeNotification BuySellMode not set for tradeID:" + trade.get_id());
            }
            final TradeTickEvent tickEvent = new TradeTickEvent(ccyPair, singleLeg.getRate(), isBuy,
                    trade.getExecutionTime(), trade.get_id(), deploymentType, tradeType);
            tickerPublisher.publishEvent(tickEvent);
        }
    }

}
