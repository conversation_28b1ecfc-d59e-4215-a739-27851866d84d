package com.integral.notifications;

import java.util.Hashtable;
import java.util.Properties;

import com.integral.cluster.ClusterConfigDataService;
import com.integral.cluster.ClusterManagerHelper;
import com.integral.dbservice.notifications.NotificationPersistenceStartupC;
import com.integral.facade.FacadeFactory;
import com.integral.finance.trade.TradeService;
import com.integral.finance.trade.functor.TradeRemoteNotificationFunctorServerC;
import com.integral.is.common.facade.ISRequestTransactionIdFacadeC;
import com.integral.is.common.facade.ISTradeTransactionIdFacadeC;
import com.integral.is.common.pool.ThreadPoolFactory;
import com.integral.is.functor.TradeAmendNotificationFunctorC;
import com.integral.is.log.MessageLogger;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.notifications.authtoken.AuthTokenNotificationHandler;
import com.integral.notifications.chiefdealer.dealorder.CDQPersistenceNotificationHandler;
import com.integral.notifications.chiefdealer.dealorder.ChiefDealerNotifier;
import com.integral.notifications.chiefdealer.dealorder.IChiefDealerNotifier;
import com.integral.notifications.coverTrade.CoverTradeNotificationHandler;
import com.integral.notifications.cptytrd.CptyTradePersistenceHandler;
import com.integral.notifications.credit.CreditTradeNotificationHandler;
import com.integral.notifications.email.EmailNotificationHandler;
import com.integral.notifications.gm.GMMessageNotifier;
import com.integral.notifications.gm.GMNotificationHandler;
import com.integral.notifications.mis.MISNotificationHandler;
import com.integral.notifications.staging.StagingAreaNotificationHandler;
import com.integral.notifications.stp.FinXMLNotificationHandler;
import com.integral.notifications.tradeTicker.TradeTickerNotificationHandler;
import com.integral.notifications.util.DealingModelPreProcessor;
import com.integral.persistence.TransactionIdFacadeC;
import com.integral.persistence.spaces.PersistenceConstants;
import com.integral.services.ServiceContainerMBean;
import com.integral.services.cluster.ClusterManager;
import com.integral.services.cluster.ClusterManagerFactory;
import com.integral.spaces.config.MetaspacesConfigMBean;
import com.integral.spaces.config.MetaspacesConfigMBeanImpl;
import com.integral.spaces.notification.Filter;
import com.integral.spaces.notification.NHResponse;
import com.integral.spaces.notification.NHResponseCode;
import com.integral.spaces.notification.Notification;
import com.integral.spaces.notification.NotificationConfiguration;
import com.integral.spaces.notification.NotificationHandler;
import com.integral.spaces.notification.NotificationManager;
import com.integral.spaces.notification.NotificationManagerFactory;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.runtime.StartupTask;

public class NotificationRegistrationStartup implements StartupTask {
    private static Log log = LogFactory.getLog(NotificationRegistrationStartup.class.getName());
   
    public static final String NOTIFICATION_HANDLER_NAME_STP = "STP";
    public static final String NOTIFICATION_HANDLER_NAME_CPTY_TRD = "CptyTrd";
    public static final String NOTIFICATION_HANDLER_NAME_GM = "GM";
    public static final String NOTIFICATION_HANDLER_NAME_CREDIT = "Credit";
    public static final String NOTIFICATION_HANDLER_NAME_MIS = "MIS";
    public static final String NOTIFICATION_HANDLER_NAME_EMAIL = "Email";
    public static final String NOTIFICATION_HANDLER_NAME_DONOTHING = "DoNothing";
    public static final String NOTIFICATION_HANDLER_NAME_TRADE_TICKER = "TradeTicker";
    public static final String NOTIFICATION_HANDLER_NAME_DEALORDER_PERSISTENCE = "DealOrderPersistence";
    public static final String NOTIFICATION_HANDLER_NAME_AUTH_TOKEN = "AuthToken";
    public static final String NOTIFICATION_HANDLER_NAME_STAGING = "Staging";
    public static final String NOTIFICATION_HANDLER_COVER_TRADE = "CoverTrade";
    public static final String NOTIFICATION_HANDLER_NAME_CREDIT_POSTTRADE = "CreditPostTrade";


	public String startup(String aName, Hashtable args) throws Exception {
		log.info("Starting spaces notification startup");

        ThreadPoolFactory.getInstance().getWarmupPool().execute(new NotificationWarmUpTask());

		log.info("Registering Entity Facades.");
		registerEntityFacades();
		if (ConfigurationFactory.getServerMBean().isIntegralSpacesEnabled()) {
			configureNotificationPersistence();
			configureNotifications();

			TradeRemoteNotificationFunctorServerC.getInstance()
					.addTradeRemoteNotificationFunctor(
							TradeService.CANCEL_EVENT,
							TradeAmendNotificationFunctorC.class);
			TradeRemoteNotificationFunctorServerC.getInstance()
					.addTradeRemoteNotificationFunctor(
							TradeService.AMEND_EVENT,
							TradeAmendNotificationFunctorC.class);

			log.info("Ended spaces notification startup");

		}
		return null;
	}

    protected void registerEntityFacades() {
    	FacadeFactory.setFacade( TransactionIdFacadeC.FACADE_NAME, com.integral.finance.dealing.Request.class, ISRequestTransactionIdFacadeC.class );
        FacadeFactory.setFacade( TransactionIdFacadeC.FACADE_NAME, com.integral.finance.trade.Trade.class, ISTradeTransactionIdFacadeC.class );
	}

    private void configureNotificationPersistence() throws InstantiationException {
        // Set up the DBService to be used for file backed notification
        log.info("INITIALIZING DBSERVICE FOR NOTIFICATION PERSISTENCE");
        NotificationPersistenceStartupC notificationStartup = new NotificationPersistenceStartupC();
        notificationStartup.startup(null, null);
    }

    /*
     * A notification server can choose the type of notifications it handles by setting the local
     * (@link NOTIFICATION_LISTENER_HANDLER_GROUPS_PROPERTY) property to select from the global set
     * of properties prefixed by (@link NOTIFICATION_HANDLER_GROUP_PREFIX).
     *
     * @return true only if notification listener is configured correctly
     */
	public static NotificationManager configureNotifications(String queueSetBase, String listenerHandlerGroup,
			String handlerGroups, String routingKeyPrefix) throws Exception {
		
		NotificationManager notificationManager = NotificationManagerFactory
				.getOrCreateNotificationManager(queueSetBase,
						listenerHandlerGroup, handlerGroups, routingKeyPrefix);
		
		int handlerSubscribers = 0;          // each handler could subscribe more than once with different filters

		String handlerGroupNameForSTP = null;
		String handlerGroupNameForGM = null;		
		String handlerGroupNameForFXIChiefDealer = null;
		String handlerGroupNameForMIS = null;
		String handlerGroupNameForEmail = null;
		String handlerGroupNameForDoNothing = null;
		String handlerGroupNameForTradeTicker = null;
		String handlerGroupNameForDealOrderPersistence = null;
		String handlerGroupNameForAuthtoken = null;
		String handlerGroupNameForStaging = null;
		String handlerGroupNameForCoverTrade = null;
		String handlerGroupNameForCptyTrd = null;
		String handlerGroupNameForCreditPostTrade = null;

		boolean handleSTPNotifications = false;
		boolean handleGMNotifications = false;		
		boolean handleMISNotifications = false;
		boolean handleEmailNotifications = false;
		boolean handleDoNothingNotifications = false;
		boolean handleTradeTickerNotifications = false;
		boolean handleDealOrderPersistence = false;
		boolean handleAuthTokenNotifications = false;
		boolean handleStagingNotifications = false;
		boolean handleCoverTradeNotifications = false;
		boolean handleCptyTradeNotifications = false;
		boolean handleCreditPostTradeNotifications = false;

		boolean noHandlerGroupProps = (handlerGroups.isEmpty() || handlerGroups == null);
		boolean noListenerHandlerProps = (listenerHandlerGroup.isEmpty() || listenerHandlerGroup == null);
		/*
		 * noHandlerGroups noListenerHandlerGroups error? Comments
		 * --------------- ----------------------- ------
		 * ------------------------------------------------------------- true
		 * true no all handlers supported true false yes won't find listener
		 * handler groups
		 * 
		 * false true yes may not be the only listener, causes out of seq msg
		 * handling false false ? check if all listener handler groups are found
		 */

		if (noHandlerGroupProps) {
			if (noListenerHandlerProps) {
				handleSTPNotifications = true;
				handleGMNotifications = true;				
				handleMISNotifications = true;
				handleEmailNotifications = true;
				handleDoNothingNotifications = true;
				handleTradeTickerNotifications = true;
				handleDealOrderPersistence = true;
				handleAuthTokenNotifications = true;
				handleStagingNotifications = true;
				handleCoverTradeNotifications = true;
				handleCptyTradeNotifications = true;
				handleCreditPostTradeNotifications = true;
				log.info("All notification handlers are supported, neither "
						+ MetaspacesConfigMBean.NOTIFICATION_HANDLER_GROUP_PREFIX
						+ "* properties nor "
						+ MetaspacesConfigMBean.NOTIFICATION_LISTENER_HANDLER_GROUPS_PROPERTY
						+ " property defined");
			} else {
				log.error("No "
						+ MetaspacesConfigMBean.NOTIFICATION_HANDLER_GROUP_PREFIX
						+ "* properties defined to match desired handler groups '"
						+ listenerHandlerGroup + "'");
				throw new Exception("Failed to configure notification listener");
			}
		} else if (noListenerHandlerProps) {
			/*
			 * No guarantee that this is the only listener server. Therefore, if
			 * we allow this listener to handle any group will create an overlap
			 * and causes out of sequence notification message handling.
			 */
			log.error(MetaspacesConfigMBean.NOTIFICATION_HANDLER_GROUP_PREFIX
					+ "* properties are defined, expecting notification listener handler group(s)");
			throw new Exception("Failed to configure notification listener");
		}

		String[] listeners = listenerHandlerGroup.split(" ");
		String desiredHandlerGroup = listeners[0];

		String[] handlers = handlerGroups.split(" ");

		for (String handler : handlers) {
			if (handler.equals(NOTIFICATION_HANDLER_NAME_STP)) {
				handlerGroupNameForSTP = desiredHandlerGroup;
				handleSTPNotifications = true;
			} else if (handler.equals(NOTIFICATION_HANDLER_NAME_GM)) {
				handlerGroupNameForGM = desiredHandlerGroup;
				handleGMNotifications = true;
			} else if (handler.equals(NOTIFICATION_HANDLER_NAME_MIS)) {
				handlerGroupNameForMIS = desiredHandlerGroup;
				handleMISNotifications = true;
			} else if (handler.equals(NOTIFICATION_HANDLER_NAME_EMAIL)) {
				handlerGroupNameForEmail = desiredHandlerGroup;
				handleEmailNotifications = true;
			} else if (handler.equals(NOTIFICATION_HANDLER_NAME_DONOTHING)) {
				handlerGroupNameForDoNothing = desiredHandlerGroup;
				handleDoNothingNotifications = true;
			} else if (handler.equals(NOTIFICATION_HANDLER_NAME_TRADE_TICKER)) {
				handlerGroupNameForTradeTicker = desiredHandlerGroup;
				handleTradeTickerNotifications = true;
			} else if (handler
					.equals(NOTIFICATION_HANDLER_NAME_DEALORDER_PERSISTENCE)) {
				handlerGroupNameForDealOrderPersistence = desiredHandlerGroup;
				handleDealOrderPersistence = true;
			} else if (handler.equals(NOTIFICATION_HANDLER_NAME_AUTH_TOKEN)) {
				handlerGroupNameForAuthtoken = desiredHandlerGroup;
				handleAuthTokenNotifications = true;
			} else if (handler.equals(NOTIFICATION_HANDLER_NAME_STAGING)) {
				handlerGroupNameForStaging = desiredHandlerGroup;
				handleStagingNotifications = true;
			} else if (handler.equals(NOTIFICATION_HANDLER_COVER_TRADE)) {
				handlerGroupNameForCoverTrade = desiredHandlerGroup;
				handleCoverTradeNotifications = true;
			} else if (handler.equals(NOTIFICATION_HANDLER_NAME_CPTY_TRD)) {
				handlerGroupNameForCptyTrd = desiredHandlerGroup;
				handleCptyTradeNotifications = true;
			} else if (handler
					.equals(NOTIFICATION_HANDLER_NAME_CREDIT_POSTTRADE)) {
				handlerGroupNameForCreditPostTrade = desiredHandlerGroup;
				handleCreditPostTradeNotifications = true;
			}
		}

		log.info("Listening on notification handling groups '"
				+ listenerHandlerGroup + "'");

		log.info("Successfully configured notification listener");

		// set the FileBackedNotificationPersistenceService here
		notificationManager.setNotificationWriter(new NotificationWriterImpl());

		notificationManager
				.setNotificationPreProcessor(new DealingModelPreProcessor());
		// adding a default notification filter
		Filter allMatchFilter = new Filter("*", "*", "*", "*", "*", "*");

		if (handleCptyTradeNotifications) {
			Filter filter = new Filter(
					PersistenceServiceFactory.getTradingMetaspaceName(), "*",
					"*", "*", "*", "*");
			notificationManager.subscribe(filter, handlerGroupNameForCptyTrd,
					new CptyTradePersistenceHandler());
			handlerSubscribers++;
		}

		if (handleSTPNotifications) {
			Filter filter = new Filter(
					PersistenceServiceFactory.getTradingMetaspaceName(), "*",
					PersistenceConstants.TRADE, "*", "*", "*");
			FinXMLNotificationHandler handler = new FinXMLNotificationHandler();
			handler.initialize();
			notificationManager.subscribe(filter, handlerGroupNameForSTP,
					handler);
			handlerSubscribers++;
		}

		if (handleTradeTickerNotifications) {
			Filter filter = new Filter(
					PersistenceServiceFactory.getTradingMetaspaceName(), "*",
					PersistenceConstants.TRADE, "*", "*", "*");
			notificationManager.subscribe(filter,
					handlerGroupNameForTradeTicker,
					new TradeTickerNotificationHandler());
			handlerSubscribers++;
		}

		if (handleMISNotifications) {
			handlerSubscribers = handlerSubscribers
					+ setupMISNotificationHandler(notificationManager,
							handlerGroupNameForMIS);
		}

		if (handleGMNotifications) {
			notificationManager.subscribe(allMatchFilter,
					handlerGroupNameForGM, new GMNotificationHandler(
							new GMMessageNotifier()));
			handlerSubscribers++;
		}
		if (handleCreditPostTradeNotifications) {
			Filter filter = new Filter(
					PersistenceServiceFactory.getTradingMetaspaceName(), "*",
					"*", "*", "*", "*");
			notificationManager.subscribe(filter,
					handlerGroupNameForCreditPostTrade,
					new CreditTradeNotificationHandler());
			handlerSubscribers++;
		}

		if (handleDealOrderPersistence) {
			handlerSubscribers = handlerSubscribers
					+ setupDealOrderPersistenceHandler(notificationManager,
							handlerGroupNameForDealOrderPersistence);
		}

		if (handleEmailNotifications) {
			Filter filter = new Filter(
					PersistenceServiceFactory.getTradingMetaspaceName(), "*",
					PersistenceConstants.TRADE, "*", "*", "*");
			notificationManager.subscribe(filter, handlerGroupNameForEmail,
					new EmailNotificationHandler());
			handlerSubscribers++;
		}

		if (handleAuthTokenNotifications) {
			Filter filter = new Filter(
					PersistenceServiceFactory.getUserSessionMetaspaceName(),
					"*", PersistenceConstants.AUTHTOKEN, "*", "*", "*");
			notificationManager.subscribe(filter, handlerGroupNameForAuthtoken,
					new AuthTokenNotificationHandler());
		}

		if (handleDoNothingNotifications) {
			notificationManager.subscribe(allMatchFilter,
					handlerGroupNameForDoNothing,
					new DoNothingNotificationHandler());
			handlerSubscribers++;
		}

		if (handleStagingNotifications) {
			Filter filter = new Filter(
					PersistenceServiceFactory.getTradingMetaspaceName(), "*",
					PersistenceConstants.STAGINGAREAORDER, "*", "*", "*");
			notificationManager.subscribe(filter, handlerGroupNameForStaging,
					new StagingAreaNotificationHandler());
			handlerSubscribers++;
		}

		if (handleCoverTradeNotifications) {
			handlerSubscribers = handlerSubscribers
					+ setupCoverTradeNotificationsHandler(notificationManager,
							handlerGroupNameForCoverTrade);
		}

		// only after all handlers have subscribed we should set up the receiver
		if (handlerSubscribers <= 0) {
			
			log.error("Notification listener was not started as there are no subscribers ");
			throw new Exception(
					"Invalid Configuration:Notification listener was not started as there are no subscribers ");
		}
		
		return notificationManager;
	}
    
	/**
	 * This startup class is loaded only when the server should be a
	 * notification server listener. That's why only in here we set up the
	 * reciever side of the notification transport.
	 * 
	 * @throws Exception
	 */
	private void configureNotifications() throws Exception {

		if (MetaspacesConfigMBeanImpl.getInstance()
				.isClusterModeEnabledForNotificationServer()) {
			joinNotificationServerCluster();
		} else {
			Properties listenerHandlerProps = MetaspacesConfigMBeanImpl
					.getInstance()
					.getNotificationListenerHandlerGroupsProperty();
			String listenerHandlerGroup = listenerHandlerProps
					.getProperty(
							MetaspacesConfigMBean.NOTIFICATION_LISTENER_HANDLER_GROUPS_PROPERTY,
							"");
			String handlerGroups = MetaspacesConfigMBeanImpl
					.getInstance()
					.getNotificationHandlerGroupProperties()
					.getProperty(
							MetaspacesConfigMBean.NOTIFICATION_HANDLER_GROUP_PREFIX
									+ listenerHandlerGroup);
			
			handlerGroups = (handlerGroups==null?"":handlerGroups);
			
			String queueSetNameBase = NotificationConfiguration
					.getInstance().getQueueSetNameBase();
			// strip out the "Q" in the end
			queueSetNameBase = queueSetNameBase.substring(0, queueSetNameBase.length()-1);
			NotificationManager notificationManager = configureNotifications(queueSetNameBase,
					listenerHandlerGroup, handlerGroups, NotificationConfiguration.getInstance().getRoutingKeyPrefix());
			notificationManager.newNotifierThreadPool();
			notificationManager.getNotificationTransport().setUpReceiver();
			notificationManager.getNotificationTransport()
					.setupReceiversOnAllQueueSets();
		}
	}

	private static void joinNotificationServerCluster() throws Exception {
		log.info(ConfigurationFactory.getServerMBean().getVirtualServerName()
				+ " joining cluster");
		ListenerStateTransitionHandler handler = new ListenerStateTransitionHandler();
		boolean isStarted = ClusterManagerHelper.getInstance().startInstance(
                MetaspacesConfigMBean.NS_CLUSTER_NAME, handler);
		if (!isStarted) {
			MessageLogger.getInstance().log(
					"NOTIFICATION.SERVER.FAILED.TO.JOIN.CLUSTER",
					NotificationRegistrationStartup.class.getName(),
					"Failed to add notification server instance to the cluster:"
							+ MetaspacesConfigMBean.NS_CLUSTER_NAME,
					ConfigurationFactory.getServerMBean()
							.getVirtualServerName());
			throw new Exception("Failed to add notification server instance:"
					+ ConfigurationFactory.getServerMBean()
							.getVirtualServerName() + " to the cluster:"
					+ MetaspacesConfigMBean.NS_CLUSTER_NAME);
		}
		log.info(ConfigurationFactory.getServerMBean().getVirtualServerName()
				+ " successfully joined the cluster:"
				+ MetaspacesConfigMBean.NS_CLUSTER_NAME);
	}
	
    private static int setupDealOrderPersistenceHandler(NotificationManager notificationManager, String handlerGroupNameForDealOrderPersistence) {
    	IChiefDealerNotifier chiefDealerNotifier = new ChiefDealerNotifier();
        CDQPersistenceNotificationHandler dealOrderNotificationHandler = new CDQPersistenceNotificationHandler(chiefDealerNotifier);
        dealOrderNotificationHandler.initialize();

        Filter filter = new Filter(PersistenceServiceFactory.getTradingMetaspaceName(), "*", PersistenceConstants.ORDERREQUEST, "*", "*", "*");
        notificationManager.subscribe(filter, handlerGroupNameForDealOrderPersistence, dealOrderNotificationHandler);

        filter = new Filter(PersistenceServiceFactory.getTradingMetaspaceName(), "*", PersistenceConstants.TRADE, "*", "*", "*");
        notificationManager.subscribe(filter, handlerGroupNameForDealOrderPersistence, dealOrderNotificationHandler);

        filter = new Filter(PersistenceServiceFactory.getTradingMetaspaceName(), "*", PersistenceConstants.MATCHEVENT, "*", "*", "*");
        notificationManager.subscribe(filter, handlerGroupNameForDealOrderPersistence, dealOrderNotificationHandler);

        return 3;
    }

    private static int setupCoverTradeNotificationsHandler(NotificationManager notificationManager, String handlerGroupNameForCoverTrade) {
        CoverTradeNotificationHandler handler = new CoverTradeNotificationHandler();
        Filter filter = new Filter(PersistenceServiceFactory.getTradingMetaspaceName(), "*", PersistenceConstants.TRADE, "*", "*", "*");
        notificationManager.subscribe(filter, handlerGroupNameForCoverTrade, handler);

        filter = new Filter(PersistenceServiceFactory.getTradingMetaspaceName(), "*", PersistenceConstants.MATCHEVENT, "*", "*", "*");
        notificationManager.subscribe(filter, handlerGroupNameForCoverTrade, handler);

        filter = new Filter(PersistenceServiceFactory.getTradingMetaspaceName(), "*", PersistenceConstants.ORDERREQUEST, "*", "*", "*");
        notificationManager.subscribe(filter, handlerGroupNameForCoverTrade, handler);

        return 3;
    }

    private static int setupMISNotificationHandler(NotificationManager notificationManager, String handlerGroupNameForMIS) {
        MISNotificationHandler misNotificationHandler = new MISNotificationHandler();
        misNotificationHandler.initialize();

        Filter filter = new Filter(PersistenceServiceFactory.getTradingMetaspaceName(), "*", PersistenceConstants.ORDERREQUEST, "*", "*", "*");
        notificationManager.subscribe(filter, handlerGroupNameForMIS, misNotificationHandler);

        filter = new Filter(PersistenceServiceFactory.getTradingMetaspaceName(), "*", PersistenceConstants.TRADE, "*", "*", "*");
        notificationManager.subscribe(filter, handlerGroupNameForMIS, misNotificationHandler);
        
        filter = new Filter(PersistenceServiceFactory.getRmmMetaspaceName(), "*", PersistenceConstants.NETTING, "*", "*", "*");
        notificationManager.subscribe(filter, handlerGroupNameForMIS, misNotificationHandler);
        
        filter = new Filter(PersistenceServiceFactory.getRmmMetaspaceName(), "*", PersistenceConstants.NETTINGTR, "*", "*", "*");
        notificationManager.subscribe(filter, handlerGroupNameForMIS, misNotificationHandler);
        
        filter = new Filter(PersistenceServiceFactory.getRmmMetaspaceName(), "*", PersistenceConstants.NETTINGMKTPRC, "*", "*", "*");
        notificationManager.subscribe(filter, handlerGroupNameForMIS, misNotificationHandler);
        
        filter = new Filter(PersistenceServiceFactory.getTradingMetaspaceName(), "*", PersistenceConstants.STAGINGAREAORDER, "*", "*", "*");
        notificationManager.subscribe(filter, handlerGroupNameForMIS, misNotificationHandler);

        return 6;
    }

    private static class DoNothingNotificationHandler implements NotificationHandler {

        @Override
        public NHResponse handle(Notification notification) {
            if( log.isDebugEnabled() )
                log.debug("Notification " + notification);
            return new NHResponse(NHResponseCode.SUCCESS);
        }

        @Override
        public long getLongIdentifier() {
            return com.integral.notifications.NotificationConfiguration.NOTIFICATION_HANDLER_DONOTHING.getLongIdentifier();
        }

        @Override
        public NHResponse handleRedelivery(Notification notification,String errorDesc) {
            return new NHResponse(NHResponseCode.SUCCESS);
        }
    }
}
