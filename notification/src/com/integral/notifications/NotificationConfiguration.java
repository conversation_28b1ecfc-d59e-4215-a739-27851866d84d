package com.integral.notifications;

/**
 * Created with IntelliJ IDEA.
 * User: shahr
 * Date: 3/17/14
 * Time: 6:22 PM
 */
public enum NotificationConfiguration {

    NOTIFICATION_HANDLER_CREDIT(1<<1,"Credit"),
    NOTIFICATION_HANDLER_GM(1<<2,"GM"),
    NOTIFICATION_HANDLER_TRADE_TICKER(1<<3,"TradeTicker"),
    NOTIFICATION_HANDLER_DONOTHING(1<<4,"DoNothing"),
    NOTIFICATION_HANDLER_STP(1<<5,"STP"),
    NOTIFICATION_HANDLER_CDQ(1<<6,"CDQ"),
    NOTIFICATION_HANDLER_EMAIL(1<<7,"Email"),
    NOTIFICATION_HANDLER_POSITION(1<<8,"Position"),
    NOTIFICATION_HANDLER_AUTHTOKEN(1<<9,"AuthToken"),
    NOTIFICATION_HANDLER_MIS(1<<10,"MIS"),
    NOTIFICATION_HANDLER_STAGING(1<<11,"Staging"),
    NOTIFICATION_HANDLER_COVER_TRADE(1<<12,"CoverTrade"),
    NOTIFICATION_HANDLER_CPTY_TRD(1<<13,"CptyTrade"),
    NOTIFICATION_HANDLER_CREDIT_POSTTRD(1<<14,"CreditPostTrade");

    private long longIdentifier;
    private String notificationHandlerName;

    NotificationConfiguration(long longIdentifier, String notificationHandlerName){
     this.longIdentifier=  longIdentifier;
     this.notificationHandlerName = notificationHandlerName;
    }

    public long getLongIdentifier() {
        return longIdentifier;
    }

    public String getNotificationHandlerName() {
        return notificationHandlerName;
    }


}
