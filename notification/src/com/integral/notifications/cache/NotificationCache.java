package com.integral.notifications.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;

import com.integral.log.Log;
import com.integral.log.LogFactory;

import java.util.Map;

import static java.util.concurrent.TimeUnit.MILLISECONDS;

/**
 * Created by pulkit on 2/13/14.
 */
public abstract class NotificationCache<T> implements Cache<T>{

    private com.google.common.cache.Cache<String,T> objectCache;
    private CacheRemovalListener<T> listener;
    protected Log log = LogFactory.getLog(NotificationCache.class);

    public NotificationCache(int cacheSize,long expirationTimeInMS,int concurrencyLevel) {
        log.info("Configuring Cache for: ");
        CacheBuilder cb = CacheBuilder.newBuilder();
        listener = new CacheRemovalListener<T>();
        cb.removalListener(listener);
        cb.concurrencyLevel(concurrencyLevel);
        cb.maximumSize(cacheSize);
        cb.expireAfterAccess(expirationTimeInMS, MILLISECONDS);
        cb.expireAfterWrite(expirationTimeInMS, MILLISECONDS);

        objectCache = cb.build();
    }

    public void add(String key,T object){
        objectCache.put(key,object);
    }

    public T get(String key){
        return objectCache.getIfPresent(key);
    }
    
    public void invalidate(String key){
        objectCache.invalidate(key);
    }

    public long size(){
        return objectCache.size();
    }

    public void clear(){
        log.error("GOT A REQUEST to CLEAR THE CACHE. Current Element count: "+objectCache.size());
    }

    public Map getAllCachedEntities()
    {
        return objectCache.asMap();
    }

    public class CacheRemovalListener<T> implements RemovalListener {
        private Log listenerLogger = LogFactory.getLog(CacheRemovalListener.class);
        private T cls;

        public CacheRemovalListener(){

        }

        @Override
        public void onRemoval(RemovalNotification notification) {
            if(listenerLogger.isDebugEnabled()){
                StringBuilder sb = new StringBuilder(cls.getClass().getName()).append(" cache removal of key=").append(notification.getKey())
                        .append(" ,value=").append(notification.getValue().toString());
                if(notification.getCause() != null){
                    sb.append(" , cause=").append(notification.getCause().name());
                }
                listenerLogger.debug(sb.toString());
            }
        }
    }
}
