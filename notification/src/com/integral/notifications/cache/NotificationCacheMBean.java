package com.integral.notifications.cache;

import com.integral.system.configuration.IdcMBean;

/**
 * Created by pulkit on 2/13/14.
 */
public interface NotificationCacheMBean extends IdcMBean{

    boolean isCacheEnabled();

    int getCacheSize();

    long getExpirationTimeInMS();

    int getCacheConcurrency();

    boolean isCacheEnabled(String type);

    int getCacheSize(String type);

    long getExpirationTimeInMS(String type);

    int getCacheConcurrency(String type);
}
