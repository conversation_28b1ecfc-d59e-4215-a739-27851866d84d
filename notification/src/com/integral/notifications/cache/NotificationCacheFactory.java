package com.integral.notifications.cache;

import com.integral.finance.dealing.Deal;
import com.integral.finance.dealing.Order;
import com.integral.finance.dealing.StagingRequestC;
import com.integral.finance.fx.FXSingleLeg;
import com.integral.finance.dealing.RequestC;
import com.integral.netting.model.NettingPortfolioC;
import com.integral.netting.model.NettingTradeRequestC;
import com.integral.notifications.chiefdealer.dealorder.CDQDealExpirableCache;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.notifications.chiefdealer.dealorder.CDQOrderExpirableCache;
import com.integral.notifications.chiefdealer.dealorder.CDViewManager;
import com.integral.notifications.chiefdealer.dealorder.CDViewManagersCache;
import com.integral.notifications.mis.MISFXSingleLegExpirableCache;
import com.integral.notifications.mis.MISNettingPortfolioExpirableCache;
import com.integral.notifications.mis.MISNettingTradeRequestExpirableCache;
import com.integral.notifications.mis.MISRequestExpirableCache;
import com.integral.notifications.mis.MISStagingAreaExpirableCache;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by pulkit on 2/13/14.
 */
public class NotificationCacheFactory {
    private static final Log log = LogFactory.getLog(NotificationCacheFactory.class);

    private static ConcurrentHashMap<Class, NotificationCache> allCaches = new ConcurrentHashMap<Class, NotificationCache>();

    public static <T> NotificationCache getNotificationCache(T type) {
        String typeSuffix = type.toString().substring(type.toString().lastIndexOf(".")+1).trim();
        log.info("Trying to initialize for: "+type.toString());
        NotificationCacheMBean cacheMBean = NotificationCacheMBeanImpl.getInstance();
        boolean cacheEnabled = cacheMBean.isCacheEnabled(typeSuffix);
        log.info(type.toString()+ "maps to:"+typeSuffix+ " for loading configurations");
        int cacheSize = cacheMBean.getCacheSize(typeSuffix);
        long cacheExpirationTime = cacheMBean.getExpirationTimeInMS(typeSuffix);
        int cacheConcurrency = cacheMBean.getCacheConcurrency(typeSuffix);
        log.info("Configuring cache for type: "+ type+ " cacheEnabled=" + cacheEnabled +
                " cacheSize=" + cacheSize +
                " cacheExpirationTime=" + cacheExpirationTime+
                " cacheConcurrency=" + cacheConcurrency);
        if (Deal.class.equals(type)) {
            if (allCaches.get(Deal.class) == null) {
                synchronized (NotificationCacheFactory.class) {
                    NotificationCache cache = allCaches.get(Deal.class);
                    if(cache == null){
                        cache = new CDQDealExpirableCache(cacheSize, cacheExpirationTime, cacheConcurrency, cacheEnabled);
                        allCaches.put(Deal.class,cache);
                    }
                }
            }
            return  allCaches.get(Deal.class);
        } else if (Order.class.equals(type)) {
            if (allCaches.get(Order.class) == null) {
                synchronized (NotificationCacheFactory.class) {
                    NotificationCache cache = allCaches.get(Order.class);
                    if(cache == null){
                        cache = new CDQOrderExpirableCache(cacheSize, cacheExpirationTime, cacheConcurrency, cacheEnabled);
                        allCaches.put(Order.class,cache);
                    }
                 }
            }
            return  allCaches.get(Order.class);
        } else if (FXSingleLeg.class.equals(type)) {
            if (allCaches.get(FXSingleLeg.class) == null) {
                synchronized (NotificationCacheFactory.class) {
                    NotificationCache cache = allCaches.get(FXSingleLeg.class);
                    if(cache == null){
                        cache = new MISFXSingleLegExpirableCache(cacheSize, cacheExpirationTime, cacheConcurrency, cacheEnabled);
                        allCaches.put(FXSingleLeg.class,cache);
                    }
                }
            }
            return allCaches.get(FXSingleLeg.class);
        } else if (RequestC.class.equals(type)) {
            if (allCaches.get(RequestC.class) == null) {
                synchronized (NotificationCacheFactory.class) {
                    NotificationCache cache = allCaches.get(RequestC.class);
                    if(cache == null){
                        cache = new MISRequestExpirableCache(cacheSize, cacheExpirationTime, cacheConcurrency, cacheEnabled);
                        allCaches.put(RequestC.class,cache);
                    }
                    return cache;
                }
            }
            return allCaches.get(RequestC.class);
        } else if (StagingRequestC.class.equals(type)) {
            if (allCaches.get(StagingRequestC.class) == null) {
                synchronized (NotificationCacheFactory.class) {
                    NotificationCache cache = allCaches.get(StagingRequestC.class);
                    if(cache == null){
                        cache = new MISStagingAreaExpirableCache(cacheSize, cacheExpirationTime, cacheConcurrency, cacheEnabled);
                        allCaches.put(StagingRequestC.class,cache);
                    }
                    return cache;
                }
            }
            return allCaches.get(StagingRequestC.class);
        }else if (CDViewManager.class.equals(type)) {
            if (allCaches.get(CDViewManager.class) == null) {
                synchronized (NotificationCacheFactory.class) {
                    NotificationCache cache = allCaches.get(CDViewManager.class);
                    if(cache == null){
                        cache = new CDViewManagersCache(cacheSize, cacheExpirationTime, cacheConcurrency, cacheEnabled);
                        allCaches.put(CDViewManager.class,cache);
                    }
                    return cache;
                }
            }
            return allCaches.get(CDViewManager.class);
        } else if (NettingPortfolioC.class.equals(type)) {
            if (allCaches.get(NettingPortfolioC.class) == null) {
                synchronized (NotificationCacheFactory.class) {
                    NotificationCache cache = allCaches.get(NettingPortfolioC.class);
                    if(cache == null){
                        cache = new MISNettingPortfolioExpirableCache(cacheSize, cacheExpirationTime, cacheConcurrency, cacheEnabled);
                        allCaches.putIfAbsent(NettingPortfolioC.class,cache);
                    }
                    return cache;
                }
            }
            return allCaches.get(NettingPortfolioC.class);
        } else if (NettingTradeRequestC.class.equals(type)) {
            if (allCaches.get(NettingTradeRequestC.class) == null) {
                synchronized (NotificationCacheFactory.class) {
                    NotificationCache cache = allCaches.get(NettingTradeRequestC.class);
                    if(cache == null){
                        cache = new MISNettingTradeRequestExpirableCache(cacheSize, cacheExpirationTime, cacheConcurrency, cacheEnabled);
                        allCaches.putIfAbsent(NettingTradeRequestC.class,cache);
                    }
                    return cache;
                }
            }
            return allCaches.get(NettingTradeRequestC.class);
        }
        throw new UnknownError("Cache for type:" + type.getClass() + " is not supported");
    }


    public static ConcurrentHashMap<Class, NotificationCache> getAllCaches() {
        return allCaches;
    }
}
