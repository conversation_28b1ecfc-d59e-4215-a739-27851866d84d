package com.integral.notifications.cache;

import com.integral.system.configuration.IdcMBeanC;

import java.util.concurrent.atomic.AtomicBoolean;
import static com.integral.notifications.cache.CacheConstants.*;
/**
 * Created by pulkit on 2/13/14.
 */
public class NotificationCacheMBeanImpl extends IdcMBeanC implements NotificationCacheMBean {

    private AtomicBoolean initialized;
    private Boolean cacheEnabled;
    private Long expirationTimeInMS;
    private Integer cacheSize;
    private Integer cacheConcurrency;

    private static class ConfigHolder {
        private static NotificationCacheMBean INSTANCE = new NotificationCacheMBeanImpl();
    }

    public static NotificationCacheMBean getInstance() {
        return ConfigHolder.INSTANCE;
    }

    private NotificationCacheMBeanImpl() {
        super("NotificationCache");
    }
    @Override
    public void initialize() {
        super.initialize();
        if (initialized == null) {
            initialized = new AtomicBoolean(false);
        }
        if (!initialized.get()) {
            cacheEnabled = getBooleanProperty(CACHE_ENABLED,true);
            expirationTimeInMS= getLongProperty(CACHE_EXPIRATIONTIME,300000);
            cacheSize= getIntProperty(CACHE_SIZE,400);
            cacheConcurrency= getIntProperty(CACHE_CONCURRENCY,16);
        }
        initialized.set(true);
    }

    @Override
    public boolean isCacheEnabled() {
        return cacheEnabled;
    }

    @Override
    public int getCacheSize() {
        return cacheSize;
    }

    @Override
    public long getExpirationTimeInMS() {
        return expirationTimeInMS;
    }

    @Override
    public int getCacheConcurrency() {
        return cacheConcurrency;
    }

    @Override
    public boolean isCacheEnabled(String type) {
        return getBooleanProperty(CACHE_ENABLED+"."+type,isCacheEnabled());
    }

    @Override
    public int getCacheSize(String type) {
        return getIntProperty(CACHE_SIZE+"."+type,getCacheSize());
    }

    @Override
    public long getExpirationTimeInMS(String type) {
        return getLongProperty(CACHE_EXPIRATIONTIME+"."+type,getExpirationTimeInMS());
    }

    @Override
    public int getCacheConcurrency(String type) {
        return getIntProperty(CACHE_CONCURRENCY+"."+type,getCacheConcurrency());
    }
}
