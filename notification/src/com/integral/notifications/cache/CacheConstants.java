package com.integral.notifications.cache;

/**
 * Created by pulkit on 2/13/14.
 */
public interface CacheConstants {

    String CACHE_SIZE = "IDC.NOTIFICATION.CACHE.SIZE";
    String CACHE_EXPIRATIONTIME = "IDC.NOTIFICATION.CACHE.EXPIRATIONTIME";
    String CACHE_ENABLED = "IDC.NOTIFICATION.CACHE.ENABLED";
    String CACHE_CONCURRENCY = "IDC.NOTIFICATION.CACHE.CONCURRENCY";

    String CACHE_MISREQUEST = "MISREQUEST";
    String CACHE_MISFXSINGLLEG = "MISFXSINGLLEG";
    String CACHE_CDVIEWMANAGER = "CDVIEWMANAGER";

}
