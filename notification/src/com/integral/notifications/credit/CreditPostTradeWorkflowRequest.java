package com.integral.notifications.credit;

import com.integral.is.common.ApplicationEventCodes;
import com.integral.model.dealing.MatchEvent;
import com.integral.model.dealing.SingleLegTrade;

/**
 * 
 * Request object captures necessary data to process the data 
 *
 */
final public class CreditPostTradeWorkflowRequest 
{

	private SingleLegTrade singleLegTrade = null;
	private int applicationEventCode;
	private MatchEvent matchEvent = null;
	private boolean isMatchEvent = false;

	/**
	 * 
	 * @param legTrade
	 * @param applicationEventCodes
	 */
	public CreditPostTradeWorkflowRequest(SingleLegTrade legTrade,int applicationEventCode) 
	{
		singleLegTrade = legTrade;
		this.applicationEventCode = applicationEventCode;
	}

	/**
	 * 
	 * @param matchEvent
	 * @param applicationEventCodes
	 */
	public CreditPostTradeWorkflowRequest(MatchEvent matchEvent,int applicationEventCode) 
	{
		this.matchEvent = matchEvent;
		this.applicationEventCode = applicationEventCode;
		this.isMatchEvent = true;
	}
	
	/**
	 * 
	 */
	public SingleLegTrade getSingleLegTrade() 
	{
		return singleLegTrade;
	}
	
	public MatchEvent getMatchEvent() 
	{
		return matchEvent;
	}
	/**
	 * 
	 * @return
	 */
	public int getApplicationEventCodes() 
	{
		return applicationEventCode;
	}

	public boolean isNetorNetted()
	{
		return ( applicationEventCode == ApplicationEventCodes.EVENT_ESP_TRADE_NET.getCode() ||
				applicationEventCode == ApplicationEventCodes.EVENT_ESP_TRADE_NETTED.getCode());
	}
	/**
	 * 
	 * @return
	 */
	public boolean isVerification()
	{
        return ( applicationEventCode == ApplicationEventCodes.EVENT_ESP_TRADE_VERIFIED.getCode() 
        		|| applicationEventCode == ApplicationEventCodes.EVENT_ESP_TRADE_CREATED_VERIFIED.getCode()
        		|| applicationEventCode == ApplicationEventCodes.EVENT_ESP_TRADE_PRE_RATE_VERIFIED.getCode());
	}
	
	public boolean isPostRateVerification()
	{
        return (applicationEventCode == ApplicationEventCodes.EVENT_ESP_TRADE_POST_RATE_VERIFIED.getCode());
	}
	
	public boolean isPreRateVerification()
	{
        return (applicationEventCode == ApplicationEventCodes.EVENT_ESP_TRADE_PRE_RATE_VERIFIED.getCode());
	}
	/**
	 * 
	 * @return
	 */
	public boolean isAcceptance()
	{
        return (applicationEventCode == ApplicationEventCodes.EVENT_ESP_TRADE_ACCEPT.getCode());
	}
	
	/**
	 * 
	 * @return
	 */
	public boolean isRejection()
	{
        return (applicationEventCode == ApplicationEventCodes.EVENT_ESP_TRADE_REJECTED.getCode());
	}
	
	public boolean isTradeRequestExpiry()
	{
        return (applicationEventCode == ApplicationEventCodes.EVENT_ESP_TRADE_REQUEST_EXPIRE.getCode());
	}
	
	public boolean isOrderMatchRequestExpiry()
	{
        return ((applicationEventCode == ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_EXPIRE.getCode())
        		|| (applicationEventCode == ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_CANCEL.getCode()) 
        		|| (applicationEventCode == ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_PRERATE_EXPIRE.getCode()) 
        		|| (applicationEventCode == ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_PRERATE_CANCEL.getCode())
        		);
	}
	
	public boolean isOrderMatchSubmit() 
	{
		return (applicationEventCode == ApplicationEventCodes.EVENT_ESP_ORDER_MATCH_SUBMIT.getCode());
	}
	
	public boolean isMatchEventCaptured()
	{
		return isMatchEvent;
	}
	
}
