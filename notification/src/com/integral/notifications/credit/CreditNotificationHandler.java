package com.integral.notifications.credit;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.creditLimit.*;
import com.integral.finance.creditLimit.configuration.CreditConfigurationFactory;
import com.integral.finance.creditLimit.functor.CreditUtilizationCacheSynchronizeNotificationFunctorC;
import com.integral.is.common.ApplicationEventCodes;
import com.integral.is.common.util.ISTransactionManager;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.notifications.NotificationConfiguration;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.session.IdcTransaction;
import com.integral.session.RemoteTransactionNotification;
import com.integral.spaces.notification.NHResponse;
import com.integral.spaces.notification.NHResponseCode;
import com.integral.spaces.notification.Notification;
import com.integral.spaces.notification.NotificationHandler;

import java.util.EnumMap;
import java.util.HashMap;

public class CreditNotificationHandler implements NotificationHandler {

	private static Log log = LogFactory.getLog( CreditNotificationHandler.class);
	
    private static final EnumMap<ApplicationEventCodes, String> creditEventTranslation = new EnumMap<ApplicationEventCodes, String>(ApplicationEventCodes.class);

    static {
    	creditEventTranslation.put(ApplicationEventCodes.EVENT_ESP_CREDIT_TAKE, CreditMessageEvent.USE.getName());
    	creditEventTranslation.put(ApplicationEventCodes.EVENT_ESP_CREDIT_UNDO, CreditMessageEvent.REMOVE.getName());
    	creditEventTranslation.put(ApplicationEventCodes.EVENT_ESP_CREDIT_UPDATE, CreditMessageEvent.UPDATE.getName());
    }
	
	@Override
	public NHResponse handle(Notification notification) {
        return new NHResponse(NHResponseCode.SUCCESS);
    }

    @Override
    public NHResponse handleRedelivery(Notification notification,String errorDesc) {
        return handle(notification);
    }

    private void processCreditUtilizationEvent( CreditUtilizationEvent cue, String event ) {
        SpaceCreditUtilizationEventC spaceCue = ( SpaceCreditUtilizationEventC )cue;
		HashMap<String, Object> propertiesMap = new HashMap<String, Object>( 4 );
		log.info( "Processing CREDIT message for guid " + cue.getGUID() + " and event " + event);
		propertiesMap.put( RemoteTransactionNotification.REMOTE_ONLY_KEY,
				RemoteTransactionNotification.REMOTE_ONLY_VALUE );
		LegalEntity legalEntity = cue.getLegalEntity();
		propertiesMap.put( CreditLimit.CREDIT_PROVIDER_ORGANIZATION, legalEntity.getOrganization().getShortName() );
		propertiesMap.put( CreditLimit.CREDIT_COUNTERPARTY, cue.getTradingParty().getShortName() );
		propertiesMap.put( CreditLimitConstants.EVENT_PROPERTY, event );

		propertiesMap.put( CreditLimit.NAMESPACE, cue.getNamespace().getShortName());
		propertiesMap.put(CreditLimit.SPACES_ENABLED, Boolean.toString(true));

		long[] cuObjectIds = new long[1];
		String[] guids = new String[1];

		int index = 0;
		CreditUtilization seCu = cue.getCreditUtilization();
		CreditUtilization dbCu = ( CreditUtilization ) ReferenceDataCacheC.getInstance().getEntityByObjectId(
				seCu.getObjectID(), CreditUtilization.class );

		cuObjectIds[index] = dbCu.getObjectID();
		guids[index] = cue.getGUID();
		propertiesMap.put( CreditLimit.CREDIT_UTILIZATION, cuObjectIds );
		propertiesMap.put( CreditLimit.CREDIT_UTILIZATION_EVENT, guids );
		
        propertiesMap.put( CreditLimitConstants.CREDIT_LIMIT_PEER_VIRTUAL_SERVER, spaceCue.getVirtualServer() );
		
		// Starting a transaction so that commit functor is invoked across the grid.
		ISTransactionManager.setUser( ISUtilImpl.getInstance().getDefaultUser());
		IdcTransaction tx = ISTransactionManager.startTransaction("CreditNotificationHandler");
		tx.addRemoteFunctor( CreditUtilizationCacheSynchronizeNotificationFunctorC.class.getName(), propertiesMap );
		ISTransactionManager.endTransaction(tx, "CreditNotificationHandler");
	}

    @Override
    public long getLongIdentifier() {
        return NotificationConfiguration.NOTIFICATION_HANDLER_CREDIT.getLongIdentifier();
    }
}
