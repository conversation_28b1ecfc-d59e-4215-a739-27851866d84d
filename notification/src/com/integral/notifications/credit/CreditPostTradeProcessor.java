package com.integral.notifications.credit;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import com.integral.is.common.util.TradeUtil;
import com.integral.is.finance.quote.calculator.CreditServiceFacade;
import com.integral.is.finance.quote.calculator.CreditServiceFacadeFactory;
import com.integral.is.finance.quote.calculator.CreditServiceFacadeFactory.CreditServiceType;
import com.integral.is.spaces.fx.persistence.DealingModelTradeHelper;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.MatchEvent;
import com.integral.model.dealing.OrderMatchRequest;
import com.integral.model.dealing.SingleLegOrderMatch;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.notifications.cache.NotificationCache;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.util.CollectionUtil;
import com.integral.util.Tuple;

public final class CreditPostTradeProcessor implements CreditMessageProcessor 
{
	private static Log log = LogFactory.getLog(CreditPostTradeProcessor.class);
	
    private CreditServiceFacade creditServiceFacade  = null; 
    
    private static MatchEventTradeMapperCache eventTradeMapperCache = new MatchEventTradeMapperCache(
    		RuntimeFactory.getServerRuntimeMBean().getPostCreditProcessorCacheSize(),
    		RuntimeFactory.getServerRuntimeMBean().getPostCreditProcessorCacheExpTimeMills(),
    		RuntimeFactory.getServerRuntimeMBean().getPostCreditProcessorCacheConcurrency());
    
   
    
	public CreditPostTradeProcessor() 
	{
		creditServiceFacade = CreditServiceFacadeFactory.getInstance(CreditServiceType.NOTIFICATION);
	}
	/**
	 * 
	 */
	public CreditPostTradeWorkflowResponse process(CreditPostTradeWorkflowRequest creditPostTradeWorkflowRequest,CreditPostTradeWorkflowResponse postTradeResponse ) 
	{
		SingleLegTrade singleLegTrade = creditPostTradeWorkflowRequest.getSingleLegTrade();
		MatchEvent matchEvent = creditPostTradeWorkflowRequest.isMatchEventCaptured()?creditPostTradeWorkflowRequest.getMatchEvent():singleLegTrade.getMatchEvent();

        if(creditPostTradeWorkflowRequest.isPostRateVerification())
        {
            log.info("[OCX-Workflow] {PostRateVerification} Started -- MatchEvent " + matchEvent);
            StringBuilder logging = new StringBuilder("[OCX-Workflow] {PostRateVerification}  { MatchEvent:");
            logging.append(matchEvent.get_id()).append(",MultiFill:").append(matchEvent.isMultiFill());
            log.info( logging.toString() );

            CreditPostTradeUtil.populateCreditWorkflowState( singleLegTrade );
            WorkflowMessage message = creditServiceFacade.updateCreditAmountMultiLevel((SingleLegOrderMatch)matchEvent, singleLegTrade,
                    singleLegTrade.getLegalEntity(), singleLegTrade.getTradingParty().getLegalEntity());

            if( message.getStatus()!=MessageStatus.SUCCESS)
            {
                postTradeResponse.setFailed("Failed to Update Credit Post Trade");
                return postTradeResponse;
            }
            eventTradeMapperCache.setPostRateFilling( matchEvent );
            log.info("[OCX-Workflow] {Verification} Completed -- MatchEvent " + matchEvent);
        }
        else if(creditPostTradeWorkflowRequest.isAcceptance() ||
       			(singleLegTrade!=null && 
       			TradeUtil.isDirectedOrderTakerTradeBetweenClearingMembers(singleLegTrade) && creditPostTradeWorkflowRequest.isVerification()))
        {
	       	log.info("[OCX-Workflow] {Acceptance} Started -- MatchEvent " + matchEvent);

       		StringBuilder logging = new StringBuilder("[OCX-Workflow] {Acceptance}  { MatchEvent:");
    		logging.append(matchEvent.get_id()).append( ",MultiFill:" ).append(matchEvent.isMultiFill());
    		logging.append( ",NFills:" ).append(matchEvent.getNumberOfFills());
    		logging.append(",Trade:").append( singleLegTrade ).append(",Amt:").append( singleLegTrade.getDealtAmount());
    		logging.append(",FirstTrade:").append(singleLegTrade.getFirstTradeId()).append("}");    	
    		log.info(logging.toString());
        	WorkflowMessage message = creditServiceFacade.takeCreditSingleLevel(singleLegTrade,true, true, creditPostTradeWorkflowRequest.isAcceptance() );
        	CreditPostTradeUtil.manageCreditWorkflowState(singleLegTrade);
			if ( matchEvent.isMultiFill() )
            {
                eventTradeMapperCache.track( singleLegTrade );
            }
	       	if( message.getStatus()==MessageStatus.SUCCESS)
	       	{
	       		postTradeResponse.setSuccessful();
	       	}
	       	log.info("[OCX-Workflow] {Acceptance} Completed -- MatchEvent " + matchEvent);
        }       	
        else if(creditPostTradeWorkflowRequest.isRejection())
        {    	
	       	log.info("[OCX-Workflow] {Rejection} Started -- MatchEvent " + matchEvent);

    		StringBuilder logging = new StringBuilder("[OCX-Workflow] {Rejection}  { MatchEvent:");
    		logging.append(matchEvent.get_id()).append( ",MultiFill:" ).append(matchEvent.isMultiFill());
    		logging.append( ",NFills:" ).append( matchEvent.getNumberOfFills() );
    		logging.append(",Trade:").append( singleLegTrade ).append(",Amt:").append( singleLegTrade.getDealtAmount());
    		logging.append(",FirstTrade:").append(singleLegTrade.getFirstTradeId()).append( "}" );
    		log.info(logging.toString());
    		eventTradeMapperCache.setRejection(matchEvent);
    		if(!CreditPostTradeUtil.populateCreditWorkflowState(singleLegTrade))
    		{
    			postTradeResponse.setFailed("Credit Rejection Failed - Trade ID " +  singleLegTrade);
		       	log.info("[OCX-Workflow] {Rejection} - No Credit Workflow State for Trade  " + singleLegTrade );
    		}
    		else
    		{
                WorkflowMessage message = creditServiceFacade.undoCreditSingleLevel( singleLegTrade, true );
                if ( message.getStatus() == MessageStatus.SUCCESS )
                {
                    postTradeResponse.setSuccessful();
                }
            }
    		CreditPostTradeUtil.clearCreditWorkflowState(singleLegTrade);
	       	log.info("[OCX-Workflow] {Rejection} Completed -- MatchEvent " + matchEvent);

        }
    	else if(creditPostTradeWorkflowRequest.isVerification())
        { 
	       	log.info("[OCX-Workflow] {Verification} Started -- MatchEvent " + matchEvent);
        	StringBuilder logging = new StringBuilder("[OCX-Workflow] {Verification}  { MatchEvent:");
    		logging.append(matchEvent.get_id()).append(",MultiFill:").append(matchEvent.isMultiFill());
    		logging.append(",NFills:").append( matchEvent.getNumberOfFills() );
    		logging.append(",Trade:").append(singleLegTrade).append(",Amt:").append( singleLegTrade.getDealtAmount());
    		logging.append(",FirstTrade:").append(singleLegTrade.getFirstTradeId()).append("}");
    		log.info(logging.toString());
    		if(singleLegTrade.isPostTradeCreditUtilizationUpdate())
        	{
   				CreditPostTradeUtil.populateCreditWorkflowState(singleLegTrade);
    			WorkflowMessage message = creditServiceFacade.updateCreditAmountSingleLevel(singleLegTrade);
    	       	if( message.getStatus()!=MessageStatus.SUCCESS)
    	       	{
    	       		postTradeResponse.setFailed("Failed to Update Credit Post Trade");
    	       		return postTradeResponse;
    	       	}
    	       	if(!matchEvent.isMultiFill())
    	       	{
    	       		CreditPostTradeUtil.clearCreditWorkflowState(singleLegTrade);
    	       	}
        	}
        	else
    		if(matchEvent.isMultiFill())
    		{
    			eventTradeMapperCache.trackFills(singleLegTrade);
    		}
	       	log.info("[OCX-Workflow] {Verification} Completed -- MatchEvent " + matchEvent);
        }
        else if(creditPostTradeWorkflowRequest.isNetorNetted())
        {
    		StringBuilder logging = new StringBuilder("[OCX-Workflow] {Net Or Netted }  { MatchEvent:");
    		logging.append(matchEvent.get_id()).append(",MultiFill:").append(matchEvent.isMultiFill());
    		logging.append(",NFills:").append(matchEvent.getNumberOfFills());
    		logging.append(",Trade:").append(singleLegTrade).append(",Amt:").append( singleLegTrade.getDealtAmount());
    		logging.append(",FirstTrade:").append(singleLegTrade.getFirstTradeId()).append("}");
    		log.info(logging.toString());
        }
    	else if(creditPostTradeWorkflowRequest.isOrderMatchSubmit())
        {     		
    		OrderMatchRequest orderMatchRequest = (OrderMatchRequest)creditPostTradeWorkflowRequest.getMatchEvent();    		
	       	log.info("[OCX-Workflow] {OrderMatchSubmit} Started -- MatchEvent " + orderMatchRequest);	
        	boolean isSuccess = creditServiceFacade.takeCreditMultiLevel(orderMatchRequest, orderMatchRequest.getLegalEntity(), orderMatchRequest.getTradingParty().getLegalEntity(), false );
        	CreditPostTradeUtil.manageCreditWorkflowState(orderMatchRequest);			
        	if ( orderMatchRequest.isMultiFill() )
            {
                eventTradeMapperCache.track( orderMatchRequest );
            }
	       	if(isSuccess)
	       	{
	       		CreditServiceFacadeFactory.getInstance().persistCreditUtilizationEvents(orderMatchRequest);
	       		postTradeResponse.setSuccessful();
	       	}
	       	log.info("[OCX-Workflow] {OrderMatchSubmit} Completed -- MatchEvent " + orderMatchRequest);	       	    		
        }       	
    	else
        {
            log.info("[OCX-Workflow] {Default} Started -- MatchEvent " + matchEvent.get_id());
        	postTradeResponse.setSuccessful();
        }
        return postTradeResponse;
	}
	/**
	 * 
	 */
	public CreditPostTradeWorkflowResponse processTradeRequestExpiry(CreditPostTradeWorkflowRequest creditPostTradeRequest,CreditPostTradeWorkflowResponse postTradeResponse  )
	{
		SingleLegTrade singleLegTrade = creditPostTradeRequest.getSingleLegTrade();
		MatchEvent matchEvent = creditPostTradeRequest.isMatchEventCaptured()?creditPostTradeRequest.getMatchEvent():singleLegTrade.getMatchEvent();
		if(!eventTradeMapperCache.isRejection(matchEvent))
    	{
            Set<SingleLegTrade> legTrades = null;
            if(matchEvent.isMultiFill()){
                legTrades = eventTradeMapperCache.getTrades( matchEvent );
            }

	    	if( legTrades!=null && legTrades.size() >= 1)
	    	{
	        	if( eventTradeMapperCache.isTracking(matchEvent))
                {
	        		SingleLegTrade originalTrade = eventTradeMapperCache.originalTrade(matchEvent);
	        		WorkflowMessage msg = handleMultipleFills(matchEvent,originalTrade,legTrades);
	        		if( msg.getStatus()!=MessageStatus.SUCCESS)
	        		{
	               		postTradeResponse.setFailed("Failed to Process MultiFill for Match Event " + matchEvent.get_id());
	        		}
	        	}
	        	else
	        	{
	        		repopulateTradesForMatchEvent(matchEvent);
	        	}
	    	}
	    	else
	    	{
				// if num fill == 1 will have one trade, if 0 just skip
				SingleLegTrade originalTrade = eventTradeMapperCache.originalTrade(matchEvent);
				if( null== originalTrade)
				{
					StringBuilder logging = new StringBuilder("[OCX-Workflow] {TradeRequestExpiry}  { MatchEvent:");
					logging.append(matchEvent.get_id()).append(",MultiFill:").append(matchEvent.isMultiFill());
					logging.append(",NFills:0,Event:expired}");
					log.info(logging.toString());
					postTradeResponse.setSuccessful();
				}
				else
				{
					singleLegTrade = (singleLegTrade == null)?originalTrade:singleLegTrade;
					StringBuilder logging = new StringBuilder("[OCX-Workflow] {TradeRequestExpiry}  { MatchEvent:");
					logging.append(matchEvent.get_id()).append(",MultiFill:").append(matchEvent.isMultiFill());
					logging.append(",NFills:1}");
					log.info(logging.toString());
					CreditPostTradeUtil.populateCreditWorkflowState(singleLegTrade);
					WorkflowMessage message = creditServiceFacade.updateCreditAmountSingleLevel(singleLegTrade);

					if (message.getStatus() != MessageStatus.SUCCESS) {
						postTradeResponse.setFailed("Failed to Update Credit Post Trade at Trade Request Expiry - MatchEvent " + matchEvent);
					}
					CreditPostTradeUtil.clearCreditWorkflowState(singleLegTrade);
				}
	    	}
		}
    	eventTradeMapperCache.clear(matchEvent);
    	return postTradeResponse;
	}
	
	
	
	public CreditPostTradeWorkflowResponse processOrderMatchRequestExpiry(CreditPostTradeWorkflowRequest creditPostTradeRequest,CreditPostTradeWorkflowResponse postTradeResponse  )
	{		
		MatchEvent matchEvent = creditPostTradeRequest.getMatchEvent();
		if(!eventTradeMapperCache.isRejection(matchEvent) && !eventTradeMapperCache.isPostRateFilling(matchEvent))
    	{
	    	if( matchEvent.isMultiFill())
	    	{
	        	if( eventTradeMapperCache.isTracking(matchEvent))
	        	{
	        		Set<SingleLegTrade> legTrades = eventTradeMapperCache.getTrades(matchEvent);
	        		WorkflowMessage msg = handleMultipleFills((OrderMatchRequest)matchEvent,legTrades);	        		
	        		if( msg.getStatus()!=MessageStatus.SUCCESS)
	        		{
	               		postTradeResponse.setFailed("Failed to Process MultiFill for Match Event " + matchEvent.get_id());
	        		}
	        	}
	        	else
	        	{
	        		repopulateTradesForOrderMatchRequest(matchEvent);
	        	}
	    	}
		}
    	eventTradeMapperCache.clear(matchEvent);
    	return postTradeResponse;
	}
	/**
	 * 
	 * @param matchEvent
	 * @param originalTrade
	 * @param legTrades
	 * @return
	 */
	private WorkflowMessage handleMultipleFills(MatchEvent matchEvent,SingleLegTrade originalTrade,Set<SingleLegTrade> legTrades)
	{
		WorkflowMessage msg = null;
		if( null== originalTrade  || CollectionUtil.isEmptyOrNull(legTrades))
		{
			 msg = MessageFactory.newWorkflowMessage();
		     msg.setStatus(MessageStatus.FAILURE);
		     return msg;
		}
		StringBuilder logging = new StringBuilder("[OCX-Workflow] {TradeRequestExpiry}  { MatchEvent:");
		logging.append(matchEvent.get_id()).append(",MultiFill:").append(matchEvent.isMultiFill());
		logging.append(",NFills:").append(matchEvent.getNumberOfFills());
		logging.append(",FirstTrade:{id:").append(originalTrade.getFirstTradeId());
		logging.append(",HashCode:").append(originalTrade.hashCode()).append("},");
		CreditPostTradeUtil.populateCreditWorkflowState(originalTrade);
		for(SingleLegTrade singleLegTrade:legTrades)
		{
			if( singleLegTrade.get_id().equals(originalTrade.get_id()))
			{
				CreditPostTradeUtil.populateCreditWorkflowState(singleLegTrade);
				logging.append(",PopulatingWorkflowStates in FillTrade:").append(singleLegTrade.hashCode());
				break;
			}
		}
		logging.append("}");
		log.info(logging.toString());
		if(originalTrade.getOrderRequest()==null)
		{
			DealingModelTradeHelper.updateWithTransients(originalTrade);
		}
		msg = creditServiceFacade.updateMultiFillCreditSingleLegLevel((SingleLegOrderMatch)matchEvent, originalTrade, legTrades);
		CreditPostTradeUtil.clearCreditWorkflowState(originalTrade);
		return msg;
	}
	
	
	private WorkflowMessage handleMultipleFills(OrderMatchRequest originalMatchRequest,Set<SingleLegTrade> legTrades)
	{
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();;
		if( null== originalMatchRequest)
		{
		     msg.setStatus(MessageStatus.FAILURE);
		     return msg;
		}
		StringBuilder logging = new StringBuilder("[OCX-Workflow] {TradeRequestExpiry}  { MatchEvent:");
		logging.append(originalMatchRequest.get_id()).append(",MultiFill:").append(originalMatchRequest.isMultiFill());
		logging.append(",NFills:").append(originalMatchRequest.getNumberOfFills());
		//logging.append(",FirstTrade:{id:").append(originalTrade.getFirstTradeId());
		logging.append(",HashCode:").append(originalMatchRequest.hashCode());
		CreditPostTradeUtil.populateCreditWorkflowState(originalMatchRequest);
		for(SingleLegTrade singleLegTrade:legTrades)
		{
			if( singleLegTrade.get_id().equals(originalMatchRequest.get_id()))
			{
				CreditPostTradeUtil.populateCreditWorkflowState(singleLegTrade);
				logging.append(",PopulatingWorkflowStates in FillTrade:").append(singleLegTrade.hashCode());
				break;
			}
		}
		logging.append("}");
		log.info(logging.toString());

		creditServiceFacade.updateCreditOnTermination(originalMatchRequest, new ArrayList<SingleLegTrade>(legTrades), originalMatchRequest.getLegalEntity(), originalMatchRequest.getTradingParty().getLegalEntity(), true);
		msg.setStatus(MessageStatus.SUCCESS);
		CreditPostTradeUtil.clearCreditWorkflowState(originalMatchRequest);
		return msg;
	}


	private void repopulateTradesForMatchEvent(MatchEvent matchEvent)
	{
		try
		{
			Tuple<SingleLegTrade, Set<SingleLegTrade>> tuple = CreditPostTradeUtil.populateMatchEventTrades(matchEvent);
	 		handleMultipleFills(matchEvent,tuple.first,tuple.second);
		}
		catch(Exception e)
		{
            log.error("[OCX-Workflow]  RePoulate MultiFill Match Event : Failed to query Trades for Order Match  " + matchEvent.get_id(),e);
		}
	}
	
	
	private void repopulateTradesForOrderMatchRequest(MatchEvent matchEvent)
	{
		try
		{
			Tuple<SingleLegTrade, Set<SingleLegTrade>> tuple = CreditPostTradeUtil.populateMatchEventTrades(matchEvent);
	 		handleMultipleFills((OrderMatchRequest)matchEvent,tuple.second);
		}
		catch(Exception e)
		{
            log.error("[OCX-Workflow]  RePoulate MultiFill Match Event : Failed to query Trades for Order Match  " + matchEvent.get_id(),e);
		}
	}
	/**
	 * 
	 * <AUTHOR>
	 */
	private static class MatchEventTradeMapperCache extends NotificationCache<Tracker> 
	{
	    public MatchEventTradeMapperCache(int cacheSize,long expirationTimeInMS,int concurrencyLevel) 
	    {
	        super(cacheSize,expirationTimeInMS,concurrencyLevel);
	    }
				
		
		public boolean isTracking(MatchEvent event)
		{
			 return (null != get(event.get_id()));
		}
		/**
		 * 
		 * @param event
		 * @return
		 */
		Set<SingleLegTrade> getTrades(MatchEvent event)
		{
			Tracker tracker= get(event.get_id());
			if( null!= tracker) return tracker.get();
			return null;
		}
		/**
		 * 
		 * @param trade
		 */
		void track(SingleLegTrade  trade )
		{
			if(trade.isFirstTrade())
			{
				Tracker tracker= new Tracker(trade);
				log.info( "CPTP.track : Adding trade to cache. matchEvent.id=" + trade.getMatchEvent().get_id() + ",trade=" + trade.get_id() );
				add(trade.getMatchEvent().get_id(), tracker);
			}
		}
		
		void track(OrderMatchRequest  orderMatchRequest )
		{
				Tracker tracker= new Tracker(orderMatchRequest);
				log.info( "CPTP.track : Adding OrderMatchRequest to cache. matchEvent.id=" + orderMatchRequest.get_id());
				add(orderMatchRequest.get_id(), tracker);
		}
		/**
		 * 
		 * @param event event
		 */
		SingleLegTrade originalTrade(MatchEvent event)
		{
			Tracker tracker= get(event.get_id());
			SingleLegTrade legTrade = null;
			if( null!= tracker)
				legTrade  = tracker.getFirstTrade();
			return legTrade;
		}
		
		OrderMatchRequest originalMatchRequest(MatchEvent event)
		{
			Tracker tracker= get(event.get_id());
			OrderMatchRequest matchRequest = null;
			if( null!= tracker)
				matchRequest  = tracker.getFirstMatchRequest();
			return matchRequest;
		}
		/**
		 * 
		 * @param trade trade
		 */
		void trackFills(SingleLegTrade  trade )
		{
			MatchEvent event = trade.getMatchEvent();
			Tracker tracker = get(event.get_id());
			
			if( null == tracker)
			{
				// something is wrong
				// Should we log
				// trade request expiry will handle the failure
                log.warn( "CPTP.trackFills : No entry found in cache for matchEvent.id=" + event.get_id() + ",trade=" + trade.get_id() );
				return;
			}
            log.info( "CPTP.trackFills : Adding trade to cache. matchEvent.id=" + event.get_id() + ",trade=" + trade.get_id() );
			tracker.add(trade);
		}
		boolean isRejection(MatchEvent event)
		{
			Tracker tracker = get(event.get_id());
			if( null != tracker)
			{
				return tracker.isRejection();
			}
			return false;
		}
		
		void setRejection(MatchEvent event)
		{
			log.info( "CPTP.setRejection : set rejection flag for matchEvent.id=" + event.get_id() );
			Tracker tracker = get(event.get_id());
			if( null != tracker)
			{
				tracker.setRejected();
			}
		}
		
		void setPostRateFilling(MatchEvent event)
		{
			log.info( "CPTP.setPostRateFill : set postRateFill flag for matchEvent.id=" + event.get_id() );
			Tracker tracker = get(event.get_id());
			if( null == tracker)
			{
				tracker= new Tracker((OrderMatchRequest)event);
				log.info( "CPTP.track : Adding OrderMatchRequest to cache. matchEvent.id=" + event.get_id());
				add(event.get_id(), tracker);
			}	
			tracker.setPostRateFilling();
		}
		
		boolean isPostRateFilling(MatchEvent event)
		{
			Tracker tracker = get(event.get_id());
			if( null != tracker)
			{
				return tracker.isPostRateFilling();
			}
			return false;
		}
		/**
		 * 
		 * @param matchEvent
		 */

		/**
		 * 
		 * @param event
		 */
		void clear(MatchEvent event)
		{
			log.info( "CPTP.clear : removing cache entry for matchEvent.id=" + event.get_id() );
			Tracker tracker = get(event.get_id()); 
			invalidate(event.get_id());
			if( null!= tracker)
				tracker.clear();
		}


		@Override
		public Class getType() {
			return Tracker.class;
		}
	}
	
	static class Tracker
	{
		SingleLegTrade firstTrade = null;
		OrderMatchRequest firstMatchRequest = null;
		Set<SingleLegTrade> legTrades = null;
		String matchID = null;
		boolean rejected =false;
		boolean postRateFilling = false;
		
		Tracker(SingleLegTrade legTrade)
		{
			matchID = legTrade.getMatchEvent().get_id();
			firstTrade =legTrade; 
			legTrades = new HashSet<SingleLegTrade>();
		}
		
		Tracker(OrderMatchRequest orderMatchRequest)
		{
			matchID = orderMatchRequest.get_id();
			firstMatchRequest = orderMatchRequest; 
			legTrades = new HashSet<SingleLegTrade>();
		}
		
		SingleLegTrade getFirstTrade()
		{
			return firstTrade;
		}
		
		OrderMatchRequest getFirstMatchRequest()
		{
			return firstMatchRequest;
		}
	
		synchronized void add(SingleLegTrade legTrade)
		{
			legTrades.add(legTrade);
		}
		void setRejected()
		{
			rejected = true;
		}
		boolean isRejection()
		{
			return rejected;
		}
		
		void setPostRateFilling()
		{
			postRateFilling = true;
		}
		
		boolean isPostRateFilling()
		{
			return postRateFilling;
		}
		
		Set<SingleLegTrade> get()
		{
			return Collections.unmodifiableSet(legTrades);
		}
		
		void clear()
		{
			firstTrade = null;
			firstMatchRequest = null;
			if( null!= legTrades ) legTrades.clear();
			legTrades = null;
		}
	}

	/**
	 * Only to be used in devapp utilities.
	 * @return event trade map size
	 */
	public static long getEventTradeMapperCacheSize()
	{
		return eventTradeMapperCache.size ();
	}
	
}
