package com.integral.notifications.credit;

import java.util.ArrayList;
import java.util.List;

public class CreditPostTradeWorkflowResponse 
{

	private boolean success = false;
	private List<String> errors = null;
	public CreditPostTradeWorkflowResponse() 
	{
		
	}

	public void setSuccessful() 
	{
		success=true;
	}
	
	public boolean isSuccessful() 
	{
		return success;
	}
	
	public void setFailed(String errorMessage)
	{
		if(null==errors)errors= new ArrayList<String>();
		errors.add(errorMessage);
		success = false;
	}
}
