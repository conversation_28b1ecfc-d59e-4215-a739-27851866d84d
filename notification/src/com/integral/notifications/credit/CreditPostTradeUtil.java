package com.integral.notifications.credit;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.integral.is.spaces.fx.esp.service.TradeServiceC;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.is.spaces.fx.persistence.PersistenceServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.DealingModelRef;
import com.integral.model.dealing.MatchEvent;
import com.integral.model.dealing.OrderMatchRequest;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.SingleLegOrderMatch;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.model.dealing.credit.CreditLimitWorkflowState;
import com.integral.notifications.cache.NotificationCache;
import com.integral.persistence.spaces.PersistenceConstants;
import com.integral.query.spaces.SpacesQueryService.QueryResult;
import com.integral.query.spaces.fx.esp.query.SingleLegTradeQueryService;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.spaces.SpaceIterator;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.user.User;
import com.integral.util.IdcUtilC;
import com.integral.util.Tuple;

public class CreditPostTradeUtil {

    private static Log log = LogFactory.getLog( CreditPostTradeUtil.class );

    private static TradeCreditWorkflowStateCache  creditWorkflowStateCache= new TradeCreditWorkflowStateCache(
    		RuntimeFactory.getServerRuntimeMBean().getPostCreditProcessorCacheSize(),
    		RuntimeFactory.getServerRuntimeMBean().getPostCreditProcessorCacheExpTimeMills(),
    		RuntimeFactory.getServerRuntimeMBean().getPostCreditProcessorCacheConcurrency());
    
    private static class TradeCreditWorkflowStateCache extends NotificationCache<List<CreditLimitWorkflowState>>
    {
    	public TradeCreditWorkflowStateCache(int cacheSize,long expirationTimeInMS,int concurrencyLevel) 
 	    {
 	        super(cacheSize,expirationTimeInMS,concurrencyLevel);
 	    }

		@Override
		public Class getType() {
			return List.class;
		}
    }
    
	private CreditPostTradeUtil() 
	{
		
	}
	/**
	 * 
	 * @param singleLegTrade
	 * @return
	 */
	static void clearCreditWorkflowState(SingleLegTrade singleLegTrade)
	{
		log.info("[OCX-Workflow] Clearing Credit Workflow State for Trade  " + singleLegTrade.get_id());
		creditWorkflowStateCache.invalidate(singleLegTrade.get_id());
	}
	
	static void clearCreditWorkflowState(OrderMatchRequest orderMatchRequest)
	{
		log.info("[OCX-Workflow] Clearing Credit Workflow State for MatchRequest  " + orderMatchRequest.get_id());
		creditWorkflowStateCache.invalidate(orderMatchRequest.get_id());
	}
	/**
	 * 
	 * @param singleLegTrade
	 * @return
	 */
	static void manageCreditWorkflowState(SingleLegTrade singleLegTrade)
	{
		log.info("[OCX-Workflow] Caching Credit Workflow State for Trade  " + singleLegTrade.get_id());
		creditWorkflowStateCache.add(singleLegTrade.get_id(),singleLegTrade.getCreditWorkflowStates());
	}
	
	static void manageCreditWorkflowState(OrderMatchRequest orderMatchRequest)
	{
		log.info("[OCX-Workflow] Caching Credit Workflow State for MatchRequest  " + orderMatchRequest.get_id());
		creditWorkflowStateCache.add(orderMatchRequest.get_id(), orderMatchRequest.getCreditWorkflowStates());
	}
	
	/**
	 * 
	 * @param singleLegTrade
	 * @return
	 */
	static boolean populateCreditWorkflowState(SingleLegTrade singleLegTrade)
	{

		if(singleLegTrade.getCreditWorkflowStates() == null || singleLegTrade.getCreditWorkflowStates().size() == 0 )
		{
			log.info("[OCX-Workflow] Updating Trade "  + singleLegTrade.get_id() + " with  Credit Workflow State from internal cache ");
			List<CreditLimitWorkflowState> creditLimitWorkflowStates = creditWorkflowStateCache.get(singleLegTrade.get_id());
			if(creditLimitWorkflowStates == null || creditLimitWorkflowStates.size() == 0 )
			{	
				TradeServiceC.queryCreditUtilizationEvents(singleLegTrade)	;
				if(singleLegTrade.getCreditWorkflowStates() == null || singleLegTrade.getCreditWorkflowStates().size() == 0 )
				{
	    			TradeServiceC.queryCreditUtilizationEvents(singleLegTrade,true)	;
	    			if(singleLegTrade.getCreditWorkflowStates() == null || singleLegTrade.getCreditWorkflowStates().size() == 0 )
	    			{
	    				return false;
	    			}
				}
			}
			else
			{
				singleLegTrade.setCreditWorkflowStates(creditLimitWorkflowStates);
			}
			return true;
		}
		
		return true;
	}
	
	static boolean populateCreditWorkflowState(OrderMatchRequest orderMatchRequest)
	{

		if(orderMatchRequest.getCreditWorkflowStates() == null || orderMatchRequest.getCreditWorkflowStates().size() == 0 )
		{
			log.info("[OCX-Workflow] Updating MatchRequest "  + orderMatchRequest.get_id() + " with  Credit Workflow State from internal cache ");
			List<CreditLimitWorkflowState> creditLimitWorkflowStates = creditWorkflowStateCache.get(orderMatchRequest.get_id());
			if(creditLimitWorkflowStates == null || creditLimitWorkflowStates.size() == 0 )
			{	
				TradeServiceC.queryCreditUtilizationEvents(orderMatchRequest, false)	;
				if(orderMatchRequest.getCreditWorkflowStates() == null || orderMatchRequest.getCreditWorkflowStates().size() == 0 )
				{
	    			TradeServiceC.queryCreditUtilizationEvents(orderMatchRequest,true)	;
	    			if(orderMatchRequest.getCreditWorkflowStates() == null || orderMatchRequest.getCreditWorkflowStates().size() == 0 )
	    			{
	    				return false;
	    			}
				}
			}
			else
			{
				orderMatchRequest.setCreditWorkflowStates(creditLimitWorkflowStates);
			}
			return true;
		}
		
		return true;
	}
	/**
	 * 
	 * @param matchEvent
	 */
	static Tuple<SingleLegTrade, Set<SingleLegTrade>> populateMatchEventTrades(MatchEvent matchEvent)
	{
		Tuple<SingleLegTrade, Set<SingleLegTrade>> tuple = new Tuple<SingleLegTrade, Set<SingleLegTrade>>(); 

		try
		{
			// go after primary , need the data
			 QueryResult<SpaceIterator<SingleLegTrade>> queryResult = SingleLegTradeQueryService.getTradesIteratorForOrderMatch(matchEvent.getNamespace().getShortName(), matchEvent.get_id(),true);
			 SingleLegTrade originalTrade = null;
			 Set<SingleLegTrade> trades = new HashSet<SingleLegTrade>();
	         switch (queryResult.getStatus()) 
	         {
	             case FAILURE:
	                 log.error("[OCX-Workflow]  RePoulate MultiFill Match Event : Failed to query Trades for Order Match  " + matchEvent.get_id());
	                 //place it in Retry Queue
	             case SUCCESS:
	                 SpaceIterator<SingleLegTrade> tradeIterator = queryResult.getResult();
	                 if (tradeIterator != null) 
	                 {
	                     while (tradeIterator.hasNext()) 
	                     {
	                         SingleLegTrade trade = tradeIterator.next();
	                         ((SingleLegOrderMatch)matchEvent).getSingleLegTrades().add(trade);

                             if(trade.getMatchEvent()==null)
                             {
                                 log.info("SingleLegTrade from spaces did not have matchEvent. So setting it explicitly for matchId="+matchEvent.get_id());
                                 DealingModelRef<SingleLegOrderMatch> matchEventRef = trade.getMatchEventRef();
                                 if(matchEventRef==null){
                                     matchEventRef = new DealingModelRef<SingleLegOrderMatch>();
                                     trade.setMatchEventRef( matchEventRef );
                                 }
                                 DealingModelUtil.populateDealingModelRef( matchEventRef, matchEvent );
                             }

	                         trades.add( trade );
	                         if( trade.isFirstTrade())
	                         {
	                        	 originalTrade = trade;
	                         }
	                     }
	                 }
	         }
	         
	         tuple.first = originalTrade;
	         tuple.second = trades;
	         return tuple;
		}
		catch(Exception e)
		{
            log.error("[OCX-Workflow]  RePoulate MultiFill Match Event : Failed to query Trades for Order Match  " + matchEvent.get_id(),e);
		}
		return tuple;
	}
	/**
	 * 
	 * @param event
	 * @return
	 */
	static User getUser(MatchEvent event)
	{
		User user = null;
		if( null == event.getOrderRequestRef().getObject())
		{
		     SingleLegOrder orderRequest = getCachedOrderRequest(PersistenceServiceFactory.getTradingMetaspaceName(),
	                    PersistenceConstants.ORDERREQUEST, event.getOrderRequestRef());
		     if( null!=orderRequest) user = orderRequest.getUser();
		} else {
			user = event.getOrderRequestRef().getObject().getUser();
		}
		return user;
		
	}
	/**
	 * 
	 * @param metaspace
	 * @param space
	 * @param dmr
	 * @return
	 */
    private static SingleLegOrder getCachedOrderRequest(String metaspace, String space, DealingModelRef dmr) 
    {
        Object obj = getCachedObject(metaspace, space, dmr, SingleLegOrder.class);
        return obj != null ? (SingleLegOrder ) obj : null;
    }
    /**
     * 
     * @param metaspace
     * @param space
     * @param dmr
     * @param cls
     * @return
     */
    private static Object getCachedObject(String metaspace, String space, DealingModelRef dmr, Class cls) 
    {
        if (dmr == null || dmr.getUid() == null || dmr.getNamespace() == null) {
            return null;
        }
        return getCachedObject( metaspace, space, dmr.getNamespace().getShortName(), dmr.getUid(), cls);
    }
    /**
     * 
     * @param metaspace
     * @param space
     * @param namespace
     * @param id
     * @param cls
     * @return
     */
    private static Object getCachedObject( String metaspace, String space, String namespace, String id, Class cls ) 
    {
        com.integral.is.spaces.fx.persistence.DealingModelSpacesCacheManager dmcm = com.integral.is.spaces.fx.persistence.DealingModelSpacesCacheManager.getInstance();
        return dmcm.getCachedObject(metaspace, namespace, space, id, cls);
    }
	/**
	 * 
	 * @param user
	 * @return
	 */
    public static IdcSessionContext switchSessionContext(User user)
	{
		IdcSessionContext oldContext= IdcSessionManager.getInstance().getSessionContext();
		IdcUtilC.setSessionContextUser( user );
		return oldContext;
	}

	/**
	 * Only to be used in devapp utilities.
	 * @return credit workflow state cache size.
	 */
	public static long getCreditWorkflowStateCacheSize ()
	{
		return creditWorkflowStateCache.size ();
	}
}
