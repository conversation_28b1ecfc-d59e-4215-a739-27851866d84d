/*
 * *
 *  *
 *  *  Copyright (c) 2001-2015 Integral Development Corp.  All rights reserved.
 *
 */

package com.integral.notifications.credit;



public interface CreditMessageProcessor
{
	/**
	 * 
	 * @param creditPostTradeRequest
	 * @param postTradeResponse
	 * @return
	 */
	CreditPostTradeWorkflowResponse process(CreditPostTradeWorkflowRequest creditPostTradeRequest,CreditPostTradeWorkflowResponse postTradeResponse  );
	/**
	 * 
	 * @param creditPostTradeRequest
	 * @param postTradeResponse
	 * @return
	 */
	CreditPostTradeWorkflowResponse processTradeRequestExpiry(CreditPostTradeWorkflowRequest creditPostTradeRequest,CreditPostTradeWorkflowResponse postTradeResponse  );
	
	CreditPostTradeWorkflowResponse processOrderMatchRequestExpiry(CreditPostTradeWorkflowRequest creditPostTradeRequest,CreditPostTradeWorkflowResponse postTradeResponse  );

}
