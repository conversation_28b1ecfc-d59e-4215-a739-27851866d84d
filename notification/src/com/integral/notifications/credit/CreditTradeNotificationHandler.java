/*
 * *
 *  *
 *  *  Copyright (c) 2001-2015 Integral Development Corp.  All rights reserved.
 *
 */

package com.integral.notifications.credit;

import java.util.ArrayList;
import java.util.Collection;

import com.integral.is.common.LiveMarketFeedAutoSubscriptionStartupC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.MatchEvent;
import com.integral.model.dealing.OrderMatchRequest;
import com.integral.model.dealing.SingleLegTrade;
import com.integral.notifications.NotificationConfiguration;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.spaces.notification.NHResponse;
import com.integral.spaces.notification.NHResponseCode;
import com.integral.spaces.notification.Notification;
import com.integral.spaces.notification.NotificationHandler;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.user.User;


final public class CreditTradeNotificationHandler implements NotificationHandler {

    private static Log log = LogFactory.getLog(CreditTradeNotificationHandler.class);
    private Collection<CreditMessageProcessor> creditMessageProcessors = new ArrayList<CreditMessageProcessor>();
    
    public CreditTradeNotificationHandler()
    {
    	init();
    }

    private void init()
    {
    	creditMessageProcessors.add(new CreditPostTradeProcessor());

        // do the live mds subscription and credit relations warm-up here.
        new LiveMarketFeedAutoSubscriptionStartupC().startup( "LiveMarketFeedAutoSubscriptionStartupC", null );
        //new NSCreditLimitStartupC().startup( "NSCreditLimitStartupC", null );
    }

    public NHResponse handle(Notification notification)
    {
        Object notificationCompleteObj = notification.getCompleteObject();
        
        if(log.isDebugEnabled())
        {
        	log.debug("[OCX-Workflow] Credit Post Trade Notification Processing , Event  AppCode : " + notification.getAppEventCode() + ", Notification Object : " + notificationCompleteObj.toString() );
        }

        if (SingleLegTrade.class.isAssignableFrom(notification.getDataObjectType()))
        {
            SingleLegTrade singleLegTrade = (SingleLegTrade) notificationCompleteObj;
            try
            {
                if ( singleLegTrade.isPostTradeCreditCheckEnabled() )
                {
                    User user  = singleLegTrade.getUser();
                    IdcSessionContext oldContext = CreditPostTradeUtil.switchSessionContext(user);
                    try
                    {
                        CreditPostTradeWorkflowRequest creditPostTradeRequest = new CreditPostTradeWorkflowRequest( singleLegTrade, notification.getAppEventCode() );
                        sleepIfDelayConfigured(notification, notificationCompleteObj);
                        CreditPostTradeWorkflowResponse postTradeResponse = new CreditPostTradeWorkflowResponse();

                        for ( CreditMessageProcessor processors : creditMessageProcessors )
                        {
                            postTradeResponse = processors.process( creditPostTradeRequest, postTradeResponse );
                            if ( !postTradeResponse.isSuccessful() )
                            {
                                return new NHResponse( NHResponseCode.FAILURE );
                            }
                        }
                    }
                    finally
                    {
                        IdcSessionManager.getInstance().setSessionContext(oldContext);
                    }
	        	}
            }
            catch(Exception e)
            {
            	log.error("[OCX-Workflow] Credit Post Trade Notification Processing Failed to Process event, Event  AppCode : " + notification.getAppEventCode() + ", Trade : " + singleLegTrade.toString(), e );
            }
            return new NHResponse(NHResponseCode.SUCCESS);

        }
        else
    	if (OrderMatchRequest.class.isAssignableFrom(notification.getDataObjectType()))
        {
    		OrderMatchRequest orderMatchRequest = (OrderMatchRequest) notificationCompleteObj;    		
            try
            {
            	if(orderMatchRequest.isPostTradeCreditCheckEnabled() && (orderMatchRequest.getTradingParty()!=null))
            	{
	                User user  = orderMatchRequest.getUser();
	                IdcSessionContext oldContext = CreditPostTradeUtil.switchSessionContext(user);
	                try
	                {
	                    CreditPostTradeWorkflowRequest creditPostTradeRequest = new CreditPostTradeWorkflowRequest( orderMatchRequest, notification.getAppEventCode() );
	                    CreditPostTradeWorkflowResponse postTradeResponse = new CreditPostTradeWorkflowResponse();
	                    
	                    if ( creditPostTradeRequest.isOrderMatchSubmit())
	                    {
	                        sleepIfDelayConfigured(notification, notificationCompleteObj);                        
	                        
	                        for ( CreditMessageProcessor processors : creditMessageProcessors )
	                        {
	                            postTradeResponse = processors.process( creditPostTradeRequest, postTradeResponse );
	                            if ( !postTradeResponse.isSuccessful() )
	                            {
	                                return new NHResponse( NHResponseCode.FAILURE );
	                            }
	                        }
	                    } 
	                    else if (creditPostTradeRequest.isOrderMatchRequestExpiry())
	                    {
	                        for ( CreditMessageProcessor processors : creditMessageProcessors )
	                        {                        
	                            postTradeResponse = processors.processOrderMatchRequestExpiry( creditPostTradeRequest, postTradeResponse );
	                            if ( !postTradeResponse.isSuccessful() )
	                            {
	                                return new NHResponse( NHResponseCode.FAILURE );
	                            }
	                        }
	                    }
	                }
	                finally
	                {
	                    IdcSessionManager.getInstance().setSessionContext(oldContext);
	                }
            	}
            }
            catch(Exception e)
            {
            	log.error("[OCX-Workflow] Credit Post Trade Notification Processing Failed to Process event, Event  AppCode : " + notification.getAppEventCode() + ", OrderMatchRequest : " + orderMatchRequest.toString(), e );
            }
            return new NHResponse(NHResponseCode.SUCCESS);
        }
        else
    	if (MatchEvent.class.isAssignableFrom(notification.getDataObjectType()))
        {
    		MatchEvent matchEvent = (MatchEvent) notificationCompleteObj;
            if(matchEvent.isOCX())
            {
                CreditPostTradeWorkflowRequest creditPostTradeRequest = new CreditPostTradeWorkflowRequest(matchEvent,notification.getAppEventCode());
                if ( creditPostTradeRequest.isTradeRequestExpiry())
                {
                    User user = CreditPostTradeUtil.getUser( matchEvent );
                    if(user == null){
                        log.error("Credit Notification Handler: user null for " + matchEvent.getOrderRequestId() + ' ' + matchEvent.get_id() + ' ' +
                                notification.getAppEventCode());
                    }
                    IdcSessionContext oldContext = CreditPostTradeUtil.switchSessionContext( user );

                    log.info( "[OCX-Workflow] Credit Post Trade Notification Processing Match Event for Trade/OrderMatch Request Expiry -- " + matchEvent );
                    try
                    {
                        CreditPostTradeWorkflowResponse postTradeResponse = new CreditPostTradeWorkflowResponse();
                        for ( CreditMessageProcessor processors : creditMessageProcessors )
                        {
                        	
                            postTradeResponse = processors.processTradeRequestExpiry( creditPostTradeRequest, postTradeResponse );
                            if ( !postTradeResponse.isSuccessful() )
                            {
                                return new NHResponse( NHResponseCode.FAILURE );
                            }
                        }
                    }
                    finally
                    {
                        IdcSessionManager.getInstance().setSessionContext( oldContext );
                    }
                }
        	}
        }
        return new NHResponse(NHResponseCode.SUCCESS);
    }

	private void sleepIfDelayConfigured(Notification notification, Object notificationCompleteObj) 
	{
		long delay = RuntimeFactory.getServerRuntimeMBean().getNSCreditProcessingDelay();
		if ( delay > 0 )
		{
		    try
		    {
		        log.info( "CTNH.handle : Sleeping for " + delay + " milliseconds. appEventCode=" + notification.getAppEventCode() + ",notificationObject=" + notificationCompleteObj.toString() );
		        Thread.sleep( delay );
		        log.info( "CTNH.handle : Finished sleeping for " + delay + " milliseconds. appEventCode=" + notification.getAppEventCode() + ",notificationObject=" + notificationCompleteObj.toString() );
		    }
		    catch ( InterruptedException ie )
		    {
		        log.error( "CTNH.handle : exception.", ie );
		    }
		}
	}

    public NHResponse handleRedelivery(Notification notification,String errorDesc) {
        return handle(notification);
    }


    public long getLongIdentifier() {
        return NotificationConfiguration.NOTIFICATION_HANDLER_CREDIT_POSTTRD.getLongIdentifier();
    }

}
