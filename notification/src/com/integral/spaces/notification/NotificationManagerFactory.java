package com.integral.spaces.notification;

import java.util.HashMap;
import java.util.Map;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.notifications.NotificationRegistrationStartup;
import com.integral.spaces.notification.NotificationConfiguration;
import com.integral.spaces.notification.NotificationManager;
import com.integral.commons.CompositeKeys;

public class NotificationManagerFactory {

	private static HashMap<CompositeKeys, NotificationManager> notificationManagerMap = new HashMap<CompositeKeys, NotificationManager>();
	private static final Log log = LogFactory
			.getLog(NotificationManagerFactory.class);

	private static volatile boolean isShutdown = false;

	public static synchronized NotificationManager getNotificationManager(
			String queueSetNameBase, String listenerHandlerGroup) {
		CompositeKeys key = getKey(queueSetNameBase, listenerHandlerGroup);
		return notificationManagerMap.get(key);
	}

	public static synchronized NotificationManager getOrCreateNotificationManager(
			String queueSetNameBase, String listenerHandlerGroup,
			String handlerGroup, String routingKeyPrefix) throws Exception {

		CompositeKeys key = getKey(queueSetNameBase, listenerHandlerGroup);
		NotificationManager notificationManager = notificationManagerMap
				.get(key);

		if (notificationManager == null) {
			log.info("Creating a new Notification Manager for queueSetNameBase:"
					+ queueSetNameBase
					+ ":listenerHandlerGroup:"
					+ listenerHandlerGroup
					+ ":handlerGroup:"
					+ listenerHandlerGroup
					+ ":routingKeyPrefix:"
					+ routingKeyPrefix);

			notificationManager = new NotificationManager();

			// init with the queueSetNameBase and HandlergroupName
			NotificationConfiguration newConfig = NotificationConfiguration
					.getInstance().copy();
			newConfig.setQueueSetNameBase(queueSetNameBase);
			newConfig.setListenerHandlerGroups(listenerHandlerGroup);
			newConfig.setHandlerGroups(handlerGroup);
			newConfig.setRoutingKeyPrefix(routingKeyPrefix);
			notificationManager.start(newConfig);
			addNotificationManager(queueSetNameBase, listenerHandlerGroup,
					notificationManager);
		}
		return notificationManager;
	}

	private static synchronized void addNotificationManager(
			String queueSetNameBase, String listenerHandlerGroup,
			NotificationManager notificationManager) {
		log.info("Adding NotificationManager:" + notificationManager + " for:"
				+ queueSetNameBase + ":Listener Handler Group:"
				+ listenerHandlerGroup);
		CompositeKeys key = getKey(queueSetNameBase, listenerHandlerGroup);
		if (notificationManagerMap.put(key, notificationManager) != null) {
			log.error("Error replacing existing Notification Manager for:"
					+ queueSetNameBase + ":Listener Handler Group:"
					+ listenerHandlerGroup);
		}
	}
	
	public static synchronized void shutdownAllNotificationManagers() {
		isShutdown = true;
		for (Map.Entry<CompositeKeys, NotificationManager> itr : notificationManagerMap
				.entrySet()) {
			log.info("Shutting down Notification Manager for:" + itr.getKey());
			try {
				itr.getValue().shutdown();
			} catch (Exception e) {
				log.error("Error in shutting down Notification Manager for:"
						+ itr.getKey(), e);
			}
		}
		notificationManagerMap.clear();
	}
	
	public static synchronized void shutdownNotificationManager(
			String queuePrefix, String listenerHandlerGroup) throws Exception {
		CompositeKeys key = getKey(queuePrefix, listenerHandlerGroup);
		NotificationManager manager = notificationManagerMap.get(key);
		if (manager != null) {
			manager.shutdown();
			notificationManagerMap.remove(key);
		} else {
			log.info("Notification Manager already shutdown for:" + key);
		}
	}

	public static boolean isShutdown() {
		return isShutdown;
	}

	public static synchronized void startNotificationManager(
			String queuePrefix, String listenerHandlerGroup,
			String handlerGroup, String routingKeyPrefix) throws Exception {

		if (isShutdown) {
			log.warn("NotificationManager Factory is already shutdown;Not starting notification manager for:queuePrefix:"
					+ queuePrefix
					+ ":listener:"
					+ listenerHandlerGroup
					+ ":handlers:"
					+ handlerGroup
					+ ":routingKeyPrefix:"
					+ routingKeyPrefix);			
		}
		NotificationManager notificationManager = NotificationRegistrationStartup
				.configureNotifications(queuePrefix, listenerHandlerGroup,
						handlerGroup, routingKeyPrefix);
		
		notificationManager.newNotifierThreadPool();
		notificationManager.getNotificationTransport().setUpReceiver();
		notificationManager.getNotificationTransport()
				.setupReceiversOnAllQueueSets();		
	}

	private static CompositeKeys getKey(String queueSetNameBase,
			String listenerHandlerGroup) {
		return CompositeKeys.getCompositeKeys(queueSetNameBase,
				listenerHandlerGroup);
	}
}
