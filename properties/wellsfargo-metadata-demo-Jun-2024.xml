<EntitiesDescriptor xmlns="urn:oasis:names:tc:SAML:2.0:metadata" ID="SM117f09aa2ea4a51d7c0de8f0d3b146b0df8dc08f8102">
    <ds:Signature xmlns:ds="http://www.w3.org/2000/09/xmldsig#">
<ds:SignedInfo>
<ds:CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
<ds:SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"/>
<ds:Reference URI="#SM117f09aa2ea4a51d7c0de8f0d3b146b0df8dc08f8102">
<ds:Transforms>
<ds:Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/>
<ds:Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
</ds:Transforms>
<ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
<ds:DigestValue>CzYaKnxe44565Yng/Ax6d29rebuHQoSmB5jBFzTtRZs=</ds:DigestValue>
</ds:Reference>
</ds:SignedInfo>
<ds:SignatureValue>
hT44u9d2I3G2y/P+iD9IVLjziSTA3/yDpBpYVYh60jwka4zNt/5uG46uEHQERoltvONjgBiXBWvP
MLW6rFit43ACmHfbM/R0QtUfGjodcoczR8vEHKvfZNgrqualM4ll4/GKYxtNFwnMwAOZ9s9lx0fD
vXc/IXEpLceepnKUEObEwMD5KvS4BqluHMPvsG5+2XW1jgqIEJr5SR/pp2TnWlPN7cYr4RHzxFzo
Na83LFi5vjtjsAZNQUqRwO+drhQ43vtkFBvrasIxQFm79QxdVQJ7OK/phBuijyB4tSTt7Lfas0fF
jwU4UrMr3dgd9ZRmloZvqcF5MUmYIGx1Du6cDw==
</ds:SignatureValue>
<ds:KeyInfo>
<ds:X509Data>
<ds:X509Certificate>
MIIFljCCBH6gAwIBAgIKb1Fj+QAAAAQcdjANBgkqhkiG9w0BAQsFADCBkjELMAkGA1UEBhMCVVMx
FDASBgNVBAoTC1dlbGxzIEZhcmdvMS4wLAYDVQQLEyVXZWxscyBGYXJnbyBDZXJ0aWZpY2F0aW9u
IEF1dGhvcml0aWVzMT0wOwYDVQQDEzRXZWxscyBGYXJnbyBFbnRlcnByaXNlIENlcnRpZmljYXRp
b24gQXV0aG9yaXR5IDA1IEcyMB4XDTE2MDMxMDE3NDUyOFoXDTE4MDMxMDE3NDUyOFowVzELMAkG
A1UEBhMCVVMxFDASBgNVBAoTC1dlbGxzIEZhcmdvMQ0wCwYDVQQLEwRJQlNTMSMwIQYDVQQDExpX
QVNTQU1MVGVzdC53ZWxsc2ZhcmdvLmNvbTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEB
AJXDl2Cr2RowULxpvsnJJ0PbrXX+yUDfWrDzrP7O+9GRRraEQvUBe2lqTIJM52WGfGH76rCjqKK4
NlpIzOFnQxU/15zbDqxe0JVzYnP6k4NKKJgo8Oja29G/a3c3U7uV7X/ArvJKsNxOhxA0889CapNk
I194QxKz76ngMhbEkqfNlCFNuitWXDffON2NjeYbOadxe9BVhbcc7054KrMjLU2fiNcaHeyfOlb6
15KTt3eirrNzEpdrEvbATlT7AwNk0rGJRC+KxlEagqukQO2eV1y6RVPayldDWyT0y5OVuXXBcUiY
ECRiD/pJrPtxMhGUcgryUIaQLNzarhwnA/9Zba8CAwEAAaOCAiYwggIiMB0GA1UdDgQWBBQ6xPRE
Mbqx77AguwLM1iORBh6olDAfBgNVHSMEGDAWgBRNp+hRgn0y1bGa181BOL87nDjvxTA6BgNVHR8E
MzAxMC+gLaArhilodHRwOi8vY3JsLnBraS53ZWxsc2ZhcmdvLmNvbS9lbnQwNUcyLmNybDB4Bggr
BgEFBQcBAQRsMGowOgYIKwYBBQUHMAKGLmh0dHA6Ly9jcmwucGtpLndlbGxzZmFyZ28uY29tL3dm
X2VudF8wNV9HMi5jcnQwLAYIKwYBBQUHMAGGIGh0dHA6Ly92YWxpZGF0b3Iud2VsbHNmYXJnby5j
b20vMAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgSwMD0GCSsGAQQBgjcVBwQwMC4GJisGAQQB
gjcVCIb7yg+H0/NEgcmTNIf2oguD/+9sgWOFtw2CqvUWAgFkAgEJMB0GA1UdJQQWMBQGCCsGAQUF
BwMCBggrBgEFBQcDATCBhgYDVR0gBH8wfTBBBgtghkgBhvt7g3QAATAyMDAGCCsGAQUFBwIBFiRo
dHRwOi8vd3d3LndlbGxzZmFyZ28uY29tL3JlcG9zaXRvcnkwOAYKYIZIAYb7e4N0BjAqMCgGCCsG
AQUFBwIBFhxodHRwOi8vd3d3LndlbGxzZmFyZ28uY29tL2NwMCUGA1UdEQQeMByCGldBU1NBTUxU
ZXN0LndlbGxzZmFyZ28uY29tMA0GCSqGSIb3DQEBCwUAA4IBAQCYOYIaZ945N6eygIzQBFmrtYGU
z0/UhKsmjDltoqSUNozNuuBcFz6fbiuHbmzkCvGevgZtHLDA02V3DHjE7lESCQnNjTR9PJsFwHo7
pZnblXl+CnbnCKi8+DV4mx91jE4nBbuRDvQlvquvbvGeiqumnp7wRVUoL9pJVIlQ+zRGPMcegIYi
0t/75JBse2gvx82VHKlehlgLNMNSdIstXDOswxxcrYV5RkzMnV/ss/blmzCr43KOxokqEGyaczBF
WnzL79DxkLNMJPP6KOYO3MP685kgKLGgRXdrP2H6MEPoaEafJYgD8a0B2l9AfGzLdTqIsXo3JxHP
kVJmSG0qSBO+
</ds:X509Certificate>
</ds:X509Data>
</ds:KeyInfo>
<ds:KeyInfo>
<ds:X509Data>
<ds:X509Certificate>
MIIEvjCCA6agAwIBAgIQBtjZBNVYQ0b2ii+nVCJ+xDANBgkqhkiG9w0BAQsFADBh
MQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3
d3cuZGlnaWNlcnQuY29tMSAwHgYDVQQDExdEaWdpQ2VydCBHbG9iYWwgUm9vdCBD
QTAeFw0yMTA0MTQwMDAwMDBaFw0zMTA0MTMyMzU5NTlaME8xCzAJBgNVBAYTAlVT
MRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxKTAnBgNVBAMTIERpZ2lDZXJ0IFRMUyBS
U0EgU0hBMjU2IDIwMjAgQ0ExMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKC
AQEAwUuzZUdwvN1PWNvsnO3DZuUfMRNUrUpmRh8sCuxkB+Uu3Ny5CiDt3+PE0J6a
qXodgojlEVbbHp9YwlHnLDQNLtKS4VbL8Xlfs7uHyiUDe5pSQWYQYE9XE0nw6Ddn
g9/n00tnTCJRpt8OmRDtV1F0JuJ9x8piLhMbfyOIJVNvwTRYAIuE//i+p1hJInuW
raKImxW8oHzf6VGo1bDtN+I2tIJLYrVJmuzHZ9bjPvXj1hJeRPG/cUJ9WIQDgLGB
Afr5yjK7tI4nhyfFK3TUqNaX3sNk+crOU6JWvHgXjkkDKa77SU+kFbnO8lwZV21r
eacroicgE7XQPUDTITAHk+qZ9QIDAQABo4IBgjCCAX4wEgYDVR0TAQH/BAgwBgEB
/wIBADAdBgNVHQ4EFgQUt2ui6qiqhIx56rTaD5iyxZV2ufQwHwYDVR0jBBgwFoAU
A95QNVbRTLtm8KPiGxvDl7I90VUwDgYDVR0PAQH/BAQDAgGGMB0GA1UdJQQWMBQG
CCsGAQUFBwMBBggrBgEFBQcDAjB2BggrBgEFBQcBAQRqMGgwJAYIKwYBBQUHMAGG
GGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBABggrBgEFBQcwAoY0aHR0cDovL2Nh
Y2VydHMuZGlnaWNlcnQuY29tL0RpZ2lDZXJ0R2xvYmFsUm9vdENBLmNydDBCBgNV
HR8EOzA5MDegNaAzhjFodHRwOi8vY3JsMy5kaWdpY2VydC5jb20vRGlnaUNlcnRH
bG9iYWxSb290Q0EuY3JsMD0GA1UdIAQ2MDQwCwYJYIZIAYb9bAIBMAcGBWeBDAEB
MAgGBmeBDAECATAIBgZngQwBAgIwCAYGZ4EMAQIDMA0GCSqGSIb3DQEBCwUAA4IB
AQCAMs5eC91uWg0Kr+HWhMvAjvqFcO3aXbMM9yt1QP6FCvrzMXi3cEsaiVi6gL3z
ax3pfs8LulicWdSQ0/1s/dCYbbdxglvPbQtaCdB73sRD2Cqk3p5BJl+7j5nL3a7h
qG+fh/50tx8bIKuxT8b1Z11dmzzp/2n3YWzW2fP9NsarA4h20ksudYbj/NhVfSbC
EXffPgK2fPOre3qGNm+499iTcc+G33Mw+nur7SpZyEKEOxEXGlLzyQ4UfaJbcme6
ce1XR2bFuAJKZTRei9AqPCCcUZlM51Ke92sRKw2Sfh3oius2FkOH6ipjv3U/697E
A7sKPPcw7+uvTPyLNhBzPvOk
</ds:X509Certificate>
</ds:X509Data>
</ds:KeyInfo>
<ds:KeyInfo>
<ds:X509Data>
<ds:X509Certificate>
MIIDrzCCApegAwIBAgIQCDvgVpBCRrGhdWrJWZHHSjANBgkqhkiG9w0BAQUFADBh
MQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3
d3cuZGlnaWNlcnQuY29tMSAwHgYDVQQDExdEaWdpQ2VydCBHbG9iYWwgUm9vdCBD
QTAeFw0wNjExMTAwMDAwMDBaFw0zMTExMTAwMDAwMDBaMGExCzAJBgNVBAYTAlVT
MRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5j
b20xIDAeBgNVBAMTF0RpZ2lDZXJ0IEdsb2JhbCBSb290IENBMIIBIjANBgkqhkiG
9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4jvhEXLeqKTTo1eqUKKPC3eQyaKl7hLOllsB
CSDMAZOnTjC3U/dDxGkAV53ijSLdhwZAAIEJzs4bg7/fzTtxRuLWZscFs3YnFo97
nh6Vfe63SKMI2tavegw5BmV/Sl0fvBf4q77uKNd0f3p4mVmFaG5cIzJLv07A6Fpt
43C/dxC//AH2hdmoRBBYMql1GNXRor5H4idq9Joz+EkIYIvUX7Q6hL+hqkpMfT7P
T19sdl6gSzeRntwi5m3OFBqOasv+zbMUZBfHWymeMr/y7vrTC0LUq7dBMtoM1O/4
gdW7jVg/tRvoSSiicNoxBN33shbyTApOB6jtSj1etX+jkMOvJwIDAQABo2MwYTAO
BgNVHQ8BAf8EBAMCAYYwDwYDVR0TAQH/BAUwAwEB/zAdBgNVHQ4EFgQUA95QNVbR
TLtm8KPiGxvDl7I90VUwHwYDVR0jBBgwFoAUA95QNVbRTLtm8KPiGxvDl7I90VUw
DQYJKoZIhvcNAQEFBQADggEBAMucN6pIExIK+t1EnE9SsPTfrgT1eXkIoyQY/Esr
hMAtudXH/vTBH1jLuG2cenTnmCmrEbXjcKChzUyImZOMkXDiqw8cvpOp/2PV5Adg
06O/nVsJ8dWO41P0jmP6P6fbtGbfYmbW0W5BjfIttep3Sp+dWOIrWcBAI+0tKIJF
PnlUkiaY4IBIqDfv8NZ5YBberOgOzW6sRBc4L0na4UU+Krk2U886UAb3LujEV0ls
YSEY1QSteDwsOoBrp+uvFRTp2InBuThs4pFsiv9kuXclVzDAGySj4dzp30d8tbQk
CAUw7C29C79Fv1C5qfPrmAESrciIxpg0X40KPMbp1ZWVbd4=
</ds:X509Certificate>
</ds:X509Data>
</ds:KeyInfo>
</ds:Signature><EntityDescriptor ID="SM10b8917de0ad09345f2bd69d3654f4908792779753ec" entityID="wellsfs.wellsfargo.com/CeoIdp">
        <IDPSSODescriptor ID="SM21072f63a116c96f5c2ae533297bfcf23c6de76f67" WantAuthnRequestsSigned="false" protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
            <KeyDescriptor use="signing">
                <ns1:KeyInfo xmlns:ns1="http://www.w3.org/2000/09/xmldsig#" Id="SMe1a31aa459bef5dd0bc63e4957203a265cf1bd6a2f">
                    <ns1:X509Data>
                        <ns1:X509IssuerSerial>
                            <ns1:X509IssuerName>CN=Wells Fargo Enterprise Certification Authority 05 G2,OU=Wells Fargo Certification Authorities,O=Wells Fargo,C=US</ns1:X509IssuerName>
                            <ns1:X509SerialNumber>525684069657580048489590</ns1:X509SerialNumber>
                        </ns1:X509IssuerSerial>
                        <ns1:X509Certificate>
                            MIIFojCCBIqgAwIBAgITagAGW60P3XYZ3oM/lAAAAAZbrTANBgkqhkiG9w0BAQsF
                            ADCBlDELMAkGA1UEBhMCVVMxFDASBgNVBAoTC1dlbGxzIEZhcmdvMS4wLAYDVQQL
                            EyVXZWxscyBGYXJnbyBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0aWVzMT8wPQYDVQQD
                            EzZXZWxscyBGYXJnbyBFbnRlcnByaXNlIENlcnRpZmljYXRpb24gQXV0aG9yaXR5
                            IDA1LTIgRzIwHhcNMjIwNzIwMDUzMDMxWhcNMjQwNzE5MDUzMDMxWjBXMQswCQYD
                            VQQGEwJVUzEUMBIGA1UEChMLV2VsbHMgRmFyZ28xDTALBgNVBAsTBElCU1MxIzAh
                            BgNVBAMTGldBU1NBTUxUZXN0LndlbGxzZmFyZ28uY29tMIIBIjANBgkqhkiG9w0B
                            AQEFAAOCAQ8AMIIBCgKCAQEAtpq1oFHVUD9uYCr/9usBm0vQBAWRgntWIFFPNBi1
                            64Fvw0DmdhZnyB8VUrTCY3c5B4O4mdvXTEESAltFHum0iXRKb9jan7X3LXL6hWov
                            1om/n0JCKFV7Qq1+eQrkeUKS6gnkD9fw0SwLyIg8l/KPHy3ePBR5QbEmnxL7L251
                            8yja2qGKO0kbUoIOvLn3pdvoToWDv/oWpQWA4zXVqssaQFooxwxljiMEvdkUOWiL
                            CEbY2qAyZO4PD0otpKpWnhxcaY85+yCkpfYtee11L+D3ZqcRLlWjBtK4asuFqtS3
                            obHjMGZvwcMQszSDIDWso355ld9kNM3TmnIrASKiIxyx8QIDAQABo4ICJzCCAiMw
                            HQYDVR0OBBYEFLpI0hzH+w9oz4LoYZ7mFUJQIDdTMB8GA1UdIwQYMBaAFFF64b4w
                            IiMpUH2mjXuSSH5vJoIrMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwucGtp
                            LndlbGxzZmFyZ28uY29tL2VudDA1LTJHMi5jcmwwdQYIKwYBBQUHAQEEaTBnMCwG
                            CCsGAQUFBzABhiBodHRwOi8vdmFsaWRhdG9yLndlbGxzZmFyZ28uY29tLzA3Bggr
                            BgEFBQcwAoYraHR0cDovL2NybC5wa2kud2VsbHNmYXJnby5jb20vZW50MDUtMkcy
                            LmNydDAMBgNVHRMBAf8EAjAAMA4GA1UdDwEB/wQEAwIEsDA9BgkrBgEEAYI3FQcE
                            MDAuBiYrBgEEAYI3FQiG+8oPh9PzRIHJkzSH9qILg//vbIFjhbcNgqr1FgIBZAIB
                            DzAdBgNVHSUEFjAUBggrBgEFBQcDAgYIKwYBBQUHAwEwgYgGA1UdIASBgDB+MDkG
                            CmCGSAGG+3uDdAEwKzApBggrBgEFBQcCARYdaHR0cHM6Ly93d3cud2VsbHNmYXJn
                            by5jb20vY3AwQQYLYIZIAYb7e4N0AAEwMjAwBggrBgEFBQcCARYkaHR0cDovL3d3
                            dy53ZWxsc2ZhcmdvLmNvbS9yZXBvc2l0b3J5MCUGA1UdEQQeMByCGldBU1NBTUxU
                            ZXN0LndlbGxzZmFyZ28uY29tMA0GCSqGSIb3DQEBCwUAA4IBAQCbrqLIGDG8nBtN
                            yfjcKdCo3ft/rKPmPNPNMTDM07cXH9u4q9Ee3+BJeB0yLEmT32SsXZ625xC3R2wn
                            cIv6zruJ84JMNXzy7bjQvFqTdFOOD9AJ22WgBoIhSB6GCYIlCtV2t4TFYCq5byeI
                            Cy7XLi0WtZUroYn9iDshRT/eS+yzN74EuUsvFkpJJdyq/lRR23l6jKCMhko+HUl7
                            QCe1XVhW8VQwioF/iTokovnypyMeBP7zNVIzPbQnUwdNzNBUiS0JlzVccnh5A6hB
                            ZPbs76gYxDPyxgWvU3U8Es31ZHVmhCq2fRn2PvNuLi4velkd1ReU3X9jM7wOerx1
                            YgCTenxH
</ns1:X509Certificate>
                        <ns1:X509SubjectName>CN=WASSAMLTest.wellsfargo.com,OU=IBSS,O=Wells Fargo,C=US</ns1:X509SubjectName>
                    </ns1:X509Data>
                </ns1:KeyInfo>
            </KeyDescriptor>
            <KeyDescriptor use="signing">
                <ns1:KeyInfo xmlns:ns1="http://www.w3.org/2000/09/xmldsig#" Id="SMe1a31aa459bef5dd0bc63e4957203a265cf1bd6a2f1">
                    <ns1:X509Data>
                        <ns1:X509IssuerSerial>
                            <ns1:X509IssuerName>CN=Wells Fargo Enterprise Certification Authority 05 G2,OU=Wells Fargo Certification Authorities,O=Wells Fargo,C=US</ns1:X509IssuerName>
                        </ns1:X509IssuerSerial>
                        <ns1:X509Certificate>
MIIFojCCBIqgAwIBAgITagARHcoOy+o8Y4WaCAAAABEdyjANBgkqhkiG9w0BAQsF
ADCBlDELMAkGA1UEBhMCVVMxFDASBgNVBAoTC1dlbGxzIEZhcmdvMS4wLAYDVQQL
EyVXZWxscyBGYXJnbyBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0aWVzMT8wPQYDVQQD
EzZXZWxscyBGYXJnbyBFbnRlcnByaXNlIENlcnRpZmljYXRpb24gQXV0aG9yaXR5
IDA1LTIgRzIwHhcNMjQwNTIxMDc1NDUzWhcNMjYwNTIxMDc1NDUzWjBXMQswCQYD
VQQGEwJVUzEUMBIGA1UEChMLV2VsbHMgRmFyZ28xDTALBgNVBAsTBElCU1MxIzAh
BgNVBAMTGldBU1NBTUxUZXN0LndlbGxzZmFyZ28uY29tMIIBIjANBgkqhkiG9w0B
AQEFAAOCAQ8AMIIBCgKCAQEAwNuV6U4M7YPiECRb9T2ACNpLiRHv7g7tZd7fw024
OKEp44sinAz0VtSYKPBtGmG8Ws9qOR3pb4XfgaV7ev+SmijxxFqazcpnFniljVU9
o2Cd4sZZTnc+EDi9lkep/xAFV8zk4CVDga8xj9o+kzCg49Dgm0/0URCc6vNWPgla
Ok3nlaBytmBS2MA4NQsWyfhMYTM4ckeMT8y8iI6UzcM+8/K3bGvHXdbw6nTjNknf
x+wySIbnA4Z1uq3BGez5/5P4/wAl4Qh2R3xh06bD07+gqLl966cXPOiOIKgpI7io
FZW1vrICbcN91xmB5oTPH9XwCkWPK5VErmr4i7IN4wCDCQIDAQABo4ICJzCCAiMw
HQYDVR0OBBYEFH8ttPGG9ga4F30hO10oN+l8+4/ZMB8GA1UdIwQYMBaAFFF64b4w
IiMpUH2mjXuSSH5vJoIrMDwGA1UdHwQ1MDMwMaAvoC2GK2h0dHA6Ly9jcmwucGtp
LndlbGxzZmFyZ28uY29tL2VudDA1LTJHMi5jcmwwdQYIKwYBBQUHAQEEaTBnMCwG
CCsGAQUFBzABhiBodHRwOi8vdmFsaWRhdG9yLndlbGxzZmFyZ28uY29tLzA3Bggr
BgEFBQcwAoYraHR0cDovL2NybC5wa2kud2VsbHNmYXJnby5jb20vZW50MDUtMkcy
LmNydDAMBgNVHRMBAf8EAjAAMA4GA1UdDwEB/wQEAwIEsDA9BgkrBgEEAYI3FQcE
MDAuBiYrBgEEAYI3FQiG+8oPh9PzRIHJkzSH9qILg//vbIFjhbcNgqr1FgIBZAIB
DzAdBgNVHSUEFjAUBggrBgEFBQcDAgYIKwYBBQUHAwEwgYgGA1UdIASBgDB+MDkG
CmCGSAGG+3uDdAEwKzApBggrBgEFBQcCARYdaHR0cHM6Ly93d3cud2VsbHNmYXJn
by5jb20vY3AwQQYLYIZIAYb7e4N0AAEwMjAwBggrBgEFBQcCARYkaHR0cDovL3d3
dy53ZWxsc2ZhcmdvLmNvbS9yZXBvc2l0b3J5MCUGA1UdEQQeMByCGldBU1NBTUxU
ZXN0LndlbGxzZmFyZ28uY29tMA0GCSqGSIb3DQEBCwUAA4IBAQAmLH3AsQv6lrVx
nE9sk4xCbyRnQ+1Zy9YspRUg10LgVbYnWVODCeI4flqTgs7wLJ7b+/XFBMS4e8B9
smds9Zoh9N7nqrBo8nYR7MVhZQ4RMUI0mVr3PWqye43zoZgFiNXbff65UnvsR3Fk
VQs+zFpwiEvDBAcB1Mb34WPujV7mfEqDHwEXu4/KiOXU9rYOhFnGVq0eDa6e7CoL
B5ZpRfGgKI3ahORsrOQIMn/je1oAw66DZpM+PlrNK2sorDbwAw1V8siPBfZYX2uC
yluwl+ufeiDjZ0m1GujPTpq3STmi3gO0ZQq6pDILtFsmwD8hBQjgV8oYBM8Tnf5S
CI4lUXuN
                        </ns1:X509Certificate>
                        <ns1:X509SubjectName>CN=WASSAMLTest.wellsfargo.com,OU=IBSS,O=Wells Fargo,C=US</ns1:X509SubjectName>
                    </ns1:X509Data>
                </ns1:KeyInfo>
            </KeyDescriptor>            
            <ArtifactResolutionService Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP" Location="https://wellsfsuat.wellsfargo.com/affwebservices/public/saml2artifactresolution" index="0" isDefault="false"/>
            <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="https://wellsfsuat.wellsfargo.com/affwebservices/public/saml2slo"/>
            <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="https://wellsfsuat.wellsfargo.com/affwebservices/public/saml2sso"/>
        </IDPSSODescriptor>
    </EntityDescriptor>
    <saml2md:EntityDescriptor entityID="https://e-sett-otpdev.ffrontier.com/idp/provider" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:saml2md="urn:oasis:names:tc:SAML:2.0:metadata">
    <saml2md:IDPSSODescriptor WantAuthnRequestsSigned="true"
        protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol" >
        <saml2md:KeyDescriptor use="signing" >
            <ds:KeyInfo>
            <ds:X509Data>
            <ds:X509Certificate>MIIDSDCCAjCgAwIBAgIEWLVmDTANBgkqhkiG9w0BAQsFADBmMQ8wDQYDVQQGDAbkuI3mmI4xDzANBgNVBAgMBuS4jeaYjjEPMA0GA1UEBwwG5LiN5piOMQ8wDQYDVQQKDAbkuI3mmI4xDzANBgNVBAsMBuS4jeaYjjEPMA0GA1UEAwwG5LiN5piOMB4XDTE3MDIyODExNTkwOVoXDTM3MDIyMzExNTkwOVowZjEPMA0GA1UEBgwG5LiN5piOMQ8wDQYDVQQIDAbkuI3mmI4xDzANBgNVBAcMBuS4jeaYjjEPMA0GA1UECgwG5LiN5piOMQ8wDQYDVQQLDAbkuI3mmI4xDzANBgNVBAMMBuS4jeaYjjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAIYx9O8HPHBqwkTJnL+SfJkZU3tquBYh2rcckfXRhmcSDwHYp0u/ZqWPboBKxbG8IoD7X7+NoTGJX58Vywct7EipOfB60X6dqcXjI7GcQKjSJoW+fjwc4G6RN5MVK3fnnACJAcSzkOLlqUIrPZ5+7owoxgKTeQDMN7RKTnvH+38eeuwfSVxXE/tmzgOQE09cHr4mqJ+gukA93zMVGpNhBwSS79xYqKQVFdKUv2d10bVy2DKj4ZCha30MYOCX/2zhtC3gk0wu6MzyvJlkNghXTqBlUOFy1VdeV+11Ogs5av7UtdmVnQ2XSDVHdaTugs8flSV8ih6DHNd3uYLIW4TmnfcCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAYSQrrrvVzmiQWXugganyAI08yL7Hdl9u4hIm5r6Q/MpJ7h1lhvt+x0idVZISNpn3Z1nGVxSBrV1cmk/TU4tfmszniLPkIeaogVh6bsB3ElvdLjefbBbNjXOqx7Q4ebd96KWBbjC+2I+eccfvlUuI0N+aCFaSQ/hSs1QQ8Q1Uy/eraX4I50VSM0u7dv8UTVNFkIAEGHC9TCz6azjt3oMTXbbCysFc2LKQwIDXGXy40gtf47XI/GZIjII6DRg7E0CkfFcRdHiZXOj/v5o7ypcYoCxvN9AseYStOIRZu8+UcZhq9tZyqQ14o+ZIV0ZwAMb87vPw8iZV09P5+LUtmqm1bw==
            </ds:X509Certificate>
            </ds:X509Data>
            </ds:KeyInfo>
        </saml2md:KeyDescriptor>
        <saml2md:KeyDescriptor use="encryption">
            <ds:KeyInfo>
            <ds:X509Data>
            <ds:X509Certificate>MIIDSDCCAjCgAwIBAgIEWLVm4jANBgkqhkiG9w0BAQsFADBmMQ8wDQYDVQQGDAbkuI3mmI4xDzANBgNVBAgMBuS4jeaYjjEPMA0GA1UEBwwG5LiN5piOMQ8wDQYDVQQKDAbkuI3mmI4xDzANBgNVBAsMBuS4jeaYjjEPMA0GA1UEAwwG5LiN5piOMB4XDTE3MDIyODEyMDI0MloXDTM3MDIyMzEyMDI0MlowZjEPMA0GA1UEBgwG5LiN5piOMQ8wDQYDVQQIDAbkuI3mmI4xDzANBgNVBAcMBuS4jeaYjjEPMA0GA1UECgwG5LiN5piOMQ8wDQYDVQQLDAbkuI3mmI4xDzANBgNVBAMMBuS4jeaYjjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALMblsJV6867XjyXBfPiFr2C4p8040SyYOvEJmaZxSNmz8x6OXdc8DqE4UhzFviaSkFtVxK2O2xdlk3N6g/dbuAwkDHhX/aqcfy1wgyuGte4jZ8EYhDhtRHUXsgqilRag1PPWdhQyaNDek3AGMqvO7N7UxgYd5O+cw6VY97DbzfPzDQrW/suf58D94uW1159BIFOZQ8pDB1432yEXLRAdIe2JoJt5nMnh1O9GHK2exLNoXFQSmaw0OiAlyFnFaVONFXSq6e9gGfR8jgaclbHZcH1OgLgdz+0BHnsoDMJsRl1laeHUS3c029u9o5ij8FNmgV8JLQK7XIwQ76S2tAGnVcCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAkF7Kfujnis9jXzDqf20u6D3a+gzd/4iSmqdz/3rMzeft2SC4XywftpySLUyJzvzYZp9ViSBxJDV6T0I5/5W1B66otLvTDWsXtf5HDJ4qnBcbc8c8C2WWhVQ1wc8ieQn+6yUwoxnl68Tw1GPVpuqO3jJWgw3e6I93brACcV61DdMUedEODaYLEM3Hl72CC1gpr7yiTF1bUT1m9Pit3+pnUE1sHablqkYlFbf22MpkM6TxsFsOTBHn9l4HuYZiJhmw+Pn/4BrfRDxpDo+2uPeNjajCxPH7SwyQ8S4gPi1V6rIdnfCCM8rOLvNEa5ZM/qr16NK3QGdyMLWwy/7l75ihSg==
            </ds:X509Certificate>
            </ds:X509Data>
            </ds:KeyInfo>
        </saml2md:KeyDescriptor>

        <!--
        <saml2md:ArtifactResolutionService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
            Location="https://e-sett-otpdev.ffrontier.com/idp/idp_art_resolver" index="0" isDefault="1"/>
        -->

        <saml2md:SingleLogoutService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
            Location="https://e-sett-otpdev.ffrontier.com/idp/slo_redirect_idp"
            ResponseLocation="https://e-sett-otpdev.ffrontier.com/idp/slo_redirect_return_idp"/>
        <!--
        <saml2md:SingleLogoutService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
            Location="https://e-sett-otpdev.ffrontier.com/idp/slo_soap_idp"/>
        -->
        <!--
        <saml2md:SingleLogoutService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
            Location="https://e-sett-otpdev.ffrontier.com/idp/slo_post_idp"
            ResponseLocation="https://e-sett-otpdev.ffrontier.com/idp/slo_post_return_idp"/>
        -->
        <!--
        <saml2md:SingleLogoutService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Artifact"
            Location="https://e-sett-otpdev.ffrontier.com/idp/slo_artifact_idp"
            ResponseLocation="https://e-sett-otpdev.ffrontier.com/idp/slo_artifact_return_idp"/>
        -->

        <saml2md:ManageNameIDService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
            Location="https://e-sett-otpdev.ffrontier.com/idp/nim_redirect_idp"
            ResponseLocation="https://e-sett-otpdev.ffrontier.com/idp/nim_redirect_return_idp"/>
        <!--
        <saml2md:ManageNameIDService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
            Location="https://e-sett-otpdev.ffrontier.com/idp/nim_soap_idp"/>
        -->
        <!--
        <saml2md:ManageNameIDService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
            Location="https://e-sett-otpdev.ffrontier.com/idp/nim_post_idp"
            ResponseLocation="https://e-sett-otpdev.ffrontier.com/idp/nim_post_return_idp"/>
        -->
        <!--
        <saml2md:ManageNameIDService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Artifact"
            Location="https://e-sett-otpdev.ffrontier.com/idp/nim_artifact_idp"
            ResponseLocation="https://e-sett-otpdev.ffrontier.com/idp/nim_artifact_return_idp"/>
        -->

        <saml2md:NameIDFormat>urn:oasis:names:tc:SAML:2.0:nameid-format:persistent</saml2md:NameIDFormat>
        <saml2md:NameIDFormat>urn:oasis:names:tc:SAML:2.0:nameid-format:transient</saml2md:NameIDFormat>
        <saml2md:NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress</saml2md:NameIDFormat>
        <saml2md:NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:X509SubjectName</saml2md:NameIDFormat>

        <saml2md:SingleSignOnService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
            Location="https://e-sett-otpdev.ffrontier.com/idp/sso_redirect"/>
        <!--
        <saml2md:SingleSignOnService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
            Location="https://e-sett-otpdev.ffrontier.com/idp/sso_post"/>
        -->
        <!--
        <saml2md:SingleSignOnService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Artifact"
            Location="https://e-sett-otpdev.ffrontier.com/idp/sso_artifact"/>
        -->
        <!--
        <saml2md:SingleSignOnService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:PAOS"
            Location="https://e-sett-otpdev.ffrontier.com/idp/sso_paos"/>
        -->
        <!--
        <saml2md:SingleSignOnService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
            Location="https://e-sett-otpdev.ffrontier.com/idp/sso_paos"/>
        -->
        <!--
        <saml2md:NameIDMappingService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
            Location="https://e-sett-otpdev.ffrontier.com/idp/map_soap_idp"/>
        -->

    </saml2md:IDPSSODescriptor>

    <!--
    <saml2md:AttributeAuthorityDescriptor
        protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
        <saml2md:KeyDescriptor use="signing" >
            <ds:KeyInfo>
            <ds:X509Data>
            <ds:X509Certificate>
            MIICSjCCAbMCBEAsOA0wDQYJKoZIhvcNAQEEBQAwbDELMAkGA1UEBhMCanAxDjAMBgNVBAgTBXRv
            a3lvMQ4wDAYDVQQHEwV0b2t5bzEMMAoGA1UEChMDbnR0MRQwEgYDVQQLEwtudHQgbGliZXJ0eTEZ
            MBcGA1UEAxMQZGVtby5saWJlcnR5Lm50dDAeFw0wNDAyMTMwMjM1NTdaFw0xNDAyMTAwMjM1NTda
            MGwxCzAJBgNVBAYTAmpwMQ4wDAYDVQQIEwV0b2t5bzEOMAwGA1UEBxMFdG9reW8xDDAKBgNVBAoT
            A250dDEUMBIGA1UECxMLbnR0IGxpYmVydHkxGTAXBgNVBAMTEGRlbW8ubGliZXJ0eS5udHQwgZ8w
            DQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBALH+5y0sA5qaulyZxNeYkUIyceuVOM8J3Xq4C4BftFKb
            1AK5L2bXAF3dWjl39OS9zGwu0vKaoJZAzha2ijMJlCytf9pkOByNUL3/i3FjWKfOqXt4GXvhAnmJ
            f5Pv70VHgAemXPcGmWXT7/18ZcjCiDJdxUoS5DmcWu5+wHuV43V1AgMBAAEwDQYJKoZIhvcNAQEE
            BQADgYEAmtrE3UypbVPD7AwxUaIGxO36xgZiwP6MjAjdVU1ZHXf/gAQ20pZdzcCL7WP0O00UhlsV
            GEoz5o9aUZTLlsNU3Kj+vTrzRWPnCXrEmQ93DSbKLjIY+snqJvmVNjrzIveqmBKuNu0mtsq0mW0b
            48pd7/ZrEMDGnJxb2SSifvldK0w=
            </ds:X509Certificate>
            </ds:X509Data>
            </ds:KeyInfo>
        </saml2md:KeyDescriptor>
        <saml2md:KeyDescriptor use="encryption">
            <ds:KeyInfo>
            <ds:X509Data>
            <ds:X509Certificate>
            MIICSjCCAbMCBEAsOFQwDQYJKoZIhvcNAQEEBQAwbDELMAkGA1UEBhMCanAxDjAMBgNVBAgTBXRv
            a3lvMQ4wDAYDVQQHEwV0b2t5bzEMMAoGA1UEChMDbnR0MRQwEgYDVQQLEwtudHQgbGliZXJ0eTEZ
            MBcGA1UEAxMQZGVtby5saWJlcnR5Lm50dDAeFw0wNDAyMTMwMjM3MDhaFw0xNDAyMTAwMjM3MDha
            MGwxCzAJBgNVBAYTAmpwMQ4wDAYDVQQIEwV0b2t5bzEOMAwGA1UEBxMFdG9reW8xDDAKBgNVBAoT
            A250dDEUMBIGA1UECxMLbnR0IGxpYmVydHkxGTAXBgNVBAMTEGRlbW8ubGliZXJ0eS5udHQwgZ8w
            DQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBANk/0GylPFjvypHKmzl8x8h1ueiZX5PAducmvcv1ka4V
            CRz5gux6neRPfjtekHJ9egEHcGR298Ig5Y+hmew1bS3XO041UTptXV76MURUPkNwzMnkZHuzTjcV
            YCeHWt4z+zRlFlpszMy8OsPwIsR0avj+8ijxBIu1BTQC9hL3oaqLAgMBAAEwDQYJKoZIhvcNAQEE
            BQADgYEADsUS/VTEE/LjDO2L8VSWS8WOP6W02wwjtVOmqmdF0OhoT6pnM/xZhwtDowxpWoNZfKrg
            0H9pkY2O66GPIbtqHBEFNC6L70oVhb6nbfR6Vzcjo+uNUqxdrZ8YWbKCf6P0ZILLWfyhzx/MzoTN
            ZQM/YKknHX4XAenZ9WEDPnYQLnI=
            </ds:X509Certificate>
            </ds:X509Data>
            </ds:KeyInfo>
        </saml2md:KeyDescriptor>

        <saml2md:AttributeService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
            Location="https://e-sett-otpdev.ffrontier.com/idp/attrq_soap_authority"/>

    </saml2md:AttributeAuthorityDescriptor>
    -->
    <!--
    <saml2md:AuthnAuthorityDescriptor
        protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
        <saml2md:KeyDescriptor use="signing" >
            <ds:KeyInfo>
            <ds:X509Data>
            <ds:X509Certificate>
            MIICSjCCAbMCBEAsOA0wDQYJKoZIhvcNAQEEBQAwbDELMAkGA1UEBhMCanAxDjAMBgNVBAgTBXRv
            a3lvMQ4wDAYDVQQHEwV0b2t5bzEMMAoGA1UEChMDbnR0MRQwEgYDVQQLEwtudHQgbGliZXJ0eTEZ
            MBcGA1UEAxMQZGVtby5saWJlcnR5Lm50dDAeFw0wNDAyMTMwMjM1NTdaFw0xNDAyMTAwMjM1NTda
            MGwxCzAJBgNVBAYTAmpwMQ4wDAYDVQQIEwV0b2t5bzEOMAwGA1UEBxMFdG9reW8xDDAKBgNVBAoT
            A250dDEUMBIGA1UECxMLbnR0IGxpYmVydHkxGTAXBgNVBAMTEGRlbW8ubGliZXJ0eS5udHQwgZ8w
            DQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBALH+5y0sA5qaulyZxNeYkUIyceuVOM8J3Xq4C4BftFKb
            1AK5L2bXAF3dWjl39OS9zGwu0vKaoJZAzha2ijMJlCytf9pkOByNUL3/i3FjWKfOqXt4GXvhAnmJ
            f5Pv70VHgAemXPcGmWXT7/18ZcjCiDJdxUoS5DmcWu5+wHuV43V1AgMBAAEwDQYJKoZIhvcNAQEE
            BQADgYEAmtrE3UypbVPD7AwxUaIGxO36xgZiwP6MjAjdVU1ZHXf/gAQ20pZdzcCL7WP0O00UhlsV
            GEoz5o9aUZTLlsNU3Kj+vTrzRWPnCXrEmQ93DSbKLjIY+snqJvmVNjrzIveqmBKuNu0mtsq0mW0b
            48pd7/ZrEMDGnJxb2SSifvldK0w=
            </ds:X509Certificate>
            </ds:X509Data>
            </ds:KeyInfo>
        </saml2md:KeyDescriptor>
        <saml2md:KeyDescriptor use="encryption">
            <ds:KeyInfo>
            <ds:X509Data>
            <ds:X509Certificate>
            MIICSjCCAbMCBEAsOFQwDQYJKoZIhvcNAQEEBQAwbDELMAkGA1UEBhMCanAxDjAMBgNVBAgTBXRv
            a3lvMQ4wDAYDVQQHEwV0b2t5bzEMMAoGA1UEChMDbnR0MRQwEgYDVQQLEwtudHQgbGliZXJ0eTEZ
            MBcGA1UEAxMQZGVtby5saWJlcnR5Lm50dDAeFw0wNDAyMTMwMjM3MDhaFw0xNDAyMTAwMjM3MDha
            MGwxCzAJBgNVBAYTAmpwMQ4wDAYDVQQIEwV0b2t5bzEOMAwGA1UEBxMFdG9reW8xDDAKBgNVBAoT
            A250dDEUMBIGA1UECxMLbnR0IGxpYmVydHkxGTAXBgNVBAMTEGRlbW8ubGliZXJ0eS5udHQwgZ8w
            DQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBANk/0GylPFjvypHKmzl8x8h1ueiZX5PAducmvcv1ka4V
            CRz5gux6neRPfjtekHJ9egEHcGR298Ig5Y+hmew1bS3XO041UTptXV76MURUPkNwzMnkZHuzTjcV
            YCeHWt4z+zRlFlpszMy8OsPwIsR0avj+8ijxBIu1BTQC9hL3oaqLAgMBAAEwDQYJKoZIhvcNAQEE
            BQADgYEADsUS/VTEE/LjDO2L8VSWS8WOP6W02wwjtVOmqmdF0OhoT6pnM/xZhwtDowxpWoNZfKrg
            0H9pkY2O66GPIbtqHBEFNC6L70oVhb6nbfR6Vzcjo+uNUqxdrZ8YWbKCf6P0ZILLWfyhzx/MzoTN
            ZQM/YKknHX4XAenZ9WEDPnYQLnI=
            </ds:X509Certificate>
            </ds:X509Data>
            </ds:KeyInfo>
        </saml2md:KeyDescriptor>

        <saml2md:AuthnQueryService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
            Location="https://e-sett-otpdev.ffrontier.com/idp/authnq_soap_authority"/>

    </saml2md:AuthnAuthorityDescriptor>
    -->

</saml2md:EntityDescriptor>
<saml2md:EntityDescriptor entityID="https://training-e-sett-otp2.ffrontier.com/idp/provider" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:saml2md="urn:oasis:names:tc:SAML:2.0:metadata">
    <saml2md:IDPSSODescriptor WantAuthnRequestsSigned="true"
        protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol" >
        <saml2md:KeyDescriptor use="signing" >
			<ds:KeyInfo>
			<ds:X509Data>
			<ds:X509Certificate>MIIDSDCCAjCgAwIBAgIEWLewNDANBgkqhkiG9w0BAQsFADBmMQ8wDQYDVQQGDAbkuI3mmI4xDzANBgNVBAgMBuS4jeaYjjEPMA0GA1UEBwwG5LiN5piOMQ8wDQYDVQQKDAbkuI3mmI4xDzANBgNVBAsMBuS4jeaYjjEPMA0GA1UEAwwG5LiN5piOMB4XDTE3MDMwMjA1NDAwNFoXDTM3MDIyNTA1NDAwNFowZjEPMA0GA1UEBgwG5LiN5piOMQ8wDQYDVQQIDAbkuI3mmI4xDzANBgNVBAcMBuS4jeaYjjEPMA0GA1UECgwG5LiN5piOMQ8wDQYDVQQLDAbkuI3mmI4xDzANBgNVBAMMBuS4jeaYjjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALDq9/k8sSMmi/4tHrtaQRLL6vx44ao4uJRjfk7qvJaP0GcKCl4jDe0Iydc3lKdcGXtFftjurXlbyE0I+6w7c6IgW8WEk7dATwKeeYPbqjgd/97UmIjmirIrV0fdTivwv/u0CjiMTcCkugAsy+hF3zbqGsjSFUSWXa98mTCukbfADnGEzyYzYGhLdvwV/B6RAJW15prTQt2CcMzRn4AtY6JcQtGhv022wZdZ6YqCH3I7iO5aaez1IW+lozptOYVXlt00M2BY8RxOEZ6sCAhrWNvfCKPM4R8t1QUfEwBiCa4OOKKwrwierPMxkUxLtBhE0jb4TQAN5Gq7hrJw7sc5zx0CAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAIUmvvL4RcYJ1rVyz5C5FKHxQfvnUMO4S+eulI2px646ah1pLiYmIVpi9MTvPrlmh9Sfs0tuCaT8V3P9SX0z9BYESh2AqQoz/4+p0uIgdAEXloeXl0BbZKwQtw5MMbdDjebHVgU8/O9qYYyRh+pIBw86J97Ha6L5J4KJVq4ejSQUYS8eNoiOJrxz8BQZne/AMq5daEvSpKGZ1qTaL3Ck9m2T6sRP58l2cv7SDS+FkQjY09bTQWV+OM9AY8YpXYcdE2r2l+JxmPJe+WzsgG/0ZoMFsWvtItfTBmD9cgcMXxD1/uCUh4uWgVtHZQGH8RA+/YOelIJNxVa3H8Mtry2CHug==
			</ds:X509Certificate>
			</ds:X509Data>
			</ds:KeyInfo>
        </saml2md:KeyDescriptor>
        <saml2md:KeyDescriptor use="encryption">
			<ds:KeyInfo>
			<ds:X509Data>
			<ds:X509Certificate>MIIDSDCCAjCgAwIBAgIEWLexWjANBgkqhkiG9w0BAQsFADBmMQ8wDQYDVQQGDAbkuI3mmI4xDzANBgNVBAgMBuS4jeaYjjEPMA0GA1UEBwwG5LiN5piOMQ8wDQYDVQQKDAbkuI3mmI4xDzANBgNVBAsMBuS4jeaYjjEPMA0GA1UEAwwG5LiN5piOMB4XDTE3MDMwMjA1NDQ1OFoXDTM3MDIyNTA1NDQ1OFowZjEPMA0GA1UEBgwG5LiN5piOMQ8wDQYDVQQIDAbkuI3mmI4xDzANBgNVBAcMBuS4jeaYjjEPMA0GA1UECgwG5LiN5piOMQ8wDQYDVQQLDAbkuI3mmI4xDzANBgNVBAMMBuS4jeaYjjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAJ7Kx1Is33hT9bCpPBEdugXx9czzsdMIZrInL+rMpZStLhBAjkHofvn3jHDfLGrVgnPH3Ufo1a0qWSPy2//4Va5yVJeGOJJHfH6R7wVT99iEOrcLRPhhCiLoX7Uhs3xHUpDzoAI+3bAEXNXGUB1lQJE0XLy/1lGV/e3tKBQ04vkqlVIlgXBccQEUtNxRmVugjPjE8wL7LPBBXkWhH3x6E34a93cGbgsko/UjlCeEIDF8DhAy5XIcDrmdrNho7xD8da+2JvABock0QuU6d0mBRplQOSka2KY6PcCsgShAgos/YHkFhMWbJBDp9n5mage/MlNdF0kfxnBcFB7I3h02r+cCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAlLMqpy9V/yod3a5R1rKUiY7O56a/rbfVyrRAO31NFNZ9S4BcJ97NewclaSHY62EF1b6AM3j/FtnQtfPfGkIOeQb5cVSYmJtNDKQgmJ8t8RJb6fjaA6SUS4dSMYZQHW+rbh5C5pSrMdoXCQ4v80ozWFFqnCj1R9SP9B9OCFCDZWKPcJjTIkJGW5bAl5t9OUTPKcqMx6HGOLH5Kwt0bq3I7b0I0f+pPvB7sKBkUMLGZLD6dYI5kQwQYSwhuSPSFI8SxbkbMOK5Zv8esTSbuE/ngkQ8+dhu7ypPp3XJXFRkT5q43hBzhspxlqeO0/48kFzf1/FCKYW35S6OO13ZeAeVIQ==
			</ds:X509Certificate>
			</ds:X509Data>
			</ds:KeyInfo>
        </saml2md:KeyDescriptor>

		<!--
        <saml2md:ArtifactResolutionService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/idp_art_resolver" index="0" isDefault="1"/>
		-->

        <saml2md:SingleLogoutService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/slo_redirect_idp"
            ResponseLocation="https://training-e-sett-otp2.ffrontier.com/idp/slo_redirect_return_idp"/>
		<!--
        <saml2md:SingleLogoutService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/slo_soap_idp"/>
		-->
		<!--
        <saml2md:SingleLogoutService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/slo_post_idp"
            ResponseLocation="https://training-e-sett-otp2.ffrontier.com/idp/slo_post_return_idp"/>
		-->
		<!--
        <saml2md:SingleLogoutService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Artifact"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/slo_artifact_idp"
            ResponseLocation="https://training-e-sett-otp2.ffrontier.com/idp/slo_artifact_return_idp"/>
		-->

        <saml2md:ManageNameIDService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/nim_redirect_idp"
            ResponseLocation="https://training-e-sett-otp2.ffrontier.com/idp/nim_redirect_return_idp"/>
		<!--
        <saml2md:ManageNameIDService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/nim_soap_idp"/>
		-->
		<!--
        <saml2md:ManageNameIDService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/nim_post_idp"
            ResponseLocation="https://training-e-sett-otp2.ffrontier.com/idp/nim_post_return_idp"/>
		-->
		<!--
        <saml2md:ManageNameIDService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Artifact"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/nim_artifact_idp"
            ResponseLocation="https://training-e-sett-otp2.ffrontier.com/idp/nim_artifact_return_idp"/>
		-->

        <saml2md:NameIDFormat>urn:oasis:names:tc:SAML:2.0:nameid-format:persistent</saml2md:NameIDFormat>
        <saml2md:NameIDFormat>urn:oasis:names:tc:SAML:2.0:nameid-format:transient</saml2md:NameIDFormat>
        <saml2md:NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress</saml2md:NameIDFormat>
        <saml2md:NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:X509SubjectName</saml2md:NameIDFormat>

        <saml2md:SingleSignOnService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/sso_redirect"/>
		<!--
        <saml2md:SingleSignOnService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/sso_post"/>
		-->
		<!--
        <saml2md:SingleSignOnService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Artifact"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/sso_artifact"/>
		-->
		<!--
        <saml2md:SingleSignOnService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:PAOS"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/sso_paos"/>
		-->
		<!--
        <saml2md:SingleSignOnService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/sso_paos"/>
		-->
		<!--
        <saml2md:NameIDMappingService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/map_soap_idp"/>
		-->

    </saml2md:IDPSSODescriptor>

	<!--
    <saml2md:AttributeAuthorityDescriptor
        protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
        <saml2md:KeyDescriptor use="signing" >
			<ds:KeyInfo>
			<ds:X509Data>
			<ds:X509Certificate>
			MIICSjCCAbMCBEAsOA0wDQYJKoZIhvcNAQEEBQAwbDELMAkGA1UEBhMCanAxDjAMBgNVBAgTBXRv
			a3lvMQ4wDAYDVQQHEwV0b2t5bzEMMAoGA1UEChMDbnR0MRQwEgYDVQQLEwtudHQgbGliZXJ0eTEZ
			MBcGA1UEAxMQZGVtby5saWJlcnR5Lm50dDAeFw0wNDAyMTMwMjM1NTdaFw0xNDAyMTAwMjM1NTda
			MGwxCzAJBgNVBAYTAmpwMQ4wDAYDVQQIEwV0b2t5bzEOMAwGA1UEBxMFdG9reW8xDDAKBgNVBAoT
			A250dDEUMBIGA1UECxMLbnR0IGxpYmVydHkxGTAXBgNVBAMTEGRlbW8ubGliZXJ0eS5udHQwgZ8w
			DQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBALH+5y0sA5qaulyZxNeYkUIyceuVOM8J3Xq4C4BftFKb
			1AK5L2bXAF3dWjl39OS9zGwu0vKaoJZAzha2ijMJlCytf9pkOByNUL3/i3FjWKfOqXt4GXvhAnmJ
			f5Pv70VHgAemXPcGmWXT7/18ZcjCiDJdxUoS5DmcWu5+wHuV43V1AgMBAAEwDQYJKoZIhvcNAQEE
			BQADgYEAmtrE3UypbVPD7AwxUaIGxO36xgZiwP6MjAjdVU1ZHXf/gAQ20pZdzcCL7WP0O00UhlsV
			GEoz5o9aUZTLlsNU3Kj+vTrzRWPnCXrEmQ93DSbKLjIY+snqJvmVNjrzIveqmBKuNu0mtsq0mW0b
			48pd7/ZrEMDGnJxb2SSifvldK0w=
			</ds:X509Certificate>
			</ds:X509Data>
			</ds:KeyInfo>
        </saml2md:KeyDescriptor>
        <saml2md:KeyDescriptor use="encryption">
			<ds:KeyInfo>
			<ds:X509Data>
			<ds:X509Certificate>
			MIICSjCCAbMCBEAsOFQwDQYJKoZIhvcNAQEEBQAwbDELMAkGA1UEBhMCanAxDjAMBgNVBAgTBXRv
			a3lvMQ4wDAYDVQQHEwV0b2t5bzEMMAoGA1UEChMDbnR0MRQwEgYDVQQLEwtudHQgbGliZXJ0eTEZ
			MBcGA1UEAxMQZGVtby5saWJlcnR5Lm50dDAeFw0wNDAyMTMwMjM3MDhaFw0xNDAyMTAwMjM3MDha
			MGwxCzAJBgNVBAYTAmpwMQ4wDAYDVQQIEwV0b2t5bzEOMAwGA1UEBxMFdG9reW8xDDAKBgNVBAoT
			A250dDEUMBIGA1UECxMLbnR0IGxpYmVydHkxGTAXBgNVBAMTEGRlbW8ubGliZXJ0eS5udHQwgZ8w
			DQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBANk/0GylPFjvypHKmzl8x8h1ueiZX5PAducmvcv1ka4V
			CRz5gux6neRPfjtekHJ9egEHcGR298Ig5Y+hmew1bS3XO041UTptXV76MURUPkNwzMnkZHuzTjcV
			YCeHWt4z+zRlFlpszMy8OsPwIsR0avj+8ijxBIu1BTQC9hL3oaqLAgMBAAEwDQYJKoZIhvcNAQEE
			BQADgYEADsUS/VTEE/LjDO2L8VSWS8WOP6W02wwjtVOmqmdF0OhoT6pnM/xZhwtDowxpWoNZfKrg
			0H9pkY2O66GPIbtqHBEFNC6L70oVhb6nbfR6Vzcjo+uNUqxdrZ8YWbKCf6P0ZILLWfyhzx/MzoTN
			ZQM/YKknHX4XAenZ9WEDPnYQLnI=
			</ds:X509Certificate>
			</ds:X509Data>
			</ds:KeyInfo>
        </saml2md:KeyDescriptor>

        <saml2md:AttributeService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/attrq_soap_authority"/>

    </saml2md:AttributeAuthorityDescriptor>
	-->
	<!--
    <saml2md:AuthnAuthorityDescriptor
        protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
        <saml2md:KeyDescriptor use="signing" >
			<ds:KeyInfo>
			<ds:X509Data>
			<ds:X509Certificate>
			MIICSjCCAbMCBEAsOA0wDQYJKoZIhvcNAQEEBQAwbDELMAkGA1UEBhMCanAxDjAMBgNVBAgTBXRv
			a3lvMQ4wDAYDVQQHEwV0b2t5bzEMMAoGA1UEChMDbnR0MRQwEgYDVQQLEwtudHQgbGliZXJ0eTEZ
			MBcGA1UEAxMQZGVtby5saWJlcnR5Lm50dDAeFw0wNDAyMTMwMjM1NTdaFw0xNDAyMTAwMjM1NTda
			MGwxCzAJBgNVBAYTAmpwMQ4wDAYDVQQIEwV0b2t5bzEOMAwGA1UEBxMFdG9reW8xDDAKBgNVBAoT
			A250dDEUMBIGA1UECxMLbnR0IGxpYmVydHkxGTAXBgNVBAMTEGRlbW8ubGliZXJ0eS5udHQwgZ8w
			DQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBALH+5y0sA5qaulyZxNeYkUIyceuVOM8J3Xq4C4BftFKb
			1AK5L2bXAF3dWjl39OS9zGwu0vKaoJZAzha2ijMJlCytf9pkOByNUL3/i3FjWKfOqXt4GXvhAnmJ
			f5Pv70VHgAemXPcGmWXT7/18ZcjCiDJdxUoS5DmcWu5+wHuV43V1AgMBAAEwDQYJKoZIhvcNAQEE
			BQADgYEAmtrE3UypbVPD7AwxUaIGxO36xgZiwP6MjAjdVU1ZHXf/gAQ20pZdzcCL7WP0O00UhlsV
			GEoz5o9aUZTLlsNU3Kj+vTrzRWPnCXrEmQ93DSbKLjIY+snqJvmVNjrzIveqmBKuNu0mtsq0mW0b
			48pd7/ZrEMDGnJxb2SSifvldK0w=
			</ds:X509Certificate>
			</ds:X509Data>
			</ds:KeyInfo>
        </saml2md:KeyDescriptor>
        <saml2md:KeyDescriptor use="encryption">
			<ds:KeyInfo>
			<ds:X509Data>
			<ds:X509Certificate>
			MIICSjCCAbMCBEAsOFQwDQYJKoZIhvcNAQEEBQAwbDELMAkGA1UEBhMCanAxDjAMBgNVBAgTBXRv
			a3lvMQ4wDAYDVQQHEwV0b2t5bzEMMAoGA1UEChMDbnR0MRQwEgYDVQQLEwtudHQgbGliZXJ0eTEZ
			MBcGA1UEAxMQZGVtby5saWJlcnR5Lm50dDAeFw0wNDAyMTMwMjM3MDhaFw0xNDAyMTAwMjM3MDha
			MGwxCzAJBgNVBAYTAmpwMQ4wDAYDVQQIEwV0b2t5bzEOMAwGA1UEBxMFdG9reW8xDDAKBgNVBAoT
			A250dDEUMBIGA1UECxMLbnR0IGxpYmVydHkxGTAXBgNVBAMTEGRlbW8ubGliZXJ0eS5udHQwgZ8w
			DQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBANk/0GylPFjvypHKmzl8x8h1ueiZX5PAducmvcv1ka4V
			CRz5gux6neRPfjtekHJ9egEHcGR298Ig5Y+hmew1bS3XO041UTptXV76MURUPkNwzMnkZHuzTjcV
			YCeHWt4z+zRlFlpszMy8OsPwIsR0avj+8ijxBIu1BTQC9hL3oaqLAgMBAAEwDQYJKoZIhvcNAQEE
			BQADgYEADsUS/VTEE/LjDO2L8VSWS8WOP6W02wwjtVOmqmdF0OhoT6pnM/xZhwtDowxpWoNZfKrg
			0H9pkY2O66GPIbtqHBEFNC6L70oVhb6nbfR6Vzcjo+uNUqxdrZ8YWbKCf6P0ZILLWfyhzx/MzoTN
			ZQM/YKknHX4XAenZ9WEDPnYQLnI=
			</ds:X509Certificate>
			</ds:X509Data>
			</ds:KeyInfo>
        </saml2md:KeyDescriptor>

        <saml2md:AuthnQueryService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
            Location="https://training-e-sett-otp2.ffrontier.com/idp/authnq_soap_authority"/>

    </saml2md:AuthnAuthorityDescriptor>
    -->

</saml2md:EntityDescriptor>
<EntityDescriptor ID="SM1de0ad09345f4f4908792773ec" entityID="ANAD">
	<IDPSSODescriptor ID="S1072f66c96f5c2ae533297bfcf23c6de76f67" protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol" WantAuthnRequestsSigned="false">
		<KeyDescriptor use="signing">
			<ns1:KeyInfo Id="e1d0bc63203a265cf1bf" xmlns:ns1="http://www.w3.org/2000/09/xmldsig#">
				<ns1:X509Data>
					<ns1:X509IssuerSerial>
						<ns1:X509IssuerName>CN=anadolubank a.s.,OU=IT,O=anadolubank a.s.,C=TR</ns1:X509IssuerName>
					</ns1:X509IssuerSerial>
					<ns1:X509Certificate>
						MIIDmTCCAoGgAwIBAgIEKuS4HTANBgkqhkiG9w0BAQsFADB9MQswCQYDVQQGEwJUUjEPMA0GA1UE
						CBMGdHVya2V5MRowGAYDVQQHExF1bXJhbml5ZSBpc3RhbmJ1bDEZMBcGA1UEChMQYW5hZG9sdWJh
						bmsgYS5zLjELMAkGA1UECxMCSVQxGTAXBgNVBAMTEGFuYWRvbHViYW5rIGEucy4wHhcNMTcxMTI3
						MDkxMTA4WhcNMzcxMTIyMDkxMTA4WjB9MQswCQYDVQQGEwJUUjEPMA0GA1UECBMGdHVya2V5MRow
						GAYDVQQHExF1bXJhbml5ZSBpc3RhbmJ1bDEZMBcGA1UEChMQYW5hZG9sdWJhbmsgYS5zLjELMAkG
						A1UECxMCSVQxGTAXBgNVBAMTEGFuYWRvbHViYW5rIGEucy4wggEiMA0GCSqGSIb3DQEBAQUAA4IB
						DwAwggEKAoIBAQCr3M3OuwCBQI0kyHxAy3IJk7D6qs5JyN6CqoFus5oQJPiyRtk8Urj2pGfME6Yq
						OAHft7c4ID2xnkV9dIe97Ubcl1arnFu7chh+zaOp6rFSJaaNhefG1OcXIRB//+VOoAf4f1i6Cxzl
						obxgPnq/XBYYJrca9t7N0IskzrIuEOV9iqNC61GNl1Nfw1YmcK2qT4wZ4lcpKjv/hMiiV23Crm6X
						DjIqvYDnLRZ1KKcA9mTaMPVNXmJ9W92l4AcoKBbZ1PuMSyPXUNtV3sDVnbUWFaAkNfIFsfw1k1GA
						XXxXE8OC5Z0suf4MyR8Q7mZNcrccskSxdGr2/jc1ypEzjh+qOUJNAgMBAAGjITAfMB0GA1UdDgQW
						BBSD6cadI6rKjiEP0a/qd92FTKK19TANBgkqhkiG9w0BAQsFAAOCAQEABQCXXT4KD0mQDRbbAN3j
						3RKlaZ24ce48ljoslG/+wJNEEcuw5xXOcR1zODJC9eBaUPjLF3PWXNKSMUZgx68nRS2SkRS3Jlpi
						EkgL449+x/MD48KdMpECPEBCRWn5OjKwRuqfCwSb7ToG8o64jaCNoJNCf/MmRQFKKIEPBwBO+yOM
						NBkoYs5eZIU+CUbo7WNP+3L/f31IDSZmQkEMk044N9WnhQDTU8H2VxZOwBtjKuPV24iSbiFNWedy
						UZKeAHxwB31TfTSYJIc528eu2QyRkJ78I3Oq3iXo1WbJTES74ZIa9qbXBwe+tIv9xBOlQiSGzi8M
						YzI6bL8BH9aboAZ6WA==
					</ns1:X509Certificate>
					<ns1:X509SubjectName>CN=anadolubank a.s.,OU=IT,O=anadolubank a.s.,C=TR</ns1:X509SubjectName>
				</ns1:X509Data>
			</ns1:KeyInfo>
		</KeyDescriptor>
		<ArtifactResolutionService isDefault="false" index="0" Location="" Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"/>
		<SingleLogoutService Location="https://ibanktest.anadolubank.com.tr/samllogout" Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"/>
		<SingleSignOnService Location="" Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"/>
	</IDPSSODescriptor>
</EntityDescriptor>
<EntityDescriptor ID="SEKERBANKIDPEntityDescriptor" entityID="SEKERBANKIDP">
    <IDPSSODescriptor ID="SEKERBANKIDPIDPSSODescriptor" protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol" WantAuthnRequestsSigned="false">
        <KeyDescriptor use="signing">
            <ns1:KeyInfo Id="SEKERBANKIDPKeyInfo" xmlns:ns1="http://www.w3.org/2000/09/xmldsig#">
                <ns1:X509Data>
                    <ns1:X509IssuerSerial>
                        <ns1:X509IssuerName>CN=kodimzatest.cb.sekerbank.com.tr,OU=Information Technologies,O=Sekerbank T.A.S.,C=TR</ns1:X509IssuerName>
                    </ns1:X509IssuerSerial>
                    <ns1:X509Certificate>
                        MIIG6DCCBdCgAwIBAgITZAAAARcM1UDwNUGmyQAAAAABFzANBgkqhkiG9w0BAQsF
                        ADBwMRIwEAYKCZImiZPyLGQBGRYCdHIxEzARBgoJkiaJk/IsZAEZFgNjb20xGTAX
                        BgoJkiaJk/IsZAEZFglzZWtlcmJhbmsxEjAQBgoJkiaJk/IsZAEZFgJjYjEWMBQG
                        ****************************************************************
                        MzhaMIGZMQswCQYDVQQGEwJUUjERMA8GA1UECBMISXN0YW5idWwxDzANBgNVBAcT
                        BkxldmVudDEZMBcGA1UEChMQU2VrZXJiYW5rIFQuQS5TLjEhMB8GA1UECxMYSW5m
                        b3JtYXRpb24gVGVjaG5vbG9naWVzMSgwJgYDVQQDEx9rb2RpbXphdGVzdC5jYi5z
                        ZWtlcmJhbmsuY29tLnRyMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA
                        rIAWW8xchcgVq8BagkVfN3WFR3XSTBwXKCsPzUO8QqI8qiQNxKltipemXNGvPC4b
                        f+E1gksGLRCflKYVViQx8Hjj0Br6AkUt4s+EyyUTe4THQOhUpTx3DAL/a8LZhRe5
                        g+3AjApiQNImukC1r2EBvw69ZmHvReu9P/Za6h8rZVBCB7qcrsAHoZXAATnMIoEg
                        AB4YhXccPugHNCq5U0o106RNh2R17loErjRgbcfjh4s7O6DIXf6j6AgYE/0tMwyU
                        pcxSaAsKruAUWCoDOkMbqPGhsB2iDo40AHbvAu5p1EW4wxvl8yJ0paOmsyDMpW46
                        5DJJRWeji/qTrzTWRsTDlwIDAQABo4IDTzCCA0swHQYDVR0OBBYEFPq4s8d2dpTW
                        mJvcLkKvAwBMMFOVMB8GA1UdIwQYMBaAFGn0uO+sz9C2kMbZYqmcBICjEjhHMIIB
                        KwYDVR0fBIIBIjCCAR4wggEaoIIBFqCCARKGgchsZGFwOi8vL0NOPVNFS0VSU1VC
                        Q0EyNTYsQ049U0VLRVJTVUJDQTI1NixDTj1DRFAsQ049UHVibGljJTIwS2V5JTIw
                        U2VydmljZXMsQ049U2VydmljZXMsQ049Q29uZmlndXJhdGlvbixEQz1jYixEQz1z
                        ZWtlcmJhbmssREM9Y29tLERDPXRyP2NlcnRpZmljYXRlUmV2b2NhdGlvbkxpc3Q/
                        YmFzZT9vYmplY3RDbGFzcz1jUkxEaXN0cmlidXRpb25Qb2ludIZFaHR0cDovL1NF
                        S0VSU1VCQ0EyNTYuY2Iuc2VrZXJiYW5rLmNvbS50ci9DZXJ0RW5yb2xsL1NFS0VS
                        U1VCQ0EyNTYuY3JsMIIBRAYIKwYBBQUHAQEEggE2MIIBMjCBugYIKwYBBQUHMAKG
                        ga1sZGFwOi8vL0NOPVNFS0VSU1VCQ0EyNTYsQ049QUlBLENOPVB1YmxpYyUyMEtl
                        eSUyMFNlcnZpY2VzLENOPVNlcnZpY2VzLENOPUNvbmZpZ3VyYXRpb24sREM9Y2Is
                        REM9c2VrZXJiYW5rLERDPWNvbSxEQz10cj9jQUNlcnRpZmljYXRlP2Jhc2U/b2Jq
                        ZWN0Q2xhc3M9Y2VydGlmaWNhdGlvbkF1dGhvcml0eTBzBggrBgEFBQcwAoZnaHR0
                        cDovL1NFS0VSU1VCQ0EyNTYuY2Iuc2VrZXJiYW5rLmNvbS50ci9DZXJ0RW5yb2xs
                        L1NFS0VSU1VCQ0EyNTYuY2Iuc2VrZXJiYW5rLmNvbS50cl9TRUtFUlNVQkNBMjU2
                        LmNydDALBgNVHQ8EBAMCBaAwPQYJKwYBBAGCNxUHBDAwLgYmKwYBBAGCNxUIge2n
                        bIeXrGuCjYUshfuiUIPu1ih2h4aPE4eG6G8CAWUCAQAwHQYDVR0lBBYwFAYIKwYB
                        BQUHAwIGCCsGAQUFBwMBMCcGCSsGAQQBgjcVCgQaMBgwCgYIKwYBBQUHAwIwCgYI
                        KwYBBQUHAwEwDQYJKoZIhvcNAQELBQADggEBAKkrBYanyxEXBX5q9LFtYfdzAgxc
                        kAnckajmfuH2rKh8n/ZR2t+GYmLUyKatOcSksda/q/cfFSekBpKQdTZWaw2Tnr1k
                        6zgtapR2H1istTlVFaAIvY9DTK3wjnsxkPPy336jPGch4DsJ6uKuCV1kI4fcMv8K
                        GoB1QT8YooK9Xss061C8I69baRrQglupYxq6IvlRfxFzzi1exqpS28JGQrsQi5Ar
                        CCXQarhleyOvdlzFwj2CmM6TuZv9b+f/I8zs8gulkFDFkkoCovba9y4h4VEKMUwb
                        neyIBq+q7vcrqAqLVJmjL66896Ypgnzotlp/DHGWhPwurMwmUV09WxgFQr0=
                    </ns1:X509Certificate>
                    <ns1:X509SubjectName>CN=kodimzatest.cb.sekerbank.com.tr,OU=Information Technologies,O=Sekerbank T.A.S.,C=TR</ns1:X509SubjectName>
                </ns1:X509Data>
            </ns1:KeyInfo>
        </KeyDescriptor>
        <ArtifactResolutionService isDefault="false" index="0" Location="" Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"/>
        <SingleLogoutService Location="https://uat.fxinside.net/fxi/whitelabel/sekerkur/logout.jsp" Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"/>
        <SingleSignOnService Location="" Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"/>
    </IDPSSODescriptor>
</EntityDescriptor>
    <EntityDescriptor ID="ALTERNATIFBANKIDPEntityDescriptor" entityID="ALTERNATIFBANKIDP">
        <IDPSSODescriptor ID="ALTERNATIFBANKIDPIDPSSODescriptor" protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol" WantAuthnRequestsSigned="false">
            <KeyDescriptor use="signing">
                <ns1:KeyInfo Id="ALTERNATIFBANKIDPKeyInfo" xmlns:ns1="http://www.w3.org/2000/09/xmldsig#">
                    <ns1:X509Data>
                        <ns1:X509IssuerSerial>
                            <ns1:X509IssuerName>CN=testidp.alternatifbank.com.tr,OU=UygulamaYonetimi,O=Alternatifbank A.S.,C=TR</ns1:X509IssuerName>
                        </ns1:X509IssuerSerial>
                        <ns1:X509Certificate>
                            MIIDrzCCApcCFF+YBCAhsryhMRTQMNlh4MPPA8WRMA0GCSqGSIb3DQEBCwUAMIGT
                            MQswCQYDVQQGEwJUUjERMA8GA1UECAwISXN0YW5idWwxEDAOBgNVBAcMB1Nhcml5
                            ZXIxHDAaBgNVBAoME0FsdGVybmF0aWZiYW5rIEEuUy4xGTAXBgNVBAsMEFV5Z3Vs
                            YW1hWW9uZXRpbWkxJjAkBgNVBAMMHXRlc3RpZHAuYWx0ZXJuYXRpZmJhbmsuY29t
                            LnRyMB4XDTIwMDYyMjExMjIyN1oXDTQ1MDIyMDExMjIyN1owgZMxCzAJBgNVBAYT
                            AlRSMREwDwYDVQQIDAhJc3RhbmJ1bDEQMA4GA1UEBwwHU2FyaXllcjEcMBoGA1UE
                            CgwTQWx0ZXJuYXRpZmJhbmsgQS5TLjEZMBcGA1UECwwQVXlndWxhbWFZb25ldGlt
                            aTEmMCQGA1UEAwwddGVzdGlkcC5hbHRlcm5hdGlmYmFuay5jb20udHIwggEiMA0G
                            CSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDLzP1idWBJMLeUUlYGYZwOD7i0B9tu
                            sIIWD4B7Qyxq4nMAp5+hDwQexIcp3Vsne1mSZT3mVBXK4ZSbJXeRhIqOlZspfLK6
                            isX675JC4u3Jj8ZvmNHVzlUTspXkl+mOzJBKl6aIIc4AyvvQEJX3hpw2MaToB6p2
                            aKFQKgGJS1DCiOw2KLnyc48UXOztzjYHfTc36lHUrB/T8t8hqcgKbZRjvmBsEKFF
                            O6a8L4MyPHvINtO76Ktetl6K4TyXPikOkTpXeUyQi+7oxHCh/i+phZU/F24DKb+d
                            H8SpsgSXXSKwpLpux1JezwZNqFTRSLYQjxxWo8rLZlRu1Q/+iS+ONL4LAgMBAAEw
                            DQYJKoZIhvcNAQELBQADggEBAI+w1iw4LoI9laFRh8/+MjFHSnN4YmX1LBdugolo
                            uWE8NPjzYBU6Z7Fapx+Ss9qsL5samHRMhZg/FTofEAOkWBjxGHOfLG6TrNELW4qo
                            PjwypKCj2q1tO7mbJmrbDxqNMu2F/adxxVXb8113s6zVf9Ux39n30w2WpGC/pqMs
                            urkpDgfZ+Kw6ph+hvujUU6eTM3H517QLAjYan+Q3GkxNyUQIZvVrXIXILpzgXldQ
                            b4EFOte4oZjpmiboItRzIHIp5s6PpIaW9TUJyeGrYE6ZR4YmB4PKWygR3/Rvnj37
                            F8Zl+5svkKFmRAzQLpRNi7JFSKLHN2Y2E2YmSNptiaoCX0Y=
                        </ns1:X509Certificate>
                        <ns1:X509SubjectName>CN=testidp.alternatifbank.com.tr,OU=UygulamaYonetimi,O=Alternatifbank A.S.,C=TR</ns1:X509SubjectName>
                    </ns1:X509Data>
                </ns1:KeyInfo>
            </KeyDescriptor>
            <ArtifactResolutionService isDefault="false" index="0" Location="" Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"/>
            <SingleLogoutService Location="https://uat.fxinside.net/fxi/whitelabel/alternatif/logout.jsp" Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"/>
            <SingleSignOnService Location="" Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"/>
        </IDPSSODescriptor>
    </EntityDescriptor>
    <EntityDescriptor xmlns="urn:oasis:names:tc:SAML:2.0:metadata" ID="_b22b73e3-b195-4dee-b795-b700d3a5ff2e" entityID="https://sts.windows.net/88cd6b58-79db-4415-a2b5-ba5cb9d0e98e/">
        <Signature xmlns="http://www.w3.org/2000/09/xmldsig#">
            <SignedInfo>
                <CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
                <SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"/>
                <Reference URI="#_b22b73e3-b195-4dee-b795-b700d3a5ff2e">
                    <Transforms>
                        <Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/>
                        <Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
                    </Transforms>
                    <DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                    <DigestValue>mydA4k7wZUWNWpcqGyl3eYjK+RpzzK859P28qiIJ3Os=</DigestValue>
                </Reference>
            </SignedInfo>
            <SignatureValue>XLvRshR7IQRGmHO1uSeEXjNqxKnV3E/OSb+xGPzTA+FOaUZE9NfgJhzT1x3dfViu6ZfNuNJ/e02Vcitv8fzRChUTGyi9PwKOZOyEYji+u4hgHll5i3SlbOD7hcOVRiTbtZbVfpCpxy55GBOyFxhmxduf3Ol8DWdJb00CUqCGBrkJNXUcxUDetBXyeEeL7b1/K/cuwGYEsKLrpf2lLximn8lOV/YzZZ4yqJmF9h+6inHA1hlz/c7xjt/RA3/Zt9oDk5TWWOllamrUCnY3lPKOLfreEIZv8r9zIGo+nybThjOjF9AditeOUOPwcQOFJyNL9xW/bZ0gK0mZSDV/kH3vbg==</SignatureValue>
            <KeyInfo>
                <X509Data>
                    <X509Certificate>MIIC8DCCAdigAwIBAgIQJqa8mrj3gaVPgVjvHY6iKDANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQD
****************************************************************************
MDhaFw0yNjEwMDkxMzM5MDRaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQg
U1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5eN5Rpbadmk+
vbSwoiNwJoMCueaWwyv6TH0Znx6sdu8bn0Ue1dec95GfhTSaqSkGjqpYINn6DE2rV1/40F1A6ITe
Stx/H3Ya5vkAIncj6pQ8mpDMa5kAFebJ6BO/yqsUKpzJC3bxhMsh9SystOpp8+1pFqCF9tMLg4rt
bQhlglAvBCUrv+119m80hyKgKArCFK2xnduKSEbBMYDuQD3xGRpZGnO2bYWtLddPY+ugjrohg8tZ
AUjFVM08ZpwWRsGehbS35Ig9QPqiuO5JWvbmyHxgbp4ZNpuaPsKefqvqqr55ejDkXpAFPAFcEIlF
V8K0+quvgUOVy29QAnySEKNFNQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQDNpAalGalWURRnyEoU
djhq+4rTFKeZ9qzR3HWJ/w6+WYcx0/ha7LBmgz8ipbRSvCA+HtcbYypw7zaZX/DYAQMDHOo+6fw4
HdWY9mCBzIW8c6PxuqfNg641QXQit7/Dtl5mFdJ67HR7Iqt2WUJYAxciGGKIMfXX7GoCdhVgM5eJ
He4pt5rz7APIK1t+cpTdPRfVYz/9tqtdf7ySBCmV1jpjIJEeYv6azCqPLfRC40NOL7iwNeVbsQIF
Uv7FTQR3eT4I6uDUFFaZMiQOvLC0LeAxBHj8B6EECy5AXmzvKX82HQdal+M5cYylIu3tGOQUBq72
jHAhi7WF3rQZpkzgKCsP</X509Certificate>
                </X509Data>
            </KeyInfo>
        </Signature>
        <RoleDescriptor xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:fed="http://docs.oasis-open.org/wsfed/federation/200706" xsi:type="fed:SecurityTokenServiceType" protocolSupportEnumeration="http://docs.oasis-open.org/wsfed/federation/200706">
            <KeyDescriptor use="signing">
                <KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#">
                    <X509Data>
                        <X509Certificate>MIIC8DCCAdigAwIBAgIQJqa8mrj3gaVPgVjvHY6iKDANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQD
****************************************************************************
MDhaFw0yNjEwMDkxMzM5MDRaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQg
U1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5eN5Rpbadmk+
vbSwoiNwJoMCueaWwyv6TH0Znx6sdu8bn0Ue1dec95GfhTSaqSkGjqpYINn6DE2rV1/40F1A6ITe
Stx/H3Ya5vkAIncj6pQ8mpDMa5kAFebJ6BO/yqsUKpzJC3bxhMsh9SystOpp8+1pFqCF9tMLg4rt
bQhlglAvBCUrv+119m80hyKgKArCFK2xnduKSEbBMYDuQD3xGRpZGnO2bYWtLddPY+ugjrohg8tZ
AUjFVM08ZpwWRsGehbS35Ig9QPqiuO5JWvbmyHxgbp4ZNpuaPsKefqvqqr55ejDkXpAFPAFcEIlF
V8K0+quvgUOVy29QAnySEKNFNQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQDNpAalGalWURRnyEoU
djhq+4rTFKeZ9qzR3HWJ/w6+WYcx0/ha7LBmgz8ipbRSvCA+HtcbYypw7zaZX/DYAQMDHOo+6fw4
HdWY9mCBzIW8c6PxuqfNg641QXQit7/Dtl5mFdJ67HR7Iqt2WUJYAxciGGKIMfXX7GoCdhVgM5eJ
He4pt5rz7APIK1t+cpTdPRfVYz/9tqtdf7ySBCmV1jpjIJEeYv6azCqPLfRC40NOL7iwNeVbsQIF
Uv7FTQR3eT4I6uDUFFaZMiQOvLC0LeAxBHj8B6EECy5AXmzvKX82HQdal+M5cYylIu3tGOQUBq72
jHAhi7WF3rQZpkzgKCsP</X509Certificate>
                    </X509Data>
                </KeyInfo>
            </KeyDescriptor>
            <fed:ClaimTypesOffered>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name">
                    <auth:DisplayName>Name</auth:DisplayName>
                    <auth:Description>The mutable display name of the user.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier">
                    <auth:DisplayName>Subject</auth:DisplayName>
                    <auth:Description>An immutable, globally unique, non-reusable identifier of the user that is unique to the application for which a token is issued.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname">
                    <auth:DisplayName>Given Name</auth:DisplayName>
                    <auth:Description>First name of the user.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname">
                    <auth:DisplayName>Surname</auth:DisplayName>
                    <auth:Description>Last name of the user.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.microsoft.com/identity/claims/displayname">
                    <auth:DisplayName>Display Name</auth:DisplayName>
                    <auth:Description>Display name of the user.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.microsoft.com/identity/claims/nickname">
                    <auth:DisplayName>Nick Name</auth:DisplayName>
                    <auth:Description>Nick name of the user.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/authenticationinstant">
                    <auth:DisplayName>Authentication Instant</auth:DisplayName>
                    <auth:Description>The time (UTC) when the user is authenticated to Windows Azure Active Directory.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/authenticationmethod">
                    <auth:DisplayName>Authentication Method</auth:DisplayName>
                    <auth:Description>The method that Windows Azure Active Directory uses to authenticate users.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.microsoft.com/identity/claims/objectidentifier">
                    <auth:DisplayName>ObjectIdentifier</auth:DisplayName>
                    <auth:Description>Primary identifier for the user in the directory. Immutable, globally unique, non-reusable.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.microsoft.com/identity/claims/tenantid">
                    <auth:DisplayName>TenantId</auth:DisplayName>
                    <auth:Description>Identifier for the user's tenant.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.microsoft.com/identity/claims/identityprovider">
                    <auth:DisplayName>IdentityProvider</auth:DisplayName>
                    <auth:Description>Identity provider for the user.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress">
                    <auth:DisplayName>Email</auth:DisplayName>
                    <auth:Description>Email address of the user.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/groups">
                    <auth:DisplayName>Groups</auth:DisplayName>
                    <auth:Description>Groups of the user.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.microsoft.com/identity/claims/accesstoken">
                    <auth:DisplayName>External Access Token</auth:DisplayName>
                    <auth:Description>Access token issued by external identity provider.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/expiration">
                    <auth:DisplayName>External Access Token Expiration</auth:DisplayName>
                    <auth:Description>UTC expiration time of access token issued by external identity provider.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.microsoft.com/identity/claims/openid2_id">
                    <auth:DisplayName>External OpenID 2.0 Identifier</auth:DisplayName>
                    <auth:Description>OpenID 2.0 identifier issued by external identity provider.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.microsoft.com/claims/groups.link">
                    <auth:DisplayName>GroupsOverageClaim</auth:DisplayName>
                    <auth:Description>Issued when number of user's group claims exceeds return limit.</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/role">
                    <auth:DisplayName>Role Claim</auth:DisplayName>
                    <auth:Description>Roles that the user or Service Principal is attached to</auth:Description>
                </auth:ClaimType>
                <auth:ClaimType xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706" Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/wids">
                    <auth:DisplayName>RoleTemplate Id Claim</auth:DisplayName>
                    <auth:Description>Role template id of the Built-in Directory Roles that the user is a member of</auth:Description>
                </auth:ClaimType>
            </fed:ClaimTypesOffered>
            <fed:SecurityTokenServiceEndpoint>
                <wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing">
                    <wsa:Address>https://login.microsoftonline.com/88cd6b58-79db-4415-a2b5-ba5cb9d0e98e/wsfed</wsa:Address>
                </wsa:EndpointReference>
            </fed:SecurityTokenServiceEndpoint>
            <fed:PassiveRequestorEndpoint>
                <wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing">
                    <wsa:Address>https://login.microsoftonline.com/88cd6b58-79db-4415-a2b5-ba5cb9d0e98e/wsfed</wsa:Address>
                </wsa:EndpointReference>
            </fed:PassiveRequestorEndpoint>
        </RoleDescriptor>
        <RoleDescriptor xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:fed="http://docs.oasis-open.org/wsfed/federation/200706" xsi:type="fed:ApplicationServiceType" protocolSupportEnumeration="http://docs.oasis-open.org/wsfed/federation/200706">
            <KeyDescriptor use="signing">
                <KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#">
                    <X509Data>
                        <X509Certificate>MIIC8DCCAdigAwIBAgIQH5qMbJ14KahCZamb78s7VDANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yMDA5MTYxNTI5MzVaFw0yMzA5MTYxNTI5MzVaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq0tb7VWHT/gvAQibbhF+l8e3vFuzeIpA0jGK0Urr1rgWtWirodp5nZ68tT/C29b5/P4OvhIy7avGktX0luKMWA77qMhymFGDfnvwVxNwl2Iz1Hl3DS1f8L1nF3WnxdC++B+QjHF4daJg11SBWyxMxFyhhW0yoOrVFSMwQvTao2qtrznBPpdxuPd5cDQeJyehSk73mluKOSZcWJBVItQT8KL4gIu7+PmSNhGqpqUNgu3PCM8uRS+xdj23hAHp3Ak/XG/OqvERovJFnbQ0ZgaW2VmD9nFEtM+l/hVgs5JA9mH3qMxTRYsfj/NxM1EY/GUlVEL7t1AABiIIsapIv5KLeQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQAhZ4o7JVCIDvD4d3otuynyB/gq4W2byOr+gF+yWCVQNSAvaanQsIS45cASUDPcpoX0Xh76Xwaj3roL2Ufw+8F2jeSzAzFg7vDVAzzko7uIg0CCuZug6Fg+4GO42g5qqRamMb9A/lTPg/2DP6LR8s6dnZLCwY0AcOMoWUo+yPT3syhwYLct4epAEELPqBwFbq/vFCqhAkGXzT+0Va4tz3sHUSb+5IpdrberTKuRcM5ap3BsvkGYIuiazJV9EiWveNgyEidHXaoIWbM58EfUYcIw94uPZUxBmyunKmP2fg79QPikME3SBTgirwpIRU51xPJIhCya5Pe1IZrNgv2Mj7PL</X509Certificate>
                    </X509Data>
                </KeyInfo>
            </KeyDescriptor>
            <fed:TargetScopes>
                <wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing">
                    <wsa:Address>https://sts.windows.net/88cd6b58-79db-4415-a2b5-ba5cb9d0e98e/</wsa:Address>
                </wsa:EndpointReference>
            </fed:TargetScopes>
            <fed:ApplicationServiceEndpoint>
                <wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing">
                    <wsa:Address>https://login.microsoftonline.com/88cd6b58-79db-4415-a2b5-ba5cb9d0e98e/wsfed</wsa:Address>
                </wsa:EndpointReference>
            </fed:ApplicationServiceEndpoint>
            <fed:PassiveRequestorEndpoint>
                <wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing">
                    <wsa:Address>https://login.microsoftonline.com/88cd6b58-79db-4415-a2b5-ba5cb9d0e98e/wsfed</wsa:Address>
                </wsa:EndpointReference>
            </fed:PassiveRequestorEndpoint>
        </RoleDescriptor>
        <IDPSSODescriptor protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
            <KeyDescriptor use="signing">
                <KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#">
                    <X509Data>
                        <X509Certificate>MIIC8DCCAdigAwIBAgIQH5qMbJ14KahCZamb78s7VDANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yMDA5MTYxNTI5MzVaFw0yMzA5MTYxNTI5MzVaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq0tb7VWHT/gvAQibbhF+l8e3vFuzeIpA0jGK0Urr1rgWtWirodp5nZ68tT/C29b5/P4OvhIy7avGktX0luKMWA77qMhymFGDfnvwVxNwl2Iz1Hl3DS1f8L1nF3WnxdC++B+QjHF4daJg11SBWyxMxFyhhW0yoOrVFSMwQvTao2qtrznBPpdxuPd5cDQeJyehSk73mluKOSZcWJBVItQT8KL4gIu7+PmSNhGqpqUNgu3PCM8uRS+xdj23hAHp3Ak/XG/OqvERovJFnbQ0ZgaW2VmD9nFEtM+l/hVgs5JA9mH3qMxTRYsfj/NxM1EY/GUlVEL7t1AABiIIsapIv5KLeQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQAhZ4o7JVCIDvD4d3otuynyB/gq4W2byOr+gF+yWCVQNSAvaanQsIS45cASUDPcpoX0Xh76Xwaj3roL2Ufw+8F2jeSzAzFg7vDVAzzko7uIg0CCuZug6Fg+4GO42g5qqRamMb9A/lTPg/2DP6LR8s6dnZLCwY0AcOMoWUo+yPT3syhwYLct4epAEELPqBwFbq/vFCqhAkGXzT+0Va4tz3sHUSb+5IpdrberTKuRcM5ap3BsvkGYIuiazJV9EiWveNgyEidHXaoIWbM58EfUYcIw94uPZUxBmyunKmP2fg79QPikME3SBTgirwpIRU51xPJIhCya5Pe1IZrNgv2Mj7PL</X509Certificate>
                    </X509Data>
                </KeyInfo>
            </KeyDescriptor>
            <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="https://login.microsoftonline.com/88cd6b58-79db-4415-a2b5-ba5cb9d0e98e/saml2"/>
            <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="https://login.microsoftonline.com/88cd6b58-79db-4415-a2b5-ba5cb9d0e98e/saml2"/>
            <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST" Location="https://login.microsoftonline.com/88cd6b58-79db-4415-a2b5-ba5cb9d0e98e/saml2"/>
        </IDPSSODescriptor>
    </EntityDescriptor>
	
    <EntityDescriptor ID="HapoalimHAPIBANKIDPEntityDescriptor" entityID="poalimbiz-web-idp">
        <IDPSSODescriptor ID="HapoalimHAPIBANKIDPIDPSSODescriptor" protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol" WantAuthnRequestsSigned="false">
            <KeyDescriptor use="signing">
                <ns1:KeyInfo Id="HapoalimHAPIBANKIDPKeyInfo" xmlns:ns1="http://www.w3.org/2000/09/xmldsig#">
                    <ns1:X509Data>
                        <ns1:X509IssuerSerial>
                            <ns1:X509IssuerName>
C=IL,ST=TA,L=Tel Aviv,O=Bank Hapoalim,OU=Servers Certificates,CN=biz2pre.bankhapoalim.co.il</ns1:X509IssuerName>
                        </ns1:X509IssuerSerial>
                        <ns1:X509Certificate>
MIIJBzCCBu+gAwIBAgITaAAAC0PW33WYyVvMHAAAAAALQzANBgkqhkiG9w0BAQsF
ADBNMQswCQYDVQQGEwJJTDEWMBQGA1UEChMNQmFuayBIYXBvYWxpbTEmMCQGA1UE
****************************************************************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                         
                        </ns1:X509Certificate>
                        <ns1:X509SubjectName>
					C=IL,ST=TA,L=Tel Aviv,O=Bank Hapoalim,OU=Servers Certificates,CN=biz2pre.bankhapoalim.co.il</ns1:X509SubjectName>
                    </ns1:X509Data>
                </ns1:KeyInfo>
            </KeyDescriptor>
            <ArtifactResolutionService isDefault="false" index="0" Location="" Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"/>
            <SingleLogoutService Location="https://uat.fxinside.net/fxi/whitelabel/hapi/logout.jsp" Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"/>
            <SingleSignOnService Location="" Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"/>
        </IDPSSODescriptor>
    </EntityDescriptor>
    <EntityDescriptor ID="FRBIDPEntityDescriptor" entityID="frbnp3.com">
        <IDPSSODescriptor ID="FRBIDPIDPSSODescriptor" protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol" WantAuthnRequestsSigned="false">
            <KeyDescriptor use="signing">
                <ns1:KeyInfo Id="FRBIDPKeyInfo" xmlns:ns1="http://www.w3.org/2000/09/xmldsig#">
                    <ns1:X509Data>
                        <ns1:X509IssuerSerial>
                            <ns1:X509IssuerName>
                                C=IL,ST=TA,L=Tel Aviv,O=Bank FRB,OU=Servers Certificates,CN=signing.websso.firstrepublic.com</ns1:X509IssuerName>
                        </ns1:X509IssuerSerial>
                        <ns1:X509Certificate>
                            MIIGkzCCBXugAwIBAgIQD5Nv9jts67ZGMKCfgP6urzANBgkqhkiG9w0BAQsFADBeMQswCQYDVQQG
                            EwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3d3cuZGlnaWNlcnQuY29tMR0w
                            ****************************************************************************
                            NTlaMIGDMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNU2FuIEZy
                            YW5jaXNjbzEcMBoGA1UEChMTRmlyc3QgUmVwdWJsaWMgQmFuazEpMCcGA1UEAxMgc2lnbmluZy53
                            ZWJzc28uZmlyc3RyZXB1YmxpYy5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC8
                            5B4tqIPgtJo9jM0NLeFNjjOwnzP0ThcahLNHO9Z5fZMQggUZb7vsj8rEevQyLacw7rQh+D1Evpan
                            pnLKixoVWOHtv7ExLWuGHGPi9fQZDP2so9j48kGma13GeOgGzK5jHOan2yR2HIhMlU3LL18UogJ1
                            C6wmxoUOhZexQYTNICOZpTN3zKP/ARXqTVJmL+0phwfOLgtedrweDkNndLrCeKRQ3i9FsDGhNDWp
                            +oBQQIt+OZzt3eCgeN2exe2+OT0uoLCdit0UNfgjIgAowA9lHevOzubI6nfESYmQOeIqhviE0ILy
                            AEf9XIvfU5k/I8A9702ZCeavkST2aDj+kvWxAgMBAAGjggMlMIIDITAfBgNVHSMEGDAWgBSQWP+w
                            nHWoUVR3se3yo0MWOJ5sxTAdBgNVHQ4EFgQUbMUNYBoaJKw7s+jYs538E9wty9QwKwYDVR0RBCQw
                            IoIgc2lnbmluZy53ZWJzc28uZmlyc3RyZXB1YmxpYy5jb20wDgYDVR0PAQH/BAQDAgWgMB0GA1Ud
                            JQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjA+BgNVHR8ENzA1MDOgMaAvhi1odHRwOi8vY2RwLmdl
                            b3RydXN0LmNvbS9HZW9UcnVzdFJTQUNBMjAxOC5jcmwwPgYDVR0gBDcwNTAzBgZngQwBAgIwKTAn
                            BggrBgEFBQcCARYbaHR0cDovL3d3dy5kaWdpY2VydC5jb20vQ1BTMHUGCCsGAQUFBwEBBGkwZzAm
                            BggrBgEFBQcwAYYaaHR0cDovL3N0YXR1cy5nZW90cnVzdC5jb20wPQYIKwYBBQUHMAKGMWh0dHA6
                            Ly9jYWNlcnRzLmdlb3RydXN0LmNvbS9HZW9UcnVzdFJTQUNBMjAxOC5jcnQwCQYDVR0TBAIwADCC
                            AX8GCisGAQQB1nkCBAIEggFvBIIBawFpAHYA6D7Q2j71BjUy51covIlryQPTy9ERa+zraeF3fW0G
                            vW4AAAGDE6s4ggAABAMARzBFAiEAn0j1/DmR2vYBqJvJkwwPcGG1QaXG8OIbmq9ScrleTI8CIBhX
                            HONl2jwtH84oLeV1I+9BCvgKmoD0n2tKMyukDhkJAHcANc8ZG7+xbFe/D61MbULLu7YnICZR6j/h
                            Ku+oA8M71kwAAAGDE6s4ZgAABAMASDBGAiEApJdN8L/EzaNvLnqkipUd7yBUxwvPPqZyTs6OKhL/
                            aPACIQCMUzVZ5GwaKN5i5+kLj/bFXt+23eUgcIN55XJqNvWdEgB2ALNzdwfhhFD4Y4bWBancEQlK
                            eS2xZwwLh9zwAw55NqWaAAABgxOrOKAAAAQDAEcwRQIhAMcPwIC5sc1eKlw/n+HThE9jGsGeOcE3
                            gvo57RUic4eKAiAZvzkVzUPOqHi9B8N/21AEOpbuOuaAqU4fXWtPOWixKjANBgkqhkiG9w0BAQsF
                            AAOCAQEAU1/LMdAwq7E1QIpEtnXUZR6BewdrdrC1wGpSjLreWSVRaVXunTn1KIhY168qpiuqChnE
                            eG2c66A6oc1a5THIt/8ZESDzw83IGKATMOLlR+VwaARa76Nrru21V7k5RB3NEodrb4gIVvhne7r1
                            aY423bpiVMD1jR9R00ruQSyJ02M8dGwbDAaotIlEUjGOP2V8a5CVHH/skUyQ+vBerwTPglK83j0g
                            8YFJ4va+5sarg6Y3DB1LBlU3mNk8NqoyzrPgMz9cHCkBIhB3wNiGomibRUembebcO9rJyi2WmI/1
                            RsafxRv/kxVoLXk2J54RS/fTZgVSax2nCLT5hNizIF/TkQ==
                        </ns1:X509Certificate>
                        <ns1:X509SubjectName>
                            C=IL,ST=TA,L=Tel Aviv,O=Bank FRB,OU=Servers Certificates,CN=signing.websso.firstrepublic.com</ns1:X509SubjectName>
                    </ns1:X509Data>
                </ns1:KeyInfo>
            </KeyDescriptor>
            <ArtifactResolutionService isDefault="false" index="0" Location="" Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"/>
            <SingleLogoutService Location="https://uat.fxinside.net/fxi/whitelabel/frb/logout.jsp" Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"/>
            <SingleSignOnService Location="" Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"/>
        </IDPSSODescriptor>
    </EntityDescriptor>

	
</EntitiesDescriptor>
