IDC.IS.MESSAGE.LOGF4J.CATEGORY=com.integral.alert.message
IDC.IS.DATA.LOGF4J.CATEGORY=com.integral.alert.data


####### Generic Platform Error Codes Start ##############

IDC.IS.ALERT.APPLICATION.SERVER.STARTUP.FAILED=Application server startup failed. All required services not started successfully. Check application logs. Restart required.
IDC.IS.ALERT.APPLICATION.SERVER.STARTUP.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.APPLICATION.SERVER.STARTUP.FAILED.ERROR.CODE=PLT-XX-001

IDC.IS.ALERT.TXNIDGENERATION=1) Check Database 2) Check if database sequence IDCFXITXNID_IS_SEQ exists.
IDC.IS.ALERT.TXNIDGENERATION.SEVERITY.LEVEL=3
IDC.IS.ALERT.TXNIDGENERATION.ERROR.CODE=PLT-XX-002

IDC.IS.ALERT.JMSListener=One or more JMS listener has failed to establish
IDC.IS.ALERT.JMSListener.SEVERITY.LEVEL=3
IDC.IS.ALERT.JMSListener.ERROR.CODE=PLT-XX-003

IDC.IS.ALERT.RDS.Client.Service.Unavailable=1) RDS client service is not initialized, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RDS.Client.Service.Unavailable.SEVERITY.LEVEL=3
IDC.IS.ALERT.RDS.Client.Service.Unavailable.ERROR.CODE=PLT-XX-004

IDC.IS.ALERT.MARKET.RATE.DESERIALIZATION=Check the Server Log for Exceptions
IDC.IS.ALERT.MARKET.RATE.DESERIALIZATION.SEVERITY.LEVEL=1

IDC.IS.ALERT.Trade.DontKnowTrade=Customer rejected the Trade verification for the Trade. Business Support should check the trade with the customer.
IDC.IS.ALERT.Trade.DontKnowTrade.SEVERITY.LEVEL=3
IDC.IS.ALERT.Trade.DontKnowTrade.ERROR.CODE=PLT-XX-005

IDC.IS.ALERT.Send.Email.Notification.Failed=1) Contact Engineering
IDC.IS.ALERT.Send.Email.Notification.Failed.SEVERITY.LEVEL=3
IDC.IS.ALERT.Send.Email.Notification.Failed.ERROR.CODE=PLT-XX-006

IDC.IS.ALERT.ERR0044=1)Read the attachment file.2)If required take help from infra team to check mail server status.
IDC.IS.ALERT.ERR0044.SEVERITY.LEVEL=3
IDC.IS.ALERT.ERR0044.ERROR.CODE=PLT-XX-007

IDC.IS.ALERT.NOTIFICATION_PREPROCESS_FAILED=Notification of message failed. This will impact some applications like GM,STP,MIS
IDC.IS.ALERT.NOTIFICATION_PREPROCESS_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.NOTIFICATION_PREPROCESS_FAILED.ERROR.CODE=PLT-XX-008

IDC.IS.ALERT.Circuit.Breaker.Triggered=This alert is seen because Alerting action is configured, to be executed at the time of circuit break.
IDC.IS.ALERT.Circuit.Breaker.Triggered.SEVERITY.LEVEL=1
IDC.IS.ALERT.Circuit.Breaker.Triggered.ERROR.CODE=PLT-XX-009

IDC.IS.ALERT.JMS.GenericError=Check if queue or topic is missing from configuration. If yes then create one. If issue persist then contact engineering.
IDC.IS.ALERT.JMS.GenericError.SEVERITY.LEVEL=3
IDC.IS.ALERT.JMS.GenericError.ERROR.CODE=PLT-XX-010

IDC.IS.ALERT.ThreadPool.NotificationListenerRejection=1) NotificationListener Thread pool capacity reached. Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.ThreadPool.NotificationListenerRejection.SEVERITY.LEVEL=1
IDC.IS.ALERT.ThreadPool.NotificationListenerRejection.ERROR.CODE=PLT-XX-011

IDC.IS.ALERT.ERR0015=1)Please escalate to Engineering with the exact error found in integral.out.2)Take thread dump too.
IDC.IS.ALERT.ERR0015.SEVERITY.LEVEL=2
IDC.IS.ALERT.ERR0015.ERROR.CODE=PLT-XX-012

IDC.IS.ALERT.ERR0020=You need to escalate this asap to a DBA. Some database commits are taking more time than usual
IDC.IS.ALERT.ERR0020.SEVERITY.LEVEL=2
IDC.IS.ALERT.ERR0020.ERROR.CODE=PLT-XX-013

IDC.IS.ALERT.ERR0045=Inform BS to co-ordinate with Customer and Provider.
IDC.IS.ALERT.ERR0045.SEVERITY.LEVEL=2
IDC.IS.ALERT.ERR0045.ERROR.CODE=PLT-XX-014

IDC.IS.ALERT.Adaptor.Unknown.Trade.Response=1) Contact Engineering
IDC.IS.ALERT.Adaptor.Unknown.Trade.Response.SEVERITY.LEVEL=2
IDC.IS.ALERT.Adaptor.Unknown.Trade.Response.ERROR.CODE=PLT-XX-015

IDC.IS.ALERT.QuickFIX.Message.ParseException=1) Contact FIX customer sending this message.
IDC.IS.ALERT.QuickFIX.Message.ParseException.SEVERITY.LEVEL=1
IDC.IS.ALERT.QuickFIX.Message.ParseException.ERROR.CODE=PLT-XX-016

IDC.IS.ALERT.ESP.TRADE.RESPONSE.DROPPED="Trade Response is dropped by the server. There may be a RISK POSITION. The log message describes the risk."
IDC.IS.ALERT.ESP.TRADE.RESPONSE.DROPPED.SEVERITY.LEVEL=1
IDC.IS.ALERT.ESP.TRADE.RESPONSE.DROPPED.ERROR.CODE=PLT-XX-017

IDC.IS.ALERT.FIX.USER.SESSION.DISCONNECT=Contact Customer
IDC.IS.ALERT.FIX.USER.SESSION.DISCONNECT.SEVERITY.LEVEL=1
IDC.IS.ALERT.FIX.USER.SESSION.DISCONNECT.ERROR.CODE=PLT-XX-018

IDC.IS.ALERT.IS.Value.Date.Mismatch=Value date mismatch between provider and our system. Talk to customer/provider.
IDC.IS.ALERT.IS.Value.Date.Mismatch.SEVERITY.LEVEL=3
IDC.IS.ALERT.IS.Value.Date.Mismatch.ERROR.CODE=PLT-XX-019

IDC.IS.ALERT.IS.TRADINGCHANNEL.NOTSUPPORTED=Configure the Trading Channel
IDC.IS.ALERT.IS.TRADINGCHANNEL.NOTSUPPORTED.SEVERITY.LEVEL=1

####### BEGIN: ORACLE ERROR ################################################

IDC.IS.ALERT.Database.Persistence=Oracle Query or Insert/Upate Failed
IDC.IS.ALERT.Database.Persistence.SEVERITY.LEVEL=3
IDC.IS.ALERT.Database.Persistence.ERROR.CODE=TLNK-ORA-001

#######  END: ORACLE ERROR ################################################

#######  BEGIN: Scheduler ERROR ################################################

IDC.IS.ALERT.AutoRoll.MIS.NoHeaders=Auto-Roll failed to get Position from MIS portal.
IDC.IS.ALERT.AutoRoll.MIS.NoHeaders.SEVERITY.LEVEL=3
IDC.IS.ALERT.AutoRoll.MIS.NoHeaders.ERROR.CODE=SCH-MIS-001

IDC.IS.ALERT.AutoRoll.MIS.Connect=MIS portal is unreachable while execution Auto-Roll.
IDC.IS.ALERT.AutoRoll.MIS.Connect.SEVERITY.LEVEL=3
IDC.IS.ALERT.AutoRoll.MIS.Connect.ERROR.CODE=SCH-MIS-002

IDC.IS.ALERT.AUTOROLL.SCHEDULER.TASK.EXECUTE=Auto-Roll failed to create the Trades.
IDC.IS.ALERT.AUTOROLL.SCHEDULER.TASK.EXECUTE.SEVERITY.LEVEL=2
IDC.IS.ALERT.AUTOROLL.SCHEDULER.TASK.EXECUTE.ERROR.CODE=SCH-AR-001

IDC.IS.ALERT.AUTO.NETTING.SCHEDULER.TASK.EXECUTE=Auto netting failed with an exception.
IDC.IS.ALERT.AUTO.NETTING.SCHEDULER.TASK.EXECUTE.SEVERITY.LEVEL=2
IDC.IS.ALERT.AUTO.NETTING.SCHEDULER.TASK.EXECUTE.ERROR.CODE=SCH-AN-001


#######  END: Scheduler ERROR ################################################



####### Generic Platform Error Codes End ##############

####### Spaces Error Codes Start ##############

IDC.IS.ALERT.MONGO.PERSISTENCE.RFS=1)Check availability of Mongo RFS cluster. 2) Ensure that majority nodes are up. 3) Check if rollback files are created 
IDC.IS.ALERT.MONGO.PERSISTENCE.RFS.SEVERITY.LEVEL=3
IDC.IS.ALERT.MONGO.PERSISTENCE.RFS.ERROR.CODE=MSP-XX-001

IDC.IS.ALERT.MONGO.PERSISTENCE.SPACES=1)Check availability of Mongo Spaces cluster. 2) Check if rollback files are created. 3) It may be an application bug
IDC.IS.ALERT.MONGO.PERSISTENCE.SPACES.SEVERITY.LEVEL=3
IDC.IS.ALERT.MONGO.PERSISTENCE.SPACES.ERROR.CODE=MSP-XX-002

IDC.IS.ALERT.SPACES.SERIALIZATION=1)Unable to serialize message. It is an application bug. Could cause persistence issues.
IDC.IS.ALERT.SPACES.SERIALIZATION.SEVERITY.LEVEL=3
IDC.IS.ALERT.SPACES.SERIALIZATION.ERROR.CODE=MSP-XX-003

IDC.IS.ALERT.SPACES.DESERIALIZATION=1)Unable to Deserialize message. Could cause wrong data to be loaded from DB.
IDC.IS.ALERT.SPACES.DESERIALIZATION.SEVERITY.LEVEL=3
IDC.IS.ALERT.SPACES.DESERIALIZATION.ERROR.CODE=MSP-XX-004

####### Spaces Error Codes End ##############

####### BEGIN:STP Error Codes Start ##############

IDC.IS.ALERT.STP.Download.Send.Failure=Failed to send Trade download STP.
IDC.IS.ALERT.STP.Download.Send.Failure.SEVERITY.LEVEL=3
IDC.IS.ALERT.STP.Download.Send.Failure.ERROR.CODE=STP-XX-001

IDC.IS.ALERT.STP.Download.Build.Failure=Failure to Build Trade download STP Message.
IDC.IS.ALERT.STP.Download.Build.Failure.SEVERITY.LEVEL=3
IDC.IS.ALERT.STP.Download.Build.Failure.ERROR.CODE=STP-XX-002

IDC.IS.ALERT.STP.On.Confirm.Failure=Failed to send Trade download STP.
IDC.IS.ALERT.STP.On.Confirm.Failure.SEVERITY.LEVEL=3
IDC.IS.ALERT.STP.On.Confirm.Failure.ERROR.CODE=STP-XX-003

IDC.IS.ALERT.Client.Ack.Timeout=No Acknowledgement from Client.
IDC.IS.ALERT.Client.Ack.Timeout.SEVERITY.LEVEL=3
IDC.IS.ALERT.Client.Ack.Timeout.ERROR.CODE=STP-FIX-002

IDC.IS.ALERT.STP=Check availability of Sonic Server where JMS messages are send
IDC.IS.ALERT.STP.SEVERITY.LEVEL=3
IDC.IS.ALERT.STP.ERROR.CODE=STP-XX-004

IDC.IS.ALERT.Fix.STP.Session.NotFound=Failed to send FIX STP message as FIX User Session is not found.
IDC.IS.ALERT.Fix.STP.Session.NotFound.SEVERITY.LEVEL=3
IDC.IS.ALERT.Fix.STP.Session.NotFound.ERROR.CODE=STP-FIX-001

IDC.IS.ALERT.TRADEDOWNLOAD=1)Check availability of Sonic Server where JMS messages are send. 2) check for TradeService deployment. 
IDC.IS.ALERT.TRADEDOWNLOAD.SEVERITY.LEVEL=3
IDC.IS.ALERT.TRADEDOWNLOAD.ERROR.CODE=STP-XX-005

IDC.IS.ALERT.STP.OverflowExecutorUsed=1) STP sender thread pool exhausted. Overflow executor thread pool used. 2) Check that the STP is sent successfully. If not STP resend may be required. The Trade details would be part of the alert message.
IDC.IS.ALERT.STP.OverflowExecutorUsed.SEVERITY.LEVEL=3
IDC.IS.ALERT.STP.OverflowExecutorUsed.ERROR.CODE=STP-XX-006

####### END:STP Error Codes End ##############

####### BEGIN: MultiApp Message Handling ##############

IDC.IS.ALERT.MultiApp.RefData.Thread.Reject=Failure to handle Multi App Message due to Capacity Issue (Thread Pool Exhaustion).
IDC.IS.ALERT.MultiApp.RefData.Thread.Reject.SEVERITY.LEVEL=2
IDC.IS.ALERT.MultiApp.RefData.Thread.Reject.ERROR.CODE=REF-NOTI-001

####### END: MultiApp Message Handling #############

####### BEGIN: Credit Error Codes ##############

IDC.IS.ALERT.CREDIT.ENDOFDAY.BREACH.ALERT=Credit breach during end of day/week process.
IDC.IS.ALERT.CREDIT.ENDOFDAY.BREACH.ALERT.SEVERITY.LEVEL=2
IDC.IS.ALERT.CREDIT.ENDOFDAY.BREACH.ALERT.ERROR.CODE=CRD-XX-001

IDC.IS.ALERT.CREDIT.Close.Positions.Fail.Alert=Credit Fail to initiate close out positions.
IDC.IS.ALERT.CREDIT.Close.Positions.Fail.Alert.SEVERITY.LEVEL=3
IDC.IS.ALERT.CREDIT.Close.Positions.Fail.Alert.ERROR.CODE=CRD-XX-002

IDC.IS.ALERT.CREDIT.Close.Positions.Awaiting.Response.Alert=Awaiting response on close out positions.
IDC.IS.ALERT.CREDIT.Close.Positions.Awaiting.Response.Alert.SEVERITY.LEVEL=3
IDC.IS.ALERT.CREDIT.Close.Positions.Awaiting.Response.Alert.ERROR.CODE=CRD-XX-003

####### END: Credit Error Codes #############


####### BEGIN: VenueProvision SanityCheck Failure Codes ##############

IDC.IS.ALERT.VenueProvision.SanityCheckFailure.Maker=Sanity Check Failure for VenueProvisionData of Maker
IDC.IS.ALERT.VenueProvision.SanityCheckFailure.Maker.SEVERITY.LEVEL=3
IDC.IS.ALERT.VenueProvision.SanityCheckFailure.Maker.ERROR.CODE=VP-XX-001

IDC.IS.ALERT.VenueProvision.SanityCheckFailure.Taker=Sanity Check Failure for VenueProvisionData of Taker
IDC.IS.ALERT.VenueProvision.SanityCheckFailure.Taker.SEVERITY.LEVEL=3
IDC.IS.ALERT.VenueProvision.SanityCheckFailure.Taker.ERROR.CODE=VP-XX-002

IDC.IS.ALERT.VenueProvision.SanityCheckFailure.General=Minor Sanity Check Failure for VenueProvisionData of Maker
IDC.IS.ALERT.VenueProvision.SanityCheckFailure.General.SEVERITY.LEVEL=2
IDC.IS.ALERT.VenueProvision.SanityCheckFailure.General.ERROR.CODE=VP-XX-003

####### END: VenueProvision SanityCheck Failure Codes #############


####### BEGIN: VirtualServer Switch Order Migration Codes ##############

IDC.IS.ALERT.VirtualServerSwitch.OrderMigration=OrderMigration failure upon VirtualServer Switch
IDC.IS.ALERT.VirtualServerSwitch.OrderMigration.SEVERITY.LEVEL=3
IDC.IS.ALERT.VirtualServerSwitch.OrderMigration.ERROR.CODE=OM-XX-001

####### END: VirtualServer Switch Order Migration Codes #############


###### RFS related properties start ###########

IDC.IS.ALERT.Adaptor.Submit.RFS.Trade=Check availability of Provider
IDC.IS.ALERT.Adaptor.Submit.RFS.Trade.SEVERITY.LEVEL=3

IDC.IS.ALERT.Adaptor.RFS.Trade.Status.Unknown=Check the status of the trade with the provider
IDC.IS.ALERT.Adaptor.RFS.Trade.Status.Unknown.SEVERITY.LEVEL=3

IDC.IS.ALERT.Adaptor.JMS.RFS.MarketRate=Check availability of Sonic Server and the Queue on which the JMS message is sent.
IDC.IS.ALERT.Adaptor.JMS.RFS.MarketRate.SEVERITY.LEVEL=3

IDC.IS.ALERT.Adaptor.JMS.RFS.TradeResponse=Check the availability of Sonic Server and the Queue on which the JMS message is sent.Check the response for the trade received from the provider
IDC.IS.ALERT.Adaptor.JMS.RFS.TradeResponse.SEVERITY.LEVEL=3

IDC.IS.ALERT.Adaptor.JMS.RFS.TradePending=Check the availability of the Sonic Server and the Queue on which the JMS message is sent.
IDC.IS.ALERT.Adaptor.JMS.RFS.TradePending.SEVERITY.LEVEL=3


IDC.IS.ALERT.RFS.CcyPair.Configuration.Missing=Configuration for supported currency pair missing 
IDC.IS.ALERT.RFS.CcyPair.Configuration.Missing.SEVERITY.LEVEL=1

IDC.IS.ALERT.RFS.Validation.MDSNotDefined=1) RFS Validation Failed. MDS not defined. Alert message contains more details.
IDC.IS.ALERT.RFS.Validation.MDSNotDefined.SEVERITY.LEVEL=1

IDC.IS.ALERT.CUSTOMER_MID_RATE_ZERO=Customer mid rate calculated zero due to Raw LP Rate is zero. Calculating the mid-rate using customer Bid and Offer.
IDC.IS.ALERT.CUSTOMER_MID_RATE_ZERO.SEVERITY.LEVEL=1

IDC.IS.ALERT.INCORRECT_NEAR_LEG_MID_RATE=Calculated Near leg mid-rate is greater than far leg mid-rate. Calculating the near leg mid-rate using customer Bid and Offer.
IDC.IS.ALERT.INCORRECT_NEAR_LEG_MID_RATE.SEVERITY.LEVEL=1

IDC.IS.ALERT.RFS.VALUE.DATE.MISMATCH=RFS Quotes Dropped. RFS ValueDate Mismatch between request and provider. 
IDC.IS.ALERT.RFS.VALUE.DATE.MISMATCH.SEVERITY.LEVEL=2
IDC.IS.ALERT.RFS.VALUE.DATE.MISMATCH.ERROR.CODE=RFS-XX-001

###### RFS related properties end ###########


########## GM Error Codes Start ############

IDC.IS.ALERT.Grid.Monitor.Send.Message.Pool.Full=1) Contact Engineering
IDC.IS.ALERT.Grid.Monitor.Send.Message.Pool.Full.SEVERITY.LEVEL=1
IDC.IS.ALERT.Grid.Monitor.Send.Message.Pool.Full.ERROR.CODE=GM-XX-001

IDC.IS.ALERT.GM.BATCHCOMMIT.ERROR=GM Messages Dropped Contact Engineering
IDC.IS.ALERT.GM.BATCHCOMMIT.ERROR.SEVERITY.LEVEL=2
IDC.IS.ALERT.GM.BATCHCOMMIT.ERROR.ERROR.CODE=GM-XX-002

IDC.IS.ALERT.GM.WORK_CONTROL_FLOW_MSG_NOT_SENT.ERROR=Work Control flow message sending failed, visit https://sites.google.com/a/integral.com/rnd/resources/alerts#TOC-Grid-Monitor-Error-Codes for more details
IDC.IS.ALERT.GM.WORK_CONTROL_FLOW_MSG_NOT_SENT.ERROR.SEVERITY.LEVEL=2
IDC.IS.ALERT.GM.WORK_CONTROL_FLOW_MSG_NOT_SENT.ERROR.ERROR.CODE=GM-XX-003

IDC.IS.ALERT.GM.WORK_CONTROL_FLOW_MSG_NOT_PROCESSED.ERROR=Work Control flow message dropped by Dealing server, visit https://sites.google.com/a/integral.com/rnd/resources/alerts#TOC-Grid-Monitor-Error-Codes for more details
IDC.IS.ALERT.GM.WORK_CONTROL_FLOW_MSG_NOT_PROCESSED.ERROR.SEVERITY.LEVEL=2
IDC.IS.ALERT.GM.WORK_CONTROL_FLOW_MSG_NOT_PROCESSED.ERROR.ERROR.CODE=GM-XX-004
########## GM Error Codes End ############

################ Riskwarehouse/Yield Manager related properties start ################
### Refer to com.integral.riskmanagement.error.ErrorCode

IDC.IS.ALERT.RMALERT.RM_INTERNAL_SERVER_ERROR=1) Check the logs for any Exceptions , visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_INTERNAL_SERVER_ERROR.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_INTERNAL_SERVER_ERROR.ERROR.CODE=YM-XX-001

IDC.IS.ALERT.RMALERT.RM_ENGINE_SHUTDOWN=1) Trades have entered the riskwarehouse after riskwarehouse shutdown, https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_ENGINE_SHUTDOWN.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_ENGINE_SHUTDOWN.ERROR.CODE=YM-XX-002

IDC.IS.ALERT.RMALERT.RM_UNSUPPORTED_EVENT_RECEIVED_IN_PROCESSOR=1) Unsupported event received in riskwarehouse, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_UNSUPPORTED_EVENT_RECEIVED_IN_PROCESSOR.SEVERITY.LEVEL=2
IDC.IS.ALERT.RMALERT.RM_UNSUPPORTED_EVENT_RECEIVED_IN_PROCESSOR.ERROR.CODE=YM-XX-003

IDC.IS.ALERT.RMALERT.RM_UNSUPPORTED_TRADE_NOTIFICATION_EVENT=1) Unsupported  trade notification received in riskwarehouse, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_UNSUPPORTED_TRADE_NOTIFICATION_EVENT.SEVERITY.LEVEL=2
IDC.IS.ALERT.RMALERT.RM_UNSUPPORTED_TRADE_NOTIFICATION_EVENT.ERROR.CODE=YM-XX-004

IDC.IS.ALERT.RMALERT.RM_ENGINE_SERIALIZATION_EXCEPTION=1) Trade has not been processed in the riskwarehouse because of failure to deserialize the trade from RMQ, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_ENGINE_SERIALIZATION_EXCEPTION.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_ENGINE_SERIALIZATION_EXCEPTION.ERROR.CODE=YM-XX-005

IDC.IS.ALERT.RMALERT.RM_RW_START_FAILED=1) Failed to start Riskwarehouse, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_START_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_START_FAILED.ERROR.CODE=YM-XX-006

IDC.IS.ALERT.RMALERT.RM_RW_STOP_FAILED=1) Failed to stop Riskwarehouse gracefully, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_STOP_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_STOP_FAILED.ERROR.CODE=YM-XX-007

IDC.IS.ALERT.RMALERT.RM_RW_EVENT_ILLEGAL_PARAM=1) Illegal parameter in event, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_EVENT_ILLEGAL_PARAM.SEVERITY.LEVEL=2
IDC.IS.ALERT.RMALERT.RM_RW_EVENT_ILLEGAL_PARAM.ERROR.CODE=YM-XX-008

IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderFailedEvent=1) Failed to process an order failed event, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderFailedEvent.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderFailedEvent.ERROR.CODE=YM-XX-009

IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderCancelEvent=1) Failed to process an order cancel event, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderCancelEvent.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderCancelEvent.ERROR.CODE=YM-XX-010

IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderExpiredEvent=1) Failed to process an order expire event, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderExpiredEvent.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderExpiredEvent.ERROR.CODE=YM-XX-011

IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderRejectedEvent=1) Failed to process an order reject event, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderRejectedEvent.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderRejectedEvent.ERROR.CODE=YM-XX-012

IDC.IS.ALERT.RMALERT.RM_RW_POSSIBLE_DUPLICATE=1) Received a possible duplicate tradeInfo, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_POSSIBLE_DUPLICATE.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_POSSIBLE_DUPLICATE.ERROR.CODE=YM-XX-013

IDC.IS.ALERT.RMALERT.RM_RW_ACK_FAILED=1) Failed to ack an RMQ message, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_ACK_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_ACK_FAILED.ERROR.CODE=YM-XX-014

IDC.IS.ALERT.RMALERT.RM_RW_NACK_FAILED=1) Failed to nack a RMQ message, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_NACK_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_NACK_FAILED.ERROR.CODE=YM-XX-015

IDC.IS.ALERT.RMALERT.RM_RW_ORDER_REMOVAL_FAILED=1) Failed to remove a pending order from riskwarehouse, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_REMOVAL_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_REMOVAL_FAILED.ERROR.CODE=YM-XX-016

IDC.IS.ALERT.RMALERT.RM_RW_ORDER_SUBMIT_FAILED=1) Failed to submit an order to EMS from riskwarehouse, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_SUBMIT_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_SUBMIT_FAILED.ERROR.CODE=YM-XX-017

IDC.IS.ALERT.RMALERT.RM_RW_ORDER_SUBMIT_TIPPING_ORDER_FAILED=1) Failed to submit an order to tipping order to EMS from riskwarehouse, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_SUBMIT_TIPPING_ORDER_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_SUBMIT_TIPPING_ORDER_FAILED.ERROR.CODE=YM-XX-018

IDC.IS.ALERT.RMALERT.RM_RW_ORDER_SUBMIT_RISKNET_ORDER_FAILED=1) Failed to submit an risknet order to EMS from riskwarehouse, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_SUBMIT_RISKNET_ORDER_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_SUBMIT_RISKNET_ORDER_FAILED.ERROR.CODE=YM-XX-019

IDC.IS.ALERT.RMALERT.RM_RW_ORDER_NOT_FOUND_FOR_CANCEL=1) Did not find the order for cancel , visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_NOT_FOUND_FOR_CANCEL.SEVERITY.LEVEL=1
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_NOT_FOUND_FOR_CANCEL.ERROR.CODE=YM-XX-020

IDC.IS.ALERT.RMALERT.RM_RW_ORDER_CANCEL_FAILED=1)Failed to cancel an order , visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_CANCEL_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_CANCEL_FAILED.ERROR.CODE=YM-XX-021

IDC.IS.ALERT.RMALERT.RM_POSITION_ROLL_PROCESS_FAILED=1) Failed to process position roll, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_POSITION_ROLL_PROCESS_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_POSITION_ROLL_PROCESS_FAILED.ERROR.CODE=YM-XX-022

IDC.IS.ALERT.RMALERT.RM_POSITION_TRADE_ROLL_PROCESS_FAILED=1) Failed to process trade roll, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_POSITION_TRADE_ROLL_PROCESS_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_POSITION_TRADE_ROLL_PROCESS_FAILED.ERROR.CODE=YM-XX-023

IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderCancelRejectEvent=1) Failed to process order cancel reject event, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderCancelRejectEvent.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderCancelRejectEvent.ERROR.CODE=YM-XX-024

IDC.IS.ALERT.RMALERT.RM_RW_ORDER_PENDING=1) Tipping order has still not completed in riskwarehouse for max_allowed_time, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_PENDING.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_PENDING.ERROR.CODE=YM-XX-025

IDC.IS.ALERT.RMALERT.RM_RW_LIVEMDS_RATE_NOT_AVAILABLE=1) LiveFXMDS does not have MarketData for the given ccy, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details 
IDC.IS.ALERT.RMALERT.RM_RW_LIVEMDS_RATE_NOT_AVAILABLE.SEVERITY.LEVEL=2
IDC.IS.ALERT.RMALERT.RM_RW_LIVEMDS_RATE_NOT_AVAILABLE.ERROR.CODE=YM-XX-026

IDC.IS.ALERT.RMALERT.RM_RW_FIX_CLIENT_SESSION_NOT_CONNECTED=1) FIX client session is not connected for the given organization, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details 
IDC.IS.ALERT.RMALERT.RM_RW_FIX_CLIENT_SESSION_NOT_CONNECTED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_FIX_CLIENT_SESSION_NOT_CONNECTED.ERROR.CODE=YM-XX-027

IDC.IS.ALERT.RMALERT.RM_RW_RN_MULTI_CAST_LISTENER_START_FAILED=1) Failed to start Multi cast subscription for listening on to the matching events from REX, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_RN_MULTI_CAST_LISTENER_START_FAILED.SEVERITY.LEVEL=2
IDC.IS.ALERT.RMALERT.RM_RW_RN_MULTI_CAST_LISTENER_START_FAILED.ERROR.CODE=YM-XX-028

IDC.IS.ALERT.RMALERT.RM_RW_ORDERCANCEL_RECEIVED_ORDER_NOT_FOUND=1) An order cancel request has been received but order not found in warehouse internal cache, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_ORDERCANCEL_RECEIVED_ORDER_NOT_FOUND.SEVERITY.LEVEL=1
IDC.IS.ALERT.RMALERT.RM_RW_ORDERCANCEL_RECEIVED_ORDER_NOT_FOUND.ERROR.CODE=YM-XX-029

IDC.IS.ALERT.RMALERT.RM_RW_ORDER_CANCEL_PENDING=1) Failed to receive cancel order confirmation from EMS, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_CANCEL_PENDING.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_CANCEL_PENDING.ERROR.CODE=YM-XX-030

IDC.IS.ALERT.RMALERT.RM_RW_MARKET_DATA_SUBSCRIPTITION_FAILED=1) Failed to start market data subscription for the given organziation and currency pair. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_MARKET_DATA_SUBSCRIPTITION_FAILED.SEVERITY.LEVEL=2
IDC.IS.ALERT.RMALERT.RM_RW_MARKET_DATA_SUBSCRIPTITION_FAILED.ERROR.CODE=YM-XX-031

IDC.IS.ALERT.RMALERT.RM_RW_FXRATEBASIS_NOT_INITIALIZED=1) Failed to initialize fxRateBasis for the given currency pair,configure it in STDQuoteConvention. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_FXRATEBASIS_NOT_INITIALIZED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_FXRATEBASIS_NOT_INITIALIZED.ERROR.CODE=YM-XX-032

IDC.IS.ALERT.RMALERT.RM_RW_INCORRECT_TRADE_DIRECTION=1) Tipping order and trade are in different directions, earmarked amount not removed,visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_INCORRECT_TRADE_DIRECTION.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_INCORRECT_TRADE_DIRECTION.ERROR.CODE=YM-XX-033

IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_MAX_NUMBER_ORDERS_REACHED=1)Max number of orders reached, circuit breaker has been enabled, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_MAX_NUMBER_ORDERS_REACHED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_MAX_NUMBER_ORDERS_REACHED.ERROR.CODE=YM-XX-034

IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_TAKER_VOL_GREATER_THAN_MAKER_VOL=1) Taker volume is greater than maker volume by threshold, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_TAKER_VOL_GREATER_THAN_MAKER_VOL.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_TAKER_VOL_GREATER_THAN_MAKER_VOL.ERROR.CODE=YM-XX-035

IDC.IS.ALERT.RMALERT.RM_RW_TRANSACTION_SERVER_LOG_WRITE_FAILED=1) Failed to persist event to db, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_TRANSACTION_SERVER_LOG_WRITE_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_TRANSACTION_SERVER_LOG_WRITE_FAILED.ERROR.CODE=YM-XX-036

IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_EMS_SESSION_NOT_CONNECTED=1) Yield Manager Circuit Breaker Enabled since EMS Session is not connected. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_EMS_SESSION_NOT_CONNECTED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_EMS_SESSION_NOT_CONNECTED.ERROR.CODE=YM-XX-037

IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_MAX_NUMBER_HEDGING_ORDER_REJECTION_REACHED=1) Yield Manager Circuit Breaker Enabled since rejection count threshold of hedging order rejections is breached. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_MAX_NUMBER_HEDGING_ORDER_REJECTION_REACHED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_MAX_NUMBER_HEDGING_ORDER_REJECTION_REACHED.ERROR.CODE=YM-XX-038

IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_MARKET_DATA_NOT_AVAILABLE=1) Yield Manager Circuit Breaker Enabled since Market Data is not available. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_MARKET_DATA_NOT_AVAILABLE.SEVERITY.LEVEL=2
IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_MARKET_DATA_NOT_AVAILABLE.ERROR.CODE=YM-XX-039

IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_AdminResetOrderRejectionCountEvent=1) Failed to reset hedging order rejection count for Risk Warehouse. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_AdminResetOrderRejectionCountEvent.SEVERITY.LEVEL=1
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_AdminResetOrderRejectionCountEvent.ERROR.CODE=YM-XX-040

IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_AdminResetMarketData=1) Failed to reset market data for Risk Warehouse. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_AdminResetMarketData.SEVERITY.LEVEL=1
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_AdminResetMarketData.ERROR.CODE=YM-XX-041

IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderSubmitSuccessEvent=1) Failed to process order submit success response. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderSubmitSuccessEvent.SEVERITY.LEVEL=1
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_OrderSubmitSuccessEvent.ERROR.CODE=YM-XX-042

IDC.IS.ALERT.RMALERT.RM_RW_POSITION_SPLIT_FAILED=1) Failed for split the position existing in Risk Warehouse. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_POSITION_SPLIT_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_POSITION_SPLIT_FAILED.ERROR.CODE=YM-XX-043

IDC.IS.ALERT.RMALERT.POSITION_SERVER_QUEUE_BINDING_FAILED=1) Failed for bind queue in position server in Risk Warehouse. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.POSITION_SERVER_QUEUE_BINDING_FAILED.SEVERITY.LEVEL=1
IDC.IS.ALERT.RMALERT.POSITION_SERVER_QUEUE_BINDING_FAILED.ERROR.CODE=YM-XX-044

IDC.IS.ALERT.RMALERT.RM_RW_TRADE_NOTIFICATION_SPLIT_FAILED=1) Failed to split trade notification for risk management in Risk Warehouse. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_TRADE_NOTIFICATION_SPLIT_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_TRADE_NOTIFICATION_SPLIT_FAILED.ERROR.CODE=YM-XX-045

IDC.IS.ALERT.RMALERT.RM_RW_POWERTRADER_API_SLOW=1) PowerTrader server is responding slowly. Please check the powertrader server status visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_POWERTRADER_API_SLOW.SEVERITY.LEVEL=2
IDC.IS.ALERT.RMALERT.RM_RW_POWERTRADER_API_SLOW.ERROR.CODE=YM-XX-046

IDC.IS.ALERT.RMALERT.RM_RW_EXTERNAL_API_BEING_CALLED_FREQUENTLY=1) Yield Manager external API is being called too frequently. Either contact the customer or disable to Yield Manager dashboard for this user to prevent  visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_EXTERNAL_API_BEING_CALLED_FREQUENTLY.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_EXTERNAL_API_BEING_CALLED_FREQUENTLY.ERROR.CODE=YM-XX-047

IDC.IS.ALERT.RMALERT.RM_RW_ORDER_OVERFILL=1) Yield Manager placed order has been overfilled. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_OVERFILL.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_OVERFILL.ERROR.CODE=YM-XX-048

IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_AdminResetEarmarkedAmt=1) Failed to reset earmarked amount. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_AdminResetEarmarkedAmt.SEVERITY.LEVEL=1
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_AdminResetEarmarkedAmt.ERROR.CODE=YM-XX-049

IDC.IS.ALERT.RMALERT.RM_RW_ORDER_NOT_FOUND_FOR_EARMARK_REMOVAL=1)Order not found for earmark removal. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_NOT_FOUND_FOR_EARMARK_REMOVAL.SEVERITY.LEVEL=2
IDC.IS.ALERT.RMALERT.RM_RW_ORDER_NOT_FOUND_FOR_EARMARK_REMOVAL.ERROR.CODE=YM-XX-050

IDC.IS.ALERT.RMALERT.RM_YM_RECONCILIATION_MISMATCH=1)YM Reconciliation has mismatch. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_YM_RECONCILIATION_MISMATCH.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_YM_RECONCILIATION_MISMATCH.ERROR.CODE=YM-XX-051

IDC.IS.ALERT.RMALERT.RM_RW_PROVISION_UPDATE_FAILED=Failed to update RWProvision Object, servers are running on stale RWProvision visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_PROVISION_UPDATE_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_PROVISION_UPDATE_FAILED.ERROR.CODE=YM-XX-052

IDC.IS.ALERT.RMALERT.RM_YM_P2P_MESSAGE_SEND_FAILED=Failed to send YM Peer to Peer Message. https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_YM_P2P_MESSAGE_SEND_FAILED.SEVERITY.LEVEL=2
IDC.IS.ALERT.RMALERT.RM_YM_P2P_MESSAGE_SEND_FAILED.ERROR.CODE=YM-XX-053

IDC.IS.ALERT.RMALERT.RM_RW_LIVEMDS_NOT_CONFIGURED=1) LiveMDS not configured for the given organization and currency pair. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_LIVEMDS_NOT_CONFIGURED.SEVERITY.LEVEL=2
IDC.IS.ALERT.RMALERT.RM_RW_LIVEMDS_NOT_CONFIGURED.ERROR.CODE=YM-XX-054

IDC.IS.ALERT.Risk.Warehouse.Server.Startup.Failed=1) Risk Warehouse server is not started, visit https://sites.google.com/a/integral.com/yield-manager/documents/ymalertdescriptions for more details
IDC.IS.ALERT.Risk.Warehouse.Server.Startup.Failed.SEVERITY.LEVEL=3
IDC.IS.ALERT.Risk.Warehouse.Server.Startup.Failed.ERROR.CODE=YM-XX-055

IDC.IS.ALERT.Risk.Warehouse.Provision.Rebuild.Failed=1) Risk Warehouse provision rebuild failed, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.Risk.Warehouse.Provision.Rebuild.Failed.SEVERITY.LEVEL=3
IDC.IS.ALERT.Risk.Warehouse.Provision.Rebuild.Failed.ERROR.CODE=YM-XX-056

IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_AdminEnableCircuitBreaker=1) Failed to process Circuit Breaker Enable. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_AdminEnableCircuitBreaker.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_AdminEnableCircuitBreaker.ERROR.CODE=YM-XX-057

IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_AdminDisableCircuitBreaker=1) Failed to process Circuit Breaker Disable. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_AdminDisableCircuitBreaker.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_PROCESSING_FAILED_AdminDisableCircuitBreaker.ERROR.CODE=YM-XX-058

IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_MAX_DAILY_BOOK_LOSS=1) Max Daily Allowed Loss limit reached. Circuit Breaker Enabled. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_MAX_DAILY_BOOK_LOSS.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_CIRCUIT_BREAKER_MAX_DAILY_BOOK_LOSS.ERROR.CODE=YM-XX-059

IDC.IS.ALERT.RMALERT.RM_RW_WAREHOUSE_EVENT_SEND_FAILED=1) Failed to send WarehouseEvent over RMQ to webserver, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_WAREHOUSE_EVENT_SEND_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_WAREHOUSE_EVENT_SEND_FAILED.ERROR.CODE=YM-XX-060

IDC.IS.ALERT.RMALERT.RM_RW_INVERTED_MARKET_DATA=1) Inverted Market Data detected in warehouse's market data source. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_INVERTED_MARKET_DATA.SEVERITY.LEVEL=2
IDC.IS.ALERT.RMALERT.RM_RW_INVERTED_MARKET_DATA.ERROR.CODE=YM-XX-061

IDC.IS.ALERT.RMALERT.RM_RW_MID_DEVIATION_BREACHED=1) Mid Rate deviation detected between warehouse's market data sources. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_MID_DEVIATION_BREACHED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_MID_DEVIATION_BREACHED.ERROR.CODE=YM-XX-062

IDC.IS.ALERT.RMALERT.RM_RW_DARWIN_CACHE_REFRESH_FAILED=1) Internal Cache could not be successfully refreshed. visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RMALERT.RM_RW_DARWIN_CACHE_REFRESH_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RMALERT.RM_RW_DARWIN_CACHE_REFRESH_FAILED.ERROR.CODE=YM-XX-063

################ Riskwarehouse/Yield Manager related properties end ################


################ ScheduleEvent related properties start ################

IDC.IS.ALERT.SCHEDULEEVENT.SCHEDULE_EVENT_EXECUTION_FAILED=1) Schedule event Failed to execute . Please check the logs to find out more information.
IDC.IS.ALERT.SCHEDULEEVENT.SCHEDULE_EVENT_EXECUTION_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.SCHEDULEEVENT.SCHEDULE_EVENT_EXECUTION_FAILED.ERROR.CODE=SCH-XX-001

################ ScheduleEvent related properties end ################


#########################TradeInfo Notification Error Codes start########################

IDC.IS.ALERT.TRADE_NOTIFICATION_PUBLISHER_FAILED_TO_INITIALIZE=1)Trade Notification publisher failed to initialize, hence system will be shutdown due to incomplete service initialization, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.TRADE_NOTIFICATION_PUBLISHER_FAILED_TO_INITIALIZE.SEVERITY.LEVEL=3
IDC.IS.ALERT.TRADE_NOTIFICATION_PUBLISHER_FAILED_TO_INITIALIZE.ERROR.CODE=TIN-XX-001

IDC.IS.ALERT.Error.TradeInfo.Publish.Failed=1) Failed to publish trade notification for risk management, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.Error.TradeInfo.Publish.Failed.SEVERITY.LEVEL=3
IDC.IS.ALERT.Error.TradeInfo.Publish.Failed.ERROR.CODE=TIN-XX-002

IDC.IS.ALERT.Error.Maker.TradeInfo.Publish.Failed= 1) Failed to publish Maker side trade notification for risk management, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.Error.Maker.TradeInfo.Publish.Failed.SEVERITY.LEVEL=3
IDC.IS.ALERT.Error.Maker.TradeInfo.Publish.Failed.ERROR.CODE=TIN-XX-003

IDC.IS.ALERT.Error.Taker.TradeInfo.Publish.Failed= 1) Failed to publish Taker side trade notification for risk management, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.Error.Taker.TradeInfo.Publish.Failed.SEVERITY.LEVEL=3
IDC.IS.ALERT.Error.Taker.TradeInfo.Publish.Failed.ERROR.CODE=TIN-XX-004

IDC.IS.ALERT.Error.Maker.Taker.TradeInfo.Publish.Failed= 1) Failed to publish Maker & Taker side trade notification for risk management, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.Error.Maker.Taker.TradeInfo.Publish.Failed.SEVERITY.LEVEL=3
IDC.IS.ALERT.Error.Maker.Taker.TradeInfo.Publish.Failed.ERROR.CODE=TIN-XX-005

IDC.IS.ALERT.Error.Maker.RFSNetSpot.TradeInfo.Publish.Failed=1) Failed to publish maker RFS Net Spot trade notification for risk management, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.Error.Maker.RFSNetSpot.TradeInfo.Publish.Failed.SEVERITY.LEVEL=3
IDC.IS.ALERT.Error.Maker.RFSNetSpot.TradeInfo.Publish.Failed.ERROR.CODE=TIN-XX-006

IDC.IS.ALERT.Error.Taker.RFSNetSpot.TradeInfo.Publish.Failed=1) Failed to publish taker RFS Net Spot trade notification for risk management, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.Error.Taker.RFSNetSpot.TradeInfo.Publish.Failed.SEVERITY.LEVEL=3
IDC.IS.ALERT.Error.Taker.RFSNetSpot.TradeInfo.Publish.Failed.ERROR.CODE=TIN-XX-007

#########################TradeNotification Error Codes End########################


IDC.IS.ALERT.UNABLE.TO.PUBLISH.AGGREGATED.RATE=Check the Server Log for Exceptions
IDC.IS.ALERT.UNABLE.TO.PUBLISH.AGGREGATED.RATE.SEVERITY.LEVEL=3

IDC.IS.ALERT.RFS.FIXING.DATE.MISMATCH=Check the Server Log for Exceptions
IDC.IS.ALERT.RFS.FIXING.DATE.MISMATCH.SEVERITY.LEVEL=3

IDC.IS.ALERT.RFS.VALIDATE.TRADE.CHANNEL=Check the Server Log for Exceptions
IDC.IS.ALERT.RFS.VALIDATE.TRADE.CHANNEL.SEVERITY.LEVEL=3

IDC.IS.ALERT.RDS_TIME_OUT=1) RDS client is timing out on data request, visit https://sites.google.com/a/integral.com/yield-manager/support/ymalertdescriptions for more details
IDC.IS.ALERT.RDS_TIME_OUT.SEVERITY.LEVEL=3

IDC.IS.ALERT.RFS.VERIFICATION.RATES.EXTRA.PRECISION=Extra precision and verified rates from provider
IDC.IS.ALERT.RFS.VERIFICATION.RATES.EXTRA.PRECISION.SEVERITY.LEVEL=3
IDC.IS.ALERT.RFS.VERIFICATION.RATES.EXTRA.PRECISION.ERROR.CODE=RFS-XX-003


################ Riskwarehouse related properties end ################

##################  OA EMS Error Codes Start ##############

IDC.IS.ALERT.Order.DayOrder.Expiration.Skipped=1) Day order expiration skipped as duplicate event is received from EOD Server. 2) Contact Engineering
IDC.IS.ALERT.Order.DayOrder.Expiration.Skipped.SEVERITY.LEVEL=3
IDC.IS.ALERT.Order.DayOrder.Expiration.Skipped.ERROR.CODE=EMSC-XX-001

IDC.IS.ALERT.DK.RECEVIED.STAGING.ORDER=DontKnow Trade Received for staging order.Please analyse the reason and perform necessary actions.
IDC.IS.ALERT.DK.RECEVIED.STAGING.ORDER.SEVERITY.LEVEL=3
IDC.IS.ALERT.DK.RECEVIED.STAGING.ERROR.CODE=EMSC-XX-002

IDC.IS.ALERT.UNABLE.TO.TURN.ONOFF.STREAMPRICES=Failed to enable/disable stream pricing for a broker, refer to log for analysis
IDC.IS.ALERT.UNABLE.TO.TURN.ONOFF.STREAMPRICES.SEVERITY.LEVEL=2
IDC.IS.ALERT.UNABLE.TO.TURN.ONOFF.STREAMPRICES.ERROR.CODE=EMSC-XX-003

IDC.IS.ALERT.DO.CANCEL.UNKNOWN=1) Directed Order could not be cancelled, please check GM and log for analysis. 2) Contact Engineering
IDC.IS.ALERT.DO.CANCEL.UNKNOWN.SEVERITY.LEVEL=3
IDC.IS.ALERT.DO.CANCEL.UNKNOWN.ERROR.CODE=EMSC-XX-004

IDC.IS.ALERT.DO.FILL.MISSING=1) There looks to be a mismatch in fill amount between EMS and Venues,please check GM,Logs for analysis. 2) Contact Engineering
IDC.IS.ALERT.DO.FILL.MISSING.SEVERITY.LEVEL=3
IDC.IS.ALERT.DO.FILL.MISSING.ERROR.CODE=EMSC-XX-005

IDC.IS.ALERT.DO.ACK.NOT.RECEIVED=1) Ack not received from Venue after a configured timeout. 2) Contact Engineering
IDC.IS.ALERT.DO.ACK.NOT.RECEIVED.SEVERITY.LEVEL=2
IDC.IS.ALERT.DO.ACK.NOT.RECEIVED.ERROR.CODE=EMSC-XX-006

IDC.IS.ALERT.DO.CANCEL.FAILED.FOR.VENUE=1) Trading Venue has gone down, please check GM and Logs for analysis. 2) Contact Engineering
IDC.IS.ALERT.DO.CANCEL.FAILED.FOR.VENUE.SEVERITY.LEVEL=2
IDC.IS.ALERT.DO.CANCEL.FAILED.FOR.VENUE.ERROR.CODE=EMSC-XX-007

IDC.IS.ALERT.DO.REQUEST.FAILED.FOR.VENUE=1) Unable to send message to Venue, please check Logs for analysis. 2) Contact Engineering
IDC.IS.ALERT.DO.REQUEST.FAILED.FOR.VENUE.SEVERITY.LEVEL=2
IDC.IS.ALERT.DO.REQUEST.FAILED.FOR.VENUE.ERROR.CODE=EMSC-XX-008

IDC.IS.ALERT.DO.FILL.FAILED.FOR.VENUE=1) Failed to process order fill from Venue, please check Logs for analysis. 2) Contact Engineering
IDC.IS.ALERT.DO.FILL.FAILED.FOR.VENUE.SEVERITY.LEVEL=2
IDC.IS.ALERT.DO.FILL.FAILED.FOR.VENUE.ERROR.CODE=EMSC-XX-009

IDC.IS.ALERT.DO.FILL.RESPONSE.DROPPED=1) Failed to handle fill response from Venue, please check Logs for analysis. 2) Contact Engineering
IDC.IS.ALERT.DO.FILL.RESPONSE.DROPPED.SEVERITY.LEVEL=2
IDC.IS.ALERT.DO.FILL.RESPONSE.DROPPED.ERROR.CODE=EMSC-XX-010

IDC.IS.ALERT.Order.Reload.AmountsValidationFailed=1) Order amounts failed validation on reload. Check order state. 2) Contact Engineering
IDC.IS.ALERT.Order.Reload.AmountsValidationFailed.SEVERITY.LEVEL=3
IDC.IS.ALERT.Order.Reload.AmountsValidationFailed.ERROR.CODE=EMSC-XX-011

IDC.IS.ALERT.BlockTrade.AllocationFailed=1) Failed to allocate the trade. 2) Contact Engineering
IDC.IS.ALERT.BlockTrade.AllocationFailed.SEVERITY.LEVEL=3
IDC.IS.ALERT.BlockTrade.AllocationFailed.ERROR.CODE=EMSC-XX-012

################ OA EMS Error Codes End ####################


############## BA EMS Error Codes Start ###################

IDC.IS.ALERT.EMS.OrderWorkflowEngineException=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.OrderWorkflowEngineException.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.OrderWorkflowEngineException.ERROR.CODE=EMSB-XX-001

IDC.IS.ALERT.EMS.OrderExecutionEngineException=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.OrderExecutionEngineException.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.OrderExecutionEngineException.ERROR.CODE=EMSB-XX-002

IDC.IS.ALERT.EMS.ClientResponseEngineException=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.ClientResponseEngineException.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.ClientResponseEngineException.ERROR.CODE=EMSB-XX-003

IDC.IS.ALERT.EMS.PostMatchEngineException=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.PostMatchEngineException.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.PostMatchEngineException.ERROR.CODE=EMSB-XX-004

IDC.IS.ALERT.EMS.OrderCompletionHandlerException=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.OrderCompletionHandlerException.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.OrderCompletionHandlerException.ERROR.CODE=EMSB-XX-005

IDC.IS.ALERT.EMS.PositionCoverFailed=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.PositionCoverFailed.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.PositionCoverFailed.ERROR.CODE=EMSB-XX-006

IDC.IS.ALERT.EMS.ClientResponseDeliveryFailed=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.ClientResponseDeliveryFailed.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.ClientResponseDeliveryFailed.ERROR.CODE=EMSB-XX-007

IDC.IS.ALERT.EMS.ReloadedOrderClientResponseCreationFailed=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.ReloadedOrderClientResponseCreationFailed.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.ReloadedOrderClientResponseCreationFailed.ERROR.CODE=EMSB-XX-008

IDC.IS.ALERT.EMS.OrderExecutionResponseSubmissionToOEEFailed=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.OrderExecutionResponseSubmissionToOEEFailed.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.OrderExecutionResponseSubmissionToOEEFailed.ERROR.CODE=EMSB-XX-009

IDC.IS.ALERT.EMS.TradeContext-OrderNotFound=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.TradeContext-OrderNotFound.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.TradeContext-OrderNotFound.ERROR.CODE=EMSB-XX-010

IDC.IS.ALERT.EMS.OrderReloadFailed=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.OrderReloadFailed.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.OrderReloadFailed.ERROR.CODE=EMSB-XX-011

IDC.IS.ALERT.EMS.OrderSuspendFailed=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.OrderSuspendFailed.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.OrderSuspendFailed.ERROR.CODE=EMSB-XX-012

IDC.IS.ALERT.EMS.OrderResumeFailed=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.OrderResumeFailed.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.OrderResumeFailed.ERROR.CODE=EMSB-XX-013

IDC.IS.ALERT.EMS.OrderCancelFailed=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.OrderCancelFailed.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.OrderCancelFailed.ERROR.CODE=EMSB-XX-014

IDC.IS.ALERT.EMS.EventSubmitFailed.PME.YMLiquidityLiftRequest=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.EventSubmitFailed.PME.YMLiquidityLiftRequest.SEVERITY.LEVEL=2
IDC.IS.ALERT.EMS.EventSubmitFailed.PME.YMLiquidityLiftRequest.ERROR.CODE=EMSB-XX-015

IDC.IS.ALERT.EMS.EventSubmitFailed.EventSubmitFailed.PME.YMUndoLiquidityLiftRequest=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.EventSubmitFailed.EventSubmitFailed.PME.YMUndoLiquidityLiftRequest.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.EventSubmitFailed.EventSubmitFailed.PME.YMUndoLiquidityLiftRequest.ERROR.CODE=EMSB-XX-016

IDC.IS.ALERT.EMS.EventSubmitFailed.PME.CreateTradeForYMLiquidityLift=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.EventSubmitFailed.PME.CreateTradeForYMLiquidityLift.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.EventSubmitFailed.PME.CreateTradeForYMLiquidityLift.ERROR.CODE=EMSB-XX-017

IDC.IS.ALERT.EMS.RequestFailed.YMLiquidityLiftRequest=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.RequestFailed.YMLiquidityLiftRequest.SEVERITY.LEVEL=2
IDC.IS.ALERT.EMS.RequestFailed.YMLiquidityLiftRequest.ERROR.CODE=EMSB-XX-018

IDC.IS.ALERT.EMS.RequestFailed.YMUndoLiquidityLiftRequest=1) Alert message contains additional information. 2) Contact Engineering.
IDC.IS.ALERT.EMS.RequestFailed.YMUndoLiquidityLiftRequest.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.RequestFailed.YMUndoLiquidityLiftRequest.ERROR.CODE=EMSB-XX-019

IDC.IS.ALERT.EMS.WarehousingOfCoverTradingRiskFailed=1) Warehousing of cover trading risk failed. Refer message for additional information 2) Contact Engineering.
IDC.IS.ALERT.EMS.WarehousingOfCoverTradingRiskFailed.SEVERITY.LEVEL=3
IDC.IS.ALERT.EMS.WarehousingOfCoverTradingRiskFailed.ERROR.CODE=EMSB-XX-020
################## BA EMS Error Codes End ############################

################## InvestorFX Error Codes Start ############################

IDC.IS.ALERT.ALLOCATION.MANUAL.TRADE.CREATION.FAILED=1) Manual Trade creation failed during allocation. Please check if creation of trade really failed or trade is excluded from the trade list.
IDC.IS.ALERT.ALLOCATION.MANUAL.TRADE.CREATION.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.ALLOCATION.MANUAL.TRADE.CREATION.FAILED.ERROR.CODE=INFX-XX-001

IDC.IS.ALERT.PORTFOLIO.EXECUTION.SANITY.CHECK.FAILED=1) Portfolio Execution Sanity check failed.Log needs to be checked for analysis.
IDC.IS.ALERT.PORTFOLIO.EXECUTION.SANITY.CHECK.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.PORTFOLIO.EXECUTION.SANITY.CHECK.FAILED.ERROR.CODE=INFX-ERR-001

IDC.IS.ALERT.PORTFOLIO.EXECUTION.SANITY.CHECK.PASSED=1) Portfolio Execution Sanity check passed. No action needs to be taken.
IDC.IS.ALERT.PORTFOLIO.EXECUTION.SANITY.CHECK.PASSED.SEVERITY.LEVEL=2
IDC.IS.ALERT.PORTFOLIO.EXECUTION.SANITY.CHECK.PASSED.ERROR.CODE=INFX-ERR-002

IDC.IS.ALERT.PORTFOLIO.SPOT.ORDER.SUBMISSION.FAILED=1) Portfolio Execution Spot Order Submission failed.Log needs to be checked for analysis.
IDC.IS.ALERT.PORTFOLIO.SPOT.ORDER.SUBMISSION.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.PORTFOLIO.SPOT.ORDER.SUBMISSION.FAILED.ERROR.CODE=INFX-XX-003

IDC.IS.ALERT.PORTFOLIO.FIXING.RATE.NOT.RECEIVED=1) Portfolio Execution Fixing Rate not received.Log needs to be checked for analysis.
IDC.IS.ALERT.PORTFOLIO.FIXING.RATE.NOT.RECEIVED.SEVERITY.LEVEL=3
IDC.IS.ALERT.PORTFOLIO.FIXING.RATE.NOT.RECEIVED.ERROR.CODE=INFX-XX-004

IDC.IS.ALERT.CREDIT.Check.Available.Credit.Alert=Portfolio Credit Availability Alert.
IDC.IS.ALERT.CREDIT.Check.Available.Credit.Alert.SEVERITY.LEVEL=1
IDC.IS.ALERT.CREDIT.Check.Available.Credit.Alert.ERROR.CODE=INFX-CRD-005

IDC.IS.ALERT.PORTFOLIO.AUTO.EXECUTION=Auto Execution Retry triggered.
IDC.IS.ALERT.PORTFOLIO.AUTO.EXECUTION.SEVERITY.LEVEL=2
IDC.IS.ALERT.PORTFOLIO.AUTO.EXECUTION.ERROR.CODE=INFX-EXE-006

IDC.IS.ALERT.PORTFOLIO.AUTO.EXECUTION.FAILED=Auto Execution Failed after retries.
IDC.IS.ALERT.PORTFOLIO.AUTO.EXECUTION.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.PORTFOLIO.AUTO.EXECUTION.FAILED.ERROR.CODE=INFX-ERR-003

IDC.IS.ALERT.PORTFOLIO.IMPORT.FAILED=Portfolio Import Failed.
IDC.IS.ALERT.PORTFOLIO.IMPORT.FAILED.SEVERITY.LEVEL=2
IDC.IS.ALERT.PORTFOLIO.IMPORT.FAILED.ERROR.CODE=INFX-EXE-001

IDC.IS.ALERT.PORTFOLIO.SPOT.ORDER.EXECUTION.FAILED=Portfolio GTM order execution Failed.
IDC.IS.ALERT.PORTFOLIO.SPOT.ORDER.EXECUTION.FAILED.SEVERITY.LEVEL=2
IDC.IS.ALERT.PORTFOLIO.SPOT.ORDER.EXECUTION.FAILED.ERROR.CODE=INFX-EXE-002

IDC.IS.ALERT.PORTFOLIO.FSR.EXECUTION.FAILED=Portfolio FSR execution Failed.
IDC.IS.ALERT.PORTFOLIO.FSR.EXECUTION.FAILED.SEVERITY.LEVEL=2
IDC.IS.ALERT.PORTFOLIO.FSR.EXECUTION.FAILED.ERROR.CODE=INFX-EXE-003

IDC.IS.ALERT.PORTFOLIO.TRUEUP.EXECUTION.FAILED=Portfolio TRUEUP execution Failed.
IDC.IS.ALERT.PORTFOLIO.TRUEUP.EXECUTION.FAILED.SEVERITY.LEVEL=2
IDC.IS.ALERT.PORTFOLIO.TRUEUP.EXECUTION.FAILED.ERROR.CODE=INFX-EXE-004

IDC.IS.ALERT.PORTFOLIO.XCCY.TRUEUP.EXECUTION.FAILED=Portfolio XCCY TRUEUP execution Failed.
IDC.IS.ALERT.PORTFOLIO.XCCY.TRUEUP.EXECUTION.FAILED.SEVERITY.LEVEL=2
IDC.IS.ALERT.PORTFOLIO.XCCY.TRUEUP.EXECUTION.FAILED.ERROR.CODE=INFX-EXE-005

IDC.IS.ALERT.STAGING.AREA.ORDER.SUBMISSION.FAILED=Staging Area Order Submission failed.Some extra configuration may be required for successful order submission.
IDC.IS.ALERT.STAGING.AREA.ORDER.SUBMISSION.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.STAGING.AREA.ORDER.SUBMISSION.FAILED.ERROR.CODE=INFX-STG-001

IDC.IS.ALERT.PORTFOLIO.ALLOCATED.TRADE.MIFID.PARAMETERS.NOT.SET=MIFID Parameters for Maker not set on Allocated Trade.
IDC.IS.ALERT.PORTFOLIO.ALLOCATED.TRADE.MIFID.PARAMETERS.NOT.SET.SEVERITY.LEVEL=3
IDC.IS.ALERT.PORTFOLIO.ALLOCATED.TRADE.MIFID.PARAMETERS.NOT.SET.ERROR.CODE=INFX-EXE-006

################## InvestorFX Error Codes End ############################


############ Transaction Server Error Codes Start #################

IDC.IS.ALERT.UNABLE.TO.RESET.READER.TASK=1) Unable to reset the reader task. So reader setup is initiated again.
IDC.IS.ALERT.UNABLE.TO.RESET.READER.TASK.SEVERITY.LEVEL=1
IDC.IS.ALERT.UNABLE.TO.RESET.READER.TASK.ERROR.CODE=TXN-XX-001

IDC.IS.ALERT.UNABLE.TO.PING.REMOTE.HOST=1) Unable to Ping remote host . Please check manually if it can be pinged.
IDC.IS.ALERT.UNABLE.TO.PING.REMOTE.HOST.SEVERITY.LEVEL=3
IDC.IS.ALERT.UNABLE.TO.PING.REMOTE.HOST.ERROR.CODE=TXN-XX-002

IDC.IS.ALERT.UNABLE.TO.SYNC.WITH.REMOTE.HOST=1) Unable to Sync with remote host . Please check remote host state.
IDC.IS.ALERT.UNABLE.TO.SYNC.WITH.REMOTE.HOST.SEVERITY.LEVEL=2
IDC.IS.ALERT.UNABLE.TO.SYNC.WITH.REMOTE.HOST.ERROR.CODE=TXN-XX-003

IDC.IS.ALERT.Transaction.Server.Read.Failed=1) Failed to read transaction
IDC.IS.ALERT.Transaction.Server.Read.Failed.SEVERITY.LEVEL=3
IDC.IS.ALERT.Transaction.Server.Read.Failed.ERROR.CODE=TXN-XX-004

############ Transaction Server Error Codes End #################


################ Position Server Error Codes Start ################
IDC.IS.ALERT.Position.Server.Queue.Binding.Failed=Unable to bind queue in Rabbit MQ , visit https://docs.google.com/document/d/1dKhgyVjO7522PsWdxst1C1Etaf47GvhvUTuOVDzpUuU for more details.
IDC.IS.ALERT.Position.Server.Queue.Binding.Failed.SEVERITY.LEVEL=3
IDC.IS.ALERT.Position.Server.Queue.Binding.Failed.ERROR.CODE=POS-XX-001

IDC.IS.ALERT.Position.Server.Queue.UnBinding.Failed=Unable to unbind queue in Rabbit MQ , visit https://docs.google.com/document/d/1dKhgyVjO7522PsWdxst1C1Etaf47GvhvUTuOVDzpUuU for more details.
IDC.IS.ALERT.Position.Server.Queue.UnBinding.Failed.SEVERITY.LEVEL=3
IDC.IS.ALERT.Position.Server.Queue.UnBinding.Failed.ERROR.CODE=POS-XX-002

IDC.IS.ALERT.Position.Server.Failed.To.Join.Position.Cluster=Position server could not join position servers cluster; 1] Check if RDS server is running 2] Check if zookeeper quorum is reachable from this server 3] Check position server logs for details
IDC.IS.ALERT.Position.Server.Failed.To.Join.Position.Cluster.SEVERITY.LEVEL=3
IDC.IS.ALERT.Position.Server.Failed.To.Join.Position.Cluster.ERROR.CODE=POS-XX-003

################ Position Server Error Codes End ################

################ Services Framework or Cluster Error Codes Start ################

IDC.IS.ALERT.Cluster.Controller.Startup.Failed=Cluster Controller start failed; 1] Check if RDS server is running 2] Check if zookeeper quorum is reachable from this server 3) Server would require a restart
IDC.IS.ALERT.Cluster.Controller.Startup.Failed.SEVERITY.LEVEL=3
IDC.IS.ALERT.Cluster.Controller.Startup.Failed.ERROR.CODE=SVF-XX-001

################ Services Framework or Cluster Error Codes End ################

################ Messaging Error Codes Start ################
IDC.IS.ALERT.MESSAGING.BROKER.BLOCKED=Too many messages pending to be delivered;Check RabbitMQ server health
IDC.IS.ALERT.MESSAGING.BROKER.BLOCKED.SEVERITY.LEVEL=3
IDC.IS.ALERT.MESSAGING.BROKER.BLOCKED.ERROR.CODE=MSG-XX-001

IDC.IS.ALERT.MESSAGING.BROKER.UNBLOCKED=RabbitMQ server recovered from blocked messages
IDC.IS.ALERT.MESSAGING.BROKER.UNBLOCKED.SEVERITY.LEVEL=1
IDC.IS.ALERT.MESSAGING.BROKER.UNBLOCKED.ERROR.CODE=MSG-XX-002

IDC.IS.ALERT.MESSAGING.PROCESSING.FAILED=Error in handling message; Check server log for more details
IDC.IS.ALERT.MESSAGING.PROCESSING.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.MESSAGING.PROCESSING.FAILED.ERROR.CODE=MSG-XX-003

IDC.IS.ALERT.MESSAGING.CONFIGURATION.INIT.FAILED=Initialization of messaging configuration failed; 1] Check mongo migration ran successfully 2] Check logs for more details
IDC.IS.ALERT.MESSAGING.CONFIGURATION.INIT.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.MESSAGING.CONFIGURATION.INIT.FAILED.ERROR.CODE=MSG-XX-004

IDC.IS.ALERT.MESSAGING.CONFIGURATION.INVALID.INIT=Incorrect initialization of messaging configuration
IDC.IS.ALERT.MESSAGING.CONFIGURATION.INVALID.INIT.SEVERITY.LEVEL=3
IDC.IS.ALERT.MESSAGING.CONFIGURATION.INVALID.INIT.ERROR.CODE=MSG-XX-005

IDC.IS.ALERT.MESSAGING.CONNECTION.LOST=RabbitMQ connection lost; Attempting to re connect
IDC.IS.ALERT.MESSAGING.CONNECTION.LOST.SEVERITY.LEVEL=1
IDC.IS.ALERT.MESSAGING.CONNECTION.LOST.ERROR.CODE=MSG-XX-006

IDC.IS.ALERT.MESSAGING.RECONNECTION.FAILED=RabbitMQ re connection failed; 1] Check if RabbitMQ server is running 2] Check if Rabbit MQ server is reachable from this server
IDC.IS.ALERT.MESSAGING.RECONNECTION.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.MESSAGING.RECONNECTION.FAILED.ERROR.CODE=MSG-XX-007

IDC.IS.ALERT.MESSAGING.PUBLISHER.INIT.FAILED=Initialization of messaging publisher failed; 1] Check mongo migration ran successfully 2] Check if RabbitMQ server is running 3] Check if Rabbit MQ server is reachable from this server
IDC.IS.ALERT.MESSAGING.PUBLISHER.INIT.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.MESSAGING.PUBLISHER.INIT.FAILED.ERROR.CODE=MSG-XX-008

IDC.IS.ALERT.MESSAGING.LISTENER.INIT.FAILED=Initialization of messaging listener failed; 1] Check mongo migration ran successfully 2] Check if RabbitMQ server is running 3] Check if Rabbit MQ server is reachable from this server
IDC.IS.ALERT.MESSAGING.LISTENER.INIT.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.MESSAGING.LISTENER.INIT.FAILED.ERROR.CODE=MSG-XX-009

################ Messaging Error Codes End ################


################ RDS Error Codes Start ################
IDC.IS.ALERT.RDS.ID.GENERATOR.Failed=Check the Server logs for the root cause
IDC.IS.ALERT.RDS.ID.GENERATOR.Failed.SEVERITY.LEVEL=3
IDC.IS.ALERT.RDS.ID.GENERATOR.Failed.ERROR.CODE=RDS-XX-001

IDC.IS.ALERT.RDS.REQUEST.TIMEOUT=Check if RDS server is running
IDC.IS.ALERT.RDS.REQUEST.TIMEOUT.SEVERITY.LEVEL=3
IDC.IS.ALERT.RDS.REQUEST.TIMEOUT.ERROR.CODE=RDS-XX-002

IDC.IS.ALERT.RDS.NOTIFICATION.LISTENER.FAILED=RDS notification listener setup failed; 1] Check if mongo migration ran successfully 2] Check if RabbitMQ server is running
IDC.IS.ALERT.RDS.NOTIFICATION.LISTENER.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RDS.NOTIFICATION.LISTENER.FAILED.ERROR.CODE=RDS-XX-003

IDC.IS.ALERT.RDS.NOTIFICATION.PUBLISH.FAILED=Publishing of a entity update failed; Check if RabbitMQ server is running
IDC.IS.ALERT.RDS.NOTIFICATION.PUBLISH.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.RDS.NOTIFICATION.PUBLISH.FAILED.ERROR.CODE=RDS-XX-004

################ RDS Error Codes End ################

###################MVPS Error Codes Start#####################################    
    
IDC.IS.ALERT.MVPS.RABBIT.MQ.RECEIVER.BLOCKED=MVPS Cannot receive Notifications from RabbitMQ
IDC.IS.ALERT.MVPS.RABBIT.MQ.RECEIVER.BLOCKED.SEVERITY.LEVEL=3
IDC.IS.ALERT.MVPS.RABBIT.MQ.RECEIVER.BLOCKED.ERROR.CODE=MVP-XX-001

IDC.IS.ALERT.MVPS.RABBIT.MQ.RECEIVER.UNBLOCKED=MVPS Receiver Unblocked
IDC.IS.ALERT.MVPS.RABBIT.MQ.RECEIVER.UNBLOCKED.SEVERITY.LEVEL=3
IDC.IS.ALERT.MVPS.RABBIT.MQ.RECEIVER.UNBLOCKED.ERROR.CODE=MVP-XX-002


IDC.IS.ALERT.MVPS.RABBIT.MQ.RECEIVER.FAIL=MVPS Rabbit MQ Receiver Down
IDC.IS.ALERT.MVPS.RABBIT.MQ.RECEIVER.FAIL.SEVERITY.LEVEL=3
IDC.IS.ALERT.MVPS.RABBIT.MQ.RECEIVER.FAIL.ERROR.CODE=MVP-XX-003


IDC.IS.ALERT.MVPS.START.FAILED=MVPS Failed to start MVP server
IDC.IS.ALERT.MVPS.START.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.MVPS.START.FAILED.ERROR.CODE=MVP-XX-004


IDC.IS.ALERT.MVPS.ORDER.HANDLE.FAIL=MVPS Failed to handle Order
IDC.IS.ALERT.MVPS.ORDER.HANDLE.FAIL.SEVERITY.LEVEL=2
IDC.IS.ALERT.MVPS.ORDER.HANDLE.FAIL.ERROR.CODE=MVP-XX-005


IDC.IS.ALERT.MVPS.SENDING.ACK.FAIL=MVPS Cannot send back Acknowledgement
IDC.IS.ALERT.MVPS.SENDING.ACK.FAIL.SEVERITY.LEVEL=2
IDC.IS.ALERT.MVPS.SENDING.ACK.FAIL.ERROR.CODE=MVP-XX-006

IDC.IS.ALERT.MVPS.RESTAPI.FAILED=MVPS RESTAPI Failed
IDC.IS.ALERT.MVPS.MVPS.RESTAPI.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.MVPS.MVPS.RESTAPI.FAILED.ERROR.CODE=MVP-XX-007

IDC.IS.ALERT.MVPS.BILATERAL.UNDISCLOSEDORG.NOTFOUND=MVPS Bilateral Undisclosed Org not found, please check admin server
IDC.IS.ALERT.MVPS.BILATERAL.UNDISCLOSEDORG.NOTFOUND.SEVERITY.LEVEL=1
IDC.IS.ALERT.MVPS.BILATERAL.UNDISCLOSEDORG.NOTFOUND.ERROR.CODE=MVP-XX-008

IDC.IS.ALERT.MVPS.CLOB.VENUE.SUPPORTED.STREAMINDEX.GREATERTHAN.CONFIGURED=MVPS CLOB Venue Supported Super Bank Index Greater than configured size, please check admin server
IDC.IS.ALERT.MVPS.CLOB.VENUE.SUPPORTED.STREAMINDEX.GREATERTHAN.CONFIGURED.SEVERITY.LEVEL=3
IDC.IS.ALERT.MMVPS.CLOB.VENUE.SUPPORTED.STREAMINDEX.GREATERTHAN.CONFIGURED.ERROR.CODE=MVP-XX-009


###################MVPS Error Codes End#####################################    

################# aggregation #####################


##BROKER
IDC.IS.ALERT.AGGB.UNABLE.TO.PUBLISH.AGGREGATED.RATE=Unable to publish Aggregated Rate, Check the Server Log for Exceptions
IDC.IS.ALERT.AGGB.UNABLE.TO.PUBLISH.AGGREGATED.RATE.SEVERITY.LEVEL=2
IDC.IS.ALERT.AGGB.UNABLE.TO.PUBLISH.AGGREGATED.RATE.ERROR.CODE=AGGB-XX-001

IDC.IS.ALERT.BA.Single.LP.Failover=Single LP Failover to another LP
IDC.IS.ALERT.BA.Single.LP.Failover.SEVERITY.LEVEL=2
IDC.IS.ALERT.BA.Single.LP.Failover.ERROR.CODE=PE-CFD-001

##CUSTOMER
IDC.IS.ALERT.AGGC.RING.BUFFER.PENDING.COUNT.THRESHOLD.EXCEEDED=Aggregation Ring Buffer pending count threshold exceeded
IDC.IS.ALERT.AGGC.RING.BUFFER.PENDING.COUNT.THRESHOLD.EXCEEDED.SEVERITY.LEVEL=2
IDC.IS.ALERT.AGGC.RING.BUFFER.PENDING.COUNT.THRESHOLD.EXCEEDED.ERROR.CODE=AGGC-XX-001

###################### Price Provision ######################

PRICEPROVISION.CONFIG.CURRENCY_PAIR=Price Provision of Update since currency pair is not supported.
PRICEPROVISION.CONFIG.CURRENCY_PAIR.SEVERITY.LEVEL.3=Price Provision of 
PRICEPROVISION.CONFIG.CURRENCY_PAIR.SEVERITY.ERROR.CODE=PP-XX-001

######################### Market Maker Start ########################
IDC.IS.ALERT.MarketMaker.RefData.UpdateFailure=Market Maker Reference Data update Failed.
IDC.IS.ALERT.MarketMaker.RefData.UpdateFailure.SEVERITY.LEVEL=2
IDC.IS.ALERT.MarketMaker.RefData.UpdateFailure.ERROR.CODE=MM-REF-001
######################### Market Maker End


###################### ISIN start ######################

IDC.IS.ALERT.ISIN.ISIN_FILE_DOWNLOAD_FAILED=ISIN File download failed.Check server for exception and retrigger the download through devapp if required.For more information, refer https://sites.google.com/a/integral.com/mtf/alerts
IDC.IS.ALERT.ISIN.ISIN_FILE_DOWNLOAD_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.ISIN.ISIN_FILE_DOWNLOAD_FAILED.ERROR.CODE=ISIN-XX-001

IDC.IS.ALERT.ISIN.DIRECTORY_CREATION_FAILED=ISIN File location directory creation failed.Check permissions on the server and add permissions if required. Retrigger download of file through devapp.For more information, refer https://sites.google.com/a/integral.com/mtf/alerts
IDC.IS.ALERT.ISIN.DIRECTORY_CREATION_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.ISIN.DIRECTORY_CREATION_FAILED.ERROR.CODE=ISIN-XX-002

IDC.IS.ALERT.ISIN.FILE_CREATION_FAILED=ISIN File creation failed.Check permissions on the server and add permissions if required. Retrigger download of file through devapp.For more information, refer https://sites.google.com/a/integral.com/mtf/alerts
IDC.IS.ALERT.ISIN.FILE_CREATION_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.ISIN.FILE_CREATION_FAILED.ERROR.CODE=ISIN-XX-003

IDC.IS.ALERT.ISIN.ISIN_FILE_STATUS_CREATION_FAILED=ISIN FileStatus creation on RDS failed.Check server for exception and retrigger the download through devapp if required.For more information, refer https://sites.google.com/a/integral.com/mtf/alerts
IDC.IS.ALERT.ISIN.ISIN_FILE_STATUS_CREATION_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.ISIN.ISIN_FILE_STATUS_CREATION_FAILED.ERROR.CODE=ISIN-XX-004

IDC.IS.ALERT.ISIN.ISIN_FILE_STATUS_UPDATE_FAILED=ISIN FileStatus update on RDS failed.Check server for exception and retrigger the download through devapp if required.For more information, refer https://sites.google.com/a/integral.com/mtf/alerts
IDC.IS.ALERT.ISIN.ISIN_FILE_STATUS_UPDATE_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.ISIN.ISIN_FILE_STATUS_UPDATE_FAILED.ERROR.CODE=ISIN-XX-005

IDC.IS.ALERT.ISIN.ISIN_DATA_INSERT_FAILED=ISINData insertion on RDS failed.Check server for exception and retrigger the file processing through devapp if required.For more information, refer https://sites.google.com/a/integral.com/mtf/alerts
IDC.IS.ALERT.ISIN.ISIN_DATA_INSERT_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.ISIN.ISIN_DATA_INSERT_FAILED.ERROR.CODE=ISIN-XX-006

IDC.IS.ALERT.ISIN.ISIN_TASK_FAILED=ISIN task failed due to unknown exception.Check server for exception and retrigger the task through devapp if required. For more information, refer https://sites.google.com/a/integral.com/mtf/alerts
IDC.IS.ALERT.ISIN.ISIN_TASK_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.ISIN.ISIN_TASK_FAILED.ERROR.CODE=ISIN-XX-007

IDC.IS.ALERT.ISIN.ISIN_PARSING_FAILED=ISIN file parsing failed. For more information, refer https://sites.google.com/a/integral.com/mtf/alerts
IDC.IS.ALERT.ISIN.ISIN_PARSING_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.ISIN.ISIN_PARSING_FAILED.ERROR.CODE=ISIN-XX-008

IDC.IS.ALERT.ISIN.ISIN_FILE_PROCESSING_NOT_COMPLETELY_SUCCESSFUL=ISIN file parsing was not completely successful. For more information, refer https://sites.google.com/a/integral.com/mtf/alerts
IDC.IS.ALERT.ISIN.ISIN_FILE_PROCESSING_NOT_COMPLETELY_SUCCESSFUL.SEVERITY.LEVEL=3
IDC.IS.ALERT.ISIN.ISIN_FILE_PROCESSING_NOT_COMPLETELY_SUCCESSFUL.ERROR.CODE=ISIN-XX-009

###################### ISIN end ######################

######################PII start########################

IDC.IS.ALERT.PII.PII_UNIQUE_NUM_NOT_GENERATED=Unique shortcode generation failed. Please check logs
IDC.IS.ALERT.PII.PII_UNIQUE_NUM_NOT_GENERATED.SEVERITY.LEVEL=3
IDC.IS.ALERT.PII.PII_UNIQUE_NUM_NOT_GENERATED.ERROR.CODE=PII-XX-001

IDC.IS.ALERT.PII.PII_UNIQUE_NUM_WARNING_LIMIT_REACHED=Unique shortcode generation has passed the warning limit. Please review short code generation ASAP
IDC.IS.ALERT.PII.PII_UNIQUE_NUM_WARNING_LIMIT_REACHED.SEVERITY.LEVEL=3
IDC.IS.ALERT.PII.PII_UNIQUE_NUM_WARNING_LIMIT_REACHED.ERROR.CODE=PII-XX-002

IDC.IS.ALERT.PII.PII_SHORT_CODE_GENERATION_FAILED=ShortCode generation has failed due to unknown exception. Please check the logs
IDC.IS.ALERT.PII.PII_SHORT_CODE_GENERATION_FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.PII.PII_SHORT_CODE_GENERATION_FAILED.ERROR.CODE=PII-XX-003

IDC.IS.ALERT.PII.PII_DATACENTER_NOT_FOUND=ShortCode generation failed due to unknown datacenter. Please update datacenter property IDC.PII.DATACENTER.* . For more information, please look at PIIConfig.properties
IDC.IS.ALERT.PII.PII_DATACENTER_NOT_FOUND.SEVERITY.LEVEL=3
IDC.IS.ALERT.PII.PII_DATACENTER_NOT_FOUND.ERROR.CODE=PII-XX-004

######################PII end ########################

###################### MIFID start ########################

IDC.IS.ALERT.Client.Ack.Reject=Reject Status in Client Acknowledgement.
IDC.IS.ALERT.Client.Ack.Reject.SEVERITY.LEVEL=3
IDC.IS.ALERT.Client.Ack.Reject.ERROR.CODE=MFD-FIX-001

IDC.IS.ALERT.LP.ACK.INVALID.USER=Invalid User in Trade Response from Provider. Ref: https://sites.google.com/a/integral.com/mtf/alerts
IDC.IS.ALERT.LP.ACK.INVALID.USER.SEVERITY.LEVEL=3
IDC.IS.ALERT.LP.ACK.INVALID.USER.ERROR.CODE=MFD-LP-001

IDC.IS.ALERT.LP.ACK.INVALID.PROVIDER=Invalid Provider Name in Trade Response from Provider. Ref: https://sites.google.com/a/integral.com/mtf/alerts
IDC.IS.ALERT.LP.ACK.INVALID.PROVIDER.SEVERITY.LEVEL=3
IDC.IS.ALERT.LP.ACK.INVALID.PROVIDER.ERROR.CODE=MFD-LP-002

IDC.IS.ALERT.LP.ACK.REJECT=Sending Reject Acknowledgement to Provider. See DKReason for details. Ref: https://sites.google.com/a/integral.com/mtf/alerts.
IDC.IS.ALERT.LP.ACK.REJECT.SEVERITY.LEVEL=3
IDC.IS.ALERT.LP.ACK.REJECT.ERROR.CODE=MFD-LP-003

IDC.IS.ALERT.LP.ACK.EXCEPTION=Exception during sending ack to provider. See integral.out for details. Ref: https://sites.google.com/a/integral.com/mtf/alerts.
IDC.IS.ALERT.LP.ACK.EXCEPTION.SEVERITY.LEVEL=3
IDC.IS.ALERT.LP.ACK.EXCEPTION.ERROR.CODE=MFD-LP-004

IDC.IS.ALERT.LP_ACK_UNABLE_TO_UPDATE_EXEC_TIME=Trade already in verified state. Execution time cannot be updated. Ref: Ref: https://sites.google.com/a/integral.com/mtf/alerts.
IDC.IS.ALERT.LP_ACK_UNABLE_TO_UPDATE_EXEC_TIME.SEVERITY.LEVEL=3
IDC.IS.ALERT.LP_ACK_UNABLE_TO_UPDATE_EXEC_TIME.ERROR.CODE=MFD-LP-005

IDC.IS.ALERT.LP.ACK.REQUEST.NOT.FOUND=Request not found server cache. Ref: https://sites.google.com/a/integral.com/mtf/alerts
IDC.IS.ALERT.LP.ACK.REQUEST.NOT.FOUND.SEVERITY.LEVEL=3
IDC.IS.ALERT.LP.ACK.REQUEST.NOT.FOUND.ERROR.CODE=MFD-LP-006

IDC.IS.ALERT.LP.ACK.FAILURE=Trade Acknowledgement failed. Ref: https://sites.google.com/a/integral.com/mtf/alerts
IDC.IS.ALERT.LP.ACK.FAILURE.SEVERITY.LEVEL=3
IDC.IS.ALERT.LP.ACK.FAILURE.ERROR.CODE=MFD-LP-007

IDC.IS.ALERT.MIFID.Val.Exception=Exception during MIFID Validations. See integral.out for details.
IDC.IS.ALERT.MIFID.Val.Exception.SEVERITY.LEVEL=3
IDC.IS.ALERT.MIFID.Val.Exception.ERROR.CODE=MFD-VAL-001

IDC.IS.ALERT.MIFID.Val.Broker.Not.MIFID=MTF trades not supported at Broker. Please enable MiFID at Broker Org detail page.
IDC.IS.ALERT.MIFID.Val.Broker.Not.MIFID.SEVERITY.LEVEL=3
IDC.IS.ALERT.MIFID.Val.Broker.Not.MIFID.ERROR.CODE=MFD-VAL-002

IDC.IS.ALERT.MIFID.Val.MakerExecUser.Not.MIFID=Maker Executing User is not MIFID.
IDC.IS.ALERT.MIFID.Val.MakerExecUser.Not.MIFID.SEVERITY.LEVEL=3
IDC.IS.ALERT.MIFID.Val.MakerExecUser.Not.MIFID.ERROR.CODE=MFD-VAL-003

IDC.IS.ALERT.MIFID.Val.TakerExecUser.Not.MIFID=Taker Executing User is not MIFID.
IDC.IS.ALERT.MIFID.Val.TakerExecUser.Not.MIFID.SEVERITY.LEVEL=3
IDC.IS.ALERT.MIFID.Val.TakerExecUser.Not.MIFID.ERROR.CODE=MFD-VAL-004

IDC.IS.ALERT.MIFID.Val.TakerInvDecMkrUser.Not.MIFID=Taker Investment Decision maker is not MIFID.
IDC.IS.ALERT.MIFID.Val.TakerInvDecMkrUser.Not.MIFID.SEVERITY.LEVEL=3
IDC.IS.ALERT.MIFID.Val.TakerInvDecMkrUser.Not.MIFID.ERROR.CODE=MFD-VAL-005

IDC.IS.ALERT.MIFID.Val.MakerInvDecMkrUser.Not.MIFID=Maker Investment Decision maker is not MIFID.
IDC.IS.ALERT.MIFID.Val.MakerInvDecMkrUser.Not.MIFID.SEVERITY.LEVEL=3
IDC.IS.ALERT.MIFID.Val.MakerInvDecMkrUser.Not.MIFID.ERROR.CODE=MFD-VAL-006

IDC.IS.ALERT.MIFID.Val.MakerLE.Not.MIFID=Maker Executing Firm is not MIFID.
IDC.IS.ALERT.MIFID.Val.MakerLE.Not.MIFID.SEVERITY.LEVEL=3
IDC.IS.ALERT.MIFID.Val.MakerLE.Not.MIFID.ERROR.CODE=MFD-VAL-007

IDC.IS.ALERT.MIFID.Val.TakerLE.Not.MIFID=Taker Executing Firm is not MIFID.
IDC.IS.ALERT.MIFID.Val.TakerLE.Not.MIFID.SEVERITY.LEVEL=3
IDC.IS.ALERT.MIFID.Val.TakerLE.Not.MIFID.ERROR.CODE=MFD-VAL-008

IDC.IS.ALERT.MIFID.Val.InvalidMakerTradingCapacity=Invalid Maker Trading Capacity.
IDC.IS.ALERT.MIFID.Val.InvalidMakerTradingCapacity.SEVERITY.LEVEL=3
IDC.IS.ALERT.MIFID.Val.InvalidMakerTradingCapacity.ERROR.CODE=MFD-VAL-009

IDC.IS.ALERT.MIFID.Val.InvalidTakerTradingCapacity=Invalid Taker Trading Capacity.
IDC.IS.ALERT.MIFID.Val.InvalidTakerTradingCapacity.SEVERITY.LEVEL=3
IDC.IS.ALERT.MIFID.Val.InvalidTakerTradingCapacity.ERROR.CODE=MFD-VAL-010

IDC.IS.ALERT.MIFID.Val.InvalidISIN=Invalid ISIN.
IDC.IS.ALERT.MIFID.Val.InvalidISIN.SEVERITY.LEVEL=3
IDC.IS.ALERT.MIFID.Val.InvalidISIN.ERROR.CODE=MFD-VAL-011

IDC.IS.ALERT.MIFID.Val.IsinLinkIdNotPresent=Invalid ISIN Link ID.
IDC.IS.ALERT.MIFID.Val.IsinLinkIdNotPresent.SEVERITY.LEVEL=3
IDC.IS.ALERT.MIFID.Val.IsinLinkIdNotPresent.ERROR.CODE=MFD-VAL-012

IDC.IS.ALERT.MIFID.MTFEventMessage.PublishFailed=MTFEventMessage Publish Failed.
IDC.IS.ALERT.MIFID.MTFEventMessage.PublishFailed.SEVERITY.LEVEL=3
IDC.IS.ALERT.MIFID.MTFEventMessage.PublishFailed.ERROR.CODE=MTF-MSG-001

IDC.IS.ALERT.MIFID.AMEND.PERSIST.FAIL=MiFID Amend Persist Failed.
IDC.IS.ALERT.MIFID.AMEND.PERSIST.FAIL.SEVERITY.LEVEL=3
IDC.IS.ALERT.MIFID.AMEND.PERSIST.FAIL.ERROR.CODE=MTF-MSG-002

IDC.IS.ALERT.CONCURRENT_RFQ_EXCEEDED_LIMIT=Concurrent RFQ count exceeded limit.
IDC.IS.ALERT.CONCURRENT_RFQ_EXCEEDED_LIMIT.SEVERITY.LEVEL=3
IDC.IS.ALERT.CONCURRENT_RFQ_EXCEEDED_LIMIT.ERROR.CODE=RFS-XX-002

IDC.IS.ALERT.MIFID.USER.SHORTCODE.UPDATE=Failed to update user shortcode. Ref: https://sites.google.com/a/integral.com/mtf/alerts
IDC.IS.ALERT.MIFID.USER.SHORTCODE.UPDATE.SEVERITY.LEVEL=3
IDC.IS.ALERT.MIFID.USER.SHORTCODE.UPDATE.ERROR.CODE=MFD-SHORTCODE-001
###################### MIFID end ##############################


###################### MDF end ##############################
IDC.IS.ALERT.ORG.PROVISION.FAILED=Org provision failed.Check log for more details and fix the Org configuration
IDC.IS.ALERT.ORG.PROVISION.FAILED.SEVERITY.LEVEL=3
IDC.IS.ALERT.ORG.PROVISION.FAILED.ERROR.CODE=MDF-ERR-001

###################### MDF end ##############################

###################### CRYPTO start ##############################
IDC.IS.ALERT.FEES.USD.UNAVAILABLE=Fees conversion to USD failed. Please correct manually.
IDC.IS.ALERT.FEES.USD.UNAVAILABLE.SEVERITY.LEVEL=2
IDC.IS.ALERT.FEES.USD.UNAVAILABLE.ERROR.CODE=CRYP-01
###################### CRYPTO end ##############################

########################PQ Directed START#####################################
IDC.IS.ALERT.PQ.DIRECTED.PQDirectedOrderFailed=PQ Directed Order Failed
IDC.IS.ALERT.PQ.DIRECTED.PQDirectedOrderFailed.SEVERITY.LEVEL=3
IDC.IS.ALERT.PQ.DIRECTED.PQDirectedOrderFailed.ERROR.CODE=PQD-XX-001
########################PQ Directed END#####################################

###################### Should be removed start ######################

## Should be removed. Not used.
IDC.IS.ALERT.ORDERIDGENERATION=1) Check Database 2) Check if database sequence IDCFXIORDERID_IS_SEQ exists.
IDC.IS.ALERT.ORDERIDGENERATION.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.ORDERCREATION=1) Check Database 
IDC.IS.ALERT.ORDERCREATION.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.ORDERFILLED=1) Check Database 
IDC.IS.ALERT.ORDERFILLED.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.ORDERFAILED=1) Check Database 
IDC.IS.ALERT.ORDERFAILED.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.ORDERCANCELLED=1) Check Database 
IDC.IS.ALERT.ORDERCANCELLED.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.REQUESTVALIDATION=Check FI setup for LP.
IDC.IS.ALERT.REQUESTVALIDATION.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.DEALAUDITING=Check Database
IDC.IS.ALERT.DEALAUDITING.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.DEALCREATION=Check Database
IDC.IS.ALERT.DEALCREATION.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.DEALSUBMITSUCCESS=Check Database
IDC.IS.ALERT.DEALSUBMITSUCCESS.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.DEALSUBMITFAILED=Check Database
IDC.IS.ALERT.DEALSUBMITFAILED.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.DEALSUBMITERROR=Check Database
IDC.IS.ALERT.DEALSUBMITERROR.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.DEALPENDINGVERIFICATION=Check Database
IDC.IS.ALERT.DEALPENDINGVERIFICATION.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.DEALVERIFIED=Check Database
IDC.IS.ALERT.DEALVERIFIED.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.DEALREJECTED=Check Database
IDC.IS.ALERT.DEALREJECTED.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.ISQSLOOKUP=Check availability and deployment of LP Quote Service
IDC.IS.ALERT.ISQSLOOKUP.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.ADAPTORRSLOOKUP=Check availability and deployment of Adaptot Request Service
IDC.IS.ALERT.ADAPTORRSLOOKUP.SEVERITY.LEVEL=3

IDC.IS.ALERT.ADAPTOREXCEPTION=See description for exception details
IDC.IS.ALERT.ADAPTOREXCEPTION.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.NETWORKDOWN=Network exception has occured while sending request to LP
IDC.IS.ALERT.NETWORKDOWN.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.ADAPTORDOWN=Check adaptor status and configuration
IDC.IS.ALERT.ADAPTORDOWN.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.AUDITING=Check Database availibility
IDC.IS.ALERT.AUDITING.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.VERIFICATIONAUDIT=Check Database availibility.
IDC.IS.ALERT.VERIFICATIONAUDIT.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.VERIFICATIONINCORRECT=Check message received from provider.
IDC.IS.ALERT.VERIFICATIONINCORRECT.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.JMSMSGADAPTORTOIS=Check availability of Sonic Server where JMS messages are send
IDC.IS.ALERT.JMSMSGADAPTORTOIS.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.ISRSLOOKUP=Check deployment of request service.
IDC.IS.ALERT.ISRSLOOKUP.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.ISOSLOOKUP=Check deployment of request service.
IDC.IS.ALERT.ISOSLOOKUP.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.TEMPQUEUE=Check availability of Sonic Server where JMS messages are send
IDC.IS.ALERT.TEMPQUEUE.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.Adaptor.Http.Communication=1)Check the HTTP URL of the server. 2)Check the availability of the server
IDC.IS.ALERT.Adaptor.Http.Communication.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.Adaptor.HTTP.Request.Processing=Check the Server Log for Exceptions
IDC.IS.ALERT.Adaptor.HTTP.Request.Processing.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.Adaptor.RequestService.Lookup=Check RequestService configuration in hostname of server
IDC.IS.ALERT.Adaptor.RequestService.Lookup.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.Adaptor.Request.Process=Check the Server Log for Exceptions
IDC.IS.ALERT.Adaptor.Request.Process.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.Adaptor.Submit.Subscription=Check availability of Provider
IDC.IS.ALERT.Adaptor.Submit.Subscription.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.Adaptor.Submit.UnSubscription=Check availability of Provider
IDC.IS.ALERT.Adaptor.Submit.UnSubscription.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.Adaptor.Submit.Trade=Check availability of Provider
IDC.IS.ALERT.Adaptor.Submit.Trade.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.Adaptor.Trade.Status.Unknown=Check the status of the trade with the provider
IDC.IS.ALERT.Adaptor.Trade.Status.Unknown.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.Adaptor.JMS.ProviderStatus=Check availability of Sonic Server where JMS messages are send
IDC.IS.ALERT.Adaptor.JMS.ProviderStatus.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.Adaptor.JMS.MarketRate=Check availability of Sonic Server where JMS messages are send
IDC.IS.ALERT.Adaptor.JMS.MarketRate.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.Adaptor.JMS.TradeResponse=Check the availability of Sonic Server and the Queue on which the JMS message is sent.Check the response for the trade received from the provider
IDC.IS.ALERT.Adaptor.JMS.TradeResponse.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.Adaptor.JMS.TradePending=Check the availability of the Sonic Server and the Queue on which the JMS message is sent.
IDC.IS.ALERT.Adaptor.JMS.TradePending.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.Adaptor.JMS.RemoteAudit=Check the availability of the Sonic Server and the Queue on which the JMS message is sent.
IDC.IS.ALERT.Adaptor.JMS.RemoteAudit.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.Adaptor.Submit.Order=Check the Server Log for Exceptions
IDC.IS.ALERT.Adaptor.Submit.Order.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.Adaptor.Submit.OrderCancel=Check the Server Log for Exceptions
IDC.IS.ALERT.Adaptor.Submit.OrderCancel.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.Adaptor.Submit.OrderStatusRequest=Check the Server Log for Exceptions
IDC.IS.ALERT.Adaptor.Submit.OrderStatusRequest.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.Adaptor.Submit.OrderMassStatusRequest=Check the Server Log for Exceptions
IDC.IS.ALERT.Adaptor.Submit.OrderMassStatusRequest.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.Adaptor.Submit.OrderMassCancelRequest=Check the Server Log for Exceptions
IDC.IS.ALERT.Adaptor.Submit.OrderMassCancelRequest.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.Adaptor.JMS.OrderExecutionResponse=Check the availability of the Sonic Server and the Queue on which the JMS message is sent.
IDC.IS.ALERT.Adaptor.JMS.OrderExecutionResponse.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.Adaptor.JMS.OrderCancelReject=Check the availability of the Sonic Server and the Queue on which the JMS message is sent.
IDC.IS.ALERT.Adaptor.JMS.OrderCancelReject.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.Adaptor.JMS.OrderMassCancelReport=Check the availability of the Sonic Server and the Queue on which the JMS message is sent.
IDC.IS.ALERT.Adaptor.JMS.OrderMassCancelReport.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.Adaptor.Submit.RFS.Subscription=Check availability of Provider
IDC.IS.ALERT.Adaptor.Submit.RFS.Subscription.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.Adaptor.Submit.RFS.UnSubscription=Check availability of Provider
IDC.IS.ALERT.Adaptor.Submit.RFS.UnSubscription.SEVERITY.LEVEL=1

## Should be removed
IDC.IS.ALERT.Adaptor.JMS.RFS.RemoteAudit=Check the availability of the Sonic Server and the Queue on which the JMS message is sent.
IDC.IS.ALERT.Adaptor.JMS.RFS.RemoteAudit.SEVERITY.LEVEL=3

## Should be removed
IDC.IS.ALERT.Adaptor.JMS.RFS.ResponseMessage=Check the availability of the Sonic Server and the Queue on which the JMS message is sent.
IDC.IS.ALERT.Adaptor.JMS.RFS.ResponseMessage.SEVERITY.LEVEL=3

IDC.IS.ALERT.ERR0042=Error in processing order cancel replace
IDC.IS.ALERT.ERR0042.SEVERITY.LEVEL=3

IDC.IS.ALERT.MDS.SPOT.ELEMENT.NONZERO.FORWARD.POINTS.FOUND=Non-zero forward points found in mds upload
IDC.IS.ALERT.MDS.SPOT.ELEMENT.NONZERO.FORWARD.POINTS.FOUND.SEVERITY.LEVEL=2
IDC.IS.ALERT.MDS.SPOT.ELEMENT.NONZERO.FORWARD.POINTS.FOUND.ERROR.CODE=CRD-XX-002



###################### Should be removed end ######################
