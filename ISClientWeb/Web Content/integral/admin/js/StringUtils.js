
// *****************************************************************************
// StringUtils.js
// This JavaScript class holds String utility methods.  These methods are
// accessible through the StringUtils variable.
//
// Copyright (c) 1999-2002 Integral Development Corp. All rights reserved.
// *****************************************************************************



function StringUtilsC() {
	this.getSubstring = getSubstring;
	this.evalString = evalString;
	this.setUrlProperty = setUrlProperty;
	this.getProperty = getProperty;
	this.trim = trim;
	this.length = length;

	//************************
	// @return subString
	// @param aString
	// @param flag1
	// @param flag2
	//************************
	function getSubstring(aString, flag1, flag2) {
		var index1 = aString.indexOf(flag1);
		if (index1 == -1) {
			return null;
		}

		var index2 = aString.indexOf(flag2, index1 + flag1.length);
		if (index2 == -1) {
			index2 = aString.length;
		}

		return aString.substring(index1 + flag1.length, index2);
	}

	//**************************
	// @return aString or if null ""
	// @param aString
	//**************************
	function evalString(aString) {
		if (!aString) {
			return "";
		}

		return (aString == "null" ? "" : aString);
	}

	//***********************
	// @return aURL
	// @param aString
	// @param Key
	// @param value
	// @param force - true override existing parameter
	//***********************

	function setUrlProperty(aString, key, value, force) {
		var keyStr = key + "=";

		var startIndex = aString.indexOf(keyStr);
		if(!aString || (startIndex == -1 && !force)) {
			return aString;
		}

		if(aString.indexOf("?") == -1) {
			return aString + "?" + key + "=" + value;
		}
		if(startIndex == -1) {
			return aString + "&" + key + "=" + value;
		}

		var endIndex = aString.indexOf("&", startIndex);
		var result = aString.substring(0, startIndex) + keyStr + value;

		if(endIndex != -1) {
			result += aString.substring(endIndex);
		}
	
		return result;
	}

	//***********************
	// @return a Propertry of a string
	// @param aString
	// @param aKey
	// @param aFlag
	// @param partialMatch
	//***********************

	function getProperty(aString, aKey, aFlag, partialMatch) {
		var flag = aFlag || "&";
		var keyStr = aKey + "=";
		var index1 = aString.indexOf(keyStr);
		if (index1 == -1) {
			return null;
		}

		if (index1 > 0 && !partialMatch && aString.substring(index1 - 1, index1).match(/[a-zA-Z]/)) {
			return null;
		}

		var index2 = aString.indexOf(flag, index1);
		if (index2 == -1) {
			index2 = aString.length;
		}

		return aString.substring(index1 + keyStr.length, index2);
	}

	//***********************
	// @return trimmed string
	// @param aString
	// @param tail 
	//***********************
	function trim(aString, tail, aTrimLeadingFlag, aTrimTrailingFlag) {
		var trimLeadingFlag = (typeof(aTrimLeadingFlag) == "undefined" ? true : aTrimLeadingFlag);
		var trimTrailingFlag = (typeof(aTrimTrailingFlag) == "undefined" ? true : aTrimTrailingFlag);

		if(length(aString) == 0) {
			return "";
		}

		if (!tail) {
			return new String(aString).replace(/^\s*|\s*$/g, "");
		}

		var index = -1;

		while(trimLeadingFlag && aString.indexOf(tail) == 0) {
			if(aString == tail) {
				return "";
			}

			aString = aString.substring(tail.length);
		}

		while(trimTrailingFlag && (index = aString.lastIndexOf(tail)) == length(aString) - tail.length && index != -1) {
			aString = aString.substring(0, index);
		}

		return aString;
	}

	//***********************
	// @return length of string safely
	// @param aString
	//***********************

	function length(aString) {
		if(aString && aString.length) {
			return aString.length;
		} else {
			return 0;
		}
	}
}

var StringUtils = new StringUtilsC();

/**
 * The following methods are deprecated, use StringUtils.aMethod() instead
 */

function getSubstring(aString, flag1, flag2) {return StringUtils.getSubstring(aString, flag1, flag2);}
function evalString(aString) {return StringUtils.evalString(aString);}
function setUrlProperty(aString, key, value, force) {return StringUtils.setUrlProperty(aString, key, value, force);}
function getProperty(aString, aKey, aFlag, partialMatch) {return StringUtils.getProperty(aString, aKey, aFlag, partialMatch);}
function trim(aString, tail) {return StringUtils.trim(aString, tail);}
function length(aString) {return StringUtils.length(aString);}
