<%@ page import="com.integral.is.common.ISConstantsC" %>
<%@ page import="com.integral.is.common.admin.ManualDealUpdateNotificationHandler" %>
<%@ page import="com.integral.log.Log" %>
<%@ page import="com.integral.log.LogFactory" %>
<%@ page import="com.integral.message.MessageFactory" %>
<%@ page import="com.integral.message.MessageStatus" %>
<%@ page import="com.integral.message.WorkflowMessage" %>
<%@ page import="org.apache.struts.util.ResponseUtils" %>

<%!
    Log log = LogFactory.getLog("AmendTrade");
%>

<%
    WorkflowMessage wfMessage = MessageFactory.newWorkflowMessage();
    try {
        String transactionId = ResponseUtils.filter(request.getParameter("transactionId"));
        String dealAction = ResponseUtils.filter(request.getParameter("event"));
        String userName = ResponseUtils.filter(request.getParameter("userName"));
        String userNamespace = ResponseUtils.filter(request.getParameter("userNamespace")); 
        String isStrCptyA = ResponseUtils.filter(request.getParameter("counterPartyA"));
        String fromApiStr = ResponseUtils.filter(request.getParameter("fromApi"));
        String newLegalEntity = ResponseUtils.filter(request.getParameter("legalEntity"));
        String sendRiskNotification = ResponseUtils.filter(request.getParameter("sendRiskNotification"));
        WorkflowMessage replyMessage = MessageFactory.newWorkflowMessage();
        StringBuilder sb = new StringBuilder(100);
        wfMessage.setParameterValue("fromApi",fromApiStr);
        wfMessage.setParameterValue("transactionId", transactionId);
        wfMessage.setParameterValue("userName", userName);
        wfMessage.setParameterValue("userNamespace", userNamespace);
        wfMessage.setParameterValue("isCptyA", isStrCptyA);
        wfMessage.setParameterValue("legalEntity", newLegalEntity);
        wfMessage.setParameterValue("sendRiskNotification", sendRiskNotification);

        log.warn("AmendTrade.jsp ==> Received transactionId : " + transactionId
                    + " event : " + dealAction
                    + " isCptyA : " + isStrCptyA
                    + " legalEntity : " + newLegalEntity
                    + " userName : " + userName
                    + " userNamespace : " + userNamespace
                    + " sendRiskNotification : " + sendRiskNotification);
        if ("CANCEL".equals(dealAction)) {
            wfMessage.setEventName(ISConstantsC.MSG_EVENT_CANCEL);
            wfMessage.setTopic(ISConstantsC.MSG_TOPIC_TRADE);
            ManualDealUpdateNotificationHandler handler = new ManualDealUpdateNotificationHandler();
            replyMessage = (WorkflowMessage) handler.handle(wfMessage);
            if (replyMessage.getStatus().equals(MessageStatus.FAILURE)) {
                log.warn("Could not cancel trade " + transactionId);
                sb.append("FAILURE");
            } else {
                log.warn("Trade " + transactionId + " cancellation is successful");
                sb.append("SUCCESS");
            }
        }
        else if ("AMEND".equals(dealAction)) {
            wfMessage.setEventName(ISConstantsC.MSG_EVENT_AMEND);
            wfMessage.setTopic(ISConstantsC.MSG_TOPIC_TRADE);
            ManualDealUpdateNotificationHandler handler = new ManualDealUpdateNotificationHandler();
            replyMessage = (WorkflowMessage) handler.handle(wfMessage);
            if (replyMessage.getStatus().equals(MessageStatus.FAILURE)) {
                log.warn("Could not amend trade " + transactionId);
                sb.append("FAILURE");
            } else {
                log.warn("Trade " + transactionId + " amended successfully");
                sb.append("SUCCESS");
            }
        }

        sb.append(":").append(transactionId).append(":");
        sb.append(replyMessage.getParameterValue("Error"));

        log.warn("AmendTrade.jsp ==> Reply : " + sb.toString());

        out.write(sb.toString());
        response.flushBuffer();

    } catch (Exception e) {
        log.error("AmendTrade.jsp: Exception :  ", e);
        out.write("FAILURE");
        response.flushBuffer();
        e.printStackTrace();
    }
%>