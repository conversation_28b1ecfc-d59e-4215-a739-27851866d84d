<%@ page import="com.integral.is.common.ISConstantsC,
                 com.integral.is.common.admin.ManualDealUpdateNotificationHandler,
                 com.integral.message.MessageFactory,
                 com.integral.message.MessageStatus,
                 com.integral.message.WorkflowMessage,
                 com.integral.session.IdcSessionContext,
                 com.integral.time.DateTimeFactory,
                 com.integral.time.IdcDate,
                 com.integral.user.User,
                 org.apache.struts.util.ResponseUtils,
                 javax.servlet.http.HttpSession"%>
<%@ page import="java.text.DateFormat"%>
<%@ page import="java.text.SimpleDateFormat"%>


<body>
<%
    WorkflowMessage wfMessage = MessageFactory.newWorkflowMessage();
    HttpSession hSession = request.getSession();
    IdcSessionContext sessionContext = (IdcSessionContext) hSession.getAttribute(IdcSessionContext.SESSION_CONTEXT);
    User user = (User)sessionContext.getUser();
    DateFormat dateFormat =  user.getDisplayPreference().getDateFormat();

    String dateFrom = ResponseUtils.filter(request.getParameter( "dateFrom" ));
    String dateTo = ResponseUtils.filter(request.getParameter( "dateTo" ));
    IdcDate valueDateObj = null;
    String dealUpdateStatus = "";
    String valueDate = null;
    String dealAction =ResponseUtils.filter(request.getParameter( "dealAction" ));
    String[] selectedItems = request.getParameterValues( "selectedItems" );
    String tranId = ResponseUtils.filter(request.getParameter( "tranId_"+selectedItems[0] ));
    String lp = ResponseUtils.filter(request.getParameter( "LP_"+selectedItems[0] ));
    String cust = ResponseUtils.filter(request.getParameter( "Cust_"+selectedItems[0] ));
    String ccyPair = ResponseUtils.filter(request.getParameter( "CcyPair_"+selectedItems[0] ));
    String dealType = ResponseUtils.filter(request.getParameter( "DealType_"+selectedItems[0] ));
    String rate = ResponseUtils.filter(request.getParameter( "rate_"+selectedItems[0] ));
    String dltAmntStr = ResponseUtils.filter(request.getParameter( "DealtAmnt_"+selectedItems[0] ));
    String valueDateStr = ResponseUtils.filter(request.getParameter( "valueDate_"+selectedItems[0] ));
    String usrName = ResponseUtils.filter(request.getParameter( "UserName_"+selectedItems[0] ));
    String nameSpace = ResponseUtils.filter(request.getParameter( "nameSpace_"+selectedItems[0] ));
    valueDateObj = DateTimeFactory.newDate(valueDateStr, dateFormat);
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    try{
        valueDate = sdf.format(valueDateObj.asJdkDate());
    }catch(Exception e){
        System.out.println(" Exception Formatting Value Date: "+e);
    }
    String rejReason = ResponseUtils.filter(request.getParameter( "rejReason_"+selectedItems[0] ));
    if(dealAction.equals("Verify"))
    {
        wfMessage.setParameterValue(ISConstantsC.TRANSACTION_ID_WFKEY,tranId);
        wfMessage.setParameterValue("rate",rate);
        wfMessage.setParameterValue("ValueDate",valueDate);
        wfMessage.setParameterValue("dealtAmount",dltAmntStr);
        String userName = usrName+"@"+nameSpace;
        wfMessage.setParameterValue("fiUserName",userName);
        wfMessage.setEventName(ISConstantsC.TRADE_VERIFIED);
        ManualDealUpdateNotificationHandler handler = new ManualDealUpdateNotificationHandler();
        WorkflowMessage wMsg = (WorkflowMessage)handler.handle(wfMessage);
        if(MessageStatus.SUCCESS.equals(wMsg.getStatus()))
        {
            dealUpdateStatus = "true";
        }
        else
        {
            dealUpdateStatus = "false";
        }
    }
    else if(dealAction.equals("Reject"))
    {
        wfMessage.setParameterValue(ISConstantsC.TRANSACTION_ID_WFKEY,tranId);
        wfMessage.setParameterValue("rejectReason",rejReason);
        String userName = usrName+"@"+nameSpace;
        wfMessage.setParameterValue("fiUserName",userName);
        wfMessage.setEventName(ISConstantsC.TRADE_REJECTED);
        ManualDealUpdateNotificationHandler handler = new ManualDealUpdateNotificationHandler();
        WorkflowMessage wMsg = (WorkflowMessage)handler.handle(wfMessage);
        if(MessageStatus.SUCCESS.equals(wMsg.getStatus()))
        {
            dealUpdateStatus = "true";
        }
        else
        {
            dealUpdateStatus = "false";
        }
    }

%>
<form method="post" name="redirectorForm" action='PendingDealsDiaplay.jsp' >

    <input type="hidden" name="dateFrom" value="<%=dateFrom%>"/>
    <input type="hidden" name="dateTo" value="<%=dateTo%>"/>
    <%
        if (dealUpdateStatus.equals("false")) {
    %>
    <input type="hidden" name="status" value="failed"/>
    <%
        }
    %>


</form>
<script>
    document.forms[0].submit();
</script>


</body>