<%@ page import="TOPLink.Public.Expressions.Expression"%>
<%@ page import="TOPLink.Public.Expressions.ExpressionBuilder"%>
<%@ page import="com.integral.finance.fx.FXLeg"%>
<%@ page import="com.integral.finance.fx.FXPaymentParameters"%>
<%@ page import="com.integral.finance.trade.Trade"%>
<%@ page import="com.integral.finance.trade.TradeLeg"%>
<%@ page import="com.integral.query.QueryCriteria"%>
<%@ page import="com.integral.query.QueryCriteriaBuilder"%>
<%@ page import="com.integral.query.QueryCriteriaBuilderC"%>
<%@ page import="com.integral.query.QueryService"%>
<%@ page import="com.integral.query.QueryFactory"%>
<%@ page import="com.integral.session.IdcSessionContext"%>
<%@ page import="com.integral.session.IdcSessionManager"%>
<%@ page import="com.integral.time.DateTimeFactory"%>
<%@ page import="com.integral.time.IdcDate"%>
<%@ page import="com.integral.user.User"%>
<%@ page import="org.apache.struts.util.ResponseUtils"%>
<%@ page import="javax.servlet.http.HttpSession"%>
<%@ page import="javax.servlet.jsp.PageContext"%>
<%@ page import="java.text.DateFormat"%>
<%@ page import="java.util.ArrayList"%>
<%@ page import="java.util.Date"%>
<%@ page import="java.util.Iterator"%>
<%@ page import="com.integral.finance.dealing.fx.FXLegDealingPrice"%>
<%@ page import="com.integral.finance.dealing.Request"%>
<jsp:include page="/integral/admin/js/DateFormatValidation.js" />
<%@page buffer="100kb" autoFlush="false"%>
<html>
<head>
<style>
.header {
	background-color: #fffff1;
	color: #000000;
	font-size: 10pt;
	font-weight: bold;
	padding-left: 2px;
	padding-right: 2px;
	text-align: center;
}

.lt {
	background-color: #E9E5D9;
	color: #000000;
	font-size: 8pt;
	padding-left: 2px;
	padding-right: 2px;
}
</style>
</head>
<title>List Of Pending Deals</title>
<body>
<form name="PendingDealsDisplay" action="UpdatePendingDealData.jsp"
	method="post">
<table width="100%" cellpadding="0" cellspacing="0">
	<tr>
		<td>
	<tr>
		<td class="e">&nbsp;</td>
	</tr>
	<!--<tr><td class="e"><li><font color = 'red'><b>Manual Deal Update Not Successfull : Adaptor Response Already Sent!</b></font></td></tr>-->
	<tr>
		<td class="e">&nbsp;</td>
	</tr>
	</td>
	</tr>
</table>

<table>

	<%
		QueryService queryService = QueryFactory.getQueryService();
		HttpSession httpSession = request.getSession();
		User user = null;
		IdcSessionContext sessionContext = (IdcSessionContext) httpSession.getAttribute(IdcSessionContext.SESSION_CONTEXT);
		if (sessionContext != null) {
			user = (User) sessionContext.getUser();
		} else {
			out.println(" NO SESSION CONTEXT Present ");
		}
		if (user == null) {
			System.out.println(" PendingDealsDisplay : USER NULL ");
			return;
		}
		IdcSessionManager.getInstance().setSessionContext(sessionContext);
		pageContext.setAttribute("user", user, PageContext.SESSION_SCOPE);
		String reqContPath = request.getContextPath();
		DateFormat dateFormat = user.getDisplayPreference().getDateFormat();
		String dateFormatStr = user.getDisplayPreference().getDateFormatPattern();
		String dateTimeFormat = user.getDisplayPreference().getDateTimeFormatPattern();
		Expression finalExp = null;
		Expression fromDateExp = null;
		Expression toDateExp = null;
		Expression nsExp = null;
		Expression tranIdExp = null;
		Expression stateExp = null;
		Expression lpOrgExp = null;
		IdcDate fromDate = null;
		IdcDate toDate = null;
		ArrayList result = new ArrayList();
		QueryCriteriaBuilder builder = new QueryCriteriaBuilderC();
		QueryCriteria criteria = builder.get("");
		String dateFrom = ResponseUtils.filter(request.getParameter("dateFrom"));
		String dateTo = ResponseUtils.filter(request.getParameter("dateTo"));
		String transactionId = ResponseUtils.filter(request.getParameter("id"));
		String format = ResponseUtils.filter(request.getParameter("format"));
		stateExp = new ExpressionBuilder().get("workflowStateMap").get("state").get("shortName").equal("TSPENDING");
		if (transactionId != null && !(transactionId).trim().equals("")) {
			tranIdExp = new ExpressionBuilder().get("transactionID").equal(transactionId);
		}
		if (dateFrom != null && !(dateFrom).trim().equals("")) {
			fromDate = DateTimeFactory.newDate(dateFrom, dateFormat);
		}
		if (fromDate != null)
			fromDateExp = new ExpressionBuilder().get("tradeDate").greaterThanEqual(fromDate);
		if (dateTo != null && !(dateTo).trim().equals("")) {

			toDate = DateTimeFactory.newDate(dateTo, dateFormat);
		}

		if (toDate != null)
			toDateExp = new ExpressionBuilder().get("tradeDate").lessThan(toDate.nextDate());
		if (fromDateExp != null)
			finalExp = fromDateExp;

		if (toDateExp != null)
			finalExp = finalExp.and(toDateExp).and(stateExp);

		if (tranIdExp != null)
			finalExp = finalExp.and(tranIdExp);
		criteria.setExpression(finalExp);
		result = (ArrayList) queryService.findAll(com.integral.finance.trade.TradeC.class, criteria);
		if (result == null) {
			out.println("NO PENDING TRADES FOUND ");
			return;
		}
		if (result.size() == 0) {
			out.println("NO PENDING TRADES");
		} else {
			pageContext.setAttribute("tableResult", result,
			PageContext.SESSION_SCOPE);
			//Check if the result is to be displayed in CSV or HTML format
			if ("CSV".equals(format)) {
	%>
	<jsp:forward page="PendingDealDownload.jsp"></jsp:forward>
	<%
	} else {
	%>
	<tr>
		<td colspan=4 class="header"><b>Pending Deals</b></td>
	</tr>
	<tr class="header">
		<td>Choose Deal</td>
		<td>Deal Id</td>
		<td>TradeDate</td>
		<td>LP</td>
		<td>Customer</td>
		<td>User</td>
		<td>CurrPair</td>
		<td>BuyAmount</td>
		<td>SellAmount</td>
		<td>BuySell</td>
		<td>DealType</td>
		<td>Rate</td>
		<td>ValueDate</td>
		<td>RejectReason</td>
	</tr>

	<%
			for (Iterator itr = result.iterator(); itr.hasNext();) {
			Trade trade = (Trade) itr.next();
			String tranId = null;
			Date trdDate = null;
			Date execTime = null;
			String lpName = null;
			String custName = null;
			String userName = null;
			String nameSpace = null;
			String ccyPair = null;
			double dltAmnt = 0.0;
			double buyAmount = 0.0;
			double sellAmount = 0.0;
			double rate = 0.0;
			Date valDateStr = null;
			String dealType = null;
			TradeLeg tradeLeg = null;
			FXLeg fxLeg = null;
			tranId = trade.getTransactionID();
			trdDate = trade.getTradeDate().asJdkDate();
			lpName = trade.getCounterpartyB().getOrganization().getShortName();
			custName = trade.getCounterpartyA().getOrganization().getShortName();
			userName = trade.getEntryUser().getShortName();
			nameSpace = trade.getNamespace().getShortName();
			Iterator it = trade.getTradeLegs().iterator();
			if (it.hasNext()) {
				tradeLeg = (TradeLeg) it.next();
				fxLeg = (FXLeg) tradeLeg;
			}
			FXPaymentParameters fxP = fxLeg.getFXPayment();
			ccyPair = fxP.getFXRate().getCurrencyPair().getName();
			
			 if(fxP.isDealtCurrency1())
			 dltAmnt =  fxP.getCurrency1Amount();
			 else
			 dltAmnt =  fxP.getCurrency2Amount();

			buyAmount = getBuyAmount(trade);
			sellAmount = getSellAmount(trade); 
			dealType = fxP.getTenor().getName();
			rate = fxP.getFXRate().getRate();
			valDateStr = fxP.getValueDate().asJdkDate();
			String encryptId = trade.getEncryptedObjectID();
			String buyOrSell = "Sell";
			if (fxP.isBuyingCurrency1())
				buyOrSell = "Buy";
			else
				buyOrSell = "Sell";
	%>
	<%!
	double getBuyAmount(Trade trade) {
		Request req = trade.getRequest();
		double buyAmt = 0.0;
		FXLegDealingPrice fxLegDealingPrice = (FXLegDealingPrice) req.getRequestPrice("singleLeg");
		if (fxLegDealingPrice == null)
			fxLegDealingPrice = (FXLegDealingPrice) req.getRequestPrice("nearLeg");	
		if (fxLegDealingPrice != null
				&& fxLegDealingPrice.isBuyingDealtCurrency()) {
			buyAmt = getDealtAmount(fxLegDealingPrice);
		} else {
			buyAmt = getSetlledAmnt(fxLegDealingPrice);
		}
		return buyAmt;
	}

	double getDealtAmount(FXLegDealingPrice fxLegDealingPrice) {
		double dealtAmt = 0;
		String dealtCcyProperty = fxLegDealingPrice.getDealtCurrencyProperty();
		String CCY2 = "currency2";
		dealtAmt = fxLegDealingPrice.getFXPayment().getCurrency1Amount();
		if (dealtAmt == 0.0) {
			dealtAmt = fxLegDealingPrice.getDealtAmount();
		}
		if (CCY2.equalsIgnoreCase(dealtCcyProperty)) {
			dealtAmt = fxLegDealingPrice.getFXPayment().getCurrency2Amount();
			if (dealtAmt == 0.0) {
				dealtAmt = fxLegDealingPrice.getDealtAmount();
			}
		}
		return dealtAmt;
	}

	double getSetlledAmnt(FXLegDealingPrice fxLegDealingPrice) {
		double settleAmt = 0;
		String dealtCcyProperty = fxLegDealingPrice.getDealtCurrencyProperty();
		String CCY2 = "currency2";
		settleAmt = fxLegDealingPrice.getFXPayment().getCurrency2Amount();
		if (settleAmt == 0.0) {
			settleAmt = fxLegDealingPrice.getAcceptedSettledAmount();
		}
		if (CCY2.equalsIgnoreCase(dealtCcyProperty)) {
			settleAmt = fxLegDealingPrice.getFXPayment().getCurrency1Amount();
			if (settleAmt == 0.0) {
				settleAmt = fxLegDealingPrice.getAcceptedSettledAmount();
			}
		}
		return settleAmt;
	}
	
	double getSellAmount(Trade trade){
		Request req = trade.getRequest();
		double sellAmnt = 0.0;
		FXLegDealingPrice fxLegDealingPrice = (FXLegDealingPrice) req.getRequestPrice("singleLeg");
		if(fxLegDealingPrice == null)
			fxLegDealingPrice = (FXLegDealingPrice) req.getRequestPrice("nearLeg");
		if(fxLegDealingPrice != null && fxLegDealingPrice.isBuyingDealtCurrency()){
			sellAmnt = getSetlledAmnt(fxLegDealingPrice);
         }
		else{
			sellAmnt = getDealtAmount(fxLegDealingPrice);
		}
		return sellAmnt;
	}
	
	%>


	<tr class="lt">
		<td><input type="radio" name="selectedItems" value=<%=encryptId%>></td>
		<td><%=tranId%></td>
		<td><%=trdDate%></td>
		<td><%=lpName%></td>
		<td><%=custName%></td>
		<td><%=userName%></td>
		<td><%=ccyPair%></td>
		<td><%=buyAmount%></td>
		<td><%=sellAmount%></td>
		<td><%=buyOrSell%></td>
		<td><%=dealType%></td>
		<td><input type="text" name='<%="rate_"+encryptId%>' size="15"
			value="<%=rate%>" /></td>
		<td><input type="text" name='<%="valueDate_"+encryptId%>'
			size="15" value="<%=dateFormat.format(valDateStr)%>" /></td>
		<td><input type="textArea" name='<%="rejReason_"+encryptId%>'
			size="20" value="" COLS=40 ROWS=6 /></td>
		<input type="hidden" name='<%="tranId_"+encryptId%>'
			value="<%=tranId%>" />
		<input type="hidden" name='<%="LP_"+encryptId%>' value="<%=lpName%>" />
		<input type="hidden" name='<%="Cust_"+encryptId%>'
			value="<%=custName%>" />
		<input type="hidden" name='<%="CcyPair_"+encryptId%>'
			value="<%=ccyPair%>" />
		<input type="hidden" name='<%="DealType_"+encryptId%>'
			value="<%=dealType%>" />
		<input type="hidden" name='<%="BuyAmnt_"+encryptId%>'
			value="<%=buyAmount%>" />
		<input type="hidden" name='<%="SellAmnt_"+encryptId%>'
			value="<%=sellAmount%>" />
		<input type="hidden" name='<%="DealtAmnt_"+encryptId%>'
			value="<%=dltAmnt%>" />	                             
		<input type="hidden" name='<%="UserName_"+encryptId%>'
			value="<%=userName%>" />
		<input type="hidden" name='<%="nameSpace_"+encryptId%>'
			value="<%=nameSpace%>" />

	</tr>
	<%
			}
			}
		}
	%>

	<input type="hidden" name="dealAction" value="testValTorem" />
	<input type="hidden" name="id" value="<%=transactionId%>" />
	<input type="hidden" name="dateFrom" value="<%=dateFrom%>" />
	<input type="hidden" name="dateTo" value="<%=dateTo%>" />

</table>
</form>
<table width="100%" cellpadding="0" cellspacing="0" class="outl2">
	<tr>
		<td class="f">&nbsp; <input type=submit class="b2" value="Verify"
			onClick="verifyDeal();" />&nbsp;&nbsp; <input type=submit class="b2"
			value="Reject" onclick="rejectDeal();" />&nbsp;&nbsp;</td>
		<td class="f" colspan="4">&nbsp;</td>
	</tr>
</table>

<script>
function verifyDeal() {
    var selectedRadioButt='';
    if (document.forms[0].selectedItems==null) {
        alert('No Radio button');
        return;
    }
    /*if (document.forms[0].selectedItems.value==null) {
        alert('Valus is null');
        if (document.forms[0].selectedItems==null) {
            alert('No Radio button');
            return;
         }
        }*/
    /* if(document.forms[0].selectedItems.value==null)
   if (document.forms[0].selectedItems==null) {
       alert('No Radio button');
       return;
    }*/
    if (document.forms[0].selectedItems.value!=null)  {
        if (!(document.forms[0].selectedItems.checked)) {
            alert('Please select atleast one deal');
            return;
        }
        else {
            //alert();
            selectedRadioButt= document.forms[0].selectedItems.value;
        }
    }
    else {
        for (i=0;i<document.forms[0].selectedItems.length;i++) {
            if (document.forms[0].selectedItems[i].checked) {
                selectedRadioButt = document.forms[0].selectedItems[i].value;
            }
        }
        if (selectedRadioButt == '')
            return;
    }
    var valDate = document.forms[0].elements('valueDate_'+selectedRadioButt);
    var rate = document.forms[0].elements('rate_'+selectedRadioButt);
    var isValidValdate = isDate(valDate.value,'<%=dateFormatStr%>');
<%--
    if (!(isDate(valDate.value,'<%=dateFormatStr%>'))){
        alert('Please Enter a valid date for the selected Deal in Format  '+'<%=dateFormatStr%>');
        return;
    }
--%>
    var ratesVal =rate.value;
    /*if ((isNaN(ratesVal))){
        alert('Please Enter a Valid number ');
        return;
    }*/
    if (ratesVal==''){
        alert('Please enter in the rate field ');
        return;
    }
/*
    if (ratesVal.length>8){
        alert('The number of characters can be 8 ');
        return;
    }
*/
    document.forms[0].dealAction.value="Verify";
    document.forms[0].action = "UpdatePendingDealData.jsp";
    document.forms[0].submit();
}
function rejectDeal() {
    var selectedRadioButt='';
    if (document.forms[0].selectedItems==null) {
        alert('No Radio button');
        return;
    }
    else if (document.forms[0].selectedItems.value!=null)  {
        if (!(document.forms[0].selectedItems.checked)) {
            alert('Please select atleast one deal');
            return;
        }
        else {
            selectedRadioButt= document.forms[0].selectedItems.value;
        }
    }
    else {
        for (i=0;i<document.forms[0].selectedItems.length;i++) {
            if (document.forms[0].selectedItems[i].checked) {
                selectedRadioButt = document.forms[0].selectedItems[i].value;
            }
        }
        if (selectedRadioButt == '')
            return;
    }
    var valDate = document.forms[0].elements('valueDate_'+selectedRadioButt);
    var rate = document.forms[0].elements('rate_'+selectedRadioButt);
    var rejReason = document.forms[0].elements('rejReason_'+selectedRadioButt);
    var isValidValdate = isDate(valDate.value,'<%=dateFormatStr%>');
    if (!(isDate(valDate.value,'<%=dateFormatStr%>'))){
        return;
    }
    var ratesVal =rate.value;
    var rejReasonVal =rejReason.value;
    if ((isNaN(ratesVal))){
        alert('Please Enter a Valid number ');
        return;
    }
    if (ratesVal==''){
        alert('Please enter in the rate field ');
        return;
    }
    if (ratesVal.length>8){
        alert('The number of characters can be 8 ');
        return;
    }
    if (rejReasonVal=='') {
        alert('Please give a Reject Reason');
        return;
    }
    if (rejReasonVal.length>100) {
        alert('The number of characters for rejection cannot be more than 100');
        return;
    }
    document.forms[0].dealAction.value="Reject";
    document.forms[0].action ="UpdatePendingDealData.jsp";
    document.forms[0].submit();
}
</script>

</body>
</html>