<%@ page import= "com.integral.is.common.ISConstantsC,com.integral.log.Log" %>
<%@ page import="com.integral.is.common.admin.ManualUpdateHandlerFactory" %>
<%@ page import="com.integral.log.LogFactory" %>
<%@ page import="com.integral.message.MessageFactory" %>
<%@ page import="com.integral.message.MessageHandler" %>
<%@ page import="com.integral.message.MessageStatus"%>
<%@ page import="com.integral.message.WorkflowMessage"%>
<%@ page import="com.integral.session.IdcSessionContext"%>
<%@ page import="com.integral.session.IdcSessionManager"%>
<%@ page import="com.integral.user.User"%>
<%@ page import="com.integral.user.UserFactory"%>
<%@ page import="org.apache.struts.util.ResponseUtils"%>
<%@ page import="java.util.StringTokenizer"%>
<%!
    public static final String USER_DELIMITTER="~";
    public static final String NAMESPACE_DELIMITTER="@";
    public static final String RATE_DELIMITTER="#";
    public static final String VALDATE_DELIMITTER="_";
    public static final String FARVALDATE_DELIMITTER="!";
    public static final String REJECTIONREASON_DELIMITTER="*";
    public static final String LEGALENTITY_DELIMITTER="^";
    public static final String TWOENTRIES_SEPERATOR=",";
    Log log = LogFactory.getLog("PendingDeals");
%>
<%
    WorkflowMessage wfMessage = null;
    try {
        String dealUpdateStatus = "";
        //Comma seperated list of multiple dealIds...
        String dealList = ResponseUtils.filter(request.getParameter("valuesRequiredOnIS"));
        String dealAction = ResponseUtils.filter(request.getParameter("dealAction"));
        String namespaceSN = ResponseUtils.filter(request.getParameter("namespaceSN"));
        namespaceSN = ( namespaceSN != null && namespaceSN.trim().length() > 0 ) ? namespaceSN.trim() : null;
        if ("Verify".equals(dealAction))
        {
            StringBuffer status = new StringBuffer();
            StringTokenizer stk = new StringTokenizer(dealList , TWOENTRIES_SEPERATOR);
            while(stk.hasMoreTokens()){
                String token = stk.nextToken();
                wfMessage = MessageFactory.newWorkflowMessage();
                setVerifyParameters(wfMessage, token);
                if( namespaceSN != null ) {
                    wfMessage.setParameterValue( "namespace", namespaceSN );
                    wfMessage.setParameterValue(ISConstantsC.NAMESPACE_SN, namespaceSN);
                }

                String txID = (String)wfMessage.getParameterValue(ISConstantsC.TRANSACTION_ID_WFKEY);
                wfMessage.setParameterValue(ISConstantsC.TRANSACTION_ID, txID);

                MessageHandler handler = ManualUpdateHandlerFactory.getFactory().getHandler( wfMessage );
                WorkflowMessage wMsg = (WorkflowMessage) handler.handle(wfMessage);

                if (MessageStatus.SUCCESS.equals(wMsg.getStatus())) {
                    status.append(txID +"~" + "true,");
                } else {
                    status.append(txID +"~" + "false,");
                }
            }
            dealUpdateStatus = status.toString();
        }

        else if ("Reject".equals(dealAction))
        {
            StringBuffer status = new StringBuffer();
            StringTokenizer stk = new StringTokenizer(dealList , TWOENTRIES_SEPERATOR);
            while(stk.hasMoreTokens()){
                String token = stk.nextToken();
                wfMessage = MessageFactory.newWorkflowMessage();
                setRejectParameters(wfMessage, token);
                if( namespaceSN != null ) {
                    wfMessage.setParameterValue( "namespace", namespaceSN );
                    wfMessage.setParameterValue(ISConstantsC.NAMESPACE_SN, namespaceSN);
                }

                String txID = (String)wfMessage.getParameterValue(ISConstantsC.TRANSACTION_ID_WFKEY);
                wfMessage.setParameterValue(ISConstantsC.TRANSACTION_ID, txID);

                MessageHandler handler = ManualUpdateHandlerFactory.getFactory().getHandler( wfMessage );
                WorkflowMessage wMsg = (WorkflowMessage) handler.handle(wfMessage);
                if (MessageStatus.SUCCESS.equals(wMsg.getStatus())) {
                    status.append(txID +"~" + "true,");
                } else {
                    status.append(txID +"~" + "false,");
                }
            }
            dealUpdateStatus = status.toString();
        }

        else if ("Confirm".equals(dealAction))
        {
            StringBuffer status = new StringBuffer();
            StringTokenizer stk = new StringTokenizer(dealList , TWOENTRIES_SEPERATOR);
            while(stk.hasMoreTokens()){
                String token = stk.nextToken();
                int id = token.indexOf(USER_DELIMITTER);
                String txID = token.substring(0,id);
                String fiUserName = token.substring(id+1);
                wfMessage = MessageFactory.newWorkflowMessage();
                wfMessage.setTopic(ISConstantsC.MSG_TOPIC_REQUEST);
                wfMessage.setEventName(ISConstantsC.MSG_EVENT_TRADE_CONFIRMED);

                wfMessage.setParameterValue(ISConstantsC.TRANSACTION_ID, txID);
                if( namespaceSN != null ) {
                    wfMessage.setParameterValue( "namespace", namespaceSN );
                    wfMessage.setParameterValue(ISConstantsC.NAMESPACE_SN, namespaceSN);
                }

                setContext(fiUserName);
                MessageHandler handler = ManualUpdateHandlerFactory.getFactory().getHandler( wfMessage );
                handler.handle(wfMessage);
                if(MessageStatus.SUCCESS.equals(wfMessage.getStatus()))
                {
                    status.append(txID +"~" + "true,");
                }
                else
                {
                    status.append(txID +"~" + "false,");
                }
            }
            dealUpdateStatus = status.toString();
        }

        else if ("UpdateLE".equals(dealAction))
        {
            StringBuffer status = new StringBuffer();
            StringTokenizer stk = new StringTokenizer(dealList , TWOENTRIES_SEPERATOR);
            while(stk.hasMoreTokens()){
                String token = stk.nextToken();
                wfMessage = MessageFactory.newWorkflowMessage();
                setLeUpdateParameters(wfMessage, token);
                if( namespaceSN != null ) {
                    wfMessage.setParameterValue( "namespace", namespaceSN );
                    wfMessage.setParameterValue(ISConstantsC.NAMESPACE_SN, namespaceSN);
                }
                String txID = (String)wfMessage.getParameterValue(ISConstantsC.TRANSACTION_ID_WFKEY);
                wfMessage.setParameterValue(ISConstantsC.TRANSACTION_ID, txID);

                MessageHandler handler = ManualUpdateHandlerFactory.getFactory().getHandler( wfMessage );
                wfMessage = (WorkflowMessage)handler.handle(wfMessage);

                if(MessageStatus.SUCCESS.equals(wfMessage.getStatus())){
                    status.append(txID +"~" + "true,");
                }
                else{
                    status.append(txID +"~" + "false,");
                }
            }
            dealUpdateStatus = status.toString();
        }

        StringBuffer responseString = new StringBuffer();
        if (log.isDebugEnabled())
            log.debug("PendingDealUpdateIS.jsp: Validation succeeded");
        responseString.append("BOF");
        responseString.append(dealUpdateStatus);
        responseString.append("EOF\n");
        String finalString = responseString.toString();
        out.write(finalString.trim());
        response.flushBuffer();
    }catch (Exception e) {
        log.error("PendingDealUpdateIS.jsp: Exception :  " , e);
    }
%>
<%!
    /**
     * //Set the session context as the context of the fi-user, so that deal instance can be retrived in the proceeding wrkFlow
     * @param  userName of the customer
     */
    void setContext(String userName){
        try{
            User user = UserFactory.getUser(userName);
            IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext(user);
            IdcSessionManager.getInstance().setSessionContext(ctx);
        }catch(Exception e){
            log.error(" PendingDealUpdateIS.jsp.setContext : Exception Setting Context For : "+userName +" : ", e);
        }
    }

    void setVerifyParameters(WorkflowMessage wfm, String token){
        int id1 = token.indexOf(USER_DELIMITTER);
        String txID = token.substring(0, id1);
        int id2 = token.indexOf(RATE_DELIMITTER);
        String userName = token.substring(id1+1, id2);
        int id3 = token.indexOf(VALDATE_DELIMITTER);
        String rate = token.substring(id2+1, id3);
        int id4 = token.indexOf(FARVALDATE_DELIMITTER);
        String nearValueDate = null;
        String farValueDate = null;
        if(id4 <= 0)
        {
            nearValueDate = token.substring(id3+1);
        }
        else
        {
            nearValueDate = token.substring(id3+1, id4);
            farValueDate = token.substring(id4+1);
        }
        wfm.setParameterValue(ISConstantsC.TRANSACTION_ID_WFKEY, txID);
        wfm.setParameterValue("rate", rate);
        if(nearValueDate != null)
        {
            wfm.setParameterValue("valueDate", nearValueDate);
        }
        if(farValueDate != null)
        {
            wfm.setParameterValue("farValueDate", farValueDate);
        }
        wfm.setParameterValue("fiUserName",userName);
        wfm.setEventName(ISConstantsC.TRADE_VERIFIED);
    }

    void setRejectParameters(WorkflowMessage wfm, String token){
        int id1 = token.indexOf(USER_DELIMITTER);
        String txID = token.substring(0, id1);
        int id2 = token.indexOf(RATE_DELIMITTER);
        String userName = token.substring(id1+1, id2);
        int id3 = token.indexOf(REJECTIONREASON_DELIMITTER);
        // ignore the rate
        String rejectReason = token.substring(id3+1);
        wfm.setParameterValue(ISConstantsC.TRANSACTION_ID_WFKEY, txID);
        wfm.setParameterValue("rejectReason", decode(rejectReason));
        wfm.setParameterValue("fiUserName",userName);
        wfm.setEventName(ISConstantsC.TRADE_REJECTED);
    }

    void setLeUpdateParameters(WorkflowMessage wfm, String token){
        int id1 = token.indexOf(USER_DELIMITTER);
        String txID = token.substring(0, id1);
        int id2 = token.indexOf(LEGALENTITY_DELIMITTER);
        String userName = token.substring(id1+1, id2);
        String leShortName = token.substring(id2+1);
        wfm.setParameterValue(ISConstantsC.TRANSACTION_ID_WFKEY, txID);
        wfm.setEventName(ISConstantsC.MSG_EVENT_UPDATE_LE);
        wfm.setParameterValue(ISConstantsC.LE_SHORTNAME_WFKEY, leShortName);
        wfm.setParameterValue("fiUserName",userName);

    }

    String decode(String rejectReason){
        String schars[] = {"<",">",",","%","\"","'", "&", "?"};
        String codes[] = {"s_LT", "s_GT", "s_CM", "s_PR", "s_DQ", "s_SQ", "s_AM", "s_QS"};
        for(int i=0; i<schars.length ; i++)
        {
            rejectReason = rejectReason.replace(codes[i], schars[i]);
        }
        return rejectReason;
    }

%>