<%@ page import="com.integral.session.IdcSessionContext"%>
<%@ page import="com.integral.time.DateTimeFactory"%>
<%@ page import="com.integral.jsp.framework.taglib.IdcFormat"%>
<html>
<title>Query Pending Deals</title>
<body>
<%
   String tradeDateStr = IdcFormat.doFormat( pageContext, DateTimeFactory.newDateTime(), null, null );
   String tradeDate= tradeDateStr.substring(0,tradeDateStr.indexOf(" "));
    HttpSession hSession = request.getSession(false);
    IdcSessionContext sessionContext = (IdcSessionContext) hSession.getAttribute(IdcSessionContext.SESSION_CONTEXT);
        if(sessionContext == null)
        {
        out.println(" QueryPendingDeal : Login Failed : Login Again ");
    }
    else

    {%>


<form action="/isClient/integral/admin/pendingDeals/PendingDealsDiaplay.jsp">
    <table>
        <tr>
            <td colspan=4><b>Find Pending Deals</b></td>
        </tr>
        <tr>
            <td colspan=4><b>Deal ID</b></td>
            <td><input type="text" name="id"/></td>
        </tr>
        <tr>
            <td colspan=4><b>Date From</b></td>
            <td><input type="text" name="dateFrom" value=<%=tradeDate%>>yyyy/mm/dd</td>
        </tr>

        <tr>
            <td colspan=4><b>Date To</b></td>
            <td><input type="text" name="dateTo" value=<%=tradeDate%>>yyyy/mm/dd</td>
        </tr>
        <tr>
            <td colspan=4><b>Format</b></td>
            <td><select name="format">
                <option value="HTML">HTML</option>
                <option value="CSV">CSV</option>
            </select>
            </td>
        </tr>
    </table>
    <input type="submit" value="display"/>
</form>
<%}
%>
</body>
</html>