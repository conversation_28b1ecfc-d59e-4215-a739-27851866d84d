<%@ page import="com.integral.finance.fx.FXLeg"%>
<%@ page import="com.integral.finance.fx.FXPaymentParameters"%>
<%@ page import="com.integral.finance.trade.Trade"%>
<%@ page import="com.integral.finance.trade.TradeLeg"%>
<%@ page import="com.integral.time.IdcSimpleDateFormat"%>
<%@ page import="com.integral.user.User"%>
<%@ page import="javax.servlet.http.HttpServletResponse"%>
<%@ page import="javax.servlet.jsp.PageContext"%>
<%@ page import="java.util.ArrayList"%>
<%@ page import="java.util.Date"%>
<%@ page import="java.util.Iterator"%>
<%!
    public String getDateTime(Date dt, User aUser, int pattern) {
        String dateFormatStr = "dd-MMM-yyyy";
        String timeFormatStr = "HH:mm:ss aa zzz";
        IdcSimpleDateFormat sdf = new IdcSimpleDateFormat();
        sdf.setTimeZone(aUser.getDisplayPreference().getTimeZone());
        if (pattern == 1)
            sdf.applyPattern(dateFormatStr);
        else
            sdf.applyPattern(timeFormatStr);
        String currentDate = "";
        if (dt != null) {
            currentDate = sdf.format(dt);
        }
        return currentDate;
    }

%>
<%

    ArrayList result = (ArrayList)pageContext.getAttribute("tableResult",PageContext.SESSION_SCOPE);
    User user = (User)pageContext.getAttribute("user",PageContext.SESSION_SCOPE);
    StringBuffer tableData = new StringBuffer();
    response.reset();
    response.setStatus(HttpServletResponse.SC_OK);
    response.setHeader("Content-Disposition","filename=" + "PendingDealReport"+System.currentTimeMillis()+".csv");
    response.setContentType("application/msexcel");
    StringBuffer header = new StringBuffer();
    header.append("Deal Id,").append("Trade Date,").append("LP,").append("FI ORG,").append("User,").append(
            "Currency Pair,").append("Dealt Amount,").append("Buy/Sell,").append("Deal Type,");
    out.clear();
    tableData.append(header).append("\n");
    for(Iterator itr = result.iterator(); itr.hasNext();)
    {
        Trade trade = (Trade)itr.next();
        String dltAmount;
        TradeLeg tradeLeg = null;
        FXLeg fxLeg = null;
        Iterator it = trade.getTradeLegs().iterator();
        if(it.hasNext()){
            tradeLeg = (TradeLeg)it.next();
            fxLeg = (FXLeg)tradeLeg;
        }
        FXPaymentParameters fxP = fxLeg.getFXPayment();
        if(fxP.isDealtCurrency1()){
            dltAmount =  String.valueOf(fxP.getCurrency1Amount());
        }
        else{
            dltAmount =   String.valueOf(fxP.getCurrency2Amount());
        }
        String buyOrSell = "Sell";
        if(fxP.isBuyingCurrency1())
            buyOrSell = "Buy";
        tableData.append("\"").append(trade.getTransactionID()).append("\",");
        tableData.append("\"").append(getDateTime(trade.getTradeDate().asJdkDate(),user, 1)).append("\",");
        tableData.append("\"").append(trade.getCounterpartyB().getOrganization().getShortName()).append("\",");
        tableData.append("\"").append(trade.getCounterpartyA().getOrganization().getShortName()).append("\",");
        tableData.append("\"").append(trade.getEntryUser().getShortName()).append("\",");
        tableData.append("\"").append(fxP.getFXRate().getCurrencyPair().getName()).append("\",");
        tableData.append("\"").append(dltAmount).append("\",");
        tableData.append("\"").append(buyOrSell).append("\",");
        tableData.append("\"").append(fxP.getTenor().getName()).append("\",").append("\n");
    }
    out.println(tableData);
%>