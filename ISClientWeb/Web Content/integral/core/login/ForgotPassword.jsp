
<%@ page language="java" %>
<%@ page import="com.integral.user.User" %>
<%@ page import="com.integral.user.UserFactory" %>
<%@ page import="org.apache.struts.util.ResponseUtils" %>
<%@ page import="com.integral.message.Message" %>
<%@ page import="com.integral.log.Log" %>
<%@ page import="com.integral.log.LogFactory" %>
<%@ page import="com.integral.xml.XMLConvertor" %>
<%@ page import="com.integral.xml.mapping.XMLException" %>
<%@ page import="com.integral.security.CryptC" %>
<%@ page import="java.io.StringWriter" %>
<%@ page import="com.integral.message.WorkflowMessage" %>
<%@ page import="java.io.StringReader" %>
<%@ page import="com.integral.message.MessageFactory" %>
<%@ page import="java.rmi.RemoteException" %>
<%@ page import="java.io.IOException" %>
<%@ page import="java.io.PrintWriter" %>
<%@ page import="com.integral.message.MessageStatus" %>
<%@ page import="java.util.Collection" %>
<%@ page import="java.util.Iterator" %>
<%@ page import="java.util.Map" %>
<%@ page import="java.util.HashMap"%>
<%@ page import="com.integral.jsp.JSPApplication" %>
<%@ page import="com.integral.session.IdcSessionContext" %>
<%@ page import="com.integral.session.IdcSessionManager" %>
<%@ page import="com.integral.xml.XMLConvertorFactory" %>


<%! 
	static Log log = LogFactory.getLog("com.integral.login.loginMain"); 
	User user;
   
	private static String getXML(Message wfMsgObj) 
	{
		String xml = null;

		try {	
			StringWriter out = new StringWriter();
			String mapping = "IntegrationServer";
			XMLConvertorFactory.getXMLConvertor().convertToXML(out, wfMsgObj, mapping);
			xml = out.toString();
			return xml;
		} catch (XMLException xe) {
				log.warn("forgotPassword.jsp: Caught XMLException in getXML. Message is - "+ xe.getDetailMessage());
		} catch (Exception e) {
				log.debug("forgotPassword.jsp: Caught exception in getXML.", e);
		}

		return xml;
	}
	
	private Message getMessage(String wfMsgStr,String event,String topic) {
        Message wfMsg = null;
        Message errorMessageHolder = null;
		StringReader reader = new StringReader(wfMsgStr);
		String mapping = "IntegrationServer";
		Object obj = null;
		errorMessageHolder = MessageFactory.newMessage();
		try {
			obj = XMLConvertorFactory.getXMLConvertor().convertFromXML(reader, mapping, errorMessageHolder);
		} catch (Exception ex) {
			log.warn("forgotPassword.jsp : Exception while converting xml to object" , ex);
            String errorStr = "XML_PROCESSING_ERROR";
            return getXMLErrorWorkflowMsg(errorStr,event,topic);
        }
		
		wfMsg = (Message) obj;
		return wfMsg;
    }
	
	private User getUser(String userName, String organization) {
		userName = userName.trim();
		organization = organization.trim();
		User user = UserFactory.getUser(userName + "@" + organization);
		return user;
	}

	private WorkflowMessage validateUser(User user,String event,String topic) {
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		msg.setStatus(MessageStatus.SUCCESS);
		msg.setEventName(event);
		msg.setTopic(topic);
		if (user == null) {
			msg.setStatus(MessageStatus.FAILURE);
	        msg.addError("INCORRECT USER NAME");
		} else {
			boolean accountLockOut = (User.ACCOUNT_STATUS_ACCOUNT_DISABLED == user.getAccountStatus()) ? true : false;
			if (accountLockOut) {
				logMessage("Account disabled"); 
				msg.setStatus(MessageStatus.FAILURE);
		        msg.addError("ACCOUNT DISABLED");
			}
		}
		return msg;
	}
	
	private void sendMessage(String event, String topic,Map messages, HttpServletResponse response) {

		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		msg.setStatus(MessageStatus.SUCCESS);
		msg.setEventName(event);
		msg.setTopic(topic);
		if (messages != null) {
			Iterator it = messages.keySet().iterator();
			while (it.hasNext()) {
				String key = (String) it.next();
				Object message = messages.get(key);
				msg.setParameterValue(key, message);
			}
		}
		
		printResponse(msg,response);

	}

	private void printResponse(WorkflowMessage message,HttpServletResponse response) {
		try {
			String strMsg = getXML(message);
			PrintWriter printWriter = response.getWriter();
			printWriter.write(strMsg);
		}catch(Exception e){
			e.printStackTrace();
		}

	}
	
	private void logMessage(String message)
	{
		log.warn("forgotPassword.jsp : "+message);
	}
	
	
	/**
     * returns   workflowmessage object with xml error string as input
     */
    private WorkflowMessage getXMLErrorWorkflowMsg(String errmsg,String event, String topic) {
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setEventName(event);
        msg.setTopic(topic);
        msg.setStatus(MessageStatus.FAILURE);
        msg.addError(errmsg);
        return msg;

    }
    
    /**
     * returns   workflowmessage object with error collection as input
     */
    private WorkflowMessage getXMLErrorWorkflowMsg(Collection errors,String event,String topic) {
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setEventName(event);
        msg.setTopic(topic);
        msg.setStatus(MessageStatus.FAILURE);
        msg.addErrors(errors);
        return msg;

    }
    
    private void sendErrorMessage(HttpServletResponse response,String errorCode, String errorMessage,String event,String topic) {

		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
	    msg.setEventName(event);
        msg.setTopic(topic);
		msg.setStatus(MessageStatus.FAILURE);
		msg.setError(errorCode, errorMessage);
		printResponse(msg,response);
    }
    
    private void initializeSession(HttpSession session,HttpServletRequest request, User user, Object userName, Object jspApplication, Object organization) {
		try {

			session = request.getSession(true);
			
			IdcSessionContext ctxt = IdcSessionManager.getInstance().getSessionContext(user);
			session.setAttribute(IdcSessionContext.SESSION_CONTEXT, ctxt);
			session.setAttribute(IdcSessionContext.USER_KEY, user);

			if (user != null) {
				if (log.isDebugEnabled()) {
					log.debug("forgotPassword.initializeSession : setting user information in session");
				}
				session.setAttribute(IdcSessionContext.USER_NAME, user.getShortName());
			}
			
			session.setAttribute("user", user);
			session.setAttribute("userName", userName);
			session.setAttribute("namespace", organization);
			session.setAttribute("jspApplication", jspApplication);
			session.setAttribute(JSPApplication.SESSION_ATTRIBUTE,jspApplication);
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
    
    /**
     * sends response to the client
     */
    private void send(HttpServletResponse response, String message) throws IOException {
       PrintWriter out = response.getWriter();
        out.println(message);
        out.flush();
        out.close();
    }
%>


<%
	
	String wfMsgStr = request.getParameter("msg");
	if(wfMsgStr == null) {
    	log.warn("forgotPassword.jsp : Received null message from client " + wfMsgStr);
    	
    	WorkflowMessage wfMsg3 = MessageFactory.newWorkflowMessage();
		wfMsg3.setStatus(MessageStatus.FAILURE);
		wfMsg3.setEventName("ANSWERSECQUESTION");
		wfMsg3.setTopic("PASSWORD");
		wfMsg3.addError("WORKFLOW MESSAGE IS NULL");
		String failureXml = getXML(wfMsg3);
		send(response, failureXml);
	}
     
	//Get workflow message from XML String
	Message msg = getMessage(wfMsgStr,"","PASSWORD");

	WorkflowMessage wfmsg = (WorkflowMessage)msg;

	
	String event = wfmsg.getEventName();

	String topic = wfmsg.getTopic();
	String organization = (String)wfmsg.getParameterValue("organization");
	String jspApplication = (String)wfmsg.getParameterValue("jspApplication");
	String userName = (String)wfmsg.getParameterValue("userShortName");
	String secretAns = (String)wfmsg.getParameterValue("secretAnswer");
	String secQuestion = (String)wfmsg.getParameterValue("secretQuestion");
	
	if(log.isDebugEnabled()) {
		StringBuffer sb = new StringBuffer();
		sb.append("forgotPassword.jsp : Parameters -- >" + " org = " + organization +
				", user = " + userName + ", jspapp = " + jspApplication + 
				", secret question = " + secQuestion);
		log.debug(sb.toString());
	}
	
	// Retrieve user object
	User user = getUser(userName, organization);
    //Validate User
	WorkflowMessage wfMsg1 = validateUser(user,event,topic);
    if(wfMsg1.getStatus().equals(MessageStatus.FAILURE)) {
		String failureXml = getXML(wfMsg1);
		send(response, failureXml);
		return;
    }

    String secretQuestion = "";
    
	if (event.equalsIgnoreCase("FORGOT")) 
	{
		secretQuestion = user.getPasswordQuestion();
		if(null != secretQuestion && !secretQuestion.equals(""))
		{
			// if secret question present then ask for answer.
			WorkflowMessage wfMsg2 = MessageFactory.newWorkflowMessage();
			wfMsg2.setTopic(topic);
			wfMsg2.setEventName("ANSWERSECQUESTION");
			wfMsg2.setParameterValue("SECRETQUESTION", secretQuestion);
			String xml = getXML(wfMsg2);
			send(response, xml);
		    return;
	    }else{
		
			initializeSession(session,request, user, userName, organization, jspApplication);
		
			// Email the new password to the user
			String oldPassword = CryptC.decrypt((String) user.getOldPasswords().get(user.getOldPasswords().size() - 1));	
			String newPassword1 = UserFactory.getPasswordGenerator().generateRandomPassword();
			logMessage("Forwarding to LoginPasswordValidation ");
			String url = "/PasswordValidationClient.do?sender=admin&oldPassword="+ oldPassword+ "&newPassword1="+ newPassword1+ "&newPassword2="+ newPassword1+"&forgetPasswordRequest="+true+"&secretQuestion="+secretQuestion+"&secretAnswer="+secretAns;
	    
			RequestDispatcher dispatcher = request.getRequestDispatcher(url);
			dispatcher.forward(request, response);
		}
	} 
	
	
	if(event.equalsIgnoreCase("ANSWERSECQUESTION"))
	{
		//TODO: do the validation of answer if platform is not doing.
		
		String passwordAnswer = user.getPasswordAnswer();
		if (secretAns == null || !(secretAns.trim()).equalsIgnoreCase(passwordAnswer.trim())) {
			WorkflowMessage wfMsg3 = MessageFactory.newWorkflowMessage();
			wfMsg3.setStatus(MessageStatus.FAILURE);
			wfMsg3.setEventName("ANSWERSECQUESTION");
			wfMsg3.setTopic(topic);
			wfMsg3.addError("INCORRECT SECRET ANSWER");
			String failureXml = getXML(wfMsg3);
			send(response, failureXml);
            return;
		}else{
		
			initializeSession(session,request, user, userName, organization, jspApplication);
		
			// Email the new password to the user
			String oldPassword = CryptC.decrypt((String) user.getOldPasswords().get(user.getOldPasswords().size() - 1));
			String newPassword1 = UserFactory.getPasswordGenerator().generateRandomPassword();
			logMessage("Forwarding to LoginPasswordValidation ");
	    	String url = "/PasswordValidationClient.do?sender=admin&oldPassword="+ oldPassword+ "&newPassword1="+ newPassword1+ "&newPassword2="+ newPassword1+"&forgetPasswordRequest="+true+"&secretQuestion="+secretQuestion+"&secretAnswer="+secretAns;
	    
			RequestDispatcher dispatcher = request.getRequestDispatcher(url);
			dispatcher.forward(request, response);
		}
	}

%>







